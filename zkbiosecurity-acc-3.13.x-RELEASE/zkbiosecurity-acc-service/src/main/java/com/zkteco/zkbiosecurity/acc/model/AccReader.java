/**
 * File Name: AccReader Created by GenerationTools on 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccReader
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午10:06
 * @version v1.0
 */
@Entity
@Table(name = "ACC_READER")
@Getter
@Setter
@Accessors(chain = true)
public class AccReader extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @ManyToOne
    @JoinColumn(name = "DOOR_ID")
    private AccDoor accDoor;

    /**  */
    @Column(name = "NAME", length = 100, nullable = false)
    private String name;

    /**  */
    @Column(name = "READER_NO", nullable = false)
    private Short readerNo;

    /**  */
    @Column(name = "READER_STATE", nullable = false)
    private Short readerState;

    /** 读头类型 按位：0:不配置，1:RS485，2:韦根，3:RS485/韦根 4:网络读头，8、zigbee读头 */
    @Column(name = "COMM_TYPE")
    private Short commType;

    /** RS485 Address */
    @Column(name = "COMM_ADDRESS")
    private Short commAddress;

    /** IP地址，针对TCP IP读头 */
    @Column(name = "IP", length = 20)
    private String ip;

    /** Port 端口，针对TCP IP读头 */
    @Column(name = "PORT")
    private Short port;

    /** Mac地址，针对zigbee读头 */
    @Column(name = "MAC", length = 30)
    private String mac;

    /** 组播地址 */
    @Column(name = "MULTICAST", length = 30)
    private String multicast;

    /** 所属扩展板id */
    @Column(name = "EXT_DEV_ID")
    private String extDevId;

    /**  */
    @OneToMany(mappedBy = "accReader", cascade = CascadeType.REMOVE)
    private Set<AccReaderOption> accReaderOptionSet = new HashSet<AccReaderOption>();

    /** 读头加密 */
    @Column(name = "READER_ENCRYPT")
    private Boolean readerEncrypt;

    /** rs485读头协议类型 */
    @Column(name = "RS485_PROTOCOL_TYPE")
    private Short rs485ProtocolType;

    /** 人员锁定功能 0：否 1：是 */
    @Column(name = "USER_LOCK_FUN")
    private Short userLockFun;

    // pro字段

    /** 针对RS485读头,对应控制器串口物理编码(1,2,3) pro */
    @Column(name = "SERIAL_PORT")
    private Short serialPort;

    /** 读头韦根格式 pro */
    @Column(name = "WG_INPUTFMT_ID")
    private String wgInputFmtId;

    /** 卡号反转 pro */
    @Column(name = "WG_REVERSED")
    private Short wgReversed;

    /** 是否离线通行 0正常 1拒绝 pro */
    @Column(name = "OFFLINE_REFUSE")
    private Short offlineRefuse;

    /** 读头序列号 */
    @Column(name = "SN", length = 30)
    private String sn;

    /** 读头固件版本 */
    @Column(name = "FW_VERSION", length = 50)
    private String fwVersion;
}