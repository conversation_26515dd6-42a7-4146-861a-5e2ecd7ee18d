/**
 * File Name: AccHoliday
 * Created by GenerationTools on 2018-02-26 下午05:53
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 对应百傲瑞达实体 AccHoliday
 * <AUTHOR>
 * @date:	2018-02-26 下午05:53
 * @version v1.0
 */
@Entity
@Table(name = "ACC_HOLIDAY")
@Getter
@Setter
@Accessors(chain=true)
public class AccHoliday extends BaseModel implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**  */
	@Column(name="NAME", length=30, nullable=false)
	private String name;

	/**  */
	@Column(name="HOLIDAY_TYPE", nullable=false)
	private Short holidayType;

	/**  */
	@Column(name="START_DATE", nullable=false)
	@Temporal(TemporalType.DATE)
	private Date startDate;

	/**  */
	@Column(name="END_DATE", nullable=false)
	@Temporal(TemporalType.DATE)
	private Date endDate;

	/**  */
	@Column(name="IS_LOOP_BY_YEAR",nullable=false)
	private Boolean isLoopByYear;

	/**  */
	@Column(name="REMARK",length=50)
	private String remark;

	@Override
	public boolean equals(Object obj)
	{
		AccHoliday other = (AccHoliday) obj;
		if (!holidayType.equals(other.holidayType))
		{
			return false;
		}
		else if (startDate.compareTo(other.startDate) != 0)
		{
			return false;
		}
		else if (endDate.compareTo(other.endDate) != 0)
		{
			return false;
		}
		else if (isLoopByYear.compareTo(other.isLoopByYear) !=0)
		{
			return false;
		}
		return true;
	}
}