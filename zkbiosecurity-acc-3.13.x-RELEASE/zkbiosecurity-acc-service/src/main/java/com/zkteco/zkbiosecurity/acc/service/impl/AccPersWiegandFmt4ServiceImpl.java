package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmt4OtherService;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;

/**
 * <AUTHOR>
 * @date 2021/10/11 11:48
 * @since 1.0.0
 */
@Service
public class AccPersWiegandFmt4ServiceImpl implements PersWiegandFmt4OtherService {

    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private PersWiegandFmtService persWiegandFmtService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;

    @Override
    public void checkUseWiegandFmt(String wiegandFmtIds) {
        List<AccDoor> accDoorList =
            accDoorDao.findByWgInputFmtIdIn((List<String>)CollectionUtil.strToList(wiegandFmtIds));
        if (Objects.nonNull(accDoorList) && !accDoorList.isEmpty()) {
            throw ZKBusinessException
                .warnException(I18nUtil.i18nCode("pers_wgFmt_checkIsUsed", I18nUtil.i18nCode("acc_module")));
        }
    }

    @Override
    public void editWiegandFmt(String wiegandFmtId) {
        PersWiegandFmtItem persWiegandFmtItem = persWiegandFmtService.getItemById(wiegandFmtId);
        if (persWiegandFmtItem != null) {
            List<PersWiegandFmtItem> persWiegandFmtItemList = new ArrayList<>();
            persWiegandFmtItemList.add(persWiegandFmtItem);
            List<AccDevice> accDeviceList = accDeviceDao.findAll();
            // 如果编辑的是内置韦根格式，传过来需要下发 DefaultWG_count 的韦根id
            if (persWiegandFmtItem.getIsDefaultFmt()) {
                accDeviceList.stream().forEach(accDevice -> {
                    accDevCmdManager.setDefWGFormatToDev(accDevice.getSn(), persWiegandFmtItemList, false);
                });
            } else {
                boolean existAutoMatch = false;
                Set<Short> delCardBit = new HashSet<>();
                List<PersWiegandFmtItem> wfList =
                    persWiegandFmtService.getItemsByWiegandCount(persWiegandFmtItem.getWiegandCount());
                for (PersWiegandFmtItem item : wfList) {
                    if (item.getIsDefaultFmt()) {
                        existAutoMatch = true;
                        break;
                    }
                }
                for (AccDevice accDevice : accDeviceList) {
                    if (accDeviceOptionService.isSupportFunList(accDevice.getSn(), 6)) {
                        accDevCmdManager.delDefWGFormatFromDev(accDevice.getSn(), persWiegandFmtItemList, false);
                    } else if (!existAutoMatch) {
                        // 这个位数的韦根格式，不需要默认匹配，老固件用
                        delCardBit.add(persWiegandFmtItem.getWiegandCount());
                        accDevCmdManager.delDefWGByCardBit(accDevice, delCardBit, false);
                    }
                }
            }
            // 一体机需要全部删除全部下发,所以将所有自动匹配的韦根格式查出
            List<PersWiegandFmtItem> defWGList = persWiegandFmtService.getAutoMatch();
            accDeviceList.stream().forEach(accDevice -> {
                // 一体机需要全部删除全部下发
                if (accDevice.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL) {
                    accDevCmdManager.delAllCardFormat(accDevice, false);
                    accDevCmdManager.setCardFormatToDev(accDevice, defWGList, false);
                } else {
                    accDevCmdManager.setCusWGFormatToDev(accDevice.getSn(), persWiegandFmtItemList, false);
                }
            });
        }
    }

    @Override
    public void delWiegandFmt(List<PersWiegandFmtItem> wiegandFmts) {
        if (wiegandFmts.size() > 0) {
            List<PersWiegandFmtItem> persWiegandFmts = new ArrayList<>();// 自动匹配的韦根格式
            Set<Short> delCardBit = new HashSet<>();
            wiegandFmts.stream().forEach(persWiegandFmtItem -> {
                if (persWiegandFmtItem.getIsDefaultFmt()) {
                    persWiegandFmts.add(persWiegandFmtItem);
                }
                List<PersWiegandFmtItem> wfList =
                    persWiegandFmtService.getItemsByWiegandCount(persWiegandFmtItem.getWiegandCount());
                boolean existAutoMatch = false;
                for (PersWiegandFmtItem item : wfList) {
                    if (item.getIsDefaultFmt()) {
                        existAutoMatch = true;
                        break;
                    }
                }
                if (!existAutoMatch) {
                    delCardBit.add(persWiegandFmtItem.getWiegandCount());
                }
            });
            List<PersWiegandFmtItem> defWGList = persWiegandFmtService.getAutoMatch();// 一体机需要全部删除全部下发,所以将所有自动匹配的韦根格式查出
            List<AccDevice> devList = accDeviceDao.findAll();
            devList.stream().forEach(accDevice -> {
                if (accDevice.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL) {// 一体机需要全部删除全部下发
                    accDevCmdManager.delAllCardFormat(accDevice, false);
                    accDevCmdManager.setCardFormatToDev(accDevice, defWGList, false);
                } else {
                    if (accDeviceOptionService.isSupportFunList(accDevice.getSn(), 6)) {
                        accDevCmdManager.delDefWGFormatFromDev(accDevice.getSn(), persWiegandFmts, false);
                        accDevCmdManager.delCusWGFormatFromDev(accDevice.getSn(), wiegandFmts, false);
                    } else if (delCardBit.size() > 0) {
                        accDevCmdManager.delDefWGByCardBit(accDevice, delCardBit, false);
                    }
                }
            });
        }
    }
}
