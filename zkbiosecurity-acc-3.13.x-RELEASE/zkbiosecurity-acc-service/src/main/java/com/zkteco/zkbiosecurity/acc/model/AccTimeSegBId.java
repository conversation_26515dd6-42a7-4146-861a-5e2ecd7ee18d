package com.zkteco.zkbiosecurity.acc.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Table(name = "ACC_TIMESEG_BID")
@Entity
@Getter
@Setter
@Accessors(chain=true)
public class AccTimeSegBId implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_acc_timesegid")
    @GenericGenerator(name = "seq_acc_timesegid", strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator", parameters = {
            @org.hibernate.annotations.Parameter(name = "sequence_name", value = "seq_acc_timesegid")})
    private Long id;
}
