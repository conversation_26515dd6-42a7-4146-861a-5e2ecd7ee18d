/*
 * File Name: AccCommunicationDataProcessor <NAME_EMAIL> on 2018/4/26 14:35. Copyright:Copyright ©
 * 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.processor;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;

import org.apache.commons.lang3.StringUtils;
import org.hibernate.exception.ConstraintViolationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.bean.AccDevCQDataBean;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.operate.AccDealDevCQDataOperate;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Component
@Order(value = 31)
public class AccCommunicationDataProcessor implements CommandLineRunner {

    Logger logger = LoggerFactory.getLogger(AccCommunicationDataProcessor.class);
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDealDevCQDataOperate accDealDevCQDataOperate;
    @Autowired
    private AccAlarmMonitorService accAlarmMonitorService;
    @Autowired
    private AccFirstInLastOutService accFirstInLastOutService;
    @Autowired(required = false)
    private AccCloudService accCloudService;

    @Override
    public void run(String... strings) throws Exception {
        // 处理缓存中门禁事件记录
        new AccCommunicationDataHandlerThread("AccCommunicationDataHandlerThread").start();
        // 启动事件记录入库保存处理线程
        new AccTransactionSaveThread("AccRTLogSaveToDBThread").start();
        // 设备上传人员信息处理线程
        new AccSavePersonThread("AccSavePersonThread").start();
        // 设备查询数据处理
        new AccQueryDataThread("AccQueryDataThread").start();
    }

    class AccSavePersonThread extends Thread {
        public AccSavePersonThread(String name) {
            super(name);
        }

        @Override
        public void run() {
            try {
                // 启动先休眠30s，降低cpu占用，供其它任务执行
                Thread.sleep(30000);
            } catch (InterruptedException e) {
                logger.error("AccSavePersonThread Sleep Error", e);
            }
            logger.info("AccSavePersonThread start ......");
            while (true) {
                try {
                    Set<String> uploadDataKeySet = accCacheManager.getCacheKeySet(BaseConstants.ADMS_UPLOAD_DATA + "*");// 获取redis中设备上传的人员数据key集合
                    if (uploadDataKeySet.size() > 0) {
                        for (String key : uploadDataKeySet) {
                            String sn = key.split(":")[2];
                            String data = accCacheManager.getLeftFirstValue(key);
                            boolean isRegDev = accDeviceService.isRegistrationDevice(sn, data);// 判断是否是登记机
                            if (isRegDev) {
                                handlerAccPerson(data, sn);
                            }
                        }
                    } else {
                        Thread.sleep(10000);
                    }
                } catch (Throwable e) {
                    logger.error("AccSavePersonThread Handler Data Error", e);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e1) {
                        logger.error("AccSavePersonThread Sleep Error", e1);
                    }
                }
            }

        }

        private void handlerAccPerson(String data, String sn) {
            JSONObject dataJson = JSONObject.parseObject(data);
            AccQueryDeviceItem accDeviceItem = accDeviceService.getQueryItemBySn(sn);
            if (Objects.nonNull(accDeviceItem)) {
                dataJson.put("sn", sn);
                dataJson.put("commType", accDeviceItem.getCommType());
                AccDevCQDataBean accDevCQDataBean = new AccDevCQDataBean(dataJson);
                accDealDevCQDataOperate.dealDataByCData(accDevCQDataBean);// 处理设备上传数据
            }
        }
    }

    class AccQueryDataThread extends Thread {
        public AccQueryDataThread(String name) {
            super(name);
        }

        @Override
        public void run() {
            try {
                // 启动先休眠30s，降低cpu占用，供其它任务执行
                Thread.sleep(35000);
            } catch (InterruptedException e) {
                logger.error("AccQueryDataThread Sleep Error", e);
            }
            logger.info("AccQueryDataThread start ......");

            while (true) {
                try {
                    // 获取redis中设备上传的人员数据key集合
                    Set<String> queryDataKeySet = accCacheManager.getCacheKeySet("adms:device:query:" + "*");
                    if (queryDataKeySet.size() > 0) {
                        for (String key : queryDataKeySet) {
                            // String sn = key.split(":", 4)[3];
                            // String data = accCacheManager.getLeftFirstValue(key);
                            List<String> datas = accCacheManager.getAndRemoveLeftNValue(key, 10);
                            datas.parallelStream().forEachOrdered(data -> dealQueryData(data));
                        }
                    } else {
                        Thread.sleep(10000);
                    }
                } catch (Throwable e) {
                    logger.error("AccSavePersonThread Handler Data Error", e);
                    try {
                        Thread.sleep(10000);
                    } catch (Exception ee) {
                        logger.error("AccSavePersonThread Sleep Error", ee);
                    }
                }
            }

        }

        private void dealQueryData(String data) {
            JSONObject dataJson = JSONObject.parseObject(data);
            AccQueryDeviceItem accDeviceItem = accDeviceService.getQueryItemBySn(dataJson.getString("sn"));
            if (Objects.nonNull(accDeviceItem)) {
                dataJson.put("commType", accDeviceItem.getCommType());
                AccDevCQDataBean accDevCQDataBean = new AccDevCQDataBean(dataJson);
                accDealDevCQDataOperate.dealDataByQueryData(accDevCQDataBean);// 处理设备上传数据
            }
        }
    }

    class AccCommunicationDataHandlerThread extends Thread {
        public AccCommunicationDataHandlerThread(String name) {
            super(name);
        }

        @Override
        public void run() {
            try {
                // 启动先休眠10s，降低cpu占用，供其它任务执行
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                logger.error("AccCommunicationDataHandlerThread Sleep Error", e);
            }
            logger.info("AccCommunicationDataHandlerThread Start Handler Data......");
            while (true) {
                try {
                    Set<String> activeQueneNameSet = accCacheManager.getCacheKeySet("adms:accLog:*");
                    if (!activeQueneNameSet.isEmpty()) {
                        for (String key : activeQueneNameSet) {
                            handlerAccLog(key);
                        }
                    } else {
                        Thread.sleep(1000);
                    }
                } catch (Throwable e) {
                    logger.error("AccCommunicationDataHandlerThread Handler Data Error", e);
                    try {
                        Thread.sleep(10000);
                    } catch (Exception ee) {
                        logger.error("AccCommunicationDataHandlerThread Sleep Error", ee);
                    }
                }
            }
        }

        private void handlerAccLog(String key) {
            //String rtLog = accCacheManager.getLeftFirstValue(key);
            List<String> rtLogs = accCacheManager.getAccLogs(key);
            for (String rtLog : rtLogs) {
                String sn = key.split(":")[2];
                if (rtLog != null) {
                    accTransactionService.handleRTLog(rtLog, sn);
                }
            }
        }
    }

    class AccTransactionSaveThread extends Thread {
        AccTransactionSaveThread(String threadName) {
            super.setName(threadName);
        }

        @Override
        public void run() {
            try {
                // 启动先休眠10s，降低cpu占用，供其它任务执行
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                logger.error("AccTransactionSaveThread Sleep Error", e);
            }
            logger.info("AccTransactionSaveThread Start Handler Data......");
            while (true) {
                try {
                    if (accCacheManager.exists(AccCacheKeyConstants.ACC_TRANSACTIONS_TO_DB)) {
                        // 记录设备事件保存，分批一条条处理，
                        saveRtTransaction();
                    } else {
                        Thread.sleep(5000);
                    }
                } catch (Throwable e) {
                    logger.error("Exception AccTransactionSaveThread : " + this.getName() + "deal rtLog error", e);
                    try {
                        Thread.sleep(10000);
                    } catch (Exception ee) {
                        logger.error("AccTransactionSaveThread Sleep Error", ee);
                    }
                }
            }
        }

        private void saveRtTransaction() {
            // 一次性取出500条事件
            List<AccTransactionItem> accTransactions = getAccTransactions(AccCacheKeyConstants.ACC_TRANSACTIONS_TO_DB);
            if (!accTransactions.isEmpty()) {
                // 将事件按100条分批
                List<List<AccTransactionItem>> accTransactionsList = CollectionUtil.split(accTransactions, 100);
                final CountDownLatch latch = new CountDownLatch(accTransactionsList.size());
                // 获取线程池
                ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolExecutor.getCustomThreadPoolExecutor();
                for (List<AccTransactionItem> accTransactionItemList : accTransactionsList) {
                    threadPoolExecutor.execute(() -> saveTransactionToDb(accTransactionItemList, latch));
                }
                // 控制所有线程全部跑完（异步执行可以去掉）
                try {
                    latch.await();
                } catch (InterruptedException e) {
                    logger.error("saveRtTransaction error", e);
                }
            }
        }

        private void saveTransactionToDb(List<AccTransactionItem> accTransactions, CountDownLatch latch) {
            Map<String, String> uniqueKeyMap = new HashMap<>();
            if (!accTransactions.isEmpty()) {
                for (AccTransactionItem at : accTransactions) {
                    try {
                        if (!uniqueKeyMap.containsKey(at.getUniqueKey())) {
                            uniqueKeyMap.put(at.getUniqueKey(), at.getUniqueKey());
                            // 不采用批量原则是保证一条条入库处理，禁止由于一条入库失败导致整体回滚
                            accTransactionService.saveItem(at);
                            if (at.getEventLevel() != null && at.getEventLevel() == AccConstants.EVENT_ALARM) {
                                accAlarmMonitorService.addByTransaction(at);
                            }
                            // 推送门禁报警消息到微信小程序 ----add by zhixiong.huang 2021-10-14 11:30
                            if (Objects.nonNull(accCloudService)) {
                                accCloudService.sendAlarmMsgToWxMiniPrograms(Collections.singletonList(at));
                            }
                        }
                    } catch (ConstraintViolationException | DataIntegrityViolationException e) {
                        // 在首次入库事件可能重复，在c3上，1s多次刷卡，提示刷卡时间间隔太短，1s会产生两条相同事件记录，导致入库失败，目前软件只需处理保证一条入库即可，控制器是由于logindex，所以会避免这个问题
                        logger.error(
                            "---------------FirstSaveExceptionTransactionHandle_ConstraintViolationException maybe transaction is repeat ,e="
                                + e.getMessage());
                    } catch (Exception e) {
                        logger.error("---------------------batch save accTransaction to DB error---------------key:"
                            + AccCacheKeyConstants.ACC_TRANSACTIONS_TO_DB + "--------value:" + JSON.toJSONString(at));
                        logger.error("-----------------------------------get acctransaction error message", e);
                    }
                }
            }
            latch.countDown();
        }

        public List<AccTransactionItem> getAccTransactions(String transKey) {
            List<AccTransactionItem> accTransactions = new ArrayList<>();
            // 一次性最多取200条处理
            // List<String> transList = accCacheManager.lrange(transKey, 0,
            // AccCacheKeyConstants.GET_TRANSACTIONS_TO_DB_INDEX);
            List<String> transList =
                accCacheManager.getAccTransactionFromCache(AccCacheKeyConstants.GET_TRANSACTIONS_TO_DB_INDEX);
            // 获取Digifort联动信息
            Map<String, String> ivideoLinkageMap = accCacheManager.getIVideoDigifortLinkage();
            short except = 0;
            try {
                AccTransactionItem tempTran = null;
                if (!transList.isEmpty()) {
                    for (String tran : transList) {
                        tempTran = JSON.parseObject(tran, AccTransactionItem.class);
                        if (StringUtils.isNotBlank(tempTran.getDescription())
                            && tempTran.getDescription().length() > 100)// 考虑中文
                        {
                            tempTran.setDescription(tempTran.getDescription().substring(0, 96) + "...");
                        }
                        // 添加digifort联动信息
                        if (StringUtils.isNotBlank(ivideoLinkageMap.get(tempTran.getUniqueKey()))) {
                            String vidLinkageHandle = StringUtils.isNotBlank(tempTran.getVidLinkageHandle())
                                ? tempTran.getVidLinkageHandle() + ";" : "";
                            vidLinkageHandle += "digifort_" + tempTran.getEventTime().getTime() + "_"
                                + ivideoLinkageMap.get(tempTran.getUniqueKey());
                            tempTran.setVidLinkageHandle(vidLinkageHandle);
                        }
                        accTransactions.add(tempTran);
                    }
                }
            } catch (Exception e) {
                logger.error("---------------------json to acctransaction error---------------key:" + transKey
                    + "--------valu:" + transList);
                logger.error("-----------------------------------get acctransaction error message:" + e.getMessage());
                except = 1;
            }
            if (!transList.isEmpty()) {
                accCacheManager.removeAccTransaction(transKey, accTransactions.size() + except);
            }
            return accTransactions;
        }

    }

}
