/**
 * File Name: AccLevelServiceImpl Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.adms.bean.DeviceDoorState;
import com.zkteco.zkbiosecurity.adms.service.AdmsDeviceService;
import org.apache.commons.collections.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelItem;
import com.zkteco.zkbiosecurity.acc.app.vo.AccCloudDoorItem;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.enums.AccLinkTypeEnum;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.utils.AccLevelUtil;
import com.zkteco.zkbiosecurity.acc.utils.ProcessBeanUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.vo.*;

/**
 * 门禁权限组service实现类
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-02 下午02:15
 */
@Service
@Transactional
public class AccLevelServiceImpl implements AccLevelService {

    private Logger logger = LoggerFactory.getLogger(AccLevelServiceImpl.class);

    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AccLevelDao accLevelDao;
    @Autowired
    private AccTimeSegDao accTimeSegDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccLevelPersonDao accLevelPersonDao;
    @Autowired
    private AccLevelDoorDao accLevelDoorDao;
    @Autowired
    private AccLevelDeptDao accLevelDeptDao;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private PersPersonLinkService persPersonLinkService;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccPersonDao accPersonDao;
    @Autowired
    private AccPersonCombOpenPersonDao accPersonCombOpenPersonDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccFirstOpenService accFirstOpenService;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private AccReaderDao accReaderDao;
    @Autowired(required = false)
    private AccGetVisTransactionService accGetVisTransactionService;
    @Autowired(required = false)
    private Acc4VisLevelService acc4VisLevelService;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AccAppTopDoorByPersonService accAppTopDoorByPersonService;
    @Autowired
    private AccAppTopDoorByPersonDao accAppTopDoorByPersonDao;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired(required = false)
    private AccProExtService accProExtService;
    @Autowired(required = false)
    private AccLevel4OtherService[] accLevel4OtherServices;
    @Autowired
    private PersBioPhotoService persBioPhotoService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private AdmsDeviceService admsDeviceService;

    /**
     * 是否支持权限组时间段功能
     */
    @Value("${system.levelTime.support:false}")
    private boolean isSupportLevelTime;

    @Override
    public AccLevelItem saveItem(AccLevelItem item) {
        AccLevel accLevel = Optional.ofNullable(item).map(AccLevelItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(id -> accLevelDao.findById(id)).orElse(new AccLevel());
        if (Objects.nonNull(acc4VisLevelService)) {
            Acc4VisLevelItem visLevelItem = new Acc4VisLevelItem();
            visLevelItem.setModuleId(item.getId());
            visLevelItem.setLevelName(item.getName());
            acc4VisLevelService.editLevelModule(visLevelItem);
        }
        ModelUtil.copyPropertiesIgnoreNull(item, accLevel);
        AccTimeSeg accTimeSeg = accTimeSegDao.findOne(item.getTimeSegId());
        if (accTimeSeg != null) {
            accLevel.setTimeSegId(accTimeSeg.getId());
        }
        accLevelDao.save(accLevel);
        /*if (item.getChangeLevel() != null && item.getChangeLevel() == true)
        {
        	syncTimeSegLevelToDev(accLevel.getId());
        }*/
        item.setId(accLevel.getId());
        return item;
    }

    @Override
    public List<AccLevelItem> getByCondition(AccLevelItem condition) {
        return (List<AccLevelItem>)accLevelDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = accLevelDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        if (condition instanceof AccLevelItem) {
            List<AccLevelItem> levelItems = (List<AccLevelItem>)pager.getData();
            String areaIds = CollectionUtil.getPropertys(levelItems, AccLevelItem::getAuthAreaId);
            List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIds);
            Map<String, AuthAreaItem> itemMap = CollectionUtil.itemListToIdMap(authAreaItemList);
            levelItems.forEach(
                accLevelItem -> accLevelItem.setAuthAreaName(itemMap.get(accLevelItem.getAuthAreaId()).getName()));
            pager.setData(levelItems);
        }
        return pager;
    }

    @Override
    public boolean deleteByIds(String ids) {
        List<String> accLevelIdList = (List<String>)CollectionUtil.strToList(ids);
        List<String> personIds = getPersonIdsByLevelIdIn(accLevelIdList);
        List<String> doorIds = accLevelDoorDao.getDoorIdsByLevelIdsIn(accLevelIdList);
        accLevelDeptDao.deleteByLevelIds(accLevelIdList); // 删除权限组和部门的中间表数据
        if (accLevelIdList != null && accLevelIdList.size() > 0) {
            accLevelIdList.forEach(accLevelId -> {
                accLevelDao.deleteById(accLevelId);
            });
        }
        // 通知其他模块删除门禁权限
        deleteAccLevel4OtherModule(doorIds, personIds);
        return false;
    }

    @Override
    public void deleteAccLevel4OtherModule(List<String> doorIds, List<String> personIds) {
        if (accLevel4OtherServices != null) {
            Arrays.stream(accLevel4OtherServices)
                .forEach(accLevel4OtherService -> accLevel4OtherService.deleteAccLevel4Others(doorIds, personIds));
        }
    }

    @Override
    public AccLevelItem getItemById(String id) {
        return Optional.ofNullable(id).filter(StringUtils::isNotBlank).map(i -> getByCondition(new AccLevelItem(i)))
            .filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public AccLevelItem initData(AccLevelItem item) {
        AccLevel accLevel = accLevelDao.findByInitFlag(item.getInitFlag());
        if (accLevel == null) {
            return this.saveItem(item);
        } else {
            item.setId(accLevel.getId());
            return item;
        }
    }

    @Override
    public void addPersonLevel(String levelIds, String personIds) {
        if (StringUtils.isNotBlank(levelIds) && StringUtils.isNotBlank(personIds)) {
            // AccPersonInfoItem personInfoBean = getPersonByIds((List<String>)CollectionUtil.strToList(personIds));
            // if (!personInfoBean.getPersonList().isEmpty()) {
                // 过滤禁用人员的后的ids
            // String realPersonIds = CollectionUtil.getItemIds(personInfoBean.getPersonList());
            addLevelByParamIds(personIds, levelIds, "person");// 添加人员权限到数据库
            // 下发人员权限到设备
            setPersonLevelToDev(levelIds, StrUtil.strToList(personIds), false);
            // setPersonLevelToDev(levelIds, personInfoBean);// 下发人员权限到设备
            // }
        }
    }

    private void setPersonLevelToDev(String levelIds, AccPersonInfoItem personInfoBean) {
        // select distinct dev from AccLevel e join e.accDoorSet d join d.device dev where e.id in (%s)
        if (StringUtils.isNotBlank(levelIds)) {
            List<String> levelIdList = (List<String>)CollectionUtil.strToList(levelIds);
            // select distinct dev from AccLevel e join e.accDoorList l join l.accDoor d join d.device dev where e.id in
            // (?1)
            List<AccDevice> devList = accDeviceDao.getDevByLevel(levelIdList);// 获取设备
            if (devList != null && !devList.isEmpty()) {
                // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
                AccDeviceUtil.moveUpParentDevList(devList);
                for (AccDevice dev : devList) {
                    List<AccPersonLevelOptItem> accPersonLevelOptItemList = new ArrayList<>();
                    levelIdList.forEach(levelId -> {
                        AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
                        accPersonLevelOptItem.setDeviceId(dev.getBusinessId());
                        String timeSegId = accLevelDao.getTimeSegIdByLevelId(levelId);
                        // 时间段数据存在扩展，需要另外查询
                        Long timeSegBId = accTimeSegService.getBusinessIdByTimeSegId(timeSegId);
                        accPersonLevelOptItem.setTimeSegId(timeSegBId);
                        // 设置权限组id add by colin
                        accPersonLevelOptItem.setLevelId(accLevelDao.findOne(levelId).getBusinessId());
                        List<Short> doorNoList = accLevelDoorDao.getDoorNoByLevelIdAndDevId(levelId, dev.getId());
                        if (doorNoList != null && !doorNoList.isEmpty()) {
                            accPersonLevelOptItem.setDoorNoList(doorNoList);
                            accPersonLevelOptItemList.add(accPersonLevelOptItem);
                        }
                    });
                    if (!personInfoBean.getPersonList().isEmpty()) {
                        accDevCmdManager.setPersonToDev(dev.getSn(), personInfoBean.getPersonList(), false);// 下发人员信息
                        if (personInfoBean.getAccBioTemplateItemList() != null
                            && !personInfoBean.getAccBioTemplateItemList().isEmpty()) {
                            accDevCmdManager.setPersonBioTemplateToDev(dev.getSn(),
                                personInfoBean.getAccBioTemplateItemList(), false); // 下发指纹信息
                        }
                        setLevelToDev(dev, accPersonLevelOptItemList, personInfoBean.getPersonList());// 下发权限
                        accDevCmdManager.downloadOver(dev.getSn(), false);
                    }
                }
            }
        }
    }

    private Map<String, String> putDevAndDoorNoToMap(List<AccDoor> accDoorList) {
        Map<String, String> accDevDoorMap = new HashMap<>();
        for (AccDoor accDoor : accDoorList) {
            if (!accDevDoorMap.containsKey(accDoor.getDevice().getSn())) {
                accDevDoorMap.put(accDoor.getDevice().getSn(), String.valueOf(accDoor.getDoorNo()));
                continue;
            }
            String doorNo = accDevDoorMap.get(accDoor.getDevice().getSn());
            doorNo = doorNo + "," + accDoor.getDoorNo();
            accDevDoorMap.put(accDoor.getDevice().getSn(), doorNo);
        }
        return accDevDoorMap;
    }

    private void setLevelToDev(AccDevice dev, List<AccPersonLevelOptItem> devLevelList,
        List<AccPersonOptItem> personInfoList) {
        if (personInfoList != null && !personInfoList.isEmpty()) {
            List<AccPersonLevelOptItem> accPersonLevelOptItemList = new ArrayList<>();
            for (AccPersonLevelOptItem accPersonLevelOptItem : devLevelList) {
                if (accPersonLevelOptItem.getDeviceId().equals(dev.getBusinessId())) {
                    for (AccPersonOptItem person : personInfoList) {
                        // 有些是personId,有些是pin
                        if (StringUtils.isNoneBlank(accPersonLevelOptItem.getPin())
                            && !person.getId().equals(accPersonLevelOptItem.getPin())
                            && !person.getPin().equals(accPersonLevelOptItem.getPin())) {
                            continue;
                        }
                        // 主卡信息加入user表，防止pin重复
                        if (StringUtils.isBlank(person.getCardNo()) || person.getCardType() == 0) {
                            AccPersonLevelOptItem accPersonLevelOptItemNew = new AccPersonLevelOptItem();
                            accPersonLevelOptItemNew.setDeviceId(accPersonLevelOptItem.getDeviceId());
                            accPersonLevelOptItemNew.setTimeSegId(accPersonLevelOptItem.getTimeSegId());
                            accPersonLevelOptItemNew.setDoorNoList(accPersonLevelOptItem.getDoorNoList());
                            accPersonLevelOptItemNew.setPin(person.getPin());
                            accPersonLevelOptItemNew.setStartTime(accPersonLevelOptItem.getStartTime());
                            accPersonLevelOptItemNew.setEndTime(accPersonLevelOptItem.getEndTime());
                            // 权限组编号 add by max 20200518
                            accPersonLevelOptItemNew.setLevelId(accPersonLevelOptItem.getLevelId());
                            accPersonLevelOptItemList.add(accPersonLevelOptItemNew);
                        }
                    }
                }
            }
            if (!accPersonLevelOptItemList.isEmpty()) {
                accDevCmdManager.setPersonLevelToDev(dev.getSn(), accPersonLevelOptItemList, false);
            }
        }
    }

    private String formatDoorAuthOfBin(Set<Short> doorNoSet) {
        assert doorNoSet != null && doorNoSet.size() > 0;
        int authSum = 0;
        for (Short doorNo : doorNoSet) {
            authSum = authSum + (int)Math.pow(2, (doorNo - 1));
        }
        return String.valueOf(authSum);
    }

    /**
     * 添加人员/门权限到数据库中，使用批量操作
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/22 18:29
     */
    @Override
    public void addLevelByParamIds(String paramIds, String levelIds, String type) {
        // 权限组添加人员，直接操作中间表，使用批量插入
        String[] levelIdArray = levelIds.split(",");
        Map<String, List<Object>> levelParamMap = null;
        levelParamMap = filterParamId(levelIds, paramIds, type);// ret: {"levelId": [personId, personId]}
        List<List<String>> fieldValList = new ArrayList<List<String>>();// 插入字段对应的值
        List<String> tempFieldVal = new ArrayList<String>();
        for (String levelId : levelIdArray) {
            if (levelParamMap.containsKey(levelId)) {
                for (Object paramId : levelParamMap.get(levelId)) {
                    tempFieldVal.add(levelId);
                    tempFieldVal.add(paramId.toString());
                    fieldValList.add(tempFieldVal);
                    tempFieldVal = new ArrayList<String>();
                }
            }
        }
        if ("person".equals(type)) {
            addLevelPerson(paramIds, levelIds, fieldValList);// 权限组加人
        } else if ("door".equals(type)) {
            addLevelDoor(paramIds, levelIds, fieldValList);// 权限组加门
        }
    }

    @Override
    public AccLevelItem getItemByName(String name) {
        AccLevel accLevel = accLevelDao.findByName(name);
        if (accLevel == null) {
            return null;
        } else {
            AccLevelItem item = new AccLevelItem();
            item.setId(accLevel.getId());
            item.setName(accLevel.getName());
            return item;
        }
    }

    @Override
    public Long getLevelPersonCount(String levelId) {
        return accLevelPersonDao.countByAccLevel_Id(levelId);
    }

    @Override
    public Long getLevelDoorCount(String levelId) {
        return accLevelDoorDao.getLevelDoorCount(levelId);
    }

    /**
     * 权限组加门
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/22 18:29
     */
    private void addLevelDoor(String doorIds, String levelIds, List<List<String>> fieldValList) {
        Map<String, List<String>> dataMap = new HashMap<String, List<String>>();
        saveLevelParamToMap(fieldValList, dataMap);
        Map<String, AccDoor> allDoorMap = new HashMap<String, AccDoor>();
        List<AccDoor> allDoorList = accDoorDao.findByIdIn((List<String>)CollectionUtil.strToList(doorIds));
        for (AccDoor door : allDoorList) {
            if (!allDoorMap.containsKey(door.getId())) {
                allDoorMap.put(door.getId(), door);
            }
        }
        Map<String, AccLevel> allLevelMap = new HashMap<String, AccLevel>();
        List<AccLevel> allLevelList = accLevelDao.findByIdIn((List<String>)CollectionUtil.strToList(levelIds));
        for (AccLevel level : allLevelList) {
            if (!allLevelMap.containsKey(level.getId())) {
                allLevelMap.put(level.getId(), level);
            }
        }
        List<AccLevelDoor> accLevelDoorList = new ArrayList<>();
        for (String levelId : dataMap.keySet()) {
            List<String> doorIdList = dataMap.get(levelId);
            AccLevel accLevel = allLevelMap.get(levelId);
            for (String doorId : doorIdList) {
                accLevelDoorList.add(new AccLevelDoor(accLevel, allDoorMap.get(doorId)));
            }
            if (accLevelDoorList.size() > 0) {
                accLevelDoorDao.saveAll(accLevelDoorList);
            }
        }
        if (accProExtService != null) {
            List<AccLevelDoorItem> levelDoorItemList = accLevelDoorList.stream().map(levelDoor -> {
                AccLevelDoorItem item = new AccLevelDoorItem();
                item.setId(levelDoor.getId());
                return item;
            }).collect(Collectors.toList());
            accProExtService.setLevelToDev(levelDoorItemList);
        }
    }

    /**
     * 权限组加人
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/22 18:29
     */
    private void addLevelPerson(String personIds, String levelIds, List<List<String>> fieldValList) {
        List<String> accLevelIdList = (List<String>)CollectionUtil.strToList(levelIds);
        List<String> accPersonIdList = (List<String>)CollectionUtil.strToList(personIds);
        Map<String, List<String>> dataMap = new HashMap<>();
        saveLevelParamToMap(fieldValList, dataMap);

        Map<String, AccLevel> allLevelMap = new HashMap<String, AccLevel>();
        List<AccLevel> allLevelList = accLevelDao.findAll();
        for (AccLevel level : allLevelList) {
            if (!allLevelMap.containsKey(level.getId())) {
                allLevelMap.put(level.getId(), level);
            }
        }
        List<AccLevelPerson> levelPersonList = new ArrayList<>();
        // 查询出是否已经入库,并过滤掉已经入库的数据 by mingfa.zheng
        List<AccLevelPerson> accLevelPersonList =
            accLevelPersonDao.findByAccLevel_IdInAndPersPersonIdIn(accLevelIdList, accPersonIdList);
        for (String levelId : dataMap.keySet()) {
            List<String> tempPersonIdList = dataMap.get(levelId);
            List<String> accLevelPersonIdList =
                accLevelPersonList.stream().map(i -> i.getAccLevel().getId() + "_" + i.getPersPersonId())
                    .filter(StringUtils::isNotBlank).collect(Collectors.toList());
            AccLevel accLevel = allLevelMap.get(levelId);
            for (String personId : tempPersonIdList) {
                // 过滤掉已经入库的数据
                if (!personId.equals("-1") && !accLevelPersonIdList.contains(levelId + "_" + personId)) {
                    levelPersonList.add(new AccLevelPerson(accLevel, personId));
                }
            }
        }
        if (levelPersonList.size() > 0) {
            accLevelPersonDao.saveAll(levelPersonList);
        }
    }

    private void saveLevelParamToMap(List<List<String>> fieldValList, Map<String, List<String>> dataMap) {
        for (List<String> dataList : fieldValList)// 将数据组装成map
        {
            String paramId = dataList.get(1);
            String levelId = dataList.get(0);
            List<String> paramIdList = new ArrayList<String>();
            if (!dataMap.keySet().contains(levelId)) {
                paramIdList.add(paramId);
                dataMap.put(levelId, paramIdList);
            } else {
                paramIdList = dataMap.get(levelId);
                if (!paramIdList.contains(paramId)) {
                    paramIdList.add(paramId);
                    dataMap.put(levelId, paramIdList);
                }
            }
        }
    }

    /**
     * 过滤插入权限组的人员/门Id，去掉已经存在的、不在acc_person/acc_door表的人员/门Id 仅供内部调用
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/22 18:30
     */
    private Map<String, List<Object>> filterParamId(String levelIds, String paramIds, String type) {
        Map<String, List<Object>> addLevelParamMap = new HashMap<String, List<Object>>();
        String[] levelIdArray = levelIds.split(",");
        List<String> paramIdList = getPersonIdOrDoorIdByList(type, paramIds);// 人员/门必须存在acc_person/acc_door表中
        List<Object> levelParamList = null;// 权限组中已有的人员/门
        List<Object> addParamIdList = new ArrayList<Object>();// 权限组不存在的人员/门Id
        Map<Object, String> paramIdMap = null;// 用于比对人员/门 是否存在
        for (String levelId : levelIdArray) {
            levelParamList = new ArrayList<>();
            List<List<String>> paramIdsList = CollectionUtil.split(paramIdList, AccConstants.LEVEL_SPLIT_COUNT);
            // List<String> paramIdsList = getPersonPropertyList(paramIdList);
            if (paramIdsList.size() > 0) {
                // 查询出权限组中已经存在的人员
                for (List<String> paramStr : paramIdsList) {
                    List<Object> tempLevelParamList = new ArrayList<>();
                    if ("person".equals(type)) {
                        tempLevelParamList = accLevelDao.getPersonFromLevel(levelId, paramStr);
                    } else {
                        tempLevelParamList = accLevelDao.getDoorFromLevel(levelId, paramStr);
                    }
                    if (tempLevelParamList.size() > 0) {
                        levelParamList.addAll(tempLevelParamList);
                    }
                }
            }
            if (levelParamList.size() > 0) {
                paramIdMap = new HashMap<Object, String>();
                for (Object personId : levelParamList) {
                    paramIdMap.put(personId, "");
                }
                for (Object personId : paramIdList) {
                    if (!paramIdMap.containsKey(personId))// 过滤已在权限组的人员，无需重复下发
                    {
                        addParamIdList.add(personId);
                    }
                }
            } else {
                addParamIdList = new ArrayList<Object>(paramIdList);
            }
            addLevelParamMap.put(levelId, addParamIdList);
        }
        return addLevelParamMap;
    }

    /**
     * 获取人员或门信息id--过滤不存在的id
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/22 18:30
     */
    private List<String> getPersonIdOrDoorIdByList(String type, String paramIds) {
        List<String> idList = new ArrayList<String>();
        List<String> paramIdList = (List<String>)CollectionUtil.strToList(paramIds);
        List<List<String>> paramIdsList = CollectionUtil.split(paramIdList, AccConstants.LEVEL_SPLIT_COUNT);
        // List<String> paramIdsList = getPersonPropertyList(paramIdList);
        if ("person".equals(type)) {
            for (List<String> personStr : paramIdsList) {
                idList.addAll(personStr);
                // List<String> tempIdList = accPersonDao.getIdByPersPersonId(stringToList(personStr));
                // if (tempIdList.size() > 0)
                // {
                // idList.addAll(tempIdList);
                // }
            }
        } else {
            for (List<String> doorStr : paramIdsList) {
                List<String> tempIdList = accDoorDao.getIdByDoorId(doorStr);
                if (tempIdList.size() > 0) {
                    idList.addAll(tempIdList);
                }
            }
        }
        return idList;
    }

    @Override
    public void immeDelPersonLevel(List<String> levelIds, String personIds) {
        if ((levelIds != null && levelIds.size() > 0) && (StringUtils.isNotBlank(personIds))) {
            Map<String, Object> personParamMap = new HashMap<String, Object>();
            personParamMap.put("id", personIds);
            List<AccPersonOptItem> personInfoList = getPersonByIds(personParamMap);
            List<String> doorIds = accLevelDoorDao.getDoorIdsByLevelIdsIn(levelIds);
            // 删除数据库中人员的权限
            delBatchLevel(levelIds, (List<String>)CollectionUtil.strToList(personIds));

            // 下发删除权限命令
            Map<String, Collection<String>> personInfoMap = AccLevelUtil.setLevelMap(personInfoList);
            delPersonLevelFromDev(levelIds, personInfoMap);
            // 通知其他模块删除门禁权限
            deleteAccLevel4OtherModule(doorIds, StrUtil.strToList(personIds));
        }
    }

    @Override
    public void immeDelPersonLevelToDev(List<String> levelIds, String personIds) {
        if ((levelIds != null && levelIds.size() > 0) && (StringUtils.isNotBlank(personIds))) {
            Map<String, Object> personParamMap = new HashMap<String, Object>();
            personParamMap.put("id", personIds);
            List<AccPersonOptItem> personInfoList = getPersonByIds(personParamMap);

            // 下发删除权限命令
            Map<String, Collection<String>> personInfoMap = AccLevelUtil.setLevelMap(personInfoList);
            delPersonLevelFromDev(levelIds, personInfoMap);
        }
    }

    /**
     * 删除人员权限命令
     *
     * @param levelIds
     * @param personParamMap {"pin": [], "id": []}
     */
    private void delPersonLevelFromDev(List<String> levelIds, Map<String, Collection<String>> personParamMap) {
        List<AccDevice> devList = accDeviceDao.getDevByLevel(levelIds);// 获取设备Set
        // Map<String, List<Object>> devLevelMap = getDevLevelByIds(levelIds);// 查询出权限组中每个设备的权限（门组合）
        // if (devList != null && devList.size() > 0){
        // 删除人员权限命令
        for (AccDevice dev : devList) {
            // if(AccDeviceUtil.isSupportFun(dev.getSn(), AccConstUtil.DEL_BY_AND))// 指定删除哪条权限
            // {
            // delPersonLevelFromDev(dev, personParamMap, devLevelMap);// 删除人员权限
            // }
            // 全删全下权限
            delPersonLevelFromDevByLevelDelPerson(dev, personParamMap);// 删除人员权限
            setLevelToDev(dev, personParamMap.get("id"));// 重新下发该设备中人员的权限，避免权限丢失(效率太低，后续优化)
        }
        // }else {
        // // 删除数据库中人员的权限
        // delBatchLevel(levelIds, (List<String>) personParamMap.get("id"));
        // }
    }

    @Override
    public void delBatchLevel(List<String> levelIds, List<String> personIds) {
        // 修复oracle数据库下，删除报错问题 modify by qingj.qiu
        List<List<String>> personIdList = CollectionUtil.split(personIds, AccConstants.LEVEL_SPLIT_COUNT);
        if (CollectionUtil.isEmpty(levelIds) || CollectionUtil.isEmpty(personIds)) {
            return;
        }
        personIdList.forEach(personId -> {
            accLevelPersonDao.delBatchLevel(levelIds, personId);
        });
    }

    @Override
    public void immeUpdatePersonLevel(String personId, String levelIds) {
        List<AccDevice> addDevList = new ArrayList<>();
        List<String> levelIdList = new ArrayList<>();
        if (StringUtils.isNotBlank(levelIds)) {
            levelIdList = (List<String>)CollectionUtil.strToList(levelIds);
            addDevList = accDeviceDao.getDevByLevel(levelIdList);// 需要添加权限的设备
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
            AccDeviceUtil.moveUpParentDevList(addDevList);

            addLevelByParamIds(personId, levelIds, "person");// 添加人员权限到数据库
        }
        AccPersonInfoItem personInfoBean = getPersonByIds((List<String>)CollectionUtil.strToList(personId));
        if (personInfoBean.getPersonList().size() > 0) {
            List<AccPersonOptItem> personInfoList = personInfoBean.getPersonList();
            if (addDevList.size() > 0) {// 添加人员权限
                for (int i = 0, size = addDevList.size(); i < size; i++) {
                    AccDevice dev = addDevList.get(i);
                    List<AccPersonLevelOptItem> devLevelList = new ArrayList<AccPersonLevelOptItem>();
                    // TODO 此处需要优化
                    levelIdList.forEach(levelId -> {
                        AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
                        accPersonLevelOptItem.setDeviceId(dev.getBusinessId());
                        String timeSegId = accLevelDao.getTimeSegIdByLevelId(levelId);
                        // 时间段数据存在扩展，需要另外查询
                        Long timeSegBId = accTimeSegService.getBusinessIdByTimeSegId(timeSegId);
                        accPersonLevelOptItem.setTimeSegId(timeSegBId);
                        // 设置权限组编号 add by max 20200518
                        accPersonLevelOptItem.setLevelId(accLevelDao.findOne(levelId).getBusinessId());
                        List<Short> doorNoList = accLevelDoorDao.getDoorNoByLevelIdAndDevId(levelId, dev.getId());
                        if (doorNoList != null && doorNoList.size() > 0) {
                            accPersonLevelOptItem.setDoorNoList(doorNoList);
                            devLevelList.add(accPersonLevelOptItem);
                        }
                        // Map<String, String> accDevDoorMap = putDevAndDoorNoToMap(accDoorList);//key：devSn --
                        // value:doorNo
                        // List<Object[]> devLevelList = dao.getDevLevel(levelIdList);// 查询权限组下各设备的权限
                    });
                    accDevCmdManager.setPersonToDev(dev.getSn(), personInfoList, false);// 下发人员信息
                    if (personInfoBean.getAccBioTemplateItemList() != null
                        && personInfoBean.getAccBioTemplateItemList().size() > 0) {
                        accDevCmdManager.setPersonBioTemplateToDev(dev.getSn(),
                            personInfoBean.getAccBioTemplateItemList(), false);// 下发指纹信息
                    }
                    setLevelToDev(dev, devLevelList, personInfoList);// 下发权限
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 将list数据，按一定长度截取成字符串，并组成数组--提取到公共util下 人员个数多时，直接使用in在sqlserver下会超出长度
     *
     * @return
     * @since 2018/3/19 15:40
     */
    public List<String> getPersonPropertyList(List<?> personPropertyList, String... split) {
        String strSplit = "";
        if (split != null && split.length > 0) {
            strSplit = split[0];
        }
        List<String> retPropertyList = new ArrayList<String>();
        StringBuffer retPropertyBuffer = new StringBuffer();
        for (int i = 0; i < personPropertyList.size(); i++) {
            String personId = personPropertyList.get(i).toString();
            if (i > 0 && i % AccConstants.LEVEL_SPLIT_COUNT == 0) {
                retPropertyBuffer.append(strSplit).append(personId).append(strSplit).append(",");
                retPropertyList.add(retPropertyBuffer.length() > 0
                    ? retPropertyBuffer.substring(0, retPropertyBuffer.length() - 1) : "");
                retPropertyBuffer = new StringBuffer();
            } else {
                retPropertyBuffer.append(strSplit).append(personId).append(strSplit).append(",");
            }
            if ((i == personPropertyList.size() - 1) && retPropertyBuffer.length() > 0) {
                retPropertyList.add(retPropertyBuffer.substring(0, retPropertyBuffer.length() - 1));
            }
        }
        return retPropertyList;
    }

    @Override
    public Pager getDeptLevel(AccDeptLevelItem codition, int pageNo, int pageSize) {
        Pager pager = getItemsByPage(codition, pageNo, pageSize);
        List<AccDeptLevelItem> accDeptLevelItems = (List<AccDeptLevelItem>)pager.getData();
        if (!accDeptLevelItems.isEmpty()) {
            String areaIds = CollectionUtil.getPropertys(accDeptLevelItems, AccDeptLevelItem::getAuthAreaId);
            List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIds);
            Map<String, AuthAreaItem> authAreaItemMap = CollectionUtil.itemListToIdMap(authAreaItemList);
            accDeptLevelItems.forEach(accDeptLevelItem -> accDeptLevelItem
                .setAuthAreaName(authAreaItemMap.get(accDeptLevelItem.getAuthAreaId()).getName()));
            pager.setData(accDeptLevelItems);
        }
        return pager;
    }

    @Override
    public List<AccLevelItem> getLevelByPersonId(String id) {
        List<String> levelIds = accLevelPersonDao.getLevelIdByPersonId(id);
        List<AccLevelItem> accLevelItems = new ArrayList<>();
        if (!levelIds.isEmpty()) {
            AccLevelItem levelItem = new AccLevelItem();
            levelItem.setInId(levelIds.stream().collect(Collectors.joining(",")));
            accLevelItems = getByCondition(levelItem);
        }
        return accLevelItems;
    }

    @Override
    public AccLevelItem getMasterLevel() {
        AccLevelItem levelItem = new AccLevelItem();
        levelItem.setInitFlag(true);
        return Optional.ofNullable(getByCondition(levelItem)).filter(list -> !list.isEmpty()).map(list -> list.get(0))
            .orElse(null);
    }

    @Override
    public List<SelectItem> getLevelList(String sessionId) {
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(sessionId);
        List<AccLevel> accTimeSegList = null;
        // 根据区域过滤
        if (StringUtils.isNotBlank(areaIds)) {
            accTimeSegList = accLevelDao.findByAuthAreaIdIn(StrUtil.strToList(areaIds));
        } else { // 如果登录的用户是超级用户或没有选择区域则查询出全部数据
            accTimeSegList = accLevelDao.findAll();
        }
        List<SelectItem> selectItems = new ArrayList<SelectItem>();
        SelectItem selectItem = null;
        for (AccLevel accLevel : accTimeSegList) {
            selectItem = new SelectItem();
            selectItem.setValue(accLevel.getId());
            selectItem.setText(accLevel.getName());
            selectItems.add(selectItem);
        }
        return selectItems;
    }

    @Override
    public void immeAddLevelDoor(String levelId, List<String> doorIdList, boolean changeLevel) {
        if (StringUtils.isNotBlank(levelId) && (doorIdList != null && !doorIdList.isEmpty())) {
            List<AccDevice> devList = accDoorDao.getDeviceByDoorId(doorIdList);
            if (changeLevel) {
                // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
                AccDeviceUtil.moveUpParentDevList(devList);

                // 查询出权限组中的人员id
                List<String> personIdList = accLevelPersonDao.getPersonIdByLevelId(levelId);
                if (!personIdList.isEmpty()) {
                    List<String> tempPersonIdList = new ArrayList<>();
                    for (int i = 0; i < devList.size(); i++) {
                        for (String pid : personIdList) {
                            tempPersonIdList.add(pid);
                            if (tempPersonIdList.size() % AccConstants.LEVEL_SPLIT_COUNT == 0) {
                                handleLevelAddDoor(tempPersonIdList, devList.get(i));
                                tempPersonIdList = new ArrayList<>();
                            }
                        }
                        if (!tempPersonIdList.isEmpty()) {
                            handleLevelAddDoor(tempPersonIdList, devList.get(i));
                            tempPersonIdList = new ArrayList<>();
                        }
                    }
                }
            }
            // 下发访客信息到设备
            if (Objects.nonNull(accGetVisTransactionService)) {
                List<AccPersonInfoItem> accPersonInfoItemList =
                    visToAccItem(accGetVisTransactionService.getVisiotrInfosByLevelId(levelId));// 根据权限组id获取访客信息
                if (Objects.nonNull(accPersonInfoItemList) && !accPersonInfoItemList.isEmpty()) {
                    devList.forEach(accDevice -> {
                        syncVisitorLevelToDev(accPersonInfoItemList, accDevice.getId(),
                            (List<String>)CollectionUtil.strToList(levelId));
                    });
                }
            }
        }
    }

    /**
     * 优化大批量数据情况下，数据发送堆栈溢出问题，主要情况下权限组下10w人员，通过权限组加门，多个设备
     *
     * @param personIdList
     * @param dev
     * <AUTHOR>
     * @since 2016年12月8日 下午4:37:27 rateUtil
     */
    private void handleLevelAddDoor(List<String> personIdList, AccDevice dev) {
        AccPersonInfoItem personInfoBean = getPersonByIds(personIdList);
        Map<String, Collection<String>> setLevelMap = AccLevelUtil.setLevelMap(personInfoBean.getPersonList());

        List<AccPersonOptItem> sendPersonOptBeans = new ArrayList<>();
        List<AccPersonOptItem> allPersOptBeans = personInfoBean.getPersonList();
        for (int i = 0; i < allPersOptBeans.size(); i++) {
            sendPersonOptBeans.add(allPersOptBeans.get(i));
            if ((i + 1) % 2000 == 0) {
                accDevCmdManager.setPersonToDev(dev.getSn(), sendPersonOptBeans, false);// 下发人员信息
                sendPersonOptBeans = new ArrayList<>();
            }
        }
        if (sendPersonOptBeans != null && sendPersonOptBeans.size() > 0) {
            accDevCmdManager.setPersonToDev(dev.getSn(), sendPersonOptBeans, false);// 下发人员信息
        }
        List<AccBioTemplateItem> sendBioTempOptBeans = new ArrayList<>();
        List<AccBioTemplateItem> allBioTempBeans = personInfoBean.getAccBioTemplateItemList();
        for (int i = 0; i < allBioTempBeans.size(); i++) {
            sendBioTempOptBeans.add(allBioTempBeans.get(i));
            // 避免内存溢出 modify by qingj.qiu
            if ((i + 1) % 400 == 0) {
                accDevCmdManager.setPersonBioTemplateToDev(dev.getSn(), sendBioTempOptBeans, false);// 下发生物模板信息
                sendBioTempOptBeans = new ArrayList<>();
            }
        }
        if (allBioTempBeans != null && allBioTempBeans.size() > 0) {
            accDevCmdManager.setPersonBioTemplateToDev(dev.getSn(), sendBioTempOptBeans, false);// 下发生物模板信息
        }
        setLevelToDev(dev, setLevelMap.get("id"));// 下发权限(效率太低，后续优化)
        accDevCmdManager.downloadOver(dev.getSn(), false);
    }

    @Override
    public void setPersonLevelToDev(String levelIds, String personIds) {
        AccPersonInfoItem personInfoBean = getPersonByIds((List<String>)CollectionUtil.strToList(personIds));// 组装下发对象
        List<String> levelIdList = (List<String>)CollectionUtil.strToList(levelIds);
        List<AccDevice> devList = accDeviceDao.getDevByLevel(levelIdList);// 获取设备
        if (devList != null && devList.size() > 0) {
            AccDeviceUtil.moveUpParentDevList(devList);// 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
            for (AccDevice dev : devList) {
                List<AccPersonLevelOptItem> accPersonLevelOptItemList = new ArrayList<>();
                levelIdList.forEach(levelId -> {
                    AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
                    accPersonLevelOptItem.setDeviceId(dev.getBusinessId());
                    String timeSegId = accLevelDao.getTimeSegIdByLevelId(levelId);
                    // 时间段数据存在扩展，需要另外查询
                    Long timeSegBId = accTimeSegService.getBusinessIdByTimeSegId(timeSegId);
                    accPersonLevelOptItem.setTimeSegId(timeSegBId);
                    // 设置权限组id add by colin
                    accPersonLevelOptItem.setLevelId(accLevelDao.findOne(levelId).getBusinessId());
                    List<Short> doorNoList = accLevelDoorDao.getDoorNoByLevelIdAndDevId(levelId, dev.getId());
                    if (doorNoList != null && doorNoList.size() > 0) {
                        accPersonLevelOptItem.setDoorNoList(doorNoList);
                        accPersonLevelOptItemList.add(accPersonLevelOptItem);
                    }
                });
                if (personInfoBean.getPersonList().size() > 0) {
                    accDevCmdManager.setPersonToDev(dev.getSn(), personInfoBean.getPersonList(), false);// 下发人员信息
                    if (personInfoBean.getAccBioTemplateItemList() != null
                        && personInfoBean.getAccBioTemplateItemList().size() > 0) {
                        accDevCmdManager.setPersonBioTemplateToDev(dev.getSn(),
                            personInfoBean.getAccBioTemplateItemList(), false); // 下发指纹信息
                    }
                    setLevelToDev(dev, accPersonLevelOptItemList, personInfoBean.getPersonList());// 下发权限
                    accDevCmdManager.downloadOver(dev.getSn(), false);
                }
            }
        }
    }

    @Override
    public Long getDoorCountByLevelId(String levelId) {
        return accLevelDoorDao.getLevelDoorCount(levelId);
    }

    @Override
    public void handleLevelDelDoor(String levelId, String personIds, String deviceIds) {
        AccPersonInfoItem personInfoBean = getPersonByIds((List<String>)CollectionUtil.strToList(personIds));// 组装下发对象
        List<AccDevice> devList = accDeviceDao.findByIdIn((List<String>)CollectionUtil.strToList(deviceIds));
        Map<String, Collection<String>> setLevelMap = AccLevelUtil.setLevelMap(personInfoBean.getPersonList());
        for (AccDevice accDevice : devList) {
            // 删除人员权限
            delPersonLevelFromDevByLevelDelDoor(accDevice, setLevelMap, null);
            // 重新下发该设备中人员的权限，避免权限丢失 (效率太低，后续优化)
            setLevelToDev(accDevice, setLevelMap.get("id"));
        }
    }

    @Override
    public void setVisitorToDev(String levelId, List<String> deviceIdList) {
        if (Objects.nonNull(accGetVisTransactionService)) {
            List<AccDevice> accDeviceList = accDeviceDao.findByIdIn(deviceIdList);
            List<AccPersonInfoItem> accPersonInfoItemList =
                visToAccItem(accGetVisTransactionService.getVisiotrInfosByLevelId(levelId));// 根据权限组id获取访客信息
            if (Objects.nonNull(accPersonInfoItemList) && !accPersonInfoItemList.isEmpty()) {
                accDeviceList.forEach(accDevice -> {
                    syncVisitorLevelToDev(accPersonInfoItemList, accDevice.getId(),
                        (List<String>)CollectionUtil.strToList(levelId));
                });
            }
        }
    }

    /**
     * 访客api获取人员信息转化门禁模型
     *
     * @param vis4AccPersonInfos
     * @return
     */
    private List<AccPersonInfoItem> visToAccItem(List<Acc4VisPersonInfo> vis4AccPersonInfos) {
        if (vis4AccPersonInfos == null) {
            return null;
        }
        List<AccPersonInfoItem> items = new ArrayList<>();
        AccPersonInfoItem temp = null;
        List<AccPersonOptItem> personList = null;
        AccPersonOptItem personOptItem = null;
        List<AccBioTemplateItem> accBioTemplateItemList = null;
        AccBioTemplateItem bioTemplateItem = null;

        for (Acc4VisPersonInfo acc4VisPersonInfo : vis4AccPersonInfos) {
            temp = new AccPersonInfoItem();
            List<Acc4VisPersonOpt> acc4VisPersonOpts = acc4VisPersonInfo.getPersonList();
            if (acc4VisPersonOpts != null) {
                personList = new ArrayList<>();
                for (Acc4VisPersonOpt acc4VisPersonOpt : acc4VisPersonOpts) {
                    personOptItem = new AccPersonOptItem();
                    // 属性拷贝
                    ModelUtil.copyProperties(acc4VisPersonOpt, personOptItem);
                    personList.add(personOptItem);
                }
                temp.setPersonList(personList);
            }

            List<Acc4VisBioTemplate> acc4VisBioTemplates = acc4VisPersonInfo.getAccBioTemplateItemList();
            if (acc4VisBioTemplates != null) {
                accBioTemplateItemList = new ArrayList<>();
                for (Acc4VisBioTemplate acc4VisBioTemplate : acc4VisBioTemplates) {
                    bioTemplateItem = new AccBioTemplateItem();
                    // 属性拷贝
                    ModelUtil.copyProperties(acc4VisBioTemplate, bioTemplateItem);
                    accBioTemplateItemList.add(bioTemplateItem);
                }
                temp.setAccBioTemplateItemList(accBioTemplateItemList);
            }

            items.add(temp);
        }
        return items;
    }

    @Override
    public void delVisitorToDev(String levelId, List<String> deviceIdList) {
        if (Objects.nonNull(accGetVisTransactionService)) {
            List<Acc4VisPersonInfo> accPersonInfoItemList =
                accGetVisTransactionService.getVisiotrInfosByLevelId(levelId);// 根据权限组id获取访客信息
            if (accPersonInfoItemList != null && accPersonInfoItemList.size() > 0) {
                List<AccDevice> accDeviceList = accDeviceDao.findByIdIn(deviceIdList);
                accPersonInfoItemList.stream().forEach(accPersonInfoItem -> {
                    accDeviceList.forEach(accDevice -> {
                        Map<String, Collection<String>> setVisLevelMap = setLevelMap(accPersonInfoItem.getPersonList());
                        delVisitorLevelFromDev(accDevice, setVisLevelMap);// 删除访客权限
                        setVisLevelToDev(accDevice, (List<String>)setVisLevelMap.get("pin"));// (效率太低，后续优化)
                    });
                });
            }
        }
    }

    /**
     * 权限变动，解析数据使用 将 [{"pin": 1, "id": 1, "cardNo":111}] -> {"id": [1,2,3], "pin": [1,2,3]}
     *
     * @param personInfoList
     * @return
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月29日 下午5:00:20
     */
    public static Map<String, Collection<String>> setLevelMap(List<Acc4VisPersonOpt> personInfoList) {
        Map<String, Collection<String>> delParamMap = new HashMap<>();
        List<String> idList = new ArrayList<>();
        List<String> pinList = new ArrayList<>();
        for (Acc4VisPersonOpt personInfo : personInfoList) {
            idList.add(personInfo.getId());
            pinList.add(personInfo.getPin());
        }
        delParamMap.put("id", idList);
        delParamMap.put("pin", pinList);
        return delParamMap;
    }

    @Override
    public void delLevelDoorByParams(String levelId, List<String> doorIdList) {
        accLevelDoorDao.delLevelDoorByParams(levelId, doorIdList);
        if (accProExtService != null) {
            accProExtService.delLevelDoorByParams(levelId, doorIdList);
        }
    }

    @Override
    public void delAccLevelDoorByLevelId(String levelId) {
        accLevelDoorDao.deleteByLevelId(levelId);
    }

    @Override
    public ZKResultMsg levelIsDelete(String ids) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        List<String> delIdList = (List<String>)CollectionUtil.strToList(ids);
        AccLevel accLevel = accLevelDao.findByInitFlag(true);
        if (accLevel != null && delIdList.contains(accLevel.getId())) {
            zkResultMsg.setRet("error");
            zkResultMsg.setData("acc");
            return zkResultMsg;
        }
        if (StringUtils.isNotBlank(ids) && Objects.nonNull(acc4VisLevelService)) {
            String msg = acc4VisLevelService.checkLevelModuleIdIsUsed(delIdList);
            if (StringUtils.isNotBlank(msg)) {
                zkResultMsg.setRet("error");
                zkResultMsg.setMsg(msg);
                zkResultMsg.setData("vis");
            }
        }
        return zkResultMsg;
    }

    @Override
    public void immeDelLevelDoor(String levelId, List<String> doorIdList) {
        if ((doorIdList != null && doorIdList.size() > 0) && StringUtils.isNotBlank(levelId)) {
            List<AccDevice> devList = accDoorDao.getDeviceByDoorId(doorIdList);
            // 下发访客权限
            if (Objects.nonNull(accGetVisTransactionService)) {
                List<Acc4VisPersonInfo> accPersonInfoItemList =
                    accGetVisTransactionService.getVisiotrInfosByLevelId(levelId);// 根据权限组id获取访客信息
                if (accPersonInfoItemList != null && accPersonInfoItemList.size() > 0) {
                    accPersonInfoItemList.stream().forEach(accPersonInfoItem -> {
                        devList.stream().forEach(accDevice -> {
                            Map<String, Collection<String>> setVisLevelMap =
                                setLevelMap(accPersonInfoItem.getPersonList());
                            delVisitorLevelFromDev(accDevice, setVisLevelMap);// 删除访客权限
                            setVisLevelToDev(accDevice, (List<String>)setVisLevelMap.get("pin"));// (效率太低，后续优化)
                        });
                    });
                }
            }
            if (accLevelPersonDao.countByAccLevel_Id(levelId) > 0)// 权限组有人
            {
                // 查询出权限组中的人员id
                List<String> personIdList = accLevelPersonDao.getPersonIdByLevelId(levelId);
                if (personIdList.size() > 0) {
                    List<String> tempPersonIdList = new ArrayList<>();
                    for (int i = 0; i < devList.size(); i++) {
                        int total = (int)((i * 100.0 / devList.size())); // 总进度
                        total = total == 100 ? 99 : total;// 防止前端过快结束
                        int currentProcess = 0;
                        for (String pid : personIdList) {
                            tempPersonIdList.add(pid);
                            currentProcess++;
                            if (tempPersonIdList.size() % AccConstants.LEVEL_SPLIT_COUNT == 0) {
                                logger.info("delete handle person ids：" + tempPersonIdList.toString());
                                handleLevelDelDoor(levelId, tempPersonIdList, devList.get(i), doorIdList, total,
                                    (int)(currentProcess * 100.0 / personIdList.size()));
                                tempPersonIdList = new ArrayList<>();
                            }
                        }
                        if (tempPersonIdList.size() > 0) {
                            logger.info("delete handle person ids：" + tempPersonIdList.toString());
                            handleLevelDelDoor(levelId, tempPersonIdList, devList.get(i), doorIdList, total,
                                (int)(currentProcess * 100.0 / personIdList.size()));
                            tempPersonIdList = new ArrayList<>();
                        }
                    }
                }
            } else {
                accLevelDoorDao.delLevelDoorByParams(levelId, doorIdList);// 权限组删除门--数据库
            }
        }
    }

    @Override
    public List<String> searchPersonByDev(String devId) {
        return accLevelDao.getPersonIdByDevId(devId);
    }

    @Override
    public void immeDelLevel(String levelId, List<String> personIdList) {
        List<AccDevice> devList = accDeviceDao.getDevByLevel(levelId);// 查询权限组中的设备
        // Map<String, List<Object>> devLevelMap = getDevLevelByIds(levelId);// 查询出权限组中每个设备的 门组合/权限
        // accLevelPersonDao.delLevelPersonByLevelIds(levelId);
        // 删除数据库中人员的权限
        delBatchLevel((List<String>)CollectionUtil.strToList(levelId), personIdList);

        Map<String, Object> personParamMap = new HashMap<String, Object>();
        personParamMap.put("idList", personIdList);
        List<AccPersonOptItem> personInfoList = getPersonByIds(personParamMap);
        Map<String, Collection<String>> personInfoMap = AccLevelUtil.setLevelMap(personInfoList);
        for (AccDevice dev : devList) {
            // if(AccDeviceUtil.isSupportFun(dev.getSn(), AccConstUtil.DEL_BY_AND))// 指定删除哪条权限
            // {
            // delPersonLevelFromDev(dev, personInfoMap, devLevelMap);
            // }
            // else// 全删全下
            accLevelDoorDao.delLevelDoorByParams(levelId, dev.getId());// 权限组删除门--数据库
            delPersonLevelFromDev(dev, personInfoMap);
            setLevelToDev(dev, personInfoMap.get("id"));// (效率太低，后续优化)
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                logger.error("Exception:", e);
            }
        }
    }

    @Override
    public void immeDelLevel(String deviceId, String levelId, List<String> personIdList) {
        AccDevice accDevice = accDeviceDao.findById(deviceId).orElse(null);
        // 删除数据库中人员的权限
        delBatchLevel((List<String>)CollectionUtil.strToList(levelId), personIdList);

        if (Objects.nonNull(accDevice)) {
            Map<String, Object> personParamMap = new HashMap<>();
            personParamMap.put("idList", personIdList);
            List<AccPersonOptItem> personInfoList = getPersonByIds(personParamMap);
            Map<String, Collection<String>> personInfoMap = AccLevelUtil.setLevelMap(personInfoList);
            accLevelDoorDao.delLevelDoorByParams(levelId, accDevice.getId());// 权限组删除门--数据库
            delPersonLevelFromDev(accDevice, personInfoMap);
            setLevelToDev(accDevice, personInfoMap.get("id"));// (效率太低，后续优化)
        }
    }

    @Override
    public ZKResultMsg getPersonLevelDoor(String personPin, boolean isAdmin) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        PersPersonItem persPersonItem = persPersonService.getItemByPin(personPin);
        if (persPersonItem != null) {
            List<AccCloudDoorItem> accCloudDoorItemList = new ArrayList<>();
            List<AccDoor> accDoorList = new ArrayList<>();
            List<String> topDoorIds = new ArrayList<>();
            List<AccAppTopDoorByPerson> accAppTopDoorByPersonList =
                accAppTopDoorByPersonDao.findByPersonIdOrderByCreateTimeAsc(persPersonItem.getId());
            if (isAdmin) {
                // 管理员查询所有门
                accDoorList.addAll(accDoorDao.findAll());
            } else {
                // 非管理员根据权限设置获取门列表
                List<String> doorIds = getDoorIdsForApp(persPersonItem.getId());
                accDoorList = accDoorDao.findByIdIn(doorIds);
            }
            // 存在置顶门则优先设置置顶门数据
            if (!CollectionUtil.isEmpty(accAppTopDoorByPersonList)) {
                Map<String, AccDoor> accDoorMap = CollectionUtil.modelListToIdMap(accDoorList);
                for (AccAppTopDoorByPerson accAppTopDoorByPerson : accAppTopDoorByPersonList) {
                    if (accDoorMap.containsKey(accAppTopDoorByPerson.getDoorId())) {
                        AccDoor accDoor = accDoorMap.get(accAppTopDoorByPerson.getDoorId());
                        AccCloudDoorItem accCloudDoorItem = new AccCloudDoorItem();
                        accCloudDoorItem.setDevSn(accDoor.getDevice().getSn());
                        accCloudDoorItem.setDoorNo(accDoor.getDoorNo());
                        accCloudDoorItem.setDoorName(accDoor.getName());
                        accCloudDoorItem.setTopDoor(AccConstants.ACC_DOOR_TOP);
                        accCloudDoorItemList.add(accCloudDoorItem);
                        topDoorIds.add(accDoor.getId());
                    }
                }
            }
            // 组装其他门数据
            if (!CollectionUtil.isEmpty(accDoorList)) {
                for (AccDoor accDoor : accDoorList) {
                    if (!topDoorIds.contains(accDoor.getId())) {
                        AccCloudDoorItem accCloudDoorItem = new AccCloudDoorItem();
                        accCloudDoorItem.setDevSn(accDoor.getDevice().getSn());
                        accCloudDoorItem.setDoorNo(accDoor.getDoorNo());
                        accCloudDoorItem.setDoorName(accDoor.getName());
                        accCloudDoorItem.setTopDoor(AccConstants.ACC_DOOR_DEFAULT);
                        accCloudDoorItemList.add(accCloudDoorItem);
                    }
                }
            }
            // 查询门状态
            accCloudDoorItemList.forEach(item -> {
                String devSn = item.getDevSn();
                DeviceDoorState deviceDoorState = admsDeviceService.getDeviceDoorState(devSn);
                String connect = accDeviceService.getStatus(devSn);
                int index = item.getDoorNo() - 1;
                int doorConnect = Integer.parseInt(connect);
                if (StringUtils.isNotBlank(deviceDoorState.getDoor())
                        && deviceDoorState.getDoor().split(",").length > index) {
                    doorConnect = Integer.parseInt(deviceDoorState.getDoor().split(",")[index]);
                }
                int doorState = Short.parseShort(connect);
                if (Short.parseShort(connect) == ConstUtil.DEV_STATE_ONLINE) {
                    if ((doorConnect & 1) == ConstUtil.DEV_STATE_OFFLINE) {
                        doorState = ConstUtil.DEV_STATE_OFFLINE;
                    } else {
                        doorState = doorConnect;
                    }
                }
                item.setDoorStatus(String.valueOf(doorState));
            });
            resultMsg.setData(accCloudDoorItemList);
        }
        return resultMsg;
    }

    @Override
    public ZKResultMsg getLevelByPersonPin(String personPins) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(personPins);
        JSONArray dataArray = new JSONArray();
        if (!persPersonItemList.isEmpty()) {
            persPersonItemList.forEach(item -> {
                List<AccLevelItem> accLevelItemList = getLevelByPersonId(item.getId());
                if (!accLevelItemList.isEmpty()) {
                    JSONObject data = new JSONObject();
                    data.put("pin", item.getPin());
                    data.put("levels", accLevelItemList);
                    dataArray.add(data);
                }
            });
            resultMsg.setData(dataArray.toJSONString());
        }
        return resultMsg;
    }

    @Override
    public ZKResultMsg getLevelByAreaCodes(String areaCodes, int page, int pageSize) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        AccLevelItem accLevelItem = new AccLevelItem();
        if (StringUtils.isNotBlank(areaCodes)) {
            Set<String> areaIds = new HashSet<>();
            Collection<String> codes = CollectionUtil.strToList(areaCodes);
            for (String code : codes) {
                AuthAreaItem authAreaItem = authAreaService.getItemByCode(code);
                areaIds.add(authAreaItem.getId());
            }
            accLevelItem.setAreaIds(StringUtils.join(areaIds, ","));
        }
        Pager pager = getItemsByPage(accLevelItem, page, pageSize);
        List<AccLevelItem> accLevelItemList = (List<AccLevelItem>)pager.getData();
        List<AccApiLevelItem> accApiLevelItems = new ArrayList<>();
        for (AccLevelItem item : accLevelItemList) {
            AccApiLevelItem apiLevel = AccApiLevelItem.createApiLevel(item);
            if (apiLevel != null) {
                accApiLevelItems.add(AccApiLevelItem.createApiLevel(item));
            }
        }
        if (!accApiLevelItems.isEmpty()) {
            pager.setData(accApiLevelItems);
            resultMsg.setData(pager);
        }
        return resultMsg;
    }

    @Override
    public List<String> getAllLevelNames() {
        return accLevelDao.getAllLevelNames();
    }

    @Override
    public List<String> getPersonIdsByLevelIdIn(List<String> levelIds) {
        return accLevelPersonDao.getPersonIdByLevelIdIn(levelIds);
    }

    @Override
    public Map<String, String> getLevelNameIdMap() {
        Map<String, String> nameIdMap = new HashMap<>();
        List<Object[]> objectAryList = accLevelDao.getNameAndId();
        objectAryList.forEach(objectAry -> {
            nameIdMap.put(objectAry[0].toString(), objectAry[1].toString());
        });
        return nameIdMap;
    }

    @Override
    public List<AccPersonOptItem> getPersonByIds(Map<String, Object> personParamMap) {
        String param = "id";
        List<Object> personParamList = new ArrayList<Object>();
        if (personParamMap.containsKey("idList")) {
            personParamList = (List<Object>)personParamMap.get("idList");
        } else {
            if (personParamMap.containsKey("pin")) {// 按pin删除
                param = "pin";
            }
            personParamList = filterPersonParamData(param, personParamMap);// 过滤人员数据
        }
        return accPersonService.getPersonPinAndPersonIdByParam(param, personParamList);// 获取人员id和工号

    }

    /**
     * 过滤人员数据
     *
     * @param param
     * @param personParamMap
     * @return List<Object>
     * <AUTHOR>
     * @since 2014年8月28日 上午11:02:41
     */
    protected List<Object> filterPersonParamData(String param, Map<String, Object> personParamMap) {
        List<Object> personParamList = new ArrayList<Object>();
        String[] personArray = personParamMap.get(param).toString().split(",");
        if ("pin".equals(param)) {
            for (int i = 0; i < personArray.length; i++) {
                personParamList.add(personArray[i].trim());// 必须转化类型
            }
        } else {
            personParamList = CollectionUtil.arrayToList(personArray);
        }
        return personParamList;
    }

    /**
     * @Description: 根据人员Ids获取下发命令所需的人员
     * @author: mingfa.zheng
     * @date: 2018/8/2 11:37
     * @param: [personIds]
     * @return: com.zkteco.zkbiosecurity.acc.vo.AccPersonInfoItem
     **/
    @Override
    public AccPersonInfoItem getPersonByIds(List<String> personIds) {
        AccPersonInfoItem personInfoBean = new AccPersonInfoItem();
        // 人员信息的集合
        List<AccPersonOptItem> personInfoList = new ArrayList<>();
        // 人员生物模版信息的集合
        List<AccBioTemplateItem> bioTemplateList = new ArrayList<>();
        // 对人员进行分组
        List<List<String>> personIdsList = CollectionUtil.split(personIds, AccConstants.LEVEL_SPLIT_COUNT);
        personIdsList.forEach(personIdList -> {
            List<PersPersonItem> persPersonItemList = persPersonService.getPersonItemForDevByIds(personIdList);
            if (persPersonItemList != null && persPersonItemList.size() > 0) {
                // 将人员信息缓存到map中
                Map<String, PersPersonItem> persPersonItemMap = CollectionUtil.itemListToIdMap(persPersonItemList);
                List<AccPerson> accPersonAllList = accPersonDao.findByPersonIdIn(personIdList);
                Map<String, AccPerson> accPersonMap =
                    CollectionUtil.listToKeyMap(accPersonAllList, AccPerson::getPersonId);
                List<AccPersonCombOpenPerson> accPersonCombOpenPersonAllList =
                    accPersonCombOpenPersonDao.findByPersPersonIdIn(personIdList); // 查询是否有设置多人开门组
                Map<String, AccPersonCombOpenPerson> accPersonCombOpenPersonMap = CollectionUtil
                    .listToKeyMap(accPersonCombOpenPersonAllList, AccPersonCombOpenPerson::getPersPersonId);

                // 将人员卡号进行分组并缓存到map中
                List<PersCardItem> persCardItemAllList = persCardService.getAllCardByPersonIdList(personIdList);
                Map<String, List<PersCardItem>> persCardItemMap =
                    persCardItemAllList.stream().collect(Collectors.groupingBy(PersCardItem::getPersonId));
                // 将人员生物模板进行分组并缓存到map中
                List<PersBioTemplateItem> persBioTemplateItemAllList =
                    persBioTemplateService.getItemByPersonIdList(personIdList);
                Map<String, List<PersBioTemplateItem>> persBioTemplateItemMap = persBioTemplateItemAllList.stream()
                    .collect(Collectors.groupingBy(PersBioTemplateItem::getPersonId));
                // 将人员比对模板进行分组并缓存到map中
                List<PersBioPhotoItem> persBioPhotoItemAllList = persBioPhotoService.findByPersonIdIn(personIdList);
                Map<String, List<PersBioPhotoItem>> persBioPhotoItemMap =
                    persBioPhotoItemAllList.stream().collect(Collectors.groupingBy(PersBioPhotoItem::getPersonId));
                persPersonItemMap.forEach((personId, persPersonItem) -> {
                    // 人员启禁用凭证为禁用，需要过滤下发，不组装人员信息
                    if (Objects.nonNull(persPersonItem.getEnabledCredential())
                        && !persPersonItem.getEnabledCredential()) {
                        return;
                    }
                    // 组装人员权限信息
                    List<PersCardItem> persCardItemList = persCardItemMap.get(personId);
                    if (persCardItemList != null && persCardItemList.size() > 0) {
                        persCardItemList.forEach(persCardItem -> {
                            AccPersonOptItem accPersonOptItem = new AccPersonOptItem();
                            accPersonOptItem.setCardNo(persCardItem.getCardNo());
                            accPersonOptItem.setCardType(persCardItem.getCardType());
                            accPersonOptItem.setCardState(persCardItem.getCardState());
                            // if (accProExtService != null) {
                            // accProExtService.setThreatLevel(accPersonOptItem, personId);
                            // }
                            setAccPersonOptItem(accPersonOptItem, persPersonItem, accPersonMap.get(personId),
                                accPersonCombOpenPersonMap.get(personId));
                            personInfoList.add(accPersonOptItem);
                        });
                    } else {
                        AccPersonOptItem accPersonOptItem = new AccPersonOptItem();
                        accPersonOptItem.setCardNo("");
                        accPersonOptItem.setCardType(null);
                        accPersonOptItem.setCardState(null);
                        // if (accProExtService != null) {
                        // accProExtService.setThreatLevel(accPersonOptItem, personId);
                        // }
                        setAccPersonOptItem(accPersonOptItem, persPersonItem, accPersonMap.get(personId),
                            accPersonCombOpenPersonMap.get(personId));
                        personInfoList.add(accPersonOptItem);
                    }
                    // 组装生物模板信息
                    List<PersBioTemplateItem> persBioTemplateItemList = persBioTemplateItemMap.get(personId);
                    if (persBioTemplateItemList != null && persBioTemplateItemList.size() > 0) {
                        persBioTemplateItemList.forEach(persBioTemplateItem -> {
                            AccBioTemplateItem accBioTemplateItem = new AccBioTemplateItem();
                            accBioTemplateItem
                                .setPin(StringUtils.isNotBlank(persPersonItem.getPin()) ? persPersonItem.getPin() : "");
                            Short templateID = persBioTemplateItem.getTemplateNo();
                            accBioTemplateItem.setTemplateId(templateID);
                            accBioTemplateItem.setDuress(
                                persBioTemplateItem.getDuress() != null ? persBioTemplateItem.getDuress() : false);
                            accBioTemplateItem.setTemplate(StringUtils.isNotBlank(persBioTemplateItem.getTemplate())
                                ? persBioTemplateItem.getTemplate() : "");
                            accBioTemplateItem.setType(persBioTemplateItem.getBioType());
                            accBioTemplateItem.setTemplateIndex(persBioTemplateItem.getTemplateNoIndex());
                            accBioTemplateItem.setVersion(persBioTemplateItem.getVersion());
                            accBioTemplateItem.setFormat(AccBioTemplateItem.FORMAT_BASE64);
                            bioTemplateList.add(accBioTemplateItem);
                        });
                    }
                    List<PersBioPhotoItem> persBioPhotoItemList = persBioPhotoItemMap.get(personId);
                    if (persBioPhotoItemList != null && persBioPhotoItemList.size() > 0) {
                        persBioPhotoItemList.forEach(persBioPhotoItem -> {
                            AccBioTemplateItem accBioTemplateItem = new AccBioTemplateItem();
                            accBioTemplateItem.setPin(persPersonItem.getPin());
                            accBioTemplateItem.setFormat(AccBioTemplateItem.FORMAT_PHOTO);
                            accBioTemplateItem.setType(persBioPhotoItem.getBioType());
                            accBioTemplateItem.setUrl(persBioPhotoItem.getPhotoPath().replace("\\", "/"));
                            bioTemplateList.add(accBioTemplateItem);
                        });
                    }
                });
            }
        });
        if (accProExtService != null) {
            accProExtService.setThreatLevel(personInfoList);
        }
        personInfoBean.setPersonList(personInfoList);
        personInfoBean.setAccBioTemplateItemList(bioTemplateList);
        return personInfoBean;
    }

    @Override
    public void setPersonLevelToDevice(String deviceId, AccPersonInfoItem personInfoBean, boolean isSyncData) {
        List<AccPersonOptItem> sendPersonOptBeans = new ArrayList<>();
        List<AccPersonOptItem> personInfoList = personInfoBean.getPersonList();
        List<String> pins = null;
        AccDevice accDevice = accDeviceDao.findById(deviceId).orElse(new AccDevice());
        boolean isSupportMulCard = accDeviceOptionService.isSupportFunList(accDevice.getSn(), 7);
        boolean isSupportBioPhoto = accDeviceOptionService.isSupportFunList(accDevice.getSn(), 38);
        for (int i = 0; i < personInfoList.size(); i++) {
            sendPersonOptBeans.add(personInfoList.get(i));
            // 分批处理，避免内存溢出
            if ((i + 1) % AccConstants.LEVEL_SPLIT_COUNT == 0) {
                pins = new ArrayList<>();
                for (AccPersonOptItem accPersonOptBean : sendPersonOptBeans) {
                    pins.add(accPersonOptBean.getPin());
                }
                if (pins.size() > 0 && !isSyncData) {
                    if (isSupportMulCard) {
                        accDevCmdManager.delMulCardUserFromDev(accDevice.getSn(), pins, false);// 多卡数据类似于指纹，下发之前需要按照pin号全部删除然后再重新下发
                    }
                    if (isSupportBioPhoto) {
                        accDevCmdManager.delAllBiophotoByPins(accDevice.getSn(), pins, false);// 根据pin号删除比对照片
                    }
                    // accPersonBiz.delAllBiotemplateByPins(ConstUtil.SYSTEM_MODULE_ACC, dev,
                    // pins);//下发之前需要按照pin号全部删除然后再重新下发
                    accDevCmdManager.delPersonBioTemplateFromDev(accDevice.getSn(), pins, false);
                }
                accDevCmdManager.setPersonToDev(accDevice.getSn(), sendPersonOptBeans, false);// 下发人员信息
                sendPersonOptBeans = new ArrayList<>();
            }
        }
        if (sendPersonOptBeans != null && sendPersonOptBeans.size() > 0) {
            pins = new ArrayList<>();
            for (AccPersonOptItem accPersonOptBean : sendPersonOptBeans) {
                pins.add(accPersonOptBean.getPin());
            }
            if (pins.size() > 0 && !isSyncData) {
                if (isSupportMulCard) {
                    accDevCmdManager.delMulCardUserFromDev(accDevice.getSn(), pins, false);// 多卡数据类似于指纹，下发之前需要按照pin号全部删除然后再重新下发
                }
                if (isSupportBioPhoto) {
                    accDevCmdManager.delAllBiophotoByPins(accDevice.getSn(), pins, false);// 根据pin号删除比对照片
                }
                // accPersonBiz.delAllBiotemplateByPins(ConstUtil.SYSTEM_MODULE_ACC, dev,
                // pins);//下发之前需要按照pin号全部删除然后再重新下发
                accDevCmdManager.delPersonBioTemplateFromDev(accDevice.getSn(), pins, false);
            }
            accDevCmdManager.setPersonToDev(accDevice.getSn(), sendPersonOptBeans, false);// 下发人员信息
        }

        List<AccBioTemplateItem> sendBioTempOptBeans = new ArrayList<>();
        List<AccBioTemplateItem> allBioTempBeans = personInfoBean.getAccBioTemplateItemList();
        for (int i = 0; i < allBioTempBeans.size(); i++) {
            sendBioTempOptBeans.add(allBioTempBeans.get(i));
            // 避免内存溢出 modify by qingj.qiu
            if ((i + 1) % 400 == 0) {
                accDevCmdManager.setPersonBioTemplateToDev(accDevice.getSn(), sendBioTempOptBeans, false);// 下发生物模板信息
                sendBioTempOptBeans = new ArrayList<>();
            }
        }
        if (sendBioTempOptBeans != null && sendBioTempOptBeans.size() > 0) {
            accDevCmdManager.setPersonBioTemplateToDev(accDevice.getSn(), sendBioTempOptBeans, false);// 下发生物模板信息
        }

        List<String> personIdList = new ArrayList<>();
        for (AccPersonOptItem person : personInfoList) {
            if (StringUtils.isBlank(person.getCardNo()) || person.getCardType() == 0)// 由于多卡数据会造成一个人多条数据，所以只要主卡或者无卡人的记录
            {
                personIdList.add(person.getId());
            }
        }
        setLevelToDev(accDevice, personIdList);// 下发权限
        // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
        accDevCmdManager.downloadOver(accDevice.getSn(), false);
    }

    @Override
    public void setPersonCardLevelToDevice(String deviceId, AccPersonInfoItem personInfoBean) {
        List<AccPersonOptItem> sendPersonOptBeans = new ArrayList<>();
        List<AccPersonOptItem> personInfoList = personInfoBean.getPersonList();
        List<String> pins = null;
        AccDevice accDevice = accDeviceDao.findById(deviceId).orElse(new AccDevice());
        boolean isSupportMulCard = accDeviceOptionService.isSupportFunList(accDevice.getSn(), 7);
        for (int i = 0; i < personInfoList.size(); i++) {
            sendPersonOptBeans.add(personInfoList.get(i));
            // 分批处理，避免内存溢出
            if ((i + 1) % 2000 == 0) {
                pins = new ArrayList<>();
                for (AccPersonOptItem accPersonOptBean : sendPersonOptBeans) {
                    pins.add(accPersonOptBean.getPin());
                }
                if (pins.size() > 0) {
                    if (isSupportMulCard) {
                        accDevCmdManager.delMulCardUserFromDev(accDevice.getSn(), pins, false);// 多卡数据类似于指纹，下发之前需要按照pin号全部删除然后再重新下发
                    }
                }
                accDevCmdManager.setPersonToDev(accDevice.getSn(), sendPersonOptBeans, false);// 下发人员信息
                sendPersonOptBeans = new ArrayList<>();
            }
        }
        if (sendPersonOptBeans != null && sendPersonOptBeans.size() > 0) {
            pins = new ArrayList<>();
            for (AccPersonOptItem accPersonOptBean : sendPersonOptBeans) {
                pins.add(accPersonOptBean.getPin());
            }
            if (pins.size() > 0) {
                if (isSupportMulCard) {
                    accDevCmdManager.delMulCardUserFromDev(accDevice.getSn(), pins, false);// 多卡数据类似于指纹，下发之前需要按照pin号全部删除然后再重新下发
                }
            }
            accDevCmdManager.setPersonToDev(accDevice.getSn(), sendPersonOptBeans, false);// 下发人员信息
        }

        List<String> personIdList = new ArrayList<>();
        for (AccPersonOptItem person : personInfoList) {
            if (StringUtils.isBlank(person.getCardNo()) || person.getCardType() == 0)// 由于多卡数据会造成一个人多条数据，所以只要主卡或者无卡人的记录
            {
                personIdList.add(person.getId());
            }
        }
        setLevelToDev(accDevice, personIdList);// 下发权限
    }

    @Override
    public void setPersonCardToDevice(String deviceId, AccPersonInfoItem accPersonInfoItem) {
        AccDevice accDevice = accDeviceDao.findById(deviceId).orElse(null);
        if (Objects.nonNull(accDevice)) {
            List<AccPersonOptItem> personInfoList = accPersonInfoItem.getPersonList();
            List<List<AccPersonOptItem>> newPersonInfoList =
                CollectionUtil.split(personInfoList, CollectionUtil.splitSize);
            boolean isSupportMulCard = accDeviceOptionService.isSupportFunList(accDevice.getSn(), 7);
            List<String> pins;
            for (List<AccPersonOptItem> personOptItemList : newPersonInfoList) {
                pins = new ArrayList<>();
                for (AccPersonOptItem personOptItem : personOptItemList) {
                    pins.add(personOptItem.getPin());
                }
                // 多卡数据下发之前需要按照pin号全部删除然后再重新下发
                if (isSupportMulCard) {
                    accDevCmdManager.delMulCardUserFromDev(accDevice.getSn(), pins, false);
                }
                // 下发卡号到设备
                accDevCmdManager.setPersonCardToDevice(accDevice.getSn(), personOptItemList, false);
            }
        }
    }

    @Override
    public String getUserIdBySessionId(String sessionId) {
        return authUserService.getUserIdBySessionId(sessionId);
    }

    @Override
    public List<AccLevelItem> getExportLevelItemList(AccLevelItem accLevelItem, int beginIndex, int endIndex) {
        return accLevelDao.getItemsDataBySql(AccLevelItem.class, SQLUtil.getSqlByItem(accLevelItem), beginIndex,
            endIndex, true);
    }

    @Override
    public ZKResultMsg importLevelData(List<AccLevelItem> itemList, boolean updateExistData) {
        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("pers_import_overData", itemList.size());
        }
        List<List<AccLevelItem>> accLevelImportItems = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        // 导入的数据中，所有权限组名称已经存在的权限组，key为权限组名称
        Map<String, AccLevel> existAccLevelMap = new HashMap<>(itemList.size());
        // 导入的数据中，所有名称已经存在的区域，key为区域名称
        Map<String, AuthAreaItem> existAuthAreaMap = new HashMap<>(itemList.size());
        // 导入的数据中，所有名称已经存在的门禁时间段，key为门禁时间段名称
        Map<String, AccTimeSeg> existAccTimeSegMap = new HashMap<>(itemList.size());
        // Map<权限组id，List<读头id>>,用于下发权限
        Map<String, List<AccDevice>> levelDeviceMap = new HashMap<>();
        // 导入权限组名称的集合，用于过滤重复的权限组名称
        Set<String> uniqueLevelSet = new HashSet<>();
        int notUpdateSize = 0;
        int importSize = itemList.size();
        int beginProgress = 20;
        // 已存在数量
        int existCount = 0;
        // 导入数据是第几行行数
        int index = 2;
        for (List<AccLevelItem> importItemList : accLevelImportItems) {
            // 取出待导入权限组名称
            Collection<String> importLevelNames = new HashSet<>();
            for (AccLevelItem item : importItemList) {
                if (StringUtils.isNotBlank(item.getName())) {
                    importLevelNames.add(item.getName().trim());
                }
            }
            // 根据权限组名称找出已经存在的权限组信息
            List<AccLevel> accLevels = accLevelDao.findByNameIn(importLevelNames);
            if (accLevels != null && accLevels.size() > 0) {
                existAccLevelMap.putAll(CollectionUtil.listToKeyMap(accLevels, AccLevel::getName));
            }

            // 取出待导入权限组的区域名称
            Collection<String> importLevelAreaNames =
                CollectionUtil.getPropertyList(importItemList, AccLevelItem::getAuthAreaName, "-1");
            // 根据区域名称找出已经存在的区域信息
            List<AuthAreaItem> areaItems = authAreaService.getItemsByNameIn(importLevelAreaNames);
            if (areaItems != null && areaItems.size() > 0) {
                existAuthAreaMap.putAll(CollectionUtil.listToKeyMap(areaItems, AuthAreaItem::getName));
            }

            // 取出待导入权限组的时间段名称
            Collection<String> importTimeSegNames =
                CollectionUtil.getPropertyList(importItemList, AccLevelItem::getTimeSegName, "-1");
            // 根据区域名称找出已经存在的区域信息
            List<AccTimeSeg> accTimeSegList = accTimeSegDao.findByNameIn(importTimeSegNames);
            if (accTimeSegList != null && accTimeSegList.size() > 0) {
                existAccTimeSegMap.putAll(CollectionUtil.listToKeyMap(accTimeSegList, AccTimeSeg::getName));
            }
        }

        AccLevel accLevel = null;
        // 名称与待入库的权限组关系 key为权限组名称name
        HashMap<String, AccLevel> nameAndAccLevelMap = new HashMap<>(itemList.size());
        Iterator<AccLevelItem> itemIterator = itemList.iterator();
        while (itemIterator.hasNext()) {
            index++;
            AccLevelItem accLevelItem = itemIterator.next();
            // 权限组名称非空校验
            if (StringUtils.isBlank(accLevelItem.getName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_nameNotNull"))));
                itemIterator.remove();
                logger.info("Row " + index + ": AccLevel name is empty");
                continue;
            }

            // 区域名称非空校验
            if (StringUtils.isBlank(accLevelItem.getAuthAreaName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("base_areaService_setArea03"))));
                itemIterator.remove();
                logger.info("Row " + index + ": AccLevel authAreaName is empty");
                continue;
            }

            // 时间段名称非空校验
            if (StringUtils.isBlank(accLevelItem.getTimeSegName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                    .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_timeSegNameNotNull"))));
                itemIterator.remove();
                logger.info("Row " + index + ": AccLevel timeSegName is empty");
                continue;
            }
            String levelName = accLevelItem.getName().trim().replaceAll("\t", "");
            // 不更新已存在数据
            if (!updateExistData && existAccLevelMap.containsKey(levelName)) {
                notUpdateSize++;
                itemIterator.remove();
                logger.info("Row " + index + ": Do not update existing data");
                continue;
            }

            // 唯一校验
            if (!uniqueLevelSet.add(levelName)) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                    .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_nameExist", levelName))));
                itemIterator.remove();
                logger.info("Row " + index + ": Level name " + levelName + " is repeated");
                continue;
            }

            // 名称不能包含特殊字符
            if (PersRegularUtil.hasSpecialChar(levelName)) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_noSpecialChar", levelName))));
                itemIterator.remove();
                logger.info(
                    "Row " + index + ": Level name can not contain special symbols ! The level name is: " + levelName);
                continue;
            }

            // 判断是否存在非法字符串
            if (PersRegularUtil.hasSqlKey(levelName)) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                    .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("common_xss_error_info", levelName))));
                itemIterator.remove();
                logger.info(
                    "Row " + index + ": Level name can not contain illegal string ! The level name is: " + levelName);
                continue;
            }

            // 判断区域名称是否存在，不存在则不进行操作
            if (!existAuthAreaMap.containsKey(accLevelItem.getAuthAreaName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_areaNotExist"))));
                itemIterator.remove();
                logger.info("Row " + index
                    + ": The area does not exist, and the original data is retained in the update state");
                continue;
            }

            // 判断时间段名称是否存在，不存在则不进行操作
            if (!existAccTimeSegMap.containsKey(accLevelItem.getTimeSegName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                    .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_timeSegNotExist"))));
                itemIterator.remove();
                logger.info("Row " + index
                    + ": The timeSeg does not exist, and the original data is retained in the update state");
                continue;
            }
            // 判断权限组是否已经存在，不存在则新增
            // 已存在的权限组更新所有字段
            accLevel = existAccLevelMap.get(levelName);
            AccTimeSeg accTimeSeg = existAccTimeSegMap.get(accLevelItem.getTimeSegName());
            AuthAreaItem authAreaItem = existAuthAreaMap.get(accLevelItem.getAuthAreaName());
            if (accLevel == null) {
                accLevel = new AccLevel();
                accLevel.setName(levelName);
            } else if (!accLevel.getTimeSegId().equals(accTimeSeg.getId())) {
                // 时间段不一样的重新下发权限
                List<AccDevice> accDeviceList = new ArrayList<>();
                List<AccLevelDoor> accLevelDoorList = accLevel.getAccDoorList();
                for (AccLevelDoor levelDoor : accLevelDoorList) {
                    if (levelDeviceMap.containsKey(accLevel.getId())) {
                        accDeviceList = levelDeviceMap.get(accLevel.getId());
                    }
                    AccDoor accDoor = levelDoor.getAccDoor();
                    accDeviceList.add(accDoor.getDevice());
                    if (accDoor.getDevice().getParentDevice() != null) {
                        accDeviceList.add(accDoor.getDevice().getParentDevice());
                    }
                    levelDeviceMap.put(accLevel.getId(), accDeviceList);
                }
            }
            accLevel.setTimeSegId(accTimeSeg.getId());
            accLevel.setAuthAreaId(authAreaItem.getId());
            nameAndAccLevelMap.put(accLevel.getName(), accLevel);
            if (existAccLevelMap.get(accLevel.getName()) != null) {
                existCount++;
            }

        }
        // 失败数量
        int faildCount = importSize - itemList.size() - notUpdateSize;
        if (!CollectionUtil.isEmpty(nameAndAccLevelMap)) {
            // 把导入的数据分割，分批处理，一次处理800人
            List<List<AccLevel>> accLevelList =
                CollectionUtil.split(nameAndAccLevelMap.values(), CollectionUtil.splitSize);
            float avgProgress = (float)60 / accLevelImportItems.size();
            int i = 0;
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                I18nUtil.i18nCode("common_op_processing")));

            // 保存权限组信息
            for (List<AccLevel> levels : accLevelList) {
                int progress = beginProgress + 10 + (int)(avgProgress * i++);
                progressCache.setProcess(new ProcessBean(progress, progress, ""));
                accLevelDao.save(levels);
            }
            // 更新的数据重新下发；
            setImportLevelInfoToDev(levelDeviceMap);
        }
        if (existCount > 0 && updateExistData) {
            // 成功：%s 条，更新：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result3", nameAndAccLevelMap.size(), existCount, faildCount)));
        } else {
            // 成功：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result", nameAndAccLevelMap.size() + notUpdateSize, faildCount)));
        }
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 导入权限组信息，如果时间段有发生改变，则权限组要重新下发
     *
     * @param levelDeviceMap:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022-11-23 11:17
     * @since 1.0.0
     */
    private void setImportLevelInfoToDev(Map<String, List<AccDevice>> levelDeviceMap) {
        for (String levelId : levelDeviceMap.keySet()) {
            // 下发访客权限
            syncTimeSegVisLevelToDev(levelId);
            List<AccDevice> accDevices = levelDeviceMap.get(levelId);
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
            AccDeviceUtil.moveUpParentDevList(accDevices);
            List<String> deviceIdList = (List<String>)CollectionUtil.getModelIdsList(accDevices);
            List<String> personIdList = getPersonIdsByLevelId(levelId);
            // 权限组中存在人员才进行人员下发
            if (personIdList != null && !personIdList.isEmpty()) {
                List<String> tempPersonIdList = new ArrayList<>();
                for (String devId : deviceIdList) {
                    for (String string : personIdList) {
                        tempPersonIdList.add(string);
                        if (tempPersonIdList.size() % AccConstants.LEVEL_SPLIT_COUNT == 0) {
                            syncTimeSegLevelToDev(devId, tempPersonIdList);
                            tempPersonIdList = new ArrayList<>();
                        }
                    }
                    if (!tempPersonIdList.isEmpty()) {
                        syncTimeSegLevelToDev(devId, tempPersonIdList);
                        tempPersonIdList = new ArrayList<>();
                    }
                }
            }
        }

    }

    @Override
    public ZKResultMsg importLevelDoorData(List<AccLevelDoorExportItem> itemList, boolean updateExistData) {
        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("pers_import_overData", itemList.size());
        }
        List<List<AccLevelDoorExportItem>> accLevelDoorImportItems =
            CollectionUtil.split(itemList, CollectionUtil.splitSize);
        // 导入的数据中，所有权限组名称已经存在的权限组，key为权限组名称
        Map<String, AccLevel> existAccLevelMap = new HashMap<>(itemList.size());
        // 导入的数据中，所有名称已经存在的门，key为门名称
        Map<String, AccDoor> existDoorMap = new HashMap<>(itemList.size());
        // 找出已经存在的权限组门数据
        List<AccLevelDoor> accLevelDoorList = new ArrayList<AccLevelDoor>();
        int importSize = itemList.size();
        int beginProgress = 20;
        // 已存在数量
        int existCount = 0;
        // 导入数据是第几行行数
        int index = 2;
        // 新增的数量
        int saveSize = 0;
        // 导入权限组门数据 权限组名称_门名称的集合，用于过滤重复的权限组门数据
        Set<String> uniqueLevelDoorSet = new HashSet<>();
        for (List<AccLevelDoorExportItem> importItemList : accLevelDoorImportItems) {
            // 取出待导入权限组门数据的权限组名称
            Collection<String> importLevelNames =
                CollectionUtil.getPropertyList(importItemList, AccLevelDoorExportItem::getLevelName, "-1");
            // 根据权限组名称找出存在的权限组信息
            List<AccLevel> accLevels = accLevelDao.findByNameIn(importLevelNames);
            if (accLevels != null && accLevels.size() > 0) {
                existAccLevelMap.putAll(CollectionUtil.listToKeyMap(accLevels, AccLevel::getName));
            }
            // 找出已经存在关系的权限组门信息
            List<String> levelIds = (List<String>)CollectionUtil.getModelIdsList(accLevels);
            accLevelDoorList.addAll(accLevelDoorDao.findByAccLevel_IdIn(levelIds));

            // 取出待导入权限组门数据的门名称
            Collection<String> importDoorNames =
                CollectionUtil.getPropertyList(importItemList, AccLevelDoorExportItem::getDoorName, "-1");
            // 根据门名称找出存在的门信息
            List<AccDoor> accDoors = accDoorDao.findByNameIn(importDoorNames);
            if (accDoors != null && accDoors.size() > 0) {
                existDoorMap.putAll(CollectionUtil.listToKeyMap(accDoors, AccDoor::getName));
            }
        }
        // 已经存在数据库的门禁权限组门信息
        Map<String, AccLevelDoor> existAccLevelDoorMap = new HashMap<String, AccLevelDoor>();
        String key = null;
        for (AccLevelDoor accLevelDoor : accLevelDoorList) {
            // key = 权限组名称 + "_" +门名称
            key = accLevelDoor.getAccLevel().getName() + "_" + accLevelDoor.getAccDoor().getName();
            existAccLevelDoorMap.put(key, accLevelDoor);
        }

        AccLevelDoor accLevelDoor = null;
        Iterator<AccLevelDoorExportItem> itemIterator = itemList.iterator();
        List<AccLevelDoor> newAccLevelDoor = new ArrayList<>();
        // Map<权限组id，List<读头id>>,用于下发权限
        Map<String, List<AccDevice>> levelDeviceMap = new HashMap<>();
        while (itemIterator.hasNext()) {
            index++;
            AccLevelDoorExportItem item = itemIterator.next();
            // 权限组名称非空校验
            if (StringUtils.isBlank(item.getLevelName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_nameNotNull"))));
                itemIterator.remove();
                logger.info("Row " + index + ": AccLevel name is empty");
                continue;
            }

            // 区域名称非空校验
            if (StringUtils.isBlank(item.getDoorName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_api_doorNameNotNull"))));
                itemIterator.remove();
                logger.info("Row " + index + ": AccDoor name is empty");
                continue;
            }

            // 权限组不存在
            if (!existAccLevelMap.containsKey(item.getLevelName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_api_levelNotExist"))));
                itemIterator.remove();
                logger.info("Row " + index + ": AccLevel is not exist");
                continue;
            }
            // 门数据不存在
            if (!existDoorMap.containsKey(item.getDoorName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_api_doorNotExist"))));
                itemIterator.remove();
                logger.info("Row " + index + ": AccDoor is not exist");
                continue;
            }
            // 唯一校验
            if (!uniqueLevelDoorSet.add(item.getLevelName() + "_" + item.getDoorName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_levelDoorExist",
                        item.getLevelName() + "_" + item.getDoorName()))));
                itemIterator.remove();
                logger.info(
                    "Row " + index + ": LevelDoor  " + item.getLevelName() + "_" + item.getDoorName() + " is repeated");
                continue;
            }
            // 判断权限组门数据是否已经存在，不存在则新增
            // 已存在的权限组更新所有字段
            accLevelDoor = existAccLevelDoorMap.get(item.getLevelName() + "_" + item.getDoorName());
            AccLevel accLevel = existAccLevelMap.get(item.getLevelName());
            AccDoor accDoor = existDoorMap.get(item.getDoorName());
            if (accLevelDoor == null) {
                List<AccDevice> accDeviceList = new ArrayList<>();
                accLevelDoor = new AccLevelDoor();
                accLevelDoor.setAccLevel(accLevel);
                accLevelDoor.setAccDoor(accDoor);
                newAccLevelDoor.add(accLevelDoor);
                if (levelDeviceMap.containsKey(accLevel.getId())) {
                    accDeviceList = levelDeviceMap.get(accLevel.getId());
                }
                accDeviceList.add(accDoor.getDevice());
                if (accDoor.getDevice().getParentDevice() != null) {
                    accDeviceList.add(accDoor.getDevice().getParentDevice());
                }
                levelDeviceMap.put(accLevel.getId(), accDeviceList);

            } else {
                existCount++;
            }
        }

        if (!CollectionUtil.isEmpty(newAccLevelDoor)) {
            // 把导入的数据分割，分批处理，一次处理800
            List<List<AccLevelDoor>> levelDoors = CollectionUtil.split(newAccLevelDoor, CollectionUtil.splitSize);
            float avgProgress = (float)60 / accLevelDoorImportItems.size();
            int i = 0;
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                I18nUtil.i18nCode("common_op_processing")));

            // 保存权限组信息
            for (List<AccLevelDoor> levelDoorList : levelDoors) {
                int progress = beginProgress + 10 + (int)(avgProgress * i++);
                progressCache.setProcess(new ProcessBean(progress, progress, ""));
                saveSize += levelDoorList.size();
                accLevelDoorDao.save(levelDoorList);
            }
            // 重新下发权限
            setImportLevelDoorToDev(levelDeviceMap);

        }
        // 失败数量
        int faildCount = importSize - saveSize - existCount;
        if (existCount > 0) {
            // 成功：%s 条，更新：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result3", saveSize, existCount, faildCount)));
        } else if (faildCount > 0 || saveSize > 0) {
            // 成功：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result", saveSize, faildCount)));
        }
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 导入权限组门信息，下发权限
     *
     * @param levelDeviceMap:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022-11-23 9:55
     * @since 1.0.0
     */
    private void setImportLevelDoorToDev(Map<String, List<AccDevice>> levelDeviceMap) {
        // 下发权限信息
        Set<String> levelIds = levelDeviceMap.keySet();
        List<List<String>> levelIdsList = CollectionUtil.split(levelIds, AccConstants.LEVEL_SPLIT_COUNT);
        List<AccLevelPerson> accLevelPersonList = new ArrayList<>();
        levelIdsList.parallelStream().forEach(levelIdList -> {
            List<AccLevelPerson> tempAccLevelPerson = accLevelPersonDao.findByAccLevel_IdIn(levelIdList);
            if (tempAccLevelPerson != null && tempAccLevelPerson.size() > 0) {
                accLevelPersonList.addAll(tempAccLevelPerson);
            }
        });

        Map<String, List<AccLevelPerson>> accLevelPersonMap = accLevelPersonList.stream().collect(
            Collectors.groupingBy(accLevelPerson -> accLevelPerson.getAccLevel().getId(), Collectors.toList()));

        for (String levelId : levelDeviceMap.keySet()) {
            List<AccDevice> accDevices = levelDeviceMap.get(levelId);
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
            AccDeviceUtil.moveUpParentDevList(accDevices);
            List<String> deviceIdList = (List<String>)CollectionUtil.getModelIdsList(accDevices);
            List<AccLevelPerson> levelPersonList = new ArrayList<>();
            List<String> personIdList = new ArrayList<>();
            if (accLevelPersonMap.containsKey(levelId)) {
                levelPersonList.addAll(accLevelPersonMap.get(levelId));
                String personIds = CollectionUtil.getPropertys(accLevelPersonList, AccLevelPerson::getPersPersonId);
                personIdList = StrUtil.strToList(personIds);
            }
            if (personIdList != null && personIdList.size() > 0) {// 权限组中存在人员才进行人员下发
                List<String> personArrayIds = accPersonService.splitPersonIds(StringUtils.join(personIdList, ","),
                    AccConstants.LEVEL_SPLIT_COUNT);
                personArrayIds.parallelStream().forEach(personArrayId -> {
                    // 下发人员权限到设备
                    handleLevelAddDoor(personArrayId, StringUtils.join(deviceIdList, ","), levelId);
                });
            }
            // 下发访客信息到设备(由于访客人数不会很多所以单独处理)
            setVisitorToDev(levelId, deviceIdList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ZKResultMsg importAccPersonData(List<AccPersonLevelByLevelExportItem> itemList) {
        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("pers_import_overData", itemList.size());
        }
        List<List<AccPersonLevelByLevelExportItem>> accLevelPersonImportItems =
            CollectionUtil.split(itemList, CollectionUtil.splitSize);
        // 导入的数据中，所有权限组名称已经存在的权限组，key为权限组名称
        Map<String, AccLevel> existAccLevelMap = new HashMap<>(itemList.size());
        // 导入的数据中，所有pin已经存在的人员信息，key为pin
        Map<String, PersPersonItem> existPersonMap = new HashMap<>(itemList.size());
        // 找出已经存在的权限组人员数据
        List<AccLevelPerson> accLevelPersonListAll = new ArrayList<AccLevelPerson>();
        List<AccLevelPerson> accLevelPersonList = new ArrayList<AccLevelPerson>();
        // 已经添加到权限组的人员信息:key为人员id
        Map<String, PersPersonItem> exitLevelPersonItemMaps = new HashMap<>();
        int importSize = itemList.size();
        int beginProgress = 20;
        // 已存在数量
        int existCount = 0;
        // 导入数据是第几行行数
        int index = 2;
        // 新增的数量
        int saveSize = 0;
        // 导入权限组门数据 权限组名称_门名称的集合，用于过滤重复的权限组门数据
        Set<String> uniqueLevelPersonSet = new HashSet<>();
        for (List<AccPersonLevelByLevelExportItem> importItemList : accLevelPersonImportItems) {
            // 取出待导入权限组人员数据的权限组名称
            Collection<String> importLevelNames =
                CollectionUtil.getPropertyList(importItemList, AccPersonLevelByLevelExportItem::getLevelName, "-1");
            // 根据权限组名称找出存在的权限组信息
            List<AccLevel> accLevels = accLevelDao.findByNameIn(importLevelNames);
            if (accLevels != null && accLevels.size() > 0) {
                existAccLevelMap.putAll(CollectionUtil.listToKeyMap(accLevels, AccLevel::getName));
            }
            // 找出已经存在关系的权限组-人员信息
            List<String> levelIds = (List<String>)CollectionUtil.getModelIdsList(accLevels);
            accLevelPersonList = accLevelPersonDao.findByAccLevel_IdIn(levelIds);
            accLevelPersonListAll.addAll(accLevelPersonList);
            List<String> personIds =
                (List<String>)CollectionUtil.getPropertyList(accLevelPersonList, AccLevelPerson::getPersPersonId, "-1");
            // 根据人员ids找出人员信息；
            List<PersPersonItem> exitPersonItems = persPersonService.getItemsByIds(personIds);
            exitLevelPersonItemMaps.putAll(CollectionUtil.itemListToIdMap(exitPersonItems));
            // 取出待导入权限组人员数据的人员pin
            Collection<String> importPersonPins =
                CollectionUtil.getPropertyList(importItemList, AccPersonLevelByLevelExportItem::getPersPin, "-1");
            // 根据人员pin找出存在的人员信息
            List<PersPersonItem> persPersonItems = persPersonService.getItemsByPins(importPersonPins);
            if (persPersonItems != null && persPersonItems.size() > 0) {
                existPersonMap.putAll(CollectionUtil.listToKeyMap(persPersonItems, PersPersonItem::getPin));
            }
        }
        // 已经存在数据库的门禁权限组人员信息
        Map<String, AccLevelPerson> existAccLevelPersonMap = new HashMap<String, AccLevelPerson>();
        String key = null;
        PersPersonItem persPersonItem = null;
        for (AccLevelPerson accLevelPerson : accLevelPersonListAll) {
            // key = 权限组名称 + "_" +人员编号
            persPersonItem = exitLevelPersonItemMaps.get(accLevelPerson.getPersPersonId());
            key = accLevelPerson.getAccLevel().getName() + "_" + persPersonItem.getPin();
            existAccLevelPersonMap.put(key, accLevelPerson);
        }

        AccLevelPerson accLevelPerson = null;
        Iterator<AccPersonLevelByLevelExportItem> itemIterator = itemList.iterator();
        List<AccLevelPerson> newAccLevelPerson = new ArrayList<>();
        List<AccLevelPerson> resetAccLevelPersonList = new ArrayList<>();
        while (itemIterator.hasNext()) {
            index++;
            AccPersonLevelByLevelExportItem item = itemIterator.next();
            // 权限组名称非空校验
            if (StringUtils.isBlank(item.getLevelName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_nameNotNull"))));
                itemIterator.remove();
                logger.info("Row " + index + ": AccLevel name is empty");
                continue;
            }

            // 人员pin非空校验
            if (StringUtils.isBlank(item.getPersPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("pers_import_pinNotEmpty"))));
                itemIterator.remove();
                logger.info("Row " + index + ": Person pin is empty");
                continue;
            }
            // 支持权限组时间段配置导入才需要做时间校验
            if (isSupportLevelTime) {
                // 开始时间、结束时间格式校验
                if (PersConstants.PERSON_DATE_ERROR.equals(item.getStartTimeStr())
                    || PersConstants.PERSON_DATE_ERROR.equals(item.getEndTimeStr())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("common_levelImport_dateFormatError"))));
                    itemIterator.remove();
                    logger.info("Row " + index + ": Date format error");
                    continue;
                }

                // 开始时间、结束时间为空校验（可以同时为空或者同时不为空）
                if ((Objects.isNull(item.getStartTime()) && Objects.nonNull(item.getEndTime()))
                    || (Objects.nonNull(item.getStartTime()) && Objects.isNull(item.getEndTime()))) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("common_levelImport_timeNotNull"))));
                    itemIterator.remove();
                    continue;
                }

                // 开始时间、结束时间大小校验
                if (Objects.nonNull(item.getStartTime()) && Objects.nonNull(item.getEndTime())) {
                    Date startTime = item.getStartTime();
                    Date endTime = item.getEndTime();
                    // 结束时间是否小于开始时间
                    if (endTime.getTime() <= startTime.getTime()) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", index,
                                I18nUtil.i18nCode("common_levelImport_endTimeLessStartTime"))));
                        itemIterator.remove();
                        continue;
                    }
                }
            }

            // 权限组不存在
            if (!existAccLevelMap.containsKey(item.getLevelName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_api_levelNotExist"))));
                itemIterator.remove();
                logger.info("Row " + index + ": AccLevel is not exist");
                continue;
            }
            // 通过人员编号找不到对应的人员
            if (!existPersonMap.containsKey(item.getPersPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", index, I18nUtil.i18nCode("pers_person_pinError", item.getPersPin()))));
                itemIterator.remove();
                logger.info("Row " + index + ": Person is not exist");
                continue;
            }
            // 唯一校验
            if (!uniqueLevelPersonSet.add(item.getLevelName() + "_" + item.getPersPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("acc_levelImport_levelPersonExist",
                        item.getLevelName() + "_" + item.getPersPin()))));
                itemIterator.remove();
                logger.info("Row " + index + ": LevelPerson  " + item.getLevelName() + "_" + item.getPersPin()
                    + " is repeated");
                continue;
            }
            // 判断权限组门数据是否已经存在，不存在则新增
            // 已存在的权限组更新所有字段
            accLevelPerson = existAccLevelPersonMap.get(item.getLevelName() + "_" + item.getPersPin());
            AccLevel accLevel = existAccLevelMap.get(item.getLevelName());
            String persId = existPersonMap.get(item.getPersPin()).getId();
            if (accLevelPerson == null) {
                accLevelPerson = new AccLevelPerson();
                accLevelPerson.setAccLevel(accLevel);
                accLevelPerson.setPersPersonId(persId);
            } else {
                existCount++;
            }
            if (isSupportLevelTime) {
                if (StringUtils.isNotBlank(accLevelPerson.getId())) {
                    Date oldStartTime = accLevelPerson.getStartTime();
                    Date oldEndTime = accLevelPerson.getEndTime();
                    String oldSt = "";
                    String oldEt = "";
                    if (oldStartTime != null && oldEndTime != null) {
                        oldSt = DateUtil.dateToString(oldStartTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                        oldEt = DateUtil.dateToString(oldEndTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                    }
                    String newSt = "";
                    String newEt = "";
                    if (item.getStartTime() != null && item.getEndTime() != null) {
                        newSt = DateUtil.dateToString(item.getStartTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                        newEt = DateUtil.dateToString(item.getEndTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                    }
                    // 只要时间一致就不再更新
                    if (!(newSt.equals(oldSt) && newEt.equals(oldEt))) {
                        resetAccLevelPersonList.add(accLevelPerson);
                    }
                }
                accLevelPerson.setStartTime(item.getStartTime());
                accLevelPerson.setEndTime(item.getEndTime());
            }
            newAccLevelPerson.add(accLevelPerson);
        }

        if (!CollectionUtil.isEmpty(newAccLevelPerson)) {
            // 把导入的数据分割，分批处理，一次处理800人
            Map<String, List<AccLevelPerson>> levelPersonMap = newAccLevelPerson.stream()
                .collect(Collectors.groupingBy(levelPerson -> levelPerson.getAccLevel().getId(), Collectors.toList()));
            float avgProgress = (float)60 / accLevelPersonImportItems.size();
            int i = 0;
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                I18nUtil.i18nCode("common_op_processing")));
            // 处理有更新的权限信息
            Map<String, List<AccLevelPerson>> updateAccLevelPersonMap = new HashMap<>();
            if (resetAccLevelPersonList != null && resetAccLevelPersonList.size() > 0) {
                updateAccLevelPersonMap = resetAccLevelPersonList.stream().collect(
                    Collectors.groupingBy(levelPerson -> levelPerson.getAccLevel().getId(), Collectors.toList()));
            }
            // 保存权限组人员信息并下发权限
            for (String levelId : levelPersonMap.keySet()) {
                List<AccLevelPerson> levelPersonList = levelPersonMap.get(levelId);
                int progress = beginProgress + 10 + (int)(avgProgress * i++);
                progressCache.setProcess(new ProcessBean(progress, progress, ""));
                saveSize += levelPersonList.size();
                accLevelPersonDao.save(levelPersonList);
                boolean isSetToDev = getDoorCountByLevelId(levelId) > 0;
                // 判断权限组中是否已经有添加门
                if (isSetToDev) {
                    List<AccLevelPerson> updateLevelPersons = updateAccLevelPersonMap.get(levelId);
                    List<AccLevelPerson> tempList = new ArrayList<>(levelPersonList);
                    // 需要删除再进行下发的数据
                    String updatePersonIds = "";
                    if (updateLevelPersons != null && updateLevelPersons.size() > 0) {
                        tempList.retainAll(updateLevelPersons);
                        // 去掉更新删除的数据，剩下的都是新增数据
                        levelPersonList.removeAll(tempList);
                        updatePersonIds =
                            CollectionUtil.getPropertys(updateLevelPersons, AccLevelPerson::getPersPersonId);
                        List<String> personArrayIds =
                            accPersonService.splitPersonIds(updatePersonIds, AccConstants.LEVEL_SPLIT_COUNT);
                        personArrayIds.parallelStream().forEach(personArrayId -> {
                            immeDelPersonLevelToDev(StrUtil.strToList(levelId), personArrayId);
                        });
                    }
                    if (levelPersonList != null && levelPersonList.size() > 0) {
                        String personIds =
                            CollectionUtil.getPropertys(levelPersonList, AccLevelPerson::getPersPersonId);
                        List<String> addPersonIds = StrUtil.strToList(personIds);
                        if (StringUtils.isNotBlank(updatePersonIds) && StringUtils.isNotBlank(personIds)) {
                            List<String> updatePersonIdList = StrUtil.strToList(updatePersonIds);
                            List<String> tempPersIdsList = new ArrayList<>(addPersonIds);
                            tempPersIdsList.retainAll(updatePersonIdList);
                            // 去掉已经下发的数据更，剩下的都是新增数据
                            addPersonIds.removeAll(tempPersIdsList);
                        }
                        if (addPersonIds != null && addPersonIds.size() > 0) {
                            List<String> personArrayIds = accPersonService
                                .splitPersonIds(StringUtils.join(addPersonIds, ","), AccConstants.LEVEL_SPLIT_COUNT);
                            personArrayIds.parallelStream().forEach(personArrayId -> {
                                setPersonTimesLevelToDev(levelId, personArrayId);
                            });
                        }
                    }
                }
            }
        }
        // 失败数量
        int faildCount = importSize - saveSize;
        if (existCount > 0) {
            // 成功：%s 条，更新：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result3", saveSize, existCount, faildCount)));
        } else if (faildCount > 0 || saveSize > 0) {
            // 成功：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result", saveSize, faildCount)));
        }
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public AccPersonInfoItem getPersonBasicInfoByIds(List<String> personIds) {
        AccPersonInfoItem personInfoBean = new AccPersonInfoItem();
        // 人员信息的集合
        List<AccPersonOptItem> personInfoList = new ArrayList<>();
        // 对人员进行分组
        List<List<String>> personIdsList = CollectionUtil.split(personIds, AccConstants.LEVEL_SPLIT_COUNT);
        personIdsList.stream().forEach(personIdList -> {
            List<PersPersonItem> persPersonItemList = persPersonService.getPersonItemForDevByIds(personIdList);
            if (persPersonItemList != null && persPersonItemList.size() > 0) {
                // 将人员信息缓存到map中
                Map<String, PersPersonItem> persPersonItemMap = CollectionUtil.itemListToIdMap(persPersonItemList);
                List<AccPerson> accPersonAllList = accPersonDao.findByPersonIdIn(personIdList);
                Map<String, AccPerson> accPersonMap =
                    CollectionUtil.listToKeyMap(accPersonAllList, AccPerson::getPersonId);
                List<AccPersonCombOpenPerson> accPersonCombOpenPersonAllList =
                    accPersonCombOpenPersonDao.findByPersPersonIdIn(personIdList); // 查询是否有设置多人开门组
                Map<String, AccPersonCombOpenPerson> accPersonCombOpenPersonMap = CollectionUtil
                    .listToKeyMap(accPersonCombOpenPersonAllList, AccPersonCombOpenPerson::getPersPersonId);

                // 将人员卡号进行分组并缓存到map中
                List<PersCardItem> persCardItemAllList = persCardService.getAllCardByPersonIdList(personIdList);
                Map<String, List<PersCardItem>> persCardItemMap =
                    persCardItemAllList.stream().collect(Collectors.groupingBy(PersCardItem::getPersonId));
                persPersonItemMap.forEach((personId, persPersonItem) -> {
                    // 组装人员权限信息
                    List<PersCardItem> persCardItemList = persCardItemMap.get(personId);
                    if (persCardItemList != null && persCardItemList.size() > 0) {
                        persCardItemList.stream().forEach(persCardItem -> {
                            AccPersonOptItem accPersonOptItem = new AccPersonOptItem();
                            accPersonOptItem.setCardNo(persCardItem.getCardNo());
                            accPersonOptItem.setCardType(persCardItem.getCardType());
                            accPersonOptItem.setCardState(persCardItem.getCardState());
                            setAccPersonOptItem(accPersonOptItem, persPersonItem, accPersonMap.get(personId),
                                accPersonCombOpenPersonMap.get(personId));
                            personInfoList.add(accPersonOptItem);
                        });
                    } else {
                        AccPersonOptItem accPersonOptItem = new AccPersonOptItem();
                        accPersonOptItem.setCardNo("");
                        accPersonOptItem.setCardType(null);
                        accPersonOptItem.setCardState(null);
                        setAccPersonOptItem(accPersonOptItem, persPersonItem, accPersonMap.get(personId),
                            accPersonCombOpenPersonMap.get(personId));
                        personInfoList.add(accPersonOptItem);
                    }
                });
            }
        });
        if (accProExtService != null) {
            accProExtService.setThreatLevel(personInfoList);
        }
        personInfoBean.setPersonList(personInfoList);
        return personInfoBean;
    }

    @Override
    public boolean verifyLoginPwd(String sessionId, String loginPwd) {
        boolean ret = false;
        if (StringUtils.isNotBlank(sessionId) && StringUtils.isNotBlank(loginPwd)) {
            ret = authUserService.verifySecurity(sessionId, loginPwd, "pwd");
        }
        return ret;
    }

    @Override
    public void addPersonAndTimesByParamIds(String personIds, String levelId) {
        if (StringUtils.isBlank(levelId) || StringUtils.isBlank(personIds)) {
            return;
        }
        List<AccPersonTimeLevelItem> accPersonLevelTimesList;
        if (personIds.indexOf(";") > 0) {
            // 人员临时权限
            accPersonLevelTimesList = AccLevelUtil.timePersonIdsToLevelTimeItems(personIds, levelId);
        } else {
            // 原人员id
            accPersonLevelTimesList = AccLevelUtil.personIdsToLevelTimeItems(personIds, levelId);
            // 按部门添加时前端不传临时权限时间，要保留人员原有临时权限，需进行数据组装再处理
            if (isSupportLevelTime) {
                buildAccPersonLevelTimeList(accPersonLevelTimesList, levelId, personIds);
            }
        }
        // 添加人员到数据库
        addLevelByParamIds(accPersonLevelTimesList, "person");
        // 判断权限组中是否已经有添加门
        boolean isSetToDev = getDoorCountByLevelId(levelId) > 0;
        if (isSetToDev) {
            // 获取人员ids
            List<String> personIdList = (List<String>)CollectionUtil.getPropertyList(accPersonLevelTimesList,
                AccPersonTimeLevelItem::getPersonId, "-1");
            // 下发人员权限到设备
            setPersonLevelToDev(levelId, personIdList, true);
        }
    }

    /**
     * 组装人员临时权限信息
     *
     * @param accPersonLevelTimesList:
     * @param levelIds:
     * @param personIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-05-16 15:31
     * @since 1.0.0
     */
    private void buildAccPersonLevelTimeList(List<AccPersonTimeLevelItem> accPersonLevelTimesList, String levelIds,
        String personIds) {
        List<AccLevelPerson> accLevelPersonList = accLevelPersonDao
            .findByAccLevel_IdInAndPersPersonIdIn(StrUtil.strToList(levelIds), StrUtil.strToList(personIds));
        Map<String, AccLevelPerson> accLevelPersonMap =
            CollectionUtil.listToKeyMap(accLevelPersonList, i -> i.getAccLevel().getId() + "_" + i.getPersPersonId());
        accPersonLevelTimesList.forEach(item -> {
            AccLevelPerson levelPerson = accLevelPersonMap.get(item.getLevelId() + "_" + item.getPersonId());
            if (levelPerson != null && levelPerson.getStartTime() != null) {
                item.setStartTime(
                    DateUtil.dateToString(levelPerson.getStartTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                item.setEndTime(
                    DateUtil.dateToString(levelPerson.getEndTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
            }
        });
    }

    @Override
    public void addLevelByParamIds(List<AccPersonTimeLevelItem> newLevelTimes, String type) {
        List<String> personIds =
            newLevelTimes.stream().map(AccPersonTimeLevelItem::getPersonId).distinct().collect(Collectors.toList());
        // 去重，获取当前已经存在的权限信息
        List<AccLevelPerson> currentLevelPersonList = accLevelPersonDao.findByPersPersonIdIn(personIds);
        Map<String, AccLevel> allLevelMap = new HashMap<>();
        List<AccLevel> allLevelList = accLevelDao.findAll();
        for (AccLevel level : allLevelList) {
            if (!allLevelMap.containsKey(level.getId())) {
                allLevelMap.put(level.getId(), level);
            }
        }
        addLevelPerson(newLevelTimes, currentLevelPersonList, allLevelMap);
    }

    /**
     * 去重，通过新增的和数据库已存在的进行去重判断
     *
     * @param newPersonLevelItems:
     * @param currentPersonLevelItems:
     * @param allLevelMap:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2024-01-05 15:46
     * @since 1.0.0
     */
    private void addLevelPerson(List<AccPersonTimeLevelItem> newPersonLevelItems,
        List<AccLevelPerson> currentPersonLevelItems, Map<String, AccLevel> allLevelMap) {
        Map<String, AccPersonTimeLevelItem> newLevelParamMap = new HashMap<>();
        for (AccPersonTimeLevelItem item : newPersonLevelItems) {
            String key = item.getPersonId() + "_" + item.getLevelId();
            newLevelParamMap.put(key, item);
        }

        Map<String, AccLevelPerson> currentLevelParamMap = new HashMap<>();
        for (AccLevelPerson item : currentPersonLevelItems) {
            String key = item.getPersPersonId() + "_" + item.getAccLevel().getId();
            currentLevelParamMap.put(key, item);
        }

        List<AccLevelPerson> addAccLevelPersonList = new ArrayList<>();
        List<AccLevelPerson> updateAccLevelPersonList = new ArrayList<>();
        for (String key : newLevelParamMap.keySet()) {
            AccPersonTimeLevelItem item = newLevelParamMap.get(key);
            if (currentLevelParamMap.containsKey(key)) {
                AccLevelPerson levelItem = currentLevelParamMap.get(key);
                Date oldStartTime = levelItem.getStartTime();
                Date oldEndTime = levelItem.getEndTime();
                String oldSt = "";
                String oldEt = "";
                if (oldStartTime != null && oldEndTime != null) {
                    oldSt = DateUtil.dateToString(oldStartTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                    oldEt = DateUtil.dateToString(oldEndTime, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                }

                String newSt = item.getStartTime() != null ? item.getStartTime() : "";
                String newEt = item.getEndTime() != null ? item.getEndTime() : "";
                // 只要时间一致就不再更新
                if (newSt.equals(oldSt) && newEt.equals(oldEt)) {
                    continue;
                }
                if (StringUtils.isNoneBlank(newSt)) {
                    levelItem.setStartTime(DateUtil.stringToDate(newSt, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                    levelItem.setEndTime(DateUtil.stringToDate(newEt, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                    updateAccLevelPersonList.add(levelItem);
                } else {
                    levelItem.setStartTime(null);
                    levelItem.setEndTime(null);
                    updateAccLevelPersonList.add(levelItem);
                }
            } else {
                String newSt = item.getStartTime();
                String newEt = item.getEndTime();
                AccLevel accLevel = allLevelMap.get(item.getLevelId());
                if (accLevel != null) {
                    if (StringUtils.isNoneBlank(newSt)) {
                        addAccLevelPersonList.add(new AccLevelPerson(accLevel, item.getPersonId(),
                            DateUtil.stringToDate(newSt, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS),
                            DateUtil.stringToDate(newEt, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS)));
                    } else {
                        addAccLevelPersonList.add(new AccLevelPerson(accLevel, item.getPersonId()));
                    }
                }
            }
        }
        if (!addAccLevelPersonList.isEmpty()) {
            accLevelPersonDao.saveAll(addAccLevelPersonList);
        }
        if (!updateAccLevelPersonList.isEmpty()) {
            accLevelPersonDao.saveAll(updateAccLevelPersonList);
        }
    }

    @Override
    public void setPersonTimesLevelToDev(String levelId, String personIds) {
        if (StringUtils.isNotBlank(personIds)) {
            setPersonLevelToDev(levelId, StrUtil.strToList(personIds), false);
        }
    }

    /**
     * 人员下发给设备（包含临时权限处理）
     *
     * @param levelId:
     * @param personIds:
     * @param needProExt:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-05-22 14:30
     * @since 1.0.0
     */
    private void setPersonLevelToDev(String levelId, List<String> personIds, boolean needProExt) {
        // 一次性查询出所有权限信息，根据设备id进行存放
        if (StringUtils.isNotBlank(levelId) && personIds != null && !personIds.isEmpty()) {
            Map<String, Map<String, List<Short>>> devPinTimeSegDoorMap =
                getPinTimeSegDoorMapByCondition(personIds, levelId, needProExt);
            // 获取设备
            List<AccDevice> devList = accDeviceDao.getDevByLevel(levelId);
            if (devList != null && !devList.isEmpty()) {
                Long businessId = accLevelDao.findOne(levelId).getBusinessId();
                // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
                AccDeviceUtil.moveUpParentDevList(devList);
                // 组装下发对象
                AccPersonInfoItem personInfoBean = getPersonByIds(personIds);
                for (AccDevice dev : devList) {
                    List<AccPersonLevelOptItem> personLevelList =
                        getPersonLevelList(devPinTimeSegDoorMap, dev, businessId);
                    if (!personInfoBean.getPersonList().isEmpty()) {
                        // 下发人员信息
                        accDevCmdManager.setPersonToDev(dev.getSn(), personInfoBean.getPersonList(), false);
                        if (personInfoBean.getAccBioTemplateItemList() != null
                            && !personInfoBean.getAccBioTemplateItemList().isEmpty()) {
                            // 下发指纹信息
                            accDevCmdManager.setPersonBioTemplateToDev(dev.getSn(),
                                personInfoBean.getAccBioTemplateItemList(), false);
                        }
                        // 下发权限
                        setLevelToDev(dev, personLevelList, personInfoBean.getPersonList());
                        accDevCmdManager.downloadOver(dev.getSn(), false);
                    }
                }
            }
        }
    }

    @Override
    public void addLevelAndTimesByParamIds(String personId, String levelIds) {
        if (StringUtils.isBlank(personId) || StringUtils.isBlank(levelIds)) {
            return;
        }
        // 添加人员权限到数据库
        // 根据传过来的权限组id再进行一次查询，以数据库为准，避免并发操作导致数据不一致
        List<AccPersonTimeLevelItem> accPersonLevelTimesList;
        if (levelIds.indexOf(";") > 0) {
            // 人员临时权限
            accPersonLevelTimesList = AccLevelUtil.timeLevelIdsToLevelTimeItems(levelIds, personId);
        } else {
            // 原权限组id
            accPersonLevelTimesList = AccLevelUtil.levelIdsToLevelTimeItems(levelIds, personId);
        }
        // 添加人员到数据库
        addLevelByParamIds(accPersonLevelTimesList, "person");
    }

    @Override
    public void setPersonLevelTimesToDev(String levelIdTimes, String personId) {
        List<String> personIds = StrUtil.strToList(personId);
        AccPersonInfoItem accPersonInfoItem = getPersonByIds(personIds);
        Map<String, Map<String, List<Short>>> devPinTimeSegDoorMap =
            getPinTimeSegDoorMapByCondition(personIds, null, false);
        List<String> levelIdList = (List<String>)CollectionUtil.strToList(levelIdTimes);
        List<AccDevice> devList = accDeviceDao.getDevByLevel(levelIdList);// 获取设备
        if (devList != null && devList.size() > 0) {
            Long businessId = accLevelDao.getOne(levelIdList.get(0)).getBusinessId();
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
            AccDeviceUtil.moveUpParentDevList(devList);
            for (AccDevice dev : devList) {
                List<AccPersonLevelOptItem> personLevelList = getPersonLevelList(devPinTimeSegDoorMap, dev, businessId);
                if (accPersonInfoItem.getPersonList().size() > 0) {
                    // 下发人员信息
                    accDevCmdManager.setPersonToDev(dev.getSn(), accPersonInfoItem.getPersonList(), false);
                    if (accPersonInfoItem.getAccBioTemplateItemList() != null
                        && accPersonInfoItem.getAccBioTemplateItemList().size() > 0) {
                        // 下发指纹信息
                        accDevCmdManager.setPersonBioTemplateToDev(dev.getSn(),
                            accPersonInfoItem.getAccBioTemplateItemList(), false);
                    }
                    // 下发权限
                    setLevelToDev(dev, personLevelList, accPersonInfoItem.getPersonList());
                }
                // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                accDevCmdManager.downloadOver(dev.getSn(), false);
            }
        }
    }

    /**
     * 组装下发权限组人员信息
     *
     * @param devPinTimeSegDoorMap:
     * @param dev:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelOptItem>
     * <AUTHOR>
     * @throws
     * @date 2024-04-23 10:48
     * @since 1.0.0
     */
    private List<AccPersonLevelOptItem> getPersonLevelList(Map<String, Map<String, List<Short>>> devPinTimeSegDoorMap,
        AccDevice dev, Long businessId) {
        List<AccPersonLevelOptItem> personLevelList = new ArrayList<>();
        if (devPinTimeSegDoorMap != null && devPinTimeSegDoorMap.size() > 0
            && devPinTimeSegDoorMap.containsKey(dev.getId())) {
            Map<String, List<Short>> pinTimeSegDoorMap = devPinTimeSegDoorMap.get(dev.getId());
            // 拼装下发人员权限的数据。
            for (String key : pinTimeSegDoorMap.keySet()) {
                String[] pinTimeSegId = key.split("_");
                AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
                accPersonLevelOptItem.setPin(pinTimeSegId[0]);
                accPersonLevelOptItem.setTimeSegId(Long.parseLong(pinTimeSegId[1]));
                // 设置权限组id add by colin
                accPersonLevelOptItem.setLevelId(businessId);
                // 加入开始时间和结束时间
                if (pinTimeSegId.length > 3) {
                    accPersonLevelOptItem
                        .setStartTime(DateUtil.stringToDate(pinTimeSegId[2], DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                    accPersonLevelOptItem
                        .setEndTime(DateUtil.stringToDate(pinTimeSegId[3], DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                }
                if (accProExtService != null) {
                    if (pinTimeSegId.length > 4) {
                        accPersonLevelOptItem.setLevelId(Long.valueOf(pinTimeSegId[4]));
                    } else {
                        accPersonLevelOptItem.setLevelId(Long.valueOf(pinTimeSegId[2]));
                    }
                }
                accPersonLevelOptItem.setTimeSegId(Long.parseLong(pinTimeSegId[1]));
                accPersonLevelOptItem.setDeviceId(dev.getBusinessId());
                List<Short> doorNoList = pinTimeSegDoorMap.get(key);
                if (doorNoList != null && doorNoList.size() > 0) {
                    accPersonLevelOptItem.setDoorNoList(doorNoList);
                    personLevelList.add(accPersonLevelOptItem);
                }
            }
        }
        return personLevelList;
    }

    @Override
    public List<AccLevelItem> getPersonShowLevelItem(AccPersonShowLevelItem condition) {
        return (List<AccLevelItem>)accLevelDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public AccLevelItem getMasterLevelBySessionId(String sessionId) {
        // 获取当前登录用户信息
        List<AuthAreaItem> areaItemList = authAreaService.findBySubject(sessionId);
        if (areaItemList != null && areaItemList.size() > 0) {
            AccLevelItem levelItem = new AccLevelItem();
            levelItem.setInitFlag(true);
            levelItem = Optional.ofNullable(getByCondition(levelItem)).filter(list -> !list.isEmpty())
                .map(list -> list.get(0)).orElse(null);
            Map<String, AuthAreaItem> areaMap = CollectionUtil.itemListToIdMap(areaItemList);
            if (Objects.nonNull(levelItem) && areaMap.containsKey(levelItem.getAuthAreaId())) {
                return levelItem;
            }
        }
        return null;
    }

    @Override
    public void setVisitorLevelToDev(List<AccPersonInfoItem> accPersonInfoItemList, List<String> addAccLevelIds,
        List<String> delAccLevelIds) {
        accPersonInfoItemList.forEach(accPersonInfoItem -> {
            List<AccPersonOptItem> personInfoList = accPersonInfoItem.getPersonList();
            if (personInfoList != null && !personInfoList.isEmpty()) {
                // 删除人员权限
                if (delAccLevelIds != null && !delAccLevelIds.isEmpty()) {
                    List<AccDevice> delDevList = accDeviceDao.getDevByLevel(delAccLevelIds);// 需要删除权限的设备
                    Map<String, Collection<String>> delParamMap = AccLevelUtil.setLevelMap(personInfoList);
                    delDevList.forEach(dev -> {
                        // 下发删除权限命令
                        delVisitorLevelFromDev(dev, delParamMap);// 删除人员权限
                        setVisLevelToDev(dev, (List<String>)delParamMap.get("pin"));// (效率太低，后续优化)
                    });
                }
                // 添加人员权限
                if (addAccLevelIds != null && !addAccLevelIds.isEmpty()) {
                    List<AccDevice> addDevList = accDeviceDao.getDevByLevel(addAccLevelIds);// 需要添加权限的设备
                    // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
                    AccDeviceUtil.moveUpParentDevList(addDevList);
                    addDevList.forEach(accDevice -> {
                        List<AccPersonLevelOptItem> devLevelList = new ArrayList<AccPersonLevelOptItem>();
                        List<List<String>> addAccLevelIdList =
                            CollectionUtil.split(addAccLevelIds, AccConstants.LEVEL_SPLIT_COUNT); // 进行分组，每800个一组。
                        addAccLevelIdList.forEach(accLevelIdList -> {
                            List<AccLevelDoor> accLevelDoorList = accLevelDoorDao
                                .findByAccDoor_Device_IdAndAccLevel_IdIn(accDevice.getId(), accLevelIdList);
                            Map<String, List<AccLevelDoor>> accLevelDoorMap =
                                accLevelDoorList.stream().collect(Collectors.groupingBy(i -> i.getId()));
                            accLevelDoorList.forEach(accLevelDoor -> {
                                AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
                                accPersonLevelOptItem.setDeviceId(accDevice.getBusinessId());
                                String timeSegId = accLevelDoor.getAccLevel().getTimeSegId();
                                // 时间段数据存在扩展，需要另外查询
                                Long timeSegBId = accTimeSegService.getBusinessIdByTimeSegId(timeSegId);
                                accPersonLevelOptItem.setTimeSegId(timeSegBId);
                                // add by colin 增加权限组id
                                accPersonLevelOptItem.setLevelId(accLevelDoor.getAccLevel().getBusinessId());
                                List<AccLevelDoor> accLevelDoors = accLevelDoorMap.get(accLevelDoor.getId());
                                if (accLevelDoors != null && !accLevelDoors.isEmpty()) {
                                    List<Short> doorNoList = accLevelDoors.stream().map(i -> i.getAccDoor().getDoorNo())
                                        .collect(Collectors.toList());
                                    accPersonLevelOptItem.setDoorNoList(doorNoList);
                                    devLevelList.add(accPersonLevelOptItem);
                                }
                            });
                        });
                        boolean isSupportMulCard = accDeviceOptionService.isSupportFunList(accDevice.getSn(), 7);
                        Collection<String> pins =
                            CollectionUtil.getPropertyList(personInfoList, AccPersonOptItem::getPin, "-1");
                        if (isSupportMulCard) {
                            accDevCmdManager.delMulCardUserFromDev(accDevice.getSn(), (List<String>)pins, false);// 多卡数据类似于指纹，下发之前需要按照pin号全部删除然后再重新下发
                        }
                        //下发之前需要按照pin号全部删除然后再重新下发
                        accDevCmdManager.delPersonBioTemplateFromDev(accDevice.getSn(), (List<String>)pins, false);
                        accDevCmdManager.setPersonToDev(accDevice.getSn(), personInfoList, false);// 下发人员信息
                        if (accPersonInfoItem.getAccBioTemplateItemList() != null
                            && !accPersonInfoItem.getAccBioTemplateItemList().isEmpty()) {
                            accDevCmdManager.setPersonBioTemplateToDev(accDevice.getSn(),
                                accPersonInfoItem.getAccBioTemplateItemList(), false);// 下发指纹信息
                        }
                        setLevelToDev(accDevice, devLevelList, personInfoList);// 下发权限
                        // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                        accDevCmdManager.downloadOver(accDevice.getSn(), false);
                    });
                }
            }
        });
    }

    @Override
    public void syncVisitorLevelToDev(List<AccPersonInfoItem> accPersonInfoItemList, String deviceId, List<String> addAccLevelIds) {
        List<String> transactionIdList = new ArrayList<>();
        accPersonInfoItemList.stream().forEach(accPersonInfoItem -> {
            List<AccPersonOptItem> personInfoList = accPersonInfoItem.getPersonList();
            Collection<String> transactionIds =
                CollectionUtil.getPropertyList(personInfoList, AccPersonOptItem::getId, "-1");
            transactionIdList.addAll(transactionIds);
        });
        // 获取访客门禁权限组集合
        Map<String, List<String>> visiotrLevels = new HashMap<>();
        if (!transactionIdList.isEmpty() && Objects.nonNull(accGetVisTransactionService)) {
            visiotrLevels =
                accGetVisTransactionService.getVisiotrLevelsByLevelIdAndTranId(addAccLevelIds, transactionIdList);
        }
        for (AccPersonInfoItem accPersonInfoItem : accPersonInfoItemList) {

            List<AccPersonOptItem> personInfoList = accPersonInfoItem.getPersonList();
            if (personInfoList != null && personInfoList.size() > 0) { // ||
                if (StringUtils.isNotBlank(deviceId))// 添加人员权限
                {
                    AccDevice accDevice = accDeviceDao.findById(deviceId).orElse(new AccDevice());
                    accDevCmdManager.setPersonToDev(accDevice.getSn(), personInfoList, false);// 下发人员信息
                    if (accPersonInfoItem.getAccBioTemplateItemList() != null
                        && accPersonInfoItem.getAccBioTemplateItemList().size() > 0) {
                        accDevCmdManager.setPersonBioTemplateToDev(accDevice.getSn(),
                            accPersonInfoItem.getAccBioTemplateItemList(), false);// 下发指纹信息
                    }
                    List<AccPersonLevelOptItem> devLevelList = new ArrayList<>();
                    for (AccPersonOptItem personInfo : personInfoList) {
                        List<String> accLevelIds = visiotrLevels.get(personInfo.getId());
                        if (Objects.nonNull(accLevelIds) && !accLevelIds.isEmpty()) {
                            for (String accLevelId : accLevelIds) {
                                AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
                                accPersonLevelOptItem.setDeviceId(accDevice.getBusinessId());
                                String timeSegId = accLevelDao.getTimeSegIdByLevelId(accLevelId);
                                // 时间段数据存在扩展，需要另外查询
                                Long timeSegBId = accTimeSegService.getBusinessIdByTimeSegId(timeSegId);
                                accPersonLevelOptItem.setTimeSegId(timeSegBId);
                                List<Short> doorNoList =
                                    accLevelDoorDao.getDoorNoByLevelIdAndDevId(accLevelId, accDevice.getId());
                                if (doorNoList != null && doorNoList.size() > 0) {
                                    accPersonLevelOptItem.setDoorNoList(doorNoList);
                                    devLevelList.add(accPersonLevelOptItem);
                                }
                            }
                        }
                    }
                    setLevelToDev(accDevice, devLevelList, personInfoList);// 下发权限
                    // 拓展接口，为了兼容华为pad设备下发downLoad命令通知设备已下发完成的功能 by mingfa.zheng 20200727
                    accDevCmdManager.downloadOver(accDevice.getSn(), false);
                }
            }
        }
    }

    @Override
    public void syncVisitorToDevByLevelIdsAndDevId(List<String> levelList, String deviceId) {
        if (Objects.nonNull(accGetVisTransactionService)) {
            List<Acc4VisPersonInfo> accPersonInfoItemList =
                accGetVisTransactionService.getVisiotrInfosByLevelId(StringUtils.join(levelList, ",")); // 根据权限组获取访客信息
            syncVisitorLevelToDev(visToAccItem(accPersonInfoItemList), deviceId, levelList);
        }

    }

    @Override
    public void immeDelPersonLevel(Map<String, String> personOptMap) {
        String paramName = "id";
        // 按id删除人员
        if (personOptMap.containsKey("pin")) {
            paramName = "pin";
        }
        Map<String, Object> personParamMap = new HashMap<String, Object>();
        personParamMap.put(paramName, personOptMap.get(paramName));
        List<AccPersonOptItem> personInfoList = getPersonByIds(personParamMap);
        // 删除人员权限
        if (personInfoList != null && personInfoList.size() > 0) {
            // boolean delData = personOptMap.containsKey("delData") ? false : true;
            immeDelPersonLevel(personInfoList);
            // 首人开门删除人员
            accFirstOpenService.delFirstOpenFromDev(personInfoList);
            // 多人开门组删除人员
            // accCombOpenPersonService.delCombOpenPersonFromDev(personInfoList);

        }
    }

    @Override
    public void syncTimeSegLevelToDev(String deviceId, List<String> personIdList) {
        AccDevice accDevice = accDeviceDao.findById(deviceId).orElse(new AccDevice());
        AccPersonInfoItem personInfoBean = getPersonByIds(personIdList);
        Map<String, Collection<String>> setLevelMap = AccLevelUtil.setLevelMap(personInfoBean.getPersonList());
        // 只删除人员权限
        delPersonsLevelFromDev(accDevice, setLevelMap);
        // 重新下发权限
        setLevelToDev(accDevice, setLevelMap.get("id"));
    }

    @Override
    public void syncTimeSegVisLevelToDev(String levelId) {
        // 下发访客信息到设备
        if (Objects.nonNull(accGetVisTransactionService)) {
            // 根据权限组id获取访客信息
            List<AccPersonInfoItem> accPersonInfoItemList =
                visToAccItem(accGetVisTransactionService.getVisiotrInfosByLevelId(levelId));
            if (Objects.nonNull(accPersonInfoItemList) && !accPersonInfoItemList.isEmpty()) {
                List<AccPersonOptItem> allPersonInfoList = new ArrayList<>();
                accPersonInfoItemList.forEach(accPersonInfoItem -> {
                    allPersonInfoList.addAll(accPersonInfoItem.getPersonList());
                });
                Map<String, Collection<String>> setLevelMap = AccLevelUtil.setLevelMap(allPersonInfoList);

                // 根据权限组id获取设备信息
                List<AccDevice> devList = accDeviceDao.getDevByLevel(levelId);
                // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
                AccDeviceUtil.moveUpParentDevList(devList);
                String timeSegId = accLevelDao.getTimeSegIdByLevelId(levelId);
                // 时间段数据存在扩展，需要另外查询
                Long timeSegBId = accTimeSegService.getBusinessIdByTimeSegId(timeSegId);
                devList.forEach(accDevice -> {
                    delPersonsLevelFromDev(accDevice, setLevelMap);// 删除人员权限
                    List<AccPersonLevelOptItem> devLevelList = new ArrayList<>();
                    AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
                    accPersonLevelOptItem.setDeviceId(accDevice.getBusinessId());
                    accPersonLevelOptItem.setTimeSegId(timeSegBId);
                    // add by colin 增加权限组id
                    accPersonLevelOptItem.setLevelId(accLevelDao.getOne(levelId).getBusinessId());
                    List<Short> doorNoList = accLevelDoorDao.getDoorNoByLevelIdAndDevId(levelId, accDevice.getId());
                    if (doorNoList != null && !doorNoList.isEmpty()) {
                        accPersonLevelOptItem.setDoorNoList(doorNoList);
                        devLevelList.add(accPersonLevelOptItem);
                    }
                    setLevelToDev(accDevice, devLevelList, allPersonInfoList);// 下发权限
                });
            }

        }

    }

    /**
     * 仅删除人员权限表数据
     *
     * @param accDevice:
     * @param personParamMap:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022-11-24 11:13
     * @since 1.0.0
     */
    private void delPersonsLevelFromDev(AccDevice accDevice, Map<String, Collection<String>> personParamMap) {
        if (personParamMap != null && personParamMap.size() > 0) {
            // 删除权限命令,
            accDevCmdManager.delPersonLevelFromDev(accDevice.getSn(), (List<String>)personParamMap.get("pin"), false);
        }
    }

    /**
     * 删除人员权限
     *
     * @param personInfoList [{"id": 1, "pin": 1, "templateNo": 1},]
     * @return
     * <AUTHOR>
     */
    private void immeDelPersonLevel(List<AccPersonOptItem> personInfoList) {
        if (personInfoList != null && personInfoList.size() > 0) {
            List<String> personIdSet = new ArrayList<>();
            List<String> pinSet = new ArrayList<>();
            for (AccPersonOptItem personInfo : personInfoList) {
                personIdSet.add(personInfo.getId());
                pinSet.add(personInfo.getPin());
            }
            // select DISTINCT e.id from AccLevel e join e.accPersonSet person with person.id in (:list)
            List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonIdIn(personIdSet);
            if (levelIdList != null && levelIdList.size() > 0) {
                // delete from acc_level_person where person_id in (%s)
                accLevelPersonDao.delLevelPersonByIds(personIdSet);
                persPersonLinkService.deleteBatchItem(AccLinkTypeEnum.ACC_LEVEL, levelIdList, personIdSet);
                Map<String, Collection<String>> personParamMap = new HashMap<String, Collection<String>>();
                personParamMap.put("id", personIdSet);
                personParamMap.put("pin", pinSet);
                delPersonLevelFromDev(levelIdList, personParamMap);// 下发删除人员权限命令
            }
        }
    }

    /**
     * 拼装下发人员命令所需要的数据
     *
     * @author: mingfa.zheng
     * @date: 2018/5/3 17:16
     * @return:
     */
    private AccPersonOptItem setAccPersonOptItem(AccPersonOptItem accPersonOptItem, PersPersonItem persPersonItem,
        AccPerson accPerson, AccPersonCombOpenPerson accPersonCombOpenPerson) {
        accPersonOptItem.setId(StringUtils.isNotBlank(persPersonItem.getId()) ? persPersonItem.getId() : "");
        accPersonOptItem.setPin(StringUtils.isNotBlank(persPersonItem.getPin()) ? persPersonItem.getPin() : "");
        // String name = StringUtils.isNotBlank(persPersonItem.getName()) ? persPersonItem.getName() : "";
        //// name += StringUtils.isNotBlank(persPersonItem.getLastName()) ? "#"+persPersonItem.getLastName() :
        // "";//固件中firstName和LastName用#隔开
        //// name = name.trim();
        accPersonOptItem.setName(StringUtils.isNotBlank(persPersonItem.getName()) ? persPersonItem.getName() : "");
        accPersonOptItem
            .setLastName(StringUtils.isNotBlank(persPersonItem.getLastName()) ? persPersonItem.getLastName() : "");
        accPersonOptItem
            .setPersonPwd(StringUtils.isNotBlank(persPersonItem.getPersonPwd()) ? persPersonItem.getPersonPwd() : "");
        accPersonOptItem
            .setIdCard(StringUtils.isNotBlank(persPersonItem.getIdCard()) ? persPersonItem.getIdCard() : "");
        accPersonOptItem.setTempUser(
            persPersonItem.getPersonType() != null ? persPersonItem.getPersonType() == 0 ? false : true : false);
        accPersonOptItem.setPhotoPath(persPersonItem.getPhotoPath());
        // 设置比对照片路径
        accPersonOptItem.setBioPhotoPath(FileUtil.getCropFacePath(persPersonItem.getPin()));
        if (accPerson != null) {
            accPersonOptItem.setStartTime(accPerson.getStartTime());
            accPersonOptItem.setEndTime(accPerson.getEndTime());
            accPersonOptItem.setSuperAuth(accPerson.getSuperAuth());
            accPersonOptItem.setDisabled(accPerson.getDisabled());
            accPersonOptItem.setPrivilege(accPerson.getPrivilege());
            accPersonOptItem.setDelayPassage(accPerson.getDelayPassage() != null
                ? "true".equals(accPerson.getDelayPassage().toString()) ? true : false : false);
        } else {
            accPersonOptItem.setStartTime(null);
            accPersonOptItem.setEndTime(null);
            accPersonOptItem.setSuperAuth((short)0);
            accPersonOptItem.setDisabled(false);
            accPersonOptItem.setPrivilege((short)0);
            accPersonOptItem.setDelayPassage(false);
        }
        accPersonOptItem.setGroupId(
            accPersonCombOpenPerson != null ? accPersonCombOpenPerson.getAccCombOpenPerson().getBusinessId() : 0);
        return accPersonOptItem;
    }

    /**
     * 优化大批量数据情况下，数据发送堆栈溢出问题，权限组删门，多个设备
     *
     * @param tempPersonIdList
     * @param dev
     * @param total
     * @param currentProcess
     * <AUTHOR> href="<EMAIL>">linzj</a>
     * @since 2016年12月12日 下午6:08:19
     */
    private void handleLevelDelDoor(String levelId, List<String> tempPersonIdList, AccDevice dev,
        List<String> doorIdList, int total, int currentProcess) {
        AccPersonInfoItem personInfoBean = getPersonByIds(tempPersonIdList);
        Map<String, Collection<String>> setLevelMap = AccLevelUtil.setLevelMap(personInfoBean.getPersonList());
        // 权限组删除门--数据库
        accLevelDoorDao.delLevelDoorByParams(levelId, doorIdList);
        // 删除人员权限
        delPersonLevelFromDevByLevelDelDoor(dev, setLevelMap, null);// 删除人员权限
        setLevelToDev(dev, setLevelMap.get("id"));// 重新下发该设备中人员的权限，避免权限丢失 (效率太低，后续优化)
    }

    private void setVisLevelToDev(AccDevice accDevice, List<String> visitorPins) {
        if (visitorPins != null && visitorPins.size() != 0) {
            // List<List<String>> visitorPinList = CollectionUtil.split(visitorPins, 500);
            // 解决oracle数据库下，参数超过1000个的问题
            List<AccPersonLevelOptItem> personLevelList = new ArrayList<AccPersonLevelOptItem>();
            if (Objects.nonNull(accGetVisTransactionService)) {
                visitorPins.forEach(visitorPin -> {
                    // TODO 访客是否考虑分批处理
                    List<String> visLevelIdList = accGetVisTransactionService.findModuleIdsByVisEmpPin(visitorPin);
                    if (visLevelIdList != null && visLevelIdList.size() > 0) {
                        List<AccLevelDoor> accLevelDoorList =
                            accLevelDoorDao.findByAccDoor_Device_IdAndAccLevel_IdIn(accDevice.getId(), visLevelIdList);
                        Map<String, List<AccLevelDoor>> accLevelDoorMap =
                            accLevelDoorList.stream().collect(Collectors.groupingBy(i -> i.getId()));
                        accLevelDoorList.stream().forEach(accLevelDoor -> {
                            AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
                            accPersonLevelOptItem.setPin(visitorPin);
                            String timeSegId = accLevelDoor.getAccLevel().getTimeSegId();
                            // 时间段数据存在扩展，需要另外查询
                            Long timeSegBId = accTimeSegService.getBusinessIdByTimeSegId(timeSegId);
                            accPersonLevelOptItem.setTimeSegId(timeSegBId);
                            accPersonLevelOptItem.setDeviceId(accDevice.getBusinessId());
                            List<AccLevelDoor> accLevelDoors = accLevelDoorMap.get(accLevelDoor.getId());
                            if (accLevelDoors != null && accLevelDoors.size() > 0) {
                                List<Short> doorNoList = accLevelDoors.stream().map(i -> i.getAccDoor().getDoorNo())
                                    .collect(Collectors.toList());
                                accPersonLevelOptItem.setDoorNoList(doorNoList);
                                personLevelList.add(accPersonLevelOptItem);
                            }
                        });
                    }
                });
            }
            if (personLevelList.size() > 0) {
                accDevCmdManager.setPersonLevelToDev(accDevice.getSn(), personLevelList, false);
            }
        }
    }

    /**
     * 下发人员的权限命令到设备(只有此类调用)
     *
     * @param dev 设备对象
     * @param personIdColl [1,2,3]
     * @author: mingfa.zheng
     * @date: 2018/5/3 19:44
     */
    private void setLevelToDev(AccDevice dev, Collection<String> personIdColl) {
        if (personIdColl != null && personIdColl.size() != 0) {
            List<List<String>> personIdList = CollectionUtil.split(personIdColl, AccConstants.LEVEL_SPLIT_COUNT);
            List<AccPersonLevelOptItem> personLevelList = null;
            Map<String, String> personPinMap = null;
            Map<String, List<Short>> pinTimeSegDoorMap = null;
            for (List<String> personIds : personIdList) {
                personLevelList = new ArrayList<>();
                pinTimeSegDoorMap = new HashMap<>();
                // 获取到人员id对应人员pin的map
                personPinMap = persPersonService.getPinsByPersonIds(personIds);
                // 根据人员ids和设备ID查询出权限和门的中间表。并根据权限组进行分组
                List<Object[]> accLevelDoorList;
                if (accProExtService != null) {
                    accLevelDoorList = accProExtService.getAccLevelDoorByPersonIdsAndDevId(personIds, dev.getId());
                } else {
                    accLevelDoorList = accLevelDoorDao.getAccLevelDoorTimesByPersonIdsAndDevId(personIds, dev.getId());
                }
                for (Object[] ob : accLevelDoorList) {
                    String pin = personPinMap.get(ob[0]);
                    if (StringUtils.isNotBlank(pin)) {
                        String tempKey = pin + "_" + ob[1];
                        // 加入开始时间和结束时间
                        String st = ob[4] != null ? ob[4].toString() : "";
                        String et = ob[5] != null ? ob[5].toString() : "";
                        if (st.length() > 0 && et.length() > 0) {
                            tempKey += "_" + st + "_" + et;
                        }
                        if (accProExtService != null) {
                            tempKey = "_" + ob[3];
                        }

                        Short doorNo = Short.parseShort(ob[2] + "");
                        if (!pinTimeSegDoorMap.containsKey(tempKey)) {
                            List<Short> doorNoList = new ArrayList<>();
                            doorNoList.add(doorNo);
                            pinTimeSegDoorMap.put(tempKey, doorNoList);
                        } else {
                            pinTimeSegDoorMap.get(tempKey).add(doorNo);
                        }
                    }
                }
                // 拼装下发人员权限的数据。
                for (String key : pinTimeSegDoorMap.keySet()) {
                    String[] pinTimeSegId = key.split("_");
                    AccPersonLevelOptItem accPersonLevelOptItem = new AccPersonLevelOptItem();
                    accPersonLevelOptItem.setPin(pinTimeSegId[0]);
                    accPersonLevelOptItem.setTimeSegId(Long.parseLong(pinTimeSegId[1]));
                    // 加入开始时间和结束时间
                    if (pinTimeSegId.length > 3) {
                        accPersonLevelOptItem.setStartTime(
                            DateUtil.stringToDate(pinTimeSegId[2], DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                        accPersonLevelOptItem
                            .setEndTime(DateUtil.stringToDate(pinTimeSegId[3], DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                    }
                    if (accProExtService != null) {
                        if (pinTimeSegId.length > 4) {
                            accPersonLevelOptItem.setLevelId(Long.valueOf(pinTimeSegId[4]));
                        } else {
                            accPersonLevelOptItem.setLevelId(Long.valueOf(pinTimeSegId[2]));
                        }
                    }
                    accPersonLevelOptItem.setDeviceId(dev.getBusinessId());
                    List<Short> doorNoList = pinTimeSegDoorMap.get(key);
                    if (doorNoList != null && doorNoList.size() > 0) {
                        accPersonLevelOptItem.setDoorNoList(doorNoList);
                        personLevelList.add(accPersonLevelOptItem);
                    }
                }
                if (personLevelList.size() > 0) {
                    accDevCmdManager.setPersonLevelToDev(dev.getSn(), personLevelList, false);
                }
            }
        }
    }

    /**
     * 权限组删除门--下发删除人员权限 删除人员权限后，当该人员对当前设备的任一门都没有权限时，则把该人员从设备中删除
     *
     * @param dev 设备
     * @param personParamMap {"pin": [], "id": []}
     * @param devLevelMap {devId: [15,8]},每个设备对应每个权限组的门组合，如权限组1为：15，权限组2为:8
     * <AUTHOR>
     * @since 2014年8月29日 上午11:30:30
     */
    private void delPersonLevelFromDevByLevelDelDoor(AccDevice dev, Map<String, Collection<String>> personParamMap,
        Map<String, List<Object>> devLevelMap) {
        if (personParamMap != null && personParamMap.size() > 0) {
            List<String> levelIdList = accLevelDoorDao.getLevelIdByDeviceId(dev.getId());
            /*//delete from acc_level_door where level_id=%s and door_id in (select ad.ID from ACC_DOOR ad left join ACC_DEVICE acd on ad.DEV_ID = acd.ID where acd.ID = ?1)
            //根根据设备id和权限组id和删除的门ids查询出所在权限组的门id
            List<String> accDoorIdList = accLevelDoorDao.getDoorIdsByDevIdAndlevelId(dev.getId(), levelId, doorIdList);
            if (accDoorIdList.size() > 0){
            	accLevelDoorDao.delLevelDoorByParams(levelId, accDoorIdList);//权限组删除门--数据库
            }*/
            // boolean sendDelCmd = true;// 是否下发删除命令
            if (devLevelMap != null && devLevelMap.containsKey(dev.getId())) {
                List<Object> devLevelList = devLevelMap.get(dev.getId());

                List<String> tempList = new ArrayList<String>();
                for (String pin : personParamMap.get("pin")) {
                    for (Object obj : devLevelList) {
                        tempList.add(pin + "," + obj);
                    }
                }
                if (tempList.size() > 0) {
                    personParamMap.put("pin", tempList);
                }
            }
            accDevCmdManager.delPersonLevelFromDev(dev.getSn(), (List<String>)personParamMap.get("pin"), false);// 删除权限命令,
            // 按pin删除

            // 当前设备中的所有权限组都不包含该人员时，则把该人员从设备中删除，离职人员处理？？
            // String personIds = StringUtils.join(personParamMap.get("id"),",");
            List<String> personIdList = (List<String>)personParamMap.get("id"); // BllCommonUtil.stringToList(personIds);
            // 解决oracle数据库下，参数超过1000个的问题,采取超过1000个分割查询的方法 ----modify by kaichun-li
            if (personIdList != null && personIdList.size() > 0) {
                List<String> allPersonIdList = new ArrayList<String>();
                List<List<String>> personIdsList = CollectionUtil.split(personIdList, AccConstants.LEVEL_SPLIT_COUNT);
                personIdsList.forEach(personIds -> {
                    if (levelIdList == null || levelIdList.size() <= 0) {
                        levelIdList.add("-1");
                    }
                    // 查询还在权限组下的人员id
                    List<String> partPersonIdList =
                        accLevelPersonDao.getPersonIdByPersonIdInAndLevelIdIn(personIds, levelIdList);
                    // 选中的人员id跟在权限组中的人员id对比，筛选没在权限组中的人员,进行删除
                    List deletePersonIdList = ListUtils.removeAll(personIds, partPersonIdList);
                    if (deletePersonIdList.size() > 0) {
                        allPersonIdList.addAll(deletePersonIdList);
                    }
                });
                if (allPersonIdList.size() > 0) {
                    Map<String, Object> tempMap = new HashMap<String, Object>();
                    tempMap.put("id", StringUtils.join(allPersonIdList, ","));
                    List<AccPersonOptItem> bioInfoList = getPersonByIds(tempMap);
                    List<String> pinSet = new ArrayList<>();
                    for (AccPersonOptItem person : bioInfoList) {
                        pinSet.add(person.getPin());
                    }
                    accDevCmdManager.delPersonBioTemplateFromDev(dev.getSn(), pinSet, false);// 删除指纹
                    // 删除人员信息的时候直接把扩展人员表也删除了
                    // if (accDeviceOptionService.getAccSupportFunListVal(dev.getId(), 7))//是否支持多卡
                    // {
                    // accPersonService.delMulCardFromDev(dev, pinSet);
                    // }
                    // accPersonService.delExuserFromDev(dev, pinSet);//删除扩展人员表
                    accDevCmdManager.delPersonFromDev(dev.getSn(), pinSet, false);// 删除人员信息
                }
            }
        }
    }

    /**
     * 权限组删除人员--下发删除人员权限 删除人员权限后，当该人员对当前设备的任一门都没有权限时，则把该人员从设备中删除
     *
     * @param dev 设备
     * @param personParamMap {"pin": [], "id": []}
     * <AUTHOR>
     * @since 2014年8月29日 上午11:30:30
     */
    private void delPersonLevelFromDevByLevelDelPerson(AccDevice dev, Map<String, Collection<String>> personParamMap) {
        if (personParamMap != null && personParamMap.size() > 0) {
            List<String> levelIdList = accLevelDoorDao.getLevelIdByDeviceId(dev.getId());
            // boolean sendDelCmd = true;// 是否下发删除命令

            // 当前设备中的所有权限组都不包含该人员时，则把该人员从设备中删除，离职人员处理？？
            // String personIds = StringUtils.join(personParamMap.get("id"),",");
            List<String> personIdList = (List<String>)personParamMap.get("id"); // BllCommonUtil.stringToList(personIds);
            // 解决oracle数据库下，参数超过1000个的问题,采取超过1000个分割查询的方法 ----modify by kaichun-li
            if (personIdList != null && personIdList.size() > 0) {
                // 删除数据库中人员的权限
                // delBatchLevel(levelIds, personIdList);
                List<String> allPersonIdList = new ArrayList<String>();
                List<List<String>> personIdsList = CollectionUtil.split(personIdList, AccConstants.LEVEL_SPLIT_COUNT);
                personIdsList.forEach(personIds -> {
                    if (levelIdList == null || levelIdList.size() <= 0) {
                        levelIdList.add("-1");
                    }
                    // 查询还在权限组下的人员id
                    List<String> partPersonIdList =
                        accLevelPersonDao.getPersonIdByPersonIdInAndLevelIdIn(personIds, levelIdList);
                    // 选中的人员id跟在权限组中的人员id对比，筛选没在权限组中的人员,进行删除
                    List deletePersonIdList = ListUtils.removeAll(personIds, partPersonIdList);
                    if (deletePersonIdList.size() > 0) {
                        allPersonIdList.addAll(deletePersonIdList);
                    }

                });
                accDevCmdManager.delPersonLevelFromDev(dev.getSn(), (List<String>)personParamMap.get("pin"), false);// 删除权限命令,
                // 按pin删除
                if (allPersonIdList.size() > 0) {
                    Map<String, Object> tempMap = new HashMap<String, Object>();
                    tempMap.put("id", StringUtils.join(allPersonIdList, ","));
                    List<AccPersonOptItem> bioInfoList = getPersonByIds(tempMap);
                    List<String> pinSet = new ArrayList<>();
                    for (AccPersonOptItem person : bioInfoList) {
                        pinSet.add(person.getPin());
                    }
                    accDevCmdManager.delPersonBioTemplateFromDev(dev.getSn(), pinSet, false);// 删除指纹
                    // 删除人员信息的时候直接把扩展人员表也删除了
                    // if (accDeviceOptionService.getAccSupportFunListVal(dev.getId(), 7))//是否支持多卡
                    // {
                    // accPersonService.delMulCardFromDev(dev, pinSet);
                    // }
                    // accPersonService.delExuserFromDev(dev, pinSet);//删除扩展人员表
                    accDevCmdManager.delPersonFromDev(dev.getSn(), pinSet, false);// 删除人员信息
                }
            }
        }
    }

    /**
     * 权限组删除门--下发删除人员权限 删除人员权限后，当该人员对当前设备的任一门都没有权限时，则把该人员从设备中删除
     *
     * @param dev 设备
     * @param personParamMap {"pin": [], "id": []}
     * <AUTHOR>
     * @since 2018年6月29日 上午11:30:30
     */
    private void delPersonLevelFromDev(AccDevice dev, Map<String, Collection<String>> personParamMap) {
        if (personParamMap != null && personParamMap.size() > 0) {
            List<String> levelIdList = accLevelDoorDao.getLevelIdByDeviceId(dev.getId());
            /*if (StringUtils.isNotBlank(levelId)){
            	accLevelDoorDao.delLevelDoorByParams(levelId, dev.getId());//权限组删除门--数据库
            }*/
            // delete from acc_level_door where level_id=%s and door_id in (select ad.ID from ACC_DOOR ad left join
            // ACC_DEVICE acd on ad.DEV_ID = acd.ID where acd.ID = ?1)
            accDevCmdManager.delPersonLevelFromDev(dev.getSn(), (List<String>)personParamMap.get("pin"), false);// 删除权限命令,
            // 按pin删除
            // 当前设备中的所有权限组都不包含该人员时，则把该人员从设备中删除，离职人员处理？？
            // String personIds = StringUtils.join(personParamMap.get("id"),",");
            List<String> personIdList = (List<String>)personParamMap.get("id"); // BllCommonUtil.stringToList(personIds);
            // 解决oracle数据库下，参数超过1000个的问题,采取超过1000个分割查询的方法 ----modify by kaichun-li
            if (personIdList != null && personIdList.size() > 0) {
                List<List<String>> personIdsList = CollectionUtil.split(personIdList, AccConstants.LEVEL_SPLIT_COUNT);
                List<String> allPersonIdList = new ArrayList<>();
                personIdsList.forEach(personIds -> {
                    if (levelIdList == null || levelIdList.size() <= 0) {
                        levelIdList.add("-1");
                    }
                    // 查询还在权限组下的人员id
                    List<String> partPersonIdList =
                        accLevelPersonDao.getPersonIdByPersonIdInAndLevelIdIn(personIds, levelIdList);
                    // 选中的人员id跟在权限组中的人员id对比，筛选没在权限组中的人员,进行删除
                    List deletePersonIdList = ListUtils.removeAll(personIds, partPersonIdList);
                    if (deletePersonIdList.size() > 0) {
                        allPersonIdList.addAll(deletePersonIdList);
                    }
                });
                if (allPersonIdList.size() > 0) {
                    Map<String, Object> tempMap = new HashMap<>();
                    tempMap.put("id", StringUtils.join(allPersonIdList, ","));
                    List<AccPersonOptItem> bioInfoList = getPersonByIds(tempMap);
                    List<String> pinSet = new ArrayList<>();
                    for (AccPersonOptItem person : bioInfoList) {
                        pinSet.add(person.getPin());
                    }
                    accDevCmdManager.delPersonBioTemplateFromDev(dev.getSn(), pinSet, false);// 删除指纹
                    // 删除人员信息的时候直接把扩展人员表也删除了
                    // if (accDeviceOptionService.getAccSupportFunListVal(dev.getId(), 7))//是否支持多卡
                    // {
                    // accPersonService.delMulCardFromDev(dev, pinSet);
                    // }
                    // accPersonService.delExuserFromDev(dev, pinSet);//删除扩展人员表
                    accDevCmdManager.delPersonFromDev(dev.getSn(), pinSet, false);// 删除人员信息
                }
            }
        }
    }

    private void delVisitorLevelFromDev(AccDevice dev, Map<String, Collection<String>> personParamMap) {
        if (personParamMap != null && personParamMap.size() > 0) {
            accDevCmdManager.delPersonLevelFromDev(dev.getSn(), (List<String>)personParamMap.get("pin"), false);// 删除权限命令,
            // 按pin删除

            // 当前设备中的所有权限组都不包含该人员时，则把该人员从设备中删除，离职人员处理？？
            List<String> visitorIdList = (List<String>)personParamMap.get("id");
            // 解决oracle数据库下，参数超过1000个的问题,采取超过1000个分割查询的方法 ----modify by kaichun-li
            List<List<String>> visitorIdsList = CollectionUtil.split(visitorIdList, AccConstants.LEVEL_SPLIT_COUNT);
            List<String> allVisitorPinList = new ArrayList<>();
            List<String> levelIdList = accLevelDoorDao.getLevelIdByDeviceId(dev.getId());
            if (Objects.nonNull(accGetVisTransactionService)) {
                visitorIdsList.stream().forEach(visitorIds -> {
                    if (levelIdList == null || levelIdList.size() <= 0) {
                        levelIdList.add("-1");
                    }
                    List<String> visitorPinList =
                        accGetVisTransactionService.getTranPinByTranIdAndLevelId(visitorIds, levelIdList);
                    if (visitorPinList != null && visitorPinList.size() > 0) {
                        allVisitorPinList.addAll(visitorPinList);
                    }
                });
            }
            if (allVisitorPinList.size() > 0) {
                /*Map<String, Object> tempMap = new HashMap<String, Object>();
                tempMap.put("id", StringUtils.join(allPersonIdList,","));
                List<AccPersonOptItem> bioInfoList = getPersonByIds(tempMap);
                List<String> pinSet = new ArrayList<>();
                for (AccPersonOptItem person : bioInfoList)
                {
                	pinSet.add(person.getPin());
                }*/
                accDevCmdManager.delPersonBioTemplateFromDev(dev.getSn(), allVisitorPinList, false);// 删除指纹
                // 删除人员信息的时候直接把扩展人员表也删除了
                // if (accDeviceOptionService.getAccSupportFunListVal(dev.getId(), 7))//是否支持多卡
                // {
                // accPersonService.delMulCardFromDev(dev, pinSet);
                // }
                // accPersonService.delExuserFromDev(dev, pinSet);//删除扩展人员表
                accDevCmdManager.delPersonFromDev(dev.getSn(), allVisitorPinList, false);// 删除人员信息
            }
        }
    }

    @Override
    public Pager getAccPersonLevelItemsByPage(AccPersonLevelItem accPersonLevelItem, int pageNo, int pageSize) {
        Pager pager = accLevelDao.getItemsBySql(accPersonLevelItem.getClass(), SQLUtil.getSqlByItem(accPersonLevelItem),
            pageNo, pageSize);
        List<AccPersonLevelItem> levelItems = (List<AccPersonLevelItem>)pager.getData();
        String areaIds = CollectionUtil.getPropertys(levelItems, AccPersonLevelItem::getAuthAreaId);
        List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> itemMap = CollectionUtil.itemListToIdMap(authAreaItemList);
        levelItems.stream().forEach(AccPersonLevelItem -> AccPersonLevelItem
            .setAuthAreaName(itemMap.get(AccPersonLevelItem.getAuthAreaId()).getName()));
        pager.setData(levelItems);
        return pager;
    }

    @Override
    public void delLevelByDevId(String devId) {
        List<String> levelIdList = accLevelDoorDao.getLevelIdByDeviceId(devId);
        for (String levelId : levelIdList) {
            List<String> doorIdList = accDoorDao.getIdByDevId(devId);
            immeDelLevelDoor(levelId, doorIdList);
        }
    }

    @Override
    public void setLevelToDevAsReader(String devId, String wgReaderId) {
        AccReader accReader = accReaderDao.findById(wgReaderId).orElse(null);
        if (Objects.nonNull(accReader)) {
            // 删除老的权限组
            AccDevice dev = accDeviceDao.findById(devId).orElse(null);
            if (Objects.nonNull(dev)) {
                delLevelByDevId(devId);
                // 添加到新的绑定设备的权限组
                List<AccLevelDoor> levelDoorList = accLevelDoorDao.findByAccDoor_Id(accReader.getAccDoor().getId());
                List<String> doorIdList = accDoorDao.getIdByDevId(devId);
                if (Objects.nonNull(levelDoorList)) {
                    levelDoorList.forEach(levelDoor -> {
                        addLevelByParamIds(StringUtils.join(doorIdList, ","), levelDoor.getAccLevel().getId(), "door");
                        immeAddLevelDoor(levelDoor.getAccLevel().getId(), doorIdList, true);
                        List<String> persPersonIdList =
                            accLevelPersonDao.getPersonIdByLevelId(levelDoor.getAccLevel().getId());
                        // addPersonLevel(levelDoor.getAccLevel().getId(), StringUtils.join(persPersonIdList, ","));
                        setLevelToDev(dev, persPersonIdList);
                    });
                }
            }
        }
    }

    /**
     * 根据当前登录用户过滤权限数据
     *
     * @param sessionId
     * @param codition
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccLevelItem codition, int pageNo, int pageSize) {
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(sessionId);
        // 非超级用户进行数据权限过滤
        if (StringUtils.isNotBlank(areaIds)) {
            codition.setAreaIds(areaIds);
        }
        Pager pager = getItemsByPage(codition, pageNo, pageSize);
        List<AccLevelItem> accLevelItemList = (List<AccLevelItem>)pager.getData();
        for (AccLevelItem accLevelItem : accLevelItemList) {
            // 如果不是超级用户登录则根据当前登录用户的区域权限获取门数量 by mingfa.zheng 20190102
            if (StringUtils.isNotBlank(areaIds)) {
                accLevelItem.setDoorCount(String.valueOf(
                    accLevelDoorDao.getDoorCountByLevelIdAndAreaIds(accLevelItem.getId(), StrUtil.strToList(areaIds))));
            } else {
                accLevelItem.setDoorCount(String.valueOf(accLevelDoorDao.getLevelDoorCount(accLevelItem.getId())));
            }
        }
        return pager;
    }

    @Override
    public ZKResultMsg getDoorCount(String sessionId, String levelId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        SecuritySubject subject = authSessionProvider.getSecuritySubject(sessionId);
        if (!subject.getIsSuperuser() && subject.getAreaIds() != null && subject.getAreaIds().size() > 0) {
            zkResultMsg.setData(accLevelDoorDao.getDoorCountByLevelIdAndAreaIds(levelId, subject.getAreaIds()));
            return zkResultMsg;
        }
        zkResultMsg.setData(accLevelDoorDao.getLevelDoorCount(levelId));
        return zkResultMsg;
    }

    @Override
    public List<String> getPersonIdsByLevelId(String levelId) {
        return accLevelPersonDao.getPersonIdByLevelId(levelId);
    }

    @Override
    public void handleLevelAddDoor(String personIds, String deviceIds, String levelId) {
        List<String> personIdList = (List<String>)CollectionUtil.strToList(personIds);
        // 一次性查询出所有权限信息，根据设备id进行存放
        Map<String, Map<String, List<Short>>> devPinTimeSegDoorMap =
            getPinTimeSegDoorMapByCondition(personIdList, levelId, false);
        List<AccDevice> devList = accDeviceDao.findByIdIn((List<String>)CollectionUtil.strToList(deviceIds));
        AccPersonInfoItem personInfoBean = getPersonByIds((personIdList));// 组装下发对象
        if (devList != null && !devList.isEmpty()) {
            Long businessId = accLevelDao.getOne(levelId).getBusinessId();
            AccDeviceUtil.moveUpParentDevList(devList);
            for (AccDevice dev : devList) {
                List<AccPersonLevelOptItem> accPersonLevelOptItemList =
                    getPersonLevelList(devPinTimeSegDoorMap, dev, businessId);

                if (!personInfoBean.getPersonList().isEmpty()) {
                    accDevCmdManager.setPersonToDev(dev.getSn(), personInfoBean.getPersonList(), false);// 下发人员信息
                    if (personInfoBean.getAccBioTemplateItemList() != null
                        && !personInfoBean.getAccBioTemplateItemList().isEmpty()) {
                        accDevCmdManager.setPersonBioTemplateToDev(dev.getSn(),
                            personInfoBean.getAccBioTemplateItemList(), false); // 下发指纹信息
                    }
                    setLevelToDev(dev, accPersonLevelOptItemList, personInfoBean.getPersonList());// 下发权限
                    accDevCmdManager.downloadOver(dev.getSn(), false);
                }
            }
        }
    }

    /**
     * 获取并组装人员信息（临时权限下发信息）
     *
     * @param personIdList:
     * @param levelId:
     * @return java.util.Map<java.lang.String,java.util.Map<java.lang.String,java.util.List<java.lang.Short>>>
     * <AUTHOR>
     * @throws
     * @date 2024-04-23 10:34
     * @since 1.0.0
     */
    private Map<String, Map<String, List<Short>>> getPinTimeSegDoorMapByCondition(List<String> personIdList,
        String levelId, boolean needProExt) {
        Map<String, Map<String, List<Short>>> devPinTimeSegDoorMap = new HashMap<>();
        // 一次性查询出所有权限信息，根据设备id进行存放
        List<Object[]> accLevelDoorList = new ArrayList<>();
        if (levelId == null) {
            accLevelDoorList = accLevelDoorDao.getAccLevelDoorByPersonId(StringUtils.join(personIdList, ","));
        } else {
            accLevelDoorList = accLevelDoorDao.getAccLevelDoorAndTimesByPersonIdsAndLevelId(personIdList, levelId);
        }
        if (accLevelDoorList != null && accLevelDoorList.size() > 0) {
            Map<String, List<Short>> pinTimeSegDoorMap = null;
            for (Object[] ob : accLevelDoorList) {
                String tempKey = ob[0] + "_" + ob[1];
                // 加入开始时间和结束时间
                String st = ob[4] != null ? ob[4].toString() : "";
                String et = ob[5] != null ? ob[5].toString() : "";
                if (st.length() > 0 && et.length() > 0) {
                    tempKey += "_" + st + "_" + et;
                }
                if (accProExtService != null && needProExt) {
                    tempKey = "_" + ob[3];
                }
                Short doorNo = Short.parseShort(ob[2] + "");
                String devId = ob[3].toString();
                pinTimeSegDoorMap = new HashMap<>();
                if (devPinTimeSegDoorMap.containsKey(devId)) {
                    pinTimeSegDoorMap = devPinTimeSegDoorMap.get(devId);
                    if (!pinTimeSegDoorMap.containsKey(tempKey)) {
                        List<Short> doorNoList = new ArrayList<>();
                        doorNoList.add(doorNo);
                        pinTimeSegDoorMap.put(tempKey, doorNoList);
                    } else {
                        pinTimeSegDoorMap.get(tempKey).add(doorNo);
                    }
                } else {
                    List<Short> doorNoList = new ArrayList<>();
                    doorNoList.add(doorNo);
                    pinTimeSegDoorMap.put(tempKey, doorNoList);
                    devPinTimeSegDoorMap.put(devId, pinTimeSegDoorMap);
                }
            }
        }
        return devPinTimeSegDoorMap;
    }

    @Override
    public List<String> getLevelByDevId(String devId) {
        return accLevelDoorDao.getLevelIdByDeviceId(devId);
    }

    @Override
    public void addLevelDoor(String levelId, List<String> doorIdList) {
        addLevelByParamIds(StringUtils.join(doorIdList, ","), levelId, "door");// 数据入库
        immeAddLevelDoor(levelId, doorIdList, true);// 下发人员权限
    }

    @Override
    public List<String> getLevelIdByDoorId(String doorId) {
        List<AccLevelDoor> accLevelDoorList = accLevelDoorDao.findByAccDoor_Id(doorId);
        List<String> accLevelIds = Lists.newArrayList();
        if (Objects.nonNull(accLevelDoorList) && !accLevelDoorList.isEmpty()) {
            accLevelDoorList.forEach(accLevelDoor -> accLevelIds.add(accLevelDoor.getAccLevel().getId()));
        }
        return accLevelIds;
    }

    @Override
    public List<String> getDevIdsByLevelId(String levelId) {
        List<AccDevice> devList = accDeviceDao.getDevByLevel(levelId);
        // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
        AccDeviceUtil.moveUpParentDevList(devList);
        return (List<String>)CollectionUtil.getModelIdsList(devList);
    }

    @Override
    public List<String> getDoorIdsForApp(String personId) {
        return accLevelDoorDao.getLevelDoorByPersonId(personId);
    }

    @Override
    public ApiResultMessage syncApiPersonLevel(String pin, String levelIds) {
        if (StringUtils.isBlank(pin)) {
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        } else if (StringUtils.isBlank(levelIds)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELIDNOTNULL,
                I18nUtil.i18nCode("acc_api_levelIdNotNull"));
        }
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem == null) {// 人员不存在
            return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        List<String> newLevelIds = StrUtil.strToList(levelIds);
        List<AccLevelItem> accLevelItems = getItemsByIdList(newLevelIds);
        // 门禁权限组不存在
        if (accLevelItems == null || accLevelItems.size() == 0) {
            return ApiResultMessage.message(AccConstants.LEVEL_NOTEXIST, I18nUtil.i18nCode("acc_api_levelNotExist"));
        } else {
            Map<String, AccLevelItem> accLevelItemMap = CollectionUtil.itemListToIdMap(accLevelItems);
            for (String newLevelId : newLevelIds) {
                if (!accLevelItemMap.containsKey(newLevelId)) {
                    return ApiResultMessage.message(AccConstants.LEVEL_NOTEXIST,
                        I18nUtil.i18nCode("acc_api_levelNotExist"));
                }
            }
        }
        List<String> exitLevelIds = accLevelPersonDao.getLevelIdByPersonId(persPersonItem.getId());// 获取拥有权限组id
        List<String> tempIdList = new ArrayList<>(newLevelIds);
        tempIdList.retainAll(exitLevelIds);
        // 原有权限组id去除交集部分得出为需要删除的权限组id
        exitLevelIds.removeAll(tempIdList);
        if (!exitLevelIds.isEmpty()) {
            immeDelPersonLevel(exitLevelIds, persPersonItem.getId());// 删除原有权限
        }
        // 需要新增的权限组id
        List<String> needAddLevelIds = new ArrayList<>(newLevelIds);
        needAddLevelIds.removeAll(tempIdList);
        // 添加人员权限到数据库
        addLevelAndTimesByParamIds(persPersonItem.getId(), String.join(",", needAddLevelIds));
        for (String levelId : newLevelIds) {
            setPersonLevelTimesToDev(levelId, persPersonItem.getId());
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage deleteApiPersonLevel(String pin, String levelIds) {
        if (StringUtils.isBlank(pin)) {
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        } else if (StringUtils.isBlank(levelIds)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELIDNOTNULL,
                I18nUtil.i18nCode("acc_api_levelIdNotNull"));
        }
        List<String> newLevelIds = StrUtil.strToList(levelIds);
        List<AccLevelItem> accLevelItems = getItemsByIdList(newLevelIds);
        // 门禁权限组不存在
        if (accLevelItems == null || accLevelItems.size() == 0) {
            return ApiResultMessage.message(AccConstants.LEVEL_NOTEXIST, I18nUtil.i18nCode("acc_api_levelNotExist"));
        } else {
            Map<String, AccLevelItem> accLevelItemMap = CollectionUtil.itemListToIdMap(accLevelItems);
            for (String newLevelId : newLevelIds) {
                if (!accLevelItemMap.containsKey(newLevelId)) {
                    return ApiResultMessage.message(AccConstants.LEVEL_NOTEXIST,
                        I18nUtil.i18nCode("acc_api_levelNotExist"));
                }
            }
        }
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem == null) {// 人员不存在
            return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        // 删除人员权限（删库，下发）
        immeDelPersonLevel((List<String>)CollectionUtil.strToList(levelIds), persPersonItem.getId());
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage addApiLevel(AccApiLevelItem accApiLevelItem) {
        String levelname = accApiLevelItem.getName();

        if (StringUtils.isBlank(levelname)) {
            // 权限组名称为空
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELNAMENOTNULL,
                I18nUtil.i18nCode("acc_api_levelNameNotNull"));
        }

        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage syncApiLevel(String levelId) {
        if (StringUtils.isBlank(levelId)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELIDNOTNULL,
                I18nUtil.i18nCode("acc_api_levelIdNotNull"));
        }

        AccLevelItem accLevelItem = getItemById(levelId);
        if (accLevelItem == null) {// 权限组不存在
            return ApiResultMessage.message(AccConstants.LEVEL_NOTEXIST, I18nUtil.i18nCode("acc_api_levelNotExist"));
        }
        List<String> personIdList = getPersonIdsByLevelId(levelId);
        if (personIdList == null || personIdList.isEmpty()) {// 权限组下没有人
            return ApiResultMessage.message(AccConstants.LEVEL_NOTHAS_PERSONID,
                I18nUtil.i18nCode("acc_api_levelNotHasPerson"));
        }

        List<List<String>> personIds = CollectionUtil.split(personIdList, CollectionUtil.splitSize);// 分批处理，防止处理超过1000，in查询报错
        personIds.forEach(idList -> {
            setSyncApiPersonLevel(levelId, StringUtils.join(idList, ","));
        });// 添加人员权限
        return ApiResultMessage.successMessage();
    }

    /**
     * 同步人员权限（API调用）
     *
     * @param levelId:
     * @param personIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-01-06 15:28
     * @since 1.0.0
     */
    private void setSyncApiPersonLevel(String levelId, String personIds) {
        if (StringUtils.isNotBlank(levelId) && StringUtils.isNotBlank(personIds)) {
            List<String> personList = (List<String>)CollectionUtil.strToList(personIds);
            AccPersonInfoItem personInfoBean = getPersonByIds(personList);
            // 下发人员权限到设备
            setPersonLevelToDev(levelId, personList, true);
        }
    }

    @Override
    public void handlerTransfer(List<AccLevelItem> accLevelItems) {
        // 避免大数据量 进行分批处理
        List<List<AccLevelItem>> accLevelItemList = CollectionUtil.split(accLevelItems, CollectionUtil.splitSize);
        for (List<AccLevelItem> accLevelList : accLevelItemList) {
            // 名称可能存在重复，要保证名称的唯一性
            Collection<String> names = CollectionUtil.getPropertyList(accLevelList, AccLevelItem::getName, "-1");
            List<AccLevel> accLevels = accLevelDao.findByNameIn(names);
            // 对通用权限组做判断
            Map<String, AccLevel> accLevelMap = CollectionUtil.listToKeyMap(accLevels, AccLevel::getName);
            // 获取时间段对象根据时间段id
            Collection<String> ids = CollectionUtil.getPropertyList(accLevelList, AccLevelItem::getTimeSegId, "-1");
            List<Long> businessIdList = new ArrayList<>();
            for (String str : ids) {
                Long i = Long.parseLong(str);
                businessIdList.add(i);
            }
            List<AccTimeSeg> accTimeSegs = accTimeSegDao.findByBusinessIdIn(businessIdList);
            Map<Long, AccTimeSeg> accTimeSegMap = CollectionUtil.listToKeyMap(accTimeSegs, AccTimeSeg::getBusinessId);
            for (AccLevelItem accLevelItem : accLevelList) {
                AccLevel accLevel = new AccLevel();
                if (accLevelItem.getInitFlag()) {
                    accLevel = accLevelDao.findByInitFlag(true);
                } else {
                    accLevel = accLevelMap.remove(accLevelItem.getName());
                }
                if (Objects.isNull(accLevel)) {
                    accLevel = new AccLevel();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accLevelItem, accLevel, "id");
                // 获取时间段对象根据bussinessId
                AccTimeSeg accTimeSeg = accTimeSegMap.get(Long.valueOf(accLevelItem.getTimeSegId()));
                if (Objects.nonNull(accTimeSeg)) {
                    accLevel.setTimeSegId(accTimeSeg.getId());
                }
                // 门禁区域
                AuthAreaItem authAreaItem = authAreaService.getItemByCode(accLevelItem.getAuthAreaId());
                if (Objects.nonNull(authAreaItem)) {
                    accLevel.setAuthAreaId(authAreaItem.getId());
                }
                accLevelDao.save(accLevel);
            }
        }
    }

    @Override
    public void handlerTransferToDoor(List<AccLevelDoorItem> accLevelDoorItems) {
        // 避免大数据量 进行分批处理
        List<List<AccLevelDoorItem>> accLevelDoorItemList =
            CollectionUtil.split(accLevelDoorItems, CollectionUtil.splitSize);
        for (List<AccLevelDoorItem> accLevelDoorList : accLevelDoorItemList) {
            // 查询出权限组的ids
            Collection<String> levelNames =
                CollectionUtil.getPropertyList(accLevelDoorList, AccLevelDoorItem::getAccLevelName, "-1");
            // 根据ids查询出门禁权限集合
            List<AccLevel> accLevelList = accLevelDao.findByNameIn((List<String>)levelNames);
            Map<String, AccLevel> accLevelMap = CollectionUtil.listToKeyMap(accLevelList, AccLevel::getName);
            for (AccLevelDoorItem accLevelDoorItem : accLevelDoorList) {
                AccLevelDoor accLevelDoor = new AccLevelDoor();
                // 获取对应的门禁权限组
                AccLevel accLevel = accLevelMap.get(accLevelDoorItem.getAccLevelName());
                // 查询出门
                AccDoor accDoor = accDoorDao.findByDoorNoAndDevice_Sn(Short.parseShort(accLevelDoorItem.getDoorNo()),
                    accLevelDoorItem.getDeviceSn());
                // 查询是否存在相同相同的记录
                AccLevelDoor accLevelDoorNew =
                    accLevelDoorDao.findByAccDoor_IdAndAccLevel_Id(accDoor.getId(), accLevel.getId());
                if (Objects.nonNull(accDoor) && Objects.nonNull(accLevel) && Objects.isNull(accLevelDoorNew)) {
                    accLevelDoor.setAccLevel(accLevel);
                    accLevelDoor.setAccDoor(accDoor);
                    accLevelDoorDao.save(accLevelDoor);
                }

            }
        }
    }

    @Override
    public void handlerTransferToPerson(List<AccPersonLevelItem> accPersonLevelItems) {
        logger.info("------总的数量--" + accPersonLevelItems.size());
        // 避免大数据量 进行分批处理
        List<List<AccPersonLevelItem>> accPersonLevelList = CollectionUtil.split(accPersonLevelItems, 5000);
        List<AccLevelPerson> accLevelPersonList = accLevelPersonDao.findAll();
        Map<String, AccLevelPerson> map = new HashMap<>();
        for (AccLevelPerson accLevelPerson : accLevelPersonList) {
            map.put(accLevelPerson.getAccLevel().getId() + accLevelPerson.getPersPersonId(), accLevelPerson);
        }
        // 获取人员集合 add by hql 20190805
        List<PersPersonItem> personItems = persPersonService.getByCondition(new PersPersonItem());
        Map<String, PersPersonItem> persPersonItemMap =
            CollectionUtil.listToKeyMap(personItems, PersPersonItem::getPin);
        // 获取门禁权限集合
        List<AccLevel> accLevelList = accLevelDao.findAll();
        Map<String, AccLevel> accLevelMap = CollectionUtil.listToKeyMap(accLevelList, AccLevel::getName);
        // 获取人员关联集合 add by hql 20190807
        Map<String, PersPersonLinkItem> persPersonLinkItemMap = new HashMap<>();
        // 本模块过滤 add by hql 20190809
        List<PersPersonLinkItem> personLinkItems =
            persPersonLinkService.getByCondition(new PersPersonLinkItem().setType("ACC_LEVEL"));
        for (PersPersonLinkItem persPersonLinkItem : personLinkItems) {
            persPersonLinkItemMap.put(
                persPersonLinkItem.getPersonId() + persPersonLinkItem.getLinkId() + persPersonLinkItem.getType(),
                persPersonLinkItem);
        }
        for (List<AccPersonLevelItem> accPersonLevelItemList : accPersonLevelList) {
            logger.info("------处理中--" + accPersonLevelItemList.size());
            // 查询人员pins
            // Collection<String> personPins = CollectionUtil.getPropertyList(accPersonLevelItemList,
            // AccPersonLevelItem::getPin, "-1");
            // 查询出门的ids
            // Collection<String> levelNames = CollectionUtil.getPropertyList(accPersonLevelItemList,
            // AccPersonLevelItem::getLevelName, "-1");
            // 根据ids查询出门禁权限集合
            // List<AccLevel> accLevelList = accLevelDao.findByNameIn((List<String>) levelNames);
            // Map<String, AccLevel> accLevelMap = CollectionUtil.listToKeyMap(accLevelList, AccLevel::getName);
            // 根据pin号查询出人员集合
            // List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(personPins);
            // Map<String, PersPersonItem> persPersonItemMap = CollectionUtil.listToKeyMap(persPersonItemList,
            // PersPersonItem::getPin);

            for (AccPersonLevelItem accPersonLevelItem : accPersonLevelItemList) {
                AccLevelPerson accLevelPerson = new AccLevelPerson();
                // 获取对应的门禁权限组
                AccLevel accLevel = accLevelMap.get(accPersonLevelItem.getLevelName());
                // 查询人员信息 并且设置对应的人员id
                PersPersonItem persPersonItem = persPersonItemMap.get(accPersonLevelItem.getPin());
                // 查询是否存在相同相同的记录
                AccLevelPerson accLevelPersonNew = map.get(accLevel.getId() + persPersonItem.getId());
                if (Objects.isNull(accLevelPersonNew)) {
                    accLevelPerson.setAccLevel(accLevel);
                    accLevelPerson.setPersPersonId(persPersonItem.getId());
                    accLevelPersonDao.save(accLevelPerson);
                }
                if (Objects.nonNull(accLevel) && Objects.nonNull(persPersonItem)) {

                    // 保存personLink 人员控件查询用到的关联
                    PersPersonLinkItem persPersonLinkItem = new PersPersonLinkItem();
                    persPersonLinkItem.setType("ACC_LEVEL");
                    persPersonLinkItem.setLinkId(accLevel.getId());
                    persPersonLinkItem.setPersonId(persPersonItem.getId());
                    // 优化查询,避免重复查询 add by hql 20190807
                    if (Objects.isNull(persPersonLinkItemMap.get(persPersonLinkItem.getPersonId()
                        + persPersonLinkItem.getLinkId() + persPersonLinkItem.getType()))) {
                        persPersonLinkService.save(persPersonLinkItem);
                    }
                }
            }
        }
        accLevelMap = null;
        persPersonItemMap = null;
        map = null;
        accPersonLevelList = null;
    }

    @Override
    public void handlerTransferToDept(List<AccDeptLevelItem> accDeptLevelItems) {
        // 避免大数据量 进行分批处理
        List<List<AccDeptLevelItem>> accDeptLevelList =
            CollectionUtil.split(accDeptLevelItems, CollectionUtil.splitSize);
        for (List<AccDeptLevelItem> accDeptLevelItemList : accDeptLevelList) {
            // 查询出门的ids
            Collection<String> levelNames =
                CollectionUtil.getPropertyList(accDeptLevelItemList, AccDeptLevelItem::getLevelName, "-1");
            // 根据ids查询出门禁权限集合
            List<AccLevel> accLevelList = accLevelDao.findByNameIn((List<String>)levelNames);
            Map<String, AccLevel> accLevelMap = CollectionUtil.listToKeyMap(accLevelList, AccLevel::getName);
            for (AccDeptLevelItem accDeptLevelItem : accDeptLevelItemList) {
                AccLevelDept accLevelDept = new AccLevelDept();
                // 获取对应的门禁权限组
                AccLevel accLevel = accLevelMap.get(accDeptLevelItem.getLevelName());
                // 查询部门
                AuthDepartmentItem authDepartmentItem =
                    authDepartmentService.getItemByCode(accDeptLevelItem.getDeptCode());
                // 查询是否存在相同相同的记录
                AccLevelDept accLevelDeptNew =
                    accLevelDeptDao.getByAccLevel_IdAndDeptId(accLevel.getId(), accDeptLevelItem.getDeptId());
                if (Objects.nonNull(authDepartmentItem) && Objects.nonNull(accLevel)
                    && Objects.isNull(accLevelDeptNew)) {
                    accLevelDept.setAccLevel(accLevel);
                    accLevelDept.setDeptId(authDepartmentItem.getId());
                    accLevelDeptDao.save(accLevelDept);
                }

            }
        }
    }

    @Override
    public ZKResultMsg enableDoor(String levelIds, String sessionId) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        String doorIds = getDoorIdsByLevelIdsAndSessionId(levelIds, sessionId);
        if (StringUtils.isNotBlank(doorIds)) {
            resultMsg = accDoorService.enable(doorIds);
        }
        return resultMsg;
    }

    @Override
    public void disableDoor(String ids, String sessionId) {
        String doorIds = getDoorIdsByLevelIdsAndSessionId(ids, sessionId);
        if (StringUtils.isNotBlank(doorIds)) {
            accDoorService.disable(doorIds);
        }
    }

    @Override
    public Map<String, String> operateDoor(String opType, String openInterval, String levelIds, String sessionId) {
        String doorIds = getDoorIdsByLevelIdsAndSessionId(levelIds, sessionId);
        Map<String, String> resultMap = new HashMap<>();
        if (StringUtils.isNotBlank(doorIds)) {
            List<AccDoor> doorList = accDoorDao.findByIdIn((List<String>)CollectionUtil.strToList(doorIds));
            AccDevice dev = null;
            short doorNo = 0;
            long cmdId = 0;
            StringBuffer offlineDev = new StringBuffer();
            StringBuffer cmdIdBuf = new StringBuffer();
            StringBuffer opFailBuf = new StringBuffer();
            StringBuffer notSupportBuf = new StringBuffer();
            String notExistDev = "";
            for (AccDoor door : doorList) {
                dev = door.getDevice();
                doorNo = door.getDoorNo();
                cmdId = 0;
                if (!door.getEnabled()) {
                    offlineDev.append(door.getName()).append(",");// 离线设备门名称集合

                } else if (Short.valueOf(accDeviceService.getStatus(dev.getSn()))
                    .shortValue() == AccConstants.DEV_STATE_ONLINE) {
                    if ("openDoor".equals(opType)) {
                        short intervalShort = Short.valueOf(openInterval);// 开门时长（1-254秒，255秒为常开）
                        cmdId = accDevCmdManager.ctrlDoor(dev.getSn(), doorNo, intervalShort, true);// 远程开门
                    } else if ("normalOpenDoor".equals(opType)) {
                        cmdId = accDevCmdManager.ctrlDoor(dev.getSn(), doorNo, (short)255, true);// 远程常开
                    } else if ("enableNormalOpenDoor".equals(opType)) {
                        cmdId = accDevCmdManager.ctrlNormalOpenTimeZone(dev.getSn(), doorNo, 1, true);// 启用当天常开时间段
                    } else if ("closeDoor".equals(opType)) {
                        cmdId = accDevCmdManager.ctrlDoor(dev.getSn(), doorNo, (short)0, true);// 远程关门
                    } else if ("disableNormalOpenDoor".equals(opType)) {
                        cmdId = accDevCmdManager.ctrlNormalOpenTimeZone(dev.getSn(), doorNo, 0, true);// 禁用当天常开时间段
                    } else if ("cancelAlarm".equals(opType)) {
                        cmdId = accDevCmdManager.cancelAlarm(dev.getSn(), doorNo, true);// 取消报警
                    } else if ("lockDoor".equals(opType)) {
                        if (accDeviceOptionService.isSupportFunList(dev.getSn(), 2)) {
                            cmdId = accDevCmdManager.ctrlLockDoor(dev.getSn(), doorNo, 1, true);// 门锁定
                        } else {
                            notSupportBuf.append(door.getName()).append(",");// 不支持门名称
                        }
                    } else if ("unLockDoor".equals(opType)) {
                        if (accDeviceOptionService.isSupportFunList(dev.getSn(), 2)) {
                            cmdId = accDevCmdManager.ctrlLockDoor(dev.getSn(), doorNo, 0, true);// 门解锁
                        } else {
                            notSupportBuf.append(door.getName()).append(",");// 不支持门名称
                        }
                    }
                    if (cmdId > 0) {
                        // 增加返回设备sn和门编号
                        cmdIdBuf.append(cmdId).append("=").append(door.getName()).append("=").append(dev.getSn())
                            .append("=").append(doorNo).append(",");
                    } else {
                        opFailBuf.append(door.getName()).append(",");// 发送命令失败门名称集合
                    }
                } else {
                    offlineDev.append(door.getName()).append(",");// 离线设备门名称集合
                }
            }
            if (doorList.size() == 0) {
                notExistDev = "true";// 不存在可操作设备
            }
            resultMap.put("offline", offlineDev.length() > 1 ? offlineDev.substring(0, offlineDev.length() - 1) : "");
            resultMap.put("faile", opFailBuf.length() > 1 ? opFailBuf.substring(0, opFailBuf.length() - 1) : "");
            resultMap.put("cmdId", cmdIdBuf.length() > 1 ? cmdIdBuf.substring(0, cmdIdBuf.length() - 1) : "");
            resultMap.put("notSupport",
                notSupportBuf.length() > 1 ? notSupportBuf.substring(0, notSupportBuf.length() - 1) : "");
            resultMap.put("notExistDev", notExistDev);
            return resultMap;
        }
        return resultMap;
    }

    /**
     * 根据用户权限获取门ids
     *
     * @param levelIds
     * @param sessionId
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-13 11:04
     */
    private String getDoorIdsByLevelIdsAndSessionId(String levelIds, String sessionId) {
        String levelDoorIds = "";
        String areaIds = "";
        // 获取当前登录用户信息
        SecuritySubject securitySubject = authSessionProvider.getSecuritySubject(sessionId);
        if (!securitySubject.getIsSuperuser() && securitySubject.getAreaIds() != null
            && securitySubject.getAreaIds().size() > 0) {
            // 非超级用户进行数据权限过滤
            areaIds = StrUtil.collectionToStr(securitySubject.getAreaIds());
        }
        List<String> doorIds = new ArrayList<>();
        if (securitySubject.getIsSuperuser()) {
            doorIds = accLevelDoorDao.getDoorIdsByLevelIdsIn(StrUtil.strToList(levelIds));
        } else if (StringUtils.isNotBlank(areaIds)) {
            doorIds = accLevelDoorDao.getDoorIdsByLevelIdsInAndAreaIdsIn(StrUtil.strToList(levelIds),
                StrUtil.strToList(areaIds));
        }
        if (doorIds != null && doorIds.size() > 0) {
            levelDoorIds = StringUtils.join(doorIds, ",");
        }
        return levelDoorIds;
    }

    @Override
    public ZKResultMsg dealResultData(Map<String, String> dataMap) {
        String cmdIdData = dataMap.get("cmdId");
        String msg = "";
        ZKResultMsg resultMsg = new ZKResultMsg();
        if ("true".equals(dataMap.get("notExistDev"))) {
            msg = I18nUtil.i18nCode("common_dev_opFaileAndReason") + I18nUtil.i18nCode("common_dev_notExistDev");
            resultMsg.setMsg(msg);
            return resultMsg;
        }
        if (!"".equals(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String cmdId = cmdData.split("=")[0];
                String doorName = cmdData.split("=")[1];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap)) {
                    msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                } else {
                    Integer ret = Integer.valueOf(resultMap.get("result"));
                    if (Objects.isNull(ret)) {
                        msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                    } else if (ret < 0) {
                        String failedInfo = I18nUtil.i18nCode(accBaseDictionaryService.getCommReason(ret));
                        msg += doorName + "," + I18nUtil.i18nCode("common_dev_opFaileAndReason") + failedInfo + ";";
                    }
                }
            }
        }
        if (!"".equals(dataMap.get("offline"))) {
            msg += dataMap.get("offline") + I18nUtil.i18nCode("common_dev_offlinePrompt") + ";";
            /*for (String doorName : dataMap.get("offline").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("common_dev_offlinePrompt") + ";";
            }*/
        }
        if (!"".equals(dataMap.get("notSupport"))) {
            msg += dataMap.get("notSupport") + I18nUtil.i18nCode("acc_dev_devNotSupportFunction") + ";";
            /*for (String doorName : dataMap.get("notSupport").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("acc_dev_devNotSupportFunction") + ";";
            }*/
        }
        if (!"".equals(msg)) {
            resultMsg.setRet("400");
            resultMsg.setMsg(msg);
        }
        resultMsg.setData("");
        return resultMsg;
    }

    @Override
    public boolean checkTimeSegUsed(String timeSegId) {
        return accLevelDao.countByTimeSegId(timeSegId) > 0;
    }

    @Override
    public List<String> getPersonIdByDevIdOrParentDevId(String devId) {
        return accLevelDao.getPersonIdByDevIdOrParentDevId(devId);
    }

    @Override
    public List<String> getLevelIdsByDeviceId(String devId) {
        return accLevelDao.getLevelIdsByDeviceId(devId);
    }

    @Override
    public List<AccLevelDoorItem> saveAccLevelDoorList(List<AccLevelDoorItem> levelDoors) {
        if (Objects.nonNull(levelDoors) && !levelDoors.isEmpty()) {
            // 获取门/权限组信息-map
            Map<String, AccDoor> doorMap = new HashMap<>();
            Map<String, AccLevel> levelMap = new HashMap<>();
            for (AccLevelDoorItem levelDoorItem : levelDoors) {
                AccDoor accDoor = accDoorDao.findOne(levelDoorItem.getAccDoorId());
                if (Objects.nonNull(accDoor)) {
                    doorMap.put(accDoor.getId(), accDoor);
                }
                AccLevel accLevel = accLevelDao.findOne(levelDoorItem.getAccLevelId());
                if (Objects.nonNull(accLevel)) {
                    levelMap.put(accLevel.getId(), accLevel);
                }
            }

            for (AccLevelDoorItem levelDoor : levelDoors) {
                AccDoor door = doorMap.get(levelDoor.getAccDoorId());
                AccLevel level = levelMap.get(levelDoor.getAccLevelId());
                AccLevelDoor accLevelDoor = new AccLevelDoor().setAccDoor(door).setAccLevel(level);
                // 保存权限组-门中间表信息
                accLevelDoorDao.save(accLevelDoor);
                levelDoor.setId(accLevelDoor.getId());
            }
        }
        return levelDoors;
    }

    @Override
    public List<?> getItemData(Class<?> cls, BaseItem condition, int beginIndex, int endIndex) {
        return accLevelDao.getItemsDataBySql(cls, SQLUtil.getSqlByItem(condition), beginIndex, endIndex, true);
    }

    @Override
    public List<AccLevelDoorItem> getLevelDoorItemsByLevelDoorIds(List<String> levelDoorIds) {
        List<AccLevelDoor> accLevelDoorList = accLevelDoorDao.findByIdList(levelDoorIds);
        List<AccLevelDoorItem> accLevelDoorItemList = new ArrayList<>();
        if (Objects.nonNull(accLevelDoorList) && !accLevelDoorList.isEmpty()) {
            for (AccLevelDoor accLevelDoor : accLevelDoorList) {
                AccLevelDoorItem item = new AccLevelDoorItem();
                item.setId(accLevelDoor.getId());
                item.setAccLevelId(accLevelDoor.getAccLevel().getId());
                item.setDeviceId(accLevelDoor.getAccDoor().getDevice().getId());
                item.setAccDoorId(accLevelDoor.getAccDoor().getId());
                accLevelDoorItemList.add(item);
            }
        }
        return accLevelDoorItemList;
    }

    @Override
    public List<AccLevelItem> getItemsByIdList(List<String> levelIdList) {
        List<AccLevel> accLevels = accLevelDao.findByIdIn(levelIdList);
        List<AccLevelItem> accLevelItemList = new ArrayList<>();
        if (Objects.nonNull(accLevels) && !accLevels.isEmpty()) {
            for (AccLevel level : accLevels) {
                AccLevelItem item = new AccLevelItem();
                ModelUtil.copyProperties(level, item);
                item.setStartDate(DateUtil.dateToString(level.getStartDate(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                item.setEndDate(DateUtil.dateToString(level.getEndDate(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                item.setTimeSegId(level.getTimeSegId());
                accLevelItemList.add(item);
            }
        }
        return accLevelItemList;
    }

    @Override
    public List<AccLevelDoorItem> getLevelDoorItemsByDevSn(String devSn) {
        List<AccLevelDoor> accLevelDoorList = accLevelDoorDao.findByAccDoor_Device_SnOrderByAccDoor_DoorNo(devSn);
        List<AccLevelDoorItem> accLevelDoorItemList = new ArrayList<>();
        if (Objects.nonNull(accLevelDoorList) && !accLevelDoorList.isEmpty()) {
            for (AccLevelDoor levelDoor : accLevelDoorList) {
                AccLevelDoorItem levelDoorItem = new AccLevelDoorItem();
                levelDoorItem.setId(levelDoor.getId());
                AccDoor door = levelDoor.getAccDoor();
                levelDoorItem.setAccDoorId(door.getId());
                levelDoorItem.setName(door.getName());
                levelDoorItem.setDeviceId(door.getDevice().getId());
                levelDoorItem.setAccLevelId(levelDoor.getAccLevel().getId());
                levelDoorItem.setAccLevelName(levelDoor.getAccLevel().getName());
                accLevelDoorItemList.add(levelDoorItem);
            }
        }
        return accLevelDoorItemList;
    }

    @Override
    public List<AccLevelDoorItem> getLevelDoorItemsByDeviceSnList(List<String> deviceSnList) {
        List<AccLevelDoor> accLevelDoorList = accLevelDoorDao.findByAccDoor_Device_SnIn(deviceSnList);
        List<AccLevelDoorItem> accLevelDoorItemList = new ArrayList<>();
        if (Objects.nonNull(accLevelDoorList) && !accLevelDoorList.isEmpty()) {
            for (AccLevelDoor accLevelDoor : accLevelDoorList) {
                AccLevelDoorItem levelDoorItem = new AccLevelDoorItem();
                levelDoorItem.setId(accLevelDoor.getId());
                AccDoor door = accLevelDoor.getAccDoor();
                levelDoorItem.setAccDoorId(door.getId());
                levelDoorItem.setDeviceId(door.getDevice().getId());
                levelDoorItem.setAccLevelId(accLevelDoor.getAccLevel().getId());
                accLevelDoorItemList.add(levelDoorItem);
            }
        }
        return accLevelDoorItemList;
    }

    @Override
    public int countByTimeSegId(String timeSegId) {
        return accLevelDao.countByTimeSegId(timeSegId);
    }

    @Override
    public Long getDoorCountByLevelIdAndAreaIds(String levelId, List<String> areaId) {
        return accLevelDoorDao.getDoorCountByLevelIdAndAreaIds(levelId, areaId);
    }

    @Override
    public Pager getSimpleItemsByPage(BaseItem condition, int page, int size) {
        return accLevelDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public List<?> getItemsByCondition(BaseItem condition) {
        return accLevelDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public AccLevelItem saveSimpleItem(AccLevelItem item) {
        AccLevel accLevel = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accLevelDao.findById(id)).orElse(new AccLevel());
        ModelUtil.copyPropertiesIgnoreNull(item, accLevel);
        // 开始结束时间
        if (StringUtils.isNoneBlank(item.getStartDate())) {
            accLevel.setStartDate(DateUtil.stringToDate(item.getStartDate(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        } else {
            accLevel.setStartDate(null);
        }
        if (StringUtils.isNoneBlank(item.getEndDate())) {
            accLevel.setEndDate(DateUtil.stringToDate(item.getEndDate(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        } else {
            accLevel.setEndDate(null);
        }
        accLevelDao.save(accLevel);

        item.setId(accLevel.getId());
        return item;
    }

    @Override
    public List<AccLevelDoorItem> getLevelDoorItemsByLevelId(String levelIds) {
        List<AccLevelDoor> accLevelDoorList = accLevelDoorDao.findByAccLevel_Id(levelIds);
        List<AccLevelDoorItem> accLevelDoorItemList = new ArrayList<>();
        if (Objects.nonNull(accLevelDoorList) && !accLevelDoorList.isEmpty()) {
            for (AccLevelDoor accLevelDoor : accLevelDoorList) {
                AccLevelDoorItem item = new AccLevelDoorItem();
                item.setId(accLevelDoor.getId());
                item.setAccLevelId(accLevelDoor.getAccLevel().getId());
                item.setDeviceId(accLevelDoor.getAccDoor().getDevice().getId());
                item.setAccDoorId(accLevelDoor.getAccDoor().getId());
                accLevelDoorItemList.add(item);
            }
        }
        return accLevelDoorItemList;
    }

    @Override
    public List<AccLevelPersonItem> getLevelPersonItemByDevSn(String devSn) {
        List<AccLevelPerson> accLevelPersonList = accLevelPersonDao.getLevelPersonByDevSn(devSn);
        List<AccLevelPersonItem> accLevelPersonItems = new ArrayList<>();
        if (Objects.nonNull(accLevelPersonList) && !accLevelPersonList.isEmpty()) {
            for (AccLevelPerson levelPerson : accLevelPersonList) {
                AccLevelPersonItem item = new AccLevelPersonItem();
                item.setId(levelPerson.getId());
                item.setAccLevelId(levelPerson.getAccLevel().getId());
                item.setLevelBId(levelPerson.getAccLevel().getBusinessId());
                item.setPersPersonId(levelPerson.getPersPersonId());
                accLevelPersonItems.add(item);
            }
        }
        return accLevelPersonItems;
    }

    @Override
    public List<String> getLevelIdsByDevSn(String devSn) {
        return accLevelDoorDao.getLevelIdByDeviceSn(devSn);
    }

    @Override
    public List<Short> getDoorNoByLevelIdAndDevId(String levelId, String deviceId) {
        return accLevelDoorDao.getDoorNoByLevelIdAndDevId(levelId, deviceId);
    }

    @Override
    public void delPersonLevelToDevice(List<AccDeviceItem> devItemList, Map<String, Collection<String>> personInfoMap) {
        if (devItemList.size() > 0 && personInfoMap != null && personInfoMap.size() > 0) {
            List<AccDevice> deviceList = ModelUtil.copyListProperties(devItemList, AccDevice.class);
            for (AccDevice dev : deviceList) {
                // 全删全下权限
                delPersonLevelFromDevByLevelDelPerson(dev, personInfoMap);
                // 重新下发该设备中人员的权限，避免权限丢失
                setLevelToDev(dev, personInfoMap.get("id"));
            }
        }
    }

    @Override
    public List<AccPersonLevelItem> getAccPersonLevelItemsByCondition(String authAreaIds, String personIds) {
        AccPersonLevelItem accPersonLevelItem = new AccPersonLevelItem();
        if (StringUtils.isNotBlank(authAreaIds)) {
            accPersonLevelItem.setAuthAreaIdIn(authAreaIds);
        }
        accPersonLevelItem.setPersonIdIn(personIds);
        List<AccPersonLevelItem> accPersonLevelItems = (List<AccPersonLevelItem>)accLevelDao
            .getItemsBySql(AccPersonLevelItem.class, SQLUtil.getSqlByItem(accPersonLevelItem));
        return accPersonLevelItems;
    }
}