package com.zkteco.zkbiosecurity.acc.data.upgrade;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.dao.AccDSTimeDao;
import com.zkteco.zkbiosecurity.acc.model.AccDSTime;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.dao.AccAntiPassbackDao;
import com.zkteco.zkbiosecurity.acc.dao.AccInterlockDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLinkageVidDao;
import com.zkteco.zkbiosecurity.acc.model.AccAntiPassback;
import com.zkteco.zkbiosecurity.acc.model.AccInterlock;
import com.zkteco.zkbiosecurity.acc.model.AccLinkageVid;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/2/5 14:41
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccVer3_11_0 implements UpgradeVersionManager {

    @Autowired
    private AccLinkageVidDao accLinkageVidDao;
    @Autowired
    private AccAntiPassbackDao accAntiPassbackDao;
    @Autowired
    private AccInterlockDao accInterlockDao;
    @Value("${system.isCloud:false}")
    private Boolean isCloud;
    @Value("${system.language:zh_CN}")
    private String language;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private AccDSTimeDao accDSTimeDao;

    @Override
    public String getVersion() {
        return "v3.11.0";
    }

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public boolean executeUpgrade() {
        // 更新视频联动配置
        updateAccLinkageVid();
        updateAccAntiPassback();
        updateAccInterlock();
        if ("zh_CN".equals(language) && !isCloud) {
            initAppMenus();
        }
        updateAccDSTime();
        return true;
    }

    /**
     * 更新视频联动配置
     * 
     * @return void
     * <AUTHOR>
     * @date 2024-02-05 14:44
     * @since 1.0.0
     */
    private void updateAccLinkageVid() {
        List<AccLinkageVid> accLinkageVidList = accLinkageVidDao.findAll();
        if (CollectionUtil.isEmpty(accLinkageVidList)) {
            return;
        }
        for (AccLinkageVid accLinkageVid : accLinkageVidList) {
            // 原录像时长复用；升级后此字段为空，默认为10秒
            accLinkageVid.setRecordBeforeTime(10);
        }
        accLinkageVidDao.saveAll(accLinkageVidList);
    }

    /**
     * 反潜名称升级
     */
    private void updateAccAntiPassback() {
        List<AccAntiPassback> accAntiPassbackList = accAntiPassbackDao.findAll();
        if (ObjectUtils.isNotEmpty(accAntiPassbackList)) {
            for (AccAntiPassback accAntiPassback : accAntiPassbackList) {
                // 原本没有name字段，升级后把设备名称赋值给规则名称
                String deviceName = accAntiPassback.getAccDevice().getAlias();
                accAntiPassback.setName(deviceName);
                accAntiPassbackDao.save(accAntiPassback);
            }
        }
    }

    /**
     * 互锁升级
     */
    private void updateAccInterlock() {
        // 删除设备id唯一键
        jdbcTemplate.execute("ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9");

        List<AccInterlock> accInterlockList = accInterlockDao.findAll();
        if (ObjectUtils.isNotEmpty(accInterlockList)) {
            for (AccInterlock accInterlock : accInterlockList) {
                // 原本没有name字段，升级后把设备名称赋值给规则名称
                String deviceName = accInterlock.getAccDevice().getAlias();
                accInterlock.setName(deviceName);
                accInterlockDao.save(accInterlock);
            }
        }
    }

    /**
     * 初始化管理员app权限
     */
    private void initAppMenus() {

        AuthPermissionItem appModuleItem = null;
        AuthPermissionItem appMenuItem = null;
        AuthPermissionItem appButtonItem = null;

        appModuleItem = authPermissionService.getItemByCode("App");
        if (null == appModuleItem) {
            // 管理员APP模块
            appModuleItem = new AuthPermissionItem("App", "app_module", "app", AuthContants.RESOURCE_TYPE_APP_SYSTEM,
                ZKConstant.TRUE, 998);
            authPermissionService.initData(appModuleItem);
        }
        appMenuItem = authPermissionService.getItemByCode("AppAcc");
        if (null == appMenuItem) {
            appMenuItem = new AuthPermissionItem("AppAcc", "app_acc", "app:APPacc", AuthContants.RESOURCE_TYPE_APP_MENU,
                ZKConstant.TRUE, 2);
            appMenuItem.setParentId(appModuleItem.getId());
            appMenuItem = authPermissionService.initData(appMenuItem);
        }

        // 门禁--报表
        appButtonItem = new AuthPermissionItem("AppAccReport", "app_acc_report", "app:APPaccReport",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 4);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
        // 门禁--远程控制
        appButtonItem = new AuthPermissionItem("AppAccRemoteControl", "app_acc_remoteControl",
            "app:APPaccRemoteControl", AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 5);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
        // 门禁--锁定
        appButtonItem = new AuthPermissionItem("AppAccLockdown", "app_acc_lockdown", "app:APPaccLockdown",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 6);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
    }

    /**
     * 升级之前版本0时区夏令时时区标识符错误问题，原本是+0000，需要变成-0000
     */
    private void updateAccDSTime() {
        AccDSTime example = new AccDSTime();
        example.setTimeZone("+0000");
        List<AccDSTime> accDSTimes = accDSTimeDao.findByExample(example);
        if (ObjectUtils.isNotEmpty(accDSTimes)) {
            for (AccDSTime accDSTime : accDSTimes) {
                accDSTime.setTimeZone("-0000");
                accDSTimeDao.save(accDSTime);
            }
        }
    }
}
