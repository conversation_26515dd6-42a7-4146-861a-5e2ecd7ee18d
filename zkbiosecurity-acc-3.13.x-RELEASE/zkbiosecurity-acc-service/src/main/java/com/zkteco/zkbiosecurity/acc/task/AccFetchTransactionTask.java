package com.zkteco.zkbiosecurity.acc.task;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ScheduledFuture;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.utils.AccDataUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

@Component
public class AccFetchTransactionTask {

    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    // 一个类里最好写一个定时任务，多个scedulefuture会被替换为最新的定时任务，导致之前的不能被停止。
    private ScheduledFuture<?> scedulefuture;

    public void initFetchTransaction() {
        String downNewLogExpression = baseSysParamService.getValByName("acc.downNewLogExpression");
        if (StringUtils.isBlank(downNewLogExpression)) {
            downNewLogExpression = "0 0 0 * * ?";
        }
        if (scedulefuture != null) {
            scedulefuture.cancel(true);
        }
        scedulefuture = scheduleService.startScheduleTask(() -> getAutoTransFromDev(), downNewLogExpression);
    }

    /**
     * 自动获取事件记录
     *
     * @author: mingfa.zheng
     * @date: 2018/5/9 11:52
     * @return:
     */
    public void getAutoTransFromDev() {
        List<AccDevice> allDevList = accDeviceDao.findAll();
        // logger.info("Auto DownloadAccEventTask****size:" + allDevList.size() + "##time:" + new
        // SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        for (AccDevice dev : allDevList) {
            // 修改为直接调用adms接口
            String devStatus = accDeviceService.getStatus(dev.getSn()); // 获取设备状态
            if (StringUtils.isNotBlank(devStatus) && Short.valueOf(devStatus) != ConstUtil.DEV_STATE_DISABLE) {
                // 主控设备才需要获取定时获取事件记录，子控是通过主控获取的，不需要再次获取 by juvenile.li 20171011 add
                if (dev.getParentDevice() == null) {
                    // 同时同步设备时间，支持时区的设备同步时间需要发送gmt时间
                    if (accDeviceOptionService.isSupportFun(dev.getSn(), "MachineTZFunOn")) {
                        accDevCmdManager.syncTime(dev.getSn(), AccDataUtil.getGMT(), true);
                    } else {
                        accDevCmdManager.syncTime(dev.getSn(), new Date(), true);
                    }
                    accDevCmdManager.getTransaction(dev.getSn(), true, true); // 对启用的设备进行获取事件记录
                    // accDevCmdBiz.setTime(ConstUtil.SYSTEM_MODULE_ACC, dev, new Date()); //同时同步设备时间
                    // accDevCmdBiz.getTableDataFromDev(dev, ConstUtil.TRANSACTION, true, false); //对启用的设备进行获取事件记录
                    // 默认是15s,pull 设备获取太快，没休眠会出现jvm奔溃，此处每次一台获取休眠15s modified by max.zheng 20171117
                    try {
                        Thread.sleep(1000 * 15);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }
}
