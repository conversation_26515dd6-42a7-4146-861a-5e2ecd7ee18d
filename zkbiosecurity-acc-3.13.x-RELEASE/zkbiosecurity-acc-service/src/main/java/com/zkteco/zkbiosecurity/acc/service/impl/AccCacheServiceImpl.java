package com.zkteco.zkbiosecurity.acc.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.service.AccCacheService;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSearchAddDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;

/**
 * <AUTHOR>
 * @date 2021/2/1 16:33
 * @since 1.0.0
 */
@Component
public class AccCacheServiceImpl implements AccCacheService {

    @Autowired
    private AccCacheManager accCacheManager;

    @Override
    public JSONObject getIdentityCardInfo(String key) {
        return accCacheManager.getIdentityCardInfo(key);
    }

    @Override
    public void setTransactionToClient(String accTransactionItemStr) {
        accCacheManager.setTransactionToClient(accTransactionItemStr);
    }

    @Override
    public void putLastRtLogToCache(AccTransactionItem accTransaction) {
        accCacheManager.putLastRtLogToCache(accTransaction);
    }

    @Override
    public void putTransactionToDb(String data) {
        accCacheManager.putTransactionToDb(data);
    }

    @Override
    public AccTransactionItem getLastRtLogFromCache(String sn) {
        return accCacheManager.getLastRtLogFromCache(sn);
    }

    @Override
    public AccSearchAddDeviceItem getTempDevFromCache(String sn) {
        return accCacheManager.getTempDevFromCache(sn);
    }

    @Override
    public void putTempDevice2Cache(String sn, AccSearchAddDeviceItem item) {
        accCacheManager.putTempDevice2Cache(sn, item);
    }

    @Override
    public AccQueryDeviceItem getDeviceInfo(String sn) {
        return accCacheManager.getDeviceInfo(sn);
    }

    @Override
    public void putDeviceInfo(AccQueryDeviceItem accQueryDeviceItem) {
        accCacheManager.putDeviceInfo(accQueryDeviceItem);
    }

    @Override
    public void putDeviceOptionInfo(String sn, String optionStr) {
        accCacheManager.putDeviceOptionInfo(sn, optionStr);
    }

    @Override
    public JSONObject getDeviceOptionInfo(String sn) {
        return accCacheManager.getDeviceOptionInfo(sn);
    }

    @Override
    public String getDeviceAuthorizeInfo(String sn) {
        return accCacheManager.getDeviceAuthorizeInfo(sn);
    }

    @Override
    public void sendMessageToTopic(String mess, String topic) {
        accCacheManager.sendMessageToTopic(mess, topic);
    }
}
