package com.zkteco.zkbiosecurity.acc.data.move;

import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.annotation.DataMigration;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.system.service.DataMoveManager;
import com.zkteco.zkbiosecurity.system.service.SystemDataMigrationService;
import com.zkteco.zkbiosecurity.system.vo.SystemDataTransferProcess;
import com.zkteco.zkbiosecurity.system.vo.SystemDataUpgradeProcess;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 门禁数据迁移
 *
 * <AUTHOR>
 * @date 2018-12-05 14:19
 */
@Component
@Slf4j
@DataMigration(order = 20)
public class AccDataMoveManagement implements DataMoveManager {

    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private SystemDataMigrationService systemDataMigrationService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDSTimeService accDSTimeService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDeviceEventService accDeviceEventService;
    @Autowired
    private AccDeviceVerifyModeService accDeviceVerifyModeService;
    @Autowired
    private AccAuxInService accAuxInService;
    @Autowired
    private AccAuxOutService accAuxOutService;
    @Autowired
    private AccDoorService accDoorServce;
    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccPersonLastAddrService accPersonLastAddrService;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccHolidayService accHolidayService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccInterlockService accInterlockService;
    @Autowired
    private AccLinkageService accLinkageService;
    @Autowired
    private AccAntiPassbackService accAntiPassbackService;
    @Autowired
    private AccFirstOpenService accFirstOpenService;
    @Autowired
    private AccCombOpenPersonService accCombOpenPersonService;
    @Autowired
    private AccCombOpenDoorService accCombOpenDoorService;
    @Autowired
    private AccCombOpenCombService accCombOpenCombService;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccLinkageInOutService accLinkageInOutService;

    /** 批量获取条数 */
    public static final int splitSize = 10000;

    /** 限制条数 */
    public static final int limitSize = 500000;

    /**
     * 数据迁移处理
     *
     * @param process 迁移进度信息类
     * @return
     */
    @Override
    public boolean handlerTransfer(SystemDataTransferProcess process) {
        long beginTime = System.currentTimeMillis();
        int beginProgress = process.getBeginProgress();
        int endProgress = process.getEndProgress();
        // 共有24张表需要迁移
        int eachProgess = (endProgress - beginProgress) / 18;

        String transferProgressMsg = "Start Acc Data Transfer......";
        // 夏令时迁移
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccDstime Data Transfer "));
        accDstimeHander();
        progressCache.setProcess(
            ProcessBean.createNormalSingleProcess(beginProgress + eachProgess, "AccDstime Data Transfer End"));

        // 迁移时间段数据
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccTimeSeg Data Transfer"));
        accTimeSegHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccTimeSeg Data Transfer End"));

        // 迁移门禁设备数据 要注意adms也需要迁移
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccDevice Data Transfer "));
        accDeviceHander();
        progressCache.setProcess(
            ProcessBean.createNormalSingleProcess(beginProgress + eachProgess, "AccDevice Data Transfer End"));

        // 门禁参数option
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccDeviceOption Data Transfer "));
        accDeviceOptionHander();
        progressCache.setProcess(
            ProcessBean.createNormalSingleProcess(beginProgress + eachProgess, "AccDeviceOption Data Transfer End "));
        // 门
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccDoor Data Transfer "));
        accDoorHander();
        progressCache.setProcess(
            ProcessBean.createNormalSingleProcess(beginProgress + eachProgess, "AccDoor Data Transfer End"));

        // 辅助输入
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccAuin Data Transfer "));
        accAuinHander();
        progressCache.setProcess(
            ProcessBean.createNormalSingleProcess(beginProgress + eachProgess, "AccAuin Data Transfer End"));

        // 辅助输出
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccAuoutSeg Data Transfer"));
        accAuoutHander();
        progressCache.setProcess(
            ProcessBean.createNormalSingleProcess(beginProgress + eachProgess, "AccAuoutSeg Data Transfer End"));

        // 读头
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccReader Data Transfer"));
        accReaderHander();
        // 更新韦根读头的状态
        accDeviceHander();
        progressCache.setProcess(
            ProcessBean.createNormalSingleProcess(beginProgress + eachProgess, "AccReader Data Transfer End"));

        // 门禁事件
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccDeviceEvent Data Transfer"));
        accDeviceEventHander();
        progressCache.setProcess(
            ProcessBean.createNormalSingleProcess(beginProgress + eachProgess, "AccDeviceEvent Data Transfer End"));

        // 门禁验证方式
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccDeviceVerify Data Transfer"));
        accDeviceVerifyModeHander();
        progressCache.setProcess(
            ProcessBean.createNormalSingleProcess(beginProgress + eachProgess, "AccDeviceVerify Data Transfer End"));

        // 门禁
        // 迁移门禁人员数据
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccPerson Data Transfer"));
        accPersonHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccPerson Data Transfer End"));

        // 迁移节假日数据
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccHoliday Data Transfer "));
        accHolidayHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccHoliday Data Transfer End"));

        // 迁移门禁权限组
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccLevel Data Transfer"));
        accLevelHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccLevel Data Transfer End"));

        // 迁移权限组-门
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccLevelDoor Data Transfer"));
        accLevelDoorHandler();
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccLevelDoor Data Transfer End"));

        // 迁移按权限组设置
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccLevelPerson Data Transfer"));
        accLevelPersonHandler();
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccLevelPerson Data Transfer End"));

        // 迁移按部门设置
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccLevelDept Data Transfer"));
        accLevelDeptHandler();
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccLevelDept Data Transfer End"));

        // 迁移互锁
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccTimeSeg Data Transfer
        // End"));
        // accInterlockHandler();
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccTimeSeg Data Transfer
        // End"));
        //
        // //迁移联动
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccTimeSeg Data Transfer
        // End"));
        // accLinkageHandler();
        //
        // //迁移联动输出点
        // accLinkageInoutHandler();
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccTimeSeg Data Transfer
        // End"));
        //
        // //迁移反潜
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccTimeSeg Data Transfer
        // End"));
        // accAntiPassbackHandler();
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccTimeSeg Data Transfer
        // End"));
        //
        // //首人常开
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccTimeSeg Data Transfer
        // End"));
        // accFirstOpenHandler();
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccTimeSeg Data Transfer
        // End"));
        //
        // //多人开门人员组
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccTimeSeg Data Transfer
        // End"));
        // accCombOpenPersonHandler();
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccTimeSeg Data Transfer
        // End"));
        //
        // //多人开门
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccTimeSeg Data Transfer
        // End"));
        // accCombOpenDoorHandler();
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccTimeSeg Data Transfer
        // End"));
        //
        // //多人开门中间表
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccTimeSeg Data Transfer
        // End"));
        // accCombOpenCombHandler();
        // progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccTimeSeg Data Transfer
        // End"));

        // 高级门禁

        // 报表
        // 迁移报表-全部数据
        // 判断是否需要迁移报表记录
        if (process.getIsMigrateTrans()) {
            progressCache
                .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccTransaction Data Transfer"));
            accTransactionHandler(beginProgress);
            progressCache
                .setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccTransaction Data Transfer End"));
        }

        // 迁移人员最后访问位置数据
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AccPersonLastAddr Data Transfer"));
        accPersonLastAddrHandler();
        progressCache
            .setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AccPersonLastAddr Data Transfer End"));
        // 迁移该模块总时长
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + eachProgess * 18 + 1,
            "Acc Module Data End Transfer Time taken:" + (System.currentTimeMillis() - beginTime) + "ms"));

        return true;
    }

    /**
     * 读头迁移
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午9:44:47
     */
    private void accReaderHander() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select dev.id as dev_id,dev.sn,d.door_no,ev.* from acc_reader ev left join acc_door d on d.id = ev.door_id left join acc_device dev on dev.id = d.dev_id");
        if (data != null) {
            List<AccReaderItem> items = new ArrayList<AccReaderItem>();
            AccReaderItem item = null;
            for (Map<String, Object> datum : data) {
                item = new AccReaderItem();
                item.setDeviceId(MapUtils.getString(datum, "dev_id"));
                item.setDeviceSn(MapUtils.getString(datum, "sn"));
                item.setId(MapUtils.getString(datum, "id"));
                item.setDoorNo(MapUtils.getString(datum, "door_no"));
                item.setDoorId(MapUtils.getString(datum, "door_id"));
                item.setReaderNo(MapUtils.getShort(datum, "reader_no"));
                item.setReaderState(MapUtils.getShort(datum, "reader_state"));
                item.setChannelId(MapUtils.getString(datum, "id"));
                item.setCommType(MapUtils.getShort(datum, "comm_type"));
                item.setCommAddress(MapUtils.getShort(datum, "comm_address"));
                item.setIp(MapUtils.getString(datum, "ip"));
                item.setPort(MapUtils.getShort(datum, "port"));
                item.setMac(MapUtils.getString(datum, "mac"));
                item.setMulticast(MapUtils.getString(datum, "multicast"));
                item.setName(MapUtils.getString(datum, "name"));
                item.setReaderEncrypt(("1").equals(MapUtils.getString(datum, "reader_encrypt")));
                items.add(item);
            }
            accReaderService.handlerTransfer(items);
        }
    }

    /**
     * 辅助输出
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午9:13:38
     */
    private void accAuoutHander() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select dev.sn,t.name as timeseg_name,ev.* from acc_auxout ev left join acc_device dev on dev.id = ev.dev_id left join acc_timeseg t on t.id = ev.timeseg_id");
        if (data != null) {
            List<AccAuxOutItem> items = new ArrayList<AccAuxOutItem>();
            AccAuxOutItem item = null;
            for (Map<String, Object> datum : data) {
                item = new AccAuxOutItem();
                item.setDevId(MapUtils.getString(datum, "dev_id"));
                item.setDevSn(MapUtils.getString(datum, "sn"));
                item.setId(MapUtils.getString(datum, "id"));
                item.setAccTimeSegId(MapUtils.getString(datum, "timeseg_id"));
                item.setAccTimeSegName(MapUtils.getString(datum, "timeseg_name"));
                item.setAuxNo(MapUtils.getShort(datum, "aux_no"));
                item.setPrinterNumber(MapUtils.getString(datum, "printer_number"));
                item.setName(MapUtils.getString(datum, "name"));
                item.setRemark(MapUtils.getString(datum, "remark"));
                items.add(item);
            }
            accAuxOutService.handlerTransfer(items);
        }

    }

    /**
     * 辅助输入
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午9:11:52
     */
    private void accAuinHander() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select dev.sn,t.name as timeseg_name,ev.* from acc_auxin ev left join acc_device dev on dev.id = ev.dev_id left join acc_timeseg t on t.id = ev.timeseg_id");
        if (data != null) {
            List<AccAuxInItem> items = new ArrayList<AccAuxInItem>();
            AccAuxInItem item = null;
            for (Map<String, Object> datum : data) {
                item = new AccAuxInItem();
                item.setDevId(MapUtils.getString(datum, "dev_id"));
                item.setDevSn(MapUtils.getString(datum, "sn"));
                item.setId(MapUtils.getString(datum, "id"));
                item.setAccTimeSegId(MapUtils.getString(datum, "timeseg_id"));
                item.setAccTimeSeg(MapUtils.getString(datum, "timeseg_name"));
                item.setAuxNo(MapUtils.getShort(datum, "aux_no"));
                item.setPrinterNumber(MapUtils.getString(datum, "printer_number"));
                item.setName(MapUtils.getString(datum, "name"));
                item.setRemark(MapUtils.getString(datum, "remark"));
                items.add(item);
            }
            accAuxInService.handlerTransfer(items);
        }

    }

    /**
     * 门迁移
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月13日 下午6:50:14
     */
    private void accDoorHander() {
        List<Map<String, Object>> data = systemDataMigrationService
            .excuteSql("select dev.sn,ev.* from acc_door ev left join acc_device dev on dev.id = ev.dev_id ");
        if (data != null) {
            List<AccDoorItem> items = new ArrayList<AccDoorItem>();
            AccDoorItem item = null;
            for (Map<String, Object> datum : data) {
                item = new AccDoorItem();
                item.setDeviceId(MapUtils.getString(datum, "dev_id"));
                item.setDeviceSn(MapUtils.getString(datum, "sn"));
                item.setId(MapUtils.getString(datum, "id"));
                item.setActiveTimeSegId(MapUtils.getString(datum, "active_timeseg_id"));
                item.setPassModeTimeSegId(MapUtils.getString(datum, "passmode_timeseg_id"));
                item.setWgInputFmtId(MapUtils.getString(datum, "wg_input_id"));
                item.setWgInputType(MapUtils.getShort(datum, "wg_input_type"));
                item.setWgOutputFmtId(MapUtils.getString(datum, "wg_output_id"));
                item.setWgOutputType(MapUtils.getShort(datum, "wg_output_type"));
                item.setDoorNo(MapUtils.getShort(datum, "door_no"));
                item.setName(MapUtils.getString(datum, "name"));
                item.setLockDelay(MapUtils.getShort(datum, "lock_delay"));
                item.setActionInterval(MapUtils.getShort(datum, "action_interval"));
                item.setDoorSensorStatus(MapUtils.getShort(datum, "door_sensor_status"));
                item.setSensorDelay(MapUtils.getShort(datum, "sensor_delay"));
                item.setBackLock(MapUtils.getString(datum, "back_lock").equals("1"));
                item.setVerifyMode(MapUtils.getShort(datum, "verify_mode"));
                item.setForcePwd(MapUtils.getString(datum, "force_pwd"));
                item.setSupperPwd(MapUtils.getString(datum, "supper_pwd"));
                item.setInApbDuration(MapUtils.getShort(datum, "in_apb_duration"));
                item.setLatchDoorType(MapUtils.getShort(datum, "latch_door_type"));
                item.setLatchTimeSegId(MapUtils.getString(datum, "latch_timeseg_id"));
                item.setLatchTimeOut(MapUtils.getShort(datum, "latch_time_out"));
                item.setReaderType(MapUtils.getShort(datum, "reader_type"));
                item.setHostStatus(MapUtils.getShort(datum, "host_status"));
                item.setDelayOpenTime(MapUtils.getShort(datum, "delay_open_time"));
                item.setExtDelayDrivertime(MapUtils.getShort(datum, "ext_delay_drivertime"));
                item.setIsDisableAudio(MapUtils.getString(datum, "is_disable_audio").equals("1"));
                item.setEnabled(MapUtils.getString(datum, "enabled").equals("1"));
                item.setWgReversed(MapUtils.getShort(datum, "wg_reversed"));
                item.setCombOpenInterval(MapUtils.getShort(datum, "combopen_interval"));

                items.add(item);
            }
            accDoorServce.handlerTransfer(items);
        }
    }

    /**
     * 门禁验证方式
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月13日 上午8:50:58
     */
    private void accDeviceVerifyModeHander() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select dev.sn,ev.* from acc_device_verifymode ev left join acc_device dev on dev.id = ev.dev_id");
        if (data != null) {
            List<AccDeviceVerifyModeItem> items = new ArrayList<AccDeviceVerifyModeItem>();
            AccDeviceVerifyModeItem item = null;
            for (Map<String, Object> datum : data) {
                item = new AccDeviceVerifyModeItem();
                item.setDeviceId(MapUtils.getString(datum, "dev_id"));
                item.setDeviceSn(MapUtils.getString(datum, "sn"));
                item.setId(MapUtils.getString(datum, "id"));
                item.setVerifyNo(MapUtils.getShort(datum, "verify_no"));
                item.setName(MapUtils.getString(datum, "name"));
                items.add(item);
            }
            accDeviceVerifyModeService.handlerTransfer(items);
        }

    }

    /**
     * 门禁事件
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月13日 上午8:50:45
     */
    private void accDeviceEventHander() {
        List<Map<String, Object>> data = systemDataMigrationService
            .excuteSql("select dev.sn,ev.* from acc_device_event ev left join acc_device dev on dev.id = ev.dev_id");
        if (data != null) {
            List<AccDeviceEventItem> items = new ArrayList<AccDeviceEventItem>();
            AccDeviceEventItem item = null;
            for (Map<String, Object> datum : data) {
                item = new AccDeviceEventItem();
                item.setDevId(MapUtils.getString(datum, "dev_id"));
                item.setDevSn(MapUtils.getString(datum, "sn"));
                item.setId(MapUtils.getString(datum, "id"));
                item.setName(MapUtils.getString(datum, "name"));
                item.setEventNo(MapUtils.getShort(datum, "event_no"));
                item.setEventLevel(MapUtils.getShort(datum, "event_level"));
                // item.setBaseMediaFileId(MapUtils.getString(datum, "media_file_id"));
                items.add(item);
            }
            accDeviceEventService.handlerTransfer(items);
        }
    }

    /**
     * @Description: 设备参数
     * <AUTHOR>
     * @since 2018年12月12日 下午5:49:39
     */
    private void accDeviceOptionHander() {
        List<Map<String, Object>> data = systemDataMigrationService
            .excuteSql("select dev.sn,op.* from acc_device_option op left join acc_device dev on dev.id = op.dev_id");
        if (data != null) {
            List<AccDeviceOptionItem> items = new ArrayList<AccDeviceOptionItem>();
            AccDeviceOptionItem item = null;
            for (Map<String, Object> datum : data) {
                item = new AccDeviceOptionItem();
                item.setDeviceId(MapUtils.getString(datum, "dev_id"));
                item.setDeviceSn(MapUtils.getString(datum, "sn"));
                item.setId(MapUtils.getString(datum, "id"));
                item.setName(MapUtils.getString(datum, "option_name"));
                item.setValue(MapUtils.getString(datum, "option_value"));
                item.setType(MapUtils.getShort(datum, "option_type"));
                items.add(item);
            }
            accDeviceOptionService.handlerTransfer(items);
        }
    }

    /**
     * 迁移夏令时
     */
    private void accDstimeHander() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from acc_dstime");
        if (data != null) {
            List<AccDSTimeItem> items = new ArrayList<AccDSTimeItem>();
            AccDSTimeItem item = null;
            for (Map<String, Object> datum : data) {
                item = new AccDSTimeItem();
                item.setDstimeMode(MapUtils.getShort(datum, "dstime_mode"));
                item.setEndTime(MapUtils.getString(datum, "end_time"));
                item.setName(MapUtils.getString(datum, "name"));
                item.setId(MapUtils.getString(datum, "id"));
                String initCode = MapUtils.getString(datum, "init_code");
                item.setInitFlag(false);
                if (StringUtils.isNotBlank(initCode) && AccDSTimeItem.INIT_CODE_US_DSTIME.equals(initCode)) {
                    item.setInitFlag(true);
                }
                item.setStartTime(MapUtils.getString(datum, "start_time"));
                items.add(item);
            }
            accDSTimeService.handlerTransfer(items);
        }
    }

    /**
     * 门禁设备迁移
     */
    private void accDeviceHander() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select  arr.name readerName, op.option_name,op.option_value,dst.name as dstime_name,area.code as area_code ,dev.* from acc_device dev left join base_area area on area.id= dev.area_id left join acc_reader arr on arr.id =dev.wg_reader_id left join acc_dstime dst on dst.id = dev.dstime_id left join acc_device_option op on op.dev_id = dev.id and op.option_name = 'NetConnectMode'");
        if (data != null) {
            List<AccDeviceItem> items = new ArrayList<AccDeviceItem>();
            AccDeviceItem item = null;
            Map<String, String> netConnectModeValue = Maps.newHashMap();
            // 连接方式
            netConnectModeValue.put("0", I18nUtil.i18nCode("acc_dev_netModeWired"));
            netConnectModeValue.put("1", I18nUtil.i18nCode("acc_dev_netMode4G"));
            netConnectModeValue.put("2", I18nUtil.i18nCode("acc_dev_netModeWifi"));
            for (Map<String, Object> datum : data) {
                item = new AccDeviceItem();
                item.setId(MapUtils.getString(datum, "id"));
                item.setAccDSTimeName(MapUtils.getString(datum, "dstime_name"));
                item.setAcpanelType(MapUtils.getShort(datum, "acpanel_type"));
                item.setAlias(MapUtils.getString(datum, "dev_alias"));
                item.setAuthAreaCode(MapUtils.getString(datum, "area_code"));
                item.setBaudrate(MapUtils.getString(datum, "baudrate"));
                item.setComAddress(MapUtils.getString(datum, "com_address"));
                item.setCommPwd(MapUtils.getString(datum, "comm_pwd"));
                item.setCommType(MapUtils.getShort(datum, "comm_type"));
                item.setComPort(MapUtils.getString(datum, "com_port"));
                item.setDeviceName(MapUtils.getString(datum, "device_name"));
                // 默认是false 禁用 操作启用的时候不会去走判断是否在线或者离线方法直接根据enable状态返回
                item.setEnabled(MapUtils.getShort(datum, "enabled") == 1);
                item.setFourToTwo("1".equals(MapUtils.getString(datum, "four_to_two")));
                item.setFwVersion(MapUtils.getString(datum, "fw_version"));
                item.setGateway(MapUtils.getString(datum, "gateway"));
                item.setIconType(MapUtils.getShort(datum, "icon_type"));
                item.setAccDSTimeId(MapUtils.getString(datum, "dstime_id"));
                item.setIpAddress(MapUtils.getString(datum, "ip_address"));
                item.setIpPort(MapUtils.getInteger(datum, "ip_port"));
                item.setIsRegistrationDevice(MapUtils.getShort(datum, "is_registrationdevice") == 1 ? true : false);
                item.setMachineType(MapUtils.getShort(datum, "machine_type"));
                item.setNetConnectMode(netConnectModeValue.get(MapUtils.getString(datum, "option_value")));
                item.setParentDeviceId(MapUtils.getString(datum, "parent_id"));
                item.setSn(MapUtils.getString(datum, "sn"));
                item.setSubnetMask(MapUtils.getString(datum, "subnet_mask"));
                item.setTimeZone(MapUtils.getString(datum, "time_zone"));
                item.setWgReaderId(MapUtils.getString(datum, "wg_reader_id"));
                item.setReaderName(MapUtils.getString(datum, "readerName"));
                items.add(item);
            }
            accDeviceService.handlerTransfer(items);
        }
    }

    /**
     * @Description: 门禁人员迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accPersonHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select pp.pin person_pin ,acp.name combopen_name,ap.* from acc_person ap left join pers_person pp on pp.id=ap.id left join acc_combopen_person acp on acp.id=ap.combopen_person_id where pp.person_type!=1");
        if (data != null) {
            List<AccPersonItem> items = new ArrayList<>();
            AccPersonItem item = null;
            for (Map<String, Object> itemDatum : data) {
                item = new AccPersonItem();
                item.setPersonPin(MapUtils.getString(itemDatum, "person_pin"));
                item.setAccCombOpenName(MapUtils.getString(itemDatum, "combopen_name"));
                item.setIsSetValidTime(MapUtils.getShort(itemDatum, "is_set_valid_time") != 0);
                item.setDisabled(MapUtils.getShort(itemDatum, "disabled") != 0);
                item.setSuperAuth(MapUtils.getShort(itemDatum, "super_auth") != null
                    ? MapUtils.getShort(itemDatum, "super_auth") : (short)0);
                item.setPrivilege(MapUtils.getShort(itemDatum, "privilege") != null
                    ? MapUtils.getShort(itemDatum, "privilege") : (short)0);
                item.setDelayPassage(MapUtils.getShort(itemDatum, "delay_passage") != 0);
                // startDate
                String startDate = MapUtils.getString(itemDatum, "start_time");
                String endDate = MapUtils.getString(itemDatum, "end_time");
                if (startDate != null) {
                    item.setStartTime(DateUtil.stringToDate(startDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                }
                if (endDate != null) {
                    item.setEndTime(DateUtil.stringToDate(endDate, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                }
                items.add(item);
            }
            accPersonService.handlerTransfer(items);
        }
    }

    /**
     * @Description: 门禁-时间段迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accTimeSegHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from acc_timeseg");
        if (data != null) {
            List<AccTimeSegItem> accTimeSegItems = new ArrayList<>();
            AccTimeSegItem accTimeSegItem = null;
            for (Map<String, Object> accTimeSegItemDatum : data) {
                accTimeSegItem = new AccTimeSegItem();
                accTimeSegItem.setId(MapUtils.getString(accTimeSegItemDatum, "id"));
                accTimeSegItem.setName(MapUtils.getString(accTimeSegItemDatum, "name"));
                accTimeSegItem.setRemark(MapUtils.getString(accTimeSegItemDatum, "remark"));
                accTimeSegItem
                    .setInitFlag(StringUtils.isNotBlank(MapUtils.getString(accTimeSegItemDatum, "init_code")));
                accTimeSegItem.setSundayStart1(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_start1")));
                accTimeSegItem.setSaturdayEnd1(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_end1")));
                accTimeSegItem.setSundayStart2(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_start2")));
                accTimeSegItem.setSundayEnd2(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_end2")));
                accTimeSegItem.setSundayStart3(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_start3")));
                accTimeSegItem.setSundayEnd3(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_end3")));
                accTimeSegItem.setMondayStart1(timeTab(MapUtils.getString(accTimeSegItemDatum, "monday_start1")));
                accTimeSegItem.setMondayEnd1(timeTab(MapUtils.getString(accTimeSegItemDatum, "monday_end1")));
                accTimeSegItem.setMondayStart2(timeTab(MapUtils.getString(accTimeSegItemDatum, "monday_start2")));
                accTimeSegItem.setMondayEnd2(timeTab(MapUtils.getString(accTimeSegItemDatum, "monday_end2")));
                accTimeSegItem.setMondayStart3(timeTab(MapUtils.getString(accTimeSegItemDatum, "monday_start3")));
                accTimeSegItem.setMondayEnd3(timeTab(MapUtils.getString(accTimeSegItemDatum, "monday_end3")));
                accTimeSegItem.setTuesdayStart1(timeTab(MapUtils.getString(accTimeSegItemDatum, "tuesday_start1")));
                accTimeSegItem.setTuesdayEnd1(timeTab(MapUtils.getString(accTimeSegItemDatum, "tuesday_end1")));
                accTimeSegItem.setTuesdayStart2(timeTab(MapUtils.getString(accTimeSegItemDatum, "tuesday_start2")));
                accTimeSegItem.setTuesdayEnd2(timeTab(MapUtils.getString(accTimeSegItemDatum, "tuesday_end2")));
                accTimeSegItem.setTuesdayStart3(timeTab(MapUtils.getString(accTimeSegItemDatum, "tuesday_start3")));
                accTimeSegItem.setTuesdayEnd3(timeTab(MapUtils.getString(accTimeSegItemDatum, "tuesday_end3")));
                accTimeSegItem.setWednesdayStart1(timeTab(MapUtils.getString(accTimeSegItemDatum, "wednesday_start1")));
                accTimeSegItem.setWednesdayEnd1(timeTab(MapUtils.getString(accTimeSegItemDatum, "wednesday_end1")));
                accTimeSegItem.setWednesdayStart2(timeTab(MapUtils.getString(accTimeSegItemDatum, "wednesday_start2")));
                accTimeSegItem.setWednesdayEnd2(timeTab(MapUtils.getString(accTimeSegItemDatum, "wednesday_end2")));
                accTimeSegItem.setWednesdayStart3(timeTab(MapUtils.getString(accTimeSegItemDatum, "wednesday_start3")));
                accTimeSegItem.setWednesdayEnd3(timeTab(MapUtils.getString(accTimeSegItemDatum, "wednesday_end3")));
                accTimeSegItem.setThursdayStart1(timeTab(MapUtils.getString(accTimeSegItemDatum, "thursday_start1")));
                accTimeSegItem.setThursdayEnd1(timeTab(MapUtils.getString(accTimeSegItemDatum, "thursday_end1")));
                accTimeSegItem.setThursdayStart2(timeTab(MapUtils.getString(accTimeSegItemDatum, "thursday_start2")));
                accTimeSegItem.setThursdayEnd2(timeTab(MapUtils.getString(accTimeSegItemDatum, "thursday_end2")));
                accTimeSegItem.setThursdayStart3(timeTab(MapUtils.getString(accTimeSegItemDatum, "thursday_start3")));
                accTimeSegItem.setThursdayEnd3(timeTab(MapUtils.getString(accTimeSegItemDatum, "thursday_end3")));
                accTimeSegItem.setFridayStart1(timeTab(MapUtils.getString(accTimeSegItemDatum, "friday_start1")));
                accTimeSegItem.setFridayEnd1(timeTab(MapUtils.getString(accTimeSegItemDatum, "friday_end1")));
                accTimeSegItem.setFridayStart2(timeTab(MapUtils.getString(accTimeSegItemDatum, "friday_start2")));
                accTimeSegItem.setFridayEnd2(timeTab(MapUtils.getString(accTimeSegItemDatum, "friday_end2")));
                accTimeSegItem.setFridayStart3(timeTab(MapUtils.getString(accTimeSegItemDatum, "friday_start3")));
                accTimeSegItem.setFridayEnd3(timeTab(MapUtils.getString(accTimeSegItemDatum, "friday_end3")));
                accTimeSegItem.setSaturdayStart1(timeTab(MapUtils.getString(accTimeSegItemDatum, "saturday_start1")));
                accTimeSegItem.setSaturdayEnd1(timeTab(MapUtils.getString(accTimeSegItemDatum, "saturday_end1")));
                accTimeSegItem.setSaturdayStart2(timeTab(MapUtils.getString(accTimeSegItemDatum, "saturday_start2")));
                accTimeSegItem.setSaturdayEnd2(timeTab(MapUtils.getString(accTimeSegItemDatum, "saturday_end2")));
                accTimeSegItem.setSaturdayStart3(timeTab(MapUtils.getString(accTimeSegItemDatum, "saturday_start3")));
                accTimeSegItem.setSaturdayEnd3(timeTab(MapUtils.getString(accTimeSegItemDatum, "saturday_end3")));
                accTimeSegItem.setSundayStart1(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_start1")));
                accTimeSegItem.setSundayEnd1(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_end1")));
                accTimeSegItem.setSundayStart2(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_start2")));
                accTimeSegItem.setSundayEnd2(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_end2")));
                accTimeSegItem.setSundayStart3(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_start3")));
                accTimeSegItem.setSundayEnd3(timeTab(MapUtils.getString(accTimeSegItemDatum, "sunday_end3")));
                accTimeSegItem
                    .setHolidayType1Start1(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype1_start1")));
                accTimeSegItem
                    .setHolidayType1End1(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype1_end1")));
                accTimeSegItem
                    .setHolidayType1Start2(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype1_start2")));
                accTimeSegItem
                    .setHolidayType1End2(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype1_end2")));
                accTimeSegItem
                    .setHolidayType1Start3(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype1_start3")));
                accTimeSegItem
                    .setHolidayType1End3(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype1_end3")));
                accTimeSegItem
                    .setHolidayType2Start1(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype2_start1")));
                accTimeSegItem
                    .setHolidayType2End1(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype2_end1")));
                accTimeSegItem
                    .setHolidayType2Start2(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype2_start2")));
                accTimeSegItem
                    .setHolidayType2End2(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype2_end2")));
                accTimeSegItem
                    .setHolidayType2Start3(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype2_start3")));
                accTimeSegItem
                    .setHolidayType2End3(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype2_end3")));
                accTimeSegItem
                    .setHolidayType3Start1(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype3_start1")));
                accTimeSegItem
                    .setHolidayType3End1(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype3_end1")));
                accTimeSegItem
                    .setHolidayType3Start2(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype3_start2")));
                accTimeSegItem
                    .setHolidayType3End2(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype3_end2")));
                accTimeSegItem
                    .setHolidayType3Start3(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype3_start3")));
                accTimeSegItem
                    .setHolidayType3End3(timeTab(MapUtils.getString(accTimeSegItemDatum, "holidaytype3_end3")));
                accTimeSegItems.add(accTimeSegItem);
            }
            accTimeSegService.handlerTransfer(accTimeSegItems);
        }
    }

    /**
     * @Description: 门禁-节假日迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accHolidayHandler() {

        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from acc_holiday");
        if (data != null) {
            List<AccHolidayItem> accHolidayItems = new ArrayList<>();
            AccHolidayItem accHolidayItem = null;
            for (Map<String, Object> accHolidayDatum : data) {
                accHolidayItem = new AccHolidayItem();
                accHolidayItem.setId(MapUtils.getString(accHolidayDatum, "id"));
                accHolidayItem.setName(MapUtils.getString(accHolidayDatum, "name"));
                accHolidayItem.setHolidayType(MapUtils.getShort(accHolidayDatum, "holiday_type"));
                accHolidayItem.setStartDate(DateUtil.stringToDate(MapUtils.getString(accHolidayDatum, "start_date"),
                    DateUtil.DateStyle.YYYY_MM_DD));
                accHolidayItem.setEndDate(DateUtil.stringToDate(MapUtils.getString(accHolidayDatum, "end_date"),
                    DateUtil.DateStyle.YYYY_MM_DD));
                accHolidayItem.setIsLoopByYear(!"0".equals(MapUtils.getString(accHolidayDatum, "is_loop_by_year")));
                accHolidayItem.setRemark(MapUtils.getString(accHolidayDatum, "remark"));
                accHolidayItems.add(accHolidayItem);
            }
            accHolidayService.handlerTransfer(accHolidayItems);
        }
    }

    /**
     * @Description: 门禁权限组迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accLevelHandler() {

        List<Map<String, Object>> data = systemDataMigrationService
            .excuteSql("select ba.code,al.* from acc_level  al left join base_area ba on ba.id=al.area_id");
        if (data != null) {
            List<AccLevelItem> accLevelItems = new ArrayList<>();
            AccLevelItem accLevelItem = null;
            for (Map<String, Object> accLevelDatum : data) {
                accLevelItem = new AccLevelItem();
                // 参数
                accLevelItem.setId(MapUtils.getString(accLevelDatum, "id"));
                accLevelItem.setTimeSegId(MapUtils.getString(accLevelDatum, "timeseg_id"));
                accLevelItem.setName(MapUtils.getString(accLevelDatum, "name"));
                accLevelItem.setInitFlag(StringUtils.isNotBlank(MapUtils.getString(accLevelDatum, "init_code")));
                accLevelItem.setAuthAreaId(MapUtils.getString(accLevelDatum, "code"));
                accLevelItems.add(accLevelItem);
            }
            accLevelService.handlerTransfer(accLevelItems);
        }
    }

    /**
     * @Description: 门禁权限组-添加门迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accLevelDoorHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select al.name level_name,adr.door_no,ad.sn from acc_level_door ald left join acc_level al on al. id = ald.level_id left join acc_door adr on adr. id = ald.door_id left join acc_device ad on ad.id =adr.dev_id");
        if (data != null) {
            List<AccLevelDoorItem> accLevelDoorItems = new ArrayList<>();
            AccLevelDoorItem accLevelDoorItem = null;
            for (Map<String, Object> accLevelDoorDatum : data) {
                accLevelDoorItem = new AccLevelDoorItem();
                // 参数
                accLevelDoorItem.setAccLevelName(MapUtils.getString(accLevelDoorDatum, "level_name"));
                accLevelDoorItem.setDoorNo(MapUtils.getString(accLevelDoorDatum, "door_no"));
                accLevelDoorItem.setDeviceSn(MapUtils.getString(accLevelDoorDatum, "sn"));
                accLevelDoorItems.add(accLevelDoorItem);
            }
            accLevelService.handlerTransferToDoor(accLevelDoorItems);
        }

    }

    /**
     * @Description: 按权限组设置迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accLevelPersonHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select pp.pin,al.name level_name from acc_level_person alp left join acc_level al on alp.level_id = al.id left join pers_person pp on alp.person_id = pp.id  where pp.person_type!=1");
        log.info("------总的--" + data.size());
        if (data != null) {
            List<AccPersonLevelItem> accPersonLevelItems = new ArrayList<>();
            AccPersonLevelItem accPersonLevelItem = null;
            for (Map<String, Object> accPersonLeveDatum : data) {
                accPersonLevelItem = new AccPersonLevelItem();
                // 参数
                accPersonLevelItem.setPin(MapUtils.getString(accPersonLeveDatum, "pin"));
                accPersonLevelItem.setLevelName(MapUtils.getString(accPersonLeveDatum, "level_name"));
                accPersonLevelItems.add(accPersonLevelItem);
            }
            accLevelService.handlerTransferToPerson(accPersonLevelItems);
        }
    }

    /**
     * @Description: 按部门设置迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accLevelDeptHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select al.name level_name,pd.code dept_code from acc_level_dept ald left join acc_level al on ald.acclevel_id = al. id left join pers_department pd on pd.id=ald.dept_id");
        if (data != null) {
            List<AccDeptLevelItem> accDeptLevelItems = new ArrayList<>();
            AccDeptLevelItem accDeptLevelItem = null;
            for (Map<String, Object> accPersonDeptDatum : data) {
                accDeptLevelItem = new AccDeptLevelItem();
                // 参数
                accDeptLevelItem.setLevelName(MapUtils.getString(accPersonDeptDatum, "level_name"));
                accDeptLevelItem.setDeptCode(MapUtils.getString(accPersonDeptDatum, "dept_code"));
                accDeptLevelItems.add(accDeptLevelItem);
            }
            accLevelService.handlerTransferToDept(accDeptLevelItems);
        }
    }

    /**
     * @Description: 互锁迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accInterlockHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from acc_interlock");
        if (data != null) {
            List<AccInterlockItem> accInterlockItems = new ArrayList<>();
            AccInterlockItem accInterlockItem = null;
            for (Map<String, Object> accInterlockDatum : data) {
                accInterlockItem = new AccInterlockItem();
                accInterlockItem.setId(MapUtils.getString(accInterlockDatum, "id"));
                accInterlockItem.setDeviceId(MapUtils.getString(accInterlockDatum, "dev_id"));
                accInterlockItem.setInterlockRule(MapUtils.getShort(accInterlockDatum, "interlock_rule"));
                accInterlockItems.add(accInterlockItem);
            }
            accInterlockService.handlerTransfer(accInterlockItems);
        }

    }

    /**
     * @Description: 联动迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accLinkageHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from acc_linkage");
        if (data != null) {
            List<AccLinkageItem> accLinkageItems = new ArrayList<>();
            AccLinkageItem accLinkageItem = null;
            for (Map<String, Object> accLinkageDatum : data) {
                accLinkageItem = new AccLinkageItem();
                accLinkageItem.setId(MapUtils.getString(accLinkageDatum, "id"));
                accLinkageItem.setDeviceId(MapUtils.getString(accLinkageDatum, "dev_id"));
                accLinkageItem.setName(MapUtils.getString(accLinkageDatum, "name"));
                accLinkageItems.add(accLinkageItem);
            }
            accLinkageService.handlerTransfer(accLinkageItems);
        }
    }

    /**
     * @Description: 联动输出点表迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accLinkageInoutHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select  al.name linkage_name,ali.* from acc_linkage_inout ali left join acc_linkage al on al.id=ali.linkage_id ");
        if (data != null) {
            List<AccLinkageInOutItem> accCombOpenPersonItems = new ArrayList<>();
            AccLinkageInOutItem item = null;
            for (Map<String, Object> itemDatum : data) {
                item = new AccLinkageInOutItem();
                item.setId(MapUtils.getString(itemDatum, "id"));
                item.setLinkageName(MapUtils.getString(itemDatum, "linkage_name"));
                item.setInputType(MapUtils.getString(itemDatum, "input_type"));
                item.setInputId(MapUtils.getString(itemDatum, "input_id"));
                item.setOutputType(MapUtils.getString(itemDatum, "output_type"));
                item.setOutputId(MapUtils.getString(itemDatum, "output_id"));
                item.setActionType(MapUtils.getShort(itemDatum, "action_type"));
                item.setActionTime(MapUtils.getShort(itemDatum, "action_time"));
                accCombOpenPersonItems.add(item);
            }
            accLinkageInOutService.handlerTransfer(accCombOpenPersonItems);
        }
    }

    /**
     * @Description: 反潜数据迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accAntiPassbackHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from acc_antipassback");
        if (data != null) {
            List<AccAntiPassbackItem> accAntiPassbackItems = new ArrayList<>();
            AccAntiPassbackItem accAntiPassbackItem = null;
            for (Map<String, Object> accTransactionDatum : data) {
                accAntiPassbackItem = new AccAntiPassbackItem();
                accAntiPassbackItem.setDeviceId(MapUtils.getString(accTransactionDatum, "dev_id"));
                accAntiPassbackItem.setApbRule(MapUtils.getShort(accTransactionDatum, "apb_rule"));
                accAntiPassbackItems.add(accAntiPassbackItem);
            }
            accAntiPassbackService.handlerTransfer(accAntiPassbackItems);
        }
    }

    /**
     * @Description: 首人常开数据迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accFirstOpenHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select adr.door_no, ad.sn,af.timeseg_id from acc_firstopen af LEFT JOIN acc_door adr ON adr. ID = af.door_id LEFT JOIN acc_device ad ON ad.id=adr.dev_id");
        if (data != null) {
            List<AccFirstOpenItem> accFirstOpenItems = new ArrayList<>();
            AccFirstOpenItem accFirstOpenItem = null;
            for (Map<String, Object> accFirstOpenDatum : data) {
                accFirstOpenItem = new AccFirstOpenItem();
                accFirstOpenItem.setDoorNo(MapUtils.getString(accFirstOpenDatum, "door_no"));
                accFirstOpenItem.setDeviceSn(MapUtils.getString(accFirstOpenDatum, "sn"));
                accFirstOpenItem.setTimeSegId(MapUtils.getString(accFirstOpenDatum, "timeseg_id"));
                accFirstOpenItems.add(accFirstOpenItem);
            }
            accFirstOpenService.handlerTransfer(accFirstOpenItems);
        }

    }

    /**
     * @Description: 多人开门人员组数据迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accCombOpenPersonHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from acc_combopen_person");
        if (data != null) {
            List<AccCombOpenPersonItem> accCombOpenPersonItems = new ArrayList<>();
            AccCombOpenPersonItem item = null;
            for (Map<String, Object> itemDatum : data) {
                item = new AccCombOpenPersonItem();
                item.setId(MapUtils.getString(itemDatum, "id"));
                item.setName(MapUtils.getString(itemDatum, "name"));
                item.setRemark(MapUtils.getString(itemDatum, "remark"));
                // 人员数量
                // item.setPersonCount(MapUtils.getString(itemDatum, "id"));
                accCombOpenPersonItems.add(item);
            }
            accCombOpenPersonService.handlerTransfer(accCombOpenPersonItems);
        }

    }

    /**
     * @Description: 多人开门迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accCombOpenDoorHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(
            "select a.door_no,d.sn,t.*  from acc_combopen_door t left join acc_door a on t.door_id=a.id left join acc_device d on a.dev_id=d.id ");
        if (data != null) {
            List<AccCombOpenDoorItem> accCombOpenDoorItems = new ArrayList<>();
            AccCombOpenDoorItem item = null;
            for (Map<String, Object> itemDatum : data) {
                item = new AccCombOpenDoorItem();
                item.setDoorNo(MapUtils.getString(itemDatum, "door_no"));
                item.setDeviceSn(MapUtils.getString(itemDatum, "sn"));
                item.setId(MapUtils.getString(itemDatum, "id"));
                item.setName(MapUtils.getString(itemDatum, "name"));
                accCombOpenDoorItems.add(item);
            }
            accCombOpenDoorService.handlerTransfer(accCombOpenDoorItems);
        }

    }

    /**
     * @Description: 多人开门中间表迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accCombOpenCombHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from acc_combopen_comb");
        if (data != null) {
            List<AccCombOpenCombItem> accCombOpenCombItems = new ArrayList<>();
            AccCombOpenCombItem item = null;
            for (Map<String, Object> accCombOpenCombDatum : data) {
                item = new AccCombOpenCombItem();
                item.setId(MapUtils.getString(accCombOpenCombDatum, "id"));
                item.setAccCombOpenPersonId(MapUtils.getString(accCombOpenCombDatum, "combopen_person_id"));
                item.setAccCombOpenDoorId(MapUtils.getString(accCombOpenCombDatum, "combopen_door_id"));
                item.setOpenerNumber(MapUtils.getShort(accCombOpenCombDatum, "opener_number"));
                item.setSort(MapUtils.getShort(accCombOpenCombDatum, "sort"));
                accCombOpenCombItems.add(item);
            }
            accCombOpenCombService.handlerTransfer(accCombOpenCombItems);
        }
    }

    /**
     * @Description: 报表-全部记录迁移
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accTransactionHandler(Integer beginProgress) {
        // 分页sql考虑数据库兼容性问题
        // 对该表进行分页处理
        // 获取总数
        List<Map<String, Object>> countData =
            systemDataMigrationService.excuteSql("select  count(1) as count_number from acc_transaction");
        long insTransactionCount = Long.parseLong(String.valueOf(countData.get(0).get("count_number")));
        // 超出限制条数不迁移
        if (insTransactionCount < limitSize) {
            // 迁移的总数据量
            progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress,
                "The total number " + insTransactionCount + " pieces"));
            // 根据size的带下，根据计算，获取需要查询几次
            long count = insTransactionCount / splitSize;
            if (insTransactionCount % splitSize > 0) {
                count++;
            }
            int size = splitSize;
            List<Map<String, Object>> data = null;
            for (int page = 0; page < count; page++) {
                // 执行分页查询
                data = systemDataMigrationService.excutePageSql("acc_transaction", page * size, size);
                // 进度条显示剩余的数量
                insTransactionCount = insTransactionCount - data.size();
                progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress,
                    "Remaining " + insTransactionCount + " pieces"));
                if (data != null) {
                    List<AccTransactionItem> accTransactionItems = new ArrayList<>();
                    AccTransactionItem accTransactionItem = null;
                    for (Map<String, Object> accTransactionDatum : data) {
                        accTransactionItem = new AccTransactionItem();
                        // 参数
                        // accTransactionItem.setId(MapUtils.getString(accTransactionDatum, "id"));
                        accTransactionItem.setUniqueKey(MapUtils.getString(accTransactionDatum, "unique_key"));
                        accTransactionItem.setLogId(MapUtils.getInteger(accTransactionDatum, "log_id"));
                        accTransactionItem
                            .setEventTime(DateUtil.stringToDate(MapUtils.getString(accTransactionDatum, "event_time"),
                                DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                        accTransactionItem.setPin(MapUtils.getString(accTransactionDatum, "pin"));
                        accTransactionItem.setName(MapUtils.getString(accTransactionDatum, "name"));
                        accTransactionItem.setLastName(MapUtils.getString(accTransactionDatum, "last_name"));
                        accTransactionItem.setDeptName(MapUtils.getString(accTransactionDatum, "dept_name"));
                        accTransactionItem.setCardNo(MapUtils.getString(accTransactionDatum, "card_no"));
                        accTransactionItem.setAreaName(MapUtils.getString(accTransactionDatum, "area_name"));
                        accTransactionItem.setDevId(MapUtils.getString(accTransactionDatum, "dev_id"));
                        accTransactionItem.setDevSn(MapUtils.getString(accTransactionDatum, "dev_sn"));
                        accTransactionItem.setDevAlias(MapUtils.getString(accTransactionDatum, "dev_alias"));
                        accTransactionItem.setEventNo(MapUtils.getShort(accTransactionDatum, "event_no"));
                        accTransactionItem.setEventName(MapUtils.getString(accTransactionDatum, "event_name"));
                        accTransactionItem.setEventPointId(MapUtils.getString(accTransactionDatum, "event_point_id"));
                        accTransactionItem
                            .setEventPointName(MapUtils.getString(accTransactionDatum, "event_point_name"));
                        accTransactionItem
                            .setEventPointType(MapUtils.getShort(accTransactionDatum, "event_point_type"));
                        accTransactionItem.setReaderState(MapUtils.getShort(accTransactionDatum, "reader_state"));
                        accTransactionItem.setReaderName(MapUtils.getString(accTransactionDatum, "reader_name"));
                        accTransactionItem.setTriggerCond(MapUtils.getShort(accTransactionDatum, "trigger_cond"));
                        accTransactionItem.setDescription(MapUtils.getString(accTransactionDatum, "description"));
                        accTransactionItem.setVerifyModeNo(MapUtils.getShort(accTransactionDatum, "verify_mode_no"));
                        accTransactionItem
                            .setVerifyModeName(MapUtils.getString(accTransactionDatum, "verify_mode_name"));
                        accTransactionItem
                            .setVidLinkageHandle(MapUtils.getString(accTransactionDatum, "vid_linkage_handle"));
                        accTransactionItem.setAccZone(MapUtils.getString(accTransactionDatum, "acc_zone"));
                        accTransactionItem.setEventAddr(MapUtils.getShort(accTransactionDatum, "event_addr"));
                        accTransactionItems.add(accTransactionItem);
                    }
                    accTransactionService.handlerTransfer(accTransactionItems);

                    accTransactionItems = null;
                }
                data = null;
            }
        }

    }

    /**
     * @Description: 迁移人员最后访问位置数据
     * @Author: Abel.huang
     * @CreateDate: 2018/12/12 17:47
     * @Version: 1.0
     */
    private void accPersonLastAddrHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from acc_person_lastaddr");
        if (data != null) {
            List<AccPersonLastAddrItem> accPersonLastAddrItems = new ArrayList<>();
            AccPersonLastAddrItem accPersonLastAddrItem = null;
            for (Map<String, Object> accPersonLastAddrDatum : data) {
                accPersonLastAddrItem = new AccPersonLastAddrItem();
                // 参数
                accPersonLastAddrItem.setPin(MapUtils.getString(accPersonLastAddrDatum, "pin"));
                accPersonLastAddrItem.setEventTime(DateUtil.stringToDate(
                    MapUtils.getString(accPersonLastAddrDatum, "event_time"), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                accPersonLastAddrItem.setName(MapUtils.getString(accPersonLastAddrDatum, "name"));
                accPersonLastAddrItem.setLastName(MapUtils.getString(accPersonLastAddrDatum, "last_name"));
                accPersonLastAddrItem.setDeptName(MapUtils.getString(accPersonLastAddrDatum, "dept_name"));
                accPersonLastAddrItem.setAreaName(MapUtils.getString(accPersonLastAddrDatum, "area_name"));
                accPersonLastAddrItem.setCardNo(MapUtils.getString(accPersonLastAddrDatum, "card_no"));
                accPersonLastAddrItem.setDevId(MapUtils.getString(accPersonLastAddrDatum, "dev_id"));
                accPersonLastAddrItem.setDevSn(MapUtils.getString(accPersonLastAddrDatum, "dev_sn"));
                accPersonLastAddrItem.setDevAlias(MapUtils.getString(accPersonLastAddrDatum, "dev_alias"));
                accPersonLastAddrItem.setVerifyModeNo(MapUtils.getShort(accPersonLastAddrDatum, "verify_mode_no"));
                accPersonLastAddrItem.setVerifyModeName(MapUtils.getString(accPersonLastAddrDatum, "verify_mode_name"));
                accPersonLastAddrItem.setEventNo(MapUtils.getShort(accPersonLastAddrDatum, "event_no"));
                accPersonLastAddrItem.setEventPointType(MapUtils.getShort(accPersonLastAddrDatum, "event_point_type"));
                accPersonLastAddrItem.setEventName(MapUtils.getString(accPersonLastAddrDatum, "event_name"));
                accPersonLastAddrItem.setEventPointId(MapUtils.getString(accPersonLastAddrDatum, "event_point_id"));
                accPersonLastAddrItem.setEventPointName(MapUtils.getString(accPersonLastAddrDatum, "event_point_name"));
                accPersonLastAddrItem.setReaderState(MapUtils.getShort(accPersonLastAddrDatum, "reader_state"));
                accPersonLastAddrItem.setReaderName(MapUtils.getString(accPersonLastAddrDatum, "reader_name"));
                accPersonLastAddrItem.setTriggerCond(MapUtils.getShort(accPersonLastAddrDatum, "trigger_cond"));
                accPersonLastAddrItem.setDescription(MapUtils.getString(accPersonLastAddrDatum, "description"));
                accPersonLastAddrItem
                    .setVidLinkageHandle(MapUtils.getString(accPersonLastAddrDatum, "vid_linkage_handle"));
                accPersonLastAddrItem.setAccZone(MapUtils.getString(accPersonLastAddrDatum, "acc_zone"));
                accPersonLastAddrItems.add(accPersonLastAddrItem);
            }
            accPersonLastAddrService.handlerTransfer(accPersonLastAddrItems);
        }

    }

    /**
     * 时间格式转换（yyyy-MM-dd HH:mm:ss 转HH:mm:ss）
     * 
     * <AUTHOR>
     * @Date 2019/8/9 9:42
     * @param
     * @return
     */
    private String timeTab(String time) {
        String dateTime = "";
        if (time.length() == 8) {
            return time;
        } else {
            Date timeTab = DateUtil.stringToDate(time, DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
            if (timeTab != null) {
                dateTime = DateUtil.dateToString(timeTab, DateUtil.DateStyle.HH_MM_SS);
            }
        }
        return dateTime;
    }

    /**
     * 数据升级处理
     *
     * @param process 升级信息类
     */
    @Override
    public void handlerUpgrade(SystemDataUpgradeProcess process) {

    }
}
