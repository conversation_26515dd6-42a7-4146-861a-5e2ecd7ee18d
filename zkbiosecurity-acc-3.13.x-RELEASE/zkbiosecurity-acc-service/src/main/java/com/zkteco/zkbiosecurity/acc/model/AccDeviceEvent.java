/**
 * File Name: AccDeviceEvent Created by GenerationTools on 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccDeviceEvent
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午02:44
 * @version v1.0
 */
@Entity
@Table(name = "ACC_DEVICE_EVENT")
@Getter
@Setter
@Accessors(chain = true)
public class AccDeviceEvent extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @ManyToOne
    @JoinColumn(name = "DEV_ID")
    private AccDevice accDevice;

    /**  */
    @Column(name = "EVENT_NO", nullable = false)
    private Short eventNo;

    /**  */
    @Column(name = "NAME", length = 100, nullable = false)
    private String name;

    /**  */
    @Column(name = "EVENT_LEVEL", nullable = false)
    private Short eventLevel;

    /** 文件id */
    @Column(name = "BASE_MEDIA_FILE_ID")
    private String baseMediaFileId;

    /** pro 事件优先级 add by colin 2020-3-31 10:47:11 */
    @Column(name = "EVENT_PRIORITY")
    private Short eventPriority;

    public AccDeviceEvent() {}

    public AccDeviceEvent(Short eventNo, String name, AccDevice accDevice, Short eventLevel) {
        this.eventNo = eventNo;
        this.name = name;
        this.accDevice = accDevice;
        this.eventLevel = eventLevel;
    }

    public AccDeviceEvent(Short eventNo, String name) {
        this.eventNo = eventNo;
        this.name = name;
    }
}