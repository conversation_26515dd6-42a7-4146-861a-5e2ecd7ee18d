package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccLevelPerson;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 权限组-人员 中间表Dao
 * 
 * <AUTHOR>
 * @date 2018/3/22 17:59
 */
public interface AccLevelPersonDao extends BaseDao<AccLevelPerson, String> {

    @Modifying
    @Query(value = "delete from AccLevelPerson e where e.accLevel.id in (?1) and e.persPersonId in (?2)")
    void delBatchLevel(List<String> levelIds, List<String> personIds);

    @Modifying
    @Query(value = "delete from AccLevelPerson e where e.persPersonId in (?1)")
    void delLevelPersonByIds(List<String> personIds);

    @Query(value = "select e.accLevel.id from AccLevelPerson e where e.persPersonId = ?1")
    List<String> getLevelIdByPersonId(String id);

    @Query(value = "select e.accLevel.id from AccLevelPerson e where e.persPersonId in (?1)")
    List<String> getLevelIdByPersonIdIn(List<String> personIds);

    /**
     * 查询权限组下的人员Id
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/26 14:16
     * @return:
     */
    @Query(value = "select e.persPersonId from AccLevelPerson e where e.accLevel.id = ?1")
    List<String> getPersonIdByLevelId(String levelId);

    /*@Query(value = "select e.persPersonId, l.id, t.businessId from AccLevelPerson e left join e.accLevel l left join l" +
            ".accTimeSeg t where e.persPersonId in (?1)")
    List<Object[]> findByPersPersonIdIn(List<String> personIds);
    */
    /**
     * 查询权限组中的人员数量
     * 
     * @author: mingfa.zheng
     * @date: 2018/5/2 9:20
     * @return:
     */
    Long countByAccLevel_Id(String levelId);

    Long countByAccLevel_IdAndPersPersonIdIn(String levelId, Collection<String> personIds);

    List<AccLevelPerson> findByAccLevel_IdInAndPersPersonIdIn(List<String> levelIdList, List<String> personIds);

    /**
     * 根据权限id以及人员id查询出权限组人员对象
     * 
     * <AUTHOR>
     * @Date 2018/12/18 10:56
     * @param
     * @return
     */
    AccLevelPerson findByAccLevel_IdAndPersPersonId(String levelId, String personId);

    @Query(value = "select e.persPersonId from AccLevelPerson e where e.accLevel.id in (?1)")
    List<String> getPersonIdByLevelIdIn(List<String> levelIds);

    /**
     * 根据设备序列号获取权限组-人员信息
     *
     * @param devSn:设备序列号
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccLevelPerson>
     * <AUTHOR>
     * @date 2021-02-05 15:05
     * @since 1.0.0
     */
    @Query(
        value = "select distinct e from AccLevelPerson e join e.accLevel al join al.accDoorList adl join adl.accDoor ad join ad.device d where d.sn = ?1")
    List<AccLevelPerson> getLevelPersonByDevSn(String devSn);

    /**
     * 根据权限组ids找出权限组-人员信息
     * 
     * @param levelIds:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccLevelPerson>
     * <AUTHOR>
     * @throws
     * @date 2022-07-25 14:51
     * @since 1.0.0
     */
    List<AccLevelPerson> findByAccLevel_IdIn(List<String> levelIds);

    /**
     * 根据人员id集合和权限组id集合IN查询
     *
     * @param personIds:人员id集合
     * @param levelIds:权限组id集合
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022-05-16 14:07
     * @since 1.0.0
     */
    @Query(
        value = "select distinct alp.pers_person_id from acc_level_person alp where alp.pers_person_id in (?1) and alp.level_id in (?2)",
        nativeQuery = true)
    List<String> getPersonIdByPersonIdInAndLevelIdIn(List<String> personIds, List<String> levelIds);

    @Query(value = "select e from AccLevelPerson e where e.persPersonId = ?1")
    List<AccLevelPerson> getLevelTimesIdByPersonId(String id);

    List<AccLevelPerson> findByPersPersonIdIn(List<String> personIds);
}
