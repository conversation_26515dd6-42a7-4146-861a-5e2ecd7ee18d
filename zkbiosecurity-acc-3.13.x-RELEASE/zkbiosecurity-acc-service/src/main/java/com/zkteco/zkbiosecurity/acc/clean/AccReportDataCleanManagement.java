package com.zkteco.zkbiosecurity.acc.clean;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.task.AccReportDataCleanTask;
import com.zkteco.zkbiosecurity.system.service.DataCleanManager;

@Component
public class AccReportDataCleanManagement implements DataCleanManager {
    @Autowired
    private AccReportDataCleanTask accReportDataCleanTask;

    @Override
    public void handlerRuntime() {
        // 定时清理门禁报表数据等
        accReportDataCleanTask.initReportDataClean();
    }
}
