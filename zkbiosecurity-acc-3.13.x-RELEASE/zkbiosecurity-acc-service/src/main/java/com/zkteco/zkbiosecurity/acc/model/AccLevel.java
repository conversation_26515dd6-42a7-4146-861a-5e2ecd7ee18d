/**
 * File Name: AccLevel Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁权限组model
 *
 * <AUTHOR>
 * @date: 2018-03-02 下午02:15
 * @version v1.0
 */
@Entity
@Table(name = "ACC_LEVEL", indexes = {@Index(columnList = "TIMESEG_ID")})
@Getter
@Setter
@Accessors(chain = true)
public class AccLevel extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 系统区域 */
    @Column(name = "AUTH_AREA_ID")
    private String authAreaId;

    /**  */
    @Column(name = "TIMESEG_ID")
    private String timeSegId;

    /**  */
    @Column(name = "NAME", length = 100, nullable = true)
    private String name;

    /** 是否初始化标记 */
    @Column(name = "INIT_FLAG")
    private Boolean initFlag;

    @OneToMany(mappedBy = "accLevel")
    private List<AccLevelPerson> accPersonList = new ArrayList<>();

    @OneToMany(mappedBy = "accLevel")
    private List<AccLevelDoor> accDoorList = new ArrayList<>();

    @OneToMany(mappedBy = "accLevel")
    private List<AccLevelDept> accLevelDeptList = new ArrayList<>();

    // pro

    /** BusinessID 下发设备中使用 mqtt */
    @Column(name = "BUSINESS_ID")
    private Long businessId;

    /** 开始日期 mqtt */
    @Column(name = "START_DATE")
    private Date startDate;

    /** 结束日期 mqtt */
    @Column(name = "END_DATE")
    private Date endDate;
}