/**
 * File Name: AccDoorServiceImpl Created by GenerationTools on 2018-03-03 上午11:59 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.app.vo.AccAppTopDoorByPersonItem;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseClientService;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.license.vo.bean.ResultCode;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;
import com.zkteco.zkbiosecurity.system.app.service.BaseAuthCloudMessageSendService;

/**
 * 对应百傲瑞达 AccDoorServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-03 上午11:59
 * @version v1.0
 */
@Service
@Transactional
public class AccDoorServiceImpl implements AccDoorService {

    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccTimeSegDao accTimeSegDao;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDeviceVerifyModeService accDeviceVerifyModeService;
    @Autowired
    private PersWiegandFmtService persWiegandFmtService;
    @Autowired
    private AccAntiPassbackService accAntiPassbackService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AccLevelDao accLevelDao;
    @Autowired
    private AccLevelDoorDao accLevelDoorDao;
    @Autowired
    private AccMapService accMapService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceVerifyModeDao accDeviceVerifyModeDao;
    @Autowired
    private AccAppTopDoorByPersonDao accAppTopDoorByPersonDao;
    @Autowired
    private AccAppTopDoorByPersonService accAppTopDoorByPersonService;
    @Autowired
    private AccPersonDao accPersonDao;
    @Autowired
    private AccHolidayService accHolidayService;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;
    @Autowired
    private AccCloudService accCloudService;
    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private BaseLicenseClientService baseLicenseClientService;
    @Autowired
    private AccExtDeviceService accExtDeviceService;
    @Autowired
    private BaseAuthCloudMessageSendService baseAuthCloudMessageSendService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccReaderService accReaderService;
    @Autowired(required = false)
    private Acc4IVideoControlEntityService acc4IVideoControlEntityService;
    @Autowired(required = false)
    private AccDoor4OtherService[] accDoor4OtherServices;
    @Value("${system.securityLevel:0}")
    private int securityLevel;

    @Override
    public AccDoorItem saveItem(AccDoorItem item, String applyTo) {
        AccDoor accDoor = Optional.ofNullable(item).map(AccDoorItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(id -> accDoorDao.findById(id)).orElse(new AccDoor());
        ModelUtil.copyPropertiesIgnoreNull(item, accDoor);

        if (StringUtils.isBlank(item.getPassModeTimeSegId()))// 如果前端页面为选择常开时间段，需要将LongOpen设为null,否则无法存储accdoor
        {
            accDoor.setPassModeTimeSegId(null);
        }
        if (item.getBackLock() == null) {
            accDoor.setBackLock(false);
        }
        if (item.getWgReversed() == null) {
            accDoor.setWgReversed((short)0);
        }
        if (item.getIsDisableAudio() == null) {
            accDoor.setIsDisableAudio(false);// 除了普通控制器，其他设备的门暂时默认启用声音
        }
        if (item.getInApbDuration() == null) {
            accDoor.setInApbDuration((short)0);
        }
        if (item.getDelayOpenTime() == null) {
            accDoor.setDelayOpenTime((short)0);
        }
        if (item.getExtDelayDrivertime() == null) {
            accDoor.setExtDelayDrivertime((short)0);
        }
        if (item.getAllowSUAccessLock() == null) {
            accDoor.setAllowSUAccessLock("0");
        }

        accDoorDao.save(accDoor);
        updateQueryDoor(accDoor);
        // 下发门参数命令到控制器
        AccDoorItem doorItem = ModelUtil.copyProperties(accDoor, new AccDoorItem());
        accDevCmdManager.setDoorOptionToDev(doorItem, false);

        if (applyTo != null) {
            if ("1".equals(applyTo)) {
                // 同步到当前设备所有门
                List<AccDoor> doorList = accDoorDao.getOtherDoorByDevId(accDoor.getDevice().getId(), accDoor.getId());
                doorList.removeIf(itAccDoor -> !itAccDoor.getEnabled());
                updateDoorEntitys(accDoor, doorList);
                // 下发门参数命令到控制器
                for (AccDoor door : doorList) {
                    doorItem = ModelUtil.copyProperties(door, new AccDoorItem());
                    accDevCmdManager.setDoorOptionToDev(doorItem, false);
                }
            } else if ("2".equals(applyTo)) {
                // 复制到同种类型设备的所有门， 不包含已下发命令的门
                List<String> devIdsList = accDeviceService.getByMachineType(accDoor.getDevice().getMachineType());
                List<AccDoor> doorList = accDoorDao.getDoorByIdAndDevId(devIdsList, accDoor.getId());
                if (!doorList.isEmpty()) {
                    doorList.removeIf(itAccDoor -> !itAccDoor.getEnabled());
                    updateDoorEntitys(accDoor, doorList);

                    // 下发门参数命令到控制器
                    for (AccDoor door : doorList) {
                        doorItem = ModelUtil.copyProperties(door, new AccDoorItem());
                        accDevCmdManager.setDoorOptionToDev(doorItem, false);
                    }
                }
            } else if ("3".equals(applyTo)) {
                // 所有无线锁设备
                List<AccDoor> doorList =
                    accDoorDao.getOtherDoorByMacType(accDoor.getId(), accDoor.getDevice().getMachineType());
                updateDoorEntitys(accDoor, doorList);
                // 下发门参数命令到控制器
                for (AccDoor door : doorList) {
                    doorItem = ModelUtil.copyProperties(door, new AccDoorItem());
                    accDevCmdManager.setDoorOptionToDev(doorItem, false);
                }
            } else if ("4".equals(applyTo)) {// 拓展板下所有的门
                if (StringUtils.isNotBlank(accDoor.getExtDevId())) {
                    List<AccDoor> doorList = accDoorDao.findByExtDevIdOrderByDoorNoAsc(accDoor.getExtDevId());
                    doorList.removeIf(itAccDoor -> !itAccDoor.getEnabled());
                    updateDoorEntitys(accDoor, doorList);
                    // 下发门参数命令到控制器
                    for (AccDoor door : doorList) {
                        doorItem = ModelUtil.copyProperties(door, new AccDoorItem());
                        accDevCmdManager.setDoorOptionToDev(doorItem, false);
                    }
                }
            }
        }

        item.setId(accDoor.getId());
        // 判断是否注册了云服务，注册才发送。
        if (baseAuthCloudMessageSendService.isAllowSendBasicData()) {
            // 通知线上更新门数据
            ZKMessage zkMessage = new ZKMessage();
            zkMessage.setModuleCode("acc");// 模块代码如acc，pers
            zkMessage.setMessageId("accCloudDeviceInfoHandle#getCloudDoor");// bean名称+ # +方法名 云平台业务代码需要写对应的bean和方法接收。
            zkMessage.setAppId(baseLicenseService.getAppId());
            List<String> doorInfoList = Lists.newArrayList();
            JSONArray doors = new JSONArray();// 组装门数据
            JSONObject door = new JSONObject();
            JSONObject devInfo = new JSONObject();
            door.put("doorNo", accDoor.getDoorNo());
            door.put("devSn", accDoor.getDevice().getSn());
            door.put("name", accDoor.getName());
            door.put("verifyMode", accDoor.getVerifyMode());
            door.put("enabled", accDoor.getEnabled());
            doors.add(door);
            devInfo.put("doors", doors);
            doorInfoList.add(devInfo.toString());
            zkMessage.setListContent(doorInfoList);
            baseLicenseClientService.sendMessage(zkMessage);
        }
        // 发送视频集成模块，更新读头名称
        if (acc4IVideoControlEntityService != null) {
            Acc4IVideoControlEntityItem acc4IVideoControlEntityItem = new Acc4IVideoControlEntityItem();
            acc4IVideoControlEntityItem.setEntityId(accDoor.getId());
            acc4IVideoControlEntityItem.setEntityName(accDoor.getName());
            acc4IVideoControlEntityItem.setEntityClassName("AccDoor");
            acc4IVideoControlEntityService.updateEntityNameByEntityId(acc4IVideoControlEntityItem);
        }

        // 更新其他模块门信息
        updateThirdDoorInfo(accDoor);
        return item;
    }

    private void updateQueryDoor(List<AccDoor> doorList) {
        final Map<AccDevice, List<AccDoor>> devMap =
            doorList.stream().collect(Collectors.groupingBy(AccDoor::getDevice));
        for (AccDevice dev : devMap.keySet()) {
            final AccQueryDeviceItem queryDev = accDeviceService.getQueryItemBySn(dev.getSn());
            accDeviceService.updateDoorByQueryItem(queryDev);
            accCacheManager.putDeviceInfo(queryDev);
        }
    }

    private void updateQueryDoor(AccDoor door) {
        AccDevice dev = door.getDevice();
        final AccQueryDeviceItem queryDev = accDeviceService.getQueryItemBySn(dev.getSn());
        accDeviceService.updateDoorByQueryItem(queryDev);
        accCacheManager.putDeviceInfo(queryDev);
    }

    /**
     * 更新其他模块门信息
     *
     * @param accDoor:门信息
     * @return void
     * <AUTHOR>
     * @date 2021-03-08 17:18
     * @since 1.0.0
     */
    private void updateThirdDoorInfo(AccDoor accDoor) {
        AccDoor4OtherItem accDoor4OtherItem = buildAccDoor4OtherItem(accDoor);
        if (accDoor4OtherServices != null) {
            Arrays.stream(accDoor4OtherServices)
                .forEach(accDoor4OtherService -> accDoor4OtherService.editAccDoorInfo(accDoor4OtherItem));
        }
    }

    /**
     * 组装对接其他模块门信息
     *
     * @param accDoor:门信息
     * @return com.zkteco.zkbiosecurity.acc.vo.AccDoor4OtherItem
     * <AUTHOR>
     * @date 2021-03-08 17:18
     * @since 1.0.0
     */
    private AccDoor4OtherItem buildAccDoor4OtherItem(AccDoor accDoor) {
        AccDoor4OtherItem accDoor4OtherItem = new AccDoor4OtherItem();
        accDoor4OtherItem.setId(accDoor.getId());
        accDoor4OtherItem.setDoorNo(accDoor.getDoorNo());
        accDoor4OtherItem.setName(accDoor.getName());
        accDoor4OtherItem.setDeviceId(accDoor.getDevice().getId());
        return accDoor4OtherItem;
    }

    /**
     *
     * 更新多个门实体到数据库
     *
     * <AUTHOR>
     * @since 2018年4月25日 下午5:26:09
     * @param curDoor
     * @param doorList
     */
    public void updateDoorEntitys(AccDoor curDoor, List<AccDoor> doorList) {
        for (AccDoor door : doorList) {
            AccDevice curDev = curDoor.getDevice();
            AccDevice accDev = door.getDevice();
            door.setLockDelay(curDoor.getLockDelay());

            // 新的一体机不支持复制闭门回锁到其他设备 modify by qingj.qiu
            if (!accDeviceService.isNewAccessControlDevice(curDev.getMachineType(), curDev.getDeviceName())) {
                door.setBackLock(curDoor.getBackLock());
            }
            door.setSensorDelay(curDoor.getSensorDelay());
            door.setActionInterval(curDoor.getActionInterval());
            door.setForcePwd(curDoor.getForcePwd());// 不需加密
            door.setSupperPwd(curDoor.getSupperPwd());// 不需加密
            door.setDoorSensorStatus(curDoor.getDoorSensorStatus());
            door.setIsDisableAudio(curDoor.getIsDisableAudio());
            door.setEnabled(curDoor.getEnabled());

            // 不是该门所在设备，非通用参数，要做功能判断
            if (!curDev.getId().equals(accDev.getId())
                && AccConstants.DEVICE_C3_MACHINE_TYPE.contains(accDev.getMachineType())) {
                if (curDoor.getVerifyMode() == (short)ConstUtil.VERIFY_MODE_ONLYPWD
                    || curDoor.getVerifyMode() == (short)ConstUtil.VERIFY_MODE_ONLYCARD
                    || curDoor.getVerifyMode() == (short)ConstUtil.VERIFY_MODE_CARDORPWD
                    || curDoor.getVerifyMode() == (short)ConstUtil.VERIFY_MODE_CARDANDPWD) {
                    door.setVerifyMode(curDoor.getVerifyMode());
                }
            } else {
                List<Short> currDoorVeryList = accDeviceVerifyModeService.getVerifyNoBySN(curDev.getSn());
                List<Short> doorVeryList = accDeviceVerifyModeService.getVerifyNoBySN(accDev.getSn());
                if (doorVeryList.containsAll(currDoorVeryList)) {
                    door.setVerifyMode(curDoor.getVerifyMode());
                }
            }
            if (accDeviceOptionService.isSupportFun(accDev.getSn(), "MultiCardInterTimeFunOn"))// 判断设备是否支持开门时间间隔设置
            {
                door.setCombOpenInterval(curDoor.getCombOpenInterval());
            }

            door.setActiveTimeSegId(curDoor.getActiveTimeSegId());
            door.setPassModeTimeSegId(curDoor.getPassModeTimeSegId());
            if (accDeviceOptionService.isSupportFun(accDev.getSn(), "~CardFormatFunOn")) {
                door.setWgInputFmtId(curDoor.getWgInputFmtId());
            }
            // 一体机需要添加的参数
            if (AccConstants.DEVICE_ACCESS_MACHINE_TYPE.contains(accDev.getMachineType())) {
                door.setWgOutputFmtId(curDoor.getWgOutputFmtId());
                door.setWgInputType(curDoor.getWgInputType());
                door.setWgOutputType(curDoor.getWgOutputType());
                door.setHostStatus(curDoor.getHostStatus());
                door.setSlaveStatus(curDoor.getSlaveStatus());
            }
            if (accDeviceOptionService.isSupportFun(accDev.getSn(), "~TimeAPBFunOn")) {
                door.setInApbDuration(curDoor.getInApbDuration());
            }
            if (accDeviceOptionService.isSupportFun(accDev.getSn(), "~REXInputFunOn")) {
                door.setLatchDoorType(curDoor.getLatchDoorType());
                door.setLatchTimeOut(curDoor.getLatchTimeOut());
            }
            if (accDeviceOptionService.isSupportFun(accDev.getSn(), "UserOpenDoorDelayFunOn")) {
                door.setExtDelayDrivertime(curDoor.getExtDelayDrivertime());
            }
            if (accDeviceOptionService.isSupportFun(accDev.getSn(), "DelayOpenDoorFunOn")) {
                door.setDelayOpenTime(curDoor.getDelayOpenTime());
            }
            if (accDeviceOptionService.isSupportFunList(accDev.getSn(), 8)) {
                door.setLatchTimeSegId(curDoor.getLatchTimeSegId());
            }
            if (accDeviceOptionService.isSupportFunList(accDev.getSn(), 33)) {
                door.setWgReversed(curDoor.getWgReversed());
            }
            if (accDeviceOptionService.isSupportFunList(accDev.getSn(), 48)) {
                door.setAllowSUAccessLock(curDoor.getAllowSUAccessLock());
            }
            // pro 参数填充
            if (accDev.getCommType().equals(AccConstants.COMM_TYPE_BEST_MQTT)
                && curDev.getCommType().equals(accDev.getCommType())) {
                door.setSenInputMode(curDoor.getSenInputMode());
                door.setSenSupervisedResistor(curDoor.getSenSupervisedResistor());
                door.setRexInputMode(curDoor.getRexInputMode());
                door.setRexSupervisedResistor(curDoor.getRexSupervisedResistor());
                // 更新出门开关类型
                door.setRexButtonType(curDoor.getRexButtonType());
            }
            accDoorDao.save(door);
        }
        updateQueryDoor(doorList);
    }

    @Override
    public List<AccDoorItem> getByCondition(AccDoorItem condition) {
        return (List<AccDoorItem>)accDoorDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = accDoorDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        if (condition instanceof AccDoorItem) {
            List<AccDoorItem> doorItems = (List<AccDoorItem>)pager.getData();
            String areaIds = CollectionUtil.getPropertys(doorItems, AccDoorItem::getAuthAreaId);
            List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIds);
            Map<String, AuthAreaItem> itemMap = CollectionUtil.itemListToIdMap(authAreaItemList);
            String extDevIds = CollectionUtil.getPropertys(doorItems, AccDoorItem::getExtDevId);
            List<AccExtDeviceItem> accExtDeviceItems = accExtDeviceService.getItemByIds(extDevIds);
            Map<String, AccExtDeviceItem> accExtDeviceItemMap = CollectionUtil.itemListToIdMap(accExtDeviceItems);
            List<String> devSns =
                (List<String>)CollectionUtil.getPropertyList(doorItems, AccDoorItem::getDeviceSn, "-1");
            List<AccDeviceOptionItem> accDeviceOptionItemList =
                accDeviceOptionService.getOptionItemBySnsAndOptName(devSns, "NewVFStyles");
            Map<String, AccDeviceOptionItem> newVfStylesItemMap =
                CollectionUtil.listToKeyMap(accDeviceOptionItemList, AccDeviceOptionItem::getDeviceSn);
            doorItems.forEach(doorItem -> {
                if (StringUtils.isNotBlank(doorItem.getAuthAreaId())) {
                    doorItem.setAuthAreaName(itemMap.get(doorItem.getAuthAreaId()).getName());
                }
                if (StringUtils.isNotBlank(doorItem.getExtDevId())) {
                    doorItem.setExtDevName(accExtDeviceItemMap.get(doorItem.getExtDevId()).getAlias());
                }
                AccDeviceOptionItem tempItem = newVfStylesItemMap.get(doorItem.getDeviceSn());
                // 因有些设备不支持新验证方式功能，NewVFStyles参数获取上来值可能为0或者""，支持才设置
                if (null != tempItem && !"0".equals(tempItem.getValue())
                    && StringUtils.isNotBlank(tempItem.getValue())) {
                    newVfStylesItemMap.get(doorItem.getDeviceSn());
                    doorItem.setVerifyModeName(accDeviceVerifyModeService
                        .getVerityModeNamesByNewVFStyles(String.valueOf(doorItem.getVerifyMode())));
                } else {
                    doorItem.setVerifyModeName(accDeviceVerifyModeService.getVerifyMode(doorItem.getDeviceSn(),
                        String.valueOf(doorItem.getVerifyMode())));
                }
            });
            pager.setData(doorItems);
        }
        return pager;
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccDoorItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }

    /**
     * 过滤门数据获取权限，根据权限组归属区域去过滤
     *
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public Pager getPagerFilterAuth(String sessionId, AccLevelSelectDoorItem condition, int pageNo, int pageSize) {
        AccLevel accLevel = accLevelDao.findById(condition.getLevelId()).orElse(new AccLevel());
        if (StringUtils.isNotBlank(accLevel.getAuthAreaId())) {
            List<String> authAreaIdList = new ArrayList<>();
            authAreaIdList.add(accLevel.getAuthAreaId());
            // 根据权限组所属区域去过滤
            List<AuthAreaItem> authAreaItemList = authAreaService.getChildAreaByAreaId(accLevel.getAuthAreaId()); // 获取该区域下的全部子区域
            if (authAreaItemList != null && authAreaItemList.size() > 0) {
                authAreaIdList.addAll(CollectionUtil.getItemIdsList(authAreaItemList));
            }
            // 非超级管理员进行数据权限过滤
            String areaIds = authSessionProvider.getAreaIdsNoSubNodesByAuthFilter(sessionId);
            if (StringUtils.isNotBlank(areaIds)) {
                // 取权限组区域ids和登录用户区域ids的交集。
                authAreaIdList.retainAll(StrUtil.strToList(areaIds));
            }
            condition.setAreaIds(StringUtils.join(authAreaIdList, ","));
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accDoorDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccDoorItem getItemById(String id) {
        List<AccDoorItem> items = getByCondition(new AccDoorItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public Pager getSelectDoorItemsByPage(AccMapSelectDoorItem condition, int pageNo, int pageSize) {
        AccMapItem accMapItem = accMapService.getItemById(condition.getFilterId());
        List<AuthAreaItem> authAreaItemList = authAreaService.getChildAreaByAreaId(accMapItem.getAuthAreaId());
        condition.setAreaIdIn(accMapItem.getAuthAreaId());
        if (Objects.nonNull(authAreaItemList) && !authAreaItemList.isEmpty()) {
            String areaIds = CollectionUtil.getItemIds(authAreaItemList);
            condition.setAreaIdIn(condition.getAreaIdIn() + "," + areaIds);
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(SQLUtil.getSqlByItem(condition));
        int orderByIndex = stringBuilder.indexOf("ORDER BY");
        stringBuilder.insert(orderByIndex, "AND d.WG_READER_ID IS NULL ");
        return accDoorDao.getItemsBySql(condition.getClass(), stringBuilder.toString(), pageNo, pageSize);
    }

    @Override
    public Map<String, String> getDoorIds(String[] idArr, String type) {
        Map<String, String> retMap = new HashMap<String, String>();
        List<AccDoor> doorList = accDoorDao.findByIdIn(Arrays.asList(idArr));
        StringBuffer doorsName = new StringBuffer();
        StringBuffer disabledDoorsName = new StringBuffer();
        StringBuffer offlineDoorsName = new StringBuffer();
        StringBuffer notSupportDoorsName = new StringBuffer();
        AccDoor accDoor = null;
        StringBuffer retIds = new StringBuffer();
        for (int i = 0; i < doorList.size(); i++) {
            accDoor = doorList.get(i);
            AccDevice dev = accDoor.getDevice();
            String devState = accDeviceService.getStatus(dev.getSn());

            // String devStateRet = accCacheManager.getString(ConstUtil.DEV_RTMONITOR_STATE + dev.getSn());
            // JSONObject retJson = JSON.parseObject(devStateRet);
            // 若选中门所在的设备为在线状态，则加入操作列表
            if (accDoor.getEnabled()) {
                if (StringUtils.isNotBlank(devState)) {
                    short connect = Short.valueOf(devState);
                    if (connect == ConstUtil.DEV_STATE_ONLINE) {
                        if (("lockDoor".equals(type) || "unLockDoor".equals(type))
                            && !accDeviceOptionService.isSupportFunList(dev.getSn(), 2)) {
                            notSupportDoorsName.append(accDoor.getName()).append(",");
                        } else {
                            retIds.append(accDoor.getId()).append(",");
                            doorsName.append(accDoor.getName()).append(",");
                        }
                    } else if (connect == ConstUtil.DEV_STATE_DISABLE) {
                        disabledDoorsName.append(accDoor.getName()).append(",");
                    } else {
                        offlineDoorsName.append(accDoor.getName()).append(",");
                    }
                } else {
                    offlineDoorsName.append(accDoor.getName()).append(",");
                }
            } else {
                disabledDoorsName.append(accDoor.getName()).append(",");
            }
        }
        retMap.put("retIds", retIds.toString().replaceAll(",$", ""));
        retMap.put("doorsName", doorsName.toString().replaceAll(",$", ""));
        retMap.put("disabledDoorsName", disabledDoorsName.toString().replaceAll(",$", ""));
        retMap.put("offlineDoorsName", offlineDoorsName.toString().replaceAll(",$", ""));
        retMap.put("notSupportDoorsName", notSupportDoorsName.toString().replaceAll(",$", ""));
        return retMap;
    }

    @Override
    public boolean isNewAccessControlDevice(String deviceId) {
        AccDeviceItem accDeviceItem = accDeviceService.getItemById(deviceId);
        return accDeviceService.isNewAccessControlDevice(accDeviceItem.getMachineType(), accDeviceItem.getDeviceName());
    }

    @Override
    public List<SelectItem> getVerifyMode(String deviceId) {
        List<SelectItem> selectItemList = new ArrayList<>();
        if (StringUtils.isBlank(deviceId)) {
            return selectItemList;
        }
        List<AccDeviceVerifyMode> verifyModeList =
            accDeviceVerifyModeDao.findByAccDevice_IdOrderByVerifyNoAsc(deviceId);
        for (AccDeviceVerifyMode deviceVerifyMode : verifyModeList) {
            // 安全等级大于等于3，过滤含独立密码验证方式
            if (securityLevel >= 3 && AccConstants.VERIFY_MODE_FILTER_BYL3
                .containsKey(Integer.parseInt(deviceVerifyMode.getVerifyNo() + ""))) {
                continue;
            }
            SelectItem selectItem = new SelectItem();
            selectItem.setValue(deviceVerifyMode.getVerifyNo().toString());
            selectItem.setText(I18nUtil.i18nCode(deviceVerifyMode.getName()));
            selectItemList.add(selectItem);
        }
        return selectItemList;
    }

    @Override
    public List<SelectItem> getWiegandFmtList() {
        List<SelectItem> selectItemList = new ArrayList<>();
        SelectItem selectItem = null;
        List<PersWiegandFmtItem> persWiegandFmtItemList =
            persWiegandFmtService.getByCondition(new PersWiegandFmtItem());
        for (PersWiegandFmtItem persWiegandFmtItem : persWiegandFmtItemList) {
            selectItem = new SelectItem();
            selectItem.setValue(persWiegandFmtItem.getId());
            selectItem.setText(persWiegandFmtItem.getName());
            selectItemList.add(selectItem);
        }

        return selectItemList;
    }

    @Override
    public boolean validGlobalApb(String doorId) {
        AccDoor accDoor = accDoorDao.getOne(doorId);
        if (accDoor != null) {
            String deviceId = accDoor.getDevice().getId();
            return accAntiPassbackService.validGlobalApb(deviceId);
        }
        return false;
    }

    @Override
    public boolean isExist(String name) {
        AccDoor accDoor = accDoorDao.findByName(name);
        if (accDoor == null) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkPwd(String forcePwd) {
        return !persPersonService.ckeckPwd(forcePwd);
    }

    @Override
    public Map<String, Object> getAccDeviceOpt(String deviceId) {
        Map<String, Object> devOptMap = new HashMap<>();
        AccDeviceItem accDeviceItem = accDeviceService.getItemById(deviceId);
        String rexInputFunOn = accDeviceOptionService.getOptVal(accDeviceItem.getSn(), "~REXInputFunOn");// 自动门功能
        String cardFormatFunOn = accDeviceOptionService.getOptVal(accDeviceItem.getSn(), "~CardFormatFunOn");// 卡格式配置
        String timeAPBFunOn = accDeviceOptionService.getOptVal(accDeviceItem.getSn(), "~TimeAPBFunOn");// 时间反潜
        devOptMap.put("REXInputFunOn", rexInputFunOn);
        devOptMap.put("CardFormatFunOn", cardFormatFunOn);
        devOptMap.put("TimeAPBFunOn", timeAPBFunOn);
        // 判断是否为一体机
        devOptMap.put("standaloneDevice", AccConstants.DEVICE_ACCESS_CONTROL == accDeviceItem.getMachineType());
        return devOptMap;
    }

    @Override
    public AccDoorItem getByDoorNoAndDeviceId(short doorNo, String deviceId) {
        AccDoor accDoor = accDoorDao.findByDoorNoAndDevice_Id(doorNo, deviceId);
        AccDoorItem accDoorItem = null;
        if (Objects.nonNull(accDoor)) {
            accDoorItem = ModelUtil.copyProperties(accDoor, new AccDoorItem());
        }
        return accDoorItem;
    }

    @Override
    public ZKResultMsg enable(String ids) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        String msg = "";
        Integer count = null;
        // 启用的门数量除去勾选中已启用的门的数量
        String[] idAry = ids.split(",");
        int enabledDoorCount = 0;
        List<AccDoor> doorList = new ArrayList<AccDoor>();
        for (String id : idAry) {
            AccDoor door = accDoorDao.getOne(id);
            if (!door.getEnabled()) {
                enabledDoorCount++;
                doorList.add(door);
            }
        }
        Map<String, Integer> countMap =
            accDeviceService.getAfterLicensedCount(1, enabledDoorCount, AccConstants.COMM_TYPE_PUSH_HTTP);
        ResultCode resultCode = baseLicenseProvider.isCountOutRangeAcc(ConstUtil.LICENSE_MODULE_ACC_PUSH, countMap);
        if (ResultCode.SUCCESS == resultCode) {
            for (AccDoor accDoor : doorList) {
                accDoor.setEnabled(true);
                accDoorDao.save(accDoor);
                // add by colin 2022-1-28 16:51:30 启用禁用门之后更新门缓存
                updateDoorCache(accDoor.getDevice().getSn());
            }
        } else {
            if (ResultCode.OVER == resultCode) {
                Map<String, Integer> beforeCountMap = accDeviceService.getBeforeLicensedCount();
                int currentDoorNum = beforeCountMap.get("pullGateCount") + beforeCountMap.get("pushGateCount");
                if (baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH) - currentDoorNum > 0) {
                    // 当前许可只允许启用%s个门！请重新选择门，或者联系销售人员购买更新许可。
                    msg = "acc_door_outNumber";
                    count = baseLicenseProvider.getDeviceCount(ConstUtil.LICENSE_MODULE_ACC_PUSH) - currentDoorNum;
                } else {
                    // 系统里存在%s个已启用的门，达到许可点数上限。要继续当前操作，请联系销售人员！
                    msg = "acc_door_pushMaxCount";
                    count = currentDoorNum;
                }
            } else if (ResultCode.TRIALEXPIRED == resultCode) {
                msg = "common_license_trialExpired";
            } else if (ResultCode.EXPIRED == resultCode) {
                msg = "common_license_expired";
            } else if (ResultCode.UNAUTH == resultCode) {
                msg = "auth_license_noLicense";
            } else {
                msg = "auth_license_checkError";
            }
        }
        if (!"".equals(msg)) {
            resultMsg.setRet("false");
            resultMsg.setMsg(msg);
            resultMsg.setI18nArgs(count);
        }
        return resultMsg;
    }

    @Override
    public void disable(String ids) {
        String[] idAry = ids.split(",");
        AccDoor accDoor = null;
        for (String id : idAry) {
            accDoor = accDoorDao.getOne(id);
            accDoor.setEnabled(false);
            accDoorDao.save(accDoor);
            // add by colin 2022-1-28 16:51:30 启用禁用门之后更新门缓存
            updateDoorCache(accDoor.getDevice().getSn());
        }
    }

    private void updateDoorCache(String devSn) {
        AccQueryDeviceItem queryDeviceItem = accDeviceService.getQueryItemBySn(devSn);
        if (queryDeviceItem != null) {
            accDeviceService.updateDoorByQueryItem(queryDeviceItem);
            accCacheManager.putDeviceInfo(queryDeviceItem);
        }
    }

    @Override
    public List<AccTransactionPersonDoorItem> getPersonDoorItemData(
        Class<AccTransactionPersonDoorItem> accTransactionPersonDoorItemClass, BaseItem condition, int beginIndex,
        int endIndex) {
        return accDoorDao.getItemsDataBySql(accTransactionPersonDoorItemClass, SQLUtil.getSqlByItem(condition),
            beginIndex, endIndex, true);
    }

    @Override
    public List<AccLevelDoorItem> getExportItemList(AccLevelDoorItem accLevelDoorItem, int beginIndex, int endIndex) {
        return accDoorDao.getItemsDataBySql(AccLevelDoorItem.class, SQLUtil.getSqlByItem(accLevelDoorItem), beginIndex,
            endIndex, true);
    }

    @Override
    public List<AccDoorItem> getItemsByIdsAndSensorStatus(List<String> doorIdList) {
        List<Short> doorSensorStatusList = Lists.newArrayList((short)1, (short)2);
        List<AccDoor> accDoorList = accDoorDao.findByIdInAndDoorSensorStatusIn(doorIdList, doorSensorStatusList);
        return ModelUtil.copyListProperties(accDoorList, AccDoorItem.class);
    }

    @Override
    public ZKResultMsg getDoorStatus(String sessionId, String loadText) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        AccDoorItem item = new AccDoorItem();
        if (StringUtils.isNotBlank(userId)) {
            item.setUserId(userId);
        }
        List<AccDoorItem> accDoorList =
            (List<AccDoorItem>)accDoorDao.getItemsBySql(item.getClass(), SQLUtil.getSqlByItem(item));
        JSONArray dataArray = new JSONArray();
        if (Objects.nonNull(accDoorList) && !accDoorList.isEmpty()) {
            for (AccDoorItem door : accDoorList) {
                // 主动过滤禁用的门和当作控制器读头的门
                AccQueryDeviceItem dev = accDeviceService.getQueryItemBySn(door.getDeviceSn());
                if (door.getEnabled() && dev != null
                    && (dev.getWgReaderId() == null || "".equals(dev.getWgReaderId()))) {
                    List<AccQueryDoorItem> doorList = dev.getAccDoorItemList();
                    Map<String, AccQueryDoorItem> doorItemMap =
                        CollectionUtil.listToKeyMap(doorList, AccQueryDoorItem::getId);
                    AccQueryDoorItem queryDoorItem = doorItemMap.get(door.getId());
                    // 门底下读头绑定的视频通道ID
                    String doorBindChannelIds = getDoorBindChannelIds(queryDoorItem);
                    JSONObject objJson = new JSONObject();
                    objJson.put("id", door.getId());
                    objJson.put("areaId", dev.getAuthAreaId());
                    objJson.put("devAlias", dev.getAlias());
                    objJson.put("devSn", dev.getSn());
                    objJson.put("no", door.getDoorNo());
                    objJson.put("name", door.getName());
                    objJson.put("image", "default");
                    objJson.put("sensor", loadText);
                    objJson.put("relay", loadText);
                    objJson.put("alarm", loadText);
                    objJson.put("connect", -1);
                    objJson.put("display", "none");
                    objJson.put("audio", door.getIsDisableAudio());// true为禁用声音
                    objJson.put("iconFolderName", AccDeviceUtil.getIconFolderName(dev));
                    objJson.put("lockDisplay", "block");
                    objJson.put("opDisplay", "inline");
                    objJson.put("channelIds", doorBindChannelIds);
                    dataArray.add(objJson);
                }
            }
        }
        return new ZKResultMsg(dataArray);
    }

    @Override
    public String getDoorBindChannelIds(AccQueryDoorItem queryDoorItem) {
        StringBuilder channelIdBuilder = new StringBuilder();
        if (queryDoorItem == null) {
            return channelIdBuilder.toString();
        }
        List<AccQueryReaderItem> readerItemList = queryDoorItem.getAccReaderItemList();
        if (Objects.nonNull(readerItemList) && readerItemList.size() > 0) {
            Set<String> channelIdSet = new HashSet<>();
            for (AccQueryReaderItem readerItem : readerItemList) {
                String channelIds = accReaderService.getAccReaderBindVidChannel(readerItem.getId());
                if (StringUtils.isBlank(channelIds)) {
                    continue;
                }
                // 通道ID不重复
                channelIdSet.addAll(StrUtil.strToList(channelIds));
            }
            channelIdSet.forEach(id -> channelIdBuilder.append(id).append(","));
            if (channelIdBuilder.length() > 0) {
                return channelIdBuilder.substring(0, channelIdBuilder.length() - 1);
            }
        }
        return channelIdBuilder.toString();
    }

    @Override
    public List<AccDoorItem> getItemsByIds(Collection<String> doorIdList) {
        AccDoorItem condition = new AccDoorItem();
        condition.setInId(StringUtils.join(doorIdList, ","));
        return getByCondition(condition);
    }

    @Override
    public List<String> getDoorIdAsReader() {
        return accDoorDao.getDoorIdAsReader(AccConstants.DEVICE_ACCESS_CONTROL);
    }

    @Override
    public Pager accLevelDoorList(String sessionId, AccLevelDoorItem condition, int page, int size) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, page, size);
    }

    @Override
    public List<String> getDoorIdsAsWGReaderByDoorId(List<String> doorIdList) {
        return accDoorDao.getDoorIdsAsWGReaderByDoorId(doorIdList);
    }

    @Override
    public List<String> getDoorIdsAsWGReaderByDoorIdAndLevelId(List<String> doorIdList, String levelId) {
        return accDoorDao.getDoorIdsAsWGReaderByDoorIdAndLevelId(levelId, doorIdList);
    }

    @Override
    public List<String> getDevAndParentDevIdsByDoorIds(List<String> doorIdList) {
        List<AccDevice> devList = accDoorDao.getDeviceByDoorId(doorIdList);
        // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
        AccDeviceUtil.moveUpParentDevList(devList);
        List<String> deviceIdList = (List<String>)CollectionUtil.getModelIdsList(devList);
        return deviceIdList;
    }

    @Override
    public List<String> getDevIdsByDoorIds(List<String> doorIdList) {
        List<AccDevice> devList = accDoorDao.getDeviceByDoorId(doorIdList);
        List<String> deviceIdList = (List<String>)CollectionUtil.getModelIdsList(devList);
        return deviceIdList;
    }

    @Override
    public List<String> getIdByDevId(String devId) {
        return accDoorDao.getIdByDevId(devId);
    }

    @Override
    public List<AccDoorItem> getItemsByDevIds(List<String> devIdList) {
        List<AccDoorItem> doorItems = new ArrayList<>();
        if (devIdList != null && devIdList.size() > 0) {
            AccDoorItem doorItem = new AccDoorItem();
            doorItem.setDevIdIn(StringUtils.join(devIdList, ","));
            doorItems = getByCondition(doorItem);
        }
        return doorItems;
    }

    @Override
    public void cloudOpenDoor(Map<String, List<Integer>> doorMap, String openInterval) {
        List<AccDevice> devList = accDeviceDao.findBySnIn(Lists.newArrayList(doorMap.keySet()));
        for (AccDevice dev : devList) {
            List<Integer> doorNoList = doorMap.get(dev.getSn());
            for (Integer doorNo : doorNoList) {
                if (Short.valueOf(accDeviceService.getStatus(dev.getSn()))
                    .shortValue() == AccConstants.DEV_STATE_ONLINE) {
                    short intervalShort = Short.valueOf(openInterval);// 开门时长（1-254秒，255秒为常开）
                    accDevCmdManager.ctrlDoor(dev.getSn(), doorNo.shortValue(), intervalShort, true);// 远程开门
                }
            }
        }
    }

    @Override
    public Long getDoorCount() {
        return accDoorDao.count();
    }

    @Override
    public List<AccDoorItem> getUploadCloudDoor(int pageNo, int pageSize) {
        AccDoorItem accDoorItem = new AccDoorItem();
        List<String> doorIdList = getDoorIdAsReader(); // 获取需要过滤的门ids
        if (doorIdList != null && doorIdList.size() > 0) {
            accDoorItem.setNotId(StringUtils.join(doorIdList, ","));
        }
        Pager pager = getItemsByPage(accDoorItem, pageNo, pageSize);
        return (List<AccDoorItem>)pager.getData();
    }

    @Override
    public List<String> getUploadCloudPersonLevel(AccDoorItem accDoorItem) {
        List<String> personLevelInfoList = new ArrayList<>();
        List<String> personIds = accLevelDoorDao.getPersonIdsByDoorId(accDoorItem.getId());
        if (personIds != null && personIds.size() > 0) {
            JSONObject personLevelInfo = null;
            List<PersPersonItem> persPersonItems = persPersonService.getItemsByIds(personIds);
            for (PersPersonItem persPersonItem : persPersonItems) {
                personLevelInfo = new JSONObject();
                personLevelInfo.put("devSn", accDoorItem.getDeviceSn());
                personLevelInfo.put("doorNo", accDoorItem.getDoorNo());
                personLevelInfo.put("pin", persPersonItem.getPin());
                personLevelInfoList.add(personLevelInfo.toJSONString());
            }
        }
        return personLevelInfoList;
    }

    @Override
    public AccDoorItem getItemByName(String doorName) {
        AccDoor accDoor = accDoorDao.findByName(doorName);
        if (accDoor != null) {
            return ModelUtil.copyPropertiesIgnoreNull(accDoor, new AccDoorItem());
        }
        return null;
    }

    @Override
    public void setDoorHostStatus(String devId, String devIOState) {
        AccDevice device = accDeviceDao.findById(devId).orElse(null);
        if (device != null) {
            List<AccDoor> doorList = device.getAccDoorList();
            for (AccDoor accDoor : doorList) {
                accDoor.setHostStatus(Short.parseShort(devIOState));
                accDoorDao.save(accDoor);
            }
        }
    }

    @Override
    public void handlerTransfer(List<AccDoorItem> doorItems) {
        // 按设备sn分组， key：pin value:
        Map<String, List<AccDoorItem>> deviceOpMap =
            doorItems.stream().collect(Collectors.groupingBy(AccDoorItem::getDeviceSn));
        // 获取数据库中原有的设备参数取出，用于比较
        List<List<String>> snsList = CollectionUtil.split(deviceOpMap.keySet(), CollectionUtil.splitSize);
        Map<String, AccDoor> optionAllMap = new HashMap<String, AccDoor>();
        Map<String, AccDevice> deviceAllMap = new HashMap<String, AccDevice>();
        // 数据大时分批处理
        for (List<String> sns : snsList) {
            List<AccDoor> doors = accDoorDao.findByDevice_SnIn(sns);
            for (AccDoor door : doors) {
                String key = door.getDevice().getSn() + "_" + door.getDoorNo();
                optionAllMap.put(key, door);
            }
            // 將所有的設備查詢出來
            List<AccDevice> devices = accDeviceDao.findBySnIn(sns);
            for (AccDevice accDevice : devices) {
                deviceAllMap.put(accDevice.getSn(), accDevice);
            }
        }
        // 检测判断数据后保存更新
        for (AccDoorItem doorItem : doorItems) {
            AccDoor door = optionAllMap.remove(doorItem.getDeviceSn() + "_" + doorItem.getDoorNo());
            if (Objects.isNull(door)) {
                door = new AccDoor();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(doorItem, door, "id");
            door.setDevice(deviceAllMap.get(doorItem.getDeviceSn()));
            // 这里的businessId就类似于编码，这个和旧架构的id是一样的 时间段
            if (StringUtils.isNotBlank(doorItem.getActiveTimeSegId())) {
                AccTimeSeg timeSeg = accTimeSegDao.findByBusinessId(Long.parseLong(doorItem.getActiveTimeSegId()));
                door.setActiveTimeSegId(timeSeg.getId());
            }
            if (StringUtils.isNotBlank(doorItem.getPassModeTimeSegId())) {
                AccTimeSeg timeSeg = accTimeSegDao.findByBusinessId(Long.parseLong(doorItem.getPassModeTimeSegId()));
                door.setPassModeTimeSegId(timeSeg.getId());
            }
            if (StringUtils.isNotBlank(doorItem.getLatchTimeSegId())) {
                AccTimeSeg timeSeg = accTimeSegDao.findByBusinessId(Long.parseLong(doorItem.getLatchTimeSegId()));
                door.setLatchTimeSegId(timeSeg.getId());
            }
            // 这里的businessId就类似于编码，这个和旧架构的id是一样的 韦根格式
            if (StringUtils.isNotBlank(doorItem.getWgInputFmtId())) {
                PersWiegandFmtItem wiegandFmtItem = new PersWiegandFmtItem();
                wiegandFmtItem.setBusinessId(Long.parseLong(doorItem.getWgInputFmtId()));
                List<PersWiegandFmtItem> item = persWiegandFmtService.getByCondition(wiegandFmtItem);
                door.setWgInputFmtId((item != null && item.size() > 0) ? item.get(0).getId() : null);
            }
            if (StringUtils.isNotBlank(doorItem.getWgOutputFmtId())) {
                PersWiegandFmtItem wiegandFmtItem = new PersWiegandFmtItem();
                wiegandFmtItem.setBusinessId(Long.parseLong(doorItem.getWgOutputFmtId()));
                List<PersWiegandFmtItem> item = persWiegandFmtService.getByCondition(wiegandFmtItem);
                door.setWgOutputFmtId((item != null && item.size() > 0) ? item.get(0).getId() : null);
            }
            accDoorDao.save(door);
        }
        optionAllMap = null;
        deviceAllMap = null;
    }

    @Override
    public List<String> getDoorIdByCommType(List<Short> commType) {
        return accDoorDao.getDoorIdByCommType(commType);
    }

    @Override
    public List<AccDoorItem> getAccDoorByAreaIds(Collection<String> areaIds) {
        List<AccDoorItem> doorItemList = new ArrayList<>();
        if (areaIds != null && areaIds.size() > 0) {
            List<AccDoor> doorList = accDoorDao.findByDevice_AuthAreaIdIn(areaIds);
            doorItemList = ModelUtil.copyListProperties(doorList, AccDoorItem.class);
        }
        return doorItemList;
    }

    @Override
    public AccDoorItem getDoorByDevSnAndDoorNo(String deviceSn, Short doorNo) {
        AccDoor accDoor = accDoorDao.findByDoorNoAndDevice_Sn(doorNo, deviceSn);
        if (Objects.nonNull(accDoor)) {
            return ModelUtil.copyPropertiesIgnoreNull(accDoor, new AccDoorItem());
        }
        return null;
    }

    /**
     * @param doorMap
     */
    @Override
    public void cloudCancelAlarm(Map<String, List<Integer>> doorMap) {
        List<AccDevice> devList = accDeviceDao.findBySnIn(Lists.newArrayList(doorMap.keySet()));
        for (AccDevice dev : devList) {
            List<Integer> doorNoList = doorMap.get(dev.getSn());
            for (Integer doorNo : doorNoList) {
                if (Short.valueOf(accDeviceService.getStatus(dev.getSn()))
                    .shortValue() == AccConstants.DEV_STATE_ONLINE) {
                    accDevCmdManager.cancelAlarm(dev.getSn(), doorNo.shortValue(), true);// 取消报警
                }
            }
        }
    }

    @Override
    public String getDoorByDevSnAndDoorNos(String sn, List<Short> doorNos) {
        List<AccDoor> accDoorList = accDoorDao.findByDoorNoInAndDevice_Sn(doorNos, sn);
        if (Objects.nonNull(accDoorList) && !accDoorList.isEmpty()) {
            return CollectionUtil.getPropertys(accDoorList, AccDoor::getId);
        }
        return null;
    }

    @Override
    public ZKResultMsg topDoor(String operator, String doorId) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        PersPersonItem personItem = persPersonService.getItemByPin(operator);
        String personId = personItem.getId();
        AccAppTopDoorByPersonItem accAppTopDoorByPersonItem = new AccAppTopDoorByPersonItem();
        accAppTopDoorByPersonItem.setDoorId(doorId);
        accAppTopDoorByPersonItem.setPersonId(personId);
        List<AccAppTopDoorByPersonItem> AccAppTopDoorByPersonList =
            accAppTopDoorByPersonService.getByCondition(accAppTopDoorByPersonItem);
        if (Objects.nonNull(AccAppTopDoorByPersonList) && AccAppTopDoorByPersonList.isEmpty()) {
            accAppTopDoorByPersonService.saveItem(accAppTopDoorByPersonItem);
        }
        return resultMsg;
    }

    @Override
    public ZKResultMsg cancleTopDoor(String operator, String doorId) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        PersPersonItem personItem = persPersonService.getItemByPin(operator);
        String personId = personItem.getId();
        AccAppTopDoorByPersonItem accAppTopDoorByPersonItem = new AccAppTopDoorByPersonItem();
        accAppTopDoorByPersonItem.setDoorId(doorId);
        accAppTopDoorByPersonItem.setPersonId(personId);
        List<AccAppTopDoorByPersonItem> AccAppTopDoorByPersonList =
            accAppTopDoorByPersonService.getByCondition(accAppTopDoorByPersonItem);
        if (Objects.nonNull(AccAppTopDoorByPersonList) && !AccAppTopDoorByPersonList.isEmpty()) {
            accAppTopDoorByPersonService.deleteByIds(AccAppTopDoorByPersonList.get(0).getId());
        }
        return resultMsg;
    }

    @Override
    public List<AccDoorItem> getAccDoorItems() {
        List<AccDoor> accDoors = accDoorDao.findAll();
        List<AccDoorItem> accDoorItems = new ArrayList<>();
        if (accDoors != null && accDoors.size() > 0) {
            AccDoorItem item = null;
            for (AccDoor accDoor : accDoors) {
                item = new AccDoorItem();
                item.setId(accDoor.getId());
                item.setDoorNo(accDoor.getDoorNo());
                item.setName(accDoor.getName());
                item.setDeviceSn(accDoor.getDevice().getSn());
                accDoorItems.add(item);
            }
        }
        return accDoorItems;
    }

    @Override
    public Map<String, String> operateDoorByTimeSeg(String opType, String openInterval, String doorIds, String operator,
        boolean isAdmin) {
        List<String> doorIdList = new ArrayList<>();
        Map<String, String> resultMap = new HashMap<>();
        if (StringUtils.isNotBlank(operator) && !isAdmin) {
            Date eventDate = new Date();
            // 操作者非管理员需要判断操作时间是否在权限时间段内
            PersPersonItem persPersonItem = persPersonService.getItemByPin(operator);
            if (persPersonItem != null) {
                AccPerson accPerson = accPersonDao.findByPersonId(persPersonItem.getId());
                // 判断人员是否在有效期内
                if (accPerson != null && accPerson.getIsSetValidTime() != null && accPerson.getIsSetValidTime()) {
                    Date startTime = accPerson.getStartTime();
                    Date endTime = accPerson.getEndTime();
                    if (!(startTime.getTime() <= eventDate.getTime() && endTime.getTime() >= eventDate.getTime())) {
                        // 不在人员有效时间段内
                        resultMap.put("error", I18nUtil.i18nCode("acc_operate_door_notInValidDate"));
                        return resultMap;
                    }
                }

                short holidayType = accHolidayService.isInHoliday(eventDate);
                List<AccDoor> accDoorList = accDoorDao.findByIdIn((List<String>)CollectionUtil.strToList(doorIds));
                String msg = "";
                // 存放时间段判断结果
                Map<String, Boolean> timeSegResult = new HashMap<>();
                // 判断人员-门对应权限组是否在有效时间段内，目前只要一个是在有效时间段内则算通过
                for (AccDoor accDoor : accDoorList) {
                    boolean isInTimeSeg = false;
                    // 根据人和门找出公共权限组ID
                    List<String> accLevelIds =
                        accLevelDao.getLevelIdByPersonIdAndDoorId(persPersonItem.getId(), accDoor.getId());
                    if (!CollectionUtil.isEmpty(accLevelIds)) {
                        List<AccLevel> accLevelList = accLevelDao.findByIdIn(accLevelIds);
                        if (!CollectionUtil.isEmpty(accLevelList)) {
                            for (AccLevel accLevel : accLevelList) {
                                String accTimeSegId = accLevel.getTimeSegId();
                                Boolean checkTimeSegResult = timeSegResult.get(accTimeSegId);
                                if (checkTimeSegResult == null) {
                                    boolean checkTime = checkTimeSeg(eventDate, accTimeSegId, holidayType);
                                    // 缓存时间段判断结果，避免重复判断同一个时间段
                                    timeSegResult.put(accTimeSegId, checkTime);
                                    if (checkTime) {
                                        isInTimeSeg = true;
                                        break;
                                    }
                                } else if (checkTimeSegResult) {
                                    isInTimeSeg = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (isInTimeSeg) {
                        doorIdList.add(accDoor.getId());
                    } else {
                        msg += accDoor.getName() + "，" + I18nUtil.i18nCode("acc_operate_door_notInValidDate");
                    }
                }
                if (!doorIdList.isEmpty()) {
                    resultMap =
                        accRTMonitorService.operateDoor(opType, openInterval, StringUtils.join(doorIdList, ","));
                }
                if (StringUtils.isNotBlank(msg)) {
                    resultMap.put("error", msg);
                }
                return resultMap;
            } else {
                resultMap.put("error", I18nUtil.i18nCode("pers_api_personNotExist"));
                return resultMap;
            }
        } else {
            return accRTMonitorService.operateDoor(opType, openInterval, doorIds);
        }
    }

    @Override
    public ZKResultMsg operateDoor(String opType, String openInterval, String doorIds, String operator,
        boolean isAdmin) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        Map<String, String> dataMap = operateDoorByTimeSeg(opType, openInterval, doorIds, operator, isAdmin);
        if ("0".equals(openInterval)) {
            // 远程关门
            opType = "closeDoor";
        } else if ("255".equals(openInterval)) {
            // 远程常开
            opType = "normalOpenDoor";
        }
        dataMap.put("operator", operator);
        dataMap.put("opType", opType);
        resultMsg = dealAppResultData(dataMap);
        return resultMsg;
    }

    @Override
    public AccDoorItem saveSimpleItem(AccDoorItem item) {
        AccDoor accDoor = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accDoorDao.findById(id)).orElse(new AccDoor());
        ModelUtil.copyPropertiesIgnoreNull(item, accDoor);
        if (StringUtils.isBlank(item.getPassModeTimeSegId())) {
            accDoor.setPassModeTimeSegId(null);
        }
        if (StringUtils.isNotBlank(item.getDeviceId())) {
            AccDevice accDevice = accDeviceDao.findById(item.getDeviceId()).orElse(null);
            if (Objects.nonNull(accDevice)) {
                accDoor.setDevice(accDevice);
            }
        }

        accDoorDao.save(accDoor);
        item.setId(accDoor.getId());
        item.setDeviceId(accDoor.getDevice().getId());
        return item;
    }

    @Override
    public List<AccDoorItem> getOtherDoorByDevId(String deviceId, String doorId) {
        List<AccDoorItem> accDoorItemList = new ArrayList<>();
        if (StringUtils.isNotBlank(deviceId) && StringUtils.isNotBlank(doorId)) {
            List<AccDoor> doorList = accDoorDao.getOtherDoorByDevId(deviceId, doorId);
            accDoorItemList = buildAccDoorItems(doorList);
        }
        return accDoorItemList;
    }

    @Override
    public List<AccDoorItem> getDoorByDoorIdAndDevIds(String doorId, List<String> devIdList) {
        List<AccDoorItem> accDoorItemList = new ArrayList<>();
        if (StringUtils.isNotBlank(doorId) && !devIdList.isEmpty()) {
            List<AccDoor> doorList = accDoorDao.getDoorByIdAndDevId(devIdList, doorId);
            accDoorItemList = buildAccDoorItems(doorList);
        }
        return accDoorItemList;
    }

    @Override
    public List<AccDoorItem> getOtherDoorByMacType(String doorId, Short machineType) {
        List<AccDoorItem> accDoorItemList = new ArrayList<>();
        if (StringUtils.isNotBlank(doorId) && Objects.nonNull(machineType)) {
            List<AccDoor> doorList = accDoorDao.getOtherDoorByMacType(doorId, machineType);
            accDoorItemList = buildAccDoorItems(doorList);
        }
        return accDoorItemList;
    }

    @Override
    public Pager getSimpleItemsByPage(BaseItem condition, int page, int size) {
        return accDoorDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public Map<String, AccDoorItem> getItemsMapByDoorIds(List<String> doorIds) {
        Map<String, AccDoorItem> itemMap = new HashMap<>();
        if (Objects.nonNull(doorIds) && !doorIds.isEmpty()) {
            List<AccDoorItem> accDoorList = getItemsByIds(doorIds);
            if (Objects.nonNull(accDoorList) && !accDoorList.isEmpty()) {
                itemMap = CollectionUtil.listToKeyMap(accDoorList, AccDoorItem::getId);
            }
        }
        return itemMap;
    }

    @Override
    public int countByActiveTimeSegId(String activeTimeSegId) {
        return accDoorDao.countByActiveTimeSegId(activeTimeSegId);
    }

    @Override
    public int countByPassModeTimeSegId(String passModeTimeSegId) {
        return accDoorDao.countByPassModeTimeSegId(passModeTimeSegId);
    }

    @Override
    public int countByLatchTimeSegId(String latchTimeSegId) {
        return accDoorDao.countByLatchTimeSegId(latchTimeSegId);
    }

    @Override
    public List<AccDoorItem> getItemsByDevSn(String devSn) {
        List<AccDoorItem> accDoorItemList = new ArrayList<>();
        if (StringUtils.isNotBlank(devSn)) {
            List<AccDoor> accDoorList = accDoorDao.findByDevice_Sn(devSn);
            accDoorItemList = buildAccDoorItems(accDoorList);
        }
        return accDoorItemList;
    }

    @Override
    public String getAccdoorSimpleName() {
        return AccDoor.class.getSimpleName();
    }

    @Override
    public List<AccLevelDoorExportItem> getExportLevelDoorItemList(AccLevelDoorExportItem accLevelDoorItem,
        int beginIndex, int endIndex) {
        return accDoorDao.getItemsDataBySql(AccLevelDoorExportItem.class, SQLUtil.getSqlByItem(accLevelDoorItem),
            beginIndex, endIndex, true);
    }

    @Override
    public boolean doorIsOnline(String devSn, Short doorNo) {
        String devRtState = accCacheManager.getAccRtState(devSn);
        if (devRtState != null) {
            JSONObject jsonObject = JSONObject.parseObject(devRtState);
            String connect = jsonObject.getString("connect");
            if ("1".equals(connect)) {
                String door = jsonObject.getString("door");
                if (StringUtils.isNotBlank(door)) {
                    String[] doorState = door.split(",");
                    // 判断door字段16进制转化为2进制数的首位是否为1，首位代表是否在线
                    return doorState.length >= doorNo && (Integer.parseInt(doorState[doorNo - 1], 16) & 1) == 1;
                } else {
                    // 老设备的实时状态可能没有door状态，故只用connect判断即可
                    return true;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    private List<AccDoorItem> buildAccDoorItems(List<AccDoor> doorList) {
        List<AccDoorItem> accDoorItemList = new ArrayList<>();
        if (Objects.nonNull(doorList) && !doorList.isEmpty()) {
            for (AccDoor door : doorList) {
                AccDoorItem doorItem = new AccDoorItem();
                ModelUtil.copyProperties(door, doorItem);
                AccDevice dev = door.getDevice();
                doorItem.setDeviceId(dev.getId());
                doorItem.setDeviceSn(dev.getSn());
                doorItem.setDevMachineType(dev.getMachineType() + "");
                accDoorItemList.add(doorItem);
            }
        }
        return accDoorItemList;
    }

    /**
     * 判断开门时间点是否在时间段内
     * 
     * <AUTHOR>
     * @date 2019/11/6 15:04
     * @param
     * @return
     */
    private boolean checkTimeSeg(Date eventDate, String timeSegId, short holidayType) {
        boolean isInTimeSeg = false;
        if (holidayType > 0) {
            isInTimeSeg = accTimeSegService.checkTimeSeg(timeSegId, holidayType + 6, eventDate);
        } else {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(eventDate);
            int weekNumber = calendar.get(Calendar.DAY_OF_WEEK) - 1;
            isInTimeSeg = accTimeSegService.checkTimeSeg(timeSegId, weekNumber, eventDate);
        }
        return isInTimeSeg;
    }

    /**
     * @Description: 处理返回数据
     * <AUTHOR>
     * @date 2018/6/6 14:54
     * @param dataMap
     * @return
     */
    private ZKResultMsg dealAppResultData(Map<String, String> dataMap) {
        String cmdIdData = dataMap.get("cmdId");
        // 操作类型
        String opType = dataMap.get("opType");
        // 操作者
        String operator = dataMap.get("operator");
        String msg = "";
        ZKResultMsg resultMsg = new ZKResultMsg();
        if ("true".equals(dataMap.get("notExistDev"))) {
            msg = I18nUtil.i18nCode("common_dev_opFaileAndReason") + I18nUtil.i18nCode("common_dev_notExistDev");
            resultMsg.setMsg(msg);
            return resultMsg;
        }
        if (StringUtils.isNotBlank(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String[] dataArr = cmdData.split("=", 4);
                // 命令ID
                String cmdId = dataArr[0];
                // 门名称
                String doorName = dataArr[1];
                // 设备SN
                String sn = dataArr[2];
                // 门编号
                String doorNo = dataArr[3];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap)) {
                    msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                } else {
                    Integer ret = Integer.valueOf(resultMap.get("result"));
                    if (Objects.isNull(ret)) {
                        msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                    } else if (ret < 0) {
                        String failedInfo = I18nUtil.i18nCode(accBaseDictionaryService.getCommReason(ret));
                        msg += doorName + "," + I18nUtil.i18nCode("common_dev_opFaileAndReason") + failedInfo + ";";
                    } else {
                        if (StringUtils.isNotBlank(opType) && StringUtils.isNotBlank(operator)) {
                            // 暂时只处理远程开门和操作者绑定
                            AccTransactionItem item =
                                accRTMonitorService.remoteOperateBindPerson(sn, doorNo, operator, opType);
                            // 上传门禁记录到云端最新调整：门禁记录不再上传至云端，调整为通过API接口方式到对应线下获取门禁记录 ----modify by zhixiong.huang 2021-08-20
                            // 10:33
                            // accCloudService.asyncPushTransactionToCloud(Collections.singletonList(item));
                        }
                    }
                }
            }
        }
        if (StringUtils.isNotBlank(dataMap.get("offline"))) {
            for (String doorName : dataMap.get("offline").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("common_dev_offlinePrompt");
            }
        }
        if (StringUtils.isNotBlank(dataMap.get("notSupport"))) {
            for (String doorName : dataMap.get("notSupport").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("acc_dev_devNotSupportFunction");
            }
        }
        if (StringUtils.isNotBlank(dataMap.get("notSupport"))) {
            for (String doorName : dataMap.get("notSupport").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("acc_dev_devNotSupportFunction");
            }
        }
        if (StringUtils.isNotBlank(dataMap.get("error"))) {
            msg += dataMap.get("error");
        }
        if (StringUtils.isNotBlank(msg)) {
            resultMsg.setRet("fail");
            resultMsg.setMsg(msg);
        }
        resultMsg.setData("");
        return resultMsg;
    }
}