/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 17:41 2018/12/29
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.park.service.ParkGetAccDoorService;
import com.zkteco.zkbiosecurity.park.vo.ParkGetAccDoorItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 获取门禁门（门禁当停车场）
 *
 * @version v1.0
 * @date Created In 17:41 2018/12/29
 */
@Service
public class ParkGetAccDoorServiceImpl implements ParkGetAccDoorService {

    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AuthUserService authUserService;

    /**
     * 获取门禁门数据分页以及搜索（门禁当停车场，所属设备必须支持一人多卡）
     *
     * @param sessionId
     * @param condition
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @date 2018/12/28 17:30
     */
    @Override
    public Pager getAccDoorForPark(String sessionId, ParkGetAccDoorItem condition, int page, int size) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        AccDoorItem accDoorItem = new AccDoorItem();
        ModelUtil.copyProperties(condition, accDoorItem);
        if (StringUtils.isNotBlank(userId)) {
            accDoorItem.setAreaIdIn(userId);
        }
        accDoorItem.setEnabled(true);
        Pager pager = accDoorService.getItemsByPage(accDoorItem, page, size);
        List<AccDoorItem> accDoorItemList = (List<AccDoorItem>)pager.getData();
        List<ParkGetAccDoorItem> parkGetAccDoorItemList = new ArrayList<>();
        for (AccDoorItem doorItem : accDoorItemList) {
            boolean isSupport = accDeviceOptionService.isSupportFunList(doorItem.getDeviceSn(), 7);
            if (isSupport) {
                ParkGetAccDoorItem parkGetAccDoorItem = new ParkGetAccDoorItem();
                parkGetAccDoorItem = ModelUtil.copyPropertiesIgnoreNull(doorItem, parkGetAccDoorItem);
                parkGetAccDoorItemList.add(parkGetAccDoorItem);
            }
        }
        if (accDoorItemList != null && accDoorItemList.size() > 0) {
            pager.setData(parkGetAccDoorItemList);
            pager.setTotal(parkGetAccDoorItemList.size());
        }
        return pager;
    }

    @Override
    public ParkGetAccDoorItem getAccDoorForParkById(String sessionId, String accDoorId) {
        AccDoorItem accDoorItem = accDoorService.getItemById(accDoorId);
        if (Objects.nonNull(accDoorItem)) {
            return ModelUtil.copyProperties(accDoorItem, new ParkGetAccDoorItem());
        }
        return null;
    }

    @Override
    public List<ParkGetAccDoorItem> getAccDoorForParkByIds(String sessionId, List<String> accDoorIds) {
        List<AccDoorItem> accDoorItemList = accDoorService.getItemsByIds(accDoorIds);
        if (Objects.nonNull(accDoorItemList) && accDoorItemList.size() > 0) {
            return ModelUtil.copyListProperties(accDoorItemList, ParkGetAccDoorItem.class);
        }
        return null;
    }
}
