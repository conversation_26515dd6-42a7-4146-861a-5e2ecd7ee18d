package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectDoorItem;
import com.zkteco.zkbiosecurity.att.service.AttGetAccDoorSelectService;
import com.zkteco.zkbiosecurity.att.service.AttGetAccDoorService;
import com.zkteco.zkbiosecurity.att.vo.Att4AccDoorSelect;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 门禁当作考勤时候门接口实现逻辑
 *
 * <AUTHOR>
 * @date 2018-11-19 17:44
 */
@Service
@Transactional
public class Att4AccDoorServiceImpl implements AttGetAccDoorService, AttGetAccDoorSelectService {
    @Autowired
    private AccDoorService accDoorService;

    @Override
	public Pager getAccDoorSelectList(Att4AccDoorSelect att4AccDoorSelect, int page, int size) {
		AccSelectDoorItem condition = new AccSelectDoorItem();
		ModelUtil.copyProperties(att4AccDoorSelect, condition);
		// 列表查询需要过滤被禁用的门
        condition.setEnabled(true);
		Pager pager = accDoorService.getItemsByPage(condition, page, size);
		//模型数据转化
        List<AccSelectDoorItem> accSelectDoorItems = (List<AccSelectDoorItem>) pager.getData();
        List<Att4AccDoorSelect> selectDoors = new ArrayList<>();
        Att4AccDoorSelect selectItem = null;
        for (AccSelectDoorItem item : accSelectDoorItems) {
            selectItem = new Att4AccDoorSelect();
            ModelUtil.copyProperties(item,selectItem);
            selectDoors.add(selectItem);
        }
        pager.setData(selectDoors);
        return pager;
	}
    
    @Override
    public List<Att4AccDoorSelect> getDoorsByIds(List<String> ids) {
        List<AccDoorItem> items =  accDoorService.getItemsByIds(ids);
        List<Att4AccDoorSelect> selectDoors = new ArrayList<>();
        Att4AccDoorSelect att4AccDoorSelect = null;
        for (AccDoorItem item : items) {
        	att4AccDoorSelect = new Att4AccDoorSelect();
            ModelUtil.copyProperties(item,att4AccDoorSelect);
            att4AccDoorSelect.setDoorName(item.getName());
            selectDoors.add(att4AccDoorSelect);
        }
        return selectDoors;
    }

    @Override
    public Att4AccDoorSelect getDoorByDevSnAndDoorNo(String deviceSn, Short doorNo) {
        AccDoorItem accDoorItem = accDoorService.getDoorByDevSnAndDoorNo(deviceSn, doorNo);
        Att4AccDoorSelect att4AccDoorSelect = null;
        if (Objects.nonNull(accDoorItem)) {
            att4AccDoorSelect = new Att4AccDoorSelect();
            ModelUtil.copyProperties(accDoorItem, att4AccDoorSelect);
            att4AccDoorSelect.setDoorName(accDoorItem.getName());
        }
        return att4AccDoorSelect;
    }

    @Override
	public Att4AccDoorSelect getDoorById(String doorId) {
		AccDoorItem doorItem = accDoorService.getItemById(doorId);
        Att4AccDoorSelect att4AccDoorSelect = null;
        if(Objects.nonNull(doorItem)) {
            att4AccDoorSelect = new Att4AccDoorSelect();
            ModelUtil.copyProperties(doorItem,att4AccDoorSelect);
            att4AccDoorSelect.setDoorName(doorItem.getName());
        }
        return att4AccDoorSelect;
	}
}
