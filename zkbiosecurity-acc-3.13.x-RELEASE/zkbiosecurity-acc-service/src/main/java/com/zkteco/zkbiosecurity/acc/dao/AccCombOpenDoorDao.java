/**
 * File Name: AccCombOpenDoor
 * Created by GenerationTools on 2018-03-14 下午03:02
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccCombOpenDoor;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import java.util.Collection;
import java.util.List;

/**
 * 对应百傲瑞达 AccCombOpenDoorDao
 * <AUTHOR>
 * @date:	2018-03-14 下午03:02
 * @version v1.0
 */
public interface AccCombOpenDoorDao extends BaseDao<AccCombOpenDoor, String> {
    AccCombOpenDoor findByName(String name);

    List<AccCombOpenDoor> findByAccDoor_IdIn(List<String> doorIdList);

    List<AccCombOpenDoor> findByAccDoor_Id(String doorId);

    /**
     * @Description:    根据名称进行in查询
     * @Author:         <PERSON>.huang
     * @CreateDate:     2018/12/13 11:26
     * @Version:        1.0
     */
    List<AccCombOpenDoor> findByNameIn(Collection<String> names);

    /**
     * @Description: 根据名称进行in查询
     * @Author: Abel.huang
     * @CreateDate: 2018/12/13 11:26
     * @Version: 1.0
     * @param businessIds
     */
    List<AccCombOpenDoor> findByBusinessIdIn(Collection<Long> businessIds);
}