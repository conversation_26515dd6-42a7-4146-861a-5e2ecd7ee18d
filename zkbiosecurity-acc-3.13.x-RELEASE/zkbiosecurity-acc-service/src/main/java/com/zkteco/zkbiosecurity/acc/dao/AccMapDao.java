/**
 * File Name: AccMap
 * Created by GenerationTools on 2018-03-20 下午02:07
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import com.zkteco.zkbiosecurity.acc.model.AccMap;

/**
 * 对应百傲瑞达 BaseMapDao
 * <AUTHOR>
 * @date:	2018-03-20 下午02:07
 * @version v1.0
 */
public interface AccMapDao extends BaseDao<AccMap, String> {
	AccMap findByName(String name);

	Long countByAuthAreaId(String areaId);
}