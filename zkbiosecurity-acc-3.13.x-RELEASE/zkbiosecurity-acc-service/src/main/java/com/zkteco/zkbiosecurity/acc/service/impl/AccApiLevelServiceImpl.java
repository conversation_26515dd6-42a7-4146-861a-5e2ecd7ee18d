package com.zkteco.zkbiosecurity.acc.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelAddDoorItem;
import com.zkteco.zkbiosecurity.acc.api.vo.AccApiLevelAddItem;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccLevelDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLevelDoorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLevelPersonDao;
import com.zkteco.zkbiosecurity.acc.model.AccLevelDoor;
import com.zkteco.zkbiosecurity.acc.model.AccLevelPerson;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTimeSegItem;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

@Service
@Transactional
public class AccApiLevelServiceImpl implements AccApiLevelService {
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccLevelDoorDao accLevelDoorDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccLevelPersonDao accLevelPersonDao;
    @Autowired
    private AccLevelDao accLevelDao;

    /**
     * 新增门禁权限组API接口
     *
     * @param accApiLevelAddItem:
     * @return
     * <AUTHOR>
     * @throws
     * @date 2024-01-15 10:02
     * @since 1.0.0
     */
    @Override
    public ApiResultMessage addApiLevel(AccApiLevelAddItem accApiLevelAddItem) {

        String levelName = accApiLevelAddItem.getName();
        String areaName = accApiLevelAddItem.getAreaName();
        String timeSegName = accApiLevelAddItem.getTimeSegName();
        // 权限组名称非空校验
        if (StringUtils.isBlank(accApiLevelAddItem.getName())) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELNAMENOTNULL,
                I18nUtil.i18nCode("acc_levelImport_nameNotNull"));
        }
        // 区域名称非空校验
        if (StringUtils.isBlank(accApiLevelAddItem.getAreaName())) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_AREANAMENOTNULL,
                I18nUtil.i18nCode("acc_api_areaNameNotNull"));
        }
        // 时间段名称非空校验
        if (StringUtils.isBlank(accApiLevelAddItem.getTimeSegName())) {
            return ApiResultMessage.message(AccConstants.Level_ACC_TIMESEGNAMENOTNULL,
                I18nUtil.i18nCode("acc_levelImport_timeSegNameNotNull"));
        }
        // 名称不能包含特殊字符
        if (PersRegularUtil.hasSpecialChar(levelName)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_NOSPECIALCHAR,
                I18nUtil.i18nCode("acc_levelImport_noSpecialChar"));
        }
        // 判断是否存在非法字符串
        if (PersRegularUtil.hasSqlKey(levelName)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_ERRORINFO,
                I18nUtil.i18nCode("common_xss_error_info"));
        }
        AccLevelItem acclevelItem = accLevelService.getItemByName(levelName);
        AuthAreaItem authAreaItem = authAreaService.getItemByName(areaName);
        AccTimeSegItem accTimeSegItem = accTimeSegService.getItemByName(timeSegName);
        // 判断区域名称是否存在
        if (authAreaItem == null) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_AREANOTEXIST,
                I18nUtil.i18nCode("acc_levelImport_areaNotExist"));
        }
        // 判断时间段名称是否存在，不存在则不进行操作
        if (accTimeSegItem == null) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_TIMESEGNOTEXIST,
                I18nUtil.i18nCode("acc_levelImport_timeSegNotExist"));
        }
        // 新增
        if (acclevelItem == null) {
            AccLevelItem item = new AccLevelItem();
            item.setName(levelName);
            item.setTimeSegId(accTimeSegItem.getId());
            item.setAuthAreaId(authAreaItem.getId());
            accLevelService.saveItem(item);
        } else {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LevelEXIST, I18nUtil.i18nCode("acc_api_levelExist"));
        }
        return ApiResultMessage.successMessage();
    }

    /**
     * API添加门禁权限组下加门接口
     *
     * @param accApiLevelAddDoorItem:
     * @return
     * <AUTHOR>
     * @throws
     * @date 2024-01-15 9:59
     * @since 1.0.0
     */
    @Override
    public ApiResultMessage addApiLevelDoor(AccApiLevelAddDoorItem accApiLevelAddDoorItem) {

        String doorName = accApiLevelAddDoorItem.getDoorName();
        String levelName = accApiLevelAddDoorItem.getLevelName();
        // 所加门下的权限组非空校验
        if (StringUtils.isBlank(levelName)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELNAMENOTNULL,
                I18nUtil.i18nCode("acc_levelImport_nameNotNull"));
        }
        // 门名称非空校验
        if (StringUtils.isBlank(doorName)) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_DOORNAMENOTNULL,
                I18nUtil.i18nCode("acc_api_doorNameNotNull"));
        }

        AccDoorItem accDoorItem = accDoorService.getItemByName(doorName);
        AccLevelItem accLevelItem = accLevelService.getItemByName(levelName);

        // 判断门是否存在
        if (accDoorItem == null) {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_NOTEXIST, I18nUtil.i18nCode("acc_api_doorNotExist"));
        }
        // 判空权限组
        if (accLevelItem == null) {
            return ApiResultMessage.message(AccConstants.LEVEL_NOTEXIST, I18nUtil.i18nCode("acc_api_levelNotExist"));
        }
        // 判断门和权限组之间的关系：数据库根据门和权限组去查找看是否存在；
        AccLevelDoor accLevelDoor =
            accLevelDoorDao.findByAccDoor_IdAndAccLevel_Id(accDoorItem.getId(), accLevelItem.getId());
        // 判断accLevelDoor是否存在，存在则提示，不存在则新增；
        if (accLevelDoor == null) {
            accLevelService.addLevelDoor(accLevelItem.getId(), StrUtil.strToList(accDoorItem.getId()));
        } else {
            return ApiResultMessage.message(AccConstants.ACC_DOOR_EXIST,
                I18nUtil.i18nCode("acc_levelImport_levelDoorExist", doorName));
        }
        return ApiResultMessage.successMessage();
    }

    /**
     * 新增批量添加人员门禁权限接口
     *
     * @param
     * @return
     * <AUTHOR>
     * @throws
     * @date 2024-01-17 14:09
     * @since 1.0.0
     */

    @Override
    public ApiResultMessage addApiPersonLevel(String pin, String levelId) {
        // 所加人员非空校验
        if (StringUtils.isBlank(pin)) {
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        // 权限组ID不为空
        if (StringUtils.isBlank(levelId)) {
            return ApiResultMessage.message(AccConstants.LEVEL_ACC_LEVELIDNOTNULL,
                I18nUtil.i18nCode("acc_api_levelIdNotNull"));
        }
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        // 判断人员是否存在
        if (persPersonItem == null) {
            return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        // 人员禁用不可编辑
        if (persPersonItem.getEnabledCredential() != null && !persPersonItem.getEnabledCredential()) {
            return ApiResultMessage.message(PersConstants.PERSON_DISABLED_NOPOP,
                I18nUtil.i18nCode("pers_person_disabledNotOp"));
        }

        AccLevelItem accLevelItem = accLevelService.getItemById(levelId);

        // 判断权限组是否存在
        if (accLevelItem == null) {
            return ApiResultMessage.message(AccConstants.LEVEL_NOTEXIST, I18nUtil.i18nCode("acc_api_levelNotExist"));
        }
        AccLevelPerson accLevelPerson =
            accLevelPersonDao.findByAccLevel_IdAndPersPersonId(levelId, persPersonItem.getId());
        // 增加
        if (accLevelPerson == null) {
            accLevelService.addPersonLevel(levelId, persPersonItem.getId());
        } else {
            return ApiResultMessage.message(AccConstants.ACC_LEVELPERSONEXIST,
                I18nUtil.i18nCode("acc_levelImport_levelPersonExist", levelId));
        }

        return ApiResultMessage.successMessage();

    }

}
