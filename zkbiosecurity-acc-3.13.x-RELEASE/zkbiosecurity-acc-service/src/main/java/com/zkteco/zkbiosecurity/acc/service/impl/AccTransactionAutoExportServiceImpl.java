package com.zkteco.zkbiosecurity.acc.service.impl;

import java.io.*;
import java.lang.reflect.Field;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ScheduledFuture;
import java.util.function.Function;
import java.util.zip.ZipOutputStream;

import org.apache.commons.collections.map.LinkedMap;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.RegionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionAutoExportService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.annotation.DateType;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.FileUtils;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import com.zkteco.zkbiosecurity.system.service.BaseSendMailService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date Created In 15:23 2019/11/4
 */
@Component
@Slf4j
public class AccTransactionAutoExportServiceImpl implements AccTransactionAutoExportService {

    /**
     * 大于这个大小就压缩 1mb
     */
    public final static int OUT_TO_ZIP_SIZE = 1048576;

    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private BaseSendMailService baseSendMailService;
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Value("${system.language:zh_CN}")
    private String sysLanguage;
    private ScheduledFuture<?> scedulefuture;

    @Override
    public void initAutoProcessorTime() {
        changeAutoProcessorTime();
    }

    @Override
    public void changeAutoProcessorTime() {
        try {
            String cron = createCron();
            changeAutoProcessorTime(cron);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
    }

    /**
     * 通过系统参数获取cron表达式
     *
     * @return cron
     */
    @Override
    public String createCron() {
        String cron = "";
        // 类型
        String frequency = baseSysParamService.getValByName("acc.autoExportFrequency");
        if (StringUtils.isNotBlank(frequency)) {
            // 按日
            if (frequency.equals("0")) {
                String hour = baseSysParamService.getValByName("acc.dayFrequencyHour");
                String minute = baseSysParamService.getValByName("acc.dayFrequencyMinute");
                // 范围符合
                if (checkInt(hour, 0, 23) && checkInt(minute, 0, 59)) {
                    cron = "0 " + minute + " " + hour + " * * ?";
                }
                // 按月
            } else if (frequency.equals("1")) {
                // 每月发送选项
                String frequencyChoose = baseSysParamService.getValByName("acc.monthFrequency");
                if (StringUtils.isNotBlank(frequencyChoose)) {
                    if (frequencyChoose.equals("0")) {
                        // 每月第一天凌晨过20秒发送
                        cron = "20 0 0 1 * ?";
                    } else if (frequencyChoose.equals("1")) {
                        String date = baseSysParamService.getValByName("acc.monthFrequencyDate");
                        if (checkInt(date, 1, 28)) {
                            cron = "20 0 0 " + date + " * ?";
                        }
                    }
                }
            }
        }
        return cron;
    }

    /**
     * 根据cron表达式修改定时任务
     *
     * @param cron
     *            表达式
     */
    @Override
    public void changeAutoProcessorTime(String cron) {
        String oldCron = baseSysParamService.getValByName("acc.autoExportCron");
        // 没有构建cron就是取消定时任务
        if (StringUtils.isBlank(cron)) {
            if (scedulefuture != null) {
                scedulefuture.cancel(true);
            }
            // 无的选项也记录一下cron
            baseSysParamService.saveValueByName("acc.autoExportCron", cron);
            return;
        }
        // 没有变化并且已存在一个定时直接退出
        if (cron.equals(oldCron) && scedulefuture != null) {
            return;
        } else {
            baseSysParamService.saveValueByName("acc.autoExportCron", cron);
        }
        if (scedulefuture != null) {
            scedulefuture.cancel(true);
        }
        log.info("accTransaction auto-export cron change:" + cron);
        scedulefuture = scheduleService.startScheduleTask(() -> doProcessor(), cron);
    }

    // ---------------私有方法--------------

    /**
     * 处理定时请求（被定时任务调用）
     */
    private void doProcessor() {
        try {
            AccTransactionItem item = new AccTransactionItem();
            log.info("start to accTransaction auto-export");
            boolean isOk = sendTransactionMail(item);
            if (!isOk) {
                log.error("accAutoExport:Automatic send failed");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage());
        }
    }

    /**
     * 通过门禁信息发
     *
     * @param item
     * @return
     */
    private boolean sendTransactionMail(BaseItem item) throws Exception {
        // 没有邮件地址直接退出
        String[] emails = getEmails();
        if (emails == null) {
            return true;
        }
        String strategy = baseSysParamService.getValByName("acc.exportMode");
        // 没有导出参数失败
        if (StringUtils.isBlank(strategy)) {
            return false;
        }
        // 缓存文件存放位置
        String uri = FileUtils.getLocalFullPath("") + FileUtils.separator + "cache" + FileUtils.separator + "acc"
            + FileUtils.separator + "accTransaction";
        // 邮件信息
        Map<String, String> mailInfo = getEmailInfo(strategy);
        // 文件名
        String fileName = mailInfo.get("fileName");
        String fileType = "xls";
        // 创建表格文件
        String excelPath = createExcel(item, uri, fileName, fileType, strategy);
        // 发送
        String mailSubject = mailInfo.get("mailSubject");
        String mailContent = mailInfo.get("mailContent");
        for (int i = 0; i < emails.length; i++) {
            if (StringUtils.isNotBlank(emails[i])) {
                baseSendMailService.sendSampleHtmlMail(emails[i], mailSubject, mailContent, excelPath);
            }
        }
        return true;
    }

    /**
     * 通过数据创建表格文件
     *
     * @param item
     *            数据实体类，用来获取注解构建excel列名
     * @param cachePath
     *            创建好的文件存放文件夹
     * @param fileName
     *            文件名
     * @param reportType
     *            文件类型
     * @param strategy
     *            export Model参数（从数据库中获取的）
     * @return 文件地址
     */
    private String createExcel(BaseItem item, String cachePath, String fileName, String reportType, String strategy)
        throws Exception {
        Path excelPath = Paths.get(cachePath);
        Path excelFilePath = Paths.get(excelPath.toString() + FileUtils.separator + fileName + "." + reportType);
        // 先清空再重新创建
        FileUtils.deleteDirectory(excelPath.toString());
        Files.createDirectories(excelPath);
        if (!Files.exists(excelFilePath)) {
            Files.createFile(excelFilePath);
        }
        String jsonColumn = getJsonColumByItem(item);
        try (OutputStream op = new FileOutputStream(excelFilePath.toFile())) {
            excelExport(page -> {
                List itemList = null;
                try {
                    itemList = getItemsByStrategy(page, strategy);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return itemList;
            }, item.getClass(), item, jsonColumn, fileName, op);
        }
        File excelFile = excelFilePath.toFile();
        // 文件太大压缩
        if (excelFile.length() > OUT_TO_ZIP_SIZE) {
            File excelZip = new File(excelPath.toString() + FileUtils.separator + fileName + ".zip");
            try (FileOutputStream zipOp = new FileOutputStream(excelZip)) {
                try (ZipOutputStream zipOutputStream = new ZipOutputStream(zipOp)) {
                    FileUtils.zipFile(excelFile, zipOutputStream);
                }
            }
            excelFilePath = excelZip.toPath();
        }
        return excelFilePath.toString();
    }

    /**
     * 通过定义的策略获取数据 分页
     * 
     * @param page
     *            第几页
     * @return
     * @throws ParseException
     */
    private List getItemsByStrategy(int page, String strategy) throws ParseException {
        // 每页300，最大100页（30000）
        if (page >= 100) {
            return null;
        }
        List items = null;
        switch (strategy) {
            // 按天（当天凌晨到导出时间内的信息）
            case "0":
                Date nowDate = getEarlyMorningTime(new Date());
                items = accTransactionService.getItemAfterTime(nowDate, page, 300);
                break;
            // 按月（上个月和本月导出时间内的信息）
            case "1":
                Date lastMonth = getLastMonth(new Date());
                lastMonth = getEarlyMorningTime(lastMonth);
                items = accTransactionService.getItemAfterTime(lastMonth, page, 300);
                break;
            // 所有信息
            case "2":
                int startIndex = page * 300;
                int endIndex = (page + 1) * 300 - 1;
                items = accTransactionService.getItemData(AccTransactionItem.class, new AccTransactionItem(),
                    startIndex, endIndex);
                break;
            default:
        }
        return items;
    }

    // 工具方法-----------

    /**
     * 获取某个日期凌晨的日期
     *
     * @param yesterdayDate
     * @return
     * @throws ParseException
     */
    private Date getEarlyMorningTime(Date yesterdayDate) throws ParseException {
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy-MM-dd");
        String yesterdayStr = dayFormat.format(yesterdayDate);
        yesterdayDate = dayFormat.parse(yesterdayStr);
        return yesterdayDate;
    }

    /**
     * 获取上个月第一天的日期
     *
     * @param date
     * @return
     */
    private Date getLastMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        // 设置为当前时间
        calendar.setTime(date);
        // 设置为上一个月
        calendar.set(Calendar.MONTH, calendar.get(Calendar.MONTH) - 1);
        // 设置为第一天
        calendar.set(Calendar.DATE, 1);
        date = calendar.getTime();
        return date;
    }

    /**
     * 通过对象的注解构建json头
     *
     * @param item
     * @return
     */
    public String getJsonColumByItem(BaseItem item) {
        Class target = item.getClass();
        // 创建有序的js对象
        JSONObject jsonObject = new JSONObject(new LinkedHashMap());
        // 用于排序的TreeSet
        TreeSet<Field> columns = new TreeSet<Field>((first, second) -> {
            int sort1 = first.getAnnotation(GridColumn.class).sortNo();
            int sort2 = second.getAnnotation(GridColumn.class).sortNo();
            return sort1 - sort2;
        });
        // 需要显示的属性加入TreeSet排序
        for (Field field : target.getDeclaredFields()) {
            field.setAccessible(true);
            if (field.isAnnotationPresent(GridColumn.class)) {
                String label = field.getAnnotation(GridColumn.class).label();
                if (StringUtils.isNotBlank(label)) {
                    String showExpression = field.getAnnotation(GridColumn.class).showExpression();
                    if (StringUtils.isNotBlank(showExpression) && showExpression.length() > 11) {
                        int indexOf = showExpression.indexOf("'");
                        if (indexOf != -1) {
                            String language = showExpression.substring(indexOf + 1, showExpression.length() - 1);
                            String operator =
                                showExpression.substring(showExpression.indexOf("#language") + 9, indexOf);
                            if (StringUtils.equals(operator, "=")) {
                                if (StringUtils.equals(sysLanguage, language)) {
                                    columns.add(field);
                                }
                            } else if (StringUtils.equals(operator, "!=")) {
                                if (!StringUtils.equals(sysLanguage, language)) {
                                    columns.add(field);
                                }
                            }
                        }
                    } else {
                        columns.add(field);
                    }
                }
            }
        }
        for (Field field : columns) {
            String label = field.getAnnotation(GridColumn.class).label();
            jsonObject.put(field.getName(), I18nUtil.i18nCode(label));
        }
        return jsonObject.toJSONString();
    }

    /**
     * str类型的数据是否在范围内
     *
     * @param v
     * @param begin
     *            开始（包括）
     * @param end
     *            结束（包括）
     * @return
     */
    private boolean checkInt(String v, int begin, int end) {
        if (StringUtils.isNotBlank(v)) {
            Integer intValue = Integer.valueOf(v);
            if (intValue != null) {
                if (intValue >= begin && intValue <= end) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取需要发送的所有 email
     * 
     * @return
     */
    private String[] getEmails() {
        String emailStr = baseSysParamService.getValByName("acc.autoExportEmail");
        if (StringUtils.isNotBlank(emailStr)) {
            emailStr = FoldexUtil.decryptByRandomSey(emailStr);
            emailStr = StringUtils.replace(emailStr, ",", ";");
            String[] emails = StringUtils.split(emailStr, ';');
            return emails;
        }
        return null;
    }

    // excel-------------

    public static <T> void excelExport(Function<Integer, List> callback, Class cls, BaseItem item, String jsonColumn,
        String fileName, OutputStream outputStream) throws IOException {
        Map map = null;
        OutputStream ops = new BufferedOutputStream(outputStream, 8192);
        Workbook workbook = null;
        workbook = new HSSFWorkbook();
        Sheet sheet = workbook.createSheet(fileName);
        JSONObject jsonObject = JSONObject.parseObject(jsonColumn, Feature.OrderedField);
        Map<String, Object> extraDataMap = new LinkedMap();
        List<String> fieldList = new ArrayList();
        String extraDataName = "";
        try {
            for (String s : jsonObject.keySet()) {
                Field field = cls.getDeclaredField(s);
                field.setAccessible(true);
                if (field.getAnnotation(GridColumn.class) != null
                    && !field.getAnnotation(GridColumn.class).isExportExcel()) {
                    continue;
                }
                if (("java.util.Map").equals(cls.getDeclaredField(s).getType().getTypeName())) {
                    extraDataMap = (Map<String, Object>)field.get(item);
                    extraDataName = s;
                    for (Map.Entry<String, Object> entry : extraDataMap.entrySet()) {
                        fieldList.add(entry.getKey());
                    }
                } else {
                    fieldList.add(s);
                }
            }
            // 格式
            CellStyle headStyle = workbook.createCellStyle();
            // 字体
            org.apache.poi.ss.usermodel.Font headFont = workbook.createFont();
            headFont.setFontName("ARIAL");
            headFont.setFontHeightInPoints((short)10);
            headFont.setBold(true);
            headStyle.setFont(headFont);
            headStyle.setAlignment(HorizontalAlignment.CENTER);
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setBorderBottom(BorderStyle.THIN);
            dataStyle.setBorderLeft(BorderStyle.THIN);
            dataStyle.setBorderTop(BorderStyle.THIN);
            dataStyle.setBorderRight(BorderStyle.THIN);
            dataStyle.setWrapText(true);
            dataStyle.setAlignment(HorizontalAlignment.CENTER);
            // 行
            Row row = sheet.createRow(0);
            // 单元格 A1
            Cell cell = row.createCell(0);
            cell.setCellStyle(headStyle);
            cell.setCellValue(fileName);
            // 合并第一行单元格
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, fieldList.size() - 1));
            int isMergedRegion = 2;
            // 列名
            Row columnRow = sheet.createRow(1);
            // Row columnRow3 = sheet.createRow(2);
            int mergedRow = 0;
            int mergedRowj = 0;
            for (int i = 0; i < fieldList.size(); i++) {
                String width = "";
                String columValue = "";
                String secHeader = "";
                if (!extraDataMap.containsKey(fieldList.get(i))) {
                    width = cls.getDeclaredField(fieldList.get(i)).getAnnotation(GridColumn.class).width();
                    columValue = cls.getDeclaredField(fieldList.get(i)).getAnnotation(GridColumn.class).label();
                    secHeader = cls.getDeclaredField(fieldList.get(i)).getAnnotation(GridColumn.class).secHeader();
                } else {
                    width = "90";
                    columValue = fieldList.get(i);
                }
                if (org.springframework.util.StringUtils.isEmpty(columValue)) {
                    columValue = cls.getDeclaredField(fieldList.get(i)).getName();
                }
                if (!org.springframework.util.StringUtils.isEmpty(width) && !"*".equals(width)) {
                    sheet.setColumnWidth(i, 50 * Integer.parseInt(width));
                }
                Cell columCell = columnRow.createCell(i);
                if (!"".equals(secHeader) && !"#cspan".equals(columValue)) {
                    // Cell columCell3 = columnRow3.createCell(i);
                    // columCell3.setCellStyle(dataStyle);
                    // columCell3.setCellValue(I18nUtil.i18nCode(secHeader));
                }
                if (!"".equals(secHeader) && "#cspan".equals(columValue)) {
                    mergedRow = mergedRow + 1;
                    mergedRowj = mergedRowj + 1;
                    // columCell = columnRow3.createCell(i);
                    columValue = secHeader;
                    isMergedRegion = 3;
                } else {
                    if (mergedRowj != 0) {
                        CellRangeAddress region = new CellRangeAddress(1, 1, i - mergedRowj - 1, i - 1);
                        sheet.addMergedRegion(region);
                        setBorderStyle(BorderStyle.THIN, region, sheet);
                    }
                    mergedRowj = 0;
                }
                if (mergedRowj != 0 && i == fieldList.size() - 1) {
                    CellRangeAddress region = new CellRangeAddress(1, 1, i - mergedRowj, i);
                    sheet.addMergedRegion(region);
                    setBorderStyle(BorderStyle.THIN, region, sheet);
                }
                if ("".equals(secHeader) && mergedRow != 0) {
                    CellRangeAddress region = new CellRangeAddress(1, 2, i, i);
                    sheet.addMergedRegion(region);
                    setBorderStyle(BorderStyle.THIN, region, sheet);
                }
                // 增加合并单元格的情况 end
                columCell.setCellStyle(dataStyle);
                columCell.setCellValue(I18nUtil.i18nCode(columValue));
            }
            String cellValue = "";
            List list;
            Integer page = 0;
            while ((list = callback.apply(page++)) != null) {
                if (list.size() < 1) {
                    break;
                }
                isMergedRegion = sheet.getLastRowNum() + 1;
                for (int i = 0; i < list.size(); i++) {
                    int newRowIndex = i + isMergedRegion;
                    Row dataRows = sheet.createRow(newRowIndex);
                    for (int j = 0; j < fieldList.size(); j++) {
                        Cell dataCell = dataRows.createCell(j);
                        dataCell.setCellStyle(dataStyle);
                        if (!extraDataMap.containsKey(fieldList.get(j))) {
                            Field field = cls.getDeclaredField(fieldList.get(j));
                            field.setAccessible(true);
                            Object cellObject = field.get(list.get(i));

                            if (!org.springframework.util.StringUtils.isEmpty(cellObject)) {
                                if (field.getType() == Date.class || field.getType() == Timestamp.class) {
                                    DateType dateType = field.getAnnotation(DateType.class);
                                    String format = getDateFormt(dateType);
                                    cellValue = new SimpleDateFormat(format).format(cellObject);
                                } else {
                                    cellValue = String.valueOf(cellObject);
                                    // 如果数据是key值，则需要国际化
                                    if (field.getAnnotation(GridColumn.class).i18n()) {
                                        cellValue = I18nUtil.i18nCode(cellValue);
                                    }
                                }
                            } else {
                                cellValue = "";
                            }
                        } else {
                            // 可扩展字段导出
                            Field field = cls.getDeclaredField(extraDataName);
                            field.setAccessible(true);
                            Map extraDataMaps = (Map<String, Object>)field.get(list.get(i));
                            cellValue = String.valueOf(extraDataMaps.get(fieldList.get(j)));
                        }
                        dataCell.setCellValue(cellValue);
                        if (org.springframework.util.StringUtils.isEmpty(cellValue) || "null".equals(cellValue)) {
                            dataCell.setCellValue("");
                        }
                    }
                }
            }
            workbook.write(ops);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ops.flush();
        ops.close();
        outputStream.close();
    }

    public static void setBorderStyle(BorderStyle border, CellRangeAddress region, Sheet sheet) {
        RegionUtil.setBorderBottom(border, region, sheet);// 下边框
        RegionUtil.setBorderLeft(border, region, sheet); // 左边框
        RegionUtil.setBorderRight(border, region, sheet); // 右边框
        RegionUtil.setBorderTop(border, region, sheet); // 上边框
    }

    public static String getDateFormt(DateType dateType) {
        String format = "yyyy-MM-dd HH:mm:ss";
        if (dateType != null) {
            switch (dateType.type()) {
                case "date":
                    format = "yyyy-MM-dd";
                    break;
                case "time":
                    format = "HH:mm:ss";
                    break;
                case "timestamp":
                    format = "yyyy-MM-dd HH:mm:ss";
                    break;
            }
        }
        return format;
    }

    private Map<String, String> getEmailInfo(String strategy) throws Exception {
        Map<String, String> info = new HashMap(4);
        String fileName, mailSubject, mailContent;
        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyyMMdd");
        mailSubject = I18nUtil.i18nCode("acc_autoExport_title");
        switch (strategy) {
            // 按天（当天凌晨到导出时间内的信息）
            case "0":
                SimpleDateFormat dayFormat = new SimpleDateFormat("yyyyMMddHHmm");
                Date now = new Date();
                Date nowDate = getEarlyMorningTime(new Date());
                fileName = I18nUtil.i18nCode("acc_autoExport_dailyMode") + "_" + dayFormat.format(nowDate) + "-"
                    + dayFormat.format(now);
                break;
            // 按月（上个月和本月导出时间内的信息）
            case "1":
                fileName = I18nUtil.i18nCode("acc_autoExport_monthlyMode") + "_" + monthFormat.format(new Date());
                break;
            // 所有信息
            case "2":
                fileName = I18nUtil.i18nCode("acc_autoExport_allMode") + "_" + monthFormat.format(new Date());
                break;
            default:
                fileName = I18nUtil.i18nCode("acc_autoExport_title") + monthFormat.format(new Date());
        }
        mailContent = fileName + " " + mailSubject;
        info.put("fileName", fileName);
        info.put("mailSubject", mailSubject);
        info.put("mailContent", mailContent);
        return info;
    }
}
