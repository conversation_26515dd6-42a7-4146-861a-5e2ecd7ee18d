package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.model.AccAuxIn;
import com.zkteco.zkbiosecurity.acc.service.AccAuxInService;
import com.zkteco.zkbiosecurity.acc.vo.AccAuxInItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.ivideo.service.IVideoGetAccAuxInService;
import com.zkteco.zkbiosecurity.ivideo.vo.IVideoGetAccAuxInItem;
import com.zkteco.zkbiosecurity.ivideo.vo.IVideoGetAccAuxInSelectItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import sun.swing.StringUIClientPropertyKey;

import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class IVideoGetAccAuxInServiceImpl implements IVideoGetAccAuxInService {
    @Autowired
    private AccAuxInService accAuxInService;

    @Override
    public Pager getItemByAuthFilter(String sessionId, IVideoGetAccAuxInSelectItem iVideoGetAccAuxInSelectItem, int page, int size) {
        AccAuxInItem auxInItem = new AccAuxInItem();
        auxInItem.setName(iVideoGetAccAuxInSelectItem.getName());
        auxInItem.setDevSn(iVideoGetAccAuxInSelectItem.getDevSn());
        auxInItem.setInId(iVideoGetAccAuxInSelectItem.getInId());
        auxInItem.setNotInId(iVideoGetAccAuxInSelectItem.getNotInId());
        auxInItem.setSortName(iVideoGetAccAuxInSelectItem.getSortName());
        Pager pager = accAuxInService.loadPagerByAuthFilter(sessionId, auxInItem, page, size);
        List<AccAuxInItem> accAuxInItemList = (List<AccAuxInItem>)pager.getData();
        List<IVideoGetAccAuxInSelectItem> items = new ArrayList<IVideoGetAccAuxInSelectItem>();
        items = ModelUtil.copyListProperties(accAuxInItemList, IVideoGetAccAuxInSelectItem.class);
        pager.setData(items);
        return pager;
    }

    @Override
    public List<IVideoGetAccAuxInItem> getAccAuxInByIdsIn(String auxInIds) {
        List<AccAuxInItem> accAuxInItems = accAuxInService.getItemsByIds(StrUtil.strToList(auxInIds));
        List<IVideoGetAccAuxInItem> items = new ArrayList<IVideoGetAccAuxInItem>();
        items = ModelUtil.copyListProperties(accAuxInItems, IVideoGetAccAuxInItem.class);
        return items;
    }
}
