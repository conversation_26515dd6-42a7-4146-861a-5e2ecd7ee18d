package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.service.Acc4HepTransactionService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4HepTransactionItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;

/**
 * <AUTHOR>
 * @date 2020-08-07 15:29
 */
@Service
@Transactional
public class Acc4HepTransactionServiceImpl implements Acc4HepTransactionService {
    @Autowired
    private AccTransactionService accTransactionService;

    @Override
    public Pager hepGetAccTransactionList(Acc4HepTransactionItem acc4HepTransactionItem, int page, int size) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        accTransactionItem.setDevSnEqual(acc4HepTransactionItem.getDevSn());
        if (acc4HepTransactionItem.getStartCreateTime() != null) {
            accTransactionItem.setStartCreateTime(acc4HepTransactionItem.getStartCreateTime());
        }
        accTransactionItem.setEndCreateTime(acc4HepTransactionItem.getEndCreateTime());
        accTransactionItem.setInEventNo(acc4HepTransactionItem.getEventNoIn());
        Pager pager = accTransactionService.getItemsByPage(accTransactionItem, page, size);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>)pager.getData();
        List<Acc4HepTransactionItem> acc4HepTransactionItems = new ArrayList<>();
        if (!accTransactionItemList.isEmpty() && accTransactionItemList.size() > 0) {
            acc4HepTransactionItems =
                ModelUtil.copyListProperties(accTransactionItemList, Acc4HepTransactionItem.class);
        }
        pager.setData(acc4HepTransactionItems);
        return pager;
    }

    @Override
    public Long countAccTransactionByCondition(Acc4HepTransactionItem acc4HepTransactionItem) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        accTransactionItem.setDevSn(acc4HepTransactionItem.getDevSn());
        if (acc4HepTransactionItem.getStartCreateTime() != null) {
            accTransactionItem.setStartCreateTime(acc4HepTransactionItem.getStartCreateTime());
        }
        accTransactionItem.setEndCreateTime(acc4HepTransactionItem.getEndCreateTime());
        accTransactionItem.setInEventNo(acc4HepTransactionItem.getEventNoIn());
        return accTransactionService.countAccTransactionByCondition(accTransactionItem);
    }
}
