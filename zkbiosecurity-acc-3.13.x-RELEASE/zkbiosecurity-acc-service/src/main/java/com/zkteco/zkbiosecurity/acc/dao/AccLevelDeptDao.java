package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccLevelDept;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 权限组-部门 中间表Dao
 * <AUTHOR>
 * @date 2018/3/22 18:04
 */
public interface AccLevelDeptDao extends BaseDao<AccLevelDept, String> {

    @Modifying
    @Query(value = "delete from AccLevelDept e where e.accLevel.id in (?2) and e.deptId = ?1")
    void delDeptLevel(String deptId, List<String> levelIdList);

    List<AccLevelDept> findByDeptId(String deptId);

    @Query(value = "select e.accLevel.id from AccLevelDept e where e.deptId = ?1")
    List<String> getLevelIdsByDeptId(String deptId);

    @Modifying
    @Query(value = "delete from AccLevelDept e where e.accLevel.id in (?1)")
    void deleteByLevelIds(List<String> levelIds);

    /**
     * 根据权限id以及部门id查询出权限组的部门对象
     * <AUTHOR>
     * @Date 2018/12/18 10:56
     * @param
     * @return
     */
    AccLevelDept getByAccLevel_IdAndDeptId(String levelId, String deptId);
}
