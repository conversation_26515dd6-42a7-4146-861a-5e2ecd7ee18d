/**
 * File Name: AccLinkageTrigger
 * Created by GenerationTools on 2018-03-16 下午04:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 AccLinkageTrigger
 * <AUTHOR>
 * @date:	2018-03-16 下午04:41
 * @version v1.0
 */
@Entity
@Table(name = "ACC_LINKAGE_TRIGGER")
@Getter
@Setter
@Accessors(chain=true)
public class AccLinkageTrigger extends BaseModel implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**  */
	@Column(name="TRIGGER_COND",nullable=false)
	private Short triggerCond;

	/**  */
	@Column(name="LINKAGE_INDEX",nullable=false)
	private Integer linkageIndex;

	/**  */
	@ManyToOne
	@JoinColumn(name="LINKAGE_ID",nullable = false)
	private AccLinkage accLinkage;

	/**  */
	@ManyToOne
	@JoinColumn(name="LINKAGE_INOUT_ID",nullable = false)
	private AccLinkageInOut accLinkageInOut;

	public AccLinkageTrigger(){
	}

	public AccLinkageTrigger(AccLinkage linkage, AccLinkageInOut linkageInOut, Integer linkageIndex, Short triggerCond)
	{
		this.accLinkage = linkage;
		this.accLinkageInOut = linkageInOut;
		this.triggerCond = triggerCond;
		this.linkageIndex = linkageIndex;
	}
}