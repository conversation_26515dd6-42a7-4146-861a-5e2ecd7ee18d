package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.service.AccReaderService;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.patrol.service.PatrolGetAccReaderService;
import com.zkteco.zkbiosecurity.patrol.vo.PatrolGetAccReaderItem;

@Service
@Transactional
public class PatrolGetAccReaderServiceImpl implements PatrolGetAccReaderService {

	@Autowired
	private AccReaderService accReaderService;
	@Override
	public PatrolGetAccReaderItem getAccReader(PatrolGetAccReaderItem patrolGetAccReaderItem) {
		AccReaderItem accReaderItem = accReaderService.getItemById(patrolGetAccReaderItem.getId());
		patrolGetAccReaderItem.setDoorId(accReaderItem.getDoorId());
		patrolGetAccReaderItem.setDoorName(accReaderItem.getDoorName());
		patrolGetAccReaderItem.setName(accReaderItem.getName());
		patrolGetAccReaderItem.setReaderNo(accReaderItem.getReaderNo());
		return patrolGetAccReaderItem;
	}
	@Override
	public List<PatrolGetAccReaderItem> getItemListByDevId(String deviceId) {
		List<AccReaderItem> accReaderItems = accReaderService.getItemListByDevId(deviceId);
		List<PatrolGetAccReaderItem> patrolGetAccReaderItems = new ArrayList<PatrolGetAccReaderItem>();
		patrolGetAccReaderItems = ModelUtil.copyListProperties(accReaderItems, PatrolGetAccReaderItem.class);
		return patrolGetAccReaderItems;
	}

	@Override
	public List<PatrolGetAccReaderItem> getItemListByDevSnIn(List<String> sns) {
		List<AccReaderItem> accReaderItems = accReaderService.getItemListByDevSnIn(sns);
		List<PatrolGetAccReaderItem> patrolGetAccReaderItems = new ArrayList<PatrolGetAccReaderItem>();
		PatrolGetAccReaderItem patrolGetAccReaderItem = null;
		for (AccReaderItem accReaderItem : accReaderItems) {
			patrolGetAccReaderItem = new PatrolGetAccReaderItem();
			patrolGetAccReaderItem.setId(accReaderItem.getId());
			patrolGetAccReaderItem.setName(accReaderItem.getName());
			patrolGetAccReaderItem.setReaderNo(accReaderItem.getReaderNo());
			patrolGetAccReaderItem.setDevSn(accReaderItem.getDeviceSn());
			patrolGetAccReaderItems.add(patrolGetAccReaderItem);
		}
		return patrolGetAccReaderItems;
	}
}
