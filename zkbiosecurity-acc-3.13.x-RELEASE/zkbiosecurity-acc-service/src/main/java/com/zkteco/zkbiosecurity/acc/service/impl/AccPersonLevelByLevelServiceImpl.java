package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccLevelDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLevelPersonDao;
import com.zkteco.zkbiosecurity.acc.enums.AccLinkTypeEnum;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonListItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/3/14 11:12
 */
@Service
@Transactional
public class AccPersonLevelByLevelServiceImpl implements AccPersonLevelByLevelService {

    private Logger logger = LoggerFactory.getLogger(AccPersonLevelByLevelServiceImpl.class);

    @Autowired
    private AccLevelDao accLevelDao;
    @Autowired
    private AccLevelPersonDao accLevelPersonDao;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthUserService authUserService;

    @Override
    public List<AccPersonLevelByLevelItem> getByCondition(AccPersonLevelByLevelItem condition) {
        return (List<AccPersonLevelByLevelItem>)accLevelDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accLevelDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accLevelDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccPersonLevelByLevelItem getItemById(String id) {
        List<AccPersonLevelByLevelItem> items = getByCondition(new AccPersonLevelByLevelItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public ZKResultMsg delPerson(String levelIds, String personIds) {
        accLevelService.immeDelPersonLevel((List<String>)CollectionUtil.strToList(levelIds), personIds);
        Long personCount = accLevelPersonDao.countByAccLevel_Id(levelIds);
        personCount = personCount == null ? Long.valueOf(0) : personCount;
        return new ZKResultMsg(personCount);
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccPersonLevelByLevelItem condition, int pageNo,
        int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager pager = getItemsByPage(condition, pageNo, pageSize);
        List<AccPersonLevelByLevelItem> levelItems = (List<AccPersonLevelByLevelItem>)pager.getData();
        String areaIds = CollectionUtil.getPropertys(levelItems, AccPersonLevelByLevelItem::getAuthAreaId);
        List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> itemMap = CollectionUtil.itemListToIdMap(authAreaItemList);
        for (AccPersonLevelByLevelItem accLevelItem : levelItems) {
            accLevelItem.setAuthAreaName(itemMap.get(accLevelItem.getAuthAreaId()).getName());
        }
        return pager;
    }

    @Override
    public List<AccPersonListItem> getExportItemList(String sessionId, AccPersonListItem accPersonListItem,
        int beginIndex, int endIndex) {
        accPersonListItem.setModelType(AccLinkTypeEnum.ACC_LEVEL.name());
        return accPersonService.getExportPersonItemList(sessionId, accPersonListItem, beginIndex, endIndex);
    }

    @Override
    public ZKResultMsg getPersonCount(String sessionId, String levelId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        SecuritySubject subject = authSessionProvider.getSecuritySubject(sessionId);
        if (!subject.getIsSuperuser() && subject.getDepartmentIds() != null && subject.getDepartmentIds().size() > 0) {
            List<PersPersonItem> persPersonItemList =
                persPersonService.getPersPersonByDeptIds(subject.getDepartmentIds());
            zkResultMsg.setData(accLevelPersonDao.countByAccLevel_IdAndPersPersonIdIn(levelId,
                CollectionUtil.getItemIdsList(persPersonItemList)));
            return zkResultMsg;
        }
        zkResultMsg.setData(accLevelPersonDao.countByAccLevel_Id(levelId));
        return zkResultMsg;
    }
}
