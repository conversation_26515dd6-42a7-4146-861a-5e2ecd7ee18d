/**
 * File Name: Acc<PERSON><PERSON><PERSON><PERSON><PERSON>erson
 * Created by GenerationTools on 2018-03-14 下午03:02
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccCombOpenPerson;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import java.util.Collection;
import java.util.List;

/**
 * 对应百傲瑞达 AccCombOpenPersonDao
 * <AUTHOR>
 * @date:	2018-03-14 下午03:02
 * @version v1.0
 */
public interface AccCombOpenPersonDao extends BaseDao<AccCombOpenPerson, String> {

    AccCombOpenPerson findByName(String name);

    /**
     * @Description:    根据名称进行in查询
     * @Author:         Abel.huang
     * @CreateDate:     2018/12/13 11:26
     * @Version:        1.0
     */
    List<AccCombOpenPerson> findByNameIn(Collection<String> names);

    /**
     * @Description:    根据名称进行in查询
     * @Author:         Abel.huang
     * @CreateDate:     2018/12/13 11:26
     * @Version:        1.0
     * @param businessId
     */
    List<AccCombOpenPerson> findByBusinessIdIn(Collection<Long> businessId);

}