package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccFirstOpen;
import com.zkteco.zkbiosecurity.acc.model.AccPersonFirstOpen;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import java.util.Collection;
import java.util.List;

public interface AccPersonFirstOpenDao extends BaseDao<AccPersonFirstOpen, String> {

    List<AccPersonFirstOpen> findByAccFirstOpenAndPersPersonIdIn(AccFirstOpen accFirstOpen, List<String> personIds);

    List<AccPersonFirstOpen> findByPersPersonIdIn(List<String> personIds);

    /**
     * @Description: 获取首人常开的人员数量
     *
     * @author: mingfa.zheng
     * @date: 2018/7/17 11:12
     * @param: [firstOpenId]
     * @return: java.lang.Long
     **/
    Long countByAccFirstOpen_Id(String firstOpenId);

    Long countByAccFirstOpen_IdAndPersPersonIdIn(String firstOpenId, Collection<String> personIds);

    /**
     * 根据首人开门id查询
     *
     * @param firstOpenId:首人开门id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccPersonFirstOpen>
     * <AUTHOR>
     * @date 2021-01-29 15:38
     * @since 1.0.0
     */
    List<AccPersonFirstOpen> findByAccFirstOpen_Id(String firstOpenId);

    /**
     * 根据首人常开id和人员id集合查询
     *
     * @param firstOpenId:首人常开id
     * @param personIds:人员id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccPersonFirstOpen>
     * <AUTHOR>
     * @date 2021-01-29 15:48
     * @since 1.0.0
     */
    List<AccPersonFirstOpen> findByAccFirstOpen_IdAndPersPersonIdIn(String firstOpenId, List<String> personIds);
}
