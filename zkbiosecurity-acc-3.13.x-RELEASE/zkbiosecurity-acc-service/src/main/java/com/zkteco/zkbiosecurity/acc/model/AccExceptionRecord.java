package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.util.Date;

/**
 * 异常记录实体类
 * 
 * @author: AI Assistant
 * @date: 2025-07-21
 */
@Entity
@Table(name = "ACC_EXCEPTION_RECORD")
@Getter
@Setter
public class AccExceptionRecord  extends BaseModel {

    /** 主键 */
    @Id
    @Column(name = "ID", length = 32)
    private String id;

    /** 工号 */
    @Column(name = "PIN", length = 50)
    private String pin;

    /** 姓名 */
    @Column(name = "NAME", length = 100)
    private String name;

    /** 部门名称 */
    @Column(name = "DEPT_NAME", length = 100)
    private String deptName;

    /** 部门编号 */
    @Column(name = "DEPT_CODE", length = 50)
    private String deptCode;

    /** 接收人员职位 */
    @Column(name = "RECEIVER_POSITION", length = 100)
    private String receiverPosition;

    /** 读头名称 */
    @Column(name = "READER_NAME", length = 100)
    private String readerName;

    /** 读头ID */
    @Column(name = "READER_ID", length = 32)
    private String readerId;

    /** 进入时间 */
    @Column(name = "ENTER_TIME")
    private Date enterTime;

    /** 外出时间 */
    @Column(name = "EXIT_TIME")
    private Date exitTime;

    /** 主题（异常进出、迟到） */
    @Column(name = "SUBJECT", length = 100)
    private String subject;

    /** 异常状态（未闭环、已返回） */
    @Column(name = "EXCEPTION_STATUS", length = 50)
    private String exceptionStatus;

    /** 发送时间 */
    @Column(name = "SEND_TIME")
    private Date sendTime;

    /** 状态 1=发送成功,2=发送失败,3=不需要发送 */
    @Column(name = "STATUS")
    private Short status;

    /** 错误信息 */
    @Column(name = "ERROR_MESSAGE", length = 500)
    private String errorMessage;

    /** 推送微信错误信息*/
    @com.zkteco.zkbiosecurity.base.annotation.Column(name = "PUSH_ERROR_MESSAGE")
    private String pushErrorMessage;

    /** 区域名称 */
    @Column(name = "AREA_NAME", length = 100)
    private String areaName;

    /** 备注 */
    @Column(name = "REMARK", length = 500)
    private String remark;

    @com.zkteco.zkbiosecurity.base.annotation.Column(name = "t.SN")
    private String sn;


    /**
     * 默认构造方法
     */
    public AccExceptionRecord() {
        super();
    }

    /**
     * 带ID的构造方法
     * 
     * @param id
     */
    public AccExceptionRecord(String id) {
        this.id = id;
    }
}
