/**
 * File Name: AccLinkageIndex
 * Created by GenerationTools on 2018-03-16 下午04:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccLinkageIndex;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccLinkageIndexDao
 * <AUTHOR>
 * @date:	2018-03-16 下午04:41
 * @version v1.0
 */
public interface AccLinkageIndexDao extends BaseDao<AccLinkageIndex, String> {

    //accLinkageIndexBiz.get(accLinkage.getDevice().getId());//获取max index
    AccLinkageIndex findByAccDevice_Id(String deviceId);
}