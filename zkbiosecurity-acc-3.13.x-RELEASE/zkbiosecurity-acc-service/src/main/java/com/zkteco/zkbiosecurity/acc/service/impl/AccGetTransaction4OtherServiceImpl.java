package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccGetTransaction4OtherService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccTransaction4OtherItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/11 15:00
 * @since 1.0.0
 */
@Service
public class AccGetTransaction4OtherServiceImpl implements AccGetTransaction4OtherService {

    @Autowired
    private AccTransactionService accTransactionService;

    @Override
    public Pager getAccTransactionList(AccTransaction4OtherItem accTransaction4OtherItem, int page, int size) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        ModelUtil.copyPropertiesIgnoreNull(accTransaction4OtherItem, accTransactionItem);
        Pager pager = accTransactionService.getItemsByPage(accTransactionItem, page, size);
        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>)pager.getData();
        List<AccTransaction4OtherItem> accTransaction4OtherItems = new ArrayList<>();
        if (!accTransactionItemList.isEmpty() && accTransactionItemList.size() > 0) {
            accTransaction4OtherItems =
                ModelUtil.copyListProperties(accTransactionItemList, AccTransaction4OtherItem.class);
        }
        pager.setData(accTransaction4OtherItems);
        return pager;
    }

    @Override
    public Long countAccTransactionByCondition(AccTransaction4OtherItem accTransaction4OtherItem) {
        AccTransactionItem accTransactionItem = new AccTransactionItem();
        ModelUtil.copyPropertiesIgnoreNull(accTransaction4OtherItem, accTransactionItem);
        return accTransactionService.countAccTransactionByCondition(accTransactionItem);
    }
}
