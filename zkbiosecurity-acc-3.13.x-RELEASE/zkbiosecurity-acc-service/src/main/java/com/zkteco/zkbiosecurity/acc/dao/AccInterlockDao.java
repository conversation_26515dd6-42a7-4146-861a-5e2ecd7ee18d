/**
 * File Name: AccInterlock Created by GenerationTools on 2018-03-13 上午09:53 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.model.AccInterlock;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Query;

/**
 * 对应百傲瑞达 AccInterlockDao
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午09:53
 * @version v1.0
 */
public interface AccInterlockDao extends BaseDao<AccInterlock, String> {
    // AccInterlock findByAccDevice_Id(String devId);

    /**
     * 查询是否存在名字
     *
     * @param name
     * @return
     */
    boolean existsByName(String name);

    List<AccInterlock> findByAccDevice_Id(String devId);

    /**
     * 查询当前最大业务id
     *
     * @return
     */
    @Query("SELECT MAX(t.businessId) FROM AccInterlock t")
    Long findMaxBusinessId();
}