package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccExceptionRecord;
import com.zkteco.zkbiosecurity.acc.model.AccInOutRecord;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

/**
 * 异常记录DAO接口
 * 
 * @author: AI Assistant
 * @date: 2025-07-21
 */
public interface AccInOutRecordDao extends BaseDao<AccInOutRecord, String> {



    /**
     * 查询未闭环的异常记录
     *
     * @return
     */
    @Query(value = "SELECT * FROM ACC_IN_OUT_RECORD  WHERE (exception_Status = '2' or exception_Status is null) and pin =?1 and SUBJECT='1'  ORDER BY create_Time DESC limit 1",nativeQuery = true)
    List<AccInOutRecord> findUnclosedRecordsAndIsNullAndPin(String pin);

    @Query(value = "SELECT * FROM Acc_Exception_Record  WHERE (exception_Status = '2' or exception_Status is null)  and SUBJECT='1'  ",nativeQuery = true)
    List<AccInOutRecord> findUnclosedRecordsAndIsNull();
}
