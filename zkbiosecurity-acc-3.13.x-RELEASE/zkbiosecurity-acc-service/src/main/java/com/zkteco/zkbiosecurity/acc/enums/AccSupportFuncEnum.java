package com.zkteco.zkbiosecurity.acc.enums;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;

/**
 * 门禁功能枚举
 *
 * <AUTHOR>
 * @date 2021-02-24 17:47
 * @since 1.0.0
 */
public enum AccSupportFuncEnum {
    /** 后台验证参数 Access不支持 */
    ISSUE_BG_VERIFY(BaseConstants.ZKBIO_ACCESS, BaseConstants.ZKBIOCV_SECURITY_FOUNDATION),
    /** 远程锁定门 Access不支持 */
    LOCK_DOOR(BaseConstants.ZKBIO_ACCESS),
    /** LCD实时监控 Access不支持 */
    LCD_RT_MONITOR(BaseConstants.ZKBIO_ACCESS),
    /** 电子地图-添加摄像头 Access不支持 */
    MAP_ADD_CHANNEL(BaseConstants.ZKBIO_ACCESS),
    /** 验证方式规则 Access不支持 */
    VERIFY_MODE_RULE(BaseConstants.ZKBIO_ACCESS, BaseConstants.ZKBIOCV_SECURITY_FOUNDATION),
    /** 人员延时通行功能 Access不支持 */
    PERS_DELAY_PASS(BaseConstants.ZKBIO_ACCESS),
    /** 高级门禁 Access不支持 */
    ACC_ADVANCED(BaseConstants.ZKBIO_ACCESS, BaseConstants.ZKBIOCV_SECURITY_FOUNDATION),
    /** 车闸icon Access不支持 */
    CAR_GATE_ICON(BaseConstants.ZKBIO_ACCESS);

    /** 不支持此功能的产品编码 */
    private String[] notSupportProduct;

    /**
     * 指定不支持此功能的产品
     *
     * @param notSupportProduct
     */
    AccSupportFuncEnum(String... notSupportProduct) {
        this.notSupportProduct = notSupportProduct;
    }

    /**
     * 判断产品是否支持此功能
     *
     * @return
     */
    public boolean isSupport(String productCode) {
        for (String excludeProduct : notSupportProduct) {
            if (excludeProduct.equals(productCode)) {
                return false;
            }
        }
        return true;
    }
}
