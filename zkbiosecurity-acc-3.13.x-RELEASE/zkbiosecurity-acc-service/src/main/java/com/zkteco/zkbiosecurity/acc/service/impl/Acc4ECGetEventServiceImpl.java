package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.event.service.OtherGetEvent4EventCenterService;
import com.zkteco.zkbiosecurity.event.vo.EventCenterTypeItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class Acc4ECGetEventServiceImpl implements OtherGetEvent4EventCenterService {

    @Override
    public List<EventCenterTypeItem> getEventTypeItems() {
        List<EventCenterTypeItem> eventCenterTypeItemList = new ArrayList<>();
        try {
            Resource resource = new ClassPathResource("acc-event.xml");
            if (!resource.exists()) {
                return null;
            }
            BufferedReader br =
                new BufferedReader(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8));
            StringBuffer buffer = new StringBuffer();
            String line = "";
            while ((line = br.readLine()) != null) {
                buffer.append(line);
            }
            br.close();
            if (StringUtils.isBlank(buffer)) {
                return null;
            }

            // 解析xml内容
            Document doc = DocumentHelper.parseText(buffer.toString());
            if (doc == null) {
                return null;
            }

            // 获取禁用的权限
            Element root = doc.getRootElement();
            if (root != null && root.elements() != null) {
                EventCenterTypeItem event = null;
                for (Element element : root.elements()) {
                    event = new EventCenterTypeItem();
                    event.setSourceModule(ConstUtil.SYSTEM_MODULE_ACC);
                    event.setTypeCode(element.elementText("code"));
                    event.setTypeName(element.elementText("key"));
                    event.setTypeLevelVaL(element.elementText("level"));
                    event.setLinkage(Boolean.parseBoolean(element.elementText("isLinkage")));
                    eventCenterTypeItemList.add(event);
                }
            }
        } catch (Exception e) {
            log.error("Init acc event error", e);
        }
        return eventCenterTypeItemList;
    }
}
