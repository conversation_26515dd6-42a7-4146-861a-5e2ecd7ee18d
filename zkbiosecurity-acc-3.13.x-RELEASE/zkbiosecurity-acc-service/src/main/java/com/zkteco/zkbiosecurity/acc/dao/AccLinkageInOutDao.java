/**
 * File Name: AccLinkageInOut
 * Created by GenerationTools on 2018-03-16 下午04:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import com.zkteco.zkbiosecurity.acc.model.AccLinkageInOut;

import java.util.List;

/**
 * 对应百傲瑞达 AccLinkageInOutDao
 * <AUTHOR>
 * @date:	2018-03-16 下午04:41
 * @version v1.0
 */
public interface AccLinkageInOutDao extends BaseDao<AccLinkageInOut, String> {

    List<AccLinkageInOut> findByAccLinkage_Id(String accLinkageId);
}