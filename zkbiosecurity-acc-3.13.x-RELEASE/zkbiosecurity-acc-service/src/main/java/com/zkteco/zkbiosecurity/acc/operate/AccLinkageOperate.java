package com.zkteco.zkbiosecurity.acc.operate;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccLinkageIasDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLinkageMediaDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLinkageVidDao;
import com.zkteco.zkbiosecurity.acc.model.AccLinkageIas;
import com.zkteco.zkbiosecurity.acc.model.AccLinkageMedia;
import com.zkteco.zkbiosecurity.acc.model.AccLinkageVid;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.hep.service.Hep4OtherTransactionService;
import com.zkteco.zkbiosecurity.ias.service.Ias4OtherService;
import com.zkteco.zkbiosecurity.line.service.Line4OtherService;
import com.zkteco.zkbiosecurity.sms.modem.service.SmsModem4OtherService;
import com.zkteco.zkbiosecurity.system.service.BaseSendMailService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.vdb.service.Vdb4OtherService;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidLinkageEventService;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidLinkageService;
import com.zkteco.zkbiosecurity.whatsapp.service.Whatsapp4OtherService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class AccLinkageOperate {

    @Autowired
    private AccLinkageVidDao accLinkageVidDao;
    @Autowired
    private AccLinkageMediaDao accLinkageMediaDao;
    @Autowired
    private AccLinkageIasDao accLinkageIasDao;
    @Autowired
    private AccLinkageVidOperate accLinkageVidOperate;
    @Autowired(required = false)
    private Vid4OtherGetVidLinkageService vid4OtherGetVidLinkageService;
    @Autowired(required = false)
    private Vid4OtherGetVidLinkageEventService Vid4OtherGetVidLinkageEventService;
    @Autowired
    private BaseSendMailService baseSendMailService;
    @Autowired(required = false)
    private SmsModem4OtherService smsModem4OtherService;
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired(required = false)
    private Hep4OtherTransactionService hep4OtherTransactionService;
    @Autowired(required = false)
    private Line4OtherService line4OtherService;
    @Autowired(required = false)
    private Whatsapp4OtherService whatsapp4OtherService;
    @Autowired(required = false)
    private Ias4OtherService ias4OtherService;
    @Autowired(required = false)
    private Vdb4OtherService vdb4OtherService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    /**
     * 执行软联动，例如发送邮件、视频联动
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/13 8:55
     * @param linkageId 联动id
     * @param lastTransaction 上一条触发联动的事件
     * @param newTransaction 当前事件
     * @return
     */
    public ZKResultMsg executeExtendLinkage(String linkageId, AccTransactionItem lastTransaction,
        AccTransactionItem newTransaction) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();// 用于存放触发联动后所做哪些联动动作
        // 视频联动
        Map<String, Object> handle = null;// 视频联动句柄
        List<AccLinkageVid> accLinkageVidList = accLinkageVidDao.findByAccLinkage_Id(linkageId);
        if (accLinkageVidList != null) {
            handle = executeVideoLinkage(accLinkageVidList, lastTransaction);
        }
        // Digifort联动
        List<String> linkageDigifortList =
            accLinkageMediaDao.getMediaContentByAccLinkageIdAndMediaType(linkageId, AccConstants.DIGIFORT);
        if (!linkageDigifortList.isEmpty()) {
            if (handle == null) {
                handle = new HashMap<>();
            }
            handle.put("digifortEventNames", StringUtils.join(linkageDigifortList, ","));
        }

        if (handle != null) {
            zkResultMsg.setData(handle);
        }

        // 发送邮件和短信、line消息推送
        List<AccLinkageMedia> linkageMediaList =
            accLinkageMediaDao.findByAccLinkage_IdAndMediaTypeNot(linkageId, AccConstants.DIGIFORT);
        if (linkageMediaList.size() > 0) {
            // 附件路径
            String attachments = "";
            // 通过视频联动句柄获得视频联动文件路径，组装成附件路径
            if (handle != null && Vid4OtherGetVidLinkageEventService != null) {
                attachments = Vid4OtherGetVidLinkageEventService
                    .getCaputureFilePath(String.valueOf(handle.get("vidLinkageHandle")));
            }
            // 发邮件内容描述带上触发联动的事件名
            newTransaction.setTriggerCond(lastTransaction.getEventNo());
            String mailContent = accTransactionService.getMailContent(newTransaction);
            // whatsapp发送内容跟sms一样，所以就直接用smsContent
            String smsContent = accTransactionService.getSMSContent(newTransaction);
            String lineContent = accTransactionService.getLineContent(newTransaction);
            // 温度、口罩异常事件, 邮件内容增加温度和口罩的信息
            if (Objects.nonNull(hep4OtherTransactionService) && lastTransaction != null
                && (lastTransaction.getEventNo() == 68 || lastTransaction.getEventNo() == 69)) {
                String irTempUnit =
                    accDeviceOptionService.getValueByNameAndDevSn(lastTransaction.getDevSn(), "IRTempUnit");
                String temperature = lastTransaction.getTemperature();
                String maskFlag = lastTransaction.getMaskFlag();
                // 温度检测，设备上传的温度是255表示没有开启检测
                if ("255".equals(temperature)) {
                    temperature = I18nUtil.i18nCode("acc_mail_unmeasured");
                } else {
                    if ("1".equals(irTempUnit)) {
                        BigDecimal temperatureNum = new BigDecimal(temperature);
                        temperatureNum = temperatureNum.multiply(new BigDecimal(1.8)).add(new BigDecimal(32))
                            .setScale(2, BigDecimal.ROUND_DOWN);
                        temperature = temperatureNum + "℉";
                    } else {
                        temperature = temperature + "℃";
                    }
                }
                // 口罩检测，设备上传的口罩参数是255表示没有开启检测
                if ("255".equals(maskFlag)) {
                    maskFlag = I18nUtil.i18nCode("acc_mail_unmeasured");
                } else {
                    maskFlag = I18nUtil.i18nCode("1".equals(maskFlag) ? "common_yes" : "common_no");
                }
                mailContent = mailContent + "<br/>" + "<span style='color:red'>"
                    + I18nUtil.i18nCode("acc_mail_temperature") + ": " + temperature + "</span><br/>"
                    + "<span style='color:red'>" + I18nUtil.i18nCode("acc_mail_mask") + ": " + maskFlag + "</span>";

                smsContent = smsContent + "\n" + I18nUtil.i18nCode("acc_mail_temperature") + ": " + temperature + "\n"
                    + I18nUtil.i18nCode("acc_mail_mask") + ": " + maskFlag;
                lineContent = lineContent + "\n" + I18nUtil.i18nCode("acc_mail_temperature") + ": " + temperature + "\n"
                    + I18nUtil.i18nCode("acc_mail_mask") + ": " + maskFlag;
            }
            for (AccLinkageMedia accLinkageMedia : linkageMediaList) {
                if (AccConstants.MAIL == accLinkageMedia.getMediaType()) {
                    sendMail(accLinkageMedia, mailContent, attachments);
                } else if (AccConstants.SMS == accLinkageMedia.getMediaType()) {
                    sendSMS(accLinkageMedia, smsContent);
                } else if (AccConstants.LINE == accLinkageMedia.getMediaType()) {
                    sendInfoToLine(accLinkageMedia, lineContent);
                } else if (AccConstants.WHATSAPP == accLinkageMedia.getMediaType()) {
                    sendToWhatsapp(accLinkageMedia, smsContent);
                }
            }
            // 可视对讲联动IVR
            executeVdbLinkage(linkageMediaList);
        }

        // 执行入侵联动
        executeIasLinkage(linkageId);
        return zkResultMsg;
    }

    private void executeVdbLinkage(List<AccLinkageMedia> linkageMediaList) {
        if (Objects.nonNull(vdb4OtherService) && !baseSysParamService.supportCloudSip()) {
            Map<Short, List<AccLinkageMedia>> linkagetMediaMap = linkageMediaList.stream()
                .collect(Collectors.groupingBy(linkageMedia -> linkageMedia.getMediaType(), Collectors.toList()));
            if (linkagetMediaMap.containsKey(AccConstants.VDB_IVR) && linkagetMediaMap.containsKey(AccConstants.VDB)) {
                List<AccLinkageMedia> ivrLinkageMedia = linkagetMediaMap.get(AccConstants.VDB_IVR);
                List<AccLinkageMedia> extensionLinkageMedias = linkagetMediaMap.get(AccConstants.VDB);
                AccLinkageMedia ivr = ivrLinkageMedia.get(0);
                for (AccLinkageMedia linkageMedia : extensionLinkageMedias) {
                    vdb4OtherService.dialIvr(ivr.getMediaContent(), linkageMedia.getMediaContent());
                }
            }
        }
    }

    /**
     * 执行入侵联动
     *
     * @param linkageId:
     * @return void
     * <AUTHOR>
     * @date 2022-12-01 17:21
     * @since 1.0.0
     */
    private void executeIasLinkage(String linkageId) {
        List<AccLinkageIas> accLinkageIasList = accLinkageIasDao.findByAccLinkage_Id(linkageId);
        if (accLinkageIasList.isEmpty() || Objects.isNull(ias4OtherService)) {
            return;
        }
        List<String> partitionIdList =
            (List<String>)CollectionUtil.getPropertyList(accLinkageIasList, AccLinkageIas::getPartitionId, "-1");
        AccLinkageIas linkageIas = accLinkageIasList.get(0);
        short actionType = linkageIas.getActionType();
        Integer armType =
            Objects.nonNull(linkageIas.getArmType()) ? Integer.parseInt(linkageIas.getArmType() + "") : -1;
        for (String partitionId : partitionIdList) {
            if (AccConstants.IAS_ARM == actionType) {
                log.info("execute ias linkage, actionType=" + actionType + ", partitionId=" + partitionId + ", armType="
                    + armType);
                ias4OtherService.arm(partitionId, armType);
            } else if (AccConstants.IAS_DISARM == actionType) {
                log.info("execute ias linkage, actionType=" + actionType + ", partitionId=" + partitionId);
                ias4OtherService.disarm(partitionId);
            }
        }
    }

    /**
     * 执行逻辑联动（未设置联动输出点）
     *
     * @param linkageId:联动ID
     * @param newTransaction:事件
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2022-07-19 15:41
     * @since 1.0.0
     */
    public ZKResultMsg executeLogicLinkage(String linkageId, AccTransactionItem newTransaction) {
        AccTransactionItem lastTransaction = newTransaction;
        return executeExtendLinkage(linkageId, lastTransaction, newTransaction);
    }

    /**
     * 执行硬联动设置的视频联动
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/13 8:57
     * @param accLinkageVidList 视频联动实体列表
     * @param accTransaction 门禁事件
     * @return
     */
    public Map<String, Object> executeVideoLinkage(List<AccLinkageVid> accLinkageVidList,
        AccTransactionItem accTransaction) {
        String handle = UUID.randomUUID().toString();
        Map<String, String> paramMap = accLinkageVidOperate.getVidLinkageParam(accTransaction);
        String entityIds = paramMap.get("entityIds");
        String entityClassName = paramMap.get("entityClassName");
        boolean isTrigger = false;
        int type = 0;
        ZKResultMsg ret = null;
        ZKResultMsg recordRet = null;
        for (AccLinkageVid accLinkageVid : accLinkageVidList) {
            int actionType = accLinkageVid.getActionType();
            int recordBeforeTime =
                accLinkageVid.getRecordBeforeTime() != null ? accLinkageVid.getRecordBeforeTime() : 0;
            int actionTime = accLinkageVid.getActionTime();
            if (actionType == AccConstants.CAPTURE) {
                if (Objects.nonNull(vid4OtherGetVidLinkageService))// 执行抓拍动作并判断是否执行成功
                {
                    ret = vid4OtherGetVidLinkageService.getVidLinkageCapture(handle, entityIds, entityClassName);
                    if (Boolean.parseBoolean(ret.getRet())) {
                        type |= AccConstants.VID_CAPTURE;
                        accTransaction.setCaptureTime(actionTime);
                        isTrigger = true;
                    }
                }
            } else if (actionType == AccConstants.RECORD) {
                if (Objects.nonNull(vid4OtherGetVidLinkageService)) {
                    // 执行录像动作并判断是否执行成功
                    recordRet = vid4OtherGetVidLinkageService.getVidLinkageRecord(handle, entityIds, entityClassName,
                        recordBeforeTime, actionTime);
                    if (Boolean.parseBoolean(recordRet.getRet())) {
                        type |= AccConstants.VID_VIDEO;
                        isTrigger = true;
                    }
                }
            } else if (actionType == AccConstants.POP_UP_VIDEO && vid4OtherGetVidLinkageService != null) {
                List<Map<String, Object>> vidDevices =
                    vid4OtherGetVidLinkageService.getLinkagePopUpVideo(entityIds, entityClassName, actionTime);
                if (vidDevices != null && vidDevices.size() > 0) {
                    accTransaction.setVidDevices(vidDevices);
                }
            }
        }
        if (isTrigger) {
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("type", type);
            dataMap.put("vidLinkageHandle", handle);
            Map<String, List<String>> captureFileMap =
                Objects.nonNull(ret) ? (Map<String, List<String>>)ret.getData() : null;
            dataMap.put("photoFile",
                Objects.nonNull(captureFileMap) ? captureFileMap.get("captureBase64List") : new ArrayList<>());
            dataMap.put("photoFilePath",
                Objects.nonNull(captureFileMap) ? captureFileMap.get("capturePathList") : new ArrayList<>());
            Map<String, List<String>> videoDataMap =
                Objects.nonNull(recordRet) ? (Map<String, List<String>>)recordRet.getData() : null;
            dataMap.put("videoChannelId",
                Objects.nonNull(videoDataMap) ? videoDataMap.get("videoChannelList") : new ArrayList<>());
            return dataMap;
        }
        return null;
    }

    /**
     * 硬联动发送邮件
     *
     * @param accLinkageMedia
     * @param content
     * @param attachments
     */
    private void sendMail(AccLinkageMedia accLinkageMedia, String content, String attachments) {
        String receiver = accLinkageMedia.getMediaContent();
        String subject = I18nUtil.i18nCode("acc_eventNo_6");
        baseSendMailService.sendSampleHtmlMail(receiver, subject, content, attachments);
    }

    /**
     * 硬联动发送短信
     *
     * @param accLinkageMedia
     * @param content
     */
    private void sendSMS(AccLinkageMedia accLinkageMedia, String content) {
        String receiver = accLinkageMedia.getMediaContent();
        if (smsModem4OtherService != null) {
            smsModem4OtherService.sendSMS(receiver, content, BaseConstants.ACC);
        }
    }

    /**
     * 联动发送消息到LineApp
     * 
     * @param accLinkageMedia:
     * @param lineContent:
     * @return void
     * <AUTHOR>
     * @date 2022-02-17 14:09
     * @since 1.0.0
     */
    private void sendInfoToLine(AccLinkageMedia accLinkageMedia, String lineContent) {
        String lineContactId = accLinkageMedia.getMediaContent();
        if (line4OtherService != null) {
            line4OtherService.sendInfoToLine(lineContactId, lineContent);
        }
    }

    /**
     * 联动发送消息到whatsapp
     * 
     * @param accLinkageMedia:
     * @param whatsappContent:
     * @return void
     * <AUTHOR>
     * @date 2022-02-22 15:54
     * @since 1.0.0
     */
    private void sendToWhatsapp(AccLinkageMedia accLinkageMedia, String whatsappContent) {
        String receiver = accLinkageMedia.getMediaContent();
        if (whatsapp4OtherService != null) {
            whatsapp4OtherService.sendToWhatsapp(receiver, whatsappContent, BaseConstants.ACC);
        }
    }

}
