package com.zkteco.zkbiosecurity.acc.cmd;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.cmd.best.acc.sipshortkey.CmdBestSipShortKeyConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.device.CmdDevThreshold;
import com.zkteco.zkbiosecurity.cmd.sec.sipcontact.CmdSipContact;
import com.zkteco.zkbiosecurity.cmd.sec.sipcontact.CmdSipContactConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.sipcontact.CmdSipContactNumber;
import com.zkteco.zkbiosecurity.cmd.sec.sipshortkey.CmdSipShortKeyConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.adms.service.AdmsDevCmdService;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.cmd.base.bean.sec.*;
import com.zkteco.zkbiosecurity.cmd.best.acc.ad.CmdBestAd;
import com.zkteco.zkbiosecurity.cmd.best.acc.ad.CmdBestAdConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.ad.CmdBestResource;
import com.zkteco.zkbiosecurity.cmd.best.acc.antiPassback.CmdBestAntiPassbackConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.authlevel.CmdBestAuthLevel;
import com.zkteco.zkbiosecurity.cmd.best.acc.authlevel.CmdBestAuthLevelConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.biodata.CmdBestBioDataConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.biophoto.CmdBestBioPhotoConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.biotemplete.CmdBestBioTemplateConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.device.CmdBestDeviceConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.devparameters.CmdBestDevParametersConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.devproperty.CmdBestDevProperty;
import com.zkteco.zkbiosecurity.cmd.best.acc.devproperty.CmdBestDevPropertyConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.doorauthorize.CmdBestDoorauthorize;
import com.zkteco.zkbiosecurity.cmd.best.acc.doorauthorize.CmdBestDoorauthorizeConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.doorparameters.CmdBestDoorParametersConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.doorpassword.CmdBestDoorPassword;
import com.zkteco.zkbiosecurity.cmd.best.acc.doorpassword.CmdBestDoorPasswordConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.doorproperty.CmdBestDoorProperty;
import com.zkteco.zkbiosecurity.cmd.best.acc.doorproperty.CmdBestDoorPropertyConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.holiday.CmdBestHolidayConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.mulcarduser.CmdBestMulCardUserConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.multimcardGroup.CmdBestMultimCardGroupConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.sipaccount.CmdBestSipAccount;
import com.zkteco.zkbiosecurity.cmd.best.acc.sipaccount.CmdBestSipAccountConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.sipcontact.CmdBestSipContact;
import com.zkteco.zkbiosecurity.cmd.best.acc.sipcontact.CmdBestSipContactConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.sipcontact.CmdBestSipNumber;
import com.zkteco.zkbiosecurity.cmd.best.acc.superauthorize.CmdBestSuperAuthorizeConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.timezone.CmdBestTimezoneConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.timezone.CmdBestTimezoneList;
import com.zkteco.zkbiosecurity.cmd.best.acc.transaction.CmdBestTransactionConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.user.CmdBestUser;
import com.zkteco.zkbiosecurity.cmd.best.acc.user.CmdBestUserConstructor;
import com.zkteco.zkbiosecurity.cmd.best.acc.userauthorize.CmdBestUserauthorize;
import com.zkteco.zkbiosecurity.cmd.best.acc.userauthorize.CmdBestUserauthorizeConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.antipassback.CmdAntipassbackConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.auxinproperty.CmdAuxInProperty;
import com.zkteco.zkbiosecurity.cmd.sec.auxinproperty.CmdAuxInPropertyConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.auxoutproperty.CmdAuxOutProperty;
import com.zkteco.zkbiosecurity.cmd.sec.auxoutproperty.CmdAuxOutPropertyConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.biodata.CmdBiodataConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.biophoto.CmdBiophoto;
import com.zkteco.zkbiosecurity.cmd.sec.biophoto.CmdBiophotoConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.biotemplate.CmdBioTemplateConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.cambindinfo.CmdCamBindInfo;
import com.zkteco.zkbiosecurity.cmd.sec.cambindinfo.CmdCamBindInfoConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.cardformat.CmdCardFormat;
import com.zkteco.zkbiosecurity.cmd.sec.cardformat.CmdCardFormatConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.constants.CmdSecConstants;
import com.zkteco.zkbiosecurity.cmd.sec.defwgformat.CmdDefWGFormat;
import com.zkteco.zkbiosecurity.cmd.sec.defwgformat.CmdDefWGFormatConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.device.CmdDevice;
import com.zkteco.zkbiosecurity.cmd.sec.device.CmdDeviceConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.deviceauthorize.CmdDeviceAuthorize;
import com.zkteco.zkbiosecurity.cmd.sec.deviceauthorize.CmdDeviceAuthorizeConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.devparameters.CmdDevParametersConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.devproperty.CmdDevPropertyConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.difftimezonevs.CmdDiffTimezoneVSExt;
import com.zkteco.zkbiosecurity.cmd.sec.difftimezonevs.CmdDiffTimezoneVSExtConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.doorparameters.CmdDoorParametersConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.doorproperty.CmdDoorPropertyConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.doorvstimezone.CmdDoorVSTimezone;
import com.zkteco.zkbiosecurity.cmd.sec.doorvstimezone.CmdDoorVSTimezoneConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.dstsetting.CmdDstsettingConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.extboardproperty.CmdExtBoardProperty;
import com.zkteco.zkbiosecurity.cmd.sec.extboardproperty.CmdExtBoardPropertyConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.extboardrelationlist.CmdExtBoardRelationList;
import com.zkteco.zkbiosecurity.cmd.sec.extboardrelationlist.CmdExtBoardRelationListConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.extuser.CmdExtuser;
import com.zkteco.zkbiosecurity.cmd.sec.extuser.CmdExtuserConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.firstcard.CmdFirstCard;
import com.zkteco.zkbiosecurity.cmd.sec.firstcard.CmdFirstCardConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.holiday.CmdHolidayConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.identitycard.CmdIdentityCardConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.inputiosetting.CmdInputIOSetting;
import com.zkteco.zkbiosecurity.cmd.sec.inputiosetting.CmdInputIOSettingConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.interlock.CmdInterlockConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.linkage.CmdLinkage;
import com.zkteco.zkbiosecurity.cmd.sec.linkage.CmdLinkageConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.mulcarduser.CmdMulCardUserConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.multimcard.CmdMultimCardConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.ntpserverlist.CmdNTPServerList;
import com.zkteco.zkbiosecurity.cmd.sec.ntpserverlist.CmdNTPServerListConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.outrelaysetting.CmdOutRelaySetting;
import com.zkteco.zkbiosecurity.cmd.sec.outrelaysetting.CmdOutRelaySettingConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.personalvstimezone.CmdPersonalVSTimezone;
import com.zkteco.zkbiosecurity.cmd.sec.personalvstimezone.CmdPersonalVSTimezoneConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.readerparameters.CmdReaderParameters;
import com.zkteco.zkbiosecurity.cmd.sec.readerparameters.CmdReaderParametersConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.readerproperty.CmdReaderProperty;
import com.zkteco.zkbiosecurity.cmd.sec.readerproperty.CmdReaderPropertyConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.readerwgformat.CmdReaderWGFormat;
import com.zkteco.zkbiosecurity.cmd.sec.readerwgformat.CmdReaderWGFormatConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.superauthorize.CmdSuperAuthorizeConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.timezone.CmdTimezone;
import com.zkteco.zkbiosecurity.cmd.sec.timezone.CmdTimezoneConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.transaction.CmdTransactionConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.triggerlist.CmdTriggerListConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.user.CmdUser;
import com.zkteco.zkbiosecurity.cmd.sec.user.CmdUserConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.userauthorize.CmdUserauthorize;
import com.zkteco.zkbiosecurity.cmd.sec.userauthorize.CmdUserauthorizeConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.userpic.CmdUserPic;
import com.zkteco.zkbiosecurity.cmd.sec.userpic.CmdUserPicConstructor;
import com.zkteco.zkbiosecurity.cmd.sec.wgformat.CmdWGFormat;
import com.zkteco.zkbiosecurity.cmd.sec.wgformat.CmdWGFormatConstructor;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;
import com.zkteco.zkbiosecurity.system.service.BaseMediaFileService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseMediaFileItem;

@Component
public class AccDevCmdManager implements AccDevCmdManagerService {
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired(required = false)
    private AdmsDevCmdService admsDevCmdService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private PersWiegandFmtService persWiegandFmtService;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccReaderDao accReaderDao;
    @Autowired
    private AccAuxInDao accAuxInDao;
    @Autowired
    private AccAuxOutDao accAuxOutDao;
    @Autowired
    private AccLinkageTriggerDao accLinkageTriggerDao;
    @Autowired(required = false)
    private AccGetAiDeviceService accGetAiDeviceService;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccReaderOptionDao accReaderOptionDao;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private AccLinkageInOutDao accLinkageInOutDao;
    @Autowired
    private AccCombOpenDoorDao accCombOpenDoorDao;
    @Autowired
    private AccCacheManager accCacheManager;
    @Value("${security.require-ssl:false}")
    private String isSupportHttps;
    @Autowired
    private BaseMediaFileService baseMediaFileService;
    @Autowired
    private AccAntiPassbackDao accAntiPassbackDao;
    @Autowired
    private AccLinkageVidDao accLinkageVidDao;

    /**
     * 构建命令设备bean
     *
     * @param accDevice 门禁设备实体
     * @return
     */
    @Deprecated
    private CmdDevice buildCmdDevice(AccDevice accDevice) {
        CmdDevice cmdDevice = new CmdDevice();
        cmdDevice.setId(accDevice.getBusinessId());
        cmdDevice.setMachineType(accDevice.getMachineType());
        cmdDevice.setCommType(accDevice.getCommType());
        cmdDevice.setDeviceName(accDevice.getDeviceName());
        buildCmdDeviceOption(cmdDevice, accDevice.getSn());
        return cmdDevice;
    }

    /**
     * 构建命令设备bean
     *
     * @param devSn 设备序列号
     * @return
     */
    private CmdDevice buildCmdDevice(String devSn) {
        CmdDevice cmdDevice = new CmdDevice();
        AccQueryDeviceItem accDevice = accDeviceService.getQueryItemBySn(devSn);
        cmdDevice.setId(accDevice.getBusinessId());
        cmdDevice.setDeviceId(accDevice.getId());
        cmdDevice.setMachineType(accDevice.getMachineType());
        cmdDevice.setCommType(accDevice.getCommType());
        cmdDevice.setDeviceName(accDevice.getDeviceName());
        cmdDevice.setSn(accDevice.getSn());
        cmdDevice.setParentDeviceId(accDevice.getParentDeviceId());
        buildCmdDeviceOption(cmdDevice, devSn);
        return cmdDevice;
    }

    private void buildCmdDeviceOption(CmdDevice cmdDevice, String devSn) {
        Map<String, String> optionMap = accDeviceOptionService.getDevOptionBySn(devSn);
        if (optionMap.containsKey("DateFmtFunOn")) {
            cmdDevice.setDateFmtFunOn(optionMap.get("DateFmtFunOn"));
        }
        if (optionMap.containsKey("~SupAuthrizeFunOn")) {
            cmdDevice.setSupAuthrizeFunOn(optionMap.get("~SupAuthrizeFunOn"));
        }
        if (optionMap.containsKey("DisableUserFunOn")) {
            cmdDevice.setDisableUserFunOn(optionMap.get("DisableUserFunOn"));
        }
        if (optionMap.containsKey("UserNameFunOn")) {
            cmdDevice.setUserNameFunOn(optionMap.get("UserNameFunOn"));
        }
        if (optionMap.containsKey("AccSupportFunList")) {
            cmdDevice.setAccSupportFunList(optionMap.get("AccSupportFunList"));
        }
        if (optionMap.containsKey("UserOpenDoorDelayFunOn")) {
            cmdDevice.setUserOpenDoorDelayFunOn(optionMap.get("UserOpenDoorDelayFunOn"));
        }
        if (optionMap.containsKey("DSTFunOn")) {
            cmdDevice.setDstFunOn(optionMap.get("DSTFunOn"));
        }
        if (optionMap.containsKey("~DSTF")) {
            cmdDevice.setDstF(optionMap.get("~DSTF"));
        }
        if (optionMap.containsKey("MachineTZFunOn")) {
            cmdDevice.setMachineTZFunOn(optionMap.get("MachineTZFunOn"));
        }
        if (optionMap.containsKey("~CardFormatFunOn")) {
            cmdDevice.setCardFormatFunOn(optionMap.get("~CardFormatFunOn"));
        }
        if (optionMap.containsKey("OutRelaySetFunOn")) {
            cmdDevice.setOutRelaySetFunOn(optionMap.get("OutRelaySetFunOn"));
        }
        if (optionMap.containsKey("~IsOnlyRFMachine")) {
            cmdDevice.setIsOnlyRFMachine(optionMap.get("~IsOnlyRFMachine"));
        }
        if (optionMap.containsKey("FingerFunOn")) {
            cmdDevice.setFingerFunOn(optionMap.get("FingerFunOn"));
        }
        if (optionMap.containsKey("FvFunOn")) {
            cmdDevice.setFvFunOn(optionMap.get("FvFunOn"));
        }
        if (optionMap.containsKey("FaceFunOn")) {
            cmdDevice.setFaceFunOn(optionMap.get("FaceFunOn"));
        }
        if (optionMap.containsKey("~ZKFPVersion")) {
            cmdDevice.setzKFPVersion(optionMap.get("~ZKFPVersion"));
        }
        if (optionMap.containsKey("VisilightFun")) {
            cmdDevice.setVisilightFun(optionMap.get("VisilightFun"));
        }
        if (optionMap.containsKey("BioDataFun")) {
            cmdDevice.setBioDataFun(optionMap.get("BioDataFun"));
        }
        if (optionMap.containsKey("PvFunOn")) {
            cmdDevice.setPvFunOn(optionMap.get("PvFunOn"));
        }
        if (optionMap.containsKey("BioPhotoFun")) {
            cmdDevice.setBioPhotoFunOn(optionMap.get("BioPhotoFun"));
        }
        if (optionMap.containsKey("FaceVersion")) {
            cmdDevice.setFaceVersion(optionMap.get("FaceVersion"));
        }
        if (optionMap.containsKey("PvVersion")) {
            cmdDevice.setPvVersion(optionMap.get("PvVersion"));
        }
        if (optionMap.containsKey("MultiBioDataSupport")) {
            cmdDevice.setMultiBioDataSupport(optionMap.get("MultiBioDataSupport"));
        }
        if (optionMap.containsKey("MultiBioPhotoSupport")) {
            cmdDevice.setMultiBioPhotoSupport(optionMap.get("MultiBioPhotoSupport"));
        }
        if (optionMap.containsKey("MultiBioVersion")) {
            cmdDevice.setMultiBioVersion(optionMap.get("MultiBioVersion"));
        }
        if (optionMap.containsKey("PhotoFunOn")) {
            cmdDevice.setPhotoFunOn(optionMap.get("PhotoFunOn"));
        }
        if (optionMap.containsKey("UserPicURLFunOn")) {
            cmdDevice.setUserPicURLFunOn(optionMap.get("UserPicURLFunOn"));
        }
        if (optionMap.containsKey("SubcontractingUpgradeFunOn")) {
            cmdDevice.setSubcontractingUpgradeFunOn(optionMap.get("SubcontractingUpgradeFunOn"));
        }
        if (optionMap.containsKey("~HW")) {
            cmdDevice.setHw(optionMap.get("~HW"));
        }
        Set<String> supportFuncIdSet = optionMap.entrySet().stream()
            // key中含有.的参赛是funcId, val==1 代表支持
            .filter(entry -> entry.getKey().contains(".") && StringUtils.equals(entry.getValue(), "1"))
            .map(Map.Entry::getKey).collect(Collectors.toSet());
        cmdDevice.setSupportFuncIdSet(supportFuncIdSet);
    }

    private Long addCmd(String sn, String cmdContent, boolean imme) {
        if (StringUtils.isBlank(cmdContent)) {
            return -1L;
        }
        if (imme) {
            return admsDevCmdService.addCmd(sn, cmdContent, true);
        } else {
            accCacheManager.addCmdCache(sn, cmdContent);
            return 1L;
        }
    }

    /**
     * 下发时间段
     *
     * @param devSn 设备序列号
     * @param timeSegList 时间段
     * @param isImme 是否紧急命令
     */
    public void setTimeSegToDev(String devSn, List<AccTimeSegItem> timeSegList, boolean isImme) {
        if (accDeviceService.isBestDevBySn(devSn)) {
            List<CmdBestTimezoneList> cmdTimezones = new ArrayList<>();
            for (AccTimeSegItem accTimeSeg : timeSegList) {
                CmdBestTimezoneList cmdTimezone = new CmdBestTimezoneList(accTimeSeg.getBusinessId(),
                    accTimeSeg.getSundayStart1(), accTimeSeg.getSundayEnd1(), accTimeSeg.getSundayStart2(),
                    accTimeSeg.getSundayEnd2(), accTimeSeg.getSundayStart3(), accTimeSeg.getSundayEnd3(),
                    accTimeSeg.getMondayStart1(), accTimeSeg.getMondayEnd1(), accTimeSeg.getMondayStart2(),
                    accTimeSeg.getMondayEnd2(), accTimeSeg.getMondayStart3(), accTimeSeg.getMondayEnd3(),
                    accTimeSeg.getTuesdayStart1(), accTimeSeg.getTuesdayEnd1(), accTimeSeg.getTuesdayStart2(),
                    accTimeSeg.getTuesdayEnd2(), accTimeSeg.getTuesdayStart3(), accTimeSeg.getTuesdayEnd3(),
                    accTimeSeg.getWednesdayStart1(), accTimeSeg.getWednesdayEnd1(), accTimeSeg.getWednesdayStart2(),
                    accTimeSeg.getWednesdayEnd2(), accTimeSeg.getWednesdayStart3(), accTimeSeg.getWednesdayEnd3(),
                    accTimeSeg.getThursdayStart1(), accTimeSeg.getThursdayEnd1(), accTimeSeg.getThursdayStart2(),
                    accTimeSeg.getThursdayEnd2(), accTimeSeg.getThursdayStart3(), accTimeSeg.getThursdayEnd3(),
                    accTimeSeg.getFridayStart1(), accTimeSeg.getFridayEnd1(), accTimeSeg.getFridayStart2(),
                    accTimeSeg.getFridayEnd2(), accTimeSeg.getFridayStart3(), accTimeSeg.getFridayEnd3(),
                    accTimeSeg.getSaturdayStart1(), accTimeSeg.getSaturdayEnd1(), accTimeSeg.getSaturdayStart2(),
                    accTimeSeg.getSaturdayEnd2(), accTimeSeg.getSaturdayStart3(), accTimeSeg.getSaturdayEnd3(),
                    accTimeSeg.getHolidayType1Start1(), accTimeSeg.getHolidayType1End1(),
                    accTimeSeg.getHolidayType1Start2(), accTimeSeg.getHolidayType1End2(),
                    accTimeSeg.getHolidayType1Start3(), accTimeSeg.getHolidayType1End3(),
                    accTimeSeg.getHolidayType2Start1(), accTimeSeg.getHolidayType2End1(),
                    accTimeSeg.getHolidayType2Start2(), accTimeSeg.getHolidayType2End2(),
                    accTimeSeg.getHolidayType2Start3(), accTimeSeg.getHolidayType2End3(),
                    accTimeSeg.getHolidayType3Start1(), accTimeSeg.getHolidayType3End1(),
                    accTimeSeg.getHolidayType3Start2(), accTimeSeg.getHolidayType3End2(),
                    accTimeSeg.getHolidayType3Start3(), accTimeSeg.getHolidayType3End3());
                cmdTimezones.add(cmdTimezone);
            }
            addCmd(devSn, CmdBestTimezoneConstructor.setTimezone(buildCmdDevice(devSn), cmdTimezones), isImme);
        } else {
            List<CmdTimezone> cmdTimezones = new ArrayList<>();
            for (AccTimeSegItem accTimeSeg : timeSegList) {
                CmdTimezone cmdTimezone = new CmdTimezone(accTimeSeg.getBusinessId(), accTimeSeg.getStartTime(),
                    accTimeSeg.getEndTime(), accTimeSeg.getSundayStart1(), accTimeSeg.getSundayEnd1(),
                    accTimeSeg.getSundayStart2(), accTimeSeg.getSundayEnd2(), accTimeSeg.getSundayStart3(),
                    accTimeSeg.getSundayEnd3(), accTimeSeg.getMondayStart1(), accTimeSeg.getMondayEnd1(),
                    accTimeSeg.getMondayStart2(), accTimeSeg.getMondayEnd2(), accTimeSeg.getMondayStart3(),
                    accTimeSeg.getMondayEnd3(), accTimeSeg.getTuesdayStart1(), accTimeSeg.getTuesdayEnd1(),
                    accTimeSeg.getTuesdayStart2(), accTimeSeg.getTuesdayEnd2(), accTimeSeg.getTuesdayStart3(),
                    accTimeSeg.getTuesdayEnd3(), accTimeSeg.getWednesdayStart1(), accTimeSeg.getWednesdayEnd1(),
                    accTimeSeg.getWednesdayStart2(), accTimeSeg.getWednesdayEnd2(), accTimeSeg.getWednesdayStart3(),
                    accTimeSeg.getWednesdayEnd3(), accTimeSeg.getThursdayStart1(), accTimeSeg.getThursdayEnd1(),
                    accTimeSeg.getThursdayStart2(), accTimeSeg.getThursdayEnd2(), accTimeSeg.getThursdayStart3(),
                    accTimeSeg.getThursdayEnd3(), accTimeSeg.getFridayStart1(), accTimeSeg.getFridayEnd1(),
                    accTimeSeg.getFridayStart2(), accTimeSeg.getFridayEnd2(), accTimeSeg.getFridayStart3(),
                    accTimeSeg.getFridayEnd3(), accTimeSeg.getSaturdayStart1(), accTimeSeg.getSaturdayEnd1(),
                    accTimeSeg.getSaturdayStart2(), accTimeSeg.getSaturdayEnd2(), accTimeSeg.getSaturdayStart3(),
                    accTimeSeg.getSaturdayEnd3(), accTimeSeg.getHolidayType1Start1(), accTimeSeg.getHolidayType1End1(),
                    accTimeSeg.getHolidayType1Start2(), accTimeSeg.getHolidayType1End2(),
                    accTimeSeg.getHolidayType1Start3(), accTimeSeg.getHolidayType1End3(),
                    accTimeSeg.getHolidayType2Start1(), accTimeSeg.getHolidayType2End1(),
                    accTimeSeg.getHolidayType2Start2(), accTimeSeg.getHolidayType2End2(),
                    accTimeSeg.getHolidayType2Start3(), accTimeSeg.getHolidayType2End3(),
                    accTimeSeg.getHolidayType3Start1(), accTimeSeg.getHolidayType3End1(),
                    accTimeSeg.getHolidayType3Start2(), accTimeSeg.getHolidayType3End2(),
                    accTimeSeg.getHolidayType3Start3(), accTimeSeg.getHolidayType3End3());
                cmdTimezones.add(cmdTimezone);
            }
            addCmd(devSn, CmdTimezoneConstructor.setTimezone(buildCmdDevice(devSn), cmdTimezones), isImme);
        }
    }

    /**
     * 删除时间段
     *
     * @param devSn
     * @param timeSegIdList
     * @param isImme
     */
    public void delTimeSegFromDev(String devSn, List<Long> timeSegIdList, boolean isImme) {
        String cmdContent = "";
        if (accDeviceService.isBestDevBySn(devSn)) {
            cmdContent = CmdBestTimezoneConstructor.delTimezone(buildCmdDevice(devSn), timeSegIdList);
        } else {
            cmdContent = CmdTimezoneConstructor.delTimezone(timeSegIdList);
        }
        addCmd(devSn, cmdContent, isImme);
    }

    /**
     * 清除时间段表数据
     *
     * @param devSn
     * @param isImme
     */
    public void delAllTimeSegFromDev(String devSn, boolean isImme) {
        String cmdContent = "";
        if (accDeviceService.isBestDevBySn(devSn)) {
            cmdContent = CmdBestTimezoneConstructor.delAllTimezone(buildCmdDevice(devSn));
        } else {
            cmdContent = CmdTimezoneConstructor.delAllTimezone();
        }
        addCmd(devSn, cmdContent, isImme);
    }

    /**
     * 下发节假日命令
     *
     * @param devSn
     * @param holidayList
     * @param isImme
     */
    public void setHolidayToDev(String devSn, List<AccHolidayItem> holidayList, boolean isImme) {
        List<CmdHoliday> cmdHolidayList = new ArrayList<>();
        holidayList.forEach(accHoliday -> cmdHolidayList.add(new CmdHoliday(accHoliday.getHolidayType(),
            accHoliday.getStartDate(), accHoliday.getEndDate(), accHoliday.getIsLoopByYear())));
        String cmdContent = "";
        if (accDeviceService.isBestDevBySn(devSn)) {
            cmdContent = CmdBestHolidayConstructor.setHoliday(buildCmdDevice(devSn), cmdHolidayList);
        } else {
            cmdContent = CmdHolidayConstructor.setHoliday(cmdHolidayList);
        }
        addCmd(devSn, cmdContent, isImme);
    }

    /**
     * 清空节假日数据
     *
     * @param devSn
     * @param isImme
     */
    public void delAllHolidayFromDev(String devSn, boolean isImme) {
        String cmdContent = "";
        if (accDeviceService.isBestDevBySn(devSn)) {
            cmdContent = CmdBestHolidayConstructor.delAllHoliday(buildCmdDevice(devSn));
        } else {
            cmdContent = CmdHolidayConstructor.delAllHoliday();
        }
        addCmd(devSn, cmdContent, isImme);
    }

    /**
     * 下发反潜命令
     *
     * @param devSn 设备sn
     * @param accAntiPassback 反潜实体
     * @param imme 是否紧急命令
     */
    public void setAntiPassbackToDev(String devSn, AccAntiPassbackItem accAntiPassback, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        CmdAntipassback apb = new CmdAntipassback();
        AccAntiPassback accAntiPassbackTemp = accAntiPassbackDao.findOne(accAntiPassback.getId());
        apb.setApbRule(accAntiPassbackTemp.getApbRule());
        apb.setAntiPassbackNum(accAntiPassbackTemp.getBusinessId());
        List<CmdTriggerList> cmdTriggerLists = new ArrayList<>();
        List<Long> triggerListBId = new ArrayList<>();
        CmdAntipassback apb1 = new CmdAntipassback();
        apb1.setAntiPassbackNum(accAntiPassbackTemp.getBusinessId());
        if (accAntiPassbackTemp.getApbRuleType() != null) {
            AccTriggerGroup accTriggerGroup1 = accAntiPassbackTemp.getTriggerGroup1();
            AccTriggerGroup accTriggerGroup2 = accAntiPassbackTemp.getTriggerGroup2();
            apb.setEntryGroupNum(String.valueOf(accTriggerGroup1.getBussinessId()));
            apb.setExitGroupNum(String.valueOf(accTriggerGroup2.getBussinessId()));

            // 新反潜协议需要软件自行下发双向反潜设置，入出组id对调发送
            apb1.setEntryGroupNum(String.valueOf(accTriggerGroup2.getBussinessId()));
            apb1.setExitGroupNum(String.valueOf(accTriggerGroup1.getBussinessId()));

            triggerListBId.add(accTriggerGroup1.getBussinessId());
            triggerListBId.add(accTriggerGroup2.getBussinessId());
            for (AccTriggerGroupAddr addr : accTriggerGroup1.getAddrSet()) {
                Short triggerNum;
                if (accTriggerGroup1.getType() == 4) {
                    AccReader reader = accReaderDao.findOne(addr.getAddress());
                    triggerNum = reader.getReaderNo();
                } else {
                    AccDoor door = accDoorDao.findOne(addr.getAddress());
                    triggerNum = door.getDoorNo();
                }
                cmdTriggerLists.add(new CmdTriggerList(accTriggerGroup1.getBussinessId(), cmdDevice.getId(),
                    accTriggerGroup1.getType(), triggerNum));
            }
            for (AccTriggerGroupAddr addr : accTriggerGroup2.getAddrSet()) {
                Short triggerNum;
                if (accTriggerGroup2.getType() == 4) {
                    AccReader reader = accReaderDao.findOne(addr.getAddress());
                    triggerNum = reader.getReaderNo();
                } else {
                    AccDoor door = accDoorDao.findOne(addr.getAddress());
                    triggerNum = door.getDoorNo();
                }
                cmdTriggerLists.add(new CmdTriggerList(accTriggerGroup2.getBussinessId(), cmdDevice.getId(),
                    accTriggerGroup2.getType(), triggerNum));
            }
        }
        if (accDeviceService.isBestDevBySn(devSn)) {
            List<CmdAntipassback> apbs = new ArrayList<>();
            apbs.add(apb);
            addCmd(devSn, CmdBestAntiPassbackConstructor.setAntiPassback(cmdDevice, Arrays.asList(devSn), apbs), imme);
        } else {
            // 先根据triggerlistid删除保持数据统一
            addCmd(devSn, CmdTriggerListConstructor.delTriggerList(cmdDevice, triggerListBId), imme);
            addCmd(devSn, CmdTriggerListConstructor.setTriggerList(cmdDevice, cmdTriggerLists), imme);
            addCmd(devSn, CmdAntipassbackConstructor.setAntipassback(cmdDevice, apb), imme);
            // 新反潜协议需要软件自行下发双向反潜设置，入出组id对调发送
            if ("1".equals(cmdDevice.getAccSupportFunList(66))) {
                addCmd(devSn, CmdAntipassbackConstructor.setAntipassback(cmdDevice, apb1), imme);
            }
        }
    }

    /**
     * 删除反潜设置
     *
     * @param devSn 设备序列号
     * @param apbNum 设备序列号
     * @param imme 是否紧急命令
     */
    public void delAntipassbackFromDev(String devSn, Long apbNum, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn,
                CmdBestAntiPassbackConstructor.delAntiPassback(cmdDevice, Arrays.asList(devSn), Arrays.asList(apbNum)),
                imme);
        } else {
            addCmd(devSn, CmdAntipassbackConstructor.delAntipassback(cmdDevice, apbNum), imme);
        }
    }

    /**
     * 清空反潜设置
     *
     * @param devSn
     * @param imme
     */
    public void delAllAntipassbackFromDev(String devSn, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestAntiPassbackConstructor.delAllAntiPassback(cmdDevice), imme);
        } else {
            addCmd(devSn, CmdAntipassbackConstructor.delAllAntipassback(cmdDevice), imme);
        }
    }

    /**
     * 下发互锁命令
     *
     * @param accInterlock 互锁实体
     * @param imme 是否紧急命令
     */
    public void setInterlockToDev(String devSn, AccInterlock accInterlock, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        CmdInterlock interlock = new CmdInterlock();

        interlock.setInterlockRule(accInterlock.getInterlockRule());
        interlock.setInterlockNum(accInterlock.getBusinessId());
        List<CmdTriggerList> cmdTriggerLists = new ArrayList<>();
        List<Long> triggerListBId = new ArrayList<>();
        if (accInterlock.getLockRule() != null) {
            AccTriggerGroup accTriggerGroup1 = accInterlock.getTriggerGroup1();
            AccTriggerGroup accTriggerGroup2 = accInterlock.getTriggerGroup2();
            interlock.setEntryGroupNum(String.valueOf(accTriggerGroup1.getBussinessId()));
            triggerListBId.add(accTriggerGroup1.getBussinessId());
            for (AccTriggerGroupAddr addr : accTriggerGroup1.getAddrSet()) {
                AccDoor door = accDoorDao.findOne(addr.getAddress());
                Short triggerNum = door.getDoorNo();
                cmdTriggerLists.add(new CmdTriggerList(accTriggerGroup1.getBussinessId(), cmdDevice.getId(),
                    accTriggerGroup1.getType(), triggerNum));
            }
            if (accInterlock.getLockRule() == 1) {
                interlock.setExitGroupNum(String.valueOf(accTriggerGroup2.getBussinessId()));
                triggerListBId.add(accTriggerGroup2.getBussinessId());
                for (AccTriggerGroupAddr addr : accTriggerGroup2.getAddrSet()) {
                    AccDoor door = accDoorDao.findOne(addr.getAddress());
                    Short triggerNum = door.getDoorNo();
                    cmdTriggerLists.add(new CmdTriggerList(accTriggerGroup2.getBussinessId(), cmdDevice.getId(),
                        accTriggerGroup2.getType(), triggerNum));
                }
            } else if (accInterlock.getLockRule() == 2) {
                // 组内互锁两个组id需要发成一样的
                interlock.setExitGroupNum(String.valueOf(accTriggerGroup1.getBussinessId()));
            }
        }
        if (accDeviceService.isBestDevBySn(devSn)) {
            // todo 待实现
        } else {
            // 先根据triggerlistid删除保持数据统一
            addCmd(devSn, CmdTriggerListConstructor.delTriggerList(cmdDevice, triggerListBId), imme);
            addCmd(devSn, CmdTriggerListConstructor.setTriggerList(cmdDevice, cmdTriggerLists), imme);
            addCmd(devSn, CmdInterlockConstructor.setInterlock(cmdDevice, interlock), imme);
        }
    }

    /**
     * 删除互锁设置
     *
     * @param devSn 设备序列号
     * @param interlockNum 互锁编号
     * @param imme 是否紧急命令
     */
    public void delInterlockFromDev(String devSn, Long interlockNum, boolean imme) {
        if (accDeviceService.isBestDevBySn(devSn)) {
            // todo 待完善
        } else {
            addCmd(devSn, CmdInterlockConstructor.delInterlock(buildCmdDevice(devSn), interlockNum), imme);
        }
    }

    /**
     * 清空互锁设置
     *
     * @param devSn
     * @param imme
     */
    public void delAllInterlockFromDev(String devSn, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            // todo 待完善
        } else {
            addCmd(devSn, CmdInterlockConstructor.delAllInterlock(cmdDevice), imme);
        }
    }

    /**
     * 下发人员信息到设备
     *
     * @param devSn 设备序列号
     * @param personOptItemList 人员命令操作bean
     * @param imme 是否紧急
     */
    @Override
    public void setPersonToDev(String devSn, List<AccPersonOptItem> personOptItemList, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        List<CmdMulCardUser> cmdMulCardUserList = new ArrayList<>();
        List<CmdSuperAuthorize> cmdSuperAuthorizeList = new ArrayList<>();
        List<String> delSuperAuthorizePins = new ArrayList<>();
        if (accDeviceService.isBestDevBySn(devSn)) {
            List<CmdBestUser> cmdBestUserList = new ArrayList<>();
            personOptItemList.forEach(accPersonOptBean -> {
                CmdBestUser user = new CmdBestUser();
                user.setPin(accPersonOptBean.getPin());
                user.setFirstName(accPersonOptBean.getName());
                user.setLastName(accPersonOptBean.getLastName());
                user.setDisabled(accPersonOptBean.getDisabled());
                user.setStartTime(accPersonOptBean.getStartTime());
                user.setEndTime(accPersonOptBean.getEndTime());
                user.setViceCard(accPersonOptBean.getIdCard());
                user.setGroup(accPersonOptBean.getGroupId());
                if (accPersonOptBean.getDelayPassage() != null && accPersonOptBean.getDelayPassage()) {
                    user.setFunSwitch("1");
                } else {
                    user.setFunSwitch("0");
                }
                cmdBestUserList.add(user);
                String cardNo = accPersonOptBean.getCardNo();
                if (StringUtils.isNotBlank(cardNo) && PersConstants.CARD_LOSS != accPersonOptBean.getCardState()) {
                    cmdMulCardUserList
                        .add(new CmdMulCardUser(accPersonOptBean.getPin(), cardNo, accPersonOptBean.getCardType()));
                }
                // 超级用户权限
                if (!Objects.isNull(accPersonOptBean.getSuperAuth())
                    && accPersonOptBean.getSuperAuth() == CmdSecConstants.ADMINISTRATOR) {
                    cmdSuperAuthorizeList.add(new CmdSuperAuthorize(accPersonOptBean.getPin(), cmdDevice.getId()));
                } else {
                    delSuperAuthorizePins.add(accPersonOptBean.getPin());
                }
            });
            addCmd(devSn, CmdBestUserConstructor.setUser(cmdDevice, cmdBestUserList), imme);
            addCmd(devSn, CmdBestMulCardUserConstructor.setMulCardUser(cmdDevice, cmdMulCardUserList), imme);
            addCmd(devSn, CmdBestSuperAuthorizeConstructor.setSuperAuthorize(cmdDevice, cmdSuperAuthorizeList), imme);
            addCmd(devSn, CmdBestSuperAuthorizeConstructor.delSuperAuthorize(cmdDevice, delSuperAuthorizePins), imme);
        } else {
            Set<CmdUser> cmdUserSet = new HashSet<>();
            Set<CmdExtuser> cmdExtuserSet = new HashSet<>();
            List<CmdUserPic> cmdUserPicList = new ArrayList<>();
            String cardhex = baseSysParamService.getValByName("pers.cardHex");// 当前系统卡号进制
            boolean isMulCardUser = "1".equals(cmdDevice.getAccSupportFunList(7));// 是否支持多卡
            boolean isSuperAuthorize = "1".equals(cmdDevice.getAccSupportFunList(16));// 支持超级用户表
            boolean isPhotoFunOn = "1".equals(cmdDevice.getPhotoFunOn());
            boolean isUserPicURLFunOn = "1".equals(cmdDevice.getUserPicURLFunOn());
            // 是否支持下发用户照片
            boolean isLongNameOn = "1".equals(cmdDevice.getAccSupportFunList(5));// 是否支持长姓名
            personOptItemList.forEach(accPersonOptBean -> {
                String cardNo = formatPersonCard(accPersonOptBean, cardhex, isMulCardUser);
                if (accPersonOptBean.getCardType() == null
                    || accPersonOptBean.getCardType() == PersConstants.MAIN_CARD) {// 主卡信息加入user表，防止pin重复，支持多卡的设备不需要对cardno赋值
                    String name = "";// 支持长姓名下发的设备user表不需要发name字段
                    if (!isLongNameOn && (StringUtils.isNotBlank(accPersonOptBean.getName())
                        || StringUtils.isNotBlank(accPersonOptBean.getLastName()))) {
                        name = (accPersonOptBean.getName() + " " + accPersonOptBean.getLastName()).trim();
                    }
                    CmdUser cmdUser = new CmdUser(accPersonOptBean.getPin(), name, accPersonOptBean.getPersonPwd(),
                        isMulCardUser ? "" : cardNo, accPersonOptBean.getStartTime(), accPersonOptBean.getEndTime(),
                        accPersonOptBean.getSuperAuth(), accPersonOptBean.getGroupId(), accPersonOptBean.getDisabled(),
                        accPersonOptBean.getPrivilege(), accPersonOptBean.getIdCard());
                    cmdUserSet.add(cmdUser);

                    CmdExtuser cmdExtuser = new CmdExtuser(accPersonOptBean.getPin(), accPersonOptBean.getName(),
                        accPersonOptBean.getLastName(), accPersonOptBean.getPhotoPath(),
                        accPersonOptBean.getDelayPassage(), accPersonOptBean.getTempUser());
                    cmdExtuserSet.add(cmdExtuser);
                }
                if (isMulCardUser && !StringUtils.isEmpty(cardNo)) {// 根据参数判断是否支持一人多卡表
                    cmdMulCardUserList
                        .add(new CmdMulCardUser(accPersonOptBean.getPin(), cardNo, accPersonOptBean.getCardType()));
                }
                if (isSuperAuthorize) {
                    if (!Objects.isNull(accPersonOptBean.getSuperAuth())
                        && accPersonOptBean.getSuperAuth() == CmdSecConstants.ADMINISTRATOR) {
                        cmdSuperAuthorizeList.add(new CmdSuperAuthorize(accPersonOptBean.getPin(), cmdDevice.getId()));
                    } else {
                        delSuperAuthorizePins.add(accPersonOptBean.getPin());
                    }
                }
                // 组装人员照片下发
                if (isPhotoFunOn && StringUtils.isNotBlank(accPersonOptBean.getPhotoPath())) {
                    String thumbPath = FileUtil.getThumbPath(accPersonOptBean.getPhotoPath());
                    // 支持url方式
                    if (isUserPicURLFunOn) {
                        CmdUserPic cmdUserPic = new CmdUserPic();
                        cmdUserPic.setPin(accPersonOptBean.getPin());
                        cmdUserPic.setFormat(CmdSecConstants.FORMAT_URL);
                        // 下发需要去掉开头的斜杠
                        cmdUserPic.setUrl(thumbPath.replaceFirst("/", ""));
                        cmdUserPicList.add(cmdUserPic);
                    } else {
                        String userPhotoBase64 = FileEncryptUtil.getDecryptFileBase64(thumbPath);
                        if (StringUtils.isNotBlank(userPhotoBase64)) {
                            CmdUserPic cmdUserPic = new CmdUserPic();
                            cmdUserPic.setContent(userPhotoBase64);
                            cmdUserPic.setSize(String.valueOf(userPhotoBase64.length()));
                            cmdUserPic.setPin(accPersonOptBean.getPin());
                            cmdUserPic.setFormat(CmdSecConstants.FORMAT_BASE64);
                            cmdUserPicList.add(cmdUserPic);
                        }
                    }
                }
            });
            if (StringUtils.isNotBlank(cmdDevice.getParentDeviceId())) {
                addCmd(devSn, CmdUserConstructor.setUserToChildDev(cmdUserSet), imme);
            } else {
                addCmd(devSn, CmdUserConstructor.setUser(cmdUserSet, cmdDevice), imme);
            }
            addCmd(devSn, CmdExtuserConstructor.setExtuser(cmdExtuserSet, cmdDevice), imme);
            addCmd(devSn, CmdMulCardUserConstructor.setMulCardUser(cmdMulCardUserList, cmdDevice), imme);
            addCmd(devSn, CmdSuperAuthorizeConstructor.setSuperAuthorize(cmdDevice, cmdSuperAuthorizeList), imme);
            addCmd(devSn, CmdSuperAuthorizeConstructor.delSuperAuthorize(cmdDevice, delSuperAuthorizePins), imme);
            // 下发用户照片
            CmdUserPicConstructor.setUserPic(cmdDevice, cmdUserPicList).forEach(cmd -> addCmd(devSn, cmd, imme));
        }
    }

    /**
     * 删除人员信息
     *
     * @param devSn
     * @param pins
     * @param imme
     */
    public void delPersonFromDev(String devSn, List<String> pins, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestUserConstructor.delUser(cmdDevice, pins), imme);
        } else {
            addCmd(devSn, CmdMulCardUserConstructor.delMulCardUser(pins, cmdDevice), imme);
            addCmd(devSn, CmdSuperAuthorizeConstructor.delSuperAuthorize(cmdDevice, pins), imme);
            addCmd(devSn, CmdExtuserConstructor.delExtuser(pins, cmdDevice), imme);
            addCmd(devSn, CmdUserConstructor.delUser(pins), imme);
        }
    }

    /**
     * 根据pin号删除多卡表
     *
     * @param devSn
     * @param pins
     * @param imme
     */
    public void delMulCardUserFromDev(String devSn, List<String> pins, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestMulCardUserConstructor.delMulCardUser(cmdDevice, pins), imme);
        } else {
            addCmd(devSn, CmdMulCardUserConstructor.delMulCardUser(pins, cmdDevice), imme);
        }
    }

    /**
     * 清空人员信息
     *
     * @param devSn
     * @param imme
     */
    public void delAllPersonFromDev(String devSn, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestUserConstructor.clearUser(cmdDevice), imme);
        } else {
            addCmd(devSn, CmdMulCardUserConstructor.delAllMulCardUser(cmdDevice), imme);
            addCmd(devSn, CmdSuperAuthorizeConstructor.delAllSuperAuthorize(cmdDevice), imme);
            addCmd(devSn, CmdExtuserConstructor.delAllExtuser(cmdDevice), imme);
            addCmd(devSn, CmdUserConstructor.delAllUser(), imme);
        }
    }

    /**
     * 获取人员表信息
     *
     * @param devSn
     * @param imme
     * @return
     */
    public Long getAllPersonFromDev(String devSn, boolean imme) {
        if (accDeviceService.isBestDevBySn(devSn)) {
            return addCmd(devSn, CmdBestUserConstructor.queryUser(buildCmdDevice(devSn)), imme);
        } else {
            return addCmd(devSn, CmdUserConstructor.getAllUser(), imme);
        }
    }

    /**
     * 获取人员扩展表信息（需设备支持）
     *
     * @param devSn
     * @param imme
     * @return
     */
    public Long getAllPersonExtInfoFromDev(String devSn, boolean imme) {
        // 只push类设备支持不需要讨论best的情况
        return addCmd(devSn, CmdExtuserConstructor.getAllExtuser(buildCmdDevice(devSn)), imme);
    }

    /**
     * 获取人员多卡表信息（需设备支持）
     *
     * @param devSn
     * @param imme
     * @return
     */
    public Long getAllPersonCardFromDev(String devSn, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            return addCmd(devSn, CmdBestMulCardUserConstructor.queryMulCardUser(cmdDevice), imme);
        } else {
            return addCmd(devSn, CmdMulCardUserConstructor.getAllMulCardUser(cmdDevice), imme);
        }
    }

    /**
     * 获取设备人员数量
     *
     * @param devSn
     * @param imme
     * @return
     */
    public Long getPersonCountFromDev(String devSn, boolean imme) {
        if (accDeviceService.isBestDevBySn(devSn)) {
            return addCmd(devSn, CmdBestUserConstructor.getUserCount(buildCmdDevice(devSn)), imme);
        } else {
            return addCmd(devSn, CmdUserConstructor.getUserCount(), imme);
        }
    }

    /**
     * 下发人员权限命令
     *
     * @param devSn
     * @param accDeviceLevelItemList
     */
    public void setPersonLevelToDev(String devSn, List<AccPersonLevelOptItem> accDeviceLevelItemList, boolean imme) {
        if (accDeviceLevelItemList != null && accDeviceLevelItemList.size() > 0) {
            CmdDevice cmdDevice = buildCmdDevice(devSn);
            if (accDeviceService.isBestDevBySn(devSn)) {
                Set<CmdBestAuthLevel> cmdBestAuthLevelSet = new HashSet<>();
                List<CmdBestDoorauthorize> cmdBestDoorauthorizes = new ArrayList<>();
                List<CmdBestUserauthorize> cmdBestUserauthorizes = new ArrayList<>();
                accDeviceLevelItemList.forEach(accPersonLevelOptItem -> {
                    cmdBestAuthLevelSet.add(
                        new CmdBestAuthLevel(accPersonLevelOptItem.getLevelId(), accPersonLevelOptItem.getStartTime(),
                            accPersonLevelOptItem.getEndTime(), accPersonLevelOptItem.getTimeSegId()));
                    for (Short doorNo : accPersonLevelOptItem.getDoorNoList()) {
                        cmdBestDoorauthorizes.add(new CmdBestDoorauthorize(doorNo, accPersonLevelOptItem.getDeviceId(),
                            accPersonLevelOptItem.getLevelId()));
                    }
                    cmdBestUserauthorizes.add(
                        new CmdBestUserauthorize(accPersonLevelOptItem.getPin(), accPersonLevelOptItem.getLevelId()));
                });
                addCmd(devSn, CmdBestAuthLevelConstructor.setAuthLevel(cmdDevice, new ArrayList<>(cmdBestAuthLevelSet)),
                    imme);
                addCmd(devSn, CmdBestDoorauthorizeConstructor.setDoorauthorize(cmdDevice, cmdBestDoorauthorizes), imme);
                addCmd(devSn, CmdBestUserauthorizeConstructor.setUserauthorize(cmdDevice, cmdBestUserauthorizes), imme);
            } else {
                List<CmdUserauthorize> cmdUserauthorizeList = new ArrayList<>();
                accDeviceLevelItemList.forEach(accPersonLevelOptItem -> {
                    cmdUserauthorizeList
                        .add(new CmdUserauthorize(accPersonLevelOptItem.getPin(), accPersonLevelOptItem.getTimeSegId(),
                            new HashSet<>(accPersonLevelOptItem.getDoorNoList()), accPersonLevelOptItem.getDeviceId(),
                            accPersonLevelOptItem.getStartTime(), accPersonLevelOptItem.getEndTime()));
                });
                CmdUserauthorizeConstructor.setUserauthorize(cmdDevice, cmdUserauthorizeList)
                    .forEach(cmd -> addCmd(devSn, cmd, imme));
            }
        }
    }

    /**
     * 删除人员权限命令
     *
     * @param devSn
     * @param pins
     * @param imme
     */
    public void delPersonLevelFromDev(String devSn, List<String> pins, boolean imme) {
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestUserauthorizeConstructor.delUserauthorize(buildCmdDevice(devSn), pins), imme);
        } else {
            addCmd(devSn, CmdUserauthorizeConstructor.delUserauthorize(pins), imme);
        }
    }

    /**
     * 清空人员权限表
     *
     * @param devSn
     * @param imme
     */
    public void delAllPersonLevelFromDev(String devSn, boolean imme) {
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestUserauthorizeConstructor.dellAllUserAuthorize(buildCmdDevice(devSn)), imme);
        } else {
            addCmd(devSn, CmdUserauthorizeConstructor.dellAllUserauthorize(), imme);
        }
    }

    /**
     * 下发人员生物模版
     *
     * @param devSn
     * @param accBioTemplateItemList
     * @param imme
     */
    @Override
    public void setPersonBioTemplateToDev(String devSn, List<AccBioTemplateItem> accBioTemplateItemList, boolean imme) {
        if (accBioTemplateItemList != null && accBioTemplateItemList.size() > 0) {
            List<CmdBioTemplate> cmdBioTemplateList = new ArrayList<>();
            accBioTemplateItemList
                .forEach(accBioTemplateItem -> cmdBioTemplateList.add(new CmdBioTemplate(accBioTemplateItem.getPin(),
                    accBioTemplateItem.getTemplateId(), accBioTemplateItem.getTemplateIndex(),
                    accBioTemplateItem.getDuress(), accBioTemplateItem.getTemplate(), accBioTemplateItem.getType(),
                    accBioTemplateItem.getVersion(), accBioTemplateItem.getFormat(), accBioTemplateItem.getUrl())));
            CmdDevice cmdDevice = buildCmdDevice(devSn);
            if (accDeviceService.isBestDevBySn(devSn)) {
                CmdBestBioTemplateConstructor.setBioTemplate(cmdDevice, cmdBioTemplateList)
                    .forEach(cmd -> addCmd(devSn, cmd, imme));
            } else {
                if (StringUtils.isNotBlank(cmdDevice.getParentDeviceId())) {
                    CmdBioTemplateConstructor.setBioTemplateToChildDev(cmdDevice, cmdBioTemplateList)
                        .forEach(cmd -> addCmd(devSn, cmd, imme));
                } else {
                    CmdBioTemplateConstructor.setBioTemplate(cmdDevice, cmdBioTemplateList)
                        .forEach(cmd -> addCmd(devSn, cmd, imme));
                }
            }
        }
    }

    /**
     * 删除人员生物模版
     *
     * @param devSn
     * @param pinList
     * @param imme
     */
    public void delPersonBioTemplateFromDev(String devSn, List<String> pinList, boolean imme) {
        // 为了不影响biodata中未接入的模版类型，这里采用按种类删除的方式
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestBioDataConstructor.delBioDataByPin(cmdDevice, pinList), imme);
        } else {
            CmdBioTemplateConstructor.delBioDataByPin(cmdDevice, pinList).forEach(cmd -> addCmd(devSn, cmd, imme));
        }
    }

    /**
     * 根据生物识别类型删除人员生物识别信息
     * 
     * @param devSn
     * @param pinList
     * @param bioType
     * @param imme
     */
    public void delPersonBioTemplateByTypeFromDev(String devSn, List<String> pinList, Short bioType, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestBioDataConstructor.delBioDataByPinAndType(cmdDevice, pinList, bioType), imme);
        } else {
            switch (bioType) {
                case CmdSecConstants.TEMPLATE_FP:
                    addCmd(devSn, CmdBioTemplateConstructor.delFingerprints(cmdDevice, pinList), imme);
                    break;
                case CmdSecConstants.TEMPLATE_FACE:
                    addCmd(devSn, CmdBioTemplateConstructor.delFaces(cmdDevice, pinList), imme);
                    break;
                case CmdSecConstants.TEMPLATE_VEIN:
                    addCmd(devSn, CmdBioTemplateConstructor.delFingerveins(cmdDevice, pinList), imme);
                    break;
                case CmdSecConstants.TEMPLATE_PALM:
                    addCmd(devSn, CmdBioTemplateConstructor.delPalmveins(cmdDevice, pinList), imme);
                    break;
                case CmdSecConstants.TEMPLATE_BIOPHOTO:
                    addCmd(devSn, CmdBioTemplateConstructor.delVislightDatas(cmdDevice, pinList), imme);
                    break;
                default:
                    addCmd(devSn, CmdBioTemplateConstructor.delBioDataByType(cmdDevice, pinList, bioType), imme);
                    break;
            }
        }
    }

    /***
     * 清除所有生物模版
     *
     * @param devSn
     * @param imme
     */
    public void delAllPersonBioTemplateFromDev(String devSn, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            // best协议只需整体删除即可
            addCmd(devSn, CmdBestBioDataConstructor.delAllBioData(cmdDevice), imme);
            addCmd(devSn, CmdBestBioPhotoConstructor.delAllBioPhoto(cmdDevice), imme);
        } else {
            CmdBioTemplateConstructor.delAllBioData(cmdDevice).forEach(cmd -> addCmd(devSn, cmd, imme));
        }
    }

    /**
     * 获取比对照片
     *
     * @param devSn
     * @param imme
     * @return
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年9月27日 下午4:33:23
     */
    public Long getPersonBioPhotoFromDev(String devSn, Short type, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            return addCmd(devSn, CmdBestBioPhotoConstructor.queryBioPhotos(cmdDevice, String.valueOf(type)), imme);
        } else {
            return addCmd(devSn, CmdBiophotoConstructor.getBiophotoByType(cmdDevice, type), imme);
        }
    }

    /**
     * 获取比对照片数量
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-08 16:10
     * @param devSn
     * @param imme
     * @return java.lang.Long
     */
    public Long getPersonBioPhotoCountFromDev(String devSn, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            return addCmd(devSn, CmdBestBioPhotoConstructor.countAllBioPhotos(cmdDevice), imme);
        } else {
            return addCmd(devSn, CmdBiophotoConstructor.getBioPhotoCount(cmdDevice), imme);
        }
    }

    /**
     * 根据类型获取人员生物模版
     *
     * @param devSn
     * @param type
     * @param imme
     * @return
     */
    public Long getPersonBioTemplateFromDev(String devSn, Short type, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        long cmdId = -1L;
        if (BaseConstants.BaseBioType.FP_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getAllFingerprint(cmdDevice), imme);
        } else if (BaseConstants.BaseBioType.FACE_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getAllFace(cmdDevice), imme);
        } else if (BaseConstants.BaseBioType.VEIN_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getAllFingervein(cmdDevice), imme);
        } else if (BaseConstants.BaseBioType.PALM_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getAllPalm(cmdDevice), imme);
        } else if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getAllVislightFace(cmdDevice), imme);
        } else {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getAllBioDataByType(cmdDevice, type), imme);
        }
        return cmdId;
    }

    /**
     * 根据类型获取人员生物模版数量
     *
     * @param devSn
     * @param type
     * @param imme
     * @return
     */
    public Long getPersonBioTemplateCountFromDev(String devSn, Short type, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        long cmdId = -1L;
        if (BaseConstants.BaseBioType.FP_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getFingerprintCount(cmdDevice), imme);
        } else if (BaseConstants.BaseBioType.FACE_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getFaceCount(cmdDevice), imme);
        } else if (BaseConstants.BaseBioType.VEIN_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getFingerveinCount(cmdDevice), imme);
        } else if (BaseConstants.BaseBioType.PALM_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getPalmCount(cmdDevice), imme);
        } else if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.equals(type)) {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getVislightFaceCount(cmdDevice), imme);
        } else {
            cmdId = addCmd(devSn, CmdBioTemplateConstructor.getBioDataCountByType(cmdDevice, type), imme);
        }
        return cmdId;
    }

    /**
     * 获取一体化模板人脸数量
     * 
     * @param devSn
     */
    public long getBiodataCountByFaceFromDev(String devSn) {
        String cmd = CmdBiodataConstructor.getBiodataCountByFace();
        return addCmd(devSn, cmd, true);
    }

    /**
     * 下发后台验证参数
     *
     * @param devSn 设备
     * @param verifyParam 是否开启后台验证
     * @param offlineRule 设备离线规则
     * @param isImme 是否紧急命令
     */
    public long issueVerifyParamToDev(String devSn, String verifyParam, String offlineRule, boolean isImme) {
        Map<String, String> optionMap = Maps.newHashMap();
        optionMap.put("AutoServerMode", verifyParam);
        List<AccDoor> accDoorList = accDoorDao.findByDevice_Sn(devSn);
        List<String> readerNoList = new ArrayList<>();
        for (AccDoor door : accDoorList) {
            for (AccReader reader : door.getAccReaderList()) {
                readerNoList.add(String.valueOf(reader.getReaderNo()));
            }
        }
        Collections.sort(readerNoList);
        StringBuffer cmdStrBuf = new StringBuffer();
        String cmd = "ID=%s\tDevID=%d\tOfflineRefuse=%s\r\n";
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        boolean isReaderProperty = "1".equals(cmdDevice.getAccSupportFunList(18));
        // 离线权限参数不为空时，拼接命令
        if (offlineRule != null) {
            for (String s : readerNoList) {
                if (isReaderProperty) {
                    cmdStrBuf.append(String.format(cmd, s, cmdDevice.getId(), offlineRule));
                } else {
                    optionMap.put(String.format("Reader%sOfflineRefuse", s), offlineRule);
                }
            }
        }

        // 韦根离线读头改到ReaderProperty表 by juvenile.li 20170104
        if (cmdStrBuf.length() > 0) {
            cmd = String.format("%s %s %s", ConstUtil.DATA_UPDATE, AccConstants.READER_PROPERTY, cmdStrBuf.toString());
            addCmd(devSn, cmd, false);
        }
        List<Long> cmdIdList = Lists.newArrayList();
        CmdDeviceConstructor.setOptions(optionMap).forEach(tempCmd -> cmdIdList.add(addCmd(devSn, tempCmd, isImme)));
        return cmdIdList.get(0);
    }

    /**
     * 下发读头参数
     *
     * @param accDevice 设备
     * @param accReaderList 读头
     * @param isImme
     */
    public void setReaderOptToDev(AccDevice accDevice, List<AccReader> accReaderList, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(accDevice);
        boolean isReaderDisable = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(0));// 是否支持读头禁用
        boolean isEncrypt = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(1));// 是否支持读头加密功能
        boolean isExt485ReaderFunOn = accDeviceOptionService.isSupportFun(accDevice.getSn(), "~Ext485ReaderFunOn");
        boolean isC3400 = AccConstants.DEVICE_C3_400 == accDevice.getMachineType();
        boolean isSupportAI = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(44));// 是否支持AI摄像头对接功能，（针对inbiopro等老设备）
        boolean isSupportOSDP = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(49));
        // 是否支持设置人员锁定
        boolean isSupportUserLockFun = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(50));
        boolean isSupportInfoRevealFun = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(51));
        String doorCount = accDeviceOptionService.getOptVal(accDevice.getSn(), "LockCount");
        Map<String, String> optionMap = new HashMap<>();
        List<CmdDevParameters> cmdDevParametersList = new ArrayList<>();
        List<CmdReaderProperty> cmdReaderPropertyList = new ArrayList<>();
        String readerEncrypt = null;
        String rs485ProtocolType = null;
        CmdReaderProperty cmdReaderProperty = null;
        AccReaderOption accReaderOpt = null;
        List<AccReaderOption> accReaderOptionList = new ArrayList<>();
        for (AccReader accReader : accReaderList) {
            // 四门韦根/485时需要拆分成两条数据下发
            if (accReader.getCommType() != null && AccConstants.READER_TYPE_RS485_OR_WGFMT == accReader.getCommType() && "4".equals(doorCount)) {
                if (AccConstants.STATE_IN.equals(accReader.getReaderState())) {
                    // 韦根，这里认为韦根读头是额外发的数据，为了避免表读头id不占用，所以进行+1000处理下发
                    cmdReaderProperty = new CmdReaderProperty((short) (accReader.getReaderNo() + 1000), accReader.getAccDoor().getDoorNo(),
                            AccConstants.READER_TYPE_WGFMT, accReader.getAccDoor().getDoorNo(), accReader.getIp(), accReader.getPort(),
                            accReader.getMac(), accReader.getReaderState(), false, accReader.getMulticast(),
                            accDevice.getBusinessId());
                    cmdReaderPropertyList.add(cmdReaderProperty);
                }
                // 485
                cmdReaderProperty = new CmdReaderProperty(accReader.getReaderNo(), accReader.getAccDoor().getDoorNo(),
                        AccConstants.READER_TYPE_RS485, accReader.getCommAddress(), accReader.getIp(), accReader.getPort(),
                        accReader.getMac(), accReader.getReaderState(), false, accReader.getMulticast(),
                        accDevice.getBusinessId());
                cmdReaderPropertyList.add(cmdReaderProperty);
            } else {
                cmdReaderProperty = new CmdReaderProperty(accReader.getReaderNo(), accReader.getAccDoor().getDoorNo(),
                        accReader.getCommType(), accReader.getCommAddress(), accReader.getIp(), accReader.getPort(),
                        accReader.getMac(), accReader.getReaderState(),
                        !(accReader.getCommType() != null && accReader.getCommType() > 0), accReader.getMulticast(),
                        accDevice.getBusinessId());
                cmdReaderPropertyList.add(cmdReaderProperty);
            }
            // 读头属性表
            String readerPropertiesCmd = CmdReaderPropertyConstructor.setReaderProperty(cmdDevice, cmdReaderPropertyList);
            if (accDevice.getParentDevice() != null) {
                addCmd(accDevice.getParentDevice().getSn(), readerPropertiesCmd, isImme);
            }
            addCmd(accDevice.getSn(), readerPropertiesCmd, isImme);

            // 读头加密功能
            if (readerEncrypt == null && isEncrypt) {
                readerEncrypt = accReader.getReaderEncrypt() ? "1" : "0";
            }
            // 支持读头属性表的设备以下属性在读头属性表中体现，故只要组装option形式的
            if (isReaderDisable) {
                // 不支持485的c3_400类型的设备（c3、k2）读头编号需要转化
                if (isC3400 && !isExt485ReaderFunOn) {
                    optionMap.put(String.format("Reader%sDisable", 2 * accReader.getReaderNo() - 1),
                        String.valueOf(accReader.getCommType()));
                } else {
                    optionMap.put(String.format("Reader%sDisable", accReader.getReaderNo()),
                        String.valueOf(accReader.getCommType()));
                }
            }
            if (isSupportAI) {// 此逻辑仅针对inbioPro设备，inbio5系列正常不会跑这里
                Acc4AiDeviceItem item = null;
                if (accGetAiDeviceService != null) {
                    item = accGetAiDeviceService.getAiDeviceByReaderId(accReader.getId());
                }
                optionMap.put(String.format("TcpReader%sIP", accReader.getReaderNo()),
                    item != null ? item.getIpAddress() : "");
                optionMap.put(String.format("TcpReader%sPort", accReader.getReaderNo()), item != null ? "4376" : "");
            }
            // 读头485协议类型配置 1：zk485 2：osdp
            if (rs485ProtocolType == null && isSupportOSDP && accReader.getRs485ProtocolType() != null) {
                rs485ProtocolType = accReader.getRs485ProtocolType().toString();
            }
            if (isSupportInfoRevealFun) {
                accReaderOpt = accReaderOptionDao.findByAccReader_IdAndName(accReader.getId(), "IsSupportInfoReveal");
                if (accReaderOpt == null) {
                    accReaderOpt = new AccReaderOption();
                    accReaderOpt.setAccReader(accReader);
                    accReaderOpt.setName("IsSupportInfoReveal");
                    accReaderOpt.setType((short)1);
                    accReaderOpt.setValue("0");
                    accReader.getAccReaderOptionSet().add(accReaderOpt);
                    accReaderOptionDao.save(accReaderOpt);
                }
                accReaderOptionList.add(accReaderOpt);
            }
            if (isSupportUserLockFun) {
                accReaderOpt = accReaderOptionDao.findByAccReader_IdAndName(accReader.getId(), "UserLockFun");
                if (Objects.isNull(accReaderOpt)) {
                    accReaderOpt = new AccReaderOption();
                    accReaderOpt.setAccReader(accReader);
                    accReaderOpt.setName("UserLockFun");
                    accReaderOpt.setType(accReader.getCommType());
                    // 默认0-人员不锁定
                    accReaderOpt.setValue("0");
                    accReader.getAccReaderOptionSet().add(accReaderOpt);
                    accReaderOptionDao.save(accReaderOpt);
                }
                accReaderOptionList.add(accReaderOpt);
            }
            if (accReader.getCommType() != null && accReader.getCommType() == AccConstants.READER_TYPE_NETWORK) {
                AccReaderOption accReaderOption =
                    accReaderOptionDao.findByAccReader_IdAndName(accReader.getId(), "IdentityCardVerifyMode");
                if (accReaderOption == null) {
                    accReaderOption = new AccReaderOption();
                    accReaderOption.setAccReader(accReader);
                    accReaderOption.setName("IdentityCardVerifyMode");
                    accReaderOption.setType((short)1);
                    accReaderOption.setValue(String.valueOf(AccConstants.READER_READMODE_NORMAL));
                    accReader.getAccReaderOptionSet().add(accReaderOption);
                    accReaderOptionDao.save(accReaderOption);
                }
                accReaderOptionList.add(accReaderOption);
            }
        }
        setReaderParametersToDev(accDevice, accReaderOptionList, false);
        if (readerEncrypt != null) {// 设备加密参数
            optionMap.put("IsSupportReaderEncrypt", readerEncrypt);
            cmdDevParametersList
                .add(new CmdDevParameters(accDevice.getBusinessId(), "IsSupportReaderEncrypt", readerEncrypt));
        }
        if (rs485ProtocolType != null) {
            optionMap.put("Reader485CommType", rs485ProtocolType);
            cmdDevParametersList
                .add(new CmdDevParameters(accDevice.getBusinessId(), "Reader485CommType", rs485ProtocolType));
        }
        // 普通参数下发
        if (accDeviceOptionService.getAccSupportFunListVal(accDevice.getId(), 26)) {
            addCmd(accDevice.getSn(), CmdDevParametersConstructor.setDevParameters(cmdDevice, cmdDevParametersList),
                isImme);
        } else {
            CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(accDevice.getSn(), cmd, isImme));
        }
    }

    /**
     * 删除读头属性数据
     *
     * @param accDevice
     * @param readerList
     * @param isImme
     */
    public void delReaderPropertyFromDev(AccDevice accDevice, List<AccReader> readerList, boolean isImme) {
        List<CmdReaderProperty> cmdReaderPropertyList = new ArrayList<>();
        for (AccReader accReader : readerList) {
            CmdReaderProperty cmdReaderProperty = new CmdReaderProperty();
            cmdReaderProperty.setReaderNo(accReader.getReaderNo());
            cmdReaderProperty.setDevId(accReader.getAccDoor().getDevice().getBusinessId());
            cmdReaderPropertyList.add(cmdReaderProperty);
        }
        String readerPropertiesCmd =
            CmdReaderPropertyConstructor.delReaderProperty(buildCmdDevice(accDevice), cmdReaderPropertyList);
        if (accDevice.getParentDevice() != null) {
            addCmd(accDevice.getParentDevice().getSn(), readerPropertiesCmd, isImme);
        }
        addCmd(accDevice.getSn(), readerPropertiesCmd, isImme);
    }

    /**
     * 下发读头参数
     *
     * @param accDevice 设备
     * @param accReaderOptionList 读头参数
     */
    public void setReaderParametersToDev(AccDevice accDevice, List<AccReaderOption> accReaderOptionList,
        boolean isImme) {
        List<CmdReaderParameters> cmdReaderParametersList = new ArrayList<>();
        accReaderOptionList.forEach(accReaderOption -> cmdReaderParametersList
            .add(new CmdReaderParameters(accReaderOption.getAccReader().getReaderNo(), accReaderOption.getName(),
                accReaderOption.getValue(), accDevice.getBusinessId())));
        String cmd =
            CmdReaderParametersConstructor.setReaderParameters(buildCmdDevice(accDevice), cmdReaderParametersList);
        if (accDevice.getParentDevice() != null) {
            addCmd(accDevice.getParentDevice().getSn(), cmd, isImme);
        }
        addCmd(accDevice.getSn(), cmd, isImme);
    }

    /**
     * 清除门参数表数据
     *
     * @param accDevice
     * @param isImme
     */
    public void delAllReaderParametersFromDev(AccDevice accDevice, boolean isImme) {
        addCmd(accDevice.getSn(), CmdReaderParametersConstructor.delAllReaderParameters(buildCmdDevice(accDevice)),
            isImme);
    }

    /**
     * 清空读头韦根表
     * 
     * @param devSn
     * @param isImme
     */
    public void delAllReaderWGFormatFromDev(String devSn, boolean isImme) {
        addCmd(devSn, CmdReaderWGFormatConstructor.delAllReaderWGFormat(buildCmdDevice(devSn)), isImme);
    }

    /**
     * 设置门参数
     *
     * @param accDoor
     * @param isImme
     */
    public void setDoorOptionToDev(AccDoorItem accDoor, boolean isImme) {
        // push、pull协议参数
        Map<String, String> optionMap = new HashMap<>();
        List<CmdDoorParameters> cmdDoorParametersList = new ArrayList<>();
        // best协议参数
        List<CmdDoorParameters> cmdBestDoorParametersList = new ArrayList<>();

        AccDevice accDevice = accDeviceDao.getDeviceByDoorId(accDoor.getId());
        CmdDevice cmdDevice = buildCmdDevice(accDevice.getSn());
        if (accDoor.getWgInputFmtId() != null && CmdSecConstants.SUPPORT.equals(cmdDevice.getCardFormatFunOn())) {
            boolean autoWGFormat = true;
            PersWiegandFmtItem wgInputFmt = persWiegandFmtService.getItemById(accDoor.getWgInputFmtId());
            String fmtStr = wgInputFmt.getCardFmt() + ":" + wgInputFmt.getParityFmt();
            if (StringUtils.isNotEmpty(wgInputFmt.getCardFmt())) {
                autoWGFormat = false;
            }

            List<CmdReaderWGFormat> cmdReaderWGFormatList = new ArrayList<>();
            cmdReaderWGFormatList.add(new CmdReaderWGFormat(accDoor.getDoorNo(),
                autoWGFormat ? 0 : wgInputFmt.getBusinessId(), accDoor.getWgReversed(), accDevice.getBusinessId()));
            String readerWGFormatCmd = CmdReaderWGFormatConstructor.setReaderWGFormat(cmdDevice, cmdReaderWGFormatList);
            List<Short> doorNos = new ArrayList<>();
            doorNos.add(accDoor.getDoorNo());
            String delReaderWGFormatCmd = CmdReaderWGFormatConstructor.delReaderWGFormat(cmdDevice, doorNos);
            if (accDevice.getParentDevice() != null) {
                addCmd(accDevice.getParentDevice().getSn(), delReaderWGFormatCmd, isImme);
                addCmd(accDevice.getParentDevice().getSn(), readerWGFormatCmd, isImme);
            }
            addCmd(accDevice.getSn(), delReaderWGFormatCmd, isImme);
            addCmd(accDevice.getSn(), readerWGFormatCmd, isImme);

            boolean isOneWay = accDeviceService.isOneWay(accDevice.getSn());// 是否单向控制器（针对韦根）
            if (!isOneWay) {
                if (autoWGFormat) {
                    optionMap.put(String.format("Reader%dAutoMatch", 2 * accDoor.getDoorNo() - 1), "1");
                    optionMap.put(String.format("Reader%dAutoMatch", 2 * accDoor.getDoorNo()), "1");
                } else {
                    optionMap.put(String.format("Reader%dAutoMatch", 2 * accDoor.getDoorNo() - 1), "0");
                    optionMap.put(String.format("Reader%dAutoMatch", 2 * accDoor.getDoorNo()), "0");
                    optionMap.put(String.format("Reader%dWGType", 2 * accDoor.getDoorNo() - 1), fmtStr);
                    optionMap.put(String.format("Reader%dWGType", 2 * accDoor.getDoorNo()), fmtStr);
                }
            } else {
                if (autoWGFormat) {
                    optionMap.put(String.format("Reader%dAutoMatch", accDoor.getDoorNo()), "1");
                } else {
                    optionMap.put(String.format("Reader%dAutoMatch", accDoor.getDoorNo()), "0");
                    optionMap.put(String.format("Reader%dWGType", accDoor.getDoorNo()), fmtStr);
                }
            }
            cmdDoorParametersList.add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorAutoMatch",
                autoWGFormat ? "1" : "0", accDevice.getBusinessId()));
            // 支持韦根输出的机器
            if (!autoWGFormat && AccConstants.DEVICE_ACCESS_MACHINE_TYPE.contains(accDevice.getMachineType())) {
                PersWiegandFmtItem wgOutputFmt = persWiegandFmtService.getItemById(accDoor.getWgOutputFmtId());
                if (wgOutputFmt != null) {
                    optionMap.put("WiegandFmt", wgOutputFmt.getCardFmt() + ":" + wgOutputFmt.getParityFmt());
                }
            }
        }
        if (AccConstants.DEVICE_ACCESS_MACHINE_TYPE.contains(accDevice.getMachineType())) {// 支持主机出入状态设置的机器，暂时只有set
            // option的形式
            optionMap.put("Reader1IOState",
                String.valueOf(accDoor.getHostStatus() != null ? accDoor.getHostStatus() : 1));
            optionMap.put("SlaveIOState",
                String.valueOf(accDoor.getSlaveStatus() != null ? accDoor.getSlaveStatus() : 0));
            optionMap.put("WiegandIDIn",
                String.valueOf(accDoor.getWgInputType() != null ? accDoor.getWgInputType() : 1));
            optionMap.put("WiegandID",
                String.valueOf(accDoor.getWgOutputType() != null ? accDoor.getWgOutputType() : 1));
        }
        // 锁驱动时长
        String lockDelay = String.valueOf(accDoor.getLockDelay() != null ? accDoor.getLockDelay() : 5);
        optionMap.put(String.format("Door%dDrivertime", accDoor.getDoorNo()), lockDelay);
        cmdDoorParametersList
            .add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorDrivertime", lockDelay, accDevice.getBusinessId()));
        cmdBestDoorParametersList
            .add(new CmdDoorParameters(accDoor.getDoorNo(), "doorDriverTime", lockDelay, accDevice.getBusinessId()));

        // 门常开时间段
        Long passModeTimeId = StringUtils.isNotBlank(accDoor.getPassModeTimeSegId())
            ? accTimeSegService.getBusinessIdByTimeSegId(accDoor.getPassModeTimeSegId()) : 0;
        String passModeTimeSeg = String.valueOf(passModeTimeId);
        optionMap.put(String.format("Door%dKeepOpenTimeZone", accDoor.getDoorNo()), passModeTimeSeg);
        cmdDoorParametersList.add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorKeepOpenTimeZone", passModeTimeSeg,
            accDevice.getBusinessId()));
        cmdBestDoorParametersList.add(
            new CmdDoorParameters(accDoor.getDoorNo(), "doorNormalOpenTZ", passModeTimeSeg, accDevice.getBusinessId()));

        // 门有效时间段
        Long activeTimeId = StringUtils.isNotBlank(accDoor.getActiveTimeSegId())
            ? accTimeSegService.getBusinessIdByTimeSegId(accDoor.getActiveTimeSegId()) : 1;
        String activeTimeSeg = String.valueOf(activeTimeId);
        optionMap.put(String.format("Door%dValidTZ", accDoor.getDoorNo()), activeTimeSeg);
        cmdDoorParametersList
            .add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorValidTZ", activeTimeSeg, accDevice.getBusinessId()));
        cmdBestDoorParametersList
            .add(new CmdDoorParameters(accDoor.getDoorNo(), "doorValidTZ", activeTimeSeg, accDevice.getBusinessId()));

        // 紧急密码
        String supperPwd = accDoor.getSupperPwd() != null ? accDoor.getSupperPwd() : "";
        optionMap.put(String.format("Door%dSupperPassWord", accDoor.getDoorNo()), supperPwd);
        cmdDoorParametersList.add(
            new CmdDoorParameters(accDoor.getDoorNo(), "DoorSupperPassWord", supperPwd, accDevice.getBusinessId()));
        cmdBestDoorParametersList.add(
            new CmdDoorParameters(accDoor.getDoorNo(), "doorEmergencyPassword", supperPwd, accDevice.getBusinessId()));

        // 刷卡间隔
        String actionInterval = String.valueOf(accDoor.getActionInterval() != null ? accDoor.getActionInterval() : 0);
        optionMap.put(String.format("Door%dIntertime", accDoor.getDoorNo()), actionInterval);
        cmdDoorParametersList.add(
            new CmdDoorParameters(accDoor.getDoorNo(), "DoorIntertime", actionInterval, accDevice.getBusinessId()));
        // 开门方式
        String verifyType = String.valueOf(accDoor.getVerifyMode() != null ? accDoor.getVerifyMode() : 6);
        // 支持新验证方式, 转成二进制字符串下发
        if (accDeviceOptionService.isContainDevParam(accDevice.getSn(), "NewVFStyles")) {
            verifyType = AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accDoor.getVerifyMode()), true);
        }
        optionMap.put(String.format("Door%dVerifyType", accDoor.getDoorNo()), verifyType);
        cmdDoorParametersList
            .add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorVerifyType", verifyType, accDevice.getBusinessId()));
        cmdBestDoorParametersList
            .add(new CmdDoorParameters(accDoor.getDoorNo(), "doorVerifyType", verifyType, accDevice.getBusinessId()));

        // 门磁类型
        String doorSensorStatus =
            String.valueOf(accDoor.getDoorSensorStatus() != null ? accDoor.getDoorSensorStatus() : 0);
        optionMap.put(String.format("Door%dSensorType", accDoor.getDoorNo()), doorSensorStatus);
        cmdDoorParametersList.add(
            new CmdDoorParameters(accDoor.getDoorNo(), "DoorSensorType", doorSensorStatus, accDevice.getBusinessId()));
        cmdBestDoorParametersList.add(
            new CmdDoorParameters(accDoor.getDoorNo(), "doorSensorType", doorSensorStatus, accDevice.getBusinessId()));

        // 门磁延时
        String sensorDelay = String.valueOf(accDoor.getSensorDelay() != null ? accDoor.getSensorDelay() : 15);
        optionMap.put(String.format("Door%dDetectortime", accDoor.getDoorNo()), sensorDelay);
        cmdDoorParametersList.add(
            new CmdDoorParameters(accDoor.getDoorNo(), "DoorDetectortime", sensorDelay, accDevice.getBusinessId()));
        cmdBestDoorParametersList.add(
            new CmdDoorParameters(accDoor.getDoorNo(), "doorSensorDelayTime", sensorDelay, accDevice.getBusinessId()));

        // 闭门回锁 1 启用，0不启用，默认1
        String backLock = String.valueOf(accDoor.getBackLock() != null ? accDoor.getBackLock() ? 1 : 0 : 0);
        optionMap.put(String.format("Door%dCloseAndLock", accDoor.getDoorNo()), backLock);
        cmdDoorParametersList
            .add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorCloseAndLock", backLock, accDevice.getBusinessId()));
        // 协迫密码
        String forcePwd = accDoor.getForcePwd() != null ? accDoor.getForcePwd() : "";
        optionMap.put(String.format("Door%dForcePassWord", accDoor.getDoorNo()), forcePwd);
        cmdDoorParametersList
            .add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorForcePassWord", forcePwd, accDevice.getBusinessId()));
        cmdBestDoorParametersList
            .add(new CmdDoorParameters(accDoor.getDoorNo(), "doorDuressPassword", forcePwd, accDevice.getBusinessId()));

        // 入反潜时长
        if (accDeviceOptionService.isSupportFun(accDevice.getSn(), "~TimeAPBFunOn")) {
            String inApbDuration =
                String.valueOf(accDoor.getInApbDuration() != null ? accDoor.getInApbDuration() * 60 : 0);
            optionMap.put(String.format("Door%dInTimeAPB", accDoor.getDoorNo()), inApbDuration);
            cmdDoorParametersList.add(
                new CmdDoorParameters(accDoor.getDoorNo(), "DoorInTimeAPB", inApbDuration, accDevice.getBusinessId()));
        }
        // 门锁定
        if (accDeviceOptionService.isSupportFun(accDevice.getSn(), "~REXInputFunOn")) {
            String latchDoorType = String.valueOf(accDoor.getLatchDoorType() != null ? accDoor.getLatchDoorType() : 0);
            optionMap.put(String.format("Door%dREXInput", accDoor.getDoorNo()), latchDoorType);
            cmdDoorParametersList.add(
                new CmdDoorParameters(accDoor.getDoorNo(), "DoorREXInput", latchDoorType, accDevice.getBusinessId()));

            // 门锁定后，延迟多长时间检测报警
            String latchTimeOut = String.valueOf(accDoor.getLatchTimeOut() != null ? accDoor.getLatchTimeOut() : "");
            optionMap.put(String.format("Door%dREXTimeOut", accDoor.getDoorNo()), latchTimeOut);
            cmdDoorParametersList.add(
                new CmdDoorParameters(accDoor.getDoorNo(), "DoorREXTimeOut", latchTimeOut, accDevice.getBusinessId()));
        }
        // 开门延时
        if (accDeviceOptionService.isSupportFun(accDevice.getSn(), "DelayOpenDoorFunOn")) {
            String delayOpenTime = String.valueOf(accDoor.getDelayOpenTime() != null ? accDoor.getDelayOpenTime() : 0);
            optionMap.put(String.format("Door%dDelayOpenTime", accDoor.getDoorNo()), delayOpenTime);
            cmdDoorParametersList.add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorDelayOpenTime", delayOpenTime,
                accDevice.getBusinessId()));
        }
        // 是否支持残疾人辅助开门时间
        if (CmdSecConstants.SUPPORT.equals(cmdDevice.getUserOpenDoorDelayFunOn())) {
            String extDelayDrivertime =
                String.valueOf(accDoor.getExtDelayDrivertime() != null ? accDoor.getExtDelayDrivertime() : 0);
            optionMap.put(String.format("ExtDoor%sDelayDrivertime", accDoor.getDoorNo()), extDelayDrivertime);
            cmdDoorParametersList.add(new CmdDoorParameters(accDoor.getDoorNo(), "ExtDoorDelayDrivertime",
                extDelayDrivertime, accDevice.getBusinessId()));
        }
        if (accDeviceOptionService.isSupportFun(accDevice.getSn(), "MultiCardInterTimeFunOn")) {
            String timeInterval =
                String.valueOf(accDoor.getCombOpenInterval() != null ? accDoor.getCombOpenInterval() : 10);
            optionMap.put(String.format("Door%sMultiCardInterTime", accDoor.getDoorNo()), timeInterval);
            cmdDoorParametersList.add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorMultiCardInterTime", timeInterval,
                accDevice.getBusinessId()));
        }
        // 支持允许配置超级用户在门锁定时通行
        if (CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(48))) {
            String allowSUAccessLock = accDoor.getAllowSUAccessLock() != null ? accDoor.getAllowSUAccessLock() : "0";
            optionMap.put(String.format("Door%sSuperuserBypassLockdown", accDoor.getDoorNo()), allowSUAccessLock);
            cmdDoorParametersList.add(new CmdDoorParameters(accDoor.getDoorNo(), "DoorSuperuserBypassLockdown",
                allowSUAccessLock, accDevice.getBusinessId()));
        }
        // 下发控制出门按钮时间段表
        String latchTimeId = accDoor.getLatchTimeSegId();
        if (StringUtils.isNoneBlank(latchTimeId)) {
            List<CmdInputIOSetting> cmdInputIOSettingList = new ArrayList<>();
            CmdInputIOSetting cmdInputIOSetting = new CmdInputIOSetting(accDoor.getDoorNo(), accDevice.getBusinessId(),
                (short)0, accTimeSegService.getBusinessIdByTimeSegId(latchTimeId));
            cmdInputIOSettingList.add(cmdInputIOSetting);
            String inputIOSettingsCmd =
                CmdInputIOSettingConstructor.setInputIOSettings(cmdDevice, cmdInputIOSettingList);
            if (accDevice.getParentDevice() != null) {
                addCmd(accDevice.getParentDevice().getSn(), inputIOSettingsCmd, isImme);
            }
            addCmd(accDevice.getSn(), inputIOSettingsCmd, isImme);
        }
        // 开门密码数据组装
        List<CmdBestDoorPassword> cmdBestDoorPasswords = new ArrayList<>();
        List<Short> delDoorPwdNos = new ArrayList<>();
        if (StringUtils.isNotBlank(accDoor.getDoorPwd())) {
            cmdBestDoorPasswords
                .add(new CmdBestDoorPassword("", null, null, accDoor.getDoorNo(), accDoor.getDoorPwd(), "", (short)0));
        } else {
            delDoorPwdNos.add(accDoor.getDoorNo());
        }

        // 统一下发参数
        if (accDeviceService.isBestDevBySn(accDevice.getSn())) {
            addCmd(accDevice.getSn(),
                CmdBestDoorParametersConstructor.setDoorParameters(cmdDevice, cmdBestDoorParametersList), isImme);
            addCmd(accDevice.getSn(), CmdBestDoorPasswordConstructor.setDoorPassword(cmdDevice, cmdBestDoorPasswords),
                isImme);
            addCmd(accDevice.getSn(), CmdBestDoorPasswordConstructor.delDoorPassword(cmdDevice, delDoorPwdNos), isImme);
        } else {
            if (CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(16))) {
                String doorParametersCmd =
                    CmdDoorParametersConstructor.setDoorParameters(cmdDevice, cmdDoorParametersList);
                if (accDevice.getParentDevice() != null) {
                    addCmd(accDevice.getParentDevice().getSn(), doorParametersCmd, isImme);
                }
                addCmd(accDevice.getSn(), doorParametersCmd, isImme);
            } else {
                CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(accDevice.getSn(), cmd, isImme));
            }
        }
    }

    /**
     * 清除门参数表数据
     *
     * @param devSn
     * @param isImme
     */
    public void delAllDoorParametersFromDev(String devSn, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestDoorParametersConstructor.delAllDoorParameters(cmdDevice), isImme);
        } else {
            addCmd(devSn, CmdDoorParametersConstructor.delAllDoorParameters(cmdDevice), isImme);
        }
    }

    /**
     * 下发辅助输入设置
     *
     * @param devSn
     * @param accAuxInList
     * @param isImme
     */
    public void setAuxInOptToDev(String devSn, List<AccAuxInItem> accAuxInList, boolean isImme) {
        List<CmdInputIOSetting> cmdInputIOSettingList = new ArrayList<>();
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        accAuxInList.forEach(accAuxIn -> {
            Long businessId = accTimeSegService.getBusinessIdByTimeSegId(accAuxIn.getAccTimeSegId());
            if (businessId != null) {
                cmdInputIOSettingList
                    .add(new CmdInputIOSetting(accAuxIn.getAuxNo(), cmdDevice.getId(), (short)1, businessId));
            }
        });
        addCmd(devSn, CmdInputIOSettingConstructor.setInputIOSettings(cmdDevice, cmdInputIOSettingList), isImme);
    }

    /**
     * 下发辅助输入设置
     *
     * @param accDevice
     * @param accAuxInList
     * @param isImme
     */
    @Deprecated
    public void setAuxInOptToDev(AccDevice accDevice, List<AccAuxIn> accAuxInList, boolean isImme) {
        List<CmdInputIOSetting> cmdInputIOSettingList = new ArrayList<>();
        accAuxInList.forEach(accAuxIn -> {
            Long businessId = accTimeSegService.getBusinessIdByTimeSegId(accAuxIn.getTimeSegId());
            if (businessId != null) {
                cmdInputIOSettingList
                    .add(new CmdInputIOSetting(accAuxIn.getAuxNo(), accDevice.getBusinessId(), (short)1, businessId));
            }
        });
        addCmd(accDevice.getSn(),
            CmdInputIOSettingConstructor.setInputIOSettings(buildCmdDevice(accDevice), cmdInputIOSettingList), isImme);
    }

    /**
     * 下发辅助输出设置
     *
     * @param devSn
     * @param accAuxOutList
     */
    public void setAuxOutOptToDev(String devSn, List<AccAuxOutItem> accAuxOutList, boolean isImme) {
        List<CmdOutRelaySetting> cmdOutRelaySettingList = new ArrayList<>();
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        accAuxOutList.forEach(accAuxOut -> {
            Long businessId = accTimeSegService.getBusinessIdByTimeSegId(accAuxOut.getAccTimeSegId());
            if (businessId != null) {
                cmdOutRelaySettingList.add(new CmdOutRelaySetting(accAuxOut.getAuxNo(), cmdDevice.getId(), businessId));
            }
        });
        addCmd(devSn, CmdOutRelaySettingConstructor.setOutRelaySetting(cmdDevice, cmdOutRelaySettingList), isImme);
    }

    /**
     * 下发辅助输出设置
     *
     * @param accDevice
     * @param accAuxOutList
     */
    @Deprecated
    public void setAuxOutOptToDev(AccDevice accDevice, List<AccAuxOut> accAuxOutList, boolean isImme) {
        List<CmdOutRelaySetting> cmdOutRelaySettingList = new ArrayList<>();
        accAuxOutList.forEach(accAuxOut -> {
            Long businessId = accTimeSegService.getBusinessIdByTimeSegId(accAuxOut.getTimeSegId());
            if (businessId != null) {
                cmdOutRelaySettingList
                    .add(new CmdOutRelaySetting(accAuxOut.getAuxNo(), accDevice.getBusinessId(), businessId));
            }
        });
        addCmd(accDevice.getSn(),
            CmdOutRelaySettingConstructor.setOutRelaySetting(buildCmdDevice(accDevice), cmdOutRelaySettingList),
            isImme);
    }

    /**
     * 删除辅助输出设置
     *
     * @param devSn
     * @param accAuxOutNoList
     * @param isImme
     */
    public void delAuxOutOptFromDev(String devSn, List<Short> accAuxOutNoList, boolean isImme) {
        addCmd(devSn, CmdOutRelaySettingConstructor.delOutRelaySetting(buildCmdDevice(devSn), accAuxOutNoList), isImme);
    }

    /**
     * 清除辅助输出表
     *
     * @param devSn
     * @param isImme
     */
    public void delAllAuxOutOptFromDev(String devSn, boolean isImme) {
        addCmd(devSn, CmdOutRelaySettingConstructor.delAllOutRelaySetting(buildCmdDevice(devSn)), isImme);
    }

    /**
     * 下发首人开门
     *
     * @param accFirstOpen
     * @param pinList
     * @param isImme
     */
    public void setFirstOpenToDev(AccFirstOpenItem accFirstOpen, List<String> pinList, boolean isImme) {
        List<CmdFirstCard> cmdFirstCardList = new ArrayList<>();
        AccDoor accDoor = accDoorDao.findById(accFirstOpen.getDoorId()).orElse(null);
        if (Objects.nonNull(accDoor)) {
            short doorNo = accDoor.getDoorNo();
            long timezoneId = accTimeSegService.getBusinessIdByTimeSegId(accFirstOpen.getTimeSegId());
            AccDevice accDevice = accDoor.getDevice();
            pinList.forEach(
                pin -> cmdFirstCardList.add(new CmdFirstCard(pin, accDevice.getBusinessId(), timezoneId, doorNo)));
            CmdDevice cmdDevice = buildCmdDevice(accDevice);
            addCmd(accDevice.getSn(), CmdFirstCardConstructor.setFirstCard(cmdDevice, cmdFirstCardList), isImme);
            Set<Short> doorNoSet = new HashSet<>();
            doorNoSet.add(doorNo);
            addCmd(accDevice.getSn(), CmdFirstCardConstructor.enableFirstCard(cmdDevice, doorNoSet), isImme);
        }
    }

    /**
     * 下发首人开门
     *
     * @param accFirstOpen
     * @param pinList
     * @param isImme
     */
    @Deprecated
    public void setFirstOpenToDev(AccFirstOpen accFirstOpen, List<String> pinList, boolean isImme) {
        List<CmdFirstCard> cmdFirstCardList = new ArrayList<>();
        short doorNo = accFirstOpen.getAccDoor().getDoorNo();
        long timezoneId = accTimeSegService.getBusinessIdByTimeSegId(accFirstOpen.getTimeSegId());
        AccDevice accDevice = accFirstOpen.getAccDoor().getDevice();
        pinList
            .forEach(pin -> cmdFirstCardList.add(new CmdFirstCard(pin, accDevice.getBusinessId(), timezoneId, doorNo)));
        CmdDevice cmdDevice = buildCmdDevice(accDevice);
        addCmd(accDevice.getSn(), CmdFirstCardConstructor.setFirstCard(cmdDevice, cmdFirstCardList), isImme);
        Set<Short> doorNoSet = new HashSet<>();
        doorNoSet.add(doorNo);
        addCmd(accDevice.getSn(), CmdFirstCardConstructor.enableFirstCard(cmdDevice, doorNoSet), isImme);
    }

    /**
     * 根据门编号删除首人开门设置
     *
     * @param accFirstOpen
     * @param isImme
     */
    public void delFirstOpenFromDev(AccFirstOpenItem accFirstOpen, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(accFirstOpen.getDeviceSn());
        Set<Short> doorNoSet = new HashSet<>();
        doorNoSet.add(Short.parseShort(accFirstOpen.getDoorNo()));
        addCmd(accFirstOpen.getDeviceSn(), CmdFirstCardConstructor.delFirstCard(cmdDevice, doorNoSet), isImme);
        addCmd(accFirstOpen.getDeviceSn(), CmdFirstCardConstructor.disableFirstCard(cmdDevice, doorNoSet), isImme);
    }

    /**
     * 根据人员工号删除某个门的首人开门设置
     * 
     * @param accFirstOpen
     * @param pins
     * @param isImme
     */
    public void delFirstOpenFromDevByPins(AccFirstOpen accFirstOpen, Collection<String> pins, boolean isImme) {
        AccDevice accDevice = accFirstOpen.getAccDoor().getDevice();
        short doorNo = accFirstOpen.getAccDoor().getDoorNo();
        addCmd(accDevice.getSn(), CmdFirstCardConstructor.delFirstCardByPinsAndDoorNo(pins, doorNo), isImme);
    }

    /**
     * 清空首人开门表数据
     *
     * @param devSn
     * @param isImme
     */
    public void delAllFirstOpenFromDev(String devSn, boolean isImme) {
        if (accDeviceService.isBestDevBySn(devSn)) {
            // todo 待完善
        } else {
            Set<Short> doorNoSet = new HashSet<>();
            CmdDevice cmdDevice = buildCmdDevice(devSn);
            List<AccDoor> accDoorList = accDoorDao.getByDevId(StrUtil.strToList(cmdDevice.getDeviceId()));
            accDoorList.forEach(accDoor -> doorNoSet.add(accDoor.getDoorNo()));
            addCmd(devSn, CmdFirstCardConstructor.delAllFirstCard(), isImme);
            addCmd(devSn, CmdFirstCardConstructor.disableFirstCard(cmdDevice, doorNoSet), isImme);
        }
    }

    /**
     * 下发多人开门表
     *
     * @param accCombOpenDoorItem
     * @param isImme
     */
    public void setCombOpenDoorToDev(AccCombOpenDoorItem accCombOpenDoorItem, boolean isImme) {
        long[] groups = {0, 0, 0, 0, 0};
        int index = 0;
        AccCombOpenDoor accCombOpenDoor = accCombOpenDoorDao.findOne(accCombOpenDoorItem.getId());
        for (AccCombOpenComb accCombOpenComb : accCombOpenDoor.getAccCombOpenCombList()) {
            if (index < 5) {
                long groupId = accCombOpenComb.getAccCombOpenPerson().getBusinessId();
                for (int i = 0; i < accCombOpenComb.getOpenerNumber(); i++) {
                    groups[index] = groupId;
                    index++;
                }
            }
        }
        AccDevice accDevice = accCombOpenDoor.getAccDoor().getDevice();
        short doorNo = accCombOpenDoor.getAccDoor().getDoorNo();
        List<CmdMultimCard> cmdMultimCardList = new ArrayList<>();
        cmdMultimCardList.add(new CmdMultimCard(accCombOpenDoor.getBusinessId(), accDevice.getBusinessId(), doorNo,
            groups[0], groups[1], groups[2], groups[3], groups[4]));
        CmdDevice cmdDevice = buildCmdDevice(accDevice);
        addCmd(accDevice.getSn(), CmdMultimCardConstructor.setCmdMultimCard(cmdDevice, cmdMultimCardList), isImme);
        Set<Short> doorNoSet = new HashSet<>();
        doorNoSet.add(doorNo);
        addCmd(accDevice.getSn(), CmdMultimCardConstructor.enableMultimCard(cmdDevice, doorNoSet), isImme);
    }

    /**
     * 下发多人开门表
     *
     * @param accCombOpenDoor
     * @param isImme
     */
    @Deprecated
    public void setCombOpenDoorToDev(AccCombOpenDoor accCombOpenDoor, boolean isImme) {
        long[] groups = {0, 0, 0, 0, 0};
        int index = 0;
        for (AccCombOpenComb accCombOpenComb : accCombOpenDoor.getAccCombOpenCombList()) {
            if (index < 5) {
                long groupId = accCombOpenComb.getAccCombOpenPerson().getBusinessId();
                for (int i = 0; i < accCombOpenComb.getOpenerNumber(); i++) {
                    groups[index] = groupId;
                    index++;
                }
            }
        }
        AccDevice accDevice = accCombOpenDoor.getAccDoor().getDevice();
        short doorNo = accCombOpenDoor.getAccDoor().getDoorNo();
        List<CmdMultimCard> cmdMultimCardList = new ArrayList<>();
        cmdMultimCardList.add(new CmdMultimCard(accCombOpenDoor.getBusinessId(), accDevice.getBusinessId(), doorNo,
            groups[0], groups[1], groups[2], groups[3], groups[4]));
        CmdDevice cmdDevice = buildCmdDevice(accDevice);
        addCmd(accDevice.getSn(), CmdMultimCardConstructor.setCmdMultimCard(cmdDevice, cmdMultimCardList), isImme);
        Set<Short> doorNoSet = new HashSet<>();
        doorNoSet.add(doorNo);
        addCmd(accDevice.getSn(), CmdMultimCardConstructor.enableMultimCard(cmdDevice, doorNoSet), isImme);
    }

    /**
     * 删除多人开门设置
     *
     * @param accCombOpenDoor
     * @param disableCombOpen
     * @param isImme
     */
    public void delCombOpenDoorFromDev(AccCombOpenDoorItem accCombOpenDoor, boolean disableCombOpen, boolean isImme) {
        List<Long> indexList = new ArrayList<>();
        indexList.add(accCombOpenDoor.getBusinessId());
        String devSn = accCombOpenDoor.getDeviceSn();
        addCmd(devSn, CmdMultimCardConstructor.delMultimCard(indexList), isImme);
        if (disableCombOpen) {
            AccDoor accDoor = accDoorDao.findById(accCombOpenDoor.getDoorId()).orElse(null);
            Set<Short> doorNoSet = new HashSet<>();
            if (Objects.nonNull(accDoor)) {
                doorNoSet.add(accDoor.getDoorNo());
            }
            addCmd(devSn, CmdMultimCardConstructor.disableMultimCard(buildCmdDevice(devSn), doorNoSet), isImme);
        }
    }

    /**
     * 清除多人开门表数据并禁用设置
     *
     * @param devSn
     * @param isImme
     */
    public void delAllCombOpenDoorFromDev(String devSn, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestMultimCardGroupConstructor.delAllMultimCard(cmdDevice), isImme);
        } else {
            Set<Short> doorNoSet = new HashSet<>();
            List<AccDoor> accDoorList = accDoorDao.findByDevice_Sn(devSn);
            accDoorList.forEach(accDoor -> doorNoSet.add(accDoor.getDoorNo()));
            addCmd(devSn, CmdMultimCardConstructor.delAllMultimCard(), isImme);
            addCmd(devSn, CmdMultimCardConstructor.disableMultimCard(cmdDevice, doorNoSet), isImme);
        }
    }

    /**
     * @param accDevice
     * @param comAddr
     * @param isImme
     * @return
     * @Description: 修改RS485地址
     */
    public Long updateRs485Addr(AccDevice accDevice, String comAddr, boolean isImme) {
        Map<String, String> optionMap = Maps.newHashMap();
        optionMap.put("DeviceID", comAddr);
        List<Long> cmdIdList = Lists.newArrayList();
        CmdDeviceConstructor.setOptions(optionMap)
            .forEach(cmd -> cmdIdList.add(addCmd(accDevice.getSn(), cmd, isImme)));
        return cmdIdList.get(0);
    }

    /**
     * @param devSn
     * @param isImme
     * @return
     * @Description: 重启设备
     */
    public Long rebootDev(String devSn, boolean isImme) {
        return addCmd(devSn, CmdDeviceConstructor.reboot(buildCmdDevice(devSn)), isImme);
    }

    /**
     * @param devSn
     * @param date
     * @param isImme
     * @returnsetTimeZone
     * @Description: 同步时间
     */
    public Long syncTime(String devSn, Date date, boolean isImme) {
        if (accDeviceService.isBestDevBySn(devSn)) {
            return addCmd(devSn, CmdBestDeviceConstructor.syncTime(buildCmdDevice(devSn)), isImme);
        } else {
            return addCmd(devSn, CmdDeviceConstructor.setTime(date), isImme);
        }
    }

    /**
     * @param devSn
     * @param timeZone
     * @param isImme
     * @return
     * @Description: 下发时区
     */
    public void setTimeZone(String devSn, String timeZone, boolean isImme) {
        addCmd(devSn, CmdDeviceConstructor.setTimeZone(buildCmdDevice(devSn), timeZone), isImme);
    }

    /**
     * @param accDevice
     * @param openOrClose
     * @param isImme
     * @return
     * @Description: 切换网络连接测试
     */
    public Long switchNetWorkTest(AccDevice accDevice, int openOrClose, boolean isImme) {
        return addCmd(accDevice.getSn(), CmdDeviceConstructor.switchNetWorkTest(buildCmdDevice(accDevice), openOrClose),
            isImme);
    }

    /**
     * @param accDevice
     * @param wirelessSSID
     * @param wirelessKey
     * @param isImme
     * @return
     * @Description: 设置Temp WIFI 测试连接用
     */
    public void setTempWIFI(AccDevice accDevice, String wirelessSSID, String wirelessKey, boolean isImme) {
        Map<String, String> optionMap = Maps.newHashMap();
        optionMap.put("TempWirelessSSID", wirelessSSID);
        optionMap.put("TempWirelessKey", wirelessKey);
        CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(accDevice.getSn(), cmd, isImme));
    }

    /**
     * @param accDevice
     * @param netConnectMode
     * @param wirelessSSID
     * @param wirelessKey
     * @param isImme
     * @return
     * @Description: 切换网络
     */
    public Long setNetConnectMode(AccDevice accDevice, String netConnectMode, String wirelessSSID, String wirelessKey,
        boolean isImme) {
        Map<String, String> optionMap = Maps.newHashMap();
        List<Long> cmdIdList = Lists.newArrayList();
        optionMap.put("CommType", netConnectMode);
        // TODO netConnectMode 暂时取值
        if (netConnectMode.equals("serial-wireless")) {
            optionMap.put("WirelessSSID", wirelessSSID);
            optionMap.put("WirelessKey", wirelessKey);
        }
        CmdDeviceConstructor.setOptions(optionMap)
            .forEach(cmd -> cmdIdList.add(addCmd(accDevice.getSn(), cmd, isImme)));
        return cmdIdList.get(0);
    }

    /**
     * @param devSn
     * @param isImme
     * @return
     * @Description: 删除夏令时
     */
    public void delDSTime(String devSn, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        addCmd(devSn, CmdDstsettingConstructor.delDST(cmdDevice), isImme);
        addCmd(devSn, CmdDstsettingConstructor.disableDST(cmdDevice), isImme);
    }

    /**
     * @param devSn
     * @param accDSTimeItem
     * @param isImme
     * @return
     * @Description: 设置夏令时
     */
    public void setDSTime(String devSn, AccDSTimeItem accDSTimeItem, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        addCmd(devSn,
            CmdDstsettingConstructor.setDST(cmdDevice, accDSTimeItem.getStartTime(), accDSTimeItem.getEndTime()),
            isImme);
        addCmd(devSn, CmdDstsettingConstructor.enableDST(cmdDevice), isImme);
    }

    /**
     * 下发时间段验证方式规则表
     *
     * @param accDevice
     * @param accVerifyModeRule
     * @param isImme
     */
    public void setDiffTimezoneVSToDev(AccDevice accDevice, AccVerifyModeRule accVerifyModeRule, boolean isImme) {
        boolean supportNewVerifyMode = accDeviceOptionService.isContainDevParam(accDevice.getSn(), "NewVFStyles");
        AccTimeSeg timeSeg = accVerifyModeRule.getAccTimeSeg();
        Set<CmdDiffTimezoneVSExt> diffTimezoneVSs = new HashSet<>();
        CmdDiffTimezoneVSExt cmdDiffTimezoneVS = new CmdDiffTimezoneVSExt(timeSeg.getBusinessId(),
            timeSeg.getSundayStart1(), timeSeg.getSundayEnd1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getSundayTime1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getSundayTime1VSDoor()), supportNewVerifyMode),
            timeSeg.getSundayStart2(), timeSeg.getSundayEnd2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getSundayTime2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getSundayTime2VSDoor()), supportNewVerifyMode),
            timeSeg.getSundayStart3(), timeSeg.getSundayEnd3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getSundayTime3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getSundayTime3VSDoor()), supportNewVerifyMode),
            timeSeg.getMondayStart1(), timeSeg.getMondayEnd1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getMondayTime1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getMondayTime1VSDoor()), supportNewVerifyMode),
            timeSeg.getMondayStart2(), timeSeg.getMondayEnd2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getMondayTime2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getMondayTime2VSDoor()), supportNewVerifyMode),
            timeSeg.getMondayStart3(), timeSeg.getMondayEnd3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getMondayTime3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getMondayTime3VSDoor()), supportNewVerifyMode),
            timeSeg.getTuesdayStart1(), timeSeg.getTuesdayEnd1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getTuesdayTime1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getTuesdayTime1VSDoor()), supportNewVerifyMode),
            timeSeg.getTuesdayStart2(), timeSeg.getTuesdayEnd2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getTuesdayTime2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getTuesdayTime2VSDoor()), supportNewVerifyMode),
            timeSeg.getTuesdayStart3(), timeSeg.getTuesdayEnd3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getTuesdayTime3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getTuesdayTime3VSDoor()), supportNewVerifyMode),
            timeSeg.getWednesdayStart1(), timeSeg.getWednesdayEnd1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getWednesdayTime1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getWednesdayTime1VSDoor()), supportNewVerifyMode),
            timeSeg.getWednesdayStart2(), timeSeg.getWednesdayEnd2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getWednesdayTime2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getWednesdayTime2VSDoor()), supportNewVerifyMode),
            timeSeg.getWednesdayStart3(), timeSeg.getWednesdayEnd3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getWednesdayTime3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getWednesdayTime3VSDoor()), supportNewVerifyMode),
            timeSeg.getThursdayStart1(), timeSeg.getThursdayEnd1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getThursdayTime1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getThursdayTime1VSDoor()), supportNewVerifyMode),
            timeSeg.getThursdayStart2(), timeSeg.getThursdayEnd2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getThursdayTime2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getThursdayTime2VSDoor()), supportNewVerifyMode),
            timeSeg.getThursdayStart3(), timeSeg.getThursdayEnd3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getThursdayTime3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getThursdayTime3VSDoor()), supportNewVerifyMode),
            timeSeg.getFridayStart1(), timeSeg.getFridayEnd1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getFridayTime1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getFridayTime1VSDoor()), supportNewVerifyMode),
            timeSeg.getFridayStart2(), timeSeg.getFridayEnd2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getFridayTime2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getFridayTime2VSDoor()), supportNewVerifyMode),
            timeSeg.getFridayStart3(), timeSeg.getFridayEnd3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getFridayTime3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getFridayTime3VSDoor()), supportNewVerifyMode),
            timeSeg.getSaturdayStart1(), timeSeg.getSaturdayEnd1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getSaturdayTime1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getSaturdayTime1VSDoor()), supportNewVerifyMode),
            timeSeg.getSaturdayStart2(), timeSeg.getSaturdayEnd2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getSaturdayTime2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getSaturdayTime2VSDoor()), supportNewVerifyMode),
            timeSeg.getSaturdayStart3(), timeSeg.getSaturdayEnd3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getSaturdayTime3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getSaturdayTime3VSDoor()), supportNewVerifyMode),
            timeSeg.getHolidayType1Start1(), timeSeg.getHolidayType1End1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType1Time1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getHolidayType1Time1VSDoor()), supportNewVerifyMode),
            timeSeg.getHolidayType1Start2(), timeSeg.getHolidayType1End2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType1Time2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getHolidayType1Time2VSDoor()), supportNewVerifyMode),
            timeSeg.getHolidayType1Start3(), timeSeg.getHolidayType1End3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType1Time3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getHolidayType1Time3VSDoor()), supportNewVerifyMode),
            timeSeg.getHolidayType2Start1(), timeSeg.getHolidayType2End1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType2Time1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getHolidayType2Time1VSDoor()), supportNewVerifyMode),
            timeSeg.getHolidayType2Start2(), timeSeg.getHolidayType2End2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType2Time2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getHolidayType2Time2VSDoor()), supportNewVerifyMode),
            timeSeg.getHolidayType2Start3(), timeSeg.getHolidayType2End3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType2Time3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getHolidayType2Time3VSDoor()), supportNewVerifyMode),
            timeSeg.getHolidayType3Start1(), timeSeg.getHolidayType3End1(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType3Time1VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getHolidayType3Time1VSDoor()), supportNewVerifyMode),
            timeSeg.getHolidayType3Start2(), timeSeg.getHolidayType3End2(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType3Time2VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(
                String.valueOf(accVerifyModeRule.getHolidayType3Time2VSDoor()), supportNewVerifyMode),
            timeSeg.getHolidayType3Start3(), timeSeg.getHolidayType3End3(),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType3Time3VSPerson()),
                supportNewVerifyMode),
            AccDeviceUtil.convertVerifyModeToBinary(String.valueOf(accVerifyModeRule.getHolidayType3Time3VSDoor()),
                supportNewVerifyMode));
        diffTimezoneVSs.add(cmdDiffTimezoneVS);
        addCmd(accDevice.getSn(),
            CmdDiffTimezoneVSExtConstructor.setDiffTimezoneVS(buildCmdDevice(accDevice), diffTimezoneVSs), isImme);
    }

    /**
     * 删除时间段验证方式数据
     *
     * @param accDevice
     * @param accVerifyModeRule
     * @param isImme
     */
    public void delDiffTimezoneVSFromDev(AccDevice accDevice, AccVerifyModeRule accVerifyModeRule, boolean isImme) {
        List<Long> timesegIdList = new ArrayList<>();
        timesegIdList.add(accVerifyModeRule.getAccTimeSeg().getBusinessId());
        addCmd(accDevice.getSn(),
            CmdDiffTimezoneVSExtConstructor.delDiffTimezoneVS(buildCmdDevice(accDevice), timesegIdList), isImme);
    }

    /**
     * 下发门时间段对应表
     *
     * @param accVerifyModeRule
     * @param accDevice
     * @param doorNoSet
     * @param isImme
     */
    public void setDoorVSTimezoneToDev(AccVerifyModeRule accVerifyModeRule, AccDevice accDevice, Set<Short> doorNoSet,
        boolean isImme) {
        Long accTimeSegbId = accVerifyModeRule.getAccTimeSeg().getBusinessId();
        Set<CmdDoorVSTimezone> doorVSTimezones = new HashSet<CmdDoorVSTimezone>();
        CmdDoorVSTimezone cmdDoorVSTimezone = null;
        for (Short doorNo : doorNoSet) {
            cmdDoorVSTimezone = new CmdDoorVSTimezone(accDevice.getBusinessId(), doorNo, accTimeSegbId);
            doorVSTimezones.add(cmdDoorVSTimezone);
        }
        addCmd(accDevice.getSn(),
            CmdDoorVSTimezoneConstructor.setDoorVSTimezone(buildCmdDevice(accDevice), doorVSTimezones), isImme);
    }

    /**
     * 删除门时间段对应表数据
     *
     * @param accDevice
     * @param doorNoSet
     * @param isImme
     */
    public void delDoorVSTimezoneFromDev(AccDevice accDevice, Set<Short> doorNoSet, boolean isImme) {
        addCmd(accDevice.getSn(), CmdDoorVSTimezoneConstructor.delDoorVSTimezone(buildCmdDevice(accDevice), doorNoSet),
            isImme);
    }

    /**
     * 下发人员时间段对应表
     *
     * @param accDevice
     * @param accVerifyModeRule
     * @param pins
     * @param doorNos
     */
    public void setUserVSTimezoneToDev(AccDevice accDevice, AccVerifyModeRule accVerifyModeRule,
        Collection<String> pins, Collection<Short> doorNos, boolean isImme) {
        Long accTimeSegbId = accVerifyModeRule.getAccTimeSeg().getBusinessId();
        Set<CmdPersonalVSTimezone> cmdPersonalVSTimezones = new HashSet<>();
        CmdPersonalVSTimezone cmdPersonalVSTimezone = null;
        for (String pin : pins) {
            for (Short doorNo : doorNos) {
                cmdPersonalVSTimezone =
                    new CmdPersonalVSTimezone(pin, accDevice.getBusinessId(), doorNo, accTimeSegbId);
                cmdPersonalVSTimezones.add(cmdPersonalVSTimezone);
            }
        }
        addCmd(accDevice.getSn(),
            CmdPersonalVSTimezoneConstructor.setPersonalVSTimezone(buildCmdDevice(accDevice), cmdPersonalVSTimezones),
            isImme);
    }

    /**
     * 删除人-时间段关联表的命令（根据pin号和门编号）
     *
     * @param dev
     * @param pins
     * @param doorNoSet
     * @param isImme
     */
    public void delUserVSTimezoneFromDevByPinAndDoorNo(AccDevice dev, Collection<String> pins,
        Collection<Short> doorNoSet, boolean isImme) {
        Set<CmdPersonalVSTimezone> cmdPersonalVSTimezoneSet = new HashSet<>();
        for (String pin : pins) {
            for (Short doorNo : doorNoSet) {
                cmdPersonalVSTimezoneSet.add(new CmdPersonalVSTimezone(pin, doorNo));
            }
        }
        addCmd(dev.getSn(), CmdPersonalVSTimezoneConstructor.delPersonalVSTimezoneByPinAndDoorNo(buildCmdDevice(dev),
            cmdPersonalVSTimezoneSet), isImme);
    }

    /**
     * 删除人-时间段关联表的命令（根据pin号）
     *
     * @param accDevice
     * @param pins
     */
    public void delUserVSTimezoneFromDevByPin(AccDevice accDevice, Collection<String> pins, boolean isImme) {
        addCmd(accDevice.getSn(),
            CmdPersonalVSTimezoneConstructor.delPersonalVSTimezoneByPin(buildCmdDevice(accDevice), pins), isImme);
    }

    /**
     * 清除不同时间段验证方式规则
     *
     * @param devSn
     * @param isImme
     */
    public void delAllVerifyModeRuleFromDev(String devSn, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        addCmd(devSn, CmdDiffTimezoneVSExtConstructor.delAllDiffTimezoneVS(cmdDevice), isImme);
        addCmd(devSn, CmdDoorVSTimezoneConstructor.delAllDoorVSTimezone(cmdDevice), isImme);
        addCmd(devSn, CmdPersonalVSTimezoneConstructor.delAllPersonalVSTimezone(cmdDevice), isImme);
    }

    /**
     * 下发设备属性
     *
     * @param accDevice
     * @param isImme
     */
    public void setDevPropertyToDev(AccDevice accDevice, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(accDevice.getSn());
        if (accDeviceService.isBestDevBySn(accDevice.getSn())) {
            addCmd(accDevice.getSn(), CmdBestDevPropertyConstructor.delALLDevProperty(cmdDevice), isImme);
            CmdBestDevProperty cmdBestDevProperty = new CmdBestDevProperty();
            cmdBestDevProperty.setId(accDevice.getBusinessId());
            cmdBestDevProperty.setMachineType(accDevice.getMachineType());
            cmdBestDevProperty.setIpAddress(accDevice.getIpAddress());
            cmdBestDevProperty.setSn(accDevice.getSn());
            addCmd(accDevice.getSn(), CmdBestDevPropertyConstructor.setDevProperty(cmdDevice, cmdBestDevProperty),
                isImme);
        } else {
            addCmd(accDevice.getSn(), CmdDevPropertyConstructor.delALLDevProperty(cmdDevice), isImme);
            CmdDevProperty cmdDevProperty = new CmdDevProperty(accDevice.getBusinessId(), accDevice.getMachineType(),
                accDevice.getIpAddress(), accDevice.getSn());
            String cmd = CmdDevPropertyConstructor.setDevProperty(cmdDevice, cmdDevProperty);
            if (accDevice.getParentDevice() != null) {
                addCmd(accDevice.getParentDevice().getSn(), cmd, isImme);
            }
            addCmd(accDevice.getSn(), cmd, isImme);
        }
    }

    /**
     * 下发门属性
     *
     * @param accDevice
     * @param accDoorList
     * @param isImme
     */
    public void setDoorPropertyToDev(AccDevice accDevice, List<AccDoor> accDoorList, boolean isImme) {
        if (accDoorList != null && accDoorList.size() > 0) {
            CmdDevice cmdDevice = buildCmdDevice(accDevice.getSn());
            if (accDeviceService.isBestDevBySn(accDevice.getSn())) {
                List<CmdBestDoorProperty> cmdBestDoorProperties = new ArrayList<>();
                for (AccDoor accDoor : accDoorList) {
                    CmdBestDoorProperty doorProperty = new CmdBestDoorProperty();
                    doorProperty.setDoorNo(accDoor.getDoorNo());
                    doorProperty.setDevId(accDevice.getBusinessId());
                    doorProperty.setAddress((int)accDoor.getDoorNo());
                    doorProperty.setDisable(!accDoor.getEnabled());
                    cmdBestDoorProperties.add(doorProperty);
                }
                addCmd(accDevice.getSn(),
                    CmdBestDoorPropertyConstructor.setDoorProperty(cmdDevice, cmdBestDoorProperties), isImme);
            } else {
                List<CmdDoorProperty> cmdDoorPropertyList = new ArrayList<>();
                for (AccDoor accDoor : accDoorList) {
                    cmdDoorPropertyList.add(new CmdDoorProperty(accDoor.getDoorNo(), accDevice.getBusinessId(),
                        (int)accDoor.getDoorNo(), !accDoor.getEnabled()));
                }
                String cmd = CmdDoorPropertyConstructor.setDoorProperty(cmdDevice, cmdDoorPropertyList);
                if (accDevice.getParentDevice() != null) {
                    addCmd(accDevice.getParentDevice().getSn(), cmd, isImme);
                }
                addCmd(accDevice.getSn(), cmd, isImme);
            }
        }
    }

    /**
     * 删除门属性
     *
     * @param devSn
     * @param accDoorList
     * @param isImme
     */
    public void delDoorPropertyFromDev(String devSn, List<AccDoorItem> accDoorList, boolean isImme) {
        if (accDoorList != null && accDoorList.size() > 0) {
            List<CmdDoorProperty> cmdDoorPropertyList = new ArrayList<>();
            CmdDevice cmdDevice = buildCmdDevice(devSn);
            for (AccDoorItem accDoor : accDoorList) {
                cmdDoorPropertyList.add(new CmdDoorProperty(accDoor.getDoorNo(), cmdDevice.getId(),
                    (int)accDoor.getDoorNo(), !accDoor.getEnabled()));
            }
            String cmd = CmdDoorPropertyConstructor.delDoorProperty(buildCmdDevice(devSn), cmdDoorPropertyList);
            AccDeviceItem parentDevItem = accDeviceService.getParentDeviceByDevSn(devSn);
            if (parentDevItem != null) {
                addCmd(parentDevItem.getSn(), cmd, isImme);
            }
            addCmd(devSn, cmd, isImme);
        }
    }

    /**
     * 删除所有门属性
     *
     * @param devSn
     * @param isImme
     */
    public void delAllDoorPropertyFromDev(String devSn, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestDoorPropertyConstructor.delALLDoorProperty(cmdDevice), isImme);
        } else {
            addCmd(devSn, CmdDoorPropertyConstructor.delALLDoorProperty(cmdDevice), isImme);
        }
    }

    /**
     * 修改设备IP
     *
     * @param accDevice
     * @param ipAddress
     * @param subnetMask
     * @param gateway
     * @param isImme
     * @return
     */
    public Long setDeviceIP(AccDevice accDevice, String ipAddress, String subnetMask, String gateway, boolean isImme) {
        return addCmd(accDevice.getSn(), CmdDeviceConstructor.setDeviceIP(ipAddress, subnetMask, gateway), isImme);
    }

    /**
     * @param accDevice
     * @param ipAddressSec
     * @param gateIPAddressSec
     * @param netMaskSec
     * @param serverCommIP
     * @param isImme
     * @return
     * @Description: 设置扩展网卡的ip地址
     */
    public Long setNetworkCardIpAddr(AccDevice accDevice, String ipAddressSec, String gateIPAddressSec,
        String netMaskSec, String serverCommIP, boolean isImme) {
        Map<String, String> optionMap = Maps.newHashMap();
        List<Long> cmdIdList = Lists.newArrayList();
        optionMap.put("IPAddress1", ipAddressSec);
        optionMap.put("GATEIPAddress1", gateIPAddressSec);
        optionMap.put("NetMask1", netMaskSec);
        if (StringUtils.isNotBlank(serverCommIP)) {
            optionMap.put("ServerCommIP", serverCommIP);
        }
        CmdDeviceConstructor.setOptions(optionMap)
            .forEach(cmd -> cmdIdList.add(addCmd(accDevice.getSn(), cmd, isImme)));
        return cmdIdList.get(0);
    }

    /**
     * 设置设备通信密码
     *
     * @param accDevice
     * @param commPwd
     * @param isImme
     * @return
     */
    public Long setCommPwd(AccDevice accDevice, String commPwd, boolean isImme) {
        return addCmd(accDevice.getSn(), CmdDeviceConstructor.setCommPwd(commPwd), isImme);
    }

    /**
     * 设置设备生物识别阈值
     *
     * @param devSn
     * @param acc4UpdateMThreshold
     * @param isImme
     * @return
     */
    public Long setMThreshold(String devSn, Acc4UpdateMThreshold acc4UpdateMThreshold, boolean isImme) {
        return addCmd(devSn, CmdDeviceConstructor.setMThreshold(new CmdDevThreshold(acc4UpdateMThreshold.getMThreshold(),
                acc4UpdateMThreshold.getFaceMThr(), acc4UpdateMThreshold.getPvMThreshold())), isImme);
    }

    /**
     * 下发联动
     *
     * @param devSn
     * @param accLinkageItem
     * @param isImme
     */
    public void setLinkageToDev(String devSn, AccLinkageItem accLinkageItem, boolean isImme) {
        List<AccLinkageInOut> accLinkageInOuts = accLinkageInOutDao.findByAccLinkage_Id(accLinkageItem.getId());
        String doorClassName = AccDoor.class.getSimpleName();
        String readerClassName = AccReader.class.getSimpleName();
        String auxInClassName = AccAuxIn.class.getSimpleName();
        String auxOutClassName = AccAuxOut.class.getSimpleName();
        List<CmdLinkage> cmdLinkageList = new ArrayList<>();
        for (AccLinkageInOut inOut : accLinkageInOuts) {
            String inputType = inOut.getInputType();
            String inputId = inOut.getInputId();
            Short inAddr = null;// 门编号或者读头编号或者辅助输入编号
            if (AccConstants.LINKAGE_ANY.equals(inputId)) {
                inAddr = 0;
            } else if (doorClassName.equals(inputType)) {
                inAddr = accDoorDao.getOne(inputId).getDoorNo();
            } else if (readerClassName.equals(inputType)) {
                inAddr = accReaderDao.getOne(inputId).getReaderNo();
            } else if (auxInClassName.equals(inputType)) {
                inAddr = accAuxInDao.getOne(inputId).getAuxNo();
            }
            String outputType = inOut.getOutputType();
            String outputId = inOut.getOutputId();
            int actionTime = inOut.getActionTime();// 联动输出动作执行时间. 0为关闭，255为常开， 1-254为打开时间.
            Short outAddr = null; // 门编号或者辅助输出编号
            if (AccConstants.LINKAGE_ALL.equals(outputId)) {// 输出点为“所有”
                outAddr = 0;
            } else if (doorClassName.equals(outputType)) {
                AccDoor accDoor = accDoorDao.getOne(outputId);
                outAddr = accDoor.getDoorNo();
            } else if (auxOutClassName.equals(outputType)) {
                AccAuxOut accAuxOut = accAuxOutDao.getOne(outputId);
                outAddr = accAuxOut.getAuxNo();
            } else if ("IPC".equals(outputType)) {
                // 当OutType为IPC联动时，outAddr无意义，赋值0即可
                outAddr = 0;
                String capture = "00";
                String video = "00";
                String videoBegin = "00";
                String videoAfter = "00";
                List<AccLinkageVid> accLinkageVidList = accLinkageVidDao.findByAccLinkage_Id(accLinkageItem.getId());
                for (AccLinkageVid vid : accLinkageVidList) {
                    if (vid.getActionType() == 2) {
                        video = "01";
                        videoBegin = String.valueOf(vid.getRecordBeforeTime());
                        videoAfter = String.valueOf(vid.getActionTime());
                    } else if (vid.getActionType() == 3) {
                        capture = "01";
                    }
                }
                actionTime = Integer.parseInt(videoAfter + videoBegin + video + capture, 16);
            }
            if (inAddr != null && outAddr != null) {
                int reserved =
                    (inputType.equals(readerClassName)) ? AccConstants.READER_LINKAGE : AccConstants.DOOR_LINKAGE;// 输入点为读头联动
                // 新增的联动动作里的锁定和解锁，下发命令时使用reserved的高四位，锁定第五位，解锁第六位
                if (inOut.getActionType() == AccConstants.ACTION_LOCK) {
                    reserved = 1 << 4 | reserved;
                } else if (inOut.getActionType() == AccConstants.ACTION_UNLOCK) {
                    reserved = 1 << 5 | reserved;
                }
                int outType = AccConstants.DOOR_LINKAGE;
                if (outputType.equals(auxOutClassName)) {
                    outType = AccConstants.AUX_LINKAGE;
                } else if ("IPC".equals(outputType)) {
                    outType = AccConstants.IPC_LINKAGE;
                }
                List<AccLinkageTrigger> triggerList = accLinkageTriggerDao
                    .findByAccLinkage_IdAndAccLinkageInOut_Id(inOut.getAccLinkage().getId(), inOut.getId());
                for (AccLinkageTrigger linkageTrigger : triggerList) {
                    cmdLinkageList.add(new CmdLinkage(linkageTrigger.getLinkageIndex(), linkageTrigger.getTriggerCond(),
                        inAddr, outType, outAddr, actionTime, reserved));
                }
            }
        }
        addCmd(devSn, CmdLinkageConstructor.setLinkage(buildCmdDevice(devSn), cmdLinkageList), isImme);
    }

    /**
     * 下发联动
     *
     * @param accDevice
     * @param accLinkage
     * @param isImme
     */
    @Deprecated
    public void setLinkageToDev(AccDevice accDevice, AccLinkage accLinkage, boolean isImme) {
        Set<AccLinkageInOut> accLinkageInOutSet = accLinkage.getAccLinkageInOutSet();
        String doorClassName = AccDoor.class.getSimpleName();
        String readerClassName = AccReader.class.getSimpleName();
        String auxInClassName = AccAuxIn.class.getSimpleName();
        String auxOutClassName = AccAuxOut.class.getSimpleName();
        List<CmdLinkage> cmdLinkageList = new ArrayList<>();
        for (AccLinkageInOut inOut : accLinkageInOutSet) {
            String inputType = inOut.getInputType();
            String inputId = inOut.getInputId();
            Short inAddr = null;// 门编号或者读头编号或者辅助输入编号
            if (AccConstants.LINKAGE_ANY.equals(inputId)) {
                inAddr = 0;
            } else if (doorClassName.equals(inputType)) {
                inAddr = accDoorDao.getOne(inputId).getDoorNo();
            } else if (readerClassName.equals(inputType)) {
                inAddr = accReaderDao.getOne(inputId).getReaderNo();
            } else if (auxInClassName.equals(inputType)) {
                inAddr = accAuxInDao.getOne(inputId).getAuxNo();
            }
            String outputType = inOut.getOutputType();
            String outputId = inOut.getOutputId();
            int actionTime = inOut.getActionTime();// 联动输出动作执行时间. 0为关闭，255为常开， 1-254为打开时间.
            Short outAddr = null; // 门编号或者辅助输出编号
            if (AccConstants.LINKAGE_ALL.equals(outputId)) {// 输出点为“所有”
                outAddr = 0;
            } else if (doorClassName.equals(outputType)) {
                AccDoor accDoor = accDoorDao.getOne(outputId);
                if (accDoor != null) {
                    outAddr = accDoor.getDoorNo();
                }
            } else if (auxOutClassName.equals(outputType)) {
                AccAuxOut accAuxOut = accAuxOutDao.getOne(outputId);
                if (accAuxOut != null) {
                    outAddr = accAuxOut.getAuxNo();
                }
            }
            if (inAddr != null && outAddr != null) {
                int reserved =
                    (inputType.equals(readerClassName)) ? AccConstants.READER_LINKAGE : AccConstants.DOOR_LINKAGE;// 输入点为读头联动
                // 新增的联动动作里的锁定和解锁，下发命令时使用reserved的高四位，锁定第五位，解锁第六位
                if (inOut.getActionType() == AccConstants.ACTION_LOCK) {
                    reserved = 1 << 4 | reserved;
                } else if (inOut.getActionType() == AccConstants.ACTION_UNLOCK) {
                    reserved = 1 << 5 | reserved;
                }
                int outType = outputType.equals(auxOutClassName) ? AccConstants.AUX_LINKAGE : AccConstants.DOOR_LINKAGE; // 0是输入点为门，1是输入点为辅助输出
                List<AccLinkageTrigger> triggerList = accLinkageTriggerDao
                    .findByAccLinkage_IdAndAccLinkageInOut_Id(inOut.getAccLinkage().getId(), inOut.getId());
                for (AccLinkageTrigger linkageTrigger : triggerList) {
                    cmdLinkageList.add(new CmdLinkage(linkageTrigger.getLinkageIndex(), linkageTrigger.getTriggerCond(),
                        inAddr, outType, outAddr, actionTime, reserved));
                }
            }
        }
        addCmd(accDevice.getSn(), CmdLinkageConstructor.setLinkage(buildCmdDevice(accDevice), cmdLinkageList), isImme);
    }

    /**
     * 删除联动设置
     *
     * @param devSn
     * @param linkIndexList
     * @param isImme
     */
    public void delLinkageFromDev(String devSn, List<Integer> linkIndexList, boolean isImme) {
        addCmd(devSn, CmdLinkageConstructor.delLinkage(linkIndexList), isImme);
    }

    /**
     * 清除设备联动
     *
     * @param devSn
     * @param isImme
     */
    public void delAllLinkageFromDev(String devSn, boolean isImme) {
        if (accDeviceService.isBestDevBySn(devSn)) {
            // todo 待完善
        } else {
            addCmd(devSn, CmdLinkageConstructor.delAllLinkage(), isImme);
        }
    }

    /**
     * 清除设备参数表
     *
     * @param devSn
     * @param isImme
     */
    public void delAllDevParametersFromDev(String devSn, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestDevParametersConstructor.delAllDevParameters(cmdDevice), isImme);
        } else {
            addCmd(devSn, CmdDevParametersConstructor.delAllDevParameters(cmdDevice), isImme);
        }
    }

    /**
     * 清空读头属性表
     *
     * @param accDevice
     * @param isImme
     */
    public void delAllReaderPropertyFromDev(AccDevice accDevice, boolean isImme) {
        addCmd(accDevice.getSn(), CmdReaderPropertyConstructor.delAllReaderProperty(buildCmdDevice(accDevice)), isImme);
    }

    /**
     * 清空门、辅助输入时间段设置表
     *
     * @param accDevice
     * @param isImme
     */
    public void delAllInputIOSettingFromDev(AccDevice accDevice, boolean isImme) {
        addCmd(accDevice.getSn(), CmdInputIOSettingConstructor.delAllInputIOSetting(buildCmdDevice(accDevice)), isImme);
    }

    /**
     * 下发辅助输入属性表
     *
     * @param accDevice
     * @param accAuxInList
     * @param isImme
     */
    public void setAuxInPropertyToDev(AccDevice accDevice, List<AccAuxIn> accAuxInList, boolean isImme) {
        if (accAuxInList != null && !accAuxInList.isEmpty()) {
            List<CmdAuxInProperty> cmdAuxInPropertyList = new ArrayList<>();
            accAuxInList.forEach(accAuxIn -> {
                cmdAuxInPropertyList
                    .add(new CmdAuxInProperty(accAuxIn.getAuxNo(), accDevice.getBusinessId(), accAuxIn.getAuxNo()));
            });
            String cmd = CmdAuxInPropertyConstructor.setAuxInProperty(buildCmdDevice(accDevice), cmdAuxInPropertyList);
            if (accDevice.getParentDevice() != null) {
                addCmd(accDevice.getParentDevice().getSn(), cmd, isImme);
            }
            addCmd(accDevice.getSn(), cmd, isImme);
        }
    }

    /**
     * 删除设备辅助输入属性表
     *
     * @param devSn
     * @param accAuxInList
     * @param isImme
     */
    public void delAuxInPropertyFromDev(String devSn, List<AccAuxInItem> accAuxInList, boolean isImme) {
        if (accAuxInList != null && !accAuxInList.isEmpty()) {
            CmdDevice cmdDevice = buildCmdDevice(devSn);
            List<CmdAuxInProperty> cmdAuxInPropertyList = new ArrayList<>();
            accAuxInList.forEach(accAuxIn -> {
                cmdAuxInPropertyList
                    .add(new CmdAuxInProperty(accAuxIn.getAuxNo(), cmdDevice.getId(), accAuxIn.getAuxNo()));
            });
            String cmd = CmdAuxInPropertyConstructor.delAuxInProperty(cmdDevice, cmdAuxInPropertyList);
            AccDeviceItem parentDevItem = accDeviceService.getParentDeviceByDevSn(devSn);
            if (parentDevItem != null) {
                addCmd(parentDevItem.getSn(), cmd, isImme);
            }
            addCmd(devSn, cmd, isImme);
        }
    }

    /**
     * 清除辅助输入属性表
     *
     * @param devSn
     * @param isImme
     */
    public void delAllAuxInPropertyFromDev(String devSn, boolean isImme) {
        addCmd(devSn, CmdAuxInPropertyConstructor.delALLAuxInProperty(buildCmdDevice(devSn)), isImme);
    }

    /**
     * 下发辅助输出属性表
     *
     * @param accDevice
     * @param accAuxOutList
     * @param isImme
     */
    public void setAuxOutPropertyToDev(AccDevice accDevice, List<AccAuxOut> accAuxOutList, boolean isImme) {
        if (accAuxOutList != null && !accAuxOutList.isEmpty()) {
            List<CmdAuxOutProperty> cmdAuxOutPropertyList = new ArrayList<>();
            accAuxOutList.forEach(accAuxOut -> {
                cmdAuxOutPropertyList
                    .add(new CmdAuxOutProperty(accAuxOut.getAuxNo(), accDevice.getBusinessId(), accAuxOut.getAuxNo()));
            });
            String cmd =
                CmdAuxOutPropertyConstructor.setAuxOutProperty(buildCmdDevice(accDevice), cmdAuxOutPropertyList);
            if (accDevice.getParentDevice() != null) {
                addCmd(accDevice.getParentDevice().getSn(), cmd, isImme);
            }
            addCmd(accDevice.getSn(), cmd, isImme);
        }
    }

    /**
     * 删除辅助输入属性表
     *
     * @param devSn
     * @param accAuxOutList
     * @param isImme
     */
    public void delAuxOutPropertyFromDev(String devSn, List<AccAuxOutItem> accAuxOutList, boolean isImme) {
        if (accAuxOutList != null && !accAuxOutList.isEmpty()) {
            List<CmdAuxOutProperty> cmdAuxOutPropertyList = new ArrayList<>();
            CmdDevice cmdDevice = buildCmdDevice(devSn);
            accAuxOutList.forEach(accAuxOut -> {
                cmdAuxOutPropertyList
                    .add(new CmdAuxOutProperty(accAuxOut.getAuxNo(), cmdDevice.getId(), accAuxOut.getAuxNo()));
            });
            String cmd = CmdAuxOutPropertyConstructor.delAuxOutProperty(buildCmdDevice(devSn), cmdAuxOutPropertyList);
            AccDeviceItem parentDevItem = accDeviceService.getParentDeviceByDevSn(devSn);
            if (parentDevItem != null) {
                addCmd(parentDevItem.getSn(), cmd, isImme);
            }
            addCmd(devSn, cmd, isImme);
        }
    }

    /**
     * 清除辅助输出属性表
     *
     * @param accDevice
     * @param isImme
     */
    public void delAllAuxOutPropertyFromDev(AccDevice accDevice, boolean isImme) {
        addCmd(accDevice.getSn(), CmdAuxOutPropertyConstructor.delALLAuxOutProperty(buildCmdDevice(accDevice)), isImme);
    }

    /**
     * @param devSn
     * @param doorNo
     * @param interval (0关闭，1~254开门时长，255常开)
     * @param isImme
     * @return
     * @Description: 远程开/关门
     */
    public Long ctrlDoor(String devSn, short doorNo, short interval, boolean isImme) {
        return addCmd(devSn, CmdDeviceConstructor.ctrlDoor(buildCmdDevice(devSn), doorNo, 1, interval), isImme);
    }

    /**
     * 远程开/关辅助输出
     *
     * @param devSn
     * @param auxOutNo
     * @param interval
     * @param isImme
     * @return
     */
    public Long ctrlAuxOut(String devSn, short auxOutNo, short interval, boolean isImme) {
        return addCmd(devSn, CmdDeviceConstructor.ctrlDoor(buildCmdDevice(devSn), auxOutNo, 2, interval), isImme);
    }

    /**
     * @param devSn
     * @param doorNo
     * @param isImme
     * @return
     * @Description: 取消报警
     */
    public Long cancelAlarm(String devSn, short doorNo, boolean isImme) {
        return addCmd(devSn, CmdDeviceConstructor.cancelAlarm(buildCmdDevice(devSn), doorNo), isImme);
    }

    /**
     * @param devSn
     * @param doorNo
     * @param enabled (0禁用 ，1启用)
     * @param isImme
     * @return
     * @Description: 启禁用常开时间段
     */
    public Long ctrlNormalOpenTimeZone(String devSn, short doorNo, int enabled, boolean isImme) {
        return addCmd(devSn, CmdDeviceConstructor.ctrlNormalOpenTimeZone(buildCmdDevice(devSn), doorNo, enabled),
            isImme);
    }

    /**
     * @param devSn
     * @param doorNo
     * @param lock (0解锁 ，1锁定)
     * @param isImme
     * @return
     * @Description: 启禁用常开时间段
     */
    public Long ctrlLockDoor(String devSn, short doorNo, int lock, boolean isImme) {
        return addCmd(devSn, CmdDeviceConstructor.ctrlLockDoor(buildCmdDevice(devSn), doorNo, lock), isImme);
    }

    /**
     * 下发默认韦根数据
     *
     * @param devSn
     * @param persWiegandFmtItemList
     * @param isImme
     */
    public void setDefWGFormatToDev(String devSn, List<PersWiegandFmtItem> persWiegandFmtItemList, boolean isImme) {
        if (persWiegandFmtItemList != null && !persWiegandFmtItemList.isEmpty()) {
            CmdDevice dev = buildCmdDevice(devSn);
            if (CmdSecConstants.SUPPORT.equals(dev.getAccSupportFunList(6))) {
                List<CmdDefWGFormat> cmdDefWGFormatList = new ArrayList<>();
                persWiegandFmtItemList.forEach(persWiegandFmtItem -> {
                    if (StringUtils.isNotEmpty(persWiegandFmtItem.getCardFmt())) {
                        cmdDefWGFormatList.add(
                            new CmdDefWGFormat(persWiegandFmtItem.getBusinessId(), persWiegandFmtItem.getWiegandCount(),
                                persWiegandFmtItem.getSiteCode(), persWiegandFmtItem.getName(),
                                persWiegandFmtItem.getCardFmt() + ":" + persWiegandFmtItem.getParityFmt()));
                    }
                });
                addCmd(devSn, CmdDefWGFormatConstructor.setDefWGFormat(dev, cmdDefWGFormatList), isImme);
            } else if (CmdSecConstants.SUPPORT.equals(dev.getCardFormatFunOn())) {
                Map<String, String> optionMap = new HashMap<>();
                persWiegandFmtItemList.forEach(persWiegandFmtItem -> {
                    if (StringUtils.isNotEmpty(persWiegandFmtItem.getCardFmt())) {
                        optionMap.put(String.format("DefWGFmt_%s", persWiegandFmtItem.getWiegandCount()),
                            persWiegandFmtItem.getCardFmt() + ":" + persWiegandFmtItem.getParityFmt());
                    }
                });
                CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(devSn, cmd, isImme));
            }
        }
    }

    /**
     * 删除默认韦根数据
     *
     * @param devSn
     * @param persWiegandFmtItemList
     * @param isImme
     */
    public void delDefWGFormatFromDev(String devSn, List<PersWiegandFmtItem> persWiegandFmtItemList, boolean isImme) {
        if (persWiegandFmtItemList != null && !persWiegandFmtItemList.isEmpty()) {
            List<CmdDefWGFormat> cmdDefWGFormatList = new ArrayList<>();
            persWiegandFmtItemList.forEach(persWiegandFmtItem -> cmdDefWGFormatList
                .add(new CmdDefWGFormat(persWiegandFmtItem.getBusinessId(), persWiegandFmtItem.getWiegandCount())));
            addCmd(devSn, CmdDefWGFormatConstructor.delDefWGFormat(buildCmdDevice(devSn), cmdDefWGFormatList), isImme);
        }
    }

    /**
     * 清空默认韦根表
     *
     * @param devSn
     * @param isImme
     */
    public void delAllDefWGFormat(String devSn, boolean isImme) {
        addCmd(devSn, CmdDefWGFormatConstructor.delAllDefWGFormat(buildCmdDevice(devSn)), isImme);
    }

    /**
     * 设置韦根格式带sitecode开关
     *
     * @param devSn
     * @param isOpen
     * @param isImme
     */
    public void setWGFmtWithSitecodeOn(String devSn, boolean isOpen, boolean isImme) {
        addCmd(devSn, CmdDeviceConstructor.setWGFmtWithSitecodeOn(buildCmdDevice(devSn), isOpen), isImme);
    }

    /**
     * 下发韦根格式表（一体机）
     *
     * @param accDevice
     * @param persWiegandFmtItemList
     * @param isImme
     */
    public void setCardFormatToDev(AccDevice accDevice, List<PersWiegandFmtItem> persWiegandFmtItemList,
        boolean isImme) {
        Map<Short, String> siteCode = new HashMap<>();
        Map<Short, String> wgName = new HashMap<>();
        Map<Short, String> cardFmt = new HashMap<>();
        Map<Short, String> parityFmt = new HashMap<>();
        for (PersWiegandFmtItem wg : persWiegandFmtItemList) {
            siteCode.put(wg.getWiegandCount(), StringUtils.isNotBlank(siteCode.get(wg.getWiegandCount()))
                ? siteCode.get(wg.getWiegandCount()) + "," + wg.getSiteCode() : wg.getSiteCode());
            wgName.put(wg.getWiegandCount(), wg.getName());
            cardFmt.put(wg.getWiegandCount(), wg.getCardFmt());
            parityFmt.put(wg.getWiegandCount(), wg.getParityFmt());
        }
        List<CmdCardFormat> cmdCardFormatList = new ArrayList<>();
        for (Short wiegandCount : siteCode.keySet()) {
            String tempParityFmt = parityFmt.get(wiegandCount).substring(1, parityFmt.get(wiegandCount).length() - 1);// 一体机处理需要把奇偶校验头尾改为0，先把头尾去除再补0
            for (int i = 1; i < 4; i++) {
                cmdCardFormatList.add(new CmdCardFormat(wiegandCount, siteCode.get(wiegandCount),
                    wgName.get(wiegandCount), cardFmt.get(wiegandCount),
                    "0" + tempParityFmt.replaceAll("e|E", "1").replaceAll("o|O", "0") + "0", "",
                    "0" + tempParityFmt.replaceAll("e|E", "0").replaceAll("o|O", "1") + "0", "", i, 1));
            }
        }
        if (!cmdCardFormatList.isEmpty()) {
            addCmd(accDevice.getSn(),
                CmdCardFormatConstructor.setCardFormat(buildCmdDevice(accDevice), cmdCardFormatList), isImme);
        }
    }

    /**
     * 清除韦根格式表（一体机用）
     *
     * @param accDevice
     * @param isImme
     */
    public void delAllCardFormat(AccDevice accDevice, boolean isImme) {
        addCmd(accDevice.getSn(), CmdCardFormatConstructor.delAllCardFormat(buildCmdDevice(accDevice)), isImme);
    }

    /**
     * 根据韦根格式位数置空韦根格式，类似于删除（老设备用）
     *
     * @param accDevice
     * @param cardBits
     * @param isImme
     */
    public void delDefWGByCardBit(AccDevice accDevice, Collection<Short> cardBits, boolean isImme) {
        if (accDeviceOptionService.isSupportFun(accDevice.getSn(), "~CardFormatFunOn")) {
            Map<String, String> optionMap = new HashMap<>();
            for (Short cardBit : cardBits) {
                optionMap.put(String.format("DefWGFmt_%s", cardBit), "");
            }
            CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(accDevice.getSn(), cmd, isImme));
        }
    }

    /**
     * 设置一人多卡功能
     *
     * @param accDevice
     * @param isOpen
     * @param isImme
     */
    public void setUserWithMulCardFunOn(AccDevice accDevice, boolean isOpen, boolean isImme) {
        addCmd(accDevice.getSn(), CmdDeviceConstructor.setUserWithMulCardFunOn(buildCmdDevice(accDevice), isOpen),
            isImme);
    }

    /**
     * 下发自定义韦根格式表
     *
     * @param devSn
     * @param persWiegandFmtItemList
     * @param isImme
     */
    public void setCusWGFormatToDev(String devSn, List<PersWiegandFmtItem> persWiegandFmtItemList, boolean isImme) {
        if (persWiegandFmtItemList != null && !persWiegandFmtItemList.isEmpty()) {
            CmdDevice cmdDevice = buildCmdDevice(devSn);
            List<CmdWGFormat> cmdWGFormatList = new ArrayList<>();
            Map<String, String> optionMap = new HashMap<>();
            boolean supportWGWithSitecode = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(6));
            persWiegandFmtItemList.forEach(persWiegandFmtItem -> {
                if (StringUtils.isNotEmpty(persWiegandFmtItem.getCardFmt())) {
                    String wgFmt = persWiegandFmtItem.getCardFmt() + ":" + persWiegandFmtItem.getParityFmt();
                    cmdWGFormatList
                        .add(new CmdWGFormat(persWiegandFmtItem.getBusinessId(), persWiegandFmtItem.getWiegandCount(),
                            persWiegandFmtItem.getSiteCode(), persWiegandFmtItem.getName(), wgFmt));
                    if (!supportWGWithSitecode) {// 不支持韦根表的老设备需要更新读头韦根的配置
                        List<AccDoor> doorList = accDoorDao.findByWgInputFmtIdAndDevice_Id(persWiegandFmtItem.getId(),
                            cmdDevice.getDeviceId());
                        for (AccDoor door : doorList) {
                            for (AccReader accReader : door.getAccReaderList()) {
                                optionMap.put(String.format("Reader%dWGType", accReader.getReaderNo()), wgFmt);
                            }
                        }
                    }
                }
            });
            addCmd(devSn, CmdWGFormatConstructor.setWGFormat(cmdDevice, cmdWGFormatList), isImme);
            CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(devSn, cmd, isImme));
        }
    }

    /**
     * 删除自定义韦根数据
     *
     * @param devSn
     * @param persWiegandFmtItemList
     * @param isImme
     */
    public void delCusWGFormatFromDev(String devSn, List<PersWiegandFmtItem> persWiegandFmtItemList, boolean isImme) {
        if (persWiegandFmtItemList != null && !persWiegandFmtItemList.isEmpty()) {
            List<CmdWGFormat> cmdWGFormatList = new ArrayList<>();
            persWiegandFmtItemList.forEach(persWiegandFmtItem -> cmdWGFormatList
                .add(new CmdWGFormat(persWiegandFmtItem.getBusinessId(), persWiegandFmtItem.getWiegandCount())));
            addCmd(devSn, CmdWGFormatConstructor.delWGFormat(buildCmdDevice(devSn), cmdWGFormatList), isImme);
        }
    }

    /**
     * 清空自定义韦根表
     *
     * @param devSn
     * @param isImme
     */
    public void delAllWGFormat(String devSn, boolean isImme) {
        addCmd(devSn, CmdWGFormatConstructor.delAllWGFormat(buildCmdDevice(devSn)), isImme);
    }

    /**
     * 设置门锁定状态
     *
     * @param accDevice
     * @param doorList
     * @param openOrClose
     * @param isImme
     */
    public void setDoorMaskFlag(AccDevice accDevice, List<AccDoor> doorList, boolean openOrClose, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(accDevice);
        if (CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(2))) {
            List<Short> doorNoList = new ArrayList<>();
            for (AccDoor accDoor : doorList) {
                doorNoList.add(accDoor.getDoorNo());
            }
            String value = openOrClose ? "1" : "0";
            if (CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(21))) {
                List<CmdDoorParameters> cmdDoorParametersList = new ArrayList<>();
                doorNoList.forEach(doorNo -> cmdDoorParametersList
                    .add(new CmdDoorParameters(doorNo, "MaskFlag", value, accDevice.getBusinessId())));
                String cmd = CmdDoorParametersConstructor.setDoorParameters(cmdDevice, cmdDoorParametersList);
                if (accDevice.getParentDevice() != null) {
                    addCmd(accDevice.getParentDevice().getSn(), cmd, false);
                }
                addCmd(accDevice.getSn(), cmd, isImme);
            } else {
                Map<String, String> optionMap = new HashMap<>();
                doorNoList.forEach(doorNo -> optionMap.put(String.format("Door%sMaskFlag", doorNo), value));
                CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(accDevice.getSn(), cmd, false));
            }
        }
    }

    /**
     * 获取事件记录表数据
     *
     * @return
     */
    public Long getTransaction(String devSn, boolean isGetNewLog, boolean imme) {
        AccDeviceItem parentDevice = accDeviceService.getParentDeviceByDevSn(devSn);
        String extSn = "";
        if (parentDevice != null) {
            extSn = parentDevice.getSn();
        } else {
            extSn = devSn;
        }
        if (accDeviceService.isBestDevBySn(devSn)) {
            return addCmd(extSn, CmdBestTransactionConstructor.queryTransaction(buildCmdDevice(extSn),
                Collections.singletonList(devSn), isGetNewLog), imme);
        } else {
            return addCmd(extSn, CmdTransactionConstructor.getTransaction(isGetNewLog, devSn), imme);
        }
    }

    /**
     * 获取事件记录表数量
     *
     * @return
     */
    public Long getTransactionCount(AccDevice accDevice, boolean imme) {
        return addCmd(accDevice.getSn(), CmdTransactionConstructor.getTransactionCount(), imme);
    }

    /**
     * 获取传入下标之前的记录
     *
     * @param devSn
     * @param maxIndex
     * @param imme
     * @return
     */
    public Long getTransactionByMaxIndex(String devSn, Integer maxIndex, boolean imme) {
        return addCmd(devSn, CmdTransactionConstructor.getTransactionByMaxIndex(buildCmdDevice(devSn), maxIndex), imme);
    }

    /**
     * 获取设备wifi列表
     *
     * @param devSn
     * @param imme
     * @return
     */
    public Long searchWifiListFromDev(String devSn, boolean imme) {
        return addCmd(devSn, CmdDeviceConstructor.getWifiList(buildCmdDevice(devSn)), imme);
    }

    /**
     * 授权子设备
     *
     * @param accDevice
     * @param snList 子设备SN列表
     * @param devAuthorizeState 设备授权状态
     * @param isImme
     */
    public void authorizeChildDevice(AccDevice accDevice, List<String> snList, Short devAuthorizeState,
        boolean isImme) {
        if (snList != null && !snList.isEmpty()) {
            List<CmdDeviceAuthorize> cmdDeviceAuthorizeList = new ArrayList<>();
            snList.forEach(sn -> cmdDeviceAuthorizeList.add(new CmdDeviceAuthorize(sn, devAuthorizeState)));
            addCmd(accDevice.getSn(),
                CmdDeviceAuthorizeConstructor.setDeviceAuthorize(buildCmdDevice(accDevice), cmdDeviceAuthorizeList),
                isImme);
        }
    }

    /**
     * 删除子设备
     *
     * @param accDevice
     * @param snList
     * @param isImme
     */
    public void delChildDevice(AccDevice accDevice, List<String> snList, boolean isImme) {
        if (snList != null && !snList.isEmpty()) {
            addCmd(accDevice.getSn(),
                CmdDeviceAuthorizeConstructor.delDeviceAuthorize(buildCmdDevice(accDevice), snList), isImme);
        }
    }

    /**
     * 清空设备授权表数据
     *
     * @param accDevice
     * @param isImme
     */
    public void delAllChildDevice(AccDevice accDevice, boolean isImme) {
        addCmd(accDevice.getSn(), CmdDeviceAuthorizeConstructor.delAllDeviceAuthorize(buildCmdDevice(accDevice)),
            isImme);
    }

    /**
     * 查询设备授权列表
     *
     * @param accDevice
     * @param isImme
     * @return
     */
    public Long queryAuthorizeListFromDev(AccDevice accDevice, boolean isImme) {
        return addCmd(accDevice.getSn(), CmdDeviceAuthorizeConstructor.getAllDeviceAuthorize(buildCmdDevice(accDevice)),
            isImme);
    }

    /**
     * 设置 webServerURL 用于多级控制器子控切主控
     *
     * @param accDevice
     * @param masterControlOn
     * @param subControlOn
     * @param webServerURL
     * @param isImme
     * @return
     */
    public Long setChildWebServerUrl(AccDevice accDevice, boolean masterControlOn, boolean subControlOn,
        String webServerURL, boolean isImme) {
        List<Long> cmdIdList = Lists.newArrayList();
        Map<String, String> optionMap = Maps.newHashMap();
        optionMap.put("~PushFunOn", subControlOn ? "0" : "1");
        optionMap.put("MasterControlOn", masterControlOn ? "1" : "0");
        optionMap.put("SubControlOn", subControlOn ? "1" : "0");
        optionMap.put("WebServerURL", webServerURL);
        CmdDeviceConstructor.setOptions(optionMap)
            .forEach(cmd -> cmdIdList.add(addCmd(accDevice.getSn(), cmd, isImme)));
        return cmdIdList.get(0);
    }

    /**
     * 检测服务连接，用于inbio5主动检测连接IR9000的服务
     *
     * @param accDevice
     * @param serverAddress
     * @param isImme
     * @return
     */
    public long checkServerConnect(AccDevice accDevice, String serverAddress, boolean isImme) {
        String cmd = String.format("TEST HOST Address=%s\r\n", serverAddress);
        return addCmd(accDevice.getSn(), cmd, isImme);
    }

    /**
     * 设置BestSvrAddress 用于inbio5连接IR9000
     *
     * @param accDevice
     * @param masterControlOn
     * @param subControlOn
     * @param bestSvrAddress
     * @param isImme
     * @return
     */
    public Long setBestSvrAddress(AccDevice accDevice, boolean masterControlOn, boolean subControlOn,
        String bestSvrAddress, boolean isImme) {
        List<Long> cmdIdList = Lists.newArrayList();
        Map<String, String> optionMap = Maps.newHashMap();
        optionMap.put("~PushFunOn", subControlOn ? "0" : "1");
        optionMap.put("MasterControlOn", masterControlOn ? "1" : "0");
        optionMap.put("SubControlOn", subControlOn ? "1" : "0");
        optionMap.put("BestSvrAddress", bestSvrAddress);
        CmdDeviceConstructor.setOptions(optionMap)
            .forEach(cmd -> cmdIdList.add(addCmd(accDevice.getSn(), cmd, isImme)));
        return cmdIdList.get(0);
    }

    /**
     * 获取设备参数
     *
     * @param accDevice
     * @param optionsList
     * @param isImme
     * @return
     */
    public List<Long> getDeviceOption(AccDevice accDevice, List<String> optionsList, boolean isImme) {
        List<Long> cmdIdList = Lists.newArrayList();
        CmdDeviceConstructor.getOptions(optionsList)
            .forEach(cmd -> cmdIdList.add(addCmd(accDevice.getSn(), cmd, isImme)));
        return cmdIdList;
    }

    /**
     * 一体机配置主从机
     *
     * @param accDevice
     * @param masterSlave
     * @param isImme
     * @return
     */
    public Long updateConfigMasterSlave(AccDevice accDevice, String masterSlave, boolean isImme) {
        Map<String, String> optionMap = Maps.newHashMap();
        List<Long> cmdIdList = Lists.newArrayList();
        if (AccConstants.ACC_MASTERSLAVE_CONFIG_CANCEL.equals(masterSlave)) { // 取消主从机
            optionMap.put("PC485AsInbio485", "0");
            optionMap.put("MasterInbio485", "0");
        } else if (AccConstants.ACC_MASTERSLAVE_CONFIG_MASTER.equals(masterSlave)) { // 设置为主机
            optionMap.put("PC485AsInbio485", "1");
            optionMap.put("MasterInbio485", "1");
            optionMap.put("RS232BaudRate", "115200");
        } else if (AccConstants.ACC_MASTERSLAVE_CONFIG_SLAVE.equals(masterSlave)) { // 设置为从机
            optionMap.put("PC485AsInbio485", "1");
            optionMap.put("MasterInbio485", "0");
            optionMap.put("RS232BaudRate", "115200");
        }
        CmdDeviceConstructor.setOptions(optionMap)
            .forEach(cmd -> cmdIdList.add(addCmd(accDevice.getSn(), cmd, isImme)));
        return cmdIdList.get(0);
    }

    /**
     * 获取子设备状态
     *
     * @param accDevice
     * @param isImme
     * @return
     */
    public void getDevRTState(AccDevice accDevice, boolean isImme) {
        addCmd(accDevice.getSn(), CmdDeviceConstructor.getChildDevRTState(buildCmdDevice(accDevice)), isImme);
    }

    /**
     * 韦根测试命令
     *
     * @param devSn
     * @param openOrClose
     * @param isImme
     * @return
     */
    public void ctrlWGTest(String devSn, boolean openOrClose, boolean isImme) {
        addCmd(devSn, CmdDeviceConstructor.ctrlWGTest(buildCmdDevice(devSn), openOrClose ? 1 : 2), isImme);
    }

    /**
     * 删除人员比对照片
     *
     * @param devSn
     * @param pins
     * @param imme
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年9月28日 上午11:01:31
     */
    public void delAllBiophotoByPins(String devSn, List<String> pins, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestBioPhotoConstructor.delBioPhotoByPin(cmdDevice, pins), imme);
        } else {
            addCmd(devSn, CmdBiophotoConstructor.delBiophotos(cmdDevice, pins), imme);
        }
    }

    /**
     * 根据类型删除人员照片数据
     *
     * @param devSn
     * @param pins
     * @param bioType
     * @param imme
     */
    public void delBiophotoByPinsAndType(String devSn, List<String> pins, Short bioType, boolean imme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestBioPhotoConstructor.delBioPhotoByPinAndType(cmdDevice, pins, bioType), imme);
        } else {
            addCmd(devSn, CmdBiophotoConstructor.delBiophotosByType(cmdDevice, pins, bioType), imme);
        }
    }

    /**
     * 升级固件
     *
     * @param devSn
     * @param file
     * @param host
     * @param port
     * @param imme
     * @return
     */
    public Long upgradeFirmware(String devSn, File file, String host, int port, boolean imme) throws Exception {
        String url = file.toString().substring(file.toString().indexOf("upload") - 1).replace("\\", "/");
        // 支持断点续传文件升级功能的，可使用相对路径
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (!CmdSecConstants.SUPPORT.equals(cmdDevice.getSubcontractingUpgradeFunOn())) {
            boolean isSSL = "true".equals(isSupportHttps);
            if (isSSL) {
                url = "https*//" + host + "*" + port + url;
            } else {
                url = "http*//" + host + "*" + port + url;
            }
        }
        AccDeviceItem parentDevice = accDeviceService.getParentDeviceByDevSn(devSn);
        if (parentDevice != null) {
            return admsDevCmdService.addCmdWithTimeout(parentDevice.getSn(),
                CmdDeviceConstructor.upgradeFirmware(cmdDevice, file, url, devSn), imme, (long)1800);
        } else {
            return admsDevCmdService.addCmdWithTimeout(devSn,
                CmdDeviceConstructor.upgradeFirmware(cmdDevice, file, url, null), imme, (long)1800);
        }
    }

    /**
     * 设置设备进出状态
     *
     * @param accDevice
     * @param devIOState
     * @param isImme
     * @return
     * <AUTHOR>
     * @since 2018年12月7日 下午4:13:08
     */
    public long setDevIOState(AccDevice accDevice, int devIOState, boolean isImme) {
        return addCmd(accDevice.getSn(), CmdDeviceConstructor.setDevIOState(devIOState), isImme);
    }

    /**
     * 下发人员信息到设备
     *
     * @return
     * <AUTHOR>
     * @Param [device, persOptItem]
     * @since 2018-12-28 11:23
     */
    public void sendPersToDev(AccDevice device, AccPersonOptItem persOptItem) {
        // 拷贝临时对象，避免设置卡号将对象卡号更新导致卡号下发错误
        AccPersonOptItem tempPersonOptItem = ModelUtil.copyProperties(persOptItem, new AccPersonOptItem());
        String cardhex = baseSysParamService.getValByName("pers.cardHex");// 当前系统卡号进制
        // AccPersonOptItem accPersonOptItem = new AccPersonOptItem();
        // ModelUtil.copyProperties(persOptItem, accPersonOptItem);
        boolean isMulCardUser = accDeviceOptionService.isSupportFunList(device.getSn(), 7);// 是否支持多卡
        String cardNo = "";
        if (tempPersonOptItem.getCardState() != null && tempPersonOptItem.getCardState() == PersConstants.CARD_VALID) {
            cardNo = tempPersonOptItem.getCardNo();
        }
        if (StringUtils.isNotBlank(cardNo)) {
            if (isMulCardUser) {
                if (cardhex.equals("0")) {
                    // 表示当前支持多卡并且是10进制卡，此类固件需要下发16进制数据
                    cardNo = AccDeviceUtil.convertCardNo(cardNo, 10, 16);
                }
            } else if (cardhex.equals("1")) {
                // 表示当前不支持多卡并且是16进制卡，此类固件需要下发10进制数据
                cardNo = AccDeviceUtil.convertCardNo(cardNo, 16, 10);
            }
            tempPersonOptItem.setCardNo(cardNo);
        }
        boolean isLongNameOn = accDeviceOptionService.isSupportFunList(device.getSn(), 5);// 是否支持长姓名
        String name = "";
        if (!isLongNameOn
            && (StringUtils.isNotBlank(persOptItem.getName()) || StringUtils.isNotBlank(persOptItem.getLastName()))) {
            name = (persOptItem.getName() + " " + persOptItem.getLastName()).trim();
            tempPersonOptItem.setName(name);
        }
        sendCmdUserInfoToDev(device, tempPersonOptItem, false);

        if (isMulCardUser && !StringUtils.isEmpty(cardNo)) {// 根据参数判断是否支持一人多卡表
            sendMulCardUserInfoToDev(device, tempPersonOptItem, false);
        }

        if (isLongNameOn) {
            sendExtInfoToDev(device, tempPersonOptItem, false);
        }
    }

    /**
     * 下发user表信息到设备
     *
     * @return
     * <AUTHOR>
     * @Param [device, persOptItem, isImme]
     * @since 2018-12-28 11:24
     */
    public void sendCmdUserInfoToDev(AccDevice device, AccPersonOptItem persOptItem, boolean isImme) {
        boolean isSuperAuthorize = accDeviceOptionService.isSupportFunList(device.getSn(), 16);// 支持超级用户表
        boolean isMulCardUser = accDeviceOptionService.isSupportFunList(device.getSn(), 7);// 是否支持多卡
        Set<CmdUser> cmdUserSet = new HashSet<CmdUser>();
        if (!isSuperAuthorize || persOptItem.getSuperAuth() == null) {
            persOptItem.setSuperAuth(Short.parseShort("0"));
        }
        if (persOptItem.getCardType() == null || persOptItem.getCardType() == PersConstants.MAIN_CARD) {// 主卡信息加入user表，防止pin重复，支持多卡的设备不需要对cardno赋值
            CmdUser cmdUser = new CmdUser(persOptItem.getPin(), persOptItem.getName(), persOptItem.getPersonPwd(),
                isMulCardUser ? "" : (StringUtils.isNotBlank(persOptItem.getCardNo()) ? persOptItem.getCardNo() : ""),
                persOptItem.getStartTime(), persOptItem.getEndTime(), persOptItem.getSuperAuth(),
                persOptItem.getGroupId() == null ? 0 : persOptItem.getGroupId(), persOptItem.getDisabled(),
                persOptItem.getPrivilege(), persOptItem.getIdCard());
            cmdUserSet.add(cmdUser);
        }
        CmdDevice cmdDevice = buildCmdDevice(device);
        // 父设备非空为子设备
        if (device.getParentDevice() != null) {
            addCmd(device.getSn(), CmdUserConstructor.setUserToChildDev(cmdUserSet), isImme);
        } else {
            addCmd(device.getSn(), CmdUserConstructor.setUser(cmdUserSet, cmdDevice), isImme);
        }
    }

    /**
     * 下发多卡表信息到设备
     *
     * @return
     * <AUTHOR>
     * @Param [device, persOptItem, isImme]
     * @since 2018-12-28 11:25
     */
    public void sendMulCardUserInfoToDev(AccDevice device, AccPersonOptItem persOptItem, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(device);
        List<CmdMulCardUser> cmdMulCardUserList = new ArrayList<>();
        if (persOptItem.getCardType() == null) {
            persOptItem.setCardType(PersConstants.MAIN_CARD);
        }
        cmdMulCardUserList
            .add(new CmdMulCardUser(persOptItem.getPin(), persOptItem.getCardNo(), persOptItem.getCardType()));
        addCmd(device.getSn(), CmdMulCardUserConstructor.setMulCardUser(cmdMulCardUserList, cmdDevice), isImme);
    }

    /**
     * 下发扩展表信息到设备
     *
     * @return
     * <AUTHOR>
     * @Param [device, accPersonOptItem, isImme]
     * @since 2018-12-28 11:25
     */
    public void sendExtInfoToDev(AccDevice device, AccPersonOptItem accPersonOptItem, boolean isImme) {
        Set<CmdExtuser> cmdExtuserSet = new HashSet<>();
        CmdExtuser cmdExtuser =
            new CmdExtuser(accPersonOptItem.getPin(), accPersonOptItem.getName(), accPersonOptItem.getLastName(),
                accPersonOptItem.getPhotoPath(), accPersonOptItem.getDelayPassage(), accPersonOptItem.getTempUser());
        cmdExtuserSet.add(cmdExtuser);
        CmdDevice cmdDevice = buildCmdDevice(device);
        addCmd(device.getSn(), CmdExtuserConstructor.setExtuser(cmdExtuserSet, cmdDevice), isImme);
    }

    /**
     * 下发远程登记请求命令
     *
     * @param device
     * @param templateType
     * @param templateNo
     * @param pin
     * @param isImme
     * @return
     * <AUTHOR>
     * @since 2018年12月13日 下午5:17:04
     */
    public long remoteRegistration(AccDevice device, int templateType, int templateNo, String pin, boolean isImme) {
        // RETRY=0可以重试，1不重试
        String cmd = "ENROLL_BIO TYPE=%s\tNO=%s\tPIN=%s\tRETRY=0\tOVERWRITE=1\r\n";
        cmd = String.format(cmd, templateType, templateNo, pin);
        return addCmd(device.getSn(), cmd, isImme);
    }

    /**
     * 下发图片到设备
     *
     * @param devSn
     * @param persOptItem need pin PhotoPath(url/base64)
     * @param photoType (short)0：base64方式, (short)1：url方式
     * @param isImme
     */
    public void sendBioPhotoInfoToDev(String devSn, AccPersonOptItem persOptItem, short photoType, boolean isImme) {
        // 门禁设备
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        CmdBiophoto biophoto = new CmdBiophoto();
        biophoto.setPin(persOptItem.getPin());
        biophoto.setContent(persOptItem.getBioPhotoBase64());
        biophoto.setType((short)9);
        biophoto.setFormat(photoType);// 下发方式，0：base64方式, 1：url方式
        biophoto.setUrl(persOptItem.getBioPhotoPath());
        // 添加
        addCmd(devSn, CmdBiophotoConstructor.setBioPhoto(cmdDevice, biophoto), isImme);
    }

    /**
     * 功能描述: 下发设置身份证登记模式
     *
     * @return
     * <AUTHOR>
     * @Param [device, readMode, doorNo, timeOut]
     * @since 2019-05-09 20:21
     */
    public Long setIdCardRegistrationMode(AccDevice device, int readMode, Short doorNo, int timeOut) {
        // 门禁设备
        CmdDevice cmdDevice = buildCmdDevice(device);
        String cmd = CmdDeviceConstructor.setIDCardRegistrationMode(cmdDevice, readMode, doorNo, timeOut);
        return addCmd(device.getSn(), cmd, true);
    }

    /**
     * 下发用户照片到设备
     *
     * @return
     * <AUTHOR>
     * @Param [devSn, bioPhotoItem, isImme]
     * @since 2019-07-02 15:34
     */
    public void sendUserPicToDev(String devSn, AccPersonOptItem accPersonOptItem, boolean isImme) {
        // 传的base64要是解密的数据
        String photoBase64 = accPersonOptItem.getPhotoBase64();
        if (StringUtils.isBlank(photoBase64) && StringUtils.isNotBlank(accPersonOptItem.getPhotoPath())) {
            photoBase64 = FileEncryptUtil.getDecryptFileBase64(FileUtil.getThumbPath(accPersonOptItem.getPhotoPath()));
        }
        if (StringUtils.isNotBlank(photoBase64)) {
            CmdUserPic cmdUserPic = new CmdUserPic();
            cmdUserPic.setContent(photoBase64);
            cmdUserPic.setSize(String.valueOf(photoBase64.length()));
            cmdUserPic.setPin(accPersonOptItem.getPin());
            addCmd(devSn, CmdUserPicConstructor.setUserPic(cmdUserPic, buildCmdDevice(devSn)), isImme);
        }
    }

    /**
     * 下发扩展板设备表到设备
     *
     * @param accDevice
     * @param accExtDevice
     * @param isImme
     */
    public void setExtBoardPropertyToDev(AccDevice accDevice, AccExtDevice accExtDevice, boolean isImme) {
        CmdExtBoardProperty cmdExtBoardProperty = new CmdExtBoardProperty(accExtDevice.getExtBoardNo(),
            accDevice.getBusinessId(), accExtDevice.getExtBoardType(), (short)1, accExtDevice.getCommAddress(), false);
        addCmd(accDevice.getSn(),
            CmdExtBoardPropertyConstructor.setExtBoardProperty(buildCmdDevice(accDevice), cmdExtBoardProperty), isImme);
    }

    /**
     * 删除扩展表信息
     *
     * @param devSn
     * @param accExtDevice
     * @param isImme
     */
    public void delExtBoardPropertyFromDev(String devSn, AccExtDeviceItem accExtDevice, boolean isImme) {
        addCmd(devSn,
            CmdExtBoardPropertyConstructor.delExtBoardProperty(buildCmdDevice(devSn), accExtDevice.getExtBoardNo()),
            isImme);
    }

    /**
     * 清空扩展表信息
     *
     * @param devSn
     * @param isImme
     */
    public void delAllExtBoardPropertyFromDev(String devSn, boolean isImme) {
        addCmd(devSn, CmdExtBoardPropertyConstructor.delAllExtBoardProperty(buildCmdDevice(devSn)), isImme);
    }

    /**
     * 下发扩展板实体关联表
     *
     * @param accDevice
     * @param accExtDevice
     * @param isImme
     */
    public void setExtBoardRelationListToDev(AccDevice accDevice, AccExtDevice accExtDevice, boolean isImme) {
        List<CmdExtBoardRelationList> relationLists = new ArrayList<>();
        List<AccDoor> doorList = accDoorDao.findByExtDevIdOrderByDoorNoAsc(accExtDevice.getId());
        int doorCount = 1;
        for (AccDoor accDoor : doorList) {
            relationLists.add(new CmdExtBoardRelationList(accExtDevice.getExtBoardNo(),
                CmdExtBoardRelationList.TYPE_DOOR, accDoor.getDoorNo(), doorCount));
            doorCount++;
        }
        List<AccReader> readerList = accReaderDao.findByExtDevIdOrderByReaderNoAsc(accExtDevice.getId());
        int readerCount = 1;
        for (AccReader accReader : readerList) {
            relationLists.add(new CmdExtBoardRelationList(accExtDevice.getExtBoardNo(),
                CmdExtBoardRelationList.TYPE_READER, accReader.getReaderNo(), readerCount));
            readerCount++;
        }
        List<AccAuxIn> auxInList = accAuxInDao.findByExtDevIdOrderByAuxNoAsc(accExtDevice.getId());
        int auxInCount = 1;
        for (AccAuxIn accAuxIn : auxInList) {
            relationLists.add(new CmdExtBoardRelationList(accExtDevice.getExtBoardNo(),
                CmdExtBoardRelationList.TYPE_AUXIN, accAuxIn.getAuxNo(), auxInCount));
            auxInCount++;
        }
        List<AccAuxOut> auxOutList = accAuxOutDao.findByExtDevIdOrderByAuxNoAsc(accExtDevice.getId());
        int auxOutCount = 1;
        for (AccAuxOut accAuxOut : auxOutList) {
            relationLists.add(new CmdExtBoardRelationList(accExtDevice.getExtBoardNo(),
                CmdExtBoardRelationList.TYPE_AUXOUT, accAuxOut.getAuxNo(), auxOutCount));
            auxOutCount++;
        }
        addCmd(accDevice.getSn(),
            CmdExtBoardRelationListConstructor.setExtBoardRelationList(buildCmdDevice(accDevice), relationLists),
            isImme);
    }

    /**
     * 根据扩展板id删除扩展板关联表
     *
     * @param accDevice
     * @param accExtDevice
     * @param isImme
     */
    public void delExtBoardRelationListFromDev(AccDevice accDevice, AccExtDevice accExtDevice, boolean isImme) {
        addCmd(accDevice.getSn(), CmdExtBoardRelationListConstructor.delExtBoardRelationList(buildCmdDevice(accDevice),
            accExtDevice.getExtBoardNo()), isImme);
    }

    /**
     * 清空扩展表关联表信息
     *
     * @param accDevice
     * @param isImme
     */
    public void delAllExtBoardRelationListFromDev(AccDevice accDevice, boolean isImme) {
        addCmd(accDevice.getSn(),
            CmdExtBoardRelationListConstructor.delAllExtBoardRelationList(buildCmdDevice(accDevice)), isImme);
    }

    /**
     * 设置设备扩展板的协议类型
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-03-26 16:18
     * @param accDevice
     * @param devProtocolType
     * @param isImme
     * @return void
     */
    public void setRS485CommType(AccDevice accDevice, Short devProtocolType, boolean isImme) {
        addCmd(accDevice.getSn(), CmdDeviceConstructor.setRS485CommType(String.valueOf(devProtocolType)), isImme);
    }

    /**
     * 获取身份证表数据
     * 
     * @param accDevice:设备信息
     * @param isImme:是否紧急命令
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2020-09-21 15:25
     * @since 1.0.0
     */
    public Long getIdentityCardInfo(AccDevice accDevice, boolean isImme) {
        return addCmd(accDevice.getSn(), CmdIdentityCardConstructor.getIdentityCard(buildCmdDevice(accDevice.getSn())),
            isImme);
    }

    /**
     * 下发人员照片到设备
     * 
     * @param devSn:
     * @param accPersonOptBean:
     * @param isImme:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-11-18 18:26
     * @since 1.0.0
     */
    public void pushUserPicToDev(String devSn, AccPersonOptItem accPersonOptBean, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        boolean isPhotoFunOn = CmdSecConstants.SUPPORT.equals(cmdDevice.getPhotoFunOn());
        boolean isUserPicURLFunOn = CmdSecConstants.SUPPORT.equals(cmdDevice.getUserPicURLFunOn());
        List<CmdUserPic> cmdUserPicList = new ArrayList<>();
        // 组装人员照片下发
        if (isPhotoFunOn && StringUtils.isNotBlank(accPersonOptBean.getPhotoPath())) {
            String thumbPath = FileUtil.getThumbPath(accPersonOptBean.getPhotoPath());
            // 支持url方式
            if (isUserPicURLFunOn) {
                CmdUserPic cmdUserPic = new CmdUserPic();
                cmdUserPic.setPin(accPersonOptBean.getPin());
                cmdUserPic.setFormat(CmdSecConstants.FORMAT_URL);
                // 下发需要去掉开头的斜杠
                cmdUserPic.setUrl(thumbPath.replaceFirst("/", ""));
                cmdUserPicList.add(cmdUserPic);
            } else {
                String userPhotoBase64 = FileEncryptUtil.getDecryptFileBase64(thumbPath);
                if (StringUtils.isNotBlank(userPhotoBase64)) {
                    CmdUserPic cmdUserPic = new CmdUserPic();
                    cmdUserPic.setContent(userPhotoBase64);
                    cmdUserPic.setSize(String.valueOf(userPhotoBase64.length()));
                    cmdUserPic.setPin(accPersonOptBean.getPin());
                    cmdUserPic.setFormat(CmdSecConstants.FORMAT_BASE64);
                    cmdUserPicList.add(cmdUserPic);
                }
            }
        }
        // 下发用户照片
        CmdUserPicConstructor.setUserPic(cmdDevice, cmdUserPicList).forEach(cmd -> addCmd(devSn, cmd, isImme));
    }

    /**
     * 下发卡号到设备，设备支持多卡功能下发多卡表，否则下发user表
     *
     * @param devSn:设备序列号
     * @param personOptItemList:人员信息
     * @param imme:是否紧急指令
     * @return void
     * <AUTHOR>
     * @date 2022-01-26 9:15
     * @since 1.0.0
     */
    public void setPersonCardToDevice(String devSn, List<AccPersonOptItem> personOptItemList, boolean imme) {
        Set<CmdUser> cmdUserSet = new HashSet<>();
        List<CmdMulCardUser> cmdMulCardUserList = new ArrayList<>();
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        String cardhex = baseSysParamService.getValByName("pers.cardHex");// 当前系统卡号进制
        boolean isMulCardUser = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(7));// 是否支持多卡
        boolean isLongNameOn = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(5));// 是否支持长姓名
        personOptItemList.forEach(accPersonOptBean -> {
            String cardNo = formatPersonCard(accPersonOptBean, cardhex, isMulCardUser);
            // 主卡信息加入user表，防止pin重复，支持多卡的设备不需要对cardno赋值
            if (accPersonOptBean.getCardType() == null || accPersonOptBean.getCardType() == PersConstants.MAIN_CARD) {
                String name = "";// 支持长姓名下发的设备user表不需要发name字段
                if (!isLongNameOn && (StringUtils.isNotBlank(accPersonOptBean.getName())
                    || StringUtils.isNotBlank(accPersonOptBean.getLastName()))) {
                    name = (accPersonOptBean.getName() + " " + accPersonOptBean.getLastName()).trim();
                }
                CmdUser cmdUser = new CmdUser(accPersonOptBean.getPin(), name, accPersonOptBean.getPersonPwd(),
                    isMulCardUser ? "" : cardNo, accPersonOptBean.getStartTime(), accPersonOptBean.getEndTime(),
                    accPersonOptBean.getSuperAuth(), accPersonOptBean.getGroupId(), accPersonOptBean.getDisabled(),
                    accPersonOptBean.getPrivilege(), accPersonOptBean.getIdCard());
                cmdUserSet.add(cmdUser);
            }
            // 根据参数判断是否支持一人多卡表
            if (isMulCardUser && !StringUtils.isEmpty(cardNo)) {
                cmdMulCardUserList
                    .add(new CmdMulCardUser(accPersonOptBean.getPin(), cardNo, accPersonOptBean.getCardType()));
            }
        });
        // 设备支持多卡卡号下发多卡表,否则下发user表
        if (isMulCardUser) {
            addCmd(devSn, CmdMulCardUserConstructor.setMulCardUser(cmdMulCardUserList, cmdDevice), imme);
        } else {
            addCmd(devSn, CmdUserConstructor.setUser(cmdUserSet, cmdDevice), imme);
        }
    }

    /**
     * 转换人员卡号
     */
    private String formatPersonCard(AccPersonOptItem accPersonOptBean, String cardhex, boolean isMulCardUser) {
        String cardNo = StringUtils.EMPTY;
        if (accPersonOptBean.getCardState() != null && accPersonOptBean.getCardState() == PersConstants.CARD_VALID) {
            cardNo = accPersonOptBean.getCardNo();
        }
        if (StringUtils.isNotBlank(cardNo)) {
            if (isMulCardUser) {
                if ("0".equals(cardhex)) {// 表示当前支持多卡并且是10进制卡，此类固件需要下发16进制数据
                    cardNo = AccDeviceUtil.convertCardNo(cardNo, 10, 16);
                }
            } else if (cardhex.equals("1")) {// 表示当前不支持多卡并且是16进制卡，此类固件需要下发10进制数据
                cardNo = AccDeviceUtil.convertCardNo(cardNo, 16, 10);
            }
        }
        return cardNo;
    }

    /**
     * tdb09相关设备需要告知设备下发完成才开始进行人脸模版提取
     *
     * @param devSn
     * @param imme
     * @return
     */
    public void downloadOver(String devSn, boolean imme) {
        addCmd(devSn, CmdDeviceConstructor.downloadOver(buildCmdDevice(devSn)), imme);
    }

    /**
     * 批量下发user表信息到设备
     * 
     * @param device:
     * @param personList:
     * @param isImme:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-07-15 15:07
     * @since 1.0.0
     */
    public void sendCmdUserInfosToDev(AccDevice device, List<AccPersonOptItem> personList, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(device);
        boolean isSuperAuthorize = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(16));// 支持超级用户表
        boolean isMulCardUser = CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(7));// 是否支持多卡
        Set<CmdUser> cmdUserSet = new HashSet<CmdUser>();
        for (AccPersonOptItem persOptItem : personList) {
            if (!isSuperAuthorize || persOptItem.getSuperAuth() == null) {
                persOptItem.setSuperAuth(Short.parseShort("0"));
            }
            if (persOptItem.getCardType() == null || persOptItem.getCardType() == PersConstants.MAIN_CARD) {// 主卡信息加入user表，防止pin重复，支持多卡的设备不需要对cardno赋值
                CmdUser cmdUser = new CmdUser(persOptItem.getPin(), persOptItem.getName(), persOptItem.getPersonPwd(),
                    isMulCardUser ? ""
                        : (StringUtils.isNotBlank(persOptItem.getCardNo()) ? persOptItem.getCardNo() : ""),
                    persOptItem.getStartTime(), persOptItem.getEndTime(), persOptItem.getSuperAuth(),
                    persOptItem.getGroupId(), persOptItem.getDisabled(), persOptItem.getPrivilege(),
                    persOptItem.getIdCard());
                cmdUserSet.add(cmdUser);
            }
        }
        // 父设备非空为子设备
        if (device.getParentDevice() != null) {
            addCmd(device.getSn(), CmdUserConstructor.setUserToChildDev(cmdUserSet), isImme);
        } else {
            addCmd(device.getSn(), CmdUserConstructor.setUser(cmdUserSet, cmdDevice), isImme);
        }
    }

    /**
     * 设置ntp服务功能开关
     * 
     * @param devSn
     * @param isOpen
     * @param isImme
     */
    public void setNtpFunOn(String devSn, boolean isOpen, boolean isImme) {
        CmdDevice dev = buildCmdDevice(devSn);
        if (CmdSecConstants.SUPPORT.equals(dev.getAccSupportFunList(61))) {
            if (CmdSecConstants.SUPPORT.equals(dev.getAccSupportFunList(26))) {
                List<CmdDevParameters> cmdDevParametersList = new ArrayList<>();
                cmdDevParametersList.add(new CmdDevParameters(dev.getId(), "NtpFunOn", isOpen ? "1" : "0"));
                addCmd(devSn, CmdDevParametersConstructor.setDevParameters(dev, cmdDevParametersList), isImme);
            } else {
                Map<String, String> optionMap = new HashMap<>();
                optionMap.put("NtpFunOn", isOpen ? "1" : "0");
                CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(devSn, cmd, isImme));
            }
        }
    }

    /**
     * 设置ntp服务地址
     * 
     * @param devSn
     * @param addressList
     * @param isImme
     */
    public void setNtpAddress(String devSn, List<String> addressList, boolean isImme) {
        // 先清空再下发
        addCmd(devSn, CmdNTPServerListConstructor.delAllNTPServerList(buildCmdDevice(devSn)), isImme);
        if (addressList != null && !addressList.isEmpty()) {
            List<CmdNTPServerList> ntpList = new ArrayList<>();
            for (String address : addressList) {
                ntpList.add(new CmdNTPServerList(address));
            }
            addCmd(devSn, CmdNTPServerListConstructor.setNTPServerList(buildCmdDevice(devSn), ntpList), isImme);
        }
    }

    /**
     * 下发二维码相关参数
     * 
     * @param dev
     * @param encryptType
     * @param encryptKey
     * @param isImme
     */
    public void setQRCodeParam(AccDeviceItem dev, String encryptType, String encryptKey, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(dev.getSn());
        if (CmdSecConstants.SUPPORT.equals(cmdDevice.getAccSupportFunList(26))) {
            List<CmdDevParameters> cmdDevParametersList = new ArrayList<>();
            cmdDevParametersList.add(new CmdDevParameters(dev.getBusinessId(), "QRCodeDecryptKey", encryptKey));
            cmdDevParametersList.add(new CmdDevParameters(dev.getBusinessId(), "QRCodeDecryptType", encryptType));
            addCmd(dev.getSn(), CmdDevParametersConstructor.setDevParameters(cmdDevice, cmdDevParametersList), isImme);
        } else {
            Map<String, String> optionMap = new HashMap<>();
            optionMap.put("QRCodeDecryptKey", encryptKey);
            optionMap.put("QRCodeDecryptType", encryptType);
            CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(dev.getSn(), cmd, isImme));
        }
    }

    /**
     * 设备参数下发
     *
     * @param sn
     * @param optionMap
     * @param isImme
     */
    public void setOption(String sn, Map<String, String> optionMap, boolean isImme) {
        CmdDeviceConstructor.setOptions(optionMap).forEach(cmd -> addCmd(sn, cmd, isImme));
    }

    /**
     * 配置可视对讲设备sip账号
     *
     * @param devSn
     * @param item
     */
    public void setSipAccount(String devSn, Acc4VdbSipAccountItem item, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            CmdBestSipAccount cmdBestSipAccount = new CmdBestSipAccount(item.getId(), item.getServer(),
                item.getUserName(), item.getAuthName(), item.getPassword(), Integer.parseInt(item.getProtocol()),
                item.getDisplayName(), Integer.parseInt(item.getPrimary()));
            addCmd(devSn, CmdBestSipAccountConstructor.setSipAccount(cmdDevice, cmdBestSipAccount), isImme);
        } else {
            Map<String, String> optionMap = new HashMap<>();
            optionMap.put("IsSupportSIPServer", "1");
            optionMap.put("SIPServerURLModel", "1");
            optionMap.put("ServerIPAddress", item.getServer());
            optionMap.put("SIPUser", item.getUserName());
            optionMap.put("SIPAuthName", item.getAuthName());
            optionMap.put("SIPDisplayName", item.getDisplayName());
            optionMap.put("SIPKey", item.getPassword());
            optionMap.put("SIPTransferProtocol", item.getProtocol());
            setOption(devSn, optionMap, isImme);
        }
    }

    /**
     * 清空设备sip账号信息
     *
     * @param devSn
     * @param item
     * @param isImme
     */
    public void clearSipAccount(String devSn, Acc4VdbSipAccountItem item, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            List<String> devIdList = new ArrayList<>();
            devIdList.add(item.getId());
            addCmd(devSn, CmdBestSipAccountConstructor.delSipAccount(cmdDevice, devIdList), isImme);
        } else {
            Map<String, String> optionMap = new HashMap<>();
            optionMap.put("ServerIPAddress", "");
            optionMap.put("SIPUser", "");
            optionMap.put("SIPAuthName", "");
            optionMap.put("SIPDisplayName", "");
            optionMap.put("SIPKey", "");
            optionMap.put("SIPTransferProtocol", "");
            setOption(devSn, optionMap, isImme);
        }
    }

    /**
     * 下发sip通讯录
     *
     * @param devSn
     * @param accContact4VdbItems
     */
    public void setSipContact(String devSn, List<AccContact4VdbItem> accContact4VdbItems, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            List<CmdBestSipContact> contacts = new ArrayList<>();
            if (accContact4VdbItems != null) {
                accContact4VdbItems.forEach(accContact4VdbItem -> {
                    CmdBestSipContact contact =
                        new CmdBestSipContact(accContact4VdbItem.getId(), accContact4VdbItem.getDisplayName(),
                            accContact4VdbItem.getGroup(), accContact4VdbItem.getPhoto());
                    List<CmdBestSipNumber> numbers = new ArrayList<>();
                    List<AccContactNumber4VdbItem> vdbNumberItemList = accContact4VdbItem.getVdbNumberItemList();
                    if (vdbNumberItemList != null) {
                        vdbNumberItemList.forEach(vdbNumberItem -> {
                            numbers.add(new CmdBestSipNumber((int)vdbNumberItem.getNumType(), vdbNumberItem.getNumber(),
                                Integer.parseInt(vdbNumberItem.getPriority())));
                        });
                    }
                    contact.setNumbers(numbers);
                    contacts.add(contact);
                });
            }
            addCmd(devSn, CmdBestSipContactConstructor.setSipContact(cmdDevice, contacts), isImme);
        } else {
            List<CmdSipContact> contacts = new ArrayList<>();
            if (accContact4VdbItems != null) {
                accContact4VdbItems.forEach(accContact4VdbItem -> {
                    CmdSipContact contact = new CmdSipContact(accContact4VdbItem.getId(),
                        accContact4VdbItem.getDisplayName(), accContact4VdbItem.getGroup(),
                        accContact4VdbItem.getDirectCall(), accContact4VdbItem.getPhoto(),
                        accContact4VdbItem.getPassCode(), accContact4VdbItem.getPin());
                    List<CmdSipContactNumber> numbers = new ArrayList<>();
                    List<AccContactNumber4VdbItem> vdbNumberItemList = accContact4VdbItem.getVdbNumberItemList();
                    if (vdbNumberItemList != null) {
                        vdbNumberItemList.forEach(vdbNumberItem -> {
                            numbers.add(new CmdSipContactNumber(accContact4VdbItem.getId(),
                                (int)vdbNumberItem.getNumType(), vdbNumberItem.getNumber(),
                                Integer.parseInt(vdbNumberItem.getPriority()), vdbNumberItem.getPatternID()));
                        });
                    }
                    contact.setNumbers(numbers);
                    contacts.add(contact);
                });
            }
            List<String> cmdList = CmdSipContactConstructor.setSipContact(contacts);
            if (!Objects.isNull(cmdList)) {
                cmdList.forEach(cmd -> addCmd(devSn, cmd, isImme));
            }
        }
    }

    /**
     * 根据id删除通讯录
     *
     * @param devSn
     * @param contactIds
     * @param isImme
     */
    public void delSipContact(String devSn, List<String> contactIds, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestSipContactConstructor.delSipContact(cmdDevice, contactIds), isImme);
        } else {
            addCmd(devSn, CmdSipContactConstructor.delSipContact(contactIds), isImme);
        }
    }

    /**
     * 下发广告
     *
     * @param devSn
     * @param item
     * @param isImme
     */
    public void setAd(String devSn, Acc4UploadAdFileItem item, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        BaseMediaFileItem fileItem = baseMediaFileService.getItemById(item.getFileId());
        if (fileItem != null) {
            if (accDeviceService.isBestDevBySn(devSn)) {
                CmdBestAd ad = new CmdBestAd();
                ad.setAdId(String.valueOf(item.getNumber()));
                List<CmdBestResource> resources = new ArrayList<>();
                resources.add(new CmdBestResource(fileItem.getPath(), item.getDisplayTime(),
                    FileUtils.getFileMd5Value(FileUtils.getLocalFullPath(fileItem.getPath())),
                    item.getPlayOrder() != null ? item.getPlayOrder() : 1));
                ad.setResources(resources);
                addCmd(devSn, CmdBestAdConstructor.setAd(cmdDevice, Collections.singletonList(ad)), isImme);
            }
            // push暂时没有此功能
        }
    }

    /**
     * 下发可视对讲参数
     *
     * @param devSn
     * @param item
     * @param isImme
     */
    public void setVdbOption(String devSn, Acc4VdbOptionItem item, boolean isImme) {
        // CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            // best协议暂无
        } else {
            Map<String, String> optionMap = new HashMap<>();
            optionMap.put("SIPDTMFKey", StringUtils.isNotBlank(item.getDeviceDtmf()) ? item.getDeviceDtmf() : "");
            if (item.getSipEnableUnit() != null) {
                optionMap.put("SipEnableUnit", item.getSipEnableUnit());
            }
            setOption(devSn, optionMap, isImme);
        }
    }

    /**
     * 清空触发列表
     *
     * @param devSn
     * @param isImme
     */
    public void delAllTriggerList(String devSn, boolean isImme) {
        addCmd(devSn, CmdTriggerListConstructor.delAllTriggerList(buildCmdDevice(devSn)), isImme);
    }

    /**
     * 下发读头绑定摄像头信息
     * 
     * <AUTHOR>
     * @date 2024-05-10 14:17
     * @since 1.0.0
     */
    public void setReaderCamBindInfo(String devSn, AccReaderItem accReader, List<String> channelNoList,
        boolean isImme) {
        List<CmdCamBindInfo> cmdCamBindInfoList = new ArrayList<>();
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (!channelNoList.isEmpty()) {
            channelNoList
                .forEach(channelNo -> cmdCamBindInfoList.add(new CmdCamBindInfo(AccConstants.CAMERA_TARGET_TYPE_READER,
                    accReader.getReaderNo(), Short.parseShort(channelNo), cmdDevice.getId())));
        }
        addCmd(devSn, CmdCamBindInfoConstructor.setCamBindInfo(cmdDevice, cmdCamBindInfoList), isImme);
    }

    /**
     * 下发辅助输入绑定摄像头信息
     * 
     * <AUTHOR>
     * @date 2024-05-10 14:17
     * @since 1.0.0
     */
    public void setAuxInCamBindInfo(String devSn, AccAuxInItem auxIn, List<String> channelNoList, boolean isImme) {
        List<CmdCamBindInfo> cmdCamBindInfoList = new ArrayList<>();
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (!channelNoList.isEmpty()) {
            channelNoList
                .forEach(channelNo -> cmdCamBindInfoList.add(new CmdCamBindInfo(AccConstants.CAMERA_TARGET_TYPE_AUXIN,
                    auxIn.getAuxNo(), Short.parseShort(channelNo), cmdDevice.getId())));
        }
        addCmd(devSn, CmdCamBindInfoConstructor.setCamBindInfo(cmdDevice, cmdCamBindInfoList), isImme);
    }

    /**
     * 删除读头绑定的摄像头信息
     * 
     * <AUTHOR>
     * @date 2024-05-10 11:16
     * @since 1.0.0
     */
    public void delReaderCamBindInfo(String devSn, Short readerNo, boolean isImme) {
        CmdCamBindInfo cmdCamBindInfo = new CmdCamBindInfo(AccConstants.CAMERA_TARGET_TYPE_READER, readerNo);
        addCmd(devSn, CmdCamBindInfoConstructor.delAllCamBindInfoByTargetId(buildCmdDevice(devSn), cmdCamBindInfo),
            isImme);
    }

    /**
     * 删除辅助输入绑定的摄像头信息
     * 
     * <AUTHOR>
     * @date 2024-05-10 11:24
     * @since 1.0.0
     */
    public void delAuxInCamBindInfo(String devSn, Short AuxInNo, boolean isImme) {
        CmdCamBindInfo cmdCamBindInfo = new CmdCamBindInfo(AccConstants.CAMERA_TARGET_TYPE_AUXIN, AuxInNo);
        addCmd(devSn, CmdCamBindInfoConstructor.delAllCamBindInfoByTargetId(buildCmdDevice(devSn), cmdCamBindInfo),
            isImme);
    }

    /**
     * 新增SIP拨号快捷键
     * 
     * @param devSn
     * @param accShortKey4VdbItems
     * @param isImme
     */
    public void setVdbDevShortKey(String devSn, List<AccShortKey4VdbItem> accShortKey4VdbItems, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        List<CmdShortKey> shortKeys = new ArrayList<>();
        if (accShortKey4VdbItems != null) {
            accShortKey4VdbItems.forEach(item -> {
                CmdShortKey shortKey = new CmdShortKey(item.getId(), item.getName(), item.getCallNumber(),
                    item.getKeyEnable(), item.getType());
                shortKeys.add(shortKey);
            });
            if (accDeviceService.isBestDevBySn(devSn)) {
                addCmd(devSn, CmdBestSipShortKeyConstructor.setSipShortKey(cmdDevice, shortKeys), isImme);
            } else {
                List<String> cmdList = CmdSipShortKeyConstructor.setSipShortKey(shortKeys);
                if (!Objects.isNull(cmdList)) {
                    cmdList.forEach(cmd -> addCmd(devSn, cmd, isImme));
                }
            }
        }
    }

    /**
     * 删除sip拨号快捷键
     * 
     * @param devSn
     * @param shortKeyIdList
     * @param isImme
     */
    public void delVdbDevShortKey(String devSn, List<String> shortKeyIdList, boolean isImme) {
        CmdDevice cmdDevice = buildCmdDevice(devSn);
        if (accDeviceService.isBestDevBySn(devSn)) {
            addCmd(devSn, CmdBestSipShortKeyConstructor.delSipShortKey(cmdDevice, shortKeyIdList), isImme);
        } else {
            addCmd(devSn, CmdSipShortKeyConstructor.delSipShortKey(shortKeyIdList), isImme);
        }
    }

    /**
     * 设置人脸后台比对服务参数
     *
     * @param sn
     * @param item
     * @param isImme
     */
    public void setFaceVerifyServer(String sn, Acc4SetFaceVerifyServer item, boolean isImme) {
        Map<String, String> optionMap = new HashMap<>();
        optionMap.put("FaceVerifyMode", item.getFaceVerifyMode());
        // 非本地比对时，才需要发其他参数
        if (!"0".equals(item.getFaceVerifyMode())) {
            optionMap.put("FaceBgServerType", item.getFaceBgServerType());
            optionMap.put("IsAccessLogicFunOn", item.getIsAccessLogic());
            optionMap.put("FaceThreshold", item.getFaceThreshold());
            if (!"0".equals(item.getFaceBgServerType())) {
                optionMap.put("FaceBgServerURL", item.getFaceBgServerURL());
                optionMap.put("FaceBgServerSecret", item.getFaceBgServerSecret());
            }
        }
        setOption(sn, optionMap, isImme);
    }
}
