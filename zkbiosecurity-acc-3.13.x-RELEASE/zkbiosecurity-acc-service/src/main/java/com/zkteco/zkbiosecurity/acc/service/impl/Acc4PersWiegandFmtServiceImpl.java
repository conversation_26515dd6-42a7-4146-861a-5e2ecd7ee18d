package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.acc.service.Acc4PersWiegandFmtService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.utils.AccDataUtil;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;

/**
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 17:58 2018/5/22
 */
@Service
@Transactional
public class Acc4PersWiegandFmtServiceImpl implements Acc4PersWiegandFmtService {

    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private PersWiegandFmtService persWiegandFmtService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccCacheManager accCacheManager;

    @Override
    public Boolean checkUseWiegandFmt(String wiegandFmtIds) {
        Boolean checkFlag = false;
        List<AccDoor> accDoorList =
            accDoorDao.findByWgInputFmtIdIn((List<String>)CollectionUtil.strToList(wiegandFmtIds));
        if (accDoorList == null || accDoorList.size() == 0) {
            checkFlag = true;
        }
        return checkFlag;
    }

    @Override
    public Boolean delWiegandFmt(String wiegandFmtIds) {
        PersWiegandFmtItem condition = new PersWiegandFmtItem();
        condition.setIds(wiegandFmtIds);
        List<PersWiegandFmtItem> persWiegandFmtList = persWiegandFmtService.getByCondition(condition);// 因为是删除后调用的，所以使用sql才能查询到删除前的数据
        if (persWiegandFmtList.size() > 0) {
            List<PersWiegandFmtItem> persWiegandFmts = new ArrayList<>();// 自动匹配的韦根格式
            Set<Short> delCardBit = new HashSet<>();
            persWiegandFmtList.stream().forEach(persWiegandFmtItem -> {
                if (persWiegandFmtItem.getIsDefaultFmt()) {
                    persWiegandFmts.add(persWiegandFmtItem);
                }
                List<PersWiegandFmtItem> wfList =
                    persWiegandFmtService.getItemsByWiegandCount(persWiegandFmtItem.getWiegandCount());
                boolean existAutoMatch = false;
                for (PersWiegandFmtItem item : wfList) {
                    if (item.getIsDefaultFmt()) {
                        existAutoMatch = true;
                        break;
                    }
                }
                if (!existAutoMatch) {
                    delCardBit.add(persWiegandFmtItem.getWiegandCount());
                }
            });
            List<PersWiegandFmtItem> defWGList = persWiegandFmtService.getAutoMatch();// 一体机需要全部删除全部下发,所以将所有自动匹配的韦根格式查出
            List<AccDevice> devList = accDeviceDao.findAll();
            devList.stream().forEach(accDevice -> {
                if (accDevice.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL) {// 一体机需要全部删除全部下发
                    accDevCmdManager.delAllCardFormat(accDevice, false);
                    accDevCmdManager.setCardFormatToDev(accDevice, defWGList, false);
                } else {
                    if (accDeviceOptionService.isSupportFunList(accDevice.getSn(), 6)) {
                        accDevCmdManager.delDefWGFormatFromDev(accDevice.getSn(), persWiegandFmts, false);
                        accDevCmdManager.delCusWGFormatFromDev(accDevice.getSn(), persWiegandFmtList, false);
                    } else if (delCardBit.size() > 0) {
                        accDevCmdManager.delDefWGByCardBit(accDevice, delCardBit, false);
                    }
                }
            });
        }
        return true;
    }

    @Override
    public Boolean editWiegandFmt(String wiegandFmtId) {
        PersWiegandFmtItem persWiegandFmtItem = persWiegandFmtService.getItemById(wiegandFmtId);
        if (persWiegandFmtItem != null) {
            List<PersWiegandFmtItem> persWiegandFmtItemList = new ArrayList<>();
            persWiegandFmtItemList.add(persWiegandFmtItem);
            List<AccDevice> accDeviceList = accDeviceDao.findAll();
            if (persWiegandFmtItem.getIsDefaultFmt()) { // 如果编辑的是内置韦根格式，传过来需要下发 DefaultWG_count 的韦根id
                accDeviceList.stream().forEach(accDevice -> {
                    accDevCmdManager.setDefWGFormatToDev(accDevice.getSn(), persWiegandFmtItemList, false);
                });
            } else {
                boolean existAutoMatch = false;
                Set<Short> delCardBit = new HashSet<>();
                List<PersWiegandFmtItem> wfList =
                    persWiegandFmtService.getItemsByWiegandCount(persWiegandFmtItem.getWiegandCount());
                for (PersWiegandFmtItem item : wfList) {
                    if (item.getIsDefaultFmt()) {
                        existAutoMatch = true;
                        break;
                    }
                }
                for (AccDevice accDevice : accDeviceList) {
                    if (accDeviceOptionService.isSupportFunList(accDevice.getSn(), 6)) {
                        accDevCmdManager.delDefWGFormatFromDev(accDevice.getSn(), persWiegandFmtItemList, false);
                    } else if (!existAutoMatch) {// 这个位数的韦根格式，不需要默认匹配，老固件用
                        delCardBit.add(persWiegandFmtItem.getWiegandCount());
                        accDevCmdManager.delDefWGByCardBit(accDevice, delCardBit, false);
                    }
                }
            }
            List<PersWiegandFmtItem> defWGList = persWiegandFmtService.getAutoMatch();// 一体机需要全部删除全部下发,所以将所有自动匹配的韦根格式查出
            accDeviceList.stream().forEach(accDevice -> {
                if (accDevice.getMachineType() == AccConstants.DEVICE_ACCESS_CONTROL) {// 一体机需要全部删除全部下发
                    accDevCmdManager.delAllCardFormat(accDevice, false);
                    accDevCmdManager.setCardFormatToDev(accDevice, defWGList, false);
                } else {
                    accDevCmdManager.setCusWGFormatToDev(accDevice.getSn(), persWiegandFmtItemList, false);
                }
            });
        }
        return true;
    }

    @Override
    public String start(String deviceId) {
        String result = accDeviceService.openWgFmtTestForDevice(deviceId, true);
        return result;
    }

    @Override
    public String readerCard(String deviceId) {
        AccDevice dev = accDeviceDao.findById(deviceId).orElse(null);
        if (Objects.nonNull(dev)) {
            String info = accCacheManager.getCacheValueByKey("adms:WGTestData:" + dev.getSn());
            if (info != null) {
                accCacheManager.delete("adms:WGTestData:" + dev.getSn());
                return info;
            }
        }
        return null;
    }

    @Override
    public String recommendFmt(String deviceId, String sizCode, String cardNo, String orgCardNo, String bitcount,
        String withSizeCode) {
        int bitscount = Integer.parseInt(bitcount);
        String[] orgCardNos = orgCardNo.split(",");
        String[] cardNos = cardNo.split(",");
        String[] sizeCodes = sizCode.split(",");

        Map<String, String> result = new HashMap<String, String>();
        try {
            String cardFmt = AccDataUtil.recommendCardFmt(bitscount, orgCardNos, cardNos, sizeCodes);
            result.put("cardFmt", cardFmt);
            result.put("parityFmt", AccDataUtil.recommendParityFmt(orgCardNos, bitscount));

            // 推荐区位码
            if ("true".equals(withSizeCode) && sizeCodes.length == 0) {
                int sizeCode = AccDataUtil.reverseSize(orgCardNos, cardFmt);
                sizeCodes = new String[orgCardNos.length];
                for (int i = 0; i < orgCardNos.length; i++) {
                    if (!StringUtils.isEmpty(orgCardNos[i])) {
                        sizeCodes[i] = String.valueOf(sizeCode);
                    }
                }
                cardFmt = AccDataUtil.recommendCardFmt(bitscount, orgCardNos, cardNos, sizeCodes);
                result.put("cardFmt", cardFmt);
                result.put("sizeCode", String.valueOf(sizeCode));
            }
            return result.toString();
        } catch (Exception e) {
            throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN, e.getMessage());
        }
    }

    @Override
    public String getAllFilterId() {
        String idStr = accDeviceService.getAllWgFmtTestFilterId();
        return idStr;
    }

    @Override
    public String stop(String deviceId) {
        return accDeviceService.openWgFmtTestForDevice(deviceId, false);
    }
}
