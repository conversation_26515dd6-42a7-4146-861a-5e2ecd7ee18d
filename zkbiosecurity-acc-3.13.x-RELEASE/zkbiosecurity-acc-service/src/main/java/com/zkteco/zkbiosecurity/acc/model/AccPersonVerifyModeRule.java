package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "ACC_PERSON_VERIFYMODERULE")
@Getter
@Setter
@Accessors(chain=true)
public class AccPersonVerifyModeRule extends BaseModel implements Serializable{

    @Column(name="PERS_PERSON_ID")
    private String persPersonId;

    @ManyToOne
    @JoinColumn(name = "ACC_VERIFYMODERULE_ID", nullable = false)
    private AccVerifyModeRule accVerifyModeRule;

}
