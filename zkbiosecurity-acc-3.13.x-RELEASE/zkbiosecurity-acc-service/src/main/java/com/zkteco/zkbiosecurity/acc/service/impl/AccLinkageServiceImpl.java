/**
 * File Name: AccLinkageServiceImpl Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.operate.AccLinkageOperate;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.ias.service.Ias4OtherService;
import com.zkteco.zkbiosecurity.ias.vo.Ias4OtherArmTypeItem;
import com.zkteco.zkbiosecurity.ias.vo.Ias4OtherPartitionItem;
import com.zkteco.zkbiosecurity.line.service.Line4OtherService;
import com.zkteco.zkbiosecurity.line.vo.OthersGetLineContactSelectItem;
import com.zkteco.zkbiosecurity.sms.modem.service.SmsModem4OtherService;
import com.zkteco.zkbiosecurity.system.service.BaseMailService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.vdb.service.Vdb4OtherService;
import com.zkteco.zkbiosecurity.vdb.vo.OthersGetVdbExtensionItem;
import com.zkteco.zkbiosecurity.vdb.vo.VdbIvr4OtherItem;
import com.zkteco.zkbiosecurity.whatsapp.service.Whatsapp4OtherService;

/**
 * 对应百傲瑞达 AccLinkageServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
@Service
@Transactional
public class AccLinkageServiceImpl implements AccLinkageService {
    @Autowired
    private AccLinkageDao accLinkageDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceEventDao accDeviceEventDao;
    @Autowired
    private AccDeviceEventService accDeviceEventService;
    @Autowired
    private BaseMailService baseMailService;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccAuxOutDao accAuxOutDao;
    @Autowired
    private AccAuxInDao accAuxInDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccReaderDao accReaderDao;
    @Autowired
    private AccLinkageTriggerDao accLinkageTriggerDao;
    @Autowired
    private AccLinkageInOutDao accLinkageInOutDao;
    @Autowired
    private AccLinkageIndexDao accLinkageIndexDao;
    @Autowired
    private AccLinkageMediaDao accLinkageMediaDao;
    @Autowired
    private AccLinkageVidDao accLinkageVidDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccLinkageOperate accLinkageOperate;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccLinkageIasDao accLinkageIasDao;
    @Autowired(required = false)
    private SmsModem4OtherService smsModem4OtherService;
    @Autowired(required = false)
    private AccGetIVideoService accGetIVideoService;
    @Autowired(required = false)
    private Line4OtherService line4OtherService;
    @Autowired(required = false)
    private AccGetLineContactService accGetLineContactService;
    @Autowired(required = false)
    private Whatsapp4OtherService whatsapp4OtherService;
    @Autowired(required = false)
    private Ias4OtherService ias4OtherService;
    @Autowired(required = false)
    private Vdb4OtherService vdb4OtherService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public AccLinkageItem saveItem(AccLinkageBeanItem accLinkageBeanItem, AccLinkageItem item) {
        AccLinkage accLinkage = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accLinkageDao.findById(id)).orElse(new AccLinkage());

        ModelUtil.copyPropertiesIgnoreNull(item, accLinkage);
        AccDevice accDevice = accDeviceDao.findById(item.getDeviceId()).orElse(new AccDevice());
        accLinkage.setAccDevice(accDevice);
        addLinkage(accLinkageBeanItem, accLinkage);// 编辑联动
        if (item.getId() != null) // 编辑时,先删除软联动设置，再统一插入软联动设置
        {
            delExtendLinkage(accLinkage.getId());
        }
        addLinkageMedia(accLinkageBeanItem, accLinkage);// 添加联动邮件设置
        addLinkageVid(accLinkageBeanItem, accLinkage);// 添加联动视频设置
        // 添加入侵联动设置
        addLinkageIas(accLinkageBeanItem, accLinkage);
        item.setId(accLinkage.getId());
        return item;
    }

    @Override
    public List<AccLinkageItem> getByCondition(AccLinkageItem condition) {
        return (List<AccLinkageItem>)accLinkageDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accLinkageDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {

        /*String[] idArray = ids;// 获取传送过来的id数组
        List<Integer> idList = new ArrayList<Integer>();
        for (String id : idArray)
        {
        	idList.add(Integer.parseInt(id));
        }
        //查询需要删除的联动对象
        List<AccLinkage> accLinkageList = findIn("id", idList);
        for (AccLinkage link : accLinkageList)
        {
        	accDevLogicBiz.delLinkageFromDev(link);//删除联动设置
        }
        deleteByIds(idArray);*/

        if (StringUtils.isNotEmpty(ids)) {
            List<String> idList = (List<String>)CollectionUtil.strToList(ids);
            idList.stream().forEach(id -> {
                AccLinkage accLinkage = accLinkageDao.findById(id).orElse(new AccLinkage());
                Set<AccLinkageTrigger> linkageTriggerSet = accLinkage.getAccLinkageTriggerSet();
                List<Integer> indexList = new ArrayList<>();
                for (AccLinkageTrigger linkageTrigger : linkageTriggerSet) {
                    indexList.add(linkageTrigger.getLinkageIndex());// 下发给固件的联动id
                }
                // 删除缓存信息
                accCacheManager.delAccQueryLinkageInfo(accLinkage.getAccDevice().getSn(), id);
                accDevCmdManager.delLinkageFromDev(accLinkage.getAccDevice().getSn(), indexList, false);// 删除联动设置
                accLinkageDao.deleteById(id);
            });
        }
        return false;
    }

    @Override
    public AccLinkageItem getItemById(String id) {
        List<AccLinkageItem> items = getByCondition(new AccLinkageItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public String getAllFilterId() {
        StringBuilder idStr = new StringBuilder();
        // 屏蔽ir9000
        List<AccDevice> devList = accDeviceDao.findByMachineTypeIn(Lists.newArrayList(AccConstants.DEVICE_BIOIR_9000));
        for (AccDevice accDevice : devList) {
            idStr.append(accDevice.getId()).append(",");
        }
        // 屏蔽当作其他机器读头的一体机
        List<String> devIds = accDeviceDao.getDevIdsAsWGReader();
        for (String devId : devIds) {
            idStr.append(devId).append(",");
        }
        return StringUtils.isNotBlank(idStr) ? idStr.substring(0, idStr.length() - 1) : "";
    }

    @Override
    public AccLinkageItem getLinkageParams(String id) {
        AccLinkageItem accLinkageItem = getItemById(id);
        AccLinkage accLinkage = accLinkageDao.findById(id).orElse(new AccLinkage());
        // 加载触发条件
        String triggerCond = "";
        String triggerCondName = "";
        String inputType = "";
        List<Integer> triggerNoList = new ArrayList<Integer>();
        for (AccLinkageTrigger linkageTrigger : accLinkage.getAccLinkageTriggerSet()) {
            triggerNoList.add((int)linkageTrigger.getTriggerCond());
        }
        List<AccDeviceEvent> eventList =
            accDeviceEventDao.findByAccDevice_IdOrderByEventNoAsc(accLinkage.getAccDevice().getId());
        for (AccDeviceEvent event : eventList) {
            if (triggerNoList.contains((int)event.getEventNo())) {
                triggerCond += event.getEventNo() + ",";
                triggerCondName += I18nUtil.i18nCode(event.getName()) + ",";
            }
        }
        if (accDeviceEventService.isAuxInEvent(triggerNoList))// 根据触发条件判断输入点的类型
        {
            inputType = AccAuxIn.class.getSimpleName();
        } else {
            inputType = AccDoor.class.getSimpleName();
        }

        accLinkageItem.setTriggerCond(triggerCond.substring(0, triggerCond.length() - 1));
        accLinkageItem.setTriggerCondName(triggerCondName.substring(0, triggerCondName.length() - 1));
        accLinkageItem.setInputType(inputType.equals("") ? inputType
            : inputType.replaceFirst(inputType.substring(0, 1), inputType.substring(0, 1).toLowerCase()));

        if (accLinkage.getAccLinkageInOutSet() != null && accLinkage.getAccLinkageInOutSet().size() > 0) {
            Short doorActionType = null;
            Short doorActionTime = null;
            Short auxoutActionType = null;
            Short auxoutActionTime = null;
            for (AccLinkageInOut inOut : accLinkage.getAccLinkageInOutSet()) {
                if (AccDoor.class.getSimpleName().equals(inOut.getOutputType())) {
                    doorActionType = inOut.getActionType();
                    doorActionTime = inOut.getActionTime();
                } else if (AccAuxOut.class.getSimpleName().equals(inOut.getOutputType())) {
                    auxoutActionType = inOut.getActionType();
                    auxoutActionTime = inOut.getActionTime();
                }
            }
            accLinkageItem.setDoorActionType(doorActionType);
            accLinkageItem.setDoorActionTime(doorActionTime);
            accLinkageItem.setAuxoutActionType(auxoutActionType);
            accLinkageItem.setAuxoutActionTime(auxoutActionTime);
        }

        String mailAddrs = "";
        String mobileNo = "";
        String contactIds = "";
        String whatsappMobileNo = "";
        String vdbExtensionIds = "";
        String vdbIvrId = "";
        for (AccLinkageMedia media : accLinkage.getAccLinkageMediaSet()) {
            if (media.getMediaType() == AccConstants.MAIL) {
                // 邮件发送由一封改为多封——linzj20140707
                mailAddrs += "," + media.getMediaContent();

            } else if (media.getMediaType() == AccConstants.SMS) {
                mobileNo += "," + media.getMediaContent();
            } else if (media.getMediaType() == AccConstants.LINE) {
                contactIds += "," + media.getMediaContent();
            } else if (media.getMediaType() == AccConstants.WHATSAPP) {
                whatsappMobileNo += "," + media.getMediaContent();
            } else if (media.getMediaType() == AccConstants.VDB) {
                vdbExtensionIds += "," + media.getMediaContent();
            } else if (media.getMediaType() == AccConstants.VDB_IVR) {
                vdbIvrId = media.getMediaContent();
            }
        }
        // 邮件发送由一封改为多封——linzj20140707
        if (!"".equals(mailAddrs)) {
            accLinkageItem.setMailAddr(mailAddrs.replaceFirst(",", ""));
        }
        if (!"".equals(mobileNo)) {
            accLinkageItem.setMobileNo(mobileNo.replaceFirst(",", ""));
        }
        if (StringUtils.isNotBlank(contactIds)) {
            accLinkageItem.setLineContactId(contactIds.replaceFirst(",", ""));
        }
        if (StringUtils.isNotBlank(whatsappMobileNo)) {
            accLinkageItem.setWhatsappMobileNo(whatsappMobileNo.replaceFirst(",", ""));
        }
        if (StringUtils.isNotBlank(vdbExtensionIds)) {
            accLinkageItem.setVdbExtensionId(vdbExtensionIds.replaceFirst(",", ""));
        }
        if (StringUtils.isNotBlank(vdbIvrId)) {
            accLinkageItem.setVdbIvrId(vdbIvrId);
        }
        for (AccLinkageVid vid : accLinkage.getAccLinkageVidSet()) {
            if (vid.getActionType() == AccConstants.POP_UP_VIDEO)// 弹出视频
            {
                accLinkageItem.setPopUpVideo(Short.toString(AccConstants.POP_UP_VIDEO));
                accLinkageItem.setPopUpTime(vid.getActionTime());
            } else if (vid.getActionType() == AccConstants.RECORD)// 录像
            {
                accLinkageItem.setRecord(Short.toString(AccConstants.RECORD));
                accLinkageItem.setRecordBeforeTime(vid.getRecordBeforeTime());
                accLinkageItem.setRecordTime(vid.getActionTime());
            } else
            // 拍照
            {
                accLinkageItem.setCapture(Short.toString(AccConstants.CAPTURE));
                accLinkageItem.setCaptureTime(vid.getActionTime());
            }
        }

        // 设置已添加的digifort事件
        List<String> linkageDigifortList =
            accLinkageMediaDao.getMediaContentByAccLinkageIdAndMediaType(accLinkage.getId(), AccConstants.DIGIFORT);
        if (!linkageDigifortList.isEmpty()) {
            accLinkageItem.setDigifortEventName(StringUtils.join(linkageDigifortList, ","));
        }

        List<AccLinkageIas> accLinkageIasList = accLinkage.getAccLinkageIasSet();
        if (!accLinkageIasList.isEmpty()) {
            String partitionIds = CollectionUtil.getPropertys(accLinkageIasList, AccLinkageIas::getPartitionId);
            AccLinkageIas linkageIas = accLinkageIasList.get(0);
            accLinkageItem.setIasPartitionIds(partitionIds);
            accLinkageItem.setPartitionActionType(linkageIas.getActionType());
            accLinkageItem.setIasManufacture(linkageIas.getManufacture());
            accLinkageItem.setPartitionArmType(linkageIas.getArmType());
        }

        return accLinkageItem;
    }

    @Override
    public ZKResultMsg checkTriggerOpt(String devId, String[] triggerCondArray, String[] inAddrArray,
        String linkageId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        JSONArray jsonArray = new JSONArray();
        List<String> triggerInAddrList = new ArrayList<String>();// 格式 [triggerCond_inputId_inputValue]
        for (String eventNo : triggerCondArray) {
            if (eventNo.indexOf("_parent") < 0) {
                String tempEventNo = eventNo.indexOf("_") >= 0 ? eventNo.split("_")[0] : eventNo;
                for (String inAddr : inAddrArray) {
                    triggerInAddrList.add(tempEventNo + "_" + inAddr);
                }
            }
        }
        // 查询出设备已设置的联动条件、输入点类型、输入点值
        List<Object[]> devLinkageTrigger = null;
        if (StringUtils.isNotBlank(linkageId)) {
            devLinkageTrigger = accLinkageDao.getLinkageInfoByDevId(devId, linkageId);
        } else {
            devLinkageTrigger = accLinkageDao.getLinkageInfoByDevId(devId);
        }
        Map<String, String> linkageOptMap = new HashMap<String, String>();
        for (Object[] opt : devLinkageTrigger) {
            linkageOptMap.put(opt[0] + "_" + opt[1] + "_" + opt[2], opt[3] + "");
        }
        List<String> tempTriggerCond = new ArrayList<String>();
        for (String triggerInAddr : triggerInAddrList) {
            for (String key : linkageOptMap.keySet()) {
                if (triggerInAddr.split("_")[0].equals(key.split("_")[0]))// 事件编号相同
                {
                    if ((triggerInAddr.indexOf("any") > 0 || key.indexOf("any") > 0 || key.equals(triggerInAddr))
                        && !tempTriggerCond.contains(linkageOptMap.get(key)))// 判断联动条件是否出现过，避免显示重复
                    {
                        tempTriggerCond.add(linkageOptMap.get(key));
                        jsonArray.add(I18nUtil.i18nCode(linkageOptMap.get(key)));
                    }
                }
            }
        }
        zkResultMsg.setData(jsonArray);
        return zkResultMsg;
    }

    @Override
    public boolean completeMailInfo() {
        return baseMailService.completeMailInfo();
    }

    @Override
    public AccLinkageItem getItemByName(String name) {
        AccLinkage accLinkage = accLinkageDao.findByName(name);
        if (accLinkage == null) {
            return null;
        } else {
            AccLinkageItem item = new AccLinkageItem();
            item.setId(accLinkage.getId());
            item.setName(accLinkage.getName());
            return item;
        }
    }

    @Override
    public Map<String, Object> getLinkTriggerOpt(String deviceId, String accLinkageId) {
        Map<String, Object> triggerOptMap = new HashMap<String, Object>();
        // List<AccDeviceEvent> eventList = accDeviceEventDao.findByAccDevice_IdOrderByNameAsc(deviceId);
        AccDeviceEventItem accDeviceEventItem = new AccDeviceEventItem();
        accDeviceEventItem.setDevId(deviceId);
        List<AccDeviceEventItem> eventList = accDeviceEventService.getByCondition(accDeviceEventItem);
        Map<Integer, String> checkEventMap = new HashMap<Integer, String>();// 需要勾上的事件编号
        Map<Integer, String> checkParentElementMap = new HashMap<Integer, String>();// 判断父节点是否需要勾选
        int eventNo = -1;
        // 编辑
        if (StringUtils.isNotBlank(accLinkageId)) {
            AccLinkage tempAccLinkage = accLinkageDao.findById(accLinkageId).orElse(new AccLinkage());
            Set<AccLinkageTrigger> linkageTrigerSet = tempAccLinkage.getAccLinkageTriggerSet();
            for (AccLinkageTrigger linkageTriger : linkageTrigerSet) {
                eventNo = (int)linkageTriger.getTriggerCond();
                checkEventMap.put(eventNo, "");
                if (accDeviceEventService.isDeviceEventByNo(eventNo))// 设备事件 by juvenile.li add 20170906
                {
                    checkParentElementMap.put(AccConstants.LINKAGE_DEVICE, "");
                } else if (accDeviceEventService.isAuxinEventByNo(eventNo)) {
                    checkParentElementMap.put(AccConstants.LINKAGE_AUXIN, "");
                } else {
                    checkParentElementMap.put(AccConstants.LINKAGE_DOOR, "");
                }
            }
        }
        triggerOptMap.put("eventList", eventList);
        triggerOptMap.put("checkEventMap", checkEventMap);
        triggerOptMap.put("checkParentElementMap", checkParentElementMap);
        triggerOptMap.put("eventNo", eventNo);
        return triggerOptMap;
    }

    @Override
    public Map<String, Object> getInOutInfo(String devId, String[] triggerOpt, String accLinkageId) {
        Map<String, Object> map = new HashMap<>();
        int tempTriggerOpt = -1;// 用于判断事件的类型：辅助输入事件、门事件，前端已经分级处理
        boolean isContainDoorEvent = false;
        for (String eventNo : triggerOpt) {
            tempTriggerOpt = Integer.parseInt(eventNo.indexOf("_") >= 0 ? eventNo.split("_")[0] : eventNo);
            if (AccConstants.LINKAGE_DOOR_EVENT.contains(tempTriggerOpt)) {
                isContainDoorEvent = true;
                break;
            }
        }
        Map<String, Map<String, String>> checkedInputMap = null;// {"inputType": {"inputId": ""}}
        Map<String, Map<String, String>> checkedOutputMap = null;
        if (StringUtils.isNotBlank(accLinkageId)) {
            checkedInputMap = new HashMap<>();
            checkedOutputMap = new HashMap<>();
            Map<String, String> tempMap = null;
            AccLinkage tempAccLinkage = accLinkageDao.findById(accLinkageId).orElse(new AccLinkage());
            Set<AccLinkageInOut> linkageInOutSet = tempAccLinkage.getAccLinkageInOutSet();// 输入
            for (AccLinkageInOut inOut : linkageInOutSet) {
                if (checkedInputMap.containsKey(inOut.getInputType())) {
                    checkedInputMap.get(inOut.getInputType()).put(inOut.getInputId(), "");
                } else {
                    tempMap = new HashMap<>();
                    tempMap.put(inOut.getInputId(), "");
                    checkedInputMap.put(inOut.getInputType(), tempMap);
                }
                if (checkedOutputMap.containsKey(inOut.getOutputType())) {
                    checkedOutputMap.get(inOut.getOutputType()).put(inOut.getOutputId(), "");
                } else {
                    tempMap = new HashMap<>();
                    tempMap.put(inOut.getOutputId(), "");
                    checkedOutputMap.put(inOut.getOutputType(), tempMap);
                }
            }
        }
        AccDevice dev = accDeviceDao.findById(devId).orElse(new AccDevice());
        // 门输出点
        List<Object[]> doorList = accDoorDao.getDoorInfoByDevId(devId);
        // 辅助输出点
        List<Object[]> auxOutList = accAuxOutDao.getAuxOutInfoByDevId(devId);
        JSONArray doorOutputArray = setListToArray(doorList, AccDoor.class.getSimpleName(), checkedOutputMap);// 门输出点
        JSONArray auxOutOutputArray = setListToArray(auxOutList, AccAuxOut.class.getSimpleName(), checkedOutputMap);
        JSONArray doorInputArray = null;
        JSONArray auxInArray = null;
        Map<String, JSONArray> readerMap = null;
        // 辅助输入点事件
        if (accDeviceEventService.isAuxinEventByNo(tempTriggerOpt)) {
            // 辅助输入点
            List<Object[]> auxInList = accAuxInDao.getAuxInInfoByDevId(devId);
            auxInArray = setListToArray(auxInList, AccAuxIn.class.getSimpleName(), checkedInputMap);
        } else {
            // 门输入点
            doorInputArray = setListToArray(doorList, AccDoor.class.getSimpleName(), checkedInputMap);
            // 设备支持读头联动
            if (!isContainDoorEvent && accDeviceOptionService.isSupportFun(dev.getSn(), "~ReaderLinkageFunOn")) {
                List<AccReader> readerList = accReaderDao.findByAccDoor_Device_IdOrderByReaderNoAsc(devId);
                readerMap = buildReaderTree(checkedInputMap, readerList);// 组装读头树
            }
        }
        // 返回的输入点
        JSONObject dataJson = new JSONObject();
        dataJson.put("id", "0_any");
        dataJson.put("text", I18nUtil.i18nCode("common_linkIO_any"));
        dataJson.put("open", "1");
        // 任意
        if (checkedInputMap != null && checkedInputMap.containsKey("any")) {
            dataJson.put("checked", "1");
        }
        // 是否为设备事件参数
        boolean isContainDeivceEvent = false;
        for (String eventNo : triggerOpt) {
            tempTriggerOpt = Integer.parseInt(eventNo.indexOf("_") >= 0 ? eventNo.split("_")[0] : eventNo);
            if (accDeviceEventService.isDeviceEventByNo(tempTriggerOpt)) {
                isContainDeivceEvent = true;
                break;
            }
        }
        JSONArray inputArray = new JSONArray();
        // 设备事件输入点仅"任意"选项，修复前端页面输入点配置门或读头无法产生联动事件的问题
        if (isContainDeivceEvent) {
            inputArray.add(dataJson);
        } else {
            // 组装输入点信息
            inputArray = buildInputArray(readerMap, dataJson, auxInArray, doorInputArray);
        }
        map.put("inputArray", inputArray);
        map.put("devSn", dev.getSn());
        map.put("checkedOutputMap", checkedOutputMap);
        map.put("doorOutputArray", doorOutputArray);
        map.put("auxOutOutputArray", auxOutOutputArray);
        return map;
    }

    private void addLinkage(AccLinkageBeanItem accLinkageBean, AccLinkage accLinkage) {
        boolean editFlag = false;// 是否是编辑
        boolean changeFlag = false;// 联动是否有修改 false：没有修改
        if (StringUtils.isBlank(accLinkage.getId())) {
            accLinkageDao.save(accLinkage);
        } else {
            editFlag = true;
            changeFlag = checkChange(accLinkageBean, accLinkage);
            accLinkage = accLinkageDao.save(accLinkage);
            if (changeFlag) {
                List<Integer> indexList = new ArrayList<>();
                List<AccLinkageTrigger> linkageTriggerList =
                    accLinkageTriggerDao.findByAccLinkage_Id(accLinkage.getId());
                List<AccLinkageInOut> linkageInOutList = accLinkageInOutDao.findByAccLinkage_Id(accLinkage.getId());
                if (linkageInOutList.size() > 0) {
                    // 先删除
                    accLinkageInOutDao.delete(linkageInOutList);
                }
                for (AccLinkageTrigger linkageTrigger : linkageTriggerList) {
                    if (!indexList.contains(linkageTrigger.getLinkageIndex())) {
                        indexList.add(linkageTrigger.getLinkageIndex());
                    }
                }
                if (indexList.size() > 0) {
                    accDevCmdManager.delLinkageFromDev(accLinkage.getAccDevice().getSn(), indexList, false);// 删除控制器上联动
                }
            }
        }
        // 新增或编辑时联动有改动
        if (!editFlag || (editFlag && changeFlag)) {
            addLinkageInOutAndTrigger(accLinkageBean, accLinkage);
        }
    }

    /**
     * 保存输入输出、触发条件并下发
     *
     * @param accLinkageBean:
     * @param accLinkage:
     * @return void
     * <AUTHOR>
     * @date 2022-07-15 11:50
     * @since 1.0.0
     */
    private void addLinkageInOutAndTrigger(AccLinkageBeanItem accLinkageBean, AccLinkage accLinkage) {
        // [0_any] or [1_AccDoor, 2_AccAuxIn]
        String[] inputsAddr = accLinkageBean.getInputsAddr();
        // [0_AccDoor, 0_AccAuxOut] or [1_AccDoor, 2_AccDoor,
        String[] outputsAddr = accLinkageBean.getOutputsAddr();
        String[] triggerCondArray = accLinkageBean.getTriggerCondArray();
        // 1_AccAuxOut, 2_AccAuxOut]
        short doorActionType = accLinkageBean.getDoorActionType();
        short doorActionTime = accLinkageBean.getDoorActionTime();
        short auxoutActionType = accLinkageBean.getAuxoutActionType();
        short auxoutActionTime = accLinkageBean.getAuxoutActionTime();
        // 触发事件
        List<Short> triggerCondList = new ArrayList<>();
        for (String eventNo : triggerCondArray) {
            if (!eventNo.contains("_parent")) {
                triggerCondList
                    .add(eventNo.contains("_") ? Short.parseShort(eventNo.split("_")[0]) : Short.parseShort(eventNo));
            }
        }

        AccLinkageIndex devLinkageIndex = accLinkageIndexDao.findByAccDevice_Id(accLinkage.getAccDevice().getId());
        int maxIndex = devLinkageIndex != null ? devLinkageIndex.getMaxIndex() : 0;
        Set<AccLinkageInOut> linkageInOutSet = new HashSet<>();
        Set<AccLinkageTrigger> linkageTriggerSet = new HashSet<>();
        AccLinkageInOut linkageInOut = null;
        AccLinkageTrigger linkageTrigger = null;
        // 是否支持IPC联动
        boolean supportIPCLinkage =
            accDeviceOptionService.getAccSupportFunListVal(accLinkage.getAccDevice().getId(), 65);
        for (String input : inputsAddr) {
            if (outputsAddr.length > 0) {
                for (String output : outputsAddr) {
                    String outputType = output.split("_")[1];
                    linkageInOut = new AccLinkageInOut(accLinkage, input.split("_")[0], input.split("_")[1],
                        output.split("_")[0], outputType,
                        outputType.equals(AccDoor.class.getSimpleName()) ? doorActionTime : auxoutActionTime,
                        outputType.equals(AccDoor.class.getSimpleName()) ? doorActionType : auxoutActionType);
                    linkageInOutSet.add(linkageInOut);
                    accLinkageInOutDao.save(linkageInOut);
                    // 触发事件
                    for (Short eventNo : triggerCondList) {
                        maxIndex++;
                        linkageTrigger = new AccLinkageTrigger(accLinkage, linkageInOut, maxIndex, eventNo);
                        linkageTriggerSet.add(linkageTrigger);
                        accLinkageTriggerDao.save(linkageTrigger);
                    }
                }
            } else {
                linkageInOut = new AccLinkageInOut(accLinkage, input.split("_")[0], input.split("_")[1], "", "",
                    (short)0, (short)0);
                linkageInOutSet.add(linkageInOut);
                accLinkageInOutDao.save(linkageInOut);
                // 触发事件
                for (Short eventNo : triggerCondList) {
                    maxIndex++;
                    linkageTrigger = new AccLinkageTrigger(accLinkage, linkageInOut, maxIndex, eventNo);
                    linkageTriggerSet.add(linkageTrigger);
                    accLinkageTriggerDao.save(linkageTrigger);
                }
            }
            // 支持ipc联动的设备也需要存inout表
            if (supportIPCLinkage
                && (accLinkageBean.getRecordTime() != null || StringUtils.isNotBlank(accLinkageBean.getCapture()))) {
                // ipc联动只需记录类型为ipc，下发时再去查询视频联动相关设置
                linkageInOut = new AccLinkageInOut(accLinkage, input.split("_")[0], input.split("_")[1], "", "IPC",
                    (short)0, (short)0);
                linkageInOutSet.add(linkageInOut);
                accLinkageInOutDao.save(linkageInOut);
                // 触发事件
                for (Short eventNo : triggerCondList) {
                    maxIndex++;
                    linkageTrigger = new AccLinkageTrigger(accLinkage, linkageInOut, maxIndex, eventNo);
                    linkageTriggerSet.add(linkageTrigger);
                    accLinkageTriggerDao.save(linkageTrigger);
                }
            }
        }
        if (devLinkageIndex != null) {
            accLinkageIndexDao.delete(devLinkageIndex);
        }
        devLinkageIndex = new AccLinkageIndex(accLinkage.getAccDevice(), maxIndex);
        // 更新max index
        accLinkageIndexDao.save(devLinkageIndex);
        // 联动变动
        if (linkageInOutSet.size() > 0 || linkageTriggerSet.size() > 0) {
            accLinkage.setAccLinkageInOutSet(linkageInOutSet);
            accLinkage.setAccLinkageTriggerSet(linkageTriggerSet);
            // 缓存联动信息
            putLinkageInfoToCache(accLinkage);
            accDevCmdManager.setLinkageToDev(accLinkage.getAccDevice().getSn(), buildItemInfo(accLinkage), false);
        }
    }

    private AccLinkageItem buildItemInfo(AccLinkage accLinkage) {
        AccLinkageItem item = new AccLinkageItem();
        item.setId(accLinkage.getId());
        item.setDeviceId(accLinkage.getAccDevice().getId());
        item.setName(accLinkage.getName());
        return item;
    }

    /**
     * 缓存联动信息
     *
     * @param accLinkage:
     * @return void
     * <AUTHOR>
     * @date 2022-07-19 11:42
     * @since 1.0.0
     */
    private void putLinkageInfoToCache(AccLinkage accLinkage) {
        AccDevice accDevice = accLinkage.getAccDevice();
        AccQueryLinkageItem linkageItem = new AccQueryLinkageItem();
        linkageItem.setId(accLinkage.getId());
        linkageItem.setDeviceId(accDevice.getId());
        linkageItem.setDeviceSn(accDevice.getSn());
        linkageItem.setName(accLinkage.getName());
        linkageItem.setAccLinkageInOutList(buildInOutItemList(accLinkage));
        linkageItem.setAccLinkageTriggerList(buildTriggerItemList(accLinkage));
        accCacheManager.putAccQueryLinkageInfo(accDevice.getSn(), linkageItem);
    }

    private List<AccLinkageInOutItem> buildInOutItemList(AccLinkage accLinkage) {
        Set<AccLinkageInOut> linkageInOutSets = accLinkage.getAccLinkageInOutSet();
        List<AccLinkageInOutItem> inOutItemList = new ArrayList<>();
        if (linkageInOutSets != null && linkageInOutSets.size() > 0) {
            for (AccLinkageInOut inout : linkageInOutSets) {
                AccLinkageInOutItem inOutItem = ModelUtil.copyProperties(inout, new AccLinkageInOutItem());
                inOutItem.setLinkageId(inout.getAccLinkage().getId());
                inOutItemList.add(inOutItem);
            }
        }
        return inOutItemList;
    }

    private List<AccLinkageTriggerItem> buildTriggerItemList(AccLinkage accLinkage) {
        Set<AccLinkageTrigger> linkageTriggerSet = accLinkage.getAccLinkageTriggerSet();
        List<AccLinkageTriggerItem> triggerItemList = new ArrayList<>();
        if (linkageTriggerSet != null && linkageTriggerSet.size() > 0) {
            for (AccLinkageTrigger trigger : linkageTriggerSet) {
                AccLinkageTriggerItem triggerItem = ModelUtil.copyProperties(trigger, new AccLinkageTriggerItem());
                triggerItem.setLinkageId(trigger.getAccLinkage().getId());
                triggerItemList.add(triggerItem);
            }
        }
        return triggerItemList;
    }

    @Override
    public List<AccQueryLinkageItem> getAccQueryLinkageItemList(String devSn) {
        Set<String> keySets = accCacheManager.getAccQueryLinkageKeySets(devSn);
        List<AccQueryLinkageItem> linkageItemList = new ArrayList<>();
        if (!keySets.isEmpty()) {
            for (String key : keySets) {
                AccQueryLinkageItem AccQueryLinkageItem = accCacheManager.getAccQueryLinkageItemByKey(key);
                if (AccQueryLinkageItem != null) {
                    linkageItemList.add(AccQueryLinkageItem);
                }
            }
        } else {
            List<AccLinkage> linkages = accLinkageDao.findByAccDevice_Sn(devSn);
            if (!linkages.isEmpty()) {
                for (AccLinkage linkage : linkages) {
                    AccQueryLinkageItem accQueryLinkageItem = new AccQueryLinkageItem();
                    accQueryLinkageItem.setId(linkage.getId());
                    accQueryLinkageItem.setName(linkage.getName());
                    accQueryLinkageItem.setDeviceId(linkage.getAccDevice().getId());
                    accQueryLinkageItem.setDeviceSn(devSn);
                    accQueryLinkageItem.setAccLinkageInOutList(buildInOutItemList(linkage));
                    accQueryLinkageItem.setAccLinkageTriggerList(buildTriggerItemList(linkage));
                    linkageItemList.add(accQueryLinkageItem);
                    accCacheManager.putAccQueryLinkageInfo(devSn, accQueryLinkageItem);
                }
            }
        }
        return linkageItemList;
    }

    /**
     * 删除软联动设置（邮件、视频联动）
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年6月1日 下午3:56:01
     * @param linkId
     */
    private void delExtendLinkage(String linkId) {
        AccLinkage accLinkage = accLinkageDao.findById(linkId).orElse(new AccLinkage());
        Set<AccLinkageMedia> accLinkageMediaSet = accLinkage.getAccLinkageMediaSet();
        accLinkageMediaDao.delete(accLinkageMediaSet);

        Set<AccLinkageVid> accLinkageVidSet = accLinkage.getAccLinkageVidSet();
        accLinkageVidDao.delete(accLinkageVidSet);

        List<AccLinkageIas> accLinkageIasList = accLinkage.getAccLinkageIasSet();
        accLinkageIasDao.delete(accLinkageIasList);
    }

    /**
     * 添加联动邮件设置
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年6月1日 上午10:56:13
     * @param accLinkageBean
     * @param accLinkage
     */
    private void addLinkageMedia(AccLinkageBeanItem accLinkageBean, AccLinkage accLinkage) {
        String[] mailAddr = accLinkageBean.getMailAddr();
        List<AccLinkageMedia> accGlobalLinkageMediaList = new ArrayList<>();
        if (mailAddr != null && mailAddr.length > 0)// 邮件设置输入框不为空时
        {
            for (String mail : mailAddr) {
                // 防止传入的数据类似；；；；；；；<EMAIL>
                if (StringUtils.isNotBlank(mail)) {
                    AccLinkageMedia tempMail = new AccLinkageMedia(accLinkage, mail, AccConstants.MAIL);
                    accGlobalLinkageMediaList.add(tempMail);
                }
            }
        }

        String[] mobileNos = accLinkageBean.getMobileNo();
        // 短信设置输入框不为空时
        if (mobileNos != null && mobileNos.length > 0) {
            for (String mobile : mobileNos) {
                if (StringUtils.isNotBlank(mobile)) {
                    AccLinkageMedia tempSMS = new AccLinkageMedia(accLinkage, mobile, AccConstants.SMS);
                    accGlobalLinkageMediaList.add(tempSMS);
                }
            }
        }
        // 保存digifort设置
        if (accLinkageBean != null && StringUtils.isNotBlank(accLinkageBean.getDigiEventNames())) {
            String[] digiEventNames = accLinkageBean.getDigiEventNames().split(",");
            for (String digiEventName : digiEventNames) {
                if (StringUtils.isNotBlank(digiEventName)) {
                    AccLinkageMedia digifort = new AccLinkageMedia(accLinkage, digiEventName, AccConstants.DIGIFORT);
                    accGlobalLinkageMediaList.add(digifort);
                }
            }
        }

        // 保存LineContact设置
        if (accLinkageBean != null && StringUtils.isNotBlank(accLinkageBean.getLineContactIds())) {
            String[] contactIds = accLinkageBean.getLineContactIds().split(",");
            for (String contactId : contactIds) {
                if (StringUtils.isNotBlank(contactId)) {
                    AccLinkageMedia lineContact = new AccLinkageMedia(accLinkage, contactId, AccConstants.LINE);
                    accGlobalLinkageMediaList.add(lineContact);
                }
            }
        }
        // 保存whatsapp设置
        String[] whatsappMobileNo = accLinkageBean.getWhatsappMobileNo();
        if (whatsappMobileNo != null && whatsappMobileNo.length > 0) {
            for (String mobile : whatsappMobileNo) {
                if (StringUtils.isNotBlank(mobile)) {
                    AccLinkageMedia whatsapp = new AccLinkageMedia(accLinkage, mobile, AccConstants.WHATSAPP);
                    accGlobalLinkageMediaList.add(whatsapp);
                }
            }
        }

        // 保存可视对讲通知对象设置
        if (accLinkageBean != null && StringUtils.isNotBlank(accLinkageBean.getVdbExtensionIds())) {
            String[] vdbExtensionIds = accLinkageBean.getVdbExtensionIds().split(",");
            for (String extensionId : vdbExtensionIds) {
                if (StringUtils.isNotBlank(extensionId)) {
                    AccLinkageMedia vdbExtension = new AccLinkageMedia(accLinkage, extensionId, AccConstants.VDB);
                    accGlobalLinkageMediaList.add(vdbExtension);
                }
            }
        }
        if (accLinkageBean != null && StringUtils.isNotBlank(accLinkageBean.getVdbIvrId())) {
            AccLinkageMedia vdbIvr =
                new AccLinkageMedia(accLinkage, accLinkageBean.getVdbIvrId(), AccConstants.VDB_IVR);
            accGlobalLinkageMediaList.add(vdbIvr);
        }
        if (accGlobalLinkageMediaList.size() > 0) {
            accLinkageMediaDao.saveAll(accGlobalLinkageMediaList);
        }
    }

    /**
     * 添加视频联动设置
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年6月1日 下午2:04:28
     * @param accLinkageBean
     * @param accLinkage
     */
    private void addLinkageVid(AccLinkageBeanItem accLinkageBean, AccLinkage accLinkage) {
        List<AccLinkageVid> accLinkageVidList = new ArrayList<AccLinkageVid>();
        // 弹出视频
        Integer popUpTime = accLinkageBean.getPopUpTime();
        if (popUpTime != null) {
            accLinkageVidList.add(new AccLinkageVid(accLinkage, popUpTime, AccConstants.POP_UP_VIDEO));
        }
        // 录像
        Integer recordBeforeTime = accLinkageBean.getRecordBeforeTime();
        Integer recordTime = accLinkageBean.getRecordTime();
        if (recordBeforeTime != null && recordTime != null) {
            accLinkageVidList.add(new AccLinkageVid(accLinkage, recordBeforeTime, recordTime, AccConstants.RECORD));
        }
        // 抓拍
        Integer captureTime = accLinkageBean.getCaptureTime();
        if (captureTime != null) {
            accLinkageVidList.add(new AccLinkageVid(accLinkage, captureTime, AccConstants.CAPTURE));
        }
        if (!accLinkageVidList.isEmpty()) {
            accLinkageVidDao.saveAll(accLinkageVidList);
        }
    }

    /**
     * 添加入侵联动设置
     *
     * @param accLinkageBeanItem:
     * @param accLinkage:
     * @return void
     * <AUTHOR>
     * @date 2022-12-01 15:54
     * @since 1.0.0
     */
    private void addLinkageIas(AccLinkageBeanItem accLinkageBeanItem, AccLinkage accLinkage) {
        String[] partitionIds = accLinkageBeanItem.getIasPartitionIds();
        if (partitionIds.length == 0) {
            return;
        }
        List<AccLinkageIas> accLinkageIasList = new ArrayList<>();
        Short actionType = accLinkageBeanItem.getPartitionActionType();
        Short armType = accLinkageBeanItem.getPartitionArmType();
        String manufacture = accLinkageBeanItem.getIasManufacture();
        for (String partitionId : partitionIds) {
            AccLinkageIas linkageIas = new AccLinkageIas();
            linkageIas.setAccLinkage(accLinkage);
            linkageIas.setPartitionId(partitionId);
            linkageIas.setActionType(actionType);
            linkageIas.setArmType(armType);
            linkageIas.setManufacture(manufacture);
            accLinkageIasList.add(linkageIas);
        }
        if (accLinkageIasList.size() > 0) {
            accLinkageIasDao.saveAll(accLinkageIasList);
        }
    }

    /**
     * 将List数据转化为JSONArray
     *
     * <AUTHOR>
     * @since 2014年8月22日 下午4:58:04
     * @param objList 要转化的List
     * @param type 要转化的类型
     * @param checkedMap 选中对象的Map
     * @return JSONArray
     * @throws Exception 异常
     */
    private JSONArray setListToArray(List<Object[]> objList, String type, Map<String, Map<String, String>> checkedMap) {
        JSONArray dataArray = new JSONArray();
        Map<String, String> tempCheckedMap = null;
        if (checkedMap != null && checkedMap.containsKey(type)) {
            tempCheckedMap = checkedMap.get(type);
        }
        JSONObject jsonObj = new JSONObject();
        for (Object[] obj : objList) {
            jsonObj = new JSONObject();
            jsonObj.put("id", obj[0] + "_" + type);
            jsonObj.put("text", obj[1]);
            if (tempCheckedMap != null && tempCheckedMap.containsKey(obj[0].toString())) {
                jsonObj.put("checked", "1");
            }
            dataArray.add(jsonObj);
        }
        return dataArray;
    }

    /**
     * 组装读头树
     *
     * <AUTHOR>
     * @since 2014年9月17日 下午3:18:40
     * @param checkedInputMap
     * @param readerList
     * @return Map<String, JSONArray>
     * @throws Exception
     */
    private Map<String, JSONArray> buildReaderTree(Map<String, Map<String, String>> checkedInputMap,
        List<AccReader> readerList) {
        Map<String, JSONArray> readerMap = new HashMap<String, JSONArray>();
        JSONArray readerArray = new JSONArray();
        JSONObject jsonObj = null;
        String doorId = "";
        Map<String, String> checkedReaderMap = null;
        if (checkedInputMap != null && checkedInputMap.containsKey(AccReader.class.getSimpleName())) {
            checkedReaderMap = checkedInputMap.get(AccReader.class.getSimpleName());
        }
        for (AccReader accReader : readerList) {
            jsonObj = new JSONObject();
            jsonObj.put("id", accReader.getId() + "_AccReader");
            jsonObj.put("text", accReader.getName());
            if (checkedReaderMap != null && checkedReaderMap.containsKey(accReader.getId().toString())) {
                jsonObj.put("checked", "1");
            }
            if ("".equals(doorId))// 首次进来
            {
                doorId = String.valueOf(accReader.getAccDoor().getId());
            }
            if (doorId.equals(String.valueOf(accReader.getAccDoor().getId())))// 同一个门的读头
            {
                readerArray.add(jsonObj);
            } else {
                readerMap.put(doorId, readerArray);
                readerArray = new JSONArray();
                readerArray.add(jsonObj);
                doorId = String.valueOf(accReader.getAccDoor().getId());
            }
        }
        if (readerArray.size() > 0) {
            readerMap.put(doorId, readerArray);
        }
        return readerMap;
    }

    /**
     * 组装输入点信息
     *
     * <AUTHOR>
     * @since 2014年9月17日 下午3:28:42
     * @param readerMap
     * @param dataJson
     * @param auxInArray
     * @param doorInputArray
     * @return JSONArray
     * @throws Exception
     */
    private JSONArray buildInputArray(Map<String, JSONArray> readerMap, JSONObject dataJson, JSONArray auxInArray,
        JSONArray doorInputArray) {
        JSONArray inputArray = new JSONArray();
        inputArray.add(dataJson);
        if (auxInArray != null) {
            inputArray = concatArray(inputArray, auxInArray);
        } else {
            // 不支持读头联动
            if (readerMap == null) {
                inputArray = concatArray(inputArray, doorInputArray);
            } else {
                // 读头作为门的下级
                JSONObject doorJson = null;
                String doorId = "";
                for (int i = 0; i < doorInputArray.size(); i++) {
                    doorJson = doorInputArray.getJSONObject(i);
                    doorId = doorJson.getString("id").split("_")[0];
                    if (readerMap.containsKey(doorId)) {
                        doorJson.put("open", "1");
                        doorJson.put("item", readerMap.get(doorId));
                    }
                    inputArray.add(doorJson);
                }
            }
        }
        return inputArray;
    }

    /**
     * 两个JSONArray相加
     *
     * <AUTHOR>
     * @since 2014年8月22日 下午5:10:15
     * @param retArray 目标JSONArray
     * @param dataArray 追加的JSONArray
     * @return JSONArray
     * @throws Exception 异常
     */
    private JSONArray concatArray(JSONArray retArray, JSONArray dataArray) {
        for (int i = 0; i < dataArray.size(); i++) {
            retArray.add(dataArray.getJSONObject(i));
        }
        return retArray;
    }

    /**
     * 比较联动是否有变动
     *
     * <AUTHOR>
     * @since 2015年1月15日 下午9:02:07
     * @param newLinkageBean
     * @param oldLinkage
     * @return boolean
     */
    private boolean checkChange(AccLinkageBeanItem newLinkageBean, AccLinkage oldLinkage) {
        boolean changeFlag = false;
        String[] inputsAddr = newLinkageBean.getInputsAddr();// [0_any] or [1_AccDoor, 2_AccAuxIn]
        String[] outputsAddr = newLinkageBean.getOutputsAddr();// [0_AccDoor, 0_AccAuxOut] or [1_AccDoor, 2_AccDoor,
                                                               // 1_AccAuxOut, 2_AccAuxOut]
        String[] triggerCondArray = newLinkageBean.getTriggerCondArray();
        short doorActionType = newLinkageBean.getDoorActionType();
        short doorActionTime = newLinkageBean.getDoorActionTime();
        short auxoutActionType = newLinkageBean.getAuxoutActionType();
        short auxoutActionTime = newLinkageBean.getAuxoutActionTime();
        Short oldDoorActionType = null;
        Short oldDoorActionTime = null;
        Short oldAuxoutActionType = null;
        Short oldAuxoutActionTime = null;
        List<String> oldInputAddrList = new ArrayList<String>();
        List<String> oldOutputAddrList = new ArrayList<String>();
        List<String> oldTriggerCondList = new ArrayList<String>();
        Set<AccLinkageInOut> oldLinkageInOutSet = oldLinkage.getAccLinkageInOutSet();
        for (AccLinkageInOut oldLinkageInOut : oldLinkageInOutSet) {
            String inAddr = oldLinkageInOut.getInputId() + "_" + oldLinkageInOut.getInputType();
            String outAddr = oldLinkageInOut.getOutputId() + "_" + oldLinkageInOut.getOutputType();
            if (AccDoor.class.getSimpleName().equals(oldLinkageInOut.getOutputType())) {
                oldDoorActionType = oldLinkageInOut.getActionType();
                oldDoorActionTime = oldLinkageInOut.getActionTime();
            } else if (AccAuxOut.class.getSimpleName().equals(oldLinkageInOut.getOutputType())) {
                oldAuxoutActionType = oldLinkageInOut.getActionType();
                oldAuxoutActionTime = oldLinkageInOut.getActionTime();
            }
            if (!oldInputAddrList.contains(inAddr)) {
                oldInputAddrList.add(inAddr);
            }
            if (!oldOutputAddrList.contains(outAddr)) {
                oldOutputAddrList.add(outAddr);
            }
            for (AccLinkageTrigger trigger : oldLinkageInOut.getAccLinkageTriggerSet()) {
                if (!oldTriggerCondList.contains(trigger.getTriggerCond().toString())) {
                    oldTriggerCondList.add(trigger.getTriggerCond().toString());
                }
            }
        }
        Arrays.sort(inputsAddr);
        Object[] oldInputAddr = oldInputAddrList.toArray();
        Arrays.sort(oldInputAddr);
        Arrays.sort(outputsAddr);
        Object[] oldOutputAddr = oldOutputAddrList.toArray();
        Arrays.sort(oldOutputAddr);
        Arrays.sort(triggerCondArray);
        Object[] oldTriggerCond = oldTriggerCondList.toArray();
        Arrays.sort(oldTriggerCond);
        if (!Arrays.equals(inputsAddr, oldInputAddr) || !Arrays.equals(outputsAddr, oldOutputAddr)
            || !Arrays.equals(triggerCondArray, oldTriggerCond)
            || (oldDoorActionType != null && doorActionType != oldDoorActionType.shortValue())
            || (oldDoorActionTime != null && doorActionTime != oldDoorActionTime.shortValue())
            || (oldAuxoutActionType != null && auxoutActionType != oldAuxoutActionType.shortValue())
            || (oldAuxoutActionTime != null && auxoutActionTime != oldAuxoutActionTime.shortValue())) {
            changeFlag = true;
        }
        return changeFlag;
    }

    @Override
    public Map<String, String> getLinkageAction(int linkIndex, String devId) {
        Map<String, String> retMap = new HashMap<String, String>();
        String description = "";
        List<Object[]> dataObj = getLinkageInfoByParams(linkIndex, devId);
        if (dataObj != null && dataObj.size() > 0) {
            Object[] linkage = dataObj.get(0);
            description = String.format("%s:%s;%s:%s", I18nUtil.i18nCode("common_linkIO_linkageName"),
                linkage[4].toString(), I18nUtil.i18nCode("common_linkIO_conditions"), linkage[0].toString());
            retMap.put("description", description);
        }
        return retMap;
    }

    /**
     * 获取联动信息
     *
     * <AUTHOR>
     * @since 2015年5月14日 下午5:52:07
     * @param linkIndex
     * @param devId
     * @return List<Object[]>
     */
    public List<Object[]> getLinkageInfoByParams(int linkIndex, String devId) {
        return accLinkageDao.getLinkageInfoByParams(linkIndex, devId);
    }

    @Override
    public List<String> getTriggerCondByLinkId(String linkageId) {
        return accLinkageDao.getTriggerCondByLinkId(linkageId);
    }

    @Override
    public String getLinkageIdByIndexAndDevId(int linkIndex, String devId) {
        List<String> linkageIdList = accLinkageTriggerDao.getLinkageIdByIndexAndDevId(linkIndex, devId);
        if (null != linkageIdList && !linkageIdList.isEmpty()) {
            return linkageIdList.get(0);
        }
        return "";
    }

    @Override
    public void handlerTransfer(List<AccLinkageItem> accLinkageItems) {
        List<List<AccLinkageItem>> accLinkageItemList = CollectionUtil.split(accLinkageItems, CollectionUtil.splitSize);
        for (List<AccLinkageItem> linkageItems : accLinkageItemList) {
            // 封装联动map对象 根据名称
            Collection<String> names = CollectionUtil.getPropertyList(linkageItems, AccLinkageItem::getName, "-1");
            List<AccLinkage> accLinkageList = accLinkageDao.findByNameIn((List<String>)names);
            Map<String, AccLinkage> accLinkageMap = CollectionUtil.listToKeyMap(accLinkageList, AccLinkage::getName);
            // 获取设备对象集合
            Collection<String> devIds = CollectionUtil.getPropertyList(linkageItems, AccLinkageItem::getDeviceId, "-1");
            // List<String>转List<Long>
            List<Long> longList = new ArrayList<>();
            for (String str : devIds) {
                Long i = Long.parseLong(str);
                longList.add(i);
            }
            List<AccDevice> accDeviceList = accDeviceDao.findByBusinessIdIn(longList);
            Map<Long, AccDevice> accDeviceMap = CollectionUtil.listToKeyMap(accDeviceList, AccDevice::getBusinessId);
            for (AccLinkageItem accLinkageItem : linkageItems) {
                AccLinkage accLinkage = accLinkageMap.remove(accLinkageItem.getName());
                if (Objects.isNull(accLinkage)) {
                    accLinkage = new AccLinkage();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accLinkageItem, accLinkage, "id");
                // 获取设备对象
                AccDevice accDevice = accDeviceMap.get(Long.valueOf(accLinkageItem.getDeviceId()));
                if (Objects.nonNull(accDevice)) {
                    accLinkage.setAccDevice(accDevice);
                }
                accLinkageDao.save(accLinkage);
            }
        }
    }

    @Override
    public boolean completeSMSModemInfo() {
        if (smsModem4OtherService != null) {
            return smsModem4OtherService.completeSMSModemInfo();
        }
        return false;
    }

    @Override
    public boolean checkShowSMS() {
        if (smsModem4OtherService != null && smsModem4OtherService.checkSmsModemLicense()) {
            return true;
        }
        return false;
    }

    @Override
    public boolean checkShowDigifort() {
        // 需要检查digifort许可
        if (Objects.nonNull(accGetIVideoService)) {
            return accGetIVideoService.checkDigifortLicense();
        }
        return false;
    }

    @Override
    public JSONArray getDigifortGlobalEvents(String linkageId, String type) {
        JSONArray globalEventNames = new JSONArray();
        if (StringUtils.isNotBlank(linkageId)) {

            List<String> linkageDigifortList =
                accLinkageMediaDao.getMediaContentByAccLinkageIdAndMediaType(linkageId, AccConstants.DIGIFORT);
            for (String name : linkageDigifortList) {
                globalEventNames.add(name);
            }
        }
        JSONArray globalEventArray = new JSONArray();
        if (Objects.nonNull(accGetIVideoService)) {
            JSONObject data = accGetIVideoService.getDigifortGlobalEventNames();
            if (data != null && data.containsKey("events")) {
                globalEventArray = data.getJSONArray("events");
            }
        }
        for (int i = 0; i < globalEventArray.size(); i++) {
            if (globalEventNames.contains(globalEventArray.getJSONObject(i).getString("id"))) {
                globalEventArray.getJSONObject(i).put("checked", 1);
                globalEventNames.remove(globalEventArray.getJSONObject(i).getString("id"));
            }
        }
        if ("checkExist".equals(type)) {
            return globalEventNames;
        } else {
            return globalEventArray;
        }
    }

    @Override
    public List<AccLinkageItem> getItemsByDevId(String devId) {
        AccLinkageItem condition = new AccLinkageItem();
        List<AccLinkageItem> accLinkageItemList = new ArrayList<>();
        if (StringUtils.isNotBlank(devId)) {
            condition.setDeviceId(devId);
            accLinkageItemList = getByCondition(condition);
        }
        return accLinkageItemList;
    }

    @Override
    public ZKResultMsg executeExtendLinkage(String linkageId, AccTransactionItem lastTransaction,
        AccTransactionItem newTransaction) {
        return accLinkageOperate.executeExtendLinkage(linkageId, lastTransaction, newTransaction);
    }

    @Override
    public boolean checkShowLine() {
        if (line4OtherService != null && line4OtherService.checkLineLicense()) {
            return true;
        }
        return false;
    }

    @Override
    public Pager getNoExistLineContacts(AccLinkageSelectContactItem condition, int pageNo, int pageSize) {
        Pager pager = new Pager();
        if (line4OtherService != null) {
            OthersGetLineContactSelectItem item = new OthersGetLineContactSelectItem();
            item = ModelUtil.copyProperties(condition, item);
            pager = line4OtherService.getLineContactSelectItemByPage(item, pageNo, pageSize);
            List<OthersGetLineContactSelectItem> accGetLineContactSelectItems =
                (List<OthersGetLineContactSelectItem>)pager.getData();
            List<AccLinkageSelectContactItem> accLinkageSelectContactItems =
                ModelUtil.copyListProperties(accGetLineContactSelectItems, AccLinkageSelectContactItem.class);
            pager.setData(accLinkageSelectContactItems);
        }
        return pager;
    }

    @Override
    public List<AccLinkage4LineContactItem> getLineContactsByIds(String lineContactId) {
        List<AccLinkage4LineContactItem> items = new ArrayList<>();
        if (line4OtherService != null) {
            List<OthersGetLineContactSelectItem> accGetLineContactSelectItems =
                line4OtherService.getLineContactByContactIds(lineContactId);
            if (accGetLineContactSelectItems != null && accGetLineContactSelectItems.size() > 0) {
                items = ModelUtil.copyListProperties(accGetLineContactSelectItems, AccLinkage4LineContactItem.class);
            }
        }
        return items;
    }

    @Override
    public boolean checkShowWhatsapp() {
        if (whatsapp4OtherService != null) {
            return whatsapp4OtherService.checkShowWhatsappLicense();
        }
        return false;
    }

    @Override
    public Pager getSelectIasPartition(String sessionId, AccLinkageSelectIasPartitionItem condition, int pageNo,
        int pageSize) {
        Pager pager = new Pager();
        pager.setData(new ArrayList<>());
        if (Objects.isNull(ias4OtherService)) {
            return pager;
        }
        if ("noSelected".equals(condition.getType())) {
            Ias4OtherPartitionItem queryItem = new Ias4OtherPartitionItem();
            ModelUtil.copyProperties(condition, queryItem);
            if (StringUtils.isBlank(condition.getSelectId())) {
                condition.setSelectId("-1");
            }
            String filterId = condition.getFilterId();
            if (StringUtils.isNotBlank(filterId)) {
                queryItem.setIdNotIn(condition.getSelectId() + "," + filterId);
            } else {
                queryItem.setIdNotIn(condition.getSelectId());
            }
            pager = ias4OtherService.getItemByAuthFilter(sessionId, queryItem, pageNo, pageSize);
            List<Ias4OtherPartitionItem> ias4OtherPartitionItemList = (List<Ias4OtherPartitionItem>)pager.getData();
            List<AccLinkageSelectIasPartitionItem> itemList =
                ModelUtil.copyListProperties(ias4OtherPartitionItemList, AccLinkageSelectIasPartitionItem.class);
            pager.setData(itemList);

        }
        return pager;
    }

    @Override
    public TreeItem getIasPartitionTreeByIds(String sessionId, String iasPartitionIds) {
        if (StringUtils.isBlank(iasPartitionIds) || Objects.isNull(ias4OtherService)) {
            return new TreeItem();
        }
        List<Ias4OtherPartitionItem> ias4OtherPartitionItems =
            ias4OtherService.getItemsByIds(Arrays.asList(iasPartitionIds.split(",")));
        if (ias4OtherPartitionItems == null || ias4OtherPartitionItems.size() == 0) {
            return new TreeItem();
        }
        List<TreeItem> items = new ArrayList<>();
        for (Ias4OtherPartitionItem ias4OtherPartitionItem : ias4OtherPartitionItems) {
            TreeItem treeItem = new TreeItem(ias4OtherPartitionItem.getId());
            treeItem.setText(ias4OtherPartitionItem.getName());
            items.add(treeItem);
        }
        return new TreeItem("0", items);
    }

    @Override
    public ZKResultMsg getArmTypeByManufacture(String manufacture) {
        if (StringUtils.isBlank(manufacture) || Objects.isNull(ias4OtherService)) {
            return new ZKResultMsg();
        }
        List<Ias4OtherArmTypeItem> ias4OtherArmTypeItemList = ias4OtherService.getArmType(manufacture);
        JSONArray dataArray = new JSONArray();
        for (Ias4OtherArmTypeItem item : ias4OtherArmTypeItemList) {
            JSONObject data = new JSONObject();
            data.put("name", item.getName());
            data.put("type", item.getArmType());
            dataArray.add(data);
        }
        return new ZKResultMsg(dataArray);
    }

    @Override
    public List<SelectItem> getVdbIvrSelectItems() {
        List<SelectItem> selectItems = new ArrayList<>();
        if (Objects.nonNull(vdb4OtherService)) {
            List<VdbIvr4OtherItem> vdbIvr4OtherItems = vdb4OtherService.getIvrItems();
            SelectItem selectItem = null;
            for (VdbIvr4OtherItem vdbIvr4OtherItem : vdbIvr4OtherItems) {
                selectItem = new SelectItem();
                selectItem.setValue(vdbIvr4OtherItem.getId());
                selectItem.setText(vdbIvr4OtherItem.getName());
                selectItems.add(selectItem);
            }
        }
        return selectItems;
    }

    @Override
    public Pager getExtensionList(AccLinkageSelectExtensionItem condition, int pageNo, int pageSize) {
        Pager pager = new Pager();
        if (vdb4OtherService != null) {
            OthersGetVdbExtensionItem item = new OthersGetVdbExtensionItem();
            item = ModelUtil.copyProperties(condition, item);
            pager = vdb4OtherService.getExtensionSelectItemByPage(item, pageNo, pageSize);
            List<OthersGetVdbExtensionItem> accGetVdbExtensionItems = (List<OthersGetVdbExtensionItem>)pager.getData();
            List<AccLinkageSelectExtensionItem> accLinkageSelectExtensionItemList =
                ModelUtil.copyListProperties(accGetVdbExtensionItems, AccLinkageSelectExtensionItem.class);
            pager.setData(accLinkageSelectExtensionItemList);
        }
        return pager;
    }

    @Override
    public List<AccLinkage4VdbExtensionItem> getVdbExtensionByIds(String extensionIds) {
        List<AccLinkage4VdbExtensionItem> items = new ArrayList<>();
        if (vdb4OtherService != null) {
            List<OthersGetVdbExtensionItem> accGetVdbExtensionItems =
                vdb4OtherService.getVdbExtensionByIds(extensionIds);
            if (accGetVdbExtensionItems != null && accGetVdbExtensionItems.size() > 0) {
                AccLinkage4VdbExtensionItem item = null;
                Map<Short, String> bindTypeName = new HashMap<>();
                bindTypeName.put(Short.parseShort("1"), I18nUtil.i18nCode("common_objectType_persPerson"));
                bindTypeName.put(Short.parseShort("2"), I18nUtil.i18nCode("common_objectType_authUser"));
                for (OthersGetVdbExtensionItem othersGetVdbExtensionItem : accGetVdbExtensionItems) {
                    item = new AccLinkage4VdbExtensionItem();
                    item.setId(othersGetVdbExtensionItem.getId());
                    String extensionInfo = othersGetVdbExtensionItem.getExtensionName() + " ("
                        + othersGetVdbExtensionItem.getExtensionNumber() + ")";
                    /*if (othersGetVdbExtensionItem.getBindType() != null) {
                        extensionInfo += " (" + bindTypeName.get(othersGetVdbExtensionItem.getBindType()) + ":"
                            + othersGetVdbExtensionItem.getBindObject() + ")";
                    }*/
                    item.setExtensionNumber(extensionInfo);
                    items.add(item);
                }
            }
        }
        return items;
    }

    @Override
    public Boolean supportCloudSip() {
        return baseSysParamService.supportCloudSip();
    }
}