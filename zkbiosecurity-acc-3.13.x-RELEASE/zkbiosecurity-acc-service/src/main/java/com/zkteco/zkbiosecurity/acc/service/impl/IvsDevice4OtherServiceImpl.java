package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccMapPosDao;
import com.zkteco.zkbiosecurity.ivs.service.IvsDevice4OtherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/18 11:00
 * @since 1.0.0
 */
@Service
public class IvsDevice4OtherServiceImpl implements IvsDevice4OtherService {

    @Autowired
    private AccMapPosDao accMapPosDao;

    @Override
    public void deleteDevice(Collection<String> ids) {
        if (!ids.isEmpty()) {
            accMapPosDao.delMapPosByEntityId((List<String>)ids, "VidChannel");
        }
    }
}
