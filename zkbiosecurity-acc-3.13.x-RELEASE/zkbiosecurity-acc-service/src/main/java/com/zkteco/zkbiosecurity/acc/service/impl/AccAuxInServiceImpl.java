/**
 * File Name: AccAuxInServiceImpl Created by GenerationTools on 2018-03-13 下午05:00 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.AccAuxInDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTimeSegDao;
import com.zkteco.zkbiosecurity.acc.model.AccAuxIn;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccTimeSeg;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidChannel2EntityService;

/**
 * 对应百傲瑞达 AccAuxInServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-13 下午05:00
 * @version v1.0
 */
@Service
@Transactional
public class AccAuxInServiceImpl implements AccAuxInService {
    @Autowired
    private AccAuxInDao accAuxInDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccTimeSegDao accTimeSegDao;
    @Autowired(required = false)
    private Vid4OtherGetVidChannel2EntityService vid4OtherGetVidChannel2EntityService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccExtDeviceService accExtDeviceService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired(required = false)
    private Acc4IVideoControlEntityService acc4IVideoControlEntityService;

    @Override
    public AccAuxInItem saveItem(AccAuxInItem item) {
        AccAuxIn accAuxIn = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accAuxInDao.findById(id)).orElse(new AccAuxIn());
        ModelUtil.copyPropertiesIgnoreNull(item, accAuxIn);
        if (accDeviceOptionService.isSupportFunList(accAuxIn.getAccDevice().getSn(), 8)) {
            if (item.getAccTimeSegId() != null) {
                accAuxIn.setTimeSegId(item.getAccTimeSegId());
            }
        }

        accAuxIn.setRemark(accAuxIn.getRemark().replace("\r\n", ""));

        accAuxInDao.save(accAuxIn);

        // 下发命令
        AccDevice accDevice = accAuxIn.getAccDevice();
        if (Objects.nonNull(accDevice)) {
            accDeviceService.updateDevInfoWithDoorAndAuxBySn(accDevice.getSn());
            List<AccAuxInItem> accAuxInList = new ArrayList<>();
            item.setAuxNo(accAuxIn.getAuxNo());
            accAuxInList.add(item);
            accDevCmdManager.setAuxInOptToDev(accDevice.getSn(), accAuxInList, false);
        }

        // 发送视频集成模块，更新辅助输入名称
        if (acc4IVideoControlEntityService != null) {
            Acc4IVideoControlEntityItem acc4IVideoControlEntityItem = new Acc4IVideoControlEntityItem();
            acc4IVideoControlEntityItem.setEntityId(accAuxIn.getId());
            acc4IVideoControlEntityItem.setEntityName(accAuxIn.getName());
            acc4IVideoControlEntityItem.setEntityClassName("AccAuxIn");
            acc4IVideoControlEntityService.updateEntityNameByEntityId(acc4IVideoControlEntityItem);
        }
        item.setId(accAuxIn.getId());
        return item;
    }

    @Override
    public List<AccAuxInItem> getByCondition(AccAuxInItem condition) {
        return (List<AccAuxInItem>)accAuxInDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = accAuxInDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        if (condition instanceof AccAuxInItem) {
            List<AccAuxInItem> itemList = (List<AccAuxInItem>)pager.getData();
            String extDevIds = CollectionUtil.getPropertys(itemList, AccAuxInItem::getExtDevId);
            List<AccExtDeviceItem> accExtDeviceItems = accExtDeviceService.getItemByIds(extDevIds);
            Map<String, AccExtDeviceItem> accExtDeviceItemMap = CollectionUtil.itemListToIdMap(accExtDeviceItems);
            itemList.forEach(item -> {
                if (StringUtils.isNotBlank(item.getExtDevId())) {
                    item.setExtDevName(accExtDeviceItemMap.get(item.getExtDevId()).getAlias());
                }
            });
            if (vid4OtherGetVidChannel2EntityService != null) {
                List<String> auxInIdList = (List<String>)CollectionUtil.getItemIdsList(itemList);
                Map<String, String> channelNameMap = vid4OtherGetVidChannel2EntityService
                    .getBindChannelNames(auxInIdList, AccAuxIn.class.getSimpleName());
                itemList.forEach(item -> {
                    item.setChannelName(channelNameMap.get(item.getId()));
                });
            }
        }
        return pager;
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accAuxInDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccAuxInItem getItemById(String id) {
        List<AccAuxInItem> items = getByCondition(new AccAuxInItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public boolean isExist(AccAuxInItem item) {
        AccAuxIn accAuxIn = accAuxInDao.findByNameAndAccDevice_Id(item.getName(), item.getDevId());
        if (accAuxIn == null) {
            return true;
        }
        return false;
    }

    @Override
    public ZKResultMsg getAuxInStatus(String sessionId) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        AccAuxInItem item = new AccAuxInItem();
        if (StringUtils.isNotBlank(userId)) {
            item.setUserId(userId);
        }
        List<AccAuxInItem> accAuxInList =
            (List<AccAuxInItem>)accAuxInDao.getItemsBySql(item.getClass(), SQLUtil.getSqlByItem(item));
        JSONArray dataArray = new JSONArray();
        if (Objects.nonNull(accAuxInList) && !accAuxInList.isEmpty()) {
            for (AccAuxInItem auxIn : accAuxInList) {
                AccQueryDeviceItem dev = accDeviceService.getQueryItemBySn(auxIn.getDevSn());
                if (dev != null) {
                    JSONObject objJson = new JSONObject();
                    objJson.put("id", auxIn.getId());
                    objJson.put("areaId", dev.getAuthAreaId());
                    objJson.put("devAlias", dev.getAlias());
                    objJson.put("devSn", dev.getSn());
                    objJson.put("no", auxIn.getAuxNo());
                    objJson.put("name", auxIn.getName());
                    objJson.put("image", "default");
                    dataArray.add(objJson);
                }
            }
        }
        return new ZKResultMsg(dataArray);
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccAuxInItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public List<AccAuxInItem> getItemsByDevIds(List<String> devIdList) {
        List<AccAuxInItem> auxInItems = new ArrayList<>();
        if (devIdList != null && devIdList.size() > 0) {
            AccAuxInItem auxInItem = new AccAuxInItem();
            auxInItem.setDevIdIn(StringUtils.join(devIdList, ","));
            auxInItems = getByCondition(auxInItem);
        }
        return auxInItems;
    }

    @Override
    public List<AccAuxInItem> getItemsByIds(List<String> auxInIdList) {
        AccAuxInItem condition = new AccAuxInItem();
        condition.setInId(StringUtils.join(auxInIdList, ","));
        return getByCondition(condition);
    }

    @Override
    public void handlerTransfer(List<AccAuxInItem> auxinItems) {
        // 按设备sn分组， key：pin value:
        Map<String, List<AccAuxInItem>> deviceOpMap =
            auxinItems.stream().collect(Collectors.groupingBy(AccAuxInItem::getDevSn));
        // 获取数据库中原有的设备参数取出，用于比较
        List<List<String>> snsList = CollectionUtil.split(deviceOpMap.keySet(), CollectionUtil.splitSize);
        Map<String, AccAuxIn> optionAllMap = new HashMap<String, AccAuxIn>();
        Map<String, AccDevice> deviceAllMap = new HashMap<String, AccDevice>();
        // 数据大时分批处理
        for (List<String> sns : snsList) {
            List<AccAuxIn> ins = accAuxInDao.findByAccDevice_SnIn(sns);
            for (AccAuxIn in : ins) {
                String key = in.getAccDevice().getSn() + "_" + in.getAuxNo();
                optionAllMap.put(key, in);
            }
            // 將所有的設備查詢出來
            List<AccDevice> devices = accDeviceDao.findBySnIn(sns);
            for (AccDevice accDevice : devices) {
                deviceAllMap.put(accDevice.getSn(), accDevice);
            }
        }
        // 检测判断数据后保存更新
        for (AccAuxInItem inItem : auxinItems) {
            AccAuxIn in = optionAllMap.remove(inItem.getDevSn() + "_" + inItem.getAuxNo());
            if (Objects.isNull(in)) {
                in = new AccAuxIn();
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(inItem, in, "id");
            in.setAccDevice(deviceAllMap.get(inItem.getDevSn()));
            // 这里的businessId就类似于编码，这个和旧架构的id是一样的 时间段
            if (StringUtils.isNotBlank(inItem.getAccTimeSegId())) {
                AccTimeSeg timeSeg = accTimeSegDao.findByBusinessId(Long.parseLong(inItem.getAccTimeSegId()));
                in.setTimeSegId(timeSeg.getId());
            }
            accAuxInDao.save(in);
        }
        optionAllMap = null;
        deviceAllMap = null;
    }

    @Override
    public boolean checkTimeSegUsed(String timeSegId) {
        return accAuxInDao.countByTimeSegId(timeSegId) > 0;
    }

    @Override
    public void saveAuxInList(AccDeviceItem accDeviceItem, List<AccAuxInItem> accAuxInList) {
        List<AccAuxIn> accAuxIns = new ArrayList<>();
        AccDevice accDevice = accDeviceDao.findOne(accDeviceItem.getId());
        for (AccAuxInItem item : accAuxInList) {
            AccAuxIn auxIn = ModelUtil.copyProperties(item, new AccAuxIn());
            auxIn.setTimeSegId(item.getAccTimeSegId());
            if (null != accDevice) {
                auxIn.setAccDevice(accDevice);
            }
            accAuxIns.add(auxIn);
        }
        if (!accAuxIns.isEmpty()) {
            accAuxInDao.save(accAuxIns);
        }
    }

    @Override
    public Pager getSimpleItemsByPage(BaseItem condition, int page, int size) {
        return accAuxInDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public AccAuxInItem getItemByAuxNoAndDevId(short auxNo, String devId) {
        AccAuxIn accAuxIn = accAuxInDao.findByAuxNoAndAccDevice_Id(auxNo, devId);
        AccAuxInItem accAuxInItem = null;
        if (Objects.nonNull(accAuxIn)) {
            accAuxInItem = ModelUtil.copyProperties(accAuxIn, new AccAuxInItem());
            AccDevice accDevice = accAuxIn.getAccDevice();
            accAuxInItem.setDevId(accDevice.getId());
            accAuxInItem.setDevSn(accDevice.getSn());
            accAuxInItem.setDevAlias(accDevice.getSn());
        }
        return accAuxInItem;
    }

    @Override
    public List<Short> getAuxInNoByDevId(String devId) {
        return accAuxInDao.findAuxInNoByDevId(devId);
    }

    @Override
    public AccAuxInItem saveSimpleItem(AccAuxInItem item) {
        AccAuxIn accAuxIn = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accAuxInDao.findById(id)).orElse(new AccAuxIn());
        ModelUtil.copyPropertiesIgnoreNull(item, accAuxIn);
        if (StringUtils.isNotBlank(item.getDevId())) {
            AccDevice accDevice = accDeviceDao.findById(item.getDevId()).orElse(null);
            if (Objects.nonNull(accDevice)) {
                accAuxIn.setAccDevice(accDevice);
            }
        }
        if (accDeviceOptionService.isSupportFunList(accAuxIn.getAccDevice().getSn(), 8)) {
            if (StringUtils.isNotBlank(item.getAccTimeSegId())) {
                accAuxIn.setTimeSegId(item.getAccTimeSegId());
            }
        }
        accAuxInDao.save(accAuxIn);
        item.setId(accAuxIn.getId());
        return item;
    }

    @Override
    public int countByTimeSegId(String timeSegId) {
        return accAuxInDao.countByTimeSegId(timeSegId);
    }

    @Override
    public String getAuxInSimpleName() {
        return AccAuxIn.class.getSimpleName();
    }
}