package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/7 17:25
 * @since 1.0.0
 */
@Entity
@Table(name = "ACC_TRIGGERGROUP")
@Getter
@Setter
@Accessors(chain = true)
public class AccTriggerGroup extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 业务id */
    @Column(name = "BUSSINESS_ID")
    private Long bussinessId;

    /** 触发点类型 0:门, 4:读头 */
    @Column(name = "TYPE")
    private Short type;

    /** 触发点 */
    @OneToMany(mappedBy = "accTriggerGroup", cascade = CascadeType.REMOVE)
    private Set<AccTriggerGroupAddr> addrSet = new HashSet<>();

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        AccTriggerGroup that = (AccTriggerGroup)o;
        return Objects.equals(bussinessId, that.bussinessId) && Objects.equals(type, that.type)
            && Objects.equals(addrSet, that.addrSet);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), bussinessId, type, addrSet);
    }
}
