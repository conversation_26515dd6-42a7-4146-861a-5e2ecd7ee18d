/**
 * File Name: AccCombOpenPersonServiceImpl Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.enums.AccLinkTypeEnum;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.AccCombOpenPersonService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonInfoItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonLinkService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 对应百傲瑞达 AccCombOpenPersonServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:02
 * @version v1.0
 */
@Service
@Transactional
public class AccCombOpenPersonServiceImpl implements AccCombOpenPersonService {
    @Autowired
    private AccCombOpenPersonDao accCombOpenPersonDao;
    @Autowired
    private AccCombOpenCombDao accCombOpenCombDao;
    @Autowired
    private AccPersonCombOpenPersonDao accPersonCombOpenPersonDao;
    @Autowired
    private PersPersonLinkService persPersonLinkService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccCombOpenPersonBIdDao accCombOpenPersonBIdDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccLevelPersonDao accLevelPersonDao;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired
    private AuthDepartmentService authDepartmentService;

    @Override
    public AccCombOpenPersonItem saveItem(AccCombOpenPersonItem item) {
        AccCombOpenPerson accCombOpenPerson =
            Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
                .flatMap(id -> accCombOpenPersonDao.findById(id)).orElse(new AccCombOpenPerson());
        ModelUtil.copyProperties(item, accCombOpenPerson);
        if (accCombOpenPerson.getBusinessId() == null) {
            accCombOpenPerson.setBusinessId(createBId());
        }
        accCombOpenPersonDao.save(accCombOpenPerson);
        item.setId(accCombOpenPerson.getId());
        return item;
    }

    @Override
    public List<AccCombOpenPersonItem> getByCondition(AccCombOpenPersonItem condition) {
        return (List<AccCombOpenPersonItem>)accCombOpenPersonDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accCombOpenPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<String> idList = (List<String>)CollectionUtil.strToList(ids);
            // 先从acc_combopen_comb表里面查询，如果存在记录，则不允许删除
            List<AccCombOpenComb> accCombOpenCombList = accCombOpenCombDao.findByAccCombOpenPerson_IdIn(idList);
            if (accCombOpenCombList != null && accCombOpenCombList.size() > 0) {
                throw new ZKBusinessException("acc_combOpen_combDeleteGroup");
            } else {
                idList.stream().forEach(id -> {
                    List<AccPersonCombOpenPerson> accPersonCombOpenPersonList =
                        accPersonCombOpenPersonDao.findByAccCombOpenPerson_Id(id);
                    if (accPersonCombOpenPersonList != null && accPersonCombOpenPersonList.size() > 0) {
                        accPersonCombOpenPersonDao.delete(accPersonCombOpenPersonList);
                    }
                    accCombOpenPersonDao.deleteById(id);
                });
                persPersonLinkService.deleteBatchItemByLinkId(AccLinkTypeEnum.ACC_COMBOPENPERSON, idList);
            }
        }
        return false;
    }

    @Override
    public AccCombOpenPersonItem getItemById(String id) {
        List<AccCombOpenPersonItem> items = getByCondition(new AccCombOpenPersonItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public AccCombOpenPersonItem getItemByName(String name) {
        AccCombOpenPerson accCombOpenPerson = accCombOpenPersonDao.findByName(name);
        if (accCombOpenPerson == null) {
            return null;
        } else {
            AccCombOpenPersonItem item = new AccCombOpenPersonItem();
            item.setId(accCombOpenPerson.getId());
            item.setName(accCombOpenPerson.getName());
            item.setRemark(accCombOpenPerson.getRemark());
            return item;
        }
    }

    @Override
    public ZKResultMsg getCombOpenJsonData() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        List<AccCombOpenPerson> accCombOpenPersonList = accCombOpenPersonDao.findAll();
        JSONArray jsonArray = new JSONArray();
        for (AccCombOpenPerson accCombOpenPerson : accCombOpenPersonList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("id", accCombOpenPerson.getId());
            jsonObject.put("groupName", accCombOpenPerson.getName());
            jsonObject.put("remark", accCombOpenPerson.getRemark());
            jsonArray.add(jsonObject);
        }
        zkResultMsg.setData(jsonArray);
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg checkPersonCount(String groupId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        JSONObject resultJson = new JSONObject();
        resultJson.put("personCount", 0);
        AccCombOpenPerson tempAccPerson = null;
        if (StringUtils.isNotBlank(groupId)) {
            tempAccPerson = accCombOpenPersonDao.findById(groupId).orElse(new AccCombOpenPerson());
            Set<AccPersonCombOpenPerson> accPersonCombOpenPersonSet = tempAccPerson.getAccPersonCombOpenPersonSet();
            if (accPersonCombOpenPersonSet != null && accPersonCombOpenPersonSet.size() > 0) {
                resultJson.put("personCount", accPersonCombOpenPersonSet.size());
            }
        }
        zkResultMsg.setData(resultJson);
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg addPerson(String combOpenPersonId, List<String> personIdList) {
        /*if (personIds == null)//为null表示部门上来的人
        {
        	personIds = persDepartmentBiz.getPersonByDept(deptIds, "accPerson");
        }*/
        // 因为各个组添加的人不能重复，以下注意要是为了选人控件从部门上来的人员过滤在其他组
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        AccCombOpenPerson accCombOpenPerson =
            accCombOpenPersonDao.findById(combOpenPersonId).orElse(new AccCombOpenPerson());
        Map<String, String> idMap = new HashMap<String, String>();
        personIdList.stream().forEach(personId -> {
            idMap.put(personId, "");
        });
        // 查出已经添加到多人开门的人员的id
        List<String> hasPersonIdList = accPersonCombOpenPersonDao.getPersonIdByCombPersonId();
        for (int i = 0; i < hasPersonIdList.size(); i++) {
            if (idMap.containsKey(hasPersonIdList.get(i))) {
                idMap.remove(hasPersonIdList.get(i));
            }
        }
        StringBuilder personIdStr = new StringBuilder();
        for (String id : idMap.keySet()) {
            personIdStr.append(id).append(",");
        }
        // -------------过滤end--------------

        if (StringUtils.isNotBlank(personIdStr)) {
            // 加人员到多人开门人员组
            if (accCombOpenPerson != null) {
                List<String> personIdStrToList = (List<String>)CollectionUtil.strToList(personIdStr.toString());
                List<AccPersonCombOpenPerson> accPersonCombOpenPersonList =
                    new ArrayList<AccPersonCombOpenPerson>(personIdStrToList.size());
                List<List<String>> personIdsList =
                    CollectionUtil.split(personIdStrToList, AccConstants.LEVEL_SPLIT_COUNT);
                personIdsList.forEach(personIds -> {
                    personIds.forEach(personId -> {
                        AccPersonCombOpenPerson accPersonCombOpenPerson = new AccPersonCombOpenPerson();
                        accPersonCombOpenPerson.setPersPersonId(personId);
                        accPersonCombOpenPerson.setAccCombOpenPerson(accCombOpenPerson);
                        accPersonCombOpenPersonList.add(accPersonCombOpenPerson);
                    });
                });
                accPersonCombOpenPersonDao.saveAll(accPersonCombOpenPersonList);
                // List<String> personIdStrToStrList = getPersonPropertyList(personIdStrToList);
                // 分批处理数据，解决oracle数据库下参数不能操过1000的问题
                /*for(String partPersonIds : personIdStrToStrList)
                {
                	accPersonDao.setPersonByPersonIdScope(accCombOpenPerson.getId(), partPersonIds);
                }*/
                // select count(*) from AccPerson e join e.accCombOpenPerson c where e.person.status=%s and c.id=%s and
                // e.person.personType =%s
                // openPersonNum = accPersonDao.getPersonCount(accCombOpenPerson.getId());
                // 下发命令更新人员信息
                List<AccCombOpenComb> accCombOpenCombList =
                    accCombOpenCombDao.findByAccCombOpenPerson_Id(accCombOpenPerson.getId());
                if (accCombOpenCombList != null && accCombOpenCombList.size() > 0) {
                    List<String> devIdList = new ArrayList<>();// 使用多人开门组的设备
                    for (AccCombOpenComb combOpenComb : accCombOpenCombList) {
                        devIdList.add(combOpenComb.getAccCombOpenDoor().getAccDoor().getDevice().getId());
                    }
                    // 更新设备中的多人开门人员信息
                    updatePersonToDev(devIdList, personIdStr.toString());
                    devIdList = null;
                    personIdStr = null;
                }
            }
            hasPersonIdList = null;
        }
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg delPerson(String combOpenId, String personIds) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        if (StringUtils.isNotBlank(combOpenId) && StringUtils.isNotBlank(personIds)) {
            // 删除人员时，必须考虑开门组中每个开门人员组最少的开门人数 --- zhangc 14-05-21
            List<String> combOpenIdList = (List<String>)CollectionUtil.strToList(combOpenId);
            List<String> personIdList = (List<String>)CollectionUtil.strToList(personIds);
            List<AccCombOpenComb> accCombOpenCombList = accCombOpenCombDao.findByAccCombOpenPerson_IdIn(combOpenIdList);
            if (accCombOpenCombList != null && accCombOpenCombList.size() > 0) {
                // 存在开门组,才去查询人员除去要删除人的剩余人数
                Integer openPersonNum = accPersonCombOpenPersonDao
                    .countByAccCombOpenPerson_IdAndPersPersonIdNotIn(combOpenId, personIdList);
                for (AccCombOpenComb combOpenComb : accCombOpenCombList) {
                    if (combOpenComb.getOpenerNumber() > openPersonNum) {
                        // 开门组中开门人员组人数不够时，提示用户不让删除
                        throw new ZKBusinessException("acc_combOpen_combOpengGroupPersonShort");
                    }
                }
            }
            // 删除数据库中多人开门人员组 的人员
            // update AccPerson e set e.accCombOpenPerson.id = NULL where e.id in (%s)
            accPersonCombOpenPersonDao.delPersonNullByPersonIdScope(personIdList);

            // 更新设备中的多人开门人员信息
            delAccCombOpenPerson(personIds);
        }
        return zkResultMsg;
    }

    @Override
    public List<String> getExistPersonIds() {
        return accPersonCombOpenPersonDao.getPersonIdByCombPersonId();
    }

    @Override
    public ZKResultMsg getPersonCount(String sessionId, String accCombOpenPersonId) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        SecuritySubject subject = authSessionProvider.getSecuritySubject(sessionId);
        if (!subject.getIsSuperuser() && subject.getDepartmentIds() != null && subject.getDepartmentIds().size() > 0) {
            List<PersPersonItem> persPersonItemList =
                persPersonService.getPersPersonByDeptIds(subject.getDepartmentIds());
            zkResultMsg.setData(accPersonCombOpenPersonDao.countByAccCombOpenPerson_IdAndPersPersonIdIn(
                accCombOpenPersonId, CollectionUtil.getItemIdsList(persPersonItemList)));
            return zkResultMsg;
        }
        zkResultMsg.setData(accPersonCombOpenPersonDao.countByAccCombOpenPerson_Id(accCombOpenPersonId));
        return zkResultMsg;
    }

    @Override
    public void handlerTransfer(List<AccCombOpenPersonItem> accCombOpenPersonItems) {
        // 数据量大的时候处理，分批处理
        List<List<AccCombOpenPersonItem>> accCombOpenPersonItemList =
            CollectionUtil.split(accCombOpenPersonItems, CollectionUtil.splitSize);
        for (List<AccCombOpenPersonItem> accCombOpenPersonItems1 : accCombOpenPersonItemList) {
            // 获取已存在的记录根据名称获取
            Collection<String> names =
                CollectionUtil.getPropertyList(accCombOpenPersonItems1, AccCombOpenPersonItem::getName, "-1");
            List<AccCombOpenPerson> accCombOpenPersonList = accCombOpenPersonDao.findByNameIn(names);
            Map<String, AccCombOpenPerson> accCombOpenPersonMap =
                CollectionUtil.listToKeyMap(accCombOpenPersonList, AccCombOpenPerson::getName);
            for (AccCombOpenPersonItem accCombOpenPersonItem : accCombOpenPersonItems1) {
                AccCombOpenPerson accCombOpenPerson = accCombOpenPersonMap.remove(accCombOpenPersonItem.getName());
                if (Objects.isNull(accCombOpenPerson)) {
                    accCombOpenPerson = new AccCombOpenPerson();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accCombOpenPersonItem, accCombOpenPerson, "id");
                accCombOpenPerson.setBusinessId(Long.valueOf(accCombOpenPersonItem.getId()));
                accCombOpenPersonDao.save(accCombOpenPerson);
            }
        }
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccCombOpenPersonItem condition, int pageNo, int pageSize) {
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public List<SelectItem> getCombOpenList() {
        List<AccCombOpenPerson> accCombOpenPersonList = accCombOpenPersonDao.findAll();
        List<SelectItem> selectItems = new ArrayList<>();
        SelectItem selectItem = null;
        for (AccCombOpenPerson combOpenPerson : accCombOpenPersonList) {
            selectItem = new SelectItem();
            selectItem.setValue(combOpenPerson.getId());
            selectItem.setText(combOpenPerson.getName());
            selectItems.add(selectItem);
        }
        return selectItems;
    }

    private Long createBId() {
        AccCombOpenPersonBId accCombOpenPersonBId = new AccCombOpenPersonBId();
        accCombOpenPersonBId = accCombOpenPersonBIdDao.save(accCombOpenPersonBId);
        return accCombOpenPersonBId.getId();
    }

    /**
     * 删除多人开门组人员并更新到设备
     * 
     * @author: mingfa.zheng
     * @date: 2018/5/22 21:12
     * @return:
     */
    private void delAccCombOpenPerson(String personIds) {
        List<String> personIdList = (List<String>)CollectionUtil.strToList(personIds);
        AccPersonInfoItem accPersonInfoItem = accLevelService.getPersonByIds(personIdList);
        List<String> levelIdList = accLevelPersonDao.getLevelIdByPersonIdIn(personIdList);
        if (levelIdList != null && levelIdList.size() > 0) {
            List<AccDevice> devList = accDeviceDao.getDevByLevel(levelIdList);
            if (devList != null && devList.size() > 0) {
                // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
                AccDeviceUtil.moveUpParentDevList(devList);
                devList.stream().forEach(accDevice -> {
                    if (accPersonInfoItem.getPersonList() != null && accPersonInfoItem.getPersonList().size() > 0) {
                        accDevCmdManager.setPersonToDev(accDevice.getSn(), accPersonInfoItem.getPersonList(), false);// 下发人员信息
                    }
                });
            }
        }
    }

    /**
     *
     * 更新人员到设备
     *
     * <AUTHOR> href="<EMAIL>">linzj</a>
     * @since 2015年6月9日 下午2:03:12
     * @param devIdList
     * @param personIds
     */
    private void updatePersonToDev(List<String> devIdList, String personIds) {
        if (devIdList.size() > 0) {
            AccPersonInfoItem personInfoItem =
                accLevelService.getPersonByIds((List<String>)CollectionUtil.strToList(personIds));
            List<AccDevice> devList = accDeviceDao.findByIdIn(devIdList);
            // 优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了 by juvenile.li add 20171114
            AccDeviceUtil.moveUpParentDevList(devList);

            for (AccDevice dev : devList) {
                accDevCmdManager.setPersonToDev(dev.getSn(), personInfoItem.getPersonList(), false);
            }
        }
    }
}