/**
 * File Name: AccDevice Created by GenerationTools on 2018-03-08 下午02:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccDeviceDao
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-08 下午02:41
 */
public interface AccDeviceDao extends BaseDao<AccDevice, String> {

    List<AccDevice> findByMachineTypeIn(List<Short> machineType);

    AccDevice findBySn(String sn);

    /**
     * 查询权限组下的设备
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/26 11:28
     */
    @Query(
        value = "select distinct dev from AccLevel e join e.accDoorList l join l.accDoor d join d.device dev where e.id in (?1)")
    List<AccDevice> getDevByLevel(List<String> levelIds);

    @Query(value = "select d from AccDevice d where d.commType=?1")
    List<AccDevice> getDevByTypeAndAuth(short commType);

    List<AccDevice> findByIdIn(List<String> idList);

    @Query(
        value = "select e from AccDevice e where e.wgReaderId in (select r.id from AccReader r where r.accDoor.device.id = ?1)")
    List<AccDevice> getDevAsWGReaderByDevId(String id);

    @Query(value = "select id from AccDevice where machineType >= 101 ")
    List<String> getAccessControlIds();

    @Query(value = "select id from AccDevice where machineType < 101 ")
    List<String> getControlIds();

    @Query(
        value = "SELECT COUNT(id) FROM AccDoor d WHERE d.device.commType in (?1) AND d.enabled = true AND d.device.wgReaderId is null")
    long getDoorCountByCommType(List<Short> commTypeList);

    @Query(
        value = "SELECT COUNT(id) FROM AccDevice e WHERE e.commType in (?1) and e.machineType <> 40 and e.wgReaderId is null")
    long getDevCountByCommType(List<Short> commTypeList);

    /**
     * 查询权限组下的设备
     *
     * @author: mingfa.zheng
     * @date: 2018/4/26 14:27
     * @return:
     */
    @Query(
        value = "select distinct dev from AccLevelDoor ald join ald.accDoor ad join ad.device dev where ald.accLevel.id in (?1)")
    List<AccDevice> getDevByLevel(String levelId);

    @Query(value = "select e from AccDevice e where e.id <> ?1 and e.ipAddress = ?2")
    AccDevice getAccDeviceByIpAddressAndDevId(String devId, String ipAddress);

    @Query(
        value = "select e from AccDevice e where e.wgReaderId in (select r.id from AccReader r where r.accDoor.device.id in (?1))")
    List<AccDevice> getAsWGReaderDevByDevId(List<String> devIds);

    AccDevice findByAlias(String alias);

    AccDevice findByIpAddress(String ipAddress);

    List<AccDevice> findByAccDSTime_IdIn(List<String> dsTimeIds);

    Long countByCommTypeIn(List<Short> commTypes);

    /**
     * @Description: 根据区域id查询该区域下是否有设备
     * @author: mingfa.zheng
     * @date: 2018/7/9 16:58
     * @param: [areaId]
     * @return: java.lang.Long
     **/
    Long countByAuthAreaId(String areaId);

    List<AccDevice> findBySnIn(List<String> sns);

    @Query(value = "select count(e.id) from AccDevice e where e.machineType in (1, 2, 4) and e.commType <> 3")
    Long getC3DevCount();

    @Query(
        value = "select count(e.id) from AccDevice e join e.accDeviceOptionList o where e.machineType in (1, 2, 4) and e.commType <> 3 and o.name = 'DCPMachine' and o.value = '1'")
    Long getK2DevCount();

    @Query(value = "select e.id from AccDevice e where e.commType in (?1)")
    List<String> findDevIdByCommTypeIn(List<Short> commTypeList);

    /**
     * 根据businessId获取设备
     *
     * @param businessId
     * @return
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:38:24
     */
    List<AccDevice> findByBusinessIdIn(Collection<Long> businessId);

    /**
     * 功能描述:根据读头id查找设备sn
     *
     * @return
     * <AUTHOR>
     * @Param [accReaderId]
     * @since 2019-04-16 9:42
     */
    @Query(value = "select dev.sn from AccReader ar join ar.accDoor ad join ad.device dev where ar.id =?1")
    String getDevSnByReaderId(String accReaderId);

    /**
     * 根据人员id查找出人员所在权限组下的所有设备
     *
     * @return
     * <AUTHOR>
     * @Param [personId]
     * @since 2019-07-02 18:02
     */
    @Query(
        value = "select distinct d from AccDevice d join d.accDoorList dl join dl.accLevelList al join al.accLevel l join l.accPersonList apl where apl.persPersonId = ?1")
    List<AccDevice> findByPersonId(String personId);

    @Query(value = "select d.id from AccDevice d where d.wgReaderId is not null")
    List<String> getDevIdsAsWGReader();

    /**
     * 根据门id查询设备
     *
     * @param doorIds:门id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccDevice>
     * <AUTHOR>
     * @date 2021-01-25 15:17
     * @since 1.0.0
     */
    @Query(value = "select distinct dev from AccDoor d join d.device dev where d.id in (?1)")
    List<AccDevice> getDevByDoorIds(List<String> doorIds);

    /**
     * 根据门id查询设备
     *
     * @param doorId:门id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccDevice>
     * <AUTHOR>
     * @date 2021-01-25 15:17
     * @since 1.0.0
     */
    @Query(value = "select dev from AccDoor d join d.device dev where d.id =?1")
    AccDevice getDeviceByDoorId(String doorId);

    /**
     * 根据设备序列号查询父设备信息
     *
     * @param devSn:设备序列号
     * @return com.zkteco.zkbiosecurity.acc.model.AccDevice
     * <AUTHOR>
     * @date 2021-01-27 14:07
     * @since 1.0.0
     */
    @Query(value = "select e.parentDevice from AccDevice e where e.sn =?1")
    AccDevice getParentDeviceByDevSn(String devSn);

    /**
     * 根据读头id查询设备
     *
     * @param readerIds:读头id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccDevice>
     * <AUTHOR>
     * @date 2021-02-07 15:41
     * @since 1.0.0
     */
    @Query(value = "select distinct dev from AccReader ar join ar.accDoor ad join ad.device dev where ar.id in (?1)")
    List<AccDevice> getDevByReaderIds(List<String> readerIds);

    /**
     * 获取所有sn
     *
     * @return java.util.List<java.lang.String>
     * @throws
     * <AUTHOR>
     * @date 2023-04-23 15:11
     * @since 1.0.0
     */
    @Query(value = "select distinct dev.sn from AccDevice dev where 1=1")
    List<String> getAllSn();

    @Query(value = "select distinct dev.sn from AccDevice dev where dev.id in (?1)")
    List<String> getSnByIdIn(List<String> ids);

    Integer countBySnIn(List<String> devSn);

    @Query(
        value = "select distinct dev from AccLevelDoor ald join ald.accDoor ad join ad.device dev where ald.accLevel.id in (?1)")
    List<AccDevice> getDevByLevelIdIn(List<String> levelIds);
}