package com.zkteco.zkbiosecurity.acc.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.service.AccDSTimeService;
import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class AccVer3_3_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private AccDSTimeService accDSTimeService;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public String getVersion() {
        return "v3.3.0";
    }

    @Override
    public boolean executeUpgrade() {
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("AccExtDevice");
        if (subMenuItem == null) {
            AuthPermissionItem topMenuItem = authPermissionService.getItemByCode("AccDeviceManager");
            if (topMenuItem != null) {
                // 扩展板菜单
                subMenuItem = new AuthPermissionItem("AccExtDevice", "acc_leftMenu_extDev", "acc:extDevice",
                    AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
                subMenuItem.setParentId(topMenuItem.getId());
                subMenuItem.setActionLink("accExtDevice.do");
                subMenuItem = authPermissionService.initData(subMenuItem);
                // 扩展板菜单-刷新
                AuthPermissionItem subButtonItem = new AuthPermissionItem("AccExtDeviceRefresh", "common_op_refresh",
                    "acc:extDevice:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
                // 扩展板菜单-新增
                subButtonItem = new AuthPermissionItem("AccExtDeviceAdd", "common_op_new", "acc:extDevice:add",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
                // 扩展板菜单-编辑
                subButtonItem = new AuthPermissionItem("AccExtDeviceEdit", "common_op_edit", "acc:extDevice:edit",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
                // 扩展板菜单-删除
                subButtonItem = new AuthPermissionItem("AccExtDeviceDel", "common_op_del", "acc:extDevice:del",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
            }
        }
        return true;
    }
}
