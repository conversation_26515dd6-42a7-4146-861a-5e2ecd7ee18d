package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.dao.AccMapPosDao;
import com.zkteco.zkbiosecurity.acc.dao.AccPersonLastAddrDao;
import com.zkteco.zkbiosecurity.acc.model.AccMapPos;
import com.zkteco.zkbiosecurity.acc.model.AccPersonLastAddr;
import com.zkteco.zkbiosecurity.acc.service.Acc4VisVisitorLastAddr;
import com.zkteco.zkbiosecurity.acc.service.AccPersonLastAddrService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4VisVisitorLastAddrItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLastAddrItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthSessionServcie;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SecuritySubject;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;

@Service
@Transactional
public class AccPersonLastAddrServiceImpl implements AccPersonLastAddrService {

    @Autowired
    private AccPersonLastAddrDao accPersonLastAddrDao;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccMapPosDao accMapPosDao;
    @Autowired
    private AuthSessionServcie authSessionServcie;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AuthSessionProvider authSessionProvider;
    @Autowired(required = false)
    private Acc4VisVisitorLastAddr acc4VisVisitorLastAddr;

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accPersonLastAddrDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accPersonLastAddrDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public List<AccPersonLastAddrItem> getByCondition(AccPersonLastAddrItem condition) {
        return (List<AccPersonLastAddrItem>)accPersonLastAddrDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public void deleteAllData() {
        accPersonLastAddrDao.truncateAll();
    }

    @Override
    public List<AccPersonLastAddrItem> getItemData(Class<AccPersonLastAddrItem> accPersonLastAddrItemClass,
        BaseItem condition, int beginIndex, int endIndex) {
        return accPersonLastAddrDao.getItemsDataBySql(accPersonLastAddrItemClass, SQLUtil.getSqlByItem(condition),
            beginIndex, endIndex, true);
    }

    @Override
    public void saveAccPersonLastAddrHandle(AccTransactionItem accTransactionItem) {
        String description = null;
        AccPersonLastAddr accPersonLastAddrDb = accPersonLastAddrDao.findByPin(accTransactionItem.getPin());// 按照事件时间倒序取第一个
        if (accPersonLastAddrDb == null)// 数据表中如果已经存在事件时间比当前事件时间大的不做数据库操作
        {
            accPersonLastAddrDb = new AccPersonLastAddr();
        }
        if (Objects.isNull(accPersonLastAddrDb.getEventTime())
            || accPersonLastAddrDb.getEventTime().compareTo(accTransactionItem.getEventTime()) < 0) {
            ModelUtil.copyPropertiesIgnoreNullWithProperties(accTransactionItem, accPersonLastAddrDb, "description",
                "id");
            description = accTransactionItem.getDescription();
            if (StringUtils.isNotBlank(description) && description.length() > 100)// 考虑中文
            {
                description = description.substring(0, 96) + "...";
            }
            accPersonLastAddrDb.setDescription(description); // 备注
            accPersonLastAddrDao.save(accPersonLastAddrDb);
            if (Objects.nonNull(acc4VisVisitorLastAddr) && accPersonLastAddrDb.getPin().startsWith("8")
                && accPersonLastAddrDb.getPin().length() == 9) { // 根据pin判断是否是访客人员，是则保存保存访客最后访问位置
                Acc4VisVisitorLastAddrItem visitorLastAddrItem = new Acc4VisVisitorLastAddrItem();
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accPersonLastAddrDb, visitorLastAddrItem, "id");
                visitorLastAddrItem.setReaderId(accTransactionItem.getReaderId());
                acc4VisVisitorLastAddr.saveVisLastAddr(visitorLastAddrItem);
            }
        }
    }

    private void saveVisVisitorLastAddr(AccPersonLastAddr accPersonLastAddrDb) {
        if (Objects.nonNull(acc4VisVisitorLastAddr) && accPersonLastAddrDb.getPin().startsWith("8")
            && accPersonLastAddrDb.getPin().length() == 9) { // 根据pin判断是否是访客人员，是则保存保存访客最后访问位置
            Acc4VisVisitorLastAddrItem visitorLastAddrItem = new Acc4VisVisitorLastAddrItem();
            ModelUtil.copyPropertiesIgnoreNullWithProperties(accPersonLastAddrDb, visitorLastAddrItem, "id");
            acc4VisVisitorLastAddr.saveVisLastAddr(visitorLastAddrItem);
        }
    }

    @Override
    public Map<String, Object> getPersonLastAddrByPin(String pin, String sessionId) {
        Map<String, Object> retMap = new HashMap<>();
        AccMapPos accMapPos = accMapPosDao.getByPersonLastAddrPin(pin);
        SecuritySubject securitySubject = null;
        if (StringUtils.isNotBlank(sessionId)) {
            securitySubject = authSessionServcie.getSecuritySubject(sessionId);
        }
        if (securitySubject != null && securitySubject.getIsSuperuser())// 当前登录用户为超级用户
        {
            retMap.put("isSuperUser", true);

        } else// 普通用户
        {
            retMap.put("isSuperUser", false);
            retMap.put("isAccMapAccess", false);
            // 判断是否有电子地图的权限
            if (securitySubject != null && securitySubject.hasPermission("acc:map:")) {
                retMap.put("isAccMapAccess", true);
                retMap.put("isAreaAccess", false);
                // 判断登录用户是否有该人员最后访问位置的区域权限。
                String areaIds = authSessionProvider.getAreaIdsNoSubNodesByAuthFilter(sessionId);
                if (StringUtils.isNotBlank(areaIds)) {
                    List<String> areaIdList = StrUtil.strToList(areaIds);
                    for (String areaId : areaIdList) {
                        if (accMapPos.getAccMap().getAuthAreaId().equals(areaId)) {
                            retMap.put("isAreaAccess", true);
                        }
                    }
                }
            }
        }

        if (accMapPos != null) {
            retMap.put("accDoorId", accMapPos.getEntityId());
            retMap.put("accMapId", accMapPos.getAccMap().getId());
            retMap.put("accMapPosId", accMapPos.getId());
        }

        return retMap;
    }

    @Override
    public void handlerTransfer(List<AccPersonLastAddrItem> accPersonLastAddrItems) {
        // 以防大数据时进行分批数据
        List<List<AccPersonLastAddrItem>> accTransactionItemList =
            CollectionUtil.split(accPersonLastAddrItems, CollectionUtil.splitSize);
        for (List<AccPersonLastAddrItem> addrItems : accTransactionItemList) {
            Collection<String> pins = CollectionUtil.getPropertyList(addrItems, AccPersonLastAddrItem::getPin, "-1");
            List<AccPersonLastAddr> accPersonLastAddrs = accPersonLastAddrDao.findByPinIn(pins);
            Map<String, AccPersonLastAddr> accPersonLastAddrMap =
                CollectionUtil.listToKeyMap(accPersonLastAddrs, AccPersonLastAddr::getPin);
            for (AccPersonLastAddrItem accPersonLastAddrItem : addrItems) {
                String description = null;
                // 按照事件时间倒序取第一个
                AccPersonLastAddr accPersonLastAddrDb = accPersonLastAddrMap.get(accPersonLastAddrItem.getPin());
                // 数据表中如果已经存在事件时间比当前事件时间大的不做数据库操作
                if (accPersonLastAddrDb == null) {
                    accPersonLastAddrDb = new AccPersonLastAddr();
                }
                if (Objects.isNull(accPersonLastAddrDb.getEventTime())
                    || accPersonLastAddrDb.getEventTime().compareTo(accPersonLastAddrItem.getEventTime()) < 0) {
                    ModelUtil.copyPropertiesIgnoreNullWithProperties(accPersonLastAddrItem, accPersonLastAddrDb,
                        "description", "id");
                    description = accPersonLastAddrItem.getDescription();
                    // 考虑中文
                    if (StringUtils.isNotBlank(description) && description.length() > 100) {
                        description = description.substring(0, 96) + "...";
                    }
                    // 备注
                    accPersonLastAddrDb.setDescription(description);
                    accPersonLastAddrDao.save(accPersonLastAddrDb);
                    saveVisVisitorLastAddr(accPersonLastAddrDb);
                }
            }
        }
    }

}
