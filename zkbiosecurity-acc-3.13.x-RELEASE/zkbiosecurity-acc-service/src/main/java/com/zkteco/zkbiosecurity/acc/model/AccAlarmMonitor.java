package com.zkteco.zkbiosecurity.acc.model;

import java.util.Date;
import java.util.List;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date Created In 17:44 2019/10/17
 */
@Entity
@Table(name = "ACC_ALARM_MONITOR")
@Getter
@Setter
@Accessors(chain = true)
public class AccAlarmMonitor extends BaseModel {

    private static final long serialVersionUID = 1L;

    /**
     * 事件发生时间
     */
    @Column(name = "EVENT_TIME")
    private Date eventTime;

    /**
     * 设备别名
     */
    @Column(name = "DEV_ALIAS")
    private String devAlias;

    /**
     * 事件发生点
     */
    @Column(name = "EVENT_POINT_NAME")
    private String eventPointName;

    /**
     * 事件名
     */
    @Column(name = "EVENT_NAME")
    private String eventName;

    /**
     * 人员名
     */
    @Column(name = "NAME")
    private String name;

    /**
     * 人员编号
     */
    @Column(name = "PIN")
    private String pin;

    /**
     * 读头名称
     */
    @Column(name = "READER_NAME")
    private String readerName;

    /**
     * 事件状态 0未确认 1确认
     */
    @Column(name = "STATUS")
    private short status;

    /** 事件编号 */
    @Column(name = "EVENT_NUM")
    private Long eventNum;

    /**
     * 优先级 0-3
     */
    @Column(name = "PRIORITY")
    private Short priority;

    /**
     * 门所属区域
     */
    @Column(name = "AREA_NAME")
    private String areaName;

    /** 设备序列号 */
    @Column(name = "DEV_SN", length = 30)
    private String devSn;

    /**
     * 处理记录
     */
    @OneToMany(mappedBy = "accAlarmMonitor", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private List<AccAlarmMonitorHistory> accAlarmMonitorHistoryList;

    @Override
    public String toString() {
        return "AccAlarmMonitor{" + "id='" + id + '\'' + ", eventNum=" + eventNum + ", priority=" + priority
            + ", eventTime=" + eventTime + ", area='" + areaName + '\'' + ", devAlias='" + devAlias + ", devSn='"
            + devSn + ", eventPointName='" + eventPointName + '\'' + ", eventName='" + eventName + '\'' + ", name='"
            + name + '\'' + ", pin='" + pin + '\'' + ", readerName='" + readerName + '\'' + ", status=" + status
            + ", createTime=" + createTime + '}';
    }

}
