package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.service.AccAlarmMonitorCacheListService;
import com.zkteco.zkbiosecurity.acc.service.AccAlarmMonitorService;
import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem;
import com.zkteco.zkbiosecurity.redis.configuration.RedisKeyExpireCallback;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;

import lombok.extern.slf4j.Slf4j;

/**
 * 报警事件缓存列表
 *
 * <AUTHOR>
 * @date 2021-12-24 15:01
 * @since 1.0.0
 */
@Service
@Slf4j
public class AccAlarmMonitorCacheListServiceImpl
    implements AccAlarmMonitorCacheListService, CommandLineRunner, RedisKeyExpireCallback {

    /** 缓存多少条数据 */
    private static final Integer ALARM_MONITOR_CACHE_LENGTH = 200;
    /** 需要展示的列表长度 */
    private static final Integer ALARM_MONITOR_SHOW_LENGTH = 100;
    /** 分布式线程锁 */
    private static final String ALARM_MONITOR_LIST_LOCK = "alarmMonitorListLock";
    /** 桶令牌key */
    private static final String RATE_LIMITER_KEY = "alarmMonitorListRateLimiter";
    /** 每秒令牌数量 */
    private static final int RATE_LIMITER_MAX = 1;
    /** 存储监控列表未推送的改动的标记 */
    private static final String ALARM_MONITOR_CHANGE_FLAG = "alarmMonitorListChangeFlag";
    /** 存储监控列表未推送的改动的值 */
    private static final String ALARM_MONITOR_CHANGE_VALUE = "alarmMonitorListChangeValue";
    /** 分布式线程唯一值 */
    private static final ThreadLocal<String> threadUuid = new ThreadLocal<>();

    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccAlarmMonitorService accAlarmMonitorService;
    /** 分布式锁使用的redisTemplate */
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Integer> redisTemplate;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Boolean> booleanRedisTemplate;
    @Autowired
    private ScheduleService scheduleService;

    @Override
    public void run(String... args) throws Exception {
        // 每秒执行一次，补充相应令牌
        String cron = "* * * * * ?";
        scheduleService.startScheduleTask(this::supplementRateLimiter, cron);
    }

    @Override
    public void add(AccAlarmMonitorItem accAlarmMonitorItem) {
        getLock();
        try {
            accCacheManager.addAlarmToCache(accAlarmMonitorItem);
            // 如果超过最大长度，移除最早的。
            final Long size = accCacheManager.countAlarmCacheList();
            if (size != null && size > ALARM_MONITOR_CACHE_LENGTH) {
                final boolean isRemove = accCacheManager.removeFirstAlarmFromCache();
                // 标记前面还有
                if (isRemove) {
                    accCacheManager.markAlarmHasMore(true);
                }
            }
        } finally {
            releaseLock();
        }
    }

    @Override
    public void remove(String id) {
        getLock();
        try {
            // 1. 根据id删除
            accCacheManager.delAlarmFromCache(id);
            // 2. 判断是否有数据可以补充数据
            final boolean alarmHasMore = accCacheManager.getAlarmHasMore();
            if (alarmHasMore) {
                final Long size = accCacheManager.countAlarmCacheList();
                // 判断是否需要补充数据，少于需要展示的数据时就要补充
                if (size < ALARM_MONITOR_SHOW_LENGTH) {
                    int appendSize = ALARM_MONITOR_CACHE_LENGTH - size.intValue();
                    // 取出最早的事件id
                    String lastId = accCacheManager.getFirstAlarmListId();
                    // 查找改id之前的事件 + 和该id产生时间相同的事件。 直接查找该事件之前的可能会遗漏相同时间产生的事件
                    final List<AccAlarmMonitorItem> alarmMonitorItems =
                        accAlarmMonitorService.getUncheckAlarmListBefore(lastId, appendSize);
                    if (alarmMonitorItems.size() <= 1) {
                        // 前面没事件了
                        accCacheManager.markAlarmHasMore(false);
                    } else {
                        final Long beforeSize = accCacheManager.countAlarmCacheList();
                        for (int i = 0; i < alarmMonitorItems.size(); i++) {
                            accCacheManager.addAlarmToCache(alarmMonitorItems.get(i));
                        }
                        final Long afterSize = accCacheManager.countAlarmCacheList();
                        if (beforeSize.equals(afterSize)) {
                            accCacheManager.markAlarmHasMore(false);
                        }
                    }
                }
            }
        } finally {
            releaseLock();
        }
    }

    @Override
    public List<AccAlarmMonitorItem> getMonitorList() {
        return accCacheManager.getAlarmFromCache(ALARM_MONITOR_SHOW_LENGTH);
    }

    @Override
    public boolean acquire() {
        Integer count = redisTemplate.opsForValue().get(RATE_LIMITER_KEY);
        if (count != null && count > 0) {
            Long current = redisTemplate.opsForValue().decrement(RATE_LIMITER_KEY);
            if (current != null && current + 1 >= 0) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void markMonitorListChange(boolean change) {
        booleanRedisTemplate.opsForValue().set(ALARM_MONITOR_CHANGE_FLAG, change, 1, TimeUnit.SECONDS);
        booleanRedisTemplate.opsForValue().set(ALARM_MONITOR_CHANGE_VALUE, change, 2, TimeUnit.SECONDS);
    }

    @Override
    public void delMonitorListChangeMark() {
        booleanRedisTemplate.delete(ALARM_MONITOR_CHANGE_FLAG);
        booleanRedisTemplate.delete(ALARM_MONITOR_CHANGE_VALUE);
    }

    @Override
    public void keyExireNotify(String key) {
        if (ALARM_MONITOR_CHANGE_FLAG.equals(key)) {
            final Boolean isNewMess = booleanRedisTemplate.opsForValue().get(ALARM_MONITOR_CHANGE_VALUE);
            if (isNewMess != null) {
                accAlarmMonitorService.sendAllAlarmMonitorWS(isNewMess);
            }
        }
    }

    private void supplementRateLimiter() {
        redisTemplate.opsForValue().set(RATE_LIMITER_KEY, RATE_LIMITER_MAX);
    }

    private void getLock() {
        String uuid = UUID.randomUUID().toString();
        // 设置当前线程的uuid
        threadUuid.set(uuid);
        while (!tryLock()) {
            try {
                TimeUnit.MILLISECONDS.sleep(100 + (int)(Math.random() * 100));
            } catch (InterruptedException e) {
                log.error("AccAlarmMonitor cache list sleep error: ", e);
            }
        }
    }

    private boolean tryLock() {
        final String uuid = threadUuid.get();
        if (StringUtils.isNotBlank(uuid)) {
            // 没有锁的时候
            if (Boolean.FALSE.equals(stringRedisTemplate.hasKey(ALARM_MONITOR_LIST_LOCK))) {
                // 尝试竞争锁
                stringRedisTemplate.opsForValue().setIfAbsent(ALARM_MONITOR_LIST_LOCK, uuid, 3, TimeUnit.SECONDS);
                // 是否竞争到了
                if (uuid.equals(stringRedisTemplate.opsForValue().get(ALARM_MONITOR_LIST_LOCK))) {
                    return true;
                }
            }
        }
        return false;
    }

    private void releaseLock() {
        final String uuid = threadUuid.get();
        if (uuid.equals(stringRedisTemplate.opsForValue().get(ALARM_MONITOR_LIST_LOCK))) {
            stringRedisTemplate.delete(ALARM_MONITOR_LIST_LOCK);
        }
    }
}
