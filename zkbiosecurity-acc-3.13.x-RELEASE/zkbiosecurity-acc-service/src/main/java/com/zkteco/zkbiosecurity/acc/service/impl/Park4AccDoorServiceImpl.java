package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.park.service.Park4AccDoorService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;

/***
 * 门禁当停车场远程开闸接口实现
 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
 * @date 2019/1/8 17:50
 */
@Service
public class Park4AccDoorServiceImpl implements Park4AccDoorService {

    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorDao accDoorDao;

    @Override
    public String openDoorForPark(int openInterval, String accDoorId) {
        String ret = "";
        if (openInterval > 0 && StringUtils.isNotBlank(accDoorId)) {
            AccDoor accDoor = accDoorDao.findById(accDoorId).orElse(null);
            if (Objects.nonNull(accDoor)) {
                Long cmdId = accDevCmdManager.ctrlDoor(accDoor.getDevice().getSn(),accDoor.getDoorNo(), (short) openInterval, true );
                Map<String, String> resultMap = accDeviceService.getCmdResultById(cmdId, 20);
                if (Objects.nonNull(resultMap)) {
                    ret = resultMap.get("result");
                }
            }
        }
        return ret;
    }
}
