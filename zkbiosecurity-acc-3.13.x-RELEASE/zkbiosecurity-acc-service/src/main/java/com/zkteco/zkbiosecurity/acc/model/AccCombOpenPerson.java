/**
 * File Name: Acc<PERSON>omb<PERSON>penPerson
 * Created by GenerationTools on 2018-03-14 下午03:02
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * 对应百傲瑞达实体 AccCombOpenPerson
 * <AUTHOR>
 * @date:	2018-03-14 下午03:02
 * @version v1.0
 */
@Entity
@Table(name = "ACC_COMBOPEN_PERSON")
@Getter
@Setter
@Accessors(chain=true)
public class AccCombOpenPerson extends BaseModel implements Serializable {
	private static final long serialVersionUID = 1L;
	@Column(name = "BUSINESS_ID",unique = true)
	private Long businessId;

	/**  */
	@Column(name="NAME",length=30,nullable=false,unique = true)
	private String name;

	/**  */
	@Column(name="REMARK",length=50)
	private String remark;

	/**  */
	@OneToMany(mappedBy="accCombOpenPerson")
	private Set<AccPersonCombOpenPerson> accPersonCombOpenPersonSet = new HashSet<AccPersonCombOpenPerson>();

}