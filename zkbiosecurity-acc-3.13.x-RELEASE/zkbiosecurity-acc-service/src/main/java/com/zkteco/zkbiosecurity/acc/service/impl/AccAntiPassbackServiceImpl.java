/**
 * File Name: AccAntiPassbackServiceImpl Created by GenerationTools on 2018-03-13 上午10:27 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccAntiPassbackDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupDao;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccAntiPassbackItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTriggerGroupItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

/**
 * 对应百傲瑞达 AccAntiPassbackServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-13 上午10:27
 */
@Service
public class AccAntiPassbackServiceImpl implements AccAntiPassbackService {
    @Autowired
    private AccAntiPassbackDao accAntiPassbackDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired(required = false)
    private Acc4AccGlobalApbService acc4AccGlobalApbService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccTriggerGroupService accTriggerGroupService;
    @Autowired
    private AccTriggerGroupDao accTriggerGroupDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccAntiPassbackItem saveItem(AccAntiPassbackItem item) {
        AccAntiPassback accAntiPassback =
            Optional.ofNullable(item).map(AccAntiPassbackItem::getId).filter(StringUtils::isNotBlank)
                .flatMap(id -> accAntiPassbackDao.findById(id)).orElse(new AccAntiPassback());
        ModelUtil.copyPropertiesIgnoreNull(item, accAntiPassback);
        if (!validApbRepeat(item)) {
            throw new ZKBusinessException("acc_apb_alreadyExists");
        }

        if (StringUtils.isBlank(accAntiPassback.getId())) {
            accAntiPassback.setBusinessId(createBID());
        }
        AccDevice accDevice = accDeviceDao.findById(item.getDeviceId()).orElse(new AccDevice());
        accAntiPassback.setAccDevice(accDevice);
        if (item.getApbRuleType() != null) {
            short triggerType = item.getApbRuleType() == 0 ? AccTriggerGroupItem.TRIGGER_TYPE_DOOR : AccTriggerGroupItem.TRIGGER_TYPE_READER;
            String group1Id = accTriggerGroupService.updateTriggerGroup(item.getGroup1Ids(), getGroupIdOrNull(accAntiPassback.getTriggerGroup1()), triggerType);
            String group2Id = accTriggerGroupService.updateTriggerGroup(item.getGroup2Ids(), getGroupIdOrNull(accAntiPassback.getTriggerGroup2()), triggerType);
            // 3. 保存新的触发点组
            accAntiPassback.setTriggerGroup1(getGroupByIdOrNull(group1Id));
            accAntiPassback.setTriggerGroup2(getGroupByIdOrNull(group2Id));
        }
        accAntiPassbackDao.save(accAntiPassback);
        item.setId(accAntiPassback.getId());
        accDevCmdManager.setAntiPassbackToDev(accDevice.getSn(), item, false);
        return item;
    }

    @Override
    public List<AccAntiPassbackItem> getByCondition(AccAntiPassbackItem condition) {
        return (List<AccAntiPassbackItem>)accAntiPassbackDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accAntiPassbackDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                AccAntiPassback accAntiPassback = accAntiPassbackDao.findOne(id);
                if (accAntiPassback != null) {
                    AccDevice accDevice = accAntiPassback.getAccDevice();
                    Long bId = accAntiPassback.getBusinessId();
                    /*AccTriggerGroupItem group1 = null, group2 = null;
                    if (accAntiPassback.getTriggerGroup1() != null) {
                        group1 = accTriggerGroupService.getItemById(accAntiPassback.getTriggerGroup1().getId());
                    }
                    if (accAntiPassback.getTriggerGroup2() != null) {
                        group2 = accTriggerGroupService.getItemById(accAntiPassback.getTriggerGroup2().getId());
                    }*/
                    accAntiPassbackDao.delete(accAntiPassback);
                    //deleteByAntiPassbackForDev(accAntiPassback, group1, group2);
                    /*accTriggerLinkService.deleteByTypeAndLinkId(AccConstants.TRIGGER_TYPE_ANTIPASSBACK,
                        accAntiPassback.getId());*/
                    accDevCmdManager.delAntipassbackFromDev(accDevice.getSn(), bId, false);
                }
            }
        }
        return true;
    }

    @Override
    public AccAntiPassbackItem getItemById(String id) {
        List<AccAntiPassbackItem> items = getByCondition(new AccAntiPassbackItem(id));
        AccAntiPassbackItem item =
            Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
        return completionItem(item);
    }

    @Override
    public Map<String, Object> getApbRule(String deviceId) {
        Map<String, Object> map = new HashMap<>();
        AccDevice dev = accDeviceDao.findById(deviceId).orElse(new AccDevice());
        List<AccDoor> accDoorList = dev.getAccDoorList();
        Integer readerCount =
            Integer.parseInt(accDeviceOptionService.getValueByNameAndDevSn(dev.getSn(), "ReaderCount"));
        map.put("readerCount", readerCount);
        if (!accDoorList.isEmpty()) {
            Map<Integer, String> doorMap = new HashMap<>();
            for (AccDoor door : accDoorList) {
                if (StringUtils.isBlank(door.getExtDevId())) {
                    doorMap.put((int)door.getDoorNo(), door.getName());
                }
            }
            map.put("doorMap", doorMap);
        }
        return map;
    }

    @Override
    public String getDevIdWithApb() {
        List<AccAntiPassback> accAntiPassbackList = accAntiPassbackDao.findAll();
        StringBuilder stringBuilder = new StringBuilder();
        for (AccAntiPassback antiPassback : accAntiPassbackList) {
            // 支持新反潜配置的设备不需要被过滤，因为可以设置多条
            if (!validDetermineApb(antiPassback.getAccDevice().getId())) {
                stringBuilder.append(antiPassback.getAccDevice().getId()).append(",");
            }
        }
        // 屏蔽IR9000
        List<AccDevice> devList = accDeviceDao.findByMachineTypeIn(Lists.newArrayList(AccConstants.DEVICE_BIOIR_9000));
        for (AccDevice dev : devList) {
            stringBuilder.append(dev.getId()).append(",");
        }
        // 屏蔽当作其他机器读头的一体机
        List<String> devIds = accDeviceDao.getDevIdsAsWGReader();
        for (String devId : devIds) {
            stringBuilder.append(devId).append(",");
        }
        return StringUtils.isBlank(stringBuilder) ? "" : stringBuilder.substring(0, stringBuilder.length() - 1);
    }

    @Override
    public Boolean validGlobalApb(String deviceId) {
        boolean setGlobalApb = true;
        List<String> readerIdList = new ArrayList<>();// 读头id
        List<AccDoor> accDoorList = accDoorDao.findByDevice_Id(deviceId);// 根据设备id获取door信息
        for (AccDoor accDoor : accDoorList) {
            for (AccReader accReader : accDoor.getAccReaderList()) {
                readerIdList.add(accReader.getId());
            }
        }
        if (!readerIdList.isEmpty()) {
            // 是否设置全局反潜
            if (acc4AccGlobalApbService != null && acc4AccGlobalApbService.existGlobalApbInReaderIds(readerIdList)) {
                setGlobalApb = false;
            }
        }
        return setGlobalApb;
    }

    @Override
    public String convertAntiPassbackRule(String id) {
        String ret = "";
        AccAntiPassback accAntiPassback = accAntiPassbackDao.findOne(id);
        short apbRule = accAntiPassback.getApbRule();
        AccDevice dev = accAntiPassback.getAccDevice();
        List<AccDoor> antiDoorList = dev.getAccDoorList();
        int readerCount = Integer.parseInt(accDeviceOptionService.getValueByNameAndDevSn(dev.getSn(), "ReaderCount"));
        Map<Integer, String> doorMap = new HashMap<>();
        for (AccDoor door : antiDoorList) {
            if (StringUtils.isBlank(door.getExtDevId())) {
                doorMap.put((int)door.getDoorNo(), door.getName());
            }
        }
        if (apbRule == 7) {
            ret = I18nUtil.i18nCode("acc_apb_reader7");
        } else if (AccConstants.DEVICE_ACCESS_CONTROL == dev.getMachineType()
            && !AccConstants.DEVICE_TF1700.equals(dev.getDeviceName())) {
            if ((apbRule == 1)) {
                ret = I18nUtil.i18nCode("acc_apb_reader5", doorMap.get(1));// 读头出反潜
            } else if ((apbRule == 2)) {
                ret = I18nUtil.i18nCode("acc_apb_reader6", doorMap.get(1));// 读头入反潜
            } else if (apbRule == 3) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));// 读头间反潜
            }
        } else if (Objects.equals(dev.getAcpanelType(), AccConstants.ACCESS_CONTROL_DEVICE)) {
            if ((apbRule == 1)) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));
            }
        } else if (doorMap.size() == AccConstants.ACPANEL_1_DOOR) {// 单门双向
            if (apbRule == 1) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));
            }
        } else if (doorMap.size() == AccConstants.ACPANEL_2_DOOR && readerCount == 4
            || dev.getMachineType() == AccConstants.DEVICE_C4_200) {// 两门双向
            if ((apbRule == 1)) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));
            } else if (apbRule == 2) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(2));
            } else if (apbRule == 3) {
                ret = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(2));
            } else if (apbRule == 4) {
                ret = I18nUtil.i18nCode("acc_apb_twoDoor", doorMap.get(1), doorMap.get(2));
            }
        } else if (doorMap.size() == AccConstants.ACPANEL_2_DOOR && readerCount == 2) {// 两门单向
            if ((apbRule == 16)) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));
            } else if (apbRule == 32) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(2));
            } else if (apbRule == 48) {
                ret = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(2));
            } else if (apbRule == 1) {
                ret = I18nUtil.i18nCode("acc_apb_twoDoor", doorMap.get(1), doorMap.get(2));
            }
        } else if (doorMap.size() == AccConstants.ACPANEL_4_DOOR) {// 四门
            if ((apbRule == 1)) {
                ret = I18nUtil.i18nCode("acc_apb_twoDoor", doorMap.get(1), doorMap.get(2));
            } else if (apbRule == 2) {
                ret = I18nUtil.i18nCode("acc_apb_twoDoor", doorMap.get(3), doorMap.get(4));
            } else if (apbRule == 3) {
                ret = I18nUtil.i18nCode("acc_apb_fourDoor", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                    doorMap.get(4));
            } else if (apbRule == 4) {
                ret = I18nUtil.i18nCode("acc_apb_fourDoor2", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                    doorMap.get(4));
            } else if (apbRule == 5) {
                ret = I18nUtil.i18nCode("acc_apb_fourDoor3", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                    doorMap.get(4));
            } else if (apbRule == 6) {
                ret = I18nUtil.i18nCode("acc_apb_fourDoor4", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                    doorMap.get(4));
            } else if (apbRule == 16) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(1));
            } else if (apbRule == 32) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(2));
            } else if (apbRule == 64) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(3));
            } else if (apbRule == 128) {
                ret = I18nUtil.i18nCode("acc_apb_reader", doorMap.get(4));
            } else if (apbRule == 48) {
                ret = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(2));
            } else if (apbRule == 80) {
                ret = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(3));
            } else if (apbRule == 144) {
                ret = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(1), doorMap.get(4));
            } else if (apbRule == 96) {
                ret = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(2), doorMap.get(3));
            } else if (apbRule == 160) {
                ret = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(2), doorMap.get(4));
            } else if (apbRule == 196) {// 对照pullsdk文档由192改为196--add by wenxin 20150120
                ret = I18nUtil.i18nCode("acc_apb_reader2", doorMap.get(3), doorMap.get(4));
            } else if (apbRule == 112) {
                ret = I18nUtil.i18nCode("acc_apb_reader3", doorMap.get(1), doorMap.get(2), doorMap.get(3));
            } else if (apbRule == 176) {
                ret = I18nUtil.i18nCode("acc_apb_reader3", doorMap.get(1), doorMap.get(2), doorMap.get(4));
            } else if (apbRule == 208) {
                ret = I18nUtil.i18nCode("acc_apb_reader3", doorMap.get(1), doorMap.get(3), doorMap.get(4));
            } else if (apbRule == 224) {
                ret = I18nUtil.i18nCode("acc_apb_reader3", doorMap.get(2), doorMap.get(3), doorMap.get(4));
            } else if (apbRule == 240) {
                ret = I18nUtil.i18nCode("acc_apb_reader4", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                    doorMap.get(4));
            }
        }
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delAntipassbackByDevId(String devId) {
        List<AccAntiPassback> accAntiPassbacks = accAntiPassbackDao.findByAccDevice_Id(devId);
        if (ObjectUtils.isNotEmpty(accAntiPassbacks)) {
            accAntiPassbackDao.delete(accAntiPassbacks);
        }
    }

    @Override
    public Boolean validApbByDevIds(List<String> devIds) {
        return accAntiPassbackDao.countAccAntiPassbackByAccDevice_IdIn(devIds) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerTransfer(List<AccAntiPassbackItem> accAntiPassbackItems) {
        // 分批处理 避免大数据
        List<List<AccAntiPassbackItem>> accAntiPassbackItemList =
            CollectionUtil.split(accAntiPassbackItems, CollectionUtil.splitSize);
        for (List<AccAntiPassbackItem> antiPassbackItems : accAntiPassbackItemList) {
            // 获取设备对象集合
            Collection<String> devIds =
                CollectionUtil.getPropertyList(antiPassbackItems, AccAntiPassbackItem::getDeviceId, "-1");
            // List<String>转List<Long>
            List<Long> businessIdList = new ArrayList<>();
            for (String str : devIds) {
                Long i = Long.parseLong(str);
                businessIdList.add(i);
            }
            List<AccDevice> accDeviceList = accDeviceDao.findByBusinessIdIn(businessIdList);
            Map<Long, AccDevice> accDeviceMap = CollectionUtil.listToKeyMap(accDeviceList, AccDevice::getBusinessId);
            for (AccAntiPassbackItem accAntiPassbackItem : antiPassbackItems) {
                AccAntiPassback accAntiPassback = new AccAntiPassback();
                // 获取设备对象根据设备id
                AccDevice accDevice = accDeviceMap.get(Long.valueOf(accAntiPassbackItem.getDeviceId()));
                // 判断是否存在相同数据
                List<AccAntiPassback> accAntiPassbackNew = accAntiPassbackDao.findByAccDevice_Id(accDevice.getId());
                if (ObjectUtils.isEmpty(accAntiPassbackNew)) {
                    // 规则
                    accAntiPassback.setApbRule(accAntiPassbackItem.getApbRule());
                    // 设备
                    accAntiPassback.setAccDevice(accDevice);
                    accAntiPassbackDao.save(accAntiPassback);
                }
            }
        }
    }

    @Override
    public List<AccAntiPassbackItem> getItemsByDevId(String devId) {
        List<AccAntiPassbackItem> accAntiPassbackItems = new ArrayList<>();
        if (StringUtils.isNotBlank(devId)) {
            AccAntiPassbackItem accAntiPassbackItem = new AccAntiPassbackItem();
            accAntiPassbackItem.setDeviceId(devId);
            accAntiPassbackItems = getByCondition(accAntiPassbackItem);
        }
        return accAntiPassbackItems;
    }

    @Override
    public Boolean validDetermineApb(String deviceId) {
        return accDeviceOptionService.getAccSupportFunListVal(deviceId, 66);
    }

    private String getReaderIdsByDoorIds(String groupIds) {
        StringBuffer ReaderStr = new StringBuffer();
        List<AccReaderItem> itemList = null;
        if (StringUtils.isNotBlank(groupIds)) {
            itemList = accReaderService.getItemsByDoorIdIn(groupIds);
            if (!itemList.isEmpty()) {
                for (AccReaderItem readerItem : itemList) {
                    ReaderStr.append(readerItem.getId()).append(",");
                }
            }
        }
        return ReaderStr.substring(0, ReaderStr.length() - 1);
    }

    private String getGroupIdOrNull(AccTriggerGroup oldGroup) {
        return oldGroup == null ? null : oldGroup.getId();
    }

    private AccTriggerGroup getGroupByIdOrNull(String id) {
        if (StringUtils.isNotBlank(id)) {
            return accTriggerGroupDao.findById(id).orElse(null);
        }
        return null;
    }

    @Override
    public boolean nameExists(String name) {
        if (StringUtils.isNotBlank(name)) {
            return accAntiPassbackDao.existsByName(name);
        }
        return false;
    }

    /**
     * 补全item的设备id，设备名，组1，组2
     *
     * @param item
     * @return
     */
    public AccAntiPassbackItem completionItem(AccAntiPassbackItem item) {
        if (item != null && StringUtils.isNotBlank(item.getId()) && item.getApbRuleType() != null) {
            AccAntiPassback accAntiPassback = accAntiPassbackDao.findOne(item.getId());
            String[][] group1DoorIdAndName = null;
            String[][] group2DoorIdAndName = null;
            if (item.getApbRuleType() == 1) {// 读头
                group1DoorIdAndName = getGroupReaderIdAndName(accAntiPassback.getTriggerGroup1());
                group2DoorIdAndName = getGroupReaderIdAndName(accAntiPassback.getTriggerGroup2());
            } else {
                group1DoorIdAndName = getGroupDoorIdAndName(accAntiPassback.getTriggerGroup1());
                group2DoorIdAndName = getGroupDoorIdAndName(accAntiPassback.getTriggerGroup2());
            }
            item.setGroup1Names(StringUtils.join(group1DoorIdAndName[1], ","));
            item.setGroup2Names(StringUtils.join(group2DoorIdAndName[1], ","));
            item.setGroup1Ids(StringUtils.join(group1DoorIdAndName[0], ","));
            item.setGroup2Ids(StringUtils.join(group2DoorIdAndName[0], ","));
            item.setTriggerGroup1BusinessId(accAntiPassback.getTriggerGroup1() != null
                ? accAntiPassback.getTriggerGroup1().getBussinessId() : null);
            item.setTriggerGroup2BusinessId(accAntiPassback.getTriggerGroup2() != null
                ? accAntiPassback.getTriggerGroup2().getBussinessId() : null);
        }
        return item;
    }

    private String[][] getGroupReaderIdAndName(AccTriggerGroup triggerGroup) {
        String[][] addrVal = new String[2][];
        if (triggerGroup != null) {
            Set<AccTriggerGroupAddr> addrSet = triggerGroup.getAddrSet();
            List<String> idList = addrSet.stream().map(AccTriggerGroupAddr::getAddress).collect(Collectors.toList());
            List<AccReaderItem> readerList = accReaderService.getItemsByIdList(idList);
            int readerCount = readerList.size();
            String[] names = new String[readerCount];
            String[] ids = new String[readerCount];
            for (int i = 0; i < readerList.size(); i++) {
                AccReaderItem reader = readerList.get(i);
                names[i] = reader.getName();
                ids[i] = reader.getId();
            }
            addrVal[0] = ids;
            addrVal[1] = names;
        }
        return addrVal;
    }

    private String[][] getGroupDoorIdAndName(AccTriggerGroup triggerGroup) {
        String[][] doorVal = new String[2][];
        if (triggerGroup != null) {
            Set<AccTriggerGroupAddr> addrSet = triggerGroup.getAddrSet();
            List<String> doorIdList =
                addrSet.stream().map(AccTriggerGroupAddr::getAddress).collect(Collectors.toList());
            //List<String> doorIdList = accReaderService.getDoorIdByReaderId(readerIdList);
            List<AccDoorItem> doorList = accDoorService.getItemsByIds(doorIdList);
            int doorCount = doorList.size();
            String[] doorName = new String[doorCount];
            String[] doorId = new String[doorCount];
            for (int i = 0; i < doorCount; i++) {
                AccDoorItem door = doorList.get(i);
                doorName[i] = door.getName();
                doorId[i] = door.getId();
            }
            doorVal[0] = doorId;
            doorVal[1] = doorName;
        }
        return doorVal;
    }

    /**
     * 获取新的递增业务id
     *
     * @return
     */
    private Long createBID() {
        Long maxBId = accAntiPassbackDao.findMaxBusinessId();
        if (maxBId == null) {
            return 1L;
        }
        return maxBId + 1L;
    }

    /**
     * 判断是否重复设置反潜
     *
     * @param item
     * @return
     */
    private Boolean validApbRepeat(AccAntiPassbackItem item) {
        if (item.getApbRuleType() != null) {
            List<AccAntiPassback> accAntiPassbacks = accAntiPassbackDao.findByAccDevice_Id(item.getDeviceId());
            if (StringUtils.isNotBlank(item.getDeviceId())) {
                for (AccAntiPassback apb : accAntiPassbacks) {
                    // 编辑时不判断自己
                    if (apb.getApbRuleType() == null || apb.getId().equals(item.getId())) {
                        continue;
                    }
                    AccTriggerGroup group1 = apb.getTriggerGroup1();
                    Set<AccTriggerGroupAddr> addrSet1 = group1.getAddrSet();
                    Set<String> addrIds1 = new HashSet<>(CollectionUtil.getPropertyList(addrSet1, AccTriggerGroupAddr::getAddress, "-1"));
                    boolean group1Equal = setEqual(addrIds1, new HashSet<>(Arrays.asList(item.getGroup1Ids().split(","))));

                    AccTriggerGroup group2 = apb.getTriggerGroup2();
                    Set<AccTriggerGroupAddr> addrSet2 = group2.getAddrSet();
                    Set<String> addrIds2 = new HashSet<>(CollectionUtil.getPropertyList(addrSet2, AccTriggerGroupAddr::getAddress, "-1"));
                    boolean group2Equal = setEqual(addrIds2, new HashSet<>(Arrays.asList(item.getGroup2Ids().split(","))));

                    if (group1Equal && group2Equal) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 判断集合元素相同
     *
     * @param addrIds1
     * @param addrIds2
     * @return
     */
    private Boolean setEqual(Set<String> addrIds1, Set<String> addrIds2) {
        if (addrIds1 == null || addrIds2 == null) {
            return false;
        }
        if (addrIds1.size() != addrIds2.size()) {
            return false;
        }
        return addrIds1.containsAll(addrIds2) && addrIds2.containsAll(addrIds1);
    }
}