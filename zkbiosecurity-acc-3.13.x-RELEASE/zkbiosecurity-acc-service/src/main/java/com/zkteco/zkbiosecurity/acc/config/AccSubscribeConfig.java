package com.zkteco.zkbiosecurity.acc.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccAlarmMonitorService;
import com.zkteco.zkbiosecurity.acc.service.AccMonitorRedirectService;

/**
 * 门禁redis事件监听配置
 *
 * <AUTHOR>
 * @date 2021-12-13 15:45
 * @since 1.0.0
 */
@Configuration
public class AccSubscribeConfig {

    @Bean
    public RedisMessageListenerContainer accRedisMessageListenerContainer(RedisConnectionFactory connectionFactory,
        AccMonitorRedirectService accMonitorRedirectService, AccAlarmMonitorService accAlarmMonitorService) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        // 监听门禁事件
        final MessageListenerAdapter transactionAdapter =
            new MessageListenerAdapter(accMonitorRedirectService, "convertAndSendAccTransaction");
        transactionAdapter.afterPropertiesSet();
        container.addMessageListener(transactionAdapter, new ChannelTopic(AccConstants.RTMONITOR_CHANNEL_TRANSACTION));
        // 门状态
        final MessageListenerAdapter doorStateAdapter =
            new MessageListenerAdapter(accMonitorRedirectService, "convertAndSendDoorState");
        doorStateAdapter.afterPropertiesSet();
        container.addMessageListener(doorStateAdapter, new ChannelTopic(AccConstants.RTMONITOR_CHANNEL_DOORSTATE));
        // 设备状态
        final MessageListenerAdapter devStateAdapter =
            new MessageListenerAdapter(accMonitorRedirectService, "convertAndSendDevState");
        devStateAdapter.afterPropertiesSet();
        container.addMessageListener(devStateAdapter, new ChannelTopic(AccConstants.RTMONITOR_CHANNEL_DEVSTATE));
        // 设备事件
        final MessageListenerAdapter devEventAdapter =
            new MessageListenerAdapter(accMonitorRedirectService, "convertAndSendDevEvent");
        devEventAdapter.afterPropertiesSet();
        container.addMessageListener(devEventAdapter, new ChannelTopic(AccConstants.RTMONITOR_CHANNEL_DEVEVENT));
        // lcd
        final MessageListenerAdapter lcdStateAdapter =
            new MessageListenerAdapter(accMonitorRedirectService, "convertAndSendLcdEvent");
        lcdStateAdapter.afterPropertiesSet();
        container.addMessageListener(lcdStateAdapter, new ChannelTopic(AccConstants.RTMONITOR_CHANNEL_LCD));
        // 报警事件
        final MessageListenerAdapter alarmAdapter =
            new MessageListenerAdapter(accAlarmMonitorService, "convertAndSendAlarm");
        alarmAdapter.afterPropertiesSet();
        container.addMessageListener(alarmAdapter, new ChannelTopic(AccConstants.ALARM_CHANNEL));
        return container;
    }
}
