package com.zkteco.zkbiosecurity.acc.utils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date Created In 17:43 2020/4/17
 */
public class VmsGetAccServiceUtil {
    public static String dealResMap(Map<String, String> resultMap) {
        List<String> resList =
            resultMap.keySet().stream().filter(k -> StringUtils.isNotBlank(resultMap.get(k))).map(k -> {
                switch (k) {
                    case "cmdId":
                        return "ok";
                    case "offline":
                        return "acc_dev_devNotOpForOffLine";
                    case "faile":
                        return "common_commStatus_executeCmdFailed";
                    case "notExistDev":
                        return "common_dev_notExistDev";
                    case "notSupport":
                        return "acc_dev_devNotSupportFunction";
                    default:
                        return "";
                }
            }).collect(Collectors.toList());
        String res = resList.get(0);
        return res;
    }
}
