package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.vo.AccBioTemplateItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonInfoItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonOptItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.vis.service.Vis4AccLevelService;
import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherBioTemplate;
import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonInfo;
import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonOpt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 访客下发门禁权限
 *
 * <AUTHOR>
 * @since 2018年11月26日 上午10:50:41
 */
@Service
public class Vis4AccLevelServiceImpl implements Vis4AccLevelService {
    @Autowired
    private AccLevelService accLevelService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setVisitorLevelToDev(List<Vis4OtherPersonInfo> vis4AccPersonInfoList, List<String> addAccLevelIds, List<String> delAccLevelIds) {
        List<AccPersonInfoItem> accPersonInfoItemList = new ArrayList<>();
        List<Vis4OtherPersonOpt> vis4AccPersonOptList = null;
        List<Vis4OtherBioTemplate> vis4AccBioTemplateList = null;
        for (Vis4OtherPersonInfo vis4AccPersonInfo : vis4AccPersonInfoList) {
            AccPersonInfoItem accPersonInfoItem = new AccPersonInfoItem();
            vis4AccPersonOptList = vis4AccPersonInfo.getPersonList();
            if (vis4AccPersonOptList != null) {
                List<AccPersonOptItem> accPersonOptItemList = ModelUtil.copyListProperties(vis4AccPersonOptList, AccPersonOptItem.class);
                accPersonInfoItem.setPersonList(accPersonOptItemList);
            }
            vis4AccBioTemplateList = vis4AccPersonInfo.getBioTemplateItemList();
            if (vis4AccBioTemplateList != null) {
                List<AccBioTemplateItem> accBioTemplateItemList = ModelUtil.copyListProperties(vis4AccBioTemplateList, AccBioTemplateItem.class);
                accPersonInfoItem.setAccBioTemplateItemList(accBioTemplateItemList);
            }
            accPersonInfoItemList.add(accPersonInfoItem);
        }
        accLevelService.setVisitorLevelToDev(accPersonInfoItemList, addAccLevelIds, delAccLevelIds);
    }

}
