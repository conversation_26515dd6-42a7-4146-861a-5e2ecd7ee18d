package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.ivideo.service.IVideoGetAccDeviceOptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional
public class IVideoGetAccDeviceOptionServiceImpl implements IVideoGetAccDeviceOptionService {
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Override
    public boolean isSupportFun(String devSn, String optName) {
        return accDeviceOptionService.isSupportFun(devSn, optName);
    }
}
