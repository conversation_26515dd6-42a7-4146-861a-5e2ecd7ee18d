/**
 * File Name: AccCombOpenComb
 * Created by GenerationTools on 2018-03-14 下午03:11
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 AccCombOpenComb
 * <AUTHOR>
 * @date:	2018-03-14 下午03:11
 * @version v1.0
 */
@Entity
@Table(name = "ACC_COMBOPEN_COMB")
@Getter
@Setter
@Accessors(chain=true)
public class AccCombOpenComb extends BaseModel implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**  */
	@ManyToOne
	@JoinColumn(name="COMBOPEN_PERSON_ID",nullable = false)
	private AccCombOpenPerson accCombOpenPerson;

	/**  */
	@ManyToOne
	@JoinColumn(name="COMBOPEN_DOOR_ID",nullable = false)
	private AccCombOpenDoor accCombOpenDoor;

	/**  */
	@Column(name="OPENER_NUMBER",nullable=false)
	private Short openerNumber;

	/**  */
	@Column(name="SORT",nullable=false)
	private Short sort;

}