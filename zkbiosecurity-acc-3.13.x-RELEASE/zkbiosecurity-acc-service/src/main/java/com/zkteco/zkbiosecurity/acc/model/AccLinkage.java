/**
 * File Name: AccLinkage Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import jdk.nashorn.internal.runtime.arrays.ArrayLikeIterator;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccLinkage
 * 
 * <AUTHOR>
 * @date: 2018-03-16 下午04:41
 * @version v1.0
 */
@Entity
@Table(name = "ACC_LINKAGE")
@Getter
@Setter
@Accessors(chain = true)
public class AccLinkage extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @ManyToOne
    @JoinColumn(name = "DEV_ID", nullable = false)
    private AccDevice accDevice;

    /**  */
    @Column(name = "NAME", length = 100, nullable = false, unique = true)
    private String name;

    /**  */
    @OneToMany(mappedBy = "accLinkage", cascade = CascadeType.REMOVE)
    private Set<AccLinkageInOut> accLinkageInOutSet = new HashSet<>();

    /**  */
    @OneToMany(mappedBy = "accLinkage", cascade = CascadeType.REMOVE)
    private Set<AccLinkageTrigger> accLinkageTriggerSet = new HashSet<>();

    /**  */
    @OneToMany(mappedBy = "accLinkage", cascade = CascadeType.REMOVE)
    private Set<AccLinkageVid> accLinkageVidSet = new HashSet<>();

    /**  */
    @OneToMany(mappedBy = "accLinkage", cascade = CascadeType.REMOVE)
    private Set<AccLinkageMedia> accLinkageMediaSet = new HashSet<>();

    @OneToMany(mappedBy = "accLinkage", cascade = CascadeType.REMOVE)
    private List<AccLinkageIas> accLinkageIasSet = new ArrayList<>();
}