package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccLevelDao;
import com.zkteco.zkbiosecurity.acc.dao.AccLevelDeptDao;
import com.zkteco.zkbiosecurity.acc.model.AccLevel;
import com.zkteco.zkbiosecurity.acc.model.AccLevelDept;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonLevelByDeptService;
import com.zkteco.zkbiosecurity.acc.service.AccPersonService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeptLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByDeptItem;
import com.zkteco.zkbiosecurity.auth.provider.AuthSessionProvider;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2018/3/14 11:12
 */
@Service
@Transactional
public class AccPersonLevelByDeptServiceImpl implements AccPersonLevelByDeptService {

    @Autowired
    private AccLevelDao accLevelDao;
    @Autowired
    private AccLevelDeptDao accLevelDeptDao;
    @Autowired
    private AccPersonService accPersonService;
    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AuthSessionProvider authSessionProvider;

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = authDepartmentService.getItemsByPage(new AuthDepartmentItem(), page, size);
        List<AuthDepartmentItem> authDepartmentItems = (List<AuthDepartmentItem>) pager.getData();
        List<AccPersonLevelByDeptItem> levelByDeptItems = new ArrayList<>();
        AccPersonLevelByDeptItem levelByDeptItem = null;
        Map<String, AuthDepartmentItem> departmentItemMap = CollectionUtil.itemListToIdMap(authDepartmentItems);
        for (AuthDepartmentItem item : authDepartmentItems)
        {
            levelByDeptItem = new AccPersonLevelByDeptItem();
            ModelUtil.copyPropertiesIgnoreNull(item, levelByDeptItem);
            if(StringUtils.isNotBlank(item.getParentId()))
            {
                levelByDeptItem.setParentDeptName(departmentItemMap.get(item.getParentId()).getName());
            }
            levelByDeptItems.add(levelByDeptItem);
        }
        pager.setData(levelByDeptItems);
        return pager;
    }

    @Override
    public boolean deleteByIds(String ids) {
        return false;
    }

    @Override
    public void setDeptLinkLevel(String deptId, String levelIds) {
        List<AccLevel> levelList = accLevelDao.findByIdIn((List<String>) CollectionUtil.strToList(levelIds));
        List<AccLevelDept> accLevelDeptList = new ArrayList<>();
        for (AccLevel level : levelList) {
            AccLevelDept accLevelDept = new AccLevelDept(level, deptId);
            accLevelDeptList.add(accLevelDept);
        }
        if(!accLevelDeptList.isEmpty()) {
            accLevelDeptDao.saveAll(accLevelDeptList);
        }
    }

    @Override
    public void delLevel(String deptId, String levelIds) {
        List<String> levelIdList = (List<String>) CollectionUtil.strToList(levelIds);
        accLevelDeptDao.delDeptLevel(deptId, levelIdList);
    }

    @Override
    public Map<String, String> getLevelListByDept(String deptId) {
        List<AccLevelDept> accLevelDeptList = accLevelDeptDao.findByDeptId(deptId);
        Map<String, String> accLevelDeptMap = new HashMap<>();
        accLevelDeptList.stream().forEach(accLevelDept -> {
            if (accLevelDept.getAccLevel() != null){
                accLevelDeptMap.put(accLevelDept.getAccLevel().getId(), accLevelDept.getAccLevel().getName());
            }
        });
        return accLevelDeptMap;
    }

    @Override
    public Pager getPagerFilterAuth(String sessionId, AccPersonLevelByDeptItem codition, int pageNo, int pageSize) {
        AuthDepartmentItem departmentItem = new AuthDepartmentItem();
        ModelUtil.copyPropertiesIgnoreNull(codition, departmentItem);
        Pager pager = authDepartmentService.loadPagerByAuthDeptFilter(sessionId, departmentItem, pageNo, pageSize);
        List<AuthDepartmentItem> authDepartmentItems = (List<AuthDepartmentItem>) pager.getData();
        String parentIds = CollectionUtil.getPropertys(authDepartmentItems, AuthDepartmentItem::getParentId);
        List<AccPersonLevelByDeptItem> levelByDeptItems = new ArrayList<>();
        AccPersonLevelByDeptItem levelByDeptItem = null;
        Map<String, AuthDepartmentItem> departmentItemMap = CollectionUtil.itemListToIdMap(authDepartmentService.getItemsByIds(parentIds));
        for (AuthDepartmentItem item : authDepartmentItems) {
            levelByDeptItem = new AccPersonLevelByDeptItem();
            ModelUtil.copyPropertiesIgnoreNull(item, levelByDeptItem);
            if(StringUtils.isNotBlank(item.getParentId())) {
                levelByDeptItem.setParentDeptName(departmentItemMap.get(item.getParentId()).getName());
            }
            levelByDeptItems.add(levelByDeptItem);
        }
        pager.setData(levelByDeptItems);
        return pager;
    }

    @Override
    public List<AccDeptLevelItem> getByCondition(AccDeptLevelItem condition) {
        return (List<AccDeptLevelItem>)accLevelDeptDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }
}
