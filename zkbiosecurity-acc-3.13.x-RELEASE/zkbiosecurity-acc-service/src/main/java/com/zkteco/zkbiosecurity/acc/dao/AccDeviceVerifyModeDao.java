/**
 * File Name: AccDeviceVerifyMode
 * Created by GenerationTools on 2018-03-20 下午04:20
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccDeviceVerifyMode;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 对应百傲瑞达 AccDeviceVerifyModeDao
 * <AUTHOR>
 * @date:	2018-03-20 下午04:20
 * @version v1.0
 */
public interface AccDeviceVerifyModeDao extends BaseDao<AccDeviceVerifyMode, String> {

    @Query(value = "select distinct e.verifyNo,e.name from AccDeviceVerifyMode e where e.accDevice.id in (?1) order by e.verifyNo")
    Set<Object[]> getCommonVerifyModeByDevId(List<String> devIds);

    /**
     * 根据验证方式编号和设备id查找验证方式数据
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2016年10月14日 下午2:30:41
     * @param verifyNoList
     * @param devId
     * @return
     */
    List<AccDeviceVerifyMode> findByVerifyNoInAndAccDevice_Id(List<Short> verifyNoList, String devId);

    AccDeviceVerifyMode findByVerifyNoAndAccDevice_Sn(short verifyNo, String devSn);

    @Query(value="SELECT verifyNo FROM AccDeviceVerifyMode where accDevice.sn = ?1 ")
    List<Short> getVerifyNoBySN(String sn);

    void deleteByAccDevice_Sn(String sn);

    List<AccDeviceVerifyMode> findByAccDevice_Sn(String devSn);

    /**
     * 根据sns集合获取验证集合
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:17:37
     * @param sns
     * @return
     */
	List<AccDeviceVerifyMode> findByAccDevice_SnIn(Collection<String> sns);

    /**
     * 根据设备id获取验证方式
     *
     * @param devId
     * @return
     */
	List<AccDeviceVerifyMode> findByAccDevice_IdOrderByVerifyNoAsc(String devId);
}