/**
 * File Name: AccVerifyModeRule
 * Created by GenerationTools on 2018-03-14 下午03:02
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccTimeSeg;
import com.zkteco.zkbiosecurity.acc.model.AccVerifyModeRule;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 对应百傲瑞达 AccVerifyModeRuleDao
 * <AUTHOR>
 * @date:	2018-03-14 下午03:02
 * @version v1.0
 */
public interface AccVerifyModeRuleDao extends BaseDao<AccVerifyModeRule, String> {
    AccVerifyModeRule findByName(String name);

    @Query(value = "select t.accTimeSeg.id from AccVerifyModeRule t where t.accTimeSeg.id <> ?1")
    List<String> getTimeSegIdByVerifyRule(String timeSegId);

    int countByAccTimeSeg(AccTimeSeg accTimeSeg);

    AccVerifyModeRule findByAccTimeSeg_Id(String accTimeSegId);

    @Query(value = "select e.accVerifyModeRule from AccDoorVerifyModeRule e where e.accDoor.device.id = ?1")
    List<AccVerifyModeRule> getRuleByDevId(String deviceId);
}