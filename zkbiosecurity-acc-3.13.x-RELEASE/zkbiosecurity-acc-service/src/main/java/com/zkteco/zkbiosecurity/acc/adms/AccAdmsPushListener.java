package com.zkteco.zkbiosecurity.acc.adms;

import java.util.*;

import com.zkteco.zkbiosecurity.acc.operate.AccLinkageVidOperate;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidLinkageService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryReaderItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransaction4OtherItem;
import com.zkteco.zkbiosecurity.adms.bean.AdmsPushRequest;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.redis.listener.RedisQueueReceiverListener;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/6/1 10:23
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccAdmsPushListener implements RedisQueueReceiverListener {
    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDeviceVerifyModeService accDeviceVerifyModeService;
    @Autowired
    private AccLinkageVidOperate accLinkageVidOperate;
    @Autowired
    private AccParamService accParamService;
    @Autowired(required = false)
    private AccTransaction4OtherService[] accTransaction4OtherServices;
    @Autowired(required = false)
    private Vid4OtherGetVidLinkageService vid4OtherGetVidLinkageService;

    @Override
    public String getTopic() {
        return "PUSH_" + BaseConstants.ACC;
    }

    @Override
    public MessageListener redisQueueReceiverListener() {
        return (message, pattern) -> {
            AdmsPushRequest admsPushRequest = JSONObject.parseObject(message.getBody(), AdmsPushRequest.class);
            // 设备序列号
            String sn = admsPushRequest.getSn();
            // 数据类型
            String type = admsPushRequest.getType();
            // 数据
            String data = admsPushRequest.getData();

            if ("rtlog".equals(type)) {
                // 实时事件
                handleRtLog(sn, data);
            } else if ("linkphotoinfo".equals(type)) {
                // IPC联动信息
                // handleLinkPhotoInfo(sn, data);
            } else {
                log.info("Acc receiver invalid, type = {}, data = {}", type, data);
            }

        };
    }

    /**
     * 处理上传IPC联动信息
     * 
     * @param sn: 设备序列号
     * @param data: IPC联动信息
     * <AUTHOR>
     * @date 2024-10-23 16:33
     * @since 1.0.0
     */
    private void handleLinkPhotoInfo(String sn, String data) {
        if (StringUtils.isBlank(data)) {
            return;
        }
        Map<String, String> dataMap = new HashMap<>(16);
        getFieldMap(dataMap, data);
        if (vid4OtherGetVidLinkageService != null) {
            // 根据LogIndex查找事件
            Integer logId = Integer.valueOf(dataMap.get("LogIndex"));
            AccTransactionItem accTransactionItem = accTransactionService.getItemByLogId(logId);
            // 获取entityId entityClassName
            Map<String, String> paramMap = accLinkageVidOperate.getVidLinkageParam(accTransactionItem);
            String entityIds = paramMap.get("entityIds");
            String entityClassName = paramMap.get("entityClassName");
            String vidLinkageHandle = UUID.randomUUID().toString();
            accTransactionItem.setVidLinkageHandle(vidLinkageHandle);
            // 为兼容原有视频联动抓拍弹窗显示照片和录像回放，此处保存视频联动事件记录和视频联动事件关系表
            vid4OtherGetVidLinkageService.handleIPCLinkPhotoInfo(vidLinkageHandle, entityIds, entityClassName, dataMap);
            accTransactionService.saveItem(accTransactionItem);
        }
    }

    /**
     * 处理实时事件,针对需要及时处理业务
     *
     * @param sn:设备序列号
     * @param rtLogStr:实时事件
     * @return void
     * <AUTHOR>
     * @date 2023-06-01 11:14
     * @since 1.0.0
     */
    private void handleRtLog(String sn, String rtLogStr) {
        if (StringUtils.isBlank(rtLogStr) || Objects.isNull(accTransaction4OtherServices)) {
            return;
        }
        Map<String, String> fieldMap = new HashMap<>(16);
        // String[] logsStrArray = rtLogStr.split("\r\n");
        // for (String log : logsStrArray) {
        getFieldMap(fieldMap, rtLogStr);
        short eventNo = !"".equals(fieldMap.get("event")) ? Short.parseShort(fieldMap.get("event")) : -1;
        String pin = fieldMap.get("pin");
        // 通过事件做处理，过滤未通过的事件
        if (!AccConstants.TRANSACTION_VERIFY_SUCCESS.contains(eventNo) || "0".equals(pin)) {
            return;
        }
        // 缓存设备参数信息
        Map<String, String> devFunMap = accDeviceOptionService.getDevOptionBySn(sn);
        String accSupportFunList = devFunMap.getOrDefault("AccSupportFunList", "");

        Map<String, String> personInfoMap = accTransactionService.getPersonInfo(pin);
        // 字段没有全部设置，后续的对接有需要要补充
        AccTransaction4OtherItem item = new AccTransaction4OtherItem();
        item.setPin(pin);
        item.setName(personInfoMap.get("name"));
        item.setLastName(personInfoMap.get("lastName"));
        item.setDeptCode(personInfoMap.get("deptCode"));
        item.setDeptName(personInfoMap.get("deptName"));

        String logTime = fieldMap.get("time");
        Date date = DateUtil.stringToDate(logTime, "yyyy-MM-dd HH:mm:ss");
        item.setEventTime(date);
        item.setEventNo(eventNo);
        boolean isMultiCard = accDeviceOptionService.getAccSupportFunListVal(7, accSupportFunList);
        String cardNo = "";
        if (fieldMap.containsKey("cardno")) {
            cardNo = !"0".equals(fieldMap.get("cardno")) ? fieldMap.get("cardno") : "";
            // 判断是否支持一人多卡，设备传上来的是16进制;
            if (isMultiCard && StringUtils.isNotBlank(cardNo)) {
                cardNo = AccDeviceUtil.convertCardNo(cardNo, 16, 10);
            }
            if (!"".equals(cardNo) && "1".equals(accParamService.getParamValByName("pers.cardHex"))
                && !PersRegularUtil.idNumberCheck(cardNo)) {
                cardNo = AccDeviceUtil.convertCardNo(cardNo, 10, 16);
            }
        }
        item.setCardNo(cardNo);

        int eventAddr = fieldMap.containsKey("eventaddr") ? Integer.parseInt(fieldMap.get("eventaddr")) : -1;
        // 出入状态
        if (fieldMap.containsKey("inoutstatus")) {
            short state = Short.parseShort(fieldMap.get("inoutstatus"));
            // 其他
            String readerName = I18nUtil.i18nCode("common_reader_state_2");
            if (state != 2) {
                final AccQueryReaderItem accReader =
                    accTransactionService.getReaderCacheByDevSnAndDoorNoAndReaderState(sn, eventAddr + "", state);
                if (accReader != null) {
                    readerName = accReader.getName();
                    item.setReaderId(accReader.getId());
                }
            }
            item.setReaderName(readerName);
        }
        String verifyModeNo = (devFunMap.containsKey("NewVFStyles")
            && StringUtils.isNotBlank(devFunMap.get("NewVFStyles")) && !"0".equals(devFunMap.get("NewVFStyles"))
            && !String.valueOf(AccConstants.VERIFY_MODE_OTHERS).equals(fieldMap.get("verifytype")))
                ? String.valueOf(AccDeviceUtil.binaryToDecimal(fieldMap.get("verifytype")))
                : fieldMap.get("verifytype");
        String verifyModeName = accDeviceVerifyModeService.getVerifyModeNameByDevSnAndVerifyModeNo(sn, verifyModeNo);
        item.setVerifyModeName(verifyModeName);
        item.setVerifyModeNo(verifyModeNo);
        item.setDescription("");

        // 推送事件到业务模块
        Arrays.stream(accTransaction4OtherServices)
            .forEach(accTransaction4OtherService -> accTransaction4OtherService.pushPublishTransactionData(item));
        // }

    }

    private void getFieldMap(Map<String, String> dataMap, String data) {
        JSONObject logJson = JSONObject.parseObject(data);
        for (String key : logJson.keySet()) {
            dataMap.put(key, logJson.getString(key));
        }
    }
}
