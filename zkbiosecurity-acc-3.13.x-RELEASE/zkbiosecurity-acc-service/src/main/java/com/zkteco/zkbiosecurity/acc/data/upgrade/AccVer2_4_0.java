package com.zkteco.zkbiosecurity.acc.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 门禁升级2.4
 */
@Slf4j
@Component
public class AccVer2_4_0 implements UpgradeVersionManager {
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public String getVersion() {
        return "v2.4.0";
    }

    @Override
    public boolean executeUpgrade() {
        // 信息自动导出
        baseSysParamService.initData(new BaseSysParamItem("acc.autoExportFrequency", "0", "自动导出类型"));
        baseSysParamService.initData(new BaseSysParamItem("acc.monthFrequency", "0", "按月导出类型"));
        baseSysParamService.initData(new BaseSysParamItem("acc.monthFrequencyDate", "29", "按月导出自定义月份"));
        baseSysParamService.initData(new BaseSysParamItem("acc.dayFrequencyHour", "24", "每天导出自定义小时"));
        baseSysParamService.initData(new BaseSysParamItem("acc.dayFrequencyMinute", "60", "每天导出自定义分钟"));
        baseSysParamService.initData(new BaseSysParamItem("acc.exportMode", "2", "导出数据选项"));
        baseSysParamService.initData(new BaseSysParamItem("acc.autoExportEmail", "", "自动导出发送的邮箱"));
        baseSysParamService.initData(new BaseSysParamItem("acc.autoExportCron", "", "自动导出定时任务cron表达式"));
        return true;
    }
}
