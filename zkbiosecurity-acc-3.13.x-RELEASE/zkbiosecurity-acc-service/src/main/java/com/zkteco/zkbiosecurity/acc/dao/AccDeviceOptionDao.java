/**
 * File Name: AccDeviceOption Created by GenerationTools on 2018-03-20 上午09:48 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDeviceOption;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccDeviceOptionDao
 * 
 * <AUTHOR>
 * @date: 2018-03-20 上午09:48
 * @version v1.0
 */
public interface AccDeviceOptionDao extends BaseDao<AccDeviceOption, String> {

    AccDeviceOption findByAccDevice_IdAndName(String deviceId, String name);

    List<AccDeviceOption> findByName(String name);

    AccDeviceOption findByAccDevice_IdAndNameLike(String deviceId, String name);

    AccDeviceOption findByAccDevice_SnAndName(String sn, String optionName);

    @Query(
        value = "select count(ado.id) from acc_device_option ado left join acc_device ad on ado.dev_id=ad.id where ado.option_name='IPAddress1' and ado.option_value=?2 and (ad.parent_id is null or ad.parent_id=?1)",
        nativeQuery = true)
    Long isExistsSecondIp(String id, String newIp);

    List<AccDeviceOption> findByAccDevice_Sn(String sn);

    List<AccDeviceOption> findByAccDevice_SnIn(Collection<String> sns);

    @Query(value = "select e.accDevice.id from AccDeviceOption e where e.name=?1 and e.value =?2")
    List<String> getDevIdByOptNameAndOptVal(String optionName, String optionVal);

    @Query(
        value = "select e.accDevice.id from AccDeviceOption e where e.name=?1 and e.value =?2 and e.accDevice.authAreaId in (?3)")
    List<String> getDevIdByOptNameAndOptValAndDevAreaIdIn(String optionName, String optionVal,
        List<String> authAreaIds);

    List<AccDeviceOption> findByAccDevice_IdAndNameIn(String devId, Collection<String> optionNames);

    @Query(
        value = "select distinct e.accDevice.sn from AccDeviceOption e where (e.name=?1 and e.value = '1' or e.name =?2 and e.value = '1')")
    List<String> getDevSnByOptNames(String maskDetectionFunOn, String irTempDetectionFunOn);

    List<AccDeviceOption> findByAccDevice_SnInAndName(List<String> snList, String optionName);

    /**
     * 根据设备id查询参数
     *
     * @param devId:设备id
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccDeviceOption>
     * <AUTHOR>
     * @date 2021-02-04 10:08
     * @since 1.0.0
     */
    List<AccDeviceOption> findByAccDevice_Id(String devId);

    /**
     * 根据设备id和参数类型查询设备参数信息
     *
     * @param devId:设备id
     * @param optionType:参数类型
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccDeviceOption>
     * <AUTHOR>
     * @date 2021-02-04 10:13
     * @since 1.0.0
     */
    List<AccDeviceOption> findByAccDevice_IdAndType(String devId, Short optionType);

    @Query(
        value = "select e.accDevice from AccDeviceOption e where e.name in (?1) and e.value =?2 and e.accDevice.authAreaId in (?3)")
    List<AccDevice> getDevByOptNameAndOptValAndDevAreaIdIn(List<String> optionNameList, String optionVal,
        List<String> authAreaIds);

    List<AccDeviceOption> findByNameAndAccDevice_AuthAreaIdIn(String optionName, List<String> authAreaIds);

    @Query(value = "select e.accDevice from AccDeviceOption e where e.name in (?1) and e.value =?2 ")
    List<AccDevice> getDevByOptNameAndOptVal(List<String> optionNameList, String optionVal);

    @Query(
        value = "select e.accDevice from AccDeviceOption e where e.name in (?1) and e.value =?2 and e.accDevice.id not in ( select e1.accDevice.id from AccDeviceOption e1 where e1.name = ?3 and e1.value =?4) and e.accDevice.authAreaId in (?5)")
    List<AccDevice> getDevByOptNameAndOptValAndDevAreaIdInAndNameNotEq(List<String> optionNameList, String optionVal,
        String notNameEq, String notValueEq, List<String> authAreaIds);

    @Query(
        value = "select e.accDevice from AccDeviceOption e where e.name in (?1) and e.value =?2 and e.accDevice.id not in ( select e1.accDevice.id from AccDeviceOption e1 where e1.name = ?3 and e1.value =?4) ")
    List<AccDevice> getDevByOptNameAndOptValAndNameNotEq(List<String> optionNameList, String optionVal,
        String notNameEq, String notValueEq);
}