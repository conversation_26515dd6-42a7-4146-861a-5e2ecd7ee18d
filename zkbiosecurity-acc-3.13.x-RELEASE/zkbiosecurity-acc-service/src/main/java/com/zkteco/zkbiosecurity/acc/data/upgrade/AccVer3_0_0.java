package com.zkteco.zkbiosecurity.acc.data.upgrade;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * 版本升级处理，升级到3.0.0
 *
 * <AUTHOR>
 * @date 2021-04-13 10:20
 */
@Slf4j
@Component
public class AccVer3_0_0 implements UpgradeVersionManager {
    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public String getVersion() {
        return "v3.0.0";
    }

    @Override
    public boolean executeUpgrade() {
        // 升级菜单
        updateMenu();
        // 表字段升级
        updateData();
        // 字段加密
        encryptData();
        return true;
    }

    private void encryptData() {
        // 报警监控收件人邮箱加密
        String receiver = baseSysParamService.getValByName("acc.receiver");
        if (receiver != null && !"".equals(receiver.trim())) {
            baseSysParamService.saveValueByName("acc.receiver", FoldexUtil.encryptByRandomSey(receiver));
        }
        // 报警监控收件人手机号码加密
        String smsReceiver = baseSysParamService.getValByName("acc.smsReceiver");
        if (smsReceiver != null && !"".equals(smsReceiver.trim())) {
            baseSysParamService.saveValueByName("acc.smsReceiver", FoldexUtil.encryptByRandomSey(smsReceiver));
        }
    }

    private void updateData() {
        // 表AccLinkageMedia(ACC_LINKAGE_MEDIA)中字段mediaContent(MEDIA_CONTENT)长度从100变为255；
        jdbcOperateTemplate.alterTableCharLen("ACC_LINKAGE_MEDIA", "MEDIA_CONTENT", "255");
        // 表AccPersonLastAddr(ACC_PERSON_LASTADDR)中字段cardNo(CARD_NO)长度从20变到200；
        jdbcOperateTemplate.alterTableCharLen("ACC_PERSON_LASTADDR", "CARD_NO", "255");
        // 表AccTransaction(ACC_TRANSACTION)中字段cardNo(CARD_NO)长度从50变到200；
        jdbcOperateTemplate.alterTableCharLen("ACC_TRANSACTION", "CARD_NO", "255");
        // 表AccDoor(ACC_DOOR)中字段forcePwd(FORCE_PWD)长度从18变成255；
        jdbcOperateTemplate.alterTableCharLen("ACC_DOOR", "FORCE_PWD", "255");
        // 表AccDoor(ACC_DOOR)中字段supperPwd(SUPPER_PWD)长度从18变成255；
        jdbcOperateTemplate.alterTableCharLen("ACC_DOOR", "SUPPER_PWD", "255");
        // 表AccDevice(ACC_DEVICE)中字段commPwd(COMM_PWD)长度从32变成255；
        jdbcOperateTemplate.alterTableCharLen("ACC_DEVICE", "COMM_PWD", "255");
        // 表AccLevel(ACC_LEVEL)中字段name(NAME)长度从30变成100；
        jdbcOperateTemplate.alterTableCharLen("ACC_LEVEL", "NAME", "100");
    }

    private void updateMenu() {
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("AccPersonLevelByPerson");
        if (Objects.nonNull(subMenuItem)) {
            /** 添加按人员设置，同步权限组操作 */
            AuthPermissionItem subButtonItem =
                new AuthPermissionItem("AccPersonLevelByPersonSync", "acc_personLevelByPerson_syncLevel",
                    "acc:personLevelByPerson:sync", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }
    }

}
