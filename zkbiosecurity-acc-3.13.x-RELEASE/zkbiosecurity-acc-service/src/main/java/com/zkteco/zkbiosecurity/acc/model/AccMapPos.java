/**
 * File Name: AccMapPos
 * Created by GenerationTools on 2018-03-20 下午02:07
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 AccMapPos
 * <AUTHOR>
 * @date:	2018-03-20 下午02:07
 * @version v1.0
 */
@Entity
@Table(name = "ACC_MAP_POS")
@Getter
@Setter
@Accessors(chain=true)
public class AccMapPos extends BaseModel implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**  */
	@ManyToOne
	@JoinColumn(name="MAP_ID")
	private AccMap accMap;

	/**  */
	@Column(name="ENTITY_TYPE",length=30,nullable=false)
	private String entityType;

	/**  */
	@Column(name="ENTITY_ID",nullable=false)
	private String entityId;

	/**  */
	@Column(name="WIDTH")
	private Double width;

	/**  */
	@Column(name="LEFT_X")
	private Double leftX;

	/**  */
	@Column(name="TOP_Y")
	private Double topY;
}