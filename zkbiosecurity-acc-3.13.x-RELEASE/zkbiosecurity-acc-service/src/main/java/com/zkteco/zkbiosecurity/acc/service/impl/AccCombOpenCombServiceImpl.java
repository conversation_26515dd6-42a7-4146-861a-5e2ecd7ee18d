/**
 * File Name: AccCombOpenCombServiceImpl Created by GenerationTools on 2018-03-14 下午03:11 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccCombOpenCombDao;
import com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonDao;
import com.zkteco.zkbiosecurity.acc.model.AccCombOpenComb;
import com.zkteco.zkbiosecurity.acc.model.AccCombOpenDoor;
import com.zkteco.zkbiosecurity.acc.model.AccCombOpenPerson;
import com.zkteco.zkbiosecurity.acc.service.AccCombOpenCombService;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenCombItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 对应百傲瑞达 AccCombOpenCombServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:11
 * @version v1.0
 */
@Service
@Transactional
public class AccCombOpenCombServiceImpl implements AccCombOpenCombService {
    @Autowired
    private AccCombOpenCombDao accCombOpenCombDao;
    @Autowired
    private AccCombOpenPersonDao accCombOpenPersonDao;
    @Autowired
    private AccCombOpenDoorDao accCombOpenDoorDao;

    @Override
    public AccCombOpenCombItem saveItem(AccCombOpenCombItem item) {
        AccCombOpenComb accCombOpenComb = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accCombOpenCombDao.findById(id)).orElse(new AccCombOpenComb());

        ModelUtil.copyProperties(item, accCombOpenComb);
        accCombOpenCombDao.save(accCombOpenComb);
        item.setId(accCombOpenComb.getId());
        return item;
    }

    @Override
    public List<AccCombOpenCombItem> getByCondition(AccCombOpenCombItem condition) {
        return (List<AccCombOpenCombItem>)accCombOpenCombDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accCombOpenCombDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                accCombOpenCombDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccCombOpenCombItem getItemById(String id) {
        List<AccCombOpenCombItem> items = getByCondition(new AccCombOpenCombItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public void handlerTransfer(List<AccCombOpenCombItem> accCombOpenCombItems) {
        // 数据量大的时候处理，分批处理
        List<List<AccCombOpenCombItem>> accCombOpenCombItemList =
            CollectionUtil.split(accCombOpenCombItems, CollectionUtil.splitSize);
        for (List<AccCombOpenCombItem> items : accCombOpenCombItemList) {
            // 获取多人开门人员对象，封装成map对象，避免多余的查询
            Collection<String> combOpenpersonIds =
                CollectionUtil.getPropertyList(items, AccCombOpenCombItem::getAccCombOpenPersonId, "-1");
            // List<String>转List<Long>
            List<Long> combOpenpersonList = new ArrayList<>();
            for (String str : combOpenpersonIds) {
                Long i = Long.parseLong(str);
                combOpenpersonList.add(i);
            }
            List<AccCombOpenPerson> accCombOpenPersonList = accCombOpenPersonDao.findByBusinessIdIn(combOpenpersonList);
            Map<Long, AccCombOpenPerson> accCombOpenPersonMap =
                CollectionUtil.listToKeyMap(accCombOpenPersonList, AccCombOpenPerson::getBusinessId);
            // 获取多人开门对象，封装成map对象，避免多余的查询
            Collection<String> combOpenDoorIds =
                CollectionUtil.getPropertyList(items, AccCombOpenCombItem::getAccCombOpenDoorId, "-1");
            // List<String>转List<Long>
            List<Long> longList = new ArrayList<>();
            for (String str : combOpenDoorIds) {
                Long i = Long.parseLong(str);
                longList.add(i);
            }
            List<AccCombOpenDoor> accCombOpenDoorList = accCombOpenDoorDao.findByBusinessIdIn(longList);
            Map<Long, AccCombOpenDoor> accCombOpenDoorMap =
                CollectionUtil.listToKeyMap(accCombOpenDoorList, AccCombOpenDoor::getBusinessId);

            for (AccCombOpenCombItem accCombOpenCombItem : items) {
                AccCombOpenComb accCombOpenComb = new AccCombOpenComb();
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accCombOpenCombItem, accCombOpenComb, "id");

                // 获取开门人员对象
                AccCombOpenPerson accCombOpenPerson =
                    accCombOpenPersonMap.get(Long.valueOf(accCombOpenCombItem.getAccCombOpenPersonId()));
                if (Objects.nonNull(accCombOpenPerson)) {
                    accCombOpenComb.setAccCombOpenPerson(accCombOpenPerson);
                }
                AccCombOpenDoor accCombOpenDoor =
                    accCombOpenDoorMap.get(Long.valueOf(accCombOpenCombItem.getAccCombOpenDoorId()));
                if (Objects.nonNull(accCombOpenDoor)) {
                    accCombOpenComb.setAccCombOpenDoor(accCombOpenDoor);
                }
                accCombOpenCombDao.save(accCombOpenComb);
            }
        }
    }

    @Override
    public List<AccCombOpenCombItem> getItemsByCombOpenDoorId(String combOpenDoorId) {
        List<AccCombOpenComb> accCombOpenCombList = accCombOpenCombDao.findByAccCombOpenDoor_Id(combOpenDoorId);
        List<AccCombOpenCombItem> accCombOpenCombItemList = new ArrayList<>();
        if (Objects.nonNull(accCombOpenCombList) && !accCombOpenCombList.isEmpty()) {
            for (AccCombOpenComb accCombOpenComb : accCombOpenCombList) {
                AccCombOpenCombItem item = new AccCombOpenCombItem();
                item.setId(accCombOpenComb.getId());
                item.setAccCombOpenDoorId(accCombOpenComb.getAccCombOpenDoor().getId());
                AccCombOpenPerson accCombOpenPerson = accCombOpenComb.getAccCombOpenPerson();
                item.setAccCombOpenPersonId(accCombOpenPerson.getId());
                item.setAccCombOpenPersonName(accCombOpenPerson.getName());
                item.setCombOpenPersonBId(accCombOpenPerson.getBusinessId());
                item.setOpenerNumber(accCombOpenComb.getOpenerNumber());
                item.setSort(accCombOpenComb.getSort());
                accCombOpenCombItemList.add(item);
            }
        }
        return accCombOpenCombItemList;
    }
}