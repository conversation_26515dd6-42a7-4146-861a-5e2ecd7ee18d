package com.zkteco.zkbiosecurity.acc.model;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/6/17 11:44
 */
@Entity
@Table(name = "ACC_ALARMMONITOR_HISTORY")
@Getter
@Setter
@Accessors(chain = true)
public class AccAlarmMonitorHistory extends BaseModel {
    /**
     * 属于那个报警事件
     */
    @ManyToOne
    @JoinColumn(name = "ALARM_MONITOR_ID")
    private AccAlarmMonitor accAlarmMonitor;

    /**
     * 修改状态
     */
    @Column(name = "STATUS")
    private short status;

    /**
     * 历史信息备注
     */
    @Column(name = "ACKNOWLEDGEMENT")
    private String acknowledgement;

    @Override
    public String toString() {
        return "AccAlarmMonitorHistory{" + ", id='" + id + '\'' + "status=" + status + ", acknowledgement='"
            + acknowledgement + '\'' + ", createTime=" + createTime + ", createrCode='" + createrCode + '\'' + '}';
    }
}
