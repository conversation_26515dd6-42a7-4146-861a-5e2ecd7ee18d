package com.zkteco.zkbiosecurity.acc.service.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.enums.AccSupportFuncEnum;
import com.zkteco.zkbiosecurity.acc.service.AccSupportFuncService;

/**
 * 判断产品是否支持门禁功能
 *
 * <AUTHOR>
 * @date 2021-02-22 18:09
 * @since 1.0.0
 */
@Service
public class AccSupportFuncServiceImpl implements AccSupportFuncService {

    /** 产品编码 */
    @Value("${system.productCode}")
    private String productCode;

    @Override
    public boolean isSupportIssueBgVerify() {
        return AccSupportFuncEnum.ISSUE_BG_VERIFY.isSupport(productCode);
    }

    @Override
    public boolean isSupportLockDoor() {
        return AccSupportFuncEnum.LOCK_DOOR.isSupport(productCode);
    }

    @Override
    public boolean isSupportLcdRTMonitor() {
        return AccSupportFuncEnum.LCD_RT_MONITOR.isSupport(productCode);
    }

    @Override
    public boolean isSupportMapAddChannel() {
        return AccSupportFuncEnum.MAP_ADD_CHANNEL.isSupport(productCode);
    }

    @Override
    public boolean isSupportVerifyModeRule() {
        return AccSupportFuncEnum.VERIFY_MODE_RULE.isSupport(productCode);
    }

    @Override
    public boolean isSupportPersDelayPassage() {
        return AccSupportFuncEnum.PERS_DELAY_PASS.isSupport(productCode);
    }

    @Override
    public boolean isSupportAccAdvanced() {
        return AccSupportFuncEnum.ACC_ADVANCED.isSupport(productCode);
    }

    @Override
    public boolean isSupportCarGateIcon() {
        return AccSupportFuncEnum.CAR_GATE_ICON.isSupport(productCode);
    }
}
