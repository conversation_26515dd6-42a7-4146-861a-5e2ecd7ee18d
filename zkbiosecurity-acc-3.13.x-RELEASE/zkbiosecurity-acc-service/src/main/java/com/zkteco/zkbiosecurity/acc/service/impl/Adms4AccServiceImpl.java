package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.bean.AccDevCQDataBean;
import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.operate.AccDealDevCQDataOperate;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceMonitorService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccQueryDeviceItem;
import com.zkteco.zkbiosecurity.adms.service.Adms4AccService;

/**
 * 设备通信服务实现类
 * 
 * <AUTHOR>
 * @date 2018/4/18 11:57
 */
@Service
@Transactional
public class Adms4AccServiceImpl implements Adms4AccService {

    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccDeviceMonitorService accDeviceMonitorService;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private AccDealDevCQDataOperate accDealDevCQDataOperate;

    @Override
    public boolean addDevice(String deviceJsonStr) {
        boolean ret = false;
        JSONObject devInfo = JSONObject.parseObject(deviceJsonStr);
        if (devInfo.containsKey("CommType")) {
            short commType = devInfo.getShort("CommType");
            if (AccConstants.COMM_TYPE_PUSH_HTTP == commType) {
                ret = accDeviceService.setPushDevice(deviceJsonStr);
            } else if (AccConstants.COMM_TYPE_BEST_MQTT == commType || AccConstants.COMM_TYPE_BEST_WS == commType) {
                ret = accDeviceService.setBestDevice(deviceJsonStr);
            }
        }
        return ret;
    }

    @Override
    public void updateAccDeviceDoorStates(String sn) {
        accRTMonitorService.updateAccDeviceDoorStates(sn);
    }

    @Override
    public void updateAccDeviceRunState(String sn) {
        accDeviceMonitorService.updateAccDeviceRunState(sn);
    }

    /**
     * 获取设备之后更新设备参数信息
     *
     * @param optionMap
     * @return
     */
    @Override
    public boolean updateDeviceOptions(Map<String, String> optionMap, String type, String cmdId) {
        if ("options".equals(type)) {
            accDeviceService.updateDevOptions(optionMap);
        } else if ("count".equals(type)) {
            accCacheManager.setQueryDataCount(cmdId, optionMap);
        }
        return true;
    }

    @Override
    public void authChildList(String sn, String table, String unAuthorizeData) {
        if (StringUtils.isNotBlank(unAuthorizeData)) {
            JSONArray devJson = JSON.parseArray(unAuthorizeData);
            String devData = accCacheManager.getDeviceAuthorizeInfo(sn);
            JSONObject json = null;
            if (devData == null) {
                json = new JSONObject();
                json.put("sn", sn);
                json.put("table", table);
                json.put("data", devJson);
            } else {
                json = JSON.parseObject(devData);
                JSONArray jsonData = json.getJSONArray("data");
                Map<String, Integer> devDataMap = new HashMap<>();
                // 对已存在的子设备数据预处理,记录序列号对应的数组下标,方便新旧数据合并处理
                for (int i = 0; i < jsonData.size(); i++) {
                    devDataMap.put(jsonData.getJSONObject(i).getString("sn"), i);
                }
                // 授权子设备, 已经存在sn的就覆盖, 不存在则新增到数组末尾
                for (int i = 0; i < devJson.size(); i++) {
                    JSONObject devJsonObject = devJson.getJSONObject(i);
                    String devJsonSn = devJsonObject.getString("sn");
                    if (StringUtils.isNotEmpty(devJsonSn) && devDataMap.get(devJsonSn) != null) {
                        jsonData.set(devDataMap.get(devJsonSn), devJsonObject);
                    } else {
                        jsonData.add(devJsonObject);
                    }
                }
                json.put("data", jsonData);
            }
            accCacheManager.setDeviceAuthorizeInfo(sn, json.toString());
        } else {
            accCacheManager.delDeviceAuthorizeInfo(sn);
        }
    }

    @Override
    public void getChildDevStatus(String sn) {
        accDeviceService.getChildDevStatus(sn);
    }

    @Override
    public void sendAutoBioData(String sn, String data) {
        //JSONObject dataJson = JSONObject.parseObject(data);
        AccDeviceItem item = accDeviceService.getItemByDevSn(sn);
        // 远程登记不进行保存
        if (StringUtils.isNotBlank(item.getId())) {
            //JSONObject bioDataJson = new JSONObject();
            //String tempData = dataJson.getString("data");
            //String pin = "";
            //String majorVer = "";
            //String type = "";
            String[] dataArray = data.split("\t");
            Map<String, String> dataMap = new HashMap<>();
            for (String bioData : dataArray) {
                String[] tempData = bioData.split("=", 2);
                dataMap.put(tempData[0], tempData[1]);
                //bioDataJson.put(key, value);
            }
            // 早期部分设备传的数据不带版本需要自行组装模板版本给之后的逻辑
            if (!dataMap.containsKey("MAJOR_VER") && dataMap.containsKey("TYPE")) {
                /*templateVersion = bioDataJson.getString("MAJOR_VER");
                if (bioDataJson.containsKey("MINOR_VER")) {
                    templateVersion = templateVersion + "." + bioDataJson.getString("MINOR_VER");
                }*/
                Map<Short, String> bioTemplateVer = accDeviceService.getBioTemplateVersionBySn(item.getSn());
                Short type = Short.parseShort(dataMap.get("TYPE"));
                String templateVersion = bioTemplateVer.get(type);
                String majorVer = "";
                String minorVer = "0";
                if (templateVersion.contains(".")) {
                    majorVer = templateVersion.split("\\.")[0];
                    minorVer = templateVersion.split("\\.")[1];
                } else {
                    majorVer = templateVersion;
                }
                data += "\tMAJOR_VER=" + majorVer + "\tMINOR_VER=" + minorVer;
            }
            //bioDataJson.put("version", templateVersion);
            accCacheManager.setAutoBioData(dataMap.get("PIN"), data);
        }
    }

    @Override
    public void sendIDCardInfo(String sn, String table, String tableName, String count, String data) {
        JSONObject dataJson = new JSONObject();
        dataJson.put("type", table);
        dataJson.put("table", tableName);
        dataJson.put("count", count);
        dataJson.put("data", data);
        AccQueryDeviceItem accDeviceItem = accDeviceService.getQueryItemBySn(sn);
        if (Objects.nonNull(accDeviceItem)) {
            dataJson.put("sn", sn);
            dataJson.put("commType", accDeviceItem.getCommType());
            AccDevCQDataBean accDevCQDataBean = new AccDevCQDataBean(dataJson);
            accDealDevCQDataOperate.dealDataByCData(accDevCQDataBean);// 处理设备上传数据
        }
    }

    @Override
    public void disableDev(String sn) {
        AccQueryDeviceItem accDeviceItem = accDeviceService.getQueryItemBySn(sn);
        if (Objects.nonNull(accDeviceItem)) {
            accDeviceService.setDevDisable(accDeviceItem.getId());
        }
    }
}
