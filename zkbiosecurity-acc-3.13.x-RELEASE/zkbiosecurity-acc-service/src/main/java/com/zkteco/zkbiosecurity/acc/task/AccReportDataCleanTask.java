package com.zkteco.zkbiosecurity.acc.task;

import java.util.concurrent.ScheduledFuture;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class AccReportDataCleanTask {
    @Autowired
    private ScheduleService scheduleService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AccTransactionService accTransactionService;
    private ScheduledFuture<?> scedulefuture;

    public void initReportDataClean() {
        if (scedulefuture != null) {
            scedulefuture.cancel(true);
        }
        try {
            // 门禁数据清理
            BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("accReportDataClean");
            String paramValue = baseSysParamItem.getParamValue();
            JSONObject json = JSON.parseObject(paramValue);
            String runtime = json.getString("runtime");
            if (StringUtils.isNotBlank(runtime) && StringUtils.isNotBlank(runtime.split(":")[0])) {
                String cronExpression = "0 0 " + runtime.split(":")[0] + " * * ?";
                scedulefuture = scheduleService
                    .startScheduleTask(() -> accTransactionService.executeAccDataClean(paramValue), cronExpression);
            }
        } catch (Exception e) {
            log.error("AccReportDataCleanTask error", e);
        }
    }
}
