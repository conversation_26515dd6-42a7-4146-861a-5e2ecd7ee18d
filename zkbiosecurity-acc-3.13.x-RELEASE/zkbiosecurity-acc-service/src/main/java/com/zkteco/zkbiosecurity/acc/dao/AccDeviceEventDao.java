/**
 * File Name: AccDeviceEvent Created by GenerationTools on 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccDeviceEvent;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccDeviceEventDao
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-14 下午02:44
 */
public interface AccDeviceEventDao extends BaseDao<AccDeviceEvent, String> {

    List<AccDeviceEvent> findByAccDevice_IdOrderByEventNoAsc(String deviceId);

    /**
     * 同步音频文件到所有设备的相同事件类型
     *
     * <AUTHOR>
     * @date: 2018-03-16 10:44
     */
    @Modifying
    @Query(value = "UPDATE AccDeviceEvent SET baseMediaFileId=?1 WHERE eventNo=?2 ")
    public void syncAllDeviceEvent(String baseMediaFileId, Short eventNo);

    /**
     * 同步音频文件到所选事件里
     *
     * <AUTHOR>
     * @date: 2018-03-16 22:44
     */
    @Modifying
    @Query(value = "UPDATE AccDeviceEvent SET baseMediaFileId=?1 WHERE id in (?2) ")
    void syncSoundToEvent(String baseMediaFileId, List<String> ids);

    @Query(
        value = "select distinct new AccDeviceEvent (e.eventNo, e.name) from AccDeviceEvent e where e.accDevice.commType = ?1 and e.eventNo not in (?2) order by e.eventNo")
    List<AccDeviceEvent> getGlobalLinkTriggerEvent(short commType, List<Short> eventList);

    AccDeviceEvent findByAccDevice_IdAndEventNo(String devId, Short eventNo);

    @Query(
        value = "select e from AccDeviceEvent e where name = ?1 and e.accDevice.id = (select dev.id from AccDevice dev join dev.accDoorList d where d.id = ?2 and d.isDisableAudio = false)")
    List<AccDeviceEvent> getByNameAndDoorId(String alarmName, String doorId);

    void deleteByAccDevice_Sn(String sn);

    List<AccDeviceEvent> findByAccDevice_Sn(String sn);

    /**
     * 获取对应sns集合下的事件
     *
     * @param sns
     * @return
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:07:45
     */
    List<AccDeviceEvent> findByAccDevice_SnIn(Collection<String> sns);

    @Query(value = "select distinct new AccDeviceEvent (e.eventNo, e.name) from AccDeviceEvent e order by e.eventNo")
    List<AccDeviceEvent> getAllEventNameDistinct();

    /**
     * 通过设备id和eventNo获取对象
     *
     * @param devId 设备id
     * @param eventNo 事件编号
     * @return
     */
    List<AccDeviceEvent> findByAccDevice_idAndEventNo(String devId, Short eventNo);

    /**
     * 同步报警事件优先级到其他设备事件中
     *
     * @param eventPriority:
     * @param eventNo:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022-07-18 17:09
     * @since 1.0.0
     */
    @Modifying
    @Query(value = "UPDATE AccDeviceEvent SET eventPriority=?1 WHERE eventNo=?2 ")
    void syncAllDeviceAlarmEvent(Short eventPriority, Short eventNo);

    @Query(value = "select t from AccDeviceEvent t where t.accDevice.sn <> ?1")
    List<AccDeviceEvent> findByAccDeviceSnNotEq(String devSn);
}