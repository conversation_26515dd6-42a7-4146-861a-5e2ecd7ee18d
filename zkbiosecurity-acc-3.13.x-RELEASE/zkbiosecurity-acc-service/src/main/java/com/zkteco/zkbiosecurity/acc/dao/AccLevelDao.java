/**
 * File Name: AccLevel Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccLevel;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccLevelDao
 * 
 * <AUTHOR>
 * @date: 2018-03-02 下午02:15
 * @version v1.0
 */
public interface AccLevelDao extends BaseDao<AccLevel, String> {
    AccLevel findByInitFlag(Boolean initFlag);

    @Query(value = "select persPersonId from AccLevelPerson e where e.accLevel.id = ?1 and e.persPersonId in (?2)")
    List<Object> getPersonFromLevel(String levelId, List<String> paramStr);

    @Query(value = "select e.accDoor.id from AccLevelDoor e where e.accLevel.id = ?1 and e.accDoor.id in (?2)")
    List<Object> getDoorFromLevel(String levelId, List<String> paramStr);

    List<AccLevel> findByIdIn(List<String> levelIds);

    @Query(value = "select e.timeSegId from AccLevel e where e.id = ?1")
    String getTimeSegIdByLevelId(String levelId);

    @Query(
        value = "select DISTINCT alp.pers_person_id from acc_level_person alp left join acc_level_door ald on alp.level_id = ald.level_id left join acc_door ad on ald.door_id = ad.id where ad.dev_id = ?1",
        nativeQuery = true)
    List<String> getPersonIdByDevId(String devId);

    @Query(
        value = "select DISTINCT alp.pers_person_id from acc_level_person alp left join acc_level_door ald on alp.level_id = ald.level_id left join acc_door ad on ald.door_id = ad.id left join acc_device dev on ad.dev_id = dev.id where dev.id = ?1 or dev.parent_id = ?1",
        nativeQuery = true)
    List<String> getPersonIdByDevIdOrParentDevId(String devId);

    @Query(
        value = "select DISTINCT alp.pers_person_id from acc_level_person alp left join acc_level_door ald on alp.level_id = ald.level_id left join acc_door ad on ald.door_id = ad.id left join acc_device dev on ad.dev_id = dev.id where dev.parent_id = ?1",
        nativeQuery = true)
    List<String> getPersonIdByDevIdAndParentDevId(String devId);

    AccLevel findByName(String name);

    int countByTimeSegId(String timeSegId);

    @Query(value = "select DISTINCT e.ID from ACC_LEVEL e left join ACC_LEVEL_DOOR ald on e.ID = ald.LEVEL_ID "
        + "left join ACC_DOOR ad on ald.DOOR_ID = ad.ID left join ACC_DEVICE dev on ad.DEV_ID = dev.ID where dev.ID = ?1 or dev.PARENT_ID = ?1",
        nativeQuery = true)
    List<String> getLevelIdsByDeviceId(String deviceId);

    List<AccLevel> findByAuthAreaIdIn(List<String> authAreaIdSet);

    Long countByAuthAreaId(String areaId);

    @Query(value = "select distinct e.id from AccLevel e where e.id in (?1)")
    List<String> getLevelIdsByLevelIds(List<String> levelIds);

    /**
     * @Description: 根据名称进行in查询
     * @Author: Abel.huang
     * @CreateDate: 2018/12/13 11:26
     * @Version: 1.0
     */
    List<AccLevel> findByNameIn(Collection<String> names);

    @Query(value = "select distinct e.name from AccLevel e")
    List<String> getAllLevelNames();

    /**
     * 获取所有权限组名称和权限组id
     * 
     * <AUTHOR>
     * @since 2019-08-27 17:34
     * @Param []
     * @return
     */
    @Query(value = "SELECT t.name,t.id FROM AccLevel t ")
    List<Object[]> getNameAndId();

    @Query(
        value = "select distinct alp.level_id from acc_level_person alp left join acc_level_door ald on alp.level_id = ald.level_id where alp.pers_person_id = ?1 and ald.door_id = ?2",
        nativeQuery = true)
    List<String> getLevelIdByPersonIdAndDoorId(String personId, String doorId);
}