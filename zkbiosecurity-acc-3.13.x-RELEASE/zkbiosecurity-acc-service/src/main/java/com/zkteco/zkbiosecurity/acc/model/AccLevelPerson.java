package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁权限组-人员中间表
 * 
 * <AUTHOR>
 * @date 2018/3/14 16:54
 */
@Entity
@Table(name = "ACC_LEVEL_PERSON", indexes = {@Index(columnList = "PERS_PERSON_ID"), @Index(columnList = "LEVEL_ID")})
@Getter
@Setter
@Accessors(chain = true)
public class AccLevelPerson extends BaseModel implements Serializable {

    @ManyToOne
    @JoinColumn(name = "LEVEL_ID", nullable = false)
    private AccLevel accLevel;

    @Column(name = "PERS_PERSON_ID")
    private String persPersonId;

    @Column(name = "START_TIME")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "END_TIME")
    private Date endTime;

    public AccLevelPerson() {}

    public AccLevelPerson(AccLevel accLevel, String persPersonId) {
        this.accLevel = accLevel;
        this.persPersonId = persPersonId;
    }

    public AccLevelPerson(AccLevel accLevel, String persPersonId, Date startTime, Date endTime) {
        this.accLevel = accLevel;
        this.persPersonId = persPersonId;
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
