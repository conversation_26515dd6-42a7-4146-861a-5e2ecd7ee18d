/**
 * File Name: AccPerson
 * Created by GenerationTools on 2018-03-02 下午02:10
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.*;

/**
 * 门禁人员model
 *
 * <AUTHOR>
 * @date:	2018-03-02 下午02:10
 * @version v1.0
 */
@Entity
@Table(name = "ACC_PERSON")
@Getter
@Setter
@Accessors(chain=true)
public class AccPerson extends BaseModel implements Serializable {

	private static final long serialVersionUID = 1L;

	/**关联人事人员id*/
    @Column(name="PERS_PERSON_ID", nullable=false)
	private String personId;

    /**设置有效时间*/
	@Column(name="IS_SET_VALID_TIME", nullable=false)
	private Boolean isSetValidTime;

	/**开始时间*/
	@Column(name="START_TIME")
	private Date startTime;

	/**结束时间*/
	@Column(name="END_TIME")
	private Date endTime;

	/**是否黑名单*/
	@Column(name="DISABLED", nullable=false)
	private Boolean disabled;

	/**是否超级用户，15-是；0-否*/
	@Column(name="SUPER_AUTH")
	private Short superAuth;

	/**设备操作权限，0-一般人员，14-管理员，2-登记员*/
	@Column(name="PRIVILEGE")
	private Short privilege;

	/**是否延长通行*/
	@Column(name="DELAY_PASSAGE", nullable=false)
	private Boolean delayPassage;

	/**未用属性，暂时屏蔽*/
//	@Column(name="IS_OBEY_GAPB", nullable=false)
//	private Boolean isObeyGapb;

//	@ManyToOne
//	@JoinColumn(name = "ACC_COMBOPEN_PERSON_ID")
//	private AccCombOpenPerson accCombOpenPerson;

	/**
	 * 默认构造方法
	 */
	public AccPerson() {
		super();
	}

	public AccPerson(String personId){
		this.personId = personId;
		this.startTime = null;
		this.endTime = null;
		this.superAuth = 0;
		this.privilege = 0;
		this.delayPassage = false;
		this.disabled = false;
		this.isSetValidTime = false;
	}
}