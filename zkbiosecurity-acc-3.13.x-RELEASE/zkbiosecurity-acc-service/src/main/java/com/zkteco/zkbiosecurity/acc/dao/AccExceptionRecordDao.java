package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccExceptionRecord;
import com.zkteco.zkbiosecurity.acc.model.AccTransaction;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

/**
 * 异常记录DAO接口
 * 
 * @author: AI Assistant
 * @date: 2025-07-21
 */
public interface AccExceptionRecordDao extends BaseDao<AccExceptionRecord, String> {

    /**
     * 根据ID列表删除异常记录
     * 
     * @param ids ID列表
     */
    @Modifying
    @Query("DELETE FROM AccExceptionRecord a WHERE a.id IN :ids")
    void deleteByIdIn(@Param("ids") List<String> ids);

    /**
     * 根据SQL语句统计数量
     *
     * @param sql SQL语句
     * @return
     */
    @Query(value = "select count(1) from acc_exception_record where pin=?1 and EXTRACT(EPOCH FROM EXIT_TIME AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC') * 1000  = ?2", nativeQuery = true)
    long countByPinAndExitTime(String pin, long exitTime);

    @Query(value = "select * from acc_exception_record where pin=?1 and SUBJECT='1' and exception_Status  is null order by EXIT_TIME desc  ", nativeQuery = true)
    List<AccExceptionRecord> getItemByPinAndExceptionStatus(String pin);

    /**
     * 查询未闭环的异常记录
     *
     * @return
     */
    @Query(value = "SELECT * FROM Acc_Exception_Record  WHERE (exception_Status = '2' or exception_Status is null) and pin =?1 and SUBJECT='1'  ORDER BY create_Time  limit 1", nativeQuery = true)
    List<AccExceptionRecord> findUnclosedRecordsAndIsNullAndPin(String pin);

    /**
     * @Description: 是否存在未闭环已推送的记录
     * @return:
     * @Author:
     * @date: 2025/7/29 14:47
     */

    @Query(value = "SELECT * FROM Acc_Exception_Record  WHERE exception_Status = '2' and status in ('1','2')  and pin =?1 and SUBJECT='1'  ORDER BY EXIT_TIME limit 2 ", nativeQuery = true)
    List<AccExceptionRecord> findUnclosedAndPushRecordsAndIsNullAndPin(String pin);

    /**
     * @Description: 查询未处理的记录
     * @return:
     * @Author:
     * @date: 2025/7/23 16:45
     */

    @Query(value = "SELECT * FROM Acc_Exception_Record  WHERE (exception_Status = '2' or exception_Status is null)  and SUBJECT='1' and  EXIT_TIME >?1", nativeQuery = true)
    List<AccExceptionRecord> findUnclosedRecordsAndIsNull(Date queryTime);

    /**
     * @Description: 查找已经推送的给组长或者课长的记录
     * @return:
     * @Author:
     * @date: 2025/7/29 15:52
     */
    @Query(value = "SELECT count(1) FROM Acc_Exception_Record  WHERE pin=?1 and SUBJECT='1' and  RECEIVER_POSITION =?2 and EXTRACT(EPOCH FROM EXIT_TIME AT TIME ZONE 'Asia/Shanghai' AT TIME ZONE 'UTC') * 1000  = ?3", nativeQuery = true)
    Long countPushRecordsByReceiverPosition(String pin, String position, long EXIT_TIME);


}
