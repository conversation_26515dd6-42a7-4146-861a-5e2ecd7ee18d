/**
 * File Name: AccLinkageVid
 * Created by GenerationTools on 2018-03-16 下午04:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 AccLinkageVid
 * <AUTHOR>
 * @date:	2018-03-16 下午04:41
 * @version v1.0
 */
@Entity
@Table(name = "ACC_LINKAGE_VID")
@Getter
@Setter
@Accessors(chain=true)
public class AccLinkageVid extends BaseModel implements Serializable {

	/** */
	private static final long serialVersionUID = 1L;

	/**  */
	@ManyToOne
	@JoinColumn(name="LINKAGE_ID",nullable = false)
	private AccLinkage accLinkage;

	/** 录像回放：事件发生前（）s */
	private Integer recordBeforeTime;
	/** 原录像时长，复用此字段；录像回放：事件发生后（）s  */
	@Column(name="ACTION_TIME",nullable=false)
	private Integer actionTime;

	/**  */
	@Column(name="ACTION_TYPE",nullable=false)
	private Short actionType;

	public AccLinkageVid()
	{

	}

	public AccLinkageVid(AccLinkage linkage, Integer actionTime, Short actionType)
	{
		this.accLinkage = linkage;
		this.actionTime = actionTime;
		this.actionType = actionType;
	}

	public AccLinkageVid(AccLinkage accLinkage, Integer recordBeforeTime, Integer actionTime, Short actionType) {
		this.accLinkage = accLinkage;
		this.recordBeforeTime = recordBeforeTime;
		this.actionTime = actionTime;
		this.actionType = actionType;
	}
}