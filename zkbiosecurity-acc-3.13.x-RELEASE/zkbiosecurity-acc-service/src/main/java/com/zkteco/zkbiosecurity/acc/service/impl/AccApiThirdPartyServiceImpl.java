package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/17 17:09
 */
@Service
@Transactional
public class AccApiThirdPartyServiceImpl implements AccApiThirdPartyService {

    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccTransactionService accTransactionService;

    @Override
    public ApiResultMessage openDoorAndCreateEvent(String pin, String readerId) {
        AccReaderItem accReaderItem = accReaderService.getItemById(readerId);
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (accReaderItem != null && persPersonItem != null) {
            AccDoorItem accDoorItem = accDoorService.getItemById(accReaderItem.getDoorId());
            AccDeviceItem accDeviceItem = accDeviceService.getItemById(accDoorItem.getDeviceId());
            //判断门和设备是否启用
            if(accDoorItem.getEnabled() && accDeviceItem.getEnabled())
            {
                Map<String, String> operateResult = accRTMonitorService.operateDoor("openDoor", String.valueOf(accDoorItem.getLockDelay()), accDoorItem.getId());
                String cmdId = operateResult.get("cmdId");
                if (StringUtils.isNotBlank(cmdId)) {
                    cmdId = cmdId.split("=")[0];
                }
                if (StringUtils.isNotBlank(operateResult.get("offline"))) {//设备离线
                    return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, I18nUtil.i18nCode("acc_api_devOffline"));
                } else if (StringUtils.isBlank(cmdId) || Integer.parseInt(cmdId) < 0) {//命令发送失败
                    return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, I18nUtil.i18nCode("common_op_failed"));
                } else {
                    String date = DateUtil.dateToString(new Date(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS);
                    String cardNo = "";
                    if (StringUtils.isNotBlank(persPersonItem.getCardNos())) {
                        List<String> cardNoList = (List<String>) CollectionUtil.strToList(persPersonItem.getCardNos());
                        if (!cardNoList.isEmpty()) {
                            cardNo = cardNoList.get(0);
                        }
                    }
                    String tempLog = String.format("time=%s\tpin=%s\tcardno=%s\teventaddr=%s\tevent=%s\tinoutstatus=%s\tverifytype=%s\r\n", date, pin, cardNo, accDoorItem.getDoorNo(), 0, accReaderItem.getReaderState(), AccConstants.VERIFY_MODE_OTHERS);
                    accTransactionService.addRTLog(accDeviceItem.getSn(), tempLog);
                }
            }
            else
            {
                return ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, I18nUtil.i18nCode("acc_api_devOffline"));
            }
        }

        return ApiResultMessage.successMessage();
    }
}
