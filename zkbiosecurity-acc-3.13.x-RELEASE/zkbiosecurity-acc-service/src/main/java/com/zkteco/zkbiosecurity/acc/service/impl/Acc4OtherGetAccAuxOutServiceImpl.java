package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.Acc4OtherGetAccAuxOutService;
import com.zkteco.zkbiosecurity.acc.service.AccAuxOutService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.acc.utils.VmsGetAccServiceUtil;
import com.zkteco.zkbiosecurity.acc.vo.Acc4OtherSelectAuxOutItem;
import com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 其他模块获取辅助输出实现类
 *
 * <AUTHOR>
 * @date 2021/1/13 16:01
 * @since 1.0.0
 */
@Service
public class Acc4OtherGetAccAuxOutServiceImpl implements Acc4OtherGetAccAuxOutService {

    @Autowired
    AccAuxOutService accAuxOutService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public Pager getItemByAuthFilter(String sessionId, Acc4OtherSelectAuxOutItem condition, int pageNo, int size) {
        String areaIds = accDeviceService.getAreaIdsByAuthFilter(sessionId);
        AccAuxOutItem accAuxOutItem = createAuxOutFilter(condition, areaIds);
        Pager pager = accAuxOutService.getItemsByPage(accAuxOutItem, pageNo, size);
        List<AccAuxOutItem> auxOutList = (List<AccAuxOutItem>)pager.getData();
        List<Acc4OtherSelectAuxOutItem> selectList =
            auxOutList.stream().map(this::createSelectItem).collect(Collectors.toList());
        pager.setData(selectList);
        return pager;
    }

    /**
     * 创建AccAuxOutItem
     *
     * @param condition
     * @param areaIds 区域Id
     * @return AccAuxOutItem
     * <AUTHOR>
     * @date 2021/1/7 18:24
     */
    private AccAuxOutItem createAuxOutFilter(Acc4OtherSelectAuxOutItem condition, String areaIds) {
        AccAuxOutItem accAuxOutItem = new AccAuxOutItem();
        ModelUtil.copyProperties(condition, accAuxOutItem);
        accAuxOutItem.setName(condition.getAuxName());
        accAuxOutItem.setAuxNo(condition.getAuxNo());
        accAuxOutItem.setDevAlias(condition.getDeviceAlias());
        accAuxOutItem.setDevSn(condition.getDeviceSn());
        accAuxOutItem.setDevId(condition.getDeviceId());
        accAuxOutItem.setAreaIdIn(areaIds);
        return accAuxOutItem;
    }

    /**
     * 创建Acc4OtherSelectAuxOutItem
     *
     * @param accAuxOutItem
     * @return Acc4OtherSelectAuxOutItem
     * <AUTHOR>
     * @date 2021/1/7 18:23
     */
    private Acc4OtherSelectAuxOutItem createSelectItem(AccAuxOutItem accAuxOutItem) {
        Acc4OtherSelectAuxOutItem Acc4OtherSelectAuxOutItem = new Acc4OtherSelectAuxOutItem();
        Acc4OtherSelectAuxOutItem.setId(accAuxOutItem.getId());
        Acc4OtherSelectAuxOutItem.setAuxNo(accAuxOutItem.getAuxNo());
        Acc4OtherSelectAuxOutItem.setAuxName(accAuxOutItem.getName());
        Acc4OtherSelectAuxOutItem.setDeviceAlias(accAuxOutItem.getDevAlias());
        Acc4OtherSelectAuxOutItem.setDeviceSn(accAuxOutItem.getDevSn());
        Acc4OtherSelectAuxOutItem.setDeviceId(accAuxOutItem.getDevId());
        ModelUtil.copyProperties(accAuxOutItem, Acc4OtherSelectAuxOutItem);
        return Acc4OtherSelectAuxOutItem;
    }

    @Override
    public List<Acc4OtherSelectAuxOutItem> getItemsByIds(List<String> auxOutIds) {
        List<AccAuxOutItem> items = accAuxOutService.getItemsByIds(auxOutIds);
        return items.stream().map(this::createSelectItem).collect(Collectors.toList());
    }

    @Override
    public Acc4OtherSelectAuxOutItem getItemById(String auxOutId) {
        AccAuxOutItem accAuxOutItem = accAuxOutService.getItemById(auxOutId);
        return createSelectItem(accAuxOutItem);
    }

    @Override
    public ZKResultMsg operate(String type, int opInterval, String auxOutId) {
        Map<String, String> resMap = accRTMonitorService.operateAuxOut(type, opInterval + "", auxOutId);
        String res = VmsGetAccServiceUtil.dealResMap(resMap);
        if (StringUtils.equals("ok", res)) {
            AccAuxOutItem item = accAuxOutService.getItemById(auxOutId);
            res = item == null ? "" : item.getName();
            ZKResultMsg zkResultMsg = ZKResultMsg.successMsg();
            zkResultMsg.setMsg(res);
            return zkResultMsg;
        }
        return ZKResultMsg.failMsg(res);
    }
}
