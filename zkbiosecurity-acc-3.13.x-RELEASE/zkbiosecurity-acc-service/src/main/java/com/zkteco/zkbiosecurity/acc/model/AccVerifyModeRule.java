/**
 * File Name: AccVerifyModeRule Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * 对应百傲瑞达实体 AccVerifyModeRule
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:02
 * @version v1.0
 */
@Entity
@Table(name = "ACC_VERIFYMODE_RULE")
@Getter
@Setter
@Accessors(chain = true)
public class AccVerifyModeRule extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @Column(name = "NAME", length = 30, nullable = false, unique = true)
    private String name;

    /**  */
    @ManyToOne
    @JoinColumn(name = "TIMESEG_ID")
    private AccTimeSeg accTimeSeg;

    /**  */
    @Column(name = "SUNDAY_TIME1_VS_PERSON")
    private Short sundayTime1VSPerson;

    /**  */
    @Column(name = "SUNDAY_TIME1_VS_DOOR")
    private Short sundayTime1VSDoor;

    /**  */
    @Column(name = "SUNDAY_TIME2_VS_PERSON")
    private Short sundayTime2VSPerson;

    /**  */
    @Column(name = "SUNDAY_TIME2_VS_DOOR")
    private Short sundayTime2VSDoor;

    /**  */
    @Column(name = "SUNDAY_TIME3_VS_PERSON")
    private Short sundayTime3VSPerson;

    /**  */
    @Column(name = "SUNDAY_TIME3_VS_DOOR")
    private Short sundayTime3VSDoor;

    /**  */
    @Column(name = "MONDAY_TIME1_VS_PERSON")
    private Short mondayTime1VSPerson;

    /**  */
    @Column(name = "MONDAY_TIME1_VS_DOOR")
    private Short mondayTime1VSDoor;

    /**  */
    @Column(name = "MONDAY_TIME2_VS_PERSON")
    private Short mondayTime2VSPerson;

    /**  */
    @Column(name = "MONDAY_TIME2_VS_DOOR")
    private Short mondayTime2VSDoor;

    /**  */
    @Column(name = "MONDAY_TIME3_VS_PERSON")
    private Short mondayTime3VSPerson;

    /**  */
    @Column(name = "MONDAY_TIME3_VS_DOOR")
    private Short mondayTime3VSDoor;

    /**  */
    @Column(name = "TUESDAY_TIME1_VS_PERSON")
    private Short tuesdayTime1VSPerson;

    /**  */
    @Column(name = "TUESDAY_TIME1_VS_DOOR")
    private Short tuesdayTime1VSDoor;

    /**  */
    @Column(name = "TUESDAY_TIME2_VS_PERSON")
    private Short tuesdayTime2VSPerson;

    /**  */
    @Column(name = "TUESDAY_TIME2_VS_DOOR")
    private Short tuesdayTime2VSDoor;

    /**  */
    @Column(name = "TUESDAY_TIME3_VS_PERSON")
    private Short tuesdayTime3VSPerson;

    /**  */
    @Column(name = "TUESDAY_TIME3_VS_DOOR")
    private Short tuesdayTime3VSDoor;

    /**  */
    @Column(name = "WEDNESDAY_TIME1_VS_PERSON")
    private Short wednesdayTime1VSPerson;

    /**  */
    @Column(name = "WEDNESDAY_TIME1_VS_DOOR")
    private Short wednesdayTime1VSDoor;

    /**  */
    @Column(name = "WEDNESDAY_TIME2_VS_PERSON")
    private Short wednesdayTime2VSPerson;

    /**  */
    @Column(name = "WEDNESDAY_TIME2_VS_DOOR")
    private Short wednesdayTime2VSDoor;

    /**  */
    @Column(name = "WEDNESDAY_TIME3_VS_PERSON")
    private Short wednesdayTime3VSPerson;

    /**  */
    @Column(name = "WEDNESDAY_TIME3_VS_DOOR")
    private Short wednesdayTime3VSDoor;

    /**  */
    @Column(name = "THURSDAY_TIME1_VS_PERSON")
    private Short thursdayTime1VSPerson;

    /**  */
    @Column(name = "THURSDAY_TIME1_VS_DOOR")
    private Short thursdayTime1VSDoor;

    /**  */
    @Column(name = "THURSDAY_TIME2_VS_PERSON")
    private Short thursdayTime2VSPerson;

    /**  */
    @Column(name = "THURSDAY_TIME2_VS_DOOR")
    private Short thursdayTime2VSDoor;

    /**  */
    @Column(name = "THURSDAY_TIME3_VS_PERSON")
    private Short thursdayTime3VSPerson;

    /**  */
    @Column(name = "THURSDAY_TIME3_VS_DOOR")
    private Short thursdayTime3VSDoor;

    /**  */
    @Column(name = "FRIDAY_TIME1_VS_PERSON")
    private Short fridayTime1VSPerson;

    /**  */
    @Column(name = "FRIDAY_TIME1_VS_DOOR")
    private Short fridayTime1VSDoor;

    /**  */
    @Column(name = "FRIDAY_TIME2_VS_PERSON")
    private Short fridayTime2VSPerson;

    /**  */
    @Column(name = "FRIDAY_TIME2_VS_DOOR")
    private Short fridayTime2VSDoor;

    /**  */
    @Column(name = "FRIDAY_TIME3_VS_PERSON")
    private Short fridayTime3VSPerson;

    /**  */
    @Column(name = "FRIDAY_TIME3_VS_DOOR")
    private Short fridayTime3VSDoor;

    /**  */
    @Column(name = "SATURDAY_TIME1_VS_PERSON")
    private Short saturdayTime1VSPerson;

    /**  */
    @Column(name = "SATURDAY_TIME1_VS_DOOR")
    private Short saturdayTime1VSDoor;

    /**  */
    @Column(name = "SATURDAY_TIME2_VS_PERSON")
    private Short saturdayTime2VSPerson;

    /**  */
    @Column(name = "SATURDAY_TIME2_VS_DOOR")
    private Short saturdayTime2VSDoor;

    /**  */
    @Column(name = "SATURDAY_TIME3_VS_PERSON")
    private Short saturdayTime3VSPerson;

    /**  */
    @Column(name = "SATURDAY_TIME3_VS_DOOR")
    private Short saturdayTime3VSDoor;

    /**  */
    @Column(name = "HOLIDAYTYPE1_TIME1_VS_PERSON")
    private Short holidayType1Time1VSPerson;

    /**  */
    @Column(name = "HOLIDAYTYPE1_TIME1_VS_DOOR")
    private Short holidayType1Time1VSDoor;

    /**  */
    @Column(name = "HOLIDAYTYPE1_TIME2_VS_PERSON")
    private Short holidayType1Time2VSPerson;

    /**  */
    @Column(name = "HOLIDAYTYPE1_TIME2_VS_DOOR")
    private Short holidayType1Time2VSDoor;

    /**  */
    @Column(name = "HOLIDAYTYPE1_TIME3_VS_PERSON")
    private Short holidayType1Time3VSPerson;

    /**  */
    @Column(name = "HOLIDAYTYPE1_TIME3_VS_DOOR")
    private Short holidayType1Time3VSDoor;

    /**  */
    @Column(name = "HOLIDAYTYPE2_TIME1_VS_PERSON")
    private Short holidayType2Time1VSPerson;

    /**  */
    @Column(name = "HOLIDAYTYPE2_TIME1_VS_DOOR")
    private Short holidayType2Time1VSDoor;

    /**  */
    @Column(name = "HOLIDAYTYPE2_TIME2_VS_PERSON")
    private Short holidayType2Time2VSPerson;

    /**  */
    @Column(name = "HOLIDAYTYPE2_TIME2_VS_DOOR")
    private Short holidayType2Time2VSDoor;

    /**  */
    @Column(name = "HOLIDAYTYPE2_TIME3_VS_PERSON")
    private Short holidayType2Time3VSPerson;

    /**  */
    @Column(name = "HOLIDAYTYPE2_TIME3_VS_DOOR")
    private Short holidayType2Time3VSDoor;

    /**  */
    @Column(name = "HOLIDAYTYPE3_TIME1_VS_PERSON")
    private Short holidayType3Time1VSPerson;

    /**  */
    @Column(name = "HOLIDAYTYPE3_TIME1_VS_DOOR")
    private Short holidayType3Time1VSDoor;

    /**  */
    @Column(name = "HOLIDAYTYPE3_TIME2_VS_PERSON")
    private Short holidayType3Time2VSPerson;

    /**  */
    @Column(name = "HOLIDAYTYPE3_TIME2_VS_DOOR")
    private Short holidayType3Time2VSDoor;

    /**  */
    @Column(name = "HOLIDAYTYPE3_TIME3_VS_PERSON")
    private Short holidayType3Time3VSPerson;

    /**  */
    @Column(name = "HOLIDAYTYPE3_TIME3_VS_DOOR")
    private Short holidayType3Time3VSDoor;

    /**  */
    @OneToMany(mappedBy = "accVerifyModeRule")
    private Set<AccDoorVerifyModeRule> doorVerifyModeRuleSet = new HashSet<>();

    /**  */
    @OneToMany(mappedBy = "accVerifyModeRule")
    private Set<AccPersonVerifyModeRule> personVerifyModeRuleSet = new HashSet<>();

    /** 是否为新验证方式,否为0,是为1 */
    @Column(name = "NEW_VERIFY_MODE")
    private Short newVerifyMode;

}