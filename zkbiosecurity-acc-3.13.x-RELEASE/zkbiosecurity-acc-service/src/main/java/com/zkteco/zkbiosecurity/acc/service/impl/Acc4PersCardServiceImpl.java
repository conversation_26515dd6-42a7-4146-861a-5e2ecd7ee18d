package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.service.Acc4PersCardService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonInfoItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Transactional
public class Acc4PersCardServiceImpl implements Acc4PersCardService {

    @Autowired
    private AccLevelService accLevelService;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private PersCardService persCardService;

    @Override
    public Boolean lossCard(String cardNos) {
        Map<String, String> personMap = persCardService.getPersonIdsByCardNosAndCardState((List<String>) CollectionUtil.strToList(cardNos), PersConstants.CARD_LOSS);
        List<String> personIdList = new ArrayList<>(personMap.values());
        if (personIdList != null && personIdList.size() > 0) {
            AccPersonInfoItem accPersonInfoItem = accLevelService.getPersonByIds(personIdList);
            List<AccDevice> accDeviceList = accDoorDao.getDevByPersonLevel(personIdList);
            if (accDeviceList != null && accDeviceList.size() > 0) {
                //优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
                AccDeviceUtil.moveUpParentDevList(accDeviceList);
                accDeviceList.stream().forEach(accDevice -> {
                    accLevelService.setPersonCardLevelToDevice(accDevice.getId(), accPersonInfoItem);
                });
            }
        }
        return true;
    }

    @Override
    public Boolean revertCard(String cardNos) {
        Map<String, String> personMap = persCardService.getPersonIdsByCardNos((List<String>) CollectionUtil.strToList(cardNos));
        List<String> personIdList = new ArrayList<>(personMap.values());
        if (personIdList != null && personIdList.size() > 0) {
            AccPersonInfoItem accPersonInfoItem = accLevelService.getPersonByIds(personIdList);
            List<AccDevice> accDeviceList = accDoorDao.getDevByPersonLevel(personIdList);
            if (accDeviceList != null && accDeviceList.size() > 0) {
                //优先处理父设备下发人员，指纹数据，到子设备就无需再次下发数据了
                AccDeviceUtil.moveUpParentDevList(accDeviceList);
                accDeviceList.stream().forEach(accDevice -> {
                    accLevelService.setPersonCardLevelToDevice(accDevice.getId(), accPersonInfoItem);
                });
            }
        }
        return true;
    }
}
