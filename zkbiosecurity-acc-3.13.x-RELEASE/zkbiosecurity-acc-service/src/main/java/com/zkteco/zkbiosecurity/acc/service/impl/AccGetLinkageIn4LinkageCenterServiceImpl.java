package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccAuxInService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceEventService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.linkage.center.service.OtherGetLinkageIn4LinkageCenterService;
import com.zkteco.zkbiosecurity.linkage.center.vo.LinkageCenter4OtherLinkageInItem;
import jodd.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/3 14:54
 * @since 1.0.0
 */
@Service
public class AccGetLinkageIn4LinkageCenterServiceImpl implements OtherGetLinkageIn4LinkageCenterService {

    @Autowired
    private AccDeviceEventService accDeviceEventService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccAuxInService accAuxInService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_ACC;
    }

    @Override
    public Pager getInputPointItems(LinkageCenter4OtherLinkageInItem condition, int pageNo, int pageSize) {
        Pager pager = new Pager();
        if (StringUtil.isBlank(condition.getTriggerEventNo())) {
            return pager;
        }
        Integer eventNo = Integer.parseInt(condition.getTriggerEventNo());
        // 设备事件
        if (accDeviceEventService.isDeviceEventByNo(eventNo)) {
            AccDeviceItem item = new AccDeviceItem();
            item.setAlias(condition.getInputPointName());
            pager = accDeviceService.getItemsByPage(item, pageNo, pageSize);
            List<AccDeviceItem> accDeviceItems = (List<AccDeviceItem>)pager.getData();
            List<LinkageCenter4OtherLinkageInItem> data =
                buildLinkageInItemsByDevice(accDeviceItems, condition.getTriggerEventNo());
            pager.setData(data);
            pager.setTotal(data.size());
        } else if (accDeviceEventService.isAuxinEventByNo(eventNo)) {
            // 辅助输入
            AccAuxInItem item = new AccAuxInItem();
            item.setName(condition.getInputPointName());
            pager = accAuxInService.getItemsByPage(item, pageNo, pageSize);
            List<AccAuxInItem> accAuxInItems = (List<AccAuxInItem>)pager.getData();
            List<LinkageCenter4OtherLinkageInItem> data =
                buildLinkageInItemsByAuxIn(accAuxInItems, condition.getTriggerEventNo());
            pager.setData(data);
            pager.setTotal(data.size());
        } else if (accDeviceEventService.isAuxOutEventByNo(eventNo)) {
            // 辅助输出
            AccAuxOutItem item = new AccAuxOutItem();
            item.setName(condition.getInputPointName());
            pager = accAuxInService.getItemsByPage(item, pageNo, pageSize);
            List<AccAuxOutItem> accAuxOutItems = (List<AccAuxOutItem>)pager.getData();
            List<LinkageCenter4OtherLinkageInItem> data =
                buildLinkageInItemsByAuxOut(accAuxOutItems, condition.getTriggerEventNo());
            pager.setData(data);
            pager.setTotal(data.size());
        } else if (accDeviceEventService.isExtBoardEventByNo(eventNo)) {
            // 扩展板
            AccExtDeviceItem item = new AccExtDeviceItem();
            item.setAlias(condition.getInputPointName());
            pager = accAuxInService.getItemsByPage(item, pageNo, pageSize);
            List<AccExtDeviceItem> accExtDeviceItems = (List<AccExtDeviceItem>)pager.getData();
            List<LinkageCenter4OtherLinkageInItem> data =
                buildLinkageInItemsByExtDevice(accExtDeviceItems, condition.getTriggerEventNo());
            pager.setData(data);
            pager.setTotal(data.size());
        } else if (accDeviceEventService.isReaderEventByNo(eventNo)) {
            // 读头事件
            AccReaderItem item = new AccReaderItem();
            item.setName(condition.getInputPointName());
            pager = accAuxInService.getItemsByPage(item, pageNo, pageSize);
            List<AccReaderItem> accReaderItems = (List<AccReaderItem>)pager.getData();
            List<LinkageCenter4OtherLinkageInItem> data =
                buildLinkageInItemsByReader(accReaderItems, condition.getTriggerEventNo());
            pager.setData(data);
            pager.setTotal(data.size());
        } else {
            AccDoorItem item = new AccDoorItem();
            item.setName(condition.getInputPointName());
            // 门事件
            pager = accDoorService.getItemsByPage(item, pageNo, pageSize);
            List<AccDoorItem> accDoorItems = (List<AccDoorItem>)pager.getData();
            List<LinkageCenter4OtherLinkageInItem> data =
                buildLinkageInItemsByDoor(accDoorItems, condition.getTriggerEventNo());
            pager.setData(data);
            pager.setTotal(data.size());
        }
        return pager;
    }

    private List<LinkageCenter4OtherLinkageInItem> buildLinkageInItemsByReader(List<AccReaderItem> accReaderItems,
        String eventNo) {
        List<LinkageCenter4OtherLinkageInItem> data = new ArrayList<>();
        if (!accReaderItems.isEmpty()) {
            for (AccReaderItem accReaderItem : accReaderItems) {
                Map<String, String> eventMap = accDeviceService.getDevEvent(accReaderItem.getSn());
                if (StringUtil.isBlank(eventMap.get(eventNo))) {
                    // 过滤不支持的设备
                    continue;
                }
                LinkageCenter4OtherLinkageInItem item = new LinkageCenter4OtherLinkageInItem();
                item.setInputPointId(accReaderItem.getId());
                item.setInputPointName(accReaderItem.getName());
                item.setDeviceName(accReaderItem.getDeviceAlias());
                data.add(item);
            }
        }
        return data;
    }

    private List<LinkageCenter4OtherLinkageInItem>
        buildLinkageInItemsByExtDevice(List<AccExtDeviceItem> accExtDeviceItems, String eventNo) {
        List<LinkageCenter4OtherLinkageInItem> data = new ArrayList<>();
        if (!accExtDeviceItems.isEmpty()) {
            for (AccExtDeviceItem accExtDeviceItem : accExtDeviceItems) {
                Map<String, String> eventMap = accDeviceService.getDevEvent(accExtDeviceItem.getSn());
                if (StringUtil.isBlank(eventMap.get(eventNo))) {
                    // 过滤不支持的设备
                    continue;
                }
                LinkageCenter4OtherLinkageInItem item = new LinkageCenter4OtherLinkageInItem();
                item.setInputPointId(accExtDeviceItem.getId());
                item.setInputPointName(accExtDeviceItem.getAlias());
                item.setDeviceName(accExtDeviceItem.getDeviceAlias());
                data.add(item);
            }
        }
        return data;
    }

    private List<LinkageCenter4OtherLinkageInItem> buildLinkageInItemsByAuxOut(List<AccAuxOutItem> accAuxOutItems,
        String eventNo) {
        List<LinkageCenter4OtherLinkageInItem> data = new ArrayList<>();
        if (!accAuxOutItems.isEmpty()) {
            for (AccAuxOutItem accAuxOutItem : accAuxOutItems) {
                Map<String, String> eventMap = accDeviceService.getDevEvent(accAuxOutItem.getDevSn());
                if (StringUtil.isBlank(eventMap.get(eventNo))) {
                    // 过滤不支持的设备
                    continue;
                }
                LinkageCenter4OtherLinkageInItem item = new LinkageCenter4OtherLinkageInItem();
                item.setInputPointId(accAuxOutItem.getId());
                item.setInputPointName(accAuxOutItem.getName());
                item.setDeviceName(accAuxOutItem.getDevAlias());
                data.add(item);
            }
        }
        return data;
    }

    private List<LinkageCenter4OtherLinkageInItem> buildLinkageInItemsByAuxIn(List<AccAuxInItem> accAuxInItems,
        String eventNo) {
        List<LinkageCenter4OtherLinkageInItem> data = new ArrayList<>();
        if (!accAuxInItems.isEmpty()) {
            for (AccAuxInItem accAuxInItem : accAuxInItems) {
                Map<String, String> eventMap = accDeviceService.getDevEvent(accAuxInItem.getDevSn());
                if (StringUtil.isBlank(eventMap.get(eventNo))) {
                    // 过滤不支持的设备
                    continue;
                }
                LinkageCenter4OtherLinkageInItem item = new LinkageCenter4OtherLinkageInItem();
                item.setInputPointId(accAuxInItem.getId());
                item.setInputPointName(accAuxInItem.getName());
                item.setDeviceName(accAuxInItem.getDevAlias());
                data.add(item);
            }
        }
        return data;
    }

    private List<LinkageCenter4OtherLinkageInItem> buildLinkageInItemsByDoor(List<AccDoorItem> accDoorItems,
        String eventNo) {
        List<LinkageCenter4OtherLinkageInItem> data = new ArrayList<>();
        if (!accDoorItems.isEmpty()) {
            for (AccDoorItem accDoorItem : accDoorItems) {
                Map<String, String> eventMap = accDeviceService.getDevEvent(accDoorItem.getDeviceSn());
                if (StringUtil.isBlank(eventMap.get(eventNo))) {
                    // 过滤不支持的设备
                    continue;
                }
                LinkageCenter4OtherLinkageInItem item = new LinkageCenter4OtherLinkageInItem();
                item.setInputPointId(accDoorItem.getId());
                item.setInputPointName(accDoorItem.getName());
                item.setDeviceName(accDoorItem.getDeviceAlias());
                data.add(item);
            }
        }
        return data;
    }

    private List<LinkageCenter4OtherLinkageInItem> buildLinkageInItemsByDevice(List<AccDeviceItem> accDeviceItems,
        String eventNo) {
        List<LinkageCenter4OtherLinkageInItem> data = new ArrayList<>();
        if (!accDeviceItems.isEmpty()) {
            for (AccDeviceItem accDeviceItem : accDeviceItems) {
                Map<String, String> eventMap = accDeviceService.getDevEvent(accDeviceItem.getSn());
                if (StringUtil.isBlank(eventMap.get(eventNo))) {
                    // 过滤不支持的设备
                    continue;
                }
                LinkageCenter4OtherLinkageInItem item = new LinkageCenter4OtherLinkageInItem();
                item.setInputPointId(accDeviceItem.getId());
                item.setInputPointName(accDeviceItem.getAlias());
                item.setDeviceName(accDeviceItem.getAlias());
                data.add(item);
            }
        }
        return data;
    }
}
