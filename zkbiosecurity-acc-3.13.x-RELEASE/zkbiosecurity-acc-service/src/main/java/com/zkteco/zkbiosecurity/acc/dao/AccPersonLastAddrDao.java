package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccPersonLastAddr;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import java.util.Collection;
import java.util.List;

/**
 * 对应百傲瑞达  AccPersonLastAddrDao
 * <AUTHOR>
 * @date:  2018-03-21 15:41:30
 * @version v1.0
 */
public interface AccPersonLastAddrDao extends BaseDao<AccPersonLastAddr, String> {

    AccPersonLastAddr findByPin(String pin);

    /**
     * @Description:    根据pin进行in查询
     * @Author:         Abel.huang
     * @CreateDate:     2018/12/13 11:26
     * @Version:        1.0
     */
    List<AccPersonLastAddr> findByPinIn(Collection<String> pins);
}
