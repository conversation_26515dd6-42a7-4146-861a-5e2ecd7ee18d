package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem;
import com.zkteco.zkbiosecurity.adms.service.AdmsParamsService;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseSysParam4OtherService;

@Service
public class Acc4BaseSysParamService implements BaseSysParam4OtherService {
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired(required = false)
    private AdmsParamsService admsParamsService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;

    @Override
    public void updateQrCodeType(String qrCodeType) {
        // 动态二维码时才需要给设备发命令
        if (BaseDataConstants.QRCODE_TYPE_DYNAMIC.equals(qrCodeType)) {
            // 查询支持动态二维码的设备
            AccDeviceOptionItem item = new AccDeviceOptionItem();
            item.setName("QRCodeDecryptFunList");
            List<AccDeviceOptionItem> optionList = accDeviceOptionService.getByCondition(item);
            List<String> deviceIdList = new ArrayList<>();
            String decryptType = admsParamsService.getValByName("qrcode.algorithm");
            if (optionList != null && optionList.size() > 0) {
                if (StringUtils.isNotBlank(decryptType)) {
                    int decryptTypeNum = Integer.parseInt(decryptType);
                    for (AccDeviceOptionItem option : optionList) {
                        String decryptFunList = option.getValue();
                        if (decryptFunList.length() >= decryptTypeNum
                            && "1".equals(decryptFunList.substring(decryptTypeNum - 1, decryptTypeNum))) {
                            deviceIdList.add(option.getDeviceId());
                        }
                    }
                }
            }
            if (deviceIdList.size() > 0) {
                String secretKey = admsParamsService.getQRCodeSecretKey();
                List<AccDeviceItem> deviceItems = accDeviceService.getItemByIds(StringUtils.join(deviceIdList, ","));
                for (AccDeviceItem dev : deviceItems) {
                    accDevCmdManager.setQRCodeParam(dev, decryptType, secretKey, false);
                }
            }
        }
    }

}
