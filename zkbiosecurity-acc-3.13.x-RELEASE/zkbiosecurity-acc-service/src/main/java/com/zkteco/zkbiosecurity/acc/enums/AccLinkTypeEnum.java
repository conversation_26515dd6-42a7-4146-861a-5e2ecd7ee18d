package com.zkteco.zkbiosecurity.acc.enums;

import com.zkteco.zkbiosecurity.pers.vo.PersPersonLinkItem;

/**
 * @ClassName AccLinkTypeEnum
 * @Description TODO
 * <AUTHOR>
 * @Date 2018/8/14 11:54
 * @Version 1.0
 **/
public enum AccLinkTypeEnum implements PersPersonLinkItem.LinkTypeInterFace {

    //权限组
    ACC_LEVEL,
    //首人常开
    ACC_FIRSTOPEN,
    //多人开门
    ACC_COMBOPENPERSON,
    //验证规则方式人员组
    ACC_VERIFYMODERULEPERSONGROUP,
    //查看区域内人员
    ACC_ZONEPERSON,
    //全局反潜
    ACC_GLOBALAPB,
    //全局联动
    ACC_GLOBALLINKAGE,
    //人员有效性
    ACC_PERSONLIMIT;
}
