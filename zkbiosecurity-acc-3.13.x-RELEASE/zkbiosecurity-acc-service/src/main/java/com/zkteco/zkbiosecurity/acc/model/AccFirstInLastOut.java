/**
 * <AUTHOR>
 * @date 2020/3/27 11:13
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@Entity
@Table(name = "ACC_FIRSTIN_LASTOUT",
    uniqueConstraints = @UniqueConstraint(columnNames = {"pin", "first_in_time", "last_out_time"}))
@Getter
@Setter
@Accessors(chain = true)
public class AccFirstInLastOut extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 人员编号 */
    @Column(name = "PIN", length = 30)
    private String pin;

    /** 姓名 */
    @Column(name = "NAME", length = 50)
    private String name;

    /** 姓氏 */
    @Column(name = "LAST_NAME", length = 50)
    private String lastName;

    /** 部门名称 */
    @Column(name = "DEPT_CODE", length = 100)
    private String deptCode;

    /** 部门名称 */
    @Column(name = "DEPT_NAME", length = 100)
    private String deptName;

    /** first in读头名称 */
    @Column(name = "READER_NAME_IN", length = 100)
    private String readerNameIn;

    /** first in事件时间 */
    @Column(name = "FIRST_IN_TIME")
    private Date firstInTime;

    /** last out读头名称 */
    @Column(name = "READER_NAME_OUT", length = 100)
    private String readerNameOut;

    /** last out事件时间 */
    @Column(name = "LAST_OUT_TIME")
    private Date lastOutTime;
}
