package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.*;

import org.hibernate.annotations.GenericGenerator;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 夏令时业务id自增序列
 * 
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Table(name = "ACC_DSTIME_BID")
@Entity
@Getter
@Setter
@Accessors(chain = true)
public class AccDSTimeBId implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_acc_dstimeid")
    @GenericGenerator(name = "seq_acc_dstimeid", strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator",
        parameters = {@org.hibernate.annotations.Parameter(name = "sequence_name", value = "seq_acc_dstimeid")})
    private Long id;
}
