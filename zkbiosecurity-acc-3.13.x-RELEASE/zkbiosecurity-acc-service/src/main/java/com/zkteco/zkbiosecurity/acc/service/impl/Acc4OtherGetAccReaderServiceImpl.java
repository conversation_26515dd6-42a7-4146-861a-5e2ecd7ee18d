package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.Acc4OtherGetAccReaderService;
import com.zkteco.zkbiosecurity.acc.service.AccReaderService;
import com.zkteco.zkbiosecurity.acc.vo.AccReader4OtherItem;
import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/26 15:39
 * @since 1.0.0
 */
@Service
public class Acc4OtherGetAccReaderServiceImpl implements Acc4OtherGetAccReaderService {

    @Autowired
    private AccReaderService accReaderService;

    @Override
    public List<AccReader4OtherItem> getItemListByDeviceId(String deviceId) {
        List<AccReaderItem> accReaderItemList = accReaderService.getItemListByDevId(deviceId);
        return ModelUtil.copyListProperties(accReaderItemList, AccReader4OtherItem.class);
    }
}
