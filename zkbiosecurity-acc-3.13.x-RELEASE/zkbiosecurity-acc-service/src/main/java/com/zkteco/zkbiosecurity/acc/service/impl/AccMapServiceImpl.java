package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccMapDao;
import com.zkteco.zkbiosecurity.acc.dao.AccMapPosDao;
import com.zkteco.zkbiosecurity.acc.model.AccMap;
import com.zkteco.zkbiosecurity.acc.model.AccMapPos;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.FileUtils;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.system.service.ModuleInfoService;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherDeviceService;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherGetVidChannelService;
import com.zkteco.zkbiosecurity.vid.vo.Vid4OtherChannelItem;
import com.zkteco.zkbiosecurity.vid.vo.Vid4OtherDeviceItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;

/**
 * 对应百傲瑞达 AccMapServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-20 下午02:07
 * @version v1.0
 */
@Service
@Transactional
public class AccMapServiceImpl implements AccMapService {
    @Autowired
    private AccMapDao accMapDao;
    @Autowired
    private AccMapPosDao accMapPosDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired(required = false)
    private Vid4OtherDeviceService vid4OtherDeviceService;
    @Autowired(required = false)
    private Vid4OtherGetVidChannelService vid4OtherGetVidChannelService;
    @Autowired(required = false)
    private AccGetVmsChannelService accGetVmsChannelService;
    @Autowired
    private ModuleInfoService moduleInfoService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AccMapPosService accMapPosService;
    @Autowired
    private AccDoorService accDoorService;

    @Override
    public AccMapItem saveItem(AccMapItem item) {
        AccMap accMap = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accMapDao.findById(id)).orElse(new AccMap());
        ModelUtil.copyPropertiesIgnoreNull(item, accMap);
        AuthAreaItem authAreaItem = authAreaService.getItemById(item.getAuthAreaId());
        accMap.setAuthAreaId(authAreaItem.getId());
        accMapDao.save(accMap);
        item.setId(accMap.getId());
        return item;
    }

    @Override
    public List<AccMapItem> getByCondition(AccMapItem condition) {
        return (List<AccMapItem>)accMapDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accMapDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                // 先删除图片
                AccMap accMap = accMapDao.findById(id).orElse(null);
                if (Objects.isNull(accMap)) {
                    continue;
                }
                File picFile = new File(accMap.getMapPath());
                if (!picFile.isAbsolute()) {
                    picFile = new File(FileUtils.getLocalFullPath(accMap.getMapPath()));
                }
                if (picFile.exists()) {
                    picFile.delete();
                }
                // 再删除地图
                accMapDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public AccMapItem getItemById(String id) {
        List<AccMapItem> items = getByCondition(new AccMapItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public List<TreeItem> createMapTree(String sessionId) {
        List<TreeItem> items = new ArrayList<>();
        TreeItem item = null;
        TreeItem pItem = null;
        String userId = authUserService.getUserIdBySessionId(sessionId);
        AccMapItem accMapItem = new AccMapItem();
        if (StringUtils.isNotBlank(userId)) {
            accMapItem.setUserId(userId);
        }
        List<AccMapItem> accMapList = getByCondition(accMapItem);// 根据登录用户权限过滤
        if (accMapList.isEmpty()) {
            return new ArrayList<>();// 没有地图返回空
        }
        List<String> areaIdList = new ArrayList<>();
        for (AccMapItem accMap : accMapList) {
            item = new TreeItem();
            item.setId(accMap.getId());
            item.setText(accMap.getName());
            pItem = new TreeItem(accMap.getAuthAreaId() + "_");// 把地图所属区域作为父节点
            item.setParent(pItem);

            items.add(item);

            areaIdList.add(accMap.getAuthAreaId());
        }

        // 获取所有区域，区域ID都加上"_"，用于前端点击过滤
        List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIdList);
        for (AuthAreaItem authAreaItem : authAreaItemList) {
            item = new TreeItem();
            item.setId(authAreaItem.getId() + "_");
            item.setText(authAreaItem.getName());
            if (authAreaItem.getParentId() != null) {
                pItem = new TreeItem(authAreaItem.getParentId() + "_");
            } else {
                pItem = new TreeItem("0");
            }
            item.setParent(pItem);

            items.add(item);
        }

        return items;
    }

    @Override
    public void saveMapPos(String mapId, Double width, Double height, String posArray) {
        AccMap accMap = accMapDao.getOne(mapId);
        accMap.setWidth(width);
        accMap.setHeight(height);
        accMapDao.save(accMap);

        if (posArray != null && !"".equals(posArray)) {
            String[] entitysInfo = posArray.split(",");
            for (int i = 0; i < entitysInfo.length; i += 4) {
                AccMapPos accMapPos = accMapPosDao.getOne(entitysInfo[i].toString());
                if (accMapPos != null) {
                    accMapPos.setWidth(Double.parseDouble(entitysInfo[i + 1]));
                    accMapPos.setLeftX(Double.parseDouble(entitysInfo[i + 2]));
                    accMapPos.setTopY(Double.parseDouble(entitysInfo[i + 3]));
                    accMapPosDao.save(accMapPos);
                }
            }
        }
    }

    @Override
    public void addEntity(String mapId, Double width, String entityType, String entityIds, String logMethod) {
        AccMap accMap = accMapDao.getOne(mapId);
        List<AccMapPos> accMapPosList = new ArrayList<>();
        double defaultLeft = 45.0;
        AccMapPos accMapPos = null;
        String[] entityIdList = entityIds.split(",");
        for (String entityId : entityIdList) {
            if (!entityId.equals("") && !entityId.contains("_")) {
                accMapPos = new AccMapPos();
                accMapPos.setEntityId(entityId);
                accMapPos.setEntityType(entityType);
                accMapPos.setAccMap(accMap);
                accMapPos.setTopY(8.0);
                accMapPos.setLeftX(defaultLeft);
                accMapPosList.add(accMapPos);
                accMapPosDao.save(accMapPos);

                defaultLeft += 110.0;
            }
        }
        // accMap.setAccMapPosList(accMapPosList);
        // accMapDao.save(accMap);
    }

    @Override
    public boolean isExist(String name) {
        AccMap accMap = accMapDao.findByName(name);
        if (accMap == null) {
            return true;
        }
        return false;
    }

    @Override
    public boolean isExistVid() {
        return moduleInfoService.isExistModuleByCode("Vid");
    }

    @Override
    public boolean isExistVms() {
        return moduleInfoService.isExistModuleByCode("Vms");
    }

    @Override
    public boolean isExistVidDevice() {
        if (vid4OtherDeviceService == null) {
            return false;
        }

        List<Vid4OtherDeviceItem> vid4OtherDeviceItemList = vid4OtherDeviceService.getEnabledVidDevice();
        return !Objects.isNull(vid4OtherDeviceItemList) && !vid4OtherDeviceItemList.isEmpty();
    }

    @Override
    public Pager getSelectChannelItemsByPage(String sessionId, AccMapSelectChannelItem condition, int pageNo,
        int pageSize) {
        if (vid4OtherGetVidChannelService == null) {
            return new Pager();
        }
        Vid4OtherChannelItem vid4OtherChannelItem = new Vid4OtherChannelItem();
        // 设置条件
        vid4OtherChannelItem.setName(condition.getName());
        vid4OtherChannelItem.setSn(condition.getSn());
        vid4OtherChannelItem.setInId(condition.getInId());
        vid4OtherChannelItem.setNotInId(condition.getNotInId());
        vid4OtherChannelItem.setEnabled(true);
        Pager pager =
            vid4OtherGetVidChannelService.loadPagerByAuthFilter(sessionId, vid4OtherChannelItem, pageNo, pageSize);
        List<Acc4VidChannelItem> vidChannelSelectItemList = (List<Acc4VidChannelItem>)pager.getData();
        List<AccMapSelectChannelItem> accMapSelectChannelItemList =
            ModelUtil.copyListProperties(vidChannelSelectItemList, AccMapSelectChannelItem.class);
        pager.setData(accMapSelectChannelItemList);
        return pager;
    }

    @Override
    public Map<String, String> getVmsChannelById(String channelId) {
        Map<String, String> vmdChannelMap = null;
        if (Objects.nonNull(accGetVmsChannelService)) {
            vmdChannelMap = accGetVmsChannelService.getVmsChannelById(channelId);
        }
        return vmdChannelMap;
    }

    @Override
    public List<AccMapPosItem> getMapPosList(String mapId) {
        AccMapPosItem accMapPosItem = new AccMapPosItem();
        accMapPosItem.setMapId(mapId);
        List<AccMapPosItem> mapPosItemList = accMapPosService.getByCondition(accMapPosItem);
        List<AccMapPosItem> mapPosList = new ArrayList<>();
        for (AccMapPosItem pos : mapPosItemList) {
            boolean isShow = true;
            if ("AccDoor".equals(pos.getEntityType())) {
                AccDoorItem accDoorItem = accDoorService.getItemById(pos.getEntityId());
                AccQueryDeviceItem queryDeviceItem = accDeviceService.getQueryItemBySn(accDoorItem.getDeviceSn());
                List<AccQueryDoorItem> queryDoorItemList = queryDeviceItem.getAccDoorItemList();
                Map<String, AccQueryDoorItem> doorItemMap =
                    CollectionUtil.listToKeyMap(queryDoorItemList, AccQueryDoorItem::getId);
                AccQueryDoorItem queryDoorItem = doorItemMap.get(accDoorItem.getId());
                // 门底下读头绑定的视频通道ID
                String doorBindChannelIds = accDoorService.getDoorBindChannelIds(queryDoorItem);
                pos.setChannelIds(doorBindChannelIds);
                // 设备有被当作读头关联不显示 add 注解信息 by max
                if (queryDeviceItem.getWgReaderId() != null) {
                    isShow = false;
                }
                // 禁用的门不显示
                if (!accDoorItem.getEnabled()) {
                    isShow = false;
                }
                pos.setEntityName(accDoorItem.getName());
            } else if ("VidChannel".equals(pos.getEntityType())) {
                Map<String, String> channelMap = accMapPosService.getVidChannelById(pos.getEntityId());
                if (channelMap.size() > 0) {
                    Boolean enabled = Boolean.valueOf(channelMap.get("enabled"));
                    if (!enabled) {
                        isShow = false;
                    }
                    pos.setEntityName(channelMap.get("name"));
                }
            }

            if (isShow) {
                mapPosList.add(pos);
            }
        }
        return mapPosList;
    }
}
