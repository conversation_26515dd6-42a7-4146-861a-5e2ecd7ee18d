/**
 * File Name: AccAuxIn Created by GenerationTools on 2018-03-13 下午05:00 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccAuxIn;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccAuxInDao
 * 
 * <AUTHOR>
 * @date: 2018-03-13 下午05:00
 * @version v1.0
 */
public interface AccAuxInDao extends BaseDao<AccAuxIn, String> {

    @Query("select e.id, e.name from AccAuxIn e where e.accDevice.id=?1 order by e.id")
    List<Object[]> getAuxInInfoByDevId(String deviceId);

    AccAuxIn findByNameAndAccDevice_Id(String name, String devId);

    @Query(value = "select e from AccAuxIn e where e.accDevice.id in (?1)")
    List<AccAuxIn> getByDevIdIn(List<String> devIdList);

    List<AccAuxIn> findByIdIn(List<String> inputIdList);

    AccAuxIn findByAuxNoAndAccDevice_Id(short eventAddr, String devId);

    List<AccAuxIn> findByAccDevice_Id(String id);

    int countByTimeSegId(String timeSegId);

    /**
     * 根据sns集合获取辅助输入
     * 
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:44:01
     * @param sns
     * @return
     */
    List<AccAuxIn> findByAccDevice_SnIn(Collection<String> sns);

    List<AccAuxIn> findByExtDevIdOrderByAuxNoAsc(String id);

    @Query(value = "select e.auxNo from AccAuxIn e where e.accDevice.id = ?1 order by e.auxNo")
    List<Short> findAuxInNoByDevId(String devId);
}