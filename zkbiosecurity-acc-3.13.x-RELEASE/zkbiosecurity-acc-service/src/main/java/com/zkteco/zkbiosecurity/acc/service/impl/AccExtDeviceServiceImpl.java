package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.utils.AccDeviceUtil;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.license.vo.bean.ResultCode;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;
import com.zkteco.zkbiosecurity.vid.service.Vid4OtherChannel2EntityService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class AccExtDeviceServiceImpl implements AccExtDeviceService {
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AccExtDeviceDao accExtDeviceDao;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccTimeSegService accTimeSegService;
    @Autowired
    private PersWiegandFmtService persWiegandFmtService;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccReaderDao accReaderDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccAuxInDao accAuxInDao;
    @Autowired
    private AccAuxOutDao accAuxOutDao;
    @Autowired
    private AccMapPosService accMapPosService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired(required = false)
    private Vid4OtherChannel2EntityService vid4OtherChannel2EntityService;
    @Autowired
    private AccFirstOpenDao accFirstOpenDao;
    @Autowired(required = false)
    private Acc4AccGlobalInterlockService acc4AccGlobalInterlockService;
    @Autowired(required = false)
    private Acc4AccReaderZoneService acc4AccReaderZoneService;

    @Value("${system.language}")
    private String language;

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccExtDeviceItem condition, int pageNo, int pageSize) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        return getItemsByPage(condition, pageNo, pageSize);
    }

    @Override
    public AccExtDeviceItem getItemById(String id) {
        if (StringUtils.isNotBlank(id)) {
            AccExtDeviceItem accExtDeviceItem = new AccExtDeviceItem();
            accExtDeviceItem.setId(id);
            List<AccExtDeviceItem> items = getByCondition(accExtDeviceItem);
            return (items != null && !items.isEmpty()) ? items.get(0) : null;
        }
        return null;
    }

    @Override
    public List<AccExtDeviceItem> getByCondition(AccExtDeviceItem condition) {
        return (List<AccExtDeviceItem>)accExtDeviceDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        Pager pager = accExtDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        if (condition instanceof AccExtDeviceItem) {
            List<AccExtDeviceItem> extDevItems = (List<AccExtDeviceItem>)pager.getData();
            String areaIds = CollectionUtil.getPropertys(extDevItems, AccExtDeviceItem::getAuthAreaId);
            List<AuthAreaItem> authAreaItemList = authAreaService.getItemsByIds(areaIds);
            Map<String, AuthAreaItem> itemMap = CollectionUtil.itemListToIdMap(authAreaItemList);
            extDevItems.forEach(extDevItem -> {
                if (StringUtils.isNotBlank(extDevItem.getAuthAreaId())) {
                    extDevItem.setAuthAreaName(itemMap.get(extDevItem.getAuthAreaId()).getName());
                }
            });
            pager.setData(extDevItems);
        }
        return pager;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<AccExtDevice> accExtDeviceList =
                accExtDeviceDao.findByIdList((List<String>)CollectionUtil.strToList(ids));
            for (AccExtDevice accExtDevice : accExtDeviceList) {
                if (AccConstants.EXT_BOARD_TYPE_DM10 == accExtDevice.getExtBoardType()) {
                    deleteDM10(accExtDevice);
                } else if (AccConstants.EXT_BOARD_TYPE_AUX485 == accExtDevice.getExtBoardType()) {
                    deleteAUX485(accExtDevice);
                } else if (AccConstants.EXT_BOARD_TYPE_EX0808 == accExtDevice.getExtBoardType()) {
                    deleteEX0808(accExtDevice);
                }
                // 更新设备信息中的门、辅助输入、辅助输出
                AccDevice accDevice = accDeviceDao.getOne(accExtDevice.getDevId());
                accDeviceService.updateDevInfoWithDoorAndAuxBySn(accDevice.getSn());
            }
        }
        return true;
    }

    /**
     * 删除dm10
     *
     * @param accExtDevice
     */
    private void deleteDM10(AccExtDevice accExtDevice) {
        AccDevice accDevice = accDeviceDao.getOne(accExtDevice.getDevId());
        List<AccDoor> doorList = accDoorDao.findByExtDevIdOrderByDoorNoAsc(accExtDevice.getId());
        List<String> doorIdList = (List<String>)CollectionUtil.getModelIdsList(doorList);
        // 删除电子地图绑定的门
        accMapPosService.delMapPosByEntityId(doorIdList, AccDoor.class.getSimpleName());
        // 删除首人开门设置
        List<AccFirstOpen> accFirstOpenList = accFirstOpenDao.findByAccDoor_IdIn(doorIdList);
        if (Objects.nonNull(accFirstOpenList) && accFirstOpenList.size() > 0) {
            accFirstOpenDao.delete(accFirstOpenList);
        }
        // 删除全局互锁
        if (acc4AccGlobalInterlockService != null) {
            acc4AccGlobalInterlockService.delGlobalInterlockDoorByDoorId(CollectionUtil.getModelIds(doorList));
        }

        List<AccReader> readerList = accReaderDao.findByExtDevIdOrderByReaderNoAsc(accExtDevice.getId());
        // 删除读头绑定的摄像头
        if (vid4OtherChannel2EntityService != null) {
            vid4OtherChannel2EntityService.deleteByEntityIdAndEntityClassName(CollectionUtil.getModelIds(readerList),
                AccReader.class.getSimpleName());
        }
        // 删除读头定义相关
        if (acc4AccReaderZoneService != null) {
            for (AccReader reader : readerList) {
                acc4AccReaderZoneService.delReaderZoneByReaderId(reader.getId());
            }
        }

        List<AccAuxIn> auxInList = accAuxInDao.findByExtDevIdOrderByAuxNoAsc(accExtDevice.getId());
        // 删除辅助输入绑定的摄像头
        if (vid4OtherChannel2EntityService != null) {
            vid4OtherChannel2EntityService.deleteByEntityIdAndEntityClassName(CollectionUtil.getModelIds(auxInList),
                AccAuxIn.class.getSimpleName());
        }

        List<AccAuxOut> auxOutList = accAuxOutDao.findByExtDevIdOrderByAuxNoAsc(accExtDevice.getId());
        List<Short> auxOutNoList = new ArrayList<>();
        for (AccAuxOut auxOut : auxOutList) {
            auxOutNoList.add(auxOut.getAuxNo());
        }
        accReaderDao.delete(readerList);
        accDoorDao.delete(doorList);
        accAuxInDao.delete(auxInList);
        accAuxOutDao.delete(auxOutList);
        accExtDeviceDao.delete(accExtDevice);

        // 删除扩展表关联表信息
        accDevCmdManager.delExtBoardRelationListFromDev(accDevice, accExtDevice, false);
        // 删除扩展表信息
        accDevCmdManager.delExtBoardPropertyFromDev(accDevice.getSn(), buildAccExtDeviceInfo(accExtDevice), false);
        // 删除门属性
        accDevCmdManager.delDoorPropertyFromDev(accDevice.getSn(), buildAccDoorItems(doorList), false);
        // 删除读头属性
        accDevCmdManager.delReaderPropertyFromDev(accDevice, readerList, false);
        // 删除辅助输入
        accDevCmdManager.delAuxInPropertyFromDev(accDevice.getSn(), buildAuxInItemListInfo(auxInList), false);
        // 删除辅助输出
        accDevCmdManager.delAuxOutPropertyFromDev(accDevice.getSn(), buildAuxOutItemListInfo(auxOutList), false);
        // 删除辅助输出设置
        accDevCmdManager.delAuxOutOptFromDev(accDevice.getSn(), auxOutNoList, false);
    }

    private AccExtDeviceItem buildAccExtDeviceInfo(AccExtDevice accExtDevice) {
        return ModelUtil.copyProperties(accExtDevice, new AccExtDeviceItem());
    }

    private List<AccDoorItem> buildAccDoorItems(List<AccDoor> doorList) {
        List<AccDoorItem> accDoorItemList = new ArrayList<>();
        if (Objects.nonNull(doorList) && !doorList.isEmpty()) {
            for (AccDoor door : doorList) {
                AccDoorItem doorItem = new AccDoorItem();
                ModelUtil.copyProperties(door, doorItem);
                AccDevice dev = door.getDevice();
                doorItem.setDeviceId(dev.getId());
                doorItem.setDeviceSn(dev.getSn());
                doorItem.setDevMachineType(dev.getMachineType() + "");
                accDoorItemList.add(doorItem);
            }
        }
        return accDoorItemList;
    }

    /**
     * 删除aux485
     *
     * @param accExtDevice
     */
    private void deleteAUX485(AccExtDevice accExtDevice) {
        AccDevice accDevice = accDeviceDao.getOne(accExtDevice.getDevId());
        List<AccAuxIn> auxInList = accAuxInDao.findByExtDevIdOrderByAuxNoAsc(accExtDevice.getId());
        accAuxInDao.delete(auxInList);
        accExtDeviceDao.delete(accExtDevice);
        // 删除扩展关联表
        accDevCmdManager.delExtBoardRelationListFromDev(accDevice, accExtDevice, false);
        // 删除扩展表信息
        accDevCmdManager.delExtBoardPropertyFromDev(accDevice.getSn(), buildAccExtDeviceInfo(accExtDevice), false);
        // 删除辅助输入
        accDevCmdManager.delAuxInPropertyFromDev(accDevice.getSn(), buildAuxInItemListInfo(auxInList), false);
    }

    /**
     * 删除ex0808
     *
     * @param accExtDevice
     */
    private void deleteEX0808(AccExtDevice accExtDevice) {
        AccDevice accDevice = accDeviceDao.getOne(accExtDevice.getDevId());
        List<AccAuxIn> auxInList = accAuxInDao.findByExtDevIdOrderByAuxNoAsc(accExtDevice.getId());
        accAuxInDao.delete(auxInList);
        List<AccAuxOut> auxOutList = accAuxOutDao.findByExtDevIdOrderByAuxNoAsc(accExtDevice.getId());
        List<Short> auxOutNoList = new ArrayList<>();
        for (AccAuxOut auxOut : auxOutList) {
            auxOutNoList.add(auxOut.getAuxNo());
        }
        accAuxOutDao.delete(auxOutList);
        accExtDeviceDao.delete(accExtDevice);

        // 删除扩展表关联表信息
        accDevCmdManager.delExtBoardRelationListFromDev(accDevice, accExtDevice, false);
        // 删除扩展表信息
        accDevCmdManager.delExtBoardPropertyFromDev(accDevice.getSn(), buildAccExtDeviceInfo(accExtDevice), false);
        // 删除辅助输入
        accDevCmdManager.delAuxInPropertyFromDev(accDevice.getSn(), buildAuxInItemListInfo(auxInList), false);
        // 删除辅助输出
        accDevCmdManager.delAuxOutPropertyFromDev(accDevice.getSn(), buildAuxOutItemListInfo(auxOutList), false);
        // 删除辅助输出设置
        accDevCmdManager.delAuxOutOptFromDev(accDevice.getSn(), auxOutNoList, false);
    }

    private List<AccAuxInItem> buildAuxInItemListInfo(List<AccAuxIn> auxInList) {
        List<AccAuxInItem> itemList = new ArrayList<>();
        for (AccAuxIn auxIn : auxInList) {
            AccAuxInItem item = ModelUtil.copyProperties(auxIn, new AccAuxInItem());
            item.setId(auxIn.getId());
            item.setDevId(auxIn.getAccDevice().getId());
            itemList.add(item);
        }
        return itemList;
    }

    private List<AccAuxOutItem> buildAuxOutItemListInfo(List<AccAuxOut> accAuxOutList) {
        List<AccAuxOutItem> itemList = new ArrayList<>();
        for (AccAuxOut auxOut : accAuxOutList) {
            AccAuxOutItem item = ModelUtil.copyProperties(auxOut, new AccAuxOutItem());
            item.setId(auxOut.getId());
            item.setDevId(auxOut.getAccDevice().getId());
            itemList.add(item);
        }
        return itemList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDM10(AccExtDeviceItem accExtDeviceItem) {
        if (StringUtils.isBlank(accExtDeviceItem.getId())) {
            if (accExtDeviceDao.countAccExtDeviceByDevIdAndExtBoardType(accExtDeviceItem.getDevId(),
                accExtDeviceItem.getExtBoardType()) >= AccConstants.EXT_BOARD_TYPE_DM10_LIMIT) {
                throw new ZKBusinessException(
                    I18nUtil.i18nCode("acc_dev_extBoardLimit", AccConstants.EXT_BOARD_TYPE_DM10_LIMIT));
            }
            if (!"zh_CN".equals(language)) {
                Map<String, Integer> countMap =
                    accDeviceService.getAfterLicensedCount(0, 1, AccConstants.COMM_TYPE_PUSH_HTTP);
                ResultCode resultCode =
                    baseLicenseProvider.isCountOutRangeAcc(ConstUtil.LICENSE_MODULE_ACC_PUSH, countMap);
                if (ResultCode.SUCCESS != resultCode) {
                    throw new ZKBusinessException(I18nUtil.i18nCode("acc_dev_Limit"));
                }
            }
            AccDevice accDevice = accDeviceDao.getOne(accExtDeviceItem.getDevId());

            AccExtDevice accExtDevice = new AccExtDevice();
            ModelUtil.copyProperties(accExtDeviceItem, accExtDevice);
            List<Short> curExtBoardNoList = accExtDeviceDao.findExtBoardNoByDevId(accDevice.getId());
            accExtDevice.setExtBoardNo(calEntityNo(curExtBoardNoList, 1).get(0));
            accExtDeviceDao.save(accExtDevice);

            String initTimeSegId = accTimeSegService.getInitTimeSegId();
            List<PersWiegandFmtItem> defalutWiegandFmt = persWiegandFmtService.getAutoMatch();
            boolean isInoutIOSet = accDeviceOptionService.getAccSupportFunListVal(accDevice.getId(), 8);// 是否支持出门按钮受时间段控制
            boolean isEncrypt = accDeviceOptionService.getAccSupportFunListVal(accDevice.getId(), 1);// 是否支持读头加密功能
            boolean isSupportReaderProtity = accDeviceOptionService.getAccSupportFunListVal(accDevice.getId(), 18);// 是否支持读头属性
            AccDoor accDoor = new AccDoor();
            accDoor.setDevice(accDevice);
            List<Short> curDoorNoList = accDoorDao.findDoorNoByDevId(accDevice.getId());
            List<Short> tarDoorNoList = calEntityNo(curDoorNoList, 1);
            short doorNo = tarDoorNoList.get(0);
            accDoor.setDoorNo(doorNo);
            accDoor.setName(accDevice.getAlias() + "-" + String.valueOf(doorNo));// 设备别名（IP）加上连字符以及门编号！！！
            accDoor.setBackLock(false);
            accDoor.setActionInterval((short)2);// 刷卡间隔，默认2秒
            accDoor.setLockDelay((short)5);// 锁驱动时长，默认5秒
            // verifyModeNo = ConstUtil.VERIFY_MODE_ONLYCARD;// 验证方式，默认为 仅卡，比如C3
            Short verifyModeNo = null;
            // 将新验证方式二进制数转为十进制数保存
            AccDeviceOptionItem newVFStyles =
                accDeviceOptionService.getItemByDevIdAndName(accDevice.getId(), "NewVFStyles");
            if (newVFStyles != null && StringUtils.isNotBlank(newVFStyles.getValue())) {
                verifyModeNo = (short)AccDeviceUtil.binaryToDecimal(newVFStyles.getValue());
            } else {
                AccDeviceOptionItem verifyStyles =
                    accDeviceOptionService.getItemByDevIdAndName(accDevice.getId(), "VerifyStyles");
                if (newVFStyles != null && StringUtils.isNotBlank(newVFStyles.getValue())) {
                    char[] binaryArry = AccDeviceUtil.getBinary(verifyStyles.getValue());// 十六进制转化为二进制
                    // 判断设备是否支持0号验证方式（自动识别）
                    if (binaryArry.length > 0 && binaryArry[0] == AccConstants.ENABLED) {
                        verifyModeNo = ConstUtil.VERIFY_MODE_CARDORFPORPWD;
                    }
                }
            }
            // 上面代码没能进行赋值说明老设备通过是否仅卡设备来决定默认验证方式
            if (verifyModeNo == null) {
                if (!accDeviceOptionService.isSupportFun(accDevice.getSn(), "~IsOnlyRFMachine")) {
                    verifyModeNo = ConstUtil.VERIFY_MODE_CARDANDFP;
                } else {
                    verifyModeNo = ConstUtil.VERIFY_MODE_ONLYCARD;
                }
            }
            accDoor.setVerifyMode(verifyModeNo);// 验证方式
            accDoor.setDoorSensorStatus((short)0);
            accDoor.setSensorDelay((short)15);
            accDoor.setReaderType((short)22);
            accDoor.setLatchDoorType((short)1);// 出门按钮不锁定
            if (isInoutIOSet) {
                accDoor.setLatchTimeSegId(initTimeSegId);
            }
            accDoor.setInApbDuration((short)0);
            accDoor.setWgInputFmtId(persWiegandFmtService.getDefAutoMatch().getId());
            accDoor.setWgOutputFmtId(defalutWiegandFmt.get(0).getId());
            accDoor.setDelayOpenTime((short)0);// 初始化开门延时时间（需要固件支持，否则该参数无效）
            accDoor.setExtDelayDrivertime((short)5);//// 针对残疾人，特定的开门时间
            accDoor.setIsDisableAudio(false);
            accDoor.setEnabled(true);
            accDoor.setCombOpenInterval((short)10);
            // 门有效时间段，默认24小时通行
            if (StringUtils.isNoneBlank(initTimeSegId)) {
                accDoor.setActiveTimeSegId(initTimeSegId);
            }
            accDoor.setWgReversed((short)0);// 设置默认wg反转模式，0为不反转
            accDoor.setExtDevId(accExtDevice.getId());
            accDoor.setAllowSUAccessLock("0");
            accDoorDao.save(accDoor);

            List<Short> curReaderNoList = accReaderDao.findReaderNoByDevId(accDevice.getId());
            List<Short> tarReaderNoList = calEntityNo(curReaderNoList, 2);
            AccReader accReaderIn = new AccReader();
            accReaderIn.setAccDoor(accDoor);
            accReaderIn.setReaderNo(tarReaderNoList.get(0));
            accReaderIn.setName(accDoor.getName() + "-" + I18nUtil.i18nCode(AccConstants.READER_IN_NAME));
            accReaderIn.setReaderState(AccConstants.STATE_IN);
            if (isSupportReaderProtity) { // 支持读头属性
                accReaderIn.setCommType(AccConstants.READER_TYPE_WGFMT);
                accReaderIn.setIp(StringUtils.EMPTY);
                accReaderIn.setMac(StringUtils.EMPTY);
                accReaderIn.setMulticast(StringUtils.EMPTY);
                accReaderIn.setPort(AccConstants.READER_DEFAULT_PORT);
            }
            if (isEncrypt) {
                accReaderIn.setReaderEncrypt(false);
            }
            accReaderIn.setExtDevId(accExtDevice.getId());
            accReaderDao.save(accReaderIn);

            // add reader 出
            AccReader accReaderOut = new AccReader();
            accReaderOut.setAccDoor(accDoor);
            accReaderOut.setReaderNo(tarReaderNoList.get(1));
            accReaderOut.setName(accDoor.getName() + "-" + I18nUtil.i18nCode(AccConstants.READER_OUT_NAME));// "门"需要国际化！！！
            accReaderOut.setReaderState(AccConstants.STATE_OUT);
            if (isSupportReaderProtity) { // 支持读头属性
                accReaderOut.setCommType(AccConstants.READER_TYPE_WGFMT);
                accReaderOut.setIp(StringUtils.EMPTY);
                accReaderOut.setMac(StringUtils.EMPTY);
                accReaderOut.setMulticast(StringUtils.EMPTY);
                accReaderOut.setPort(AccConstants.READER_DEFAULT_PORT);
            }
            if (isEncrypt) {
                accReaderOut.setReaderEncrypt(false);
            }
            accReaderOut.setExtDevId(accExtDevice.getId());
            accReaderDao.save(accReaderOut);
            // 添加辅助输入
            List<Short> curAuxInNoList = accAuxInDao.findAuxInNoByDevId(accDevice.getId());
            List<Short> tarAuxInNoList = calEntityNo(curAuxInNoList, 1);
            short auxInNo = tarAuxInNoList.get(0);
            AccAuxIn auxIn = new AccAuxIn();
            auxIn.setAccDevice(accDevice);
            auxIn.setAuxNo(auxInNo);
            auxIn.setName(I18nUtil.i18nCode(AccConstants.AUX_IN_NAME) + "-" + auxInNo);
            auxIn.setPrinterNumber("AUX IN");
            if (isInoutIOSet) {
                auxIn.setTimeSegId(initTimeSegId);
            }
            auxIn.setExtDevId(accExtDevice.getId());
            accAuxInDao.save(auxIn);
            // 添加辅助输出
            List<Short> curAuxOutNoList = accAuxOutDao.findAuxOutNoByDevId(accDevice.getId());
            List<Short> tarAuxOutNoList = calEntityNo(curAuxOutNoList, 1);
            short auxOutNo = tarAuxOutNoList.get(0);
            AccAuxOut auxOut = new AccAuxOut();
            auxOut.setAccDevice(accDevice);
            auxOut.setAuxNo(auxOutNo);
            auxOut.setName(I18nUtil.i18nCode(AccConstants.AUX_OUT_NAME) + "-" + auxOutNo);
            auxOut.setPrinterNumber("AUX OUT");
            auxOut.setExtDevId(accExtDevice.getId());
            accAuxOutDao.save(auxOut);

            // 下发扩展板表
            accDevCmdManager.setExtBoardPropertyToDev(accDevice, accExtDevice, false);
            // 下发门相关表
            List<AccDoor> doorList = new ArrayList<>();
            doorList.add(accDoor);
            accDevCmdManager.setDoorPropertyToDev(accDevice, doorList, false);
            AccDoorItem doorItem = ModelUtil.copyProperties(accDoor, new AccDoorItem());
            accDevCmdManager.setDoorOptionToDev(doorItem, false);
            // 读头属性表
            List<AccReader> readerList = new ArrayList<>();
            readerList.add(accReaderIn);
            readerList.add(accReaderOut);
            accDevCmdManager.setReaderOptToDev(accDevice, readerList, false);
            // 下发辅助输入属性表
            List<AccAuxIn> auxInList = new ArrayList<>();
            auxInList.add(auxIn);
            accDevCmdManager.setAuxInPropertyToDev(accDevice, auxInList, false);
            accDevCmdManager.setAuxInOptToDev(accDevice, auxInList, false);
            // 下发辅助输出属性表
            List<AccAuxOut> auxOutList = new ArrayList<>();
            auxOutList.add(auxOut);
            accDevCmdManager.setAuxOutPropertyToDev(accDevice, auxOutList, false);
            accDevCmdManager.setAuxOutOptToDev(accDevice, auxOutList, false);
            // 下发关联扩展板数据
            accDevCmdManager.setExtBoardRelationListToDev(accDevice, accExtDevice, false);
            // 下发设备扩展板协议类型
            accDevCmdManager.setRS485CommType(accDevice, accExtDeviceItem.getDevProtocolType(), false);
            // 更新设备信息中的门、辅助输入、辅助输出
            accDeviceService.updateDevInfoWithDoorAndAuxBySn(accDevice.getSn());
        } else {
            AccExtDevice accExtDevice = accExtDeviceDao.getOne(accExtDeviceItem.getId());
            ModelUtil.copyPropertiesIgnoreNull(accExtDeviceItem, accExtDevice);
            accExtDeviceDao.save(accExtDevice);
            AccDevice accDevice = accDeviceDao.getOne(accExtDeviceItem.getDevId());
            accDevCmdManager.setExtBoardPropertyToDev(accDevice, accExtDevice, false);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setAUX485(AccExtDeviceItem accExtDeviceItem) {
        if (StringUtils.isBlank(accExtDeviceItem.getId())) {
            if (accExtDeviceDao.countAccExtDeviceByDevIdAndExtBoardType(accExtDeviceItem.getDevId(),
                accExtDeviceItem.getExtBoardType()) >= AccConstants.EXT_BOARD_TYPE_AUX485_LIMIT) {
                throw new ZKBusinessException(
                    I18nUtil.i18nCode("acc_dev_extBoardLimit", AccConstants.EXT_BOARD_TYPE_AUX485_LIMIT));
            }
            AccDevice accDevice = accDeviceDao.getOne(accExtDeviceItem.getDevId());
            // 是否支持出门按钮受时间段控制
            boolean isInoutIOSet = accDeviceOptionService.getAccSupportFunListVal(accDevice.getId(), 8);
            AccExtDevice accExtDevice = new AccExtDevice();
            ModelUtil.copyProperties(accExtDeviceItem, accExtDevice);
            List<Short> curExtBoardNoList = accExtDeviceDao.findExtBoardNoByDevId(accDevice.getId());
            accExtDevice.setExtBoardNo(calEntityNo(curExtBoardNoList, 1).get(0));
            accExtDeviceDao.save(accExtDevice);
            // 添加辅助输入
            String initTimeSegId = accTimeSegService.getInitTimeSegId();
            List<Short> curAuxInNoList = accAuxInDao.findAuxInNoByDevId(accDevice.getId());
            List<Short> tarAuxInNoList = calEntityNo(curAuxInNoList, 4);
            List<AccAuxIn> auxInList = new ArrayList<>();
            for (int i = 0; i < tarAuxInNoList.size(); i++) {
                AccAuxIn auxIn = new AccAuxIn();
                auxIn.setAccDevice(accDevice);
                auxIn.setAuxNo(tarAuxInNoList.get(i));
                auxIn.setName(I18nUtil.i18nCode(AccConstants.AUX_IN_NAME) + "-" + tarAuxInNoList.get(i));
                auxIn.setPrinterNumber("AUX" + (i + 1));
                if (isInoutIOSet) {
                    auxIn.setTimeSegId(initTimeSegId);
                }
                auxIn.setExtDevId(accExtDevice.getId());
                auxInList.add(accAuxInDao.save(auxIn));
            }
            // 下发扩展板表
            accDevCmdManager.setExtBoardPropertyToDev(accDevice, accExtDevice, false);
            // 下发辅助输入属性表
            accDevCmdManager.setAuxInPropertyToDev(accDevice, auxInList, false);
            accDevCmdManager.setAuxInOptToDev(accDevice, auxInList, false);
            // 下发关联扩展板数据
            accDevCmdManager.setExtBoardRelationListToDev(accDevice, accExtDevice, false);
            // 下发设备扩展板协议类型
            accDevCmdManager.setRS485CommType(accDevice, accExtDeviceItem.getDevProtocolType(), false);
            // 更新设备信息中的门、辅助输入、辅助输出
            accDeviceService.updateDevInfoWithDoorAndAuxBySn(accDevice.getSn());
        } else {
            AccExtDevice accExtDevice = accExtDeviceDao.getOne(accExtDeviceItem.getId());
            ModelUtil.copyPropertiesIgnoreNull(accExtDeviceItem, accExtDevice);
            accExtDeviceDao.save(accExtDevice);
            AccDevice accDevice = accDeviceDao.getOne(accExtDeviceItem.getDevId());
            accDevCmdManager.setExtBoardPropertyToDev(accDevice, accExtDevice, false);
        }
    }

    @Override
    public boolean isExistAlias(String alias) {
        AccExtDevice accExtDevice = accExtDeviceDao.findByAlias(alias);
        return accExtDevice == null;
    }

    @Override
    public boolean isExistAddress(Short commAddress, String devId) {
        int filterExtDeviceCount = accExtDeviceDao.countAccExtDeviceByDevIdAndCommAddress(devId, commAddress);
        if (filterExtDeviceCount > 0) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setEX0808(AccExtDeviceItem accExtDeviceItem) {
        if (StringUtils.isBlank(accExtDeviceItem.getId())) {
            if (accExtDeviceDao.countAccExtDeviceByDevIdAndExtBoardType(accExtDeviceItem.getDevId(),
                accExtDeviceItem.getExtBoardType()) >= AccConstants.EXT_BOARD_TYPE_EX0808_LIMIT) {
                throw new ZKBusinessException(
                    I18nUtil.i18nCode("acc_dev_extBoardLimit", AccConstants.EXT_BOARD_TYPE_EX0808_LIMIT));
            }
            AccDevice accDevice = accDeviceDao.getOne(accExtDeviceItem.getDevId());

            AccExtDevice accExtDevice = new AccExtDevice();
            ModelUtil.copyProperties(accExtDeviceItem, accExtDevice);
            List<Short> curExtBoardNoList = accExtDeviceDao.findExtBoardNoByDevId(accDevice.getId());
            accExtDevice.setExtBoardNo(calEntityNo(curExtBoardNoList, 1).get(0));
            accExtDeviceDao.save(accExtDevice);

            // 是否支持出门按钮受时间段控制
            boolean isInoutIOSet = accDeviceOptionService.getAccSupportFunListVal(accDevice.getId(), 8);
            // 添加辅助输入
            String initTimeSegId = accTimeSegService.getInitTimeSegId();
            List<Short> curAuxInNoList = accAuxInDao.findAuxInNoByDevId(accDevice.getId());
            List<Short> tarAuxInNoList = calEntityNo(curAuxInNoList, 8);
            List<AccAuxIn> auxInList = new ArrayList<>();
            for (int i = 0; i < tarAuxInNoList.size(); i++) {
                AccAuxIn auxIn = new AccAuxIn();
                auxIn.setAccDevice(accDevice);
                auxIn.setAuxNo(tarAuxInNoList.get(i));
                auxIn.setName(I18nUtil.i18nCode(AccConstants.AUX_IN_NAME) + "-" + tarAuxInNoList.get(i));
                auxIn.setPrinterNumber("AUX" + (i + 1));
                if (isInoutIOSet) {
                    auxIn.setTimeSegId(initTimeSegId);
                }
                auxIn.setExtDevId(accExtDevice.getId());
                auxInList.add(accAuxInDao.save(auxIn));
            }
            // 添加辅助输出
            List<Short> curAuxOutNoList = accAuxOutDao.findAuxOutNoByDevId(accDevice.getId());
            List<Short> tarAuxOutNoList = calEntityNo(curAuxOutNoList, 8);
            List<AccAuxOut> auxOutList = new ArrayList<>();
            for (int i = 0; i < tarAuxOutNoList.size(); i++) {
                AccAuxOut auxOut = new AccAuxOut();
                auxOut.setAccDevice(accDevice);
                auxOut.setAuxNo(tarAuxOutNoList.get(i));
                auxOut.setName(I18nUtil.i18nCode(AccConstants.AUX_OUT_NAME) + "-" + tarAuxOutNoList.get(i));
                auxOut.setPrinterNumber("AUXOUT" + (i + 1));
                auxOut.setExtDevId(accExtDevice.getId());
                auxOutList.add(accAuxOutDao.save(auxOut));
            }
            // 下发扩展板表
            accDevCmdManager.setExtBoardPropertyToDev(accDevice, accExtDevice, false);
            // 下发辅助输入属性表
            accDevCmdManager.setAuxInPropertyToDev(accDevice, auxInList, false);
            accDevCmdManager.setAuxInOptToDev(accDevice, auxInList, false);
            // 下发辅助输出属性表
            accDevCmdManager.setAuxOutPropertyToDev(accDevice, auxOutList, false);
            accDevCmdManager.setAuxOutOptToDev(accDevice, auxOutList, false);
            // 下发关联扩展板数据
            accDevCmdManager.setExtBoardRelationListToDev(accDevice, accExtDevice, false);
            // 下发设备扩展板协议类型
            accDevCmdManager.setRS485CommType(accDevice, accExtDeviceItem.getDevProtocolType(), false);
            // 更新设备信息中的门、辅助输入、辅助输出
            accDeviceService.updateDevInfoWithDoorAndAuxBySn(accDevice.getSn());
        } else {
            AccExtDevice accExtDevice = accExtDeviceDao.getOne(accExtDeviceItem.getId());
            ModelUtil.copyPropertiesIgnoreNull(accExtDeviceItem, accExtDevice);
            accExtDeviceDao.save(accExtDevice);
            AccDevice accDevice = accDeviceDao.getOne(accExtDeviceItem.getDevId());
            accDevCmdManager.setExtBoardPropertyToDev(accDevice, accExtDevice, false);
        }
    }

    @Override
    public List<AccExtDeviceItem> getItemByIds(String extDevIds) {
        List<AccExtDeviceItem> accExtDeviceItems = new ArrayList<>();
        if (StringUtils.isNotBlank(extDevIds)) {
            List<AccExtDevice> accExtDeviceList = accExtDeviceDao.findByIdList(CollectionUtil.strToList(extDevIds));
            if (accExtDeviceList.size() > 0 && accExtDeviceList != null) {
                accExtDeviceItems = ModelUtil.copyListProperties(accExtDeviceList, AccExtDeviceItem.class);
            }
        }
        return accExtDeviceItems;
    }

    /**
     * 计算新增的实体对应的编号，编号规则为正整数自增，删除需要补缺
     *
     * @param curEntityNoList
     * @param count
     * @return
     */
    private List<Short> calEntityNo(List<Short> curEntityNoList, int count) {
        Map<Short, Integer> map = new HashMap<>();
        List<Short> entityNoList = new ArrayList<>();
        for (Short no : curEntityNoList) {
            map.put(no, 1);
        }
        short i = 1;
        while (entityNoList.size() < count) {
            if (!map.containsKey(i)) {
                entityNoList.add(i);
            }
            i++;
        }
        return entityNoList;
    }

    @Override
    public Short getDevProtocolType(String devId) {
        List<AccExtDevice> accExtDeviceList = accExtDeviceDao.findByDevIdIn(CollectionUtil.strToList(devId));
        if (accExtDeviceList != null && accExtDeviceList.size() > 0) {
            AccExtDevice accExtDevice = accExtDeviceList.get(0);
            return accExtDevice.getDevProtocolType();
        }
        return null;
    }

    @Override
    public Pager getSimpleItemsByPage(BaseItem condition, int page, int size) {
        return accExtDeviceDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccExtDeviceItem saveSimpleItem(AccExtDeviceItem item) {
        AccExtDevice accExtDevice = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> accExtDeviceDao.findById(id)).orElse(new AccExtDevice());
        ModelUtil.copyPropertiesIgnoreNull(item, accExtDevice);

        // 保存入库
        accExtDeviceDao.save(accExtDevice);
        item.setId(accExtDevice.getId());
        return item;
    }

    @Override
    public List<Short> getExtBoardNoByDevId(String devId) {
        return accExtDeviceDao.findExtBoardNoByDevId(devId);
    }

    @Override
    public List<AccExtDeviceItem> getItemsByDevId(String devId) {
        AccExtDevice extDevice = new AccExtDevice();
        extDevice.setDevId(devId);
        List<AccExtDevice> accExtDeviceList = accExtDeviceDao.findByExample(extDevice);
        List<AccExtDeviceItem> accExtDeviceItemList = new ArrayList<>();
        if (!accExtDeviceList.isEmpty()) {
            accExtDeviceItemList = ModelUtil.copyListProperties(accExtDeviceList, AccExtDeviceItem.class);
        }
        return accExtDeviceItemList;
    }
}
