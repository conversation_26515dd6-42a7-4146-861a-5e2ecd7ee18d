/**
 * File Name: AccDoor Created by GenerationTools on 2018-03-03 上午11:59 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccDoor;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccDoorDao
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-03 上午11:59
 */
public interface AccDoorDao extends BaseDao<AccDoor, String> {

    List<AccDoor> findByDevice_Id(String deviceId);

    List<AccDoor> findByDevice_IdIn(List<String> devIdList);

    @Query("select e.id, e.name from AccDoor e where e.device.id=?1 order by e.id")
    List<Object[]> getDoorInfoByDevId(String deviceId);

    @Query(value = "select d.id from AccDoor d where id in (?1)")
    List<String> getIdByDoorId(List<String> doorIds);

    List<AccDoor> findByIdIn(List<String> doorIds);

    @Query(value = "select d.device.id from AccDoor d where d.id in (?1)")
    List<String> getDevIdByDoorId(List<String> doorIdList);

    @Query(value = "select e from AccDoor e where e.device.id in (?1)")
    List<AccDoor> getByDevId(List<String> devIdList);

    // select device.id from AccDoor where id in (select door.id from AccReader where id in (:list))
    // @Query(value = "select e.device.id from AccDoor e where e.id in (select r.door.id from AccReader r where r.id in
    // (?1))")
    @Query(
        value = "select e.DEV_ID from ACC_DOOR e where e.ID in (select r.DOOR_ID from ACC_READER r where r.ID in (?1))",
        nativeQuery = true)
    List<String> getDevIdListByReaderIdList(List<String> readerIdList);

    @Query(value = "SELECT t.id FROM AccDoor t WHERE t.device.commType in (?1)")
    List<String> getDoorIdByCommType(List<Short> commType);

    @Query(value = "SELECT t.id FROM AccDoor t WHERE t.device.machineType in (?1)")
    List<String> getDoorIdByMachineType(List<Short> machineType);

    @Query(value = "SELECT id FROM AccDoor e where e.device.id = ?1")
    List<String> getIdByDevId(String deviceId);

    AccDoor findByDoorNoAndDevice_Id(short eventAddr, String devId);

    /**
     * @Description: 根据门id获取绑定的一体机当读头的门id（一体机当读头方案中使用）
     * @author: mingfa.zheng
     * @date: 2018/7/9 14:19
     * @param: [doorIdList]
     * @return: java.util.List<java.lang.String>
     **/
    @Query(
        value = "select d.id from AccDoor d where d.device.id in (select distinct e.id from AccDevice e where e.wgReaderId in (select r.id from AccReader r where r.accDoor.id in (?1)))")
    List<String> getDoorIdsAsWGReaderByDoorId(List<String> doorIdList);

    /**
     * @Description: 查询某权限组中要操作的门绑定的读头一体机的门id
     * @author: mingfa.zheng
     * @date: 2018/7/9 14:30
     * @param: [levelId, doorIdList]
     * @return: java.util.List<java.lang.String>
     **/
    @Query(value = "select d.id from AccDoor d join d.accLevelList l where l.accLevel.id = ?1 "
        + "and d.device.id in (select distinct e.id from AccDevice e where e.wgReaderId in (select r.id from AccReader r where r.accDoor.id in (?2)))")
    List<String> getDoorIdsAsWGReaderByDoorIdAndLevelId(String levelId, List<String> doorIdList);

    AccDoor findByName(String name);

    @Query(value = "SELECT e FROM AccDoor e WHERE e.device.id = ?1 and e.id <> ?2 ")
    List<AccDoor> getOtherDoorByDevId(String deviceId, String doorId);

    @Query(value = "SELECT e FROM AccDoor e WHERE e.device.id in (?1) and e.id <> ?2 ")
    List<AccDoor> getDoorByIdAndDevId(List<String> devIdsList, String doorId);

    @Query(value = "SELECT e FROM AccDoor e WHERE e.id <> ?1 and e.device.machineType = ?2 ")
    List<AccDoor> getOtherDoorByMacType(String doorId, Short machineType);

    /**
     * 根据门id获取设备列表
     *
     * @author: mingfa.zheng
     * @date: 2018/4/28 11:30
     * @return:
     */
    // select distinct dev from AccDoor d join d.device dev where d.id in (:list)
    @Query(value = "select distinct dev from AccDoor d join d.device dev where d.id in (?1)")
    List<AccDevice> getDeviceByDoorId(List<String> doorIdList);

    /**
     * 根据人事密码查询是否存在门的胁迫密码
     *
     * @author: mingfa.zheng
     * @date: 2018/5/23 9:57
     * @return:
     */
    Long countByForcePwd(String personPwd);

    int countByActiveTimeSegId(String accTimeSegId);

    int countByPassModeTimeSegId(String accTimeSegId);

    int countByLatchTimeSegId(String accTimeSegId);

    List<AccDoor> findByIdInAndDoorSensorStatusIn(List<String> doorIdList, List<Short> doorSensorStatusList);

    List<AccDoor> findByWgInputFmtIdIn(List<String> wgInputFmtIdList);

    List<AccDoor> findByWgInputFmtIdAndDevice_Id(String wgInputFmtId, String deviceId);

    List<AccDoor> findByDevice_CommType(short commType);

    List<AccDoor> findByDevice_CommTypeIn(List<Short> devType);

    /**
     * 查询设置为控制器的韦根读头的一体机的门id
     *
     * @author: mingfa.zheng
     * @date: 2018/6/27 9:44
     * @return:
     */
    @Query(value = "select id from AccDoor where device.machineType = ?1 and device.wgReaderId is not null")
    List<String> getDoorIdAsReader(Short machineType);

    @Query(value = "select e from AccDoor e order by e.device.businessId asc, e.doorNo asc")
    List<AccDoor> getAllOrderByDevice();

    /**
     * 获取人员所属设备--权限组
     *
     * @author: mingfa.zheng
     * @date: 2018/7/5 9:44
     * @return:
     */
    @Query(
        value = "select distinct d.device from AccDoor d join d.accLevelList l join l.accLevel al join al.accPersonList p where p.persPersonId in (?1)")
    List<AccDevice> getDevByPersonLevel(List<String> personIds);

    List<AccDoor> findByDevice_Sn(String sn);

    /**
     * 根据sns集合获取门
     *
     * @param sns
     * @return
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:28:05
     */
    List<AccDoor> findByDevice_SnIn(Collection<String> sns);

    /**
     * 根据doorNo、sn获取门
     *
     * @param doorNo
     * @param sn
     * @return
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:28:05
     */
    AccDoor findByDoorNoAndDevice_Sn(short doorNo, String sn);

    /**
     * 根据区域id获取门
     *
     * @param areaIds
     * @return
     */
    List<AccDoor> findByDevice_AuthAreaIdIn(Collection<String> areaIds);

    /**
     * 迁移代码： 根据doorNo、sn获取门
     *
     * @param doorNoList
     * @param sn
     * @return
     * @Description:
     * <AUTHOR>
     * @since 2018年12月14日 上午10:28:05
     */
    List<AccDoor> findByDoorNoInAndDevice_Sn(List<Short> doorNoList, String sn);

    /**
     * 根据扩展板id查询门
     *
     * @param extDevId
     * @return
     */
    List<AccDoor> findByExtDevIdOrderByDoorNoAsc(String extDevId);

    @Query(value = "select e.doorNo from AccDoor e where e.device.id = ?1 order by e.doorNo")
    List<Short> findDoorNoByDevId(String devId);

    List<AccDoor> findByNameIn(Collection<String> importDoorNames);
}