/**
 * <AUTHOR>
 * @date 2020/3/27 11:29
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Date;
import java.util.List;

import com.zkteco.zkbiosecurity.acc.model.AccFirstInLastOut;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
public interface AccFirstInLastOutDao extends BaseDao<AccFirstInLastOut, String> {

    List<AccFirstInLastOut> findByPinAndFirstInTimeGreaterThanEqualAndFirstInTimeLessThanEqual(String pin,
        Date todayBeginTime, Date todayEndTime);
}
