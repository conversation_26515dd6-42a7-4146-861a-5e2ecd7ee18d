package com.zkteco.zkbiosecurity.acc.task;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.service.AccFirstInLastOutService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ScheduledFuture;

@Component
public class AccCheckAttShiftTask {


    @Autowired
    private ScheduleService scheduleService;

    private ScheduledFuture<?> scedulefuture;

    @Autowired
    AccTransactionService accTransactionService;

    public void initAccCheckAttShiftTask() {
        if (scedulefuture != null) {
            scedulefuture.cancel(true);
        }
        scedulefuture = scheduleService
                .startScheduleTask(() -> accTransactionService.checkExceptionRecord(), "0 0/1 * * * ?");
    }
}
