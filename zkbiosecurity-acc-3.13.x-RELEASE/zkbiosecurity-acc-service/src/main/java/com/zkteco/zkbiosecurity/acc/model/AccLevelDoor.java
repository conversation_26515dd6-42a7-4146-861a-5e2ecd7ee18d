package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁权限组-门中间表
 * 
 * <AUTHOR>
 * @date 2018/3/14 16:54
 */
@Entity
@Table(name = "ACC_LEVEL_DOOR", indexes = {@Index(columnList = "LEVEL_ID"), @Index(columnList = "DOOR_ID")})
@Getter
@Setter
@Accessors(chain = true)
public class AccLevelDoor extends BaseModel implements Serializable {

    @ManyToOne
    @JoinColumn(name = "LEVEL_ID", nullable = false)
    private AccLevel accLevel;

    @ManyToOne
    @JoinColumn(name = "DOOR_ID", nullable = false)
    private AccDoor accDoor;

    public AccLevelDoor() {}

    public AccLevelDoor(AccLevel accLevel, AccDoor accDoor) {
        this.accLevel = accLevel;
        this.accDoor = accDoor;
    }
}
