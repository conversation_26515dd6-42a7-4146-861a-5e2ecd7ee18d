/**
 * File Name: AccInterlockServiceImpl Created by GenerationTools on 2018-03-13 上午09:53 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao;
import com.zkteco.zkbiosecurity.acc.dao.AccDoorDao;
import com.zkteco.zkbiosecurity.acc.dao.AccInterlockDao;
import com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupDao;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccInterlockItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTriggerGroupItem;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.cmd.base.constants.CmdConstatns;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

/**
 * 对应百傲瑞达 AccInterlockServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-13 上午09:53
 * @version v1.0
 */
@Service
public class AccInterlockServiceImpl implements AccInterlockService {
    @Autowired
    private AccInterlockDao accInterlockDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccTriggerGroupService accTriggerGroupService;
    @Autowired
    private AccTriggerGroupDao accTriggerGroupDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccInterlockItem saveItem(AccInterlockItem item) {
        AccInterlock accInterlock = Optional.ofNullable(item).map(AccInterlockItem::getId)
            .filter(StringUtils::isNotBlank).flatMap(id -> accInterlockDao.findById(id)).orElse(new AccInterlock());
        ModelUtil.copyPropertiesIgnoreNull(item, accInterlock);
        // 新老互锁兼容处理
        if (accInterlock.getInterlockRule() != null) {
            accInterlock.setLockRule(null);
        }

        if (!validInterlockRepeat(item)) {
            throw new ZKBusinessException("acc_interlock_alreadyExists");
        }

        if (StringUtils.isBlank(accInterlock.getId())) {
            accInterlock.setBusinessId(createBID());
        }
        AccDevice accDevice = accDeviceDao.findById(item.getDeviceId()).orElse(new AccDevice());
        accInterlock.setAccDevice(accDevice);
        if (accInterlock.getLockRule() != null){
            if (StringUtils.isNotBlank(item.getGroup2DoorIds())) {
                accInterlock.setLockRule(Short.valueOf("1"));
            } else {
                accInterlock.setLockRule(Short.valueOf("2"));
            }
            // 2. 维护触发点组
            String group1Id = accTriggerGroupService.updateTriggerGroup(item.getGroup1DoorIds(),
                getGroupIdOrNull(accInterlock.getTriggerGroup1()), AccTriggerGroupItem.TRIGGER_TYPE_DOOR);
            String group2Id = accTriggerGroupService.updateTriggerGroup(item.getGroup2DoorIds(),
                getGroupIdOrNull(accInterlock.getTriggerGroup2()), AccTriggerGroupItem.TRIGGER_TYPE_DOOR);
            // 3. 新的触发点组
            accInterlock.setTriggerGroup1(getGroupByIdOrNull(group1Id));
            accInterlock.setTriggerGroup2(getGroupByIdOrNull(group2Id));
            accInterlock = accInterlockDao.save(accInterlock);
        }
        accInterlockDao.save(accInterlock);
        item.setId(accInterlock.getId());
        accDevCmdManager.setInterlockToDev(accDevice.getSn(), accInterlock, false);
        return item;
    }

    @Override
    public List<AccInterlockItem> getByCondition(AccInterlockItem condition) {
        return (List<AccInterlockItem>)accInterlockDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accInterlockDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                AccInterlock accInterlock = accInterlockDao.findOne(id);
                if (accInterlock != null) {
                    AccDevice dev = accInterlock.getAccDevice();
                    Long bId = accInterlock.getBusinessId();
                    accInterlockDao.delete(accInterlock);
                    accDevCmdManager.delInterlockFromDev(dev.getSn(), bId, false);
                }
            }
        }
        return false;
    }

    @Override
    public AccInterlockItem getItemById(String id) {
        List<AccInterlockItem> items = getByCondition(new AccInterlockItem(id));
        AccInterlockItem item =
            Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
        return completionItem(item);
    }

    @Override
    public String getDevIdWithInterlock() {
        StringBuilder stringBuilder = new StringBuilder();
        List<AccInterlock> accInterlockList = accInterlockDao.findAll();
        for (AccInterlock accInterlock : accInterlockList)//
        {
            if (!validDetermineInterlock(accInterlock.getAccDevice().getId())) {
                stringBuilder.append(accInterlock.getAccDevice().getId()).append(",");
            }
        }
        List<AccDevice> accDeviceList = accDeviceDao.findAll();
        for (AccDevice accDevice : accDeviceList)// 不支持互锁的设备
        {
            int lockCount = Integer.parseInt(accDeviceOptionService.getOptVal(accDevice.getSn(), "LockCount"));
            // 无线锁和一体机不支持互锁
            if (lockCount <= 1) {
                stringBuilder.append(accDevice.getId()).append(",");
            }
        }
        return StringUtils.isBlank(stringBuilder) ? "" : stringBuilder.substring(0, stringBuilder.length() - 1);
    }

    @Override
    public Map<Integer, String> getRule(String deviceId) {
        List<AccDoor> accDoorList = accDoorDao.findByDevice_Id(deviceId);
        Map<Integer, String> doorMap = new HashMap<>();
        for (AccDoor accDoor : accDoorList) {
            if (StringUtils.isBlank(accDoor.getExtDevId())) {
                doorMap.put((int)accDoor.getDoorNo(), accDoor.getName());
            }
        }
        return doorMap;
    }

    @Override
    public String convertInterlockRule(String id) {
        String ret = "";
        AccInterlock accInterlock = accInterlockDao.findOne(id);
        Map<Integer, String> doorMap = new HashMap<>();
        List<AccDoor> accDoorList = accInterlock.getAccDevice().getAccDoorList();
        if (accInterlock.getInterlockRule() != null) {
            short interlockRule = accInterlock.getInterlockRule();
            for (AccDoor door : accDoorList) {
                if (StringUtils.isBlank(door.getExtDevId())) {
                    doorMap.put((int)door.getDoorNo(), door.getName());
                }
            }
            if (doorMap.size() > 1) {// 当前控制器为两门
                if (interlockRule == 1) {
                    ret = I18nUtil.i18nCode("acc_interlock_mode1Or2", doorMap.get(1), doorMap.get(2));// 1-2 门互锁
                }
            }
            if (doorMap.size() == 4) {// 当前控制器为四门
                if (interlockRule == 2) {
                    ret = I18nUtil.i18nCode("acc_interlock_mode1Or2", doorMap.get(3), doorMap.get(4));// 3-4门互锁
                } else if (interlockRule == 3) {
                    ret = I18nUtil.i18nCode("acc_interlock_mode3", doorMap.get(1), doorMap.get(2), doorMap.get(3));// 1-2-3
                                                                                                                   // 门互锁
                } else if (interlockRule == 4) {
                    ret = I18nUtil.i18nCode("acc_interlock_mode4", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                        doorMap.get(4));// 1-2 门互锁,3-4门互锁
                } else if (interlockRule == 5) {
                    ret = I18nUtil.i18nCode("acc_interlock_mode5", doorMap.get(1), doorMap.get(2), doorMap.get(3),
                        doorMap.get(4));// 1-2-3-4 门互锁
                }
            }
        }
        return ret;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delInterLockByDevId(String devId) {
        List<AccInterlock> accInterlocks = accInterlockDao.findByAccDevice_Id(devId);
        if (ObjectUtils.isNotEmpty(accInterlocks)) {
            accInterlocks.forEach(accInterlock -> {
                deleteByIds(accInterlock.getId());
            });
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerTransfer(List<AccInterlockItem> accInterlockItems) {
        // 避免大数据量 进行分批处理
        List<List<AccInterlockItem>> accInterlockItemList =
            CollectionUtil.split(accInterlockItems, CollectionUtil.splitSize);
        for (List<AccInterlockItem> accInterlocks : accInterlockItemList) {
            Collection<String> devIds =
                CollectionUtil.getPropertyList(accInterlocks, AccInterlockItem::getDeviceId, "-1");
            // 获取互锁对象集合
            List<AccInterlock> accInterlockList = accInterlockDao.findAll();
            Map<AccDevice, AccInterlock> accInterlockMap =
                CollectionUtil.listToKeyMap(accInterlockList, AccInterlock::getAccDevice);

            // List<String>转List<Long>
            List<Long> longList = new ArrayList<>();
            for (String str : devIds) {
                Long i = Long.parseLong(str);
                longList.add(i);
            }
            // 获取设备对象集合
            List<AccDevice> accDeviceList = accDeviceDao.findByBusinessIdIn(longList);
            Map<Long, AccDevice> accDeviceMap = CollectionUtil.listToKeyMap(accDeviceList, AccDevice::getBusinessId);
            for (AccInterlockItem accInterlockItem : accInterlocks) {
                // 获取设备对象
                AccDevice accDevice = accDeviceMap.get(Long.valueOf(accInterlockItem.getDeviceId()));
                // 去重
                AccInterlock accInterlock = accInterlockMap.remove(accDevice);
                if (Objects.isNull(accInterlock)) {
                    accInterlock = new AccInterlock();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accInterlockItem, accInterlock, "id");
                if (Objects.nonNull(accDevice)) {
                    accInterlock.setAccDevice(accDevice);
                }
                accInterlockDao.save(accInterlock);
            }
        }
    }

    @Override
    public List<AccInterlockItem> getItemsByDevId(String devId) {
        List<AccInterlockItem> accInterlockItems = new ArrayList<>();
        if (StringUtils.isNotBlank(devId)) {
            AccInterlockItem accInterlockItem = new AccInterlockItem();
            accInterlockItem.setDeviceId(devId);
            accInterlockItems = getByCondition(accInterlockItem);
        }
        return accInterlockItems;
    }

    @Override
    public Pager loadPagerByAuthFilter(String sessionId, AccInterlockItem condition, int page, int size) {
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager itemsPage = new Pager();
        List<AccInterlockItem> interLockItemList = (List<AccInterlockItem>)accInterlockDao
            .getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        interLockItemList = interLockItemList.stream().map(item -> completionItem(item)).collect(Collectors.toList());
        // 根据区域Id过滤查询数据
        /*if (StringUtils.isNotBlank(authAreaIds)) {
            AccDeviceItem devItem = new AccDeviceItem();
            devItem.setAreaIdIn(authAreaIds);
            List<AccDeviceItem> authDevice = accDeviceService.getByCondition(devItem);
            List<String> devIdArr = authDevice.stream().map(AccDeviceItem::getId).collect(Collectors.toList());
            String devIdIn = StringUtils.join(devIdArr.toArray(), ",");
            interLockItemList = interLockItemList.stream().filter(item -> devIdIn.contains(item.getDeviceId())).collect(Collectors.toList());
        }*/
        itemsPage.setTotal(interLockItemList.size());
        // 获取分页查询最后一个数据的下标
        int end = interLockItemList.size() > page * size + size ? page * size + size : interLockItemList.size();
        itemsPage.setData(interLockItemList.subList(page * size, end));
        itemsPage.setPage(page);
        itemsPage.setSize(size);
        return itemsPage;
    }

    @Override
    public boolean nameExists(String name) {
        if (StringUtils.isNotBlank(name)) {
            return accInterlockDao.existsByName(name);
        }
        return false;
    }

    @Override
    public String getLockCountByDevice(String devId) {
        if (StringUtils.isNotBlank(devId)) {
            AccDeviceItem devItem = accDeviceService.getItemById(devId);
            if (devItem != null && StringUtils.isNotBlank(devItem.getSn())) {
                return accDeviceOptionService.getValueByNameAndDevSn(devItem.getSn(), CmdConstatns.LOCK_COUNT);
            }
        }
        return null;
    }

    private String getGroupIdOrNull(AccTriggerGroup oldGroup) {
        return oldGroup == null ? null : oldGroup.getId();
    }

    private AccTriggerGroup getGroupByIdOrNull(String id) {
        if (StringUtils.isNotBlank(id)) {
            return accTriggerGroupDao.findById(id).orElse(null);
        }
        return null;
    }

    /**
     * 补全item的设备id，设备名，组1，组2
     *
     * @param item
     * @return
     */
    @Override
    public AccInterlockItem completionItem(AccInterlockItem item) {
        if (item != null && StringUtils.isNotBlank(item.getId()) && item.getLockRule() != null) {
            AccInterlock interlock = accInterlockDao.findById(item.getId()).get();
            String[][] group1DoorIdAndName = getGroupDoorIdAndName(interlock.getTriggerGroup1());
            String[][] group2DoorIdAndName = getGroupDoorIdAndName(interlock.getTriggerGroup2());
            item.setGroup1DoorNames(StringUtils.join(group1DoorIdAndName[1], ","));
            item.setGroup2DoorNames(StringUtils.join(group2DoorIdAndName[1], ","));
            item.setGroup1DoorIds(StringUtils.join(group1DoorIdAndName[0], ","));
            item.setGroup2DoorIds(StringUtils.join(group2DoorIdAndName[0], ","));
            item.setGroup1BusinessId(
                interlock.getTriggerGroup1() != null ? interlock.getTriggerGroup1().getBussinessId() : null);
            item.setGroup2BusinessId(
                interlock.getTriggerGroup2() != null ? interlock.getTriggerGroup2().getBussinessId() : null);
            // 回填设备
            List<AccDeviceItem> devList = new ArrayList<>();
            List<AccDeviceItem> groupDevs = getGroupDevs(interlock.getTriggerGroup1());
            devList.addAll(groupDevs);
            groupDevs = getGroupDevs(interlock.getTriggerGroup2());
            devList.addAll(groupDevs);
            final Set<String> devIds = devList.stream().map(AccDeviceItem::getId).collect(Collectors.toSet());
            item.setDeviceId(StringUtils.join(devIds, ","));
            final Set<String> devNames = devList.stream().map(AccDeviceItem::getAlias).collect(Collectors.toSet());
            item.setDeviceName(StringUtils.join(devNames, ","));
        }
        return item;
    }

    /**
     * 构造触发点组的门id和门名称
     *
     * @param accProTriggerGroup
     * @return
     */
    private String[][] getGroupDoorIdAndName(AccTriggerGroup accProTriggerGroup) {
        String[][] doorVal = new String[2][];
        if (accProTriggerGroup != null) {
            Set<AccTriggerGroupAddr> addrSet = accProTriggerGroup.getAddrSet();
            List<String> doorIdList =
                addrSet.stream().map(AccTriggerGroupAddr::getAddress).collect(Collectors.toList());
            List<AccDoorItem> doorList = accDoorService.getItemsByIds(doorIdList);
            int doorCount = doorList.size();
            String[] doorName = new String[doorCount];
            String[] doorId = new String[doorCount];
            for (int i = 0; i < doorCount; i++) {
                AccDoorItem door = doorList.get(i);
                doorName[i] = door.getName();
                doorId[i] = door.getId();
            }
            doorVal[0] = doorId;
            doorVal[1] = doorName;
        }
        return doorVal;
    }

    /**
     * 获取触发点组所属设备
     *
     * @param triggerGroup
     * @return
     */
    private List<AccDeviceItem> getGroupDevs(AccTriggerGroup triggerGroup) {
        if (triggerGroup != null) {
            Set<AccTriggerGroupAddr> addrSet = triggerGroup.getAddrSet();
            List<String> idList = addrSet.stream().map(AccTriggerGroupAddr::getAddress).collect(Collectors.toList());
            final List<AccDeviceItem> groupDevs =
                accTriggerGroupService.getDevByTriggerGroupAddrAndType(idList, AccTriggerGroupItem.TRIGGER_TYPE_DOOR);
            return groupDevs;
        }
        return new ArrayList<>(0);
    }

    @Override
    public Boolean validDetermineInterlock(String deviceId) {
        return accDeviceOptionService.getAccSupportFunListVal(deviceId, 66);
    }

    /**
     * 获取新的递增业务id
     *
     * @return
     */
    private Long createBID() {
        Long maxBId = accInterlockDao.findMaxBusinessId();
        if (maxBId == null) {
            return 1L;
        }
        return maxBId + 1L;
    }

    /**
     * 判断是否重复设置互锁
     *
     * @param item
     * @return
     */
    private Boolean validInterlockRepeat(AccInterlockItem item) {
        if (item.getLockRule() != null) {
            List<AccInterlock> accInterlocks = accInterlockDao.findByAccDevice_Id(item.getDeviceId());
            if (StringUtils.isNotBlank(item.getDeviceId())) {
                for (AccInterlock interlock : accInterlocks) {
                    // 编辑时不判断自己
                    if (interlock.getLockRule() == null || interlock.getId().equals(item.getId())) {
                        continue;
                    }
                    AccTriggerGroup group1 = interlock.getTriggerGroup1();
                    Set<AccTriggerGroupAddr> addrSet1 = group1.getAddrSet();
                    Set<String> addrIds1 = new HashSet<>(CollectionUtil.getPropertyList(addrSet1, AccTriggerGroupAddr::getAddress, "-1"));
                    boolean group1Equal = setEqual(addrIds1, new HashSet<>(Arrays.asList(item.getGroup1DoorIds().split(","))));

                    AccTriggerGroup group2 = interlock.getTriggerGroup2();
                    boolean group2Equal = false;
                    if (group2 != null && StringUtils.isNotBlank(item.getGroup2DoorIds())) {
                        Set<AccTriggerGroupAddr> addrSet2 = group2.getAddrSet();
                        Set<String> addrIds2 = new HashSet<>(CollectionUtil.getPropertyList(addrSet2, AccTriggerGroupAddr::getAddress, "-1"));
                        group2Equal = setEqual(addrIds2, new HashSet<>(Arrays.asList(item.getGroup2DoorIds().split(","))));
                    }

                    if (group1Equal && group2Equal) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 判断集合元素相同
     *
     * @param addrIds1
     * @param addrIds2
     * @return
     */
    private Boolean setEqual(Set<String> addrIds1, Set<String> addrIds2) {
        if (addrIds1 == null || addrIds2 == null) {
            return false;
        }
        if (addrIds1.size() != addrIds2.size()) {
            return false;
        }
        return addrIds1.containsAll(addrIds2) && addrIds2.containsAll(addrIds1);
    }

}