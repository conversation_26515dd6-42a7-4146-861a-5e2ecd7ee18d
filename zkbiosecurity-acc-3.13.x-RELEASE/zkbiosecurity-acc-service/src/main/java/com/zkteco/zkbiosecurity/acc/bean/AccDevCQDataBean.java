/*
 * File Name: DevCQDataBean
 * <NAME_EMAIL> on 2018/6/12 11:40.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.bean;

import com.alibaba.fastjson.JSONObject;

import java.io.Serializable;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
public class AccDevCQDataBean implements Serializable {
    private String type;////数据类型，如：tabledata, options, count, FP, CARD, device, online
    private String table;//表名称
    private Integer count;//当前包的记录条数
    private String data;//包的数据记录
    private String sn;
    private Long cmdId;//命令id, 自动上传的数据，该值为-1
    private Integer packCnt;//包的总个数
    private Integer packIdx;//当前包序号
    private Short commType;//设备通信方式

    public AccDevCQDataBean() {
    }

    public AccDevCQDataBean(JSONObject dataJson) {
        type = dataJson.containsKey("type") ? dataJson.getString("type") : "";
        table = dataJson.containsKey("table") ? dataJson.getString("table") : "";
        count = dataJson.containsKey("count") ? dataJson.getIntValue("count") : 0;
        data = dataJson.containsKey("data") ? dataJson.getString("data") : "";
        sn = dataJson.containsKey("sn") ? dataJson.getString("sn") : "";
        cmdId = dataJson.containsKey("cmdId") ? dataJson.getLongValue("cmdId") : -1;
        packCnt = dataJson.containsKey("packcnt") ? dataJson.getIntValue("packcnt") : 0;
        packIdx = dataJson.containsKey("packIdx") ? dataJson.getIntValue("packIdx") : 0;
        commType = dataJson.containsKey("commType") ? (short) dataJson.getShortValue("commType") : -1;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public Long getCmdId() {
        return cmdId;
    }

    public void setCmdId(Long cmdId) {
        this.cmdId = cmdId;
    }

    public Integer getPackCnt() {
        return packCnt;
    }

    public void setPackCnt(Integer packCnt) {
        this.packCnt = packCnt;
    }

    public Integer getPackIdx() {
        return packIdx;
    }

    public void setPackIdx(Integer packIdx) {
        this.packIdx = packIdx;
    }

    public Short getCommType() {
        return commType;
    }

    public void setCommType(Short commType) {
        this.commType = commType;
    }
}
