package com.zkteco.zkbiosecurity.acc.cache;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.model.AccDevice;
import com.zkteco.zkbiosecurity.acc.model.AccTransaction;
import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;

/**
 * 门禁缓存操作
 * 
 * <AUTHOR>
 * @date 2018/4/21 9:51
 */
@Component
public class AccCacheManager {

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, AccSearchAddDeviceItem> accAddPushDevItemRedisTemplate;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, AccQueryDeviceItem> queryDeviceItemRedisTemplate;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, AccAlarmMonitorItem> alarmMonitorItemRedisTemplate;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Boolean> booleanRedisTemplate;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, AccQueryLinkageItem> queryLinkageItemRedisTemplate;
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, AccDeviceUploadTransactionItem> transactionValidTimeTemplate;
    // 标记每次从redis里获取待入库的事件记录条数
    private static final int GET_TRANSACTIONS_TO_DB_INDEX = 49;

    /** 存储事件id的zset key */
    private static final String ALARM_LIST_ID = "alarmListId";
    /** 存储具体事件信息的 key */
    private static final String ALARM_LIST_ITEM = "alarmListItem";
    /** 列表之前是否还有报警信息 */
    private static final String ALARM_LIST_HAS_MORE = "alarmListHasMore";
    /** 设备信息缓存key */
    String ADMS_DEVICE_INFO = "adms:devInfo:";
    @Resource(name = "redisTemplate")
    private RedisTemplate<String, List<AccDeviceEventItem>> accDeviceEventItemRedisTemplate;

    private static final String ADMS_ACC_RT_STATE = "adms:accRtState:";

    /**
     * @Description: 根据key获取值
     * @param key
     * @return
     */
    public String getCacheValueByKey(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    /**
     * @Description: 存放临时设备信息
     * @param sn
     * @param item
     * @return
     */
    public void putTempDevice2Cache(String sn, AccSearchAddDeviceItem item) {
        accAddPushDevItemRedisTemplate.opsForValue().set(AccCacheKeyConstants.TEMP_DEV + sn, item);
        stringRedisTemplate.expire(AccCacheKeyConstants.TEMP_DEV + sn, 60 * 3, TimeUnit.SECONDS);
    }

    /**
     * @Description: 获取临时设备信息
     * @param sn
     * @return
     */
    public AccSearchAddDeviceItem getTempDevFromCache(String sn) {
        return accAddPushDevItemRedisTemplate.opsForValue().get(AccCacheKeyConstants.TEMP_DEV + sn);
    }

    /**
     * @Description: 删除临时设备信息
     * @param sn
     * @return
     */
    public void delTempDevFromCache(String sn) {
        accAddPushDevItemRedisTemplate.delete(AccCacheKeyConstants.TEMP_DEV + sn);
    }

    /**
     * @Description: 获取list左边第一个元素
     * @param key
     * @return
     */
    public String getLeftFirstValue(String key) {
        return stringRedisTemplate.opsForList().leftPop(key);
    }

    public List<String> getAndRemoveLeftNValue(String key, int count) {
        List<String> datas = stringRedisTemplate.opsForList().range(key, 0, count - 1);
        if (datas != null && datas.size() > 0) {
            stringRedisTemplate.opsForList().trim(key, datas.size(), -1);
        }
        return datas;
    }

    /**
     * @Description: 匹配获取key集合
     * @param key
     * @return
     */
    public Set<String> getCacheKeySet(String key) {
        return stringRedisTemplate.keys(key);
    }

    /**
     * @Description: 判断key是否存在
     * @param key
     * @return
     */
    public boolean exists(String key) {
        return stringRedisTemplate.hasKey(key);
    }

    /**
     * @Description: 事件记录入库数据保存
     * @param data
     * @return
     */
    public void putTransactionToDb(String data) {
        stringRedisTemplate.opsForList().rightPush(AccCacheKeyConstants.ACC_TRANSACTIONS_TO_DB, data);
    }

    /**
     * @Description: 人员最后访问位置入库数据保存
     * @param data
     * @return
     */
    public void putPersonLastAddr(String data) {
        stringRedisTemplate.opsForList().rightPush(AccCacheKeyConstants.ACC_PERSON_LAST_ADDR, data);
    }

    public void delete(String transKey) {
        stringRedisTemplate.delete(transKey);
    }

    public void set(String transKey, String transValue) {
        stringRedisTemplate.opsForValue().set(transKey, transValue);
    }

    public void putMap(String key, Map<String, String> map) {
        stringRedisTemplate.opsForHash().putAll(key, map);
    }

    public Map<String, String> getMap(String key) {
        Map<String, String> newMap = new HashMap<>();
        Map<Object, Object> map = stringRedisTemplate.opsForHash().entries(key);
        for (Object object : map.keySet()) {
            newMap.put(object.toString(), map.get(object).toString());
        }
        return newMap;
    }

    public void delDevLinkInfo(AccDevice dev) {
        Set<String> keys = new HashSet<>();
        // 设备事件类型
        keys.add(AccCacheKeyConstants.REDIS_ACC_EVENT + dev.getSn());
        // 设备验证模式
        keys.add(AccCacheKeyConstants.REDIS_ACC_VERIFY_MODE + dev.getSn());
        // 联动信息
        keys.addAll(getAccQueryLinkageKeySets(dev.getSn()));
        // 绑定的视频通道信息
        keys.addAll(getEntityBindVidChannelKeySets());
        // 删除
        stringRedisTemplate.delete(keys);
    }

    public List<AccTransaction> getAccTransactions(String transKey) {
        List<AccTransaction> accTransactions = new ArrayList<>();
        // 一次性最多取200条处理
        List<String> transList = stringRedisTemplate.opsForList().range(transKey, 0, GET_TRANSACTIONS_TO_DB_INDEX);
        short except = 0;
        try {
            AccTransaction tempTran = null;
            if (transList != null) {
                for (String tran : transList) {
                    tempTran = JSON.parseObject(tran, AccTransaction.class);
                    if (tempTran.getDescription().length() > 100)// 考虑中文
                    {
                        tempTran.setDescription(tempTran.getDescription().substring(0, 96) + "...");
                    }
                    accTransactions.add(tempTran);
                }
            }
        } catch (Exception e) {
            except = 1;
        }
        if (transList != null && transList.size() > 0) {
            stringRedisTemplate.opsForList().trim(transKey, accTransactions.size() + except, -1);// 将有异常的数据从队列中取出来
        }
        return accTransactions;
    }

    /**
     * @Description: 设置设备最近异常状态
     * @param devSn
     * @param lastError
     * @return
     */
    public void putDevLastError(String devSn, String lastError) {
        stringRedisTemplate.opsForHash().put(AccCacheKeyConstants.DEV_LAST_ERROR, devSn, lastError);
    }

    /**
     * @Description: 获取设备最近异常状态
     * @param devSn
     * @return
     */
    public String getDevLastError(String devSn) {
        String lastError = "";
        if (exists(AccCacheKeyConstants.DEV_LAST_ERROR)) {
            Object tempError = stringRedisTemplate.opsForHash().get(AccCacheKeyConstants.DEV_LAST_ERROR, devSn);
            lastError = tempError != null ? tempError.toString() : "";
        }
        return lastError;
    }

    /**
     * @Description: 存放设备最后一次事件记录，用于硬联动取前一条事件记录来判断触发点
     * <AUTHOR>
     * @date 2018/6/11 8:51
     * @param accTransaction
     * @return
     */
    public void putLastRtLogToCache(AccTransactionItem accTransaction) {
        stringRedisTemplate.opsForHash().put(AccCacheKeyConstants.DEV_LAST_RTLOG, accTransaction.getDevSn(),
            JSONObject.toJSONString(accTransaction));
    }

    public AccTransactionItem getLastRtLogFromCache(String sn) {
        AccTransactionItem lastLog = null;
        if (exists(AccCacheKeyConstants.DEV_LAST_RTLOG)) {
            Object tempLastLog = stringRedisTemplate.opsForHash().get(AccCacheKeyConstants.DEV_LAST_RTLOG, sn);
            lastLog = JSONObject.parseObject(tempLastLog.toString(), AccTransactionItem.class);
        }
        return lastLog;
    }

    /**
     * @Description: 获取设备查询数据
     * @param cmdId
     * @return
     */
    public JSONObject getQueryData(Long cmdId) {
        return JSONObject.parseObject(stringRedisTemplate.opsForValue().get(AccCacheKeyConstants.QUERY_DATA + cmdId));
    }

    /**
     * @Description: 设置查询记录条数
     * @param cmdId
     * @param optionMap
     * @return
     */
    public void setQueryDataCount(String cmdId, Map<String, String> optionMap) {
        JSONObject dataCount = new JSONObject();
        optionMap.forEach((k, v) -> dataCount.put(k, v));
        stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.QUERY_DATA_COUNT + cmdId, dataCount.toJSONString());
        stringRedisTemplate.expire(AccCacheKeyConstants.QUERY_DATA_COUNT + cmdId, 60 * 60 * 12, TimeUnit.SECONDS);
    }

    /**
     * @Description: 保存设备查询数据
     * @param cmdId
     * @param data
     * @return
     */
    public void setPackageInfoToCache(Long cmdId, JSONObject data) {
        stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.QUERY_DATA + cmdId, data.toJSONString());
        stringRedisTemplate.expire(AccCacheKeyConstants.QUERY_DATA + cmdId, 60 * 60 * 2, TimeUnit.SECONDS);
    }

    /**
     * @Description: 获取事件记录
     * @param count
     * @return
     */
    public List<String> getAccTransactionFromCache(Integer count) {
        return stringRedisTemplate.opsForList().range(AccCacheKeyConstants.ACC_TRANSACTIONS_TO_DB, 0,
            count == null ? GET_TRANSACTIONS_TO_DB_INDEX : count);
    }

    /**
     * @Description: 移除已经处理的事件记录
     * @param transKey
     * @param size
     * @return
     */
    public void removeAccTransaction(String transKey, int size) {
        stringRedisTemplate.opsForList().trim(transKey, size, -1);
    }

    /**
     * @Description: 移除已经处理人员最后访问位置数据
     * @param size
     * @return
     */
    public void removePersonLastAddr(int size) {
        stringRedisTemplate.opsForList().trim(AccCacheKeyConstants.ACC_PERSON_LAST_ADDR, size, -1);
    }

    /**
     * @Description: 获取设备上传account数据
     * @param sn
     * @param cmdId
     * @return
     */
    public JSONObject getAccountData(String sn, String cmdId) {
        String accountData =
            stringRedisTemplate.opsForValue().get(String.format(AccCacheKeyConstants.ACCOUNT_DATA_ACC, sn, cmdId));
        if (StringUtils.isNotBlank(accountData)) {
            return JSONObject.parseObject(accountData);
        }
        return null;
    }

    /**
     * @Description: 设置wifi列表
     * @param cmdId
     * @param data
     * @return
     */
    public void setWifiListInfo(Long cmdId, JSONObject data) {
        if (stringRedisTemplate.hasKey(AccCacheKeyConstants.SEARCH_WIFI_LIST + cmdId)) {
            delWifiListInfo(String.valueOf(cmdId));
        }
        stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.SEARCH_WIFI_LIST + cmdId, data.toJSONString());
        stringRedisTemplate.expire(AccCacheKeyConstants.SEARCH_WIFI_LIST + cmdId, 60 * 60 * 2, TimeUnit.SECONDS);
    }

    /**
     * @Description: 获取wifi列表
     * @param cmdId
     * @return
     */
    public String getWifiListInfo(Long cmdId) {
        return stringRedisTemplate.opsForValue().get(AccCacheKeyConstants.SEARCH_WIFI_LIST + cmdId);
    }

    /**
     * @Description: 删除缓存中wifi信息
     * @param cmdId
     * @return
     */
    public void delWifiListInfo(String cmdId) {
        stringRedisTemplate.delete(AccCacheKeyConstants.SEARCH_WIFI_LIST + cmdId);
    }

    /**
     * @Description: 设置设备授权信息
     * @param sn
     * @param data
     * @return
     */
    public void setDeviceAuthorizeInfo(String sn, String data) {
        stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.DEVICE_AUTHORIZE_KEY + sn, data);
        stringRedisTemplate.expire(AccCacheKeyConstants.DEVICE_AUTHORIZE_KEY + sn, 10 * 60, TimeUnit.SECONDS);
    }

    /**
     * @Description: 获取设备授权信息
     * @param sn
     * @return
     */
    public String getDeviceAuthorizeInfo(String sn) {
        String data = stringRedisTemplate.opsForValue().get(AccCacheKeyConstants.DEVICE_AUTHORIZE_KEY + sn);
        if (StringUtils.isNotBlank(data)) {
            return data;
        }
        return null;
    }

    /**
     * @Description: 删除设备授权信息
     * @param sn
     * @return
     */
    public void delDeviceAuthorizeInfo(String sn) {
        if (exists(AccCacheKeyConstants.DEVICE_AUTHORIZE_KEY + sn)) {
            stringRedisTemplate.delete(AccCacheKeyConstants.DEVICE_AUTHORIZE_KEY + sn);
        }
    }

    /**
     * @Description: 设置设备授权信息
     * @param sn
     * @return
     */
    public void setAuthAccDeviceInfo(String sn) {
        stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.AUTHORIZE_KEY + sn, sn);
        stringRedisTemplate.expire(AccCacheKeyConstants.AUTHORIZE_KEY + sn, 60 * 3, TimeUnit.SECONDS);
    }

    /**
     * @Description: 设置身份证信息
     * @param key
     * @param data
     * @return
     */
    public void setIdentityCardInfo(String key, String data) {
        stringRedisTemplate.opsForValue().set(key, data);
        stringRedisTemplate.expire(key, 60 * 10, TimeUnit.SECONDS);// 设置10分钟过期
    }

    /**
     * @Description: 获取身份证信息
     * @param key
     * @return
     */
    public JSONObject getIdentityCardInfo(String key) {
        String data = stringRedisTemplate.opsForValue().get(key);
        JSONObject json = null;
        if (StringUtils.isNotBlank(data)) {
            json = JSON.parseObject(data);
        }
        return json;
    }

    /**
     * @Description: 删除设备时移除设备最近异常状态
     * @param sn
     * @return
     */
    public void delDeviceLastError(String sn) {
        stringRedisTemplate.opsForHash().delete(AccCacheKeyConstants.DEV_LAST_ERROR, sn);
    }

    /**
     * @Description: 删除设备时移除设备保存上一条记录
     * @param sn
     * @return
     */
    public void delDeviceLastRTLog(String sn) {
        stringRedisTemplate.opsForHash().delete(AccCacheKeyConstants.DEV_LAST_RTLOG, sn);
    }

    /**
     * @Description: 获取门禁设备信息
     * @param sn
     * @return
     */
    public AccQueryDeviceItem getDeviceInfo(String sn) {
        return queryDeviceItemRedisTemplate.opsForValue().get(AccCacheKeyConstants.ACC_DEVICE_INFO + sn);
    }

    /**
     * @Description: 设置门禁设备信息
     * @param accQueryDeviceItem
     * @return
     */
    public void putDeviceInfo(AccQueryDeviceItem accQueryDeviceItem) {
        queryDeviceItemRedisTemplate.opsForValue()
            .set(AccCacheKeyConstants.ACC_DEVICE_INFO + accQueryDeviceItem.getSn(), accQueryDeviceItem);
    }

    /**
     * @Description: 获取门禁设备信息
     * @param sn
     * @return
     */
    public JSONObject getDeviceOptionInfo(String sn) {
        String optionStr = stringRedisTemplate.opsForValue().get(AccCacheKeyConstants.ACC_DEVICE_OPTIONS + sn);
        if (StringUtils.isNotBlank(optionStr)) {
            return JSONObject.parseObject(optionStr);
        }
        return null;
    }

    /**
     * @Description: 设置门禁设备信息
     * @param optionStr
     * @return
     */
    public void putDeviceOptionInfo(String sn, String optionStr) {
        stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.ACC_DEVICE_OPTIONS + sn, optionStr);
    }

    /**
     * @Description: 删除门禁设备信息
     * @param sn
     * @return
     */
    public void delDeviceInfo(String sn) {
        queryDeviceItemRedisTemplate.delete(AccCacheKeyConstants.ACC_DEVICE_INFO + sn);
    }

    /**
     * @Description: 删除门禁设备信息
     * @param sn
     * @return
     */
    public void delDeviceOptionInfo(String sn) {
        stringRedisTemplate.delete(AccCacheKeyConstants.ACC_DEVICE_OPTIONS + sn);
    }

    /**
     * 保存实时监控客户端ID
     *
     * @auther lambert.li
     * @date 2018/11/14 18:21
     * @param clientId
     * @return
     */
    public void setRTMonitorClient(String clientId) {
        if (!exists(AccCacheKeyConstants.ACC_MONITOR_CLIENT + clientId)) {
            stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.ACC_MONITOR_CLIENT + clientId,
                String.valueOf(System.currentTimeMillis()));
        }
        stringRedisTemplate.expire(AccCacheKeyConstants.ACC_MONITOR_CLIENT + clientId, 3 * 60, TimeUnit.SECONDS);
    }

    /**
     * 设置实时事件到客户端中，API实时监控接口使用
     *
     * @auther lambert.li
     * @date 2018/11/14 19:14
     * @param accTransactionItemStr
     * @return
     */
    public void setTransactionToClient(String accTransactionItemStr) {
        Set<String> keySet = stringRedisTemplate.keys(AccCacheKeyConstants.ACC_MONITOR_CLIENT + "*");
        if (!keySet.isEmpty()) {
            keySet.forEach(key -> {
                String clientId = key.split(":", 4)[3];// 获取客户端ID
                boolean hasKey = exists(AccCacheKeyConstants.ACC_MONITOR_CLIENT_RTLOG + clientId);
                stringRedisTemplate.opsForList().rightPush(AccCacheKeyConstants.ACC_MONITOR_CLIENT_RTLOG + clientId,
                    accTransactionItemStr);
                if (!hasKey) { // 存在key时不更新过期时间
                    stringRedisTemplate.expire(AccCacheKeyConstants.ACC_MONITOR_CLIENT_RTLOG + clientId, 3 * 60,
                        TimeUnit.SECONDS);
                }
            });
        }
    }

    /**
     * 获取客户端中实时事件记录
     *
     * @auther lambert.li
     * @date 2018/11/15 9:13
     * @param clientId
     * @return
     */
    public List<String> getClientTransaction(String clientId) {
        if (exists(AccCacheKeyConstants.ACC_MONITOR_CLIENT_RTLOG + clientId)) {
            List<String> transList =
                stringRedisTemplate.opsForList().range(AccCacheKeyConstants.ACC_MONITOR_CLIENT_RTLOG + clientId, 0, -1);
            delete(AccCacheKeyConstants.ACC_MONITOR_CLIENT_RTLOG + clientId);
            return transList;
        }
        return null;
    }

    /**
     *
     * @auther lambert.li
     * @date 2018/11/15 9:12
     * @param sn
     * @param tempLog
     * @return
     */
    public void setAccRTLogToCache(String sn, String tempLog) {
        stringRedisTemplate.opsForList().rightPush(BaseConstants.ADMS_ACC_LOG + sn, tempLog);
    }

    /**
     * 保存远程登记的模板信息
     *
     * <AUTHOR>
     * @since 2019-01-11 9:59
     * @Param [pin, bioData]
     * @return
     */
    public void setAutoBioData(String pin, String bioData) {
        long nowTime = System.currentTimeMillis();
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(AccCacheKeyConstants.ACC_REMOTE_TEMPLATE + pin + "_" + nowTime))) {
            nowTime = nowTime + 1;
        }
        stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.ACC_REMOTE_TEMPLATE + pin + "_" + nowTime, bioData);
        // 保存两分钟
        stringRedisTemplate.expire(AccCacheKeyConstants.ACC_REMOTE_TEMPLATE + pin + "_" + nowTime, 120,
            TimeUnit.SECONDS);
    }

    /**
     * 临时存储设备推送的照片路径
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2020/4/20 8:50
     * @param sn 设备序列号
     * @param photoPath 照片路径
     */
    public void setAttPhotoToCache(String sn, String photoPath) {
        if (StringUtils.isNotBlank(photoPath)) {
            // 从照片名上获取时间字符串
            String timeStr =
                photoPath.substring(photoPath.lastIndexOf("/") + 1, photoPath.lastIndexOf(".")).split("-")[0];
            stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.ACC_ATT_PHOTO + sn + "_" + timeStr, photoPath);
            // 保存一分钟
            stringRedisTemplate.expire(AccCacheKeyConstants.ACC_ATT_PHOTO + sn + "_" + timeStr, 60, TimeUnit.SECONDS);
        }
    }

    /**
     * 获取设备推送的照片路径
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2020/4/20 8:50
     * @param sn 设备序列号
     * @param timeStr
     */
    public String getAttPhotoFromCache(String sn, String timeStr) {
        return stringRedisTemplate.opsForValue().get(AccCacheKeyConstants.ACC_ATT_PHOTO + sn + "_" + timeStr);
    }

    /**
     * 将门禁抓拍图片暂存到redis
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-06-18 14:06
     * @param accTransactionItem
     * @return void
     */
    public void setPhotoToCache(AccTransactionItem accTransactionItem) {
        stringRedisTemplate.opsForList().rightPush(AccCacheKeyConstants.ACC_TRANSACTION_PHOTO_TO_DB,
            JSON.toJSONString(accTransactionItem));
        stringRedisTemplate.expire(AccCacheKeyConstants.ACC_TRANSACTION_PHOTO_TO_DB, 20, TimeUnit.SECONDS);
    }

    /**
     * 获取抓拍图片
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-06-18 15:50
     * @param count
     * @return java.util.List<java.lang.String>
     */
    public List<String> getAccTransactionPhotoFromCache(Integer count) {
        return stringRedisTemplate.opsForList().range(AccCacheKeyConstants.ACC_TRANSACTION_PHOTO_TO_DB, 0,
            count == null ? GET_TRANSACTIONS_TO_DB_INDEX : count);
    }

    public Map<String, String> getIVideoDigifortLinkage() {
        Map<String, String> map = new HashMap<>();
        Set<String> cameraSet = stringRedisTemplate.keys(AccCacheKeyConstants.DIGIFORT_LINKAGE_CAMERA_PREFIX + "*");
        for (String camerakey : cameraSet) {
            String cameraName = camerakey.replace(AccCacheKeyConstants.DIGIFORT_LINKAGE_CAMERA_PREFIX, "");
            Set<String> transactionKeys = stringRedisTemplate.opsForSet().members(camerakey);
            for (String transactionKey : transactionKeys) {
                String cameraNames = StringUtils.defaultString(map.get(transactionKey), "");
                if (StringUtils.isNotBlank(cameraNames)) {
                    cameraNames += "," + cameraName;
                } else {
                    cameraNames = cameraName;
                }
                map.put(transactionKey, cameraNames);
            }
        }
        return map;
    }

    /**
     * 向指定主题发布消息
     *
     * @param mess
     * @param topic
     */
    public void sendMessageToTopic(String mess, String topic) {
        try {
            stringRedisTemplate.convertAndSend(topic, mess);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ZKBusinessException("err");
        }
    }

    /**
     * 将报警事件添加到缓存中
     *
     * @param accAlarmMonitorItem
     */
    public void addAlarmToCache(AccAlarmMonitorItem accAlarmMonitorItem) {
        stringRedisTemplate.opsForZSet().add(ALARM_LIST_ID, accAlarmMonitorItem.getId(),
            accAlarmMonitorItem.getEventTime().getTime());
        alarmMonitorItemRedisTemplate.opsForHash().put(ALARM_LIST_ITEM, accAlarmMonitorItem.getId(),
            accAlarmMonitorItem);
    }

    /**
     * 删除指定的报警事件
     *
     * @param id
     */
    public void delAlarmFromCache(String id) {
        stringRedisTemplate.opsForZSet().remove(ALARM_LIST_ID, id);
        alarmMonitorItemRedisTemplate.opsForHash().delete(ALARM_LIST_ITEM, id);
    }

    /**
     * 从列表中拿出固定数量的报警事件
     *
     * @param size
     * @return
     */
    public List<AccAlarmMonitorItem> getAlarmFromCache(int size) {
        final Long listSize = countAlarmCacheList();
        // 从后往前拿，取size数量的报警事件
        final Set<String> ids =
            stringRedisTemplate.opsForZSet().range(ALARM_LIST_ID, listSize - size >= 0 ? listSize - size : 0, listSize);
        if (ids != null) {
            final Map<Object, Object> map = alarmMonitorItemRedisTemplate.opsForHash().entries(ALARM_LIST_ITEM);
            return ids.stream().map(map::get).filter(Objects::nonNull).map(ob -> (AccAlarmMonitorItem)ob)
                .collect(Collectors.toList());
        } else {
            return new LinkedList<>();
        }
    }

    /**
     * 获取列表中最早的事件
     *
     * @return
     */
    public String getFirstAlarmListId() {
        final Set<String> range = stringRedisTemplate.opsForZSet().range(ALARM_LIST_ID, 0, 0);
        if (range != null) {
            return range.toArray(new String[] {})[0];
        }
        return null;
    }

    /**
     * 统计列表中的报警事件数量
     *
     * @return
     */
    public Long countAlarmCacheList() {
        final Long size = stringRedisTemplate.opsForZSet().size(ALARM_LIST_ID);
        if (size == null) {
            return 0L;
        }
        return size;
    }

    /**
     * 移除最早的报警事件
     *
     * @return
     */
    public boolean removeFirstAlarmFromCache() {
        final Set<String> idSet = stringRedisTemplate.opsForZSet().range(ALARM_LIST_ID, 0, 0);
        if (idSet != null && idSet.size() > 0) {
            final String id = idSet.stream().findFirst().get();
            if (StringUtils.isNotBlank(id)) {
                stringRedisTemplate.opsForZSet().remove(ALARM_LIST_ID, id);
                alarmMonitorItemRedisTemplate.opsForHash().delete(ALARM_LIST_ITEM, id);
                return true;
            }
        }
        return false;
    }

    /**
     * 标记列表之前是否还有报警事件（数据库里是否还要更早的报警事件）
     *
     * @param hasMore
     */
    public void markAlarmHasMore(boolean hasMore) {
        booleanRedisTemplate.opsForValue().set(ALARM_LIST_HAS_MORE, hasMore);
    }

    /**
     * 获取标记的值
     *
     * @return
     */
    public boolean getAlarmHasMore() {
        final Boolean hasKey = booleanRedisTemplate.hasKey(ALARM_LIST_HAS_MORE);
        if (hasKey != null && hasKey) {
            final Boolean hasMore = booleanRedisTemplate.opsForValue().get(ALARM_LIST_HAS_MORE);
            return hasMore != null && hasMore;
        }
        return false;
    }

    /**
     * 缓存设备状态，用于与当前设备状态比较
     *
     * <AUTHOR>
     * @date 2022/4/7 15:39
     * @param state
     * @param sn
     * @since 1.0.0
     * @return void
     */
    public void setDevStateToCache(String state, String sn) {
        stringRedisTemplate.opsForValue().set(AccCacheKeyConstants.ACC_DEVICE_STATE + sn, state);
    }

    /**
     * 获取缓存设备状态
     *
     * <AUTHOR>
     * @date 2022/4/7 17:01
     * @param sn
     * @since 1.0.0
     * @return java.lang.String
     */
    public String getDevStateFromCache(String sn) {
        return stringRedisTemplate.opsForValue().get(AccCacheKeyConstants.ACC_DEVICE_STATE + sn);
    }

    /**
     * 删除缓存设备状态
     *
     * <AUTHOR>
     * @date 2022/4/19 13:53
     * @param sn
     * @since 1.0.0
     * @return void
     */
    public void deleteDevState(String sn) {
        stringRedisTemplate.delete(AccCacheKeyConstants.ACC_DEVICE_STATE + sn);
    }

    /**
     * 缓存拉取记录的时间戳
     *
     * @param key:
     * @param lastCreateTime:
     * @return void
     * <AUTHOR>
     * @date 2022-05-12 16:40
     * @since 1.0.0
     */
    public void putFirstInLastOutTime(String key, String lastCreateTime) {
        stringRedisTemplate.opsForValue().set(key, lastCreateTime);
    }

    /**
     * 缓存设备联动信息
     *
     * @param devSn:设备序列号
     * @param accQueryLinkageItem:联动信息
     * @return void
     * <AUTHOR>
     * @date 2022-07-20 11:04
     * @since 1.0.0
     */
    public void putAccQueryLinkageInfo(String devSn, AccQueryLinkageItem accQueryLinkageItem) {
        queryLinkageItemRedisTemplate.opsForValue().set(
            AccCacheKeyConstants.ACC_LINKAGE_INFO + devSn + ":" + accQueryLinkageItem.getId(), accQueryLinkageItem);
    }

    /**
     * 获取设备缓存的联动信息
     *
     * @param devSn:设备序列号
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2022-07-20 11:05
     * @since 1.0.0
     */
    public Set<String> getAccQueryLinkageKeySets(String devSn) {
        return queryLinkageItemRedisTemplate.keys(AccCacheKeyConstants.ACC_LINKAGE_INFO + devSn + ":*");
    }

    /**
     * 根据key获取缓存联动信息
     *
     * @param key: 缓存key
     * @return com.zkteco.zkbiosecurity.acc.vo.AccQueryLinkageItem
     * <AUTHOR>
     * @date 2022-07-20 11:05
     * @since 1.0.0
     */
    public AccQueryLinkageItem getAccQueryLinkageItemByKey(String key) {
        return queryLinkageItemRedisTemplate.opsForValue().get(key);
    }

    /**
     * 删除设备缓存联动信息
     *
     * @param devSn:设备序列号
     * @return void
     * <AUTHOR>
     * @date 2022-07-20 11:06
     * @since 1.0.0
     */
    public void delAccQueryLinkageInfo(String devSn) {
        Set<String> keys = getAccQueryLinkageKeySets(devSn);
        if (!keys.isEmpty()) {
            queryLinkageItemRedisTemplate.delete(keys);
        }
    }

    /**
     * 根据设备序列号和联动ID删除缓存联动信息
     *
     * @param devSn:设备序列号
     * @param linkageId:联动id
     * @return void
     * <AUTHOR>
     * @date 2022-07-20 11:06
     * @since 1.0.0
     */
    public void delAccQueryLinkageInfo(String devSn, String linkageId) {
        queryLinkageItemRedisTemplate.delete(AccCacheKeyConstants.ACC_LINKAGE_INFO + devSn + ":" + linkageId);
    }

    /**
     * 保存绑定的视频通道信息
     *
     * @param entityName:实体名称
     * @param entityId:实体ID
     * @param channelIds:通道ID
     * @return void
     * <AUTHOR>
     * @date 2023-02-22 17:42
     * @since 1.0.0
     */
    public void putEntityBindVidChannel(String entityName, String entityId, String channelIds) {
        set(AccCacheKeyConstants.ACC_BIND_CHANNEL_INFO + entityName + ":" + entityId, channelIds);
    }

    /**
     * 获取读头绑定的视频通道信息
     *
     * @param entityId:实体ID
     * @return java.lang.String
     * <AUTHOR>
     * @date 2023-02-22 17:53
     * @since 1.0.0
     */
    public String getAccReaderBindVidChannel(String entityId) {
        return getCacheValueByKey(AccCacheKeyConstants.ACC_BIND_CHANNEL_INFO + "AccReader:" + entityId);
    }

    /**
     * 获取绑定的视频通道信息keys
     *
     * @return void
     * <AUTHOR>
     * @date 2023-02-23 13:53
     * @since 1.0.0
     */
    public Set<String> getEntityBindVidChannelKeySets() {
        return getCacheKeySet(AccCacheKeyConstants.ACC_BIND_CHANNEL_INFO + "*");
    }

    /**
     * 获取设备待执行指令数
     *
     * @author: max.zheng
     * @date: 2018-04-03 17:16:37
     * @param sn
     * @return
     */
    public Long getDevCmdCount(String sn) {
        long commCount = stringRedisTemplate.boundListOps(AccCacheKeyConstants.COMMON_CMD + sn).size();
        long immeCount = stringRedisTemplate.boundListOps(AccCacheKeyConstants.IMME_CMD + sn).size();
        return commCount + immeCount;
    }

    /**
     * 获取设备在线或离线状态，0离线、1在线
     *
     * @author: max.zheng
     * @date: 2018-04-03 11:14:48
     * @param sn
     * @return
     */
    public String getOnlineStatus(String sn) {
        if (stringRedisTemplate.hasKey(AccCacheKeyConstants.ADMS_DEV_HEARTBEAT + sn)) {
            return "1";
        } else {
            return "0";
        }
    }

    /**
     * 获取设备缓存json数据
     *
     * @author: max.zheng
     * @date: 2018-03-29 17:38:62
     * @param sn
     * @return
     */
    public String getAdmsDeviceInfo(String sn) {
        return stringRedisTemplate.opsForValue().get(ADMS_DEVICE_INFO + sn);
    }

    /**
     * 保存命令到redis
     *
     * @param sn
     * @param content
     */
    public void addCmdCache(String sn, String content) {
        stringRedisTemplate.opsForList().rightPush("adms:cmdData:" + sn, content);
    }

    /**
     * 设备事件类型缓存到redis
     *
     * @param sn:
     * @param deviceEventItemList:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-08-16 16:36
     * @since 1.0.0
     */
    public void putDeviceEvent2Cache(String sn, List<AccDeviceEventItem> deviceEventItemList) {
        accDeviceEventItemRedisTemplate.opsForValue().set(AccCacheKeyConstants.ACC_DEVICE_EVENT + sn,
            deviceEventItemList);
    }

    /**
     * 获取事件类型数据
     *
     * @param sn:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDeviceEventItem>
     * <AUTHOR>
     * @throws
     * @date 2023-08-16 17:54
     * @since 1.0.0
     */
    public List<AccDeviceEventItem> getDeviceEvent(String sn) {
        return accDeviceEventItemRedisTemplate.opsForValue().get(AccCacheKeyConstants.ACC_DEVICE_EVENT + sn);
    }

    public void setTransactionValidTime(String sn, AccDeviceUploadTransactionItem item) {
        transactionValidTimeTemplate.opsForValue().set(AccCacheKeyConstants.ACC_TRANSACTION_VALIDTIME + sn, item, 3600, TimeUnit.SECONDS);
    }

    public AccDeviceUploadTransactionItem getTransactionValidTime(String sn) {
        return transactionValidTimeTemplate.opsForValue().get(AccCacheKeyConstants.ACC_TRANSACTION_VALIDTIME + sn);
    }

    /**
     * 获取门禁实时门状态
     *
     * @param sn
     * @return
     */
    public String getAccRtState(String sn) {
        return stringRedisTemplate.opsForValue().get(ADMS_ACC_RT_STATE + sn);
    }

    /**
     * 获取门禁实时事件
     *
     * @param transKey
     * @return
     */
    public List<String> getAccLogs(String transKey) {
        List<String> transList = stringRedisTemplate.opsForList().range(transKey, 0, 9);

        if (transList != null && !transList.isEmpty()) {
            stringRedisTemplate.opsForList().trim(transKey, transList.size(), -1);
        }
        return transList;
    }

}
