/**
 * File Name: AccAuxOut Created by GenerationTools on 2018-03-14 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 AccAuxOut
 * 
 * <AUTHOR>
 * @date: 2018-03-14 上午09:38
 * @version v1.0
 */
@Entity
@Table(name = "ACC_AUXOUT")
@Getter
@Setter
@Accessors(chain = true)
public class AccAuxOut extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**  */
    @ManyToOne
    @JoinColumn(name = "DEV_ID")
    private AccDevice accDevice;

    /**  */
    @Column(name = "AUX_NO", nullable = false)
    private Short auxNo;

    /**  */
    @Column(name = "PRINTER_NUMBER", length = 20, nullable = false)
    private String printerNumber;

    /**  */
    @Column(name = "NAME", length = 100, nullable = false)
    private String name;

    /**  */
    @Column(name = "REMARK", length = 50)
    private String remark;

    /** 时间段 */
    @Column(name = "TIMESEG_ID")
    private String timeSegId;

    /** 所属扩展板id */
    @Column(name = "EXT_DEV_ID")
    private String extDevId;

}