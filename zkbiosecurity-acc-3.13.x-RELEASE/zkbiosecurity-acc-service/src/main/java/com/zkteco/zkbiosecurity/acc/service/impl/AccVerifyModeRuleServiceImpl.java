/**
 * File Name: AccVerifyModeRuleServiceImpl Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.service.impl;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.enums.AccLinkTypeEnum;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceOptionService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceVerifyModeService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccVerifyModeRuleService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceOptionItem;
import com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRuleItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonLinkService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;

/**
 * 对应百傲瑞达 AccVerifyModeRuleServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:02
 * @version v1.0
 */
@Service
@Transactional
public class AccVerifyModeRuleServiceImpl implements AccVerifyModeRuleService {
    @Autowired
    private AccVerifyModeRuleDao accVerifyModeRuleDao;
    @Autowired
    private AccTimeSegDao accTimeSegDao;
    @Autowired
    private AccDeviceOptionDao accDeviceOptionDao;
    @Autowired
    private AccDeviceVerifyModeDao accDeviceVerifyModeDao;
    @Autowired
    private AccPersonVerifyModeRuleDao accPersonVerifyModeRuleDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired
    private AccDoorVerifyModeRuleDao accDoorVerifyModeRuleDao;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDeviceOptionService accDeviceOptionService;
    @Autowired
    private AccReaderDao accReaderDao;
    @Autowired
    private PersPersonLinkService persPersonLinkService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private AccDeviceVerifyModeService accDeviceVerifyModeService;

    @Override
    public AccVerifyModeRuleItem saveItem(AccVerifyModeRuleItem item) {
        AccVerifyModeRule accVerifyModeRule =
            Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
                .flatMap(id -> accVerifyModeRuleDao.findById(id)).orElse(new AccVerifyModeRule());
        ModelUtil.copyProperties(item, accVerifyModeRule);
        AccTimeSeg accTimeSeg = accTimeSegDao.findById(item.getTimeSegId()).orElse(new AccTimeSeg());
        accVerifyModeRule.setAccTimeSeg(accTimeSeg);
        accVerifyModeRuleDao.save(accVerifyModeRule);
        List<AccDoor> accDoorList = accDoorVerifyModeRuleDao.getAccDoorByVerifyRuleId(accVerifyModeRule.getId());
        if (accDoorList != null && accDoorList.size() > 0) {
            List<String> personIdList =
                accPersonVerifyModeRuleDao.getPersonIdByAccVerifyModeRuleId(accVerifyModeRule.getId());
            List<AccDevice> devList =
                accDoorDao.getDeviceByDoorId((List<String>)CollectionUtil.getModelIdsList(accDoorList));
            devList.stream().forEach(accDevice -> {
                accDevCmdManager.setDiffTimezoneVSToDev(accDevice, accVerifyModeRule, false);
                Set<Short> doorNoSet = new HashSet<Short>();
                accDoorList.stream().forEach(accDoor -> {
                    if (accDoor.getDevice().getId().equals(accDevice.getId())) {
                        doorNoSet.add(accDoor.getDoorNo());
                    }
                });
                if (doorNoSet.size() > 0) {
                    accDevCmdManager.setDoorVSTimezoneToDev(accVerifyModeRule, accDevice, doorNoSet, false);
                }
                if (personIdList != null && personIdList.size() > 0) {
                    Map<String, String> pinsByPersonIds = persPersonService.getPinsByPersonIds(personIdList);
                    List<String> pinList = new ArrayList<>(pinsByPersonIds.values());
                    accDevCmdManager.setUserVSTimezoneToDev(accDevice, accVerifyModeRule, pinList, doorNoSet, false);
                }
            });
        }
        item.setId(accVerifyModeRule.getId());
        return item;
    }

    @Override
    public List<AccVerifyModeRuleItem> getByCondition(AccVerifyModeRuleItem condition) {
        return (List<AccVerifyModeRuleItem>)accVerifyModeRuleDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accVerifyModeRuleDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<String> idList = (List<String>)CollectionUtil.strToList(ids);
            List<AccVerifyModeRule> accVerifyModeRuleList = accVerifyModeRuleDao.findAll(idList);
            accVerifyModeRuleList.stream().forEach(accVerifyModeRule -> {
                Set<AccDoorVerifyModeRule> accDoorVerifyModeRuleSet = accVerifyModeRule.getDoorVerifyModeRuleSet();
                List<AccDoor> accDoorList =
                    accDoorVerifyModeRuleDao.getAccDoorByVerifyRuleId(accVerifyModeRule.getId());
                if (accDoorVerifyModeRuleSet != null && accDoorVerifyModeRuleSet.size() > 0) {
                    accDoorVerifyModeRuleDao.delete(accDoorVerifyModeRuleSet);
                    List<String> personIdList =
                        accPersonVerifyModeRuleDao.getPersonIdByAccVerifyModeRuleId(accVerifyModeRule.getId());
                    List<AccDevice> devList =
                        accDoorDao.getDeviceByDoorId((List<String>)CollectionUtil.getModelIdsList(accDoorList));
                    Set<Short> doorNoSet = null;
                    for (AccDevice dev : devList) {
                        accDevCmdManager.delDiffTimezoneVSFromDev(dev, accVerifyModeRule, false);
                        doorNoSet = new HashSet<Short>();
                        for (AccDoor door : accDoorList) {
                            if (door.getDevice().getId().equals(dev.getId())) {
                                doorNoSet.add(door.getDoorNo());
                            }
                        }
                        if (doorNoSet.size() > 0) {
                            accDevCmdManager.delDoorVSTimezoneFromDev(dev, doorNoSet, false);
                        }
                        if (personIdList.size() > 0) {
                            Map<String, String> pinsByPersonIds = persPersonService.getPinsByPersonIds(personIdList);
                            List<String> pinList = new ArrayList<>(pinsByPersonIds.values());
                            accDevCmdManager.delUserVSTimezoneFromDevByPin(dev, pinList, false);
                        }
                    }
                }
                if (accVerifyModeRule.getPersonVerifyModeRuleSet() != null
                    && accVerifyModeRule.getPersonVerifyModeRuleSet().size() > 0) {
                    accPersonVerifyModeRuleDao.delete(accVerifyModeRule.getPersonVerifyModeRuleSet());
                }
                accVerifyModeRuleDao.delete(accVerifyModeRule);
            });
            persPersonLinkService.deleteBatchItemByLinkId(AccLinkTypeEnum.ACC_VERIFYMODERULEPERSONGROUP, idList);
        }
        return false;
    }

    @Override
    public AccVerifyModeRuleItem getItemById(String id) {
        List<AccVerifyModeRuleItem> items = getByCondition(new AccVerifyModeRuleItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public AccVerifyModeRuleItem getItemByName(String name) {
        AccVerifyModeRule accVerifyModeRule = accVerifyModeRuleDao.findByName(name);
        if (accVerifyModeRule == null) {
            return null;
        } else {
            AccVerifyModeRuleItem item = new AccVerifyModeRuleItem();
            item.setId(accVerifyModeRule.getId());
            item.setName(accVerifyModeRule.getName());
            return item;
        }
    }

    @Override
    public ZKResultMsg getTimeSegJSONWithoutRule(String id) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        JSONArray jsonArray = new JSONArray();
        List<AccTimeSeg> accTimeSegList = null;
        List<String> timeSegIdList = accVerifyModeRuleDao.getTimeSegIdByVerifyRule(id);
        if (timeSegIdList.size() > 0) {
            accTimeSegList = accTimeSegDao.findByIdNotIn(timeSegIdList);
        } else {
            accTimeSegList = accTimeSegDao.findAll();
        }
        for (AccTimeSeg timeSeg : accTimeSegList) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("timeSegId", timeSeg.getId());
            jsonObject.put("timeSegName", timeSeg.getName());
            jsonArray.add(jsonObject);
        }
        zkResultMsg.setData(jsonArray);
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg getVerifyMode() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        JSONObject jsonObject = new JSONObject();
        List<AccDeviceOption> optionList = accDeviceOptionDao.findByName("AccSupportFunList");
        List<String> devSnList = Lists.newArrayList();
        for (AccDeviceOption option : optionList) {
            devSnList.add(option.getAccDevice().getSn());
        }
        // 含有新验证方式参数
        List<AccDeviceOption> supportNewVerifyModeList =
            accDeviceOptionDao.findByAccDevice_SnInAndName(devSnList, "NewVFStyles");
        Map<AccDevice, AccDeviceOption> map =
            CollectionUtil.listToKeyMap(supportNewVerifyModeList, AccDeviceOption::getAccDevice);
        // 支持不同验证方式设置的设备id
        List<String> devIdSupportVS = new ArrayList<>();
        for (AccDeviceOption option : optionList) {
            String val = option.getValue();
            // 支持验证方式规则设置功能,且不支持新验证方式
            if (accDeviceOptionService.getAccSupportFunListVal(12, val) && !map.containsKey(option.getAccDevice())) {
                devIdSupportVS.add(option.getAccDevice().getId());
            }
        }
        Set<Object[]> devVerifyModeList = new HashSet<>();
        if (devIdSupportVS.size() > 0) {
            devVerifyModeList = accDeviceVerifyModeDao.getCommonVerifyModeByDevId(devIdSupportVS);
        }
        JSONArray jsonArray = new JSONArray();
        for (Object[] verifyMode : devVerifyModeList) {
            JSONObject tempObj = new JSONObject();
            tempObj.put("verifyNo", verifyMode[0]);
            tempObj.put("name", I18nUtil.i18nCode(verifyMode[1] + ""));
            jsonArray.add(tempObj);
        }
        jsonObject.put("doorVerifyMode", jsonArray);
        jsonObject.put("supportNewVerifyModeLength", supportNewVerifyModeList.size());
        jsonObject.put("newVerifyModeMap", AccConstants.NEW_VERIFY_MODE);
        zkResultMsg.setData(jsonObject);
        return zkResultMsg;
    }

    @Override
    public void addPerson(String verifyModeRulePersonGroupId, List<String> personIdList) {
        AccVerifyModeRule accVerifyModeRule =
            accVerifyModeRuleDao.findById(verifyModeRulePersonGroupId).orElse(new AccVerifyModeRule());
        // List<AccPerson> accPersonList = accPersonDao.findAll(personIdList);
        List<AccPersonVerifyModeRule> accPersonVerifyModeRuleList =
            new ArrayList<AccPersonVerifyModeRule>(personIdList.size());
        // 进行分批处理
        List<List<String>> personIdsList = CollectionUtil.split(personIdList, AccConstants.LEVEL_SPLIT_COUNT);
        personIdsList.forEach(personIds -> {
            personIds.forEach(personId -> {
                AccPersonVerifyModeRule accPersonVerifyModeRule = new AccPersonVerifyModeRule();
                accPersonVerifyModeRule.setPersPersonId(personId);
                accPersonVerifyModeRule.setAccVerifyModeRule(accVerifyModeRule);
                accPersonVerifyModeRuleList.add(accPersonVerifyModeRule);
            });
        });

        accPersonVerifyModeRuleDao.saveAll(accPersonVerifyModeRuleList);
        Map<String, String> pinsByPersonIds = persPersonService.getPinsByPersonIds(personIdList);
        List<String> pinList = new ArrayList<>(pinsByPersonIds.values());
        List<AccDoor> accDoorList = accDoorVerifyModeRuleDao.getAccDoorByVerifyRuleId(accVerifyModeRule.getId());
        if (accDoorList != null && accDoorList.size() > 0) {
            List<AccDevice> devList =
                accDoorDao.getDeviceByDoorId((List<String>)CollectionUtil.getModelIdsList(accDoorList));
            for (AccDevice accDevice : devList) {
                Set<Short> doorNoSet = new HashSet<Short>();
                for (AccDoor door : accDoorList) {
                    if (door.getDevice().getId().equals(accDevice.getId())) {
                        doorNoSet.add(door.getDoorNo());
                    }
                }
                accDevCmdManager.setUserVSTimezoneToDev(accDevice, accVerifyModeRule, pinList, doorNoSet, false);
            }
        }
    }

    @Override
    public void delPerson(String verifyModeRuleGroupId, String personIds) {
        List<String> verifyModeRuleGroupIdList = (List<String>)CollectionUtil.strToList(verifyModeRuleGroupId);
        List<String> personIdList = (List<String>)CollectionUtil.strToList(personIds);
        accPersonVerifyModeRuleDao.delPersonByRuleIdAndPersId(verifyModeRuleGroupIdList, personIdList);
        AccVerifyModeRule accVerifyModeRule =
            accVerifyModeRuleDao.findById(verifyModeRuleGroupId).orElse(new AccVerifyModeRule());
        Map<String, String> pinsByPersonIds = persPersonService.getPinsByPersonIds(personIdList);
        List<String> pinList = new ArrayList<>(pinsByPersonIds.values());
        List<AccDoor> accDoorList = accDoorVerifyModeRuleDao.getAccDoorByVerifyRuleId(accVerifyModeRule.getId());
        if (accDoorList != null && accDoorList.size() > 0) {
            List<AccDevice> devList =
                accDoorDao.getDeviceByDoorId((List<String>)CollectionUtil.getModelIdsList(accDoorList));
            for (AccDevice accDevice : devList) {
                accDevCmdManager.delUserVSTimezoneFromDevByPin(accDevice, pinList, false);
            }
        }
    }

    @Override
    public void addDoor(String verifyModeRuleId, List<String> doorIdList) {
        AccVerifyModeRule accVerifyModeRule =
            accVerifyModeRuleDao.findById(verifyModeRuleId).orElse(new AccVerifyModeRule());
        List<AccDoor> accDoorList = accDoorDao.findByIdIn(doorIdList);
        List<AccDoorVerifyModeRule> accDoorVerifyModeRuleList =
            new ArrayList<AccDoorVerifyModeRule>(accDoorList.size());
        for (AccDoor accDoor : accDoorList) {
            AccDoorVerifyModeRule accDoorVerifyModeRule = new AccDoorVerifyModeRule();
            accDoorVerifyModeRule.setAccDoor(accDoor);
            accDoorVerifyModeRule.setAccVerifyModeRule(accVerifyModeRule);
            accDoorVerifyModeRuleList.add(accDoorVerifyModeRule);
        }
        accDoorVerifyModeRuleDao.save(accDoorVerifyModeRuleList);
        List<AccDevice> devList = accDoorDao.getDeviceByDoorId(doorIdList);
        List<String> personIdList = accPersonVerifyModeRuleDao.getPersonIdByAccVerifyModeRuleId(verifyModeRuleId);
        for (AccDevice dev : devList) {
            accDevCmdManager.setDiffTimezoneVSToDev(dev, accVerifyModeRule, false);
            Set<Short> doorNoSet = new HashSet<Short>();
            for (AccDoor door : accDoorList) {
                if (door.getDevice().getId().equals(dev.getId())) {
                    doorNoSet.add(door.getDoorNo());
                }
            }
            if (doorNoSet.size() > 0) {
                accDevCmdManager.setDoorVSTimezoneToDev(accVerifyModeRule, dev, doorNoSet, false);
            }
            if (personIdList != null && personIdList.size() > 0) {
                Map<String, String> pinsByPersonIds = persPersonService.getPinsByPersonIds(personIdList);
                List<String> pinList = new ArrayList<>(pinsByPersonIds.values());
                accDevCmdManager.setUserVSTimezoneToDev(dev, accVerifyModeRule, pinList, doorNoSet, false);
            }
        }
    }

    @Override
    public void delDoor(String verifyModeRuleId, String doorIds) {
        // delete from acc_verifymoderule_door where verifymoderule_id in (%s) and door_id in (%s)
        accDoorVerifyModeRuleDao.delDoorByRuleIdAndDoorId((List<String>)CollectionUtil.strToList(verifyModeRuleId),
            (List<String>)CollectionUtil.strToList(doorIds));
        List<AccDoor> accDoorList = accDoorDao.findByIdIn((List<String>)CollectionUtil.strToList(doorIds));
        List<AccDevice> devList = accDoorDao.getDeviceByDoorId((List<String>)CollectionUtil.strToList(doorIds));
        List<String> personIdList = accPersonVerifyModeRuleDao.getPersonIdByAccVerifyModeRuleId(verifyModeRuleId);
        for (AccDevice dev : devList) {
            Set<Short> doorNoSet = new HashSet<Short>();
            for (AccDoor door : accDoorList) {
                if (door.getDevice().getId().equals(dev.getId())) {
                    doorNoSet.add(door.getDoorNo());
                }
            }
            if (doorNoSet.size() > 0) {
                accDevCmdManager.delDoorVSTimezoneFromDev(dev, doorNoSet, false);
            }
            if (personIdList != null && personIdList.size() > 0) {
                Map<String, String> pinsByPersonIds = persPersonService.getPinsByPersonIds(personIdList);
                List<String> pinList = new ArrayList<>(pinsByPersonIds.values());
                accDevCmdManager.delUserVSTimezoneFromDevByPinAndDoorNo(dev, pinList, doorNoSet, false);
            }
        }
    }

    @Override
    public String getFilterDoorId(String id) {
        List<AccDeviceOptionItem> supportNewVfOptList = accDeviceOptionService.getItemsByOptName("NewVFStyles");
        Map<String, AccDeviceOptionItem> supportNewVfOptMap =
            CollectionUtil.listToKeyMap(supportNewVfOptList, AccDeviceOptionItem::getDeviceSn);
        AccVerifyModeRule accVerifyModeRule = accVerifyModeRuleDao.findById(id).orElse(new AccVerifyModeRule());

        Set<String> filterDoorIds = new HashSet<>();
        List<AccDevice> devSupportVerifyRule = new ArrayList<>();
        List<AccDevice> deviceList = accDeviceDao.findAll();
        for (AccDevice accDevice : deviceList) {
            if (!accDeviceOptionService.isSupportFunList(accDevice.getSn(), 12)) {
                // select id from AccDoor e where e.device.id=%s
                filterDoorIds.addAll(accDoorDao.getIdByDevId(accDevice.getId()));
            } else if (Objects.nonNull(accVerifyModeRule.getNewVerifyMode())
                && accVerifyModeRule.getNewVerifyMode() == AccConstants.IS_NEW_VERIFY_MODE) {
                if (!supportNewVfOptMap.containsKey(accDevice.getSn())) {
                    filterDoorIds.addAll(accDoorDao.getIdByDevId(accDevice.getId()));
                }
            } else {
                if (supportNewVfOptMap.containsKey(accDevice.getSn())) {
                    filterDoorIds.addAll(accDoorDao.getIdByDevId(accDevice.getId()));
                }
                devSupportVerifyRule.add(accDevice);
            }
        }

        if (devSupportVerifyRule.size() > 0) {
            // 统计出规则中含有的验证方式，用于之后过滤未同时含有这些验证方式的门
            List<Short> verifyNoList = new ArrayList<Short>();
            // AccVerifyModeRule accVerifyModeRule = accVerifyModeRuleDao.findById(id).orElse(new AccVerifyModeRule());
            Field[] fields = AccVerifyModeRule.class.getDeclaredFields();
            String regex = "([\\s\\S]*?)Door|Person";
            boolean hasTimeVSPerson = false;// 是否含有人的验证方式
            for (Field field : fields) {
                String name = field.getName();
                Matcher matcher = Pattern.compile(regex).matcher(name);
                if (matcher.find()) {
                    String methodName =
                        "get" + Character.toUpperCase(name.charAt(0)) + name.substring(1, name.length());
                    try {
                        Method method = AccVerifyModeRule.class.getMethod(methodName);
                        Short verifyNo = (Short)method.invoke(accVerifyModeRule);
                        if (verifyNo != null && verifyNo != 255 && !verifyNoList.contains(verifyNo)) {
                            if (!hasTimeVSPerson && methodName.endsWith("Person")) {
                                hasTimeVSPerson = true;
                            }
                            verifyNoList.add(verifyNo);
                        }
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
            filterDoorIds.addAll(filterDoorIdByVerifyNo(devSupportVerifyRule, verifyNoList));
            if (hasTimeVSPerson) {
                filterDoorIds.addAll(accReaderDao.getDoorIdWith485Reader());
            }
        }

        List<String> doorIdWithVerifyRyle = accDoorVerifyModeRuleDao.getDoorIdByVerifyRule();
        filterDoorIds.addAll(doorIdWithVerifyRyle);
        List<String> doorIdList = accDoorService.getDoorIdAsReader();
        if (doorIdList != null && doorIdList.size() > 0) {
            filterDoorIds.addAll(doorIdList);
        }

        return (filterDoorIds != null && filterDoorIds.size() > 0) ? StringUtils.join(filterDoorIds, ",") : "";
    }

    @Override
    public List<String> getExistPersonIds() {
        return accPersonVerifyModeRuleDao.getPersonIdByCombPersonId();
    }

    @Override
    public void syncVerifyModeRuleToDev(String deviceId) {
        AccDevice accDevice = accDeviceDao.findById(deviceId).orElse(new AccDevice());
        List<AccVerifyModeRule> accVerifyModeRuleList = accVerifyModeRuleDao.getRuleByDevId(deviceId);
        if (accVerifyModeRuleList != null && accVerifyModeRuleList.size() > 0) {
            accVerifyModeRuleList.stream().forEach(accVerifyModeRule -> {
                List<AccDoor> accDoorList = accDoorVerifyModeRuleDao
                    .getAccDoorByVerifyModeRuleIdAndDeviceId(accVerifyModeRule.getId(), deviceId);
                if (accDoorList != null && accDoorList.size() > 0) {
                    List<String> personIdList =
                        accPersonVerifyModeRuleDao.getPersonIdByAccVerifyModeRuleId(accVerifyModeRule.getId());
                    accDevCmdManager.setDiffTimezoneVSToDev(accDevice, accVerifyModeRule, false);
                    Set<Short> doorNoSet = new HashSet<Short>();
                    accDoorList.stream().forEach(accDoor -> {
                        if (accDoor.getDevice().getId().equals(deviceId)) {
                            doorNoSet.add(accDoor.getDoorNo());
                        }
                    });
                    if (doorNoSet.size() > 0) {
                        accDevCmdManager.setDoorVSTimezoneToDev(accVerifyModeRule, accDevice, doorNoSet, false);
                    }
                    if (personIdList.size() > 0) {
                        Map<String, String> pinsByPersonIds = persPersonService.getPinsByPersonIds(personIdList);
                        List<String> pinList = new ArrayList<>(pinsByPersonIds.values());
                        accDevCmdManager.setUserVSTimezoneToDev(accDevice, accVerifyModeRule, pinList, doorNoSet,
                            false);
                    }
                }
            });
        }
    }

    @Override
    public List<SelectItem> getTimeSegListWithoutRule(String id) {
        List<AccTimeSeg> accTimeSegList = null;
        List<String> timeSegIdList = accVerifyModeRuleDao.getTimeSegIdByVerifyRule(id);
        if (timeSegIdList.size() > 0) {
            accTimeSegList = accTimeSegDao.findByIdNotIn(timeSegIdList);
        } else {
            accTimeSegList = accTimeSegDao.findAll();
        }
        List<SelectItem> selectItems = new ArrayList<>();
        SelectItem selectItem = null;
        for (AccTimeSeg accTimeSeg : accTimeSegList) {
            selectItem = new SelectItem();
            selectItem.setValue(accTimeSeg.getId());
            selectItem.setText(accTimeSeg.getName());
            selectItems.add(selectItem);
        }
        return selectItems;
    }

    /**
     * 根据验证方式编号来过滤门
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2016年10月14日 下午2:57:08
     * @param devSupportVerifyRule
     * @param verifyNoList
     * @return
     */
    private List<String> filterDoorIdByVerifyNo(List<AccDevice> devSupportVerifyRule, List<Short> verifyNoList) {
        List<String> filterDoorId = new ArrayList<String>();
        List<AccDeviceVerifyMode> accDeviceVerifyModeList = null;
        for (AccDevice dev : devSupportVerifyRule) {
            accDeviceVerifyModeList = accDeviceVerifyModeDao.findByVerifyNoInAndAccDevice_Id(verifyNoList, dev.getId());
            if (accDeviceVerifyModeList == null || accDeviceVerifyModeList.size() != verifyNoList.size()) {
                filterDoorId.addAll(accDoorDao.getIdByDevId(dev.getId()));
            }
        }
        return filterDoorId;
    }
}