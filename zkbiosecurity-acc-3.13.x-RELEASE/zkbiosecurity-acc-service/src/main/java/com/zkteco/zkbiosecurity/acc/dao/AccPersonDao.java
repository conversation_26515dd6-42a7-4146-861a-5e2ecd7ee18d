/**
 * File Name: AccPerson Created by GenerationTools on 2018-03-02 下午02:10 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.acc.model.AccPerson;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccPersonDao
 * 
 * <AUTHOR>
 * @date: 2018-03-02 下午02:10
 * @version v1.0
 */
public interface AccPersonDao extends BaseDao<AccPerson, String> {

    /**
     * 根据人员id获取门禁人员id--防止门禁人员和人事人员表数据不同步
     * 
     * <AUTHOR>
     * @since 2018/3/22 17:51
     * @return
     */
    @Query(value = "select p.id from AccPerson p where p.personId in (?1)")
    List<String> getIdByPersPersonId(List<String> strings);

    List<AccPerson> findByIdIn(List<String> strings);

    @Query(value = "select e.id from AccPerson e where e.personId in (?1)")
    List<String> getIdsByPersonId(Collection<String> personIds);

    List<AccPerson> findByPersonIdIn(List<String> personIds);

    AccPerson findByPersonId(String personId);

    @Query(value = "select e.personId from AccPerson e where e.id in (?1)")
    List<String> getPersPersonIdsByAccPersonId(List<String> accPersonId);

    void deleteAccPersonByPersonIdIn(List<String> personList);

    @Query(value = "select p.personId from AccPerson p where p.privilege in (?1)")
    List<String> getPersonIdByPrivilegeIn(List<Short> privileges);
}