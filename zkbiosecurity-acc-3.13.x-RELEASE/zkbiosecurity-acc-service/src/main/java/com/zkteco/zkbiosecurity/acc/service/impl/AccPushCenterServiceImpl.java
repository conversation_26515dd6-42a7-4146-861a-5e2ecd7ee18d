package com.zkteco.zkbiosecurity.acc.service.impl;

import com.alibaba.fastjson.JSON;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.push.enums.PushCenter4OtherEnum;
import com.zkteco.zkbiosecurity.push.service.PushCenter4OtherService;
import com.zkteco.zkbiosecurity.push.service.PushCenterNotifyOtherService;
import com.zkteco.zkbiosecurity.push.vo.PushCenter4OtherItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/9/8 9:38
 * @since 1.0.0
 */
@Slf4j
@Service
public class AccPushCenterServiceImpl implements PushCenterNotifyOtherService {

    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private AccTransactionDao accTransactionDao;
    @Autowired(required = false)
    private PushCenter4OtherService pushCenter4OtherService;

    @Override
    public boolean notifyPushData(Date startTime, Date endTime, PushCenter4OtherEnum pushCenter4OtherEnum) {
        // 是否由本模块处理
        boolean isHandle = false;
        if (Objects.nonNull(pushCenter4OtherService)) {
            try {
                if (PushCenter4OtherEnum.ACC_TRANSACTION.equals(pushCenter4OtherEnum)
                    || PushCenter4OtherEnum.ACC_TRANSACTION_IMAGE.equals(pushCenter4OtherEnum)) {
                    isHandle = true;
                    PushCenter4OtherItem pushCenter4OtherItem = new PushCenter4OtherItem();
                    AccTransactionItem condition = new AccTransactionItem();
                    condition.setStartTime(startTime);
                    condition.setEndTime(endTime);
                    // 分页处理
                    int beginIndex = 0;
                    while (true) {
                        int pageSize = CollectionUtil.splitSize;
                        int endIndex = beginIndex + (pageSize - 1);
                        List<AccTransactionItem> accTransactionItemList = accTransactionDao.getItemsDataBySql(
                            AccTransactionItem.class, SQLUtil.getSqlByItem(condition), beginIndex, endIndex, true);
                        if (accTransactionItemList != null && accTransactionItemList.size() > 0) {
                            List<String> accTransList = new ArrayList<>();
                            List<String> accPhotoUrlList = new ArrayList<>();
                            for (AccTransactionItem accTrans : accTransactionItemList) {
                                accTransList.add(JSON.toJSONString(accTrans));
                                // 考勤照片
                                if (StringUtils.isNotBlank(accTrans.getCapturePhotoPath())
                                    && accTrans.getCapturePhotoPath().contains(AccConstants.LINKAGE_PHOTO_PATH)) {
                                    accPhotoUrlList.add(accTrans.getCapturePhotoPath());
                                }
                            }
                            if (PushCenter4OtherEnum.ACC_TRANSACTION.equals(pushCenter4OtherEnum)) {
                                pushCenter4OtherItem.setPushCenter4OtherEnum(PushCenter4OtherEnum.ACC_TRANSACTION);
                                pushCenter4OtherItem.setContentList(accTransList);
                            } else {
                                pushCenter4OtherItem
                                    .setPushCenter4OtherEnum(PushCenter4OtherEnum.ACC_TRANSACTION_IMAGE);
                                pushCenter4OtherItem.setContentList(accPhotoUrlList);
                            }
                            pushCenter4OtherService.pushData(pushCenter4OtherItem);
                            if (accTransactionItemList.size() < pageSize) {
                                break;
                            }
                        } else {
                            break;
                        }
                        beginIndex = endIndex + 1;
                    }
                }
            } catch (Exception e) {
                log.error("accPushCenterService notifyPushData error", e);
            }
        }
        return isHandle;
    }
}
