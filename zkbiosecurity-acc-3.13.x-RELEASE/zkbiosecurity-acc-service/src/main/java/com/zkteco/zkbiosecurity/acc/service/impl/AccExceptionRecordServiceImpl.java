package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.zkteco.zkbiosecurity.acc.model.AccExceptionRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.dao.AccExceptionRecordDao;
import com.zkteco.zkbiosecurity.acc.service.AccExceptionRecordService;
import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.vo.AccExceptionRecordItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;

/**
 * 异常记录服务实现类
 * 
 * @author: AI Assistant
 * @date: 2025-07-21
 */
@Service
@Transactional
public class AccExceptionRecordServiceImpl implements AccExceptionRecordService {

    @Autowired
    private AccExceptionRecordDao accExceptionRecordDao;
    
    @Autowired
    private AccTransactionService accTransactionService;

    @Override
    public AccExceptionRecordItem saveItem(AccExceptionRecordItem item) {
        AccExceptionRecord accExceptionRecord = Optional.ofNullable(item)
            .map(i -> i.getId())
            .filter(StringUtils::isNotBlank)
            .flatMap(id -> accExceptionRecordDao.findById(id))
            .orElse(new AccExceptionRecord());
            
        ModelUtil.copyProperties(item, accExceptionRecord);
        accExceptionRecordDao.save(accExceptionRecord);
        item.setId(accExceptionRecord.getId());
        return item;
    }

    @Override
    public List<AccExceptionRecordItem> getByCondition(AccExceptionRecordItem condition) {
        return (List<AccExceptionRecordItem>) accExceptionRecordDao.getItemsBySql(
            AccExceptionRecordItem.class, SQLUtil.getSqlByItem(condition));
    }

    @Override
    public AccExceptionRecordItem getItemById(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        AccExceptionRecordItem condition = new AccExceptionRecordItem();
        condition.setId(id);
        List<AccExceptionRecordItem> items = getByCondition(condition);
        return items.isEmpty() ? null : items.get(0);
    }

    @Override
    public Pager loadExceptionRecordByAuthUserFilter(String sessionId, AccExceptionRecordItem condition, 
                                                     int pageNo, int pageSize,
                                                     long limitCount) {
        // 设置权限过滤条件
        
        return accExceptionRecordDao.getItemsBySql(condition.getClass(), 
            SQLUtil.getSqlByItem(condition), pageNo, pageSize, limitCount);
    }

    @Override
    public List<?> getItemData(Class targetClass, BaseItem condition, int beginIndex, int endIndex) {
        return accExceptionRecordDao.getItemsDataBySql(targetClass,
            SQLUtil.getSqlByItem(condition), beginIndex, endIndex,false);
    }

    @Override
    public String getAreaNamesBySessionId(String sessionId) {
        // 复用门禁事务服务的方法
        return accTransactionService.getAreaNamesBySessionId(sessionId);
    }

    @Override
    public String getDeptCodesBySessionId(String sessionId) {
        // 复用门禁事务服务的方法
        return accTransactionService.getDeptCodesBySessionId(sessionId);
    }

    @Override
    public AccExceptionRecordItem createExceptionRecord(String pin, String name, String deptName, 
                                                       String receiverPosition, String readerName, 
                                                       Date enterTime, Date exitTime, 
                                                       String subject, String exceptionStatus) {
        AccExceptionRecordItem item = new AccExceptionRecordItem();
        item.setPin(pin);
        item.setName(name);
        item.setDeptName(deptName);
        item.setReceiverPosition(receiverPosition);
        item.setReaderName(readerName);
        item.setEnterTime(enterTime);
        item.setExitTime(exitTime);
        item.setSubject(subject);
        item.setExceptionStatus(exceptionStatus);
        item.setStatus((short) 0); // 0: 未发送

        return saveItem(item);
    }

    @Override
    public boolean sendExceptionNotification(AccExceptionRecordItem item) {
        try {
            // 这里可以集成微信公众号、邮件、短信等通知方式
            // 模拟发送逻辑
            
            // 更新发送时间和状态
            item.setSendTime(new Date());
            item.setStatus((short) 1); // 1: 发送成功
            item.setErrorMessage(null);
            saveItem(item);
            
            return true;
        } catch (Exception e) {
            // 发送失败，记录错误信息
            item.setSendTime(new Date());
            item.setStatus((short) 2); // 2: 发送失败
            item.setErrorMessage(e.getMessage());
            saveItem(item);
            
            return false;
        }
    }

    @Override
    public AccExceptionRecordItem updateExceptionStatus(String id, String exceptionStatus) {
        AccExceptionRecordItem item = getItemById(id);
        if (item != null) {
            item.setExceptionStatus(exceptionStatus);
//            item.setUpdater("system");
            return saveItem(item);
        }
        return null;
    }

    @Override
    public Pager getItemsByPage(BaseItem baseItem, int i, int i1) {
        return accExceptionRecordDao.getItemsBySql(AccExceptionRecordItem.class, SQLUtil.getSqlByItem(baseItem), i, i1);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<String> idList = Arrays.asList(ids.split(","));
            idList.forEach(id->{
                accExceptionRecordDao.deleteById(id);

            });
        }
        return true;
    }

    @Override
    public void deleteAllData() {
        accExceptionRecordDao.deleteAll();
    }

    @Override
    public int resendFailedRecords(String ids) {
        if (StringUtils.isBlank(ids)) {
            return 0;
        }
        
        List<String> idList = Arrays.asList(ids.split(","));
        int successCount = 0;
        
        for (String id : idList) {
            AccExceptionRecordItem item = getItemById(id);
            if (item != null && item.getStatus() != null && item.getStatus() == 2) {
                if (sendExceptionNotification(item)) {
                    successCount++;
                }
            }
        }
        
        return successCount;
    }
}
