package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccBaseDictionaryService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccRTMonitorService;
import com.zkteco.zkbiosecurity.acc.service.AccReaderService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.vms.service.Vms4AccDoorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class Vms4AccDoorServiceImpl implements Vms4AccDoorService {

    @Autowired
    private AccReaderService accReaderService;
    @Autowired
    private AccRTMonitorService accRTMonitorService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccBaseDictionaryService accBaseDictionaryService;

    @Override
    public ZKResultMsg operateDoorByReaderIds(String opType, String openInterval, String readerIds) {
        List<String> doorIds = accReaderService.getDoorIdByReaderId(StrUtil.strToList(readerIds));
        if (Objects.nonNull(doorIds) && doorIds.size() > 0) {
            Map<String, String> dataMap = accRTMonitorService.operateDoor(opType, openInterval, StrUtil.collectionToStr(doorIds));
            return dealResultData(dataMap);
        }
        return ZKResultMsg.failMsg();
    }

    private ZKResultMsg dealResultData(Map<String, String> dataMap) {
        String cmdIdData = dataMap.get("cmdId");
        String msg = "";
        ZKResultMsg resultMsg = new ZKResultMsg();
        if ("true".equals(dataMap.get("notExistDev"))) {
            msg = I18nUtil.i18nCode("common_dev_opFaileAndReason") + I18nUtil.i18nCode("common_dev_notExistDev");
            resultMsg.setMsg(msg);
            return resultMsg;
        }
        if (!"".equals(cmdIdData)) {
            for (String cmdData : cmdIdData.split(",")) {
                String cmdId = cmdData.split("=")[0];
                String doorName = cmdData.split("=")[1];
                Map<String, String> resultMap = accDeviceService.getCmdResultById(Long.valueOf(cmdId), 20);
                if (Objects.isNull(resultMap)) {
                    msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                }
                else {
                    Integer ret = Integer.valueOf(resultMap.get("result"));
                    if (Objects.isNull(ret)) {
                        msg += doorName + "," + I18nUtil.i18nCode("common_op_failed") + ";";
                    }
                    else if (ret < 0) {
                        String failedInfo = I18nUtil.i18nCode(accBaseDictionaryService.getCommReason(ret));
                        msg += doorName + "," + I18nUtil.i18nCode("common_dev_opFaileAndReason") + failedInfo + ";";
                    }
                }
            }
        }
        if (!"".equals(dataMap.get("offline"))) {
            for (String doorName : dataMap.get("offline").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("common_dev_offlinePrompt") + ";";
            }
        }
        if (!"".equals(dataMap.get("notSupport"))) {
            for (String doorName : dataMap.get("notSupport").split(",")) {
                msg += doorName + "," + I18nUtil.i18nCode("acc_dev_devNotSupportFunction") + ";";
            }
        }
        if (!"".equals(msg)) {
            resultMsg.setRet("400");
            resultMsg.setMsg(msg);
        }
        resultMsg.setData("");
        return resultMsg;
    }
}
