package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.patrol.service.PatrolGetAccDeviceService;
import com.zkteco.zkbiosecurity.patrol.vo.PatrolGetAccDeviceItem;
import com.zkteco.zkbiosecurity.patrol.vo.PatrolGetAccSelectDeviceItem;

@Service
@Transactional
public class PatrolGetAccDeviceServiceImpl implements PatrolGetAccDeviceService {

    @Autowired
    private AccDeviceService accDeviceService;

    @Override
    public PatrolGetAccDeviceItem getPatrolDevice(PatrolGetAccDeviceItem patrolGetAccDeviceItem) {
        AccDeviceItem accDeviceItem = accDeviceService.getItemById(patrolGetAccDeviceItem.getDeviceId());
        return setPatrolGetAccDevItem(patrolGetAccDeviceItem, accDeviceItem);
    }

    /**
     * 设置巡更设备信息
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-11-28 10:48
     * @param patrolGetAccDeviceItem
     * @param accDeviceItem
     * @return com.zkteco.zkbiosecurity.patrol.vo.PatrolGetAccDeviceItem
     */
    private PatrolGetAccDeviceItem setPatrolGetAccDevItem(PatrolGetAccDeviceItem patrolGetAccDeviceItem,
        AccDeviceItem accDeviceItem) {
        patrolGetAccDeviceItem.setAuthAreaId(accDeviceItem.getAuthAreaId());
        patrolGetAccDeviceItem.setAuthAreaName(accDeviceItem.getAuthAreaName());
        patrolGetAccDeviceItem.setDeviceAlias(accDeviceItem.getAlias());
        patrolGetAccDeviceItem.setDeviceName(accDeviceItem.getDeviceName());
        patrolGetAccDeviceItem.setDeviceSn(accDeviceItem.getSn());
        patrolGetAccDeviceItem.setDeviceId(accDeviceItem.getId());
        return patrolGetAccDeviceItem;
    }

    @Override
    public Pager getItemByAuthFilter(String sessionId, PatrolGetAccSelectDeviceItem patrolGetAccSelectDeviceItem,
        int page, int size) {
        AccSelectDeviceItem item = new AccSelectDeviceItem();
        item.setAreaIdIn(patrolGetAccSelectDeviceItem.getAreaIdIn());
        item.setInId(patrolGetAccSelectDeviceItem.getInId());
        item.setNotInId(patrolGetAccSelectDeviceItem.getNotInId());
        item.setAuthAreaId(patrolGetAccSelectDeviceItem.getAuthAreaId());
        item.setAuthAreaName(patrolGetAccSelectDeviceItem.getAuthAreaName());
        item.setDeviceAlias(patrolGetAccSelectDeviceItem.getDeviceAlias());
        item.setDeviceName(patrolGetAccSelectDeviceItem.getDeviceName());
        item.setDeviceSn(patrolGetAccSelectDeviceItem.getDeviceSn());
        Pager pager = accDeviceService.getItemByAuthFilter(sessionId, item, page, size);
        List<AccSelectDeviceItem> accDeviceItemList = (List<AccSelectDeviceItem>)pager.getData();
        List<PatrolGetAccSelectDeviceItem> items = new ArrayList<PatrolGetAccSelectDeviceItem>();
        items = ModelUtil.copyListProperties(accDeviceItemList, PatrolGetAccSelectDeviceItem.class);
        pager.setData(items);
        return pager;
    }

    @Override
    public List<PatrolGetAccDeviceItem> getItemsByDevSnIn(List<String> sns) {
        List<AccDeviceItem> accDeviceItems = accDeviceService.getItemsByDevSnIn(sns);
        List<PatrolGetAccDeviceItem> items = new ArrayList<PatrolGetAccDeviceItem>();
        PatrolGetAccDeviceItem patrolGetAccDeviceItem = null;
        for (AccDeviceItem accDeviceItem : accDeviceItems) {
            patrolGetAccDeviceItem = new PatrolGetAccDeviceItem();
            patrolGetAccDeviceItem = setPatrolGetAccDevItem(patrolGetAccDeviceItem, accDeviceItem);
            items.add(patrolGetAccDeviceItem);
        }
        return items;
    }

    @Override
    public List<TreeItem> getAccDeviceVerifyModeTree() {
        List<TreeItem> items = new ArrayList<>();
        Map<Integer, String> verifyModesMap = AccConstants.VERIFY_MODE;
        // 对map的key进行升序排序
        Set keySet = verifyModesMap.keySet();
        Object[] keySetArray = keySet.toArray();
        Arrays.sort(keySetArray);
        for (Object key : keySetArray) {
            TreeItem item = new TreeItem();
            item.setId(verifyModesMap.get(key));
            item.setText(I18nUtil.i18nCode(verifyModesMap.get(key)));
            item.setParent(new TreeItem("0"));
            items.add(item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return treeItems;
    }
}
