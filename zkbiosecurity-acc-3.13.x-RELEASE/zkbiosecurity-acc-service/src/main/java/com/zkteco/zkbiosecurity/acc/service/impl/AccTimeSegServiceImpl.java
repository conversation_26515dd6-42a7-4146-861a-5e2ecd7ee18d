package com.zkteco.zkbiosecurity.acc.service.impl;

import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.cmd.AccDevCmdManager;
import com.zkteco.zkbiosecurity.acc.dao.*;
import com.zkteco.zkbiosecurity.acc.model.*;
import com.zkteco.zkbiosecurity.acc.service.*;
import com.zkteco.zkbiosecurity.acc.vo.AccTimeSegItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalTime;
import java.util.*;

/**
 * 门禁时间段service实现类
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午10:18
 * @version v1.0
 */
@Service
public class AccTimeSegServiceImpl implements AccTimeSegService {
    @Autowired
    private AccTimeSegDao accTimeSegDao;
    @Autowired
    private AccTimeSegBIdDao accTimeSegBIdDao;
    @Autowired
    private AccDevCmdManager accDevCmdManager;
    @Autowired
    private AccDeviceDao accDeviceDao;
    @Autowired
    private AccDoorDao accDoorDao;
    @Autowired(required = false)
    private Acc4AccGlobalLinkageService acc4AccGlobalLinkageService;
    @Autowired
    private AccVerifyModeRuleDao accVerifyModeRuleDao;
    @Autowired
    private AccDoorVerifyModeRuleDao accDoorVerifyModeRuleDao;
    @Autowired
    private AccFirstOpenService accFirstOpenService;
    @Autowired
    private AccAuxInService accAuxInService;
    @Autowired
    private AccAuxOutService accAuxOutService;
    @Autowired
    private AccLevelService accLevelService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccTimeSegItem initData(AccTimeSegItem item) {
        AccTimeSeg accTimeSeg = accTimeSegDao.findByInitFlag(item.getInitFlag());
        if (accTimeSeg == null) {
            return this.saveItem(item);
        } else {
            item.setId(accTimeSeg.getId());
            return item;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AccTimeSegItem saveItem(AccTimeSegItem item) {
        AccTimeSeg accTimeSeg = Optional.ofNullable(item).map(AccTimeSegItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(id -> accTimeSegDao.findById(id)).orElse(new AccTimeSeg());
        boolean isModified = false;
        if ((accTimeSeg.getId() == null)) {
            isModified = true;
        } else {
            AccTimeSeg newAccTimeSeg = new AccTimeSeg();
            ModelUtil.copyPropertiesIgnoreNull(item, newAccTimeSeg);
            if (!accTimeSeg.equals(newAccTimeSeg)) {
                isModified = true;
            }
        }
        ModelUtil.copyPropertiesIgnoreNull(item, accTimeSeg);
        if (accTimeSeg.getBusinessId() == null) {
            accTimeSeg.setBusinessId(createBId());
        }
        if (item.getStartTime() == null) {
            accTimeSeg.setStartTime(null);
        }
        if (item.getEndTime() == null) {
            accTimeSeg.setEndTime(null);
        }
        // 设置有效时间
        setValidTime(accTimeSeg);
        accTimeSegDao.save(accTimeSeg);
        if (isModified) { // 新增和改动后下发命令
            List<AccTimeSeg> timeSegList = new ArrayList<>();
            timeSegList.add(accTimeSeg);
            List<AccDevice> devList = accDeviceDao.findAll();
            devList.forEach(accDevice -> {
                List<AccTimeSegItem> timeSegItemList = ModelUtil.copyListProperties(timeSegList, AccTimeSegItem.class);
                accDevCmdManager.setTimeSegToDev(accDevice.getSn(), timeSegItemList, false);
            });
        }
        if (StringUtils.isNotBlank(accTimeSeg.getId())) {
            AccVerifyModeRule accVerifyModeRule = accVerifyModeRuleDao.findByAccTimeSeg_Id(accTimeSeg.getId());
            if (accVerifyModeRule != null) {
                List<AccDoor> doorIdList = accDoorVerifyModeRuleDao.getAccDoorByVerifyRuleId(accVerifyModeRule.getId());
                if (doorIdList != null && !doorIdList.isEmpty()) {
                    List<AccDevice> devList =
                        accDoorDao.getDeviceByDoorId((List<String>)CollectionUtil.getModelIdsList(doorIdList));
                    for (AccDevice accDevice : devList) {
                        accDevCmdManager.setDiffTimezoneVSToDev(accDevice, accVerifyModeRule, false);
                    }
                }
            }
        }
        item.setId(accTimeSeg.getId());
        return item;
    }

    /**
     * 设置有效时间
     */
    private void setValidTime(AccTimeSeg timeSeg) {
        if (timeSeg.getEndTime() != null) {
            // 结束时间23:59:59
            Date endTime = DateUtil.getDayEndTime(timeSeg.getEndTime());
            timeSeg.setEndTime(endTime);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBId() {
        AccTimeSegBId accTimeSegBId = new AccTimeSegBId();
        accTimeSegBId = accTimeSegBIdDao.save(accTimeSegBId);
        return accTimeSegBId.getId();
    }

    @Override
    public List<AccTimeSegItem> getByCondition(AccTimeSegItem condition) {
        return (List<AccTimeSegItem>)accTimeSegDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return accTimeSegDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<Long> timeSegIdList = new ArrayList<>();
            boolean isRefered = false;
            List<AccTimeSeg> accTimeSegList = accTimeSegDao.findAll(CollectionUtil.strToList(ids));
            for (AccTimeSeg accTimeSeg : accTimeSegList) {
                if (Objects.isNull(accTimeSeg.getInitFlag()) || !accTimeSeg.getInitFlag()) {
                    if (accLevelService.checkTimeSegUsed(accTimeSeg.getId())) {
                        isRefered = true;
                        break;
                    }
                    if (accDoorDao.countByActiveTimeSegId(accTimeSeg.getId()) > 0) {
                        isRefered = true;
                        break;
                    }
                    if (accDoorDao.countByPassModeTimeSegId(accTimeSeg.getId()) > 0) {
                        isRefered = true;
                        break;
                    }
                    if (accDoorDao.countByLatchTimeSegId(accTimeSeg.getId()) > 0) {
                        isRefered = true;
                        break;
                    }
                    if (accFirstOpenService.checkTimeSegUsed(accTimeSeg.getId())) {
                        isRefered = true;
                        break;
                    }
                    if (accAuxInService.checkTimeSegUsed(accTimeSeg.getId())) {
                        isRefered = true;
                        break;
                    }
                    if (accAuxOutService.checkTimeSegUsed(accTimeSeg.getId())) {
                        isRefered = true;
                        break;
                    }
                    if (acc4AccGlobalLinkageService != null
                        && acc4AccGlobalLinkageService.checkTimesegUsed(accTimeSeg.getId())) {
                        isRefered = true;
                        break;
                    }
                    if (accVerifyModeRuleDao.countByAccTimeSeg(accTimeSeg) > 0) {
                        isRefered = true;
                        break;
                    }
                    timeSegIdList.add(accTimeSeg.getBusinessId());
                } else {
                    throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN,
                        "common_prompt_initDataCanNotDel");
                }
            }
            if (isRefered) {
                throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN, "acc_timeSeg_canNotDel");
            } else {
                accTimeSegDao.deleteInBatch(accTimeSegList);
                if (!timeSegIdList.isEmpty()) {
                    List<AccDevice> devList = accDeviceDao.findAll();
                    devList.forEach(accDevice -> {
                        accDevCmdManager.delTimeSegFromDev(accDevice.getSn(), timeSegIdList, false);
                    });
                }
            }
        }
        return true;
    }

    @Override
    public AccTimeSegItem getItemById(String id) {
        List<AccTimeSegItem> items = getByCondition(new AccTimeSegItem(id));
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<SelectItem> getTimeSegList() {
        List<AccTimeSegItem> accTimeSegList = getByCondition(new AccTimeSegItem());
        List<SelectItem> selectItems = new ArrayList<>();
        SelectItem selectItem = null;
        for (AccTimeSegItem accTimeSeg : accTimeSegList) {
            selectItem = new SelectItem();
            selectItem.setValue(accTimeSeg.getId());
            selectItem.setText(accTimeSeg.getName());
            selectItems.add(selectItem);
        }
        return selectItems;
    }

    @Override
    public AccTimeSegItem getInitTimeSeg() {
        AccTimeSegItem item = new AccTimeSegItem();
        item.setInitFlag(true);
        List<AccTimeSegItem> items = getByCondition(item);
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public String validName(String name) {
        AccTimeSeg accTimeSeg = accTimeSegDao.findByName(name);
        return accTimeSeg == null ? "true" : "false";
    }

    @Override
    public Map<String, String> getActiveTimeSegByDoorId(List<String> doorIdList) {
        Map<String, String> dataMap = Maps.newHashMap();
        List<Object[]> doorActiveTimeList = accTimeSegDao.getActiveTimeSegByDoorId(doorIdList);
        if (Objects.nonNull(doorActiveTimeList) && !doorActiveTimeList.isEmpty()) {
            doorActiveTimeList.stream().forEach(objects -> dataMap.put(objects[0].toString(), objects[1].toString()));
        }
        return dataMap;
    }

    @Override
    public Map<String, String> getPassmodeTimeSegByDoorId(List<String> doorIdList) {
        Map<String, String> dataMap = Maps.newHashMap();
        List<Object[]> doorActiveTimeList = accTimeSegDao.getPassmodeTimeSegByDoorId(doorIdList);
        if (Objects.nonNull(doorActiveTimeList) && !doorActiveTimeList.isEmpty()) {
            doorActiveTimeList.forEach(objects -> dataMap.put(objects[0].toString(), objects[1].toString()));
        }
        return dataMap;
    }

    @Override
    public boolean checkTimeSeg(String timeSegId, int date, Date eventDate) {
        AccTimeSeg timeSeg = accTimeSegDao.findById(timeSegId).orElse(null);
        boolean isInTimeSeg = false;
        if (Objects.nonNull(timeSeg)) {
            String startTime1 = null;
            String endTime1 = null;
            String startTime2 = null;
            String endTime2 = null;
            String startTime3 = null;
            String endTime3 = null;
            // 判断具体时间是星期几或者哪种节假日类型
            switch (date) {
                case 0:
                    startTime1 = timeSeg.getSundayStart1();
                    endTime1 = timeSeg.getSundayEnd1();
                    startTime2 = timeSeg.getSundayStart2();
                    endTime2 = timeSeg.getSundayEnd2();
                    startTime3 = timeSeg.getSundayStart3();
                    endTime3 = timeSeg.getSundayEnd3();
                    break;
                case 1:
                    startTime1 = timeSeg.getMondayStart1();
                    endTime1 = timeSeg.getMondayEnd1();
                    startTime2 = timeSeg.getMondayStart2();
                    endTime2 = timeSeg.getMondayEnd2();
                    startTime3 = timeSeg.getMondayStart3();
                    endTime3 = timeSeg.getMondayEnd3();
                    break;
                case 2:
                    startTime1 = timeSeg.getTuesdayStart1();
                    endTime1 = timeSeg.getTuesdayEnd1();
                    startTime2 = timeSeg.getTuesdayStart2();
                    endTime2 = timeSeg.getTuesdayEnd2();
                    startTime3 = timeSeg.getTuesdayStart3();
                    endTime3 = timeSeg.getTuesdayEnd3();
                    break;
                case 3:
                    startTime1 = timeSeg.getWednesdayStart1();
                    endTime1 = timeSeg.getWednesdayEnd1();
                    startTime2 = timeSeg.getWednesdayStart2();
                    endTime2 = timeSeg.getWednesdayEnd2();
                    startTime3 = timeSeg.getWednesdayStart3();
                    endTime3 = timeSeg.getWednesdayEnd3();
                    break;
                case 4:
                    startTime1 = timeSeg.getThursdayStart1();
                    endTime1 = timeSeg.getThursdayEnd1();
                    startTime2 = timeSeg.getThursdayStart2();
                    endTime2 = timeSeg.getThursdayEnd2();
                    startTime3 = timeSeg.getThursdayStart3();
                    endTime3 = timeSeg.getThursdayEnd3();
                    break;
                case 5:
                    startTime1 = timeSeg.getFridayStart1();
                    endTime1 = timeSeg.getFridayEnd1();
                    startTime2 = timeSeg.getFridayStart2();
                    endTime2 = timeSeg.getFridayEnd2();
                    startTime3 = timeSeg.getFridayStart3();
                    endTime3 = timeSeg.getFridayEnd3();
                    break;
                case 6:
                    startTime1 = timeSeg.getSaturdayStart1();
                    endTime1 = timeSeg.getSaturdayEnd1();
                    startTime2 = timeSeg.getSaturdayStart2();
                    endTime2 = timeSeg.getSaturdayEnd2();
                    startTime3 = timeSeg.getSaturdayStart3();
                    endTime3 = timeSeg.getSaturdayEnd3();
                    break;
                case 7:
                    startTime1 = timeSeg.getHolidayType1Start1();
                    endTime1 = timeSeg.getHolidayType1End1();
                    startTime2 = timeSeg.getHolidayType1Start2();
                    endTime2 = timeSeg.getHolidayType1End2();
                    startTime3 = timeSeg.getHolidayType1Start3();
                    endTime3 = timeSeg.getHolidayType1End3();
                    break;
                case 8:
                    startTime1 = timeSeg.getHolidayType2Start1();
                    endTime1 = timeSeg.getHolidayType2End1();
                    startTime2 = timeSeg.getHolidayType2Start2();
                    endTime2 = timeSeg.getHolidayType2End2();
                    startTime3 = timeSeg.getHolidayType2Start3();
                    endTime3 = timeSeg.getHolidayType2End3();
                    break;
                case 9:
                    startTime1 = timeSeg.getHolidayType3Start1();
                    endTime1 = timeSeg.getHolidayType3End1();
                    startTime2 = timeSeg.getHolidayType3Start2();
                    endTime2 = timeSeg.getHolidayType3End2();
                    startTime3 = timeSeg.getHolidayType3Start3();
                    endTime3 = timeSeg.getHolidayType3End3();
                    break;

                default:
                    break;
            }
            if (compareTime(eventDate, startTime1, endTime1) || compareTime(eventDate, startTime2, endTime2)
                    || compareTime(eventDate, startTime3, endTime3)) {
                isInTimeSeg = true;
            }
        }
        return isInTimeSeg;
    }

    private boolean compareTime(Date eventDate, String startTimeStr, String endTimeStr) {
        if (StringUtils.isBlank(startTimeStr) || StringUtils.isBlank(endTimeStr)
                || ("00:00".equals(startTimeStr) && "00:00".equals(endTimeStr))) {
            return false;
        }
        int startTimeHour = Integer.parseInt(startTimeStr.split(":")[0]);
        int startTimeMinute = Integer.parseInt(startTimeStr.split(":")[1]);
        LocalTime startTime = LocalTime.of(startTimeHour, startTimeMinute, 0);

        int endTimeHour = Integer.parseInt(endTimeStr.split(":")[0]);
        int endTimeMinute = Integer.parseInt(endTimeStr.split(":")[1]);
        LocalTime endTime = LocalTime.of(endTimeHour, endTimeMinute, 59);

        LocalTime eventTime = LocalTime.of(eventDate.getHours(), eventDate.getMinutes(), eventDate.getSeconds());

        return !eventTime.isBefore(startTime) && !eventTime.isAfter(endTime);
    }

    /**
     * 取出目前最大的BId
     * 
     * <AUTHOR>
     * @Date 2020/4/23 15:25
     * @param
     * @return
     */
    private Long getBId() {
        List<AccTimeSegBId> accTimeSegBIds = accTimeSegBIdDao.findAll();
        if (!CollectionUtil.isEmpty(accTimeSegBIds)) {
            return accTimeSegBIds.get(accTimeSegBIds.size() - 1).getId();
        } else {
            return 1L;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerTransfer(List<AccTimeSegItem> accTimeSegItems) {
        // 以防大数据时进行分批数据 -名称会存在冲突,先根据名称查询出来，然后去掉
        List<List<AccTimeSegItem>> accTimeSegItemList = CollectionUtil.split(accTimeSegItems, CollectionUtil.splitSize);
        for (List<AccTimeSegItem> accTimeSegs : accTimeSegItemList) {
            // 根据名称获取时间段对象集合 名称去重
            Collection<String> names = CollectionUtil.getPropertyList(accTimeSegs, AccTimeSegItem::getName, "-1");
            List<AccTimeSeg> accTimeSegList = accTimeSegDao.findByNameIn(names);
            Map<String, AccTimeSeg> accTimeSegMap = CollectionUtil.listToKeyMap(accTimeSegList, AccTimeSeg::getName);
            for (AccTimeSegItem accTimeSegItem : accTimeSegItems) {
                AccTimeSeg accTimeSeg = accTimeSegMap.remove(accTimeSegItem.getName());
                if (Objects.isNull(accTimeSeg)) {
                    accTimeSeg = new AccTimeSeg();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(accTimeSegItem, accTimeSeg, "id");
                // 通过id获取时间段对象 不为空的则set id进行更新操作
                AccTimeSeg accTimeSegNew = accTimeSegDao.findByBusinessId(Long.valueOf(accTimeSegItem.getId()));
                if (Objects.nonNull(accTimeSegNew)) {
                    ModelUtil.copyPropertiesIgnoreNullWithProperties(accTimeSegNew, accTimeSeg, "id");
                    accTimeSegDao.save(accTimeSegNew);
                } else {
                    accTimeSeg.setBusinessId(Long.valueOf(accTimeSegItem.getId()));
                    // 修复自增id重复的问题 取最大的businessId add by hql 20200423
                    // 如果小于目前的accTimeSegBId 则没有影响 大于则需要设置自增序列号
                    long bid = getBId();
                    if (Long.parseLong(accTimeSegItem.getId()) > bid) {
                        while (bid < Long.parseLong(accTimeSegItem.getId())) {
                            createBId();
                            bid++;
                        }
                    }
                    accTimeSegDao.save(accTimeSeg);
                }
            }
        }
    }

    @Override
    public Long getBusinessIdByTimeSegId(String timeSegId) {
        return accTimeSegDao.getBusinessIdByTimeSegId(timeSegId);
    }

    @Override
    public String getInitTimeSegId() {
        return Optional.ofNullable(getInitTimeSeg()).map(AccTimeSegItem::getId).orElse(null);
    }

    @Override
    public AccTimeSegItem getItemByName(String name) {
        return Optional.ofNullable(name).filter(StringUtils::isNotBlank).map(accTimeSegDao::findByName).map(timeSeg -> {
                AccTimeSegItem accTimeSegItem = ModelUtil.copyPropertiesIgnoreNull(timeSeg, new AccTimeSegItem());
                accTimeSegItem.setId(timeSeg.getId());
                accTimeSegItem.setRemark(timeSeg.getRemark());
                accTimeSegItem.setName(timeSeg.getName());
                return accTimeSegItem;
            }).orElse(null);
    }
}
