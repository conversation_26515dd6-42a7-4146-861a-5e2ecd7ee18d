/**
 * File Name: AccFirstOpen Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.model.AccFirstOpen;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;

/**
 * 对应百傲瑞达 AccFirstOpenDao
 * 
 * <AUTHOR>
 * @date: 2018-03-14 下午03:02
 * @version v1.0
 */
public interface AccFirstOpenDao extends BaseDao<AccFirstOpen, String> {
    List<AccFirstOpen> findByAccDoor_IdIn(List<String> doorIdList);

    int countByTimeSegId(String timeSegId);
}