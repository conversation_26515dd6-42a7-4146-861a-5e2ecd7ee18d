/**
 * File Name: AccLinkage
 * Created by GenerationTools on 2018-03-16 下午04:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.acc.dao;

import com.zkteco.zkbiosecurity.acc.model.AccLinkage;
import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/**
 * 对应百傲瑞达 AccLinkageDao
 * <AUTHOR>
 * @date:	2018-03-16 下午04:41
 * @version v1.0
 */
public interface AccLinkageDao extends BaseDao<AccLinkage, String> {

    AccLinkage findByName(String name);

    @Query(value = "select distinct t.TRIGGER_COND, a.INPUT_ID, a.INPUT_TYPE, e.NAME from ACC_LINKAGE_TRIGGER t " +
            "left join ACC_LINKAGE l on t.LINKAGE_ID=l.ID " +
            "left join ACC_LINKAGE_INOUT a on t.LINKAGE_INOUT_ID=a.ID " +
            "left join ACC_DEVICE_EVENT e on t.TRIGGER_COND=e.EVENT_NO and e.DEV_ID=l.DEV_ID where l.DEV_ID=?1", nativeQuery = true)
    List<Object[]> getLinkageInfoByDevId(String devId);


    @Query(value = "select distinct t.TRIGGER_COND, a.INPUT_ID, a.INPUT_TYPE, e.NAME from ACC_LINKAGE_TRIGGER t " +
            "left join ACC_LINKAGE l on t.LINKAGE_ID=l.ID " +
            "left join ACC_LINKAGE_INOUT a on t.LINKAGE_INOUT_ID=a.ID " +
            "left join ACC_DEVICE_EVENT e on t.TRIGGER_COND=e.EVENT_NO and e.DEV_ID=l.DEV_ID where l.DEV_ID=?1 and l.ID!=?2", nativeQuery = true)
    List<Object[]> getLinkageInfoByDevId(String devId, String linkId);

    /**
     * 获取联动触发的条件
     * @author: mingfa.zheng
     * @date: 2018/4/26 8:49
     * @return:
     */
    @Query(value = "select distinct e.NAME from ACC_DEVICE_EVENT e " +
            "left join ACC_LINKAGE l on e.DEV_ID=l.DEV_ID " +
            "left join ACC_LINKAGE_TRIGGER t on l.ID=t.LINKAGE_ID " +
            "where l.ID=?1 and e.EVENT_NO=t.TRIGGER_COND",nativeQuery = true)
    List<String> getTriggerCondByLinkId(String linkageId);

    @Query(value="SELECT DISTINCT e.name AS event_name, o.output_type AS output_type, o.output_id AS output_id, o.action_type AS action_type, l.name AS linkage_name FROM acc_device_event e "
                +"LEFT JOIN acc_linkage l ON e.dev_id=l.dev_id "
                +"LEFT JOIN acc_linkage_trigger t ON l.id=t.linkage_id "
                +"LEFT JOIN acc_linkage_inout o ON t.linkage_inout_id=o.id "
                +"WHERE t.linkage_index = ?1 AND e.event_no=t.trigger_cond AND l.dev_id = ?2", nativeQuery = true)
    List<Object[]> getLinkageInfoByParams(int linkIndex, String devId);

    /**
     * @Description:    根据名称进行in查询
     * @Author:         Abel.huang
     * @CreateDate:     2018/12/13 11:26
     * @Version:        1.0
     */
    List<AccLinkage> findByNameIn(Collection<String> names);

    /**
     * 根据设备序列号查询
     *
     * @param devSn: 设备序列号
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.model.AccLinkage>
     * <AUTHOR>
     * @date 2022-07-19 17:53
     * @since 1.0.0
     */
    List<AccLinkage> findByAccDevice_Sn(String devSn);
}