package com.zkteco.zkbiosecurity.acc.data.upgrade;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/7/11 14:06
 * @since 1.0.0
 */
@Slf4j
@Component
public class AccVer3_1_2 implements UpgradeVersionManager {

    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccCacheManager accCacheManager;

    @Override
    public String getVersion() {
        return "v3.1.2";
    }

    @Override
    public String getModule() {
        return BaseConstants.ACC;
    }

    @Override
    public boolean executeUpgrade() {
        // 删除缓存中设备信息
        delDeviceInfo();
        return true;
    }

    /**
     * 删除缓存中设备信息，等获取时查询为空再次组装放入；解决先前缓存中设备的门、读头等信息为空
     *
     * @return void
     * <AUTHOR>
     * @date 2022-07-11 14:11
     * @since 1.0.0
     */
    private void delDeviceInfo() {
        List<AccDeviceItem> accDeviceItemList = accDeviceService.getAllDeviceItems();
        if (!accDeviceItemList.isEmpty()) {
            for (AccDeviceItem item : accDeviceItemList) {
                accCacheManager.delDeviceInfo(item.getSn());
            }
        }
    }
}
