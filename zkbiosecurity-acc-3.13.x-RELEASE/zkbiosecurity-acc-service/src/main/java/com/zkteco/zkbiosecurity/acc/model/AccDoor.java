/**
 * File Name: AccDoor Created by GenerationTools on 2018-03-03 上午11:59 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.acc.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁门model
 *
 * <AUTHOR>
 * @date: 2018-04-24 上午11:59
 * @version v1.0
 */
@Entity
@Table(name = "ACC_DOOR", indexes = {@Index(columnList = "DEV_ID")})
@Getter
@Setter
@Accessors(chain = true)
public class AccDoor extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /** 门编号 */
    @Column(name = "DOOR_NO", nullable = false)
    private Short doorNo;

    /** 门名称 */
    @Column(name = "NAME", length = 100, nullable = false)
    private String name;

    /** 所属设备 */
    @ManyToOne
    @JoinColumn(name = "DEV_ID")
    private AccDevice device;

    /** 门有效时间段。 */
    @Column(name = "ACTIVE_TIMESEG_ID")
    private String activeTimeSegId;

    /** 门常开时间段 */
    @Column(name = "PASSMODE_TIMESEG_ID")
    private String passModeTimeSegId;

    /** 锁驱动时长 */
    @Column(name = "LOCK_DELAY", nullable = false)
    private Short lockDelay;

    /** 动作间隔：刷卡等 */
    @Column(name = "ACTION_INTERVAL", nullable = false)
    private Short actionInterval;

    /** 门磁状态 */
    @Column(name = "DOOR_SENSOR_STATUS", nullable = false)
    private Short doorSensorStatus;

    /** 门磁延时 */
    @Column(name = "SENSOR_DELAY")
    private Short sensorDelay;

    /** 闭门回锁 */
    @Column(name = "BACK_LOCK", nullable = false)
    private Boolean backLock;

    /** 验证方式 */
    @Column(name = "VERIFY_MODE", nullable = false)
    private Short verifyMode;

    /** 胁迫密码 */
    @Convert(converter = EncryptConverter.class)
    @Column(name = "FORCE_PWD")
    private String forcePwd;

    /** 紧急密码 */
    @Convert(converter = EncryptConverter.class)
    @Column(name = "SUPPER_PWD")
    private String supperPwd;

    /** 入反潜时长 */
    @Column(name = "IN_APB_DURATION")
    private Short inApbDuration;

    /** 出门按钮状态 0:锁定 1：不锁定 */
    @Column(name = "LATCH_DOOR_TYPE")
    private Short latchDoorType;

    /** 报警延时 */
    @Column(name = "LATCH_TIME_OUT")
    private Short latchTimeOut;

    /** 出门按钮时间段 */
    @Column(name = "LATCH_TIMESEG_ID")
    private String latchTimeSegId;

    /** 读头类型 */
    @Column(name = "READER_TYPE")
    private Short readerType;

    /** 开门延时 */
    @Column(name = "DELAY_OPEN_TIME")
    private Short delayOpenTime;

    /** 针对残疾人，特定的开门时间 */
    @Column(name = "EXT_DELAY_DRIVERTIME")
    private Short extDelayDrivertime;

    /** 是否禁用门的声音 */
    @Column(name = "IS_DISABLE_AUDIO", nullable = true)
    private Boolean isDisableAudio;

    /** 门是否禁用 */
    @Column(name = "ENABLED", nullable = true)
    private Boolean enabled;

    /** 韦根输入格式 */
    @Column(name = "WG_INPUT_ID")
    private String wgInputFmtId;

    /** 韦根输入类型：0工号，1卡号 */
    @Column(name = "WG_INPUT_TYPE")
    private Short wgInputType;

    /** 韦根输出格式 */
    @Column(name = "WG_OUTPUT_ID")
    private String wgOutputFmtId;

    /** 韦根输出类型：0工号，1卡号 */
    @Column(name = "WG_OUTPUT_TYPE")
    private Short wgOutputType;

    /** 主机出入状态：1 入，0 出 */
    @Column(name = "HOST_STATUS")
    private Short hostStatus;

    /** 从机出入状态：1 入，0 出 */
    @Column(name = "SLAVE_STATUS")
    private Short slaveStatus;

    /** 多人开门操作间隔 */
    @Column(name = "COMBOPEN_INTERVAL")
    private Short combOpenInterval;

    /** 是否允许卡号位反转 */
    @Column(name = "WG_REVERSED")
    private Short wgReversed;

    /** 所属扩展板id */
    @Column(name = "EXT_DEV_ID")
    private String extDevId;

    /** 是否允许超级用户在锁门的时候通行 */
    @Column(name = "ALLOW_SUACCESS_LOCK")
    private String allowSUAccessLock;

    /**  */
    @OneToMany(mappedBy = "accDoor", cascade = CascadeType.REMOVE)
    private List<AccLevelDoor> accLevelList = new ArrayList<>();

    @OneToMany(mappedBy = "accDoor", cascade = CascadeType.REMOVE)
    private List<AccDoorVerifyModeRule> accDoorVerifyModeRuleList = new ArrayList<>();

    @OneToMany(mappedBy = "accDoor", cascade = CascadeType.REMOVE)
    private List<AccReader> accReaderList = new ArrayList<>();

    @OneToMany(mappedBy = "accDoor", cascade = CascadeType.REMOVE)
    private List<AccCombOpenDoor> accCombOpenDoorList = new ArrayList<>();

    @OneToMany(mappedBy = "accDoor")
    private List<AccFirstOpen> accFirstOpenList = new ArrayList<>();

    // pro
    /** 门磁监控输入模式 mqtt */
    @Column(name = "SEN_INPUT_MODE")
    private String senInputMode;

    /** 门磁监控电阻值 mqtt */
    @Column(name = "SEN_SUPERVISED_RESISTOR")
    private String senSupervisedResistor;

    /** 门磁监控输入模式 mqtt */
    @Column(name = "SEX_INPUT_MODE")
    private String rexInputMode;

    /** 门磁监控电阻值 mqtt */
    @Column(name = "SEX_SUPERVISED_RESISTOR")
    private String rexSupervisedResistor;

    /** 出门开关类型 mqtt */
    @Column(name = "REX_BUTTON_TYPE")
    private String rexButtonType;

    /** 开门密码 */
    @Convert(converter = EncryptConverter.class)
    @Column(name = "DOOR_PWD")
    private String doorPwd;
}