package com.zkteco.zkbiosecurity.acc.service.impl;

import java.util.Map;

import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.core.utils.ImgEncodeUtil;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.security.SecurityService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.cache.AccCacheManager;
import com.zkteco.zkbiosecurity.acc.constants.AccCacheKeyConstants;
import com.zkteco.zkbiosecurity.acc.service.AccParamService;
import com.zkteco.zkbiosecurity.acc.task.AccFetchTransactionTask;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

@Service
@Transactional
public class AccParamServiceImpl implements AccParamService {
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AccFetchTransactionTask accFetchTransactionTask;
    @Autowired
    private AccCacheManager accCacheManager;
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private SecurityService securityService;

    @Override
    public void saveItem(Map<String, String> params) {
        String receiver = params.get("acc.receiver");
        if (receiver != null && !"".equals(receiver.trim())) {
            accCacheManager.set(AccCacheKeyConstants.ACC_ALARM_RECEIVER, receiver);
            params.put("acc.receiver", FoldexUtil.encryptByRandomSey(receiver));
        } else {
            accCacheManager.delete(AccCacheKeyConstants.ACC_ALARM_RECEIVER);
        }
        String smsReceiver = params.get("acc.smsReceiver");
        if (smsReceiver != null && !"".equals(smsReceiver.trim())) {
            accCacheManager.set(AccCacheKeyConstants.ACC_ALARM_SMSRECEIVER, smsReceiver);
            params.put("acc.smsReceiver", FoldexUtil.encryptByRandomSey(smsReceiver));
        } else {
            accCacheManager.delete(AccCacheKeyConstants.ACC_ALARM_SMSRECEIVER);
        }
        String autoExportEmail = params.get("acc.autoExportEmail");
        if (autoExportEmail != null && !"".equals(autoExportEmail.trim())) {
            params.put("acc.autoExportEmail", FoldexUtil.encryptByRandomSey(autoExportEmail));
        }
        baseSysParamService.saveParams(params);
        if (StringUtils.isNotBlank(params.get("acc.downNewLogExpression"))) {
            accFetchTransactionTask.initFetchTransaction();
        }
    }

    @Override
    public Map<String, String> getPersParams() {
        return baseSysParamService.getParamsByModule("pers");
    }

    @Override
    public String getParamValByName(String paramName) {
        return baseSysParamService.getValByName(paramName);
    }

    @Override
    public Map<String, String> getAccParams() {
        return baseSysParamService.getParamsByModule("acc");
    }

    @Override
    public Map<String, String> getVidParams() {
        return baseSysParamService.getParamsByModule("vid");
    }

    @Override
    public String getAvatarBase64ByPath(String photoPath) {
        return persParamsService.getDecryptBase64ByPhotoPathAndEncrypt(photoPath, "pers.headPortrait.encryptProp");
    }

    @Override
    public String getDecryptBase64ByCapturePhotoPath(String sessionId, String capturePhotoPath) {
        String photoBase64 = FileEncryptUtil.getDecryptFileBase64(capturePhotoPath);
        boolean enabled = securityService.checkPermissionExceptSupperUser(sessionId, "acc:capturePhoto:encryptProp");
        if (StringUtils.isNotBlank(photoBase64) && enabled) {
            photoBase64 = ImgEncodeUtil.base64BoxBlurFilter(photoBase64);
        }
        return photoBase64;
    }
}
