package com.zkteco.zkbiosecurity.acc.service.impl;

import com.zkteco.zkbiosecurity.acc.service.AccAuxOutService;
import com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.ivideo.service.IVideoGetAccAuxOutService;
import com.zkteco.zkbiosecurity.ivideo.vo.IVideoGetAccAuxOutItem;
import com.zkteco.zkbiosecurity.ivideo.vo.IVideoGetAccAuxOutSelectItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@Transactional
public class IVideoGetAccAuxOutServiceImpl implements IVideoGetAccAuxOutService {
    @Autowired
    private AccAuxOutService accAuxOutService;
    @Override
    public Pager getItemByAuthFilter(String sessionId, IVideoGetAccAuxOutSelectItem iVideoGetAccAuxOutSelectItem, int page, int size) {
        AccAuxOutItem accAuxOutItem = new AccAuxOutItem();
        accAuxOutItem.setName(iVideoGetAccAuxOutSelectItem.getName());
        accAuxOutItem.setDevSn(iVideoGetAccAuxOutSelectItem.getDevSn());
        accAuxOutItem.setInId(iVideoGetAccAuxOutSelectItem.getInId());
        accAuxOutItem.setNotInId(iVideoGetAccAuxOutSelectItem.getNotInId());
        accAuxOutItem.setSortName(iVideoGetAccAuxOutSelectItem.getSortName());
        Pager pager = accAuxOutService.loadPagerByAuthFilter(sessionId, accAuxOutItem, page, size);
        List<AccAuxOutItem> accAuxOutItemList = (List<AccAuxOutItem>) pager.getData();
        List<IVideoGetAccAuxOutSelectItem> items = new ArrayList<IVideoGetAccAuxOutSelectItem>();
        items = ModelUtil.copyListProperties(accAuxOutItemList, IVideoGetAccAuxOutSelectItem.class);
        pager.setData(items);
        return pager;
    }

    @Override
    public List<IVideoGetAccAuxOutItem> getAccAuxOutByIdsIn(String auxOutIds) {
        List<AccAuxOutItem> accAuxOutItems = accAuxOutService.getItemsByIds(StrUtil.strToList(auxOutIds));
        List<IVideoGetAccAuxOutItem> items = new ArrayList<IVideoGetAccAuxOutItem>();
        items = ModelUtil.copyListProperties(accAuxOutItems, IVideoGetAccAuxOutItem.class);
        return items;
    }
}
