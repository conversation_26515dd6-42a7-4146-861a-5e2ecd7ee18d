<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zkteco</groupId>
        <artifactId>zkbiosecurity-acc</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>zkbiosecurity-acc-service</artifactId>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>
    <version>${project.parent.version}</version>
    <dependencies>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-core</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-redis</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-acc-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-acc-i18n</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!-- 其他模块引用 -->
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-scheduler</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-auth-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-system-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-license-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-pers-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-adms-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-cmd</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-auth-provider</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-module-all</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-messaging</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-foldex</artifactId>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-guard-encrypt</artifactId>
            </plugin>
        </plugins>
    </build>
</project>