package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.acc.vo.Acc4PersDeviceSelectItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accSelectDevice.do")
public interface AccSelectDeviceRemote {

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccSelectDeviceItem codition);

    /**
     * 卡格式测试：选择设备双列表
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectDeviceList")
    @ResponseBody
    DxGrid selectDeviceList(Acc4PersDeviceSelectItem condition);

    /**
     * 获取设备下拉列表数据
     * 
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2023-06-01 13:54
     * @since 1.0.0
     */
    @RequestMapping(params = "getDeviceList")
    @ResponseBody
    ZKResultMsg getDeviceList();
}
