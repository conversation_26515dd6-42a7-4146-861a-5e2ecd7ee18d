/**
 * File Name: AccTimeSeg Created by GenerationTools on 2018-02-24 上午10:18 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccTimeSegItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accTimeSeg.do")
public interface AccTimeSegRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午10:18
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午10:18
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午10:18
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccTimeSegItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午10:18
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccTimeSegItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午10:18
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(String ids);

    /**
     * 重复性校验
     * 
     * @param name
     * @return
     */
    @RequestMapping(params = "validName")
    @ResponseBody
    String validName(String name);

    @RequestMapping(params = "getTimeSegList")
    @ResponseBody
    ZKResultMsg getTimeSegList();

    /**
     * @Description: 校验时间段各参数信息
     * <AUTHOR>
     * @date 2018/5/25 14:26
     * @param accTimeSegItem
     * @return
     */
    @RequestMapping(params = "dataValid")
    @ResponseBody
    ZKResultMsg dataValid(AccTimeSegItem accTimeSegItem);
}