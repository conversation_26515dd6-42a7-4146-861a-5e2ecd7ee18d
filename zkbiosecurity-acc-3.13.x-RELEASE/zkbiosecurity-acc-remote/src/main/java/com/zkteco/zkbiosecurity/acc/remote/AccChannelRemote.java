package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.acc.vo.AccChannelSelectItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accChannel.do")
public interface AccChannelRemote {

    /**
     * 列表数据获取
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccChannelSelectItem condition);

    /**
     * 判断视频模块是否存在摄像头
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 14:38
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "isExistVidChannel")
    @ResponseBody
    ZKResultMsg isExistVidChannel();
}
