/**
 * File Name: AccFirstOpen Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenItem;
import com.zkteco.zkbiosecurity.acc.vo.AccFirstOpenSelectDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonFirstOpenItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accFirstOpen.do")
public interface AccFirstOpenRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccFirstOpenItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccFirstOpenItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 首人常开: 添加人员
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/21 16:37
     * @return:
     */
    @RequestMapping(params = "addPerson")
    @ResponseBody
    ZKResultMsg addPerson(@RequestParam(value = "firstOpenId") String firstOpenId,
        @RequestParam(value = "personIds") String personIds, @RequestParam(value = "deptIds") String deptIds);

    /**
     * 首人常开: 删除人员
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/22 15:22
     * @return:
     */
    @RequestMapping(params = "delPerson")
    @ResponseBody
    ZKResultMsg delPerson(@RequestParam(value = "firstOpenId") String firstOpenId,
        @RequestParam(value = "personIds") String personIds);

    /**
     * 首人常开：右列表的人员信息
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/12 10:42
     * @return:
     */
    @RequestMapping(params = "personList")
    @ResponseBody
    DxGrid personList(AccPersonFirstOpenItem codition);

    /**
     * 首人常开：选门控件
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/12 10:42
     * @return:
     */
    @RequestMapping(params = "selectDoorlist")
    @ResponseBody
    DxGrid selectDoorlist(AccFirstOpenSelectDoorItem codition);
}