/**
 * File Name: AccDSTime Created by GenerationTools on 2018-02-28 下午02:21 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccDSTimeItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accDSTime.do")
public interface AccDSTimeRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-28 下午02:21
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-28 下午02:21
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-02-28 下午02:21
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccDSTimeItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-28 下午02:21
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccDSTimeItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-02-28 下午02:21
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 获取夏令时集合（下拉框显示）
     * <AUTHOR>
     * @date 2018/5/18 11:24
     * @return
     */
    @RequestMapping(params = "getDSTimeList")
    @ResponseBody
    ZKResultMsg getDSTimeList();

    /**
     * @Description: 判断夏令时名称是否重复
     * <AUTHOR>
     * @date 2018/5/18 17:02
     * @param name 夏令时名称
     * @return
     */
    @RequestMapping(params = "isExistName")
    @ResponseBody
    boolean isExistName(@RequestParam(value = "name") String name);

    /**
     * @Description: 设置夏令时页面跳转
     * <AUTHOR>
     * @date 2018/7/6 12:01
     * @param id
     * @return
     */
    @RequestMapping(params = "getById")
    @ResponseBody
    ModelAndView getById(@RequestParam(value = "id") String id);

    /**
     * @Description: 设置设备夏令时
     * <AUTHOR>
     * @date 2018/7/6 15:31
     * @param dSTimeId
     * @param devIds
     * @return
     */
    @RequestMapping(params = "setDSTime")
    @ResponseBody
    ZKResultMsg setDSTime(@RequestParam(value = "dSTimeId") String dSTimeId,
        @RequestParam(value = "devIds") String devIds);

    /**
     * 根据时区查找对应的夏令时列表
     * 
     * @param timeZone
     * @return
     */
    @RequestMapping(params = "getDSTimeListByTimeZone")
    @ResponseBody
    ZKResultMsg getDSTimeListByTimeZone(String timeZone);

    /**
     * 查找时区是否有夏令时
     * 
     * @param timeZone
     * @return
     */
    @RequestMapping(params = "hasDSTimeByTimeZone")
    @ResponseBody
    ZKResultMsg hasDSTimeByTimeZone(String timeZone);

    /**
     * 获取选择夏令时的弹窗
     * 
     * @param timeZone
     * @param dsTime
     * @return
     */
    @RequestMapping(params = "getDSTimeSelectView")
    ModelAndView getDSTimeSelectView(String timeZone, String dsTime);
}