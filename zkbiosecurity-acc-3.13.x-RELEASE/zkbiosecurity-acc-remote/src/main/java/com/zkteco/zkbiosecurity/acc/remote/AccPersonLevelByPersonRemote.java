/**
 * File Name: AccPersonLevelByPerson Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonSelectLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonShowLevelItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accPersonLevelByPerson.do")
public interface AccPersonLevelByPersonRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccPersonLevelByPersonItem codition);

    /**
     * 获取按人员设置权限List
     * 
     * <AUTHOR>
     * @since 2018/3/14 15:03
     * @return
     */
    @RequestMapping(params = "getPersonLevel")
    @ResponseBody
    DxGrid getPersonLevel(AccPersonLevelItem codition);

    /**
     * 获取按人员设置选择权限List
     * 
     * <AUTHOR>
     * @since 2018/3/14 16:35
     * @return
     */
    @RequestMapping(params = "getPersonSelectLevel")
    @ResponseBody
    DxGrid getPersonSelectLevel(AccPersonSelectLevelItem codition);

    /**
     * 按人员设置：添加人员所属权限组
     * 
     * <AUTHOR>
     * @since 2018/3/26 10:57
     * @return
     */
    @RequestMapping(params = "addLevel")
    @ResponseBody
    ZKResultMsg addLevel(@RequestParam(value = "personId") String personId,
        @RequestParam(value = "levelIds") String levelIds);

    /**
     * 按人员设置：删除人员所属权限组
     * 
     * <AUTHOR>
     * @since 2018/3/26 10:58
     * @return
     */
    @RequestMapping(params = "delLevel")
    @ResponseBody
    ZKResultMsg delLevel(@RequestParam(value = "personId") String personId,
        @RequestParam(value = "levelIds") String levelIds);

    /**
     * @Description: 门禁设置
     * <AUTHOR>
     * @date 2018/5/14 15:30
     * @param ids 人员id
     * @return
     */
    @RequestMapping(params = "setAccParam")
    @ResponseBody
    ModelAndView setAccParam(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 按人员设置导出所属权限组
     * <AUTHOR>
     * @date 2018/5/14 15:32
     * @return
     */
    @RequestMapping(params = "export")
    @ResponseBody
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 按人员设置-根据选中的人员id导出人员所在权限组信息
     *
     * @param request:
     * @param response:
     * @return void
     * <AUTHOR>
     * @throws @date 2020-09-24 15:00
     * @since 1.0.0
     */
    @RequestMapping(params = "exportPersonLevel")
    @ResponseBody
    void exportPersonLevel(HttpServletRequest request, HttpServletResponse response);

    /**
     * 同步人员权限组信息
     *
     * @param personIds:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws @date 2021-06-11 11:33
     * @since 1.0.0
     */
    @RequestMapping(params = "syncPersonLevel")
    @ResponseBody
    ZKResultMsg syncPersonLevel(@RequestParam(value = "personIds", required = true) String personIds);

    /**
     * 获取人员权限组信息
     *
     * @param condition:
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR>
     * @throws
     * @date 2024-01-06 11:29
     * @since 1.0.0
     */
    @RequestMapping(params = "getPersonSelectLevelByPersonId")
    @ResponseBody
    DxGrid getPersonSelectLevelByPersonId(AccPersonShowLevelItem condition);
}