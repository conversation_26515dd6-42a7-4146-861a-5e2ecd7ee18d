/**
 * <AUTHOR>
 * @date 2020/3/27 11:07
 */

package com.zkteco.zkbiosecurity.acc.remote;

import java.lang.reflect.InvocationTargetException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccFirstInLastOutItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@RequestMapping(value = "/accFirstInLastOut.do")
public interface AccFirstInLastOutRemote {
    /**
     * 默认页面跳转
     *
     * @author: yibiao.shen
     * @date:2018-03-09 13:48:23
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     *
     * @author: yibiao.shen
     * @date:2018-03-09 13:48:23
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccFirstInLastOutItem condition);

    /**
     * 清空数据
     *
     * <AUTHOR>
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @date 2020/3/26 17:48
     */
    @RequestMapping(params = "clearData")
    @ResponseBody
    ZKResultMsg clearData();

    /**
     * 导出
     * 
     * <AUTHOR>
     * @param request,
     *            response
     * @return void
     * @date 2020/3/27 11:09
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response)
        throws InvocationTargetException, IllegalAccessException;
}
