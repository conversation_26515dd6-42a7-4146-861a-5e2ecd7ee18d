package com.zkteco.zkbiosecurity.acc.remote;

import java.lang.reflect.InvocationTargetException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccExceptionRecordItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 异常记录远程接口
 * 
 * @author: AI Assistant
 * @date: 2025-07-21
 */
@RequestMapping(value = "/accExceptionRecord.do")
public interface AccExceptionRecordRemote {

    /**
     * 默认页面跳转
     * 
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     * 
     * @param condition 查询条件
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccExceptionRecordItem condition);

    /**
     * 新增页面跳转
     * 
     * @return
     */
    @RequestMapping(params = "add")
    ModelAndView add();

    /**
     * 编辑页面跳转
     * 
     * @param id 记录ID
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id") String id);

    /**
     * 保存异常记录
     * 
     * @param item 异常记录项
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccExceptionRecordItem item);

    /**
     * 删除异常记录
     * 
     * @param ids ID列表，逗号分隔
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 清除所有异常记录
     * 
     * @return
     */
    @RequestMapping(params = "clearData")
    @ResponseBody
    ZKResultMsg clearData();

    /**
     * 导出异常记录
     * 
     * @param request HTTP请求
     * @param response HTTP响应
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response)
        throws InvocationTargetException, IllegalAccessException;

    /**
     * 发送异常记录通知
     * 
     * @param ids ID列表，逗号分隔
     * @return
     */
    @RequestMapping(params = "sendNotification")
    @ResponseBody
    ZKResultMsg sendNotification(@RequestParam(value = "ids") String ids);

    /**
     * 重新发送失败的异常记录
     * 
     * @param ids ID列表，逗号分隔
     * @return
     */
    @RequestMapping(params = "resendFailed")
    @ResponseBody
    ZKResultMsg resendFailed(@RequestParam(value = "ids") String ids);

    /**
     * 更新异常状态
     * 
     * @param id 记录ID
     * @param exceptionStatus 异常状态
     * @return
     */
    @RequestMapping(params = "updateStatus")
    @ResponseBody
    ZKResultMsg updateStatus(@RequestParam(value = "id") String id, 
                           @RequestParam(value = "exceptionStatus") String exceptionStatus);

    /**
     * 获取异常记录详情
     * 
     * @param id 记录ID
     * @return
     */
    @RequestMapping(params = "detail")
    @ResponseBody
    ZKResultMsg detail(@RequestParam(value = "id") String id);


}
