/**
 * File Name: AccAntiPassback Created by GenerationTools on 2018-03-13 上午10:27 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccAntiPassbackItem;
import com.zkteco.zkbiosecurity.acc.vo.AccAntiPassbackSelectDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccAntiPassbackSelectDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccAntiPassbackSelectReaderItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accAntiPassback.do")
public interface AccAntiPassbackRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:27
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:27
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:27
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccAntiPassbackItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:27
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccAntiPassbackItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:27
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 页面加载设备对应反潜规则下拉列表
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年6月5日 上午10:58:59
     * @return String
     */
    @RequestMapping(params = "getRule")
    @ResponseBody
    ZKResultMsg getRule(@RequestParam(value = "deviceId") String deviceId);

    /**
     * 验证设备关联的读头是否有参与全局反潜
     *
     * <AUTHOR>
     * @since 2014年12月30日 下午2:34:05
     * @return
     */
    @RequestMapping(params = "validGlobalApb")
    @ResponseBody
    ZKResultMsg validGlobalApb(@RequestParam(value = "deviceId") String deviceId);

    /**
     * 反潜：选设备控件
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/16 9:39
     * @return:
     */
    @RequestMapping(params = "selectDevicelist")
    @ResponseBody
    DxGrid selectDevicelist(AccAntiPassbackSelectDeviceItem codition);

    @RequestMapping(params = "selectReader")
    ModelAndView selectReader(String deviceId, String notInId, String group);

    @RequestMapping(params = "selectReaderList")
    @ResponseBody
    DxGrid selectReaderList(AccAntiPassbackSelectReaderItem codition);

    @RequestMapping(params = "selectDoor")
    ModelAndView selectDoor(String deviceId, String notInId, String group);

    @RequestMapping(params = "selectDoorList")
    @ResponseBody
    DxGrid selectDoorList(AccAntiPassbackSelectDoorItem codition);

    @RequestMapping(params = "validDetermineApb")
    @ResponseBody
    ZKResultMsg validDetermineApb(String devId);

    @RequestMapping(params = "validApbCount")
    @ResponseBody
    ZKResultMsg validApbCount(String deviceId);

    /**
     * 名称唯一检测
     *
     * @param name
     * @return
     */
    @RequestMapping(params = "validName")
    @ResponseBody
    boolean validName(String name);
}