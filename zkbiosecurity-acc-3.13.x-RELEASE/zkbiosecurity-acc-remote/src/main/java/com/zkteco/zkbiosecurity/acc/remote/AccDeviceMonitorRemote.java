/**
 * File Name: AccDeviceEvent Created by GenerationTools on 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accDeviceMonitor.do")
public interface AccDeviceMonitorRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午02:44
     * @return
     */
    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 清除全部命令
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午02:44
     * @param sn
     * @param alias
     * @return
     */
    @RequestMapping(params = "clearCmdCache")
    @ResponseBody
    ZKResultMsg clearCmdCache(@RequestParam(value = "sn") String sn, @RequestParam(value = "alias") String alias);

}