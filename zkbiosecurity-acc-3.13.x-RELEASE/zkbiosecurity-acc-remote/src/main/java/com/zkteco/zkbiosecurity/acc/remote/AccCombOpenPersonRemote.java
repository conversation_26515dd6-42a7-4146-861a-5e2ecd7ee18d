/**
 * File Name: Acc<PERSON><PERSON><PERSON>penPerson Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonCombOpenPersonItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accCombOpenPerson.do")
public interface AccCombOpenPersonRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccCombOpenPersonItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccCombOpenPersonItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 判断名称是否有效/允许
     * 
     * @author: mingfa.zheng
     * @date: 2018-02-23 09:51:28
     * @param name
     * @return
     */
    @RequestMapping(params = "valid")
    @ResponseBody
    String valid(@RequestParam(value = "name") String name);

    @RequestMapping(params = "getCombOpenJsonData")
    @ResponseBody
    ZKResultMsg getCombOpenJsonData();

    /*
     * add by noah
     * 验证用户人数，改成直接取到组下名的人员数量。modify by: ob.huang 2013-08-20
     * 验证用户选择对应组的开门人数是否合法
     */
    @RequestMapping(params = "checkPersonCount")
    @ResponseBody
    ZKResultMsg checkPersonCount(@RequestParam(value = "groupId") String groupId);

    /**
     * 多人开门组：添加人员
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/21 16:37
     * @return:
     */
    @RequestMapping(params = "addPerson")
    @ResponseBody
    ZKResultMsg addPerson(@RequestParam(value = "combOpenPersonId") String combOpenPersonId,
        @RequestParam(value = "personIds") String personIds, @RequestParam(value = "deptIds") String deptIds);

    /**
     * 多人开门组：删除人员
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/22 15:53
     * @return:
     */
    @RequestMapping(params = "delPerson")
    @ResponseBody
    ZKResultMsg delPerson(@RequestParam(value = "combOpenId") String combOpenId,
        @RequestParam(value = "personIds") String personIds);

    /**
     * 获取右列表人员的list数据
     * 
     * @param condition:
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR>
     * @throws
     * @date 2022-08-03 10:05
     * @since 1.0.0
     */
    @RequestMapping(params = "personList")
    @ResponseBody
    DxGrid personList(AccPersonCombOpenPersonItem condition);

    /**
     * 获取多人开门人员组下拉列表数据
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-07-21 15:46
     * @since 1.0.0
     */
    @RequestMapping(params = "getCombOpenList")
    @ResponseBody
    ZKResultMsg getCombOpenList();
}