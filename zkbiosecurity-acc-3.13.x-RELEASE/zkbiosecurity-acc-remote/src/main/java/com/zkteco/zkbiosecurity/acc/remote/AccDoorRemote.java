/**
 * File Name: AccDoor Created by GenerationTools on 2018-03-03 上午11:59 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accDoor.do")
public interface AccDoorRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-03 上午11:59
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-03 上午11:59
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id") String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-03 上午11:59
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccDoorItem item, @RequestParam(value = "applyTo") String applyTo);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-03 上午11:59
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccDoorItem codition);

    /**
     * 操作门（远程开门关门等）的跳转
     * 
     * @author: verber
     * @date: 2018-04-23 上午11:59
     * @param ids
     * @return
     */
    @RequestMapping(params = "getDoorIds")
    @ResponseBody
    ModelAndView getDoorIds(@RequestParam(value = "ids") String ids);

    /**
     * 获取支持的验证方式
     * 
     * @author: verber
     * @date: 2018-04-25 上午11:59
     * @param deviceId
     * @return
     */
    @RequestMapping(params = "getVerifyMode")
    @ResponseBody
    ZKResultMsg getVerifyMode(@RequestParam(value = "deviceId") String deviceId);

    /**
     * 获取韦根格式
     * 
     * @author: verber
     * @date: 2018-04-25 上午11:59
     * @return
     */
    @RequestMapping(params = "getWiegandFmtList")
    @ResponseBody
    ZKResultMsg getWiegandFmtList();

    /**
     * 验证设备关联的读头是否有参与全局反潜
     *
     * <AUTHOR>
     * @since 2018年4月25日 11:34:05
     * @param doorId
     * @return
     */
    @RequestMapping(params = "validGlobalApb")
    @ResponseBody
    boolean validGlobalApb(@RequestParam(value = "doorId") String doorId);

    /**
     * 判断名称是否存在
     *
     * <AUTHOR>
     * @since 2018年4月25日 11:34:05
     * @param name
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    boolean isExist(@RequestParam(value = "name") String name);

    /**
     * 判断密码是否合法
     *
     * <AUTHOR>
     * @since 2018年4月25日 11:34:05
     * @param forcePwd
     * @return
     */
    @RequestMapping(params = "checkPwd")
    @ResponseBody
    boolean checkPwd(@RequestParam(value = "forcePwd") String forcePwd);

    /**
     * 获取设备参数
     *
     * <AUTHOR>
     * @since 2018年4月25日 11:34:05
     * @param deviceId
     * @return
     */
    @RequestMapping(params = "getAccDeviceOpt")
    @ResponseBody
    ZKResultMsg getAccDeviceOpt(@RequestParam(value = "deviceId") String deviceId);

    /**
     * 启用
     *
     * <AUTHOR>
     * @since 2018年4月27日 11:34:05
     * @param ids
     * @return
     */
    @RequestMapping(params = "enable")
    @ResponseBody
    ZKResultMsg enable(@RequestParam(value = "ids") String ids);

    /**
     * 禁用
     *
     * <AUTHOR>
     * @since 2018年4月27日 11:34:05
     * @param ids
     * @return
     */
    @RequestMapping(params = "disable")
    @ResponseBody
    ZKResultMsg disable(@RequestParam(value = "ids") String ids);

    /**
     *
     * 远程开门
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:34:40
     * @param ids
     * @param openInterval
     * @return
     */
    @RequestMapping(params = "openDoor")
    @ResponseBody
    ZKResultMsg openDoor(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "openInterval") String openInterval);

    /**
     *
     * 远程关门
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:52
     * @param ids
     * @return
     */
    @RequestMapping(params = "closeDoor")
    @ResponseBody
    ZKResultMsg closeDoor(@RequestParam(value = "ids") String ids);

    /**
     *
     * 取消报警
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:19
     * @param ids
     * @return
     */
    @RequestMapping(params = "cancelAlarm")
    @ResponseBody
    ZKResultMsg cancelAlarm(@RequestParam(value = "ids") String ids);

    /**
     *
     * 远程锁定
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:34
     * @return
     */
    @RequestMapping(params = "lockDoor")
    @ResponseBody
    ZKResultMsg lockDoor(@RequestParam(value = "ids") String ids);

    /**
     *
     * 远程解锁
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:49
     * @return
     */
    @RequestMapping(params = "unLockDoor")
    @ResponseBody
    ZKResultMsg unLockDoor(@RequestParam(value = "ids") String ids);

    /**
     *
     * 远程常开
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:38:08
     * @param ids
     * @return
     */
    @RequestMapping(params = "normalOpenDoor")
    @ResponseBody
    ZKResultMsg normalOpenDoor(@RequestParam(value = "ids") String ids);

    /**
     *
     * 启用当天常开时间段
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:38:42
     * @return
     */
    @RequestMapping(params = "enableNormalOpenDoor")
    @ResponseBody
    ZKResultMsg enableNormalOpenDoor(@RequestParam(value = "ids") String ids);

    /**
     *
     * 禁用当天常开时间段
     *
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:15
     * @return
     */
    @RequestMapping(params = "disableNormalOpenDoor")
    @ResponseBody
    ZKResultMsg disableNormalOpenDoor(@RequestParam(value = "ids") String ids);
}