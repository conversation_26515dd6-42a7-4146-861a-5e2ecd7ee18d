package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.acc.vo.AccSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

@RequestMapping(value = "/accSelectDoor.do")
public interface AccSelectDoorRemote {

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccSelectDoorItem codition);
}
