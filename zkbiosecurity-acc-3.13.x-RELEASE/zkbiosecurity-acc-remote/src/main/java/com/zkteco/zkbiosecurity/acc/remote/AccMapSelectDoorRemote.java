package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.acc.vo.AccMapSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

@RequestMapping(value = "/accMapSelectDoor.do")
public interface AccMapSelectDoorRemote {

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccMapSelectDoorItem codition);
}
