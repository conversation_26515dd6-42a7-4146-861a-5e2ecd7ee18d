package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccDeviceSelectItem;
import com.zkteco.zkbiosecurity.acc.vo.AccExtDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accExtDevice.do")
public interface AccExtDeviceRemote {

    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccExtDeviceItem item);

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccExtDeviceItem codition);

    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    @RequestMapping(params = "selectDeviceList")
    @ResponseBody
    DxGrid selectDeviceList(AccDeviceSelectItem condition);

    @RequestMapping(params = "getExtBoardTypeSelect")
    @ResponseBody
    ZKResultMsg getExtBoardTypeSelect(String devId);

    @RequestMapping(params = "isExistAlias")
    @ResponseBody
    boolean isExistAlias(@RequestParam(value = "alias") String alias);

    @RequestMapping(params = "isExistAddress")
    @ResponseBody
    boolean isExistAddress(@RequestParam(value = "commAddress") Short commAddress,
        @RequestParam(value = "devId") String devId);

    @RequestMapping(params = "getDevProtocolTypeSelect")
    @ResponseBody
    ZKResultMsg getDevProtocolTypeSelect(String devId);

    @RequestMapping(params = "getExtBoardTypeSelects")
    @ResponseBody
    ZKResultMsg getExtBoardTypeSelects(String devId, String devProtocolType);
}
