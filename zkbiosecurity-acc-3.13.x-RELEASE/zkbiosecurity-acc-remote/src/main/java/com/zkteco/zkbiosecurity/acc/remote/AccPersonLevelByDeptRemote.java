/**
 * File Name: AccPersonLevelByDept Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccDeptLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeptSelectLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByDeptItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accPersonLevelByDept.do")
public interface AccPersonLevelByDeptRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccPersonLevelByDeptItem codition);

    /**
     * 获取按部门设置权限List
     * 
     * <AUTHOR>
     * @since 2018/3/14 15:03
     * @return
     */
    @RequestMapping(params = "getDeptLevel")
    @ResponseBody
    DxGrid getDeptLevel(AccDeptLevelItem codition);

    /**
     * 获取按部门设置选择权限List
     * 
     * <AUTHOR>
     * @since 2018/3/14 18:05
     * @return
     */
    @RequestMapping(params = "getDeptSelectLevel")
    @ResponseBody
    DxGrid getDeptSelectLevel(AccDeptSelectLevelItem codition);

    /**
     * 按部门设置：添加部门权限
     * 
     * <AUTHOR>
     * @since 2018/3/26 14:11
     * @return
     */
    @RequestMapping(params = "addLevel")
    @ResponseBody
    ZKResultMsg addLevel(@RequestParam(value = "deptId") String deptId,
        @RequestParam(value = "levelIds") String levelIds,
        @RequestParam(value = "immeUpdate", required = false) String immeUpdate);

    /**
     * 按部门设置：删除部门权限
     * 
     * <AUTHOR>
     * @since 2018/3/26 14:11
     * @return
     */
    @RequestMapping(params = "delLevel")
    @ResponseBody
    ZKResultMsg delLevel(@RequestParam(value = "leftIds") String leftIds,
        @RequestParam(value = "rightIds") String rightIds, @RequestParam(value = "immeUpdate") String immeUpdate);

    /**
     * 根据部门Id,获取默认权限。 for ajax 请求
     * 
     * @author: mingfa.zheng
     * @date: 2018/5/15 17:15
     * @return:
     */
    @RequestMapping(params = "getLevelListByDept")
    @ResponseBody
    ZKResultMsg getLevelListByDept(@RequestParam(value = "deptId") String deptId);
}