/**
 * File Name: AccDevice Created by GenerationTools on 2018-03-08 下午02:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accDevice.do")
public interface AccDeviceRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-08 下午02:41
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-08 下午02:41
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-08 下午02:41
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccDeviceItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-08 下午02:41
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccDeviceItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-08 下午02:41
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 导出设备excle表格
     * <AUTHOR>
     * @date 2018/5/2 8:36
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(params = "export")
    @ResponseBody
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 判断设备名称是否重复
     * 
     * <AUTHOR>
     * @since 2018/4/19 16:06
     * @return
     */
    @RequestMapping(params = "isExistByAlias")
    @ResponseBody
    boolean isExistByAlias(@RequestParam(value = "alias") String alias);

    /**
     * 获取所有设备Ip和sn
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "getAllIPSn")
    @ResponseBody
    ZKResultMsg getAllIPSn();

    /**
     * 搜索设备
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "searchDev")
    @ResponseBody
    ZKResultMsg searchDev(@RequestParam(value = "nowTime", required = false) Long nowTime);

    /**
     * 获取搜索设备信息
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "getSearchDevInfo")
    @ResponseBody
    ModelAndView getSearchDevInfo();

    /**
     * 获取设备指纹对比阈值信息
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "getDevMThresholdInfo")
    @ResponseBody
    ModelAndView getDevMThresholdInfo(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "type") String type);

    /**
     * 获取设备IP信息
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "getDevIPAddressInfo")
    @ResponseBody
    ModelAndView getDevIPAddressInfo(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "type") String type);

    /**
     * 获取设备通信密码信息
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "getDevCommPwdInfo")
    @ResponseBody
    ModelAndView getDevCommPwdInfo(@RequestParam(value = "ids") String ids, @RequestParam(value = "type") String type);

    /**
     * 获取设备网络连接信息
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "getDevNetConnectModeInfo")
    @ResponseBody
    ModelAndView getDevNetConnectModeInfo(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "type") String type);

    /**
     * 获取设备子设备信息
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "addChildDeviceInfo")
    @ResponseBody
    ModelAndView addChildDeviceInfo(@RequestParam(value = "ids") String ids, @RequestParam(value = "type") String type);

    /**
     * 获取修改主设备信息
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "modParentDeviceInfo")
    @ResponseBody
    ModelAndView modParentDeviceInfo(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "type") String type);

    /**
     * 获取配置主设备信息
     * 
     * <AUTHOR>
     * @since 2018/4/16 20:48
     * @return
     */
    @RequestMapping(params = "configParentDeviceInfo")
    @ResponseBody
    ModelAndView configParentDeviceInfo(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "type") String type);

    /**
     * 验证push许可数量
     * 
     * <AUTHOR>
     * @since 2018/4/17 11:37
     * @return
     */
    @RequestMapping(params = "validPushDevCount")
    @ResponseBody
    ZKResultMsg validPushDevCount(@RequestParam(value = "sn") String sn,
        @RequestParam(required = false, value = "machineType") String machineType);

    /**
     * 修改设备IP、修改服务器IP
     * 
     * <AUTHOR>
     * @since 2018/4/17 11:47
     * @return
     */
    @RequestMapping(params = "modifyIPAddress")
    @ResponseBody
    ZKResultMsg modifyIPAddress(AccSearchAddDeviceItem item);

    /**
     * 设置启用
     * 
     * <AUTHOR>
     * @since 2018/4/24 9:24
     * @return
     */
    @RequestMapping(params = "setDevEnable")
    @ResponseBody
    ZKResultMsg setDevEnable(@RequestParam(value = "ids") String ids);

    /**
     * 设置禁用
     * 
     * <AUTHOR>
     * @since 2018/4/24 9:25
     * @return
     */
    @RequestMapping(params = "setDevDisable")
    @ResponseBody
    ZKResultMsg setDevDisable(@RequestParam(value = "ids") String ids);

    /**
     * 同步所有数据获取设备信息
     * 
     * <AUTHOR>
     * @since 2018/4/24 9:25
     * @return
     */
    @RequestMapping(params = "getDevIdsBySyncData")
    @ResponseBody
    ModelAndView getDevIdsBySyncData(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "type") String type);

    /**
     * 更新固件获取设备ids
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2016年12月1日 下午7:26:26
     * @return
     */
    @RequestMapping(params = "getDevIdsByUpgradeFirmware")
    @ResponseBody
    ModelAndView getDevIdsByUpgradeFirmware(@RequestParam(value = "type") String type,
        @RequestParam(value = "checkOffline", required = false) String checkOffline,
        @RequestParam(value = "ids") String ids);

    /**
     * 重启设备
     * 
     * @return
     */
    @RequestMapping(params = "rebootDevice")
    @ResponseBody
    ZKResultMsg rebootDevice(@RequestParam(value = "ids") String ids);

    /**
     * 获取设备参数
     * 
     * <AUTHOR>
     * @since 2018/4/24 10:43
     * @return
     */
    @RequestMapping(params = "getOptFromDev")
    @ResponseBody
    ZKResultMsg getOptFromDev(@RequestParam(value = "ids") String ids);

    /**
     * 同步所有设备时获取设备类型并分类
     * 
     * <AUTHOR>
     * @since 2018/4/24 15:20
     * @return
     */
    @RequestMapping(params = "syncAllDataByDevType")
    @ResponseBody
    ZKResultMsg syncAllDataByDevType(@RequestParam(value = "devType") String devType,
        @RequestParam(value = "devId") String devId);

    /**
     * @Description: 同步所有数据
     * <AUTHOR>
     * @date 2018/5/3 19:09
     * @param devIds 设备ID
     * @param optBoxValue 选中同步参数
     * @return
     */
    @RequestMapping(params = "syncAllData")
    @ResponseBody
    ZKResultMsg syncAllData(@RequestParam(value = "devIds") String devIds,
        @RequestParam(value = "optBoxValue", required = false) String optBoxValue);

    /**
     * @Description: 获取设备同步时间信息
     * <AUTHOR>
     * @date 2018/5/3 19:09
     * @param ids
     * @param type
     * @param checkOffline
     * @return
     */
    @RequestMapping(params = "getDevSyncTimeInfo")
    @ResponseBody
    ModelAndView getDevSyncTimeInfo(@RequestParam(value = "ids") String ids, @RequestParam(value = "type") String type,
        @RequestParam(value = "checkOffline", required = false) String checkOffline);

    /**
     * 获取设备人员信息
     *
     * @param ids
     * @return
     */
    @RequestMapping(params = "getUploadPersonInfo")
    @ResponseBody
    ModelAndView getUploadPersonInfo(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 获取设备上传事件记录信息
     * <AUTHOR>
     * @date 2018/5/3 19:09
     * @param ids
     * @param type
     * @param checkOffline
     * @return
     */
    @RequestMapping(params = "getUploadTransactionInfo")
    @ResponseBody
    ModelAndView getUploadTransactionInfo(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "type") String type,
        @RequestParam(value = "checkOffline", required = false) String checkOffline);

    /**
     * 根据id获取设备后台验证信息
     * 
     * <AUTHOR>
     * @since 2018/4/25 10:45
     * @return
     */
    @RequestMapping(params = "getDevIdsByIssueBgValid")
    @ResponseBody
    ModelAndView getDevIdsByIssueBgValid(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 设置后台验证参数
     * <AUTHOR>
     * @date 2018/4/25 14:18
     * @param devIds 设备Ids
     * @param verifyParam 是否启用后台验证
     * @param offlineRule 设备离线规则
     * @return
     */
    @RequestMapping(params = "issueBGVerifyParam")
    @ResponseBody
    ZKResultMsg issueBGVerifyParam(@RequestParam(value = "devIds") String devIds,
        @RequestParam(value = "verifyParam") String verifyParam,
        @RequestParam(value = "offlineRule") String offlineRule);

    /**
     * @Description: 检查是否开启多人验证
     * <AUTHOR>
     * @date 2018/4/25 14:20
     * @param devIds 设备ID
     * @return
     */
    @RequestMapping(params = "checkCombOpenDoor")
    @ResponseBody
    ZKResultMsg checkCombOpenDoor(@RequestParam(value = "devIds") String devIds);

    /**
     * @Description: 判断设备是否在线
     * <AUTHOR>
     * @date 2018/4/25 15:12
     * @param deviceId 设备ID
     * @return
     */
    @RequestMapping(params = "isOnline")
    @ResponseBody
    ZKResultMsg isOnline(@RequestParam(value = "deviceId") String deviceId);

    /**
     * @Description: 判断IP地址是否存在
     * <AUTHOR>
     * @date 2018/4/28 8:49
     * @param ipAddress ip地址
     * @return
     */
    @RequestMapping(params = "isExistIpAddress")
    @ResponseBody
    boolean isExistIpAddress(@RequestParam(value = "ipAddress") String ipAddress);

    /**
     * @Description: 判断设备名称是否存在
     * <AUTHOR>
     * @date 2018/4/28 8:49
     * @param alias 设备名称
     * @return
     */
    @RequestMapping(params = "isExistAlias")
    @ResponseBody
    boolean isExistAlias(@RequestParam(value = "alias") String alias);

    /**
     * @Description: 检查授权密码
     * <AUTHOR>
     * @date 2018/4/28 8:55
     * @param operationPwd 授权密码
     * @return
     */
    @RequestMapping(params = "checkOperPwd")
    @ResponseBody
    boolean checkOperPwd(@RequestParam(value = "operationPwd") String operationPwd);

    /**
     * @Description: 设备升级固件
     * <AUTHOR>
     * @date 2018/4/28 17:50
     * @param devIds 设备id
     * @param devFile 升级文件
     * @param upgradeType 升级类型
     * @return
     */
    @RequestMapping(params = "upgradeFirmware")
    @ResponseBody
    ZKResultMsg upgradeFirmware(@RequestParam(value = "devIds") String devIds,
        @RequestParam(value = "devFile") MultipartFile devFile, @RequestParam(value = "upgradeType") String upgradeType,
        @RequestParam(value = "devRadio") String devRadio);

    /**
     * @Description: 获取人员信息
     * <AUTHOR>
     * @date 2018/4/28 17:58
     * @param devIds 设备ID
     * @param dataType 数据类型（人员信息 or 指纹信息）
     * @param tempClientId 客户端ID
     * @return
     */
    @RequestMapping(params = "uploadPersonInfo")
    @ResponseBody
    ZKResultMsg uploadPersonInfo(@RequestParam(value = "devIds") String devIds,
        @RequestParam(value = "dataType") String dataType, @RequestParam(value = "tempClientId") String tempClientId);

    /**
     * @Description: 获取事件记录
     * <AUTHOR>
     * @date 2018/5/2 11:30
     * @return
     */
    @RequestMapping(params = "uploadTransaction")
    @ResponseBody
    ZKResultMsg uploadTransaction(AccDeviceUploadTransactionItem item);

    /**
     * @Description: 同步设备时间
     * <AUTHOR>
     * @date 2018/5/2 16:03
     * @param devIds 设备Id
     * @return
     */
    @RequestMapping(params = "syncTime")
    @ResponseBody
    ZKResultMsg syncTime(@RequestParam(value = "devIds") String devIds);

    /**
     * @Description: 判断是否存在设备IP
     * <AUTHOR>
     * @date 2018/5/3 11:56
     * @param id 设备ID
     * @param ipAddress 设备ip地址
     * @return
     */
    @RequestMapping(params = "isExistsIp")
    @ResponseBody
    ZKResultMsg isExistsIp(@RequestParam(value = "id") String id, @RequestParam(value = "ipAddress") String ipAddress);

    /**
     * @Description: 判断设备第二网卡IP是否重复
     * <AUTHOR>
     * @date 2018/5/3 14:01
     * @param id 设备id
     * @param newIp 新IP地址
     * @return
     */
    @RequestMapping(params = "isExistsSecondIp")
    @ResponseBody
    ZKResultMsg isExistsSecondIp(@RequestParam(value = "id") String id, @RequestParam(value = "newIp") String newIp);

    /**
     * @Description: 修改设备ip
     * <AUTHOR>
     * @date 2018/5/3 14:42
     * @param devId 设备Id
     * @param ipAddress 设备主网卡Ip
     * @param subnetMask 设备主网卡子网掩码
     * @param gateway 设备主网卡网关
     * @param ipAddressSec 设备第二网卡Ip
     * @param netMaskSec 设备第二网卡子网掩码
     * @param gateIPAddressSec 设备第二网卡网关
     * @return
     */
    @RequestMapping(params = "updateIpAddr")
    @ResponseBody
    ZKResultMsg updateIpAddr(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "ipAddress") String ipAddress, @RequestParam(value = "subnetMask") String subnetMask,
        @RequestParam(value = "gateway") String gateway,
        @RequestParam(value = "ipAddressSec", required = false) String ipAddressSec,
        @RequestParam(value = "netMaskSec", required = false) String netMaskSec,
        @RequestParam(value = "gateIPAddressSec", required = false) String gateIPAddressSec);

    /**
     * @Description: 根据设备Id获取支持设置登记机的设备
     * <AUTHOR>
     * @date 2018/5/3 16:20
     * @param ids 设备ID
     * @return
     */
    @RequestMapping(params = "getDevIdsBySetRegistrationDevice")
    @ResponseBody
    ModelAndView getDevIdsBySetRegistrationDevice(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 设置登记机
     * <AUTHOR>
     * @date 2018/5/3 16:41
     * @param devId 设备ID
     * @param isRegistrationSel 是否设置为登记机
     * @return
     */
    @RequestMapping(params = "setRegistrationDevice")
    @ResponseBody
    ZKResultMsg setRegistrationDevice(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "isRegistrationSel") String isRegistrationSel);

    /**
     * @Description: 查询设备下的门禁规则
     * <AUTHOR>
     * @date 2018/5/3 20:26
     * @param ids 设备ID
     * @return
     */
    @RequestMapping(params = "queryDevRule")
    @ResponseBody
    ModelAndView queryDevRule(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 查询设备使用情况
     * <AUTHOR>
     * @date 2018/5/4 9:50
     * @param ids 设备ID
     * @return
     */
    @RequestMapping(params = "queryDevUsage")
    @ResponseBody
    ModelAndView queryDevUsage(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 获取设备的人员数量和指纹数量使用情况（从固件中获取）
     * <AUTHOR>
     * @date 2018/5/4 11:23
     * @param devId 设备ID
     * @return
     */
    @RequestMapping(params = "getDevUsage")
    @ResponseBody
    ZKResultMsg getDevUsage(String devId);

    /**
     * @Description: 检测设备支持的pin号和卡号的长度是否和系统匹配
     * <AUTHOR>
     * @date 2018/5/7 10:02
     * @param commTypeVal 设备连接方式
     * @param maxMCUCardBits 卡号最大长度
     * @return
     */
    @RequestMapping(params = "checkPinLenAndCardLen")
    @ResponseBody
    ZKResultMsg checkPinLenAndCardLen(@RequestParam(value = "commTypeVal") String commTypeVal,
        @RequestParam(value = "maxMCUCardBits") String maxMCUCardBits);

    /**
     * 修改设备生物识别阈值
     *
     * @param acc4UpdateMThreshold
     * @return
     */
    @RequestMapping(params = "updateMThreshold")
    @ResponseBody
    ZKResultMsg updateMThreshold(Acc4UpdateMThreshold acc4UpdateMThreshold);

    /**
     * @Description: 检查密码
     * <AUTHOR>
     * @date 2018/5/8 16:38
     * @param oldCommPwd 通信密码
     * @param devId 设备ID
     * @return
     */
    @RequestMapping(params = "checkPwd")
    @ResponseBody
    ZKResultMsg checkPwd(@RequestParam(value = "oldCommPwd") String oldCommPwd,
        @RequestParam(value = "devId") String devId);

    /**
     * @Description: 修改设备密码
     * <AUTHOR>
     * @date 2018/5/8 20:00
     * @param devId 设备Id
     * @param newCommPwd 密码
     * @return
     */
    @RequestMapping(params = "updateCommPwd")
    @ResponseBody
    ZKResultMsg updateCommPwd(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "newCommPwd") String newCommPwd);

    /**
     * @Description: 修改RS485地址
     * <AUTHOR>
     * @date 2018/5/9 11:20
     * @param ids 设备ID
     * @return
     */
    @RequestMapping(params = "getRs485Addr")
    @ResponseBody
    ModelAndView getRs485Addr(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 修改Rs485参数
     * <AUTHOR>
     * @date 2018/5/21 11:40
     * @param devId 设备id
     * @param comAddr 485地址
     * @return
     */
    @RequestMapping(params = "updateRs485Addr")
    @ResponseBody
    ZKResultMsg updateRs485Addr(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "comAddr") String comAddr);

    /**
     * 获取区域下拉树
     * 
     * @author: verber
     * @date: 2018-05-14 11:53:23
     * @return
     */
    @RequestMapping(params = "tree")
    @ResponseBody
    TreeItem tree();

    /**
     * @Description: 根据id获取需要设置时区的设备信息
     * <AUTHOR>
     * @date 2018/5/16 14:36
     * @param ids 设备ID
     * @return
     */
    @RequestMapping(params = "getDevIdsBySetTimeZone")
    @ResponseBody
    ModelAndView getDevIdsBySetTimeZone(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 设置设备时区
     * <AUTHOR>
     * @date 2018/5/16 17:14
     * @param devIds 设备ID
     * @param timeZone 时区
     * @return
     */
    @RequestMapping(params = "setTimeZone")
    @ResponseBody
    ZKResultMsg setTimeZone(@RequestParam(value = "devIds") String devIds,
        @RequestParam(value = "timeZone") String timeZone);

    /**
     * 设置时区和夏令时
     * 
     * @param devIds
     * @param timeZone
     * @param accDSTimeId
     * @return
     */
    @RequestMapping(params = "setTimeZoneAndDsTime")
    @ResponseBody
    ZKResultMsg setTimeZoneAndDsTime(String devIds, String timeZone, String accDSTimeId);

    /**
     * @Description: 根据id获取设备，设置夏令时，按夏令时分类
     * <AUTHOR>
     * @date 2018/5/18 9:23
     * @param ids 设备id
     * @return
     */
    @RequestMapping(params = "getDevIdsBySetDstimeSortByDstime")
    @ResponseBody
    ModelAndView getDevIdsBySetDstimeSortByDstime(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 设置设备夏令时
     * <AUTHOR>
     * @date 2018/5/18 11:56
     * @param devIds 设备id
     * @param dstimeId 夏令时id
     * @return
     */
    @RequestMapping(params = "setDSTime")
    @ResponseBody
    ZKResultMsg setDSTime(@RequestParam(value = "devIds") String devIds,
        @RequestParam(value = "dstimeId") String dstimeId);

    /**
     * @Description: 搜索WIFI列表
     * <AUTHOR>
     * @date 2018/5/21 16:21
     * @param devId 设备id
     * @return
     */
    @RequestMapping(params = "searchWifiList")
    @ResponseBody
    ModelAndView searchWifiList(@RequestParam(value = "devId") String devId);

    /**
     * @Description: 获取wifi list
     * <AUTHOR>
     * @date 2018/5/21 17:19
     * @param cmdId 命令id
     * @return
     */
    @RequestMapping(params = "getWifiList")
    @ResponseBody
    ZKResultMsg getWifiList(@RequestParam(value = "cmdId") String cmdId);

    /**
     * @Description: 重新搜索WIFI列表
     * <AUTHOR>
     * @date 2018/5/21 17:42
     * @param devId 设备id
     * @return
     */
    @RequestMapping(params = "searchWifiListAgain")
    @ResponseBody
    ZKResultMsg searchWifiListAgain(@RequestParam(value = "devId") String devId);

    /**
     * @Description: 测试信号连接是否正常
     * <AUTHOR>
     * @date 2018/5/21 18:00
     * @param devId 设备id
     * @param currentMode 当前设备连接方式
     * @param netConnectMode 修改连接方式
     * @param wirelessSSID wifi帐号
     * @param wirelessKey wifi密码
     * @return
     */
    @RequestMapping(params = "switchNetWorkTest")
    @ResponseBody
    ZKResultMsg switchNetWorkTest(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "currentMode") String currentMode,
        @RequestParam(value = "netConnectMode") String netConnectMode,
        @RequestParam(value = "wirelessSSID", required = false) String wirelessSSID,
        @RequestParam(value = "wirelessKey", required = false) String wirelessKey);

    /**
     * @Description: 获取命令执行结果
     * <AUTHOR>
     * @date 2018/5/22 14:30
     * @param devId 设备Id
     * @param cmdId 命令Id
     * @param type 类型
     * @return
     */
    @RequestMapping(params = "getCmdResultById")
    @ResponseBody
    ZKResultMsg getCmdResultById(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "cmdId") String cmdId, @RequestParam(value = "type") String type);

    /**
     * @Description: 切换设备网络连接方式
     * <AUTHOR>
     * @date 2018/5/22 15:07
     * @param devId 设备id
     * @param netConnectMode 连接方式
     * @param wirelessSSID wifi帐号
     * @param wirelessKey wifi密码
     * @return
     */
    @RequestMapping(params = "updateNetConnectMode")
    @ResponseBody
    ZKResultMsg updateNetConnectMode(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "netConnectMode") String netConnectMode,
        @RequestParam(value = "wirelessSSID") String wirelessSSID,
        @RequestParam(value = "wirelessKey") String wirelessKey);

    /**
     * @Description: 添加跨网段设备
     * <AUTHOR>
     * @date 2018/5/24 14:32
     * @param item 设备item
     * @return
     */
    @RequestMapping(params = "grantAuthority")
    @ResponseBody
    ZKResultMsg grantAuthority(AccSearchAddDeviceItem item);

    @RequestMapping(params = "getAuthorizeChildDevList")
    @ResponseBody
    DxGrid getAuthorizeChildDevList(AccAuthorizeChildDevItem accAuthorizeChildDevItem);

    /**
     * @Description: 校验授权IR9000子设备的许可数量
     * <AUTHOR>
     * @date 2018/5/28 11:02
     * @param devId
     * @param devSns
     * @return
     */
    @RequestMapping(params = "validChildDevCount")
    @ResponseBody
    ZKResultMsg validChildDevCount(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "devSns") String devSns);

    /**
     * @Description: 获取父设备列表
     * <AUTHOR>
     * @date 2018/5/29 19:32
     * @return
     */
    @RequestMapping(params = "getParentDevList")
    @ResponseBody
    ZKResultMsg getParentDevList();

    /**
     * @Description: 配置主设备
     * <AUTHOR>
     * @date 2018/6/15 8:57
     * @param parentDevId
     * @param webServerURL
     * @return
     */
    @RequestMapping(params = "configParentDevice")
    @ResponseBody
    ZKResultMsg configParentDevice(@RequestParam(value = "id") String id,
        @RequestParam(value = "parentDevId") String parentDevId,
        @RequestParam(value = "webServerURL", required = false) String webServerURL);

    /**
     * @Description: // 加载设备树前，判断是否有设备
     * <AUTHOR>
     * @date 2018/7/6 13:58
     * @return
     */
    @RequestMapping(params = "checkDevExist")
    @ResponseBody
    ZKResultMsg checkDevExist();

    /**
     * @Description: 加载设备树形菜单
     * <AUTHOR>
     * @date 2018/7/6 14:06
     * @return
     */
    @RequestMapping(params = "getDevTreeJson")
    @ResponseBody
    ZKResultMsg getDevTreeJson(@RequestParam(value = "type") String type,
        @RequestParam(value = "area", required = false) String area,
        @RequestParam(value = "getDstime") String getDstime);

    /**
     * @Description: 授权子设备
     * <AUTHOR>
     * @date 2018/7/10 11:03
     * @param devId
     * @param devSns
     * @return
     */
    @RequestMapping(params = "authorizeChildDevice")
    @ResponseBody
    ZKResultMsg authorizeChildDevice(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "devSns") String devSns);

    /**
     * @Description: 测试连接服务器是否正常
     * <AUTHOR>
     * @date 2018/7/12 8:49
     * @param devId
     * @param parentDevId
     * @param serverAddress
     * @return
     */
    @RequestMapping(params = "checkServerConnection")
    @ResponseBody
    ZKResultMsg checkServerConnection(@RequestParam(value = "devId") String devId,
        @RequestParam(value = "parentDevId") String parentDevId,
        @RequestParam(value = "serverAddress") String serverAddress);

    /**
     * @Description: 根据设备sn获取设备状态
     * <AUTHOR>
     * @date 2018/7/30 10:41
     * @param devSns
     * @return
     */
    @RequestMapping(params = "isOnlineByDevSn")
    @ResponseBody
    ZKResultMsg isOnlineByDevSn(@RequestParam(value = "devSns") String devSns);

    /**
     * 获取设备参数（pull添加使用）
     * 
     * @return
     */
    @RequestMapping(params = "getDevInfo")
    @ResponseBody
    ZKResultMsg getDevInfo(AccDeviceParamItem item);

    /**
     * @Description: 查看子设备列表
     * @date 2018/8/15 10:27
     * @param id
     * @return
     */
    @RequestMapping(params = "lookUpChildDeviceInfo")
    @ResponseBody
    ModelAndView lookUpChildDeviceInfo(@RequestParam(value = "id") String id);

    /**
     * @Description: 加载子设备列表
     * <AUTHOR>
     * @date 2018/8/15 10:33
     * @param parentDevId
     * @return
     */
    @RequestMapping(params = "getChildDevList")
    @ResponseBody
    DxGrid getChildDevList(@RequestParam(value = "parentDevId") String parentDevId);

    /**
     * 判断设备是否支持清除管理员操作
     *
     * <AUTHOR>
     * @since 2018年12月3日 下午4:29:03
     * @param devIds
     * @return
     */
    @RequestMapping(params = "isSupportClearAdministrator")
    @ResponseBody
    ZKResultMsg isSupportClearAdministrator(@RequestParam(value = "devIds") String devIds);

    /**
     * 清除设备管理员
     *
     * <AUTHOR>
     * @since 2018年12月3日 下午5:28:56
     * @param devIds
     * @return
     */
    @RequestMapping(params = "clearAdministrator")
    @ResponseBody
    ZKResultMsg clearAdministrator(@RequestParam(value = "devIds") String devIds);

    /**
     * 获取设备进出状态
     *
     * <AUTHOR>
     * @since 2018年12月5日 下午3:36:54
     * @param ids
     * @param type
     * @return
     */
    @RequestMapping(params = "getDevIOStateInfo")
    @ResponseBody
    ModelAndView getDevIOStateInfo(@RequestParam(value = "ids") String ids, @RequestParam(value = "type") String type);

    /**
     * 设置一体机出入状态
     *
     * <AUTHOR>
     * @since 2018年12月5日 下午5:14:27
     * @return
     */
    @RequestMapping(params = "setDevIOState")
    @ResponseBody
    ZKResultMsg setDevIOState();

    @RequestMapping(params = "validDevRegDeviceType")
    @ResponseBody
    ZKResultMsg validDevRegDeviceType(@RequestParam(value = "regDeviceType") String regDeviceType);

    /**
     * 获取支持远程登记设备
     *
     * <AUTHOR>
     * @since 2018年12月6日 下午6:40:29
     * @return
     */
    @RequestMapping(params = "getRemoteRegistDev")
    @ResponseBody
    ZKResultMsg getRemoteRegistDev();

    /**
     * 远程登记生物模版
     *
     * <AUTHOR>
     * @since 2018年12月10日 上午11:14:11
     * @return
     */
    @RequestMapping(params = "remoteRegistration")
    @ResponseBody
    ZKResultMsg remoteRegistration();

    /**
     * 获取远程登记模板
     *
     * <AUTHOR>
     * @since 2018年12月10日 上午10:47:04
     * @return
     */
    @RequestMapping(params = "getRemoteTemplate")
    @ResponseBody
    ZKResultMsg getRemoteTemplate();

    /**
     * 功能描述: 获取身份证信息
     * 
     * <AUTHOR>
     * @since 2019-05-09 20:23
     * @Param []
     * @return
     */
    @RequestMapping(params = "getReadIDCardInfo")
    @ResponseBody
    ZKResultMsg getReadIDCardInfo();

    /**
     * 设置设备扩展参数界面跳转
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2020/6/2 14:04
     * @param ids
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "getDevExtendInfo")
    @ResponseBody
    ModelAndView getDevExtendInfo(String ids);

    /**
     * 更新扩展参数
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2020/6/3 16:59
     * @param params
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "updateDevExtendParam")
    @ResponseBody
    ZKResultMsg updateDevExtendParam(@RequestParam Map<String, String> params);

    /**
     * 返回设置ntp服务信息的页面
     * 
     * @param ids:
     * @return org.springframework.web.servlet.ModelAndView
     * <AUTHOR>
     * @throws
     * @date 2022-07-26 13:59
     * @since 1.0.0
     */
    @RequestMapping(params = "getNtpServerInfo")
    ModelAndView getNtpServerInfo(@RequestParam(value = "ids") String ids);

    /**
     * 处理下发ntp服务的请求
     *
     * @param ids
     * @param status
     * @param serverAddress
     * @return
     */
    @RequestMapping(params = "setNtpServer")
    void setNtpServer(@RequestParam(value = "ids") String ids, @RequestParam(value = "status") Short status,
        @RequestParam(value = "serverAddress", required = false) String serverAddress);

    /**
     * 保存要替换的设备sn,ipAddress;
     *
     * @param newSn:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-08-22 15:01
     * @since 1.0.0
     */
    @RequestMapping(params = "replaceDevInfo")
    @ResponseBody
    ZKResultMsg replaceDevInfo(@RequestParam(value = "newSn") String newSn);

    /**
     * 校验填写的sn是否已被添加或者是否已被设置为替换的sn
     * 
     * @param newSn:
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2022-08-10 14:44
     * @since 1.0.0
     */
    @RequestMapping(params = "validSn")
    @ResponseBody
    String validSn(@RequestParam(value = "newSn") String newSn);

    /**
     * sn作为checkbox的id
     * 
     * @return com.zkteco.zkbiosecurity.base.bean.TreeItem
     * <AUTHOR>
     * @throws
     * @date 2023-02-17 16:22
     * @since 1.0.0
     */
    @RequestMapping(params = "devSnTree")
    @ResponseBody
    TreeItem devSnTree();

    /**
     * 清除设备命令
     *
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2023-09-26 17:31
     * @since 1.0.0
     */
    @RequestMapping(params = "clearCmdCache")
    @ResponseBody
    ZKResultMsg clearCmdCache(String ids);

    @RequestMapping(params = "isSupportReplaceDevice")
    @ResponseBody
    ZKResultMsg isSupportReplaceDevice(@RequestParam(value = "devIds") String devIds);

    @RequestMapping(params = "validAddPushDev")
    @ResponseBody
    ZKResultMsg validAddPushDev(@RequestParam(value = "sn") String sn,
        @RequestParam(required = false, value = "machineType") String machineType,
        @RequestParam(required = false, value = "isSupportNVR") String isSupportNVR);

    @RequestMapping(params = "setResourceFile")
    @ResponseBody
    ModelAndView setResourceFile(@RequestParam(value = "ids") String ids, String type);

    @RequestMapping(params = "getAdResourceFile")
    @ResponseBody
    ZKResultMsg getAdResourceFile();

    @RequestMapping(params = "uploadAdResourceFile")
    @ResponseBody
    ZKResultMsg uploadAdResourceFile(Acc4UploadAdFileItem item);

    @RequestMapping(params = "getDevLinkIPC")
    @ResponseBody
    ZKResultMsg getDevLinkIPC(String devSn);

    /**
     * 返回设置人脸比对服务信息的页面
     *
     * @param ids
     * @return
     */
    @RequestMapping(params = "getFaceVerifyServerInfo")
    ModelAndView getFaceVerifyServerInfo(@RequestParam(value = "ids") String ids);

    /**
     * 返回设置人脸比对服务信息的页面
     *
     * @param item
     * @return
     */
    @RequestMapping(params = "setFaceVerifyServer")
    void setFaceVerifyServer(Acc4SetFaceVerifyServer item);
}