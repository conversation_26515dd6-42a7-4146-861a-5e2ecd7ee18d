/**
 * File Name: AccMapPos Created by GenerationTools on 2018-03-20 下午02:07 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccMapPosItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accMapPos.do")
public interface AccMapPosRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id") String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccMapPosItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccMapPosItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 获取门或摄像头的宽
     * 
     * @author: verbver
     * @date: 2018-03-23 14:07
     * @param mapId
     * @return
     */
    @RequestMapping(params = "getEntityWidthByMapId")
    @ResponseBody
    ZKResultMsg getEntityWidthByMapId(@RequestParam(value = "mapId") String mapId);
}