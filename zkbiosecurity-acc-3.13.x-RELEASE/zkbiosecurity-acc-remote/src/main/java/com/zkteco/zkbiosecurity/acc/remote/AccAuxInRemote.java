/**
 * File Name: AccAuxIn Created by GenerationTools on 2018-03-13 下午05:00 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccAuxInItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accAuxIn.do")
public interface AccAuxInRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 下午05:00
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 下午05:00
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id") String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 下午05:00
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccAuxInItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 下午05:00
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccAuxInItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 下午05:00
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 判断是否存在
     * 
     * @author: verber
     * @date: 2018-03-13 18:13
     * @param item
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    boolean isExist(AccAuxInItem item);

    /**
     * 获取与实体（辅助输入）绑定的摄像头，页面跳转
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 15:48
     * @param
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "getChannelByEntityId")
    @ResponseBody
    ModelAndView getChannelByEntityId();

    /**
     * 读头绑定/解绑摄像头
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 15:49
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "bindOrUnbindChannel")
    @ResponseBody
    ZKResultMsg bindOrUnbindChannel();
}