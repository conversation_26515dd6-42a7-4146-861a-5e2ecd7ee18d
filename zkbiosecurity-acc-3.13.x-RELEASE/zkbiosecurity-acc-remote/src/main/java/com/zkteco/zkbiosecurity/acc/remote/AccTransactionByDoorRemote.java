package com.zkteco.zkbiosecurity.acc.remote;

import com.zkteco.zkbiosecurity.acc.vo.AccTransactionDoorPersonItem;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccTransactionDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@RequestMapping(value = "/accTransactionByDoor.do")
public interface AccTransactionByDoorRemote {
    /**
     * 默认页面跳转
     * 
     * @author: yibiao.shen
     * @date:2018-03-09 15:31:23
     * @return
     */
    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccTransactionDoorItem condition);

    @RequestMapping(params = "personList")
    @ResponseBody
    DxGrid personList(AccTransactionDoorPersonItem condition);

    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);
}
