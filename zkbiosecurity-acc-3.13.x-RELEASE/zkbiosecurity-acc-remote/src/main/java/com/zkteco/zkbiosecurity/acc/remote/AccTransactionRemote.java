package com.zkteco.zkbiosecurity.acc.remote;

import java.lang.reflect.InvocationTargetException;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accTransaction.do")
public interface AccTransactionRemote {
    /**
     * 默认页面跳转
     * 
     * @author: yibiao.shen
     * @date:2018-03-07 16:48:23
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     * 
     * @author: yibiao.shen
     * @date:2018-03-06 16:53:23
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccTransactionItem condition);

    /**
     * 级联删除数据
     * 
     * @author: yibiao.shen
     * @date:2018-03-06 16:53:23
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 删除所有数据
     * 
     * @author: verber
     * @date: 2018-04-28 15:13
     * @return
     */
    @RequestMapping(params = "clearData")
    @ResponseBody
    ZKResultMsg clearData();

    /**
     * 导出数据
     * 
     * @author: verber
     * @date: 2018-05-03 10:02
     * @return
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response)
        throws InvocationTargetException, IllegalAccessException;

    /**
     * @Description: 判断文件是否存在
     * <AUTHOR>
     * @date 2018/6/12 9:14
     * @param vidLinkageHandle
     * @return
     */
    @RequestMapping(params = "isFileExist")
    @ResponseBody
    ZKResultMsg isFileExist(@RequestParam(value = "vidLinkageHandle") String vidLinkageHandle);

    /**
     * @Description: 查询门最近发生事件
     * <AUTHOR>
     * @date 2018/6/14 18:02
     * @param accTransactionItem
     * @return
     */
    @RequestMapping(params = "getTransactionsTodayLast")
    @ResponseBody
    DxGrid getTransactionsTodayLast(AccTransactionItem accTransactionItem);

    /**
     * 读取卡号或者身份证
     * 
     * @param readerIds
     * @param type
     *            cardNo:卡号 IDCard:身份证
     * @return
     */
    @RequestMapping(params = "readerCardNo")
    @ResponseBody
    ZKResultMsg readerCardNo(@RequestParam(value = "readerIds") String readerIds,
        @RequestParam(value = "type") String type, @RequestParam(value = "time", required = false) Date time);

    /**
     * 手机APP根据用户和门获取开门记录
     * 
     * @param pin
     *            用户pin号
     * @param doorId
     *            门id
     * @param pageNo
     *            开始页编号
     * @param pageSize
     *            页面大小
     * <AUTHOR>
     * @return
     */
    @RequestMapping(params = "listByApp")
    @ResponseBody
    ZKResultMsg listByApp(String pin, String doorId, int pageNo, int pageSize);

    /**
     * 获取事件对应视频联动数据
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/29 10:36
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "viewVidLinkData")
    @ResponseBody
    ZKResultMsg viewVidLinkData(String vidLinkageHandle, String fileType);

    /**
     * 事件记录下载视频录像文件
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/29 10:38
     * @param id
     *            事件id
     * @return
     */
    @RequestMapping(params = "getVideoFile")
    @ResponseBody
    ZKResultMsg getVideoFile(String id);

    /**
     * 导出事件记录照片
     *
     * @date 2020/7/22 17:45
     * @throws Exception
     */
    @RequestMapping(params = "exportPhoto")
    void exportPhoto() throws Exception;
	
	/**
	 * 事件记录下载digifort录像
	 * <AUTHOR>
	 * @since 2020/7/17 18:52
	 * @param cameraName
	 * @param eventTime
	 * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
	 */
	@RequestMapping(params="getDigifortVideoFile")
	@ResponseBody
	ZKResultMsg getDigifortVideoFile(String cameraName, long eventTime);

	/**
	 * 获取解密照片
	 *
	 * @param photoPath:照片路径
	 * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
	 * <AUTHOR>
	 * @date 2021-05-26 17:41
	 * @since 1.0.0
	 */
    @RequestMapping(params="getDecryptPhotoBase64")
    @ResponseBody
    ZKResultMsg getDecryptPhotoBase64(String photoPath);
}
