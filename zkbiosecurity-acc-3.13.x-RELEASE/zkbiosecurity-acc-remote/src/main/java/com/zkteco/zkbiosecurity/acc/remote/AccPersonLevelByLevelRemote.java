/**
 * File Name: AccPersonLevelByLevel Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccPersonForLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonLevelByLevelItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accPersonLevelByLevel.do")
public interface AccPersonLevelByLevelRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccPersonLevelByLevelItem codition);

    /**
     * 获取按权限组设置人员列表
     * 
     * <AUTHOR>
     * @since 2018/3/14 14:33
     * @return
     */
    @RequestMapping(params = "getLevelPerson")
    @ResponseBody
    DxGrid getLevelPerson(AccPersonForLevelItem condition);

    /**
     * 按权限组设置添加人员
     * 
     * <AUTHOR>
     * @since 2018/3/22 16:51
     * @return
     */
    @RequestMapping(params = "addPerson")
    @ResponseBody
    ZKResultMsg addPerson(@RequestParam(value = "levelId") String levelId,
        @RequestParam(value = "personIds") String personIds, @RequestParam(value = "deptIds") String deptIds);

    /**
     * 按权限组删除人员
     * 
     * <AUTHOR>
     * @since 2018/3/22 18:32
     * @return
     */
    @RequestMapping(params = "delPerson")
    @ResponseBody
    ZKResultMsg delPerson(@RequestParam(value = "levelId") String levelId,
        @RequestParam(value = "personIds") String personIds);

    /**
     * @Description: 导出权限组下的人员
     * <AUTHOR>
     * @date 2018/6/27 10:09
     * @return
     */
    @RequestMapping(params = "export")
    @ResponseBody
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 批量导出权限组人员信息
     *
     * @param request:
     * @param response:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-07-25 13:58
     * @since 1.0.0
     */
    @RequestMapping(params = "exportLevelPerson")
    @ResponseBody
    void exportLevelPerson(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入权限组人员信息
     * 
     * @param upload:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-07-26 17:40
     * @since 1.0.0
     */
    @RequestMapping(params = "importLevelPerson")
    @ResponseBody
    ZKResultMsg importLevelPerson(@RequestParam("upload") MultipartFile upload) throws IOException;

}