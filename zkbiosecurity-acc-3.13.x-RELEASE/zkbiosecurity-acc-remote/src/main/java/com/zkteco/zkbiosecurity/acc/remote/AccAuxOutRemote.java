/**
 * File Name: AccAuxOut Created by GenerationTools on 2018-03-14 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccAuxOutItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accAuxOut.do")
public interface AccAuxOutRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 上午09:38
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 上午09:38
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id") String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 上午09:38
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccAuxOutItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 上午09:38
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccAuxOutItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 上午09:38
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 操作辅助输出页面跳转
     * 
     * @author: verber
     * @date: 2018-03-29 14:38
     * @return
     */
    @RequestMapping(params = "getAuxOutIds")
    ModelAndView getAuxOutIds(@RequestParam(value = "type") String type, @RequestParam(value = "ids") String ids);

    /**
     * 判断是否存在
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 上午09:38
     * @param item
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    boolean isExist(AccAuxOutItem item);

    /**
     * 远程打开
     *
     * @return
     */
    @RequestMapping(params = "openAuxOut")
    @ResponseBody
    ZKResultMsg openAuxOut(@RequestParam(value = "ids") String ids,
                           @RequestParam(value = "openInterval") String openInterval);

    /**
     * 远程关闭
     *
     * @return
     */
    @RequestMapping(params = "closeAuxOut")
    @ResponseBody
    ZKResultMsg closeAuxOut(@RequestParam(value = "ids") String ids);

    /**
     * 远程常开
     *
     * @return
     */
    @RequestMapping(params = "auxOutNormalOpen")
    @ResponseBody
    ZKResultMsg auxOutNormalOpen(@RequestParam(value = "ids") String ids);
}