package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.acc.vo.AccSelectReaderItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

@RequestMapping(value = "/accSelectReader.do")
public interface AccSelectReaderRemote {

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccSelectReaderItem codition);
}
