/**
 * File Name: AccMap Created by GenerationTools on 2018-03-20 下午02:07 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccMapItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accMap.do")
public interface AccMapRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(@RequestParam(value = "file", required = false) MultipartFile file, AccMapItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccMapItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-20 下午02:07
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 获取区域地图树
     * 
     * @author: Verber
     * @date: 2018-03-19 16:48:23
     * @return
     */
    @RequestMapping(params = "getMapTree")
    @ResponseBody
    TreeItem getMapTree();

    /**
     * 获取区域地图
     * 
     * @author: Verber
     * @date: 2018-03-20 10:48:23
     * @return
     */
    @RequestMapping(params = "getMap")
    @ResponseBody
    ModelAndView getMap(@RequestParam(value = "id") String id);

    /**
     * 保存地图位置
     * 
     * @author: Verber
     * @date: 2018-03-22 16:48:23
     * @return
     */
    @RequestMapping(params = "saveMapPos")
    @ResponseBody
    ZKResultMsg saveMapPos(@RequestParam(value = "mapId") String mapId,
        @RequestParam(value = "mapWidth") Double mapWidth, @RequestParam(value = "mapHeight") Double mapHeight,
        @RequestParam(value = "posArray") String posArray);

    /**
     * 
     * @author: Verber
     * @date: 2018-03-25 16:48:23
     * @return
     */
    @RequestMapping(params = "addDoor")
    @ResponseBody
    ZKResultMsg addDoor(@RequestParam(value = "mapId") String mapId, @RequestParam(value = "width") Double width,
        @RequestParam(value = "entityType") String entityType, @RequestParam(value = "entityIds") String entityIds,
        @RequestParam(value = "logMethod") String logMethod);

    /**
     *
     * @author: Verber
     * @date: 2018-03-25 16:48:23
     * @return
     */
    @RequestMapping(params = "addChannel")
    @ResponseBody
    ZKResultMsg addChannel(@RequestParam(value = "mapId") String mapId, @RequestParam(value = "width") Double width,
        @RequestParam(value = "entityType") String entityType, @RequestParam(value = "entityIds") String entityIds,
        @RequestParam(value = "logMethod") String logMethod);

    /**
     * 判断名称是否存在
     * 
     * @author: Verber
     * @date: 2018-03-20 11:48:23
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    boolean isExist(AccMapItem item);

    /**
     * 添加门或视频的跳转
     * 
     * @author: verber
     * @date: 2018-03-23 15:59
     * @param mapId
     * @param entityType
     * @param width
     * @return
     */
    @RequestMapping(params = "getEntitySelectItem")
    ModelAndView getEntitySelectItem(@RequestParam(value = "mapId") String mapId,
        @RequestParam(value = "entityType") String entityType, @RequestParam(value = "width") String width);

    /**
     * 判断名称是否存在视频模块和视频设备
     * 
     * @author: verber
     * @date: 2018-04-04 16:06
     * @return
     */
    @RequestMapping(params = "isExistVid")
    @ResponseBody
    ZKResultMsg isExistVid();

    /**
     * 摄像头预览界面跳转
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/29 11:57
     * @return
     */
    @RequestMapping(params = "getVidPreview")
    @ResponseBody
    ModelAndView getVidPreview();
}