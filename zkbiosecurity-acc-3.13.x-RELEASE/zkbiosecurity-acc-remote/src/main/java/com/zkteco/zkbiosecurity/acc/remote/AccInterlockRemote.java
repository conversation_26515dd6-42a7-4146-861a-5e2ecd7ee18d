/**
 * File Name: AccInterlock Created by GenerationTools on 2018-03-13 上午09:53 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccInterlockItem;
import com.zkteco.zkbiosecurity.acc.vo.AccInterlockSelectDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccInterlockSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accInterlock.do")
public interface AccInterlockRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午09:53
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午09:53
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午09:53
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccInterlockItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午09:53
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccInterlockItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午09:53
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 获取互锁模式
     * 
     * @return
     */
    @RequestMapping(params = "getRule")
    @ResponseBody
    ZKResultMsg getRule(@RequestParam(value = "deviceId") String deviceId);

    /**
     * 互锁：选设备控件
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/16 9:39
     * @return:
     */
    @RequestMapping(params = "selectDevicelist")
    @ResponseBody
    DxGrid selectDevicelist(AccInterlockSelectDeviceItem codition);

    /**
     * 通过设备id选择门
     * 
     * @auther 31876
     * @date 14:02
     * @since 1.0.0
     */
    @RequestMapping(params = "selectDoor")
    @ResponseBody
    ModelAndView selectDoor(String deviceId, String notInId, String interLockId, String group);

    /**
     * 互锁：选门控件
     * 
     * @auther 31876
     * @date 14:03
     * @since 1.0.0
     */
    @RequestMapping(params = "selectDoorlist")
    @ResponseBody
    DxGrid selectDoorlist(AccInterlockSelectDoorItem codition);

    /**
     * 名称唯一检测
     * 
     * @auther 31876
     * @date 14:04
     * @since 1.0.0
     */
    @RequestMapping(params = "validName")
    @ResponseBody
    boolean validName(String name);

    /**
     * 通过设备id获取需要自动填入的门信息
     * 
     * @auther 31876
     * @date 14:05
     * @since 1.0.0
     */
    @RequestMapping(params = "getAutoFillDoorByDev")
    @ResponseBody
    ZKResultMsg getAutoFillDoorByDev(String devId);

    /**
     * 通过设备判断需要的互锁规则
     * 
     * @auther 31876
     * @date 14:05
     * @since 1.0.0
     */
    @RequestMapping(params = "validDetermineInterlock")
    @ResponseBody
    ZKResultMsg validDetermineInterlock(String devId);

}