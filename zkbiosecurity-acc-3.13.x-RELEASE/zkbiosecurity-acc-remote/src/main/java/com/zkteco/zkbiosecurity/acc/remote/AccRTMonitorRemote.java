/**
 * File Name: AccDeviceEvent Created by GenerationTools on 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import java.util.List;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccRTMonitorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accRTMonitor.do")
public interface AccRTMonitorRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午02:44
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午02:44
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccRTMonitorItem codition);

    /**
     * 
     * 获取所有门设备
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:11
     * @return
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping(params = "getAllDoor")
    @ResponseBody
    List getAllDoor();

    /**
     * 
     * 获取所有辅助输入设备
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:30
     * @return
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping(params = "getAllAuxIn")
    @ResponseBody
    List getAllAuxIn();

    /**
     * 
     * 获取所有辅助输出设备
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:41
     * @return
     */
    @SuppressWarnings("rawtypes")
    @RequestMapping(params = "getAllAuxOut")
    @ResponseBody
    List getAllAuxOut();

    /**
     * 
     * 远程开门
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:34:40
     * @param ids
     * @param openInterval
     * @return
     */
    @RequestMapping(params = "openDoor")
    @ResponseBody
    ZKResultMsg openDoor(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "openInterval") String openInterval);

    /**
     * 
     * 远程关门
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:36:52
     * @param ids
     * @return
     */
    @RequestMapping(params = "closeDoor")
    @ResponseBody
    ZKResultMsg closeDoor(@RequestParam(value = "ids") String ids);

    /**
     * 
     * 取消报警
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:19
     * @param ids
     * @return
     */
    @RequestMapping(params = "cancelAlarm")
    @ResponseBody
    ZKResultMsg cancelAlarm(@RequestParam(value = "ids") String ids);

    /**
     * 
     * 远程锁定
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:34
     * @return
     */
    @RequestMapping(params = "lockDoor")
    @ResponseBody
    ZKResultMsg lockDoor(@RequestParam(value = "ids") String ids);

    /**
     * 
     * 远程解锁
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:37:49
     * @return
     */
    @RequestMapping(params = "unLockDoor")
    @ResponseBody
    ZKResultMsg unLockDoor(@RequestParam(value = "ids") String ids);

    /**
     * 
     * 远程常开
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:38:08
     * @param ids
     * @return
     */
    @RequestMapping(params = "normalOpenDoor")
    @ResponseBody
    ZKResultMsg normalOpenDoor(@RequestParam(value = "ids") String ids);

    @RequestMapping(params = "getAreasByUser")
    @ResponseBody
    ZKResultMsg getAreasByUser();

    /**
     * 
     * 启用当天常开时间段
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:38:42
     * @return
     */
    @RequestMapping(params = "enableNormalOpenDoor")
    @ResponseBody
    ZKResultMsg enableNormalOpenDoor(@RequestParam(value = "ids") String ids);

    /**
     * 
     * 禁用当天常开时间段
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:15
     * @return
     */
    @RequestMapping(params = "disableNormalOpenDoor")
    @ResponseBody
    ZKResultMsg disableNormalOpenDoor(@RequestParam(value = "ids") String ids);

    /**
     * 
     * 远程打开
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:35
     * @return
     */
    @RequestMapping(params = "openAuxOut")
    @ResponseBody
    ZKResultMsg openAuxOut(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "openInterval") String openInterval);

    /**
     * 
     * 远程关闭
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:39:51
     * @return
     */
    @RequestMapping(params = "closeAuxOut")
    @ResponseBody
    ZKResultMsg closeAuxOut(@RequestParam(value = "ids") String ids);

    /**
     * 
     * 远程常开
     * 
     * @author: train.chen
     * @date: 2018年5月18日 下午4:40:04
     * @return
     */
    @RequestMapping(params = "auxOutNormalOpen")
    @ResponseBody
    ZKResultMsg auxOutNormalOpen(@RequestParam(value = "ids") String ids);

    @RequestMapping(params = "isKeepAlive")
    @ResponseBody
    ZKResultMsg isKeepAlive();

    /**
     * 判断是否有LCD许可，判断是否能在实时监控显示LCD功能菜单；
     * 
     * <AUTHOR>
     * @since 2018年11月29日 下午1:55:43
     * @return
     */
    @RequestMapping(params = "isShowlcdRTMonitor")
    @ResponseBody
    Boolean isShowlcdRTMonitor();

    /**
     * 打开LCD页面
     * 
     * <AUTHOR>
     * @since 2018年11月29日 下午1:56:21
     * @return
     */
    @RequestMapping(params = "openLcdRTMonitor")
    @ResponseBody
    ModelAndView openLcdRTMonitor();

    /**
     * 获取进入监控界面最近的5条人员通行信息
     * 
     * <AUTHOR>
     * @since 2018年11月29日 下午1:56:13
     * @param areaNames
     * @return
     */
    @RequestMapping(params = "getPersonInfo")
    @ResponseBody
    ZKResultMsg getPersonInfo(@RequestParam(value = "areaNames") String areaNames);
}