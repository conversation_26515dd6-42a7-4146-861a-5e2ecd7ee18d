/**
 * File Name: AccPerson Created by GenerationTools on 2018-03-02 下午02:10 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.AccPersonListItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectPersonRadioItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accPerson.do")
public interface AccPersonRemote {
    /**
     * 编辑界面跳转
     *
     * @param personId
     * @return
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:10
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "personId", required = false) String personId);

    @RequestMapping(params = "saveParamSet")
    @ResponseBody
    ZKResultMsg saveParamSet(AccPersonItem item, @RequestParam(value = "ids") String ids);

    /**
     * 根据部门ID获取部门人数
     *
     * @return
     * <AUTHOR>
     * @since 2018/3/14 14:33
     */
    @RequestMapping(params = "getPersonCountByDept")
    @ResponseBody
    ZKResultMsg getPersonCountByDept(@RequestParam(value = "deptIds", required = false) String deptIds);

    /**
     * 获取全局联动人员列表
     *
     * @return
     * <AUTHOR>
     * @since 2018/4/3 14:38
     */
    @RequestMapping(params = "getGlobalLinkagePerson")
    @ResponseBody
    DxGrid getGlobalLinkagePerson(AccPersonListItem codition);

    /**
     * @param cardNo
     * @return
     * @Description: 过滤有有效卡的人员
     * <AUTHOR>
     * @date 2018/6/22 9:44
     */
    @RequestMapping(params = "filterPersonByVaildCard")
    @ResponseBody
    ModelAndView filterPersonByVaildCard(@RequestParam(value = "cardNo") String cardNo);

    /**
     * @param accSelectPersonRadioItem
     * @return
     * @Description: 获取没有卡号人员列表
     * <AUTHOR>
     * @date 2018/6/22 9:45
     */
    @RequestMapping(params = "getNoCardPerson")
    @ResponseBody
    DxGrid getNoCardPerson(AccSelectPersonRadioItem accSelectPersonRadioItem);

    /**
     * 选人控件
     *
     * @author: mingfa.zheng
     * @date: 2018/4/12 10:42
     * @return:
     */
    @RequestMapping(params = "selectPersonlist")
    @ResponseBody
    DxGrid selectPersonlist(AccSelectPersonItem codition);

    /**
     * @param personId
     * @param cardNo
     * @return
     * @Description: 人员设置卡号
     * <AUTHOR>
     * @date 2018/6/27 11:32
     */
    @RequestMapping(params = "setPersonCard")
    @ResponseBody
    ZKResultMsg setPersonCard(@RequestParam(value = "personId") String personId,
        @RequestParam(value = "cardNo") String cardNo);

    /**
     * 实时监控发卡页面跳转
     *
     * @param cardNo
     * @return
     */
    @RequestMapping(params = "getAllField")
    ModelAndView getAllField(@RequestParam(value = "cardNo", required = false) String cardNo);

    @RequestMapping(params = "selectLevelPersonList")
    @ResponseBody
    DxGrid selectLevelPersonList(AccSelectPersonItem condition);
}