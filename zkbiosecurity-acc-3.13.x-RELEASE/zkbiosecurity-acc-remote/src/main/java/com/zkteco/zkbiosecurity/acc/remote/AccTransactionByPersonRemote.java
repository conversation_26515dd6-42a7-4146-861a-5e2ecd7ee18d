package com.zkteco.zkbiosecurity.acc.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccTransactionPersonDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionPersonItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

@RequestMapping(value = "/accTransactionByPerson.do")
public interface AccTransactionByPersonRemote {
    /**
     * 默认页面跳转
     * 
     * @author: yibiao.shen
     * @date:2018-03-09 16:33:23
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 上午09:13
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccTransactionPersonItem condition);

    /**
     * 列表数据获取
     *
     * @author: verber
     * @date: 2018-04-21 上午09:13
     * @param condition
     * @return
     */
    @RequestMapping(params = "doorList")
    @ResponseBody
    DxGrid doorList(AccTransactionPersonDoorItem condition);

    /**
     * 导出数据
     *
     * @author: verber
     * @date: 2018-05-03 10:02
     * @return
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);
}
