/**
 * File Name: AccDeviceVerifyMode Created by GenerationTools on 2018-03-20 下午04:20 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.base.bean.TreeItem;

@RequestMapping(value = "/accDeviceVerifyMode.do")
public interface AccDeviceVerifyModeRemote {
    /**
     * 获取已定义验证方式下拉树
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2020/3/12 10:31
     * @return
     */
    @RequestMapping(params = "getDeviceVerifyModeTree")
    @ResponseBody
    TreeItem getDeviceVerifyModeTree();

    /**
     * 根据设备id获取设备新验证方式
     *
     * @param deviceId:设备id
     * @return com.zkteco.zkbiosecurity.base.bean.TreeItem
     * <AUTHOR>
     * @date 2020-11-10 15:38
     * @since 1.0.0
     */
    @RequestMapping(params = "getDeviceVerifyModeTreeByDeviceId")
    @ResponseBody
    TreeItem getDeviceVerifyModeTreeByDeviceId(@RequestParam(value = "deviceId") String deviceId);

    /**
     * 获取支持且无重复的新验证方式下拉树
     *
     * @return com.zkteco.zkbiosecurity.base.bean.TreeItem
     * <AUTHOR>
     * @date 2020-11-30 18:07
     * @since 1.0.0
     */
    @RequestMapping(params = "getSupportNewVerifyModeTree")
    @ResponseBody
    TreeItem getSupportNewVerifyModeTree();
}