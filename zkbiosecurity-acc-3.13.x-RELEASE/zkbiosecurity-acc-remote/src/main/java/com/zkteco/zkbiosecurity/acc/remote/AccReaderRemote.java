/**
 * File Name: AccReader Created by GenerationTools on 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectReaderItem;
import com.zkteco.zkbiosecurity.acc.vo.AccSelectReaderRadioItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accReader.do")
public interface AccReaderRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:06
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:06
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id") String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:06
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccReaderItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:06
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccReaderItem condition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-13 上午10:06
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 判断名称是否存在
     * 
     * @author: verber
     * @date: 2018-03-13 16:06
     * @param item
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    boolean isExist(AccReaderItem item);

    /**
     * 判断名称是否存在视频模块
     * 
     * @author: verber
     * @date: 2018-04-04 16:06
     * @return
     */
    @RequestMapping(params = "isExistVid")
    @ResponseBody
    boolean isExistVid();

    /**
     * 判断IP是否存在
     * 
     * @author: verber
     * @date: 2018-05-11 16:06
     * @param item
     * @return
     */
    @RequestMapping(params = "isExistIP")
    @ResponseBody
    boolean isExistIP(AccReaderItem item);

    /**
     * 判断通讯地址是否存在
     * 
     * @author: verber
     * @date: 2018-05-11 16:06
     * @param item
     * @return
     */
    @RequestMapping(params = "readerCommAddressExist")
    @ResponseBody
    boolean readerCommAddressExist(AccReaderItem item);

    /**
     * @Description: 过滤非一体机的读头，用于设置韦根读头
     * <AUTHOR>
     * @date 2018/6/26 11:39
     * @param condition
     * @return
     */
    @RequestMapping(params = "getWGReaderFilterList")
    @ResponseBody
    DxGrid getWGReaderFilterList(AccSelectReaderRadioItem condition);

    /**
     * 选读头控件
     * 
     * @author: mingfa.zheng
     * @date: 2018/7/1 10:42
     * @return:
     */
    @RequestMapping(params = "selectReaderList")
    @ResponseBody
    DxGrid selectReaderList(AccSelectReaderItem condition);

    /**
     * @Description ai模块获取绑定的读头列表(目前只支持inbio5的控制器)
     * <AUTHOR>
     * @Date 2018/11/20 15:18
     * @Param
     * @return
     **/
    @RequestMapping(params = "selectReaderBindAiDeviceList")
    @ResponseBody
    DxGrid selectReaderBindAiDeviceList(AccSelectReaderRadioItem condition);

    /**
     * 功能描述: 过滤非TCP读头，用于身份证读取方式是TCP读头的情况下
     * 
     * <AUTHOR>
     * @since 2019-05-06 17:20
     * @Param [condition]
     * @return
     */
    @RequestMapping(params = "getTcpReaderFilterList")
    @ResponseBody
    DxGrid getTcpReaderFilterList(AccSelectReaderRadioItem condition);

    @RequestMapping(params = "startReaderIDCard")
    @ResponseBody
    ZKResultMsg startReaderIDCard();

    /**
     * 获取与实体（读头）绑定的摄像头，页面跳转
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 15:03
     * @param
     * @return org.springframework.web.servlet.ModelAndView
     */
    @RequestMapping(params = "getChannelByEntityId")
    @ResponseBody
    ModelAndView getChannelByEntityId();

    /**
     * 读头绑定/解绑摄像头
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 15:06
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "bindOrUnbindChannel")
    @ResponseBody
    ZKResultMsg bindOrUnbindChannel();
}