/**
 * File Name: AccCombOpenDoor Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccCombOpenSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accCombOpenDoor.do")
public interface AccCombOpenDoorRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccCombOpenDoorItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccCombOpenDoorItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 判断名称是否有效/允许
     * 
     * @author: mingfa.zheng
     * @date: 2018-02-23 09:51:28
     * @param name
     * @return
     */
    @RequestMapping(params = "valid")
    @ResponseBody
    String valid(@RequestParam(value = "name") String name);

    /**
     * 验证门关联的设备是否启用后台验证
     *
     * <AUTHOR>
     * @since 2014年12月30日 下午2:34:05
     * @return
     */
    @RequestMapping(params = "validBackgroundVerify")
    @ResponseBody
    ZKResultMsg validBackgroundVerify(@RequestParam(value = "doorId") String doorId);

    /**
     * 选门控件
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/12 10:42
     * @return:
     */
    @RequestMapping(params = "selectDoorlist")
    @ResponseBody
    DxGrid selectDoorlist(AccCombOpenSelectDoorItem codition);
}