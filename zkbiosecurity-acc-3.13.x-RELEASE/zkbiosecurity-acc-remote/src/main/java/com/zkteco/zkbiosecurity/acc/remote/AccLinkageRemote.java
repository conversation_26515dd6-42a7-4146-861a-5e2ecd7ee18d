/**
 * File Name: AccLinkage Created by GenerationTools on 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.*;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accLinkage.do")
public interface AccLinkageRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-16 下午04:41
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-16 下午04:41
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-16 下午04:41
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccLinkageItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-16 下午04:41
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccLinkageItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-16 下午04:41
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     *
     * <AUTHOR> 获取联动要过滤的设备id
     * @since 2015年11月11日 下午2:37:22
     * @return
     */
    @RequestMapping(params = "getAllFilterId")
    @ResponseBody
    ZKResultMsg getAllFilterId();

    /**
     * 判断联动条件设置是否重复
     *
     * <AUTHOR> href="mailto:<EMAIL>">yulong.dai</a>
     * @modify by: wenxin 2014-09-17
     * @since 2014年8月25日 上午10:33:27
     * @return String
     */
    @RequestMapping(params = "checkTriggerOpt")
    @ResponseBody
    ZKResultMsg checkTriggerOpt();

    /**
     * 检测是否设置邮箱
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2015年2月6日 上午9:16:04
     * @return String
     */
    @RequestMapping(params = "checkMailParam")
    @ResponseBody
    ZKResultMsg checkMailParam();

    /**
     * 判断设备是否支持锁定功能
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2016年11月15日 下午7:47:56
     */
    @RequestMapping(params = "isSupportLockDoor")
    @ResponseBody
    ZKResultMsg isSupportLockDoor(@RequestParam(value = "devId") String devId);

    /**
     * 判断名称是否有效/允许
     * 
     * @author: mingfa.zheng
     * @date: 2018-02-23 09:51:28
     * @param name
     * @return
     */
    @RequestMapping(params = "valid")
    @ResponseBody
    String valid(@RequestParam(value = "name") String name);

    /**
     * 获取联动触发条件
     *
     * <AUTHOR> href="mailto:<EMAIL>">yulong.dai</a>
     * @since 2014年8月25日 上午10:35:31
     * @return String
     */
    @RequestMapping(params = "getLinkTriggerOpt")
    @ResponseBody
    ZKResultMsg getLinkTriggerOpt();

    /**
     * 获取联动输入点输入出，用于前端页面显示
     *
     * <AUTHOR> href="mailto:<EMAIL>">yulong.dai</a>
     * @since 2014年8月25日 上午10:35:27
     * @return String
     */
    @RequestMapping(params = "getInOutInfo")
    @ResponseBody
    ZKResultMsg getInOutInfo();

    /**
     * 选设备控件
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/16 9:39
     * @return:
     */
    @RequestMapping(params = "selectDevicelist")
    @ResponseBody
    DxGrid selectDevicelist(AccLinkageSelectDeviceItem codition);

    /**
     * 检测是否设置SMS
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-11-12 16:25
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "checkSMSModemParam")
    @ResponseBody
    ZKResultMsg checkSMSModemParam();

    /**
     * 获取Digifort全局事件树
     * 
     * <AUTHOR>
     * @since 2020/7/15 17:21
     * @param linkageId
     * @param type
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "getDigifortGlobalEvents")
    @ResponseBody
    ZKResultMsg getDigifortGlobalEvents(String linkageId, String type);

    /**
     * 获取通讯录列表
     * 
     * @param condition:
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR>
     * @throws
     * @date 2022-02-15 17:26
     * @since 1.0.0
     */
    @RequestMapping(params = "selectLineContactList")
    @ResponseBody
    DxGrid selectLineContactList(AccLinkageSelectContactItem condition);

    /**
     * 获取选入侵分区双列表数据
     *
     * @param condition:
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR>
     * @date 2022-11-30 16:06
     * @since 1.0.0
     */
    @RequestMapping(params = "getSelectIasPartition")
    @ResponseBody
    DxGrid getSelectIasPartition(AccLinkageSelectIasPartitionItem condition);

    /**
     * 根据入侵分区ID，获取入侵分区树结构数据
     *
     * @param iasPartitionIds:
     * @return com.zkteco.zkbiosecurity.base.bean.TreeItem
     * <AUTHOR>
     * @date 2022-11-30 17:25
     * @since 1.0.0
     */
    @RequestMapping(params = "getIasPartitionTreeByIds")
    @ResponseBody
    TreeItem getIasPartitionTreeByIds(String iasPartitionIds);

    /**
     * 根据厂商获取布防类型
     *
     * @param manufacture:入侵模块设备厂商
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2022-12-01 9:25
     * @since 1.0.0
     */
    @RequestMapping(params = "getArmTypeByManufacture")
    @ResponseBody
    ZKResultMsg getArmTypeByManufacture(String manufacture);

    /**
     * 获取vdb_ivr信息
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-04-16 8:44
     * @since 1.0.0
     */
    @ResponseBody
    @RequestMapping(params = "getVdbIvr")
    ZKResultMsg getVdbIvr();

    /**
     * 获取ivr要通知的对象信息
     *
     * @param condition:
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     * <AUTHOR>
     * @throws
     * @date 2024-04-16 10:15
     * @since 1.0.0
     */
    @RequestMapping(params = "selectVdbExtensionList")
    @ResponseBody
    DxGrid selectVdbExtensionList(AccLinkageSelectExtensionItem condition);

}