/**
 * File Name: AccHoliday Created by GenerationTools on 2018-02-26 下午05:53 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccHolidayItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accHoliday.do")
public interface AccHolidayRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-26 下午05:53
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-26 下午05:53
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-02-26 下午05:53
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccHolidayItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-26 下午05:53
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccHolidayItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-02-26 下午05:53
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * @Description: 判断节假日名称是否重复
     * <AUTHOR>
     * @date 2018/5/8 14:56
     * @param name
     *            节假日名称
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    String isExist(@RequestParam(value = "name") String name);

    /**
     * @Description: 校验节假日各参数信息
     * <AUTHOR>
     * @date 2018/5/25 10:51
     * @param accHolidayItem
     *            节假日vo
     * @return
     */
    @RequestMapping(params = "dataValid")
    @ResponseBody
    ZKResultMsg dataValid(AccHolidayItem accHolidayItem);
}