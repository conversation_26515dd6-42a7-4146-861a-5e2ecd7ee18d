/**
 * File Name: AccDeviceEvent Created by GenerationTools on 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import java.lang.reflect.InvocationTargetException;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorHistoryItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accAlarmMonitor.do")
public interface AccAlarmMonitorRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午02:44
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 清除全部命令
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午02:44
     * @param key
     * @return
     */
    @RequestMapping(params = "ackAlarm")
    @ResponseBody
    ZKResultMsg ackAlarm(@RequestParam(value = "key") String key);

    /**
     * 有客户的取消暂停，需要接受消息。
     * 
     * @return
     */
    @RequestMapping(params = "newMessages")
    @ResponseBody
    ZKResultMsg receiveNewMessagesRequest();

    /**
     * 获取事件编号之后发生的数据
     * 
     * @param filters:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-06-16 17:48
     * @since 1.0.0
     */
    @RequestMapping(params = "getDataByEventNum")
    @ResponseBody
    ZKResultMsg getDataByEventNum(@RequestParam Map filters);

    /**
     * 初次进入页面请求60条数据
     * 
     * @param filters:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-06-16 17:49
     * @since 1.0.0
     */
    @RequestMapping(params = "getFirstPage")
    @ResponseBody
    ZKResultMsg getFirstPage(@RequestParam Map filters);

    /**
     * 通过页码获取数据
     * 
     * @param filters:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-06-16 17:49
     * @since 1.0.0
     */
    @RequestMapping(params = "getInfoByPageNo")
    @ResponseBody
    ZKResultMsg getInfoByPageNo(@RequestParam Map filters);

    /**
     * 修改状态的页面
     *
     * @param id
     * @return
     */
    @RequestMapping(params = "editAcknowledged")
    ModelAndView editAcknowledged(String id);

    /**
     * 修改状态
     *
     * @param id
     * @return
     */
    @RequestMapping(params = "editStatus")
    @ResponseBody
    ZKResultMsg editStatus(String id, Short status, String acknowledgement, Boolean sendEmail, String emails);

    /**
     * 查看历史
     *
     * @param id
     * @return
     */
    @RequestMapping(params = "showHistory")
    ModelAndView showHistory(String id);

    /**
     * 处理历史的报表
     *
     * @return
     */
    @RequestMapping(params = "history")
    ModelAndView history();

    /**
     * 获取历史列表
     *
     * @param condition
     * @return
     */
    @RequestMapping(params = "historyList")
    @ResponseBody
    DxGrid historyList(AccAlarmMonitorHistoryItem condition);

    /**
     * 清除历史
     *
     * @return
     */
    @RequestMapping(params = "clearHistoryData")
    @ResponseBody
    ZKResultMsg clearHistoryData();

    /**
     * 导出历史
     *
     * @param request
     * @param response
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    @RequestMapping(params = "exportHistory")
    @ResponseBody
    void exportHistory(HttpServletRequest request, HttpServletResponse response)
        throws InvocationTargetException, IllegalAccessException;
}