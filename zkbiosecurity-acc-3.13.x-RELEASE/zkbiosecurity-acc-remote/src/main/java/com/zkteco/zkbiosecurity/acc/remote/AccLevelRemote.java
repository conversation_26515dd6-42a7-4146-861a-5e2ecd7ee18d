/**
 * File Name: AccLevel Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccLevelDoorItem;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import com.zkteco.zkbiosecurity.acc.vo.AccLevelSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accLevel.do")
public interface AccLevelRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccLevelItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccLevelItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-02 下午02:15
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(String ids);

    /**
     * 获取权限组下拉框
     * 
     * <AUTHOR>
     * @since 2018/4/17 10:47
     * @return
     */
    @RequestMapping(params = "getLevelList")
    @ResponseBody
    ZKResultMsg getLevelList();

    /**
     * 权限组： 添加门控件
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/16 19:15
     * @return:
     */
    @RequestMapping(params = "selectDoorlist")
    @ResponseBody
    DxGrid selectDoorlist(AccLevelSelectDoorItem codition);

    /**
     * 权限组：添加门
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/23 16:09
     * @return:
     */
    @RequestMapping(params = "addDoor")
    @ResponseBody
    ZKResultMsg addDoor(@RequestParam(value = "levelId") String levelId,
        @RequestParam(value = "doorIds") String doorIds);

    /**
     * 权限组：删除门
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/23 16:09
     * @return:
     */
    @RequestMapping(params = "delDoor")
    @ResponseBody
    ZKResultMsg delDoor(@RequestParam(value = "levelId") String levelId,
        @RequestParam(value = "doorIds") String doorIds);

    /**
     * 获取右列表门的list数据
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/23 15:55
     * @return:
     */
    @RequestMapping(params = "doorList")
    @ResponseBody
    DxGrid doorList(AccLevelDoorItem codition);

    /**
     * 判断名称是否有效/允许
     * 
     * @author: mingfa.zheng
     * @date: 2018-02-23 09:51:28
     * @param name
     * @return
     */
    @RequestMapping(params = "valid")
    @ResponseBody
    String valid(@RequestParam(value = "name") String name);

    /**
     * @Description: 导出权限组
     * <AUTHOR>
     * @date 2018/5/14 10:09
     * @return
     */
    @RequestMapping(params = "export")
    @ResponseBody
    void export(HttpServletRequest request, HttpServletResponse response);

    @RequestMapping(params = "getPersonCount")
    @ResponseBody
    ZKResultMsg getPersonCount(@RequestParam(value = "levelId") String levelId);

    /**
     * @Description: 判断权限组是否可以删除
     *
     * @author: mingfa.zheng
     * @date: 2018/8/9 19:13
     * @param: [ids]
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     **/
    @RequestMapping(params = "levelIsDelete")
    @ResponseBody
    ZKResultMsg levelIsDelete(@RequestParam(value = "ids") String ids);

    /**
     * 手机App用，根据条件查询用户能开的门的集合
     * 
     * <AUTHOR>
     * @param id 用户id
     * @return
     */
    @RequestMapping(params = "doorListByApp")
    @ResponseBody
    ZKResultMsg doorListByApp(String id);

    /**
     * 远程开门
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:27
     * @param ids
     * @param openInterval
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "openDoor")
    @ResponseBody
    ZKResultMsg openDoor(@RequestParam(value = "ids") String ids,
        @RequestParam(value = "openInterval") String openInterval);

    /**
     * 远程关门
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:27
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "closeDoor")
    @ResponseBody
    ZKResultMsg closeDoor(@RequestParam(value = "ids") String ids);

    /**
     * 取消报警
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:28
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "cancelAlarmDoor")
    @ResponseBody
    ZKResultMsg cancelAlarmDoor(@RequestParam(value = "ids") String ids);

    /**
     * 远程常开
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:28
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "normalOpenDoor")
    @ResponseBody
    ZKResultMsg normalOpenDoor(@RequestParam(value = "ids") String ids);

    /**
     * 启用门
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:32
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "enableDoor")
    @ResponseBody
    ZKResultMsg enableDoor(@RequestParam(value = "ids") String ids);

    /**
     * 禁用门
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:32
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "disableDoor")
    @ResponseBody
    ZKResultMsg disableDoor(@RequestParam(value = "ids") String ids);

    /**
     * 远程锁定
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:28
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "lockDoor")
    @ResponseBody
    ZKResultMsg lockDoor(@RequestParam(value = "ids") String ids);

    /**
     * 远程解锁
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:28
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "unLockDoor")
    @ResponseBody
    ZKResultMsg unLockDoor(@RequestParam(value = "ids") String ids);

    /**
     * 启用当天常开时间段
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:29
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "enableNormalOpenDoor")
    @ResponseBody
    ZKResultMsg enableNormalOpenDoor(@RequestParam(value = "ids") String ids);

    /**
     * 禁用当天常开时间段
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-10 15:29
     * @param ids
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "disableNormalOpenDoor")
    @ResponseBody
    ZKResultMsg disableNormalOpenDoor(@RequestParam(value = "ids") String ids);

    /**
     * 授权设备添加权限组时数量限制
     * 
     * @param id
     * @return
     */
    @RequestMapping(params = "authDevAddLevelVerify")
    @ResponseBody
    ZKResultMsg authDevAddLevelVerify(String id);

    /**
     * @Description: 导出权限组下的门
     * <AUTHOR>
     * @date 2018/5/14 10:09
     * @return
     */
    @RequestMapping(params = "exportLevelDoor")
    @ResponseBody
    void exportLevelDoor(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导入权限组信息
     *
     * @param upload:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-07-20 14:53
     * @since 1.0.0
     */
    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(@RequestParam("upload") MultipartFile upload) throws IOException;

    /**
     * 导入权限组门信息
     *
     * @param upload:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-07-20 14:53
     * @since 1.0.0
     */
    @RequestMapping(params = "importLevelDoor")
    @ResponseBody
    ZKResultMsg importLevelDoorExcel(@RequestParam("upload") MultipartFile upload) throws IOException;

}