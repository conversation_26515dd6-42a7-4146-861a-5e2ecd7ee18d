package com.zkteco.zkbiosecurity.acc.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccAlarmMonitorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

/**
 * 报警记录
 * 
 * <AUTHOR>
 * @date Created In 11:28 2020/3/26
 */
@RequestMapping("/accAlarmMonitorReport.do")
public interface AccAlarmMonitorReportRemote {

    /**
     * 首页
     * 
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表
     * 
     * @param accAlarmMonitorItem
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccAlarmMonitorItem accAlarmMonitorItem);

    /**
     * 导出
     * 
     * @param request
     * @param response
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 报警事件处理：处理或者确认报警
     * 
     * @param ids:
     * @return org.springframework.web.servlet.ModelAndView
     * <AUTHOR>
     * @throws
     * @date 2022-09-21 18:20
     * @since 1.0.0
     */
    @RequestMapping(params = "editAcknowledged")
    ModelAndView editAcknowledged(@RequestParam(value = "ids", required = false) String ids);

}
