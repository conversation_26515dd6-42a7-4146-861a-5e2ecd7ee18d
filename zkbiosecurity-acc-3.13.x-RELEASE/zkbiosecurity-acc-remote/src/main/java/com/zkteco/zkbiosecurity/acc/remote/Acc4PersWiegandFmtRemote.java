/**
 * File Name: PersWiegandFmt Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accPersWiegandFmt.do")
public interface Acc4PersWiegandFmtRemote {
    /**
     * 开启韦根测试的功能
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/27 11:03
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "start")
    @ResponseBody
    ZKResultMsg start(@RequestParam("deviceId") String deviceId);

    /**
     * 卡格式测试-读卡
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/27 11:08
     * @param deviceId
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "readerCard")
    @ResponseBody
    ZKResultMsg readerCard(@RequestParam("deviceId") String deviceId);

    /**
     * 卡格式测试-推荐格式
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/27 11:11
     * @param deviceId
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "recommendFmt")
    @ResponseBody
    ZKResultMsg recommendFmt(@RequestParam("deviceId") String deviceId, @RequestParam("sizeCode") String sizeCode,
        @RequestParam("cardNo") String cardNo, @RequestParam("orgCardNo") String orgCardNo,
        @RequestParam("bitscount") String bitscount, @RequestParam("withSizeCode") String withSizeCode);

    /**
     * 卡格式测试-过滤不支持韦根测试的设备
     * 
     * @return
     */
    @RequestMapping(params = "getAllFilterId")
    @ResponseBody
    ZKResultMsg getAllFilterId();

    /**
     * 卡格式测试-结束韦根测试的功能
     * 
     * @param deviceId
     * @return
     */
    @RequestMapping(params = "stop")
    @ResponseBody
    ZKResultMsg stop(String deviceId);
}