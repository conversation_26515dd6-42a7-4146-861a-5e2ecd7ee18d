package com.zkteco.zkbiosecurity.acc.remote;

import java.lang.reflect.InvocationTargetException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccAlarmTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accAlarmTransaction.do")
public interface AccAlarmTransactionRemote {
    /**
     * 默认页面跳转
     * 
     * @author: yibiao.shen
     * @date:2018-03-09 14:51:23
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     * 
     * @author: yibiao.shen
     * @date:2018-03-09 14:52:23
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccAlarmTransactionItem condition);

    /**
     * 级联删除数据
     * 
     * @author: yibiao.shen
     * @date:2018-03-09 14:53:23
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 删除所有异常数据
     * 
     * @author: verber
     * @date: 2018-04-28 15:13
     * @return
     */
    @RequestMapping(params = "clearData")
    @ResponseBody
    ZKResultMsg clearData();

    /**
     * 导出数据
     * 
     * @author: verber
     * @date: 2018-05-03 10:02
     * @return
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response)
        throws InvocationTargetException, IllegalAccessException;
}
