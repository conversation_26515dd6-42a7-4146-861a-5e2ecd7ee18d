package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.acc.vo.AccMapSelectChannelItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;

@RequestMapping(value = "/accMapSelectChannel.do")
public interface AccMapSelectChannelRemote {

    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccMapSelectChannelItem codition);
}
