/**
 * File Name: AccVerifyModeRule Created by GenerationTools on 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccDoorVerifyModeRuleItem;
import com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRuleItem;
import com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRuleSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accVerifyModeRule.do")
public interface AccVerifyModeRuleRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(AccVerifyModeRuleItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccVerifyModeRuleItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam(value = "ids") String ids);

    /**
     * 判断名称是否有效/允许
     * 
     * @author: mingfa.zheng
     * @date: 2018-02-23 09:51:28
     * @param name
     * @return
     */
    @RequestMapping(params = "valid")
    @ResponseBody
    String valid(@RequestParam(value = "name") String name);

    /**
     * 获取还未绑定验证方式规则的时间段
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2016年9月23日 上午9:14:49
     * @return
     */
    @RequestMapping(params = "getTimeSegJSONWithoutRule")
    @ResponseBody
    ZKResultMsg getTimeSegJSONWithoutRule(@RequestParam(value = "id") String id);

    /**
     * 获取时间段对象，用于前端加载
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2016年9月23日 下午2:46:21
     * @return
     */
    @RequestMapping(params = "getTimeSegById")
    @ResponseBody
    ZKResultMsg getTimeSegById(@RequestParam(value = "id") String id);

    /***
     * 获取系统中支持该功能的设备的验证方式列表
     *
     * <AUTHOR> href=mailto:<EMAIL>>yulong.dai</a>
     * @since 2016年10月14日 上午8:58:47
     * @return
     */
    @RequestMapping(params = "getVerifyMode")
    @ResponseBody
    ZKResultMsg getVerifyMode();

    /**
     * 获取右列表门的list数据
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/23 15:55
     * @return:
     */
    @RequestMapping(params = "doorList")
    @ResponseBody
    DxGrid doorList(AccDoorVerifyModeRuleItem codition);

    /**
     * 验证方式规则：添加门
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/23 16:09
     * @return:
     */
    @RequestMapping(params = "addDoor")
    @ResponseBody
    ZKResultMsg addDoor(@RequestParam(value = "verifyModeRuleId") String verifyModeRuleId,
        @RequestParam(value = "doorIds") String doorIds);

    /**
     * 验证方式规则：删除门
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/23 16:09
     * @return:
     */
    @RequestMapping(params = "delDoor")
    @ResponseBody
    ZKResultMsg delDoor(@RequestParam(value = "verifyModeRuleId") String verifyModeRuleId,
        @RequestParam(value = "doorIds") String doorIds);

    /**
     * 验证方式规则： 添加门控件
     * 
     * @author: mingfa.zheng
     * @date: 2018/4/16 19:15
     * @return:
     */
    @RequestMapping(params = "selectDoorlist")
    @ResponseBody
    DxGrid selectDoorlist(AccVerifyModeRuleSelectDoorItem codition);

    /**
     * 获取未设置验证方式规则的时间段下拉列表数据
     *
     * @param id: 时间段id
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2021-07-23 15:51
     * @since 1.0.0
     */
    @RequestMapping(params = "getTimeSegListWithoutRule")
    @ResponseBody
    ZKResultMsg getTimeSegListWithoutRule(@RequestParam(value = "id") String id);
}