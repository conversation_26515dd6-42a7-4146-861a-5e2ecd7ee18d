package com.zkteco.zkbiosecurity.acc.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.acc.vo.AccPersonVerifyModeRuleItem;
import com.zkteco.zkbiosecurity.acc.vo.AccVerifyModeRulePersonGroupItem;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "/accVerifyModeRulePersonGroup.do")
public interface AccVerifyModeRulePersonGroupRemote {

    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-03-14 下午03:02
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 验证方式规则人员组列表数据获取
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/15 16:40
     * @return:
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(AccVerifyModeRulePersonGroupItem codition);

    /**
     * 验证方式规则人员组: 添加人员
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/21 16:37
     * @return:
     */
    @RequestMapping(params = "addPerson")
    @ResponseBody
    ZKResultMsg addPerson(@RequestParam(value = "verifyModeRulePersonGroupId") String verifyModeRulePersonGroupId,
        @RequestParam(value = "personIds") String personIds, @RequestParam(value = "deptIds") String deptIds);

    /**
     * 验证方式规则人员组: 删除人员
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/23 16:18
     * @return:
     */
    @RequestMapping(params = "delPerson")
    @ResponseBody
    ZKResultMsg delPerson(@RequestParam(value = "verifyModeRulePersonGroupId") String verifyModeRulePersonGroupId,
        @RequestParam("personIds") String personIds);

    /**
     * 获取右列表人员的list数据
     * 
     * @author: mingfa.zheng
     * @date: 2018/3/23 16:23
     * @return:
     */
    @RequestMapping(params = "personList")
    @ResponseBody
    DxGrid personList(AccPersonVerifyModeRuleItem condition);
}
