package com.zkteco.zkbiosecurity.acc.client;

import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.service.AccLevelService;
import com.zkteco.zkbiosecurity.acc.utils.AccUploadPageUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseClientService;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class AccPersonLevelHandle {

    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private BaseLicenseClientService baseLicenseClientService;
    @Autowired
    private AccLevelService accLevelService;

    /**
     * 定时推送人员权限到云端
     * @return
     */
//    @Scheduled(cron = "0 0 0/1 * * ?")//每隔一分钟，业务可以自己控制
    public ZKResultMsg accPersonLevelUpload(){
        //判断是否注册了云服务，注册才发送。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId())){
            ZKMessage zkMessage = new ZKMessage();

            //平台分发处理  必须设置模块码和下列形式的消息id
            zkMessage.setModuleCode("acc");
            zkMessage.setMessageId("accCloudPersonLevelHandle#handlePersonLevelMessage"); //发送消息到云平台。
            zkMessage.setAppId(baseLicenseService.getAppId());

            Long doorCount = accDoorService.getDoorCount();
            int pageNo = AccUploadPageUtil.getPage(doorCount);
            for (int i=0; i<pageNo; i++){
                List<AccDoorItem> accDoorItems = accDoorService.getUploadCloudDoor(i, AccUploadPageUtil.pageSize);
                if (accDoorItems != null && accDoorItems.size() > 0){
                    accDoorItems.forEach(accDoorItem -> {
                        List<String> personLevelList = accDoorService.getUploadCloudPersonLevel(accDoorItem);
                        List<List<String>> personLevelsList = CollectionUtil.split(personLevelList,AccUploadPageUtil.pageSize);
                        personLevelsList.forEach(personLevels -> {
                            zkMessage.setListContent(personLevels);
                            baseLicenseClientService.sendMessage(zkMessage);
                        });
                    });
                }
            }

        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 从二号项目迁移代码：获取人员有权限的门列表
     * <AUTHOR>
     * @date 2019/11/8 9:47
     * @param message
     * @return
     */
    public ZKResultMsg getPersonLevelDoor(ZKMessage message) {
        ZKResultMsg rs = null;
        try {
            Map<String, Object> dataMap = message.getContent();
            String personPin = MapUtils.getString(dataMap, "personPin");
            boolean isAdmin = MapUtils.getBoolean(dataMap, "isAdmin", false);
            rs = accLevelService.getPersonLevelDoor(personPin, isAdmin);
        }
        catch(Exception e) {
            rs = ZKResultMsg.failMsg();
        }
        return I18nUtil.i18nMsg(rs);
    }
}
