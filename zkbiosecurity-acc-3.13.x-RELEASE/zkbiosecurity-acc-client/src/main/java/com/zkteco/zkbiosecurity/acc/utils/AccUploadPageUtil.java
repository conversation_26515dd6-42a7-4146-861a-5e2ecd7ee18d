/*
 * File Name: AccUploadPageUtil
 * <NAME_EMAIL> on 2018/9/4 15:54.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.utils;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
public class AccUploadPageUtil {

    public static final int pageSize = 30;

    /**
     * @Description: 根据数量获取分页页数
     * <AUTHOR>
     * @date 2018/9/4 20:13
     * @param count
     * @return
     */
    public static int getPage(long count) {
        int page = (int) (count / pageSize);
        if (count % pageSize > 0) {
            page += 1;
        }
        return page;
    }
}
