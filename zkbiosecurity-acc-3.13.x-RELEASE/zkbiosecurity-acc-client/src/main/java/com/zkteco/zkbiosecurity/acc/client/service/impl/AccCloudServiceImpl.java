package com.zkteco.zkbiosecurity.acc.client.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccCloudService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.utils.AccUploadPageUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseClientService;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import com.zkteco.zkbiosecurity.system.app.service.BaseAuthCloudMessageSendService;
import com.zkteco.zkbiosecurity.system.service.BaseMessageService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseMessageItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @Date 2019/1/16 14:21
 **/
@Service
public class AccCloudServiceImpl implements AccCloudService {

    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private BaseLicenseClientService baseLicenseClientService;
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BaseAuthCloudMessageSendService baseAuthCloudMessageSendService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired(required = false)
    private BaseMessageService baseMessageService;

    @Override
    public void asyncPushTransactionToCloud(List<AccTransactionItem> accTransactionItemList) {
        //判断是否注册了云服务且激活了当前模块许可，才发送。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId()) && isActiveLicense() && !CollectionUtil.isEmpty(accTransactionItemList)){
            //异步发送数据，避免kafka调用超时影响正常业务
            CompletableFuture.runAsync(() -> {
                //对事件进行分组，30条一组
                List<List<AccTransactionItem>> accTransactionItemsList = CollectionUtil.split(accTransactionItemList, AccUploadPageUtil.pageSize);
                ZKMessage zkMessage = new ZKMessage();
                zkMessage.setModuleCode("acc");
                zkMessage.setMessageId("accCloudTransactionHandle#getCloudTransaction");//bean名称+ # +方法名   云平台业务代码需要写对应的bean和方法接收。
                zkMessage.setAppId(baseLicenseService.getAppId());
                accTransactionItemsList.forEach(accTransactionItems -> {
                    for (AccTransactionItem accTransactionItem : accTransactionItems) {
                        accTransactionItem.setVerifyModeName(I18nUtil.i18nCode(accTransactionItem.getVerifyModeName()));
                        accTransactionItem.setEventName(I18nUtil.i18nCode(accTransactionItem.getEventName()));
                    }
                    zkMessage.setListContent(accTransactionItems);
//                    kafkaTemplate.send(zkMessage.getModuleCode(), zkMessage);
                    baseLicenseClientService.sendMessage(zkMessage);
                });
            });
        }
    }

    @Override
    public void asyncDevStatusToCloud(String sn) {
        //判断是否注册了云服务，激活了当前模块许可，且设备数据已经推送过，才更新设备状态。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId()) && isActiveLicense() && StringUtils.isNotBlank(sn)
                && StringUtils.isNotBlank(baseSysParamService.getValByName(AccConstants.ACC_DEVICE_LAST_PUSH_TIME))){
            //异步发送数据，避免kafka调用超时影响正常业务
            CompletableFuture.runAsync(() -> {
                ZKMessage message = new ZKMessage();
                message.setModuleCode("acc");
                message.setMessageId("accCloudDeviceInfoHandle#asyncDevStatus");//bean名称+ # +方法名   云平台业务代码需要写对应的bean和方法接收。
                Map<String, Object> dataMap = new HashMap<>();
                String status = accDeviceService.getStatus(sn);
                dataMap.put("sn", sn);
                dataMap.put("status", status);
                message.setContent(dataMap);
                baseLicenseClientService.sendMessage(message);
            });
        }
    }

    @Override
    public void deleteCloudDevice(List<String> devSnList) {
        //判断是否注册了云服务，且设备数据已经推送过，才更新设备状态。
        if (baseAuthCloudMessageSendService.isAllowSendBusinessData() && isActiveLicense() && !CollectionUtil.isEmpty(devSnList) && accDeviceService.getLastPushTime() != null){
            //异步发送数据，避免kafka调用超时影响正常业务
            CompletableFuture.runAsync(() -> {
                ZKMessage message = new ZKMessage();
                message.setModuleCode("acc");
                message.setMessageId("accCloudDeviceInfoHandle#deleteDeviceBySn");//bean名称+ # +方法名   云平台业务代码需要写对应的bean和方法接收。
                message.setListContent(devSnList);
                baseLicenseClientService.sendMessage(message);
            });
        }
    }

    @Override
    public boolean isActiveLicense() {
        List<String> activeModuleCodeList = baseLicenseProvider.getActiveModuleLIist();
        if (activeModuleCodeList.contains(ConstUtil.LICENSE_MODULE_ACC_PUSH) || activeModuleCodeList.contains(ConstUtil.LICENSE_MODULE_ACC_PULL)) {
            return true;
        }
        return false;
    }

    @Override
    public void sendAlarmMsgToWxMiniPrograms(List<AccTransactionItem> accTransactionItemList) {
        //判断是否注册了云服务且激活了当前模块许可，才发送。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId()) && isActiveLicense() && !CollectionUtil.isEmpty(accTransactionItemList)) {
            //异步发送数据
            CompletableFuture.runAsync(() -> {
                accTransactionItemList.forEach(accTransactionItem -> {
                    if (AccConstants.ALARM_MSG_EVENT_NO_LIST.contains(accTransactionItem.getEventNo())) {
                        this.sendAlarmMsg(accTransactionItem);
                    }
                });
            });
        }
    }

    /**
     * 推送门禁报警消息到微信小程序
     *
     * <AUTHOR>
     * @date 2021-10-14 10:48
     * @param accTransactionItem
     * @since 1.0.0
     * @return void
     */
    private void sendAlarmMsg(AccTransactionItem accTransactionItem) {
        BaseMessageItem baseMessageItem = new BaseMessageItem();
        JSONObject extendJson = new JSONObject();
        String personInfo = "";
        baseMessageItem.setBusinessId(accTransactionItem.getId());
        baseMessageItem.setBusinessCode("AccWarn");
        baseMessageItem.setTitle(I18nUtil.i18nCode(accTransactionItem.getEventName()));
        baseMessageItem.setStatus("0");
        baseMessageItem.setType("WarnMsg");
        if (StringUtils.isNotBlank(accTransactionItem.getPin())) {
            personInfo = String.format("%s(%s)", accTransactionItem.getName(), accTransactionItem.getPin());
            baseMessageItem.setReceiverId(accTransactionItem.getPin());
        }

        baseMessageItem.setRemindTime(new Date());
        extendJson.put("personInfo", personInfo);
        extendJson.put("warnType", String.valueOf(accTransactionItem.getEventNo()));
        extendJson.put("warnContent1", accTransactionItem.getDevAlias());
        extendJson.put("warnContent2", DateUtil.dateToString(accTransactionItem.getEventTime(), DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
        baseMessageItem.setExtendJson(extendJson.toJSONString());
        if (Objects.nonNull(baseMessageService)) {
            this.baseMessageService.saveAndSendToCloud(baseMessageItem);
        }
    }
}
