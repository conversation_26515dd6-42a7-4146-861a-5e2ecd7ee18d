package com.zkteco.zkbiosecurity.acc.client;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.acc.service.AccCloudService;
import com.zkteco.zkbiosecurity.acc.service.AccDeviceService;
import com.zkteco.zkbiosecurity.acc.service.AccDoorService;
import com.zkteco.zkbiosecurity.acc.utils.AccUploadPageUtil;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseClientService;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import com.zkteco.zkbiosecurity.system.app.service.BaseAuthCloudMessageSendService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Component
public class AccDeviceHandle {
    @Autowired
    private AccDeviceService accDeviceService;
    @Autowired
    private AccDoorService accDoorService;
    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private BaseLicenseClientService baseLicenseClientService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private BaseAuthCloudMessageSendService baseAuthCloudMessageSendService;
    @Autowired
    private AccCloudService accCloudService;

    /**
     * @Description: 定时推送设备数据
     * <AUTHOR>
     * @date 2018/9/5 11:39
     * @return
     */
    @Scheduled(cron = "0 0/5 * * * ?")//每隔一分钟，业务可以自己控制
    public ZKResultMsg accDeviceUpload() {
        //判断是否注册了云服务且是否激活了当前模块许可，注册才发送。
        if (baseAuthCloudMessageSendService.isAllowSendBusinessData() && accCloudService.isActiveLicense()){
            Long pushTime = accDeviceService.getLastPushTime();
            ZKMessage zkMessage = new ZKMessage();
            zkMessage.setModuleCode("acc");//模块代码如acc，pers
            zkMessage.setMessageId("accCloudDeviceInfoHandle#getCloudDevice");//bean名称+ # +方法名   云平台业务代码需要写对应的bean和方法接收。
            zkMessage.setAppId(baseLicenseService.getAppId());
            if (pushTime == null) {
                long count = accDeviceService.getDevicesCount();
                if (count > 0) {
                    int page = AccUploadPageUtil.getPage(count);//获取分页页数
                    for (int i = 0; i < page; i ++) {
                        List<String> devInfoList = accDeviceService.getUploadCloudDevices(i, AccUploadPageUtil.pageSize);//分页获取数据，限制消息体大小
                        zkMessage.setListContent(devInfoList);
//                    kafkaTemplate.send(zkMessage.getModuleCode(), zkMessage);
                        baseLicenseClientService.sendMessage(zkMessage);
                    }
                }
            } else {
                Date lastUpdate = new Date(pushTime);
                lastUpdate = DateUtil.addMinute(lastUpdate, -1);
                List<String> devInfoList = accDeviceService.getUploadCloudDevicesByLastPushTime(lastUpdate);//分页获取数据，限制消息体大小
                if (devInfoList != null && !devInfoList.isEmpty()) {
                    List<List<String>> devInfos = CollectionUtil.split(devInfoList, AccUploadPageUtil.pageSize);
                    for (List<String> devInfo : devInfos) {
                        zkMessage.setListContent(devInfo);
                        //kafka异步发送
                        baseLicenseClientService.sendMessage(zkMessage);
                    }
                }
            }
            accDeviceService.setLastPushTime();
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * @Description: 云服务调用远程开门方法
     * <AUTHOR>
     * @date 2018/9/5 11:29
     * @param zkMessage
     * @return
     */
    public ZKResultMsg cloudOpenDoor(ZKMessage zkMessage) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        Map map = zkMessage.getContent();
        String openInterval = MapUtils.getString(map, "openInterval");
        String operator = MapUtils.getString(map, "operator");
        boolean isAdmin = MapUtils.getBoolean(map, "isAdmin", false);
        Map<String, List<Short>> doorMap = (Map<String, List<Short>>) map.get("doors");
        if (doorMap != null && !doorMap.isEmpty()) {
            for(String sn : doorMap.keySet()) {
                if (doorMap.get(sn) != null && !doorMap.get(sn).isEmpty()) {
                    String doorNos = StringUtils.join(doorMap.get(sn), ",");
                    List<Short> doorNoList = Arrays.stream(doorNos.split(",")).map(s-> Short.parseShort(s.trim())).collect(Collectors.toList());
                    String doorIds = accDoorService.getDoorByDevSnAndDoorNos(sn, doorNoList);
                    if (StringUtils.isNotBlank(doorIds)) {
                        resultMsg = accDoorService.operateDoor("openDoor", openInterval, doorIds, operator, isAdmin);
                    }
                }
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * @Description: 云服务调用取消报警方法
     * <AUTHOR>
     * @date 2018/9/5 11:29
     * @param zkMessage
     * @return
     */
    public ZKResultMsg cloudCancelAlarm(ZKMessage zkMessage) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        Map map = zkMessage.getContent();
        String openInterval = MapUtils.getString(map, "openInterval");
        String operator = MapUtils.getString(map, "operator");
        boolean isAdmin = MapUtils.getBoolean(map, "isAdmin", false);
        Map<String, List<Short>> doorMap = (Map<String, List<Short>>) map.get("doors");
        if (doorMap != null && !doorMap.isEmpty()) {
            for(String sn : doorMap.keySet()) {
                if (doorMap.get(sn) != null && !doorMap.get(sn).isEmpty()) {
                    String doorNos = StringUtils.join(doorMap.get(sn), ",");
                    List<Short> doorNoList = Arrays.stream(doorNos.split(",")).map(s-> Short.parseShort(s.trim())).collect(Collectors.toList());
                    String doorIds = accDoorService.getDoorByDevSnAndDoorNos(sn, doorNoList);
                    if (StringUtils.isNotBlank(doorIds)) {
                        resultMsg = accDoorService.operateDoor("cancelAlarm", openInterval, doorIds, operator, isAdmin);
                    }
                }
            }
        }
        return I18nUtil.i18nMsg(resultMsg);
    }
}
