/*
 * File Name: AccTransactionUploadHandle
 * <NAME_EMAIL> on 2018/9/4 11:37.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.client;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.acc.service.AccTransactionService;
import com.zkteco.zkbiosecurity.acc.utils.AccUploadPageUtil;
import com.zkteco.zkbiosecurity.acc.vo.AccTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseClientService;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Component
public class AccTransactionHandle {

    @Autowired
    private AccTransactionService accTransactionService;
    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private BaseLicenseClientService baseLicenseClientService;

    /**
     * @Description: 定时推送门禁事件记录
     * <AUTHOR>
     * @date 2018/9/5 11:47
     * @return
     */
//    @Scheduled(cron = "0 0 0/1 * * ?")//每隔五分钟，业务可以自己控制
    public ZKResultMsg accTransactionUpload() {
        //判断是否注册了云服务，注册才发送。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId())){
            //取出所有事件记录全量同步
            long count = accTransactionService.getTransactionsCount();
            if (count > 0) {
                int page = AccUploadPageUtil.getPage(count);
                ZKMessage zkMessage = new ZKMessage();
                zkMessage.setModuleCode("acc");
                zkMessage.setMessageId("accCloudTransactionHandle#getCloudTransaction");//bean名称+ # +方法名   云平台业务代码需要写对应的bean和方法接收。
                zkMessage.setAppId(baseLicenseService.getAppId());
                for (int i = 0; i < page; i ++) {
                    Pager pager = accTransactionService.getItemsByPage(new AccTransactionItem(), i, AccUploadPageUtil.pageSize);
                    if (pager.getData() != null && pager.getData().size() > 0) {
                        List<AccTransactionItem> accTransactionItemList = (List<AccTransactionItem>) pager.getData();
                        zkMessage.setListContent(accTransactionItemList);
//                        kafkaTemplate.send(zkMessage.getModuleCode(), zkMessage);
                        baseLicenseClientService.sendMessage(zkMessage);
                    }
                }
            }
        }
        return ZKResultMsg.successMsg();
    }
}
