package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 读头缓存信息
 * 
 * <AUTHOR>
 * @date 2021-7-7 10:01:23
 * @since 1.0.0
 **/
@From(after = "ACC_READER t")
@OrderBy(after = "t.READER_NO ASC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccQueryReaderItem extends BaseItem {

    /** id */
    @Column(name = "t.ID")
    private String id;

    /** 名称 */
    @Column(name = "t.NAME")
    private String name;

    /** 读头编号 */
    @Column(name = "t.READER_NO")
    private Short readerNo;

    /** 读头出入状态 */
    @Column(name = "t.READER_STATE")
    private Short readerState;
}