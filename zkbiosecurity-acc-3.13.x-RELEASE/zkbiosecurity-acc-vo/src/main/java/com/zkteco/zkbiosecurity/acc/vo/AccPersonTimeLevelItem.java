package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class AccPersonTimeLevelItem implements Serializable {

    private String levelId;

    private String startTime;

    private String endTime;

    private Date startTimeDate;

    private Date endTimeDate;

    private String personId;

}
