/**
 * @author: GenerationTools
 * @date: 2018-03-13 下午05:00 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@From(after = "ACC_AUXIN t " + "LEFT JOIN ACC_DEVICE ad ON ad.ID = t.DEV_ID LEFT JOIN ACC_TIMESEG at ON at.ID = t.TIMESEG_ID ")
@OrderBy(after = "ad.BUSINESS_ID ASC, t.AUX_NO ASC")
@GridConfig(operate = true, idField = "id", winHeight = 350, winWidth = 420,
    operates = {
        @GridOperate(type = "edit", permission = "acc:auxIn:edit", url = "accAuxIn.do?edit", label = "common_op_edit"),
        @GridOperate(type = "custom", permission = "acc:auxIn:bindChannel", filter = "accShowBindChannel",
            click = "accAuxInBindChannel", label = "common_vid_bindOrUnbindChannel")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccAuxInItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", show = false)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_name", width = "150", columnType = "edit", editUrl = "/accAuxIn.do?edit",
        editPermission = "acc:auxIn:edit")
    private String name;

    /**  */
    @Column(name = "t.DEV_ID")
    @GridColumn(label = "acc_auxIn_device", show = false)
    private String devId;

    /** 设备名称 */
    @Column(name = "ad.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "150")
    private String devAlias;

    /** 设备sn */
    @Column(name = "ad.SN")
    private String devSn;

    /**  */
    @Column(name = "t.AUX_NO")
    @GridColumn(label = "common_number")
    private Short auxNo;

    /**  */
    @Column(name = "t.PRINTER_NUMBER")
    //@GridColumn(label = "common_dev_printingName", width = "110")
    private String printerNumber;

    /** 时间段 */
    @Column(name = "t.TIMESEG_ID")
    @GridColumn(label = "acc_auxIn_timeZone", show = false)
    private String accTimeSegId;

    /** 时间段名称 */
    @Column(name = "at.NAME")
    // @GridColumn(label = "acc_auxIn_timeZone",width="150")
    private String accTimeSeg;

    /** 绑定的摄像头 */
    // @Column(name="vc.ID")
    @GridColumn(label = "acc_common_boundChannel", show = false)
    private String channelId;

    // @Column(name="vc.NAME")
    @GridColumn(label = "acc_common_boundChannel", width = "150", sort = "na", showHeader = "accShowBindChannel")
    private String channelName;

    @GridColumn(label = "acc_ownedBoard", width = "110", showExpression = "#language!='zh_CN'")
    private String extDevName;

    /**  */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "150")
    private String remark;

    @Condition(value = "ad.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Condition(value = "ad.ID IN (%s)", formatType = "quote")
    private String devIdIn;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    @Column(name = "t.EXT_DEV_ID")
    private String extDevId;

    // pro
    /** 检测模式 0正常 1线路检测 */
    @Column(name = "t.INPUT_MODE")
    private String inputMode;

    /** 电阻值 */
    @Column(name = "t.SUPERVISED_RESISTOR")
    private String supervisedResistor;

    /** 是否禁用, true禁用，false可用 */
    @Column(name = "t.DISABLE")
    private Boolean disable;

    /** 输入类型, 0:无, 1:常开, 2:常闭 */
    @Column(name = "t.INPUT_TYPE")
    private String inputType;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "ad.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccAuxInItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccAuxInItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccAuxInItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param auxNo
     * @param printerNumber
     * @param name
     * @param remark
     */
    public AccAuxInItem(String id, Short auxNo, String printerNumber, String name, String remark) {
        super();
        this.id = id;
        this.auxNo = auxNo;
        this.printerNumber = printerNumber;
        this.name = name;
        this.remark = remark;
    }
}