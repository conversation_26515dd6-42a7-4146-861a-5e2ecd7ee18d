package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 按人员设置选择权限组VO
 * 
 * <AUTHOR>
 * @date 2018/3/14 16:30
 */
@From(after = "ACC_LEVEL t " + "LEFT JOIN ACC_TIMESEG e ON t.TIMESEG_ID = e.ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id", winHeight = 200, winWidth = 400, operates = {})
public class AccDeptSelectLevelItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "common_level_name", width = "110", sortNo = 1)
    private String levelName;

    /** 梯控时间段名称 */
    @Column(name = "e.NAME")
    @GridColumn(label = "acc_timeSeg_entity", width = "80", sortNo = 2)
    private String timeSegName;

    /** 时间段id */
    @Column(name = "t.TIMESEG_ID")
    private String timeSegId;

    private String type;

    @Condition(value = "t.ID NOT IN (SELECT ald.LEVEL_ID FROM ACC_LEVEL_DEPT ald where ald.DEPT_ID = ''{0}'')")
    private String filterId;

    private String selectId;

    @Column(name = "t.ID", equalTag = "in")
    private String inId;

    @Column(name = "t.ID", equalTag = "not in")
    private String notInId;

    private String deptId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    public AccDeptSelectLevelItem() {}

    public AccDeptSelectLevelItem(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public String getTimeSegName() {
        return timeSegName;
    }

    public void setTimeSegName(String timeSegName) {
        this.timeSegName = timeSegName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSelectId() {
        return selectId;
    }

    public void setSelectId(String selectId) {
        this.selectId = selectId;
    }

    public String getInId() {
        return inId;
    }

    public void setInId(String inId) {
        this.inId = inId;
    }

    public String getNotInId() {
        return notInId;
    }

    public void setNotInId(String notInId) {
        this.notInId = notInId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getFilterId() {
        return filterId;
    }

    public void setFilterId(String filterId) {
        this.filterId = filterId;
    }

    public String getAuthAreaIdIn() {
        return authAreaIdIn;
    }

    public void setAuthAreaIdIn(String authAreaIdIn) {
        this.authAreaIdIn = authAreaIdIn;
    }

    public String getTimeSegId() {
        return timeSegId;
    }

    public void setTimeSegId(String timeSegId) {
        this.timeSegId = timeSegId;
    }
}
