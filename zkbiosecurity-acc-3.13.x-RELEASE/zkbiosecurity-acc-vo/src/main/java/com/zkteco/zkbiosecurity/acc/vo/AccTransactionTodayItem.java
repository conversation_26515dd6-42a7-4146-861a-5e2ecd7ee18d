package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 今日访问记录
 * 
 * @author: verber
 * @date: 2018-04-28 10:45:58
 */

@From(after = "ACC_TRANSACTION t ")
@OrderBy(after = "t.EVENT_TIME DESC")
@GridConfig
@Getter
@Setter
public class AccTransactionTodayItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(width = "0", sortNo = 0, show = false)
    private String id;

    /** 记录编号 */
    @Column(name = "t.LOG_ID")
    @GridColumn(label = "acc_common_logEventNumber", width = "70", sortNo = 1)
    private Integer logId;

    /** 事件时间 */
    @Column(name = "t.EVENT_TIME")
    @GridColumn(label = "common_time", width = "150", sortNo = 2, sort = "na")
    private Date eventTime;

    /** 区域名称 */
    @Column(name = "t.AREA_NAME")
    @GridColumn(label = "base_area_name", width = "100", sortNo = 3)
    private String areaName;

    /** 设备名称 */
    @Column(name = "t.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "110", sortNo = 4)
    private String devAlias;

    /** 事件点名称 */
    @Column(name = "t.EVENT_POINT_NAME")
    @GridColumn(label = "common_eventPoint", width = "125", sortNo = 5)
    private String eventPointName;

    /** 事件名称 */
    @Column(name = "t.EVENT_NAME", equalTag = "in")
    @GridColumn(label = "common_eventDescription", width = "125", sortNo = 6, i18n = true, sort = "na")
    private String eventName;

    @GridColumn(label = "common_event_level", columnType = "custom", sortNo = 7, convert = "convertAccEventLevelToText",
        width = "110", sort = "na")
    private String levelAndEventPriority;

    /** 媒体文件 */
    @Column(name = "t.VID_LINKAGE_HANDLE")
    @GridColumn(label = "common_mediaFile", width = "100", columnType = "custom", convert = "convertAccVidLinkage",
        sortNo = 8, isExportExcel = false, sort = "na")
    private String vidLinkageHandle;

    /** 抓拍照片路径 */
    @Column(name = "t.CAPTURE_PHOTO_PATH")
    private String capturePhotoPath;

    /** 人员编号 */
    @Column(name = "t.PIN")
    @Condition(value = "t.PIN", equalTag = "notNull")
    @GridColumn(label = "pers_person_pin", width = "100", sortNo = 9, encryptMode = "${pers.pin.encryptMode}",
            permission = "acc:pin:encryptProp")
    private String pin;

    /** 姓名 */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "100", sortNo = 10, encryptMode = "${pers.name.encryptMode}",
            permission = "acc:name:encryptProp")
    private String name;

    /** 姓氏 */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 11,
        encryptMode = "${pers.lastName.encryptMode}", permission = "acc:name:encryptProp")
    private String lastName;

    /** 人员卡号 */
    @Column(name = "t.CARD_NO", encryptConverter = true)
    @GridColumn(label = "pers_card_cardNo", width = "100", sortNo = 12, encryptMode = "${pers.cardNo.encryptMode}",
            permission = "acc:cardNo:encryptProp")
    private String cardNo;

    /** 部门编号 */
    @Column(name = "t.DEPT_CODE")
    @GridColumn(label = "pers_dept_deptNo", width = "70", sortNo = 13)
    private String deptCode;

    /** 部门名称 */
    @Column(name = "t.DEPT_NAME")
    @GridColumn(label = "pers_dept_deptName", width = "100", sortNo = 14)
    private String deptName;

    /** 读头名称 */
    @Column(name = "t.READER_NAME")
    @GridColumn(label = "acc_readerDefine_readerName", width = "100", sortNo = 15)
    private String readerName;

    /** 验证方式名称 */
    @Column(name = "t.VERIFY_MODE_NAME", equalTag = "in")
    @GridColumn(label = "common_verifyMode_entiy", width = "100", sortNo = 16, i18n = true,
        convert = "convertVerifyModeName", sort = "na")
    private String verifyModeName;

    /** 描述 */
    @Column(name = "t.DESCRIPTION")
    private String description;// 描述

    @Column(name = "t.DEV_ID")
    private String devId;

    @Column(name = "t.DEV_SN")
    private String devSn;

    @Column(name = "t.VERIFY_MODE_NO")
    private Short verifyModeNo;

    @Column(name = "t.EVENT_NO")
    private Short eventNo;
    
    private Short eventLevel;

    @Column(name = "t.EVENT_POINT_TYPE")
    private Short eventPointType;

    @Column(name = "t.EVENT_POINT_ID")
    private String eventPointId;

    @Column(name = "t.READER_STATE")
    private Short readerState;

    @Column(name = "t.TRIGGER_COND")
    private Short triggerCond;// 联动触发条件

    @Column(name = "t.ACC_ZONE")
    private String accZone;// 门禁区域

    @Column(name = "t.EVENT_ADDR")
    private Short eventAddr;

    @Column(name = "t.UNIQUE_KEY")
    private String uniqueKey;

    @Condition(value = "t.EVENT_TIME", equalTag = ">=")
    private Date startTime;

    @Condition(value = "t.EVENT_TIME", equalTag = "<=")
    private Date endTime;

    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    @Column(name = "t.AREA_NAME", equalTag = "in")
    private String areaNameIn;

    @Condition(value = "(t.DEPT_CODE in (%s) or t.DEPT_CODE is null or t.DEPT_CODE = '')", formatType = "quote")
    private String deptCodeIn;

    @Column(name = "t.EVENT_PRIORITY", equalTag = "=")
    private Short eventPriority;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
        value = "(t.DEPT_CODE IN (select ud.code from auth_department ud where ud.id in(select aud.auth_dept_id from auth_user_dept aud where aud.auth_user_id = ''{0}'')) or t.DEPT_CODE is null or t.DEPT_CODE = '''')")
    private String deptCodeInByUserId;

    /**
     * 根据用户ID查询关联区域及子区域名称
     */
    @Condition(
        value = "t.AREA_NAME IN (select aa.name from auth_area aa where aa.id in(select aua.auth_area_id from auth_user_area aua where aua.auth_user_id = ''{0}''))")
    private String areaNameByUserId;

    /**
     * 默认构造方法
     */
    public AccTransactionTodayItem() {
        super();
    }

    /**
     * @param id
     */
    public AccTransactionTodayItem(String id) {
        this.id = id;
    }
}