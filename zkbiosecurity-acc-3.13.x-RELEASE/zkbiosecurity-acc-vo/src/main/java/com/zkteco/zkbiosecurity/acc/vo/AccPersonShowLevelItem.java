package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 按人员设置选择权限组VO
 * 
 * <AUTHOR>
 * @date 2018/3/14 16:30
 */
@Setter
@Getter
@From(after = "ACC_LEVEL t " + "LEFT JOIN ACC_TIMESEG e ON t.TIMESEG_ID = e.ID "
    + "LEFT JOIN ACC_LEVEL_PERSON alp ON alp.LEVEL_ID = t.ID LEFT JOIN AUTH_AREA a ON a.id = t.auth_area_id  ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id", winHeight = 200, winWidth = 400, operates = {})
public class AccPersonShowLevelItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "30", sortNo = 0, type = "ch", columnType = "ch", defaultValue = "true")
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "common_level_name", width = "100", sortNo = 1, sort = "na")
    private String levelName;

    /** 梯控时间段名称 */
    @Column(name = "e.NAME")
    @GridColumn(label = "acc_timeSeg_entity", width = "70", sortNo = 2, sort = "na")
    private String timeSegName;

    /** 设置有效时间 */
    private Boolean isSetValidTime;

    @Column(name = "alp.START_TIME")
    @GridColumn(label = "common_level_startTime", width = "125", sortNo = 3, type = "cusCalendar",
        columnType = "cusCalendar", sort = "na", showHeader = "accShowLevelTime")
    private Date startTime;

    /** 结束时间 */
    @Column(name = "alp.END_TIME")
    @GridColumn(label = "common_level_endTime", width = "125", sortNo = 4, type = "cusCalendar",
        columnType = "cusCalendar", sort = "na", showHeader = "accShowLevelTime")
    private Date endTime;

    @Column(name = "t.ID")
    @GridColumn(label = "", width = "30", minWidth = "30", sortNo = 5, columnType = "custom", sort = "na",
        convert = "convertEditAccPersonClearDate", showHeader = "accShowLevelTime")
    private String clearDate;

    @Column(name = "a.NAME")
    @GridColumn(label = "base_area_name", width = "90", sortNo = 6, sort = "na")
    private String authAreaName;

    private String type;

    @Condition(value = "alp.PERS_PERSON_ID = ''{0}''")
    private String filterId;

    private String selectId;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    private String personId;

    @Condition(value = "t.AUTH_AREA_ID IN (%s)", formatType = "quote")
    private String authAreaIdIn;

    public AccPersonShowLevelItem() {}

    public AccPersonShowLevelItem(String id) {
        this.id = id;
    }

}
