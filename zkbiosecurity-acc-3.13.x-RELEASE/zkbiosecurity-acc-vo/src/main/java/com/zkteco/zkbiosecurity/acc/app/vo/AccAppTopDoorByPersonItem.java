package com.zkteco.zkbiosecurity.acc.app.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁置顶门vo
 *
 * <AUTHOR>
 * @date:	2019-07-11 上午11:59
 */
@From(after = "ACC_TOPDOOR_BY_PERSON t ")
@OrderBy(after = "t.CREATE_TIME ASC")
@Getter
@Setter
@Accessors(chain=true)
public class AccAppTopDoorByPersonItem extends BaseItem {

    @Column(name="t.ID")
    private String id;

    /** 人员ID */
    @Column(name="PERSON_ID")
    private String personId;

    /** 门ID */
    @Column(name="DOOR_ID")
    private String doorId;
}
