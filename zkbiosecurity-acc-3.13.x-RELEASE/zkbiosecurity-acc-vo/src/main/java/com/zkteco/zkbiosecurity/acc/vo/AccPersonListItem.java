package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

@GridConfig(operate = false, idField = "id")
@Getter
@Setter
public class AccPersonListItem extends BaseItem {
    /** 主键 */
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /** 人员编号 */
    @GridColumn(label = "pers_person_pin", sortNo = 1, width = "100", encryptMode = "${pers.pin.encryptMode}",
        encryptProp = "${pers.pin.encryptProp}")
    private String personPin;

    /** 姓名 */
    @GridColumn(label = "pers_person_name", sortNo = 2, width = "100", encryptMode = "${pers.name.encryptMode}",
        encryptProp = "${pers.name.encryptProp}")
    private String personName;

    /** 姓 */
    @GridColumn(label = "pers_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3, width = "100",
        encryptMode = "${pers.lastName.encryptMode}", encryptProp = "${pers.lastName.encryptProp}")
    private String personLastName;

    /** 卡号 */
    @GridColumn(label = "pers_card_cardNo", sortNo = 4, width = "100", sort = "na", show = false,
        encryptMode = "${pers.cardNo.encryptMode}", encryptProp = "${pers.cardNo.encryptProp}")
    private String cardNo;

    /** 部门名称 */
    @GridColumn(label = "pers_dept_entity", sortNo = 5, width = "150")
    private String deptName;

    private String linkId; // 选人控件对应的模块ID

    private String modelType; // 对应模块类型

    private String likeName;// 用于全名模糊查询
}
