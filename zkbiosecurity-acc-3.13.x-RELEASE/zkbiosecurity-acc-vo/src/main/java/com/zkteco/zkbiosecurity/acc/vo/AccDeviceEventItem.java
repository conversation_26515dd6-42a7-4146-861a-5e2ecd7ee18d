/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 门禁事件类型vo
 */
@From(after = "ACC_DEVICE_EVENT t " + "LEFT JOIN ACC_DEVICE ad ON ad.ID = t.DEV_ID ")
@OrderBy(after = "ad.BUSINESS_ID ASC, t.EVENT_NO ASC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 550,
    operates = {@GridOperate(type = "edit", permission = "acc:deviceEvent:edit", url = "/accDeviceEvent.do?edit",
        label = "common_op_edit")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccDeviceEventItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.NAME", equalTag = "in")
    @GridColumn(label = "common_event_name", width = "200", columnType = "edit", editUrl = "/accDeviceEvent.do?edit",
        editPermission = "acc:deviceEvent:edit", i18n = true)
    private String name;

    /**  */
    @Column(name = "t.EVENT_NO")
    @GridColumn(label = "common_event_number", width = "120")
    private Short eventNo;

    /**  */
    @Column(name = "t.EVENT_LEVEL")
    private Short eventLevel;

    /** 事件等级和优先级显示使用字段 */
    @GridColumn(label = "common_event_level", columnType = "custom", convert = "convertToWordWithColor", width = "110",
        sort = "na")
    private String levelAndEventPriority;

    /**  */
    @Column(name = "t.DEV_ID")
    @GridColumn(label = "acc_deviceEvent_device", show = false)
    private String devId;

    /**  */
    @Column(name = "ad.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "150")
    private String devAlias;

    /**  */
    @Column(name = "ad.SN")
    @GridColumn(label = "common_dev_sn", width = "150")
    private String devSn;

    /** 文件id */
    @Column(name = "t.BASE_MEDIA_FILE_ID")
    @GridColumn(label = "acc_deviceEvent_baseMediaFile", show = false)
    private String baseMediaFileId;

    @GridColumn(label = "acc_deviceEvent_baseMediaFile", show = false)
    private String baseMediaFileName;

    @GridColumn(label = "acc_deviceEvent_baseMediaFile", show = false)
    private String baseMediaFilePath;

    @GridColumn(label = "acc_deviceEvent_baseMediaFile", show = false)
    private String baseMediaFileSize;

    @GridColumn(label = "acc_deviceEvent_baseMediaFile", show = false)
    private String baseMediaFileSuffix;

    @Condition(value = "ad.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    /** 优先级 pro */
    @Column(name = "t.EVENT_PRIORITY")
    private Short eventPriority;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "ad.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccDeviceEventItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccDeviceEventItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccDeviceEventItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param eventNo
     * @param name
     * @param eventLevel
     */
    public AccDeviceEventItem(String id, Short eventNo, String name, Short eventLevel) {
        super();
        this.id = id;
        this.eventNo = eventNo;
        this.name = name;
        this.eventLevel = eventLevel;
    }

    public AccDeviceEventItem(Short eventNo, String name, String devId, Short eventLevel) {
        this.eventNo = eventNo;
        this.name = name;
        this.devId = devId;
        this.eventLevel = eventLevel;
    }
}