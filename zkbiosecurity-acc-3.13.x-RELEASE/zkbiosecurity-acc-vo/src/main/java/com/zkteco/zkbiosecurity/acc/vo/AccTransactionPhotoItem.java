package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 记录图片查询对象
 * 
 * @author: ljf
 * @date: 2020/7/22 18:27
 */
@From(after = "ACC_TRANSACTION t ")
@OrderBy(after = "t.EVENT_TIME DESC")
@Getter
@Setter
public class AccTransactionPhotoItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    private String id;

    /** 事件时间 */
    @Column(name = "t.EVENT_TIME")
    private Date eventTime;

    /** 人员编号 */
    @Column(name = "t.PIN")
    private String pin;

    /**
     * 设备SN
     */
    @Column(name = "t.DEV_SN")
    private String devSn;

    /** 媒体文件 */
    @Column(name = "t.VID_LINKAGE_HANDLE")
    private String vidLinkageHandle;

    /** 抓拍照片路径 */
    @Column(name = "t.CAPTURE_PHOTO_PATH")
    private String capturePhotoPath;

    @Condition(value = "t.EVENT_TIME", equalTag = ">=")
    private Date startTime;

    @Condition(value = "t.EVENT_TIME", equalTag = "<=")
    private Date endTime;

    @Column(name = "t.AREA_NAME", equalTag = "in")
    private String areaNameIn;

    @Condition(value = "(t.DEPT_CODE in (%s) or t.DEPT_CODE is null or t.DEPT_CODE = '')", formatType = "quote")
    private String deptCodeIn;

    @Condition(value = "t.EVENT_NO", equalTag = "not in")
    private String notInEventNo;

    /**
     * 设备名称
     */
    @Condition(value = "t.DEV_ALIAS", equalTag = "like")
    private String devAlias;

    @Condition(value = "t.DEV_ID", equalTag = "in")
    private String inDeviceId;
    /**
     * 压缩目录
     */
    private String compressDirectory;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
        value = "(t.DEPT_CODE IN (select ud.code from auth_department ud where ud.id in(select aud.auth_dept_id from auth_user_dept aud where aud.auth_user_id = ''{0}'')) or t.DEPT_CODE is null or t.DEPT_CODE = '''')")
    private String deptCodeInByUserId;

    /**
     * 根据用户ID查询关联区域及子区域名称
     */
    @Condition(
        value = "t.AREA_NAME IN (select aa.name from auth_area aa where aa.id in(select aua.auth_area_id from auth_user_area aua where aua.auth_user_id = ''{0}''))")
    private String areaNameByUserId;

    @Condition(value = "t.DEV_SN", equalTag = "in")
    private String inDeviceSn;

    /**
     * 默认构造方法
     */
    public AccTransactionPhotoItem() {
        super();
    }

}
