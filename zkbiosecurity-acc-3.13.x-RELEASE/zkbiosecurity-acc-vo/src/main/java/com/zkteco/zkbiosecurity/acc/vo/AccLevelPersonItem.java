package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/2/4 13:57
 * @since 1.0.0
 */
@From(after = "ACC_LEVEL t LEFT JOIN ACC_LEVEL_PERSON lp ON lp.LEVEL_ID=t.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AccLevelPersonItem extends BaseItem {

    /** 主键 */
    @Column(name = "lp.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.LEVEL_ID")
    private String accLevelId;

    @Column(name = "lp.PERS_PERSON_ID")
    private String persPersonId;

    @Column(name = "t.BUSINESS_ID")
    private Long levelBId;
}
