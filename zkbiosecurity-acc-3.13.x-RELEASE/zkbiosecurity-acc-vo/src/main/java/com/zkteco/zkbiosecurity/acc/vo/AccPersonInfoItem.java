package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 下发人员相关参数bean--应该可以提取放到系统目录下，所有的业务模块都可以共用
 *
 * <AUTHOR>
 * @since 2014年8月22日 下午2:36:01
 */
public class AccPersonInfoItem implements Serializable{

    private static final long serialVersionUID = 1L;

    private List<AccPersonOptItem> personList = new ArrayList<AccPersonOptItem>();

    private List<AccBioTemplateItem> accBioTemplateItemList = new ArrayList<AccBioTemplateItem>();

    public List<AccPersonOptItem> getPersonList()
    {
        return personList;
    }

    public void setPersonList(List<AccPersonOptItem> personList)
    {
        this.personList = personList;
    }

    public List<AccBioTemplateItem> getAccBioTemplateItemList() {
        return accBioTemplateItemList;
    }

    public void setAccBioTemplateItemList(List<AccBioTemplateItem> accBioTemplateItemList) {
        this.accBioTemplateItemList = accBioTemplateItemList;
    }
}
