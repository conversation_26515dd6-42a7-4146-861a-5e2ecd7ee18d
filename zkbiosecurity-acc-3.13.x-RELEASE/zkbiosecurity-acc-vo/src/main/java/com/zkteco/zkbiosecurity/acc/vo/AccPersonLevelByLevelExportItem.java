package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/7/25 11:58
 */
@From(
    after = "PERS_PERSON t LEFT JOIN ACC_LEVEL_PERSON lP ON lP.PERS_PERSON_ID=t.ID LEFT JOIN ACC_LEVEL al ON al.ID=lP.LEVEL_ID")
@OrderBy(after = "al.NAME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AccPersonLevelByLevelExportItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "al.NAME")
    @GridColumn(label = "common_level_name", width = "170", sortNo = 1)
    private String levelName;

    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", width = "170", sortNo = 2, encryptMode = "${pers.pin.encryptMode}",
        encryptProp = "${pers.pin.encryptProp}")
    private String persPin;

    // 导出门禁权限组人员信息扩展字段
    @Condition(value = "lP.LEVEL_ID IN (%s)", formatType = "quote")
    private String levelIdsIn;

    /**
     * 开始时间
     */
    @Column(name = "lP.START_TIME")
    @GridColumn(label = "common_level_startTime", width = "170", sortNo = 3, showHeader = "accShowLevelTime")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "lP.END_TIME")
    @GridColumn(label = "common_level_endTime", width = "170", sortNo = 4, showHeader = "accShowLevelTime")
    private Date endTime;

    private String startTimeStr;

    private String endTimeStr;

    /**
     * 导入行数
     */
    private Integer rowNum;

    @Condition(
        value = "t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

}
