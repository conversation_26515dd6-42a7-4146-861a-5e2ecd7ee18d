/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:11 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:11
 */
@From(after = "ACC_COMBOPEN_COMB t LEFT JOIN ACC_COMBOPEN_PERSON p ON t.COMBOPEN_PERSON_ID=p.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AccCombOpenCombItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    private String id;

    @Column(name = "p.ID")
    private String accCombOpenPersonId;

    @Column(name = "p.NAME")
    private String accCombOpenPersonName;

    /**  */
    @Column(name = "t.OPENER_NUMBER")
    private Short openerNumber;

    /**  */
    @Column(name = "t.SORT")
    private Short sort;

    /** 多人开门人员业务id */
    @Column(name = "p.BUSINESS_ID")
    private Long combOpenPersonBId;

    /** 数据迁移扩展 **/
    /** 多人开门id */
    private String accCombOpenDoorId;

    /**
     * 默认构造方法
     */
    public AccCombOpenCombItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccCombOpenCombItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccCombOpenCombItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param openerNumber
     * @param sort
     */
    public AccCombOpenCombItem(String id, Short openerNumber, Short sort) {
        super();
        this.id = id;
        this.openerNumber = openerNumber;
        this.sort = sort;
    }
}