package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.annotation.GridOperate;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 按部门设置VO
 * 
 * <AUTHOR>
 * @date 2018/3/14 14:38
 */
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {@GridOperate(type = "select", permission = "acc:personLevelByDept:addLevel",
        url = "skip.do?page=acc_personLevelByDept_deptSelectLevelContent&deptId=(id)&deptCode=(code)",
        label = "common_level_addDefaultLevel", click = "afterAddDeptLevel")})
public class AccPersonLevelByDeptItem extends BaseItem {
    /** 主键 */
    @GridColumn(show = false)
    private String id;

    @GridColumn(label = "pers_dept_deptNo", width = "145", sortNo = 1)
    private String code;

    @GridColumn(label = "pers_dept_deptName", width = "135", sortNo = 2)
    private String name;

    @GridColumn(label = "pers_dept_parentDept", width = "140", sortNo = 3, sort = "na")
    private String parentDeptName;

    public AccPersonLevelByDeptItem() {}

    public AccPersonLevelByDeptItem(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getParentDeptName() {
        return parentDeptName;
    }

    public void setParentDeptName(String parentDeptName) {
        this.parentDeptName = parentDeptName;
    }
}
