package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 按人员设置选择权限组VO
 * 
 * <AUTHOR>
 * @date 2018/3/14 16:30
 */
@Getter
@Setter
@From(after = "ACC_LEVEL t " + "LEFT JOIN ACC_TIMESEG e ON t.TIMESEG_ID = e.ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id", winHeight = 200, winWidth = 400, operates = {})
public class AccPersonSelectLevelItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "common_level_name", width = "110", sortNo = 1)
    private String levelName;

    /** 梯控时间段名称 */
    @Column(name = "e.NAME")
    @GridColumn(label = "acc_timeSeg_entity", width = "80", sortNo = 2)
    private String timeSegName;
    /**
     * 开始时间
     */
    @GridColumn(label = "common_level_startTime", width = "125", sortNo = 3, type = "cusCalendar",
        columnType = "cusCalendar", sort = "na", showHeader = "accShowLevelTime")
    private Date startTime;

    /**
     * 结束时间
     */
    @GridColumn(label = "common_level_endTime", width = "125", sortNo = 4, type = "cusCalendar",
        columnType = "cusCalendar", sort = "na", showHeader = "accShowLevelTime")
    private Date endTime;

    @Column(name = "t.ID")
    @GridColumn(label = "", width = "30", minWidth = "30", sortNo = 5, columnType = "custom", sort = "na",
        convert = "convertPersonSelectLevelClearDate", showHeader = "accShowLevelTime")
    private String clearDate;
    private String type;

    @Condition(value = "t.ID NOT IN (SELECT alp.LEVEL_ID FROM ACC_LEVEL_PERSON alp where alp.PERS_PERSON_ID = ''{0}'')")
    private String filterId;

    private String selectId;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    private String personId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    public AccPersonSelectLevelItem() {}

    public AccPersonSelectLevelItem(String id) {
        this.id = id;
    }

}
