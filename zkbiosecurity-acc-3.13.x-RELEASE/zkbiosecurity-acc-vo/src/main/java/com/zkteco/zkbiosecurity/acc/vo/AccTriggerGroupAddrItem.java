package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/8 13:52
 * @since 1.0.0
 */
@From(after = "ACC_TRIGGERGROUP_ADDR t LEFT JOIN ACC_TRIGGERGROUP tr  on t.GROUP_ID = tr.ID")
@OrderBy(after = "t.CREATE_TIME ASC")
@Getter
@Setter
@Accessors(chain = true)
public class AccTriggerGroupAddrItem extends BaseItem {
    /** 触发点id */
    @Column(name = "t.ID")
    private String id;

    /** 属于那个触发点组 */
    @Column(name = "t.GROUP_ID")
    private String triggerGroupId;

    /** 触发点 **/
    @Column(name = "t.ADDRESS")
    private String address;
}
