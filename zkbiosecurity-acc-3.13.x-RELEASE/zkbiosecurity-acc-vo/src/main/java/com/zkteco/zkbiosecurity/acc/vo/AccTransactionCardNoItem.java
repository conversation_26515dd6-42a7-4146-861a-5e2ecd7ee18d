package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

@From(
    after = "ACC_DOOR t LEFT JOIN ACC_TRANSACTION a ON a.EVENT_POINT_ID = t.ID LEFT JOIN ACC_READER c ON c.DOOR_ID = t.ID ")
@Where(after = " and c.READER_STATE = a.READER_STATE and a.CARD_NO IS NOT NULL")
@OrderBy(after = "a.CREATE_TIME DESC")
@GridConfig
public class AccTransactionCardNoItem extends BaseItem {

    @Column(name = "t.ID")
    private String id;

    @Condition(value = "c.ID IN (%s)", formatType = "quote")
    private String readerIdIn;

    @Column(name = "a.CARD_NO", encryptConverter = true)
    private String cardNo;

    @Column(name = "a.CREATE_TIME", equalTag = ">=")
    private Date creatTime;

    @Override
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Date getCreatTime() {
        return creatTime;
    }

    public void setCreatTime(Date creatTime) {
        this.creatTime = creatTime;
    }

    public String getReaderIdIn() {
        return readerIdIn;
    }

    public void setReaderIdIn(String readerIdIn) {
        this.readerIdIn = readerIdIn;
    }
}
