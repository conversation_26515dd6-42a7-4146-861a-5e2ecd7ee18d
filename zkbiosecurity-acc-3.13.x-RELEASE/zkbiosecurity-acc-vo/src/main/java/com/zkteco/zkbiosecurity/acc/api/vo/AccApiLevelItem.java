package com.zkteco.zkbiosecurity.acc.api.vo;

import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/17 14:02
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccApiLevelItem implements Serializable {

    /* 权限组id */
    private String id;

    /* 权限组名称 */
    private String name;

    public static AccApiLevelItem createApiLevel(AccLevelItem accLevelItem) {
        AccApiLevelItem accApiLevelItem = null;
        if (accLevelItem != null) {
            accApiLevelItem = new AccApiLevelItem();
            accApiLevelItem.setId(accLevelItem.getId());
            accApiLevelItem.setName(accLevelItem.getName());
        }
        return accApiLevelItem;
    }
}
