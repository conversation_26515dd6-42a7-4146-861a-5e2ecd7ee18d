/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02
 */
@From(
    after = "ACC_COMBOPEN_DOOR t LEFT JOIN ACC_DOOR a ON t.DOOR_ID=a.ID " + "LEFT JOIN ACC_DEVICE d ON a.DEV_ID=d.ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 345, winWidth = 700,
    operates = {
        @GridOperate(type = "edit", permission = "acc:combOpenDoor:edit", url = "accCombOpenDoor.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:combOpenDoor:del", url = "accCombOpenDoor.do?del&names=(name)",
            label = "common_op_del")})
@Getter
@Setter
@Accessors(chain = true)
public class AccCombOpenDoorItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(columnType = "edit", label = "acc_combOpen_comboName", width = "150", sortNo = 1,
        editPermission = "acc:combOpenDoor:edit", editUrl = "/accCombOpenDoor.do?edit")
    private String name;

    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "130", sortNo = 2)
    private String deviceAlias;

    @Column(name = "a.DOOR_NO")
    @GridColumn(label = "acc_door_number", width = "150", sortNo = 3)
    private String doorNo;

    @Column(name = "a.NAME")
    @GridColumn(label = "acc_door_name", width = "150", sortNo = 4)
    private String doorName;

    @GridColumn(label = "acc_combOpen_verifyOneTime", width = "190", sortNo = 5, sort = "na")
    private String verifyOneTime;

    @GridColumn(label = "acc_combOpen_personGroup", width = "*", sortNo = 6, sort = "na")
    private String combOpenPersonId;

    @Column(name = "a.ID")
    private String doorId;

    @Condition(value = "a.ID", equalTag = "in")
    private String doorIdsIn;

    @Condition(value = "d.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    @Column(name = "d.ID")
    private String deviceId;

    @Column(name = "t.BUSINESS_ID")
    private Long businessId;

    /** 数据迁移扩展 */
    /** 设备号 */
    @Column(name = "d.SN")
    private String deviceSn;

    /**
     * 默认构造方法
     */
    public AccCombOpenDoorItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccCombOpenDoorItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccCombOpenDoorItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     */
    public AccCombOpenDoorItem(String id, String name) {
        super();
        this.id = id;
        this.name = name;
    }
}