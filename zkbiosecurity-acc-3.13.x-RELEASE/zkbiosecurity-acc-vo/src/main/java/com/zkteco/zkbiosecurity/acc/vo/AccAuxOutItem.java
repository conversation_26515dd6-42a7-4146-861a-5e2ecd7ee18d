/**
 * @author: GenerationTools
 * @date: 2018-03-14 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 *
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 上午09:38
 */
@From(after = "ACC_AUXOUT t " + "LEFT JOIN ACC_DEVICE ad ON ad.ID = t.DEV_ID "
    + "LEFT JOIN ACC_TIMESEG at ON at.ID = t.TIMESEG_ID LEFT JOIN ACC_EXT_DEVICE aed on aed.ID = t.EXT_DEV_ID")
@OrderBy(after = "ad.BUSINESS_ID ASC, t.AUX_NO ASC")
@GridConfig(operate = true, idField = "id", winHeight = 350, winWidth = 420, operates = {
    @GridOperate(type = "edit", permission = "acc:auxOut:edit", url = "accAuxOut.do?edit", label = "common_op_edit")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccAuxOutItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_name", width = "150", columnType = "edit", editUrl = "/accAuxOut.do?edit",
        editPermission = "acc:auxOut:edit")
    private String name;

    /**  */
    @Column(name = "t.DEV_ID")
    @GridColumn(label = "acc_auxOut_device", show = false)
    private String devId;

    /**  */
    @Column(name = "ad.SN")
    @GridColumn(label = "common_dev_sn", show = false)
    private String devSn;

    /**  */
    @Column(name = "ad.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "150")
    private String devAlias;

    /**  */
    @Column(name = "t.AUX_NO")
    @GridColumn(label = "common_number")
    private Short auxNo;

    /**  */
    @Column(name = "t.PRINTER_NUMBER")
    //@GridColumn(label = "common_dev_printingName", width = "110")
    private String printerNumber;

    /** 时间段 */
    @Column(name = "t.TIMESEG_ID")
    private String accTimeSegId;

    @GridColumn(label = "acc_auxOut_passageModeTimeZone", width = "185", sort = "na")
    private String accTimeSegName;

    @GridColumn(label = "acc_ownedBoard", width = "110", showExpression = "#language!='zh_CN'")
    @Column(name = "aed.ALIAS")
    private String extDevName;

    /**  */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "150")
    private String remark;

    @Condition(value = "ad.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Condition(value = "ad.ID IN (%s)", formatType = "quote")
    private String devIdIn;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    @Column(name = "t.EXT_DEV_ID")
    private String extDevId;

    @Column(name = "aed.ALIAS")
    private String extAlias;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "ad.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccAuxOutItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccAuxOutItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccAuxOutItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param auxNo
     * @param printerNumber
     * @param name
     * @param remark
     */
    public AccAuxOutItem(String id, Short auxNo, String printerNumber, String name, String remark) {
        super();
        this.id = id;
        this.auxNo = auxNo;
        this.printerNumber = printerNumber;
        this.name = name;
        this.remark = remark;
    }
}