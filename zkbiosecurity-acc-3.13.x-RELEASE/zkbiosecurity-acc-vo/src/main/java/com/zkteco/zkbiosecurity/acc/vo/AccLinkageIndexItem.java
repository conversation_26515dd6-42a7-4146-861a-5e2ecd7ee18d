/**
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41
 */
@From(after = "ACC_LINKAGEINDEX t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "acc:linkageIndex:edit", url = "/accLinkageIndex.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:linkageIndex:del", url = "/accLinkageIndex.do?del",
            label = "common_op_del")})
@Getter
@Setter
@Accessors(chain = true)
public class AccLinkageIndexItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.ID")
    @GridColumn(label = "acc_linkageIndex_device")
    private AccDeviceItem device;

    /**  */
    @Column(name = "t.MAX_INDEX")
    @GridColumn(label = "acc_linkageIndex_maxIndex")
    private Integer maxIndex;

    /**
     * 默认构造方法
     */
    public AccLinkageIndexItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccLinkageIndexItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccLinkageIndexItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param maxIndex
     */
    public AccLinkageIndexItem(String id, Integer maxIndex) {
        super();
        this.id = id;
        this.maxIndex = maxIndex;
    }
}