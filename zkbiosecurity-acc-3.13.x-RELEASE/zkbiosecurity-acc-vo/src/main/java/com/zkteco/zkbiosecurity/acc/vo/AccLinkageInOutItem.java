/**
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41
 */
@From(after = "ACC_LINKAGE_INOUT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "acc:linkageInOut:edit", url = "accLinkageInOut.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:linkageInOut:del", url = "accLinkageInOut.do?del",
            label = "common_op_del")})
@Getter
@Setter
@Accessors(chain = true)
public class AccLinkageInOutItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    @Column(name = "t.LINKAGE_ID")
    private String linkageId;

    /**  */
    @Column(name = "t.INPUT_TYPE")
    @GridColumn(label = "acc_linkageInOut_inputType")
    private String inputType;

    /**  */
    @Column(name = "t.INPUT_ID")
    @GridColumn(label = "acc_linkageInOut_inputId")
    private String inputId;

    /**  */
    @Column(name = "t.OUTPUT_TYPE")
    @GridColumn(label = "acc_linkageInOut_outputType")
    private String outputType;

    /**  */
    @Column(name = "t.OUTPUT_ID")
    @GridColumn(label = "acc_linkageInOut_outputId")
    private String outputId;

    /**  */
    @Column(name = "t.ACTION_TYPE")
    @GridColumn(label = "acc_linkageInOut_actionType")
    private Short actionType;

    /**  */
    @Column(name = "t.ACTION_TIME")
    @GridColumn(label = "acc_linkageInOut_actionTime")
    private Short actionTime;

    /** 数据迁移扩展 **/
    /** 联动名称 **/
    private String linkageName;

    /**
     * 默认构造方法
     */
    public AccLinkageInOutItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccLinkageInOutItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccLinkageInOutItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param inputType
     * @param inputId
     * @param outputType
     * @param outputId
     * @param actionType
     * @param actionTime
     */
    public AccLinkageInOutItem(String id, String inputType, String inputId, String outputType, String outputId,
        Short actionType, Short actionTime) {
        super();
        this.id = id;
        this.inputType = inputType;
        this.inputId = inputId;
        this.outputType = outputType;
        this.outputId = outputId;
        this.actionType = actionType;
        this.actionTime = actionTime;
    }
}