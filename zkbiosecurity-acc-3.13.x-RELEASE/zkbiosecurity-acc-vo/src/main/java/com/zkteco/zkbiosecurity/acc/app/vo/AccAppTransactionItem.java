package com.zkteco.zkbiosecurity.acc.app.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 2018/12/7 10:42
 */
@Setter
@Getter
@Accessors(chain = true)
public class AccAppTransactionItem implements Serializable {

    /**
     * 事件记录ID
     */
    private String id;

    /**
     * 人员编号
     */
    private String pin;

    /**
     * 事件时间
     */
    private String eventTime;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 设备名称
     */
    private String devAlias;

    /**
     * 事件点名称
     */
    private String eventPointName;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 人员名称
     */
    private String personName;

    /**
     * 人员姓氏
     */
    private String lastName;

    /**
     * 读头名称
     */
    private String readerName;

    /**
     * 验证方式编号
     */
    private String verifyModeNo;

    /**
     * 验证方式名称
     */
    private String verifyModeName;

    /**
     * 事件级别
     */
    private String status;

    /** 人员名称 */
    private String name;

    /** 部门名称 */
    private String deptName;

    /** 抓拍图片base64 */
    private String capturePhotoPathBase64;

    private String capturePhotoPath;

    /** 人员图片base64 */
    private String persPhotoBase64;

    private String persPhotoPath;

    private String persPositionName;

    private Short readerState;
}
