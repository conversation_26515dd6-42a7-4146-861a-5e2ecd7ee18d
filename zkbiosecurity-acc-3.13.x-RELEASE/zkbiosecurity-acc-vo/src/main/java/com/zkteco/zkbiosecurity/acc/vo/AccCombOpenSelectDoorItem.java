package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

@From(after = "ACC_DOOR t LEFT JOIN ACC_DEVICE d ON d.ID=t.DEV_ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
public class AccCombOpenSelectDoorItem extends BaseItem {

    /** 主键 */
    @Column(name="t.ID")
    @GridColumn(columnType = "ra", width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "acc_door_name",sortNo = 1,width = "140")
    private String doorName;

    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev",sortNo = 2,width = "125")
    private String deviceAlias;

    @Column(name = "d.SN")
    @GridColumn(label = "common_dev_sn",sortNo = 3, minWidth = "210")
    private String deviceSn;

    @Column(name = "d.ID")
    private String deviceId;

    private String type;

   // @Condition(value = "t.ID NOT IN (SELECT af.DOOR_ID FROM ACC_COMBOPEN_DOOR af whe)")
    private String selectId;

    @Column(name = "t.ID", equalTag = "in")
    private String selectDoorIdsIn;

    @Column(name = "t.ID", equalTag = "not in")
    private String selectDoorIdsNotIn;

    @Condition(value = "d.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    @Column(name="t.ENABLED")
    private Boolean enabled;

    @Override
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDoorName() {
        return doorName;
    }

    public void setDoorName(String doorName) {
        this.doorName = doorName;
    }

    public String getDeviceAlias() {
        return deviceAlias;
    }

    public void setDeviceAlias(String deviceAlias) {
        this.deviceAlias = deviceAlias;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSelectId() {
        return selectId;
    }

    public void setSelectId(String selectId) {
        this.selectId = selectId;
    }

    public String getSelectDoorIdsIn() {
        return selectDoorIdsIn;
    }

    public void setSelectDoorIdsIn(String selectDoorIdsIn) {
        this.selectDoorIdsIn = selectDoorIdsIn;
    }

    public String getSelectDoorIdsNotIn() {
        return selectDoorIdsNotIn;
    }

    public void setSelectDoorIdsNotIn(String selectDoorIdsNotIn) {
        this.selectDoorIdsNotIn = selectDoorIdsNotIn;
    }

    public String getAuthAreaIdIn() {
        return authAreaIdIn;
    }

    public void setAuthAreaIdIn(String authAreaIdIn) {
        this.authAreaIdIn = authAreaIdIn;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}
