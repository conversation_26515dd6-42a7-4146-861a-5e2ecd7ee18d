package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 人员最后访问位置
 * 
 * @author: yibiao.shen
 * @date: 2018-03-09 11:25:58
 */
@From(after = "ACC_PERSON_LASTADDR t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccPersonLastAddrItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(width = "0", sortNo = 0, show = false)
    private String id;

    /** 记录编号 */
    @Column(name = "t.EVENT_NO")
    private Integer logId;

    /** 人员编号 */
    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", width = "100", sortNo = 1, convert = "addHref",
        encryptMode = "${pers.pin.encryptMode}", encryptProp = "${pers.pin.encryptProp}")
    private String pin;

    /** 姓名 */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "100", sortNo = 2, encryptMode = "${pers.name.encryptMode}",
        encryptProp = "${pers.name.encryptProp}")
    private String name;

    /** 姓氏 */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "100", showExpression = "#language!='zh_CN'", sortNo = 3,
        encryptMode = "${pers.lastName.encryptMode}", encryptProp = "${pers.lastName.encryptProp}")
    private String lastName;

    /** 人员卡号 */
    @Column(name = "CARD_NO", encryptConverter = true)
    @GridColumn(label = "pers_card_cardNo", width = "100", sortNo = 4, encryptMode = "${pers.cardNo.encryptMode}",
        encryptProp = "${pers.cardNo.encryptProp}")
    private String cardNo;

    /** 事件时间 */
    @Column(name = "t.EVENT_TIME")
    @GridColumn(label = "common_time", width = "125", sortNo = 5)
    private Date eventTime;

    /** 部门编号 */
    @Column(name = "t.DEPT_CODE")
    @GridColumn(label = "pers_dept_deptNo", width = "70", sortNo = 6)
    private String deptCode;

    /** 部门名称 */
    @Column(name = "t.DEPT_NAME")
    @GridColumn(label = "pers_dept_deptName", width = "100", sortNo = 7)
    private String deptName;

    /** 设备名称 */
    @Column(name = "t.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "110", sortNo = 8)
    private String devAlias;

    /** 事件点名称 */
    @Column(name = "t.EVENT_POINT_NAME")
    @GridColumn(label = "common_eventPoint", width = "125", sortNo = 9)
    private String eventPointName;

    /** 事件名称 */
    @Column(name = "t.EVENT_NAME", equalTag = "in")
    @GridColumn(label = "common_eventDescription", width = "125", sortNo = 10, i18n = true)
    private String eventName;

    /** 读头名称 */
    @Column(name = "t.READER_NAME")
    @GridColumn(label = "acc_readerDefine_readerName", width = "100", sortNo = 11)
    private String readerName;

    /** 验证方式名称 */
    @Column(name = "t.VERIFY_MODE_NAME", equalTag = "in")
    @GridColumn(label = "common_verifyMode_entiy", width = "100", sortNo = 12, i18n = true)
    private String verifyModeName;

    /** 区域名称 */
    @Column(name = "AREA_NAME")
    @GridColumn(label = "base_area_name", width = "70", sortNo = 13)
    private String areaName;

    /** 描述 */
    @Column(name = "DESCRIPTION")
    @GridColumn(label = "common_remark", width = "50", sortNo = 14)
    private String description;

    @Condition(value = "t.EVENT_TIME", equalTag = ">=")
    private Date startTime;

    @Condition(value = "t.EVENT_TIME", equalTag = "<=")
    private Date endTime;

    private String devId;

    private String devSn;

    private Short verifyModeNo;

    private Short eventNo;

    private Short eventPointType;

    private String eventPointId;

    private Short readerState;

    private Short triggerCond;

    private String vidLinkageHandle;

    private String accZone;

    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    /**
     * 默认构造方法
     */
    public AccPersonLastAddrItem() {
        super();
    }

    /**
     * @param id
     */
    public AccPersonLastAddrItem(String id) {
        this.id = id;
    }
}
