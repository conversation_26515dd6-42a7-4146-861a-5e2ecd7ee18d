package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@From(
    after = "PERS_PERSON t LEFT JOIN AUTH_DEPARTMENT d ON t.auth_dept_id = d.id")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@GridConfig(operate = false, idField = "id")
public class AccSelectPersonItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    private String personId;

    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", sortNo = 1, width = "90")
    private String personPin;

    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", sortNo = 2, width = "90")
    private String personName;

    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3, width = "90")
    private String personLastName;

    @Column(name = "t.GENDER")
    @GridColumn(label = "pers_person_gender", format = "F=pers_person_female,M=pers_person_male,U=common_unknown,=---",
        sortNo = 5, width = "60", sort = "na", show = false)
    private String gender;

    @Column(name = "d.NAME")
    @GridColumn(label = "pers_dept_entity", sortNo = 6, sort = "na", width = "100")
    private String deptName;

    /**
     * 开始时间
     */
    @GridColumn(label = "common_level_startTime", width = "125", sortNo = 7, type = "cusCalendar",
        columnType = "cusCalendar", sort = "na", showHeader = "accShowLevelTime")
    private Date startTime;

    /**
     * 结束时间
     */
    @GridColumn(label = "common_level_endTime", width = "125", sortNo = 8, type = "cusCalendar",
        columnType = "cusCalendar", sort = "na", showHeader = "accShowLevelTime")
    private Date endTime;

    @GridColumn(label = "", width = "30", minWidth = "30", sortNo = 9, columnType = "custom", sort = "na",
        convert = "convertAccSelectPersonClearDate", showHeader = "accShowLevelTime")
    private String clearDate;

    private String beginPin;

    private String endPin;

    // 判断是左列表（值为noSelect）还是右列表（值为select）
    private String type;

    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    // 选中人员的ID
    @Condition(value = "t.ID", equalTag = "not in")
    private String selectId;

    private String linkId; // 选人控件对应的模块ID

    private String modelType; // 对应模块类型

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(value = "d.ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /** 权限组id */
    @Condition(value = "NOT EXISTS (SELECT 1 FROM ACC_LEVEL_PERSON alp WHERE alp.PERS_PERSON_ID = t.ID AND alp.LEVEL_ID=''{0}'')")
    private String levelId;

    /** 首人常开id */
    @Condition(
        value = "t.ID NOT IN (SELECT apf.PERS_PERSON_ID FROM ACC_PERSON_FIRSTOPEN apf WHERE apf.ACC_FIRSTOPEN_ID=''{0}'')")
    private String accFirstOpenId;

    /** 多人开门人员组id */
    @Condition(
        value = "t.ID NOT IN (SELECT apc.PERS_PERSON_ID FROM ACC_PERSON_COMBOPENPERSON apc WHERE apc.ACC_COMBOPENPERSON_ID !=''{0}'' )")
    private String accCombOpenPersonId;

    /** 验证方式规则人员组id */
    @Condition(
        value = "t.ID NOT IN (SELECT apv.PERS_PERSON_ID FROM ACC_PERSON_VERIFYMODERULE apv WHERE apv.ACC_VERIFYMODERULE_ID !=''{0}'' )")
    private String accVerifyModeRuleId;

    @Column(name = "t.ENABLED_CREDENTIAL")
    private Boolean enabledCredential;
}
