package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 以门查询
 * 
 * @author: yibiao.shen
 * @date: 2018-03-09 15:41:58
 */
@From(after = "ACC_DOOR t LEFT JOIN ACC_DEVICE ad ON ad.ID = t.DEV_ID ")
@OrderBy(after = "t.CREATE_TIME ")
@GridConfig
@Getter
@Setter
@Accessors(chain = true)
public class AccTransactionDoorItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(width = "0", show = false)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "acc_door_name", width = "140")
    private String name;

    /**  */
    @Column(name = "t.DOOR_NO")
    @GridColumn(label = "acc_door_number", width = "120")
    private Short doorNo;

    /**  */
    @Column(name = "ad.ID", equalTag = "=")
    private String deviceId;

    /**  */
    @Column(name = "ad.ID", equalTag = "in")
    private String deviceIds;

    /**  */
    @Column(name = "ad.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev", width = "120")
    private String deviceAlias;

    @Condition(value = "ad.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(value = "ad.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccTransactionDoorItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccTransactionDoorItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccTransactionDoorItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param doorNo
     * @param name
     * @param deviceId
     * @param deviceAlias
     */
    public AccTransactionDoorItem(String id, Short doorNo, String name, String deviceId, String deviceAlias) {
        super();
        this.id = id;
        this.doorNo = doorNo;
        this.name = name;
        this.deviceId = deviceId;
        this.deviceAlias = deviceAlias;
    }
}
