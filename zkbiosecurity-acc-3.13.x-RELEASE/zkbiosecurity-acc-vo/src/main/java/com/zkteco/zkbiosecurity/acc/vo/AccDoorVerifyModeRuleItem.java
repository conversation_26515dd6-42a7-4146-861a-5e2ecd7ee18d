package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

@From(after = "ACC_DOOR d LEFT JOIN ACC_DEVICE ad ON d.DEV_ID=ad.ID " +
        "LEFT JOIN ACC_DOOR_VERIFYMODERULE adv ON adv.ACC_DOOR_ID=d.ID")
@OrderBy(after = "d.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
public class AccDoorVerifyModeRuleItem extends BaseItem{
    /** 主键 */
    @Column(name="d.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "d.NAME")
    @GridColumn(label = "acc_door_name",width = "170",sortNo = 1)
    private String doorName;

    @Column(name = "ad.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev",width = "*",sortNo = 2)
    private String deviceAlias;

    @Column(name = "adv.ACC_VERIFYMODERULE_ID")
    private String accVerifyModeRuleId;

    @Condition(value = "ad.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDoorName() {
        return doorName;
    }

    public void setDoorName(String doorName) {
        this.doorName = doorName;
    }

    public String getDeviceAlias() {
        return deviceAlias;
    }

    public void setDeviceAlias(String deviceAlias) {
        this.deviceAlias = deviceAlias;
    }

    public String getAccVerifyModeRuleId() {
        return accVerifyModeRuleId;
    }

    public void setAccVerifyModeRuleId(String accVerifyModeRuleId) {
        this.accVerifyModeRuleId = accVerifyModeRuleId;
    }

    public String getAuthAreaIdIn() {
        return authAreaIdIn;
    }

    public void setAuthAreaIdIn(String authAreaIdIn) {
        this.authAreaIdIn = authAreaIdIn;
    }
}
