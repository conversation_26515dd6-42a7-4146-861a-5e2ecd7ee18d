package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Setter
@Getter
@Accessors(chain = true)
public class Acc4UpdateMThreshold implements Serializable {
    private static final long serialVersionUID = 1L;

    private String devId;

    private String mThreshold;

    private String faceMThr;

    private String pvMThreshold;
}
