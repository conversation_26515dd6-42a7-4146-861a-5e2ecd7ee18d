/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02
 */
@From(after = "ACC_COMBOPEN_PERSON t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 175, winWidth = 420,
    operates = {
        @GridOperate(type = "edit", permission = "acc:combOpenPerson:edit", url = "accCombOpenPerson.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "select", click = "afterAddCombOpenPerson", permission = "acc:combOpenPerson:addPerson",
            url = "skip.do?page=acc_combOpenPerson_accCombOpenSelectPersonContent&linkName=(name)&linkId=(id)",
            label = "pers_common_addPerson")})
@Getter
@Setter
public class AccCombOpenPersonItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(columnType = "edit", label = "acc_combOpen_personGroupName", width = "100",
        editPermission = "acc:combOpenPerson:edit", editUrl = "/accCombOpenPerson.do?edit")
    private String name;

    /*@GridColumn(label = "pers_common_personCount", width = "80")
    private String personCount;*/

    /**  */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "100")
    private String remark;

    /**
     * 默认构造方法
     */
    public AccCombOpenPersonItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccCombOpenPersonItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccCombOpenPersonItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     * @param remark
     */
    public AccCombOpenPersonItem(String id, String name, String remark) {
        super();
        this.id = id;
        this.name = name;
        this.remark = remark;
    }

    /**
     * @return id 主键
     */
    public String getId() {
        return id;
    }

    /**
     * @param id 要设置的 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name 要设置的
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return remark
     */
    public String getRemark() {
        return remark;
    }

    /**
     * @param remark 要设置的
     */
    public void setRemark(String remark) {
        this.remark = remark;
    }
}