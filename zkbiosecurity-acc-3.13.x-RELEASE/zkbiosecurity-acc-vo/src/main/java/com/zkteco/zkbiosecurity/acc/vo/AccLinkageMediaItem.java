/**
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41
 */
@From(after = "ACC_LINKAGE_MEDIA t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "acc:linkageMedia:edit", url = "accLinkageMedia.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:linkageMedia:del", url = "accLinkageMedia.do?del",
            label = "common_op_del")})
public class AccLinkageMediaItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.LINKAGE_ID")
    @GridColumn(label = "acc_linkageMedia_linkage")
    private AccLinkageItem linkage;

    /**  */
    @Column(name = "t.MEDIA_CONTENT")
    @GridColumn(label = "acc_linkageMedia_mediaContent")
    private String mediaContent;

    /**  */
    @Column(name = "t.MEDIA_TYPE")
    @GridColumn(label = "acc_linkageMedia_mediaType")
    private Short mediaType;

    /**
     * 默认构造方法
     */
    public AccLinkageMediaItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccLinkageMediaItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccLinkageMediaItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param mediaContent
     * @param mediaType
     */
    public AccLinkageMediaItem(String id, String mediaContent, Short mediaType) {
        super();
        this.id = id;
        this.mediaContent = mediaContent;
        this.mediaType = mediaType;
    }

    /**
     * @return id 主键
     */
    public String getId() {
        return id;
    }

    /**
     * @param id
     *            要设置的 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return linkage
     */
    public AccLinkageItem getLinkage() {
        return linkage;
    }

    /**
     * @param linkage
     *            要设置的
     */
    public void setLinkage(AccLinkageItem linkage) {
        this.linkage = linkage;
    }

    /**
     * @return mediaContent
     */
    public String getMediaContent() {
        return mediaContent;
    }

    /**
     * @param mediaContent
     *            要设置的
     */
    public void setMediaContent(String mediaContent) {
        this.mediaContent = mediaContent;
    }

    /**
     * @return mediaType
     */
    public Short getMediaType() {
        return mediaType;
    }

    /**
     * @param mediaType
     *            要设置的
     */
    public void setMediaType(Short mediaType) {
        this.mediaType = mediaType;
    }
}