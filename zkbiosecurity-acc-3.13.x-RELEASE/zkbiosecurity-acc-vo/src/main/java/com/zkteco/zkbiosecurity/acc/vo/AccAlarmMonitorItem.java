/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

@From(after = "ACC_ALARM_MONITOR t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@GridConfig(operate = false, idField = "id", winHeight = 520, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "acc:alarmMonitor:ackAlarm", filter = "accShowAcknowledgedAlarm",
            url = "accAlarmMonitor.do?editAcknowledged", label = "acc_rtMonitor_ackAlarm")})
public class AccAlarmMonitorItem extends BaseItem implements Serializable {

    /**
     * 说明成员变量的意义,目的,功能,可能被用到的地方
     */
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    @Column(name = "t.EVENT_NUM")
    // @GridColumn(label = "acc_common_logEventNumber", width = "130")
    private Long eventNum;

    /** 事件发生时间 */
    @Column(name = "t.EVENT_TIME")
    @GridColumn(label = "common_time", width = "150")
    @DateType(type = "timestamp")
    private Date eventTime;

    /** 设备别名 */
    @Column(name = "t.DEV_ALIAS")
    @GridColumn(label = "common_dev_entity", width = "100")
    private String devAlias;

    /** 设备序列号 */
    @Column(name = "t.DEV_SN")
    private String devSn;

    /** 事件发生点 */
    @Column(name = "t.EVENT_POINT_NAME")
    @GridColumn(label = "common_eventPoint", width = "110")
    private String eventPointName;

    /** 事件名 */
    @Column(name = "t.EVENT_NAME", equalTag = "in")
    @GridColumn(label = "common_eventDescription", width = "150", i18n = true, sort = "na")
    private String eventName;

    /** 人员名 */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person", width = "120")
    private String name;

    /** 人员编号 */
    @Column(name = "t.PIN")
    @GridColumn(show = false)
    private String pin;

    /** 读头名称 */
    @Column(name = "t.READER_NAME")
    @GridColumn(show = false)
    private String readerName;

    @Column(name = "t.AREA_NAME")
    @GridColumn(label = "common_level_personArea")
    private String areaName;

    @Column(name = "t.PRIORITY")
    @GridColumn(label = "acc_alarm_priority", width = "108",
        format = "0=auth_security_strengthLevel0,1=auth_security_strengthLevel1,2=auth_security_strengthLevel2,3=acc_musterPointReport_danger")
    private Short priority;

    /** 事件状态 0未确认 1确认 */
    @Column(name = "t.STATUS")
    @GridColumn(label = "common_status",
        format = "0=acc_alarm_unhandled,1=acc_alarm_inProcess,2=acc_alarm_acknowledged")
    private Short status;

    @Condition(value = "t.EVENT_TIME", equalTag = ">=")
    private Date startTime;

    @Condition(value = "t.EVENT_TIME", equalTag = "<=")
    private Date endTime;

    @Condition(value = "t.EVENT_NO", equalTag = "in")
    private String eventNosIn;

    @Condition(value = "t.EVENT_TIME", equalTag = "<=")
    private Date afterEventTime;

    private List<AccAlarmMonitorHistoryItem> historyItemList;

    @Condition(value = "t.AREA_NAME", equalTag = "in")
    private String areaNameIn;

    /**
     * 默认构造方法
     */
    public AccAlarmMonitorItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccAlarmMonitorItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccAlarmMonitorItem(String id) {
        super(true);
        this.id = id;
    }
}