/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02
 */
@From(after = "ACC_VERIFYMODE_RULE t LEFT JOIN ACC_TIMESEG a ON t.TIMESEG_ID=a.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 550, winWidth = 1400, operates = {
    @GridOperate(type = "edit", permission = "acc:verifyModeRule:edit", url = "accVerifyModeRule.do?edit",
        label = "common_op_edit"),
    @GridOperate(type = "select", click = "afterAddVerifyModeRuleDoor", permission = "acc:verifyModeRule:addDoor",
        url = "skip.do?page=acc_verifyModeRule_accSelectDoorContent&verifyModeRuleId=(id)&verifyModeRuleName=(name)",
        label = "acc_map_addDoor")})
@Getter
@Setter
@Accessors(chain = true)
public class AccVerifyModeRuleItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(columnType = "edit", label = "acc_common_ruleName", width = "150", sortNo = 1,
        editPermission = "acc:verifyModeRule:edit", editUrl = "/accVerifyModeRule.do?edit")
    private String name;

    /**  */
    @Column(name = "a.NAME")
    @GridColumn(label = "common_leftMenu_timeZone", width = "110", sortNo = 2)
    private String timeSegName;

    @Column(name = "a.ID")
    private String timeSegId;

    @Column(name = "a.BUSINESS_ID")
    private Long timeSegBusinessId;

    /**  */
    @Column(name = "t.SUNDAY_TIME1_VS_PERSON")
    private Short sundayTime1VSPerson;

    /**  */
    @Column(name = "t.SUNDAY_TIME1_VS_DOOR")
    private Short sundayTime1VSDoor;

    /**  */
    @Column(name = "t.SUNDAY_TIME2_VS_PERSON")
    private Short sundayTime2VSPerson;

    /**  */
    @Column(name = "t.SUNDAY_TIME2_VS_DOOR")
    private Short sundayTime2VSDoor;

    /**  */
    @Column(name = "t.SUNDAY_TIME3_VS_PERSON")
    private Short sundayTime3VSPerson;

    /**  */
    @Column(name = "t.SUNDAY_TIME3_VS_DOOR")
    private Short sundayTime3VSDoor;

    /**  */
    @Column(name = "t.MONDAY_TIME1_VS_PERSON")
    private Short mondayTime1VSPerson;

    /**  */
    @Column(name = "t.MONDAY_TIME1_VS_DOOR")
    private Short mondayTime1VSDoor;

    /**  */
    @Column(name = "t.MONDAY_TIME2_VS_PERSON")
    private Short mondayTime2VSPerson;

    /**  */
    @Column(name = "t.MONDAY_TIME2_VS_DOOR")
    private Short mondayTime2VSDoor;

    /**  */
    @Column(name = "t.MONDAY_TIME3_VS_PERSON")
    private Short mondayTime3VSPerson;

    /**  */
    @Column(name = "t.MONDAY_TIME3_VS_DOOR")
    private Short mondayTime3VSDoor;

    /**  */
    @Column(name = "t.TUESDAY_TIME1_VS_PERSON")
    private Short tuesdayTime1VSPerson;

    /**  */
    @Column(name = "t.TUESDAY_TIME1_VS_DOOR")
    private Short tuesdayTime1VSDoor;

    /**  */
    @Column(name = "t.TUESDAY_TIME2_VS_PERSON")
    private Short tuesdayTime2VSPerson;

    /**  */
    @Column(name = "t.TUESDAY_TIME2_VS_DOOR")
    private Short tuesdayTime2VSDoor;

    /**  */
    @Column(name = "t.TUESDAY_TIME3_VS_PERSON")
    private Short tuesdayTime3VSPerson;

    /**  */
    @Column(name = "t.TUESDAY_TIME3_VS_DOOR")
    private Short tuesdayTime3VSDoor;

    /**  */
    @Column(name = "t.WEDNESDAY_TIME1_VS_PERSON")
    private Short wednesdayTime1VSPerson;

    /**  */
    @Column(name = "t.WEDNESDAY_TIME1_VS_DOOR")
    private Short wednesdayTime1VSDoor;

    /**  */
    @Column(name = "t.WEDNESDAY_TIME2_VS_PERSON")
    private Short wednesdayTime2VSPerson;

    /**  */
    @Column(name = "t.WEDNESDAY_TIME2_VS_DOOR")
    private Short wednesdayTime2VSDoor;

    /**  */
    @Column(name = "t.WEDNESDAY_TIME3_VS_PERSON")
    private Short wednesdayTime3VSPerson;

    /**  */
    @Column(name = "t.WEDNESDAY_TIME3_VS_DOOR")
    private Short wednesdayTime3VSDoor;

    /**  */
    @Column(name = "t.THURSDAY_TIME1_VS_PERSON")
    private Short thursdayTime1VSPerson;

    /**  */
    @Column(name = "t.THURSDAY_TIME1_VS_DOOR")
    private Short thursdayTime1VSDoor;

    /**  */
    @Column(name = "t.THURSDAY_TIME2_VS_PERSON")
    private Short thursdayTime2VSPerson;

    /**  */
    @Column(name = "t.THURSDAY_TIME2_VS_DOOR")
    private Short thursdayTime2VSDoor;

    /**  */
    @Column(name = "t.THURSDAY_TIME3_VS_PERSON")
    private Short thursdayTime3VSPerson;

    /**  */
    @Column(name = "t.THURSDAY_TIME3_VS_DOOR")
    private Short thursdayTime3VSDoor;

    /**  */
    @Column(name = "t.FRIDAY_TIME1_VS_PERSON")
    private Short fridayTime1VSPerson;

    /**  */
    @Column(name = "t.FRIDAY_TIME1_VS_DOOR")
    private Short fridayTime1VSDoor;

    /**  */
    @Column(name = "t.FRIDAY_TIME2_VS_PERSON")
    private Short fridayTime2VSPerson;

    /**  */
    @Column(name = "t.FRIDAY_TIME2_VS_DOOR")
    private Short fridayTime2VSDoor;

    /**  */
    @Column(name = "t.FRIDAY_TIME3_VS_PERSON")
    private Short fridayTime3VSPerson;

    /**  */
    @Column(name = "t.FRIDAY_TIME3_VS_DOOR")
    private Short fridayTime3VSDoor;

    /**  */
    @Column(name = "t.SATURDAY_TIME1_VS_PERSON")
    private Short saturdayTime1VSPerson;

    /**  */
    @Column(name = "t.SATURDAY_TIME1_VS_DOOR")
    private Short saturdayTime1VSDoor;

    /**  */
    @Column(name = "t.SATURDAY_TIME2_VS_PERSON")
    private Short saturdayTime2VSPerson;

    /**  */
    @Column(name = "t.SATURDAY_TIME2_VS_DOOR")
    private Short saturdayTime2VSDoor;

    /**  */
    @Column(name = "t.SATURDAY_TIME3_VS_PERSON")
    private Short saturdayTime3VSPerson;

    /**  */
    @Column(name = "t.SATURDAY_TIME3_VS_DOOR")
    private Short saturdayTime3VSDoor;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_TIME1_VS_PERSON")
    private Short holidayType1Time1VSPerson;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_TIME1_VS_DOOR")
    private Short holidayType1Time1VSDoor;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_TIME2_VS_PERSON")
    private Short holidayType1Time2VSPerson;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_TIME2_VS_DOOR")
    private Short holidayType1Time2VSDoor;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_TIME3_VS_PERSON")
    private Short holidayType1Time3VSPerson;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_TIME3_VS_DOOR")
    private Short holidayType1Time3VSDoor;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_TIME1_VS_PERSON")
    private Short holidayType2Time1VSPerson;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_TIME1_VS_DOOR")
    private Short holidayType2Time1VSDoor;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_TIME2_VS_PERSON")
    private Short holidayType2Time2VSPerson;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_TIME2_VS_DOOR")
    private Short holidayType2Time2VSDoor;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_TIME3_VS_PERSON")
    private Short holidayType2Time3VSPerson;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_TIME3_VS_DOOR")
    private Short holidayType2Time3VSDoor;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_TIME1_VS_PERSON")
    private Short holidayType3Time1VSPerson;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_TIME1_VS_DOOR")
    private Short holidayType3Time1VSDoor;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_TIME2_VS_PERSON")
    private Short holidayType3Time2VSPerson;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_TIME2_VS_DOOR")
    private Short holidayType3Time2VSDoor;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_TIME3_VS_PERSON")
    private Short holidayType3Time3VSPerson;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_TIME3_VS_DOOR")
    private Short holidayType3Time3VSDoor;

    /** 是否为新验证方式,否为0,是为1 */
    @Column(name = "t.NEW_VERIFY_MODE")
    private Short newVerifyMode;

    /**
     * 默认构造方法
     */
    public AccVerifyModeRuleItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccVerifyModeRuleItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccVerifyModeRuleItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     * @param sundayTime1VSPerson
     * @param sundayTime1VSDoor
     * @param sundayTime2VSPerson
     * @param sundayTime2VSDoor
     * @param sundayTime3VSPerson
     * @param sundayTime3VSDoor
     * @param mondayTime1VSPerson
     * @param mondayTime1VSDoor
     * @param mondayTime2VSPerson
     * @param mondayTime2VSDoor
     * @param mondayTime3VSPerson
     * @param mondayTime3VSDoor
     * @param tuesdayTime1VSPerson
     * @param tuesdayTime1VSDoor
     * @param tuesdayTime2VSPerson
     * @param tuesdayTime2VSDoor
     * @param tuesdayTime3VSPerson
     * @param tuesdayTime3VSDoor
     * @param wednesdayTime1VSPerson
     * @param wednesdayTime1VSDoor
     * @param wednesdayTime2VSPerson
     * @param wednesdayTime2VSDoor
     * @param wednesdayTime3VSPerson
     * @param wednesdayTime3VSDoor
     * @param thursdayTime1VSPerson
     * @param thursdayTime1VSDoor
     * @param thursdayTime2VSPerson
     * @param thursdayTime2VSDoor
     * @param thursdayTime3VSPerson
     * @param thursdayTime3VSDoor
     * @param fridayTime1VSPerson
     * @param fridayTime1VSDoor
     * @param fridayTime2VSPerson
     * @param fridayTime2VSDoor
     * @param fridayTime3VSPerson
     * @param fridayTime3VSDoor
     * @param saturdayTime1VSPerson
     * @param saturdayTime1VSDoor
     * @param saturdayTime2VSPerson
     * @param saturdayTime2VSDoor
     * @param saturdayTime3VSPerson
     * @param saturdayTime3VSDoor
     * @param holidayType1Time1VSPerson
     * @param holidayType1Time1VSDoor
     * @param holidayType1Time2VSPerson
     * @param holidayType1Time2VSDoor
     * @param holidayType1Time3VSPerson
     * @param holidayType1Time3VSDoor
     * @param holidayType2Time1VSPerson
     * @param holidayType2Time1VSDoor
     * @param holidayType2Time2VSPerson
     * @param holidayType2Time2VSDoor
     * @param holidayType2Time3VSPerson
     * @param holidayType2Time3VSDoor
     * @param holidayType3Time1VSPerson
     * @param holidayType3Time1VSDoor
     * @param holidayType3Time2VSPerson
     * @param holidayType3Time2VSDoor
     * @param holidayType3Time3VSPerson
     * @param holidayType3Time3VSDoor
     */
    public AccVerifyModeRuleItem(String id, String name, Short sundayTime1VSPerson, Short sundayTime1VSDoor,
        Short sundayTime2VSPerson, Short sundayTime2VSDoor, Short sundayTime3VSPerson, Short sundayTime3VSDoor,
        Short mondayTime1VSPerson, Short mondayTime1VSDoor, Short mondayTime2VSPerson, Short mondayTime2VSDoor,
        Short mondayTime3VSPerson, Short mondayTime3VSDoor, Short tuesdayTime1VSPerson, Short tuesdayTime1VSDoor,
        Short tuesdayTime2VSPerson, Short tuesdayTime2VSDoor, Short tuesdayTime3VSPerson, Short tuesdayTime3VSDoor,
        Short wednesdayTime1VSPerson, Short wednesdayTime1VSDoor, Short wednesdayTime2VSPerson,
        Short wednesdayTime2VSDoor, Short wednesdayTime3VSPerson, Short wednesdayTime3VSDoor,
        Short thursdayTime1VSPerson, Short thursdayTime1VSDoor, Short thursdayTime2VSPerson, Short thursdayTime2VSDoor,
        Short thursdayTime3VSPerson, Short thursdayTime3VSDoor, Short fridayTime1VSPerson, Short fridayTime1VSDoor,
        Short fridayTime2VSPerson, Short fridayTime2VSDoor, Short fridayTime3VSPerson, Short fridayTime3VSDoor,
        Short saturdayTime1VSPerson, Short saturdayTime1VSDoor, Short saturdayTime2VSPerson, Short saturdayTime2VSDoor,
        Short saturdayTime3VSPerson, Short saturdayTime3VSDoor, Short holidayType1Time1VSPerson,
        Short holidayType1Time1VSDoor, Short holidayType1Time2VSPerson, Short holidayType1Time2VSDoor,
        Short holidayType1Time3VSPerson, Short holidayType1Time3VSDoor, Short holidayType2Time1VSPerson,
        Short holidayType2Time1VSDoor, Short holidayType2Time2VSPerson, Short holidayType2Time2VSDoor,
        Short holidayType2Time3VSPerson, Short holidayType2Time3VSDoor, Short holidayType3Time1VSPerson,
        Short holidayType3Time1VSDoor, Short holidayType3Time2VSPerson, Short holidayType3Time2VSDoor,
        Short holidayType3Time3VSPerson, Short holidayType3Time3VSDoor) {
        super();
        this.id = id;
        this.name = name;
        this.sundayTime1VSPerson = sundayTime1VSPerson;
        this.sundayTime1VSDoor = sundayTime1VSDoor;
        this.sundayTime2VSPerson = sundayTime2VSPerson;
        this.sundayTime2VSDoor = sundayTime2VSDoor;
        this.sundayTime3VSPerson = sundayTime3VSPerson;
        this.sundayTime3VSDoor = sundayTime3VSDoor;
        this.mondayTime1VSPerson = mondayTime1VSPerson;
        this.mondayTime1VSDoor = mondayTime1VSDoor;
        this.mondayTime2VSPerson = mondayTime2VSPerson;
        this.mondayTime2VSDoor = mondayTime2VSDoor;
        this.mondayTime3VSPerson = mondayTime3VSPerson;
        this.mondayTime3VSDoor = mondayTime3VSDoor;
        this.tuesdayTime1VSPerson = tuesdayTime1VSPerson;
        this.tuesdayTime1VSDoor = tuesdayTime1VSDoor;
        this.tuesdayTime2VSPerson = tuesdayTime2VSPerson;
        this.tuesdayTime2VSDoor = tuesdayTime2VSDoor;
        this.tuesdayTime3VSPerson = tuesdayTime3VSPerson;
        this.tuesdayTime3VSDoor = tuesdayTime3VSDoor;
        this.wednesdayTime1VSPerson = wednesdayTime1VSPerson;
        this.wednesdayTime1VSDoor = wednesdayTime1VSDoor;
        this.wednesdayTime2VSPerson = wednesdayTime2VSPerson;
        this.wednesdayTime2VSDoor = wednesdayTime2VSDoor;
        this.wednesdayTime3VSPerson = wednesdayTime3VSPerson;
        this.wednesdayTime3VSDoor = wednesdayTime3VSDoor;
        this.thursdayTime1VSPerson = thursdayTime1VSPerson;
        this.thursdayTime1VSDoor = thursdayTime1VSDoor;
        this.thursdayTime2VSPerson = thursdayTime2VSPerson;
        this.thursdayTime2VSDoor = thursdayTime2VSDoor;
        this.thursdayTime3VSPerson = thursdayTime3VSPerson;
        this.thursdayTime3VSDoor = thursdayTime3VSDoor;
        this.fridayTime1VSPerson = fridayTime1VSPerson;
        this.fridayTime1VSDoor = fridayTime1VSDoor;
        this.fridayTime2VSPerson = fridayTime2VSPerson;
        this.fridayTime2VSDoor = fridayTime2VSDoor;
        this.fridayTime3VSPerson = fridayTime3VSPerson;
        this.fridayTime3VSDoor = fridayTime3VSDoor;
        this.saturdayTime1VSPerson = saturdayTime1VSPerson;
        this.saturdayTime1VSDoor = saturdayTime1VSDoor;
        this.saturdayTime2VSPerson = saturdayTime2VSPerson;
        this.saturdayTime2VSDoor = saturdayTime2VSDoor;
        this.saturdayTime3VSPerson = saturdayTime3VSPerson;
        this.saturdayTime3VSDoor = saturdayTime3VSDoor;
        this.holidayType1Time1VSPerson = holidayType1Time1VSPerson;
        this.holidayType1Time1VSDoor = holidayType1Time1VSDoor;
        this.holidayType1Time2VSPerson = holidayType1Time2VSPerson;
        this.holidayType1Time2VSDoor = holidayType1Time2VSDoor;
        this.holidayType1Time3VSPerson = holidayType1Time3VSPerson;
        this.holidayType1Time3VSDoor = holidayType1Time3VSDoor;
        this.holidayType2Time1VSPerson = holidayType2Time1VSPerson;
        this.holidayType2Time1VSDoor = holidayType2Time1VSDoor;
        this.holidayType2Time2VSPerson = holidayType2Time2VSPerson;
        this.holidayType2Time2VSDoor = holidayType2Time2VSDoor;
        this.holidayType2Time3VSPerson = holidayType2Time3VSPerson;
        this.holidayType2Time3VSDoor = holidayType2Time3VSDoor;
        this.holidayType3Time1VSPerson = holidayType3Time1VSPerson;
        this.holidayType3Time1VSDoor = holidayType3Time1VSDoor;
        this.holidayType3Time2VSPerson = holidayType3Time2VSPerson;
        this.holidayType3Time2VSDoor = holidayType3Time2VSDoor;
        this.holidayType3Time3VSPerson = holidayType3Time3VSPerson;
        this.holidayType3Time3VSDoor = holidayType3Time3VSDoor;
    }
}