/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午10:18 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 门禁时间段vo
 *
 * @author: GenerationTools
 * @date: 2018-02-24 上午10:18
 */
@Getter
@Setter
@Accessors(chain = true)
@From(after = "ACC_TIMESEG t ")
@OrderBy(after = "t.CREATE_TIME ASC")
@GridConfig(operate = true, idField = "id", winHeight = 550, winWidth = 800,
    operates = {
        @GridOperate(type = "edit", permission = "acc:timeSeg:edit", url = "accTimeSeg.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:timeSeg:del", url = "accTimeSeg.do?del&names=(name)",
            label = "common_op_del", showConvertor = "showConvertor")})
public class AccTimeSegItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_timeSeg_name", width = "300", sortNo = 1, columnType = "edit",
        editPermission = "acc:timeSeg:edit", editUrl = "/accTimeSeg.do?edit")
    private String name;

    /**  */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "300", sortNo = 2)
    private String remark;

    /* BusinessID 下发设备中使用 */
    @Column(name = "t.BUSINESS_ID")
    private Long businessId;

    /**  */
    @Column(name = "t.INIT_FLAG")
    private Boolean initFlag;

    /** 时间段有效开始时间 */
    @Column(name = "t.START_TIME")
    private Date startTime;

    /** 时间段有效结束时间 */
    @Column(name = "t.END_TIME")
    private Date endTime;

    /**  */
    @Column(name = "t.SUNDAY_START1")
    private String sundayStart1;

    /**  */
    @Column(name = "t.SUNDAY_END1")
    private String sundayEnd1;

    /**  */
    @Column(name = "t.SUNDAY_START2")
    private String sundayStart2;

    /**  */
    @Column(name = "t.SUNDAY_END2")
    private String sundayEnd2;

    /**  */
    @Column(name = "t.SUNDAY_START3")
    private String sundayStart3;

    /**  */
    @Column(name = "t.SUNDAY_END3")
    private String sundayEnd3;

    /**  */
    @Column(name = "t.MONDAY_START1")
    private String mondayStart1;

    /**  */
    @Column(name = "t.MONDAY_END1")
    private String mondayEnd1;

    /**  */
    @Column(name = "t.MONDAY_START2")
    private String mondayStart2;

    /**  */
    @Column(name = "t.MONDAY_END2")
    private String mondayEnd2;

    /**  */
    @Column(name = "t.MONDAY_START3")
    private String mondayStart3;

    /**  */
    @Column(name = "t.MONDAY_END3")
    private String mondayEnd3;

    /**  */
    @Column(name = "t.TUESDAY_START1")
    private String tuesdayStart1;

    /**  */
    @Column(name = "t.TUESDAY_END1")
    private String tuesdayEnd1;

    /**  */
    @Column(name = "t.TUESDAY_START2")
    private String tuesdayStart2;

    /**  */
    @Column(name = "t.TUESDAY_END2")
    private String tuesdayEnd2;

    /**  */
    @Column(name = "t.TUESDAY_START3")
    private String tuesdayStart3;

    /**  */
    @Column(name = "t.TUESDAY_END3")
    private String tuesdayEnd3;

    /**  */
    @Column(name = "t.WEDNESDAY_START1")
    private String wednesdayStart1;

    /**  */
    @Column(name = "t.WEDNESDAY_END1")
    private String wednesdayEnd1;

    /**  */
    @Column(name = "t.WEDNESDAY_START2")
    private String wednesdayStart2;

    /**  */
    @Column(name = "t.WEDNESDAY_END2")
    private String wednesdayEnd2;

    /**  */
    @Column(name = "t.WEDNESDAY_START3")
    private String wednesdayStart3;

    /**  */
    @Column(name = "t.WEDNESDAY_END3")
    private String wednesdayEnd3;

    /**  */
    @Column(name = "t.THURSDAY_START1")
    private String thursdayStart1;

    /**  */
    @Column(name = "t.THURSDAY_END1")
    private String thursdayEnd1;

    /**  */
    @Column(name = "t.THURSDAY_START2")
    private String thursdayStart2;

    /**  */
    @Column(name = "t.THURSDAY_END2")
    private String thursdayEnd2;

    /**  */
    @Column(name = "t.THURSDAY_START3")
    private String thursdayStart3;

    /**  */
    @Column(name = "t.THURSDAY_END3")
    private String thursdayEnd3;

    /**  */
    @Column(name = "t.FRIDAY_START1")
    private String fridayStart1;

    /**  */
    @Column(name = "t.FRIDAY_END1")
    private String fridayEnd1;

    /**  */
    @Column(name = "t.FRIDAY_START2")
    private String fridayStart2;

    /**  */
    @Column(name = "t.FRIDAY_END2")
    private String fridayEnd2;

    /**  */
    @Column(name = "t.FRIDAY_START3")
    private String fridayStart3;

    /**  */
    @Column(name = "t.FRIDAY_END3")
    private String fridayEnd3;

    /**  */
    @Column(name = "t.SATURDAY_START1")
    private String saturdayStart1;

    /**  */
    @Column(name = "t.SATURDAY_END1")
    private String saturdayEnd1;

    /**  */
    @Column(name = "t.SATURDAY_START2")
    private String saturdayStart2;

    /**  */
    @Column(name = "t.SATURDAY_END2")
    private String saturdayEnd2;

    /**  */
    @Column(name = "t.SATURDAY_START3")
    private String saturdayStart3;

    /**  */
    @Column(name = "t.SATURDAY_END3")
    private String saturdayEnd3;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_START1")
    private String holidayType1Start1;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_END1")
    private String holidayType1End1;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_START2")
    private String holidayType1Start2;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_END2")
    private String holidayType1End2;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_START3")
    private String holidayType1Start3;

    /**  */
    @Column(name = "t.HOLIDAYTYPE1_END3")
    private String holidayType1End3;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_START1")
    private String holidayType2Start1;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_END1")
    private String holidayType2End1;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_START2")
    private String holidayType2Start2;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_END2")
    private String holidayType2End2;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_START3")
    private String holidayType2Start3;

    /**  */
    @Column(name = "t.HOLIDAYTYPE2_END3")
    private String holidayType2End3;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_START1")
    private String holidayType3Start1;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_END1")
    private String holidayType3End1;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_START2")
    private String holidayType3Start2;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_END2")
    private String holidayType3End2;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_START3")
    private String holidayType3Start3;

    /**  */
    @Column(name = "t.HOLIDAYTYPE3_END3")
    private String holidayType3End3;

    /**
     * 默认构造方法
     */
    public AccTimeSegItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccTimeSegItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccTimeSegItem(String id) {
        super(true);
        this.id = id;
    }

    public AccTimeSegItem(String name, String remark, Boolean initFlag, String sundayStart1, String sundayEnd1,
        String sundayStart2, String sundayEnd2, String sundayStart3, String sundayEnd3, String mondayStart1,
        String mondayEnd1, String mondayStart2, String mondayEnd2, String mondayStart3, String mondayEnd3,
        String tuesdayStart1, String tuesdayEnd1, String tuesdayStart2, String tuesdayEnd2, String tuesdayStart3,
        String tuesdayEnd3, String wednesdayStart1, String wednesdayEnd1, String wednesdayStart2, String wednesdayEnd2,
        String wednesdayStart3, String wednesdayEnd3, String thursdayStart1, String thursdayEnd1, String thursdayStart2,
        String thursdayEnd2, String thursdayStart3, String thursdayEnd3, String fridayStart1, String fridayEnd1,
        String fridayStart2, String fridayEnd2, String fridayStart3, String fridayEnd3, String saturdayStart1,
        String saturdayEnd1, String saturdayStart2, String saturdayEnd2, String saturdayStart3, String saturdayEnd3,
        String holidayType1Start1, String holidayType1End1, String holidayType1Start2, String holidayType1End2,
        String holidayType1Start3, String holidayType1End3, String holidayType2Start1, String holidayType2End1,
        String holidayType2Start2, String holidayType2End2, String holidayType2Start3, String holidayType2End3,
        String holidayType3Start1, String holidayType3End1, String holidayType3Start2, String holidayType3End2,
        String holidayType3Start3, String holidayType3End3) {
        this.name = name;
        this.remark = remark;
        this.initFlag = initFlag;
        this.sundayStart1 = sundayStart1;
        this.sundayEnd1 = sundayEnd1;
        this.sundayStart2 = sundayStart2;
        this.sundayEnd2 = sundayEnd2;
        this.sundayStart3 = sundayStart3;
        this.sundayEnd3 = sundayEnd3;
        this.mondayStart1 = mondayStart1;
        this.mondayEnd1 = mondayEnd1;
        this.mondayStart2 = mondayStart2;
        this.mondayEnd2 = mondayEnd2;
        this.mondayStart3 = mondayStart3;
        this.mondayEnd3 = mondayEnd3;
        this.tuesdayStart1 = tuesdayStart1;
        this.tuesdayEnd1 = tuesdayEnd1;
        this.tuesdayStart2 = tuesdayStart2;
        this.tuesdayEnd2 = tuesdayEnd2;
        this.tuesdayStart3 = tuesdayStart3;
        this.tuesdayEnd3 = tuesdayEnd3;
        this.wednesdayStart1 = wednesdayStart1;
        this.wednesdayEnd1 = wednesdayEnd1;
        this.wednesdayStart2 = wednesdayStart2;
        this.wednesdayEnd2 = wednesdayEnd2;
        this.wednesdayStart3 = wednesdayStart3;
        this.wednesdayEnd3 = wednesdayEnd3;
        this.thursdayStart1 = thursdayStart1;
        this.thursdayEnd1 = thursdayEnd1;
        this.thursdayStart2 = thursdayStart2;
        this.thursdayEnd2 = thursdayEnd2;
        this.thursdayStart3 = thursdayStart3;
        this.thursdayEnd3 = thursdayEnd3;
        this.fridayStart1 = fridayStart1;
        this.fridayEnd1 = fridayEnd1;
        this.fridayStart2 = fridayStart2;
        this.fridayEnd2 = fridayEnd2;
        this.fridayStart3 = fridayStart3;
        this.fridayEnd3 = fridayEnd3;
        this.saturdayStart1 = saturdayStart1;
        this.saturdayEnd1 = saturdayEnd1;
        this.saturdayStart2 = saturdayStart2;
        this.saturdayEnd2 = saturdayEnd2;
        this.saturdayStart3 = saturdayStart3;
        this.saturdayEnd3 = saturdayEnd3;
        this.holidayType1Start1 = holidayType1Start1;
        this.holidayType1End1 = holidayType1End1;
        this.holidayType1Start2 = holidayType1Start2;
        this.holidayType1End2 = holidayType1End2;
        this.holidayType1Start3 = holidayType1Start3;
        this.holidayType1End3 = holidayType1End3;
        this.holidayType2Start1 = holidayType2Start1;
        this.holidayType2End1 = holidayType2End1;
        this.holidayType2Start2 = holidayType2Start2;
        this.holidayType2End2 = holidayType2End2;
        this.holidayType2Start3 = holidayType2Start3;
        this.holidayType2End3 = holidayType2End3;
        this.holidayType3Start1 = holidayType3Start1;
        this.holidayType3End1 = holidayType3End1;
        this.holidayType3Start2 = holidayType3Start2;
        this.holidayType3End2 = holidayType3End2;
        this.holidayType3Start3 = holidayType3Start3;
        this.holidayType3End3 = holidayType3End3;
    }

    public boolean showConvertor() {
        if (this.initFlag != null) {
            return !this.initFlag;
        }
        return true;
    }

}