package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;

import lombok.Getter;
import lombok.Setter;

/**
 * 事件类型列表控件下拉VO
 *
 * <AUTHOR>
 * @date 2023/2/24 15:36
 */
@Getter
@Setter
@GridConfig
public class AccDeviceEventSelectItem {

    @GridColumn(checkbox = true, width = "40")
    private String id;

    @GridColumn(label = "common_event_name", i18n = true)
    private String name;
}
