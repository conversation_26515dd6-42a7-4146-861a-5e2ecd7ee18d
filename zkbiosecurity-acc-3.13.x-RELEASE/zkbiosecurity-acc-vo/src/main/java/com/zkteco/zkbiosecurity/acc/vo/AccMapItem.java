/**
 * @author: GenerationTools
 * @date: 2018-03-20 下午02:07 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 *
 * 
 * @author: GenerationTools
 * @date: 2018-03-20 下午02:07
 */
@From(after = "ACC_MAP t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccMapItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.AUTH_AREA_ID")
    @GridColumn(label = "acc_accMap_baseArea")
    private String authAreaId;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "acc_accMap_name")
    private String name;

    /**  */
    @Column(name = "t.MAP_PATH")
    @GridColumn(label = "acc_accMap_mapPath")
    private String mapPath;

    /**  */
    @Column(name = "t.WIDTH")
    @GridColumn(label = "acc_accMap_width")
    private Double width;

    /**  */
    @Column(name = "t.HEIGHT")
    @GridColumn(label = "acc_accMap_height")
    private Double height;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "t.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccMapItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccMapItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccMapItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     * @param mapPath
     * @param width
     * @param height
     */
    public AccMapItem(String id, String name, String mapPath, Double width, Double height) {
        super();
        this.id = id;
        this.name = name;
        this.mapPath = mapPath;
        this.width = width;
        this.height = height;
    }
}