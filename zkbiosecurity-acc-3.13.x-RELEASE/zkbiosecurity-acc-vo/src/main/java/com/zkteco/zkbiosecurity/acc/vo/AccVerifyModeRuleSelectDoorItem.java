package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@From(after = "ACC_DOOR t LEFT JOIN ACC_DEVICE d ON d.ID=t.DEV_ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AccVerifyModeRuleSelectDoorItem extends BaseItem {

    /** 主键 */
    @Column(name="t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "acc_door_name",sortNo = 1,width = "140")
    private String doorName;

    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev",sortNo = 2,width = "120")
    private String deviceAlias;

    @Column(name = "d.SN")
    @GridColumn(label = "common_dev_sn",sortNo = 3, minWidth = "210")
    private String deviceSn;

    @Column(name = "d.ID")
    private String deviceId;

    private String type;

    @Condition(value = "t.ID NOT IN (SELECT ad.ACC_DOOR_ID FROM ACC_DOOR_VERIFYMODERULE ad WHERE ad.ACC_VERIFYMODERULE_ID = ''{0}'')")
    private String filterId;

    private String selectId;

    @Column(name = "t.ID", equalTag = "in")
    private String selectDoorIdsIn;

    @Column(name = "t.ID", equalTag = "not in")
    private String selectDoorIdsNotIn;

    @Column(name = "t.ENABLED")
    private Boolean enabled;

    private String verifyModeRuleId;

    @Condition(value = "d.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;
}
