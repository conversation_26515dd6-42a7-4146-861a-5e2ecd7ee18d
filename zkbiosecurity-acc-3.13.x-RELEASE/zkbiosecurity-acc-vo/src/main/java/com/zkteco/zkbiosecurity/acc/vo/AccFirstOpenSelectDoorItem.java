package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

@From(after = "ACC_DOOR t LEFT JOIN ACC_DEVICE d ON d.ID=t.DEV_ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
public class AccFirstOpenSelectDoorItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(columnType = "ra", width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "acc_door_name", sortNo = 1, width = "140")
    private String doorName;

    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev", sortNo = 2, width = "130")
    private String deviceAlias;

    @Column(name = "d.SN")
    @GridColumn(label = "common_dev_sn", sortNo = 3, minWidth = "210")
    private String deviceSn;

    @Column(name = "d.ID")
    private String deviceId;

    private String type;

    @Condition(value = "t.ID NOT IN (SELECT af.DOOR_ID FROM ACC_FIRSTOPEN af )")
    private String selectId;

    @Column(name = "t.ID", equalTag = "in")
    private String selectDoorIdsIn;

    @Column(name = "t.ID", equalTag = "not in")
    private String selectDoorIdsNotIn;

    @Condition(value = "d.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    @Column(name = "t.ENABLED")
    private Boolean enabled;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "d.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;
}
