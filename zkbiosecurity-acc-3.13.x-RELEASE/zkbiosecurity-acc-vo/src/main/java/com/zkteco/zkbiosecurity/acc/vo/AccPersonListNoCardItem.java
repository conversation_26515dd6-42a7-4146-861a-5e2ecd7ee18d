package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

@GridConfig(operate = false, idField = "id")
@Getter
@Setter
public class AccPersonListNoCardItem extends BaseItem {
    /** 主键 */
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @GridColumn(label = "pers_person_pin", sortNo = 1, width = "100", encryptMode = "${pers.pin.encryptMode}",
        encryptProp = "${pers.pin.encryptProp}")
    private String personPin;

    @GridColumn(label = "pers_person_name", sortNo = 2, width = "100", encryptMode = "${pers.name.encryptMode}",
        encryptProp = "${pers.name.encryptProp}")
    private String personName;

    @GridColumn(label = "pers_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3, width = "100",
        encryptMode = "${pers.lastName.encryptMode}", encryptProp = "${pers.lastName.encryptProp}")
    private String personLastName;

    @GridColumn(label = "pers_dept_entity", sortNo = 4, width = "*")
    private String deptName;

    private String linkId; // 选人控件对应的模块ID

    private String modelType; // 对应模块类型

    private String likeName;
}
