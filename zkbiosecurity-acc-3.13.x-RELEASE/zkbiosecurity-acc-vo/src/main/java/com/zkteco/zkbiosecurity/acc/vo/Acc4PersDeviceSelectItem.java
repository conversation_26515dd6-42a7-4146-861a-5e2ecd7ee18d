package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 针对卡格式测试，选择设备的双列表的栅格显示
 * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @date 2018/7/25 11:27
 * @return
 */
@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public  class Acc4PersDeviceSelectItem extends BaseItem {

	private static final long serialVersionUID = 1L;

	@GridColumn(columnType = "ra", width = "40", sortNo = 0, sort = "na")
	private String id;

	@GridColumn(label="common_dev_name", sortNo = 1, width = "*")
	private String deviceAlias;

	@GridColumn(label = "common_dev_sn", sortNo = 2, width = "*")
	private String deviceSn;

	private String deviceName;

	private String notInId;

	private String inId;

	// 判断是左列表（值为noSelect）还是右列表（值为select）
	private String type;

	// 选中人员的ID
	private String selectId;

}
