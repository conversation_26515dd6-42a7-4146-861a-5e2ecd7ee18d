package com.zkteco.zkbiosecurity.acc.api.vo;

import com.zkteco.zkbiosecurity.acc.vo.AccLevelItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class AccApiLevelAddItem implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(allowableValues="通用权限组")
    private String name;

    @ApiModelProperty
    private String areaName;


    @ApiModelProperty
    private String timeSegName;


}
