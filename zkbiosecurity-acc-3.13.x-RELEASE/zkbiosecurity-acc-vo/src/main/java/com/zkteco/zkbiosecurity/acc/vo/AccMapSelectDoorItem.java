package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

@From(after = "ACC_DOOR t "
            + "LEFT JOIN ACC_DEVICE d ON d.ID=t.DEV_ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
public class AccMapSelectDoorItem extends BaseItem {

    /** 主键 */
    @Column(name="t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "acc_door_name",sortNo = 1,width = "120")
    private String doorName;

    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev",sortNo = 2,width = "120")
    private String deviceAlias;

    @Column(name = "d.SN")
    @GridColumn(label = "common_dev_sn",sortNo = 3,width = "*")
    private String deviceSn;

    @Column(name = "d.ID")
    private String deviceId;

    @Column(name = "t.ID", equalTag="in")
    private String inId;

    @Column(name = "t.ID", equalTag="not in")
    private String notInId;

    @Condition(value = "t.ID NOT IN (SELECT amp.ENTITY_ID FROM ACC_MAP_POS amp WHERE amp.ENTITY_TYPE = 'AccDoor' AND amp.MAP_ID = '%s')")
    private String filterId;

    private String type;

    private String selectId;

    @Condition(value = "d.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Column(name="t.ENABLED")
    private Boolean enabled;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDoorName() {
        return doorName;
    }

    public void setDoorName(String doorName) {
        this.doorName = doorName;
    }

    public String getDeviceAlias() {
        return deviceAlias;
    }

    public void setDeviceAlias(String deviceAlias) {
        this.deviceAlias = deviceAlias;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getInId() {
        return inId;
    }

    public void setInId(String inId) {
        this.inId = inId;
    }

    public String getNotInId() {
        return notInId;
    }

    public void setNotInId(String notInId) {
        this.notInId = notInId;
    }

    public String getFilterId() {
        return filterId;
    }

    public void setFilterId(String filterId) {
        this.filterId = filterId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSelectId() {
        return selectId;
    }

    public void setSelectId(String selectId) {
        this.selectId = selectId;
    }

    public String getAreaIdIn() {
        return areaIdIn;
    }

    public void setAreaIdIn(String areaIdIn) {
        this.areaIdIn = areaIdIn;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}
