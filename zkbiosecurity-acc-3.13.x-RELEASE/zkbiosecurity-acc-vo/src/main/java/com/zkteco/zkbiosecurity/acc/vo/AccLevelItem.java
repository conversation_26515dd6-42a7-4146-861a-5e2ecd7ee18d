/**
 * @author: GenerationTools
 * @date: 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 门禁权限组vo
 * 
 * @author: yulong.dai
 * @date: 2018-03-02 下午02:15
 */
@From(
    after = "ACC_LEVEL t LEFT JOIN ACC_TIMESEG ts ON t.TIMESEG_ID = ts.ID LEFT JOIN AUTH_AREA a ON a.id = t.auth_area_id ")
@OrderBy(after = "t.CREATE_TIME ASC")
@GridConfig(operate = true, idField = "id", winHeight = 200, winWidth = 400,
    operates = {
        @GridOperate(type = "edit", permission = "acc:level:edit", url = "accLevel.do?edit", label = "common_op_edit"),
        @GridOperate(type = "custom", click = "accLevelAddDoor", permission = "acc:level:addDoor",
            label = "acc_map_addDoor")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccLevelItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /** 权限组名称 */
    @Column(name = "t.NAME")
    @GridColumn(columnType = "edit", label = "common_level_name", width = "110", sortNo = 1,
        editPermission = "acc:level:edit", editUrl = "/accLevel.do?edit")
    private String name;

    /** 系统区域id */
    @Column(name = "t.AUTH_AREA_ID")
    private String authAreaId;

    @Column(name = "a.NAME")
    @GridColumn(label = "base_area_name", width = "90", sortNo = 2, sort = "na")
    private String authAreaName;

    @Column(name = "ts.ID")
    private String timeSegId;

    /**  */
    @Column(name = "ts.NAME")
    @GridColumn(label = "acc_timeSeg_entity", width = "90", sortNo = 3)
    private String timeSegName;

    @GridColumn(label = "acc_level_doorCount", width = "90", sortNo = 4, sort = "na", isExportExcel = false)
    private String doorCount;

    @Column(name = "t.INIT_FLAG")
    private Boolean initFlag;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    private Boolean changeLevel;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String areaIds;

    // ---pro---
    /** 威胁等级 */
    private String threatLevel;

    /** 用来展示threatLevel的字段 **/
    private String threatLevelName;

    /** 开始日期 */
    @Column(name = "t.START_DATE")
    private String startDate;

    /** 结束日期 */
    @Column(name = "t.END_DATE")
    private String endDate;

    /** BusinessID 下发设备中使用 mqtt */
    @Column(name = "t.BUSINESS_ID")
    private Long businessId;

    @Condition(
        value = "t.AUTH_AREA_ID IN (SELECT ua.AUTH_AREA_ID FROM AUTH_USER_AREA ua WHERE ua.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccLevelItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccLevelItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccLevelItem(String id) {
        super(true);
        this.id = id;
    }

    public AccLevelItem(String name, Boolean initFlag, String authAreaId, String timeSegId) {
        this.name = name;
        this.initFlag = initFlag;
        this.authAreaId = authAreaId;
        this.timeSegId = timeSegId;
    }
}