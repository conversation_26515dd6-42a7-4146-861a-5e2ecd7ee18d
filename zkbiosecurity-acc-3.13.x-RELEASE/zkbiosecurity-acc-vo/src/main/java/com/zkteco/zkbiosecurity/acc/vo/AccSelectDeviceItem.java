package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

@From(after = "ACC_DEVICE t LEFT JOIN AUTH_AREA a on t.auth_area_id = a.id ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
public class AccSelectDeviceItem extends BaseItem {

    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /** 设备名称 */
    @Column(name = "t.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", sortNo = 1, width = "120")
    private String deviceAlias;

    /** 设备型号 */
    @Column(name = "t.DEVICE_NAME")
    @GridColumn(label = "common_dev_deviceModel", sortNo = 2, width = "90")
    private String deviceName;

    /** 区域ID */
    @Column(name = "t.AUTH_AREA_ID", equalTag = "=")
    private String authAreaId;

    /** 区域名称 */
    @Column(name = "a.NAME")
    @GridColumn(label = "base_area_name", width = "90", sortNo = 4)
    private String authAreaName;

    /** 设备序列号 */
    @Column(name = "t.SN")
    @GridColumn(label = "common_dev_sn", sortNo = 3, width = "*")
    private String deviceSn;

    /** IP地址 */
    @Column(name = "t.IP_ADDRESS")
    private String ipAddress;

    @Column(name = "t.ID", equalTag = "in")
    private String inId;

    @Column(name = "t.ID", equalTag = "not in")
    private String notInId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Condition(value = "t.MACHINE_TYPE NOT IN ({0})")
    private Short machineTypeNoEquals;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(value = "a.ID IN (SELECT ua.AUTH_AREA_ID FROM AUTH_USER_AREA ua WHERE ua.AUTH_USER_ID=''{0}'')")
    private String userId;

}
