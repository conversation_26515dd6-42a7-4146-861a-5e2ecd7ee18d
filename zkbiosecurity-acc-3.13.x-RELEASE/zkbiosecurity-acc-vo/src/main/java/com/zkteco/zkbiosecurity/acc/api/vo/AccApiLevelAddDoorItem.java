package com.zkteco.zkbiosecurity.acc.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class AccApiLevelAddDoorItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /*门名称*/
    @ApiModelProperty(allowableValues="10.8.14.162-1")
    private String doorName;

    /*权限组*/
    @ApiModelProperty(allowableValues="通用权限组")
    private String levelName;

}
