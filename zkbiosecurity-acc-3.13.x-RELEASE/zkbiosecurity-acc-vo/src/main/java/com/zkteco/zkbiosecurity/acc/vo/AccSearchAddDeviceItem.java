package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2018/4/17 13:33
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccSearchAddDeviceItem implements Serializable {

    /* 操作类型：修改设备IP、修改服务器IP */
    private String type;
    /* 服务器IP */
    private String webServerIP;
    /* 服务器端口 */
    private String webServerPort;
    /* mac地址 */
    private String mac;
    /* 是否删除设备数据 */
    private String clearAllData;
    /* 序列号 */
    private String sn;
    /* 区域Id */
    private String authAreaId;
    /* 设备名称 */
    private String devName;
    /* 新DNS */
    private String newDNS;
    /* 新服务器URL */
    private String newWebServerURL;
    /* 是否支持HTTPS */
    private String isSupportSSL;
    /* 图标类型 */
    private String iconType;
    /* 4门转2门 */
    private String door4ToDoor2;
    /* 子网掩码 */
    private String subnetMask;
    /* 网关 */
    private String gateway;
    /* 通信密码 */
    private String commPwd;
    /* 新IP地址 */
    private String newIP;
    /* 服务器地址类型 */
    private String newWebServerType;
    /* 权限组ID */
    private String levelId;

    /** pro Mqtt设备 时区 */
    private String timezone;

    /* 设备原ip */
    private String ipAddress;

    // nvr用户名密码
    private String accUsername;
    private String accPassword;

}
