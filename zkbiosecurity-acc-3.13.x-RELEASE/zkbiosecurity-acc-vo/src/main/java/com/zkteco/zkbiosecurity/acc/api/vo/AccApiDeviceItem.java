package com.zkteco.zkbiosecurity.acc.api.vo;

import com.zkteco.zkbiosecurity.acc.vo.AccDeviceItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备-VO
 * @Auther: lambert.li
 * @Date: 2018/11/7 18:01
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccApiDeviceItem implements Serializable {

    /* 设备ID */
    private String id;

    /* 设备SN */
    private String sn;

    /* 设备名称 */
    private String name;

    /* 设备类型 */
    private String type;

    /* 设备状态 */
    private String status;

    /* 设备模块 */
    private String module;

    public static AccApiDeviceItem createAccDevice(AccDeviceItem accDeviceItem) {
        AccApiDeviceItem apiDeviceItem = null;
        if (accDeviceItem != null) {
            apiDeviceItem = new AccApiDeviceItem();
            apiDeviceItem.setId(accDeviceItem.getId());
            apiDeviceItem.setName(accDeviceItem.getAlias());
            apiDeviceItem.setSn(accDeviceItem.getSn());
            apiDeviceItem.setType(accDeviceItem.getDeviceName());
            apiDeviceItem.setModule("acc");
            apiDeviceItem.setStatus(accDeviceItem.getEnabled()? "1" : "0");
        }
        return apiDeviceItem;
    }
}
