package com.zkteco.zkbiosecurity.acc.app.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 2018/12/12 13:46
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccAppChatObject {
    private String userName;
    private String message;

    public AccAppChatObject() {

    }

    public AccAppChatObject(String userName, String message) {
        super();
        this.userName = userName;
        this.message = message;
    }
}
