/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

@GridConfig(operate = false, idField = "id")
@Getter
@Setter
public class AccRTMonitorItem extends BaseItem implements Serializable {

    /**
     * 说明成员变量的意义,目的,功能,可能被用到的地方
     */
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @GridColumn(checkbox = false, width = "40", show = false)
    private String id;

    @GridColumn(label = "common_time", width = "150")
    private String eventTime;

    @GridColumn(label = "base_area_entity", width = "100")
    private String areaName;

    @GridColumn(label = "common_dev_entity", width = "190")
    private String devAlias;

    @GridColumn(label = "common_eventPoint", width = "140")
    private String eventPointName;

    @GridColumn(label = "common_eventDescription", width = "150")
    private String eventName;

    @GridColumn(label = "pers_card_cardNo", width = "100", encryptMode = "${pers.cardNo.encryptMode}",
        encryptProp = "${pers.cardNo.encryptProp}")
    private int cardNo;

    @GridColumn(label = "pers_person", width = "100", encryptMode = "${pers.name.encryptMode}",
        encryptProp = "${pers.name.encryptProp}")
    private String pinName;

    @GridColumn(label = "acc_readerDefine_readerName", width = "145")
    private String readerName;

    @GridColumn(label = "common_verifyMode_entiy", width = "160")
    private String verifyModeName;

    @GridColumn(label = "")
    private String filter;

    /**
     * @return filter
     */
    public String getFilter() {
        return filter;
    }

    /**
     * @param filter 要设置的 filter
     */
    public void setFilter(String filter) {
        this.filter = filter;
    }

    /**
     * 默认构造方法
     */
    public AccRTMonitorItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccRTMonitorItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccRTMonitorItem(String id) {
        super(true);
        this.id = id;
    }
}