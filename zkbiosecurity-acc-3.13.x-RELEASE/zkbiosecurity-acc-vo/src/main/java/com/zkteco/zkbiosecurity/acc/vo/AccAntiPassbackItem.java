/**
 * @author: GenerationTools
 * @date: 2018-03-13 上午10:27 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-13 上午10:27
 */
@From(after = "ACC_ANTIPASSBACK t LEFT JOIN ACC_DEVICE p ON t.DEV_ID = p.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 500, winWidth = 520,
    operates = {
        @GridOperate(type = "edit", permission = "acc:antiPassback:edit", url = "accAntiPassback.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:antiPassback:del",
            url = "accAntiPassback.do?del&deviceNames=(deviceName)", label = "common_op_del")})
@Getter
@Setter
public class AccAntiPassbackItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /** 业务id */
    @Column(name = "t.BUSINESS_ID")
    private Long businessId;

    /** 名称 */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_name", width = "150", columnType = "edit", editUrl = "accAntiPassback.do?edit",
        editPermission = "acc:antiPassback:edit")
    private String name;

    /**  */
    @Column(name = "p.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "150")
    private String deviceName;

    /**  */
    @GridColumn(label = "acc_apb_rules", width = "600", sort = "na")
    private String apbRuleShow;

    @Column(name = "t.APB_RULE")
    private Short apbRule;

    @Column(name = "p.ID")
    private String deviceId;

    /** 规则类型 */
    @Column(name = "t.RULE_TYPE")
    //@GridColumn(label = "acc_dev_ruleType", format = "0=acc_apb_door,1=acc_apb_readerHead", width = "150")
    private Short apbRuleType;

    @Condition(value = "p.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    /* * 组1的触发点名称集合 */
    //@GridColumn(label = "acc_interlock_group1", width = "200")
    private String group1Names;

    /** 组1的触发点编号集合 */
    private String group1Ids;

    /** 组2的触发点名称集合 */
    //@GridColumn(label = "acc_interlock_group2", width = "200")
    private String group2Names;

    /** 组2的触发点编号集合 */
    private String group2Ids;

    /** 组1 id */
    @Column(name = "t.TRIGGER_GROUP1")
    private String triggerGroup1Id;

    /** 组2 id */
    @Column(name = "t.TRIGGER_GROUP2")
    private String triggerGroup2Id;
    /** 组1 业务id */
    private Long triggerGroup1BusinessId;

    /** 组2 业务id */
    private Long triggerGroup2BusinessId;

    /**
     * 默认构造方法
     */
    public AccAntiPassbackItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccAntiPassbackItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccAntiPassbackItem(String id) {
        super(true);
        this.id = id;
    }
}