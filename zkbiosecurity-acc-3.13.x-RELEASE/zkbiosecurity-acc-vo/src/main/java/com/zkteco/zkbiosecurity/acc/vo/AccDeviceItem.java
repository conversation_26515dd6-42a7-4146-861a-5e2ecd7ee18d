/**
 * @author: GenerationTools
 * @date: 2018-03-08 下午02:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.Date;

import com.zkteco.zkbiosecurity.acc.constants.AccConstants;
import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁设备vo
 * 
 * @author: yulong.dai
 * @date: 2018-03-08 下午02:41
 */
@From(after = "ACC_DEVICE t LEFT JOIN AUTH_AREA a on t.auth_area_id = a.id ")
@OrderBy(after = "t.BUSINESS_ID ASC")
@GridConfig(operate = true, idField = "id", winHeight = 600, winWidth = 450, minWidth = "140", operates = {
    @GridOperate(type = "edit", permission = "acc:device:edit", url = "accDevice.do?edit", label = "common_op_edit"),
    @GridOperate(type = "del", permission = "acc:device:del", url = "accDevice.do?del&alias=(alias)",
        label = "common_op_del"),
    @GridOperate(type = "custom", permission = "adms:devCmd:refresh", click = "acc_viewCommand",
        label = "common_devMonitor_viewTheCommand"),
    @GridOperate(type = "custom", permission = "acc:device:addChildDevice",
        showConvertor = "filterOperateByMachineType", click = "accAddChildDevice", label = "acc_dev_addChildDevice"),
    @GridOperate(type = "custom", permission = "acc:device:modParentDevice", showConvertor = "filterOperateByParentId",
        click = "accModParentDevice", label = "acc_dev_modParentDevice"),
    @GridOperate(type = "custom", permission = "acc:device:configParentDevice", filter = "accDeviceConfigParentDevice",
        click = "accConfigParentDevice", label = "acc_dev_configParentDevice"),
    @GridOperate(type = "custom", showConvertor = "filterOperateByMachineType", click = "accLookUpChildDevice",
        label = "acc_dev_lookUpChildDevice")})
@Getter
@Setter
@Accessors(chain = true)
public class AccDeviceItem extends BaseItem implements Serializable {

    /** 主键 */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sortNo = 0, sort = "na")
    private String id;

    @Column(name = "t.DEV_ALIAS")
    @GridColumn(columnType = "edit", label = "common_dev_name", width = "140", sortNo = 1,
        editPermission = "acc:device:edit", editUrl = "accDevice.do?edit")
    private String alias;

    @Column(name = "t.SN")
    @GridColumn(label = "common_dev_sn", width = "110", sortNo = 2)
    private String sn;

    @Column(name = "t.AUTH_AREA_ID", equalTag = "in")
    private String authAreaId;

    @Column(name = "a.NAME")
    @GridColumn(label = "base_area_name", width = "90", sortNo = 3)
    private String authAreaName;

    @Column(name = "t.COMM_TYPE")
    @GridColumn(label = "common_dev_commType", format = "1=TCP/IP,2=RS485,3=HTTP,4=MQTT", sortNo = 4, width = "90")
    private Short commType;

    @GridColumn(label = "common_dev_netConnectMode", width = "100",
        format = "0=acc_dev_netModeWired,1=acc_dev_netMode4G,2=acc_dev_netModeWifi", sortNo = 5)
    private String netConnectMode;

    @Column(name = "t.IP_ADDRESS")
    @GridColumn(label = "common_ipAddress", width = "130", sortNo = 6)
    private String ipAddress;

    @GridColumn(label = "common_dev_rs485Param", width = "100", sortNo = 7, sort = "na")
    private String rs485Param;

    @GridColumn(label = "common_status", width = "60", sortNo = 8, sort = "na", columnType = "custom",
        convert = "convertConnectState")
    private String connectState;

    @Column(name = "t.DEVICE_NAME")
    @GridColumn(label = "common_dev_deviceModel", width = "105", sortNo = 9)
    private String deviceName;

    @Column(name = "t.IS_REGISTRATIONDEVICE")
    @GridColumn(label = "acc_dev_registrationDevice", width = "120", sortNo = 10, convert = "convertToIcon",
        columnType = "custom")
    private Boolean isRegistrationDevice;

    @Column(name = "t.FW_VERSION")
    @GridColumn(label = "common_dev_firmwareVersion", width = "120", sortNo = 11)
    private String fwVersion;


    @Column(name = "t.is_push_exception")
    @GridColumn(label = "acc_is_Push_Exception", width = "120", sortNo = 11,format="1=推送,2=不推送")
    private String isPushException;

    @GridColumn(label = "common_devMonitor_leftCmdCount", width = "120", sortNo = 13, isExportExcel = false,
        sort = "na")
    private Long cmdCount;

    @Column(name = "t.MACHINE_TYPE")
    private Short machineType;

    @Column(name = "t.ACPANEL_TYPE")
    private Short acpanelType;

    @Condition(value = "t.ID", equalTag = "in")
    private String ids;

    @Column(name = "t.PARENT_ID", equalTag = "=")
    private String parentDeviceId;

    @Column(name = "t.ENABLED")
    private Boolean enabled;

    @Column(name = "t.SUBNET_MASK")
    private String subnetMask;

    @Column(name = "t.GATEWAY")
    private String gateway;

    @Column(name = "t.COM_PORT")
    private String comPort;

    @Column(name = "t.COM_ADDRESS")
    private String comAddress;

    @Column(name = "t.BAUDRATE")
    private String baudrate;

    @Column(name = "t.COMM_PWD", encryptConverter = true)
    private String commPwd;

    @Column(name = "t.ICON_TYPE")
    private Short iconType;

    @Column(name = "t.WG_READER_ID")
    private String wgReaderId;

    @Column(name = "t.TIME_ZONE")
    @GridColumn(label = "acc_dev_timeZone", width = "100", sortNo = 12, columnType = "custom", convert = "convertTZ", showExpression = "#language!='zh_CN'")
    private String timeZone;

    @Column(name = "t.DSTIME_ID")
    private String accDSTimeId;

    private String masterSlave;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Column(name = "t.IP_PORT")
    private Integer ipPort;

    @Column(name = "t.FOUR_TO_TWO")
    private Boolean fourToTwo;

    @Column(name = "t.MAC_ADDRESS")
    private String macAddress;

    /* BusinessID 下发设备中使用 */
    @Column(name = "t.BUSINESS_ID", equalTag = "=")
    private Long businessId;

    @Condition(value = "t.UPDATE_TIME", equalTag = ">")
    private Date lastUpdateDate;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "t.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /** 数据迁移扩展 **/
    /** 区域编码 **/
    private String authAreaCode;
    /** 夏令时名称 **/
    private String accDSTimeName;
    /** 读头名称 **/
    private String readerName;
    @Condition(value = "t.SN", equalTag = "in")
    private String sns;

    @Condition(value = "t.SN", equalTag = "not in")
    private String snsNotIn;

    /**
     * 默认构造方法
     */
    public AccDeviceItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccDeviceItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccDeviceItem(String id) {
        super(true);
        this.id = id;
    }

    public boolean filterOperateByMachineType() {
        if (this.machineType.shortValue() == AccConstants.DEVICE_BIOIR_9000) {
            return true;
        }
        return false;
    }

    public boolean filterOperateByParentId() {
        if (this.parentDeviceId != null && !"".equals(this.parentDeviceId)) {
            return true;
        }
        return false;
    }
}