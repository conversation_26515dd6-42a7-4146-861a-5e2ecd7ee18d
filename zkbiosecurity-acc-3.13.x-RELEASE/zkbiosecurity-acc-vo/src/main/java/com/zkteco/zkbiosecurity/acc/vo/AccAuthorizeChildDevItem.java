/*
 * File Name: AccAuthorizeChildDevItem
 * <NAME_EMAIL> on 2018/5/28 8:55.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@GridConfig(operate = false, idField = "id", winHeight=430, winWidth=500
)
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccAuthorizeChildDevItem implements Serializable {

    @GridColumn(checkbox = true, width = "40", sortNo = 0, sort = "na")
    private String id;

    @GridColumn(label = "common_dev_deviceModel", width = "70", sortNo = 1)
    private String devName;

    @GridColumn(label = "common_ipAddress", width = "100", sortNo = 2)
    private String ipAddress;

    @GridColumn(label = "common_dev_sn", width = "150", sortNo = 3)
    private String devSn;

    @GridColumn(label = "common_dev_macAddress", width = "150", sortNo = 4)
    private String mac;

    private String gateway;

    private String subnetMask;

    private String devType;

    private String ver;

    private Long cmdId;

    public AccAuthorizeChildDevItem() {
    }
}
