package com.zkteco.zkbiosecurity.acc.api.vo;

import com.zkteco.zkbiosecurity.acc.vo.AccDoorItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 门-VO
 * @Auther: lambert.li
 * @Date: 2018/11/7 19:19
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccApiDoorItem implements Serializable {

    /* 门Id */
    private String id;

    /* 门名称 */
    private String name;

    /* 设备ID */
    private String deviceId;

    public static AccApiDoorItem createApiDoor(AccDoorItem accDoorItem) {
        AccApiDoorItem accApiDoorItem = null;
        if (accDoorItem != null) {
            accApiDoorItem = new AccApiDoorItem();
            accApiDoorItem.setId(accDoorItem.getId());
            accApiDoorItem.setDeviceId(accDoorItem.getDeviceId());
            accApiDoorItem.setName(accDoorItem.getName());
        }
        return accApiDoorItem;
    }
}
