/**
 * @author: GenerationTools
 * @date: 2018-03-02 上午09:13 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 *
 * 
 * @author: Verber
 * @date: 2018-03-08 上午09:13
 */
// @From(after = "ACC_PERSON ap ")
// @OrderBy(after = "ap.CREATE_TIME ")
@GridConfig
@Getter
@Setter
public class AccTransactionPersonItem extends BaseItem {

    /** 主键 */
    // @Column(name="ap.ID")
    @GridColumn(width = "40", show = false)
    private String id;

    // @Column(name="ap.PERS_PERSON_ID")
    @GridColumn(label = "", show = false)
    private String persPersonId;

    /**  */
    // @Column(name="pp.PIN")
    @GridColumn(label = "pers_person_pin", width = "130", encryptMode = "${pers.pin.encryptMode}",
            permission = "acc:pin:encryptProp")
    private String pin;

    /**  */
    // @Column(name="pp.NAME")
    @GridColumn(label = "pers_person_name", width = "130", encryptMode = "${pers.name.encryptMode}",
            permission = "acc:name:encryptProp")
    private String name;

    @GridColumn(label = "pers_person_lastName", width = "130", showExpression = "#language!='zh_CN'",
        encryptMode = "${pers.lastName.encryptMode}", permission = "acc:name:encryptProp")
    private String lastName;

    /**  */
    // @Column(name="bd.ID", equalTag="=")
    private String deptId;

    /**  */
    // @Column(name="bd.NAME")
    @GridColumn(label = "pers_dept_deptName", width = "130")
    private String deptName;

    private String likeName;

    /**
     * 默认构造方法
     */
    public AccTransactionPersonItem() {
        super();
    }

    /**
     * @param id
     */
    public AccTransactionPersonItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param pin
     * @param name
     * @param deptName
     */
    public AccTransactionPersonItem(String id, String pin, String name, String deptId, String deptName) {
        super();
        this.id = id;
        this.pin = pin;
        this.name = name;
        this.deptId = deptId;
        this.deptName = deptName;
    }
}