package com.zkteco.zkbiosecurity.acc.app.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2018/12/5 17:33
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccAppLevelItem implements Serializable {

    /**
     *  权限组ID
     */
    private String accLevelId;

    /**
     *  权限组名称
     */
    private String accLevelName;

    /**
     *  是否选中
     */
    private String selected;
}
