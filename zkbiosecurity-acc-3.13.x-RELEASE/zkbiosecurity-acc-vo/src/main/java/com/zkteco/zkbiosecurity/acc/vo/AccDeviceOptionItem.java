/**
 * @author: GenerationTools
 * @date: 2018-03-20 上午09:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-20 上午09:48
 */
@From(after = "ACC_DEVICE_OPTION t left join ACC_DEVICE d on t.DEV_ID=d.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "acc:deviceOption:edit", url = "accDeviceOption.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:deviceOption:del", url = "accDeviceOption.do?del",
            label = "common_op_del")})
@Getter
@Setter
@Accessors(chain = true)
public class AccDeviceOptionItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "d.ID")
    @GridColumn(label = "acc_deviceOption_device")
    private String deviceId;

    /**  */
    @Column(name = "t.OPTION_NAME")
    @GridColumn(label = "acc_deviceOption_name")
    private String name;

    /**  */
    @Column(name = "t.OPTION_VALUE")
    @GridColumn(label = "acc_deviceOption_value")
    private String value;

    /**  */
    @Column(name = "t.OPTION_TYPE")
    @GridColumn(label = "acc_deviceOption_type")
    private Short type;

    /** 数据迁移扩展 **/
    /** 设备序列号 **/
    private String deviceSn;

    /**
     * 默认构造方法
     */
    public AccDeviceOptionItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccDeviceOptionItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccDeviceOptionItem(String id) {
        super(true);
        this.id = id;
    }

    public AccDeviceOptionItem(String deviceId, String name) {
        super(true);
        this.deviceId = deviceId;
        this.name = name;
    }

    /**
     * @param id
     * @param name
     * @param value
     * @param type
     */
    public AccDeviceOptionItem(String id, String name, String value, Short type) {
        super();
        this.id = id;
        this.name = name;
        this.value = value;
        this.type = type;
    }
}