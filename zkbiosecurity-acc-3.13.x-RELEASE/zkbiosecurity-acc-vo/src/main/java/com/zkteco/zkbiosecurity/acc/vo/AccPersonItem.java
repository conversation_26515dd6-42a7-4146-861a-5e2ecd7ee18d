/**
 * @author: GenerationTools
 * @date: 2018-03-02 下午02:10 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-02 下午02:10
 */
@From(after = "ACC_PERSON t ")
@OrderBy(after = "t.CREATE_TIME DESC")
public class AccPersonItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "t.PERS_PERSON_ID")
    private String personId;

    private String personPin;

    private String personCardNo;

    private String personPassword;

    /**  */
    @Column(name = "t.IS_SET_VALID_TIME")
    private Boolean isSetValidTime;

    /**  */
    @Column(name = "t.START_TIME")
    private Date startTime;

    /**  */
    @Column(name = "t.END_TIME")
    private Date endTime;

    /**  */
    @Column(name = "t.DISABLED")
    private Boolean disabled;

    /**  */
    @Column(name = "t.SUPER_AUTH")
    private Short superAuth;

    /**  */
    @Column(name = "t.PRIVILEGE")
    private Short privilege;

    /**  */
    @Column(name = "t.DELAY_PASSAGE")
    private Boolean delayPassage;

    @Column(name = "t.PERS_PERSON_ID", equalTag = "in")
    private String inPersonId;

    /**  */
    // @Column(name="t.IS_OBEY_GAPB")
    // private Boolean isObeyGapb;

    /** 数据迁移扩展 **/
    private String accCombOpenName;

    /**
     * 默认构造方法
     */
    public AccPersonItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccPersonItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccPersonItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param isSetValidTime
     * @param startTime
     * @param endTime
     * @param disabled
     * @param superAuth
     * @param privilege
     * @param delayPassage
     */
    public AccPersonItem(String id, Boolean isSetValidTime, Date startTime, Date endTime, Boolean disabled,
        Short superAuth, Short privilege, Boolean delayPassage) {
        super();
        this.id = id;
        this.isSetValidTime = isSetValidTime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.disabled = disabled;
        this.superAuth = superAuth;
        this.privilege = privilege;
        this.delayPassage = delayPassage;
    }

    /**
     * @return id 主键
     */
    public String getId() {
        return id;
    }

    /**
     * @param id
     *            要设置的 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getPersonPin() {
        return personPin;
    }

    public void setPersonPin(String personPin) {
        this.personPin = personPin;
    }

    public String getPersonCardNo() {
        return personCardNo;
    }

    public void setPersonCardNo(String personCardNo) {
        this.personCardNo = personCardNo;
    }

    /**
     * @return isSetValidTime
     */
    public Boolean getIsSetValidTime() {
        return isSetValidTime;
    }

    /**
     * @param isSetValidTime
     *            要设置的
     */
    public void setIsSetValidTime(Boolean isSetValidTime) {
        this.isSetValidTime = isSetValidTime;
    }

    /**
     * @return startTime
     */
    public Date getStartTime() {
        return startTime;
    }

    /**
     * @param startTime
     *            要设置的
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * @return endTime
     */
    public Date getEndTime() {
        return endTime;
    }

    /**
     * @param endTime
     *            要设置的
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * @return disabled
     */
    public Boolean getDisabled() {
        return disabled;
    }

    /**
     * @param disabled
     *            要设置的
     */
    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    /**
     * @return superAuth
     */
    public Short getSuperAuth() {
        return superAuth;
    }

    /**
     * @param superAuth
     *            要设置的
     */
    public void setSuperAuth(Short superAuth) {
        this.superAuth = superAuth;
    }

    /**
     * @return privilege
     */
    public Short getPrivilege() {
        return privilege;
    }

    /**
     * @param privilege
     *            要设置的
     */
    public void setPrivilege(Short privilege) {
        this.privilege = privilege;
    }

    /**
     * @return delayPassage
     */
    public Boolean getDelayPassage() {
        return delayPassage;
    }

    /**
     * @param delayPassage
     *            要设置的
     */
    public void setDelayPassage(Boolean delayPassage) {
        this.delayPassage = delayPassage;
    }

    public String getInPersonId() {
        return inPersonId;
    }

    public void setInPersonId(String inPersonId) {
        this.inPersonId = inPersonId;
    }

    public String getPersonPassword() {
        return personPassword;
    }

    public void setPersonPassword(String personPassword) {
        this.personPassword = personPassword;
    }

    public String getAccCombOpenName() {
        return accCombOpenName;
    }

    public void setAccCombOpenName(String accCombOpenName) {
        this.accCombOpenName = accCombOpenName;
    }
}