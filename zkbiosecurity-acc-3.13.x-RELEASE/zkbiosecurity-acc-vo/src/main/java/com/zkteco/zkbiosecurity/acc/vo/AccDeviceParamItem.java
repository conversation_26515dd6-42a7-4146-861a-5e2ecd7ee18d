package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class AccDeviceParamItem {
    private Short commType;

    private String ipAddress;

    private Integer ipPort;

    private String subnetMask;

    private String gateway;

    private Short comPort;

    private Integer baudrate;

    private Short comAddress;

    private String commPwd;

    private String acpanelType;

    private String fourToTwo;

    public AccDeviceParamItem() {}

}
