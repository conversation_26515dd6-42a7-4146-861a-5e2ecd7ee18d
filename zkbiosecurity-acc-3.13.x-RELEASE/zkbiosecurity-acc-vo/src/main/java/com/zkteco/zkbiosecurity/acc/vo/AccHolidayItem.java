/**
 * @author: GenerationTools
 * @date: 2018-02-26 下午05:53 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 门禁节假日
 * 
 * @author: yulong.dai
 * @date: 2018-02-26 下午05:53
 */
@Getter
@Setter
@Accessors(chain = true)
@From(after = "ACC_HOLIDAY t ")
@OrderBy(after = "t.CREATE_TIME ASC")
@GridConfig(operate = true, idField = "id", winHeight = 300, winWidth = 420,
    operates = {
        @GridOperate(type = "edit", permission = "acc:holiday:edit", url = "accHoliday.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:holiday:del", url = "accHoliday.do?del&names=(name)",
            label = "common_op_del")})
public class AccHolidayItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_holiday_name", width = "115", sortNo = 1, columnType = "edit",
        editPermission = "acc:holiday:edit", editUrl = "/accHoliday.do?edit")
    private String name;

    /**  */
    @Column(name = "t.HOLIDAY_TYPE")
    @GridColumn(label = "common_holiday_type", width = "100", sortNo = 2,
        format = "1=common_holiday_type1,2=common_holiday_type2,3=common_holiday_type3")
    private Short holidayType;

    /**  */
    @Column(name = "t.START_DATE")
    @DateType(type = "date")
    @GridColumn(label = "common_startDate", width = "100", sortNo = 3)
    private Date startDate;

    /**  */
    @Column(name = "t.END_DATE")
    @DateType(type = "date")
    @GridColumn(label = "common_endDate", width = "100", sortNo = 4)
    private Date endDate;

    /**  */
    @Column(name = "t.IS_LOOP_BY_YEAR")
    @GridColumn(label = "common_holiday_recurring", width = "100", sortNo = 5,
        format = "true=common_yes,false=common_no", columnType = "custom")
    private Boolean isLoopByYear;

    /**  */
    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", width = "100", sortNo = 6)
    private String remark;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    /**
     * 默认构造方法
     */
    public AccHolidayItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccHolidayItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccHolidayItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     * @param holidayType
     * @param startDate
     * @param endDate
     * @param isLoopByYear
     * @param remark
     */
    public AccHolidayItem(String id, String name, Short holidayType, Date startDate, Date endDate,
        Boolean isLoopByYear, String remark) {
        super();
        this.id = id;
        this.name = name;
        this.holidayType = holidayType;
        this.startDate = startDate;
        this.endDate = endDate;
        this.isLoopByYear = isLoopByYear;
        this.remark = remark;
    }
}