/**
 * @author: GenerationTools
 * @date: 2018-02-28 下午02:21 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 门禁夏令时vo
 * 
 * @author: yulong.dai
 * @date: 2018-02-28 下午02:21
 */
@From(after = "ACC_DSTIME t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 230, winWidth = 570, isInitNotEdit = true,
    operates = {
        @GridOperate(type = "edit", permission = "acc:dSTime:edit", url = "accDSTime.do?edit", label = "common_op_edit",
            showConvertor = "showConvertor"),
        @GridOperate(type = "del", permission = "acc:dSTime:del", url = "accDSTime.do?del&name=(name)",
            label = "common_op_del", showConvertor = "showConvertor")})
public class AccDSTimeItem extends BaseItem {
    public static final String INIT_CODE_US_DSTIME = "INIT_CODE_US_DSTIME";
    public static final short DSTIME_MODE_ONE = 0;// 夏令时模式一
    public static final short DSTIME_MODE_TWO = 1;// 夏令时模式二
    public static final String DSTIME_STARTTIME = "03020002";
    public static final String DSTIME_ENDTIME = "11010002";

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_dsTime_name", width = "140", sortNo = 1, columnType = "edit", i18n = true,
        editPermission = "acc:dSTime:edit", editUrl = "/accDSTime.do?edit")
    private String name;

    /**  */
    @Column(name = "t.DSTIME_MODE")
    private Short dstimeMode;

    /**  */
    @Column(name = "t.START_TIME")
    @GridColumn(label = "common_op_startTime", width = "210", sortNo = 2, columnType = "custom",
        convert = "convertDsTime")
    private String startTime;

    /**  */
    @Column(name = "t.END_TIME")
    @GridColumn(label = "common_op_endTime", width = "210", sortNo = 3, columnType = "custom",
        convert = "convertDsTime")
    private String endTime;

    /**  */
    @Column(name = "t.INIT_FLAG")
    private Boolean initFlag;

    @GridColumn(label = "acc_dev_timeZone", width = "210", columnType = "custom", convert = "convertTimeZone")
    @Column(name = "t.TIME_ZONE")
    private String timeZone;

    // pro

    /** 夏令时开始年份 */
    @Column(name = "t.START_YEAR")
    private String startYear;

    /** 夏令时结束年份 */
    @Column(name = "t.END_YEAR")
    private String endYear;

    /** 业务id */
    @Column(name = "t.BUSSINESS_ID")
    private Long bussinessId;

    /**
     * 默认构造方法
     */
    public AccDSTimeItem() {}

    /**
     * 构造方法
     */
    public AccDSTimeItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccDSTimeItem(String id) {
        this.id = id;
    }

    /**
     * @param name
     * @param dstimeMode
     * @param startTime
     * @param endTime
     * @param initFlag
     */
    public AccDSTimeItem(String name, Short dstimeMode, String startTime, String endTime, Boolean initFlag) {
        this.name = name;
        this.dstimeMode = dstimeMode;
        this.startTime = startTime;
        this.endTime = endTime;
        this.initFlag = initFlag;
    }

    /**
     * 
     * @param name
     * @param startTime
     * @param endTime
     * @param timeZone
     * @param dstimeMode
     */
    public AccDSTimeItem(String name, String startTime, String endTime, String timeZone, Short dstimeMode,
        Boolean initFlag) {
        this.name = name;
        this.startTime = startTime;
        this.endTime = endTime;
        this.timeZone = timeZone;
        this.dstimeMode = dstimeMode;
        this.initFlag = initFlag;
    }

    /**
     * @return id 主键
     */
    public String getId() {
        return id;
    }

    /**
     * @param id 要设置的 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name 要设置的
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * @return dstimeMode
     */
    public Short getDstimeMode() {
        return dstimeMode;
    }

    /**
     * @param dstimeMode 要设置的
     */
    public void setDstimeMode(Short dstimeMode) {
        this.dstimeMode = dstimeMode;
    }

    /**
     * @return startTime
     */
    public String getStartTime() {
        return startTime;
    }

    /**
     * @param startTime 要设置的
     */
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    /**
     * @return endTime
     */
    public String getEndTime() {
        return endTime;
    }

    /**
     * @param endTime 要设置的
     */
    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Boolean getInitFlag() {
        return initFlag;
    }

    public void setInitFlag(Boolean initFlag) {
        this.initFlag = initFlag;
    }

    public String getStartYear() {
        return startYear;
    }

    public void setStartYear(String startYear) {
        this.startYear = startYear;
    }

    public String getEndYear() {
        return endYear;
    }

    public void setEndYear(String endYear) {
        this.endYear = endYear;
    }

    public Long getBussinessId() {
        return bussinessId;
    }

    public void setBussinessId(Long bussinessId) {
        this.bussinessId = bussinessId;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public boolean showConvertor() {
        if (this.initFlag != null) {
            return !this.initFlag;
        }
        return true;
    }
}