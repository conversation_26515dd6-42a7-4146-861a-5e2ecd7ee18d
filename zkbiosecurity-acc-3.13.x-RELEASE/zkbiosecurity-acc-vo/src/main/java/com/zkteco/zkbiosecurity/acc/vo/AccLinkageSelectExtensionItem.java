package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 可视对讲要通知的对象：有绑定分机号的人事人员或者系统用户
 * 
 * <AUTHOR>
 * @date 2024/4/16 10:18
 */
@Getter
@Setter
@GridConfig(operate = false, idField = "id")
public class AccLinkageSelectExtensionItem extends BaseItem implements Serializable {
    /** 主键 */
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /** 分机号 */
    @GridColumn(label = "common_vdb_extensionNumber", width = "150")
    private String extensionNumber;
    /** 分机名称 */
    @GridColumn(label = "common_vdb_extensionName")
    private String extensionName;

    private Short bindType;
    /** 绑定对象 */
    // @GridColumn(label = "common_vdb_notificationObject")
    private String bindObject;

    private String selectId;

    private String type;

    private String userId;

}
