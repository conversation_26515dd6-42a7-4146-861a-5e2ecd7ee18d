package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Description: 设备读头item
 * @author: yulong.dai
 * @date 2020/5/22 14:25
 **/
@From(after = "ACC_READER t " + "LEFT JOIN ACC_DOOR ad ON ad.ID = t.DOOR_ID "
    + "LEFT JOIN ACC_DEVICE dev ON dev.ID = ad.DEV_ID ")
@OrderBy(after = "dev.BUSINESS_ID ASC, t.READER_NO ASC")
@GridConfig(operate = true, idField = "id", winHeight = 500, winWidth = 550, operates = {
    @GridOperate(type = "edit", permission = "acc:reader:edit", url = "accReader.do?edit", label = "common_op_edit"),
    @GridOperate(type = "custom", permission = "acc:reader:bindChannel", filter = "accShowBindChannel",
        click = "accReaderBindChannel", label = "common_vid_bindOrUnbindChannel")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccReaderItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", show = false)
    private String id;

    /** 读头名称 */
    @Column(name = "t.NAME")
    @GridColumn(label = "acc_readerDefine_readerName", width = "150", columnType = "edit",
        editUrl = "accReader.do?edit", editPermission = "acc:reader:edit")
    private String name;

    /**  */
    @Column(name = "t.DOOR_ID")
    @GridColumn(label = "acc_door_id", show = false)
    private String doorId;

    /**  */
    @Column(name = "ad.NAME")
    @GridColumn(label = "acc_door_name", width = "150")
    private String doorName;

    /**  */
    @Column(name = "t.READER_NO")
    @GridColumn(label = "common_number")
    private Short readerNo;

    /** 读头类型 按位：0:不配置，1:RS485，2:韦根，3:RS485/韦根 4:网络读头，5:zigbee读头 */
    @Column(name = "t.COMM_TYPE")
    @GridColumn(label = "acc_reader_commType", width = "150",
        format = "0=acc_readerCommType_disable,1=acc_readerCommType_rs485,"
            + "2=acc_readerCommType_wiegand,3=acc_readerCommType_wg485,4=acc_readerCommType_tcp,5=acc_readerCommType_zigbee")
    private Short commType;

    /** RS485 Address */
    @Column(name = "t.COMM_ADDRESS", equalTag = "=")
    @GridColumn(label = "acc_readerCommType_rs485Address", width = "100")
    private Short commAddress;

    /** 读头出入状态 */
    @Column(name = "t.READER_STATE")
    @GridColumn(label = "acc_reader_inout", format = "0=common_in,1=common_out")
    private Short readerState;

    /** 绑定的摄像头 */
    @Column(name = "t.ID")
    @GridColumn(label = "acc_common_boundChannel", show = false)
    private String channelId;

    @GridColumn(label = "acc_common_boundChannel", width = "150", sort = "na", showHeader = "accShowBindChannel")
    private String channelName;

    /** ip地址 */
    @Column(name = "t.IP")
    @GridColumn(label = "acc_reader_ip", show = false)
    private String ip;

    /** 端口 */
    @Column(name = "t.PORT")
    @GridColumn(label = "acc_reader_port", show = false)
    private Short port;

    /** mac地址 */
    @Column(name = "t.MAC")
    @GridColumn(label = "acc_reader_mac", show = false)
    private String mac;

    /** 组播地址 */
    @Column(name = "t.MULTICAST")
    @GridColumn(label = "acc_reader_multicast", show = false)
    private String multicast;

    /** 读头加密 */
    @Column(name = "t.READER_ENCRYPT")
    @GridColumn(label = "acc_reader_readerEncrypt", show = false)
    private Boolean readerEncrypt;

    @GridColumn(label = "acc_ownedBoard", width = "110", showExpression = "#language!='zh_CN'")
    private String extDevName;

    @Condition(value = "dev.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Condition(value = "t.ID IN (%s)", formatType = "quote")
    private String idIn;

    /** 数据迁移 **/
    /** 设备ID **/
    @Column(name = "dev.id")
    private String deviceId;
    /** 设备SN **/
    @Column(name = "dev.SN")
    private String deviceSn;
    /** 门编号 **/
    @Column(name = "ad.DOOR_NO")
    private String doorNo;

    private String idCardMode;

    @Column(name = "t.EXT_DEV_ID")
    private String extDevId;

    @Column(name = "t.RS485_PROTOCOL_TYPE")
    private Short rs485ProtocolType;

    @Condition(value = "t.DOOR_ID IN (%s)", formatType = "quote")
    private String doorIdIn;

    /** 人员锁定功能 0：否 1：是 */
    @Column(name = "t.USER_LOCK_FUN")
    private Short userLockFun;

    /** 人员部分信息隐藏 0：否 1：是 */
    private String userInfoReveal;

    // pro拓展

    /** 针对RS485读头,对应控制器串口物理编码(1,2,3) */
    @Column(name = "t.SERIAL_PORT", equalTag = "=")
    private Short serialPort;

    /** 韦根格式 */
    @Column(name = "t.WG_INPUTFMT_ID")
    private String wgInputFmtId;

    /** 卡号反转 */
    @Column(name = "t.WG_REVERSED")
    private Short wgReversed;

    /** 是否离线通行 0正常 1拒绝 pro */
    @Column(name = "t.OFFLINE_REFUSE")
    private Short offlineRefuse;

    /** 操作间隔 */
    private Integer actionInterval;

    /** 读头加密模式 0:未加密通道, 1:安装模式(默认密码加密), 2:加密模式(自定义密码加密) */
    private Short readerEncryptModel;

    /** 读头加密密码 */
    private String readerEncryptPwd;

    /** 读头验证方式 */
    private String verifyMode;

    /** 设备businessId */
    @Column(name = "dev.BUSINESS_ID")
    private Long businessId;

    /** 读头序列号 */
    @Column(name = "t.SN")
    private String sn;

    /** 读头固件版本 */
    @Column(name = "t.FW_VERSION")
    private String fwVersion;

    @Column(name = "dev.DEV_ALIAS")
    private String deviceAlias;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "dev.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccReaderItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccReaderItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccReaderItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     * @param readerNo
     * @param readerState
     * @param commType
     * @param commAddress
     * @param ip
     * @param port
     * @param mac
     * @param multicast
     * @param readerEncrypt
     */
    public AccReaderItem(String id, String name, Short readerNo, Short readerState, Short commType, Short commAddress,
        String ip, Short port, String mac, String multicast, Boolean readerEncrypt) {
        super();
        this.id = id;
        this.name = name;
        this.readerNo = readerNo;
        this.readerState = readerState;
        this.commType = commType;
        this.commAddress = commAddress;
        this.ip = ip;
        this.port = port;
        this.mac = mac;
        this.multicast = multicast;
        this.readerEncrypt = readerEncrypt;
    }
}