package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 按门禁权限组设置
 *
 * <AUTHOR>
 * @date 2022/7/26 16:15
 */
@From(
    after = "ACC_LEVEL_PERSON t LEFT JOIN ACC_LEVEL l ON t.LEVEL_ID=l.ID Left join PERS_PERSON p on p.id = t.PERS_PERSON_ID LEFT join AUTH_DEPARTMENT d on p.auth_dept_id = d.id")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AccPersonForLevelItem extends BaseItem {

    /** 主键 */
    @Column(name = "p.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /** 人员编号 */
    @Column(name = "p.PIN")
    @GridColumn(label = "pers_person_pin", sortNo = 1, width = "100", encryptMode = "${pers.pin.encryptMode}",
            permission = "acc:pin:encryptProp")
    private String personPin;

    /** 姓名 */
    @Column(name = "p.NAME")
    @GridColumn(label = "pers_person_name", sortNo = 2, width = "100", encryptMode = "${pers.name.encryptMode}",
            permission = "acc:name:encryptProp")
    private String personName;

    /** 姓 */
    @Column(name = "p.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3, width = "100",
        encryptMode = "${pers.lastName.encryptMode}", permission = "acc:name:encryptProp")
    private String personLastName;

    /** 部门名称 */
    @Column(name = "d.NAME")
    @GridColumn(label = "pers_dept_entity", sortNo = 5, width = "150", sort = "na")
    private String deptName;

    /**
     * 开始时间
     */
    @Column(name = "t.START_TIME")
    @GridColumn(label = "common_level_startTime", width = "125", sortNo = 7, showHeader = "accShowLevelTime")
    private Date startTime;

    /**
     * 结束时间
     */
    @Column(name = "t.END_TIME")
    @GridColumn(label = "common_level_endTime", width = "125", sortNo = 8, showHeader = "accShowLevelTime")
    private Date endTime;

    @Column(name = "t.LEVEL_ID", equalTag = "=")
    private String linkId;

    @Condition(value = "(LOWER (p.NAME) LIKE LOWER (''%{0}%'') OR LOWER (p.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    /** 权限组id */
    @Column(name = "t.LEVEL_ID")
    private String levelId;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(value = "d.ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;
}
