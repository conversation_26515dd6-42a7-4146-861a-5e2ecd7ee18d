package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class AccSearchDeviceItem implements Serializable {
    /*设备ip*/
    private String ip;
    /*设备sn*/
    private String sn;
    /*mac地址*/
    private String macAddress;
    /*子网掩码*/
    private String subnetMask;
    /*网关*/
    private String gateway;
    /*设备类型*/
    private String deviceType;
    /*服务器地址*/
    private String serverUrl;
    /*设备名称*/
    private String deviceName;
    /*通信方法--push*/
    private String protype;
    /*授权标识*/
    private Boolean authFlag;
    /*是否支持https*/
    private String isSupportSSL;
    /*是否支持dns*/
    private String dnsFunOn;
    /*dns*/
    private String dns;
    /*固件版本*/
    private String ver;
    /*设置支持功能列表*/
    private String accSupportFunList;
    /*是否支持多卡*/
    private Boolean isSupportMultiCard;
    /*最大卡号*/
    private String maxMCUCardBits;
    /*机器型号*/
    private String machineType;
    /*父设备sn*/
    private String parentSn;
    /*父设备id*/
    private String parentDevId;
    /*父设备名称*/
    private String parentDevAlias;
    /*主设备参数*/
    private String masterControlOn;
    /*子设备参数*/
    private String subControlOn;
    /** 模式，1是表示pull广播包搜索到，2表示push自动注册得到 */
    private String modeType;
    /*设备控机参数*/
    private String regDeviceType;
    /*是否支持NVR*/
    private Boolean isSupportNVR;
}
