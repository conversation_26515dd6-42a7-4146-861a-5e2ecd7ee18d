package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AccPersonLevelOptItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long deviceId;

    private Long timeSegId;

    private String pin;

    private List<Short> doorNoList = new ArrayList<>();

    /** 权限组编号,对应AccLevel实体businessId add by max 20200518 */
    private Long levelId;

    private Date startTime;

    private Date endTime;
}
