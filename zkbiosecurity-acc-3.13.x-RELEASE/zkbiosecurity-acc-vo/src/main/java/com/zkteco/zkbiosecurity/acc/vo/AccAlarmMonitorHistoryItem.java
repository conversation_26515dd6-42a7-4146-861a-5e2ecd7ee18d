package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;

import lombok.Getter;
import lombok.Setter;

/**
 * 报警监控历史记录
 * 
 * <AUTHOR>
 * @date Created In 09:55 2020/3/17
 */
@From(after = " ACC_ALARMMONITOR_HISTORY h LEFT JOIN ACC_ALARM_MONITOR t ON t.id=h.alarm_monitor_id")
@OrderBy(after = "h.CREATE_TIME DESC")
@GridConfig
@Getter
@Setter
public class AccAlarmMonitorHistoryItem extends BaseItem implements Serializable {
    private static final long serialVersionUID = 1L;

    @Column(name = "h.ID")
    @GridColumn(checkbox = true, sortNo = 0, width = "40", show = false)
    private String id;

    @Column(name = "t.EVENT_NUM")
    @GridColumn(label = "common_event_number")
    private Long eventNum;
    /**
     * 修改时间
     */
    @Column(name = "h.create_time")
    @GridColumn(label = "common_createdTime", width = "120", sortNo = 1)
    private Date createTime;

    @Column(name = "t.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "120", sortNo = 2)
    private String deviceName;

    @Column(name = "t.DEV_SN")
    private String devSn;

    /**
     * 事件发生点
     */
    @Column(name = "t.EVENT_POINT_NAME")
    @GridColumn(label = "common_eventPoint", width = "120", sortNo = 3)
    private String eventPoint;

    @Column(name = "t.EVENT_NAME", equalTag = "in")
    @GridColumn(label = "common_eventDescription", width = "120", sortNo = 4, i18n = true, sort = "na")
    private String eventName;

    /**
     * 修改状态
     */
    @Column(name = "h.status")
    @GridColumn(label = "common_inOutStatus", width = "120", sortNo = 5,
        format = "1=acc_alarm_inProcess,2=acc_alarm_acknowledged")
    private Short status;

    private String statusStr;

    /**
     * 操作者
     */
    @Column(name = "h.CREATER_CODE")
    @GridColumn(label = "common_opUser", width = "120", sortNo = 6)
    private String createrCode;
    /**
     * 修改备注
     */
    @Column(name = "h.ACKNOWLEDGEMENT")
    @GridColumn(label = "acc_alarm_acknowledgement", sortNo = 7)
    private String acknowledgement;

    @Condition(value = "h.create_time", equalTag = ">=")
    private Date startTime;

    @Condition(value = "h.create_time", equalTag = "<=")
    private Date endTime;

    @Condition(value = "t.AREA_NAME", equalTag = "in")
    private String areaNameIn;

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
        switch (status) {
            case 1:
                this.statusStr = I18nUtil.i18nCode("acc_alarm_inProcess");
                break;
            case 2:
                this.statusStr = I18nUtil.i18nCode("acc_alarm_acknowledged");
            default:
        }
    }
}
