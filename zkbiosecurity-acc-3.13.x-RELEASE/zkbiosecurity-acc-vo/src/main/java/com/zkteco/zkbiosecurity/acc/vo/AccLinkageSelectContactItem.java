package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 联动添加通讯录
 *
 * <AUTHOR>
 * @date 2022/2/15 17:02
 */
@Getter
@Setter
@GridConfig(operate = false, idField = "id", winHeight = 400, winWidth = 600)
public class AccLinkageSelectContactItem extends BaseItem implements Serializable {

    /** 主键 */
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /** 联系人 */

    @GridColumn(label = "system_systemClient_contacts")
    private String contact;

    private String selectId;

    private String type;
}
