/**
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41
 */
@From(after = "ACC_LINKAGE_TRIGGER t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "acc:linkageTrigger:edit", url = "accLinkageTrigger.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:linkageTrigger:del", url = "accLinkageTrigger.do?del",
            label = "common_op_del")})
@Getter
@Setter
@Accessors(chain = true)
public class AccLinkageTriggerItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.TRIGGER_COND")
    @GridColumn(label = "acc_linkageTrigger_triggerCond")
    private Short triggerCond;

    /**  */
    @Column(name = "t.LINKAGE_INDEX")
    @GridColumn(label = "acc_linkageTrigger_linkageIndex")
    private Integer linkageIndex;

    /**  */
    @Column(name = "t.LINKAGE_ID")
    @GridColumn(label = "acc_linkageTrigger_linkage")
    private String linkageId;

    /**  */
    @Column(name = "t.LINKAGE_INOUT_ID")
    @GridColumn(label = "acc_linkageTrigger_linkageInOut")
    private String linkageInOutId;

    /**
     * 默认构造方法
     */
    public AccLinkageTriggerItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccLinkageTriggerItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccLinkageTriggerItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param triggerCond
     * @param linkageIndex
     */
    public AccLinkageTriggerItem(String id, Short triggerCond, Integer linkageIndex) {
        super();
        this.id = id;
        this.triggerCond = triggerCond;
        this.linkageIndex = linkageIndex;
    }
}