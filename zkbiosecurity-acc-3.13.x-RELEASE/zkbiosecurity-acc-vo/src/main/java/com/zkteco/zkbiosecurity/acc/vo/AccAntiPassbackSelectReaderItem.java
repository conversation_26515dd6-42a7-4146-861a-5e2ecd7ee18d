package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/8 14:42
 * @since 1.0.0
 */
@From(after = "ACC_READER t " + "LEFT JOIN ACC_DOOR ad ON ad.ID = t.DOOR_ID "
    + "LEFT JOIN ACC_DEVICE dev ON dev.ID = ad.DEV_ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true)
@Getter
@Setter
@Accessors(chain = true)
public class AccAntiPassbackSelectReaderItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /** 读头名 */
    @Column(name = "t.NAME")
    @GridColumn(label = "acc_readerDefine_readerName", width = "150")
    private String name;

    /** 门名称 */
    @Column(name = "ad.NAME")
    @GridColumn(label = "acc_door_name", width = "110")
    private String doorName;

    /** 门id */
    @Column(name = "t.DOOR_ID")
    private String doorId;

    /** 门是否启用 */
    @Column(name = "ad.ENABLED")
    private Boolean doorEnabled;

    /** 设备名称 */
    @Column(name = "dev.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev", width = "100")
    private String deviceAlias;

    @Column(name = "dev.ID")
    private String deviceId;

    @Column(name = "t.EXT_DEV_ID")
    private String extDevId;

    /** 设备sn */
    @Column(name = "dev.SN")
    @GridColumn(label = "common_dev_sn")
    private String deviceSn;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    @Column(name = "t.ID", equalTag = "in")
    private String selectReaderIdsIn;

    @Column(name = "t.ID", equalTag = "not in")
    private String selectReaderIdsNotIn;

    private String type;

    private String selectId;

    @Condition(value = "dev.AUTH_AREA_ID IN (%s)", formatType = "quote")
    private String authAreaIdIn;

    private String filterIds;
}
