/**
 * @author:	GenerationTools
 * @date:	2018-03-13 上午11:35
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 读头定义选择控件列表
 * 
 * @author: verber
 * @date: 2018-04-08 15:35
 */
@From(after = "ACC_READER t "
	        + "LEFT JOIN ACC_DOOR ad ON ad.ID = t.DOOR_ID "
		    + "LEFT JOIN ACC_DEVICE dev ON dev.ID = ad.DEV_ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id")
public class AccSelectReaderItem extends BaseItem {

	/** 主键 */
	@Column(name="t.ID")
	@GridColumn(checkbox = true, width = "40")
	private String id;

	/**  */
	@Column(name="t.NAME")
	@GridColumn(label = "acc_readerDefine_readerName", width = "150")
	private String name;

	@Column(name = "ad.NAME")
	@GridColumn(label = "acc_door_name", width = "110")
	private String doorName;

	/**  */
	@Column(name="t.DOOR_ID")
	private String doorId;

	/**  */
	@Column(name="dev.DEV_ALIAS")
	@GridColumn(label = "common_ownedDev", width = "100")
	private String deviceAlias;

	/**  */
	@Column(name="dev.SN")
	@GridColumn(label = "common_dev_sn")
	private String deviceSn;

	@Condition(value = "t.ID", equalTag="in")
	private String inId;

	@Condition(value = "t.ID", equalTag="not in")
	private String notInId;

	private String type;

	private String selectId;

	@Condition(value = "dev.AUTH_AREA_ID", equalTag = "in")
	private String authAreaIdIn;

	private String filterIds;

	/**
	 * 默认构造方法
	 */
	public AccSelectReaderItem() {
		super();
	}

	/**
	 * 构造方法
	 */
	public AccSelectReaderItem(Boolean equals) {
		super(equals);
	}

	/**
	 * @param id
	 */
	public AccSelectReaderItem(String id) {
		super(true);
		this.id = id;
	}

	/**
	 * @param id
	 * @param name
	 */
	public AccSelectReaderItem(String id, String name) {
		super();
		this.id = id;
		this.name = name;

	}

	/**
	 * @return id 主键
	 */
	public String getId()
	{
		return id;
	}

	/**
	 * @param id 要设置的 主键
	 */
	public void setId(String id)
	{
		this.id = id;
	}

	public String getDoorId() {
		return doorId;
	}

	public void setDoorId(String doorId) {
		this.doorId = doorId;
	}


	/**
	 * @return name 
	 */
	public String getName()
	{
		return name;
	}

	/**
	 * @param name 要设置的 
	 */
	public void setName(String name)
	{
		this.name = name;
	}

	public String getDeviceAlias() {
		return deviceAlias;
	}

	public void setDeviceAlias(String deviceAlias) {
		this.deviceAlias = deviceAlias;
	}

	public String getDeviceSn() {
		return deviceSn;
	}

	public void setDeviceSn(String deviceSn) {
		this.deviceSn = deviceSn;
	}

	public String getInId() {
		return inId;
	}

	public void setInId(String inId) {
		this.inId = inId;
	}

	public String getNotInId() {
		return notInId;
	}

	public void setNotInId(String notInId) {
		this.notInId = notInId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getSelectId() {
		return selectId;
	}

	public void setSelectId(String selectId) {
		this.selectId = selectId;
	}

	public String getDoorName() {
		return doorName;
	}

	public void setDoorName(String doorName) {
		this.doorName = doorName;
	}

	public String getAuthAreaIdIn() {
		return authAreaIdIn;
	}

	public void setAuthAreaIdIn(String authAreaIdIn) {
		this.authAreaIdIn = authAreaIdIn;
	}

	public String getFilterIds() {
		return filterIds;
	}

	public void setFilterIds(String filterIds) {
		this.filterIds = filterIds;
	}
}