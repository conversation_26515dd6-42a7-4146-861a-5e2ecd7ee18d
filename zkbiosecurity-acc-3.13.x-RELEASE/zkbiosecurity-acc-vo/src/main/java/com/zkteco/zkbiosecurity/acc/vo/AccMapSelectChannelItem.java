package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

@GridConfig(operate = false, idField = "id")
public class AccMapSelectChannelItem extends BaseItem {

    /** 主键 */
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @GridColumn(label = "common_dev_channelName",sortNo = 1,width = "120")
    private String name;

    @GridColumn(label = "common_ownedDev",sortNo = 2,width = "120")
    private String alias;

    @GridColumn(label = "common_dev_sn",sortNo = 3,width = "*")
    private String sn;

    private String inId;

    private String notInId;

    private String filterId;

    private String type;

    private String selectId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getInId() {
        return inId;
    }

    public void setInId(String inId) {
        this.inId = inId;
    }

    public String getNotInId() {
        return notInId;
    }

    public void setNotInId(String notInId) {
        this.notInId = notInId;
    }

    public String getFilterId() {
        return filterId;
    }

    public void setFilterId(String filterId) {
        this.filterId = filterId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSelectId() {
        return selectId;
    }

    public void setSelectId(String selectId) {
        this.selectId = selectId;
    }
}
