package com.zkteco.zkbiosecurity.acc.api.vo;

import com.zkteco.zkbiosecurity.acc.vo.AccReaderItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 读头-VO
 * @Auther: lambert.li
 * @Date: 2018/11/7 19:23
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccApiReaderItem implements Serializable {

    /* 读头ID */
    private String id;
    /** 门 */
    private String doorId;
    /** 名称 */
    private String name;
    /** 读头编号 */
    private Short readerNo;
    /** 出入状态：0：入 1：出 */
    private Short readerState;

    public static AccApiReaderItem createApiReader(AccReaderItem accReaderItem) {
        AccApiReaderItem accApiReaderItem = null;
        if (accReaderItem != null) {
            accApiReaderItem = new AccApiReaderItem();
            accApiReaderItem.setId(accReaderItem.getId());
            accApiReaderItem.setDoorId(accReaderItem.getDoorId());
            accApiReaderItem.setReaderNo(accReaderItem.getReaderNo());
            accApiReaderItem.setReaderState(accReaderItem.getReaderState());
            accApiReaderItem.setName(accReaderItem.getName());
        }
        return accApiReaderItem;
    }
}
