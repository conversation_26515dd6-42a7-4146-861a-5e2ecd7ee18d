/**
 * @author: GenerationTools
 * @date: 2018-03-20 下午02:07 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-20 下午02:07
 */
@From(after = "ACC_MAP_POS t " + "LEFT JOIN ACC_MAP am ON am.ID = t.MAP_ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id", winHeight = 400, winWidth = 600, operates = {
    @GridOperate(type = "edit", permission = "acc:accMapPos:edit", url = "accMapPos.do?edit", label = "common_op_edit"),
    @GridOperate(type = "del", permission = "acc:accMapPos:del", url = "accMapPos.do?del", label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class AccMapPosItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.MAP_ID")
    @GridColumn(label = "acc_accMapPos_map")
    private String mapId;

    /**  */
    @Column(name = "am.name")
    @GridColumn(label = "acc_accMapPos_map")
    private String mapName;

    /**  */
    @Column(name = "t.ENTITY_TYPE")
    @GridColumn(label = "acc_accMapPos_entityType")
    private String entityType;

    /**  */
    @Column(name = "t.ENTITY_ID")
    @GridColumn(label = "acc_accMapPos_entityId")
    private String entityId;

    private String entityName;
    /** 门节点时候才有值 */
    // private String lockDisplay;
    /**  */
    @Column(name = "t.WIDTH")
    @GridColumn(label = "acc_accMapPos_width")
    private Double width;

    /**  */
    @Column(name = "t.LEFT_X")
    @GridColumn(label = "acc_accMapPos_leftX")
    private Double leftX;

    /**  */
    @Column(name = "t.TOP_Y")
    @GridColumn(label = "acc_accMapPos_topY")
    private Double topY;

    /** 门底下读头绑定的视频通道ID */
    private String channelIds;

    /**
     * 默认构造方法
     */
    public AccMapPosItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccMapPosItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccMapPosItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param entityType
     * @param entityId
     * @param width
     * @param leftX
     * @param topY
     */
    public AccMapPosItem(String id, String entityType, String entityId, Double width, Double leftX, Double topY) {
        super();
        this.id = id;
        this.entityType = entityType;
        this.entityId = entityId;
        this.width = width;
        this.leftX = leftX;
        this.topY = topY;
    }
}