package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AccLinkageBeanItem {

    private String devId;
    private String[] triggerCondArray;
    private String[] inAddrArray;
    private String linkageId;
    private String[] inputsAddr;
    private String[] outputsAddr;
    private short doorActionType;
    private short doorActionTime;
    private short auxoutActionType;
    private short auxoutActionTime;
    private String[] mailAddr;
    private Integer popUpTime;
    /** 录像回放：事件发生前（）s */
    private Integer recordBeforeTime;
    /** 原录像时长，复用此字段；录像回放：事件发生后（）s */
    private Integer recordTime;
    private String capture;
    private Integer captureTime;
    private String[] mobileNo;
    private String digiEventNames;
    private String lineContactIds;
    private String[] whatsappMobileNo;
    private String vdbExtensionIds;
    private String vdbIvrId;

    /** 分区动作类型 */
    private Short partitionActionType;
    /** 分区ids */
    private String[] iasPartitionIds;
    /** 厂商 */
    private String iasManufacture;
    /** 布防类型 */
    private Short partitionArmType;

    public AccLinkageBeanItem() {

    }
}
