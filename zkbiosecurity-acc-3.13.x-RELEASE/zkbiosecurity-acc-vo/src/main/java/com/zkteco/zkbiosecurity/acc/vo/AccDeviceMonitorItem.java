/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午02:44 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.annotation.GridOperate;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "custom", permission = "acc:deviceMonitor:clearCmdCache", click = "acc_clearCmdCache",
            label = "common_devMonitor_clearCmdCache"),
        @GridOperate(type = "custom", permission = "adms:devCmd:refresh", click = "acc_viewCommand",
            label = "common_devMonitor_viewTheCommand")})
public class AccDeviceMonitorItem extends BaseItem implements Serializable {

    /**
     * 说明成员变量的意义,目的,功能,可能被用到的地方
     */
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @GridColumn(checkbox = false, width = "40", show = false)
    private String id;

    @GridColumn(label = "common_dev_name", width = "120")
    private String devName;

    @GridColumn(label = "common_dev_sn", width = "150")
    private String devSn;

    @GridColumn(label = "base_area_entity", width = "150")
    private String areaName;

    private String areaId;

    @GridColumn(label = "common_devMonitor_opState", width = "150", i18n = true)
    private String opState;

    @GridColumn(label = "common_devMonitor_curState", width = "110", i18n = true)
    private String curState;

    @GridColumn(label = "common_devMonitor_leftCmdCount", width = "120")
    private Long cmdCount;

    @GridColumn(label = "common_devMonitor_lastError", width = "180", i18n = true)
    private String lastError;

    private String devStatus;

    /**
     * 默认构造方法
     */
    public AccDeviceMonitorItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccDeviceMonitorItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccDeviceMonitorItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @return id
     */
    public String getId() {
        return id;
    }

    /**
     * @param id 要设置的 id
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * @return devName
     */
    public String getDevName() {
        return devName;
    }

    /**
     * @param devName 要设置的 devName
     */
    public void setDevName(String devName) {
        this.devName = devName;
    }

    /**
     * @return devSn
     */
    public String getDevSn() {
        return devSn;
    }

    /**
     * @param devSn 要设置的 devSn
     */
    public void setDevSn(String devSn) {
        this.devSn = devSn;
    }

    /**
     * @return areaName
     */
    public String getAreaName() {
        return areaName;
    }

    /**
     * @param areaName 要设置的 areaName
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * @return opState
     */
    public String getOpState() {
        return opState;
    }

    /**
     * @param opState 要设置的 opState
     */
    public void setOpState(String opState) {
        this.opState = opState;
    }

    /**
     * @return curState
     */
    public String getCurState() {
        return curState;
    }

    /**
     * @param curState 要设置的 curState
     */
    public void setCurState(String curState) {
        this.curState = curState;
    }

    /**
     * @return cmdCount
     */
    public Long getCmdCount() {
        return cmdCount;
    }

    /**
     * @param cmdCount 要设置的 cmdCount
     */
    public void setCmdCount(Long cmdCount) {
        this.cmdCount = cmdCount;
    }

    /**
     * @return lastError
     */
    public String getLastError() {
        return lastError;
    }

    /**
     * @param lastError 要设置的 lastError
     */
    public void setLastError(String lastError) {
        this.lastError = lastError;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getDevStatus() {
        return devStatus;
    }

    public void setDevStatus(String devStatus) {
        this.devStatus = devStatus;
    }
}