package com.zkteco.zkbiosecurity.acc.api.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 门状态-VO
 * @Auther: lambert.li
 * @Date: 2018/11/7 19:22
 */

@Getter
@Setter
@Accessors(chain = true)
public class AccApiDoorStateItem extends AccApiDoorItem implements Serializable {

    /* 设备连接状态 */
    private String connect;

    /* 门磁状态 */
    private String sensor;

    /* 报警 */
    private String alarm;

    /* 继电器 */
    private String relay;
}
