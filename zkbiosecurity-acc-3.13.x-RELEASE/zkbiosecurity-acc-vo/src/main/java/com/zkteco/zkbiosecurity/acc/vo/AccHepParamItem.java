package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020-05-27 13:43
 */
@Getter
@Setter
@ToString
@Accessors(chain=true)
public class AccHepParamItem implements Serializable {
    /**
     * 设备Id
     */
    private String devId;
    /**
     * 设备名称
     */
    private String alias;
    /**
     * 开启红外体温检测: 0否 1是
     */
    private String enableIRTempDetection;
    /**
     * 开启体温异常不通行: 0否 1是
     */
    private String enableNormalIRTempPass;
    /**
     * 开启口罩检测检测: 0否 1是
     */
    private String enableMaskDetection;
    /**
     * 开启未戴口罩不通行: 0否 1是
     */
    private String enableWearMaskPass;
    /**
     * 允许未登记人员通行: 0否 1是
     */
    private String enableUnregisterPass;
    /**
     * 触发外部报警功能: 0否 1是
     */
    private String enableTriggerAlarm;
    /**
     * 温度单位:0是摄氏度，1是华氏
     */
    private BigDecimal irTempThreshold;
    /**
     * 高温报警阀值
     */
    private BigDecimal irTempCorrection;
    /**
     * 温度校正偏差值
     */
    private String irTempUnit;
}
