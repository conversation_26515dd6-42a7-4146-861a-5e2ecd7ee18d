/**
 * @author:	GenerationTools
 * @date:	2018-03-02 上午09:13
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 根据人员ID查询楼层
 * 
 * @author: Verber
 * @date: 2018-03-08 14:13
 */
@From(after = "ACC_DOOR ad "
		    + "LEFT JOIN ACC_LEVEL_DOOR ald ON ald.DOOR_ID = ad.ID "
		    + "LEFT JOIN ACC_LEVEL_PERSON alp ON alp.LEVEL_ID = ald.LEVEL_ID ")
@OrderBy(after = "ad.CREATE_TIME ")
@GridConfig
public class AccTransactionPersonDoorItem extends BaseItem {

	/** 主键 */
	@Column(name="ad.ID")
	@GridColumn(checkbox = true, width = "40",show=false)
	private String id;

	/**  */
	@Column(name="ad.DOOR_NO")
	@GridColumn(label = "acc_door_number", width = "130")
	private Short floorNo;

	/**  */
	@Column(name="ad.NAME")
	@GridColumn(label = "acc_door_name", width = "130")
	private String name;

	@Column(name="alp.PERS_PERSON_ID", equalTag="=")
	private String personId;

	/**
	 * 默认构造方法
	 */
	public AccTransactionPersonDoorItem() {
		super();
	}

	/**
	 * 构造方法
	 */
	public AccTransactionPersonDoorItem(Boolean equals) {
		super(equals);
	}

	/**
	 * @param id
	 */
	public AccTransactionPersonDoorItem(String id) {
		super(true);
		this.id = id;
	}

	/**
	 * @param id
	 * @param floorNo
	 * @param name
	 */
	public AccTransactionPersonDoorItem(String id, Short floorNo, String name, String deviceAlias, String personId) {
		super();
		this.id = id;
		this.floorNo = floorNo;
		this.name = name;
		this.personId = personId;
	}

	/**
	 * @return id 主键
	 */
	public String getId()
	{
		return id;
	}

	/**
	 * @param id 要设置的 主键
	 */
	public void setId(String id)
	{
		this.id = id;
	}

	/**
	 * @return floorNo 
	 */
	public Short getFloorNo()
	{
		return floorNo;
	}

	/**
	 * @param floorNo 要设置的 
	 */
	public void setFloorNo(Short floorNo)
	{
		this.floorNo = floorNo;
	}

	/**
	 * @return name 
	 */
	public String getName()
	{
		return name;
	}

	/**
	 * @param name 要设置的 
	 */
	public void setName(String name)
	{
		this.name = name;
	}
	

	public String getPersonId() {
		return personId;
	}

	public void setPersonId(String personId) {
		this.personId = personId;
	}
}