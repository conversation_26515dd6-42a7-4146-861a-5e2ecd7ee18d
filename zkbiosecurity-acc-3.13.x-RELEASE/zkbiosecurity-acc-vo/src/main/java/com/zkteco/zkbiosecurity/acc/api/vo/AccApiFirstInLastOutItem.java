package com.zkteco.zkbiosecurity.acc.api.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @DATE 2020-09-02 15:51
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccApiFirstInLastOutItem implements Serializable {
    /** 主键 */
    private String id;

    /** 人员编号 */
    private String pin;

    /** 姓名 */
    private String name;

    /** 姓氏 */
    private String lastName;

    /** first in读头名称 */
    private String readerNameIn;

    /** first in事件时间 */
    private String firstInTime;

    /** last out读头名称 */
    private String readerNameOut;

    /** last out事件时间 */
    private String lastOutTime;

    /** 部门编号 */
    private String deptCode;

    /** 部门名称 */
    private String deptName;

}
