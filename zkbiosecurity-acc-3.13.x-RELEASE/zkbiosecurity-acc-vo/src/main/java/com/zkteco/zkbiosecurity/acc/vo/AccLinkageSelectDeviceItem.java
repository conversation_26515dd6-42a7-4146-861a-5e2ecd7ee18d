package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

@From(after = "ACC_DEVICE t")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
public class AccLinkageSelectDeviceItem extends BaseItem{

    @Column(name = "t.ID")
    @GridColumn(columnType = "ra", width = "40", sortNo = 0)
    private String id;

    /**设备名称*/
    @Column(name = "t.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", sortNo = 1, width = "120")
    private String deviceAlias;

    /**设备型号*/
    @Column(name = "t.DEVICE_NAME")
    @GridColumn(label = "common_dev_deviceModel", sortNo = 1, width = "90")
    private String deviceName;

    /**区域ID*/
    @Column(name="t.AUTH_AREA_ID", equalTag = "=")
    private String authAreaId;

    /**区域名称*/
    @GridColumn(label = "base_area_name", width = "90", sortNo = 3)
    private String authAreaName;

    /**设备序列号*/
    @Column(name = "t.SN")
    @GridColumn(label = "common_dev_sn", sortNo = 2,width = "*")
    private String deviceSn;

    //判断是左列表（值为noSelect）还是右列表（值为select）
    private String type;

    private String selectId;

    @Column(name = "t.ID", equalTag = "in")
    private String selectDeviceIdsIn;

    @Column(name = "t.ID", equalTag = "not in")
    private String selectDeviceIdsNotIn;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    @Override
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeviceAlias() {
        return deviceAlias;
    }

    public void setDeviceAlias(String deviceAlias) {
        this.deviceAlias = deviceAlias;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public String getAuthAreaId() {
        return authAreaId;
    }

    public void setAuthAreaId(String authAreaId) {
        this.authAreaId = authAreaId;
    }

    public String getAuthAreaName() {
        return authAreaName;
    }

    public void setAuthAreaName(String authAreaName) {
        this.authAreaName = authAreaName;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSelectId() {
        return selectId;
    }

    public void setSelectId(String selectId) {
        this.selectId = selectId;
    }

    public String getSelectDeviceIdsIn() {
        return selectDeviceIdsIn;
    }

    public void setSelectDeviceIdsIn(String selectDeviceIdsIn) {
        this.selectDeviceIdsIn = selectDeviceIdsIn;
    }

    public String getSelectDeviceIdsNotIn() {
        return selectDeviceIdsNotIn;
    }

    public void setSelectDeviceIdsNotIn(String selectDeviceIdsNotIn) {
        this.selectDeviceIdsNotIn = selectDeviceIdsNotIn;
    }

    public String getAuthAreaIdIn() {
        return authAreaIdIn;
    }

    public void setAuthAreaIdIn(String authAreaIdIn) {
        this.authAreaIdIn = authAreaIdIn;
    }
}
