package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@From(after = "ACC_EXT_DEVICE t LEFT JOIN ACC_DEVICE d ON d.ID=t.DEV_ID ")
@OrderBy(after = "d.BUSINESS_ID ASC, t.EXT_BOARD_NO ASC")
@GridConfig(operate = true, idField = "id", winHeight = 350, winWidth = 450, operates = {@GridOperate(type = "edit",
    permission = "acc:extDevice:edit", url = "/accExtDevice.do?edit", label = "common_op_edit")})
@Getter
@Setter
@Accessors(chain = true)
public class AccExtDeviceItem extends BaseItem {

    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.ALIAS")
    @GridColumn(label = "common_name", width = "150", sortNo = 1, columnType = "edit",
        editPermission = "acc:extDevice:edit", editUrl = "/accExtDevice.do?edit")
    private String alias;

    @Column(name = "d.AUTH_AREA_ID", equalTag = "=")
    private String authAreaId;

    @GridColumn(label = "base_area_name", width = "100", sortNo = 2, sort = "na")
    private String authAreaName;

    @Column(name = "t.DEV_ID")
    private String devId;

    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev", width = "150", sortNo = 3)
    private String deviceAlias;

    @Column(name = "t.EXT_BOARD_NO")
    @GridColumn(label = "common_number", width = "100", sortNo = 4, sort = "na")
    private Short extBoardNo;

    @Column(name = "t.EXT_BOARD_TYPE")
    @GridColumn(label = "acc_dev_extBoardType", width = "120", sortNo = 5, sort = "na",
        format = "101=DM10," + "103=AUX485,104=EX0808")
    private Short extBoardType;

    @Column(name = "t.COMM_ADDRESS")
    @GridColumn(label = "common_dev_rs485Address", width = "120", sortNo = 6)
    private Short commAddress;

    @Condition(value = "d.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Column(name = "t.DEV_PROTOCOL_TYPE")
    @GridColumn(label = "acc_dev_protocol", width = "110", sortNo = 7, sort = "na", format = "1=ZK485,2=OSDP")
    private Short devProtocolType;

    @Column(name = "t.SERIAL_PORT")
    private Short serialPort;

    @Column(name = "t.APERIO_MACADDR_MODE")
    private Short aperioMacAddrMode;

    @Column(name = "t.PARENT_ID")
    private String parentId;

    /** 扩展板序列号 */
    @Column(name = "t.SN")
    private String sn;

    /** 扩展板固件版本 */
    @Column(name = "t.FW_VERSION")
    private String fwVersion;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(value = "d.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;
}
