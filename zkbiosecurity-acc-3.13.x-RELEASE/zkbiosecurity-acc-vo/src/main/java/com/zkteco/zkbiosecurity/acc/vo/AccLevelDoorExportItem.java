package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> 导出门禁权限组门信息VO
 * @date 2022/7/20 11:29
 */
@From(after = "ACC_DOOR t LEFT JOIN ACC_LEVEL_DOOR ld ON ld.DOOR_ID=t.ID LEFT JOIN ACC_LEVEL al ON al.ID=ld.LEVEL_ID")
@OrderBy(after = "al.NAME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AccLevelDoorExportItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "al.NAME")
    @GridColumn(label = "common_level_name", width = "170", sortNo = 1)
    private String levelName;

    @Column(name = "t.NAME")
    @GridColumn(label = "acc_door_name", width = "170", sortNo = 2)
    private String doorName;

    // 导出门禁权限组门信息扩展字段
    @Condition(value = "ld.LEVEL_ID IN (%s)", formatType = "quote")
    private String levelIdsIn;

    /**
     * 导入行数
     */
    private Integer rowNum;

}