/**
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-14 下午03:02
 */
@From(after = "ACC_FIRSTOPEN t LEFT JOIN ACC_DOOR d ON t.DOOR_ID=d.ID " + "LEFT JOIN ACC_DEVICE e ON d.DEV_ID=e.ID "
    + "LEFT JOIN ACC_TIMESEG s ON t.TIMESEG_ID=s.ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 180, winWidth = 400,
    operates = {
        @GridOperate(type = "edit", permission = "acc:firstOpen:edit", url = "accFirstOpen.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "select", click = "afterAddFirstOpenPerson", permission = "acc:firstOpen:addPerson",
            url = "skip.do?page=acc_firstOpen_accFirstOpenSelectPersonContent&linkId=(id)&linkName=(doorName)",
            label = "pers_common_addPerson")})
@Getter
@Setter
public class AccFirstOpenItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "d.NAME")
    @GridColumn(columnType = "edit", label = "acc_door_name", sortNo = 1, width = "110",
        editPermission = "acc:firstOpen:edit", editUrl = "/accFirstOpen.do?edit")
    private String doorName;

    /**  */
    @Column(name = "e.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", sortNo = 2, width = "100")
    private String deviceAlias;

    @Column(name = "s.NAME")
    @GridColumn(label = "acc_door_passageModeTimeZone", sortNo = 3, width = "195")
    private String timeSegName;

    @GridColumn(label = "pers_common_personCount", sortNo = 4, width = "80", sort = "na", show = false)
    private String personCount;

    @Column(name = "d.ID")
    private String doorId;

    @Column(name = "s.ID")
    private String timeSegId;

    @Condition(value = "d.ID", equalTag = "in")
    private String doorIdsIn;

    @Condition(value = "e.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "e.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /** 数据迁移扩展字段 **/
    /** 门编号 */
    @Column(name = "d.DOOR_NO")
    private String doorNo;
    /** 设备号 **/
    @Column(name = "e.SN")
    private String deviceSn;

    /**
     * 默认构造方法
     */
    public AccFirstOpenItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccFirstOpenItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccFirstOpenItem(String id) {
        super(true);
        this.id = id;
    }
}