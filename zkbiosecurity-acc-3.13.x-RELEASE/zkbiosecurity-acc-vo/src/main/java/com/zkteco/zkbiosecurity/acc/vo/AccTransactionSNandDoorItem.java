package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR> href="mailto:<EMAIL>">jinjie.you</a>
 * @version V1.0
 * @date Created In 16:27 2018/8/14
 */

@From(after = "ACC_TRANSACTION t ")
@Where(after = " and t.pin !=' ' and t.pin is not null and t.event_No not in ('6','300') and t.event_Point_Type = 0")
@GridConfig
@Setter
@Getter
public class AccTransactionSNandDoorItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    @Column(name = "t.ID")
    private String id;

    @Column(name = "t.DEV_SN")
    private String devSn;

    @Column(name = "t.EVENT_ADDR")
    private Short eventAddr;

    @Column(name = "t.EVENT_TIME", equalTag = ">=")
    private Date beginDate;

    @Column(name = "t.EVENT_TIME", equalTag = "<=")
    private Date endDate;

    @Column(name = "t.EVENT_TIME")
    private Date eventTime;

    /**设备名称*/
    @Column(name = "t.DEV_ALIAS")
    private String devAlias;

    /**人员编号 */
    @Column(name = "t.PIN")
    private String pin;

    /**姓名 */
    @Column(name = "t.NAME")
    private String name;

    /**姓氏 */
    @Column(name = "t.LAST_NAME")
    private String lastName;

    /**人员卡号 */
    @Column(name = "t.CARD_NO")
    private String cardNo;

    /**部门编号 */
    @Column(name = "t.DEPT_CODE")
    private String deptCode;

    /**部门名称 */
    @Column(name = "t.DEPT_NAME")
    private String deptName;

    /**区域名称 */
    @Column(name = "t.AREA_NAME")
    private String areaName;

    /**验证方式 */
    @Column(name = "t.VERIFY_MODE_NO")
    private Short verifyModeNo;

}

