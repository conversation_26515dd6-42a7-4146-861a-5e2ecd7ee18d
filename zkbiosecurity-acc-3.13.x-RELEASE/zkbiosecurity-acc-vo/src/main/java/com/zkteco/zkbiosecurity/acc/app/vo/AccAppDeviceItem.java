package com.zkteco.zkbiosecurity.acc.app.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
public class AccAppDeviceItem implements Serializable {

    /**
     * 设备idID
     */
    private String id;

    private String alias;

    private String sn;

    private String authAreaName;

    private String deviceName;

    private String connectState;
}
