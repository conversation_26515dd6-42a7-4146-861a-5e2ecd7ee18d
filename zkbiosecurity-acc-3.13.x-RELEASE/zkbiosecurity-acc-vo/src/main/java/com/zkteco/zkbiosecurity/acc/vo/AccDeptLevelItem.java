package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 按部门设置右侧权限组VO
 * 
 * <AUTHOR>
 * @date 2018/3/14 14:48
 */
@From(after = "ACC_LEVEL t " + "LEFT JOIN ACC_TIMESEG ts ON t.TIMESEG_ID = ts.ID "
    + "LEFT JOIN ACC_LEVEL_DEPT ald ON ald.LEVEL_ID = t.ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id", winHeight = 200, winWidth = 400, operates = {})
public class AccDeptLevelItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /** 权限组名称 */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_level_name", width = "110", sortNo = 1)
    private String name;

    /** 系统区域id */
    @Column(name = "t.AUTH_AREA_ID")
    private String authAreaId;

    @GridColumn(label = "base_area_name", width = "110", sortNo = 2, sort = "na")
    private String authAreaName;

    @Column(name = "t.TIMESEG_ID")
    private String timeSegId;

    /**  */
    @Column(name = "ts.NAME")
    @GridColumn(label = "acc_timeSeg_entity", width = "110", sortNo = 3)
    private String timeSegName;

    @Column(name = "ald.DEPT_ID")
    @GridColumn(show = false)
    private String deptId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    /**
     * 数据迁移扩展字段
     */
    /** 权限组名称 **/
    private String levelName;

    /** 部门编号 */
    private String deptCode;

    public AccDeptLevelItem() {}

    public AccDeptLevelItem(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAuthAreaId() {
        return authAreaId;
    }

    public void setAuthAreaId(String authAreaId) {
        this.authAreaId = authAreaId;
    }

    public String getAuthAreaName() {
        return authAreaName;
    }

    public void setAuthAreaName(String authAreaName) {
        this.authAreaName = authAreaName;
    }

    public String getTimeSegId() {
        return timeSegId;
    }

    public void setTimeSegId(String timeSegId) {
        this.timeSegId = timeSegId;
    }

    public String getTimeSegName() {
        return timeSegName;
    }

    public void setTimeSegName(String timeSegName) {
        this.timeSegName = timeSegName;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getAuthAreaIdIn() {
        return authAreaIdIn;
    }

    public void setAuthAreaIdIn(String authAreaIdIn) {
        this.authAreaIdIn = authAreaIdIn;
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }
}
