/**
 * @author: GenerationTools
 * @date: 2018-03-20 下午04:20 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-20 下午04:20
 */
@From(after = "ACC_DEVICE_VERIFYMODE t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "acc:deviceVerifyMode:edit", url = "accDeviceVerifyMode.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:deviceVerifyMode:del", url = "accDeviceVerifyMode.do?del",
            label = "common_op_del")})
public class AccDeviceVerifyModeItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.DEV_ID")
    @GridColumn(label = "acc_deviceVerifyMode_device")
    private String deviceId;

    /**  */
    @Column(name = "t.VERIFY_NO")
    @GridColumn(label = "acc_deviceVerifyMode_verifyNo")
    private Short verifyNo;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "acc_deviceVerifyMode_name")
    private String name;

    /** 数据迁移 **/
    /** 设备号 **/
    private String deviceSn;

    /**
     * 默认构造方法
     */
    public AccDeviceVerifyModeItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccDeviceVerifyModeItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccDeviceVerifyModeItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param verifyNo
     * @param name
     */
    public AccDeviceVerifyModeItem(String id, Short verifyNo, String name) {
        super();
        this.id = id;
        this.verifyNo = verifyNo;
        this.name = name;
    }

    public AccDeviceVerifyModeItem(Short verifyNo, String name, String deviceId) {
        this.verifyNo = verifyNo;
        this.name = name;
        this.deviceId = deviceId;
    }

    /**
     * @return id 主键
     */
    public String getId() {
        return id;
    }

    /**
     * @param id
     *            要设置的 主键
     */
    public void setId(String id) {
        this.id = id;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    /**
     * @return verifyNo
     */
    public Short getVerifyNo() {
        return verifyNo;
    }

    /**
     * @param verifyNo
     *            要设置的
     */
    public void setVerifyNo(Short verifyNo) {
        this.verifyNo = verifyNo;
    }

    /**
     * @return name
     */
    public String getName() {
        return name;
    }

    /**
     * @param name
     *            要设置的
     */
    public void setName(String name) {
        this.name = name;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }
}