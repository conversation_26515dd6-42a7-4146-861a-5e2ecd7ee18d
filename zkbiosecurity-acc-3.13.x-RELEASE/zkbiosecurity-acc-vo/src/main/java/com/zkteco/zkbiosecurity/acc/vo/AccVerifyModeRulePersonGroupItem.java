package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

@From(after = "ACC_VERIFYMODE_RULE t LEFT JOIN ACC_TIMESEG p ON t.TIMESEG_ID=p.ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 180, winWidth = 400, operates = {@GridOperate(type = "select",
    click = "afterAddVerifyModeRulePerson", permission = "acc:verifyModeRulePersonGroup:addPerson",
    url = "skip.do?page=acc_verifyModeRulePersonGroup_accVerifyModeRuleSelectPersonContent&linkId=(id)&linkName=(name)",
    label = "pers_common_addPerson")})
@Getter
@Setter
public class AccVerifyModeRulePersonGroupItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "acc_common_ruleName", width = "150", sortNo = 1)
    private String name;

    @Column(name = "p.NAME")
    @GridColumn(label = "common_leftMenu_timeZone", width = "110", sortNo = 2)
    private String timeSegName;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTimeSegName() {
        return timeSegName;
    }

    public void setTimeSegName(String timeSegName) {
        this.timeSegName = timeSegName;
    }
}
