/**
 * <AUTHOR>
 * @date 2020/3/27 11:09
 */

package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@From(after = "ACC_FIRSTIN_LASTOUT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccFirstInLastOutItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(width = "0", show = false)
    private String id;

    /** 人员编号 */
    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", width = "100", encryptMode = "${pers.pin.encryptMode}",
            permission = "acc:pin:encryptProp")
    private String pin;

    /** 姓名 */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "100", encryptMode = "${pers.name.encryptMode}",
            permission = "acc:name:encryptProp")
    private String name;

    /** 姓氏 */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "100", showExpression = "#language!='zh_CN'",
        encryptMode = "${pers.lastName.encryptMode}", permission = "acc:name:encryptProp")
    private String lastName;

    /** first in读头名称 */
    @Column(name = "t.READER_NAME_IN")
    @GridColumn(label = "acc_inOut_inReaderName", width = "160")
    private String readerNameIn;

    /** first in事件时间 */
    @Column(name = "t.FIRST_IN_TIME")
    @GridColumn(label = "acc_inOut_firstInTime", width = "135")
    private Date firstInTime;

    /** last out读头名称 */
    @Column(name = "t.READER_NAME_OUT")
    @GridColumn(label = "acc_inOut_outReaderName", width = "160")
    private String readerNameOut;

    /** last out事件时间 */
    @Column(name = "t.LAST_OUT_TIME")
    @GridColumn(label = "acc_inOut_lastOutTime", width = "135")
    private Date lastOutTime;

    /** 部门编号 */
    @Column(name = "t.DEPT_CODE")
    @GridColumn(label = "pers_dept_deptNo", width = "100")
    private String deptCode;

    /** 部门名称 */
    @Column(name = "t.DEPT_NAME")
    @GridColumn(label = "pers_dept_deptName", width = "100")
    private String deptName;

    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    @Condition(value = "t.FIRST_IN_TIME", equalTag = ">=")
    private Date startTime;

    @Condition(value = "t.FIRST_IN_TIME", equalTag = "<=")
    private Date endTime;

    @Condition(value = "(t.DEPT_CODE in (%s) or t.DEPT_CODE is null or t.DEPT_CODE = '')", formatType = "quote")
    private String deptCodeIn;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
            value = "(t.DEPT_CODE IN (select ud.code from auth_department ud where ud.id in(select aud.auth_dept_id from auth_user_dept aud where aud.auth_user_id = ''{0}'')) or t.DEPT_CODE is null or t.DEPT_CODE = '''')")
    private String deptCodeInByUserId;

    /** 人员编号 */
    @Condition(value = "t.PIN", equalTag = "=")
    private String pinEqual;
}
