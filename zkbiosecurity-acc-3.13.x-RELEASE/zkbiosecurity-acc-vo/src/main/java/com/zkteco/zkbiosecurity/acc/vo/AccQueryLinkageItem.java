package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/7/19 11:23
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccQueryLinkageItem {

    /** 联动ID */
    private String id;

    /** 设备ID */
    private String deviceId;

    /** 设备序列号 */
    private String deviceSn;

    /** 联动名称 */
    private String name;

    /** 输入输出 */
    private List<AccLinkageInOutItem> accLinkageInOutList = new ArrayList<>();

    /** 触发条件 */
    private List<AccLinkageTriggerItem> accLinkageTriggerList = new ArrayList<>();
}
