package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 按人员设置VO
 * 
 * <AUTHOR>
 * @date 2018/3/14 14:18
 */

@From(
        after = "PERS_PERSON t LEFT JOIN AUTH_DEPARTMENT d ON t.AUTH_DEPT_ID = d.ID LEFT JOIN ACC_PERSON ap ON t.ID = ap.PERS_PERSON_ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {@GridOperate(type = "custom", permission = "acc:personLevelByPerson:addLevel",
        label = "common_level_addPersonLevel", click = "accPersonSelectLevel")})
@Getter
@Setter
public class AccPersonLevelByPersonItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /** 人员编号 */
    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", sortNo = 1, width = "90", encryptMode = "${pers.pin.encryptMode}",
            permission = "acc:pin:encryptProp")
    private String persPin;

    /** 姓名 */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", sortNo = 2, width = "100", encryptMode = "${pers.name.encryptMode}",
            permission = "acc:name:encryptProp")
    private String name;

    /** 姓 */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3, width = "100",
        encryptMode = "${pers.lastName.encryptMode}", permission = "acc:name:encryptProp")
    private String lastName;

    /** 部门名称 */
    @Column(name = "d.NAME")
    @GridColumn(label = "pers_dept_entity", sortNo = 4, width = "120", sort = "na")
    private String deptName;

    private String inDeptId;

    /** 启禁用凭证 */
    @Column(name = "t.ENABLED_CREDENTIAL")
    private Boolean enabledCredential;

    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    @Column(name = "ap.PRIVILEGE")
    @GridColumn(label = "pers_person_devOpAuth", sortNo = 5, width = "100", sort = "na", columnType = "custom",
        convert = "accConvertPrivilege")
    private Short privilege;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(value = "d.ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    @Column(name = "ap.START_TIME")
    @GridColumn(label = "common_level_startTime", sortNo = 5, width = "120", sort = "na")
    private Date startTime;

    @Column(name = "ap.END_TIME")
    @GridColumn(label = "common_level_endTime", sortNo = 6, width = "120", sort = "na")
    private Date endTime;

    public AccPersonLevelByPersonItem() {}

    public AccPersonLevelByPersonItem(String id) {
        this.id = id;
    }
}
