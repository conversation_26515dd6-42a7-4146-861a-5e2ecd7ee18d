/**
 * @author: GenerationTools
 * @date: 2018-03-13 上午09:53 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-13 上午09:53
 */
@From(after = "ACC_INTERLOCK t LEFT JOIN ACC_DEVICE p ON t.DEV_ID = p.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 500, winWidth = 520,
    operates = {
        @GridOperate(type = "edit", permission = "acc:interlock:edit", url = "accInterlock.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:interlock:del",
            url = "accInterlock.do?del&deviceNames=(deviceName)", label = "common_op_del")})
@Getter
@Setter
public class AccInterlockItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /** 业务id */
    @Column(name = "t.BUSINESS_ID")
    private Long businessId;

    /** 互锁名称 */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_name", width = "150", columnType = "edit", editUrl = "accInterlock.do?edit",
        editPermission = "acc:interlock:edit")
    private String name;

    /**  */
    @Column(name = "p.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "150")
    private String deviceName;

    @GridColumn(label = "acc_interlock_rule", width = "600", sort = "na")
    private String interlockRuleShow;

    @Column(name = "t.INTERLOCK_RULE")
    private Short interlockRule;

    @Column(name = "p.ID")
    private String deviceId;

    @Condition(value = "p.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    /** 互锁规则 1-组间互锁 2-组内互锁 */
    @Column(name = "t.LOCK_RULE")
    private Short lockRule;

    /** 组1 id */
    @Column(name = "t.TRIGGER_GROUP1")
    private String group1Id;

    /** 组2 id */
    @Column(name = "t.TRIGGER_GROUP2")
    private String group2Id;

    /** 组1的门名称 */
    //@GridColumn(label = "acc_interlock_group1", width = "200")
    private String group1DoorNames;

    /** 组2的门名称 */
    //@GridColumn(label = "acc_interlock_group2", width = "200")
    private String group2DoorNames;

    /** 组1的门id */
    private String group1DoorIds;

    /** 组2的门id */
    private String group2DoorIds;

    /** 过滤 */
    @Condition(value = "d.ID IN (%s)", formatType = "quote")
    private String devIdIn;

    /** 触发点1业务id */
    private Long group1BusinessId;

    /** 触发点2业务id */
    private Long group2BusinessId;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "p.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccInterlockItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccInterlockItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccInterlockItem(String id) {
        super(true);
        this.id = id;
    }
}