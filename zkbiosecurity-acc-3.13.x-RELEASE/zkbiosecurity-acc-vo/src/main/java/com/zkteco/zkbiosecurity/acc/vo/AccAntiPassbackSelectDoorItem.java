package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/8 14:53
 * @since 1.0.0
 */
@From(after = "ACC_DOOR t " + "LEFT JOIN ACC_DEVICE d ON d.ID=t.DEV_ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class AccAntiPassbackSelectDoorItem extends BaseItem {
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "acc_door_name", sortNo = 1, width = "120")
    private String doorName;

    @Column(name = "t.DOOR_NO")
    private Short doorNo;

    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev", sortNo = 2, width = "120")
    private String deviceAlias;

    @Column(name = "d.SN")
    @GridColumn(label = "common_dev_sn", sortNo = 3, width = "*")
    private String deviceSn;

    @Column(name = "d.ID")
    private String deviceId;

    @Column(name = "d.AUTH_AREA_ID")
    private String areaId;

    @Column(name = "t.ID", equalTag = "in")
    private String inId;

    @Column(name = "t.ID", equalTag = "not in")
    private String notInId;

    private String type;

    private String selectId;

    @Column(name = "t.ID", equalTag = "in")
    private String selectDoorIdsIn;

    @Column(name = "t.ID", equalTag = "not in")
    private String selectDoorIdsNotIn;

    @Column(name = "t.ENABLED")
    private Boolean enabled;

    private String group;

    @Column(name = "d.COMM_TYPE")
    private Short commType;
}
