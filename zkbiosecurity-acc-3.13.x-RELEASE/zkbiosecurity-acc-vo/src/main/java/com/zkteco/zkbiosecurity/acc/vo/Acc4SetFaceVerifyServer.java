package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Setter
@Getter
@Accessors(chain = true)
public class Acc4SetFaceVerifyServer implements Serializable {
    private static final long serialVersionUID = 1L;

    private String ids;

    private String faceBgServerType;

    private String isAccessLogic;

    private String faceVerifyMode;

    private String faceThreshold;

    private String faceBgServerURL;

    private String faceBgServerSecret;
}
