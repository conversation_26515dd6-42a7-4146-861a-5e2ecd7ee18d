package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
public class Acc4RemoteTemplate implements Serializable {
    private static final long serialVersionUID = 1L;

    private String pin;

    private String no;

    private String index;

    private String type;

    private String majorVer;

    private String minorVer;

    private String tmp;

    private String bioPhoto;

    private String version;
}
