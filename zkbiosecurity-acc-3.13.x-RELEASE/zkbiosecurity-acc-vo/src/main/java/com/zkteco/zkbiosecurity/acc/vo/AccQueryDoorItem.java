package com.zkteco.zkbiosecurity.acc.vo;

import java.util.List;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;

/**
 * 门状态
 *
 * <AUTHOR>
 * @date 2021-06-22 16:27
 * @since 1.0.0
 */
@From(after = "ACC_DOOR t")
@OrderBy(after = "t.DOOR_NO ASC")
public class AccQueryDoorItem {

    /** id */
    @Column(name = "t.ID")
    private String id;

    /** 门编号 */
    @Column(name = "t.DOOR_NO")
    private Short doorNo;

    /** 名称 */
    @Column(name = "t.NAME")
    private String name;

    /** 是否禁用声音 */
    @Column(name = "t.IS_DISABLE_AUDIO")
    private boolean isDisableAudio;

    /** 门是否禁用 */
    @Column(name = "t.ENABLED")
    private Boolean enabled;

    /** 读头缓存信息 */
    private List<AccQueryReaderItem> accReaderItemList;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Short getDoorNo() {
        return doorNo;
    }

    public void setDoorNo(Short doorNo) {
        this.doorNo = doorNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean getIsDisableAudio() {
        return isDisableAudio;
    }

    public void setIsDisableAudio(boolean disableAudio) {
        isDisableAudio = disableAudio;
    }

    public boolean isDisableAudio() {
        return isDisableAudio;
    }

    public void setDisableAudio(boolean disableAudio) {
        isDisableAudio = disableAudio;
    }

    public List<AccQueryReaderItem> getAccReaderItemList() {
        return accReaderItemList;
    }

    public void setAccReaderItemList(List<AccQueryReaderItem> accReaderItemList) {
        this.accReaderItemList = accReaderItemList;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }
}
