/**
 * @author: GenerationTools
 * @date: 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 按权限组设置vo
 * 
 * @author: lambert.li
 * @date: 2018-03-02 下午02:15
 */
@From(after = "ACC_LEVEL t " + "LEFT JOIN ACC_TIMESEG ts ON t.TIMESEG_ID = ts.ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 200, winWidth = 400,
    operates = {@GridOperate(type = "custom", permission = "acc:personLevelByLevel:addPerson",
        label = "pers_common_addPerson", click = "accLevelSelectPerson")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccPersonLevelByLevelItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /** 权限组名称 */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_level_name", width = "110", sortNo = 1)
    private String name;

    /** 系统区域id */
    @Column(name = "t.AUTH_AREA_ID")
    @GridColumn(show = false)
    private String authAreaId;

    @GridColumn(label = "base_area_name", width = "110", sortNo = 2, sort = "na")
    private String authAreaName;

    @Column(name = "t.TIMESEG_ID")
    @GridColumn(show = false)
    private String timeSegId;

    /**  */
    @Column(name = "ts.NAME")
    @GridColumn(label = "acc_timeSeg_entity", width = "110", sortNo = 3)
    private String timeSegName;

    /*@GridColumn(label = "pers_common_personCount", width = "80", sortNo = 4, sort="na")
    private String personCount;*/

    /**  */
    @Column(name = "t.INIT_FLAG")
    private Boolean initFlag;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "t.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccPersonLevelByLevelItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccPersonLevelByLevelItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccPersonLevelByLevelItem(String id) {
        super(true);
        this.id = id;
    }

    public AccPersonLevelByLevelItem(String name, Boolean initFlag, String authAreaId, String timeSegId) {
        this.name = name;
        this.initFlag = initFlag;
        this.authAreaId = authAreaId;
        this.timeSegId = timeSegId;
    }
}