package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 人员信息
 * 
 * @author: verber
 * @date: 2018-04-28 15:44:58
 */
@From(
    after = "ACC_LEVEL_PERSON t INNER join ACC_LEVEL_DOOR ald on ald.LEVEL_ID = t.LEVEL_ID LEFT JOIN ACC_LEVEL l ON t.LEVEL_ID=l.ID Left join PERS_PERSON p on p.id = t.PERS_PERSON_ID LEFT join AUTH_DEPARTMENT d on p.auth_dept_id = d.id")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
public class AccTransactionDoorPersonItem extends BaseItem {

    /** 主键 */
    @Column(name = "p.ID")
    @GridColumn(checkbox = true, width = "40", show = false)
    private String id;

    /**  */
    // @Column(name="pp.PIN")
    @Column(name = "p.PIN")
    @GridColumn(label = "pers_person_pin", width = "120", sort = "na", encryptMode = "${pers.pin.encryptMode}",
            permission = "acc:pin:encryptProp")
    private String pin;

    /**  */
    // @Column(name="pp.NAME")
    @Column(name = "p.NAME")
    @GridColumn(label = "pers_person_name", width = "120", sort = "na", encryptMode = "${pers.name.encryptMode}",
            permission = "acc:name:encryptProp")
    private String name;

    @Column(name = "p.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "120", showExpression = "#language!='zh_CN'", sort = "na",
        encryptMode = "${pers.lastName.encryptMode}", permission = "acc:name:encryptProp")
    private String lastName;
    /**  */
    @Column(name = "d.NAME")
    @GridColumn(label = "pers_dept_deptName", width = "120", sort = "na")
    private String deptName;

    /**  */
    @Column(name = "ald.DOOR_ID")
    private String doorId;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(value = "d.ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public AccTransactionDoorPersonItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccTransactionDoorPersonItem(Boolean equals) {
        super();
    }

    /**
     * @param id
     */
    public AccTransactionDoorPersonItem(String id) {
        super();
        this.id = id;
    }

    /**
     * @param id
     * @param pin
     * @param name
     * @param deptName
     * @param doorId s
     */
    public AccTransactionDoorPersonItem(String id, String pin, String name, String deptName, String doorId) {
        super();
        this.id = id;
        this.pin = pin;
        this.name = name;
        this.deptName = deptName;
        this.doorId = doorId;
    }
}