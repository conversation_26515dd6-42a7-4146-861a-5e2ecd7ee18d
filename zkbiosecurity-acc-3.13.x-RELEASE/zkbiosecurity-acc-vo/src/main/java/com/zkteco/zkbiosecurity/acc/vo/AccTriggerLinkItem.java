package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/8 13:49
 * @since 1.0.0
 */
@From(after = "ACC_TRIGGER_LINK t LEFT JOIN ACC_DEVICE dev on t.DEVICE_ID = dev.ID")
@OrderBy(after = "t.CREATE_TIME ASC")
@Getter
@Setter
@Accessors(chain = true)
public class AccTriggerLinkItem extends BaseItem {
    @Column(name = "t.ID")
    private String id;

    /** 门禁设备id **/
    @Column(name = "t.DEVICE_ID")
    private String deviceId;

    /** type: 0互锁 1反潜 **/
    @Column(name = "t.TYPE", equalTag = "=")
    private Short type;

    /** 关联id(反潜 或者 互锁表的记录id) **/
    @Column(name = "t.LINK_ID", equalTag = "=")
    private String linkId;

    /** 设备序列号 **/
    @Column(name = "dev.SN")
    private String sn;

    @Condition(value = "t.DEVICE_ID IN (%s)", formatType = "quote")
    private String inDeviceId;

    @Condition(value = "t.LINk_ID IN (%s)", formatType = "quote")
    private String inLinkId;
}
