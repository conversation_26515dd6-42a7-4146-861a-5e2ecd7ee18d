/**
 * @author: GenerationTools
 * @date: 2018-03-08 下午02:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;

/**
 * 门禁设备查询vo
 * 
 * @author: yulong.dai
 * @date: 2018-03-08 下午02:41
 */
@From(after = "ACC_DEVICE t")
@OrderBy(after = "t.BUSINESS_ID ASC")
@Getter
@Setter
@Accessors(chain = true)
public class AccQueryDeviceItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID", equalTag = "=")
    private String id;

    @Column(name = "t.DEV_ALIAS")
    private String alias;

    @Column(name = "t.SN", equalTag = "=")
    private String sn;

    @Column(name = "t.AUTH_AREA_ID", equalTag = "=")
    private String authAreaId;

    @GridColumn(label = "base_area_name", width = "70", sortNo = 3, sort = "na")
    private String authAreaName;

    @Column(name = "t.COMM_TYPE")
    private Short commType;

    @Column(name = "t.DEVICE_NAME")
    private String deviceName;

    @Column(name = "t.IS_REGISTRATIONDEVICE")
    private Boolean isRegistrationDevice;

    @Column(name = "t.MACHINE_TYPE")
    private Short machineType;

    @Column(name = "t.WG_READER_ID")
    private String wgReaderId;

    @Column(name = "t.ENABLED")
    private Boolean enabled;

    @Column(name = "t.ICON_TYPE")
    private Short iconType;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Column(name = "t.BUSINESS_ID")
    private Long businessId;

    @Column(name = "t.IP_ADDRESS")
    private String ipAddress;

    @Column(name = "t.IP_PORT")
    private Integer ipPort;

    @Column(name = "t.FW_VERSION")
    private String fwVersion;

    @Column(name = "t.PARENT_ID")
    private String parentDeviceId;

    /** 扩展板信息 */
    private List<AccQueryExtDeviceItem> accQueryExtDeviceItemList = new ArrayList<>();
    /** 缓存设备下的门、辅助输入、辅助输出 */
    private List<AccQueryDoorItem> accDoorItemList = new ArrayList<>();
    private List<AccQueryAuxInItem> accAuxInItemList = new ArrayList<>();
    private List<AccQueryAuxOutItem> accAuxOutItemList = new ArrayList<>();
}