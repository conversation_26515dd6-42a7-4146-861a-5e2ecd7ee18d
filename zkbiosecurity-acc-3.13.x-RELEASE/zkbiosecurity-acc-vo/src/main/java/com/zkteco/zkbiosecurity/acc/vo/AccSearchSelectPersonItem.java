/*
 * File Name: AccSearchSelectPersonItem
 * <NAME_EMAIL> on 2018/5/17 18:30.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
public class AccSearchSelectPersonItem implements Serializable {

    private String personId;//人员id
    private String personPin;//编号
    private String personName;//姓名
    private String personLastName;//姓
    private String cardNo;//卡号
    private String deptName;//部门名称
    private String linkId;//关联id （levelId, globalLinkageId等）
    private String type;//关联表

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getPersonPin() {
        return personPin;
    }

    public void setPersonPin(String personPin) {
        this.personPin = personPin;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getPersonLastName() {
        return personLastName;
    }

    public void setPersonLastName(String personLastName) {
        this.personLastName = personLastName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getLinkId() {
        return linkId;
    }

    public void setLinkId(String linkId) {
        this.linkId = linkId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
