package com.zkteco.zkbiosecurity.acc.app.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 2018/12/6 13:35
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccAppDoorItem implements Serializable {

    /**
     * 门ID
     */
    private String id;

    /**
     * 门名称
     */
    private String doorName;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 门报警状态（0：无报警。1：报警，2：禁用，3：离线）
     */
    private Integer doorAlarmStatus;

    /**
     * 门报警类型 1门被意外打开，2防拆，3胁迫密码开门，4胁迫指纹开门，5胁迫开门，6门开超时，7常开报警，8电池电压过低，9立即更换电池，10非法操作，11启用后备电源
     */
    private Integer alarm;

    /**
     * 区域名称
     */
    private String areaName;

    /** 时区 */
    private String timeZone;

    /** 门磁 空值-未知，0无门磁，2关闭，1打开 */
    private String sensor;

    /** 继电器 继电器状态（0关闭，1打开，空值-未知） */
    private String relay;

    /** 门状态 */
    private short doorStatus;

    private String filter;

    private String selectAreaId;
}
