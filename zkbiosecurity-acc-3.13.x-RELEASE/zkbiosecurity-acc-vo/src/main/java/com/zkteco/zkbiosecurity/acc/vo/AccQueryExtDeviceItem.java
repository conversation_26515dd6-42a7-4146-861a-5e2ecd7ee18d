package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class AccQueryExtDeviceItem extends BaseItem {

    /**
     * 主键
     */
    private String id;

    /**
     * 扩展板名称
     */
    private String alias;

    /**
     * 所属设备id
     */
    private String devId;

    /**
     * 扩展板编号
     */
    private Short extBoardNo;
}
