package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@From(after = "ACC_DEVICE t")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain=true)
public class AccExtSelectDeviceItem extends BaseItem {

    @Column(name = "t.ID")
    @GridColumn(columnType = "ra", width = "40", sortNo = 0)
    private String id;

    /**设备名称*/
    @Column(name = "t.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", sortNo = 1, width = "120")
    private String deviceAlias;

    /**设备型号*/
    @Column(name = "t.DEVICE_NAME")
    @GridColumn(label = "common_dev_deviceModel", sortNo = 1, width = "90")
    private String deviceName;

    /**区域ID*/
    @Column(name="t.AUTH_AREA_ID", equalTag = "=")
    private String authAreaId;

    /**区域名称*/
    @GridColumn(label = "base_area_name", width = "90", sortNo = 3)
    private String authAreaName;

    /**设备序列号*/
    @Column(name = "t.SN")
    @GridColumn(label = "common_dev_sn", sortNo = 2,width = "*")
    private String deviceSn;

    //判断是左列表（值为noSelect）还是右列表（值为select）
    private String type;

    private String selectId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;
}
