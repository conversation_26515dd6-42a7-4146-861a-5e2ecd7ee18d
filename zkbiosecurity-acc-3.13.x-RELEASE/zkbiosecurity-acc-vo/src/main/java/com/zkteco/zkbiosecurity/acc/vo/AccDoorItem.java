/**
 * @author: GenerationTools
 * @date: 2018-03-03 上午11:59 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁门vo
 *
 * <AUTHOR>
 * @date: 2018-04-24 上午11:59
 */
@From(after = "ACC_DOOR t " + "LEFT JOIN ACC_DEVICE d ON d.ID=t.DEV_ID "
    + "LEFT JOIN ACC_TIMESEG ats ON ats.ID=t.ACTIVE_TIMESEG_ID ")
@OrderBy(after = "d.BUSINESS_ID ASC, t.DOOR_NO ASC")
@GridConfig(operate = true, idField = "id", winHeight = 570, winWidth = 910,
    operates = {
        @GridOperate(type = "edit", permission = "acc:door:edit", url = "accDoor.do?edit", label = "common_op_edit")})
@Getter
@Setter
@Accessors(chain = true)
public class AccDoorItem extends BaseItem {

    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "acc_door_name", width = "150", sortNo = 1, columnType = "edit",
        editPermission = "acc:door:edit", editUrl = "/accDoor.do?edit")
    private String name;

    @Column(name = "d.AUTH_AREA_ID", equalTag = "=")
    private String authAreaId;

    @GridColumn(label = "base_area_name", width = "100", sortNo = 2)
    private String authAreaName;

    @Column(name = "t.DEV_ID")
    private String deviceId;

    @Column(name = "d.MACHINE_TYPE")
    private String devMachineType;

    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev", width = "150", sortNo = 3)
    private String deviceAlias;

    @Column(name = "d.SN")
    @GridColumn(label = "common_dev_sn", width = "150", sortNo = 4)
    private String deviceSn;

    @Column(name = "t.DOOR_NO")
    @GridColumn(label = "acc_door_number", width = "100", sortNo = 5)
    private Short doorNo;

    @Column(name = "t.ENABLED")
    @GridColumn(label = "common_enable", width = "60", sortNo = 6, convert = "convertToIcon", columnType = "custom",
        showExpression = "#language!='zh_CN'")
    private Boolean enabled;

    @Column(name = "t.ACTIVE_TIMESEG_ID")
    private String activeTimeSegId;

    @Column(name = "ats.NAME")
    @GridColumn(label = "acc_door_activeTimeZone", width = "150", sortNo = 7)
    private String activeTimeSegName;

    @Column(name = "t.DOOR_SENSOR_STATUS")
    @GridColumn(label = "acc_door_sensorType", width = "130", sortNo = 8,
        format = "0=acc_eventNo_-1,1=acc_door_normalOpen,2=acc_door_normalClose")
    private Short doorSensorStatus;

    @Column(name = "t.VERIFY_MODE")
    private Short verifyMode;

    /** 验证方式名称,前端解析 */
    @GridColumn(label = "common_verifyMode_entiy", width = "150", sortNo = 9, columnType = "custom",
        convert = "convertVerifyModeName")
    private String verifyModeName;

    @GridColumn(label = "acc_ownedBoard", width = "110", sortNo = 10, showExpression = "#language!='zh_CN'")
    private String extDevName;

    @Column(name = "t.PASSMODE_TIMESEG_ID")
    private String passModeTimeSegId;

    /**  */
    @Column(name = "t.WG_INPUT_ID")
    private String wgInputFmtId;

    /**  */
    @Column(name = "t.WG_INPUT_TYPE")
    private Short wgInputType;

    /**  */
    @Column(name = "t.WG_OUTPUT_ID")
    private String wgOutputFmtId;

    /**  */
    @Column(name = "t.WG_OUTPUT_TYPE")
    private Short wgOutputType;

    /**  */
    @Column(name = "t.LOCK_DELAY")
    private Short lockDelay;

    /**  */
    @Column(name = "t.ACTION_INTERVAL")
    private Short actionInterval;

    /**  */
    @Column(name = "t.SENSOR_DELAY")
    private Short sensorDelay;

    /**  */
    @Column(name = "t.BACK_LOCK")
    private Boolean backLock;

    /**  */
    @Column(name = "t.FORCE_PWD", encryptConverter = true)
    private String forcePwd;

    /**  */
    @Column(name = "t.SUPPER_PWD", encryptConverter = true)
    private String supperPwd;

    /**  */
    @Column(name = "t.IN_APB_DURATION")
    private Short inApbDuration;

    /**  */
    @Column(name = "t.LATCH_DOOR_TYPE")
    private Short latchDoorType;

    /**  */
    @Column(name = "t.LATCH_TIME_OUT")
    private Short latchTimeOut;

    /**  */
    @Column(name = "t.LATCH_TIMESEG_ID")
    private String latchTimeSegId;

    /**  */
    @Column(name = "t.READER_TYPE")
    private Short readerType;

    /**  */
    @Column(name = "t.HOST_STATUS")
    private Short hostStatus;

    @Column(name = "t.SLAVE_STATUS")
    private Short slaveStatus;

    /**  */
    @Column(name = "t.DELAY_OPEN_TIME")
    private Short delayOpenTime;

    /**  */
    @Column(name = "t.EXT_DELAY_DRIVERTIME")
    private Short extDelayDrivertime;

    /**  */
    @Column(name = "t.IS_DISABLE_AUDIO")
    private Boolean isDisableAudio;

    /** 是否卡号反转 */
    @Column(name = "t.WG_REVERSED")
    private Short wgReversed;

    /** 多人开门操作间隔 */
    @Column(name = "t.COMBOPEN_INTERVAL")
    private Short combOpenInterval;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notId;

    @Condition(value = "d.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Condition(value = "d.ID IN (%s)", formatType = "quote")
    private String devIdIn;

    @Column(name = "t.ALLOW_SUACCESS_LOCK")
    private String allowSUAccessLock;

    @Column(name = "t.EXT_DEV_ID")
    private String extDevId;

    // pro

    /** 门磁监控输入模式 */
    @Column(name = "t.SEN_INPUT_MODE")
    private String senInputMode;

    /** 门磁监控电阻值 */
    @Column(name = "t.SEN_SUPERVISED_RESISTOR")
    private String senSupervisedResistor;

    /** 门磁监控输入模式 */
    @Column(name = "t.SEX_INPUT_MODE")
    private String rexInputMode;

    /** 门磁监控电阻值 */
    @Column(name = "t.SEX_SUPERVISED_RESISTOR")
    private String rexSupervisedResistor;

    /** 设备businessId */
    @Column(name = "d.BUSINESS_ID")
    private Long businessId;

    /** 出门开关类型 */
    @Column(name = "t.REX_BUTTON_TYPE")
    private String rexButtonType;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "d.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 根据门名称或设备名称模糊查询
     */
    @Condition(value = "(t.NAME LIKE ''%{0}%'' OR d.DEVICE_NAME = ''%{0}%'')")
    private String doorOrDeviceName;
    @Column(name = "d.TIME_ZONE")
    private String timeZone;

    @Column(name = "t.DOOR_PWD", encryptConverter = true)
    private String doorPwd;

    /**
     * 默认构造方法
     */
    public AccDoorItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccDoorItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccDoorItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param wgInputType
     * @param wgOutputType
     * @param doorNo
     * @param name
     * @param lockDelay
     * @param actionInterval
     * @param doorSensorStatus
     * @param sensorDelay
     * @param backLock
     * @param verifyMode
     * @param forcePwd
     * @param supperPwd
     * @param inApbDuration
     * @param latchDoorType
     * @param latchTimeOut
     * @param readerType
     * @param hostStatus
     * @param delayOpenTime
     * @param extDelayDrivertime
     * @param isDisableAudio
     * @param enabled
     * @param wgReversed
     * @param combOpenInterval
     */
    public AccDoorItem(String id, Short wgInputType, Short wgOutputType, Short doorNo, String name, Short lockDelay,
        Short actionInterval, Short doorSensorStatus, Short sensorDelay, Boolean backLock, Short verifyMode,
        String forcePwd, String supperPwd, Short inApbDuration, Short latchDoorType, Short latchTimeOut,
        Short readerType, Short hostStatus, Short delayOpenTime, Short extDelayDrivertime, Boolean isDisableAudio,
        Boolean enabled, Short wgReversed, Short combOpenInterval) {
        super();
        this.id = id;
        this.wgInputType = wgInputType;
        this.wgOutputType = wgOutputType;
        this.doorNo = doorNo;
        this.name = name;
        this.lockDelay = lockDelay;
        this.actionInterval = actionInterval;
        this.doorSensorStatus = doorSensorStatus;
        this.sensorDelay = sensorDelay;
        this.backLock = backLock;
        this.verifyMode = verifyMode;
        this.forcePwd = forcePwd;
        this.supperPwd = supperPwd;
        this.inApbDuration = inApbDuration;
        this.latchDoorType = latchDoorType;
        this.latchTimeOut = latchTimeOut;
        this.readerType = readerType;
        this.hostStatus = hostStatus;
        this.delayOpenTime = delayOpenTime;
        this.extDelayDrivertime = extDelayDrivertime;
        this.isDisableAudio = isDisableAudio;
        this.enabled = enabled;
        this.wgReversed = wgReversed;
        this.combOpenInterval = combOpenInterval;
    }
}