package com.zkteco.zkbiosecurity.acc.constants;

/**
 * <AUTHOR>
 * @date 2018/4/23 15:48
 */
public class AccCacheKeyConstants {

    public static final String TEMP_DEV = "acc:tempDevice:";// 临时设备
    // 获取待处理入库的事件队列key
    public static final String ACC_TRANSACTIONS_TO_DB = "acc:accTransactionsToDb";// 事件入库
    public static final String ACC_PERSON_LAST_ADDR = "acc:accPersonLastAddr";
    // 实时事件入库异常记录
    public static final String ACC_TRANSACTIONS_TO_DB_EXCEPTION = "acc:accTransactionsToDbException";

    // 标记每次从redis里获取待入库的事件记录条数
    public static int GET_TRANSACTIONS_TO_DB_INDEX = 499;

    public static final String ACC_RTLOG_QUENE_NAME_SET = "ACC_RTLOG_QUENE_NAME_SET";// redis存放的实时事件队列名称

    public static final String ACC_RTLOGS = "acc:rtLogs:";

    public static final String REDIS_ACC_EVENT = "cache:acc:devEventMap:";// redis中存储事件类型的key+sn
    public static final String REDIS_ACC_VERIFY_MODE = "cache:acc:verifyModeMap:";// redis中存储验证方式的key+sn

    // 设备最近异常状态
    public static final String DEV_LAST_ERROR = "acc:devLastError";
    // 保存设备最近上传事件，用于硬联动取前一条事件记录来判断触发点
    public static final String DEV_LAST_RTLOG = "acc:devLastRtLog";
    // 设备上传查询数据key
    public static final String QUERY_DATA = "acc:queryData:";
    // 设备上传查询数据条数key
    public static final String QUERY_DATA_COUNT = "acc:dataCount:";
    // 存放卡号重复key
    public static final String QUERY_ACC_CONFLICT_CARD_CMDID = "acc:queryConflictCard:";
    // 存放卡号重复key
    public static final String ACCOUNT_DATA_ACC = "acc:accountData:%s_%s";// sn_cmdId
    // TODO 暂时命名
    public static final String ERROR_USER = "IS_ERROR_USER";
    public static final String SUM_RECORD_USER = "SUM_RECORD_USER";

    /** 搜索WIFI List KEY */
    public static final String SEARCH_WIFI_LIST = "acc:searchWifiList:";
    /** 虚拟待授权子设备 KEY */
    public static final String DEVICE_AUTHORIZE_KEY = "acc:deviceAuthorize:";
    /** 授权子设备 KEY */
    public static final String AUTHORIZE_KEY = "acc:authorize:";
    /** 身份登记模式下上传 KEY + cmdID */
    public static final String IDENTITY_CARD_INFO_KEY = "acc:identityCardInfo:";

    // 报警事件收件人邮箱 redis中 key
    public static final String ACC_ALARM_RECEIVER = "acc:alarmReceiver";
    // 报警事件收件人电话号码 redis中 key
    public static final String ACC_ALARM_SMSRECEIVER = "acc:alarmSMSReceiver";

    // 门禁设备信息
    public static final String ACC_DEVICE_INFO = "acc:deviceInfo:";
    // 门禁设备参数信息
    public static final String ACC_DEVICE_OPTIONS = "acc:devOptions:";
    // 门禁设备状态
    public static final String ACC_DEVICE_STATE = "acc:devState:";
    // 门禁实时监控客户端
    public static final String ACC_MONITOR_CLIENT = "acc:monitor:client:";
    // 门禁实时监控客户端记录
    public static final String ACC_MONITOR_CLIENT_RTLOG = "acc:monitor:rtLog:";

    // 远程登记指纹/指静脉
    public static final String ACC_REMOTE_TEMPLATE = "acc:remote:template:";
    // 抓拍照片
    public static final String ACC_ATT_PHOTO = "acc:attPhoto:";

    public static final String ACC_TRANSACTION_PHOTO_TO_DB = "acc:accTransactionPhotoToDB";

    // Digifort 视频联动key前缀
    public static final String DIGIFORT_LINKAGE_CAMERA_PREFIX = "ivideo:digifortVideoLinkage:";

    /** 人员进出记录拉取时间 */
    public static final String ACC_FIRSTINLASTOUT_TIME = "acc:firstInLastOutTime";

    /** 联动信息 */
    public static final String ACC_LINKAGE_INFO = "acc:linkageInfo:";

    /** 绑定的视频通道信息 */
    public static final String ACC_BIND_CHANNEL_INFO = "acc:bindChannel:";

    public static final String IMME_CMD = "ADMS_IMME_CMD:";
    public static final String COMMON_CMD = "ADMS_COMMON_CMD:";

    /** 设备信息缓存key */
    String ADMS_DEVICE_INFO = "adms:devInfo:";

    /** ADMS心跳存在key */
    public static final String ADMS_DEV_HEARTBEAT = "adms:heartbeat:";

    public static final String ACC_DEVICE_EVENT = "acc:devEventItem:";

    public static final String ACC_TRANSACTION_VALIDTIME = "acc:transaction:validTime:";
}
