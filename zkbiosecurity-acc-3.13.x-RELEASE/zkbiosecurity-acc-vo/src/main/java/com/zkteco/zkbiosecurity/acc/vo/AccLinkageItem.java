/**
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-03-16 下午04:41
 */
@From(after = "ACC_LINKAGE t LEFT JOIN ACC_DEVICE d ON t.DEV_ID=d.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 650, winWidth = 800, operates = {
    @GridOperate(type = "edit", permission = "acc:linkage:edit", url = "accLinkage.do?edit", label = "common_op_edit"),
    @GridOperate(type = "del", permission = "acc:linkage:del", url = "accLinkage.do?del&names=(name)",
        label = "common_op_del")})
@Getter
@Setter
public class AccLinkageItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(columnType = "edit", label = "common_linkIO_linkageName", width = "120", sortNo = 1,
        editPermission = "acc:linkage:edit", editUrl = "/accLinkage.do?edit")
    private String name;

    /**  */
    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_dev_name", width = "150", sortNo = 2)
    private String deviceAlias;

    @GridColumn(label = "common_linkIO_conditions", width = "400", sortNo = 3, sort = "na")
    private String triggerCond;

    @Column(name = "d.ID")
    private String deviceId;

    @Condition(value = "d.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    // private String triggerCond;

    private String triggerCondName;

    private String inputType;

    private Short doorActionType;

    private Short doorActionTime;

    private Short auxoutActionType;

    private Short auxoutActionTime;

    private String mailAddr;

    private String popUpVideo;

    private Integer popUpTime;

    private String record;

    /** 录像回放：事件发生前（）s */
    private Integer recordBeforeTime;
    /** 原录像时长，复用此字段；录像回放：事件发生后（）s */
    private Integer recordTime;

    private String capture;

    private Integer captureTime;

    private String mobileNo;

    private String digifortEventName;

    private String lineContactId;

    private String whatsappMobileNo;
    private String vdbIvrId;
    private String vdbExtensionId;

    /** 分区动作类型 */
    private Short partitionActionType;
    /** 分区ids */
    private String iasPartitionIds;
    /** 厂商 */
    private String iasManufacture;
    /** 布防类型 */
    private Short partitionArmType;

    /**
     * 默认构造方法
     */
    public AccLinkageItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccLinkageItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccLinkageItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     */
    public AccLinkageItem(String id, String name) {
        super();
        this.id = id;
        this.name = name;
    }

}