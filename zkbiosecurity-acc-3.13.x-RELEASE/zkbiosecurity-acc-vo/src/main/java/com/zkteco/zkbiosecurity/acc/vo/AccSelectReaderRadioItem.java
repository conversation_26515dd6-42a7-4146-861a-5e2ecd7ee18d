package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@From(after = "ACC_READER t " + "LEFT JOIN ACC_DOOR ad ON ad.ID = t.DOOR_ID "
    + "LEFT JOIN ACC_DEVICE dev ON dev.ID = ad.DEV_ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class AccSelectReaderRadioItem extends BaseItem {

    @Column(name = "t.ID")
    @GridColumn(columnType = "ra", width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "acc_readerDefine_readerName", width = "130", sortNo = 1)
    private String name;

    /**  */
    @Column(name = "ad.NAME")
    @GridColumn(label = "acc_door_name", width = "130", sortNo = 2)
    private String doorName;

    /** 设备名称 */
    @Column(name = "dev.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev", width = "100", sortNo = 3)
    private String deviceAlias;

    @Column(name = "dev.SN")
    private String devSn;

    @Condition(value = "t.id", equalTag = "in")
    private String inId;

    @Condition(value = "t.id", equalTag = "not in")
    private String notInId;

    private String type;

    private String selectId;

    @Condition(value = "dev.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "dev.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    public AccSelectReaderRadioItem() {}
}
