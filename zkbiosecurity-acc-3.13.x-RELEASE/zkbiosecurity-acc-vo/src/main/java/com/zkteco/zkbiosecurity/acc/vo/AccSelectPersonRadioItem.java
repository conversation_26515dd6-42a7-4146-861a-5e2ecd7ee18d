package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@From(
    after = "PERS_PERSON t LEFT JOIN PERS_CARD pc ON t.ID = pc.PERSON_ID LEFT JOIN AUTH_DEPARTMENT d ON t.auth_dept_id = d.id")
@Where(after = " AND (pc.CARD_NO is null or pc.CARD_NO = '')")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class AccSelectPersonRadioItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(columnType = "ra", width = "40", sortNo = 0)
    private String id;

    private String personId;

    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", sortNo = 1, width = "80")
    private String personPin;

    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", sortNo = 2, width = "80")
    private String personName;

    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", showExpression = "#language!='zh_CN'", sortNo = 3, width = "80")
    private String personLastName;

    @Column(name = "pc.CARD_NO", encryptConverter = true)
    @GridColumn(label = "pers_card_cardNo", sortNo = 4, width = "65")
    private String cardNo;

    @Column(name = "t.GENDER")
    @GridColumn(label = "pers_person_gender", format = "F=pers_person_female,M=pers_person_male,=---", sortNo = 5,
        show = false)
    private String gender;

    @Column(name = "d.NAME")
    @GridColumn(label = "pers_dept_entity", sortNo = 6, width = "*")
    private String deptName;

    private String beginPin;

    private String endPin;

    private String type;

    @Condition(value = "t.ID", equalTag = "not in")
    private String selectId;

    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(value = "d.ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    public AccSelectPersonRadioItem() {}

    public AccSelectPersonRadioItem(String id, String personId, String personPin, String personName,
        String personLastName, String cardNo, String gender, String deptName, String beginPin, String endPin) {
        this.id = id;
        this.personId = personId;
        this.personPin = personPin;
        this.personName = personName;
        this.personLastName = personLastName;
        this.cardNo = cardNo;
        this.gender = gender;
        this.deptName = deptName;
        this.beginPin = beginPin;
        this.endPin = endPin;
    }
}
