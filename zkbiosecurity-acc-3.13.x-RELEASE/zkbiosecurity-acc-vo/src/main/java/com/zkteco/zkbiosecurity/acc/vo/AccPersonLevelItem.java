package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 按人员设置右侧权限VO
 * 
 * <AUTHOR>
 * @date 2018/3/14 14:24
 */
@From(after = "ACC_LEVEL t " + "LEFT JOIN ACC_TIMESEG ts ON t.TIMESEG_ID = ts.ID "
    + "LEFT JOIN ACC_LEVEL_PERSON alp ON alp.LEVEL_ID = t.ID ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id", winHeight = 200, winWidth = 400, operates = {})
@Getter
@Setter
@Accessors(chain = true)
public class AccPersonLevelItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /** 权限组名称 */
    @Column(name = "t.NAME")
    @GridColumn(label = "common_level_name", width = "110", sortNo = 1)
    private String name;

    /** 系统区域id */
    @Column(name = "t.AUTH_AREA_ID")
    private String authAreaId;

    @GridColumn(label = "base_area_name", width = "110", sortNo = 2, sort = "na")
    private String authAreaName;

    @Column(name = "t.TIMESEG_ID")
    private String timeSegId;

    /**  */
    @Column(name = "ts.NAME")
    @GridColumn(label = "acc_timeSeg_entity", width = "110", sortNo = 3)
    private String timeSegName;

    /** 开始时间 */
    @GridColumn(label = "common_level_startTime", width = "125", sortNo = 4, type = "dhxCalendar",
        showHeader = "accShowLevelTime")
    @Column(name = "alp.START_TIME")
    private Date startTime;

    /** 结束时间 */
    @GridColumn(label = "common_level_endTime", width = "125", sortNo = 5, type = "dhxCalendar",
        showHeader = "accShowLevelTime")
    @Column(name = "alp.END_TIME")
    private Date endTime;

    @Column(name = "alp.PERS_PERSON_ID", equalTag = "=")
    @GridColumn(show = false)
    private String personId;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String authAreaIdIn;

    @Condition(value = "alp.PERS_PERSON_ID IN (%s)", formatType = "quote")
    private String personIdIn;

    /**
     * 数据迁移扩展字段
     */
    /** 人员编号 **/
    private String pin;
    /** 权限组名称 **/
    private String levelName;

    public AccPersonLevelItem() {}

    public AccPersonLevelItem(String id) {
        this.id = id;
    }
}
