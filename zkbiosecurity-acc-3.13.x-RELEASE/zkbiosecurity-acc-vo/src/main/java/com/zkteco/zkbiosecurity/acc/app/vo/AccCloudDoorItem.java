package com.zkteco.zkbiosecurity.acc.app.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2019/11/7 11:27
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccCloudDoorItem implements Serializable {

    /**
     * 门编号
     */
    private Short doorNo;

    /**
     * 设备SN
     */
    private String devSn;

    /**
     * 门名称
     */
    private String doorName;

    /**
     * 是否置顶门 1 置顶 0 不置顶
     */
    private String topDoor;

    private String doorStatus;
}
