package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

/**
 * 异常记录表
 * 
 * @author: AI Assistant
 * @date: 2025-07-21
 */

@From(after = "ACC_EXCEPTION_RECORD t ")
@OrderBy(after = "t.EXIT_TIME DESC")
@GridConfig
@Getter
@Setter
public class AccExceptionRecordItem extends BaseItem {
    
    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(width = "0", sortNo = 0, show = false)
    private String id;

    /** 工号 */
    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", width = "100", sortNo = 1)
    private String pin;

    /** 姓名 */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "100", sortNo = 2)
    private String name;

    /** 部门名称 */
    @Column(name = "t.DEPT_NAME")
    @GridColumn(label = "pers_dept_deptName", width = "120", sortNo = 3)
    private String deptName;

    /** 接收人员职位 */
    @Column(name = "t.RECEIVER_POSITION")
    @GridColumn(label = "acc_exception_receiverPosition", width = "120", sortNo = 4)
    private String receiverPosition;

    /** 读头名称 */
    @Column(name = "t.READER_NAME")
    @GridColumn(label = "acc_readerDefine_readerName", width = "120", sortNo = 5)
    private String readerName;

    /** 外出时间 */
    @Column(name = "t.EXIT_TIME")
    @GridColumn(label = "acc_exception_exitTime", width = "150", sortNo = 6)
    private Date exitTime;

    /** 进入时间 */
    @Column(name = "t.ENTER_TIME")
    @GridColumn(label = "acc_exception_enterTime", width = "150", sortNo = 7)
    private Date enterTime;



    /** 主题（异常进出、迟到） */
    @Column(name = "t.SUBJECT", equalTag = "in")
    @GridColumn(label = "acc_exception_subject", width = "120", sortNo = 8, format = "1=异常进出,2=迟到")
    private String subject;

    /** 异常状态（未闭环、已返回） */
    @Column(name = "t.EXCEPTION_STATUS", equalTag = "in")
    @GridColumn(label = "acc_exception_status", width = "100", sortNo = 9 , format = "1=已返回,2=未闭环")
    private String exceptionStatus;

    /** 发送时间 */
    @Column(name = "t.SEND_TIME")
    @GridColumn(label = "acc_exception_sendTime", width = "150", sortNo = 10)
    private Date sendTime;

    /** 状态（是否发送成功） */
    @Column(name = "t.STATUS")
    @GridColumn(label = "acc_exception_sendStatus", width = "100", sortNo = 11, format = "1=发送成功,2=发送失败,3=不需要发送")
    private Short status;

    /** 异常记录错误信息 */
    @Column(name = "t.ERROR_MESSAGE")
    @GridColumn(label = "acc_exception_errorMessage", width = "200", sortNo = 12)
    private String errorMessage;

    /** 推送微信错误信息*/
    @Column(name = "t.PUSH_ERROR_MESSAGE")
    @GridColumn(label = "acc_sendWehcat_errorMessage", width = "200", sortNo = 12)
    private String pushErrorMessage;


    /** 部门编号 */
    @Column(name = "t.DEPT_CODE")
    private String deptCode;

    /** 区域名称 */
    @Column(name = "t.AREA_NAME")
    @GridColumn(label = "base_area_name", width = "100", sortNo = 13,show = false)
    private String areaName;

    /** 读头ID */
    @Column(name = "t.READER_ID")
    private String readerId;

    /** 备注 */
    @Column(name = "t.REMARK")
    private String remark;


    @Column(name = "t.SN")
    private String sn;

    // 查询条件字段

    /** 开始时间 */
    @Condition(value = "t.ENTER_TIME", equalTag = ">=")
    private Date startTime;

    /** 结束时间 */
    @Condition(value = "t.ENTER_TIME", equalTag = "<=")
    private Date endTime;

    /** 发送开始时间 */
    @Condition(value = "t.SEND_TIME", equalTag = ">=")
    private Date sendStartTime;

    /** 发送结束时间 */
    @Condition(value = "t.SEND_TIME", equalTag = "<=")
    private Date sendEndTime;

    /** 姓名模糊查询 */
    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    /** 部门编号范围查询 */
    @Column(name = "t.DEPT_CODE", equalTag = "in")
    private String deptCodeIn;

    /** 区域名称范围查询 */
    @Column(name = "t.AREA_NAME", equalTag = "in")
    private String areaNameIn;

    /** 主题范围查询 */
    @Column(name = "t.SUBJECT", equalTag = "in")
    private String subjectIn;

    /** 异常状态范围查询 */
    @Column(name = "t.EXCEPTION_STATUS", equalTag = "in")
    private String exceptionStatusIn;

    /** 发送状态范围查询 */
    @Column(name = "t.STATUS", equalTag = "in")
    private String statusIn;


    /** 外出时间 */
    @Condition(value = "t.EXIT_TIME",equalTag = "=")
    private Date eqlExitTime;


    /** 工号 */
    @Condition(value = "t.PIN",equalTag = "=")
    private String eqlPin;


    /** 前端页面查询或者导出过滤 */
    @Condition(value = "(t.STATUS is not null and t.status !=3)")
    private Short statusFilter;


    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
        value = "(t.DEPT_CODE IN (select ud.code from auth_department ud where ud.id in(select aud.auth_dept_id from auth_user_dept aud where aud.auth_user_id = ''{0}'')) or t.DEPT_CODE is null or t.DEPT_CODE = '''')")
    private String deptCodeInByUserId;

    /**
     * 根据用户ID查询关联区域及子区域名称
     */
    @Condition(
        value = "t.AREA_NAME IN (select aa.name from auth_area aa where aa.id in(select aua.auth_area_id from auth_user_area aua where aua.auth_user_id = ''{0}''))")
    private String areaNameByUserId;

    /**
     * 默认构造方法
     */
    public AccExceptionRecordItem() {
        super();
    }

    /**
     * @param id
     */
    public AccExceptionRecordItem(String id) {
        this.id = id;
    }
}
