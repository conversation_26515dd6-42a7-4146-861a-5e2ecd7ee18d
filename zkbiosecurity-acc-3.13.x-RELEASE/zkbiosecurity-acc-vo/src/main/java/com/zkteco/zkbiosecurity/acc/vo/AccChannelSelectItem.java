package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AccChannelSelectItem extends BaseItem {

    /** 主键 */
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /** 通道名称 */
    @GridColumn(label = "common_dev_channelName", width = "120", minWidth = "50", sort = "na")
    private String name;

    /** 所属设备名称 */
    @GridColumn(label = "common_ownedDev", width = "120", minWidth = "50", sort = "na")
    private String alias;

    /** 序列号 */
    @GridColumn(label = "common_dev_sn", width = "40", sort = "na")
    private String sn;

    /** 判断是左列表（值为noSelect）还是右列表（值为select） */
    private String type;

    private String selectId;

    private String inId;

    private String notInId;

    private Boolean enabled;

    private String areaIdIn;

    /** 门禁设备序列号 */
    private String deviceSn;

    public AccChannelSelectItem() {
        super();
    }

    public AccChannelSelectItem(String id, String name, String alias, String sn) {
        super();
        this.id = id;
        this.name = name;
        this.alias = alias;
        this.sn = sn;
    }
}
