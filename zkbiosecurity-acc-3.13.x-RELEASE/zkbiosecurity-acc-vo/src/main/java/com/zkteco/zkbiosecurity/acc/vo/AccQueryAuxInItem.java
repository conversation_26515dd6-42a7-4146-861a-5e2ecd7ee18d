package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 辅助输入状态
 *
 * <AUTHOR>
 * @date 2021-06-22 17:26
 * @since 1.0.0
 */
@From(after = "ACC_AUXIN t")
@OrderBy(after = "t.AUX_NO ASC")
@Data
@Accessors(chain = true)
public class AccQueryAuxInItem implements Serializable {

    /** id */
    @Column(name = "t.ID")
    private String id;

    /** 编号 */
    @Column(name = "t.AUX_NO")
    private Short auxNo;

    /** 名称 */
    @Column(name = "t.NAME")
    private String name;
}
