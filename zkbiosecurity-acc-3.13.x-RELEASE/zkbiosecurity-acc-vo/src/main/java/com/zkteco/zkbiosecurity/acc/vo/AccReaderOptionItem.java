/**
 * @author: GenerationTools
 * @date: 2018-03-13 上午10:06 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *
 * 
 * @author: GenerationTools
 * @date: 2018-03-13 上午10:06
 */
@From(after = "ACC_READER_OPTION t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "acc:readerOption:edit", url = "accReaderOption.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "acc:readerOption:del", url = "accReaderOption.do?del",
            label = "common_op_del")})
@Getter
@Setter
@Accessors(chain = true)
public class AccReaderOptionItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name = "t.READER_ID")
    @GridColumn(label = "acc_readerOption_reader")
    private String readerId;

    /**  */
    @Column(name = "t.OPTION_NAME")
    @GridColumn(label = "acc_readerOption_name")
    private String name;

    /**  */
    @Column(name = "t.OPTION_VALUE")
    @GridColumn(label = "acc_readerOption_value")
    private String value;

    /**  */
    @Column(name = "t.OPTION_TYPE")
    @GridColumn(label = "acc_readerOption_type")
    private Short type;

    /** 读头编号 */
    private Short readerNo;

    /**
     * 默认构造方法
     */
    public AccReaderOptionItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccReaderOptionItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccReaderOptionItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     * @param value
     * @param type
     */
    public AccReaderOptionItem(String id, String name, String value, Short type) {
        super();
        this.id = id;
        this.name = name;
        this.value = value;
        this.type = type;
    }
}