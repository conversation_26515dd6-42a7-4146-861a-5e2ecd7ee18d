package com.zkteco.zkbiosecurity.acc.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;

public class AccConstants {

    public final static int BASECMD = 1;// 基础、通信参数
    public final static int FUNCMD = 2;// 功能参数
    public final static int EXFUNCMD = 3;// 功能拓展参数

    // 基础+通信
    public static final String DEV_ACC_BASEOPTION = "FirmVer,~DeviceName,LockCount,ReaderCount,AuxInCount,AuxOutCount,"
        + "MachineType,~IsOnlyRFMachine,~MaxUserCount,~MaxAttLogCount,~MaxFingerCount,~MaxUserFingerCount,MThreshold,NetMask,GATEIPAddress,"
        + "~<PERSON><PERSON><PERSON>Version,SimpleEventType,VerifyStyles,EventTypes,ComPwd,MaxMCUCardBits,NewVFStyles,IPAddress,CommType,VMSSvrUrl,VMSSvrPort";

    // 功能
    public static final String DEV_ACC_FUNOPTION =
        "IclockSvrFun,OverallAntiFunOn,~REXInputFunOn,~CardFormatFunOn,~SupAuthrizeFunOn,"
            + "~ReaderCFGFunOn,~ReaderLinkageFunOn,~RelayStateFunOn,~Ext485ReaderFunOn,~TimeAPBFunOn,~CtlAllRelayFunOn,~LossCardFunOn,"
            + "DisableUserFunOn,DeleteAndFunOn,LogIDFunOn,DateFmtFunOn,DelAllLossCardFunOn,DelayOpenDoorFunOn,UserOpenDoorDelayFunOn,"
            + "MultiCardInterTimeFunOn,DSTFunOn,OutRelaySetFunOn,MachineTZFunOn,AutoServerFunOn,PC485AsInbio485,MasterInbio485,RS232BaudRate,"
            + "AutoServerMode,IPCLinkFunOn,IPCLinkServerIP,MasterControlOn,SubControlOn,AccSupportFunList,MaskDetectionFunOn,IRTempDetectionFunOn";

    // 功能拓展表
    // 新增人脸和指静脉容量参数 modify by qingj.qiu
    public static final String DEV_ACC_EXFUNOPTION = "FingerFunOn,FvFunOn,FaceFunOn,~MaxFace7Count,~MaxFvCount,"
        + "EnalbeIRTempDetection,EnableNormalIRTempPass,EnalbeMaskDetection,EnableWearMaskPass,IRTempThreshold,IRTempUnit,"
        + "EnableUnregisterPass,EnableTriggerAlarm,IRTempCorrection";

    // 设备扩展参数(CFace01新增)
    public static final String DEV_EXTEND_FACEUI_OPTION =
        "VOLUME,EnableShowRegResult,FVInterval,EnableShowIP,EnableShowMacAddress,timeFormat,DtFmt";
    public static final String DEV_EXTEND_FACEOPTION =
        "~FaceMThr,FacePitchThreshold,FaceRollYawThreshold,ModifyClarityCond,FaceMinThreshold,"
            + "IsSupportFaceAntiFake,EnableIRTempImage,AttributeFunOn,EnalbeIRTempDetection,IRTempLowThreshold,IRTempThreshold,"
            + "EnableNormalIRTempPass,EnalbeMaskDetection,EnableWearMaskPass,IRTempUnit,EnableUnregisterPass,EnableTriggerAlarm,IRTempCorrection";
    public static final String DEV_EXTEND_ACCOPTION = "ChannelMode,IsSupportQRcode,QRCodeEnable";
    //public static final String DEV_EXTEND_VIDEOINTERCOMOPTION = "VisualIntercomFunOn,VideoProtocol,VMSSvrUrl,VMSSvrPort";

    // 门禁授权编码
    public static final String LICENSE_ACCADVANCED = "advancdacc";

    // 权限组切分常量
    public static final int LEVEL_SPLIT_COUNT = 500;

    // 从pull设备获取人员或事件时，设定的包处理时间
    public static final int PULL_WAIT_TIME = 1500000;
    // 从push设备获取人员或事件时，设定一个包的处理时间
    public static final int PUSH_WAIT_TIME = 100000;

    // 操作类型--设备中操作
    public static final Short DEV_TYPE_ZERO = 0;// 获取人员信息
    public static final Short DEV_TYPE_FIRST = 1;// 获取指纹信息
    public static final Short DEV_TYPE_SECOND = 2;// 获取面部信息
    public static final Short DEV_TYPE_THIRD = 3;// 获取指静脉信息
    /** 设备属性表 */
    public static final String DEV_PROPERTY = "DevProperty";
    /** 设备参数表 */
    public static final String DEV_PARAMETERS = "DevParameters";
    /** 门属性表 */
    public static final String DOOR_PROPERTY = "DoorProperty";
    public static final String DELETE_DOOR = "[Door]";
    /** 门参数表 */
    public static final String DOOR_PARAMETERS = "DoorParameters";
    /** 读头属性表 */
    public static final String READER_PROPERTY = "ReaderProperty";
    /** 读头属性表 */
    public static final String READER_PARAMETERS = "ReaderParameters";
    /** 韦根格式表 */
    public static final String WG_FORMAT = "WGFormat";
    /** 默认韦根格式表 */
    public static final String DEFAULT_WG_FORMAT = "DefaultWGFormat";
    /** 读头韦根格式表 */
    public static final String READER_WG_FORMAT = "ReaderWGFormat";
    /** 辅助输出属性表 */
    public static final String AUX_OUT_PROPERTY = "AuxOut";
    /** 辅助输入属性表 */
    public static final String AUX_IN_PROPERTY = "AuxIn";
    /** 从机管理员表 */
    public static final String DOWN_SLAVE = "DownSlave";
    /** 一人多卡表 */
    public static final String MUL_CARD_USER = "mulcarduser";
    /** 输入时间段对应表 */
    public static final String INPUT_IO_SETTING = "InputIOSetting";
    /** 时间段-验证方式关联表 */
    public static final String DIFF_TIMEZONE_VS = "DiffTimezoneVS";
    /** 门-时间段关联表 */
    public static final String DOOR_VS_TIMEZONE = "DoorVSTimezone";
    /** 人-时间段关联表 */
    public static final String USER_VS_TIMEZONE = "PersonalVSTimezone";
    /** 虚拟WIFI列表 */
    public static final String WIFI_LIST = "APList";
    /** 虚拟待授权子设备 */
    public static final String DEVICE_AUTHORIZE = "DeviceAuthorize";
    /** 身份证信息表 */
    public static final String IDENTITY_CARD_INFO = "identitycard";

    /** 将设备信息保存到redis的key，形如PUSHDEVINFO_sn; */
    public static final String PUSH_DEV_INFO = "PUSH_DEV_INFO_";
    /** 韦根测试数据Redis的key WG_TEST_DATA + sn */
    public static final String WG_TEST_DATA = "__opWgTestData_";
    /** 切换网络测速连接的数据 */
    public static final String TEST_SIGNAL = "__opTestSignalData_";
    /** 搜索WIFI List KEY */
    public static final String SEARCH_WIFI_LIST = "SEARCH_WIFI_LIST_";
    /** 虚拟待授权子设备 KEY */
    public static final String DEVICE_AUTHORIZE_KEY = "Device_Authorize_";
    /** 身份登记模式下上传 KEY + cmdID */
    public static final String IDENTITY_CARD_INFO_KEY = "Identity_Card_Info_";
    /** 未授权 */
    public static final short DEVICE_UNAUTHORIZE = 0;
    /** 授权中 */
    public static final short DEVICE_AUTHORIZING = 1;
    /** 已授权 */
    public static final short DEVICE_AUTHORIZED = 2;

    // 各种机型常量 machineType
    public static final short DEVICE_C3_100 = 4;
    public static final short DEVICE_C3_200 = 1;
    public static final short DEVICE_C3_400 = 2;
    public static final short DEVICE_C3_400_TO_200 = 7;
    public static final short DEVICE_C4_200 = 5;// inBIO280
    public static final short DEVICE_C4_400 = 3;
    public static final short DEVICE_C4_400_TO_200 = 6;
    public static final short DEVICE_C3_160 = 8;// inBIO160
    public static final short DEVICE_C3_260 = 9;// inBIO260
    public static final short DEVICE_C3_460 = 10;// inBIO460
    // public static final short DEVICE_ELEVATOR_MAIN = 11;//梯控主板的类型为11。设备类型值为5
    public static final short DEVICE_C5_100 = 23;// C5
    public static final short DEVICE_C5_200 = 24;
    public static final short DEVICE_C5_400 = 25;
    public static final short DEVICE_INBIO5_100 = 26;// Inbio5
    public static final short DEVICE_INBIO5_200 = 27;
    public static final short DEVICE_INBIO5_400 = 28;
    public static final short DEVICE_C2 = 29;
    public static final short DEVICE_BIOIR_9000 = 40;
    public static final short DEVICE_ACCESS_CONTROL = 101; // 一体机
    public static final short DEVICE_ACCESS_VEIN = 102; // 指静脉
    public static final short DEVICE_ACCESS_FACE = 103; // iface7
    public static final short DEVICE_BIOCV_460 = 2000; // BioCV460
    public static final short DEVICE_WIRELESS_LOCK = 301; // 无线锁

    public static final List<Short> DEVICE_C3_MACHINE_TYPE = new ArrayList<>();
    static {
        DEVICE_C3_MACHINE_TYPE.add(DEVICE_C3_100);
        DEVICE_C3_MACHINE_TYPE.add(DEVICE_C3_200);
        DEVICE_C3_MACHINE_TYPE.add(DEVICE_C3_400);
        DEVICE_C3_MACHINE_TYPE.add(DEVICE_C3_400_TO_200);
    }
    public static final List<Short> DEVICE_INBIO_MACHINE_TYPE = new ArrayList<>();// 暂时不用
    static {
        DEVICE_INBIO_MACHINE_TYPE.add(DEVICE_C3_160);
        DEVICE_INBIO_MACHINE_TYPE.add(DEVICE_C3_260);
        DEVICE_INBIO_MACHINE_TYPE.add(DEVICE_C3_460);
        DEVICE_INBIO_MACHINE_TYPE.add(DEVICE_C4_200);
        DEVICE_INBIO_MACHINE_TYPE.add(DEVICE_C4_400);
        DEVICE_INBIO_MACHINE_TYPE.add(DEVICE_C4_400_TO_200);
    }
    // 一体机
    public static final List<Short> DEVICE_ACCESS_MACHINE_TYPE = new ArrayList<>();// 支持拆机报警事件等功能的机器
    static {
        DEVICE_ACCESS_MACHINE_TYPE.add(DEVICE_ACCESS_CONTROL);
        DEVICE_ACCESS_MACHINE_TYPE.add(DEVICE_ACCESS_VEIN);
        DEVICE_ACCESS_MACHINE_TYPE.add(DEVICE_ACCESS_FACE);
    }
    // C5
    public static final List<Short> DEVICE_C5_MACHINE_TYPE = new ArrayList<>();
    static {
        DEVICE_C5_MACHINE_TYPE.add(DEVICE_C5_100);
        DEVICE_C5_MACHINE_TYPE.add(DEVICE_C5_200);
        DEVICE_C5_MACHINE_TYPE.add(DEVICE_C5_400);
    }
    // INBIO5
    public static final List<Short> DEVICE_INBIO5_MACHINE_TYPE = new ArrayList<>();
    static {
        DEVICE_INBIO5_MACHINE_TYPE.add(DEVICE_INBIO5_100);
        DEVICE_INBIO5_MACHINE_TYPE.add(DEVICE_INBIO5_200);
        DEVICE_INBIO5_MACHINE_TYPE.add(DEVICE_INBIO5_400);
    }

    // 通信方式-add by darcy20150115
    public static final short COMM_TYPE_PULL_TCPIP = 1;
    public static final short COMM_TYPE_PULL_RS485 = 2;
    public static final short COMM_TYPE_PUSH_HTTP = 3;
    public static final short COMM_TYPE_BEST_MQTT = 4;
    public static final short COMM_TYPE_BEST_WS = 5;

    // 互锁
    public static final short TRIGGER_TYPE_INTERLOCK = 0;
    // 反潜
    public static final short TRIGGER_TYPE_ANTIPASSBACK = 1;

    // 读头类型
    /** 无,不使用 */
    public static final short READER_TYPE_NOMINAL = 0;
    /** RS485 */
    public static final short READER_TYPE_RS485 = 1;
    /** 韦根 */
    public static final short READER_TYPE_WGFMT = 2;
    /** RS485或韦根 */
    public static final short READER_TYPE_RS485_OR_WGFMT = 3;
    /** 网络读头TCPIP */
    public static final short READER_TYPE_NETWORK = 4;
    /** Zigbee */
    public static final short READER_TYPE_ZIGBEE = 8;
    /** 网络读头TCPIP 默认端口 */
    public static final short READER_DEFAULT_PORT = 4376;

    /** 普通模式 */
    public static final byte READER_READMODE_NORMAL = 0;
    /** 身份证通行模式 */
    public static final byte READER_READMODE_IDCARD_PASS = 1;
    /** 仅身份证模式,预留使用，暂未用到 */
    public static final byte READER_READMODE_ONLY_IDCARD = 2;

    // 设备板子类型 AC(Access Control) Panel
    // 主要用于表单新增设备时，特别是设备连接不成功时添加设备。如果为push模式下自动添加设备，该值直接通过设备传上来的machineType值来判断是哪种类型。
    public static final Short ACPANEL_1_DOOR = 1;// 单门控制器
    public static final Short ACPANEL_2_DOOR = 2;// 两门控制器
    public static final Short ACPANEL_4_DOOR = 4;// 四门控制器
    public static final Short ACCESS_CONTROL_DEVICE = 5;// 一体机，单门

    // 功能是否支持
    public static final Integer FUNCTION_UNSUPPORT = -1;// -1为不支持。用于数据库查询等。

    // 门的出入状态
    public static final Short STATE_IN = 0;// 入
    public static final Short STATE_OUT = 1;// 出
    public static final Short STATE_NO = 2;// 无
    public static final Short DOOR_STATE_CLOSE = 0;// 关
    public static final Short DOOR_STATE_OPEN = 1;// 开

    // 门磁状态
    public static final Short DOOR_SENSOR_NONE = 0;// 无
    public static final Short DOOR_SENSOR_CLOSE = 1;// 关
    public static final Short DOOR_SENSOR_OPEN = 2;// 开

    public static final Short ENABLE = 1;// 启用/支持
    public static final Short DISABLE = 0;// 禁用/不支持

    public static final String OFFLINE_RULE_STANDARDLEVEL_STRING = "0";// 标准同行权限
    public static final String OFFLINE_RULE_ACCESSDENIED = "1";// 拒绝用户通行

    public static final Short DEV_NOT_SUPPORT_FEATURE = 1;// 设备不支持当前操作

    public static final String WIEGAND_AUTO = "1";// 自动匹配
    public static final String WIEGAND_USER_DEFINED = "2";// 自定义韦根格式

    public static final String ONLY_RF_MACHINE = "~IsOnlyRFMachine";// 仅卡设备参数
    public static final String DEL_BY_AND = "DeleteAndFunOn";// ”且“ 操作参数

    public static final Integer EVENT_LINKCONTROL = 6;// 联动事件
    public static final Integer CANCEL_ALARM = 7;// 取消报警
    public static final Integer OPEN_DOOR = 8;// 远程开门
    public static final Integer CLOSE_DOOR = 9;// 远程关门
    public static final Integer DISABLE_NORMAL_OPEN = 10;// 禁用常开
    public static final Integer ENABLE_NORMAL_OPEN = 11;// 启用常开
    public static final Integer EVENT_DISCONNECT = 700;// 连接断开
    public static final Integer EVENT_NORMAL_OPEN = 205;// 远程开门常开
    public static final Integer EVENT_DEVSTART = 206;// 设备启动
    public static final Integer EVENT_CONNECT = 214;// 连上服务器
    public static final Integer EVENT_IDENTITY_CARD_PASS = 218;// 身份证通行
    public static final Integer EVENT_CONNECTFAILURE = 105;// 无法连接服务器

    public static final String FVTEMPLATE = "fvtemplate";// 指静脉数据
    public static final String FP_VERSION_10 = "10";// 指纹版本10
    public static final String FP_VERSION_12 = "12";// 指纹版本12
    public static final String ATTPHOTO = "ATTPHOTO";// 一体机拍照数据
    public static final String BIOPHOTO = "biophoto";// 比对照片数据
    public static final String BIODATA = "biodata";// 一体化模板数据
    public static final String FINGER_FUN_ON = "FingerFunOn";// 支持指纹参数
    public static final String FV_FUN_ON = "FvFunOn";// 支持指静脉参数
    public static final String FACE_FUN_ON = "FaceFunOn";// 支持面部参数
    public static final String BIOPHOTO_FUN_ON = "BioPhotoFun";// 支持比对照片参数
    public static final String PHOTO_FUN_ON = "PhotoFunOn";// 支持用户照片参数
    public static final String PV_FUN_ON = "PvFunOn";// 支持掌纹参数
    public static final String LIVE_PV_FUN_ON = "LivePvFunOn";// 支持掌纹比对照片参数
    public static final String IRIS_FUN_ON = "IrisFunOn";// 支持虹膜参数

    public static final List<Integer> DEVICE_EVENT = new ArrayList<>();
    static {
        DEVICE_EVENT.add(100);// 防拆报警
        DEVICE_EVENT.add(106);// 市电掉电
        DEVICE_EVENT.add(107);// 电池掉电
        DEVICE_EVENT.add(108);// 无法连接主控
        DEVICE_EVENT.add(217);// 成功连接主控
        DEVICE_EVENT.add(4008);// 市电恢复
        DEVICE_EVENT.add(6005);// 记录容量达90%
        DEVICE_EVENT.add(6015);// 复位扩展设备电源
        DEVICE_EVENT.add(6016);// 恢复本机出厂设置
    }

    // 辅助输出事件
    public static final List<Integer> AUXOUT_EVENT = new ArrayList<>();
    static {
        AUXOUT_EVENT.add(12);// 开启辅助输出
        AUXOUT_EVENT.add(13);// 关闭辅助输出
        AUXOUT_EVENT.add(229);// 辅助输出定时常开
        AUXOUT_EVENT.add(230);// 辅助输出定时关闭常开
    }

    // 辅助输入事件
    public static final List<Integer> AUXIN_EVENT = new ArrayList<>();
    static {
        AUXIN_EVENT.add(65);// 辅助输入不在时间段内操作
        AUXIN_EVENT.add(220);// 输入点断开
        AUXIN_EVENT.add(221);// 输入点短路
        AUXIN_EVENT.add(225);// 输入点恢复正常
        AUXIN_EVENT.add(226);// 输入点报警
        AUXIN_EVENT.add(114);// 线路检测，火警输入断开
        AUXIN_EVENT.add(115);// 线路检测，火警输入短路
        AUXIN_EVENT.add(116);// 线路检测，辅助输入断开
        AUXIN_EVENT.add(117);// 线路检测，辅助输入短路
        AUXIN_EVENT.add(243);// 火警输入断开
        AUXIN_EVENT.add(244);// 火警输入短路
    }

    /** 扩展板事件 */
    public static final List<Integer> EXTBOARD_EVENT = new ArrayList<>();
    static {
        // 扩展板离线
        EXTBOARD_EVENT.add(112);
        // 扩展板在线
        EXTBOARD_EVENT.add(247);
    }

    /** 读头事件 */
    public static final List<Integer> READER_EVENT = new ArrayList<>();
    static {
        // 读头升级失败
        READER_EVENT.add(66);
        // 读头防拆报警
        READER_EVENT.add(109);
        // 读头离线
        READER_EVENT.add(110);
        // 读头升级成功
        READER_EVENT.add(235);
        // 读头防拆报警解除
        READER_EVENT.add(236);
        // 读头在线
        READER_EVENT.add(237);
    }

    public static final Integer START_FIRE_DOOR = 210;// 启动消防开门事件，硬件联动事件
    public static final Integer VERIFY_MODE_OTHERS = 200;// 验证方式“其他”数值

    public static final int ACTION_NORMAL_OPEN = 255;// 常开
    public static final int ACTION_CLOSE = 0;// 关闭
    public static final int ACTION_OPEN = 1;// 打开
    public static final int ACTION_LOCK = 2;// 锁定
    public static final int ACTION_UNLOCK = 3;// 解锁

    // 事件点类型
    public static final Integer EVENT_POINT_TYPE_NONE = -1;// 无
    public static final Integer EVENT_POINT_TYPE_DOOR = 0;// 门事件
    public static final Integer EVENT_POINT_TYPE_AUX_IN = 1;// 辅助输入点事件
    public static final Integer EVENT_POINT_TYPE_AUX_OUT = 2;// 辅助输出点事件
    public static final Integer EVENT_POINT_TYPE_DEVICE = 3;// 设备事件
    public static final Integer EVENT_POINT_TYPE_EXTBOARD = 4;// 扩展板事件

    public static final Integer FILTER_PERSON_IDS = -1;// 选人控件需要过滤的人员id，没有人员时使用
    public static final int SUCCESS = 0;// 操作成功
    public static final int ERROR = -1;// 操作失败
    public static final int DISABLE_OPTION = 0;// 不支持这个参数带的功能
    public static final int MAX_CMD_COUNT = 2000;// 命令最大允许条数:2000， 但是数据库中存储的内容最大为65535，
    public static final int MAX_TEMPLATE_CMD_COUNT = 200;// 指纹命令允许条数：200
    public static final int ADD_BATCH_COUNT = 800;// 批量插入的条数
    public static final int MAX_CMD_PACKAGE_SIZE = 40;// 最大包40K

    public static final short MAIL = 1;// 邮件服务
    public static final short SMS = 2; // 短信服务
    public static final short AUDIO = 3; // 播放音频文件
    public static final short DIGIFORT = 4; // digifort
    public static final short LINE = 5; // LINE 通讯录
    public static final short WHATSAPP = 6; // whatsapp
    public static final short MUSTERPOINT = 7; // 紧急疏散点

    public static final short VDB_IVR = 8; // 可视对讲 IVR

    public static final short VDB = 9; // 可视对讲 分机绑定的对象
    public static final short POP_UP_VIDEO = 1;// 弹出视频
    public static final short RECORD = 2;// 录像
    public static final short CAPTURE = 3;// 抓拍
    public static final short VID_VIDEO = 1;// 录像，视频模块复制过来的值
    public static final short VID_CAPTURE = 2;// 抓拍，视频模块复制过来的值
    /** 布防 */
    public static final short IAS_ARM = 0;
    /** 撤防 */
    public static final short IAS_DISARM = 1;

    // 设备参数
    public static final String DEV_MULTICARD_TIME = "MultiCardInterTimeFunOn";// 设备是否支持多人刷卡时间间隔设置

    // 读头入、出 按配置文件初始化
    public static final String READER_IN_NAME = "common_in";
    public static final String READER_OUT_NAME = "common_out";
    //
    // //添加辅助输入、输出
    public static final String AUX_IN_NAME = "common_leftMenu_auxIn";// Auxiliary Input
    public static final String AUX_OUT_NAME = "acc_leftMenu_auxOut";// Auxiliary Output

    // 事件类型级别（数据库里对应值）
    public final static short EVENT_NORMAL = 0;// 正常
    public final static short EVENT_WARNING = 1; // 异常
    public final static short EVENT_ALARM = 2; // 报警

    // 定义新增验证方式，旧代码常量定义在core里，不合理，这些都是业务常量，只需在AccConstUtil定义即可；add by max 20150703
    public static final int VERIFY_MODE_ONLYFACE = 15;// 人脸
    public static final int VERIFY_MODE_FACEFP = 16;// 人脸加指纹
    public static final int VERIFY_MODE_FACEPWD = 17;// 人脸加密码
    public static final int VERIFY_MODE_FACECARD = 18;// 人脸加卡
    public static final int VERIFY_MODE_FACEFPCARD = 19;// 人脸加指纹加卡
    public static final int VERIFY_MODE_FACEFPPWD = 20;// 人脸加指纹加密码
    public static final int VERIFY_MODE_FV = 21;// 指静脉
    public static final int VERIFY_MODE_FVPWD = 22;// 指静脉加密码
    public static final int VERIFY_MODE_FVCARD = 23;// 指静脉加卡
    public static final int VERIFY_MODE_FVPWDCARD = 24;// 指静脉加密码加卡
    public static final int VERIFY_MODE_PV = 25;// 掌静脉
    public static final int VERIFY_MODE_PVCARD = 26;// 掌静脉加卡
    public static final int VERIFY_MODE_PVFACE = 27;// 掌静脉加人脸
    public static final int VERIFY_MODE_PVFP = 28;// 掌静脉加指纹
    public static final int VERIFY_MODE_PVFACEFP = 29;// 掌静脉加人脸加指纹
    public static final String VERIFY_MODE_UNDEFINED = "common_verifyMode_undefined";
    // 验证方式--新（2015.1.28，指的是有verifyStyle参数的设备）
    public static final Map<Integer, String> VERIFY_MODE = new HashMap<>();
    static {
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_CARDORFPORPWD, "common_verifyMode_cardOrFpOrPwd");// 卡或指纹或密码
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_ONLYFP, "common_verifyMode_onlyFp");// 仅指纹
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_ONLYPIN, "common_verifyMode_onlyPin");// 仅工号验证
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_ONLYPWD, "common_verifyMode_onlyPwd");// 仅密码
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_ONLYCARD, "common_verifyMode_onlyCard");// 仅卡
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_FPORPWD, "common_verifyMode_pwdOrFp");// 指纹或密码
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_CARDORFP, "common_verifyMode_cardOrFp");// 卡或指纹
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_CARDORPWD, "common_verifyMode_cardOrPwd");// 卡或密码
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_PINANDFP, "common_verifyMode_pinAndFp");// 工号加指纹
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_FPANDPWD, "common_verifyMode_fpAndPwd");// 指纹加密码
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_CARDANDFP, "common_verifyMode_cardAndFp");// 卡加指纹
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_CARDANDPWD, "common_verifyMode_cardAndPwd");// 卡加密码
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_FPANDCARDANDPWD, "common_verifyMode_cardAndFpAndPwd");// 指纹加密码加卡
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_PINANDFPANDPWD, "common_verifyMode_pinAndFpAndPwd");// 工号加指纹加密码
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_PINANDFPORCARDANDFP, "common_verifyMode_pinAndFpOrCardAndFp");// 工号加指纹 或
                                                                                                            // 卡加指纹
        VERIFY_MODE.put(VERIFY_MODE_ONLYFACE, "acc_verify_mode_onlyface");// 人脸
        VERIFY_MODE.put(VERIFY_MODE_FACEFP, "acc_verify_mode_facefp");// 人脸加指纹
        VERIFY_MODE.put(VERIFY_MODE_FACEPWD, "acc_verify_mode_facepwd");// 人脸加密码
        VERIFY_MODE.put(VERIFY_MODE_FACECARD, "acc_verify_mode_facecard");// 人脸加卡
        VERIFY_MODE.put(VERIFY_MODE_FACEFPCARD, "acc_verify_mode_facefpcard");// 人脸加指纹加卡
        VERIFY_MODE.put(VERIFY_MODE_FACEFPPWD, "acc_verify_mode_facefppwd");// 人脸加指纹加密码
        VERIFY_MODE.put(VERIFY_MODE_FV, "acc_verify_mode_fv");// 指静脉
        VERIFY_MODE.put(VERIFY_MODE_FVPWD, "acc_verify_mode_fvpwd");// 指静脉加密码
        VERIFY_MODE.put(VERIFY_MODE_FVCARD, "acc_verify_mode_fvcard");// 指静脉加卡
        VERIFY_MODE.put(VERIFY_MODE_FVPWDCARD, "acc_verify_mode_fvpwdcard");// 指静脉加密码加卡
        VERIFY_MODE.put(VERIFY_MODE_PV, "acc_verify_mode_pv");// 掌静脉
        VERIFY_MODE.put(VERIFY_MODE_PVCARD, "acc_verify_mode_pvcard");// 掌静脉加卡
        VERIFY_MODE.put(VERIFY_MODE_PVFACE, "acc_verify_mode_pvface");// 掌静脉加人脸
        VERIFY_MODE.put(VERIFY_MODE_PVFP, "acc_verify_mode_pvfp");// 掌静脉加指纹
        VERIFY_MODE.put(VERIFY_MODE_PVFACEFP, "acc_verify_mode_pvfacefp");// 掌静脉加人脸加指纹
        VERIFY_MODE.put(ConstUtil.VERIFY_MODE_OTHER, "common_verifyMode_other");// 其他
    }

    /** L3需要过滤含有独立密码的验证方式 */
    public static final Map<Integer, String> VERIFY_MODE_FILTER_BYL3 = new HashMap<>();
    static {
        // 仅密码
        VERIFY_MODE_FILTER_BYL3.put(ConstUtil.VERIFY_MODE_ONLYPWD, "common_verifyMode_onlyPwd");
        // 指纹或密码
        VERIFY_MODE_FILTER_BYL3.put(ConstUtil.VERIFY_MODE_FPORPWD, "common_verifyMode_pwdOrFp");
        // 卡或密码
        VERIFY_MODE_FILTER_BYL3.put(ConstUtil.VERIFY_MODE_CARDORPWD, "common_verifyMode_cardOrPwd");
    }

    /** 旧验证方式 */
    public static final int NOT_NEW_VERIFY_MODE = 0;
    /** 新验证方式 */
    public static final int IS_NEW_VERIFY_MODE = 1;
    /** 逻辑位 */
    public static final int NEW_VERIFY_MODE_LOGIC = 0;
    // 人脸
    public static final int NEW_VERIFY_MODE_FACE = 1;
    // 掌纹
    public static final int NEW_VERIFY_MODE_PALM_PRINT = 2;
    // 掌静脉
    public static final int NEW_VERIFY_MODE_PALM_VEIN = 3;
    // 指纹
    public static final int NEW_VERIFY_MODE_FP = 4;
    // 指静脉
    public static final int NEW_VERIFY_MODE_VEIN = 5;
    // 声纹
    public static final int NEW_VERIFY_MODE_VP = 6;
    // 虹膜
    public static final int NEW_VERIFY_MODE_IRIS = 7;
    // 视网膜
    public static final int NEW_VERIFY_MODE_RETINA = 8;
    // 密码
    public static final int NEW_VERIFY_MODE_PW = 9;
    // 工号
    public static final int NEW_VERIFY_MODE_PIN = 10;
    // 卡
    public static final int NEW_VERIFY_MODE_RF = 11;
    // 身份证
    public static final int NEW_VERIFY_MODE_IDNUM = 12;
    /** 新验证方式，有NewVFStyles参数的设备 */
    public static final Map<Integer, String> NEW_VERIFY_MODE = new HashMap<>();
    static {
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_LOGIC, "common_newVerify_mode_logic");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_FACE, "common_newVerify_mode_face");
        // 2-掌纹、3-掌静脉,统一为手掌
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_PALM_PRINT, "common_newVerify_mode_pv");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_PALM_VEIN, "common_newVerify_mode_pv");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_FP, "common_newVerify_mode_fp");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_VEIN, "common_newVerify_mode_fv");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_VP, "common_newVerify_mode_voice");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_IRIS, "common_newVerify_mode_iris");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_RETINA, "common_newVerify_mode_retina");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_PW, "common_newVerify_mode_pw");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_PIN, "common_newVerify_mode_pin");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_RF, "common_newVerify_mode_rf");
        NEW_VERIFY_MODE.put(NEW_VERIFY_MODE_IDNUM, "common_newVerify_mode_idcard");
    }

    // 旧设备验证方式， 按5.2的验证方式判断--卡相关（比如C3）
    public static final List<Integer> OLD_VERIFY_MODE = new ArrayList<Integer>();
    static {
        OLD_VERIFY_MODE.add(ConstUtil.VERIFY_MODE_ONLYPWD);// 仅密码
        OLD_VERIFY_MODE.add(ConstUtil.VERIFY_MODE_ONLYCARD);// 仅卡
        OLD_VERIFY_MODE.add(ConstUtil.VERIFY_MODE_CARDORPWD);// 卡或密码
        OLD_VERIFY_MODE.add(ConstUtil.VERIFY_MODE_CARDANDPWD);// 卡加密码
    }

    // 旧设备指纹验证方式（2015.1.28时的生产固件，inbioX60，inbioX80）
    public static final List<Integer> OLD_FP_VERIFY_MODE_ACP = new ArrayList<Integer>(OLD_VERIFY_MODE);
    static {
        OLD_FP_VERIFY_MODE_ACP.add(ConstUtil.VERIFY_MODE_ONLYFP);// 仅指纹
        OLD_FP_VERIFY_MODE_ACP.add(ConstUtil.VERIFY_MODE_CARDORFP);// 卡或指纹
        OLD_FP_VERIFY_MODE_ACP.add(ConstUtil.VERIFY_MODE_CARDANDFP);// 卡加指纹
    }
    // 旧设备指纹验证方式--一体机老pull，比如TF1700
    public static final List<Integer> OLD_FP_VERIFY_MODE_ACD = new ArrayList<Integer>(OLD_VERIFY_MODE);
    static {
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_CARDORFPORPWD);// 卡或指纹或密码
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_ONLYFP);// 仅指纹
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_ONLYPIN);// 仅工号验证
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_FPORPWD);// 指纹或密码
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_CARDORFP);// 卡或指纹
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_PINANDFP);// 工号加指纹
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_FPANDPWD);// 指纹加密码
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_CARDANDFP);// 卡加指纹
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_FPANDCARDANDPWD);// 指纹加密码加卡
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_PINANDFPANDPWD);// 工号加指纹加密码
        OLD_FP_VERIFY_MODE_ACD.add(ConstUtil.VERIFY_MODE_PINANDFPORCARDANDFP);// 工号加指纹 或 卡加指纹
    }
    //
    // 硬件联动触发条件需要过滤的事件
    public static final List<Integer> LINKAGE_TRIGGER_FILTER = new ArrayList<>();
    static {
        // LINKAGE_TRIGGER_FILTER.add(1);// 常开时间段内刷卡
        LINKAGE_TRIGGER_FILTER.add(6);// 触发联动事件
        // LINKAGE_TRIGGER_FILTER.add(7);// 取消报警
        LINKAGE_TRIGGER_FILTER.add(8);// 远程开门
        LINKAGE_TRIGGER_FILTER.add(9);// 远程关门
        LINKAGE_TRIGGER_FILTER.add(10);// 禁用当天常开时间段
        LINKAGE_TRIGGER_FILTER.add(11);// 启用当天常开时间段
        LINKAGE_TRIGGER_FILTER.add(12);// 开启辅助输出
        LINKAGE_TRIGGER_FILTER.add(13);// 关闭辅助输出
        // LINKAGE_TRIGGER_FILTER.add(16);// 常开时间段内按指纹
        LINKAGE_TRIGGER_FILTER.add(26);// 多人验证（刷卡），多人验证等待
        LINKAGE_TRIGGER_FILTER.add(32);// 多人验证（按指纹）
        LINKAGE_TRIGGER_FILTER.add(44);// 后台验证失败
        LINKAGE_TRIGGER_FILTER.add(45);// 后台验证超时
        LINKAGE_TRIGGER_FILTER.add(47);// 发送命令失败
        LINKAGE_TRIGGER_FILTER.add(51);// 多人验证（密码）
        LINKAGE_TRIGGER_FILTER.add(105);// 无法连接服务器
        LINKAGE_TRIGGER_FILTER.add(108);// 无法连接主控
        LINKAGE_TRIGGER_FILTER.add(205);// 远程开门常开
        LINKAGE_TRIGGER_FILTER.add(206);// 设备启动
        LINKAGE_TRIGGER_FILTER.add(214);// 恢复连接
        LINKAGE_TRIGGER_FILTER.add(217);// 成功连接主控
        LINKAGE_TRIGGER_FILTER.add(218);// 身份证通行
        LINKAGE_TRIGGER_FILTER.add(222);// 后台验证成功
        LINKAGE_TRIGGER_FILTER.add(223);// 后台验证
        LINKAGE_TRIGGER_FILTER.add(229);// 辅助输出定时常开
        LINKAGE_TRIGGER_FILTER.add(230);// 辅助输出定时关闭常开
        LINKAGE_TRIGGER_FILTER.add(233);// 远程锁定
        LINKAGE_TRIGGER_FILTER.add(234);// 远程解锁
        LINKAGE_TRIGGER_FILTER.add(700);// 设备连接断开
        LINKAGE_TRIGGER_FILTER.add(300);// 全局联动
        LINKAGE_TRIGGER_FILTER.add(500);// 全局反潜(逻辑)
        LINKAGE_TRIGGER_FILTER.add(501);// 人员有效性(有效日期)
        LINKAGE_TRIGGER_FILTER.add(502);// 人数控制
        LINKAGE_TRIGGER_FILTER.add(503);// 全局互锁
        LINKAGE_TRIGGER_FILTER.add(505);// 全局反潜(定时)
        LINKAGE_TRIGGER_FILTER.add(506);// 全局反潜(定时逻辑)
        LINKAGE_TRIGGER_FILTER.add(507);// 人员有效性(第一次使用后有效天数)
        LINKAGE_TRIGGER_FILTER.add(508);// 人员有效性(使用次数)
        LINKAGE_TRIGGER_FILTER.add(509);// 后台验证失败(人未登记)
        LINKAGE_TRIGGER_FILTER.add(510);// 后台验证失败(数据异常)
        LINKAGE_TRIGGER_FILTER.add(6005);// 记录达到90%
    }

    // 全局联动触发条件需要过滤的事件
    public static final List<Integer> GLOBAL_LINKAGE_TRIGGER_FILTER = new ArrayList<Integer>(LINKAGE_TRIGGER_FILTER);
    static {
        GLOBAL_LINKAGE_TRIGGER_FILTER.add(8);// 远程开门
        GLOBAL_LINKAGE_TRIGGER_FILTER.add(9);// 远程关门
        GLOBAL_LINKAGE_TRIGGER_FILTER.add(205);// 远程常开
        GLOBAL_LINKAGE_TRIGGER_FILTER.add(5);// 常开时间段内开门
        GLOBAL_LINKAGE_TRIGGER_FILTER.add(233);// 远程锁定
        GLOBAL_LINKAGE_TRIGGER_FILTER.add(234);// 远程解锁
        GLOBAL_LINKAGE_TRIGGER_FILTER.add(63);// 门已锁定
        // GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(223));//全局联动需要支持支持后台验证
        GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(500));// 全局联动需要支持全局反潜(逻辑)
        GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(503));// 全局联动需要支持全局互锁
        GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(505));// 全局联动需要支持全局反潜(定时)
        GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(506));// 全局联动需要支持全局反潜(定时逻辑)
        GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(222));// 全局联动需要支持后台验证成功
        GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(501));// 全局联动需要支持人员有效性(有效日期)
        GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(507));// 全局联动需要支持人员有效性(第一次使用后有效天数)
        GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(508));// 全局联动需要支持人员有效性(使用次数)
        GLOBAL_LINKAGE_TRIGGER_FILTER.remove(new Integer(502));// 全局联动需要支持人数控制
    }

    // 联动中，只有门触发的事件
    public static final List<Integer> LINKAGE_DOOR_EVENT = new ArrayList<Integer>();
    static {
        LINKAGE_DOOR_EVENT.add(5);// 常开时间段内开门
        LINKAGE_DOOR_EVENT.add(7);// 取消报警
        LINKAGE_DOOR_EVENT.add(8);// 远程开门
        LINKAGE_DOOR_EVENT.add(9);// 远程关门
        LINKAGE_DOOR_EVENT.add(28);// 门开超时
        LINKAGE_DOOR_EVENT.add(36);// 门非有效时间段（按出门按钮）
        LINKAGE_DOOR_EVENT.add(37);// 常开时间段无法关门
        LINKAGE_DOOR_EVENT.add(38);// 卡已挂失
        LINKAGE_DOOR_EVENT.add(63);// 门已锁定
        LINKAGE_DOOR_EVENT.add(64);// 出门按钮不在时间段内操作
        LINKAGE_DOOR_EVENT.add(102);// 门被意外打开
        LINKAGE_DOOR_EVENT.add(200);// 门已打开
        LINKAGE_DOOR_EVENT.add(201);// 门已关闭
        LINKAGE_DOOR_EVENT.add(202);// 出门按钮开门
        LINKAGE_DOOR_EVENT.add(204);// 常开时间段结束
        LINKAGE_DOOR_EVENT.add(205);// 远程开门常开
        LINKAGE_DOOR_EVENT.add(209);// 触发出门按钮（被锁定）
        LINKAGE_DOOR_EVENT.add(233);// 远程锁定
        LINKAGE_DOOR_EVENT.add(234);// 远程解锁
    }

    public static final int LINKAGE_DOOR = 0;// 门事件
    public static final int LINKAGE_AUXIN = 1;// 辅助输入事件
    public static final int LINKAGE_DEVICE = 2;// 设备事件
    public static final String LINKAGE_ANY = "0";// 任意输入点
    public static final String LINKAGE_ALL = "0";// 联动“所有”
    public static final int DOOR_LINKAGE = 0;// 门-联动时可以作为输入点，也可以作为输出点。
    public static final int READER_LINKAGE = 1;// 读头－联动时只作为输入点
    public static final int AUX_LINKAGE = 1;// 辅助点－联动时辅助输入只能作为输入，辅助输出只能作为输出
    public static final int IPC_LINKAGE = 2;// ipc作为输出类型的联动

    // 门禁事件
    public static final char ENABLED = '1';// 二进制判断，支持这个模块功能（事件类型、验证方式）

    public static final String EVENT_NO_UNDEFINED = "acc_eventNo_undefined";

    // 公共事件
    public static final Map<Integer, String> PUBLIC_EVENT = new HashMap<>();
    static {
        PUBLIC_EVENT.put(4, "acc_eventNo_4");// 紧急状态密码开门
        PUBLIC_EVENT.put(5, "acc_eventNo_5");// 常开时间段内开门
        PUBLIC_EVENT.put(6, "acc_eventNo_6");// 触发联动事件
        PUBLIC_EVENT.put(7, "acc_eventNo_7");// 取消报警
        PUBLIC_EVENT.put(8, "acc_eventNo_8");// 远程开门
        PUBLIC_EVENT.put(9, "acc_eventNo_9");// 远程关门
        PUBLIC_EVENT.put(10, "acc_eventNo_10");// 禁用当天常开时间段
        PUBLIC_EVENT.put(11, "acc_eventNo_11");// 启用当天常开时间段
        PUBLIC_EVENT.put(12, "acc_eventNo_12");// 开启辅助输出
        PUBLIC_EVENT.put(13, "acc_eventNo_13");// 关闭辅助输出
        PUBLIC_EVENT.put(22, "acc_eventNo_22");// 非法时间段
        PUBLIC_EVENT.put(23, "acc_eventNo_23");// 非法访问
        PUBLIC_EVENT.put(24, "acc_eventNo_24");// 反潜
        PUBLIC_EVENT.put(25, "acc_eventNo_25");// 互锁
        PUBLIC_EVENT.put(28, "acc_eventNo_28");// 门开超时
        PUBLIC_EVENT.put(36, "acc_eventNo_36");// 门非有效时间段(按出门按钮)
        PUBLIC_EVENT.put(37, "acc_eventNo_37");// 常开时间段无法关门
        // PUBLIC_EVENT.put(38, PageUtil.i18n(null, "${acc_eventNo_38}"));// 卡已挂失
        PUBLIC_EVENT.put(39, "acc_eventNo_39");// 黑名单
        PUBLIC_EVENT.put(42, "acc_eventNo_42");// 韦根格式错误
        PUBLIC_EVENT.put(47, "acc_eventNo_47");// 发送命令失败
        PUBLIC_EVENT.put(102, "acc_eventNo_102");// 门被意外打开
        PUBLIC_EVENT.put(200, "acc_eventNo_200");// 门已打开
        PUBLIC_EVENT.put(201, "acc_eventNo_201");// 门已关闭
        PUBLIC_EVENT.put(202, "acc_eventNo_202");// 出门按钮开门
        PUBLIC_EVENT.put(204, "acc_eventNo_204");// 常开时间段结束
        PUBLIC_EVENT.put(205, "acc_eventNo_205");// 远程开门常开
        PUBLIC_EVENT.put(206, "acc_eventNo_206");// 设备启动
        PUBLIC_EVENT.put(208, "acc_eventNo_208");// 超级用户开门
        PUBLIC_EVENT.put(209, "acc_eventNo_209");// 触发出门按钮(被锁定)
        // PUBLIC_EVENT.put(211, PageUtil.i18n(null, "${acc_eventNo_211}"));//超级用户关门--梯控事件
        PUBLIC_EVENT.put(220, "acc_eventNo_220");// 辅助输入点断开
        PUBLIC_EVENT.put(221, "acc_eventNo_221");// 辅助输入点短路
    }

    // 仅 一体机、指静脉、面部机 支持的事件
    public static final Map<Integer, String> DEVICE_ACCESS_CONTROL_EVENT = new HashMap<Integer, String>();
    static {
        DEVICE_ACCESS_CONTROL_EVENT.put(100, "acc_eventNo_100");// 防拆报警: 机器被拆除
        DEVICE_ACCESS_CONTROL_EVENT.put(210, "acc_eventNo_215");// 首卡开门(密码)
    }

    // 一体机不支持的事件
    public static final Map<Integer, String> DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT = new HashMap<>();
    static {
        DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.put(25, "acc_eventNo_25");// 互锁
        DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.put(39, "acc_eventNo_39");// 黑名单
        DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.put(42, "acc_eventNo_42");// 韦根格式错误
        DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.put(208, "acc_eventNo_208");// 超级用户开门
        DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.put(209, "acc_eventNo_209");// 触发出门按钮(被锁定)
        DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.put(215, "acc_eventNo_215");// 首卡开门(密码)
        DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.put(220, "acc_eventNo_220");// 辅助输入点断开
        DEVICE_ACCESS_CONTROL_EVENT_NO_SUPPORT.put(221, "acc_eventNo_221");// 辅助输入点短路
    }

    // -----------以上均为对事件进行简单归类后的事件定义。下面为兼容模式下的事件定义------2013.12.19 Darcy
    // 合并前的事件
    public static final Map<Integer, String> OLD_EVENT = new HashMap<>();
    static {
        OLD_EVENT.put(0, "acc_eventNo_0");// 正常刷卡开门
        OLD_EVENT.put(1, "acc_eventNo_1");// 常开时间段内刷卡//#含门常开时段和首卡常开设置的开门时段 （开门后）
        OLD_EVENT.put(2, "acc_eventNo_2");// 首卡开门(刷卡)
        OLD_EVENT.put(3, "acc_eventNo_3");// 多人开门(刷卡)//门打开
        OLD_EVENT.put(20, "acc_eventNo_20");// 刷卡间隔太短
        OLD_EVENT.put(21, "acc_eventNo_21");// 门非有效时间段(刷卡)
        OLD_EVENT.put(26, "acc_eventNo_26");// 多人验证(刷卡//刷卡
        OLD_EVENT.put(27, "acc_eventNo_27");// 卡未注册//10
        OLD_EVENT.put(29, "acc_eventNo_29");// 卡已过有效期
        OLD_EVENT.put(30, "acc_eventNo_30");// 密码错误
        OLD_EVENT.put(41, "acc_eventNo_41");// 验证方式错误
        // OLD_EVENT.put(43, PageUtil.i18n(null, "${acc_eventNo_43}"));//反潜验证超时//固件连不通软件--保留字段 屏蔽
        OLD_EVENT.put(47, "acc_eventNo_47");// 发送命令失败//增加开门命令失败事件 ，产生开门命令而485通信失败时上传此事件，事件代码为 38
        OLD_EVENT.put(48, "acc_eventNo_48");// 多人验证失败(刷卡)
        OLD_EVENT.put(49, "acc_eventNo_49");// 门非有效时间段(密码)
        OLD_EVENT.put(50, "acc_eventNo_50");// 按密码间隔太短
        OLD_EVENT.put(51, "acc_eventNo_51");// 多人验证(密码)
        OLD_EVENT.put(52, "acc_eventNo_52");// 多人验证失败(密码)
        OLD_EVENT.put(53, "acc_eventNo_53");// 密码已过有效期
        OLD_EVENT.put(101, "acc_eventNo_101");// 胁迫密码开门
        OLD_EVENT.put(207, "acc_eventNo_207");// 密码开门
        OLD_EVENT.put(214, "acc_eventNo_214");// 多人开门(密码)
        OLD_EVENT.put(215, "acc_eventNo_215");// 首卡开门(密码)
        OLD_EVENT.put(216, "acc_eventNo_216");// 常开时间段内按密码
        OLD_EVENT.putAll(PUBLIC_EVENT);
    }

    // 指纹设备才支持的事件
    public static final Map<Integer, String> OLD_FP_EVENT = new HashMap<Integer, String>();
    static {
        OLD_FP_EVENT.put(14, "acc_eventNo_14");// 正常按指纹开门
        OLD_FP_EVENT.put(15, "acc_eventNo_15");// 多人开门(按指纹)
        OLD_FP_EVENT.put(16, "acc_eventNo_16");// 常开时间段内按指纹
        OLD_FP_EVENT.put(17, "acc_eventNo_17");// 卡加指纹开门
        OLD_FP_EVENT.put(18, "acc_eventNo_18");// 首卡开门(按指纹)
        OLD_FP_EVENT.put(19, "acc_eventNo_19");// 首卡开门(卡加指纹)
        OLD_FP_EVENT.put(31, "acc_eventNo_31");// 按指纹间隔太短
        OLD_FP_EVENT.put(32, "acc_eventNo_32");// 多人验证(按指纹)
        OLD_FP_EVENT.put(33, "acc_eventNo_33");// 指纹已过有效期
        OLD_FP_EVENT.put(34, "acc_eventNo_34");// 指纹未注册
        OLD_FP_EVENT.put(35, "acc_eventNo_35");// 门非有效时间段(按指纹)
        OLD_FP_EVENT.put(40, "acc_eventNo_40");// 多人验证失败(按指纹)
        OLD_FP_EVENT.put(103, "acc_eventNo_103");// 胁迫指纹开门
        OLD_FP_EVENT.put(203, "acc_eventNo_203");// 多人开门(卡加指纹)
    }

    // 合并验证方式后的事件
    public static final Map<Integer, String> SIMPLE_EVENT = new HashMap<>();

    static {
        SIMPLE_EVENT.put(0, "acc_newEventNo_0");// 正常验证通过
        SIMPLE_EVENT.put(1, "acc_newEventNo_1");// 常开时间段内验证
        SIMPLE_EVENT.put(2, "acc_newEventNo_2");// 首人开门
        SIMPLE_EVENT.put(3, "acc_newEventNo_3");// 多人开门
        SIMPLE_EVENT.put(20, "acc_newEventNo_20");// 操作间隔太短
        SIMPLE_EVENT.put(21, "acc_newEventNo_21");// 门非有效时间段验证开门
        SIMPLE_EVENT.put(26, "acc_newEventNo_26");// 多人验证等待
        SIMPLE_EVENT.put(27, "acc_newEventNo_27");// 人未登记
        SIMPLE_EVENT.put(29, "acc_newEventNo_29");// 人已过有效期
        SIMPLE_EVENT.put(30, "acc_newEventNo_30");// 密码错误（胁迫密码、紧急密码）
        SIMPLE_EVENT.put(41, "acc_newEventNo_41");// 验证方式错误（一体机中增加）
        SIMPLE_EVENT.put(43, "acc_newEventNo_43");// 人员锁定
        SIMPLE_EVENT.put(44, "acc_newEventNo_44");// 后台验证失败
        SIMPLE_EVENT.put(45, "acc_newEventNo_45");// 后台验证超时
        SIMPLE_EVENT.put(48, "acc_newEventNo_48");// 多人验证失败
        SIMPLE_EVENT.put(54, "acc_newEventNo_54");// 电池电压过低
        SIMPLE_EVENT.put(55, "acc_newEventNo_55");// 立即更换电池
        SIMPLE_EVENT.put(56, "acc_newEventNo_56");// 非法操作
        SIMPLE_EVENT.put(57, "acc_newEventNo_57");// 后备电源
        SIMPLE_EVENT.put(58, "acc_newEventNo_58");// 常开报警
        SIMPLE_EVENT.put(59, "acc_newEventNo_59");// 非法管理
        SIMPLE_EVENT.put(60, "acc_newEventNo_60");// 门被反锁
        SIMPLE_EVENT.put(61, "acc_newEventNo_61");// 重复验证
        SIMPLE_EVENT.put(62, "acc_newEventNo_62");// 禁止用户
        SIMPLE_EVENT.put(63, "acc_newEventNo_63");// 门已锁定
        SIMPLE_EVENT.put(64, "acc_newEventNo_64");// 出门按钮不在时间段内操作
        SIMPLE_EVENT.put(65, "acc_newEventNo_65");// 辅助输入不在时间段内操作
        SIMPLE_EVENT.put(66, "acc_newEventNo_66");// 读头升级失败
        SIMPLE_EVENT.put(67, "acc_newEventNo_67");// 远程比对成功(设备未授权)
        SIMPLE_EVENT.put(68, "acc_newEventNo_68");// 温度异常验证不通过
        SIMPLE_EVENT.put(69, "acc_newEventNo_69");// 未戴口罩验证不通过
        SIMPLE_EVENT.put(70, "acc_newEventNo_70");// 人脸比对服务器通信异常
        SIMPLE_EVENT.put(71, "acc_newEventNo_71");// 人脸服务器响应异常
        SIMPLE_EVENT.put(73, "acc_newEventNo_73");// 无效二维码
        SIMPLE_EVENT.put(74, "acc_newEventNo_74");// 二维码已过期
        SIMPLE_EVENT.put(100, "acc_eventNo_100");// 防拆报警: 机器被拆除, 一体机上传支持的事件，不支持则过滤
        SIMPLE_EVENT.put(101, "acc_newEventNo_101");// 胁迫开门报警
        SIMPLE_EVENT.put(105, "acc_newEventNo_105");// 无法连接服务器
        SIMPLE_EVENT.put(106, "acc_newEventNo_106");// 市电掉电
        SIMPLE_EVENT.put(107, "acc_newEventNo_107");// 电池掉电
        SIMPLE_EVENT.put(108, "acc_newEventNo_108");// 无法连接主控
        SIMPLE_EVENT.put(109, "acc_newEventNo_109");// 读头防拆报警
        SIMPLE_EVENT.put(110, "acc_newEventNo_110");// 读头离线
        SIMPLE_EVENT.put(112, "acc_newEventNo_112");// 扩展板离线
        SIMPLE_EVENT.put(114, "acc_newEventNo_114");// 线路检测，火警输入断开
        SIMPLE_EVENT.put(115, "acc_newEventNo_115");// 线路检测，火警输入短路
        SIMPLE_EVENT.put(116, "acc_newEventNo_116");// 线路检测，辅助输入断开
        SIMPLE_EVENT.put(117, "acc_newEventNo_117");// 线路检测，辅助输入短路
        SIMPLE_EVENT.put(118, "acc_newEventNo_118");// 线路检测，出门开关断开
        SIMPLE_EVENT.put(119, "acc_newEventNo_119");// 线路检测，出门开关短路
        SIMPLE_EVENT.put(120, "acc_newEventNo_120");// 线路检测，门磁断开
        SIMPLE_EVENT.put(121, "acc_newEventNo_121");// 线路检测，门磁短路
        SIMPLE_EVENT.put(207, "acc_eventNo_207");// 密码开门
        SIMPLE_EVENT.put(214, "acc_newEventNo_214");// 连上服务器
        SIMPLE_EVENT.put(217, "acc_newEventNo_217");// 成功连接主控
        SIMPLE_EVENT.put(218, "acc_newEventNo_218");// 身份证通行
        SIMPLE_EVENT.put(222, "acc_newEventNo_222");// 后台验证成功
        SIMPLE_EVENT.put(223, "acc_newEventNo_223");// 后台验证
        SIMPLE_EVENT.put(224, "acc_newEventNo_224");// 按门铃

        SIMPLE_EVENT.put(229, "acc_newEventNo_229");// 辅助输出定时常开
        SIMPLE_EVENT.put(230, "acc_newEventNo_230");// 辅助输出定时关闭常开

        SIMPLE_EVENT.put(232, "acc_newEventNo_232");// 验证通过（门延时打开）
        SIMPLE_EVENT.put(233, "acc_newEventNo_233");// 远程锁定
        SIMPLE_EVENT.put(234, "acc_newEventNo_234");// 远程解锁
        SIMPLE_EVENT.put(235, "acc_newEventNo_235");// 读头升级成功
        SIMPLE_EVENT.put(236, "acc_newEventNo_236");// 读头防拆报警解除
        SIMPLE_EVENT.put(237, "acc_newEventNo_237");// 读头在线
        SIMPLE_EVENT.put(239, "acc_newEventNo_239");// 设备呼叫
        SIMPLE_EVENT.put(240, "acc_newEventNo_240");// 通话结束
        SIMPLE_EVENT.put(243, "acc_newEventNo_243");// 火警输入断开
        SIMPLE_EVENT.put(244, "acc_newEventNo_244");// 火警输入短路
        SIMPLE_EVENT.put(247, "acc_newEventNo_247");// 扩展板在线
        SIMPLE_EVENT.putAll(PUBLIC_EVENT);
    }

    // 自定义异常事件 -----从500开始
    public static final int BACK_VERIFY = 223;
    public static final int BACK_VERIFY_SUCCESS = 222;
    public static final int BACK_VERIFY_FAIL = 44;
    public static final int BACK_VERIFY_GAPB_LOGICAL = 500;// 逻辑反潜
    public static final int BACK_VERIFY_AVAILABILITY_DATE = 501;// 有效日期
    public static final int BACK_VERIFY_OCCUPANCY = 502;
    public static final int BACK_VERIFY_INTERLOCK = 503;
    public static final int BACK_VERIFY_ROUTE = 504;
    public static final int BACK_VERIFY_GAPB_TIMED = 505;// 定时反潜
    public static final int BACK_VERIFY_GAPB_LOGICALANDTIMED = 506;// 定时逻辑反潜
    public static final int BACK_VERIFY_AVAILABILITY_FIRSTUSE = 507;// 第一次使用后有效天数
    public static final int BACK_VERIFY_AVAILABILITY_TIMES = 508;// 使用次数
    public static final int BACK_VERIFY_PERSON_FAIL = 509;// 后台验证失败(人未登记)
    public static final int BACK_VERIFY_RULE_ERROR = 510;// 后台验证失败(数据异常)
    //
    // 自定义报警 ----从700开始
    public static final short CUSTOM_ALARM_DMR = 701;

    public static final Map<Integer, String> ACC_ADVANCE_VALID = new HashMap<Integer, String>();
    static {
        ACC_ADVANCE_VALID.put(BACK_VERIFY_GAPB_LOGICAL, "acc_advanceEvent_500");
        ACC_ADVANCE_VALID.put(BACK_VERIFY_AVAILABILITY_DATE, "acc_advanceEvent_501");
        ACC_ADVANCE_VALID.put(BACK_VERIFY_OCCUPANCY, "acc_advanceEvent_502");
        ACC_ADVANCE_VALID.put(BACK_VERIFY_INTERLOCK, "acc_advanceEvent_503");
        // ACC_ADVANCE_VALID.put(BACK_VERIFY_ROUTE, "acc_advanceEvent_504"));
        ACC_ADVANCE_VALID.put(BACK_VERIFY_GAPB_TIMED, "acc_advanceEvent_505");
        ACC_ADVANCE_VALID.put(BACK_VERIFY_GAPB_LOGICALANDTIMED, "acc_advanceEvent_506");
        ACC_ADVANCE_VALID.put(BACK_VERIFY_AVAILABILITY_FIRSTUSE, "acc_advanceEvent_507");
        ACC_ADVANCE_VALID.put(BACK_VERIFY_AVAILABILITY_TIMES, "acc_advanceEvent_508");
        ACC_ADVANCE_VALID.put(BACK_VERIFY_PERSON_FAIL, "acc_advanceEvent_509");
        ACC_ADVANCE_VALID.put(BACK_VERIFY_RULE_ERROR, "acc_advanceEvent_510");
    }
    public static final Map<Short, String> CUSTOM_ALARM = new HashMap<>();
    static {
        CUSTOM_ALARM.put(CUSTOM_ALARM_DMR, "acc_alarmEvent_701");
    }
    public static final int CUSTOM_EVENT_NORMAL_GLOBALLINKAGE = 300;// 正常自定义事件--300：触发全局联动

    // 自定义事件--正常
    public static final Map<Integer, String> CUSTOM_EVENT_NORMAL = new HashMap<>();
    static {
        CUSTOM_EVENT_NORMAL.put(CUSTOM_EVENT_NORMAL_GLOBALLINKAGE, "acc_globalLinkage_trigger");
    }

    // // 高级门禁功能中的常量设置
    // public static final int OCCUPANCY_0_1 = 1; // Goes From 0 to 1
    // public static final int OCCUPANCY_1_2 = 2; //"...Goes From 1 to 2");
    // public static final int OCCUPANCY_2_1 = 3;//"...Goes From 2 to 1");
    // public static final int OCCUPANCY_1_0 = 4;//"...Goes From 1 to 0");
    // public static final int OCCUPANCY_AT_MIN = 5;//"...Is At Minimun");
    // //public static final int OCCUPANCY_MIN_SUB_1 = 6;//"...Is At Minimun - 1");
    // public static final int OCCUPANCY_MIN_ADD_1 = 6;//"...Is At Minimun + 1");
    // public static final int OCCUPANCY_AT_MAX = 7;//"...Is At Maximun");
    // public static final int OCCUPANCY_MAX_SUB_1 = 8;//"...Is At Maximun - 1");
    //
    public static final String AVAILABILITY_VALIDTYPE_DATE = "1";
    public static final String AVAILABILITY_VALIDTYPE_DAYS = "2";
    public static final String AVAILABILITY_VALIDTYPE_TIMES = "3";
    //
    // //occupancy 条件
    // public static final Map<Integer, String> OCCUPANCY_COND = new HashMap<Integer, String>();
    // static
    // {
    // OCCUPANCY_COND.put(OCCUPANCY_0_1, I18nUtil.i18nCode("acc_occupancy_from0to1"));
    // OCCUPANCY_COND.put(OCCUPANCY_1_2, I18nUtil.i18nCode("acc_occupancy_from1to2"));
    // OCCUPANCY_COND.put(OCCUPANCY_2_1, I18nUtil.i18nCode("acc_occupancy_from2to1"));
    // OCCUPANCY_COND.put(OCCUPANCY_1_0, I18nUtil.i18nCode("acc_occupancy_from1to0"));
    // OCCUPANCY_COND.put(OCCUPANCY_AT_MIN, I18nUtil.i18nCode("acc_occupancy_atMin"));
    // //OCCUPANCY_COND.put(OCCUPANCY_MIN_SUB_1, PageUtil.i18n(null, "${acc_occupancy_minSub1}"));
    // OCCUPANCY_COND.put(OCCUPANCY_MIN_ADD_1, I18nUtil.i18nCode("acc_occupancy_minAdd1"));
    // OCCUPANCY_COND.put(OCCUPANCY_AT_MAX, I18nUtil.i18nCode("acc_occupancy_atMax"));
    // OCCUPANCY_COND.put(OCCUPANCY_MAX_SUB_1, I18nUtil.i18nCode("acc_occupancy_maxSub1"));
    // }
    //
    // /** 门禁push设备通信关键参数----针对acc_device_option表,其他关键参数可从acc_device表中获得 */
    public static final List<String> DEV_COMM_KEY_PARAM_LIST = new ArrayList<String>();
    // /** 门禁系统所需的设备功能参数----针对acc_device_option表,其他关键参数可从acc_device表中获得 */
    public static final List<String> DEV_FUN_KEY_PARAM_LIST = new ArrayList<String>();
    static {
        DEV_COMM_KEY_PARAM_LIST.add("registryCode");
        DEV_COMM_KEY_PARAM_LIST.add("DeviceType");
        DEV_COMM_KEY_PARAM_LIST.add("SessionID");
        DEV_COMM_KEY_PARAM_LIST.add("ErrorDelay");
        DEV_COMM_KEY_PARAM_LIST.add("RequestDelay");
        DEV_COMM_KEY_PARAM_LIST.add("TransTimes");
        DEV_COMM_KEY_PARAM_LIST.add("TransInterval");
        DEV_COMM_KEY_PARAM_LIST.add("TransTables");
        DEV_COMM_KEY_PARAM_LIST.add("Realtime");

        DEV_FUN_KEY_PARAM_LIST.add("UserNameFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~IsOnlyRFMachine");
        DEV_FUN_KEY_PARAM_LIST.add("~REXInputFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~CardFormatFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~SupAuthrizeFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~ReaderCFGFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~ReaderLinkageFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~RelayStateFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~Ext485ReaderFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~TimeAPBFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~CtlAllRelayFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("~LossCardFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("OverallAntiFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("DeleteAndFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("DisableUserFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("ReaderCount");
        DEV_FUN_KEY_PARAM_LIST.add("AuxInCount");
        DEV_FUN_KEY_PARAM_LIST.add("AuxOutCount");
        DEV_FUN_KEY_PARAM_LIST.add("LockCount");
        DEV_FUN_KEY_PARAM_LIST.add("AccFunctions");
        DEV_FUN_KEY_PARAM_LIST.add("LogIDFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("DateFmtFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("DelayOpenDoorFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("UserOpenDoorDelayFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("DSTFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("OutRelaySetFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("MultiCardInterTimeFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("MachineTZFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("DelAllLossCardFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("SimpleEventType");
        DEV_FUN_KEY_PARAM_LIST.add("AutoServerFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("AutoServerMode");
        DEV_FUN_KEY_PARAM_LIST.add("IPCLinkFunOn");
        DEV_FUN_KEY_PARAM_LIST.add("IPCLinkServerIP");
        // 是否支持用户照片
        DEV_FUN_KEY_PARAM_LIST.add("PhotoFunOn");
        // 是否支持指静脉
        DEV_FUN_KEY_PARAM_LIST.add("FvFunOn");
        // 是否支持面部
        DEV_FUN_KEY_PARAM_LIST.add("FaceFunOn");
        // 是否支持指纹
        DEV_FUN_KEY_PARAM_LIST.add("FingerFunOn");
        // 是否支持拍照
        DEV_FUN_KEY_PARAM_LIST.add("CameraOpen");
        // 新控制器门禁功能支持参数
        DEV_FUN_KEY_PARAM_LIST.add("AccSupportFunList");
        DEV_FUN_KEY_PARAM_LIST.add("DoorMaskOn");
        // 主控功能参数
        DEV_FUN_KEY_PARAM_LIST.add("MasterControlOn");
        // 子控功能参数
        DEV_FUN_KEY_PARAM_LIST.add("SubControlOn");
    }
    //
    // //门禁功能参数
    // public static final Map<String, String> ACC_FUNCTION_PARAM = new HashMap<String, String>();
    // static
    // {
    // //未完 待定
    // // ACC_FUNCTION_PARAM.put("0", "~SupAuthrizeFunOn");
    // // ACC_FUNCTION_PARAM.put("1", "LossCardfunOn");
    // // ACC_FUNCTION_PARAM.put("2", "acc_occupancy_from1to2");
    // // ACC_FUNCTION_PARAM.put("3", "acc_occupancy_from2to1");
    // // ACC_FUNCTION_PARAM.put("4", "acc_occupancy_from1to0");
    // // ACC_FUNCTION_PARAM.put("5", "acc_occupancy_atMin");
    // // ACC_FUNCTION_PARAM.put("6", "acc_occupancy_minSub1");
    // // ACC_FUNCTION_PARAM.put("7", "acc_occupancy_minAdd1");
    // // ACC_FUNCTION_PARAM.put("8", "acc_occupancy_atMax");
    // // ACC_FUNCTION_PARAM.put("9", "TimeAPBFunON");
    // // ACC_FUNCTION_PARAM.put("10", "acc_occupancy_from0to1");
    // // ACC_FUNCTION_PARAM.put("11", "acc_occupancy_from0to1");
    // // ACC_FUNCTION_PARAM.put("12", "acc_occupancy_from1to2");
    // // ACC_FUNCTION_PARAM.put("13", "AuxOutTZFunOn");
    // // ACC_FUNCTION_PARAM.put("14", "acc_occupancy_from1to0");
    // // ACC_FUNCTION_PARAM.put("15", "acc_occupancy_atMin");
    // // ACC_FUNCTION_PARAM.put("16", "acc_occupancy_atMin");
    // }
    //
    // //获取人员，指纹和事件记录
    // public static final String IS_ERROR_TRANSACTION = "IS_ERROR_TRANSACTION_";
    // public static final String INSERT_RECORD_TRANSACTION = "INSERT_RECORD_TRANSACTION_";
    // public static final String UPDATE_RECORD_TRANSACTION = "UPDATE_RECORD_TRANSACTION_";
    // public static final String SUM_RECORD_TRANSACTION = "SUM_RECORD_TRANSACTION_";
    //
    // public static final String IS_ERROR_USER = "IS_ERROR_USER_";
    // public static final String INSERT_RECORD_USER = "INSERT_RECORD_USER_";
    // public static final String UPDATE_RECORD_USER = "UPDATE_RECORD_USER_";
    // public static final String SUM_RECORD_USER = "SUM_RECORD_USER_";

    public static final String DEVICE_CONTROL_NAME = "CONTROL";// 控制器
    public static final String DEVICE_ACCESS_CONTROL_NAME = "ONEMACHINE";// 一体机
    public static final String DEVICE_ACCESS_VEIN_NAME = "FINGERVEIN";// 指静脉
    public static final String DEVICE_WIRELESS_LOCK_NAME = "WLOCK";// 无线锁
    //
    // /** 设备支持的功能 */
    // 控制器
    public static final List<String> DEVICE_CONTROL_FUNCTION = new ArrayList<String>();
    static {
        DEVICE_CONTROL_FUNCTION.add(AccSyncDataType.ACCLEVEL.getValue());// 人员、门禁权限
        DEVICE_CONTROL_FUNCTION.add(AccSyncDataType.TIMEZONEANDHOLIDAY.getValue());// 时间段、节假日
        DEVICE_CONTROL_FUNCTION.add(AccSyncDataType.DOOROPT.getValue());// 门参数
        DEVICE_CONTROL_FUNCTION.add(AccSyncDataType.LINKAGE.getValue());// 联动
        DEVICE_CONTROL_FUNCTION.add(AccSyncDataType.INTERLOCK.getValue());// 互锁
        DEVICE_CONTROL_FUNCTION.add(AccSyncDataType.ANTIPASSBACK.getValue());// 反潜
        DEVICE_CONTROL_FUNCTION.add(AccSyncDataType.FIRSTPERSON.getValue());// 首人开门
        DEVICE_CONTROL_FUNCTION.add(AccSyncDataType.MULTIPERSON.getValue());// 多人开门
    }
    // 一体机
    public static final List<String> DEVICE_ACCESS_FUNCTION = new ArrayList<String>();
    static {
        DEVICE_ACCESS_FUNCTION.add(AccSyncDataType.ACCLEVEL.getValue());// 人员、门禁权限
        DEVICE_ACCESS_FUNCTION.add(AccSyncDataType.TIMEZONEANDHOLIDAY.getValue());// 时间段、节假日
        DEVICE_ACCESS_FUNCTION.add(AccSyncDataType.DOOROPT.getValue());// 门参数
        DEVICE_ACCESS_FUNCTION.add("antiPassback");// 反潜
        DEVICE_ACCESS_FUNCTION.add("firstPerson");// 首人开门
        DEVICE_ACCESS_FUNCTION.add(AccSyncDataType.MULTIPERSON.getValue());// 多人开门
        DEVICE_ACCESS_FUNCTION.add("linkage");// 联动
        // DEVICE_ACCESS_FUNCTION.add("interlock");//互锁
    }
    //
    // 无线锁
    public static final List<String> DEVICE_WIRELESS_LOCK_FUNCTION = new ArrayList<String>();
    static {
        DEVICE_WIRELESS_LOCK_FUNCTION.add(AccSyncDataType.ACCLEVEL.getValue());// 人员、门禁权限
        DEVICE_WIRELESS_LOCK_FUNCTION.add(AccSyncDataType.TIMEZONEANDHOLIDAY.getValue());// 时间段、节假日
        DEVICE_WIRELESS_LOCK_FUNCTION.add(AccSyncDataType.DOOROPT.getValue());// 门参数
        // DEVICE_WIRELESS_LOCK_FUNCTION.add("linkage");//联动
        // DEVICE_WIRELESS_LOCK_FUNCTION.add("interlock");//互锁
        // DEVICE_WIRELESS_LOCK_FUNCTION.add("antiPassback");//反潜
        // DEVICE_WIRELESS_LOCK_FUNCTION.add("firstPerson");//首人开门
        // DEVICE_WIRELESS_LOCK_FUNCTION.add("multiPerson");//多人开门
    }
    // IR9000不支持的功能列表 TODO 暂时还没用 by juvenile.li add 20171115
    public static final List<String> IR9000_NOT_SUPPORT_FP = new ArrayList<String>();
    static {
        IR9000_NOT_SUPPORT_FP.add(AccSyncDataType.INTERLOCK.getValue());// 互锁
        IR9000_NOT_SUPPORT_FP.add(AccSyncDataType.ANTIPASSBACK.getValue());// 反潜
        IR9000_NOT_SUPPORT_FP.add(AccSyncDataType.FIRSTPERSON.getValue());// 首人开门
        IR9000_NOT_SUPPORT_FP.add(AccSyncDataType.MULTIPERSON.getValue());// 多人开门
    }

    public static final Map<String, List<String>> FUNCTION = new HashMap<String, List<String>>();
    static {
        FUNCTION.put("CONTROL", DEVICE_CONTROL_FUNCTION);
        FUNCTION.put("ONEMACHINE", DEVICE_ACCESS_FUNCTION);
        FUNCTION.put("WLOCK", DEVICE_WIRELESS_LOCK_FUNCTION);
    }
    // 不支持指纹的设备
    // public static final List<String> NOT_SUPPORT_FP = new ArrayList<String>();
    // static
    // {
    // NOT_SUPPORT_FP.add(DEVICE_WIRELESS_LOCK + "");
    // }

    // 主从机配置
    public static final String ACC_MASTERSLAVE_CONFIG_CANCEL = "0";// 取消主从机配置
    public static final String ACC_MASTERSLAVE_CONFIG_MASTER = "1";// 主机
    public static final String ACC_MASTERSLAVE_CONFIG_SLAVE = "2";// 从机

    // 支持修改RS485地址的设备类型,没有拨码开关才支持-add by darcy 20150115
    public static final List<Short> DEVICE_SUPPORT_MODIFY_RS485_ADDRESS =
        new ArrayList<Short>(DEVICE_ACCESS_MACHINE_TYPE);
    static {
        DEVICE_SUPPORT_MODIFY_RS485_ADDRESS.add(DEVICE_C4_200);
        DEVICE_SUPPORT_MODIFY_RS485_ADDRESS.add(DEVICE_C4_400);
        DEVICE_SUPPORT_MODIFY_RS485_ADDRESS.add(DEVICE_C4_400_TO_200);
    }

    /* 根据设备类型，显示名称 */
    public static final Short DEVICE_CONTROL = 0;// 其他（默认控制器）
    public static final String ONEMACHINE = "ONEMACHINE";
    public static final String FINGERVEIN = "FINGERVEIN";
    public static final String CONTROL = "CONTROL";
    public static final Map<Short, String> MACHINETYPE_DEVTYPE = new HashMap<Short, String>();
    static {
        MACHINETYPE_DEVTYPE.put(DEVICE_ACCESS_CONTROL, ONEMACHINE);// 一体机
        MACHINETYPE_DEVTYPE.put(DEVICE_ACCESS_VEIN, FINGERVEIN);// 指静脉
        MACHINETYPE_DEVTYPE.put(DEVICE_CONTROL, CONTROL);// 其它（默认控制器）
    }
    public static final Map<String, String> DEV_NAME_MAP = new HashMap<String, String>();
    static {
        DEV_NAME_MAP.put(ONEMACHINE, "acc_dev_oneMachine");
        DEV_NAME_MAP.put(FINGERVEIN, "acc_dev_fingervein");
        DEV_NAME_MAP.put(CONTROL, "acc_dev_control");
    }
    // 全局联动触发条件中和人员直接相关的条件
    public static final List<Integer> GLOBAL_LINKAGE_TRIGGER_PER_FILTER = new ArrayList<Integer>();
    static {
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(0);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(2);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(3);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(21);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(22);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(23);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(24);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(29);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(39);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(41);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(48);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(101);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(208);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(222);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(223);
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(500);// 全局反潜(逻辑)
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(501);// 人员有效性(有效日期)
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(503);// 全局互锁
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(505);// 全局反潜(定时)
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(506);// 全局反潜(定时逻辑)
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(507);// 人员有效性(第一次使用后有效天数)
        GLOBAL_LINKAGE_TRIGGER_PER_FILTER.add(508);// 人员有效性(使用次数)
    }

    // 事件解析时避免卡号重复解析的事件。--验证未通过但是卡号会上传的事件。
    public static final List<Integer> CARD_PERSON_REPEATED_EVENT_FILTER = new ArrayList<Integer>();
    static {
        CARD_PERSON_REPEATED_EVENT_FILTER.add(27);// 卡未注册
        CARD_PERSON_REPEATED_EVENT_FILTER.add(20);// 刷卡间隔太短
        CARD_PERSON_REPEATED_EVENT_FILTER.add(38);// 卡已挂失
    }

    // 全局联动规则适用人员范围
    public static final short GLOBAL_LINKAGE_RANGE_ALL = 0;
    public static final short GLOBAL_LINKAGE_RANGE_SELECTED = 1;
    public static final short GLOBAL_LINKAGE_RANGE_NOSUPPORT = 2;

    // 视频硬联动图片保存位置
    public static final String LINKAGE_PHOTO_PATH = "/upload/event/photo";
    // 视频硬联动图片保存位置
    public static final String MAP_PATH = "/upload/acc/accMap";

    // 指静脉算法版本
    public static final String FV_BIO_VERSION = "3";
    // 7：指静脉
    public static final String FV_BIO_TYPE = "7";

    // 一体机TF1700
    public static final String DEVICE_TF1700 = "TF1700";

    // 获取人员信息操作
    public static final String UPLOAD_PERSONINFO = "uploadPersonInfo";
    // 获取事件记录操作
    public static final String UPLOAD_TRANSACTION = "uploadTransaction";

    // account数据存放的key
    public static final String ACCOUNT_DATA_ACC = "ACCOUNT_DATA_ACC_";

    // public static final List<Object[]> GLOBAL_LINKAGE_TRIGGER_BG_VERIFY = new ArrayList<Object[]>();
    // static
    // {
    // GLOBAL_LINKAGE_TRIGGER_BG_VERIFY.add(new Object[]{500, I18nUtil.i18nCode("acc_advanceEvent_500")});
    // GLOBAL_LINKAGE_TRIGGER_BG_VERIFY.add(new Object[]{505, I18nUtil.i18nCode("acc_advanceEvent_505")});
    // GLOBAL_LINKAGE_TRIGGER_BG_VERIFY.add(new Object[]{506, I18nUtil.i18nCode("acc_advanceEvent_506")});
    // GLOBAL_LINKAGE_TRIGGER_BG_VERIFY.add(new Object[]{503, I18nUtil.i18nCode("acc_advanceEvent_503")});
    // }

    // 监控配置的状态
    public static final int ACC_MONITORSETTINGS_NONE = 0x000000;
    public static final int ACC_MONITORSETTINGS_MONITOR = 0x000001;
    public static final int ACC_MONITORSETTINGS_OPEN_DOOR = 0x000002;
    public static final int ACC_MONITORSETTINGS_ALARM = 0x000004;
    public static final int ACC_MONITORSETTINGS_SOUND = 0x000008;

    public static final int[] ACC_MONITORSETTINGS = new int[5];
    static {
        ACC_MONITORSETTINGS[0] = ACC_MONITORSETTINGS_NONE;
        ACC_MONITORSETTINGS[1] = ACC_MONITORSETTINGS_MONITOR;
        ACC_MONITORSETTINGS[2] = ACC_MONITORSETTINGS_OPEN_DOOR;
        ACC_MONITORSETTINGS[3] = ACC_MONITORSETTINGS_ALARM;
        ACC_MONITORSETTINGS[4] = ACC_MONITORSETTINGS_SOUND;
    }

    /**
     * 绑定一体机做为韦根读头的控制器所属的模块，1为门禁
     */
    public static final short WG_READER_MODULE_ACC = 1;
    /**
     * 绑定一体机做为韦根读头的控制器所属的模块，2为梯控
     */
    public static final short WG_READER_MODULE_ELE = 2;

    /**
     * 用于判断事件是否为可以通过的事件
     */
    public static final List<Short> TRANSACTION_VERIFY_SUCCESS = new ArrayList<Short>();
    static {
        TRANSACTION_VERIFY_SUCCESS.add((short)0);// 正常验证开门
        TRANSACTION_VERIFY_SUCCESS.add((short)1);// 常开时间段内验证
        TRANSACTION_VERIFY_SUCCESS.add((short)2);// 首人开门
        TRANSACTION_VERIFY_SUCCESS.add((short)3);// 多人开门
        TRANSACTION_VERIFY_SUCCESS.add((short)14);// 正常按指纹开门
        TRANSACTION_VERIFY_SUCCESS.add((short)15);// 多人开门（按指纹）
        TRANSACTION_VERIFY_SUCCESS.add((short)16);// 常开时间段内按指纹
        TRANSACTION_VERIFY_SUCCESS.add((short)17);// 卡加指纹开门
        TRANSACTION_VERIFY_SUCCESS.add((short)18);// 首人开门（按指纹）
        TRANSACTION_VERIFY_SUCCESS.add((short)19);// 首人开门（卡加指纹）
        TRANSACTION_VERIFY_SUCCESS.add((short)203);// 多人开门(卡加指纹)
        TRANSACTION_VERIFY_SUCCESS.add((short)207);// 密码开门
        TRANSACTION_VERIFY_SUCCESS.add((short)208);// 超级用户开门
        TRANSACTION_VERIFY_SUCCESS.add((short)214);// 多人开门(密码)
        TRANSACTION_VERIFY_SUCCESS.add((short)215);// 首人开门(密码)
        TRANSACTION_VERIFY_SUCCESS.add((short)222);// 后台验证成功
        TRANSACTION_VERIFY_SUCCESS.add((short)232);// 验证通过（门延时打开）
    }

    /** 设备状态 */
    public static final short DEV_STATE_OFFLINE = 0;// 0离线
    public static final short DEV_STATE_ONLINE = 1;// 1在线
    public static final short DEV_STATE_DISABLE = 2;// 2禁用
    public static final short DEV_STATE_LOCK = 3;// 3锁定

    /** 实时监控--状态维护 */
    public static final String DEV_RTMONITOR_STATE = "DEV_RTMONITOR_STATE_";// 实时监控状态 +sn
    public static final String DEV_RTMONITOR_STATE_CHANGED = "_DEV_RTMONITOR_STATE_CHANGED";// 实时监控 前面需要加前缀。比如ACC_
    public static final String DEV_RTMONITOR_STATE_CHANGED_LOCK = "_DEV_RTMONITOR_STATE_CHANGED_LOCK";// 操作key为_DEV_RTMONITOR_STATE_CHANGED的锁
    public static final String RT_LOGS = "RT_LOGS_";// 实时事件保存在redis中的key，例如RT_LOGS_ELE,RT_LOGS_ACC等等
    public static final String QUERY_DATA = "QUERY_DATA_";// 获取所有事件记录人员等数据，保存在redis中的key，例如QUERY_DATA_ELE,QUERY_DATA_ACC等等
    public static final String QUERY_EMP_DATA = "QUERY_EMP_DATA_";// 从设备获取人员信息

    /** 设备通信方式 */
    public static final short COMM_TCPIP = 1;// PULL
    public static final short COMM_RS485 = 2;// PULL
    public static final short COMM_HTTP = 3;// PUSH
    public static final short COMM_HTTPS = 4;// PUSH

    public static final int DEV_DISCONNECT_EVENT_NO = 700;
    public static final Map<Integer, String> ACC_MONITOR_DEV_EVENT = new HashMap<>();
    static {
        ACC_MONITOR_DEV_EVENT.put(DEV_DISCONNECT_EVENT_NO, "common_commStatus_disconnected");// 设备连接断开
    }

    // 设备参数默认值
    public static final String DEVICE_OPTIONS_DEFAULT_VALUE = "0";

    // 存在多人开门
    public static final String DEV_HASBEENSET_COMBOPENDOOR = "hasBeenSetCombOpenDoor";

    public static final String NEW_RECORDS = "1";// 新记录
    public static final String CHECK_NEW_RECORDS = "3";// 检查并获取新记录

    public static final Map<String, Short> UPLOAD_PERSON_BIOINFO = new HashMap<>();
    static {
        UPLOAD_PERSON_BIOINFO.put("1", BaseConstants.BaseBioType.FP_BIO_TYPE);
        UPLOAD_PERSON_BIOINFO.put("2", BaseConstants.BaseBioType.FACE_BIO_TYPE);
        UPLOAD_PERSON_BIOINFO.put("3", BaseConstants.BaseBioType.VEIN_BIO_TYPE);
        UPLOAD_PERSON_BIOINFO.put("4", BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        UPLOAD_PERSON_BIOINFO.put("5", BaseConstants.BaseBioType.PALM_BIO_TYPE);
    }

    public static final String CMD_RETURN = "CMD_RETURN";

    public static final int DEV_MONITOR_DISABLE = -1002;// 设备监控禁用状态

    public enum AccSyncDataType {

        ACCLEVEL("accLevel"), TIMEZONEANDHOLIDAY("timeZoneAndHoliday"), DOOROPT("doorOpt"), LINKAGE("linkage"),
        INTERLOCK("interlock"), ANTIPASSBACK("antiPassback"), FIRSTPERSON("firstPerson"), MULTIPERSON("multiPerson"),
        WIEGANDFMT("wiegandFmt"), OUTRELAYSET("outRelaySet"), BACKGROUNDVERIFYPARAM("backgroundVerifyParam"),
        AUXINSET("auxinSet"), VERIFYMODERULE("verifyModeRule");

        private String value;

        AccSyncDataType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }

    /*-----------------------------API----------------------------------------*/
    // 成功
    public static final int OP_SUCCESS = 0;
    // 程序错误
    public static final int API_PROGRAM_ERROR = -1;
    // 该权限组不存在
    public static final int LEVEL_NOTEXIST = -24;
    // 该权限组下没有人员
    public static final int LEVEL_NOTHAS_PERSONID = -25;
    // 门禁权限下发失败
    public static final int LEVEL_ACC_ADD_FAILED = -40;
    // 删除门禁权限失败
    public static final int LEVEL_ACC_DELETE_FAILED = -41;
    // 人员编号或者权限组编号为空
    public static final int LEVEL_ACC_NULL = -42;
    // 权限组ID不能为空
    public static final int LEVEL_ACC_LEVELIDNOTNULL = -43;
    // 门ID不能为空
    public static final int ACC_DOOR_DOORIDNOTNULL = -44;
    // 开门时长在0~254之间
    public static final int ACC_DOOR_INTERVAL_SIZE = -45;
    // 门名称不能为空
    public static final int ACC_DOOR_DOORNAMENOTNULL = -46;
    // 门不存在
    public static final int ACC_DOOR_NOTEXIST = -47;
    // 设备SN不能为空
    public static final int ACC_TRANSACTION_SNNOTNULL = -48;
    // 权限组名称不为空
    public static final int LEVEL_ACC_LEVELNAMENOTNULL = -49;
    // 权限组名称不能包含特殊字符！
    public static final int LEVEL_ACC_NOSPECIALCHAR = -50;
    // 权限组名称存在非法字符串
    public static final int LEVEL_ACC_ERRORINFO = -51;
    // 区域不存在
    public static final int LEVEL_ACC_AREANOTEXIST = -52;
    // 时间段不存在
    public static final int LEVEL_ACC_TIMESEGNOTEXIST = -53;
    // 权限组存在
    public static final int LEVEL_ACC_LevelEXIST = -54;
    // 区域不能为空
    public static final int LEVEL_ACC_AREANAMENOTNULL = -55;
    // 时间段名称不能为空
    public static final int Level_ACC_TIMESEGNAMENOTNULL = -56;
    // 门存在
    public static final int ACC_DOOR_EXIST = -57;
    // 权限组下人员不存在
    public static final int ACC_LEVELPERSONEXIST = -58;
    // 时间戳不能为空
    public static final int ACC_TIMESTAMPNOTNULL = -59;

    // pageNo或者pageSize不能设置小于等于0
    public static final int API_WRONG_PAGE = -90;
    // pageSize 大于1000
    public static final int API_PAGE_OVERSIZE = -91;
    // 非法时间
    public static final int API_DATE_ERROR = -100;
    // 开始时间不能大于结束时间
    public static final int API_DATE_STARTTIME_LARGE = -101;

    // 设备不存在
    public static final int ACC_DEV_NOTEXIST = -102;
    // 设备处于离线或禁用状态
    public static final int ACC_DEV_OFFLINE = -103;
    // 设备不支持此功能
    public static final int ACC_DEV_NOTSUPPORTFUNCTION = -104;
    // 下发失败
    public static final int ACC_DEV_ISSUEDFAILED = -105;

    // 门处于离线或禁用状态
    public static final int ACC_DOOR_OFFLINE = -106;

    // 设备最后推送时间
    public static final String ACC_DEVICE_LAST_PUSH_TIME = "acc.device.lastPushTime";

    /** 门禁设备扩展板类型（和machinetype不冲突，目前用于软件下发定义扩展板的类型） */
    public static final short EXT_BOARD_TYPE_DM10 = 101;
    public static final short EXT_BOARD_TYPE_AUX485 = 103;
    public static final short EXT_BOARD_TYPE_EX0808 = 104;
    /** mqtt设备 */
    public static final short EXT_BOARD_TYPE_IO1602 = 105;
    public static final short EXT_BOARD_TYPE_IO0216 = 106;
    public static final short EXT_BOARD_TYPE_IO0808 = 107;
    public static final short EXT_BOARD_TYPE_APERIO = 108;

    /** 每种扩展板限制添加的数量 */
    public static final int EXT_BOARD_TYPE_DM10_LIMIT = 8;
    public static final int EXT_BOARD_TYPE_AUX485_LIMIT = 2;
    public static final int EXT_BOARD_TYPE_EX0808_LIMIT = 8;

    /**
     * 置顶
     */
    public static final String ACC_DOOR_TOP = "1";

    /**
     * 不置顶
     */
    public static final String ACC_DOOR_DEFAULT = "0";

    /** 门禁设备扩展板协议类型 */
    public static final Short EXT_PROTOCOL_TYPE_ZK485 = 1;
    public static final Short EXT_PROTOCOL_TYPE_OSDP = 2;

    public static final Map<Short, String> EXT_PROTOCOL_TYPE = new HashMap<>();
    static {
        EXT_PROTOCOL_TYPE.put(EXT_PROTOCOL_TYPE_ZK485, "ZK485");
        EXT_PROTOCOL_TYPE.put(EXT_PROTOCOL_TYPE_OSDP, "OSDP");
    }

    /** 设备操作权限，0-一般人员，14-管理员，2-登记员 */
    public static final Short PRIVILEGE_GENERALUSER = 0;
    public static final Short PRIVILEGE_ADMINISTRATOR = 14;
    public static final Short PRIVILEGE_ENROLLER = 2;

    /** 文件存在 */
    public static final Short FILE_EXIST = 0;
    /** 前端解析base64图片前缀 */
    public static final String PHOTO_BASE64_PREFIX = "data:image/jpg;base64,";

    /**
     * 推送门禁报警消息到微信小程序的事件编号
     */
    public static final List<Short> ALARM_MSG_EVENT_NO_LIST = new ArrayList();
    static {
        ALARM_MSG_EVENT_NO_LIST.add(Short.valueOf((short)100));// 防拆报警
        ALARM_MSG_EVENT_NO_LIST.add(Short.valueOf((short)101));// 胁迫密码开门
        ALARM_MSG_EVENT_NO_LIST.add(Short.valueOf((short)102));// 门被意外打开
    }

    /** redis实时事件监听通道 */
    public static final String RTMONITOR_CHANNEL_TRANSACTION = "rtMonitorChannelTransaction";
    /** redis门状态监听通道 */
    public static final String RTMONITOR_CHANNEL_DOORSTATE = "rtMonitorChannelDoorState";
    /** redis设备状态监听通道 */
    public static final String RTMONITOR_CHANNEL_DEVSTATE = "rtMonitorChannelDevState";
    /** redis设备事件监听通道 */
    public static final String RTMONITOR_CHANNEL_DEVEVENT = "rtMonitorChannelDevEvent";
    /** redis lcd监听通道 */
    public static final String RTMONITOR_CHANNEL_LCD = "rtMonitorChannelLcd";
    /** 报警事件 */
    public static final String ALARM_CHANNEL = "alarmChannel";

    /** 可见光手掌 */
    public static final Short TEMPLATE_VISILIGHT_PALM = 10;
    /** 虹膜 */
    public static final Short TEMPLATE_VISILIGHT_IRIS = 4;

    /** 设备事件类型报警事件优先级别： 低、中、高、危险 */
    /** 低 */
    public static final Short DEV_ALARMEVENTPRIORITY_LOW = 0;
    /** 中 */
    public static final Short DEV_ALARMEVENTPRIORITY_MID = 1;
    /** 高 */
    public static final Short DEV_ALARMEVENTPRIORITY_HIGH = 2;
    /** 危险 */
    public static final Short DEV_ALARMEVENTPRIORITY_DANGER = 3;

    /*-----------------------------扩展事件----------------------------------------*/
    /** 正常事件参数 */
    public static final String NEW_NORMAL_EVENT_TYPES = "NewNormalEventTypes";
    /** 异常事件参数 */
    public static final String NEW_ERROR_EVENT_TYPES = "NewErrorEventTypes";
    /** 警告事件参数 */
    public static final String NEW_WARNING_EVENT_TYPES = "NewWarningEventTypes";
    /** 定义新事件的偏移量 */
    public static final Map<String, Integer> NEW_EVENT_OFFSET = new HashMap<>();
    static {
        NEW_EVENT_OFFSET.put(NEW_NORMAL_EVENT_TYPES, 4000);
        NEW_EVENT_OFFSET.put(NEW_ERROR_EVENT_TYPES, 5000);
        NEW_EVENT_OFFSET.put(NEW_WARNING_EVENT_TYPES, 6000);
    }

    /** 新事件 */
    public static final Map<Integer, String> NEW_EVENT = new HashMap<>();
    /** 正常事件 */
    public static final Map<Integer, String> NEW_EVENT_NORMAL = new HashMap<>();
    /** 异常事件 */
    public static final Map<Integer, String> NEW_EVENT_ERROR = new HashMap<>();
    /** 警告事件 */
    public static final Map<Integer, String> NEW_EVENT_WARNING = new HashMap<>();

    static {
        NEW_EVENT_NORMAL.put(4008, "acc_newEventNo_4008");// 市电恢复
        NEW_EVENT_NORMAL.put(4014, "acc_newEventNo_4014");// 消防输入信号断开，结束门常开
        NEW_EVENT_NORMAL.put(4015, "acc_newEventNo_4015");// 门已在线
        NEW_EVENT_NORMAL.put(4018, "acc_newEventNo_4018");// 后台比对开门
    }

    static {
        NEW_EVENT_ERROR.put(5023, "acc_newEventNo_5023");// 消防状态受限中
        NEW_EVENT_ERROR.put(5024, "acc_newEventNo_5024");// 多人验证超时
        NEW_EVENT_ERROR.put(5029, "acc_newEventNo_5029");// 后台比对失败
    }

    static {
        NEW_EVENT_WARNING.put(6005, "acc_newEventNo_6005");// 记录容量达90%
        NEW_EVENT_WARNING.put(6006, "acc_newEventNo_6006");// 线路短路（485）
        NEW_EVENT_WARNING.put(6007, "acc_newEventNo_6007");// 线路短路（韦根）
        NEW_EVENT_WARNING.put(6011, "acc_newEventNo_6011");// 门已离线
        NEW_EVENT_WARNING.put(6012, "acc_newEventNo_6012");// 门拆机报警
        NEW_EVENT_WARNING.put(6013, "acc_newEventNo_6013");// 消防输入信号触发，开启门常开
        NEW_EVENT_WARNING.put(6015, "acc_newEventNo_6015");// 复位扩展设备电源
        NEW_EVENT_WARNING.put(6016, "acc_newEventNo_6016");// 恢复本机出厂设置
        NEW_EVENT_WARNING.put(6070, "acc_newEventNo_6070");// 后台比对黑名单禁止开门
    }

    static {
        NEW_EVENT.putAll(NEW_EVENT_NORMAL);
        NEW_EVENT.putAll(NEW_EVENT_ERROR);
        NEW_EVENT.putAll(NEW_EVENT_WARNING);
    }
    /*-----------------------------扩展事件 END----------------------------------------*/

    /** 设备参数，ntp服务器地址(仅用于软件) */
    public static final String NTP_KRY = "NtpServerAddress";
    /** ntp服务器开关，用于下发设备参数 */
    public static final String NTP_FUN_ON = "ntpFunOn";

    /**
     * best协议和push协议人员相关字段兼容 pro拓展
     **/
    public static final Map<String, String> MQTT_PUSH_PARAM = new HashMap<>();

    static {
        // mqtt 参数转 push
        MQTT_PUSH_PARAM.put("pin", "pin");
        MQTT_PUSH_PARAM.put("passwd", "password");
        MQTT_PUSH_PARAM.put("group", "group");
        MQTT_PUSH_PARAM.put("idNum", "cardno");
        MQTT_PUSH_PARAM.put("firstName", "name");
        MQTT_PUSH_PARAM.put("lastName", "lastName");
        MQTT_PUSH_PARAM.put("funSwitch", "funswitch");
        MQTT_PUSH_PARAM.put("disable", "disable");
        // push没有的
        MQTT_PUSH_PARAM.put("startTime", "starttime");
        MQTT_PUSH_PARAM.put("endTime", "endtime");
        MQTT_PUSH_PARAM.put("organizationNum", "organizationNum");
        MQTT_PUSH_PARAM.put("threatLevel", "threatLevel");
        // 多卡
        MQTT_PUSH_PARAM.put("card", "cardno");
        MQTT_PUSH_PARAM.put("type", "cardtype");
        MQTT_PUSH_PARAM.put("lossFlag", "lossFlag");
    }

    public static final Map<Short, String> ALARM_EVENT_PRIORITY = new HashMap<>();
    static {
        // 报警事件级别类型
        ALARM_EVENT_PRIORITY.put((short)0, "auth_security_strengthLevel0");
        ALARM_EVENT_PRIORITY.put((short)1, "auth_security_strengthLevel1");
        ALARM_EVENT_PRIORITY.put((short)2, "auth_security_strengthLevel2");
        ALARM_EVENT_PRIORITY.put((short)3, "acc_musterPointReport_danger");
    }

    // 支持nvr
    public static final String ACC_DEVICE_SUPPORTNVR = "1";

    /** 绑定类型-读头 */
    public static final Short CAMERA_TARGET_TYPE_READER = 0;
    /** 绑定类型-辅助输入 */
    public static final Short CAMERA_TARGET_TYPE_AUXIN = 1;
}
