package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.experimental.Accessors;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/3/14 11:28
 * @since 1.0.0
 */
@Accessors(chain = true)
public class AccTriggerGroupItem extends BaseItem {
    /** 触发点类型 门 */
    public static final Short TRIGGER_TYPE_DOOR = Short.valueOf("0");
    /** 触发点类型 读头 */
    public static final Short TRIGGER_TYPE_READER = Short.valueOf("4");

    /** 对象id */
    private String id;
    /** 业务id */
    private Long bussinessId;
    /** 触发点类型 0:门, 4:读头 */
    private Short type;
    /** 触发点 */
    private String addrs;

    @Override
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getBussinessId() {
        return bussinessId;
    }

    public void setBussinessId(Long bussinessId) {
        this.bussinessId = bussinessId;
    }

    public Short getType() {
        return type;
    }

    public void setType(Short type) {
        this.type = type;
    }

    public String getAddrs() {
        return addrs;
    }

    public void setAddrs(String addrs) {
        this.addrs = addrs;
    }
}
