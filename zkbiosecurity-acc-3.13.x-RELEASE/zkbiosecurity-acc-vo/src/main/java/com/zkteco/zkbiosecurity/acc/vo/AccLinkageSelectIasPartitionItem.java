package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.GridConfig;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2022/11/30 15:58
 * @since 1.0.0
 */
@Getter
@Setter
@GridConfig
@Accessors(chain = true)
public class AccLinkageSelectIasPartitionItem extends BaseItem {

    @GridColumn(checkbox = true, width = "40")
    private String id;

    /** 分区编号 */
    private Integer code;

    /** 分区名称 */
    @GridColumn(label = "base_linkage_ias_partitionName", width = "150")
    private String name;

    /** 设备名称 */
    @GridColumn(label = "base_linkage_ias_intrusionDeviceName")
    private String deviceName;

    /** 厂商 */
    private String manufacturer;

    private String type;

    private String selectId;

    private String filterId;
}
