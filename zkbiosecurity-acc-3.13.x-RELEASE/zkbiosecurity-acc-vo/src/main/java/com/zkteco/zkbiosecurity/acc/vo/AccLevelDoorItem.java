/**
 * @author: GenerationTools
 * @date: 2018-03-03 上午11:59 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁权限组门双列表-门vo
 * 
 * @author: yulong.dai
 * @date: 2018-03-03 上午11:59
 */
@From(after = "ACC_DOOR t LEFT JOIN ACC_LEVEL_DOOR ld ON ld.DOOR_ID=t.ID LEFT JOIN ACC_DEVICE d ON d.ID=t.DEV_ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
public class AccLevelDoorItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    /**  */
    @Column(name = "t.NAME")
    @GridColumn(label = "acc_door_name", width = "170", sortNo = 1)
    private String name;

    @Column(name = "d.DEV_ALIAS")
    @GridColumn(label = "common_ownedDev", width = "170", sortNo = 2)
    private String devName;

    @Column(name = "ld.LEVEL_ID", equalTag = "=")
    private String accLevelId;

    @Condition(value = "t.ID NOT IN (%s)", formatType = "quote")
    private String notInId;

    @Condition(value = "d.AUTH_AREA_ID", equalTag = "in")
    private String areaIds;

    @Column(name = "d.ID")
    private String deviceId;

    @Column(name = "ld.DOOR_ID")
    private String accDoorId;

    /**
     * 根据用户ID查询关联区域及子区域ID
     */
    @Condition(
        value = "d.AUTH_AREA_ID IN (SELECT ud.AUTH_AREA_ID FROM AUTH_USER_AREA ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 数据迁移扩展字段
     */
    /** 权限组名称 **/
    private String accLevelName;
    /** 权限组名称 **/
    private String doorNo;
    /** 设备号 **/
    private String deviceSn;

    /**
     * 默认构造方法
     */
    public AccLevelDoorItem() {
        super();
    }

    /**
     * 构造方法
     */
    public AccLevelDoorItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public AccLevelDoorItem(String id) {
        super(true);
        this.id = id;
    }

}