/**
 * @author: GenerationTools
 * @date: 2018-03-08 下午02:41 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁设备导出vo
 * 
 * @author: yulong.dai
 * @date: 2018-03-08 下午02:41
 */
@From(after = "ACC_DEVICE t")
@OrderBy(after = "t.BUSINESS_ID ASC")
@Getter
@Setter
@Accessors(chain = true)
public class AccDeviceExportItem extends BaseItem implements Serializable {

    /** 主键 */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sortNo = 0, sort = "na")
    private String id;

    @Column(name = "t.DEV_ALIAS")
    @GridColumn(columnType = "treexe", label = "common_dev_name", width = "140", sortNo = 1,
        editPermission = "acc:device:edit", editUrl = "/accDevice.do?edit")
    private String alias;

    @Column(name = "t.SN")
    @GridColumn(label = "common_dev_sn", width = "115", sortNo = 2)
    private String sn;

    @Column(name = "t.AUTH_AREA_ID", equalTag = "=")
    private String authAreaId;

    @GridColumn(label = "base_area_name", width = "70", sortNo = 3, sort = "na")
    private String authAreaName;

    @Column(name = "t.COMM_TYPE")
    @GridColumn(label = "common_dev_commType", format = "1=TCP/IP,2=RS485,3=HTTP", sortNo = 4)
    private String commType;

    @GridColumn(label = "common_dev_netConnectMode", width = "100",
        format = "0=acc_dev_netModeWired,1=acc_dev_netMode4G,2=acc_dev_netModeWifi", sortNo = 5, sort = "na")
    private String netConnectMode;

    @Column(name = "t.IP_ADDRESS")
    @GridColumn(label = "common_ipAddress", width = "100", sortNo = 6)
    private String ipAddress;

    @GridColumn(label = "common_dev_rs485Param", width = "80", sortNo = 7, sort = "na")
    private String rs485Param;

    @GridColumn(label = "common_status", width = "60", sortNo = 8, columnType = "custom",
        convert = "convertConnectState", sort = "na")
    private String connectState;

    @Column(name = "t.DEVICE_NAME")
    @GridColumn(label = "common_dev_deviceModel", width = "80", sortNo = 9)
    private String deviceName;

    @Column(name = "t.IS_REGISTRATIONDEVICE")
    @GridColumn(label = "acc_dev_registrationDevice", width = "60", sortNo = 10, convert = "convertToIcon",
        columnType = "custom")
    private String isRegistrationDevice;

    @Column(name = "t.FW_VERSION")
    @GridColumn(label = "common_dev_firmwareVersion", width = "180", sortNo = 11)
    private String fwVersion;

    @Condition(value = "t.AUTH_AREA_ID", equalTag = "in")
    private String areaIdIn;

    @Column(name = "t.COM_PORT")
    private String comPort;

    @Column(name = "t.COM_ADDRESS")
    private String comAddress;

    @Column(name = "t.BAUDRATE")
    private String baudrate;

    // pro扩展字段
    @GridColumn(label = "acc_dev_fireAlarmType", width = "100", sortNo = 12, sort = "na")
    private String fireAlarmType;

    @GridColumn(label = "common_devMonitor_leftCmdCount", width = "120", sortNo = 13, isExportExcel = false,
        sort = "na", show = false)
    private Long cmdCount;

    @Column(name = "t.ENABLED")
    private Boolean enabled;

    @Condition(value = "t.SN", equalTag = "in")
    private String snIn;

    @Condition(value = "t.SN", equalTag = "not in")
    private String snsNotIn;

    /**
     * 默认构造方法
     */
    public AccDeviceExportItem() {
        super();
    }
}