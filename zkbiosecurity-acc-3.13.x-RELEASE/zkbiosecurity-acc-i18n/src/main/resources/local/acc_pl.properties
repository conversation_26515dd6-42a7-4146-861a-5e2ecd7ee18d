#[1] Menu w lewo
acc_module=Dost<PERSON><PERSON>
acc_leftMenu_accDev=Sp<PERSON><PERSON>t kontroli dostępu
acc_leftMenu_auxOut=wyj<PERSON><PERSON> pomocnicze
acc_leftMenu_dSTime=Czas letni
acc_leftMenu_access=Kontrola dostępu
acc_leftMenu_door=Bramy
acc_leftMenu_accRule=Zasady kontroli dostępu
acc_leftMenu_interlock=Blokada interakcyjna
acc_leftMenu_antiPassback=Przeciw powrotu
acc_leftMenu_globalLinkage=Powiązanie na poziomie całego systemu
acc_leftMenu_firstOpen=Brama otwarta normalna przy pierwszej osoby
acc_leftMenu_combOpen=Wieloosobowe otwieranie bramy
acc_leftMenu_personGroup=Grupa personelu do wieloosobowego otwierania bramy
acc_leftMenu_level=Grupa uprawnień kontroli dostępu
acc_leftMenu_electronicMap=Mapa elektroniczna
acc_leftMenu_personnelAccessLevels=Uprawienie kontroli dostępu personelu
acc_leftMenu_searchByLevel=Wyszukiwanie według grup uprawnień
acc_leftMenu_searchByDoor=Wyszukiwanie przez bramy
acc_leftMenu_expertGuard=Zaawansowana kontrola dostępu
acc_leftMenu_zone=Strefa kontroli dostępu
acc_leftMenu_readerDefine=Definicja głowicy odczytu
acc_leftMenu_gapbSet=Przeciw powrotu na poziomie całego systemu
acc_leftMenu_whoIsInside=Przeglądanie personelu w strefie
acc_leftMenu_whatRulesInside=Przeglądanie zasady obowiązujące w danej strefie
acc_leftMenu_occupancy=Kontrola ilości personelu
acc_leftMenu_route=Określenie trasy
acc_leftMenu_globalInterlock=Blokada interakcyjna na poziomie całego systemu
acc_leftMeue_globalInterlockGroup=Grupa blokady interakcyjnej na poziomie całego systemu
acc_leftMenu_dmr=Czas trwania pobytu
acc_leftMenu_personLimit=Ważność personelu
acc_leftMenu_verifyModeRule=Zasady metody uwierzytelniania
acc_leftMenu_verifyModeRulePersonGroup=Grupa personelu do zasady metody uwierzytelniania
acc_leftMenu_extDev=Płyta rozszerzająca We/Wy
acc_leftMenu_firstInLastOut=Zapisy najwcześniejsze i najnowsze
acc_leftMenu_accReports=Raporty kontroli dostępu
#[3] Przedział czasu kontroli dostępu
acc_timeSeg_entity=Przedział czasu kontroli dostępu
acc_timeSeg_canNotDel=Przedział czasu jest w użyciu i jego usunięcie jest niedozwolone!
#
acc_common_ruleName=Nazwa zasady
acc_common_hasBeanSet=Ustalona
acc_common_notSet=Nieustalona
acc_common_hasBeenOpened=Włączona
acc_common_notOpened=Niewłączona
acc_common_partSet=Ustalona częściowo
acc_common_linkageAndApbTip=Mogą występować konflikty, gdy w tym samym czasie są ustawione powiązanie i powiązanie na poziomie całego systemu, albo przeciw powrotu i przeciw powrotu na poziomie całego systemu.
acc_common_vidlinkageTip=Należy upewnić się, że punkt wejściowy odpowiadający blokadom jest powiązany z dostępnym kanałem wideo, w przeciwnym razie funkcja blokady wideo nie będzie działać prawidłowo!
acc_common_accZoneFromTo=Nie można ustawić tych samych stref dostępu
acc_common_logEventNumber=Numer zapisu
acc_common_bindOrUnbindChannel=Kamery do powiązania/rozwiązywania
acc_common_boundChannel=Kamera powiązana
#Informacje o sprzęcie
acc_dev_iconType=Typ ikony
acc_dev_carGate=Bramka jazdy
acc_dev_channelGate=Kanał
acc_dev_acpType=Typ sterownika kontroli dostępu
acc_dev_oneDoorACP=Sterownik jednobramkowy
acc_dev_twoDoorACP=Sterownik dwubramkowy
acc_dev_fourDoorACP=Sterownik czterobramkowy
acc_dev_onDoorACD=Urządzenie all-in-one
acc_dev_switchToTwoDoorTwoWay=Przełączanie na dwoje dwukierunkowych bramy
acc_dev_addDevConfirm2=Wskazówka: Urządzenie zostało pomyślnie podłączone, ale typ sterownika nie odpowiada rzeczywistemu, zostanie zmodyfikowany do {0} sterownika bramy, kontynuować dodanie?
acc_dev_addDevConfirm4=Wskazówka: Urządzenie zostało pomyślnie podłączone, ale typ sterownika nie odpowiada rzeczywistemu, zostanie zmodyfikowany na All-in-One, kontynuować dodanie?
acc_dev_oneMachine=Urządzenie all-in-one
acc_dev_fingervein=Żyła palcowa
acc_dev_control=Sterownik
acc_dev_protocol=Rodzaj protokołu
acc_ownedBoard=Płyta rozszerzająca
#Obsługa urządzeń
acc_dev_start=Rozpoczęcie
acc_dev_accLevel=Upoważnienie kontroli dostępu
acc_dev_timeZoneAndHoliday=Okres czasu, święta
acc_dev_linkage=Powiązanie
acc_dev_doorOpt=Parametry bramy
acc_dev_firstPerson=Otwierania bramy przy pierwszej osoby
acc_dev_multiPerson=Wieloosobowe otwieranie bramy
acc_dev_interlock=Blokada interakcyjna
acc_dev_antiPassback=Przeciw powrotu
acc_dev_wiegandFmt=Format Wiegand
acc_dev_outRelaySet=Ustawienia wyjść pomocniczych
acc_dev_backgroundVerifyParam=Parametry weryfikacji za kulisami
acc_dev_getPersonInfoPrompt=Należy upewnić się, że udało się uzyskać informacje o danym personelu, w przeciwnym razie powstanie sytuację wyjątkową, kontynuować?
acc_dev_getEventSuccess=Skuteczne pobranie zdarzeń
acc_dev_getEventFail=Awaria uzyskania zdarzeń 
acc_dev_getInfoSuccess=Informacja zostanie uzyskana skutecznie
acc_dev_getInfoXSuccess=Skuteczne pobranie {0}
acc_dev_getInfoFail=Awaria uzyskania informacji
acc_dev_updateExtuserInfoFail=Awaria aktualizacji danych personelu, takich jak przedłużenie dostępu itp. Należy pobrać dane personelu ponownie.
acc_dev_getPersonCount=Ilość personelu pobranych
acc_dev_getFPCount=Ilość odcisków palców pobranych
acc_dev_getFVCount=Ilość żył palcowych pobranych
acc_dev_getFaceCount=Ilość twarzy pobranych
acc_dev_getPalmCount=Ilość odbitek dłoni pobranych
acc_dev_getBiophotoCount=Ilość zdjęć pobranych do porównywania
acc_dev_noData=Brak danych w urządzeniu
acc_dev_noNewData=Brak nowych zapisanych danych w urządzeniu
acc_dev_softLtDev=Liczba personelu w oprogramowaniu jest większa niż w urządzeniu.
acc_dev_personCount=Liczba personelu:
acc_dev_personDetail=Szczegóły są następujące.
acc_dev_softEqualDev=W oprogramowaniu i sprzęcie jest taka sama liczba personelu.
acc_dev_softGtDev=Liczba personelu w urządzeniu jest większa niż w oprogramowaniu.
acc_dev_cmdSendFail=Awaria wysyłania żądania, należy pobrać ponownie.
acc_dev_issueVerifyParam=Ustawienia parametrów uwierzytelniania za kulisami
acc_dev_verifyParamSuccess=Parametry uwierzytelniania za kulisami zostaną wysyłane skutecznie
acc_dev_backgroundVerify=Uwierzytelnianie za kulisami
acc_dev_selRightFile=Należy wybrać prawidłowy plik aktualizacyjny!
acc_dev_devNotOpForOffLine=Urządzenie jest odłączone, należy ponownie spróbować później!
acc_dev_devNotSupportFunction=Urządzenie nie obsługuje tej funkcji!
acc_dev_devNotOpForDisable=Urządzenie jest wyłączone, należy ponownie spróbować później!
acc_dev_devNotOpForNotOnline=Urządzenie jest odłączone lub wyłączone, należy ponownie spróbować później!
acc_dev_getPersonInfo=Uzyskanie informacji o personelu
acc_dev_getFPInfo=Uzyskanie informacji o odciskach palców
acc_dev_getFingerVeinInfo=Uzyskanie informacji o żyłach palcowych
acc_dev_getPalmInfo=Uzyskanie informacje o odbitkach dłoni
acc_dev_getBiophotoInfo=Uzyskanie informacji o twarzy widocznych
acc_dev_getIrisInfo=Uzyskaj informacje o tęczówce
acc_dev_disable=W stanie wyłączonym, należy wybrać ponownie!
acc_dev_offlineAndContinue=W stanie odłączonym, kontynuować operację?
acc_dev_offlineAndSelect=W stanie odłączonym.
acc_dev_opAllDev=Wszystkie urządzenia
acc_dev_opOnlineDev=Urządzenia podłączone
acc_dev_opException=Anomalia w trakcie wytwarzania
acc_dev_exceptionAndConfirm=Błąd połączenia do urządzenia, awaria obsługiwania, należy sprawdzić połączenie z Internetem!
acc_dev_getFaceInfo=Uzyskanie informacji o twarzy
acc_dev_selOpDevType=Należy wybrać typ urządzenia, które ma być obsługiwane.
acc_dev_hasFilterByFunc=Urządzenia, które są odłączone lub nie obsługują tej funkcji, zostaną przefiltrowane.
acc_dev_masterSlaveMode=Tryb nadrzędny/podrzędny RS485
acc_dev_master=Komputer nadrzędny
acc_dev_slave=Komputer podrzędny
acc_dev_modifyRS485Addr=Modyfikowanie adresu RS485
acc_dev_rs485AddrTip=Należy wprowadzić liczbę całkowitą 1-63!
acc_dev_enableFeature=Uwierzytelnianie za kulisami włączone
acc_dev_disableFeature=Uwierzytelnianie za kulisami wyłączone
acc_dev_getCountOnly=Tylko pobranie ilości
acc_dev_queryDevPersonCount=Określenie liczby personelu sprzętu
acc_dev_queryDevVolume=Określenie dostępność sprzętu
acc_dev_ruleType=Typ zasady
acc_dev_contenRule=Treść zasady
acc_dev_accessRules=Przeglądanie zasad kontroli dostępu w urządzeniach
acc_dev_ruleContentTip=Należy oddzielić pionowymi liniami (|) wiele zasad.
acc_dev_rs485AddrFigure=Schemat wybierania adresu RS485
acc_dev_addLevel=Dodanie do grupy uprawnień.
acc_dev_personOrFingerTanto=Zbyt za dużo osób lub odcisków palców, awaria synchronizacji zostanie postępowania...
acc_dev_personAndFingerUnit=(szt.)
acc_dev_setDstime=Ustawienia czasu letniego
acc_dev_setTimeZone=Ustawienia strefy czasowej urządzenia
acc_dev_selectedTZ=Strefa czasowa została wybrana
acc_dev_timeZoneSetting=W trakcie ustawiania strefy czasowej urządzenia...
acc_dev_timeZoneCmdSuccess=Polecenie ustawiania strefy czasowej urządzenia zostało pomyślnie wysłane...
acc_dev_enableDstime=Włączenie czasu letniego
acc_dev_disableDstime=Wyłączenie czasu letniego
acc_dev_timeZone=Strefa czasowa
acc_dev_dstSettingTip=W trakcie ustawiania czasu letniego
acc_dev_dstDelTip=W trakcie usuwania czasu letniego
acc_dev_enablingDst=W trakcie włączenia czasu letniego
acc_dev_dstEnableCmdSuccess=Żądanie włączenia czasu letniego urządzenia zostało pomyślnie wysłane.
acc_dev_disablingDst=W trakcie wyłączenia czasu letniego.
acc_dev_dstDisableCmdSuccess=Żądanie wyłączenia czasu letniego urządzenia zostało pomyślnie wysłane.
acc_dev_dstCmdSuccess=Żądanie ustawiania czasu letniego urządzenia zostało pomyślnie wysłane.
acc_dev_usadst=Czas letni
acc_dev_notSetDst=Nieustalony
acc_dev_selectedDst=Czas letni wybrany
acc_dev_configMasterSlave=Konfiguracja nadrzędna/podrzędna
acc_dev_hasFilterByUnOnline=Urządzenia odłączone zostaną przefiltrowane.
acc_dev_softwareData=W przypadku stwierdzenia niezgodności pomiędzy danymi oprogramowania i urządzenia, przed wykonaniem wyszukiwaniem należy zsynchronizować ich dane!
acc_dev_disabled=Następujące urządzenia są wyłączone i nie mogą być obsługiwane!
acc_dev_offline=Następujące urządzenia są odłączone i nie mogą być obsługiwane!
acc_dev_noSupport=Następujące urządzenia nie obsługują tej funkcji i nie mogą być obsługiwane!
acc_dev_noRegDevTip=Pobieranie danych z niezarejestrowanych urządzeń nadpisuje dane w oprogramowaniu, czy na pewno kontynuować?
acc_dev_noOption=Nie ma żadnych opcji kwalifikujących się.
acc_dev_devFWUpdatePrompt=Dodane urządzenie nie może normalnie korzystać z funkcji czarnej listy i daty ważności personelu w systemie (szczegółowe informacje znajdują się w instrukcji obsługi).
acc_dev_panelFWUpdatePrompt=Dodane urządzenie nie może normalnie korzystać z funkcji czarnej listy i daty ważności personelu w systemie, czy należy natychmiast uaktualnić oprogramowanie układowe?
acc_dev_sendEventCmdSuccess=Żądanie usunięcia zdarzeń zostało pomyślnie wysłane
acc_dev_tryAgain=Należy spróbować ponownie
acc_dev_eventAutoCheckAndUpload=Automatyczne sprawdzanie i pobieranie zapisów
acc_dev_eventUploadStart=Rozpoczęcie pozyskiwania zapisów zdarzeń urządzenia
acc_dev_eventUploadEnd=Zakończenie pozyskiwania zapisów zdarzeń urządzenia
acc_dev_eventUploadFailed=Awaria pozyskiwania zapisów zdarzeń urządzenia
acc_dev_eventUploadPrompt=Urządzenie z niską wersją oprogramowania układowego jest wykrywane, a przed aktualizacją oprogramowania układowego, należy wybrać z opcji poniżej:
acc_dev_backupToSoftware=Tworzenie kopii zapasowych danych do oprogramowania
acc_dev_deleteEvent=Usuwanie zapisów zdarzeń z urządzenia
acc_dev_upgradePrompt=Wersja oprogramowania układowego jest zbyt za niska, aktualizacja może spowodować zakłócenia w zapisie zdarzeń, więc najpierw można wykonać kopię zapasową danych do oprogramowania.
acc_dev_conflictCardNo=Istnieje w systemie inny personel z numerem karty jako {0}!
acc_dev_rebootAfterOperate=Operacja zakończyła się pomyślnie i urządzenie zostanie zrestartowane w późniejszym czasie.
acc_dev_baseOptionTip=Anomalia pobrania parametrów bazowych
acc_dev_funOptionTip=Anomalia pobrania parametrów funkcji
acc_dev_sendComandoTip=Żądanie uzyskiwania parametrów urządzenia nie zostało wysłane skutecznie.
acc_dev_noC3LicenseTip=Nie można dodać tego typu urządzenia ({0}). Aby kontynuować bieżącą operację, należy skontaktować się ze sprzedawcą!
acc_dev_combOpenDoorTip=({0}) jest ustationa na wieloosobowe otwieranie bramy i nie można jednocześnie korzystać z funkcji weryfikacji za kulisami!
acc_dev_combOpenDoorPersonCountTip=Grupa {0} nie może otworzyć więcej niż {1}!
acc_dev_addDevTip=Ta operacja dotyczy tylko urządzeń z dodanymi protokołami komunikacyjnymi PULL!
acc_dev_addError=Anomalia dodania urządzenia, brak parametrów ({0})!
acc_dev_updateIPAndPortError=Awaria aktualizowanie IP i portu serwera
acc_dev_transferFilesTip=Oprogramowanie układowe sprawdza się pomyślnie, należy wysłać plik.
acc_dev_serialPortExist=Port szeregowy (obliczanie) obecny
acc_dev_isExist=Czy urządzenie istnieje
acc_dev_description=Opis
acc_dev_searchEthernet=Wyszukiwanie urządzeń Ethernet
acc_dev_searchRS485=Wyszukiwanie urządzeń RS485
acc_dev_rs485AddrTip1=Adres początkowy RS485 nie może być większy niż adres końcowy.
acc_dev_rs485AddrTip2=Zakres wyszukiwania RS485 musi się mieścić w granicach 20
acc_dev_clearAllCmdCache=Kasowanie wszystkich żądań
acc_dev_authorizedSuccessful=Skuteczne autoryzowanie
acc_dev_authorize=Autoryzowanie
acc_dev_registrationDevice=Urządzenie rejestrujące
acc_dev_setRegistrationDevice=Ustawienia urządzenia rejestrującego
acc_dev_mismatchedDevice=Podłączenie urządzenia nie powiodło się, przyczyna: nieprawidłowy numer seryjny urządzenia!
acc_dev_pwdStartWithZero=Kody komunikacyjne nie zaczynają się od zera!
acc_dev_maybeDisabled=Aktualna licencja pozwala na dodanie {0} bram kolejnych, a bramy, które przekroczą limit punktów licencyjnych w nowo dodanym urządzeniu zostaną wyłączone, nadal kontynuować?
acc_dev_Limit=Maksymalna liczba punktów licencyjnych w systemie została osiągnięta, w przypadku konieczności dodania kolejnych, należy dokonać autoryzacji.
acc_dev_selectDev=Należy wybrać urządzenie!
acc_dev_cannotAddPullDevice=Nie wolno dodawać urządzeń PULL! Aby kontynuować bieżącą operację, należy skontaktować się ze sprzedawcą!
acc_dev_notContinueAddPullDevice=W systemie znajduje się już {0} urządzenie PULL, jego dodanie jest niedozwolone! Aby kontynuować bieżącą operację, należy skontaktować się ze sprzedawcą!
acc_dev_deviceNameNull=Model urządzenia jest pusty, nie można dodać żadnych urządzeń!
acc_dev_commTypeErr=Niedopasowany sposób komunikacji urządzenia, jego dodanie jest niedozwolone!
acc_dev_inputDomainError=Należy wprowadzić prawidłowy format adresu domeny!
acc_dev_levelTip=Liczba personelu w grupie uprawnień jest większa niż 5000 i nie można automatycznie dodać grupy do urządzenia!
acc_dev_auxinSet=Ustawiania wejść pomocniczych
acc_dev_verifyModeRule=Zasady metody uwierzytelniania
acc_dev_netModeWired=Okablowany
acc_dev_netMode4G=4G
acc_dev_netModeWifi=Wifi
acc_dev_updateNetConnectMode=Przełączanie połączenia z Internetem
acc_dev_wirelessSSID=Bezprzewodowy SSID
acc_dev_wirelessKey=Klucz tajny bezprzewodowy
acc_dev_searchWifi=Wyszukiwanie list bezprzewodowych
acc_dev_testNetConnectSuccess=Połączenie komunikacyjne jest udane, czy potwierdzić przełączanie?
acc_dev_testNetConnectFailed=Obecne połączenie nie komunikuje się prawidłowo!
acc_dev_signalIntensity=Natężenie sygnału
acc_dev_resetSearch=Wyszukiwanie ponownie
acc_dev_addChildDevice=Autoryzowanie urządzenia podrzędnego
acc_dev_modParentDevice=Zmiana urządzenia głównego
acc_dev_configParentDevice=Konfiguracja urządzenia głównego
acc_dev_lookUpChildDevice=Przeglądanie urządzenia podrzędnego
acc_dev_addChildDeviceTip=Autoryzacja jest wymagana na podstawie autoryzowanego urządzenia podrzędnego
acc_dev_maxSubCount=Liczba autoryzowanych urządzeń podrzędnych przekracza maksymalną liczbę {0} dostępów.
acc_dev_seletParentDevice=Należy wybrać urządzenie główne!
acc_dev_networkCard=Karta sieciowa
acc_dev_issueParam=Przystosowywanie parametrów wysłania
acc_dev_issueMode=Tryb wysłania
acc_dev_initIssue=Dane wysłania inicjalizacja oprogramowania układowego 3030
acc_dev_customIssue=Przystosowywanie danych wysłania
acc_dev_issueData=Dane
acc_dev_parent=Główne urządzenie
acc_dev_parentEnable=Urządzenie główne jest wyłączone
acc_dev_parentTips=Wiązanie urządzenia nadrzędnego spowoduje usunięcie wszystkich istniejących danych w urządzeniu i należy go konfigurować ponownie.
acc_dev_addDevIpTip=Nowy adres IP nie może być taki sam jak adres IP serwera.
acc_dev_modifyDevIpTip=Adres nowego serwera nie może być taki sam jak adres IP urządzenia.
acc_dev_setWGReader=Ustawienia głowicy odczytu Wiegand
acc_dev_selectReader=Kliknąć, aby wybrać głowicę odczytu
acc_dev_IllegalDevice=Niedozwolone urządzenie
acc_dev_syncTimeWarnTip=Czas synchronizowany następujących urządzeń musi być zsynchronizowany na urządzeniu nadrzędnym.
acc_dev_setTimeZoneWarnTip=Strefy czasowe następujących urządzeń muszą być zsynchronizowane na urządzeniu nadrzędnym.
acc_dev_setDstimeWarnTip=Czas letni następujących urządzeń musi być zsynchronizowany na urządzeniu nadrzędnym.
acc_dev_networkSegmentSame=Dwie karty NIC nie mogą korzystać z tego samego segmentu sieci.
acc_dev_upgradeProtocolNoMatch=Niedopasowanie protokołu aktualizacji plików
acc_dev_ipAddressConflict=Już istnieje urządzenie z tym samym adresem IP, należy zmodyfikować adres IP urządzenia i dodać go ponownie.
acc_dev_checkServerPortTip=Ustawiony port serwera nie jest tożsamy z portem komunikacyjnym systemu, co może prowadzić do nieudanego dodawania urządzenia, czy należy kontynuować obsługiwanie?
acc_dev_clearAdmin=Kasowanie administratora
acc_dev_setDevSate=Ustawienia stanu dostępu maszyny
acc_dev_sureToClear=Czy na pewno wykonać operację kasowania administratora?
acc_dev_hostState=Stan urządzenia nadrzędnego
acc_dev_regDeviceTypeTip=Jest to urządzenie sterowane, a bezpośrednie jego dodania są niedozwolone, należy skontaktować się ze sprzedawcą oprogramowania!
acc_dev_extBoardType=Typ płyty rozszerzającej
acc_dev_extBoardTip=Aby urządzenie zaczęło działać, należy go zrestartować po zakończeniu konfiguracji!
acc_dev_extBoardLimit=Do każdego urządzenia może być dodane tylko {0} płytek rozszerzających tego typu!
acc_dev_replace=Wymień urządzenie
acc_dev_replaceTip=Po wymianie stare urządzenie nie będzie działać, bądź ostrożny!
acc_dev_replaceTip1=Po wymianie wykonaj operację "synchronizuj wszystkie dane";
acc_dev_replaceTip2=Upewnij się, że nowy model urządzenia jest taki sam!
acc_dev_replaceTip3=Upewnij się, że urządzenie zastępcze ma ustawiony ten sam adres serwera i port, co stare urządzenie!
acc_dev_replaceFail=Typ urządzenia jest niespójny i nie można go zastąpić!
acc_dev_notApb=To urządzenie nie jest w stanie wykonać operacji przeciwko łodzi podwodnej lub bramie
acc_dev_upResourceFile=Przesyłaj pliki zasobów
acc_dev_playOrder=Kolejność odtwarzania
acc_dev_setFaceServerInfo=Ustaw parametry porównywania backendu twarzy
acc_dev_faceVerifyMode=Tryb porównywania twarzy
acc_dev_faceVerifyMode1=Porównanie lokalne
acc_dev_faceVerifyMode2=Porównanie backend
acc_dev_faceVerifyMode3=Lokalny priorytet porównania
acc_dev_faceBgServerType=Typ serwera obsługi twarzy
acc_dev_faceBgServerType1=Usługi platformy oprogramowania
acc_dev_faceBgServerType2=Usługi stron trzecich
acc_dev_isAccessLogic=Włącz weryfikację logiki kontroli dostępu
#[5] Bramy - Inne związane z tym bramy są tu również zwielokrotnione
acc_door_entity=Bramy
acc_door_number=Numer bramy
acc_door_name=Nazwa bramy
acc_door_activeTimeZone=Ważny przedział czasowy bramy
acc_door_passageModeTimeZone=Przedział czasowy otwarcia normalnego bramy
acc_door_setPassageModeTimeZone=Przedział czasowy otwarcia normalnego bramy został ustalony
acc_door_notPassageModeTimeZone=Przedział czasowy otwarcia normalnego bramy nie został ustalony
acc_door_lockOpenDuration=Czas trwania napędu blokady
acc_door_entranceApbDuration=Czas trwania wejścia przeciw powrotu
acc_door_sensor=Magnes bramy
acc_door_sensorType=Typ magnesu bramy
acc_door_normalOpen=Otwarcie normalne 
acc_door_normalClose=Zamknięcie normalne
acc_door_sensorDelay=Opóźnienie czasu magnesu bramy
acc_door_closeAndReverseState=Wyłączenie przekaźnika elektrycznego natychmiast po zamknięciu bramy
acc_door_hostOutState=Status dostępu urządzenia głównego
acc_door_slaveOutState=Status dostępu urządzenia podrzędnego
acc_door_inState=Wejście 
acc_door_outState=Wyjście 
acc_door_requestToExit=Status przyciska wyjściowego
acc_door_withoutUnlock=Blokowanie
acc_door_unlocking=Nieblokowanie
acc_door_alarmDelay=Opóźnienie czasu przyciska wyjściowego
acc_door_duressPassword=Hasło przymusu
acc_door_currentDoor=Aktualna brama
acc_door_allDoorOfCurDev=Wszystkie bramy aktualnych urządzeń
acc_door_allDoorOfAllDev=Wszystkie bramy wszystkich urządzeń
acc_door_allDoorOfAllControlDev=Wszystkie bramy wszystkich sterowników
acc_door_allDoorOfAllStandaloneDev=Wszystkie bramy wszystkich urządzeń all-in-one
acc_door_allWirelessLock=Wszystkie blokady bezprzewodowe
acc_door_max6BitInteger=Maksymalnie 6 liczb całkowitych
acc_door_direction=Kierunek wejścia i wyjścia
acc_door_onlyInReader=Wyłączna głowica odczytu do wejścia
acc_door_bothInAndOutReader=Głowica odczytu do wejścia i wyjścia
acc_door_noDoor=Należy dodać bramy
acc_door_nameRepeat=Powtarzalna nazwa bramy
acc_door_duressPwdError=Hasło przymusu nie może być takie same jak dowolne hasło z personelu.
acc_door_urgencyStatePwd=Należy podać {0} liczb całkowitych!
acc_door_noDevOnline=Nie istnieje żadne urządzenie podłączone lub brama nie obsługuje metod uwierzytelniania kart!
acc_door_durationLessLock=Czas opóźnienia magnesu bramy musi być dłuższy niż czas trwania napędu blokady!
acc_door_lockMoreDuration=Czas trwania napędu blokady musi być krótszy niż czas opóźnienia magnesu bramy!
acc_door_lockAndExtLessDuration=Czas trwania napędu blokady plus wydłużony czas ruchu musi być mniejszy niż czas opóźnienia magnesu bramy!
acc_door_noDevTrigger=Nie istnieje żaden sprzęt kwalifikacyjny!
acc_door_relay=Przekaźnik elektryczny
acc_door_pin=Numer pracownika
acc_door_selDoor=Wybieranie bramy
acc_door_sensorStatus=Magnesy bramy ({0})
acc_door_sensorDelaySeconds=Czas opóźnienia ({0} sekund)
acc_door_timeSeg=Przedział czasowy ({0})
acc_door_combOpenInterval=Odstęp czasu pomiędzy otwarciami bramy wieloosobowo
acc_door_delayOpenTime=Opóźnienie czas otwarcia
acc_door_extDelayDrivertime=Wydłużenie godziny dostępu
acc_door_enableAudio=Włączanie alarmów
acc_door_disableAudio=Wyłączanie alarmów
acc_door_lockAndExtDelayTip=Suma czasu trwania napędu blokady i wydłużonego czasu ruchu nie może być większa niż 254 sekundy.
acc_door_disabled=Następujące bramy są wyłączone i nie mogą być obsługiwane!
acc_door_offline=Następujące bramy są odłączone i nie mogą być obsługiwane!
acc_door_notSupport=Następujące bramy nie obsługują tej funkcji i nie mogą być obsługiwane!
acc_door_select=Należy wybrać bramę
acc_door_pushMaxCount=W systemie znajduje się {0} bram właczonych, osiągających maksymalną liczbę punktów licencyjnych. Aby kontynuować bieżącą operację, należy skontaktować się ze sprzedawcą!
acc_door_outNumber=Aktualna licencja może włączyć tylko {0} bram! Należy ponownie wybrać bramę lub skontaktować się ze sprzedawcą w celu zakupu odnowionej licencji.
acc_door_latchTimeZone=Uzasadniony przedział czasowy przyciska wyjściowego
acc_door_wgFmtReverse=Odwrócenie pozycji numeru karty
acc_door_allowSUAccessLock=Umożliwienie przejścia super użytkownikom przy zablokowanych bramach.
acc_door_verifyModeSinglePwd=Hasła nie mogą być używane jako niezależna metoda weryfikacji!
acc_door_doorPassword=Hasło otwierania drzwi
#Wejście pomocnicze
acc_auxIn_timeZone=Uzasadniony przedział czasowy
#wyjście pomocnicze
acc_auxOut_passageModeTimeZone=Przedział czasowy otwarcia normalnego
acc_auxOut_disabled=Następujące wyjścia pomocnicze są wyłączone i nie mogą być obsługiwane!
acc_auxOut_offline=Następujące wyjścia pomocnicze są odłączone i nie mogą być obsługiwane!
#[8] Grupa uprawnień kontroli dostępu
acc_level_doorGroup=Kombinacja bramy
acc_level_openingPersonnel=Personal do otwierania bramy
acc_level_noDoor=Nie ma żadnych opcji do wyboru, należy najpierw dodać urządzenie!
acc_level_doorRequired=Należy obowiązkowo wybrać bramę!
acc_level_doorCount=Liczba bram
acc_level_doorDelete=Usunięcie bramy
acc_level_isAddDoor=Natychmiast dodać bramy do nowo dodanej grupy uprawnień?
acc_level_master=Grupa uprawnień ogólnych
acc_level_noneSelect=Należy dodać grupę uprawnień
acc_level_useDefaultLevel=Czy przełącza na uprawnienia dostępu obecnego działu?
acc_level_persAccSet=Ustawienia dostępu personelu
acc_level_visUsed={0} już jest używana przez moduł zwiedzający i nie może być usunięta!
acc_level_doorControl=Sterowanie bramą
acc_level_personExceedMax=Liczba aktualnych grup dyskusyjnych ({0}), oraz maksymalna liczba opcjonalnych grup dyskusyjnych ({1})
acc_level_exportLevel=Eksportuj poziom dostępu
acc_level_exportLevelDoor=Eksportuj drzwi poziomu dostępu
acc_level_exportLevelPerson=Eksportuj personel o poziomie dostępu
acc_level_importLevel=Importuj poziom dostępu
acc_level_importLevelDoor=Importuj drzwi poziomu dostępu
acc_level_importLevelPerson=Importuj personel o poziomie dostępu
acc_level_exportDoorFileName=Drzwi Informacje o poziomie dostępu
acc_level_exportPersonFileName=Informacje osobowe o poziomie dostępu
acc_levelImport_nameNotNull=Nazwa poziomu dostępu nie może być pusta
acc_levelImport_timeSegNameNotNull=Strefa czasowa nie może być pusta
acc_levelImport_areaNotExist=Obszar nie istnieje!
acc_levelImport_timeSegNotExist=Strefa czasowa nie istnieje!
acc_levelImport_nameExist=Nazwa poziomu dostępu {0} już istnieje!
acc_levelImport_levelDoorExist=Drzwi poziomu dostępu {0} już istnieją!
acc_levelImport_levelPersonExist=Personel poziomu dostępu {0} już istnieje!
acc_levelImport_noSpecialChar=Nazwa poziomu dostępu nie może zawierać znaków specjalnych!
#[10] Otwarcie normalne przy pierwszej osobie
acc_firstOpen_setting=Ustawienia otwarcia normalnego przy pierwszej osobie
acc_firstOpen_browsePerson=Przeglądanie personelu otwierających bram
#[11] Otwarcie bramy przez grupę połączeniowej wieloosobowej
acc_combOpen_comboName=Nazwa grupy połączeniowej
acc_combOpen_personGroupName=Nazwa grupy
acc_combOpen_personGroup=Grupa personelu do wieloosobowego otwierania bramy
acc_combOpen_verifyOneTime=Ilość osób do uwierzytelniania jednocześnie
acc_combOpen_eachGroupCount=Ilość osób do otwarcia bramy w grupach
acc_combOpen_group=Grupa
acc_combOpen_changeLevel=Grupa personelu otwarcia bramy
acc_combOpen_combDeleteGroup=Istnieje odniesienie do wieloosobowego otwarcia bramy, należy najpierw taką zasadę skasować!
acc_combOpen_ownedLevel=Grupa uprawnień 
acc_combOpen_mostPersonCount=Maksymalna ilość osób połączeniowych nie przekroczy 5!
acc_combOpen_leastPersonCount=Minimalna ilość osób otwierających bramy nie mniejsza niż 2!
acc_combOpen_groupNameRepeat=Powtarzalna nazwa grupy połączeniowej!
acc_combOpen_groupNotUnique=Nie wolno ustawić tej samej grupy otwierającej bramę!
acc_combOpen_persNumErr=Liczba osób w wybranej grupie przekracza rzeczywistą ilość, należy wybrać ponownie!
acc_combOpen_combOpengGroupPersonShort=Po usunięciu danego personelu, w grupie otwierającej bramę nie ma wystarczającej ilości osób do otwierania bramy, należy najpierw usunąć tę grupę!
acc_combOpen_backgroundVerifyTip=Urządzenie, do którego należą bramy, ma włączoną autoryzację za kulisami i nie może być używane z zasadą wieloosobowego otwierania bramy!
#[12] Blokada interakcyjna
acc_interlock_rule=Zasada blokady interakcyjnej
acc_interlock_mode1Or2=Blokada interakcyjna między {0} a {1}
acc_interlock_mode3=Blokada interakcyjna pomiędzy {0}, {1} i {2}
acc_interlock_mode4=Blokada interakcyjna między {0} a {1}, Blokada interakcyjna między {2} a {3}
acc_interlock_mode5=Blokada interakcyjna pomiędzy {0}, {1}, {2} i {3}
acc_interlock_hasBeenSet=Blokada interakcyjna zostanie ustalona
acc_interlock_group1=Grupa 1
acc_interlock_group2=Grupa 2
acc_interlock_ruleInfo=Połączenie między grupami
acc_interlock_alreadyExists=Ta sama reguła blokowania już istnieje, proszę nie dodawać jej ponownie!
acc_interlock_groupInterlockCountErr=Zasada blokowania wewnątrz grupy wymaga co najmniej dwóch drzwi
acc_interlock_ruleType=Typ reguły blokowania
#[13] Przeciw powrotu
acc_apb_rules=Zasada przeciw powrotu
acc_apb_reader=Przeciw powrotu między główkami odczytu{0}
acc_apb_reader2=Jednoczesny przeciw powrotu między główkami odczytu {0}, {1}
acc_apb_reader3=Jednoczesny przeciw powrotu między główkami odczytu {0}, {1} i {2}
acc_apb_reader4=Jednoczesny przeciw powrotu między główkami odczytu {0}, {1}, {2} i {3}
acc_apb_reader5=Przeciw powrotu głowicy odczytu {0} wychodzenia
acc_apb_reader6=Przeciw powrotu głowicy odczytu {0}  wejścia
acc_apb_reader7=Przeciw powrotu między czteroma dowolnymi bramki
acc_apb_twoDoor=Przeciw powrotu między {0} a {1}
acc_apb_fourDoor=Przeciw powrotu między {0} a {1} Przeciw powrotu między {2} a {3}
acc_apb_fourDoor2=Przeciw powrotu między {0} lub {1} a {2} lub {3}
acc_apb_fourDoor3=Przeciw powrotu między {0} a {1} lub {2}
acc_apb_fourDoor4=Przeciw powrotu między {0} a {1} lub {2} lub {3}
acc_apb_hasBeenSet=Przeciw powrotu zostanie ustalony
acc_apb_conflictWithGapb=Przeciw powrotu zostanie ustalony na tym urządzeniu, nie wolno ustalać tej samej zasady ponownie!
acc_apb_conflictWithApb=Przeciw powrotu zostanie ustalony na urządzeniach w obecnej strefie, nie wolno ustalać zasady przeciw powrotu na poziomie całego systemu!
acc_apb_conflictWithEntranceApb=Przeciw powrotu przy wejściu zostanie ustalony w urządzeniach na obecnej strefie, nie wolno ustalać zasady przeciw powrotu na poziomie całego systemu!
acc_apb_controlIn=Przeciw powrotu przy wejściu
acc_apb_controlOut=Przeciw powrotu przy wyjściu
acc_apb_controlInOut=Przeciw powrotu przy wejściu i wyjściu
acc_apb_groupIn=Dołącz do grupy
acc_apb_groupOut=Poza grupą
acc_apb_reverseName=Odwrotna łódź podwodna
acc_apb_door=Brama przeciwko łodzi podwodnej
acc_apb_readerHead=Czytanie głowy, by przeciwdziałać łodzi podwodnej
acc_apb_alreadyExists=Ta sama zasada anty-podwodna już istnieje, proszę nie dodawać jej ponownie!
#[17] Mapa elektroniczna
acc_map_addDoor=Dodanie bramy
acc_map_addChannel=Dodanie kamery
acc_map_noAccess=Brak dostępu do modułu mapy elektronicznej, należy skontaktować się z administratorem!
acc_map_noAreaAccess=Brak dostępu do mapy elektronicznej dla tego obszaru, należy skontaktować się z administratorem!
acc_map_imgSizeError=Prześlij zdjęcie, którego rozmiar jest mniejszy niż {0} M!
#[18] Zapis zdarzeń związanych z kontrolą dostępu
acc_trans_entity=Zapis zdarzeń związanych z kontrolą dostępu
acc_trans_eventType=Typ zdarzenia
acc_trans_firmwareEvent=zdarzenie związane z oprogramowaniem układowym (obliczanie)
acc_trans_softwareEvent=zdarzenie związane z oprogramowaniem
acc_trans_today=Dzisiejsza historia dostępu
acc_trans_lastAddr=Ostatnia lokalizacja dostępu personelu
acc_trans_viewPhotos=Przeglądanie zdjęć
acc_trans_exportPhoto=Eksportuj zdjęcia
acc_trans_dayNumber=Dni
acc_trans_photo=Zdjęcie incydentu kontroli dostępu
acc_trans_fileIsTooLarge=Wyeksportowany plik jest za duży, zmniejsz zakres eksportu
#[19] Metody uwierzytelniania kontroli dostępu
acc_verify_mode_onlyface=Twarz
acc_verify_mode_facefp=Twarz+odciski palców
acc_verify_mode_facepwd=Twarz+hasło
acc_verify_mode_facecard=Twarz+karta
acc_verify_mode_facefpcard=Twarz+odciski palców+karta
acc_verify_mode_facefppwd=Twarz+odciski palców+hasło
acc_verify_mode_fv=Żyła palcowa
acc_verify_mode_fvpwd=Żyła palcowa+hasło
acc_verify_mode_fvcard=Żyła palcowa+karta
acc_verify_mode_fvpwdcard=Żyła palcowa+hasło+karta
acc_verify_mode_pv=Palma
acc_verify_mode_pvcard=Palma+karta
acc_verify_mode_pvface=Palma+twarz
acc_verify_mode_pvfp=Palma+odcisk palców
acc_verify_mode_pvfacefp=Palma+twarz+odcisk palców
#[20] Numer zdarzenia kontroli dostępu
acc_eventNo_-1=Brak
acc_eventNo_0=Otwarcia bramy odczytywaniem karty normalnie
acc_eventNo_1=Otwarcie bramy odczytywaniem karty podczas otwarcia normalnego
acc_eventNo_2=Otwarcie przy pierwszej osobie (odczytywaniem karty)
acc_eventNo_3=Otwarcie wieloosobowe (odczytywaniem karty)
acc_eventNo_4=Otwarcie hasłem w przypadku awaryjnym
acc_eventNo_5=Otwarcie podczas otwarcia normalnego
acc_eventNo_6=Zdarzenie związane z powiązaniem wyzwalającym
acc_eventNo_7=Anulowanie alarmu
acc_eventNo_8=Zdalne otwieranie bramy.
acc_eventNo_9=Zdalne zamykanie bramy.
acc_eventNo_10=wyłączenie korzystania z czasu otwarcia normalnego danego dnia
acc_eventNo_11=Start korzystania z czasu otwarcia normalnego danego dnia
acc_eventNo_12=Zdalne włączanie wyjścia pomocniczego
acc_eventNo_13=Zdalne wyłączanie wyjścia pomocniczego
acc_eventNo_14=Normalne otwieranie bramy naciskiem palców
acc_eventNo_15=Wieloosobowe twieranie bramy (naciskiem palców)
acc_eventNo_16=Otwieranie bramy naciskiem palców podczas otwarcia normalnego
acc_eventNo_17=Otwieranie bramy odczytywaniem karty i naciskiem palców
acc_eventNo_18=Otwieranie bramy przy pierwszej osobie (naciskiem palców)
acc_eventNo_19=Otwieranie bramy przy pierwszej osobie (odczytywaniem karty i naciskiem palców)
acc_eventNo_20=Odstęp czasu między operacjami jest zbyt krótki
acc_eventNo_21=Brama w przedziale czasowym nieuzasadnionym (odczytywaniem karty)
acc_eventNo_22=Nieupoważniony przedział czasowy
acc_eventNo_23=Odczyt nieupoważniony
acc_eventNo_24=Przeciw powrotu
acc_eventNo_25=Blokada interakcyjna
acc_eventNo_26=Uwierzytelnianie wieloosobowe (odczytywaniem karty)
acc_eventNo_27=Karta niezarejestrowana
acc_eventNo_28=Brama otwiera się nadprogramowo
acc_eventNo_29=Karta wygasła.
acc_eventNo_30=Błąd hasła
acc_eventNo_31=Odstęp czasu między odciskami palców jest zbyt krótki.
acc_eventNo_32=Uwierzytelnianie wieloosobowe (odciskiem palców)
acc_eventNo_33=Odciski palców wygasły.
acc_eventNo_34=Odciski palców niezarejestrowane
acc_eventNo_35=Brama w przedziale czasowym nieuzasadnionym (odciskiem palców)
acc_eventNo_36=Brama w przedziale czasowym nieuzasadnionym (wcisnąć przycisk bramy)
acc_eventNo_37=Brama nie może być zamknięta w czasach otwarcia normalnego
acc_eventNo_38=Karta przepadła.
acc_eventNo_39=Dostęp wyłączony
acc_eventNo_40=Awaria uwierzytelniania wieloosobowego (odciskiem palców)
acc_eventNo_41=Błędna metoda uwierzytelniania
acc_eventNo_42=Błąd formatu Wiegand
acc_eventNo_43=Uwierzytelnianie przeciw powrotu nadprogramowo
acc_eventNo_44=Awaria uwierzytelniania za kulisami
acc_eventNo_45=Uwierzytelnianie za kulisami nadprogramowo
acc_eventNo_47=Awaria wysłania zlecenia
acc_eventNo_48=Awaria uwierzytelniania wieloosobowego (odczytywaniem karty)
acc_eventNo_49=Brama w przedziale czasowym nieuzasadnionym (hasłem)
acc_eventNo_50=Odstęp czasu między wprowadzaniami hasła jest zbyt krótki
acc_eventNo_51=Uwierzytelnianie wieloosobowe (hasłem)
acc_eventNo_52=Awaria uwierzytelniania wieloosobowego (hasłem)
acc_eventNo_53=Hasło nieważne
acc_eventNo_100=Alarm antysabotażowy
acc_eventNo_101=Otwieranie bramy hasłem przymusu
acc_eventNo_102=Brama została przypadkowo otwarta.
acc_eventNo_103=Otwieranie bramy naciskiem przymusu
acc_eventNo_200=Brama jest otwarta.
acc_eventNo_201=Brama jest zamknięta.
acc_eventNo_202=Otwieranie bramy naciskiem wyjściowym
acc_eventNo_203=Otwieranie wieloosobowe (odczytywaniem karty plus naciskiem palców)
acc_eventNo_204=Koniec okresu czasu otwarcia normalnego
acc_eventNo_205=Zdalne otwieranie włączone normalnie
acc_eventNo_206=Uruchomienie urządzenia
acc_eventNo_207=Otwieranie bramy hasłem
acc_eventNo_208=Otwieranie bramy przez super użytkownika
acc_eventNo_209=Wyzwalanie przycisku wychodzenia (zablokowane)
acc_eventNo_210=Aktywacja wyłącznika przeciwpożarowego.
acc_eventNo_211=Zamknięcie przez super użytkownika
acc_eventNo_212=Aktywowanie funkcji sterowania windą.
acc_eventNo_213=Wyłączenie funkcji sterowania windą
acc_eventNo_214=Otwieranie bramy wieloosobowo (hasłem)
acc_eventNo_215=Otwieranie przy pierwszej osobie (hasłem)
acc_eventNo_216=Wpis hasła w przedziale czasowym otwarcia normalnego
acc_eventNo_220=Odłączenie punktu wejściowego pomocniczego 
acc_eventNo_221=Zwarcie punktu wejściowego pomocniczego
acc_eventNo_222=Skuteczne uwierzytelnianie za kulisami
acc_eventNo_223=Uwierzytelnianie za kulisami
acc_eventNo_225=Normalny punkt wejściowy pomocniczy
acc_eventNo_226=Wyzwalanie punktu wejściowego pomocniczego
acc_newEventNo_0=Otwieranie bramy normalną weryfikacją
acc_newEventNo_1=Uwierzytelnianie w przedziale czasowym otwarcia normalnym
acc_newEventNo_2=Otwierania bramy przy pierwszej osoby
acc_newEventNo_3=Wieloosobowe otwieranie bramy
acc_newEventNo_20=Odstęp czasu między operacjami jest zbyt krótki
acc_newEventNo_21=Otwieranie bramy w przedziale czasowym nieuzasadnionym
acc_newEventNo_26=Oczekiwanie uwierzytelniania wielu osób
acc_newEventNo_27=Personel niezarejestrowany
acc_newEventNo_29=Personel nieuzasadniony
acc_newEventNo_30=Błąd hasła
acc_newEventNo_41=Błędna metoda uwierzytelniania
acc_newEventNo_43=Blokada personelu
acc_newEventNo_44=Awaria uwierzytelniania za kulisami
acc_newEventNo_45=Uwierzytelnianie za kulisami nadprogramowo
acc_newEventNo_48=Awaria uwierzytelniania wieloosobowego
acc_newEventNo_54=Zbyt niskie napięcie baterii
acc_newEventNo_55=Należy natychmiast wymienić baterię
acc_newEventNo_56=Nieupoważnione obsługiwanie
acc_newEventNo_57=Moc rezerwowa
acc_newEventNo_58=Alarm otwarcia normalnego
acc_newEventNo_59=Zarządzenie nieupoważnione
acc_newEventNo_60=Brama została zablokowana w środku
acc_newEventNo_61=Powtórzenie uwierzytelniania
acc_newEventNo_62=Użytkownicy zablokowani
acc_newEventNo_63=Brama została zablokowana
acc_newEventNo_64=Działanie przyciska wyjściowego nie w przedziale czasowym uzasadnionym
acc_newEventNo_65=Działanie wyjścia pomocniczego nie w przedziale czasowym uzasadnionym
acc_newEventNo_66=Awaria aktualizacji głowicy odczytu
acc_newEventNo_67=Zdalne porównanie powiodło się (urządzenie nie jest autoryzowane)
acc_newEventNo_68=Wysoka temperatura ciała - odmowa dostępu
acc_newEventNo_69=Bez maski - Odmowa dostępu
acc_newEventNo_70=Wyjątek komunikacji serwera porównania twarzy
acc_newEventNo_71=Serwer twarzy odpowiada nieprawidłowo
acc_newEventNo_73=Nieprawidłowy kod QR
acc_newEventNo_74=Kod QR wygasł
acc_newEventNo_101=Alarm otwierania przymusu bramy
acc_newEventNo_104=Alarm otwierania bramy odczytywaniem karty nieuzasadnionej
acc_newEventNo_105=Brak możliwości podłączenia do serwera
acc_newEventNo_106=Awaria zasilania sieciowego
acc_newEventNo_107=Spadek poziomu baterii
acc_newEventNo_108=Brak możliwości podłączenia do głównego urządzenia
acc_newEventNo_109=Alarm antysabotażowy głowicy odczytu
acc_newEventNo_110=Głowica odczytu odłączona
acc_newEventNo_112=Płyta rozszerzająca offline
acc_newEventNo_114=Wejście alarmu pożarowego odłączone (wykrywanie linii)
acc_newEventNo_115=zwarcie wejściowe alarmu pożarowego (wykrywanie linii)
acc_newEventNo_116=Wejście pomocnicze odłączone (wykrywanie linii)
acc_newEventNo_117=Zwarcie wejściowe pomocnicze (wykrywanie linii)
acc_newEventNo_118=Przełącznik wyjścia odłączony (wykrywanie linii)
acc_newEventNo_119=Zwarcie przełącznika wyjścia (detekcja obwodów)
acc_newEventNo_120=Odłączenie magnetyczne drzwi (wykrywanie linii)
acc_newEventNo_121=zwarcie magnetyczne drzwi (wykrywanie linii)
acc_newEventNo_159=Zdalne otwieranie bramy
acc_newEventNo_214=Skuteczne podłączenie do serwera
acc_newEventNo_217=Skuteczne połączenie do głównego urządzenia
acc_newEventNo_218=Przejście dowodem osobistemu
acc_newEventNo_222=Skuteczne uwierzytelnianie za kulisami
acc_newEventNo_223=Uwierzytelnianie za kulisami
acc_newEventNo_224=Nacisnąć dzwonek
acc_newEventNo_227=Otwieranie bramy podwójnej
acc_newEventNo_228=Zamykanie bramy podwójnej
acc_newEventNo_229=Otwarcia normalne w ustalonym czasie wyjścia pomocniczego
acc_newEventNo_230=Otwarcia normalne wyłączenia w ustalonym czasie wyjścia pomocniczego
acc_newEventNo_232=Skuteczne uwierzytelnianie
acc_newEventNo_233=Zdalne blokowanie
acc_newEventNo_234=Zdalne odblokowanie
acc_newEventNo_235=Skuteczna aktualizacja głowicy odczytu
acc_newEventNo_236=Usunięcie alarmu antysabotażowego głowicy odczytu
acc_newEventNo_237=Głowica odczytu podłączona
acc_newEventNo_239=Wywołanie urządzenia
acc_newEventNo_240=Połączenie zakończone
acc_newEventNo_243=Wejście alarmu pożarowego odłączone
acc_newEventNo_244=zwarcie wejściowe alarmu pożarowego
acc_newEventNo_247=Tablica rozbudowy online
acc_newEventNo_4008=Odzyskiwanie sieci
acc_newEventNo_4014=Sygnał wejściowy ognia odłączony, drzwi końcowe normalnie otwarte
acc_newEventNo_4015=Drzwi już online
acc_newEventNo_4018=Porównanie backend Otwórz
acc_newEventNo_5023=Ograniczony stan pożaru
acc_newEventNo_5024=Sprawdź limit czasu dla wielu pracowników
acc_newEventNo_5029=Porównanie backend nie powiodło się
acc_newEventNo_6005=Pojemność rekordu osiąga górny limit
acc_newEventNo_6006=Zwarcie linii (RS485)
acc_newEventNo_6007=Zwarcie w obwodzie (Wigan)
acc_newEventNo_6011=Drzwi są wyłączone
acc_newEventNo_6012=Alarm demontażu drzwi
acc_newEventNo_6013=Wyzwalany sygnał wejściowy ognia, drzwi otwarte
acc_newEventNo_6015=Resetuj zasilanie urządzenia rozszerzającego
acc_newEventNo_6016=Przywróć ustawienia fabryczne maszyny
acc_newEventNo_6070=Porównanie backend (lista zabroniona)
acc_eventNo_undefined=Brak określenia numeru zdarzenia
acc_advanceEvent_500=Przeciw powrotu na poziomie całego systemu (logika)
acc_advanceEvent_501=Uzasadnienie personelu (data ważności)
acc_advanceEvent_502=Kontrola ilości personelu
acc_advanceEvent_503=Blokada interakcyjna na poziomie całego systemu
acc_advanceEvent_504=Określenie trasy
acc_advanceEvent_505=Przeciw powrotu na poziomie całego systemu (w ustalonym czasie)
acc_advanceEvent_506=Przeciw powrotu na poziomie całego systemu (logika w ustalonym czasie)
acc_advanceEvent_507=Uzasadnienie personelu (ważne dni pozostałe po pierwszym użyciu)
acc_advanceEvent_508=Uzasadnienie personelu (ilość użytkowania)
acc_advanceEvent_509=Awaria uwierzytelniania za kulisami (personel niezarejestrowany)
acc_advanceEvent_510=Awaria uwierzytelniania za kulisami (anomalia danych)
acc_alarmEvent_701=Alarm DMR (Ustawienia zasady: {0})
#[21] Monitorowanie w czasie rzeczywistym
acc_rtMonitor_openDoor=Otwieranie bramy
acc_rtMonitor_closeDoor=Zamykanie bramy
acc_rtMonitor_remoteNormalOpen=Zdalne otwarcie normalne
acc_rtMonitor_realTimeEvent=Zdarzenie w czasie rzeczywistym
acc_rtMonitor_photoMonitor=Monitorowanie zdjęć
acc_rtMonitor_alarmMonitor=Monitorowanie alarmów
acc_rtMonitor_doorState=Status bramy
acc_rtMonitor_auxOutName=Nazwa wyjścia pomocniczego
acc_rtMonitor_nonsupport=Nieobsługiwane
acc_rtMonitor_lock=Blokowanie
acc_rtMonitor_unLock=Odblokowanie
acc_rtMonitor_disable=Blokowanie
acc_rtMonitor_noSensor=Brak magnesu bramy
acc_rtMonitor_alarm=Alarm
acc_rtMonitor_openForce=Brama została przypadkowo otwarta.
acc_rtMonitor_tamper=Antysabotażowy
acc_rtMonitor_duressPwdOpen=Otwieranie bramy hasłem przymusu
acc_rtMonitor_duressFingerOpen=Otwieranie bramy naciskiem przymusu
acc_rtMonitor_duressOpen=Otwieranie bramy przymusu
acc_rtMonitor_openTimeout=Brama otwiera się nadprogramowo
acc_rtMonitor_unknown=Nieznany
acc_rtMonitor_noLegalDoor=Obecnie brak bram kwalifikowanych!
acc_rtMonitor_noLegalAuxOut=Obecnie brak wyjścia pomocnego!
acc_rtMonitor_curDevNotSupportOp=Stan obecnego urządzenie nie obsługuje tego obsługiwania.
acc_rtMonitor_curNormalOpen=Obecne otwarcie normalne
acc_rtMonitor_whetherDisableTimeZone=Obecna brama jest w stanie otwarcia normalnego, czy zamknąć bramę po wyłączeniu okresu czasu otwarcia normalnego?
acc_rtMonitor_curSystemNoDoors=Nie ma żadnych bram dodawanych do obecnego systemu ani żadnych bram odpowiadających potrzebom użytkownika!
acc_rtMonitor_cancelAlarm=Anulowanie alarmu
acc_rtMonitor_openAllDoor=Otwieranie wszystkich bram obecnych
acc_rtMonitor_closeAllDoor=Zamykanie wszystkich bram obecnych
acc_rtMonitor_confirmCancelAlarm=Należy upewnić się, czy anulować alarm.
acc_rtMonitor_calcelAllDoor=Anulowanie wszystkich alarmów
acc_rtMonitor_initDoorStateTip=W trakcie uzyskiwania wszystkich bram w ramach uprawnień użytkownika w systemie...
acc_rtMonitor_alarmEvent=Zdarzenie alarmów
acc_rtMonitor_ackAlarm=Potwierdzenie alarmu
acc_rtMonitor_ackAllAlarm=Potwierdzenie wszystkich alarmów
acc_rtMonitor_ackAlarmTime=Potwierdzenie czasu alarmu
acc_rtMonitor_sureToAckThese=Należy upewnić się, czy potwierdzić {0} alarmów. Alarm zostanie anulowany po potwierdzeniu.
acc_rtMonitor_sureToAckAllAlarm=Należy upewnić się, czy potwierdzić wszystkie alarmy. Alarm zostanie anulowany po potwierdzeniu.
acc_rtMonitor_noSelectAlarmEvent=Należy wybrać zdarzenie alarmu do potwierdzenia!
acc_rtMonitor_noAlarmEvent=Brak zdarzeń alarmów w obecnym systemie!
acc_rtMonitor_forcefully=Anulowanie alarmu otwierania bramy przypadkowo
acc_rtMonitor_addToRegPerson=Dodawana do personelu zarejestrowanych
acc_rtMonitor_cardExist=Karta jest zajęta przez {0}, nie można jej wytwarzać powtarzalnie!
acc_rtMonitor_opResultPrompt=Zlecenia wysłane: {0} skutecznych, {1} nieskutecznych!
acc_rtMonitor_doorOpFailedPrompt=Żądanie z następujących bram nie zostanie prawidłowo wysłane, należy próbować ponownie!
acc_rtMonitor_remoteOpen=Zdalne otwieranie
acc_rtMonitor_remoteClose=Zdalne zamykanie
acc_rtMonitor_alarmSoundClose=Dźwięk wyłączony
acc_rtMonitor_alarmSoundOpen=Dźwięk włączony
acc_rtMonitor_playAudio=Upomnienie dźwiękowe
acc_rtMonitor_isOpenShowPhoto=Włączanie funkcji wyświetlania zdjęć
acc_rtMonitor_isOpenPlayAudio=Włączenie funkcji alarmu dźwiękowego
acc_rtm_open=Przycisk zdalnego wyzwalania
acc_rtm_close=Przycisk zdalnej blokady
acc_rtm_eleModule=Kontrola windy
acc_cancelAlarm_fp=Anulowanie alarmu otwarcia bramy odciskami palców.
acc_cancelAlarm_pwd=Anulowanie alarmu otwarcia bramy kodem przymusu
acc_cancelAlarm_timeOut=Anulowanie alarmu otwarcia bramy nadprogramowo
#Timer do synchronizacji urządzenia
acc_timing_syncDevTime=Timer do synchronizacji urządzenia
acc_timing_executionTime=Czas do wykonania
acc_timing_theLifecycle=Cykl do wykonania
acc_timing_errorPrompt=Błąd wpisu, należy wpisać liczbę całkowitą między 1-31.
acc_timing_checkedSyncTime=Należy wybrać czas do synchronizacji.
#[25] Raporty kontroli dostępu
acc_trans_hasAccLevel=Bramy umożliwiające wejścia i wyjścia
#
#[G1] Strefa kontroli dostępu
acc_zone_addZone=Należy dodać strefę kontroli dostępu
acc_zone_code=Numer
acc_zone_parentZone=Nadrzędna strefa kontroli dostępu
acc_zone_parentZoneCode=Numer stref nadrzędnych kontroli dostępu
acc_zone_parentZoneName=Nazwa stref nadrzędnych kontroli dostępu
acc_zone_outside=Strefa zewnętrzna
#[G2] Określenie głowicy odczytu 
acc_readerDefine_readerName=Nazwa głowicy odczytu
acc_readerDefine_fromZone=Ze strefy kontroli dostępu
acc_readerDefine_toZone=Do strefy kontroli dostępu
acc_readerDefine_delInfo1=Strefy dostępu związane z określeniem głowicy odczytu są powiązane z funkcją zaawansowanej kontroli dostępu i ich usunięcie jest niedozwolone! 
acc_readerDefine_selReader=Należy wybrać głowicę odczytu
acc_readerDefine_selectReader=Należy dodać głowicę odczytu!
acc_readerDefine_tip=Po przybyciu personelu na strefę zewnętrzną, jej zapis w tej strefie zostanie usunięty.
#[G3] Przeciw powrotu na poziomie całego systemu
acc_gapb_zone=Strefa kontroli dostępu
acc_gapb_whenToResetGapb=Resetowanie czasu przeciw powrotu
acc_gapb_apbType=Typ przeciw powrotu
acc_gapb_logicalAPB=Przeciw powrotu na logikę
acc_gapb_timedAPB=Przeciw powrotu w ustalonym czasie
acc_gapb_logicalTimedAPB=Przeciw powrotu na logikę i czas określony
acc_gapb_lockoutDuration=Czas trwania blokowania
acc_gapb_devOfflineRule=W przypadku kontroli odłączone
acc_gapb_standardLevel=Uprawienie dostępu standardowego
acc_gapb_accessDenied=Odmowa dostępu użytkownika
acc_gapb_doorControlZone=Lista bramy kontroluje wejściem i wyjściem do strefy kontroli dostępu
acc_gapb_resetStatus=Resetowanie statusu przeciw powrotu na poziomie całego systemu
acc_gapb_obeyAPB=Przestrzeganie zasady przeciw powrotu na poziomie całego systemu
acc_gapb_isResetGAPB=Resetowanie przeciw powrotu
acc_gapb_resetGAPBSuccess=Skuteczne resetowanie statusu przeciw powrotu
acc_gapb_resetGAPBFaile=Awaria resetowania statusu przeciw powrotu
acc_gapb_chooseArea=Należy wybrać strefę ponownie
acc_gapb_notDelInfo1=Strefa bramowa jest odniesiona jako nadrzędna strefa bramy i nie może być usunięta!
acc_gapb_notDelInfo2=Do tej strefy kontroli dostępu odnosi się określenie głowicy odczytu i nie można jej usunąć!
acc_gapb_notDelInfo3=Do tej strefy kontroli dostępu odnosi się funkcja zaawansowana i nie można ją usunąć!
acc_gapb_notDelInfo4=Do tej strefy kontroli dostępu odnosi się LED i nie można jej usunąć!
acc_gapb_zoneNumRepeat=Powtarzalny numer strefy kontroli dostępu!
acc_gapb_zoneNameRepeat=Powtarzalna nazwa strefy kontroli dostępu!
acc_gapb_personResetGapbPre=Należy upewnić się, czy resetować
acc_gapb_personResetGapbSuffix=tę zasadę przeciw powrotu osobowego.
acc_gapb_apbPrompt=Ta sama brama nie może być używana do kontrolowania dwóch stosunkowo niezależnych granic przeciw powrotu!
acc_gapb_occurApb=W przypadku postępowania przeciw powrotu
acc_gapb_noOpenDoor=Brama się nie otwiera
acc_gapb_openDoor=Otwieranie bramy
acc_gapb_zoneNumLength=Długość większa niż 20 znaków!
acc_gapb_zoneNameLength=Długość większa niż 30 znaków!
acc_gapb_zoneRemarkLength=Długość większa niż 50 znaków!
acc_gapb_isAutoServerMode=W przypadku wykrycia urządzenia z niewłączonym uwierzytelnianiem za kulisami, co może mieć wpływ na funkcjonalność, czy należy go włączyć natychmiast?
acc_gapb_applyTo=Zastosowywane się do
acc_gapb_allPerson=Wszystkich użytkowników
acc_gapb_justSelected=Wybranych użytkowników
acc_gapb_excludeSelected=Niewybranych użytkowników
#[G4] kto jest w środku
acc_zoneInside_lastAccessTime=Najbliższy czas wejścia
acc_zoneInside_lastAccessReader=Najbliższa głowica odczytu wejścia
acc_zoneInside_noPersonInZone=Nie zawiera żadnego personelu w tej strefie kontroli dostępu!
acc_zoneInside_noRulesInZone=Brak zasady ustawionej.
acc_zoneInside_totalPeople=Suma personelu
acc_zonePerson_selectPerson=Należy wybrać personelu lub dział!
#[G5] Ścieżka  
acc_route_name=Nazwa trasy
acc_route_setting=Ustawienia trasy
acc_route_addReader=Dodanie głowicy odczytu
acc_route_delReader=Usunięcie głowicy odczytu
acc_route_defineReaderLine=Określenie trasy głowicy odczytu
acc_route_up=Do góry
acc_route_down=Do dołu
acc_route_selReader=Należy najpierw wybrać głowicę odczytu przed obsługiwaniem.
acc_route_onlyOneOper=Należy wybrać tylko I wyłącznie jedną do obsługiwania.
acc_route_readerOrder=Kolejność określenia głowicy odczytu
acc_route_atLeastSelectOne=Należy wybrać przynajmniej jedną głowicę odczytu!
acc_route_routeIsExist=Trasa ta już istnieje.
#[G6] DMR
acc_dmr_residenceTime=Czas trwania pobytu
acc_dmr_setting=Ustawienia czasu trwania
#[G7] Obłożenie
acc_occupancy_max=Maksymalna liczba personelu umieszczonych
acc_occupancy_min=Minimalna liczba personelu umieszczonych
acc_occupancy_unlimit=Bez limitu
acc_occupancy_note=Maksymalna liczba personelu umieszczonych powinna być większa niż minimalna liczba personelu umieszczonych!
acc_occupancy_containNote=Należy wprowadzić przynajmniej jedną liczbę personelu z maksymalnej lub minimalnej umieszczonych.
acc_occupancy_maxMinValid=Należy wpisać liczbę większą od zera!
acc_occupancy_conflict=Strefa została ustawiona zgodnie z zasadami kontroli personelu!
acc_occupancy_maxMinTip=Pusta maksymalna/minimalna liczba personelu umieszczonych oznacza bez limitu.
#Dostępność kart
acc_personLimit_zonePropertyName=Nazwa właściwości strefy kontroli dostępu 
acc_personLimit_useType=Metoda zastosowania
acc_personLimit_userDate=Data ważności
acc_personLimit_useDays=Ważne dni pozostałe po pierwszym użyciu
acc_personLimit_useTimes=Ilość użytkowania
acc_personLimit_setZoneProperty=Ustawienia właściwości strefy kontroli dostępu
acc_personLimit_zoneProperty=Właściwość strefy kontroli dostępu
acc_personLimit_availabilityName=Nazwa upoważnienia personelu
acc_personLimit_days=Ilość dni
acc_personLimit_Times=Częstotliwość
acc_personLimit_noDel=Wybrana właściwość strefy dostępu jest powoływana i nie może być usunięta!
acc_personLimit_cannotEdit=Ta właściwość strefy dostępu jest powoływana i nie może być modyfikowana do użytkowania!
acc_personLimit_detail=Szczegóły
acc_personLimit_userDateTo=Ważny do
acc_personLimit_addPersonRepeatTip=Osoba z wybranego działu została dodana do atrybutu obszaru kontroli dostępu, proszę wybrać dział ponownie!
acc_personLimit_leftTimes=Pozostało {0} razy
acc_personLimit_expired=wygasły
acc_personLimit_unused=Nieużywany
#Blokada interakcyjna na poziomie całego systemu
acc_globalInterlock_addGroup=Dodawaie grupy
acc_globalInterlock_delGroup=Usunięcie grupy
acc_globalInterlock_refuseAddGroupMessage=Nie wolno powtarzalnie dodać bramy w grupie do tej samej blokady interakcyjnej.
acc_globalInterlock_refuseAddlockMessage=Dodane bramy pojawiają się już w innych grupach blokad interakcyjnych
acc_globalInterlock_refuseDeleteGroupMessage=Najpierw należy usunąć dane z blokady interakcyjnej.
acc_globalInterlock_isGroupInterlock=Blokada interakcyjna
acc_globalInterlock_isAddTheDoorImmediately=Dodać bramę natychmiast?
acc_globalInterlock_isAddTheGroupImmediately=Dodać grupę natychmiast?
#Ustawienia parametrów kontroli dostępu
acc_param_autoEventDev=Liczba równoległych urządzeń, które automatycznie pobierają zapisy zdarzeń
acc_param_autoEventTime=Odstęp czasu automatycznie pobierania zapisów zdarzeń
acc_param_noRepeat=Adres mailowy nie może być powtórzony, należy go ponownie wprowadzić
acc_param_most18=Łącznie można dodać do 18 adresów mailowych
acc_param_deleteAlert=Nie można usunąć pola wszystkich adresów mailowych
acc_param_invalidOrRepeat=Nieprawidłowy format adresu mailowego lub powtórzony adres mailowy
#Powiązanie na poziomie całego systemu
acc_globalLinkage_noSupport=Aktualnie wybrane bramy nie obsługują funkcji blokowania i odblokowywania.
acc_globalLinkage_trigger=Wyzwalanie powiązań na poziomie całego systemu
acc_globalLinkage_noAddPerson=Ten warunek wyzwalania zasady powiązania nie zawiera warunków związanych z personelem i odnosi się domyślnie do całego personelu!
acc_globalLinkage_selectAtLeastOne=Należy wybrać co najmniej jedno obsługiwanie wyzwalające powiązania!
acc_globalLinkage_selectTrigger=Należy dodać warunki wyzwalania powiązań!
acc_globalLinkage_selectInput=Należy dodać punkty wejściowe powiązań!
acc_globalLinkage_selectOutput=Należy dodać punkty wyjściowe powiązań!
acc_globalLinkage_audioRemind=Alarm dźwiękowy powiązań
acc_globalLinkage_audio=Dźwięk powiązania
acc_globalLinkage_isApplyToAll=Odnosi się do całego personelu
acc_globalLinkage_scope=Zakres personelu
acc_globalLinkage_everyPerson=Dowolnie
acc_globalLinkage_selectedPerson=Wybrane
acc_globalLinkage_noSupportPerson=Nieobsługiwane
acc_globalLinkage_reselectInput=Zmienił się typ warunków wyzwalania, należy ponownie wybrać punkt wejściowy powiązania!
acc_globalLinkage_addPushDevice=Należy dodać urządzenia obsługujące tę funkcję!
#Inny
acc_InputMethod_tips=Należy przejść na metodę wprowadzania danych w języku angielskim!
acc_device_systemCheckTip=Informacja o urządzeniu kontroli dostępu nie została dodana!
acc_notReturnMsg=Nie otrzymano żadnej wiadomości zwrotnej!
acc_validity_period=Licencja wygasła i funkcji nie obsługuje!
acc_device_pushMaxCount=W systemie istnieje już {0} urządzeń, osiągając limit uprawnień, nie można dodać urządzeń!
acc_device_videoHardwareLinkage=Ustawienia powiązań sprzętowych wideo
acc_device_videoCameraIP=Adres IP kamery
acc_device_videoCameraPort=Port kamery
acc_location_unable=Punkt zdarzenia nie został dodany do mapy elektronicznej, a dokładna lokalizacja personelu nie może być określona!
acc_device_wgDevMaxCount=Urządzenie w systemie osiągnęło limit licencji i nie może zmieniać ustawień!
#Przystosowywanie zdarzeń alarmów
acc_deviceEvent_selectSound=Należy wybrać plik audio
acc_deviceEvent_batchSetSoundErr=Anomalia ustawienia alarmu dźwiękowego w partii!
acc_deviceEvent_batchSet=Ustawienia dźwięk
acc_deviceEvent_sound=Dźwięk zdarzeń
acc_deviceEvent_exist=Istnieje
acc_deviceEvent_upload=Ponowne dodanie
#Wyszukiwanie ostatnich wydarzeń przy bramie śledczej
acc_doorEventLatestHappen=Wyszukiwanie ostatnich wydarzeń przy bramie śledczej
#Informacje o personelu kontroli dostępu
acc_pers_delayPassage=Wydłużenie dostępu
#Alarm o pojemności sprzętu
acc_dev_usageConfirm=Urządzenia, których pojemność została przekroczona w 90 procentach
acc_dev_immediateCheck=Zobacz teraz
acc_dev_inSoftware=W oprogramowaniu
acc_dev_inFirmware=W oprogramowaniu układowym
acc_dev_get=Przeglądanie
acc_dev_getAll=Zobacz wszystko
acc_dev_loadError=Awaria załadowania
#Odczyt
acc_reader_inout=Wyjście/Wejście
acc_reader_lightRule=Zasada oświetlenia
acc_reader_defLightRule=Zasada domyślna
acc_reader_encrypt=Szyfrowanie
acc_reader_allReaderOfCurDev=Wszystkie głowice odczytu obecnego urządzenia
acc_reader_tip1=Opcja szyfrowania domyślnie replikuje się na wszystkich głowicach odczytu na tym samym urządzeniu!
acc_reader_tip2=Opcja trybu ID jest dostępna tylko dla głowic odczytu obsługujących tę funkcję!
acc_reader_tip3=Typ protokołu RS485 jest kopiowany do wszystkich czytników w bieżącym urządzeniu. Ustawienia zaczną obowiązywać po ponownym uruchomieniu urządzenia!
acc_reader_tip4=Opcja ukrycia niektórych informacji osobowych jest domyślnie kopiowana do wszystkich czytelników tego samego urządzenia!
acc_reader_commType=Typ komunikacji
acc_reader_commAddress=Adres komunikacji
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=Blokowanie
acc_readerComAddress_repeat=Powtórzony adres komunikacji
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=Adres RS485
acc_readerCommType_wgAddress=Adres Wiegand
acc_reader_macError=Należy wprowadzić prawidłowy format adresu Mac.
acc_reader_machineType=Typ głowicy odczytu
acc_reader_readMode=Tryb
acc_reader_readMode_normal=Tryb standardowy
acc_reader_readMode_idCard=Tryb dowodu osobistego
acc_reader_note=Wskazówki: Należy wybierać urządzenia kontroli dostępu tylko w tej samej strefie ({0}), w której znajduje się kamera
acc_reader_rs485Type=Typ protokołu RS485
acc_reader_userLock=Blokada dostępu personelu
acc_reader_userInfoReveal=Ukryj informacje o personelu części
#
acc_operation_pwd=Kod autoryzacyjny
acc_operation_pwd_error=Nieprawidłowy kod autoryzacyjny
acc_new_input_not_same=Niezgodne wprowadzanie nowego klucza tajnego
acc_op_set_keyword=Ustawienia tajnego klucza autoryzacyjnego
acc_op_old_key=Stary tajny klucz autoryzacyjny
acc_op_new_key=Nowy tajny klucz autoryzacyjny
acc_op_cofirm_key=Potwierdzenie tajny kluczy autoryzacyjnych
acc_op_old_key_error=Niezgodne wprowadzanie starego klucza tajnego
#Zasady metody uwierzytelniania
acc_verifyRule_name=Nazwa zasady
acc_verifyRule_door=Metoda uwierzytelniania-brama
acc_verifyRule_person=Metoda uwierzytelniania-użytkownik
acc_verifyRule_copy=Należy wprowadzić metodę weryfikacji poniedziałku do innych dni roboczych.
acc_verifyRule_tip1=Należy wybrać co najmniej jedną metodę uwierzytelniania!
acc_verifyRule_tip2=Jeśli zasada zawiera metodę uwierzytelniania użytkownika, można dodać tylko bramki, które zawierają głowicy odczytu Wiegand!
acc_verifyRule_tip3=Głowica odczytu RS485 może działać tylko zgodnie z metodą uwierzytelniania bramy i nie obsługuje priorytetowej metody uwierzytelniania użytkownika.
acc_verifyRule_oldVerifyMode=Stary tryb weryfikacji
acc_verifyRule_newVerifyMode=Nowy tryb weryfikacji
acc_verifyRule_newVerifyModeSelectTitle=Wybierz nową metodę weryfikacji
acc_verifyRule_newVerifyModeNoSupportTip=Nie ma urządzenia obsługującego nową metodę weryfikacji!
#Test Wiegand
acc_wiegand_beforeCard=Długość numeru karty aktualnej: {0} (binarny), się nie równa do ostatniej karty.
acc_wiegand_curentCount=Aktualne cyfry numeru karty:{0}(binarny)
acc_wiegand_card=Karta
acc_wiegand_readCard=Odczyt karty
acc_wiegand_clearCardInfo=Ksowanie informacji numeru karty
acc_wiegand_originalCard=Oryginalny numer karty
acc_wiegand_recommendFmt=Format zalecany
acc_wiegand_parityFmt=Format paragrafu parytetowego
acc_wiegand_withSizeCode=Automatyczne obliczanie numeru kierunkowego w przypadku braku numeru kierunkowego.
acc_wiegand_tip1=Numery kierunkowe na powyższych kartach nie pasują do siebie i nie mogą być częścią tej samej partii.
acc_wiegand_tip2=Numer kierunkowy:{0} numer karty:{1} nie pasują się do siebie w oryginalnym numerze karty. Należy sprawdzić, czy kod kierunkowy i numer karty są wprowadzone prawidłowo!
acc_wiegand_tip3=Wprowadzony numer karty ({0}) nie może być dopasowany do oryginalnego numeru karty, należy ponownie sprawdzić, czy wprowadzony numer karty jest prawidłowy!
acc_wiegand_tip4=Wprowadzony numer kierunkowy ({0}) nie może być dopasowany do oryginalnego numeru karty, należy ponownie sprawdzić, czy wprowadzony numer kierunkowy jest prawidłowy.
acc_wiegand_tip5=W przypadku konieczności skorzystania z przypisanej funkcji numeru kierunkowego, należy upewnić się, że kolumny, w których znajduje się numer kierunkowy, są puste!
acc_wiegand_warnInfo1=Przy wymianie nowej karty do odczytu, należy ręcznie przejść do następnej karty.
#Monitorowanie LCD w czasie rzeczywistym 
acc_leftMenu_LCDRTMonitor=Panel do wyświetlenia dostępu personelu
acc_LCDRTMonitor_current=Informacja o personelu aktywnym
acc_LCDRTMonitor_previous=Informacja o personelu historycznym
#API
acc_api_levelIdNotNull=ID grupy uprawnień nie może być pusty.
acc_api_levelExist=Jest obecna grupa uprawnień
acc_api_areaNameNotNull=Region nie może być pusty
acc_api_levelNotExist=Grupa uprawnień nie istnieje
acc_api_levelNotHasPerson=Brak personelu w ramach grupy uprawnień.
acc_api_doorIdNotNull=ID bramy nie może być pusty.
acc_api_doorNameNotNull=Nazwa bramy nie może być pusta.
acc_api_doorIntervalSize=Czas otwierania bramy należy umieść się w okresie 1-254.
acc_api_doorNotExist=Brama nie istnieje.
acc_api_devOffline=Urządzenie jest odłączone lub wyłączone
acc_api_devSnNotNull=SN urządzenie nie może być puste
acc_api_timesTampNotNull=Znacznik czasu nie może być pusty
acc_api_openingTimeCannotBeNull=Czas otwarcia drzwi nie może być pusty
acc_api_parameterValueCannotBeNull=Wartość parametru nie może być pusta
acc_api_deviceNumberDoesNotExist=Numer seryjny urządzenia nie istnieje
acc_api_readerIdCannotBeNull=Identyfikator czytelnika nie może być pusty
acc_api_theReaderDoesNotExist=Czytana głowa nie istnieje
acc_operate_door_notInValidDate=Obecnie nie w uzasadnionych godzinach otwarcia bramy zdalnych, w razie potrzeby należy skontaktować się z administratorem!
acc_api_doorOffline=Drzwi są w trybie offline lub wyłączone
#Automatyczny eksport informacji o kontroli dostępu
acc_autoExport_title=Automatyczny eksport zapisu zdarzeń
acc_autoExport_frequencyTitle=Częstotliwość eksportu automatycznego
acc_autoExport_frequencyDay=Codziennie
acc_autoExport_frequencyMonth=Co miesiąc
acc_autoExport_firstDayMonth=W pierwszym dniu miesiąca
acc_autoExport_specificDate=Data eksportu
acc_autoExport_exportModeTitle=Tryb eksportu
acc_autoExport_dailyMode=Zapis zdarzeń dziennego
acc_autoExport_monthlyMode=Zapis zdarzeń miesięcznego (Wszystkie informacje z poprzedniego miesiąca i zapis zdarzeń z bieżącego miesiąca)
acc_autoExport_allMode=Zapis zdarzeń wszystkich (maksymalnie 3000 zapisów zdarzeń)
acc_autoExport_recipientMail=Adres mailowy do otrzymania
#Pierwszy wejdzie i ostatni wyjdzie
acc_inOut_inReaderName=Pierwszy wejdzie-nazwa odczytu
acc_inOut_firstInTime=Czas pierwszego wejścia
acc_inOut_outReaderName=Ostatni wyjdzie- nazwa odczytu
acc_inOut_lastOutTime=Czas ostatniego wyjścia
# 防疫 参数
acc_dev_setHep=Ustaw parametry wykrywania maski i temperatury
acc_dev_enableIRTempDetection=Włącz kontrolę temperatury za pomocą podczerwieni
acc_dev_enableNormalIRTempPass=Odmów dostępu, gdy temperatura przekroczy zakres
acc_dev_enableMaskDetection=Włącz wykrywanie maski
acc_dev_enableWearMaskPass=Odmów dostępu bez maski
acc_dev_tempHighThreshold=Próg alarmu wysokiej temperatury
acc_dev_tempUnit=Jednostka temperatury
acc_dev_tempCorrection=Korekta odchylenia temperatury
acc_dev_enableUnregisterPass=Zezwalaj niezarejestrowanym osobom na dostęp
acc_dev_enableTriggerAlarm=Wyzwalaj alarm zewnętrzny
# 联动 邮件
acc_mail_temperature=Temperatura ciała
acc_mail_mask=Czy nosić maskę
acc_mail_unmeasured=Niezmierzone
#Digifort 联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort Global Events
acc_digifort_chooseDigifortEvents=Wybierz globalne wydarzenia Digifort
acc_digifort_eventExpiredTip=Jeśli zdarzenie globalne zostanie usunięte z serwera Digifort, będzie miało kolor czerwony.
acc_digifort_checkConnection=Sprawdź, czy informacje o połączeniu serwera Digifort są poprawne.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Dodaj kontakty
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
# 扩展 参数
acc_dev_setExtendParam=Ustaw rozszerzone parametry
acc_extendParam_faceUI=Wyświetlacz interfejsu
acc_extendParam_faceParam=Parametry ściany
acc_extendParam_accParam=Parametry kontroli dostępu
acc_extendParam_intercomParam=Parametry interkomu wizualnego
acc_extendParam_volume=Tom
acc_extendParam_identInterval=Interwał identyfikacji (ms)
acc_extendParam_historyVerifyResult=Wyświetl historyczne wyniki weryfikacji
acc_extendParam_macAddress=Wyświetl adres MAC
acc_extendParam_showIp=Pokaż adres IP
acc_extendParam_24HourFormat=Pokaż format 24-godzinny
acc_extendParam_dateFormat=Format daty
acc_extendParam_1NThreshold=1: próg N
acc_extendParam_facePitchAngle=Kąt nachylenia powierzchni
acc_extendParam_faceRotationAngle=Kąt obrotu ściany
acc_extendParam_imageQuality=Jakość obrazu
acc_extendParam_miniFacePixel=Minimalny piksel twarzy
acc_extendParam_biopsy=Włącz biopsję
acc_extendParam_showThermalImage=Pokaż obraz termiczny
acc_extendParam_attributeAnalysis=Włącz analizę atrybutów
acc_extendParam_temperatureAttribute=Atrybut wykrywania temperatury
acc_extendParam_maskAttribute=Atrybut wykrywania maski
acc_extendParam_minTemperature=Min. temperatura
acc_extendParam_maxTemperature=Temperatura maksymalna
acc_extendParam_gateMode=Tryb bramki
acc_extendParam_qrcodeEnable=Włącz funkcję kodu QR
# 可视 对讲
acc_dev_intercomServer=Adres usługi interkomu wizualnego
acc_dev_intercomPort=Serwer interkomu wizualnego
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Synchronizacja grupy uprawień
# 夏令时名称
acc_dsTimeUtc_none=Nie ustawione
acc_dsTimeUtc_AreaNone=W tym obszarze nie ma czasu na oszczędzanie światła
acc_dsTimeUtc1000_0=Canberra, Melbourne, Sydney
acc_dsTimeUtc1000_1=HobartGenericName
acc_dsTimeUtc_0330_0=Nowa Fundlandia
acc_dsTimeUtc_1000_0=Wyspy Aleutiańskie
acc_dsTimeUtc_0200_0=Śródatlantycki - używany
acc_dsTimeUtc0930_0=Adelaide.
acc_dsTimeUtc_0100_0=Azory
acc_dsTimeUtc_0400_0=Atlantic time (Kanada)
acc_dsTimeUtc_0400_1=Santiago.
acc_dsTimeUtc_0400_2=Asuncja
acc_dsTimeUtc_0300_0=Grenlandia
acc_dsTimeUtc_0300_1=Wyspy Saint Pierre i Miquelon
acc_dsTimeUtc0200_0=Kisinau
acc_dsTimeUtc0200_1=Helsinki, Kijow, Riga, Sofia, Tallinn, Wilno
acc_dsTimeUtc0200_2=Ateny, Bukareszt
acc_dsTimeUtc0200_3=Jerozolima
acc_dsTimeUtc0200_4=Amman.
acc_dsTimeUtc0200_5=Bejrut.
acc_dsTimeUtc0200_6=Damaszek
acc_dsTimeUtc0200_7=Gaza, Hebron
acc_dsTimeUtc0200_8=DżubaName
acc_dsTimeUtc_0600_0=Czas centralny (USA i Kanada)
acc_dsTimeUtc_0600_1=Guadalajara, Mexico City, Monterrey
acc_dsTimeUtc_0600_2=Wyspa Wielkanocna
acc_dsTimeUtc1300_0=Wyspy Samoa
acc_dsTimeUtc_0500_0=Havana.
acc_dsTimeUtc_0500_1=Wschodni czas (Stany Zjednoczone i Kanada)
acc_dsTimeUtc_0500_2=Haiti.
acc_dsTimeUtc_0500_3=Indiana (Wschód)
acc_dsTimeUtc_0500_4=Wyspy Turks i Caicos
acc_dsTimeUtc_0800_0=Ocean Spokojny (USA i Kanada)
acc_dsTimeUtc_0800_1=Baja Kalifornia
acc_dsTimeUtc0330_0=teheran lub tehran
acc_dsTimeUtc0000_0=Dublin, Edynburg, Lizbona, Londyn
acc_dsTimeUtc1200_0=FidżiName
acc_dsTimeUtc1200_1=Petropavlovsk Kamczatka stara
acc_dsTimeUtc1200_2=Wellington, Auckland
acc_dsTimeUtc1100_0=Wyspa Norfolk
acc_dsTimeUtc_0700_0=Chihuahua, La Paz, Mazaatlan
acc_dsTimeUtc_0700_1=Czas górski (USA i Kanada)
acc_dsTimeUtc0100_0=Belgrad, Bratysława, Budapeszt, Lublana, Praga
acc_dsTimeUtc0100_1=Sarajewo, Skopje, Warszawa, Zagrzeb
acc_dsTimeUtc0100_2=CasablancaName
acc_dsTimeUtc0100_3=Bruksela, Kopenhaga, Madryt, Paryż
acc_dsTimeUtc0100_4=Amsterdam, Berlin, Bern, Rzym, Sztokholm, Wiedeń
acc_dsTimeUtc_0900_0=Alaska
#安全点(muster point)
acc_leftMenu_accMusterPoint=Punkt zbiórki
acc_musterPoint_activate=Aktywuj
acc_musterPoint_addDept=Dodaj dział
acc_musterPoint_delDept=Usuń dział
acc_musterPoint_report=Raport punktowy
acc_musterPointReport_sign=Zaloguj się ręcznie
acc_musterPointReport_generate=Generuj raporty
acc_musterPoint_addSignPoint=Dodaj punkt znaku
acc_musterPoint_delSignPoint=Usuń punkt znaku
acc_musterPoint_selectSignPoint=Proszę dodać znak!
acc_musterPoint_signPoint=Punkt znaku
acc_musterPoint_delFailTip=Istnieją już aktywowane punkty zbiórki i nie można ich usunąć!
acc_musterPointReport_enterTime=Wprowadź czas
acc_musterPointReport_dataAnalysis=Analiza danych
acc_musterPointReport_safe=Bezpieczny
acc_musterPointReport_danger=Niebezpieczeństwo
acc_musterPointReport_signInManually=dziurkowanie ręczne
acc_musterPoint_editTip=Punkt zbiórki jest aktywny i nie można go edytować!
acc_musterPointEmail_total=Oczekiwana obecność:
acc_musterPointEmail_safe=zameldowane (sejf):
acc_musterPointEmail_dangerous=W niebezpieczeństwie:
acc_musterPoint_messageNotification=Powiadomienie o aktywacji wiadomości
acc_musterPointReport_sendEmail=Zaplanowany raport push
acc_musterPointReport_sendInterval=Wyślij interwał
acc_musterPointReport_sendTip=Upewnij się, że wybrana metoda powiadomienia została skonfigurowana pomyślnie, w przeciwnym razie powiadomienie nie zostanie wysłane normalnie!
acc_musterPoint_mailSubject=Zawiadomienie o montażu awaryjnym
acc_musterPoint_mailContent=Proszę natychmiast zebrać się na "{0}" i zalogować się na urządzeniu "{1}", dziękuję!
acc_musterPointReport_mailHead=Cześć, to nagły raport zgodny. Proszę przejrzeć.
acc_musterPoint_visitorsStatistics=Statystyki odwiedzających
# 报警监控
acc_alarm_priority=priorytet
acc_alarm_total=w całości
acc_alarm_today=Dzisiejszy rekord
acc_alarm_unhandled=Niepotwierdzone
acc_alarm_inProcess=W procesie
acc_alarm_acknowledged=Potwierdzone
acc_alarm_top5=Pierwsze pięć alarm ów@ info: whatsthis
acc_alarm_monitoringTime=Czas monitorowania
acc_alarm_history=Historia przetwarzania alarmów
acc_alarm_acknowledgement=Przetwarzanie dokumentacji
acc_alarm_eventDescription=Szczegóły zdarzenia
acc_alarm_acknowledgeText=Po zaznaczeniu, szczegóły zdarzenia alarmowego zostaną przesłane do podanej skrzynki pocztowej
acc_alarm_emailSubject=Dodaj rekord przetwarzania zdarzeń alarmowych
acc_alarm_mute=Niemowa
acc_alarm_suspend=zawieszenie
acc_alarm_confirmed=Incydent został potwierdzony.
acc_alarm_list=Dziennik alarmów
#ntp
acc_device_setNTPService=Ustawienia serwera NTP
acc_device_setNTPServiceTip=Wprowadź wiele adresów serwerów rozdzielonych przecinkami (,) lub średnikami (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=W działaniu kontrolera dostępu użytkownik Super nie jest ograniczony przepisami dotyczącymi stref czasowych, anty-passback i blokady oraz ma wyjątkowo wysoki priorytet otwierania drzwi.
acc_editPerson_delayPassageTip=Wydłuż czas oczekiwania personelu poprzez punkty dostępu. Odpowiedni dla osób niepełnosprawnych fizycznie lub osób z innymi niepełnosprawnościami.
acc_editPerson_disabledTip=Tymczasowo wyłączyć poziom dostępu personelu.
#门禁向导
acc_guide_title=Kreator konfiguracji modułu kontroli dostępu
acc_guide_addPersonTip=Musisz dodać osobę i odpowiednie dane uwierzytelniające (twarz, odcisk palca, kartę, dłoń lub hasło); jeśli już to zrobiłeś, pomiń ten krok bezpośrednio
acc_guide_timesegTip=Skonfiguruj prawidłowy okres czasu otwarcia
acc_guide_addDeviceTip=Dodaj odpowiednie urządzenie jako punkt dostępowy
acc_guide_addLevelTip=Dodaj poziom kontroli dostępu
acc_guide_personLevelTip=Przypisz danej osobie odpowiednie uprawnienia kontroli dostępu
acc_guide_rtMonitorTip=Sprawdź rekordy kontroli dostępu w czasie rzeczywistym
acc_guide_rtMonitorTip2=Przeglądanie w czasie rzeczywistym rekordów kontroli dostępu po dodaniu obszaru i odpowiednich drzwi
#查看区域内人员
acc_zonePerson_cleanCount=Wyczyść statystyki osób wchodzących i wychodzących
acc_zonePerson_inCount=Statystyki liczby wchodzących osób
acc_zonePerson_outCount=Statystyki liczby osób wychodzących
#biocv460
acc_device_validFail=Nazwa użytkownika lub hasło jest nieprawidłowe, a weryfikacja nie powiedzie się!
acc_device_pwdRequired=Można wprowadzić tylko maksymalnie 6 liczby całkowite