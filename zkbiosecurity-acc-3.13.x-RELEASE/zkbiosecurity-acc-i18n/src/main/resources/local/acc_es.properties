#[1]左侧菜单
acc_module=Acceso
acc_leftMenu_accDev=Dispositivos de Acceso
acc_leftMenu_auxOut=Salidas Auxiliares
acc_leftMenu_dSTime=Horario de Verano
acc_leftMenu_access=Control de Acceso
acc_leftMenu_door=Puertas
acc_leftMenu_accRule=Regla de Acceso
acc_leftMenu_interlock=Esclusamiento
acc_leftMenu_antiPassback=Anti-Passback
acc_leftMenu_globalLinkage=Vínculos Globales
acc_leftMenu_firstOpen=Primera Apertura
acc_leftMenu_combOpen=Verificación Multi-Usuario
acc_leftMenu_personGroup=Grupos Multi-Usuario
acc_leftMenu_level=Niveles de Acceso
acc_leftMenu_electronicMap=Mapa Virtual
acc_leftMenu_personnelAccessLevels=Niveles de Acceso del Personal
acc_leftMenu_searchByLevel=Por Nivel de Acceso
acc_leftMenu_searchByDoor=Privilegios por Puerta
acc_leftMenu_expertGuard=Funciones Avanzadas
acc_leftMenu_zone=Zonas
acc_leftMenu_readerDefine=Definir Lectores
acc_leftMenu_gapbSet=Anti-Passback Global
acc_leftMenu_whoIsInside=Quién Está Dentro
acc_leftMenu_whatRulesInside=Reglas de Quién Está Dentro
acc_leftMenu_occupancy=Control de Ocupación
acc_leftMenu_route=Control de Ruta
acc_leftMenu_globalInterlock=Esclusamiento Global
acc_leftMeue_globalInterlockGroup=Grupo Esclusamiento Global
acc_leftMenu_dmr=Regla de Hombre Aislado
acc_leftMenu_personLimit=Disponibilidad de Usuario
acc_leftMenu_verifyModeRule=Modo de Verificación
acc_leftMenu_verifyModeRulePersonGroup=Grupos de Verificación
acc_leftMenu_extDev=Control de I/O
acc_leftMenu_firstInLastOut=Primero en entrar y último en salir
acc_leftMenu_accReports=Informes de control de acceso
#[3]门禁时间段
acc_timeSeg_entity=Horario
acc_timeSeg_canNotDel=El período de tiempo está en uso y no se puede eliminar!
#[4]门禁设备--公共的在common中
acc_common_ruleName=Nombre de Regla
acc_common_hasBeanSet=Se ha establecido
acc_common_notSet=No establecido
acc_common_hasBeenOpened=Ha sido abierta
acc_common_notOpened=No abierta
acc_common_partSet=Parte del conjunto
acc_common_linkageAndApbTip=Si configura vínculos globales, anti-passback y anti-passback global pueden ocurrir conflictos.
acc_common_vidlinkageTip=Confirme que el punto de entrada tenga una cámara asociada, de lo contrario el vínculo de video no funcionará.
acc_common_accZoneFromTo=No se puede establecer la misma zona
acc_common_logEventNumber=ID de Evento
acc_common_bindOrUnbindChannel=Encuadernar / desenlazar la cámara
acc_common_boundChannel=Cámara encuadernada
#设备信息
acc_dev_iconType=Tipo de Ícono
acc_dev_carGate=Barrera Vehicular
acc_dev_channelGate=Barrera Flap
acc_dev_acpType=Tipo de Panel
acc_dev_oneDoorACP=Panel de 1 puerta
acc_dev_twoDoorACP=Panel de 2 puertas
acc_dev_fourDoorACP=Panel de 4 puertas
acc_dev_onDoorACD=Terminal Autónoma
acc_dev_switchToTwoDoorTwoWay=Cambiar a panel de 2 puertas
acc_dev_addDevConfirm2=La conexión con el dispositivo fue exitosa pero el tipo de panel es diferente del seleccionado. ¿Desea cambiar a panel de {0} puertas?
acc_dev_addDevConfirm4=Terminal Autónoma. ¿Desea agregarla?
acc_dev_oneMachine=Terminal Autónoma
acc_dev_fingervein=Vena
acc_dev_control=Control de Dispositivo
acc_dev_protocol=Tipo de Protocolo
acc_ownedBoard=Placas de expansión agregadas
#设备操作
acc_dev_start=Iniciar
acc_dev_accLevel=Autorización de Acceso
acc_dev_timeZoneAndHoliday=Horarios, Días Festivos
acc_dev_linkage=Vínculos
acc_dev_doorOpt=Configuración de Puerta
acc_dev_firstPerson=Primera Apertura
acc_dev_multiPerson=Verificación Multi-Usuario
acc_dev_interlock=Esclusamiento
acc_dev_antiPassback=Anti-Passback
acc_dev_wiegandFmt=Formato Weigand
acc_dev_outRelaySet=Configuración de salida auxiliar
acc_dev_backgroundVerifyParam=Verificación en Segundo Plano
acc_dev_getPersonInfoPrompt=Confirme haber obtenido la información del personal exitósamente, de lo contrario puede ocurrir un error. ¿Continuar?
acc_dev_getEventSuccess=Descarga de eventos exitosa.
acc_dev_getEventFail=Descarga de eventos fallida.
acc_dev_getInfoSuccess=Descarga de información exitosa.
acc_dev_getInfoXSuccess=Descarga de {0} exitosa.
acc_dev_getInfoFail=Descarga de información fallida.
acc_dev_updateExtuserInfoFail=No se pudo actualizar la información del personal.
acc_dev_getPersonCount=Obtener total de usuarios
acc_dev_getFPCount=Obtener total de huellas
acc_dev_getFVCount=Obtener total de venas
acc_dev_getFaceCount=Obtener total de rostros
acc_dev_getPalmCount=Obtener Cantidad de Palma
acc_dev_getBiophotoCount=Obtener total de fotos
acc_dev_noData=El dispositivo no tiene datos.
acc_dev_noNewData=El dispositivo no tiene eventos nuevos.
acc_dev_softLtDev=Existen más usuarios en el software que en el dispositivo.
acc_dev_personCount=Número de usuarios:
acc_dev_personDetail=Los detalles a continuación:
acc_dev_softEqualDev=Los usuarios del equipo y del software son iguales.
acc_dev_softGtDev=Existen más usuarios en el dispositivo que en el software.
acc_dev_cmdSendFail=No se pudo enviar el comando. Intente de nuevo.
acc_dev_issueVerifyParam=Verificación en Segundo Plano
acc_dev_verifyParamSuccess=Parámetros de Verificación en Segundo Plano aplicados exitosamente.
acc_dev_backgroundVerify=Verificación en Segundo Plano
acc_dev_selRightFile=Seleccione el archivo de actualización correcto.
acc_dev_devNotOpForOffLine=El dispositivo esta fuera de línea.
acc_dev_devNotSupportFunction=El dispositivo no soporta esta característica.
acc_dev_devNotOpForDisable=El dispositivo está deshabilitado.
acc_dev_devNotOpForNotOnline=El dispositivo esta desconectado o deshabilitado.
acc_dev_getPersonInfo=Obtener información de usuarios
acc_dev_getFPInfo=Obtener información de huellas
acc_dev_getFingerVeinInfo=Obtener información de venas
acc_dev_getPalmInfo=Obtener información de palma
acc_dev_getBiophotoInfo=Obtener información de cara de luz visible
acc_dev_getIrisInfo=Obtener información sobre el iris
acc_dev_disable=deshabilitado, seleccione de nuevo.
acc_dev_offlineAndContinue=no está en línea. ¿Continuar?
acc_dev_offlineAndSelect=no está en línea.
acc_dev_opAllDev=Todos los dispositivos
acc_dev_opOnlineDev=Dispositivos en línea
acc_dev_opException=Control de excepciones
acc_dev_exceptionAndConfirm=Tiempo de espera agotado para la conexión. Revise la conexión de red y reintente.
acc_dev_getFaceInfo=Obtener información de rostros
acc_dev_selOpDevType=Seleccione el tipo de dispositivo.
acc_dev_hasFilterByFunc=Solo mostrar equipos en línea que soporten la función.
acc_dev_masterSlaveMode=RS485 modo maestro y esclavo
acc_dev_master=Maestro
acc_dev_slave=Esclavo
acc_dev_modifyRS485Addr=Dirección RS485
acc_dev_rs485AddrTip=Ingrese un número entre 1 y 63.
acc_dev_enableFeature=Dispositivos con verificación en segundo plano activada.
acc_dev_disableFeature=Dispositivos con verificación en segundo plano desactivada.
acc_dev_getCountOnly=Obtener total
acc_dev_queryDevPersonCount=Consultar Cantidad de Usuarios
acc_dev_queryDevVolume=Capacidad del Dispositivo
acc_dev_ruleType=Tipo de Regla
acc_dev_contenRule=Detalles de las Reglas
acc_dev_accessRules=Reglas del Dispositivo
acc_dev_ruleContentTip=Separación de reglas con caracter vertical (|)
acc_dev_rs485AddrFigure=Figura de Dirección RS485
acc_dev_addLevel=Agregar a Nivel de Acceso Maestro
acc_dev_personOrFingerTanto=El número de usuarios o huellas es demasiado grande. Sincronización fallida.
acc_dev_personAndFingerUnit=(Número)
acc_dev_setDstime=Horario de Verano
acc_dev_setTimeZone=Zona Horaria
acc_dev_selectedTZ=Zona Horaria Seleccionada
acc_dev_timeZoneSetting=Zona Horaria
acc_dev_timeZoneCmdSuccess=Envío de configuración exitosa.
acc_dev_enableDstime=Habilitar Horario de Verano
acc_dev_disableDstime=Deshabilitar Horario de Verano
acc_dev_timeZone=Horario
acc_dev_dstSettingTip=Configuración de Horario de Verano
acc_dev_dstDelTip=Cancelar Horario de Verano del Dispositivo
acc_dev_enablingDst=Habilitando Horario de Verano
acc_dev_dstEnableCmdSuccess=Horario de verano habilitado.
acc_dev_disablingDst=Deshabilitando horario de verano.
acc_dev_dstDisableCmdSuccess=Horario de verano deshabilitado exitosamente.
acc_dev_dstCmdSuccess=Configuración de horario de verano enviada exitosamente.
acc_dev_usadst=Horario de Verano
acc_dev_notSetDst=Sin configuraciones
acc_dev_selectedDst=Horario de Verano Seleecionado
acc_dev_configMasterSlave=Configuración maestro-esclavo
acc_dev_hasFilterByUnOnline=Solo mostrar dispositivos en línea
acc_dev_softwareData=Si observa que los datos no coinciden con el dispositivo, sincronize los datos con el sistema antes de realizar la consulta.
acc_dev_disabled=Los dispositivos deshabilitados no pueden utilizarse.
acc_dev_offline=Los dispositivos fuera de línea no pueden utilizarse.
acc_dev_noSupport=Los dispositivos que no soportan esta función no pueden ser utilizados.
acc_dev_noRegDevTip=El acceso a los datos de los equipos no registrados sobrescribirá los datos en el software, ¿está seguro de continuar?
acc_dev_noOption=No hay opciones
acc_dev_devFWUpdatePrompt=El dispositivo que desea agregar no soporta usuarios deshabilitados ni vigencia de acceso (para más información, consulte el manual del usuario)
acc_dev_panelFWUpdatePrompt=El dispositivo que desea agregar no soporta usuarios deshabilitados ni vigencia de acceso, ¿desea actualizar el firmware?
acc_dev_sendEventCmdSuccess=Comando de eliminar evento enviado exitosamente.
acc_dev_tryAgain=Intente de nuevo.
acc_dev_eventAutoCheckAndUpload=Comprobación automática y cargar eventos
acc_dev_eventUploadStart=Inicio de carga de eventos.
acc_dev_eventUploadEnd=Carga de eventos completada.
acc_dev_eventUploadFailed=Carga de eventos fallida.
acc_dev_eventUploadPrompt=El firmware del dispositivo es obsoleto. ¿Desea actualizar?
acc_dev_backupToSoftware=Respaldar los datos en el software
acc_dev_deleteEvent=Eliminar los registros antiguos
acc_dev_upgradePrompt=El firmware de su dispositivo es obsoleto. Antes de actualizar, realice un respaldo de los datos del equipo.
acc_dev_conflictCardNo=La tarjeta ya esta asignada al usuario {0} en el sistema.
acc_dev_rebootAfterOperate=Operación exitosa. El dispositivo se reiniciará.
acc_dev_baseOptionTip=No se pudo obtener los parámetros.
acc_dev_funOptionTip=Error al obtener los parámetros de función.
acc_dev_sendComandoTip=Comando de obtener parámetros fallido.
acc_dev_noC3LicenseTip=No se puede agregar el dispositivo ({0}), obtenga una licencia nueva.
acc_dev_combOpenDoorTip=({0})La función multi-usuario se encuentra activada y no puede ser usada simultaneamente con la verificación en segundo plano.
acc_dev_combOpenDoorPersonCountTip=¡El número de personas que abren la puerta en el grupo {0} no debe ser superior a {1}!
acc_dev_addDevTip=Sólo aplica para dispositivos con protocolo Pull.
acc_dev_addError=Error al agregar el dispositivo, faltan los parámetros ({0})!
acc_dev_updateIPAndPortError=Actualización de IP o puerto fallida.
acc_dev_transferFilesTip=Prueba de firmware completada, transfiriendo archivos.
acc_dev_serialPortExist=Puertos Serie Existentes
acc_dev_isExist=Dispositivos Existentes
acc_dev_description=Descripción
acc_dev_searchEthernet=Buscar Dispositivos Ethernet
acc_dev_searchRS485=Buscar Dispositivos RS485
acc_dev_rs485AddrTip1=La dirección RS485 incial no puede ser mayor que la dirección final
acc_dev_rs485AddrTip2=El alcance de la búsqueda RS485 debe ser menor de 20
acc_dev_clearAllCmdCache=Borrar Todos los Comandos
acc_dev_authorizedSuccessful=Autorización Exitosa
acc_dev_authorize=Autorizar
acc_dev_registrationDevice=Dispositivo de Registro
acc_dev_setRegistrationDevice=Dispositivo de Registro
acc_dev_mismatchedDevice=Este dispositivo no puede conectarse debido a un error de número de serie.
acc_dev_pwdStartWithZero=La contraseña de comunicación no debe iniciar con cero.
acc_dev_maybeDisabled=La licencia actual solo permite agregar {0} puerta(s) más. Las puertas que excedan la licencia quedarán deshabilitadas, ¿desea continuar?
acc_dev_Limit=La licencia del sistema ha alcanzado el límite. Debe actualizar la licencia.
acc_dev_selectDev=Seleccione el dispositivo.
acc_dev_cannotAddPullDevice=No es posible agregar dispositivos Pull. Contacte al personal de ventas.
acc_dev_notContinueAddPullDevice=Existen {0} dispositivos Pull en el sistema y no es posible agregar más. Contacte al personal de ventas.
acc_dev_deviceNameNull=Dispositivo sin modelo. No se puede agregar.
acc_dev_commTypeErr=El tipo de comunicación no coincide y no es posible agregar el dispositivo.
acc_dev_inputDomainError=Ingrese una dirección de dominio válida.
acc_dev_levelTip=El nivel de acceso sobrepasa 5,000 usuarios y ya no permite agregar automáticamente.
acc_dev_auxinSet=Parámetros de Entrada Auxiliar
acc_dev_verifyModeRule=Verificar Reglas
acc_dev_netModeWired=Cableado
acc_dev_netMode4G=4G
acc_dev_netModeWifi=WiFi
acc_dev_updateNetConnectMode=Cambiar Conexión de Red
acc_dev_wirelessSSID=SSID
acc_dev_wirelessKey=Contraseña
acc_dev_searchWifi=Buscar WiFi
acc_dev_testNetConnectSuccess=Comunicación exitosa, ¿está seguro de cambiar?
acc_dev_testNetConnectFailed=La conexión no es estable.
acc_dev_signalIntensity=Intensidad de Señal
acc_dev_resetSearch=Buscar de Nuevo
acc_dev_addChildDevice=Adición de un dispositivo secundario
acc_dev_modParentDevice=Cambiar el dispositivo maestro
acc_dev_configParentDevice=Ajuste de Dispositivo Maestro
acc_dev_lookUpChildDevice=Ver dispositivos infantiles
acc_dev_addChildDeviceTip=Se requiere autorización para el dispositivo esclavo.
acc_dev_maxSubCount=El número de dispositivos esclavos autorizados excede el número máximo de {0}.
acc_dev_seletParentDevice=Seleccione el dispositivo maestro.
acc_dev_networkCard=Tarjeta de red
acc_dev_issueParam=La costumbre de enviar los parámetros
acc_dev_issueMode=Modo de Sincronización
acc_dev_initIssue=Firmware v3030 Inicialización de Datos
acc_dev_customIssue=Sincronizar Datos Personalizado
acc_dev_issueData=Datos
acc_dev_parent=Dispositivo Maestro
acc_dev_parentEnable=El dispositivo maestro está deshabilitado
acc_dev_parentTips=La vinculación del dispositivo maestro borrará todos los datos que ya se encuentren en el dispositivo y deberá volver a establecerlos.
acc_dev_addDevIpTip=La nueva dirección IP no puede ser la misma del servidor.
acc_dev_modifyDevIpTip=La nueva dirección del servidor no puede ser la misma que la IP del dispositivo.
acc_dev_setWGReader=Establecer Lector Wiegand
acc_dev_selectReader=Clic para seleccionar el lector
acc_dev_IllegalDevice=Dispositivo inválido
acc_dev_syncTimeWarnTip=La hora de los siguientes dispositivos debe sincronizarse desde el dispositivo maestro.
acc_dev_setTimeZoneWarnTip=La zona horaria de los siguientes dispositivos debe sincronizarse desde el dispositivo maestro.
acc_dev_setDstimeWarnTip=El horario de verano de los siguientes dispositivos debe sincronizarse desde el dispositivo maestro.
acc_dev_networkSegmentSame=Dos adaptadores de red no pueden usar el mismo segmento de red.
acc_dev_upgradeProtocolNoMatch=El protocolo del archivo de actualización no coincide,
acc_dev_ipAddressConflict=Un dispositivo con esa dirección IP ya existe. Modifique la dirección IP del dispositivo y agréguelo nuevamente.
acc_dev_checkServerPortTip=El puerto del servidor configurado es inconsistente con el puerto de comunicación del sistema, lo que puede resultar en la falla de agregar. ¿Continuar funcionando?
acc_dev_clearAdmin=Eliminar permisos de administrador
acc_dev_setDevSate=Configurar estado de Entrada/Salida en el equipo
acc_dev_sureToClear=¿Estas seguro de eliminar el permiso de administrador en el equipo?
acc_dev_hostState=Estado Maestro del equipo.
acc_dev_regDeviceTypeTip=Este dispositivo está restringido y no está permitido agregar directamente. Por favor, póngase en contacto con el proveedor de software!
acc_dev_extBoardType=Tipo de placa de E/S
acc_dev_extBoardTip=Después de la configuración, debe reiniciar el dispositivo para que surta efecto.
acc_dev_extBoardLimit=¡Solo se pueden agregar {0} tarjetas de E / S de este tipo a cada dispositivo!
acc_dev_replace=Reemplazar dispositivo
acc_dev_replaceTip=Después del reemplazo, el dispositivo antiguo no funcionará, ¡tenga cuidado!
acc_dev_replaceTip1=Después del reemplazo, realice la operación "sincronizar todos los datos";
acc_dev_replaceTip2=¡Asegúrese de que el modelo de dispositivo de reemplazo sea el mismo!
acc_dev_replaceTip3=¡Asegúrese de que el dispositivo de reemplazo haya configurado la misma dirección de servidor y puerto que el dispositivo anterior!
acc_dev_replaceFail=¡El tipo de máquina del dispositivo es inconsistente y no se puede reemplazar!
acc_dev_notApb=Este dispositivo no puede realizar antisubmarino de puerta o cabeza de lectura
acc_dev_upResourceFile=Cargar archivos de recursos
acc_dev_playOrder=Orden de reproducción
acc_dev_setFaceServerInfo=Establecer parámetros de comparación de back - end facial
acc_dev_faceVerifyMode=Modo de comparación facial
acc_dev_faceVerifyMode1=Comparación local
acc_dev_faceVerifyMode2=Comparación de back - end
acc_dev_faceVerifyMode3=Prioridad comparativa local
acc_dev_faceBgServerType=Tipo de servidor de back - end facial
acc_dev_faceBgServerType1=Servicios de plataforma de software
acc_dev_faceBgServerType2=Servicios de terceros
acc_dev_isAccessLogic=Activar la verificación lógica de control de acceso
#[5]门-其他关联的也复用此处
acc_door_entity=Puertas
acc_door_number=Número de Puerta
acc_door_name=Nombre de Puerta
acc_door_activeTimeZone=Horario Activo
acc_door_passageModeTimeZone=Horario de Apertura Programada
acc_door_setPassageModeTimeZone=Horario de Apertura Programada Establecido
acc_door_notPassageModeTimeZone=Sin Horario de Apertura Programada
acc_door_lockOpenDuration=Apertura de Puerta
acc_door_entranceApbDuration=Duración de Anti-Passback
acc_door_sensor=Sensor de Puerta
acc_door_sensorType=Sensor de Puerta
acc_door_normalOpen=Normalmente Abierto
acc_door_normalClose=Normalmente Cerrado
acc_door_sensorDelay=Retardo de Sensor de Puerta
acc_door_closeAndReverseState=Cerrar al Detectar Puerta
acc_door_hostOutState=Estado de dispositivo maestro
acc_door_slaveOutState=Estado de dispositivo esclavo
acc_door_inState=Entrada
acc_door_outState=Salida
acc_door_requestToExit=Estado de REX
acc_door_withoutUnlock=Cerrado
acc_door_unlocking=Abierto
acc_door_alarmDelay=Retardo de REX
acc_door_duressPassword=Contraseña de Coacción
acc_door_currentDoor=Puerta Actual
acc_door_allDoorOfCurDev=Todas las puertas de este dispositivo
acc_door_allDoorOfAllDev=Todas las puertas de todos los dispositivos
acc_door_allDoorOfAllControlDev=Todas las puertas de los paneles de acceso
acc_door_allDoorOfAllStandaloneDev=Todas las puertas de las terminales autónomas
acc_door_allWirelessLock=Todas las cerraduras inalámbricas
acc_door_max6BitInteger=Máximo 6 Dígitos
acc_door_direction=Dirección de Acceso
acc_door_onlyInReader=Solo Lector de Entrada
acc_door_bothInAndOutReader=Lector de Entrada y Salida
acc_door_noDoor=Agregue la puerta.
acc_door_nameRepeat=Nombre de puerta ya existe.
acc_door_duressPwdError=La contraseña de coacción no puede ser igual a la contraseña personal.
acc_door_urgencyStatePwd=Introduzca {0} dígitos.
acc_door_noDevOnline=El dispositivo está desconectado o la puerta no soporta el método de verificación.
acc_door_durationLessLock=El sensor de la puerta debe superar el tiempo de apertura de puerta.
acc_door_lockMoreDuration=El tiempo de apertura de puerta debe ser menor que el sensor de puerta.
acc_door_lockAndExtLessDuration=El tiempo y retardo de apertura debe ser menor que el de sensor de puerta.
acc_door_noDevTrigger=No hay dispositivos que coincidan estas condiciones.
acc_door_relay=Relevador
acc_door_pin=ID de Usuario
acc_door_selDoor=Seleccione la Puerta
acc_door_sensorStatus=Sensor de Puerta ({0})
acc_door_sensorDelaySeconds=Retardo ({0} s)
acc_door_timeSeg=Horario ({0})
acc_door_combOpenInterval=Intervalo entre Multi-Usuario
acc_door_delayOpenTime=Retardo de Puerta Abierta
acc_door_extDelayDrivertime=Apertura Extendida
acc_door_enableAudio=Activar Alarmas
acc_door_disableAudio=Desactivar sonidos de alarma
acc_door_lockAndExtDelayTip=La suma del tiempo de apertura de puerta y la apertura extendida no debe ser mayor de 254 segundos.
acc_door_disabled=Las puertas deshabilitadas no pueden ser operadas.
acc_door_offline=Las puertas fuera de línea no pueden ser operadas.
acc_door_notSupport=La puerta no soporta esta característica.
acc_door_select=Seleccione la puerta
acc_door_pushMaxCount=Existen {0} puertas habilitadas en el sistema y ha llegado al límite de la licencia. Contacte al personal de ventas.
acc_door_outNumber=La licencia actual sólo permite agregar {0} puertas adicionales. Seleccione las puertas de nuevo e intente otra vez, o contacte al personal de ventas para adquirir otra licencia.
acc_door_latchTimeZone=Horario de REX
acc_door_wgFmtReverse=Número de Tarjeta Inverso
acc_door_allowSUAccessLock=Permitir acceso de superusuario cuando cierre
acc_door_verifyModeSinglePwd=¡¡ la contraseña no se puede usar como método de verificación independiente!
acc_door_doorPassword=Contraseña de apertura
#辅助输入
acc_auxIn_timeZone=Horario de Entrada Auxiliar
#辅助输出
acc_auxOut_passageModeTimeZone=Horario Normalmente Abierto
acc_auxOut_disabled=Las salidas auxiliares deshabilitadas no pueden ser operadas.
acc_auxOut_offline=Las salidas auxiliares fuera de línea no pueden ser operadas.
#[8]门禁权限组
acc_level_doorGroup=Combinación de Puertas
acc_level_openingPersonnel=Personal de Apertura
acc_level_noDoor=No hay opciones que puedan ser seleccionadas. Agregue un dispositivo.
acc_level_doorRequired=Debe seleccionar una puerta.
acc_level_doorCount=Total de Puertas
acc_level_doorDelete=Eliminar Puerta
acc_level_isAddDoor=¿Desea agregar puertas al nivel de acceso?
acc_level_master=General
acc_level_noneSelect=Agregue un nivel de acceso.
acc_level_useDefaultLevel=¿Cambiar el nivel de acceso al nuevo departamento?
acc_level_persAccSet=Configuración de control de acceso de personal
acc_level_visUsed={0} ya está en uso por el módulo visitante y no se puede eliminar.
acc_level_doorControl=Control de la puerta
acc_level_personExceedMax=El número actual de grupos de permisos ({0}), el número máximo opcional de grupos de permisos ({1})
acc_level_exportLevel=Exportar nivel de acceso
acc_level_exportLevelDoor=Exportar puertas de nivel de acceso
acc_level_exportLevelPerson=Exportar personal de nivel de acceso
acc_level_importLevel=Importar nivel de acceso
acc_level_importLevelDoor=Importar puertas de nivel de acceso
acc_level_importLevelPerson=Importar Personal de Nivel de Acceso
acc_level_exportDoorFileName=Información de puertas de nivel de acceso
acc_level_exportPersonFileName=Información del personal del nivel de acceso
acc_levelImport_nameNotNull=El nombre del nivel de acceso no puede estar vacío
acc_levelImport_timeSegNameNotNull=La zona horaria no puede estar vacía
acc_levelImport_areaNotExist=¡El área no existe!
acc_levelImport_timeSegNotExist=¡La zona horaria no existe!
acc_levelImport_nameExist=¡El nombre del nivel de acceso {0} ya existe!
acc_levelImport_levelDoorExist=¡Las puertas del nivel de acceso {0} ya existen!
acc_levelImport_levelPersonExist=¡El Personal de Nivel de Acceso {0} ya existe!
acc_levelImport_noSpecialChar=¡El nombre del nivel de acceso no puede contener caracteres especiales!
#[10]首人常开
acc_firstOpen_setting=Primera Apertura
acc_firstOpen_browsePerson=Examinar Usuarios
#[11]多人组合开门
acc_combOpen_comboName=Nombre de Combinación
acc_combOpen_personGroupName=Nombre de Grupo
acc_combOpen_personGroup=Grupo Multi-Usuario
acc_combOpen_verifyOneTime=Total de Usuarios
acc_combOpen_eachGroupCount=Número de usuarios de cada grupo
acc_combOpen_group=Grupo
acc_combOpen_changeLevel=Grupo de Apertura de Puerta
acc_combOpen_combDeleteGroup=Combinación de apertura existente. Primero elimine.
acc_combOpen_ownedLevel=Pertenece al Grupo
acc_combOpen_mostPersonCount=El total del grupo no debe ser mayor de 5 usuarios.
acc_combOpen_leastPersonCount=Debe seleccionar al menos 2 usuarios.
acc_combOpen_groupNameRepeat=El nombre del grupo ya existe.
acc_combOpen_groupNotUnique=Los grupos de usuarios no deben ser iguales.
acc_combOpen_persNumErr=El número del grupo excede el valor de su elección, seleccione de nuevo.
acc_combOpen_combOpengGroupPersonShort=Después de remover el personal, el número de usuarios del grupo no es suficiente. Primero elimine el grupo.
acc_combOpen_backgroundVerifyTip=La puerta pertenece a un dispositivo de verificación en segundo plano y no puede utilizar la función multi-usuario al mismo tiempo.
#[12]互锁
acc_interlock_rule=Regla de Esclusamiento
acc_interlock_mode1Or2=Esclusar entre {0} y {1}
acc_interlock_mode3=Esclusar entre {0} y {1} y {2}
acc_interlock_mode4=Esclusar entre {0} y {1} y entre {2} y {3}
acc_interlock_mode5=Esclusar entre {0} y {1} y {2} y {3}
acc_interlock_hasBeenSet=Se ha establecido el esclusamiento.
acc_interlock_group1=Grupo 1
acc_interlock_group2=Grupo 2
acc_interlock_ruleInfo=Bloqueo entre grupos
acc_interlock_alreadyExists=¡Ya existe la misma regla de bloqueo, ¡ no lo agregue repetidamente!
acc_interlock_groupInterlockCountErr=Las reglas de interconexión dentro del Grupo requieren al menos dos puertas
acc_interlock_ruleType=Tipo de regla de interconexión
#[13]反潜
acc_apb_rules=Regla Anti-Passback
acc_apb_reader=Anti-Passback entre lectores de puerta {0}
acc_apb_reader2=Anti-Passback entre los lectores de puertas: {0}, {1}
acc_apb_reader3=Anti-Passback entre los lectores de puertas: {0}, {1}, {2}
acc_apb_reader4=Anti-Passback entre los lectores de puertas: {0}, {1}, {2}, {3}
acc_apb_reader5=Anti-Passback lector de salida en la puerta {0}
acc_apb_reader6=Anti-Passback lector entrada en la puerta {0}
acc_apb_reader7=Anti-Passback en cualquier puerta
acc_apb_twoDoor=Anti-Passback entre {0} y {1}
acc_apb_fourDoor=Anti-Passback entre {0} y {1} y entre {2} y {3}
acc_apb_fourDoor2=Anti-Passback entre {0} ó {1} y {2} ó {3}
acc_apb_fourDoor3=Anti-Passback entre {0} y {1} ó {2}
acc_apb_fourDoor4=Anti-Passback entre {0} y {1} ó {2} ó {3}
acc_apb_hasBeenSet=Se ha establecido Anti-Passback.
acc_apb_conflictWithGapb=El dispositivo contiene ajustes anti-passback, no se pudieron establecer las reglas.
acc_apb_conflictWithApb=La zona del dispositivo tiene ajustes anti-passback, no se pudieron establecer las reglas.
acc_apb_conflictWithEntranceApb=La zona del dispositivo tiene ajustes anti-passback en la entrada, no se pudieron establecer las reglas.
acc_apb_controlIn=Anti-Passback Entrada
acc_apb_controlOut=Anti-Passback Salida
acc_apb_controlInOut=Anti-Passback Entrada/Salida
acc_apb_groupIn=Entrar En El Grupo
acc_apb_groupOut=Fuera Del Grupo
acc_apb_reverseName=Antisubmarino Inverso
acc_apb_door=Antisubmarino Puerta
acc_apb_readerHead=Lectura De La Cabeza Antisubmarino
acc_apb_alreadyExists=¡Ya existen las mismas reglas antisubmarino, ¡ no las agregue repetidamente!
#[17]电子地图
acc_map_addDoor=Agregar Puerta
acc_map_addChannel=Agregar Cámara
acc_map_noAccess=Sin privilegios para el módulo del mapa virtual. Contacte al administrador.
acc_map_noAreaAccess=No tiene permiso de mapa para esta área, ¡contacte al administrador!
acc_map_imgSizeError=La imagen no debe exceder de {0}Mb.
#[18]门禁事件记录
acc_trans_entity=Evento
acc_trans_eventType=Tipo de Evento
acc_trans_firmwareEvent=Eventos de Firmware
acc_trans_softwareEvent=Eventos de Software
acc_trans_today=Eventos de Hoy
acc_trans_lastAddr=Última Posición Registrada
acc_trans_viewPhotos=Ver Fotos
acc_trans_exportPhoto=Exportar fotos
acc_trans_photo=Foto del incidente de control de acceso
acc_trans_dayNumber=Dias
acc_trans_fileIsTooLarge=El archivo exportado es demasiado grande, reduzca el alcance para exportar
#[19]门禁验证方式
acc_verify_mode_onlyface=Rostro
acc_verify_mode_facefp=Rostro+Huella
acc_verify_mode_facepwd=Rostro+Contraseña
acc_verify_mode_facecard=Rostro+Tarjeta
acc_verify_mode_facefpcard=Rostro+Huella+Tarjeta
acc_verify_mode_facefppwd=Rostro+Huella+Contraseña
acc_verify_mode_fv=Vena
acc_verify_mode_fvpwd=Vena+Contraseña
acc_verify_mode_fvcard=Vena+Tarjeta
acc_verify_mode_fvpwdcard=Vena+Contraseña+Tarjeta
acc_verify_mode_pv=Palmar
acc_verify_mode_pvcard=Palmar+Tarjeta
acc_verify_mode_pvface=Palmar+Rostro
acc_verify_mode_pvfp=Palmar+Huella
acc_verify_mode_pvfacefp=Palmar+Rostro+Huella
#[20]门禁事件编号
acc_eventNo_-1=Ninguno
acc_eventNo_0=Apertura con tarjeta
acc_eventNo_1=Lectura de tarjeta durante apertura programada
acc_eventNo_2=Primer apertura con tarjeta
acc_eventNo_3=Verificación multi-usuario con tarjeta
acc_eventNo_4=Apertura con contraseña de emergencia
acc_eventNo_5=Apertura durante horario de apertura programado
acc_eventNo_6=Vínculo Accionado
acc_eventNo_7=Detener Alarma
acc_eventNo_8=Apertura Remota
acc_eventNo_9=Cerrado Remoto
acc_eventNo_10=Desactivar Apertura Programada
acc_eventNo_11=Activar Apertura Programada
acc_eventNo_12=Abrir Salida Auxiliar Remotamente
acc_eventNo_13=Cerrar Salida Auxiliar Remotamente
acc_eventNo_14=Apertura con huella digital
acc_eventNo_15=Verificación multi-usuario con huella digital
acc_eventNo_16=Huella digital durante apertura programada
acc_eventNo_17=Apertura con huella más tarjeta
acc_eventNo_18=Primer apertura con huella digital
acc_eventNo_19=Primer apertura con huella más tarjeta
acc_eventNo_20=Intervalo de operación muy corto
acc_eventNo_21=Lectura de tarjeta durante horario de puerta inactiva
acc_eventNo_22=Fuera de horario permitido
acc_eventNo_23=Acceso denegado
acc_eventNo_24=Anti-passback
acc_eventNo_25=Esclusamiento
acc_eventNo_26=Verificación multi-usuario con tarjetas
acc_eventNo_27=Tarjeta no registrada
acc_eventNo_28=Puerta mantenida abierta
acc_eventNo_29=Tarjeta expirada
acc_eventNo_30=Error de contraseña
acc_eventNo_31=Intervalo de huella muy corto
acc_eventNo_32=Verificación multi-usuario con huellas
acc_eventNo_33=Huella expirada
acc_eventNo_34=Huella deshabilitada
acc_eventNo_35=Lectura de huella durante horario de puerta inactiva
acc_eventNo_36=Botón de salida durante horario de puerta inactiva
acc_eventNo_37=Fallo al cerrar durante apertura programada
acc_eventNo_38=Tarjeta con reporte de pérdida
acc_eventNo_39=Acceso desactivado
acc_eventNo_40=Verificación multi-usuario con huellas fallida
acc_eventNo_41=Error de modo de verificación
acc_eventNo_42=Error de formato Wiegand
acc_eventNo_43=Tiempo agotado en verificación Anti-passback
acc_eventNo_44=Verificación en segundo plano fallida
acc_eventNo_45=Verificación en segundo plano tiempo agotado
acc_eventNo_47=Fallo al enviar el comando
acc_eventNo_48=Verificación multi-usuario con tarjetas fallida
acc_eventNo_49=Lectura de contraseña durante horario de puerta inactiva
acc_eventNo_50=Intervalo de lectura de huella muy corto
acc_eventNo_51=Verificación multi-usuario con contraseñas
acc_eventNo_52=Verificación multi-usuario con contraseñas fallida
acc_eventNo_53=Contraseña expirada
acc_eventNo_100=Alarma de tamper
acc_eventNo_101=Apertura con contraseña de coacción
acc_eventNo_102=Puerta forzada
acc_eventNo_103=Apertura con huella de coacción
acc_eventNo_200=Puerta abierta correctamente
acc_eventNo_201=Puerta cerrada correctamente
acc_eventNo_202=Apertura con botón de salida
acc_eventNo_203=Verificación multi-usuario con huella más tarjeta
acc_eventNo_204=Apertura programada finalizada
acc_eventNo_205=Apertura remota normalmente abierto
acc_eventNo_206=Equipo Inicializado
acc_eventNo_207=Apertura con contraseña
acc_eventNo_208=Apertura de puerta de superusuario
acc_eventNo_209=Boton de salida activado (sin bloqueo)
acc_eventNo_210=Activar puerta de emergencia
acc_eventNo_211=Cierre de puertas de superusuario
acc_eventNo_212=Activar función de control de elevador
acc_eventNo_213=Desactivar función de control de elevador
acc_eventNo_214=Verificación multi-usuario con contraseñas
acc_eventNo_215=Primera aperura con contraseña
acc_eventNo_216=Contraseña durante horario de apertura programada
acc_eventNo_220=Entrada auxiliar desconectada
acc_eventNo_221=Entrada auxiliar conectada
acc_eventNo_222=Verificación en segundo plano exitosa
acc_eventNo_223=Verificación en segundo plano fallida
acc_eventNo_225=Entrada auxiliar normal
acc_eventNo_226=Entrada auxiliar activada
acc_newEventNo_0=Apertura con verificación normal
acc_newEventNo_1=Verificación con tarjeta durante apertura programada
acc_newEventNo_2=Primera apertura con tarjeta
acc_newEventNo_3=Verificación multi-usuario con tarjeta
acc_newEventNo_20=Intervalo de operación muy corto
acc_newEventNo_21=Apertura de puerta durante puerta inactiva
acc_newEventNo_26=Verificando multi-usuario
acc_newEventNo_27=Usuario no registrado
acc_newEventNo_29=Usuario expirado
acc_newEventNo_30=Error de contraseña
acc_newEventNo_41=Error de modo de verificación
acc_newEventNo_43=Bloqueo de personal
acc_newEventNo_44=Verificación en segundo plano fallida
acc_newEventNo_45=Verificación en segundo plano - tiempo agotado
acc_newEventNo_48=Verificación multi-usuario fallida
acc_newEventNo_54=Batería baja
acc_newEventNo_55=Remplazar batería lo antes posible
acc_newEventNo_56=Operación no permitida
acc_newEventNo_57=Energía de reserva
acc_newEventNo_58=Alarma normalmente abierta
acc_newEventNo_59=Operación no permitida
acc_newEventNo_60=Puerta bloqueada por dentro
acc_newEventNo_61=Replicado
acc_newEventNo_62=Usuarios prohibidos
acc_newEventNo_63=Puerta bloqueada
acc_newEventNo_64=Botón de salida en periodo de inactividad
acc_newEventNo_65=Salida auxiliar en periodo de inactividad
acc_newEventNo_66=Actualización de lector fallida
acc_newEventNo_67=Comparación a distancia exitosa (el dispositivo no está autorizado)
acc_newEventNo_68=Temperatura corporal alta - acceso denegado
acc_newEventNo_69=Sin máscara - acceso denegado
acc_newEventNo_70=La relación de cara se comunica anormalmente con el servidor
acc_newEventNo_71=El servidor de rostros respondió anormalmente
acc_newEventNo_73=Código QR no válido
acc_newEventNo_74=Código QR caducado
acc_newEventNo_101=Alarma de apertura por coacción
acc_newEventNo_104=Alarma de lectura de tarjeta inválida
acc_newEventNo_105=No se puede conectar con el servidor
acc_newEventNo_106=Sin energía
acc_newEventNo_107=Bateria baja
acc_newEventNo_108=No se pudo conectar con el dispositivo maestro
acc_newEventNo_109=Alarma de sabotaje del lector
acc_newEventNo_110=Lector fuera de línea
acc_newEventNo_112=La placa de expansión está fuera de línea
acc_newEventNo_114=Entrada de alarma de incendios desconectada (detección de línea)
acc_newEventNo_115=Cortocircuito de entrada de alarma de incendios (detección de línea)
acc_newEventNo_116=Entrada auxiliar desconectada (detección de línea)
acc_newEventNo_117=Cortocircuito de entrada auxiliar (detección de línea)
acc_newEventNo_118=Interruptor de salida apagado (detección de línea)
acc_newEventNo_119=Cortocircuito en el interruptor de salida (detección de línea)
acc_newEventNo_120=Desconexión magnética de puerta (detección de línea)
acc_newEventNo_121=Cortocircuito magnético de puerta (detección de línea)
acc_newEventNo_159=Control Remoto de Puerta
acc_newEventNo_214=Conectado con el servidor
acc_newEventNo_217=Conexión con dispositivo maestro exitosa
acc_newEventNo_218=Verificación de Tarjeta
acc_newEventNo_222=Verificación en segundo plano exitosa
acc_newEventNo_223=Verificación en segundo plano
acc_newEventNo_224=Timbrar
acc_newEventNo_227=Doble apertura de puerta
acc_newEventNo_228=Doble cierre de puerta
acc_newEventNo_229=Salida auxiliar N.A. temporizada
acc_newEventNo_230=Salida auxiliar temporizada
acc_newEventNo_232=Verificación Exitosa
acc_newEventNo_233=Activar Bloqueo
acc_newEventNo_234=Desactivar Bloqueo
acc_newEventNo_235=Actualización de lector exitosa
acc_newEventNo_236=Alarma de sabotaje del lector borrada
acc_newEventNo_237=Lector en línea
acc_newEventNo_239=Llamada al dispositivo
acc_newEventNo_240=Llamada finalizada
acc_newEventNo_243=Entrada de alarma de incendios desactivada
acc_newEventNo_244=Cortocircuito de entrada de alarma de incendios
acc_newEventNo_247=Tablero de expansión en línea
acc_newEventNo_4008=Recuperación de red
acc_newEventNo_4014=La señal de entrada de incendios está desconectada y la puerta final suele abrirse
acc_newEventNo_4015=La puerta está en línea
acc_newEventNo_4018=Comparación de Backend Abrir
acc_newEventNo_5023=Estado de incendio limitado
acc_newEventNo_5024=Horas extras de verificación de varias personas
acc_newEventNo_5029=Falló la comparación del backend
acc_newEventNo_6005=La capacidad de registro está llegando al límite
acc_newEventNo_6006=Cortocircuito en línea (rs485)
acc_newEventNo_6007=Cortocircuito en línea (wegen)
acc_newEventNo_6011=La puerta está fuera de línea
acc_newEventNo_6012=Alarma de demolición de puertas
acc_newEventNo_6013=La señal de entrada de incendios se activa y la puerta se abre normalmente
acc_newEventNo_6015=Restablecer la fuente de alimentación del equipo de expansión
acc_newEventNo_6016=Configuración de fábrica de la máquina de recuperación
acc_newEventNo_6070=Comparación de backend (lista prohibida)
acc_eventNo_undefined=Número de eventos indefinido
acc_advanceEvent_500=Anti-passback global (lógico)
acc_advanceEvent_501=Validación de personal (uso de fecha)
acc_advanceEvent_502=Número de validación de control
acc_advanceEvent_503=Esclusamiento global
acc_advanceEvent_504=Control de Ruta
acc_advanceEvent_505=Anti-passback global (por tiempo)
acc_advanceEvent_506=Anti-passback global (lógico por tiempo)
acc_advanceEvent_507=Validación de personal (después del primer uso días válido)
acc_advanceEvent_508=Validación de personal (uso número de veces)
acc_advanceEvent_509=Verificación en segundo plano fallida (personal no registrado)
acc_advanceEvent_510=Verificación en segundo plano fallida (excepción de datos)
acc_alarmEvent_701=Violación de DMR (reglas: {0})
#[21]实时监控
acc_rtMonitor_openDoor=Abrir
acc_rtMonitor_closeDoor=Cerrar
acc_rtMonitor_remoteNormalOpen=Normalmente Abierto Remoto
acc_rtMonitor_realTimeEvent=Eventos en Tiempo Real
acc_rtMonitor_photoMonitor=Monitoreo de Fotos
acc_rtMonitor_alarmMonitor=Monitoreo de Alarmas
acc_rtMonitor_doorState=Estado de Puerta
acc_rtMonitor_auxOutName=Nombre de Salida Auxiliar
acc_rtMonitor_nonsupport=No Soportado
acc_rtMonitor_lock=Bloqueado
acc_rtMonitor_unLock=Desbloqueado
acc_rtMonitor_disable=Deshabilitado
acc_rtMonitor_noSensor=Sin Sensor de Puerta
acc_rtMonitor_alarm=Alarma
acc_rtMonitor_openForce=Puerta forzada
acc_rtMonitor_tamper=Tamper
acc_rtMonitor_duressPwdOpen=Apertura con Contraseña de Coacción
acc_rtMonitor_duressFingerOpen=Apertura con Huella de Coacción
acc_rtMonitor_duressOpen=Apertura de Coacción
acc_rtMonitor_openTimeout=Puerta Mantenida Abierta
acc_rtMonitor_unknown=Desconocido
acc_rtMonitor_noLegalDoor=No existen puertas que cumplan la condición.
acc_rtMonitor_noLegalAuxOut=No existen salidas auxiliares que cumplan la condición.
acc_rtMonitor_curDevNotSupportOp=El estado actual del dispositivo no soporta esta operación.
acc_rtMonitor_curNormalOpen=Actualmente normalmente abierto
acc_rtMonitor_whetherDisableTimeZone=El estado actual de la puerta es siempre abierta.
acc_rtMonitor_curSystemNoDoors=No existen puertas agregadas o ninguna puerta cumple sus requerimientos.
acc_rtMonitor_cancelAlarm=Detener Alarma
acc_rtMonitor_openAllDoor=Abrir todas las puertas
acc_rtMonitor_closeAllDoor=Cerrar todas las puertas
acc_rtMonitor_confirmCancelAlarm=¿Está seguro de detener la alarma?
acc_rtMonitor_calcelAllDoor=Detener todas las alarmas
acc_rtMonitor_initDoorStateTip=Obteniendo todas las puertas autorizadas a usuarios en el sistema...
acc_rtMonitor_alarmEvent=Evento de Alarma
acc_rtMonitor_ackAlarm=Confirmar
acc_rtMonitor_ackAllAlarm=Confirmar Todas
acc_rtMonitor_ackAlarmTime=Hora de Confirmación
acc_rtMonitor_sureToAckThese=¿Está seguro de detener la alarma {0}? Después de confirmar se detendrá la alarma.
acc_rtMonitor_sureToAckAllAlarm=¿Está seguro de detener todas las alarmas? Después de confirmar se detendrán todas las alarmas.
acc_rtMonitor_noSelectAlarmEvent=Seleccione para confirmar el evento de alarma.
acc_rtMonitor_noAlarmEvent=No hay eventos de alarma en el sistema.
acc_rtMonitor_forcefully=Detener alarma (puerta forzada)
acc_rtMonitor_addToRegPerson=Agregar a Usuario Registrado
acc_rtMonitor_cardExist=La tarjeta ya esta asignada a {0} y no se puede duplicar.
acc_rtMonitor_opResultPrompt={0} solicitudes enviadas exitasamente. Fallidas: {1}
acc_rtMonitor_doorOpFailedPrompt=No se pudo enviar la solicitud a las puertas, intente de nuevo.
acc_rtMonitor_remoteOpen=Apertura Remota
acc_rtMonitor_remoteClose=Cerrado Remoto
acc_rtMonitor_alarmSoundClose=Audio Cerrado
acc_rtMonitor_alarmSoundOpen=Audio Abierto
acc_rtMonitor_playAudio=Reproducir Audio
acc_rtMonitor_isOpenShowPhoto=Activar Mostrar Foto
acc_rtMonitor_isOpenPlayAudio=Activar Alerta de Audio
acc_rtm_open=Apertura Remota de Botón
acc_rtm_close=Cerrado Remoto de Botón
acc_rtm_eleModule=Elevador
acc_cancelAlarm_fp=Detener alarma (apertura con huella de coacción)
acc_cancelAlarm_pwd=Detener alarma (apertura con contraseña de coacción)
acc_cancelAlarm_timeOut=Detener alarma (puerta mantenida abierta)
#定时同步设备时间
acc_timing_syncDevTime=Sincronizando tiempo con el equipo
acc_timing_executionTime=Tiempo de Ejecución
acc_timing_theLifecycle=Ciclo de Vida
acc_timing_errorPrompt=Introduzca un número entre 1-31.
acc_timing_checkedSyncTime=Seleccione una hora de sincronización.
#[25]门禁报表
acc_trans_hasAccLevel=Tiene Acceso a:
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Agregar Zona
acc_zone_code=ID de Zona
acc_zone_parentZone=Zona Superior
acc_zone_parentZoneCode=ID de Zona Superior
acc_zone_parentZoneName=Nombre de Zona Superior
acc_zone_outside=fuera
#[G2]读头定义
acc_readerDefine_readerName=Nombre de Lector
acc_readerDefine_fromZone=Va de
acc_readerDefine_toZone=A
acc_readerDefine_delInfo1=La definición de zona de éstos lectores es referido por una función de control de acceso avanzado y no se puede eliminar.
acc_readerDefine_selReader=Seleccione el lector.
acc_readerDefine_selectReader=Agregue un lector.
acc_readerDefine_tip=Después que el personal llegue a la zona exterior, el registro del personal será eliminado.
#[G3]全局反潜
acc_gapb_zone=Zona
acc_gapb_whenToResetGapb=Reinicio de Anti-passback
acc_gapb_apbType=Tipo de Anti-passback
acc_gapb_logicalAPB=Anti-passback Lógico
acc_gapb_timedAPB=Anti-passback Temporizado
acc_gapb_logicalTimedAPB=Anti-passback Logico Temporizado
acc_gapb_lockoutDuration=Duración de Desbloqueo
acc_gapb_devOfflineRule=Si el equipo esta fuera de línea
acc_gapb_standardLevel=Nivel de Acceso Estándar
acc_gapb_accessDenied=Acceso Denegado
acc_gapb_doorControlZone=Las siguientes puertas controlan el acceso de entrada y salida de la zona
acc_gapb_resetStatus=Reiniciar Estado Anti-passback
acc_gapb_obeyAPB=Obedecer Reglas Anti-passback
acc_gapb_isResetGAPB=Reiniciar Anti-passback
acc_gapb_resetGAPBSuccess=Reinicio del estado Anti-passback completado.
acc_gapb_resetGAPBFaile=Fallo al reiniciar estado Anti-passback.
acc_gapb_chooseArea=Seleccione el área.
acc_gapb_notDelInfo1=La zona de acceso referida como área superior no se puede eliminar.
acc_gapb_notDelInfo2=La zona es referida en la definición de lectores y no se puede eliminar.
acc_gapb_notDelInfo3=La zona es referida por una función de control de acceso avanzado y no se puede eliminar.
acc_gapb_notDelInfo4=El LED hace referencia al área de acceso y no se puede eliminar.
acc_gapb_zoneNumRepeat=El ID de la zona ya existe.
acc_gapb_zoneNameRepeat=El nombre de la zona ya existe.
acc_gapb_personResetGapbPre=¿Está seguro de reiniciar?
acc_gapb_personResetGapbSuffix=Reglas Anti-passback del Personal
acc_gapb_apbPrompt=Una puerta no se puede utilizar para controlar dos límites Anti-Passback independientes
acc_gapb_occurApb=Eventos de Anti-passback
acc_gapb_noOpenDoor=No abra la puerta
acc_gapb_openDoor=Abra la puerta
acc_gapb_zoneNumLength=Longitud superior a 20 caracteres
acc_gapb_zoneNameLength=Longitud superior a 30 caracteres
acc_gapb_zoneRemarkLength=Longitud superior a 50 caracteres
acc_gapb_isAutoServerMode=Detección de un equipo sin la función de verificación en segundo activada, podría afectar a la función. ¿Desea activarla?
acc_gapb_applyTo=Aplicar para
acc_gapb_allPerson=Todo el personal
acc_gapb_justSelected=Sólo personal seleccionado
acc_gapb_excludeSelected=Excluir personal seleccionado
#[G4]who is inside
acc_zoneInside_lastAccessTime=Último Acceso
acc_zoneInside_lastAccessReader=Último Lector de Acceso
acc_zoneInside_noPersonInZone=No hay usuarios en la zona.
acc_zoneInside_noRulesInZone=No se han establecido reglas.
acc_zoneInside_totalPeople=Total de Personas
acc_zonePerson_selectPerson=Por favor seleccione una persona o departamento!
#[G5]路径
acc_route_name=Nombre de la ruta
acc_route_setting=Configuración de Ruta
acc_route_addReader=Agregar lector
acc_route_delReader=Borrar Lector
acc_route_defineReaderLine=Definir línea del lector
acc_route_up=Arriba
acc_route_down=Abajo
acc_route_selReader=Seleccione el lector después de la operación
acc_route_onlyOneOper=Solo se puede seleccionar un lector para operar
acc_route_readerOrder=Secuencia definida de lectores
acc_route_atLeastSelectOne=Por favor seleccione al menos una cabeza lectora!
acc_route_routeIsExist=¡Esta ruta ya existe!
#[G6]DMR
acc_dmr_residenceTime=Tiempo de estadía
acc_dmr_setting=Configuración DMR
#[G7]Occupancy
acc_occupancy_max=Capacidad máxima
acc_occupancy_min=Capacidad mínima
acc_occupancy_unlimit=Ilimitado
acc_occupancy_note=¡La capacidad máxima debe ser mayor que la capacidad mínima!
acc_occupancy_containNote=Por favor, introduzca al menos una de la capacidad máxima / mínima!
acc_occupancy_maxMinValid=Introduzca un número mayor que cero.
acc_occupancy_conflict=Las reglas de control de ocupación se han establecido en esta zona.
acc_occupancy_maxMinTip=Ningún valor de capacidad significa ninguna limitación.
#card availability
acc_personLimit_zonePropertyName=Nombre de la propiedad de zona
acc_personLimit_useType=Uso
acc_personLimit_userDate=Vigencia
acc_personLimit_useDays=Válido después del primer día
acc_personLimit_useTimes=Frecuencia de Uso
acc_personLimit_setZoneProperty=Ajustes de Zona
acc_personLimit_zoneProperty=Propiedades de Zona
acc_personLimit_availabilityName=Nombre Disponible
acc_personLimit_days=Días
acc_personLimit_Times=Veces
acc_personLimit_noDel=Las propiedades de control de acceso de zona están en uso y no se pueden eliminar.
acc_personLimit_cannotEdit=Esta propiedad de zona de control de acceso está referenciada y no puede modificar el tipo de uso!
acc_personLimit_detail=Detalle
acc_personLimit_userDateTo=Válido Hasta
acc_personLimit_addPersonRepeatTip=La persona del departamento seleccionado se ha agregado al atributo del área de control de acceso, ¡seleccione el departamento nuevamente!
acc_personLimit_leftTimes=Quedan {0} veces
acc_personLimit_expired=Caducada
acc_personLimit_unused=No usado
#全局互锁
acc_globalInterlock_addGroup=Agregar Grupo
acc_globalInterlock_delGroup=Borrar Grupo
acc_globalInterlock_refuseAddGroupMessage=No se puede duplicar la puerta en el mismo grupo
acc_globalInterlock_refuseAddlockMessage=La puerta ha aparecido en otros grupos de esclusamiento
acc_globalInterlock_refuseDeleteGroupMessage=Elimine los datos de esclusamiento relacionados
acc_globalInterlock_isGroupInterlock=Grupo de Esclusamiento
acc_globalInterlock_isAddTheDoorImmediately=Agregar puerta inmediatamente
acc_globalInterlock_isAddTheGroupImmediately=Agregar grupo inmediatamente
#门禁参数设置
acc_param_autoEventDev=Descargar automáticamente el registro de equipo de procesamiento hilo de eventos
acc_param_autoEventTime=Descargar automáticamente el hilo intervalo de registro de eventos
acc_param_noRepeat=La dirección de email no se puede repetir. Intente de nuevo.
acc_param_most18=Agregue hasta 18 direcciones de email.
acc_param_deleteAlert=No se puede borrar el campo de dirección de email.
acc_param_invalidOrRepeat=Formato de email incorrecto o email repetido.
#全局联动
acc_globalLinkage_noSupport=La puerta seleccionada actualmente no admite las funciones de bloqueo y desbloqueo.
acc_globalLinkage_trigger=Activar Vínculos Globales
acc_globalLinkage_noAddPerson=El vínculo no está relacionado a un usuario por lo que aplicará a todo el personal.
acc_globalLinkage_selectAtLeastOne=¡Seleccione al menos una operación de activación de enlace!
acc_globalLinkage_selectTrigger=Agregue las condiciones del vínculo.
acc_globalLinkage_selectInput=Agregue el punto de entrada.
acc_globalLinkage_selectOutput=Agregue el punto de Salida.
acc_globalLinkage_audioRemind=Mensajes Vínculos de Audio
acc_globalLinkage_audio=Vínculo de Audio
acc_globalLinkage_isApplyToAll=Aplicar a Todos los Usuarios
acc_globalLinkage_scope=Rango de Usuario
acc_globalLinkage_everyPerson=Cualquiera
acc_globalLinkage_selectedPerson=Seleccionado
acc_globalLinkage_noSupportPerson=No Soportado
acc_globalLinkage_reselectInput=Las condiciones del vínculo han cambiado. Seleccione el punto de inicio nuevamente.
acc_globalLinkage_addPushDevice=Agregue dispositivos que soporten esta característica.
#其他
acc_InputMethod_tips=Cambie a Español.
acc_device_systemCheckTip=No hay dispositivos de acceso.
acc_notReturnMsg=No ha regresado ningún mensaje.
acc_validity_period=La licencia ha expirado y la función ya no está disponible.
acc_device_pushMaxCount=Sistema ya existe en {0} dispositivo(s), llegar a límite de la licencia, No se puede agregar dispositivos!
acc_device_videoHardwareLinkage=Conjunto de Hardware de Vídeo de Vinculación
acc_device_videoCameraIP=IP de Cámara de Video
acc_device_videoCameraPort=Puerto de Cámara de Video
acc_location_unable=El punto del evento no fue agregado al mapa virtual, no se pudo determinar la ubicación.
acc_device_wgDevMaxCount=El dispositivo ha alcanzado el límite de licencia. No se puede modificar la configuración.
#自定义报警事件
acc_deviceEvent_selectSound=Seleccione los archivos de audio
acc_deviceEvent_batchSetSoundErr=Error en lote de archivos
acc_deviceEvent_batchSet=Sonido
acc_deviceEvent_sound=Sonido del Evento
acc_deviceEvent_exist=Ya existe
acc_deviceEvent_upload=Subir
#查询门最近发生事件
acc_doorEventLatestHappen=Consultar los Últimos Eventos
#门禁人员信息
acc_pers_delayPassage=Apertura Extendida
#设备容量提示
acc_dev_usageConfirm=Capacidad de Equipo Mayor al 90%
acc_dev_immediateCheck=Comprobar Ahora
acc_dev_inSoftware=En Software
acc_dev_inFirmware=En Firmware
acc_dev_get=Obtener
acc_dev_getAll=Obtener Todo
acc_dev_loadError=Carga Fallida
#Reader
acc_reader_inout=Entrada/Salida
acc_reader_lightRule=Reglas de Luces
acc_reader_defLightRule=Regla Predeterminada
acc_reader_encrypt=Encriptar
acc_reader_allReaderOfCurDev=Todos los Lectores del Dispositivo Actual
acc_reader_tip1=La encriptación aplicará a todos los lectores del dispositivo actual.
acc_reader_tip2=La opción de modo ID solo está disponible para los cabezales de lectura que admiten esta función.
acc_reader_tip3=El tipo de protocolo RS485 se copia a todos los lectores en el dispositivo actual. ¡La configuración tendrá efecto después de que el dispositivo se reinicie!
acc_reader_tip4=¡La opción para ocultar cierta información personal se copia a todos los lectores del mismo dispositivo de forma predeterminada!
acc_reader_commType=Tipo de Comunicación
acc_reader_commAddress=Dirección
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=Deshabilitado
acc_readerComAddress_repeat=Dirección duplicada
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=Direccion RS485
acc_readerCommType_wgAddress=Dirección Wiegand
acc_reader_macError=Introduzca la dirección MAC en el formato correcto.
acc_reader_machineType=Tipo de Lector
acc_reader_readMode=Modo
acc_reader_readMode_normal=Modo Normal
acc_reader_readMode_idCard=Modo de Tarjeta
acc_reader_note=Consejos: Solo se puede seleccionar los dispositivos de control en la misma área ({0}) que la cámara.
acc_reader_rs485Type=Tipo de protocolo RS485
acc_reader_userLock=Bloqueo de acceso de personal
acc_reader_userInfoReveal=Ocultar información de personal de piezas
#operat
acc_operation_pwd=Contraseña de Operación
acc_operation_pwd_error=Error de contraseña
acc_new_input_not_same=La contraseña nueva no coincide
acc_op_set_keyword=Ajustes de Licencia
acc_op_old_key=Licencia Antigua
acc_op_new_key=Licencia Nueva
acc_op_cofirm_key=Confirmar Licencia
acc_op_old_key_error=Error en licencia antigua
#验证方式规则
acc_verifyRule_name=Nombre de la Regla
acc_verifyRule_door=Verificación de Puerta
acc_verifyRule_person=Verificación del Usuario
acc_verifyRule_copy=Copiar el Lunes a los Demás Días de la Semana
acc_verifyRule_tip1=Seleccione al menos un modo de verificación.
acc_verifyRule_tip2=Si la regla contiene el modo de verificación de usuario no es posible agregar una puerta con lector RS485, cambie el lector por uno Wiegand.
acc_verifyRule_tip3=El lector RS485 debe seguir el modo de verificación de la puerta, no soporta modo de verificación del personal.
acc_verifyRule_oldVerifyMode=Modo de verificación anterior
acc_verifyRule_newVerifyMode=Nuevo modo de verificación
acc_verifyRule_newVerifyModeSelectTitle=Seleccione un nuevo método de verificación
acc_verifyRule_newVerifyModeNoSupportTip=¡No hay ningún dispositivo que admita el nuevo método de verificación!
#Wiegand Test
acc_wiegand_beforeCard=La longitud de la tarjeta nueva ({0} bits) no es igual a la última tarjeta.
acc_wiegand_curentCount=La longitud del número de tarjeta actual es: {0} Bits
acc_wiegand_card=Tarjeta
acc_wiegand_readCard=Leer Tarjeta
acc_wiegand_clearCardInfo=Limpiar Información de Tarjeta
acc_wiegand_originalCard=Número Original de la Tarjeta
acc_wiegand_recommendFmt=Formato Recomendado
acc_wiegand_parityFmt=Formato de Paridad Impar-Par
acc_wiegand_withSizeCode=Auto calcular código de sitio si el código de sitio es nulo
acc_wiegand_tip1=Estas tarjetas puede que no pertenecezcan al mismo lote de tarjetas.
acc_wiegand_tip2=Código del sitio:{0}, Número de la tarjeta:{1}, falló en coincidir con el número de la tarjeta original.Por favor, compruebe el código del sitio y el número de tarjeta de nuevo!
acc_wiegand_tip3=El número ({0}) de tarjeta introducido no se puede emparejar en el número de tarjeta original. Compruebe por favor otra vez!
acc_wiegand_tip4=El código de tamaño introducido ({0}) no se puede emparejar en el número de tarjeta original. Compruebe otra vez!
acc_wiegand_tip5=Si desea utilizar esta función, mantenga todas las columnas del código del sitio vacías.
acc_wiegand_warnInfo1=Cuando continúe leyendo una nueva tarjeta, cambie manualmente la siguiente tarjeta.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Tablero de Personal
acc_LCDRTMonitor_current=Información Personal Actual
acc_LCDRTMonitor_previous=Información Personal Antiguo
#api
acc_api_levelIdNotNull=El ID del grupo de privilegios no puede estar vacío
acc_api_levelExist=El grupo de permisos existe
acc_api_levelNotExist=El grupo de permisos no existe
acc_api_areaNameNotNull=La zona no puede estar vacía
acc_api_levelNotHasPerson=No hay personas bajo el permiso del grupo
acc_api_doorIdNotNull=La identificación de la puerta no puede estar vacía
acc_api_doorNameNotNull=El nombre de la puerta no puede estar vacío
acc_api_doorIntervalSize=Puerta Abierta debe durar entre 1 ~ 254
acc_api_doorNotExist=Puerta inexistente
acc_api_devOffline=El dispositivo está desconectado o deshabilitado
acc_api_devSnNotNull=El dispositivo SN no puede estar vacío
acc_api_timesTampNotNull=La marca de tiempo no puede estar vacía
acc_api_openingTimeCannotBeNull=El tiempo de apertura de la puerta no puede estar vacío
acc_api_parameterValueCannotBeNull=El valor del parámetro no puede estar vacío
acc_api_deviceNumberDoesNotExist=El número de serie del dispositivo no existe
acc_api_readerIdCannotBeNull=El ID del lector no puede estar vacío
acc_api_theReaderDoesNotExist=La cabeza de lectura no existe
acc_operate_door_notInValidDate=Actualmente no está dentro del horario de apertura de puertas remotas, por favor póngase en contacto con el administrador si es necesario!
acc_api_doorOffline=La puerta está fuera de línea o deshabilitada
#门禁信息自动导出
acc_autoExport_title=Exportación automática de información
acc_autoExport_frequencyTitle=Frecuencia de exportación automática
acc_autoExport_frequencyDay=Todos los dias
acc_autoExport_frequencyMonth=Mensual
acc_autoExport_firstDayMonth=Primer dia del mes
acc_autoExport_specificDate=Fecha de exportación
acc_autoExport_exportModeTitle=Modo de exportación
acc_autoExport_dailyMode=Información diaria
acc_autoExport_monthlyMode=Información mensual(Toda la información e información este mes del mes pasado)
acc_autoExport_allMode=Toda la información(Hasta 30,000 mensajes)
acc_autoExport_recipientMail=Recibiendo buzón
#First In And Last Out
acc_inOut_inReaderName=Nombre de lector de entrada más antiguo
acc_inOut_firstInTime=Hora de entrada más temprana
acc_inOut_outReaderName=Apellido del último nombre del lector
acc_inOut_lastOutTime=Último tiempo de licencia
#防疫参数
acc_dev_setHep=Config. de Detección de Máscara y Temperatura
acc_dev_enableIRTempDetection=Habilitar la detección de temperatura con infrarrojos
acc_dev_enableNormalIRTempPass=Denegar acceso cuando la temperatura está por encima del rango
acc_dev_enableMaskDetection=Habilitar detección de máscara
acc_dev_enableWearMaskPass=Denegar acceso sin máscara
acc_dev_tempHighThreshold=Umbral de alarma de alta temperatura
acc_dev_tempUnit=Unidad de temperatura
acc_dev_tempCorrection=Corrección de desviación de temperatura
acc_dev_enableUnregisterPass=Permitir el acceso de personas no registradas
acc_dev_enableTriggerAlarm=Activar alarma externa
#联动邮件
acc_mail_temperature=Temperatura corporal
acc_mail_mask=Ya sea para usar la máscara
acc_mail_unmeasured=No medido
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Eventos globales de Digifort
acc_digifort_chooseDigifortEvents=Elija Digifort Global Events
acc_digifort_eventExpiredTip=Si el evento global se elimina del servidor Digifort, estará en rojo.
acc_digifort_checkConnection=Verifique si la información de conexión del servidor Digifort es correcta.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Añadir contactos
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=Establecer parámetros extendidos
acc_extendParam_faceUI=Pantalla de interfaz
acc_extendParam_faceParam=Parámetros faciales
acc_extendParam_accParam=Parámetros de control de acceso
acc_extendParam_intercomParam=Parámetros de intercomunicación visual
acc_extendParam_volume=Volumen
acc_extendParam_identInterval=Intervalo de identificación (ms)
acc_extendParam_historyVerifyResult=Mostrar resultados de verificación histórica
acc_extendParam_macAddress=Mostrar dirección MAC
acc_extendParam_showIp=Mostrar dirección IP
acc_extendParam_24HourFormat=Mostrar formato de 24 horas
acc_extendParam_dateFormat=Formato de fecha
acc_extendParam_1NThreshold=1: umbral N
acc_extendParam_facePitchAngle=ángulo de inclinación de la cara
acc_extendParam_faceRotationAngle=Ángulo de rotación de la cara
acc_extendParam_imageQuality=Calidad de imagen
acc_extendParam_miniFacePixel=Pixel de cara mínima
acc_extendParam_biopsy=Habilitar biopsia
acc_extendParam_showThermalImage=Mostrar imagen térmica
acc_extendParam_attributeAnalysis=Habilitar análisis de atributos
acc_extendParam_temperatureAttribute=Atributo de detección de temperatura
acc_extendParam_maskAttribute=Atributo de detección de máscara
acc_extendParam_minTemperature=Temperatura mínima
acc_extendParam_maxTemperature=Temperatura máxima
acc_extendParam_gateMode=Modo de puerta
acc_extendParam_qrcodeEnable=Activar la función de código QR
#可视对讲
acc_dev_intercomServer=Dirección de servicio de intercomunicación visual
acc_dev_intercomPort=Servidor de walkie - talkie visual
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Sincronizar Niveles
# 夏令时名称
acc_dsTimeUtc_none=No establecer
acc_dsTimeUtc_AreaNone=No hay horario de verano en esta área
acc_dsTimeUtc1000_0=Canberra, Melbourne, Sydney
acc_dsTimeUtc1000_1=Hobart
acc_dsTimeUtc_0330_0=Terranova
acc_dsTimeUtc_1000_0=Islas Aleutianas
acc_dsTimeUtc_0200_0=Atlántico medio - antiguo
acc_dsTimeUtc0930_0=Adelaida
acc_dsTimeUtc_0100_0=Azores
acc_dsTimeUtc_0400_0=Hora del Atlántico (Canadá)
acc_dsTimeUtc_0400_1=Santiago
acc_dsTimeUtc_0400_2=Asunción
acc_dsTimeUtc_0300_0=Groenlandia
acc_dsTimeUtc_0300_1=San Pedro y Miquelón
acc_dsTimeUtc0200_0=Chisinau
acc_dsTimeUtc0200_1=Helsinki, Kiev, Riga, Sofía, Tallinn, Vilnius
acc_dsTimeUtc0200_2=Atenas, Bucarest
acc_dsTimeUtc0200_3=Jerusalén
acc_dsTimeUtc0200_4=Ammán
acc_dsTimeUtc0200_5=Beirut
acc_dsTimeUtc0200_6=Damasco
acc_dsTimeUtc0200_7=Hebrón, Gaza
acc_dsTimeUtc0200_8=Juba
acc_dsTimeUtc_0600_0=Hora central (Estados Unidos y Canadá)
acc_dsTimeUtc_0600_1=Guadalajara, ciudad de México, Monterrey
acc_dsTimeUtc_0600_2=Isla de Pascua
acc_dsTimeUtc1300_0=Samoa
acc_dsTimeUtc_0500_0=La Habana
acc_dsTimeUtc_0500_1=Hora del Este (Estados Unidos y Canadá)
acc_dsTimeUtc_0500_2=Haití
acc_dsTimeUtc_0500_3=Indiana (East)
acc_dsTimeUtc_0500_4=Islas Turcas y Caicos
acc_dsTimeUtc_0800_0=Hora del Pacífico (Estados Unidos y Canadá)
acc_dsTimeUtc_0800_1=Baja California
acc_dsTimeUtc0330_0=Teherán
acc_dsTimeUtc0000_0=Dublín, Edimburgo, Lisboa, Londres
acc_dsTimeUtc1200_0=Fiji
acc_dsTimeUtc1200_1=Petropavlovsk Kamchatka Old Usage
acc_dsTimeUtc1200_2=Wellington, Auckland
acc_dsTimeUtc1100_0=Isla Norfolk
acc_dsTimeUtc_0700_0=Chihuahua, la paz, massatlán
acc_dsTimeUtc_0700_1=Tiempo de montaña (Estados Unidos y Canadá)
acc_dsTimeUtc0100_0=Belgrado, Bratislava, Budapest, Liubliana, Praga
acc_dsTimeUtc0100_1=Sarajevo, Skopje, Varsovia, Zagreb
acc_dsTimeUtc0100_2=Casablanca
acc_dsTimeUtc0100_3=Bruselas, Copenhague, Madrid, París
acc_dsTimeUtc0100_4=Ámsterdam, Berlín, Berna, Roma, Estocolmo, Viena
acc_dsTimeUtc_0900_0=Alaska
#安全点(muster point)
acc_leftMenu_accMusterPoint=Punto de encuentro
acc_musterPoint_activate=Activar
acc_musterPoint_addDept=Agregar departamento
acc_musterPoint_delDept=Eliminar departamento
acc_musterPoint_report=Informe de punto de encuentro
acc_musterPointReport_sign=Iniciar sesión manualmente
acc_musterPointReport_generate=Generar informes
acc_musterPoint_addSignPoint=Agregar punto de señal
acc_musterPoint_delSignPoint=Eliminar punto de inicio de sesión
acc_musterPoint_selectSignPoint=¡Agregue un punto de señal!
acc_musterPoint_signPoint=Punto de señal
acc_musterPoint_delFailTip=¡Ya hay puntos de encuentro activados y no se pueden eliminar!
acc_musterPointReport_enterTime=Introduzca la hora
acc_musterPointReport_dataAnalysis=Análisis de datos
acc_musterPointReport_safe=Seguro
acc_musterPointReport_danger=Peligro
acc_musterPointReport_signInManually=perforación manual
acc_musterPoint_editTip=¡El punto de encuentro está activo y no se puede editar!
acc_musterPointEmail_total=Asistencia prevista:
acc_musterPointEmail_safe=Check-in (seguro):
acc_musterPointEmail_dangerous=En peligro:
acc_musterPoint_messageNotification=Notificación de mensajes en el momento de la activación
acc_musterPointReport_sendEmail=Plan para empujar el informe
acc_musterPointReport_sendInterval=Intervalo de envío
acc_musterPointReport_sendTip=¡¡ asegúrese de que el método de notificación seleccionado se ha configurado con éxito, de lo contrario la notificación no se enviará normalmente!
acc_musterPoint_mailSubject=Aviso de reunión de emergencia
acc_musterPoint_mailContent=Reúnase en "{0}" inmediatamente e inicie sesión en el dispositivo "{1}", ¡gracias!
acc_musterPointReport_mailHead=Hola, este es el informe de emergencia. Por favor, revise.
acc_musterPoint_visitorsStatistics=Estadísticas de visitantes
# 报警监控
acc_alarm_priority=Prioridad
acc_alarm_total=Total
acc_alarm_today=Registro de hoy
acc_alarm_unhandled=No confirmado
acc_alarm_inProcess=En proceso
acc_alarm_acknowledged=Confirmado
acc_alarm_top5=Top 5 Alarm Events
acc_alarm_monitoringTime=Tiempo de seguimiento
acc_alarm_history=Historial de procesamiento de alarmas
acc_alarm_acknowledgement=Registro de procesamiento
acc_alarm_eventDescription=Detalles del evento
acc_alarm_acknowledgeText=Cuando se selecciona, se envía un mensaje de detalles del evento de alarma al buzón de correo especificado
acc_alarm_emailSubject=Añadir registro de procesamiento de eventos de alarma
acc_alarm_mute=Silencio
acc_alarm_suspend=Pausa
acc_alarm_confirmed=El evento ha sido confirmado
acc_alarm_list=Registro de alarmas
#ntp
acc_device_setNTPService=Configuración del servidor NTP
acc_device_setNTPServiceTip=Ingrese varias direcciones de servidor, separadas por coma (,) o punto y coma (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=En el funcionamiento del controlador de control de acceso, el superusuario no está limitado por la zona horaria, el anti - reentrada y la disposición de enclavamiento, y tiene una alta prioridad de apertura de puertas.
acc_editPerson_delayPassageTip=Ampliar el tiempo de espera para que el personal pase por el punto de acceso. Apto para personas con discapacidad física u otra discapacidad.
acc_editPerson_disabledTip=Inhabilite temporalmente el nivel de acceso de las personas.
#门禁向导
acc_guide_title=Asistente de configuración del módulo de control de acceso
acc_guide_addPersonTip=Necesita agregar la persona y las credenciales correspondientes (rostro o huella digital o tarjeta o palma o contraseña); si ya ha agregado, salte este paso directamente
acc_guide_timesegTip=Configure un período de tiempo de apertura válido
acc_guide_addDeviceTip=Agregue el dispositivo correspondiente como punto de acceso
acc_guide_addLevelTip=Agregar nivel de control de acceso
acc_guide_personLevelTip=Asigne la autoridad de control de acceso correspondiente a la persona
acc_guide_rtMonitorTip=Verificar registros de control de acceso en tiempo real
acc_guide_rtMonitorTip2=Ver el registro de control de acceso en tiempo real después de agregar el área a la que pertenece y la puerta correspondiente
#查看区域内人员
acc_zonePerson_cleanCount=Borrar las estadísticas de entrada y salida de personas
acc_zonePerson_inCount=Estadísticas del número de personas que ingresan
acc_zonePerson_outCount=Estadísticas del número de personas que salen
#biocv460
acc_device_validFail=¡El nombre de usuario o contraseña es incorrecto y la verificación falla!
acc_device_pwdRequired=Solo se puede introducir un máximo de 6 enteros