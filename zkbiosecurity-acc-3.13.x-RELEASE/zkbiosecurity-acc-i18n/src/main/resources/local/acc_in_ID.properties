#[1]左侧菜单
acc_module=Akses
acc_leftMenu_accDev=Perangkat Akses
acc_leftMenu_auxOut=Auxiliary Output
acc_leftMenu_dSTime=Daylight Saving Time
acc_leftMenu_access=Akses <PERSON>
acc_leftMenu_door=Pintu
acc_leftMenu_accRule=Aturan Akses
acc_leftMenu_interlock=Interlock
acc_leftMenu_antiPassback=Anti-Passback
acc_leftMenu_globalLinkage=Global Linkage
acc_leftMenu_firstOpen=First-Person Normally Open
acc_leftMenu_combOpen=Multi-Person Opening Door
acc_leftMenu_personGroup=Grup Multi-Person
acc_leftMenu_level=Level Akses
acc_leftMenu_electronicMap=Peta
acc_leftMenu_personnelAccessLevels=Level Akses Personil
acc_leftMenu_searchByLevel=Berdasarkan Level Akses
acc_leftMenu_searchByDoor=Hak Akses berdasarkan Pintu
acc_leftMenu_expertGuard=Fungsi Lanjutan
acc_leftMenu_zone=Zona
acc_leftMenu_readerDefine=Tentukan Reader
acc_leftMenu_gapbSet=Global Anti-Passback
acc_leftMenu_whoIsInside=Who is Inside
acc_leftMenu_whatRulesInside=What Rules Apply Inside
acc_leftMenu_occupancy=Kontrol Okupansi
acc_leftMenu_route=Kontrol Rute
acc_leftMenu_globalInterlock=Global Interlock
acc_leftMeue_globalInterlockGroup=Global Interlock Group
acc_leftMenu_dmr=Aturan Dead Man
acc_leftMenu_personLimit=Person Availability
acc_leftMenu_verifyModeRule=Mode Verifikasi
acc_leftMenu_verifyModeRulePersonGroup=Grup Mode Verifikasi
acc_leftMenu_extDev=Dewan I/O
acc_leftMenu_firstInLastOut=Masuk Pertama dan Terakhir
acc_leftMenu_accReports=Laporan Kontrol Akses
#[3]门禁时间段
acc_timeSeg_entity=Zona Waktu
acc_timeSeg_canNotDel=Periode waktu sedang digunakan dan tidak bisa dihapus!
#[4]门禁设备--公共的在common中
acc_common_ruleName=Nama Aturan
acc_common_hasBeanSet=Sudah Diatur
acc_common_notSet=Tidak diatur
acc_common_hasBeenOpened=Sudah dibuka
acc_common_notOpened=Tidak terbuka
acc_common_partSet=Bagian dari aturan
acc_common_linkageAndApbTip=Linkage dan global linkage, anti-passback dan global anti-passback diatur secara bersamaan, mungkin akan konflik.
acc_common_vidlinkageTip=Pastikan titik input yang sesuai linkage terikat ke saluran video yang tersedia, jika tidak video fungsi linkage tidak akan berfungsi!
acc_common_accZoneFromTo=Tidak bisa atur zona yang sama
acc_common_logEventNumber=Event ID
acc_common_bindOrUnbindChannel=Binding/unbinding the camera
acc_common_boundChannel=Bound camera
#设备信息
acc_dev_iconType=Tipe Ikon
acc_dev_carGate=Parking Barrier
acc_dev_channelGate=Flap Barrier
acc_dev_acpType=Tipe Panel Kontrol
acc_dev_oneDoorACP=Panel Akses Kontrol 1 Pintu
acc_dev_twoDoorACP=Panel Akses Kontrol 2 Pintu
acc_dev_fourDoorACP=Panel Akses Kontrol 4 Pintu
acc_dev_onDoorACD=Perangkat Standalone
acc_dev_switchToTwoDoorTwoWay=Alihkan ke 2 pintu - 2 arah
acc_dev_addDevConfirm2=Tip: Koneksi perangkat berhasil, tapi tipe panel akses kontrol berbeda dengan aktual, ubah ke panel kontrol {0} pintu . Lanjut untuk tambahkan?
acc_dev_addDevConfirm4=Perangkat Standalone. lanjut untuk tambahkan?
acc_dev_oneMachine=Perangkat Standalone
acc_dev_fingervein=Vena Jari
acc_dev_control=Kontrol Perangkat
acc_dev_protocol=Jenis Protokol
acc_ownedBoard=Memiliki Papan Ekspansi
#设备操作
acc_dev_start=Mulai
acc_dev_accLevel=Hak Akses
acc_dev_timeZoneAndHoliday=Zona Waktu, Libur
acc_dev_linkage=Linkage
acc_dev_doorOpt=Parameter Pintu
acc_dev_firstPerson=Pintu Terbuka Orang Pertama
acc_dev_multiPerson=Pintu Terbuka Multi-Orang
acc_dev_interlock=Interlock
acc_dev_antiPassback=AntiPassback
acc_dev_wiegandFmt=Format Wiegand
acc_dev_outRelaySet=Atur Output Bantu
acc_dev_backgroundVerifyParam=Opsi Verifikasi-Lb
acc_dev_getPersonInfoPrompt=Harap pastikan bahwa Anda telah mendapatkan informasi personel dengan sukses, jika tidak akan terjadi pengecualian. Lanjutkan?
acc_dev_getEventSuccess=Ambil even berhasil.
acc_dev_getEventFail=Ambil even gagal.
acc_dev_getInfoSuccess=Ambil informasi berhasil.
acc_dev_getInfoXSuccess=Ambil {0} berhasil.
acc_dev_getInfoFail=Ambil informasi gagal.
acc_dev_updateExtuserInfoFail=Perbarui informasi personil untuk bagian lanjutan gagal, silahkan ambil informasi.
acc_dev_getPersonCount=Ambil Jumlah Pengguna
acc_dev_getFPCount=Ambil Jumlah Sidik Jari
acc_dev_getFVCount=Ambil Jumlah Vena Jari
acc_dev_getFaceCount=Ambil Jumlah Wajah
acc_dev_getPalmCount=Ambil Jumlah Telapak Tangan
acc_dev_getBiophotoCount=Ambil Jumlah Foto Wajah
acc_dev_noData=Tidak ada data balikan dari perangkat.
acc_dev_noNewData=Tidak ada transaksi baru di perangkat.
acc_dev_softLtDev=Pengguna disoftware lebih banyak daripada di perangkat.
acc_dev_personCount=Jumlah Orang:
acc_dev_personDetail=Rincianya sebagai berikut:
acc_dev_softEqualDev=Jumlah pengguna disoftware dan perangkat sama.
acc_dev_softGtDev=Pengguna perangkat lebih banyak daripada di software.
acc_dev_cmdSendFail=Kirim perintah gagal, silahkan kirim ulang.
acc_dev_issueVerifyParam=Tetapkan Opsi Verifikasi Bg
acc_dev_verifyParamSuccess=Terapkan opsi Verifikasi-Lb berhasil
acc_dev_backgroundVerify=Verifikasi Latarbelakang
acc_dev_selRightFile=Pilih file upgrade yang benar!
acc_dev_devNotOpForOffLine=Perangkat offline, silahkan coba lagi
acc_dev_devNotSupportFunction=Perangkat tidak mendukung fitur ini
acc_dev_devNotOpForDisable=Perangkat nonaktif, silahkan coba lagi
acc_dev_devNotOpForNotOnline=Perangkat offline atau nonaktif, silahkan coba lagi
acc_dev_getPersonInfo=Ambil info personil
acc_dev_getFPInfo=Ambil info sidik jari
acc_dev_getFingerVeinInfo=Ambil info vena jari
acc_dev_getPalmInfo=Ambil info telapak tangan
acc_dev_getBiophotoInfo=Ambil info wajah cahaya tampak
acc_dev_getIrisInfo=Dapatkan informasi iris
acc_dev_disable=nonaktif, silahkan pilih ulang
acc_dev_offlineAndContinue=offline, lanjutkan?
acc_dev_offlineAndSelect=offline.
acc_dev_opAllDev=Semua perangkat
acc_dev_opOnlineDev=Perangkat Online
acc_dev_opException=tangani pengecualian
acc_dev_exceptionAndConfirm=Waktu koneksi perangkat habis, operasi gagal. Periksa koneksi perangkat
acc_dev_getFaceInfo=Ambil Info Wajah
acc_dev_selOpDevType=Pilih jenis perangkat yang akan dioperasikan:
acc_dev_hasFilterByFunc=Hanya tampilkan perangkat yang online dan mendukung fungsi ini!
acc_dev_masterSlaveMode=Mode RS485 master dan slave
acc_dev_master=Host
acc_dev_slave=Slave
acc_dev_modifyRS485Addr=Modifikasi alamat RS485
acc_dev_rs485AddrTip=Silahkan masukkan angka antara 1 dan 63!
acc_dev_enableFeature=Perangkat yang telah mengaktifkan verifikasi-Lb
acc_dev_disableFeature=Perangkat yang telah menonaktifkan verifikasi-Lb
acc_dev_getCountOnly=Hanya ambil jumlah
acc_dev_queryDevPersonCount=Lihat Jumlah Personil
acc_dev_queryDevVolume=Lihat kapasitas perangkat
acc_dev_ruleType=Tipe Aturan
acc_dev_contenRule=Rincian Aturan
acc_dev_accessRules=Lihat aturan perangkat
acc_dev_ruleContentTip=Antara banyak aturan dipisahkan dengan '|' .
acc_dev_rs485AddrFigure=Gambar kode alamat RS485
acc_dev_addLevel=Tambahkan ke Level
acc_dev_personOrFingerTanto=Jumlah personil atau sidik jari melebihi batas, sinkronisasi gagal...
acc_dev_personAndFingerUnit=(Jumlah)
acc_dev_setDstime=Atur Daylight Saving Time
acc_dev_setTimeZone=Atur Zona Waktu Perangkat
acc_dev_selectedTZ=Pilih Zona Waktu
acc_dev_timeZoneSetting=Mengatur Zona Waktu...
acc_dev_timeZoneCmdSuccess=Kirim Perintah Atur Zona Waktu Berhasil...
acc_dev_enableDstime=Aktifkan Daylight Saving Time
acc_dev_disableDstime=Nonaktifkan Daylight Saving Time
acc_dev_timeZone=Zona Waktu
acc_dev_dstSettingTip=Mengatur Daylight Saving Time...
acc_dev_dstDelTip=Menghapus Daylight Saving Time Perangkat...
acc_dev_enablingDst=Aktifkan Daylight Saving Time
acc_dev_dstEnableCmdSuccess=Kirim Perintah Aktifkan Daylight Saving Time Berhasil.
acc_dev_disablingDst=Nonaktifkan Daylight Saving Time.
acc_dev_dstDisableCmdSuccess=Menonaktifkan Perintah Waktu Hemat Siang Hari, Kirim Sukses.
acc_dev_dstCmdSuccess=Perintah Penghematan Waktu Siang Hari, Kirim Sukses ...
acc_dev_usadst=Daylight Saving Time
acc_dev_notSetDst=Tidak ada pengaturan
acc_dev_selectedDst=Waktu Hemat Siang Hari Terpilih
acc_dev_configMasterSlave=Konfigurasi master-slave
acc_dev_hasFilterByUnOnline=Hanya perlihatkan Perangkat online.
acc_dev_softwareData=Jika Anda menemukan bahwa data tidak konsisten dengan perangkat, silakan menyinkronkan data perangkat sebelum mencoba lagi.
acc_dev_disabled=Perangkat yang dinonaktifkan tidak dapat dioperasikan!
acc_dev_offline=Perangkat offline tidak dapat dioperasikan!
acc_dev_noSupport=Perangkat tidak mendukung fungsi ini dan tidak dapat dioperasikan!
acc_dev_noRegDevTip=Perangkat ini tidak didefinisikan sebagai perangkat register. Data dalam perangkat lunak tidak akan diperbarui. Apakah Anda ingin melanjutkan?
acc_dev_noOption=Tidak ada opsi yang memenuhi syarat
acc_dev_devFWUpdatePrompt=Perangkat saat ini biasanya tidak akan menggunakan orang yang dinonaktifkan dan fungsi validitas orang (silakan merujuk ke manual pengguna).
acc_dev_panelFWUpdatePrompt=Perangkat saat ini biasanya tidak akan menggunakan orang yang dinonaktifkan dan fungsi validitas orang, segera memutakhirkan firmware?
acc_dev_sendEventCmdSuccess=Hapus Perintah Acara berhasil dikirim
acc_dev_tryAgain=Silakan coba lagi.
acc_dev_eventAutoCheckAndUpload=Otomatis memeriksa dan mengunggah acara
acc_dev_eventUploadStart=Mulai mengunggah acara.
acc_dev_eventUploadEnd=Pengunggahan acara selesai.
acc_dev_eventUploadFailed=Gagal mengunggah acara.
acc_dev_eventUploadPrompt=Versi firmware perangkat terlalu lama, sebelum Anda meningkatkan firmware, Anda ingin:
acc_dev_backupToSoftware=Cadangkan data ke perangkat lunak
acc_dev_deleteEvent=Hapus catatan lama
acc_dev_upgradePrompt=Versi firmware terlalu lama dan dapat menyebabkan kesalahan, harap buat cadangan data ke perangkat lunak sebelum memperbarui firmware.
acc_dev_conflictCardNo=Kartu hadir dalam sistem untuk {0} di orang lain!
acc_dev_rebootAfterOperate=Operasi berhasil, perangkat akan memulai kembali nanti.
acc_dev_baseOptionTip=Abnormalitas dari parameter dasar yang diperoleh.
acc_dev_funOptionTip=Abnormalitas parameter fungsi yang diperoleh.
acc_dev_sendComandoTip=Dapatkan perintah parameter perangkat gagal dikirim.
acc_dev_noC3LicenseTip=Tidak dapat menambahkan jenis perangkat ini ({0})! Silakan hubungi bagian penjualan kami.
acc_dev_combOpenDoorTip=({0}) Pintu terbuka banyak orang telah ditetapkan, tidak dapat digunakan bersamaan dengan verifikasi latar belakang.
acc_dev_combOpenDoorPersonCountTip=Grup {0} tidak dapat membuka lebih dari {1}!
acc_dev_addDevTip=Ini hanya berlaku untuk menambahkan perangkat dengan protokol komunikasi PULL!
acc_dev_addError=Pengecualian penambahan perangkat, parameter yang hilang ({0})!
acc_dev_updateIPAndPortError=Perbarui IP server dan kesalahan port ...
acc_dev_transferFilesTip=Tes firmware selesai, mentransfer file
acc_dev_serialPortExist=Serial Port Exist
acc_dev_isExist=Memiliki Perangkat
acc_dev_description=Deskripsi
acc_dev_searchEthernet=Cari perangkat Ethernet
acc_dev_searchRS485=Cari perangkat RS485
acc_dev_rs485AddrTip1=Alamat mulai RS485 tidak boleh lebih besar dari alamat akhir
acc_dev_rs485AddrTip2=Ruang lingkup pencarian RS485 harus kurang dari 20
acc_dev_clearAllCmdCache=Hapus Semua Perintah
acc_dev_authorizedSuccessful=Resmi Berhasil
acc_dev_authorize=Wewenang
acc_dev_registrationDevice=Daftarkan Perangkat
acc_dev_setRegistrationDevice=Diatur sebagai Perangkat Registrasi
acc_dev_mismatchedDevice=Perangkat ini tidak dapat digunakan untuk pasar Anda. Silakan periksa nomor seri perangkat dengan penjualan kami.
acc_dev_pwdStartWithZero=Kata sandi komunikasi tidak dapat dimulai dengan nol!
acc_dev_maybeDisabled=Lisensi saat ini hanya memungkinkan Anda untuk menambah {0} pintu lain, pintu perangkat tambahan baru yang melebihi batas pintu lisensi akan dinonaktifkan, apakah akan terus beroperasi?
acc_dev_Limit=Kunci Lisensi Perangkat telah mencapai batas atas untuk ditambahkan, mohon otorisasi.
acc_dev_selectDev=Silakan pilih perangkat!
acc_dev_cannotAddPullDevice=Tidak dapat menambahkan perangkat penarik! Silakan hubungi sales kami.
acc_dev_notContinueAddPullDevice=Telah ada {0} perangkat tarik dalam sistem, tidak dapat melanjutkan menambahkan lagi! Silakan hubungi sales kami.
acc_dev_deviceNameNull=Model perangkat kosong; tidak dapat menambahkan perangkat!
acc_dev_commTypeErr=Jenis Komunikasi tidak cocok; tidak dapat menambahkan perangkat!
acc_dev_inputDomainError=Silakan masukkan alamat domain yang valid.
acc_dev_levelTip=Ada lebih dari 5000 orang di level ini, tidak dapat menambahkan perangkat ke level di sini.
acc_dev_auxinSet=Pengaturan Input Bantu
acc_dev_verifyModeRule=Aturan Mode Verifikasi
acc_dev_netModeWired=Berkabel
acc_dev_netMode4G=4G
acc_dev_netModeWifi=WIFI
acc_dev_updateNetConnectMode=Alihkan Koneksi Jaringan
acc_dev_wirelessSSID=SSID Nirkabel
acc_dev_wirelessKey=Kunci Nirkabel
acc_dev_searchWifi=Cari WIFI
acc_dev_testNetConnectSuccess=Apakah koneksi berhasil?
acc_dev_testNetConnectFailed=Koneksi tidak dapat berkomunikasi dengan baik!
acc_dev_signalIntensity=Kekuatan Sinyal
acc_dev_resetSearch=Cari Lagi
acc_dev_addChildDevice=Tambah Sub-Perangkat
acc_dev_modParentDevice=Ubah perangkat master
acc_dev_configParentDevice=Pengaturan Perangkat Master
acc_dev_lookUpChildDevice=Lihat perangkat anak
acc_dev_addChildDeviceTip=Otorisasi perlu dilakukan di bawah sub-perangkat yang diotorisasi
acc_dev_maxSubCount=Jumlah sub-perangkat resmi melebihi jumlah maksimum {0} unit.
acc_dev_seletParentDevice=Silakan pilih perangkat master!
acc_dev_networkCard=Kartu Jaringan
acc_dev_issueParam=Parameter Sinkronisasi Kustom
acc_dev_issueMode=Sinkronisasi Mode
acc_dev_initIssue=Versi Firmware 3030 Data Inisialisasi
acc_dev_customIssue=Kustom Menyinkronkan Data
acc_dev_issueData=Data
acc_dev_parent=Perangkat Utama
acc_dev_parentEnable=Perangkat master dinonaktifkan
acc_dev_parentTips=Binding master device akan menghapus semua data dalam perangkat dan perlu diatur kembali.
acc_dev_addDevIpTip=Alamat IP baru tidak boleh sama dengan alamat IP server
acc_dev_modifyDevIpTip=Alamat server baru tidak boleh sama dengan alamat IP perangkat
acc_dev_setWGReader=Setel Pembaca Wiegand
acc_dev_selectReader=Klik untuk memilih pembaca
acc_dev_IllegalDevice=Perangkat Ilegal
acc_dev_syncTimeWarnTip=Waktu sinkronisasi untuk perangkat berikut ini harus disinkronkan pada perangkat master.
acc_dev_setTimeZoneWarnTip=Zona waktu perangkat berikut ini harus disinkronkan pada perangkat master.
acc_dev_setDstimeWarnTip=Waktu musim panas untuk perangkat-perangkat berikut harus disinkronkan pada perangkat master.
acc_dev_networkSegmentSame=Dua adapter jaringan tidak dapat menggunakan segmen jaringan yang sama.
acc_dev_upgradeProtocolNoMatch=Upgrade file protokol tidak cocok
acc_dev_ipAddressConflict=Perangkat dengan alamat IP yang sama sudah ada. Silakan modifikasi alamat IP perangkat dan tambahkan lagi.
acc_dev_checkServerPortTip=Port server yang ditetapkan tidak konsisten dengan port komunikasi sistem, yang dapat mengakibatkan kegagalan untuk menambahkan. Terus beroperasi?
acc_dev_clearAdmin=Hapus Izin Administrator
acc_dev_setDevSate=Atur Status Masuk / Keluar Perangkat
acc_dev_sureToClear=Apakah Anda yakin akan menghapus izin administrator perangkat?
acc_dev_hostState=Status perangkat master
acc_dev_regDeviceTypeTip=Perangkat ini dibatasi dan tidak diizinkan untuk ditambahkan secara langsung. Silakan hubungi penyedia perangkat lunak!
acc_dev_extBoardType=Tipe Papan I/O
acc_dev_extBoardTip=Setelah konfigurasi, Anda perlu me-restart perangkat agar berlaku.
acc_dev_extBoardLimit=Hanya {0} papan I / O jenis ini yang dapat ditambahkan ke setiap perangkat!
acc_dev_replace=Ganti Perangkat
acc_dev_replaceTip=Setelah penggantian, perangkat lama tidak akan berfungsi, harap berhati-hati!
acc_dev_replaceTip1=Setelah penggantian, lakukan operasi "sinkronisasi semua data";
acc_dev_replaceTip2=Pastikan model perangkat penggantinya sama!
acc_dev_replaceTip3=Harap pastikan perangkat pengganti telah menetapkan alamat server dan port yang sama dengan perangkat lama!
acc_dev_replaceFail=Jenis mesin perangkat tidak konsisten dan tidak dapat diganti!
acc_dev_notApb=Perangkat ini tidak dapat melakukan operasi gerbang atau membaca kepala anti kapal selam
acc_dev_upResourceFile=Memuat naik berkas sumber daya
acc_dev_playOrder=Perintah main
acc_dev_setFaceServerInfo=Tetapkan Parameter Perbandingan Belakang Muka
acc_dev_faceVerifyMode=Mod Comparison Wajah
acc_dev_faceVerifyMode1=Perbandingan Lokal
acc_dev_faceVerifyMode2=Perbandingan Backend
acc_dev_faceVerifyMode3=Prioritas Perbandingan Lokal
acc_dev_faceBgServerType=Jenis Server Backend Muka
acc_dev_faceBgServerType1=Layanan Platform Perisian
acc_dev_faceBgServerType2=Layanan Partii ketiga
acc_dev_isAccessLogic=Aktifkan Verifikasi Logik Kontrol Akses
#[5]门-其他关联的也复用此处
acc_door_entity=Pintu
acc_door_number=Nomor pintu
acc_door_name=Nama Pintu
acc_door_activeTimeZone=Zona waktu aktif
acc_door_passageModeTimeZone=Zona Waktu Mode Passage
acc_door_setPassageModeTimeZone=Set Zona Waktu Mode Passage
acc_door_notPassageModeTimeZone=Zona Waktu Mode Non-Passage
acc_door_lockOpenDuration=Durasi buka kunci
acc_door_entranceApbDuration=Durasi Masuk Anti-Passback
acc_door_sensor=Sensor pintu
acc_door_sensorType=Tipe sensor pintu
acc_door_normalOpen=Normal buka
acc_door_normalClose=Normal tutup
acc_door_sensorDelay=Penundaan sensor pintu
acc_door_closeAndReverseState=StatusTutupDanSebaliknya
acc_door_hostOutState=Akses status host
acc_door_slaveOutState=Slave Out of State
acc_door_inState=Masuk
acc_door_outState=Keluar
acc_door_requestToExit=REX Mode
acc_door_withoutUnlock=Kunci
acc_door_unlocking=Tidak dikunci
acc_door_alarmDelay=Tunda Alarm
acc_door_duressPassword=Kata Sandi Paksaan
acc_door_currentDoor=Pintu Saat Ini
acc_door_allDoorOfCurDev=Semua Pintu di Perangkat Saat Ini
acc_door_allDoorOfAllDev=Semua Pintu di Semua Perangkat
acc_door_allDoorOfAllControlDev=Semua Pintu di Semua Perangkat Kontrol
acc_door_allDoorOfAllStandaloneDev=Semua Pintu dari semua Perangkat Standalone
acc_door_allWirelessLock=Semua Kunci Nirkabel
acc_door_max6BitInteger=Bilangan Bulat 6 Maksimum
acc_door_direction=Arah
acc_door_onlyInReader=Entri Pembaca Saja
acc_door_bothInAndOutReader=Pembaca Entri & Keluar
acc_door_noDoor=Silakan tambahkan pintu.
acc_door_nameRepeat=Nama pintu digandakan.
acc_door_duressPwdError=Kata sandi paksaan tidak boleh sama dengan kata sandi pribadi apa pun.
acc_door_urgencyStatePwd=Silakan masukkan {0} bit integer!
acc_door_noDevOnline=Tidak ada perangkat yang online, atau pintunya tidak mendukung mode verifikasi kartu.
acc_door_durationLessLock=Keterlambatan Sensor Pintu harus lebih besar dari Durasi Buka Kunci.
acc_door_lockMoreDuration=Durasi Buka Kunci harus kurang dari Keterlambatan Sensor Pintu.
acc_door_lockAndExtLessDuration=Jumlah Durasi Kunci Terbuka dan Keterlambatan Passage harus kurang dari Keterlambatan Sensor Pintu.
acc_door_noDevTrigger=Perangkat tidak memenuhi persyaratan!
acc_door_relay=Relay
acc_door_pin=Pin
acc_door_selDoor=Pilih Pintu
acc_door_sensorStatus=Sensor Pintu ({0})
acc_door_sensorDelaySeconds=Keterlambatan Sensor Pintu ({0}s)
acc_door_timeSeg=Zona Waktu ({0})
acc_door_combOpenInterval=Interval Operasi Multi Orang
acc_door_delayOpenTime=Tunda Pintu Terbuka
acc_door_extDelayDrivertime=Keterlambatan Passage
acc_door_enableAudio=Aktifkan Alarm
acc_door_disableAudio=Nonaktifkan Suara Alarm
acc_door_lockAndExtDelayTip=Jumlah Interval Durasi Kunci Terbuka dan Interval Waktu Tunda Waktu tidak lebih dari 254 detik.
acc_door_disabled=Pintu yang dinonaktifkan tidak dapat dioperasikan!
acc_door_offline=Pintu offline tidak dapat dioperasikan!
acc_door_notSupport=Pintu-pintu berikut tidak mendukung fitur ini!
acc_door_select=Pilih pintu
acc_door_pushMaxCount=Ada {0} pintu yang diaktifkan dalam sistem dan telah mencapai batas lisensi! Silakan hubungi bagian penjualan kami
acc_door_outNumber=Lisensi saat ini hanya memungkinkan Anda untuk menambah {0} pintu lain. Harap pilih kembali pintu dan coba lagi, atau hubungi bagian penjualan kami untuk mendapatkan lisensi pembaruan.
acc_door_latchTimeZone=REX Zona Waktu
acc_door_wgFmtReverse=Pembalikan nomor kartu
acc_door_allowSUAccessLock=Izinkan Superuser Mengakses Ketika Terkunci
acc_door_verifyModeSinglePwd=Kata sandi tidak dapat digunakan sebagai metode verifikasi independen!
acc_door_doorPassword=Pintu membuka sandi
#辅助输入
acc_auxIn_timeZone=Zona Waktu Aktif
#辅助输出
acc_auxOut_passageModeTimeZone=Zona Waktu Mode Passage
acc_auxOut_disabled=Output tambahan yang dinonaktifkan tidak dapat dioperasikan!
acc_auxOut_offline=Output tambahan offline tidak dapat dioperasikan!
#[8]门禁权限组
acc_level_doorGroup=Kombinasi Pintu
acc_level_openingPersonnel=Membuka Personel
acc_level_noDoor=Tidak ada item yang tersedia. Silakan tambahkan perangkat terlebih dahulu.
acc_level_doorRequired=Pintu harus dipilih.
acc_level_doorCount=Jumlah Pintu
acc_level_doorDelete=Hapus Pintu
acc_level_isAddDoor=Segera tambahkan pintu ke Level Kontrol Akses saat ini?
acc_level_master=Umum
acc_level_noneSelect=Silakan tambahkan tingkat kontrol akses.
acc_level_useDefaultLevel=Alihkan tingkat akses ke departemen baru?
acc_level_persAccSet=Pengaturan kontrol akses personel
acc_level_visUsed={0} sudah digunakan oleh modul tamu dan tidak bisa dihapus!
acc_level_doorControl=Kontrol Pintu
acc_level_personExceedMax=Jumlah kelompok ijin saat ini ({0}), dan jumlah maksimum kelompok ijin pilihan ({1})
acc_level_exportLevel=Ekspor Tingkat Akses
acc_level_exportLevelDoor=Ekspor Pintu Tingkat Akses
acc_level_exportLevelPerson=Ekspor Personil Tingkat Akses
acc_level_importLevel=Impor Tingkat Akses
acc_level_importLevelDoor=Impor Pintu Tingkat Akses
acc_level_importLevelPerson=Impor Personil Tingkat Akses
acc_level_exportDoorFileName=Informasi Pintu Tingkat Akses
acc_level_exportPersonFileName=Informasi Personil Tingkat Akses
acc_levelImport_nameNotNull=Nama Level Akses tidak boleh kosong
acc_levelImport_timeSegNameNotNull=Zona Waktu tidak boleh kosong
acc_levelImport_areaNotExist=Area tidak ada!
acc_levelImport_timeSegNotExist=Zona Waktu tidak ada!
acc_levelImport_nameExist=Nama Level Akses {0} sudah ada!
acc_levelImport_levelDoorExist=Pintu Tingkat Akses {0} sudah ada!
acc_levelImport_levelPersonExist=Personil Tingkat Akses {0} sudah ada!
acc_levelImport_noSpecialChar=Nama tingkat akses tidak boleh berisi karakter khusus!
#[10]首人常开
acc_firstOpen_setting=Orang pertama buka normal
acc_firstOpen_browsePerson=Jelajahi Personil
#[11]多人组合开门
acc_combOpen_comboName=Nama Kombinasi
acc_combOpen_personGroupName=Nama Grup
acc_combOpen_personGroup=Grup Muti-Person
acc_combOpen_verifyOneTime=Hitungan Personil Saat Ini
acc_combOpen_eachGroupCount=Jumlah personel pembuka di setiap grup
acc_combOpen_group=Grup
acc_combOpen_changeLevel=Grup Pintu Terbuka
acc_combOpen_combDeleteGroup=Referensi kombinasi pembuka yang ada, harap hapus dulu.
acc_combOpen_ ownedLevel=Grup Milik
acc_combOpen_mostPersonCount=Jumlah grup tidak boleh lebih dari lima orang.
acc_combOpen_leastPersonCount=Jumlah grup tidak boleh kurang dari dua orang.
acc_combOpen_groupNameRepeat=Duplikat nama grup.
acc_combOpen_groupNotUnique=Grup Orang Pintu Terbuka tidak boleh identik.
acc_combOpen_persNumErr=Jumlah grup melebihi nilai aktual pilihan Anda, silakan pilih kembali!
acc_combOpen_combOpengGroupPersonShort=Setelah orang-orang dihapus, jumlah grup grup yang membuka pintu personil tidak cukup, silakan hapus grup yang terbuka terlebih dahulu.
acc_combOpen_backgroundVerifyTip=Pintu ada di perangkat dengan verifikasi latar belakang yang diaktifkan; tidak bisa digunakan bersamaan dengan muti-person open door!
#[12]互锁
acc_interlock_rule=Aturan Interlock
acc_interlock_mode1Or2=Interlock antara {0} dan {1}
acc_interlock_mode3=Interlock antara {0} dan {1} dan {2}
acc_interlock_mode4=Interlock antara {0} dan {1} atau antara {2} dan {3}
acc_interlock_mode5=Saling mengunci antara {0} dan {1} dan {2} dan {3}
acc_interlock_hasBeenSet=Memiliki pengaturan program interlock
acc_interlock_group1=Grup 1
acc_interlock_group2=Grup 2
acc_interlock_ruleInfo=Inter group interlocking
acc_interlock_alreadyExists=Peraturan interlock yang sama sudah ada, tolong jangan menambahnya lagi!
acc_interlock_groupInterlockCountErr=Peraturan interlock kelompok dalam memerlukan setidaknya dua pintu
acc_interlock_ruleType=Interlocking rule type
#[13]反潜
acc_apb_rules=Aturan Anti-Passback
acc_apb_reader=Anti-Passback antara pembaca pintu {0}
acc_apb_reader2=Anti-Passback di antara pembaca pintu:{0},{1}
acc_apb_reader3=Anti-Passback di antara pembaca pintu:{0},{1},{2}
acc_apb_reader4=Anti-Passback di antara pembaca pintu:{0},{1},{2},{3}
acc_apb_reader5=Pembaca anti-Passback di pintu {0}
acc_apb_reader6=Pembaca anti-Passback di pintu {0}
acc_apb_reader7=Anti-Passback di antara pembaca dari semua 4 pintu
acc_apb_twoDoor=Anti-Passback antara {0} dan {1}
acc_apb_fourDoor=Anti-Passback antara {0} dan {1}, Anti-Passback antara {2} dan {3}
acc_apb_fourDoor2=Anti-Passback antara {0} atau {1} dan {2} atau {3}
acc_apb_fourDoor3=Anti-Passback antara {0} dan {1} atau {2}
acc_apb_fourDoor4=Anti-Passback antara {0} dan {1} atau {2} atau {3}
acc_apb_hasBeenSet=Telah ditetapkan untuk Anti-Passback
acc_apb_conflictWithGapb=Perangkat ini memiliki pengaturan Anti-Passback global, dan tidak dapat dimodifikasi!
acc_apb_conflictWithApb=Zona perangkat memiliki pengaturan Anti-Passback, dan tidak dapat dimodifikasi!
acc_apb_conflictWithEntranceApb=Zona perangkat memiliki pengaturan Anti-Passback masuk, dan tidak dapat dimodifikasi!
acc_apb_controlIn=Anti-Passback In
acc_apb_controlOut=Anti-Passback Out
acc_apb_controlInOut=Anti-Passback In/Out
acc_apb_groupIn=Bergabung Dengan Kelompok
acc_apb_groupOut=Keluar Dari Kelompok
acc_apb_reverseName=Reverse Anti Submarine
acc_apb_door=Gerbang Anti Kapal Selam
acc_apb_readerHead=Membaca Kepala Untuk Melawan Kapal Selam
acc_apb_alreadyExists=Peraturan anti kapal selam yang sama sudah ada, tolong jangan menambahnya lagi!
#[17]电子地图
acc_map_addDoor=Tambahkan Pintu
acc_map_addChannel=Tambah Kamera
acc_map_noAccess=Tidak ada izin untuk mengakses modul peta elektronik, silakan hubungi administrator!
acc_map_noAreaAccess=Anda tidak memiliki izin peta untuk area ini, silakan hubungi administrator!
acc_map_imgSizeError=Harap unggah gambar yang ukurannya kurang dari {0} juta!
#[18]门禁事件记录
acc_trans_entity=Transaksi
acc_trans_eventType=Jenis acara
acc_trans_firmwareEvent=Acara Firmware
acc_trans_softwareEvent=Acara Perangkat Lunak
acc_trans_today=Acara Mulai Hari Ini
acc_trans_lastAddr=Posisi Terakhir Diketahui
acc_trans_viewPhotos=Lihat Foto
acc_trans_exportPhoto=Ekspor foto
acc_trans_dayNumber=Berhari-hari
acc_trans_photo=Foto akses kontrol kejadian
acc_trans_fileIsTooLarge=File yang diekspor terlalu besar, harap kurangi cakupan untuk diekspor
#[19]门禁验证方式
acc_verify_mode_onlyface=Wajah
acc_verify_mode_facefp=Wajah+Sidik Jari
acc_verify_mode_facepwd=Wajah+Kata Sandi
acc_verify_mode_facecard=Wajah+Kartu
acc_verify_mode_facefpcard=Wajah+Sidik Jari+Kartu
acc_verify_mode_facefppwd=Wajah+Sidik Jari+Kata Sandi
acc_verify_mode_fv=Jari Vena
acc_verify_mode_fvpwd=Finger Vein And Password
acc_verify_mode_fvcard=Jari+Vena Kartu
acc_verify_mode_fvpwdcard=Jari Vena+Kata Sandi+Kartu
acc_verify_mode_pv=Palm
acc_verify_mode_pvcard=Palm And Card
acc_verify_mode_pvface=Palm+Wajah
acc_verify_mode_pvfp=Palm+Sidik Jari
acc_verify_mode_pvfacefp=Palm+Wajah+Sidik Jari
#[20]门禁事件编号
acc_eventNo_-1=Tidak Ada
acc_eventNo_0=Sapuan Normal Terbuka
acc_eventNo_1=Gesek selama Zona Waktu Mode Passage
acc_eventNo_2=Kartu Normal Terbuka Pertama (Kartu Gesek)
acc_eventNo_3=Multi-Person Open (Kartu Gesek)
acc_eventNo_4=Kata Sandi Darurat Terbuka
acc_eventNo_5=Buka selama Zona Waktu Mode Passage
acc_eventNo_6=Acara Linkage Dipicu
acc_eventNo_7=Batalkan Alarm
acc_eventNo_8=Pembukaan Jauh
acc_eventNo_9=Penutupan Jauh
acc_eventNo_10=Nonaktifkan Zona Waktu Mode Passage Intraday
acc_eventNo_11=Aktifkan Zona Waktu Mode Passing Intraday
acc_eventNo_12=Output Bantu Terbuka dari Jarak Jauh
acc_eventNo_13=Output Bantu Tutup dari Jarak Jauh
acc_eventNo_14=Sidik Jari Normal Dibuka
acc_eventNo_15=Multi-Person Open (Sidik Jari)
acc_eventNo_16=Tekan Sidik Jari selama Zona Waktu Mode Passage
acc_eventNo_17=Kartu plus Sidik Jari Terbuka
acc_eventNo_18=Kartu Normal Terbuka Pertama (Tekan Sidik Jari)
acc_eventNo_19=Kartu Normal Terbuka Pertama (Kartu plus Sidik Jari)
acc_eventNo_20=Operasikan Interval terlalu pendek
acc_eventNo_21=Zona Waktu Tidak Aktif Pintu (Kartu Gesek)
acc_eventNo_22=Zona Waktu Ilegal
acc_eventNo_23=Akses Ditolak
acc_eventNo_24=Anti-Passback
acc_eventNo_25=Interlock
acc_eventNo_26=Otentikasi Multi-Orang (Kartu Gesek)
acc_eventNo_27=Kartu Dinonaktifkan
acc_eventNo_28=Batas Waktu Buka Pintu Diperpanjang
acc_eventNo_29=Kartu kedaluwarsa
acc_eventNo_30=Kesalahan Kata Sandi
acc_eventNo_31=Tekan Interval Sidik Jari terlalu pendek
acc_eventNo_32=Otentikasi Multi-Orang (Tekan Sidik Jari)
acc_eventNo_33=Sidik Jari Kedaluwarsa
acc_eventNo_34=Dinonaktifkan Sidik Jari
acc_eventNo_35=Zona Waktu Tidak Aktif Pintu (Tekan Sidik Jari)
acc_eventNo_36=Pintu Zona Waktu Tidak Aktif (Tekan Tombol Keluar)
acc_eventNo_37=Gagal Tutup selama Zona Waktu Mode Passage
acc_eventNo_38=Kartu Dilaporkan Hilang
acc_eventNo_39=Akses Dilumpuhkan
acc_eventNo_40=Otentikasi Multi-Orang Gagal (Tekan Sidik Jari)
acc_eventNo_41=Verifikasi Mode Kesalahan
acc_eventNo_42=Kesalahan Format Wiegand
acc_eventNo_43=Batas Waktu Verifikasi Anti-Passback
acc_eventNo_44=Verifikasi Latar Belakang Gagal
acc_eventNo_45=Latar Belakang Verifikasi Batas Waktu
acc_eventNo_47=Gagal Mengirim Perintah
acc_eventNo_48=Otentikasi Multi-Orang Gagal (Kartu Gesek)
acc_eventNo_49=Zona Waktu Tidak Aktif Pintu (Kata Sandi)
acc_eventNo_50=Tekan Interval Kata Sandi terlalu Pendek
acc_eventNo_51=Otentikasi Banyak Orang (Kata Sandi)
acc_eventNo_52=Kegagalan Otentikasi Banyak Orang (Kata Sandi)
acc_eventNo_53=Kata Sandi Kedaluwarsa
acc_eventNo_100=Tamper Alarm
acc_eventNo_101=Buka Kata Sandi Terbuka
acc_eventNo_102=Pintu Dibuka Secara Paksa
acc_eventNo_103=Buka Sidik Jari Duress
acc_eventNo_200=Pintu Dibuka Dengan Benar
acc_eventNo_201=Pintu Ditutup dengan Benar
acc_eventNo_202=Keluar dari Tombol Buka
acc_eventNo_203=Multi-Person Open (Kartu plus Sidik Jari)
acc_eventNo_204=Zona Waktu Mode Passage Berakhir
acc_eventNo_205=Pembukaan Normal Jarak Jauh
acc_eventNo_206=Perangkat Dimulai
acc_eventNo_207=Kata Sandi Terbuka
acc_eventNo_208=Pintu Terbuka Pengguna Super
acc_eventNo_209=Tombol Keluar dipicu (Tanpa Membuka Kunci)
acc_eventNo_210=Mulai pintu api
acc_eventNo_211=Pintu Tutup Pengguna Super
acc_eventNo_212=Mengaktifkan fungsi kontrol elevator
acc_eventNo_213=Nonaktifkan fungsi kontrol elevator
acc_eventNo_214=Multi-Person Open (Kata Sandi)
acc_eventNo_215=Kartu Normal Terbuka Pertama (Kata Sandi)
acc_eventNo_216=Kata Sandi selama Zona Waktu Mode Passage
acc_eventNo_220=Input Bantu Terputus (Terbuka)
acc_eventNo_221=Input Bantu Tersingkat (Tertutup)
acc_eventNo_222=Verifikasi Latar Belakang Berhasil
acc_eventNo_223=Verifikasi Latar Belakang
acc_eventNo_225=Input Bantu Normal
acc_eventNo_226=Pemicu Input Bantu
acc_newEventNo_0=Normal Verifikasi Terbuka
acc_newEventNo_1=Verifikasi Selama Zona Waktu Mode Passage
acc_newEventNo_2=Personil Pertama Terbuka
acc_newEventNo_3=Multi-Personel Terbuka
acc_newEventNo_20=Interval Operasi terlalu pendek
acc_newEventNo_21=Zona Waktu Tidak Aktif Pintu Verifikasi Terbuka
acc_newEventNo_26=Tunggu Otentikasi Multi-Personel
acc_newEventNo_27=Personel Tidak Terdaftar
acc_newEventNo_29=Personil Kedaluwarsa
acc_newEventNo_30=Kesalahan Kata Sandi
acc_newEventNo_41=Verifikasi Mode Kesalahan
acc_newEventNo_43=Kunci Staf
acc_newEventNo_44=Latar Belakang Verifikasi Gagal
acc_newEventNo_45=Latar Belakang Verifikasi Batas Waktu
acc_newEventNo_48=Verifikasi Multi-Personel Gagal
acc_newEventNo_54=Tegangan baterai terlalu rendah
acc_newEventNo_55=Segera ganti baterai
acc_newEventNo_56=Operasi Ilegal
acc_newEventNo_57=Daya Cadangan
acc_newEventNo_58=Biasanya Buka Alarm
acc_newEventNo_59=Manajemen Ilegal
acc_newEventNo_60=Pintu Terkunci Di Dalam
acc_newEventNo_61=Digandakan
acc_newEventNo_62=Melarang Pengguna
acc_newEventNo_63=Pintu Terkunci
acc_newEventNo_64=Tombol Keluar tidak aktif Zona Waktu
acc_newEventNo_65=Zona Waktu Input Auxiliary Tidak Aktif
acc_newEventNo_66=Pembaruan Pembaca Gagal
acc_newEventNo_67=Pemasangan jarak jauh berhasil (perangkat tidak berwenang)
acc_newEventNo_68=Suhu Tubuh Tinggi - Akses Ditolak
acc_newEventNo_69=Tanpa Mask - Access Ditolak
acc_newEventNo_70=Rasio wajah berkomunikasi secara tidak normal dengan server.
acc_newEventNo_71=Server wajah merespon secara tidak normal.
acc_newEventNo_73=Kode QR tidak valid
acc_newEventNo_74=Kode QR Kedaluwarsa
acc_newEventNo_101=Alarm Terbuka Paksaan
acc_newEventNo_104=Alarm Geser Kartu Tidak Valid
acc_newEventNo_105=Tidak dapat terhubung ke server
acc_newEventNo_106=Pematian daya utama
acc_newEventNo_107=Daya baterai mati
acc_newEventNo_108=Tidak dapat terhubung ke perangkat master
acc_newEventNo_109=Pembaca kerusakan alarm
acc_newEventNo_110=Pembaca offline
acc_newEventNo_112=Papan pengembangan offline
acc_newEventNo_114=Input alarm kebakaran terputus (deteksi baris)
acc_newEventNo_115=Alarm kebakaran input sirkuit pendek (deteksi baris)
acc_newEventNo_116=Masukan bantuan terputus (deteksi baris)
acc_newEventNo_117=Sirkuit pendek input bantuan (deteksi baris)
acc_newEventNo_118=Exit switch terputus (deteksi baris)
acc_newEventNo_119=Keluar switch sirkuit pendek (deteksi sirkuit)
acc_newEventNo_120=Matikan magnetik pintu (deteksi garis)
acc_newEventNo_121=Sirkuit pendek magnetik pintu (deteksi garis)
acc_newEventNo_159=Remote control untuk membuka pintu
acc_newEventNo_214=Terhubung ke server
acc_newEventNo_217=Berhasil terhubung ke perangkat master
acc_newEventNo_218=Verifikasi Kartu ID
acc_newEventNo_222=Latar Belakang Verifikasi Sukses
acc_newEventNo_223=Verifikasi Latar Belakang
acc_newEventNo_224=Bunyikan bel
acc_newEventNo_227=Buka pintu dua kali
acc_newEventNo_228=Tutup dua kali pintu
acc_newEventNo_229=Output Auxiliary Jangka Waktu Biasanya Terbuka
acc_newEventNo_230=Output Auxiliary Berakhir Waktu
acc_newEventNo_232=Verifikasi Sukses
acc_newEventNo_233=Aktifkan Lockdown
acc_newEventNo_234=Nonaktifkan Lockdown
acc_newEventNo_235=Pembaruan Pembaruan Berhasil
acc_newEventNo_236=Alarm tamper reader dihapus
acc_newEventNo_237=Pembaca Online
acc_newEventNo_239=Panggilan perangkat
acc_newEventNo_240=Panggilan diakhiri
acc_newEventNo_243=Input alarm kebakaran terputus
acc_newEventNo_244=Sirkuit pendek input alarm kebakaran
acc_newEventNo_247=Papan pengembangan online
acc_newEventNo_4008=Pemulihan listrik
acc_newEventNo_4014=Sinyal input kebakaran terputus, pintu akhir biasanya terbuka
acc_newEventNo_4015=Pintunya online
acc_newEventNo_4018=Perbandingan Backend Buka
acc_newEventNo_5023=Status perlindungan api terbatas
acc_newEventNo_5024=Timeout Verifikasi Multi-Personal
acc_newEventNo_5029=Perbandingan Backend Gagal
acc_newEventNo_6005=Kapasitas Rekam mencapai batas atas
acc_newEventNo_6006=Sirkuit pendek baris (RS485)
acc_newEventNo_6007=Short circuit in the circuit (Wigan)
acc_newEventNo_6011=Pintunya offline
acc_newEventNo_6012=Alarm penghapusan pintu
acc_newEventNo_6013=Sinyal input kebakaran diaktifkan, pintu terbuka
acc_newEventNo_6015=Ulangi pasokan listrik perangkat ekspansi
acc_newEventNo_6016=Kembalikan pengaturan pabrik mesin
acc_newEventNo_6070=Perbandingan Backend (Daftar Terlarang)
acc_eventNo_undefined=Nomor Acara Tidak Terdefinisi
acc_advanceEvent_500=Global Anti-Passback (logis)
acc_advanceEvent_501=Ketersediaan Orang (gunakan Tanggal)
acc_advanceEvent_502=Jumlah Kontrol Orang
acc_advanceEvent_503=Interlock Global
acc_advanceEvent_504=Kontrol Rute
acc_advanceEvent_505=Global Anti-Passback (waktunya)
acc_advanceEvent_506=Global Anti-Passback (berjangka waktu logis)
acc_advanceEvent_507=Ketersediaan Orang (setelah penggunaan hari pertama yang valid)
acc_advanceEvent_508=Ketersediaan Orang (gunakan beberapa kali)
acc_advanceEvent_509=Verifikasi latar belakang gagal (personel tidak terdaftar)
acc_advanceEvent_510=Verifikasi latar belakang gagal (pengecualian data)
acc_alarmEvent_701=Pelanggaran DMR (aturan pengaturan: {0})
#[21]实时监控
acc_rtMonitor_openDoor=Buka
acc_rtMonitor_closeDoor=Tutup
acc_rtMonitor_remoteNormalOpen=Jarak Jauh Biasanya Terbuka
acc_rtMonitor_realTimeEvent=Acara Real-Time
acc_rtMonitor_photoMonitor=Pemantauan Foto
acc_rtMonitor_alarmMonitor=Pemantauan Alarm
acc_rtMonitor_doorState=Status Pintu
acc_rtMonitor_auxOutName=Nama Output Bantu
acc_rtMonitor_nonsupport=Tidak didukung
acc_rtMonitor_lock=Terkunci
acc_rtMonitor_unLock=Tidak Terkunci
acc_rtMonitor_disable=Dinonaktifkan
acc_rtMonitor_noSensor=Tidak Ada Sensor Pintu
acc_rtMonitor_alarm=Alarm
acc_rtMonitor_openForce=Dibuka Secara paksa
acc_rtMonitor_tamper=Tamper
acc_rtMonitor_duressPwdOpen=Buka Kata Sandi Buka
acc_rtMonitor_duressFingerOpen=Buka Sidik Jari Duress
acc_rtMonitor_duressOpen=Buka paksa
acc_rtMonitor_openTimeout=Membuka Batas Waktu
acc_rtMonitor_unknown=Tidak Dikenal
acc_rtMonitor_noLegalDoor=Tidak ada pintu yang memenuhi persyaratan.
acc_rtMonitor_noLegalAuxOut=Tidak ada output tambahan yang memenuhi syarat!
acc_rtMonitor_curDevNotSupportOp=Status perangkat saat ini tidak mendukung operasi ini!
acc_rtMonitor_curNormalOpen=Saat ini normal terbuka
acc_rtMonitor_whetherDisableTimeZone=Keadaan pintu saat ini selalu terbuka.
acc_rtMonitor_curSystemNoDoors=Sistem saat ini belum menambahkan pintu apa pun atau tidak dapat menemukan pintu apa pun yang memenuhi persyaratan Anda.
acc_rtMonitor_cancelAlarm=Batalkan Alarm
acc_rtMonitor_openAllDoor=Buka semua pintu saat ini
acc_rtMonitor_closeAllDoor=Tutup semua pintu saat ini
acc_rtMonitor_confirmCancelAlarm=Anda yakin untuk membatalkan alarm ini?
acc_rtMonitor_calcelAllDoor=Batalkan semua alarm
acc_rtMonitor_initDoorStateTip=Memperoleh semua pintu yang diotorisasi untuk pengguna dalam sistem ...
acc_rtMonitor_alarmEvent=Acara Alarm
acc_rtMonitor_ackAlarm=Pengakuan
acc_rtMonitor_ackAllAlarm=Akui Semua
acc_rtMonitor_ackAlarmTime=Akui Waktu
acc_rtMonitor_sureToAckThese=Apakah Anda yakin Anda mengonfirmasi alarm {0} ini? Setelah konfirmasi Anda, semua alarm akan dibatalkan.
acc_rtMonitor_sureToAckAllAlarm=Anda yakin ingin membatalkan semua alarm? Setelah konfirmasi Anda, semua alarm akan dibatalkan.
acc_rtMonitor_noSelectAlarmEvent=Silakan pilih untuk mengkonfirmasi acara alarm!
acc_rtMonitor_noAlarmEvent=Tidak ada acara alarm di sistem saat ini!
acc_rtMonitor_forcefully=Batalkan Alarm (Pintu Dibuka Secara paksa)
acc_rtMonitor_addToRegPerson=Ditambahkan ke orang yang terdaftar
acc_rtMonitor_cardExist=Kartu ini telah ditetapkan oleh {0} dan tidak dapat dikeluarkan lagi.
acc_rtMonitor_opResultPrompt=Kirim permintaan {0} berhasil, gagal {1}.
acc_rtMonitor_doorOpFailedPrompt=Gagal mengirim permintaan ke pintu berikut, silakan coba lagi!
acc_rtMonitor_remoteOpen=Buka Jarak Jauh
acc_rtMonitor_remoteClose=Tutup Jarak Jauh
acc_rtMonitor_alarmSoundClose=Audio ditutup
acc_rtMonitor_alarmSoundOpen=Audio dibuka
acc_rtMonitor_playAudio=Pengingat Suara
acc_rtMonitor_isOpenShowPhoto=Aktifkan Fungsi Tampilan Foto
acc_rtMonitor_isOpenPlayAudio=Aktifkan Fungsi Peringatan Audio
acc_rtm_open=Remote Buka Tombol
acc_rtm_close=Remote Tutup Tombol
acc_rtm_eleModule=Elevator
acc_cancelAlarm_fp=Batalkan Alarm (Buka Sidik Jari Duress)
acc_cancelAlarm_pwd=Batalkan Alarm (Buka Kata Sandi Buka)
acc_cancelAlarm_timeOut=Batalkan Alarm (Perpanjangan Batas Waktu Pintu Terbuka)
#定时同步设备时间
acc_timing_syncDevTime=Perangkat waktu sinkronisasi waktu
acc_timing_executionTime=Waktu eksekusi
acc_timing_theLifecycle=Siklus hidup
acc_timing_errorPrompt=Input salah.
acc_timing_checkedSyncTime=Silakan pilih waktu sinkronisasi.
#[25]门禁报表
acc_trans_hasAccLevel=Memiliki Level untuk Diakses
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Silakan tambahkan zona
acc_zone_code=Kode Zona
acc_zone_parentZone=Zona Induk
acc_zone_parentZoneCode=Kode Zona Induk
acc_zone_parentZoneName=Nama Zona Induk
acc_zone_outside=Di luar
#[G2]读头定义
acc_readerDefine_readerName=Nama Pembaca
acc_readerDefine_fromZone=Pergi Dari
acc_readerDefine_toZone=Pergi Ke
acc_readerDefine_delInfo1=Zona definisi pembaca ini dirujuk oleh fungsi kontrol akses lanjutan dan tidak dapat dihapus!
acc_readerDefine_selReader=Pilih Pembaca
acc_readerDefine_selectReader=Silakan tambahkan pembaca!
acc_readerDefine_tip=Setelah personel tiba di luar zona, catatan personel akan dihapus.
#[G3]全局反潜
acc_gapb_zone=Zona
acc_gapb_whenToResetGapb=Reset Waktu Anti-Passback
acc_gapb_apbType=Jenis Anti-Passback
acc_gapb_logicalAPB=Anti-Passback Logis
acc_gapb_timedAPB=Anti-Passback Berwaktu
acc_gapb_logicalTimedAPB=Logical Berwaktu Anti-Passback
acc_gapb_lockoutDuration=Durasi Lockout
acc_gapb_devOfflineRule=Jika perangkat offline
acc_gapb_standardLevel=Level Akses Standar
acc_gapb_accessDenied=Akses Ditolak
acc_gapb_doorControlZone=Pintu berikut mengontrol akses masuk dan keluar dari zona
acc_gapb_resetStatus=Reset Status Anti-Passback
acc_gapb_obeyAPB=Patuhi aturan Anti-Passback
acc_gapb_isResetGAPB=Reset Anti-Passback
acc_gapb_resetGAPBSuccess=Reset keberhasilan status Anti-Passback
acc_gapb_resetGAPBFaile=Reset kondisi Anti-Passback gagal
acc_gapb_chooseArea=Silakan pilih kembali zona.
acc_gapb_notDelInfo1=Zona akses dirujuk sebagai area akses superior dan tidak dapat dihapus.
acc_gapb_notDelInfo2=Zona direferensikan oleh Reader Define dan tidak dapat dihapus.
acc_gapb_notDelInfo3=Zona direferensikan oleh fungsi kontrol akses lanjutan dan tidak dapat dihapus.
acc_gapb_notDelInfo4=Area akses dirujuk oleh LED dan tidak dapat dihapus!
acc_gapb_zoneNumRepeat=Nomor domain kontrol memiliki duplikat
acc_gapb_zoneNameRepeat=Nama domain kontrol memiliki duplikat
acc_gapb_personResetGapbPre=Anda yakin mengatur ulang ini?
acc_gapb_personResetGapbSuffix=aturan orang Anti-Passback?
acc_gapb_apbPrompt=Satu pintu tidak dapat digunakan untuk mengontrol dua batas perimeter yang relatif independen.
acc_gapb_occurApb=Anti-Passback terjadi
acc_gapb_noOpenDoor=Jangan membuka pintu
acc_gapb_openDoor=Buka pintunya
acc_gapb_zoneNumLength=Panjangnya lebih dari 20 karakter
acc_gapb_zoneNameLength=Panjangnya lebih dari 30 karakter
acc_gapb_zoneRemarkLength=Panjangnya lebih dari 50 karakter
acc_gapb_isAutoServerMode=Perangkat tanpa fungsi verifikasi latar belakang terdeteksi, yang dapat memengaruhi fungsi. Buka sekarang?
acc_gapb_applyTo=Berlaku untuk
acc_gapb_allPerson=Semua Personel
acc_gapb_justSelected=Personil Yang Baru Dipilih
acc_gapb_excludeSelected=Kecualikan Personil yang Dipilih
#[G4]who is inside
acc_zoneInside_lastAccessTime=Waktu Akses Terakhir
acc_zoneInside_lastAccessReader=Pembaca Akses Terakhir
acc_zoneInside_noPersonInZone=Tidak ada orang di zona tersebut
acc_zoneInside_noRulesInZone=Tidak ada aturan yang ditetapkan.
acc_zoneInside_totalPeople=Jumlah Orang
acc_zonePerson_selectPerson=Silakan pilih orang atau departemen!
#[G5]路径
acc_route_name=Nama rute
acc_route_setting=Pengaturan Rute
acc_route_addReader=Tambah Pembaca
acc_route_delReader=Hapus Pembaca
acc_route_defineReaderLine=Tentukan baris pembaca
acc_route_up=Naik
acc_route_down=Turun
acc_route_selReader=Pilih pembaca setelah operasi.
acc_route_onlyOneOper=Hanya dapat memilih satu pembaca untuk beroperasi.
acc_route_readerOrder=Urutan yang ditentukan pembaca
acc_route_atLeastSelectOne=Silakan pilih setidaknya satu readhead!
acc_route_routeIsExist=Rute ini sudah ada!
#[G6]DMR
acc_dmr_residenceTime=Waktu tinggal
acc_dmr_setting=pengaturan DMR
#[G7]Occupancy
acc_occupancy_max=Kapasitas maksimum
acc_occupancy_min=Kapasitas minimum
acc_occupancy_unlimit=Tidak terbatas
acc_occupancy_note=Kapasitas maksimum harus lebih besar dari kapasitas minimum!
acc_occupancy_containNote=Silakan masukkan setidaknya satu dari kapasitas maksimum / minimum!
acc_occupancy_maxMinValid=Silakan tentukan kapasitas lebih besar dari 0.
acc_occupancy_conflict=Aturan kontrol hunian telah ditetapkan di zona ini.
acc_occupancy_maxMinTip=Tidak ada nilai kapasitas berarti tidak ada batasan.
#card availability
acc_personLimit_zonePropertyName=Nama properti zona
acc_personLimit_useType=Gunakan
acc_personLimit_userDate=Tanggal Valid
acc_personLimit_useDays=Setelah penggunaan hari pertama yang valid
acc_personLimit_useTimes=Gunakan beberapa kali
acc_personLimit_setZoneProperty=Setel Properti Zona
acc_personLimit_zoneProperty=Zone Properties
acc_personLimit_availabilityName=Nama Ketersediaan
acc_personLimit_days=Hari
acc_personLimit_Times=Waktu
acc_personLimit_noDel=Properti zona kontrol akses terpilih direferensikan dan tidak dapat dihapus!
acc_personLimit_cannotEdit=Atribut area akses direferensikan, tidak dapat dimodifikasi!
acc_personLimit_detail=Detail
acc_personLimit_userDateTo=Valid Hingga
acc_personLimit_addPersonRepeatTip=Orang di bawah departemen yang dipilih telah ditambahkan ke atribut area kontrol akses, silakan pilih departemen itu lagi!
acc_personLimit_leftTimes={0} Waktu Tersisa
acc_personLimit_expired=kedaluwarsa
acc_personLimit_unused=tidak terpakai
#全局互锁
acc_globalInterlock_addGroup=Tambah Grup
acc_globalInterlock_delGroup=Hapus Grup
acc_globalInterlock_refuseAddGroupMessage=Interlock yang sama, pintu tidak dapat diduplikasi dalam grup yang ditambahkan
acc_globalInterlock_refuseAddlockMessage=Pintu yang ditambahkan telah muncul di grup lain dari perangkat yang saling terkait
acc_globalInterlock_refuseDeleteGroupMessage=Harap hapus data terkait interlock
acc_globalInterlock_isGroupInterlock=Interlock Grup
acc_globalInterlock_isAddTheDoorImmediately=Tambahkan pintu segera
acc_globalInterlock_isAddTheGroupImmediately=Tambahkan grup dengan segera
#门禁参数设置
acc_param_autoEventDev=Otomatis mengunduh log peristiwa jumlah perangkat bersamaan
acc_param_autoEventTime=Otomatis mengunduh log peristiwa dari interval bersamaan
acc_param_noRepeat=Tidak ada pengulangan alamat email yang diizinkan, silakan isi lagi.
acc_param_most18=Tambahkan hingga 18 alamat email
acc_param_deleteAlert=Tidak bisa menghapus semua kotak input alamat email.
acc_param_invalidOrRepeat=Kesalahan format alamat email atau pengulangan alamat email.
#全局联动
acc_globalLinkage_noSupport=Pintu yang saat ini dipilih tidak mendukung fungsi mengunci dan membuka kunci.
acc_globalLinkage_trigger=Trigger Global Linkage
acc_globalLinkage_noAddPerson=Aturan tautan tidak berisi pemicu terkait personil, secara default, berlaku untuk semua personel!
acc_globalLinkage_selectAtLeastOne=Silakan pilih setidaknya satu operasi pemicu tautan!
acc_globalLinkage_selectTrigger=Silakan tambahkan kondisi pemicu tautan!
acc_globalLinkage_selectInput=Silakan tambahkan titik input!
acc_globalLinkage_selectOutput=Silakan tambahkan titik output!
acc_globalLinkage_audioRemind=Tautan Voice Prompts
acc_globalLinkage_audio=Linkage Voice
acc_globalLinkage_isApplyToAll=Berlaku untuk semua personil
acc_globalLinkage_scope=Rentang Personil
acc_globalLinkage_everyPerson=Apa saja
acc_globalLinkage_selectedPerson=Dipilih
acc_globalLinkage_noSupportPerson=Tidak didukung
acc_globalLinkage_reselectInput=Jenis kondisi pemicu telah berubah, silakan pilih kembali titik input!
acc_globalLinkage_addPushDevice=Silakan tambahkan dukungan untuk fungsi perangkat ini
#其他
acc_InputMethod_tips=Silakan beralih ke mode input bahasa Inggris!
acc_device_systemCheckTip=Akses perangkat tidak ada!
acc_notReturnMsg=Tidak ada informasi yang dikembalikan
acc_validity_period=Lisensi telah melewati periode yang valid; fungsi tidak dapat beroperasi.
acc_device_pushMaxCount=Sistem sudah ada di perangkat {0}, mencapai batas lisensi, tidak dapat menambahkan perangkat!
acc_device_videoHardwareLinkage=Atur Tautan Perangkat Keras Video
acc_device_videoCameraIP=IP Kamera Video
acc_device_videoCameraPort=Port Kamera Video
acc_location_unable=Titik kejadian tidak ditambahkan ke peta elektronik, tidak dapat menemukan lokasi tertentu!
acc_device_wgDevMaxCount=Perangkat telah mencapai batas lisensi dalam sistem dan tidak dapat mengubah pengaturan!
#自定义报警事件
acc_deviceEvent_selectSound=Silakan pilih file audio.
acc_deviceEvent_batchSetSoundErr=Batch set alarm berbunyi tidak normal.
acc_deviceEvent_batchSet=Atur Audio
acc_deviceEvent_sound=Suara Acara
acc_deviceEvent_exist=Sudah Ada
acc_deviceEvent_upload=Unggah
#查询门最近发生事件
acc_doorEventLatestHappen=Permintaan acara terkini dari pintu
#门禁人员信息
acc_pers_delayPassage=Delay Passage
#设备容量提示
acc_dev_usageConfirm=Kapasitas saat ini yang melebihi 90% dari peralatan
acc_dev_immediateCheck=Segera periksa
acc_dev_inSoftware=Dalam Perangkat Lunak
acc_dev_inFirmware=Di FirmWare
acc_dev_get=Dapatkan
acc_dev_getAll=Dapatkan Semua
acc_dev_loadError=Memuat Kegagalan
#Reader
acc_reader_inout=Masuk/Keluar
acc_reader_lightRule=Lampu Aturan
acc_reader_defLightRule=Aturan Default
acc_reader_encrypt=Enkripsi
acc_reader_allReaderOfCurDev=Semua Pembaca di Perangkat Saat Ini
acc_reader_tip1=Enkripsi disalin ke semua pembaca di perangkat saat ini!
acc_reader_tip2=Opsi mode ID hanya tersedia untuk readhead yang mendukung fitur ini!
acc_reader_tip3=Jenis protokol RS485 disalin ke semua pembaca di perangkat saat ini. Pengaturan akan berlaku setelah perangkat dimulai ulang!
acc_reader_tip4=Opsi untuk menyembunyikan beberapa informasi personel disalin ke semua pembaca perangkat yang sama secara default!
acc_reader_commType=Jenis Komunikasi
acc_reader_commAddress=Alamat Komunikasi
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=Dinonaktifkan
acc_readerComAddress_repeat=Alamat komunikasi rangkap
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=alamat RS485
acc_readerCommType_wgAddress=Alamat Wiegand
acc_reader_macError=Silakan masukkan alamat Mac dalam format yang benar!
acc_reader_machineType=Jenis Pembaca
acc_reader_readMode=Mode
acc_reader_readMode_normal=Mode Normal
acc_reader_readMode_idCard=Mode Kartu ID
acc_reader_note=Tips: Hanya perangkat kontrol akses di area yang sama ({0}) karena kamera dapat dipilih.
acc_reader_rs485Type=Jenis Protokol RS485
acc_reader_userLock=Kunci Akses Personil
acc_reader_userInfoReveal=Sembunyikan Informasi Bagian Personalia
#operat
acc_operation_pwd=Kata Sandi Operasi
acc_operation_pwd_error=Kesalahan Kata Sandi
acc_new_input_not_same=Input kunci baru tidak konsisten
acc_op_set_keyword=Pengaturan kunci lisensi
acc_op_old_key=Kunci Lama
acc_op_new_key=Kunci Baru
acc_op_cofirm_key=Kunci Konfirmasi
acc_op_old_key_error=Kesalahan kunci lama
#验证方式规则
acc_verifyRule_name=Nama Aturan
acc_verifyRule_door=Verifikasi Pintu
acc_verifyRule_person=Verifikasi Personil
acc_verifyRule_copy=Salin Pengaturan Senin ke Lainnya pada Hari Kerja:
acc_verifyRule_tip1=Silakan pilih setidaknya satu mode verifikasi!
acc_verifyRule_tip2=Jika aturan tersebut berisi mode verifikasi seseorang, Anda hanya dapat menambahkan pintu dengan pembaca Wiegand!
acc_verifyRule_tip3=RS485 reader hanya dapat mengikuti mode verifikasi pintu, tidak mendukung mode verifikasi personel.
acc_verifyRule_oldVerifyMode=Mode Verifikasi Lama
acc_verifyRule_newVerifyMode=Mode verifikasi baru
acc_verifyRule_newVerifyModeSelectTitle=Pilih metode verifikasi baru
acc_verifyRule_newVerifyModeNoSupportTip=Tidak ada perangkat yang mendukung metode verifikasi baru!
#Wiegand Test
acc_wiegand_beforeCard=Panjang kartu baru ({0} bit) tidak sama dengan kartu sebelumnya!
acc_wiegand_curentCount=Panjang Nomor Kartu Saat Ini:{0} Bit
acc_wiegand_card=Kartu
acc_wiegand_readCard=Baca Kartu
acc_wiegand_clearCardInfo=Hapus Informasi Kartu
acc_wiegand_originalCard=Nomor Kartu Asli
acc_wiegand_recommendFmt=Format Kartu yang Disarankan
acc_wiegand_parityFmt=Format Paritas Ganjil-Genap
acc_wiegand_withSizeCode=Otomatis menghitung kode situs sementara kode situs dibiarkan kosong
acc_wiegand_tip1=Kartu-kartu ini mungkin tidak termasuk kelompok kartu yang sama.
acc_wiegand_tip2=Kode situs: {0}, Nomor kartu: {1}, gagal cocok dengan nomor kartu aslinya. Silakan periksa kode situs dan nomor kartu yang dimasukkan lagi!
acc_wiegand_tip3=Nomor kartu yang dimasukkan ({0}) tidak dapat dicocokkan dengan nomor kartu asli. Silakan periksa lagi!
acc_wiegand_tip4=Kode situs yang dimasukkan ({0}) tidak dapat dicocokkan dengan nomor kartu asli. Silakan periksa lagi!
acc_wiegand_tip5=Jika Anda ingin menggunakan fitur ini, harap kosongkan semua kolom kode situs!
acc_wiegand_warnInfo1=Ketika Anda terus membaca kartu baru, silakan beralih ke kartu berikutnya secara manual.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Papan Masuk / Keluar Personil
acc_LCDRTMonitor_current=Informasi Personel Saat Ini
acc_LCDRTMonitor_previous=Informasi Personel Sebelumnya
#api
acc_api_levelIdNotNull=ID grup Privilege tidak boleh kosong
acc_api_levelExist=Grup izin ada
acc_api_areaNameNotNull=Daerah tidak dapat kosong
acc_api_levelNotExist=Grup izin tidak ada
acc_api_levelNotHasPerson=Tidak ada orang di bawah grup izin
acc_api_doorIdNotNull=ID Pintu tidak boleh kosong
acc_api_doorNameNotNull=Nama pintu tidak boleh kosong
acc_api_doorIntervalSize=Buka pintu antara 1 ~ 254
acc_api_doorNotExist=Pintu tidak ada
acc_api_devOffline=Perangkat sedang offline atau dinonaktifkan
acc_api_devSnNotNull=Perangkat SN tidak boleh kosong
acc_api_timesTampNotNull=Timestamp tidak dapat kosong
acc_api_openingTimeCannotBeNull=Waktu buka pintu tidak boleh kosong
acc_api_parameterValueCannotBeNull=Nilai parameter tidak boleh kosong
acc_api_deviceNumberDoesNotExist=Nomor seri perangkat tidak ada
acc_api_readerIdCannotBeNull=ID Pembaca tidak boleh kosong
acc_api_theReaderDoesNotExist=Kepala baca tidak ada
acc_operate_door_notInValidDate=Saat ini tidak tersedia dalam jarak jauh untuk waktu yang efektif, hubungi admin jika perlu!
acc_api_doorOffline=Pintu sedang offline atau dinonaktifkan
#门禁信息自动导出
acc_autoExport_title=Eksport maklumat automatik
acc_autoExport_frequencyTitle=Kekerapan eksport secara automatik
acc_autoExport_frequencyDay=Setiap hari
acc_autoExport_frequencyMonth=Bulanan
acc_autoExport_firstDayMonth=Hari pertama bulan itu
acc_autoExport_specificDate=Tarikh eksport
acc_autoExport_exportModeTitle=Mod eksport
acc_autoExport_dailyMode=Maklumat harian
acc_autoExport_monthlyMode=Maklumat bulanan(Semua maklumat dan maklumat pada bulan lepas)
acc_autoExport_allMode=Semua maklumat(Sehingga 30,000 mesej)
acc_autoExport_recipientMail=Menerima peti mel
#First In And Last Out
acc_inOut_inReaderName=Nama Pembaca Entri Awal
acc_inOut_firstInTime=Waktu masuk paling awal
acc_inOut_outReaderName=Nama pembaca-cuti terakhir
acc_inOut_lastOutTime=Waktu cuti terakhir
#防疫参数
acc_dev_setHep=Tetapkan Parameter Pencegahan Epidemi
acc_dev_enableIRTempDetection=Aktifkan Pemeriksaan Suhu dengan Infra Merah
acc_dev_enableNormalIRTempPass=Tolak Akses Saat Suhu Di Atas Rentang
acc_dev_enableMaskDetection=Aktifkan Pendeteksian Topeng
acc_dev_enableWearMaskPass=Tolak Akses Tanpa Masker
acc_dev_tempHighThreshold=Ambang Alarm Suhu Tinggi
acc_dev_tempUnit=Unit Suhu
acc_dev_tempCorrection=Koreksi Penyimpangan Suhu
acc_dev_enableUnregisterPass=Izinkan Orang Tidak Terdaftar Mengakses
acc_dev_enableTriggerAlarm=Pemicu Alarm Eksternal
#联动邮件
acc_mail_temperature=Suhu tubuh
acc_mail_mask=Apakah akan memakai topeng
acc_mail_unmeasured=Tidak terukur
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Acara Global Digifort
acc_digifort_chooseDigifortEvents=Pilih Acara Global Digifort
acc_digifort_eventExpiredTip=Jika acara global dihapus dari server Digifort, itu akan berwarna merah.
acc_digifort_checkConnection=Periksa apakah informasi koneksi server Digifort benar.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Tambahkan Kontak
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=Set parameter yang diperluas
acc_extendParam_faceUI=Tampilan antarmuka
acc_extendParam_faceParam=Parameter wajah
acc_extendParam_accParam=Parameter kontrol akses
acc_extendParam_intercomParam=Parameter interkom visual
acc_extendParam_volume=Volume
acc_extendParam_identInterval=Interval identifikasi (ms)
acc_extendParam_historyVerifyResult=Tampilkan hasil verifikasi historis
acc_extendParam_macAddress=Tampilkan alamat MAC
acc_extendParam_showIp=Tampilkan alamat IP
acc_extendParam_24HourFormat=Tampilkan format 24 jam
acc_extendParam_dateFormat=Format tanggal
acc_extendParam_1NThreshold=1: N ambang batas
acc_extendParam_facePitchAngle=Pitch angle of face
acc_extendParam_faceRotationAngle=Sudut rotasi wajah
acc_extendParam_imageQuality=Kualitas gambar
acc_extendParam_miniFacePixel=Pixel wajah minimum
acc_extendParam_biopsy=Aktifkan biopsi
acc_extendParam_showThermalImage=Tampilkan gambar termal
acc_extendParam_attributeAnalysis=Aktifkan analisis atribut
acc_extendParam_temperatureAttribute=Atribut deteksi suhu
acc_extendParam_maskAttribute=Atribut deteksi topeng
acc_extendParam_minTemperature=Batas bawah batas deteksi suhu tubuh
acc_extendParam_maxTemperature=Batas atas ambang deteksi suhu tubuh
acc_extendParam_gateMode=Mode Gerbang
acc_extendParam_qrcodeEnable=Aktifkan fungsi QR code
#可视对讲
acc_dev_intercomServer=Alamat layanan interkomunikasi visual
acc_dev_intercomPort=Server interkom visual
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Sinkronisasi Level
# 夏令时名称
acc_dsTimeUtc_none=Tidak ditetapkan
acc_dsTimeUtc_AreaNone=Tidak ada cahaya siang menghemat waktu di daerah ini
acc_dsTimeUtc1000_0=Canberra, Melbourne, Sydney
acc_dsTimeUtc1000_1=Hobart
acc_dsTimeUtc_0330_0=Newfoundland
acc_dsTimeUtc_1000_0=Kepulauan Aleutian
acc_dsTimeUtc_0200_0=Tengah Atlantik - digunakan
acc_dsTimeUtc0930_0=Adelaide
acc_dsTimeUtc_0100_0=Azores
acc_dsTimeUtc_0400_0=Waktu Atlantik (Kanada)
acc_dsTimeUtc_0400_1=Santiago
acc_dsTimeUtc_0400_2=Asuncion
acc_dsTimeUtc_0300_0=Greenland
acc_dsTimeUtc_0300_1=Kepulauan Saint Pierre dan Miquelon
acc_dsTimeUtc0200_0=Chisinau
acc_dsTimeUtc0200_1=Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius
acc_dsTimeUtc0200_2=Athens, Bukarest
acc_dsTimeUtc0200_3=Yerusalem
acc_dsTimeUtc0200_4=Amman
acc_dsTimeUtc0200_5=Beirut
acc_dsTimeUtc0200_6=Damaskus
acc_dsTimeUtc0200_7=Gaza, Hebron
acc_dsTimeUtc0200_8=Juba
acc_dsTimeUtc_0600_0=Waktu Tengah (Amerika Serikat dan Kanada)
acc_dsTimeUtc_0600_1=Guadalajara, Mexico City, Monterrey
acc_dsTimeUtc_0600_2=Pulau Paskah
acc_dsTimeUtc1300_0=Pulau Samoa
acc_dsTimeUtc_0500_0=Havana
acc_dsTimeUtc_0500_1=Waktu Timur (Amerika Serikat dan Kanada)
acc_dsTimeUtc_0500_2=Haiti
acc_dsTimeUtc_0500_3=Indiana (Timur)
acc_dsTimeUtc_0500_4=Pulau Turks dan Caicos
acc_dsTimeUtc_0800_0=Waktu Pasifik (Amerika Serikat dan Kanada)
acc_dsTimeUtc_0800_1=Baja California
acc_dsTimeUtc0330_0=teheran atau tehran
acc_dsTimeUtc0000_0=Dublin, Edinburgh, Lisbon, London
acc_dsTimeUtc1200_0=Fiji
acc_dsTimeUtc1200_1=Petropavlovsk Kamchatka tua
acc_dsTimeUtc1200_2=Wellington, Auckland
acc_dsTimeUtc1100_0=Pulau Norfolk
acc_dsTimeUtc_0700_0=Chihuahua, La Paz, Mazatlan
acc_dsTimeUtc_0700_1=Waktu gunung (Amerika Serikat dan Kanada)
acc_dsTimeUtc0100_0=Belgrade, Bratislava, Budapest, Ljubljana, Praha
acc_dsTimeUtc0100_1=Sarajevo, Skopje, Warsaw, Zagreb
acc_dsTimeUtc0100_2=Casablanca
acc_dsTimeUtc0100_3=Brussels, Kopenhagen, Madrid, Paris
acc_dsTimeUtc0100_4=Amsterdam, Berlin, Bern, Roma, Stockholm, Vienna
acc_dsTimeUtc_0900_0=Alaska
#安全点(muster point)
acc_leftMenu_accMusterPoint=Titik Kumpulkan
acc_musterPoint_activate=Aktifkan
acc_musterPoint_addDept=Tambah Departemen
acc_musterPoint_delDept=Hapus Departemen
acc_musterPoint_report=Laporan Poin Pengumpulan
acc_musterPointReport_sign=Masuk Secara Manual
acc_musterPointReport_generate=Buat Laporan
acc_musterPoint_addSignPoint=Tambah Titik Tanda
acc_musterPoint_delSignPoint=Hapus Titik Tanda
acc_musterPoint_selectSignPoint=Harap tambahkan titik tanda!
acc_musterPoint_signPoint=Titik Tanda Tangan
acc_musterPoint_delFailTip=Sudah ada poin yang dikumpulkan dan tidak bisa dihapus!
acc_musterPointReport_enterTime=Masukkan Waktu
acc_musterPointReport_dataAnalysis=Analisis Data
acc_musterPointReport_safe=Aman
acc_musterPointReport_danger=Bahaya
acc_musterPointReport_signInManually=pukulan manual
acc_musterPoint_editTip=Kumpulan poin aktif dan tidak dapat diedit!
acc_musterPointEmail_total=Kehadiran yang diharapkan:
acc_musterPointEmail_safe=Diperiksa (aman):
acc_musterPointEmail_dangerous=Dalam bahaya :
acc_musterPoint_messageNotification=Pemberitahuan pesan saat diaktifkan
acc_musterPointReport_sendEmail=Laporan dorongan dijadwalkan
acc_musterPointReport_sendInterval=Kirim Interval
acc_musterPointReport_sendTip=Silakan pastikan bahwa metode pemberitahuan yang dipilih dikonfigurasi dengan sukses, sebaliknya pemberitahuan tidak akan dikirim secara normal!
acc_musterPoint_mailSubject=Berita pengumpulan darurat
acc_musterPoint_mailContent=Harap segera berkumpul di "{0}" dan masuk di perangkat "{1}", terima kasih!
acc_musterPointReport_mailHead=Hai, ini laporan darurat. Tolong ulangi.
acc_musterPoint_visitorsStatistics=Statistik pengunjung
# 报警监控
acc_alarm_priority=prioritas
acc_alarm_total=total
acc_alarm_today=Rekaman hari ini
acc_alarm_unhandled=Tidak dikonfirmasi
acc_alarm_inProcess=Dalam proses
acc_alarm_acknowledged=Dikonfirmasi
acc_alarm_top5=Lima peristiwa alarm terbaik
acc_alarm_monitoringTime=Waktu pengawasan
acc_alarm_history=Sejarah proses penggera
acc_alarm_acknowledgement=Memproses catatan
acc_alarm_eventDescription=Perincian peristiwa
acc_alarm_acknowledgeText=Setelah pemilihan, email rincian peristiwa alarm akan dikirim ke kotak surat yang dinyatakan
acc_alarm_emailSubject=Tambah catatan proses peristiwa alarm
acc_alarm_mute=Diam
acc_alarm_suspend=suspend
acc_alarm_confirmed=Insiden telah dikonfirmasi
acc_alarm_list=Log Alarm
#ntp
acc_device_setNTPService=Pengaturan server NTP
acc_device_setNTPServiceTip=Masukkan beberapa alamat server, dipisahkan dengan koma (,) atau titik koma (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=Dalam operasi pengendali akses, pengguna Super tidak terbatas oleh peraturan pada zona waktu, anti-passback dan interlock dan memiliki prioritas terbuka pintu yang sangat tinggi.
acc_editPerson_delayPassageTip=Perluas waktu menunggu untuk personel melalui titik akses. Sesuai dengan tantangan fisik atau orang-orang dengan cacat lainnya.
acc_editPerson_disabledTip=Untuk sementara mematikan tingkat akses persunal.
#门禁向导
acc_guide_title=Wizard Pengaturan Modul Kontrol Akses
acc_guide_addPersonTip=Anda perlu menambahkan orang dan kredensial yang sesuai (wajah atau sidik jari atau kartu atau telapak tangan atau kata sandi); jika Anda sudah menambahkan, lewati langkah ini secara langsung
acc_guide_timesegTip=Silakan konfigurasikan periode waktu buka yang valid
acc_guide_addDeviceTip=Silakan tambahkan perangkat yang sesuai sebagai titik akses
acc_guide_addLevelTip=Tambahkan tingkat kontrol akses
acc_guide_personLevelTip=Tetapkan otoritas kontrol akses yang sesuai untuk orang tersebut
acc_guide_rtMonitorTip=Periksa catatan kontrol akses secara real time
acc_guide_rtMonitorTip2=Perlihatan catatan kontrol akses pada waktu nyata setelah menambah area dan pintu yang sesuai
#查看区域内人员
acc_zonePerson_cleanCount=Hapus statistik orang yang masuk dan keluar
acc_zonePerson_inCount=Statistik jumlah orang yang masuk
acc_zonePerson_outCount=Statistik jumlah orang yang keluar
#biocv460
acc_device_validFail=Nama pengguna atau kata sandi salah dan verifikasi gagal!
acc_device_pwdRequired=Bisa hanya memasukkan maksimum 6 integer