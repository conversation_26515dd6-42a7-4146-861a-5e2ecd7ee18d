#[1]左侧菜单
acc_module=Accès
acc_leftMenu_accDev=Dispositif d'accès
acc_leftMenu_auxOut=Sortie auxiliaire
acc_leftMenu_dSTime=Heure d'été
acc_leftMenu_access=Contrôle d'accès
acc_leftMenu_door=Porte
acc_leftMenu_accRule=Règle d'accès
acc_leftMenu_interlock=Interverrouillage
acc_leftMenu_antiPassback=Anti-Passback
acc_leftMenu_globalLinkage=Liaison Global
acc_leftMenu_firstOpen=Première-Personne ouvre normalement
acc_leftMenu_combOpen=Multi-Personne ouvre la porte
acc_leftMenu_personGroup=Groupe personne multiple
acc_leftMenu_level=Niveaux d'accès
acc_leftMenu_electronicMap=Carte
acc_leftMenu_personnelAccessLevels=Niveaux d'accès du Personnel
acc_leftMenu_searchByLevel=Par niveau d'accès
acc_leftMenu_searchByDoor=Droits d'accès à la porte
acc_leftMenu_expertGuard=Fonctions avancées
acc_leftMenu_zone=Zone
acc_leftMenu_readerDefine=Lecteur Défini
acc_leftMenu_gapbSet=Anti-Passback Global
acc_leftMenu_whoIsInside=Qui est à l'intérieur
acc_leftMenu_whatRulesInside=Quelles règles s'appliquent à l'intérieur
acc_leftMenu_occupancy=Contrôle d'occupation
acc_leftMenu_route=Contrôle de l'itinéraire
acc_leftMenu_globalInterlock=Verrouillage Global
acc_leftMeue_globalInterlockGroup=Groupe de verrouillage global
acc_leftMenu_dmr=Règle d'homme mort
acc_leftMenu_personLimit=Disponibilité de la personne
acc_leftMenu_verifyModeRule=Mode de Vérification
acc_leftMenu_verifyModeRulePersonGroup=Groupe de mode de vérification
acc_leftMenu_extDev=Tableau E/S
acc_leftMenu_firstInLastOut=Premier entré et dernier sorti
acc_leftMenu_accReports=Rapports de contrôle d'accès
#[3]门禁时间段
acc_timeSeg_entity=Fuseau horaire
acc_timeSeg_canNotDel=La période est en cours d'utilisation et ne peut être supprimée!
#[4]门禁设备--公共的在common中
acc_common_ruleName=Nom de la règle
acc_common_hasBeanSet=A été défini
acc_common_notSet=Pas défini
acc_common_hasBeenOpened=A été ouvert
acc_common_notOpened=Pas ouvert
acc_common_partSet=Part de la configuration
acc_common_linkageAndApbTip=La liaison et la liaison globale, l'anti-passback et l'anti-passback global sont définis en même temps, il peut y avoir des conflits.
acc_common_vidlinkageTip=Assurez-vous que la liaison du point d'entrée correspondante est liée au canal vidéo disponible, sinon la fonction de liaison vidéo ne fonctionnera pas!
acc_common_accZoneFromTo=Impossible de définir la même zone
acc_common_logEventNumber=Identifiant évènement
acc_common_bindOrUnbindChannel=Relier/détacher la caméra
acc_common_boundChannel=Caméra liée
#设备信息
acc_dev_iconType=Type d'icône
acc_dev_carGate=Barrière de Parking
acc_dev_channelGate=Barrière de Volet
acc_dev_acpType=Type de panneau de contrôle
acc_dev_oneDoorACP=Panneau de contrôle d'accès à une porte
acc_dev_twoDoorACP=Panneau de contrôle d'accès à deux portes
acc_dev_fourDoorACP=Panneau de contrôle d'accès à quatre portes
acc_dev_onDoorACD=Appareil Autonome
acc_dev_switchToTwoDoorTwoWay=Basculer vers deux portes deux voies
acc_dev_addDevConfirm2=Conseil:La connexion du périphérique a abouti, mais le type du panneau de contrôle d'accès est différent de celui en place. Modifiez-le en {0} porte(s) panneau de contrôle. Continuer à ajouter?
acc_dev_addDevConfirm4=Appareil Autonome. Continuer à ajouter?
acc_dev_oneMachine=Appareil Autonome
acc_dev_fingervein=Veine de Doigt
acc_dev_control=Contrôle de l'appareil
acc_dev_protocol=Type de protocole
acc_ownedBoard=Conseil propriétaire
#设备操作
acc_dev_start=Début
acc_dev_accLevel=Autorité d'accès
acc_dev_timeZoneAndHoliday=Fuseau horaire, vacances
acc_dev_linkage=Liaison
acc_dev_doorOpt=Paramètres de porte
acc_dev_firstPerson=Première-Personne ouvre la porte
acc_dev_multiPerson=Multi-Personne ouvre la porte
acc_dev_interlock=Interverrouillage
acc_dev_antiPassback=AntiPassback
acc_dev_wiegandFmt=Format Wiegand
acc_dev_outRelaySet=Réglages de sortie auxiliaire
acc_dev_backgroundVerifyParam=Options Bg-Vérification
acc_dev_getPersonInfoPrompt=Assurez-vous d'avoir bien obtenu les informations du personnel, sinon une exception se produira. Continuer?
acc_dev_getEventSuccess=Obtenir les événements avec succès.
acc_dev_getEventFail=Obtention d'événement(s) échouée.
acc_dev_getInfoSuccess=Obtenir l'information avec succès.
acc_dev_getInfoXSuccess=Obtenir {0} avec succès.
acc_dev_getInfoFail=Obtention de l'information échouée.
acc_dev_updateExtuserInfoFail=Échec de la mise à jour des informations du personnel pour un passage prolongé. Veuillez récupérer les informations..
acc_dev_getPersonCount=Obtenir la quantité d'utilisateur
acc_dev_getFPCount=Obtenir la quantité d'Empreintes digitales
acc_dev_getFVCount=Obtenir la quantité de Veine de doigt
acc_dev_getFaceCount=Obtenir la quantité de visage
acc_dev_getPalmCount=Obtenir la quantité de paume
acc_dev_getBiophotoCount=Obtenir la quantité de photos de visage.
acc_dev_noData=Aucune donnée n'est renvoyée par l'appareil.
acc_dev_noNewData=Aucune nouvelle transaction dans l'appareil.
acc_dev_softLtDev=Il y a plus de personnes dans le logiciel que dans l'appareil.
acc_dev_personCount=Quantité de personnes:
acc_dev_personDetail=Les détails sont comme suit:
acc_dev_softEqualDev=La quantité de personnes dans le logiciel et l'appareil est la même.
acc_dev_softGtDev=Il y a plus de personnes dans l'appareil que dans le logiciel.
acc_dev_cmdSendFail=Échec de l'envoi des commandes, veuillez renvoyer.
acc_dev_issueVerifyParam=Définir options Vérification-Background
acc_dev_verifyParamSuccess=Options Vérification-Background appliquées avec succès
acc_dev_backgroundVerify=Vérification Background
acc_dev_selRightFile=Sélectionnez le fichier de mise à niveau correct!
acc_dev_devNotOpForOffLine=L'appareil est hors ligne, veuillez réessayer ultérieurement.
acc_dev_devNotSupportFunction=L'appareil ne supporte pas cette fonctionnalité
acc_dev_devNotOpForDisable=L'appareil est désactivé, veuillez réessayer ultérieurement.
acc_dev_devNotOpForNotOnline=L'appareil est hors ligne ou désactivé, veuillez réessayer ultérieurement
acc_dev_getPersonInfo=Obtenir les informations du personnel
acc_dev_getFPInfo=Obtenir les informations d'empreintes digitales
acc_dev_getFingerVeinInfo=Obtenir les informations de Veine de doigt
acc_dev_getPalmInfo=Obtenir les informations de Paume
acc_dev_getBiophotoInfo=Obtenir les informations de photo de visage
acc_dev_getIrisInfo=Obtenir des informations sur l'iris
acc_dev_disable=Désactivé, veuillez sélectionner à nouveau
acc_dev_offlineAndContinue=est hors ligne, continuer?
acc_dev_offlineAndSelect=est hors ligne.
acc_dev_opAllDev=Tous les appareils
acc_dev_opOnlineDev=Appareil en ligne
acc_dev_opException=Gérer les exceptions
acc_dev_exceptionAndConfirm=Délai de connexion du périphérique, l'opération a échoué. Vérifiez la connexion réseau
acc_dev_getFaceInfo=Obtenir les informations de visage
acc_dev_selOpDevType=Sélectionnez le type d'appareil à utiliser:
acc_dev_hasFilterByFunc=Afficher uniquement les appareils en ligne et prenant en charge cette fonction!
acc_dev_masterSlaveMode=Mode maître et esclave RS485
acc_dev_master=Hôte
acc_dev_slave=Esclave
acc_dev_modifyRS485Addr=Modifier adresse RS485
acc_dev_rs485AddrTip=Veuillez entrer un nombre entier compris entre 1 et 63!
acc_dev_enableFeature=Les appareils qui ont activé la vérification Background
acc_dev_disableFeature=Les appareils qui ont désactivé la vérification Background
acc_dev_getCountOnly=Ne comptez que
acc_dev_queryDevPersonCount=Comptage de personnes interrogées
acc_dev_queryDevVolume=Voir la capacité de l'appareil
acc_dev_ruleType=Type de règle
acc_dev_contenRule=Détails des règles
acc_dev_accessRules=Voir les règles de l'appareil
acc_dev_ruleContentTip=Entre les règles multiples avec un '|' séparé.
acc_dev_rs485AddrFigure=Figure de code d'adresse RS485
acc_dev_addLevel=Ajouter au niveau
acc_dev_personOrFingerTanto=Le nombre de personnel ou d'empreintes digitales dépasse les limites, la synchronisation a échoué...
acc_dev_personAndFingerUnit=(Nombre)
acc_dev_setDstime=Régler l'heure d'été
acc_dev_setTimeZone=Régler le fuseau horaire de l'appareil
acc_dev_selectedTZ=Fuseau horaire sélectionné
acc_dev_timeZoneSetting=Réglage du fuseau horaire...
acc_dev_timeZoneCmdSuccess=Commande du Fuseau horaire envoyée avec succès...
acc_dev_enableDstime=Activer l'heure d'été
acc_dev_disableDstime=Désactiver l'heure d'été
acc_dev_timeZone=Fuseau horaire
acc_dev_dstSettingTip=Réglage de l'heure d'été...
acc_dev_dstDelTip=Supprimer l'heure d'été du périphérique...
acc_dev_enablingDst=Activation de l'heure d'été
acc_dev_dstEnableCmdSuccess=Activation de la commande d'heure d'été envoyée avec succès.
acc_dev_disablingDst=Désactivation de l'heure d'été.
acc_dev_dstDisableCmdSuccess=Désactivation de la commande de l'heure d'été envoyée avec succès.
acc_dev_dstCmdSuccess=Commande d'heure d'été envoyée avec succès...
acc_dev_usadst=Heure d'été
acc_dev_notSetDst=Pas de réglages
acc_dev_selectedDst=Heure d'été sélectionnée
acc_dev_configMasterSlave=Configuration Maître-esclave
acc_dev_hasFilterByUnOnline=Afficher uniquement le périphérique en ligne.
acc_dev_softwareData=Si vous constatez que les données ne sont pas cohérentes avec l'appareil, veuillez les synchroniser avant de réessayer.
acc_dev_disabled=Les appareils désactivés ne peuvent pas être utilisés!
acc_dev_offline=Les appareils hors ligne ne peuvent pas être utilisés!
acc_dev_noSupport=Les appareils ne supportent pas cette fonction et ne peuvent pas être utilisés!
acc_dev_noRegDevTip=Ce périphérique n'est pas défini en tant que périphérique enregistré. Les données du logiciel ne seront pas mises à jour. Voulez-vous continuer?
acc_dev_noOption=Pas d'options qualifiées
acc_dev_devFWUpdatePrompt=L’appareil actuel n’utilisera normalement pas la fonction de personne valide et personne désactivée (veuillez vous référer au manuel d'utilisation).
acc_dev_panelFWUpdatePrompt=L’appareil actuel n’utilisera normalement pas la fonction de personne valide et personne désactivée, mettre à jour immédiatement le firmware?
acc_dev_sendEventCmdSuccess=Commande de suppression d'événement envoyée avec succès
acc_dev_tryAgain=Veuillez réessayer.
acc_dev_eventAutoCheckAndUpload=Vérification automatique et événements transférés
acc_dev_eventUploadStart=Commencer à transférer les événements.
acc_dev_eventUploadEnd=Transfert d'événements terminé.
acc_dev_eventUploadFailed=Transfert d'événements échoué.
acc_dev_eventUploadPrompt=La version du firmware de l'appareil est trop ancienne. Avant de mettre à niveau le firmware, vous souhaitez:
acc_dev_backupToSoftware=Sauvegarder les données sur le logiciel
acc_dev_deleteEvent=Supprimer l'ancien enregistrement
acc_dev_upgradePrompt=La version du firmware est trop ancienne et pourrait causer une erreur. Veuillez sauvegarder les données dans le logiciel avant de mettre à jour le firmware..
acc_dev_conflictCardNo=Carte présente dans le système à {0} chez l'autre personne!
acc_dev_rebootAfterOperate=L'opération a réussi, l'appareil redémarrera plus tard.
acc_dev_baseOptionTip=Anomalie du paramètre de base obtenu.
acc_dev_funOptionTip=Anomalie du paramètre de fonction obtenu.
acc_dev_sendComandoTip=Envoi de la commande d'obtention du paramètre de périphérique échoué.
acc_dev_noC3LicenseTip=Ne peut pas ajouter ce type d'appareil ({0})! Veuillez contacter notre département de ventes.
acc_dev_combOpenDoorTip=({0})Multi-personne ouvre la porte a été défini, Ne peut être utilisé au même moment avec la Vérification-Background.
acc_dev_combOpenDoorPersonCountTip=Le groupe {0} ne peut pas ouvrir plus de {1}!
acc_dev_addDevTip=Ceci s'applique uniquement à l'ajout de périphérique avec protocole de communication PULL!
acc_dev_addError=Exception d'ajout de périphérique, paramètres manquants ({0})!
acc_dev_updateIPAndPortError=Mettre à jour le serveur IP et l'erreur de port...
acc_dev_transferFilesTip=Test du firmware terminé, transfert de fichiers
acc_dev_serialPortExist=Le port de série existe
acc_dev_isExist=Avoir l'appareil
acc_dev_description=Description
acc_dev_searchEthernet=Rechercher le périphérique Ethernet
acc_dev_searchRS485=Rechercher le périphérique RS485
acc_dev_rs485AddrTip1=L'adresse de début RS485 ne peut pas être supérieure à l'adresse de fin
acc_dev_rs485AddrTip2=Le champ de recherche de RS485 doit être inférieur à 20
acc_dev_clearAllCmdCache=Effacer toutes les commandes
acc_dev_authorizedSuccessful=Autorisé avec succès
acc_dev_authorize=Autoriser
acc_dev_registrationDevice=Enregistrer périphérique
acc_dev_setRegistrationDevice=Définir comme périphérique d'enregistrement
acc_dev_mismatchedDevice=Cet appareil ne peut pas être utilisé pour votre marché. S'il vous plaît vérifier le numéro de série de l'appareil avec notre équipe de ventes.
acc_dev_pwdStartWithZero=Le mot de passe de communication ne peut pas commencer par zéro!
acc_dev_maybeDisabled=La licence actuelle vous permet uniquement d’ajouter {0} autre porte(s). La porte du nouveau périphérique ajouté qui dépasse la limite de la porte de la licence sera désactivée, souhaitez vous continuer à utiliser?
acc_dev_Limit=La clé de licence du périphérique a atteint la limite supérieure d'ajout, veuillez autoriser.
acc_dev_selectDev=Veuillez sélectionner le périphérique!
acc_dev_cannotAddPullDevice=Impossible d'ajouter le périphérique PULL! Veuillez contacter notre service de ventes.
acc_dev_notContinueAddPullDevice=Il y a {0} périphériques PULL dans le système, ne peut pas continuer d'ajouter! Veuillez contacter notre service de ventes.
acc_dev_deviceNameNull=Le modèle de périphérique est vide. ne peut pas ajouter de périphérique!
acc_dev_commTypeErr=Le type de communication ne correspond pas; ne peut pas ajouter de périphérique!
acc_dev_inputDomainError=Veuillez entrer une adresse de domaine valide.
acc_dev_levelTip=Il y a plus de 5000 personnes dans le niveau, impossible d'ajouter l'appareil au niveau d'ici. 
acc_dev_auxinSet=Réglage de l'entrée auxiliaire
acc_dev_verifyModeRule=Règle du mode de vérification
acc_dev_netModeWired=Câblé
acc_dev_netMode4G=4G
acc_dev_netModeWifi=WIFI
acc_dev_updateNetConnectMode=Changer de connexion réseau
acc_dev_wirelessSSID=SSID sans câble
acc_dev_wirelessKey=Clé sans câble
acc_dev_searchWifi=Rechercher WIFI
acc_dev_testNetConnectSuccess=La connexion est-elle réussie?
acc_dev_testNetConnectFailed=La connexion ne peut pas communiquer correctement!
acc_dev_signalIntensity=Force du signal
acc_dev_resetSearch=Chercher à nouveau
acc_dev_addChildDevice=Ajouter sous-périphérique
acc_dev_modParentDevice=Changer le périphérique maître
acc_dev_configParentDevice=Configuration du périphérique maître
acc_dev_lookUpChildDevice=Voir les périphériques enfants
acc_dev_addChildDeviceTip=L'autorisation doit être effectuée sous un sous-appareil autorisé.
acc_dev_maxSubCount=Le nombre de sous-appareils autorisés dépasse le nombre maximal de %d unité.
acc_dev_seletParentDevice=Veuillez sélectionner le périphérique maître!
acc_dev_networkCard=Carte réseau
acc_dev_issueParam=Paramètres de synchronisation personnalisés
acc_dev_issueMode=Mode de synchronisation
acc_dev_initIssue=Données d'initialisation de la version 3030 du Firmware
acc_dev_customIssue=Données de la synchronisation personnalisée
acc_dev_issueData=Données
acc_dev_parent=Périphérique maître
acc_dev_parentEnable=Le périphérique maître est désactivé
acc_dev_parentTips=Relier le périphérique maître supprimera toutes les données dans le périphérique et aura besoin d'être configuré à nouveau.
acc_dev_addDevIpTip=La nouvelle adresse IP ne peut pas être identique à l'adresse IP du serveur.
acc_dev_modifyDevIpTip=La nouvelle adresse du serveur ne peut pas être identique à l'adresse IP du périphérique.
acc_dev_setWGReader=Configurer le lecteur Wiegand
acc_dev_selectReader=Cliquez pour sélectionner le lecteur
acc_dev_IllegalDevice=Périphérique illégal
acc_dev_syncTimeWarnTip=Les temps de synchronisation des périphériques suivants doivent être synchronisés sur le périphérique maître.
acc_dev_setTimeZoneWarnTip=Le fuseau horaire du périphérique suivant doit être synchronisé sur le périphérique maître.
acc_dev_setDstimeWarnTip=L’heure d’été pour les appareils suivants doit être synchronisée sur l’appareil maître.
acc_dev_networkSegmentSame=Deux adaptateurs réseau ne peuvent pas utiliser le même segment de réseau.
acc_dev_upgradeProtocolNoMatch=Le protocole de fichier de mise à niveau ne correspond pas
acc_dev_ipAddressConflict=Un périphérique avec la même adresse IP existe déjà. Veuillez modifier l'adresse IP du périphérique et l'ajouter à nouveau.
acc_dev_checkServerPortTip=Le port du serveur configuré est incompatible avec le port de communication du système, ce qui peut entraîner l’échec de l’ajout. Continuer à opérer?
acc_dev_clearAdmin=Effacer la permission d'administrateur
acc_dev_setDevSate=Définir le statut entrée/sortie du périphérique
acc_dev_sureToClear=Êtes-vous sûr de supprimer les permissions administrateur du périphérique?
acc_dev_hostState=Statut du périphérique Maître
acc_dev_regDeviceTypeTip=Ce périphérique est limité et ne peut être ajouté directement. Veuillez contacter le fournisseur du logiciel!
acc_dev_extBoardType=Type de tableau E/S
acc_dev_extBoardTip=Après la configuration, vous devez redémarrer l'appareil pour prendre effet.
acc_dev_extBoardLimit=Seul {0} le tableau E/S de ce type peut être ajouté à chaque périphérique!
acc_dev_replace=Remplacer l'appareil
acc_dev_replaceTip=Après le remplacement, l'ancien appareil ne fonctionnera plus, soyez prudent!
acc_dev_replaceTip1=Après le remplacement, veuillez effectuer l'opération "synchroniser toutes les données";
acc_dev_replaceTip2=Veuillez vous assurer que le modèle d'appareil de remplacement est le même!
acc_dev_replaceTip3=Veuillez vous assurer que l'appareil de remplacement a défini la même adresse de serveur et le même port que l'ancien appareil !
acc_dev_replaceFail=Le type de machine du périphérique est incohérent et ne peut pas être remplacé !
acc_dev_notApb=Cet appareil ne peut pas faire de porte ou de tête de lecture anti - sous - marine
acc_dev_upResourceFile=Télécharger un fichier de ressources
acc_dev_playOrder=Ordre de jeu
acc_dev_setFaceServerInfo=Définir les paramètres de comparaison de l'arrière du visage
acc_dev_faceVerifyMode=Mode de comparaison des visages
acc_dev_faceVerifyMode1=Comparaison locale
acc_dev_faceVerifyMode2=Comparaison back - end
acc_dev_faceVerifyMode3=Priorité de comparaison locale
acc_dev_faceBgServerType=Types de serveurs back - end faciaux
acc_dev_faceBgServerType1=Services de plateforme logicielle
acc_dev_faceBgServerType2=Services de tiers
acc_dev_isAccessLogic=Activer la vérification logique du contrôle d'accès
#[5]门-其他关联的也复用此处
acc_door_entity=Porte
acc_door_number=Numéro de porte
acc_door_name=Nom de porte
acc_door_activeTimeZone=Fuseau horaire actif
acc_door_passageModeTimeZone=Fuseau horaire du mode Passage
acc_door_setPassageModeTimeZone=Réglage du fuseau horaire du mode Passage
acc_door_notPassageModeTimeZone=Fuseau horaire en mode non-passage
acc_door_lockOpenDuration=Durée du verrouillage et de l'ouverture
acc_door_entranceApbDuration=Durée de l'entrée de l'Anti-Passback
acc_door_sensor=Capteur de porte
acc_door_sensorType=Type de capteur de porte
acc_door_normalOpen=Ouvrir Normallement
acc_door_normalClose=Fermer Normallement
acc_door_sensorDelay=Délai de capteur de porte
acc_door_closeAndReverseState=Etat de verrouillage inversé à la fermeture de la porte
acc_door_hostOutState=Statut d'accès de l'hôte
acc_door_slaveOutState=Esclave hors d'état
acc_door_inState=Entrée
acc_door_outState=Sortie
acc_door_requestToExit=Mode Demande de sortie
acc_door_withoutUnlock=Verrouiller
acc_door_unlocking=Déverrouiller
acc_door_alarmDelay=Délai Demande de sortie
acc_door_duressPassword=Mot de passe de contrainte
acc_door_currentDoor=Porte actuelle
acc_door_allDoorOfCurDev=Toutes les portes du périphérique actuel
acc_door_allDoorOfAllDev=Toutes les portes de tous les périphériques
acc_door_allDoorOfAllControlDev=Toutes les portes de tous les périphériques de contrôle
acc_door_allDoorOfAllStandaloneDev=Toutes les portes de tous les périphériques autonomes
acc_door_allWirelessLock=Toutes les serrures sans câble
acc_door_max6BitInteger=Maximum de 6 bits entier
acc_door_direction=Direction
acc_door_onlyInReader=Lecteur d'entrée seulement
acc_door_bothInAndOutReader=Lecteurs d'Entrée & Sortie
acc_door_noDoor=Veuillez ajouter la porte.
acc_door_nameRepeat=Les noms de porte sont dupliqués.
acc_door_duressPwdError=Le mot de passe sous contrainte ne doit pas être identique à un mot de passe personnel.
acc_door_urgencyStatePwd=Veuillez entrer {0} bit entier!
acc_door_noDevOnline=Aucun périphérique n'est en ligne ou la porte ne prend pas en charge le mode de vérification de la carte.
acc_door_durationLessLock=Le délai du capteur de porte doit être supérieur à la durée d'ouverture du verrou.
acc_door_lockMoreDuration=La durée d'ouverture du verrou doit être inférieure au délai du capteur de porte.
acc_door_lockAndExtLessDuration=La somme de la durée d'ouverture du verrou et du délai de passage doit être inférieure au délai du capteur de porte.
acc_door_noDevTrigger=Le périphérique ne répond pas aux conditions!
acc_door_relay=Relais
acc_door_pin=Pin
acc_door_selDoor=Sélectionner porte
acc_door_sensorStatus=Capteur de porte ({0})
acc_door_sensorDelaySeconds=Délai du capteur de porte ({0} s)
acc_door_timeSeg=Fuseau horaire ({0})
acc_door_combOpenInterval=Intervalle d'opération Multi-personnes
acc_door_delayOpenTime=Délai d'ouverture de porte
acc_door_extDelayDrivertime=Délai de passage
acc_door_enableAudio=Activer alarme
acc_door_disableAudio=Désactiver alarme
acc_door_lockAndExtDelayTip=La somme de la durée d'ouverture du verrou et de délai d'intervalle de temps de passage n'est pas supérieure à 254 secondes.
acc_door_disabled=Les portes désactivées ne peuvent pas être utilisées!
acc_door_offline=Les portes hors ligne ne peuvent pas être utilisées!
acc_door_notSupport=Les portes suivantes ne supportent pas cette fonctionnalité!
acc_door_select=Sélectionner la porte
acc_door_pushMaxCount=Il y a {0} portes activées dans le système et la limite de la licence est atteinte! Veuillez contacter notre département de ventes.
acc_door_outNumber=La licence actuelle vous permet uniquement d’ajouter {0} autres porte(s). Veuillez sélectionner à nouveau les portes et réessayer ou contacter notre service commercial pour obtenir une mise à jour de la licence.
acc_door_latchTimeZone=Fuseau horaire Demande de sortie
acc_door_wgFmtReverse=Inversion du numéro de carte
acc_door_allowSUAccessLock=Autoriser l'accès superutilisateur lors du verrouillage
acc_door_verifyModeSinglePwd=Les mots de passe ne peuvent pas être utilisés comme moyen de vérification indépendant!
acc_door_doorPassword=Mot de passe pour ouvrir la porte
#辅助输入
acc_auxIn_timeZone=Fuseau horaire actif
#辅助输出
acc_auxOut_passageModeTimeZone=Fuseau horaire du Mode passage
acc_auxOut_disabled=La sortie auxiliaire désactivée ne peut pas être utilisée!
acc_auxOut_offline=La sortie auxiliaire hors ligne ne peut pas être utilisée!
#[8]门禁权限组
acc_level_doorGroup=Combinaison de porte
acc_level_openingPersonnel=Personnel ouvrant
acc_level_noDoor=Il n'y a pas d'article disponible. Veuillez ajouter d'abord le périphérique.
acc_level_doorRequired=La porte doit être sélectionnée.
acc_level_doorCount=Comptage de porte
acc_level_doorDelete=Supprimer porte
acc_level_isAddDoor=Ajouter immédiatement des portes au niveau de contrôle d'accès actuel?
acc_level_master=Maître
acc_level_noneSelect=Veuillez ajouter un niveau de contrôle d'accès.
acc_level_useDefaultLevel=Basculer le niveau d'accès vers le nouveau département?
acc_level_persAccSet=Paramètres de contrôle d'accès du personnel
acc_level_visUsed={0} est déjà utilisé par le module visiteur et ne peut pas être supprimé!
acc_level_doorControl=Contrôle de porte
acc_level_personExceedMax=Nombre actuel de groupes de permission ({0}), nombre maximum de groupes de permission optionnels ({1})
acc_level_exportLevel=Exporter le niveau d'accès
acc_level_exportLevelDoor=Exporter les portes du niveau d'accès
acc_level_exportLevelPerson=Exporter le personnel du niveau d'accès
acc_level_importLevel=Importer le niveau d'accès
acc_level_importLevelDoor=Importer les portes du niveau d'accès
acc_level_importLevelPerson=Importer le personnel du niveau d'accès
acc_level_exportDoorFileName=Informations sur les portes du niveau d'accès
acc_level_exportPersonFileName=Informations personnelles du niveau d'accès
acc_levelImport_nameNotNull=Le nom du niveau d'accès ne peut pas être vide
acc_levelImport_timeSegNameNotNull=Le fuseau horaire ne peut pas être vide
acc_levelImport_areaNotExist=La zone n'existe pas!
acc_levelImport_timeSegNotExist=Le fuseau horaire n'existe pas!
acc_levelImport_nameExist=Le nom de niveau d'accès {0} existe déjà!
acc_levelImport_levelDoorExist=Les portes du niveau d'accès {0} existent déjà!
acc_levelImport_levelPersonExist=Le personnel de niveau d'accès {0} existe déjà!
acc_levelImport_noSpecialChar=Le nom du niveau d'accès ne peut pas contenir de caractères spéciaux!
#[10]首人常开
acc_firstOpen_setting=Première personne ouvre normalement
acc_firstOpen_browsePerson=Parcourir le Personnel
#[11]多人组合开门
acc_combOpen_comboName=Nom de la combinaison
acc_combOpen_personGroupName=Nom de groupe
acc_combOpen_personGroup=Groupe Multi-Personne
acc_combOpen_verifyOneTime=Comptage du personnel actuel
acc_combOpen_eachGroupCount=Nombre de personnel ouvrant dans chaque groupe
acc_combOpen_group=Groupe
acc_combOpen_changeLevel=Groupe d'ouverture de portes
acc_combOpen_combDeleteGroup=Référence de combinaison d'ouverture existante, veuillez d'abord supprimer.
acc_combOpen_ownedLevel=Groupes d'appartenance
acc_combOpen_mostPersonCount=Le dénombrement de groupe ne doit pas dépasser cinq personnes.
acc_combOpen_leastPersonCount=Le dénombrement de groupe ne doit pas être moins de deux personnes.
acc_combOpen_groupNameRepeat=Nom de groupe dupliqué.
acc_combOpen_groupNotUnique=Les groupes de personnes ouvrant la porte ne doivent pas être identiques.
acc_combOpen_persNumErr=Le nombre de groupe dépasse la valeur réelle de votre choix, veuillez resélectionner!
acc_combOpen_combOpengGroupPersonShort=Une fois les personnes supprimées, le nombre de groupes d'ouverture de portes pour personnel n'est pas suffisant. Veuillez d'abord supprimer le groupe ouvert.
acc_combOpen_backgroundVerifyTip=La porte est dans un périphérique avec la vérification-background activée et ne peut être utilisée au même moment avec l'ouverture de porte multi-personne!
#[12]互锁
acc_interlock_rule=Règle d'interverrouillage
acc_interlock_mode1Or2=Interverrouillage entre {0} et {1}
acc_interlock_mode3=Interverrouillage entre {0} et {1} et {2}
acc_interlock_mode4=Interverrouillage entre {0} et {1} ou entre {2} et {3}
acc_interlock_mode5=Interverrouillage entre {0} et {1} et {2} et {3}
acc_interlock_hasBeenSet=A des paramètres de programme d'interverrouillage
acc_interlock_group1=Groupe 1
acc_interlock_group2=Groupe 2
acc_interlock_ruleInfo=Interlock entre groupes
acc_interlock_alreadyExists=Les mêmes règles d'interverrouillage existent déjà, ne répétez pas l'ajout!
acc_interlock_groupInterlockCountErr=Au moins deux portes sont requises pour les règles d'interverrouillage au sein du Groupe
acc_interlock_ruleType=Types de règles d'interverrouillage
#[13]反潜
acc_apb_rules=Règle Anti-Passback
acc_apb_reader=Anti-Passback entre les lecteurs de portes {0}
acc_apb_reader2=Anti-Passback entre les lecteurs de portes: {0}, {1}
acc_apb_reader3=Anti-Passback entre les lecteurs de portes: {0}, {1}, {2}
acc_apb_reader4=Anti-Passback entre les lecteurs de portes: {0}, {1}, {2}, {3}
acc_apb_reader5=Anti-Passback hors-lecteur sur la porte {0}
acc_apb_reader6=Anti-Passback en-lecteur sur la porte {0}
acc_apb_reader7=Anti-Passback entre les lecteurs de toutes les 4 portes
acc_apb_twoDoor=Anti-Passback entre {0} et {1}
acc_apb_fourDoor=Anti-Passback entre {0} et {1} , Anti-Passback entre {2} et {3}
acc_apb_fourDoor2=Anti-Passback entre {0} ou {1} et {2} ou {3}
acc_apb_fourDoor3=Anti-Passback entre {0} et {1} ou {2}
acc_apb_fourDoor4=Anti-Passback entre {0} et {1} ou {2} ou {3}
acc_apb_hasBeenSet=A été défini pour Anti-Passback
acc_apb_conflictWithGapb=Ce périphérique possède des paramètres Anti-Passback globaux et ne peut pas être modifié!
acc_apb_conflictWithApb=La zone du périphérique a des paramètres Anti-Passback et ne peut être modifiée!
acc_apb_conflictWithEntranceApb=La zone de l'appareil possède des paramètres Anti-Passback d'entrée et ne peut être modifiée!
acc_apb_controlIn=Anti-Passback Entrée
acc_apb_controlOut=Anti-Passback Sortie
acc_apb_controlInOut=Anti-Passback Entrée/Sortie
acc_apb_groupIn=Entrer Dans Un Groupe
acc_apb_groupOut=Sortir Du Groupe
acc_apb_reverseName=Anti - sous - marine inverse
acc_apb_door=Porte anti - sous - marine
acc_apb_readerHead=Lire la tête anti - sous - marine
acc_apb_alreadyExists=Les mêmes règles anti - sous - marines existent déjà, ne répétez pas l'ajout!
#[17]电子地图
acc_map_addDoor=Ajouter Porte
acc_map_addChannel=Ajouter Caméra
acc_map_noAccess=Aucune autorisation d'accès au module de carte électronique, veuillez contacter l'administrateur!
acc_map_noAreaAccess=Vous ne disposez pas de l'autorisation E-map pour cette zone, veuillez contacter l'administrateur!
acc_map_imgSizeError=Veuillez télécharger l'image dont la taille est inférieure à {0}M!
#[18]门禁事件记录
acc_trans_entity=Transaction
acc_trans_eventType=Type d'évènement
acc_trans_firmwareEvent=Événements Firmware
acc_trans_softwareEvent=Événement logiciel
acc_trans_today=Événements à partir d'aujourd'hui
acc_trans_lastAddr=Dernière position connue
acc_trans_viewPhotos=Voir les photos
acc_trans_exportPhoto=Exporter des photos
acc_trans_dayNumber=Journées
acc_trans_photo=Photo d'incident de contrôle d'accès
acc_trans_fileIsTooLarge=Le fichier exporté est trop volumineux, veuillez réduire la portée de l'exportation
#[19]门禁验证方式
acc_verify_mode_onlyface=Visage
acc_verify_mode_facefp=Visage+Empreinte Digitale
acc_verify_mode_facepwd=Visage+Mot de passe
acc_verify_mode_facecard=Visage+Carte
acc_verify_mode_facefpcard=Visage+Empreinte Digitale+Carte
acc_verify_mode_facefppwd=Visage+Empreinte Digitale+Mot de passe
acc_verify_mode_fv=Veine de Doigt
acc_verify_mode_fvpwd=Veine de Doigt+Mot de passe
acc_verify_mode_fvcard=Veine de doigt+Carte
acc_verify_mode_fvpwdcard=Veine de doigt+Mot de passe+Carte
acc_verify_mode_pv=Paume
acc_verify_mode_pvcard=Paume+Carte
acc_verify_mode_pvface=Paume+Visage
acc_verify_mode_pvfp=Paume+Empreinte digitale
acc_verify_mode_pvfacefp=Paume+Visage+Empreinte Digitale
#[20]门禁事件编号
acc_eventNo_-1=Aucun
acc_eventNo_0=Glissez normalement pour ouvrir
acc_eventNo_1=Glissez pendant la zone de temps en mode passage
acc_eventNo_2=Première-Carte ouverture normale (Glissez Carte)
acc_eventNo_3=Ouverture Multi-Personne (Glissez Carte)
acc_eventNo_4=Ouverture Mot de passe d'urgence
acc_eventNo_5=Ouverture pendant la zone de temps en mode passage
acc_eventNo_6=Événement de liaison déclenché
acc_eventNo_7=Annuler Alarme
acc_eventNo_8=Ouverture à distance
acc_eventNo_9=Fermeture à distance
acc_eventNo_10=Désactiver la zone de temps en mode passage en journée
acc_eventNo_11=Activer la zone de temps en mode passage en journée
acc_eventNo_12=Sortie auxiliaire Ouverture à distance
acc_eventNo_13=Sortie auxiliaire Fermeture à distance
acc_eventNo_14=Ouverture normale Empreinte Digitale
acc_eventNo_15=Ouverture Multi-Personne (Empreinte Digitale)
acc_eventNo_16=Appuyez l'empreinte digitale pendant la zone de temps en mode passage
acc_eventNo_17=La carte plus l'empreinte digitale ouvre
acc_eventNo_18=Première-Carte ouvre normallement (Appuyer empreinte digitale)
acc_eventNo_19=Première-Carte ouvre normallement (Carte plus Empreinte digitale)
acc_eventNo_20=Intervalle d'opération trop court
acc_eventNo_21=Zone de temps de la porte inactive(Glissez Carte)
acc_eventNo_22=Zone de temps illégal
acc_eventNo_23=Accès refusé
acc_eventNo_24=Anti-Passback
acc_eventNo_25=Interverrouillage
acc_eventNo_26=Authentification multi-personne(Glissez Card)
acc_eventNo_27=Carte désactivée
acc_eventNo_28=Délai d'attente prolongé pour porte ouverte
acc_eventNo_29=Carte expirée
acc_eventNo_30=Erreur de mot de passe
acc_eventNo_31=Intervalle d'appui empreinte digitale trop court
acc_eventNo_32=Authentification Multi-Personne(Appuyez empreinte digitale)
acc_eventNo_33=Empreinte digitale expirée
acc_eventNo_34=Empreinte digitale désactivée
acc_eventNo_35=Zone de temps de la porte inactive(Appuyez Empreinte digitale)
acc_eventNo_36=Zone de temps de la porte inactive(Appuyez Bouton de sortie)
acc_eventNo_37=Echec de la fermeture pendant la zone de temps en mode passage
acc_eventNo_38=Carte signalée perdue
acc_eventNo_39=L'accès est désactivé
acc_eventNo_40=Échec de l'authentification multi-personne(Appuyer Empreinte digitale)
acc_eventNo_41=Erreur de mode de vérification
acc_eventNo_42=Erreur de format Wiegand
acc_eventNo_43=Expiration du délai de vérification Anti-Passback
acc_eventNo_44=Échec de la Vérification-Background
acc_eventNo_45=Expiration de la Vérification-Background
acc_eventNo_47=Échec de l'envoi de la commande
acc_eventNo_48=Échec de l'authentification multi-personne(Glissez Carte)
acc_eventNo_49=Zone de temps de la porte inactive(Mot de passe)
acc_eventNo_50=Intervalle d'entrée de mot de passe trop court
acc_eventNo_51=Authentification multi-personne(Mot de passe)
acc_eventNo_52=Authentification multi-personne échouée(Mot de passe)
acc_eventNo_53=Mot de passe expiré
acc_eventNo_100=Alarme anti-sabotage
acc_eventNo_101=Mot de passe de contrainte pour ouvrir
acc_eventNo_102=Porte ouverte avec force
acc_eventNo_103=Empreinte digitale de contrainte pour ouvrir
acc_eventNo_200=Porte ouverte correctement
acc_eventNo_201=Porte fermée correctement
acc_eventNo_202=Bouton de sortie ouvre
acc_eventNo_203=Multi-Personne Ouvre(Carte plus Empreinte digitale)
acc_eventNo_204=Zone de temps en mode passage terminée
acc_eventNo_205=Ouverture normale à distance
acc_eventNo_206=Périphérique démarré
acc_eventNo_207=Mot de passe ouvert
acc_eventNo_208=Super utilisateur ouvre les portes
acc_eventNo_209=Bouton de sortie déclenché(Sans déverrouillage)
acc_eventNo_210=Démarrer la porte coupe-feu
acc_eventNo_211=Super utilisateur ferme les portes.
acc_eventNo_212=Activer la fonction de contrôle de l'ascenseur
acc_eventNo_213=Désactiver la fonction de contrôle de l'ascenseur
acc_eventNo_214=Multi-Personne Ouvre(Mot de passe)
acc_eventNo_215=Première-Carte ouvre normalement(Mot de passe)
acc_eventNo_216=Mot de passe pendant la zone de temps en mode Passage
acc_eventNo_220=Entrée auxiliaire déconnectée (Ouvert)
acc_eventNo_221=Entrée auxiliaire court-circuitée (Fermé)
acc_eventNo_222=Vérification-Background Réussie
acc_eventNo_223=Vérification-Background
acc_eventNo_225=Entrée auxiliaire normale
acc_eventNo_226=Entrée auxiliaire déclenchée
acc_newEventNo_0=Ouverture Vérification normale
acc_newEventNo_1=Vérifier pendant la zone de temps en mode passage
acc_newEventNo_2=Premier-Personnel Ouvre
acc_newEventNo_3=Multi-Personnel Ouvre
acc_newEventNo_20=Intervalle d'opération trop court
acc_newEventNo_21=Vérification de la zone de temps d'ouverture de la porte inactive
acc_newEventNo_26=Attente d'authentification multi-personnel
acc_newEventNo_27=Personnel non enregistré
acc_newEventNo_29=Personnel expiré
acc_newEventNo_30=Erreur de mot de passe
acc_newEventNo_41=Erreur de mode de vérification
acc_newEventNo_43=Verrouillage du bâton
acc_newEventNo_44=Échec de la vérification Background
acc_newEventNo_45=Expiration de la Vérification-Background
acc_newEventNo_48=Échec de la vérification multi-personnel
acc_newEventNo_54=Le voltage de la batterie est trop faible
acc_newEventNo_55=Remplacez immédiatement la batterie
acc_newEventNo_56=Opération illégale
acc_newEventNo_57=Alimentation de secours
acc_newEventNo_58=Alarme d'ouverture normale
acc_newEventNo_59=Gestion illégale
acc_newEventNo_60=Porte verrouillée à l'intérieur
acc_newEventNo_61=Répliqué
acc_newEventNo_62=Utilisateurs prohibés
acc_newEventNo_63=Porte verrouillée
acc_newEventNo_64=Zone de temps de bouton de sortie inactive
acc_newEventNo_65=Zone de temps d'entrée auxiliaire inactive
acc_newEventNo_66=Échec de la mise à niveau du lecteur
acc_newEventNo_67=Appariement à distance réussi (appareil non autorisé)
acc_newEventNo_68=Température corporelle élevée - Accès refusé
acc_newEventNo_69=Sans masque - Accès refusé
acc_newEventNo_70=Le rapport de visage communique anormalement avec le serveur.
acc_newEventNo_71=Le serveur de visage a répondu anormalement.
acc_newEventNo_73=Code QR non valide
acc_newEventNo_74=Code QR expiré
acc_newEventNo_101=Alarme d'ouverture sous contrainte
acc_newEventNo_104=Alarme de glissement de carte non valide
acc_newEventNo_105=Ne peut pas se connecter au serveur
acc_newEventNo_106=Alimentation principale déchargée
acc_newEventNo_107=Batterie déchargée
acc_newEventNo_108=Ne peut pas se connecter au péripérique maître
acc_newEventNo_109=Alarme de sabotage du lecteur
acc_newEventNo_110=Lecteur hors ligne
acc_newEventNo_112=Carte d'extension hors ligne
acc_newEventNo_114=Entrée d'alarme incendie déconnectée (détection de ligne)
acc_newEventNo_115=Court - circuit d'entrée d'alarme incendie (détection de ligne)
acc_newEventNo_116=Entrée auxiliaire déconnectée (détection de ligne)
acc_newEventNo_117=Court - circuit d'entrée auxiliaire (détection de ligne)
acc_newEventNo_118=Interrupteur de sortie ouvert (détection de ligne)
acc_newEventNo_119=Court - circuit de l'interrupteur de sortie (détection de ligne)
acc_newEventNo_120=Ouverture magnétique de la porte (détection de ligne)
acc_newEventNo_121=Court - circuit magnétique de porte (détection de ligne)
acc_newEventNo_159=La télécommande pour ouvrir la porte
acc_newEventNo_214=Connecté au serveur
acc_newEventNo_217=Connecté au périphérique maître avec succès
acc_newEventNo_218=Vérification de la carte d'identité
acc_newEventNo_222=Vérification Background Réussie
acc_newEventNo_223=Vérification Background
acc_newEventNo_224=Sonner la cloche
acc_newEventNo_227=Ouvrir la porte en position tirer comme pousser
acc_newEventNo_228=Fermer la porte en position tirer comme pousser
acc_newEventNo_229=Chronométrage de la Sortie auxiliaire normallement ouvert
acc_newEventNo_230=Chronométrage de la Sortie auxiliaire terminé
acc_newEventNo_232=Vérification Réussie
acc_newEventNo_233=Activer le verrouillage
acc_newEventNo_234=Désactiver le verrouillage
acc_newEventNo_235=Succès de la mise à niveau du lecteur
acc_newEventNo_236=Alarme de sabotage du lecteur effacée
acc_newEventNo_237=Lecteur en ligne
acc_newEventNo_239=Appel de périphérique
acc_newEventNo_240=Appel terminé
acc_newEventNo_243=Alarme incendie entrée déconnectée
acc_newEventNo_244=Court - circuit d'entrée d'alarme incendie
acc_newEventNo_247=Extended Board en ligne
acc_newEventNo_4008=Récupération secteur
acc_newEventNo_4014=Signal d'entrée d'incendie déconnecté, porte de fin normalement ouverte
acc_newEventNo_4015=La porte est en ligne
acc_newEventNo_4018=Comparaison de backend Ouvrir
acc_newEventNo_5023=Statut incendie limité
acc_newEventNo_5024=Délai de validation Multi - personnes
acc_newEventNo_5029=Comparaison du backend échouée
acc_newEventNo_6005=La capacité d'enregistrement atteint la limite supérieure
acc_newEventNo_6006=Court-circuit de ligne (RS485)
acc_newEventNo_6007=Court-circuit de ligne (wegen)
acc_newEventNo_6011=La porte est hors ligne
acc_newEventNo_6012=Alarme de démontage de porte
acc_newEventNo_6013=Déclenchement du signal d'entrée d'incendie, ouverture normale de la porte
acc_newEventNo_6015=Réinitialiser l'alimentation de l'équipement d'extension
acc_newEventNo_6016=Paramètres d'usine de la machine de récupération
acc_newEventNo_6070=Comparaison de backend (liste interdite)
acc_eventNo_undefined=Numéro d'événement non défini
acc_advanceEvent_500=Anti-Passback global(logique)
acc_advanceEvent_501=Disponibilité de personne (utiliser la date)
acc_advanceEvent_502=Numéro de personne de contrôle
acc_advanceEvent_503=Interverrouillage Global
acc_advanceEvent_504=Contrôle d'itinéraire
acc_advanceEvent_505=Anti-Passback Global(chronométré)
acc_advanceEvent_506=Anti-Passback global(chronométrage logique)
acc_advanceEvent_507=Disponibilité de personne (après la première utilisation de jours valides)
acc_advanceEvent_508=Disponibilité de personne (utiliser le nombre de fois)
acc_advanceEvent_509=Vérification Background échouée (personnel non enregistré)
acc_advanceEvent_510=Vérification Background échouée (exception de données)
acc_alarmEvent_701=Violation DMR(règles de réglage:{0})
#[21]实时监控
acc_rtMonitor_openDoor=Ouvrir
acc_rtMonitor_closeDoor=Fermer
acc_rtMonitor_remoteNormalOpen=Ouvrir normallement à distance
acc_rtMonitor_realTimeEvent=Événements en temps réel
acc_rtMonitor_photoMonitor=Contrôle de photo
acc_rtMonitor_alarmMonitor=Contrôle d'alarme
acc_rtMonitor_doorState=État de la porte
acc_rtMonitor_auxOutName=Nom de Sortie auxiliaire
acc_rtMonitor_nonsupport=Non supporté
acc_rtMonitor_lock=Verrouiller
acc_rtMonitor_unLock=Déverrouiller
acc_rtMonitor_disable=Désactivé
acc_rtMonitor_noSensor=Pas de capteur de porte
acc_rtMonitor_alarm=Alarme
acc_rtMonitor_openForce=Ouvert avec force
acc_rtMonitor_tamper=Altérer
acc_rtMonitor_duressPwdOpen=Ouverture avec mot de passe de contrainte
acc_rtMonitor_duressFingerOpen=Ouverture avec Empreinte digitale de contrainte
acc_rtMonitor_duressOpen=Ouverture sous contrainte
acc_rtMonitor_openTimeout=Expiration du délai d'ouverture
acc_rtMonitor_unknown=Inconnu
acc_rtMonitor_noLegalDoor=Aucune porte ne remplit la condition.
acc_rtMonitor_noLegalAuxOut=Aucune sortie auxiliaire ne remplit la condition!
acc_rtMonitor_curDevNotSupportOp=L'état actuel de l'appareil ne prend pas en charge cette opération!
acc_rtMonitor_curNormalOpen=Actuellement ouverture normale
acc_rtMonitor_whetherDisableTimeZone=L'état actuel de la porte est toujours ouvert.
acc_rtMonitor_curSystemNoDoors=Le système actuel n'a ajouté aucune porte ou ne trouve aucune porte qui répond à vos besoins.
acc_rtMonitor_cancelAlarm=Annuler alarme
acc_rtMonitor_openAllDoor=Ouvrir toutes les portes actuelles
acc_rtMonitor_closeAllDoor=Fermer toutes les portes actuelles
acc_rtMonitor_confirmCancelAlarm=Voulez-vous vraiment annuler cette alarme?
acc_rtMonitor_calcelAllDoor=Annuler toutes les alarmes
acc_rtMonitor_initDoorStateTip=Obtention de toutes les portes autorisées aux utilisateurs du système...
acc_rtMonitor_alarmEvent=Événement d'alarme
acc_rtMonitor_ackAlarm=Reconnaître
acc_rtMonitor_ackAllAlarm=Tout reconnaître
acc_rtMonitor_ackAlarmTime=Temps de reconnaissance
acc_rtMonitor_sureToAckThese=Êtes-vous sûr de confirmer cette {0} alarme? Après votre confirmation, toutes les alarmes seront annulées.
acc_rtMonitor_sureToAckAllAlarm=Êtes-vous vraiment sûr d'annuler toutes les alarmes? Après votre confirmation, toutes les alarmes seront annulées.
acc_rtMonitor_noSelectAlarmEvent=Veuillez choisir de confirmer l'événement d'alarme!
acc_rtMonitor_noAlarmEvent=Il n'y a aucun événement d'alarme dans le système actuel!
acc_rtMonitor_forcefully=Annuler l'alarme (porte ouverte avec force)
acc_rtMonitor_addToRegPerson=Ajouté à la personne inscrite
acc_rtMonitor_cardExist=Cette carte a été attribuée par {0} et ne peut pas être délivrée à nouveau.
acc_rtMonitor_opResultPrompt=Envoyer {0} requête avec succès, échoué {1}.
acc_rtMonitor_doorOpFailedPrompt=Échec de l'envoi des demandes aux portes suivantes, veuillez réessayer!
acc_rtMonitor_remoteOpen=Ouverture à distance
acc_rtMonitor_remoteClose=Fermeture à distance
acc_rtMonitor_alarmSoundClose=L'audio est fermé
acc_rtMonitor_alarmSoundOpen=L'audio est ouvert
acc_rtMonitor_playAudio=Sons de rappel d'événement
acc_rtMonitor_isOpenShowPhoto=Activer la fonction d'affichage des photos
acc_rtMonitor_isOpenPlayAudio=Activer la fonction d'alerte audio
acc_rtm_open=Ouverture à distance du bouton
acc_rtm_close=Fermeture à distance du bouton
acc_rtm_eleModule=Ascenseur
acc_cancelAlarm_fp=Annuler l'alarme (Ouverture avec empreinte digitale sous contrainte)
acc_cancelAlarm_pwd=Annuler l'alarme (Ouverture avec mot de passe sous contrainte)
acc_cancelAlarm_timeOut=Annuler l'alarme (Expiration de l'extension de porte ouverte)
#定时同步设备时间
acc_timing_syncDevTime=Chronométrage de synchronisation de périphérique
acc_timing_executionTime=Le temps d'exécution
acc_timing_theLifecycle=Le cycle de vie
acc_timing_errorPrompt=Entrée de données incorrecte.
acc_timing_checkedSyncTime=Veuillez sélectionner une heure de synchronisation.
#[25]门禁报表
acc_trans_hasAccLevel=Avoir un niveau pour accès
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Veuillez ajouter une zone
acc_zone_code=Code de Zone
acc_zone_parentZone=Zone parent
acc_zone_parentZoneCode=Code de la Zone Parent
acc_zone_parentZoneName=Nom de la Zone Parent
acc_zone_outside=Dehors
#[G2]读头定义
acc_readerDefine_readerName=Nom du lecteur
acc_readerDefine_fromZone=Va de
acc_readerDefine_toZone=Va à
acc_readerDefine_delInfo1=La zone de cette définition de lecteur est référencée par une fonction de contrôle d'accès avancée et ne peut être supprimée!
acc_readerDefine_selReader=Sélectionner Lecteur
acc_readerDefine_selectReader=Veuillez ajouter un lecteur!
acc_readerDefine_tip=Une fois le personnel arrivé en dehors de la zone, le dossier personnel sera supprimé.
#[G3]全局反潜
acc_gapb_zone=Zone
acc_gapb_whenToResetGapb=Réinitialisation du délai Anti-Passback
acc_gapb_apbType=Type d'Anti-Passback
acc_gapb_logicalAPB=Anti-passback logique
acc_gapb_timedAPB=Anti-passback chronométré
acc_gapb_logicalTimedAPB=Anti-passback logique chronométré
acc_gapb_lockoutDuration=Durée de verrouillage
acc_gapb_devOfflineRule=Si le périphérique est hors ligne
acc_gapb_standardLevel=Niveau d'accès standard
acc_gapb_accessDenied=Accès refusé
acc_gapb_doorControlZone=Les portes suivantes contrôlent l'accès à l'intérieur et à l'extérieur de la zone
acc_gapb_resetStatus=Réinitialiser le statut Anti-Passback
acc_gapb_obeyAPB=Respecter les règles Anti-Passback
acc_gapb_isResetGAPB=Réinitialiser l'Anti-Passback
acc_gapb_resetGAPBSuccess=Etat de réinitialisation Anti-Passback réussi
acc_gapb_resetGAPBFaile=Etat de réinitialisation Anti-Passback échoué
acc_gapb_chooseArea=Veuillez resélectionner la zone.
acc_gapb_notDelInfo1=La zone d'accès est référencée comme zone d'accès supérieure et ne peut être supprimée.
acc_gapb_notDelInfo2=La zone est référencée par Lecteur Définie et ne peut être supprimée.
acc_gapb_notDelInfo3=La zone est référencée par des fonctions de contrôle d'accès avancées et ne peut être supprimée.
acc_gapb_notDelInfo4=La zone d'accès est référencée par la LED et ne peut être supprimée!
acc_gapb_zoneNumRepeat=Les numéros de domaine de contrôle ont des doublons
acc_gapb_zoneNameRepeat=Les noms de domaine de contrôle ont des doublons
acc_gapb_personResetGapbPre=Etes-vous sûr de réinitialiser ceci?
acc_gapb_personResetGapbSuffix=règles de personne Anti-Passback?
acc_gapb_apbPrompt=Une seule porte ne peut pas être utilisée pour contrôler deux limites de périmètre relativement indépendantes.
acc_gapb_occurApb=L'anti-passback se produit
acc_gapb_noOpenDoor=Ne pas ouvrir la porte
acc_gapb_openDoor=Ouvrir la porte
acc_gapb_zoneNumLength=Longueur supérieure à 20 caractères
acc_gapb_zoneNameLength=Longueur supérieure à 30 caractères
acc_gapb_zoneRemarkLength=Longueur supérieure à 50 caractères
acc_gapb_isAutoServerMode=Un appareil sans fonction de vérification background est détecté, ce qui peut affecter la fonction. L'ouvrir maintenant?
acc_gapb_applyTo=Postuler à
acc_gapb_allPerson=Tout le personnel
acc_gapb_justSelected=Juste Personnel sélectionné
acc_gapb_excludeSelected=Exclure personnel sélectionné
#[G4]who is inside
acc_zoneInside_lastAccessTime=Temps du dernier accès
acc_zoneInside_lastAccessReader=Dernier lecteur d'accès
acc_zoneInside_noPersonInZone=Pas de personne dans la zone
acc_zoneInside_noRulesInZone=Aucune règle définie.
acc_zoneInside_totalPeople=Nombre total de personnes
acc_zonePerson_selectPerson=Veuillez sélectionner une personne ou un département!
#[G5]路径
acc_route_name=Nom d'itinéraire
acc_route_setting=Réglage d'itinéraire
acc_route_addReader=Ajouter lecteur
acc_route_delReader=Supprimer lecteur
acc_route_defineReaderLine=Définir la ligne de lecture
acc_route_up=En haut
acc_route_down=En bas
acc_route_selReader=Sélectionner le lecteur après l'opération.
acc_route_onlyOneOper=Ne peut choisir qu'un seul lecteur pour fonctionner.
acc_route_readerOrder=Séquence définie du lecteur
acc_route_atLeastSelectOne=Veuillez sélectionner au moins une tête de lecture!
acc_route_routeIsExist=Cet itinéraire existe déjà!
#[G6]DMR
acc_dmr_residenceTime=Temps de résidence
acc_dmr_setting=Réglage DMR
#[G7]Occupancy
acc_occupancy_max=Capacité maximale
acc_occupancy_min=Capacité minimale
acc_occupancy_unlimit=Illimité
acc_occupancy_note=La capacité minimale de sièges ne doit pas dépasser la capacité maximale de sièges.
acc_occupancy_containNote=Veuillez définir au moins une des capacités maximale/minimale!
acc_occupancy_maxMinValid=Veuillez définir une capacité supérieure à 0.
acc_occupancy_conflict=Les règles de contrôle d'occupation ont été définies dans cette zone.
acc_occupancy_maxMinTip=Aucune valeur de capacité signifie aucune limitation.
#card availability
acc_personLimit_zonePropertyName=Nom de propriété de zone
acc_personLimit_useType=Utilisation
acc_personLimit_userDate=Date Valide
acc_personLimit_useDays=Après la première utilisation de jours valides
acc_personLimit_useTimes=Nombre de fois d'utilisation
acc_personLimit_setZoneProperty=Définir les propriétés de zone
acc_personLimit_zoneProperty=Propriétés de zone
acc_personLimit_availabilityName=Nom de disponibilité
acc_personLimit_days=Jours
acc_personLimit_Times=Nombre de Fois
acc_personLimit_noDel=Les propriétés de zone de contrôle d'accès sélectionnées sont référencées et ne peuvent être supprimées!
acc_personLimit_cannotEdit=L'attribut de zone d'accès est référencé, ne peut être modifié!
acc_personLimit_detail=Détail
acc_personLimit_userDateTo=Valide jusque
acc_personLimit_addPersonRepeatTip=La personne sous le département sélectionné a été ajoutée à l'attribut de la zone de contrôle d'accès, veuillez sélectionner à nouveau le département!
acc_personLimit_leftTimes={0} fois restantes
acc_personLimit_expired=expiré
acc_personLimit_unused=Inutilisé
#全局互锁
acc_globalInterlock_addGroup=Ajouter Groupe
acc_globalInterlock_delGroup=Supprimer Groupe
acc_globalInterlock_refuseAddGroupMessage=Le même interverrouillage, la porte ne peut pas être dupliquée dans le groupe ajouté
acc_globalInterlock_refuseAddlockMessage=La porte ajoutée est apparue dans d'autres groupes de périphériques interverrouillés
acc_globalInterlock_refuseDeleteGroupMessage=Veuillez supprimer les données liées à l'interverrouillage
acc_globalInterlock_isGroupInterlock=Interverrouillage de groupe
acc_globalInterlock_isAddTheDoorImmediately=Ajouter la porte immédiatement
acc_globalInterlock_isAddTheGroupImmediately=Ajouter le groupe immédiatement
#门禁参数设置
acc_param_autoEventDev=Télécharger automatiquement le journal des événements du nombre d'appareils simultanés
acc_param_autoEventTime=Télécharger automatiquement le journal des événements d'intervalles simultanés
acc_param_noRepeat=Aucune répétition des adresses e-mail n'est autorisée, veuillez remplir à nouveau.
acc_param_most18=Ajouter jusqu'à 18 adresses e-mail
acc_param_deleteAlert=Impossible de supprimer toutes les zones de saisie d'adresse e-mail.
acc_param_invalidOrRepeat=Erreur de format d'adresse e-mail ou répétition d'adresse e-mail.
#全局联动
acc_globalLinkage_noSupport=La porte actuellement sélectionnée ne prend pas en charge les fonctions verrouillage et verrouillage désactivé.
acc_globalLinkage_trigger=Déclencher Liaison globale
acc_globalLinkage_noAddPerson=La règle de liaison ne contient pas de déclencheur lié au personnel par défaut applicable à tout le personnel!
acc_globalLinkage_selectAtLeastOne=Veuillez sélectionner au moins un point de sortie de liaison et une liaison vidéo!
acc_globalLinkage_selectTrigger=Veuillez ajouter des conditions de déclenchement de liaison!
acc_globalLinkage_selectInput=Veuillez ajouter le point d'entrée!
acc_globalLinkage_selectOutput=Veuillez ajouter le point de sortie!
acc_globalLinkage_audioRemind=Intructions vocales de liaison
acc_globalLinkage_audio=Liaison vocale
acc_globalLinkage_isApplyToAll=Appliquer à tout le personnel
acc_globalLinkage_scope=Gamme du personnel
acc_globalLinkage_everyPerson=N'importe lequel
acc_globalLinkage_selectedPerson=sélectionné
acc_globalLinkage_noSupportPerson=Non supporté
acc_globalLinkage_reselectInput=Le type de conditions de déclenchement a changé, veuillez resélectionner le point d'entrée!
acc_globalLinkage_addPushDevice=Veuillez ajouter un support pour cette fonction d'appareil
#其他
acc_InputMethod_tips=Veuillez passer en mode de saisie Anglais!
acc_device_systemCheckTip=Le périphérique d'accès n'existe pas!
acc_notReturnMsg=Aucune information retournée
acc_validity_period=La licence a dépassé la période de validité; la fonction ne peut pas être opérée.
acc_device_pushMaxCount=Le système existe déjà dans {0} périphérique(s), atteint la limite de licence, ne peut pas ajouter l'appareil!
acc_device_videoHardwareLinkage=Définir la liaison du matériel vidéo
acc_device_videoCameraIP=Caméra vidéo IP
acc_device_videoCameraPort=Port vidéo caméra
acc_location_unable=Le point de l'incident n'a pas été ajouté à la carte électronique, impossible de localiser l'emplacement spécifique! 
acc_device_wgDevMaxCount=L'appareil a atteint la limite de licence dans le système et ne peut modifier les paramètres!
#自定义报警事件
acc_deviceEvent_selectSound=Veuillez sélectionner les fichiers audio.
acc_deviceEvent_batchSetSoundErr=Le réglage d'alarme par lots semble anormale.
acc_deviceEvent_batchSet=Régler Audio
acc_deviceEvent_sound=Son d'événement
acc_deviceEvent_exist=Existe déjà
acc_deviceEvent_upload=Transférer
#查询门最近发生事件
acc_doorEventLatestHappen=Recherchez les derniers événements de la porte
#门禁人员信息
acc_pers_delayPassage=Passage différé
#设备容量提示
acc_dev_usageConfirm=Capacité actuelle en excès de 90% de l'équipement
acc_dev_immediateCheck=Vérifier immédiatement
acc_dev_inSoftware=En logiciel
acc_dev_inFirmware=En FirmWare
acc_dev_get=Obtenir
acc_dev_getAll=Tout obtenir
acc_dev_loadError=Échec de chargement
#Reader
acc_reader_inout=Entrée/Sortie
acc_reader_lightRule=Règles d'éclairage
acc_reader_defLightRule=Règle par défaut
acc_reader_encrypt=Crypter
acc_reader_allReaderOfCurDev=Tous les lecteurs du périphérique actuel
acc_reader_tip1=Le cryptage est copié sur tous les lecteurs de l'appareil actuel!
acc_reader_tip2=L'option de mode ID n'est disponible que pour les têtes de lecture qui prennent en charge cette fonctionnalité!
acc_reader_tip3=Le type de protocole RS485 est copié sur tous les lecteurs du périphérique actuel. Les paramètres prendront effet après le redémarrage de l'appareil!
acc_reader_tip4=L'option permettant de masquer certaines informations personnelles est copiée par défaut sur tous les lecteurs du même appareil!
acc_reader_commType=Type de Communication
acc_reader_commAddress=Adresse de Communication
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=désactivé
acc_readerComAddress_repeat=Dupliquer Adresse de communication
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=Adresse RS485
acc_readerCommType_wgAddress=Adresse Wiegand
acc_reader_macError=Veuillez saisir l'adresse Mac au format correct!
acc_reader_machineType=Type de lecteur
acc_reader_readMode=Mode
acc_reader_readMode_normal=Mode normal
acc_reader_readMode_idCard=Mode carte d'identité
acc_reader_note=Conseils: Seuls les périphériques de contrôle d'accès dans la même zone ({0}) que la caméra peuvent être sélectionnés.
acc_reader_rs485Type=Type de protocole RS485
acc_reader_userLock=Verrou d'accès du personnel
acc_reader_userInfoReveal=Masquer les informations sur le personnel de la pièce
#operat
acc_operation_pwd=Mot de passe d'opération
acc_operation_pwd_error=Erreur de mot de passe
acc_new_input_not_same=La nouvelle entrée clé n'est pas cohérente
acc_op_set_keyword=Paramètres de clé de licence
acc_op_old_key=Ancienne clé
acc_op_new_key=Nouvelle clé
acc_op_cofirm_key=Confirmer Clé
acc_op_old_key_error=Erreur de l'ancienne Clé
#验证方式规则
acc_verifyRule_name=Nom de la règle
acc_verifyRule_door=Vérification de porte
acc_verifyRule_person=Vérification du personnel
acc_verifyRule_copy=Copier le paramètre du lundi dans d'autres jours de la semaine:
acc_verifyRule_tip1=Veuillez sélectionner au moins un mode de vérification!
acc_verifyRule_tip2=Si la règle contient le mode de vérification d'une personne, vous ne pouvez ajouter qu'une porte avec un lecteur Wiegand!
acc_verifyRule_tip3=Le lecteur RS485 ne peut suivre que le mode de vérification de porte, ne prend pas en charge le mode de vérification du personnel.
acc_verifyRule_oldVerifyMode=Ancien mode de vérification
acc_verifyRule_newVerifyMode=Nouveau mode de vérification
acc_verifyRule_newVerifyModeSelectTitle=Sélectionnez une nouvelle méthode de vérification
acc_verifyRule_newVerifyModeNoSupportTip=Aucun appareil ne prend en charge la nouvelle méthode de vérification!
#Wiegand Test
acc_wiegand_beforeCard=La longueur de la nouvelle carte({0} bits) n'est pas égale à la carte précédente!
acc_wiegand_curentCount=Longueur actuelle du numéro de carte : {0} Bits
acc_wiegand_card=Carte
acc_wiegand_readCard=Lire la carte
acc_wiegand_clearCardInfo=Effacer les informations de la carte
acc_wiegand_originalCard=Numéro de carte original
acc_wiegand_recommendFmt=Format de carte recommandé
acc_wiegand_parityFmt=Format de parité paire-impaire
acc_wiegand_withSizeCode=Calculer automatiquement le code du site quand le code du site est laissé vide
acc_wiegand_tip1=Ces cartes peuvent ne pas appartenir au même lot de cartes.
acc_wiegand_tip2=Code de Site:{0},numéro de carte:{1}, échec de correspondance du numéro de carte d'origine. Veuillez vérifier à nouveau le code de site et le numéro de carte saisis!
acc_wiegand_tip3=Le numéro de carte entré ({0}) ne peut pas correspondre au numéro de carte d'origine. Veuillez vérifier à nouveau!
acc_wiegand_tip4=Le code de site entré ({0}) ne peut pas correspondre au numéro de carte d'origine. Veuillez vérifier à nouveau!
acc_wiegand_tip5=Si vous souhaitez utiliser cette fonctionnalité, veuillez laisser toutes les colonnes de code de site vides!
acc_wiegand_warnInfo1=Lorsque vous continuez à lire une nouvelle carte, veuillez passer manuellement à la carte suivante.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Tableau d'entrée/sortie du personnel
acc_LCDRTMonitor_current=Informations actuelles du personnel
acc_LCDRTMonitor_previous=Informations précédentes du personnel
#api
acc_api_levelIdNotNull=L'ID du groupe d'autorisations ne peut pas être vide
acc_api_levelNotExist=Le groupe d'autorisations n'existe pas
acc_api_levelExist=Le Groupe d'autorisations existe
acc_api_areaNameNotNull=La zone ne peut pas être vide
acc_api_levelNotHasPerson=Aucune personne sous le groupe d'autorisation
acc_api_doorIdNotNull=L'ID de la porte ne peut pas être vide
acc_api_doorNameNotNull=Le nom de la porte ne peut pas être vide
acc_api_doorIntervalSize=La durée d'ouverture de la porte doit être comprise entre 1~254
acc_api_doorNotExist=La porte n'existe pas
acc_api_devOffline=Le périphérique est hors ligne ou désactivé
acc_api_devSnNotNull=Le SN du périphérique ne peut pas être vide
acc_api_timesTampNotNull=L'horodatage ne peut pas être vide
acc_api_openingTimeCannotBeNull=Le temps d'ouverture de la porte ne peut pas être vide
acc_api_parameterValueCannotBeNull=La valeur du paramètre ne peut pas être vide
acc_api_deviceNumberDoesNotExist=Le numéro de série de l'appareil n'existe pas
acc_api_readerIdCannotBeNull=L'ID de lecteur ne peut pas être vide
acc_api_theReaderDoesNotExist=La tête de lecture n'existe pas
acc_operate_door_notInValidDate=Actuellement pas dans le délai effectif d'ouverture à distance, veuillez contacter l'administrateur si nécessaire!
acc_api_doorOffline=La porte est hors ligne ou désactivée
#门禁信息自动导出
acc_autoExport_title=Exportation automatique des transactions
acc_autoExport_frequencyTitle=Fréquence d'exportation automatique
acc_autoExport_frequencyDay=Par Jour
acc_autoExport_frequencyMonth=Par Mois
acc_autoExport_firstDayMonth=Premier jour du mois
acc_autoExport_specificDate=Date spécifique
acc_autoExport_exportModeTitle=Mode d'exportation
acc_autoExport_dailyMode=Transactions quotidiennes
acc_autoExport_monthlyMode=Transactions mensuelles(Toutes les transactions entre la date du mois dernier et ce mois)
acc_autoExport_allMode=Toutes les données(exporter jusqu'à 30000 pièces de données)
acc_autoExport_recipientMail=Boîte aux lettres du destinataire
#First In And Last Out
acc_inOut_inReaderName=Premier nom du lecteur d'entrée
acc_inOut_firstInTime=Heure d'entrée la plus précoce
acc_inOut_outReaderName=Dernier nom du lecteur de congé
acc_inOut_lastOutTime=Dernier temps de congé
#防疫参数
acc_dev_setHep=Définir les paramètres de détection de masque&température
acc_dev_enableIRTempDetection=Activer dépistage de température avec infrarouge
acc_dev_enableNormalIRTempPass=Refuser accès si température dépasse la plage
acc_dev_enableMaskDetection=Activer détection de masque
acc_dev_enableWearMaskPass=Refuser accès sans masque
acc_dev_tempHighThreshold=Seuil d'alarme haute température
acc_dev_tempUnit=Unité de température
acc_dev_tempCorrection=Correction d'écart de température
acc_dev_enableUnregisterPass=Autoriser personnes non enregistrées à accéder
acc_dev_enableTriggerAlarm=Déclencher alarme externe
#联动邮件
acc_mail_temperature=Température corporelle
acc_mail_mask=Que ce soit pour porter le masque
acc_mail_unmeasured=Non mesuré
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Événements globaux Digifort
acc_digifort_chooseDigifortEvents=Choisissez les événements globaux Digifort
acc_digifort_eventExpiredTip=Si l'événement global est supprimé du serveur Digifort, il sera en rouge.
acc_digifort_checkConnection=Veuillez vérifier si les informations de connexion du serveur Digifort sont correctes.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Ajouter des contacts
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=Définir les paramètres étendus
acc_extendParam_faceUI=Affichage de l'interface
acc_extendParam_faceParam=Paramètres de face
acc_extendParam_accParam=Paramètres de contrôle d'accès
acc_extendParam_intercomParam=Paramètres d'intercom visuel
acc_extendParam_volume=Volume
acc_extendParam_identInterval=Intervalle d'identification (ms)
acc_extendParam_historyVerifyResult=Afficher les résultats de la vérification historique
acc_extendParam_macAddress=Afficher l'adresse MAC
acc_extendParam_showIp=Afficher l'adresse IP
acc_extendParam_24HourFormat=Afficher le format 24 heures
acc_extendParam_dateFormat=Format de date
acc_extendParam_1NThreshold=seuil 1: N
acc_extendParam_facePitchAngle=Angle de tangage du visage
acc_extendParam_faceRotationAngle=Angle de rotation de la face
acc_extendParam_imageQuality=Qualité d'image
acc_extendParam_miniFacePixel=Pixel de visage minimum
acc_extendParam_biopsy=Activer la biopsie
acc_extendParam_showThermalImage=Afficher l'image thermique
acc_extendParam_attributeAnalysis=Activer l'analyse d'attribut
acc_extendParam_temperatureAttribute=Attribut de détection de température
acc_extendParam_maskAttribute=Attribut de détection de masque
acc_extendParam_minTemperature=Limite inférieure du seuil de détection de la température corporelle
acc_extendParam_maxTemperature=Limite supérieure du seuil de détection de la température corporelle
acc_extendParam_gateMode=Mode Gate
acc_extendParam_qrcodeEnable=Activer la fonction QR code
#可视对讲
acc_dev_intercomServer=Adresse de service d'interphone visuel
acc_dev_intercomPort=Serveur d'intercom visuel
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Niveau de synchronisation
# 夏令时名称
acc_dsTimeUtc_none=Ne pas définir
acc_dsTimeUtc_AreaNone=Il n’y a pas d’heure avancée dans la région
acc_dsTimeUtc1000_0=Canberra, Melbourne, Sydney
acc_dsTimeUtc1000_1=Hobart.
acc_dsTimeUtc_0330_0=Terre - Neuve
acc_dsTimeUtc_1000_0=Îles Aléoutiennes
acc_dsTimeUtc_0200_0=Atlantique Moyen - ancien
acc_dsTimeUtc0930_0=Adelaide
acc_dsTimeUtc_0100_0=Açores
acc_dsTimeUtc_0400_0=Heure de l'Atlantique (Canada)
acc_dsTimeUtc_0400_1=Santiago
acc_dsTimeUtc_0400_2=Asunción
acc_dsTimeUtc_0300_0=Groenland
acc_dsTimeUtc_0300_1=Îles Saint - Pierre - et - Miquelon
acc_dsTimeUtc0200_0=Chisinau
acc_dsTimeUtc0200_1=Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius
acc_dsTimeUtc0200_2=Athènes, Bucarest
acc_dsTimeUtc0200_3=Jérusalem
acc_dsTimeUtc0200_4=Amman
acc_dsTimeUtc0200_5=Beyrouth
acc_dsTimeUtc0200_6=Damas
acc_dsTimeUtc0200_7=Hébron, Gaza
acc_dsTimeUtc0200_8=Juba
acc_dsTimeUtc_0600_0=Heure centrale (États - Unis et Canada)
acc_dsTimeUtc_0600_1=Guadalajara, Mexico, Monterrey
acc_dsTimeUtc_0600_2=L'île de Pâques
acc_dsTimeUtc1300_0=Samoa
acc_dsTimeUtc_0500_0=La Havane
acc_dsTimeUtc_0500_1=Heure de l'Est (États - Unis et Canada)
acc_dsTimeUtc_0500_2=Haïti
acc_dsTimeUtc_0500_3=Indiana (est)
acc_dsTimeUtc_0500_4=Îles Turques et Caïques
acc_dsTimeUtc_0800_0=Heure du Pacifique (États - Unis et Canada)
acc_dsTimeUtc_0800_1=Basse Californie
acc_dsTimeUtc0330_0=Téhéran
acc_dsTimeUtc0000_0=Dublin, Edimbourg, Lisbonne, Londres
acc_dsTimeUtc1200_0=Fidji
acc_dsTimeUtc1200_1=Petropavlovsk - Kamchatka - ancien
acc_dsTimeUtc1200_2=Oakland, Wellington
acc_dsTimeUtc1100_0=Norfolk Island
acc_dsTimeUtc_0700_0=Chihuahua, la Paz, masatlan
acc_dsTimeUtc_0700_1=Heure des montagnes (États - Unis et Canada)
acc_dsTimeUtc0100_0=Belgrade, Bratislava, Budapest, Ljubljana, Prague
acc_dsTimeUtc0100_1=Sarajevo, Skopje, Varsovie, Zagreb
acc_dsTimeUtc0100_2=Casablanca
acc_dsTimeUtc0100_3=Bruxelles, Copenhague, Madrid, Paris
acc_dsTimeUtc0100_4=Amsterdam, Berlin, Berne, Rome, Stockholm, Vienne
acc_dsTimeUtc_0900_0=Alaska
#安全点(muster point)
acc_leftMenu_accMusterPoint=Point de rassemblement
acc_musterPoint_activate=Activer
acc_musterPoint_addDept=Ajouter un département
acc_musterPoint_delDept=Supprimer le département
acc_musterPoint_report=Rapport sur les points de rassemblement
acc_musterPointReport_sign=Se connecter manuellement
acc_musterPointReport_generate=Générer des rapports
acc_musterPoint_addSignPoint=Ajouter un point de signalisation
acc_musterPoint_delSignPoint=Supprimer le point de signalisation
acc_musterPoint_selectSignPoint=Veuillez ajouter un point de signalisation!
acc_musterPoint_signPoint=Point de signalisation
acc_musterPoint_delFailTip=Il y a déjà des points de rassemblement activés et ils ne peuvent pas être supprimés!
acc_musterPointReport_enterTime=Entrez l'heure
acc_musterPointReport_dataAnalysis=Analyse des données
acc_musterPointReport_safe=Sûr
acc_musterPointReport_danger=Danger
acc_musterPointReport_signInManually=pointage manuel
acc_musterPoint_editTip=Le point de rassemblement est actif et ne peut pas être modifié!
acc_musterPointEmail_total=Nombre de participants attendus:
acc_musterPointEmail_safe=Check - in (sécurité):
acc_musterPointEmail_dangerous=En danger:
acc_musterPoint_messageNotification=Notification de message lors de l'activation
acc_musterPointReport_sendEmail=Rapports Push planifiés
acc_musterPointReport_sendInterval=Intervalle d'envoi
acc_musterPointReport_sendTip=Assurez - vous que la méthode de notification sélectionnée est configurée avec succès, sinon les notifications ne seront pas envoyées correctement!
acc_musterPoint_mailSubject=Notification de collection d'urgence
acc_musterPoint_mailContent=Veuillez vous rassembler immédiatement à "{0}" et vous connecter sur l'appareil "{1}", merci!
acc_musterPointReport_mailHead=Bonjour, voici le rapport d'urgence. Veuillez réviser.
acc_musterPoint_visitorsStatistics=Statistiques visiteurs
# 报警监控
acc_alarm_priority=Priorité
acc_alarm_total=Total
acc_alarm_today=Aujourd'hui
acc_alarm_unhandled=Non confirmé
acc_alarm_inProcess=En cours de traitement
acc_alarm_acknowledged=Confirmé
acc_alarm_top5=Les cinq premiers événements d'alarme
acc_alarm_monitoringTime=Temps de surveillance
acc_alarm_history=Historique du traitement des alarmes
acc_alarm_acknowledgement=Traitement des dossiers
acc_alarm_eventDescription=Détails de l'événement
acc_alarm_acknowledgeText=Une fois sélectionné, les détails de l'événement d'alarme seront envoyés à la boîte aux lettres spécifiée
acc_alarm_emailSubject=Ajouter un enregistrement de traitement des événements d'alarme
acc_alarm_mute=Silence.
acc_alarm_suspend=Pause
acc_alarm_confirmed=L'événement a été confirmé
acc_alarm_list=Journal des alarmes
#ntp
acc_device_setNTPService=Paramètres du serveur NTP
acc_device_setNTPServiceTip=Entrez plusieurs adresses de serveur, séparées par une virgule (,) ou un point-virgule (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=Dans le fonctionnement du Contrôleur d'accès, le superutilisateur n'est pas limité par le fuseau horaire, l'anti - retour et l'enclenchement, et a une très haute priorité d'ouverture de porte.
acc_editPerson_delayPassageTip=Prolonger le temps d'attente du personnel pour passer le point d'accès. Convient aux personnes handicapées physiques ou autres.
acc_editPerson_disabledTip=Désactive temporairement le niveau d'accès des personnes.
#门禁向导
acc_guide_title=Assistant de configuration du module de contrôle d'accès
acc_guide_addPersonTip=Vous devez ajouter la personne et les informations d'identification correspondantes (visage ou empreinte digitale ou carte ou paume ou mot de passe ); si vous avez déjà ajouté, ignorez directement cette étape
acc_guide_timesegTip=Veuillez configurer une période d'ouverture valide
acc_guide_addDeviceTip=Veuillez ajouter l'appareil correspondant comme point d'accès
acc_guide_addLevelTip=Ajouter un niveau de contrôle d'accès
acc_guide_personLevelTip=Attribuer l'autorité de contrôle d'accès correspondante à la personne
acc_guide_rtMonitorTip=Vérifier les enregistrements de contrôle d'accès en temps réel
acc_guide_rtMonitorTip2=Visualisez les enregistrements de contrôle d'accès en temps réel après avoir ajouté la zone à laquelle vous appartenez et la porte correspondante
#查看区域内人员
acc_zonePerson_cleanCount=Effacer les statistiques des personnes entrant et sortant
acc_zonePerson_inCount=Statistiques du nombre de personnes entrant
acc_zonePerson_outCount=Statistiques du nombre de départs
#biocv460
acc_device_validFail=Le nom d'utilisateur ou le mot de passe est incorrect et la vérification échoue!
acc_device_pwdRequired=Seuls les entiers à 6 chiffres maximum peuvent être entrés