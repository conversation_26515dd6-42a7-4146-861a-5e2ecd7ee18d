#[1]左侧菜单
acc_module=الوصول
acc_leftMenu_accDev=جهاز الوصول
acc_leftMenu_auxOut=المخرجات الإضافية
acc_leftMenu_dSTime=التوقيت الصيفي
acc_leftMenu_access=التحكم فى الوصول
acc_leftMenu_door=المدخل
acc_leftMenu_accRule=قاعدة الوصول
acc_leftMenu_interlock=الانترلوك
acc_leftMenu_antiPassback=مانع الحركة العكسية
acc_leftMenu_globalLinkage=الرابطة العالمية
acc_leftMenu_firstOpen=صيغة المتكلم عادة متاحة
acc_leftMenu_combOpen=صيغة الجمع تتيح الفرصة للدخول
acc_leftMenu_personGroup=مجموعة متعددة الأشخاص
acc_leftMenu_level=مستويات الوصول
acc_leftMenu_electronicMap=خريطة
acc_leftMenu_personnelAccessLevels=مستويات وصول الموظفين
acc_leftMenu_searchByLevel=عن طريق مستوى الوصول
acc_leftMenu_searchByDoor=حقوق الوصول من المدخل
acc_leftMenu_expertGuard=وظائف متقدمة
acc_leftMenu_zone=النطاق
acc_leftMenu_readerDefine=تعريف القارئ
acc_leftMenu_gapbSet=الحماية الشاملة ضد الرجوع العكسى
acc_leftMenu_whoIsInside=من هو في الجزء الداخلى
acc_leftMenu_whatRulesInside=ما هي القواعد تنطبق فى الجزء داخلى
acc_leftMenu_occupancy=التحكم في التشغيل
acc_leftMenu_route=التحكم في المسار
acc_leftMenu_globalInterlock=الانترلوك العالمي
acc_leftMeue_globalInterlockGroup=مجموعة الأنترلوك العالمية
acc_leftMenu_dmr=قاعدة الشخص الميت
acc_leftMenu_personLimit=توافر الأشخاص
acc_leftMenu_verifyModeRule=وضع التحقق
acc_leftMenu_verifyModeRulePersonGroup=مجموعة وضع التحقق
acc_leftMenu_extDev=I/O المجلس
acc_leftMenu_firstInLastOut=الأول في الداخل والخارج
acc_leftMenu_accReports=تقارير التحكم في الوصول
#[3]门禁时间段
acc_timeSeg_entity=وحدة زمنية
acc_timeSeg_canNotDel=الفترة الزمنية قيد الاستخدام ولا يمكن حذفها
#[4]门禁设备--公共的在common中
acc_common_ruleName=اسم القاعدة
acc_common_hasBeanSet=قد تم الضبط
acc_common_notSet=لم يتم الضبط
acc_common_hasBeenOpened=افتتح
acc_common_notOpened=لم يفتتح
acc_common_partSet=جزء من المجموعة
acc_common_linkageAndApbTip=ويتم ضبط الروابط والربط العالمي، ومنع الدخول، ومنع الدخول على الصعيد العالمي في الوقت نفسه، وقد تكون هناك تعارضات
acc_common_vidlinkageTip=تأكد من ربط رابط نقطة الإدخال المقابل بقناة الفيديو المتاحة، وإلا فلن تعمل وظيفة رابط الفيديو!
acc_common_accZoneFromTo=لا يمكن ضبط نفس النطاق
acc_common_logEventNumber=معرف الحدث
acc_common_bindOrUnbindChannel=تجليد / فك الكاميرا
acc_common_boundChannel=منضم الكاميرا
#设备信息
acc_dev_iconType=نوع الأيقونة
acc_dev_carGate=حاجز الوقوف
acc_dev_channelGate=حاجز التغطية
acc_dev_acpType=نوع لوحة التحكم
acc_dev_oneDoorACP=لوحة تحكم الوصول من مدخل واحد
acc_dev_twoDoorACP=لوحة تحكم الوصول من مدخلين
acc_dev_fourDoorACP=لوحة تحكم الوصول من اربع مداخل
acc_dev_onDoorACD=جهاز مستقل
acc_dev_switchToTwoDoorTwoWay=قم بالتبديل إلى مدخلين ثنائي الاتجاه
acc_dev_addDevConfirm2=نصيحة: نجح اتصال الجهاز، لكن نوع لوحة التحكم بالوصول يختلف عن اللوحة الفعلية، قم بتعديلها إلى لوحة التحكم (المداخل) المدخل (0). هل تريد متابعة الإضافة؟
acc_dev_addDevConfirm4=جهاز مستقل. الاستمرار في إضافة؟
acc_dev_oneMachine=جهاز مستقل
acc_dev_fingervein=الإشارة بالاصبع
acc_dev_control=التحكم فى الجهاز
acc_dev_protocol=نوع البروتوكول
acc_ownedBoard=امتلاك لوحة التوسع
#设备操作
acc_dev_start=البدء
acc_dev_accLevel=سلطة الوصول
acc_dev_timeZoneAndHoliday=النطاق الزمني، العطلات
acc_dev_linkage=مجموعة أوصال
acc_dev_doorOpt=معايير المدخل
acc_dev_firstPerson=صيغة المتكلم متاحة للدخول
acc_dev_multiPerson=صيغة الجمع تتيح الفرصة للدخول
acc_dev_interlock=الانترلوك
acc_dev_antiPassback=مضاد للرجوع العكسى
acc_dev_wiegandFmt=تنسيق يجاند
acc_dev_outRelaySet=إعدادات المخرجات الإضافية
acc_dev_backgroundVerifyParam=خيارات التحقق Bg
acc_dev_getPersonInfoPrompt=يرجى التأكد من حصولك على معلومات الموظفين بنجاح، وإلا فسيحدث استثناء. استمر؟
acc_dev_getEventSuccess=الحصول على الأحداث بنجاح
acc_dev_getEventFail=فشل في الحصول على الحدث
acc_dev_getInfoSuccess=الحصول على المعلومات بنجاح.
acc_dev_getInfoXSuccess=الحصول على {0} بنجاح.
acc_dev_getInfoFail=فشل في الحصول على المعلومات.
acc_dev_updateExtuserInfoFail=فشل تحديث معلومات الموظفين للممر الممتد، يرجى استرداد المعلومات.
acc_dev_getPersonCount=الحصول على كمية المستخدمين
acc_dev_getFPCount=الحصول على كمية بصمات الاصابع
acc_dev_getFVCount=الحصول على كمية الاشارة بالاصابع
acc_dev_getFaceCount=الحصول على كمية الوجوه
acc_dev_getPalmCount=احصل على عدد النخيل
acc_dev_getBiophotoCount=الحصول على كمية صور الوجه
acc_dev_noData=لا يتم إرجاع البيانات من الجهاز
acc_dev_noNewData=لا توجد معاملات جديدة في الجهاز.
acc_dev_softLtDev=يوجد عدد أكبر من الأشخاص في البرنامج مقارنة بالجهاز.
acc_dev_personCount=كمية الاشخاص
acc_dev_personDetail=وفيما يلي التفاصيل:
acc_dev_softEqualDev=كمية الأشخاص في كل من البرنامج والجهاز هي نفسها.
acc_dev_softGtDev=يوجد عدد أكبر من الأشخاص في الجهاز أكثر من عددهم في البرامج.
acc_dev_cmdSendFail=أخفق إرسال الأوامر، يرجى إعادة الإرسال
acc_dev_issueVerifyParam=تعيين خيارات التحقق من الخلفية
acc_dev_verifyParamSuccess=تم تطبيق خيارات التحقق من الخلفية بنجاح
acc_dev_backgroundVerify=التحقق من الخلفية
acc_dev_selRightFile=حدد ملف التصاعد الصحيح!
acc_dev_devNotOpForOffLine=الجهاز غير متصل بالإنترنت، يرجى إعادة المحاولة لاحقًا
acc_dev_devNotSupportFunction=الجهاز لا يدعم هذه الميزة
acc_dev_devNotOpForDisable=تم تعطيل الجهاز، يرجى إعادة المحاولة لاحقًا
acc_dev_devNotOpForNotOnline=الجهاز غير متصل أو معطل، يرجى إعادة المحاولة لاحقًا
acc_dev_getPersonInfo=الحصول على معلومات الأشخاص
acc_dev_getFPInfo=الحصول على معلومات بصمة الاصابع
acc_dev_getFingerVeinInfo=الحصول على معلومات عن إشارة الاصابع
acc_dev_getPalmInfo=احصل على معلومات النخيل
acc_dev_getBiophotoInfo=الحصول على معلومات وجه الضوء المرئي
acc_dev_getIrisInfo=الحصول على معلومات ايريس
acc_dev_disable=تعطيل، يرجى إعادة اختيار
acc_dev_offlineAndContinue=غير متصل، تابع؟
acc_dev_offlineAndSelect=غير متصل.
acc_dev_opAllDev=كل الاجهزة
acc_dev_opOnlineDev=جهاز متصل بالانترنت
acc_dev_opException=التعامل مع الاستثناءات
acc_dev_exceptionAndConfirm=مهلة اتصال الجهاز، فشلت العملية. تحقق من اتصال الشبكة
acc_dev_getFaceInfo=الحصول على معلومات الوجه
acc_dev_selOpDevType=حدد نوع الجهاز المطلوب تشغيله:
acc_dev_hasFilterByFunc=تظهر فقط الأجهزة عبر الإنترنت ودعم هذه الوظيفة!
acc_dev_masterSlaveMode=وضع RS485 الرئيسي ووضع العاكس التابع
acc_dev_master=استضافة
acc_dev_slave=الوضع العاكس التابع
acc_dev_modifyRS485Addr=قم بتعديل عنوان RS485
acc_dev_rs485AddrTip=الرجاء إدخال عدد صحيح بين 1 و 63!
acc_dev_enableFeature=الأجهزة التي مكّنت التحقق من الخلفية
acc_dev_disableFeature=الأجهزة التي عطلت التحقق من الخلفية
acc_dev_getCountOnly=الحصول على العد فقط
acc_dev_queryDevPersonCount=الاستعلام عن عدد العاملين
acc_dev_queryDevVolume=عرض سعة الجهاز
acc_dev_ruleType=نوع القاعدة
acc_dev_contenRule=تفاصيل القواعد
acc_dev_accessRules=عرض قواعد الأجهزة
acc_dev_ruleContentTip=بين قواعد متعددة مع فصل '|'.
acc_dev_rs485AddrFigure=شكل رمز العنوان RS485
acc_dev_addLevel=إضافة إلى المستوى
acc_dev_personOrFingerTanto=تجاوز عدد العاملين أو بصمة الإصبع الحدود، فشلت المزامنة...
acc_dev_personAndFingerUnit=( الرقم)
acc_dev_setDstime=ضبط التوقيت الصيفي
acc_dev_setTimeZone=ضبط النطاق الزمني للجهاز
acc_dev_selectedTZ=النطاق الزمنى المحدد
acc_dev_timeZoneSetting=إعداد النطاق الزمني ...
acc_dev_timeZoneCmdSuccess=نجاح إرسال أمر النطاق الزمني...
acc_dev_enableDstime=تمكين التوقيت الصيفي
acc_dev_disableDstime=تعطيل التوقيت الصيفي
acc_dev_timeZone=النطاق الزمنى
acc_dev_dstSettingTip=إعداد التوقيت الصيفي ...
acc_dev_dstDelTip=إزالة التوقيت الصيفي للجهاز ...
acc_dev_enablingDst=تمكين التوقيت الصيفي
acc_dev_dstEnableCmdSuccess=تم تفعيل أمر التوقيت الصيفي بنجاح الإرسال.
acc_dev_disablingDst=إلغاء تفعيل التوقيت الصيفي.
acc_dev_dstDisableCmdSuccess=نجاح تعطيل إرسال أمر التوقيت الصيفي
acc_dev_dstCmdSuccess=نجاح إرسال أمر التوقيت الصيفي...
acc_dev_usadst=التوقيت الصيفي
acc_dev_notSetDst=لا يوجد إعدادات
acc_dev_selectedDst=تم تحديد التوقيت الصيفي
acc_dev_configMasterSlave=التكوين الرئيسي التابع
acc_dev_hasFilterByUnOnline=فقط عرض الجهاز عبر الإنترنت.
acc_dev_softwareData=إذا وجدت أن البيانات غير متوافقة مع الجهاز، فيرجى مزامنة بيانات الأجهزة قبل إعادة المحاولة.
acc_dev_disabled=لا يمكن تشغيل الأجهزة المعطلة!
acc_dev_offline=لا يمكن تشغيل الأجهزة غير المتصلة!
acc_dev_noSupport=لا تدعم الأجهزة هذه الوظيفة ولا يمكن تشغيلها!
acc_dev_noRegDevTip=لم يتم تعريف هذا الجهاز كجهاز تسجيل. لن يتم تحديث البيانات الموجودة في البرنامج. هل ترغب في الاستمرار؟
acc_dev_noOption=لا توجد خيارات مؤهلة
acc_dev_devFWUpdatePrompt=لن يستخدم الجهاز الحالي عادة الشخص الغير قادر جسديا ووظيفة صلاحية الشخص (يرجى الرجوع إلى دليل المستخدم).
acc_dev_panelFWUpdatePrompt=الجهاز الحالي لن تستخدم عادة الشخص الغير قادر جسديا و وظيفة صلاحية الشخص، على الفور تصاعد البرامج الثابتة؟
acc_dev_sendEventCmdSuccess=تم إرسال أمر حذف الحدث بنجاح
acc_dev_tryAgain=يرجى إعادة المحاولة
acc_dev_eventAutoCheckAndUpload=التحقق التلقائي من الأحداث وتحميلها
acc_dev_eventUploadStart=بدء تحميل الأحداث
acc_dev_eventUploadEnd=اكتمل تحميل الأحداث.
acc_dev_eventUploadFailed=فشل تحميل الأحداث.
acc_dev_eventUploadPrompt=إصدار البرنامج الثابت للجهاز قديم جدًا، قبل تصعيد البرنامج الثابت، تريد:
acc_dev_backupToSoftware=نسخ البيانات احتياطيًا إلى البرنامج
acc_dev_deleteEvent=حذف السجل القديم
acc_dev_upgradePrompt=إصدار البرنامج الثابت قديم جدًا وقد يتسبب في حدوث أخطاء، يرجى نسخ البيانات احتياطيًا إلى البرنامج قبل تحديث البرنامج الثابت
acc_dev_conflictCardNo=بطاقة موجودة في النظام إلى {0} في الشخص الآخر!
acc_dev_rebootAfterOperate=نجحت العملية، سيتم إعادة تشغيل الجهاز لاحقًا.
acc_dev_baseOptionTip=عدم انتظام المعلمة الأساسية التي تم الحصول عليها.
acc_dev_funOptionTip=عدم انتظام معلمة الوظيفة التي تم الحصول عليها.
acc_dev_sendComandoTip=فشل إرسال أمر معلمة الجهاز.
acc_dev_noC3LicenseTip=لا يمكن إضافة هذا النوع من الأجهزة ({0}) يرجى الاتصال بقسم المبيعات لدينا.
acc_dev_combOpenDoorTip=({0}) تم تعيين باب مفتوح متعدد الأشخاص ، لا يمكن استخدامه في نفس الوقت مع التحقق من الخلفية.
acc_dev_combOpenDoorPersonCountTip=لا يمكن للمجموعة {0} أن تفتح أكثر من {1}!
acc_dev_addDevTip=ينطبق ذلك فقط على إضافة جهاز مزود بميزة الاتصال بالسحب
acc_dev_addError=إضافة استثناء الجهاز ، مفقود المعلمات ({0})!
acc_dev_updateIPAndPortError=تحديث خطأ IP الخاص بالخادم والمنفذ...
acc_dev_transferFilesTip=اكتمل اختبار البرنامج الثابت، جاري نقل الملفات
acc_dev_serialPortExist=المنفذ التسلسلي موجود
acc_dev_isExist=وجود جهاز
acc_dev_description=الوصف
acc_dev_searchEthernet=البحث في جهاز Ethernet
acc_dev_searchRS485=البحث في جهاز RS485
acc_dev_rs485AddrTip1=لا يمكن أن يكون عنوان البدء RS485 أكبر من عنوان النهاية
acc_dev_rs485AddrTip2=يجب أن يكون نطاق البحث في RS485 أقل من 20
acc_dev_clearAllCmdCache=حذف كل الاوامر
acc_dev_authorizedSuccessful=تم الترخيص بنجاح
acc_dev_authorize=الترخيص
acc_dev_registrationDevice=تسجيل الجهاز
acc_dev_setRegistrationDevice=تعيين كجهاز تسجيل
acc_dev_mismatchedDevice=لا يمكن استخدام هذا الجهاز لسوقك. يرجى التحقق من الرقم التسلسلي للجهاز مع مبيعاتنا.
acc_dev_pwdStartWithZero=كلمة مرور الاتصالات لا يمكن أن تبدأ بصفر!
acc_dev_maybeDisabled=يسمح لك الترخيص الحالي فقط بإضافة {0} باب آخر ، سيتم تعطيل باب الجهاز المضاف الجديد الذي يتجاوز حد باب الترخيص ، سواء كنت ستستمر في العمل؟
acc_dev_Limit=لقد بلغ مفتاح ترخيص الجهاز الحد الأعلى لإضافته، يرجى التصريح
acc_dev_selectDev=يرجى تحديد الجهاز!
acc_dev_cannotAddPullDevice=تعذر إضافة جهاز السحب يرجى الاتصال بمبيعاتنا.
acc_dev_notContinueAddPullDevice=هناك {0} سحب أجهزة في النظام ، لا يمكن الاستمرار في الإضافة مرة أخرى! يرجى الاتصال بمبيعاتنا.
acc_dev_deviceNameNull=نموذج الجهاز فارغ ؛ لا يمكن إضافة الجهاز!
acc_dev_commTypeErr=نوع الاتصال غير متطابق; لا يمكن إضافة جهاز!
acc_dev_inputDomainError=يرجى إدخال عنوان مجال صالح.
acc_dev_levelTip=هناك أكثر من 5000 شخص في المستوى، ولا يمكن إضافة الجهاز إلى المستوى هنا
acc_dev_auxinSet=إعداد الإدخال الإضافي
acc_dev_verifyModeRule=قاعدة التحقق من الوضع
acc_dev_netModeWired=سلكي
acc_dev_netMode4G=الجيل الرابع
acc_dev_netModeWifi=واي فاي
acc_dev_updateNetConnectMode=تبديل اتصال الشبكة
acc_dev_wirelessSSID=SSID اللاسلكية
acc_dev_wirelessKey=مفتاح لاسلكي
acc_dev_searchWifi=البحث عن واي فاي
acc_dev_testNetConnectSuccess=هل الاتصال ناجح؟
acc_dev_testNetConnectFailed=تعذر الاتصال بشكل صحيح!
acc_dev_signalIntensity=قوة الإشارة
acc_dev_resetSearch=البحث مرة اخرى
acc_dev_addChildDevice=إضافة جهاز فرعي
acc_dev_modParentDevice=قم بتغيير الجهاز الرئيسي
acc_dev_configParentDevice=إعدادات الجهاز الرئيسى
acc_dev_lookUpChildDevice=عرض الأجهزة التابعة
acc_dev_addChildDeviceTip=يجب إجراء التصريح بموجب جهاز فرعي معتمد
acc_dev_maxSubCount=يتجاوز عدد الأجهزة الفرعية المعتمدة الحد الأقصى لعدد الوحدات {0}.
acc_dev_seletParentDevice=برجاء اختيار الجهاز الرئيسى
acc_dev_networkCard=بطاقة الشبكة
acc_dev_issueParam=معلمات المزامنة المخصصة
acc_dev_issueMode=وضع المزامنة
acc_dev_initIssue=بيانات تهيئة إصدار البرنامج الثابت 3030
acc_dev_customIssue=مزامنة البيانات المخصصة
acc_dev_issueData=البيانات
acc_dev_parent=الجهاز الرئيسى
acc_dev_parentEnable=تم إلغاء تفعيل الجهاز الرئيسي
acc_dev_parentTips=سيقوم جهاز الربط الرئيسي بحذف جميع البيانات الموجودة في الجهاز وسيحتاج إلى إعادة ضبطه مرة أخرى.
acc_dev_addDevIpTip=لا يمكن أن يكون عنوان IP الجديد هو نفسه عنوان IP الخاص بالخادم
acc_dev_modifyDevIpTip=لا يمكن أن يكون عنوان الخادم الجديد هو نفسه عنوان IP الخاص بالجهاز
acc_dev_setWGReader=تعيين القارئ يجاند
acc_dev_selectReader=انقر لتحديد القارئ
acc_dev_IllegalDevice=جهاز غير قانوني
acc_dev_syncTimeWarnTip=يجب مزامنة أوقات التزامن للأجهزة التالية على الجهاز الرئيسي
acc_dev_setTimeZoneWarnTip=يجب مزامنة المنطقة الزمنية للجهاز التالي على الجهاز الرئيسي.
acc_dev_setDstimeWarnTip=يجب مزامنة التوقيت الصيفي للأجهزة التالية على الجهاز الرئيسي.
acc_dev_networkSegmentSame=لا يمكن لمحولي شبكة استخدام نفس مقطع الشبكة
acc_dev_upgradeProtocolNoMatch=بروتوكول ملف التصاعد غير مطابق
acc_dev_ipAddressConflict=الجهاز الذي يحمل نفس عنوان IP موجود بالفعل. يرجى تعديل عنوان IP الخاص بالجهاز وإضافته مرة أخرى.
acc_dev_checkServerPortTip=منفذ الخادم المكوّن لا يتوافق مع منفذ اتصال النظام ، مما قد يؤدي إلى فشل الإضافة. مواصلة العمل؟
acc_dev_clearAdmin=واضح إذن المسؤول
acc_dev_setDevSate=ضبط الجهاز في / خارج الحالة
acc_dev_sureToClear=هل أنت متأكد من محو أذونات مسؤول الجهاز؟
acc_dev_hostState=حالة الجهاز الرئيسي
acc_dev_regDeviceTypeTip=هذا الجهاز مقيد وغير مسموح به بشكل مباشر. يرجى الاتصال بمزود البرنامج!
acc_dev_extBoardType=I/O نوع المجلس
acc_dev_extBoardTip=بعد التكوين ، تحتاج إلى إعادة تشغيل الجهاز لتفعيله.
acc_dev_extBoardLimit=فقط {0} لوحة I / O من هذا النوع يمكن إضافتها إلى كل جهاز!
acc_dev_replace=استبدال الجهاز
acc_dev_replaceTip=بعد الاستبدال ، لن يعمل الجهاز القديم ، يرجى توخي الحذر!
acc_dev_replaceTip1=بعد الاستبدال ، يرجى إجراء عملية "مزامنة كافة البيانات" ؛
acc_dev_replaceTip2=الرجاء التأكد من أن طراز الجهاز البديل هو نفسه!
acc_dev_replaceTip3=يرجى التأكد من أن الجهاز البديل قد قام بتعيين نفس عنوان الخادم والمنفذ مثل الجهاز القديم!
acc_dev_replaceFail=نوع الجهاز غير متناسق ولا يمكن استبداله!
acc_dev_notApb=هذا الجهاز لا يمكن أن تحمل على الباب أو رأس القراءة المضادة للغواصات
acc_dev_upResourceFile=تحميل ملف الموارد
acc_dev_playOrder=تسلسل اللعب
acc_dev_setFaceServerInfo=تعيين الوجه الخلفية مقارنة المعلمات
acc_dev_faceVerifyMode=وجه نمط المقارنة
acc_dev_faceVerifyMode1=المقارنة المحلية
acc_dev_faceVerifyMode2=خلفية المقارنة
acc_dev_faceVerifyMode3=الأولوية المحلية
acc_dev_faceBgServerType=الوجه الخلفي نوع الخادم
acc_dev_faceBgServerType1=منصة البرمجيات والخدمات
acc_dev_faceBgServerType2=خدمة طرف ثالث
acc_dev_isAccessLogic=تمكين التحكم في الوصول التحقق المنطقي
#[5]门-其他关联的也复用此处
acc_door_entity=المدخل
acc_door_number=رقم المدخل
acc_door_name=اسم المدخل
acc_door_activeTimeZone=المنطقة الزمنية النشطة
acc_door_passageModeTimeZone=وضع مرور المنطقة الزمنية
acc_door_setPassageModeTimeZone=إعدادات المنطقة الزمنية لوضع المرور
acc_door_notPassageModeTimeZone=المنطقة الزمنية غير المقطوعة
acc_door_lockOpenDuration=إغلاق المدة المفتوحة
acc_door_entranceApbDuration=مدة الدخول المانعة للتجول
acc_door_sensor=مستشعر المدخل
acc_door_sensorType=نوع مستشعر المدخل
acc_door_normalOpen=مفتوح عادة
acc_door_normalClose=مغلق عادة
acc_door_sensorDelay=تأخير مستشعر المدخل
acc_door_closeAndReverseState=قم بتمكين إغلاق المدخل عند إغلاق المدخل
acc_door_hostOutState=حالة إدخال/إخراج الجهاز الرئيسي
acc_door_slaveOutState=حالة دخول/خروج الجهاز التابع
acc_door_inState=الدخول
acc_door_outState=الخروج
acc_door_requestToExit=وضع rex
acc_door_withoutUnlock=إغلاق
acc_door_unlocking=فتح
acc_door_alarmDelay=تأخير rex
acc_door_duressPassword=كلمة مرور بالضغط
acc_door_currentDoor=المدخل الحالي
acc_door_allDoorOfCurDev=جميع المداخل في الجهاز الحالي
acc_door_allDoorOfAllDev=جميع المداخل في جميع الأجهزة
acc_door_allDoorOfAllControlDev=جميع المداخل في جميع أجهزة التحكم
acc_door_allDoorOfAllStandaloneDev=جميع المداخل فى جميع الأجهزة المستقلة
acc_door_allWirelessLock=كافة الأقفال اللاسلكية
acc_door_max6BitInteger=العدد الصحيح 6 بت كحد أقصى
acc_door_direction=الاتجاه
acc_door_onlyInReader=قارئ الإدخال فقط
acc_door_bothInAndOutReader=أجهزة قراءة الدخول والخروج
acc_door_noDoor=يرجى إضافة المدخل
acc_door_nameRepeat=يتم تكرار أسماء المداخل
acc_door_duressPwdError=يجب ألا تكون كلمة مرور بالضغط هي نفسها كلمة المرور الشخصية.
acc_door_urgencyStatePwd=الرجاء إدخال عدد صحيح {0} بت!
acc_door_noDevOnline=لا يوجد جهاز متصل بالإنترنت، أو أن المدخل لا يدعم وضع التحقق من البطاقة.
acc_door_durationLessLock=يجب أن يكون تأخير استشعار المدخل أكبر من مدة إغلاق الفتح.
acc_door_lockMoreDuration=يجب أن تكون مدة إغلاق الفتح أقل من تأخير استشعار المدخل.
acc_door_lockAndExtLessDuration=يجب أن يكون مجموع مدة فتح الإغلاق وتأخير المرور أقل من تأخير مستشعر المدخل
acc_door_noDevTrigger=الجهاز لا يفي بالشروط!
acc_door_relay=تأخير
acc_door_pin=تثبيت
acc_door_selDoor=اختيار المدخل
acc_door_sensorStatus=مستشعر الباب ({0})
acc_door_sensorDelaySeconds=تأخير استشعار الباب ({0} s)
acc_door_timeSeg=النطاق الزمنى ({0})
acc_door_combOpenInterval=الفاصل الزمني للعمليات متعددة الأشخاص
acc_door_delayOpenTime=تأخير فتح المدخل
acc_door_extDelayDrivertime=تأخير المرور
acc_door_enableAudio=تفعيل التنبيه
acc_door_disableAudio=تعطيل أصوات المنبه
acc_door_lockAndExtDelayTip=مجموع فترة فتح الاغلاق وفترة تأخير المهلة لا يتجاوز 254 ثانية.
acc_door_disabled=لا يمكن تشغيل المدخلات المعطلة!
acc_door_offline=لا يمكن تشغيل المدخلات دون اتصال!
acc_door_notSupport=المدخلات التالية لا تدعم هذه الميزة!
acc_door_select=اختيار المدخل
acc_door_pushMaxCount=هناك أبواب٪ s ممكّنة في النظام وقد وصلت إلى حد الترخيص يرجى الاتصال بقسم المبيعات لدينا
acc_door_outNumber=يسمح لك الترخيص الحالي فقط بإضافة المزيد من المدخلات. يرجى إعادة تحديد المدخلات والمحاولة مرة أخرى، أو الاتصال بقسم المبيعات للحصول على ترخيص التحديث.
acc_door_latchTimeZone=النطاق الزمنى rex
acc_door_wgFmtReverse=عكس رقم البطاقة
acc_door_allowSUAccessLock=السماح الخارق الوصول عند تأمين
acc_door_verifyModeSinglePwd=كلمة السر لا يمكن استخدامها بشكل مستقل !
acc_door_doorPassword=فتح كلمة السر
#辅助输入
acc_auxIn_timeZone=النطاق الزمنى النشط
#辅助输出
acc_auxOut_passageModeTimeZone=النطاق الزمني لوضع المرور
acc_auxOut_disabled=لا يمكن تشغيل المخرج الإضافي المعطل!
acc_auxOut_offline=لا يمكن تشغيل المخرج الإضافي دون اتصال!
#[8]门禁权限组
acc_level_doorGroup=مزيج المدخلات
acc_level_openingPersonnel=فتح المدخل للموظفين
acc_level_noDoor=لا يوجد عنصر متوفر. يرجى إضافة الجهاز أولا.
acc_level_doorRequired=يجب تحديد المدخل
acc_level_doorCount=عدد المدخل
acc_level_doorDelete=حذف المدخل
acc_level_isAddDoor=هل تريد إضافة المدخلات فورا إلى مستوى التحكم في الوصول الحالي؟
acc_level_master=رئيسى
acc_level_noneSelect=يرجى إضافة مستوى تحكم بالوصول.
acc_level_useDefaultLevel=هل تريد تبديل مستوى الوصول إلى القسم الجديد؟
acc_level_persAccSet=إعدادات مراقبة وصول الموظفين
acc_level_visUsed={0} قيد الاستخدام بالفعل بواسطة وحدة الزائر ولا يمكن حذفه!
acc_level_doorControl=تحكم الباب
acc_level_personExceedMax=العدد الحالي من مجموعات الأذونات ( { 0 } ، والحد الأقصى لعدد من مجموعات الأذونات الاختيارية ( { 1 }
acc_level_exportLevel=تصدير مستوى الوصول
acc_level_exportLevelDoor=تصدير أبواب مستوى الوصول
acc_level_exportLevelPerson=تصدير مستوى الوصول للأفراد
acc_level_importLevel=استيراد مستوى الوصول
acc_level_importLevelDoor=استيراد أبواب مستوى الوصول
acc_level_importLevelPerson=استيراد أفراد بمستوى الوصول
acc_level_exportDoorFileName=معلومات الأبواب لمستوى الوصول
acc_level_exportPersonFileName=معلومات الموظفين لمستوى الوصول
acc_levelImport_nameNotNull=لا يمكن أن يكون اسم مستوى الوصول فارغًا
acc_levelImport_timeSegNameNotNull=لا يمكن أن تكون المنطقة الزمنية فارغة
acc_levelImport_areaNotExist=المنطقة غير موجودة!
acc_levelImport_timeSegNotExist=المنطقة الزمنية غير موجودة!
acc_levelImport_nameExist=اسم مستوى الوصول {0} موجود بالفعل!
acc_levelImport_levelDoorExist=أبواب مستوى الوصول {0} موجودة بالفعل!
acc_levelImport_levelPersonExist=أفراد مستوى الوصول {0} موجودون بالفعل!
acc_levelImport_noSpecialChar=لا يمكن أن يحتوي اسم مستوى الوصول على أحرف خاصة!
#[10]首人常开
acc_firstOpen_setting=إتاحة صيغة المتكلم
acc_firstOpen_browsePerson=تصفح الأفراد
#[11]多人组合开门
acc_combOpen_comboName=الاسم المركب
acc_combOpen_personGroupName=اسم المجموعة
acc_combOpen_personGroup=مجموعة متعددة الاشخاص
acc_combOpen_verifyOneTime=عدد الموظفين الحاليين
acc_combOpen_eachGroupCount=عدد الموظفين في كل مجموعة
acc_combOpen_group=المجموعة
acc_combOpen_changeLevel=مجموعة المدخلات
acc_combOpen_combDeleteGroup=مرجع مجموعة الفتح الحالي، يرجى الحذف أولا.
acc_combOpen_ownedLevel=المجموعات المنتمية
acc_combOpen_mostPersonCount=يجب ألا يزيد عدد المجموعات عن خمسة أشخاص.
acc_combOpen_leastPersonCount=يجب ألا يقل عدد المجموعات عن شخصين.
acc_combOpen_groupNameRepeat=اسم المجموعة مكرر.
acc_combOpen_groupNotUnique=يجب ألا تكون مجموعات الأشخاص ذوي المدخلات المفتوحة متطابقة.
acc_combOpen_persNumErr=يتجاوز عدد المجموعة القيمة الفعلية التي تختارها، يرجى إعادة التحديد
acc_combOpen_combOpengGroupPersonShort=بعد إزالة الأشخاص، لا يكفي عدد أفراد المجموعة المفتوحة لمدخل الموظفين، يرجى حذف المجموعة المفتوحة أولاً.
acc_combOpen_backgroundVerifyTip=المدخل موجود في الجهاز مع تمكين التحقق من الخلفية; لا يمكن استخدامه في الوقت نفسه مع مدخل مفتوح متعدد الأشخاص!
#[12]互锁
acc_interlock_rule=قاعدة الترابط
acc_interlock_mode1Or2=الانترلوك بين {0} و {1}
acc_interlock_mode3=الانترلوك بين {0} و {1} و {2}
acc_interlock_mode4=الانترلوك بين {0} و {1} أو بين {2} و {3}
acc_interlock_mode5=الانترلوك بين {0} و {1} و {2} و {3}
acc_interlock_hasBeenSet=بها إعدادات برنامج الترابط
acc_interlock_group1=المجموعة
acc_interlock_group2=المجموعة
acc_interlock_ruleInfo=مجموعة المتشابكة
acc_interlock_alreadyExists=هناك بالفعل نفس التعشيق القاعدة ، لا تضيف مرارا وتكرارا !
acc_interlock_groupInterlockCountErr=اثنين على الأقل من الأبواب المطلوبة في مجموعة قواعد التعشيق
acc_interlock_ruleType=التعشيق نوع القاعدة
#[13]反潜
acc_apb_rules=قاعدة مقاومة المرور
acc_apb_reader=مكافحة التراجع بين قراء الباب {0}
acc_apb_reader2=مكافحة السلبي بين قراء الأبواب: {0} ، {1}
acc_apb_reader3=مكافحة التراجع بين قراء الأبواب: {0} ، {1} ، {2}
acc_apb_reader4=مكافحة التراجع بين قراء الأبواب: {0} ، {1} ، {2} ، {3}
acc_apb_reader5=القارئ الخارجي المضاد للانعكاس على الباب {0}
acc_apb_reader6=قارئ مضاد للخطأ في الباب {0}
acc_apb_reader7=مقاومة المرور بين القراء من جميع المدخلات 4
acc_apb_twoDoor=مكافحة التراجع بين {0} و {1}
acc_apb_fourDoor=مكافحة التراجع بين {0} و {1}، مكافحة التراجع بين {2} و {3}
acc_apb_fourDoor2=منع التراجع بين {0} أو {1} و {2} أو {3}
acc_apb_fourDoor3=منع التراجع بين {0} و {1} أو {2}
acc_apb_fourDoor4=منع التراجع بين {0} و {1} أو {2} أو {3}
acc_apb_hasBeenSet=تم تعيينه إلى مضاد المرور
acc_apb_conflictWithGapb=يحتوي هذا الجهاز على إعدادات شاملة لمكافحة المرور، ولا يمكن تعديله!
acc_apb_conflictWithApb=تحتوي منطقة الجهاز على إعدادات مكافحة المرور، ولا يمكن أن تكونتم التعديل!
acc_apb_conflictWithEntranceApb=تحتوي منطقة الجهاز على إعدادات المدخل المضادة لكلمة المرور، ولا يمكن تعديلها!
acc_apb_controlIn=مضاد للمرور بالداخل
acc_apb_controlOut=مضاد للمرور بالخارج
acc_apb_controlInOut=مضاد للمرور بالداخل والخارج
acc_apb_groupIn=في المجموعة
acc_apb_groupOut=خارج المجموعة
acc_apb_reverseName=المضادة للغواصات
acc_apb_door=بوابة مضادة للغواصات
acc_apb_readerHead=قراءة رئيس المضادة للغواصات
acc_apb_alreadyExists=نفس القواعد المضادة للغواصات موجودة بالفعل ، لا تضيف مرارا وتكرارا !
#[17]电子地图
acc_map_addDoor=إضافة مدخل
acc_map_addChannel=إضافة كاميرا
acc_map_noAccess=لا يسمح بالوصول إلى وحدة الخريطة الإلكترونية، يرجى الاتصال بالمسؤول!
acc_map_noAreaAccess=ليس لديك إذن الخريطة الإلكترونية لهذه المنطقة ، يرجى الاتصال بالمسؤول!
acc_map_imgSizeError=يرجى تحميل الصورة التي حجمها أقل من {0}M!
#[18]门禁事件记录
acc_trans_entity=المعاملة
acc_trans_eventType=نوع الحدث
acc_trans_firmwareEvent=أحداث البرامج الثابتة
acc_trans_softwareEvent=حدث البرنامج
acc_trans_today=أحداث من اليوم
acc_trans_lastAddr=آخروضع معروف
acc_trans_viewPhotos=عرض الصور
acc_trans_exportPhoto=تصدير الصور
acc_trans_dayNumber=أيام
acc_trans_photo=صورة حادث التحكم في الوصول
acc_trans_fileIsTooLarge=الملف الذي تم تصديره كبير جدًا ، يُرجى تقليل النطاق للتصدير
#[19]门禁验证方式
acc_verify_mode_onlyface=الوجه
acc_verify_mode_facefp=الوجه + بصمة الإصبع
acc_verify_mode_facepwd=وجه + كلمة مرور
acc_verify_mode_facecard=وجه + بطاقة
acc_verify_mode_facefpcard=وجه + بصمة + بطاقة
acc_verify_mode_facefppwd=وجه + بصمة + كلمة مرور
acc_verify_mode_fv=وريد الاصبع
acc_verify_mode_fvpwd=وريد الإصبع + كلمة المرور
acc_verify_mode_fvcard=وريد الاصبع + البطاقة
acc_verify_mode_fvpwdcard=وريد الإصبع + كلمة المرور + البطاقة
acc_verify_mode_pv=كف، نخلة
acc_verify_mode_pvcard=الكف والبطاقة
acc_verify_mode_pvface=راحة اليد + الوجه
acc_verify_mode_pvfp=راحة اليد + بصمة الإصبع
acc_verify_mode_pvfacefp=كف + وجه + بصمة
#[20]门禁事件编号
acc_eventNo_-1=لا شيء
acc_eventNo_0=السحب العادي مفتوح
acc_eventNo_1=اسحب الشاشة أثناء النطاق الزمنى لوضع المرور
acc_eventNo_2=فتح عادي للبطاقة الأولى (تمرير البطاقة)
acc_eventNo_3=فتح متعدد الأشخاص (تمرير البطاقة)
acc_eventNo_4=كلمة مرور الطوارئ مفتوحة
acc_eventNo_5=فتح أثناء النطاق الزمنى لوضع المرور
acc_eventNo_6=تم تشغيل حدث الوصلة
acc_eventNo_7=إلغاء التنبيه
acc_eventNo_8=الفتح عن بعد
acc_eventNo_9=الإغلاق عن بعد
acc_eventNo_10=قم بإلغاء تفعيل المنطقة الزمنية لوضع المرور خلال اليوم
acc_eventNo_11=تمكين المنطقة الزمنية لوضع المرور خلال اليوم
acc_eventNo_12=يتم فتح المخرج الإضافي عن بعد
acc_eventNo_13=يتم إغلاق المخرج الإضافي عن بعد
acc_eventNo_14=بصمة الإصبع العادية مفتوحة
acc_eventNo_15=متعدد الأشخاص مفتوح (بصمة الإصبع)
acc_eventNo_16=اضغط على (بصمة الإصبع) أثناء المنطقة الزمنية لوضع المرور
acc_eventNo_17=فتح البطاقة بالإضافة إلى بصمة الإصبع
acc_eventNo_18=فتح عادي للبطاقة الأولى (اضغط بصمة الإصبع)
acc_eventNo_19=فتح البطاقة الأولى بشكل عادي (البطاقة بالإضافة إلى بصمة الإصبع)
acc_eventNo_20=فترة التشغيل قصيرة جدا
acc_eventNo_21=المنطقة الزمنية غير النشطة للمدخل (تمرير البطاقة)
acc_eventNo_22=نطاق زمنى غير قانونى
acc_eventNo_23=رفض الوصول
acc_eventNo_24=مانع الحركة العكسية
acc_eventNo_25=الترابط
acc_eventNo_26=مصادقة متعددة الأشخاص (تمرير البطاقة)
acc_eventNo_27=بطاقة معطلة
acc_eventNo_28=مهلة فتح المدخل لفترة طويلة
acc_eventNo_29=بطاقة انتهت صلاحيتها
acc_eventNo_30=كلمة مرور خاطئة
acc_eventNo_31=اضغط الفاصل الزمني لبصمات الأصابع لفترة قصيرة جدًا
acc_eventNo_32=مصادقة متعددة الأشخاص (بصمة الاصبع)
acc_eventNo_33=بصمة الاصابع منتهية الصلاحية
acc_eventNo_34=تم إلغاء تفعيل البصمة
acc_eventNo_35=المنطقة الزمنية غير النشطة للمدخل (اضغط على البصمة)
acc_eventNo_36=المنطقة الزمنية غير النشطة للمدخل (اضغط على زر EXIT (الخروج))
acc_eventNo_37=فشل الإغلاق أثناء المنطقة الزمنية لوضع المرور
acc_eventNo_38=تم الإبلاغ عن فقدان البطاقة
acc_eventNo_39=الوصول معطل
acc_eventNo_40=فشلت المصادقة متعددة الأشخاص(اضغط على البصمة)
acc_eventNo_41=تحقق من خطأ في الوضع
acc_eventNo_42=خطأ في تنسيق Wiegand
acc_eventNo_43=مهلة التحقق من عدم المرور
acc_eventNo_44=فشل التحقق من الخلفية
acc_eventNo_45=مهلة التحقق من الخلفية
acc_eventNo_47=فشل إرسال الأمر
acc_eventNo_48=فشلت المصادقة متعددة الأشخاص (اسحب البطاقة
acc_eventNo_49=المنطقة الزمنية غير النشطة للمدخل (كلمة المرور)
acc_eventNo_50=اضغط على الفاصل الزمني لكلمة المرور قصير جدا
acc_eventNo_51=مصادقة متعددة الأشخاص (كلمة المرور)
acc_eventNo_52=فشلت المصادقة متعددة الأشخاص(كلمة المرور)
acc_eventNo_53=تم انتهاء صلاحية كلمة المرور
acc_eventNo_100=العبث بالإنذار
acc_eventNo_101=كلمة المرور المفتوحة بالضغط
acc_eventNo_102=فتح المدخل بقوة
acc_eventNo_103=بصمة الإصبع بالضغط مفتوحة
acc_eventNo_200=المدخل مفتوح بشكل صحيح
acc_eventNo_201=المدخل مغلق بشكل صحيح
acc_eventNo_202=زر EXIT (الخروج) مفتوح
acc_eventNo_203=فتح متعدد الأشخاص (بطاقة زائد بصمة)
acc_eventNo_204=انتهت المنطقة الزمنية لوضع المرور
acc_eventNo_205=الفتح العادي عن بعد
acc_eventNo_206=تم بدء تشغيل الجهاز
acc_eventNo_207=فتح بكلمة المرور
acc_eventNo_208=فتح المستخدم الفائق للمداخل
acc_eventNo_209=تم تشغيل زر EXIT (الخروج) (بدون إلغاء القفل)
acc_eventNo_210=بدأ تشغيل باب الحريق
acc_eventNo_211=أغلاق المستخدم الفائق الأبواب
acc_eventNo_212=تمكين وظيفة التحكم في المصعد
acc_eventNo_213=تعطيل وظيفة التحكم في المصعد
acc_eventNo_214=فتح متعدد الأشخاص (كلمة المرور)
acc_eventNo_215=فتح البطاقة العادية الأولى (كلمة المرور)
acc_eventNo_216=كلمة المرور أثناء المنطقة الزمنية لوضع المرور
acc_eventNo_220=م فصل الإدخال الإضافي (مفتوح)
acc_eventNo_221=تم تقصير الإدخال الإضافي (مغلق)
acc_eventNo_222=تم التحقق من الخلفية بنجاح
acc_eventNo_223=التحقق من الخلفية
acc_eventNo_225=إدخال الأجهزة الإضافية عادي
acc_eventNo_226=مشغل الإدخال الإضافي
acc_newEventNo_0=التحقق العادي مفتوح
acc_newEventNo_1=تحقق أثناء المنطقة الزمنية لوضع المرور
acc_newEventNo_2=فتح بصيغة المتكلم
acc_newEventNo_3=فتح متعدد الافراد
acc_newEventNo_20=الفاصل الزمني للتشغيل قصير للغاية
acc_newEventNo_21=فتح التحقق من المنطقة الزمنية غير النشطة للباب
acc_newEventNo_26=انتظار المصادقة متعددة الأفراد
acc_newEventNo_27=موظفون غير مسجلين
acc_newEventNo_29=انتهت صلاحية دخول الموظفين
acc_newEventNo_30=كلمة المرور غير صحيحة
acc_newEventNo_41=تحقق من خطأ في الوضع
acc_newEventNo_43=قفل الموظفين
acc_newEventNo_44=فشل التحقق من الخلفية
acc_newEventNo_45=انتهت مهلة التحقق من الخلفية
acc_newEventNo_48=فشل التحقق من الأفراد متعددين
acc_newEventNo_54=قوة البطارية منخفضة جدا
acc_newEventNo_55=استبدل البطارية على الفور
acc_newEventNo_56=تشغيل غير قانونى
acc_newEventNo_57=كهرباء احتياطية
acc_newEventNo_58=فتح الإنذار بشكل طبيعي
acc_newEventNo_59=إدارة غير قانونية
acc_newEventNo_60=تم قفل الباب من الداخل
acc_newEventNo_61=تكرار
acc_newEventNo_62=حظر المستخدمين
acc_newEventNo_63=الباب مغلق
acc_newEventNo_64=المنطقة الزمنية لزر الخروج غير النشط
acc_newEventNo_65=المنطقة الزمنية لإدخال إضافي غير نشط
acc_newEventNo_66=فشلت تطوير القارئ
acc_newEventNo_67=نجاح المقارنة عن بعد (المعدات غير مصرح بها)
acc_newEventNo_68=ارتفاع درجة حرارة الجسم - تم رفض الوصول
acc_newEventNo_69=بدون قناع - تم رفض الوصول
acc_newEventNo_70=تتصل نسبة الوجه بشكل غير طبيعي بالخادم.
acc_newEventNo_71=استجاب خادم الوجه بشكل غير طبيعي.
acc_newEventNo_73=رمز الاستجابة السريعة غير صالح
acc_newEventNo_74=انتهت صلاحية رمز الاستجابة السريعة
acc_newEventNo_101=فتح الانذار بالضغط
acc_newEventNo_104=منبه تمرير البطاقة غير صالح
acc_newEventNo_105=تعذر الاتصال بالخادم
acc_newEventNo_106=إيقاف التشغيل الرئيسي
acc_newEventNo_107=إيقاف تشغيل البطارية
acc_newEventNo_108=يتعذر الاتصال بالجهاز الرئيسي
acc_newEventNo_109=قارئ العبث إنذار
acc_newEventNo_110=قارئ غير متصل
acc_newEventNo_112=توسيع المجلس حاليا
acc_newEventNo_114=إنذار الحريق المدخلات قطع ( خط الكشف )
acc_newEventNo_115=إنذار الحريق المدخلات قصيرة الدوائر ( خط الكشف )
acc_newEventNo_116=مساعدة المدخلات قطع ( خط الكشف )
acc_newEventNo_117=مساعدة المدخلات قصيرة الدوائر ( خط الكشف )
acc_newEventNo_118=الخروج التبديل قطع ( خط الكشف )
acc_newEventNo_119=دارة قصيرة من الخروج التبديل ( خط الكشف )
acc_newEventNo_120=الباب المغناطيسي قطع ( خط الكشف )
acc_newEventNo_121=بوابة قصيرة الدوائر المغناطيسية ( خط الكشف )
acc_newEventNo_159=جهاز التحكم عن بعد لفتح الباب
acc_newEventNo_214=متصل بالخادم
acc_newEventNo_217=تم الاتصال بالجهاز الرئيسي بنجاح
acc_newEventNo_218=التحقق من بطاقة الهوية
acc_newEventNo_222=نجاح التحقق من الخلفية
acc_newEventNo_223=التحقق من الخلفية
acc_newEventNo_224=الضغط على الجرس
acc_newEventNo_227=فتح الباب مرتين
acc_newEventNo_228=غلق الباب مرتين
acc_newEventNo_229=تم فتح وقت الإخراج الإضافي بشكل طبيعي
acc_newEventNo_230=إغلاق مؤقت للمخرج الإضافي
acc_newEventNo_232=تحقق من النجاح
acc_newEventNo_233=تفعيل القفل السفلى
acc_newEventNo_234=تعطيل القفل السفلى
acc_newEventNo_235=نجاح تطوير القارىء
acc_newEventNo_236=القارئ العبث إنذار مسح
acc_newEventNo_237=القارئ على الانترنت
acc_newEventNo_239=اتصال الجهاز
acc_newEventNo_240=تم إنهاء المكالمة
acc_newEventNo_243=إنذار الحريق قطع الإدخال
acc_newEventNo_244=إنذار الحريق مدخلات قصيرة
acc_newEventNo_247=توسيع المجلس على الانترنت
acc_newEventNo_4008=انتعاش التيار الكهربائي
acc_newEventNo_4014=النار إشارة الإدخال هو قطع ، نهاية الباب عادة مفتوحة
acc_newEventNo_4015=الباب على الانترنت
acc_newEventNo_4018=مقارنة الخلفية مفتوحة
acc_newEventNo_5023=حالة الحريق محدودة
acc_newEventNo_5024=التحقق من صحة مهلة متعددة
acc_newEventNo_5029=فشلت مقارنة الخلفية
acc_newEventNo_6005=وصلت سعة التسجيل إلى الحد الأقصى
acc_newEventNo_6006=دارة قصيرة ( RS485 )
acc_newEventNo_6007=دارة قصيرة ( ويغان )
acc_newEventNo_6011=الباب غير متصل
acc_newEventNo_6012=الباب غير متصل
acc_newEventNo_6013=النار إشارة الإدخال الزناد ، فتح الباب في كثير من الأحيان
acc_newEventNo_6015=إعادة توسيع جهاز السلطة
acc_newEventNo_6016=استعادة إعدادات المصنع
acc_newEventNo_6070=مقارنة الخلفية (القائمة المحظورة)
acc_eventNo_undefined=رقم الحدث غير محدد
acc_advanceEvent_500=الحماية الشاملة من المرور (المنطقي)
acc_advanceEvent_501=توفر الشخص(استخدام التاريخ)
acc_advanceEvent_502=عدد عناصر التحكم
acc_advanceEvent_503=الترابط العالمى
acc_advanceEvent_504=التحكم فى الطريق
acc_advanceEvent_505=منع الدخول العام (بوقت محدد)
acc_advanceEvent_506=منع الدخول العام (منطقي بوقت محدد)
acc_advanceEvent_507=توافر الشخص(بعد أول استخدام لأيام صالحة)
acc_advanceEvent_508=توافر الشخص(عدد مرات الاستخدام)
acc_advanceEvent_509=فشل التحقق من الخلفية (موظفون غير مسجلين)
acc_advanceEvent_510=فشل التحقق من الخلفية (استثناء البيانات)
acc_alarmEvent_701=انتهاك DMR (قواعد الإعداد: {0})
#[21]实时监控
acc_rtMonitor_openDoor=فتح
acc_rtMonitor_closeDoor=إغلاق
acc_rtMonitor_remoteNormalOpen=الفتح الطبيعي عن بعد
acc_rtMonitor_realTimeEvent=الأحداث في الوقت الحقيقي
acc_rtMonitor_photoMonitor=مراقبة الصور
acc_rtMonitor_alarmMonitor=مراقبة الانذار
acc_rtMonitor_doorState=حالة الباب
acc_rtMonitor_auxOutName=اسم المخرج الإضافي
acc_rtMonitor_nonsupport=غير مدعوم
acc_rtMonitor_lock=مقفل
acc_rtMonitor_unLock=غير مقفل
acc_rtMonitor_disable=معطل
acc_rtMonitor_noSensor=لا يوجد مستشعر على الباب
acc_rtMonitor_alarm=الانذار
acc_rtMonitor_openForce=تم الفتح بقوة
acc_rtMonitor_tamper=العبث بالانذار
acc_rtMonitor_duressPwdOpen=الفتح بضغط كلمة المرور
acc_rtMonitor_duressFingerOpen=الفتح بضغط بصمة الاصبع
acc_rtMonitor_duressOpen=فتح بالضغط
acc_rtMonitor_openTimeout=انتهاء وقت الفتح
acc_rtMonitor_unknown=غير معلوم
acc_rtMonitor_noLegalDoor=لا يوجد باب يفي بالحالة.
acc_rtMonitor_noLegalAuxOut=لا يوجد مخرج إضافي يفي بالحالة!
acc_rtMonitor_curDevNotSupportOp=لا تدعم حالة الجهاز الحالية هذه العملية
acc_rtMonitor_curNormalOpen=مفتوح حاليا بشكل عادي
acc_rtMonitor_whetherDisableTimeZone=الحالة الحالية للباب مفتوحة دائما.
acc_rtMonitor_curSystemNoDoors=لم يضف النظام الحالي أي باب أو لا يمكنه العثور على أي باب يلبي متطلباتك.
acc_rtMonitor_cancelAlarm=الغاء الانذار
acc_rtMonitor_openAllDoor=فتح جميع الأبواب الحالية
acc_rtMonitor_closeAllDoor=غلق جميع الأبواب الحالية
acc_rtMonitor_confirmCancelAlarm=هل أنت متأكد من إلغاء هذا التنبيه؟
acc_rtMonitor_calcelAllDoor=الغاء جميع انواع الانذار
acc_rtMonitor_initDoorStateTip=الحصول على جميع الأبواب المصرح بها للمستخدمين في النظام ...
acc_rtMonitor_alarmEvent=انذار الحدث
acc_rtMonitor_ackAlarm=التعرف
acc_rtMonitor_ackAllAlarm=التعرف على الكل
acc_rtMonitor_ackAlarmTime=وقت التعرف
acc_rtMonitor_sureToAckThese=هل أنت متأكد من تأكيد هذا التنبيه {0}؟ بعد التأكيد، سيتم إلغاء جميع الإنذارات.
acc_rtMonitor_sureToAckAllAlarm=هل أنت متأكد من تأكيد هذا التنبيه {0}؟ بعد التأكيد، سيتم إلغاء جميع الإنذارات.
acc_rtMonitor_noSelectAlarmEvent=يرجى اختيار تأكيد حدث التنبيه
acc_rtMonitor_noAlarmEvent=لا توجد أحداث إنذار في النظام الحالي!
acc_rtMonitor_forcefully=إلغاء الإنذار (الباب مفتوح بقوة)
acc_rtMonitor_addToRegPerson=تمت الإضافة إلى الشخص المسجل
acc_rtMonitor_cardExist=تم تعيين هذه البطاقة من قبل {0} ولا يمكن إصدارها مرة أخرى.
acc_rtMonitor_opResultPrompt=أرسل طلب {0} بنجاح ، فشل {1}.
acc_rtMonitor_doorOpFailedPrompt=فشل إرسال الطلبات إلى الأبواب التالية، يرجى إعادة المحاولة!
acc_rtMonitor_remoteOpen=فتح عن بعد
acc_rtMonitor_remoteClose=إغلاق عن بعد
acc_rtMonitor_alarmSoundClose=الصوت مغلق
acc_rtMonitor_alarmSoundOpen=الصوت مفتوح
acc_rtMonitor_playAudio=أصوات تذكير
acc_rtMonitor_isOpenShowPhoto=تمكين وظيفة عرض الصور
acc_rtMonitor_isOpenPlayAudio=تمكين وظيفة تنبيه بالصوت
acc_rtm_open=افتح الزر عن بعد
acc_rtm_close=غلق الزر عن بعد
acc_rtm_eleModule=المصعد
acc_cancelAlarm_fp=إلغاء التنبيه (فتح ببصمة الإصبع بالضغط)
acc_cancelAlarm_pwd=إلغاء التنبيه (فتح بكلمة المرور بالضغط)
acc_cancelAlarm_timeOut=إلغاء التنبيه (مهلة فتح الباب لفترة طويلة)
#定时同步设备时间
acc_timing_syncDevTime=وقت مزامنة الجهاز
acc_timing_executionTime=وقت التنفيذ
acc_timing_theLifecycle=الدورة الحياتية
acc_timing_errorPrompt=الإدخال خاطئ.
acc_timing_checkedSyncTime=يرجى تحديد وقت المزامنة
#[25]门禁报表
acc_trans_hasAccLevel=الحصول على مستوى للوصول
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=يرجى إضافة نطاق
acc_zone_code=رمز النطاق
acc_zone_parentZone=النطاق الرئيسي
acc_zone_parentZoneCode=رمز النطاق الرئيسى
acc_zone_parentZoneName=اسم النطاق الرئيسى
acc_zone_outside=بالخارج
#[G2]读头定义
acc_readerDefine_readerName=اسم القارىء
acc_readerDefine_fromZone=يمتد من
acc_readerDefine_toZone=يمتد إلى
acc_readerDefine_delInfo1=تتم الإشارة إلى منطقة تعريف القارئ بواسطة وظيفة متقدمة للتحكم في الوصول ولا يمكن حذفها!
acc_readerDefine_selReader=اختيار القارىء
acc_readerDefine_selectReader=برجاء إضافة القارىء
acc_readerDefine_tip=بعد وصول الموظفين خارج المنطقة، سيتم حذف سجل الموظفين.
#[G3]全局反潜
acc_gapb_zone=نطاق
acc_gapb_whenToResetGapb=إعادة ضبط وقت مقاومة المرور
acc_gapb_apbType=نوع مضاد المرور
acc_gapb_logicalAPB=مقاوم مرور منطقي
acc_gapb_timedAPB=توقيت منع المرور
acc_gapb_logicalTimedAPB=توقيت منطقى لمنع المرور
acc_gapb_lockoutDuration=فترة الغلق
acc_gapb_devOfflineRule=اذا كان الجهاز غير متصل
acc_gapb_standardLevel=مستوى الوصول القياسي
acc_gapb_accessDenied=الدخول مرفوض
acc_gapb_doorControlZone=تتحكم الأبواب التالية في الوصول إلى داخل المنطقة وخارجها
acc_gapb_resetStatus=إعادة تعيين حالة مقاومة المرور
acc_gapb_obeyAPB=التزم بقواعد مقاومةالمرور
acc_gapb_isResetGAPB=أعد ضبط نظام مقاومة المرور
acc_gapb_resetGAPBSuccess=إعادة تعيين حالة مقاومة المرور بنجاح
acc_gapb_resetGAPBFaile=فشلت إعادة تعيين حالة مقاومةالمرور
acc_gapb_chooseArea=يرجى إعادة تحديد المنطقة.
acc_gapb_notDelInfo1=يشار إلى منطقة الوصول كمنطقة وصول فائقة ولا يمكن حذفها.
acc_gapb_notDelInfo2=تتم الإشارة إلى المنطقة من قبل القارئ المحدد ولا يمكن حذفها.
acc_gapb_notDelInfo3=تتم الإشارة إلى المنطقة بواسطة وظائف التحكم في الوصول المتقدمة ولا يمكن حذفها.
acc_gapb_notDelInfo4=The access area is referenced by the LED and cannot be deleted!
acc_gapb_zoneNumRepeat=تحتوي أرقام مجال التحكم على نسخ مكررة
acc_gapb_zoneNameRepeat=أسماء مجالات التحكم لها نسخ مكررة
acc_gapb_personResetGapbPre=هل تريد بالتأكيد إعادة تعيين هذا؟
acc_gapb_personResetGapbSuffix=ما هى القواعد التى يتعداها الشخص لعدم إماكنيته من المرور
acc_gapb_apbPrompt=لا يمكن استخدام باب واحد للتحكم في حدودي محيط مستقلتين نسبيا.
acc_gapb_occurApb=حدوث مقاومة المرور
acc_gapb_noOpenDoor=لا تفتح الباب
acc_gapb_openDoor=افتح الباب
acc_gapb_zoneNumLength=الطول أكبر من 20 رمز
acc_gapb_zoneNameLength=الطول أكبر من 30 رمز
acc_gapb_zoneRemarkLength=الطول أكبر من 50 رمز
acc_gapb_isAutoServerMode=تم الكشف عن الجهاز بدون وظيفة التحقق من الخلفية، مما قد يؤثر على الوظيفة. افتحه الآن؟
acc_gapb_applyTo=يتقدم إلى
acc_gapb_allPerson=كل العاملين
acc_gapb_justSelected=موظفون مختارون للتو
acc_gapb_excludeSelected=استثناء الموظفين المحددين من بالداخل
#[G4]who is inside
acc_zoneInside_lastAccessTime=اخر توقيت للوصول
acc_zoneInside_lastAccessReader=اخر توقيت للقارىء
acc_zoneInside_noPersonInZone=لا يوجد اى شخص فى المنطقة
acc_zoneInside_noRulesInZone=لا يوجد وضع للقواعد
acc_zoneInside_totalPeople=المجموع الكلى للاشخاص
acc_zonePerson_selectPerson=يرجى اختيار شخص أو قسم!
#[G5]路径
acc_route_name=اسم الطريق
acc_route_setting=إعدادات الطريق
acc_route_addReader=إضافة قارىء
acc_route_delReader=إالغاء القارىء
acc_route_defineReaderLine=تحديد خط القارىء
acc_route_up=اعلى
acc_route_down=اسفل
acc_route_selReader=تحديد القارىء بعد التشغيل
acc_route_onlyOneOper=يمكن اختيار قارئ واحد فقط للتشغيل
acc_route_readerOrder=تسلسل محدد بواسطة القارئ
acc_route_atLeastSelectOne=يرجى اختيار قارئ واحد على الأقل!
acc_route_routeIsExist=هذا المسار موجود بالفعل!
#[G6]DMR
acc_dmr_residenceTime=فترة بقاء
acc_dmr_setting=إعدادات DMR
#[G7] الإشغال
acc_occupancy_max=القدرة القصوى
acc_occupancy_min=الحد الأدنى من القدرة
acc_occupancy_unlimit=غير محدد
acc_occupancy_note=يجب أن تكون السعة القصوى أكبر من الحد الأدنى للسعة!
acc_occupancy_containNote=يرجى تعيين واحد على الأقل من الحد الأقصى / الحد الأدنى للسعة!
acc_occupancy_maxMinValid=يرجى ضبط السعة أكبر من 0.
acc_occupancy_conflict=تم تعيين قواعد التحكم في الإشغال في هذه المنطقة.
acc_occupancy_maxMinTip=لا توجد قيمة للسعة تعني عدم وجود قيود.
#توافر البطاقة
acc_personLimit_zonePropertyName=اسم خاصية المنطقة
acc_personLimit_useType=الاستخدام
acc_personLimit_userDate=التاريخ الصحيح
acc_personLimit_useDays=بعد أول استخدام لأيام صالحة
acc_personLimit_useTimes=استخدام عدد المرات
acc_personLimit_setZoneProperty=تعيين خصائص المنطقة
acc_personLimit_zoneProperty=خصائص المنطقة
acc_personLimit_availabilityName=اسم التوفر
acc_personLimit_days=الايام
acc_personLimit_Times=التوقيتات
acc_personLimit_noDel=تتم الإشارة إلى خصائص منطقة التحكم في الوصول المحددة ولا يمكن حذفها!
acc_personLimit_cannotEdit=تتم الإشارة إلى سمة منطقة الوصول، ولا يمكن تعديلها!
acc_personLimit_detail=التفاصيل
acc_personLimit_userDateTo=صالحة حتى
acc_personLimit_addPersonRepeatTip=تمت إضافة الشخص تحت القسم المحدد إلى سمة منطقة التحكم في الوصول ، يرجى تحديد القسم مرة أخرى!
acc_personLimit_leftTimes={0} عدد المرات المتبقية
acc_personLimit_expired=منتهية الصلاحية
acc_personLimit_unused=غير مستعمل
#全局互锁
acc_globalInterlock_addGroup=إضافة مجموعة
acc_globalInterlock_delGroup=إلغاء مجموعة
acc_globalInterlock_refuseAddGroupMessage=لا يمكن تكرار نفس القفل الداخلي للباب داخل المجموعة التي تمت إضافتها
acc_globalInterlock_refuseAddlockMessage=ظهر الباب المضاف في مجموعات أخرى من أجهزة الترابط
acc_globalInterlock_refuseDeleteGroupMessage=يرجى إزالة البيانات المتعلقة بالترابط
acc_globalInterlock_isGroupInterlock=مجموعة الترابط
acc_globalInterlock_isAddTheDoorImmediately=أضف الباب على الفور
acc_globalInterlock_isAddTheGroupImmediately=أضف المجموعة على الفور
#门禁参数设置
acc_param_autoEventDev=قم بتنزيل سجل الأحداث الخاص بعدد الأجهزة المتزامنة تلقائيًا
acc_param_autoEventTime=قم بتنزيل سجل الأحداث للفواصل الزمنية المتزامنة تلقائيًا
acc_param_noRepeat=لا يسمح بتكرار عناوين البريد الإلكتروني، يرجى ملء البيانات مرة أخرى.
acc_param_most18=أضف حتى 18 عنوان بريد إلكتروني
acc_param_deleteAlert=لا يمكن حذف كل مربع إدخال عنوان البريد الإلكتروني
acc_param_invalidOrRepeat=خطأ في تنسيق عنوان البريد الإلكتروني أو تكرار عنوان البريد الإلكتروني.
#全局联动
acc_globalLinkage_noSupport=لا يدعم الباب المحدد حاليًا تأمين وظائف إلغاء التأمين.
acc_globalLinkage_trigger=الزناد العالمية الربط
acc_globalLinkage_noAddPerson=لا تحتوي قاعدة الربط على مشغل متعلق بالموظفين، افتراضيًا، ينطبق على جميع الموظفين!
acc_globalLinkage_selectAtLeastOne=الرجاء تحديد عملية تشغيل ربط واحدة على الأقل!
acc_globalLinkage_selectTrigger=يرجى إضافة شروط تشغيل الوصلة!
acc_globalLinkage_selectInput=يرجى إضافة نقطة الإدخال!
acc_globalLinkage_selectOutput=يرجة إضافة نقطة الاخراج
acc_globalLinkage_audioRemind=المطالبات الصوتية للوصلة
acc_globalLinkage_audio=الربط الصوتي
acc_globalLinkage_isApplyToAll=يتم تطبيقه على كل العاملين
acc_globalLinkage_scope=نطاق الموظفين
acc_globalLinkage_everyPerson=اى منهم
acc_globalLinkage_selectedPerson=تم الاختيار
acc_globalLinkage_noSupportPerson=غير مدعوم
acc_globalLinkage_reselectInput=تم تغيير نوع ظروف التشغيل، يرجى إعادة تحديد نقطة الإدخال!
acc_globalLinkage_addPushDevice=يرجى إضافة دعم لوظيفة الجهاز هذه
#其他
acc_InputMethod_tips=يرجى التبديل إلى وضع الإدخال باللغة الإنجليزية!
acc_device_systemCheckTip=جهاز الوصول غير موجود!
acc_notReturnMsg=لم يتم إرجاع أي معلومات
acc_validity_period=لقد مر الترخيص بالفترة السارية ؛ وظيفة لا يمكن أن تعمل.
acc_device_pushMaxCount=النظام موجود بالفعل في {0} الجهاز ، والوصول إلى حد الترخيص ، لا يمكن إضافة الجهاز!
acc_device_videoHardwareLinkage=ضبط مكونات وصلة الفيديو
acc_device_videoCameraIP=كاميرا الفيديو ip
acc_device_videoCameraPort=منفذ كاميرا الفيديو
acc_location_unable=لم تتم إضافة نقطة الحادث إلى الخريطة الإلكترونية، تعذر تحديد الموقع المحدد!
acc_device_wgDevMaxCount=وصل الجهاز إلى حد الترخيص في النظام ولا يمكنه تعديل الإعدادات!
#自定义报警事件
acc_deviceEvent_selectSound=يرجى تحديد ملفات الصوت
acc_deviceEvent_batchSetSoundErr=أصوات التنبيه التي تم ضبطها بشكل جماعي غير طبيعية
acc_deviceEvent_batchSet=ضبط الصوت
acc_deviceEvent_sound=حدوث الصوت
acc_deviceEvent_exist=موجود بالفعل
acc_deviceEvent_upload=تحميل
#查询门最近发生事件
acc_doorEventLatestHappen=الاستعلام عن الأحداث الأخيرة عن الباب
#门禁人员信息
acc_pers_delayPassage=تأخير المرور
#设备容量提示
acc_dev_usageConfirm=السعة الحالية التي تزيد على 90% من المعدات
acc_dev_immediateCheck=تحقق فورا
acc_dev_inSoftware=في مجال البرمجيات
acc_dev_inFirmware=في البرنامج الثابت
acc_dev_get=الحصول
acc_dev_getAll=الحصول على الكل
acc_dev_loadError=فشل التحميل
#Reader
acc_reader_inout=الداخل / الخارج
acc_reader_lightRule=قواعد الضوء
acc_reader_defLightRule=القاعدة الافتراضية
acc_reader_encrypt=تشفير
acc_reader_allReaderOfCurDev=جميع أجهزة القراءة في الجهاز الحالي
acc_reader_tip1=يتم نسخ التشفير إلى كافة أجهزة القراءة في الجهاز الحالي!
acc_reader_tip2=خيار وضع الهوية متاح فقط للقراء الذين يدعمون هذه الميزة!
acc_reader_tip3=يتم نسخ نوع بروتوكول RS485 إلى جميع القراء في الجهاز الحالي. ستسري الإعدادات بعد إعادة تشغيل الجهاز!
acc_reader_tip4=يتم نسخ خيار إخفاء بعض المعلومات الشخصية إلى جميع قراء نفس الجهاز افتراضيًا!
acc_reader_commType=نوع الاتصالات
acc_reader_commAddress=عنوان الاتصالات
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=ويغان
acc_readerCommType_wg485=RS485/ ويغان
acc_readerCommType_disable=معطل
acc_readerComAddress_repeat=عنوان اتصال مكرر
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=عنوان RS485
acc_readerCommType_wgAddress=عنوان ويغان
acc_reader_macError=يرجى إدخال عنوان MAC بالتنسيق الصحيح!
acc_reader_machineType=نوع القارىء
acc_reader_readMode=الوضع
acc_reader_readMode_normal=الوضع الطبيعى
acc_reader_readMode_idCard=وضع بطاقة الهوية
acc_reader_note=نصائح: لا يمكن تحديد سوى أجهزة التحكم في نفس المنطقة ({0}) التي يمكن تحديد الكاميرا.
acc_reader_rs485Type=نوع بروتوكول RS485
acc_reader_userLock=قفل وصول الموظفين
acc_reader_userInfoReveal=إخفاء جزء المعلومات الشخصية
#operat
acc_operation_pwd=كلمة المرور الخاصة بالتشغيل
acc_operation_pwd_error=كلمة مرور غير صحيحة
acc_new_input_not_same=إدخال المفتاح الجديد غير متسق
acc_op_set_keyword=إعدادات مفتاح الترخيص
acc_op_old_key=مفتاح قديم
acc_op_new_key=مفتاح جديد
acc_op_cofirm_key=تأكيد المفتاح
acc_op_old_key_error=مفتاح قديم غير صحيح
#验证方式规则
acc_verifyRule_name=اسم القاعدة
acc_verifyRule_door=التحقق من الباب
acc_verifyRule_person=التحقق من العاملين
acc_verifyRule_copy=نسخ إعدادات الاثنين إلى باقي أيام الأسبوع:
acc_verifyRule_tip1=يرجى تحديد وضع تحقق واحد على الأقل!
acc_verifyRule_tip2=إذا كانت القاعدة تحتوي على وضع التحقق من شخص ما، فيمكنك فقط إضافة باب باستخدام قارئ ويغان!
acc_verifyRule_tip3=قارئ RS485 يمكن أن يتبع فقط وضع التحقق من الباب، لا يدعم وضع التحقق من الموظفين.
acc_verifyRule_oldVerifyMode=وضع التحقق القديم
acc_verifyRule_newVerifyMode=وضع التحقق الجديد
acc_verifyRule_newVerifyModeSelectTitle=حدد طريقة تحقق جديدة
acc_verifyRule_newVerifyModeNoSupportTip=لا يوجد جهاز يدعم طريقة التحقق الجديدة!
#Wiegand Test
acc_wiegand_beforeCard=طول البطاقة الجديدة ({0} بت) لا يساوي البطاقة السابقة!
acc_wiegand_curentCount=طول رقم البطاقة الحالية: {0} بت
acc_wiegand_card=بطاقة
acc_wiegand_readCard=اقرأ البطاقة
acc_wiegand_clearCardInfo=مسح معلومات البطاقة
acc_wiegand_originalCard=الرقم البطاقة الاصلية
acc_wiegand_recommendFmt=تنسيق البطاقة الموصى به
acc_wiegand_parityFmt=تنسيق التماثل الفردى الزوجي
acc_wiegand_withSizeCode=حساب رمز الموقع تلقائيا عندما يكون رمز الموقع فارغا
acc_wiegand_tip1=قد لا تنتمي هذه البطاقات إلى نفس مجموعة البطاقات
acc_wiegand_tip2=رمز الموقع: {0} ، رقم البطاقة: {1} ، فشل في مطابقة رقم البطاقة الأصلي. يرجى التحقق من رمز الموقع إدخال ورقم البطاقة مرة أخرى!
acc_wiegand_tip3=لا يمكن مطابقة رقم البطاقة المدخلة ({0}) برقم البطاقة الأصلي. يرجى التحقق مرة أخرى!
acc_wiegand_tip4=لا يمكن مطابقة رمز الموقع الذي تم إدخاله ({0}) في رقم البطاقة الأصلية. يرجى التحقق مرة أخرى!
acc_wiegand_tip5=إذا كنت تريد استخدام هذه الميزة، فيرجى الاحتفاظ بجميع أعمدة رمز الموقع فارغة!
acc_wiegand_warnInfo1=عندما تستمر في قراءة بطاقة جديدة، يرجى التبديل يدويًا إلى البطاقة التالية.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=لوحة دخول/خروج الموظفين
acc_LCDRTMonitor_current=المعلومات المتعلقة بالموظفين الحاليين
acc_LCDRTMonitor_previous=معلومات الموظفين السابقة
#api
acc_api_levelIdNotNull=لا يمكن أن يكون معرف مستوى الوصول فارغًا
acc_api_levelExist=إذن المجموعة موجودة 
acc_api_areaNameNotNull=المنطقة لا يمكن أن تكون فارغة
acc_api_levelNotExist=مستوى الوصول غير موجود
acc_api_levelNotHasPerson=لا يوجد أشخاص تحت مستوى الوصول
acc_api_doorIdNotNull=لا يمكن أن يكون معرف الباب فارغًا
acc_api_doorNameNotNull=اسم الباب لا يمكن أن يكون فارغا
acc_api_doorIntervalSize=يجب أن تكون مدة فتح الباب بين 1 ~ 254
acc_api_doorNotExist=الباب غير موجود
acc_api_devOffline=الجهاز غير متصل أو معطل
acc_api_devSnNotNull=لا يمكن أن يكون الجهاز SN فارغًا
acc_api_timesTampNotNull=ختم الوقت لا يمكن أن تكون فارغة
acc_api_openingTimeCannotBeNull=لا يمكن أن يكون وقت فتح الباب فارغًا
acc_api_parameterValueCannotBeNull=لا يمكن أن تكون قيمة المعلمة فارغة
acc_api_deviceNumberDoesNotExist=الرقم التسلسلي للجهاز غير موجود
acc_api_readerIdCannotBeNull=لا يمكن أن يكون معرّف القارئ فارغًا
acc_api_theReaderDoesNotExist=قراءة الرأس غير موجود
acc_operate_door_notInValidDate=ليس حاليًا في الوقت الفعلي للفتح عن بُعد ، يرجى الاتصال بالمسؤول إذا لزم الأمر!
acc_api_doorOffline=الباب غير متصل أو معطل
#门禁信息自动导出
acc_autoExport_title=التصدير التلقائي لسجلات الأحداث
acc_autoExport_frequencyTitle=تردد التصدير التلقائي
acc_autoExport_frequencyDay=كل يوم
acc_autoExport_frequencyMonth=كل شهر
acc_autoExport_firstDayMonth=اليوم الأول من الشهر
acc_autoExport_specificDate=تاريخ التصدير
acc_autoExport_exportModeTitle=وضع التصدير
acc_autoExport_dailyMode=سجل الأحداث اليومية
acc_autoExport_monthlyMode=سجل الأحداث الشهري(جميع المعلومات من الشهر الماضي وهذا الشهر)
acc_autoExport_allMode=جميع سجلات الأحداث(ما يصل إلى 30000 سجلات الأحداث)
acc_autoExport_recipientMail=تلقي صندوق البريد
#First In And Last Out
acc_inOut_inReaderName=اسم قارئ الإدخال الأقدم
acc_inOut_firstInTime=وقت الدخول المبكر
acc_inOut_outReaderName=اسم آخر قارئ للقراءة
acc_inOut_lastOutTime=وقت الإجازة الأخير
#防疫参数
acc_dev_setHep=تعيين معلمات الوقاية من الأوبئة
acc_dev_enableIRTempDetection=تمكين فحص درجة الحرارة باستخدام الأشعة تحت الحمراء
acc_dev_enableNormalIRTempPass=منع الوصول عندما تكون درجة الحرارة فوق النطاق
acc_dev_enableMaskDetection=تمكين اكتشاف القناع
acc_dev_enableWearMaskPass=رفض الوصول بدون قناع
acc_dev_tempHighThreshold=حد إنذار درجة الحرارة العالية
acc_dev_tempUnit=وحدة درجة الحرارة
acc_dev_tempCorrection=تصحيح انحراف درجة الحرارة
acc_dev_enableUnregisterPass=السماح للأشخاص غير المسجلين بالوصول
acc_dev_enableTriggerAlarm=تشغيل إنذار خارجي
#联动邮件
acc_mail_temperature=درجة حرارة الجسم
acc_mail_mask=سواء لبس القناع
acc_mail_unmeasured=غير مُقاس
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort الأحداث العالمية
acc_digifort_chooseDigifortEvents=اختر Digifort الأحداث العالمية
acc_digifort_eventExpiredTip=إذا تم حذف الحدث العالمي من خادم Digifort ، فسيظهر باللون الأحمر.
acc_digifort_checkConnection=يرجى التحقق من صحة معلومات الاتصال الخاصة بخادم Digifort.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=اضف جهات اتصال
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=قم بتعيين المعلمات الموسعة
acc_extendParam_faceUI=عرض الواجهة
acc_extendParam_faceParam=معلمات الوجه
acc_extendParam_accParam=معلمات التحكم في الوصول
acc_extendParam_intercomParam=الاتصال الداخلي المعلمات البصرية
acc_extendParam_volume=الحجم
acc_extendParam_identInterval=فاصل التعريف (بالمللي ثانية)
acc_extendParam_historyVerifyResult=عرض نتائج التحقق السابقة
acc_extendParam_macAddress=عرض عنوان MAC
acc_extendParam_showIp=إظهار عنوان IP
acc_extendParam_24HourFormat=إظهار تنسيق 24 ساعة
acc_extendParam_dateFormat=تنسيق التاريخ
acc_extendParam_1NThreshold=1: عتبة N
acc_extendParam_facePitchAngle=زاوية الملعب
acc_extendParam_faceRotationAngle=زاوية دوران الوجه
acc_extendParam_imageQuality=جودة الصورة
acc_extendParam_miniFacePixel=الحد الأدنى من بكسل الوجه
acc_extendParam_biopsy=تمكين الخزعة
acc_extendParam_showThermalImage=إظهار الصورة الحرارية
acc_extendParam_attributeAnalysis=تمكين تحليل السمات
acc_extendParam_temperatureAttribute=خاصية الكشف عن درجة الحرارة
acc_extendParam_maskAttribute=خاصية الكشف عن القناع
acc_extendParam_minTemperature=الحد الأدنى لعتبة الكشف عن درجة حرارة الجسم
acc_extendParam_maxTemperature=الحد الأعلى للحد الأدنى لكشف درجة حرارة الجسم
acc_extendParam_gateMode=وضع البوابة
acc_extendParam_qrcodeEnable=تفعيل وظيفة الرمز التربيعي
#可视对讲
acc_dev_intercomServer=عنوان خدمة الاتصال الداخلي المرئي
acc_dev_intercomPort=خدمة الاتصال الداخلي المرئي
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=تزامن المستوى
# 夏令时名称
acc_dsTimeUtc_none=لا مجموعة
acc_dsTimeUtc_AreaNone=لا التوقيت الصيفي في المنطقة
acc_dsTimeUtc1000_0=كانبيرا ، ملبورن ، سيدني
acc_dsTimeUtc1000_1=هوبارت
acc_dsTimeUtc_0330_0=نيوفاوندلاند
acc_dsTimeUtc_1000_0=ألوشيان
acc_dsTimeUtc_0200_0=وسط المحيط الأطلسي
acc_dsTimeUtc0930_0=أديليد
acc_dsTimeUtc_0100_0=جزر الأزور
acc_dsTimeUtc_0400_0=Atlantic Time ( كندا )
acc_dsTimeUtc_0400_1=سانتياغو
acc_dsTimeUtc_0400_2=أسونسيون
acc_dsTimeUtc_0300_0=غرينلاند
acc_dsTimeUtc_0300_1=سانت بيير وميكلون
acc_dsTimeUtc0200_0=تشيسيناو
acc_dsTimeUtc0200_1=هلسنكي , كييف , ريغا , صوفيا , تالين , فيلنيوس
acc_dsTimeUtc0200_2=أثينا ، بوخارست
acc_dsTimeUtc0200_3=أورشليم
acc_dsTimeUtc0200_4=عمّان
acc_dsTimeUtc0200_5=بيروت .
acc_dsTimeUtc0200_6=دمشق .
acc_dsTimeUtc0200_7=غزة , الخليل
acc_dsTimeUtc0200_8=جوبا
acc_dsTimeUtc_0600_0=وسط الوقت ( الولايات المتحدة وكندا )
acc_dsTimeUtc_0600_1=غوادالاخارا ، مكسيكو سيتي ، مونتيري
acc_dsTimeUtc_0600_2=جزيرة الفصح
acc_dsTimeUtc1300_0=ساموا
acc_dsTimeUtc_0500_0=هافانا
acc_dsTimeUtc_0500_1=التوقيت الشرقي ( الولايات المتحدة وكندا )
acc_dsTimeUtc_0500_2=هايتي
acc_dsTimeUtc_0500_3=إنديانا ( شرق )
acc_dsTimeUtc_0500_4=جزر تركس وكايكوس
acc_dsTimeUtc_0800_0=المحيط الهادئ الوقت ( الولايات المتحدة وكندا )
acc_dsTimeUtc_0800_1=ولاية كاليفورنيا السفلى
acc_dsTimeUtc0330_0=تهران
acc_dsTimeUtc0000_0=دبلن ، أدنبرة ، لشبونة ، لندن
acc_dsTimeUtc1200_0=فيجي
acc_dsTimeUtc1200_1=بتروبافلوفسك - كامتشاتكا - مستعمل قديم
acc_dsTimeUtc1200_2=أوكلاند ، ولينغتون
acc_dsTimeUtc1100_0=جزيرة نورفولك
acc_dsTimeUtc_0700_0=تشيهواهوا ، لاباز ، مازاتلان
acc_dsTimeUtc_0700_1=جبل الوقت ( الولايات المتحدة وكندا )
acc_dsTimeUtc0100_0=بلغراد , براتيسلافا , بودابست , ليوبليانا , براغ
acc_dsTimeUtc0100_1=سراييفو , سكوبي , وارسو , زغرب
acc_dsTimeUtc0100_2=الدار البيضاء
acc_dsTimeUtc0100_3=بروكسل ، كوبنهاغن ، مدريد ، باريس
acc_dsTimeUtc0100_4=أمستردام , برلين , برن , روما , ستوكهولم , فيينا
acc_dsTimeUtc_0900_0=ألاسكا
#安全点(muster point)
acc_leftMenu_accMusterPoint=موستر بوينت
acc_musterPoint_activate=تنشيط
acc_musterPoint_addDept=إضافة قسم
acc_musterPoint_delDept=حذف القسم
acc_musterPoint_report=تقرير نقطة التجمع
acc_musterPointReport_sign=قم بتسجيل الدخول يدويًا
acc_musterPointReport_generate=إنشاء التقارير
acc_musterPoint_addSignPoint=إضافة نقطة تسجيل
acc_musterPoint_delSignPoint=حذف نقطة التوقيع
acc_musterPoint_selectSignPoint=الرجاء إضافة نقطة تسجيل!
acc_musterPoint_signPoint=نقطة تسجيل
acc_musterPoint_delFailTip=توجد بالفعل نقاط تجمع نشطة ولا يمكن حذفها!
acc_musterPointReport_enterTime=أدخل الوقت
acc_musterPointReport_dataAnalysis=تحليل البيانات
acc_musterPointReport_safe=آمن
acc_musterPointReport_danger=خطر
acc_musterPointReport_signInManually=تثقيب يدوي
acc_musterPoint_editTip=نقطة التجمع نشطة ولا يمكن تحريرها!
acc_musterPointEmail_total=الحضور المتوقع :
acc_musterPointEmail_safe=الدخول ( الأمن ) :
acc_musterPointEmail_dangerous=في خطر
acc_musterPoint_messageNotification=رسالة الإخطار عند تفعيلها
acc_musterPointReport_sendEmail=خطة دفع التقرير
acc_musterPointReport_sendInterval=إرسال الفاصلة
acc_musterPointReport_sendTip=تأكد من اختيار طريقة الإخطار بنجاح ، وإلا فإن الإخطار لن ترسل بشكل صحيح !
acc_musterPoint_mailSubject=عاجل جمع إشعار
acc_musterPoint_mailContent=الرجاء الاجتماع فورًا عند "{0}" وتسجيل الدخول على الجهاز "{1}" ، شكرًا لك!
acc_musterPointReport_mailHead=مرحباً ، هذا تقرير الطوارئ يرجى مراجعة .
acc_musterPoint_visitorsStatistics=إحصائيات الزوار
# 报警监控
acc_alarm_priority=الأولوية
acc_alarm_total=مجموع
acc_alarm_today=سجل اليوم
acc_alarm_unhandled=غير معترف بها
acc_alarm_inProcess=التعامل مع
acc_alarm_acknowledged=أكد
acc_alarm_top5=أعلى خمسة إنذار الحدث
acc_alarm_monitoringTime=رصد الوقت
acc_alarm_history=إنذار تجهيز التاريخ
acc_alarm_acknowledgement=تجهيز سجل
acc_alarm_eventDescription=تفاصيل الحدث
acc_alarm_acknowledgeText=البريد الإلكتروني إلى البريد الإلكتروني المحدد بعد اختيار تفاصيل الحدث التنبيه
acc_alarm_emailSubject=إضافة إنذار معالجة الحدث سجل
acc_alarm_mute=كتم الصوت
acc_alarm_suspend=تعليق
acc_alarm_confirmed=وقد تم تأكيد الحدث
acc_alarm_list=سجل الإنذار
#ntp
acc_device_setNTPService=إعدادات خادم NTP
acc_device_setNTPServiceTip=أدخل عناوين خادم متعددة ، مفصولة بفاصلة (،) أو فاصلة منقوطة (؛)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=في عملية التحكم في الوصول ، سوبر المستخدمين لا تقتصر على المنطقة الزمنية ، عكس الدخول و التعشيق ، ولها أولوية عالية لفتح الباب .
acc_editPerson_delayPassageTip=تمديد وقت انتظار الموظفين من خلال نقطة الوصول . فائدة نموذج مناسبة لأنّ الإعاقة الجسدية أو غيرها من الأشخاص ذوي الإعاقة .
acc_editPerson_disabledTip=تعطيل الوصول إلى مستوى مؤقتا .
#门禁向导
acc_guide_title=معالج إعداد وحدة التحكم في الوصول
acc_guide_addPersonTip=تحتاج إلى إضافة الشخص وبيانات الاعتماد المقابلة (الوجه أو بصمة الإصبع أو البطاقة أو راحة اليد أو كلمة المرور) ؛ إذا كنت قد أضفت بالفعل ، فتخط هذه الخطوة مباشرة
acc_guide_timesegTip=الرجاء تكوين فترة عمل صالحة
acc_guide_addDeviceTip=الرجاء إضافة الجهاز المقابل كنقطة وصول
acc_guide_addLevelTip=إضافة مستوى التحكم في الوصول
acc_guide_personLevelTip=قم بتعيين سلطة التحكم في الوصول المقابلة للشخص
acc_guide_rtMonitorTip=تحقق من سجلات التحكم في الوصول في الوقت الفعلي
acc_guide_rtMonitorTip2=إضافة إلى المناطق التي تنتمي إليها ، في الوقت الحقيقي عرض سجلات التحكم في الوصول بعد الرد على الباب
#查看区域内人员
acc_zonePerson_cleanCount=امسح إحصائيات الأشخاص الذين يدخلون ويغادرون
acc_zonePerson_inCount=إحصائيات عن عدد الأشخاص الذين يدخلون
acc_zonePerson_outCount=إحصائيات عن عدد الأشخاص المغادرين
#biocv460
acc_device_validFail=اسم المستخدم أو كلمة المرور غير صحيح وفشل التحقق!
acc_device_pwdRequired=فقط أدخل ماكس 6 أرقام صحيحة