#[1]左侧菜单
acc_module=門禁
acc_leftMenu_accDev=門禁設備
acc_leftMenu_auxOut=輔助輸出
acc_leftMenu_dSTime=夏令時
acc_leftMenu_access=門禁
acc_leftMenu_door=門禁
acc_leftMenu_accRule=門禁規則
acc_leftMenu_interlock=互鎖
acc_leftMenu_antiPassback=反潛
acc_leftMenu_globalLinkage=全局聯動
acc_leftMenu_firstOpen=首人常開
acc_leftMenu_combOpen=多人開門
acc_leftMenu_personGroup=多人開門人員組
acc_leftMenu_level=門禁權限組
acc_leftMenu_electronicMap=電子地圖
acc_leftMenu_personnelAccessLevels=人員門禁權限
acc_leftMenu_searchByLevel=以權限組查詢
acc_leftMenu_searchByDoor=以門查詢
acc_leftMenu_expertGuard=進階門禁
acc_leftMenu_zone=門禁區域
acc_leftMenu_readerDefine=讀頭定義
acc_leftMenu_gapbSet=全局反潛
acc_leftMenu_whoIsInside=檢視區域內人員
acc_leftMenu_whatRulesInside=檢視區域內規則
acc_leftMenu_occupancy=人數控制
acc_leftMenu_route=路線定義
acc_leftMenu_globalInterlock=全局互鎖
acc_leftMeue_globalInterlockGroup=全局互鎖組
acc_leftMenu_dmr=停留時間
acc_leftMenu_personLimit=人員有效性
acc_leftMenu_verifyModeRule=驗證模式規則
acc_leftMenu_verifyModeRulePersonGroup=驗證模式規則人員組
acc_leftMenu_extDev=I/O擴展板
acc_leftMenu_firstInLastOut=人員進出記錄
acc_leftMenu_accReports=門禁報表
#[3]门禁时间段
acc_timeSeg_entity=門禁時間段
acc_timeSeg_canNotDel=時間段正在使用中，不能刪除！
#[4]门禁设备--公共的在common中
acc_common_ruleName=規則名稱
acc_common_hasBeanSet=已設定
acc_common_notSet=未設定
acc_common_hasBeenOpened=已開啟
acc_common_notOpened=未開啟
acc_common_partSet=部分設定
acc_common_linkageAndApbTip=聯動和全局聯動、反潛和全局反潛同時設定，可能會有衝突。
acc_common_vidlinkageTip=請確保聯動對應的輸入點已綁定可用視訊通道，否則視訊聯動功能將無法標準使用！
acc_common_accZoneFromTo=無法設定相同的門禁區域
acc_common_logEventNumber=記錄編號
acc_common_bindOrUnbindChannel=綁定/解綁監視器
acc_common_boundChannel=已綁定監視器
#设备信息
acc_dev_iconType=圖示類型
acc_dev_carGate=車閘
acc_dev_channelGate=通道
acc_dev_acpType=門禁控制器類型
acc_dev_oneDoorACP=單門控制器
acc_dev_twoDoorACP=兩門控制器
acc_dev_fourDoorACP=四門控制器
acc_dev_onDoorACD=一體機
acc_dev_switchToTwoDoorTwoWay=切換為兩門雙向
acc_dev_addDevConfirm2=提示：設備連線成功，但控制器類型與實際不符，將修改為{0}門控制器，繼續新增？
acc_dev_addDevConfirm4=提示：設備連線成功，但控制器類型與實際不符，將修改為一體機，繼續新增？
acc_dev_oneMachine=一體機
acc_dev_fingervein=指靜脈
acc_dev_control=控制器
acc_dev_protocol=協定類型
acc_ownedBoard=所屬擴展板
#设备操作
acc_dev_start=開始
acc_dev_accLevel=門禁權限
acc_dev_timeZoneAndHoliday=時間段、節假日
acc_dev_linkage=聯動
acc_dev_doorOpt=門參數
acc_dev_firstPerson=首人開門
acc_dev_multiPerson=多人開門
acc_dev_interlock=互鎖
acc_dev_antiPassback=反潛
acc_dev_wiegandFmt=韋根格式
acc_dev_outRelaySet=輔助輸出設定
acc_dev_backgroundVerifyParam=後台驗證參數
acc_dev_getPersonInfoPrompt=請確保已成功取得人員訊息，否則會產生異常，是否繼續？
acc_dev_getEventSuccess=取得事件成功
acc_dev_getEventFail=取得事件失敗
acc_dev_getInfoSuccess=取得訊息成功
acc_dev_getInfoXSuccess=取得{0}成功
acc_dev_getInfoFail=取得訊息失敗
acc_dev_updateExtuserInfoFail=更新人員訊息中的延長通行等失敗，請重新取得人員訊息。
acc_dev_getPersonCount=取得人員數
acc_dev_getFPCount=取得指紋數
acc_dev_getFVCount=取得指靜脈數
acc_dev_getFaceCount=取得人臉數
acc_dev_getPalmCount=取得手掌數
acc_dev_getBiophotoCount=取得比對照片數
acc_dev_noData=設備中沒有資料
acc_dev_noNewData=設備中沒有新記錄資料
acc_dev_softLtDev=軟體中的人數大於設備。
acc_dev_personCount=人數為：
acc_dev_personDetail=詳細訊息如下：
acc_dev_softEqualDev=軟體和設備中的人員相等。
acc_dev_softGtDev=設備中的人數大於軟體。
acc_dev_cmdSendFail=指令傳送失敗，請重新取得
acc_dev_issueVerifyParam=設定後台驗證參數
acc_dev_verifyParamSuccess=後台驗證參數下發成功
acc_dev_backgroundVerify=後台驗證
acc_dev_selRightFile=請選取正確的升級檔案！
acc_dev_devNotOpForOffLine=設備處於離線狀態，請稍後重試！
acc_dev_devNotSupportFunction=設備不支援此功能！
acc_dev_devNotOpForDisable=設備處於禁用狀態，請稍後重試！
acc_dev_devNotOpForNotOnline=設備處於離線或禁用狀態，請稍後重試！
acc_dev_getPersonInfo=取得人員訊息
acc_dev_getFPInfo=取得指紋訊息
acc_dev_getFingerVeinInfo=取得指靜脈訊息
acc_dev_getPalmInfo=取得手掌訊息
acc_dev_getBiophotoInfo=取得可見光人臉信息
acc_dev_getIrisInfo=獲取虹膜資訊
acc_dev_disable=處於禁用狀態，請重新選取！
acc_dev_offlineAndContinue=處於離線狀態，是否繼續操作？
acc_dev_offlineAndSelect=處於離線狀態。
acc_dev_opAllDev=所有設備
acc_dev_opOnlineDev=線上設備
acc_dev_opException=處理發生異常
acc_dev_exceptionAndConfirm=設備連線逾時，操作失敗，請檢查網路連線！
acc_dev_getFaceInfo=取得臉部訊息
acc_dev_selOpDevType=請選取要操作的設備類型：
acc_dev_hasFilterByFunc=已過濾離線或不支援該功能的設備！
acc_dev_masterSlaveMode=RS485主從機模式
acc_dev_master=主電腦
acc_dev_slave=從機
acc_dev_modifyRS485Addr=修改RS485位址
acc_dev_rs485AddrTip=請輸入1-63之間的整數！
acc_dev_enableFeature=後台驗證啟用
acc_dev_disableFeature=後台驗證禁用
acc_dev_getCountOnly=僅取得數量
acc_dev_queryDevPersonCount=查詢設備人員數
acc_dev_queryDevVolume=查詢設備容量
acc_dev_ruleType=規則類型
acc_dev_contenRule=規則內容
acc_dev_accessRules=檢視設備中門禁規則
acc_dev_ruleContentTip=多個規則之間用豎線（|）隔開。
acc_dev_rs485AddrFigure=RS485位址撥碼圖
acc_dev_addLevel=新增到權限組
acc_dev_personOrFingerTanto=人員或指紋數量過多，同步失敗...
acc_dev_personAndFingerUnit=（個）
acc_dev_setDstime=設定夏令時
acc_dev_setTimeZone=設定設備時區
acc_dev_selectedTZ=已選時區
acc_dev_timeZoneSetting=正在設定設備時區...
acc_dev_timeZoneCmdSuccess=設備時區指令傳送成功...
acc_dev_enableDstime=啟用夏令時
acc_dev_disableDstime=禁用夏令時
acc_dev_timeZone=時區
acc_dev_dstSettingTip=正在設定夏令時...
acc_dev_dstDelTip=正在刪除設備夏令時...
acc_dev_enablingDst=正在啟用夏令時
acc_dev_dstEnableCmdSuccess=啟用設備夏令時指令傳送成功。
acc_dev_disablingDst=正在禁用夏令時。
acc_dev_dstDisableCmdSuccess=禁用設備夏令時指令傳送成功。
acc_dev_dstCmdSuccess=夏令時設定指令傳送成功...
acc_dev_usadst=夏令時
acc_dev_notSetDst=尚未設定
acc_dev_selectedDst=已選夏令時
acc_dev_configMasterSlave=主從配置
acc_dev_hasFilterByUnOnline=已過濾非線上設備
acc_dev_softwareData=如發現軟體和設備資料不一致，請先同步二者資料後再查詢！
acc_dev_disabled=以下設備處於禁用狀態，無法操作！
acc_dev_offline=以下設備處於離線狀態，無法操作！
acc_dev_noSupport=以下設備不支援該功能，無法操作！
acc_dev_noRegDevTip=取得非登記機的資料會覆蓋軟體中的資料，確定要繼續嗎？
acc_dev_noOption=暫無符合條件選項。
acc_dev_devFWUpdatePrompt=正在新增的設備將無法使用標準使用系統內的禁止名單、人員有效期功能（詳情請參考使用者手冊）。
acc_dev_panelFWUpdatePrompt=正在新增的設備將無法標準使用系統內的禁止名單、人員有效期功能，是否立即升級韌體？
acc_dev_sendEventCmdSuccess=傳送刪除事件指令成功
acc_dev_tryAgain=請重試
acc_dev_eventAutoCheckAndUpload=自動檢查並取得記錄
acc_dev_eventUploadStart=開始取得設備事件記錄
acc_dev_eventUploadEnd=取得設備事件記錄結束
acc_dev_eventUploadFailed=取得設備事件記錄失敗
acc_dev_eventUploadPrompt=檢驗到韌體版本過低的設備，升級韌體之前，您希望：
acc_dev_backupToSoftware=備份資料到軟體
acc_dev_deleteEvent=刪除設備中事件記錄
acc_dev_upgradePrompt=韌體版本過低升級後可能導致事件記錄錯亂，可以先將資料備份到軟體。
acc_dev_conflictCardNo=系統中存在卡號為{0}的其它人員！
acc_dev_rebootAfterOperate=操作成功，稍後設備將重啟。
acc_dev_baseOptionTip=取得基礎參數異常
acc_dev_funOptionTip=取得功能參數異常
acc_dev_sendComandoTip=取得設備參數指令傳送失敗
acc_dev_noC3LicenseTip=無法新增該類型設備（{0}）。要繼續現用的操作，請聯繫銷售人員！
acc_dev_combOpenDoorTip=（{0}）已設定多人開門，不能同時使用後台驗證功能！
acc_dev_combOpenDoorPersonCountTip=組{0}開門人數不得大於{1}！
acc_dev_addDevTip=此操作僅適用於新增通信協定為PULL的設備！
acc_dev_addError=設備新增異常，缺少參數（{0}）！
acc_dev_updateIPAndPortError=更新伺服器IP和連接埠異常
acc_dev_transferFilesTip=韌體檢驗成功，傳檔案
acc_dev_serialPortExist=串口存在
acc_dev_isExist=設備是否存在
acc_dev_description=描述
acc_dev_searchEthernet=搜尋乙太網設備
acc_dev_searchRS485=搜尋RS485設備 
acc_dev_rs485AddrTip1=RS485的起始位址不能夠大於結束位址
acc_dev_rs485AddrTip2=RS485的搜尋範圍必須在20以內
acc_dev_clearAllCmdCache=清除全部指令
acc_dev_authorizedSuccessful=授權成功
acc_dev_authorize=授權
acc_dev_registrationDevice=登記機
acc_dev_setRegistrationDevice=設定登記機
acc_dev_mismatchedDevice=設備連線失敗，原因：設備序號錯誤！
acc_dev_pwdStartWithZero=通信密碼不能以0開頭！
acc_dev_maybeDisabled=現用的許可容許再新增{0}個門，新增設備中超出許可點數限制的門將會被禁用，是否繼續操作？
acc_dev_Limit=系統中許可點數已經達到上限，如需新增，請授權。
acc_dev_selectDev=請選取設備！
acc_dev_cannotAddPullDevice=不容許新增PULL設備！要繼續現用的操作，請聯繫銷售人員！
acc_dev_notContinueAddPullDevice=系統裡已經存在{0}台PULL設備，不容許繼續新增！要繼續現用的操作，請聯繫銷售人員！
acc_dev_deviceNameNull=設備型號為空，不能新增設備！
acc_dev_commTypeErr=設備通信模式不符合，無法新增！
acc_dev_inputDomainError=請輸入正確格式的功能變數名位址！
acc_dev_levelTip=權限組下人數大於5000，無法自動新增！
acc_dev_auxinSet=輔助輸入設定
acc_dev_verifyModeRule=驗證模式規則
acc_dev_netModeWired=有線
acc_dev_netMode4G=4G
acc_dev_netModeWifi=Wifi
acc_dev_updateNetConnectMode=切換網路連線
acc_dev_wirelessSSID=無線SSID
acc_dev_wirelessKey=無線密鑰
acc_dev_searchWifi=搜尋無線清單
acc_dev_testNetConnectSuccess=通信連線成功，是否確認切換？
acc_dev_testNetConnectFailed=現用的連線模式不能標準通信！
acc_dev_signalIntensity=信號強度
acc_dev_resetSearch=重新搜尋
acc_dev_addChildDevice=授權子設備
acc_dev_modParentDevice=變更主設備
acc_dev_configParentDevice=配置主設備
acc_dev_lookUpChildDevice=檢視子設備
acc_dev_addChildDeviceTip=需在授權子設備下進行授權
acc_dev_maxSubCount=授權子設備數量超過最大接入數量{0}台。
acc_dev_seletParentDevice=請選取主設備！
acc_dev_networkCard=網路卡
acc_dev_issueParam=自訂下發參數
acc_dev_issueMode=下發模式
acc_dev_initIssue=3030韌體起始化下發資料
acc_dev_customIssue=自訂下發資料
acc_dev_issueData=資料
acc_dev_parent=主設備
acc_dev_parentEnable=主設備處於禁用狀態
acc_dev_parentTips=綁定主設備將會刪除設備中已有的所有資料，需要重新對其進行設定。
acc_dev_addDevIpTip=新IP位址不能和伺服器IP位址一致
acc_dev_modifyDevIpTip=新伺服器位址不能與設備IP位址相同
acc_dev_setWGReader=設定韋根讀頭
acc_dev_selectReader=點擊選取讀頭
acc_dev_IllegalDevice=非法設備
acc_dev_syncTimeWarnTip=以下這些設備的同步時間需在主控上同步。
acc_dev_setTimeZoneWarnTip=以下這些設備的時區需在主控上同步。
acc_dev_setDstimeWarnTip=以下這些設備的夏令時需在主控上同步。
acc_dev_networkSegmentSame=兩個網路卡不容許使用相同的網段。
acc_dev_upgradeProtocolNoMatch=升級檔案協定不符合
acc_dev_ipAddressConflict=已存在相同IP位址設備,請修改設備IP位址後重新新增。
acc_dev_checkServerPortTip=設定的伺服器連接埠與系統通信連接埠不一致，可能導致無法新增，是否繼續操作？
acc_dev_clearAdmin=清除管理員
acc_dev_setDevSate=設定機器出入狀態
acc_dev_sureToClear=你確定要執行清除管理員操作嗎？
acc_dev_hostState=主電腦狀態
acc_dev_regDeviceTypeTip=此設備為受控設備，不容許新增，請聯繫軟體提供者！
acc_dev_extBoardType=擴展板類型
acc_dev_extBoardTip=配置後需重啟設備才可生效！
acc_dev_extBoardLimit=每台設備只允許添加該類擴展板{0}個！
acc_dev_replace=替換設備
acc_dev_replaceTip=替换之后，旧设备将无法使用，请谨慎操作！
acc_dev_replaceTip1=替換之後，請進行“同步所有數據”操作；
acc_dev_replaceTip2=請確保替換的設備型號一致！
acc_dev_replaceTip3=請確保替換的設備已設定好和舊設備一樣的伺服器位址和連接埠號碼！
acc_dev_replaceFail=設備類型不一致，無法替換！
acc_dev_notApb=此設備無法進行門或讀頭反潜
acc_dev_upResourceFile=上傳資源檔
acc_dev_playOrder=播放順序
acc_dev_setFaceServerInfo=設定人臉後臺比對參數
acc_dev_faceVerifyMode=人臉比對模式
acc_dev_faceVerifyMode1=本地比對
acc_dev_faceVerifyMode2=後臺比對
acc_dev_faceVerifyMode3=本地比對優先
acc_dev_faceBgServerType=人臉後臺服務器類型
acc_dev_faceBgServerType1=軟體平臺服務
acc_dev_faceBgServerType2=協力廠商服務
acc_dev_isAccessLogic=是否啟用門禁邏輯驗證
#[5]门-其他关联的也复用此处
acc_door_entity=門
acc_door_number=門編號
acc_door_name=門名稱
acc_door_activeTimeZone=門有效時間段
acc_door_passageModeTimeZone=門常開時間段
acc_door_setPassageModeTimeZone=已設定門常開時間段
acc_door_notPassageModeTimeZone=未設定門常開時間段
acc_door_lockOpenDuration=鎖驅動時長
acc_door_entranceApbDuration=入反潛時長
acc_door_sensor=門磁
acc_door_sensorType=門磁類型
acc_door_normalOpen=常開
acc_door_normalClose=常閉
acc_door_sensorDelay=門磁延時
acc_door_closeAndReverseState=閉門回鎖
acc_door_hostOutState=主電腦出入狀態
acc_door_slaveOutState=從機出入狀態
acc_door_inState=入
acc_door_outState=出
acc_door_requestToExit=出門按鈕狀態
acc_door_withoutUnlock=鎖定
acc_door_unlocking=不鎖定
acc_door_alarmDelay=出門按鈕延時
acc_door_duressPassword=脅迫密碼
acc_door_currentDoor=現用的門
acc_door_allDoorOfCurDev=現用的設備所有門
acc_door_allDoorOfAllDev=所有設備所有門
acc_door_allDoorOfAllControlDev=所有控制器設備所有門
acc_door_allDoorOfAllStandaloneDev=所有一體機設備所有門
acc_door_allWirelessLock=所有無線鎖
acc_door_max6BitInteger=最大6位整數
acc_door_direction=進出方向
acc_door_onlyInReader=僅入讀頭
acc_door_bothInAndOutReader=出入讀頭
acc_door_noDoor=請新增門
acc_door_nameRepeat=門名稱重複
acc_door_duressPwdError=脅迫密碼不能與任意人員密碼相同！
acc_door_urgencyStatePwd=請輸入{0}位整數！
acc_door_noDevOnline=沒有線上的設備，或門不支援卡驗證模式！
acc_door_durationLessLock=門磁延時時長必須大於鎖驅動時長！
acc_door_lockMoreDuration=鎖驅動時長必須小於門磁延時時長！
acc_door_lockAndExtLessDuration=鎖驅動時長加延長通行時間必須小於門磁延時時長！
acc_door_noDevTrigger=沒有滿足條件的設備！
acc_door_relay=繼電器
acc_door_pin=工號
acc_door_selDoor=選門
acc_door_sensorStatus=門磁（{0}）
acc_door_sensorDelaySeconds=延時（{0}秒）
acc_door_timeSeg=時間段（{0}）
acc_door_combOpenInterval=多人開門操作間隔
acc_door_delayOpenTime=開門延時
acc_door_extDelayDrivertime=延長通行時間
acc_door_enableAudio=啟用報警提醒
acc_door_disableAudio=禁用報警提醒
acc_door_lockAndExtDelayTip=鎖驅動時長與延長通行時間的總和不能大於254秒。
acc_door_disabled=以下門處於禁用狀態，無法操作！
acc_door_offline=以下門處於離線狀態，無法操作！
acc_door_notSupport=以下門不支援此功能，無法操作！
acc_door_select=選取門
acc_door_pushMaxCount=系統裡存在{0}個已啟用的門，達到許可點數上限。要繼續現用的操作，請聯繫銷售人員！
acc_door_outNumber=現用的許可只容許啟用{0}個門！請重新選取門，或是聯繫銷售人員購買更新許可。
acc_door_latchTimeZone=出門按鈕有效時間段
acc_door_wgFmtReverse=卡號位反轉
acc_door_allowSUAccessLock=允許超級用戶在門鎖定時通行
acc_door_verifyModeSinglePwd=密碼不能作為獨立驗證模式進行使用！
acc_door_doorPassword=開門密碼
#辅助输入
acc_auxIn_timeZone=有效時間段
#辅助输出
acc_auxOut_passageModeTimeZone=常開時間段
acc_auxOut_disabled=以下輔助輸出處於禁用狀態，無法操作！
acc_auxOut_offline=以下輔助輸出處於離線狀態，無法操作！
#[8]门禁权限组
acc_level_doorGroup=的門組合
acc_level_openingPersonnel=的開門人員
acc_level_noDoor=沒有可以選取的選項，請先新增設備！
acc_level_doorRequired=必須選取門！
acc_level_doorCount=門數量
acc_level_doorDelete=刪除門
acc_level_isAddDoor=立即向剛新增的權限組新增門？
acc_level_master=通用權限組
acc_level_noneSelect=請新增權限組
acc_level_useDefaultLevel=是否切換為該部門的門禁權限？
acc_level_persAccSet=人事門禁設定
acc_level_visUsed={0}已被訪客模組使用，無法刪除！
acc_level_doorControl=門控制
acc_level_personExceedMax=當前許可權組人數（{0}），可選的許可權組人數最大為（{1}）
acc_level_exportLevel=導出權限組信息
acc_level_exportLevelDoor=導出權限組門信息
acc_level_exportLevelPerson=導出權限組人員信息
acc_level_importLevel=導入權限組信息
acc_level_importLevelDoor=導入權限組門信息
acc_level_importLevelPerson=導入權限組人員信息
acc_level_exportDoorFileName=門禁權限組門信息
acc_level_exportPersonFileName=門禁權限組人員信息
acc_levelImport_nameNotNull=權限組名稱不能為空
acc_levelImport_timeSegNameNotNull=時間段名稱不能為空
acc_levelImport_areaNotExist=區域不存在！
acc_levelImport_timeSegNotExist=時間段不存在！
acc_levelImport_nameExist=權限組名稱 {0} 已存在！
acc_levelImport_levelDoorExist=權限組門信息{0}已存在！
acc_levelImport_levelPersonExist=權限組人員信息{0}已存在！
acc_levelImport_noSpecialChar=權限組名稱不能包含特殊字符！
#[10]首人常开
acc_firstOpen_setting=首人常開設定
acc_firstOpen_browsePerson=瀏覽開門人員
#[11]多人组合开门
acc_combOpen_comboName=組合名稱
acc_combOpen_personGroupName=組名稱
acc_combOpen_personGroup=多人開門人員組
acc_combOpen_verifyOneTime=同時驗證人數
acc_combOpen_eachGroupCount=各組開門人數
acc_combOpen_group=組
acc_combOpen_changeLevel=開門人員組
acc_combOpen_combDeleteGroup=存在多人開門引用，請先刪除多人開門！
acc_combOpen_ownedLevel=所屬權限組
acc_combOpen_mostPersonCount=組合人數最多五人！
acc_combOpen_leastPersonCount=開門人員最少兩人！
acc_combOpen_groupNameRepeat=組合名稱重複！
acc_combOpen_groupNotUnique=開門人員組不能相同！
acc_combOpen_persNumErr=您選取的該組人數超出實際值，請重新選取！
acc_combOpen_combOpengGroupPersonShort=刪除該人員後，開門組中開門人員組人數不夠，請先刪除開門組！
acc_combOpen_backgroundVerifyTip=該門所屬的設備已啟用後台驗證，不能和多人開門規則同時使用！
#[12]互锁
acc_interlock_rule=互鎖規則
acc_interlock_mode1Or2={0} 與 {1} 互鎖
acc_interlock_mode3={0} 與 {1} 與 {2} 互鎖
acc_interlock_mode4={0} 與 {1} 互鎖，{2} 與 {3} 互鎖
acc_interlock_mode5={0} 與 {1} 與 {2} 與 {3} 互鎖
acc_interlock_hasBeenSet=已設定互鎖
acc_interlock_group1=組1
acc_interlock_group2=組2
acc_interlock_ruleInfo=組間互鎖
acc_interlock_alreadyExists=已存在相同的互鎖規則，請勿重複添加！
acc_interlock_groupInterlockCountErr=組內互鎖規則至少需要兩個門
acc_interlock_ruleType=互鎖規則類型
#[13]反潜
acc_apb_rules=反潛規則
acc_apb_reader={0}讀頭間反潛
acc_apb_reader2={0}，{1}各自讀頭間同時反潛
acc_apb_reader3={0}，{1}，{2}各自讀頭間同時反潛
acc_apb_reader4={0}，{1}，{2}，{3}各自讀頭間同時反潛
acc_apb_reader5={0}讀頭出反潛
acc_apb_reader6={0}讀頭入反潛
acc_apb_reader7=任意4門出入反潛
acc_apb_twoDoor={0} 與 {1}反潛
acc_apb_fourDoor={0} 與 {1}反潛，{2} 與 {3}反潛
acc_apb_fourDoor2={0}或{1} 與 {2}或{3}反潛
acc_apb_fourDoor3={0} 與 {1}或{2}反潛
acc_apb_fourDoor4={0} 與 {1}或{2}或{3}反潛
acc_apb_hasBeenSet=已設定反潛
acc_apb_conflictWithGapb=該設備已設定全局反潛，不能再設定該規則！
acc_apb_conflictWithApb=該區域中的設備已設定反潛，不能再設定全局反潛規則！
acc_apb_conflictWithEntranceApb=該區域中的設備已設定入時間反潛，不能再設定全局反潛規則！
acc_apb_controlIn=入反潛
acc_apb_controlOut=出反潛
acc_apb_controlInOut=出入反潛
acc_apb_groupIn=入組
acc_apb_groupOut=出組
acc_apb_reverseName=的反向反潜
acc_apb_door=門反潜
acc_apb_readerHead=讀頭反潜
acc_apb_alreadyExists=已存在相同的反潜規則，請勿重複添加！
#[17]电子地图
acc_map_addDoor=新增門
acc_map_addChannel=新增監視器
acc_map_noAccess=您沒有電子地圖模組的權限，請聯繫管理員！
acc_map_noAreaAccess=您沒有該區域的電子地圖權限，請聯繫管理員！
acc_map_imgSizeError=請上傳大小不超過{0}M的圖片！
#[18]门禁事件记录
acc_trans_entity=門禁事件記錄
acc_trans_eventType=事件類型
acc_trans_firmwareEvent=韌體事件
acc_trans_softwareEvent=軟體事件
acc_trans_today=今日訪問記錄
acc_trans_lastAddr=人員最後訪問位置
acc_trans_viewPhotos=檢視照片
acc_trans_exportPhoto=導出照片
acc_trans_dayNumber=天數
acc_trans_photo=門禁事件照片
acc_trans_fileIsTooLarge=導出的文件過大，請縮小範圍導出
#[19]门禁验证方式
acc_verify_mode_onlyface=人臉
acc_verify_mode_facefp=人臉+指紋
acc_verify_mode_facepwd=人臉+密碼
acc_verify_mode_facecard=人臉+卡
acc_verify_mode_facefpcard=人臉+指紋+卡
acc_verify_mode_facefppwd=人臉+指紋+密碼
acc_verify_mode_fv=指靜脈
acc_verify_mode_fvpwd=指靜脈+密碼
acc_verify_mode_fvcard=指靜脈+卡
acc_verify_mode_fvpwdcard=指靜脈+密碼+卡
acc_verify_mode_pv=手掌
acc_verify_mode_pvcard=手掌+卡
acc_verify_mode_pvface=手掌+人臉
acc_verify_mode_pvfp=手掌+指紋
acc_verify_mode_pvfacefp=手掌+人臉+指紋
#[20]门禁事件编号
acc_eventNo_-1=無
acc_eventNo_0=標準刷卡開門
acc_eventNo_1=常開時間段內刷卡
acc_eventNo_2=首人開門(刷卡)
acc_eventNo_3=多人開門(刷卡)
acc_eventNo_4=緊急狀態密碼開門
acc_eventNo_5=常開時間段開門
acc_eventNo_6=觸發聯動事件
acc_eventNo_7=取消報警
acc_eventNo_8=遠端開門
acc_eventNo_9=遠端關門
acc_eventNo_10=禁用當天常開時間段
acc_eventNo_11=啟用當天常開時間段
acc_eventNo_12=遠端開啟輔助輸出
acc_eventNo_13=遠端關閉輔助輸出
acc_eventNo_14=標準按指紋開門
acc_eventNo_15=多人開門(按指紋)
acc_eventNo_16=常開時間段內按指紋
acc_eventNo_17=卡加指紋開門
acc_eventNo_18=首人開門(按指紋)
acc_eventNo_19=首人開門(卡加指紋)
acc_eventNo_20=操作間隔太短
acc_eventNo_21=門非有效時間段(刷卡)
acc_eventNo_22=非法時間段
acc_eventNo_23=非法訪問
acc_eventNo_24=反潛
acc_eventNo_25=互鎖
acc_eventNo_26=多人驗證(刷卡)
acc_eventNo_27=卡未註冊
acc_eventNo_28=門開逾時
acc_eventNo_29=卡已過有效期
acc_eventNo_30=密碼錯誤
acc_eventNo_31=按指紋間隔太短
acc_eventNo_32=多人驗證(按指紋)
acc_eventNo_33=指紋已過有效期
acc_eventNo_34=指紋未註冊
acc_eventNo_35=門非有效時間段(按指紋)
acc_eventNo_36=門非有效時間段(按出門按鈕)
acc_eventNo_37=常開時間段無法關門
acc_eventNo_38=卡已掛失
acc_eventNo_39=禁止名單
acc_eventNo_40=多人驗證失敗(按指紋)
acc_eventNo_41=驗證模式錯誤
acc_eventNo_42=韋根格式錯誤
acc_eventNo_43=反潛驗證逾時
acc_eventNo_44=後台驗證失敗
acc_eventNo_45=後台驗證逾時
acc_eventNo_47=傳送指令失敗
acc_eventNo_48=多人驗證失敗(刷卡)
acc_eventNo_49=門非有效時間段(密碼)
acc_eventNo_50=按密碼間隔太短
acc_eventNo_51=多人驗證(密碼)
acc_eventNo_52=多人驗證失敗(密碼)
acc_eventNo_53=密碼已過有效期
acc_eventNo_100=防拆報警
acc_eventNo_101=脅迫密碼開門
acc_eventNo_102=門被意外開啟
acc_eventNo_103=脅迫指紋開門
acc_eventNo_200=門已開啟
acc_eventNo_201=門已關閉
acc_eventNo_202=出門按鈕開門
acc_eventNo_203=多人開門(卡加指紋)
acc_eventNo_204=常開時間段結束
acc_eventNo_205=遠端開門常開
acc_eventNo_206=設備啟動
acc_eventNo_207=密碼開門
acc_eventNo_208=超級使用者開門
acc_eventNo_209=觸發出門按鈕(被鎖定)
acc_eventNo_210=啟動消防開門
acc_eventNo_211=超級使用者關門
acc_eventNo_212=開啟電梯控制功能
acc_eventNo_213=關閉電梯控制功能
acc_eventNo_214=多人開門(密碼)
acc_eventNo_215=首人開門(密碼)
acc_eventNo_216=常開時間段內按密碼
acc_eventNo_220=輔助輸入點中斷
acc_eventNo_221=輔助輸入點短路
acc_eventNo_222=後台驗證成功
acc_eventNo_223=後台驗證
acc_eventNo_225=輔助輸入點標準
acc_eventNo_226=輔助輸入點觸發
acc_newEventNo_0=標準驗證開門
acc_newEventNo_1=常開時間段內驗證
acc_newEventNo_2=首人開門
acc_newEventNo_3=多人開門
acc_newEventNo_20=操作間隔太短
acc_newEventNo_21=門非有效時間段驗證開門
acc_newEventNo_26=多人驗證等待
acc_newEventNo_27=人未登記
acc_newEventNo_29=人已過有效期
acc_newEventNo_30=密碼錯誤
acc_newEventNo_41=驗證模式錯誤
acc_newEventNo_43=人員鎖定
acc_newEventNo_44=後台驗證失敗
acc_newEventNo_45=後台驗證逾時
acc_newEventNo_48=多人驗證失敗
acc_newEventNo_54=電池電壓過低
acc_newEventNo_55=立即更換電池
acc_newEventNo_56=非法操作
acc_newEventNo_57=後備電源
acc_newEventNo_58=常開報警
acc_newEventNo_59=非法管理
acc_newEventNo_60=門被反鎖
acc_newEventNo_61=重複驗證
acc_newEventNo_62=禁止使用者
acc_newEventNo_63=門已鎖定
acc_newEventNo_64=出門按鈕不在有效時間段內操作
acc_newEventNo_65=輔助輸入不在有效時間段內操作
acc_newEventNo_66=讀頭升級失敗
acc_newEventNo_67=遠端比對成功(裝置未授權)
acc_newEventNo_68=體溫過高-拒絕通行
acc_newEventNo_69=未佩戴口罩-拒絕通行
acc_newEventNo_70=人臉比對伺服器通信異常
acc_newEventNo_71=人臉伺服器響應異常
acc_newEventNo_73=無效二維碼
acc_newEventNo_74=二維碼已過期
acc_newEventNo_101=脅迫開門報警
acc_newEventNo_104=無效卡刷卡報警
acc_newEventNo_105=無法連線伺服器
acc_newEventNo_106=市電掉電
acc_newEventNo_107=電池掉電
acc_newEventNo_108=無法連線主控
acc_newEventNo_109=讀頭防拆報警
acc_newEventNo_110=讀頭離線
acc_newEventNo_112=擴展板離線
acc_newEventNo_114=火警輸入斷開(線路檢測)
acc_newEventNo_115=火警輸入短路(線路檢測)
acc_newEventNo_116=輔助輸入斷開(線路檢測)
acc_newEventNo_117=輔助輸入短路(線路檢測)
acc_newEventNo_118=出門開關斷開(線路檢測)
acc_newEventNo_119=出門開關短路(線路檢測)
acc_newEventNo_120=門磁斷開(線路檢測)
acc_newEventNo_121=門磁短路(線路檢測)
acc_newEventNo_159=遙控開門
acc_newEventNo_214=成功連線伺服器
acc_newEventNo_217=成功連線主控
acc_newEventNo_218=身份證通行
acc_newEventNo_222=後台驗證成功
acc_newEventNo_223=後台驗證
acc_newEventNo_224=按門鈴
acc_newEventNo_227=門雙開
acc_newEventNo_228=門雙關
acc_newEventNo_229=輔助輸出定時常開
acc_newEventNo_230=輔助輸出定時關閉常開
acc_newEventNo_232=驗證通過
acc_newEventNo_233=遠端鎖定
acc_newEventNo_234=遠端解鎖
acc_newEventNo_235=讀頭升級成功
acc_newEventNo_236=讀頭防拆報警解除
acc_newEventNo_237=讀頭在線
acc_newEventNo_239=設備呼叫
acc_newEventNo_240=通話結束
acc_newEventNo_243=火警輸入斷開
acc_newEventNo_244=火警輸入短路
acc_newEventNo_247=擴展板線上
acc_newEventNo_4008=市電恢復
acc_newEventNo_4014=消防輸入信號斷開，結束門常開
acc_newEventNo_4015=門已線上
acc_newEventNo_4018=後臺比對開門
acc_newEventNo_5023=消防狀態受限中
acc_newEventNo_5024=多人驗證超時
acc_newEventNo_5029=後臺比對失敗
acc_newEventNo_6005=記錄容量即將達到上限
acc_newEventNo_6006=線路短路(RS485)
acc_newEventNo_6007=線路短路(韋根)
acc_newEventNo_6011=門已離線
acc_newEventNo_6012=門拆機報警
acc_newEventNo_6013=消防輸入信號觸發，開啟門常開
acc_newEventNo_6015=復位擴展設備電源
acc_newEventNo_6016=恢復機出廠設定
acc_newEventNo_6070=後臺比對(禁止名單)
acc_eventNo_undefined=事件編號未定義
acc_advanceEvent_500=全局反潛(邏輯)
acc_advanceEvent_501=人員有效性(有效日期)
acc_advanceEvent_502=人數控制
acc_advanceEvent_503=全局互鎖
acc_advanceEvent_504=線路定義
acc_advanceEvent_505=全局反潛(定時)
acc_advanceEvent_506=全局反潛(定時邏輯)
acc_advanceEvent_507=人員有效性(第一次使用後有效天數)
acc_advanceEvent_508=人員有效性(使用次數)
acc_advanceEvent_509=後台驗證失敗(人未登記)
acc_advanceEvent_510=後台驗證失敗(資料異常)
acc_alarmEvent_701=DMR報警(設定規則：{0})
#[21]实时监控
acc_rtMonitor_openDoor=開門
acc_rtMonitor_closeDoor=關門
acc_rtMonitor_remoteNormalOpen=遠端常開
acc_rtMonitor_realTimeEvent=實時事件
acc_rtMonitor_photoMonitor=照片監測
acc_rtMonitor_alarmMonitor=報警監測
acc_rtMonitor_doorState=門狀態
acc_rtMonitor_auxOutName=輔助輸出名稱
acc_rtMonitor_nonsupport=不支援
acc_rtMonitor_lock=鎖定
acc_rtMonitor_unLock=解鎖
acc_rtMonitor_disable=禁用
acc_rtMonitor_noSensor=無門磁
acc_rtMonitor_alarm=報警
acc_rtMonitor_openForce=門被意外開啟
acc_rtMonitor_tamper=防拆
acc_rtMonitor_duressPwdOpen=脅迫密碼開門
acc_rtMonitor_duressFingerOpen=脅迫指紋開門
acc_rtMonitor_duressOpen=脅迫開門
acc_rtMonitor_openTimeout=門開逾時
acc_rtMonitor_unknown=不詳
acc_rtMonitor_noLegalDoor=現用的沒有符合條件的門！
acc_rtMonitor_noLegalAuxOut=現用的沒有符合條件的輔助輸出！
acc_rtMonitor_curDevNotSupportOp=現用的設備狀態不支援該操作！
acc_rtMonitor_curNormalOpen=現用的已常開
acc_rtMonitor_whetherDisableTimeZone=現用的門處於常開狀態，是否禁用當天常開時間段後關門？
acc_rtMonitor_curSystemNoDoors=現用的系統中沒有新增門或是沒有查詢到符合您需要的門！
acc_rtMonitor_cancelAlarm=取消報警
acc_rtMonitor_openAllDoor=開現用的所有門
acc_rtMonitor_closeAllDoor=關現用的所有門
acc_rtMonitor_confirmCancelAlarm=你確定要取消報警？
acc_rtMonitor_calcelAllDoor=取消全部報警
acc_rtMonitor_initDoorStateTip=正在取得系統內使用者授權範圍內的所有門......
acc_rtMonitor_alarmEvent=報警事件
acc_rtMonitor_ackAlarm=確認報警
acc_rtMonitor_ackAllAlarm=確認所有報警
acc_rtMonitor_ackAlarmTime=確認報警時間
acc_rtMonitor_sureToAckThese=你確定要確認這{0}條報警？確認後報警將被取消。
acc_rtMonitor_sureToAckAllAlarm=你確定要確認所有報警？確認後報警將被取消。
acc_rtMonitor_noSelectAlarmEvent=請選取要確認的報警事件！
acc_rtMonitor_noAlarmEvent=現用的系統沒有報警事件！
acc_rtMonitor_forcefully=取消門被意外開啟報警
acc_rtMonitor_addToRegPerson=新增到已登記人員
acc_rtMonitor_cardExist=此卡已被 {0} 佔用，不能重複發卡！
acc_rtMonitor_opResultPrompt=傳送請求成功{0}個，失敗{1}個！
acc_rtMonitor_doorOpFailedPrompt=以下門傳送請求失敗，請重試！
acc_rtMonitor_remoteOpen=遠端開啟
acc_rtMonitor_remoteClose=遠端關閉
acc_rtMonitor_alarmSoundClose=已關閉音訊
acc_rtMonitor_alarmSoundOpen=已開啟音訊
acc_rtMonitor_playAudio=音效提醒
acc_rtMonitor_isOpenShowPhoto=開啟顯示照片功能
acc_rtMonitor_isOpenPlayAudio=開啟音效提醒功能
acc_rtm_open=遠端釋放按鍵
acc_rtm_close=遠端鎖定按鍵
acc_rtm_eleModule=梯控
acc_cancelAlarm_fp=取消脅迫指紋開門報警
acc_cancelAlarm_pwd=取消脅迫密碼開門報警
acc_cancelAlarm_timeOut=取消門開逾時報警
#定时同步设备时间
acc_timing_syncDevTime=定時同步設備時間
acc_timing_executionTime=執行時間
acc_timing_theLifecycle=執行週期
acc_timing_errorPrompt=輸入有誤，請輸入1-31之間的數字！
acc_timing_checkedSyncTime=請選取同步時間
#[25]门禁报表
acc_trans_hasAccLevel=可以進出的門
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=請新增門禁區域
acc_zone_code=編號
acc_zone_parentZone=上級門禁區域
acc_zone_parentZoneCode=上級門禁區域編號
acc_zone_parentZoneName=上級門禁區域名稱
acc_zone_outside=外圍區域
#[G2]读头定义
acc_readerDefine_readerName=讀頭名稱
acc_readerDefine_fromZone=從門禁區域
acc_readerDefine_toZone=到門禁區域
acc_readerDefine_delInfo1=該讀頭定義關聯的門禁區域被進階門禁功能引用，無法刪除！
acc_readerDefine_selReader=選取讀頭
acc_readerDefine_selectReader=請新增讀頭！
acc_readerDefine_tip=人員到達外圍區域後，將會刪除該區域內該人員的記錄。
#[G3]全局反潜
acc_gapb_zone=門禁區域
acc_gapb_whenToResetGapb=重設反潛的時間
acc_gapb_apbType=反潛類型
acc_gapb_logicalAPB=邏輯反潛
acc_gapb_timedAPB=定時反潛
acc_gapb_logicalTimedAPB=定時邏輯反潛
acc_gapb_lockoutDuration=鎖閉時長
acc_gapb_devOfflineRule=控制器離線時
acc_gapb_standardLevel=標準通行權限
acc_gapb_accessDenied=拒絕使用者通行
acc_gapb_doorControlZone=門清單控制門禁區域的進出通行
acc_gapb_resetStatus=重設全局反潛狀態
acc_gapb_obeyAPB=服從全局反潛規則
acc_gapb_isResetGAPB=重設反潛
acc_gapb_resetGAPBSuccess=重設反潛狀態成功
acc_gapb_resetGAPBFaile=重設反潛狀態失敗
acc_gapb_chooseArea=請重新選取區域
acc_gapb_notDelInfo1=該門禁區域被引用為上級門禁區域，不能刪除！
acc_gapb_notDelInfo2=該門禁區域被讀頭定義引用，不能刪除！
acc_gapb_notDelInfo3=該門禁區域被進階門禁功能引用，不能刪除！
acc_gapb_notDelInfo4=該門禁區域被LED引用，不能刪除！
acc_gapb_zoneNumRepeat=門禁區域編號重複！
acc_gapb_zoneNameRepeat=門禁區域名稱重複！
acc_gapb_personResetGapbPre=確定要重設這
acc_gapb_personResetGapbSuffix=條人員反潛規則？
acc_gapb_apbPrompt=同一個門不能被用作控制兩個相對獨立的反潛邊界！
acc_gapb_occurApb=反潛發生時
acc_gapb_noOpenDoor=不開門
acc_gapb_openDoor=開門
acc_gapb_zoneNumLength=長度大於20字元！
acc_gapb_zoneNameLength=長度大於30字元！
acc_gapb_zoneRemarkLength=長度大於50字元！
acc_gapb_isAutoServerMode=檢驗到有設備未開啟後台驗證功能，可能對功能造成影響，是否立即開啟？
acc_gapb_applyTo=套用到
acc_gapb_allPerson=所有人員
acc_gapb_justSelected=選中人員
acc_gapb_excludeSelected=除選中以外的人員
#[G4]who is inside
acc_zoneInside_lastAccessTime=最近進入時間
acc_zoneInside_lastAccessReader=最近進入讀頭
acc_zoneInside_noPersonInZone=該門禁區域內沒有人員！
acc_zoneInside_noRulesInZone=沒有設定規則！
acc_zoneInside_totalPeople=總人數
acc_zonePerson_selectPerson=請選取人員或部門！
#[G5]路径
acc_route_name=路線名稱
acc_route_setting=路線設定
acc_route_addReader=新增讀頭
acc_route_delReader=刪除讀頭
acc_route_defineReaderLine=定義讀頭路線
acc_route_up=上移
acc_route_down=下移
acc_route_selReader=請選取讀頭後再進行相應操作！
acc_route_onlyOneOper=只能選取一個進行操作！
acc_route_readerOrder=讀頭定義順序
acc_route_atLeastSelectOne=請至少選取一個讀頭！
acc_route_routeIsExist=此條路線已經存在！
#[G6]DMR
acc_dmr_residenceTime=停留時間
acc_dmr_setting=停留時間設定
#[G7]Occupancy
acc_occupancy_max=最大容納人數
acc_occupancy_min=最小容納人數
acc_occupancy_unlimit=無限制
acc_occupancy_note=最大容納人數应大於最小容納人數！
acc_occupancy_containNote=最大/最小容納人數請至少輸入一個！
acc_occupancy_maxMinValid=請輸入大於0的數字！
acc_occupancy_conflict=該區域已設定人數控制規則！
acc_occupancy_maxMinTip=最大/最小容納人數為空表示不限制。
#card availability
acc_personLimit_zonePropertyName=門禁區域屬性名稱
acc_personLimit_useType=使用模式
acc_personLimit_userDate=有效日期
acc_personLimit_useDays=第一次使用後有效天數
acc_personLimit_useTimes=使用次數
acc_personLimit_setZoneProperty=設定門禁區域屬性
acc_personLimit_zoneProperty=門禁區域屬性
acc_personLimit_availabilityName=人員有效性名稱
acc_personLimit_days=天數
acc_personLimit_Times=次數
acc_personLimit_noDel=選取的門禁區域屬性被引用，不能刪除！
acc_personLimit_cannotEdit=該門禁區域屬性被引用，不能修改使用模式！
acc_personLimit_detail=詳情
acc_personLimit_userDateTo=有效期至
acc_personLimit_addPersonRepeatTip=所選部門下人員已添加到門禁區域屬性，請重新選擇部門!
acc_personLimit_leftTimes=剩餘{0}次
acc_personLimit_expired=已過期
acc_personLimit_unused=未使用
#全局互锁
acc_globalInterlock_addGroup=新增組
acc_globalInterlock_delGroup=刪除組
acc_globalInterlock_refuseAddGroupMessage=同一互鎖中，組內的門不能重複新增
acc_globalInterlock_refuseAddlockMessage=新增的門已出現在互鎖的其他組中
acc_globalInterlock_refuseDeleteGroupMessage=請先刪除互鎖中關聯資料
acc_globalInterlock_isGroupInterlock=組內互鎖
acc_globalInterlock_isAddTheDoorImmediately=是否立即新增門
acc_globalInterlock_isAddTheGroupImmediately=是否立即新增組
#门禁参数设置
acc_param_autoEventDev=自動下載事件記錄並發設備數量
acc_param_autoEventTime=自動下載事件記錄並發事件間隔
acc_param_noRepeat=信箱位址不能重複，請重新填寫
acc_param_most18=最多新增18個信箱位址
acc_param_deleteAlert=不能刪除所有信箱輸入框
acc_param_invalidOrRepeat=信箱位址格式錯誤或信箱位址重複
#全局联动
acc_globalLinkage_noSupport=現用的選中的門不支援鎖定、解鎖功能
acc_globalLinkage_trigger=觸發全局聯動
acc_globalLinkage_noAddPerson=該聯動規則觸發條件中不包括與人員關聯的條件，預設適用於所有人員！
acc_globalLinkage_selectAtLeastOne=請至少選擇一項聯動觸發操作！
acc_globalLinkage_selectTrigger=請新增聯動觸發條件！
acc_globalLinkage_selectInput=請新增聯動輸入點！
acc_globalLinkage_selectOutput=請新增聯動輸出點！
acc_globalLinkage_audioRemind=聯動音效提醒
acc_globalLinkage_audio=聯動音效
acc_globalLinkage_isApplyToAll=適用於所有人
acc_globalLinkage_scope=人員範圍
acc_globalLinkage_everyPerson=任意
acc_globalLinkage_selectedPerson=選取
acc_globalLinkage_noSupportPerson=不支援
acc_globalLinkage_reselectInput=觸發條件類型發生變化，請重新選取聯動輸入點！
acc_globalLinkage_addPushDevice=請新增支援該功能的設備！
#其他
acc_InputMethod_tips=請切換為英文輸入法狀態！
acc_device_systemCheckTip=檢驗到門禁設備訊息未新增！
acc_notReturnMsg=未取得到返回訊息！
acc_validity_period=許可已過有效期，該功能不能操作！
acc_device_pushMaxCount=系統中已存在{0}台設備，達到許可上限，無法新增設備！
acc_device_videoHardwareLinkage=視訊硬聯動設定
acc_device_videoCameraIP=攝像機IP位址
acc_device_videoCameraPort=攝像機連接埠
acc_location_unable=該事件點未新增到電子地圖中，無法定位該人員的具體位置！
acc_device_wgDevMaxCount=系統中設備已達到許可上限，無法修改設定！
#自定义报警事件
acc_deviceEvent_selectSound=請選取音訊檔案
acc_deviceEvent_batchSetSoundErr=批量設定報警音效異常！
acc_deviceEvent_batchSet=設定音效
acc_deviceEvent_sound=事件音效
acc_deviceEvent_exist=已存在
acc_deviceEvent_upload=重新上傳
#查询门最近发生事件
acc_doorEventLatestHappen=查詢門最近發生事件
#门禁人员信息
acc_pers_delayPassage=延長通行
#设备容量提示
acc_dev_usageConfirm=出現容量超出90%的設備
acc_dev_immediateCheck=立即檢視
acc_dev_inSoftware=軟體中
acc_dev_inFirmware=韌體中
acc_dev_get=檢視
acc_dev_getAll=檢視全部
acc_dev_loadError=加載失敗
#Reader
acc_reader_inout=出入
acc_reader_lightRule=燈亮規則
acc_reader_defLightRule=預設規則
acc_reader_encrypt=加密
acc_reader_allReaderOfCurDev=現用的設備所有讀頭
acc_reader_tip1=加密選項預設複製到同一設備的所有讀頭！
acc_reader_tip2=身份證模式選項僅支援該功能的讀頭！
acc_reader_tip3=RS485協議類型默認複製到同一設備的所有讀頭，設備重啟後生效！
acc_reader_tip4=隱藏部分人員信息選項默認複製到同一設備的所有讀頭！
acc_reader_commType=通信類型
acc_reader_commAddress=通信位址
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=韋根
acc_readerCommType_wg485=韋根/RS485
acc_readerCommType_disable=禁用
acc_readerComAddress_repeat=同一種通信位址重複
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=RS485位址
acc_readerCommType_wgAddress=韋根位址
acc_reader_macError=請輸入正確格式的Mac位址！
acc_reader_machineType=讀頭型號
acc_reader_readMode=模式
acc_reader_readMode_normal=普通模式
acc_reader_readMode_idCard=身份證模式
acc_reader_note=溫馨提示：只能選取與攝像機同一區域（{0}）的門禁設備
acc_reader_rs485Type=RS485協議類型
acc_reader_userLock=人員鎖定
acc_reader_userInfoReveal=隱藏部分人員信息
#operat
acc_operation_pwd=授權密碼
acc_operation_pwd_error=授權密碼有誤
acc_new_input_not_same=新密鑰輸入不一致
acc_op_set_keyword=授權密鑰設定
acc_op_old_key=舊授權密鑰
acc_op_new_key=新授權密鑰
acc_op_cofirm_key=授權密鑰確認
acc_op_old_key_error=舊密鑰輸入有誤
#验证方式规则
acc_verifyRule_name=規則名稱
acc_verifyRule_door=驗證模式-門
acc_verifyRule_person=驗證模式-人
acc_verifyRule_copy=複製星期一的驗證模式到其他工作日：
acc_verifyRule_tip1=請至少選取一個驗證模式！
acc_verifyRule_tip2=若果規則中包括人的驗證模式，則只能新增包括韋根讀頭的門！
acc_verifyRule_tip3=RS485讀頭只能遵循門的驗證模式，不支援優先使用人的驗證模式。
acc_verifyRule_oldVerifyMode=舊驗證方式
acc_verifyRule_newVerifyMode=新驗證方式
acc_verifyRule_newVerifyModeSelectTitle=選擇新驗證方式
acc_verifyRule_newVerifyModeNoSupportTip=不存在支持新驗證方式的設備!
#Wiegand Test
acc_wiegand_beforeCard=現用的卡的卡號長度：{0} 位（二進位），與上一張卡不相同！
acc_wiegand_curentCount=現用的卡號位數為：{0}（二進位）
acc_wiegand_card=卡
acc_wiegand_readCard=讀卡
acc_wiegand_clearCardInfo=清除卡號訊息
acc_wiegand_originalCard=原始卡號
acc_wiegand_recommendFmt=推薦格式
acc_wiegand_parityFmt=奇偶校驗格式
acc_wiegand_withSizeCode=無區位碼時自動推算區位碼
acc_wiegand_tip1=上述卡的區位碼不一致，可能不屬於同一批次。
acc_wiegand_tip2=區位碼:{0}，卡號:{1}，在原始卡號中符合出現異常。請檢查輸入的區位碼和卡號是否正確！
acc_wiegand_tip3=輸入的卡號({0})無法在原始卡號中符合到,請再次確認輸入的卡號是否正確！
acc_wiegand_tip4=輸入的區位碼({0})無法在原始卡號中符合到,請再次確認輸入的區位碼是否正確！
acc_wiegand_tip5=如果需要使用推算區位碼功能，請確保區位碼所在列均為空！
acc_wiegand_warnInfo1=更換新的卡進行讀卡操作時，請手動切換到下一張卡。
#LCD实时监控
acc_leftMenu_LCDRTMonitor=出入人員面板
acc_LCDRTMonitor_current=現用的人員訊息
acc_LCDRTMonitor_previous=歷史人員訊息
#api
acc_api_levelIdNotNull=權限組ID不能為空
acc_api_levelExist=權限組存在
acc_api_levelNotExist=權限組不存在
acc_api_areaNameNotNull=區域不能為空
acc_api_levelNotHasPerson=權限組下沒有人員
acc_api_doorIdNotNull=門ID不能為空
acc_api_doorNameNotNull=門名稱不能為空
acc_api_doorIntervalSize=開門時長需在1~254之間
acc_api_doorNotExist=門不存在
acc_api_devOffline=設備處於離線或禁用狀態
acc_api_devSnNotNull=設備SN不能為空
acc_api_timesTampNotNull=時間戳記不能為空
acc_api_openingTimeCannotBeNull=開門時長不能為空
acc_api_parameterValueCannotBeNull=參數值不能為空
acc_api_deviceNumberDoesNotExist=設備序列號不存在
acc_api_readerIdCannotBeNull=讀頭ID不能為空
acc_api_theReaderDoesNotExist=讀頭不存在
acc_operate_door_notInValidDate=當前不在遠程開門有效時間內，如需請聯繫管理員！
acc_api_doorOffline=門處於離線或停用狀態
#门禁信息自动导出
acc_autoExport_title=事件記錄自動導出
acc_autoExport_frequencyTitle=自動導出頻率
acc_autoExport_frequencyDay=每天
acc_autoExport_frequencyMonth=每月
acc_autoExport_firstDayMonth=每月第一天
acc_autoExport_specificDate=導出日期
acc_autoExport_exportModeTitle=導出模式
acc_autoExport_dailyMode=每日事件記錄
acc_autoExport_monthlyMode=每月事件記錄（上個月所有的信息和本月的事件記錄）
acc_autoExport_allMode=所有事件記錄（最多30000條事件記錄）
acc_autoExport_recipientMail=接收郵箱
#First In And Last Out
acc_inOut_inReaderName=最早進入-讀頭名稱
acc_inOut_firstInTime=最早進入時間
acc_inOut_outReaderName=最晚離開-讀頭名稱
acc_inOut_lastOutTime=最晚離開時間
#防疫参数
acc_dev_setHep=設置防疫參數
acc_dev_enableIRTempDetection=開啟紅外測溫檢測
acc_dev_enableNormalIRTempPass=體溫異常不通行
acc_dev_enableMaskDetection=開啟口罩檢測
acc_dev_enableWearMaskPass=未戴口罩不允許通行
acc_dev_tempHighThreshold=體溫高溫報警閾值
acc_dev_tempUnit=溫度單位
acc_dev_tempCorrection=體溫校正偏差值
acc_dev_enableUnregisterPass=允許未登記人員通行
acc_dev_enableTriggerAlarm=觸發外部報警功能
#联动邮件
acc_mail_temperature=体温
acc_mail_mask=是否佩戴口罩
acc_mail_unmeasured=未测量
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort全局事件
acc_digifort_chooseDigifortEvents=選擇Digifort全局事件
acc_digifort_eventExpiredTip=如果從Digifort服務器刪除了全局事件，它將以紅色顯示。
acc_digifort_checkConnection=請檢查Digifort服務器的連接信息是否正確
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=添加聯繫人
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=設置擴展參數
acc_extendParam_faceUI=界面顯示
acc_extendParam_faceParam=人臉參數
acc_extendParam_accParam=門禁參數
acc_extendParam_intercomParam=可視對講參數
acc_extendParam_volume=音量
acc_extendParam_identInterval=識別間隔(毫秒)
acc_extendParam_historyVerifyResult=顯示歷史驗證結果
acc_extendParam_macAddress=顯示MAC地址
acc_extendParam_showIp=顯示IP地址
acc_extendParam_24HourFormat=顯示24小時格式
acc_extendParam_dateFormat=日期格式
acc_extendParam_1NThreshold=1:N閾值
acc_extendParam_facePitchAngle=人臉俯仰角度
acc_extendParam_faceRotationAngle=人臉旋轉角度
acc_extendParam_imageQuality=圖像質量
acc_extendParam_miniFacePixel=最小人臉像素
acc_extendParam_biopsy=開啟活體檢測
acc_extendParam_showThermalImage=顯示熱力圖像
acc_extendParam_attributeAnalysis=開啟屬性分析
acc_extendParam_temperatureAttribute=體溫檢測屬性
acc_extendParam_maskAttribute=口罩檢測屬性
acc_extendParam_minTemperature=體溫檢測閾值下限
acc_extendParam_maxTemperature=體溫檢測閾值上限
acc_extendParam_gateMode=閘機模式
acc_extendParam_qrcodeEnable=啟用二維碼功能
#可视对讲
acc_dev_intercomServer=可視對講服務地址
acc_dev_intercomPort=可視對講服務埠
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=同步權限組
# 夏令时名称
acc_dsTimeUtc_none=不設定
acc_dsTimeUtc_AreaNone=該區域沒有夏令時
acc_dsTimeUtc1000_0=坎培拉，墨爾本，雪梨
acc_dsTimeUtc1000_1=霍巴特
acc_dsTimeUtc_0330_0=紐芬蘭
acc_dsTimeUtc_1000_0=阿留申群島
acc_dsTimeUtc_0200_0=中大西洋-舊用
acc_dsTimeUtc0930_0=阿德萊德
acc_dsTimeUtc_0100_0=亞速爾群島
acc_dsTimeUtc_0400_0=大西洋時間（加拿大）
acc_dsTimeUtc_0400_1=聖地牙哥
acc_dsTimeUtc_0400_2=亞松森
acc_dsTimeUtc_0300_0=格陵蘭
acc_dsTimeUtc_0300_1=聖皮爾和密克隆群島
acc_dsTimeUtc0200_0=基希訥烏
acc_dsTimeUtc0200_1=赫爾辛基，基輔，裏加，索菲亞，塔林，維爾紐斯
acc_dsTimeUtc0200_2=雅典，布加勒斯特
acc_dsTimeUtc0200_3=耶路撒冷
acc_dsTimeUtc0200_4=安曼
acc_dsTimeUtc0200_5=貝魯特
acc_dsTimeUtc0200_6=大馬士革
acc_dsTimeUtc0200_7=加沙，希伯倫
acc_dsTimeUtc0200_8=朱巴
acc_dsTimeUtc_0600_0=中部時間（美國和加拿大）
acc_dsTimeUtc_0600_1=瓜達拉哈拉，墨西哥城，蒙特雷
acc_dsTimeUtc_0600_2=復活節島
acc_dsTimeUtc1300_0=薩摩亞群島
acc_dsTimeUtc_0500_0=哈瓦那
acc_dsTimeUtc_0500_1=東部時間（美國和加拿大）
acc_dsTimeUtc_0500_2=海地
acc_dsTimeUtc_0500_3=印地安那州（東部）
acc_dsTimeUtc_0500_4=特克斯和凱科斯群島
acc_dsTimeUtc_0800_0=太平洋時間（美國和加拿大）
acc_dsTimeUtc_0800_1=下加利福尼亞州
acc_dsTimeUtc0330_0=德黑蘭
acc_dsTimeUtc0000_0=都柏林，愛丁堡，里斯本，倫敦
acc_dsTimeUtc1200_0=斐濟
acc_dsTimeUtc1200_1=彼得羅巴甫洛夫斯克-堪察加-舊用
acc_dsTimeUtc1200_2=奧克蘭，惠靈頓
acc_dsTimeUtc1100_0=諾福克島
acc_dsTimeUtc_0700_0=奇瓦瓦，拉巴斯，馬薩特蘭
acc_dsTimeUtc_0700_1=山地時間（美國和加拿大）
acc_dsTimeUtc0100_0=貝爾格勒，布拉提斯拉瓦，布達佩斯，盧布亞納，布拉格
acc_dsTimeUtc0100_1=塞拉耶佛，斯高彼亞，華沙，札格雷布
acc_dsTimeUtc0100_2=卡薩布蘭卡
acc_dsTimeUtc0100_3=布魯塞爾，哥本哈根，馬德里，巴黎
acc_dsTimeUtc0100_4=阿姆斯特丹，柏林，伯恩，羅馬，斯德哥爾摩，維也納
acc_dsTimeUtc_0900_0=阿拉斯加
#安全点(muster point)
acc_leftMenu_accMusterPoint=緊急疏散點
acc_musterPoint_activate=激活
acc_musterPoint_addDept=添加部門
acc_musterPoint_delDept=刪除部門
acc_musterPoint_report=安全點報表
acc_musterPointReport_sign=手動簽到
acc_musterPointReport_generate=生成報表
acc_musterPoint_addSignPoint=添加簽到點
acc_musterPoint_delSignPoint=刪除簽到點
acc_musterPoint_selectSignPoint=請添加簽到點！
acc_musterPoint_signPoint=簽到點
acc_musterPoint_delFailTip=存在已經啟動的緊急疏散點，不能刪除！
acc_musterPointReport_enterTime=進入時間
acc_musterPointReport_dataAnalysis=數據分析
acc_musterPointReport_safe=安全
acc_musterPointReport_danger=危險
acc_musterPointReport_signInManually=手動打卡
acc_musterPoint_editTip=緊急疏散點已激活，不能編輯！
acc_musterPointEmail_total=應簽到總人數
acc_musterPointEmail_safe=已簽到人數（安全）
acc_musterPointEmail_dangerous=未簽到人數（危險）
acc_musterPoint_messageNotification=啟動時進行消息通知
acc_musterPointReport_sendEmail=定時推送報表
acc_musterPointReport_sendInterval=發送間隔
acc_musterPointReport_sendTip=請確保勾選的通知管道配寘成功，否則通知將無法正常發送！
acc_musterPoint_mailSubject=緊急集合通知
acc_musterPoint_mailContent=請立即在“{0}”集合，並在“{1}”設備上簽到，謝謝！
acc_musterPointReport_mailHead=你好，這是一份緊急情況報告。請查閱。
acc_musterPoint_visitorsStatistics=統計訪客人員
# 报警监控
acc_alarm_priority=優先順序
acc_alarm_total=總共
acc_alarm_today=今日記錄
acc_alarm_unhandled=未確認
acc_alarm_inProcess=處理中
acc_alarm_acknowledged=已確認
acc_alarm_top5=報警事件前五名
acc_alarm_monitoringTime=監控時間
acc_alarm_history=報警處理歷史
acc_alarm_acknowledgement=處理記錄
acc_alarm_eventDescription=事件詳情
acc_alarm_acknowledgeText=選中後將會發送報警事件詳情郵件到指定的郵箱
acc_alarm_emailSubject=新增報警事件處理記錄
acc_alarm_mute=靜音
acc_alarm_suspend=暫停
acc_alarm_confirmed=該事件已被確認
acc_alarm_list=報警記錄
#ntp
acc_device_setNTPService=NTP服務器設置
acc_device_setNTPServiceTip=輸入多個服務器地址，請以逗號(,)或分號(;)隔開。
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=超級用戶不受時間段、反潜、互鎖、多卡開門、節假日、時間反潜等限制，有很高的開門優先順序。
acc_editPerson_delayPassageTip=勾選後，可延長該人員通行時的等待時間。 適用於殘疾人或者其他有特別需要的人群。
acc_editPerson_disabledTip=勾選後，將臨時禁用該人員的門禁許可權。
#门禁向导
acc_guide_title=門禁模塊操作指引
acc_guide_addPersonTip=需先添加人員和相應的憑證（人臉或指紋或卡或手掌或密碼）；若已添加，則直接跳過此步驟
acc_guide_timesegTip=請配置有效的開門時間段
acc_guide_addDeviceTip=請添加對應的設備作為門禁點
acc_guide_addLevelTip=添加門禁權限
acc_guide_personLevelTip=給人員分配對應的門禁權限
acc_guide_rtMonitorTip=實時查看門禁記錄
acc_guide_rtMonitorTip2=添加所屬區域和對應門後實时查看門禁記錄
#查看区域内人员
acc_zonePerson_cleanCount=清除人員進出統計數量
acc_zonePerson_inCount=人員進入數量統計
acc_zonePerson_outCount=人員離開數量統計
#biocv460
acc_device_validFail=用戶名或密碼不正確，驗證失敗！
acc_device_pwdRequired=只能輸入最大6比特整數