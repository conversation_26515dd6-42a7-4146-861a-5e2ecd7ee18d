#[1]左侧菜单
acc_module=출입통제
acc_leftMenu_accDev=출입장치
acc_leftMenu_auxOut=외부 출력
acc_leftMenu_dSTime=서머타임제
acc_leftMenu_access=출입통제 규칙
acc_leftMenu_door=출입문 설정
acc_leftMenu_accRule=출입통제 규칙
acc_leftMenu_interlock=인터락
acc_leftMenu_antiPassback=안티패스백
acc_leftMenu_globalLinkage=글로벌 트리거 설정
acc_leftMenu_firstOpen=관리자 인증 후 개방 모드
acc_leftMenu_combOpen=그룹인증 조합 (인증방식)
acc_leftMenu_personGroup=그룹인증 조합 (그룹/사용자)
acc_leftMenu_level=출입 권한 설정 (출입문)
acc_leftMenu_electronicMap=E-Map
acc_leftMenu_personnelAccessLevels=사용자 출입 권한
acc_leftMenu_searchByLevel=출입 권한별
acc_leftMenu_searchByDoor=출입문별 사용자 확인
acc_leftMenu_expertGuard=고급 기능
acc_leftMenu_zone=출입통제 구역
acc_leftMenu_readerDefine=장치 설정
acc_leftMenu_gapbSet=글로벌 안티패스백
acc_leftMenu_whoIsInside=구역별 사용자 목록
acc_leftMenu_whatRulesInside=적용 규칙
acc_leftMenu_occupancy=인원수 제한
acc_leftMenu_route=경로 제어
acc_leftMenu_globalInterlock=글로벌 인터락 (설정)
acc_leftMeue_globalInterlockGroup=글로벌 인터락 (그룹)
acc_leftMenu_dmr=비활성화 규칙
acc_leftMenu_personLimit=출입제한 설정
acc_leftMenu_verifyModeRule=인증모드
acc_leftMenu_verifyModeRulePersonGroup=인증모드 사용자 설정
acc_leftMenu_extDev=I / O 보드
acc_leftMenu_firstInLastOut=사용자 ID 검색
acc_leftMenu_accReports=출입통제 보고서
#[3]门禁时间段
acc_timeSeg_entity=스케줄
acc_timeSeg_canNotDel=스케줄이 설정되어있어서 삭제할 수 없습니다
#[4]门禁设备--公共的在common中
acc_common_ruleName=규칙명
acc_common_hasBeanSet=설정 완료
acc_common_notSet=설정되지 않았습니다
acc_common_hasBeenOpened=열림
acc_common_notOpened=열리지 않았습니다
acc_common_partSet=일부 항목
acc_common_linkageAndApbTip=일반/글로벌 트리거 또는 일반/글로벌 안티패스백을 동시에 설정 시 오류가 발생할 수 있습니다
acc_common_vidlinkageTip=카메라 연동 설정을 다시 확인하십시오
acc_common_accZoneFromTo=동일하게 설정할 수 없습니다
acc_common_logEventNumber=No
acc_common_bindOrUnbindChannel=카메라 연결 / 연결 해제
acc_common_boundChannel=연결된 카메라
#设备信息
acc_dev_iconType=장치 유형
acc_dev_carGate=주차 차단기
acc_dev_channelGate=플랩 게이트
acc_dev_acpType=출입통제 유형
acc_dev_oneDoorACP=1 Door 컨트롤러
acc_dev_twoDoorACP=2 Door 컨트롤러
acc_dev_fourDoorACP=4 Door 컨트롤러
acc_dev_onDoorACD=단독형 장치
acc_dev_switchToTwoDoorTwoWay=양방향으로 설정
acc_dev_addDevConfirm2=장치가 연결 되었지만 컨트롤러 유형이 일치하지 않습니다 / {0} 컨트롤러 유형으로 변경하여 추가 하시겠습니까?
acc_dev_addDevConfirm4=장치가 연결 되었지만 컨트롤러 유형이 일치하지 않습니다 / 계속 하시겠습니까?
acc_dev_oneMachine=단독형 장치
acc_dev_fingervein=손가락 정맥
acc_dev_control=컨트롤러
acc_dev_protocol=프로토콜 유형
acc_ownedBoard=설정된 컨트롤러
#设备操作
acc_dev_start=시작
acc_dev_accLevel=출입 권한
acc_dev_timeZoneAndHoliday=출입시간 / 휴일
acc_dev_linkage=트리거
acc_dev_doorOpt=출입문 설정
acc_dev_firstPerson=관리자 인증 후 개방 모드
acc_dev_multiPerson=그룹인증 조합
acc_dev_interlock=인터락
acc_dev_antiPassback=안티패스백
acc_dev_wiegandFmt=Wiegand 형식
acc_dev_outRelaySet=외부 출력 설정
acc_dev_backgroundVerifyParam=연결 끊김 시 인증방식 설정
acc_dev_getPersonInfoPrompt=사용자 정보가 서버에 없을 경우 생체 정보를 가져올 수 없습니다 / 실행 하시겠습니까?
acc_dev_getEventSuccess=기록 가져오기 완료
acc_dev_getEventFail=기록 가져오기 실패
acc_dev_getInfoSuccess=정보 가져오기
acc_dev_getInfoXSuccess={0} 완료
acc_dev_getInfoFail=정보 가져오기 실패
acc_dev_updateExtuserInfoFail=지정된 경로 내 사용자 정보를 가져올 수 없습니다 / 다시 확인 하십시오
acc_dev_getPersonCount=사용자 정보 가져오기
acc_dev_getFPCount=지문 정보 가져오기
acc_dev_getFVCount=손가락 정맥 정보 가져오기
acc_dev_getFaceCount=얼굴 정보 가져오기
acc_dev_getPalmCount=손바닥 정맥 정보 가져오기
acc_dev_getBiophotoCount=얼굴 사진 정보 가져오기
acc_dev_noData=장치에 등록된 정보가 없습니다
acc_dev_noNewData=장치 내 신규 기록이 없습니다
acc_dev_softLtDev=장치보다 서버에 더 많은 사용자가 등록되어 있습니다
acc_dev_personCount=사용자 수 :
acc_dev_personDetail=세부 사항 :
acc_dev_softEqualDev=서버와 장치에 등록된 사용자 수가 동일합니다
acc_dev_softGtDev=장치에 등록된 사용자 수가 서버 사용자 수보다 많습니다
acc_dev_cmdSendFail=명령어 전송 오류 / 다시 전송 하십시오
acc_dev_issueVerifyParam=백그라운드 인증 설정
acc_dev_verifyParamSuccess=백그라운드 인증 설정이 적용되었습니다
acc_dev_backgroundVerify=백그라운드 인증 여부
acc_dev_selRightFile=파일을 선택 하십시오
acc_dev_devNotOpForOffLine=오프라인 상태 입니다 / 연결하고 다시 시도 하세요
acc_dev_devNotSupportFunction=해당 기능을 지원하지 않습니다
acc_dev_devNotOpForDisable=장치가 비활성화 상태입니다
acc_dev_devNotOpForNotOnline=장치가 오프라인이거나 비활성화 상태입니다
acc_dev_getPersonInfo=사용자 정보 가져오기
acc_dev_getFPInfo=지문 정보 가져오기
acc_dev_getFingerVeinInfo=손가락 정맥 정보 가져오기
acc_dev_getPalmInfo=손바닥 정맥 정보 가져오기
acc_dev_getBiophotoInfo=얼굴 사진 정보 가져오기
acc_dev_getIrisInfo=홍채 정보 얻기
acc_dev_disable=비활성화로 설정되어 있습니다 / 다시 선택 하세요
acc_dev_offlineAndContinue=오프라인 상태 입니다 / 계속 하시겠습니까?
acc_dev_offlineAndSelect=연결되어 있지 않음
acc_dev_opAllDev=모든 장치
acc_dev_opOnlineDev=연결된 장치
acc_dev_opException=예외 처리
acc_dev_exceptionAndConfirm=장치 연결시간 초과 / 네트워크 상태를 확인 하십시오
acc_dev_getFaceInfo=얼굴 정보 가져오기
acc_dev_selOpDevType=장치 유형 선택 :
acc_dev_hasFilterByFunc=기능을 지원하는 장치만 표시
acc_dev_masterSlaveMode=RS485 Master-Slave 모드
acc_dev_master=Master
acc_dev_slave=Slave
acc_dev_modifyRS485Addr=RS485 설정
acc_dev_rs485AddrTip=1~ 63 사이의 숫자를 입력 하세요
acc_dev_enableFeature=백그라운드 기능 활성화
acc_dev_disableFeature=백그라운드 인증을 사용하지 않는 장치
acc_dev_getCountOnly=가져올 수 있는 정보수량 확인
acc_dev_queryDevPersonCount=관리자 수 확인
acc_dev_queryDevVolume=장치 저장 수량 확인
acc_dev_ruleType=메뉴
acc_dev_contenRule=규칙 세부 정보
acc_dev_accessRules=규칙 설정 값 확인
acc_dev_ruleContentTip=규칙명 "|" 구분
acc_dev_rs485AddrFigure=RS485 주소(그림)
acc_dev_addLevel=권한
acc_dev_personOrFingerTanto=사용자 또는 지문의 제한 수량이 초과하여 동기화에 실패했습니다
acc_dev_personAndFingerUnit=(번호)
acc_dev_setDstime=서머타임제 설정
acc_dev_setTimeZone=표준시간 설정 (UTC 설정)
acc_dev_selectedTZ=시간대 선택
acc_dev_timeZoneSetting=스케줄 설정
acc_dev_timeZoneCmdSuccess=스케줄 명령 전송
acc_dev_enableDstime=서머타임제 사용
acc_dev_disableDstime=서머타임제 사용 안함
acc_dev_timeZone=시간 설정
acc_dev_dstSettingTip=서머타임제 설정 중입니다
acc_dev_dstDelTip=서머타임제 기능 삭제 중입니다
acc_dev_enablingDst=서머타임제 사용
acc_dev_dstEnableCmdSuccess=서머타임제 사용
acc_dev_disablingDst=서머타임제 비활성화
acc_dev_dstDisableCmdSuccess=서머타임제 비활성화 명령 전송
acc_dev_dstCmdSuccess=서머타임제 명령 보내기
acc_dev_usadst=북미 서머타임제
acc_dev_notSetDst=설정 없음
acc_dev_selectedDst=서머타임제 선택
acc_dev_configMasterSlave=-Master-Slave 구성
acc_dev_hasFilterByUnOnline=활성화 장치만 표시
acc_dev_softwareData=서버와 수량 정보가 다른 경우 데이터를 동기화 하십시오
acc_dev_disabled=비활성화된 장치입니다
acc_dev_offline=네트워크 상태를 확인해주세요
acc_dev_noSupport=해당 장치는 기능을 지원하지 않습니다
acc_dev_noRegDevTip=등록장치로 설정되지 않았습니다 / 장치에 등록된 사용자를 가져오시겠습니까?
acc_dev_noOption=조건에 해당하는 옵션이 없습니다
acc_dev_devFWUpdatePrompt=현재 장치는 사용자 비활성화/기간 설정 기능을 사용할 수 없습니다(사용자 매뉴얼 참조)
acc_dev_panelFWUpdatePrompt=현재 장치는 사용자 비활성화/기간 설정 기능을 사용할 수 없습니다 / 펌웨어 업그레이드 하시겠습니까?
acc_dev_sendEventCmdSuccess=명령 삭제 전송
acc_dev_tryAgain=다시 시도하십시오
acc_dev_eventAutoCheckAndUpload=자동 검사 및 기록 가져오기
acc_dev_eventUploadStart=기록 가져오기 시작
acc_dev_eventUploadEnd=기록 가져오기 완료
acc_dev_eventUploadFailed=기록 가져오기 실패
acc_dev_eventUploadPrompt=펌웨어 버전이 낮습니다 / 업그레이드 전 해당 작업을 수행 하십시오 :
acc_dev_backupToSoftware=서버에 데이터 백업
acc_dev_deleteEvent=장치 기록 삭제
acc_dev_upgradePrompt=펌웨어 버전이 낮아 오류가 발생될 수 있습니다 / 업그레이드 전 데이터를 백업하십시오
acc_dev_conflictCardNo={0} 사용자에게 카드 번호가 등록되어 있습니다
acc_dev_rebootAfterOperate=작업 완료 / 장치를 재부팅 합니다
acc_dev_baseOptionTip=기본 설정 값 가져오기 실패
acc_dev_funOptionTip=기능 설정 값 가져오기 실패
acc_dev_sendComandoTip=설정 값 가져오기 명령 실패
acc_dev_noC3LicenseTip=해당 유형 장치 ({0})를 추가 할 수 없습니다 / 제조사에 문의하세요
acc_dev_combOpenDoorTip=({0}) 그룹인증 조합과 백그라운드 인증은 동시에 사용할 수 없습니다
acc_dev_combOpenDoorPersonCountTip=그룹 {0}은(는) {1}명 이상 설정할 수 없습니다
acc_dev_addDevTip=해당 기능은 Pull 장치에만 적용할 수 있습니다
acc_dev_addError=장치 추가 오류가 발생했습니다 / 오류: ({0})
acc_dev_updateIPAndPortError=서버 IP / 포트 업데이트 실패
acc_dev_transferFilesTip=인증이 완료되어 파일을 전송합니다
acc_dev_serialPortExist=시리얼 포트 설정
acc_dev_isExist=장치가 존재합니다
acc_dev_description=설명
acc_dev_searchEthernet=이더넷 장치 검색
acc_dev_searchRS485=RS485 장치 검색
acc_dev_rs485AddrTip1=RS485 시작 주소는 종료 주소보다 크게 설정할 수 없습니다
acc_dev_rs485AddrTip2=RS485의 검색 범위는 20 미만이어야 합니다
acc_dev_clearAllCmdCache=전체 명령 삭제
acc_dev_authorizedSuccessful=설정 완료
acc_dev_authorize=설정
acc_dev_registrationDevice=등록
acc_dev_setRegistrationDevice=등록 장치 설정
acc_dev_mismatchedDevice=장치를 연결할 수 없습니다 / 장치 S/N 오류
acc_dev_pwdStartWithZero=통신 비밀번호는 [0]으로 시작할 수 없습니다
acc_dev_maybeDisabled=현재 라이선스로 {0}개의 출입문을 추가할 수 있습니다 / 출입문 수량을 초과한 후 신규 추가 출입문은 비활성화됩니다
acc_dev_Limit=등록된 장치 수량이 라이선스를 초과 하였습니다
acc_dev_selectDev=장치를 선택 하십시오
acc_dev_cannotAddPullDevice=Pull 장치를 추가할 수 없습니다
acc_dev_notContinueAddPullDevice=시스템에 {0} Pull 장치 등록되어 있으며 추가 할 수 없습니다
acc_dev_deviceNameNull=장치 모델명을 입력하십시오
acc_dev_commTypeErr=통신 방식이 일치하지 않습니다
acc_dev_inputDomainError=도메인 주소를 입력 하십시오
acc_dev_levelTip=권한에 등록된 사용자가 5,000명을 초과하여 추가할 수 없습니다
acc_dev_auxinSet=외부 입력 설정
acc_dev_verifyModeRule=인증모드 규칙
acc_dev_netModeWired=유선
acc_dev_netMode4G=무선 (4G)
acc_dev_netModeWifi=무선 (WiFi)
acc_dev_updateNetConnectMode=네트워크 통신 전환
acc_dev_wirelessSSID=무선 SSID
acc_dev_wirelessKey=무선통신 키
acc_dev_searchWifi=WiFi 검색
acc_dev_testNetConnectSuccess=통신 연결 성공 했습니다 / 전환 하시겠습니까?
acc_dev_testNetConnectFailed=연결이 불안정하여 통신 오류 발생
acc_dev_signalIntensity=신호 강도
acc_dev_resetSearch=다시 검색
acc_dev_addChildDevice=하위 장치 추가
acc_dev_modParentDevice=마스터 장치 변경
acc_dev_configParentDevice=마스터 장치 설정
acc_dev_lookUpChildDevice=하위 장치 확인
acc_dev_addChildDeviceTip=하위 장치에 권한 부여가 필요합니다
acc_dev_maxSubCount=설정된 장치 수량이 {0} 개를 초과 하였습니다
acc_dev_seletParentDevice=마스터 장치를 선택하십시오
acc_dev_networkCard=네트워크 카드
acc_dev_issueParam=설정 동기화 (사용자 지정)
acc_dev_issueMode=동기화 모드
acc_dev_initIssue=펌웨어 초기화 데이터 전송(3030)
acc_dev_customIssue=데이터 동기화 (사용자 지정)
acc_dev_issueData=데이터
acc_dev_parent=Master 장치
acc_dev_parentEnable=Master 장치 비활성화
acc_dev_parentTips=Master 장치를 수정하면 전체 데이터가 삭제되어 다시 설정해야 합니다
acc_dev_addDevIpTip=IP 주소는 서버 IP와 동일하게 설정할 수 없습니다
acc_dev_modifyDevIpTip=기존 장치 IP와 동일할 수 없습니다
acc_dev_setWGReader=Wiegand 장치 설정
acc_dev_selectReader=장치를 선택하십시오
acc_dev_IllegalDevice=잘못된 장치
acc_dev_syncTimeWarnTip=Master 장치 시간 기준으로 동기화 됩니다
acc_dev_setTimeZoneWarnTip=해당 장치의 표준 시간은 마스터 장치에서 동기화해야 합니다
acc_dev_setDstimeWarnTip=Master 장치에 설정된 서머타임제로 시간 동기화 됩니다
acc_dev_networkSegmentSame=두 개의 랜 카드에서 동일한 네트워크 세그먼트를 사용할 수 없습니다
acc_dev_upgradeProtocolNoMatch=업그레이드 파일이 일치하지 않습니다
acc_dev_ipAddressConflict=등록된 IP 입니다 / IP 주소를 수정 한 후 다시 추가 하십시오
acc_dev_checkServerPortTip=서버 포트와 시스템 통신 포트가 일치하지 않아 추가할 수 없습니다 / 계속하시겠습니까?
acc_dev_clearAdmin=관리자 권한 삭제
acc_dev_setDevSate=장치 입/출력 상태 설정
acc_dev_sureToClear=장치 관리자 권한을 삭제 하시겠습니까?
acc_dev_hostState=Master 장치 상태
acc_dev_regDeviceTypeTip=해당 장치는 추가 할 수 없습니다
acc_dev_extBoardType=컨트롤러 유형
acc_dev_extBoardTip=설정 후 장치를 재부팅해야 적용됩니다
acc_dev_extBoardLimit=이 장치는 {0}개의  I/O 보드를 추가 할 수 있습니다
acc_dev_replace=장치 교체
acc_dev_replaceTip=교체 후 기존 장치는 동작하지 않습니다
acc_dev_replaceTip1=교체 후 데이터를 동기화하십시오
acc_dev_replaceTip2=교체 장치 모델이 동일하지 않습니다
acc_dev_replaceTip3=교체 장치가 기존 장치와 동일한 서버 주소 및 포트를 설정했는지 확인하세요!
acc_dev_replaceFail=장치 머신 유형이 일치하지 않아 교체할 수 없습니다!
acc_dev_notApb=이 장치는 문이나 헤드 대잠수함을 할 수 없습니다
acc_dev_upResourceFile=에셋 파일 업로드
acc_dev_playOrder=재생 순서
acc_dev_setFaceServerInfo=얼굴 백엔드 비교 매개변수 설정
acc_dev_faceVerifyMode=얼굴 비교 모드
acc_dev_faceVerifyMode1=로컬 비교
acc_dev_faceVerifyMode2=백엔드 비교
acc_dev_faceVerifyMode3=로컬 비교 우선 순위
acc_dev_faceBgServerType=얼굴 백엔드 서버 유형
acc_dev_faceBgServerType1=소프트웨어 플랫폼 서비스
acc_dev_faceBgServerType2=타사 서비스
acc_dev_isAccessLogic=액세스 제어 논리 유효성 검사 사용
#[5]门-其他关联的也复用此处
acc_door_entity=출입문
acc_door_number=출입문 번호
acc_door_name=출입문명
acc_door_activeTimeZone=출입 가능시간
acc_door_passageModeTimeZone=개방 모드 스케줄
acc_door_setPassageModeTimeZone=출입 가능 시간 설정
acc_door_notPassageModeTimeZone=출입 제한 시간 설정
acc_door_lockOpenDuration=출입문 개방 시간
acc_door_entranceApbDuration=중복출입 제한 시간
acc_door_sensor=출입문 센서
acc_door_sensorType=출입문 센서 유형
acc_door_normalOpen=N/O
acc_door_normalClose=N/C
acc_door_sensorDelay=설정시간 이상 개방 시 알람
acc_door_closeAndReverseState=출입문 상태 반대 설정
acc_door_hostOutState=Master 상태
acc_door_slaveOutState=Slave 상태
acc_door_inState=입력
acc_door_outState=출력
acc_door_requestToExit=잠금 모드
acc_door_withoutUnlock=활성화 
acc_door_unlocking=비활성화
acc_door_alarmDelay=설정시간 이상 강제 개방 시 알람
acc_door_duressPassword=협박 비밀번호
acc_door_currentDoor=현재 출입문
acc_door_allDoorOfCurDev=현재 장치의 모든 출입문
acc_door_allDoorOfAllDev=전체 장치의 모든 출입문
acc_door_allDoorOfAllControlDev=전체 장치의 모든 출입문
acc_door_allDoorOfAllStandaloneDev=전체 장치의 모든 출입문
acc_door_allWirelessLock=전체 무선 잠금 장치
acc_door_max6BitInteger=최대 6자
acc_door_direction=출입 방향
acc_door_onlyInReader=입실 리더기 전용
acc_door_bothInAndOutReader=입/퇴실 리더기
acc_door_noDoor=출입문을 추가 하십시오
acc_door_nameRepeat=등록된 출입문명 입니다
acc_door_duressPwdError=협박 비밀번호는 사용자 비밀번호와 다르게 설정해야 합니다
acc_door_urgencyStatePwd={0} 자리 숫자를 입력 하십시오
acc_door_noDevOnline=네트워크 연결이 끊어졌거나 카드 인증 모드가 아닙니다
acc_door_durationLessLock=개방 시 알람 시간은 개방 시간보다 작을 수 없습니다
acc_door_lockMoreDuration=개방 시간은 개방 시 알람 시간보다 클 수 없습니다
acc_door_lockAndExtLessDuration=개방 시간과 센서 감지 지연 시간의 합은 개방 알람 시간보다 작게 설정 하십시오
acc_door_noDevTrigger=조건에 해당하는 장치가 없습니다
acc_door_relay=릴레이
acc_door_pin=Pin
acc_door_selDoor=출입문 선택
acc_door_sensorStatus=출입문 센서 ({0})
acc_door_sensorDelaySeconds=출입문 센서 지연 시간 ({0}초)
acc_door_timeSeg=시간대 ({0})
acc_door_combOpenInterval=그룹 인증 시간 간격
acc_door_delayOpenTime=출입문 개방 지연
acc_door_extDelayDrivertime=센서 감지 지연
acc_door_enableAudio=알람 활성화
acc_door_disableAudio=알람 비활성화
acc_door_lockAndExtDelayTip=개방 시간과 센서 지연 시간의 합은 254초 이하로 설정 하십시오
acc_door_disabled=비활성화 출입문은 설정할 수 없습니다
acc_door_offline=오프라인 출입문은 설정할 수 없습니다
acc_door_notSupport=해당 출입문은 기능을 지원하지 않습니다
acc_door_select=출입문을 선택 하십시오
acc_door_pushMaxCount=서버에 {0} 개 출입문이 있으며 라이선스를 초과하였습니다
acc_door_outNumber=현재 라이선스는 {0} 개 출입문을 설정할 수 있습니다
acc_door_latchTimeZone=잠금 모드 스케줄
acc_door_wgFmtReverse=카드번호 역방향
acc_door_allowSUAccessLock=잠금 시 관리자 인증 허용
acc_door_verifyModeSinglePwd=암호는 독립적인 인증으로 사용할 수 없습니다!
acc_door_doorPassword=문 여는 비밀번호
#辅助输入
acc_auxIn_timeZone=외부 입력 시 스케줄
#辅助输出
acc_auxOut_passageModeTimeZone=개방 모드 스케줄
acc_auxOut_disabled=외부 출력이 비활성화 상태입니다
acc_auxOut_offline=외부 출력이 오프라인 상태입니다
#[8]门禁权限组
acc_level_doorGroup=출입문 목록
acc_level_openingPersonnel=개방 사용자 목록
acc_level_noDoor=장치가 없습니다 / 장치를 먼저 추가 하십시오
acc_level_doorRequired=출입문을 선택하십시오
acc_level_doorCount=출입문 갯수
acc_level_doorDelete=출입문 삭제
acc_level_isAddDoor=출입 권한에 출입문을 설정 하시겠습니까?
acc_level_master=기본 권한
acc_level_noneSelect=출입 그룹을 추가하십시오
acc_level_useDefaultLevel=출입 권한을 신규 부서에 적용 하시겠습니까
acc_level_persAccSet=사용자 출입통제 설정
acc_level_visUsed={0} 가 방문자 기능에 설정되어 있어 삭제할 수 없습니다
acc_level_doorControl=출입문 제어
acc_level_personExceedMax=현재 권한 그룹 인원수는({0})명 이고, 선택한 권한의 그룹 최대 인원수는 ({1})명 입니다
acc_level_exportLevel=출입 권한 내보내기
acc_level_exportLevelDoor=출입 권한의 출입문 내보내기
acc_level_exportLevelPerson=출입 권한의 사용자 내보내기
acc_level_importLevel=출입 권한 가져오기
acc_level_importLevelDoor=출입 권한의 출입문 가져오기
acc_level_importLevelPerson=출입 권한의 사용자 가져오기
acc_level_exportDoorFileName=출입 권한의 출입문 정보
acc_level_exportPersonFileName=출입 권한의 사용자 정보
acc_levelImport_nameNotNull=출입 권한명은 비워둘 수 없습니다.
acc_levelImport_timeSegNameNotNull=시간대는 비워둘 수 없습니다.
acc_levelImport_areaNotExist=구역이 존재하지 않습니다!
acc_levelImport_timeSegNotExist=시간대가 존재하지 않습니다!
acc_levelImport_nameExist=출입 권한명 {0}이(가) 이미 존재합니다!
acc_levelImport_levelDoorExist=출입 권한 {0}의 출입문이 이미 존재합니다!
acc_levelImport_levelPersonExist=출입 권한 {0}의 사용자가 이미 존재합니다!
acc_levelImport_noSpecialChar=출입 권한명은 특수 문자를 포함할 수 없습니다!
#[10]首人常开
acc_firstOpen_setting=관리자 인증 후 개방 모드
acc_firstOpen_browsePerson=관리자로 설정한 사용자 목록
#[11]多人组合开门
acc_combOpen_comboName=그룹인증명
acc_combOpen_personGroupName=그룹명
acc_combOpen_personGroup=그룹인증 설정
acc_combOpen_verifyOneTime=사용자 수
acc_combOpen_eachGroupCount=그룹인증 설정
acc_combOpen_group=그룹
acc_combOpen_changeLevel=그룹인증 조합 (사용자 설정)
acc_combOpen_combDeleteGroup=그룹인증 조합이 설정되어 있습니다
acc_combOpen_ownedLevel=소속 그룹
acc_combOpen_mostPersonCount=총인원수는 5명을 초과할 수 없습니다
acc_combOpen_leastPersonCount=총인원수는 2명 이상 설정되어야 합니다
acc_combOpen_groupNameRepeat=그룹명 중복
acc_combOpen_groupNotUnique=그룹인증 조합에서 동일한 그룹을 설정할 수 없습니다
acc_combOpen_persNumErr=그룹인증 조합에 등록된 사용자수를 초과하였습니다
acc_combOpen_combOpengGroupPersonShort=사용자가 그룹인증 조합에 설정되어있습니다
acc_combOpen_backgroundVerifyTip=백그라운드 기능과 그룹인증 조합기능은 동시에 설정할 수 없습니다
#[12]互锁
acc_interlock_rule=인터락 규칙
acc_interlock_mode1Or2={0} (+) {1} 인터락
acc_interlock_mode3={0} (+) {1} (+) {2} 인터락
acc_interlock_mode4={0} (+) {1} (or) {2} (+) {3} 인터락
acc_interlock_mode5={0} (+) {1} (+) {2} (+) {3} 인터락
acc_interlock_hasBeenSet=인터락이 설정 되었습니다
acc_interlock_group1=그룹 1
acc_interlock_group2=그룹 2
acc_interlock_ruleInfo=그룹 간 상호 잠금
acc_interlock_alreadyExists=동일한 잠금 규칙이 이미 있으므로 다시 추가하지 마십시오!
acc_interlock_groupInterlockCountErr=그룹 내 상호 잠금 규칙에는 최소 2개의 문이 필요합니다.
acc_interlock_ruleType=상호 잠금 규칙 유형
#[13]反潜
acc_apb_rules=안티패스백 규칙
acc_apb_reader={0} : 각 출입문 안티패스백 (Wiegand+RS485)
acc_apb_reader2={0} (/) {1} : 각 출입문 안티패스백
acc_apb_reader3={0} (/) {1} (/) {2} : 각 출입문 안티패스백
acc_apb_reader4={0} (/) {1} (/) {2} (/) {3} : 각 출입문 안티패스백
acc_apb_reader5={0} Door 안티패스백 퇴실
acc_apb_reader6={0} Door 안티패스백 입실
acc_apb_reader7=모든 리더기 안티패스백 (Wiegand)
acc_apb_twoDoor={0} (+) {1} 리더기 안티패스백
acc_apb_fourDoor={0} (+) {1} (/) {2} (+) {3} 리더기 안티패스백
acc_apb_fourDoor2={0} (or) {1} (+) {2} (or) {3} 리더기 안티패스백
acc_apb_fourDoor3={0} (+) {1} (or) {2} 리더기 안티패스백
acc_apb_fourDoor4={0} (+) {1} (or) {2} (or) {3} 리더기 안티패스백
acc_apb_hasBeenSet=안티패스백 설정
acc_apb_conflictWithGapb=글로벌 안티패스백으로 설정되어 있어 추가할 수 없습니다
acc_apb_conflictWithApb=해당 장치의 구역이 안티패스백으로 설정되어 있습니다
acc_apb_conflictWithEntranceApb=해당 장치의 구역이 안티패스백으로 설정되어 있습니다
acc_apb_controlIn=안티패스백 입실
acc_apb_controlOut=안티패스백 퇴실
acc_apb_controlInOut=안티패스백 입/퇴실
acc_apb_groupIn=그룹 가입
acc_apb_groupOut=그룹 아웃
acc_apb_reverseName=의 역방향 대잠수함
acc_apb_door=문 대잠수함
acc_apb_readerHead=독두대잠
acc_apb_alreadyExists=동일한 대잠수함 규칙이 이미 존재하므로 다시 추가하지 마십시오!
#[17]电子地图
acc_map_addDoor=출입문 추가
acc_map_addChannel=카메라 추가
acc_map_noAccess=E-Map 수정 권한이 없습니다
acc_map_noAreaAccess=해당 지역에 대한 E-map 권한이 없습니다
acc_map_imgSizeError={0} MB용량 미만의 이미지를 업로드 하십시오
#[18]门禁事件记录
acc_trans_entity=출입통제 기록
acc_trans_eventType=이벤트 유형
acc_trans_firmwareEvent=펌웨어 이벤트
acc_trans_softwareEvent=서버 이벤트
acc_trans_today=기록 (당일)
acc_trans_lastAddr=마지막 인증 장치
acc_trans_viewPhotos=이미지 확인
acc_trans_exportPhoto=이미지 내보내기
acc_trans_photo=인증 시 캡쳐 이미지
acc_trans_dayNumber=일자
acc_trans_fileIsTooLarge=선택한 범위가 커서 출력할 수 없습니다 
#[19]门禁验证方式
acc_verify_mode_onlyface=얼굴
acc_verify_mode_facefp=얼굴+지문
acc_verify_mode_facepwd=얼굴+비밀번호
acc_verify_mode_facecard=얼굴+카드
acc_verify_mode_facefpcard=얼굴+지문+카드
acc_verify_mode_facefppwd=얼굴+지문+비밀번호
acc_verify_mode_fv=손가락 정맥
acc_verify_mode_fvpwd=손가락 정맥+비밀번호
acc_verify_mode_fvcard=손가락 정맥+카드
acc_verify_mode_fvpwdcard=손가락 정맥+비밀번호+카드
acc_verify_mode_pv=손바닥 정맥
acc_verify_mode_pvcard=손바닥 정맥+카드
acc_verify_mode_pvface=손바닥 정맥+얼굴
acc_verify_mode_pvfp=손바닥 정맥+지문
acc_verify_mode_pvfacefp=손바닥 정맥+얼굴+지문
#[20]门禁事件编号
acc_eventNo_-1=없음
acc_eventNo_0=출입문 열림
acc_eventNo_1=정상 인증 [개방 모드(당일)]
acc_eventNo_2=관리자 인증 후 개방 모드 (카드)
acc_eventNo_3=그룹인증 조합 출입문 열림 (카드)
acc_eventNo_4=비상시 비밀번호 인증
acc_eventNo_5=정상 인증 [개방 모드]
acc_eventNo_6=트리거
acc_eventNo_7=알람 해제
acc_eventNo_8=출입문 개방
acc_eventNo_9=출입문 닫기
acc_eventNo_10=개방 모드 해제
acc_eventNo_11=개방 모드(당일) 설정
acc_eventNo_12=외부 출력 켜기
acc_eventNo_13=외부 출력 끄기
acc_eventNo_14=지문 인증
acc_eventNo_15=그룹인증 조합 인증 (지문)
acc_eventNo_16=개방 모드(당일) 스케줄 (지문)
acc_eventNo_17=1:1 인증 (카드+지문)
acc_eventNo_18=관리자 인증 후 개방 (지문)
acc_eventNo_19=관리자 인증 후 개방 (카드+지문)
acc_eventNo_20=인증 간격 짧음
acc_eventNo_21=출입문 비활성화 상태 (카드)
acc_eventNo_22=인증 불가능한 시간대
acc_eventNo_23=인증 실패
acc_eventNo_24=중복출입 제한 시간대
acc_eventNo_25=인터락
acc_eventNo_26=그룹인증 조합 인증 (카드)
acc_eventNo_27=미등록 카드
acc_eventNo_28=장기간 출입문 개방
acc_eventNo_29=기간 만료 (카드)
acc_eventNo_30=비밀번호 오류
acc_eventNo_31=인증 간격 짧음 (지문)
acc_eventNo_32=그룹인증 조합 (지문)
acc_eventNo_33=기간 만료 (지문)
acc_eventNo_34=미등록 지문
acc_eventNo_35=출입불가 시간 인증 (지문)
acc_eventNo_36=출입불가 시간 퇴실 버튼
acc_eventNo_37=출입문 닫기 실패 [개방 모드(당일) 스케줄]
acc_eventNo_38=카드 비활성화
acc_eventNo_39=액세스 해제
acc_eventNo_40=그룹인증 조합 실패 (지문)
acc_eventNo_41=인증모드 오류
acc_eventNo_42=Wiegand 형식 오류
acc_eventNo_43=안티패스백 인증 시간 초과
acc_eventNo_44=백그라운드 인증 실패
acc_eventNo_45=백그라운드 인증 시간 초과
acc_eventNo_47=명령 전송 실패
acc_eventNo_48=그룹인증 조합 실패 (카드)
acc_eventNo_49=출입불가 시간 인증 (비밀번호)
acc_eventNo_50=인증 간격 짧음 (비밀번호)
acc_eventNo_51=그룹인증 조합 (비밀번호)
acc_eventNo_52=그룹인증 조합 실패 (비밀번호)
acc_eventNo_53=기간 만료 (비밀번호)
acc_eventNo_100=템퍼 알람
acc_eventNo_101=협박 비밀번호 인증
acc_eventNo_102=강제 개방
acc_eventNo_103=협박 지문 인증
acc_eventNo_200=출입문 정상 개방
acc_eventNo_201=출입문 정상 닫힘
acc_eventNo_202=출입문 개방 (잠금 모드)
acc_eventNo_203=그룹인증 조합 (카드+지문)
acc_eventNo_204=개방 모드 해제
acc_eventNo_205=개방 모드(당일) 활성화 
acc_eventNo_206=장치 연결
acc_eventNo_207=비밀번호 인증
acc_eventNo_208=관리자 출입문 개방
acc_eventNo_209=잠금 모드 해제
acc_eventNo_210=화재연동 출입문 개방
acc_eventNo_211=관리자 출입문 닫기
acc_eventNo_212=엘리베이터 기능 활성화
acc_eventNo_213=엘리베이터 기능 비활성화
acc_eventNo_214=그룹인증 조합 (비밀번호)
acc_eventNo_215=관리자 인증 후 개방 모드 (비밀번호)
acc_eventNo_216=비밀번호 인증 [개방 모드(당일) 스케줄]
acc_eventNo_220=외부 입력 신호 끊김
acc_eventNo_221=외부 입력 (화재연동)
acc_eventNo_222=백그라운드 인증 성공
acc_eventNo_223=백그라운드 인증
acc_eventNo_225=외부 입력 정상
acc_eventNo_226=외부 입력 트리거
acc_newEventNo_0=출입문 개방
acc_newEventNo_1=개방 모드(당일) 스케줄 인증
acc_newEventNo_2=관리자 인증 후 개방 모드(당일) 스케줄
acc_newEventNo_3=그룹인증 조합 인증
acc_newEventNo_20=인증 간격 짧음
acc_newEventNo_21=인증 성공 (출입문 비활성화)
acc_newEventNo_26=그룹인증 조합 인증 대기
acc_newEventNo_27=미등록 사용자
acc_newEventNo_29=출입 기간 만료
acc_newEventNo_30=비밀번호 오류
acc_newEventNo_41=인증모드 오류
acc_newEventNo_43=사용자 비활성화
acc_newEventNo_44=백그라운드 인증 실패
acc_newEventNo_45=백그라운드 인증 시간 초과
acc_newEventNo_48=그룹인증 실패
acc_newEventNo_54=배터리 전압이 낮음
acc_newEventNo_55=배터리 교체 필요
acc_newEventNo_56=설정 오류
acc_newEventNo_57=백업 전원
acc_newEventNo_58=정상 개방 알람
acc_newEventNo_59=관리 오류
acc_newEventNo_60=출입문 내부에서 잠금
acc_newEventNo_61=중복 인증
acc_newEventNo_62=사용자 비활성화
acc_newEventNo_63=출입문 폐쇄
acc_newEventNo_64=잠금 모드 스케줄 (비활성화)
acc_newEventNo_65=비활성화 시간에 외부 입력 감지
acc_newEventNo_66=업그레이드 실패
acc_newEventNo_67=원격 인증 성공(장치가 승인되지 않음)
acc_newEventNo_68=높은 온도-인증 실패
acc_newEventNo_69=인증 실패(마스크 미착용)
acc_newEventNo_70=서버 통신 오류
acc_newEventNo_71=서버 응답 시간 초과
acc_newEventNo_73=QR 코드 오류
acc_newEventNo_74=QR 코드 기간 만료
acc_newEventNo_101=강제 개방 알람
acc_newEventNo_104=카드 인증 오류 알람
acc_newEventNo_105=서버와 연결 끊김
acc_newEventNo_106=주전원 꺼짐
acc_newEventNo_107=배터리 전원 꺼짐
acc_newEventNo_108=마스터 장치와 연결 실패
acc_newEventNo_109=장치 템퍼 알람
acc_newEventNo_110=연결 끊김
acc_newEventNo_112=확장 보드 오프라인
acc_newEventNo_114=화재 입력 분리(회선 감지)
acc_newEventNo_115=화재 경보 입력 단락(회선 감지)
acc_newEventNo_116=보조 입력 분리(회선 감지)
acc_newEventNo_117=보조 입력 단락(회선 감지)
acc_newEventNo_118=외출 스위치 분리(회선 감지)
acc_newEventNo_119=외출 스위치 단락(회선 감지)
acc_newEventNo_120=문 자기 분리(회선 감지)
acc_newEventNo_121=문자 합선(회선 감지)
acc_newEventNo_159=외부 입력 (개방)
acc_newEventNo_214=서버 연결
acc_newEventNo_217=마스터 장치와 연결 성공
acc_newEventNo_218=카드 인증 모드
acc_newEventNo_222=백그라운드 설정 완료
acc_newEventNo_223=백그라운드 설정
acc_newEventNo_224=외부 입력 (벨)
acc_newEventNo_227=2개 출입문 개방
acc_newEventNo_228=2개 출입문 닫기
acc_newEventNo_229=외부 출력 정상 출력
acc_newEventNo_230=외부 출력 (닫기)
acc_newEventNo_232=인증 성공
acc_newEventNo_233=출입문 폐쇄
acc_newEventNo_234=출입문 복구
acc_newEventNo_235=업그레이드 성공
acc_newEventNo_236=템퍼 알람 취소
acc_newEventNo_237=연결 장치 등록
acc_newEventNo_239=인터폰 호출
acc_newEventNo_240=통화 종료
acc_newEventNo_243=화재 경보 입력 해제
acc_newEventNo_244=화재 경보 입력 단락
acc_newEventNo_247=확장 보드 온라인
acc_newEventNo_4008=주전원 복구
acc_newEventNo_4014=소방 입력 신호가 끊기면, 끝문이 항상 열립니다
acc_newEventNo_4015=문이 이미 연결되어 있습니다
acc_newEventNo_4018=백엔드 비교 열기
acc_newEventNo_5023=소방 상태 제한 중
acc_newEventNo_5024=다중 인증 시간 초과
acc_newEventNo_5029=백엔드 비교 실패
acc_newEventNo_6005=기록 용량이 가득 찼습니다
acc_newEventNo_6006=회선 단락(RS485)
acc_newEventNo_6007=회선 단락 (웨건)
acc_newEventNo_6011=문이 오프라인 상태입니다
acc_newEventNo_6012=문 분해 경보
acc_newEventNo_6013=소방 입력 신호 촉발, 문 항상 열기
acc_newEventNo_6015=확장 장치 전원 공급 장치 재설정
acc_newEventNo_6016=복구 시스템 기본 설정
acc_newEventNo_6070=백엔드 비교 (금지된 목록)
acc_eventNo_undefined=이벤트 번호 미설정
acc_advanceEvent_500=글로벌 안티패스백 (기본)
acc_advanceEvent_501=출입제한 (시작 일자 설정)
acc_advanceEvent_502=인원수 제한 설정
acc_advanceEvent_503=글로벌 인터락
acc_advanceEvent_504=경로 설정
acc_advanceEvent_505=글로벌 안티패스백 (출입 횟수 제한)
acc_advanceEvent_506=글로벌 안티패스백 (설정 시간 초기화)
acc_advanceEvent_507=출입제한 설정 (사용 기간 설정)
acc_advanceEvent_508=출입제한 설정 (인증 횟수 제한)
acc_advanceEvent_509=백그라운드 인증 실패 (미등록 사용자)
acc_advanceEvent_510=백그라운드 인증 실패 (데이터 오류)
acc_alarmEvent_701=DMR 알람 (설정 규칙 : {0})
#[21]实时监控
acc_rtMonitor_openDoor=개방
acc_rtMonitor_closeDoor=닫힘
acc_rtMonitor_remoteNormalOpen=당일 개방 모드
acc_rtMonitor_realTimeEvent=실시간 기록
acc_rtMonitor_photoMonitor=사진 모니터링
acc_rtMonitor_alarmMonitor=알람 모니터링
acc_rtMonitor_doorState=출입문 센서
acc_rtMonitor_auxOutName=외부 출력명
acc_rtMonitor_nonsupport=지원 안함
acc_rtMonitor_lock=잠금
acc_rtMonitor_unLock=잠금 해제
acc_rtMonitor_disable=비활성화
acc_rtMonitor_noSensor=출입문 센서 없음
acc_rtMonitor_alarm=알람
acc_rtMonitor_openForce=강제 개방
acc_rtMonitor_tamper=템퍼
acc_rtMonitor_duressPwdOpen=협박 비밀번호 개방
acc_rtMonitor_duressFingerOpen=협박 지문 개방
acc_rtMonitor_duressOpen=협박 인증
acc_rtMonitor_openTimeout=개방 응답 없음
acc_rtMonitor_unknown=알 수 없음
acc_rtMonitor_noLegalDoor=조건에 해당하는 출입문이 없습니다
acc_rtMonitor_noLegalAuxOut=조건에 해당하는 외부 출력이 없습니다
acc_rtMonitor_curDevNotSupportOp=해당 설정은 지원되지 않습니다
acc_rtMonitor_curNormalOpen=현재 정상 개방
acc_rtMonitor_whetherDisableTimeZone=당일 개방 모드로 설정되어있습니다
acc_rtMonitor_curSystemNoDoors=출입문을 추가하지 않았거나 조건에 맞는 출입문이 없습니다
acc_rtMonitor_cancelAlarm=알람 해제
acc_rtMonitor_openAllDoor=전체 출입문 개방
acc_rtMonitor_closeAllDoor=전체 출입문 닫기
acc_rtMonitor_confirmCancelAlarm=알람을 해제 하시겠습니까?
acc_rtMonitor_calcelAllDoor=전체 알람 해제
acc_rtMonitor_initDoorStateTip=사용자에게 설정된 출입문을 확인중입니다
acc_rtMonitor_alarmEvent=알람 기록
acc_rtMonitor_ackAlarm=확인
acc_rtMonitor_ackAllAlarm=전체 확인
acc_rtMonitor_ackAlarmTime=수신 확인 시간
acc_rtMonitor_sureToAckThese=해당 {0} 알람을 확인하시겠습니까 / 확인 후 모든 알람이 해제됩니다
acc_rtMonitor_sureToAckAllAlarm=전체 알람을 확인 하시겠습니까? / 확인 후 모든 알람이 해제됩니다
acc_rtMonitor_noSelectAlarmEvent=알람 기록을 확인하도록 선택하십시오
acc_rtMonitor_noAlarmEvent=알람 기록이 없습니다
acc_rtMonitor_forcefully=알람 해제 (강제 개방)
acc_rtMonitor_addToRegPerson=등록된 사용자
acc_rtMonitor_cardExist=해당 카드는 {0} 가 사용 중이어서 발급할 수 없습니다
acc_rtMonitor_opResultPrompt=전송 {0} 건 / 실패 {1} 건
acc_rtMonitor_doorOpFailedPrompt=출입문에 명령 전송이 실패했습니다 / 다시 전송하세요
acc_rtMonitor_remoteOpen=개방
acc_rtMonitor_remoteClose=닫기
acc_rtMonitor_alarmSoundClose=알람 해제
acc_rtMonitor_alarmSoundOpen=알람 사용
acc_rtMonitor_playAudio=이벤트 알람 소리
acc_rtMonitor_isOpenShowPhoto=사진 표시
acc_rtMonitor_isOpenPlayAudio=알람 발생 시 음성 출력
acc_rtm_open=원격 개방
acc_rtm_close=원격 닫기
acc_rtm_eleModule=엘리베이터
acc_cancelAlarm_fp=알람 해제 (협박 지문 열림)
acc_cancelAlarm_pwd=알람 해제 (협박 비밀번호 열림)
acc_cancelAlarm_timeOut=알람 해제 (개방 시간 초과)
#定时同步设备时间
acc_timing_syncDevTime=시간 동기화
acc_timing_executionTime=실행 시간
acc_timing_theLifecycle=실행 주기
acc_timing_errorPrompt=입력 오류 / 1~31 사이 숫자를 입력 하십시오
acc_timing_checkedSyncTime=동기화 시간을 선택하십시오
#[25]门禁报表
acc_trans_hasAccLevel=설정된 출입통제 권한
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=구역을 추가 하십시오
acc_zone_code=코드 번호
acc_zone_parentZone=상위 구역
acc_zone_parentZoneCode=상위 코드 번호
acc_zone_parentZoneName=상위 구역명
acc_zone_outside=기본 구역
#[G2]读头定义
acc_readerDefine_readerName=장치명
acc_readerDefine_fromZone=출발 구역
acc_readerDefine_toZone=도착 구역
acc_readerDefine_delInfo1=해당 장치가 다른 기능에 사용되고 있어 삭제할 수 없습니다
acc_readerDefine_selReader=장치 선택
acc_readerDefine_selectReader=장치를 추가 하십시오
acc_readerDefine_tip=사용자가 외부로 이동한 후 기록이 삭제됩니다
#[G3]全局反潜
acc_gapb_zone=글로벌 안티패스백 구역
acc_gapb_whenToResetGapb=초기화 시간 주기
acc_gapb_apbType=안티패스백 유형
acc_gapb_logicalAPB=기본 안티패스백
acc_gapb_timedAPB=1회 출입제한 (설정 시간)
acc_gapb_logicalTimedAPB=기본 안티패스백 (설정 시간)
acc_gapb_lockoutDuration=설정 시간
acc_gapb_devOfflineRule=연결 끊김 시 동작
acc_gapb_standardLevel=기존 설정 유지
acc_gapb_accessDenied=출입 제한
acc_gapb_doorControlZone=적용 장치
acc_gapb_resetStatus=안티패스백 초기화
acc_gapb_obeyAPB=안티패스백 규칙 준수
acc_gapb_isResetGAPB=안티패스백 재설정
acc_gapb_resetGAPBSuccess=안티패스백 재설정 성공
acc_gapb_resetGAPBFaile=안티패스백 재설정 실패
acc_gapb_chooseArea=글로벌 안티패스백 출입문을 다시 선택하십시오
acc_gapb_notDelInfo1=해당 출입통제 구역은 상위 출입통제 구역에 포함되어 삭제할 수 없습니다
acc_gapb_notDelInfo2=해당 출입통제 구역은 사용중이어서 삭제할 수 없습니다
acc_gapb_notDelInfo3=해당 출입통제 구역은 사용중이어서 삭제할 수 없습니다
acc_gapb_notDelInfo4=해당 출입통제 구역은 사용중이어서 삭제할 수 없습니다
acc_gapb_zoneNumRepeat=등록되어 있는 번호입니다
acc_gapb_zoneNameRepeat=등록된 항목명이 있습니다
acc_gapb_personResetGapbPre=다시 설정 하시겠습니까?
acc_gapb_personResetGapbSuffix=사용자 안티패스백 규칙을 설정 하시겠습니까?
acc_gapb_apbPrompt=출입문은 2가지 이상 설정을 할 수 없습니다
acc_gapb_occurApb=안티패스백 발생
acc_gapb_noOpenDoor=출입문을 개방하지 마세요
acc_gapb_openDoor=출입문 개방
acc_gapb_zoneNumLength=입력 초과 (최대 20자)
acc_gapb_zoneNameLength=입력 초과 (최대 30자)
acc_gapb_zoneRemarkLength=입력 초과 (최대 50자)
acc_gapb_isAutoServerMode=고급 기능은 백그라운드 기능을 설정해야합니다. / 설정하시겠습니까?
acc_gapb_applyTo=적용 사용자
acc_gapb_allPerson=전체 사용자
acc_gapb_justSelected=선택한 사용자
acc_gapb_excludeSelected=선택한 사용자 제외
#[G4]who is inside
acc_zoneInside_lastAccessTime=마지막 인증 시간
acc_zoneInside_lastAccessReader=마지막 인증 장치
acc_zoneInside_noPersonInZone=구역에 사용자가 없습니다
acc_zoneInside_noRulesInZone=구역에 설정된 규칙 없습니다
acc_zoneInside_totalPeople=전체 사용자 수
acc_zonePerson_selectPerson=사용자 또는 부서를 선택 하십시오
#[G5]路径
acc_route_name=경로명
acc_route_setting=경로 설정
acc_route_addReader=장치 추가
acc_route_delReader=장치 삭제
acc_route_defineReaderLine=장치 경로 설정
acc_route_up=위로
acc_route_down=아래로
acc_route_selReader=장치를 먼저 선택하십시오
acc_route_onlyOneOper=하나만 선택할 수 있습니다
acc_route_readerOrder=리더기 설정 순서
acc_route_atLeastSelectOne=장치를 선택하십시오
acc_route_routeIsExist=해당 경로가 이미 존재합니다
#[G6]DMR
acc_dmr_residenceTime=체류 시간
acc_dmr_setting=체류 시간 설정
#[G7]Occupancy
acc_occupancy_max=최대 인원수
acc_occupancy_min=최소 인원수
acc_occupancy_unlimit=제한 없음
acc_occupancy_note=최대 용량은 최소 용량보다 커야 합니다
acc_occupancy_containNote=최소/최대 용량을 입력하십시오
acc_occupancy_maxMinValid=0 이상의 숫자를 입력하십시오
acc_occupancy_conflict=해당 구역은 이미 설정되어 있습니다
acc_occupancy_maxMinTip=최대/최소 용량이 비어 있으면 제한이 없습니다
#card availability
acc_personLimit_zonePropertyName=출입제한 항목
acc_personLimit_useType=설정 항목
acc_personLimit_userDate=시작 일자 설정
acc_personLimit_useDays=사용 기간 설정
acc_personLimit_useTimes=인증 횟수 제한
acc_personLimit_setZoneProperty=출입제한 설정
acc_personLimit_zoneProperty=출입제한
acc_personLimit_availabilityName=출입제한 항목명
acc_personLimit_days=일
acc_personLimit_Times=횟수
acc_personLimit_noDel=해당 출입통제 구역은 사용중이어서 삭제할 수 없습니다
acc_personLimit_cannotEdit=현재 사용중이어서 수정할 수 없습니다
acc_personLimit_detail=세부사항
acc_personLimit_userDateTo=시작 날짜
acc_personLimit_addPersonRepeatTip=선택한 부서의 사용자가 출입통제 권한에 추가 되었습니다 / 부서를 다시 선택하십시오
acc_personLimit_leftTimes={0}회 남음
acc_personLimit_expired=기간 만료
acc_personLimit_unused=사용 안함
#全局互锁
acc_globalInterlock_addGroup=그룹 추가
acc_globalInterlock_delGroup=그룹 삭제
acc_globalInterlock_refuseAddGroupMessage=그룹에 설정된 출입문은 중복할 수 없습니다
acc_globalInterlock_refuseAddlockMessage=다른 그룹에 설정된 출입문이 추가되었습니다
acc_globalInterlock_refuseDeleteGroupMessage=인터락 관련 설정을 삭제하십시오
acc_globalInterlock_isGroupInterlock=글로벌 인터락 설정
acc_globalInterlock_isAddTheDoorImmediately=출입문을 추가하십시오
acc_globalInterlock_isAddTheGroupImmediately=그룹을 추가하십시오
#门禁参数设置
acc_param_autoEventDev=연결된 장치 기록 자동 다운로드
acc_param_autoEventTime=실시간 기록 자동 다운로드
acc_param_noRepeat=이미 등록된 메일 주소입니다
acc_param_most18=메일 주소는 최대 18개 입니다
acc_param_deleteAlert=이메일 주소 입력란을 모두 삭제할 수 없습니다
acc_param_invalidOrRepeat=E-Mail 주소 중복 혹은 오류
#全局联动
acc_globalLinkage_noSupport=해당 출입문은 인터락 기능을 지원하지 않습니다
acc_globalLinkage_trigger=외부 입력 유형별 출력 (글로벌)
acc_globalLinkage_noAddPerson=해당 트리거의 입력 유형은 전체 사용자를 적용해야합니다
acc_globalLinkage_selectAtLeastOne=출력 위치를 추가하십시오
acc_globalLinkage_selectTrigger=입력 유형을 추가하십시오
acc_globalLinkage_selectInput=입력 위치를 추가하십시오
acc_globalLinkage_selectOutput=출력 위치를 추가하십시오
acc_globalLinkage_audioRemind=알람음
acc_globalLinkage_audio=알람음
acc_globalLinkage_isApplyToAll=전체 사용자 적용
acc_globalLinkage_scope=적용 사용자
acc_globalLinkage_everyPerson=전체
acc_globalLinkage_selectedPerson=선택된 사용자
acc_globalLinkage_noSupportPerson=전체
acc_globalLinkage_reselectInput=입력 위치를 다시 추가하십시오
acc_globalLinkage_addPushDevice=해당 기능을 지원하지 않습니다
#其他
acc_InputMethod_tips=영문 입력 모드로 전환 하십시오
acc_device_systemCheckTip=출입통제 장치가 없습니다
acc_notReturnMsg=응답 없음
acc_validity_period=라이선스 기간이 만료되어 기능을 사용할 수 없습니다
acc_device_pushMaxCount=라이선스 한도를 초과하여 장치를 추가할 수 없습니다 / 현재 등록된 장치 수량: {0} 대
acc_device_videoHardwareLinkage=영상 장치 연결 설정
acc_device_videoCameraIP=카메라 IP
acc_device_videoCameraPort=카메라 포트
acc_location_unable=E-Map에 출입문이 추가되지 않아 이벤트 발생 위치를 찾을 수 없습니다
acc_device_wgDevMaxCount=라이선스 수량을 초과하여 설정을 수정할 수 없습니다
#自定义报警事件
acc_deviceEvent_selectSound=음성 파일을 선택하십시오
acc_deviceEvent_batchSetSoundErr=알람 음성 설정에 문제가 발생했습니다
acc_deviceEvent_batchSet=음성 설정
acc_deviceEvent_sound=이벤트 음성
acc_deviceEvent_exist=선택
acc_deviceEvent_upload=가져오기
#查询门最近发生事件
acc_doorEventLatestHappen=출입문 최신 기록 확인하기
#门禁人员信息
acc_pers_delayPassage=센서 감지 지연
#设备容量提示
acc_dev_usageConfirm=저장공간이 가득 찬 장치가 있습니다
acc_dev_immediateCheck=즉시 확인
acc_dev_inSoftware=서버 용량
acc_dev_inFirmware=장치 용량
acc_dev_get=확인
acc_dev_getAll=전체 확인
acc_dev_loadError=로딩 실패
#Reader
acc_reader_inout=입/출력
acc_reader_lightRule=간단 규칙
acc_reader_defLightRule=기본 규칙
acc_reader_encrypt=암호화
acc_reader_allReaderOfCurDev=현재 장치의 모든 리더기
acc_reader_tip1=연결된 장치에 암호화 설정이 적용됩니다
acc_reader_tip2=현재 장치에서 신분증 모드 기능은 사용할 수 없습니다
acc_reader_tip3=모든 리더기에 RS485 통신이 적용됩니다 / 장치가 다시 시작해야 설정이 적용됩니다
acc_reader_tip4=모든 리더기에 사용자 정보 숨기기가 설정됩니다
acc_reader_commType=통신 유형
acc_reader_commAddress=통신 주소
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand / RS485
acc_readerCommType_disable=비활성화
acc_readerComAddress_repeat=등록된 통신 주소
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=RS485 주소
acc_readerCommType_wgAddress=Wiegand 주소
acc_reader_macError=MAC 주소를 입력하십시오
acc_reader_machineType=장치 유형
acc_reader_readMode=모드
acc_reader_readMode_normal=일반 모드
acc_reader_readMode_idCard=카드 모드
acc_reader_note=Tip : 카메라와 동일한 구역 ({0})에 출입통제 장치만 선택할 수 있습니다
acc_reader_rs485Type=RS485 프로토콜 유형
acc_reader_userLock=사용자 출입 불가
acc_reader_userInfoReveal=사용자 정보 숨기기
#operat
acc_operation_pwd=인증 비밀번호
acc_operation_pwd_error=비밀번호 오류
acc_new_input_not_same=비밀번호가 일치하지 않습니다
acc_op_set_keyword=라이선스 설정
acc_op_old_key=기존 키
acc_op_new_key=신규 키
acc_op_cofirm_key=라이선스 확인
acc_op_old_key_error=기존 키 오류
#验证方式规则
acc_verifyRule_name=규칙명
acc_verifyRule_door=출입문 인증
acc_verifyRule_person=사용자 인증
acc_verifyRule_copy=월요일 설정 복사 (월~금 적용)
acc_verifyRule_tip1=인증모드를 선택 하십시오
acc_verifyRule_tip2=사용자 인증모드를 설정할 경우 RS485 리더기로 설정되어 있는 출입문은 설정할 수 없습니다
acc_verifyRule_tip3=RS485 리더기는 출입문 인증 모드만 설정되며 사용자별 인증 모드는 지원하지 않습니다
acc_verifyRule_oldVerifyMode=이전 인증 모드
acc_verifyRule_newVerifyMode=신규 인증 모드
acc_verifyRule_newVerifyModeSelectTitle=신규 인증 모드 선택
acc_verifyRule_newVerifyModeNoSupportTip=신규 인증 모드를 지원하는 장치가 없습니다
#Wiegand Test
acc_wiegand_beforeCard=신규 카드 번호 길이({0} 비트)가 이전 카드와 일치하지 않습니다
acc_wiegand_curentCount=현재 카드 번호 길이 : {0} 비트
acc_wiegand_card=카드
acc_wiegand_readCard=카드 읽기
acc_wiegand_clearCardInfo=카드 정보 초기화
acc_wiegand_originalCard=기존 카드 번호
acc_wiegand_recommendFmt=카드 형식 추천
acc_wiegand_parityFmt=홀수 짝수 패리티 형식
acc_wiegand_withSizeCode=Site Code가 비어있는 경우 Site Code 자동 계산
acc_wiegand_tip1=해당 카드의 Site Code가 일치하지 않으며, 차수가 동일하지 않을 수 있습니다
acc_wiegand_tip2=기존 카드번호와 일치하지 않습니다 / Site Code : {0}, 카드 번호 : {1}를 다시 확인하십시오
acc_wiegand_tip3=기존 카드번호와 일치하지 않습니다 / 입력한 카드 번호를 확인하십시오 ({0})
acc_wiegand_tip4=기존 카드 번호와 일치하지 않습니다 / Site Code를 확인하십시오 ({0})
acc_wiegand_tip5=이 기능을 사용하려면 모든 Site Code 값을 삭제하십시오
acc_wiegand_warnInfo1=신규 카드로 교체 시 수동으로 교체하십시오
#LCD实时监控
acc_leftMenu_LCDRTMonitor=출입통제 보드
acc_LCDRTMonitor_current=현재 사용자 정보
acc_LCDRTMonitor_previous=최근 사용자 정보
#api
acc_api_levelIdNotNull=권한 그룹 ID는 비워둘 수 없습니다
acc_api_levelExist=권한 그룹이 등록됨
acc_api_areaNameNotNull=영역은 비워둘 수 없습니다.
acc_api_levelNotExist=권한 그룹이 등록되어 있지 않습니다
acc_api_levelNotHasPerson=권한 그룹에 사용자가 없습니다
acc_api_doorIdNotNull=출입문 ID는 비워둘 수 없습니다
acc_api_doorNameNotNull=출입문명은 비워둘 수 없습니다
acc_api_doorIntervalSize=개방 시간은 1에서 254 사이로 설정하십시오
acc_api_doorNotExist=등록되지 않은 출입문입니다
acc_api_devOffline=장치가 연결되어 있지 않거나 비활성화 되어있습니다
acc_api_devSnNotNull=장치 S/N은 비워둘 수 없습니다
acc_api_timesTampNotNull=타임스탬프는 비워둘 수 없습니다.
acc_api_openingTimeCannotBeNull=개방 시간은 비워 둘 수 없습니다
acc_api_parameterValueCannotBeNull=매개 변수 값은 비워 둘 수 없습니다
acc_api_deviceNumberDoesNotExist=등록되지 않은 장치 시리얼 번호입니다
acc_api_readerIdCannotBeNull=리더기 ID는 비워 둘 수 없습니다
acc_api_theReaderDoesNotExist=등록되지 않은 리더기입니다
acc_operate_door_notInValidDate=현재 출입문 개방 시간이 아닙니다 / 관리자에게 문의 하십시오
acc_api_doorOffline=문이 오프라인이거나 비활성화되었습니다.
#门禁信息自动导出
acc_autoExport_title=자동 다운로드 (기록)
acc_autoExport_frequencyTitle=다운로드 주기
acc_autoExport_frequencyDay=일별
acc_autoExport_frequencyMonth=월별
acc_autoExport_firstDayMonth=매월 첫째 날
acc_autoExport_specificDate=특정 일자
acc_autoExport_exportModeTitle=기록 데이터
acc_autoExport_dailyMode=일일 기록
acc_autoExport_monthlyMode=월간 기록 (지난 달)
acc_autoExport_allMode=모든 데이터 (최대 30,000 개의 데이터 내보내기)
acc_autoExport_recipientMail=E-Mail 주소
#First In And Last Out
acc_inOut_inReaderName=첫 번째 장치명
acc_inOut_firstInTime=첫 번째 시간
acc_inOut_outReaderName=마지막 장치명
acc_inOut_lastOutTime=마지막 시간
#防疫参数
acc_dev_setHep=방역관리 설정
acc_dev_enableIRTempDetection=온도 측정 기능
acc_dev_enableNormalIRTempPass=고온 인증 실패
acc_dev_enableMaskDetection=마스크 인식 기능
acc_dev_enableWearMaskPass=마스크 미착용 인증 실패
acc_dev_tempHighThreshold=고온 알람 설정 값
acc_dev_tempUnit=온도 단위
acc_dev_tempCorrection=온도 편차 보정
acc_dev_enableUnregisterPass=방문객 모드 설정
acc_dev_enableTriggerAlarm=알람 설정
#联动邮件
acc_mail_temperature=온도
acc_mail_mask=마스크 착용 여부
acc_mail_unmeasured=미측정
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort 글로벌 이벤트
acc_digifort_chooseDigifortEvents=Digifort 글로벌 이벤트 선택
acc_digifort_eventExpiredTip=글로벌 이벤트가 Digifort 서버에서 삭제되면 빨간색으로 표시됩니다
acc_digifort_checkConnection=Digifort 서버 연결을 다시 확인하십시오
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=연락처 추가
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=추가 기능 설정
acc_extendParam_faceUI=인터페이스 설정
acc_extendParam_faceParam=방역관리 설정
acc_extendParam_accParam=출입통제 설정
acc_extendParam_intercomParam=시각적 대화 매개 변수
acc_extendParam_volume=음량
acc_extendParam_identInterval=인증 간격 (ms)
acc_extendParam_historyVerifyResult=인증 기록 확인
acc_extendParam_macAddress=MAC 주소
acc_extendParam_showIp=IP 주소
acc_extendParam_24HourFormat=24 시간 형식
acc_extendParam_dateFormat=일자 형식
acc_extendParam_1NThreshold=1:N 인증 품질 점수
acc_extendParam_facePitchAngle=얼굴 상하 회전각도
acc_extendParam_faceRotationAngle=얼굴 좌우 회전각도
acc_extendParam_imageQuality=입력 이미지 품질 점수
acc_extendParam_miniFacePixel=인증 가능한 최소 얼굴 크기
acc_extendParam_biopsy=생체인식 사용
acc_extendParam_showThermalImage=열화상 표시
acc_extendParam_attributeAnalysis=속성 분석 활성화
acc_extendParam_temperatureAttribute=온도 측정 기능
acc_extendParam_maskAttribute=마스크 인식 기능
acc_extendParam_minTemperature=온도 측정 하한값
acc_extendParam_maxTemperature=온도 측정 상한값
acc_extendParam_gateMode=게이트 모드
acc_extendParam_qrcodeEnable=QR 코드 기능 활성화
#可视对讲
acc_dev_intercomServer=시각적 대화 서비스 주소
acc_dev_intercomPort=시각적 대화 서비스
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=권한 동기화
# 夏令时名称
acc_dsTimeUtc_none=설정 안함
acc_dsTimeUtc_AreaNone=사용 안함
acc_dsTimeUtc1000_0=캔버라, 멜버른, 시드니
acc_dsTimeUtc1000_1=호바트
acc_dsTimeUtc_0330_0=뉴펀들랜드
acc_dsTimeUtc_1000_0=알류샨 제도
acc_dsTimeUtc_0200_0=중대서양주 (구)
acc_dsTimeUtc0930_0=애들레이드
acc_dsTimeUtc_0100_0=아소르스 제도
acc_dsTimeUtc_0400_0=대서양 표준시 (캐나다)
acc_dsTimeUtc_0400_1=산티아고
acc_dsTimeUtc_0400_2=아순시온
acc_dsTimeUtc_0300_0=그린란드
acc_dsTimeUtc_0300_1=생피에르 미클롱 제도
acc_dsTimeUtc0200_0=키시나우
acc_dsTimeUtc0200_1=헬싱키, 키예프, 리가, 소피아, 탈린, 빌뉴스
acc_dsTimeUtc0200_2=아테네, 부쿠레슈티
acc_dsTimeUtc0200_3=예루살렘
acc_dsTimeUtc0200_4=암만
acc_dsTimeUtc0200_5=베이루트
acc_dsTimeUtc0200_6=다마스커스
acc_dsTimeUtc0200_7=가자, 히브론
acc_dsTimeUtc0200_8=주바
acc_dsTimeUtc_0600_0=중부 표준시 (미국, 캐나다)
acc_dsTimeUtc_0600_1=과달라하라, 멕시코 시티, 몬테레이
acc_dsTimeUtc_0600_2=이스터섬
acc_dsTimeUtc1300_0=사모아 제도
acc_dsTimeUtc_0500_0=하바나
acc_dsTimeUtc_0500_1=미동부 표준시 (미국, 캐나다)
acc_dsTimeUtc_0500_2=아이티
acc_dsTimeUtc_0500_3=인디애나주 (동부)
acc_dsTimeUtc_0500_4=터크스 케이커스 제도
acc_dsTimeUtc_0800_0=태평양 표준시 (미국, 캐나다)
acc_dsTimeUtc_0800_1=바하칼리포르니아
acc_dsTimeUtc0330_0=테헤란
acc_dsTimeUtc0000_0=더블린, 에든버러, 리스본, 런던
acc_dsTimeUtc1200_0=피지
acc_dsTimeUtc1200_1=페트로파블로프스크 캄차츠키 (구)
acc_dsTimeUtc1200_2=오클랜드
acc_dsTimeUtc1100_0=노퍽섬
acc_dsTimeUtc_0700_0=치와와, 라파스, 마사틀란
acc_dsTimeUtc_0700_1=산악 시간대 (미국, 캐나다)
acc_dsTimeUtc0100_0=베오그라드, 브라티슬라바, 부다페스트, 류블랴나, 프라하
acc_dsTimeUtc0100_1=사라예보, 스코페, 바르샤바, 자그레브
acc_dsTimeUtc0100_2=카사블랑카
acc_dsTimeUtc0100_3=브뤼셀, 코펜하겐, 마드리드, 파리
acc_dsTimeUtc0100_4=암스테르담, 베를린, 베른, 로마, 스톡홀름, 빈
acc_dsTimeUtc_0900_0=알래스카
#安全点(muster point)
acc_leftMenu_accMusterPoint=비상 대피소
acc_musterPoint_activate=활성화
acc_musterPoint_addDept=부서 추가
acc_musterPoint_delDept=부서 삭제
acc_musterPoint_report=비상 대피 보고서
acc_musterPointReport_sign=수동 인증
acc_musterPointReport_generate=보고서 생성
acc_musterPoint_addSignPoint=비상 대피소 추가
acc_musterPoint_delSignPoint=비상 대피소 삭제
acc_musterPoint_selectSignPoint=비상 대피소를 추가하세요!
acc_musterPoint_signPoint=비상 대피소
acc_musterPoint_delFailTip=활성화된 비상 대피소는 삭제할 수 없습니다!
acc_musterPointReport_enterTime=시간 입력
acc_musterPointReport_dataAnalysis=데이터 분석
acc_musterPointReport_safe=안전
acc_musterPointReport_danger=위험
acc_musterPointReport_signInManually=수동 인증
acc_musterPoint_editTip=활성화된 비상 대피소는 수정할 수 없습니다!
acc_musterPointEmail_total=예상 참석자:
acc_musterPointEmail_safe=체크인 (안전):
acc_musterPointEmail_dangerous=위험에 처하다:
acc_musterPoint_messageNotification=활성화 시 메시지 알림
acc_musterPointReport_sendEmail=계획 푸시 보고서
acc_musterPointReport_sendInterval=전송 간격
acc_musterPointReport_sendTip=선택한 알림 방법이 성공적으로 구성되었는지 확인하십시오. 그렇지 않으면 알림이 제대로 전송되지 않습니다!
acc_musterPoint_mailSubject=긴급 집합 알림
acc_musterPoint_mailContent=즉시 "{0}"에 모여 "{1}" 기기에서 로그인하세요. 감사합니다!
acc_musterPointReport_mailHead=안녕하세요, 긴급 상황 보고입니다.복습하세요.
acc_musterPoint_visitorsStatistics=방문자 통계
# 报警监控
acc_alarm_priority=우선 순위
acc_alarm_total=모두
acc_alarm_today=금일 기록
acc_alarm_unhandled=미확인
acc_alarm_inProcess=처리 중
acc_alarm_acknowledged=확인
acc_alarm_top5=알람 기록 TOP 5 명
acc_alarm_monitoringTime=모니터링 시간
acc_alarm_history=알람 처리 기록
acc_alarm_acknowledgement=처리 기록
acc_alarm_eventDescription=기록 세부 정보
acc_alarm_acknowledgeText=알람 기록 세부 정보를 설정된 메일로 발송합니다
acc_alarm_emailSubject=처리 기록 추가
acc_alarm_mute=음소거
acc_alarm_suspend=일시 중지
acc_alarm_confirmed=알람이 발생했습니다
acc_alarm_list=알람 기록
#ntp
acc_device_setNTPService=NTP 서버 설정
acc_device_setNTPServiceTip=서버 주소는 쉼표(,) 또는 세미콜론(;)으로 구분하십시오.
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=운영 관리자는 출입문 시간대, 잠금 등의 제한을 받지 않으며 개방 순위가 아주 높습니다.
acc_editPerson_delayPassageTip=출입문을 통과 시간을 연장합니다. 몸이 불편한 노약자 혹은 장애인에게 적합합니다.
acc_editPerson_disabledTip=사용자의 출입 권한을 일시적으로 해제합니다.
#门禁向导
acc_guide_title=액세스 제어 모듈 설정 마법사
acc_guide_addPersonTip=사람과 해당 자격 증명(얼굴 또는 지문 또는 카드 또는 손바닥 또는 암호)을 추가해야 합니다. 이미 추가한 경우 이 단계를 바로 건너뛰십시오.
acc_guide_timesegTip=유효한 시작 시간을 구성하십시오
acc_guide_addDeviceTip=해당 장치를 액세스 포인트로 추가하십시오
acc_guide_addLevelTip=액세스 제어 수준 추가
acc_guide_personLevelTip=해당 접근 제어 권한을 사람에게 할당
acc_guide_rtMonitorTip=액세스 제어 기록을 실시간으로 확인
acc_guide_rtMonitorTip2=소속 영역 추가 및 방문 후 실시간 출입문 기록 보기
#查看区域内人员
acc_zonePerson_cleanCount=들어오고 나가는 사람들의 통계 지우기
acc_zonePerson_inCount=입장 인원 통계
acc_zonePerson_outCount=퇴사 인원 통계
#biocv460
acc_device_validFail=사용자 이름이나 비밀번호가 올바르지 않고 확인이 실패합니다!
acc_device_pwdRequired=최대 6비트 정수만 입력 가능