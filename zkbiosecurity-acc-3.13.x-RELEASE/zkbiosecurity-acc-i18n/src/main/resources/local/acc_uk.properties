#[1]左侧菜单
acc_module=Access
acc_leftMenu_accDev=Пристрій контролю доступу
acc_leftMenu_auxOut=Допоміжний вихід
acc_leftMenu_dSTime=Літній час
acc_leftMenu_access=Управління доступом
acc_leftMenu_door=Двері
acc_leftMenu_accRule=Правило доступу
acc_leftMenu_interlock=Шлюз
acc_leftMenu_antiPassback=Заборона подвійного проходу
acc_leftMenu_globalLinkage=Глобальний шлюз
acc_leftMenu_firstOpen=1-й співробітник - режим відкрито
acc_leftMenu_combOpen=Відкриття комісуванням
acc_leftMenu_personGroup=Група комісування
acc_leftMenu_level=Рівні доступу
acc_leftMenu_electronicMap=Карта
acc_leftMenu_personnelAccessLevels=Рівень доступу співробітника
acc_leftMenu_searchByLevel=По рівням дотупу
acc_leftMenu_searchByDoor=Права доступа по двері
acc_leftMenu_expertGuard=Розширені функції
acc_leftMenu_zone=Зона
acc_leftMenu_readerDefine=Призначення зчитувача
acc_leftMenu_gapbSet=Глобальна заборона подвійного проходу
acc_leftMenu_whoIsInside=Кто всередені
acc_leftMenu_whatRulesInside=Правила що діють всередені
acc_leftMenu_occupancy=Occupancy Control
acc_leftMenu_route=Route Control
acc_leftMenu_globalInterlock=Глобальний шлюз
acc_leftMeue_globalInterlockGroup=Група глобального шлюзу
acc_leftMenu_dmr=Dead Man Rule
acc_leftMenu_personLimit=Person Availability
acc_leftMenu_verifyModeRule=Режим верифікації
acc_leftMenu_verifyModeRulePersonGroup=Режим групової верифікації
acc_leftMenu_extDev=Панель I/O
acc_leftMenu_firstInLastOut=Перший вхід, останній вихід
acc_leftMenu_accReports=Звіти контроля доступу
#[3]门禁时间段
acc_timeSeg_entity=Часова зона
acc_timeSeg_canNotDel=Період часу використовується і не може бути видалений!
#[4]门禁设备--公共的在common中
acc_common_ruleName=Назва правила
acc_common_hasBeanSet=Було встановлено
acc_common_notSet=Не встановлено
acc_common_hasBeenOpened=Було відкрито
acc_common_notOpened=Не відкрито
acc_common_partSet=Частина набору
acc_common_linkageAndApbTip=Прив'язка і глобальна прив'язка, заборона подвійного проходу і глобальна заборона подвійного проходу встановлені одночасно, можуть виникати конфлікти.
acc_common_vidlinkageTip=Переконайтеся, що відповідна вхідна точка прив'язки встановлена до доступного відеоканалу, інакше функція відеозв’язку не працюватиме!
acc_common_accZoneFromTo=Неможливо встановити ту саму зону
acc_common_logEventNumber=ID події
acc_common_bindOrUnbindChannel=Прив’язування/відв’язування камери
acc_common_boundChannel=Прив'язати камеру
#设备信息
acc_dev_iconType=Тип значка
acc_dev_carGate=Шлагбаум
acc_dev_channelGate=Турнікет
acc_dev_acpType=Тип панелі керування
acc_dev_oneDoorACP=Однодверна панель управління доступом
acc_dev_twoDoorACP=Дводверна панель управління доступом
acc_dev_fourDoorACP=Чотиридверна панель управління доступом
acc_dev_onDoorACD=Автономний пристрій
acc_dev_switchToTwoDoorTwoWay=Змінити на 2 двері 2 сторони
acc_dev_addDevConfirm2=Порада. Підключення пристрою успішно, але тип панелі керування доступом відрізняється від фактичного, змініть його на {0} панель керування дверима. Продовжувати додавати?
acc_dev_addDevConfirm4=Автономний пристрій. Продовжувати додавати?
acc_dev_oneMachine=Автономний пристрій
acc_dev_fingervein=Вени пальця
acc_dev_control=Пристрій контроля
acc_dev_protocol=Тип протоколу
acc_ownedBoard=Контроль плати
#设备操作
acc_dev_start=Старт
acc_dev_accLevel=Панель доступу
acc_dev_timeZoneAndHoliday=Часова зона, свята
acc_dev_linkage=Прив'язка
acc_dev_doorOpt=Параметри двері
acc_dev_firstPerson=Перший співробітник - нормально відкрито
acc_dev_multiPerson=Відкриття комісуванням
acc_dev_interlock=Шлюз
acc_dev_antiPassback=Заборона подвійного проходу
acc_dev_wiegandFmt=Формат Wiegand
acc_dev_outRelaySet=Налаштування допоміжного виходу
acc_dev_backgroundVerifyParam=Параметри фонової перевірки
acc_dev_getPersonInfoPrompt=Будь ласка, переконайтеся, що ви успішно отримали інформацію про персонал, інакше станеться помилка. Продовжити?
acc_dev_getEventSuccess=Отримання журналу подій вдало
acc_dev_getEventFail=Не вдалося отримати журнал події.
acc_dev_getInfoSuccess=Отримано інформацію успішно.
acc_dev_getInfoXSuccess=Отримано {0} вдало.
acc_dev_getInfoFail=Не вдалося отримати інформацію.
acc_dev_updateExtuserInfoFail=Не вдалося оновити інформацію, будь ласка, перевірте інформацію.
acc_dev_getPersonCount=Отримати кількість користувачів
acc_dev_getFPCount=Отримати кількість шаблонів відбитків пальців
acc_dev_getFVCount=Отримати кількість шаблонів вен пальця
acc_dev_getFaceCount=Отримати кількість шаблонів обличчя
acc_dev_getPalmCount=Отримати кількість шаблонів долоні
acc_dev_getBiophotoCount=Отримати кількість шаблонів обличчя
acc_dev_noData=Дані з пристрою не повертаються.
acc_dev_noNewData=В пристрої немає нових транзакцій.
acc_dev_softLtDev=У програмному забезпеченні більше людей, ніж у пристрої.
acc_dev_personCount=Кількість співробітників:
acc_dev_personDetail=Подробиці наведені нижче:
acc_dev_softEqualDev=Кількість людей у програмному забезпеченні та пристрої однакова.
acc_dev_softGtDev=У пристрої більше людей, ніж у програмному забезпеченні.
acc_dev_cmdSendFail=Не вдалося надіслати команди, надішліть повторно.
acc_dev_issueVerifyParam=Встановіть параметри фонової перевірки
acc_dev_verifyParamSuccess=Параметри фонової перевірки вдало застосовані
acc_dev_backgroundVerify=Фонова перевірка
acc_dev_selRightFile=Виберіть коректний файл оновлення!
acc_dev_devNotOpForOffLine=Пристрій офлайн. Спробуйте пізніше
acc_dev_devNotSupportFunction=Пристрій не підтримує цю функцію
acc_dev_devNotOpForDisable=Пристрій вимкнено. Спробуйте пізніше
acc_dev_devNotOpForNotOnline=Пристрій офлайн або вимкнено. Спробуйте пізніше
acc_dev_getPersonInfo=Отримати інформацію про персонал
acc_dev_getFPInfo=Отримати інформацію про шаблони відбитків пальців
acc_dev_getFingerVeinInfo=Отримати інформацію про шаблони вен пальців
acc_dev_getPalmInfo=Отримати інформацію про шаблони долонь
acc_dev_getBiophotoInfo=Отримати інформацію про шаблони обличчь у видимому світлі
acc_dev_getIrisInfo=Отримати інформацію про iris
acc_dev_disable=вимкнено, будь ласка, виберіть повторно
acc_dev_offlineAndContinue=офлайн, продовжити?
acc_dev_offlineAndSelect=офлайн.
acc_dev_opAllDev=Віс пристрої
acc_dev_opOnlineDev=Онлайн пристрої
acc_dev_opException=обробити помилки
acc_dev_exceptionAndConfirm=Час очікування підключення пристрою вийшов, операція не вдалася. Перевірте підключення до мережі
acc_dev_getFaceInfo=Отримати інформацію про обличчя
acc_dev_selOpDevType=Виберіть тип пристрою для роботи:
acc_dev_hasFilterByFunc=Показати лише пристрої онлайн та що підтримують цю функцію!
acc_dev_masterSlaveMode=RS485 ведучий і ведений режими
acc_dev_master=Хост
acc_dev_slave=Ведений
acc_dev_modifyRS485Addr=Змінити адресу RS485
acc_dev_rs485AddrTip=Будь ласка, введіть ціле число від 1 до 63!
acc_dev_enableFeature=Пристрої, на яких увімкнено фонову перевірку
acc_dev_disableFeature=Пристрої, на яких вимкнено фонову перевірку
acc_dev_getCountOnly=Отримати тільки підрахунок
acc_dev_queryDevPersonCount=Запит на кількість персоналу
acc_dev_queryDevVolume=Переглянути ємність пристрою
acc_dev_ruleType=Тип правила
acc_dev_contenRule=Деталі правил
acc_dev_accessRules=Переглянути правила використання пристроїв
acc_dev_ruleContentTip=Між кількома правилами, відокремлених символом '|'.
acc_dev_rs485AddrFigure=Код адреси RS485
acc_dev_addLevel=Додати до рівня
acc_dev_personOrFingerTanto=Кількість персоналу або відбитків пальців перевищує ліміт, синхронізація не вдалася...
acc_dev_personAndFingerUnit=(Номер)
acc_dev_setDstime=Встановити літній час
acc_dev_setTimeZone=Встановити часовий пояс пристрою
acc_dev_selectedTZ=Обрана часова зона
acc_dev_timeZoneSetting=Налаштування часового поясу...
acc_dev_timeZoneCmdSuccess=Надсилання команди часового поясу вдале...
acc_dev_enableDstime=Увімкнути літній час
acc_dev_disableDstime=Вимкнути літній час
acc_dev_timeZone=Часовий пояс
acc_dev_dstSettingTip=Налаштування літнього часу...
acc_dev_dstDelTip=Видалення налаштування переходу на літній час...
acc_dev_enablingDst=Увімкнути літній час
acc_dev_dstEnableCmdSuccess=Команду увімкнення переходу на літній час успішно надіслано.
acc_dev_disablingDst=Відключити перехід на літній час.
acc_dev_dstDisableCmdSuccess=Успішно відключено команду переходу на літній час.
acc_dev_dstCmdSuccess=Успішно надіслано команду переходу на літній час...
acc_dev_usadst=Літній час
acc_dev_notSetDst=Без налаштувань
acc_dev_selectedDst=Вибрано літній час
acc_dev_configMasterSlave=Конфігурація master-slave
acc_dev_hasFilterByUnOnline=Показати лише пристрої онлайн.
acc_dev_softwareData=Якщо дані не узгоджуються з пристроєм, будь ласка, синхронізуйте дані пристроїв, перш ніж повторити спробу.
acc_dev_disabled=Вимкненими пристроями керувати не можна!
acc_dev_offline=Неможливо керувати автономними пристроями!
acc_dev_noSupport=Пристрої не підтримують цю функцію і не можуть ними керувати!
acc_dev_noRegDevTip=Цей пристрій не налаштовано як пристрій реєстрації. Дані в програмному забезпеченні не оновлюватимуться. Хочете продовжити?
acc_dev_noOption=Нет подходящих вариантов
acc_dev_devFWUpdatePrompt=Поточний пристрій не працює з відключеними свівробітниками та функцією підтвердження особи (будь ласка, зверніться до посібника користувача).
acc_dev_panelFWUpdatePrompt=Поточний пристрій не працюватиме з відключеними співробітниками та з функцією перевірки особи, оновити мікропрограму?
acc_dev_sendEventCmdSuccess=Команда видалення події успішно надіслана
acc_dev_tryAgain=Будь ласка спробуйте ще раз.
acc_dev_eventAutoCheckAndUpload=Автоматична перевірка та завантаження подій
acc_dev_eventUploadStart=Почати завантажувати події.
acc_dev_eventUploadEnd=Завантаження подій завершено.
acc_dev_eventUploadFailed=Не вдалося завантажити події.
acc_dev_eventUploadPrompt=Версія мікропрограмного забезпечення пристрою застаріла. Перш ніж оновити мікропрограму, потрібно:
acc_dev_backupToSoftware=Резервне копіювання даних у програмне забезпечення
acc_dev_deleteEvent=Видалити старий запис
acc_dev_upgradePrompt=Версія мікропрограми застаріла і може призвести до помилки, перед оновленням мікропрограмного забезпечення створіть резервну копію даних.
acc_dev_conflictCardNo=Картка в системі для {0} іншої особи!
acc_dev_rebootAfterOperate=Операція пройшла успішно, пристрій перезавантажиться пізніше.
acc_dev_baseOptionTip=Помилка отримання основного параметра.
acc_dev_funOptionTip=Помилка отримання параметра функції.
acc_dev_sendComandoTip=Не вдалося надіслати команду параметра пристрою.
acc_dev_noC3LicenseTip=Не вдається додати цей тип пристрою ({0})! Будь ласка, зв’яжіться з нашим відділом продажів.
acc_dev_combOpenDoorTip=({0}) Відкриття дверей комісуванням налаштовано, не можна використовувати одночасно з фоновою перевіркою.
acc_dev_combOpenDoorPersonCountTip=Кількість людей, які відкривають двері в групі {0}, не повинна перевищувати {1}!
acc_dev_addDevTip=Це стосується лише додавання пристрою з протоколом зв’язку PULL!
acc_dev_addError=Виняток додавання пристрою, відсутні параметри ({0})!
acc_dev_updateIPAndPortError=Оновлення IP-адресу сервера та помилку порту...
acc_dev_transferFilesTip=Перевірка прошивки завершена, передача файлів почалася
acc_dev_serialPortExist=Послідовний порт існує
acc_dev_isExist=Наявність пристрою
acc_dev_description=Опис
acc_dev_searchEthernet=Знайти пристрій Ethernet
acc_dev_searchRS485=Знайти пристрій RS485
acc_dev_rs485AddrTip1=Початкова адреса RS485 не може бути більшою за кінцеву адресу
acc_dev_rs485AddrTip2=Область пошуку RS485 має бути менше 20
acc_dev_clearAllCmdCache=Очистити всі команди
acc_dev_authorizedSuccessful=Авторизовано успішно
acc_dev_authorize=Авторизувати
acc_dev_registrationDevice=Зареєструвати пристрій
acc_dev_setRegistrationDevice=Встановити як пристрій реєстрації
acc_dev_mismatchedDevice=Цей пристрій не можна використовувати для вашого ринку. Будь ласка, перевірте серійний номер пристрою у наших відділах продажу.
acc_dev_pwdStartWithZero=Пароль зв'язку не може починатися з нуля!
acc_dev_maybeDisabled=Поточна ліцензія дозволяє вам додати ще {0} дверей, двері нового доданого пристрою, які перевищують ліміт дверей ліцензії, будуть вимкнені, продовжувати роботу?
acc_dev_Limit=Ліцензійний ключ пристрою досяг верхньої межі для додавання, будь ласка, авторизуйте.
acc_dev_selectDev=Будь ласка, виберіть пристрій!
acc_dev_cannotAddPullDevice=Не вдається додати PULL пристрій! Будь ласка, зв’яжіться з нашим відділом продажу.
acc_dev_notContinueAddPullDevice=У системі було {0} PULL пристроїв, не можна продовжувати додавати! Будь ласка, зв’яжіться з нашим відділом продажу.
acc_dev_deviceNameNull=Модель пристрою порожня; не вдається додати пристрій!
acc_dev_commTypeErr=Тип зв'язку не відповідає; не вдається додати пристрій!
acc_dev_inputDomainError=Введіть дійсну адресу домену.
acc_dev_levelTip=На рівні більше 5000 людей, не можна додати пристрій до рівня тут.
acc_dev_auxinSet=Налаштування допоміжного входу
acc_dev_verifyModeRule=Правило режиму перевірки
acc_dev_netModeWired=Дротовий
acc_dev_netMode4G=4G
acc_dev_netModeWifi=Wi-Fi
acc_dev_updateNetConnectMode=Переключити мережеве підключення
acc_dev_wirelessSSID=Бездротовий SSID
acc_dev_wirelessKey=Бездротовий ключ
acc_dev_searchWifi=Пошук WIFI
acc_dev_testNetConnectSuccess=З'єднання успішне?
acc_dev_testNetConnectFailed=З'єднання не може правильно працювати!
acc_dev_signalIntensity=Сила сигналу
acc_dev_resetSearch=Шукати ще раз
acc_dev_addChildDevice=Додати підпристрій
acc_dev_modParentDevice=Змініть головний пристрій
acc_dev_configParentDevice=Налаштування головного пристрою
acc_dev_lookUpChildDevice=Перегляд дочірніх пристроїв
acc_dev_addChildDeviceTip=Авторизацію потрібно виконати на авторизованому підпристрої
acc_dev_maxSubCount=Кількість дозволених підпристроїв перевищує максимальну кількість одиниць: {0}.
acc_dev_seletParentDevice=Будь ласка, виберіть головний пристрій!
acc_dev_networkCard=Мережева карта
acc_dev_issueParam=Спеціальні параметри синхронізації
acc_dev_issueMode=Режим синхронізації
acc_dev_initIssue=Дані ініціалізації версії мікропрограми 3030
acc_dev_customIssue=Спеціальна синхронізація даних
acc_dev_issueData=Дані
acc_dev_parent=Головний пристрій
acc_dev_parentEnable=Головний пристрій вимкнено
acc_dev_parentTips=Прив’язування головного пристрою видалить усі дані на пристрої, і їх потрібно буде налаштувати знову.
acc_dev_addDevIpTip=Нова IP-адреса не може збігатися з IP-адресою сервера
acc_dev_modifyDevIpTip=Нова адреса сервера не може збігатися з IP-адресою пристрою
acc_dev_setWGReader=Встановити Wiegand зчитувач
acc_dev_selectReader=Натисніть, щоб вибрати зчитувач
acc_dev_IllegalDevice=Некоректний пристрій
acc_dev_syncTimeWarnTip=Час синхронізації для наступних пристроїв слід синхронізувати на головному пристрої.
acc_dev_setTimeZoneWarnTip=На головному пристрої слід синхронізувати часовий пояс наступного пристрою.
acc_dev_setDstimeWarnTip=Перехід на літній час для наступних пристроїв слід синхронізувати на головному пристрої.
acc_dev_networkSegmentSame=Два мережеві адаптери не можуть використовувати один сегмент мережі.
acc_dev_upgradeProtocolNoMatch=Протокол файлу оновлення не відповідає
acc_dev_ipAddressConflict=Пристрій з такою ж IP-адресою вже існує. Будь ласка, змініть IP-адресу пристрою та додайте її знову.
acc_dev_checkServerPortTip=Налаштований порт сервера не відповідає порту системного зв’язку, що може призвести до невдалого додавання. Продовжувати роботу?
acc_dev_clearAdmin=Очистити дозвіл адміністратора
acc_dev_setDevSate=Встановити статус входу/виходу пристрою
acc_dev_sureToClear=Дійсно очистити права адміністратора пристрою?
acc_dev_hostState=Статус головного пристрою
acc_dev_regDeviceTypeTip=Цей пристрій обмежено, і його не можна додавати безпосередньо. Будь ласка, зв’яжіться з постачальником програмного забезпечення!
acc_dev_extBoardType=Тип плати I/O
acc_dev_extBoardTip=Після налаштування вам потрібно перезавантажити пристрій, щоб він вступив в дію.
acc_dev_extBoardLimit=До кожного пристрою можна додати лише {0} плату вводу/виводу цього типу!
acc_dev_replace=Замінити пристрій
acc_dev_replaceTip=Після заміни старий пристрій не працюватиме, будьте обережні!
acc_dev_replaceTip1=Після заміни виконайте операцію «синхронізувати всі дані»;
acc_dev_replaceTip2=Будь ласка, переконайтеся, що модель замінного пристрою та сама!
acc_dev_replaceTip3=Будь ласка, переконайтеся, що пристрій заміни встановив ту саму адресу та порт сервера, що й старий пристрій!
acc_dev_replaceFail=Тип машини пристрою невідповідний і не може бути замінений!
acc_dev_notApb=Цей пристрій не вдалося виконати операції проти підморських вод або перечитати голову
acc_dev_upResourceFile=Вивантажити файли ресурсів
acc_dev_playOrder=Порядок відтворення
acc_dev_setFaceServerInfo=Встановити параметри порівняння обличчя сервера
acc_dev_faceVerifyMode=Режим порівняння обличчя
acc_dev_faceVerifyMode1=Локальне порівняння
acc_dev_faceVerifyMode2=Порівняння сервера
acc_dev_faceVerifyMode3=Пріоритет локального порівняння
acc_dev_faceBgServerType=Тип сервера обличчя сервера
acc_dev_faceBgServerType1=Сервіси програмної платформи
acc_dev_faceBgServerType2=Сервіси програмної платформи
acc_dev_isAccessLogic=Увімкнути логічне перевірку керування доступом
#[5]门-其他关联的也复用此处
acc_door_entity=Двері
acc_door_number=Номер дверей
acc_door_name=Назва двері
acc_door_activeTimeZone=Активний часова зона
acc_door_passageModeTimeZone=Часовий пояс режиму проходження
acc_door_setPassageModeTimeZone=Встановлено часовий пояс режиму проходження
acc_door_notPassageModeTimeZone=Часовий пояс у непрохідному режимі
acc_door_lockOpenDuration=Тривалість відкриття
acc_door_entranceApbDuration=Тривалість заборони повторного проходу
acc_door_sensor=Датчик стану дверей
acc_door_sensorType=Тип датчика дверей
acc_door_normalOpen=Нормально відкритий
acc_door_normalClose=Нормально закритий
acc_door_sensorDelay=Затримка датчика дверей
acc_door_closeAndReverseState=Зворотній стан замка
acc_door_hostOutState=Статус доступу до хоста
acc_door_slaveOutState=Статус доступу до ведомого пристрою
acc_door_inState=Вхід
acc_door_outState=Вихід
acc_door_requestToExit=REX режим
acc_door_withoutUnlock=Закрито
acc_door_unlocking=Відкрито
acc_door_alarmDelay=Затримка REX
acc_door_duressPassword=Пароль примусу
acc_door_currentDoor=Поточні двері
acc_door_allDoorOfCurDev=Усі двері в поточному пристрої
acc_door_allDoorOfAllDev=Усі двері в усіх пристроях
acc_door_allDoorOfAllControlDev=Усі двері в усіх пристроях керування
acc_door_allDoorOfAllStandaloneDev=Усі двері всіх автономних пристроїв
acc_door_allWirelessLock=Усі бездротові замки
acc_door_max6BitInteger=Максимум 6-бітове ціле число
acc_door_direction=Напрямок
acc_door_onlyInReader=Тільки вхідний зчитувач
acc_door_bothInAndOutReader=Зчитувачі входу та виходу
acc_door_noDoor=Будь ласка, додайте двері.
acc_door_nameRepeat=Назви дверей дублюються.
acc_door_duressPwdError=Пароль примусу не повинен збігатися з будь-яким особистим паролем.
acc_door_urgencyStatePwd=Будь ласка, введіть {0} бітове ціле число!
acc_door_noDevOnline=Жоден пристрій не в мережі або двері не підтримують режим перевірки картки.
acc_door_durationLessLock=Затримка датчика дверей має бути більшою за тривалість відкриття замка.
acc_door_lockMoreDuration=Тривалість відкриття замка має бути меншою за затримку датчика дверей.
acc_door_lockAndExtLessDuration=Сума тривалості відкриття замка та тривалого часу спуску має бути меншою за затримку датчика дверей.
acc_door_noDevTrigger=Пристрій не відповідає умовам!
acc_door_relay=Реле
acc_door_pin=Пін
acc_door_selDoor=Вибрати Двері
acc_door_sensorStatus=Датчик дверей ({0})
acc_door_sensorDelaySeconds=Затримка датчика дверей ({0} с)
acc_door_timeSeg=Часовий пояс ({0})
acc_door_combOpenInterval=Інтервал роботи з кількома особами
acc_door_delayOpenTime=Затримка відкриття дверей
acc_door_extDelayDrivertime=Розширений час випуску
acc_door_enableAudio=Увімкнути звукову тривогу
acc_door_disableAudio=Вимкнути звукову тривогу
acc_door_lockAndExtDelayTip=Сума тривалості відкриття замка та інтервалу часу затримки проходження не перевищує 254 секунди.
acc_door_disabled=Відключеними дверями не можна керувати!
acc_door_offline=Автономними дверима не можна керувати!
acc_door_notSupport=Наступні двері не підтримують цю функцію!
acc_door_select=Виберіть двері
acc_door_pushMaxCount=У системі є {0} увімкнених дверей і досягнуто ліміту ліцензій! Будь ласка, зв’яжіться з нашим відділом продажів
acc_door_outNumber=Поточна ліцензія дозволяє лише додати ще {0} двері. Виберіть двері і спробуйте ще раз або зв’яжіться з нашим відділом продажів, щоб отримати ліцензію на оновлення.
acc_door_latchTimeZone=REX Time Zone
acc_door_wgFmtReverse=Скасування номера картки
acc_door_allowSUAccessLock=Дозволити доступ суперкористувача під час блокування
acc_door_verifyModeSinglePwd=Паролі не можна використовувати як незалежний метод перевірки!
acc_door_doorPassword=Пароль відкриття дверей
#辅助输入
acc_auxIn_timeZone=Активна часова зона
#辅助输出
acc_auxOut_passageModeTimeZone=Часова зона режиму проходу
acc_auxOut_disabled=Вимкнений допоміжний вихід не може працювати!
acc_auxOut_offline=Неможливо керувати автономним допоміжним виходом!
#[8]门禁权限组
acc_level_doorGroup=Комбінація дверей
acc_level_openingPersonnel=Відкриття персоналу
acc_level_noDoor=Немає доступного товару. Спершу додайте пристрій.
acc_level_doorRequired=Необхідно вибрати двері.
acc_level_doorCount=Підрахунок дверей
acc_level_doorDelete=Видалити двері
acc_level_isAddDoor=Негайно додати двері до поточного рівня контролю доступу?
acc_level_master=Генеральний
acc_level_noneSelect=Будь ласка, додайте рівень контролю доступу.
acc_level_useDefaultLevel=Змінити рівень доступу до нового відділу?
acc_level_persAccSet=Налаштування контролю доступу персоналу
acc_level_visUsed={0} вже використовується модулем відвідувачів і не може бути видалений!
acc_level_doorControl=Контроль дверей
acc_level_personExceedMax=Кількість поточних груп дозволів ({0}) і максимальна кількість необов’язкових груп дозволів ({1})
acc_level_exportLevel=Експорт рівня доступу
acc_level_exportLevelDoor=Експорт дверей рівня доступу
acc_level_exportLevelPerson=Експорт персоналу рівня доступу
acc_level_importLevel=Рівень доступу до імпорту
acc_level_importLevelDoor=Імпорт дверей рівня доступу
acc_level_importLevelPerson=Імпорт персоналу рівня доступу
acc_level_exportDoorFileName=Відомості про рівень доступу дверей
acc_level_exportPersonFileName=Інформація про персонал рівня доступу
acc_levelImport_nameNotNull=Назва рівня доступу не може бути пустою
acc_levelImport_timeSegNameNotNull=Часовий пояс не може бути порожнім
acc_levelImport_areaNotExist=Зона не існує!
acc_levelImport_timeSegNotExist=Часовий пояс не існує!
acc_levelImport_nameExist=Назва рівня доступу {0} вже існує!
acc_levelImport_levelDoorExist=Двері рівня доступу {0} вже існують!
acc_levelImport_levelPersonExist=Персонал рівня доступу {0} вже існує!
acc_levelImport_noSpecialChar=Назва рівня доступу не може містити спеціальні символи!
#[10]首人常开
acc_firstOpen_setting=Відкрито після першої особи
acc_firstOpen_browsePerson=Переглянути персонал
#[11]多人组合开门
acc_combOpen_comboName=Назва комбінації
acc_combOpen_personGroupName=Назва групи
acc_combOpen_personGroup=Група комісування
acc_combOpen_verifyOneTime=Поточна кількість персоналу
acc_combOpen_eachGroupCount=Кількість персоналу відкриття в кожній группі
acc_combOpen_group=Група
acc_combOpen_changeLevel=Група відкриття
acc_combOpen_combDeleteGroup=Посилання на наявну початкову комбінацію, спершу видаліть.
acc_combOpen_ownedLevel=Належить до груп
acc_combOpen_mostPersonCount=Кількість учасників у групі не повинна перевищувати п’яти осіб.
acc_combOpen_leastPersonCount=Кількість груп не повинна бути менше двох осіб.
acc_combOpen_groupNameRepeat=Дублікат назви групи.
acc_combOpen_groupNotUnique=Групи відкритих дверей не повинні бути ідентичними.
acc_combOpen_persNumErr=Кількість групи перевищує фактичне значення вашого вибору, будь ласка, виберіть повторно!
acc_combOpen_combOpengGroupPersonShort=Після видалення людей кількість груп відкритих дверей для персоналу недостатня, спершу видаліть відкриту групу.
acc_combOpen_backgroundVerifyTip=Двері в пристрої з увімкненою фоновою перевіркою; не можна використовувати одночасно з відкритими дверима для кількох осіб!
#[12]互锁
acc_interlock_rule=Правило шлюза
acc_interlock_mode1Or2=Шлюз між {0} та {1}
acc_interlock_mode3=Шлюз між {0} та {1} та{2}
acc_interlock_mode4=Шлюз між {0} та{1} або між {2} та {3}
acc_interlock_mode5=Шлюз між {0} та {1} та {2} та {3}
acc_interlock_hasBeenSet=Має налаштування програми шлюзу
acc_interlock_group1=Група 1
acc_interlock_group2=Група 2
acc_interlock_ruleInfo=Між групами
acc_interlock_alreadyExists=Те ж саме правило міжблокування вже існує, будь ласка, не додайте його знову!
acc_interlock_groupInterlockCountErr=Внутрішня група вимагає принаймні двох дверей
acc_interlock_ruleType=Тип правила переблокування
#[13]反潜
acc_apb_rules=Правило заборони подвійного проходу
acc_apb_reader=Правило заборони подвійного проходу між зчитувачами дверей {0}
acc_apb_reader2=Правило заборони подвійного проходу між зчитувачами дверей: {0}, {1}
acc_apb_reader3=Правило заборони подвійного проходу між зчитувачами дверей: {0}, {1}, {2}
acc_apb_reader4=Правило заборони подвійного проходу між зчитувачами дверей: {0}, {1}, {2}, {3}
acc_apb_reader5=Заборона подвійного проходу, зчитувач на вихід {0}
acc_apb_reader6=Заборона подвійного проходу, зчитувач на вхід {0}
acc_apb_reader7=Заборона подвійного проходу між считувачами усіх 4 дверей
acc_apb_twoDoor=Заборона подвійного проходу між {0} та {1}
acc_apb_fourDoor=Заборона подвійного проходу між {0} та {1} , Заборона подвійного проходу між {2} та {3}
acc_apb_fourDoor2=Заборона подвійного проходу між {0} або {1} та {2} або {3}
acc_apb_fourDoor3=Заборона подвійного проходу між {0} та {1} або {2}
acc_apb_fourDoor4=Заборона подвійного проходу між {0} та {1} або {2} або {3}
acc_apb_hasBeenSet=Встановлено для заборони подвійного проходу
acc_apb_conflictWithGapb=Цей пристрій має глобальні налаштування заборони подвійного проходу і не може бути змінений!
acc_apb_conflictWithApb=Зона пристрою має налаштування заборони подвійного проходу і не може бути змінена!
acc_apb_conflictWithEntranceApb=Зона пристрою має вхідні налаштування заборони подвійного проходу, і не може бути змінена!
acc_apb_controlIn=Заборона подвійного проходу на вхід
acc_apb_controlOut=Заборона подвійного проходу на вихід
acc_apb_controlInOut=Заборона подвійного проходу на вхід та вихід
acc_apb_groupIn=Приєднатися до групи
acc_apb_groupOut=Вийти з групи
acc_apb_reverseName=Обережний антипідморський
acc_apb_door=Антипідморський корабль
acc_apb_readerHead=Читання голови проти підморського корабля
acc_apb_alreadyExists=Те ж саме антипідморське правило вже існує, будь ласка, не додайте його знову!
#[17]电子地图
acc_map_addDoor=Додати двері
acc_map_addChannel=Додати камеру
acc_map_noAccess=Немає дозволу на доступ до модуля карти, зверніться до адміністратора!
acc_map_noAreaAccess=Ви не маєте дозволу на електронну карту для цієї території, будь ласка, зв’яжіться з адміністратором!
acc_map_imgSizeError=Будь ласка, завантажте зображення, розмір якого менше ніж {0}МБ!
#[18]门禁事件记录
acc_trans_entity=Операція
acc_trans_eventType=Тип події
acc_trans_firmwareEvent=Події прошивки
acc_trans_softwareEvent=Програмна подія
acc_trans_today=Події за сьогодні
acc_trans_lastAddr=Остання відома позиція
acc_trans_viewPhotos=Переглянути фото
acc_trans_exportPhoto=Експорт фото
acc_trans_photo=Фото події контролю доступу
acc_trans_dayNumber=Дні
acc_trans_fileIsTooLarge=Експортований файл завеликий, зменште обсяг експорту
#[19]门禁验证方式
acc_verify_mode_onlyface=Обличчя
acc_verify_mode_facefp=Обличчя+Відбиток Пальця
acc_verify_mode_facepwd=Обличчя+Пароль
acc_verify_mode_facecard=Обличчя+Карта
acc_verify_mode_facefpcard=Обличчя+Відбиток Пальця+Карта
acc_verify_mode_facefppwd=Обличчя+Відбиток+Пароль
acc_verify_mode_fv=Вени пальця
acc_verify_mode_fvpwd=Вени Пальця+Пароль
acc_verify_mode_fvcard=Вени Пальця+Карта
acc_verify_mode_fvpwdcard=Вени Пальця+Пароль+Карта
acc_verify_mode_pv=Долоня
acc_verify_mode_pvcard=Долоня+Карта
acc_verify_mode_pvface=Долоня+Обличчя
acc_verify_mode_pvfp=Долоня+Відбиток Пальця
acc_verify_mode_pvfacefp=Долоня+Обличчя+Відиток Пальця
#[20]门禁事件编号
acc_eventNo_-1=Немає
acc_eventNo_0=Відкриття карткою
acc_eventNo_1=Прохід під час режиму вільного проходу
acc_eventNo_2=Перший співробітник - відкрито (Карта)
acc_eventNo_3=Відкриття комісуванням (Карта)
acc_eventNo_4=Відкриття аварійним паролем
acc_eventNo_5=Відкритт під час режиму проходу
acc_eventNo_6=Подія прив'язки
acc_eventNo_7=Зупинити тривогу
acc_eventNo_8=Віддалене відкриття
acc_eventNo_9=Віддалене закриття
acc_eventNo_10=Вимкнути часову зону режиму вільного проходу під час дня
acc_eventNo_11=Увімкнути часову зону режиму вільного проходу під час дня
acc_eventNo_12=Допоміжний вихід дистанційно відкритий
acc_eventNo_13=Допоміжний вихід дистанційно закритий
acc_eventNo_14=Відкрито за допомогою відбитка
acc_eventNo_15=Відкрито комісуванням(Відбиток)
acc_eventNo_16=Відбиток пальця в часовій зоні режиму вільного проходу
acc_eventNo_17=Відкриття карткою та Відбитком
acc_eventNo_18=Відкриття першою карткою (відбиток пальця)
acc_eventNo_19=Відкриття першою карткою (Картка та відбиток пальця)
acc_eventNo_20=Занадто короткий інтервал роботи
acc_eventNo_21=Неактивна часова зона дверей(Картка)
acc_eventNo_22=Некоректна часова зона
acc_eventNo_23=Доступ заборонено
acc_eventNo_24=Заборона подвійного проходу
acc_eventNo_25=Шлюз
acc_eventNo_26=Відкриття комісуванням(Карта)
acc_eventNo_27=Картка з відключеними правами доступу
acc_eventNo_28=Подовжений час очікування відкритих дверей
acc_eventNo_29=Термін дії картки закінчився
acc_eventNo_30=Помилка пароля
acc_eventNo_31=Занадто короткий інтервал відбитків пальців
acc_eventNo_32=Відкриття комісуванням(Відбиток)
acc_eventNo_33=Термін дії відбитка вийшов
acc_eventNo_34=Відбиток з відключеними правами доступа
acc_eventNo_35=Часова зона неактивних дверей (Відбиток пальця)
acc_eventNo_36=Часова зона неактивних дверей (Кнопка вихода)
acc_eventNo_37=Не вдалося закрити в часовій зоні режиму проходу
acc_eventNo_38=Повідомлення про втрату картки
acc_eventNo_39=Доступ вимкнено
acc_eventNo_40=Помилка відкриття комісуванням(Відбиток)
acc_eventNo_41=Помилка режиму перевірки
acc_eventNo_42=Помилка формату Wiegand
acc_eventNo_43=Час очікування перевірки заборони подвійного проходу вийшов
acc_eventNo_44=Помилка фонової верифікації
acc_eventNo_45=Час очікування фонової перевірки вийшов
acc_eventNo_47=Не вдалося надіслати команду
acc_eventNo_48=Помилка автентифікації кількох осіб (Карта)
acc_eventNo_49=Часова зона неактивності дверей (Пароль)
acc_eventNo_50=Інтервал пароля занадто короткий
acc_eventNo_51=Відкриття комісуванням(Пароль)
acc_eventNo_52=Помилка відкриття комісуванням(Пароль)
acc_eventNo_53=Термін дії пароля вийшов
acc_eventNo_100=Тривога тампера
acc_eventNo_101=Відкриття паролем примусу
acc_eventNo_102=Двері відчинені силою
acc_eventNo_103=Відкрито відбитком примусу
acc_eventNo_200=Двері відчинено коректно
acc_eventNo_201=Двері закриті коректно
acc_eventNo_202=Відкриття кнопкою виходу
acc_eventNo_203=Відкриття комісуванням(Карта та Відбиток)
acc_eventNo_204=Часова зона вільного проходу закінчилася
acc_eventNo_205=Дистанційо відкрито
acc_eventNo_206=Пристрій запущено
acc_eventNo_207=Відкриття паролем
acc_eventNo_208=Відкриття з правами суперкористувача
acc_eventNo_209=Спрацювння кнопки виходу(Без відкриття)
acc_eventNo_210=Відкриття протипожежних дверей
acc_eventNo_211=Закриття з правами суперкористувача
acc_eventNo_212=Увімкнути функцію керування ліфтом
acc_eventNo_213=Вимкнути функцію керування ліфтом
acc_eventNo_214=Відкриття комісуванням(Пароль)
acc_eventNo_215=Відкриття першим співробітником(Пароль)
acc_eventNo_216=Пароль під час часової зони вільного проходу
acc_eventNo_220=Допоміжний вхід відключено (Відкрито)
acc_eventNo_221=Допоміжний вхід замкнено (Закрито)
acc_eventNo_222=Фонова перевірка вдало
acc_eventNo_223=Фонова перевірка
acc_eventNo_225=Допоміжний вхід Нормальний
acc_eventNo_226=Тригер допоміжного входу
acc_newEventNo_0=Звичайною перевіркою відкрито
acc_newEventNo_1=Перевірка під час дії часової зони вільного проходу
acc_newEventNo_2=Преша особа - відкрито
acc_newEventNo_3=Відкрито комісуванням
acc_newEventNo_20=Занадто короткий інтервал операції
acc_newEventNo_21=Відкриття під час дії часової зони відключення дверей
acc_newEventNo_26=Очікування аутентифікацію кількох осіб
acc_newEventNo_27=Незареєстрований співробітник
acc_newEventNo_29=Термін реєстрації співробітника вийшов
acc_newEventNo_30=Помилка пароля
acc_newEventNo_41=Помилка режиму перевірки
acc_newEventNo_43=Штатний замок
acc_newEventNo_44=Помилка фонової верифікації
acc_newEventNo_45=Час фонової верифікації вийшов
acc_newEventNo_48=Помилка відкриття комісуванням
acc_newEventNo_54=Низький заряд батареї
acc_newEventNo_55=Негайно замініть батарею
acc_newEventNo_56=Некоректна операція
acc_newEventNo_57=Резервне живлення
acc_newEventNo_58=Сигнал нормального відкриття
acc_newEventNo_59=Некоректне управління
acc_newEventNo_60=Дврі зачинено зсередини
acc_newEventNo_61=Тиражується
acc_newEventNo_62=Заборонені користувачі
acc_newEventNo_63=Двері зачинені
acc_newEventNo_64=Часова зона неактивності кнопки виходу
acc_newEventNo_65=Часова зона неактивності допоміжного входу
acc_newEventNo_66=Помилка оновлення зчитувача
acc_newEventNo_67=Віддалене порівняння виконано (пристрій не авторизовано)
acc_newEventNo_68=Висока температура тіла – доступ заборонено
acc_newEventNo_69=Без маски – доступ заборонено
acc_newEventNo_70=Помилка зв’язку сервера порівняння облич
acc_newEventNo_71=Сервер порівняння обличчь відповідає нестабільно
acc_newEventNo_73=Невірний QR-код
acc_newEventNo_74=Термін дії QR-коду закінчився
acc_newEventNo_101=Тривога відкриття під примусом
acc_newEventNo_104=Тривога невірної картки
acc_newEventNo_105=Не вдається підключитися до сервера
acc_newEventNo_106=Відключення основного живлення
acc_newEventNo_107=Зниження живлення батареї
acc_newEventNo_108=Не вдається підключитися до головного пристрою
acc_newEventNo_109=Тривога тамперного контакту зчитуача
acc_newEventNo_110=Зчитувач офлайн
acc_newEventNo_112=Табліца розширення вимкнена
acc_newEventNo_114=Вхід нагадування пожежі відключено (виявлення рядка)
acc_newEventNo_115=Вхідний короткий коло нагадування пожежі (виявлення рядка)
acc_newEventNo_116=Додатковий вхід роз’ єднаний (виявлення рядка)
acc_newEventNo_117=Додатковий вхідний короткий круг (виявлення лінії)
acc_newEventNo_118=Вийти з’ єднаний перемикач (виявлення рядка)
acc_newEventNo_119=Вийти з комбінації коротких об’ єктів (виявлення об’ єктів)
acc_newEventNo_120=Магнітне розлучення дверей (виявлення лінії)
acc_newEventNo_121=Магнітний короткий круг дверей (виявлення лінії)
acc_newEventNo_159=Пульт дистанційного керування для відкриття дверей
acc_newEventNo_214=Підключено до сервера
acc_newEventNo_217=Підключено до головного пристрою успішно
acc_newEventNo_218=Перевірка ID карткою
acc_newEventNo_222=Фонова перевірка вдала
acc_newEventNo_223=Фонова перевірка
acc_newEventNo_224=Дзвонити у дзвінок
acc_newEventNo_227=Двічі відкрити двері
acc_newEventNo_228=Двічі закрити двері
acc_newEventNo_229=Відкрито по таймеру допоміжного виходу
acc_newEventNo_230=Закрито по таймеру допоміжного виходу
acc_newEventNo_232=Вдала перевірка
acc_newEventNo_233=Активувати блокування
acc_newEventNo_234=Деактивувати блокування
acc_newEventNo_235=Успішне оновлення зчитувача
acc_newEventNo_236=Тривогу тамперного контакту скасовано
acc_newEventNo_237=Зчитувач онлайн
acc_newEventNo_239=Виклик пристрою
acc_newEventNo_240=Виклик завершено
acc_newEventNo_243=Вхід нагадування пожежі відключено
acc_newEventNo_244=Вхідний короткий коло пожежного нагадування
acc_newEventNo_247=Дошка розширення онлайн
acc_newEventNo_4008=Відновлення мережі
acc_newEventNo_4014=Вхідний сигнал пожежі роз’ єднаний, кінцеві двері зазвичай відкриті
acc_newEventNo_4015=Двері вже онлайн
acc_newEventNo_4018=Відкрити порівняння
acc_newEventNo_5023=Обмежений стан захисту від пожежі
acc_newEventNo_5024=Час перевірки декількох персоналів
acc_newEventNo_5029=Помилка порівняння задньої частини
acc_newEventNo_6005=Ємність запису досягає верхньої межі
acc_newEventNo_6006=Короткі лінії (RS485)
acc_newEventNo_6007=Короткий ланцюг у ланцюзі (Віган)
acc_newEventNo_6011=Door is offline
acc_newEventNo_6012=alarm disassembly door
acc_newEventNo_6013=Пожарний вхідний сигнал увімкнено, двері відкриваються зазвичай
acc_newEventNo_6015=Відновити застосування енергії пристрою розширення
acc_newEventNo_6016=Відновити параметри фабрики пристроїв
acc_newEventNo_6070=Порівняння безпосереднього сервісу (заборонений список)
acc_eventNo_undefined=Номер події не визначено
acc_advanceEvent_500=Глобальна заборона подвійного проходу (logical)
acc_advanceEvent_501=Доступність співробітника(використовуючи дату)
acc_advanceEvent_502=Номер особи контролю
acc_advanceEvent_503=Глобальний шлюз
acc_advanceEvent_504=Контроль маршруту
acc_advanceEvent_505=Глобальна заборона подвійного проходу(timed)
acc_advanceEvent_506=Глобальна заборона подвійного проходу(timed logical)
acc_advanceEvent_507=Доступність співробітника(після першого використання дійсних днів)
acc_advanceEvent_508=Доступність співробітника(використовувати кількість разів)
acc_advanceEvent_509=Помилка фонової верефікації (незареєстрований персонал)
acc_advanceEvent_510=Помилка фонової верефікації (помилка даних)
acc_alarmEvent_701=Порушення DMR(встановлення правил: {0})
#[21]实时监控
acc_rtMonitor_openDoor=Відкрито
acc_rtMonitor_closeDoor=Закрито
acc_rtMonitor_remoteNormalOpen=Віддалено відкрито
acc_rtMonitor_realTimeEvent=Події у реальному часі
acc_rtMonitor_photoMonitor=Моніторинг фото
acc_rtMonitor_alarmMonitor=Моніторинг тривог
acc_rtMonitor_doorState=Стан дверей
acc_rtMonitor_auxOutName=Назва додаткового виходу
acc_rtMonitor_nonsupport=Не підтримується
acc_rtMonitor_lock=Закрито
acc_rtMonitor_unLock=Вікрито
acc_rtMonitor_disable=Відключено
acc_rtMonitor_noSensor=Немає сенсора стану дверей
acc_rtMonitor_alarm=Тривога
acc_rtMonitor_openForce=Відкрито силою
acc_rtMonitor_tamper=Тампер
acc_rtMonitor_duressPwdOpen=Відкриття паролем примусу
acc_rtMonitor_duressFingerOpen=Відкриття відбитком примусу
acc_rtMonitor_duressOpen=Відкрито під примусом
acc_rtMonitor_openTimeout=Вийшов час відкриття
acc_rtMonitor_unknown=Невідомо
acc_rtMonitor_noLegalDoor=Немає жодної двері, яка відповідає умові.
acc_rtMonitor_noLegalAuxOut=Жоден допоміжний вихід не відповідає умові!
acc_rtMonitor_curDevNotSupportOp=Поточний стан пристрою не підтримує цю операцію!
acc_rtMonitor_curNormalOpen=На даний момент відкритий
acc_rtMonitor_whetherDisableTimeZone=Поточний стан дверей завжди відкритий.
acc_rtMonitor_curSystemNoDoors=У системі немає дверей або не можна знайти двері, які відповідають вашим вимогам.
acc_rtMonitor_cancelAlarm=Скасувати тривогу
acc_rtMonitor_openAllDoor=Відкрити всі двері
acc_rtMonitor_closeAllDoor=Закрити всі двері
acc_rtMonitor_confirmCancelAlarm=Дійсно скасувати всі тривоги?
acc_rtMonitor_calcelAllDoor=Скасувати всі тривоги
acc_rtMonitor_initDoorStateTip=Отримати всі двері, авторизованих у системі...
acc_rtMonitor_alarmEvent=Події тривог
acc_rtMonitor_ackAlarm=Облік
acc_rtMonitor_ackAllAlarm=Повний облік
acc_rtMonitor_ackAlarmTime=Облік часу
acc_rtMonitor_sureToAckThese=Дійсно скасувати тривогу {0}? Після вашого підтвердження тривоги будуть скасовані.
acc_rtMonitor_sureToAckAllAlarm=Ви впевнені, що хочете скасувати всі тривоги? Після вашого підтвердження всі тривоги будуть скасовані.
acc_rtMonitor_noSelectAlarmEvent=Будь ласка, виберіть для підтвердження події тривог!
acc_rtMonitor_noAlarmEvent=У поточній системі немає подій тривог!
acc_rtMonitor_forcefully=Скасувати тривгу (Двері відчинені силою)
acc_rtMonitor_addToRegPerson=Додано до зареєстрованої особи
acc_rtMonitor_cardExist=Цю картку призначено {0}, і вона не може бути видана повторно.
acc_rtMonitor_opResultPrompt=Надіслано {0} запитів вдало, не вдалося {1}.
acc_rtMonitor_doorOpFailedPrompt=Не вдалося надіслати запити на наступні двері. Спробуйте ще раз!
acc_rtMonitor_remoteOpen=Віддалено відкрито
acc_rtMonitor_remoteClose=Відалено закрито
acc_rtMonitor_alarmSoundClose=Звук тривоги відключено
acc_rtMonitor_alarmSoundOpen=Звук тривоги включено
acc_rtMonitor_playAudio=Звукові нагадування
acc_rtMonitor_isOpenShowPhoto=Увімкнути функцію відображення фотографій
acc_rtMonitor_isOpenPlayAudio=Увімкнути функцію звукового сповіщення
acc_rtm_open=Віддалено відкрито кнопкою
acc_rtm_close=Віддалено закрито кнопкою
acc_rtm_eleModule=Ліфт
acc_cancelAlarm_fp=Скасувати тривогу (Відкриття відбитком примусу)
acc_cancelAlarm_pwd=Скасувати тривогу (Відкриття паролем примусу)
acc_cancelAlarm_timeOut=Скасувати тривогу (Подовжений час очікування відкритих дверей вийшов)
#定时同步设备时间
acc_timing_syncDevTime=Планування синхронізації часу пристроя
acc_timing_executionTime=Час виконання
acc_timing_theLifecycle=Життєвий цикл
acc_timing_errorPrompt=Некоректне введення
acc_timing_checkedSyncTime=Будь ласка, оберіть час синхронізації.
#[25]门禁报表
acc_trans_hasAccLevel=Наявність рівня доступу
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Будь ласка, додайте зону
acc_zone_code=Код зони
acc_zone_parentZone=Батьківська зона
acc_zone_parentZoneCode=Код батьківської зони
acc_zone_parentZoneName=Назва батьківської зони
acc_zone_outside=Зовні
#[G2]读头定义
acc_readerDefine_readerName=Назва зчитувача
acc_readerDefine_fromZone=Іде з
acc_readerDefine_toZone=Прямує до
acc_readerDefine_delInfo1=На зону цього визначення зчитувача посилається розширена функція контролю доступу, і її не можна видалити!
acc_readerDefine_selReader=Вибрати зчитувач
acc_readerDefine_selectReader=Будь ласка, додайте зчитувач!
acc_readerDefine_tip=Після виходу персоналу за межі зони особовий запис буде видалено.
#[G3]全局反潜
acc_gapb_zone=Зона
acc_gapb_whenToResetGapb=Скидання часу заборони подвійного проходу
acc_gapb_apbType=Тип заборони повійного прохду
acc_gapb_logicalAPB=Заборона подвійного проходу (logical)
acc_gapb_timedAPB=Приурочена заборона подвйного проходу
acc_gapb_logicalTimedAPB=Приурочена зборона подвійного проходу (logical)
acc_gapb_lockoutDuration=Тривалість блокування
acc_gapb_devOfflineRule=Якщо пристрій офлайн
acc_gapb_standardLevel=Стандартний рівень доступу
acc_gapb_accessDenied=Доступ заборонено
acc_gapb_doorControlZone=Наступні двері контролюють доступ у зону та з неї
acc_gapb_resetStatus=Скинути статус заборони подвійного проходу
acc_gapb_obeyAPB=Дотримуватись правил заборони подвійного проходу
acc_gapb_isResetGAPB=Скинути заборону подвіййного проходу
acc_gapb_resetGAPBSuccess=Скинуто статус заборони подвійного проходу вдало
acc_gapb_resetGAPBFaile=Помилка скидання статусу заборони подвійного проходу
acc_gapb_chooseArea=Будь ласка, виберіть ще раз.
acc_gapb_notDelInfo1=Зона доступу є найвищою зоною доступу і не може бути видалена.
acc_gapb_notDelInfo2=На зону посилається зчитувач, і її не можна видалити.
acc_gapb_notDelInfo3=На зону посилаються розширені функції контролю доступу, і її неможливо видалити.
acc_gapb_notDelInfo4=На зону доступу посилається LED і її неможливо видалити!
acc_gapb_zoneNumRepeat=Номери контрольного домену повторюються
acc_gapb_zoneNameRepeat=Контрольні доменні імена мають дублікати
acc_gapb_personResetGapbPre=Дійсно скинути?
acc_gapb_personResetGapbSuffix=особисті правила заборони подвійного проходу?
acc_gapb_apbPrompt=Одні двері не можна використовувати для керування двома відносно незалежними межами периметра.
acc_gapb_occurApb=Відбувся захист від зворотного проходження
acc_gapb_noOpenDoor=Не відкривати двері
acc_gapb_openDoor=Відкрити двері
acc_gapb_zoneNumLength=Довжина більше 20 символів
acc_gapb_zoneNameLength=Довжина більше 30 символів
acc_gapb_zoneRemarkLength=Довжина більше 50 символів
acc_gapb_isAutoServerMode=Виявлено пристрій без функції фонової перевірки, що може вплинути на функцію. Відкрити зараз?
acc_gapb_applyTo=Застосувати до
acc_gapb_allPerson=Весь персонал
acc_gapb_justSelected=Тільки відібраний персонал
acc_gapb_excludeSelected=Виключити вибраний персонал
#[G4]who is inside
acc_zoneInside_lastAccessTime=Час останнього доступу
acc_zoneInside_lastAccessReader=Останній досту до зчитувача
acc_zoneInside_noPersonInZone=У зоні немає людей
acc_zoneInside_noRulesInZone=Немає встановлених правил.
acc_zoneInside_totalPeople=Всього людей
acc_zonePerson_selectPerson=Будь ласка, виберіть особу або відділ!
#[G5]路径
acc_route_name=Назва маршруту
acc_route_setting=Налаштування маршруту
acc_route_addReader=Додати зчитувач
acc_route_delReader=Видалити зчитувач
acc_route_defineReaderLine=Визначити рядок зчитувача
acc_route_up=Догори
acc_route_down=Вниз
acc_route_selReader=Виберати зчитувач після операції.
acc_route_onlyOneOper=Можна вибрати лише один зчитувач для роботи.
acc_route_readerOrder=Послідовність визначена зчитувачем
acc_route_atLeastSelectOne=Будь ласка, виберіть хоча б один зчитувач!
acc_route_routeIsExist=Цей маршрут вже існує!
#[G6]DMR
acc_dmr_residenceTime=Час проживання
acc_dmr_setting=Налаштування DMR
#[G7]Occupancy
acc_occupancy_max=Максимальна місткість
acc_occupancy_min=Мінімальна місткість
acc_occupancy_unlimit=Необмежений
acc_occupancy_note=Максимальна місткість повинна бути більше мінімальної!
acc_occupancy_containNote=Будь ласка, встановіть принаймні одну з максимальної/мінімальної ємності!
acc_occupancy_maxMinValid=Будь ласка, встановіть ємність більше 0.
acc_occupancy_conflict=У цій зоні встановлені правила контролю зайнятості.
acc_occupancy_maxMinTip=Відсутність значення потужності означає відсутність обмежень.
#card availability
acc_personLimit_zonePropertyName=Назва властивості зони
acc_personLimit_useType=Використовувати
acc_personLimit_userDate=Дійсна дата
acc_personLimit_useDays=Після першого використання у дійсні дні
acc_personLimit_useTimes=Використовувати кілька разів
acc_personLimit_setZoneProperty=Встановити властивості зони
acc_personLimit_zoneProperty=Властивості зони
acc_personLimit_availabilityName=Ім'я доступності
acc_personLimit_days=Днів
acc_personLimit_Times=Разів
acc_personLimit_noDel=На вибрані властивості зони контролю доступу є посилання і не можуть бути видалені!
acc_personLimit_cannotEdit=На атрибут області доступу є посилання, не може бути змінений!
acc_personLimit_detail=Деталі
acc_personLimit_userDateTo=Дійсний до
acc_personLimit_addPersonRepeatTip=Особу у вибраному відділі додано до атрибута області контролю доступу, будь ласка, виберіть відділ ще раз!
acc_personLimit_leftTimes=Залишилося {0} разів
acc_personLimit_expired=Термін дії закінчився
acc_personLimit_unused=Не використовується
#全局互锁
acc_globalInterlock_addGroup=Додати групу
acc_globalInterlock_delGroup=Видалити групу
acc_globalInterlock_refuseAddGroupMessage=Той самий шлюз, двері не можуть бути продубльовані в межах доданої групи
acc_globalInterlock_refuseAddlockMessage=Додані двері з'явилися в інших групах шлюзових пристроїв
acc_globalInterlock_refuseDeleteGroupMessage=Будь ласка, видаліть дані, пов’язані зі шлюзом
acc_globalInterlock_isGroupInterlock=Груповий шлюз
acc_globalInterlock_isAddTheDoorImmediately=Додати двері зараз
acc_globalInterlock_isAddTheGroupImmediately=Додати групу зараз
#门禁参数设置
acc_param_autoEventDev=Автоматичне завантаження журналу подій кількості одночасних пристроїв
acc_param_autoEventTime=Автоматичне завантаження журналу подій одночасних інтервалів
acc_param_noRepeat=Не допускається повторення адрес електронної пошти, заповніть, будь ласка.
acc_param_most18=Додайте адреси електронної пошти, до 18
acc_param_deleteAlert=Неможливо видалити всі поля електронних адрес.
acc_param_invalidOrRepeat=Помилка формату електронної адреси або повторення адреси електронної пошти.
#全局联动
acc_globalLinkage_noSupport=Вибрані на даний момент двері не підтримують функції блокування та деактивації блокування.
acc_globalLinkage_trigger=Тригер глобальної прив'язки
acc_globalLinkage_noAddPerson=Правило зв’язування не містить тригера, пов’язаного з персоналом, за замовчуванням, застосовного до всього персоналу!
acc_globalLinkage_selectAtLeastOne=Будь ласка, виберіть принаймні одну операцію тригера прив'язки!
acc_globalLinkage_selectTrigger=Будь ласка, додайте умови активації прив'язки!
acc_globalLinkage_selectInput=Будь ласка, додайте точку введення!
acc_globalLinkage_selectOutput=Будь ласка, додайте вихідну точку!
acc_globalLinkage_audioRemind=Прив'язка голосових підказок
acc_globalLinkage_audio=Звук прив'язки
acc_globalLinkage_isApplyToAll=Вжити до всього персоналу
acc_globalLinkage_scope=Діапазон персоналу
acc_globalLinkage_everyPerson=Будь-який
acc_globalLinkage_selectedPerson=Обраний
acc_globalLinkage_noSupportPerson=Не підтримується
acc_globalLinkage_reselectInput=Тип умов тригера змінився, будь ласка, повторно виберіть точку введення!
acc_globalLinkage_addPushDevice=Будь ласка, додайте функцію підтримки цього пристрою
#其他
acc_InputMethod_tips=Будь ласка, перейдіть до режиму введення англійською мовою!
acc_device_systemCheckTip=Пристрій доступу не існує!
acc_notReturnMsg=Інформація не повернута
acc_validity_period=Термін дії ліцензії пройшов; функція не може працювати.
acc_device_pushMaxCount=Система вже існує на пристроях ({0}), досягнуто ліміту ліцензій, неможливо додати пристрій!
acc_device_videoHardwareLinkage=Встановити відео прив'язку
acc_device_videoCameraIP=IP відеокамери
acc_device_videoCameraPort=Порт відеокамери
acc_location_unable=Точка події не додана до електронної карти, не вдалося знайти місце
acc_device_wgDevMaxCount=Пристрій досяг ліміту ліцензії в системі і не може змінити налаштування!
#自定义报警事件
acc_deviceEvent_selectSound=Виберіть аудіофайли.
acc_deviceEvent_batchSetSoundErr=Помилка пакетного налаштування звуків тривоги.
acc_deviceEvent_batchSet=Встановити аудіо
acc_deviceEvent_sound=Звук події
acc_deviceEvent_exist=Вже існує
acc_deviceEvent_upload=Завантажити
#查询门最近发生事件
acc_doorEventLatestHappen=Запитати останні події з дверей
#门禁人员信息
acc_pers_delayPassage=Продовжити зону проходу
#设备容量提示
acc_dev_usageConfirm=Поточна потужність перевищує 90% обладнання
acc_dev_immediateCheck=Перевірити зараз
acc_dev_inSoftware=У програмному забезпеченні
acc_dev_inFirmware=У прошивці
acc_dev_get=Отримати
acc_dev_getAll=Отримати Усе
acc_dev_loadError=Помилка завантаження
#Reader
acc_reader_inout=Вхід/Вихід
acc_reader_lightRule=Правила освітлення
acc_reader_defLightRule=Правило за замовчуванням
acc_reader_encrypt=Шифрування
acc_reader_allReaderOfCurDev=Всі зчитувачі поточного пристрою
acc_reader_tip1=Шифрування застосовується на всі зчитувачі на поточному пристрої!
acc_reader_tip2=Режим ID доступний лише для зчитувачів, які підтримують цю функцію!
acc_reader_tip3=Тип протоколу RS485 копіюється на всі зчитувачі поточного пристрою. Налаштування набудуть чинності після перезавантаження пристрою!
acc_reader_tip4=Опція приховування деякої інформації про персонал копіюється на всі зчитувачі одного пристрою за замовчуванням!
acc_reader_commType=Тип зв'язку
acc_reader_commAddress=Адреса зв'язку
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=Вимкнено
acc_readerComAddress_repeat=Дублювання адреси зв'язку
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=Адреса RS485
acc_readerCommType_wgAddress=Адреса Wiegand
acc_reader_macError=Будь ласка, введіть адресу Mac у правильному форматі!
acc_reader_machineType=Тип зчитувача
acc_reader_readMode=Режим
acc_reader_readMode_normal=Звичайний режим
acc_reader_readMode_idCard=Режим ID карти
acc_reader_note=Порада: можна вибрати лише пристрої контролю доступу в тій самій зоні ({0}), що й камера.
acc_reader_rs485Type=Тип протоколу RS485
acc_reader_userLock=Блокування доступу персоналу
acc_reader_userInfoReveal=Приховати інформацію про персонал
#operat
acc_operation_pwd=Пароль
acc_operation_pwd_error=Помилка пароля
acc_new_input_not_same=Введення нового ключа не є послідовним
acc_op_set_keyword=Налаштування ліцензійного ключа
acc_op_old_key=Старий ключ
acc_op_new_key=Новий ключ
acc_op_cofirm_key=Ключ підтвердження
acc_op_old_key_error=Помилка старого ключа
#验证方式规则
acc_verifyRule_name=Назва првила
acc_verifyRule_door=Верифікація двері
acc_verifyRule_person=Підтвердження співробітника
acc_verifyRule_copy=Скопіювати налаштування понеділка на інші будні:
acc_verifyRule_tip1=Будь ласка, виберіть принаймні один режим перевірки!
acc_verifyRule_tip2=Якщо правило містить режим перевірки особи, ви можете додати двері лише за допомогою зчитувача Wiegand!
acc_verifyRule_tip3=Зчитувач RS485 може стежити лише за режимом перевірки дверей, не підтримує режим перевірки персоналу.
acc_verifyRule_oldVerifyMode=Старий режим перевірки
acc_verifyRule_newVerifyMode=Новий режим перевірки
acc_verifyRule_newVerifyModeSelectTitle=Виберіть новий метод підтвердження
acc_verifyRule_newVerifyModeNoSupportTip=Немає жодного пристрою, який підтримує новий метод перевірки!
#Wiegand Test
acc_wiegand_beforeCard=Довжина нової картки ({0} бітів) не дорівнює попередній картці!
acc_wiegand_curentCount=Довжина поточного номера картки: {0} біт
acc_wiegand_card=Картка
acc_wiegand_readCard=Зчитати картку
acc_wiegand_clearCardInfo=Очистити інформацію про картку
acc_wiegand_originalCard=Оригінальний номер картки
acc_wiegand_recommendFmt=Рекомендований формат картки
acc_wiegand_parityFmt=Формат непарної паритети
acc_wiegand_withSizeCode=Автоматичне обчислення коду сайту, якщо код сайту залишено порожнім
acc_wiegand_tip1=Ці картки можуть не належати до однієї партії карток.
acc_wiegand_tip2=Код сайту:{0},номер картки:{1}, не збігається з оригінальним номером картки. Будь ласка, перевірте введений код сайту та номер картки ще раз!
acc_wiegand_tip3=Введений номер картки ({0}) не може збігатися з оригінальним номером картки. Будь ласка, перевірте ще раз!
acc_wiegand_tip4=Введений код сайту ({0}) не може збігатися з оригінальним номером картки. Будь ласка, перевірте ще раз!
acc_wiegand_tip5=Якщо ви хочете використовувати цю функцію, залиште всі стовпці коду сайту порожніми!
acc_wiegand_warnInfo1=Коли ви продовжуєте зчитувати нову картку, вручну перейдіть на наступну картку.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Плата Входу/Виходу
acc_LCDRTMonitor_current=Актуальна інформація про персонал
acc_LCDRTMonitor_previous=Попередня інформація про персонал
#api
acc_api_levelIdNotNull=Ідентифікатор рівня доступу не може бути порожнім
acc_api_levelExist=Рівень доступу існує
acc_api_levelNotExist=Рівня доступу не існує
acc_api_areaNameNotNull=Регіон не може бути порожнім
acc_api_levelNotHasPerson=Немає співробітників у рівні доступу
acc_api_doorIdNotNull=Ідентифікатор дверей не може бути порожнім
acc_api_doorNameNotNull=Назва двері не може бути порожньою
acc_api_doorIntervalSize=Тривалість відкриття дверей повинна бути від 1 до 254
acc_api_doorNotExist=Двері не існує
acc_api_devOffline=Пристрій офлайн або вимкнено
acc_api_devSnNotNull=Серіний номер пристрою не може бути порожнім
acc_api_timesTampNotNull=Часовий печат не може бути порожнім
acc_api_openingTimeCannotBeNull=Час відкриття дверей не може бути порожнім
acc_api_parameterValueCannotBeNull=Значення параметра не може бути порожнім
acc_api_deviceNumberDoesNotExist=Серійний номер пристрою не існує
acc_api_readerIdCannotBeNull=Ідентифікатор читача не може бути нульовим
acc_api_theReaderDoesNotExist=Зчитувач не існує
acc_operate_door_notInValidDate=На даний момент не діє час дистанційного відкриття, будь ласка, зв’яжіться з адміністратором у разі потреби!
acc_api_doorOffline=Двері офлайн або вимкнено
#门禁信息自动导出
acc_autoExport_title=Автоекспорт транзакцій
acc_autoExport_frequencyTitle=Частота автоекспорту
acc_autoExport_frequencyDay=Протягом дня
acc_autoExport_frequencyMonth=Протягом місяця
acc_autoExport_firstDayMonth=Перший день місяця
acc_autoExport_specificDate=Конкретна дата
acc_autoExport_exportModeTitle=Режим експорту
acc_autoExport_dailyMode=Щоденні транзакції
acc_autoExport_monthlyMode=Щомісячні трансакції (усі транзакції між датою минулого місяця та датою цього місяця)
acc_autoExport_allMode=Усі дані (експортувати до 30000 одиниць даних)
acc_autoExport_recipientMail=Поштова скринька одержувача
#First In And Last Out
acc_inOut_inReaderName=Назва зчитувача першого входу
acc_inOut_firstInTime=Час першого входу
acc_inOut_outReaderName=Назва зчитувача останнього виходу
acc_inOut_lastOutTime=Час останнього виходу
#防疫参数
acc_dev_setHep=Встановити параметри визначення маски та температури
acc_dev_enableIRTempDetection=Увімкнути температурний скринінг за допомогою інфрачервоного випромінювання
acc_dev_enableNormalIRTempPass=Заборонити доступ, коли температура перевищує діапазон
acc_dev_enableMaskDetection=Увімкнути виявлення маски
acc_dev_enableWearMaskPass=Заборонити доступ без маски
acc_dev_tempHighThreshold=Поріг тривоги високої температури
acc_dev_tempUnit=Одиниця температури
acc_dev_tempCorrection=Корекція відхилення температури
acc_dev_enableUnregisterPass=Дозволити доступ незареєстрованим людям
acc_dev_enableTriggerAlarm=Запуск зовнішньої сигналізації
#联动邮件
acc_mail_temperature=Температура тіла
acc_mail_mask=Чи необхідно носити маску
acc_mail_unmeasured=Невиміряний
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Глобальні події Digifort
acc_digifort_chooseDigifortEvents=Вибрати глобальні події Digifort
acc_digifort_eventExpiredTip=Якщо глобальну подію буде видалено з сервера Digifort, воно буде червоним.
acc_digifort_checkConnection=Будь ласка, перевірте, чи правильна інформація про підключення сервера Digifort.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Додати контакт
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=Встановити розширені параметри
acc_extendParam_faceUI=Дисплей інтерфейсу
acc_extendParam_faceParam=Параметри обличчя
acc_extendParam_accParam=Параметри контроля доступу
acc_extendParam_intercomParam=Параметри візуального інтеркому
acc_extendParam_volume=Гучність
acc_extendParam_identInterval=Інтервал ідентифікації (мс)
acc_extendParam_historyVerifyResult=Відображати результати перевірки за часом
acc_extendParam_macAddress=Відображати MAC адресу
acc_extendParam_showIp=Відображати IP адресу
acc_extendParam_24HourFormat=Відображати 24-часовий формат
acc_extendParam_dateFormat=Формат дати
acc_extendParam_1NThreshold=1: N поріг
acc_extendParam_facePitchAngle=Кут нахилу обличчя
acc_extendParam_faceRotationAngle=Кут повороту обличчя
acc_extendParam_imageQuality=Якість зображення
acc_extendParam_miniFacePixel=Мінімально пікселів обличчя
acc_extendParam_biopsy=Увімкнути біопсію
acc_extendParam_showThermalImage=Показати теплові зображення
acc_extendParam_attributeAnalysis=Увімкнути аналіз атрибутів
acc_extendParam_temperatureAttribute=Атрибут визначення температури
acc_extendParam_maskAttribute=Атрибут виявлення маски
acc_extendParam_minTemperature=Мінімальна температура
acc_extendParam_maxTemperature=Максимальна температура
acc_extendParam_gateMode=Режим воріт
acc_extendParam_qrcodeEnable=Увімкнути функцію QR коду
#可视对讲
acc_dev_intercomServer=Адреса візуальної інтеркомунальної служби
acc_dev_intercomPort=Візуальний сервер інтерком
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Рівень синхронізації
# 夏令时名称
acc_dsTimeUtc_none=не встановлено
acc_dsTimeUtc_AreaNone=Немає літнього часу для цього часового поясу
acc_dsTimeUtc1000_0=Канберра, Мельбурн, Сідней
acc_dsTimeUtc1000_1=Хобарт
acc_dsTimeUtc_0330_0=Ньюфаундленд
acc_dsTimeUtc_1000_0=Алеутські острови
acc_dsTimeUtc_0200_0=Середня Атлантика – застосовано
acc_dsTimeUtc0930_0=Аделаїда
acc_dsTimeUtc_0100_0=Азорські острови
acc_dsTimeUtc_0400_0=Атлантичний час (Канада)
acc_dsTimeUtc_0400_1=Сантьяго
acc_dsTimeUtc_0400_2=Асунсьйон
acc_dsTimeUtc_0300_0=Гренландія
acc_dsTimeUtc_0300_1=Острови Сен-П'єр і Мікелон
acc_dsTimeUtc0200_0=Кишинів
acc_dsTimeUtc0200_1=Гельсінкі, Київ, Рига, Софія, Таллінн, Вільнюс
acc_dsTimeUtc0200_2=Афіни, Бухарест
acc_dsTimeUtc0200_3=Єрусалим
acc_dsTimeUtc0200_4=Амман
acc_dsTimeUtc0200_5=Бейрут
acc_dsTimeUtc0200_6=Дамаск
acc_dsTimeUtc0200_7=Газа, Хеврон
acc_dsTimeUtc0200_8=Джуба
acc_dsTimeUtc_0600_0=Центральний час (США та Канада)
acc_dsTimeUtc_0600_1=Гвадалахара, Мехіко, Монтеррей
acc_dsTimeUtc_0600_2=Острів Пасхи
acc_dsTimeUtc1300_0=Острови Самоа
acc_dsTimeUtc_0500_0=Гавана
acc_dsTimeUtc_0500_1=Східний час (США та Канада)
acc_dsTimeUtc_0500_2=Гаїті
acc_dsTimeUtc_0500_3=Індіана (Схід)
acc_dsTimeUtc_0500_4=Острови Теркс і Кайкос
acc_dsTimeUtc_0800_0=Тихоокеанський час (США та Канада)
acc_dsTimeUtc_0800_1=Нижня Каліфорнія
acc_dsTimeUtc0330_0=Тегеран
acc_dsTimeUtc0000_0=Дублін, Единбург, Лісабон, Лондон
acc_dsTimeUtc1200_0=Фіджі
acc_dsTimeUtc1200_1=Петропавловськ, Камчатка
acc_dsTimeUtc1200_2=Веллінгтон, Окленд
acc_dsTimeUtc1100_0=Острів Норфолк
acc_dsTimeUtc_0700_0=Чихуахуа, Ла-Пас, Мазатлан
acc_dsTimeUtc_0700_1=Гірський час (США та Канада)
acc_dsTimeUtc0100_0=Белград, Братислава, Будапешт, Любляна, Прага
acc_dsTimeUtc0100_1=Сараєво, Скоп'є, Варшава, Загреб
acc_dsTimeUtc0100_2=Касабланка
acc_dsTimeUtc0100_3=Брюссель, Копенгаген, Мадрид, Париж
acc_dsTimeUtc0100_4=Амстердам, Берлін, Берн, Рим, Стокгольм, Відень
acc_dsTimeUtc_0900_0=Аляска
#安全点(muster point)
acc_leftMenu_accMusterPoint=Точка збору
acc_musterPoint_activate=Активувати
acc_musterPoint_addDept=Додати відділ
acc_musterPoint_delDept=Видалити відділ
acc_musterPoint_report=Звіт про бали збору
acc_musterPointReport_sign=Увійти вручну
acc_musterPointReport_generate=Створити звіти
acc_musterPoint_addSignPoint=Додати точку входу
acc_musterPoint_delSignPoint=Видалити точку входу
acc_musterPoint_selectSignPoint=Будь ласка, додайте знак!
acc_musterPoint_signPoint=Точка знака
acc_musterPoint_delFailTip=Уже є активовані бали збору, і їх неможливо видалити!
acc_musterPointReport_enterTime=Введіть час
acc_musterPointReport_dataAnalysis=Аналіз даних
acc_musterPointReport_safe=безпечно
acc_musterPointReport_danger=небезпека
acc_musterPointReport_signInManually=ручний перфоратор
acc_musterPoint_editTip=Точка збору активна і не може бути відредагована!
acc_musterPointEmail_total=Очікувана участь:
acc_musterPointEmail_safe=Перевірено (безпечно):
acc_musterPointEmail_dangerous=У небезпеці:
acc_musterPoint_messageNotification=Повідомлення про повідомлення під час активації
acc_musterPointReport_sendEmail=Запланований звіт про штовху
acc_musterPointReport_sendInterval=Інтервал надсилання
acc_musterPointReport_sendTip=Будь ласка, переконайтеся, що вибраний метод повідомлення буде успішно налаштовано, інакше повідомлення не буде надіслано звичайно!
acc_musterPoint_mailSubject=Нагадування про нагадування
acc_musterPoint_mailContent=Негайно зберіться на "{0}" і ввійдіть на пристрої "{1}", дякую!
acc_musterPointReport_mailHead=Привіт, це надзвичайний звіт. Будь ласка, перегляньте.
acc_musterPoint_visitorsStatistics=Статистика відвідувачів
# 报警监控
acc_alarm_priority=Пріоритет
acc_alarm_total=Усього
acc_alarm_today=Сьогоднішній запис
acc_alarm_unhandled=Непідтверджено
acc_alarm_inProcess=Обробка
acc_alarm_acknowledged=Підтверджено
acc_alarm_top5=Топ 5 подій тривоги
acc_alarm_monitoringTime=Час моніторингу
acc_alarm_history=Історія обробки сигналів
acc_alarm_acknowledgement=Обробка записів
acc_alarm_eventDescription=Деталі події
acc_alarm_acknowledgeText=Після вибору на призначену поштову скриньку буде надіслано повідомлення про подію тривоги.
acc_alarm_emailSubject=Додати запис обробки
acc_alarm_mute=Вимкнути звук
acc_alarm_suspend=Призупинити
acc_alarm_confirmed=Подія була підтверджена.
acc_alarm_list=Журнал тривог
#ntp
acc_device_setNTPService=Налаштування сервера NTP
acc_device_setNTPServiceTip=Введіть кілька адрес серверів, розділених комами (,) або крапкою з комою (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=Під час операції контролера доступу суперкористувач не обмежено правилами щодо часових зон, анти-пасссбаку і інтерблокування і має надзвичайно високий пріоритет відкриття дверей.
acc_editPerson_delayPassageTip=Розширити час чекання персоналу через точки доступу. Відповідний фізичним викликом або людям з іншими вадами.
acc_editPerson_disabledTip=Тимчасово вимкнути рівень доступу персоналу.
#门禁向导
acc_guide_title=Майстер налаштування модуля контролю доступу
acc_guide_addPersonTip=Вам потрібно додати особу та відповідні облікові дані (обличчя, відбиток пальця, картку, долоню або пароль); якщо ви вже додали, пропустіть цей крок безпосередньо
acc_guide_timesegTip=Будь ласка, налаштуйте дійсний період часу відкриття
acc_guide_addDeviceTip=Будь ласка, додайте відповідний пристрій як точку доступу
acc_guide_addLevelTip=Додати рівень контролю доступу
acc_guide_personLevelTip=Призначте особі відповідні повноваження контролю доступу
acc_guide_rtMonitorTip=Перевірте записи контролю доступу в реальному часі
acc_guide_rtMonitorTip2=Перегляд записів керування доступом у реальному часі після додавання області та відповідних дверей
#查看区域内人员
acc_zonePerson_cleanCount=Очистити статистику людей, які входять і виходять
acc_zonePerson_inCount=Статистика кількості людей, які зайшли
acc_zonePerson_outCount=Статистика кількості людей, які залишають
#biocv460
acc_device_validFail=Ім'я користувача або пароль неправильне, і перевірка не вдається!
acc_device_pwdRequired=Можна ввести лише максимально 6 цілих чисел