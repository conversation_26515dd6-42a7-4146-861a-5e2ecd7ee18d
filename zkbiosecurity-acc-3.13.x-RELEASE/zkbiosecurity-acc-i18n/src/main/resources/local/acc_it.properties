#[1]左侧菜单
acc_module=Accesso
acc_leftMenu_accDev=Dispositivo di accesso
acc_leftMenu_auxOut=Uscita accessoria
acc_leftMenu_dSTime=Ora legale
acc_leftMenu_access=Controllo accessi
acc_leftMenu_door=Porta
acc_leftMenu_accRule=Regola di accesso
acc_leftMenu_interlock=Interblocco
acc_leftMenu_antiPassback=Anti-passback
acc_leftMenu_globalLinkage=Collegamento globale
acc_leftMenu_firstOpen=Normalmente aperta prima persona
acc_leftMenu_combOpen=Porta con apertura da parte di più persone
acc_leftMenu_personGroup=Gruppo di più persone
acc_leftMenu_level=Livelli di accesso
acc_leftMenu_electronicMap=Mappa
acc_leftMenu_personnelAccessLevels=Livelli di accesso del personale
acc_leftMenu_searchByLevel=Per livello di accesso
acc_leftMenu_searchByDoor=Diritti di accesso per porta
acc_leftMenu_expertGuard=Funzioni avanzate
acc_leftMenu_zone=Zona
acc_leftMenu_readerDefine=Definizione lettore
acc_leftMenu_gapbSet=Anti-passback globale
acc_leftMenu_whoIsInside=Chi è all’interno
acc_leftMenu_whatRulesInside=Regole che si applicano all’interno
acc_leftMenu_occupancy=Controllo occupazione
acc_leftMenu_route=Controllo percorso
acc_leftMenu_globalInterlock=Interblocco globale
acc_leftMeue_globalInterlockGroup=Gruppo interblocco globale
acc_leftMenu_dmr=Regola dell’uomo morto
acc_leftMenu_personLimit=Disponibilità persona
acc_leftMenu_verifyModeRule=Modalità di verifica
acc_leftMenu_verifyModeRulePersonGroup=Gruppo modalità di verifica
acc_leftMenu_extDev=I/O Board
acc_leftMenu_firstInLastOut=Primo dentro e ultimo fuori
acc_leftMenu_accReports=Rapporti di controllo di accesso
#[3]门禁时间段
acc_timeSeg_entity=Fuso orario
acc_timeSeg_canNotDel=Il periodo di tempo è in uso e non può essere eliminato.
#[4]门禁设备--公共的在common中
acc_common_ruleName=Nome regola
acc_common_hasBeanSet=Impostato
acc_common_notSet=Non impostato
acc_common_hasBeenOpened=Aperto
acc_common_notOpened=Non aperto
acc_common_partSet=Parte dell’insieme
acc_common_linkageAndApbTip=Collegamento e collegamento globale, anti-passback e anti-passback globale sono impostati contemporaneamente e possono manifestarsi dei conflitti.
acc_common_vidlinkageTip=Verificare che il collegamento del punto di ingresso corrispondente sia vincolato a un canale video disponibile, altrimenti il collegamento video non può funzionare.
acc_common_accZoneFromTo=Impossibile impostare la stessa zona
acc_common_logEventNumber=ID evento
acc_common_bindOrUnbindChannel=Vincolamento/Svincolamento della telecamera
acc_common_boundChannel=Telecamera vincolata
#设备信息
acc_dev_iconType=Tipo icona
acc_dev_carGate=Barriera per parcheggio
acc_dev_channelGate=Varco a vetri a scomparsa
acc_dev_acpType=Tipo di pannello di controllo
acc_dev_oneDoorACP=Pannello di controllo accessi per una porta
acc_dev_twoDoorACP=Pannello di controllo accessi per due porte
acc_dev_fourDoorACP=Pannello di controllo accessi per quattro porte
acc_dev_onDoorACD=Dispositivo indipendente
acc_dev_switchToTwoDoorTwoWay=Passare a doppio passaggio con due porte
acc_dev_addDevConfirm2=Suggerimento: Il dispositivo è collegato ma il tipo di pannello di controllo accessi differisce da quello effettivo; modificarlo scegliendo un pannello per {0} porta/e. Continuare ad aggiungere?
acc_dev_addDevConfirm4=Dispositivo indipendente. Continuare ad aggiungere?
acc_dev_oneMachine=Dispositivo indipendente
acc_dev_fingervein=Vena del dito
acc_dev_control=Controllo dispositivo
acc_dev_protocol=Tipo di protocollo
acc_ownedBoard=Consiglio di amministrazione
#设备操作
acc_dev_start=Avvia
acc_dev_accLevel=Autorità di accesso
acc_dev_timeZoneAndHoliday=Fuso orario, giorni festivi
acc_dev_linkage=Collegamento
acc_dev_doorOpt=Parametri porta
acc_dev_firstPerson=Apertura porta prima persona
acc_dev_multiPerson=Apertura porta da parte di più persone
acc_dev_interlock=Interblocco
acc_dev_antiPassback=Anti-passback
acc_dev_wiegandFmt=Formato Wiegand
acc_dev_outRelaySet=Impostazioni uscita accessoria
acc_dev_backgroundVerifyParam=Opzioni verifica BG
acc_dev_getPersonInfoPrompt=Assicurarsi di aver acquisito correttamente i dati del personale, onde evitare eccezioni. Continuare?
acc_dev_getEventSuccess=Acquisizione eventi riuscita.
acc_dev_getEventFail=Acquisizione evento/i non riuscita.
acc_dev_getInfoSuccess=Acquisizione informazioni riuscita.
acc_dev_getInfoXSuccess=Acquisizione {0} riuscita.
acc_dev_getInfoFail=Acquisizione informazioni non riuscita.
acc_dev_updateExtuserInfoFail=Aggiornamento dei dati del personale per l’estensione del passaggio non riuscito; recuperare le informazioni.
acc_dev_getPersonCount=Ottieni numero di utenti
acc_dev_getFPCount=Ottieni numero di impronte digitali
acc_dev_getFVCount=Ottieni numero di vene del dito
acc_dev_getFaceCount=Ottieni numero di volti
acc_dev_getPalmCount=Ottieni numero di palmi
acc_dev_getBiophotoCount=Ottieni numero di immagini del volto
acc_dev_noData=Nessun dato restituito dal dispositivo.
acc_dev_noNewData=Nessuna nuova transazione nel dispositivo.
acc_dev_softLtDev=Più persone nel software che nel dispositivo.
acc_dev_personCount=Numero di persone:
acc_dev_personDetail=In dettaglio:
acc_dev_softEqualDev=Stesso numero di persone nel software e nel dispositivo.
acc_dev_softGtDev=Più persone nel dispositivo che nel software.
acc_dev_cmdSendFail=Impossibile inviare i comandi; inviare di nuovo.
acc_dev_issueVerifyParam=Imposta opzioni verifica BG
acc_dev_verifyParamSuccess=Opzioni verifica BG applicate correttamente
acc_dev_backgroundVerify=Verifica in background
acc_dev_selRightFile=Selezionare il file di aggiornamento corretto.
acc_dev_devNotOpForOffLine=Il dispositivo è offline; riprovare in seguito
acc_dev_devNotSupportFunction=Il dispositivo non supporta questa funzione
acc_dev_devNotOpForDisable=Il dispositivo è disabilitato; riprovare in seguito
acc_dev_devNotOpForNotOnline=Il dispositivo è offline o disabilitato; riprovare in seguito
acc_dev_getPersonInfo=Ottieni dati personale
acc_dev_getFPInfo=Ottieni dati impronta digitale
acc_dev_getFingerVeinInfo=Ottieni dati vena del dito
acc_dev_getPalmInfo=Ottieni dati palmo
acc_dev_getBiophotoInfo=Ottieni dati immagine del volto
acc_dev_getIrisInfo=Ottenere informazioni sull'iride
acc_dev_disable=disabilitato; riselezionare
acc_dev_offlineAndContinue=offline; continuare?
acc_dev_offlineAndSelect=offline.
acc_dev_opAllDev=Tutti i dispositivi
acc_dev_opOnlineDev=Dispositivo online
acc_dev_opException=gestisci eccezioni
acc_dev_exceptionAndConfirm=Timeout di connessione del dispositivo; operazione non riuscita. Verificare la connessione di rete
acc_dev_getFaceInfo=Ottieni dati volto
acc_dev_selOpDevType=Selezionare il tipo di dispositivo da utilizzare:
acc_dev_hasFilterByFunc=Mostra solo i dispositivi online che supportano questa funzione.
acc_dev_masterSlaveMode=Modalità RS485 master e slave
acc_dev_master=Host
acc_dev_slave=Slave
acc_dev_modifyRS485Addr=Modifica indirizzo RS485
acc_dev_rs485AddrTip=Immettere numero intero compreso tra 1 e 63.
acc_dev_enableFeature=Dispositivi con verifica in background abilitata
acc_dev_disableFeature=Dispositivi con verifica in background disabilitata
acc_dev_getCountOnly=Ottieni solo numero
acc_dev_queryDevPersonCount=Richiedi quantità personale
acc_dev_queryDevVolume=Visualizza capacità dispositivo
acc_dev_ruleType=Tipo di regola
acc_dev_contenRule=Dettagli regole
acc_dev_accessRules=Visualizza regole dei dispositivi
acc_dev_ruleContentTip=Se si indicano più regole, separarle con “|”.
acc_dev_rs485AddrFigure=Figura codice indirizzo RS485
acc_dev_addLevel=Aggiungi a livello
acc_dev_personOrFingerTanto=Numero di membri del personale o impronte digitali oltre i limiti; sincronizzazione non riuscita...
acc_dev_personAndFingerUnit=(numero)
acc_dev_setDstime=Imposta ora legale
acc_dev_setTimeZone=Imposta fuso orario dispositivo
acc_dev_selectedTZ=Fuso orario selezionato
acc_dev_timeZoneSetting=Impostazione fuso orario in corso...
acc_dev_timeZoneCmdSuccess=Comando fuso orario inviato...
acc_dev_enableDstime=Abilita ora legale
acc_dev_disableDstime=Disabilita ora legale
acc_dev_timeZone=Fuso orario
acc_dev_dstSettingTip=Impostazione ora legale in corso...
acc_dev_dstDelTip=Eliminazione ora legale dispositivo...
acc_dev_enablingDst=Abilitazione ora legale
acc_dev_dstEnableCmdSuccess=Comando abilitazione ora legale inviato.
acc_dev_disablingDst=Disabilitazione ora legale.
acc_dev_dstDisableCmdSuccess=Comando disabilitazione ora legale inviato.
acc_dev_dstCmdSuccess=Comando ora legale inviato...
acc_dev_usadst=Ora legale
acc_dev_notSetDst=Nessuna impostazione
acc_dev_selectedDst=Ora legale selezionata
acc_dev_configMasterSlave=Configurazione master-slave
acc_dev_hasFilterByUnOnline=Mostra solo il dispositivo online.
acc_dev_softwareData=Se i dati non corrispondono al dispositivo, sincronizzare i dati del dispositivo prima di riprovare.
acc_dev_disabled=Impossibile azionare i dispositivi disattivati.
acc_dev_offline=Impossibile azionare i dispositivi offline.
acc_dev_noSupport=I dispositivi non supportano questa funzione e non possono essere azionati.
acc_dev_noRegDevTip=Questo dispositivo non è definito come dispositivo di registrazione. I dati presenti nel software non saranno aggiornati. Continuare?
acc_dev_noOption=Nessuna opzione qualificata
acc_dev_devFWUpdatePrompt=Di norma il dispositivo corrente non utilizza le persone disabilitate e la funzione di validità persona (consultare il manuale utente).
acc_dev_panelFWUpdatePrompt=Di norma il dispositivo corrente non utilizza le persone disabilitate e la funzione di validità persona; aggiornare immediatamente il firmware?
acc_dev_sendEventCmdSuccess=Comando di eliminazione evento inviato
acc_dev_tryAgain=Riprovare.
acc_dev_eventAutoCheckAndUpload=Verifica automatica e caricamento eventi
acc_dev_eventUploadStart=Avviare il caricamento degli eventi.
acc_dev_eventUploadEnd=Caricamento eventi completato.
acc_dev_eventUploadFailed=Caricamento eventi non riuscito.
acc_dev_eventUploadPrompt=La versione del firmware è troppo vecchia; prima di aggiornare il firmware, è opportuno:
acc_dev_backupToSoftware=Eseguire il backup dei dati nel software
acc_dev_deleteEvent=Eliminare il vecchio record
acc_dev_upgradePrompt=La versione del firmware è troppo vecchia e potrebbe causare un errore; eseguire il backup dei dati nel software prima dell’aggiornamento del firmware.
acc_dev_conflictCardNo=Carta già assegnata all’utente {0} nel sistema.
acc_dev_rebootAfterOperate=Operazione riuscita; il dispositivo verrà riavviato.
acc_dev_baseOptionTip=Ottenuti parametri di base anomali.
acc_dev_funOptionTip=Ottenuto parametro funzione anomalo.
acc_dev_sendComandoTip=Comando di acquisizione del parametro del dispositivo non inviato.
acc_dev_noC3LicenseTip=Impossibile aggiungere questo tipo di dispositivo ({0}). Contattare il reparto vendite.
acc_dev_combOpenDoorTip=({0})È stata impostata l’apertura della porta da parte di più persone; impossibile utilizzarla contemporaneamente alla verifica in background.
acc_dev_combOpenDoorPersonCountTip=Il gruppo {0} non può aprire più di {1}!
acc_dev_addDevTip=Ciò riguarda soltanto l’aggiunta di dispositivi con protocollo di comunicazione PULL.
acc_dev_addError=Eccezione aggiunta dispositivo; parametri mancanti ({0}).
acc_dev_updateIPAndPortError=Errore aggiornamento porta e IP server...
acc_dev_transferFilesTip=Test del firmware completato; trasferimento dei file in corso
acc_dev_serialPortExist=Porta seriale esistente
acc_dev_isExist=Dispositivo esistente
acc_dev_description=Descrizione
acc_dev_searchEthernet=Ricerca dispositivo Ethernet
acc_dev_searchRS485=Ricerca dispositivo RS485
acc_dev_rs485AddrTip1=L’indirizzo iniziale dell’RS485 deve essere minore dell’indirizzo finale
acc_dev_rs485AddrTip2=L’ambito della ricerca dell’RS485 deve essere inferiore a 20
acc_dev_clearAllCmdCache=Cancella tutti i comandi
acc_dev_authorizedSuccessful=Autorizzazione fornita
acc_dev_authorize=Autorizza
acc_dev_registrationDevice=Dispositivo di registrazione
acc_dev_setRegistrationDevice=Imposta come dispositivo di registrazione
acc_dev_mismatchedDevice=Dispositivo non utilizzabile nel mercato dell’utente. Verificare il numero di serie del dispositivo con il reparto vendite.
acc_dev_pwdStartWithZero=La password di comunicazione non può iniziare con uno zero.
acc_dev_maybeDisabled=La licenza corrente consente di aggiungere soltanto altre {0) porte; in caso di superamento del limite della licenza, la porta eccedente sarà disabilitata; continuare?
acc_dev_Limit=Limite di aggiunta del codice licenza raggiunto; autorizzare.
acc_dev_selectDev=Selezionare il dispositivo.
acc_dev_cannotAddPullDevice=Impossibile aggiungere il dispositivo pull. Contattare il reparto vendite.
acc_dev_notContinueAddPullDevice={0} dispositivi pull nel sistema; impossibile continuare ad aggiungere. Contattare il reparto vendite.
acc_dev_deviceNameNull=Modello del dispositivo non indicato; impossibile aggiungere il dispositivo.
acc_dev_commTypeErr=Mancata corrispondenza del tipo di comunicazione; impossibile aggiungere il dispositivo.
acc_dev_inputDomainError=Inserire un indirizzo di dominio valido.
acc_dev_levelTip=Il livello comprende più di 5000 persone; impossibile aggiungere il dispositivo a questo livello.
acc_dev_auxinSet=Impostazione ingresso accessorio
acc_dev_verifyModeRule=Regola modalità di verifica
acc_dev_netModeWired=Cablata
acc_dev_netMode4G=4G
acc_dev_netModeWifi=Wi-Fi
acc_dev_updateNetConnectMode=Cambia connessione di rete
acc_dev_wirelessSSID=SSID wireless
acc_dev_wirelessKey=Chiave di rete wireless
acc_dev_searchWifi=Ricerca Wi-Fi
acc_dev_testNetConnectSuccess=Connessione riuscita?
acc_dev_testNetConnectFailed=La connessione non riesce a comunicare correttamente.
acc_dev_signalIntensity=Intensità del segnale
acc_dev_resetSearch=Cerca ancora
acc_dev_addChildDevice=Aggiungi dispositivo secondario
acc_dev_modParentDevice=Cambia dispositivo principale
acc_dev_configParentDevice=Impostazione dispositivo principale
acc_dev_lookUpChildDevice=Visualizza dispositivi secondari
acc_dev_addChildDeviceTip=L’autorizzazione deve essere fornita da un dispositivo secondario autorizzato
acc_dev_maxSubCount=Il numero di dispositivi secondari autorizzati supera il limite massimo di %d unità.
acc_dev_seletParentDevice=Selezionare il dispositivo principale.
acc_dev_networkCard=Scheda di rete
acc_dev_issueParam=Parametri di sincronizzazione personalizzati
acc_dev_issueMode=Modalità di sincronizzazione
acc_dev_initIssue=Dati inizializzazione firmware versione 3030
acc_dev_customIssue=Dati sincronizzazione personalizzati
acc_dev_issueData=Dati
acc_dev_parent=Dispositivo principale
acc_dev_parentEnable=Dispositivo principale disattivato
acc_dev_parentTips=Il vincolamento del dispositivo principale eliminerà tutti i dati nel dispositivo, che dovrà essere reimpostato.
acc_dev_addDevIpTip=Il nuovo indirizzo IP non può essere uguale all’indirizzo IP del server
acc_dev_modifyDevIpTip=Il nuovo indirizzo del server non può essere uguale all’indirizzo IP del dispositivo
acc_dev_setWGReader=Imposta lettore Wiegand
acc_dev_selectReader=Fare clic per selezionare il lettore
acc_dev_IllegalDevice=Dispositivo non valido
acc_dev_syncTimeWarnTip=Gli orari di sincronizzazione dei seguenti dispositivi devono essere sincronizzati sul dispositivo principale.
acc_dev_setTimeZoneWarnTip=Il fuso orario del seguente dispositivo deve essere sincronizzato sul dispositivo principale.
acc_dev_setDstimeWarnTip=L’ora legale dei seguenti dispositivi deve essere sincronizzata sul dispositivo principale.
acc_dev_networkSegmentSame=Due adattatori di rete non possono usare lo stesso segmento della rete.
acc_dev_upgradeProtocolNoMatch=Mancata corrispondenza del protocollo del file di aggiornamento
acc_dev_ipAddressConflict=Esiste già un dispositivo con lo stesso indirizzo IP. Modificare l’indirizzo IP del dispositivo e aggiungerlo nuovamente.
acc_dev_checkServerPortTip=Mancata corrispondenza fra porta del server configurata e porta di comunicazione del sistema, che può impedire l’aggiunta. Continuare?
acc_dev_clearAdmin=Elimina permesso amministratore
acc_dev_setDevSate=Imposta stato ingresso/uscita dispositivo
acc_dev_sureToClear=Eliminare i permessi amministratore?
acc_dev_hostState=Stato dispositivo principale
acc_dev_regDeviceTypeTip=Dispositivo con restrizioni che non consente l’aggiunta diretta. Rivolgersi al fornitore del software.
acc_dev_extBoardType=I/O Board Type
acc_dev_extBoardTip=After the configuration, you need to restart the device to take effect.
acc_dev_extBoardLimit=Only {0} I/O board of this type can be added to each device!
acc_dev_replace=Sostituisci dispositivo
acc_dev_replaceTip=Dopo la sostituzione, il vecchio dispositivo non funzionerà, fai attenzione!
acc_dev_replaceTip1=Dopo la sostituzione, eseguire l'operazione "sincronizza tutti i dati";
acc_dev_replaceTip2=Assicurati che il modello del dispositivo sostitutivo sia lo stesso!
acc_dev_replaceTip3=Assicurati che il dispositivo sostitutivo abbia impostato lo stesso indirizzo server e la stessa porta del vecchio dispositivo!
acc_dev_replaceFail=Il tipo di macchina del dispositivo non è coerente e non può essere sostituito!
acc_dev_notApb=Questo dispositivo non è in grado di eseguire operazioni anti-sottomarine con cancello o lettura della testa
acc_dev_upResourceFile=Carica file delle risorse
acc_dev_playOrder=Ordine di riproduzione
acc_dev_setFaceServerInfo=Imposta parametri di confronto del backend facciale
acc_dev_faceVerifyMode=Modalità di confronto viso
acc_dev_faceVerifyMode1=Confronto locale
acc_dev_faceVerifyMode2=Confronto backend
acc_dev_faceVerifyMode3=Priorità di confronto locale
acc_dev_faceBgServerType=Tipo di server di backend facciale
acc_dev_faceBgServerType1=Servizi di piattaforma software
acc_dev_faceBgServerType2=Servizi di terzi
acc_dev_isAccessLogic=Abilita verifica logica del controllo di accesso
#[5]门-其他关联的也复用此处
acc_door_entity=Porta
acc_door_number=Numero porta
acc_door_name=Nome porta
acc_door_activeTimeZone=Fuso orario attivo
acc_door_passageModeTimeZone=Fuso orario modalità passaggio
acc_door_setPassageModeTimeZone=Fuso orario modalità passaggio impostato
acc_door_notPassageModeTimeZone=Fuso orario modalità non passaggio
acc_door_lockOpenDuration=Durata apertura serratura
acc_door_entranceApbDuration=Durata anti-passback ingresso
acc_door_sensor=Sensore porta
acc_door_sensorType=Tipo sensore porta
acc_door_normalOpen=Normalmente aperta
acc_door_normalClose=Normalmente chiusa
acc_door_sensorDelay=Ritardo sensore porta
acc_door_closeAndReverseState=Inversione stato serratura alla chiusura della porta
acc_door_hostOutState=Stato accesso host
acc_door_slaveOutState=Stato uscita slave
acc_door_inState=Entrata
acc_door_outState=Uscita
acc_door_requestToExit=Modalità REX
acc_door_withoutUnlock=Blocca
acc_door_unlocking=Sblocca
acc_door_alarmDelay=Ritardo REX
acc_door_duressPassword=Password Duress
acc_door_currentDoor=Porta corrente
acc_door_allDoorOfCurDev=Tutte le porte nel dispositivo corrente
acc_door_allDoorOfAllDev=Tutte le porte in tutti i dispositivi
acc_door_allDoorOfAllControlDev=Tutte le porte in tutti i dispositivi di controllo
acc_door_allDoorOfAllStandaloneDev=Tutte le porte di tutti i dispositivi indipendenti
acc_door_allWirelessLock=Tutti i blocchi wireless
acc_door_max6BitInteger=Intero max. 6 bit
acc_door_direction=Direzione
acc_door_onlyInReader=Solo lettore ingresso
acc_door_bothInAndOutReader=Lettori ingresso e uscita
acc_door_noDoor=Aggiungere la porta.
acc_door_nameRepeat=Nomi porte già presenti.
acc_door_duressPwdError=La password Duress non deve coincidere con alcuna password personale.
acc_door_urgencyStatePwd=Immettere un numero intero a {0} bit.
acc_door_noDevOnline=Nessun dispositivo online oppure la porta non supporta la modalità di verifica con carta.
acc_door_durationLessLock=Il ritardo del sensore della porta deve essere maggiore della durata di apertura della serratura.
acc_door_lockMoreDuration=La durata di apertura della serratura deve essere minore del ritardo del sensore della porta.
acc_door_lockAndExtLessDuration=La somma della durata di apertura della serratura e del ritardo di passaggio deve essere minore del ritardo del sensore della porta.
acc_door_noDevTrigger=Il dispositivo non soddisfa le condizioni.
acc_door_relay=Relè
acc_door_pin=Pin
acc_door_selDoor=Seleziona porta
acc_door_sensorStatus=Sensore porta ({0})
acc_door_sensorDelaySeconds=Ritardo sensore porta ({0} s)
acc_door_timeSeg=Fuso orario ({0})
acc_door_combOpenInterval=Intervallo azionamento da parte di più persone
acc_door_delayOpenTime=Ritardo apertura porta
acc_door_extDelayDrivertime=Ritardo passaggio
acc_door_enableAudio=Abilita allarme
acc_door_disableAudio=Disabilita allarme
acc_door_lockAndExtDelayTip=La somma della durata di apertura della serratura e dell’intervallo del tempo di ritardo di passaggio non supera i 254 secondi.
acc_door_disabled=Impossibile azionare le porte disabilitate.
acc_door_offline=Impossibile azionare le porte offline.
acc_door_notSupport=Le porte seguenti non supportano questa funzione.
acc_door_select=Selezionare la porta
acc_door_pushMaxCount=Il sistema comprende {0} porte abilitate e ha raggiunto il limite della licenza. Contattare il reparto vendite
acc_door_outNumber=La licenza corrente consente di aggiungere soltanto altre {0} porte. Riselezionare le porte e riprovare oppure richiedere una licenza di aggiornamento al reparto vendite.
acc_door_latchTimeZone=Fuso orario REX
acc_door_wgFmtReverse=Storno numero carta
acc_door_allowSUAccessLock=Allow Superuser Access When Lockdown
acc_door_verifyModeSinglePwd=Le password non possono essere utilizzate come metodo di verifica indipendente!
acc_door_doorPassword=Password di apertura della porta
#辅助输入
acc_auxIn_timeZone=Fuso orario attivo
#辅助输出
acc_auxOut_passageModeTimeZone=Fuso orario modalità passaggio
acc_auxOut_disabled=Impossibile azionare l’uscita accessoria disabilitata.
acc_auxOut_offline=Impossibile azionare l’uscita accessoria offline.
#[8]门禁权限组
acc_level_doorGroup=Combinazione porte
acc_level_openingPersonnel=Personale apertura
acc_level_noDoor=Nessun elemento disponibile. Aggiungere prima il dispositivo.
acc_level_doorRequired=Selezionare la porta.
acc_level_doorCount=Numero porte
acc_level_doorDelete=Elimina porta
acc_level_isAddDoor=Aggiungere immediatamente le porte al livello corrente di controllo accessi?
acc_level_master=Principale
acc_level_noneSelect=Aggiungere un livello di controllo accessi.
acc_level_useDefaultLevel=Passare al livello di accesso del nuovo reparto?
acc_level_persAccSet=Impostazioni controllo accessi personale
acc_level_visUsed={0} è utilizzato dal modulo visitatori e non può essere eliminato.
acc_level_doorControl=Door Control
acc_level_personExceedMax=Il numero dei gruppi di autorizzazione attuali ({0}) e il numero massimo di gruppi di permessi opzionali ({1})
acc_level_exportLevel=Esporta livello di accesso
acc_level_exportLevelDoor=Esporta le porte del livello di accesso
acc_level_exportLevelPerson=Esporta personale di livello di accesso
acc_level_importLevel=Importa livello di accesso
acc_level_importLevelDoor=Importa porte di accesso al livello
acc_level_importLevelPerson=Importa il personale di livello di accesso
acc_level_exportDoorFileName=Informazioni porte del livello di accesso
acc_level_exportPersonFileName=Informazioni sul personale del livello di accesso
acc_levelImport_nameNotNull=Il nome del livello di accesso non può essere vuoto
acc_levelImport_timeSegNameNotNull=Il fuso orario non può essere vuoto
acc_levelImport_areaNotExist=L'area non esiste!
acc_levelImport_timeSegNotExist=Il fuso orario non esiste!
acc_levelImport_nameExist=Il nome del livello di accesso {0} esiste già!
acc_levelImport_levelDoorExist=Le porte di accesso al livello {0} esistono già!
acc_levelImport_levelPersonExist=Il personale di livello di accesso {0} esiste già!
acc_levelImport_noSpecialChar=Il nome del livello di accesso non può contenere caratteri speciali!
#[10]首人常开
acc_firstOpen_setting=Apertura normale prima persona
acc_firstOpen_browsePerson=Sfoglia personale
#[11]多人组合开门
acc_combOpen_comboName=Nome combinazione
acc_combOpen_personGroupName=Nome gruppo
acc_combOpen_personGroup=Gruppo di più persone
acc_combOpen_verifyOneTime=Numero personale corrente
acc_combOpen_eachGroupCount=Numero personale apertura in ciascun gruppo
acc_combOpen_group=Gruppo
acc_combOpen_changeLevel=Gruppo apertura porta
acc_combOpen_combDeleteGroup=Riferimento combinazione apertura esistente; eliminare prima.
acc_combOpen_ownedLevel=Appartenenza gruppi
acc_combOpen_mostPersonCount=Il gruppo non può contenere più di cinque persone.
acc_combOpen_leastPersonCount=Il gruppo non può contenere meno di due persone.
acc_combOpen_groupNameRepeat=Nome gruppo già presente.
acc_combOpen_groupNotUnique=I gruppi di persone di apertura della porta non possono essere identici.
acc_combOpen_persNumErr=Il numero del gruppo supera il valore selezionato dall’utente; riselezionare.
acc_combOpen_combOpengGroupPersonShort=Dopo la rimozione delle persone, il numero del gruppo apertura porta personale è insufficiente; eliminare prima il gruppo apertura.
acc_combOpen_backgroundVerifyTip=La porta del dispositivo ha la verifica in background abilitata e quest’ultima non può essere utilizzata contemporaneamente all’apertura della porta da parte di più persone.
#[12]互锁
acc_interlock_rule=Regola interblocco
acc_interlock_mode1Or2=Interblocco fra {0} e {1}
acc_interlock_mode3=Interblocco fra {0} e {1} e {2}
acc_interlock_mode4=Interblocco fra {0} e {1} o fra {2} e {3}
acc_interlock_mode5=Interblocco fra {0} e {1} e {2} e {3}
acc_interlock_hasBeenSet=Presenti impostazioni programma interblocco
acc_interlock_group1=Gruppo 1
acc_interlock_group2=Gruppo 2
acc_interlock_ruleInfo=Interblocco tra gruppi
acc_interlock_alreadyExists=La stessa regola di interblocco esiste già, per favore non aggiungerla di nuovo!
acc_interlock_groupInterlockCountErr=La regola di interblocco intragruppo richiede almeno due porte
acc_interlock_ruleType=Tipo di regola interblocco
#[13]反潜
acc_apb_rules=Regola anti-passback
acc_apb_reader=Anti-passback fra i lettori della porta {0}
acc_apb_reader2=Anti-passback fra i lettori delle porte: {0}, {1}
acc_apb_reader3=Anti-passback fra i lettori delle porte: {0}, {1}, {2}
acc_apb_reader4=Anti-passback fra i lettori delle porte: {0}, {1}, {2}, {3}
acc_apb_reader5=Anti-passback lettore uscita su porta {0}
acc_apb_reader6=Anti-passback lettore ingresso su porta {0}
acc_apb_reader7=Anti-passback fra i lettori di tutte e 4 le porte
acc_apb_twoDoor=Anti-passback fra {0} e {1}
acc_apb_fourDoor=Anti-passback fra {0} e {1} , anti-passback fra {2} e {3}
acc_apb_fourDoor2=Anti-passback fra {0} o {1} e {2} o {3}
acc_apb_fourDoor3=Anti-passback fra {0} e {1} o {2}
acc_apb_fourDoor4=Anti-passback fra {0} e {1} o {2} o {3}
acc_apb_hasBeenSet=Impostato per anti-passback
acc_apb_conflictWithGapb=Dispositivo con impostazioni anti-passback globali che non può essere modificato.
acc_apb_conflictWithApb=Zona del dispositivo con impostazioni anti-passback che non può essere modificata.
acc_apb_conflictWithEntranceApb=Zona del dispositivo con impostazioni anti-passback di entrata che non può essere modificata.
acc_apb_controlIn=Anti-passback ingresso
acc_apb_controlOut=Anti-passback uscita
acc_apb_controlInOut=Anti-passback ingresso/uscita
acc_apb_groupIn=Unisciti Al Gruppo
acc_apb_groupOut=Fuori Gruppo
acc_apb_reverseName=Antisottomarino inverso
acc_apb_door=Cancello Anti Sottomarino
acc_apb_readerHead=Leggere La Testa Per Contrastare Il Sottomarino
acc_apb_alreadyExists=La stessa regola anti sottomarino esiste già, per favore non aggiungerla di nuovo!
#[17]电子地图
acc_map_addDoor=Aggiungi porta
acc_map_addChannel=Aggiungi telecamera
acc_map_noAccess=Nessuna autorizzazione di accesso al modulo della mappa elettronica; rivolgersi all’amministratore.
acc_map_noAreaAccess=Non si dispone dell’autorizzazione per la mappa elettronica relativa a quest’area; rivolgersi all’amministratore.
acc_map_imgSizeError=Carica l'immagine la cui dimensione è inferiore a {0} M!
#[18]门禁事件记录
acc_trans_entity=Transazione
acc_trans_eventType=Tipo di evento
acc_trans_firmwareEvent=Eventi firmware
acc_trans_softwareEvent=Evento software
acc_trans_today=Eventi da oggi
acc_trans_lastAddr=Ultima posizione nota
acc_trans_viewPhotos=Visualizza foto
acc_trans_exportPhoto=Esporta foto
acc_trans_dayNumber=Giorni
acc_trans_photo=Foto incidente di controllo accessi
acc_trans_fileIsTooLarge=Il file esportato è troppo grande, riduci l'ambito dell'esportazione
#[19]门禁验证方式
acc_verify_mode_onlyface=Volto
acc_verify_mode_facefp=Volto+impronta digitale
acc_verify_mode_facepwd=Volto+password
acc_verify_mode_facecard=Volto+carta
acc_verify_mode_facefpcard=Volto+impronta digitale+carta
acc_verify_mode_facefppwd=Volto+impronta digitale+password
acc_verify_mode_fv=Vena del dito
acc_verify_mode_fvpwd=Vena del dito+password
acc_verify_mode_fvcard=Vena del dito+carta
acc_verify_mode_fvpwdcard=Vena del dito+password+carta
acc_verify_mode_pv=Palma
acc_verify_mode_pvcard=Palma+carta
acc_verify_mode_pvface=Palma+volto
acc_verify_mode_pvfp=Palma+impronta digitale
acc_verify_mode_pvfacefp=Palma+volto+impronta digitale
#[20]门禁事件编号
acc_eventNo_-1=Nessuno
acc_eventNo_0=Apertura normale con lettura
acc_eventNo_1=Lettura durante fuso orario modalità passaggio
acc_eventNo_2=Apertura normale con prima carta (lettura carta)
acc_eventNo_3=Apertura da parte di più persone (lettura carta)
acc_eventNo_4=Apertura con password d’emergenza
acc_eventNo_5=Apertura durante fuso orario modalità passaggio
acc_eventNo_6=Evento collegamento azionato
acc_eventNo_7=Annulla allarme
acc_eventNo_8=Apertura a distanza
acc_eventNo_9=Chiusura a distanza
acc_eventNo_10=Disabilita fuso orario modalità passaggio giornaliero
acc_eventNo_11=Abilita fuso orario modalità passaggio giornaliero
acc_eventNo_12=Apertura a distanza uscita accessoria
acc_eventNo_13=Chiusura a distanza uscita accessoria
acc_eventNo_14=Apertura normale con impronta digitale
acc_eventNo_15=Apertura da parte di più persone (impronta digitale)
acc_eventNo_16=Pressione impronta digitale durante fuso orario modalità passaggio
acc_eventNo_17=Apertura con carta + impronta digitale
acc_eventNo_18=Apertura normale con prima carta (pressione impronta digitale)
acc_eventNo_19=Apertura normale con prima carta (carta + impronta digitale)
acc_eventNo_20=Intervallo azionamento troppo breve
acc_eventNo_21=Fuso orario porta inattiva (lettura carta)
acc_eventNo_22=Fuso orario non valido
acc_eventNo_23=Accesso negato
acc_eventNo_24=Anti-passback
acc_eventNo_25=Interblocco
acc_eventNo_26=Autenticazione da parte di più persone (lettura carta)
acc_eventNo_27=Carta disabilitata
acc_eventNo_28=Timeout apertura porta esteso
acc_eventNo_29=Carta scaduta
acc_eventNo_30=Errore password
acc_eventNo_31=Intervallo pressione impronta digitale troppo breve
acc_eventNo_32=Autenticazione da parte di più persone (pressione impronta digitale)
acc_eventNo_33=Impronta digitale scaduta
acc_eventNo_34=Impronta digitale disabilitata
acc_eventNo_35=Fuso orario porta inattiva (pressione impronta digitale)
acc_eventNo_36=Fuso orario porta inattiva (pressione pulsante di uscita)
acc_eventNo_37=Chiusura non riuscita durante fuso orario modalità passaggio
acc_eventNo_38=Carta segnalata come smarrita
acc_eventNo_39=Accesso disabilitato
acc_eventNo_40=Autenticazione da parte di più persone non riuscita (pressione impronta digitale)
acc_eventNo_41=Errore modalità di verifica
acc_eventNo_42=Errore formato Wiegand
acc_eventNo_43=Timeout verifica anti-passback
acc_eventNo_44=Verifica in background non riuscita
acc_eventNo_45=Timeout verifica in background
acc_eventNo_47=Impossibile inviare il comando
acc_eventNo_48=Autenticazione da parte di più persone non riuscita (lettura carta)
acc_eventNo_49=Fuso orario porta inattiva (password)
acc_eventNo_50=Intervallo pressione password troppo breve
acc_eventNo_51=Autenticazione da parte di più persone (password)
acc_eventNo_52=Autenticazione da parte di più persone non riuscita (password)
acc_eventNo_53=Password scaduta
acc_eventNo_100=Allarme antimanomissione
acc_eventNo_101=Apertura con password Duress
acc_eventNo_102=Apertura forzata porta
acc_eventNo_103=Apertura con impronta digitale Duress
acc_eventNo_200=Apertura corretta porta
acc_eventNo_201=Chiusura corretta porta
acc_eventNo_202=Apertura con pulsante di uscita
acc_eventNo_203=Apertura da parte di più persone (carta + impronta digitale)
acc_eventNo_204=Fine fuso orario modalità passaggio
acc_eventNo_205=Apertura normale a distanza
acc_eventNo_206=Dispositivo avviato
acc_eventNo_207=Apertura con password
acc_eventNo_208=Apertura porte super utente
acc_eventNo_209=Pulsante di uscita azionato (senza sblocco)
acc_eventNo_210=Avvia porta di emergenza
acc_eventNo_211=Chiusura porte super utente
acc_eventNo_212=Abilita funzione controllo ascensori
acc_eventNo_213=Disabilita funzione controllo ascensori
acc_eventNo_214=Apertura da parte di più persone (password)
acc_eventNo_215=Apertura normale con prima carta (password)
acc_eventNo_216=Password durante fuso orario modalità passaggio
acc_eventNo_220=Ingresso accessorio scollegato (aperto)
acc_eventNo_221=Ingresso accessorio in cortocircuito (chiuso)
acc_eventNo_222=Verifica in background riuscita
acc_eventNo_223=Verifica in background
acc_eventNo_225=Ingresso accessorio normale
acc_eventNo_226=Azionamento ingresso accessorio
acc_newEventNo_0=Apertura con verifica normale
acc_newEventNo_1=Verifica durante fuso orario modalità passaggio
acc_newEventNo_2=Apertura primo membro personale
acc_newEventNo_3=Apertura più membri del personale
acc_newEventNo_20=Intervallo operazione troppo breve
acc_newEventNo_21=Apertura con verifica fuso orario porta inattiva
acc_newEventNo_26=Attesa autenticazione da parte di più membri del personale
acc_newEventNo_27=Personale non registrato
acc_newEventNo_29=Personale scaduto
acc_newEventNo_30=Errore password
acc_newEventNo_41=Errore modalità di verifica
acc_newEventNo_43=Blocco personale
acc_newEventNo_44=Verifica in background non riuscita
acc_newEventNo_45=Timeout verifica in background
acc_newEventNo_48=Verifica da parte di più membri del personale non riuscita
acc_newEventNo_54=Tensione batteria troppo bassa
acc_newEventNo_55=Sostituire immediatamente la batteria
acc_newEventNo_56=Operazione non valida
acc_newEventNo_57=Alimentazione di riserva
acc_newEventNo_58=Allarme normalmente aperto
acc_newEventNo_59=Gestione non valida
acc_newEventNo_60=Porta chiusa dall’interno
acc_newEventNo_61=Ripetuto
acc_newEventNo_62=Proibisci utenti
acc_newEventNo_63=Porta chiusa
acc_newEventNo_64=Fuso orario pulsante di uscita inattivo
acc_newEventNo_65=Fuso orario ingresso accessorio inattivo
acc_newEventNo_66=Aggiornamento lettore non riuscito
acc_newEventNo_67=Confronto remoto riuscito (dispositivo non autorizzato)
acc_newEventNo_68=Temperatura corporea elevata - Accesso negato
acc_newEventNo_69=Senza maschera - Accesso negato
acc_newEventNo_70=Eccezione di comunicazione del server di confronto volti
acc_newEventNo_71=Il server facciale risponde in modo irregolare
acc_newEventNo_73=Codice QR non valido
acc_newEventNo_74=Codice QR scaduto
acc_newEventNo_101=Allarme apertura Duress
acc_newEventNo_104=Allarme lettura carta non valida
acc_newEventNo_105=Impossibile connettersi al server
acc_newEventNo_106=Alimentazione di rete assente
acc_newEventNo_107=Alimentazione batteria assente
acc_newEventNo_108=Impossibile connettersi al dispositivo principale
acc_newEventNo_109=Reader Tamper Alarm
acc_newEventNo_110=Reader Offline
acc_newEventNo_112=Scheda di espansione offline
acc_newEventNo_114=Ingresso allarme antincendio disconnesso (rilevamento linea)
acc_newEventNo_115=Corretto di ingresso dell'allarme antincendio (rilevamento della linea)
acc_newEventNo_116=Ingresso ausiliario disconnesso (rilevamento linea)
acc_newEventNo_117=Corretto di ingresso ausiliario (rilevamento della linea)
acc_newEventNo_118=Interruttore di uscita disconnesso (rilevamento linea)
acc_newEventNo_119=cortocircuito dell'interruttore di uscita (rilevamento del circuito)
acc_newEventNo_120=Disconnessione magnetica della porta (rilevamento della linea)
acc_newEventNo_121=Cortocircuito magnetico della porta (rilevamento della linea)
acc_newEventNo_159=Telecomando per l’apertura della porta
acc_newEventNo_214=Connessione al server avvenuta
acc_newEventNo_217=Connessione al dispositivo principale avvenuta
acc_newEventNo_218=Verifica carta identificativa
acc_newEventNo_222=Verifica in background riuscita
acc_newEventNo_223=Verifica in background
acc_newEventNo_224=Suona il campanello
acc_newEventNo_227=Doppia apertura della porta
acc_newEventNo_228=Doppia chiusura della porta
acc_newEventNo_229=Uscita accessoria normalmente aperta a tempo
acc_newEventNo_230=Chiusura uscita accessoria a tempo
acc_newEventNo_232=Verifica riuscita
acc_newEventNo_233=Attiva blocco
acc_newEventNo_234=Disattiva blocco
acc_newEventNo_235=Aggiornamento lettore riuscito
acc_newEventNo_236=Reader Tamper Alarm Cleared
acc_newEventNo_237=Reader Online
acc_newEventNo_239=Chiamata dispositivo
acc_newEventNo_240=Chiamata terminata
acc_newEventNo_243=Ingresso allarme antincendio scollegato
acc_newEventNo_244=Cortcircuito di ingresso dell'allarme antincendio
acc_newEventNo_247=Scheda di espansione online
acc_newEventNo_4008=Recupero di rete
acc_newEventNo_4014=Segnale di ingresso antincendio scollegato, porta terminale normalmente aperta
acc_newEventNo_4015=La porta è online
acc_newEventNo_4018=Confronto del backend Apri
acc_newEventNo_5023=Stato di protezione antincendio limitato
acc_newEventNo_5024=Timeout di verifica multi-personale
acc_newEventNo_5029=Confronto del backend non riuscito
acc_newEventNo_6005=La capacità di registrazione sta per raggiungere il limite
acc_newEventNo_6006=Cortcircuito di linea (RS485)
acc_newEventNo_6007=Cortcircuito nel circuito (Wigan)
acc_newEventNo_6011=La porta è offline
acc_newEventNo_6012=Allarme di smontaggio porta
acc_newEventNo_6013=Segnale di ingresso incendio attivato, porta aperta
acc_newEventNo_6015=Ripristinare l'alimentazione del dispositivo di espansione
acc_newEventNo_6016=Ripristina le impostazioni di fabbrica della macchina
acc_newEventNo_6070=Confronto backend (Lista proibita)
acc_eventNo_undefined=Numero evento indefinito
acc_advanceEvent_500=Anti-passback globale (logico)
acc_advanceEvent_501=Disponibilità persona (uso della data)
acc_advanceEvent_502=Numero di controllo persona
acc_advanceEvent_503=Interblocco globale
acc_advanceEvent_504=Controllo percorso
acc_advanceEvent_505=Anti-passback globale (a tempo)
acc_advanceEvent_506=Anti-passback globale (logico a tempo)
acc_advanceEvent_507=Disponibilità persona (dopo il primo utilizzo dei giorni validi)
acc_advanceEvent_508=Disponibilità persona (uso del numero di volte)
acc_advanceEvent_509=Verifica background non riuscita (personale non registrato)
acc_advanceEvent_510=Verifica background non riuscita (eccezione dati)
acc_alarmEvent_701=Violazione DMR (regole di impostazione: {0})
#[21]实时监控
acc_rtMonitor_openDoor=Apri
acc_rtMonitor_closeDoor=Chiudi
acc_rtMonitor_remoteNormalOpen=Normalmente aperta a distanza
acc_rtMonitor_realTimeEvent=Eventi in tempo reale
acc_rtMonitor_photoMonitor=Monitoraggio foto
acc_rtMonitor_alarmMonitor=Monitoraggio allarmi
acc_rtMonitor_doorState=Stato porta
acc_rtMonitor_auxOutName=Nome uscita accessoria
acc_rtMonitor_nonsupport=Non supportato
acc_rtMonitor_lock=Bloccato
acc_rtMonitor_unLock=Sbloccato
acc_rtMonitor_disable=Disabilitato
acc_rtMonitor_noSensor=Nessun sensore porta
acc_rtMonitor_alarm=Allarme
acc_rtMonitor_openForce=Apertura forzata
acc_rtMonitor_tamper=Manomissione
acc_rtMonitor_duressPwdOpen=Apertura con password Duress
acc_rtMonitor_duressFingerOpen=Apertura con impronta digitale Duress
acc_rtMonitor_duressOpen=Apertura Duress
acc_rtMonitor_openTimeout=Timeout apertura
acc_rtMonitor_unknown=Sconosciuto
acc_rtMonitor_noLegalDoor=Nessuna porta soddisfa la condizione.
acc_rtMonitor_noLegalAuxOut=Nessuna uscita accessoria soddisfa la condizione.
acc_rtMonitor_curDevNotSupportOp=Lo stato del dispositivo corrente non supporta questa operazione.
acc_rtMonitor_curNormalOpen=Apertura normale corrente
acc_rtMonitor_whetherDisableTimeZone=Lo stato corrente della porta è sempre aperta.
acc_rtMonitor_curSystemNoDoors=Il sistema corrente non ha aggiunto alcuna porta o non è in grado di rilevare porte che soddisfino i requisiti.
acc_rtMonitor_cancelAlarm=Annulla allarme
acc_rtMonitor_openAllDoor=Apri tutte le porte correnti
acc_rtMonitor_closeAllDoor=Chiudi tutte le porte correnti
acc_rtMonitor_confirmCancelAlarm=Annullare questo allarme?
acc_rtMonitor_calcelAllDoor=Annulla tutti gli allarmi
acc_rtMonitor_initDoorStateTip=Acquisizione in corso di tutte le porte autorizzate per gli utenti nel sistema...
acc_rtMonitor_alarmEvent=Evento allarme
acc_rtMonitor_ackAlarm=Conferma
acc_rtMonitor_ackAllAlarm=Conferma tutto
acc_rtMonitor_ackAlarmTime=Ora conferma
acc_rtMonitor_sureToAckThese=Confermare questo allarme {0}? Dopo la conferma, tutti gli allarmi saranno annullati.
acc_rtMonitor_sureToAckAllAlarm=Annullare tutti gli allarmi? Dopo la conferma, tutti gli allarmi saranno annullati.
acc_rtMonitor_noSelectAlarmEvent=Scegliere di confermare l’evento di allarme.
acc_rtMonitor_noAlarmEvent=Nessun evento di allarme nel sistema corrente.
acc_rtMonitor_forcefully=Annulla allarme (apertura forzata porta)
acc_rtMonitor_addToRegPerson=Aggiunta alla persona registrata
acc_rtMonitor_cardExist=La carta è stata assegnata da {0} e non può essere riemessa.
acc_rtMonitor_opResultPrompt={0} richieste inviate, {1} non inviate.
acc_rtMonitor_doorOpFailedPrompt=Impossibile inviare le richieste alle porte seguenti; riprovare.
acc_rtMonitor_remoteOpen=Apertura a distanza
acc_rtMonitor_remoteClose=Chiusura a distanza
acc_rtMonitor_alarmSoundClose=Audio chiuso
acc_rtMonitor_alarmSoundOpen=Audio aperto
acc_rtMonitor_playAudio=Segnali acustici promemoria eventi
acc_rtMonitor_isOpenShowPhoto=Abilita funzione visualizzazione foto
acc_rtMonitor_isOpenPlayAudio=Abilita funzione avviso audio
acc_rtm_open=Apertura a distanza con bottone
acc_rtm_close=Chiusura a distanza con bottone
acc_rtm_eleModule=Ascensore
acc_cancelAlarm_fp=Annulla allarme (apertura con impronta digitale Duress)
acc_cancelAlarm_pwd=Annulla allarme (apertura con password Duress)
acc_cancelAlarm_timeOut=Annulla allarme (timeout apertura porta esteso)
#定时同步设备时间
acc_timing_syncDevTime=Sincronizzazione dell’ora del dispositivo
acc_timing_executionTime=Ora di esecuzione
acc_timing_theLifecycle=Ciclo di vita
acc_timing_errorPrompt=Valore errato.
acc_timing_checkedSyncTime=Selezionare l’ora di sincronizzazione.
#[25]门禁报表
acc_trans_hasAccLevel=Livello per l’accesso disponibile
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Aggiungere la zona
acc_zone_code=Codice zona
acc_zone_parentZone=Zona principale
acc_zone_parentZoneCode=Codice zona principale
acc_zone_parentZoneName=Nome zona principale
acc_zone_outside=Esterno
#[G2]读头定义
acc_readerDefine_readerName=Nome lettore
acc_readerDefine_fromZone=Da
acc_readerDefine_toZone=A
acc_readerDefine_delInfo1=La zona della definizione del lettore è indicata da una funzione di controllo accessi avanzata e non può essere eliminata.
acc_readerDefine_selReader=Seleziona lettore
acc_readerDefine_selectReader=Aggiungere il lettore.
acc_readerDefine_tip=Quando il personale giunge all’esterno della zona, il relativo record viene eliminato.
#[G3]全局反潜
acc_gapb_zone=Zona
acc_gapb_whenToResetGapb=Azzeramento tempo anti-passback
acc_gapb_apbType=Tipo di anti-passback
acc_gapb_logicalAPB=Anti-passback logico
acc_gapb_timedAPB=Anti-passback a tempo
acc_gapb_logicalTimedAPB=Anti-passback logico a tempo
acc_gapb_lockoutDuration=Durata blocco
acc_gapb_devOfflineRule=Dispositivo offline
acc_gapb_standardLevel=Livello di accesso standard
acc_gapb_accessDenied=Accesso negato
acc_gapb_doorControlZone=Le porte seguenti controllano l’accesso all’interno e all’esterno della zona
acc_gapb_resetStatus=Reimposta stato anti-passback
acc_gapb_obeyAPB=Segui regole anti-passback
acc_gapb_isResetGAPB=Reimposta anti-passback
acc_gapb_resetGAPBSuccess=Reimpostazione stato anti-passback riuscita
acc_gapb_resetGAPBFaile=Reimpostazione stato anti-passback non riuscita
acc_gapb_chooseArea=Riselezionare la zona.
acc_gapb_notDelInfo1=La zona di accesso è indicata come area di accesso superiore e non può essere eliminata.
acc_gapb_notDelInfo2=La zona è usata come riferimento dalla definizione del lettore e non può essere eliminata.
acc_gapb_notDelInfo3=La zona è usata come riferimento dalle funzioni di controllo accessi avanzate e non può essere eliminata.
acc_gapb_notDelInfo4=L’area di accesso è usata come riferimento dal LED e non può essere eliminata.
acc_gapb_zoneNumRepeat=Numeri del dominio di controllo doppi
acc_gapb_zoneNameRepeat=Nomi del dominio di controllo doppi
acc_gapb_personResetGapbPre=Reimpostare?
acc_gapb_personResetGapbSuffix=regole anti-passback persona?
acc_gapb_apbPrompt=Non è possibile utilizzare un’unica porta per controllare due confini del perimetro indipendenti.
acc_gapb_occurApb=Anti-passback azionato
acc_gapb_noOpenDoor=Non aprire la porta
acc_gapb_openDoor=Aprire la porta
acc_gapb_zoneNumLength=Lunghezza superiore a 20 caratteri
acc_gapb_zoneNameLength=Lunghezza superiore a 30 caratteri
acc_gapb_zoneRemarkLength=Lunghezza superiore a 50 caratteri
acc_gapb_isAutoServerMode=Rilevato dispositivo senza funzione di verifica in background che può influire sul funzionamento. Aprirlo ora?
acc_gapb_applyTo=Applica a
acc_gapb_allPerson=Tutto il personale
acc_gapb_justSelected=Solo il personale selezionato
acc_gapb_excludeSelected=Escludi personale selezionato
#[G4]who is inside
acc_zoneInside_lastAccessTime=Ora ultimo accesso
acc_zoneInside_lastAccessReader=Ultimo lettore di accesso
acc_zoneInside_noPersonInZone=Nessuna persona nella zona
acc_zoneInside_noRulesInZone=Nessuna regola impostata.
acc_zoneInside_totalPeople=Persone totali
acc_zonePerson_selectPerson=Selezionare una persona o un reparto.
#[G5]路径
acc_route_name=Nome percorso
acc_route_setting=Impostazione percorso
acc_route_addReader=Aggiungi lettore
acc_route_delReader=Elimina lettore
acc_route_defineReaderLine=Definisci riga lettore
acc_route_up=Su
acc_route_down=Giù
acc_route_selReader=Selezionare il lettore dopo l’operazione.
acc_route_onlyOneOper=Scegliere un solo lettore da azionare.
acc_route_readerOrder=Sequenza lettori definita
acc_route_atLeastSelectOne=Selezionare almeno una testina di lettura.
acc_route_routeIsExist=Percorso già esistente.
#[G6]DMR
acc_dmr_residenceTime=Durata presenza
acc_dmr_setting=Impostazione DMR
#[G7]Occupancy
acc_occupancy_max=Capacità massima
acc_occupancy_min=Capacità minima
acc_occupancy_unlimit=Nessun limite
acc_occupancy_note=La capacità di posti minima non può superare quella massima.
acc_occupancy_containNote=Impostare almeno un valore fra capacità massima/minima.
acc_occupancy_maxMinValid=Impostare una capacità maggiore di 0.
acc_occupancy_conflict=In questa zona sono state impostate regole di controllo occupazione.
acc_occupancy_maxMinTip=L’assenza di un valore di capacità indica che non sono presenti limiti.
#card availability
acc_personLimit_zonePropertyName=Nome proprietà zona
acc_personLimit_useType=Utilizzo
acc_personLimit_userDate=Data di validità
acc_personLimit_useDays=Dopo il primo utilizzo dei giorni validi
acc_personLimit_useTimes=Frequenza di utilizzo
acc_personLimit_setZoneProperty=Imposta proprietà zona
acc_personLimit_zoneProperty=Proprietà zona
acc_personLimit_availabilityName=Nome disponibilità
acc_personLimit_days=Giorni
acc_personLimit_Times=Orari
acc_personLimit_noDel=Le proprietà della zona di controllo accessi selezionata sono usate come riferimento e non possono essere eliminate.
acc_personLimit_cannotEdit=L’attributo dell’area di accesso è usato come riferimento e non può essere modificato.
acc_personLimit_detail=Dettaglio
acc_personLimit_userDateTo=Termine validità
acc_personLimit_addPersonRepeatTip=La persona sotto il dipartimento selezionato è stata aggiunta all'attributo dell'area di controllo accessi, per favore seleziona di nuovo il dipartimento!
acc_personLimit_leftTimes={0} Tempi rimasti
acc_personLimit_expired=scaduto
acc_personLimit_unused=Non usato
#全局互锁
acc_globalInterlock_addGroup=Aggiungi gruppo
acc_globalInterlock_delGroup=Elimina gruppo
acc_globalInterlock_refuseAddGroupMessage=Stesso interblocco; la porta non può essere duplicata nel gruppo aggiunto
acc_globalInterlock_refuseAddlockMessage=La porta aggiunta è comparsa in altri gruppi di dispositivi interbloccati
acc_globalInterlock_refuseDeleteGroupMessage=Rimuovere i dati relativi all’interblocco
acc_globalInterlock_isGroupInterlock=Interblocco gruppo
acc_globalInterlock_isAddTheDoorImmediately=Aggiungi immediatamente la porta
acc_globalInterlock_isAddTheGroupImmediately=Aggiungi immediatamente il gruppo
#门禁参数设置
acc_param_autoEventDev=Download automatico del registro eventi del numero di dispositivi contemporanei
acc_param_autoEventTime=Download automatico del registro eventi degli intervalli contemporanei
acc_param_noRepeat=Non è possibile ripetere gli indirizzi e-mail; compilare nuovamente.
acc_param_most18=Aggiungere fino a 18 indirizzi e-mail
acc_param_deleteAlert=Impossibile eliminare il campo degli indirizzi e-mail.
acc_param_invalidOrRepeat=Errore di formato o ripetizione dell’indirizzo e-mail.
#全局联动
acc_globalLinkage_noSupport=La porta selezionata non supporta le funzioni di blocco e disattivazione del blocco.
acc_globalLinkage_trigger=Aziona collegamento globale
acc_globalLinkage_noAddPerson=La regola del collegamento non contiene un azionamento correlato al personale; per impostazione predefinita, è applicabile all’intero personale.
acc_globalLinkage_selectAtLeastOne=Selezionare almeno un punto di uscita di collegamento o un collegamento video.
acc_globalLinkage_selectTrigger=Aggiungere le condizioni di azionamento del collegamento.
acc_globalLinkage_selectInput=Aggiungere il punto di ingresso.
acc_globalLinkage_selectOutput=Aggiungere il punto di uscita.
acc_globalLinkage_audioRemind=Messaggi vocali collegamento
acc_globalLinkage_audio=Voce collegamento
acc_globalLinkage_isApplyToAll=Applica a tutto il personale
acc_globalLinkage_scope=Intervallo personale
acc_globalLinkage_everyPerson=Qualsiasi
acc_globalLinkage_selectedPerson=Selezionato
acc_globalLinkage_noSupportPerson=Non supportato
acc_globalLinkage_reselectInput=Il tipo di condizioni di azionamento è cambiato; riselezionare il punto di ingresso.
acc_globalLinkage_addPushDevice=Aggiungere il supporto per questa funzione del dispositivo
#其他
acc_InputMethod_tips=Passare alla modalità di immissione in inglese.
acc_device_systemCheckTip=Il dispositivo di accesso non esiste.
acc_notReturnMsg=Nessuna informazione restituita
acc_validity_period=Il periodo di validità della licenza è scaduto; impossibile utilizzare la funzione.
acc_device_pushMaxCount=Il sistema è presente in {0} dispositivo/i; il limite della licenza è stato raggiunto e non è possibile aggiungere il dispositivo.
acc_device_videoHardwareLinkage=Imposta collegamento hardware video
acc_device_videoCameraIP=IP videocamera
acc_device_videoCameraPort=Porta videocamera
acc_location_unable=Punto dell’evento non aggiunto alla mappa elettronica; impossibile individuare la posizione specifica.
acc_device_wgDevMaxCount=Il dispositivo ha raggiunto il limite della licenza nel sistema e non è possibile modificare le impostazioni.
#自定义报警事件
acc_deviceEvent_selectSound=Selezionare i file audio.
acc_deviceEvent_batchSetSoundErr=Anomalia audio allarme lotto impostato.
acc_deviceEvent_batchSet=Imposta audio
acc_deviceEvent_sound=Audio evento
acc_deviceEvent_exist=Già presente
acc_deviceEvent_upload=Carica
#查询门最近发生事件
acc_doorEventLatestHappen=Richiedi ultimi eventi relativi alla porta
#门禁人员信息
acc_pers_delayPassage=Ritarda passaggio
#设备容量提示
acc_dev_usageConfirm=Capacità corrente dell’apparecchiatura oltre il 90%
acc_dev_immediateCheck=Verificare immediatamente
acc_dev_inSoftware=Nel software
acc_dev_inFirmware=Nel firmware
acc_dev_get=Acquisisci
acc_dev_getAll=Acquisisci tutto
acc_dev_loadError=Caricamento non riuscito
#Reader
acc_reader_inout=Ingresso/Uscita
acc_reader_lightRule=Regole illuminazione
acc_reader_defLightRule=Regola predefinita
acc_reader_encrypt=Crittografa
acc_reader_allReaderOfCurDev=Tutti i lettori nel dispositivo corrente
acc_reader_tip1=Crittografia copiata su tutti i lettori nel dispositivo corrente.
acc_reader_tip2=Opzione modalità ID disponibile solo per testine di lettura che supportano questa funzione.
acc_reader_tip3=Il tipo di protocollo RS485 viene copiato su tutti i lettori nel dispositivo corrente. Le impostazioni avranno effetto dopo il riavvio del dispositivo!
acc_reader_tip4=L'opzione per nascondere alcune informazioni sul personale viene copiata per impostazione predefinita su tutti i lettori dello stesso dispositivo!
acc_reader_commType=Tipo comunicazione
acc_reader_commAddress=Indirizzo comunicazione
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=Disabilitato
acc_readerComAddress_repeat=Duplica indirizzo comunicazione
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=Indirizzo RS485
acc_readerCommType_wgAddress=Indirizzo Wiegand
acc_reader_macError=Inserire l’indirizzo Mac nel formato corretto.
acc_reader_machineType=Tipo di lettore
acc_reader_readMode=Modalità
acc_reader_readMode_normal=Modalità normale
acc_reader_readMode_idCard=Modalità carta identificativa
acc_reader_note=Suggerimenti: È possibile selezionare soltanto i dispositivi di controllo accessi che si trovano nella stessa area ({0}) della telecamera.
acc_reader_rs485Type=Tipo di protocollo RS485
acc_reader_userLock=Blocco accesso personale
acc_reader_userInfoReveal=Nascondere parte delle informazioni sul personale
#operat
acc_operation_pwd=Password operazione
acc_operation_pwd_error=Errore password
acc_new_input_not_same=Mancata corrispondenza del nuovo codice inserito
acc_op_set_keyword=Impostazioni codice licenza
acc_op_old_key=Vecchio codice
acc_op_new_key=Nuovo codice
acc_op_cofirm_key=Conferma codice
acc_op_old_key_error=Errore vecchio codice
#验证方式规则
acc_verifyRule_name=Nome regola
acc_verifyRule_door=Verifica porta
acc_verifyRule_person=Verifica personale
acc_verifyRule_copy=Copia impostazioni del lunedì negli altri giorni della settimana:
acc_verifyRule_tip1=Selezionare almeno una modalità di verifica.
acc_verifyRule_tip2=Se la regola contiene una modalità di verifica della persona, è possibile aggiungere solo una porta con lettore Wiegand.
acc_verifyRule_tip3=Il lettore RS485 può seguire soltanto la modalità di verifica porta e non supporta la modalità di verifica personale.
acc_verifyRule_oldVerifyMode=Vecchia modalità di verifica
acc_verifyRule_newVerifyMode=Nuova modalità di verifica
acc_verifyRule_newVerifyModeSelectTitle=Seleziona un nuovo metodo di verifica
acc_verifyRule_newVerifyModeNoSupportTip=Nessun dispositivo che supporta il nuovo metodo di verifica!
#Wiegand Test
acc_wiegand_beforeCard=La lunghezza della nuova carta ({0} bit) non coincide con quella della carta precedente.
acc_wiegand_curentCount=Lunghezza numero carta corrente: {0} bit
acc_wiegand_card=Carta
acc_wiegand_readCard=Leggi carta
acc_wiegand_clearCardInfo=Cancella dati carta
acc_wiegand_originalCard=Numero carta originale
acc_wiegand_recommendFmt=Formato carta consigliato
acc_wiegand_parityFmt=Formato parità dispari-pari
acc_wiegand_withSizeCode=Calcola automaticamente il codice del sito quando il relativo campo è lasciato in bianco
acc_wiegand_tip1=Queste carte possono non appartenere allo stesso lotto.
acc_wiegand_tip2=Codice sito:{0},Numero carta:{0}, mancata corrispondenza nel numero carta originale. Verificare nuovamente il codice del sito e il numero della carta immessi.
acc_wiegand_tip3=Mancata corrispondenza del numero carta immesso ({0}) nel numero carta originale. Ricontrollare.
acc_wiegand_tip4=Mancata corrispondenza del codice sito immesso ({0}) nel numero carta originale. Ricontrollare.
acc_wiegand_tip5=Per utilizzare questa funzione, mantenere vuote tutte le colonne del codice del sito.
acc_wiegand_warnInfo1=Quando si prosegue con la lettura di una nuova carta, passare manualmente alla carta successiva.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Pannello ingresso/uscita personale
acc_LCDRTMonitor_current=Dati personale corrente
acc_LCDRTMonitor_previous=Dati personale precedente
#api
acc_api_levelIdNotNull=Il campo dell’ID del gruppo di autorizzazioni non può essere vuoto
acc_api_levelExist=Il gruppo di autorizzazioni esiste
acc_api_areaNameNotNull=La regione non può essere vuota
acc_api_levelNotExist=Il gruppo di autorizzazioni non esiste
acc_api_levelNotHasPerson=Il gruppo di autorizzazioni non contiene alcuna persona
acc_api_doorIdNotNull=Il campo dell’ID della porta non può essere vuoto
acc_api_doorNameNotNull=Il campo del nome della porta non può essere vuoto
acc_api_doorIntervalSize=La durata di apertura della porta deve essere compresa fra 1 e 254
acc_api_doorNotExist=La porta non esiste
acc_api_devOffline=Dispositivo offline o disabilitato
acc_api_devSnNotNull=Il campo del numero di serie del dispositivo non può essere vuoto
acc_api_timesTampNotNull=Timestamp tidak dapat kosong
acc_api_openingTimeCannotBeNull=Il tempo di apertura della porta non può essere vuoto
acc_api_parameterValueCannotBeNull=Il valore del parametro non può essere vuoto
acc_api_deviceNumberDoesNotExist=Il numero di serie del dispositivo non esiste
acc_api_readerIdCannotBeNull=L'ID lettore non può essere vuoto
acc_api_theReaderDoesNotExist=La testina di lettura non esiste
acc_operate_door_notInValidDate=Attualmente non entro il tempo effettivo di apertura remota, contattare l'amministratore se necessario!
acc_api_doorOffline=La porta è offline o disabilitata
#门禁信息自动导出
acc_autoExport_title=Esportazione automatica delle transazioni
acc_autoExport_frequencyTitle=Frequenza di esportazione automatica
acc_autoExport_frequencyDay=Di giorno
acc_autoExport_frequencyMonth=Per mese
acc_autoExport_firstDayMonth=Primo giorno del mese
acc_autoExport_specificDate=Data specifica
acc_autoExport_exportModeTitle=Modalità di esportazione
acc_autoExport_dailyMode=Transazioni giornaliere
acc_autoExport_monthlyMode=Transazioni mensili (tutte le transazioni tra la data del mese scorso e questo mese)
acc_autoExport_allMode=Tutti i dati (esporta fino a 30000 pezzi di dati)
acc_autoExport_recipientMail=Cassetta postale del destinatario
#First In And Last Out
acc_inOut_inReaderName=Primo nome nel lettore
acc_inOut_firstInTime=Primo nel tempo
acc_inOut_outReaderName=Nome dell'ultimo lettore in uscita
acc_inOut_lastOutTime=Ultima uscita
#防疫参数
acc_dev_setHep=Impostare i parametri di rilevamento di maschera e temperatura
acc_dev_enableIRTempDetection=Abilita lo screening della temperatura con infrarossi
acc_dev_enableNormalIRTempPass=Negare l'accesso quando la temperatura supera l'intervallo
acc_dev_enableMaskDetection=Abilita rilevamento maschera
acc_dev_enableWearMaskPass=Nega accesso senza maschera
acc_dev_tempHighThreshold=Soglia di allarme di alta temperatura
acc_dev_tempUnit=Unità di temperatura
acc_dev_tempCorrection=Correzione della deviazione della temperatura
acc_dev_enableUnregisterPass=Consenti l'accesso a persone non registrate
acc_dev_enableTriggerAlarm=Attiva allarme esterno
#联动邮件
acc_mail_temperature=Temperatura corporea
acc_mail_mask=Se indossare la maschera
acc_mail_unmeasured=Non misurato
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort Global Events
acc_digifort_chooseDigifortEvents=Choose Digifort Global Events
acc_digifort_eventExpiredTip=Se l'evento globale viene eliminato dal server Digifort, sarà in rosso.
acc_digifort_checkConnection=Si prega di verificare se le informazioni di connessione del server Digifort sono corrette.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Aggiungi contatti
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=Imposta parametri estesi
acc_extendParam_faceUI=Display dell'interfaccia
acc_extendParam_faceParam=Parametri del viso
acc_extendParam_accParam=Parametri di controllo dell'accesso
acc_extendParam_intercomParam=Parametri interfono visivi
acc_extendParam_volume=Volume
acc_extendParam_identInterval=Intervallo di identificazione (ms)
acc_extendParam_historyVerifyResult=Visualizza i risultati della verifica storica
acc_extendParam_macAddress=Visualizza l'indirizzo MAC
acc_extendParam_showIp=Mostra indirizzo IP
acc_extendParam_24HourFormat=Mostra il formato 24 ore
acc_extendParam_dateFormat=Formato data
acc_extendParam_1NThreshold=Soglia 1: N
acc_extendParam_facePitchAngle=Angolo di beccheggio del viso
acc_extendParam_faceRotationAngle=Angolo di rotazione della faccia
acc_extendParam_imageQuality=Qualità dell'immagine
acc_extendParam_miniFacePixel=Pixel viso minimo
acc_extendParam_biopsy=Abilita la biopsia
acc_extendParam_showThermalImage=Mostra l'immagine termica
acc_extendParam_attributeAnalysis=Abilita l'analisi degli attributi
acc_extendParam_temperatureAttribute=Attributo di rilevamento della temperatura
acc_extendParam_maskAttribute=Attributo di rilevamento della maschera
acc_extendParam_minTemperature=Temperatura minima
acc_extendParam_maxTemperature=Temperatura massima
acc_extendParam_gateMode=Gate Mode
acc_extendParam_qrcodeEnable=Attiva la funzione QR code
#可视对讲
acc_dev_intercomServer=Indirizzo del servizio interfono visivo
acc_dev_intercomPort=Server interfono visivo
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Gruppo di autorizzazioni sincrono
# 夏令时名称
acc_dsTimeUtc_none=Non impostato
acc_dsTimeUtc_AreaNone=Non c'e'tempo di risparmio diurno in questa zona.
acc_dsTimeUtc1000_0=Canberra, Melbourne, Sydney
acc_dsTimeUtc1000_1=Heavyiron
acc_dsTimeUtc_0330_0=Terranova
acc_dsTimeUtc_1000_0=Isole Aleutine
acc_dsTimeUtc_0200_0=Mid Atlantic - usato
acc_dsTimeUtc0930_0=Adelaide.
acc_dsTimeUtc_0100_0=Azzorre
acc_dsTimeUtc_0400_0=Atlantic time (Canada)
acc_dsTimeUtc_0400_1=Santiago.
acc_dsTimeUtc_0400_2=Asuncia
acc_dsTimeUtc_0300_0=Groenlandia
acc_dsTimeUtc_0300_1=Isole Saint Pierre e Miquelon
acc_dsTimeUtc0200_0=Chisinau
acc_dsTimeUtc0200_1=Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius
acc_dsTimeUtc0200_2=Atene, Bucarest
acc_dsTimeUtc0200_3=Gerusalemme
acc_dsTimeUtc0200_4=Ammano
acc_dsTimeUtc0200_5=Beirut.
acc_dsTimeUtc0200_6=Damasco
acc_dsTimeUtc0200_7=Gaza, Hebron
acc_dsTimeUtc0200_8=Juba.
acc_dsTimeUtc_0600_0=Tempo centrale (USA e Canada)
acc_dsTimeUtc_0600_1=Guadalajara, Città del Messico, Monterrey
acc_dsTimeUtc_0600_2=Isola di Pasqua
acc_dsTimeUtc1300_0=Isole Samoa
acc_dsTimeUtc_0500_0=L'Avana
acc_dsTimeUtc_0500_1=Est (Stati Uniti e Canada)
acc_dsTimeUtc_0500_2=Haiti
acc_dsTimeUtc_0500_3=Indiana (Est)
acc_dsTimeUtc_0500_4=Isole Turks e Caicos
acc_dsTimeUtc_0800_0=Pacific time (USA e Canada)
acc_dsTimeUtc_0800_1=Baja California
acc_dsTimeUtc0330_0=teheran o tehran
acc_dsTimeUtc0000_0=Dublino, Edimburgo, Lisbona, Londra
acc_dsTimeUtc1200_0=Fiji
acc_dsTimeUtc1200_1=Petropaklovsk Kamchatka vecchio
acc_dsTimeUtc1200_2=Wellington, Auckland
acc_dsTimeUtc1100_0=Isola di Norfolk
acc_dsTimeUtc_0700_0=Chihuahua, La Paz, Mazatlan
acc_dsTimeUtc_0700_1=Tempo di montagna (USA e Canada)
acc_dsTimeUtc0100_0=Belgrado, Bratislava, Budapest, Lubiana, Praga
acc_dsTimeUtc0100_1=Sarajevo, Skopje, Varsavia, Zagabria
acc_dsTimeUtc0100_2=Casablanca
acc_dsTimeUtc0100_3=Bruxelles, Copenaghen, Madrid, Parigi
acc_dsTimeUtc0100_4=Amsterdam, Berlino, Berna, Roma, Stoccolma, Vienna
acc_dsTimeUtc_0900_0=Albom
#安全点(muster point)
acc_leftMenu_accMusterPoint=Punto di raccolta
acc_musterPoint_activate=Attiva
acc_musterPoint_addDept=Aggiungi reparto
acc_musterPoint_delDept=Elimina reparto
acc_musterPoint_report=Rapporto punto di raccolta
acc_musterPointReport_sign=Accedi manualmente
acc_musterPointReport_generate=Genera rapporti
acc_musterPoint_addSignPoint=Aggiungi punto segnaletico
acc_musterPoint_delSignPoint=Cancella punto segnale
acc_musterPoint_selectSignPoint=Aggiungi un punto segnaletico!
acc_musterPoint_signPoint=Segnale punto
acc_musterPoint_delFailTip=Ci sono punti di raccolta già attivati e non possono essere eliminati!
acc_musterPointReport_enterTime=Inserisci l'ora
acc_musterPointReport_dataAnalysis=Analisi dei dati
acc_musterPointReport_safe=Sicuro
acc_musterPointReport_danger=Pericolo
acc_musterPointReport_signInManually=punzonatura manuale
acc_musterPoint_editTip=Il punto di raccolta è attivo e non può essere modificato!
acc_musterPointEmail_total=Partecipazione prevista:
acc_musterPointEmail_safe=Checked in (safe):
acc_musterPointEmail_dangerous=In pericolo:
acc_musterPoint_messageNotification=Notifica del messaggio all'attivazione
acc_musterPointReport_sendEmail=Rapporto push programmato
acc_musterPointReport_sendInterval=Intervallo di invio
acc_musterPointReport_sendTip=Assicurati che il metodo di notifica selezionato sia configurato correttamente, altrimenti la notifica non verrà inviata normalmente!
acc_musterPoint_mailSubject=Avviso di montaggio di emergenza
acc_musterPoint_mailContent=Si prega di riunirsi immediatamente presso "{0}" e accedere al dispositivo "{1}", grazie!
acc_musterPointReport_mailHead=Salve, questo e' un rapporto di emergenza. Si prega di rivedere.
acc_musterPoint_visitorsStatistics=Statistiche dei visitatori
# 报警监控
acc_alarm_priority=priorità
acc_alarm_total=in totale
acc_alarm_today=Il record di oggi
acc_alarm_unhandled=Non confermata
acc_alarm_inProcess=In corso
acc_alarm_acknowledged=Confermato
acc_alarm_top5=Ultimi cinque eventi di allarme
acc_alarm_monitoringTime=Tempo di monitoraggio
acc_alarm_history=Storia dell'elaborazione degli avvisi
acc_alarm_acknowledgement=Registrazioni di trattamento
acc_alarm_eventDescription=Informazioni sugli eventi
acc_alarm_acknowledgeText=Dopo la selezione, l'e-mail dell'evento di allarme sarà inviata alla casella di posta specificata
acc_alarm_emailSubject=Aggiungi record di eventi di avviso
acc_alarm_mute=Muto
acc_alarm_suspend=sospensione
acc_alarm_confirmed=L'incidente è stato confermato.
acc_alarm_list=Registro allarmi
#ntp
acc_device_setNTPService=Impostazioni del server NTP
acc_device_setNTPServiceTip=Inserisci più indirizzi server, separati da virgole (,) o punti e virgola (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=Nel funzionamento del controllore di accesso, un Super utente non è limitato dalle normative sui fusi orari, anti-passback e interlock e ha priorità estremamente elevata di apertura della porta.
acc_editPerson_delayPassageTip=Estendere i tempi di attesa per il personale attraverso i punti di accesso. Adatto a disabili fisici o persone con altre disabilità.
acc_editPerson_disabledTip=Disabilitare temporaneamente il livello di accesso del personale.
#门禁向导
acc_guide_title=Configurazione guidata del modulo di controllo accessi
acc_guide_addPersonTip=Devi aggiungere la persona e le credenziali corrispondenti (faccia o impronta digitale o tessera o palmo o password); se l'hai già aggiunta, salta questo passaggio direttamente
acc_guide_timesegTip=Configura un periodo di apertura valido
acc_guide_addDeviceTip=Aggiungi il dispositivo corrispondente come punto di accesso
acc_guide_addLevelTip=Aggiungi livello di controllo dell'accesso
acc_guide_personLevelTip=Assegna l'autorità di controllo accessi corrispondente alla persona
acc_guide_rtMonitorTip=Controlla i record di controllo degli accessi in tempo reale
acc_guide_rtMonitorTip2=Visualizzazione in tempo reale dei record di controllo di accesso dopo l'aggiunta dell'area e della porta corrispondente
#查看区域内人员
acc_device_pwdRequired=Può inserire un massimo di 6 numeri interi