#[1]左侧菜单
acc_module=Erişim kontrolü
acc_leftMenu_accDev=Geçiş Cihazı
acc_leftMenu_auxOut=Aux Çıkış
acc_leftMenu_dSTime=Yaz Saati Uygulaması
acc_leftMenu_access=Geçiş Kontrol
acc_leftMenu_door=Kapı
acc_leftMenu_accRule=Geçiş Kuralları
acc_leftMenu_interlock=Interlock
acc_leftMenu_antiPassback=Anti-PassBack
acc_leftMenu_globalLinkage=Global Tetikleme
acc_leftMenu_firstOpen=İlk-Personel Normal Kapı Açma
acc_leftMenu_combOpen=Çoklu-Personel Kapı Açma
acc_leftMenu_personGroup=Çoklu-Personel Grubu
acc_leftMenu_level=Geçiş Yetkisi
acc_leftMenu_electronicMap=Harita
acc_leftMenu_personnelAccessLevels=Personel Geçiş Yetkileri
acc_leftMenu_searchByLevel=Geçiş Yetkisi Bazlı
acc_leftMenu_searchByDoor=Kapıya Erişim Hakkı
acc_leftMenu_expertGuard=Gelişmiş Fonksiyonlar
acc_leftMenu_zone=Alan
acc_leftMenu_readerDefine=Okuyucu Tanımı
acc_leftMenu_gapbSet=Global Anti-Passback
acc_leftMenu_whoIsInside=Kim içeride
acc_leftMenu_whatRulesInside=İçerdeki Kuralları Kontrol Edin
acc_leftMenu_occupancy=Doluluk Kontrolü
acc_leftMenu_route=Rota Kontrolü
acc_leftMenu_globalInterlock=Global InterLock
acc_leftMeue_globalInterlockGroup=Global Interlock Grubu
acc_leftMenu_dmr=Bekleme Süresi
acc_leftMenu_personLimit=Kişi Kullanılabilirliği
acc_leftMenu_verifyModeRule=Doğrulama Modu
acc_leftMenu_verifyModeRulePersonGroup=Doğrulama Mod Grubu
acc_leftMenu_extDev=I/O Kartı
acc_leftMenu_firstInLastOut=İlk Giriş ve Son Çıkış
acc_leftMenu_accReports=Erişim Kontrol Raporları
#[3]门禁时间段
acc_timeSeg_entity=Zaman Dilimi
acc_timeSeg_canNotDel=Zaman Dilimi kullanımda, silinemez !
#[4]门禁设备--公共的在common中
acc_common_ruleName=Kural adı
acc_common_hasBeanSet=Ayarlandı
acc_common_notSet=Ayarlanmadı
acc_common_hasBeenOpened=Açıldı
acc_common_notOpened=Açılmadı
acc_common_partSet=Kısmi ayar
acc_common_linkageAndApbTip=Tetikleme ve Global Tetikleme, Anti-Passback ve Global Anti-Passback aynı anda ayarlanırsa, sistemde çakışma olabilir.
acc_common_vidlinkageTip=İlgili giriş noktası bağlantı mevcut video kanalı bağlı ve çalışır olduğundan emin olun!
acc_common_accZoneFromTo=Aynı alan ayarlanamıyor
acc_common_logEventNumber=Olay ID
acc_common_bindOrUnbindChannel=Kamera Atama/Kaldırma
acc_common_boundChannel=Bağlı Kamera
#设备信息
acc_dev_iconType=Simge Tipi
acc_dev_carGate=Park Bariyeri
acc_dev_channelGate=Flap Bariyer
acc_dev_acpType=Kontrol Panel Tipi
acc_dev_oneDoorACP=Tek-Kapı Geçiş Kontrol Paneli
acc_dev_twoDoorACP=İki Kapılı Geçiş Kontrol Paneli
acc_dev_fourDoorACP=Dört-Kapı Geçiş Kontrol Paneli
acc_dev_onDoorACD=Bağımsız Cihaz
acc_dev_switchToTwoDoorTwoWay=2 Kapı 2 Yön'e Değiştir.
acc_dev_addDevConfirm2=İpucu: Cihaz bağlantısı başarılı, ancak erişim kontrol panelinin türü gerçek olandan farklı, {0} kapı kontrol panelini değiştirin.
acc_dev_addDevConfirm4=Bağımsız cihaz. Eklemeye devam etmek istiyor musunuz?
acc_dev_oneMachine=Bağımsız Cihaz
acc_dev_fingervein=Parmak Damar
acc_dev_control=Cihaz Kontrol
acc_dev_protocol=Protokol Tipi
acc_ownedBoard=Mevcut Pano
#设备操作
acc_dev_start=Başlangıç
acc_dev_accLevel=Geçiş Yetkisi
acc_dev_timeZoneAndHoliday=Zaman Dilimi, Tatiller
acc_dev_linkage=Tetikleme
acc_dev_doorOpt=Kapı Parametreleri
acc_dev_firstPerson=İlk-Personel ile Kapı Açma
acc_dev_multiPerson=Çoklu-Personel Kapı Açma
acc_dev_interlock=Interlock
acc_dev_antiPassback=AntiPassBack
acc_dev_wiegandFmt=Wiegand Format
acc_dev_outRelaySet=Aux Çıkış Ayarları
acc_dev_backgroundVerifyParam=AP-Doğrulama Ayarları
acc_dev_getPersonInfoPrompt=Aksi bir durum olmaması için Personel bilgilerinin alındığından emin olunuz. Devam etmek istiyor musunuz?
acc_dev_getEventSuccess=Olayları alma başarılı
acc_dev_getEventFail=Olay(ları) alma başarısız
acc_dev_getInfoSuccess=Bilgi Alma Başarılı
acc_dev_getInfoXSuccess=Alma {0} başarılı.
acc_dev_getInfoFail=Bilgi alma başarısız
acc_dev_updateExtuserInfoFail=Personel için Geçiş Uzatma bilgisi yüklemenedi. Lütfen operatörden bilgi alın
acc_dev_getPersonCount=Kullanıcı Sayısını Al
acc_dev_getFPCount=Parmakizi sayısı al
acc_dev_getFVCount=Parmak Damar Sayısını Al
acc_dev_getFaceCount=Yüz Sayısı Al
acc_dev_getPalmCount=Avuç içi Sayısını Al
acc_dev_getBiophotoCount=Yüz Resim Sayısı Al
acc_dev_noData=Cihazdan geri dönen veri yok
acc_dev_noNewData=Cihazda yeni kayıt yok.
acc_dev_softLtDev=Yazlımdaki kişi sayısı cihazdaki kişi sayısından fazla.
acc_dev_personCount=Kişi Sayısı:
acc_dev_personDetail=Detaylar aşağıdaki gibidir:
acc_dev_softEqualDev=Hem cihazdaki hemde yazılımdaki kişi sayısı eşittir.
acc_dev_softGtDev=Cihazda yazılımdan fazla kişi var
acc_dev_cmdSendFail=Komut göndermek başarısız, yeniden gönderin.
acc_dev_issueVerifyParam=ArkaPlan Doğrulama Ayarları
acc_dev_verifyParamSuccess=Ap-Doğrulama seçenekleri başarıyla uygulandı.
acc_dev_backgroundVerify=Arkaplan Doğrulaması
acc_dev_selRightFile=Lütfen geçerli bir güncelleme dosyası seçin!
acc_dev_devNotOpForOffLine=Cihaz ÇevrimDışı, Lütfen daha sonra tekrar deneyin.
acc_dev_devNotSupportFunction=Cihaz bu özelliği desteklemiyor
acc_dev_devNotOpForDisable=Cihaz çevrimdışı, Lütfen tekrar deneyin
acc_dev_devNotOpForNotOnline=Cihaz kapalı yada devre dışı, lütfen tekrar deneyin
acc_dev_getPersonInfo=Personel bilgilerini al
acc_dev_getFPInfo=Parmak izi bilgisi edin
acc_dev_getFingerVeinInfo=Parmak Damar bilgisi Al
acc_dev_getPalmInfo=Avuç içi Bilgisi Al
acc_dev_getBiophotoInfo=Görünen yüzün ışık bilgileri
acc_dev_getIrisInfo=Iris bilgisini alın
acc_dev_disable=Aktif Değil, Lütfen tekrar seçin.
acc_dev_offlineAndContinue=çevrimdışı, devam edilsin mı?
acc_dev_offlineAndSelect=ÇevrimDışı
acc_dev_opAllDev=Tüm Cihazlar
acc_dev_opOnlineDev=Online Cihaz
acc_dev_opException=İşleme istisnası
acc_dev_exceptionAndConfirm=Cihaz bağlantısı zaman aşımı, işlem başarısız oldu. Ağ bağlantısını kontrol edin
acc_dev_getFaceInfo=Alınan yüz bilgileri
acc_dev_selOpDevType=İşlem yapılacak cihazların türünü seçin:
acc_dev_hasFilterByFunc=Yalnızca Çevrimiçi ve bu özelliği destekleyen cihazları göster.
acc_dev_masterSlaveMode=RS485 Ana ve Yardımcı mod
acc_dev_master=Sunucu
acc_dev_slave=Yardımcı
acc_dev_modifyRS485Addr=RS485 Adresi Düzenle
acc_dev_rs485AddrTip=Lütfen 1 ila 63 arasında bir sayı giriniz!
acc_dev_enableFeature=Arka plan doğrulamasını etkinleştiren cihazlar
acc_dev_disableFeature=Arka plan doğrulaması devre dışı bırakılan cihazlar.
acc_dev_getCountOnly=Sadece Sayıları Al
acc_dev_queryDevPersonCount=Personel Sayı Sorgusu
acc_dev_queryDevVolume=Cihaz Kapasitesini Görüntüle
acc_dev_ruleType=Kural Tipi
acc_dev_contenRule=Kural Detayları
acc_dev_accessRules=Cihaz Kurallarını Görüntüle
acc_dev_ruleContentTip=Birden çok kural '|' ile ayrılmış
acc_dev_rs485AddrFigure=RS485 Adresi Kod Şekil
acc_dev_addLevel=Yetki Ekle
acc_dev_personOrFingerTanto=Personel veya parmak izi sayısı sınırı aşıyor, senkronizasyon başarısız ...
acc_dev_personAndFingerUnit=(Numara)
acc_dev_setDstime=Yaz Saati Uygulaması Ayarla
acc_dev_setTimeZone=Cihaza Zaman Dilimi Ayarı
acc_dev_selectedTZ=Seçilen Zaman Dilimi
acc_dev_timeZoneSetting=Zaman Dilimi Ayarlanıyor...
acc_dev_timeZoneCmdSuccess=Zaman Dilimi Komut Gönderme Başarılı...
acc_dev_enableDstime=Gün Işığı Tasarruf Aktif
acc_dev_disableDstime=Yaz Saati Uygulaması DevreDışı
acc_dev_timeZone=Zaman Dilimi
acc_dev_dstSettingTip=Gün Işığı Tasarruf Ayarları
acc_dev_dstDelTip=Yaz Saati Uygulaması Cihazdan Kaldırılıyor...
acc_dev_enablingDst=Yaz Saati Uygulaması Aktifleştiriliyor
acc_dev_dstEnableCmdSuccess=Gün Işığı tasarruf Aktif komutu cihaza başarıyla gönderildi.
acc_dev_disablingDst=Yaz Saati Uygulaması Devre Dışı Bırakılıyor.
acc_dev_dstDisableCmdSuccess=Yaz Saati Uygulama Devre Dışı Komutu Gönderme Başarılı
acc_dev_dstCmdSuccess=Yaz Saati Uygulama Komut Gönderimi Başarılı
acc_dev_usadst=USA Yaz Saati Uygulaması
acc_dev_notSetDst=Ayar Yok
acc_dev_selectedDst=Yaz Saati Uygulaması Seçildi.
acc_dev_configMasterSlave=Ana Cihaz- Yardımcı Cihaz konfigrasyonu
acc_dev_hasFilterByUnOnline=Sadece çevrimiçi cihazı gösterin.
acc_dev_softwareData=Eğer cihazda tutatsız bir veri bulursanız, tekrar denemeden önce cihazı bilgisini senkronize edinç
acc_dev_disabled=Çevrimdışı cihazlara işlem yapılamaz!
acc_dev_offline=Cihaz çevrimdışı işlem yapılamaz
acc_dev_noSupport=Cihazlar bu işlevi desteklemez ve çalıştırılamaz!
acc_dev_noRegDevTip=Cihaz, Kayıt cihazı olarak ayarlanmamış. Bilgiler yazılıma yüklenmeyecek. Devam etmek istiyor musunuz?
acc_dev_noOption=Şuan uygun seçenek yok.
acc_dev_devFWUpdatePrompt=Mevcut cihaz normalde devre dışı, kişiyi ve kişi geçerlilik işlevini kullanmayacaktır (lütfen kullanım kılavuzuna bakın).
acc_dev_panelFWUpdatePrompt=Geçerli cihaz normal kullanım için değildir,
acc_dev_sendEventCmdSuccess=Olay Silme Komutu başarıyla gönderildi.
acc_dev_tryAgain=Lütfen tekrar denetin
acc_dev_eventAutoCheckAndUpload=Otomatik kontrol ve yükleme olaylar
acc_dev_eventUploadStart=Olay yüklemeyi başlat
acc_dev_eventUploadEnd=Olay yükleme tamamlandı.
acc_dev_eventUploadFailed=Olay yüklemesi başarısız
acc_dev_eventUploadPrompt=Düşük Firmware sürümüne sahip bir cihaz tespit edildi, Firmware yükseltmek istiyor musunuz：
acc_dev_backupToSoftware=Verileri yazılıma yedekle
acc_dev_deleteEvent=Eski kayıtları silin
acc_dev_upgradePrompt=Firmware versiyonu çok düşün ve hatalı olabilir, Güncelleme yapmadan önce lütfen programa yedek alınız.
acc_dev_conflictCardNo=Sistemde diğer kişiye {0} kart mevcut!
acc_dev_rebootAfterOperate=İşlem başarılı, Cihaz yeniden başlatılacak.
acc_dev_baseOptionTip=Alınan basit değerler anormal.
acc_dev_funOptionTip=Alınan fonksiyon parametreler anormal
acc_dev_sendComandoTip=Cihazdan parametre komut alma bilgi göndermesi başarısız.
acc_dev_noC3LicenseTip=Bu tip cihaz eklenemiyor!({0})! Satış ekibi ile temasa geçiniz
acc_dev_combOpenDoorTip=({0}) Çoklu-Personel kapı açma ayarlandı, Arkaplan doğrulaması ile aynı anda kullanılamaz
acc_dev_combOpenDoorPersonCountTip={0} grubu, {1} adetten fazlasını açamaz!
acc_dev_addDevTip=Bu sadece PULL iletişim protokolüne sahip bir cihaz eklemek için geçerlidir!
acc_dev_addError=Cihaz istisna eklemesi, Eksik parametre ({0})!
acc_dev_updateIPAndPortError=Sunucu IP ve port güncelleme hatası...
acc_dev_transferFilesTip=Firmware test tamamlandı, Dosya iletiliyor
acc_dev_serialPortExist=Seri Port Bağlantısı var
acc_dev_isExist=Cihaz Var
acc_dev_description=Açıklama
acc_dev_searchEthernet=Ethernet cihazı ara
acc_dev_searchRS485=RS485 cihazı ara
acc_dev_rs485AddrTip1=RS485 başlangıç adresi, bitiş adresinden büyük olamaz
acc_dev_rs485AddrTip2=RS485 arama kapsamı 20'den az olmalıdır
acc_dev_clearAllCmdCache=Tüm Komutları Temizle
acc_dev_authorizedSuccessful=Yetkilendirme Başarılı
acc_dev_authorize=Yetki
acc_dev_registrationDevice=Kayıt Cihazı
acc_dev_setRegistrationDevice=Kayıt Cihazı olarak Ayarla
acc_dev_mismatchedDevice=Bu cihaz sizin bölgenizde kullanlamaz. Lütfen satış ekibimizle cihazın seri numarasını kontrol ediniz.
acc_dev_pwdStartWithZero=Haberleşme şifresi sıfır ile başlayamaz!
acc_dev_maybeDisabled=Mevcut lisans, {0} kapı daha eklenmesine izin veriyor.Yeni eklenen cihazda izin verilen nokta sınırını aşan kapılar devre dışı bırakılacak. Devam etmek istiyor musunuz?
acc_dev_Limit=Lisans sistemi üst sınıra ulaştı, Eklemek için yetki verin
acc_dev_selectDev=Lütfen cihaz seçin!
acc_dev_cannotAddPullDevice=Pul Cihaz eklenemiyor ! Lütfen Satış Ekibi ile irtibata geçiniz.
acc_dev_notContinueAddPullDevice=Sistemde {0} Pull Cihaz Var, Tekrar eklenemiyor. Lütfen satış ekibiyle irtibata geçiniz.
acc_dev_deviceNameNull=Cihaz modeli boş; Cihaz eklenemedi.
acc_dev_commTypeErr=Haberleşme tipi eşleşmiyor, Cihaz eklenemez!
acc_dev_inputDomainError=Lütfen geçerli sunucu adresi girin.
acc_dev_levelTip=Seviyede 5000'den fazla kişi var, cihazı buraya ekleyemiyor.
acc_dev_auxinSet=Aux Giriş Ayarları
acc_dev_verifyModeRule=Doğrulama Modu Kuralı
acc_dev_netModeWired=Kablolu
acc_dev_netMode4G=4G
acc_dev_netModeWifi=WIFI
acc_dev_updateNetConnectMode=Ağ Bağlantısını Değiştir.
acc_dev_wirelessSSID=Wireless SSID
acc_dev_wirelessKey=Kablosuz Anahtar
acc_dev_searchWifi=WIFI Ara
acc_dev_testNetConnectSuccess=Bağlantı başarılı mı?
acc_dev_testNetConnectFailed=İletişim için düzgün bağlantı kurulamıyor.
acc_dev_signalIntensity=Sinyal Gücü
acc_dev_resetSearch=Tekrar Ara
acc_dev_addChildDevice=Alt-Cihaz Ekle
acc_dev_modParentDevice=Ana cihazı değiştir.
acc_dev_configParentDevice=Ana Cihaz Ayarları
acc_dev_lookUpChildDevice=Alt cihazları görüntüle
acc_dev_addChildDeviceTip=Yetkilendirme, yetkili bir alt aygıt altında yapılmalıdır
acc_dev_maxSubCount=Yetkili alt cihaz sayısı maksimum {0} birim sayısını aşıyor.
acc_dev_seletParentDevice=Lütfen ana cihazı seçin
acc_dev_networkCard=Ağ Kartı
acc_dev_issueParam=Özel Senkronizasyon Parametreleri
acc_dev_issueMode=Senkronizasyon Modu
acc_dev_initIssue=Firmware Sürümü 3030 Başlatma Verileri
acc_dev_customIssue=Özelleştirilmiş Veri Senkronizasyonu
acc_dev_issueData=Veri
acc_dev_parent=Ana Cihaz
acc_dev_parentEnable=Ana cihaz çevrimdışı
acc_dev_parentTips=Ana cihaza bağlanması cihazdaki tüm verileri siler ve yeniden ayarlanması gerekir.
acc_dev_addDevIpTip=Yeni IP Adresi, Server adresi ile aynı olamaz
acc_dev_modifyDevIpTip=Yeni sunucu IP'si cihazın IP'si ile aynı olamaz.
acc_dev_setWGReader=Wiegand Okuyucu Ayarı
acc_dev_selectReader=Okuyucu seçmek için Tıklayınız
acc_dev_IllegalDevice=Geçersiz Cihaz
acc_dev_syncTimeWarnTip=Aşağıdaki cihazlar için senkronizasyon süreleri ana cihazda senkronizasyonuna bağlıdır.
acc_dev_setTimeZoneWarnTip=Cihazların saat dilimleri ana cihaz ile senkronize olmalıdır.
acc_dev_setDstimeWarnTip=Aşağıdaki cihazların Yaz Saat Uygulama senkronizasyonu ana cihaza bağlıdır.
acc_dev_networkSegmentSame=İki ağ bağdaştırıcısını aynı ağ segmentinde kullanamazsınız.
acc_dev_upgradeProtocolNoMatch=Güncelleme dosya protokolü eşleşmiyor
acc_dev_ipAddressConflict=Aynı IP adresine sahip cihaz var. Lütfen cihaz IP adresini değiştirin ve tekrar ekleyin.
acc_dev_checkServerPortTip=Ayarlanan Sunucu portuyla sistem haberleşme portu tutarsız, eklemek başarısız olabilir. Devam edilsin mi?
acc_dev_clearAdmin=Yönetici Yetkisi Temizle
acc_dev_setDevSate=Ayar Cihazı Giriş / Çıkış Durumu
acc_dev_sureToClear=Cihaz Yönetici Yetkilerini silmek istediğinizden emin misiniz?
acc_dev_hostState=Ana Cihaz Durumu
acc_dev_regDeviceTypeTip=Bu cihaz kısıtlanmıştır. Doğrudan silinemez ve düzenlenemez. Lütfen Admin operatör ile irtibata geçiniz.
acc_dev_extBoardType=I/O Kart Tipi
acc_dev_extBoardTip=Konfigrasyondan sonra, ayarların etkin olması için cihazın yeniden başlatılması gerekiyor.
acc_dev_extBoardLimit=Her aygıta yalnızca {0} bu tür I/O kartı eklenebilir!
acc_dev_replace=Cihazı Değiştir
acc_dev_replaceTip=Değiştirme işleminden sonra eski cihaz çalışmayacak, lütfen dikkatli olun!
acc_dev_replaceTip1=Değiştirme işleminden sonra lütfen "tüm verileri senkronize et" işlemini gerçekleştirin;
acc_dev_replaceTip2=Lütfen değiştirilecek cihaz modelinin aynı olduğundan emin olun!
acc_dev_replaceTip3=Lütfen yeni cihazın eski cihazla aynı sunucu adresini ve bağlantı noktasını ayarladığından emin olun!
acc_dev_replaceFail=Cihaz makine tipi tutarsız ve değiştirilemez!
acc_dev_notApb=Bu cihaz kapıyı gerçekleştirebilir ya da başı deniz operasyonlarını okuyamaz.
acc_dev_upResourceFile=Kaynak dosyalarını yükle
acc_dev_playOrder=Oynama düzeni
acc_dev_setFaceServerInfo=Facial Arka Uç Karşılaştırma Parametersini Ayarlayın
acc_dev_faceVerifyMode=Yüzü Karşılaştırma Modu
acc_dev_faceVerifyMode1=Yerel Karşılaştırma
acc_dev_faceVerifyMode2=Arka Uç Karşılaştırması
acc_dev_faceVerifyMode3=Yerel Karşılaştırma Öncelikliği
acc_dev_faceBgServerType=Facial Arka Uç Sunucusu Türü
acc_dev_faceBgServerType1=Yazılım Platformu Hizmetleri
acc_dev_faceBgServerType2=Üçüncü Parti Hizmetleri
acc_dev_isAccessLogic=Erişim Kontrol Mantık Denetimi Etkinleştir
#[5]门-其他关联的也复用此处
acc_door_entity=Kapı
acc_door_number=Kapı Numarası
acc_door_name=Kapı İsmi
acc_door_activeTimeZone=Aktif Zaman Dilimi
acc_door_passageModeTimeZone=Geçici Zaman Dilimi Modu
acc_door_setPassageModeTimeZone=Geçiş Modu Zaman Dilim Ayarı
acc_door_notPassageModeTimeZone=Geçişsiz Zaman Dilimi Modu
acc_door_lockOpenDuration=Kilit Açma Süresi
acc_door_entranceApbDuration=Anti-PassBack Geçiş Süresi
acc_door_sensor=Kapı Sensörü
acc_door_sensorType=Kapı Sensör Tipi
acc_door_normalOpen=Normal Açık
acc_door_normalClose=Normalde Kapalı
acc_door_sensorDelay=Kapı Sensör Gecikmesi
acc_door_closeAndReverseState=Kapı Kapı Ters Kilit Durmu
acc_door_hostOutState=Sunucu Giriş Durumu
acc_door_slaveOutState=Yardımcı Cihaz Durumu
acc_door_inState=Giriş
acc_door_outState=Dışarı
acc_door_requestToExit=REX (Çıkış İsteği) Modu
acc_door_withoutUnlock=Kilitli
acc_door_unlocking=Kilitli Değil
acc_door_alarmDelay=REX Geçikmesi
acc_door_duressPassword=Panik Şifresi
acc_door_currentDoor=Geçerli Kapı
acc_door_allDoorOfCurDev=Geçerli Cihazın Tüm Kapıları
acc_door_allDoorOfAllDev=Tüm Cihazların Tüm Kapıları
acc_door_allDoorOfAllControlDev=Tüm Bağımsız Cihazların Tüm Kapıları
acc_door_allDoorOfAllStandaloneDev=Tüm Bağımsız Cihazların Tüm Kapıları
acc_door_allWirelessLock=Tüm Kablosuz Kilitler
acc_door_max6BitInteger=Maksimum 6 Bit
acc_door_direction=Yönlendirme
acc_door_onlyInReader=Sadece Giriş Okuyucu
acc_door_bothInAndOutReader=Giriş & Çıkış Okuyucuları
acc_door_noDoor=Lütfen kapı ekleyin
acc_door_nameRepeat=Tekrarlanan kapı ismi
acc_door_duressPwdError=Acil durum şifresi personel şifresi ile aynı olmamalı.
acc_door_urgencyStatePwd=Lütfen {0} bit tamsayı girin!
acc_door_noDevOnline=Çevrim içi olmayan cihaz veya kapı kart doğrulama modunu desteklemez.
acc_door_durationLessLock=Kapı Sensörü Gecikmesi Kilit Açma Süresinden daha büyük olmalıdır.
acc_door_lockMoreDuration=Kilit Açma Süresi, Kapı Sensör Geçikme Süresinden Daha Az Olmalıdır.
acc_door_lockAndExtLessDuration=Kilit Açma Süresinin ve Geçiş Gecikmesinin toplamı Kapı Sensörü Gecikmesinden daha az olmalıdır.
acc_door_noDevTrigger=Şartlara uygun cihaz bulunamadı!
acc_door_relay=Röle
acc_door_pin=Pin
acc_door_selDoor=Kapı Seç
acc_door_sensorStatus=Kapı Sensör ({0})
acc_door_sensorDelaySeconds=Kapı Sensör Gecikmesi ({0}) s)
acc_door_timeSeg=Zaman Dilimi ({0})
acc_door_combOpenInterval=Çift-Personel İşlem Aralığı
acc_door_delayOpenTime=Kapı Açık Kalma Süresi
acc_door_extDelayDrivertime=Geçiş Geçikmesi
acc_door_enableAudio=Alarm Aktif
acc_door_disableAudio=Alarm Sesleri Devre Dışı
acc_door_lockAndExtDelayTip=Kilit Açma Süresi ve Kapı Geçiş Geçikme süre toplamı 254 saniyeden büyük olamaz
acc_door_disabled=Çevrimdıiı kapılarda işlem yapılamaz!
acc_door_offline=Devre dışı olan kapıda işlem yapılamaz!
acc_door_notSupport=Aşağıdaki kapılar bu özelliği desteklemiyor!
acc_door_select=Kapı Seçin
acc_door_pushMaxCount=Sistemde {0} etkin kapı var ve lisans sınırına ulaştı! Lütfen satış departmanımızla iletişime geçin
acc_door_outNumber=Mevcut lisans yalnızca {0} kapıyı etkinleştirmek için izin verir! Yeniden kapı ekleme veya kişi kapasitesi için bir güncelleme lisans satın alınız..
acc_door_latchTimeZone=REX Zaman Dilimi
acc_door_wgFmtReverse=Ters Kart Numarası
acc_door_allowSUAccessLock=Kapı Kilitlendiğinde Super Kullanıcılara İzin Ver
acc_door_verifyModeSinglePwd=Şifreler bağımsız bir doğrulama yöntemi olarak kullanılamaz!
acc_door_doorPassword=Kapı açma parolanı
#辅助输入
acc_auxIn_timeZone=Aktif Zaman Dilimi
#辅助输出
acc_auxOut_passageModeTimeZone=Geçiş Modu Zaman Dilimi
acc_auxOut_disabled=Devre Dışı Aux çıkışına işlem yapılamaz
acc_auxOut_offline=Çevrim dışı aux çıkışa işlem yapılamaz
#[8]门禁权限组
acc_level_doorGroup=Kapı Kombinasyonu
acc_level_openingPersonnel=Açılış Personeli
acc_level_noDoor=Kullanılabilir öğe yok. Lütfen önce cihaz seçin.
acc_level_doorRequired=Kapı Seçilmeli
acc_level_doorCount=Kapı sayısı
acc_level_doorDelete=Kapı Sil
acc_level_isAddDoor=Hemen mevcut Erişim Kontrol seviyesine kapı ekleyin?
acc_level_master=Genel
acc_level_noneSelect=Lütfen geçiş kontrol yetkisi ekleyin.
acc_level_useDefaultLevel=Geçiş kontrol yetkisini yeni departmana ayarla.
acc_level_persAccSet=Personel geçiş kontrol ayarları
acc_level_visUsed={0} ziyaretçi modülü tarafından zaten kullanılıyor ve silinemez!
acc_level_doorControl=Kapı Kontrol
acc_level_personExceedMax=Mevcut izin gruplarının sayısı ({0}), ve seçeneksel izin gruplarının maksimum sayısı ({1})
acc_level_exportLevel=Erişim Düzeyini Dışa Aktar
acc_level_exportLevelDoor=Erişim Düzeyinin Kapılarını Dışa Aktar
acc_level_exportLevelPerson=Erişim Düzeyindeki Personeli Dışa Aktar
acc_level_importLevel=Erişim Düzeyini İçe Aktar
acc_level_importLevelDoor=Erişim Düzeyinin Kapılarını İçe Aktar
acc_level_importLevelPerson=Erişim Düzeyindeki Personeli İçe Aktar
acc_level_exportDoorFileName=Erişim Seviyesinin Kapı Bilgileri
acc_level_exportPersonFileName=Erişim Düzeyi Personel Bilgileri
acc_levelImport_nameNotNull=Erişim Düzeyi adı boş olamaz
acc_levelImport_timeSegNameNotNull=Saat Dilimi boş olamaz
acc_levelImport_areaNotExist=Alan mevcut değil!
acc_levelImport_timeSegNotExist=Saat Dilimi mevcut değil!
acc_levelImport_nameExist=Erişim Düzeyi adı {0} zaten var!
acc_levelImport_levelDoorExist={0} Erişim Düzeyinin Kapıları zaten var!
acc_levelImport_levelPersonExist={0} Erişim Düzeyindeki Personel zaten var!
acc_levelImport_noSpecialChar=Erişim düzeyi adı özel karakterler içeremez!
#[10]首人常开
acc_firstOpen_setting=Tek Kişi Normal Açma
acc_firstOpen_browsePerson=Personele Göz At
#[11]多人组合开门
acc_combOpen_comboName=Kombinasyon İsmi
acc_combOpen_personGroupName=Grup İsmi
acc_combOpen_personGroup=Çoklu-Personel Grubu
acc_combOpen_verifyOneTime=Geçerli Personel Sayısı
acc_combOpen_eachGroupCount=Her gruptaki açılış personel sayısı
acc_combOpen_group=Grup
acc_combOpen_changeLevel=Kapı Açma Grubu
acc_combOpen_combDeleteGroup=Mevcut açılış kombinasyon referansı, Lütfen önce Mevcut olanı silin
acc_combOpen_ownedLevel=Gruba Ait
acc_combOpen_mostPersonCount=Grup personel sayısı 5'ten fazla olamaz
acc_combOpen_leastPersonCount=Grup sayısı iki personelden az olamaz
acc_combOpen_groupNameRepeat=Grup ismi mevcut
acc_combOpen_groupNotUnique=Kapı Açma Kişi Grupları aynı olmamalıdır.
acc_combOpen_persNumErr=Seçtiğiniz gruptaki kişi sayısı olması gereken değeri aşıyor, lütfen tekrar seçin!
acc_combOpen_combOpengGroupPersonShort=Personel çıkarıldıktan sonra, personel kapı açma grubunun sayısı yeterli olmayacaktır, lütfen önce açık grubu silin.
acc_combOpen_backgroundVerifyTip=Kapıda ArkaPlan doğrulaması var ise; Çoklu-Personel Kapı açma özelliği aynı anda kullanılamaz.
#[12]互锁
acc_interlock_rule=Interlock Kuralı
acc_interlock_mode1Or2=Interlock {0} ve {1} arasında
acc_interlock_mode3=Interlock {0} ve {1} ve {2} arasında
acc_interlock_mode4=Interlock {0} ve{1} arasında ya da {2} ve {3} arasında
acc_interlock_mode5=Interlock  {0} ve{1} ve{2} ve {3} arasında
acc_interlock_hasBeenSet=İnterlock programı ayarlandı
acc_interlock_group1=Grup 1
acc_interlock_group2=Grup 2
acc_interlock_ruleInfo=Ara grup kilitleme
acc_interlock_alreadyExists=Aynı kilitleme kuralı zaten mevcut, lütfen tekrar eklemeyin!
acc_interlock_groupInterlockCountErr=İçindeki grup kilitleme kuralı en azından iki kapı gerekiyor.
acc_interlock_ruleType=Interlocking Rule Type
#[13]反潜
acc_apb_rules=Anti-Passback Kuralı
acc_apb_reader=Anti-Passback {0} okuyucuları arasında
acc_apb_reader2=Anti-Passback, kapı okuyucuları arasında: {0}, {1}
acc_apb_reader3=Anti-Passback, kapı okuyucuları arasında: {0}, {1}, {2}
acc_apb_reader4=Anti-Passback, kapı okuyucuları arasında: {0}, {1}, {2}, {3}
acc_apb_reader5={0} Kapısı Çıkış Okuyucu için Anti-Passback
acc_apb_reader6={0} Kapısı Giriş Okuyucu için Anti-Passback
acc_apb_reader7=4 Kapı okuyucuları için Anti-Passback
acc_apb_twoDoor=Anti-Passback {0} ve{1} arasında
acc_apb_fourDoor=Anti-Passback {0} ve{1} arasında , Anti-Passback {2} ve {3} arasında
acc_apb_fourDoor2=Anti-Passback, {0} ya da {1} ve {2} ya da {3}
acc_apb_fourDoor3=Anti-Passback, {0} ve {1} ya da {2} arasında
acc_apb_fourDoor4=Anti-Passback, {0} ve {1} ya da {2} ya da {3} arasında
acc_apb_hasBeenSet=Anti-PassBack Kuruldu
acc_apb_conflictWithGapb=Bu cihazda Global Ant-Passback ayarları vardır, değiştirilemez !
acc_apb_conflictWithApb=Cihazın alanında Anti-Passback ayarları vardır, ve düzenleme yapılamaz!
acc_apb_conflictWithEntranceApb=Alandaki cihaza Anti-Passback ayarları girilmiş, değişiklik yapılamaz 
acc_apb_controlIn=Anti-PassBack Giriş
acc_apb_controlOut=Anti-Passback Çıkıi
acc_apb_controlInOut=Anti-Passback Giriş/Çıkış
acc_apb_groupIn=Grupa katılın
acc_apb_groupOut=Grup dışında.
acc_apb_reverseName=Dönüş denizci
acc_apb_door=Deniz denizcilerine karşı kapı
acc_apb_readerHead=Başını denizcilere karşı koymak için okuyorum.
acc_apb_alreadyExists=The same anti submarine rule already exists, please do not add it again!
#[17]电子地图
acc_map_addDoor=Kapı Ekle
acc_map_addChannel=Kamera Ekle
acc_map_noAccess=Elektronik Haritaya erişiminiz yok, Lütfen Yönetici Operatör ile iletişime geçinz.
acc_map_noAreaAccess=Bu alan için e-harita iznine sahip değilsiniz, lütfen yöneticiyle iletişime geçin!
acc_map_imgSizeError=Lütfen boyutu {0} M'den küçük olan resmi yükleyin!
#[18]门禁事件记录
acc_trans_entity=Hareketler
acc_trans_eventType=Olay Tipi
acc_trans_firmwareEvent=Firmware Olayları
acc_trans_softwareEvent=Yazılım Olayı
acc_trans_today=Günlük Olaylar
acc_trans_lastAddr=Bilinen Son Pozisyon
acc_trans_viewPhotos=Resimlere Görüntüle
acc_trans_exportPhoto=Çıkan Resimler
acc_trans_dayNumber=Günler
acc_trans_photo=Erişim Kontrol Olay Fotoğrafı
acc_trans_fileIsTooLarge=Dışa aktarılan dosya çok büyük, lütfen dışa aktarmak için kapsamı azaltın
#[19]门禁验证方式
acc_verify_mode_onlyface=Yüz
acc_verify_mode_facefp=Yüz+Parmakizi
acc_verify_mode_facepwd=Yüz+Şifre
acc_verify_mode_facecard=Yüz+Kart
acc_verify_mode_facefpcard=Yüz+Parmakizi+Kart
acc_verify_mode_facefppwd=Yüz+Parmak izi+Şifre
acc_verify_mode_fv=Parmak Damar
acc_verify_mode_fvpwd=Parmak Damar+Şifre
acc_verify_mode_fvcard=Parmak Damar+Kart
acc_verify_mode_fvpwdcard=Parmak Damar+Şifre+Kart
acc_verify_mode_pv=Avuç içi Damar
acc_verify_mode_pvcard=Avuçiçi Damar+Kart
acc_verify_mode_pvface=Avuç içi damar+Yük
acc_verify_mode_pvfp=Avuç içi Damar+Parmakizi
acc_verify_mode_pvfacefp=Avuç Damar+Yüz+Parmak izi
#[20]门禁事件编号
acc_eventNo_-1=Yok
acc_eventNo_0=Normal Kart Okutarak Açma
acc_eventNo_1=Geçiş Modu Zaman Diliminde Kart Okutma
acc_eventNo_2=Çift-Personel Normal Açma(Kart Okutarak)
acc_eventNo_3=Çift-Personel K.Açma(Kart Okutarak)
acc_eventNo_4=Acil Durum Şifresi
acc_eventNo_5=Geçiş Modu Saat Diliminde Açma
acc_eventNo_6=Tetikleme Olayı Gerçekleşti
acc_eventNo_7=Alarm Kapat
acc_eventNo_8=Uzaktan Açma
acc_eventNo_9=Uzaktan Kapatma
acc_eventNo_10=Gün içi Geçiş Mod Zaman Dilimi DevreDışı
acc_eventNo_11=Gün içi Geçiş Mod Zaman Dilimi Aktif
acc_eventNo_12=Aux Çıkış Uzaktan Açma
acc_eventNo_13=Aux Çıkış Uzaktan Kapama
acc_eventNo_14=Normal Parmak izi ile Kapı açma
acc_eventNo_15=Çoklı-Personel ile K.Açma(Parmak izi)
acc_eventNo_16=Geçiş Modu Zaman Diliminde Parmak Basma
acc_eventNo_17=Kart + Parmak izi Açma
acc_eventNo_18=İlk-Personel Normal Açma (Parmakizi)
acc_eventNo_19=İlk-Personel Normal Açma (Kart ve Parmakizi)
acc_eventNo_20=İşlem Aralığı çok Kısa
acc_eventNo_21=Kapı Geçersiz Zaman Dilimi (Kart Okutma)
acc_eventNo_22=Geçersiz Zaman Dilimi
acc_eventNo_23=Geçiş Reddedildi.
acc_eventNo_24=Anti-Passback
acc_eventNo_25=Interlock
acc_eventNo_26=Çoklu-Personel Doğrulama (Kart Okutma)
acc_eventNo_27=Devre Dışı Kart
acc_eventNo_28=Kapı Açık Zaman Aşımı
acc_eventNo_29=Zamanı Dolu Kart
acc_eventNo_30=Hatalı Şifre
acc_eventNo_31=Parmak izi Okutma Aralığı çok Kıasa
acc_eventNo_32=Çoklu-Personel Doğrulama (Parmakizi)
acc_eventNo_33=Zamanı Dolu Parmak izi
acc_eventNo_34=Parmak izi Devre Dışı
acc_eventNo_35=Kapıda Etkin Olmayan Zaman Dilimi (Parmak izi)
acc_eventNo_36=Kapıda Etkin Olmayan Zaman Dilimi (Çıkış Butonu)
acc_eventNo_37=Geçiş Modu Zaman Dilimi kapatma başarısız
acc_eventNo_38=Kart Kayıp Bildirimi
acc_eventNo_39=Erişim Kapalı
acc_eventNo_40=Çoklu-Personel Doğrulama Başarılı (Parmak izi)
acc_eventNo_41=Doğrulama Modu Hatası
acc_eventNo_42=Wieagand Format Hatası
acc_eventNo_43=Anti-Passback Doğrulama Zaman Aşımı
acc_eventNo_44=Arkaplan Doğrulama Başarısız
acc_eventNo_45=Arkaplan Doğrulama Zaman Aşımı
acc_eventNo_47=Komut Gönderme Başarısız
acc_eventNo_48=Çoklu-Personel Doğrulama Başarısız(Kart Okutma)
acc_eventNo_49=Geçerli Olmayan Zaman Aralığı (Şifre)
acc_eventNo_50=Şifre Giriş Aralığı çok Kısa
acc_eventNo_51=Çoklu-Personel Doğrulaması (Şifre)
acc_eventNo_52=Çoklu-Personel Doğrulaması Başarısız(Şifre)
acc_eventNo_53=Zamanı Dolu Şifre
acc_eventNo_100=Sabotaj Alarmı
acc_eventNo_101=Panik Şifresi ile Açıldı
acc_eventNo_102=Kapı Zorla Açıldı
acc_eventNo_103=Panik Parmak izi Kapı Açma
acc_eventNo_200=Kapı Doğrudan Açıldı
acc_eventNo_201=Kapı Doğrudan Kapandı
acc_eventNo_202=Çıkış Buton ile açma
acc_eventNo_203=Çoklu-Personel Açma (Kart+Parmakizi)
acc_eventNo_204=Geçiş Modu Zaman Dilim Aşımı
acc_eventNo_205=Uzaktan Normal Açma
acc_eventNo_206=Cİhaz başlatıldı
acc_eventNo_207=Şifre ile Açma
acc_eventNo_208=Süper Kullanıcı kapı açma
acc_eventNo_209=Çıkış Butonu tetiklendir (Kilitli Olmadan)
acc_eventNo_210=Yangın kapısını başlat
acc_eventNo_211=Süper Kullanıcı Kapılar Kapalı
acc_eventNo_212=Asansör kontrol fonksiyonu Aktif
acc_eventNo_213=Asansör kontrol fonksiyonu devre dışı
acc_eventNo_214=Çoklu-Personel K.Açma (Şifre)
acc_eventNo_215=İlk-Personel Normal Açma (Şifre)
acc_eventNo_216=Geçiş Zaman Dilim Mod Şifresi
acc_eventNo_220=Aux Giriş AçıkDevre (Açık)
acc_eventNo_221=Aux Giriş KısaDevre (Kapalı)
acc_eventNo_222=ArkaPlan Doğrulaması Başarılı
acc_eventNo_223=ArkaPlan Doğrulaması
acc_eventNo_225=Aux Giriş Normal
acc_eventNo_226=Aux Giriş Tetikleme
acc_newEventNo_0=Normal Doğrulama ile Açma
acc_newEventNo_1=Geçiçi Mod Zaman Dilim Doğrulaması
acc_newEventNo_2=İlk-Personel K.Açma
acc_newEventNo_3=Çoklu-Perseonel K.Açma
acc_newEventNo_20=İşlem Aralığı çok Kısa
acc_newEventNo_21=Geçersiz Zaman Diliminde Kapı Açma
acc_newEventNo_26=Çoklu-Personel Doğrulaması Bekleniyor
acc_newEventNo_27=Kayıtlı Olmayan Personel
acc_newEventNo_29=Süresi Dolu Personel
acc_newEventNo_30=Şifre Hatası
acc_newEventNo_41=Doğrulama Mod Hatası
acc_newEventNo_43=Personel Kilidi
acc_newEventNo_44=Arkaplan Doğrulaması Başarısız
acc_newEventNo_45=Arkaplan Doğrulama Zaman Aşımı
acc_newEventNo_48=Çoklu-Personel Doğrulaması Başarısız
acc_newEventNo_54=Batarya voltajı çok düşük
acc_newEventNo_55=Acilen bataryayı değiştirin.
acc_newEventNo_56=İllegal İşlem
acc_newEventNo_57=Yedek Güç
acc_newEventNo_58=Normal Açma Alarmı
acc_newEventNo_59=İllegal Yönetim
acc_newEventNo_60=Kapı İçeriden Kilitli
acc_newEventNo_61=Çoğaltılmış
acc_newEventNo_62=Yasaklanan Personel
acc_newEventNo_63=Kapı Kilitlendi
acc_newEventNo_64=Çıkış Buton Zaman Dilimi Devre Dışı
acc_newEventNo_65=Aux Giriş Zaman Dilimi Devre Dışı
acc_newEventNo_66=Okuyucu Güncellemesi Başarısız
acc_newEventNo_67=Uzaktan Karşılaştırma Başarılı (Cihaz Yetkilendirilmedi)
acc_newEventNo_68=Yüksek Vücut Isısı - Erişim Engellendi
acc_newEventNo_69=Maske Yok - Erişim Engellendi
acc_newEventNo_70=Yüz karşılaştırma sunucusu iletişim istisnası
acc_newEventNo_71=Yüz sunucusu düzensiz yanıt veriyor
acc_newEventNo_73=Geçersiz QR Kodu
acc_newEventNo_74=QR Kodunun Süresi Doldu
acc_newEventNo_101=Panik Açma Alarmı
acc_newEventNo_104=Geçersiz Kart Okutma Alarmı
acc_newEventNo_105=Sunucuya bağlanılamıyor
acc_newEventNo_106=Ana güç zayıf
acc_newEventNo_107=Güç Kaynağı pili zayıf
acc_newEventNo_108=Ana cihaza bağlanılamıyor
acc_newEventNo_109=Okuyucu Sabotaj Alarmı
acc_newEventNo_110=Okuyucu Çevrimdışı
acc_newEventNo_112=Eklenme tahtası devre dışı
acc_newEventNo_114=Ateş alarm ı girdi bağlantısı kesildi (satır keşfetmesi)
acc_newEventNo_115=Ateş alarm ı kısa devre girdi (devre değerlendirme)
acc_newEventNo_116=Yardımcı girdi bağlantısı kesmesi (satır keşfetmesi)
acc_newEventNo_117=Yardımcıl giriş kısa devre (satır değerlendirme)
acc_newEventNo_118=Bağlantı kesildiğinden çık (satır keşfetmesi)
acc_newEventNo_119=Çıkış değiştiricinin kısa devre (devre değerlendirmesi)
acc_newEventNo_120=Kapı manyetik bağlantısı (satır keşfetmesi)
acc_newEventNo_121=Kapı manyetik kısa devre (devre değerlendirme)
acc_newEventNo_159=Uzak Kontrol ile Kapı Açma
acc_newEventNo_214=Server'a bağlantı sağlandı
acc_newEventNo_217=Ana Cihaza bağlantı başarılı
acc_newEventNo_218=ID Kart Doğrulaması
acc_newEventNo_222=Arkaplan Doğrulaması Başarılı
acc_newEventNo_223=Arkaplan Doğrulaması
acc_newEventNo_224=Zili Çal
acc_newEventNo_227=Kapıyı iki kez aç
acc_newEventNo_228=Kapıyı iki kez kapat
acc_newEventNo_229=Aux Çıkış Zamanlı Normal Açma
acc_newEventNo_230=Aux Çıkışlarını Zamanlı Kapat
acc_newEventNo_232=Doğrulama Başarılı
acc_newEventNo_233=Kilitlemeyi Devreye Al
acc_newEventNo_234=Kilitlemeyi Devre Dışı Bırak
acc_newEventNo_235=Okuyucu Güncelleme Başarılı
acc_newEventNo_236=Okuyucu Sabotaj Alarmı Silindi
acc_newEventNo_237=Online Okuyucu
acc_newEventNo_239=Cihaz Arama
acc_newEventNo_240=Arama Sonlandırıldı
acc_newEventNo_243=Alarm girdi bağlantısı kesildi
acc_newEventNo_244=Fire alarm input short circuit
acc_newEventNo_247=Yükselme tahtası online
acc_newEventNo_4008=Şebeke kurtarma
acc_newEventNo_4014=Ateş giriş sinyali bağlantısı kesildi, sonun kapısı normalde açık
acc_newEventNo_4015=Kapı internette
acc_newEventNo_4018=Arka Uç Karşılaştırması Açık
acc_newEventNo_5023=Sınırlı ateş koruması durumu
acc_newEventNo_5024=Çok Kişisel Denetim Zaman Aşımı
acc_newEventNo_5029=Arka Uç Karşılaştırması Başarısız Oldu
acc_newEventNo_6005=Kayıt Kapasitesi üst sınıra ulaşıyor
acc_newEventNo_6006=Hat kısa devre (RS485)
acc_newEventNo_6007=Dönüşte kısa devre (Wigan)
acc_newEventNo_6011=Kapı devre dışında
acc_newEventNo_6012=Kapı boşaltma alarm ı
acc_newEventNo_6013=Ateş giriş sinyali etkilendi, kapı açık
acc_newEventNo_6015=Eklentisi cihazının güç tasarımını ayarlayın
acc_newEventNo_6016=Makine fabrikası ayarlarını geri döndür
acc_newEventNo_6070=Arka Uç Karşılaştırması (Yasaklanan Liste)
acc_eventNo_undefined=Tanımsız Olay Numarası
acc_advanceEvent_500=Global Anti-Passback(Mantıksal)
acc_advanceEvent_501=Kişi kullanılabilirliği (Tarih Kullanım)
acc_advanceEvent_502=Kontrol edilen Kişi Sayısı
acc_advanceEvent_503=Global Interlock
acc_advanceEvent_504=Rota Kontrolü
acc_advanceEvent_505=Global Anti-Passback(Zamanlı)
acc_advanceEvent_506=Global Anti-Passback(Zaman Mantığı)
acc_advanceEvent_507=Kişi kullanılabilirliği (geçerli günlerin ilk kullanımından sonra)
acc_advanceEvent_508=Kişi kullanılabilirliği (kullanım sayısı)
acc_advanceEvent_509=ArkaPlan doğrulama başarısız (Kayıtsız Personel)
acc_advanceEvent_510=Arkaplan doğrulama hatası (Özel Veri)
acc_alarmEvent_701=DMR İhlali (Ayar kurallar: {0})
#[21]实时监控
acc_rtMonitor_openDoor=Aç
acc_rtMonitor_closeDoor=Kapat
acc_rtMonitor_remoteNormalOpen=Uzaktan Normal Açma
acc_rtMonitor_realTimeEvent=Canlı-İzleme Olayları
acc_rtMonitor_photoMonitor=Fotoğraf İzleme
acc_rtMonitor_alarmMonitor=Alarm İzleme
acc_rtMonitor_doorState=Kapı Durumu
acc_rtMonitor_auxOutName=Aux Çıkış İsmi
acc_rtMonitor_nonsupport=Desteklenmiyor
acc_rtMonitor_lock=Kilitli
acc_rtMonitor_unLock=Kilitli Değil
acc_rtMonitor_disable=Devre Dışı
acc_rtMonitor_noSensor=Kapı Sensörü Yok
acc_rtMonitor_alarm=Alarm
acc_rtMonitor_openForce=Zorla Açıldı
acc_rtMonitor_tamper=Demontaj
acc_rtMonitor_duressPwdOpen=Panik Şifre ile Açma
acc_rtMonitor_duressFingerOpen=Panik Parmak izi Açma
acc_rtMonitor_duressOpen=Panik K. Açma
acc_rtMonitor_openTimeout=Kapı Açık Zaman Aşımı
acc_rtMonitor_unknown=Bilinmeyen
acc_rtMonitor_noLegalDoor=Şuan bu koşulları sağlayan kapı yok!
acc_rtMonitor_noLegalAuxOut=Hiçbir Aux Çıkış isteği karşılamıyor
acc_rtMonitor_curDevNotSupportOp=Mevcut cihaz bu işlemi desteklemiyor.
acc_rtMonitor_curNormalOpen=Geçerli normal açma
acc_rtMonitor_whetherDisableTimeZone=Kapının geçerli durumu sürekli açık olacak
acc_rtMonitor_curSystemNoDoors=Sistem herhangi bir kapıyı ekleyemedi ya da isteğinize uygun bir kapı bulamadı.
acc_rtMonitor_cancelAlarm=Alarmı iptal et
acc_rtMonitor_openAllDoor=Geçerli tüm kapıları aç
acc_rtMonitor_closeAllDoor=Tüm geçerli kapıları kapat
acc_rtMonitor_confirmCancelAlarm=Alarmı kapatmak istediğinizden emin misiniz?
acc_rtMonitor_calcelAllDoor=Tüm Alarmları Durdur
acc_rtMonitor_initDoorStateTip=Sistemin kullanıcı yetkisindeki tüm kapılar alınıyor....
acc_rtMonitor_alarmEvent=Alarm Olayı
acc_rtMonitor_ackAlarm=AlarmOnayla
acc_rtMonitor_ackAllAlarm=Tüm Alarmları Onayla
acc_rtMonitor_ackAlarmTime=Alarm Zamanını Onayla
acc_rtMonitor_sureToAckThese=Bu {0} alarmı onayladığınızdan emin misiniz? Onayınızdan sonra tüm alarmlar iptal edilecektir.
acc_rtMonitor_sureToAckAllAlarm=Tüm alarmları iptal etmek istediğinizden emin misiniz? Onayınızdan sonra tüm alarmlar iptal edilecektir.
acc_rtMonitor_noSelectAlarmEvent=Lütfen seçişlen alarm olaylarını onaylayın!
acc_rtMonitor_noAlarmEvent=Mevcut sistemde olay alarmı yok!
acc_rtMonitor_forcefully=Alarm Kapat (Kapı Zorla Açıldı)
acc_rtMonitor_addToRegPerson=Kayıtlı kişi eklendi.
acc_rtMonitor_cardExist=Bu kart {0} tarafından atandı ve tekrar verilemez.
acc_rtMonitor_opResultPrompt={0} istek gönderimi başarılı, {1} başarısız oldu.
acc_rtMonitor_doorOpFailedPrompt=Aşağıdaki kapılara istek gönderilemedi. Lütfen Tekrar Deneyin !
acc_rtMonitor_remoteOpen=Uzaktan Aç
acc_rtMonitor_remoteClose=Uzaktan Kapama
acc_rtMonitor_alarmSoundClose=Ses kapatıldı
acc_rtMonitor_alarmSoundOpen=Ses Açıldı
acc_rtMonitor_playAudio=Sesli Hatırlatıcı
acc_rtMonitor_isOpenShowPhoto=Resmi Ekranda Gösterme Fonsiyonu Aktif
acc_rtMonitor_isOpenPlayAudio=Ses Alarm Fonksiyonu Aktif
acc_rtm_open=Uzaktan Buton Açma
acc_rtm_close=Uzaktan Kapatma Butonu
acc_rtm_eleModule=Asansör
acc_cancelAlarm_fp=İptal Alarmı (PanikParmak İzi Açık)
acc_cancelAlarm_pwd=Alarm Kapat (Panik Şifresi ile Açma)
acc_cancelAlarm_timeOut=Alarmı Kapat (Kapı Açma Zaman Aşım)
#定时同步设备时间
acc_timing_syncDevTime=Cihaz Saatinin Senkronizasyon zamanı
acc_timing_executionTime=Yürütme Zamanı
acc_timing_theLifecycle=Yürütme Döngüsü
acc_timing_errorPrompt=Giriş Hatalı
acc_timing_checkedSyncTime=Lütfen Senkronizasyon zamanı seçin
#[25]门禁报表
acc_trans_hasAccLevel=Oluşturulmuş Yetki Seviyesi
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Lütfen alan ekleyin
acc_zone_code=Alan Kodu
acc_zone_parentZone=Ana Aan
acc_zone_parentZoneCode=Ana Alan Kodu
acc_zone_parentZoneName=Ana Alan İsmi
acc_zone_outside=Dışarıda
#[G2]读头定义
acc_readerDefine_readerName=Okuyucu Adı
acc_readerDefine_fromZone=den gidiyor
acc_readerDefine_toZone=den Gelen
acc_readerDefine_delInfo1=Alana tanımlanan okuycu, gelişmiş bir erişim kontrol fonksiyonu tarafından referans alınır ve silinemez!
acc_readerDefine_selReader=Okuyucu Seç
acc_readerDefine_selectReader=Lütfen okuyucu ekleyin!
acc_readerDefine_tip=Personel alandışına alındıktan sonra, personel kaydı silinir.
#[G3]全局反潜
acc_gapb_zone=Alan
acc_gapb_whenToResetGapb=Anti-Passback Süresi Sıfırla
acc_gapb_apbType=Anti-Passback Tipi
acc_gapb_logicalAPB=Mantıksal Anti-Passback
acc_gapb_timedAPB=Zamanlanmış Antİ-Passback
acc_gapb_logicalTimedAPB=Zamanlanmış Mantıksal Anti-Passback
acc_gapb_lockoutDuration=Kilitleme Süresi
acc_gapb_devOfflineRule=Cihaz Çevrimdışı olduğunda
acc_gapb_standardLevel=Standart Geçiş Yetkisi
acc_gapb_accessDenied=Geçiş Reddedildi
acc_gapb_doorControlZone=Aşağıdaki kapılar, bölgenin giriş ve çıkışını kontrol eder
acc_gapb_resetStatus=Anti-Passback Durumlarını Sıfırla
acc_gapb_obeyAPB=Anti-Passback kurallarına uyun
acc_gapb_isResetGAPB=Anti-Passback Sıfırla
acc_gapb_resetGAPBSuccess=Anti-Passback durum sıfırlaması başarılı
acc_gapb_resetGAPBFaile=Anti-Passbak durum hatalarını sıfırla
acc_gapb_chooseArea=Lütfen alanı tekrar seçin
acc_gapb_notDelInfo1=Bu bölge gelişmiş geçiş kontrole sahiptir. Silinemez !
acc_gapb_notDelInfo2=Bu bölgeye okuyucu tanımı vardır, silinemez !
acc_gapb_notDelInfo3=Bu bölgede Gelişmiş geçiş kontrol ayarlıdır. Silinemez !
acc_gapb_notDelInfo4=Erişim alanına LED ekran ayarlıdır ve silinemez !
acc_gapb_zoneNumRepeat=Kontrol Sunucu numaralarında çakışma var
acc_gapb_zoneNameRepeat=Kontrol Server isimleri kopyalandı
acc_gapb_personResetGapbPre=Sıfırlamak istediğinizden emin misiniz?
acc_gapb_personResetGapbSuffix=Personel için Anti-PassBack Kuralları?
acc_gapb_apbPrompt=Tek bir kapı nispeten bağımsız iki çevre sınırını kontrol etmek için kullanılamaz.
acc_gapb_occurApb=Anti-Passback oluştur
acc_gapb_noOpenDoor=Kapıyı Açmayan
acc_gapb_openDoor=Kapı Aç
acc_gapb_zoneNumLength=Uzunluk 20 karakterden fazla olmalı
acc_gapb_zoneNameLength=30 karakterden daha büyük uzunluk
acc_gapb_zoneRemarkLength=Uzunluk 50 karakterden fazla
acc_gapb_isAutoServerMode=Arka plan doğrulama işlevi olmadan Cihaz fonksiyonunu çalışmayabilir, Aktif edilsin mi?
acc_gapb_applyTo=Uygula
acc_gapb_allPerson=Tüm Personeller
acc_gapb_justSelected=Sadece Seçili Personel
acc_gapb_excludeSelected=Seçilmiş Personelleri Hariç Tut
#[G4]who is inside
acc_zoneInside_lastAccessTime=Son Giriş Zamanı
acc_zoneInside_lastAccessReader=Son Geçiş Okuyucusu
acc_zoneInside_noPersonInZone=Alanda olmayan personel
acc_zoneInside_noRulesInZone=Kural ayarlanmadı
acc_zoneInside_totalPeople=Toplam Kişi
acc_zonePerson_selectPerson=Lütfen personel yada departman seçiniz!
#[G5]路径
acc_route_name=Rota İsmi
acc_route_setting=Rota Ayarları
acc_route_addReader=Okuyucu Ekle
acc_route_delReader=Okuyucu Sil
acc_route_defineReaderLine=Okuyucu Tanım satırı
acc_route_up=Yukarı
acc_route_down=Aşağı
acc_route_selReader=İşlemden sonra okuyucu seçin.
acc_route_onlyOneOper=İşlem için sadece bir okuyucu seçin.
acc_route_readerOrder=Okuyucu Tanım Bölümü
acc_route_atLeastSelectOne=Lütfen en az bir okuyucu seçin!
acc_route_routeIsExist=Bu rota kullanılıyor!
#[G6]DMR
acc_dmr_residenceTime=Bekleme Süresi
acc_dmr_setting=Bekleme süresi ayarları
#[G7]Occupancy
acc_occupancy_max=Maksimum Kapasite
acc_occupancy_min=Minimum Kapasite
acc_occupancy_unlimit=Limitsiz
acc_occupancy_note=Maksimum kapasite, Minimum kapasiteden büyük olmalıdır.
acc_occupancy_containNote=Lütfen Minimum/Maksimum kapasiteden en az birini ayarlayın!
acc_occupancy_maxMinValid=LÜtfen kapasiteyi 0 dan yüksek ayarlayın
acc_occupancy_conflict=Bu alanda Kapasite Kontrol kuralı ayarlanmıştır.
acc_occupancy_maxMinTip=Boşkapasite değeri hiçbir sınırlama yok anlamına gelir.
#card availability
acc_personLimit_zonePropertyName=Bölge Özellik Adı
acc_personLimit_useType=Kullan
acc_personLimit_userDate=Geçerlilik Tarihi
acc_personLimit_useDays=İlk kullanımdan sonra geçerli günler
acc_personLimit_useTimes=Kullanım Sıklığı
acc_personLimit_setZoneProperty=Alan Özelliklerini Ayarla
acc_personLimit_zoneProperty=Alan Özellikleri
acc_personLimit_availabilityName=Kullanılabilir İsim
acc_personLimit_days=Günler
acc_personLimit_Times=Kaç Defa
acc_personLimit_noDel=Seçilen geçiş kontrol bölgesi kullanılıyor, Silinemez !
acc_personLimit_cannotEdit=Geçiş Alanı Kullanılıyor, Değiştirilemez!
acc_personLimit_detail=Detay
acc_personLimit_userDateTo=Tarihe Kadar Geçerlidir
acc_personLimit_addPersonRepeatTip=Seçilen departmanın altındaki kişi erişim kontrol alanı özelliğine eklendi, lütfen departmanı tekrar seçin!
acc_personLimit_leftTimes={0} Kez Kaldı
acc_personLimit_expired=süresi doldu
acc_personLimit_unused=Kullanılmamış
#全局互锁
acc_globalInterlock_addGroup=Grup Ekle
acc_globalInterlock_delGroup=Grup Sil
acc_globalInterlock_refuseAddGroupMessage=Ayni Interlock, Grup içindeki kapılara tekrar eklenemez
acc_globalInterlock_refuseAddlockMessage=Kilitli diğer gruplarda zaten eklenen kapılar görünüyor
acc_globalInterlock_refuseDeleteGroupMessage=Lütfen İnterlock ilişkili veriyi kaldırın
acc_globalInterlock_isGroupInterlock=Grup Interlock
acc_globalInterlock_isAddTheDoorImmediately=Hemen kapı ekleyin
acc_globalInterlock_isAddTheGroupImmediately=Hemen grup ekleyin
#门禁参数设置
acc_param_autoEventDev=Seçilen cihazların olay kayıtlarını otomatik indir.
acc_param_autoEventTime=Eşzamanlı olay aralıkları için olay kayıtlarını otomatik olarak indir
acc_param_noRepeat=Aynı e-posta adresi girilemez, Lütfen tekrar deneyin.
acc_param_most18=18 den yukarı Mail adresi ekle
acc_param_deleteAlert=Tüm e-posta adres giriş kutusu silinemiyor.
acc_param_invalidOrRepeat=E-posta adresi biçim hatası veya e-posta adresi tekrarı.
#全局联动
acc_globalLinkage_noSupport=Seçili olan kapı Kilitleme özelliğini desteklemiyor ve kilitleme fonksiyonu devre dışı.
acc_globalLinkage_trigger=Global Tetikleme Etkinleştirme
acc_globalLinkage_noAddPerson=Tetikleme kuralı sadece tek personel için değildir. Tetikleme tüm personeller için geçerlidir.
acc_globalLinkage_selectAtLeastOne=Lütfen en az bir tetikleme çıkış noktası ve video bağlantısı seçin!
acc_globalLinkage_selectTrigger=Lütfen tetikleyici koşullarını ekleyin!
acc_globalLinkage_selectInput=Lütfen giriş nokrası seçiniz!
acc_globalLinkage_selectOutput=Lütfen çıkış noktası ekleyin!
acc_globalLinkage_audioRemind=Tetikleme Ses Yönlendirme
acc_globalLinkage_audio=Tetikleme Sesi
acc_globalLinkage_isApplyToAll=Tüm personele uygula
acc_globalLinkage_scope=Personel Aralığı
acc_globalLinkage_everyPerson=Herhangi
acc_globalLinkage_selectedPerson=Seçildi
acc_globalLinkage_noSupportPerson=Desteklemiyor
acc_globalLinkage_reselectInput=Tetikleyici ayar türü değişti, lütfen giriş noktasını tekrar seçin!
acc_globalLinkage_addPushDevice=Lütfen bu özelliği destekleyen bir cihaz ekleyin!
#其他
acc_InputMethod_tips=Lütfen İngilizce giriş moduna geçin.
acc_device_systemCheckTip=Geçiş cihazı bulunmuyor!
acc_notReturnMsg=Dönen Bilgi yok
acc_validity_period=Lisans süresi doldu, işlev çalışamaz.
acc_device_pushMaxCount=Sistem {0} cihazında zaten var, lisans sınırına ulaştı, cihazı ekleyemiyor!
acc_device_videoHardwareLinkage=Donanımsal Video Tetikleme Ayarı
acc_device_videoCameraIP=Video Kamera IP
acc_device_videoCameraPort=Video Kamera Portu
acc_location_unable=Olay noktası elektronik haritaya eklenmedi, Konum bulunamadı.
acc_device_wgDevMaxCount=Sistemdeki cihaz lisans sınırına ulaşıldı ve değiştirilemiyor.
#自定义报警事件
acc_deviceEvent_selectSound=Lütfen ses dosyası seçin
acc_deviceEvent_batchSetSoundErr=Ayarlanan alarm sesleri anormal
acc_deviceEvent_batchSet=Ses Ayarı
acc_deviceEvent_sound=Olay Sesi
acc_deviceEvent_exist=Zaten Var
acc_deviceEvent_upload=Yükle
#查询门最近发生事件
acc_doorEventLatestHappen=Kapıdaki son olayları sorgula
#门禁人员信息
acc_pers_delayPassage=Geçiş Gecikmesi
#设备容量提示
acc_dev_usageConfirm=Ekipmanın akım kapasitesi %90'ı aştı
acc_dev_immediateCheck=Hemen Kontrol Et
acc_dev_inSoftware=Yazılımda
acc_dev_inFirmware=Firmware
acc_dev_get=Al
acc_dev_getAll=Tümünü Al
acc_dev_loadError=Yükleme Hatası
#Reader
acc_reader_inout=Giriş/Çıkış
acc_reader_lightRule=Işık Kuralları
acc_reader_defLightRule=Varsayılan Kural
acc_reader_encrypt=Şifreli
acc_reader_allReaderOfCurDev=Mecvut Cihazın Tüm Okuyucuları
acc_reader_tip1=Şifreleme, mevcut cihazdaki tüm okuyuculara kopyalanır !
acc_reader_tip2=ID modu seçeneği yalnızca bu özelliği destekleyen okuyucular için kullanılabilir!
acc_reader_tip3=RS485 protokol tipi, mevcut cihazdaki tüm okuyuculara kopyalanır. Ayarlar, cihaz yeniden başlatıldıktan sonra geçerli olacaktır!
acc_reader_tip4=Bazı kişisel bilgileri gizleme seçeneği varsayılan olarak aynı cihazın tüm okuyucularına kopyalanır!
acc_reader_commType=Haberleşme Tipi
acc_reader_commAddress=Haberleşme Adresi
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=DevreDışı
acc_readerComAddress_repeat=Haberlşme adresi mevcut
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=RS485 Adresi
acc_readerCommType_wgAddress=Wiegand Adresi
acc_reader_macError=Lütfen doğru formatta MAC adresini girin!
acc_reader_machineType=Okuyucu Tipi
acc_reader_readMode=Mod
acc_reader_readMode_normal=Normal Mod
acc_reader_readMode_idCard=ID Kart Modu
acc_reader_note=İpucu: Geçiş Kontrol cihazları için aynı alandaki ({0}) kamera seçilebilir.
acc_reader_rs485Type=RS485 Protokol Tipi
acc_reader_userLock=Personel Geçiş Kilidi
acc_reader_userInfoReveal=Gizli Personel Bilgileri
#operat
acc_operation_pwd=İşlem Şifresi
acc_operation_pwd_error=Şifre Hatalı
acc_new_input_not_same=Yeni anahtar girişi tutarlı değil
acc_op_set_keyword=Lisans Anahtar ayarları
acc_op_old_key=Eski Anahtar
acc_op_new_key=Yeni Anahtar
acc_op_cofirm_key=Doğrulama Anahtarı
acc_op_old_key_error=Eski şifre hatası
#验证方式规则
acc_verifyRule_name=Kural İsmi
acc_verifyRule_door=Kapı Doğrulaması
acc_verifyRule_person=Personel Doğrulaması
acc_verifyRule_copy=Pazartesi Ayarlarını Diğer Haftalara Kopyala
acc_verifyRule_tip1=Lütfen en az bir doğrulama modu seçin!
acc_verifyRule_tip2=Kural bir kişinin doğrulama modunu içeriyorsa, sadece bir wiegand okuyucu ile bir kapı ekleyebilirsiniz!
acc_verifyRule_tip3=RS485 okuyucu sadece kapı doğrulama modunu takip edebilir, personel doğrulama modunu desteklemez.
acc_verifyRule_oldVerifyMode=Eski Doğrulama Modu
acc_verifyRule_newVerifyMode=Yeni Doğrulama Modu
acc_verifyRule_newVerifyModeSelectTitle=Yeni Doğrulama Modu Seçin
acc_verifyRule_newVerifyModeNoSupportTip=Yeni Doğrulama Tipini destekleyen cihazı yok!
#Wiegand Test
acc_wiegand_beforeCard=Yeni kart ({0} bit) boyu, önceki karta eşit değildir!
acc_wiegand_curentCount=Geçerli Kart Numara Uzunluğu : {0} Bit
acc_wiegand_card=Kart
acc_wiegand_readCard=Kart Okuma
acc_wiegand_clearCardInfo=Kart Bilgisi Temizle
acc_wiegand_originalCard=Orjinal Kart Numarası
acc_wiegand_recommendFmt=Önerilen Kart Formati
acc_wiegand_parityFmt=Tek-Çift Parite Formatı
acc_wiegand_withSizeCode=Alan kodu boş bırakılırsa,bu alanı otomatik doldur.
acc_wiegand_tip1=Bu kartlar aynı partiye ait olmayabilir.
acc_wiegand_tip2=Site kodu: {0}, Kart numarası: {1}, orijinal kart ile eşleşmiyor. Tekrar Girilen site kodu ve kart numarasını kontrol edin!
acc_wiegand_tip3=Girilen kart numarası ({0}) orijinal kartı numarasını uyumlu değil. Lütfen tekrar kontrol edin!
acc_wiegand_tip4=giriş yapılmış site kodu ({0}) orijinal kartı numarasını uyumlu olamaz. Lütfen tekrar kontrol edin!
acc_wiegand_tip5=Bu özelliği kullanmak istiyorsanız, tüm site kodu sütunları boş tutun!
acc_wiegand_warnInfo1=Yeni bir kart okumaya devam ettiğinde, elle sonraki karta geçiş yapın.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Personel  Giriş/Çıkış Ekranı
acc_LCDRTMonitor_current=Geçerli Personel Bilgisi
acc_LCDRTMonitor_previous=Önceki Personel Bilgisi
#api
acc_api_levelIdNotNull=Geçiş Yetki ID si boş bırakılamaz.
acc_api_levelExist=Bir izin grupu bulundu.
acc_api_levelNotExist=Geçiş Yetkisi bulunamadı.
acc_api_areaNameNotNull=Bölge boş olamaz
acc_api_levelNotHasPerson=Geçiş yetkisinde kişi yok.
acc_api_doorIdNotNull=Kapı ID boş bırakılamaz
acc_api_doorNameNotNull=Kapı ismi boş olamaz
acc_api_doorIntervalSize=Kapının açık kalma aralığı 1-254
acc_api_doorNotExist=Kapı mevcut değil
acc_api_devOffline=Cihaz Çevrim Dışı ya da Devre Dışı
acc_api_devSnNotNull=Cihaz SN boş olamaz
acc_api_timesTampNotNull=Zaman işareti boş olamaz.
acc_api_openingTimeCannotBeNull=Kapı açılma süresi boş olamaz
acc_api_parameterValueCannotBeNull=Parametre değeri boş olamaz
acc_api_deviceNumberDoesNotExist=Cihaz seri numarası mevcut değil
acc_api_readerIdCannotBeNull=Okuyucu kimliği boş olamaz
acc_api_theReaderDoesNotExist=Okuyucu mevcut değil
acc_operate_door_notInValidDate=Şu anda uzaktan açılış etkin süresi içinde değil, gerekirse yönetici ile irtibata geçiniz!
acc_api_doorOffline=Kapı çevrimdışı veya devre dışı
#门禁信息自动导出
acc_autoExport_title=Kayıtları Otomatik Dışa Aktar
acc_autoExport_frequencyTitle=Oto-Dışa Aktarım Frekansı
acc_autoExport_frequencyDay=Günlük
acc_autoExport_frequencyMonth=Aylık
acc_autoExport_firstDayMonth=Ayın ilk Günü
acc_autoExport_specificDate=Özel Tarih
acc_autoExport_exportModeTitle=Çıkartma Modu
acc_autoExport_dailyMode=Günlük Kayıtlar
acc_autoExport_monthlyMode=Aylık İşlemler (Geçen ay ile bu ay arasındaki tüm işlemler)
acc_autoExport_allMode=Tüm Veriler (30000 parçaya kadar veri aktarma)
acc_autoExport_recipientMail=Alıcının Posta Kutusu
#First In And Last Out
acc_inOut_inReaderName=İlk Okuyucu Adı
acc_inOut_firstInTime=İlk Zaman
acc_inOut_outReaderName=Son Çıkan Okuyucu Adı
acc_inOut_lastOutTime=Son Çıkış Zamanı
# 防疫 参数
acc_dev_setHep=Maske ve Sıcaklık Algılama Parametrelerini Ayarlama
acc_dev_enableIRTempDetection=Kızılötesi ile Sıcaklık Taramasını Etkinleştir
acc_dev_enableNormalIRTempPass=Sıcaklık Aralığın Üzerinde Olduğunda Erişimi Reddet
acc_dev_enableMaskDetection=Maske Algılamayı Etkinleştir
acc_dev_enableWearMaskPass=Maske Olmadan Erişimi Reddet
acc_dev_tempHighThreshold=Yüksek Sıcaklık Alarm Eşiği
acc_dev_tempUnit=Sıcaklık Birimi
acc_dev_tempCorrection=Sıcaklık Sapması Düzeltmesi
acc_dev_enableUnregisterPass=Kayıtlı Olmayan Kişilerin Erişimine İzin Ver
acc_dev_enableTriggerAlarm=Harici Alarmı Tetikle
# 联动 邮件
acc_mail_temperature=Vücut sıcaklığı
acc_mail_mask=Maskenin takılıp takılmayacağı
acc_mail_unmeasured=Ölçülmemiş
#Digifort 联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort Global Olayları
acc_digifort_chooseDigifortEvents=Digifort Global Olaylarını Seçin
acc_digifort_eventExpiredTip=Global olay Digifort sunucusundan silinirse, kırmızı renkte olacaktır.
acc_digifort_checkConnection=Lütfen Digifort sunucusunun bağlantı bilgilerinin doğru olup olmadığını kontrol edin.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Kişileri ekleyin
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
# 扩展 参数
acc_dev_setExtendParam=Genişletilmiş Parametreleri Ayarla
acc_extendParam_faceUI=Arayüz görüntüsü
acc_extendParam_faceParam=Yüz parametreleri
acc_extendParam_accParam=Erişim kontrol parametreleri
acc_extendParam_intercomParam=Görsel interom parametreleri
acc_extendParam_volume=Hacim
acc_extendParam_identInterval=Tanımlama aralığı (ms)
acc_extendParam_historyVerifyResult=Geçmiş doğrulama sonuçlarını görüntüle
acc_extendParam_macAddress=MAC adresini görüntüle
acc_extendParam_showIp=IP adresini göster
acc_extendParam_24HourFormat=24 saatlik biçimi göster
acc_extendParam_dateFormat=Tarih biçimi
acc_extendParam_1NThreshold=1: N eşiği
acc_extendParam_facePitchAngle=Yüzün eğim açısı
acc_extendParam_faceRotationAngle=Yüz döndürme açısı
acc_extendParam_imageQuality=Görüntü kalitesi
acc_extendParam_miniFacePixel=Minimum yüz pikseli
acc_extendParam_biopsy=Biyopsiyi etkinleştir
acc_extendParam_showThermalImage=Termal görüntüyü göster
acc_extendParam_attributeAnalysis=Öznitelik analizini etkinleştir
acc_extendParam_temperatureAttribute=Sıcaklık algılama özelliği
acc_extendParam_maskAttribute=Maske algılama özelliği
acc_extendParam_minTemperature=Minimum sıcaklık
acc_extendParam_maxTemperature=Maksimum sıcaklık
acc_extendParam_gateMode=Kapı Modu
acc_extendParam_qrcodeEnable=QR kod özelliğini aç
# 可视 对讲
acc_dev_intercomServer=Görsel interom servisi adresi
acc_dev_intercomPort=Görsel interom sunucusu
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Senkronizasyon Düzeyi
# 夏令时名称
acc_dsTimeUtc_none=Ayarlanmış
acc_dsTimeUtc_AreaNone=Bu bölgede güneş ışığı kurtarma zamanı yok.
acc_dsTimeUtc1000_0=Canberra, Melbourne, Sydney
acc_dsTimeUtc1000_1=Hobart
acc_dsTimeUtc_0330_0=Newfoundland
acc_dsTimeUtc_1000_0=Aleutik Adaları
acc_dsTimeUtc_0200_0=Atlantik Orta - kullanılan
acc_dsTimeUtc0930_0=Adelaide
acc_dsTimeUtc_0100_0=Azores
acc_dsTimeUtc_0400_0=Atlantic Time (Canada)Australia
acc_dsTimeUtc_0400_1=Santiago
acc_dsTimeUtc_0400_2=Asuncion
acc_dsTimeUtc_0300_0=Greenland
acc_dsTimeUtc_0300_1=Saint Pierre ve Miquelon Adaları
acc_dsTimeUtc0200_0=Chisinau
acc_dsTimeUtc0200_1=Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius
acc_dsTimeUtc0200_2=Atina, Bucharest
acc_dsTimeUtc0200_3=Yerusalem
acc_dsTimeUtc0200_4=Amman
acc_dsTimeUtc0200_5=Beirut
acc_dsTimeUtc0200_6=Damascus
acc_dsTimeUtc0200_7=Gaza, Hebron
acc_dsTimeUtc0200_8=Juba
acc_dsTimeUtc_0600_0=Orta Zaman (ABD ve Kanada)
acc_dsTimeUtc_0600_1=Guadalajara, Mexico City, Monterrey
acc_dsTimeUtc_0600_2=Easter Island
acc_dsTimeUtc1300_0=Samoa Adaları
acc_dsTimeUtc_0500_0=Havana
acc_dsTimeUtc_0500_1=Doğu Zaman (Birleşik Devletler ve Kanada)caribbean. kgm
acc_dsTimeUtc_0500_2=Haiti
acc_dsTimeUtc_0500_3=Indiana (Doğu)
acc_dsTimeUtc_0500_4=Turks ve Caicos Adaları
acc_dsTimeUtc_0800_0=Pacific Time (USA ve Kanada)oceania. kgm
acc_dsTimeUtc_0800_1=Baja California
acc_dsTimeUtc0330_0=Teheran veya Tehran
acc_dsTimeUtc0000_0=Dublin, Edinburgh, Lisbon, London
acc_dsTimeUtc1200_0=Fiji
acc_dsTimeUtc1200_1=Petropavlovsk Kamchatka old
acc_dsTimeUtc1200_2=Wellington, Auckland
acc_dsTimeUtc1100_0=Norfolk Adası
acc_dsTimeUtc_0700_0=Chihuahua, La Paz, Mazatlan
acc_dsTimeUtc_0700_1=Dağ zamanı (ABD ve Kanada) caribbean. kgm
acc_dsTimeUtc0100_0=Belgrad, Bratislava, Budapest, Ljubljana, Prag
acc_dsTimeUtc0100_1=Sarajevo, Skopje, Warsaw, Zagreb
acc_dsTimeUtc0100_2=Casablanca
acc_dsTimeUtc0100_3=Brussels, Copenhagen, Madrid, Paris
acc_dsTimeUtc0100_4=Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna
acc_dsTimeUtc_0900_0=Alaska
#安全点(muster point)
acc_leftMenu_accMusterPoint=Toplanma Noktası
acc_musterPoint_activate=Etkinleştir
acc_musterPoint_addDept=Departman Ekle
acc_musterPoint_delDept=Departmanı Sil
acc_musterPoint_report=Toplanma Noktası Raporu
acc_musterPointReport_sign=Manuel Olarak Oturum Açın
acc_musterPointReport_generate=Rapor Oluştur
acc_musterPoint_addSignPoint=İşaret Noktası Ekle
acc_musterPoint_delSignPoint=İşaret Noktasını Sil
acc_musterPoint_selectSignPoint=Lütfen bir işaret noktası ekleyin!
acc_musterPoint_signPoint=İşaret Noktası
acc_musterPoint_delFailTip=Zaten etkinleştirilmiş toplanma noktaları var ve silinemez!
acc_musterPointReport_enterTime=Zaman Girin
acc_musterPointReport_dataAnalysis=Veri Analizi
acc_musterPointReport_safe=Güvenli
acc_musterPointReport_danger=Tehlike
acc_musterPointReport_signInManually=manuel delme
acc_musterPoint_editTip=Toplanma noktası aktif ve düzenlenemez!
acc_musterPointEmail_total=Beklenmiş katılış:
acc_musterPointEmail_safe=İçeri kontrol edildi (güvenli):
acc_musterPointEmail_dangerous=Tehlikede.
acc_musterPoint_messageNotification=Etkinleştirmede mesaj bildirimi
acc_musterPointReport_sendEmail=Planlandırılmış baskı raporu
acc_musterPointReport_sendInterval=Gönderme Aralığı
acc_musterPointReport_sendTip=Lütfen seçilen bildirim yönteminin başarıyla ayarlanmasını sağlayın, yoksa bildirim normalde gönderilmez!
acc_musterPoint_mailSubject=Acil toplantı haberi
acc_musterPoint_mailContent=Lütfen hemen "{0}" adresinde toplanın ve "{1}" cihazında oturum açın, teşekkürler!
acc_musterPointReport_mailHead=Merhaba, bu acil bir rapor. Lütfen inceleyin.
acc_musterPoint_visitorsStatistics=Ziyaretçilerin istatistikleri
# 报警监控
acc_alarm_priority=Öncelik
acc_alarm_total=Toplamda
acc_alarm_today=Bugünün Rekoru
acc_alarm_unhandled=Onaylanmadı
acc_alarm_inProcess=İşleniyor
acc_alarm_acknowledged=Onaylandı
acc_alarm_top5=En önemli 5 alarm olayı
acc_alarm_monitoringTime=İzleme zamanı
acc_alarm_history=Alarm İşleme Geçmişi
acc_alarm_acknowledgement=Kayıtları İşliyor
acc_alarm_eventDescription=Etkinlik Ayrıntıları
acc_alarm_acknowledgeText=Seçimden sonra, alarm olay ayrıntıları e-postası atanan posta kutusuna gönderilecektir.
acc_alarm_emailSubject=İşleme Kaydı Ekle
acc_alarm_mute=Sessiz
acc_alarm_suspend=Askıya al
acc_alarm_confirmed=Etkinlik onaylandı.
acc_alarm_list=Alarm Günlüğü
#ntp
acc_device_setNTPService=NTP sunucu ayarları
acc_device_setNTPServiceTip=virgül (,) veya noktalı virgül (;) ile ayırarak birden çok sunucu adresi girin
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=İrişim denetleyicisi operasyonunda, bir Super kullanıcı zaman bölgelerinde, geçiş önünde ve karıştırıcı kuralları tarafından sınırlanmıyor ve çok yüksek kapı a çma önceliği vardır.
acc_editPerson_delayPassageTip=İçeri noktalarından personel için bekleme zamanı uzat. Fiziksel şekilde mücadele edilebilir ya da diğer hastalıklarla ilgili insanlar için yeterli.
acc_editPerson_disabledTip=Kişinin erişim seviyesini geçici olarak etkisizleştir.
#门禁向导
acc_guide_title=Erişim Kontrol Modülü Kurulum Sihirbazı
acc_guide_addPersonTip=Kişiyi ve ilgili kimlik bilgilerini (yüz veya parmak izi veya kart veya avuç içi veya şifre) eklemeniz gerekir; zaten eklediyseniz, doğrudan bu adımı atlayın
acc_guide_timesegTip=Lütfen geçerli bir açılış dönemi yapılandırın
acc_guide_addDeviceTip=Lütfen ilgili cihazı erişim noktası olarak ekleyin
acc_guide_addLevelTip=Erişim kontrol düzeyi ekle
acc_guide_personLevelTip=Kişiye ilgili erişim kontrol yetkisini atayın
acc_guide_rtMonitorTip=Erişim kontrol kayıtlarını gerçek zamanlı olarak kontrol edin
acc_guide_rtMonitorTip2=Bölgeyi ekledikten sonra erişim kontrol kayıtlarını gerçek zamanlı görüntüle
#查看区域内人员
acc_zonePerson_cleanCount=Giren ve ayrılan kişilerin istatistiklerini temizle
acc_zonePerson_inCount=Giren kişi sayısı istatistikleri
acc_zonePerson_outCount=Ayrılan kişi sayısı istatistikleri
#biocv460
acc_device_validFail=Kullanıcı adı veya şifre yanlıştır ve doğrulama başarısız olur!
acc_device_pwdRequired=Sadece 6 büyük sayı girebilir