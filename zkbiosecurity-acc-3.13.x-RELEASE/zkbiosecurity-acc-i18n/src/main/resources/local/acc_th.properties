#[1]左侧菜单
acc_module=ควบคุมประตู
acc_leftMenu_accDev=อุปกรณ์ควบคุมประตู
acc_leftMenu_auxOut=เอาท์พุทเสริม
acc_leftMenu_dSTime=DST
acc_leftMenu_access=ควบคุมการเข้า-ออก
acc_leftMenu_door=ประตู
acc_leftMenu_accRule=ข้อกำหนดการควบคุม
acc_leftMenu_interlock=อินเตอร์ล็อค
acc_leftMenu_antiPassback=Anti-Passback
acc_leftMenu_globalLinkage=โกบอลลิ้งเอจ
acc_leftMenu_firstOpen=คนแรกที่เปิดประตูแบบปกติ
acc_leftMenu_combOpen=หลายคนเปิดประตู
acc_leftMenu_personGroup=หลายคนในกลุ่ม
acc_leftMenu_level=ระดับการควบคุม
acc_leftMenu_electronicMap=แผนที่
acc_leftMenu_personnelAccessLevels=ระดับการควบคุมพนักงาน
acc_leftMenu_searchByLevel=โดยระดับการควบคุม
acc_leftMenu_searchByDoor=สิทธิในการเข้าถึงประตู
acc_leftMenu_expertGuard=ฟังก์ชันขั้นสูง
acc_leftMenu_zone=โซน
acc_leftMenu_readerDefine=กำหนดหัวอ่าน
acc_leftMenu_gapbSet=โกบอล Anti-passback
acc_leftMenu_whoIsInside=ตรวจดูบุคคลภายในพื้นที่
acc_leftMenu_whatRulesInside=กฎระเบียบด้านใน
acc_leftMenu_occupancy=ควบคุมการเข้า
acc_leftMenu_route=ควบคุมเส้นทาง
acc_leftMenu_globalInterlock=โกบอลอินเตอร์ล็อค
acc_leftMeue_globalInterlockGroup=โกบอลกลุ่มอินเตอร์ล็อค
acc_leftMenu_dmr=กฎการปิด
acc_leftMenu_personLimit=ความพร้อมของพนักงาน
acc_leftMenu_verifyModeRule=โหมดการตรวจสอบ
acc_leftMenu_verifyModeRulePersonGroup=โหมดการตรวจสอบแบบกลุ่ม
acc_leftMenu_extDev=บอร์ด I/O
acc_leftMenu_firstInLastOut=เวลาเข้าแรกสุดและเวลาออกหลังสุด
acc_leftMenu_accReports=รายงานการควบคุมการเข้าถึง
#[3]门禁时间段
acc_timeSeg_entity=โซนเวลา
acc_timeSeg_canNotDel=ช่วงเวลาใช้งานอยู่และไม่สามารถลบได้!
#[4]门禁设备--公共的在common中
acc_common_ruleName=ชื่อกฎ
acc_common_hasBeanSet=ได้รับการตั้งค่าเเล้ว
acc_common_notSet=ไม่ได้ตั้งค่า
acc_common_hasBeenOpened=ได้รับการเปิดเเล้ว
acc_common_notOpened=ไม่ได้เปิด
acc_common_partSet=เป็นส่วนหนึ่งของการตั้งค่า
acc_common_linkageAndApbTip=ลิ้งเอจและโกบอลลิ้งเอจ  anti-passback และโกบอล anti-passback จะถูกตั้งค่าในเวลาเดียวกัน อาจมีความขัดแย้ง
acc_common_vidlinkageTip=กรุณาตรวจสอบว่าจุดการป้อนข้อมูลลิ้งเอจได้ผูกกับช่องวิดีโอที่สามารถใช้ได้ มิฉะนั้นฟังก์ชันวิดีโอลิ้งเอจจะไม่ทำงาน!
acc_common_accZoneFromTo=ไม่สามารถตั้งค่าโซนเดียวกันได้
acc_common_logEventNumber=รหัสเหตุการณ์
acc_common_bindOrUnbindChannel=การผูกมัด / ไม่ยึดกล้อง
acc_common_boundChannel=กล้องที่ถูกผูกไว้
#设备信息
acc_dev_iconType=ประเภทไอคอน
acc_dev_carGate=ไม้กั้นรถ
acc_dev_channelGate=ทางเดิน
acc_dev_acpType=ประเภทแผงควบคุม
acc_dev_oneDoorACP=แผงควบคุมการเข้า-ออก 1 ประตู
acc_dev_twoDoorACP=แผงควบคุมการเข้า-ออก 2 ประตู
acc_dev_fourDoorACP=แผงควบคุมการเข้า-ออก 4 ประตู
acc_dev_onDoorACD=อุปกรณ์สแตนอโลน
acc_dev_switchToTwoDoorTwoWay=ประตูเปิดได้สองทาง
acc_dev_addDevConfirm2=หมายเหตุ: การเชื่อมต่ออุปกรณ์สำเร็จ แต่ประเภทของแผงควบคุมการเข้า-ออกแตกต่างจากที่เกิดขึ้นจริง แก้ไขไปยังแผงควบคุม{0}ประตู(s) ต้องการทำรายการเพิ่ม?
acc_dev_addDevConfirm4=อุปกรณ์สแตนอโลน ต้องการเพิ่มต่อหรือไม่?
acc_dev_oneMachine=อุปกรณ์สแตนอโลน
acc_dev_fingervein=เส้นเลือดดำ
acc_dev_control=แผงควบคุม
acc_dev_protocol=ประเภทของโปรโตคอล
acc_ownedBoard=เป็นเจ้าของบอร์ดขยาย
#设备操作
acc_dev_start=เริ่ม
acc_dev_accLevel=ได้รับอนุญาตในการควบคุม
acc_dev_timeZoneAndHoliday=โซนเวลา , วันหยุด
acc_dev_linkage=ลิ้งเอจ
acc_dev_doorOpt=พารามิเตอร์ประตู
acc_dev_firstPerson=คนแรกเปิดประตู
acc_dev_multiPerson=หลายคนเปิดประตู
acc_dev_interlock=อินเตอร์ล็อค
acc_dev_antiPassback=AntiPassback
acc_dev_wiegandFmt=รูปแบบวีแกนด์
acc_dev_outRelaySet=พารามิเตอร์เอาท์พุทเสริม
acc_dev_backgroundVerifyParam=ตัวเลือกการตรวจสอบพื้นหลัง
acc_dev_getPersonInfoPrompt=กรุณาตรวจสอบว่าคุณได้รับข้อมูลพนักงานสำเร็จแล้ว มิฉะนั้นจะเกิดข้อยกเว้นขึ้น ต้องการดำเนินการต่อ?
acc_dev_getEventSuccess=ดึงเหตุการณ์สำเร็จ
acc_dev_getEventFail=เกิดข้อผิดพลาดในการดึงเหตุการณ์
acc_dev_getInfoSuccess=ดึงข้อมูลสำเร็จ
acc_dev_getInfoXSuccess=ได้รับ {0} สำเร็จ
acc_dev_getInfoFail=เกิดข้อผิดพลาดในการดึงข้อมูล
acc_dev_updateExtuserInfoFail=ไม่สามารถอัพเดทข้อมูลพนักงานสำหรับข้อมูลเพิ่มเติม กรุณาดึงข้อมูล
acc_dev_getPersonCount=ดึงจำนวนผู้ใช้
acc_dev_getFPCount=ดึงจำนวนลายนิ้วมือ
acc_dev_getFVCount=ดึงจำนวนเส้นเลือดดำ
acc_dev_getFaceCount=ดึงจำนวนใบหน้า
acc_dev_getPalmCount=ดึงจำนวนฝ่ามือ
acc_dev_getBiophotoCount=ดึงจำนวนภาพใบหน้า
acc_dev_noData=ไม่มีข้อมูลกลับคืนจากอุปกรณ์นี้
acc_dev_noNewData=ไม่มีข้อมูลบันทึกใหม่ในอุปกรณ์นี้
acc_dev_softLtDev=มีผู้ใช้ในซอฟต์เเวร์มากกว่าในอุปกรณ์
acc_dev_personCount=จำนวนบุคคล
acc_dev_personDetail=มีรายละเอียดดังต่อไปนี้:
acc_dev_softEqualDev=จำนวนบุคคลในอุปกรณ์และซอฟต์แวร์ทั้งสองเท่ากัน
acc_dev_softGtDev=จำนวนบุคคลในอุปกรณ์มากกว่าในซอฟต์แวร์
acc_dev_cmdSendFail=การส่งคำสั่งล้มเหลว กรุณาลองอีกครั้ง
acc_dev_issueVerifyParam=ตั้งค่าการยืนยันตัวเลือกพื้นหลัง
acc_dev_verifyParamSuccess=การยืนยันตัวเลือกพื้นหลังสำเร็จ
acc_dev_backgroundVerify=ยืนยันพื้นหลัง
acc_dev_selRightFile=เลือกไฟล์ที่อัพเกรดถูกต้อง!
acc_dev_devNotOpForOffLine=อุปกรณ์ออฟไลน์ กรุณาลองอีกครั้งในภายหลัง
acc_dev_devNotSupportFunction=อุปกรณ์ไม่รองรับคุณสมบัตินี้
acc_dev_devNotOpForDisable=อุปกรณ์ปิดการใช้งาน กรุณาลองอีกครั้งในภายหลัง
acc_dev_devNotOpForNotOnline=อุปกรณ์ออฟไลน์หรือปิดการใช้งาน กรุณาลองอีกครั้งในภายหลัง
acc_dev_getPersonInfo=ดึงข้อมูลพนักงาน
acc_dev_getFPInfo=ดึงข้อมูลลายนิ้วมือ
acc_dev_getFingerVeinInfo=ดึงข้อมูลเส้นเลือดดำ
acc_dev_getPalmInfo=ดึงข้อมูลฝ่ามือ
acc_dev_getBiophotoInfo=ดึงข้อมูลรูบหน้าที่มองเห็นได้
acc_dev_getIrisInfo=รับข้อมูลเกี่ยวกับม่านตา
acc_dev_disable=ปิดการใช้งาน กรุณาเลือกอีกครั้ง
acc_dev_offlineAndContinue=ออฟไลน์ ดำเนินการต่อ ?
acc_dev_offlineAndSelect=ออฟไลน์
acc_dev_opAllDev=อุปกรณ์ทั้งหมด
acc_dev_opOnlineDev=อุปกรณ์ออนไลน์
acc_dev_opException=จัดการกับข้อยกเว้น
acc_dev_exceptionAndConfirm=หมดเวลาการเชื่อมต่ออุปกรณ์ การดำเนินการล้มเหลว ตรวจสอบการเชื่อมต่อเครือข่าย
acc_dev_getFaceInfo=ดึงข้อมูลใบหน้า
acc_dev_selOpDevType=เลือกประเภทของอุปกรณ์ที่จะดำเนินการ:
acc_dev_hasFilterByFunc=แสดงเฉพาะอุปกรณ์ที่ออนไลน์และรองรับฟังก์ชั่น !
acc_dev_masterSlaveMode=RS485 โหมดหลักและโหมดรอง
acc_dev_master=โหมดโฮสต์
acc_dev_slave=โหมดรอง
acc_dev_modifyRS485Addr=แก้ไขที่อยู่ RS485
acc_dev_rs485AddrTip=กรุณาระบุจำนวนเต็มระหว่าง 1 และ 63 !
acc_dev_enableFeature=อุปกรณ์มีการเปิดใช้งานการตรวจสอบพื้นหลัง
acc_dev_disableFeature=อุปกรณ์มีการปิดใช้งานการตรวจสอบพื้นหลัง
acc_dev_getCountOnly=ดึงจำนวนรวมเท่านั้น
acc_dev_queryDevPersonCount=ดึงจำนวนพนักงาน
acc_dev_queryDevVolume=ดูความจุอุปกรณ์
acc_dev_ruleType=ประเภทกฎ
acc_dev_contenRule=รายละเอียดกฎ
acc_dev_accessRules=ดูกฎของอุปกรณ์
acc_dev_ruleContentTip=ใช้เครื่องหมาย '|' คั่นระหว่างกฎที่หลากหลาย
acc_dev_rs485AddrFigure=เลขรหัสที่อยู่ RS485
acc_dev_addLevel=เพิ่มลงในระดับ
acc_dev_personOrFingerTanto=หมายเลขสมาชิกหรือลายนิ้วมือเกินเกินขีดจำกัด การซิงค์ล้มเหลว...
acc_dev_personAndFingerUnit=(หมายเลข)
acc_dev_setDstime=ตั้งค่า DST
acc_dev_setTimeZone=ตั้งค่าโซนเวลาอุปกรณ์
acc_dev_selectedTZ=เลือกโซนเวลา
acc_dev_timeZoneSetting=การตั้งค่าโซนเวลา
acc_dev_timeZoneCmdSuccess=ส่งคำสั่งโซนเวลาสำเร็จ
acc_dev_enableDstime=เปิดการใช้งาน DST
acc_dev_disableDstime=ปิดการใช้งาน DST
acc_dev_timeZone=โซนเวลา
acc_dev_dstSettingTip=การตั้งค่าDST
acc_dev_dstDelTip=กำลังนำออกอุปกรณ์ DST
acc_dev_enablingDst=กำลังเปิดการใช้งาน DST
acc_dev_dstEnableCmdSuccess=ส่งคำสั่งเปิดการใช้งาน DSTสำเร็จ
acc_dev_disablingDst=กำลังปิดการใช้งาน DST
acc_dev_dstDisableCmdSuccess=ส่งคำสั่งปิดการใช้งาน DSTสำเร็จ
acc_dev_dstCmdSuccess=ส่งคำสั่ง DST สำเร็จ
acc_dev_usadst=DST
acc_dev_notSetDst=ไม่มีการตั้งค่า
acc_dev_selectedDst=เลือก DST
acc_dev_configMasterSlave=ตั้งค่าโหมดหลักโหมดรอง
acc_dev_hasFilterByUnOnline=แสดงเฉพาะอุปกรณ์ที่ออนไลน์เท่านั้น
acc_dev_softwareData=หากคุณพบว่าข้อมูลไม่สอดคล้องกับอุปกรณ์ กรุณาซิงค์ข้อมูลทั้งสองก่อนที่จะค้นหา !
acc_dev_disabled=อุปกรณ์ถูกปิดการใช้งานไม่สามารถดำเนินการได้ !
acc_dev_offline=อุปกรณ์ออฟไลน์ไม่สามารถดำเนินการได้ !
acc_dev_noSupport=อุปกรณ์ไม่รองรับฟังก์ชันนี้ ไม่สามารถดำเนินการได้ !
acc_dev_noRegDevTip=อุปกรณ์นี้ไม่ได้กำหนดให้เป็นอุปกรณ์ลงทะเบียน ข้อมูลในซอฟต์แวร์จะไม่อัพเดท แน่ใจว่าจะดำเนินการต่อ?
acc_dev_noOption=ไม่มีตัวเลือกที่เหมาะสม
acc_dev_devFWUpdatePrompt=อุปกรณ์ในปัจจุบันมีความผิดปกติปิดการใช้งานพนักงานและฟังก์ชั่นการตรวจสอบพนักงาน(กรุณาดูคู่มือการใช้)
acc_dev_panelFWUpdatePrompt=อุปกรณ์ในปัจจุบันมีความผิดปกติปิดการใช้งานพนักงานลและฟังก์ชั่นการตรวจสอบพนักงาน อัพเกรดเฟิร์มแวร์ทันทรี
acc_dev_sendEventCmdSuccess=ส่งคำสั่งลบเหตุการณ์สำเร็จ
acc_dev_tryAgain=กรุณาลองใหม่อีกครั้ง
acc_dev_eventAutoCheckAndUpload=ตรวจสอบอัตโนมัติและอัปโหลดเหตุการณ์
acc_dev_eventUploadStart=เริ่มอัปโหลดเหตุการณ์
acc_dev_eventUploadEnd=อัปโหลดเหตุการณ์สำเร็จ
acc_dev_eventUploadFailed=อัปโหลดเหตุการณ์ล้มเหลว
acc_dev_eventUploadPrompt=รุ่นเฟิร์มแวร์ของอุปกรณ์นั้นเก่าเกินไป ก่อนที่คุณอัพเกรดรุ่นเฟิร์มแวร์ คุณต้อง:
acc_dev_backupToSoftware=สำรองข้อมูลไปยังซอฟแวร์
acc_dev_deleteEvent=ลบบันทึกเก่า
acc_dev_upgradePrompt=รุ่นของเฟิร์มแวร์เก่าเกินไปและอาจทำให้เกิดข้อผิดพลาด กรุณาสำรองข้อมูลไปยังซอฟต์แวร์ก่อนการอัพเดตเฟิร์มแวร์
acc_dev_conflictCardNo=แสดงบัตรในระบบเป็น {0} ในคนอื่น ๆ !
acc_dev_rebootAfterOperate=การดำเนินการสำเร็จ อุปกรณ์จะรีสตาร์ทในภายหลัง
acc_dev_baseOptionTip=ความผิดปกติของพารามิเตอร์พื้นฐาน
acc_dev_funOptionTip=ความผิดปกติของฟังก์ชันพารามิเตอร์
acc_dev_sendComandoTip=ส่งคำสั่งพารามิเตอร์อุปกรณ์ล้มเหลว
acc_dev_noC3LicenseTip=ไม่สามารถเพิ่มอุปกรณ์ประเภท ({0}) ได้ !  กรุณาติดต่อฝ่ายขาย
acc_dev_combOpenDoorTip=({0})ตั้งค่าการเปิดประตูแบบหลายคน ไม่สามารถใช้งานการตรวจสอบพื้นหลังในเวลาเดียวกันได้ !
acc_dev_combOpenDoorPersonCountTip=กลุ่ม {0} เปิดได้ไม่เกิน {1}!
acc_dev_addDevTip=ใช้เฉพาะอุปกรณ์ที่เพิ่มโปรโตคอลการสื่อสารเป็น PULL !
acc_dev_addError=ข้อยกเว้นการเพิ่มอุปกรณ์, พารามิเตอร์หายไป ({0})!
acc_dev_updateIPAndPortError=อัพเดทไอพีเซิร์ฟเวอร์ และพอร์ตล้มเหลว...
acc_dev_transferFilesTip=ทดสอบเฟิร์มแวร์เสร็จสิ้น ถ่ายโอนไฟล์
acc_dev_serialPortExist=ซีเรียลพอร์ตมีอยู่แล้ว
acc_dev_isExist=มีอุปกรณ์
acc_dev_description=คำอธิบาย
acc_dev_searchEthernet=ค้นหาอินเทอร์เน็ตของอุปกรณ์
acc_dev_searchRS485=ค้นหา RS485 ของอุปกรณ์
acc_dev_rs485AddrTip1=RS485 ที่อยู่เริ่มต้นไม่สามารถมากกว่าที่อยู่สิ้นสุด
acc_dev_rs485AddrTip2=ขอบเขตการค้นหาของ RS485 ต้องน้อยกว่า 20
acc_dev_clearAllCmdCache=ล้างคำสั่งทั้งหมด
acc_dev_authorizedSuccessful=อนุญาตสำเร็จ
acc_dev_authorize=อนุญาต
acc_dev_registrationDevice=ลงทะเบียนอุปกรณ์
acc_dev_setRegistrationDevice=ตั้งค่าเป็นการลงทะเบียนอุปกรณ์
acc_dev_mismatchedDevice=อุปกรณ์นี้ไม่สามารถใช้กับตลาดของคุณได้ กรุณาตรวจสอบซีเรียลนัมเบอร์ของอุปกรณ์นี้กับฝ่ายขายของเรา
acc_dev_pwdStartWithZero=รหัสผ่านการสื่อสารไม่สามารถเริ่มต้นด้วยศูนย์!
acc_dev_maybeDisabled=ใบอนุญาตนี้อนุญาตให้เพิ่มประตูได้อีก {0} ประตู เพิ่มอุปกรณ์ใหม่ไม่สามารถเพิ่มจำนวนประตูได้ ต้องการดำเนินการต่อ?
acc_dev_Limit=ใบอนุญาตในระบบได้ถูกส่งไปยังการอนุญาต ถ้าต้องการเพิ่มกรุณาขอการอนุญาต
acc_dev_selectDev=กรุณาเลือกอุปกรณ์ !
acc_dev_cannotAddPullDevice=ไม่สามารถเพิ่มอุปกรณ์ pull ! กรุณาติดต่อฝ่ายขาย
acc_dev_notContinueAddPullDevice=มีอุปกรณ์ pull {0} ในระบบ ไม่สามารถดำเนินการเพิ่มได้อีกครั้ง กรุณาติดต่อฝ่ายขาย
acc_dev_deviceNameNull=ไม่มีชื่ออุปกรณ์เเละไม่สามารถเพิ่มอุปกรณ์ !
acc_dev_commTypeErr=ประเภทการเชื่อมต่อไม่ตรงกันและไม่สามารถเพิ่มอุปกรณ์ !
acc_dev_inputDomainError=กรุณากรอกที่อยู่โดเมนที่ถูกต้อง
acc_dev_levelTip=มีมากว่า 5,000 คนในระดับนี้ ไม่สามารถเพิ่มอุปกรณ์นี้ลงบนระดับนี้ได้
acc_dev_auxinSet=การตั้งค่าอินพุทเสริม
acc_dev_verifyModeRule=กฎโหมดการตรวจสอบ
acc_dev_netModeWired=อินเทอร์เน็ตแบบใช้สาย
acc_dev_netMode4G=4G
acc_dev_netModeWifi=Wifi
acc_dev_updateNetConnectMode=สลับการเชื่อมต่อเครือข่าย
acc_dev_wirelessSSID=อินเตอร์เน็ตไร้สาย SSID
acc_dev_wirelessKey=รหัส WIFI
acc_dev_searchWifi=ค้นหา WIFI
acc_dev_testNetConnectSuccess=การเชื่อมต่อสำเร็จหรือไม่?
acc_dev_testNetConnectFailed=การเชื่อมต่อนี้เชื่อมตไม่ถูกต้อง!
acc_dev_signalIntensity=ความแรงของสัญญาณ
acc_dev_resetSearch=ค้นหาอีกครั้ง
acc_dev_addChildDevice=เพิ่มอุปกรณ์ย่อย
acc_dev_modParentDevice=เปลี่ยนอุปกรณ์หลัก
acc_dev_configParentDevice=กำหนดค่าอุปกรณ์หลัก
acc_dev_lookUpChildDevice=ดูอุปกรณ์สำหรับลูก
acc_dev_addChildDeviceTip=ต้องมีการมอบอำนาจภายใต้อุปกรณ์ย่อยที่ได้รับอนุญาต
acc_dev_maxSubCount=จำนวนอุปกรณ์ย่อยที่ได้รับอนุญาตเกินจำนวนสูงสุดของการเข้าถึง {0} หน่วย
acc_dev_seletParentDevice=โปรดเลือกอุปกรณ์หลัก!
acc_dev_networkCard=บัตรเครือข่าย
acc_dev_issueParam=กำหนดค่าซิงค์พารามิเตอร์
acc_dev_issueMode=โหมดซิงค์
acc_dev_initIssue=เฟิร์มเเวร์เวอร์ชัน 3030 การเริ่มต้นข้อมูล
acc_dev_customIssue=กำหนดค่าซิงค์ข้อมูล
acc_dev_issueData=ข้อมูล
acc_dev_parent=อุปกรณ์หลัก
acc_dev_parentEnable=อุปกรณ์หลักถูกปิดใช้งาน
acc_dev_parentTips=การผูกอุปกรณ์หลักจะลบข้อมูลทั้งหมดที่อยู่ในอุปกรณ์และต้องตั้งค่าใหม่
acc_dev_addDevIpTip=ที่อยู่ IP ใหม่ไม่สามารถสอดคล้องกับที่อยู่ IP ของเซิร์ฟเวอร์
acc_dev_modifyDevIpTip=ที่อยู่เซิร์ฟเวอร์ใหม่ต้องไม่เหมือนกับที่อยู่ IP ของอุปกรณ์
acc_dev_setWGReader=ตั้งเครื่องอ่านวีแกนด์
acc_dev_selectReader=คลิกเพื่อเลือกหัวอ่าน
acc_dev_IllegalDevice=อุปกรณ์ที่ไม่ถูกต้อง
acc_dev_syncTimeWarnTip=เวลาซิงโครไนซ์กับอุปกรณ์ต่อไปนี้ควรซิงค์ในเครื่องหลัก
acc_dev_setTimeZoneWarnTip=โซนเวลาสำหรับอุปกรณ์เหล่านี้ต้องมีการซิงโครไนซ์ในอุปกรณ์หลัก
acc_dev_setDstimeWarnTip=ต้องมีการปรับเวลาออมแสงตามฤดูกาลสำหรับอุปกรณ์ต่อไปนี้ในต้นแบบ
acc_dev_networkSegmentSame=อะแดปเตอร์เครือข่ายสองตัวไม่สามารถใช้ส่วนเครือข่ายเดียวกันได้
acc_dev_upgradeProtocolNoMatch=โปรโตคอลไฟล์การอัปเกรดไม่ตรงกัน
acc_dev_ipAddressConflict=มีอุปกรณ์ที่มีที่อยู่ IP เดียวกันอยู่แล้วโปรดแก้ไขที่อยู่ IP ของอุปกรณ์และเพิ่มอุปกรณ์อีกครั้ง
acc_dev_checkServerPortTip=พอร์ตเซิร์ฟเวอร์ไม่สอดคล้องกับพอร์ตการสื่อสารระบบซึ่งอาจทำให้ไม่สามารถเพิ่มได้ดำเนินการต่อไปหรือไม่?
acc_dev_clearAdmin=ยกเลิกการเป็นผู้ดูแลระบบ
acc_dev_setDevSate=การตั้งค่าเข้า/ออกของอุปกรณ์
acc_dev_sureToClear=คุณต้องการยกเลิกผู้ดูแลระบบอุปกรณ์หรือไม่?
acc_dev_hostState=สถานะอุปกรณ์หลัก
acc_dev_regDeviceTypeTip=อุปกรณ์นี้ไม่สามารถเพิ่มได้ เนื่องจากถูกป้องกันอยู่ โปรดติดต่อเจ้าหน้าที่ทางซอฟต์แวร์ที่เกี่ยวข้อง
acc_dev_extBoardType=ประเภทบอร์ด I/O
acc_dev_extBoardTip=หลังจากการกำหนดค่าคุณต้องรีสตาร์ทอุปกรณ์เพื่อให้มีผล
acc_dev_extBoardLimit=เฉพาะ {0} บอร์ด I/O ประเภทนี้เท่านั้นที่สามารถเพิ่มในแต่ละอุปกรณ์!
acc_dev_replace=เปลี่ยนอุปกรณ์
acc_dev_replaceTip=หลังจากการแทนที่ อุปกรณ์เก่าจะไม่ทำงาน โปรดระวัง!
acc_dev_replaceTip1=หลังจากการแทนที่ โปรดดำเนินการ "ซิงค์ข้อมูลทั้งหมด";
acc_dev_replaceTip2=โปรดตรวจสอบให้แน่ใจว่ารุ่นอุปกรณ์เปลี่ยนเหมือนกัน!
acc_dev_replaceTip3=โปรดตรวจสอบให้แน่ใจว่าอุปกรณ์ทดแทนได้ตั้งค่าที่อยู่เซิร์ฟเวอร์และพอร์ตเดียวกันกับอุปกรณ์เก่า!
acc_dev_replaceFail=ประเภทเครื่องของอุปกรณ์ไม่สอดคล้องกันและไม่สามารถเปลี่ยนได้!
acc_dev_notApb=อุปกรณ์นี้ไม่สามารถทำประตูหรืออ่านหัวต่อต้านการดำน้ำ
acc_dev_upResourceFile=อัปโหลดไฟล์ทรัพยากร
acc_dev_playOrder=ลำดับการเล่น
acc_dev_setFaceServerInfo=ตั้งค่าพารามิเตอร์การเปรียบเทียบส่วนหลังของใบหน้า
acc_dev_faceVerifyMode=โหมดเปรียบเทียบใบหน้า
acc_dev_faceVerifyMode1=เปรียบเทียบท้องถิ่น
acc_dev_faceVerifyMode2=การเปรียบเทียบแบ็คเอนด์
acc_dev_faceVerifyMode3=ลำดับความสำคัญในการเปรียบเทียบท้องถิ่น
acc_dev_faceBgServerType=ประเภทเซิร์ฟเวอร์แบ็คเอนด์ใบหน้า
acc_dev_faceBgServerType1=บริการแพลตฟอร์มซอฟต์แวร์
acc_dev_faceBgServerType2=บริการของบุคคลที่สาม
acc_dev_isAccessLogic=เปิดใช้งานการตรวจสอบตรรกะการควบคุมการเข้าถึง
#[5]门-其他关联的也复用此处
acc_door_entity=ประตู
acc_door_number=หมายเลขประตู
acc_door_name=ชื่อประตู
acc_door_activeTimeZone=ใช้งานโซนเวลา
acc_door_passageModeTimeZone=โซนเวลาโหมดสิทธิการผ่าน
acc_door_setPassageModeTimeZone=ตั้งค่าโซนเวลาโหมดสิทธิการผ่าน
acc_door_notPassageModeTimeZone=ไม่ได้ตั้งค่าโซนเวลาโหมดสิทธิการผ่าน
acc_door_lockOpenDuration=ระยะเวลาปลดล็อก
acc_door_entranceApbDuration=Anti-passback ระยะเวลาของการเข้า
acc_door_sensor=เซ็นเซอร์ประตู
acc_door_sensorType=ประเภทเซ็นเซอร์ประตู
acc_door_normalOpen=เปิดปกติ
acc_door_normalClose=ปิดปกติ
acc_door_sensorDelay=หน่วงเวลาเซ็นเซอร์ประตู
acc_door_closeAndReverseState=สถานะย้อนกลับล็อกเมื่อประตูปิด
acc_door_hostOutState=สถานะโหมดหลัก
acc_door_slaveOutState=สถานะโหมดรอง
acc_door_inState=เข้า
acc_door_outState=ออก
acc_door_requestToExit=โหมด REX
acc_door_withoutUnlock=ล็อค
acc_door_unlocking=ปลดล็อค
acc_door_alarmDelay=หน่วงเวลา REX
acc_door_duressPassword=รหัสผ่านฉุกเฉิน
acc_door_currentDoor=ประตูปัจจุบัน
acc_door_allDoorOfCurDev=ประตูทั้งหมดของอุปกรณ์ปัจจุบัน
acc_door_allDoorOfAllDev=ประตูทั้งหมดของอุปกรณ์ทั้งหมด
acc_door_allDoorOfAllControlDev=ควบคุมอุปกรณ์ทั้งหมดในประตู
acc_door_allDoorOfAllStandaloneDev=ประตูทั้งหมดของอุปกรณ์สแตนอโลนทั้งหมด
acc_door_allWirelessLock=ระบบล็อคไร้สายทั้งหมด
acc_door_max6BitInteger=จำนวนเต็มสูงสุด 6 บิต
acc_door_direction=คำสั่ง
acc_door_onlyInReader=อ่านเฉพาะเข้า
acc_door_bothInAndOutReader=อ่านเข้าและออก
acc_door_noDoor=กรุณาเพิ่มประตู
acc_door_nameRepeat=ชื่อประตูซ้ำ
acc_door_duressPwdError=รหัสผ่านฉุกเฉินไม่สามารถเหมือนกับรหัสผ่านพนักงานได้
acc_door_urgencyStatePwd=กรุณากรอกจำนวนเต็ม {0} หลัก
acc_door_noDevOnline=ไม่มีอุปกรณ์ที่ออนไลน์หรือประตูไม่รองรับโหมดการตรวจสอบบัตร
acc_door_durationLessLock=หน่วงเวลาเซ็นเซอร์ประตูต้องไม่มากกว่าหน่วงเวลาการล็อค
acc_door_lockMoreDuration=หน่วงเวลาการล็อคต้องไม่มากกว่าหน่วงเวลาเซ็นเซอร์ประตู
acc_door_lockAndExtLessDuration=ผลรวมของระยะเวลาปลดล็อกประตูเเละหน่วงเวลาการผ่านต้องไม่มากกว่าหน่วงเวลาเซ็นเซอร์ประตู
acc_door_noDevTrigger=อุปกรณ์ไม่ตรงตามเงื่อนไข !
acc_door_relay=รีเลย์
acc_door_pin=รหัสผ่าน
acc_door_selDoor=เลือกประตู
acc_door_sensorStatus=เซ็นเซอร์ประตู ({0})
acc_door_sensorDelaySeconds=หน่วงเวลาเซ็นเซอร์ประตู ({0} s)
acc_door_timeSeg=โซนเวลา  ({0})
acc_door_combOpenInterval=ช่วงเวลาการดำเนินการหลายคน
acc_door_delayOpenTime=หน่วงเวลาเปิดประตู
acc_door_extDelayDrivertime=หน่วงเวลาเปิดปกติ
acc_door_enableAudio=เปิดการใช้งานสัญญาณเตือน
acc_door_disableAudio=ปิดใช้งานเสียงสัญญาณเตือน
acc_door_lockAndExtDelayTip=ระยะเวลาการเปิดล็อคและผลรวมการหน่วงเวลาต้องไม่เกิน 254 วินาที
acc_door_disabled=ประตูนี้ปิดใช้งานไม่สามารถดำเนินการได้!
acc_door_offline=ประตูนี้อยู่ในสถานะออฟไลน์ ไม่สามารถดำเนินการได้!
acc_door_notSupport=ประตูต่อไปนี้ไม่รองรับคุณสมบัตินี้
acc_door_select=เลือกประตู
acc_door_pushMaxCount=มีการเปิดการใช้งานประตูแล้ว {0} ในระบบและมีการจำกัดของใบอนุญาต! กรุณาติดต่อฝ่ายขายของเรา
acc_door_outNumber=ใบอนุญาติในปัจจุบันยินยอมให้คุณเพิ่มประตูได้มากขึ้นอีก {0} เท่านั้น  โปรดเลือกประตูและลองอีกครั้ง หรือติดต่อฝ่ายขายเพื่ออัพเดตใบอนุญาติ
acc_door_latchTimeZone=โซนเวลา REX
acc_door_wgFmtReverse=หมายเลขบัตรแบบย้อนกลับ
acc_door_allowSUAccessLock=อนุญาตให้ผู้ใช้เข้าถึงเมื่อล็อค
acc_door_verifyModeSinglePwd=รหัสผ่านไม่สามารถใช้เป็นวิธีการตรวจสอบอิสระ!
acc_door_doorPassword=รหัสผ่านสำหรับประตูเปิด
#辅助输入
acc_auxIn_timeZone=โซนเวลาใช้งาน
#辅助输出
acc_auxOut_passageModeTimeZone=โซนเวลาโหมดสิทธิการผ่าน
acc_auxOut_disabled=เอ้าท์พุทเสริมสถานะผิดปกติ ไม่สามารถดำเนินการได้!
acc_auxOut_offline=เอ้าท์พุทเสริมสถานะออฟไลน์ ไม่สามารถดำเนินการได้ !
#[8]门禁权限组
acc_level_doorGroup=การรวมประตู
acc_level_openingPersonnel=พนักงานที่เปิดประตู
acc_level_noDoor=ไม่มีรายการที่ใช้ได้ กรุณาเพิ่มอุปกรณ์เป็นอันดับเรก
acc_level_doorRequired=จำเป็นต้องเลือกประตู
acc_level_doorCount=จำนวนประตู
acc_level_doorDelete=ลบประตู
acc_level_isAddDoor=เพิ่มประตูไปยังระดับการควบคุมการเข้า-ออกนี้ทันที?
acc_level_master=ทั่วไป
acc_level_noneSelect=กรุณาเพิ่มระดับการควบคุมการเข้า-ออก
acc_level_useDefaultLevel=ต้องการเปลี่ยนระดับสิทธิ์การควบคุมไปยังแผนกใหม่ ?
acc_level_persAccSet=ตั้งค่าควบคุมประตูของพนักงาน
acc_level_visUsed={0} ใช้งานระบบผู้มาติดต่อไม่สามารถลบได้
acc_level_doorControl=ควบคุมประตู
acc_level_personExceedMax=จำนวนของกลุ่มที่ได้รับอนุญาตในปัจจุบัน ({0}) และจำนวนกลุ่มที่ได้รับอนุญาตเลือกสูงสุด ({1})
acc_level_exportLevel=ส่งออกระดับการเข้าถึง
acc_level_exportLevelDoor=ส่งออกประตูของระดับการเข้าถึง
acc_level_exportLevelPerson=ส่งออกบุคลากรของระดับการเข้าถึง
acc_level_importLevel=นำเข้าระดับการเข้าถึง
acc_level_importLevelDoor=ประตูนำเข้าของระดับการเข้าถึง
acc_level_importLevelPerson=นำเข้าบุคลากรระดับการเข้าถึง
acc_level_exportDoorFileName=ข้อมูลประตูของระดับการเข้าถึง
acc_level_exportPersonFileName=ข้อมูลบุคลากรของระดับการเข้าถึง
acc_levelImport_nameNotNull=ชื่อระดับการเข้าถึงไม่สามารถเว้นว่างได้
acc_levelImport_timeSegNameNotNull=เขตเวลาไม่สามารถเว้นว่างได้
acc_levelImport_areaNotExist=ไม่มีพื้นที่!
acc_levelImport_timeSegNotExist=เขตเวลาไม่มีอยู่จริง!
acc_levelImport_nameExist=ชื่อระดับการเข้าถึง {0} มีอยู่แล้ว!
acc_levelImport_levelDoorExist=ประตูระดับการเข้าถึง {0} มีอยู่แล้ว!
acc_levelImport_levelPersonExist=บุคลากรระดับการเข้าถึง {0} มีอยู่แล้ว!
acc_levelImport_noSpecialChar=ชื่อระดับการเข้าถึงไม่สามารถมีอักขระพิเศษได้!
#[10]首人常开
acc_firstOpen_setting=บุคคลแรก เปิดปกติ
acc_firstOpen_browsePerson=เรียกดูพนักงาน
#[11]多人组合开门
acc_combOpen_comboName=การรวมชื่อ
acc_combOpen_personGroupName=ชื่อกลุ่ม
acc_combOpen_personGroup=กลุ่มหลายคน
acc_combOpen_verifyOneTime=จำนวนพนักงานปัจจุบัน
acc_combOpen_eachGroupCount=จำนวนพนักงานที่เปิดในแต่ละกลุ่ม
acc_combOpen_group=กลุ่ม
acc_combOpen_changeLevel=กลุ่มพนักงานเปิดประตู
acc_combOpen_combDeleteGroup=อ้างอิงคนที่เปิดประตูหลายคน กรุณาลบก่อน
acc_combOpen_ownedLevel=อยู่ในกลุ่ม
acc_combOpen_mostPersonCount=จำนวนกลุ่มต้องไม่มากกว่า 5 คน
acc_combOpen_leastPersonCount=จำนวนกลุ่มต้องไม่น้อยกว่า 2 คน
acc_combOpen_groupNameRepeat=ชื่อกลุ่มซ้ำ
acc_combOpen_groupNotUnique=กลุ่มบุคคลเปิดประตูต้องไม่เหมือนกัน
acc_combOpen_persNumErr=จำนวนของกลุ่มเกินกว่าค่าที่เลือก กรุณาเลือกอีกครั้ง !
acc_combOpen_combOpengGroupPersonShort=หลังจากที่ย้ายบุคคล จำนวนกลุ่มพนักงานที่เปิดประตูกลุ่มจะไม่เพียงพอ กรุณาลบกลุ่มเปิดเป็นอันดับแรก
acc_combOpen_backgroundVerifyTip=ประตูเป็นอุปกรณ์ที่ใช้ในการตรวจสอบพื้นหลัง ไม่สามารถนำโหมดหลายคนเปิดประตูมาใช้ในเวลาเดียวกันได้ !
#[12]互锁
acc_interlock_rule=กฎของอินเตอร์ล็อค
acc_interlock_mode1Or2=อินเตอร์ล็อคระหว่าง {0} และ {1}
acc_interlock_mode3=ในหมู่อินเตอร์ล็อค {0} และ {1} และ {2}
acc_interlock_mode4=อินเตอร์ล็อคระหว่าง {0} และ {1} หรือ {2} และ {3}
acc_interlock_mode5=ในหมู่อินเตอร์ล็อค {0} และ {1} และ {2} และ {3}
acc_interlock_hasBeenSet=ได้รับการตั้งค่าอินเตอร์ล็อค
acc_interlock_group1=กลุ่ม 1
acc_interlock_group2=กลุ่ม 2
acc_interlock_ruleInfo=การเชื่อมต่อระหว่างกลุ่ม
acc_interlock_alreadyExists=มีกฎการเชื่อมต่อเดียวกันอยู่แล้วอย่าเพิ่มซ้ำ!
acc_interlock_groupInterlockCountErr=กฎการเชื่อมต่อภายในกลุ่มต้องมีอย่างน้อยสองประตู
acc_interlock_ruleType=ประเภทกฎที่เชื่อมต่อกัน
#[13]反潜
acc_apb_rules=กฎ Anti-Passback
acc_apb_reader=Anti-Passback ระหว่างหัวอ่านของประตู {0}
acc_apb_reader2=Anti-Passback ระหว่างหัวอ่านของประตู: {0}, {1}
acc_apb_reader3=Anti-Passback ระหว่างหัวอ่านของประตู: {0}, {1}, {2}
acc_apb_reader4=Anti-Passback ระหว่างหัวอ่านของประตู: {0}, {1}, {2}, {3}
acc_apb_reader5=Anti-Passback หัวอ่านด้านนอกประตู {0}
acc_apb_reader6=Anti-Passback หัวอ่านด้านในประตู {0}
acc_apb_reader7=Anti-Passback ระหว่างหัวอ่านของทั้ง 4 ประตู
acc_apb_twoDoor=Anti-Passback ระหว่าง {0} และ {1}
acc_apb_fourDoor=Anti-Passback ระหว่าง {0} และ {1} , การติดตามกลับระหว่าง {2} และ {3}
acc_apb_fourDoor2=Anti-Passback ระหว่าง {0} หรือ {1} และ {2} หรือ {3}
acc_apb_fourDoor3=Anti-Passback ระหว่าง  {0} และ {1} หรือ {2}
acc_apb_fourDoor4=Anti-Passback ระหว่าง {0} และ {1} หรือ {2} หรือ {3}
acc_apb_hasBeenSet=ได้รับการตั้งค่า Anti-Passback
acc_apb_conflictWithGapb=อุปกรณ์ได้ตั้งค่าโกบอล Anti-Passback ไม่สามารถตั้งค่ากฏนี้ได้อีกครั้ง!
acc_apb_conflictWithApb=ในพื้นที่ของอุปกรณ์นี้ได้ตั้งค่า Anti-Passback  ไม่สามารถตั้งค่ากฏโกบอล Anti-Passback!
acc_apb_conflictWithEntranceApb=ในพื้นที่ของอุปกรณ์นี้ได้ตั้งค่าทางเข้า Anti-Passback ไม่สามารถตั้งค่ากฏโกบอล Anti-Passback!
acc_apb_controlIn=Anti-Passback การเข้า
acc_apb_controlOut=Anti-Passback การออก
acc_apb_controlInOut=Anti-Passback การเข้า/ออก
acc_apb_groupIn=เข้าสู่กลุ่ม
acc_apb_groupOut=ออกจากกลุ่ม
acc_apb_reverseName=ย้อนกลับต่อต้านเรือดำน้ำ
acc_apb_door=ประตูต่อต้านเรือดำน้ำ
acc_apb_readerHead=หัวอ่าน Anti-Submersible
acc_apb_alreadyExists=มีกฎต่อต้านเรือดำน้ำเดียวกันอยู่แล้วอย่าเพิ่มซ้ำ!
#[17]电子地图
acc_map_addDoor=เพิ่มประตู
acc_map_addChannel=เพิ่มกล้อง
acc_map_noAccess=คุณไม่ได้รับอนุญาตให้ใช้แผนที่โมดูลอิเล็กทรอนิกส์ กรุณาติดต่อเจ้าหน้าที่ !
acc_map_noAreaAccess=คุณไม่มีสิทธิ์ใช้งาน E-map โปรดติดต่อผู้ดูแลระบบ
acc_map_imgSizeError=กรุณาอัพโหลดภาพที่มีขนาดน้อยกว่า {0}M!
#[18]门禁事件记录
acc_trans_entity=รายงการบันทึก
acc_trans_eventType=ประเภทเหตุการณ์
acc_trans_firmwareEvent=เหตุการณ์เฟิร์มแวร์
acc_trans_softwareEvent=เหตุการณ์ซอฟท์แวร์
acc_trans_today=เหตุการณ์จากวันนี้
acc_trans_lastAddr=ตำแหน่งล่าสุดที่ทราบ
acc_trans_viewPhotos=ดูรูปภาพ
acc_trans_exportPhoto=ส่งออกรูปภาพ
acc_trans_photo=รูปถ่ายเหตุการณ์การควบคุมการเข้าถึง
acc_trans_dayNumber=วัน
acc_trans_fileIsTooLarge=ไฟล์ที่ส่งออกมีขนาดใหญ่เกินไปโปรดลดขอบเขตในการส่งออก
#[19]门禁验证方式
acc_verify_mode_onlyface=ใบหน้า
acc_verify_mode_facefp=ใบหน้า+ลายนิ้วมือ
acc_verify_mode_facepwd=ใบหน้า+รหัสผ่าน
acc_verify_mode_facecard=ใบหน้า+บัตร
acc_verify_mode_facefpcard=ใบหน้า+ลายนิ้วมือ+บัตร
acc_verify_mode_facefppwd=ใบหน้า+ลายนิ้วมือ+รหัสผ่าน
acc_verify_mode_fv=เส้นเลือดดำ
acc_verify_mode_fvpwd=เส้นเลือดดำ+รหัสผ่าน
acc_verify_mode_fvcard=เส้นเลือดดำ+บัตร
acc_verify_mode_fvpwdcard=เส้นเลือดดำ+รหัสผ่านเเละบัตร
acc_verify_mode_pv=ฝ่ามือ
acc_verify_mode_pvcard=ผ่ามือ+การ์ด
acc_verify_mode_pvface=ฝ่ามือ+ใบหน้า
acc_verify_mode_pvfp=ฝ่ามือ+ลายนิ้วมือ
acc_verify_mode_pvfacefp=ฝ่ามือ+ใบหน้า+ลายนิ้วมือ
#[20]门禁事件编号
acc_eventNo_-1=ไม่มี
acc_eventNo_0=ทาบบัตรเปิดปกติ
acc_eventNo_1=ทาบบัตรระหว่างสิทธิการผ่านโหมดโซนเวลา
acc_eventNo_2=บัตรใบแรกเปิดปกติ (ทาบบัตร)
acc_eventNo_3=เปิดหลายคน (ทาบบัตร)
acc_eventNo_4=เปิดด้วยรหัสผ่านฉุกเฉิน
acc_eventNo_5=เปิดประตูระหว่างสิทธิการผ่านโหมดโซนเวลา
acc_eventNo_6=ลิ้งเอจเหตุการณ์ทริกเกอร์
acc_eventNo_7=ยกเลิกสัญญาณเตือนภัย
acc_eventNo_8=กำลังเปิดรีโมท
acc_eventNo_9=กำลังปิดรีโมท
acc_eventNo_10=ปิดการใช้งานโหมดสิทธิการผ่านของโซนเวลา
acc_eventNo_11=เปิดการใช้งานโหมดสิทธิการผ่านของโซนเวลา
acc_eventNo_12=เปิดรีโมทเอ้าท์พุทเสริม
acc_eventNo_13=ปิดรีโมทเอ้าท์พุทเสริม
acc_eventNo_14=เปิดด้วยลายนิ้วมือปกติ
acc_eventNo_15=พนักงานหลายคนเปิด (ลายนิ้วมือ)
acc_eventNo_16=สแกนลายนิ้วมือระหว่างโหมดสิทธิการผ่านของโซนเวลา
acc_eventNo_17=เปิดด้วยบัตรเพิ่มลายนิ้วมือ
acc_eventNo_18=บัตรใบแรกเปิดปกติ (สแกนลายนิ้วมือ)
acc_eventNo_19=บัตรใบแรกเปิดปกติ (บัตร+ลายนิ้วมือ)
acc_eventNo_20=ช่วงเวลาการดำเนินการสั้นเกินไป
acc_eventNo_21=โซนเวลาการใช้งานประตู (ทาบบัตร)
acc_eventNo_22=โซนเวลาที่ผิดกฎ
acc_eventNo_23=ไม่ได้รับอนุญาต
acc_eventNo_24=Anti-Passback
acc_eventNo_25=อินเตอร์ล็อค
acc_eventNo_26=การตรวจสอบยืนยันหลายคน (ทาบบัตร)
acc_eventNo_27=ปิดการใช้งานบัตร
acc_eventNo_28=หมดเวลาเปิดประตู
acc_eventNo_29=บัตรหมดอายุ
acc_eventNo_30=รหัสผ่านล้มเหลว
acc_eventNo_31=ช่วงเวลาในการสแกนลายนิ้วมือสั้นเกินไป
acc_eventNo_32=การตรวจสอบสิทธิหลายบุคคล (สแกนลายนิ้วมือ)
acc_eventNo_33=ลายนิ้วมือหมดอายุ
acc_eventNo_34=ปิดการใช้งานลายนิ้วมือ
acc_eventNo_35=ประตูไม่ได้ใช้งานโซนเวลา (สแกนลายนิ้วมือ)
acc_eventNo_36=ประตูไม่ได้ใช้งานในโซนเวลา (กดปุ่มออก)
acc_eventNo_37=การปิดประตูล้มเหลวระหว่างผ่านโหมดทามโซน
acc_eventNo_38=รายงานบัตรหาย
acc_eventNo_39=การเข้าถึงถูกปิดใช้งาน
acc_eventNo_40=การตรวจสอบสิทธิหลายบุคคลล้มเหลว (สแกนลายนิ้วมือ)
acc_eventNo_41=โหมดการตรวจสอบล้มเหลว
acc_eventNo_42=รูปแบบวีแกนด์ล้มเหลว
acc_eventNo_43=หมดเวลาตรวจสอบ Anti-passback
acc_eventNo_44=การตรวจสอบพื้นหลังล้มเหลว
acc_eventNo_45=หมดเวลาการตรวจสอบพื้นหลัง
acc_eventNo_47=การส่งคำสั่งล้มเหลว
acc_eventNo_48=การรับรองหลายบุคคลล้มเหลว (ทาบบัตร)
acc_eventNo_49=ประตูไม่ได้ใช้งานในโซนเวลา (รหัสผ่าน)
acc_eventNo_50=ช่วงเวลาในการกดรหัสสั้นเกินไป
acc_eventNo_51=การตรวจสอบสิทธิหลายบุคคล (รหัสผ่าน)
acc_eventNo_52=การตรวจสอบสิทธิหลายบุคคลล้มเหลว (รหัสผ่าน)
acc_eventNo_53=รหัสผ่านหมดอายุ
acc_eventNo_100=สัญญาณแจ้งเตือนการแกะ
acc_eventNo_101=เปิดรหัสผ่านฉุกเฉิน
acc_eventNo_102=เปิดทันที
acc_eventNo_103=เปิดด้วยลายนิ้วมือฉุกเฉิน
acc_eventNo_200=ประตูเปิดอย่างถูกต้อง
acc_eventNo_201=ประตูปิดอย่างถูกต้อง
acc_eventNo_202=กดปุ่มออกเพื่อเปิดประตู
acc_eventNo_203=การเปิดประตูหลายคน (บัตร+ลายนิ้วมือ)
acc_eventNo_204=เกินโซนเวลาของสิทธิ์การผ่าน
acc_eventNo_205=กำลังเปิดด้วยรีโมทปกติ
acc_eventNo_206=เริ่มอุปกรณ์
acc_eventNo_207=เปิดด้วยรหัสผ่าน
acc_eventNo_208=ผู้ใช้ระดับสูงเปิดประตู
acc_eventNo_209=ทริกเกอร์ปุ่มออก (ที่ถูกล็อค)
acc_eventNo_210=เริ่มที่ประตูดับเพลิง
acc_eventNo_211=ผู้ใช้ระดับสูงปิดประตู
acc_eventNo_212=เปิดการใช้งานฟังก์ชันควบคุมลิฟท์
acc_eventNo_213=ปิดการใช้งานฟังก์ชันควบคุมลิฟท์
acc_eventNo_214=เปิดหลายบุคคล (รหัสผ่าน)
acc_eventNo_215=บัตรใบแรกเปิดปกติ (รหัสผ่าน)
acc_eventNo_216=ใส่รหัสผ่านของโหมดสิทธิการผ่านของโซนเวลา
acc_eventNo_220=ไม่ได้เชื่อมต่ออินพุทเสริม (เปิด)
acc_eventNo_221=อินพุทเสริมสั้น (ปิด)
acc_eventNo_222=การตรวจสอบพื้นหลังสำเร็จ
acc_eventNo_223=การตรวจสอบพื้นหลัง
acc_eventNo_225=อินพุทเสริมปกติ
acc_eventNo_226=อินพุทเสริมทริกเกอร์
acc_newEventNo_0=ตรวจสอบการเปิดปกติ
acc_newEventNo_1=ตรวจสอบโหมดสิทธิการผ่านของโซนเวลา
acc_newEventNo_2=พนักงานคนเเรกเปิดประตู
acc_newEventNo_3=พนักงานหลายคนเปิดประตู
acc_newEventNo_20=ช่วงเวลาการดำเนินการสั้นเกินไป
acc_newEventNo_21=ตรวจสอบโซนเวลาที่ไม่ใช้งานประตู
acc_newEventNo_26=รอการการตรวจสอบสิทธิของพนักงานหลายคน
acc_newEventNo_27=พนักงานที่ไม่ได้ลงทะเบียน
acc_newEventNo_29=พนักงานหมดอายุ
acc_newEventNo_30=รหัสผ่านหมดอายุ
acc_newEventNo_41=โหมดการตรวจสอบล้มเหลว
acc_newEventNo_43=ล็อคพนักงาน
acc_newEventNo_44=การตรวจสอบพื้นหลังล้มเหลว
acc_newEventNo_45=การตรวจสอบพื้นหลังหมดเวลา
acc_newEventNo_48=การตรวจสอบหลายบุคคลล้มเหลว
acc_newEventNo_54=แบตเตอรี่ต่ำเกินไป
acc_newEventNo_55=เปลี่ยนแบตเตอรี่ทันที
acc_newEventNo_56=การดำเนินการที่ผิดกฎหมาย
acc_newEventNo_57=พลังงานไฟฟ้าสำรอง
acc_newEventNo_58=เปิดสัญญาณเตือนปกติ
acc_newEventNo_59=การบริหารจัดการการกระทำที่ผิดกฎหมาย
acc_newEventNo_60=ประตูถูกล็อคจากด้านใน
acc_newEventNo_61=ทำซ้ำ
acc_newEventNo_62=ห้ามผู้ใช้
acc_newEventNo_63=ประตูล็อกแล้ว
acc_newEventNo_64=ไม่ได้ใช้งานปุ่มออกของโซนเวลา
acc_newEventNo_65=ไม่ได้ใช้งานอินพุทตัวเสริมของโซนเวลา
acc_newEventNo_66=การอัปเกรดหลักล้มเหลว
acc_newEventNo_67=การจับคู่ระยะไกลสําเร็จ (อุปกรณ์ไม่ได้รับอนุญาต)
acc_newEventNo_68=อุณหภูมิร่างกายสูง - ปฏิเสธการเข้า
acc_newEventNo_69=ไม่มีหน้ากาก – ปฏิเสธการเข้า
acc_newEventNo_70=อัตราส่วนใบหน้าสื่อสารอย่างผิดปกติกับเซิร์ฟเวอร์
acc_newEventNo_71=เซิร์ฟเวอร์หน้าตอบสนองอย่างผิดปกติ
acc_newEventNo_73=รหัส QR Code ไม่ถูกต้อง
acc_newEventNo_74=QR Code หมดอายุแล้ว
acc_newEventNo_101=เปิดสัญญาณเตือนฉุกเฉิน
acc_newEventNo_104=สัญญาณเตือนทาบบัตรไม่ถูกต้อง
acc_newEventNo_105=ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้
acc_newEventNo_106=พลังงานหลักลดลง
acc_newEventNo_107=เเบตเตอรี่ลดต่ำลง
acc_newEventNo_108=ไม่สามารถเชื่อมต่ออุปกรณ์หลัก
acc_newEventNo_109=ตัวอ่านสัญญาณเตือนการแกะ
acc_newEventNo_110=ตัวอ่านออฟไลน์
acc_newEventNo_112=บอร์ดขยายออฟไลน์
acc_newEventNo_114=ตัดการเชื่อมต่อสัญญาณเตือนไฟไหม้ (การตรวจจับสาย)
acc_newEventNo_115=ลัดวงจรอินพุตสัญญาณเตือนไฟไหม้ (การตรวจจับสาย)
acc_newEventNo_116=ตัดการเชื่อมต่ออินพุตเสริม (การตรวจจับสาย)
acc_newEventNo_117=ลัดวงจรอินพุตเสริม (การตรวจจับสาย)
acc_newEventNo_118=สวิตช์ออกจากประตู (การตรวจจับสาย)
acc_newEventNo_119=ลัดวงจรสวิตช์ประตู (การตรวจจับสาย)
acc_newEventNo_120=ประตูตัดการเชื่อมต่อแม่เหล็ก (การตรวจจับสาย)
acc_newEventNo_121=ประตูแม่เหล็กลัดวงจร (การตรวจจับสาย
acc_newEventNo_159=รีโมทคอลโทรลเพื่อเปิดประตู
acc_newEventNo_214=เชื่อมต่อกับเซิร์ฟเวอร์แล้ว
acc_newEventNo_217=เชื่อมต่ออุปกรณ์หลักสำเร็จ
acc_newEventNo_218=ตรวจสอบบัตร ID
acc_newEventNo_222=ตรวจสอบพื้นหลังสำเร็จ
acc_newEventNo_223=ตรวจสอบพื้นหลัง
acc_newEventNo_224=กดกระดิ่ง
acc_newEventNo_227=เปิดประตูคู่
acc_newEventNo_228=ปิดประตูคู่
acc_newEventNo_229=เปิดเอ้าท์พุทเสริมช่วงเวลาปกติ
acc_newEventNo_230=ปิดเอ้าท์พุทเสริมในช่วงเวลา
acc_newEventNo_232=ตรวจสอบสำเร็จ
acc_newEventNo_233=เปิดใช้งานการล็อก
acc_newEventNo_234=ปิดใช้งานการล็อก
acc_newEventNo_235=อัพเกรดหัวอ่านสำเร็จ
acc_newEventNo_236=ล้างสัญญาณเตือนการแกะของหัวอ่าาน
acc_newEventNo_237=หัวอ่านออนไลน์
acc_newEventNo_239=เรียกอุปกรณ์
acc_newEventNo_240=สิ้นสุดการโทร
acc_newEventNo_243=ตัดการเชื่อมต่อสัญญาณเตือนไฟไหม้
acc_newEventNo_244=สัญญาณเตือนไฟไหม้ลัดวงจร
acc_newEventNo_247=บอร์ดขยายออนไลน์
acc_newEventNo_4008=การกู้คืนแหล่งจ่ายไฟหลัก
acc_newEventNo_4014=สัญญาณไฟอินพุตตัดการเชื่อมต่อประตูสุดท้ายเปิดตามปกติ
acc_newEventNo_4015=ประตูออนไลน์
acc_newEventNo_4018=การเปรียบเทียบ Backend เปิด
acc_newEventNo_5023=อยู่ในสถานะไฟ จำกัด
acc_newEventNo_5024=หมดเวลาการตรวจสอบหลายคน
acc_newEventNo_5029=การเปรียบเทียบ Backend ล้มเหลว
acc_newEventNo_6005=ความจุการบันทึกถึงขีดจำกัดบน
acc_newEventNo_6006=สายไฟลัดวงจร (R485)
acc_newEventNo_6007=สายไฟลัดวงจร (Wegan)
acc_newEventNo_6011=ประตูออฟไลน์
acc_newEventNo_6012=สัญญาณเตือนการถอดประตู
acc_newEventNo_6013=สัญญาณไฟเข้าทริกเกอร์ประตูเปิดตามปกติ
acc_newEventNo_6015=รีเซ็ตแหล่งจ่ายไฟของอุปกรณ์ขยาย
acc_newEventNo_6016=การตั้งค่าโรงงานเครื่องกู้คืน
acc_newEventNo_6070=การเปรียบเทียบ Backend (รายการห้าม)
acc_eventNo_undefined=ไม่ได้กำหนดจำนวนเหตุการณ์
acc_advanceEvent_500=โกบอล Anti-passback (ตรรกะ)
acc_advanceEvent_501=บุคคลที่ใช้ได้ (ใช้วันที่)
acc_advanceEvent_502=จำนวนคนของการควบคุม
acc_advanceEvent_503=โกบอลอินเตอร์ล็อค
acc_advanceEvent_504=ควบคุมเส้นทาง
acc_advanceEvent_505=โกบอล Anti-passback (การกำหนดเวลา)
acc_advanceEvent_506=โกบอล Anti-passback (ตรรกะการกำหนดเวลา)
acc_advanceEvent_507=บุคคลที่ใช้ได้ (หลังจากการใช้วันที่ที่ถูกต้องครั้งแรก)
acc_advanceEvent_508=บุคคลที่ใช้ได้ (จำนวนครั้งการใช้งาน)
acc_advanceEvent_509=ตรวจสอบพื้นหลังล้มเหลว (ไม่มีคนลงทะเบียน)
acc_advanceEvent_510=ตรวจสอบพื้นหลังล้มเหลว (ข้อมูลไม่ถูกต้อง)
acc_alarmEvent_701=สัญญาณเตือน DMR (กฎการตั้งค่า：{0})
#[21]实时监控
acc_rtMonitor_openDoor=เปิด
acc_rtMonitor_closeDoor=ปิด
acc_rtMonitor_remoteNormalOpen=รีโมทเปิดปกติ
acc_rtMonitor_realTimeEvent=เหตุการณ์เรียลไทม์
acc_rtMonitor_photoMonitor=การตรวจสอบภาพถ่าย
acc_rtMonitor_alarmMonitor=การตรวจสอบสัญญาณเตือนภัย
acc_rtMonitor_doorState=สถานะประตู
acc_rtMonitor_auxOutName=ชื่อเอาท์พุทเสริม
acc_rtMonitor_nonsupport=ไม่รองรับ
acc_rtMonitor_lock=ล็อค
acc_rtMonitor_unLock=ไม่ได้ล็อค
acc_rtMonitor_disable=ปิดการใช้งาน
acc_rtMonitor_noSensor=ไม่มีเซ็นเซอร์ประตู
acc_rtMonitor_alarm=สัญญาณเตือน
acc_rtMonitor_openForce=เปิดเฉียบพลัน
acc_rtMonitor_tamper=การแกะ
acc_rtMonitor_duressPwdOpen=เปิดรหัสผ่านฉุกเฉิน
acc_rtMonitor_duressFingerOpen=เปิดลายนิ้วมือฉุกเฉิน
acc_rtMonitor_duressOpen=เปิดการใช้งานฉุกเฉิน
acc_rtMonitor_openTimeout=การเปิดหมดเวลา
acc_rtMonitor_unknown=ไม่ทราบ
acc_rtMonitor_noLegalDoor=ไม่มีประตูที่สอดคล้องกับเงื่อนไข !
acc_rtMonitor_noLegalAuxOut=ไม่มีเอาท์พุทเสริมที่สอดคล้องกับเงื่อนไข !
acc_rtMonitor_curDevNotSupportOp=สถานะปัจจุบันของอุปกรณ์ไม่สนับสนุนการดำเนินการนี้ !
acc_rtMonitor_curNormalOpen=ปัจจุบันเปิดปกติ
acc_rtMonitor_whetherDisableTimeZone=สถานะปัจจุบันของประตูคือเปิดตลอดเวลา
acc_rtMonitor_curSystemNoDoors=ขณะนี้ในระบบไม่ได้เพิ่มประตูหรือค้นหาประตูที่สอดคล้องกับความต้องการของท่านไม่พบ!
acc_rtMonitor_cancelAlarm=ยกเลิกสัญญาณเตือนภัย
acc_rtMonitor_openAllDoor=เปิดประตูปัจจุบันทั้งหมด
acc_rtMonitor_closeAllDoor=ปิดประตูปัจจุบันทั้งหมด
acc_rtMonitor_confirmCancelAlarm=คุณแน่ใจหรือว่าต้องการยกเลิกสัญญาณเตือนภัย
acc_rtMonitor_calcelAllDoor=ยกเลิกสัญญาณเตือนภัยทั้งหมด
acc_rtMonitor_initDoorStateTip=ประตูทุกบานได้รับอนุญาตไปยังผู้ใช้ในระบบ
acc_rtMonitor_alarmEvent=เหตุการณ์สัญญาณเตือนภัย
acc_rtMonitor_ackAlarm=ยอมรับ
acc_rtMonitor_ackAllAlarm=ยอมรับทั้งหมด
acc_rtMonitor_ackAlarmTime=เวลายอมรับ
acc_rtMonitor_sureToAckThese=คุณแน่ใจว่าต้องการยืนยันสัญญาณเตือน {0} ?  หลังจากคุณทำการยืนยันแล้ว สัญญาณเตือนทั้งหมดจะถูกยกเลิก
acc_rtMonitor_sureToAckAllAlarm=คุณแน่ใจว่าต้องการยกเลิกสัญญาณเตือน {0} ?  หลังจากคุณทำการยืนยันแล้ว สัญญาณเตือนทั้งหมดจะถูกยกเลิก
acc_rtMonitor_noSelectAlarmEvent=กรุณาเลือกเพื่อนยืนยันเหตุการณ์สัญญาณเตือน
acc_rtMonitor_noAlarmEvent=ในระบบปัจจุบันไม่มีเหตุการณ์สัญญาณเตือนภัย
acc_rtMonitor_forcefully=ยกเลิกสัญญาณเตือน เปิดทันที)
acc_rtMonitor_addToRegPerson=เพิ่มไปยังพนักงานที่ลงทะเบียน
acc_rtMonitor_cardExist=บัตรใบนี้ถูกกำหนดโดย {0} และไม่สามารถออกซ้ำได้อีก
acc_rtMonitor_opResultPrompt=ส่งคำร้องขอ {0}  ล้มเหลว {1}.
acc_rtMonitor_doorOpFailedPrompt=การส่งคำขอไปที่ประตูดังต่อไปนีล้มเหลว โปรดลองอีกครั้ง!
acc_rtMonitor_remoteOpen=เปิดรีโมท
acc_rtMonitor_remoteClose=ปิดรีโมท
acc_rtMonitor_alarmSoundClose=ปิดเสียง
acc_rtMonitor_alarmSoundOpen=เปิดเสียง
acc_rtMonitor_playAudio=เสียงเตือน
acc_rtMonitor_isOpenShowPhoto=เปิดฟังก์ชั่นแสดงภาพ
acc_rtMonitor_isOpenPlayAudio=เปิดฟังก์ชั่นแจ้งเตือนเสียง
acc_rtm_open=เปิดปุ่มรีโมท
acc_rtm_close=ปิดปุ่มรีโมท
acc_rtm_eleModule=ลิฟท์
acc_cancelAlarm_fp=ยกเลิกสัญญาณเตือนภัยเมื่อใช้ลายนิ้วมือฉุกเฉินเปิดประตู
acc_cancelAlarm_pwd=ยกเลิกสัญญาณเตือนภัยเมื่อใช้รหัสฉุกเฉินเปิดประตู
acc_cancelAlarm_timeOut=ยกเลิกสัญญาณเตือนเปิดประตูเกินเวลาที่กำหนด
#定时同步设备时间
acc_timing_syncDevTime=ตั้วเวลาให้ตรงกับเวลาอุปกรณ์
acc_timing_executionTime=เวลาดำเนินการ
acc_timing_theLifecycle=วงจรการดำเนินการ
acc_timing_errorPrompt=การป้อนข้อมูลไม่ถูกต้อง
acc_timing_checkedSyncTime=โปรดเลือกเวลาซิงโครไนซ์
#[25]门禁报表
acc_trans_hasAccLevel=มีระดับการควบคุม
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=กรุณาเพิ่มโซน
acc_zone_code=รหัสโซน
acc_zone_parentZone=โซนปกครอง
acc_zone_parentZoneCode=รหัสปกครองโซน
acc_zone_parentZoneName=ชื่อปกครองโซน
acc_zone_outside=ด้านนอก
#[G2]读头定义
acc_readerDefine_readerName=ชื่อหัวอ่าน
acc_readerDefine_fromZone=ไปจาก
acc_readerDefine_toZone=ไปที่
acc_readerDefine_delInfo1=โซนหัวอ่านเกี่ยวข้องกับการควบคุมพื้นที่ถูกอ้างอิงโดยฟังก์ชั่นการควบคุมระดับสูง ไม่สามารถลบได้!
acc_readerDefine_selReader=เลือกหัวอ่าน
acc_readerDefine_selectReader=กรุณาเพิ่มหัวอ่าน
acc_readerDefine_tip=หลังจากที่พนักงานออกไปยังเขตพื้นที่อื่น ข้อมูลพนักงานในเขตพื้นที่ก่อนหน้านี้จะถูกล้าง
#[G3]全局反潜
acc_gapb_zone=พื้นที่ควบคุม
acc_gapb_whenToResetGapb=รีเซ็ตเวลา Anti-passback
acc_gapb_apbType=ประเภทAnti-passback
acc_gapb_logicalAPB=ตรรกะ Anti-passback
acc_gapb_timedAPB=การตั้งเวลา Anti-passback
acc_gapb_logicalTimedAPB=ตรรกะการตั้งเวลา Anti-passback
acc_gapb_lockoutDuration=ระยะเวลาการปิดใช้
acc_gapb_devOfflineRule=หากอุปกรณ์ออฟไลน์
acc_gapb_standardLevel=ระดับการควบคุมมาตรฐาน
acc_gapb_accessDenied=ปฏิเสธการเข้าใช้
acc_gapb_doorControlZone=ควบคุมประตูผ่านเข้า-ออกของโซน
acc_gapb_resetStatus=รีเซตสถานะAnti-passback
acc_gapb_obeyAPB=กำหนดตามกฎ Anti-passback
acc_gapb_isResetGAPB=รีเซต Anti-Passback
acc_gapb_resetGAPBSuccess=รีเซตสถานะ Anti-Passback สำเร็จ
acc_gapb_resetGAPBFaile=รีเซตสถานะ Anti-Passback ล้มเหลว
acc_gapb_chooseArea=กรุณาเลือกโซน
acc_gapb_notDelInfo1=พื้นที่การเข้าถึงเป็นพื้นที่การเข้าถึงระดับสูง ไม่สามารถลบได้
acc_gapb_notDelInfo2=พื้นที่การเข้าถึงเป็นพื้นที่ที่กำหนดหัวอ่าน ไม่สามารถลบ
acc_gapb_notDelInfo3=พื้นที่การเข้าถึงมีฟังก์ชั่นการเข้าถึงระดับสูง ไม่สามารถลบได้
acc_gapb_notDelInfo4=พื้นที่การเข้าถึงประตูถูกอ้างอิงโดย LED ไม่สามารถลบได้
acc_gapb_zoneNumRepeat=หมายเลขโดเมนการควบคุมซ้ำกัน
acc_gapb_zoneNameRepeat=ชื่อโดเมนการควบคุมซ้ำกัน
acc_gapb_personResetGapbPre=คุณแน่ใจหรือที่ต้องการรีเซต
acc_gapb_personResetGapbSuffix=กฎ Anti-passbackบ ของพนักงาน
acc_gapb_apbPrompt=ประตูเดียวกันไม่สามารถใช้การควบคุมเขต Anti-passback อิสระ!
acc_gapb_occurApb=เกิด Anti-passback
acc_gapb_noOpenDoor=ไม่เปิดประตู
acc_gapb_openDoor=เปิดประตู
acc_gapb_zoneNumLength=ความยาวมากกว่า 20 ตัวอักษร
acc_gapb_zoneNameLength=ความยาวมากกว่า 30 ตัวอักษร
acc_gapb_zoneRemarkLength=ความยาวมากกว่า 50 ตัวอักษร
acc_gapb_isAutoServerMode=ตรวจพบว่าอุปกรณ์ไม่ได้เปิดการใช้งานการตรวจสอบพื้นหลัง อาจจะมีผลกระทบกับฟังก์ชั่น ต้องการเปิดการทำงานทันที?
acc_gapb_applyTo=ใช้กับ
acc_gapb_allPerson=พนักงานทั้งหมด
acc_gapb_justSelected=พนักงานที่เลือก
acc_gapb_excludeSelected=ยกเว้นพนักงานที่เลือก
#[G4]who is inside
acc_zoneInside_lastAccessTime=เวลาที่เข้าถึงล่าสุด
acc_zoneInside_lastAccessReader=หัวอ่านที่เข้าถึงล่าสุด
acc_zoneInside_noPersonInZone=ไม่มีพนักงานในโซน
acc_zoneInside_noRulesInZone=ไม่ได้ตั้งค่ากฎ
acc_zoneInside_totalPeople=บุคคลทั้งหมด
acc_zonePerson_selectPerson=โปรดเลือกพนักงานหรือแผนก
#[G5]路径
acc_route_name=ชื่อเส้นทาง
acc_route_setting=การตั้งค่าเส้นทาง
acc_route_addReader=เพิ่มหัวอ่าน
acc_route_delReader=ล้างหัวอ่าน
acc_route_defineReaderLine=กำหนดเส้นหัวอ่าน
acc_route_up=ขึ้น
acc_route_down=ลง
acc_route_selReader=เลือกหัวอ่านหลังจากการดำเนินการ
acc_route_onlyOneOper=สามารถเลือกหัวอ่านดำเนินการได้แค่อันเดียว
acc_route_readerOrder=กำหนดลำดับหัวอ่าน
acc_route_atLeastSelectOne=โปรดเลือกหัวอ่านอย่างน้อยหนึ่ง
acc_route_routeIsExist=เส้นทางนี้มีอยู่แล้ว!
#[G6]DMR
acc_dmr_residenceTime=เวลาคงอยู่
acc_dmr_setting=การตั้งค่า DMR
#[G7]Occupancy
acc_occupancy_max=ความจุสูงสุด
acc_occupancy_min=ความจุต่ำสุด
acc_occupancy_unlimit=ไม่จำกัด
acc_occupancy_note=ความจุสูงสุดควรมากกว่าความจุขั้นต่ำ!
acc_occupancy_containNote=โปรดป้อนความจุสูงสุด / ขั้นต่ำอย่างน้อยหนึ่งรายการ!
acc_occupancy_maxMinValid=กรุณาใส่หมายเลขที่มากกว่า 0!
acc_occupancy_conflict=มีการตั้งกฎการควบคุมการเข้าพักในโซนนี้
acc_occupancy_maxMinTip=ไม่มีค่าความจุหมายถึงไม่มีข้อ จำกัด
#card availability
acc_personLimit_zonePropertyName=ชื่อคุณสมบัติโซน
acc_personLimit_useType=ใช้
acc_personLimit_userDate=วันที่ถูกต้อง
acc_personLimit_useDays=หลังจากการใช้งานครั้งแรกของวันที่ถูกต้อง
acc_personLimit_useTimes=ใช้จำนวนครั้ง
acc_personLimit_setZoneProperty=ตั้งค่าคุณสมบัติโซน
acc_personLimit_zoneProperty=คุณสมบัติโซน
acc_personLimit_availabilityName=พร้อมชื่อ
acc_personLimit_days=วัน
acc_personLimit_Times=เวลา
acc_personLimit_noDel=เลือกคุณสมบัติโซนควบคุมการเข้า-ออกที่มีการอ้างอิงและไม่สามารถล้างได้
acc_personLimit_cannotEdit=คุณลักษณะพื้นที่การผ่านอ้างอิง ไม่สามารถเปลี่ยนแปลงได้ !
acc_personLimit_detail=รายละเอียด
acc_personLimit_userDateTo=ใช้ได้ถึงวันที่
acc_personLimit_addPersonRepeatTip=พนักงานที่อยู่ภายใต้แผนกที่เลือกถูกเพิ่มเข้าไปในคุณลักษณะของพื้นที่ควบคุมการเข้าถึงโปรดเลือกแผนกอีกครั้ง!
acc_personLimit_leftTimes=เหลืออีก {0} ครั้ง
acc_personLimit_expired=หมดอายุ
acc_personLimit_unused=ไม่ได้ใช้
#全局互锁
acc_globalInterlock_addGroup=เพิ่มกลุ่ม
acc_globalInterlock_delGroup=ลบกลุ่ม
acc_globalInterlock_refuseAddGroupMessage=อินเทอร์ล็อกเดียวกัน ประตูไม่สามารถซ้ำกันในกลุ่มที่เพิ่มเเล้วได้
acc_globalInterlock_refuseAddlockMessage=เพิ่มประตูได้ปรากฎขึ้นในกลุ่มของอินเทอร์ล็อกอื่นๆ
acc_globalInterlock_refuseDeleteGroupMessage=กรุณาลบอินเทอร์ล็อกที่เกี่ยวข้องกับข้อมูล
acc_globalInterlock_isGroupInterlock=กลุ่มอินเทอร์ล็อก
acc_globalInterlock_isAddTheDoorImmediately=เพิ่มประตูทันที
acc_globalInterlock_isAddTheGroupImmediately=เพิ่มกลุ่มทันที
#门禁参数设置
acc_param_autoEventDev=ดาวน์โหลดจำนวนบันทึกเหตุการณ์ของอุปกรณ์พร้อมกันอัตโนมัติ
acc_param_autoEventTime=ดาวน์โหลดบันทึกเหตุการณ์ที่ช่วงเวลาพร้อมกันอัตโนมัติ
acc_param_noRepeat=ไม่มีการทำซ้ำที่อยู่อีเมลล์ที่ได้รับอนุญาติ การุณากรอกข้อมูลอีกครั้ง
acc_param_most18=เพิ่มที่อยู่อีเมลล์ได้สูงสุด 18 อีเมลล์
acc_param_deleteAlert=ไม่สามารถล้างกล่องที่อยู่อีเมลล์ทั้งหมด
acc_param_invalidOrRepeat=ข้อผิดพลาดรูปแบบที่อยู่อีเมลหรือการทำซ้ำที่อยู่อีเมล
#全局联动
acc_globalLinkage_noSupport=ประตูที่เลือกในปัจจุบันไม่รองรับฟังก์ชันการล็อคและปลดล็อค
acc_globalLinkage_trigger=ทริกเกอร์โกลบอลลิ้งเอจ
acc_globalLinkage_noAddPerson=กฎของลิ้งเอจไม่สามารถมีทริกเกอร์บุคลากรที่เกี่ยวข้อง โดยค่าเริ่มต้นจะบังคับใช้กับทุกคน!
acc_globalLinkage_selectAtLeastOne=โปรดเลือกทริกเกอร์บการเชื่อมโยงอย่างน้อยหนึ่ง
acc_globalLinkage_selectTrigger=กรุณาเพิ่มเงื่อนไขการลิ้งเอจ!
acc_globalLinkage_selectInput=กรุณาเพิ่มจุดการป้อนข้อมูล!
acc_globalLinkage_selectOutput=กรุณาเพิ่มจุดการส่งออก!
acc_globalLinkage_audioRemind=ลิ้งเอจเสียงเตือน
acc_globalLinkage_audio=ลิ้งเอจเสียง
acc_globalLinkage_isApplyToAll=นำไปใช้กับพนักงานทุกคน
acc_globalLinkage_scope=ช่วงพนักงาน
acc_globalLinkage_everyPerson=ใดๆ
acc_globalLinkage_selectedPerson=เลือก
acc_globalLinkage_noSupportPerson=ไม่รองรับ
acc_globalLinkage_reselectInput=ประเภทของเงื่อนไขเรียกมีการเปลี่ยนแปลง โปรดเลือกจุดที่ป้อนเข้าอีกครั้ง!
acc_globalLinkage_addPushDevice=กรุณาเพิ่มการสนับสนุนสำหรับอุปกรณ์ฟังก์ชั่นนี้
#其他
acc_InputMethod_tips=โปรดเปลี่ยนไปใช้โหมดการป้อนข้อมูลภาษาอังกฤษ!
acc_device_systemCheckTip=ไม่มีอุปกรณ์การเข้าถึง
acc_notReturnMsg=ไม่ให้ส่งกลับข้อมูล
acc_validity_period=ใบอนุญาตได้ผ่านช่วงเวลาของความถูกต้อง ฟังก์ชั่นไม่สามารถดำเนินงานได้!
acc_device_pushMaxCount=ระบบมีอยู่แล้วใน {0} อุปกรณ์  ใบอนุญาตถึงขีดจำกัด ไม่สามารถเพิ่มอุปกรณ์!
acc_device_videoHardwareLinkage=ตั้งค่าลิ้งเอจฮาร์ดแวร์วิดีโอ
acc_device_videoCameraIP=พอร์ตกล้องไอพี
acc_device_videoCameraPort=พอร์ตกล้องวิดีโอ
acc_location_unable=จุดที่เหตุการณ์ที่เกิดขึ้นไม่ได้ถูกเพิ่มไปยังแผนที่อิเล็กทรอนิกส์ ไม่สามารถที่จะหาตำแหน่งที่เฉพาะเจาะจงได้!
acc_device_wgDevMaxCount=อุปกรณ์ถึงขีด จำกัด ใบอนุญาตแล้วไม่สามารถแก้ไขการตั้งค่าได้!
#自定义报警事件
acc_deviceEvent_selectSound=กรุณาเลือกไฟล์เสียง
acc_deviceEvent_batchSetSoundErr=ชุดการตั้งสัญญาณเตือนเสียงผิดปกติ
acc_deviceEvent_batchSet=ตั้งค่าเสียง
acc_deviceEvent_sound=เหตุการณ์เสียง
acc_deviceEvent_exist=มีอยู่แล้ว
acc_deviceEvent_upload=อัพโหลดอีกครั้ง
#查询门最近发生事件
acc_doorEventLatestHappen=สอบถามเหตุการณ์ที่เกิดขึ้นล่าสุดจากประตู
#门禁人员信息
acc_pers_delayPassage=หน่วงเวลาเปิดปกติ
#设备容量提示
acc_dev_usageConfirm=มีปริมาณที่เกินกว่า 90% ของอุปกรณ์
acc_dev_immediateCheck=ตรวจสอบทันที
acc_dev_inSoftware=ในโปรแกรม
acc_dev_inFirmware=ในเฟิร์มแวร์
acc_dev_get=ตรวจสอบ
acc_dev_getAll=ตรวจสอบทั้งหมด
acc_dev_loadError=โหลดล้มเหลว
#Reader
acc_reader_inout=เข้า/ออก
acc_reader_lightRule=กฏของแสง
acc_reader_defLightRule=กฏของการคืนค่าเริ่มต้น
acc_reader_encrypt=การเข้ารหัสลับ
acc_reader_allReaderOfCurDev=หัวอ่านทั้งหมดของอุปกรณ์ปัจจุบัน
acc_reader_tip1=การเข้ารหัสจะถูกคัดลอกไปยังผู้อ่านทั้งหมดของอุปกรณ์ปัจจุบัน !
acc_reader_tip2=ตัวเลือกโหมด ID ใช้งานได้เฉพาะสำหรับ readheads ที่รองรับคุณสมบัตินี้!
acc_reader_tip3=ประเภทโปรโตคอล RS485 จะถูกคัดลอกไปยังเครื่องอ่านทั้งหมดในอุปกรณ์ปัจจุบัน การตั้งค่าจะมีผลหลังจากรีสตาร์ทอุปกรณ์!
acc_reader_tip4=ตัวเลือกในการซ่อนข้อมูลบุคลากรบางส่วนจะถูกคัดลอกไปยังผู้อ่านทั้งหมดของอุปกรณ์เดียวกันตามค่าเริ่มต้น!
acc_reader_commType=ประเภทการเชื่อมต่อ
acc_reader_commAddress=ที่อยู่การเชื่อมต่อ
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=วีแกนด์
acc_readerCommType_wg485=วีแกนด์/RS485
acc_readerCommType_disable=ปิดการใช้งาน
acc_readerComAddress_repeat=ที่อยู่การเชื่อมต่อซ้ำ
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=ที่อยู่ RS485
acc_readerCommType_wgAddress=ที่อยู่วีแกนด์
acc_reader_macError=กรุณากรอกที่อยู่ Mac ในรูปแบบที่ถุกต้อง !
acc_reader_machineType=ประเภทหัวอ่าน
acc_reader_readMode=โหมด
acc_reader_readMode_normal=โหมดปกติ
acc_reader_readMode_idCard=โหมดบัตร ID
acc_reader_note=คำแนะนำ: เฉพาะอุปกรณ์ควบคุมการเข้าถึงในพื้นที่เดียวกัน ({0}) เท่านั้นที่สามารถเลือกกล้องได้
acc_reader_rs485Type=ประเภทโปรโตคอล RS485
acc_reader_userLock=ล็อคการเข้าถึงของพนักงาน
acc_reader_userInfoReveal=ยกเลิกบางส่วนของข้อมูลพนักงาน
#operat
acc_operation_pwd=รหัสผ่านการดำเนินงาน
acc_operation_pwd_error=รหัสผ่านล้มเหลว
acc_new_input_not_same=รหัสใหม่อินพุทไม่ตรงกัน
acc_op_set_keyword=การตั้งค่าการอนุญาตรหัส
acc_op_old_key=รหัสเก่า
acc_op_new_key=รหัสใหม่
acc_op_cofirm_key=ยืนยันรหัส
acc_op_old_key_error=รหัสเก่าล้มเหลว
#验证方式规则
acc_verifyRule_name=ชื่อกฏ
acc_verifyRule_door=การยืนยันประตู
acc_verifyRule_person=การยืนยันพนักงาน
acc_verifyRule_copy=คัดลอกการตั้งค่าของวันจันทร์ไปยังวันอื่น ๆ ของวันธรรมดา:
acc_verifyRule_tip1=กรุณาเลือกโหมดการยืนยันอย่างน้อยหนึ่งโหมด !
acc_verifyRule_tip2=หากกฎมีโหมดการตรวจสอบพนักงานสามารถ คุณสามารถประตูหัวอ่าน Wiegand เท่านั้น
acc_verifyRule_tip3=เครื่องอ่าน RS485 สามารถทำตามการตรวจสอบประตูเท่านั้นไม่สนับสนุนการตรวจสอบพนักงานที่ต้องการ
acc_verifyRule_oldVerifyMode=โหมดตรวจสอบเก่า
acc_verifyRule_newVerifyMode=โหมดการตรวจสอบใหม่
acc_verifyRule_newVerifyModeSelectTitle=เลือกวิธีการตรวจสอบใหม่
acc_verifyRule_newVerifyModeNoSupportTip=ไม่มีอุปกรณ์ที่รองรับวิธีการตรวจสอบใหม่!
#Wiegand Test
acc_wiegand_beforeCard=ความยาวของบัตรใหม่({0} บิต)ไม่สามารถเท่ากับบัตรสุดท้ายได้ !
acc_wiegand_curentCount=ความยาวหมายเลขบัตรในปัจจุบัน : {0} บิต
acc_wiegand_card=บัตร
acc_wiegand_readCard=อ่านบัตร
acc_wiegand_clearCardInfo=ล้างข้อมูลบัตร
acc_wiegand_originalCard=หมายเลขบัตรแบบเดิม
acc_wiegand_recommendFmt=แนะนำรูปแบบบัตร
acc_wiegand_parityFmt=รูปแบบความเท่าเทียมกัน
acc_wiegand_withSizeCode=คำนวณรหัสไซต์โดยอัตโนมัติขณะที่โค้ดไซต์ว่างไว้
acc_wiegand_tip1=การ์ดเหล่านี้อาจไม่ใช่ของแบทช์ชุดเดียวกัน
acc_wiegand_tip2=ไซต์โค้ด:{0},หมายเลขบัตร:{1}, จับคู่หมายเลขบัตรเดิมล้มเหลว กรุณาตรวจสอบการป้อนไซต์โค้ดเเละหมายเลขบัตรอีกครั้ง!
acc_wiegand_tip3=ป้อนหมายเลขบัตร({0})ไม่สามารถจับคู่กับหมายลเขบัตรเดิมได้ กรุณาตรวจสอบอีกครั้ง!
acc_wiegand_tip4=ป้อนไซต์โค้ด({0})ไม่สามารถจับคู่กับหมายลเขบัตรเดิมได้ กรุณาตรวจสอบอีกครั้ง!
acc_wiegand_tip5=ถ้าคุณต้องการใช้คุณสมบัตินี้ กรุณาเก็บคอลัมน์ไซต์โค้ดทั้งหมดให้ว่างเปล่า!
acc_wiegand_warnInfo1=เมื่อคุณต้องอ่านบัตรใหม่โปรดสลับไปใช้บัตรต่อไปด้วยตนเอง
#LCD实时监控
acc_leftMenu_LCDRTMonitor=พนักงานของการเข้า / ออก
acc_LCDRTMonitor_current=ข้อมูลพนักงานปัจจุบัน
acc_LCDRTMonitor_previous=ข้อมูลพนักงานก่อนหน้า
#api
acc_api_levelIdNotNull=รหัสกลุ่มสิทธิ์ไม่สามารถเว้นว่างได้
acc_api_levelExist=มีกลุ่มผู้มีอำนาจ
acc_api_levelNotExist=ไม่พบสิทธิ์
acc_api_areaNameNotNull=พื้นที่ต้องไม่ว่างเปล่า
acc_api_levelNotHasPerson=ไม่มีพนักงานที่อยู่ในกลุ่มได้รับอนุญาต
acc_api_doorIdNotNull=รหัสประตูต้องไม่ว่างเปล่า
acc_api_doorNameNotNull=ชื่อประตูต้องไม่ว่างเปล่า
acc_api_doorIntervalSize=ระยะเวลาเปิดประตูอยู่ระหว่าง 1 ~ 254
acc_api_doorNotExist=ไม่มีประตูอยู่
acc_api_devOffline=อุปกรณ์ออฟไลน์หรือปิดใช้งาน
acc_api_devSnNotNull=SN อุปกรณ์ต้องไม่ว่างเปล่า
acc_api_timesTampNotNull=การประทับเวลาไม่สามารถว่างได้
acc_api_openingTimeCannotBeNull=เวลาเปิดประตูต้องไม่ว่างเปล่า
acc_api_parameterValueCannotBeNull=ค่าพารามิเตอร์ต้องไม่ว่างเปล่า
acc_api_deviceNumberDoesNotExist=ไม่มีหมายเลขซีเรียลของอุปกรณ์
acc_api_readerIdCannotBeNull=ID ผู้อ่านต้องไม่ว่างเปล่า
acc_api_theReaderDoesNotExist=ไม่มีหัวอ่าน
acc_operate_door_notInValidDate=ขณะนี้ไม่ได้อยู่ในเวลาเปิดระยะไกลที่ถูกต้องถ้าคุณต้องการที่จะติดต่อผู้ดูแลระบบ!
acc_api_doorOffline=ประตูออฟไลน์หรือปิดใช้งาน
#门禁信息自动导出
acc_autoExport_title=การส่งออกข้อมูลอัตโนมัติ
acc_autoExport_frequencyTitle=ส่งออกโดยอัตโนมัติ
acc_autoExport_frequencyDay=ทุกวัน
acc_autoExport_frequencyMonth=ต่อเดือน
acc_autoExport_firstDayMonth=วันแรกของเดือน
acc_autoExport_specificDate=วันที่ส่งออก
acc_autoExport_exportModeTitle=โหมดการส่งออก
acc_autoExport_dailyMode=ข้อมูลรายวัน
acc_autoExport_monthlyMode=ข้อมูลรายเดือน(ข้อมูลและข้อมูลทั้งหมดในเดือนนี้ของเดือนที่แล้ว)
acc_autoExport_allMode=ข้อมูลทั้งหมด(มากถึง 30,000 ข้อความ)
acc_autoExport_recipientMail=กำลังรับกล่องจดหมาย
#First In And Last Out
acc_inOut_inReaderName=ชื่อหัวอ่านแรกสุด
acc_inOut_firstInTime=เวลาเข้าแรกสุด
acc_inOut_outReaderName=ชื่อหัวอ่านหลังสุด
acc_inOut_lastOutTime=เวลาที่ออกหลังสุด
#防疫参数
acc_dev_setHep=ตั้งค่าพารามิเตอร์การตรวจจับหน้ากาก&อุณหภูมิ
acc_dev_enableIRTempDetection=เปิดใช้งานการตรวจวัดอุณหภูมิด้วยอินฟราเรด
acc_dev_enableNormalIRTempPass=ปฏิเสธการเข้าถึงเมื่ออุณหภูมิเกินช่วง
acc_dev_enableMaskDetection=เปิดใช้งานการตรวจจับหน้ากาก
acc_dev_enableWearMaskPass=ปฏิเสธการเข้าถึงโดยไม่ใช้หน้ากาก
acc_dev_tempHighThreshold=เกณฑ์การเตือนอุณหภูมิสูง
acc_dev_tempUnit=หน่วยอุณหภูมิ
acc_dev_tempCorrection=การแก้ไขความเบี่ยงเบนของอุณหภูมิ
acc_dev_enableUnregisterPass=อนุญาตให้พนักงานที่ไม่ลงทะเบียนสามารถเข้าถึงได้
acc_dev_enableTriggerAlarm=สัญญาณเตือนทริกเกอร์ภายนอก
#联动邮件
acc_mail_temperature=อุณหภูมิร่างกาย
acc_mail_mask=ไม่ว่าจะใส่หน้ากาก
acc_mail_unmeasured=ไม่ผ่านการทดสอบ
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort Global Events
acc_digifort_chooseDigifortEvents=เลือก Digifort Global Events
acc_digifort_eventExpiredTip=ถ้าเหตุการณ์ทั่วโลกถูกลบจากเซิร์ฟเวอร์ Digifort มันจะเป็นสีแดง
acc_digifort_checkConnection=โปรดตรวจสอบว่าข้อมูลการเชื่อมต่อของเซิร์ฟเวอร์ Digifort ถูกต้องหรือไม่
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=เพิ่มผู้ติดต่อ
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=ตั้งค่าพารามิเตอร์เพิ่มเติม
acc_extendParam_faceUI=จอแสดงผลส่วนต่อประสาน
acc_extendParam_faceParam=พารามิเตอร์ใบหน้า
acc_extendParam_accParam=พารามิเตอร์ควบคุมการเข้าถึง
acc_extendParam_intercomParam=พารามิเตอร์อินเตอร์คอมภาพ
acc_extendParam_volume=ปริมาณ
acc_extendParam_identInterval=ช่วงการระบุ (ms)
acc_extendParam_historyVerifyResult=แสดงผลการตรวจสอบประวัติ
acc_extendParam_macAddress=แสดงที่อยู่ MAC
acc_extendParam_showIp=แสดงที่อยู่ IP
acc_extendParam_24HourFormat=แสดงรูปแบบ 24 ชั่วโมง
acc_extendParam_dateFormat=รูปแบบวันที่
acc_extendParam_1NThreshold=เกณฑ์ 1: N
acc_extendParam_facePitchAngle=ระดับมุมของใบหน้า
acc_extendParam_faceRotationAngle=มุมการหมุนหน้า
acc_extendParam_imageQuality=คุณภาพของภาพ
acc_extendParam_miniFacePixel=พิกเซลใบหน้าขั้นต่ำ
acc_extendParam_biopsy=เปิดใช้งานการตรวจสิ้งมีชีวิต
acc_extendParam_showThermalImage=แสดงภาพความร้อน
acc_extendParam_attributeAnalysis=เปิดใช้งานการวิเคราะห์คุณสมบัติ
acc_extendParam_temperatureAttribute=คุณลักษณะการตรวจจับอุณหภูมิ
acc_extendParam_maskAttribute=คุณลักษณะการตรวจจับหน้ากาก
acc_extendParam_minTemperature=อุณหภูมิต่ำสุด
acc_extendParam_maxTemperature=อุณหภูมิสูงสุด
acc_extendParam_gateMode=โหมดของโหมดเกท
acc_extendParam_qrcodeEnable=เปิดฟังก์ชั่น QRCode
#可视对讲
acc_dev_intercomServer=ที่อยู่บริการอินเตอร์คอมภาพ
acc_dev_intercomPort=เทอร์มินัลบริการอินเตอร์คอมภาพ
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=ระดับซิงโครไนซ์
# 夏令时名称
acc_dsTimeUtc_none=ไม่ตั้งค่า
acc_dsTimeUtc_AreaNone=ไม่มีเวลา DST ในพื้นที่
acc_dsTimeUtc1000_0=แคนเบอร์รา, เมลเบิร์น, ซิดนีย์
acc_dsTimeUtc1000_1=โฮบาร์ต
acc_dsTimeUtc_0330_0=นิวฟันด์แลนด์
acc_dsTimeUtc_1000_0=หมู่เกาะ Aleutian
acc_dsTimeUtc_0200_0=กลางมหาสมุทรแอตแลนติก
acc_dsTimeUtc0930_0=แอดิเลด
acc_dsTimeUtc_0100_0=หมู่เกาะแอสซอร์
acc_dsTimeUtc_0400_0=เวลาแอตแลนติกแคนาดา
acc_dsTimeUtc_0400_1=ซานดิเอโก
acc_dsTimeUtc_0400_2=จุดสูงสุด
acc_dsTimeUtc_0300_0=กรีนแลนด์
acc_dsTimeUtc_0300_1=เซนต์ปิแอร์และไมโครนีล
acc_dsTimeUtc0200_0=คีชีเนา
acc_dsTimeUtc0200_1=เฮลซิงกิเคียฟริกาโซเฟียทารินวิลนุส
acc_dsTimeUtc0200_2=บูคาเรสต์เอเธนส์
acc_dsTimeUtc0200_3=กรุงเยรูซาเล็ม
acc_dsTimeUtc0200_4=อัมมาน
acc_dsTimeUtc0200_5=เบรุต
acc_dsTimeUtc0200_6=ดามัสกัส
acc_dsTimeUtc0200_7=กาซาเฮโบรน
acc_dsTimeUtc0200_8=จูบา
acc_dsTimeUtc_0600_0=เวลากลางสหรัฐอเมริกาและแคนาดา
acc_dsTimeUtc_0600_1=Guadalajara เมืองเม็กซิโก
acc_dsTimeUtc_0600_2=เกาะอีสเตอร์
acc_dsTimeUtc1300_0=ประเทศซามัว
acc_dsTimeUtc_0500_0=ฮาวานา
acc_dsTimeUtc_0500_1=เวลาทางทิศตะวันออกของสหรัฐอเมริกาและแคนาดา
acc_dsTimeUtc_0500_2=เฮติ
acc_dsTimeUtc_0500_3=รัฐอินเดียนาตะวันออก
acc_dsTimeUtc_0500_4=เติร์กและเคคอส
acc_dsTimeUtc_0800_0=เวลาในมหาสมุทรแปซิฟิกสหรัฐอเมริกาและแคนาดา
acc_dsTimeUtc_0800_1=รัฐแคลิฟอร์เนียตอนล่าง
acc_dsTimeUtc0330_0=เตหะราน
acc_dsTimeUtc0000_0=ดับลินเอดินบะระลิสบอนลอนดอน
acc_dsTimeUtc1200_0=ประเทศฟิจิ
acc_dsTimeUtc1200_1=ปีเตอร์ pavlovsk ฉ้อโกง
acc_dsTimeUtc1200_2=โอ๊คแลนด์เวลลิงตัน
acc_dsTimeUtc1100_0=เกาะนอร์โฟล์ค
acc_dsTimeUtc_0700_0=ชิวาว่าลาปาซมอสแลนด์
acc_dsTimeUtc_0700_1=เวลาภูเขาในสหรัฐอเมริกาและแคนาดา
acc_dsTimeUtc0100_0=เบลเกรดบราติสลาวาบูดาเปสลูบูร์น่าปราก
acc_dsTimeUtc0100_1=ซาราเจโวสกอร์ปรีวอร์ซอซาเกร็บ
acc_dsTimeUtc0100_2=คาซาบลังก้า
acc_dsTimeUtc0100_3=บรัสเซลส์โคเปนเฮเกนมาดริดปารีส
acc_dsTimeUtc0100_4=อัมสเตอร์ดัมเบอร์ลินเบอร์นี่โรมสตอกโฮล์มเวียนนา
acc_dsTimeUtc_0900_0=มลรัฐอะแลสกา
#安全点(muster point)
acc_leftMenu_accMusterPoint=จุดมัสเตอร์
acc_musterPoint_activate=เปิดใช้งาน
acc_musterPoint_addDept=เพิ่มแผนก
acc_musterPoint_delDept=ลบแผนก
acc_musterPoint_report=รายงานคะแนนรวม
acc_musterPointReport_sign=ลงชื่อเข้าใช้ด้วยตนเอง
acc_musterPointReport_generate=สร้างรายงาน
acc_musterPoint_addSignPoint=เพิ่มจุดเข้าสู่ระบบ
acc_musterPoint_delSignPoint=ลบจุดลงชื่อเข้าใช้
acc_musterPoint_selectSignPoint=โปรดเพิ่มเครื่องหมายจุด!
acc_musterPoint_signPoint=จุดลงชื่อเข้าใช้
acc_musterPoint_delFailTip=มีจุดรวมพลที่เปิดใช้งานแล้วและไม่สามารถลบได้!
acc_musterPointReport_enterTime=ป้อน Time
acc_musterPointReport_dataAnalysis=การวิเคราะห์ข้อมูล
acc_musterPointReport_safe=Safe
acc_musterPointReport_danger=Danger
acc_musterPointReport_signInManually=manual punch
acc_musterPoint_editTip=จุดรวมตัวเปิดใช้งานอยู่และไม่สามารถแก้ไขได้!
acc_musterPointEmail_total=จำนวนผู้เข้าร่วมโดยประมาณ:
acc_musterPointEmail_safe=เช็คอิน (ปลอดภัย):
acc_musterPointEmail_dangerous=มีความเสี่ยง:
acc_musterPoint_messageNotification=การแจ้งเตือนข้อความเมื่อเปิดใช้งาน
acc_musterPointReport_sendEmail=วางแผนผลักดันรายงาน
acc_musterPointReport_sendInterval=ส่งช่วงเวลา
acc_musterPointReport_sendTip=โปรดตรวจสอบให้แน่ใจว่าการกำหนดค่าวิธีการแจ้งเตือนที่เลือกสำเร็จมิฉะนั้นการแจ้งเตือนจะไม่ถูกส่งอย่างถูกต้อง!
acc_musterPoint_mailSubject=ประกาศการชุมนุมฉุกเฉิน
acc_musterPoint_mailContent=โปรดรวบรวมที่ "{0}" ทันทีและลงชื่อเข้าใช้ที่อุปกรณ์ "{1}" ขอบคุณ!
acc_musterPointReport_mailHead=หวัดดี นี่เป็นรายงานฉุกเฉิน ขอให้ทบทวน
acc_musterPoint_visitorsStatistics=สถิติผู้เข้าชม
# 报警监控
acc_alarm_priority=ลำดับความสำคัญ
acc_alarm_total=ทั้งหมด
acc_alarm_today=บันทึกวันนี้
acc_alarm_unhandled=ยังไม่ได้รับการยืนยัน
acc_alarm_inProcess=จัดการกับ
acc_alarm_acknowledged=คอนเฟิร์ม
acc_alarm_top5=ห้าอันดับแรกของการแจ้งเตือน
acc_alarm_monitoringTime=ตรวจสอบเวลา
acc_alarm_history=ประวัติของการประมวลผลสัญญาณเตือนภัย
acc_alarm_acknowledgement=บันทึกการประมวลผล
acc_alarm_eventDescription=รายละเอียดเหตุการณ์
acc_alarm_acknowledgeText=เมื่อเลือกรายละเอียดของเหตุการณ์การแจ้งเตือนจะถูกส่งไปยังกล่องจดหมายที่ระบุ
acc_alarm_emailSubject=เพิ่มบันทึกการจัดการเหตุการณ์ปลุก
acc_alarm_mute=เงียบสงัด
acc_alarm_suspend=พัก
acc_alarm_confirmed=เหตุการณ์นี้ได้รับการยืนยันแล้ว
acc_alarm_list=บันทึกการปลุก
#ntp
acc_device_setNTPService=การตั้งค่าเซิร์ฟเวอร์ NTP
acc_device_setNTPServiceTip=ป้อนที่อยู่เซิร์ฟเวอร์หลายรายการ โดยคั่นด้วยเครื่องหมายจุลภาค (,) หรือเครื่องหมายอัฒภาค (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=ในการดำเนินงานของตัวควบคุมการเข้าถึงซูเปอร์ผู้ใช้ไม่ได้รับผลกระทบจากโซนเวลาย้อนกลับและล็อคซึ่งกันและกันมีความสําคัญมากในการเปิดประตู
acc_editPerson_delayPassageTip=เพิ่มเวลารอผ่านจุดเชื่อมต่อ เหมาะสำหรับคนพิการหรือคนพิการอื่นๆ
acc_editPerson_disabledTip=ระดับการเข้าถึงสำหรับคนพิการชั่วคราว
#门禁向导
acc_guide_title=ตัวช่วยสร้างการตั้งค่าโมดูลควบคุมการเข้าถึง
acc_guide_addPersonTip=คุณต้องเพิ่มบุคคลและข้อมูลรับรองที่เกี่ยวข้อง (ใบหน้าหรือลายนิ้วมือ หรือการ์ดหรือฝ่ามือหรือรหัสผ่าน) หากคุณเพิ่มแล้ว ให้ข้ามขั้นตอนนี้โดยตรง
acc_guide_timesegTip=โปรดกำหนดค่าช่วงเวลาเปิดที่ถูกต้อง
acc_guide_addDeviceTip=โปรดเพิ่มอุปกรณ์ที่เกี่ยวข้องเป็นจุดเชื่อมต่อ
acc_guide_addLevelTip=เพิ่มระดับการควบคุมการเข้าถึง
acc_guide_personLevelTip=กำหนดสิทธิ์ควบคุมการเข้าถึงที่เกี่ยวข้องให้กับบุคคลนั้น
acc_guide_rtMonitorTip=ตรวจสอบบันทึกการควบคุมการเข้าถึงตามเวลาจริง
acc_guide_rtMonitorTip2=ดูบันทึกการเข้าถึงแบบเรียลไทม์หลังจากเพิ่มพื้นที่ที่เป็นของคุณและประตูที่สอดคล้องกัน
#查看区域内人员
acc_zonePerson_cleanCount=ล้างสถิติคนเข้าและออก
acc_zonePerson_inCount=สถิติจำนวนคนเข้า
acc_zonePerson_outCount=สถิติของจำนวนคนที่ออกไป
#biocv460
acc_device_validFail=ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้องและการตรวจสอบล้มเหลว!
acc_device_pwdRequired=สามารถใส่จำนวนเต็มได้สูงสุด 6 หลักเท่านั้น