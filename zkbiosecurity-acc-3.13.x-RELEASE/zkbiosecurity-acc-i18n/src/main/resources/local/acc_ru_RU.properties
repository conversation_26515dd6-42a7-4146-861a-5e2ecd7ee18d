#[1]左侧菜单
acc_module=Доступ
acc_leftMenu_accDev=Устройства
acc_leftMenu_auxOut=Дополнительные выходы
acc_leftMenu_dSTime=Летнее время
acc_leftMenu_access=Управление доступом
acc_leftMenu_door=Двери
acc_leftMenu_accRule=Правила доступа
acc_leftMenu_interlock=Шлюз
acc_leftMenu_antiPassback=Запрет двойного прохода
acc_leftMenu_globalLinkage=Глобальные привязки
acc_leftMenu_firstOpen=1-й сотрудник - режим открыто
acc_leftMenu_combOpen=Открытие по комиссионированию
acc_leftMenu_personGroup=Группа комиссионирования
acc_leftMenu_level=Уровни доступа
acc_leftMenu_electronicMap=Карта
acc_leftMenu_personnelAccessLevels=Уровни доступа сотрудников
acc_leftMenu_searchByLevel=По уровню доступа
acc_leftMenu_searchByDoor=Права доступа по двери
acc_leftMenu_expertGuard=Расширенные функции
acc_leftMenu_zone=Зона
acc_leftMenu_readerDefine=Направление считывателя
acc_leftMenu_gapbSet=Глобальный запрет двойного прохода
acc_leftMenu_whoIsInside=Местонахождение
acc_leftMenu_whatRulesInside=Применяемые правила
acc_leftMenu_occupancy=Контроль числа людей в помещении
acc_leftMenu_route=Контроль маршрута
acc_leftMenu_globalInterlock=Глобальный шлюз
acc_leftMeue_globalInterlockGroup=Групповой глобальный шлюз
acc_leftMenu_dmr=Правило время пребывания
acc_leftMenu_personLimit=Доступ сотрудника
acc_leftMenu_verifyModeRule=Режим проверки
acc_leftMenu_verifyModeRulePersonGroup=Групповой режим проверки
acc_leftMenu_extDev=Панель I/O
acc_leftMenu_firstInLastOut=Первый вход посл. выход
acc_leftMenu_accReports=Отчеты о контроле доступа
#[3]门禁时间段
acc_timeSeg_entity=Временная зона
acc_timeSeg_canNotDel=Временной период используется и не может быть удален!
#[4]门禁设备--公共的在common中
acc_common_ruleName=Имя правила
acc_common_hasBeanSet=Задано
acc_common_notSet=Не задано
acc_common_hasBeenOpened=Было открыто
acc_common_notOpened=Не открывалось
acc_common_partSet=Часть набора
acc_common_linkageAndApbTip=Вы настраиваете одновременно привязки и глобальные привязки, запрет двойного прохода и глобальный запрет двойного прохода - возможны конфликты.
acc_common_vidlinkageTip=Убедитесь, что соответствующая точка входа связана с доступным видеоканалом, в противном случае функция привязки с видео не будет работать!
acc_common_accZoneFromTo=Нельзя установить одну и ту же зону
acc_common_logEventNumber=ID события
acc_common_bindOrUnbindChannel=Привязать/отвязать видеокамеру
acc_common_boundChannel=Привязанная видеокамера
#设备信息
acc_dev_iconType=Тип пиктограммы
acc_dev_carGate=Шлагбаум
acc_dev_channelGate=Распашной турникет
acc_dev_acpType=Тип контроллера
acc_dev_oneDoorACP=Однодверный контроллер
acc_dev_twoDoorACP=Двудверный контроллер
acc_dev_fourDoorACP=Четырёхдверный контроллер
acc_dev_onDoorACD=Standalone устройство
acc_dev_switchToTwoDoorTwoWay=Переключить на две двусторонних двери
acc_dev_addDevConfirm2=Подсказка: подключение к устройству выполнено, но тип контроллера отличается от фактического, изменить его на {0} контроллер дверей(ми). Продолжить добавление?
acc_dev_addDevConfirm4=Устройство standalone. Продолжить добавление?
acc_dev_oneMachine=Standalone устройство
acc_dev_fingervein=Вены пальца
acc_dev_control=Управление
acc_dev_protocol=Тип протокола
acc_ownedBoard=Пренад. плата расширения
#设备操作
acc_dev_start=Начать
acc_dev_accLevel=Права доступа
acc_dev_timeZoneAndHoliday=Временная зона, праздники
acc_dev_linkage=Привязка
acc_dev_doorOpt=Параметры двери
acc_dev_firstPerson=1-сотрудник открытие двери
acc_dev_multiPerson=Комиссионирование открытия двери
acc_dev_interlock=Шлюз
acc_dev_antiPassback=Запрет двойного прохода
acc_dev_wiegandFmt=Формат Wiegand
acc_dev_outRelaySet=Настройки доп. выходов
acc_dev_backgroundVerifyParam=Функции фоновой проверки
acc_dev_getPersonInfoPrompt=Пожалуйста, убедитесь, что вы успешно получили информацию о сотруднике, в противном случае произойдет ошибка. Продолжить?
acc_dev_getEventSuccess=Успешно получил события.
acc_dev_getEventFail=Не удалось получить событие(я).
acc_dev_getInfoSuccess=Успешно получил информацию.
acc_dev_getInfoXSuccess=Получено {0} успешно.
acc_dev_getInfoFail=Не удалось получить информацию.
acc_dev_updateExtuserInfoFail=Не удалось обновить расширенную информацию о проходах сотрудников, пожалуйста, попробуйте получить информацию снова.
acc_dev_getPersonCount=Узнать количество пользователей
acc_dev_getFPCount=Узнать количество отпечатков пальцев
acc_dev_getFVCount=Узнать количество вен пальцев
acc_dev_getFaceCount=Узнать количество шаблонов лиц
acc_dev_getPalmCount=Узнать количество рисунков ладоней
acc_dev_getBiophotoCount=Узнать количество фото лиц
acc_dev_noData=Нет данных возвращаемых с устройства.
acc_dev_noNewData=Нет новых событий в устройстве.
acc_dev_softLtDev=В ПО больше людей, чем в устройстве.
acc_dev_personCount=Количество людей:
acc_dev_personDetail=Детали описаны далее:
acc_dev_softEqualDev=Количество людей в ПО и устройстве одинаково.
acc_dev_softGtDev=В устройстве больше людей, чем в ПО.
acc_dev_cmdSendFail=Не удалось отправить команды, повторите отправку.
acc_dev_issueVerifyParam=Настроить функции фоновой проверки
acc_dev_verifyParamSuccess=Функции фоновой проверки применены
acc_dev_backgroundVerify=Фоновая проверка
acc_dev_selRightFile=Пожалуйста, выберите правильный файл обновления!
acc_dev_devNotOpForOffLine=Устройство вне сети, пожалуйста, попробуйте позже
acc_dev_devNotSupportFunction=Устройство не поддерживает эту функцию
acc_dev_devNotOpForDisable=Устройство отключено, пожалуйста, попробуйте позже
acc_dev_devNotOpForNotOnline=Устройство вне сети или отключено, пожалуйста, попробуйте позже
acc_dev_getPersonInfo=Получить данные о сотрудниках
acc_dev_getFPInfo=Получить данные об отпечатках пальцев
acc_dev_getFingerVeinInfo=Получить данные о венах пальцев
acc_dev_getPalmInfo=Получить данные о рисунках ладони
acc_dev_getBiophotoInfo=Получить данные о сотрудниках
acc_dev_getIrisInfo=Получение информации об радужной оболочке
acc_dev_disable=отключено, выберите снова
acc_dev_offlineAndContinue=вне сети, продолжить?
acc_dev_offlineAndSelect=вне сети.
acc_dev_opAllDev=Все устройства
acc_dev_opOnlineDev=Устройство в сети
acc_dev_opException=обработать нарушения
acc_dev_exceptionAndConfirm=Превышение времени соединения с устройством, операция не удалась. Проверьте подключение к сети
acc_dev_getFaceInfo=Получить информацию о шаблонах лиц
acc_dev_selOpDevType=Выберите тип устройства для действия:
acc_dev_hasFilterByFunc=Показывать только устройства в сети и поддерживающие эту функцию!
acc_dev_masterSlaveMode=Режим master/slave RS485
acc_dev_master=Host
acc_dev_slave=Slave
acc_dev_modifyRS485Addr=Изменить адрес RS485
acc_dev_rs485AddrTip=Пожалуйста, введите цифру от 1 до 63!
acc_dev_enableFeature=Устройства с вкл. фоновой проверкой
acc_dev_disableFeature=Устройства с выкл. фоновой проверкой
acc_dev_getCountOnly=Только подсчет количества
acc_dev_queryDevPersonCount=Запросить число сотрудников
acc_dev_queryDevVolume=Посмотреть вместимость устройства
acc_dev_ruleType=Тип правила
acc_dev_contenRule=Детали правила
acc_dev_accessRules=Посмотреть правила устройства
acc_dev_ruleContentTip=Между правилами разделитель '|'.
acc_dev_rs485AddrFigure=Код адреса RS485
acc_dev_addLevel=Добавить к уровню
acc_dev_personOrFingerTanto=Количество сотрудников или отпечатков пальцев превышают допустимые значения, синхронизация не удалась ...
acc_dev_personAndFingerUnit=(число)
acc_dev_setDstime=Установить летнее время
acc_dev_setTimeZone=Установить часовой пояс устройства
acc_dev_selectedTZ=Выбранный часовой пояс
acc_dev_timeZoneSetting=Настройка часового пояса ...
acc_dev_timeZoneCmdSuccess=Установка часового пояса успешно отправлена ...
acc_dev_enableDstime=Включить летнее время
acc_dev_disableDstime=Отключить летнее время
acc_dev_timeZone=Часовой пояс
acc_dev_dstSettingTip=Установка летнего времени ...
acc_dev_dstDelTip=Удаление летнего времени с устройства ...
acc_dev_enablingDst=Включение летнего времени
acc_dev_dstEnableCmdSuccess=Команда включения летнего времени успешно отправлена.
acc_dev_disablingDst=Отключение летнего времени.
acc_dev_dstDisableCmdSuccess=Команда отключения летнего времени успешно отправлена.
acc_dev_dstCmdSuccess=Установка летнего времени успешно отправлена.
acc_dev_usadst=Летнее время
acc_dev_notSetDst=Нет настроек
acc_dev_selectedDst=Выберите летнее время
acc_dev_configMasterSlave=Настройка Master-slave
acc_dev_hasFilterByUnOnline=Отображать только устройства в сети.
acc_dev_softwareData=Если вы обнаружили, что данные не соответствуют устройству, пожалуйста, синхронизируйте данные устройств перед повторной попыткой.
acc_dev_disabled=Отключенными устройствами нельзя управлять!
acc_dev_offline=Устройствами вне сети нельзя управлять!
acc_dev_noSupport=Устройства не поддерживают эту функцию и не могут её выполнить!
acc_dev_noRegDevTip=Это устройство не выбрано как устройство регистрации. Данные в ПО не будут обновлены. Хотите продолжить?
acc_dev_noOption=Нет подходящих вариантов
acc_dev_devFWUpdatePrompt=Это устройство не поддерживает функции временного отключения прав доступа и проверки времени доступа (см. Руководство пользователя).
acc_dev_panelFWUpdatePrompt=Это устройство не поддерживает функции временного отключения прав доступа и проверки времени доступа, обновить прошивку сейчас?
acc_dev_sendEventCmdSuccess=Команда удаления события успешно отправлена.
acc_dev_tryAgain=Пожалуйста, попробуйте снова.
acc_dev_eventAutoCheckAndUpload=Автоматическая проверка и загрузка событий
acc_dev_eventUploadStart=Начать загрузку событий.
acc_dev_eventUploadEnd=Загрузка событий завершена.
acc_dev_eventUploadFailed=Ошибка загрузки событий.
acc_dev_eventUploadPrompt=Версия прошивки устройства слишком старая, перед обновлением прошивки вы хотите:
acc_dev_backupToSoftware=Сохранить резервную копию в программном обеспечении
acc_dev_deleteEvent=Удалить старую запись
acc_dev_upgradePrompt=Версия прошивки устарела и может привести к ошибке. Перед обновлением прошивки создайте резервную копию данных.
acc_dev_conflictCardNo=Карта уже добавлена в систему для {0} другого сотрудника!
acc_dev_rebootAfterOperate=Операция прошла успешно, устройство позже перезагрузится.
acc_dev_baseOptionTip=Нарушение полученного основного параметра.
acc_dev_funOptionTip=Нарушение полученного параметра функции.
acc_dev_sendComandoTip=Не удалось отправить команду запроса параметров устройства.
acc_dev_noC3LicenseTip=Невозможно добавить устройство этого типа ({0})! Пожалуйста, свяжитесь с нашим отделом продаж.
acc_dev_combOpenDoorTip=({0}) Настроена функция комиссионирования двери, нельзя использовать одновременно с активированной функцией фоновой проверки.
acc_dev_combOpenDoorPersonCountTip=Группа {0} не может открывать более {1}!
acc_dev_addDevTip=Это применимо только к добавлению устройства с протоколом связи PULL!
acc_dev_addError=Ошибка добавления устройства, пропущены параметры ({0})!
acc_dev_updateIPAndPortError=Ошибка обновления порта и IP сервера...
acc_dev_transferFilesTip=Завершена проверка прошивки, передаю файлы
acc_dev_serialPortExist=Последовательный порт существует
acc_dev_isExist=Содержит устройство
acc_dev_description=Описание
acc_dev_searchEthernet=Поиск устройства Ethernet
acc_dev_searchRS485=Поиск устройства RS485
acc_dev_rs485AddrTip1=Начальный адрес RS485 не может быть больше конечного адреса
acc_dev_rs485AddrTip2=Диапазон поиска RS485 должен быть меньше 20
acc_dev_clearAllCmdCache=Очистить все команды
acc_dev_authorizedSuccessful=Авторизовано
acc_dev_authorize=Авторизовать
acc_dev_registrationDevice=Устройство регистрации
acc_dev_setRegistrationDevice=Назначить устройством регистрации
acc_dev_mismatchedDevice=Это устройство нельзя использовать на вашем рынке. Пожалуйста, свяжитесь с нашим отделом продаж, чтобы убедиться, что серийный номер устройства принадлежит к локальному рынку.
acc_dev_pwdStartWithZero=Пароль связи не может начинаться с нуля!
acc_dev_maybeDisabled=Текущая лицензия позволяет вам добавить еще {0} дверь(ей), дверь нового добавленного устройства, которая превышает лимит лицензии, будет заблокирована, продолжить?
acc_dev_Limit=Лицензия достигла максимального количества устройств, пожалуйста авторизуйтесь.
acc_dev_selectDev=Пожалуйста, выберите устройство!
acc_dev_cannotAddPullDevice=Нельзя добавить
acc_dev_notContinueAddPullDevice=В системе уже есть {0} pull-устройств(а), нельзя продолжить добавление! Пожалуйста, свяжитесь с нашим отделом продаж
acc_dev_deviceNameNull=Модель устройства пуста, нельзя добавить устройство!
acc_dev_commTypeErr=Тип связи не совпадает; нельзя добавить устройство!
acc_dev_inputDomainError=Пожалуйста, введите действительный адрес домена.
acc_dev_levelTip=В уровне более 5000 сотрудников, нельзя добавить устройство к этому уровню.
acc_dev_auxinSet=Настройки доп. входов
acc_dev_verifyModeRule=Правила режима проверки
acc_dev_netModeWired=Проводное
acc_dev_netMode4G=4G
acc_dev_netModeWifi=Wi-Fi
acc_dev_updateNetConnectMode=Сменить сетевое подключение
acc_dev_wirelessSSID=Имя SSID
acc_dev_wirelessKey=Пароль
acc_dev_searchWifi=Поиск Wi-Fi
acc_dev_testNetConnectSuccess=Это подключение успешное?
acc_dev_testNetConnectFailed=Подключение не может работать должным образом!
acc_dev_signalIntensity=Сила сигнала
acc_dev_resetSearch=Искать снова
acc_dev_addChildDevice=Добавить вложенное устройство
acc_dev_modParentDevice=Сменить master устройство
acc_dev_configParentDevice=Настройки master устройства
acc_dev_lookUpChildDevice=Посмотреть подчиненные устройства
acc_dev_addChildDeviceTip=Авторизация должна быть выполнена под разрешенным вложенном-устройством
acc_dev_maxSubCount=Количество разрешенных вложенных устройств превышает максимальное количество {0}.
acc_dev_seletParentDevice=Пожалуйста, выберите master устройство!
acc_dev_networkCard=Сетевая карта
acc_dev_issueParam=Пользовательские параметры синхронизации
acc_dev_issueMode=Режим синхронизации
acc_dev_initIssue=Версия прошивки 3030 Инициализация данных
acc_dev_customIssue=Пользовательские параметры данных
acc_dev_issueData=Данные
acc_dev_parent=Master устройство
acc_dev_parentEnable=Master устройство отключено
acc_dev_parentTips=Привязка master устройства удалит все данные на устройстве, и его будет необходимо настроить заново.
acc_dev_addDevIpTip=Новый IP-адрес не может совпадать с IP-адресом сервера.
acc_dev_modifyDevIpTip=Новый адрес сервера не может совпадать с IP-адресом устройства.
acc_dev_setWGReader=Настроить считыватель Wiegand
acc_dev_selectReader=Нажмите, чтобы выбрать считыватель
acc_dev_IllegalDevice=Недопустимое устройство
acc_dev_syncTimeWarnTip=Время синхронизации для этих устройств должно быть синхронизировано на master устройстве.
acc_dev_setTimeZoneWarnTip=Часовой пояс этого устройства должен быть синхронизирован на master устройстве.
acc_dev_setDstimeWarnTip=Летнее время для этих устройств должно быть синхронизировано на master устройстве.
acc_dev_networkSegmentSame=Два сетевых адаптера не могут использовать один и тот же сегмент сети.
acc_dev_upgradeProtocolNoMatch=Протокол файла обновления не совпадает
acc_dev_ipAddressConflict=Устройство с таким же IP-адресом уже существует. Пожалуйста, измените IP-адрес устройства и добавьте его снова.
acc_dev_checkServerPortTip=Указанный порт сервера несовместим с системным портом связи, что может привести к невозможности добавления. Продолжить добавления?
acc_dev_clearAdmin=Очистить права админа
acc_dev_setDevSate=Уст. состояние входа/выхода устройства
acc_dev_sureToClear=Вы уверены, что хотите удалить права администратора устройства?
acc_dev_hostState=Состояние master устройства
acc_dev_regDeviceTypeTip=Использование устройства ограничено и не может быть добавлено напрямую. Пожалуйста, свяжитесь с поставщиком программного обеспечения!
acc_dev_extBoardType=Тип панели I/O
acc_dev_extBoardTip=После настройки необходимо перезагрузить устройство, чтобы оно вступило в силу.
acc_dev_extBoardLimit=Только {0} плата ввода-вывода этого типа может быть добавлена к каждому устройству!
acc_dev_replace=Заменить устройство
acc_dev_replaceTip=После замены старое устройство работать не будет, будьте осторожны!
acc_dev_replaceTip1=После замены выполните операцию "синхронизировать все данные";
acc_dev_replaceTip2=Пожалуйста, убедитесь, что заменяемая модель устройства такая же!
acc_dev_replaceTip3=Убедитесь, что на заменяющем устройстве установлены тот же адрес и порт сервера, что и на старом устройстве!
acc_dev_replaceFail=Тип устройства несовместим и не может быть заменен!
acc_dev_notApb=Устройство не может выполнять противолодочные операции с дверью или головкой считывания.
acc_dev_upResourceFile=Загрузить файл ресурса
acc_dev_playOrder=Порядок воспроизведения
acc_dev_setFaceServerInfo=Параметры сравнения задней части лица
acc_dev_faceVerifyMode=Схема сравнения лиц
acc_dev_faceVerifyMode1=Локальное сравнение
acc_dev_faceVerifyMode2=Модуль сравнения
acc_dev_faceVerifyMode3=Локальный сравнительный приоритет
acc_dev_faceBgServerType=Тип сервера
acc_dev_faceBgServerType1=Услуги платформы программного обеспечения
acc_dev_faceBgServerType2=Услуги третьих сторон
acc_dev_isAccessLogic=Включить логическую проверку управления доступом
#[5]门-其他关联的也复用此处
acc_door_entity=Дверь
acc_door_number=Номер двери
acc_door_name=Имя двери
acc_door_activeTimeZone=Текущая временная зона
acc_door_passageModeTimeZone=Временная зона работы режима прохода
acc_door_setPassageModeTimeZone=Настройки временной зоны работы режима прохода
acc_door_notPassageModeTimeZone=Временная зона работы режима запрета прохода
acc_door_lockOpenDuration=Время открытия замка
acc_door_entranceApbDuration=Длительность запрета двойного прохода
acc_door_sensor=Датчик двери
acc_door_sensorType=Тип датчика двери
acc_door_normalOpen=Нормально открытый
acc_door_normalClose=Нормально закрытый
acc_door_sensorDelay=Время удержания датчика двери
acc_door_closeAndReverseState=Включить блокировку двери, по закрытию
acc_door_hostOutState=Состояние входа/выхода master устройства
acc_door_slaveOutState=Состояние входа/выхода slave устройства
acc_door_inState=Вход
acc_door_outState=Выход
acc_door_requestToExit=Режим кнопки выхода
acc_door_withoutUnlock=Закрыто
acc_door_unlocking=Открыто
acc_door_alarmDelay=Задержка 
acc_door_duressPassword=Пароль принуждения
acc_door_currentDoor=Текущая дверь
acc_door_allDoorOfCurDev=Все двери в текущем устройстве
acc_door_allDoorOfAllDev=Все двери во всех устройствах
acc_door_allDoorOfAllControlDev=Все двери во всех контроллерах
acc_door_allDoorOfAllStandaloneDev=Все двери во всех Standalone устройствах
acc_door_allWirelessLock=Все беспроводные замки
acc_door_max6BitInteger=Максимально 6-битное целое число
acc_door_direction=Направление
acc_door_onlyInReader=Считыватель входа
acc_door_bothInAndOutReader=Считыватели входа/выхода
acc_door_noDoor=Пожалуйста, добавьте дверь.
acc_door_nameRepeat=Имена дверей повторяются.
acc_door_duressPwdError=Пароль принуждения не должен совпадать с любым другим паролем сотрудника.
acc_door_urgencyStatePwd=Пожалуйста, введите {0} битное целое число!
acc_door_noDevOnline=Нет устройств в сети, или дверь не поддерживает режим работы с картами.
acc_door_durationLessLock=Время удержания датчика двери должно быть больше, чем длительность открытия замка.
acc_door_lockMoreDuration=Время открытия замка должна быть меньше, чем время удержания датчика двери.
acc_door_lockAndExtLessDuration=Общее время открытия замка и задержки прохода должна быть меньше, чем время удержания датчика двери.
acc_door_noDevTrigger=Устройство не соответствует условиям!
acc_door_relay=Реле
acc_door_pin=ID сотрудника
acc_door_selDoor=Выберите дверь
acc_door_sensorStatus=Датчик двери({0})
acc_door_sensorDelaySeconds=Время удержания датчика двери ({0} с)
acc_door_timeSeg=Временная зона ({0})
acc_door_combOpenInterval=Интервал поднесения карт комиссионирования
acc_door_delayOpenTime=Время задержки двери в открытом состоянии
acc_door_extDelayDrivertime=Задержка прохода
acc_door_enableAudio=Включить тревогу
acc_door_disableAudio=Отключить тревогу
acc_door_lockAndExtDelayTip=Общее время открытия замка и времени задержки прохода не должно превышать 254 секунды.
acc_door_disabled=Нельзя управлять отключенной дверью!
acc_door_offline=Нельзя управлять дверью вне сети!
acc_door_notSupport=Следующие двери не поддерживают эту функцию!
acc_door_select=Выберите дверь
acc_door_pushMaxCount=В системе {0} включенных дверей, вы достигли максимума лицензии! Пожалуйста, свяжитесь с нашим отделом продаж
acc_door_outNumber=Текущая лицензия позволяет добавить еще {0} дверь(ей). Пожалуйста, повторно выберите двери и попробуйте снова, или свяжитесь с нашим отделом продаж, чтобы обновить лицензию.
acc_door_latchTimeZone=Временная зона кнопки выхода
acc_door_wgFmtReverse=Перевернуть номер карты
acc_door_allowSUAccessLock=Разрешить доступ суперпользователя при блокировке
acc_door_verifyModeSinglePwd=Пароль не может быть использован как независимый способ проверки!
acc_door_doorPassword=Открыть код.
#辅助输入
acc_auxIn_timeZone=Текущая временная зона
#辅助输出
acc_auxOut_passageModeTimeZone=Временная зона работы режима прохода
acc_auxOut_disabled=Нельзя управлять отключенными доп. выходами!
acc_auxOut_offline=Нельзя управлять доп. выходами вне сети!
#[8]门禁权限组
acc_level_doorGroup=Комбинация дверей
acc_level_openingPersonnel=Открывающие сотрудники
acc_level_noDoor=Нет доступных элементов. Пожалуйста, в начале добавьте устройство.
acc_level_doorRequired=Должна быть выбрана дверь.
acc_level_doorCount=Количество дверей
acc_level_doorDelete=Удалить дверь
acc_level_isAddDoor=Сразу добавить двери к текущему уровню доступа?
acc_level_master=Master
acc_level_noneSelect=Пожалуйста, добавьте уровень доступа.
acc_level_useDefaultLevel=Переключить уровень доступа в новый отдел?
acc_level_persAccSet=Настройки доступа сотрудников
acc_level_visUsed={0} уже используется в модуле ПО посетители и его нельзя удалить!
acc_level_doorControl=Управление дверью
acc_level_personExceedMax=текущее количество групп прав ({0}), максимальное количество дополнительных прав ({1})
acc_level_exportLevel=Экспорт уровня доступа
acc_level_exportLevelDoor=Экспорт дверей уровня доступа
acc_level_exportLevelPerson=Экспорт персонала уровня доступа
acc_level_importLevel=Импорт уровня доступа
acc_level_importLevelDoor=Импорт дверей уровня доступа
acc_level_importLevelPerson=Импорт персонала уровня доступа
acc_level_exportDoorFileName=Информация о дверях уровня доступа
acc_level_exportPersonFileName=Информация о персонале уровня доступа
acc_levelImport_nameNotNull=Имя уровня доступа не может быть пустым
acc_levelImport_timeSegNameNotNull=Часовой пояс не может быть пустым
acc_levelImport_areaNotExist=Области не существует!
acc_levelImport_timeSegNotExist=Часовой пояс не существует!
acc_levelImport_nameExist=Имя уровня доступа {0} уже существует!
acc_levelImport_levelDoorExist=Двери уровня доступа {0} уже существуют!
acc_levelImport_levelPersonExist=Персонал с уровнем доступа {0} уже существует!
acc_levelImport_noSpecialChar=Имя уровня доступа не может содержать специальные символы!
#[10]首人常开
acc_firstOpen_setting=1-й сотрудник - режим открыто
acc_firstOpen_browsePerson=Поиск сотрудника
#[11]多人组合开门
acc_combOpen_comboName=Имя комбинации
acc_combOpen_personGroupName=Имя группы
acc_combOpen_personGroup=Имя группы комиссионирования
acc_combOpen_verifyOneTime=Текущее число сотрудников
acc_combOpen_eachGroupCount=Количество сотрудников для открытия в каждой группе
acc_combOpen_group=Группа
acc_combOpen_changeLevel=Группа открытия двери
acc_combOpen_combDeleteGroup=Есть ссылка на комбинацию открытия, пожалуйста, сначала удалите.
acc_combOpen_ownedLevel=Принадлежит группам
acc_combOpen_mostPersonCount=Количество сотрудников в группе должно быть не более пяти.
acc_combOpen_leastPersonCount=Количество сотрудников в группе должно быть не менее двух.
acc_combOpen_groupNameRepeat=Имя группы повторяется.
acc_combOpen_groupNotUnique=Сотрудники группы открытия двери не должны повторятся.
acc_combOpen_persNumErr=Номер группы превышает выбранное вами значение, пожалуйста, выберите заново!
acc_combOpen_combOpengGroupPersonShort=После того, как сотрудники были удалены, количество людей в группе открытия двери недостаточно, пожалуйста, сначала удалите открытую группу.
acc_combOpen_backgroundVerifyTip=Дверь находится в устройстве с включенной фоновой проверкой; нельзя использовать одновременно в группе комиссионирования!
#[12]互锁
acc_interlock_rule=Правила шлюза
acc_interlock_mode1Or2=Шлюз между {0} и {1}
acc_interlock_mode3=Шлюз между {0} и {1} и {2}
acc_interlock_mode4=Шлюз между {0} и {1} или между {2} и {3}
acc_interlock_mode5=Шлюз между {0} и {1} и {2} и {3}
acc_interlock_hasBeenSet=Есть ли шлюзы в настройках программы
acc_interlock_group1=Группа 1
acc_interlock_group2=Группа 2
acc_interlock_ruleInfo=Межгрупповая блокировка
acc_interlock_alreadyExists=Существуют те же правила блокировки, не добавляйте их снова!
acc_interlock_groupInterlockCountErr=Правила блокировки внутри группы требуют как минимум двух дверей.
acc_interlock_ruleType=Типы правил блокировки
#[13]反潜
acc_apb_rules=Правила запрета двойного прохода
acc_apb_reader=Запрет двойного прохода между считывателями двери {0}
acc_apb_reader2=Запрет двойного прохода между считывателями дверей {0}, {1}
acc_apb_reader3=Запрет двойного прохода между считывателями дверей {0}, {1}, {2}
acc_apb_reader4=Запрет двойного прохода между считывателями дверей {0}, {1}, {2}, {3}
acc_apb_reader5=Запрет двойного прохода выходного-считывателя двери {0}
acc_apb_reader6=Запрет двойного прохода входного-считывателя двери {0}
acc_apb_reader7=Запрет двойного прохода между считывателями всех 4-х дверей
acc_apb_twoDoor=Запрет двойного прохода между {0} и {1}
acc_apb_fourDoor=Запрет двойного прохода между {0} и {1}, запрет двойного прохода между {2} и {3}
acc_apb_fourDoor2=Запрет двойного прохода между {0} или {1} и {2} или {3}
acc_apb_fourDoor3=Запрет двойного прохода между {0} и {1} или {2}
acc_apb_fourDoor4=Запрет двойного прохода между {0} и {1} или {2} или {3}
acc_apb_hasBeenSet=Был установлен для запрета двойного прохода
acc_apb_conflictWithGapb=Это устройство имеет настройки глобального запрета двойного прохода, и не может быть изменено!
acc_apb_conflictWithApb=Зона этого устройства имеет настройки запрета двойного прохода, и не может быть изменено!
acc_apb_conflictWithEntranceApb=Зона этого устройства имеет настройки входа для запрета двойного прохода, и не может быть изменено!
acc_apb_controlIn=Запрет двойного прохода - вход
acc_apb_controlOut=Запрет двойного прохода - выход
acc_apb_controlInOut=Запрет двойного прохода вход/выход
acc_apb_groupIn=В группу
acc_apb_groupOut=Выход из группы
acc_apb_reverseName=Противолодочный противолодочный
acc_apb_door=Дверь противолодочной
acc_apb_readerHead=противолодочная противолодочная головка
acc_apb_alreadyExists=Существуют те же правила противолодочной обороны, не добавляйте их снова!
#[17]电子地图
acc_map_addDoor=Добавить дверь
acc_map_addChannel=Добавить видеокамеру
acc_map_noAccess=Нет прав доступа к модулю ПО электронной карты, пожалуйста, обратитесь к администратору!
acc_map_noAreaAccess=Нет прав доступа к этой зоне электронной карты, пожалуйста, обратитесь к администратору!
acc_map_imgSizeError=Пожалуйста, загрузите изображение размером менее {0}Мб!!
#[18]门禁事件记录
acc_trans_entity=Операции
acc_trans_eventType=Тип события
acc_trans_firmwareEvent=События прошивки
acc_trans_softwareEvent=События ПО
acc_trans_today=События за сегодня
acc_trans_lastAddr=Последнее известное местонахождение
acc_trans_viewPhotos=Просмотр фото
acc_trans_exportPhoto=Экспорт фотографий
acc_trans_dayNumber=дней
acc_trans_photo=Контроль доступа к фотографиям
acc_trans_fileIsTooLarge=Экспортируемый файл слишком велик, уменьшите область экспорта
#[19]门禁验证方式
acc_verify_mode_onlyface=Лицо
acc_verify_mode_facefp=Лицо+от. пальца
acc_verify_mode_facepwd=Лицо+пароль
acc_verify_mode_facecard=Лицо+карта доступа
acc_verify_mode_facefpcard=Лицо+от. пальца+карта доступа
acc_verify_mode_facefppwd=Лицо+палец+пароль
acc_verify_mode_fv=Вены пальца
acc_verify_mode_fvpwd=Вены пальца+пароль
acc_verify_mode_fvcard=Вены пальца+пароль,+карта доступа
acc_verify_mode_fvpwdcard=Вены пальца+пароль+карта доступа
acc_verify_mode_pv=ладони
acc_verify_mode_pvcard=ладони+карта доступа
acc_verify_mode_pvface=ладони+лицо
acc_verify_mode_pvfp=ладони+от. пальца
acc_verify_mode_pvfacefp=ладони+лицо+от. пальца
#[20]门禁事件编号
acc_eventNo_-1=Нет
acc_eventNo_0=Открытие по считыванию
acc_eventNo_1=Считывание в течении работы временной зоны режима прохода
acc_eventNo_2=Открытие первой-картой (считывание карты)
acc_eventNo_3=Открытие комиссионированием (считывание карты)
acc_eventNo_4=Открытие аварийным паролем
acc_eventNo_5=Открытие в течении работы временной зоны режима прохода
acc_eventNo_6=Срабатывание привязки
acc_eventNo_7=Снятие тревоги
acc_eventNo_8=Дистанционное открытие
acc_eventNo_9=Дистанционное закрытие
acc_eventNo_10=Выключен режим свободного прохода
acc_eventNo_11=Включен режим свободного прохода
acc_eventNo_12=Доп. выход дистанционное открытие
acc_eventNo_13=Доп. выход дистанционное закрытие
acc_eventNo_14=Открытие отпечатком пальца
acc_eventNo_15=Открытие комиссионированием(отпечаток пальца)
acc_eventNo_16=Считывание отпечатка пальца во временной зоне работы режима прохода
acc_eventNo_17=Открытие по карте доступа и отпечатку пальца
acc_eventNo_18=Открытие первой-картой (отпечаток пальца)
acc_eventNo_19=Открытие первой-картой (считывание карты + отпечаток пальца)
acc_eventNo_20=Слишком короткий интервал
acc_eventNo_21=Вне временной зоны доступа к двери (считывание карты)
acc_eventNo_22=Недействительная временная зона
acc_eventNo_23=Доступ запрещен
acc_eventNo_24=Запрет двойного прохода
acc_eventNo_25=Шлюз
acc_eventNo_26=Аутентификация комиссионирования (считывание карты)
acc_eventNo_27=Карта с отключенными правами доступа
acc_eventNo_28=Превышение времени открытия двери
acc_eventNo_29=Карта с истёкшим сроком действия
acc_eventNo_30=Ошибка пароля
acc_eventNo_31=Слишком короткий интервал между считыванием отпечатков пальцев
acc_eventNo_32=Аутентификация комиссионирования(отпечаток пальца)
acc_eventNo_33=Срок действия отпечатка пальца истёк
acc_eventNo_34=Отпечатка пальца с отключенными правами доступа
acc_eventNo_35=Вне временной зоны работы двери (отпечаток пальца)
acc_eventNo_36=Вне временной зоны работы двери (нажатие кнопки выхода)
acc_eventNo_37=Ошибка закрытия во временной зоне работы режима прохода
acc_eventNo_38=Карта заявлена потерянной
acc_eventNo_39=Доступ запрещен
acc_eventNo_40=Ошибка аутентификации комиссионирования(отпечаток пальца)
acc_eventNo_41=Ошибка режима проверки
acc_eventNo_42=Ошибка формата Wiegand
acc_eventNo_43=Превышение времени проверки запрета двойного прохода
acc_eventNo_44=Ошибка фоновой проверки
acc_eventNo_45=Превышение времени фоновой проверки
acc_eventNo_47=Ошибка отправки команды
acc_eventNo_48=Ошибка аутентификации комиссионирования(считывание карты)
acc_eventNo_49=Вне работы временной зоны двери(пароль)
acc_eventNo_50=Слишком короткий интервал между вводом пароля
acc_eventNo_51=Аутентификации комиссионирования(пароль)
acc_eventNo_52=Ошибка аутентификации комиссионирования(пароль)
acc_eventNo_53=Срок действий пароля истёк
acc_eventNo_100=Тревога вскрытия
acc_eventNo_101=Открытие паролем принуждения
acc_eventNo_102=Взлом двери
acc_eventNo_103=Открытие отпечатком пальца по принуждению
acc_eventNo_200=Открытие двери
acc_eventNo_201=Закрытие двери
acc_eventNo_202=Нажатие кнопки выхода
acc_eventNo_203=Открытие комиссионированием(карта доступа + отпечаток пальца)
acc_eventNo_204=Окончание работы временной зоне режима прохода
acc_eventNo_205=Дист. установка режима открыто
acc_eventNo_206=Включение устройства
acc_eventNo_207=Открытие паролем
acc_eventNo_208=Открытие дверей суперпользователем
acc_eventNo_209=Нажатие кнопка выхода (без разблокирования)
acc_eventNo_210=Открытие противопожарной двери
acc_eventNo_211=Закрытие дверей суперпользователем
acc_eventNo_212=Включение функции управления лифтом
acc_eventNo_213=Выключение функции управления лифтом
acc_eventNo_214=Открытие комиссионированием(пароль)
acc_eventNo_215=Открытие первой-картой(пароль)
acc_eventNo_216=Открытие паролем во временной зоне работы режима прохода
acc_eventNo_220=Отключение доп. входа(открытие)
acc_eventNo_221=Включение доп. входа(закрытие)
acc_eventNo_222=Фоновая проверка работает
acc_eventNo_223=Фоновая проверка
acc_eventNo_225=Нормализация доп. входа
acc_eventNo_226=Активация доп. входа
acc_newEventNo_0=Открытие по верификации
acc_newEventNo_1=Проверка во временной зоне работы режима прохода
acc_newEventNo_2=1-сотрудник открытие
acc_newEventNo_3=Открытие комиссионированием
acc_newEventNo_20=Интервал между действиями слишком короткий
acc_newEventNo_21=Открытие вне временной зоны работы режима прохода
acc_newEventNo_26=Ожидание аутентификации комиссионирования
acc_newEventNo_27=Незарегистрированный пользователь
acc_newEventNo_29=Сотрудник с просроченными правами
acc_newEventNo_30=Ошибка пароля
acc_newEventNo_41=Ошибка режима проверки
acc_newEventNo_43=Блокировка персонала
acc_newEventNo_44=Ошибка фоновой проверки
acc_newEventNo_45=Превышение ожидания фоновой проверки
acc_newEventNo_48=Ошибка комиссионирования
acc_newEventNo_54=Слишком низкий заряд аккумуляторной батареи
acc_newEventNo_55=Незамедлительно замените аккумуляторную батарею
acc_newEventNo_56=Недопустимая операция
acc_newEventNo_57=Резервное питание
acc_newEventNo_58=Активация НО тревоги
acc_newEventNo_59=Недопустимое управление
acc_newEventNo_60=Закрытие двери изнутри
acc_newEventNo_61=Повторенный
acc_newEventNo_62=Запрещённые пользователи
acc_newEventNo_63=Дверь закрыта
acc_newEventNo_64=Вне временной зоне работы кнопки выхода
acc_newEventNo_65=Вне временной зоне работы доп.входа
acc_newEventNo_66=Ошибка обновления считывателя
acc_newEventNo_67=Успешное дистанционное сопряжение (устройство не авторизовано)
acc_newEventNo_68=Высокая  t° тела - доступ запрещен
acc_newEventNo_69=Без маски - доступ запрещен
acc_newEventNo_70=Соотношение лица передается ненормально с сервером.
acc_newEventNo_71=Лицо сервера ответил ненормально.
acc_newEventNo_73=Неверный QR-код
acc_newEventNo_74=Срок действия QR-кода истек
acc_newEventNo_101=Тревога открытия по принуждению
acc_newEventNo_104=Тревога предъявления недействительной карты
acc_newEventNo_105=Невозможно соединиться с сервером
acc_newEventNo_106=Отключение основного питания
acc_newEventNo_107=Отключение аккумуляторной батареи
acc_newEventNo_108=Невозможно подключиться к master устройству
acc_newEventNo_109=Тампер считывателя
acc_newEventNo_110=Считыватель вне сети
acc_newEventNo_112=Расширенная панель
acc_newEventNo_114=отключение ввода пожарной сигнализации (проверка линии)
acc_newEventNo_115=короткое замыкание ввода пожарной сигнализации
acc_newEventNo_116=отключение вспомогательного ввода (обнаружение линии)
acc_newEventNo_117=вспомогательное короткое замыкание ввода (определение линии)
acc_newEventNo_118=Отключение выключателя выхода (проверка линии)
acc_newEventNo_119=короткое замыкание выключателя выхода (проверка линии)
acc_newEventNo_120=Магнитное отключение двери (обнаружение линии)
acc_newEventNo_121=магнитное короткое замыкание двери
acc_newEventNo_159=Дист. управление открытием двери
acc_newEventNo_214=Соединено с сервером
acc_newEventNo_217=Успешное подключение к master устройству
acc_newEventNo_218=Верификация по ID карте
acc_newEventNo_222=Успешная фоновая проверка
acc_newEventNo_223=Фоновая проверка
acc_newEventNo_224=Включение звонка
acc_newEventNo_227=Двойное открытие двери
acc_newEventNo_228=Двойное закрытие двери
acc_newEventNo_229=Доп. выход нормально-открыт на время
acc_newEventNo_230=Доп. выход закрыт на время
acc_newEventNo_232=Успешная проверка
acc_newEventNo_233=Вкл. блокировки
acc_newEventNo_234=Откл. блокировки
acc_newEventNo_235=Успешное обновление считывателя
acc_newEventNo_236=Тревога считывателя снята
acc_newEventNo_237=Считыватель в сети
acc_newEventNo_239=Вызов устройства
acc_newEventNo_240=Вызов завершен
acc_newEventNo_243=Ввод пожарной сигнализации отключен
acc_newEventNo_244=короткое замыкание на входе пожарной сигнализации
acc_newEventNo_247=Расширенная доска онлайн
acc_newEventNo_4014=Отключение пожарного входного сигнала, конец двери всегда открыт
acc_newEventNo_4015=Двери онлайн
acc_newEventNo_4018=Сравнение Backend Открыть
acc_newEventNo_5023=Пожарное состояние ограничено
acc_newEventNo_5024=Многочисленные проверки истекли
acc_newEventNo_5029=Сравнение backend не удалось
acc_newEventNo_4008=Восстановление сети
acc_newEventNo_6005=Рекордная емкость достигает верхнего предела
acc_newEventNo_6006=Линейное короткое замыкание (RS485)
acc_newEventNo_6007=Линейное короткое замыкание (Веген)
acc_newEventNo_6011=Дверь отключена
acc_newEventNo_6012=сигнализация устройства для демонтажа дверей
acc_newEventNo_6013=срабатывание пожарного входного сигнала, открытая дверь всегда открыта
acc_newEventNo_6015=Сбросить питание устройства расширения
acc_newEventNo_6016=Восстановление заводских настроек
acc_newEventNo_6070=Сравнение backend (запрещенный список)
acc_eventNo_undefined=Неизвестный номер события
acc_advanceEvent_500=Глобальный запрет двойного прохода(логический)
acc_advanceEvent_501=Доступ сотрудника (на дату)
acc_advanceEvent_502=Номер сотрудника контроля
acc_advanceEvent_503=Глобальный шлюз
acc_advanceEvent_504=Контроль маршрута
acc_advanceEvent_505=Глобальный запрет двойного прохода (на время)
acc_advanceEvent_506=Глобальный запрет двойного прохода (на время логический)
acc_advanceEvent_507=Доступ сотрудника (после первого входа разрешенное кол-во дней)
acc_advanceEvent_508=Доступ сотрудника (количество раз)
acc_advanceEvent_509=Ошибка фоновой проверки (незарегистрированный пользователь)
acc_advanceEvent_510=Ошибка фоновой проверки (ошибка данных)
acc_alarmEvent_701=Нарушение правил пребывания (настройки правил: {0})
#[21]实时监控
acc_rtMonitor_openDoor=Открытие
acc_rtMonitor_closeDoor=Закрытие
acc_rtMonitor_remoteNormalOpen=Дистан. нормальное-открытие
acc_rtMonitor_realTimeEvent=События в реальном времени
acc_rtMonitor_photoMonitor=Наблюдение за фотографиями
acc_rtMonitor_alarmMonitor=Наблюдение за тревогами
acc_rtMonitor_doorState=Состояние двери
acc_rtMonitor_auxOutName=Имя доп. выхода
acc_rtMonitor_nonsupport=Не поддерживаемый
acc_rtMonitor_lock=Закрыто
acc_rtMonitor_unLock=Открыто
acc_rtMonitor_disable=Отключено
acc_rtMonitor_noSensor=Нет датчика двери
acc_rtMonitor_alarm=Тревога
acc_rtMonitor_openForce=Взлом
acc_rtMonitor_tamper=Датчик вскрытия
acc_rtMonitor_duressPwdOpen=Открытие паролем принуждения
acc_rtMonitor_duressFingerOpen=Открытие от. пальца по принуждению
acc_rtMonitor_duressOpen=Открытие под принуждением
acc_rtMonitor_openTimeout=Превышение ожидания открытия
acc_rtMonitor_unknown=Неизвестно
acc_rtMonitor_noLegalDoor=Нет двери, которая соответствует условию.
acc_rtMonitor_noLegalAuxOut=Нет доп. выхода, который соответствует условию.
acc_rtMonitor_curDevNotSupportOp=Текущий режим устройства не поддерживает эту операцию!
acc_rtMonitor_curNormalOpen=Нормально открытый в текущий момент
acc_rtMonitor_whetherDisableTimeZone=Текущее состояние двери всегда открыто.
acc_rtMonitor_curSystemNoDoors=В текущей системе не добавлено ни одной двери или система не может найти дверь, которая соответствует вашим требованиям.
acc_rtMonitor_cancelAlarm=Снять тревогу
acc_rtMonitor_openAllDoor=Открыть все текущие двери
acc_rtMonitor_closeAllDoor=Закрыть все текущие двери
acc_rtMonitor_confirmCancelAlarm=Уверены, что хотите отключить эту тревогу?
acc_rtMonitor_calcelAllDoor=Отключить все тревоги
acc_rtMonitor_initDoorStateTip=Получение всех разрешённых дверей пользователям системы...
acc_rtMonitor_alarmEvent=Тревожное событие
acc_rtMonitor_ackAlarm=Подтверждение
acc_rtMonitor_ackAllAlarm=Подтверждение всех
acc_rtMonitor_ackAlarmTime=Время подтверждения
acc_rtMonitor_sureToAckThese=Вы уверены, что подтверждаете эту {0} тревогу? После вашего подтверждения все тревоги будут отменены.
acc_rtMonitor_sureToAckAllAlarm=Вы уверены, что хотите отключить все тревоги? После вашего подтверждения все тревоги будут отменены.
acc_rtMonitor_noSelectAlarmEvent=Пожалуйста, выберите, чтобы подтвердить событие тревоги!
acc_rtMonitor_noAlarmEvent=Сейчас в системе нет тревожных событий!
acc_rtMonitor_forcefully=Снять тревогу (взлом двери)
acc_rtMonitor_addToRegPerson=Добавлено к зарегистрированному сотруднику
acc_rtMonitor_cardExist=Эта карта доступа была назначена {0} и не может быть выдана снова.
acc_rtMonitor_opResultPrompt=Успешно отправлено {0} запрос(ов), сбой {1}.
acc_rtMonitor_doorOpFailedPrompt=Не удалось отправить запросы на следующие двери, пожалуйста, попробуйте еще раз!
acc_rtMonitor_remoteOpen=Дистанционное открытие
acc_rtMonitor_remoteClose=Дистанционное закрытие
acc_rtMonitor_alarmSoundClose=Аудио отключено
acc_rtMonitor_alarmSoundOpen=Аудио включено
acc_rtMonitor_playAudio=Звуковое оповещение о событиях
acc_rtMonitor_isOpenShowPhoto=Включить функцию отображения фотографий
acc_rtMonitor_isOpenPlayAudio=Включить функцию звукового оповещения
acc_rtm_open=Дистанционное открытие кнопки
acc_rtm_close=Дистанционное закрытие кнопки
acc_rtm_eleModule=Лифт
acc_cancelAlarm_fp=Снять тревогу (открытие от. пальца по принуждению)
acc_cancelAlarm_pwd=Снять тревогу (открытие паролем принуждения)
acc_cancelAlarm_timeOut=Снять тревогу (превышение времени открытия двери)
#定时同步设备时间
acc_timing_syncDevTime=Устройство синхронизация времени
acc_timing_executionTime=Время выполнения
acc_timing_theLifecycle=История эксплуатации
acc_timing_errorPrompt=Неверный вход.
acc_timing_checkedSyncTime=Пожалуйста, выберите время синхронизации.
#[25]门禁报表
acc_trans_hasAccLevel=Есть уровень доступа
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Пожалуйста добавьте зону
acc_zone_code=Код зоны
acc_zone_parentZone=Родительская зона
acc_zone_parentZoneCode=Код родительской зоны
acc_zone_parentZoneName=Имя родительской зоны
acc_zone_outside=Снаружи
#[G2]读头定义
acc_readerDefine_readerName=Имя считывателя
acc_readerDefine_fromZone=Выходит из
acc_readerDefine_toZone=Входит в
acc_readerDefine_delInfo1=На направление зоны этого считывателя ссылается расширенная функция контроля доступа, и ее нельзя удалить!
acc_readerDefine_selReader=Выберите считыватель
acc_readerDefine_selectReader=Пожалуйста, добавьте считыватель!
acc_readerDefine_tip=После того, как сотрудник выйдет за пределы зоны, учет записей о сотруднике будет удален.
#[G3]全局反潜
acc_gapb_zone=Зона
acc_gapb_whenToResetGapb=Сброс времени запрета двойного прохода
acc_gapb_apbType=Тип запрета двойного прохода
acc_gapb_logicalAPB=Логический запрет двойного прохода
acc_gapb_timedAPB=Запрет двойного прохода на время
acc_gapb_logicalTimedAPB=Запрет двойного прохода на время логический
acc_gapb_lockoutDuration=Время блокировки
acc_gapb_devOfflineRule=если устройство вне сети
acc_gapb_standardLevel=Обычный уровень доступа
acc_gapb_accessDenied=Доступ запрещён
acc_gapb_doorControlZone=Следующие двери контролируют вход и выход из зоны
acc_gapb_resetStatus=Сброс состояния запрета двойного прохода
acc_gapb_obeyAPB=Соблюдение правил запрета двойного прохода
acc_gapb_isResetGAPB=Сбросить запрет двойного прохода
acc_gapb_resetGAPBSuccess=Успешный сброс состояния запрета двойного прохода
acc_gapb_resetGAPBFaile=Ошибка сброса состояния запрета двойного прохода
acc_gapb_chooseArea=Пожалуйста, выберите зону заново.
acc_gapb_notDelInfo1=Зона доступа является верхней зоной доступа и не может быть удалена.
acc_gapb_notDelInfo2=На зону ссылается направление считывателя, и ее нельзя удалить.
acc_gapb_notDelInfo3=На зону ссылаются расширенные функции контроля доступа, и ее нельзя удалить.
acc_gapb_notDelInfo4=Зона доступа ссылается на LED и не может быть удалена!
acc_gapb_zoneNumRepeat=Номера контрольных доменов дублируются
acc_gapb_zoneNameRepeat=Доменные имена управления дублируются
acc_gapb_personResetGapbPre=Вы уверены, что это сбросить?
acc_gapb_personResetGapbSuffix=правила запрета двойного прохода сотрудника?
acc_gapb_apbPrompt=Одна дверь не может использоваться для управления двумя независимыми границами периметра.
acc_gapb_occurApb=Работает запрет двойного прохода
acc_gapb_noOpenDoor=Не открывать дверь
acc_gapb_openDoor=Открыть дверь
acc_gapb_zoneNumLength=Длина более 20 символов
acc_gapb_zoneNameLength=Длина более 30 символов
acc_gapb_zoneRemarkLength=Длина более 50 символов
acc_gapb_isAutoServerMode=Обнаружено устройство без функции фоновой проверки, что может повлиять на работу эту функции. Открыть сейчас?
acc_gapb_applyTo=Применить к
acc_gapb_allPerson=Все сотрудники
acc_gapb_justSelected=Только выбранные сотрудники
acc_gapb_excludeSelected=Исключая выбранных сотрудников
#[G4]who is inside0
acc_zoneInside_lastAccessTime=Последнее время доступа
acc_zoneInside_lastAccessReader=Считыватель по последнему доступу
acc_zoneInside_noPersonInZone=Нет сотрудников в зоне
acc_zoneInside_noRulesInZone=Нет заданных правил.
acc_zoneInside_totalPeople=Всего людей
acc_zonePerson_selectPerson=Пожалуйста, выберите сотрудника или отдел!
#[G5]路径
acc_route_name=Название маршрута
acc_route_setting=Настройки маршрута
acc_route_addReader=Добавить считыватель
acc_route_delReader=Удалить считыватель
acc_route_defineReaderLine=Линия направления считывателя
acc_route_up=Вверх
acc_route_down=Вниз
acc_route_selReader=Выберите считыватель после действия.
acc_route_onlyOneOper=Для управления можно выбрать только один считыватель.
acc_route_readerOrder=Считыватель определил последовательность
acc_route_atLeastSelectOne=Пожалуйста, выберите хотя бы один считыватель!
acc_route_routeIsExist=Этот маршрут уже существует!
#[G6]DMR
acc_dmr_residenceTime=Время пребывания
acc_dmr_setting=Настройки DMR
#[G7]Occupancy
acc_occupancy_max=Макс. присутствие
acc_occupancy_min=Мин. присутствие
acc_occupancy_unlimit=Без ограничений
acc_occupancy_note=Минимальное значение присутствия не может быть больше максимального значения.
acc_occupancy_containNote=Пожалуйста, установите хотя бы одно значение макс. / мин. присутствия!
acc_occupancy_maxMinValid=Пожалуйста, установите присутствие больше 0.
acc_occupancy_conflict=В этой зоне установлен контроль присутствия.
acc_occupancy_maxMinTip=Отсутствие количества присутствия означает, что нет ограничений.
#card availability
acc_personLimit_zonePropertyName=Имя свойства зоны
acc_personLimit_useType=Использовать
acc_personLimit_userDate=Дата действия
acc_personLimit_useDays=После первого входа в течении разрешенных дней
acc_personLimit_useTimes=Исп. количество раз
acc_personLimit_setZoneProperty=Установить свойства зоны
acc_personLimit_zoneProperty=Свойства зоны
acc_personLimit_availabilityName=Имя доступа
acc_personLimit_days=Дней
acc_personLimit_Times=Раз
acc_personLimit_noDel=Выбранные свойства зоны контроля доступа имеют ссылки и не могут быть удалены!
acc_personLimit_cannotEdit=На параметр зоны доступа существует ссылка, нельзя изменить!
acc_personLimit_detail=Подробности
acc_personLimit_userDateTo=Действительно до
acc_personLimit_addPersonRepeatTip=Человек из выбранного отдела был добавлен в атрибут области контроля доступа, пожалуйста, выберите отдел еще раз!
acc_personLimit_leftTimes=Осталось {0} раз
acc_personLimit_expired=истекший
acc_personLimit_unused=Не используется
#全局互锁
acc_globalInterlock_addGroup=Добавить группу
acc_globalInterlock_delGroup=Удалить группу
acc_globalInterlock_refuseAddGroupMessage=Тот же самый шлюз, дверь нельзя продублировать внутри добавленной группы.
acc_globalInterlock_refuseAddlockMessage=Добавленная дверь появилась в других группах устройств шлюза
acc_globalInterlock_refuseDeleteGroupMessage=Пожалуйста, удалите данные, связанные со шлюзом
acc_globalInterlock_isGroupInterlock=Групповой шлюз
acc_globalInterlock_isAddTheDoorImmediately=Сразу добавить дверь
acc_globalInterlock_isAddTheGroupImmediately=Сразу добавить группу
#门禁参数设置
acc_param_autoEventDev=Автоматическая загрузка журнала событий от количества одновременно работающих устройств
acc_param_autoEventTime=Автоматическая загрузка журнала событий по определенным интервалам
acc_param_noRepeat=Дублирование адресов электронной почты не допускается, пожалуйста, заполните снова.
acc_param_most18=Добавить до 18 адресов электронной почты
acc_param_deleteAlert=Невозможно удалить все поля ввода адреса электронной почты.
acc_param_invalidOrRepeat=Ошибка формата адреса электронной почты или адрес электронной почты повторяется.
#全局联动
acc_globalLinkage_noSupport=Выбранная дверь не поддерживает функцию блокировки и отключает функции блокирования.
acc_globalLinkage_trigger=Срабатывание глобальной привязки
acc_globalLinkage_noAddPerson=Правило срабатывания привязки не соотносящееся с конкретным сотрудником, по умолчанию применяется ко всем сотрудникам!
acc_globalLinkage_selectAtLeastOne=Пожалуйста, выберите хотя бы один выход и видео привязку!
acc_globalLinkage_selectTrigger=Пожалуйста, добавьте условия срабатывания привязки!
acc_globalLinkage_selectInput=Пожалуйста, добавьте вход!
acc_globalLinkage_selectOutput=Пожалуйста, добавьте выход!
acc_globalLinkage_audioRemind=Подсказка: привязки речи
acc_globalLinkage_audio=Привязка речи
acc_globalLinkage_isApplyToAll=Применить ко всем сотрудникам
acc_globalLinkage_scope=Диапазон сотрудников
acc_globalLinkage_everyPerson=Любой
acc_globalLinkage_selectedPerson=Выбранный
acc_globalLinkage_noSupportPerson=Не поддерживаемый
acc_globalLinkage_reselectInput=Тип условий срабатывания изменился, пожалуйста, выберите вход заново!
acc_globalLinkage_addPushDevice=Пожалуйста, добавьте поддержку для этой функции устройства
#其他
acc_InputMethod_tips=Пожалуйста, переключитесь на английский режим ввода!
acc_device_systemCheckTip=Нет устройства доступа!
acc_notReturnMsg=Никакой информации не возвратилось
acc_validity_period=У лицензии истёк срок действия; функция не может работать.
acc_device_pushMaxCount=В системе уже существует в {0} устройств(а), достигнут предел лицензии, не удается добавить устройство!
acc_device_videoHardwareLinkage=Установить аппаратную привязку с видео
acc_device_videoCameraIP=IP видеокамеры
acc_device_videoCameraPort=Порт видеокамеры
acc_location_unable=Точка проишествия не была добавлена на электронную карту, не удалось найти конкретное местоположение!
acc_device_wgDevMaxCount=В системе достигнуто максимальное количество устройств согласно лицензии, нельзя изменить настройки устройства!
#自定义报警事件
acc_deviceEvent_selectSound=Пожалуйста, выберите аудио файлы.
acc_deviceEvent_batchSetSoundErr=Пакетная установка звукового оповещения на нарушения.
acc_deviceEvent_batchSet=Установить аудио
acc_deviceEvent_sound=Звуковой сигнал события
acc_deviceEvent_exist=Уже существует
acc_deviceEvent_upload=Загрузить
#查询门最近发生事件
acc_doorEventLatestHappen=Запрос последних событий от двери
#门禁人员信息
acc_pers_delayPassage=Задержка прохода
#设备容量提示
acc_dev_usageConfirm=Текущая потребление превышает 90% мощности оборудования
acc_dev_immediateCheck=Проверить сразу
acc_dev_inSoftware=В программном обеспечении
acc_dev_inFirmware=В прошивке
acc_dev_get=Получить
acc_dev_getAll=Получить все
acc_dev_loadError=Ошибка загрузки
#Reader
acc_reader_inout=Вход/Выход
acc_reader_lightRule=Правила индикации
acc_reader_defLightRule=Правило по умолчанию
acc_reader_encrypt=Шифрование
acc_reader_allReaderOfCurDev=Все считыватели в текущем устройстве
acc_reader_tip1=Шифрование копируется всем считывателям на текущем устройстве!
acc_reader_tip2=Опция режима ID поддерживает только те считыватели, у которых есть эта функция!
acc_reader_tip3=Тип протокола RS485 копируется на все считыватели в текущем устройстве. Настройки вступят в силу после перезагрузки устройства!
acc_reader_tip4=Возможность скрыть некоторую информацию о персонале по умолчанию копируется на все считыватели одного устройства!
acc_reader_commType=Тип подключения
acc_reader_commAddress=Адрес связи
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=Отключенный
acc_readerComAddress_repeat=Повторяющийся адрес связи
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=Адрес RS485
acc_readerCommType_wgAddress=Адрес Wiegand
acc_reader_macError=Пожалуйста, введите MAC-адрес в правильном формате!
acc_reader_machineType=Тип считывателя
acc_reader_readMode=Режим
acc_reader_readMode_normal=Обычный режим
acc_reader_readMode_idCard=Режим ID карты
acc_reader_note=Подсказка: Можно выбрать только устройства доступа в той же области ({0}), что и камера AI.
acc_reader_rs485Type=Тип протокола RS485
acc_reader_userLock=Блокировка доступа персонала
acc_reader_userInfoReveal=Скрыть информацию о персонале
#operat
acc_operation_pwd=Пароль на действие
acc_operation_pwd_error=Ошибка пароля
acc_new_input_not_same=Новый ключ не совпадает
acc_op_set_keyword=Настройки ключа лицензии
acc_op_old_key=Старый ключ
acc_op_new_key=Новый ключ
acc_op_cofirm_key=Подтвердить ключ
acc_op_old_key_error=Ключ старый ошибка
#验证方式规则
acc_verifyRule_name=Имя правила
acc_verifyRule_door=Проверка двери
acc_verifyRule_person=Проверка сотрудников
acc_verifyRule_copy=Скопировать настройки понедельника на другие дни недели:
acc_verifyRule_tip1=Пожалуйста, выберите хотя бы один режим проверки!
acc_verifyRule_tip2=Если правило содержит режим проверки сотрудника, вы можете добавить дверь только со считывателем Wiegand!
acc_verifyRule_tip3=Считыватель RS485 может только выполнять режим проверки присущий двери, не поддерживает режим проверки сотрудников.
acc_verifyRule_oldVerifyMode=Старый режим проверки
acc_verifyRule_newVerifyMode=Новый режим проверки
acc_verifyRule_newVerifyModeSelectTitle=Выберите новый метод проверки
acc_verifyRule_newVerifyModeNoSupportTip=Нет устройства, поддерживающего новый метод проверки!
#Wiegand Test
acc_wiegand_beforeCard=Длина новой карты ({0} бит) не равна предыдущей карте!
acc_wiegand_curentCount=Текущая длина номера карты: {0} бит
acc_wiegand_card=Карта
acc_wiegand_readCard=Считать карту
acc_wiegand_clearCardInfo=Очистить информацию о карте
acc_wiegand_originalCard=Исходный номер карты
acc_wiegand_recommendFmt=Рекомендованный формат карты
acc_wiegand_parityFmt=Контроль формата четности-нечетности
acc_wiegand_withSizeCode=Авто расчет кода объекта, когда код объекта оставлен пустым
acc_wiegand_tip1=Возможно эти карты не принадлежат одной и той же партии карт.
acc_wiegand_tip2=Код объекта: {0}, номер карты: {1}, не соответствует исходному номеру карты. Пожалуйста, проверьте введённый код объекта и номер карты еще раз!
acc_wiegand_tip3=Введенный номер карты ({0}) не может совпадать с исходным номером карты. Проверьте еще раз, пожалуйста!
acc_wiegand_tip4=Введенный код объекта ({0}) не может совпадать с исходным номером карты доступа. Проверьте еще раз, пожалуйста!
acc_wiegand_tip5=Если вы хотите использовать эту функцию, пожалуйста, оставьте все столбцы кода объекта пустыми!
acc_wiegand_warnInfo1=Когда вы будете считывать новую карту, пожалуйста, вручную переключитесь к следующей карте.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Панель вх./выхода сотрудников
acc_LCDRTMonitor_current=Текущая информация о сотруднике
acc_LCDRTMonitor_previous=Предыдущая информация о сотруднике
#api
acc_api_levelIdNotNull=ID группы доступа не может быть пустым
acc_api_levelExist=Группа посещений существует
acc_api_levelNotExist=Группа доступа не существует
acc_api_areaNameNotNull=Зона не может быть пустой.
acc_api_levelNotHasPerson=Нет сотрудников в группе доступа
acc_api_doorIdNotNull=ID двери не может быть пустым
acc_api_doorNameNotNull=Имя двери не может быть пустым
acc_api_doorIntervalSize=Время открытия двери должно быть между (1~254)
acc_api_doorNotExist=Дверь не существует
acc_api_devOffline=Устройство вне сети или отключено
acc_api_devSnNotNull=Серийный номер устройства не может быть пустым
acc_api_timesTampNotNull=Время не может быть пустым.
acc_api_openingTimeCannotBeNull=Время открытия двери не может быть пустым
acc_api_parameterValueCannotBeNull=Значение параметра не может быть пустым
acc_api_deviceNumberDoesNotExist=Серийный номер устройства не существует
acc_api_readerIdCannotBeNull=ID читателя не может быть пустым
acc_api_theReaderDoesNotExist=Считывающая головка не существует
acc_operate_door_notInValidDate=В настоящее время не в течение действительного времени дистанционного открытия, пожалуйста, свяжитесь с администратором в случае необходимости!
acc_api_doorOffline=Дверь не в сети или отключена
#门禁信息自动导出
acc_autoExport_title=Авто экспорт данных
acc_autoExport_frequencyTitle=Частота экспорта
acc_autoExport_frequencyDay=Ежедневно
acc_autoExport_frequencyMonth=Ежемесячно
acc_autoExport_firstDayMonth=Первый день месяца
acc_autoExport_specificDate=Выбранная дата
acc_autoExport_exportModeTitle=Режим экспорта
acc_autoExport_dailyMode=Ежедневная информация
acc_autoExport_monthlyMode=Ежемесячная информация (Вся информация за текущий и прошлый месяц)
acc_autoExport_allMode=Вся информация (До 30 000 сообщений)
acc_autoExport_recipientMail=Эл. почта получателя
#First In And Last Out
acc_inOut_inReaderName=Имя считывателя первого входа
acc_inOut_firstInTime=Время первого входа
acc_inOut_outReaderName=Имя считывателя последнего выхода
acc_inOut_lastOutTime=Время последнего выхода
#防疫参数
acc_dev_setHep=Параметры профилактики болезни
acc_dev_enableIRTempDetection=Вкл. измерение t° с помощью ИК
acc_dev_enableNormalIRTempPass=Запретить доступ при превышении t°
acc_dev_enableMaskDetection=Вкл. обнаружение мед. маски
acc_dev_enableWearMaskPass=Запретить доступ без мед. маски
acc_dev_tempHighThreshold=Порог тревоги при превышении t°
acc_dev_tempUnit=Единица измерения t°
acc_dev_tempCorrection=Коррекция отклонения t°
acc_dev_enableUnregisterPass=Разрешить людям вне системы доступ
acc_dev_enableTriggerAlarm=Включить внешний сигнал тревоги
#联动邮件
acc_mail_temperature=Температура
acc_mail_mask=Мед. маска
acc_mail_unmeasured=Без измерения
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort Global Events
acc_digifort_chooseDigifortEvents=Выберите глобальные события Digifort
acc_digifort_eventExpiredTip=Если глобальное событие будет удалено с сервера Digifort, оно будет красным.
acc_digifort_checkConnection=Пожалуйста, проверьте правильность информации о соединении с сервером Digifort.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Добавить контакты
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=Уст. доп. параметры
acc_extendParam_faceUI=Вид интерфейса
acc_extendParam_faceParam=Параметры лица
acc_extendParam_accParam=Параметры СКД
acc_extendParam_intercomParam=Параметры визуального диалога
acc_extendParam_volume=Емкость
acc_extendParam_identInterval=Интервал идентификации (мс)
acc_extendParam_historyVerifyResult=Отображать историю результата проверки
acc_extendParam_macAddress=Отображать MAC адрес
acc_extendParam_showIp=Отображать IP адрес
acc_extendParam_24HourFormat=Исп. 24-ч. формат
acc_extendParam_dateFormat=Формат даты
acc_extendParam_1NThreshold=Порог 1: N
acc_extendParam_facePitchAngle=Угол наклона лица
acc_extendParam_faceRotationAngle=Угол поворота лица
acc_extendParam_imageQuality=Качество изображения
acc_extendParam_miniFacePixel=Мин. пикс. лица
acc_extendParam_biopsy=Вкл. биопсию
acc_extendParam_showThermalImage=Отображать термальное видео
acc_extendParam_attributeAnalysis=Включить анализ атрибутов
acc_extendParam_temperatureAttribute=Обнаружения t°
acc_extendParam_maskAttribute=Обнаружения мед. маски
acc_extendParam_minTemperature=Мин. t°
acc_extendParam_maxTemperature=Макс. t°
acc_extendParam_gateMode=Режим прохода
acc_extendParam_qrcodeEnable=Включить функцию QR-кода
#可视对讲
acc_dev_intercomServer=Адрес службы визуальной связи
acc_dev_intercomPort=Визуально - разговорный сервер
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Синхронизировать уровень
# 夏令时名称
acc_dsTimeUtc_none=не установлено
acc_dsTimeUtc_AreaNone=в этом районе нет летнее время
acc_dsTimeUtc1000_0=Канберра, мельбурн, сидней
acc_dsTimeUtc1000_1=Хобарт
acc_dsTimeUtc_0330_0=Ньюфаундленд
acc_dsTimeUtc_1000_0=Алеутские острова
acc_dsTimeUtc_0200_0=центральная атлантика
acc_dsTimeUtc0930_0=Аделаида
acc_dsTimeUtc_0100_0=Азорские острова
acc_dsTimeUtc_0400_0=Атлантическое время (Канада)
acc_dsTimeUtc_0400_1=Сантьяго
acc_dsTimeUtc_0400_2=Асунсьон
acc_dsTimeUtc_0300_0=гренландия
acc_dsTimeUtc_0300_1=Сен - Пьер и Микелон
acc_dsTimeUtc0200_0=Кишинев
acc_dsTimeUtc0200_1=Хельсинки, киев, Рига, София, Таллин, вильнюс
acc_dsTimeUtc0200_2=Афины, Бухарест
acc_dsTimeUtc0200_3=иерусалим
acc_dsTimeUtc0200_4=Амман
acc_dsTimeUtc0200_5=Бейрут
acc_dsTimeUtc0200_6=Дамаск
acc_dsTimeUtc0200_7=газа, Хеврон
acc_dsTimeUtc0200_8=Джуба
acc_dsTimeUtc_0600_0=среднее время (США и Канада)
acc_dsTimeUtc_0600_1=Гвадалахара, Мехико, Монтеррей
acc_dsTimeUtc_0600_2=Остров Пасхи
acc_dsTimeUtc1300_0=Самоа
acc_dsTimeUtc_0500_0=Гавана
acc_dsTimeUtc_0500_1=Восточное время (США и Канада)
acc_dsTimeUtc_0500_2=Гаити
acc_dsTimeUtc_0500_3=Индиана (восток)
acc_dsTimeUtc_0500_4=Острова Тёркс и Кайкос
acc_dsTimeUtc_0800_0=Тихоокеанское время (США и Канада)
acc_dsTimeUtc_0800_1=Нижняя Калифорния
acc_dsTimeUtc0330_0=Тегеран
acc_dsTimeUtc0000_0=Дублин, эдинбург, Лиссабон, лондон
acc_dsTimeUtc1200_0=Фиджи
acc_dsTimeUtc1200_1=петропавловск - камчатский - старый
acc_dsTimeUtc1200_2=Окленд, веллингтон
acc_dsTimeUtc1100_0=Норфолк
acc_dsTimeUtc_0700_0=Чиуауа, ла - Пас, Масатлан
acc_dsTimeUtc_0700_1=Горное время (США и Канада)
acc_dsTimeUtc0100_0=Белград, Братислава, Будапешт, Любляна, прага
acc_dsTimeUtc0100_1=Сараево, Скопье, Варшава, Загреб
acc_dsTimeUtc0100_2=Касабланка
acc_dsTimeUtc0100_3=Брюссель, Копенгаген, Мадрид, париж
acc_dsTimeUtc0100_4=Амстердам, Берлин, Берн, Рим, Стокгольм, Вена
acc_dsTimeUtc_0900_0=аляска
#安全点(muster point)
acc_leftMenu_accMusterPoint=Точка сбора
acc_musterPoint_activate=Активировать
acc_musterPoint_addDept=Добавить отдел
acc_musterPoint_delDept=Удалить отдел
acc_musterPoint_report=Отчет о точке сбора
acc_musterPointReport_sign=Войти вручную
acc_musterPointReport_generate=Создавать отчеты
acc_musterPoint_addSignPoint=Добавить точку входа
acc_musterPoint_delSignPoint=Удалить точку входа
acc_musterPoint_selectSignPoint=Пожалуйста, добавьте указатель!
acc_musterPoint_signPoint=Точка входа
acc_musterPoint_delFailTip=Точки сбора уже активированы и не могут быть удалены!
acc_musterPointReport_enterTime=Введите время
acc_musterPointReport_dataAnalysis=Анализ данных
acc_musterPointReport_safe=безопасно
acc_musterPointReport_danger=опасность
acc_musterPointReport_signInManually=ручной штамп
acc_musterPoint_editTip=Точка сбора активна и не может быть изменена!
acc_musterPointEmail_total=Предполагаемое число участников:
acc_musterPointEmail_safe=Прием (безопасность):
acc_musterPointEmail_dangerous=В опасности:
acc_musterPoint_messageNotification=Уведомление при активации
acc_musterPointReport_sendEmail=Запланирована презентация доклада
acc_musterPointReport_sendInterval=Интервал передачи
acc_musterPointReport_sendTip=Убедитесь, что выбранный способ уведомления настроен успешно, иначе уведомление не будет отправлено должным образом!
acc_musterPoint_mailSubject=Уведомление об экстренном сборе
acc_musterPoint_mailContent=Пожалуйста, немедленно соберитесь в "{0}" и войдите в систему на устройстве "{1}", спасибо!
acc_musterPointReport_mailHead=Привет, это экстренный отчет. Пожалуйста, повторите.
acc_musterPoint_visitorsStatistics=Статистика посетителей
# 报警监控
acc_alarm_priority=приоритет
acc_alarm_total=всего
acc_alarm_today=запись дня
acc_alarm_unhandled=подтвердить
acc_alarm_inProcess=в процессе
acc_alarm_acknowledged=подтвердиться
acc_alarm_top5=первые пять напоминаний @ info: whatsthis
acc_alarm_monitoringTime=контрольное время
acc_alarm_history=История обработки напоминаний
acc_alarm_acknowledgement=обработка записей
acc_alarm_eventDescription=Подробности событий
acc_alarm_acknowledgeText=При включении этой опции напоминание будет отправлено в указанный почтовый ящик
acc_alarm_emailSubject=Добавить запись для обработки напоминания @ info: whatsthis
acc_alarm_mute=Звук выключен
acc_alarm_suspend=пауза
acc_alarm_confirmed=событие подтверждено
acc_alarm_list=Журнал тревог
#ntp
acc_device_setNTPService=Настройки NTP-сервера
acc_device_setNTPServiceTip=Введите несколько адресов серверов, разделенных запятыми (,) или точкой с запятой (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=при работе с контроллером дверей суперпользователь не ограничивается временными зонами, обратной обработкой и блокировкой, имеет очень высокий приоритет открытия дверей.
acc_editPerson_delayPassageTip=продление срока ожидания сотрудников через пункт доступа. приспособлены для физической или иной инвалидности.
acc_editPerson_disabledTip=временно отключить уровень доступа.
#门禁向导
acc_guide_title=Мастер установки модуля контроля доступа
acc_guide_addPersonTip=Вам необходимо добавить человека и соответствующие учетные данные (лицо, отпечаток пальца, карту, ладонь или пароль); если вы уже добавили, пропустите этот шаг напрямую
acc_guide_timesegTip=Настройте допустимый период времени открытия
acc_guide_addDeviceTip=Пожалуйста, добавьте соответствующее устройство в качестве точки доступа
acc_guide_addLevelTip=Добавить уровень контроля доступа
acc_guide_personLevelTip=Назначьте соответствующие полномочия управления доступом человеку
acc_guide_rtMonitorTip=Проверка записей управления доступом в режиме реального времени
acc_guide_rtMonitorTip2=Просмотр записей о запрете доступа в режиме реального времени после добавления зоны и соответствующей двери
#查看区域内人员
acc_zonePerson_cleanCount=Очистить статистику входящих и выходящих людей
acc_zonePerson_inCount=Статистика количества входящих людей
acc_zonePerson_outCount=Статистика количества ушедших людей
#biocv460
acc_device_validFail=Имя пользователя или пароль неверно, а проверка не удается!
acc_device_pwdRequired=Введите максимальное 6 - битное целое число.