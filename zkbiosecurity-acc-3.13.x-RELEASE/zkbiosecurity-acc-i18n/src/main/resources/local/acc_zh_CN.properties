#[1]左侧菜单
acc_module=门禁
acc_leftMenu_accDev=门禁设备
acc_leftMenu_auxOut=辅助输出
acc_leftMenu_dSTime=夏令时
acc_leftMenu_access=门禁
acc_leftMenu_door=门
acc_leftMenu_accRule=门禁规则
acc_leftMenu_interlock=互锁
acc_leftMenu_antiPassback=反潜
acc_leftMenu_globalLinkage=全局联动
acc_leftMenu_firstOpen=首人常开
acc_leftMenu_combOpen=多人开门
acc_leftMenu_personGroup=多人开门人员组
acc_leftMenu_level=门禁权限组
acc_leftMenu_electronicMap=电子地图
acc_leftMenu_personnelAccessLevels=人员门禁权限
acc_leftMenu_searchByLevel=以权限组查询
acc_leftMenu_searchByDoor=以门查询
acc_leftMenu_expertGuard=高级门禁
acc_leftMenu_zone=门禁区域
acc_leftMenu_readerDefine=读头定义
acc_leftMenu_gapbSet=全局反潜
acc_leftMenu_whoIsInside=查看区域内人员
acc_leftMenu_whatRulesInside=查看区域内规则
acc_leftMenu_occupancy=人数控制
acc_leftMenu_route=路线定义
acc_leftMenu_globalInterlock=全局互锁
acc_leftMeue_globalInterlockGroup=全局互锁组
acc_leftMenu_dmr=停留时间
acc_leftMenu_personLimit=人员有效性
acc_leftMenu_verifyModeRule=验证方式规则
acc_leftMenu_verifyModeRulePersonGroup=验证方式规则人员组
acc_leftMenu_extDev=I/O扩展板
acc_leftMenu_firstInLastOut=人员进出记录
acc_leftMenu_accReports=门禁报表
#[3]门禁时间段
acc_timeSeg_entity=门禁时间段
acc_timeSeg_canNotDel=时间段正在使用中，不能删除！
#[4]门禁设备--公共的在common中
acc_common_ruleName=规则名称
acc_common_hasBeanSet=已设置
acc_common_notSet=未设置
acc_common_hasBeenOpened=已开启
acc_common_notOpened=未开启
acc_common_partSet=部分设置
acc_common_linkageAndApbTip=联动和全局联动、反潜和全局反潜同时设置，可能会有冲突。
acc_common_vidlinkageTip=请确保联动对应的输入点已绑定可用视频通道，否则视频联动功能将无法正常使用！
acc_common_accZoneFromTo=无法设置相同的门禁区域
acc_common_logEventNumber=记录编号
acc_common_bindOrUnbindChannel=绑定/解绑摄像头
acc_common_boundChannel=已绑定摄像头
#设备信息
acc_dev_iconType=图标类型
acc_dev_carGate=车闸
acc_dev_channelGate=通道
acc_dev_acpType=门禁控制器类型
acc_dev_oneDoorACP=单门控制器
acc_dev_twoDoorACP=两门控制器
acc_dev_fourDoorACP=四门控制器
acc_dev_onDoorACD=一体机
acc_dev_switchToTwoDoorTwoWay=切换为两门双向
acc_dev_addDevConfirm2=提示：设备连接成功，但控制器类型与实际不符，将修改为{0}门控制器，继续添加？
acc_dev_addDevConfirm4=提示：设备连接成功，但控制器类型与实际不符，将修改为一体机，继续添加？
acc_dev_oneMachine=一体机
acc_dev_fingervein=指静脉
acc_dev_control=控制器
acc_dev_protocol=协议类型
acc_ownedBoard=所属扩展板
#设备操作
acc_dev_start=开始
acc_dev_accLevel=门禁权限
acc_dev_timeZoneAndHoliday=时间段、节假日
acc_dev_linkage=联动
acc_dev_doorOpt=门参数
acc_dev_firstPerson=首人开门
acc_dev_multiPerson=多人开门
acc_dev_interlock=互锁
acc_dev_antiPassback=反潜
acc_dev_wiegandFmt=韦根格式
acc_dev_outRelaySet=辅助输出设置
acc_dev_backgroundVerifyParam=后台验证参数
acc_dev_getPersonInfoPrompt=请确保已成功获取人员信息，否则会产生异常，是否继续？
acc_dev_getEventSuccess=获取事件成功
acc_dev_getEventFail=获取事件失败
acc_dev_getInfoSuccess=获取信息成功
acc_dev_getInfoXSuccess=获取{0}成功
acc_dev_getInfoFail=获取信息失败
acc_dev_updateExtuserInfoFail=更新人员信息中的延长通行等失败，请重新获取人员信息。
acc_dev_getPersonCount=获取人员数
acc_dev_getFPCount=获取指纹数
acc_dev_getFVCount=获取指静脉数
acc_dev_getFaceCount=获取人脸数
acc_dev_getPalmCount=获取手掌数
acc_dev_getBiophotoCount=获取比对照片数
acc_dev_noData=设备中没有数据
acc_dev_noNewData=设备中没有新记录数据
acc_dev_softLtDev=软件中的人数大于设备。
acc_dev_personCount=人数为：
acc_dev_personDetail=详细信息如下：
acc_dev_softEqualDev=软件和设备中的人员相等。
acc_dev_softGtDev=设备中的人数大于软件。
acc_dev_cmdSendFail=命令发送失败，请重新获取
acc_dev_issueVerifyParam=设置后台验证参数
acc_dev_verifyParamSuccess=后台验证参数下发成功
acc_dev_backgroundVerify=后台验证
acc_dev_selRightFile=请选择正确的升级文件！
acc_dev_devNotOpForOffLine=设备处于离线状态，请稍后重试！
acc_dev_devNotSupportFunction=设备不支持此功能！
acc_dev_devNotOpForDisable=设备处于禁用状态，请稍后重试！
acc_dev_devNotOpForNotOnline=设备处于离线或禁用状态，请稍后重试！
acc_dev_getPersonInfo=获取人员信息
acc_dev_getFPInfo=获取指纹信息
acc_dev_getFingerVeinInfo=获取指静脉信息
acc_dev_getPalmInfo=获取手掌信息
acc_dev_getBiophotoInfo=获取可见光人脸信息
acc_dev_getIrisInfo=获取虹膜信息
acc_dev_disable=处于禁用状态，请重新选择！
acc_dev_offlineAndContinue=处于离线状态，是否继续操作？
acc_dev_offlineAndSelect=处于离线状态。
acc_dev_opAllDev=所有设备
acc_dev_opOnlineDev=在线设备
acc_dev_opException=处理发生异常
acc_dev_exceptionAndConfirm=设备连接超时，操作失败，请检查网络连接！
acc_dev_getFaceInfo=获取面部信息
acc_dev_selOpDevType=请选择要操作的设备类型：
acc_dev_hasFilterByFunc=已过滤离线或不支持该功能的设备！
acc_dev_masterSlaveMode=RS485主从机模式
acc_dev_master=主机
acc_dev_slave=从机
acc_dev_modifyRS485Addr=修改RS485地址
acc_dev_rs485AddrTip=请输入1-63之间的整数！
acc_dev_enableFeature=后台验证启用
acc_dev_disableFeature=后台验证禁用
acc_dev_getCountOnly=仅获取数量
acc_dev_queryDevPersonCount=查询设备人员数
acc_dev_queryDevVolume=查询设备容量
acc_dev_ruleType=规则类型
acc_dev_contenRule=规则内容
acc_dev_accessRules=查看设备中门禁规则
acc_dev_ruleContentTip=多个规则之间用竖线（|）隔开。
acc_dev_rs485AddrFigure=RS485地址拨码图
acc_dev_addLevel=添加到权限组
acc_dev_personOrFingerTanto=人员或指纹数量过多，同步失败...
acc_dev_personAndFingerUnit=（个）
acc_dev_setDstime=设置夏令时
acc_dev_setTimeZone=设置设备时区
acc_dev_selectedTZ=已选时区
acc_dev_timeZoneSetting=正在设置设备时区...
acc_dev_timeZoneCmdSuccess=设备时区命令发送成功...
acc_dev_enableDstime=启用夏令时
acc_dev_disableDstime=禁用夏令时
acc_dev_timeZone=时区
acc_dev_dstSettingTip=正在设置夏令时...
acc_dev_dstDelTip=正在删除设备夏令时...
acc_dev_enablingDst=正在启用夏令时
acc_dev_dstEnableCmdSuccess=启用设备夏令时命令发送成功。
acc_dev_disablingDst=正在禁用夏令时。
acc_dev_dstDisableCmdSuccess=禁用设备夏令时命令发送成功。
acc_dev_dstCmdSuccess=夏令时设置命令发送成功...
acc_dev_usadst=夏令时
acc_dev_notSetDst=尚未设置
acc_dev_selectedDst=已选夏令时
acc_dev_configMasterSlave=主从配置
acc_dev_hasFilterByUnOnline=已过滤非在线设备
acc_dev_softwareData=如发现软件和设备数据不一致，请先同步二者数据后再查询！
acc_dev_disabled=以下设备处于禁用状态，无法操作！
acc_dev_offline=以下设备处于离线状态，无法操作！
acc_dev_noSupport=以下设备不支持该功能，无法操作！
acc_dev_noRegDevTip=获取非登记机的数据会覆盖软件中的数据，确定要继续吗？
acc_dev_noOption=暂无符合条件选项。
acc_dev_devFWUpdatePrompt=正在添加的设备将无法使用正常使用系统内的禁止名单、人员有效期功能（详情请参考用户手册）。
acc_dev_panelFWUpdatePrompt=正在添加的设备将无法正常使用系统内的禁止名单、人员有效期功能，是否立即升级固件？
acc_dev_sendEventCmdSuccess=发送删除事件命令成功
acc_dev_tryAgain=请重试
acc_dev_eventAutoCheckAndUpload=自动检查并获取记录
acc_dev_eventUploadStart=开始获取设备事件记录
acc_dev_eventUploadEnd=获取设备事件记录结束
acc_dev_eventUploadFailed=获取设备事件记录失败
acc_dev_eventUploadPrompt=检测到固件版本过低的设备，升级固件之前，您希望：
acc_dev_backupToSoftware=备份数据到软件
acc_dev_deleteEvent=删除设备中事件记录
acc_dev_upgradePrompt=固件版本过低升级后可能导致事件记录错乱，可以先将数据备份到软件。
acc_dev_conflictCardNo=系统中存在卡号为{0}的其它人员！
acc_dev_rebootAfterOperate=操作成功，稍后设备将重启。
acc_dev_baseOptionTip=获取基础参数异常
acc_dev_funOptionTip=获取功能参数异常
acc_dev_sendComandoTip=获取设备参数命令发送失败
acc_dev_noC3LicenseTip=无法新增该类型设备（{0}）。要继续当前操作，请联系销售人员！
acc_dev_combOpenDoorTip=（{0}）已设置多人开门，不能同时使用后台验证功能！
acc_dev_combOpenDoorPersonCountTip=组{0}开门人数不得大于{1}！
acc_dev_addDevTip=此操作仅适用于添加通信协议为PULL的设备！
acc_dev_addError=设备添加异常，缺少参数（{0}）！
acc_dev_updateIPAndPortError=更新服务器IP和端口异常
acc_dev_transferFilesTip=固件检测成功，传文件
acc_dev_serialPortExist=串口存在
acc_dev_isExist=设备是否存在
acc_dev_description=描述
acc_dev_searchEthernet=搜索以太网设备
acc_dev_searchRS485=搜索RS485设备
acc_dev_rs485AddrTip1=RS485的起始地址不能够大于结束地址
acc_dev_rs485AddrTip2=RS485的搜索范围必须在20以内
acc_dev_clearAllCmdCache=清除全部命令
acc_dev_authorizedSuccessful=授权成功
acc_dev_authorize=授权
acc_dev_registrationDevice=登记机
acc_dev_setRegistrationDevice=设置登记机
acc_dev_mismatchedDevice=设备连接失败，原因：设备序列号错误！
acc_dev_pwdStartWithZero=通讯密码不能以0开头！
acc_dev_maybeDisabled=当前许可允许再添加{0}个门，新添加设备中超出许可点数限制的门将会被禁用，是否继续操作？
acc_dev_Limit=系统中许可点数已经达到上限，如需添加，请授权。
acc_dev_selectDev=请选择设备！
acc_dev_cannotAddPullDevice=不允许添加PULL设备！要继续当前操作，请联系销售人员！
acc_dev_notContinueAddPullDevice=系统里已经存在{0}台PULL设备，不允许继续添加！要继续当前操作，请联系销售人员！
acc_dev_deviceNameNull=设备型号为空，不能添加设备！
acc_dev_commTypeErr=设备通讯方式不匹配，无法添加！
acc_dev_inputDomainError=请输入正确格式的域名地址！
acc_dev_levelTip=权限组下人数大于5000，无法自动添加！
acc_dev_auxinSet=辅助输入设置
acc_dev_verifyModeRule=验证方式规则
acc_dev_netModeWired=有线
acc_dev_netMode4G=4G
acc_dev_netModeWifi=Wifi
acc_dev_updateNetConnectMode=切换网络连接
acc_dev_wirelessSSID=无线SSID
acc_dev_wirelessKey=无线密钥
acc_dev_searchWifi=搜索无线列表
acc_dev_testNetConnectSuccess=通讯连接成功，是否确认切换？
acc_dev_testNetConnectFailed=当前连接方式不能正常通讯！
acc_dev_signalIntensity=信号强度
acc_dev_resetSearch=重新搜索
acc_dev_addChildDevice=授权子设备
acc_dev_modParentDevice=变更主设备
acc_dev_configParentDevice=配置主设备
acc_dev_lookUpChildDevice=查看子设备
acc_dev_addChildDeviceTip=需在授权子设备下进行授权
acc_dev_maxSubCount=授权子设备数量超过最大接入数量{0}台。
acc_dev_seletParentDevice=请选择主设备！
acc_dev_networkCard=网卡
acc_dev_issueParam=自定义下发参数
acc_dev_issueMode=下发模式
acc_dev_initIssue=3030固件初始化下发数据
acc_dev_customIssue=自定义下发数据
acc_dev_issueData=数据
acc_dev_parent=主设备
acc_dev_parentEnable=主设备处于禁用状态
acc_dev_parentTips=绑定主设备将会删除设备中已有的所有数据，需要重新对其进行设置。
acc_dev_addDevIpTip=新IP地址不能和服务器IP地址一致
acc_dev_modifyDevIpTip=新服务器地址不能与设备IP地址相同
acc_dev_setWGReader=设置韦根读头
acc_dev_selectReader=点击选择读头
acc_dev_IllegalDevice=非法设备
acc_dev_syncTimeWarnTip=以下这些设备的同步时间需在主控上同步。
acc_dev_setTimeZoneWarnTip=以下这些设备的时区需在主控上同步。
acc_dev_setDstimeWarnTip=以下这些设备的夏令时需在主控上同步。
acc_dev_networkSegmentSame=两个网卡不允许使用相同的网段。
acc_dev_upgradeProtocolNoMatch=升级文件协议不匹配
acc_dev_ipAddressConflict=已存在相同IP地址设备,请修改设备IP地址后重新添加。
acc_dev_checkServerPortTip=设置的服务器端口与系统通讯端口不一致，可能导致无法添加，是否继续操作？
acc_dev_clearAdmin=清除管理员
acc_dev_setDevSate=设置机器出入状态
acc_dev_sureToClear=你确定要执行清除管理员操作吗？
acc_dev_hostState=主机状态
acc_dev_regDeviceTypeTip=此设备为受控设备，不允许添加，请联系软件供应商！
acc_dev_extBoardType=扩展板类型
acc_dev_extBoardTip=配置后需重启设备才可生效！
acc_dev_extBoardLimit=每台设备只允许添加该类扩展板{0}个！
acc_dev_replace=替换设备
acc_dev_replaceTip=替换之后，旧设备将无法使用，请谨慎操作！
acc_dev_replaceTip1=替换之后，请进行“同步所有数据”操作；
acc_dev_replaceTip2=请确保替换的设备型号一致！
acc_dev_replaceTip3=请确保替换的设备已设置好和旧设备一样的服务器地址和端口号
acc_dev_replaceFail=设备类型不一致，无法替换！
acc_dev_notApb=此设备无法进行门或读头反潜
acc_dev_upResourceFile=上传资源文件
acc_dev_playOrder=播放顺序
acc_dev_setFaceServerInfo=设置人脸后台比对参数
acc_dev_faceVerifyMode=人脸比对模式
acc_dev_faceVerifyMode1=本地比对
acc_dev_faceVerifyMode2=后台比对
acc_dev_faceVerifyMode3=本地比对优先
acc_dev_faceBgServerType=人脸后台服务器类型
acc_dev_faceBgServerType1=软件平台服务
acc_dev_faceBgServerType2=第三方服务
acc_dev_isAccessLogic=是否启用门禁逻辑验证
#[5]门-其他关联的也复用此处
acc_door_entity=门
acc_door_number=门编号
acc_door_name=门名称
acc_door_activeTimeZone=门有效时间段
acc_door_passageModeTimeZone=门常开时间段
acc_door_setPassageModeTimeZone=已设置门常开时间段
acc_door_notPassageModeTimeZone=未设置门常开时间段
acc_door_lockOpenDuration=锁驱动时长
acc_door_entranceApbDuration=入反潜时长
acc_door_sensor=门磁
acc_door_sensorType=门磁类型
acc_door_normalOpen=常开
acc_door_normalClose=常闭
acc_door_sensorDelay=门磁延时
acc_door_closeAndReverseState=闭门回锁
acc_door_hostOutState=主机出入状态
acc_door_slaveOutState=从机出入状态
acc_door_inState=入
acc_door_outState=出
acc_door_requestToExit=出门按钮状态
acc_door_withoutUnlock=锁定
acc_door_unlocking=不锁定
acc_door_alarmDelay=出门按钮延时
acc_door_duressPassword=胁迫密码
acc_door_currentDoor=当前门
acc_door_allDoorOfCurDev=当前设备所有门
acc_door_allDoorOfAllDev=所有设备所有门
acc_door_allDoorOfAllControlDev=所有控制器设备所有门
acc_door_allDoorOfAllStandaloneDev=所有一体机设备所有门
acc_door_allWirelessLock=所有无线锁
acc_door_max6BitInteger=最大6位整数
acc_door_direction=进出方向
acc_door_onlyInReader=仅入读头
acc_door_bothInAndOutReader=出入读头
acc_door_noDoor=请添加门
acc_door_nameRepeat=门名称重复
acc_door_duressPwdError=胁迫密码不能与任意人员密码相同！
acc_door_urgencyStatePwd=请输入{0}位整数！
acc_door_noDevOnline=没有在线的设备，或门不支持卡验证方式！
acc_door_durationLessLock=门磁延时时长必须大于锁驱动时长！
acc_door_lockMoreDuration=锁驱动时长必须小于门磁延时时长！
acc_door_lockAndExtLessDuration=锁驱动时长加延长通行时间必须小于门磁延时时长！
acc_door_noDevTrigger=没有满足条件的设备！
acc_door_relay=继电器
acc_door_pin=工号
acc_door_selDoor=选门
acc_door_sensorStatus=门磁（{0}）
acc_door_sensorDelaySeconds=延时（{0}秒）
acc_door_timeSeg=时间段（{0}）
acc_door_combOpenInterval=多人开门操作间隔
acc_door_delayOpenTime=开门延时
acc_door_extDelayDrivertime=延长通行时间
acc_door_enableAudio=启用报警提醒
acc_door_disableAudio=禁用报警提醒
acc_door_lockAndExtDelayTip=锁驱动时长与延长通行时间的总和不能大于254秒。
acc_door_disabled=以下门处于禁用状态，无法操作！
acc_door_offline=以下门处于离线状态，无法操作！
acc_door_notSupport=以下门不支持此功能，无法操作！
acc_door_select=选择门
acc_door_pushMaxCount=系统里存在{0}个已启用的门，达到许可点数上限。要继续当前操作，请联系销售人员！
acc_door_outNumber=当前许可只允许启用{0}个门！请重新选择门，或者联系销售人员购买更新许可。
acc_door_latchTimeZone=出门按钮有效时间段
acc_door_wgFmtReverse=卡号位反转
acc_door_allowSUAccessLock=允许超级用户在门锁定时通行
acc_door_verifyModeSinglePwd=密码不能作为独立验证方式进行使用！
acc_door_doorPassword=开门密码
#辅助输入
acc_auxIn_timeZone=有效时间段
#辅助输出
acc_auxOut_passageModeTimeZone=常开时间段
acc_auxOut_disabled=以下辅助输出处于禁用状态，无法操作！
acc_auxOut_offline=以下辅助输出处于离线状态，无法操作！
#[8]门禁权限组
acc_level_doorGroup=的门组合
acc_level_openingPersonnel=的开门人员
acc_level_noDoor=没有可以选择的选项，请先添加设备！
acc_level_doorRequired=必须选择门！
acc_level_doorCount=门数量
acc_level_doorDelete=删除门
acc_level_isAddDoor=立即向刚添加的权限组添加门？
acc_level_master=通用权限组
acc_level_noneSelect=请添加权限组
acc_level_useDefaultLevel=是否切换为该部门的门禁权限？
acc_level_persAccSet=人事门禁设置
acc_level_visUsed={0}已被访客模块使用，无法删除！
acc_level_doorControl=门控制
acc_level_personExceedMax=当前权限组人数（{0}），可选的权限组人数最大为（{1}）
acc_level_exportLevel=导出权限组信息
acc_level_exportLevelDoor=导出权限组门信息
acc_level_exportLevelPerson=导出权限组人员信息
acc_level_importLevel=导入权限组信息
acc_level_importLevelDoor=导入权限组门信息
acc_level_importLevelPerson=导入权限组人员信息
acc_level_exportDoorFileName=门禁权限组门信息
acc_level_exportPersonFileName=门禁权限组人员信息
acc_levelImport_nameNotNull=权限组名称不能为空
acc_levelImport_timeSegNameNotNull=时间段名称不能为空
acc_levelImport_areaNotExist=区域不存在！
acc_levelImport_timeSegNotExist=时间段不存在！
acc_levelImport_nameExist=权限组名称 {0} 已存在！
acc_levelImport_levelDoorExist=权限组门信息{0}已存在！
acc_levelImport_levelPersonExist=权限组人员信息{0}已存在！
acc_levelImport_noSpecialChar=权限组名称不能包含特殊字符！
#[10]首人常开
acc_firstOpen_setting=首人常开设置
acc_firstOpen_browsePerson=浏览开门人员
#[11]多人组合开门
acc_combOpen_comboName=组合名称
acc_combOpen_personGroupName=组名称
acc_combOpen_personGroup=多人开门人员组
acc_combOpen_verifyOneTime=同时验证人数
acc_combOpen_eachGroupCount=各组开门人数
acc_combOpen_group=组
acc_combOpen_changeLevel=开门人员组
acc_combOpen_combDeleteGroup=存在多人开门引用，请先删除多人开门！
acc_combOpen_ownedLevel=所属权限组
acc_combOpen_mostPersonCount=组合人数最多五人！
acc_combOpen_leastPersonCount=开门人员最少两人！
acc_combOpen_groupNameRepeat=组合名称重复！
acc_combOpen_groupNotUnique=开门人员组不能相同！
acc_combOpen_persNumErr=您选择的该组人数超出实际值，请重新选择！
acc_combOpen_combOpengGroupPersonShort=删除该人员后，开门组中开门人员组人数不够，请先删除开门组！
acc_combOpen_backgroundVerifyTip=该门所属的设备已启用后台验证，不能和多人开门规则同时使用！
#[12]互锁
acc_interlock_rule=互锁规则
acc_interlock_mode1Or2={0} 与 {1} 互锁
acc_interlock_mode3={0} 与 {1} 与 {2} 互锁
acc_interlock_mode4={0} 与 {1} 互锁，{2} 与 {3} 互锁
acc_interlock_mode5={0} 与 {1} 与 {2} 与 {3} 互锁
acc_interlock_hasBeenSet=已设置互锁
acc_interlock_group1=组1
acc_interlock_group2=组2
acc_interlock_ruleInfo=组间互锁
acc_interlock_alreadyExists=已存在相同的互锁规则，请勿重复添加！
acc_interlock_groupInterlockCountErr=组内互锁规则至少需要两个门
acc_interlock_ruleType=互锁规则类型
#[13]反潜
acc_apb_rules=反潜规则
acc_apb_reader={0}读头间反潜
acc_apb_reader2={0}，{1}各自读头间同时反潜
acc_apb_reader3={0}，{1}，{2}各自读头间同时反潜
acc_apb_reader4={0}，{1}，{2}，{3}各自读头间同时反潜
acc_apb_reader5={0}读头出反潜
acc_apb_reader6={0}读头入反潜
acc_apb_reader7=任意4门出入反潜
acc_apb_twoDoor={0} 与 {1}反潜
acc_apb_fourDoor={0} 与 {1}反潜，{2} 与 {3}反潜
acc_apb_fourDoor2={0}或{1} 与 {2}或{3}反潜
acc_apb_fourDoor3={0} 与 {1}或{2}反潜
acc_apb_fourDoor4={0} 与 {1}或{2}或{3}反潜
acc_apb_hasBeenSet=已设置反潜
acc_apb_conflictWithGapb=该设备已设置全局反潜，不能再设置该规则！
acc_apb_conflictWithApb=该区域中的设备已设置反潜，不能再设置全局反潜规则！
acc_apb_conflictWithEntranceApb=该区域中的设备已设置入时间反潜，不能再设置全局反潜规则！
acc_apb_controlIn=入反潜
acc_apb_controlOut=出反潜
acc_apb_controlInOut=出入反潜
acc_apb_groupIn=入组
acc_apb_groupOut=出组
acc_apb_reverseName=的反向反潜
acc_apb_door=门反潜
acc_apb_readerHead=读头反潜
acc_apb_alreadyExists=已存在相同的反潜规则，请勿重复添加！
#[17]电子地图
acc_map_addDoor=添加门
acc_map_addChannel=添加摄像头
acc_map_noAccess=您没有电子地图模块的权限，请联系管理员！
acc_map_noAreaAccess=您没有该区域的电子地图权限，请联系管理员！
acc_map_imgSizeError=请上传大小不超过{0}M的图片！
#[18]门禁事件记录
acc_trans_entity=门禁事件记录
acc_trans_eventType=事件类型
acc_trans_firmwareEvent=固件事件
acc_trans_softwareEvent=软件事件
acc_trans_today=今日访问记录
acc_trans_lastAddr=人员最后访问位置
acc_trans_viewPhotos=查看照片
acc_trans_exportPhoto=导出照片
acc_trans_photo=门禁事件照片
acc_trans_dayNumber=天数
acc_trans_fileIsTooLarge=导出的文件过大，请缩小范围导出
#[19]门禁验证方式
acc_verify_mode_onlyface=人脸
acc_verify_mode_facefp=人脸+指纹
acc_verify_mode_facepwd=人脸+密码
acc_verify_mode_facecard=人脸+卡
acc_verify_mode_facefpcard=人脸+指纹+卡
acc_verify_mode_facefppwd=人脸+指纹+密码
acc_verify_mode_fv=指静脉
acc_verify_mode_fvpwd=指静脉+密码
acc_verify_mode_fvcard=指静脉+卡
acc_verify_mode_fvpwdcard=指静脉+密码+卡
acc_verify_mode_pv=手掌
acc_verify_mode_pvcard=手掌+卡
acc_verify_mode_pvface=手掌+人脸
acc_verify_mode_pvfp=手掌+指纹
acc_verify_mode_pvfacefp=手掌+人脸+指纹
#[20]门禁事件编号
acc_eventNo_-1=无
acc_eventNo_0=正常刷卡开门
acc_eventNo_1=常开时间段内刷卡
acc_eventNo_2=首人开门(刷卡)
acc_eventNo_3=多人开门(刷卡)
acc_eventNo_4=紧急状态密码开门
acc_eventNo_5=常开时间段开门
acc_eventNo_6=触发联动事件
acc_eventNo_7=取消报警
acc_eventNo_8=远程开门
acc_eventNo_9=远程关门
acc_eventNo_10=禁用当天常开时间段
acc_eventNo_11=启用当天常开时间段
acc_eventNo_12=远程打开辅助输出
acc_eventNo_13=远程关闭辅助输出
acc_eventNo_14=正常按指纹开门
acc_eventNo_15=多人开门(按指纹)
acc_eventNo_16=常开时间段内按指纹
acc_eventNo_17=卡加指纹开门
acc_eventNo_18=首人开门(按指纹)
acc_eventNo_19=首人开门(卡加指纹)
acc_eventNo_20=操作间隔太短
acc_eventNo_21=门非有效时间段(刷卡)
acc_eventNo_22=非法时间段
acc_eventNo_23=非法访问
acc_eventNo_24=反潜
acc_eventNo_25=互锁
acc_eventNo_26=多人验证(刷卡)
acc_eventNo_27=卡未注册
acc_eventNo_28=门开超时
acc_eventNo_29=卡已过有效期
acc_eventNo_30=密码错误
acc_eventNo_31=按指纹间隔太短
acc_eventNo_32=多人验证(按指纹)
acc_eventNo_33=指纹已过有效期
acc_eventNo_34=指纹未注册
acc_eventNo_35=门非有效时间段(按指纹)
acc_eventNo_36=门非有效时间段(按出门按钮)
acc_eventNo_37=常开时间段无法关门
acc_eventNo_38=卡已挂失
acc_eventNo_39=禁止名单
acc_eventNo_40=多人验证失败(按指纹)
acc_eventNo_41=验证方式错误
acc_eventNo_42=韦根格式错误
acc_eventNo_43=反潜验证超时
acc_eventNo_44=后台验证失败
acc_eventNo_45=后台验证超时
acc_eventNo_47=发送命令失败
acc_eventNo_48=多人验证失败(刷卡)
acc_eventNo_49=门非有效时间段(密码)
acc_eventNo_50=按密码间隔太短
acc_eventNo_51=多人验证(密码)
acc_eventNo_52=多人验证失败(密码)
acc_eventNo_53=密码已过有效期
acc_eventNo_100=防拆报警
acc_eventNo_101=胁迫密码开门
acc_eventNo_102=门被意外打开
acc_eventNo_103=胁迫指纹开门
acc_eventNo_200=门已打开
acc_eventNo_201=门已关闭
acc_eventNo_202=出门按钮开门
acc_eventNo_203=多人开门(卡加指纹)
acc_eventNo_204=常开时间段结束
acc_eventNo_205=远程开门常开
acc_eventNo_206=设备启动
acc_eventNo_207=密码开门
acc_eventNo_208=超级用户开门
acc_eventNo_209=触发出门按钮(被锁定)
acc_eventNo_210=启动消防开门
acc_eventNo_211=超级用户关门
acc_eventNo_212=开启电梯控制功能
acc_eventNo_213=关闭电梯控制功能
acc_eventNo_214=多人开门(密码)
acc_eventNo_215=首人开门(密码)
acc_eventNo_216=常开时间段内按密码
acc_eventNo_220=辅助输入点断开
acc_eventNo_221=辅助输入点短路
acc_eventNo_222=后台验证成功
acc_eventNo_223=后台验证
acc_eventNo_225=辅助输入点正常
acc_eventNo_226=辅助输入点触发
acc_newEventNo_0=正常验证开门
acc_newEventNo_1=常开时间段内验证
acc_newEventNo_2=首人开门
acc_newEventNo_3=多人开门
acc_newEventNo_20=操作间隔太短
acc_newEventNo_21=门非有效时间段验证开门
acc_newEventNo_26=多人验证等待
acc_newEventNo_27=人未登记
acc_newEventNo_29=人已过有效期
acc_newEventNo_30=密码错误
acc_newEventNo_41=验证方式错误
acc_newEventNo_43=人员锁定
acc_newEventNo_44=后台验证失败
acc_newEventNo_45=后台验证超时
acc_newEventNo_48=多人验证失败
acc_newEventNo_54=电池电压过低
acc_newEventNo_55=立即更换电池
acc_newEventNo_56=非法操作
acc_newEventNo_57=后备电源
acc_newEventNo_58=常开报警
acc_newEventNo_59=非法管理
acc_newEventNo_60=门被反锁
acc_newEventNo_61=重复验证
acc_newEventNo_62=禁止用户
acc_newEventNo_63=门已锁定
acc_newEventNo_64=出门按钮不在有效时间段内操作
acc_newEventNo_65=辅助输入不在有效时间段内操作
acc_newEventNo_66=读头升级失败
acc_newEventNo_67=远程比对成功(设备未授权)
acc_newEventNo_68=体温过高-拒绝通行
acc_newEventNo_69=未佩戴口罩-拒绝通行
acc_newEventNo_70=人脸比对服务器通信异常
acc_newEventNo_71=人脸服务器响应异常
acc_newEventNo_73=无效二维码
acc_newEventNo_74=二维码已过期
acc_newEventNo_101=胁迫开门报警
acc_newEventNo_104=无效卡刷卡报警
acc_newEventNo_105=无法连接服务器
acc_newEventNo_106=市电掉电
acc_newEventNo_107=电池掉电
acc_newEventNo_108=无法连接主控
acc_newEventNo_109=读头防拆报警
acc_newEventNo_110=读头离线
acc_newEventNo_112=扩展板离线
acc_newEventNo_114=火警输入断开(线路检测)
acc_newEventNo_115=火警输入短路(线路检测)
acc_newEventNo_116=辅助输入断开(线路检测)
acc_newEventNo_117=辅助输入短路(线路检测)
acc_newEventNo_118=出门开关断开(线路检测)
acc_newEventNo_119=出门开关短路(线路检测)
acc_newEventNo_120=门磁断开(线路检测)
acc_newEventNo_121=门磁短路(线路检测)
acc_newEventNo_159=遥控开门
acc_newEventNo_214=成功连接服务器
acc_newEventNo_217=成功连接主控
acc_newEventNo_218=身份证通行
acc_newEventNo_222=后台验证成功
acc_newEventNo_223=后台验证
acc_newEventNo_224=按门铃
acc_newEventNo_227=门双开
acc_newEventNo_228=门双关
acc_newEventNo_229=辅助输出定时常开
acc_newEventNo_230=辅助输出定时关闭常开
acc_newEventNo_232=验证通过
acc_newEventNo_233=远程锁定
acc_newEventNo_234=远程解锁
acc_newEventNo_235=读头升级成功
acc_newEventNo_236=读头防拆报警解除
acc_newEventNo_237=读头在线
acc_newEventNo_239=设备呼叫
acc_newEventNo_240=通话结束
acc_newEventNo_243=火警输入断开
acc_newEventNo_244=火警输入短路
acc_newEventNo_247=扩展板在线
acc_newEventNo_4008=市电恢复
acc_newEventNo_4014=消防输入信号断开，结束门常开
acc_newEventNo_4015=门已在线
acc_newEventNo_4018=后台比对开门
acc_newEventNo_5023=消防状态受限中
acc_newEventNo_5024=多人验证超时
acc_newEventNo_5029=后台比对失败
acc_newEventNo_6005=记录容量即将达到上限
acc_newEventNo_6006=线路短路(RS485)
acc_newEventNo_6007=线路短路(韦根)
acc_newEventNo_6011=门已离线
acc_newEventNo_6012=门拆机报警
acc_newEventNo_6013=消防输入信号触发，开启门常开
acc_newEventNo_6015=复位扩展设备电源
acc_newEventNo_6016=恢复设备出厂设置
acc_newEventNo_6070=后台比对(禁止名单)
acc_eventNo_undefined=事件编号未定义
acc_advanceEvent_500=全局反潜(逻辑)
acc_advanceEvent_501=人员有效性(有效日期)
acc_advanceEvent_502=人数控制
acc_advanceEvent_503=全局互锁
acc_advanceEvent_504=线路定义
acc_advanceEvent_505=全局反潜(定时)
acc_advanceEvent_506=全局反潜(定时逻辑)
acc_advanceEvent_507=人员有效性(第一次使用后有效天数)
acc_advanceEvent_508=人员有效性(使用次数)
acc_advanceEvent_509=后台验证失败(人未登记)
acc_advanceEvent_510=后台验证失败(数据异常)
acc_alarmEvent_701=DMR报警(设置规则：{0})
#[21]实时监控
acc_rtMonitor_openDoor=开门
acc_rtMonitor_closeDoor=关门
acc_rtMonitor_remoteNormalOpen=远程常开
acc_rtMonitor_realTimeEvent=实时事件
acc_rtMonitor_photoMonitor=照片监测
acc_rtMonitor_alarmMonitor=报警监测
acc_rtMonitor_doorState=门状态
acc_rtMonitor_auxOutName=辅助输出名称
acc_rtMonitor_nonsupport=不支持
acc_rtMonitor_lock=锁定
acc_rtMonitor_unLock=解锁
acc_rtMonitor_disable=禁用
acc_rtMonitor_noSensor=无门磁
acc_rtMonitor_alarm=报警
acc_rtMonitor_openForce=门被意外打开
acc_rtMonitor_tamper=防拆
acc_rtMonitor_duressPwdOpen=胁迫密码开门
acc_rtMonitor_duressFingerOpen=胁迫指纹开门
acc_rtMonitor_duressOpen=胁迫开门
acc_rtMonitor_openTimeout=门开超时
acc_rtMonitor_unknown=未知
acc_rtMonitor_noLegalDoor=当前没有符合条件的门！
acc_rtMonitor_noLegalAuxOut=当前没有符合条件的辅助输出！
acc_rtMonitor_curDevNotSupportOp=当前设备状态不支持该操作！
acc_rtMonitor_curNormalOpen=当前已常开
acc_rtMonitor_whetherDisableTimeZone=当前门处于常开状态，是否禁用当天常开时间段后关门？
acc_rtMonitor_curSystemNoDoors=当前系统中没有添加门或者没有查询到符合您需要的门！
acc_rtMonitor_cancelAlarm=取消报警
acc_rtMonitor_openAllDoor=开当前所有门
acc_rtMonitor_closeAllDoor=关当前所有门
acc_rtMonitor_confirmCancelAlarm=你确定要取消报警？
acc_rtMonitor_calcelAllDoor=取消全部报警
acc_rtMonitor_initDoorStateTip=正在获取系统内用户授权范围内的所有门......
acc_rtMonitor_alarmEvent=报警事件
acc_rtMonitor_ackAlarm=确认报警
acc_rtMonitor_ackAllAlarm=确认所有报警
acc_rtMonitor_ackAlarmTime=确认报警时间
acc_rtMonitor_sureToAckThese=你确定要确认这{0}条报警？确认后报警将被取消。
acc_rtMonitor_sureToAckAllAlarm=你确定要确认所有报警？确认后报警将被取消。
acc_rtMonitor_noSelectAlarmEvent=请选择要确认的报警事件！
acc_rtMonitor_noAlarmEvent=当前系统没有报警事件！
acc_rtMonitor_forcefully=取消门被意外打开报警
acc_rtMonitor_addToRegPerson=添加到已登记人员
acc_rtMonitor_cardExist=此卡已被 {0} 占用，不能重复发卡！
acc_rtMonitor_opResultPrompt=发送请求成功{0}个，失败{1}个！
acc_rtMonitor_doorOpFailedPrompt=以下门发送请求失败，请重试！
acc_rtMonitor_remoteOpen=远程打开
acc_rtMonitor_remoteClose=远程关闭
acc_rtMonitor_alarmSoundClose=已关闭音频
acc_rtMonitor_alarmSoundOpen=已开启音频
acc_rtMonitor_playAudio=声音提醒
acc_rtMonitor_isOpenShowPhoto=开启显示照片功能
acc_rtMonitor_isOpenPlayAudio=开启声音提醒功能
acc_rtm_open=远程释放按键
acc_rtm_close=远程锁定按键
acc_rtm_eleModule=梯控
acc_cancelAlarm_fp=取消胁迫指纹开门报警
acc_cancelAlarm_pwd=取消胁迫密码开门报警
acc_cancelAlarm_timeOut=取消门开超时报警
#定时同步设备时间
acc_timing_syncDevTime=定时同步设备时间
acc_timing_executionTime=执行时间
acc_timing_theLifecycle=执行周期
acc_timing_errorPrompt=输入有误，请输入1-31之间的数字！
acc_timing_checkedSyncTime=请选择同步时间
#[25]门禁报表
acc_trans_hasAccLevel=可以进出的门
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=请新增门禁区域
acc_zone_code=编号
acc_zone_parentZone=上级门禁区域
acc_zone_parentZoneCode=上级门禁区域编号
acc_zone_parentZoneName=上级门禁区域名称
acc_zone_outside=外围区域
#[G2]读头定义
acc_readerDefine_readerName=读头名称
acc_readerDefine_fromZone=从门禁区域
acc_readerDefine_toZone=到门禁区域
acc_readerDefine_delInfo1=该读头定义相关的门禁区域被高级门禁功能引用，无法删除！
acc_readerDefine_selReader=选择读头
acc_readerDefine_selectReader=请添加读头！
acc_readerDefine_tip=人员到达外围区域后，将会删除该区域内该人员的记录。
#[G3]全局反潜
acc_gapb_zone=门禁区域
acc_gapb_whenToResetGapb=重置反潜的时间
acc_gapb_apbType=反潜类型
acc_gapb_logicalAPB=逻辑反潜
acc_gapb_timedAPB=定时反潜
acc_gapb_logicalTimedAPB=定时逻辑反潜
acc_gapb_lockoutDuration=锁闭时长
acc_gapb_devOfflineRule=控制器离线时
acc_gapb_standardLevel=标准通行权限
acc_gapb_accessDenied=拒绝用户通行
acc_gapb_doorControlZone=门列表控制门禁区域的进出通行
acc_gapb_resetStatus=重置全局反潜状态
acc_gapb_obeyAPB=服从全局反潜规则
acc_gapb_isResetGAPB=重置反潜
acc_gapb_resetGAPBSuccess=重置反潜状态成功
acc_gapb_resetGAPBFaile=重置反潜状态失败
acc_gapb_chooseArea=请重新选择区域
acc_gapb_notDelInfo1=该门禁区域被引用为上级门禁区域，不能删除！
acc_gapb_notDelInfo2=该门禁区域被读头定义引用，不能删除！
acc_gapb_notDelInfo3=该门禁区域被高级门禁功能引用，不能删除！
acc_gapb_notDelInfo4=该门禁区域被LED引用，不能删除！
acc_gapb_zoneNumRepeat=门禁区域编号重复！
acc_gapb_zoneNameRepeat=门禁区域名称重复！
acc_gapb_personResetGapbPre=确定要重置这
acc_gapb_personResetGapbSuffix=条人员反潜规则？
acc_gapb_apbPrompt=同一个门不能被用作控制两个相对独立的反潜边界！
acc_gapb_occurApb=反潜发生时
acc_gapb_noOpenDoor=不开门
acc_gapb_openDoor=开门
acc_gapb_zoneNumLength=长度大于20字符！
acc_gapb_zoneNameLength=长度大于30字符！
acc_gapb_zoneRemarkLength=长度大于50字符！
acc_gapb_isAutoServerMode=检测到有设备未开启后台验证功能，可能对功能造成影响，是否立即开启？
acc_gapb_applyTo=应用到
acc_gapb_allPerson=所有人员
acc_gapb_justSelected=选中人员
acc_gapb_excludeSelected=除选中以外的人员
#[G4]who is inside
acc_zoneInside_lastAccessTime=最近进入时间
acc_zoneInside_lastAccessReader=最近进入读头
acc_zoneInside_noPersonInZone=该门禁区域内没有人员！
acc_zoneInside_noRulesInZone=没有设置规则！
acc_zoneInside_totalPeople=总人数
acc_zonePerson_selectPerson=请选择人员或部门！
#[G5]路径
acc_route_name=路线名称
acc_route_setting=路线设置
acc_route_addReader=添加读头
acc_route_delReader=删除读头
acc_route_defineReaderLine=定义读头路线
acc_route_up=上移
acc_route_down=下移
acc_route_selReader=请选择读头后再进行相应操作！
acc_route_onlyOneOper=只能选择一个进行操作！
acc_route_readerOrder=读头定义顺序
acc_route_atLeastSelectOne=请至少选择一个读头！
acc_route_routeIsExist=此条路线已经存在！
#[G6]DMR
acc_dmr_residenceTime=停留时间
acc_dmr_setting=停留时间设置
#[G7]Occupancy
acc_occupancy_max=最大容纳人数
acc_occupancy_min=最小容纳人数
acc_occupancy_unlimit=无限制
acc_occupancy_note=最大容纳人数应大于最小容纳人数！
acc_occupancy_containNote=最大/最小容纳人数请至少输入一个！
acc_occupancy_maxMinValid=请输入大于0的数字！
acc_occupancy_conflict=该区域已设置人数控制规则！
acc_occupancy_maxMinTip=最大/最小容纳人数为空表示不限制。
#card availability
acc_personLimit_zonePropertyName=门禁区域属性名称
acc_personLimit_useType=使用方式
acc_personLimit_userDate=有效日期
acc_personLimit_useDays=第一次使用后有效天数
acc_personLimit_useTimes=使用次数
acc_personLimit_setZoneProperty=设置门禁区域属性
acc_personLimit_zoneProperty=门禁区域属性
acc_personLimit_availabilityName=人员有效性名称
acc_personLimit_days=天数
acc_personLimit_Times=次数
acc_personLimit_noDel=选择的门禁区域属性被引用，不能删除！
acc_personLimit_cannotEdit=该门禁区域属性被引用，不能修改使用方式！
acc_personLimit_detail=详情
acc_personLimit_userDateTo=有效期至
acc_personLimit_addPersonRepeatTip=所选部门下人员已添加到门禁区域属性，请重新选择部门!
acc_personLimit_leftTimes=剩余{0}次
acc_personLimit_expired=已过期
acc_personLimit_unused=未使用
#全局互锁
acc_globalInterlock_addGroup=添加组
acc_globalInterlock_delGroup=删除组
acc_globalInterlock_refuseAddGroupMessage=同一互锁中，组内的门不能重复添加
acc_globalInterlock_refuseAddlockMessage=添加的门已出现在互锁的其他组中
acc_globalInterlock_refuseDeleteGroupMessage=请先删除互锁中相关数据
acc_globalInterlock_isGroupInterlock=组内互锁
acc_globalInterlock_isAddTheDoorImmediately=是否立即添加门
acc_globalInterlock_isAddTheGroupImmediately=是否立即添加组
#门禁参数设置
acc_param_autoEventDev=自动下载事件记录并发设备数量
acc_param_autoEventTime=自动下载事件记录并发事件间隔
acc_param_noRepeat=邮箱地址不能重复，请重新填写
acc_param_most18=最多添加18个邮箱地址
acc_param_deleteAlert=不能删除所有邮箱输入框
acc_param_invalidOrRepeat=邮箱地址格式错误或邮箱地址重复
#全局联动
acc_globalLinkage_noSupport=当前选中的门不支持锁定、解锁功能
acc_globalLinkage_trigger=触发全局联动
acc_globalLinkage_noAddPerson=该联动规则触发条件中不包含与人员相关的条件，默认适用于所有人员！
acc_globalLinkage_selectAtLeastOne=请至少选择一项联动触发操作！
acc_globalLinkage_selectTrigger=请添加联动触发条件！
acc_globalLinkage_selectInput=请添加联动输入点！
acc_globalLinkage_selectOutput=请添加联动输出点！
acc_globalLinkage_audioRemind=联动声音提醒
acc_globalLinkage_audio=联动声音
acc_globalLinkage_isApplyToAll=适用于所有人
acc_globalLinkage_scope=人员范围
acc_globalLinkage_everyPerson=任意
acc_globalLinkage_selectedPerson=选定
acc_globalLinkage_noSupportPerson=不支持
acc_globalLinkage_reselectInput=触发条件类型发生变化，请重新选择联动输入点！
acc_globalLinkage_addPushDevice=请添加支持该功能的设备！
#其他
acc_InputMethod_tips=请切换为英文输入法状态！
acc_device_systemCheckTip=检测到门禁设备信息未添加！
acc_notReturnMsg=未获取到返回信息！
acc_validity_period=许可已过有效期，该功能不能操作！
acc_device_pushMaxCount=系统中已存在{0}台设备，达到许可上限，无法添加设备！
acc_device_videoHardwareLinkage=视频硬联动设置
acc_device_videoCameraIP=摄像机IP地址
acc_device_videoCameraPort=摄像机端口
acc_location_unable=该事件点未添加到电子地图中，无法定位该人员的具体位置！
acc_device_wgDevMaxCount=系统中设备已达到许可上限，无法修改设置！
#自定义报警事件
acc_deviceEvent_selectSound=请选择音频文件
acc_deviceEvent_batchSetSoundErr=批量设置报警声音异常！
acc_deviceEvent_batchSet=设置声音
acc_deviceEvent_sound=事件声音
acc_deviceEvent_exist=已存在
acc_deviceEvent_upload=重新上传
#查询门最近发生事件
acc_doorEventLatestHappen=查询门最近发生事件
#门禁人员信息
acc_pers_delayPassage=延长通行
#设备容量提示
acc_dev_usageConfirm=出现容量超出90%的设备
acc_dev_immediateCheck=立即查看
acc_dev_inSoftware=软件中
acc_dev_inFirmware=固件中
acc_dev_get=查看
acc_dev_getAll=查看全部
acc_dev_loadError=加载失败
#Reader
acc_reader_inout=出入
acc_reader_lightRule=灯亮规则
acc_reader_defLightRule=默认规则
acc_reader_encrypt=加密
acc_reader_allReaderOfCurDev=当前设备所有读头
acc_reader_tip1=加密选项默认复制到同一设备的所有读头！
acc_reader_tip2=身份证模式选项仅适用于支持该功能的读头！
acc_reader_tip3=RS485协议类型默认复制到同一设备的所有读头，设备重启后生效！
acc_reader_tip4=隐藏部分人员信息选项默认复制到同一设备的所有读头！
acc_reader_commType=通讯类型
acc_reader_commAddress=通讯地址
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=韦根
acc_readerCommType_wg485=韦根/RS485
acc_readerCommType_disable=禁用
acc_readerComAddress_repeat=同一种通讯地址重复
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=RS485地址
acc_readerCommType_wgAddress=韦根地址
acc_reader_macError=请输入正确格式的Mac地址！
acc_reader_machineType=读头型号
acc_reader_readMode=模式
acc_reader_readMode_normal=普通模式
acc_reader_readMode_idCard=身份证模式
acc_reader_note=温馨提示：只能选择与摄像机同一区域（{0}）的门禁设备
acc_reader_rs485Type=RS485协议类型
acc_reader_userLock=人员锁定
acc_reader_userInfoReveal=隐藏部分人员信息
#operat
acc_operation_pwd=授权密码
acc_operation_pwd_error=授权密码有误
acc_new_input_not_same=新密钥输入不一致
acc_op_set_keyword=授权密钥设置
acc_op_old_key=旧授权密钥
acc_op_new_key=新授权密钥
acc_op_cofirm_key=授权密钥确认
acc_op_old_key_error=旧密钥输入有误
#验证方式规则
acc_verifyRule_name=规则名称
acc_verifyRule_door=验证方式-门
acc_verifyRule_person=验证方式-人
acc_verifyRule_copy=复制星期一的验证方式到其他工作日：
acc_verifyRule_tip1=请至少选择一个验证方式！
acc_verifyRule_tip2=如果规则中包含人的验证方式，则只能添加包含韦根读头的门！
acc_verifyRule_tip3=RS485读头只能遵循门的验证方式，不支持优先使用人的验证方式。
acc_verifyRule_oldVerifyMode=旧验证方式
acc_verifyRule_newVerifyMode=新验证方式
acc_verifyRule_newVerifyModeSelectTitle=选择新验证方式
acc_verifyRule_newVerifyModeNoSupportTip=不存在支持新验证方式的设备!
#Wiegand Test
acc_wiegand_beforeCard=当前卡的卡号长度：{0} 位（二进制），与上一张卡不相同！
acc_wiegand_curentCount=当前卡号位数为：{0}（二进制）
acc_wiegand_card=卡
acc_wiegand_readCard=读卡
acc_wiegand_clearCardInfo=清除卡号信息
acc_wiegand_originalCard=原始卡号
acc_wiegand_recommendFmt=推荐格式
acc_wiegand_parityFmt=奇偶校验格式
acc_wiegand_withSizeCode=无区位码时自动推算区位码
acc_wiegand_tip1=上述卡的区位码不一致，可能不属于同一批次。
acc_wiegand_tip2=区位码:{0}，卡号:{1}，在原始卡号中匹配出现异常。请检查输入的区位码和卡号是否正确！
acc_wiegand_tip3=输入的卡号({0})无法在原始卡号中匹配到,请再次确认输入的卡号是否正确！
acc_wiegand_tip4=输入的区位码({0})无法在原始卡号中匹配到,请再次确认输入的区位码是否正确！
acc_wiegand_tip5=如果需要使用推算区位码功能，请确保区位码所在列均为空！
acc_wiegand_warnInfo1=更换新的卡进行读卡操作时，请手动切换到下一张卡。
#LCD实时监控
acc_leftMenu_LCDRTMonitor=出入人员面板
acc_LCDRTMonitor_current=当前人员信息
acc_LCDRTMonitor_previous=历史人员信息
#api
acc_api_levelIdNotNull=权限组ID不能为空
acc_api_areaNameNotNull=区域不能为空
acc_api_levelExist=权限组已存在
acc_api_levelNotExist=权限组不存在
acc_api_levelNotHasPerson=权限组下没有人员
acc_api_doorIdNotNull=门ID不能为空
acc_api_doorNameNotNull=门名称不能为空
acc_api_doorIntervalSize=开门时长需在1~254之间
acc_api_doorNotExist=门不存在
acc_api_devOffline=设备处于离线或禁用状态
acc_api_devSnNotNull=设备SN不能为空
acc_api_timesTampNotNull=时间戳不能为空
acc_api_openingTimeCannotBeNull=开门时长不能为空
acc_api_parameterValueCannotBeNull=参数值不能为空
acc_api_deviceNumberDoesNotExist=设备序列号不存在
acc_api_readerIdCannotBeNull=读头ID不能为空
acc_api_theReaderDoesNotExist=读头不存在
acc_operate_door_notInValidDate=当前不在远程开门有效时间内，如需请联系管理员！
acc_api_doorOffline=门处于离线或禁用状态
#门禁信息自动导出
acc_autoExport_title=事件记录自动导出
acc_autoExport_frequencyTitle=自动导出频率
acc_autoExport_frequencyDay=每天
acc_autoExport_frequencyMonth=每月
acc_autoExport_firstDayMonth=每月第一天
acc_autoExport_specificDate=导出日期
acc_autoExport_exportModeTitle=导出模式
acc_autoExport_dailyMode=每日事件记录
acc_autoExport_monthlyMode=每月事件记录（上个月所有的信息和本月的事件记录）
acc_autoExport_allMode=所有事件记录（最多30000条事件记录）
acc_autoExport_recipientMail=接收邮箱
#First In And Last Out
acc_inOut_inReaderName=最早进入-读头名称
acc_inOut_firstInTime=最早进入时间
acc_inOut_outReaderName=最晚离开-读头名称
acc_inOut_lastOutTime=最晚离开时间
#防疫参数
acc_dev_setHep=设置防疫参数
acc_dev_enableIRTempDetection=开启红外测温检测
acc_dev_enableNormalIRTempPass=体温异常不通行
acc_dev_enableMaskDetection=开启口罩检测
acc_dev_enableWearMaskPass=未戴口罩不允许通行
acc_dev_tempHighThreshold=体温高温报警阈值
acc_dev_tempUnit=温度单位
acc_dev_tempCorrection=体温校正偏差值
acc_dev_enableUnregisterPass=允许未登记人员通行
acc_dev_enableTriggerAlarm=触发外部报警功能
#联动邮件
acc_mail_temperature=体温
acc_mail_mask=是否佩戴口罩
acc_mail_unmeasured=未测量
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort全局事件
acc_digifort_chooseDigifortEvents=选择Digifort全局事件
acc_digifort_eventExpiredTip=如果从Digifort服务器删除了全局事件，它将以红色显示。
acc_digifort_checkConnection=请检查Digifort服务器的连接信息是否正确。
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=添加联系人
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=设置扩展参数
acc_extendParam_faceUI=界面显示
acc_extendParam_faceParam=人脸参数
acc_extendParam_accParam=门禁参数
acc_extendParam_intercomParam=可视对讲参数
acc_extendParam_volume=音量
acc_extendParam_identInterval=识别间隔(毫秒)
acc_extendParam_historyVerifyResult=显示历史验证结果
acc_extendParam_macAddress=显示MAC地址
acc_extendParam_showIp=显示IP地址
acc_extendParam_24HourFormat=显示24小时格式
acc_extendParam_dateFormat=日期格式
acc_extendParam_1NThreshold=1:N阈值
acc_extendParam_facePitchAngle=人脸俯仰角度
acc_extendParam_faceRotationAngle=人脸旋转角度
acc_extendParam_imageQuality=图像质量
acc_extendParam_miniFacePixel=最小人脸像素
acc_extendParam_biopsy=开启活体检测
acc_extendParam_showThermalImage=显示热力图像
acc_extendParam_attributeAnalysis=开启属性分析
acc_extendParam_temperatureAttribute=体温检测属性
acc_extendParam_maskAttribute=口罩检测属性
acc_extendParam_minTemperature=体温检测阈值下限
acc_extendParam_maxTemperature=体温检测阈值上限
acc_extendParam_gateMode=闸机模式
acc_extendParam_qrcodeEnable=启用二维码功能
#可视对讲
acc_dev_intercomServer=可视对讲服务地址
acc_dev_intercomPort=可视对讲服务端口
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=同步权限组
# 夏令时名称
acc_dsTimeUtc_none=不设置
acc_dsTimeUtc_AreaNone=该区域没有夏令时
acc_dsTimeUtc1000_0=堪培拉，墨尔本，悉尼
acc_dsTimeUtc1000_1=霍巴特
acc_dsTimeUtc_0330_0=纽芬兰
acc_dsTimeUtc_1000_0=阿留申群岛
acc_dsTimeUtc_0200_0=中大西洋-旧用
acc_dsTimeUtc0930_0=阿德莱德
acc_dsTimeUtc_0100_0=亚速尔群岛
acc_dsTimeUtc_0400_0=大西洋时间(加拿大)
acc_dsTimeUtc_0400_1=圣地亚哥
acc_dsTimeUtc_0400_2=亚松森
acc_dsTimeUtc_0300_0=格陵兰
acc_dsTimeUtc_0300_1=圣皮埃尔和密克隆群岛
acc_dsTimeUtc0200_0=基希讷乌
acc_dsTimeUtc0200_1=赫尔辛基，基辅，里加，索非亚，塔林，维尔纽斯
acc_dsTimeUtc0200_2=雅典，布加勒斯特
acc_dsTimeUtc0200_3=耶路撒冷
acc_dsTimeUtc0200_4=安曼
acc_dsTimeUtc0200_5=贝鲁特
acc_dsTimeUtc0200_6=大马士革
acc_dsTimeUtc0200_7=加沙，希伯伦
acc_dsTimeUtc0200_8=朱巴
acc_dsTimeUtc_0600_0=中部时间(美国和加拿大)
acc_dsTimeUtc_0600_1=瓜达拉哈拉，墨西哥城，蒙特雷
acc_dsTimeUtc_0600_2=复活节岛
acc_dsTimeUtc1300_0=萨摩亚群岛
acc_dsTimeUtc_0500_0=哈瓦那
acc_dsTimeUtc_0500_1=东部时间(美国和加拿大)
acc_dsTimeUtc_0500_2=海地
acc_dsTimeUtc_0500_3=印地安那州(东部)
acc_dsTimeUtc_0500_4=特克斯和凯科斯群岛
acc_dsTimeUtc_0800_0=太平洋时间(美国和加拿大)
acc_dsTimeUtc_0800_1=下加利福尼亚州
acc_dsTimeUtc0330_0=德黑兰
acc_dsTimeUtc0000_0=都柏林，爱丁堡，里斯本，伦敦
acc_dsTimeUtc1200_0=斐济
acc_dsTimeUtc1200_1=彼得罗巴甫洛夫斯克-堪察加-旧用
acc_dsTimeUtc1200_2=奥克兰，惠灵顿
acc_dsTimeUtc1100_0=诺福克岛
acc_dsTimeUtc_0700_0=奇瓦瓦，拉巴斯，马萨特兰
acc_dsTimeUtc_0700_1=山地时间(美国和加拿大)
acc_dsTimeUtc0100_0=贝尔格莱德，布拉迪斯拉发，布达佩斯，卢布尔雅那，布拉格
acc_dsTimeUtc0100_1=萨拉热窝，斯科普里，华沙，萨格勒布
acc_dsTimeUtc0100_2=卡萨布兰卡
acc_dsTimeUtc0100_3=布鲁塞尔，哥本哈根，马德里，巴黎
acc_dsTimeUtc0100_4=阿姆斯特丹，柏林，伯尔尼，罗马，斯德哥尔摩，维也纳
acc_dsTimeUtc_0900_0=阿拉斯加
#安全点(muster point)
acc_leftMenu_accMusterPoint=紧急疏散点
acc_musterPoint_activate=激活
acc_musterPoint_addDept=添加部门
acc_musterPoint_delDept=删除部门
acc_musterPoint_report=紧急疏散点报表
acc_musterPointReport_sign=手动签到
acc_musterPointReport_generate=生成报表
acc_musterPoint_addSignPoint=添加签到点
acc_musterPoint_delSignPoint=删除签到点
acc_musterPoint_selectSignPoint=请添加签到点！
acc_musterPoint_signPoint=签到点
acc_musterPoint_delFailTip=存在已经启动的紧急疏散点，不能删除！
acc_musterPointReport_enterTime=进入时间
acc_musterPointReport_dataAnalysis=数据分析
acc_musterPointReport_safe=安全
acc_musterPointReport_danger=危险
acc_musterPointReport_signInManually=手动打卡
acc_musterPoint_editTip=紧急疏散点已激活，不能编辑！
acc_musterPointEmail_total=应签到总人数
acc_musterPointEmail_safe=已签到人数（安全）
acc_musterPointEmail_dangerous=未签到人数（危险）
acc_musterPoint_messageNotification=激活时进行消息通知
acc_musterPointReport_sendEmail=定时推送报表
acc_musterPointReport_sendInterval=发送间隔
acc_musterPointReport_sendTip=请确保勾选的通知方式配置成功，否则通知将无法正常发送！
acc_musterPoint_mailSubject=紧急集合通知
acc_musterPoint_mailContent=请立即在"{0}"集合，并在"{1}"设备上签到，谢谢！
acc_musterPointReport_mailHead=你好，这是一份紧急情况报告。请查阅。
acc_musterPoint_visitorsStatistics=统计访客人员
# 报警监控
acc_alarm_priority=优先级
acc_alarm_total=总共
acc_alarm_today=今日记录
acc_alarm_unhandled=未确认
acc_alarm_inProcess=处理中
acc_alarm_acknowledged=已确认
acc_alarm_top5=报警事件前五名
acc_alarm_monitoringTime=监控时间
acc_alarm_history=报警处理历史
acc_alarm_acknowledgement=处理记录
acc_alarm_eventDescription=事件详情
acc_alarm_acknowledgeText=选中后将会发送报警事件详情邮件到指定的邮箱
acc_alarm_emailSubject=新增报警事件处理记录
acc_alarm_mute=静音
acc_alarm_suspend=暂停
acc_alarm_confirmed=该事件已被确认
acc_alarm_list=报警记录
#ntp
acc_device_setNTPService=NTP服务器设置
acc_device_setNTPServiceTip=输入多个服务器地址，请以逗号(,)或分号(;)隔开。
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=超级用户不受时间段、反潜、互锁、多卡开门、节假日、时间反潜等限制，有很高的开门优先级。
acc_editPerson_delayPassageTip=勾选后，可延长该人员通行时的等待时间。适用于残疾人或者其他有特别需要的人群。
acc_editPerson_disabledTip=勾选后，将临时禁用该人员的门禁权限。
#门禁向导
acc_guide_title=门禁模块操作指引
acc_guide_addPersonTip=需先添加人员和相应的凭证（人脸或指纹或卡或手掌或密码）；若已添加，则直接跳过此步骤
acc_guide_timesegTip=请配置有效的开门时间段
acc_guide_addDeviceTip=请添加对应的设备作为门禁点
acc_guide_addLevelTip=添加门禁权限
acc_guide_personLevelTip=给人员分配对应的门禁权限
acc_guide_rtMonitorTip=实时查看门禁记录
acc_guide_rtMonitorTip2=添加所属区域和对应门后实时查看门禁记录
#查看区域内人员
acc_zonePerson_cleanCount=清除人员进出统计数量
acc_zonePerson_inCount=人员进入数量统计
acc_zonePerson_outCount=人员离开数量统计
#biocv460
acc_device_validFail=用户名或密码不正确，校验失败！
acc_device_pwdRequired=只能输入最大6位整数


#异常记录相关国际化配置 - 中文
#左侧菜单
acc_leftMenu_exceptionRecord=异常记录
acc_exception_record=异常记录
acc_exception_receiverPosition=接收人员职位
acc_exception_enterTime=进入时间
acc_exception_exitTime=外出时间
acc_exception_subject=主题
acc_exception_status=异常状态
acc_exception_sendTime=发送时间
acc_exception_sendStatus=状态
acc_exception_errorMessage=异常记录错误信息
acc_sendWehcat_errorMessage=推送微信信息
acc_exception_sendStartTime=发送开始时间
acc_exception_sendEndTime=发送结束时间

#异常记录主题
acc_exception_subject_abnormalAccess=异常进出
acc_exception_subject_late=迟到

#异常记录状态
acc_exception_status_unclosed=未闭环
acc_exception_status_returned=已返回

#发送状态
acc_exception_status_unsent=未发送
acc_exception_status_sent=发送成功
acc_exception_status_failed=发送失败

#操作相关
acc_exception_sendNotification=发送通知
acc_exception_resendFailed=重新发送失败记录
acc_exception_resend=重新发送
acc_exception_markAsReturned=标记为已返回
acc_exception_statistics=统计信息

#确认消息
acc_exception_confirm_sendNotification=确定要发送选中的异常记录通知吗？
acc_exception_confirm_resendFailed=确定要重新发送选中的失败记录吗？

#统计相关
acc_exception_totalCount=总记录数
acc_exception_unsentCount=未发送数量
acc_exception_sentCount=发送成功数量
acc_exception_failedCount=发送失败数量
acc_exception_unclosedCount=未闭环数量
acc_exception_returnedCount=已返回数量
acc_is_Push_Exception=是否推送异常记录