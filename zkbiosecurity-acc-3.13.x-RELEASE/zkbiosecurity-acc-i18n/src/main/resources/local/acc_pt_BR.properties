#[1]左侧菜单
acc_module=Acesso
acc_leftMenu_accDev=Dispositivo de Acesso
acc_leftMenu_auxOut=Saída Auxiliar
acc_leftMenu_dSTime=Horário de Verão
acc_leftMenu_access=Controle de Acesso
acc_leftMenu_door=Porta
acc_leftMenu_accRule=Regra de Acesso
acc_leftMenu_interlock=Intertravamento
acc_leftMenu_antiPassback=Antirretorno
acc_leftMenu_globalLinkage=Vinculação Global
acc_leftMenu_firstOpen=Abertura pela Primeira Pessoa
acc_leftMenu_combOpen=Autenticação de Multi-Pessoas
acc_leftMenu_personGroup=Grupo de Multi-Pessoas
acc_leftMenu_level=Níveis de Acesso
acc_leftMenu_electronicMap=Mapa
acc_leftMenu_personnelAccessLevels=Níveis de Acesso de Pessoal
acc_leftMenu_searchByLevel=Por Nível de Acesso
acc_leftMenu_searchByDoor=Permissão de Acesso por Porta
acc_leftMenu_expertGuard=Funções Avançadas
acc_leftMenu_zone=Zona
acc_leftMenu_readerDefine=Definir Leitor
acc_leftMenu_gapbSet=Antirretorno Global
acc_leftMenu_whoIsInside=Quem Está Dentro
acc_leftMenu_whatRulesInside=Quais Regras se Aplicam Dentro
acc_leftMenu_occupancy=Controle de Ocupação
acc_leftMenu_route=Controle de Rota
acc_leftMenu_globalInterlock=Intertravamento Global
acc_leftMeue_globalInterlockGroup=Grupo de Intertravamento Global
acc_leftMenu_dmr=Regra do Homem Morto
acc_leftMenu_personLimit=Validade do Acesso
acc_leftMenu_verifyModeRule=Modo de Verificação
acc_leftMenu_verifyModeRulePersonGroup=Grupo do Modo de Verificação
acc_leftMenu_extDev=Placa de E/S
acc_leftMenu_firstInLastOut=Primeira Entrada e Última Saída
acc_leftMenu_accReports=Relatórios de Acesso
#[3]门禁时间段
acc_timeSeg_entity=Faixa Horária
acc_timeSeg_canNotDel=O período está em uso e não pode ser excluído!
#[4]门禁设备--公共的在common中
acc_common_ruleName=Nome da Regra
acc_common_hasBeanSet=Foi definido
acc_common_notSet=Não foi definido
acc_common_hasBeenOpened=Foi aberto
acc_common_notOpened=Não foi aberto
acc_common_partSet=Parte da Configuração
acc_common_linkageAndApbTip=Vinculação e vinculação global, antirretorno e antirretorno global estão definidos no mesmo tempo, podem ocorrer conflitos.
acc_common_vidlinkageTip=Verifique se a vinculação do ponto de entrada correspondente está vinculada ao canal de vídeo disponível, caso contrário, a função de vinculação de vídeo não funcionará!
acc_common_accZoneFromTo=Não é possível definir a mesma zona
acc_common_logEventNumber=ID do Evento
acc_common_bindOrUnbindChannel=Vincular/desvincular a câmera
acc_common_boundChannel=Câmera vinculada
#设备信息
acc_dev_iconType=Tipo de Ícone
acc_dev_carGate=Cancela de Estacionamento
acc_dev_channelGate=Catraca Flap
acc_dev_acpType=Tipo de Painel de Controle
acc_dev_oneDoorACP=Painel de Controle de Acesso de Uma Porta
acc_dev_twoDoorACP=Painel de Controle de Acesso de Duas Portas
acc_dev_fourDoorACP=Painel de Controle de Acesso de Quatro Portas
acc_dev_onDoorACD=Dispositivo Standalone
acc_dev_switchToTwoDoorTwoWay=Alterar para Duas Portas Bidirecional
acc_dev_addDevConfirm2=Dica: A conexão do dispositivo foi bem-sucedida, mas o tipo de painel de controle de acesso é diferente do atual, altere para o {0}painel de controle da (s) porta (s). Continuar a adicionar?
acc_dev_addDevConfirm4=Dispositivo Standalone. Continuar a adicionar?
acc_dev_oneMachine=Dispositivo Standalone
acc_dev_fingervein=Veias do Dedo
acc_dev_control=Controle do Dispositivo
acc_dev_protocol=Tipo de protocolo
acc_ownedBoard=Proprietário
#设备操作
acc_dev_start=Enviar
acc_dev_accLevel=Autorização de Acesso
acc_dev_timeZoneAndHoliday=Faixa horária, feriados
acc_dev_linkage=Vinculação
acc_dev_doorOpt=Parâmetros da Porta
acc_dev_firstPerson=Primeira Pessoa Porta Aberta
acc_dev_multiPerson=Porta Aberta por Combinação de Acesso
acc_dev_interlock=Intertravamento
acc_dev_antiPassback=Antirretorno
acc_dev_wiegandFmt=Formato Wiegand
acc_dev_outRelaySet=Configurações de Saída Auxiliar
acc_dev_backgroundVerifyParam=Opções de Verificação BG
acc_dev_getPersonInfoPrompt=Verificar se você obteve informações pessoais com sucesso, caso contrário, uma exceção ocorrerá. Continuar?
acc_dev_getEventSuccess=Receber eventos com sucesso.
acc_dev_getEventFail=Falha ao obter evento(s).
acc_dev_getInfoSuccess=Obter informações com sucesso.
acc_dev_getInfoXSuccess=Obter {0} com sucesso.
acc_dev_getInfoFail=Falha ao obter a informação.
acc_dev_updateExtuserInfoFail=Falha ao atualizar as informações pessoais para passagem prolongada, recupere as informações.
acc_dev_getPersonCount=Obter a Quantidade de Usuários
acc_dev_getFPCount=Obter a Quantidade de Impressões Digitais
acc_dev_getFVCount=Obter Quantidade de Veias do Dedo
acc_dev_getFaceCount=Obter Quantidade de Faces
acc_dev_getPalmCount=Obter a Quantidade de Palmas
acc_dev_getBiophotoCount=Obter Quantidade de Imagens de Face
acc_dev_noData=Nenhum dado foi retornado do dispositivo.
acc_dev_noNewData=Não há novas transações no dispositivo.
acc_dev_softLtDev=Há mais pessoas no software do que no dispositivo.
acc_dev_personCount=Quantidade de Pessoas:
acc_dev_personDetail=Informações a seguir:
acc_dev_softEqualDev=A quantidade de pessoas no software e no dispositivo é a mesma.
acc_dev_softGtDev=Existem mais pessoas no dispositivo do que no software.
acc_dev_cmdSendFail=Falha ao enviar comandos, favor reenviar.
acc_dev_issueVerifyParam=Definir as Opções de Verificação BG
acc_dev_verifyParamSuccess=Opções de Verificação BG aplicadas com sucesso
acc_dev_backgroundVerify=Verificação em Segundo Plano
acc_dev_selRightFile=Selecione o arquivo de atualização correto!
acc_dev_devNotOpForOffLine=O dispositivo está offline, tente novamente mais tarde
acc_dev_devNotSupportFunction=O dispositivo não suporta esta função
acc_dev_devNotOpForDisable=O dispositivo está desativado, tente novamente mais tarde
acc_dev_devNotOpForNotOnline=O dispositivo está offline ou desativado, tente novamente mais tarde
acc_dev_getPersonInfo=Obter informações de pessoas
acc_dev_getFPInfo=Obter informações sobre impressões digitais
acc_dev_getFingerVeinInfo=Obter informações sobre as veias dos dedos
acc_dev_getPalmInfo=Obter informações de Palma
acc_dev_getBiophotoInfo=Obter informações da Imagem da Face
acc_dev_getIrisInfo=Obter informação sobre a íris
acc_dev_disable=desabilitado, selecione novamente
acc_dev_offlineAndContinue=está offline, continuar?
acc_dev_offlineAndSelect=está offline.
acc_dev_opAllDev=Todos os dispositivos
acc_dev_opOnlineDev=Dispositivo Online
acc_dev_opException=gerenciar exceções
acc_dev_exceptionAndConfirm=Tempo esgotado de conexão do dispositivo, a operação falhou. Verifique a conexão de rede
acc_dev_getFaceInfo=Obter informações de Face
acc_dev_selOpDevType=Selecione o tipo de dispositivo a ser operado:
acc_dev_hasFilterByFunc=Mostrar apenas os dispositivos on-line e com suporte para esta função!
acc_dev_masterSlaveMode=Modo master e slave RS485
acc_dev_master=Host
acc_dev_slave=Slave
acc_dev_modifyRS485Addr=Modificar o Endereço do RS485
acc_dev_rs485AddrTip=Digite um número inteiro entre 1 e 63!
acc_dev_enableFeature=Os dispositivos que ativaram a verificação em segundo plano
acc_dev_disableFeature=Os dispositivos que desativaram a verificação em segundo plano
acc_dev_getCountOnly=Obter apenas a contagem
acc_dev_queryDevPersonCount=Consulta de Contagem de Pessoal
acc_dev_queryDevVolume=Exibir Capacidade do Dispositivo
acc_dev_ruleType=Tipo de Regra
acc_dev_contenRule=Informações das Regras
acc_dev_accessRules=Exibir Regras do Dispositivos
acc_dev_ruleContentTip=Entre as diversas regras separados com um '|'.
acc_dev_rs485AddrFigure=Figura do Código do Endereço do RS485
acc_dev_addLevel=Adicionar ao Nível
acc_dev_personOrFingerTanto=O número de pessoas ou impressões digitais excede os limites, a sincronização falhou...
acc_dev_personAndFingerUnit=(Número)
acc_dev_setDstime=Definir o Horário de Verão
acc_dev_setTimeZone=Definir Fuso-Horário do Dispositivo
acc_dev_selectedTZ=Faixa horária Selecionado
acc_dev_timeZoneSetting=Definição de Faixa horária...
acc_dev_timeZoneCmdSuccess=Comando de Envio do Faixa horária com Sucesso...
acc_dev_enableDstime=Ativar o Horário de Verão
acc_dev_disableDstime=Desativar o Horário de Verão
acc_dev_timeZone=Faixa Horária
acc_dev_dstSettingTip=Definição do Horário de Verão...
acc_dev_dstDelTip=Removendo o Horário de Verão do Dispositivo...
acc_dev_enablingDst=Ativando o Horário de Verão
acc_dev_dstEnableCmdSuccess=Ativando o Envio do Comando de Horário de Verão com Sucesso.
acc_dev_disablingDst=Desativando o Horário de Verão.
acc_dev_dstDisableCmdSuccess=Desativando o Envio do Comando de Horário de Verão com Sucesso.
acc_dev_dstCmdSuccess=Envio do Comando de Horário de Verão com Sucesso.
acc_dev_usadst=Horário de verão
acc_dev_notSetDst=Sem configurações
acc_dev_selectedDst=Horário de Verão Selecionado
acc_dev_configMasterSlave=Configuração master-slave.
acc_dev_hasFilterByUnOnline=Mostrar apenas os dispositivos online.
acc_dev_softwareData=Se você achar que os dados não são consistentes com o dispositivo, sincronize os dados dos dispositivos antes de tentar novamente.
acc_dev_disabled=Os dispositivos desativados não podem ser operados!
acc_dev_offline=Os dispositivos off-line não podem ser operados!
acc_dev_noSupport=Os dispositivos não suportam esta função e não podem ser operados!
acc_dev_noRegDevTip=Este dispositivo não está definido como um dispositivo de registro. Os dados no software não serão atualizados. Gostaria de continuar?
acc_dev_noOption=Nenhuma opção qualificada
acc_dev_devFWUpdatePrompt=O dispositivo atual normalmente não usa a pessoa desativada e a função de validade da pessoa (consulte o manual do usuário).
acc_dev_panelFWUpdatePrompt=O dispositivo atual normalmente não usa a pessoa desativada e a função de validade da pessoa, atualizar imediatamente o firmware?
acc_dev_sendEventCmdSuccess=Comando Excluir Evento enviado com sucesso
acc_dev_tryAgain=Por favor, tente novamente.
acc_dev_eventAutoCheckAndUpload=Eventos automáticos de verificação e upload
acc_dev_eventUploadStart=Iniciar upload de eventos.
acc_dev_eventUploadEnd=Upload de eventos concluído.
acc_dev_eventUploadFailed=Falha no upload de eventos.
acc_dev_eventUploadPrompt=A versão do firmware do dispositivo é muito antiga, antes de atualizar o firmware, você deseja:
acc_dev_backupToSoftware=Realizar o back-up dos dados para o software
acc_dev_deleteEvent=Excluir o registro antigo
acc_dev_upgradePrompt=A versão do firmware é muito antiga e pode causar erros, realize o back-up dos dados para o software antes de atualizar o firmware.
acc_dev_conflictCardNo=Cartão existente no sistema para {0} em outra pessoa!
acc_dev_rebootAfterOperate=A operação foi bem-sucedida, o dispositivo será reiniciado posteriormente.
acc_dev_baseOptionTip=Anormalidade do parâmetro básico obtido.
acc_dev_funOptionTip=Anormalidade do parâmetro de função obtido.
acc_dev_sendComandoTip=Falha ao enviar o comando do parâmetro do dispositivo.
acc_dev_noC3LicenseTip=Não é possível adicionar este tipo de dispositivo ({0})! Entre em contato com nosso departamento de vendas.
acc_dev_combOpenDoorTip=({0}) A porta aberta por Combinação de Acesso foi configurada, ela não pode ser usada ao mesmo tempo com a verificação em segundo plano.
acc_dev_combOpenDoorPersonCountTip=O grupo {0} não pode abrir mais de {1}!
acc_dev_addDevTip=Isso se aplica apenas à adição de um dispositivo com protocolo de comunicação PULL!
acc_dev_addError=Exceção de adição de dispositivo, parâmetros ausentes ({0})!
acc_dev_updateIPAndPortError=Atualizar IP do servidor e erro de porta...
acc_dev_transferFilesTip=Teste de firmware concluído, transferindo arquivos
acc_dev_serialPortExist=Porta Serial Existente
acc_dev_isExist=Obtendo dispositivo
acc_dev_description=Descrição
acc_dev_searchEthernet=Procurar o dispositivo Ethernet
acc_dev_searchRS485=Procurar o dispositivo RS485
acc_dev_rs485AddrTip1=O endereço inicial do RS485 não pode ser maior que o endereço final
acc_dev_rs485AddrTip2=O escopo de pesquisa do RS485 deve ser menor que 20
acc_dev_clearAllCmdCache=Comando Limpar Tudo
acc_dev_authorizedSuccessful=Autorizado com Sucesso
acc_dev_authorize=Autorizar
acc_dev_registrationDevice=Dispositivo de Registro
acc_dev_setRegistrationDevice=Definir como Dispositivo de Registro
acc_dev_mismatchedDevice=Este dispositivo não pode ser usado para o seu mercado. Verifique o número de série do dispositivo com nossa equipe de vendas.
acc_dev_pwdStartWithZero=A senha de comunicação não pode começar com zero!
acc_dev_maybeDisabled=A licença atual permite apenas adicionar mais {0} porta (s), a porta do novo dispositivo adicionado que exceder o limite de portas da licença será desativada, você deseja continuar operando?
acc_dev_Limit=A Chave de Licença do dispositivo atingiu o limite superior para adicionar, favor autorizar.
acc_dev_selectDev=Por favor, selecione o dispositivo!
acc_dev_cannotAddPullDevice=Não é possível adicionar o dispositivo de puxar! Entre em contato com nossa equipe de vendas.
acc_dev_notContinueAddPullDevice=Há dispositivos {0} Push no sistema, não é possível continuar adicionando! Entre em contato com nossa equipe de vendas.
acc_dev_deviceNameNull=O modelo do dispositivo está vazio; não é possível adicionar o dispositivo!
acc_dev_commTypeErr=O Tipo de Comunicação não corresponde; não é possível adicionar o dispositivo!
acc_dev_inputDomainError=Digite um endereço de domínio válido.
acc_dev_levelTip=Existem mais de 5000 pessoas no nível, não é possível adicionar o dispositivo neste nível.
acc_dev_auxinSet=Configuração de Entrada Auxiliar
acc_dev_verifyModeRule=Regra do Modo de Verificação
acc_dev_netModeWired=Com Fio
acc_dev_netMode4G=4G
acc_dev_netModeWifi=WI-FI
acc_dev_updateNetConnectMode=Alterar Conexão de Rede
acc_dev_wirelessSSID=SSID Sem Fio
acc_dev_wirelessKey=Chave Sem Fio
acc_dev_searchWifi=Buscar rede WI-FI
acc_dev_testNetConnectSuccess=A conexão foi bem-sucedida?
acc_dev_testNetConnectFailed=A conexão não consegue se comunicar adequadamente!
acc_dev_signalIntensity=Potência do Sinal
acc_dev_resetSearch=Buscar Novamente
acc_dev_addChildDevice=Adicionar Subdispositivo
acc_dev_modParentDevice=Alterar o dispositivo master
acc_dev_configParentDevice=Configuração do dispositivo master
acc_dev_lookUpChildDevice=Ver dispositivos filhos
acc_dev_addChildDeviceTip=A autorização precisa ser realizada em um subdispositivo autorizado
acc_dev_maxSubCount=O número de subdispositivos autorizados excede o número máximo de %d unidades.
acc_dev_seletParentDevice=Por favor, selecione o dispositivo master!
acc_dev_networkCard=Placa de Rede
acc_dev_issueParam=Parâmetros de Sincronização Personalizados
acc_dev_issueMode=Modo de Sincronização
acc_dev_initIssue=Dados de Inicialização da Versão 3030 do firmware
acc_dev_customIssue=Sincronizar Dados Personalizados
acc_dev_issueData=Dados
acc_dev_parent=Dispositivo Master
acc_dev_parentEnable=O dispositivo master está desativado
acc_dev_parentTips=O dispositivo master vinculado excluirá todos os dados no dispositivo e precisará ser configurado novamente.
acc_dev_addDevIpTip=O novo endereço de IP não pode ser igual ao endereço IP do servidor
acc_dev_modifyDevIpTip=O novo endereço do servidor não pode ser igual ao endereço IP do dispositivo
acc_dev_setWGReader=Definir Leitor Wiegand
acc_dev_selectReader=Clique para selecionar o leitor
acc_dev_IllegalDevice=Dispositivo Ilegal
acc_dev_syncTimeWarnTip=Os períodos de sincronização para os seguintes dispositivos devem ser sincronizados no dispositivo master.
acc_dev_setTimeZoneWarnTip=O Faixa horária do dispositivo a seguir deve ser sincronizado no dispositivo master.
acc_dev_setDstimeWarnTip=O horário de verão para os seguintes dispositivos deve ser sincronizado no dispositivo master.
acc_dev_networkSegmentSame=Dois adaptadores de rede não podem usar o mesmo segmento de rede.
acc_dev_upgradeProtocolNoMatch=O protocolo do arquivo de atualização não corresponde
acc_dev_ipAddressConflict=Já existe um dispositivo com o mesmo endereço IP. Modifique o endereço IP do dispositivo e adicione-o novamente.
acc_dev_checkServerPortTip=A porta do servidor configurada é inconsistente com a porta de comunicação do sistema, o que pode resultar na falha em adicionar. Continuar operando?
acc_dev_clearAdmin=Remover Bloqueio de Administrador
acc_dev_setDevSate=Definir Status de Entrada/Saída do Dispositivo
acc_dev_sureToClear=Tem certeza de que deseja limpar as permissões de administrador do dispositivo?
acc_dev_hostState=Status do Dispositivo Master
acc_dev_regDeviceTypeTip=Este dispositivo é restrito e não tem permissão para adicionar diretamente. Entre em contato com o fornecedor do software!
acc_dev_extBoardType=Tipo de placa de E/S
acc_dev_extBoardTip=Após a configuração, você precisa reiniciar o dispositivo.
acc_dev_extBoardLimit=Somente {0} placa de E/S desse tipo pode ser adicionada a cada dispositivo!
acc_dev_replace=Substituir Dispositivo
acc_dev_replaceTip=Após a substituição, o dispositivo antigo não funcionará, tenha cuidado!
acc_dev_replaceTip1=Após a substituição, execute a operação "sincronizar todos os dados";
acc_dev_replaceTip2=Por favor, certifique-se de que o modelo do dispositivo de substituição é o mesmo!
acc_dev_replaceTip3=Certifique-se de que o dispositivo substituto tenha definido o mesmo endereço de servidor e porta do dispositivo antigo!
acc_dev_replaceFail=O tipo de máquina do dispositivo é inconsistente e não pode ser substituído!
acc_dev_notApb=Este dispositivo é incapaz de executar operações anti-submarino de portão ou de leitura de cabeça
acc_dev_upResourceFile=Carregar ficheiros de recursos
acc_dev_playOrder=Ordem de Reprodução
acc_dev_setFaceServerInfo=Definir os Parâmetros de Comparação da Infra- Estrutura Facial
acc_dev_faceVerifyMode=Modo de Comparação de Rostos
acc_dev_faceVerifyMode1=Comparação Local
acc_dev_faceVerifyMode2=Comparação de Infra- Estrutura
acc_dev_faceVerifyMode3=Prioridade de Comparação Local
acc_dev_faceBgServerType=Tipo de Servidor de Infra- Estrutura Facial
acc_dev_faceBgServerType1=Serviços de Plataforma de Software
acc_dev_faceBgServerType2=Serviços de terceiros
acc_dev_isAccessLogic=Activar a Verificação Lógica do Controlo de Acesso
#[5]门-其他关联的也复用此处
acc_door_entity=Porta
acc_door_number=Número da Porta
acc_door_name=Nome da Porta
acc_door_activeTimeZone=Faixa Horária Ativa
acc_door_passageModeTimeZone=Faixa Horária Livre
acc_door_setPassageModeTimeZone=Configurar Faixa horária do Modo de Passagem Livre
acc_door_notPassageModeTimeZone=Faixa horária do Modo Sem Passagem
acc_door_lockOpenDuration=Duração da Abertura da Fechadura
acc_door_entranceApbDuration=Duração do Antirretorno na entrada
acc_door_sensor=Sensor da Porta
acc_door_sensorType=Tipo de Sensor de Porta
acc_door_normalOpen=Normalmente aberto
acc_door_normalClose=Normalmente fechado
acc_door_sensorDelay=Atraso do Sensor da Porta
acc_door_closeAndReverseState=Status da Fechadura Reversa no fechamento da porta
acc_door_hostOutState=Sentido Dispositio Mestre
acc_door_slaveOutState=Sentido Dispositivo Auxiliar
acc_door_inState=Entrada
acc_door_outState=Saída
acc_door_requestToExit=Modo REX
acc_door_withoutUnlock=Fechar
acc_door_unlocking=Abrir
acc_door_alarmDelay=Espera REX
acc_door_duressPassword=Senha de Coação
acc_door_currentDoor=Porta Atual
acc_door_allDoorOfCurDev=Todas as portas no dispositivo atual
acc_door_allDoorOfAllDev=Todas as portas em todos os dispositivos
acc_door_allDoorOfAllControlDev=Todas as portas em todos os dispositivos de controle
acc_door_allDoorOfAllStandaloneDev=Todas as portas de todos os dispositivos StandAlone
acc_door_allWirelessLock=Todos as Fechaduras Sem Fio
acc_door_max6BitInteger=Máximo de 6 dígitos
acc_door_direction=Direção
acc_door_onlyInReader=Somente Leitor de Entrada
acc_door_bothInAndOutReader=Leitores de Entrada e Saída
acc_door_noDoor=Por favor, adicione a porta.
acc_door_nameRepeat=Os nomes das portas estão duplicados.
acc_door_duressPwdError=A senha de coação não deve ser igual a qualquer senha pessoal.
acc_door_urgencyStatePwd=Por favor insira um número inteiro de {0}bits!
acc_door_noDevOnline=Nenhum dispositivo está online ou a porta não suporta o modo de verificação do cartão.
acc_door_durationLessLock=A Espera do Sensor da Porta deve ser maior que a da Duração da Abertura da Fechadura.
acc_door_lockMoreDuration=A Duração da Abertura da Fechadura deve ser menor que o atrado do Sensor da Porta.
acc_door_lockAndExtLessDuration=A soma da Duração da Abertura da Fechadura e do Atraso de Passagem deve ser menor que a Espera do Sensor da Porta.
acc_door_noDevTrigger=O dispositivo não satisfaz as condições!
acc_door_relay=Retransmissão
acc_door_pin=Pin
acc_door_selDoor=Selecionar Porta
acc_door_sensorStatus=Sensor da Porta ({0})
acc_door_sensorDelaySeconds=Espera do Sensor da Porta ({0} s)
acc_door_timeSeg=Faixa horária ({0})
acc_door_combOpenInterval=Intervalo entre identificações Múlti-Pessoas
acc_door_delayOpenTime=Espera de Porta Aberta
acc_door_extDelayDrivertime=Espera de Passagem
acc_door_enableAudio=Ativar Alarme
acc_door_disableAudio=Desativar sons de alarme
acc_door_lockAndExtDelayTip=A soma da Duração da Abertura da Fechadura e do Intervalo de Tempo de Espera de Passagem não é maior que 254 segundos.
acc_door_disabled=As portas desativadas não podem ser operadas!
acc_door_offline=As portas off-line não podem ser operadas!
acc_door_notSupport=As portas a seguir não suportam essa função!
acc_door_select=Selecione a porta
acc_door_pushMaxCount=Há {0} portas ativadas no sistema e atingiram o limite da licença! Entre em contato com nosso departamento de vendas
acc_door_outNumber=A licença atual permite apenas adicionar mais {0} porta(s) Selecione novamente as portas e tente novamente ou entre em contato com nosso departamento de vendas para obter uma atualização da licença.
acc_door_latchTimeZone=Faixa horária REX
acc_door_wgFmtReverse=Reversão do número do cartão
acc_door_allowSUAccessLock=Permitir acesso de superusuário ao bloqueio
acc_door_verifyModeSinglePwd=As senhas não podem ser usadas como um método de verificação independente!
acc_door_doorPassword=Senha de abertura da porta
#辅助输入
acc_auxIn_timeZone=Faixa horária ativa
#辅助输出
acc_auxOut_passageModeTimeZone=Faixa horária do Modo de Passagem Livre
acc_auxOut_disabled=A saída auxiliar desativada não pode ser operada!
acc_auxOut_offline=A saída auxiliar off-line não pode ser operada!
#[8]门禁权限组
acc_level_doorGroup=Combinação de Portas
acc_level_openingPersonnel=Pessoal de Abertura
acc_level_noDoor=Não há nenhum item disponível. Por favor, adicione o dispositivo primeiro.
acc_level_doorRequired=A porta deve ser selecionada.
acc_level_doorCount=Contagem de Portas
acc_level_doorDelete=Excluir Porta
acc_level_isAddDoor=Adicionar portas imediatamente ao Nível de Controle de Acesso atual?
acc_level_master=Geral
acc_level_noneSelect=Por favor, adicione um nível de controle de acesso.
acc_level_useDefaultLevel=Mudar o nível de acesso para o novo departamento?
acc_level_persAccSet=Configurações de controle de acesso de pessoal
acc_level_visUsed={0} já está sendo usado pelo módulo de visitante e não pode ser excluído!
acc_level_doorControl=Controle de porta
acc_level_personExceedMax=O número de grupos de permissão atuais ({0}), e o número máximo de grupos de permissão opcionais ({1})
acc_level_exportLevel=Exportar Nível de Acesso
acc_level_exportLevelDoor=Exportar Portas de Nível de Acesso
acc_level_exportLevelPerson=Exportar Pessoas do Nível de Acesso
acc_level_importLevel=Importar Nível de Acesso
acc_level_importLevelDoor=Importar Portas de Nível de Acesso
acc_level_importLevelPerson=Importar Pessoas do Nível de Acesso
acc_level_exportDoorFileName=Informações das Portas do Nível de Acesso
acc_level_exportPersonFileName=Informações de Pessoas do Nível de Acesso
acc_levelImport_nameNotNull=O nome do nível de acesso não pode estar vazio
acc_levelImport_timeSegNameNotNull=Fuso Horário não pode estar vazio
acc_levelImport_areaNotExist=A área não existe!
acc_levelImport_timeSegNotExist=Fuso Horário não existe!
acc_levelImport_nameExist=O nome do Nível de Acesso {0} já existe!
acc_levelImport_levelDoorExist=As Portas de Nível de Acesso {0} já existem!
acc_levelImport_levelPersonExist=O Pessoal de Nível de Acesso {0} já existe!
acc_levelImport_noSpecialChar=O nome do nível de acesso não pode conter caracteres especiais!
#[10]首人常开
acc_firstOpen_setting=Primeira Pessoa Abertura Normal
acc_firstOpen_browsePerson=Procurar Pessoa
#[11]多人组合开门
acc_combOpen_comboName=Nome da Combinação
acc_combOpen_personGroupName=Nome do Grupo
acc_combOpen_personGroup=Grupo de Multi-Pessoas
acc_combOpen_verifyOneTime=Contagem Atual de Pessoas
acc_combOpen_eachGroupCount=Número de Pessoas para Abertura em cada grupo
acc_combOpen_group=Grupo
acc_combOpen_changeLevel=Grupo de Porta Aberta
acc_combOpen_combDeleteGroup=Referência de combinação de abertura existente, exclua primeiro.
acc_combOpen_ownedLevel=Grupos Pertencentes
acc_combOpen_mostPersonCount=A contagem de grupos não deve exceder cinco pessoas.
acc_combOpen_leastPersonCount=A contagem de grupos não deve ser inferior a uma pessoas.
acc_combOpen_groupNameRepeat=Nome do grupo duplicado.
acc_combOpen_groupNotUnique=Grupos de Pessoas de Portas Abertas não devem ser idênticos.
acc_combOpen_persNumErr=O número do grupo excede o valor atual da sua escolha, selecione novamente!
acc_combOpen_combOpengGroupPersonShort=Depois que as pessoas foram removidas, o número de pessoas do grupo de portas abertas não é suficiente, por favor exclua o grupo aberto primeiro.
acc_combOpen_backgroundVerifyTip=A porta está no dispositivo com verificação em segundo plano ativada não pode ser usada ao mesmo tempo com a porta aberta por Combinação de Acesso!
#[12]互锁
acc_interlock_rule=Regra de Intertravamento
acc_interlock_mode1Or2=Intertravamento entre {0} e {1}
acc_interlock_mode3=Intertravamento entre {0} e {1} e {2}
acc_interlock_mode4=Intertravamento entre {0} e {1} ou entre {2} e {3}
acc_interlock_mode5=Intertravamento entre {0} e {1} e {2} e {3}
acc_interlock_hasBeenSet=Possui configurações de programa de intertravamento
acc_interlock_group1=Grupo 1
acc_interlock_group2=Grupo 2
acc_interlock_ruleInfo=Intergrupamento intergrupal
acc_interlock_alreadyExists=A mesma regra de intertravamento já existe, por favor não a adicione novamente!
acc_interlock_groupInterlockCountErr=A regra de intertravamento intragrupo requer pelo menos duas portas
acc_interlock_ruleType=Tipo De Regra De Bloqueio
#[13]反潜
acc_apb_rules=Regra de Antirretorno
acc_apb_reader=Antirretorno entre leitores da porta {0}
acc_apb_reader2=Antirretorno entre leitores de portas: {0}, {1}
acc_apb_reader3=Antirretorno entre leitores de portas: {0}, {1}, {2}
acc_apb_reader4=Antirretorno entre leitores de portas: {0}, {1}, {2}, {3}
acc_apb_reader5=Leitor de saída Antirretorno na porta {0}
acc_apb_reader6=Leitor de entrada Antirretorno na porta {0}
acc_apb_reader7=Antirretorno entre os leitores de todas as 4 portas
acc_apb_twoDoor=Antirretorno entre {0} e {1}
acc_apb_fourDoor=Antirretorno entre {0} e {1}, Antirretorno entre {2} e {3}
acc_apb_fourDoor2=Antirretorno entre {0} ou {1} e {2} ou {3}
acc_apb_fourDoor3=Antirretorno entre {0} e {1} ou {2}
acc_apb_fourDoor4=Antirretorno entre {0} e {1} ou {2} ou {3}
acc_apb_hasBeenSet=Foi definido para o Antirretorno
acc_apb_conflictWithGapb=Este dispositivo possui configurações globais de Antirretorno e não pode ser modificado!
acc_apb_conflictWithApb=A zona do dispositivo possui configurações Antirretorno e não pode ser modificada!
acc_apb_conflictWithEntranceApb=A zona do dispositivo possui configurações Antirretorno de entrada e não pode ser modificada!
acc_apb_controlIn=Entrada Antirretorno
acc_apb_controlOut=Saída Antirretorno
acc_apb_controlInOut=Entrada/Saída Antirretorno
acc_apb_groupIn=Juntar-se ao Grupo
acc_apb_groupOut=Fora Do Grupo
acc_apb_reverseName=Anti-submarino reverso
acc_apb_door=Anti-submarino Portão
acc_apb_readerHead=Ler A Cabeça Para Combater O Submarino
acc_apb_alreadyExists=A mesma regra anti-submarino já existe, por favor, não a adicione novamente!
#[17]电子地图
acc_map_addDoor=Adicionar Porta
acc_map_addChannel=Adicionar Câmera
acc_map_noAccess=Sem permissão para acessar o módulo de E-map, entre em contato com o administrador!
acc_map_noAreaAccess=Você não tem permissão do E-map para esta área, entre em contato com o administrador!
acc_map_imgSizeError=Carregue uma imagem com tamanho inferior a {0}M!
#[18]门禁事件记录
acc_trans_entity=Registros
acc_trans_eventType=Tipo de Evento
acc_trans_firmwareEvent=Eventos de firmware
acc_trans_softwareEvent=Evento de software
acc_trans_today=Eventos de Hoje
acc_trans_lastAddr=Última posição conhecida
acc_trans_viewPhotos=Ver fotos
acc_trans_exportPhoto=Exportar fotos
acc_trans_photo=Foto do incidente do controle de acesso
acc_trans_dayNumber=Dias
acc_trans_fileIsTooLarge=O arquivo exportado é muito grande, por favor reduza o escopo para exportar
#[19]门禁验证方式
acc_verify_mode_onlyface=Face
acc_verify_mode_facefp=Face+Impressão Digital
acc_verify_mode_facepwd=Face+Senha
acc_verify_mode_facecard=Face+Cartão
acc_verify_mode_facefpcard=Face+Impressão Digital+Cartão
acc_verify_mode_facefppwd=Face+Impressão Digital+Senha
acc_verify_mode_fv=Veias do Dedo
acc_verify_mode_fvpwd=Veias do Dedo+Senha
acc_verify_mode_fvcard=Veias do Dedo+Cartão
acc_verify_mode_fvpwdcard=Veias do Dedo+Senha+Cartão
acc_verify_mode_pv=Veias da Palma
acc_verify_mode_pvcard=Veias da Palma+Cartão
acc_verify_mode_pvface=Veias da Palma+Face
acc_verify_mode_pvfp=Veias da Palma+Impressão Digital
acc_verify_mode_pvfacefp=Veias da Palma+Face+Impressão Digital
#[20]门禁事件编号
acc_eventNo_-1=Nenhum
acc_eventNo_0=Deslizar para Abrir Normal
acc_eventNo_1=Deslizar durante o Faixa horária do Modo de Passagem Livre
acc_eventNo_2=Abertura Normal do Primeiro Cartão (Cartão Magnético)
acc_eventNo_3=Abertura por Combinação de Acesso (Cartão Magnético)
acc_eventNo_4=Abertura com Senha de Emergência
acc_eventNo_5=Aberto durante o Faixa horária do Modo de Passagem
acc_eventNo_6=Evento de Vinculação Acionado
acc_eventNo_7=Cancelar Alarme
acc_eventNo_8=Abertura Remota
acc_eventNo_9=Fechamento Remoto
acc_eventNo_10=Desativar a faixa horária agendada
acc_eventNo_11=Ativar a faixa horária agendada
acc_eventNo_12=Saída Auxiliar Aberta Remotamente
acc_eventNo_13=Saída Auxiliar Fechada Remotamente
acc_eventNo_14=Abertura com Impressão Digital Normal
acc_eventNo_15=Aberto por Combinação de Acesso (Impressão Digital)
acc_eventNo_16=Colocação de Impressão Digital durante o Faixa horária do Modo de Passagem
acc_eventNo_17=Abertura com Cartão e Impressão Digital
acc_eventNo_18=Abertura Normal do Primeiro Cartão (Pressionar Impressão digital)
acc_eventNo_19=Abertura Normal do Primeiro Cartão (Cartão e Impressão Digital)
acc_eventNo_20=Intervalo de Operação Muito Curto
acc_eventNo_21=Faixa horária Inativo da Porta (Cartão Magnético)
acc_eventNo_22=Faixa horária Ilegal
acc_eventNo_23=Acesso Negado
acc_eventNo_24=Antirretorno
acc_eventNo_25=Intertravamento
acc_eventNo_26=Autenticação por Combinação de Acesso (Cartão Magnético)
acc_eventNo_27=Cartão Desativado
acc_eventNo_28=Tempo Limite de Porta Aberta Estendido
acc_eventNo_29=Cartão Expirado
acc_eventNo_30=Erro de Senha
acc_eventNo_31=Intervalo de Colocação de Impressão Digital Muito Curto
acc_eventNo_32=Autenticação por Combinação de Acesso (Colocação de Impressão Digital)
acc_eventNo_33=Impressão Digital Expirada
acc_eventNo_34=Impressão Digital Desativada
acc_eventNo_35=Faixa horária Inativo da Porta (Colocação de Impressão Digital)
acc_eventNo_36=Faixa horária Inativo da Porta (Pressionar o Botão Sair)
acc_eventNo_37=Falha ao Fechar durante o Faixa horária do Modo de Passagem Livre
acc_eventNo_38=Cartão Relatado Como Perdido
acc_eventNo_39=Desativar
acc_eventNo_40=Falha na Autenticação por Combinação de Acesso (Colocação de Impressão digital)
acc_eventNo_41=Erro no Modo de Verificação
acc_eventNo_42=Erro no Formato Wiegand
acc_eventNo_43=Tempo Limite de Verificação Antirretorno
acc_eventNo_44=Falha na Verificação em Segundo Plano
acc_eventNo_45=Tempo Limite de Verificação em Segundo Plano
acc_eventNo_47=Falha ao Enviar o Comando
acc_eventNo_48=Falha na Autenticação por Combinação de Acessos (Cartão Magnético)
acc_eventNo_49=Faixa horária Inativo da Porta (Senha)
acc_eventNo_50=Intervalo de Colocação de Senha Muito Curto
acc_eventNo_51=Autenticação por Combinação de Acesso (Senha)
acc_eventNo_52=Falha na Autenticação por Combinação de Acesso (Senha)
acc_eventNo_53=Senha Expirada
acc_eventNo_100=Alarme Tamper
acc_eventNo_101=Abertura com Senha de Coação
acc_eventNo_102=Abertura Forçada da Porta
acc_eventNo_103=Abertura com Impressão digital de Coação
acc_eventNo_200=Porta Aberta Corretamente
acc_eventNo_201=Porta Fechada Corretamente
acc_eventNo_202=Abertura com Botão Sair
acc_eventNo_203=Abertura por Combinação de Acesso (Cartão e Impressão Digital)
acc_eventNo_204=Término do Faixa horária do Modo de Passagem
acc_eventNo_205=Abertura Normal Remota
acc_eventNo_206=Dispositivo Iniciado
acc_eventNo_207=Abertura com Senha
acc_eventNo_208=Abertura de Portas pelo Superusuário
acc_eventNo_209=Botão Sair Acionado (Sem Destrancar)
acc_eventNo_210=Iniciar porta corta-fogo
acc_eventNo_211=Fechamento de Portas pelo Superusuário
acc_eventNo_212=Ativar a Função de Controle do Elevador
acc_eventNo_213=Desativar a Função de Controle do Elevador
acc_eventNo_214=Abertura por Combinação de Acesso (Senha)
acc_eventNo_215=Abertura Normal do Primeiro Cartão (Senha)
acc_eventNo_216=Senha durante o Faixa horária do Modo de Passagem
acc_eventNo_220=Entrada Auxiliar Aberta
acc_eventNo_221=Entrada Auxiliar Fechada
acc_eventNo_222=Verificação em Segundo Plano Bem-Sucedida
acc_eventNo_223=Verificação em Segundo Plano
acc_eventNo_225=Entrada Auxiliar Normal
acc_eventNo_226=Acionar Entrada Auxiliar
acc_newEventNo_0=Verificação de abertura normal
acc_newEventNo_1=Verificação durante o Faixa horária do Modo de Passagem
acc_newEventNo_2=Abertura pela Primeira Pessoa
acc_newEventNo_3=Abertura por Combinação de Acesso
acc_newEventNo_20=Intervalo de operação muito curto
acc_newEventNo_21=Verificação de abertura durante faixa horária inativa
acc_newEventNo_26=Espera na autenticação por Combinação de Acesso
acc_newEventNo_27=Usuário não registrado
acc_newEventNo_29=Usuário com permissão expirada
acc_newEventNo_30=Erro de senha
acc_newEventNo_41=Erro no modo de verificação
acc_newEventNo_43=Bloqueio de equipe
acc_newEventNo_44=Falha na verificação em segundo plano
acc_newEventNo_45=Tempo limite da verificação em segundo plano
acc_newEventNo_48=A verificação por Combinação de Acesso falhou
acc_newEventNo_54=A tensão da bateria está muito baixa
acc_newEventNo_55=Substitua a bateria imediatamente
acc_newEventNo_56=Operação Ilegal
acc_newEventNo_57=Energia de Reserva
acc_newEventNo_58=Abertura Normal com Alarme
acc_newEventNo_59=Gerenciamento Ilegal
acc_newEventNo_60=Porta Trancada por Dentro
acc_newEventNo_61=Replicado
acc_newEventNo_62=Proibir Usuários
acc_newEventNo_63=Porta Trancada
acc_newEventNo_64=Faixa horária do Botão Sair Inativo
acc_newEventNo_65=Faixa horária da Entrada Auxiliar Inativo
acc_newEventNo_66=Falha na Atualização do Leitor
acc_newEventNo_67=Emparelhamento remoto bem sucedido (dispositivo não autorizado)
acc_newEventNo_68=Temperatura corporal Elevada - acesso negado
acc_newEventNo_69=Sem máscara - acesso negado
acc_newEventNo_70=Exceção de comunicação com o servidor de comparação de face.
acc_newEventNo_71=O servidor de comparação facial respondeu anormalmente.
acc_newEventNo_73=Código QR inválido
acc_newEventNo_74=Código QR Expirado
acc_newEventNo_101=Alarme de Abertura com Coação
acc_newEventNo_104=Alarme de Cartão Magnético Inválido
acc_newEventNo_105=Não é possível se conectar ao servidor
acc_newEventNo_106=Alimentação principal desligada
acc_newEventNo_107=Bateria desligada
acc_newEventNo_108=Não é possível conectar ao dispositivo master
acc_newEventNo_109=Alarme de violação do leitor
acc_newEventNo_110=Leitor offline
acc_newEventNo_112=Placa de expansão offline
acc_newEventNo_114=Entrada de alarme de incêndio desconectada (detecção de linha)
acc_newEventNo_115=Curto-circuito de entrada de alarme de incêndio (detecção de linha)
acc_newEventNo_116=Entrada auxiliar desconectada (detecção de linha)
acc_newEventNo_117=Short-circuit de entrada auxiliar (detecção de linha)
acc_newEventNo_118=Interruptor de saída desconectado (detecção de linha)
acc_newEventNo_119=Curto-circuito do interruptor de saída (detecção do circuito)
acc_newEventNo_120=Desconexão magnética da porta (detecção de linha)
acc_newEventNo_121=Curto-circuito magnético da porta (detecção de linha)
acc_newEventNo_159=Controle remoto para abertura da porta
acc_newEventNo_214=Conectado ao servidor
acc_newEventNo_217=Conectado ao dispositivo master com sucesso
acc_newEventNo_218=Verificação do Cartão ID
acc_newEventNo_222=Verificação em Segundo Plano Bem-Sucedida
acc_newEventNo_223=Verificação em Segundo Plano
acc_newEventNo_224=Tocar a campainha
acc_newEventNo_227=Abertura dupla da porta
acc_newEventNo_228=Fechamento duplo da porta
acc_newEventNo_229=Abertura Temporizada Normal da Saída Auxiliar
acc_newEventNo_230=Fechamento Temporizado da Saída Auxiliar
acc_newEventNo_232=Verifique o Sucesso
acc_newEventNo_233=Ativar Bloqueio
acc_newEventNo_234=Desativar Bloqueio
acc_newEventNo_235=Atualização do Leitor Bem-Sucedida
acc_newEventNo_236=Alarme de violação do leitor limpo
acc_newEventNo_237=Leitor online
acc_newEventNo_239=Chamada de dispositivo
acc_newEventNo_240=Chamada encerrada
acc_newEventNo_243=Entrada de alarme de incêndio desligada
acc_newEventNo_244=Curto-circuito de entrada de alarme de incêndio
acc_newEventNo_247=Quadro de expansão online
acc_newEventNo_4008=Recuperação de rede
acc_newEventNo_4014=Sinal de entrada de fogo desconectado, porta da extremidade normalmente aberta
acc_newEventNo_4015=A porta está online
acc_newEventNo_4018=Comparação de Backend Abrir
acc_newEventNo_5023=Estado de fogo restrito
acc_newEventNo_5024=Tempo limite de verificação multipessoal
acc_newEventNo_5029=Falha na comparação do backend
acc_newEventNo_6005=A capacidade de gravação está prestes a atingir seu limite
acc_newEventNo_6006=Curto-circuito de linha (RS485)
acc_newEventNo_6007=Curto-circuito no circuito (Wigan)
acc_newEventNo_6011=A porta está desligada
acc_newEventNo_6012=Alarme de desmontagem da porta
acc_newEventNo_6013=Sinal de entrada de fogo acionado, porta aberta normalmente
acc_newEventNo_6015=Redefinir a fonte de alimentação do dispositivo de expansão
acc_newEventNo_6016=Restaurar as configurações de fábrica da máquina
acc_newEventNo_6070=Comparação de backend (lista proibida)
acc_eventNo_undefined=Número do Evento Indefinido
acc_advanceEvent_500=Antirretorno Global (lógico)
acc_advanceEvent_501=Validade do Acesso (utilizar a Data)
acc_advanceEvent_502=Número da Pessoa de Controle
acc_advanceEvent_503=Intertravamento Global
acc_advanceEvent_504=Controle de Rota
acc_advanceEvent_505=Antirretorno Global (Temporizado)
acc_advanceEvent_506=Antirretorno Global (Temporizado lógico)
acc_advanceEvent_507=Validade do Acesso(após o primeiro uso em dias válidos)
acc_advanceEvent_508=Validade do Acesso(quantidade de usos)
acc_advanceEvent_509=Falha na verificação em segundo plano (pessoal não registrado)
acc_advanceEvent_510=Falha na verificação em segundo plano (exceção de dados)
acc_alarmEvent_701=Violação DMR (definindo regras: {0})
#[21]实时监控
acc_rtMonitor_openDoor=Abrir
acc_rtMonitor_closeDoor=Fechar
acc_rtMonitor_remoteNormalOpen=Abertura Remota Normal
acc_rtMonitor_realTimeEvent=Eventos em Tempo Real
acc_rtMonitor_photoMonitor=Monitoramento de Fotos
acc_rtMonitor_alarmMonitor=Monitoramento de Alarmes
acc_rtMonitor_doorState=Status da Porta
acc_rtMonitor_auxOutName=Nome da Saída Auxiliar
acc_rtMonitor_nonsupport=Não Suportado
acc_rtMonitor_lock=Bloqueado
acc_rtMonitor_unLock=Aberto
acc_rtMonitor_disable=Desativado
acc_rtMonitor_noSensor=Sem Sensor de Porta
acc_rtMonitor_alarm=Alarme
acc_rtMonitor_openForce=Abertura Forçada
acc_rtMonitor_tamper=Tamper
acc_rtMonitor_duressPwdOpen=Abertura com Senha de Coação
acc_rtMonitor_duressFingerOpen=Abertura com Impressão digital de Coação
acc_rtMonitor_duressOpen=Abertura com Coação
acc_rtMonitor_openTimeout=Tempo Limite de Abertura
acc_rtMonitor_unknown=Desconhecido
acc_rtMonitor_noLegalDoor=Não existem portas que preencham as condições.
acc_rtMonitor_noLegalAuxOut=Nenhuma saída auxiliar preenche a condição!
acc_rtMonitor_curDevNotSupportOp=O status atual do dispositivo não suporta esta operação!
acc_rtMonitor_curNormalOpen=Atualmente aberta normalmente
acc_rtMonitor_whetherDisableTimeZone=O status atual da porta é sempre aberta.
acc_rtMonitor_curSystemNoDoors=O sistema atual não adicionou nenhuma porta ou não encontrou nenhuma porta que atenda aos requisitos.
acc_rtMonitor_cancelAlarm=Cancelar Alarme
acc_rtMonitor_openAllDoor=Abrir todas as portas atuais
acc_rtMonitor_closeAllDoor=Fechar todas as portas atuais
acc_rtMonitor_confirmCancelAlarm=Tem certeza que deseja cancelar este alarme?
acc_rtMonitor_calcelAllDoor=Cancelar todos os alarmes
acc_rtMonitor_initDoorStateTip=Obtendo todas as portas autorizadas aos usuários no sistema...
acc_rtMonitor_alarmEvent=Evento de Alarme
acc_rtMonitor_ackAlarm=Aceitar
acc_rtMonitor_ackAllAlarm=Aceitar Todos
acc_rtMonitor_ackAlarmTime=Tempo de Aceitação
acc_rtMonitor_sureToAckThese=Tem certeza que quer confirmar este {0} alarme? Após a sua confirmação, todos os alarmes serão cancelados.
acc_rtMonitor_sureToAckAllAlarm=Tem certeza de que deseja cancelar todos os alarmes? Após a sua confirmação, todos os alarmes serão cancelados.
acc_rtMonitor_noSelectAlarmEvent=Por favor, confirme o evento de alarme!
acc_rtMonitor_noAlarmEvent=Não há eventos de alarme no sistema atual!
acc_rtMonitor_forcefully=Cancelar Alarme (Abertura Forçada da Porta)
acc_rtMonitor_addToRegPerson=Adicionado a pessoa registrada
acc_rtMonitor_cardExist=Este cartão foi atribuído por {0} e não pode ser emitido novamente.
acc_rtMonitor_opResultPrompt={0} solicitações enviadas com sucesso, {1} falharam.
acc_rtMonitor_doorOpFailedPrompt=Falha ao enviar solicitações para as seguintes portas, tente novamente!
acc_rtMonitor_remoteOpen=Abertura Remota
acc_rtMonitor_remoteClose=Fechamento Remoto
acc_rtMonitor_alarmSoundClose=Áudio está fechado
acc_rtMonitor_alarmSoundOpen=Áudio está aberto
acc_rtMonitor_playAudio=Sons de Lembrete de Evento
acc_rtMonitor_isOpenShowPhoto=Ativar Função de Exibição de Fotos
acc_rtMonitor_isOpenPlayAudio=Ativar Função de Alerta por Áudio
acc_rtm_open=Botão de Abertura Remota
acc_rtm_close=Botão de Fechamento Remoto
acc_rtm_eleModule=Elevador
acc_cancelAlarm_fp=Cancelar Alarme (Abertura com Impressão Digital de Coação)
acc_cancelAlarm_pwd=Cancelar Alarme (Abertura com Senha de Coação)
acc_cancelAlarm_timeOut=Cancelar Alarme (Tempo Limite Estendido de Porta Aberta)
#定时同步设备时间
acc_timing_syncDevTime=Dispositivo de Tempo de Sincronização
acc_timing_executionTime=O tempo de execução
acc_timing_theLifecycle=O ciclo de vida
acc_timing_errorPrompt=A entrada está errada.
acc_timing_checkedSyncTime=Por favor, selecione um horário de sincronização.
#[25]门禁报表
acc_trans_hasAccLevel=Possuir Nível para Acesso
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Por favor, adicione a zona
acc_zone_code=Código da Zona
acc_zone_parentZone=Zona Principal
acc_zone_parentZoneCode=Código da Zona Principal
acc_zone_parentZoneName=Nome da Zona Principal
acc_zone_outside=Externo
#[G2]读头定义
acc_readerDefine_readerName=Nome do Leitor
acc_readerDefine_fromZone=Vem de
acc_readerDefine_toZone=Vai para
acc_readerDefine_delInfo1=A zona desta definição de leitor é referenciada por uma função de controle de acesso avançado e não pode ser excluída!
acc_readerDefine_selReader=Selecionar Leitor
acc_readerDefine_selectReader=Por favor, adicione o leitor!
acc_readerDefine_tip=Depois do pessoal chegar na parte externa da zona, o registro de pessoal será excluído.
#[G3]全局反潜
acc_gapb_zone=Zona
acc_gapb_whenToResetGapb=Redefinição do Tempo Antirretorno
acc_gapb_apbType=Tipo Antirretorno
acc_gapb_logicalAPB=Antirretorno Lógico
acc_gapb_timedAPB=Antirretorno Temporizado
acc_gapb_logicalTimedAPB=Antirretorno Lógico Temporizado
acc_gapb_lockoutDuration=Tempo do Bloqueio
acc_gapb_devOfflineRule=Se o dispositivo estiver offline
acc_gapb_standardLevel=Nível de Acesso Padrão
acc_gapb_accessDenied=Acesso Negado
acc_gapb_doorControlZone=As portas a seguir controlam o acesso dentro e fora da zona
acc_gapb_resetStatus=Redefinir Status do Antirretorno
acc_gapb_obeyAPB=Obedecer as regras do Antirretorno
acc_gapb_isResetGAPB=Redefinir o Antirretorno
acc_gapb_resetGAPBSuccess=Sucesso em redefinir o status do Antirretorno
acc_gapb_resetGAPBFaile=Falha em redefinir o status do Antirretorno
acc_gapb_chooseArea=Por favor, selecione novamente a zona.
acc_gapb_notDelInfo1=A zona de acesso é referenciada como a área de acesso superior e não pode ser excluída.
acc_gapb_notDelInfo2=A zona é referenciada pela Definição do Leitor e não pode ser excluída.
acc_gapb_notDelInfo3=A zona é referenciada por funções avançadas de controle de acesso e não pode ser excluída.
acc_gapb_notDelInfo4=A área de acesso é referenciada pelo LED e não pode ser excluída!
acc_gapb_zoneNumRepeat=Os números do domínio de controle estão duplicados
acc_gapb_zoneNameRepeat=Os nomes do domínio de controle estão duplicados
acc_gapb_personResetGapbPre=Tem certeza que quer redefinir para o padrão?
acc_gapb_personResetGapbSuffix=regras de Antirretorno de pessoa?
acc_gapb_apbPrompt=Uma única porta não pode ser usada para controlar dois limites de perímetro relativamente independentes.
acc_gapb_occurApb=Ocorrência do Antirretorno
acc_gapb_noOpenDoor=Abertura de porta Negada
acc_gapb_openDoor=Abrir a porta
acc_gapb_zoneNumLength=Tamanho maior que 20 caracteres
acc_gapb_zoneNameLength=Tamanho maior que 30 caracteres
acc_gapb_zoneRemarkLength=Tamanho maior que 50 caracteres
acc_gapb_isAutoServerMode=Foi detectado um dispositivo sem a função de verificação em segundo plano, que pode afetar o funcionamento Abrir agora?
acc_gapb_applyTo=Aplicar a
acc_gapb_allPerson=Todo o Pessoal
acc_gapb_justSelected=Apenas Pessoal Selecionado
acc_gapb_excludeSelected=Excluir Pessoal Selecionado
#[G4]who is inside
acc_zoneInside_lastAccessTime=Hora do Último Acesso
acc_zoneInside_lastAccessReader=Último Acesso do Leitor
acc_zoneInside_noPersonInZone=Nenhuma pessoa na zona
acc_zoneInside_noRulesInZone=Não há regras definidas.
acc_zoneInside_totalPeople=Total de Pessoas
acc_zonePerson_selectPerson=Por favor, selecione uma pessoa ou departamento!
#[G5]路径
acc_route_name=Nome da rota
acc_route_setting=Configuração de Rota
acc_route_addReader=Adicionar Leitor
acc_route_delReader=Excluir Leitor
acc_route_defineReaderLine=Definir linha do leitor
acc_route_up=Acima
acc_route_down=Abaixo
acc_route_selReader=Selecione o leitor após a operação.
acc_route_onlyOneOper=Só é possível escolher um leitor para operar.
acc_route_readerOrder=Sequência definida pelo leitor
acc_route_atLeastSelectOne=Por favor, selecione pelo menos uma cabeça de leitura!
acc_route_routeIsExist=Esta rota já existe!
#[G6]DMR
acc_dmr_residenceTime=Tempo de permanência
acc_dmr_setting=Configuração DMR
#[G7]Occupancy
acc_occupancy_max=Capacidade Máxima
acc_occupancy_min=Capacidade Mínima
acc_occupancy_unlimit=Ilimitado
acc_occupancy_note=A capacidade mínima de acomodação não é superior a capacidade máxima de acomodação.
acc_occupancy_containNote=Defina pelo menos uma capacidade máxima/mínima!
acc_occupancy_maxMinValid=Defina uma capacidade maior que 0.
acc_occupancy_conflict=As regras de controle de ocupação foram definidas nesta zona.
acc_occupancy_maxMinTip=Um valor de capacidade vazio significa nenhuma limitação.
#card availability
acc_personLimit_zonePropertyName=Nome da propriedade da zona
acc_personLimit_useType=Uso
acc_personLimit_userDate=Data Válida
acc_personLimit_useDays=Após o Primeiro Uso
acc_personLimit_useTimes=Número de Vezes
acc_personLimit_setZoneProperty=Definir Propriedades da Zona
acc_personLimit_zoneProperty=Propriedades da Zona
acc_personLimit_availabilityName=Nome da Validade
acc_personLimit_days=Dias
acc_personLimit_Times=Quantidade
acc_personLimit_noDel=As propriedades da zona de controle de acesso selecionadas estão referenciadas e não podem ser excluídas!
acc_personLimit_cannotEdit=O atributo da área de acesso está referenciado e não pode ser modificado!
acc_personLimit_detail=Detalhes
acc_personLimit_userDateTo=Válido até
acc_personLimit_addPersonRepeatTip=A pessoa do departamento selecionado foi adicionada ao atributo da área de controle de acesso, selecione o departamento novamente!
acc_personLimit_leftTimes={0} Tempos restantes
acc_personLimit_expired=expirado
acc_personLimit_unused=Não usado
#全局互锁
acc_globalInterlock_addGroup=Adicionar Grupo
acc_globalInterlock_delGroup=Excluir Grupo
acc_globalInterlock_refuseAddGroupMessage=O mesmo intertravamento de porta não pode ser duplicado dentro do grupo adicionado
acc_globalInterlock_refuseAddlockMessage=A porta adicionada aparece em outros grupos de dispositivos de intertravamento
acc_globalInterlock_refuseDeleteGroupMessage=Remova os dados relacionados ao intertravamento
acc_globalInterlock_isGroupInterlock=Grupo de Intertravamento
acc_globalInterlock_isAddTheDoorImmediately=Adicione a porta imediatamente
acc_globalInterlock_isAddTheGroupImmediately=Adicione o grupo imediatamente
#门禁参数设置
acc_param_autoEventDev=Download automático do log de eventos do número de dispositivos simultâneos
acc_param_autoEventTime=Download automático do log de eventos de intervalos simultâneos
acc_param_noRepeat=Não é permitida nenhuma repetição de endereços de e-mail, preencha novamente.
acc_param_most18=Adicione até 18 endereços de e-mail
acc_param_deleteAlert=Não é possível excluir todas as caixa de entrada dos endereços de e-mail.
acc_param_invalidOrRepeat=Erro no formato do endereço de e-mail ou repetição do endereço de e-mail.
#全局联动
acc_globalLinkage_noSupport=A porta atualmente selecionada não suporta intertravamento e desativa as funções de bloqueio.
acc_globalLinkage_trigger=Acionar Vinculação Global
acc_globalLinkage_noAddPerson=A regra de vinculação não contém um acionamento relacionado ao pessoal, por padrão, é aplicável a todo o pessoal!
acc_globalLinkage_selectAtLeastOne=Selecione pelo menos um ponto de saída de vinculação e vinculação de vídeo!
acc_globalLinkage_selectTrigger=Por favor, adicione as condições de vinculação de acionamento!
acc_globalLinkage_selectInput=Por favor, adicione o ponto de entrada!
acc_globalLinkage_selectOutput=Por favor, adicione o ponto de saída!
acc_globalLinkage_audioRemind=Instruções de Vinculação de Voz
acc_globalLinkage_audio=Vinculação de Voz
acc_globalLinkage_isApplyToAll=Aplicar a todo o pessoal
acc_globalLinkage_scope=Intervalo de pessoal
acc_globalLinkage_everyPerson=Qualquer
acc_globalLinkage_selectedPerson=Selecionado
acc_globalLinkage_noSupportPerson=Não Suportado
acc_globalLinkage_reselectInput=O tipo de condições de acionamento foi alterado, por favor, selecione novamente o ponto de entrada!
acc_globalLinkage_addPushDevice=Por favor, adicione suporte para esta função do dispositivo
#其他
acc_InputMethod_tips=Por favor, altere para o modo de entrada em inglês!
acc_device_systemCheckTip=O dispositivo de acesso não existe!
acc_notReturnMsg=Nenhuma informação retornada
acc_validity_period=A licença passou do período de validade; a função não pode operar.
acc_device_pushMaxCount=O sistema já existe em {0} dispositivo (s), limite de licença alcançado, não é possível adicionar o dispositivo!
acc_device_videoHardwareLinkage=Definir a Vinculação de Hardware de Vídeo
acc_device_videoCameraIP=IP da Câmera de Vídeo
acc_device_videoCameraPort=Porta da Câmera de Vídeo
acc_location_unable=O ponto do incidente não foi adicionado ao E-map, não é possível localizar o local específico!
acc_device_wgDevMaxCount=O dispositivo atingiu o limite da licença no sistema e não é possível modificar as configurações!
#自定义报警事件
acc_deviceEvent_selectSound=Por favor, selecione os arquivos de áudio.
acc_deviceEvent_batchSetSoundErr=Configuração do pacote de sons de alarme está anormal.
acc_deviceEvent_batchSet=Definir Áudio
acc_deviceEvent_sound=Som do Evento
acc_deviceEvent_exist=Já Existe
acc_deviceEvent_upload=Upload
#查询门最近发生事件
acc_doorEventLatestHappen=Consultar os últimos eventos da porta
#门禁人员信息
acc_pers_delayPassage=Passagem Estendida
#设备容量提示
acc_dev_usageConfirm=Capacidade atual superior a 90% do equipamento
acc_dev_immediateCheck=Verifique imediatamente
acc_dev_inSoftware=No Software
acc_dev_inFirmware=No FirmWare
acc_dev_get=Obter
acc_dev_getAll=Obter Tudo
acc_dev_loadError=Falha no Carregamento
#Reader
acc_reader_inout=Entrada/Saída
acc_reader_lightRule=Regras de Luzes
acc_reader_defLightRule=Regra Padrão
acc_reader_encrypt=Criptografar
acc_reader_allReaderOfCurDev=Todos os Leitores no Dispositivo Atual
acc_reader_tip1=A criptografia foi copiada para todos os leitores no dispositivo atual!
acc_reader_tip2=A opção de modo ID está disponível apenas para cabeças de leitura que suportam esse recurso!
acc_reader_tip3=O tipo de protocolo RS485 é copiado para todos os leitores no dispositivo atual. As configurações entrarão em vigor após a reinicialização do dispositivo!
acc_reader_tip4=A opção de ocultar algumas informações pessoais é copiada para todos os leitores do mesmo dispositivo por padrão!
acc_reader_commType=Tipo de Comunicação
acc_reader_commAddress=Endereço de Comunicação
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=Desativado
acc_readerComAddress_repeat=Endereço de comunicação duplicado
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=Endereço do RS485
acc_readerCommType_wgAddress=Endereço do Wiegand
acc_reader_macError=Por favor, digite o endereço Mac no formato correto!
acc_reader_machineType=Tipo de Leitor
acc_reader_readMode=Modo
acc_reader_readMode_normal=Modo Normal
acc_reader_readMode_idCard=Modo de Cartão ID
acc_reader_note=Dicas: Somente dispositivos de controle de acesso na mesma área ({0}) da câmera podem ser selecionados.
acc_reader_rs485Type=Tipo de protocolo RS485
acc_reader_userLock=Bloqueio de acesso de pessoal
acc_reader_userInfoReveal=Ocultar Informações de Parte Pessoal
#operat
acc_operation_pwd=Senha de Operação
acc_operation_pwd_error=Erro de Senha
acc_new_input_not_same=A nova chave inserida não é consistente
acc_op_set_keyword=Configurações da chave de licença
acc_op_old_key=Chave Antiga
acc_op_new_key=Chave Nova
acc_op_cofirm_key=Confirmar Chave
acc_op_old_key_error=Erro de chave antiga
#验证方式规则
acc_verifyRule_name=Nome da Regra
acc_verifyRule_door=Verificação da Porta
acc_verifyRule_person=Verificação de Pessoal
acc_verifyRule_copy=Copie a configuração da segunda-feira para os Outros Dias da Semana:
acc_verifyRule_tip1=Selecione pelo menos um modo de verificação!
acc_verifyRule_tip2=Se a regra contiver o modo de verificação de uma pessoa, você só poderá adicionar uma porta com um leitor Wiegand!
acc_verifyRule_tip3=O leitor RS485 pode apenas acompanhar o modo de verificação da porta, não suporta o modo de verificação de pessoal.
acc_verifyRule_oldVerifyMode=Antigo modo de verificação
acc_verifyRule_newVerifyMode=Novo modo de verificação
acc_verifyRule_newVerifyModeSelectTitle=Selecione um novo método de verificação
acc_verifyRule_newVerifyModeNoSupportTip=Não há nenhum dispositivo que suporte o novo método de verificação!
#Wiegand Test
acc_wiegand_beforeCard=O tamanho do novo cartão ({0} bits) não é igual ao cartão anterior!
acc_wiegand_curentCount=Comprimento Atual do Número do Cartão: {0} bits
acc_wiegand_card=Cartão
acc_wiegand_readCard=Ler Cartão
acc_wiegand_clearCardInfo=Limpar Informações do Cartão
acc_wiegand_originalCard=Número do Cartão Original
acc_wiegand_recommendFmt=Formato de Cartão Recomendado
acc_wiegand_parityFmt=Formato de Paridade Par-Ímpar
acc_wiegand_withSizeCode=Calcular automaticamente o código do centro quando o código do centro estiver em em branco
acc_wiegand_tip1=Esses cartões podem não pertencer ao mesmo lote de cartões.
acc_wiegand_tip2=Código do centro :{0}, Número do cartão: {1}, falha ao coincidir ao número do cartão original. Verifique novamente o código do centro e o número do cartão inseridos!
acc_wiegand_tip3=O número do cartão inserido ({0}) não pode coincidir ao número do cartão original. Por favor verifique novamente!
acc_wiegand_tip4=O código do site inserido ({0}) não pode coincidir ao número do cartão original. Por favor verifique novamente!
acc_wiegand_tip5=Se você quer usar esse recurso, mantenha todas as colunas de código do centro vazias!
acc_wiegand_warnInfo1=Para continuar a ler um novo cartão, altere manualmente para o próximo cartão.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Quadro de Entrada/Saída de Pessoal
acc_LCDRTMonitor_current=Informações Pessoais Atuais
acc_LCDRTMonitor_previous=Informações Pessoais Anteriores
#api
acc_api_levelIdNotNull=O ID do grupo de permissões não pode estar vazio
acc_api_levelNotExist=O grupo de permissões não existe
acc_api_levelExist=Existe um grupo de permissões
acc_api_areaNameNotNull=A região não pode estar vazia
acc_api_levelNotHasPerson=Não há nenhuma pessoa no grupo de permissão
acc_api_doorIdNotNull=O ID da porta não pode estar vazio
acc_api_doorNameNotNull=O nome da porta não pode estar vazio
acc_api_doorIntervalSize=A duração da abertura da porta deve estar entre 1 e 254
acc_api_doorNotExist=A porta não existe
acc_api_devOffline=O dispositivo está off-line ou desativado
acc_api_devSnNotNull=O SN do dispositivo não pode estar vazio
acc_api_timesTampNotNull=A marca de hora não pode estar vazia
acc_api_openingTimeCannotBeNull=O tempo de abertura da porta não pode estar vazio
acc_api_parameterValueCannotBeNull=O valor do parâmetro não pode estar vazio
acc_api_deviceNumberDoesNotExist=O número de série do dispositivo não existe
acc_api_readerIdCannotBeNull=O ID do leitor não pode estar vazio
acc_api_theReaderDoesNotExist=A cabeça de leitura não existe
acc_operate_door_notInValidDate=Atualmente, não dentro do tempo efetivo de abertura remota, entre em contato com o administrador, se necessário!
acc_api_doorOffline=A porta está off-line ou desativada
#门禁信息自动导出
acc_autoExport_title=Exportação Automática de Transações
acc_autoExport_frequencyTitle=Exportação Automática de Frequencias
acc_autoExport_frequencyDay=Por dia
acc_autoExport_frequencyMonth=Por mês
acc_autoExport_firstDayMonth=Primeiro dia do mês
acc_autoExport_specificDate=Data específica
acc_autoExport_exportModeTitle=Modo de exportação
acc_autoExport_dailyMode=Transações diárias
acc_autoExport_monthlyMode=Transações mensais (todas as transações entre a data do último mês e este mês)
acc_autoExport_allMode=Todos os dados (exporte até 30000 partes de dados)
acc_autoExport_recipientMail=Caixa de Correio do Destinatário
#First In And Last Out
acc_inOut_inReaderName=Primeiro nome no leitor
acc_inOut_firstInTime=Primeira vez
acc_inOut_outReaderName=Último nome do leitor externo
acc_inOut_lastOutTime=Último horário de saída
#防疫参数
acc_dev_setHep=Definir parâmetros de detecção de máscara e temperatura
acc_dev_enableIRTempDetection=Habilitar a triagem de temperatura com infravermelho
acc_dev_enableNormalIRTempPass=Negar acesso quando a temperatura estiver acima da faixa
acc_dev_enableMaskDetection=Ativar detecção de máscara
acc_dev_enableWearMaskPass=Negar acesso sem máscara
acc_dev_tempHighThreshold=Limiar de alarme de alta temperatura
acc_dev_tempUnit=Unidade de temperatura
acc_dev_tempCorrection=Correção de desvio de temperatura
acc_dev_enableUnregisterPass=Permitir acesso a pessoas não registradas
acc_dev_enableTriggerAlarm=Disparar alarme externo
#联动邮件
acc_mail_temperature=Temperatura corporal
acc_mail_mask=Se deve usar a máscara
acc_mail_unmeasured=Não medido
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Eventos globais do Digifort
acc_digifort_chooseDigifortEvents=Escolha os eventos globais do Digifort
acc_digifort_eventExpiredTip=Se o evento global for excluído do servidor Digifort, ele estará em vermelho.
acc_digifort_checkConnection=Por favor, verifique se as informações de conexão do servidor Digifort estão corretas.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Adicionar contatos
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=Definir Parâmetros Estendidos
acc_extendParam_faceUI=Exibição da interface
acc_extendParam_faceParam=Parâmetros de face
acc_extendParam_accParam=Parâmetros de controle de acesso
acc_extendParam_intercomParam=Parâmetros visuais do intercomunicador
acc_extendParam_volume=Volume
acc_extendParam_identInterval=Intervalo de identificação (ms)
acc_extendParam_historyVerifyResult=Exibir resultados da verificação histórica
acc_extendParam_macAddress=Exibir endereço MAC
acc_extendParam_showIp=Mostrar endereço IP
acc_extendParam_24HourFormat=Mostrar formato de 24 horas
acc_extendParam_dateFormat=Formato da data
acc_extendParam_1NThreshold=1: N limite
acc_extendParam_facePitchAngle=Ângulo de inclinação da face
acc_extendParam_faceRotationAngle=Ângulo de rotação da face
acc_extendParam_imageQuality=Qualidade da imagem
acc_extendParam_miniFacePixel=Pixel mínimo de face
acc_extendParam_biopsy=Ativar biópsia
acc_extendParam_showThermalImage=Mostrar imagem térmica
acc_extendParam_attributeAnalysis=Ativar análise de atributo
acc_extendParam_temperatureAttribute=Atributo de detecção de temperatura
acc_extendParam_maskAttribute=Atributo de detecção de máscara
acc_extendParam_minTemperature=Limite inferior do limite de detecção de temperatura corporal
acc_extendParam_maxTemperature=Limite superior do limite de detecção de temperatura corporal
acc_extendParam_gateMode=Modo Gate
acc_extendParam_qrcodeEnable=Ativar código QR dinâmico
#可视对讲
acc_dev_intercomServer=Endereço do serviço de intercomunicação visual
acc_dev_intercomPort=Servidor de intercomunicação visual
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Nível de Sincronização
# 夏令时名称
acc_dsTimeUtc_none=Não definido
acc_dsTimeUtc_AreaNone=Não há Horário de verão nesta área.
acc_dsTimeUtc1000_0=Canberra, Melbourne, Sydney.
acc_dsTimeUtc1000_1=Hobart.
acc_dsTimeUtc_0330_0=Newfoundland
acc_dsTimeUtc_1000_0=Aleutian Islands
acc_dsTimeUtc_0200_0=Médio Atlântico - utilizado
acc_dsTimeUtc0930_0=Adelaide.
acc_dsTimeUtc_0100_0=Açores
acc_dsTimeUtc_0400_0=Hora do Atlântico (Canadá)
acc_dsTimeUtc_0400_1=Santiago.
acc_dsTimeUtc_0400_2=Assunção
acc_dsTimeUtc_0300_0=Groelândia
acc_dsTimeUtc_0300_1=Ilhas Saint Pierre e Miquelon
acc_dsTimeUtc0200_0=Chisinau.
acc_dsTimeUtc0200_1=Helsínquia, Kiev, Riga, Sofia, Tallinn, Vilnius
acc_dsTimeUtc0200_2=Atenas, Bucareste
acc_dsTimeUtc0200_3=Jerusalém.
acc_dsTimeUtc0200_4=Amã
acc_dsTimeUtc0200_5=Beirute.
acc_dsTimeUtc0200_6=Damasco
acc_dsTimeUtc0200_7=Gaza, Hebron
acc_dsTimeUtc0200_8=Juba.
acc_dsTimeUtc_0600_0=Central time (EUA e Canadá)
acc_dsTimeUtc_0600_1=Guadalajara, Cidade do México, Monterrey
acc_dsTimeUtc_0600_2=Ilha da Páscoa
acc_dsTimeUtc1300_0=Ilhas Samoa
acc_dsTimeUtc_0500_0=Havana.
acc_dsTimeUtc_0500_1=Eastern time (Estados Unidos e Canadá)
acc_dsTimeUtc_0500_2=Haiti.
acc_dsTimeUtc_0500_3=Indiana (Leste)
acc_dsTimeUtc_0500_4=Ilhas Turcas e Caicos
acc_dsTimeUtc_0800_0=Pacific time (EUA e Canadá)
acc_dsTimeUtc_0800_1=Baja Califórnia.
acc_dsTimeUtc0330_0=teherano ou tehrano
acc_dsTimeUtc0000_0=Dublin, Edimburgo, Lisboa, Londres
acc_dsTimeUtc1200_0=Fiji.
acc_dsTimeUtc1200_1=Petropavlovsk Kamchatka antigo
acc_dsTimeUtc1200_2=Wellington, Auckland.
acc_dsTimeUtc1100_0=Ilha Norfolk
acc_dsTimeUtc_0700_0=Chihuahua, La Paz, Mazatlan
acc_dsTimeUtc_0700_1=Mountain time (EUA e Canadá)
acc_dsTimeUtc0100_0=Belgrado, Bratislava, Budapeste, Ljubljana, Praga
acc_dsTimeUtc0100_1=Sarajevo, Skopje, Varsóvia, Zagreb
acc_dsTimeUtc0100_2=Casablanca.
acc_dsTimeUtc0100_3=Bruxelas, Copenhage, Madrid, Paris
acc_dsTimeUtc0100_4=Amsterdã, Berlim, Berna, Roma, Estocolmo, Viena
acc_dsTimeUtc_0900_0=Alasca
#安全点(muster point)
acc_leftMenu_accMusterPoint=Ponto de Encontro
acc_musterPoint_activate=Ativar
acc_musterPoint_addDept=Adicionar Departamento
acc_musterPoint_delDept=Excluir Departamento
acc_musterPoint_report=Relatório do Ponto de Encontro
acc_musterPointReport_sign=Entrada Manual
acc_musterPointReport_generate=Gerar Relatórios
acc_musterPoint_addSignPoint=Adicionar ponto de sinalização
acc_musterPoint_delSignPoint=Excluir ponto de sinal
acc_musterPoint_selectSignPoint=Por favor, adicione um ponto de sinal!
acc_musterPoint_signPoint=Ponto de sinalização
acc_musterPoint_delFailTip=Já existem pontos de encontro ativos e não podem ser excluídos!
acc_musterPointReport_enterTime=Digite a hora
acc_musterPointReport_dataAnalysis=Análise de Dados
acc_musterPointReport_safe=Seguro
acc_musterPointReport_danger=Perigo
acc_musterPointReport_signInManually=perfuração manual
acc_musterPoint_editTip=O ponto de encontro está ativo e não pode ser editado!
acc_musterPointEmail_total=Presença prevista:
acc_musterPointEmail_safe=Registado (cofre):
acc_musterPointEmail_dangerous=Em perigo:
acc_musterPoint_messageNotification=Notificação da mensagem aquando da activação
acc_musterPointReport_sendEmail=Relatório de push agendado
acc_musterPointReport_sendInterval=Enviar Intervalo
acc_musterPointReport_sendTip=Certifique-se de que o método de notificação selecionado está configurado com sucesso, caso contrário, a notificação não será enviada normalmente!
acc_musterPoint_mailSubject=Aviso de montagem de emergência
acc_musterPoint_mailContent=Por favor, reúna-se em "{0}" imediatamente e faça login no dispositivo "{1}", obrigado!
acc_musterPointReport_mailHead=Olá, é um relatório de emergência. Por favor, reveja.
acc_musterPoint_visitorsStatistics=Estatísticas dos visitantes
# 报警监控
acc_alarm_priority=Prioridade
acc_alarm_total=No total
acc_alarm_today=Registro de hoje
acc_alarm_unhandled=Não confirmados
acc_alarm_inProcess=Em processo
acc_alarm_acknowledged=Confirmado
acc_alarm_top5=Cinco primeiros alarmes
acc_alarm_monitoringTime=Tempo de Monitoramento
acc_alarm_history=Histórico de Alarmes
acc_alarm_acknowledgement=Registos de processamento
acc_alarm_eventDescription=Detalhes do Evento
acc_alarm_acknowledgeText=Após a seleção, OS detalhes do evento de alarme e-mail serão enviados para a caixa de correio especificada
acc_alarm_emailSubject=Adicionar Registro de processamento de eventos de alarme
acc_alarm_mute=Mudo
acc_alarm_suspend=Suspender
acc_alarm_confirmed=O incidente foi confirmado.
acc_alarm_list=Log de alarme
#ntp
acc_device_setNTPService=Configurações do servidor NTP
acc_device_setNTPServiceTip=Digite vários endereços de servidor, separados por vírgulas (,) ou ponto e vírgula (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=Superusuários não são limitados por fuso horário, antirretorno, intertravamento, abertura de porta multicartão, feriados, antirretorno horário, etc., e têm alta prioridade de abertura de porta.
acc_editPerson_delayPassageTip=Quando marcado, o tempo de espera para esta pessoa pode ser estendido. Adequado para pessoas com deficiência ou outras pessoas com necessidades especiais.
acc_editPerson_disabledTip=Quando marcada, a autoridade de controle de acesso da pessoa será desabilitada temporariamente.
#门禁向导
acc_guide_title=Assistente de configuração do módulo de controle de acesso
acc_guide_addPersonTip=Você precisa adicionar a pessoa e as credenciais correspondentes (face ou impressão digital ou cartão ou palm ou senha); se você já adicionou, pule esta etapa diretamente
acc_guide_timesegTip=Por favor, configure um período de abertura válido
acc_guide_addDeviceTip=Por favor, adicione o dispositivo correspondente como ponto de acesso
acc_guide_addLevelTip=Adicionar nível de controle de acesso
acc_guide_personLevelTip=Atribua a autoridade de controle de acesso correspondente à pessoa
acc_guide_rtMonitorTip=Verificar registros de controle de acesso em tempo real
acc_guide_rtMonitorTip2=Visualização em tempo real de registros de controle de acesso após adicionar a área e a porta correspondente
#查看区域内人员
acc_zonePerson_cleanCount=Limpar as estatísticas de entrada e saída de pessoas
acc_zonePerson_inCount=Estatísticas do número de pessoas que entram
acc_zonePerson_outCount=Estatísticas do número de pessoas saindo
#biocv460
acc_device_validFail=O nome de usuário ou senha está incorreto e a verificação falha!
acc_device_pwdRequired=Só pode inserir um máximo de 6 inteiros