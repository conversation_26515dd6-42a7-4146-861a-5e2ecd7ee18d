#[1]左侧菜单
acc_module=Access
acc_leftMenu_accDev=Device Management
acc_leftMenu_auxOut=Auxiliary Output
acc_leftMenu_dSTime=Daylight Saving Time
acc_leftMenu_access=Access Control
acc_leftMenu_door=Door Setting
acc_leftMenu_accRule=Access Rule
acc_leftMenu_interlock=Interlock
acc_leftMenu_antiPassback=Anti-Passback
acc_leftMenu_globalLinkage=Global Linkage
acc_leftMenu_firstOpen=First-Person Open
acc_leftMenu_combOpen=Multi-person Verification
acc_leftMenu_personGroup=Multi-Person Group
acc_leftMenu_level=Access Levels
acc_leftMenu_electronicMap=Map
acc_leftMenu_personnelAccessLevels=Personnel Access Levels
acc_leftMenu_searchByLevel=By Access Level
acc_leftMenu_searchByDoor=Access Rights By Door
acc_leftMenu_expertGuard=Advanced Functions
acc_leftMenu_zone=Area Definition
acc_leftMenu_readerDefine=Reader Define
acc_leftMenu_gapbSet=Global Anti-Passback
acc_leftMenu_whoIsInside=Area Headcount
acc_leftMenu_whatRulesInside=What Rules Apply Inside
acc_leftMenu_occupancy=Crowd Control
acc_leftMenu_route=Route Control
acc_leftMenu_globalInterlock=Global Interlock
acc_leftMeue_globalInterlockGroup=Global Interlock Group
acc_leftMenu_dmr=Stay Duration
acc_leftMenu_personLimit=Authorization Limits
acc_leftMenu_verifyModeRule=Verification Mode
acc_leftMenu_verifyModeRulePersonGroup=Verification Mode Group
acc_leftMenu_extDev=I/O Board
acc_leftMenu_firstInLastOut=First In And Last Out
acc_leftMenu_accReports=Access Control Reports
#[3]门禁时间段
acc_timeSeg_entity=Time Period
acc_timeSeg_canNotDel=The time period is in use and cannot be deleted!
#[4]门禁设备--公共的在common中
acc_common_ruleName=Rule Name
acc_common_hasBeanSet=Has been set
acc_common_notSet=Not Set
acc_common_hasBeenOpened=Has been opened
acc_common_notOpened=Not opened
acc_common_partSet=Part of the set
acc_common_linkageAndApbTip=Linkage and global linkage, anti-passback and global anti-passback are set at the same time, there may be conflicts.
acc_common_vidlinkageTip=Make sure that the corresponding input point linkage is bound to available video channel, otherwise the video linkage function will not work!
acc_common_accZoneFromTo=Cannot set the same zone
acc_common_logEventNumber=Event ID
acc_common_bindOrUnbindChannel=Binding/unbinding the camera
acc_common_boundChannel=Bound camera
#设备信息
acc_dev_iconType=Icon Type
acc_dev_carGate=Parking Barrier
acc_dev_channelGate=Flap Barrier
acc_dev_acpType=Control Panel Type
acc_dev_oneDoorACP=One-Door Access Control Panel
acc_dev_twoDoorACP=Two-Door Access Control Panel
acc_dev_fourDoorACP=Four-Door Access Control Panel
acc_dev_onDoorACD=Standalone Device
acc_dev_switchToTwoDoorTwoWay=Switch to Two-door Two-way
acc_dev_addDevConfirm2=Tip: Device connection is successful, but the type of the access control panel differs from the actual one, modify it to {0} door(s) control panel. Continue to add?
acc_dev_addDevConfirm4=Standalone Device. Continue to add?
acc_dev_oneMachine=Standalone Device
acc_dev_fingervein=Finger Vein
acc_dev_control=Device Control
acc_dev_protocol=Protocol Type
acc_ownedBoard=Owning Board
#设备操作
acc_dev_start=Start
acc_dev_accLevel=Access Authority
acc_dev_timeZoneAndHoliday=TimeZone, holidays
acc_dev_linkage=Linkage
acc_dev_doorOpt=Door Parameters
acc_dev_firstPerson=First-Person Open Door
acc_dev_multiPerson=Multi-Person Open Door
acc_dev_interlock=Interlock
acc_dev_antiPassback=Anti-Passback
acc_dev_wiegandFmt=Wiegand Format
acc_dev_outRelaySet=Auxiliary Output Settings
acc_dev_backgroundVerifyParam=Bg-Verification Options
acc_dev_getPersonInfoPrompt=Please ensure that you have get personnel information successfully, otherwise an exception will occur. Continue?
acc_dev_getEventSuccess=Get events successfully.
acc_dev_getEventFail=Failed to get event(s).
acc_dev_getInfoSuccess=Get information successfully.
acc_dev_getInfoXSuccess=Get {0} successfully.
acc_dev_getInfoFail=Failed to get the information.
acc_dev_updateExtuserInfoFail=Failed to update the personnel information for extended passage, please retrieve information.
acc_dev_getPersonCount=Get User Quantity
acc_dev_getFPCount=Get Fingerprint Quantity
acc_dev_getFVCount=Get Finger Vein Quantity
acc_dev_getFaceCount=Get Face Quantity
acc_dev_getPalmCount=Get Palm Quantity
acc_dev_getBiophotoCount=Get Face Picture Quantity
acc_dev_noData=No data are returned from the device.
acc_dev_noNewData=No new transactions in the device.
acc_dev_softLtDev=There are more people in software than in device.
acc_dev_personCount=People Quantity:
acc_dev_personDetail=Details are as follows:
acc_dev_softEqualDev=People quantity in both the software and device are the same.
acc_dev_softGtDev=There are more people in device than in software.
acc_dev_cmdSendFail=Failed to send commands, please re-send.
acc_dev_issueVerifyParam=Set Bg-Verification Options
acc_dev_verifyParamSuccess=Bg-Verification options applied successfully
acc_dev_backgroundVerify=Background Verification
acc_dev_selRightFile=Select the correct upgrade file!
acc_dev_devNotOpForOffLine=Device is offline, please try again later
acc_dev_devNotSupportFunction=Device does not support this feature
acc_dev_devNotOpForDisable=Device is disabled, please try again later
acc_dev_devNotOpForNotOnline=Device is offline or disabled, please try again later
acc_dev_getPersonInfo=Obtain personnel information
acc_dev_getFPInfo=Obtain fingerprint information
acc_dev_getFingerVeinInfo=Obtain finger vein information
acc_dev_getPalmInfo=Obtain palm information
acc_dev_getBiophotoInfo=Obtain visible light face information
acc_dev_getIrisInfo=Obtain iris information
acc_dev_disable=disabled, please re-select
acc_dev_offlineAndContinue=is offline, continue?
acc_dev_offlineAndSelect=is offline.
acc_dev_opAllDev=All Device
acc_dev_opOnlineDev=Online Device
acc_dev_opException=handle exceptions
acc_dev_exceptionAndConfirm=Device connection timeout, the operation failed. Check the network connection
acc_dev_getFaceInfo=Obtain face information
acc_dev_selOpDevType=Select the type of device to be operated:
acc_dev_hasFilterByFunc=Only show the devices online and supporting this function!
acc_dev_masterSlaveMode=RS485 master and slave mode
acc_dev_master=Master
acc_dev_slave=Slave
acc_dev_modifyRS485Addr=Modify RS485 Address
acc_dev_rs485AddrTip=Please enter an integer between 1 and 63!
acc_dev_enableFeature=The devices which have enabled background verification
acc_dev_disableFeature=The devices which have disabled background verification
acc_dev_getCountOnly=Get count only
acc_dev_queryDevPersonCount=Query Personnel Count
acc_dev_queryDevVolume=View Device Capacity
acc_dev_ruleType=Rule Type
acc_dev_contenRule=Rules Details
acc_dev_accessRules=View Rules of Devices
acc_dev_ruleContentTip=Between multiple rules with a '|' separated.
acc_dev_rs485AddrFigure=RS485 Address Code Figure
acc_dev_addLevel=Add to Level
acc_dev_personOrFingerTanto=The number of personnel or fingerprint exceeds limits, synchronization failed...
acc_dev_personAndFingerUnit=(Number)
acc_dev_setDstime=Set Daylight Saving Time
acc_dev_setTimeZone=Set Device Time Zone
acc_dev_selectedTZ=Selected Time Zone
acc_dev_timeZoneSetting=Time Zone Setting...
acc_dev_timeZoneCmdSuccess=Time Zone Command Send Success...
acc_dev_enableDstime=Enable Daylight Saving Time
acc_dev_disableDstime=Disable Daylight Saving Time
acc_dev_timeZone=TimeZone
acc_dev_dstSettingTip=Daylight Saving Time Setting...
acc_dev_dstDelTip=Removing Device Daylight Saving Time...
acc_dev_enablingDst=Enabling Daylight Saving Time
acc_dev_dstEnableCmdSuccess=Enabling Daylight Saving Time Command Send Success.
acc_dev_disablingDst=Disabling the Daylight Saving Time.
acc_dev_dstDisableCmdSuccess=Disabling the Daylight Saving Time Command Send Success.
acc_dev_dstCmdSuccess=Daylight Saving Time Command Send Success...
acc_dev_usadst=Daylight Saving Time
acc_dev_notSetDst=No settings
acc_dev_selectedDst=Selected Daylight Saving Time
acc_dev_configMasterSlave=Master-slave configuration
acc_dev_hasFilterByUnOnline=Only show the online Device.
acc_dev_softwareData=If you find that the data is not consistent with the device, please synchronize the data of devices before trying again.
acc_dev_disabled=The disabled devices can not be operated!
acc_dev_offline=The offline devices can not be operated!
acc_dev_noSupport=The devices do not support this function and cannot be operated!
acc_dev_noRegDevTip=This device is not defined as a register device. The data in the software will not be updated. Would you like to continue?
acc_dev_noOption=No qualified options
acc_dev_devFWUpdatePrompt=The current device will not normally use the disabled person and the person validity function (please refer to the user manual).
acc_dev_panelFWUpdatePrompt=The current device will not normally use the disabled person and the person validity function, immediately upgrade firmware?
acc_dev_sendEventCmdSuccess=Delete Event Command successfully sent
acc_dev_tryAgain=Please try again.
acc_dev_eventAutoCheckAndUpload=Automatic checking and upload events
acc_dev_eventUploadStart=Start to upload events.
acc_dev_eventUploadEnd=Events upload completed.
acc_dev_eventUploadFailed=Events upload failed.
acc_dev_eventUploadPrompt=The device firmware version is too old, before you upgrade the firmware, you want to:
acc_dev_backupToSoftware=Back up data to software
acc_dev_deleteEvent=Delete the old record
acc_dev_upgradePrompt=The firmware version is too old and could cause error, please back up data to software before updating firmware.
acc_dev_conflictCardNo=Card present in the system to {0} in the other person!
acc_dev_rebootAfterOperate=The operation succeeded, the device will restart later.
acc_dev_baseOptionTip=Abnormality of obtained basic parameter.
acc_dev_funOptionTip=Abnormality of obtained function parameter.
acc_dev_sendComandoTip=Get the device parameter command failed to send.
acc_dev_noC3LicenseTip=Cannot add this type of device ({0})! Please contact our sales department.
acc_dev_combOpenDoorTip=({0})Multi-person open door has been set, can't be used at the same time with background verification.
acc_dev_combOpenDoorPersonCountTip=The number of people opening the door in group {0} must not be greater than {1}!
acc_dev_addDevTip=This applies only to adding a device with PULL communication protocol!
acc_dev_addError=Device add exception, missing parameters ({0})!
acc_dev_updateIPAndPortError=Update server IP and port error...
acc_dev_transferFilesTip=Firmware test completed, transferring files
acc_dev_serialPortExist=Serial Port Exist
acc_dev_isExist=Having Device
acc_dev_description=Description
acc_dev_searchEthernet=Search the Ethernet device
acc_dev_searchRS485=Search the RS485 device
acc_dev_rs485AddrTip1=RS485 start address cannot be greater than the end address
acc_dev_rs485AddrTip2=The search scope of RS485 must be less than 20
acc_dev_clearAllCmdCache=Clear All Command
acc_dev_authorizedSuccessful=Authorized Successful
acc_dev_authorize=Authorize
acc_dev_registrationDevice=Register Device
acc_dev_setRegistrationDevice=Set as Registration Device
acc_dev_mismatchedDevice=This device cannot be used for your market. Please check the serial number of the device with our sales.
acc_dev_pwdStartWithZero=Communication password can not start with zero!
acc_dev_maybeDisabled=The current license only allows you to add another {0} more door(s), the door of the new added device that exceeds the license door limit will be disabled, whether to continue to operate?
acc_dev_Limit=Device License key has reached the upper limit to add, please authorize.
acc_dev_selectDev=Please select the device!
acc_dev_cannotAddPullDevice=Cannot add the pull device! Please contact our sales.
acc_dev_notContinueAddPullDevice=There have been {0} pull devices in the system, cannot continue adding again! Please contact our sales.
acc_dev_deviceNameNull=Device model is empty; cannot add device!
acc_dev_commTypeErr=Communication Type does not match; cannot add device!
acc_dev_inputDomainError=Please enter a valid domain address.
acc_dev_levelTip=There are more than 5000 people in the level, cannot add the device to the level here.
acc_dev_auxinSet=Auxiliary Input Setting
acc_dev_verifyModeRule=Verification Mode Rule
acc_dev_netModeWired=Wired
acc_dev_netMode4G=4G
acc_dev_netModeWifi=WIFI
acc_dev_updateNetConnectMode=Switch Network Connection
acc_dev_wirelessSSID=Wireless SSID
acc_dev_wirelessKey=Wireless Key
acc_dev_searchWifi=Search WIFI
acc_dev_testNetConnectSuccess=Is the connection successful?
acc_dev_testNetConnectFailed=The connection can not communicate properly!
acc_dev_signalIntensity=Signal Strength
acc_dev_resetSearch=Search Again
acc_dev_addChildDevice=Add Sub-Device
acc_dev_modParentDevice=Change the master device
acc_dev_configParentDevice=Master Device Setting
acc_dev_lookUpChildDevice=View child devices
acc_dev_addChildDeviceTip=Authorization needs to be performed under an authorized sub-device
acc_dev_maxSubCount=The number of authorized sub-devices exceeds the maximum number of {0} units.
acc_dev_seletParentDevice=Please Select the master device!
acc_dev_networkCard=Network Card
acc_dev_issueParam=Custom Synchronization Parameters
acc_dev_issueMode=Synchronize Mode
acc_dev_initIssue=Firmware Version 3030 Initialization Data
acc_dev_customIssue=Custom Synchronize Data
acc_dev_issueData=Data
acc_dev_parent=Master Device
acc_dev_parentEnable=Master device is disabled
acc_dev_parentTips=Binding master device will delete all data in the device and will need to be set again.
acc_dev_addDevIpTip=The new IP address cannot be the same as the server IP address
acc_dev_modifyDevIpTip=The new server address cannot be the same as the device IP address
acc_dev_setWGReader=Set Wiegand Reader
acc_dev_selectReader=Click to select the reader
acc_dev_IllegalDevice=Illegal Device
acc_dev_syncTimeWarnTip=Synchronization times for the following devices should be synchronized on the master device.
acc_dev_setTimeZoneWarnTip=The following device's time zone should be synchronized on the master device.
acc_dev_setDstimeWarnTip=The daylight saving time for the following devices should be synchronized on the master device.
acc_dev_networkSegmentSame=Two network adapters can not use the same network segment.
acc_dev_upgradeProtocolNoMatch=Upgrade file protocol does not match
acc_dev_ipAddressConflict=The device with the same IP address already exists. Please modify the device IP address and add it again.
acc_dev_checkServerPortTip=The configured server port is inconsistent with the system communication port, which may result in the failure to add. Continue to operate?
acc_dev_clearAdmin=Clear Administrator Permission
acc_dev_setDevSate=Set Device In/Out Status
acc_dev_sureToClear=Are you sure to clear device administrator permissions?
acc_dev_hostState=Master Device Status
acc_dev_regDeviceTypeTip=This device is restricted and is not allowed to add directly. Please contact the software provider!
acc_dev_extBoardType=I/O Board Type
acc_dev_extBoardTip=After the configuration, you need to restart the device to take effect.
acc_dev_extBoardLimit=Only {0} I/O board of this type can be added to each device!
acc_dev_replace=Replace Device
acc_dev_replaceTip=After the replacement, the old device will not work, please be careful!
acc_dev_replaceTip1=After the replacement, please perform the "sync all data" operation;
acc_dev_replaceTip2=Please make sure the replacement device model is the same!
acc_dev_replaceTip3=Please make sure that the replacement device have set the same server address and port as the old device!
acc_dev_replaceFail=The device machine type is inconsistent and cannot be replaced!
acc_dev_notApb=This device is unable to perform gate or read head anti submarine operations
acc_dev_upResourceFile=Upload resource files
acc_dev_playOrder=Play order
acc_dev_setFaceServerInfo=Set Facial Backend Comparison Parameters
acc_dev_faceVerifyMode=Face Comparison Mode
acc_dev_faceVerifyMode1=Local Comparison
acc_dev_faceVerifyMode2=Backend Comparison
acc_dev_faceVerifyMode3=Local Comparison Priority
acc_dev_faceBgServerType=Type of Facial Backend Server
acc_dev_faceBgServerType1=Software Platform Services
acc_dev_faceBgServerType2=Third-Party Services
acc_dev_isAccessLogic=Enable Access Control Logic Verification
#[5]门-其他关联的也复用此处
acc_door_entity=Door
acc_door_number=Door Number
acc_door_name=Door Name
acc_door_activeTimeZone=Activation Period
acc_door_passageModeTimeZone=Passage Mode Time Zone
acc_door_setPassageModeTimeZone=Passage Mode Time Zone Set
acc_door_notPassageModeTimeZone=Non-Passage Mode Time Zone
acc_door_lockOpenDuration=Unlock Duration
acc_door_entranceApbDuration=In Anti-Passback Duration
acc_door_sensor=Door Sensor
acc_door_sensorType=Door Sensor Type
acc_door_normalOpen=Unrestricted Access Mode
acc_door_normalClose=Restricted Access Mode
acc_door_sensorDelay=Door Sensor Delay
acc_door_closeAndReverseState=Reverse Lock State on Door Close
acc_door_hostOutState=Master Access Status
acc_door_slaveOutState=Slave Access Status
acc_door_inState=Entry
acc_door_outState=Exit
acc_door_requestToExit=REX Mode
acc_door_withoutUnlock=Lock
acc_door_unlocking=Unlock
acc_door_alarmDelay=REX Delay
acc_door_duressPassword=Duress Password
acc_door_currentDoor=Current Door
acc_door_allDoorOfCurDev=All Doors in Current Device
acc_door_allDoorOfAllDev=All Doors in All Devices
acc_door_allDoorOfAllControlDev=All Doors in All Control Devices
acc_door_allDoorOfAllStandaloneDev=All Doors of all Standalone Devices
acc_door_allWirelessLock=All Wireless Locks
acc_door_max6BitInteger=Maximum 6 Bit Integer
acc_door_direction=Direction
acc_door_onlyInReader=Entry Reader Only
acc_door_bothInAndOutReader=Entry & Exit Readers
acc_door_noDoor=Please add the door.
acc_door_nameRepeat=The door names are duplicated.
acc_door_duressPwdError=The duress password must not be the same as any personal password.
acc_door_urgencyStatePwd=Please input {0} bit integer!
acc_door_noDevOnline=No device is online, or the door does not support card verification mode.
acc_door_durationLessLock=The Door Sensor Delay must be greater than the Lock Open Duration.
acc_door_lockMoreDuration=The Lock Open Duration must be less than the Door Sensor Delay.
acc_door_lockAndExtLessDuration=The sum of the Lock Open Duration and the Extended Release Time must be less than the Door Sensor Delay.
acc_door_noDevTrigger=The device does not meet the conditions!
acc_door_relay=Relay
acc_door_pin=PIN
acc_door_selDoor=Select Door
acc_door_sensorStatus=Door Sensor ({0})
acc_door_sensorDelaySeconds=Door Sensor Delay ({0} s)
acc_door_timeSeg=Time Zone ({0})
acc_door_combOpenInterval=Multi-Person Operation Interval
acc_door_delayOpenTime=Unlock Delay
acc_door_extDelayDrivertime=Extended Release Time
acc_door_enableAudio=Enable Alarm
acc_door_disableAudio=Disable Alarm Sounds
acc_door_lockAndExtDelayTip=The sum of the Lock Open Duration and Delay Time Interval of Passage is not greater than 254 seconds.
acc_door_disabled=The disabled doors can not be operated!
acc_door_offline=The offline doors can not be operated!
acc_door_notSupport=The following doors don't support this feature!
acc_door_select=Select the door
acc_door_pushMaxCount=There are {0} enabled doors in the system and have reached the license limit! Please contact our sales department
acc_door_outNumber=The current license only allows you to add another {0} more door(s). Please re-select the doors and try again, or contact our sales department to get an update license.
acc_door_latchTimeZone=REX Time Zone
acc_door_wgFmtReverse=Card number reversal
acc_door_allowSUAccessLock=Allow Superuser Access When Lockdown
acc_door_verifyModeSinglePwd=Password cannot be used as an independent verification method!
acc_door_doorPassword=Door opening password
#辅助输入
acc_auxIn_timeZone=Activation Period
#辅助输出
acc_auxOut_passageModeTimeZone=Passage Mode Time Zone
acc_auxOut_disabled=The disabled auxiliary output cannot be operated!
acc_auxOut_offline=The offline auxiliary output cannot be operated!
#[8]门禁权限组
acc_level_doorGroup=Door Combination
acc_level_openingPersonnel=Opening Personnel
acc_level_noDoor=There is no item available. Please add device first.
acc_level_doorRequired=Door must be selected.
acc_level_doorCount=Door Count
acc_level_doorDelete=Delete Door
acc_level_isAddDoor=Immediately add doors to the current Access Control Level?
acc_level_master=General
acc_level_noneSelect=Please add an access control level.
acc_level_useDefaultLevel=Switch the access level to the new department?
acc_level_persAccSet=Personnel access control settings
acc_level_visUsed={0} is already in use by the visitor module and cannot be deleted!
acc_level_doorControl=Door Control
acc_level_personExceedMax=The number of current permission groups ({0}), and the maximum number of optional permission groups ({1})
acc_level_exportLevel=Export Access Level
acc_level_exportLevelDoor=Export Doors of Access Level
acc_level_exportLevelPerson=Export Personnel of Access Level
acc_level_importLevel=Import Access Level
acc_level_importLevelDoor=Import Doors of Access Level
acc_level_importLevelPerson=Import  Personnel of Access Level
acc_level_exportDoorFileName=Doors Information of Access Level
acc_level_exportPersonFileName=Personnel Information of Access Level
acc_levelImport_nameNotNull=Access Level name cannot be empty
acc_levelImport_timeSegNameNotNull=Time Zone cannot be empty
acc_levelImport_areaNotExist=Area does not exist!
acc_levelImport_timeSegNotExist=Time Zone does not exist!
acc_levelImport_nameExist=Access Level name {0} already exists!
acc_levelImport_levelDoorExist=The Doors of Access Level {0} already exists!
acc_levelImport_levelPersonExist=The Personnel of Access Level {0} already exists!
acc_levelImport_noSpecialChar=Access level name cannot contain special characters!
#[10]首人常开
acc_firstOpen_setting=First-Person Open
acc_firstOpen_browsePerson=Browse Personnel
#[11]多人组合开门
acc_combOpen_comboName=Combination Name
acc_combOpen_personGroupName=Group Name
acc_combOpen_personGroup=Muti-Person Group
acc_combOpen_verifyOneTime=Current Personnel Count
acc_combOpen_eachGroupCount=Number of opening personnel in each group
acc_combOpen_group=Group
acc_combOpen_changeLevel=Open Door Group
acc_combOpen_combDeleteGroup=Existing opening combination reference, please delete first.
acc_combOpen_ownedLevel=Belong Groups
acc_combOpen_mostPersonCount=Group count must not be more than five people.
acc_combOpen_leastPersonCount=Group count must not be less than two people.
acc_combOpen_groupNameRepeat=Group name duplicate.
acc_combOpen_groupNotUnique=Open Door Person Groups must not be identical.
acc_combOpen_persNumErr=The number of the group exceeds the actual value of your choice, please re-select!
acc_combOpen_combOpengGroupPersonShort=After people were removed, the number of personnel door open group group is not enough, please delete open group first.
acc_combOpen_backgroundVerifyTip=The door is in device with enabled background verification; cannot be used at the same time with muti-person open door!
#[12]互锁
acc_interlock_rule=Interlock Rule
acc_interlock_mode1Or2=Interlock between {0} and {1}
acc_interlock_mode3=Interlock among {0} and {1} and {2}
acc_interlock_mode4=Interlock between {0} and {1} or between {2} and {3}
acc_interlock_mode5=Interlock among {0} and {1} and {2} and {3}
acc_interlock_hasBeenSet=Has interlock program settings
acc_interlock_group1=Group 1
acc_interlock_group2=Group 2
acc_interlock_ruleInfo=Inter Group Interlocking
acc_interlock_alreadyExists=The same interlocking rule already exists, please do not add it again!
acc_interlock_groupInterlockCountErr=The intra group interlocking rule requires at least two doors
acc_interlock_ruleType=Interlocking Rule Type
#[13]反潜
acc_apb_rules=Anti-Passback Rule
acc_apb_reader=Anti-Passback between readers of door {0}
acc_apb_reader2=Anti-Passback among readers of doors: {0}, {1}
acc_apb_reader3=Anti-Passback among readers of doors: {0}, {1}, {2}
acc_apb_reader4=Anti-Passback among readers of doors: {0}, {1}, {2}, {3}
acc_apb_reader5=Anti-Passback out-reader on the door {0}
acc_apb_reader6=Anti-Passback in-reader on the door {0}
acc_apb_reader7=Anti-Passback among readers of all 4 doors
acc_apb_twoDoor=Anti-Passback between {0} and {1}
acc_apb_fourDoor=Anti-Passback between {0} and {1} , Anti-Passback between {2} and {3}
acc_apb_fourDoor2=Anti-Passback between {0} or {1} and {2} or {3}
acc_apb_fourDoor3=Anti-Passback between {0} and {1} or {2}
acc_apb_fourDoor4=Anti-Passback between {0} and {1} or {2} or {3}
acc_apb_hasBeenSet=Has been set for Anti-Passback
acc_apb_conflictWithGapb=This device has global Anti-Passback settings, and cannot be modified!
acc_apb_conflictWithApb=Zone of the device has Anti-Passback settings, and cannot be modified!
acc_apb_conflictWithEntranceApb=Zone of the device has entrance Anti-Passback settings, and cannot be modified!
acc_apb_controlIn=Anti-Passback In
acc_apb_controlOut=Anti-Passback Out
acc_apb_controlInOut=Anti-Passback In/Out
acc_apb_groupIn=Join The Group
acc_apb_groupOut=Out Of Group
acc_apb_reverseName=Reverse Anti Submarine
acc_apb_door=Gate Anti Submarine
acc_apb_readerHead=Reading The Tead To Counter The Submarine
acc_apb_alreadyExists=The same anti submarine rule already exists, please do not add it again!
#[17]电子地图
acc_map_addDoor=Add Door
acc_map_addChannel=Add Camera
acc_map_noAccess=No permission to access electronic map module, please contact the administrator!
acc_map_noAreaAccess=You do not have the E-map permission for this area, please contact the administrator!
acc_map_imgSizeError=Please upload the picture which the size is less than {0}M!
#[18]门禁事件记录
acc_trans_entity=Transaction
acc_trans_eventType=Event type
acc_trans_firmwareEvent=Firmware Events
acc_trans_softwareEvent=Software Event
acc_trans_today=Events From Today
acc_trans_lastAddr=Last Known Position
acc_trans_viewPhotos=View Photos
acc_trans_exportPhoto=Export photos
acc_trans_photo=Access control incident photo
acc_trans_dayNumber=Days
acc_trans_fileIsTooLarge=The exported file is too large, please reduce the scope to export
#[19]门禁验证方式
acc_verify_mode_onlyface=Face
acc_verify_mode_facefp=Face+Fingerprint
acc_verify_mode_facepwd=Face+Password
acc_verify_mode_facecard=Face+Card
acc_verify_mode_facefpcard=Face+Fingerprint+Card
acc_verify_mode_facefppwd=Face+Fingerprint+Password
acc_verify_mode_fv=Finger Vein
acc_verify_mode_fvpwd=Finger Vein+Password
acc_verify_mode_fvcard=Finger Vein+Card
acc_verify_mode_fvpwdcard=Finger Vein+Password+Card
acc_verify_mode_pv=Palm
acc_verify_mode_pvcard=Palm+Card
acc_verify_mode_pvface=Palm+Face
acc_verify_mode_pvfp=Palm+Fingerprint
acc_verify_mode_pvfacefp=Palm+Face+Fingerprint
#[20]门禁事件编号
acc_eventNo_-1=None
acc_eventNo_0=Normal Swipe Open
acc_eventNo_1=Swipe during Passage Mode Time Zone
acc_eventNo_2=First-Card Normal Open(Swipe Card)
acc_eventNo_3=Multi-Person Open(Swipe Card)
acc_eventNo_4=Emergency Password Open
acc_eventNo_5=Open during Passage Mode Time Zone
acc_eventNo_6=Linkage Event Triggered
acc_eventNo_7=Cancel Alarm
acc_eventNo_8=Remote Opening
acc_eventNo_9=Remote Closing
acc_eventNo_10=Disable Intraday Passage Mode Time Zone
acc_eventNo_11=Enable Intraday Passage Mode Time Zone
acc_eventNo_12=Auxiliary Output Remotely Open
acc_eventNo_13=Auxiliary Output Remotely Close
acc_eventNo_14=Normal Fingerprint Open
acc_eventNo_15=Multi-Person Open(Fingerprint)
acc_eventNo_16=Press Fingerprint during Passage Mode Time Zone
acc_eventNo_17=Card plus Fingerprint Open
acc_eventNo_18=First-Card Normal Open(Press Fingerprint)
acc_eventNo_19=First-Card Normal Open(Card plus Fingerprint)
acc_eventNo_20=Operate Interval too Short
acc_eventNo_21=Door Inactive Time Zone(Swipe Card)
acc_eventNo_22=Illegal Time Zone
acc_eventNo_23=Access Denied
acc_eventNo_24=Anti-Passback
acc_eventNo_25=Interlock
acc_eventNo_26=Multi-Person Authentication(Swipe Card)
acc_eventNo_27=Disabled Card
acc_eventNo_28=Extended Open Door Timeout
acc_eventNo_29=Card Expired
acc_eventNo_30=Password Error
acc_eventNo_31=Press Fingerprint Interval too Short
acc_eventNo_32=Multi-Person Authentication(Press Fingerprint)
acc_eventNo_33=Fingerprint Expired
acc_eventNo_34=Disabled Fingerprint
acc_eventNo_35=Door Inactive Time Zone(Press Fingerprint)
acc_eventNo_36=Door Inactive Time Zone(Press Exit Button)
acc_eventNo_37=Failed to Close during Passage Mode Time Zone
acc_eventNo_38=Card Reported Lost
acc_eventNo_39=Access Disabled
acc_eventNo_40=Multi-Person Authentication Failed(Press Fingerprint)
acc_eventNo_41=Verify Mode Error
acc_eventNo_42=Wiegand Format Error
acc_eventNo_43=Anti-Passback Verification Timeout
acc_eventNo_44=Background Verify Failed
acc_eventNo_45=Background Verify Timeout
acc_eventNo_47=Failed to Send the Command
acc_eventNo_48=Multi-Person Authentication Failed(Swipe Card)
acc_eventNo_49=Door Inactive Time Zone(Password)
acc_eventNo_50=Press Password Interval too Short
acc_eventNo_51=Multi-Person Authentication(Password)
acc_eventNo_52=Multi-Person Authentication Fails(Password)
acc_eventNo_53=Password Expired
acc_eventNo_100=Tamper Alarm
acc_eventNo_101=Duress Password Open
acc_eventNo_102=Door Opened Forcefully
acc_eventNo_103=Duress Fingerprint Open
acc_eventNo_200=Door Opened Correctly
acc_eventNo_201=Door Closed Correctly
acc_eventNo_202=Exit Button Open
acc_eventNo_203=Multi-Person Open(Card plus Fingerprint)
acc_eventNo_204=Passage Mode Time Zone Over
acc_eventNo_205=Remote Normal Opening
acc_eventNo_206=Device Started
acc_eventNo_207=Password Open
acc_eventNo_208=Superuser Open Doors
acc_eventNo_209=Exit Button triggered(Without Unlock)
acc_eventNo_210=Start the fire door
acc_eventNo_211=Superuser Close Doors
acc_eventNo_212=Enable elevator control function
acc_eventNo_213=Disable elevator control function
acc_eventNo_214=Multi-Person Open(Password)
acc_eventNo_215=First-Card Normal Open(Password)
acc_eventNo_216=Password during Passage Mode Time Zone
acc_eventNo_220=Auxiliary Input Disconnected (Open)
acc_eventNo_221=Auxiliary Input Shorted (Closed)
acc_eventNo_222=Background Verification Succeeded
acc_eventNo_223=Background Verification
acc_eventNo_225=Auxiliary Input Normal
acc_eventNo_226=Auxiliary Input Trigger
acc_newEventNo_0=Normal Verify Open
acc_newEventNo_1=Verify During Passage Mode Time Zone
acc_newEventNo_2=First-Personnel Open
acc_newEventNo_3=Multi-Personnel Open
acc_newEventNo_20=Operation Interval too Short
acc_newEventNo_21=Door Inactive Time Zone Verify Open
acc_newEventNo_26=Multi-Personnel Authentication Wait
acc_newEventNo_27=Unregistered Personnel
acc_newEventNo_29=Personnel Expired
acc_newEventNo_30=Password Error
acc_newEventNo_41=Verify Mode Error
acc_newEventNo_43=Staff Lock
acc_newEventNo_44=Background Verify Failed
acc_newEventNo_45=Background Verify Timed Out
acc_newEventNo_48=Multi-Personnel Verify Failed
acc_newEventNo_54=The battery voltage is too low
acc_newEventNo_55=Replace the battery immediately
acc_newEventNo_56=Illegal Operation
acc_newEventNo_57=Backup Power
acc_newEventNo_58=Normally Open Alarm
acc_newEventNo_59=Illegal Management
acc_newEventNo_60=Door Locked Inside
acc_newEventNo_61=Replicated
acc_newEventNo_62=Prohibit Users
acc_newEventNo_63=Door Locked
acc_newEventNo_64=Inactive Exit button Time Zone
acc_newEventNo_65=Inactive Auxiliary Input Time Zone
acc_newEventNo_66=Reader Upgrade Failed
acc_newEventNo_67=Remote Comparison Succeeded (Device Not Authorized)
acc_newEventNo_68=High Body Temperature - Access Denied
acc_newEventNo_69=Without Mask - Access Denied
acc_newEventNo_70=Face comparison server communication exception
acc_newEventNo_71=The face server is responding erratically
acc_newEventNo_73=Invalid QR Code
acc_newEventNo_74=QR Code Expired
acc_newEventNo_101=Duress Open Alarm
acc_newEventNo_104=Invalid Card Swipe alarm
acc_newEventNo_105=Cannot connect to server
acc_newEventNo_106=Main power down
acc_newEventNo_107=Battery power down
acc_newEventNo_108=Can not connect to the master device
acc_newEventNo_109=Reader Tamper Alarm
acc_newEventNo_110=Reader Offline
acc_newEventNo_112=Expansion board offline
acc_newEventNo_114=Fire alarm input disconnected (line detection)
acc_newEventNo_115=Fire alarm input short circuit (line detection)
acc_newEventNo_116=Auxiliary input disconnected (line detection)
acc_newEventNo_117=Auxiliary input short circuit (line detection)
acc_newEventNo_118=Exit switch disconnected (line detection)
acc_newEventNo_119=Exit switch short circuit (circuit detection)
acc_newEventNo_120=Door magnetic disconnection (line detection)
acc_newEventNo_121=Door magnetic short circuit (line detection)
acc_newEventNo_159=The remote control to open the door
acc_newEventNo_214=Connected to the server
acc_newEventNo_217=Connected to the master device successfully
acc_newEventNo_218=ID Card Verification
acc_newEventNo_222=Background Verify Success
acc_newEventNo_223=Background Verify
acc_newEventNo_224=Ring the bell
acc_newEventNo_227=Double open the door
acc_newEventNo_228=Double close the door
acc_newEventNo_229=Auxiliary Output Timed Normally Open
acc_newEventNo_230=Auxiliary Output Timed Close
acc_newEventNo_232=Verify Success
acc_newEventNo_233=Activate Lockdown
acc_newEventNo_234=Deactivate Lockdown
acc_newEventNo_235=Reader Upgrade Success
acc_newEventNo_236=Reader Tamper Alarm Cleared
acc_newEventNo_237=Reader Online
acc_newEventNo_239=Device call
acc_newEventNo_240=Call ended
acc_newEventNo_243=Fire alarm input disconnected
acc_newEventNo_244=Fire alarm input short circuit
acc_newEventNo_247=Expansion board online
acc_newEventNo_4008=Mains recovery
acc_newEventNo_4014=Fire input signal disconnected, end door normally open
acc_newEventNo_4015=Door is online
acc_newEventNo_4018=Backend Comparison Open
acc_newEventNo_5023=Restricted fire protection status
acc_newEventNo_5024=Multi-Personnel Verify Timeout
acc_newEventNo_5029=Backend Comparison Failed
acc_newEventNo_6005=Record Capacity is reaching upper limit
acc_newEventNo_6006=Line short circuit (RS485)
acc_newEventNo_6007=Short circuit in the circuit (Wiegand)
acc_newEventNo_6011=Door is offline
acc_newEventNo_6012=Door dismantling alarm
acc_newEventNo_6013=Fire input signal triggered, door open
acc_newEventNo_6015=Reset the power supply of the expansion device
acc_newEventNo_6016=Restore device factory settings
acc_newEventNo_6070=Backend Comparison (Prohibited List)
acc_eventNo_undefined=Event Number Undefined
acc_advanceEvent_500=Global Anti-Passback(logical)
acc_advanceEvent_501=Person Availability(use the Date)
acc_advanceEvent_502=Person Number of Control
acc_advanceEvent_503=Global Interlock
acc_advanceEvent_504=Route Control
acc_advanceEvent_505=Global Anti-Passback(timed)
acc_advanceEvent_506=Global Anti-Passback(timed logical)
acc_advanceEvent_507=Person Availability(after the first use of valid days)
acc_advanceEvent_508=Person Availability(use number of times)
acc_advanceEvent_509=Background verification fails (unregistered personnel)
acc_advanceEvent_510=Background verification fails (data exception)
acc_alarmEvent_701=DMR Violation(setting rules: {0})
#[21]实时监控
acc_rtMonitor_openDoor=Open
acc_rtMonitor_closeDoor=Close
acc_rtMonitor_remoteNormalOpen=Remote Normally Open
acc_rtMonitor_realTimeEvent=Real-Time Events
acc_rtMonitor_photoMonitor=Photo Monitoring
acc_rtMonitor_alarmMonitor=Alarm Monitoring
acc_rtMonitor_doorState=Door State
acc_rtMonitor_auxOutName=Auxiliary Output Name
acc_rtMonitor_nonsupport=Unsupported
acc_rtMonitor_lock=Locked
acc_rtMonitor_unLock=Unlocked
acc_rtMonitor_disable=Disabled
acc_rtMonitor_noSensor=No Door Sensor
acc_rtMonitor_alarm=Alarm
acc_rtMonitor_openForce=Opened Forcefully
acc_rtMonitor_tamper=Tamper
acc_rtMonitor_duressPwdOpen=Duress Password Open
acc_rtMonitor_duressFingerOpen=Duress Fingerprint Open
acc_rtMonitor_duressOpen=Duress Open
acc_rtMonitor_openTimeout=Opening Timeout
acc_rtMonitor_unknown=Unknown
acc_rtMonitor_noLegalDoor=There is no door that meets the condition.
acc_rtMonitor_noLegalAuxOut=No auxiliary output meets the condition!
acc_rtMonitor_curDevNotSupportOp=Current device status does not support this operation!
acc_rtMonitor_curNormalOpen=Currently normal open
acc_rtMonitor_whetherDisableTimeZone=The current state of the door is always open.
acc_rtMonitor_curSystemNoDoors=The current system has not added any door or cannot find any door that meets your requirements.
acc_rtMonitor_cancelAlarm=Cancel Alarm
acc_rtMonitor_openAllDoor=Open all current doors
acc_rtMonitor_closeAllDoor=Close all current doors
acc_rtMonitor_confirmCancelAlarm=Are you sure to cancel this alarm?
acc_rtMonitor_calcelAllDoor=Cancel all alarms
acc_rtMonitor_initDoorStateTip=Obtaining all doors authorized to users in the system...
acc_rtMonitor_alarmEvent=Alarm Event
acc_rtMonitor_ackAlarm=Acknowledge
acc_rtMonitor_ackAllAlarm=Acknowledge All
acc_rtMonitor_ackAlarmTime=Acknowledge Time
acc_rtMonitor_sureToAckThese=Are you sure you confirm this {0} alarm? After your confirmation, all alarms will be canceled.
acc_rtMonitor_sureToAckAllAlarm=Are you sure you want to cancel all alarm? After your confirmation, all alarms will be canceled.
acc_rtMonitor_noSelectAlarmEvent=Please choose to confirm the alarm event!
acc_rtMonitor_noAlarmEvent=There are no alarm events in the current system!
acc_rtMonitor_forcefully=Cancel Alarm (Door Opened Forcefully)
acc_rtMonitor_addToRegPerson=Added to the registered person
acc_rtMonitor_cardExist=This card has been assigned by {0} and cannot be issued again.
acc_rtMonitor_opResultPrompt=Send {0} request successfully, failed {1}.
acc_rtMonitor_doorOpFailedPrompt=Failed to send requests to the following doors, please try again!
acc_rtMonitor_remoteOpen=Remote Open
acc_rtMonitor_remoteClose=Remote Close
acc_rtMonitor_alarmSoundClose=Audio is closed
acc_rtMonitor_alarmSoundOpen=Audio is opened
acc_rtMonitor_playAudio=Sounds Reminder
acc_rtMonitor_isOpenShowPhoto=Enable Display Photos Function
acc_rtMonitor_isOpenPlayAudio=Enable Audio Alert Function
acc_rtm_open=Remote Open the Button
acc_rtm_close=Remote Close the Button
acc_rtm_eleModule=Elevator
acc_cancelAlarm_fp=Cancel Alarm (Duress Fingerprint Open)
acc_cancelAlarm_pwd=Cancel Alarm (Duress Password Open)
acc_cancelAlarm_timeOut=Cancel Alarm (Extended Open Door Timeout)
#定时同步设备时间
acc_timing_syncDevTime=Timing synchronization time device
acc_timing_executionTime=The execution time
acc_timing_theLifecycle=The life cycle
acc_timing_errorPrompt=Input is wrong.
acc_timing_checkedSyncTime=Please select a synchronization time.
#[25]门禁报表
acc_trans_hasAccLevel=Having Level to Access
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Please add zone
acc_zone_code=Zone Code
acc_zone_parentZone=Parent Zone
acc_zone_parentZoneCode=Parent Zone Code
acc_zone_parentZoneName=Parent Zone Name
acc_zone_outside=Outside
#[G2]读头定义
acc_readerDefine_readerName=Reader Name
acc_readerDefine_fromZone=Goes From
acc_readerDefine_toZone=Goes To
acc_readerDefine_delInfo1=The zone of this reader definition is referenced by an advanced access control function and cannot be deleted!
acc_readerDefine_selReader=Select Reader
acc_readerDefine_selectReader=Please add reader!
acc_readerDefine_tip=After personnel arrives outside zone, the personnel record will be deleted.
#[G3]全局反潜
acc_gapb_zone=Zone
acc_gapb_whenToResetGapb=Anti-Passback Time Reset
acc_gapb_apbType=Anti-Passback Type
acc_gapb_logicalAPB=Logical Anti-Passback
acc_gapb_timedAPB=Timed Anti-Passback
acc_gapb_logicalTimedAPB=Timed Logical Anti-Passback
acc_gapb_lockoutDuration=Lockout Duration
acc_gapb_devOfflineRule=If the device is offline
acc_gapb_standardLevel=Standard Access Level
acc_gapb_accessDenied=Access Denied
acc_gapb_doorControlZone=The following doors control access in and out of the zone
acc_gapb_resetStatus=Reset Anti-Passback Status
acc_gapb_obeyAPB=Obey Anti-Passback rules
acc_gapb_isResetGAPB=Reset the Anti-Passback
acc_gapb_resetGAPBSuccess=Reset Anti-Passback state success
acc_gapb_resetGAPBFaile=Reset Anti-Passback state fails
acc_gapb_chooseArea=Please re-select the zone.
acc_gapb_notDelInfo1=The access zone is referenced as the superior access area and cannot be deleted.
acc_gapb_notDelInfo2=The zone is referenced by Reader Define and cannot be deleted.
acc_gapb_notDelInfo3=The zone is referenced by advanced access control functions and cannot be deleted.
acc_gapb_notDelInfo4=The access area is referenced by the LED and cannot be deleted!
acc_gapb_zoneNumRepeat=The control domain numbers have duplicates
acc_gapb_zoneNameRepeat=The control domain names have duplicates
acc_gapb_personResetGapbPre=Are you sure to reset this?
acc_gapb_personResetGapbSuffix=rules of person Anti-Passback?
acc_gapb_apbPrompt=A single door cannot be used to control two relatively independent perimeter boundaries.
acc_gapb_occurApb=Anti-Passback occurs
acc_gapb_noOpenDoor=Do not open the door
acc_gapb_openDoor=Open the door
acc_gapb_zoneNumLength=Length greater than 20 characters
acc_gapb_zoneNameLength=Length greater than 30 characters
acc_gapb_zoneRemarkLength=Length greater than 50 characters
acc_gapb_isAutoServerMode=Device without background verification function is detected, which may affect the function. Open it now?
acc_gapb_applyTo=Apply to
acc_gapb_allPerson=All Personnel
acc_gapb_justSelected=Just Selected Personnel
acc_gapb_excludeSelected=Exclude Selected Personnel
#[G4]who is inside
acc_zoneInside_lastAccessTime=Last Access Time
acc_zoneInside_lastAccessReader=Last Access Reader
acc_zoneInside_noPersonInZone=No person in the zone
acc_zoneInside_noRulesInZone=No set rules.
acc_zoneInside_totalPeople=Total People
acc_zonePerson_selectPerson=Please select a person or department!
#[G5]路径
acc_route_name=Route name
acc_route_setting=Route Setting
acc_route_addReader=Add Reader
acc_route_delReader=Delete Reader
acc_route_defineReaderLine=Define reader line
acc_route_up=Up
acc_route_down=Down
acc_route_selReader=Select reader after the operation.
acc_route_onlyOneOper=Can only choose one reader to operate.
acc_route_readerOrder=Reader defined sequence
acc_route_atLeastSelectOne=Please select at least one reader!
acc_route_routeIsExist=This route already exists!
#[G6]DMR
acc_dmr_residenceTime=Residence time
acc_dmr_setting=DMR setting
#[G7]Occupancy
acc_occupancy_max=Maximum Capacity
acc_occupancy_min=Minimum Capacity
acc_occupancy_unlimit=Unlimited
acc_occupancy_note=The maximum capacity should be greater than the minimum capacity!
acc_occupancy_containNote=Please set at least one of the maximum/minimum capacity!
acc_occupancy_maxMinValid=Please set the capacity greater than 0.
acc_occupancy_conflict=The occupancy control rules has been set in this zone.
acc_occupancy_maxMinTip=No capacity value means no limitation.
#card availability
acc_personLimit_zonePropertyName=Zone property name
acc_personLimit_useType=Use
acc_personLimit_userDate=Valid Date
acc_personLimit_useDays=After the first use of valid days
acc_personLimit_useTimes=Use number of times
acc_personLimit_setZoneProperty=Set Zone Properties
acc_personLimit_zoneProperty=Zone Properties
acc_personLimit_availabilityName=Availability Name
acc_personLimit_days=Days
acc_personLimit_Times=Times
acc_personLimit_noDel=Selected access control zone properties are referenced and cannot be deleted!
acc_personLimit_cannotEdit=The access area attribute is referenced, cannot be modified!
acc_personLimit_detail=Detail
acc_personLimit_userDateTo=Valid Until
acc_personLimit_addPersonRepeatTip=The person under the selected department has been added to the attribute of the access control area, please select the department again!
acc_personLimit_leftTimes={0} Times Left
acc_personLimit_expired=expired
acc_personLimit_unused=Unused
#全局互锁
acc_globalInterlock_addGroup=Add Group
acc_globalInterlock_delGroup=Delete Group
acc_globalInterlock_refuseAddGroupMessage=The same interlock, door can not be duplicated within the group added
acc_globalInterlock_refuseAddlockMessage=The door added has appeared in other groups of interlocked devices
acc_globalInterlock_refuseDeleteGroupMessage=Please remove interlock-related data
acc_globalInterlock_isGroupInterlock=Group Interlock
acc_globalInterlock_isAddTheDoorImmediately=Add the door immediately
acc_globalInterlock_isAddTheGroupImmediately=Add the group immediately
#门禁参数设置
acc_param_autoEventDev=Auto download the event log of the number of concurrent devices
acc_param_autoEventTime=Auto download the event log of concurrent intervals
acc_param_noRepeat=No repetition of e-mail addresses is allowed, please fill in again.
acc_param_most18=Add up to 18 E-mail addresses
acc_param_deleteAlert=Cannot delete all email address input box.
acc_param_invalidOrRepeat=Email address format error or email address repetition.
#全局联动
acc_globalLinkage_noSupport=The currently selected door does not support lockdown and deactivate lockdown functions.
acc_globalLinkage_trigger=Trigger Global Linkage
acc_globalLinkage_noAddPerson=The linkage rule does not contain a personnel-related trigger, by default, applicable to all personnel!
acc_globalLinkage_selectAtLeastOne=Please select at least one linkage trigger operation!
acc_globalLinkage_selectTrigger=Please add linkage trigger conditions!
acc_globalLinkage_selectInput=Please add the input point!
acc_globalLinkage_selectOutput=Please add the output point!
acc_globalLinkage_audioRemind=Linkage Voice Prompts
acc_globalLinkage_audio=Linkage Voice
acc_globalLinkage_isApplyToAll=Apply to all personnel
acc_globalLinkage_scope=Personnel Range
acc_globalLinkage_everyPerson=Any
acc_globalLinkage_selectedPerson=Selected
acc_globalLinkage_noSupportPerson=Unsupported
acc_globalLinkage_reselectInput=The type of trigger conditions have changed, please re-select the input point!
acc_globalLinkage_addPushDevice=Please add support for this device function
#其他
acc_InputMethod_tips=Please switch to English input mode!
acc_device_systemCheckTip=Access device does not exist!
acc_notReturnMsg=No information returned
acc_validity_period=The license has passed the valid period; the function can't operate.
acc_device_pushMaxCount=System already exists in {0} device(s), reach license limit, can't add the device!
acc_device_videoHardwareLinkage=Set Video Hardware Linkage
acc_device_videoCameraIP=Video Camera IP
acc_device_videoCameraPort=Video Camera Port
acc_location_unable=The incident point was not added to the electronic map, unable to locate the specific location!
acc_device_wgDevMaxCount=The device has reached the license limit in the system and cannot modify the settings!
#自定义报警事件
acc_deviceEvent_selectSound=Please select the audio files.
acc_deviceEvent_batchSetSoundErr=Batch set alarm sounds abnormal.
acc_deviceEvent_batchSet=Set Audio
acc_deviceEvent_sound=Event Sound
acc_deviceEvent_exist=Already Exists
acc_deviceEvent_upload=Upload
#查询门最近发生事件
acc_doorEventLatestHappen=Query the latest events from the door
#门禁人员信息
acc_pers_delayPassage=Extend Passage
#设备容量提示
acc_dev_usageConfirm=Current capacity in excess of 90% of the equipment
acc_dev_immediateCheck=Immediately check
acc_dev_inSoftware=In Software
acc_dev_inFirmware=In FirmWare
acc_dev_get=Get
acc_dev_getAll=Get All
acc_dev_loadError=Loading Failure
#Reader
acc_reader_inout=In/Out
acc_reader_lightRule=Lights Rules
acc_reader_defLightRule=Default Rule
acc_reader_encrypt=Encrypt
acc_reader_allReaderOfCurDev=All Readers in Current Device
acc_reader_tip1=The encryption is copied to all readers in current device!
acc_reader_tip2=ID mode option is only available for readers that support this feature!
acc_reader_tip3=The RS485 protocol type is copied to all readers in current device. The settings will take effect after the device restarts!
acc_reader_tip4=The option to hide some personnel information is copied to all readers of the same device by default!
acc_reader_commType=Communication Type
acc_reader_commAddress=Communication Address
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=Disabled
acc_readerComAddress_repeat=Duplicate communication address
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=RS485 Address
acc_readerCommType_wgAddress=Wiegand Address
acc_reader_macError=Please enter the Mac address in the correct format!
acc_reader_machineType=Reader Type
acc_reader_readMode=Mode
acc_reader_readMode_normal=Normal Mode
acc_reader_readMode_idCard=ID Card Mode
acc_reader_note=Tips: Only access control devices in the same area ({0}) as the camera can be selected.
acc_reader_rs485Type=RS485 Protocol Type
acc_reader_userLock=Personnel Access Lock
acc_reader_userInfoReveal=Conceal Part Personnel Information
#operat
acc_operation_pwd=Operation Password
acc_operation_pwd_error=Password Error
acc_new_input_not_same=New key input is not consistent
acc_op_set_keyword=License key settings
acc_op_old_key=Old Key
acc_op_new_key=New Key
acc_op_cofirm_key=Confirm Key
acc_op_old_key_error=Old key error
#验证方式规则
acc_verifyRule_name=Rule Name
acc_verifyRule_door=Door Verification
acc_verifyRule_person=Personnel Verification
acc_verifyRule_copy=Copy Monday's Setting to Others Weekdays:
acc_verifyRule_tip1=Please select at least one verification mode!
acc_verifyRule_tip2=If the rule contains a person's verification mode, you can only add a door with a Wiegand reader!
acc_verifyRule_tip3=RS485 reader can only follow the door verification mode, does not support the personnel verification mode.
acc_verifyRule_oldVerifyMode=Old Verify Mode
acc_verifyRule_newVerifyMode=New Verify Mode
acc_verifyRule_newVerifyModeSelectTitle=Select new verification method
acc_verifyRule_newVerifyModeNoSupportTip=There is no device that supports the new verification method!
#Wiegand Test
acc_wiegand_beforeCard=The length of the new card({0} bits) doesn't equal to the prior card!
acc_wiegand_curentCount=Current Card Number Length : {0} Bits
acc_wiegand_card=Card
acc_wiegand_readCard=Read Card
acc_wiegand_clearCardInfo=Clear Card Information
acc_wiegand_originalCard=Original Card Number
acc_wiegand_recommendFmt=Recommended Card Format
acc_wiegand_parityFmt=Odd-Even Parity Format
acc_wiegand_withSizeCode=Auto calculate site code while the site code is left blank
acc_wiegand_tip1=These cards may not belong to the same batch of cards.
acc_wiegand_tip2=Site code:{0},Card number:{1}, failed to match in the original card number. Please check the inputted site code and card number again!
acc_wiegand_tip3=The inputted card number ({0}) cannot be matched in the original card number. Please check again!
acc_wiegand_tip4=The inputted site code ({0}) cannot be matched in the original card number. Please check again!
acc_wiegand_tip5=If you want to use this feature, please keep all the site code columns empty!
acc_wiegand_warnInfo1=When you continue to read a new card, please manually switch to the next card.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Personnel In/Out Board
acc_LCDRTMonitor_current=Current Personnel Information
acc_LCDRTMonitor_previous=Previous Personnel Information
#api
acc_api_levelIdNotNull=Access level ID cannot be empty
acc_api_levelExist=Access level does exist
acc_api_levelNotExist=Access level does not exist
acc_api_areaNameNotNull=Access area cannot be empty
acc_api_levelNotHasPerson=No people under the access level
acc_api_doorIdNotNull=Door ID cannot be empty
acc_api_doorNameNotNull=Door name cannot be empty
acc_api_doorIntervalSize=Door open duration should  between 1~254
acc_api_doorNotExist=Door does not exist
acc_api_devOffline=Device is offline or disabled
acc_api_devSnNotNull=Device SN cannot be empty
acc_api_timesTampNotNull=The timestamp cannot be empty
acc_api_openingTimeCannotBeNull=The door opening time cannot be empty
acc_api_parameterValueCannotBeNull=The parameter value cannot be empty
acc_api_deviceNumberDoesNotExist=Device serial number does not exist
acc_api_readerIdCannotBeNull=Reader ID cannot be null
acc_api_theReaderDoesNotExist=Reader does not exist
acc_operate_door_notInValidDate=Currently not within the effective time of remote opening, please contact the administrator if necessary!
acc_api_doorOffline=Door is offline or disabled
#门禁信息自动导出
acc_autoExport_title=Transactions Auto-Export
acc_autoExport_frequencyTitle=Auto-Export Frequency
acc_autoExport_frequencyDay=By Day
acc_autoExport_frequencyMonth=By Month
acc_autoExport_firstDayMonth=First day of the month
acc_autoExport_specificDate=Specific Date
acc_autoExport_exportModeTitle=Export Mode
acc_autoExport_dailyMode=Daily Transactions
acc_autoExport_monthlyMode=Monthly Transactions(All transactions between date of last month and this month)
acc_autoExport_allMode=All Data(export up to 30000 pieces of data)
acc_autoExport_recipientMail=Recipient's Mailbox
#First In And Last Out
acc_inOut_inReaderName=First in-Reader Name
acc_inOut_firstInTime=First in Time
acc_inOut_outReaderName=Last Out-Reader Name
acc_inOut_lastOutTime=Last Out Time
#防疫参数
acc_dev_setHep=Set Mask&Temperature Detection Parameters
acc_dev_enableIRTempDetection=Enable Temperature Screening With Infrared
acc_dev_enableNormalIRTempPass=Deny Access When Temperature Over The Range
acc_dev_enableMaskDetection=Enable Mask Detection
acc_dev_enableWearMaskPass=Deny Access Without Mask
acc_dev_tempHighThreshold=High Temperature Alarm Threshold
acc_dev_tempUnit=Temperature Unit
acc_dev_tempCorrection=Temperature Deviation Correction
acc_dev_enableUnregisterPass=Allow Unregistered People to Access
acc_dev_enableTriggerAlarm=Trigger External Alarm
#联动邮件
acc_mail_temperature=Body temperature
acc_mail_mask=Whether to wear the mask
acc_mail_unmeasured=Unmeasured
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort Global Events
acc_digifort_chooseDigifortEvents=Choose Digifort Global Events
acc_digifort_eventExpiredTip=If the global event is deleted from Digifort server, it will be in red.
acc_digifort_checkConnection=Please check if the connection information of Digifort server is correct.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Add Contacts
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=Set Extended Parameters
acc_extendParam_faceUI=Interface display
acc_extendParam_faceParam=Face Parameters
acc_extendParam_accParam=Access Control Parameters
acc_extendParam_intercomParam=Visual Intercom Parameters
acc_extendParam_volume=Volume
acc_extendParam_identInterval=Identification interval (ms)
acc_extendParam_historyVerifyResult=Display historical verification results
acc_extendParam_macAddress=Display MAC address
acc_extendParam_showIp=Show IP address
acc_extendParam_24HourFormat=Show 24-hour format
acc_extendParam_dateFormat=Date format
acc_extendParam_1NThreshold=1: N threshold
acc_extendParam_facePitchAngle=Pitch angle of face
acc_extendParam_faceRotationAngle=Face rotation angle
acc_extendParam_imageQuality=Image quality
acc_extendParam_miniFacePixel=Minimum face pixel
acc_extendParam_biopsy=Enable biopsy
acc_extendParam_showThermalImage=Show thermal image
acc_extendParam_attributeAnalysis=Enable attribute analysis
acc_extendParam_temperatureAttribute=Temperature detection attribute
acc_extendParam_maskAttribute=Mask detection attribute
acc_extendParam_minTemperature=Min temperature
acc_extendParam_maxTemperature=Max temperature
acc_extendParam_gateMode=Gate Mode
acc_extendParam_qrcodeEnable=Enable QR code function
#可视对讲
acc_dev_intercomServer=Visual intercom service address
acc_dev_intercomPort=Visual intercom server
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Synchronize Level
# 夏令时名称
acc_dsTimeUtc_none=Not set
acc_dsTimeUtc_AreaNone=No DST for this Time Zone
acc_dsTimeUtc1000_0=Canberra, Melbourne, Sydney
acc_dsTimeUtc1000_1=Hobart
acc_dsTimeUtc_0330_0=Newfoundland
acc_dsTimeUtc_1000_0=Aleutian Islands
acc_dsTimeUtc_0200_0=Mid Atlantic - used
acc_dsTimeUtc0930_0=Adelaide
acc_dsTimeUtc_0100_0=Azores
acc_dsTimeUtc_0400_0=Atlantic time (Canada)
acc_dsTimeUtc_0400_1=Santiago
acc_dsTimeUtc_0400_2=Asuncion
acc_dsTimeUtc_0300_0=Greenland
acc_dsTimeUtc_0300_1=Saint Pierre and Miquelon Islands
acc_dsTimeUtc0200_0=Chisinau
acc_dsTimeUtc0200_1=Helsinki, Kiev, Riga, Sofia, Tallinn, Vilnius
acc_dsTimeUtc0200_2=Athens, Bucharest
acc_dsTimeUtc0200_3=Jerusalem
acc_dsTimeUtc0200_4=Amman
acc_dsTimeUtc0200_5=Beirut
acc_dsTimeUtc0200_6=Damascus
acc_dsTimeUtc0200_7=Gaza, Hebron
acc_dsTimeUtc0200_8=Juba
acc_dsTimeUtc_0600_0=Central time (USA and Canada)
acc_dsTimeUtc_0600_1=Guadalajara, Mexico City, Monterrey
acc_dsTimeUtc_0600_2=Easter Island
acc_dsTimeUtc1300_0=Samoa Islands
acc_dsTimeUtc_0500_0=Havana 
acc_dsTimeUtc_0500_1=Eastern time (United States and Canada)
acc_dsTimeUtc_0500_2=Haiti
acc_dsTimeUtc_0500_3=Indiana (East)
acc_dsTimeUtc_0500_4=Turks and Caicos Islands 
acc_dsTimeUtc_0800_0=Pacific time (USA and Canada)
acc_dsTimeUtc_0800_1=Baja California
acc_dsTimeUtc0330_0=teheran or tehran
acc_dsTimeUtc0000_0=Dublin, Edinburgh, Lisbon, London
acc_dsTimeUtc1200_0=Fiji
acc_dsTimeUtc1200_1=Petropavlovsk Kamchatka old
acc_dsTimeUtc1200_2=Wellington, Auckland
acc_dsTimeUtc1100_0=Norfolk Island
acc_dsTimeUtc_0700_0=Chihuahua, La Paz, Mazatlan
acc_dsTimeUtc_0700_1=Mountain time (USA and Canada)
acc_dsTimeUtc0100_0=Belgrade, Bratislava, Budapest, Ljubljana, Prague
acc_dsTimeUtc0100_1=Sarajevo, Skopje, Warsaw, Zagreb
acc_dsTimeUtc0100_2=Casablanca
acc_dsTimeUtc0100_3=Brussels, Copenhagen, Madrid, Paris
acc_dsTimeUtc0100_4=Amsterdam, Berlin, Bern, Rome, Stockholm, Vienna
acc_dsTimeUtc_0900_0=Alaska
#安全点(muster point)
acc_leftMenu_accMusterPoint=Muster Point
acc_musterPoint_activate=Activate
acc_musterPoint_addDept=Add Department
acc_musterPoint_delDept=Delete Department
acc_musterPoint_report=Muster Point Report
acc_musterPointReport_sign=Sign In Manually
acc_musterPointReport_generate=Generate Reports
acc_musterPoint_addSignPoint=Add Sign Point
acc_musterPoint_delSignPoint=Delete Sign Point
acc_musterPoint_selectSignPoint=Please add a sign point!
acc_musterPoint_signPoint=Sign Point
acc_musterPoint_delFailTip=There are already activated muster points and cannot be deleted!
acc_musterPointReport_enterTime=Enter Time
acc_musterPointReport_dataAnalysis=Data Analysis
acc_musterPointReport_safe=Safe
acc_musterPointReport_danger=Danger
acc_musterPointReport_signInManually=manual punch
acc_musterPoint_editTip=The muster point is active and cannot be edited!
acc_musterPointEmail_total=Expected attendance :
acc_musterPointEmail_safe=Checked in (safe) :
acc_musterPointEmail_dangerous=In danger :
acc_musterPoint_messageNotification=Message notification on activation
acc_musterPointReport_sendEmail=Scheduled push report
acc_musterPointReport_sendInterval=Send Interval
acc_musterPointReport_sendTip=Please ensure that the selected notification method is configured successfully, otherwise the notification will not be sent normally!
acc_musterPoint_mailSubject=Emergency assembly notice
acc_musterPoint_mailContent=Please gather at "{0}" immediately and sign in at the "{1}" device, thank you!
acc_musterPointReport_mailHead=Hi, This is an emergency assenbly report. Please review.
acc_musterPoint_visitorsStatistics=Statistics of visitors
# 报警监控
acc_alarm_priority=Priority
acc_alarm_total=In total
acc_alarm_today=Today's Record
acc_alarm_unhandled=Unconfirmed
acc_alarm_inProcess=Processing
acc_alarm_acknowledged=Confirmed
acc_alarm_top5=Top 5 alarm events
acc_alarm_monitoringTime=Monitoring time
acc_alarm_history=Alarm Processing History
acc_alarm_acknowledgement=Processing Records
acc_alarm_eventDescription=Event Details
acc_alarm_acknowledgeText=After selection, the alarm event details email will be sent to the assigned mailbox.
acc_alarm_emailSubject=Add Processing Record
acc_alarm_mute=Mute
acc_alarm_suspend=Suspend
acc_alarm_confirmed=The event has been confirmed.
acc_alarm_list=Alarm Log
#ntp
acc_device_setNTPService=NTP server settings
acc_device_setNTPServiceTip=Enter multiple server addresses, separated by commas (,) or semicolons (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=In access controller operation, a Super user is not restricted by the regulations on time zones, anti-passback and interlock and has extremely high door-opening priority.
acc_editPerson_delayPassageTip=Extend the waiting time for the personnel through the access points. Suitable for physically challenged or people with other disabilities.
acc_editPerson_disabledTip=Temporarily disable the personnel’s access level.
#门禁向导
acc_guide_title=Access Control Module Setup Wizard
acc_guide_addPersonTip=You need to add the person and the corresponding credentials (face or fingerprint or card or palm or password); if you have already added, skip this step directly
acc_guide_timesegTip=Please configure a valid opening time period
acc_guide_addDeviceTip=Please add the corresponding device as the access point
acc_guide_addLevelTip=Add access control level
acc_guide_personLevelTip=Assign the corresponding access control authority to the person
acc_guide_rtMonitorTip=Check access control records in real time
acc_guide_rtMonitorTip2=Real time viewing of access control records after adding the corresponding area and door
#查看区域内人员
acc_zonePerson_cleanCount=Clear the statistics of people entering and leaving
acc_zonePerson_inCount=Statistics of the number of people entering
acc_zonePerson_outCount=Statistics of the number of people leaving
#biocv460
acc_device_validFail=The username or password is incorrect and the verification fails!
acc_en_US.properties=Can only input a maximum of 6 integers