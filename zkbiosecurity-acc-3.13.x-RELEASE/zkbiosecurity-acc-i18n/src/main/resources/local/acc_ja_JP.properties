#系统名称
acc_module=アクセス
acc_leftMenu_accDev=アクセスデバイス
acc_leftMenu_auxOut=補助出力
acc_leftMenu_dSTime=サマータイム
acc_leftMenu_access=アクセスコントロール
acc_leftMenu_door=ドア
acc_leftMenu_accRule=アクセスルール
acc_leftMenu_interlock=インターロック
acc_leftMenu_antiPassback=アンチ・パスバック
acc_leftMenu_globalLinkage=グローバルリンケージ
acc_leftMenu_firstOpen=ファーストユーザー解錠
acc_leftMenu_combOpen=マルチパーソン
acc_leftMenu_personGroup=マルチパーソングループ
acc_leftMenu_level=アクセスレベル
acc_leftMenu_electronicMap=マップ
acc_leftMenu_personnelAccessLevels=パーソナルアクセスレベル
acc_leftMenu_searchByLevel=アクセスレベル別
acc_leftMenu_searchByDoor=ドアアクセス権限
acc_leftMenu_expertGuard=拡張機能
acc_leftMenu_zone=ゾーン
acc_leftMenu_readerDefine=リーダ定義
acc_leftMenu_gapbSet=グローバルアンチ・パスバック
acc_leftMenu_whoIsInside=在室管理
acc_leftMenu_whatRulesInside=ゾーンに設定されたルール画面
acc_leftMenu_occupancy=占有制御
acc_leftMenu_route=ルート制御
acc_leftMenu_globalInterlock=グローバルインターロック
acc_leftMeue_globalInterlockGroup=グローバルインターロックグループ
acc_leftMenu_dmr=デッドマンルール
acc_leftMenu_personLimit=ユーザー可用性
acc_leftMenu_verifyModeRule=認証モード
acc_leftMenu_verifyModeRulePersonGroup=認証モードグループ
acc_leftMenu_extDev=I/O拡張ボード
acc_leftMenu_firstInLastOut=最初入り先と最後出し先
acc_leftMenu_accReports=アクセス制御レポート
#[3]门禁时间段
acc_timeSeg_entity=タイムゾーン
acc_timeSeg_canNotDel=タイムゾーンは使用されていて、削除できません！
#[4]门禁设备--公共的在common中
acc_common_ruleName=ルール名
acc_common_hasBeanSet=設定済
acc_common_notSet=未設定
acc_common_hasBeenOpened=解錠済
acc_common_notOpened=未解錠
acc_common_partSet=一部の設定
acc_common_linkageAndApbTip=リンケージとグローバル・リンケージ、アンチ・パスバック、グローバル・アンチ・パスバックを同時に設定すると、競合が発生する可能性があります。
acc_common_vidlinkageTip=対応する入力ポイントリンケージが利用可能なビデオチャンネルにバインドされていることを確認してください。未設定の場合、ビデオリンケージ機能が動作しません。
acc_common_accZoneFromTo=同じゾーンを設定できません。
acc_common_logEventNumber=イベントID
acc_common_bindOrUnbindChannel=カメラのバインド/バインド解除
acc_common_boundChannel=カメラがバインドされました
#设备信息
acc_dev_iconType=アイコンタイプ
acc_dev_carGate=パーキングゲート
acc_dev_channelGate=フラッパーゲート
acc_dev_acpType=コントローラタイプ
acc_dev_oneDoorACP=1ドアコントローラ
acc_dev_twoDoorACP=2ドアコントローラ
acc_dev_fourDoorACP=4ドアコントローラ
acc_dev_onDoorACD=スタンドアロンデバイス
acc_dev_switchToTwoDoorTwoWay=2ドア2方向変更
acc_dev_addDevConfirm2=チップ：デバイス接続は成功しましたが,アクセスデバイスタイプが異なるので{0}ドアコントローラに変更しますが、続行しますか？
acc_dev_addDevConfirm4=スタンドアロンデバイスを追加しますか？
acc_dev_oneMachine=スタンドアロンデバイス
acc_dev_fingervein=指静脈
acc_dev_control=コントローラ
acc_dev_protocol=プロトコルタイプ
acc_ownedBoard=使用済みボード
#设备操作
acc_dev_start=開始
acc_dev_accLevel=アクセス権限
acc_dev_timeZoneAndHoliday=タイムゾーン、休日
acc_dev_linkage=リンケージ
acc_dev_doorOpt=ドアパラメータ
acc_dev_firstPerson=ファーストユーザー解錠
acc_dev_multiPerson=マルチパーソン
acc_dev_interlock=インターロック
acc_dev_antiPassback=アンチ・パスバック
acc_dev_wiegandFmt=Wiegandフォーマット
acc_dev_outRelaySet=補助出力設定
acc_dev_backgroundVerifyParam=バックグラウンド認証オプション
acc_dev_getPersonInfoPrompt=ユーザー情報が正常に取得されていることを確認してください。それ以外の場合は例外が発生します。 続行しますか？
acc_dev_getEventSuccess=イベント取得成功
acc_dev_getEventFail=イベント取得失敗
acc_dev_getInfoSuccess=情報取得成功
acc_dev_getInfoXSuccess={0}取得成功
acc_dev_getInfoFail=情報取得失敗
acc_dev_updateExtuserInfoFail=延長通過ユーザー情報の更新に失敗しましたので、再試行してください。
acc_dev_getPersonCount=ユーザー数取得
acc_dev_getFPCount=指紋数取得
acc_dev_getFVCount=指静脈数取得
acc_dev_getFaceCount=顔数取得
acc_dev_getPalmCount=手のひら数取得
acc_dev_getBiophotoCount=顔写真数取得
acc_dev_noData=デバイスからのデータリターンがありません
acc_dev_noNewData=デバイスに新しい履歴がありません
acc_dev_softLtDev=ソフトウェアにはデバイスよりも多くのユーザーが存在します。
acc_dev_personCount=ユーザー数：
acc_dev_personDetail=詳細は次のとおりです：
acc_dev_softEqualDev=ソフトウェアとデバイスの両方のユーザー数は同じです。
acc_dev_softGtDev=デバイスにはソフトウェアより多くのユーザーが存在します。
acc_dev_cmdSendFail=コマンド送信失敗、再試行してください
acc_dev_issueVerifyParam=バックグラウンド認証パラメータ
acc_dev_verifyParamSuccess=バックグラウンド認証パラメータ設定成功
acc_dev_backgroundVerify=バックグラウンド認証パラメータ
acc_dev_selRightFile=不正なアップグレードファイルが選択されました！
acc_dev_devNotOpForOffLine=デバイスはオフラインです。リトライしてください！
acc_dev_devNotSupportFunction=デバイスはこの機能をサポートしません
acc_dev_devNotOpForDisable=デバイスが無効です。再試行してください
acc_dev_devNotOpForNotOnline=デバイスがオフラインか無効です。再試行してください
acc_dev_getPersonInfo=ユーザー情報取得
acc_dev_getFPInfo=指紋情報取得
acc_dev_getFingerVeinInfo=指静脈情報取得
acc_dev_getPalmInfo=手のひら情報取得
acc_dev_getIrisInfo=虹彩情報の取得
acc_dev_getBiophotoInfo=顔写真情報取得
acc_dev_disable=無効です。再選択してください
acc_dev_offlineAndContinue=オフラインですが、続行しますか？
acc_dev_offlineAndSelect=オフライン
acc_dev_opAllDev=全てのデバイス
acc_dev_opOnlineDev=オフラインデバイス
acc_dev_opException=例外処理
acc_dev_exceptionAndConfirm=デバイス通信タイムアウトのため操作失敗しました。ネットワーク状況を確認してください
acc_dev_getFaceInfo=顔情報取得
acc_dev_selOpDevType=操作するデバイスタイプを選択してください
acc_dev_hasFilterByFunc=オンラインでデバイスを表示し、デバイスの機能をサポートします！
acc_dev_masterSlaveMode=RS485マスタースレーブモード
acc_dev_master=ホスト
acc_dev_slave=スレーブ
acc_dev_modifyRS485Addr=RS485アドレス編集
acc_dev_rs485AddrTip=1-63の整数を入力してください！
acc_dev_enableFeature=バックグラウンド認証有効デバイス
acc_dev_disableFeature=バックグラウンド認証無効デバイス
acc_dev_getCountOnly=数のみ取得
acc_dev_queryDevPersonCount=ユーザー数クエリ
acc_dev_queryDevVolume=デバイス容量確認
acc_dev_ruleType=ルールタイプ
acc_dev_contenRule=ルール詳細
acc_dev_accessRules=デバイスのルール確認
acc_dev_ruleContentTip=複数のルールは 「|」 で区切ってください。
acc_dev_rs485AddrFigure=RS485アドレスコード
acc_dev_addLevel=レベル追加
acc_dev_personOrFingerTanto=ユーザーまたは指紋の数が超過しており、同期に失敗しました...
acc_dev_personAndFingerUnit=（数）
acc_dev_setDstime=DST設定
acc_dev_setTimeZone=デバイスタイムゾーン設定
acc_dev_selectedTZ=タイムゾーン選択
acc_dev_timeZoneSetting=タイムゾーン設定中...
acc_dev_timeZoneCmdSuccess=タイムゾーンコマンド送信成功
acc_dev_enableDstime=DST有効
acc_dev_disableDstime=DST無効
acc_dev_timeZone=タイムゾーン
acc_dev_dstSettingTip=DST設定中...
acc_dev_dstDelTip=デバイスDST削除中..
acc_dev_enablingDst=DST有効
acc_dev_dstEnableCmdSuccess=有効DSTコマンド送信成功
acc_dev_disablingDst=無効DST
acc_dev_dstDisableCmdSuccess=無効DSTコマンド送信成功
acc_dev_dstCmdSuccess=DSTコマンド送信成功
acc_dev_usadst=DST
acc_dev_notSetDst=設定無し
acc_dev_selectedDst=DST選択済
acc_dev_configMasterSlave=マスタースレーブ設定
acc_dev_hasFilterByUnOnline=スタンドアロンデバイスのみ表示
acc_dev_softwareData=データがデバイスと一致していない場合は、クエリの前にデータを同期してください！
acc_dev_disabled=無効になったデバイスは操作できません！
acc_dev_offline=オフラインのデバイスは操作できません
acc_dev_noSupport=このデバイスはこの機能をサポートしていないため、操作できません！
acc_dev_noRegDevTip=このデバイスは登録デバイスとして定義されていません。 ソフトウェア内のデータが対象となります。 続行しますか？
acc_dev_noOption=適格なオプションはありません
acc_dev_devFWUpdatePrompt=現在のデバイスでは、通常、無効にされたユーザー、ユーザーの有効性機能は使用されません（ユーザーマニュアルを参照してください）。
acc_dev_panelFWUpdatePrompt=現在のデバイスは、通常、無効にされたユーザーとユーザーの有効性機能を使用しません、すぐにファームウェアをアップグレードしますか？
acc_dev_sendEventCmdSuccess=イベント削除コマンドが正常に送信されました
acc_dev_tryAgain=再試行してください
acc_dev_eventAutoCheckAndUpload=自動チェックとアップロードイベント
acc_dev_eventUploadStart=イベントアップロード開始
acc_dev_eventUploadEnd=イベントアップロード完了
acc_dev_eventUploadFailed=イベントアップロード失敗
acc_dev_eventUploadPrompt=デバイスファームウェアのバージョンが古すぎます。ファームウェアをアップグレードする前に、次の操作を行います：
acc_dev_backupToSoftware=データをソフトウェアにバックアップする
acc_dev_deleteEvent=古い履歴の削除
acc_dev_upgradePrompt=ファームウェアのバージョンが古すぎてエラーが発生する可能性があります。ファームウェアを更新する前に、ソフトウェアにデータをバックアップしてください。
acc_dev_conflictCardNo=他のユーザー{0}でカードが存在します！
acc_dev_rebootAfterOperate=操作成功、デバイスは後で再起動します。
acc_dev_baseOptionTip=基本パラメータ異常
acc_dev_funOptionTip=関数パラメータ異常
acc_dev_sendComandoTip=デバイスパラメータコマンドの送信に失敗しました。
acc_dev_noC3LicenseTip=このタイプのデバイス ({0})は追加できません! 販売店に連絡してください.
acc_dev_combOpenDoorTip=マルチパーソンが設定されているので、バックグラウンド検証と同時に設定することはできません。
acc_dev_combOpenDoorPersonCountTip=グループ{0}は{1}を超えて開くことはできません！
acc_dev_addDevTip=通信プロトコルがPULLであるデバイスを追加する場合にのみ適用されます。
acc_dev_addError=デバイス追加例外、パラメータが欠落しています ({0})!
acc_dev_updateIPAndPortError=サーバIPとポートアップデートエラー...
acc_dev_transferFilesTip=ファームウェアテストが完了したので、ファイルを転送します
acc_dev_serialPortExist=シリアルポートが存在します
acc_dev_isExist=デバイスを追加
acc_dev_description=詳細
acc_dev_searchEthernet=イーサネットデバイス検索
acc_dev_searchRS485=RS485デバイス検索
acc_dev_rs485AddrTip1=RS485の開始アドレスは終了アドレスより大きくすることはできません
acc_dev_rs485AddrTip2=RS485の検索範囲は２０未満でなければなりません
acc_dev_clearAllCmdCache=全コマンドクリア
acc_dev_authorizedSuccessful=承認成功
acc_dev_authorize=承認
acc_dev_registrationDevice=デバイス登録
acc_dev_setRegistrationDevice=登録機として設定
acc_dev_mismatchedDevice=このデバイスは使用できません。 販売店にデバイスのシリアル番号を確認してください。
acc_dev_pwdStartWithZero=通信パスワードの先頭は0にできません！
acc_dev_maybeDisabled=現在のライセンスではさらに{0}ドア追加することができますが、ライセンス数を超えるドアを追加することはできません
acc_dev_Limit=ライセンス数の上限に達しましたので、ドア追加するにはアップグレードライセンスを購入してください
acc_dev_selectDev=デバイスを選択してください
acc_dev_cannotAddPullDevice=PULLデバイスを追加できません。販売店にご相談ください
acc_dev_notContinueAddPullDevice=システムに{0}のPULL デバイスが存在するので追加できません! 販売店にご相談ください。
acc_dev_deviceNameNull=デバイスモデルがnullで、デバイスを追加できません！
acc_dev_commTypeErr=通信タイプが一致せず、デバイスを追加できません！
acc_dev_inputDomainError=有効なドメインアドレスを入力してください
acc_dev_levelTip=レベルには５０００人以上のユーザーがいますが、ここにデバイスを追加することはできません。
acc_dev_auxinSet=補助入力設定
acc_dev_verifyModeRule=認証モードルール
acc_dev_netModeWired=有線
acc_dev_netMode4G=4G
acc_dev_netModeWifi=Wifi
acc_dev_updateNetConnectMode=ネットワーク接続切り替え
acc_dev_wirelessSSID=ウィアレスSSID
acc_dev_wirelessKey=ウィアレスキー
acc_dev_searchWifi=WIFI検索
acc_dev_testNetConnectSuccess=接続は成功しましたか？
acc_dev_testNetConnectFailed=接続が正常に通信できません！
acc_dev_signalIntensity=電波強度
acc_dev_resetSearch=再検索
acc_dev_addChildDevice=サブデバイス追加
acc_dev_modParentDevice=マスターデバイス変更
acc_dev_configParentDevice=マスターデバイス設定
acc_dev_lookUpChildDevice=スレーブデバイスの表示
acc_dev_addChildDeviceTip=承認は承認されたサブデバイスの下で実行される必要があります
acc_dev_maxSubCount=許可されたサブデバイスの数が最大数{0}個を超えています。
acc_dev_seletParentDevice=マスターデバイスを選択して下さい！
acc_dev_networkCard=ネットワークカード
acc_dev_issueParam=カスタム同期パラメータ
acc_dev_issueMode=同期モード
acc_dev_initIssue=ファームウェアバージョン３０３０初期化データ
acc_dev_customIssue=カスタム同期データ
acc_dev_issueData=データ
acc_dev_parent=マスターデバイス
acc_dev_parentEnable=マスターデバイスが無効です。
acc_dev_parentTips=マスターデバイスを連動すると、デバイス内のすべてのデータが削除されるため、再度設定する必要があります。
acc_dev_addDevIpTip=新しいIPアドレスをサーバのIPアドレスと同じにすることはできません
acc_dev_modifyDevIpTip=新しいサーバアドレスをデバイスのIPアドレスと同じにすることはできません
acc_dev_setWGReader=Wiegandリーダ設定
acc_dev_selectReader=クリックしてリーダを選択
acc_dev_IllegalDevice=不正デバイス
acc_dev_syncTimeWarnTip=次のデバイスの同期時刻は、マスターデバイス上で同期させる必要があります。
acc_dev_setTimeZoneWarnTip=次のデバイスのタイムゾーンは、マスターデバイス上で同期させる必要があります。
acc_dev_setDstimeWarnTip=次のデバイスのDSTは、マスターデバイス上で同期させる必要があります。
acc_dev_networkSegmentSame=2つのネットワークアダプタは同じネットワークセグメントを使用できません。
acc_dev_upgradeProtocolNoMatch=アップグレードファイルのプロトコルが一致しません
acc_dev_ipAddressConflict=同じIPアドレスを持つデバイスが既に存在します。デバイスのIPアドレスを変更して、もう一度追加してください。
acc_dev_checkServerPortTip=設定されたサーバーポートがシステム通信ポートと一致しないため、追加に失敗する可能性があります。続行しますか？
acc_dev_clearAdmin=管理者権限のクリア
acc_dev_setDevSate=デバイスの入/出状態を設定する
acc_dev_sureToClear=デバイス管理者権限を消去しますか？
acc_dev_hostState=マスターデバイスステータス
acc_dev_regDeviceTypeTip=直接追加することはできません。販売店に連絡してください！
acc_dev_extBoardType=拡張ボードタイプ
acc_dev_extBoardTip=設定を有効にするには、デバイスを再起動する必要があります！
acc_dev_extBoardLimit=拡張ボードは、デバイスごとに２つだけ使用できます！
acc_dev_replace=デバイスの交換
acc_dev_replaceTip=交換後、古いデバイスは動作しなくなりますのでご注意ください!
acc_dev_replaceTip1=交換後、「すべてのデータを同期」操作を実行してください。
acc_dev_replaceTip2=交換用デバイスのモデルが同じであることを確認してください!
acc_dev_replaceTip3=交換用デバイスが古いデバイスと同じサーバー アドレスとポートを設定していることを確認してください。
acc_dev_replaceFail=デバイスのマシンタイプが矛盾しているため、交換できません。
acc_dev_notApb=このデバイスはゲートまたはリードヘッドの対潜ができません
acc_dev_upResourceFile=リソースファイルのアップロード
acc_dev_playOrder=再生順序
acc_dev_setFaceServerInfo=顔後端比較パラメータの設定
acc_dev_faceVerifyMode=顔比較モード
acc_dev_faceVerifyMode1=ローカル比較
acc_dev_faceVerifyMode2=バックエンド比較
acc_dev_faceVerifyMode3=ローカル比較優先度
acc_dev_faceBgServerType=顔バックエンドサーバタイプ
acc_dev_faceBgServerType1=ソフトウェアプラットフォームサービス
acc_dev_faceBgServerType2=サードパーティ製サービス
acc_dev_isAccessLogic=アクセス制御論理検証の有効化
#[5]门-其他关联的也复用此处
acc_door_entity=ドア
acc_door_number=ドア番号
acc_door_name=ドア名
acc_door_activeTimeZone=有効タイムゾーン
acc_door_passageModeTimeZone=自動解錠タイムゾーン
acc_door_setPassageModeTimeZone=自動解錠タイムゾーン設定
acc_door_notPassageModeTimeZone=非自動解錠タイムゾーン
acc_door_lockOpenDuration=解錠時間
acc_door_entranceApbDuration=アンチ・パスバック期間
acc_door_sensor=ドアセンサー
acc_door_sensorType=ドアセンサータイプ
acc_door_normalOpen=常時開
acc_door_normalClose=ノーマルクローズ
acc_door_sensorDelay=ドアセンサー遅延
acc_door_closeAndReverseState=閉扉後施錠
acc_door_hostOutState=ホストアクセスステータス
acc_door_slaveOutState=スレーブステート
acc_door_inState=入
acc_door_outState=出
acc_door_requestToExit=REXモード
acc_door_withoutUnlock=施錠
acc_door_unlocking=解錠
acc_door_alarmDelay=REX遅延
acc_door_duressPassword=非常パスワード
acc_door_currentDoor=現在のドア
acc_door_allDoorOfCurDev=現在のデバイスの全ドア
acc_door_allDoorOfAllDev=全デバイスの全ドア
acc_door_allDoorOfAllControlDev=全てのデバイスの全ドア
acc_door_allDoorOfAllStandaloneDev=全スタンドアロンデバイスの全ドア
acc_door_allWirelessLock=全ワイヤレスロック
acc_door_max6BitInteger=最大６ビット整数
acc_door_direction=説明
acc_door_onlyInReader=入側リーダのみ
acc_door_bothInAndOutReader=入出リーダ
acc_door_noDoor=ドアを追加してください
acc_door_nameRepeat=ドア名が重複しています。
acc_door_duressPwdError=非常パスワードは、ユーザーパスワードと同じにできません
acc_door_urgencyStatePwd={0}ビット整数を入力してください!
acc_door_noDevOnline=オンラインのデバイスがないか、ドアがカード認証モードをサポートしていません。
acc_door_durationLessLock=ドアセンサー遅延は、解錠時間より長くなければなりません。
acc_door_lockMoreDuration=解錠時間は、ドアセンサー遅延より短くなくてはなりません。
acc_door_lockAndExtLessDuration=解錠時間と通過時間の合計は、ドアセンサー遅延より短くなくてはなりません
acc_door_noDevTrigger=デバイスは条件を満たしていません！
acc_door_relay=リレー
acc_door_pin=ID
acc_door_selDoor=ドア選択
acc_door_sensorStatus=ドアセンサー（{0}秒）
acc_door_sensorDelaySeconds=ドアセンサー遅延（{0}秒）
acc_door_timeSeg=タイムゾーン（{0}秒）
acc_door_combOpenInterval=マルチパーソン認証間隔
acc_door_delayOpenTime=開扉遅延
acc_door_extDelayDrivertime=解錠遅延
acc_door_enableAudio=アラーム有効
acc_door_disableAudio=アラーム無効
acc_door_lockAndExtDelayTip=解錠時間と通過間隔遅延の合計は、２５４秒以下にしてください
acc_door_disabled=無効ドアは操作できません！
acc_door_offline=オフラインドアは操作できません！
acc_door_notSupport=次のドアは、この機能をサポートしていません！
acc_door_select=ドアを選択してください
acc_door_pushMaxCount=システムに{0}のドアが設定され上限に達しました。販売店にご相談ください。
acc_door_outNumber=現在のライセンスでは{0}のドア(s)を追加できますので、ドアを再選択して再試行するか、販売店にご相談ください。
acc_door_latchTimeZone=REXタイムゾーン
acc_door_wgFmtReverse=カード番号反転
acc_door_allowSUAccessLock=施錠期間でスーパーユーザーが通過できます。
acc_door_verifyModeSinglePwd=パスワードは独立した認証方式としては使用できません！
acc_door_doorPassword=オープンパスワード
#辅助输入
acc_auxIn_timeZone=有効タイムゾーン
#辅助输出
acc_auxOut_passageModeTimeZone=自動解錠タイムゾーン
acc_auxOut_disabled=無効補助出力は操作できません！
acc_auxOut_offline=オフライン補助出力は操作できません！
#[8]门禁权限组
acc_level_doorGroup=ドアコンビネーション
acc_level_openingPersonnel=解錠ユーザー
acc_level_noDoor=利用可能なアイテムがありません。最初にデバイスを追加してください
acc_level_doorRequired=ドアを選択する必要があります
acc_level_doorCount=選択中ドア
acc_level_doorDelete=ドア削除
acc_level_isAddDoor=すぐに選択中のアクセスレベルにドアを追加しますか？
acc_level_master=マスター
acc_level_noneSelect=アクセスレベルを追加してください。
acc_level_useDefaultLevel=アクセスレベルを新しい部署に切り替えますか？
acc_level_persAccSet=ユーザーアクセス設置
acc_level_visUsed={0}がビジターモジュールで使用されました、削除できません！
acc_level_doorControl=ドア管理
acc_level_personExceedMax=現在のパーミッショングループ数（{0}）は、オプションのパーミッショングループ数が最大({1})です。
acc_level_exportLevel=エクスポートアクセスレベル
acc_level_exportLevelDoor=アクセスレベルのドアをエクスポート
acc_level_exportLevelPerson=アクセスレベルの担当者のエクスポート
acc_level_importLevel=インポートアクセスレベル
acc_level_importLevelDoor=アクセスレベルのドアのインポート
acc_level_importLevelPerson=アクセスレベルの人員のインポート
acc_level_exportDoorFileName=ドアアクセスレベルの情報
acc_level_exportPersonFileName=アクセスレベルの個人情報
acc_levelImport_nameNotNull=アクセスレベル名を空にすることはできません
acc_levelImport_timeSegNameNotNull=タイムゾーンを空にすることはできません
acc_levelImport_areaNotExist=エリアが存在しません！
acc_levelImport_timeSegNotExist=タイムゾーンが存在しません！
acc_levelImport_nameExist=アクセスレベル名{0}はすでに存在します！
acc_levelImport_levelDoorExist=アクセスレベル{0}のドアはすでに存在します！
acc_levelImport_levelPersonExist=アクセスレベル{0}の担当者はすでに存在します！
acc_levelImport_noSpecialChar=アクセス レベル名に特殊文字を含めることはできません!
#[10]首人常开
acc_firstOpen_setting=ファーストユーザー解錠
acc_firstOpen_browsePerson=ユーザー閲覧
#[11]多人组合开门
acc_combOpen_comboName=コンビネーション名
acc_combOpen_personGroupName=グループ名
acc_combOpen_personGroup=マルチパーソングループ
acc_combOpen_verifyOneTime=選択中ユーザー数
acc_combOpen_eachGroupCount=各グループのユーザー数
acc_combOpen_group=グループ
acc_combOpen_changeLevel=オープンドアグループ
acc_combOpen_combDeleteGroup=既存のコンビネーションオープンリファレンスは、最初に削除してください！
acc_combOpen_ownedLevel=所属グループ
acc_combOpen_mostPersonCount=グループのユーザーは、最大5人です！
acc_combOpen_leastPersonCount=グループのユーザーは、2人以上です！
acc_combOpen_groupNameRepeat=グループ名重複！
acc_combOpen_groupNotUnique=オープンドアユーザーグループは、同じにできません！
acc_combOpen_persNumErr=グループ選択数が、設定可能最大数を超えているので、再選択してください！
acc_combOpen_combOpengGroupPersonShort=ユーザー削除後、ユーザーオープングループ数が不足しますので、最初にグループを削除してください！
acc_combOpen_backgroundVerifyTip=ドアにバックグラウンド認証が設定されているので、マルチパーソンを設定することができません！
#[12]互锁
acc_interlock_rule=インターロックルール
acc_interlock_mode1Or2={0}と{1}間インターロック
acc_interlock_mode3={0}と{1}と{2}間インターロック
acc_interlock_mode4={0}と{1}または{2}と{3}間インターロック
acc_interlock_mode5={0}と{1}と{2}と{3}間インターロック
acc_interlock_hasBeenSet=インターロックが設定されました。
acc_interlock_group1=グループ1
acc_interlock_group2=グループ2
acc_interlock_ruleInfo=グループ間インタロック
acc_interlock_alreadyExists=同じインターロック規則が既に存在します。重複して追加しないでください！
acc_interlock_groupInterlockCountErr=グループ内の相互ロック規則には少なくとも2つのドアが必要です
acc_interlock_ruleType=相互ロック・ルール・タイプ
#[13]反潜
acc_apb_rules=アンチ・パスバックルール
acc_apb_reader=ドア{0}リーダ間アンチ・パスバック
acc_apb_reader2=ドア{0}/{1}リーダ間アンチ・パスバック
acc_apb_reader3=ドア{0}/{1}/{2}リーダ間アンチ・パスバック
acc_apb_reader4=ドア{0}/{1}/{2}/{3}リーダ間アンチ・パスバック
acc_apb_reader5=ドア{0}出場リーダアンチ・パスバック 
acc_apb_reader6=ドア{0}入場リーダアンチ・パスバック 
acc_apb_reader7=全４つドアリーダ間アンチ・パスバック
acc_apb_twoDoor={0}と{1}間アンチ・パスバック
acc_apb_fourDoor={0}と{1}または{2}と{3}間アンチ・パスバック
acc_apb_fourDoor2={0}/{1}と{2}/{3}間アンチ・パスバック
acc_apb_fourDoor3={0}と{1}/{2}間アンチ・パスバック
acc_apb_fourDoor4={0}と{1}/{2}/{3}間アンチ・パスバック
acc_apb_hasBeenSet=アンチ・パスバックが設定されました。
acc_apb_conflictWithGapb=デバイスにグローバルアンチ・パスバックが設定済みなので、ルールを設定できません！
acc_apb_conflictWithApb=デバイスのゾーンにはアンチ・パスバックが設定済みなので、ルールを設定できません！
acc_apb_conflictWithEntranceApb=デバイスのゾーンにはエントランスアンチ・パスバックが設定済みなので、ルールを設定できませんでした！
acc_apb_controlIn=入場アンチ・パスバック
acc_apb_controlOut=出場アンチ・パスバック
acc_apb_controlInOut=入場と出場アンチ・パスバック
acc_apb_groupIn=グループ化
acc_apb_groupOut=グループ化
acc_apb_reverseName=の逆潜り
acc_apb_door=ゲート対潜
acc_apb_readerHead=リードヘッド対潜
acc_apb_alreadyExists=同じ対潜ルールが既に存在しますので、繰り返し追加しないでください！
#[17]电子地图
acc_map_addDoor=全ドア
acc_map_addChannel=全カメラ
acc_map_noAccess=マップモジュール権限がありません、管理者に連絡してください！
acc_map_noAreaAccess=エリアマップモジュール権限がありません、管理者に連絡してください！
acc_map_imgSizeError=サイズが{0}M未満の画像をアップロードしてください！
#[18]门禁事件记录
acc_trans_entity=トランザクション
acc_trans_eventType=イベントタイプ
acc_trans_firmwareEvent=ファームウェアイベント
acc_trans_softwareEvent=ソフトウェアイベント
acc_trans_today=今日のイベント
acc_trans_lastAddr=最後の位置
acc_trans_viewPhotos=写真閲覧
acc_trans_exportPhoto=写真をエクスポートする
acc_trans_dayNumber=日々
acc_trans_photo=アクセス制御インシデント写真
acc_trans_fileIsTooLarge=エクスポートされたファイルが大きすぎます。エクスポートする範囲を減らしてください
#[19]门禁验证方式
acc_verify_mode_onlyface=顔
acc_verify_mode_facefp=顔＋指紋
acc_verify_mode_facepwd=顔＋パスワード
acc_verify_mode_facecard=顔＋カード
acc_verify_mode_facefpcard=顔＋指紋＋カード
acc_verify_mode_facefppwd=顔＋指紋＋パスワード
acc_verify_mode_fv=指静脈
acc_verify_mode_fvpwd=指静脈＋パスワード
acc_verify_mode_fvcard=指静脈＋カード
acc_verify_mode_fvpwdcard=指静脈＋パスワード＋カード
acc_verify_mode_pv=手のひら
acc_verify_mode_pvcard=手のひら＋カード
acc_verify_mode_pvface=手のひら＋顔
acc_verify_mode_pvfp=手のひら＋指紋
acc_verify_mode_pvfacefp=手のひら＋顔＋指紋
#[20]门禁事件编号
acc_eventNo_-1=無
acc_eventNo_0=通常認証
acc_eventNo_1=自動解錠中認証
acc_eventNo_2=ファーストユーザー解錠（カード）
acc_eventNo_3=マルチパーソン認証（カード）
acc_eventNo_4=非常パスワード認証
acc_eventNo_5=自動解錠タイムゾーン
acc_eventNo_6=リンケージイベントトリガー
acc_eventNo_7=アラームキャンセル
acc_eventNo_8=遠隔解錠
acc_eventNo_9=遠隔施錠
acc_eventNo_10=自動解錠タイムゾーン無効
acc_eventNo_11=自動解錠タイムゾーン有効
acc_eventNo_12=補助出力遠隔解錠
acc_eventNo_13=補助出力遠隔施錠
acc_eventNo_14=指紋認証
acc_eventNo_15=マルチパーソン認証（指紋）
acc_eventNo_16=自動解錠中指紋認証
acc_eventNo_17=カード+指紋認証
acc_eventNo_18=ファーストユーザー解錠（指紋）
acc_eventNo_19=ファーストユーザー解錠（カード＋指紋）
acc_eventNo_20=認証間隔が短い
acc_eventNo_21=無効タイムゾーン（カード）
acc_eventNo_22=不正タイムゾーン
acc_eventNo_23=アクセス拒否
acc_eventNo_24=アンチ・パスバック
acc_eventNo_25=インターロック
acc_eventNo_26=マルチパーソン認証（カード）
acc_eventNo_27=無効カード
acc_eventNo_28=開扉タイムアウト
acc_eventNo_29=カード期限切れ
acc_eventNo_30=パスワードエラー
acc_eventNo_31=指紋認証間隔が短い
acc_eventNo_32=マルチパーソン認証（指紋）
acc_eventNo_33=指紋有効期限切れ
acc_eventNo_34=無効指紋
acc_eventNo_35=無効タイムゾーン（指紋）
acc_eventNo_36=無効タイムゾーン（解錠スイッチ）
acc_eventNo_37=自動解錠中施錠失敗
acc_eventNo_38=カードレポート損失
acc_eventNo_39=アクセスが無効になっています
acc_eventNo_40=マルチパーソン認証失敗（指紋）
acc_eventNo_41=認証モードエラー
acc_eventNo_42=Wiegandフォーマットエラー
acc_eventNo_43=アンチ・パスバック認証タイムアウト
acc_eventNo_44=バックグラウンド認証失敗
acc_eventNo_45=バックグラウンド認証タイムアウト
acc_eventNo_47=コマンド送信失敗
acc_eventNo_48=マルチパーソン認証失敗（カード）
acc_eventNo_49=ドア無効タイムゾーン（パスワード）
acc_eventNo_50=パスワード間隔が短い
acc_eventNo_51=マルチパーソン認証（パスワード）
acc_eventNo_52=マルチパーソン認証失敗（パスワード）
acc_eventNo_53=パスワード期限切れ
acc_eventNo_100=タンパーアラーム
acc_eventNo_101=非常パスワード解錠
acc_eventNo_102=強制解錠
acc_eventNo_103=非常指紋解錠
acc_eventNo_200=ドア正常開扉
acc_eventNo_201=ドア正常閉扉
acc_eventNo_202=解錠スイッチ解錠
acc_eventNo_203=マルチパーソン認証（カード+指紋）
acc_eventNo_204=自動解錠タイムゾーン終了
acc_eventNo_205=遠隔解錠
acc_eventNo_206=デバイス開始
acc_eventNo_207=パスワード認証
acc_eventNo_208=管理者解錠
acc_eventNo_209=解錠スイッチトリガー（施錠無し）
acc_eventNo_210=火報入力
acc_eventNo_211=管理者施錠
acc_eventNo_212=エレベータコントロール機能有効
acc_eventNo_213=エレベータコントロール機能無効
acc_eventNo_214=マルチパーソン認証（パスワード）
acc_eventNo_215=ファーストユーザー解錠（パスワード）
acc_eventNo_216=自動解錠中パスワード認証
acc_eventNo_220=補助入力切断
acc_eventNo_221=補助入力短絡
acc_eventNo_222=バックグラウンド認証成功
acc_eventNo_223=バックグラウンド認証
acc_eventNo_225=補助入力正常
acc_eventNo_226=補助入力トリガー
acc_newEventNo_0=通常認証
acc_newEventNo_1=自動解錠中認証
acc_newEventNo_2=ファーストユーザー解錠
acc_newEventNo_3=マルチパーソン認証
acc_newEventNo_20=操作間隔が短い
acc_newEventNo_21=無効タイムゾーン認証
acc_newEventNo_26=マルチパーソン認証待機
acc_newEventNo_27=未登録ユーザー
acc_newEventNo_29=有効期限切れユーザー
acc_newEventNo_30=パスワードエラー
acc_newEventNo_41=認証モードエラー
acc_newEventNo_43=スタッフロック
acc_newEventNo_44=バックグラウンド認証エラー
acc_newEventNo_45=バックグラウンド認証タイムアウト
acc_newEventNo_48=マルチパーソン認証失敗
acc_newEventNo_54=バッテリー電圧低下
acc_newEventNo_55=バッテリーをすぐに交換してください
acc_newEventNo_56=不正操作
acc_newEventNo_57=バックアップ電力
acc_newEventNo_58=解錠アラーム
acc_newEventNo_59=不正管理
acc_newEventNo_60=ドア内部施錠
acc_newEventNo_61=複製
acc_newEventNo_62=禁止ユーザー
acc_newEventNo_63=ドア施錠
acc_newEventNo_64=不正タイムゾーン中解錠スイッチ
acc_newEventNo_65=不正タイムゾーン中補助入力
acc_newEventNo_66=リーダアップグレード失敗
acc_newEventNo_67=リモート比較が成功しました（デバイスが承認されていません）
acc_newEventNo_68=ハイボディ - アクセスが拒否されました。
acc_newEventNo_69=マスクなし-アクセス拒否
acc_newEventNo_70=顔比較サーバー通信例外
acc_newEventNo_71=フェイスサーバーが不規則に応答しています
acc_newEventNo_73=無効なQRコード
acc_newEventNo_74=QRコードの有効期限が切れました
acc_newEventNo_101=非常解錠アラーム
acc_newEventNo_104=不正カード認証アラーム
acc_newEventNo_105=サーバ未接続
acc_newEventNo_106=停電
acc_newEventNo_107=バッテリー不良
acc_newEventNo_108=マスターデバイスに接続できません
acc_newEventNo_109=リーダタンパーアラーム
acc_newEventNo_110=リーダオフライン
acc_newEventNo_112=拡張ボードがオフラインになっている
acc_newEventNo_114=火災入力遮断(回線検出)
acc_newEventNo_115=火災入力短絡(回線検出)
acc_newEventNo_116=補助入力遮断(回線検出)
acc_newEventNo_117=補助入力短絡(回線検出)
acc_newEventNo_118=出先スイッチオフ(回線検出)
acc_newEventNo_119=出先スイッチ短絡(回線検出)
acc_newEventNo_120=ゲート磁気遮断(回線検出)
acc_newEventNo_121=ゲート磁気短絡(回線検出)
acc_newEventNo_159=遠隔解錠
acc_newEventNo_214=サーバ接続中
acc_newEventNo_217=マスターデバイス接続成功
acc_newEventNo_218=IDカード認証
acc_newEventNo_222=バックグラウンド認証成功
acc_newEventNo_223=バックグラウンド認証
acc_newEventNo_224=ベル鳴動
acc_newEventNo_227=ダブルオープンドア
acc_newEventNo_228=ダブルクローズドア
acc_newEventNo_229=補助出力オープン
acc_newEventNo_230=補助出力クローズ
acc_newEventNo_232=認証成功
acc_newEventNo_233=ロックダウン有効
acc_newEventNo_234=ロックダウン無効
acc_newEventNo_235=リーダアップグレード成功
acc_newEventNo_236=リーダタンパーアラーム解除
acc_newEventNo_237=リーダオンライン
acc_newEventNo_239=デバイス呼び出し
acc_newEventNo_240=通話が終了しました
acc_newEventNo_243=火災入力切断
acc_newEventNo_244=火災警報入力短絡
acc_newEventNo_247=拡張ボードはオンライン
acc_newEventNo_4008=主電源の回復
acc_newEventNo_4014=消防入力信号が遮断され、終了ドアが常に開いている
acc_newEventNo_4015=ドアはオンライン
acc_newEventNo_4018=バックエンド比較 オープン
acc_newEventNo_5023=消防状態制限中
acc_newEventNo_5024=複数人認証のタイムアウト
acc_newEventNo_5029=バックエンド比較失敗
acc_newEventNo_6005=記録容量が限界に近づいています
acc_newEventNo_6006=回線短絡(RS485)
acc_newEventNo_6007=回線短絡(Wiegand)
acc_newEventNo_6011=ドアがオフラインになっています
acc_newEventNo_6012=ドア分解機アラーム
acc_newEventNo_6013=消防入力信号がトリガーされ、ドアを開けると常に
acc_newEventNo_6015=拡張デバイスの電源をリセット
acc_newEventNo_6016=リカバリマシンの出荷時設定
acc_newEventNo_6070=バックエンド比較(禁止リスト)
acc_eventNo_undefined=イベントNo.未定義
acc_advanceEvent_500=グローバルアンチ・パスバック（ロジカル）
acc_advanceEvent_501=ユーザー可用性（日付使用）
acc_advanceEvent_502=コントロールユーザー数
acc_advanceEvent_503=グローバルインターロック
acc_advanceEvent_504=ルート制御
acc_advanceEvent_505=グローバルアンチ・パスバック（時限）
acc_advanceEvent_506=グローバルアンチ・パスバック（時限、ロジカル）
acc_advanceEvent_507=ユーザー可用性（有効日の最初の使用後）
acc_advanceEvent_508=ユーザー可用性（使用回数）
acc_advanceEvent_509=バックグラウンド認証失敗（未登録ユーザー）
acc_advanceEvent_510=バックグラウンド認証失敗（データ例外）
acc_alarmEvent_701=DMR 違反(設定ルール: {0})
#[21]实时监控
acc_rtMonitor_openDoor=開
acc_rtMonitor_closeDoor=閉
acc_rtMonitor_remoteNormalOpen=遠隔解錠
acc_rtMonitor_realTimeEvent=リアルタイムイベント
acc_rtMonitor_photoMonitor=写真モニタリング
acc_rtMonitor_alarmMonitor=アラームモニタリング
acc_rtMonitor_doorState=ドア状況
acc_rtMonitor_auxOutName=補助出力名
acc_rtMonitor_nonsupport=サポート外
acc_rtMonitor_lock=施錠
acc_rtMonitor_unLock=解錠
acc_rtMonitor_disable=無効
acc_rtMonitor_noSensor=ドアセンサー無
acc_rtMonitor_alarm=アラーム
acc_rtMonitor_openForce=強制解錠
acc_rtMonitor_tamper=タンパー
acc_rtMonitor_duressPwdOpen=非常パスワード解錠
acc_rtMonitor_duressFingerOpen=非常指紋解錠
acc_rtMonitor_duressOpen=非常解錠
acc_rtMonitor_openTimeout=開扉タイムアウト
acc_rtMonitor_unknown=不明
acc_rtMonitor_noLegalDoor=条件を満たすドアがありません！
acc_rtMonitor_noLegalAuxOut=補助出力を動作させる条件がありません！
acc_rtMonitor_curDevNotSupportOp=選択中のデバイスはこの操作をサポートしません！
acc_rtMonitor_curNormalOpen=現状常時開
acc_rtMonitor_whetherDisableTimeZone=ドアの現在状況は常時開です、無効にして施錠しますか？
acc_rtMonitor_curSystemNoDoors=現状のシステムではドアが追加されていないか、要件を満たすドアがありません！
acc_rtMonitor_cancelAlarm=アラームキャンセル
acc_rtMonitor_openAllDoor=全ドア解錠
acc_rtMonitor_closeAllDoor=全ドア施錠
acc_rtMonitor_confirmCancelAlarm=アラームをキャンセルしますか？
acc_rtMonitor_calcelAllDoor=全アラームキャンセル
acc_rtMonitor_initDoorStateTip=システム内のユーザーに許可されたドアを取得しています。
acc_rtMonitor_alarmEvent=アラームイベント
acc_rtMonitor_ackAlarm=アラーム確認
acc_rtMonitor_ackAllAlarm=全アラーム確認
acc_rtMonitor_ackAlarmTime=アラーム時間確認
acc_rtMonitor_sureToAckThese={0}アラームを確認しますか? 確認後、アラームをキャンセルします。
acc_rtMonitor_sureToAckAllAlarm=全アラームを確認しますか？確認後、アラームをキャンセルします。
acc_rtMonitor_noSelectAlarmEvent=アラームイベントを選択してください！
acc_rtMonitor_noAlarmEvent=アラームイベントがありません！
acc_rtMonitor_forcefully=アラームキャンセル（強制解錠）
acc_rtMonitor_addToRegPerson=登録されたユーザーに追加
acc_rtMonitor_cardExist={0}に登録されました、重複しないでください！
acc_rtMonitor_opResultPrompt=送信成功：{0}個 送信失败：{1}個
acc_rtMonitor_doorOpFailedPrompt=下記ドアコマンド送信失敗、再試行してください!
acc_rtMonitor_remoteOpen=遠隔解錠
acc_rtMonitor_remoteClose=遠隔施錠
acc_rtMonitor_alarmSoundClose=オーディオが閉じました。
acc_rtMonitor_alarmSoundOpen=オーディオを開きました。
acc_rtMonitor_playAudio=アラート音機能
acc_rtMonitor_isOpenShowPhoto=开启显示照片功能
acc_rtMonitor_isOpenPlayAudio=アラート音機能有効
acc_rtm_open=遠隔解錠ボタン
acc_rtm_close=遠隔施錠ボタン
acc_rtm_eleModule=エレベータ
acc_cancelAlarm_fp=アラームキャンセル（非常指紋解錠）
acc_cancelAlarm_pwd=アラームキャンセル（非常パスワード解錠）
acc_cancelAlarm_timeOut=アラームキャンセル（開扉タイムアウト）
#定时同步设备时间
acc_timing_syncDevTime=デバイス時間同期タイミング
acc_timing_executionTime=実行時間
acc_timing_theLifecycle=ライフサイクル
acc_timing_errorPrompt=入力が不正です！
acc_timing_checkedSyncTime=同期時間を選択してください
#[25]门禁报表
acc_trans_hasAccLevel=出入できるドア
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=ゾーンを追加してください
acc_zone_code=ゾーンコード
acc_zone_parentZone=上位ゾーン
acc_zone_parentZoneCode=上位ゾーンコード
acc_zone_parentZoneName=上位ゾーン名
acc_zone_outside=外側
#[G2]读头定义
acc_readerDefine_readerName=リーダ名
acc_readerDefine_fromZone=開始
acc_readerDefine_toZone=終了
acc_readerDefine_delInfo1=これらのリーダ定義のゾーンは、拡張アクセス制御機能によって参照されているので、削除することはできません！
acc_readerDefine_selReader=リーダ選択
acc_readerDefine_selectReader=リーダを追加してください！
acc_readerDefine_tip=ユーザーが外側ゾーンに移動した後、ユーザーレコードは削除されます。
#[G3]全局反潜
acc_gapb_zone=ゾーン
acc_gapb_whenToResetGapb=アンチ・パスバックリセット時間
acc_gapb_apbType=アンチ・パスバックタイプ
acc_gapb_logicalAPB=ロジカルアンチ・パスバック
acc_gapb_timedAPB=時限アンチ・パスバック
acc_gapb_logicalTimedAPB=時限ロジカルアンチ・パスバック
acc_gapb_lockoutDuration=ロックアウト時間
acc_gapb_devOfflineRule=デバイスはオフラインです
acc_gapb_standardLevel=スタンダードアクセスレベル
acc_gapb_accessDenied=アクセス拒否
acc_gapb_doorControlZone=次のドアは、ゾーンの内外へのアクセスを制御します
acc_gapb_resetStatus=アンチ・パスバックステータスリセット
acc_gapb_obeyAPB=アンチ・パスバックルールに従う
acc_gapb_isResetGAPB=アンチ・パスバックリセット
acc_gapb_resetGAPBSuccess=アンチ・パスバックステータスリセット
acc_gapb_resetGAPBFaile=アンチ・パスバックステートリセット失敗
acc_gapb_chooseArea=ゾーンを再選択してください
acc_gapb_notDelInfo1=ゾーンは上位アクセスに設定されました、削除できません！
acc_gapb_notDelInfo2=ゾーンはリーダ定義に使用されました、削除できません！
acc_gapb_notDelInfo3=ゾーンは拡張アクセスに使用されました、削除できません！
acc_gapb_notDelInfo4=ゾーンはLEDに使用されました、削除できません！
acc_gapb_zoneNumRepeat=重複です！
acc_gapb_zoneNameRepeat=重複です！
acc_gapb_personResetGapbPre=リセットしますか？
acc_gapb_personResetGapbSuffix=ユーザーアンチ・パスバックルール？
acc_gapb_apbPrompt=１つのドアに、独立した２つのアンチ・パスバックルールは設定できません！
acc_gapb_occurApb=アンチ・パスバック発生
acc_gapb_noOpenDoor=開扉禁止
acc_gapb_openDoor=開扉
acc_gapb_zoneNumLength=長さが２０文字を超えます！
acc_gapb_zoneNameLength=長さが３０文字を超えます！
acc_gapb_zoneRemarkLength=長さが５０文字を超えます！
acc_gapb_isAutoServerMode=バックグラウンド認証機能を持たないデバイスが検出され、機能に影響する可能性があります。 今すぐ開きますか？
acc_gapb_applyTo=適用
acc_gapb_allPerson=全ユーザー
acc_gapb_justSelected=選択ユーザー
acc_gapb_excludeSelected=選択ユーザー以外
#[G4]who is inside
acc_zoneInside_lastAccessTime=最終アクセス時刻
acc_zoneInside_lastAccessReader=最終アクセスリーダ
acc_zoneInside_noPersonInZone=ゾーン内にユーザーがありません！
acc_zoneInside_noRulesInZone=ルール未設定
acc_zoneInside_totalPeople=ユーザー合計
acc_zonePerson_selectPerson=ユーザー/部署を選択してください！
#[G5]路径
acc_route_name=ルート名
acc_route_setting=ルート設定
acc_route_addReader=リーダ追加
acc_route_delReader=リーダ削除
acc_route_defineReaderLine=リーダライン定義
acc_route_up=上
acc_route_down=下
acc_route_selReader=リーダを選択してください！
acc_route_onlyOneOper=１つのリーダしか選択できません！
acc_route_readerOrder=リーダ配列定義
acc_route_atLeastSelectOne=リーダを選択してください！
acc_route_routeIsExist=ルートが重複です！
#[G6]DMR
acc_dmr_residenceTime=滞在時間
acc_dmr_setting=DMR設定
#[G7]Occupancy
acc_occupancy_max=最大容量
acc_occupancy_min=最小容量
acc_occupancy_unlimit=無制限
acc_occupancy_note=最小在室容量は、最大在室容量以下です。
acc_occupancy_containNote=最大/最小容量を少なくとも1つ設定してください。
acc_occupancy_maxMinValid=容量を0より大きい値に設定してください。
acc_occupancy_conflict=このゾーンには、占有管理ルールが設定されています。
acc_occupancy_maxMinTip=最大/最小容量を空にすると、無制限の意味です。
#card availability
acc_personLimit_zonePropertyName=ゾーンプロパティ名
acc_personLimit_useType=使用
acc_personLimit_userDate=有効日
acc_personLimit_useDays=有効日の最初の使用後
acc_personLimit_useTimes=回数使用
acc_personLimit_setZoneProperty=ゾーンプロパティ設定
acc_personLimit_zoneProperty=ゾーンプロパティ
acc_personLimit_availabilityName=可用性名
acc_personLimit_days=日
acc_personLimit_Times=回
acc_personLimit_noDel=選択したアクセス制御ゾーンのプロパティが参照されているので、削除できません。
acc_personLimit_cannotEdit=アクセス領域属性は参照されているので、変更できません。
acc_personLimit_detail=詳細
acc_personLimit_userDateTo=次まで有効：
acc_personLimit_addPersonRepeatTip=選択した部門の人がアクセス制御領域の属性に追加されました。もう一度部門を選択してください！
acc_personLimit_leftTimes={0}残り時間
acc_personLimit_expired=期限切れ
acc_personLimit_unused=使用されていない
#全局互锁
acc_globalInterlock_addGroup=グループ追加
acc_globalInterlock_delGroup=グループ削除
acc_globalInterlock_refuseAddGroupMessage=同じインターロックは、追加されたグループ内でドアを複製することはできません
acc_globalInterlock_refuseAddlockMessage=追加したいドアは、他のグループのインターロックに設定済みです
acc_globalInterlock_refuseDeleteGroupMessage=インターロック関連のデータを削除してください
acc_globalInterlock_isGroupInterlock=グループインターロック
acc_globalInterlock_isAddTheDoorImmediately=すぐにドア追加
acc_globalInterlock_isAddTheGroupImmediately=すぐにグループ追加
#门禁参数设置
acc_param_autoEventDev=イベントログには同時にデバイス数が自動的にダウンロードされます
acc_param_autoEventTime=イベントログ間隔を自動的にダウンロードする
acc_param_noRepeat=Emailアドレスは重複できません。再入力してください
acc_param_most18=最大18のEmailアドレスを追加できます
acc_param_deleteAlert=すべてのEmailアドレスの入力ボックスを削除できません。
acc_param_invalidOrRepeat=Emailフォーマットエラーまたは、重複しています
#全局联动
acc_globalLinkage_noSupport=選択したドアが施錠/解錠する機能をサポートできません。
acc_globalLinkage_trigger=グローバルリンケージトリガー
acc_globalLinkage_noAddPerson=リンケージルールにはユーザー関連のトリガーは含まれていませんが、デフォルトではすべてのユーザーに適用されます！
acc_globalLinkage_selectAtLeastOne=リンケージ出力ポイントとビデオリンケージの間で少なくとも1つを選択してください！
acc_globalLinkage_selectTrigger=リンケージトリガー条件を追加してください！
acc_globalLinkage_selectInput=入力ポイントを追加してください！
acc_globalLinkage_selectOutput=出力ポイントを追加してください！
acc_globalLinkage_audioRemind=音声プロンプトリンケージ
acc_globalLinkage_audio=音声リンケージ
acc_globalLinkage_isApplyToAll=すべてのユーザーに適用
acc_globalLinkage_scope=ユーザー範囲
acc_globalLinkage_everyPerson=いずれか
acc_globalLinkage_selectedPerson=選択済
acc_globalLinkage_noSupportPerson=サポート外
acc_globalLinkage_reselectInput=トリガー条件のタイプが変更されました。入力ポイントを再選択してください！
acc_globalLinkage_addPushDevice=機能をサポートするデバイスを追加してください
#其他
acc_InputMethod_tips=英語入力モードに切り替えてください！
acc_device_systemCheckTip=アクセスデバイスが存在しません！
acc_notReturnMsg=情報を返信無し
acc_validity_period=ライセンス期限切れで、機能は動作不可です
acc_device_pushMaxCount=システムには既に{0}デバイスが存在し、ライセンス上限に達したため、デバイスを追加できません
acc_device_videoHardwareLinkage=ビデオハードウェアリンケージ
acc_device_videoCameraIP=ビデオカメラIP
acc_device_videoCameraPort=ビデオカメラポート
acc_location_unable=インシデントポイントはマップに追加できませんでした
acc_device_wgDevMaxCount=デバイスがシステムのライセンス制限に達したため、設定を変更できません。
#自定义报警事件
acc_deviceEvent_selectSound=オーディオファイルを選択してください
acc_deviceEvent_batchSetSoundErr=バッチ設定アラーム音が異常です！
acc_deviceEvent_batchSet=オーディオ設定
acc_deviceEvent_sound=イベント音
acc_deviceEvent_exist=既存
acc_deviceEvent_upload=アップロード
#查询门最近发生事件
acc_doorEventLatestHappen=ドアイベントクエリ
#门禁人员信息
acc_pers_delayPassage=解錠遅延
#设备容量提示
acc_dev_usageConfirm=デバイスの 90% を超える容量
acc_dev_immediateCheck=すぐにチェック
acc_dev_inSoftware=ソフトウェア内
acc_dev_inFirmware=ファームウェア内
acc_dev_get=取得
acc_dev_getAll=すべて取得
acc_dev_loadError=ロード失敗
#Reader
acc_reader_inout=入/出
acc_reader_lightRule=ライトルール
acc_reader_defLightRule=デフォルトルール
acc_reader_encrypt=暗号化
acc_reader_allReaderOfCurDev=選択デバイスの全リーダ
acc_reader_tip1=暗号化は選択デバイスの全リーダにコピーされます！
acc_reader_tip2=リーダが身分証明書の機能がサポートできません！
acc_reader_tip3=RS485プロトコルタイプは、現在のデバイスのすべてのリーダーにコピーされます。 設定はデバイスの再起動後に有効になります！
acc_reader_tip4=一部の人事情報を非表示にするオプションは、デフォルトで同じデバイスのすべてのリーダーにコピーされます！
acc_reader_commType=通信タイプ
acc_reader_commAddress=通信アドレス
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=無効
acc_readerComAddress_repeat=重複した通信アドレス
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=RS485 アドレス
acc_readerCommType_wgAddress=Wiegand アドレス
acc_reader_macError=MACアドレスを正しいフォーマットで入力してください
acc_reader_machineType=リーダタイプ
acc_reader_readMode=モード
acc_reader_readMode_normal=ノーマルモード
acc_reader_readMode_idCard=IDカードモード
acc_reader_note=注意：カメラと同じゾーン({0})にあるデバイスを選択してください！
acc_reader_rs485Type=RS485プロトコルタイプ
acc_reader_userLock=人事アクセスロック
acc_reader_userInfoReveal=隠蔽パーツ人事情報
#operat
acc_operation_pwd=操作パスワード
acc_operation_pwd_error=パスワードエラー
acc_new_input_not_same=新しいキーが一致しません
acc_op_set_keyword=ライセンスキー設定
acc_op_old_key=古いキー
acc_op_new_key=新しいキー
acc_op_cofirm_key=キー確認
acc_op_old_key_error=古いキーエラー
#验证方式规则
acc_verifyRule_name=ルール名
acc_verifyRule_door=ドア認証
acc_verifyRule_person=ユーザー認証
acc_verifyRule_copy=月曜の設定を他の平日にコピーします：
acc_verifyRule_tip1=最低一つ認証モードを選択してください！
acc_verifyRule_tip2=ルールにユーザー認証モードが含まれている場合、RS485リーダーが入っているドアを追加できません。リーダータイプをWiegandに変更してください！
acc_verifyRule_tip3=RS485リーダはドア認証モードに従うことができるだけで、ユーザー認証モードはサポートしてません。
acc_verifyRule_oldVerifyMode=古い検証モード
acc_verifyRule_newVerifyMode=新しい検証モード
acc_verifyRule_newVerifyModeSelectTitle=新しい検証方法を選択します
acc_verifyRule_newVerifyModeNoSupportTip=新しい検証方法をサポートするデバイスはありません！
#Wiegand Test
acc_wiegand_beforeCard=新しいカードビット数は({0} bits)古いカードと同じではありません！
acc_wiegand_curentCount=現在のカード長：{0} Bits
acc_wiegand_card=カード
acc_wiegand_readCard=カード読み取り
acc_wiegand_clearCardInfo=カード情報クリア
acc_wiegand_originalCard=オリジナルカードNo.
acc_wiegand_recommendFmt=推奨カードフォーマット
acc_wiegand_parityFmt=オッドイーブンパリティフォーマット
acc_wiegand_withSizeCode=サイトコードが空白のままサイトコードを自動計算します。
acc_wiegand_tip1=サイトコードが同じではありません。
acc_wiegand_tip2=サイトコード:{0}、カードNo.:{1}が、元のカードNo.と一致しませんでした。 入力されたサイトコードとカードNo.を再度確認してください！
acc_wiegand_tip3=入力されたカードNo.({0})が、元のカードNo.と一致しません。 再度確認してください！
acc_wiegand_tip4=入力されたサイズコード({0})が、元のカードNo.と一致しません。 再度確認してください！
acc_wiegand_tip5=この機能を使用には、すべてのサイトコード列を空にしてください。
acc_wiegand_warnInfo1=新しいカードを読み続けるときは、手動で次のカードに切り替えてください。
#LCD实时监控
acc_leftMenu_LCDRTMonitor=ユーザー入/出ボード
acc_LCDRTMonitor_current=現在のユーザー情報
acc_LCDRTMonitor_previous=以前のユーザー情報
#api
acc_api_levelIdNotNull=アクセスレベルが空にできません。
acc_api_levelExist=アクセスレベルあり
acc_api_levelNotExist=アクセスレベルがありません。
acc_api_areaNameNotNull=領域を空にすることはできません
acc_api_levelNotHasPerson=ユーザーがいません。
acc_api_doorIdNotNull=ドアIDが空にできません。
acc_api_doorNameNotNull=ドア名が空にできません。
acc_api_doorIntervalSize=解錠時間は１～２５４秒です。
acc_api_doorNotExist=ドアがありません。
acc_api_devOffline=デバイスがオフラインです。
acc_api_devSnNotNull=SN番号が空にできません。
acc_api_timesTampNotNull=タイムスタンプを空にすることはできません
acc_api_openingTimeCannotBeNull=ドアの開放時間を空にすることはできません
acc_api_parameterValueCannotBeNull=パラメータ値を空にすることはできません
acc_api_deviceNumberDoesNotExist=デバイスのシリアル番号が存在しません
acc_api_readerIdCannotBeNull=リーダーIDを空にすることはできません
acc_api_theReaderDoesNotExist=読み取りヘッドが存在しません
acc_operate_door_notInValidDate=リモートで解錠できません、管理者と連絡してください！
acc_api_doorOffline=ドアがオフラインまたは無効になっています
#门禁信息自动导出
acc_autoExport_title=イベント履歴自動導出
acc_autoExport_frequencyTitle=自動導出頻度
acc_autoExport_frequencyDay=毎日
acc_autoExport_frequencyMonth=毎月
acc_autoExport_firstDayMonth=最初日（毎月）
acc_autoExport_specificDate=導出日付
acc_autoExport_exportModeTitle=導出モード
acc_autoExport_dailyMode=イベント履歴（毎日）
acc_autoExport_monthlyMode=イベント履歴（毎月）
acc_autoExport_allMode=全イベント履歴（最大３００００）
acc_autoExport_recipientMail=受信メール
#First In And Last Out
acc_inOut_inReaderName=最初入りリーダー名
acc_inOut_firstInTime=最初入り時間
acc_inOut_outReaderName=最後出しリーダー名
acc_inOut_lastOutTime=最後出し時間
#防疫参数
acc_dev_setHep=マスク/体温検知パラメーター
acc_dev_enableIRTempDetection=赤外線体温検知有効
acc_dev_enableNormalIRTempPass=発熱で通過拒否
acc_dev_enableMaskDetection=マスク検知有効
acc_dev_enableWearMaskPass=マスク無しで通過拒否
acc_dev_tempHighThreshold=発熱アラームしきい値
acc_dev_tempUnit=体温単位
acc_dev_tempCorrection=温度偏差補正
acc_dev_enableUnregisterPass=未登録ユーザー通過
acc_dev_enableTriggerAlarm=外部トリガーアラーム
#联动邮件
acc_mail_temperature=体温
acc_mail_mask=マスク
acc_mail_unmeasured=未測定
#Digifort联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifortグローバルイベント
acc_digifort_chooseDigifortEvents=Digifortグローバルイベントを選択してください
acc_digifort_eventExpiredTip=グローバルイベントがDigifortサーバーから削除されると、赤で表示されます。
acc_digifort_checkConnection=Digifortサーバーの接続情報が正しいか確認してください。
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=連絡先を追加
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
#扩展参数
acc_dev_setExtendParam=拡張パラメーター設定
acc_extendParam_faceUI=インタフェース表示
acc_extendParam_faceParam=顔パラメーター
acc_extendParam_accParam=アクセスパラメーター
acc_extendParam_intercomParam=ビジュアルインタラクティブパラメータ
acc_extendParam_volume=音量
acc_extendParam_identInterval=認証間隔（ms）
acc_extendParam_historyVerifyResult=過去認証結果表示
acc_extendParam_macAddress=MACアドレス表示
acc_extendParam_showIp=IPアドレス表示
acc_extendParam_24HourFormat=24時間フォーマット
acc_extendParam_dateFormat=日付フォーマット
acc_extendParam_1NThreshold=1: Nしきい値
acc_extendParam_facePitchAngle=顔認証垂直角度
acc_extendParam_faceRotationAngle=顔認証水平角度
acc_extendParam_imageQuality=画質
acc_extendParam_miniFacePixel=最小顔ピクセル
acc_extendParam_biopsy=生体検知有効
acc_extendParam_showThermalImage=熱画像表示
acc_extendParam_attributeAnalysis=属性分析有効
acc_extendParam_temperatureAttribute=体温検知パラメーター
acc_extendParam_maskAttribute=マスク検知パラメーター
acc_extendParam_minTemperature=最小体温
acc_extendParam_maxTemperature=最大体温
acc_extendParam_gateMode=ゲートモード
acc_extendParam_qrcodeEnable=QRコード機能を有効にする
#可视对讲
acc_dev_intercomServer=ビジュアルインタラクティブサービスアドレス
acc_dev_intercomPort=ビジュアルインタラクティブサービス
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=同期許可グループ
# 夏令时名称
acc_dsTimeUtc_none=設定しない
acc_dsTimeUtc_AreaNone=このエリアにはサマータイムがありません。
acc_dsTimeUtc1000_0=キャンベラ、メルボルン、シドニー
acc_dsTimeUtc1000_1=ホバート
acc_dsTimeUtc_0330_0=ニューファンドランド
acc_dsTimeUtc_1000_0=アリューシャン列島
acc_dsTimeUtc_0200_0=中大西洋-旧用
acc_dsTimeUtc0930_0=アデレード
acc_dsTimeUtc_0100_0=アゾレス諸島
acc_dsTimeUtc_0400_0=大西洋時間（カナダ）
acc_dsTimeUtc_0400_1=サンディエゴ
acc_dsTimeUtc_0400_2=アーソンソンソン
acc_dsTimeUtc_0300_0=グリーンランド
acc_dsTimeUtc_0300_1=サンピエール島・ミクロン島
acc_dsTimeUtc0200_0=朴訥である
acc_dsTimeUtc0200_1=ヘルシンキ、キエフ、リガ、ソフィア、タリン、ヴィリニュス
acc_dsTimeUtc0200_2=アテネ、ブカレスト
acc_dsTimeUtc0200_3=エルサレム
acc_dsTimeUtc0200_4=エメン
acc_dsTimeUtc0200_5=ベイルート
acc_dsTimeUtc0200_6=ダマスカス
acc_dsTimeUtc0200_7=ガザ、ヘブライ
acc_dsTimeUtc0200_8=朱色のパキスタン
acc_dsTimeUtc_0600_0=中部標準時（米国とカナダ）
acc_dsTimeUtc_0600_1=グアンダラハラ、メキシコシティ、モンテレー
acc_dsTimeUtc_0600_2=イースター島
acc_dsTimeUtc1300_0=サモア諸島
acc_dsTimeUtc_0500_0=ハバナ
acc_dsTimeUtc_0500_1=東部標準時（米国とカナダ）
acc_dsTimeUtc_0500_2=ハイチ
acc_dsTimeUtc_0500_3=インディアナ州(東部)
acc_dsTimeUtc_0500_4=タークス・カイコス諸島
acc_dsTimeUtc_0800_0=太平洋時間（米国とカナダ）
acc_dsTimeUtc_0800_1=カリフォルニア州に降りる
acc_dsTimeUtc0330_0=テヘラン
acc_dsTimeUtc0000_0=ダブリン、エディンバラ、リスボン、ロンドン
acc_dsTimeUtc1200_0=フィジー
acc_dsTimeUtc1200_1=ピーターロパブロスキー-カンチャカ-旧用
acc_dsTimeUtc1200_2=オークランド、ウェリントン
acc_dsTimeUtc1100_0=ノーフォーク島
acc_dsTimeUtc_0700_0=チワワ、ラパス、マサトランド
acc_dsTimeUtc_0700_1=山岳部時間（米国とカナダ）
acc_dsTimeUtc0100_0=ベオグラード、ブラチスラヴァ、ブダペスト、リュブリャナ、プラハ
acc_dsTimeUtc0100_1=サラエヴォ、スコピエ、ワルシャワ、ザグレブ
acc_dsTimeUtc0100_2=カサブランカ
acc_dsTimeUtc0100_3=ブリュッセル、コペンハーゲン、マドリード、パリ
acc_dsTimeUtc0100_4=アムステルダム、ベルリン、ベルン、ロマ、ストックホルム、ウィーン
acc_dsTimeUtc_0900_0=アラスカ
#安全点(muster point)
acc_leftMenu_accMusterPoint=マスターポイント
acc_musterPoint_activate=アクティブ化
acc_musterPoint_addDept=部門の追加
acc_musterPoint_delDept=部門の削除
acc_musterPoint_report=マスターポイントレポート
acc_musterPointReport_sign=手動でサインイン
acc_musterPointReport_generate=レポートの生成
acc_musterPoint_addSignPoint=サインポイントの追加
acc_musterPoint_delSignPoint=サインポイントの削除
acc_musterPoint_selectSignPoint=サインポイントを追加してください！
acc_musterPoint_signPoint=サインポイント
acc_musterPoint_delFailTip=すでにアクティブ化されているマスターポイントがあり、削除できません！
acc_musterPointReport_enterTime=時間を入力してください
acc_musterPointReport_dataAnalysis=データ分析
acc_musterPointReport_safe=Safe
acc_musterPointReport_danger=Danger
acc_musterPointReport_signInManually=手動パンチ
acc_musterPoint_editTip=マスターポイントはアクティブであり、編集できません！
acc_musterPointEmail_total=予定出席者数：
acc_musterPointEmail_safe=チェックイン（セキュリティ）：
acc_musterPointEmail_dangerous=危険な状況：
acc_musterPoint_messageNotification=アクティブ化時のメッセージ通知
acc_musterPointReport_sendEmail=計画プッシュレポート
acc_musterPointReport_sendInterval=送信間隔
acc_musterPointReport_sendTip=選択した通知方式の構成が成功していることを確認してください。そうしないと、通知が正常に送信されません！
acc_musterPoint_mailSubject=緊急集合通知
acc_musterPoint_mailContent=すぐに「{0}」に集まり、「{1}」デバイスでサインインしてください。ありがとうございます!
acc_musterPointReport_mailHead=はい、これは緊急事態報告書です。復習してください。
acc_musterPoint_visitorsStatistics=ゲスト統計
# 报警监控
acc_alarm_priority=優先度
acc_alarm_total=全部
acc_alarm_today=今日の記録
acc_alarm_unhandled=未確認
acc_alarm_inProcess=処理中
acc_alarm_acknowledged=確認済み
acc_alarm_top5=警察事件上位5名
acc_alarm_monitoringTime=モニタ時間
acc_alarm_history=警報処理の歴史
acc_alarm_acknowledgement=処理記録
acc_alarm_eventDescription=イベント詳細
acc_alarm_acknowledgeText=チェックしたら、アラームイベントの詳細を指定のメールボックスに送信します。
acc_alarm_emailSubject=アラーム事件処理記録の追加
acc_alarm_mute=ミュート
acc_alarm_suspend=一時停止
acc_alarm_confirmed=この事件は確認されました。
acc_alarm_list=アラームログ
#ntp
acc_device_setNTPService=NTPサーバー設定
acc_device_setNTPServiceTip=複数のサーバーアドレスをコンマ（、）またはセミコロン（;）で区切って入力します
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=ゲートコントローラの操作では、スーパーユーザーはタイムゾーン、逆折り返し、インターロックの規定に制限されず、非常に高いドアの優先度を持っています。
acc_editPerson_delayPassageTip=アクセスポイントを通過するユーザの待ち時間を延長します。身体障害者やその他の障害者に適しています。
acc_editPerson_disabledTip=個人のアクセス・レベルを一時的に無効にします。
#门禁向导
acc_guide_title=アクセス コントロール モジュール セットアップ ウィザード
acc_guide_addPersonTip=個人と対応する資格情報 (顔、指紋、カード、手のひら、またはパスワード) を追加する必要があります。すでに追加している場合は、この手順を直接スキップしてください。
acc_guide_timesegTip=有効な開始時間を設定してください
acc_guide_addDeviceTip=対応するデバイスをアクセスポイントとして追加してください
acc_guide_addLevelTip=アクセス制御レベルを追加
acc_guide_personLevelTip=対応するアクセス制御権限を個人に割り当てます
acc_guide_rtMonitorTip=アクセス制御レコードをリアルタイムで確認する
acc_guide_rtMonitorTip2=所属エリアの追加とゲート対応後のゲートレコードのリアルタイム表示
#查看区域内人员
acc_zoneperson_cleanCount=出入りする人の統計をクリアします
acc_zoneperson_inCount=入場者数の統計
acc_zoneperson_outCount=退職者数の統計
#biocv460
acc_device_validFail=ユーザー名またはパスワードが正しくなく、検証が失敗します！
acc_device_pwdRequired=最大6ビット整数のみ入力可能