#[1]左侧菜单
acc_module=Zugang
acc_leftMenu_accDev=Zugangsgerät
acc_leftMenu_auxOut=Zusatzausgang
acc_leftMenu_dSTime=Sommerzeit
acc_leftMenu_access=Zugangskontrolle
acc_leftMenu_door=Tür
acc_leftMenu_accRule=Zugangsregelung
acc_leftMenu_interlock=Sperre
acc_leftMenu_antiPassback=Anti-Passback (Zutrittswiederholsperre)
acc_leftMenu_globalLinkage=Allgemeine Verknüpfung
acc_leftMenu_firstOpen=Erste Normalöffnung durch Einzelperson
acc_leftMenu_combOpen=Türöffnung durch mehrere zur Türöffnung benötigter Personen
acc_leftMenu_personGroup=Gruppe von mehreren zur Türöffnung benötigter Personen
acc_leftMenu_level=Zugangsebenen
acc_leftMenu_electronicMap=Karte
acc_leftMenu_personnelAccessLevels=Zugangsebenen Personal
acc_leftMenu_searchByLevel=Nach Zugangsebene
acc_leftMenu_searchByDoor=Zugangsrechte nach Tür
acc_leftMenu_expertGuard=Erweiterte Funktionen
acc_leftMenu_zone=Bereich
acc_leftMenu_readerDefine=Lesegerät festlegen
acc_leftMenu_gapbSet=Global Anti-Passback (allgemeine Zutrittswiederholsperre)
acc_leftMenu_whoIsInside=Wer ist drinnen
acc_leftMenu_whatRulesInside=Welche Regelungen drinnen gelten
acc_leftMenu_occupancy=Auslastungskontrolle
acc_leftMenu_route=Wegstreckenkontrolle
acc_leftMenu_globalInterlock=Allgemeine Sperre
acc_leftMeue_globalInterlockGroup=Allgemeine Sperre Gruppe
acc_leftMenu_dmr=Dead Man Rule
acc_leftMenu_personLimit=Personenverfügbarkeit
acc_leftMenu_verifyModeRule=Überprüfungsmodus
acc_leftMenu_verifyModeRulePersonGroup=Überprüfungsmodusgruppe
acc_leftMenu_extDev=E / A-Karte
acc_leftMenu_firstInLastOut=First In und Last Out
acc_leftMenu_accReports=Zugriffskontrollberichte
#[3]门禁时间段
acc_timeSeg_entity=Zeitzone
acc_timeSeg_canNotDel=Die Zeitraum ist aktiv und kann nicht gelöscht werden!
#[4]门禁设备--公共的在common中
acc_common_ruleName=Name der Regelung
acc_common_hasBeanSet=Wurde festgelegt
acc_common_notSet=Nicht festgelegt
acc_common_hasBeenOpened=Wurde geöffnet
acc_common_notOpened=Nicht geöffnet
acc_common_partSet=Teil des Sets
acc_common_linkageAndApbTip=Verknüpfung und allgemeine Verknüpfung, Anti-Passback und Global Anti-Passback wurden gleichzeitig festgelegt, es kann zu Konflikten kommen.
acc_common_vidlinkageTip=Vergewissern Sie sich, dass die entsprechende Eingangspunktverknüpfung mit dem verfügbaren Videokanal verbunden ist, andernfalls funktioniert die Videoverknüpfung nicht!
acc_common_accZoneFromTo=Kann nicht gleichen Bereich festlegen
acc_common_logEventNumber=Ereignis-ID
acc_common_bindOrUnbindChannel=Verbindung mit Kamera aufbauen/trennen
acc_common_boundChannel=Verbundene Kamera
#设备信息
acc_dev_iconType=Symbol-Typ
acc_dev_carGate=Parkschranke
acc_dev_channelGate=Klappschranke
acc_dev_acpType=Art des Kontrollpanels
acc_dev_oneDoorACP=1-Tür Zugangskontrollpanel
acc_dev_twoDoorACP=2-Tür Zugangskontrollpanel
acc_dev_fourDoorACP=4-Tür Zugangskontrollpanel
acc_dev_onDoorACD=Standalone Gerät
acc_dev_switchToTwoDoorTwoWay=Zu 2-Türen 2-Wege wechseln
acc_dev_addDevConfirm2=Hinweis: Die Geräteverbindung war erfolgreich, aber die Art des Zugangskontrollpanels stimmt nicht dem tatsächlichen überein. Bitte auf {0} Tür(en) Kontrollpanel anpassen. Mit dem Hinzufügen fortfahren?
acc_dev_addDevConfirm4=Standalone Gerät. Mit dem Hinzufügen fortfahren?
acc_dev_oneMachine=Standalone Gerät
acc_dev_fingervein=Fingervene
acc_dev_control=Gerätekontrolle
acc_dev_protocol=Protokolltyp
acc_ownedBoard=Besitz Board
#设备操作
acc_dev_start=Start
acc_dev_accLevel=Zugangsberechtigung
acc_dev_timeZoneAndHoliday=Zeitzone, Urlaubstage
acc_dev_linkage=Verknüpfung
acc_dev_doorOpt=Türparameter
acc_dev_firstPerson=Erste Türöffnung durch Einzelperson
acc_dev_multiPerson=Türöffnung durch mehrere zur Türöffnung benötigter Personen
acc_dev_interlock=Sperre
acc_dev_antiPassback=Anti-Passback
acc_dev_wiegandFmt=Wiegand-Format
acc_dev_outRelaySet=Einstellungen Zusatzausgang
acc_dev_backgroundVerifyParam=Optionen Bg-Prüfung
acc_dev_getPersonInfoPrompt=Bitte vergewissern Sie sich, dass die Personaldaten richtig abgerufen wurden, andernfalls wird eine Ausnahme auftreten. Fortfahren?
acc_dev_getEventSuccess=Ereignisse abrufen erfolgreich.
acc_dev_getEventFail=Ereignis(se) abrufen fehlgeschlagen.
acc_dev_getInfoSuccess=Information abrufen erfolgreich.
acc_dev_getInfoXSuccess={0} abrufen erfolgreich.
acc_dev_getInfoFail=Informationen abrufen fehlgeschlagen.
acc_dev_updateExtuserInfoFail=Aktualisierung der Personaldaten für erweiterten Durchgang fehlgeschlagen, bitte Informationen aufrufen.
acc_dev_getPersonCount=Anzahl der Nutzer abrufen
acc_dev_getFPCount=Anzahl der Fingerabdrücke aufrufen
acc_dev_getFVCount=Anzahl der Fingervenen abrufen
acc_dev_getFaceCount=Anzahl der Gesichter abrufen
acc_dev_getPalmCount=Anzahl der Handflächen abrufen
acc_dev_getBiophotoCount=Anzahl der Gesichtsbilder abrufen
acc_dev_noData=Keine Datenrücksendung des Geräts.
acc_dev_noNewData=Keine neuen Transaktionen im Gerät.
acc_dev_softLtDev=Mehr Personen in der Software als im Gerät.
acc_dev_personCount=Anzahl Personen:
acc_dev_personDetail=Details wie folgt:
acc_dev_softEqualDev=Gleiche Anzahl von Personen in Software und Gerät.
acc_dev_softGtDev=Mehr Personen im Gerät als in der Software.
acc_dev_cmdSendFail=Senden von Befehlen fehlgeschlagen, bitte erneut senden.
acc_dev_issueVerifyParam=Optionen Bg-Verifizierung konfigurieren
acc_dev_verifyParamSuccess=Optionen Bg-Verifizierung erfolgreich durchgeführt
acc_dev_backgroundVerify=Hintergrund-Prüfung
acc_dev_selRightFile=Wählen Sie die richtige Upgrade-Datei aus!
acc_dev_devNotOpForOffLine=Das Gerät ist offline, bitte versuchen Sie es später noch einmal
acc_dev_devNotSupportFunction=Das Gerät unterstützt diese Funktion nicht
acc_dev_devNotOpForDisable=Das Gerät ist deaktiviert, bitte versuchen Sie es später noch einmal
acc_dev_devNotOpForNotOnline=Das Gerät ist offline oder deaktiviert, bitte versuchen Sie es später noch einmal
acc_dev_getPersonInfo=Personaldaten erhalten
acc_dev_getFPInfo=Fingerabdruckinformationen erhalten
acc_dev_getFingerVeinInfo=Fingerveneninformationen erhalten
acc_dev_getPalmInfo=Handflächeninformationen erhalten
acc_dev_getBiophotoInfo=Gesichtsbilderinformationen erhalten
acc_dev_getIrisInfo=Iris Informationen erhalten
acc_dev_disable=deaktiviert, bitte erneut auswählen
acc_dev_offlineAndContinue=ist offline, fortfahren?
acc_dev_offlineAndSelect=ist offline.
acc_dev_opAllDev=Ganzes Gerät
acc_dev_opOnlineDev=Online Gerät
acc_dev_opException=Behandlung von Ausnahmen
acc_dev_exceptionAndConfirm=Zeitüberschreitung Geräteverbindung, Vorgang fehlgeschlagen. Überprüfen Sie die Netzwerkverbindung
acc_dev_getFaceInfo=Gesichtsinformationen erhalten
acc_dev_selOpDevType=Wählen Sie die Art des zu bedienenden Geräts
acc_dev_hasFilterByFunc=Nur Online-Geräte anzeigen und solche, die diese Funktion unterstützen!
acc_dev_masterSlaveMode=RS485 Master- und Slave-Mode
acc_dev_master=Host
acc_dev_slave=Slave
acc_dev_modifyRS485Addr=RS485 Adresse ändern
acc_dev_rs485AddrTip=Bitte eine ganze Zahl zwischen 1 und 63 eingeben!
acc_dev_enableFeature=Die Geräte, die Hintergrund-Prüfung aktiviert haben
acc_dev_disableFeature=Die Geräte, die Hintergrund-Prüfung deaktiviert haben
acc_dev_getCountOnly=Count only abrufen
acc_dev_queryDevPersonCount=Abfrage Personal-Count
acc_dev_queryDevVolume=Ansicht Gerätekapazität
acc_dev_ruleType=Regeltyp
acc_dev_contenRule=Regeldetails
acc_dev_accessRules=Ansicht Geräteregeln
acc_dev_ruleContentTip=Zwischen mehrfachen Regeln durch '|‘ getrennt.
acc_dev_rs485AddrFigure=RS485 Adresscode-Abbildung
acc_dev_addLevel=Zu Ebene hinzufügen
acc_dev_personOrFingerTanto=Die Anzahl des Personals oder der Fingerabdrücke übersteigt die Obergrenze, Synchronisation fehlgeschlagen...
acc_dev_personAndFingerUnit=(Zahl)
acc_dev_setDstime=Sommerzeit einstellen
acc_dev_setTimeZone=Zeitzone des Geräts einstellen
acc_dev_selectedTZ=Ausgewählte Zeitzone
acc_dev_timeZoneSetting=Zeitzone Einstellung...
acc_dev_timeZoneCmdSuccess=Zeitzone Befehl senden erfolgreich...
acc_dev_enableDstime=Sommerzeit aktivieren
acc_dev_disableDstime=Sommerzeit deaktivieren
acc_dev_timeZone=Zeitzone
acc_dev_dstSettingTip=Sommerzeit-Einstellung...
acc_dev_dstDelTip=Sommerzeit von Gerät entfernen
acc_dev_enablingDst=Sommerzeit aktivieren
acc_dev_dstEnableCmdSuccess=Befehl Sommerzeit aktivieren senden erfolgreich.
acc_dev_disablingDst=Sommerzeit deaktivieren
acc_dev_dstDisableCmdSuccess=Befehl Sommerzeit aktivieren senden deaktivieren.
acc_dev_dstCmdSuccess=Befehl Sommerzeit senden erfolgreich...
acc_dev_usadst=Sommerzeit
acc_dev_notSetDst=Keine Einstellungen
acc_dev_selectedDst=Ausgewählte Sommerzeit
acc_dev_configMasterSlave=Master-Slave-Konfiguration
acc_dev_hasFilterByUnOnline=Nur Online-Gerät anzeigen.
acc_dev_softwareData=Wenn die Daten nicht mit Gerät übereinstimmen, bitte Daten der Geräte vor erneutem Versuch synchronisieren.
acc_dev_disabled=Die deaktivierten Geräte können nicht bedient werden!
acc_dev_offline=Die Offline-Geräte können nicht bedient werden!
acc_dev_noSupport=Die Geräte unterstützen diese Funktion nicht und können nicht bedient werden!
acc_dev_noRegDevTip=Dieses Gerät ist nicht als Erfassungsgerät definiert. Die Daten in der Software werden nicht aktualisiert. Möchten Sie fortfahren?
acc_dev_noOption=Keine geeigneten Optionen
acc_dev_devFWUpdatePrompt=Das aktuelle Gerät wird die deaktivierte-Personen-Funktion und die gültige-Personen-Funktion normalerweise nicht ausführen (bitte konsultieren Sie die Bedienungsanleitung).
acc_dev_panelFWUpdatePrompt=Das aktuelle Gerät wird die deaktivierte-Personenfunktion und die gültige-Personenfunktion nicht normal ausführen, Firmware jetzt aktualisieren?
acc_dev_sendEventCmdSuccess=Befehl Ereignis löschen erfolgreich gesendet
acc_dev_tryAgain=Bitte versuchen Sie es erneut.
acc_dev_eventAutoCheckAndUpload=Automatische Überprüfung und Ereignisse hochladen
acc_dev_eventUploadStart=Hochladen von Ereignissen starten.
acc_dev_eventUploadEnd=Hochladen von Ereignissen abgeschlossen.
acc_dev_eventUploadFailed=Hochladen von Ereignissen fehlgeschlagen.
acc_dev_eventUploadPrompt=Die Version der Geräte-Firmware ist zu alt, vor dem Firmware-Upgrade möchten Sie:
acc_dev_backupToSoftware=Daten in Software sichern
acc_dev_deleteEvent=Alten Datensatz löschen
acc_dev_upgradePrompt=Die Firmware-Version ist zu alt und könnte Fehler verursachen, bitte sichern Sie die Daten vor dem Firmware-Upgrade in der Software.
acc_dev_conflictCardNo=Karte im System ist zu {0} in der anderen Person!
acc_dev_rebootAfterOperate=Der Vorgang war erfolgreich, das Gerät wird später neu gestartet.
acc_dev_baseOptionTip=Abweichung in der erhaltenen Basisgröße.
acc_dev_funOptionTip=Abweichung im erhaltenen Funktionsparameter.
acc_dev_sendComandoTip=Befehl Geräteparameter erhalten nicht gesendet.
acc_dev_noC3LicenseTip=Dieser Gerätetyp kann nicht hinzugefügt werden ({0})! Bitte kontaktieren Sie unser Vertriebsteam.
acc_dev_combOpenDoorTip=({0}) Türöffnung durch mehrere Personen wurde konfiguriert, kann nicht gleichzeitig mit Hintergrund-Prüfung benutzt werden.
acc_dev_combOpenDoorPersonCountTip=Gruppe {0} kann nicht mehr als {1} öffnen!
acc_dev_addDevTip=Dies gilt nur für das Hinzufügen eines Geräts mit PULL Kommunikationsprotokoll!
acc_dev_addError=Gerät Ausnahme hinzufügen, fehlende Parameter ({0})!
acc_dev_updateIPAndPortError=Fehler bei der Aktualisierung von Server IP- und Anschluss...
acc_dev_transferFilesTip=Firmware-Test abgeschlossen, Daten werden übertragen
acc_dev_serialPortExist=Serielle Schnittstelle existent
acc_dev_isExist=Existierende Geräts
acc_dev_description=Beschreibung
acc_dev_searchEthernet=Ethernet-Gerät durchsuchen
acc_dev_searchRS485=RS485-Gerät durchsuchen
acc_dev_rs485AddrTip1=RS485-Startadresse darf nicht länger als die Endadresse sein
acc_dev_rs485AddrTip2=Der RS485-Durchsuchungsbereich muss unter 20 sein.
acc_dev_clearAllCmdCache=Alles löschen Befehl
acc_dev_authorizedSuccessful=Erfolgreich autorisiert
acc_dev_authorize=Autorisieren
acc_dev_registrationDevice=Erfassungsgerät
acc_dev_setRegistrationDevice=Als Erfassungsgerät konfigurieren
acc_dev_mismatchedDevice=Dieses Gerät kann nicht auf Ihrem Markt eingesetzt werden. Überprüfen Sie bitte die Seriennummer des Geräts mit unserem Vertrieb.
acc_dev_pwdStartWithZero=Kommunikations-Passwort darf nicht mit Null beginnen!
acc_dev_maybeDisabled=Die aktuelle Lizenz erlaubt Ihnen nur noch das Hinzufügen von {0} Tür(en), die Tür des neu hinzugefügten Geräts, die die Türobergrenze der Lizenz überschreitet, wird deaktiviert, möchten Sie fortfahren?
acc_dev_Limit=Gerätelizenzschlüssel hat die Obergrenze erreicht, bitte autorisieren.
acc_dev_selectDev=Bitte das Gerät auswählen!
acc_dev_cannotAddPullDevice=Pull-Gerät kann nicht hinzugefügt werden! Bitte wenden Sie sich an unseren Vertrieb.
acc_dev_notContinueAddPullDevice={0} Pull-Geräte im System, wieder hinzufügen nicht möglich! Bitte wenden Sie sich an unseren Vertrieb.
acc_dev_deviceNameNull=Gerätemodell leer, Gerät hinzufügen nicht möglich!
acc_dev_commTypeErr=Kommunikationstyp stimmt nicht überein, Gerät hinzufügen nicht möglich!
acc_dev_inputDomainError=Bitte geben Sie eine gültige Domain-Adresse ein.
acc_dev_levelTip=Es sind mehr als 5000 Personen auf der Ebene, Hinzufügen von Gerät zu Ebene hier nicht möglich.
acc_dev_auxinSet=Einstellung Zusatzeingang
acc_dev_verifyModeRule=Verifizierungsmodus-Regelung
acc_dev_netModeWired=Verdrahtet
acc_dev_netMode4G=4G
acc_dev_netModeWifi=WLAN
acc_dev_updateNetConnectMode=Netzwerkverbindung wechseln
acc_dev_wirelessSSID=Drahtlos SSID
acc_dev_wirelessKey=Funkschlüssel
acc_dev_searchWifi=WLAN suchen
acc_dev_testNetConnectSuccess=Ist die Verbindung erfolgreich?
acc_dev_testNetConnectFailed=Die Verbindung kann nicht korrekt kommunizieren!
acc_dev_signalIntensity=Signalstärke
acc_dev_resetSearch=Erneut suchen
acc_dev_addChildDevice=Teilgerät hinzufügen
acc_dev_modParentDevice=Master-Gerät ändern
acc_dev_configParentDevice=Einstellung Master-Gerät
acc_dev_lookUpChildDevice=Ansicht untergeordnete Geräte
acc_dev_addChildDeviceTip=Autorisierung muss unter autorisiertem Teilgerät ausgeführt werden
acc_dev_maxSubCount=Die Anzahl autorisierter Teilgeräte überschreitet die Höchstzahl von {0} Einheiten.
acc_dev_seletParentDevice=Bitte Master-Gerät auswählen!
acc_dev_networkCard=Netzwerkkarte
acc_dev_issueParam=Benutzerdefinierte Synchronisierungsparameter
acc_dev_issueMode=Modus Synchronisieren
acc_dev_initIssue=Firmware Version 3030 Initialisierungsdaten
acc_dev_customIssue=Benutzerdefinierte Synchronisierungsdaten
acc_dev_issueData=Daten
acc_dev_parent=Master-Gerät
acc_dev_parentEnable=Master-Gerät deaktiviert
acc_dev_parentTips=Die Anbindung des Master-Geräts wird alle Daten des Geräts löschen und wird neu konfiguriert werden müssen.
acc_dev_addDevIpTip=Die neue IP-Adresse kann nicht mit der Server-IP-Adresse identisch sein
acc_dev_modifyDevIpTip=Die neue Server-Adresse kann nicht mit der IP-Adresse identisch sein
acc_dev_setWGReader=Wiegand-Lesegerät einstellen
acc_dev_selectReader=Zum Auswählen des Lesegeräts klicken
acc_dev_IllegalDevice=Unzulässiges Gerät
acc_dev_syncTimeWarnTip=Synchronisationszeiten für die folgenden Geräte sollten auf dem Master-Gerät synchronisiert werden.
acc_dev_setTimeZoneWarnTip=Die Zeitzone von folgendem Gerät sollte auf dem Master-Gerät synchronisiert werden.
acc_dev_setDstimeWarnTip=Die Sommerzeit für die folgenden Geräte sollte auf dem Master-Gerät synchronisiert werden.
acc_dev_networkSegmentSame=Zwei Netzwerkadapter können nicht dasselbe Netzwerksegment nutzen.
acc_dev_upgradeProtocolNoMatch=Aktualisiertes Dateiprotokoll passt nicht
acc_dev_ipAddressConflict=Das Gerät mit derselben IP-Adresse existiert bereits. Bitte ändern Sie die Geräte-IP und fügen Sie sie erneut hinzu.
acc_dev_checkServerPortTip=Der konfigurierte Server-Anschluss stimmt nicht mit dem System-Kommunikationsanschluss überein, was zu Problemen beim Hinzufügen führen könnte. Vorgang fortsetzen?
acc_dev_clearAdmin=Administratorberechtigung entfernen
acc_dev_setDevSate=Einstellung Gerätestatus In/Out
acc_dev_sureToClear=Sind Sie sicher, dass Sie die Administratorberechtigungen löschen möchten?
acc_dev_hostState=Status Master-Gerät
acc_dev_regDeviceTypeTip=Dieses Gerät ist beschränkt und darf nicht direkt hinzufügen. Bitte wenden Sie sich an den Softwareanbieter
acc_dev_extBoardType=I/O Board Type
acc_dev_extBoardTip=Nach der Konfiguration müssen Sie das Gerät neu starten, um wirksam zu werden.
acc_dev_extBoardLimit=Nur {0} I/O board von diesem Typ können pro Gerät hinzugefühgt werden!
acc_dev_replace=Gerät ersetzen
acc_dev_replaceTip=Nach dem Austausch funktioniert das alte Gerät nicht, bitte seien Sie vorsichtig!
acc_dev_replaceTip1=Führen Sie nach dem Austausch bitte die Operation „Alle Daten synchronisieren“ durch;
acc_dev_replaceTip2=Bitte stellen Sie sicher, dass das Modell des Ersatzgeräts dasselbe ist!
acc_dev_replaceTip3=Bitte stellen Sie sicher, dass das Ersatzgerät die gleiche Serveradresse und den gleichen Port wie das alte Gerät eingestellt hat!
acc_dev_replaceFail=Der Gerätemaschinentyp ist inkonsistent und kann nicht ersetzt werden!
acc_dev_notApb=Dieses Gerät ist nicht in der Lage, Gate- oder Lesekopf-Anti-U-Boot-Operationen durchzuführen
acc_dev_upResourceFile=Ressourcendateien hochladen
acc_dev_playOrder=Spielreihenfolge
acc_dev_setFaceServerInfo=Parameter für den Vergleich des Gesichtsbackends festlegen
acc_dev_faceVerifyMode=Gesichtsvergleichsmodus
acc_dev_faceVerifyMode1=Lokaler Vergleich
acc_dev_faceVerifyMode2=Backend Vergleich
acc_dev_faceVerifyMode3=Priorität des lokalen Vergleichs
acc_dev_faceBgServerType=Typ des Facial Backend Servers
acc_dev_faceBgServerType1=Software Platform Services
acc_dev_faceBgServerType2=Dienstleistungen Dritter
acc_dev_isAccessLogic=Überprüfung der Zugriffskontrolllogik aktivieren
#[5]门-其他关联的也复用此处
acc_door_entity=Tür
acc_door_number=Türnummer
acc_door_name=Türname
acc_door_activeTimeZone=Aktive Zeitzone
acc_door_passageModeTimeZone=Durchgangsmodus Zeitraum
acc_door_setPassageModeTimeZone=Einstellung Durchgangsmodus Zeitzone
acc_door_notPassageModeTimeZone=Kein-Durchgangsmodus Zeitzone
acc_door_lockOpenDuration=Dauer Entsperrung
acc_door_entranceApbDuration=Anti-Passback Eintrittsdauer
acc_door_sensor=Türsensor
acc_door_sensorType=Art des Türsensors
acc_door_normalOpen=Normalöffnung
acc_door_normalClose=Normalschließung
acc_door_sensorDelay=Verzögerung Türsensor
acc_door_closeAndReverseState=Rücklaufsperrzustand bei Türschließung
acc_door_hostOutState=Host-Zugangs-Status
acc_door_slaveOutState=Gerätezustand Slave
acc_door_inState=Zugang
acc_door_outState=Out
acc_door_requestToExit=REX-Modus
acc_door_withoutUnlock=Zuschließen
acc_door_unlocking=Aufschließen
acc_door_alarmDelay=REX-Verzögerung
acc_door_duressPassword=Zwangscode
acc_door_currentDoor=Aktuelle Tür
acc_door_allDoorOfCurDev=Alle Türen in aktuellem Gerät
acc_door_allDoorOfAllDev=Alle Türen in allen Geräten
acc_door_allDoorOfAllControlDev=Alle Türen in allen Kontrollgeräten
acc_door_allDoorOfAllStandaloneDev=Alle Türen in allen Standalone-Geräten
acc_door_allWirelessLock=Alle Funkverriegelungen
acc_door_max6BitInteger=Maximal 6 Bit Integer
acc_door_direction=Richtung
acc_door_onlyInReader=Nur Eingangslesegerät
acc_door_bothInAndOutReader=Ein- und Ausgangslesegeräte
acc_door_noDoor=Bitte die Tür hinzufügen
acc_door_nameRepeat=Die Türnamen sind dupliziert.
acc_door_duressPwdError=Der Zwangscode darf mit keinem der persönlichen Passwörter identisch sein.
acc_door_urgencyStatePwd=Bitte {0} Bit Integer eingeben!
acc_door_noDevOnline=Keines der Geräte ist online oder die Tür unterstützt Kartenverifizierungsmodus nicht.
acc_door_durationLessLock=Die Türsensor-Verzögerung muss länger als die Dauer der Entsperrung sein.
acc_door_lockMoreDuration=Die Dauer der Entsperrung muss kürzer als die Türsensor Verzögerung sein.
acc_door_lockAndExtLessDuration=Die Gesamtdauer von Entsperrung und Durchgangsverzögerung muss kürzer als die Türsensor-Verzögerung sein.
acc_door_noDevTrigger=Das Gerät erfüllt nicht die Bedingungen!
acc_door_relay=Relais
acc_door_pin=Pin
acc_door_selDoor=Tür auswählen
acc_door_sensorStatus=Türsensor ({0})
acc_door_sensorDelaySeconds=Türsensor-Verzögerung ({0} s)
acc_door_timeSeg=Zeitzone ({0})
acc_door_combOpenInterval=Mehrpersonenbedienungsintervall
acc_door_delayOpenTime=Verzögerung Türöffnung
acc_door_extDelayDrivertime=Durchgangsverzögerung
acc_door_enableAudio=Alarm aktivieren
acc_door_disableAudio=Alarm deaktivieren
acc_door_lockAndExtDelayTip=Die Gesamtdauer von Entsperrung und Durchgangsverzögerungsintervall ist nicht länger als 254 Sekunden.
acc_door_disabled=Die deaktivierten Türen können nicht bedient werden!
acc_door_offline=Die Offline-Türen können nicht bedient werden!
acc_door_notSupport=Die folgenden Türen unterstützen diese Funktion nicht!
acc_door_select=Tür auswählen
acc_door_pushMaxCount=Es sind {0} aktivierte Türen im System und haben die Lizenz-Obergrenze erreicht! Bitte kontaktieren Sie unser Vertriebsteam.
acc_door_outNumber=Die aktuelle Lizenz erlaubt Ihnen das Hinzufügen von {0} weiteren Tür(en). Bitte wählen Sie die Türen erneut aus und versuchen Sie es nochmal oder kontaktieren Sie unser Vertriebsteam, um eine Lizenz-Aktualisierung zu erwerben.
acc_door_latchTimeZone=REX-Zeitzone
acc_door_wgFmtReverse=Kartennummer-Stornierung
acc_door_allowSUAccessLock=Superuser-Zutritt bei Sperrung erlauben
acc_door_verifyModeSinglePwd=Passwörter können nicht als unabhängige Verifizierungsmethode verwendet werden!
acc_door_doorPassword=Türöffnungspasswort
#辅助输入
acc_auxIn_timeZone=Aktive Zeitzone
#辅助输出
acc_auxOut_passageModeTimeZone=Durchgangsmodus Zeitraum
acc_auxOut_disabled=Der deaktivierte Zusatzausgang kann nicht bedient werden!
acc_auxOut_offline=Der Offline-Zusatzausgang kann nicht bedient werden!
#[8]门禁权限组
acc_level_doorGroup=Türkombination
acc_level_openingPersonnel=Öffnungspersonal
acc_level_noDoor=Es gibt keinen verfügbaren Artikel. Bitte erst Gerät hinzufügen.
acc_level_doorRequired=Tür muss ausgewählt werden.
acc_level_doorCount=Tür-Count
acc_level_doorDelete=Tür löschen
acc_level_isAddDoor=Sofort Türen zur aktuellen Zugangskontrollebene hinzufügen?
acc_level_master=Master
acc_level_noneSelect=Bitte Zugangskontrollebene hinzufügen
acc_level_useDefaultLevel=Zugangsebene auf neue Abteilung übertragen?
acc_level_persAccSet=Einstellungen Personal-Zugangskontrolle
acc_level_visUsed={0} werden bereits vom Besucher-Modul genutzt und können nicht gelöscht werden!
acc_level_doorControl=Tür Kontrolle
acc_level_personExceedMax=Die Anzahl der aktuellen Berechtigungsgruppen ({0}) und die maximale Anzahl optionaler Berechtigungsgruppen ({1})
acc_level_exportLevel=Zugriffsebene exportieren
acc_level_exportLevelDoor=Türen der Zugangsebene exportieren
acc_level_exportLevelPerson=Personal der Zugriffsebene exportieren
acc_level_importLevel=Zugriffsebene importieren
acc_level_importLevelDoor=Türen der Zugangsebene importieren
acc_level_importLevelPerson=Personal der Zugriffsebene importieren
acc_level_exportDoorFileName=Türinformationen der Zugriffsebene
acc_level_exportPersonFileName=Personalinformationen der Zugriffsebene
acc_levelImport_nameNotNull=Name der Zugriffsebene darf nicht leer sein
acc_levelImport_timeSegNameNotNull=Zeitzone darf nicht leer sein
acc_levelImport_areaNotExist=Gebiet existiert nicht!
acc_levelImport_timeSegNotExist=Zeitzone existiert nicht!
acc_levelImport_nameExist=Name der Zugriffsebene {0} existiert bereits!
acc_levelImport_levelDoorExist=Die Türen der Zugriffsebene {0} existieren bereits!
acc_levelImport_levelPersonExist=Das Personal der Zugriffsebene {0} existiert bereits!
acc_levelImport_noSpecialChar=Name der Zugriffsebene darf keine Sonderzeichen enthalten!
#[10]首人常开
acc_firstOpen_setting=Erste Normalöffnung durch Einzelperson
acc_firstOpen_browsePerson=Personal durchsuchen
#[11]多人组合开门
acc_combOpen_comboName=Kombinationsname
acc_combOpen_personGroupName=Gruppenname
acc_combOpen_personGroup=Mehrpersonen Gruppe
acc_combOpen_verifyOneTime=Aktueller Personal-Count
acc_combOpen_eachGroupCount=Anzahl Öffnungspersonal in jeder Gruppe
acc_combOpen_group=Gruppe
acc_combOpen_changeLevel=Türöffnungsgruppe
acc_combOpen_combDeleteGroup=Hinweis auf existierende Öffungskombination, bitte erst löschen.
acc_combOpen_ownedLevel=Belong-Gruppen
acc_combOpen_mostPersonCount=Gruppencount darf fünf Personen nicht überschreiten.
acc_combOpen_leastPersonCount=Gruppencount darf zwei Personen nicht unterschreiten.
acc_combOpen_groupNameRepeat=Gruppenname doppelt.
acc_combOpen_groupNotUnique=Türöffnungsgruppen dürfen nicht identisch sein.
acc_combOpen_persNumErr=Die Gruppengröße überschreitet den tatsächlichen Wert Ihrer Auswahl, bitte wählen Sie erneut aus!
acc_combOpen_combOpengGroupPersonShort=Nachdem Personen aus der Gruppe entfernt wurden, ist die Größe der Türöffnungsgruppe zu klein, bitte Öffnungsgruppe erst löschen.
acc_combOpen_backgroundVerifyTip=Die Tür ist im Gerät mit aktivierter Hintergrund-Prüfung; kann nicht gleichzeitig mit Mehrpersonen-Türöffnung benutzt werden!
#[12]互锁
acc_interlock_rule=Sperr-Regelung
acc_interlock_mode1Or2=Sperre zwischen {0} und {1}
acc_interlock_mode3=Sperre zwischen {0} und {1} und {2}
acc_interlock_mode4=Sperre zwischen {0} und {1} oder zwischen {2} und {3}
acc_interlock_mode5=Sperre zwischen {0} und {1} und {2} und {3}
acc_interlock_hasBeenSet=Sperr-Programm-Einstellungen vorhanden
acc_interlock_group1=Gruppe 1
acc_interlock_group2=Gruppe 2
acc_interlock_ruleInfo=Intergruppenübergreifende Verzahnung
acc_interlock_alreadyExists=Die gleiche Sperrregel existiert bereits, bitte nicht noch einmal hinzufügen!
acc_interlock_groupInterlockCountErr=Die gruppeninterne Verriegelung erfordert mindestens zwei Türen
acc_interlock_ruleType=Regeltyp für Interlocking
#[13]反潜
acc_apb_rules=Anti-Passback-Regelung
acc_apb_reader=Anti-Passback zwischen Türlesegeräten {0}
acc_apb_reader2=Anti-Passback zwischen Türlesegeräten: {0}, {1}
acc_apb_reader3=Anti-Passback zwischen Türlesegeräten: {0}, {1}, {2}
acc_apb_reader4=Anti-Passback zwischen Türlesegeräten: {0}, {1}, {2}, {3}
acc_apb_reader5=Anti-Passback Auslesegerät an der Tür {0}
acc_apb_reader6=Anti-Passback Einlesegerät an der Tür {0}
acc_apb_reader7=Anti-Passback zwischen Türlesegeräten an allen 4 Türen
acc_apb_twoDoor=Anti-Passback zwischen {0} und {1}
acc_apb_fourDoor=Anti-Passback zwischen {0} und {1}, Anti-Passback zwischen {2} und {3}
acc_apb_fourDoor2=Anti-Passback zwischen {0} oder {1} und {2} oder {3}
acc_apb_fourDoor3=Anti-Passback zwischen {0} und {1} oder {2}
acc_apb_fourDoor4=Anti-Passback zwischen {0} und {1} oder {2} oder {3}
acc_apb_hasBeenSet=Wurde auf Anti-Passback eingestellt
acc_apb_conflictWithGapb=Dieses Gerät hat allgemeine Anti-Passback-Einstellungen und kann nicht geändert werden!
acc_apb_conflictWithApb=Bereich des Geräts hat Anti-Passback-Einstellungen und kann nicht geändert werden!
acc_apb_conflictWithEntranceApb=Bereich des Geräts hat Eingangs-Anti-Passback-Einstellungen, die nicht geändert werden können!
acc_apb_controlIn=Anti-Passback In
acc_apb_controlOut=Anti-Passback Out
acc_apb_controlInOut=Anti-Passback In/Out
acc_apb_groupIn=Treten Sie Der Gruppe Bei
acc_apb_groupOut=Außerhalb der Gruppe
acc_apb_reverseName=Rückwärts-U-Boot
acc_apb_door=Gate Anti-U-Boot
acc_apb_readerHead=Den Kopf lesen, um dem U-Boot entgegenzuwirken
acc_apb_alreadyExists=Die gleiche Anti-U-Boot-Regel existiert bereits, bitte fügen Sie sie nicht noch einmal hinzu!
#[17]电子地图
acc_map_addDoor=Tür hinzufügen
acc_map_addChannel=Kamera hinzufügen
acc_map_noAccess=Keine Erlaubnis für den Zugang zum elektronischen Karten-Modul, bitte kontaktieren Sie den Administrator!
acc_map_noAreaAccess=Sie haben keine E-Map Erlaubnis für dieses Gebiet, bitte kontaktieren Sie den Administrator!
acc_map_imgSizeError=Bitte laden Sie das Bild hoch, dessen Größe kleiner als {0} M ist!
#[18]门禁事件记录
acc_trans_entity=Transaktion
acc_trans_eventType=Ereignistyp
acc_trans_firmwareEvent=Firmware-Ereignisse
acc_trans_softwareEvent=Software-Ereignis
acc_trans_today=Ereignisse von heute
acc_trans_lastAddr=Zuletzt bekannte Position
acc_trans_viewPhotos=Fotos ansehen
acc_trans_exportPhoto=Fotos exportieren
acc_trans_dayNumber=Tage
acc_trans_photo=Vorfallfoto der Zugriffskontrolle
acc_trans_fileIsTooLarge=Die exportierte Datei ist zu groß. Bitte reduzieren Sie den Exportbereich
#[19]门禁验证方式
acc_verify_mode_onlyface=Gesicht
acc_verify_mode_facefp=Gesicht+Fingerabdruck
acc_verify_mode_facepwd=Gesicht+Passwort
acc_verify_mode_facecard=Gesicht+Karte
acc_verify_mode_facefpcard=Gesicht+Fingerabdruck+Karte
acc_verify_mode_facefppwd=Gesicht+Fingerabdruck+Passwort
acc_verify_mode_fv=Fingervene
acc_verify_mode_fvpwd=Fingervene+Passwort
acc_verify_mode_fvcard=Fingervene+Karte
acc_verify_mode_fvpwdcard=Fingervene+Passwort+Karte
acc_verify_mode_pv=Palme
acc_verify_mode_pvcard=Palme+Karte
acc_verify_mode_pvface=Palme+Gesicht
acc_verify_mode_pvfp=Palme+Fingerabdruck
acc_verify_mode_pvfacefp=Palme+Gesicht+Fingerabdruck
#[20]门禁事件编号
acc_eventNo_-1=Kein
acc_eventNo_0=Normalöffnung mit Magnetkarte
acc_eventNo_1=Mit Magnetkarte im Zeitraum des Durchgangsmodus
acc_eventNo_2=Erste Normalöffnung mit Karte(Magnetstreifenkarte)
acc_eventNo_3=Öffnung durch mehrere Personen(Magnetstreifenkarte)
acc_eventNo_4=Öffnung mit Notfallpasswort
acc_eventNo_5=Öffnung im Zeitraum des Durchgangsmodus
acc_eventNo_6=Verknüpfungsereignis ausgelöst
acc_eventNo_7=Alarm abbrechen
acc_eventNo_8=Fernöffnung
acc_eventNo_9=Fernschließung
acc_eventNo_10=Innertags-Durchgangsmodus-Zeitraum deaktivieren
acc_eventNo_11=Innertags-Durchgangsmodus Zeitraum aktivieren
acc_eventNo_12=Zusatzausgang aus der Ferne geöffnet
acc_eventNo_13=Zusatzausgang aus der Ferne geschlossen
acc_eventNo_14=Normalöffnung mit Fingerabdruck
acc_eventNo_15=Öffnung durch mehrere Personen(Fingerabdruck)
acc_eventNo_16=Fingerabdruck während Durchgangsmodus-Zeitraum erfassen
acc_eventNo_17=Öffnung mit Karte plus Fingerabdruck
acc_eventNo_18=Erste Normalöffnung mit Karte(Fingerabdruck erfassen)
acc_eventNo_19=Erste Normalöffnung mit Karte(Karte plus Fingerabdruck)
acc_eventNo_20=Vorgangsintervall zu kurz
acc_eventNo_21=Zeitfenster Tür inaktiv(Magnetstreifenkarte)
acc_eventNo_22=Unzulässiges Zeitfenster
acc_eventNo_23=Zugang verweigert
acc_eventNo_24=Anti-Passback (Zutrittswiederholsperre)
acc_eventNo_25=Sperre
acc_eventNo_26=Authentifizierung durch mehrere Personen(Magnetstreifenkarte)
acc_eventNo_27=Deaktivierte Karte
acc_eventNo_28=Erweiterte Türöffnung Zeitüberschreitung
acc_eventNo_29=Karte abgelaufen
acc_eventNo_30=Passwort-Fehler
acc_eventNo_31=Intervall Fingerabdruck erfassen zu kurz
acc_eventNo_32=Authentifizierung durch mehrere Personen(Fingerabdruck erfassen)
acc_eventNo_33=Fingerabdruck nicht mehr gültig
acc_eventNo_34=Deaktivierter Fingerabdruck
acc_eventNo_35=Zeitfenster Tür inaktiv(Fingerabdruck erfassen)
acc_eventNo_36=Tür inaktiv Zeitfenster (Exit-Button drücken)
acc_eventNo_37=Schließen während Durchgangsmodus-Zeitfenster fehlgeschlagen
acc_eventNo_38=Karte als verloren gemeldet
acc_eventNo_39=Zugriff deaktiviert
acc_eventNo_40=Authentifizierung durch mehrere Personen fehlgeschlagen(Fingerabdruck erfassen)
acc_eventNo_41=Verifizierungsmodus-Fehler
acc_eventNo_42=Wiegand-Format-Fehler
acc_eventNo_43=Zeitüberschreitung Anti-Passback-Verifizierung
acc_eventNo_44=Hintergrund-Prüfung fehlgeschlagen
acc_eventNo_45=Hintergrund-Prüfung Zeitüberschreitung
acc_eventNo_47=Der Befehl konnte nicht gesendet werden
acc_eventNo_48=Authentifizierung durch mehrere Personen fehlgeschlagen(Magnetstreifenkarte)
acc_eventNo_49=Zeitfenster Tür inaktiv(Passwort)
acc_eventNo_50=Intervall Passwort erfassen zu kurz
acc_eventNo_51=Authentifizierung durch mehrere Personen(Passwort)
acc_eventNo_52=Authentifizierung durch mehrere Personen schlägt fehl(Passwort)
acc_eventNo_53=Passwort nicht mehr gültig
acc_eventNo_100=Sabotage-Alarm
acc_eventNo_101=Öffnung mit Zwangscode
acc_eventNo_102=Tür gewaltsam geöffnet
acc_eventNo_103=Erzwungene Öffnung mit Fingerabdruck
acc_eventNo_200=Tür korrekt geöffnet
acc_eventNo_201=Tür korrekt geschlossen
acc_eventNo_202=Öffnung mit Exit-Button
acc_eventNo_203=Öffnung durch mehrere Personen(Karte plus Fingerabdruck)
acc_eventNo_204=Durchgangsmodus-Zeitfenster beendet
acc_eventNo_205=Normale Fernöffnung
acc_eventNo_206=Gerät gestartet
acc_eventNo_207=Öffnung mit Passwort
acc_eventNo_208=Türöffnung durch Superuser
acc_eventNo_209=Exit-Button ausgelöst(Ohne Entsperrung)
acc_eventNo_210=Feuertür starten
acc_eventNo_211=Türschließung durch Superuser
acc_eventNo_212=Aufzugs-Kontrollfunktion aktivieren
acc_eventNo_213=Aufzugs-Kontrollfunktion deaktivieren
acc_eventNo_214=Öffnung durch mehrere Personen(Passwort)
acc_eventNo_215=Erste Normalöffnung mit Karte(Passwort)
acc_eventNo_216=Passwort während Durchgangsmodus-Zeitfenster
acc_eventNo_220=Zusatzeingang getrennt (Geöffnet)
acc_eventNo_221=Zusatzeingang kurzgeschlossen (Geschlossen)
acc_eventNo_222=Hintergrund-Prüfung erfolgreich
acc_eventNo_223=Hintergrund-Prüfung
acc_eventNo_225=Zusatzeingang normal
acc_eventNo_226=Auslöser Zusatzeingang
acc_newEventNo_0=Normalöffnung durch Verifizieren
acc_newEventNo_1=Verifizieren während Durchgangsmodus-Zeitfenster
acc_newEventNo_2=Erstöffnung durch Personal
acc_newEventNo_3=Öffnung durch mehrere Mitarbeiter
acc_newEventNo_20=Vorgangsintervall zu kurz
acc_newEventNo_21=Öffnung durch Verifizieren im Tür inaktiv Zeitfenster
acc_newEventNo_26=Verifizierung durch mehrere Mitarbeiter Warten
acc_newEventNo_27=Nicht erfasstes Personal
acc_newEventNo_29=Personalstatus nicht mehr gültig
acc_newEventNo_30=Passwort-Fehler
acc_newEventNo_41=Verifizierungsmodus-Fehler
acc_newEventNo_43=Staff Lock
acc_newEventNo_44=Hintergrund-Prüfung fehlgeschlagen
acc_newEventNo_45=Hintergrund-Prüfung Zeitüberschreitung
acc_newEventNo_48=Authentifizierung durch mehrere Mitarbeiter fehlgeschlagen
acc_newEventNo_54=Die Akkuspannung ist zu niedrig
acc_newEventNo_55=Akku sofort austauschen
acc_newEventNo_56=Unzulässiger Vorgang
acc_newEventNo_57=Reservestrom
acc_newEventNo_58=Normalöffnung Alarm
acc_newEventNo_59=Unzulässige Steuerung
acc_newEventNo_60=Tür von innen verriegelt
acc_newEventNo_61=Kopiert
acc_newEventNo_62=User verbieten
acc_newEventNo_63=Tür verriegelt
acc_newEventNo_64=Exit-Button-Zeitfenster deaktiviert
acc_newEventNo_65=Zusatzeingang-Zeitfenster deaktiviert
acc_newEventNo_66=Lesegerät-Upgrade fehlgeschlagen
acc_newEventNo_67=Remote-Vergleich erfolgreich (Gerät nicht autorisiert)
acc_newEventNo_68=Hohe Körpertemperatur - Zugriff verweigert
acc_newEventNo_69=Ohne Maske - Zugriff verweigert
acc_newEventNo_70=Kommunikationsausnahme des Gesichtsvergleichsservers
acc_newEventNo_71=Der Gesichtsserver reagiert unregelmäßig
acc_newEventNo_73=Ungültiger QR-Code
acc_newEventNo_74=QR-Code abgelaufen
acc_newEventNo_101=Zwangsöffnung Alarm
acc_newEventNo_104=Ungültige Magnetstreifenkarte Alarm
acc_newEventNo_105=Kann nicht mit Server verbunden werden
acc_newEventNo_106=Hauptstromausfall
acc_newEventNo_107=Akkubetriebsausfall
acc_newEventNo_108=Kann nicht mit Master-Gerät verbunden werden
acc_newEventNo_109=Leser Tamper Alarm
acc_newEventNo_110=Leser Offline
acc_newEventNo_112=Erweiterungsplatte offline
acc_newEventNo_114=Brandalarmeingang getrennt (Leitungserkennung)
acc_newEventNo_115=Kurzschluss des Brandmeldeingangs (Leitungserkennung)
acc_newEventNo_116=Hilfseingang getrennt (Leitungserkennung)
acc_newEventNo_117=Kurzschluss des Nebeneingangs (Leitungserkennung)
acc_newEventNo_118=Ausgangsschalter getrennt (Leitungserkennung)
acc_newEventNo_119=Exit Schalter Kurzschluss (Schaltungserkennung)
acc_newEventNo_120=Türmagnettrennung (Leitungserkennung)
acc_newEventNo_121=Tür magnetischer Kurzschluss (Leitungserkennung)
acc_newEventNo_159=Türöffnung durch Fernbedienung
acc_newEventNo_214=Mit dem Server verbunden
acc_newEventNo_217=Erfolgreich mit Master-Gerät verbunden
acc_newEventNo_218=Ausweis-Kartenverifizierung
acc_newEventNo_222=Hintergrund-Prüfung erfolgreich
acc_newEventNo_223=Hintergrund-Prüfung
acc_newEventNo_224=Läuten Sie die Glocke
acc_newEventNo_227=Zweifache Türöffnung
acc_newEventNo_228=Zweifache Türschließung
acc_newEventNo_229=Zusatzausgang zeitgesteuert normalerweise offen
acc_newEventNo_230=Zusatzausgang zeitgesteuert geschlossen
acc_newEventNo_232=Prüfung erfolgreich
acc_newEventNo_233=Sperrmodus aktivieren
acc_newEventNo_234=Sperrmodus deaktivieren
acc_newEventNo_235=Lesegerät-Upgrade erfolgreich
acc_newEventNo_236=Leser Tamper Alarm gelöscht
acc_newEventNo_237=Leser Online
acc_newEventNo_239=Geräteaufruf
acc_newEventNo_240=Anruf beendet
acc_newEventNo_243=Brandmeldeeingang getrennt
acc_newEventNo_244=Kurzschluss des Brandmeldeeingangs
acc_newEventNo_247=Erweiterungsplatte online
acc_newEventNo_4008=Netzwiederkehr
acc_newEventNo_4014=Feuereingangssignal getrennt, Endtür normalerweise geöffnet
acc_newEventNo_4015=Die Tür ist online
acc_newEventNo_4018=Backend Vergleich Öffnen
acc_newEventNo_5023=Eingeschränkter Brandschutzstatus
acc_newEventNo_5024=Timeout für mehrere Personen überprüfen
acc_newEventNo_5029=Backend-Vergleich fehlgeschlagen
acc_newEventNo_6005=Die Aufnahmekapazität stößt an ihre Grenzen
acc_newEventNo_6006=Leitungs-Kurzschluss (RS485)
acc_newEventNo_6007=Kurzschluss im Stromkreis (Wigan)
acc_newEventNo_6011=Die Tür ist offline
acc_newEventNo_6012=Alarm zur Türdemontage
acc_newEventNo_6013=Feuereingangssignal ausgelöst, Tür offen
acc_newEventNo_6015=Setzen Sie die Stromversorgung des Erweiterungsgeräts zurück
acc_newEventNo_6016=Werkseinstellungen der Maschine wiederherstellen
acc_newEventNo_6070=Backend-Vergleich (Verbotene Liste)
acc_eventNo_undefined=Undefinierte Ereignis-Nummer
acc_advanceEvent_500=Allgemeiner Anti-Passback(logisch)
acc_advanceEvent_501=Personen-Verfügbarkeit(nach Datum)
acc_advanceEvent_502=Personen-Kontrollnummer
acc_advanceEvent_503=Allgemeine Sperre
acc_advanceEvent_504=Wegstreckenkontrolle
acc_advanceEvent_505=Allgemeiner Anti-Passback(zeitgesteuert)
acc_advanceEvent_506=Allgemeiner Anti-Passback(logisch zeitgesteuert)
acc_advanceEvent_507=Personen-Verfügbarkeit(nach Erstnutzung gültiger Tage)
acc_advanceEvent_508=Personen-Verfügbarkeit(nach Häufigkeit)
acc_advanceEvent_509=Hintergrund-Prüfung schlägt fehl(nicht erfasstes Personal)
acc_advanceEvent_510=Hintergrund-Prüfung schlägt fehl(Datenausnahme)
acc_alarmEvent_701=DMR-Verstoß(Konfigurationsregelungen: {0})
#[21]实时监控
acc_rtMonitor_openDoor=Öffnen
acc_rtMonitor_closeDoor=Schließen
acc_rtMonitor_remoteNormalOpen=Normale Fernöffnung
acc_rtMonitor_realTimeEvent=Ereignisse in Echtzeit
acc_rtMonitor_photoMonitor=Foto-Überwachung
acc_rtMonitor_alarmMonitor=Alarm-Überwachung
acc_rtMonitor_doorState=Tür-Zustand
acc_rtMonitor_auxOutName=Zusatzausgang Name
acc_rtMonitor_nonsupport=Nicht unterstützt
acc_rtMonitor_lock=Verriegelt
acc_rtMonitor_unLock=Entriegelt
acc_rtMonitor_disable=Deaktiviert
acc_rtMonitor_noSensor=Kein Türsensor
acc_rtMonitor_alarm=Alarm
acc_rtMonitor_openForce=Gewaltsam geöffnet
acc_rtMonitor_tamper=Manipulation
acc_rtMonitor_duressPwdOpen=Öffnung mit Zwangscode
acc_rtMonitor_duressFingerOpen=Erzwungene Öffnung mit Fingerabdruck
acc_rtMonitor_duressOpen=Zwangsöffnung
acc_rtMonitor_openTimeout=Öffnung Zeitüberschreitung
acc_rtMonitor_unknown=Unbekannt
acc_rtMonitor_noLegalDoor=Keine Tür erfüllt die Bedingungen.
acc_rtMonitor_noLegalAuxOut=Kein Zusatzausgang erfüllt die Bedingungen!
acc_rtMonitor_curDevNotSupportOp=Aktueller Gerätezustand unterstützt diesen Vorgang nicht!
acc_rtMonitor_curNormalOpen=Aktuell Normalöffnung
acc_rtMonitor_whetherDisableTimeZone=Der aktuelle Status der Tür ist immer geöffnet.
acc_rtMonitor_curSystemNoDoors=Das aktuelle System hat keine Tür hinzugefügt oder findet keine Ihren Anforderungen entsprechende Tür.
acc_rtMonitor_cancelAlarm=Alarm abbrechen
acc_rtMonitor_openAllDoor=Alle aktuellen Türen öffnen
acc_rtMonitor_closeAllDoor=Alle aktuellen Türen schließen
acc_rtMonitor_confirmCancelAlarm=Sind Sie sicher, dass Sie diesen Alarm abbrechen möchten?
acc_rtMonitor_calcelAllDoor=Alle Alarme abbrechen
acc_rtMonitor_initDoorStateTip=Alle Türen, die für Benutzer im System autorisiert sind, erhalten...
acc_rtMonitor_alarmEvent=Alarm-Ereignis
acc_rtMonitor_ackAlarm=Bestätigen
acc_rtMonitor_ackAllAlarm=Alles bestätigen
acc_rtMonitor_ackAlarmTime=Zeit bestätigen
acc_rtMonitor_sureToAckThese=Sind Sie sicher, dass Sie diesen {0} Alarm bestätigen möchten? Nach Ihrer Bestätigung werden alle Alarme abgebrochen.
acc_rtMonitor_sureToAckAllAlarm=Sind Sie sicher, dass Sie alle Alarme abbrechen möchten? Nach Ihrer Bestätigung werden alle Alarme abgebrochen.
acc_rtMonitor_noSelectAlarmEvent=Bitte Bestätigung des Alarm-Ereignisses auswählen!
acc_rtMonitor_noAlarmEvent=Keine Alarm-Ereignisse im aktuellen System!
acc_rtMonitor_forcefully=Alarm abbrechen (Tür gewaltsam geöffnet)
acc_rtMonitor_addToRegPerson=Zur erfassten Person hinzugefügt
acc_rtMonitor_cardExist=Diese Karte wurde von {0} zugewiesen und kann nicht noch einmal ausgegeben werden.
acc_rtMonitor_opResultPrompt=Anfrage {0} erfolgreich gesendet, {1} fehlgeschlagen.
acc_rtMonitor_doorOpFailedPrompt=Senden von Anfragen an folgende Türen fehlgeschlagen, bitte versuchen Sie es erneut!
acc_rtMonitor_remoteOpen=Fernöffnung
acc_rtMonitor_remoteClose=Fernschließung
acc_rtMonitor_alarmSoundClose=Audio geschlossen
acc_rtMonitor_alarmSoundOpen=Audio geöffnet
acc_rtMonitor_playAudio=Ereignis-Reminder-Sounds
acc_rtMonitor_isOpenShowPhoto=Foto-Anzeige aktivieren
acc_rtMonitor_isOpenPlayAudio=Warnton aktivieren
acc_rtm_open=Button per Fernsteuerung öffnen
acc_rtm_close=Button per Fernsteuerung schließen
acc_rtm_eleModule=Aufzug
acc_cancelAlarm_fp=Alarm abbrechen (Zwangsöffnung mit Fingerabdruck)
acc_cancelAlarm_pwd=Alarm abbrechen (Zwangsöffnung mit Passwort)
acc_cancelAlarm_timeOut=Alarm abbrechen (Erweiterte Türöffnung Zeitüberschreitung)
#定时同步设备时间
acc_timing_syncDevTime=Zeitsteuerung der Synchronisierung Zeitgerät
acc_timing_executionTime=Die Ausführungsfrist
acc_timing_theLifecycle=Die Lebensdauer
acc_timing_errorPrompt=Falsche Eingabe.
acc_timing_checkedSyncTime=Bitte wählen Sie einen Synchronisierungszeitpunkt aus.
#[25]门禁报表
acc_trans_hasAccLevel=Zugangs-Ebene vorhanden
#以下为门禁高级功能国际化
#[G1]zone门禁区域
acc_zone_addZone=Bitte Bereich hinzufügen
acc_zone_code=Bereichscode
acc_zone_parentZone=Übergeordneter Bereich
acc_zone_parentZoneCode=Übergeordneter Bereich Code
acc_zone_parentZoneName=Übergeordneter Bereich Name
acc_zone_outside=Außen
#[G2]读头定义
acc_readerDefine_readerName=Lesegerät Name
acc_readerDefine_fromZone=Geht von
acc_readerDefine_toZone=Geht nach
acc_readerDefine_delInfo1=Eine erweiterte Funktionen zur Zugangskontrolle verweist auf den Bereich dieses Lesegeräts und kann nicht gelöscht werden!
acc_readerDefine_selReader=Lesegerät auswählen
acc_readerDefine_selectReader=Bitte Lesegerät hinzufügen!
acc_readerDefine_tip=Sobald Personal Bereich verlässt, wird die Personalakte gelöscht.
#[G3]全局反潜
acc_gapb_zone=Bereich
acc_gapb_whenToResetGapb=Anti-Passback Zeit zurücksetzen
acc_gapb_apbType=Anti-Passback Typ
acc_gapb_logicalAPB=Logischer Anti-Passback
acc_gapb_timedAPB=Zeitgesteuerter Anti-Passback
acc_gapb_logicalTimedAPB=Zeitgesteuerter logischer Anti-Passback
acc_gapb_lockoutDuration=Sperrdauer
acc_gapb_devOfflineRule=Falls das Gerät offline ist
acc_gapb_standardLevel=Standard Zugangsebene
acc_gapb_accessDenied=Zugang verweigert
acc_gapb_doorControlZone=Die folgenden Türen kontrollieren Zu- und Ausgang des Bereichs
acc_gapb_resetStatus=Anti-Passback Status zurücksetzen
acc_gapb_obeyAPB=Anti-Passback Regel befolgen
acc_gapb_isResetGAPB=Anti-Passback zurücksetzen
acc_gapb_resetGAPBSuccess=Anti-Passback Status zurücksetzen erfolgreich
acc_gapb_resetGAPBFaile=Anti-Passback Status zurücksetzen fehlgeschlagen
acc_gapb_chooseArea=Bitte Bereich neu auswählen.
acc_gapb_notDelInfo1=Der Zugangsbereich wird als Superior-Zugangsbereich angegeben und kann nicht gelöscht werden.
acc_gapb_notDelInfo2=Auf den Bereich wird durch Reader-Define verwiesen und kann nicht gelöscht werden.
acc_gapb_notDelInfo3=Auf den Bereich wird durch Erweiterte Funktionen zur Zugangskontrolle verwiesen und kann nicht gelöscht werden!
acc_gapb_notDelInfo4=Auf den Zugangsbereich wird durch LED verwiesen und kann nicht gelöscht werden.
acc_gapb_zoneNumRepeat=Duplikate unter den Kontroll-Domain Nummern
acc_gapb_zoneNameRepeat=Duplikate unter den Kontroll-Domain Namen
acc_gapb_personResetGapbPre=Sind Sie sicher, dass Sie zurücksetzen möchten?
acc_gapb_personResetGapbSuffix=Personen-Anti-Passback-Regeln?
acc_gapb_apbPrompt=Eine einzelne Tür kann nicht zur Kontrolle von zwei relativ unabhängigen Abgrenzungen eingesetzt werden.
acc_gapb_occurApb=Anti-Passback findet statt
acc_gapb_noOpenDoor=Tür nicht öffnen
acc_gapb_openDoor=Tür öffnen
acc_gapb_zoneNumLength=Länge über 20 Zeichen
acc_gapb_zoneNameLength=Länge über 30 Zeichen
acc_gapb_zoneRemarkLength=Länge über 50 Zeichen
acc_gapb_isAutoServerMode=Gerät ohne Hintergrund-Prüffunktion entdeckt, Funktion möglicherweise beeinträchtigt. Jetzt öffnen?
acc_gapb_applyTo=Anwenden auf
acc_gapb_allPerson=Gesamtes Personal
acc_gapb_justSelected=Nur ausgewähltes Personal
acc_gapb_excludeSelected=Ausgewähltes Personal ausschließen
#[G4]who is inside
acc_zoneInside_lastAccessTime=Letzte Zugangszeit
acc_zoneInside_lastAccessReader=Letztes Zugangslesegerät
acc_zoneInside_noPersonInZone=Keine Person im Bereich
acc_zoneInside_noRulesInZone=Keine Regeln festgelegt
acc_zoneInside_totalPeople=Gesamtanzahl Personen
acc_zonePerson_selectPerson=Bitte Person oder Abteilung auswählen!
#[G5]路径
acc_route_name=Streckenname
acc_route_setting=Strecken-Konfiguration
acc_route_addReader=Lesegerät hinzufügen
acc_route_delReader=Lesegerät löschen
acc_route_defineReaderLine=Lesegerät-Linie definieren
acc_route_up=Up
acc_route_down=Down
acc_route_selReader=Nach dem Vorgang Lesegerät auswählen.
acc_route_onlyOneOper=Sie können nur ein Lesegerät für den Vorgang auswählen.
acc_route_readerOrder=Lesegerät-definierter Abschnitt
acc_route_atLeastSelectOne=Bitte mindestens einen Abtastkopf auswählen!
acc_route_routeIsExist=Diese Strecke existiert bereits!
#[G6]DMR
acc_dmr_residenceTime=Verweilzeit
acc_dmr_setting=DMR-Einstellung
#[G7]Occupancy
acc_occupancy_max=Maximale Kapazität
acc_occupancy_min=Minimale Kapazität
acc_occupancy_unlimit=Unbegrenzt
acc_occupancy_note=Minimale Sitzplatzkapazität darf maximale Sitzplatzkapazität nicht überschreiten.
acc_occupancy_containNote=Bitte mindestens eine der Maximal/Minimal-Kapazitäten festlegen!
acc_occupancy_maxMinValid=Bitte Kapazität höher als 0 festlegen.
acc_occupancy_conflict=Die Präsenzkontrollregeln wurden in diesem Bereich festgelegt.
acc_occupancy_maxMinTip=Kein Kapazitätswert heißt keine Begrenzung.
#card availability
acc_personLimit_zonePropertyName=Bereichseigenschaft Name
acc_personLimit_useType=Nutzung
acc_personLimit_userDate=Gültiges Datum
acc_personLimit_useDays=Nach Erstnutzung gültiger Tage
acc_personLimit_useTimes=Häufigkeit nutzen
acc_personLimit_setZoneProperty=Bereichseigenschaft festlegen
acc_personLimit_zoneProperty=Bereichseigenschaft
acc_personLimit_availabilityName=Verfügbarkeit Name
acc_personLimit_days=Tage
acc_personLimit_Times=Anzahl
acc_personLimit_noDel=Ausgewählte Bereichseigenschaften der Zugangskontrolle sind referenziert und können nicht gelöscht werden!
acc_personLimit_cannotEdit=Das Zugangsbereich-Kennzeichen ist referenziert und kann nicht geändert werden!
acc_personLimit_detail=Detail
acc_personLimit_userDateTo=Gültig bis
acc_personLimit_addPersonRepeatTip=Die Person unter der ausgewählten Abteilung wurde dem Attribut des Zugriffskontrollbereichs hinzugefügt. Bitte wählen Sie die Abteilung erneut aus!
acc_personLimit_leftTimes={0} verbleibende Zeit
acc_personLimit_expired=abgelaufen
acc_personLimit_unused=Ungebraucht
#全局互锁
acc_globalInterlock_addGroup=Gruppe hinzufügen
acc_globalInterlock_delGroup=Gruppe löschen
acc_globalInterlock_refuseAddGroupMessage=Identische Sperrung, Tür kann nicht innerhalb hinzugefügter Gruppe dupliziert werden
acc_globalInterlock_refuseAddlockMessage=Die hinzugefügte Tür erscheint in anderen Gruppen gesperrter Geräte
acc_globalInterlock_refuseDeleteGroupMessage=Bitte sperrbezogene Daten löschen
acc_globalInterlock_isGroupInterlock=Gruppensperrung
acc_globalInterlock_isAddTheDoorImmediately=Tür sofort hinzufügen
acc_globalInterlock_isAddTheGroupImmediately=Gruppe sofort hinzufügen
#门禁参数设置
acc_param_autoEventDev=Automatischer Download des Ereignisprotokolls der Anzahl simultaner Geräte
acc_param_autoEventTime=Automatischer Download des Ereignisprotokolls der Anzahl simultaner Intervalle
acc_param_noRepeat=Wiederholung von E-Mail Adressen unzulässig, bitte erneut ausfüllen.
acc_param_most18=Bis zu 18 E-Mail Adressen hinzufügen
acc_param_deleteAlert=Löschen von gesamtem E-Mail Eingabefeld nicht möglich.
acc_param_invalidOrRepeat=Formatfehler oder Wiederholung der E-Mail Adresse.
#全局联动
acc_globalLinkage_noSupport=Die ausgewählte Tür unterstützt weder Sperrfunktion noch Sperrfunktion deaktivieren.
acc_globalLinkage_trigger=Allgemeine Verknüpfung auslösen
acc_globalLinkage_noAddPerson=Die Verknüpfungsregelung enthält standardmäßig keinen personalbezogenen und auf gesamtes Personal anwendbaren Auslöser!
acc_globalLinkage_selectAtLeastOne=Bitte mindestens einen Verknüpfungsausgabepunkt und Videoverknüpfung auswählen!
acc_globalLinkage_selectTrigger=Bitte Bedingungen für Verknüpfungs-Auslöser hinzufügen!
acc_globalLinkage_selectInput=Bitte Eingangspunkt hinzufügen!
acc_globalLinkage_selectOutput=Bitte Ausgangspunkt hinzufügen!
acc_globalLinkage_audioRemind=Verknüpfungs-Ansagen
acc_globalLinkage_audio=Verknüpfungs-Stimme
acc_globalLinkage_isApplyToAll=Auf gesamtes Personal anwenden
acc_globalLinkage_scope=Personal Reichweite
acc_globalLinkage_everyPerson=Beliebig
acc_globalLinkage_selectedPerson=Ausgewählt
acc_globalLinkage_noSupportPerson=Nicht unterstützt
acc_globalLinkage_reselectInput=Die Art der Trigger-Bedingungen haben sich geändert, bitte Eingangspunkt neu wählen!
acc_globalLinkage_addPushDevice=Bitte Unterstützung für diese Gerätefunktion hinzufügen
#其他
acc_InputMethod_tips=Bitte zu Eingabemodus Englisch wechseln!
acc_device_systemCheckTip=Zugangsgerät existiert nicht!
acc_notReturnMsg=Keine Information zurückgesendet
acc_validity_period=Die Lizenz hat ihre Gültigkeit verloren, die Funktion kann nicht verwenden werden.
acc_device_pushMaxCount=System existiert bereits in {0} Gerät(en), Lizenz-Obergrenze erreicht, kann Gerät nicht hinzufügen!
acc_device_videoHardwareLinkage=Video-Hardware-Verknüpfung einrichten
acc_device_videoCameraIP=Videokamera-IP
acc_device_videoCameraPort=Videokamera-Anschluss
acc_location_unable=Der Ort des Vorfalls wurde nicht zur elektronischen Karte hinzugefügt, spezifischer Lage kann nicht gefunden werden!
acc_device_wgDevMaxCount=Das Gerät hat die Lizenz-Obergrenze im System erreicht und kann die Einstellungen nicht ändern!
#自定义报警事件
acc_deviceEvent_selectSound=Bitte Audiodateien auswählen.
acc_deviceEvent_batchSetSoundErr=Eingestellte Alarmtöne abweichend.
acc_deviceEvent_batchSet=Audio konfigurieren
acc_deviceEvent_sound=Ereignis-Sound
acc_deviceEvent_exist=Existiert bereits
acc_deviceEvent_upload=Hochladen
#查询门最近发生事件
acc_doorEventLatestHappen=Letzte Ereignisse von der Tür abfragen
#门禁人员信息
acc_pers_delayPassage=Durchgang verzögern
#设备容量提示
acc_dev_usageConfirm=Aktuelle Kapazität höher als 90% der Anlage.
acc_dev_immediateCheck=Sofort überprüfen
acc_dev_inSoftware=In Software
acc_dev_inFirmware=In Firmware
acc_dev_get=Abrufen
acc_dev_getAll=Alles abrufen
acc_dev_loadError=Ladefehler
#Reader
acc_reader_inout=In/Out
acc_reader_lightRule=Lichtregelungen
acc_reader_defLightRule=Standardregelung
acc_reader_encrypt=Verschlüsseln
acc_reader_allReaderOfCurDev=Alle Lesegeräte im aktuellen Gerät
acc_reader_tip1=Die Verschlüsselung wird in alle Lesegeräte im aktuellen Gerät kopiert!
acc_reader_tip2=ID-Modus-Option nur verfügbar für Abtastköpfe, die diese Funktion unterstützen!
acc_reader_tip3=Der RS485-Protokolltyp wird auf alle Lesegeräte im aktuellen Gerät kopiert. Die Einstellungen werden nach dem Neustart des Geräts wirksam!
acc_reader_tip4=Die Option zum Ausblenden einiger Personalinformationen wird standardmäßig auf alle Lesegeräte desselben Geräts kopiert!
acc_reader_commType=Kommunikationstyp
acc_reader_commAddress=Kommunikationsadresse
acc_readerCommType_rs485=RS485
acc_readerCommType_wiegand=Wiegand
acc_readerCommType_wg485=Wiegand/RS485
acc_readerCommType_disable=Deaktiviert
acc_readerComAddress_repeat=Kommunikationsadresse duplizieren
acc_readerCommType_tcp=TCP/IP
acc_readerCommType_zigbee=Zigbee
acc_readerCommType_rs485Address=RS485-Adresse
acc_readerCommType_wgAddress=Wiegand-Adresse
acc_reader_macError=Bitte Mac-Adresse in korrektem Format eingeben!
acc_reader_machineType=Lesegerät-Typ
acc_reader_readMode=Modus
acc_reader_readMode_normal=Normalmodus
acc_reader_readMode_idCard=ID-Kartenmodus
acc_reader_note=Hinweise: Zugangskontrollgeräte nur im gleichen Bereich ({0}), in dem eine Kamera ausgewählt werden kann.
acc_reader_rs485Type=RS485-Protokolltyp
acc_reader_userLock=Personenzugriffssperre
acc_reader_userInfoReveal=Teil Personalinformationen verbergen
#operat
acc_operation_pwd=Passwort für Vorgang
acc_operation_pwd_error=Passwort-Fehler
acc_new_input_not_same=Neue Schlüsseleingabe ist unstimmig!
acc_op_set_keyword=Lizenzschlüssel-Einstellungen
acc_op_old_key=Alter Schlüssel
acc_op_new_key=Neuer Schlüssel
acc_op_cofirm_key=Schlüssel bestätigen
acc_op_old_key_error=Fehler alter Schlüssel
#验证方式规则
acc_verifyRule_name=Name der Regelung
acc_verifyRule_door=Türprüfung
acc_verifyRule_person=Personalprüfung
acc_verifyRule_copy=Einstellungen von Montag auf andere Wochentage kopieren
acc_verifyRule_tip1=Bitte mindestens einen Prüfmodus auswählen!
acc_verifyRule_tip2=Wenn die Regelung einen Personenprüfmodus beinhaltet, können sie nur eine Tür mit Wiegand-Lesegerät hinzufügen!
acc_verifyRule_tip3=RS485-Lesegerät kann nur dem Türprüfmodus folgen, unterstützt nicht Personenprüfmodus.
acc_verifyRule_oldVerifyMode=Alter Überprüfungsmodus
acc_verifyRule_newVerifyMode=Neuer Überprüfungsmodus
acc_verifyRule_newVerifyModeSelectTitle=Neue Überprüfungsmethode auswählen
acc_verifyRule_newVerifyModeNoSupportTip=Es gibt kein Gerät, das die neue Überprüfungsmethode unterstützt!
#Wiegand Test
acc_wiegand_beforeCard=Die Länge der neuen Karte ({0} Bits) stimmt nicht mit der vorherigen Karte überein!
acc_wiegand_curentCount=Aktuelle Länge der Kartennummer: {0} Bits
acc_wiegand_card=Karte
acc_wiegand_readCard=Karte lesen
acc_wiegand_clearCardInfo=Karteninformation entfernen
acc_wiegand_originalCard=Original-Kartennummer
acc_wiegand_recommendFmt=Empfohlenes Kartenformat
acc_wiegand_parityFmt=Paritätsformat gerade/ungerade
acc_wiegand_withSizeCode=Automatische Berechnung des Adressschlüssels während Adressschlüssel ausgelassen wird
acc_wiegand_tip1=Diese Karte gehört eventuell nicht zur selben Kartenserie.
acc_wiegand_tip2=Adressschlüssel:{0}, Kartennummer:{1}, Übereinstimmung mit der Original-Kartennummer fehlgeschlagen. Bitte eingegebenen Adressschlüssel und Kartennummer erneut überprüfen!
acc_wiegand_tip3=Die eingegebene Kartennummer: ({0}), passt nicht in die Original-Kartennummer. Bitte erneut überprüfen!
acc_wiegand_tip4=Der eingegebene Adressschlüssel ({0}) stimmt nicht mit der Original-Kartennummer überein. Bitte erneut überprüfen!
acc_wiegand_tip5=Wenn Sie diese Funktion nutzen möchten, lassen Sie bitte die Adressschlüssel-Spalten frei!
acc_wiegand_warnInfo1=Wenn Sie weiterhin eine neue Karte lesen, wechseln Sie bitte manuell zur nächsten Karte.
#LCD实时监控
acc_leftMenu_LCDRTMonitor=Personal In/Out Tafel
acc_LCDRTMonitor_current=Aktuelle Personaldaten
acc_LCDRTMonitor_previous=Vorige Personaldaten
#api
acc_api_levelIdNotNull=Ausweisnummer der Berechtigungsgruppe darf nicht frei bleiben
acc_api_levelNotExist=Berechtigungsgruppe existiert nicht
acc_api_levelExist=Berechtigungsgruppe existiert
acc_api_areaNameNotNull=Region kann nicht leer sein
acc_api_levelNotHasPerson=Keine Personen unter der Berechtigungsgruppe
acc_api_doorIdNotNull=Tür-Identifikationsnummer darf nicht frei bleiben
acc_api_doorNameNotNull=Türname darf nicht frei bleiben
acc_api_doorIntervalSize=Türöffnungsdauer sollte zwischen 1~254 sein
acc_api_doorNotExist=Tür existiert nicht
acc_api_devOffline=Gerät ist offline oder deaktiviert
acc_api_devSnNotNull=Gerät SN darf nicht frei bleiben
acc_api_timesTampNotNull=Der Zeitstempel kann nicht leer sein
acc_api_openingTimeCannotBeNull=Die Türöffnungszeit darf nicht leer sein
acc_api_parameterValueCannotBeNull=Der Parameterwert darf nicht leer sein
acc_api_deviceNumberDoesNotExist=Die Seriennummer des Geräts existiert nicht
acc_api_readerIdCannotBeNull=Die Leser-ID darf nicht leer sein
acc_api_theReaderDoesNotExist=Lesekopf existiert nicht
acc_operate_door_notInValidDate=Es befindet sich derzeit nicht innerhalb der effektiven Zeit des Remote-Öffnens. Bitte wenden Sie sich an den Administrator, wenn Sie es benötigen!
acc_api_doorOffline=Tür ist offline oder deaktiviert
#门禁信息自动导出
acc_autoExport_title=Transaktionen automatisch exportieren
acc_autoExport_frequencyTitle=Auto-Export Frequenz
acc_autoExport_frequencyDay=Jeden Tag
acc_autoExport_frequencyMonth=Jeden Monat
acc_autoExport_firstDayMonth=Am erten jeden Monat
acc_autoExport_specificDate=Specific Date
acc_autoExport_exportModeTitle=Export Mode
acc_autoExport_dailyMode=Tägliche Transactions
acc_autoExport_monthlyMode=Monatliche Transaktionen (Alle Transaktionen zwischen dem Datum des letzten Monats und diesem Monat)
acc_autoExport_allMode=Alle Daten (Export von bis zu 30000 Datenstücken)
acc_autoExport_recipientMail=Mailbox des Empfängers
#First In und Last Out
acc_inOut_inReaderName=Erster In-Reader-Name
acc_inOut_firstInTime=First in Time
acc_inOut_outReaderName=Letzter Out-Reader-Name
acc_inOut_lastOutTime=Last Out Time
# 防疫 参数
acc_dev_setHep=Masken- und Temperaturerkennungsparameter einstellen
acc_dev_enableIRTempDetection=Temperaturprüfung mit Infrarot aktivieren
acc_dev_enableNormalIRTempPass=Zugriff verweigern, wenn die Temperatur über dem Bereich liegt
acc_dev_enableMaskDetection=Maskenerkennung aktivieren
acc_dev_enableWearMaskPass=Zugriff ohne Maske verweigern
acc_dev_tempHighThreshold=Hochtemperatur-Alarmschwelle
acc_dev_tempUnit=Temperatureinheit
acc_dev_tempCorrection=Temperaturabweichungskorrektur
acc_dev_enableUnregisterPass=Nicht registrierten Personen den Zugriff erlauben
acc_dev_enableTriggerAlarm=Externen Alarm auslösen
# 联动 邮件
acc_mail_temperature=Körpertemperatur
acc_mail_mask=Gibt an, ob die Maske getragen werden soll
acc_mail_unmeasured=Nicht gemessen
#Digifort 联动
acc_digifort_linkage=Digifort
acc_digifort_globalEvent=Digifort Global Events
acc_digifort_chooseDigifortEvents=Wählen Sie Digifort Global Events
acc_digifort_eventExpiredTip=Wenn das globale Ereignis vom Digifort-Server gelöscht wird, wird es rot angezeigt.
acc_digifort_checkConnection=Bitte überprüfen Sie, ob die Verbindungsinformationen des Digifort-Servers korrekt sind.
#line消息推送联动
acc_line_linkage=Line
acc_line_addContact=Kontakte hinzufügen
#WhatsApp联动
acc_whatsapp_linkage=WhatsApp
# 扩展 参数
acc_dev_setExtendParam=Erweiterte Parameter festlegen
acc_extendParam_faceUI=Schnittstellenanzeige
acc_extendParam_faceParam=Gesichtsparameter
acc_extendParam_accParam=Zugriffssteuerungsparameter
acc_extendParam_intercomParam=Visuelle Intercom-Parameter
acc_extendParam_volume=Volume
acc_extendParam_identInterval=Identifikationsintervall (ms)
acc_extendParam_historyVerifyResult=Historische Überprüfungsergebnisse anzeigen
acc_extendParam_macAddress=MAC-Adresse anzeigen
acc_extendParam_showIp=IP-Adresse anzeigen
acc_extendParam_24HourFormat=24-Stunden-Format anzeigen
acc_extendParam_dateFormat=Datumsformat
acc_extendParam_1NThreshold=1: N Schwelle
acc_extendParam_facePitchAngle=Neigungswinkel des Gesichts
acc_extendParam_faceRotationAngle=Gesichtsdrehwinkel
acc_extendParam_imageQuality=Bildqualität
acc_extendParam_miniFacePixel=Minimales Gesichtspixel
acc_extendParam_biopsy=Biopsie aktivieren
acc_extendParam_showThermalImage=Wärmebild anzeigen
acc_extendParam_attributeAnalysis=Attributanalyse aktivieren
acc_extendParam_temperatureAttribute=Attribut zur Temperaturerkennung
acc_extendParam_maskAttribute=Maskenerkennungsattribut
acc_extendParam_minTemperature=Min. Temperatur
acc_extendParam_maxTemperature=Maximale Temperatur
acc_extendParam_gateMode=Gate-Modus
acc_extendParam_qrcodeEnable=Aktiviere QR-Code-Funktion
# 可视 对讲
acc_dev_intercomServer=Adresse der visuellen Gegensprechanlage
acc_dev_intercomPort=Visuelle Gegensprechanlage
#权限组-按人员设置
acc_personLevelByPerson_syncLevel=Synchronisierte Berechtigungsgruppe
# 夏令时名称
acc_dsTimeUtc_none=Nicht gesetzt
acc_dsTimeUtc_AreaNone=Es gibt keine Sommerzeit in diesem Bereich
acc_dsTimeUtc1000_0=Canberra, Melbourne, Sydney
acc_dsTimeUtc1000_1=HobartName
acc_dsTimeUtc_0330_0=Neufundland
acc_dsTimeUtc_1000_0=Aleutische Inseln
acc_dsTimeUtc_0200_0=Mittlerer Atlantik
acc_dsTimeUtc0930_0=Adelaide.
acc_dsTimeUtc_0100_0=Azoren
acc_dsTimeUtc_0400_0=Atlantische Zeit (Kanada)
acc_dsTimeUtc_0400_1=Santiago
acc_dsTimeUtc_0400_2=Asuncion.kgm
acc_dsTimeUtc_0300_0=Grönland
acc_dsTimeUtc_0300_1=Saint Pierre und Miquelon Inseln
acc_dsTimeUtc0200_0=Chisinau
acc_dsTimeUtc0200_1=Helsinki, Kiew, Riga, Sofia, Tallinn, Vilnius
acc_dsTimeUtc0200_2=Athen, Bukarest
acc_dsTimeUtc0200_3=Jerusalem
acc_dsTimeUtc0200_4=Amman.
acc_dsTimeUtc0200_5=Beirut
acc_dsTimeUtc0200_6=Damaskus
acc_dsTimeUtc0200_7=Gaza, Hebron
acc_dsTimeUtc0200_8=Juba
acc_dsTimeUtc_0600_0=Zentralzeit (USA und Kanada)
acc_dsTimeUtc_0600_1=Guadalajara, Mexico City, Monterrey
acc_dsTimeUtc_0600_2=Insel Ostern
acc_dsTimeUtc1300_0=Samoa Inseln
acc_dsTimeUtc_0500_0=Havanna
acc_dsTimeUtc_0500_1=Ostzeit (USA und Kanada)
acc_dsTimeUtc_0500_2=Haiti
acc_dsTimeUtc_0500_3=Indiana (Ost)
acc_dsTimeUtc_0500_4=Turks- und Caicosinseln
acc_dsTimeUtc_0800_0=Pazifik (USA und Kanada)
acc_dsTimeUtc_0800_1=Baja California
acc_dsTimeUtc0330_0=Teheran oder Tehran
acc_dsTimeUtc0000_0=Dublin, Edinburgh, Lissabon, London
acc_dsTimeUtc1200_0=Fidschi
acc_dsTimeUtc1200_1=Petropavlovsk Kamtschatka alt
acc_dsTimeUtc1200_2=Wellington, Auckland
acc_dsTimeUtc1100_0=Insel Norfolk
acc_dsTimeUtc_0700_0=Chihuahua, La Paz, Mazatlan
acc_dsTimeUtc_0700_1=Bergzeit (USA und Kanada)
acc_dsTimeUtc0100_0=Belgrad, Bratislava, Budapest, Ljubljana, Prag
acc_dsTimeUtc0100_1=Sarajevo, Skopje, Warschau, Zagreb
acc_dsTimeUtc0100_2=Casablanca.kgm
acc_dsTimeUtc0100_3=Brüssel, Kopenhagen, Madrid, Paris
acc_dsTimeUtc0100_4=Amsterdam, Berlin, Bern, Rom, Stockholm, Wien
acc_dsTimeUtc_0900_0=Alaska.
#安全点(muster point)
acc_leftMenu_accMusterPoint=Musterpunkt
acc_musterPoint_activate=Aktivieren
acc_musterPoint_addDept=Abteilung hinzufügen
acc_musterPoint_delDept=Abteilung löschen
acc_musterPoint_report=Musterpunktbericht
acc_musterPointReport_sign=Manuell anmelden
acc_musterPointReport_generate=Erzeuge Berichte
acc_musterPoint_addSignPoint=Sign-Point hinzufügen
acc_musterPoint_delSignPoint=Zeichenpunkt löschen
acc_musterPoint_selectSignPoint=Bitte einen Schilderpunkt hinzufügen!
acc_musterPoint_signPoint=Signaturpunkt
acc_musterPoint_delFailTip=Es sind bereits Musterpunkte aktiviert und können nicht gelöscht werden!
acc_musterPointReport_enterTime=Zeit eingeben
acc_musterPointReport_dataAnalysis=Datenanalyse
acc_musterPointReport_safe=Sicher
acc_musterPointReport_danger=Gefahr
acc_musterPointReport_signInManually=manueller Stempel
acc_musterPoint_editTip=Der Musterpunkt ist aktiv und kann nicht bearbeitet werden!
acc_musterPointEmail_total=Erwartete Anwesenheit:
acc_musterPointEmail_safe=Eingecheckt (Safe):
acc_musterPointEmail_dangerous=In Gefahr:
acc_musterPoint_messageNotification=Meldung bei Aktivierung
acc_musterPointReport_sendEmail=Geplanter Push-Bericht
acc_musterPointReport_sendInterval=Sende Intervall
acc_musterPointReport_sendTip=Bitte stellen Sie sicher, dass die gewählte Benachrichtigungsmethode erfolgreich konfiguriert ist, sonst wird die Benachrichtigung nicht normal gesendet!
acc_musterPoint_mailSubject=Notmontagemeldung
acc_musterPoint_mailContent=Bitte versammeln Sie sich sofort bei "{0}" und melden Sie sich am Gerät "{1}" an, danke!
acc_musterPointReport_mailHead=Hi, hier ist ein Notfallbericht. Bitte überprüfen.
acc_musterPoint_visitorsStatistics=Besucherstatistiken
# 报警监控
acc_alarm_priority=Priorität
acc_alarm_total=in insgesamt
acc_alarm_today=Der heutige Rekord
acc_alarm_unhandled=Unbestätigt
acc_alarm_inProcess=Im Prozess
acc_alarm_acknowledged=Bestätigt
acc_alarm_top5=Top fünf Alarmereignisse
acc_alarm_monitoringTime=Zeit der Überwachung
acc_alarm_history=Historie der Alarmverarbeitung
acc_alarm_acknowledgement=Verarbeitung von Aufzeichnungen
acc_alarm_eventDescription=Details der Veranstaltung
acc_alarm_acknowledgeText=Nach der Auswahl werden die Details des Ereignisses an die angegebene Mailbox gesendet.
acc_alarm_emailSubject=Datensatz für die Bearbeitung von Alarmereignissen hinzufügen
acc_alarm_mute=Mute
acc_alarm_suspend=Suspend
acc_alarm_confirmed=Der Vorfall wurde bestätigt
acc_alarm_list=Alarmprotokoll
#ntp
acc_device_setNTPService=NTP-Servereinstellungen
acc_device_setNTPServiceTip=Geben Sie mehrere Serveradressen ein, getrennt durch Komma (,) oder Semikolon (;)
#人事设置门禁属性页面相关提示于
acc_editPerson_adminTip=Im Zutrittskontrollbetrieb ist ein Super-Benutzer nicht durch die Vorschriften zu Zeitzonen, Anti-Passback und Verriegelung eingeschränkt und hat eine extrem hohe Türöffnungspriorität.
acc_editPerson_delayPassageTip=Verlängern Sie die Wartezeit für das Personal durch die Zugangspunkte. Geeignet für körperlich Behinderte oder Menschen mit anderen Behinderungen.
acc_editPerson_disabledTip=Deaktivieren Sie vorübergehend die Zugangsebene des Personals.
#门禁向导
acc_guide_title=Einrichtungsassistent für das Zugriffskontrollmodul
acc_guide_addPersonTip=Sie müssen die Person und die entsprechenden Anmeldeinformationen (Gesicht oder Fingerabdruck oder Karte oder Handfläche oder Passwort) hinzufügen. Wenn Sie diese bereits hinzugefügt haben, überspringen Sie diesen Schritt direkt
acc_guide_timesegTip=Bitte konfigurieren Sie einen gültigen Öffnungszeitraum
acc_guide_addDeviceTip=Bitte fügen Sie das entsprechende Gerät als Zugangspunkt hinzu
acc_guide_addLevelTip=Zugriffskontrollebene hinzufügen
acc_guide_personLevelTip=Weisen Sie der Person die entsprechende Zugriffskontrollberechtigung zu
acc_guide_rtMonitorTip=Überprüfen Sie die Zugriffskontrolldatensätze in Echtzeit
acc_guide_rtMonitorTip2=Echtzeitansicht der Zutrittskontrollsätze nach dem Hinzufügen des Bereichs und der entsprechenden Tür
#查看区域内人员
acc_zonePerson_cleanCount=Löschen Sie die Statistiken der ein- und ausgehenden Personen
acc_zonePerson_inCount=Statistik der Anzahl der eintretenden Personen
acc_zonePerson_outCount=Statistik über die Anzahl der Personen, die das Unternehmen verlassen
#biocv460
acc_device_validFail=Der Benutzername oder das Passwort ist falsch und die Überprüfung schlägt fehl!
acc_device_pwdRequired=Kann nur maximal 6 ganze Zahlen eingeben