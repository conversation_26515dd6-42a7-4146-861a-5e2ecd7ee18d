---
description: 
globs: 
alwaysApply: false
---
# 项目结构规则

## 项目概述
这是一个基于Spring Boot的生物识别安全系统，包含多个模块。

## 主要模块结构

### 考勤模块 (ACC)
- 位置：`zkbiosecurity-acc-3.13.x-RELEASE/`
- 主要功能：考勤管理、异常记录处理
- 关键文件：
  - [AccApiWechatAuthController.java](mdc:zkbiosecurity-acc-3.13.x-RELEASE/zkbiosecurity-acc-web/src/main/java/com/zkteco/zkbiosecurity/acc/api/controller/AccApiWechatAuthController.java)
  - [AccExceptionRecordController.java](mdc:zkbiosecurity-acc-3.13.x-RELEASE/zkbiosecurity-acc-web/src/main/java/com/zkteco/zkbiosecurity/acc/controller/AccExceptionRecordController.java)

### 人员管理模块 (PERS)
- 位置：`zkbiosecurity-pers-v3.14.0/`
- 主要功能：人员信息管理

### 考勤模块 (ATT)
- 位置：`zkbiosecurity-att-4.13.x-RELEASE/`
- 主要功能：考勤系统

### 启动模块
- 位置：`zkbiosecurity-startup/`
- 配置文件：[application.properties](mdc:zkbiosecurity-startup/zkbiosecurity-startup-WioSecurityV6600_4.5.x_R/zkbiosecurity-startup-ui/src/main/resources/application.properties)

## 资源文件结构

### 静态资源
- 位置：`src/main/resources/static/`
- 包含：HTML、CSS、JavaScript、图片等静态文件
- 重要目录：
  - `static/wechatAuthResult/` - 微信授权结果页面
  - `static/exceptionRecordDetail/` - 异常记录详情页面

### 模板文件
- 位置：`src/main/resources/templates/`
- 使用FreeMarker模板引擎
- 重要目录：
  - `templates/exceptionRecordDetail/` - 异常记录详情模板

### 数据库备份
- 位置：`tempDBBackup/`
- 文件：[db_20250725180000_1.0.0-SNAPSHOT.version.psql](mdc:tempDBBackup/db_20250725180000_1.0.0-SNAPSHOT.version.psql)

## 日志文件
- 位置：`logs/` 和 `_logs/`
- 包含系统运行日志和错误日志

## 生物识别文件
- 位置：`BioSecurityFile/`
- 包含：照片、用户头像等生物识别相关文件

## 模块API
- 位置：`zkbiosecurity-module-api-v3.13.0/`
- 包含各模块间的API接口定义

## 开发注意事项
1. 静态资源访问需要使用直接读取文件内容的方法
2. FreeMarker配置会影响static目录下的HTML文件
3. 安全配置需要在application.properties中设置匿名访问权限
4. 所有API都使用统一的返回格式 `ApiResultMessage`

