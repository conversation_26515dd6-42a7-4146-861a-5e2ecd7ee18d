---
description: 
globs: 
alwaysApply: false
---
# Spring Boot 静态资源处理规则

## 静态资源配置
- 静态资源文件位于 `src/main/resources/static/` 目录下
- FreeMarker模板文件位于 `src/main/resources/templates/` 目录下
- 配置文件位置：[application.properties](mdc:zkbiosecurity-startup/zkbiosecurity-startup-WioSecurityV6600_4.5.x_R/zkbiosecurity-startup-ui/src/main/resources/application.properties)

## FreeMarker配置
- FreeMarker模板加载路径：`classpath:/static`
- 这会导致FreeMarker尝试解析static目录下的所有HTML文件
- 包含JavaScript的HTML文件可能被FreeMarker误解析，导致语法错误

## 静态资源访问方法
### 推荐方法：直接读取文件内容
```java
@RequestMapping(value = { "/pageName" }, method = RequestMethod.GET)
public void pageName(HttpServletResponse response) throws IOException {
    try {
        ClassPathResource resource = new ClassPathResource("static/path/to/file.html");
        InputStream inputStream = resource.getInputStream();
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
        StringBuilder content = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            content.append(line).append("\n");
        }
        
        response.setContentType("text/html;charset=UTF-8");
        response.getWriter().write(content.toString());
    } catch (Exception e) {
        response.setStatus(HttpStatus.NOT_FOUND.value());
        response.getWriter().write("文件未找到: " + e.getMessage());
    }
}
```

### 避免的方法
- 不要使用 `ModelAndView` 返回静态HTML文件路径
- 不要使用 `response.sendRedirect()` 重定向到静态文件
- 这些方法会被FreeMarker拦截并尝试解析

## 安全配置
在 [application.properties](mdc:zkbiosecurity-startup/zkbiosecurity-startup-WioSecurityV6600_4.5.x_R/zkbiosecurity-startup-ui/src/main/resources/application.properties) 中添加匿名访问权限：
```properties
security.anon.url=app/v1/.*,api/v3/wechatAuth/.*,static/wechatAuthResult/.*,static/exceptionRecordDetail/.*
```

## 常见错误及解决方案
1. **FreeMarker解析错误**：使用直接读取文件内容的方法
2. **404错误**：检查文件路径和ClassPathResource路径
3. **权限错误**：在security.anon.url中添加相应路径

