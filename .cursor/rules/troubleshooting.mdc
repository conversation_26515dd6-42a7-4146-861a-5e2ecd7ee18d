---
description: 
globs: 
alwaysApply: false
---
# 故障排除规则

## 常见错误及解决方案

### 1. FreeMarker解析错误
**错误信息**：
```
freemarker.core.ParseException: Syntax error in template "xxx.html" in line X, column Y:
Found string literal: 'N/A'. Expecting: boolean (true/false)
```

**原因**：FreeMarker尝试解析包含JavaScript的HTML文件，将JavaScript字符串误认为是FreeMarker语法

**解决方案**：
- 使用直接读取文件内容的方法，而不是ModelAndView或重定向
- 参考：[AccApiWechatAuthController.java](mdc:zkbiosecurity-acc-3.13.x-RELEASE/zkbiosecurity-acc-web/src/main/java/com/zkteco/zkbiosecurity/acc/api/controller/AccApiWechatAuthController.java)

### 2. 404错误
**错误信息**：`HTTP 404 Not Found`

**可能原因**：
- 文件路径不正确
- 静态资源配置问题
- 安全配置缺失

**解决方案**：
1. 检查文件是否存在于正确位置
2. 使用ClassPathResource直接读取文件
3. 在application.properties中添加匿名访问权限

### 3. 权限错误
**错误信息**：`HTTP 403 Forbidden` 或认证失败

**解决方案**：
在 [application.properties](mdc:zkbiosecurity-startup/zkbiosecurity-startup-WioSecurityV6600_4.5.x_R/zkbiosecurity-startup-ui/src/main/resources/application.properties) 中添加：
```properties
security.anon.url=app/v1/.*,api/v3/wechatAuth/.*,static/wechatAuthResult/.*,static/exceptionRecordDetail/.*
```

### 4. 编译错误
**错误信息**：`The method readAllBytes() is undefined for the type InputStream`

**原因**：使用了Java 9+的方法，但项目使用Java 8

**解决方案**：
使用Java 8兼容的文件读取方法：
```java
BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
StringBuilder content = new StringBuilder();
String line;
while ((line = reader.readLine()) != null) {
    content.append(line).append("\n");
}
```

## 调试技巧

### 1. 添加测试端点
```java
@RequestMapping(value = { "/testStatic" }, method = RequestMethod.GET)
public void testStatic(HttpServletResponse response) throws IOException {
    response.setContentType("text/html;charset=UTF-8");
    response.getWriter().write("<html><body><h1>静态资源测试成功</h1></body></html>");
}
```

### 2. 检查文件路径
使用ClassPathResource检查文件是否存在：
```java
ClassPathResource resource = new ClassPathResource("static/path/to/file.html");
if (resource.exists()) {
    // 文件存在
} else {
    // 文件不存在
}
```

### 3. 日志调试
在application.properties中启用调试日志：
```properties
logging.level.com.zkteco.zkbiosecurity=DEBUG
logging.level.org.springframework.web=DEBUG
```

## 最佳实践

1. **静态资源访问**：始终使用直接读取文件内容的方法
2. **错误处理**：为所有文件操作添加try-catch块
3. **路径检查**：使用ClassPathResource.exists()验证文件存在
4. **安全配置**：及时更新security.anon.url配置
5. **代码兼容性**：使用Java 8兼容的API

