---
description: 
globs: 
alwaysApply: false
---
# 微信授权控制器规则

## 控制器位置
主要控制器：[AccApiWechatAuthController.java](mdc:zkbiosecurity-acc-3.13.x-RELEASE/zkbiosecurity-acc-web/src/main/java/com/zkteco/zkbiosecurity/acc/api/controller/AccApiWechatAuthController.java)

## API路径
基础路径：`/api/v3/wechatAuth`

## 主要功能端点

### 页面访问
- **微信授权结果页面**：`GET /api/v3/wechatAuth/wechatAuthResult`
- **异常记录详情页面**：`GET /api/v3/wechatAuth/exceptionRecordDetail`

### 用户认证
- **用户登录**：`POST /api/v3/wechatAuth/login`
  - 参数：`employeeId`, `password`
  - 返回：用户信息（工号、姓名、部门、状态）

- **用户注销**：`POST /api/v3/wechatAuth/logout`
  - 参数：`employeeId`, `password`
  - 功能：清除用户微信授权信息

### 微信相关
- **获取AppId**：`GET /api/v3/wechatAuth/getAppId`
  - 返回：微信应用的AppId

- **获取OpenId**：`POST /api/v3/wechatAuth/getOpenId`
  - 参数：`code`, `pin`
  - 功能：通过微信授权码获取用户OpenId并关联存储

### 异常记录
- **获取异常记录详情**：`GET /api/v3/wechatAuth/getExceptionRecordDetail`
  - 参数：`id` (记录ID)
  - 返回：异常记录的详细信息

## 依赖服务
- `WechatService`：微信相关服务
- `PersPersonService`：人员管理服务
- `AccExceptionRecordService`：异常记录服务

## 错误处理
所有API都使用 `ApiResultMessage` 统一返回格式：
- 成功：`ApiResultMessage.successMessage(data)`
- 失败：`ApiResultMessage.message(AccConstants.API_PROGRAM_ERROR, message)`

## 安全配置
在 [application.properties](mdc:zkbiosecurity-startup/zkbiosecurity-startup-WioSecurityV6600_4.5.x_R/zkbiosecurity-startup-ui/src/main/resources/application.properties) 中已配置匿名访问：
```properties
security.anon.url=app/v1/.*,api/v3/wechatAuth/.*,static/wechatAuthResult/.*,static/exceptionRecordDetail/.*
```

