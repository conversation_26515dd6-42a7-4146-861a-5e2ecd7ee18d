/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(after = "PERS_IDENTITY_CARD_INFO t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersIdentityCardInfoItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 姓名
     */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_identityCardInfo_name")
    private String name;

    /**
     * 性别
     */
    @Column(name = "t.GENDER")
    @GridColumn(label = "pers_identityCardInfo_gender")
    private String gender;

    /**
     * 生日
     */
    @Column(name = "t.BIRTHDAY")
    @GridColumn(label = "pers_identityCardInfo_birthday")
    private Date birthday;

    /**
     * 身份证号码
     */
    @Column(name = "t.ID_CARD", equalTag = "=", encryptConverter = true)
    @GridColumn(label = "pers_identityCardInfo_idCard")
    private String idCard;

    /**
     * 物理卡号
     */
    @Column(name = "t.PHYSICAL_NO", equalTag = "=", encryptConverter = true)
    @GridColumn(label = "pers_identityCardInfo_physicalNo")
    private String physicalNo;

    /**
     * 签发机关
     */
    @Column(name = "t.ISSUED_ORGAN")
    @GridColumn(label = "pers_identityCardInfo_issuedOrgan")
    private String issuedOrgan;

    /**
     * 有效开始时间
     */
    @Column(name = "t.START_DATE")
    @GridColumn(label = "pers_identityCardInfo_startDate")
    private Date startDate;

    /**
     * 有效截止时间
     */
    @Column(name = "t.END_DATE")
    @GridColumn(label = "pers_identityCardInfo_endDate")
    private Date endDate;

    /**
     * 民族
     */
    @Column(name = "t.NATION")
    @GridColumn(label = "pers_identityCardInfo_nation")
    private String nation;

    /**
     * 地址
     */
    @Column(name = "t.ADDRESS", encryptConverter = true)
    @GridColumn(label = "pers_identityCardInfo_address")
    private String address;

    /**
     * 照片
     */
    @Column(name = "t.PHOTO_PATH")
    @GridColumn(label = "pers_identityCardInfo_photoPath")
    private String photoPath;

    /**
     * 指纹1
     */
    @Column(name = "t.TEMPLATE1")
    @GridColumn(label = "pers_identityCardInfo_template1")
    private String template1;

    /**
     * 指纹2
     */
    @Column(name = "t.TEMPLATE2")
    @GridColumn(label = "pers_identityCardInfo_template2")
    private String template2;

    /**
     * 默认构造方法
     */
    public PersIdentityCardInfoItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersIdentityCardInfoItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersIdentityCardInfoItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     * @param gender
     * @param birthday
     * @param idCard
     * @param physicalNo
     * @param issuedOrgan
     * @param startDate
     * @param endDate
     * @param nation
     * @param address
     * @param photoPath
     * @param template1
     * @param template2
     */
    public PersIdentityCardInfoItem(String id, String name, String gender, Date birthday, String idCard,
        String physicalNo, String issuedOrgan, Date startDate, Date endDate, String nation, String address,
        String photoPath, String template1, String template2) {
        super();
        this.id = id;
        this.name = name;
        this.gender = gender;
        this.birthday = birthday;
        this.idCard = idCard;
        this.physicalNo = physicalNo;
        this.issuedOrgan = issuedOrgan;
        this.startDate = startDate;
        this.endDate = endDate;
        this.nation = nation;
        this.address = address;
        this.photoPath = photoPath;
        this.template1 = template1;
        this.template2 = template2;
    }
}