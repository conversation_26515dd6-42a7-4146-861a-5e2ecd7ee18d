/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import java.sql.Timestamp;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(
    after = "PERS_CARD t LEFT JOIN PERS_PERSON p ON t.PERSON_ID=p.ID LEFT join AUTH_DEPARTMENT d on p.auth_dept_id = d.id")
@OrderBy(after = "t.CREATE_TIME DESC, t.ID DESC")
@GridConfig(operate = false, idField = "id", winHeight = 400, winWidth = 600)
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersCardItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 卡号
     */
    @Column(name = "t.CARD_NO", encryptConverter = true)
    @GridColumn(label = "pers_card_cardNo", width = "100", sort = "na", encryptMode = "${pers.cardNo.encryptMode}",
            permission = "pers:cardNo:encryptProp")
    private String cardNo;

    @Column(name = "p.ID", equalTag = "=")
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "p.PIN")
    @GridColumn(label = "pers_person_pin", width = "100", encryptMode = "${pers.pin.encryptMode}",
            permission = "pers:pin:encryptProp")
    private String personPin;

    /**
     * 人员姓名
     */
    @Column(name = "p.NAME")
    @GridColumn(label = "pers_person_name", width = "100", encryptMode = "${pers.name.encryptMode}",
            permission = "pers:name:encryptProp")
    private String personName;

    /**
     * 人员名
     */
    @Column(name = "p.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "100", showExpression = "#language!='zh_CN'",
        encryptMode = "${pers.lastName.encryptMode}", permission = "pers:name:encryptProp")
    private String personLastName;

    /**
     * 部门编码
     */
    @GridColumn(label = "pers_dept_deptNo", width = "150", sort = "na")
    @Column(name = "d.CODE")
    private String deptCode;

    /**
     * 部门名称
     */
    @GridColumn(label = "pers_dept_deptName", width = "160", sort = "na")
    @Column(name = "d.NAME")
    private String deptName;

    /**
     * 部门ID
     */
    @Column(name = "p.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 卡类型 主卡：0;副卡：1
     */
    @Column(name = "t.CARD_TYPE")
    private Short cardType;

    /**
     * 卡操作类型;时间卡,授权卡等 ACMS可回收卡100，不可回收101
     */
    @Column(name = "t.CARD_OP_TYPE")
    private Short cardOpType;

    /**
     * 发卡时间
     */
    @Column(name = "t.ISSUE_TIME")
    @GridColumn(label = "pers_issueCard_date", width = "140")
    private Timestamp issueTime;

    /**
     * 卡状态
     */
    @Column(name = "t.CARD_STATE")
    @GridColumn(label = "pers_card_state", columnType = "custom", format = "1=pers_card_effect,3=pers_card_disabled")
    private Short cardState;

    /**
     * 卡有效期的开始时间
     */
    @Column(name = "t.START_TIME")
    private Timestamp startTime;

    /**
     * 卡有效期的截止时间
     */
    @Column(name = "t.END_TIME")
    private Timestamp endTime;

    /**
     * 逻辑卡号
     */
    @Column(name = "t.LOGICAL_CARD_NO")
    private String logicalCardNo;

    @Condition(value = "p.ID", equalTag = "in")
    private String personIdIn;

    @Condition(value = "t.CARD_NO", equalTag = "in", encryptConverter = true)
    private String cardNoIn;

    @Condition(value = "p.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "(LOWER (p.NAME) LIKE LOWER (''%{0}%'') OR LOWER (p.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
        value = "p.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    @Column(name = "t.IS_FROM")
    private String isFrom;

    /** 业务id */
    @Column(name = "t.BUSSINESS_ID")
    private String bussinessId;

    /** ACMS激活码 */
    private String activationCode;

    /** ACMS卡状态 */
    private String status;

    /**
     * 默认构造方法
     */
    public PersCardItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersCardItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersCardItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param personPin
     * @param cardNo
     * @param cardType
     * @param cardState
     */
    public PersCardItem(String personPin, String cardNo, Short cardType, Short cardState) {
        this.personPin = personPin;
        this.cardNo = cardNo;
        this.cardType = cardType;
        this.cardState = cardState;
    }

    /**
     * @param cardNo
     * @param cardType
     * @param cardState
     */
    public PersCardItem(String cardNo, Short cardType, Short cardState) {
        this.cardNo = cardNo;
        this.cardType = cardType;
        this.cardState = cardState;
    }

    public PersCardItem(String cardNo, Short cardType, Short cardState, String isFrom) {
        this.cardNo = cardNo;
        this.cardType = cardType;
        this.cardState = cardState;
        this.isFrom = isFrom;
    }

    public PersCardItem(String cardNo, Short cardType, Short cardState, String isFrom, String bussinessId,
        Short cardOpType) {
        this.cardNo = cardNo;
        this.cardType = cardType;
        this.cardState = cardState;
        this.isFrom = isFrom;
        this.bussinessId = bussinessId;
        this.cardOpType = cardOpType;
    }

    /**
     * @param id
     * @param cardType
     * @param cardOpType
     * @param cardState
     * @param cardNo
     * @param issueTime
     * @param startTime
     * @param endTime
     * @param logicalCardNo
     */
    public PersCardItem(String id, Short cardType, Short cardOpType, Short cardState, String cardNo,
        Timestamp issueTime, Timestamp startTime, Timestamp endTime, String logicalCardNo) {
        super();
        this.id = id;
        this.cardType = cardType;
        this.cardOpType = cardOpType;
        this.cardState = cardState;
        this.cardNo = cardNo;
        this.issueTime = issueTime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.logicalCardNo = logicalCardNo;
    }
}