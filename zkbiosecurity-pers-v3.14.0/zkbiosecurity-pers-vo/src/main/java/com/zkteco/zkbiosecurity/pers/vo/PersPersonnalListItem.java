package com.zkteco.zkbiosecurity.pers.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 名单库
 * 
 * <AUTHOR>
 * @date: 2018-03-08 下午06:03
 * @version v1.0
 */
@From(after = "PERS_PERSONNAL_LIST t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id",
    operates = {
        @GridOperate(type = "edit", permission = "pers:personnallist:edit", url = "persPersonnalList.do?edit",
            label = "common_op_edit", showConvertor = "showConvertor"),
        @GridOperate(type = "del", permission = "pers:personnallist:del", url = "persPersonnalList.do?del",
            label = "common_op_del", showConvertor = "showConvertor"),
        @GridOperate(type = "custom", click = "addPerson", permission = "pers:personnallist:addPerson",
            label = "pers_common_addPerson")})
@Setter
@Getter
@Accessors(chain = true)
public class PersPersonnalListItem extends BaseItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /** 域模式 */
    @Column(name = "t.SCHEME")
    @GridColumn(label = "pers_personnal_list_scheme", width = "100", show = false)
    private Integer scheme;

    /** 名单库名称 */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_personnal_list_name", width = "100")
    private String name;

    /** 名单库ID */
    @Column(name = "t.GROUP_STR_ID")
    @GridColumn(label = "pers_personnal_list_group_str_id", width = "100", show = false)
    private String groupStrId;

    /** 名单库类型(2:黑 3:白 4:红) */
    @Column(name = "t.TYPE")
    @GridColumn(label = "pers_personnal_list_type", width = "100",
        format = "2=pers_personnal_bannedList,3=pers_personnal_allowList,4=pers_personnal_redList")
    private String type;

    /** 用户自定义 */
    @Column(name = "t.TAG")
    @GridColumn(label = "pers_personnal_list_tag", width = "100", show = false)
    private String tag;

    /** 描述 */
    @Column(name = "t.DESCRIPTION")
    @GridColumn(label = "base_system_description", width = "100")
    private String description;

    /** 人员数量 */
    @GridColumn(label = "pers_personnal_list_personCount", width = "100")
    private String personCount;

    @Column(name = "t.INIT_FLAG")
    private Boolean initFlag;

    @Condition(equalTag = "in", value = "t.ID")
    private String inId;

    @Condition(value = "t.TYPE", equalTag = "not in")
    private String typeNotIn;

    public PersPersonnalListItem() {
        super();
    }

    public PersPersonnalListItem(String id) {
        super();
        this.id = id;
    }

    public PersPersonnalListItem(String name, String type) {
        this.name = name;
        this.type = type;
    }

    public PersPersonnalListItem(String name, String type, Boolean initFlag) {
        this.name = name;
        this.type = type;
        this.initFlag = initFlag;
    }

    public boolean showConvertor() {
        return initFlag == null ? true : !this.initFlag;
    }
}
