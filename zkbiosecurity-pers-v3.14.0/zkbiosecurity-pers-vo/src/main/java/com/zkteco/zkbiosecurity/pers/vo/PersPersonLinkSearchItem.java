/*
 * File Name: AccSearchSelectPersonItem
 * <NAME_EMAIL> on 2018/5/17 18:30.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @date 2018/5/21 9:57
 * @return
 */
@From(after = "PERS_PERSON_LINK t LEFT JOIN PERS_PERSON p ON t.PERSON_ID=p.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonLinkSearchItem extends BaseItem implements Serializable {

    /**
     * 类型  LinkTypeEnum 枚举值
     */
    @Column(name = "t.TYPE", equalTag = "=")
    private String type;

    /**
     * 业务表的ID
     */
    @Column(name = "t.LINK_ID", equalTag = "=")
    private String linkId;

    /**
     * 人员id
     */
    @Column(name = "p.ID", equalTag = "=")
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "p.PIN")
    private String personPin;

    /**
     * 姓名
     */
    @Column(name = "p.NAME")
    private String personName;

    /**
     * 姓
     */
    @Column(name = "p.LAST_NAME")
    private String personLastName;

    /**
     * 卡号
     */
    @Condition("p.ID IN (SELECT pc.PERSON_ID FROM PERS_CARD pc WHERE pc.CARD_STATE=1 AND pc.CARD_NO LIKE ''%{0}%'')")
    private String cardNo;

    /**
     * 部门id
     */
    @Column(name = "p.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门
     */
    private String deptName;

    @Condition(value = "p.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Override
    public String getId() {
        return personId;
    }

}
