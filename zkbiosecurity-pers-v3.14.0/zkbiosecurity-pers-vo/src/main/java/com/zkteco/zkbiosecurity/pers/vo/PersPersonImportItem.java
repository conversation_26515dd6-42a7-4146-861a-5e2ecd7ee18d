/*
 * File Name: PersPersonImportItem Created by caiyun.chen on 2019/7/9 15:41. Copyright:Copyright © 1985-2017 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.vo;

import java.util.Date;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.annotation.DateType;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonImportItem extends BaseItem {

    private String id;

    /**
     * 人员编号
     */
    @GridColumn(label = "pers_person_pin", width = "90")
    private String pin;

    /**
     * 名字
     */
    @GridColumn(label = "pers_person_name", width = "120")
    private String name;

    /**
     * 姓氏
     */
    @GridColumn(label = "pers_person_lastName", width = "120", showExpression = "#language!='zh_CN'")
    private String lastName;

    /**
     * 部门编码
     */
    @GridColumn(label = "pers_dept_deptNo", width = "120")
    private String deptCode;

    /**
     * 部门名称
     */
    @GridColumn(label = "pers_dept_deptName", width = "120")
    private String deptName;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 多卡
     */
    @GridColumn(label = "pers_card_cardNo", width = "120")
    private String cardNos;

    /**
     * 性别
     */
    @GridColumn(label = "pers_person_gender", width = "120")
    private String gender;

    /**
     * 出生日期
     */
    @GridColumn(label = "pers_person_birthday", width = "120")
    @DateType(type = "date")
    private Date birthday;

    private String birthdayStr;

    /**
     * 联系电话
     */
    @GridColumn(label = "pers_person_mobilePhone", width = "120")
    private String mobilePhone;

    /**
     * 邮箱
     */
    @GridColumn(label = "pers_person_email", width = "120")
    private String email;

    /**
     * 车牌号
     */
    @GridColumn(label = "pers_person_carPlate", width = "120")
    private String carPlate;

    @GridColumn(label = "pers_cardTemplate_entryDate", width = "120")
    @DateType(type = "date")
    private Date hireDate;

    private String hireDateStr;

    @GridColumn(label = "pers_cert_type", width = "120")
    private String certName;

    @GridColumn(label = "pers_cert_number", width = "120")
    private String certNumber;

    @GridColumn(label = "pers_position_code", width = "120")
    private String positionCode;

    @GridColumn(label = "pers_position_name", width = "120")
    private String positionName;

    private Map<String, Object> attrMap;

    @GridColumn(label = "common_verifyMode_entiy", width = "120")
    private String verifyMode;

    @GridColumn(label = "pers_person_building")
    private String vdbBuildingName;

    @GridColumn(label = "pers_person_unitName")
    private String vdbUnitName;

    @GridColumn(label = "pers_person_roomNo")
    private String roomNo;

    /**
     * 导入行数
     */
    private Integer rowNum;
}
