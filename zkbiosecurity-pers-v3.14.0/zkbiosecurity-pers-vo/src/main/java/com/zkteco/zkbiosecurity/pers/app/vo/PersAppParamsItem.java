package com.zkteco.zkbiosecurity.pers.app.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * APP人事参数VO
 * <AUTHOR>
 * @Date: 2018/12/3 16:49
 */
@Getter
@Setter
@Accessors(chain = true)
public class PersAppParamsItem implements Serializable {

    /**
     * pin最大长度
     */
    private String pinLen;

    /**
     * pin是否支持字母
     */
    private String pinSupportLetter;

    /**
     * pin是否自动增长
     */
    private String pinSupportIncrement;

    /**
     * pin是否默认
     */
    private String pinSupportDefault;

    /**
     * 是否支持多卡
     */
    private String cardsSupport;

    /**
     * 离职人员是否保留pin号
     */
    private String pinRetain;

    /**
     * 卡号长度，默认32位
     */
    private String cardLen;

    /**
     * 卡进制显示 0：为十进制、1：为十六进制
     */
    private String cardHex;

    /**
     * 卡号读取方式 1：控制器读头、2：ID180（读身份证物理卡号）3、身份证读头
     */
    private String cardsReadMode;

    /* 身份证读头方式 */
    /**
     * pin最大长度
     */
    private String IDReadMode;
}
