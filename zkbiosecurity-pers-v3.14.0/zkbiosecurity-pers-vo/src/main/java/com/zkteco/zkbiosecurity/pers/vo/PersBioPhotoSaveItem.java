package com.zkteco.zkbiosecurity.pers.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
public class PersBioPhotoSaveItem {
    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 生物模板类型
     */
    private Short bioType;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * base64数据
     */
    private String photoBase64;

    /**
     * 更新人员头像
     */
    private Boolean updateAvatar;

    public PersBioPhotoSaveItem() {}

    public PersBioPhotoSaveItem(String personPin, Short bioType, String fileName, String photoBase64) {
        this.personPin = personPin;
        this.bioType = bioType;
        this.fileName = fileName;
        this.photoBase64 = photoBase64;
    }
}
