/*
 * File Name: PersPersonEmpItem Created by caiyun.chen on 2019/5/6 18:41. Copyright:Copyright © 1985-2017 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 组装成访客被访人Item
 * 
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@From(after = "PERS_PERSON t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonEmpItem extends BaseItem {

    /**
     * 主键
     */
    @Column(name = "t.ID")
    private String id;

    /** id */
    @Column(name = "t.ID")
    private String personId;

    /** 被访人编号 */
    @Column(name = "t.PIN")
    private String personPin;

    /** 被访人姓名 */
    @Column(name = "t.NAME")
    private String personName;

    /** 被访人姓氏 */
    @Column(name = "t.LAST_NAME")
    private String personLastName;

    /**
     * 联系电话
     */
    @Column(name = "t.MOBILE_PHONE", encryptConverter = true)
    private String mobilePhone;

    /** 部门名称 */
    private String deptName;

    /** 部门id */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /** 部门编号 */
    private String deptCode;

    /** 公司电话 */
    private String officePhone;

}
