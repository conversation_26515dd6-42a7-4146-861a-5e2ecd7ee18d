/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 *
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(after = "PERS_CERTIFICATE t LEFT JOIN PERS_PERSON p ON t.PERSON_ID=p.ID")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 400, winWidth = 600,
    operates = {
        @GridOperate(type = "edit", permission = "pers:certificate:edit", url = "/persCertificate.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "pers:certificate:del", url = "/persCertificate.do?del",
            label = "common_op_del")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersCertificateItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    @Column(name = "p.ID", equalTag = "=")
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "p.PIN")
    @GridColumn(label = "pers_person_pin", width = "100")
    private String pin;

    /**
     * 人员姓名
     */
    @Column(name = "p.NAME")
    @GridColumn(label = "pers_person_name", width = "100")
    private String name;

    /**
     * 人员名
     */
    @Column(name = "p.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "100", showExpression = "#language!='zh_CN'")
    private String lastName;

    /**
     * 证件类型
     */
    @Column(name = "t.CERT_TYPE")
    @GridColumn(label = "pers_certificate_certType")
    private String certType;

    /**
     * 证件号码
     */
    @Column(name = "t.CERT_NUMBER", encryptConverter = true)
    @GridColumn(label = "pers_certificate_certNumber")
    private String certNumber;

    /**
     * 证件状态
     */
    @Column(name = "t.CERT_STATUS")
    @GridColumn(label = "pers_certificate_certStatus")
    private Short certStatus;

    /** 人员ID不等于 */
    @Condition(value = "p.ID", equalTag = "<>")
    private String personIdNe;

    @Column(name = "t.CERT_TYPE")
    @GridColumn(columnType = "dic", key = "certificateType", label = "vis_cert_type")
    private String certName;

    /**
     * 默认构造方法
     */
    public PersCertificateItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersCertificateItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersCertificateItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param certType
     * @param certNumber
     * @param certStatus
     */
    public PersCertificateItem(String id, String certType, String certNumber, Short certStatus) {
        super();
        this.id = id;
        this.certType = certType;
        this.certNumber = certNumber;
        this.certStatus = certStatus;
    }
}