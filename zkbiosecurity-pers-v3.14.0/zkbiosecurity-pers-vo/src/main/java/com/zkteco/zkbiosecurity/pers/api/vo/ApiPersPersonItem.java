package com.zkteco.zkbiosecurity.pers.api.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2019/1/4 16:15
 */
public class ApiPersPersonItem implements Serializable {

    /** 人员ID */
    private String id;

    /** 人员编号 */
    private String pin;

    /** 人员姓名 */
    private String name;

    /** 人员姓（海外） */
    private String lastName;

    /** 部门ID */
    private String deptId;

    /** 部门编号 */
    private String deptCode;

    /** 部门名称 */
    private String deptName;

    /** 性别 */
    private String gender;

    /** 人员状态 */
    private Short status;

    /** 员工类型 */
    private Short personType;

    /** 手机号码 */
    private String mobilePhone;

    /** 自助登录密码 */
    private String selfPwd;

    /** 设备验证密码 */
    private String personPwd;

    /** 邮箱 */
    private String email;

    /** 人员照片路径 */
    private String photoPath;

    /** 人员抠图照片路径 */
    private String cropPhotoPath;

    /** 人员照片 */
    private String photo;

    /** 员工生日 */
    private String birthday;

    /** 证件类型 */
    private String certType;

    /** 证件号码 */
    private String certNumber;

    /** 家庭地址 */
    private String homeAddress;

    /** 数据来源 */
    private String isFrom;

    /** 入职日期 */
    private String hireDate;

    /** 卡号 */
    private String cardNos;

    /** 民族 */
    private String nation;

    /**来源  1|海外 0|国内*/
    private  String personFrom;

    /** 页数 */
    private Integer page;

    /** 每页记录数 */
    private Integer size;

    /** 禁用/启用 */
    private Boolean enabled;

    /** 验证码 */
    private String verificationCode;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Short getStatus() {
        return status;
    }

    public void setStatus(Short status) {
        this.status = status;
    }

    public Short getPersonType() {
        return personType;
    }

    public void setPersonType(Short personType) {
        this.personType = personType;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getSelfPwd() {
        return selfPwd;
    }

    public void setSelfPwd(String selfPwd) {
        this.selfPwd = selfPwd;
    }

    public String getPersonPwd() {
        return personPwd;
    }

    public void setPersonPwd(String personPwd) {
        this.personPwd = personPwd;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhotoPath() {
        return photoPath;
    }

    public void setPhotoPath(String photoPath) {
        this.photoPath = photoPath;
    }

    public String getCropPhotoPath() {
        return cropPhotoPath;
    }

    public void setCropPhotoPath(String cropPhotoPath) {
        this.cropPhotoPath = cropPhotoPath;
    }

    public String getPhoto() {
        return photo;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getCertType() {
        return certType;
    }

    public void setCertType(String certType) {
        this.certType = certType;
    }

    public String getCertNumber() {
        return certNumber;
    }

    public void setCertNumber(String certNumber) {
        this.certNumber = certNumber;
    }

    public String getHomeAddress() {
        return homeAddress;
    }

    public void setHomeAddress(String homeAddress) {
        this.homeAddress = homeAddress;
    }

    public String getIsFrom() {
        return isFrom;
    }

    public void setIsFrom(String isFrom) {
        this.isFrom = isFrom;
    }

    public String getHireDate() {
        return hireDate;
    }

    public void setHireDate(String hireDate) {
        this.hireDate = hireDate;
    }

    public String getCardNos() {
        return cardNos;
    }

    public void setCardNos(String cardNos) {
        this.cardNos = cardNos;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public String getPersonFrom() {
        return personFrom;
    }

    public void setPersonFrom(String personFrom) {
        this.personFrom = personFrom;
    }
}
