/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(after = "PERS_LEAVEPERSON t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winHeight = 270, winWidth = 500,
    operates = {
        @GridOperate(type = "edit", permission = "pers:leavePerson:edit", url = "persLeavePerson.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "custom", permission = "pers:leavePerson:reinstated", click = "persReinstated",
            label = "pers_leavePerson_reinstated")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersLeavePersonItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /** 人员编号 */
    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", width = "100", encryptMode = "${pers.pin.encryptMode}",
            permission = "pers:pin:encryptProp")
    private String pin;

    /** 离职人员名字 */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "100", encryptMode = "${pers.name.encryptMode}",
            permission = "pers:name:encryptProp")
    private String name;

    /** 姓氏 */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "100", showExpression = "#language!='zh_CN'",
        encryptMode = "${pers.lastName.encryptMode}", permission = "pers:name:encryptProp")
    private String lastName;

    /** 部门名字 */
    @Column(name = "t.AUTH_DEPT_NAME")
    @GridColumn(label = "pers_dept_deptName", width = "150", sort = "na")
    private String deptName;

    /** 部门id */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /** 部门编号 */
    @Column(name = "t.AUTH_DEPT_CODE")
    private String deptCode;

    /** 入职时间 */
    @Column(name = "t.HIRE_DATE")
    @DateType(type = "date")
    @GridColumn(label = "pers_cardTemplate_entryDate", width = "110")
    private Date hireDate;

    /** 离职日期 */
    @Column(name = "t.LEAVE_DATE")
    @DateType(type = "date")
    @GridColumn(label = "pers_dimission_date", width = "130")
    private Date leaveDate;

    /** 离职类型id */
    @Column(name = "t.LEAVETYPE_ID")
    private Integer leaveType;

    /** 离职类型 */
    @Column(name = "t.LEAVETYPE_ID")
    @GridColumn(label = "pers_dimission_type", width = "160", key = "PersLeaveType", columnType = "dic")
    private String leaveTypeString;

    /** 离职原因 */
    @Column(name = "t.LEAVE_REASON")
    @GridColumn(label = "pers_dimission_reason", width = "180")
    private String leaveReason;

    /** 是否考勤 */
    @Column(name = "t.IS_ATTENDANCE")
    private Boolean isAttendance;

    /** 分组id */
    @Column(name = "t.GROUP_ID")
    private String attGroupId;

    @Condition(value = "t.LEAVE_DATE", equalTag = ">=", toDate = true)
    private String opTimeBegin;

    @Condition(value = "t.LEAVE_DATE", equalTag = "<=", toDate = true)
    private String opTimeEnd;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.PIN", equalTag = "in")
    private String inPersonPins;

    @Condition(value = "t.LEAVE_DATE", equalTag = ">=")
    private Date beginDate;

    @Condition(value = "t.LEAVE_DATE", equalTag = "<=")
    private Date endDate;

    @Condition(value = "(LOWER(t.NAME) LIKE LOWER(''%{0}%'') OR LOWER(t.LAST_NAME) LIKE LOWER(''%{0}%''))")
    private String likeName;

    private String forbidden;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
        value = "t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public PersLeavePersonItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersLeavePersonItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersLeavePersonItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param deptName
     * @param deptId
     * @param deptCode
     * @param pin
     * @param name
     * @param lastName
     * @param leaveDate
     * @param leaveReason
     * @param leaveType
     * @param hireDate
     * @param isAttendance
     * @param attGroupId
     */
    public PersLeavePersonItem(String id, String deptName, String deptId, String deptCode, String pin, String name,
        String lastName, Date leaveDate, String leaveReason, Integer leaveType, Date hireDate, Boolean isAttendance,
        String attGroupId) {
        super();
        this.id = id;
        this.deptName = deptName;
        this.deptId = deptId;
        this.deptCode = deptCode;
        this.pin = pin;
        this.name = name;
        this.lastName = lastName;
        this.leaveDate = leaveDate;
        this.leaveReason = leaveReason;
        this.leaveType = leaveType;
        this.hireDate = hireDate;
        this.isAttendance = isAttendance;
        this.attGroupId = attGroupId;
    }
}