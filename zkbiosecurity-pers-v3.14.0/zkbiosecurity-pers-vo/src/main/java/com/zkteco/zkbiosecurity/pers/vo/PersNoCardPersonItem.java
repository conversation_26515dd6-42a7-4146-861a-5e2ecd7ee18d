package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 11:37 2018/12/21
 */

@From(after = "PERS_PERSON t LEFT join AUTH_DEPARTMENT d on t.auth_dept_id = d.id")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersNoCardPersonItem extends BaseItem {
    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", width = "100")
    private String personPin;

    /**
     * 名字
     */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "120")
    private String personName;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "120", showExpression = "#language!='zh_CN'")
    private String personLastName;

    /**
     * 部门名称
     */
    @Column(name = "d.NAME")
    @GridColumn(label = "pers_dept_deptName", width = "120", sort = "na")
    private String deptName;

    /**
     * 卡号
     */
    @GridColumn(label = "pers_card_cardNo", width = "120", sort = "na")
    private String cardNo;

    /**
     * 人员类型
     */
    @Condition(value = "t.PERSON_TYPE", equalTag = "=")
    private Short personType;

    /**
     * 无卡人员
     */
    @Condition(value = "t.ID NOT IN (SELECT c.PERSON_ID FROM PERS_CARD c WHERE c.CARD_STATE={0} GROUP BY c.PERSON_ID)")
    private Short cardState;

    /**
     * 部门ID
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /** 左右列表 */
    private String type;

    /** 过滤右边已选的人员ID */
    @Condition(value = "t.ID", equalTag = "not in")
    private String notInPersonId;

    private String startPersonPin;

    private String endPersonPin;

    @Column(name = "t.NUMBER_PIN", equalTag = ">=")
    private Long startNumberPin;

    @Column(name = "t.NUMBER_PIN", equalTag = "<=")
    private Long endNumberPin;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
        value = "t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    @Condition(value = "(LOWER(t.NAME) LIKE LOWER(''%{0}%'') OR LOWER(t.LAST_NAME) LIKE LOWER(''%{0}%''))")
    private String likeName;

    // 选中人员的ID
    @Condition(value = "t.ID", equalTag = "not in")
    private String selectId;

    // ACMS发卡过滤没有邮箱人员
    @Condition(value = "t.EMAIL <> '''' and t.EMAIL is not null")
    private String emailNotNull;

}
