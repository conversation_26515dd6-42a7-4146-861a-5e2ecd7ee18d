/**
 * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @Date 2018/12/27 3:43
 * @Param        
 * @return
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(after = "PERS_PERSON t")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonToInsAreaItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN")
    private String pin;

    /**
     * 名字
     */
    @Column(name = "t.NAME")
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    private String lastName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门ID
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 多卡
     */
    private String cardNo;

    /**
     * 生物模版数
     * fpCount + "_" + faceCount + "_" + fvCount;
     */
    private String bioTemplateCount;

    /**
     * 人员类型
     */
    @Column(name = "t.PERSON_TYPE")
    private Short personType;

    /**
     * 人员来源
     */
    @Column(name = "t.IS_FROM")
    private String isFrom;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    @Condition(value = "t.PIN", equalTag = "in")
    private String inPin;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    /**
     * 默认构造方法
     */
    public PersPersonToInsAreaItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersPersonToInsAreaItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersPersonToInsAreaItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     *
     * @date 2018/6/6 14:55
 * @param id
 * @param pin
 * @param name
 * @param lastName
 * @param personType
 * @param isFrom
     * @return
     */
    public PersPersonToInsAreaItem(String id, String pin, String name, String lastName, Short personType, String isFrom) {
        super();
        this.id = id;
        this.pin = pin;
        this.name = name;
        this.lastName = lastName;
        this.personType = personType;
        this.isFrom = isFrom;
    }
}