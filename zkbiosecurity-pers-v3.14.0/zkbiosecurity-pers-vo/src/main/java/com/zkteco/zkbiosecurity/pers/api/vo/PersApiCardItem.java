package com.zkteco.zkbiosecurity.pers.api.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 卡号-VO
 *
 * <AUTHOR>
 * @Date: 2018/11/7 17:53
 */
@Getter
@Setter
@Accessors(chain = true)
public class PersApiCardItem implements Serializable {

    /**
     * 人员编号
     */
    @ApiModelProperty(allowableValues = "123")
    private String pin;

    /**
     * 卡号
     */
    @ApiModelProperty(allowableValues = "1234567")
    private String cardNo;

    /**
     * 0:主卡，1：副卡
     */
    @ApiModelProperty(allowableValues = "0 or 1")
    private String cardType;

    public static PersApiCardItem createCard(PersCardItem persCard) {
        PersApiCardItem apiCard = null;
        if (null != persCard) {
            apiCard = new PersApiCardItem();
            apiCard.setCardNo(persCard.getCardNo());
            apiCard.setCardType(persCard.getCardType().toString());
            apiCard.setPin(persCard.getPersonPin());
        }
        return apiCard;
    }
}
