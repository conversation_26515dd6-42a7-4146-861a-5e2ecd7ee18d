/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import java.sql.Timestamp;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(after = "PERS_ISSUECARD t ")
@OrderBy(after = "t.CREATE_TIME DESC, t.ID DESC")
@GridConfig
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersIssueCardItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(show = false)
    private String id;

    /**
     * 卡号
     */
    @Column(name = "t.CARD_NO", encryptConverter = true)
    @GridColumn(label = "pers_card_cardNo", width = "120", sort = "na", encryptMode = "${pers.cardNo.encryptMode}",
            permission = "pers:cardNo:encryptProp")
    private String cardNo;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN", equalTag = "=")
    @GridColumn(label = "pers_person_pin", width = "120", encryptMode = "${pers.pin.encryptMode}",
            permission = "pers:pin:encryptProp")
    private String pin;

    /**
     * 名字
     */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "100", encryptMode = "${pers.name.encryptMode}",
            permission = "pers:name:encryptProp")
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "100", showExpression = "#language!='zh_CN'",
        encryptMode = "${pers.lastName.encryptMode}", permission = "pers:name:encryptProp")
    private String lastName;

    /**
     * 操作类型
     */
    @Column(name = "t.OPERATE_TYPE")
    @GridColumn(label = "pers_issueCard_operate", width = "120",
        format = "1=pers_issueCard_entity,6=pers_lossCard_entity,7=pers_revertCard_entity,10=pers_card_writeMgr,11=pers_card_write,12=pers_card_back,13=pers_card_change")
    private Short operateType;

    /**
     * 操作员
     */
    @Column(name = "t.CREATER_CODE")
    @GridColumn(label = "pers_issueCard_operator", width = "120")
    private String operator;

    /**
     * 发卡时间
     */
    @Column(name = "t.CREATE_TIME")
    @GridColumn(label = "base_opLog_opTime", width = "150")
    private Timestamp issueTime;

    /**
     * 修改时间
     */
    @Column(name = "t.UPDATE_TIME")
    @GridColumn(label = "pers_issueCard_changeTime", width = "150")
    private Timestamp changeTime;

    @Condition(value = "t.CREATE_TIME", equalTag = ">=", toDate = true)
    private String opTimeBegin;

    @Condition(value = "t.CREATE_TIME", equalTag = "<=", toDate = true)
    private String opTimeEnd;

    /**
     * 默认构造方法
     */
    public PersIssueCardItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersIssueCardItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersIssueCardItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param pin
     * @param name
     * @param lastName
     * @param cardNo
     * @param operateType
     * @param issueTime
     * @param changeTime
     */
    public PersIssueCardItem(String id, String pin, String name, String lastName, String cardNo, Short operateType,
        Timestamp issueTime, Timestamp changeTime) {
        super();
        this.id = id;
        this.pin = pin;
        this.name = name;
        this.lastName = lastName;
        this.cardNo = cardNo;
        this.operateType = operateType;
        this.issueTime = issueTime;
        this.changeTime = changeTime;
    }
}