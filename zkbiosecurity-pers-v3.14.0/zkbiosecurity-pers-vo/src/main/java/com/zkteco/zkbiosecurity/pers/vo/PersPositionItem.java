/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 人员职位item
 */
@From(after = "PERS_POSITION t LEFT JOIN PERS_POSITION p ON t.PARENT_ID=p.ID")
@OrderBy(after = "t.SORT,t.CREATE_TIME")
@GridConfig(operate = true, idField = "id", winHeight = 300, winWidth = 420,
    operates = {
        @GridOperate(type = "edit", permission = "pers:position:edit", url = "persPosition.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "pers:position:del", url = "persPosition.do?del",
            label = "common_op_del", click = "reloadPositionTreeAndGrid")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPositionItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /** 职位编号 */
    @Column(name = "t.CODE")
    @GridColumn(columnType = "edit", label = "pers_position_code", width = "130", editPermission = "pers:position:del",
        editUrl = "persPosition.do?edit&id=(id)")
    private String code;

    /** 职位名称 */
    @Column(name = "t.NAME")
    @GridColumn(columnType = "edit", label = "pers_position_name", width = "120", editPermission = "pers:position:del",
        editUrl = "persPosition.do?edit&id=(id)")
    private String name;

    /** 上级职位编号 */
    @Column(name = "p.CODE")
    @GridColumn(label = "pers_position_parentCode", width = "180")
    private String parentCode;

    /** 上级职位名称 */
    @Column(name = "p.NAME")
    @GridColumn(label = "pers_position_parentName", width = "120")
    private String parentName;

    /** 排序编号 */
    @Column(name = "t.SORT")
    private Integer sortNo;

    @Column(name = "p.ID", equalTag = "=")
    private String parentId;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.CODE", equalTag = "in")
    private String inCode;

    /**
     * 默认构造方法
     */
    public PersPositionItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersPositionItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersPositionItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     * @param code
     * @param sortNo
     */
    public PersPositionItem(String id, String name, String code, Integer sortNo) {
        super();
        this.id = id;
        this.name = name;
        this.code = code;
        this.sortNo = sortNo;
    }
}