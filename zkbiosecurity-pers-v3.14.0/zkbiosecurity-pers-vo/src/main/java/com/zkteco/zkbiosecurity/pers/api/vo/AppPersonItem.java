/*
 * File Name: AppPersonItem
 * <NAME_EMAIL> on 2018/9/25 17:32.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 用户信息VO
 * <AUTHOR> href:"mailto:<EMAIL>">lambert.li</a>
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class AppPersonItem implements Serializable {

    /* 人员标识ID */
    @ApiModelProperty(value = "人员id", example = "")
    private String customerId;
    /* 出生日期 */
    @ApiModelProperty(value = "YYYY-MM-DD", example = "2016-07-15")
    private String birthday;
    /* 性别 */
    @ApiModelProperty(value = "F:Female M:Male", example = "F")
    private String gender;
    /* 人员编号 */
    @ApiModelProperty(value = "人员编号", example = "1234567")
    private String pin;
    /* 头像 */
    @ApiModelProperty(value = "头像地址", example = "https://……")
    private String avatarUrl;
    /* 抠图路径 */
    @ApiModelProperty(value = "头像抠图地址", example = "https://……")
    private String cropUrl;
    /* 手机号码 */
    @ApiModelProperty(value = "手机号码", example = "13565847894")
    private String mobile;
    /* 密码 */
    @ApiModelProperty(value = "登录密码", example = "123456")
    private String password;
    /* 姓名 */
    @ApiModelProperty(value = "人员姓名", example = "111")
    private String name;
    /* 英文（姓氏） */
    @ApiModelProperty(value = "英文（姓氏）")
    private String lastName;
    /* 卡号 */
    @ApiModelProperty(hidden = true)
    private String cardNos;
    /* 邮箱 */
    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;
    /* 证件类型 */
    @ApiModelProperty(value = "证件类型，2：身份证，3：护照，4：驾照，5：工作证，6：市民卡，7：行驶证，8：其他，1000：厦门社保卡", example = "2")
    private String certType;
    /* 证件号码 */
    @ApiModelProperty(value = "证件号码", example = "123456")
    private String certNumber;
    /* 部门名称 */
    @ApiModelProperty(value = "部门名称", example = "部门名称")
    private String deptName;
    /* 部门编号 */
    @ApiModelProperty(value = "部门编号", example = "1")
    private String deptCode;
    /* 部门编号 */
    @ApiModelProperty(value = "部门ID", example = "1")
    private String deptId;
    /* 关联百傲瑞达v5000 id */
    @ApiModelProperty(value = "appId", example = "1234")
    private String appId;
    /* 入职日期 */
    @ApiModelProperty(value = "入职日期", example = "2018-10-26")
    private String hireDate;
    /* 地址 */
    @ApiModelProperty(value = "地址")
    private String familyAddress;
    /* 签发地址 */
    @ApiModelProperty(value = "签发地址")
    private String issuedAddress;
    /* 有效期 止 */
    @ApiModelProperty(value = "有效期 止")
    private String indateEnd;
    /* 有效期 起 */
    @ApiModelProperty(value = "有效期 起")
    private String indateStart;
    /** 数据来源 */
    @ApiModelProperty(value = "数据来源")
    private String isFrom;
    /** 人员启用状态（是否登录） */
    @ApiModelProperty(value = "人员启用状态")
    private Boolean enabled;
    /** 是否重置密码 true 是 false 否 */
    private Boolean resetPassword;
    /** 设备验证密码 */
    private String devicePassword;
    /** 是否管理员 true 是 false 否 */
    private Boolean isSuperuser;

    @ApiModelProperty(value = "小程序或公众号原始ID", example = "12313asdasd")
    private String wxOriginalId;
    @ApiModelProperty(value = "小程序或公众号OpenId", example = "1231231")
    private String wxOpenId;
    /** 企业ID */
    private String companyId;
    /** 人员权限组id */
    private String accLevelIds;
    /** 人员权限组名称 */
    private String accLevelName;
    /** 人员职位 */
    private String positionName;

    @ApiModelProperty(value = "验证码", example = "123456")
    private String verificationCode;

    private String changeAppId;
}
