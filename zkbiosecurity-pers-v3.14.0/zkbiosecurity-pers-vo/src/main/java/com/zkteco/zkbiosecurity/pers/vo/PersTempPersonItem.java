/*
 * File Name: PersTempPersonItem <NAME_EMAIL> on 2018/9/26 10:23. Copyright:Copyright © 1999-2018
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@From(after = "PERS_TEMP_PERSON t ")
@OrderBy(after = "t.UPDATE_TIME DESC")
@GridConfig(operate = true, idField = "id", winWidth = 520, winHeight = 350,
    operates = {
        @GridOperate(type = "custom", permission = "pers:tempPerson:audit", filter = "persShowReview",
            click = "auditView", label = "pers_tempPerson_audit"),
        @GridOperate(type = "custom", permission = "pers:tempPerson:view", filter = "persShowView",
            click = "viewPersTempPerson", label = "pers_tempPerson_view"),
        @GridOperate(type = "del", permission = "pers:tempPerson:del", url = "persTempPerson.do?del",
            label = "common_op_del")})
@Setter
@Getter
@Accessors(chain = true)
public class PersTempPersonItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", width = "120", encryptMode = "${pers.pin.encryptMode}",
            permission = "pers:pin:encryptProp")
    private String pin;

    /**
     * 名字
     */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "120", encryptMode = "${pers.name.encryptMode}",
            permission = "pers:name:encryptProp")
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "120", showExpression = "#language!='zh_CN'",
        encryptMode = "${pers.lastName.encryptMode}", permission = "pers:name:encryptProp")
    private String lastName;

    /**
     * 部门ID
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 部门ID
     */
    private String deptCode;

    /**
     * 联系电话
     */
    @Column(name = "t.MOBILE_PHONE", encryptConverter = true)
    @GridColumn(label = "pers_person_mobilePhone", width = "140", encryptMode = "${pers.mobilePhone.encryptMode}",
            permission = "pers:mobilePhone:encryptProp")
    private String mobilePhone;

    /**
     * 审核状态
     */
    @Column(name = "t.STATUS")
    @GridColumn(label = "common_status", width = "90", columnType = "custom", convert = "convertAuditStatus")
    private Short status;

    /**
     * 创建时间
     */
    @Column(name = "t.CREATE_TIME")
//    @GridColumn(label = "pers_person_createTime", width = "150")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "t.UPDATE_TIME")
    @GridColumn(label = "pers_issueCard_changeTime", width = "150")
    private Date updateTime;

    /**
     * appId
     */
    @Column(name = "t.APP_ID")
    private String appId;

    private String token;

    private Long uid;

    /**
     * 照片路径
     */
    @Column(name = "t.PHOTO_PATH")
    private String photoPath;

    /**
     * 密码
     */
    @Column(name = "t.PERSON_PWD", encryptConverter = true)
    private String personPwd;

    /**
     * 设备验证密码
     */
    private String devicePwd;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.APP_ID", equalTag = "in")
    private String inAppId;

    /**
     * 抠图路径
     */
    @Column(name = "t.CROP_PHOTO_PATH")
    private String cropPhotoPath;

    /**
     * 人员来源
     */
    @Column(name = "t.IS_FROM")
    private String isFrom;

    /**
     * 证件类型
     */
    @Column(name = "t.CERT_TYPE")
    private String certType;

    /**
     * 证件号码
     */
    @Column(name = "t.CERT_NUMBER", encryptConverter = true)
    private String certNumber;

    /**
     * 邮箱
     */
    @Column(name = "t.EMAIL", encryptConverter = true)
    private String email;

    /**
     * 地址
     */
    @Column(name = "t.FAMILY_ADDRESS", encryptConverter = true)
    private String familyAddress;

    /**
     * 性别
     */
    @Column(name = "t.GENDER")
    private String gender;

    /**
     * 出生日期
     */
    @Column(name = "t.BIRTHDAY")
    private Date birthday;

    @Condition(value = "(LOWER(t.NAME) LIKE LOWER(''%{0}%'') OR LOWER(t.LAST_NAME) LIKE LOWER(''%{0}%''))")
    private String likeName;

    /**
     * 职位编号
     */
    @Column(name = "t.POSITION_CODE")
    private String positionCode;

    /**
     * 门禁权限组ids
     */
    @Column(name = "t.ACC_LEVEL_IDS")
    private String accLevelIds;

    /**
     * 人员卡号
     */
    @Column(name = "t.CARD_NOS", encryptConverter = true)
    private String cardNos;

    /*
     *公司名称（同云上人事reamrk）
     */
    @Column(name = "t.COMPANY_NAME")
    private String remrak;

    /**
     * 入职时间
     */
    @Column(name = "t.HIRE_DATE")
    private Date hireDate;

    /**
     * 区域id
     */
    private String areaId;

    /**
     * 职位ID
     */
    private String positionId;

    @GridColumn(dynamicColumn = "persTempPersonDyna")
    private Map attrExtMap;

    private String photoPathBase64;

    private String cropPhotoPathBase64;

    /**
     * 云端照片base64
     */
    private String cloudImgBase64;

    /**
     * 云端抠图照片base64
     */
    private String cloudCropImgBase64;

    @Column(name = "t.EXISTS_MOBILE_USER")
    private Boolean existsMobileUser;

    /**
     * 云端是否首次创建系统用户
     */
    private Boolean isCloudCreateMobileUser;

    public PersTempPersonItem() {
        super();
    }

    public PersTempPersonItem(String id) {
        super();
        this.id = id;
    }
}
