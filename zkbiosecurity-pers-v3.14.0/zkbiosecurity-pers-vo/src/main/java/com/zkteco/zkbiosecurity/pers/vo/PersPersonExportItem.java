/*
 * File Name: PersPersonExportItem Created by caiyun.chen on 2019/7/3 16:49. Copyright:Copyright © 1985-2017 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.vo;

import java.util.Date;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@From(after = "PERS_PERSON t")
@OrderBy(after = "t.UPDATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonExportItem extends BaseItem {
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", width = "90")
    private String pin;

    /**
     * 名字
     */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "120")
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "120", showExpression = "#language!='zh_CN'")
    private String lastName;

    /**
     * 部门编码
     */
    @GridColumn(label = "pers_dept_deptNo", width = "120")
    private String deptCode;

    /**
     * 部门名称
     */
    @GridColumn(label = "pers_dept_deptName", width = "120")
    private String deptName;

    /**
     * 部门ID
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 多卡
     */
    @Condition(value = "t.ID IN (SELECT c.PERSON_ID FROM PERS_CARD c WHERE c.CARD_STATE=1 AND c.CARD_NO IN(%s))",
        formatType = "quote")
    @GridColumn(label = "pers_card_cardNo", width = "120")
    private String cardNos;

    /**
     * 性别
     */
    @Column(name = "t.GENDER")
    @GridColumn(label = "pers_person_gender", width = "120")
    private String gender;

    /**
     * 出生日期
     */
    @Column(name = "t.BIRTHDAY")
    @DateType(type = "date")
    @GridColumn(label = "pers_person_birthday", width = "120")
    private Date birthday;

    /**
     * 联系电话
     */
    @Column(name = "t.MOBILE_PHONE", encryptConverter = true)
    @GridColumn(label = "pers_person_mobilePhone", width = "120")
    private String mobilePhone;

    /**
     * 邮箱
     */
    @Column(name = "t.EMAIL", encryptConverter = true)
    @GridColumn(label = "pers_person_email", width = "120")
    private String email;

    /**
     * 车牌号
     */
    @Column(name = "t.CAR_PLATE")
    @GridColumn(label = "pers_person_carPlate", width = "120")
    private String carPlate;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    /**
     * 用于卡号模糊查询
     */
    @Condition(value = "t.ID IN (SELECT c.PERSON_ID FROM PERS_CARD c WHERE c.CARD_NO LIKE ''%{0}%'')")
    private String likeCardNo;

    @GridColumn(label = "pers_cert_type", width = "120")
    private String certName;

    @GridColumn(label = "pers_cert_number", width = "120")
    private String certNumber;

    @GridColumn(label = "pers_position_code", width = "120")
    private String positionCode;

    @GridColumn(label = "pers_position_name", width = "120")
    private String positionName;

    @GridColumn(label = "pers_person_building", width = "120")
    private String buildingName;

    @GridColumn(label = "pers_person_unitName", width = "120")
    private String unitName;

    @GridColumn(label = "pers_person_roomNo", width = "120")
    private String roomNo;

    private PersAttributeExtItem attributeExt;

    @GridColumn(show = false)
    private Map<String, String> attrMap;

    @GridColumn(label = "common_verifyMode_entiy", width = "120", show = false)
    private String verifyMode;

    @Column(name = "t.HIRE_DATE")
    @DateType(type = "date")
    @GridColumn(label = "pers_cardTemplate_entryDate", width = "120")
    private Date hireDate;

    public PersAttributeExtItem getAttributeExt() {
        if (this.attributeExt == null) {
            return new PersAttributeExtItem();
        }
        return attributeExt;
    }
}
