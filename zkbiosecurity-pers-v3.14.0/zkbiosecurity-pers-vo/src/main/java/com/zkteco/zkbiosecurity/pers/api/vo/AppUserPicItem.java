package com.zkteco.zkbiosecurity.pers.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class AppUserPicItem {
    /* 手机号码 */
    @ApiModelProperty(value = "手机号码", example = "1356854796")
    private String mobile;

    /**人员姓名*/
    @ApiModelProperty(value = "", example = "1356854796")
    private String name;


    /* 图片base64 */
    @ApiModelProperty(value = "人员照片", example = "hfgfgqweiqu……")
    private String photoBase64;

    /* 员工id */
    @ApiModelProperty(value = "员工id", example = "hfgfgqweiqu……")
    private String customerId;

    /** 企业ID */
    private String companyId;

    @ApiModelProperty(value = "appId", example = "*********")
    private String appId;
}
