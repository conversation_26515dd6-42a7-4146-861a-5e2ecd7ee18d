/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(
    after = "PERS_PERSON t LEFT JOIN PERS_POSITION p ON t.POSITION_ID=p.ID LEFT join AUTH_DEPARTMENT d on t.auth_dept_id = d.id")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonPinItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN")
    private String pin;

    /**
     * 照片路径
     */
    @Column(name = "t.PHOTO_PATH")
    private String photoPath;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    @Condition(value = "t.PIN", equalTag = "in")
    private String inPin;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    /**
     * 辅助前端页面查询过滤用的，姓名
     */
    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;
    /**
     * 辅助前端页面查询过滤用的，性别
     */
    @Condition(value = "t.GENDER", equalTag = "=")
    private String gender;
    /**
     * 辅助前端页面查询过滤用的，email
     */
    @Condition(value = "t.EMAIL", equalTag = "like", encryptConverter = true)
    private String email;

    @Column(name = "t.MOBILE_PHONE", encryptConverter = true)
    private String mobilePhone;
    /**
     * 辅助前端页面查询过滤用的，状态
     */
    @Condition(value = "t.EXCEPTION_FLAG", equalTag = "=")
    private Short exceptionFlag;
    /**
     * 辅助前端页面查询过滤用的，用于卡号模糊查询
     */
    @Condition(value = "t.ID IN (SELECT c.PERSON_ID FROM PERS_CARD c WHERE c.CARD_NO LIKE ''%{0}%'')")
    private String likeCardNo;
    /**
     * 辅助前端页面查询过滤用的，部门名称
     */
    @Column(name = "d.NAME")
    private String deptName;
    /**
     * 辅助前端页面查询过滤用的，用于左侧部门树查询
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    private String verifyMode;

    @Column(name = "p.NAME")
    private String positionName;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
        value = "t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public PersPersonPinItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersPersonPinItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersPersonPinItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param pin
     */
    public PersPersonPinItem(String id, String pin) {
        super();
        this.id = id;
        this.pin = pin;
    }
}