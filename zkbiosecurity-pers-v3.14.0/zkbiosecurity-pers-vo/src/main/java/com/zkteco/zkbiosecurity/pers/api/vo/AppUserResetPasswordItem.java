package com.zkteco.zkbiosecurity.pers.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
@ApiModel
public class AppUserResetPasswordItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "人员id", example = "123456")
    private String customerId;

    @ApiModelProperty(value = "人员pin", example = "123456")
    private String pin;
    @ApiModelProperty(value = "旧密码", example = "123456")
    private String oldPassword;
    @ApiModelProperty(value = "新密码", example = "123456")
    private String newPassword;
}
