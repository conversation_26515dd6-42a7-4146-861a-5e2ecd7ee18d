/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 该方法用于人员跟区域的关系查询
 *
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(after = "PERS_PERSON_LINK pl LEFT JOIN PERS_PERSON t ON pl.PERSON_ID=t.ID")
@OrderBy(after = "pl.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonAreaItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN")
    private String personPin;

    /**
     * 名字
     */
    @Column(name = "t.NAME")
    private String personName;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    private String personLastName;

    /**
     * 部门编码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门ID
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 区域ID
     */
    @Column(name = "pl.LINK_ID", equalTag = "=")
    private String areaId;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 多卡
     */
    private String cardNo;

    /**
     * 生物模版数
     * fpCount + "_" + faceCount + "_" + fvCount+ "_" + cropFaceCount;
     */
    private String bioTemplateCount;

    /**
     * 人员类型
     */
    @Column(name = "t.PERSON_TYPE")
    private Short personType;

    /**
     * 人员来源
     */
    @Column(name = "t.IS_FROM")
    private String isFrom;

    @Column(name = "pl.TYPE", equalTag = "=")
    private String type;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    @Condition(value = "t.PIN", equalTag = "in")
    private String inPin;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "pl.LINK_ID", equalTag = "in")
    private String inAreaId;

    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    /**
     * 默认构造方法
     */
    public PersPersonAreaItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersPersonAreaItem(Boolean equals) {
        super(equals);
    }

    @Override
    public String getId() {
        return this.personId;
    }

    /**
     * @param id
     */
    public PersPersonAreaItem(String id) {
        super(true);
        this.personId = id;
    }
}