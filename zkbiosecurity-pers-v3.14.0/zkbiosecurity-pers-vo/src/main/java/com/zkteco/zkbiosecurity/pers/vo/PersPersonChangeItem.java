/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 人员部门变动信息item
 */
@From(after = "PERS_PERSONCHANGE t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonChangeItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "t.PERSON_ID", equalTag = "=")
    private String personId;

    /**
     * 部门ID
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 修改原因
     */
    @Column(name = "t.CHANGE_REASON")
    private String changeReason;

    /**
     * 默认构造方法
     */
    public PersPersonChangeItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersPersonChangeItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersPersonChangeItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param changeReason
     */
    public PersPersonChangeItem(String id, String changeReason) {
        super();
        this.id = id;
        this.changeReason = changeReason;
    }
}