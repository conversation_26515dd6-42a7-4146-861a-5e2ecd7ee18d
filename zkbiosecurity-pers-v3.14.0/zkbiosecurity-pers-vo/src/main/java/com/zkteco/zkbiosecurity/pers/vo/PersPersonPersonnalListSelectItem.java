package com.zkteco.zkbiosecurity.pers.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020/7/24
 */

@From(after = "PERS_PERSON t LEFT join AUTH_DEPARTMENT d on t.auth_dept_id = d.id")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id")
@Setter
@Getter
@Accessors(chain = true)
public class PersPersonPersonnalListSelectItem extends BaseItem implements Serializable {

    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /** 人员编号 */
    @Column(name = "t.PIN")
    @GridColumn(label = "pers_person_pin", width = "90")
    private String pin;

    /** 名字 */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "120")
    private String name;

    /** 姓氏 */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "100", showExpression = "#language!='zh_CN'")
    private String lastName;

    /** 部门名称 */
    @Column(name = "d.NAME")
    @GridColumn(label = "pers_dept_deptName", width = "120", sort = "na")
    private String deptName;

    /** 部门inId */
    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    private String notInId;

    /** 名单库id */
    private String personnelListId;

    /** 类型(已选/未选) */
    private String type;

    @Condition(value = "t.ID", equalTag = "not in")
    private String selectId;

    @Condition(
        value = "t.ID NOT IN (SELECT pl.PERSON_ID FROM PERS_PERSONNALLIST_PERSON pl WHERE pl.PERSONNALLIST_ID=''{0}'')")
    private String existPersonId;

    @Condition(
        value = "t.ID NOT IN (SELECT pl.PERSON_ID FROM PERS_PERSONNALLIST_PERSON pl LEFT JOIN PERS_PERSONNAL_LIST l on pl.PERSONNALLIST_ID=l.ID WHERE l.TYPE=''{0}'')")
    private String bandPersonId;

    @Condition(value = "d.ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    @Column(name = "t.ENABLED_CREDENTIAL")
    private Boolean enabledCredential;

    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;
}
