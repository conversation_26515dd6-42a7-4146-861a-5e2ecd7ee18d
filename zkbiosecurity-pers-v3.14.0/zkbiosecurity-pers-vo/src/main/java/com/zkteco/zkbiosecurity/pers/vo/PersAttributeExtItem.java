/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 自定义属性vo
 */
@From(after = "PERS_ATTRIBUTE_EXT t ")
@OrderBy(after = "t.CREATE_TIME DESC")
// 人事导入功能使用BeanUtils.setProperty设置属性值 使用@Setter注解无法设置属性值 所以使用get、set方法
// @Getter
// @Setter
// @Accessors(chain = true)
// @ToString
public class PersAttributeExtItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "t.PERSON_ID", equalTag = "=")
    private String personId;

    /**
     * 值1
     */
    @Column(name = "t.ATTR_VALUE1")
    private String attrValue1;

    /**
     * 值2
     */
    @Column(name = "t.ATTR_VALUE2")
    private String attrValue2;

    /**
     * 值3
     */
    @Column(name = "t.ATTR_VALUE3")
    private String attrValue3;

    /**
     * 值4
     */
    @Column(name = "t.ATTR_VALUE4")
    private String attrValue4;

    /**
     * 值5
     */
    @Column(name = "t.ATTR_VALUE5")
    private String attrValue5;

    /**
     * 值6
     */
    @Column(name = "t.ATTR_VALUE6")
    private String attrValue6;

    /**
     * 值7
     */
    @Column(name = "t.ATTR_VALUE7")
    private String attrValue7;

    /**
     * 值8
     */
    @Column(name = "t.ATTR_VALUE8")
    private String attrValue8;

    /**
     * 值9
     */
    @Column(name = "t.ATTR_VALUE9")
    private String attrValue9;

    /**
     * 值10
     */
    @Column(name = "t.ATTR_VALUE10")
    private String attrValue10;

    /**
     * 值11
     */
    @Column(name = "t.ATTR_VALUE11")
    private String attrValue11;

    /**
     * 值12
     */
    @Column(name = "t.ATTR_VALUE12")
    private String attrValue12;

    /**
     * 值13
     */
    @Column(name = "t.ATTR_VALUE13")
    private String attrValue13;

    /**
     * 值14
     */
    @Column(name = "t.ATTR_VALUE14")
    private String attrValue14;

    /**
     * 值15
     */
    @Column(name = "t.ATTR_VALUE15")
    private String attrValue15;

    /**
     * 值16
     */
    @Column(name = "t.ATTR_VALUE16")
    private String attrValue16;

    /**
     * 值17
     */
    @Column(name = "t.ATTR_VALUE17")
    private String attrValue17;

    /**
     * 值18
     */
    @Column(name = "t.ATTR_VALUE18")
    private String attrValue18;

    /**
     * 值19
     */
    @Column(name = "t.ATTR_VALUE19")
    private String attrValue19;

    /**
     * 值20
     */
    @Column(name = "t.ATTR_VALUE20")
    private String attrValue20;

    /**
     * 值21
     */
    @Column(name = "t.ATTR_VALUE21")
    private String attrValue21;

    /**
     * 值22
     */
    @Column(name = "t.ATTR_VALUE22")
    private String attrValue22;

    /**
     * 值23
     */
    @Column(name = "t.ATTR_VALUE23")
    private String attrValue23;

    /**
     * 值24
     */
    @Column(name = "t.ATTR_VALUE24")
    private String attrValue24;

    /**
     * 值25
     */
    @Column(name = "t.ATTR_VALUE25")
    private String attrValue25;

    /**
     * 值26
     */
    @Column(name = "t.ATTR_VALUE26")
    private String attrValue26;

    /**
     * 值27
     */
    @Column(name = "t.ATTR_VALUE27")
    private String attrValue27;

    /**
     * 值28
     */
    @Column(name = "t.ATTR_VALUE28")
    private String attrValue28;

    /**
     * 值29
     */
    @Column(name = "t.ATTR_VALUE29")
    private String attrValue29;

    /**
     * 值30
     */
    @Column(name = "t.ATTR_VALUE30")
    private String attrValue30;

    /**
     * 值31
     */
    @Column(name = "t.ATTR_VALUE31")
    private String attrValue31;

    /**
     * 值32
     */
    @Column(name = "t.ATTR_VALUE32")
    private String attrValue32;

    @Condition(value = "t.PERSON_ID", equalTag = "in")
    private String personIdIn;

    /**
     * 默认构造方法
     */
    public PersAttributeExtItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersAttributeExtItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersAttributeExtItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param attrValue1
     * @param attrValue2
     * @param attrValue3
     * @param attrValue4
     * @param attrValue5
     * @param attrValue6
     * @param attrValue7
     * @param attrValue8
     * @param attrValue9
     * @param attrValue10
     * @param attrValue11
     * @param attrValue12
     * @param attrValue13
     * @param attrValue14
     * @param attrValue15
     * @param attrValue16
     * @param attrValue17
     * @param attrValue18
     * @param attrValue19
     * @param attrValue20
     * @param attrValue21
     * @param attrValue22
     * @param attrValue23
     * @param attrValue24
     * @param attrValue25
     * @param attrValue26
     * @param attrValue27
     * @param attrValue28
     * @param attrValue29
     * @param attrValue30
     * @param attrValue31
     * @param attrValue32
     */
    public PersAttributeExtItem(String id, String attrValue1, String attrValue2, String attrValue3, String attrValue4,
        String attrValue5, String attrValue6, String attrValue7, String attrValue8, String attrValue9,
        String attrValue10, String attrValue11, String attrValue12, String attrValue13, String attrValue14,
        String attrValue15, String attrValue16, String attrValue17, String attrValue18, String attrValue19,
        String attrValue20, String attrValue21, String attrValue22, String attrValue23, String attrValue24,
        String attrValue25, String attrValue26, String attrValue27, String attrValue28, String attrValue29,
        String attrValue30, String attrValue31, String attrValue32) {
        super();
        this.id = id;
        this.attrValue1 = attrValue1;
        this.attrValue2 = attrValue2;
        this.attrValue3 = attrValue3;
        this.attrValue4 = attrValue4;
        this.attrValue5 = attrValue5;
        this.attrValue6 = attrValue6;
        this.attrValue7 = attrValue7;
        this.attrValue8 = attrValue8;
        this.attrValue9 = attrValue9;
        this.attrValue10 = attrValue10;
        this.attrValue11 = attrValue11;
        this.attrValue12 = attrValue12;
        this.attrValue13 = attrValue13;
        this.attrValue14 = attrValue14;
        this.attrValue15 = attrValue15;
        this.attrValue16 = attrValue16;
        this.attrValue17 = attrValue17;
        this.attrValue18 = attrValue18;
        this.attrValue19 = attrValue19;
        this.attrValue20 = attrValue20;
        this.attrValue21 = attrValue21;
        this.attrValue22 = attrValue22;
        this.attrValue23 = attrValue23;
        this.attrValue24 = attrValue24;
        this.attrValue25 = attrValue25;
        this.attrValue26 = attrValue26;
        this.attrValue27 = attrValue27;
        this.attrValue28 = attrValue28;
        this.attrValue29 = attrValue29;
        this.attrValue30 = attrValue30;
        this.attrValue31 = attrValue31;
        this.attrValue32 = attrValue32;
    }

    @Override
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getAttrValue1() {
        return attrValue1;
    }

    public void setAttrValue1(String attrValue1) {
        this.attrValue1 = attrValue1;
    }

    public String getAttrValue2() {
        return attrValue2;
    }

    public void setAttrValue2(String attrValue2) {
        this.attrValue2 = attrValue2;
    }

    public String getAttrValue3() {
        return attrValue3;
    }

    public void setAttrValue3(String attrValue3) {
        this.attrValue3 = attrValue3;
    }

    public String getAttrValue4() {
        return attrValue4;
    }

    public void setAttrValue4(String attrValue4) {
        this.attrValue4 = attrValue4;
    }

    public String getAttrValue5() {
        return attrValue5;
    }

    public void setAttrValue5(String attrValue5) {
        this.attrValue5 = attrValue5;
    }

    public String getAttrValue6() {
        return attrValue6;
    }

    public void setAttrValue6(String attrValue6) {
        this.attrValue6 = attrValue6;
    }

    public String getAttrValue7() {
        return attrValue7;
    }

    public void setAttrValue7(String attrValue7) {
        this.attrValue7 = attrValue7;
    }

    public String getAttrValue8() {
        return attrValue8;
    }

    public void setAttrValue8(String attrValue8) {
        this.attrValue8 = attrValue8;
    }

    public String getAttrValue9() {
        return attrValue9;
    }

    public void setAttrValue9(String attrValue9) {
        this.attrValue9 = attrValue9;
    }

    public String getAttrValue10() {
        return attrValue10;
    }

    public void setAttrValue10(String attrValue10) {
        this.attrValue10 = attrValue10;
    }

    public String getAttrValue11() {
        return attrValue11;
    }

    public void setAttrValue11(String attrValue11) {
        this.attrValue11 = attrValue11;
    }

    public String getAttrValue12() {
        return attrValue12;
    }

    public void setAttrValue12(String attrValue12) {
        this.attrValue12 = attrValue12;
    }

    public String getAttrValue13() {
        return attrValue13;
    }

    public void setAttrValue13(String attrValue13) {
        this.attrValue13 = attrValue13;
    }

    public String getAttrValue14() {
        return attrValue14;
    }

    public void setAttrValue14(String attrValue14) {
        this.attrValue14 = attrValue14;
    }

    public String getAttrValue15() {
        return attrValue15;
    }

    public void setAttrValue15(String attrValue15) {
        this.attrValue15 = attrValue15;
    }

    public String getAttrValue16() {
        return attrValue16;
    }

    public void setAttrValue16(String attrValue16) {
        this.attrValue16 = attrValue16;
    }

    public String getAttrValue17() {
        return attrValue17;
    }

    public void setAttrValue17(String attrValue17) {
        this.attrValue17 = attrValue17;
    }

    public String getAttrValue18() {
        return attrValue18;
    }

    public void setAttrValue18(String attrValue18) {
        this.attrValue18 = attrValue18;
    }

    public String getAttrValue19() {
        return attrValue19;
    }

    public void setAttrValue19(String attrValue19) {
        this.attrValue19 = attrValue19;
    }

    public String getAttrValue20() {
        return attrValue20;
    }

    public void setAttrValue20(String attrValue20) {
        this.attrValue20 = attrValue20;
    }

    public String getAttrValue21() {
        return attrValue21;
    }

    public void setAttrValue21(String attrValue21) {
        this.attrValue21 = attrValue21;
    }

    public String getAttrValue22() {
        return attrValue22;
    }

    public void setAttrValue22(String attrValue22) {
        this.attrValue22 = attrValue22;
    }

    public String getAttrValue23() {
        return attrValue23;
    }

    public void setAttrValue23(String attrValue23) {
        this.attrValue23 = attrValue23;
    }

    public String getAttrValue24() {
        return attrValue24;
    }

    public void setAttrValue24(String attrValue24) {
        this.attrValue24 = attrValue24;
    }

    public String getAttrValue25() {
        return attrValue25;
    }

    public void setAttrValue25(String attrValue25) {
        this.attrValue25 = attrValue25;
    }

    public String getAttrValue26() {
        return attrValue26;
    }

    public void setAttrValue26(String attrValue26) {
        this.attrValue26 = attrValue26;
    }

    public String getAttrValue27() {
        return attrValue27;
    }

    public void setAttrValue27(String attrValue27) {
        this.attrValue27 = attrValue27;
    }

    public String getAttrValue28() {
        return attrValue28;
    }

    public void setAttrValue28(String attrValue28) {
        this.attrValue28 = attrValue28;
    }

    public String getAttrValue29() {
        return attrValue29;
    }

    public void setAttrValue29(String attrValue29) {
        this.attrValue29 = attrValue29;
    }

    public String getAttrValue30() {
        return attrValue30;
    }

    public void setAttrValue30(String attrValue30) {
        this.attrValue30 = attrValue30;
    }

    public String getAttrValue31() {
        return attrValue31;
    }

    public void setAttrValue31(String attrValue31) {
        this.attrValue31 = attrValue31;
    }

    public String getAttrValue32() {
        return attrValue32;
    }

    public void setAttrValue32(String attrValue32) {
        this.attrValue32 = attrValue32;
    }

    public String getPersonIdIn() {
        return personIdIn;
    }

    public void setPersonIdIn(String personIdIn) {
        this.personIdIn = personIdIn;
    }
}