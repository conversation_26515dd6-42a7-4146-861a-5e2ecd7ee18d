package com.zkteco.zkbiosecurity.pers.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.regex.Pattern;

import org.apache.commons.lang3.StringUtils;

/**
 * 人事正则工具类 正则工具统一定义到一起，使用 Pattern 有预编译功能，效率比较高。
 * 
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 15:31 2018/12/18
 */
public class PersRegularUtil {
    /** 人员编号含有数字字母正则 */
    protected final static Pattern pinPattern = Pattern.compile("^(?!0$|0+$)[a-zA-Z0-9]+$");
    /** 16进制卡的正则 */
    protected final static Pattern cardPattern = Pattern.compile("[0-9a-fA-F]+");
    /** 人员编号或卡号不能以0开头 */
    public static Pattern zeroPattern = Pattern.compile("^0.*");

    /** 除省份车牌匹配正则 */
    public static Pattern carnumberPattern = Pattern.compile("^[0-9A-Z\\u4e00-\\u9fa5]+$");

    /** 国内手机号码正则匹配 */
    protected final static Pattern mobilePhonePattern = Pattern.compile("^1[1-9][0-9]\\d{8}$");

    public static final String[] UNCHAR =
        new String[] {"<", ">", "`", "~", "!", "@", "#", "$", "%", "^", "*", "?", "/", "|", "\\", ":", ";", "=", "\"",
            "'", ",", "--", "+", "，", "。", "、", "；", "‘", "’", "【", "】", "、", "！", "￥", "……", "&", "*", "（", "）", "——"};

    public static Pattern sqlPattern1 = Pattern.compile("[+]*(like|and|or|xor|rlike|not)(([+])|([\\s]+['0-9]+))", 2);
    public static Pattern sqlPattern2 = Pattern.compile("[+]*(like|and|or|xor|rlike|not)(([+]{2})|([\\s]+['0-9]+))", 2);
    public static Pattern sqlPattern3 = Pattern.compile(
        "((like|and|or|xor|rlike|not)[\\s]*[%'])|([%'][\\s]*(like|and|or|xor|rlike|not)[\\W]+)|(\\|\\|)|(&&)", 2);

    /**
     * 是否含有数字和字母
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/18 15:41
     * @param pin
     * @return boolean
     */
    public static boolean hasNumberOrLetter(String pin) {
        return pinPattern.matcher(pin).matches();
    }

    /**
     * 是否为16进制卡
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/18 15:42
     * @param cardNo
     * @return boolean
     */
    public static boolean isHex(String cardNo) {
        return cardPattern.matcher(cardNo).matches();
    }

    /**
     * 是否含有特殊字符
     * 
     * @param value
     * @return
     */
    public static boolean hasSpecialChar(String value) {
        // 特殊字符限制增加"--", "+"
        String[] specialChars = UNCHAR;
        for (String c : specialChars) {
            if (value.contains(c)) {
                return true;
            }
        }
        return false;
    }

    // 身份证号码正确性校验,默认18位身份证
    // 省(直辖市)码表
    private static String provinceCode[] =
        {"11", "12", "13", "14", "15", "21", "22", "23", "31", "32", "33", "34", "35", "36", "37", "41", "42", "43",
            "44", "45", "46", "50", "51", "52", "53", "54", "61", "62", "63", "64", "65", "71", "81", "82", "91"};
    // 身份证前17位每位加权因子
    private static int[] powFactor = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
    // 身份证第18位校检码
    private static char[] verifyValue = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
    /**
     * 身份证正则表达式 1.以非0位开头的数字6位 1 -6位。①1-2 升级行政区代码 ②3-4 地级行政区划分代码 ③5-6 县区行政区分代码 2.以19或20开头的的8位yyyymmdd的日期数据。 7 - 14位。
     * 3.15-17 顺序码，同一地区同年、同月、同日出生人的编号，奇数是男性，偶数是女性 4.18 校验码，如果是0-9则用0-9表示，如果是10则用X（罗马数字10）表示
     */
    private static String idNumberPattern =
        "^[1-9][0-9]{5}(?:19|20)?[0-9]{2}(?:0[1-9]|1[012])(?:0[1-9]|[12][0-9]|3[01])[0-9]{3}[0-9Xx]?$";
    private static Pattern regex = Pattern.compile(idNumberPattern);

    public static boolean idNumberCheck(String idNum) {
        idNum = idNum.toUpperCase().trim();
        if (null == idNum || idNum.trim().length() != 18) {
            return false;
        }
        char checkValue = getCheckValue(idNum);
        if (regex.matcher(idNum).find() && isValidProvinceId(idNum.substring(0, 2)) && checkValue == idNum.charAt(17)
            && isValidDate(idNum.substring(6, 14))) {
            return true;
        }
        return false;
    }

    /**
     * 检查身份证的省份信息是否正确
     * 
     * @param provinceId
     * @return
     */
    private static boolean isValidProvinceId(String provinceId) {
        for (String id : provinceCode) {
            if (id.equals(provinceId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验一个日期的合法性，排除如2月30日这种日期
     * 
     * @param str
     * @return
     */
    private static boolean isValidDate(String str) {
        boolean convertSuccess = true;
        // 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        try {
            // 设置lenient为false. 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException e) {
            // e.printStackTrace();
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
            convertSuccess = false;
        }
        return convertSuccess;
    }

    private static char getCheckValue(String idNum) {
        int sum = 0;

        for (int i = 0; i < 17; i++) {
            sum = sum + Integer.parseInt(idNum.substring(i, i + 1)) * powFactor[i];
        }
        return verifyValue[sum % 11];
    }

    /** 邮箱正则 */
    public static Pattern emailPattern = Pattern.compile("^[-a-zA-Z0-9._%+']+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    /**
     * 国内手机号码正则匹配
     * 
     * @param mobilePhone:
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2022-04-07 17:39
     * @since 1.0.0
     */
    public static boolean isValidMobilePhone(String mobilePhone) {
        return mobilePhonePattern.matcher(mobilePhone).matches();
    }

    /**
     * 1位不隐藏，后面加一个* 2-3位时，隐藏第2位 4-5位时，隐藏第3位 6-7位时，隐藏第3-4位 8-9位时，隐藏第3-5位 10位及以上，隐藏第3-6位
     *
     * @param number:
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2022-07-20 9:29
     * @since 1.0.0
     */
    public static String hideWithAsterisk(String number) {
        if (StringUtils.isNotBlank(number)) {
            int numberLength = number.length();
            int length = numberLength / 2;
            if (length > 0) {
                String[] numbers = number.split("");
                if (length == 1) {
                    numbers[1] = "*";
                } else if (length > 1 && length < 5) {
                    for (int j = 2; j < length + 1; j++) {
                        numbers[j] = "*";
                    }
                } else {
                    for (int j = 2; j < 6; j++) {
                        numbers[j] = "*";
                    }
                }
                number = StringUtils.join(numbers, "");
            } else {
                number = number + "*";
            }
        }
        return number;
    }

    /**
     * 判断是否含有sql语句关键词
     *
     * @param str
     * @return
     */
    public static boolean hasSqlKey(String str) {
        if (sqlPattern1.matcher(str).find() || sqlPattern2.matcher(str).find() || sqlPattern3.matcher(str).find()) {
            return true;
        }
        return false;
    }
}
