package com.zkteco.zkbiosecurity.pers.api.vo;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人员基础信息VO
 *
 * <AUTHOR>
 * @date 2021-11-16 10:48
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class ApiPersonBaseInfoItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @ApiModelProperty(hidden = true)
    private String id;

    /**
     * 人员编号
     */
    @ApiModelProperty(allowableValues = "1234567")
    private String pin;

    /**
     * 部门编号
     */
    @ApiModelProperty(allowableValues = "1")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(hidden = true)
    private String deptName;

    /**
     * 人员姓名
     */
    @ApiModelProperty(allowableValues = "max")
    private String name;

    /**
     * 海外（人员姓氏）
     */
    @ApiModelProperty(allowableValues = "lastName")
    private String lastName;

    /**
     * 人员性别
     */
    @ApiModelProperty(value = "F:Female M:Male", allowableValues = "F")
    private String gender;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "YYYY-MM-DD", allowableValues = "2016-07-15")
    private String birthday;

    /**
     * 卡号
     */
    @ApiModelProperty(value = "main card", allowableValues = "123456789")
    private String cardNo;

    /**
     * 副卡
     */
    @ApiModelProperty(allowableValues = "987643", value = "card1,card2")
    private String supplyCards;

    /**
     * 自助密码
     */
    // @ApiModelProperty(allowableValues = "123456")
    @ApiModelProperty(hidden = true)
    private String selfPwd;

    /**
     * 邮件通知
     */
    private Boolean isSendMail = false;

    /**
     * 手机号码
     */
    @ApiModelProperty(allowableValues = "15123456789")
    private String mobilePhone;

    /**
     * 设备验证密码
     */
    @ApiModelProperty(allowableValues = "123456")
    private String personPwd;

    /**
     * 邮件
     */
    @ApiModelProperty(allowableValues = "<EMAIL>")
    private String email;

    /**
     * 社会安全号
     */
    @ApiModelProperty(allowableValues = "111111")
    private String ssn;

    /**
     * 证件类型
     */
    @ApiModelProperty(allowableValues = "2")
    private String certType;

    /**
     * 证件号码
     */
    @ApiModelProperty(allowableValues = "12345678")
    private String certNumber;

    /**
     * 入职时间
     */
    @ApiModelProperty(value = "入职时间", example = " 2019-06-10")
    private Date hireDate;

}