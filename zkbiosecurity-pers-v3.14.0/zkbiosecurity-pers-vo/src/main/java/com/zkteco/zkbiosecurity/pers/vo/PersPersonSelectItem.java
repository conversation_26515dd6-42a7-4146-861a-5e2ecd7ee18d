package com.zkteco.zkbiosecurity.pers.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@From(after = "PERS_PERSON t")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonSelectItem extends BaseItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN")
    private String persPin;

    /**
     * 姓名
     */
    @Column(name = "t.NAME")
    private String persName;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    private String persLastName;
    /**
     * 人员类型
     */
    @Column(name = "t.PERSON_TYPE")
    private Short personType;

    /**
     * 性别
     */
    @Column(name = "t.GENDER", equalTag = "=")
    private String gender;

    /**
     * 部门编码
     */
    @GridColumn(label = "pers_dept_deptNo", width = "100", sort = "na")
    private String deptCode;

    /**
     * 部门
     */
    @GridColumn(label = "pers_dept_deptName", width = "100", sort = "na")
    private String deptName;
    /**
     * 卡号
     */

    @Condition(
        value = "t.ID IN (SELECT pc.PERSON_ID FROM PERS_CARD pc WHERE pc.CARD_STATE=1 AND pc.CARD_NO LIKE ''%{0}%'')",
        encryptConverter = true)
    private String cardNo;

    /**
     * 部门id
     */
    @Column(name = "t.AUTH_DEPT_ID")
    private String deptId;

    /** 身份证 */
    @Column(name = "t.ID_CARD", equalTag = "=", encryptConverter = true)
    private String idCard;

    /** 邮箱 */
    @Column(name = "t.EMAIL", encryptConverter = true)
    private String email;

    /**
     * 联系电话
     */
    @Column(name = "t.MOBILE_PHONE", encryptConverter = true)
    private String mobilePhone;

    /**
     * 启禁用凭证
     */
    @Column(name = "t.ENABLED_CREDENTIAL")
    private Boolean enabledCredential;

    /**
     * 类型 LinkTypeEnum 枚举
     */
    private String type;

    /**
     * 链接ID
     */
    private String linkId;

    private String notInlinkId;

    private String inlinkId;

    /**
     * 部门ID的OR语句查询
     */
    private String orInDeptId;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "not in")
    private String notInDeptId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    /** 已弃用, 给值也不生效 */
    @Deprecated
    private String startPin;
    /** 已弃用, 给值也不生效 */
    @Deprecated
    private String endPin;

    @Condition(value = "(LOWER (t.NAME) LIKE LOWER (''%{0}%'') OR LOWER (t.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

    // 是否包含下级
    private String isIncludeLower;

    private String isNotNullField;

    /**
     * 异常标志位
     */
    @Column(name = "t.EXCEPTION_FLAG")
    private Short exceptionFlag;

    @Column(name = "t.NUMBER_PIN")
    private Long numberPin;

    @Condition(value = "t.DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID = ''{0}'' )")
    private String userId;

    public PersPersonSelectItem() {
        super();
    }

    public PersPersonSelectItem(Boolean equals) {
        super(equals);
    }

    public PersPersonSelectItem(String id) {
        super();
        this.id = id;
    }

    public PersPersonSelectItem(String id, String persPin, String persName, String persLastName, String cardNo,
        String gender, String deptName, String deptId, String inId, String notInId) {
        super();
        this.id = id;
        this.persPin = persPin;
        this.persName = persName;
        this.persLastName = persLastName;
        this.cardNo = cardNo;
        this.gender = gender;
        this.deptName = deptName;
        this.deptId = deptId;
        this.inId = inId;
        this.notInId = notInId;
    }
}
