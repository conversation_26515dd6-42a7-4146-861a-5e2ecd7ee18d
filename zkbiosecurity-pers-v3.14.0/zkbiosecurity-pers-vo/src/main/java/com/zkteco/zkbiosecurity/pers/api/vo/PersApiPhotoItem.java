package com.zkteco.zkbiosecurity.pers.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 更新人员头像VO
 *
 * <AUTHOR>
 * @date 2021-11-17 11:48
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class PersApiPhotoItem implements Serializable {

    /**
     * 人员id
     */
    @ApiModelProperty(hidden = true)
    private String id;
    /**
     * 人员编号
     */
    @ApiModelProperty(allowableValues = "1234567")
    private String pin;

    /**
     * 图片路径
     */
    // @ApiModelProperty(value = "图片路径", example = "/upload/pers/user")
    @ApiModelProperty(hidden = true)
    private String photoPath;

    /**
     * 人员照片（base64）
     */
    @ApiModelProperty(allowableValues = "Base64", value = "")
    private String personPhoto;

    /**
     * app前端语言编码
     */
    @ApiModelProperty(hidden = true)
    private String lang;
}
