package com.zkteco.zkbiosecurity.pers.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersBioTemplate2OtherItem {
    private String personId;
    private String pin;
    private Short validType;
    private Short bioType;
    private String version;
    private String template;
    private Short templateNo;
    private Short templateNoIndex;
    private Boolean duress;
}
