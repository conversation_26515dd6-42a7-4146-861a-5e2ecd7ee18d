/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonBioTemplateItem implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 生物特征模板内容
     */
    private String template;
    /**
     * 生物特征模板编号
     */
    private Short templateNo;
    /**
     * 生物特征模板对应索引
     */
    private Short templateNoIndex;
    /**
     * 是否胁迫
     */
    private Boolean duress;

    /**
     * 版本
     */
    private String version;

    public PersPersonBioTemplateItem() {
        super();
    }

    /**
     * @param template
     * @param templateNo
     * @param templateNoIndex
     */
    public PersPersonBioTemplateItem(String template, Short templateNo, Short templateNoIndex) {
        super();
        this.template = template;
        this.templateNo = templateNo;
        this.templateNoIndex = templateNoIndex;
    }
}