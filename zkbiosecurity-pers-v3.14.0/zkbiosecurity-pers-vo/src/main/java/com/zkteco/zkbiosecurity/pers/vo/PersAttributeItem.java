/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(after = "PERS_ATTRIBUTE t ")
@OrderBy(after = "t.POSITION_X,t.POSITION_Y")
@GridConfig(operate = true, idField = "id", winHeight = 450, winWidth = 400,
    operates = {
        @GridOperate(type = "edit", permission = "pers:attribute:edit", url = "persAttribute.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "pers:attribute:del", url = "persAttribute.do?del&attrName=(showName)",
            label = "common_op_del", showConvertor = "showConvertor")})
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersAttributeItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 参数名字
     */
    @Column(name = "t.ATTR_NAME")
    private String attrName;

    /**
     * 显示名称
     */
    @Column(name = "t.ATTR_NAME")
    @GridColumn(label = "pers_attribute_attrName", width = "120",
        format = "street=pers_attr_street,nation=pers_attr_nation,emp_type=pers_attr_emp_type,"
            + "office_address=pers_attr_office_address,office_phone=pers_attr_office_phone,home_phone=pers_attr_home_phone,job_title=pers_attr_job_title,"
            + "birthplace=pers_attr_birthplace,polit_status=pers_attr_polit_status,country=pers_attr_country,hire_type=pers_attr_hire_type,"
            + "home_address=pers_attr_home_address,postcode=pers_attr_postcode")
    private String showName;

    /**
     * 参数的值列表
     */
    @Column(name = "t.VALUE_LIST")
    @GridColumn(label = "pers_attribute_attrValue", width = "150")
    private String attrValue;

    /**
     * 控件类型
     */
    @Column(name = "t.CONTROL_TYPE")
    @GridColumn(label = "pers_attribute_controlType", width = "100",
        format = "select=pers_attrDefini_select,checkbox=pers_attrDefini_check,radio=pers_attrDefini_radio,text=pers_attrDefini_text")
    private String controlType;

    /**
     * 是否是初始化参数
     */
    @Column(name = "t.SQL_STR")
    private String sqlStr;

    /**
     * 控件坐标x轴
     */
    @Column(name = "t.POSITION_X")
    @GridColumn(label = "pers_attribute_positionX")
    private Integer positionX;

    /**
     * 控件坐标y轴
     */
    @Column(name = "t.POSITION_Y")
    @GridColumn(label = "pers_attribute_positionY")
    private Integer positionY;

    /**
     * 人员类型
     */
    @Column(name = "t.PERSON_TYPE")
    private Short personType;

    /**
     * 列索引
     */
    @Column(name = "t.FILED_INDEX")
    private Integer filedIndex;

    /**
     * 是否在表格中显示
     */
    @Column(name = "t.SHOW_TABLE")
    @GridColumn(label = "pers_attribute_showTable", width = "160", format = "true=common_yes,false=common_no")
    private Boolean showTable;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    /**
     * 用于人员编辑具体的值
     */
    private String val;

    /**
     * 默认构造方法
     */
    public PersAttributeItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersAttributeItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersAttributeItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param attrName
     * @param controlType
     * @param attrValue
     * @param sqlStr
     * @param positionX
     * @param positionY
     * @param personType
     * @param filedIndex
     * @param showTable
     */
    public PersAttributeItem(String id, String attrName, String controlType, String attrValue, String sqlStr,
        Integer positionX, Integer positionY, Short personType, Integer filedIndex, Boolean showTable) {
        super();
        this.id = id;
        this.attrName = attrName;
        this.controlType = controlType;
        this.attrValue = attrValue;
        this.sqlStr = sqlStr;
        this.positionX = positionX;
        this.positionY = positionY;
        this.personType = personType;
        this.filedIndex = filedIndex;
        this.showTable = showTable;
    }

    /**
     * @param attrName
     * @param controlType
     * @param attrValue
     * @param sqlStr
     * @param positionX
     * @param positionY
     * @param personType
     * @param filedIndex
     */
    public PersAttributeItem(String attrName, String controlType, String attrValue, String sqlStr, int positionX,
        int positionY, Short personType, int filedIndex) {
        this.controlType = controlType;
        this.attrName = attrName;
        this.attrValue = attrValue;
        this.sqlStr = sqlStr;
        this.positionX = positionX;
        this.positionY = positionY;
        this.personType = personType;
        this.filedIndex = filedIndex;
        this.showTable = false;
    }

    public boolean showConvertor() {
        if ("initHep".equals(this.sqlStr)) {
            return false;
        }
        return true;
    }
}