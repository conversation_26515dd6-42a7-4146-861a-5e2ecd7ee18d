package com.zkteco.zkbiosecurity.pers.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 部门-VO
 *
 * <AUTHOR>
 * @Date: 2018/11/7 17:54
 */
@Getter
@Setter
@Accessors(chain = true)
public class PersApiDepartmentItem implements Serializable {

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门编号
     */
    private String code;

    /**
     * 排序编号
     */
    @ApiModelProperty(allowableValues = "999999")
    private Integer sortNo;

    /**
     * 上级部门编号
     */
    private String parentCode;
}
