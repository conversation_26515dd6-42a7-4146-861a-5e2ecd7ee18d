package com.zkteco.zkbiosecurity.pers.api.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人脸检测VO
 *
 * <AUTHOR>
 * @date 2024/8/19 15:50
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class PersApiFacePhotoItem implements Serializable {
    /**
     * 人员照片（base64）
     */
    @ApiModelProperty(allowableValues = "Base64", value = "")
    private String personPhoto;
}
