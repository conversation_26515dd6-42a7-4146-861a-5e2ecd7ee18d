package com.zkteco.zkbiosecurity.pers.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 通知其他模块人员离职Item
 *
 * <AUTHOR>
 * @date 2023/3/29 11:21
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class PersPushLeavePersonItem implements Serializable {
    /** 人员id */
    private String personId;
    /** 人员pin */
    private String pin;
    /** 离职人员id */
    private String leaveId;
}
