package com.zkteco.zkbiosecurity.pers.app.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date: 2018/12/3 15:03
 */
@Getter
@Setter
@Accessors(chain = true)
public class PersAppPersonLevelItem implements Serializable {

    /**
     * 权限组ID
     */
    private String accLevelId;

    /**
     * 权限组名称
     */
    private String accLevelName;

    /**
     * 是否选中权限组
     */
    private Boolean selected;
}
