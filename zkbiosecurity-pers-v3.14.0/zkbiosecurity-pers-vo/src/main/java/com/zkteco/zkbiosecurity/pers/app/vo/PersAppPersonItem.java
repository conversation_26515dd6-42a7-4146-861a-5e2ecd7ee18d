package com.zkteco.zkbiosecurity.pers.app.vo;

import java.io.Serializable;
import java.util.List;

import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPersonItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * APP人员VO
 * 
 * <AUTHOR>
 * @Date: 2018/11/30 11:46
 */
@Getter
@Setter
@Accessors(chain = true)
public class PersAppPersonItem extends PersApiPersonItem implements Serializable {

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 人员图片
     */
    private String photoImg;

    /**
     * 出生日期
     */
    private String birthdayStr;

    /**
     * 是否设备管理员，true是设备管理员否则不是
     */
    private Boolean opAuth;

    /**
     * 人员权限组信息
     */
    private List<PersAppPersonLevelItem> accLevels;

    /**
     * 指纹数量
     */
    private Integer fingerTemplateCount;

    /**
     * 人脸数量
     */
    private Integer faceTemplateCount;

    /**
     * 指静脉数量
     */
    private Integer veinTemplateCount;

    /**
     * 新增权限组ID
     */
    private String accAddAccLevelIds;

    /**
     * 删除权限组ID
     */
    private String accDelAccLevelIds;

    /**
     * 验证token
     */
    private String token;

    private String photoImgBase64;

    /**
     * 授权登录app
     */
    private Boolean appAuthorization;

    private String gender;
}
