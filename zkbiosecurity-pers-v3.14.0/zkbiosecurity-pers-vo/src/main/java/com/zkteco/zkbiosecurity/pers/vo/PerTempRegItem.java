package com.zkteco.zkbiosecurity.pers.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 移动端注册业务层数据实体类
 * 
 * <AUTHOR> href="mailto:<EMAIL>">colin.cheng</a>
 * @version V1.0
 * @date Created In 13:39 2019/3/28
 */
@Setter
@Getter
@ToString
public class PerTempRegItem {
    private String id;
    private String pin;
    private String name;
    private String phone;
    private String photo;
    private String lastName;

    /**
     * 是否有接触可疑病例或者患者
     */
    private String exposure;

    /**
     * 症状
     */
    private String symptom;

    /**
     * 到访过的城市
     */
    private String visitCity;

    /**
     * 健康信息备注
     */
    private String healthRemarks;
}
