/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import java.util.Date;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(
    after = "PERS_PERSON t LEFT JOIN PERS_POSITION p ON t.POSITION_ID=p.ID LEFT JOIN PERS_CERTIFICATE c on c.PERSON_ID = t.ID LEFT join AUTH_DEPARTMENT d on t.auth_dept_id = d.id")
@OrderBy(after = "CASE WHEN t.ID=:orderId THEN 1 ELSE 999 END, t.UPDATE_TIME DESC, t.ID DESC")
@GridConfig(operate = true, idField = "id", winHeight = 670, winWidth = 1000, operates = {
    @GridOperate(type = "edit", permission = "pers:person:edit", url = "persPerson.do?edit", label = "common_op_edit"),
    @GridOperate(type = "del", permission = "pers:person:del", url = "persPerson.do?del&pins=(pin)",
        label = "common_op_del")})
@Getter
@Setter
@Accessors(chain = true)
public class PersPersonItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN")
    @GridColumn(columnType = "edit", label = "pers_person_pin", width = "110", editPermission = "pers:person:edit",
        editUrl = "persPerson.do?edit&id=(id)", encryptMode = "${pers.pin.encryptMode}",
        permission = "pers:pin:encryptProp")
    private String pin;

    /**
     * 名字
     */
    @Column(name = "t.NAME")
    @GridColumn(label = "pers_person_name", width = "110", encryptMode = "${pers.name.encryptMode}",
        permission = "pers:name:encryptProp")
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "110", showExpression = "#language!='zh_CN'",
        encryptMode = "${pers.lastName.encryptMode}", permission = "pers:name:encryptProp")
    private String lastName;

    /**
     * 部门编码
     */
    @GridColumn(label = "pers_dept_deptNo", show = false)
    @Column(name = "d.CODE")
    private String deptCode;

    /**
     * 部门名称
     */
    @GridColumn(label = "pers_dept_deptName", width = "150", sort = "na")
    @Column(name = "d.NAME")
    private String deptName;

    /**
     * 职位名称
     */
    @GridColumn(label = "pers_position_name", width = "120")
    @Column(name = "p.NAME")
    private String positionName;

    /**
     * 部门ID
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 限制连接数
     */
    @Column(name = "t.PERS_LOGIN_LIMIT")
    private Integer persLoginLimit;
    /**
     * 多卡
     */
    @Condition(value = "t.ID IN (SELECT c.PERSON_ID FROM PERS_CARD c WHERE c.CARD_STATE=1 AND c.CARD_NO IN(%s))",
        formatType = "quote")
    @GridColumn(label = "pers_card_cardNo", width = "120", sort = "na", encryptMode = "${pers.cardNo.encryptMode}",
        permission = "pers:cardNo:encryptProp")
    private String cardNos;

    /**
     * 用于卡号模糊查询
     */
    @Condition(value = "t.ID IN (SELECT c.PERSON_ID FROM PERS_CARD c WHERE c.CARD_NO LIKE ''%{0}%'')",
        encryptConverter = true)
    private String likeCardNo;

    /**
     * 人员具有的设备验证手段
     */
    @GridColumn(columnType = "custom", label = "common_verifyMode_entiy", sort = "na", width = "140",
        convert = "persConvertVerifyMode")
    private String verifyMode;

    /**
     * 启禁用凭证
     */
    @Column(name = "t.ENABLED_CREDENTIAL")
    @GridColumn(label = "common_enable", width = "100", columnType = "custom", convert = "convertToIcon",
        showHeader = "persShowEnabledCredential")
    private Boolean enabledCredential;

    /**
     * 授权登录app
     */
    @Column(name = "t.APP_AUTHORIZATION")
    @GridColumn(label = "pers_applogin_status", width = "130", columnType = "custom",
        convert = "convertAppAuthorization", showHeader = "persShowAppAuthorization")
    private Boolean appAuthorization;

    /**
     * 创建时间
     */
    @Column(name = "t.CREATE_TIME")
    @GridColumn(label = "pers_person_createTime", width = "150")
    private Date createTime;

    /**
     * 性别
     */
    @Column(name = "t.GENDER")
    @GridColumn(label = "pers_person_gender", format = "F=pers_person_female,M=pers_person_male,U=common_unknown",
        encryptMode = "${pers.gender.encryptMode}", permission = "pers:gender:encryptProp")
    private String gender;

    /**
     * 照片路径
     */
    @Column(name = "t.PHOTO_PATH")
    private String photoPath;

    /**
     * 缩略图地址
     */
    private String thumbPhotoPath;

    /**
     * 状态
     */
    @Column(name = "t.STATUS")
    private Short status;

    /**
     * 人员类型
     */
    @Column(name = "t.PERSON_TYPE")
    private Short personType;

    /**
     * 出生日期
     */
    @Column(name = "t.BIRTHDAY")
    @DateType(type = "date")
    @GridColumn(label = "pers_person_birthday", encryptMode = "${pers.birthday.encryptMode}",
        permission = "pers:birthday:encryptProp")
    private Date birthday;

    /**
     * 联系电话
     */
    @Column(name = "t.MOBILE_PHONE", encryptConverter = true)
    @GridColumn(label = "pers_person_mobilePhone", width = "80", encryptMode = "${pers.mobilePhone.encryptMode}",
        permission = "pers:mobilePhone:encryptProp")
    private String mobilePhone;

    /**
     * 邮箱
     */
    @Column(name = "t.EMAIL", encryptConverter = true)
    @GridColumn(label = "pers_person_email", width = "150", encryptMode = "${pers.email.encryptMode}",
        permission = "pers:email:encryptProp")
    private String email;

    /**
     * 人员密码
     */
    @Column(name = "t.PERSON_PWD", equalTag = "=", encryptConverter = true)
    @GridColumn(label = "pers_person_password", show = false)
    private String personPwd;

    /**
     * 社会安全号
     */
    @Column(name = "t.SSN")
    private String ssn;

    /**
     * 车牌号
     */
    @Column(name = "t.CAR_PLATE")
    @GridColumn(label = "pers_person_carPlate", show = false)
    private String carPlate;

    /**
     * 自助密码
     */
    @Column(name = "t.SELF_PWD", encryptConverter = true)
    @GridColumn(label = "pers_person_reseCode", show = false)
    private String selfPwd;

    /**
     * 异常标志位
     */
    @Column(name = "t.EXCEPTION_FLAG")
    @GridColumn(label = "common_status", width = "70", sort = "na",
        format = "0=common_commStatus_normal,1=pers_person_cardDuress,2=pers_person_pwdException,3=pers_person_pinException")
    private Short exceptionFlag;

    /**
     * 是否发送邮件
     */
    @Column(name = "t.IS_SENDMAIL")
    private Boolean isSendMail;

    /**
     * 入职时间
     */
    @Column(name = "t.HIRE_DATE")
    @GridColumn(label = "pers_cardTemplate_entryDate", show = false)
    private Date hireDate;
    /**
     * 身份证
     */
    @Column(name = "t.ID_CARD", equalTag = "=", encryptConverter = true)
    private String idCard;

    /**
     * 证件号码
     */
    @Column(name = "c.CERT_NUMBER")
    private String certNumber;
    /**
     * 身份证物理卡号
     */
    @Column(name = "t.ID_CARD_PHYSICAL_NO", equalTag = "=", encryptConverter = true)
    private String idCardPhysicalNo;

    /**
     * 人员来源
     */
    @Column(name = "t.IS_FROM", equalTag = "=")
    private String isFrom;

    /**
     * 人员来源
     */
    @Condition(value = "t.IS_FROM", equalTag = "in")
    private String isFromIn;

    /**
     * 人员来源
     */
    @Condition(value = "(t.IS_FROM NOT IN (%s) OR t.IS_FROM IS NULL)", formatType = "quote")
    private String isFromNotIn;

    /**
     * 职位ID
     */
    @Column(name = "p.ID", equalTag = "=")
    private String positionId;

    @Condition(value = "p.ID", equalTag = "in")
    private String inPositionId;
    /**
     * 职位code
     */
    @Column(name = "p.CODE", equalTag = "=")
    private String positionCode;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.ID", equalTag = "not in")
    private String notInId;

    @Condition(value = "t.PIN", equalTag = "in")
    private String inPin;

    @Condition(value = "t.PIN", equalTag = "not in")
    private String notInPin;

    @Condition(value = "t.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    @Condition(value = "d.CODE", equalTag = "in")
    private String inDeptCode;

    @GridColumn(dynamicColumn = "persPersonDyna")
    private Map attrExtMap;

    @Column(name = "t.APP_ID")
    private String appId;

    @Condition(value = "(LOWER(t.NAME) LIKE LOWER(''%{0}%'') OR LOWER(t.LAST_NAME) LIKE LOWER(''%{0}%''))")
    private String likeName;

    @Condition(
        value = "(LOWER(t.NAME) LIKE LOWER(''%{0}%'') OR LOWER(t.LAST_NAME) LIKE LOWER(''%{0}%'') OR LOWER(c.CERT_NUMBER) LIKE LOWER(''%{0}%'') OR LOWER(t.PIN) LIKE LOWER(''%{0}%''))")
    private String likeCurrency;

    @Condition(value = "t.HIRE_DATE", equalTag = ">=")
    private Date beginDate;

    @Condition(value = "t.HIRE_DATE", equalTag = "<=")
    private Date endDate;

    /*存放抠图路径，该属性文件有值则直接当作抠图使用*/
    private String cropPhotoPath;

    @Column(name = "t.UPDATE_TIME")
    private Date updateTime;

    /**
     * 是否发送短信
     */
    @Column(name = "t.SEND_SMS")
    private Boolean sendSMS;

    /**
     * 是否发送WhatsApp
     */
    @Column(name = "t.SEND_WHATSAPP")
    private Boolean sendWhatsapp;

    /**
     * whatsapp账号
     */
    @Column(name = "t.WHATSAPP_MOBILENO")
    private String whatsappMobileNo;

    /**
     * app 推送
     */
    @Column(name = "t.SEND_APP")
    private Boolean sendApp;

    //微信openid

    @Column(name = "t.wechat_open_id")
    private String wechatOpenId;

    /**
     * 国家区号
     */
    private String areaCode;

    /**
     * 是否字母
     */
    private Boolean pinLetter;

    // --pro--

    /** threatLevel威胁等级 mqtt */
    private String threatLevel;

    /**
     * base64照片数据
     */
    private String photoBase64;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
        value = "t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    @Column(name = "t.NUMBER_PIN")
    private Long numberPin;

    @Column(name = "t.EXISTS_MOBILE_USER")
    private Boolean existsMobileUser;

    @Condition(value = "(t.NAME =''{0}'' OR t.NAME_SPELL =''{0}'')")
    private String nameSearch;



    /**
     * 职位名称
     */
    @Condition(value = "p.NAME",equalTag = "=")
    private String eqlPositionName;


    @Condition(value = "d.id",equalTag = "=")
    private String eqlDeptId;

    /**
     * base64抠图数据
     */
    private String cropPhotoBase64;

    /**
     * 是否删除抠图数据
     */
    private Boolean cropPhotoDel;

    /**
     * 新增虚拟卡数量
     */
    private String acmsCardNum;

    /**
     * acms是否为主卡
     */
    private String acmsMasterCard;

    /**
     * base64掌纹比对照片数据
     */
    private String plamPhotoBase64;

    /**
     * 是否删除掌纹比对照片数据
     */
    private Boolean plamPhotoDel;

    /**
     * id的别名字段，用于下拉grid
     */
    private String orderId;

    /**
     * 默认构造方法
     */
    public PersPersonItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersPersonItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersPersonItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param createTime
     * @param pin
     * @param name
     * @param lastName
     * @param gender
     * @param photoPath
     * @param status
     * @param personType
     * @param birthday
     * @param mobilePhone
     * @param email
     * @param personPwd
     * @param ssn
     * @param carPlate
     * @param selfPwd
     * @param exceptionFlag
     * @param isSendMail
     * @param hireDate
     * @param idCard
     * @param idCardPhysicalNo
     * @param isFrom
     */
    public PersPersonItem(String id, Date createTime, String pin, String name, String lastName, String gender,
        String photoPath, Short status, Short personType, Date birthday, String mobilePhone, String email,
        String personPwd, String ssn, String carPlate, String selfPwd, Short exceptionFlag, Boolean isSendMail,
        Date hireDate, String idCard, String idCardPhysicalNo, String isFrom) {
        super();
        this.id = id;
        this.createTime = createTime;
        this.pin = pin;
        this.name = name;
        this.lastName = lastName;
        this.gender = gender;
        this.photoPath = photoPath;
        this.status = status;
        this.personType = personType;
        this.birthday = birthday;
        this.mobilePhone = mobilePhone;
        this.email = email;
        this.personPwd = personPwd;
        this.ssn = ssn;
        this.carPlate = carPlate;
        this.selfPwd = selfPwd;
        this.exceptionFlag = exceptionFlag;
        this.isSendMail = isSendMail;
        this.hireDate = hireDate;
        this.idCard = idCard;
        this.idCardPhysicalNo = idCardPhysicalNo;
        this.isFrom = isFrom;
    }
}