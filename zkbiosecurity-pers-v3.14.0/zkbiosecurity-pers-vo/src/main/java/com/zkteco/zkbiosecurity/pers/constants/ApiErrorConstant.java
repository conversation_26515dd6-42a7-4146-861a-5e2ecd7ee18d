package com.zkteco.zkbiosecurity.pers.constants;

import java.util.HashMap;
import java.util.Map;

/**
 * 错误码
 * @Description:
 * <AUTHOR>
 * @version 1.0.0
 * @since 2018年12月28日 上午11:12:15
 */
public class ApiErrorConstant
{
	/**缺少必选参数**/
	public static final Integer ERROR_CODE_ARGUMENTS_MISSING = -40;
	/**非法的参数**/
	public static final Integer ERROR_CODE_ARGUMENTS_INVALID = -41;
	/**系统错误 code **/
	public static final Integer ERROR_CODE_SYSTEM_ERROR = -102;
	
	public static final Integer ERROR_CODE_PLAT_UPLOAD_FAIL	= -3	;//Upload Fail	图片上传失败	将传入的图片格式改为正确的格式、适当的大小的图片放进消息体里面传输过来，如果传输仍然失败需要减小图片大小或者增加网络带宽进行尝试
	public static final Integer ERROR_CODE_PLAT_APPCALL_LIMITED = -7;//	App Call Limited	应用调用次数超限，包含调用频率超限	调整程序合理调用API，等限频时间过了再调用
	public static final Integer ERROR_CODE_PLAT_NOT_ALLOWED_HTTP = -9;//Http Action Not Allowed	HTTP方法被禁止	请用大写的POST或GET，如果有图片等信息传入则一定要用POST才可以
	public static final Integer ERROR_CODE_PLAT_SERVICE_UNAVAILABLE = -10;//	Service Currently Unavailable	服务不可用	多数是由未知异常引起的，仔细检查传入的参数是否符合文档描述
	public static final Integer ERROR_CODE_PLAT_MISSING_METHOD = -21;//	missing method缺少方法名参数	传入的参数加入method字段
	public static final Integer ERROR_CODE_PLAT_INVALID_METHOD = -22;//	Invalid Method 不存在的方法名	传入的method字段必需是你所调用的API的名称，并且该API是确实存在的
	public static final Integer ERROR_CODE_PLAT_INVALID_FORMAT = -23;//	Invalid Format	;//无效数据格式	传入的format必需为json或xml中的一种
	public static final Integer ERROR_CODE_PLAT_MISSING_SIGNATURE = -24		;//缺少签名参数	传入的参数中必需包含sign字段
	public static final Integer ERROR_CODE_PLAT_INVALID_SIGNATURE = -25		;// Invalid Signature 无效签名	签名必需根据正确的算法算出来的。算法请见：http://open.taobao.com/doc/detail.htm?id=111#s6
	public static final Integer ERROR_CODE_PLAT_MISSING_ACCESS_TOKEN = -26		;//缺少SessionKey参数	传入的参数中必需包含session字段
	public static final Integer ERROR_CODE_PLAT_INVALID_ACCESS_TOKEN = -27	 ;//无效的SessionKey参数	传入的session必需是用户绑定session拿到的，如果报session不合法可能是用户没有绑定session或session过期造成的，用户需要重新绑定一下然后传入新的sessionKey
	public static final Integer ERROR_CODE_PLAT_MISSING_APP_ID = -28		;//缺少AppId参数	传入的参数必需包含app_id字段
	public static final Integer ERROR_CODE_PLAT_INVALID_APP_ID = -29		;//无效的AppId参数	应用所处的环境跟选择的环境不一致，例如：应用处于沙箱测试环境，却选择在正式环境进行测试。
	public static final Integer ERROR_CODE_PLAT_MISSING_TIMESTAMP =-30		;//缺少时间戳参数	传入的参数中必需包含timestamp参数
	public static final Integer ERROR_CODE_PLAT_INVALID_TIMESTAMP = -31		;//非法的时间戳参数	时间戳，格式为yyyy-mm-dd hh:mm:ss，例如：2008-01-25 20:23:30。淘宝API服务端允许客户端请求时间误差为10分钟
	public static final Integer ERROR_CODE_PLAT_MISSING_VERSION = -32		;//缺少版本参数	传入的参数中必需包含v字段
	public static final Integer ERROR_CODE_PLAT_INVALID_VERSION = -33		;//非法的版本参数	用户传入的版本号格式错误，必需为数字格式
	public static final Integer ERROR_CODE_PLAT_UNSUPPORTED_VERSION = -34		;//不支持的版本号	用户传入的版本号没有被提供
	public static final Integer ERROR_CODE_PLAT_PARAMETER_ERROR = -43;//参数错误	一般是用户传入参数非法引起的，请仔细检查入参格式、范围是否一一对应
	public static final Integer ERROR_CODE_PLAT_INVALID_ENCODING = -47		;//编码错误	一般是用户做http请求的时候没有用UTF-8编码请求造成的
	public static final Integer ERROR_CODE_PLAT_INVALID_DATA_FORMAT = -48;//	Invalid Data Format	;//无效的数据格式
	public static final Integer ERROR_CODE_PLAT_MISSING_APP_KEY = -49;//缺少AppKey
	public static final Integer ERROR_CODE_PLAT_INVALID_APP_KEY = -50;//无效AppKey
	public static final Integer ERROR_CODE_PLAT_MISSING_GRANT_TYPE = -51;//缺少GrantType
	public static final Integer ERROR_CODE_PLAT_INVALID_GRANT_TYPE = -52;//无效GrantType
	public static final Integer ERROR_CODE_PLAT_MISSING_REFRESH_TOKEN = -53;//缺少refresh_token
	public static final Integer ERROR_CODE_PLAT_INVALID_REFRESH_TOKEN = -54;//无效refresh_token

	/*---------------- 人事错误码 ---------------------*/
	/** 人员编号为空 */
	public static final Integer ERROR_CODE_PERS_PIN_NULL = -201;
	/** 人员编号格式错误 */
	public static final Integer ERROR_CODE_PERS_PIN_INVALID = -202;
	/** 人员编号长度超出 */
	public static final Integer ERROR_CODE_PERS_PIN_TOO_LONG = -203;
	/** 人员姓名长度超出 */
	public static final Integer ERROR_CODE_PERS_NAME_TOO_LONG = -204;
	/** 人员姓名格式错误 */
	public static final Integer ERROR_CODE_PERS_NAME_INVALID = -205;
	/** 人员性别格式错误 */
	public static final Integer ERROR_CODE_PERS_GENDER_INVALID = -206;
	/** 人员状态格式错误 */
	public static final Integer ERROR_CODE_PERS_STATUS_INVALID = -207;
	/** 人员生日格式错误 */
	public static final Integer ERROR_CODE_PERS_BIRTHDAY_INVALID = -208;
	/** 手机号码格式错误 */
	public static final Integer ERROR_CODE_PERS_PHONE_INVALID =-209;
	/** 证件类型格式错误 */
	public static final Integer ERROR_CODE_PERS_CERT_TYPE_INVALID = -210;
	/** 证件号码格式错误 */
	public static final Integer ERROR_CODE_PERS_CERT_NUMBER_INVALID = -211;
	/** 证件号码长度错误 */
	public static final Integer ERROR_CODE_PERS_CERT_NUMBER_TOO_LONG = -212;
	/** 卡号长度错误 */
	public static final Integer ERROR_CODE_PERS_CARD_NUMBER_TOO_LONG = -213;
	/** 民族错误 */
	public static final Integer ERROR_CODE_PERS_NATION_INVALID = -214;
	/** 入职时间错误 */
	public static final Integer ERROR_CODE_PERS_HIRE_DATE_INVALID = -215;
	/** 邮箱错误 */
	public static final Integer ERROR_CODE_PERS_EMAIL_INVALID = -216;
	/** 数据来源长度错误 */
	public static final Integer ERROR_CODE_PERS_ISFROM_TOO_LONG = -217;
	/** 人员姓氏长度超出 */
	public static final Integer ERROR_CODE_PERS_LAST_NAME_TOO_LONG = -218;
	/** 人员姓氏格式错误 */
	public static final Integer ERROR_CODE_PERS_LAST_NAME_INVALID = -219;
	/** 证件号码为空 */
	public static final Integer ERROR_CODE_PERS_CERT_NUMBER_NULL = -220;
	/** 证件类型为空 */
	public static final Integer ERROR_CODE_PERS_CERT_TYPE_NULL = -221;
	/** 证件类型不存在 */
	public static final Integer ERROR_CODE_PERS_CERT_TYPE_NOT_EXIST = -222;
	/** 身份证号码格式错误 */
	public static final Integer ERROR_CODE_PERS_IDENTITY_CARD_INVALID = -223;
	/** 人员ID为空 */
	public static final Integer ERROR_CODE_PERS_ID_NULL = -224;
	/** 人员新增失败 */
	public static final Integer ERROR_CODE_PERS_ADD_ERROR = -225;
	/** 人员不存在 */
	public static final Integer ERROR_CODE_PERS_PERSON_NOT_EXIST = -226;
	/** 用户名为空 */
	public static final Integer ERROR_CODE_PERS_LOGIN_USERNAME_NULL = -227;
	/** 密码为空 */
	public static final Integer ERROR_CODE_PERS_LOGIN_PASSWORD_NULL = -228;
	/** 用户不存在 */
	public static final Integer ERROR_CODE_PERS_LOGIN_USER_NOT_EXIST = -229;
	/** 用户名或密码错误 */
	public static final Integer ERROR_CODE_PERS_LOGIN_USER_PASSWORD_ERROR = -230;
	/** 用户禁用 */
	public static final Integer ERROR_CODE_PERS_LOGIN_USER_DISABLED = -231;
	/** 手机号码为空 */
	public static final Integer ERROR_CODE_PERS_REGIST_PHONE_NULL = -232;
	/** 手机号码重复 */
	public static final Integer ERROR_CODE_PERS_REGIST_PHONE_REPEAT = -233;
	/** pin重复 */
	public static final Integer ERROR_CODE_PERS_REGIST_PIN_REPEAT = -234;
	/** 验证码错误 */
	public static final Integer ERROR_CODE_PERS_RESET_VERIFICATIONCODE_ERROR = -235;
	/** 人员状态为空 */
	public static final Integer ERROR_CODE_PERS_PERSON_STATUS_ERROR = -236;
	/** 邮箱为空 */
	public static final Integer ERROR_CODE_PERS_EMAIL_NULL = -237;
	/** 无效图片 */
	public static final Integer ERROR_CODE_PERS_IMAGE_INVALID = -238;
	/** 图片太大 */
	public static final Integer ERROR_CODE_PERS_IMAGE_OVER_SIZE = -239;
	/** 手机号码为空 */
	public static final Integer ERROR_CODE_PERS_PHONE_NULL =-240;
	/** 人员删除失败 */
	public static final Integer ERROR_CODE_PERS_DELETE_ERROR = -241;
	/** 邮箱重复 */
	public static final Integer ERROR_CODE_PERS_EMAIL_REPEAT = -242;


	/**考勤错误代码值  -401 到-500*/
	/**设备启用状态非空**/
	public static final Integer ERROR_CODE_ATT_STATUS_NULL = -401;
	/**在线状态非空**/
	public static final Integer ERROR_CODE_ATT_ONLINESTATUS_NULL = -402;
	/**在线状态格式校验**/
	public static final Integer ERROR_CODE_ATT_ONLINESTATUS_INVALID = -403;
	/**找不到对应部门**/
	public static final Integer ERROR_CODE_ATT_INVALID_NODEPARTMENT= -404;
	/**考勤区域为空**/
	public static final Integer ERROR_CODE_ATT_INVALID_NOAREA = -405;
	/**设备为空**/
	public static final Integer ERROR_CODE_ATT_DEVICE_NULL = -406;
	/**设备号为空**/
	public static final Integer ERROR_CODE_ATT_DEVSN_NULL = -407;
	/**设备号长度超出**/
	public static final Integer ERROR_CODE_ATT_DEVSN_TOO_LONG  = -408;
	/**设备号格式错误**/
	public static final Integer ERROR_CODE_ATT_DEVSN_INVALID  = -409;
	/**设备名称为空***/
	public static final Integer ERROR_CODE_ATT_DEVNAME_NULL  = -410;
	/**设备名称长度超出**/
	public static final Integer ERROR_CODE_ATT_DEVNAME_TOO_LONG  = -411;
	/**ip地址为空**/
	public static final Integer ERROR_CODE_ATT_IP_NULL = -412;
	/**ip地址长度超出**/
	public static final Integer ERROR_CODE_ATT_IP_TOO_LONG  = -413;
	/**ip地址格式错误**/
	public static final Integer ERROR_CODE_ATT_IP_INVALID  = -414;
	/**设备型号为空**/
	public static final Integer ERROR_CODE_ATT_DEVMODEL_NULL = -415;
	/**设备型号长度超出**/
	public static final Integer ERROR_CODE_ATT_DEVMODEL_TOO_LONG  = -416;
	/**设备型号格式校验**/
	public static final Integer ERROR_CODE_ATT_DEVMODEL_INVALID  = -417;
	/**考勤日期为空**/
	public static final Integer ERROR_CODE_ATT_DATE_TIME_NULL  = -418;
	/**考勤日期格式错误**/
	public static final Integer ERROR_CODE_ATT_DATE_TIME_INVALID  = -419;
	/** 假种编号为空 */
	public static final Integer ERROR_CODE_LEAVE_NO_NULL = -420;
	/**假种编号格式错误 */
	public static final Integer ERROR_CODE_LEAVE_NO_INVALID = -421;
	/** 假种编号长度超出 */
	public static final Integer ERROR_CODE_LEAVE_NO_TOO_LONG = -422;
	/** 假种名称为空 */
	public static final Integer ERROR_CODE_LEAVE_NAME_NULL = -423;
	/**假种名称格式错误 */
	public static final Integer ERROR_CODE_LEAVE_NAME_INVALID = -424;
	/** 假种名称长度超出 */
	public static final Integer ERROR_CODE_LEAVE_NAME_TOO_LONG = -425;
	/**考勤设备新增失败**/
	public static final Integer ERROR_CODE_ADD_ATT_DEVICE_ERROR  = -426;
	/**考勤记录新增失败**/
	public static final Integer ERROR_CODE_ADD_ATT_TRANSCATION_ERROR  = -427;
	/**考勤补签新增失败**/
	public static final Integer ERROR_CODE_ADD_ATT_SIGN_ERROR  = -428;
	/**考勤补签删除失败 */
	public static final Integer ERROR_CODE_DELETE_ATT_SIGN_ERROR = -429;
	/**考勤请假新增失败**/
	public static final Integer ERROR_CODE_ADD_ATT_LEAVE_ERROR  = -430;
	/**考勤请假删除失败 */
	public static final Integer ERROR_CODE_DELETE_ATT_LEAVE_ERROR = -431;
	/** 考勤排班类型为空 */
	public static final Integer ERROR_CODE_ATT_SCH_NULL = -432;
    /** 考勤排班类型格式错误 */
    public static final Integer ERROR_CODE_ATT_SCH_INVALID = -433;
    /** 考勤班次名称为空 */
    public static final Integer ERROR_CODE_ATT_SHIFT_NAME_NULL = -434;
    /** 考勤班次名称长度超出 */
    public static final Integer ERROR_CODE_ATT_SHIFT_NAME_TOO_LONG = -435;
    /** 考勤班次名称格式错误 */
    public static final Integer ERROR_CODE_ATT_SHIFT_NAME_INVALID= -436;
    /**考勤排班新增失败**/
    public static final Integer ERROR_CODE_ADD_ATT_SCH_ERROR  = -437;
    /**考勤报表新增失败**/
    public static final Integer ERROR_CODE_ADD_ATT_RECORD_ERROR  = -438;
	/**签到时间为空**/
	public static final Integer ERROR_CODE_ATT_SIGN_IN_TIME_NULL = -439;
	/**签到地点为空**/
	public static final Integer ERROR_CODE_ATT_SIGN_IN_PLACE_NULL = -440;
	/** 考勤班次编号为空 */
	public static final Integer ERROR_CODE_ATT_SHIFT_NO_NULL = -441;
	/** 考勤班次编号长度超出 */
	public static final Integer ERROR_CODE_ATT_SHIFT_NO_TOO_LONG = -442;
	/** 考勤班次编号格式错误 */
	public static final Integer ERROR_CODE_ATT_SHIFT_NO_INVALID= -443;
	/** 考勤排班异常状态为空 */
	public static final Integer ERROR_CODE_ATT_SCH_EXCEPTION_STATE_NULL = -444;
	/** 考勤排班异常状态长度超出 */
	public static final Integer ERROR_CODE_ATT_SCH_EXCEPTION_STATE_TOO_LONG = -445;
	/** 考勤排班异常状态格式错误 */
	public static final Integer ERROR_CODE_ATT_SCH_EXCEPTION_STATE_INVALID= -446;
	/** 考勤排班时间段为空 */
	public static final Integer ERROR_CODE_ATT_SCH_TIMESLOT_NULL = -447;
	/** 考勤排班时间段格式错误 */
	public static final Integer ERROR_CODE_ATT_SCH_TIMESLOT_INVALID= -448;
	/** 考勤排班异常状态为空 */
	public static final Integer ERROR_CODE_ATT_SCH_ISMUST_SIGN_NULL = -449;
	/** 考勤排班异常状态格式错误 */
	public static final Integer ERROR_CODE_ATT_SCH_ISMUST_SIGN_INVALID= -450;
	/** 考勤报表星期为空 */
	public static final Integer ERROR_CODE_ATT_RECORD_WEEK_NULL = -451;
	/** 考勤报表星期格式错误 */
	public static final Integer ERROR_CODE_ATT_RECORD_WEEK_INVALID= -452;
	/** 考勤班次时间数据为空 */
	public static final Integer ERROR_CODE_ATT_SHIFT_TIME_DATA_NULL = -453;
	/** 考勤班次时间数据格式错误 */
	public static final Integer ERROR_CODE_ATT_SHIFT_TIME_DATA_INVALID= -454;
	/** 考勤有效打卡次数数据为空 */
	public static final Integer ERROR_CODE_ATT_CARD_VALID_DATA_NULL = -455;
	/** 考勤有效打卡次数数据格式错误 */
	public static final Integer ERROR_CODE_ATT_CARD_VALID_DATA_INVALID= -456;
	/** 考勤补签申请状态数据格式错误 */
	public static final Integer ERROR_CODE_ATT_SIGN_APPLY_STATUS_INVALID= -457;
	/**考勤排班删除失败 */
	public static final Integer ERROR_CODE_DELETE_ATT_SCH_ERROR = -458;
	/**考勤时间段与是否签到格式不匹配 */
	public static final Integer ERROR_CODE_ATT_TIMESLOT_ISMUST_SIGN_MATCH_ERROR = -459;
	/**考勤异常排班类型数据为空 */
	public static final Integer ERROR_CODE_ATT_EXCEPTION_SCH_TYPE_NULL_ERROR = -460;
	/**图表标题为空 */
	public static final Integer ERROR_CODE_STATICAL_CHART_NULL_ERROR = -461;
	/**图表数据为空 */
	public static final Integer ERROR_CODE_STATICAL_CHART_DATA_NULL_ERROR = -462;
	/**图表类型为空 */
	public static final Integer ERROR_CODE_STATICAL_CHART_TYPE_NULL_ERROR = -463;


	/*----------------- 系统管理错误（部门、区域） -----------------------*/
	/** 部门编号为空 */
	public static final Integer ERROR_CODE_DEPT_CODE_NULL = -1001;
	/** 部门编号格式错误 */
	public static final Integer ERROR_CODE_DEPT_CODE_INVALID = -1002;
	/** 部门编号格式错误 */
	public static final Integer ERROR_CODE_DEPT_CODE_TOO_LONG = -1003;
	/** 部门名称格式错误 */
	public static final Integer ERROR_CODE_DEPT_NAME_INVALID = -1004;
	/** 部门名称长度超出 */
	public static final Integer ERROR_CODE_DEPT_NAME_TOO_LONG = -1005;
	/** 部门删除失败 */
	public static final Integer ERROR_CODE_DELETE_DEPT_ERROR = -1006;
	/** 初始化部门不能删除 */
	public static final Integer ERROR_CODE_DELETE_INIT_DEPT = -1007;
	/** 部门含有系统用户 */
	public static final Integer ERROR_CODE_DELETE_DEPT_HAS_USER = -1008;
	/** 含有子部门 */
	public static final Integer ERROR_CODE_DELETE_DEPT_HAS_CHILD = -1009;
	/** 部门下有人员 */
	public static final Integer ERROR_CODE_DELETE_DEPT_HAS_PERSON = -1010;
	/** 部门新增失败 */
	public static final Integer ERROR_CODE_ADD_DEPT_ERROR = -1011;

	/*------区域 start----*/
	/** 区域编号为空 */
	public static final Integer ERROR_CODE_AREA_CODE_NULL = -1100;
	/** 区域名称为空 */
	public static final Integer ERROR_CODE_AREA_NAME_NULL = -1101;
	/** 区域为空 */
	public static final Integer ERROR_CODE_AREA_NULL = -1102;
	/** 区域名称长度超出 */
	public static final Integer ERROR_CODE_AREA_NAME_TOO_LONG = -1103;
	/** 区域编号长度超出 */
	public static final Integer ERROR_CODE_AREA_CODE_TOO_LONG = -1104;
	/** 区域编号格式错误 */
	public static final Integer ERROR_CODE_AREA_CODE_INVALID = -1105;
	/** 区域名称格式错误 */
	public static final Integer ERROR_CODE_AREA_NAME_INVALID = -1106;
	/** 区域删除失败 */
	public static final Integer ERROR_CODE_DELETE_AREA_ERROR = -1107;
	/** 区域新增失败 */
	public static final Integer ERROR_CODE_ADD_AREA_ERROR = -1011;
	/** 企业地址长度超出 */
	public static final Integer ERROR_CODE_COMANY_ADDRESS_TOO_LONG = -1120;
	/** 企业为空 */
	public static final Integer ERROR_CODE_COMANY_NULL = -1121;

	/*------区域 end---*/


	/*---------- 系统公共 ---------*/
	/**上传数量超出限制错误-其他 */
	public static final Integer ERROR_QUANTITY_EXCEEDS_LIMIT_OTHER = -1196;
	/**上传数量超出限制错误-人事 */
	public static final Integer ERROR_QUANTITY_EXCEEDS_LIMIT_PERS = -1197;
	/** 日期参数错误 */
	public static final Integer ERROR_CODE_DATE_NULL = -1198;
	/** 分页参数为空 */
	public static final Integer ERROR_CODE_PAGE_NULL = -1199;



	/**系统错误 message **/
	public static final String ERROR_MESSAGE_SYSTEM_ERROR_MESSAGE = "System Error";
	
	/**系统错误子类错误码 Sign Error 签名失败**/
	public static final String SUB_ERROR_CODE_SYSTEM_SIGN_ERROR = "sys.sign-error";
	
	/**系统错误子类错误码 VO Class Not Exist Error 不存在对象 **/
	public static final String SUB_ERROR_CODE_SYSTEM_VOCLASS_NOTEXIST_ERROR = "sys.vo-class-not-exist-error";
	/**系统错误子类错误码Method Invalid 不存在对象 **/
	public static final String SUB_ERROR_CODE_SYSTEM_METHOD_INVALID = "sys.method-invalid";
	
	/**
	 * 系统级别错误子类错误码
	 */
	public final static Map<Integer ,String> ERROR_MESSAGE_MAP = new HashMap<Integer ,String>();
	static {
		ERROR_MESSAGE_MAP.put(ERROR_CODE_ARGUMENTS_MISSING, "client-error:Missing required arguments");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_ARGUMENTS_INVALID, "client-error:Invalid arguments");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_SYSTEM_ERROR, "System Error");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_UPLOAD_FAIL, "Upload Fail");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_APPCALL_LIMITED, "App Call Limited");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_NOT_ALLOWED_HTTP, "Http Action Not Allowed");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_SERVICE_UNAVAILABLE, "Service Currently Unavailable");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_MISSING_METHOD, "Missing Method");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_METHOD, "Invalid Method");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_FORMAT, "	Invalid Format");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_MISSING_SIGNATURE, "Missing Signature");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_SIGNATURE, "Invalid Signature");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_MISSING_ACCESS_TOKEN, "Missing Access Token");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_ACCESS_TOKEN, "Invalid Access Token");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_MISSING_APP_ID, "Missing App Id");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_APP_ID, "Invalid App Id");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_MISSING_TIMESTAMP, "Missing Timestamp");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_TIMESTAMP, "Invalid Timestamp");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_MISSING_VERSION, "Missing Version");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_VERSION, "Invalid Version");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_UNSUPPORTED_VERSION, "Unsupported Version");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_PARAMETER_ERROR, "Parameter Error");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_ACCESS_TOKEN, "Invalid access token");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_ENCODING, "Invalid encoding");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_DATA_FORMAT, "Invalid Data Format");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_MISSING_APP_KEY, "Missing App Key");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_APP_KEY, "Invalid App Key");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_MISSING_GRANT_TYPE, "Missing Grant Type");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_GRANT_TYPE, "Invalid Grant Type");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_MISSING_REFRESH_TOKEN, "Missing Refresh Token");
		ERROR_MESSAGE_MAP.put(ERROR_CODE_PLAT_INVALID_REFRESH_TOKEN, "Invalid Refresh Token");
	}
	
	
	/**
	 * 系统级别错误子类错误码
	 */
	public final static Map<String ,String> SUB_ERROR_MESSAGE_MAP = new HashMap<String ,String>();
	static {
		SUB_ERROR_MESSAGE_MAP.put(SUB_ERROR_CODE_SYSTEM_SIGN_ERROR, "Sign Error");
		SUB_ERROR_MESSAGE_MAP.put(SUB_ERROR_CODE_SYSTEM_VOCLASS_NOTEXIST_ERROR, "VO Class Not Exist Error");
		SUB_ERROR_MESSAGE_MAP.put(SUB_ERROR_CODE_SYSTEM_METHOD_INVALID, "Method Invalid");
	}

	public final static Map<Integer, String> BUSINESS_ERROR_MESSAGE_MAP = new HashMap<>();
	static {
		//人员异常
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_PIN_NULL, "Person Pin Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_PIN_INVALID, "Person Pin Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_PIN_TOO_LONG, "Person Pin Too Long");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_NAME_TOO_LONG, "Person Name Too Long");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_NAME_INVALID, "Person Name Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_LAST_NAME_TOO_LONG, "Person LastName Too Long");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_LAST_NAME_INVALID, "Person LastName Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_GENDER_INVALID, "Person Gender Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_STATUS_INVALID, "Person Status Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_BIRTHDAY_INVALID, "Person BirthDay Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_PHONE_INVALID, "Person Phone Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_CERT_TYPE_INVALID, "Person Cert Type Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_CERT_NUMBER_INVALID, "Person Cert Number Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_CERT_NUMBER_TOO_LONG, "Person Cert Number Too Long");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_CARD_NUMBER_TOO_LONG, "Person Card Number Too Long");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_NATION_INVALID, "Person Nation Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_HIRE_DATE_INVALID, "Person HireDate Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_EMAIL_INVALID, "Person Email Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_ISFROM_TOO_LONG, "Person IsFrom Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_CERT_NUMBER_NULL, "Person Cert Number Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_CERT_TYPE_NULL, "Person Cert Type Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_CERT_TYPE_NOT_EXIST, "Person Cert Type Not Exist");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_IDENTITY_CARD_INVALID, "Person Identity Card Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_ID_NULL, "Person ID Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_ADD_ERROR, "Person Add Error");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_PERSON_NOT_EXIST, "Person Not Exist");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_LOGIN_USERNAME_NULL, "Person Login Username Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_LOGIN_PASSWORD_NULL, "Person Login Password Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_LOGIN_USER_NOT_EXIST, "Person Login User Not Exist");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_LOGIN_USER_PASSWORD_ERROR, "Person Login Username Or Password Error");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_LOGIN_USER_DISABLED, "Person Login User Disabled");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_REGIST_PHONE_NULL, "Person Regist Phone Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_REGIST_PHONE_REPEAT, "Person Regist Phone Repeat");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_REGIST_PIN_REPEAT, "Person Regist Pin Repeat");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_RESET_VERIFICATIONCODE_ERROR, "Verification Code Error");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_PERSON_STATUS_ERROR, "Person Status Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_EMAIL_NULL, "Email Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_IMAGE_INVALID, "Person Photo Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_IMAGE_OVER_SIZE, "Person Photo Over Size");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_PHONE_NULL, "Person Phone Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_DELETE_ERROR, "Person Delete Fail");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PERS_EMAIL_REPEAT, "Person Email Repeat");

		//部门异常
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DEPT_CODE_NULL, "Department Code Null");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DEPT_CODE_INVALID, "Department Code Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DEPT_CODE_TOO_LONG, "Department Code Too Long");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DEPT_NAME_INVALID, "Department Name Invalid");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DEPT_NAME_TOO_LONG, "Department Name Too Long");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DELETE_DEPT_ERROR, "Delete Department Error");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DELETE_INIT_DEPT, "Delete Init Department Error");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DELETE_DEPT_HAS_USER, "Department Has User");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DELETE_DEPT_HAS_CHILD, "Department Has Child");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DELETE_DEPT_HAS_PERSON, "Department Has Person");

        //系统公共错误
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_QUANTITY_EXCEEDS_LIMIT_OTHER, "Exceeds Limit 200 Slip");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_QUANTITY_EXCEEDS_LIMIT_PERS, "Exceeds Limit 50 Slip");
		BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DATE_NULL, "Date Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_PAGE_NULL, "Page is Null");


        //考勤
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_STATUS_NULL,"Att Device Status Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_ONLINESTATUS_NULL,"Att Device Onlinestatus Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_ONLINESTATUS_INVALID,"Att Device Onlinestatus Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_INVALID_NODEPARTMENT,"Auth  Department Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_INVALID_NOAREA,"Att Area Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DEVICE_NULL,"Att Device Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DEVSN_NULL,"Att Device Sn Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DEVSN_TOO_LONG,"Att Device Sn Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DEVSN_INVALID,"Att Device Sn  Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DEVNAME_NULL,"Att Device Name Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DEVNAME_TOO_LONG,"Att Device Name Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_IP_NULL,"Att Device Ip Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_IP_TOO_LONG,"Att Device Ip Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_IP_INVALID,"Att Device Ip Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DEVMODEL_NULL,"Att Device Model Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DEVMODEL_TOO_LONG,"Att Device Model Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DEVMODEL_INVALID,"Att Device Model Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DATE_TIME_INVALID,"Att  Date Time Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_DATE_TIME_NULL,"Att Date Time Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_LEAVE_NO_NULL,"Att Leave No Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_LEAVE_NO_INVALID,"Att Leave No Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_LEAVE_NO_TOO_LONG,"Att Leave No Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_LEAVE_NAME_NULL,"Att Leave Name Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_LEAVE_NAME_INVALID,"Att Leave Name  Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_LEAVE_NAME_TOO_LONG,"Att Leave Name Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ADD_ATT_DEVICE_ERROR,"Att Device Add Fail");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ADD_ATT_TRANSCATION_ERROR,"Att Transcation Add Fail");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ADD_ATT_SIGN_ERROR,"Att Sign Add Fail");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DELETE_ATT_SIGN_ERROR,"Att Sign Delete Fail");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SIGN_IN_TIME_NULL,"Att Sign In Time Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SIGN_IN_PLACE_NULL,"Att Sign In Place Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ADD_ATT_LEAVE_ERROR,"Att Leave Add Fail");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DELETE_ATT_LEAVE_ERROR,"Att Leave Delete Fail");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SCH_NULL,"Att Sch Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SCH_INVALID,"Att Sch Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SHIFT_NAME_NULL,"Att Shift Name Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SHIFT_NAME_TOO_LONG,"Att Shift Name Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SHIFT_NAME_INVALID,"Att Shift Name Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ADD_ATT_SCH_ERROR,"Att Sch Add Fail");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ADD_ATT_RECORD_ERROR,"Att Record Add Fail");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SHIFT_NO_NULL,"Att Shift No  Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SHIFT_NO_TOO_LONG,"Att Shift No Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SHIFT_NO_INVALID,"Att  Shift No Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SCH_EXCEPTION_STATE_NULL,"Att  Sch Exception State Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SCH_EXCEPTION_STATE_TOO_LONG,"Att  Sch Exception State Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SCH_EXCEPTION_STATE_INVALID,"Att  Sch Exception State Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SCH_TIMESLOT_NULL,"Att  Sch TimeSlot Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SCH_TIMESLOT_INVALID,"Att  Sch TimeSlot Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SCH_ISMUST_SIGN_NULL,"Att  Sch Is Must Sign Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SCH_ISMUST_SIGN_INVALID,"Att  Sch Is Must Sign Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_RECORD_WEEK_NULL,"Att  Record Week Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_RECORD_WEEK_INVALID,"Att  Record Week Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SHIFT_TIME_DATA_NULL,"Att  Shift Time Data Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SHIFT_TIME_DATA_INVALID,"Att  Shift Time Data Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_CARD_VALID_DATA_NULL,"Att  Card Valid Data Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_CARD_VALID_DATA_INVALID,"Att  Card Valid Data Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_SIGN_APPLY_STATUS_INVALID,"Att  Sign Apply Status Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_DELETE_ATT_SCH_ERROR,"Att  Sch Apply Delete Fail");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_TIMESLOT_ISMUST_SIGN_MATCH_ERROR,"Att  Sch Timeslot And Ismustsign Match Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_ATT_EXCEPTION_SCH_TYPE_NULL_ERROR,"Att  Record ExceptionSchType Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_STATICAL_CHART_NULL_ERROR,"Statical Chart Title Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_STATICAL_CHART_DATA_NULL_ERROR,"Statical Chart Data Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_STATICAL_CHART_TYPE_NULL_ERROR,"Statical Chart Type Null");



        /*--系统管理*/
        //系统区域
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_AREA_CODE_NULL,"Area Code Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_AREA_NAME_NULL,"Area Name Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_AREA_NULL,"Area Is Empty");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_AREA_NAME_TOO_LONG,"Area Name Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_AREA_CODE_TOO_LONG,"Area Code Too Long");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_AREA_CODE_INVALID,"Area Code Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_AREA_NAME_INVALID,"Area Name Invalid");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_COMANY_NULL,"Auth Company Null");
        BUSINESS_ERROR_MESSAGE_MAP.put(ERROR_CODE_COMANY_ADDRESS_TOO_LONG,"Auth Comany Address Too Long");


    }
}
