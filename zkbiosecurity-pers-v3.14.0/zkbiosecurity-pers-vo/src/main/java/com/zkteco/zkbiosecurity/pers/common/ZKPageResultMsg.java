package com.zkteco.zkbiosecurity.pers.common;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @Date: 2019/1/10 15:34
 */
@Getter
@Setter
@Accessors(chain = true)
public class ZKPageResultMsg extends ZKResultMsg {

    private Integer page;

    private Integer pageSize;

    private Integer total;

    public ZKPageResultMsg() {

    }

    public ZKPageResultMsg(Pager pager, Integer pagerNo, Integer pageSize) {
        super(pager.getData());
        this.total = pager.getTotal();
        this.page = pagerNo;
        this.total = pageSize;
    }

    public ZKPageResultMsg(String ret, String msg) {
        super(ret, msg);
    }

    public static ZKPageResultMsg successMsg() {
        return new ZKPageResultMsg("ok", "common_op_succeed");
    }

    public static ZKPageResultMsg failMsg() {
        return new ZKPageResultMsg("fail", "common_op_failed");
    }
}
