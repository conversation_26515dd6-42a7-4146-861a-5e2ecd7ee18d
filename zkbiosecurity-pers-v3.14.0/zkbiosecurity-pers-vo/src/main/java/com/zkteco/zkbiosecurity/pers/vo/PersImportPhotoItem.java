package com.zkteco.zkbiosecurity.pers.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 导入照片VO
 *
 * <AUTHOR>
 * @date 2023/3/17 17:36
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersImportPhotoItem {
    private static final long serialVersionUID = 1L;
    /** 人员编号 */
    private String pin;
    /** 头像照片路径 */
    private String photoPath;
    /** 抠图照片路径 */
    private String cropPhotoPath;
    /** 是否导入人员照片 */
    private Boolean importPhoto;
    /** 是否导入抠图 */
    private Boolean importCropFace;
    /** 导入文件路径 */
    private String filePath;
    /** 是否生成人脸模板 */
    private Boolean importTemplateFace;
}
