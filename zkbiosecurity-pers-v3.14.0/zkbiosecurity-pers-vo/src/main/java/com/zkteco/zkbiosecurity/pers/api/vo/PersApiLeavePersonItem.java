package com.zkteco.zkbiosecurity.pers.api.vo;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/11/11 16:03
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class PersApiLeavePersonItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 人员编号
     */
    @ApiModelProperty(allowableValues = "1234567")
    private String pin;

    /**
     * 离职日期
     */
    @ApiModelProperty(value = "离职日期", example = " 2019-06-10")
    private Date leaveDate;

    /**
     * 离职类型
     */
    @ApiModelProperty(allowableValues = "1")
    private String leaveType;
}
