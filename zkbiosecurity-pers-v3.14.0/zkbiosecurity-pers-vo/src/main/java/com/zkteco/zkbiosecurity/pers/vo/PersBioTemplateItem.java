/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(
    after = "PERS_BIOTEMPLATE t LEFT JOIN PERS_PERSON p ON t.PERSON_ID=p.ID LEFT join AUTH_DEPARTMENT d on p.auth_dept_id = d.id")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersBioTemplateItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "t.PERSON_ID", equalTag = "=")
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "p.PIN", equalTag = "=")
    @GridColumn(label = "pers_person_pin")
    private String personPin;

    /**
     * 人员姓名
     */
    @Column(name = "p.name")
    @GridColumn(label = "pers_person_name")
    private String personName;

    /**
     * 姓氏
     */
    @Column(name = "p.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", showExpression = "#language!='zh_CN'")
    private String lastName;

    /**
     * 是否有效
     */
    @Column(name = "t.VALID_TYPE")
    @GridColumn(label = "pers_person_templateValidType", format = "0=pers_card_disabled,1=pers_card_effect")
    private Short validType;

    /**
     * 生物特征类型 0：通用的 1：指纹 2：面部 3：声纹 4：虹膜 5：视网膜 6：掌纹 7：指静脉 8：掌静脉 9：可见光人脸
     */
    @Column(name = "t.BIO_TYPE")
    @GridColumn(label = "pers_person_templateBioTypeNumber")
    private Short bioType;

    @Column(name = "t.BIO_TYPE")
    @GridColumn(label = "pers_person_templateBioType",
        format = "0=pers_person_universal,1=pers_person_regFinger,2=pers_person_infraredFace,3=pers_person_voice,4=pers_person_iris,"
            + "5=pers_person_retina,6=pers_person_palmPrints,7=pers_person_regVein,8=pers_person_metacarpalVein,"
            + "9=pers_person_visibleFace,10=pers_person_visiblePalm")
    private Short bioTypeVal;

    /**
     * 生物特征版本
     */
    @Column(name = "t.VERSION")
    @GridColumn(label = "pers_person_templateVersion")
    private String version;

    /**
     * 生物特征模板内容
     */
    @Column(name = "t.TEMPLATE")
    @GridColumn(label = "pers_person_template")
    private String template;

    /**
     * 生物特征模板编号
     */
    @Column(name = "t.TEMPLATE_NO")
    @GridColumn(label = "pers_person_templateNo")
    private Short templateNo;

    /**
     * 生物特征模板对应索引
     */
    @Column(name = "t.TEMPLATE_NO_INDEX")
    @GridColumn(label = "pers_person_templateNoIndex")
    private Short templateNoIndex;

    /**
     * 是否胁迫
     */
    @Column(name = "t.DURESS")
    @GridColumn(label = "pers_person_bioDuress", format = "true=common_yes,false=common_no")
    private Boolean duress;

    @Condition(value = "t.PERSON_ID", equalTag = "in")
    private String personIdIn;

    @Condition(value = "p.AUTH_DEPT_ID", equalTag = "in")
    private String inDeptId;

    /**
     * 辅助前端页面查询过滤用的，编号
     */
    @Condition(value = "p.PIN", equalTag = "like")
    private String pin;
    /**
     * 辅助前端页面查询过滤用的，姓名
     */
    @Condition(value = "(LOWER (p.NAME) LIKE LOWER (''%{0}%'') OR LOWER (p.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;
    /**
     * 辅助前端页面查询过滤用的，性别
     */
    @Condition(value = "p.GENDER", equalTag = "=")
    private String gender;
    /**
     * 辅助前端页面查询过滤用的，email
     */
    @Condition(value = "p.EMAIL", equalTag = "like", encryptConverter = true)
    private String email;

    @Column(name = "p.MOBILE_PHONE", encryptConverter = true)
    private String mobilePhone;
    /**
     * 辅助前端页面查询过滤用的，状态
     */
    @Condition(value = "p.EXCEPTION_FLAG", equalTag = "=")
    private Short exceptionFlag;
    /**
     * 辅助前端页面查询过滤用的，用于卡号模糊查询
     */
    @Condition(value = "t.PERSON_ID IN (SELECT c.PERSON_ID FROM PERS_CARD c WHERE c.CARD_NO LIKE ''%{0}%'')",
        encryptConverter = true)
    private String likeCardNo;
    /**
     * 辅助前端页面查询过滤用的，部门名称
     */
    @Column(name = "d.NAME")
    private String deptName;
    /**
     * 辅助前端页面查询过滤用的，用于左侧部门树查询
     */
    @Column(name = "p.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    private String verifyMode;

    @Condition(value = "p.POSITION_ID IN (SELECT c.ID FROM PERS_POSITION c WHERE c.NAME LIKE ''%{0}%'')")
    private String positionName;

    /**
     * 根据用户ID查询关联部门及子部门ID
     */
    @Condition(
        value = "p.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    /**
     * 默认构造方法
     */
    public PersBioTemplateItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersBioTemplateItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersBioTemplateItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param validType
     * @param bioType
     * @param version
     * @param template
     * @param templateNo
     * @param templateNoIndex
     */
    public PersBioTemplateItem(String id, Short validType, Short bioType, String version, String template,
        Short templateNo, Short templateNoIndex) {
        super();
        this.id = id;
        this.validType = validType;
        this.bioType = bioType;
        this.version = version;
        this.template = template;
        this.templateNo = templateNo;
        this.templateNoIndex = templateNoIndex;
    }
}