/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(after = "PERS_PERSON_LINK t ")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonLinkItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /** LinkType 接口，各个模块要使用时，自己定义枚举实现接口即可，方便定制扩展用 */
    public interface LinkTypeInterFace {
        String name();
    }

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "t.PERSON_ID", equalTag = "=")
    private String personId;

    /**
     * 类型  LinkTypeEnum 枚举值
     */
    @Column(name = "t.TYPE",equalTag = "=")
    private String type;

    /**
     * 业务表的ID
     */
    @Column(name = "t.LINK_ID", equalTag = "=")
    private String linkId;

    @Condition(value = "t.PERSON_ID", equalTag = "in")
    private String personIdIn;

    /**
     * 默认构造方法
     */
    public PersPersonLinkItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersPersonLinkItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersPersonLinkItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param type
     * @param linkId
     */
    public PersPersonLinkItem(String id, LinkTypeInterFace type, String linkId) {
        super();
        this.id = id;
        this.type = type.name();
        this.linkId = linkId;
    }
}