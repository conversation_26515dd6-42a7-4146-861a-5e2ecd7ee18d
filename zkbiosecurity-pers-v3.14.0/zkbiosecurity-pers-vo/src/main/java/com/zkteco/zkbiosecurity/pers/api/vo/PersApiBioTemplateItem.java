package com.zkteco.zkbiosecurity.pers.api.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
@ApiModel
public class PersApiBioTemplateItem implements Serializable {

    @ApiModelProperty(hidden = true)
    private String id;
    @ApiModelProperty(allowableValues = "1")
    private String pin;
    @ApiModelProperty(allowableValues = "1 or 3")
    private String validType;
    @ApiModelProperty(allowableValues = "1", hidden = true)
    private Short bioType;
    @ApiModelProperty(allowableValues = "10")
    private String version;
    @ApiModelProperty(allowableValues = "fsfsfs")
    private String template;
    @ApiModelProperty(allowableValues = "3")
    private String templateNo;
    @ApiModelProperty(allowableValues = "1", hidden = true)
    private Short templateNoIndex;

    public PersApiBioTemplateItem() {
        super();
    }

    public PersApiBioTemplateItem(String id, String pin, String validType, Short bioType, String version,
        String template, String templateNo, Short templateNoIndex) {
        super();
        this.id = id;
        this.pin = pin;
        this.validType = validType;
        this.bioType = bioType;
        this.version = version;
        this.template = template;
        this.templateNo = templateNo;
        this.templateNoIndex = templateNoIndex;
    }

    public static PersApiBioTemplateItem createBioTemplate(PersBioTemplateItem persBioTemplate) {
        PersApiBioTemplateItem apiBioTemplate = null;
        if (persBioTemplate != null) {
            apiBioTemplate = new PersApiBioTemplateItem(persBioTemplate.getId(), persBioTemplate.getPersonPin(),
                persBioTemplate.getValidType() + "", persBioTemplate.getBioType(), persBioTemplate.getVersion(),
                persBioTemplate.getTemplate(), String.valueOf(persBioTemplate.getTemplateNo()),
                persBioTemplate.getTemplateNoIndex());
        }
        return apiBioTemplate;
    }
}
