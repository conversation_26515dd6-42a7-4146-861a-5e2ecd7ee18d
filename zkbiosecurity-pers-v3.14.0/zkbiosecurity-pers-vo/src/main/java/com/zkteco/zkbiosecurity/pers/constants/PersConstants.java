package com.zkteco.zkbiosecurity.pers.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 20130313 用于人事业务常用的常量
 */
public class PersConstants {
    public static final Short PERSON_ENABLE = 0;
    public static final Short PERSON_DISABLED = 1;
    /**
     * 0、自动匹配模式（内置隐藏）
     */
    public static final short WG_AUTO_MODE = 0;
    /**
     * 默认模式
     */
    public static final short WG_DEFAULT_MODE = 1;
    /**
     * 自定义模式
     */
    public static final short WG_CUSTOM_MODE = 2;

    public static final char ODD_PARITY = 'o';
    public static final char EVEN_PARITY = 'e';
    public static final char CID = 'c';
    public static final char SITE_CODE = 's';
    public static final char FACILITY_CODE = 'f';
    public static final char MANUFACTORY_CODE = 'm';
    public static final char ODD_AND_EVEN_PARITY = 'b';
    public static final char NOT_PARITY = 'x';
    public static final char ODD_OR_EVEN_PARITY = 'p';

    /**
     * 生物模板编号 NO 默认值
     */
    public static final short PERS_BIOTEMPLATE_DEF_NO = 0;

    /**
     * 生物模板编号索引 NO_INDEX 默认值
     */
    public static final short PERS_BIOTEMPLATE_DEF_NO_INDEX = 0;

    /**
     * 生物模板编号索引 NO_INDEX
     */
    public static final short PERS_BIOTEMPLATE_DEF_DATA_NO_INDEX = 3;

    // 卡状态
    /**
     * 有效卡
     */
    public static final short CARD_VALID = 1;
    /**
     * 无效卡（挂失卡）
     */
    public static final short CARD_LOSS = 3;

    /** 操作类型 */
    /**
     * 挂失
     */
    public static final short CARD_INVALID = 6;
    /**
     * 解挂
     */
    public static final short CARD_SOLUTLINKED = 7;
    /**
     * 退卡
     */
    public static final short INEXISTENCE = 12;

    /**
     * 主卡
     */
    public static final short MAIN_CARD = 0;
    /**
     * 副卡
     */
    public static final short DEPUTY_CARD = 1;

    // 卡显示格式
    /**
     * 十进制格式显示
     */
    public static final String CARD_DECIMAL = "0";
    /**
     * 十六进制格式显示
     */
    public static final String CARD_HEXADECIMAL = "1";

    // 人员状态
    /**
     * 正常
     */
    public static final short PERSON_NORMAL = 0;

    /**
     * 普通用户类型
     */
    public static final short PERSON_NORMAL_TYPE = 0;

    /** ---------------------人员异常状态------------------------------------- **/
    /**
     * 不是异常
     */
    public static final short PERSON_ISNOTEXCEPTION = 0;
    /**
     * 卡号重复异常
     */
    public static final short PERSON_ISEXCEPTION = 1;
    /**
     * 用户密码异常
     */
    public static final short PERSON_PWD_EXCEPTION = 2;
    /**
     * pin号异常
     */
    public static final short PERSON_PIN_EXCEPTION = 3;

    /**
     * 默认人员编号长度
     */
    public static final short DEFAULT_PIN_LENGTH = 9;

    // 人员类型
    /**
     * 内部人员 员工
     */
    public static final short PERSON_TYPE_EMPLOYEE = 0;;

    /**
     * 人员自助密码 123456 的加密字符串
     */
    public static final String PERSON_SELFPWD = "e10adc3949ba59abbe56e057f20f883e";

    /*----------------------------制卡相关-----------------------------------------*/
    /**
     * 默认模板code
     */
    public static String TEMPLATE_VERTICAL_DEFAULT = "default";
    /**
     * 横向默认模板code
     */
    public static String TEMPLATE_HORIZONTAL_DEFAULT = "defaultH";
    /**
     * 横向模板
     */
    public static String TEMPLATE_VERTICAL = "vertical";
    /**
     * 竖向模板
     */
    public static String TEMPLATE_HORIZONTAL = "horizontal";

    /*-----------------------------导入多卡分割符----------------------------------------*/
    /**
     * 导入卡分割符 多卡间间隔& 主卡&副卡1&副卡2、多车牌间间隔& 车牌1&车牌2&车牌3 使用&不用,原因纯数字加,在excel文件上会变成货币形式，导入数据会有问题
     */
    public static String IMPORT_CARD_SPLIT = "&";

    // 默认排序值
    public static int DEF_SORT_NO = 999999;

    /*-----------------------------人员数据来源------------------------------------*/
    /**
     * 线下添加人员
     */
    public static final String PERS_USRE_SYSTEM = "PERS_USER_MANUALLY_ADDED";
    /**
     * H5页面添加人员
     */
    public static final String PERS_USRE_H5WEB = "h5_web";
    /**
     * 用户版APP
     */
    public static final String PERS_CLOUD_APP = "PERS_CLOUD_APP";
    /**
     * 离职人员恢复
     */
    public static final String PERS_USER_LEAVE_ADDED = "PERS_USER_LEAVE_ADDED";

    /*-----------------------------API----------------------------------------*/
    // 成功
    public static final int OP_SUCCESS = 0;
    // 程序错误
    public static final int API_PROGRAM_ERROR = -1;
    // 部门编号或名称不能为空
    public static final int DEPT_CODEORNAME_ISNULL = -10;
    // 部门编号已存在
    public static final int DEPT_CODE_EXIST = -11;
    // 部门名称已经存在
    public static final int DEPT_NAME_EXIST = -12;
    // 部门编号不存在
    public static final int DEPT_CODE_NOTEXIST = -13;
    // 不能设置自己为上级部门
    public static final int DEPT_SELFNOT_PARENT = -14;
    // 部门不允许删除（含有子部门或者部门底下有人员存在或者初始化部门不能删除）
    public static final int DEPT_DELETE_FAILED = -15;
    // 部门编号不能包含特殊字符
    public static final int DEPT_CODE_NOSPECIALCHAR = -16;
    // 部门名称不能包含特殊字符
    public static final int DEPT_NAME_NOSPECIALCHAR = -17;
    // 排序不能为空
    public static final int DEPT_SORTNO_ISNULL = -17;

    // pin号不能为空
    public static final int PERSONPIN_ISNULL = -20;
    // 人员pin已经存在
    public static final int PERSON_PIN_EXIST = -21;
    // 该人员不存在
    public static final int PERSONID_NOTEXIST = -22;
    // 卡号已经被使用
    public static final int CART_HASUSE = -23;
    // 该权限组不存在
    public static final int LEVEL_NOTEXIST = -24;
    // 该权限组下没有人员
    public static final int LEVEL_NOTHAS_PERSONID = -25;
    // 密码已经被使用
    public static final int PERSON_PWD_HASUSE = -26;
    // 人员照片无效
    public static final int PERSON_PHOTO_INVALID = -27;
    // 人员照片0~50M
    public static final int PERSON_PHOTO_FILESIZE = -28;
    // 人员编号异常
    public static final int PERSON_PIN_ERROR = -29;
    // 人员主卡为空
    public static final int PERSON_MASTERCARD_ISNULL = -30;
    // 人员不允许删除（被其他模块占用）
    public static final int PERSON_DELETE_FAILED = -35;
    // 人员编号不能以0开头
    public static final int PERSON_PIN_STARTWITHZERO = -36;
    // 人员编号超长！
    public static final int PERSON_PIN_TOOLONG = -37;
    // 人员编号首位不能为8、9。
    public static final int PERSON_PIN_FIRSTtVALID = -38;
    // 人员编号格式错误！
    public static final int PERSON_PIN_FORMATERROR = -39;
    // 姓名超长！
    public static final int PERSON_NAME_TOOLONG = -40;
    // 不能包含逗号
    public static final int PERSON_NAME_NOCOMMA = -41;
    // 人员密码仅支持数字！
    public static final int PERSON_PWD_ONLYNUMBER = -42;
    // 人员密码超长！
    public static final int PERSON_PWD_TOOLONG = -43;
    // 电话号码长度超过20位！
    public static final int PERSON_PHONE_TOOLONG = -44;
    // 邮箱超长！
    public static final int PERSON_EMAIL_TOOLONG = -45;
    // 邮箱格式错误！
    public static final int PERSON_EMAIL_FORMATERROR = -46;
    // 卡号格式错误
    public static final int PERSON_CARDNO_FORMATERROR = -47;
    // 当前系统不支持多卡
    public static final int PERSON_CARDS_NOTSUPPORT = -48;
    // 已达到许可上限，要继续当前操作，请联系销售人员！
    public static final int PERSON_lICENSE_MAXCOUNT = -49;
    // 卡号重复
    public static final int PERSON_CARDNO_DURESS = -50;
    // 密码重复
    public static final int PERSON_PWD_DURESS = -51;
    // 新设置的人员编号的长度不能小于已存在的人员编号的长度
    public static final int PERSON_PARAM_MAXPINLENGTH = -52;
    // 系统中存在设备支持的人员编号最大长度为9，请输入小于9的整数！
    public static final int PERSON_PARAM_PINBEYONDDEVLENGTH = -53;
    // 人员编号支持自动递增，不能修改人员编号模式
    public static final int PERSON_PARAM_CHANGEPINLETTERS = -54;
    // 人员编号支持包含字母，不能修改人员编号模式
    public static final int PERSON_PARAM_CHANGEPININCREMENT = -55;
    // 已有人员编号包含字母，不能更改人员编号模式
    public static final int PERSON_PARAM_NOTCHANGEPIN = -56;
    // 系统中存在设备不支持包含字母的人员编号，不能更改人员编号模式。
    public static final int PERSON_PARAM_NOSUPPORTPINLETTER = -57;
    // 当前系统已存在卡号，不能更改卡格式显示
    public static final int PERSON_PARAM_HEXCHANGEWARN = -58;
    // 系统未启动二维码功能
    public static final int PERSON_QRCODE_DISABLE = -59;
    // 该人员没有分配卡号
    public static final int PERSON_QRCODE_NOCARD = -60;
    // pin号不能包含特殊字符
    public static final int PERSON_PIN_NOSPECIALCHAR = -61;
    // 姓名不能包含特殊字符
    public static final int PERSON_NAME_NOSPECIALCHAR = -62;
    // 抠图失败
    public static final int PERSON_CROPFACE_FAILE = -63;
    // 离职类型不能为空
    public static final int PERSON_LEAVETYPE_ISNULL = -64;
    // 离职类型不存在
    public static final int PERSON_LEAVETYPE_NOTEXIST = -65;
    // 离职日期不能为空
    public static final int PERSON_LEAVEDATE_ISNULL = -66;
    // pageNo或者pageSize不能设置小于等于0
    public static final int PERSON_WRONG_PAGE = -67;
    // pageSize 大于1000
    public static final int PERSON_PAGE_OVERSIZE = -68;
    // 车牌个数超过限制
    public static final int PERSON_CARPLATE_OVERSIZE = -69;
    // 二代身份证格式错误
    public static final int PERSON_IDNUMBER_ERROR = -70;
    // 邮箱重复
    public static final int PERSON_EMAIL_REPEAT = -71;
    // 离职人员不存在
    public static final int PERSON_LEAVEPIN_ISNULL = -72;
    // 证件号码已存在
    public static final int PERSON_CERTNUM_EXIST = -73;
    // 车牌重复
    public static final int PERSON_CARPLATE_EXIST = -74;
    // 车牌格式
    public static final int PERSON_CARPLATE_FORMAT = -75;
    // pageNo或者pageSize不能为空
    public static final int PERSON_PAGE_NULL = -80;
    // 手机号重复
    public static final int PERSON_PHONE_REPEAT = -81;
    // 手机号格式不正确
    public static final int PERSON_PHONE_FORMATERROR = -82;
    // 卡号不能为空
    public static final int PERSON_CART_EMPTY = -83;
    // 卡类型错误
    public static final int PERSON_CART_TYPEERROR = -84;
    // 请输入由英文字母、数字组成的字符
    public static final int PERSON_PIN_LETTERANDINT = -85;
    // 请输入由数字组成的字符
    public static final int PERSON_PIN_INT = -86;

    /** 指纹接口API常量 */
    // 该人员指纹重复
    public static final int PERSONID_FINGER_EXIST = -31;
    // 该人员validtype输入不是1或3
    public static final int PERSONID_VALIDTYPE_INCORRECT = -32;
    // 手指个数大于9或小于0
    public static final int TEMPLATENO_NO_RANGE_0_9 = -33;
    // templateNo不存在
    public static final int PERSON_TEMPLATENO_NOTEXIST = -34;
    // template不能为空
    public static final int PERSON_TEMPLATE_ISNULL = -76;
    // version只能输入数字
    public static final int PERSON_VERSION_ISNOTNUMBER = -77;
    // 该人员已被禁用，无法操作
    public static final int PERSON_DISABLED_NOPOP = -78;
    // 操作数据超过限制
    public static final int PERSON_OPERATE_OVERSIZE = -79;

    // 非法时间
    public static final int API_DATE_ERROR = -100;
    // 开始时间不能大于结束时间
    public static final int API_DATE_STARTTIME_LARGE = -101;
    // 手机号码不能修改
    public static final int PERSON_PHONE_NOTMODIFIED = -102;

    /**
     * 身份类别:普通
     */
    public static final String CATEGORY_COMMON = "0";

    /**
     * 临时人员状态：待审核
     */
    public static final short TEMPPERSON_STATUS_TOAUDIT = 0;
    /**
     * 临时人员状态：已审核
     */
    public static final short TEMPPERSON_STATUS_APPROVED = 1;

    /** ---------------------人员异常状态------------------------------------- **/
    /**
     * 禁用
     */
    public static final short PERSON_STATUS_EXCEPTION = 4;

    public static final String PERSON_FROM_APP = "PERS_CLOUD_APP";

    /**
     * 管理员类型
     */
    public static final short PERSON_ADMIN_TYPE = 1;

    /**
     * 手机号码登录
     */
    public static final String PERS_LOGIN_MOBILE = "mobile";

    /**
     * 邮箱登录
     */
    public static final String PERS_LOGIN_EMAIL = "email";

    /**
     * 手机验证码登录
     */
    public static final String PERS_LOGIN_PHONECODE = "phoneCode";

    /**
     * 待办消息处理状态 0 未处理 1 已处理
     */
    public static final String PERS_TODOLIST_NOTDEAL = "0";
    public static final String PERS_TODOLIST_HASDEAL = "1";

    /**
     * 待办消息类型 1 访客预约通知
     */
    public static final String PERS_GTASKS_RESERVATION = "1";

    /**
     * 人员审批成功
     */
    public static final String PERS_AUDITED_SUCCESS = "1";
    /**
     * 人员审批拒绝
     */
    public static final String PERS_AUDITED_FAILED = "2";

    /**
     * 状态 0 正常
     */
    public static final Short PERSON_STATUS_NORMAL = 0;

    /**
     * 状态 1 离职
     */
    public static final Short PERSON_STATUS_LEAVE = 1;

    public static final String PERS_USERWXEXT = "persCloud.userWxExt:";

    /**
     * 人员操作类型：删除
     */
    public static final String PERSON_OPERATE_TYPE_DEL = "del";

    /**
     * 人员操作类型：离职
     */
    public static final String PERSON_OPERATE_TYPE_LEAVE = "leave";

    /**
     * 验证方式：卡
     */
    public static final String VERIFY_MODE_CARD = "1000";
    /**
     * 验证方式：比对照片
     */
    public static final String VERIFY_MODE_CROPFACE = "1001";
    /**
     * 验证方式：不包含卡
     */
    public static final String VERIFY_MODE_NOCARD = "N1000";
    /**
     * 验证方式：不包含指纹
     */
    public static final String VERIFY_MODE_NOREGFINGER = "N1";
    /**
     * 验证方式：不包含掌静脉
     */
    public static final String VERIFY_MODE_NOMETACARPALVEIN = "N8";
    /**
     * 验证方式：不包含指静脉
     */
    public static final String VERIFY_MODE_NOREGVEIN = "N7";
    /**
     * 验证方式：不包含近红外人脸模板
     */
    public static final String VERIFY_MODE_NOINFRAREDFACETEMPLATE = "N2";
    /**
     * 验证方式：不包含可见光人脸模板
     */
    public static final String VERIFY_MODE_NOVISIBLEFACETEMPLATE = "N9";
    /**
     * 验证方式：不包含比对照片
     */
    public static final String VERIFY_MODE_NOCROPFACE = "N1001";

    /**
     * 验证方式：不包含可见光手掌
     */
    public static final String VERIFY_MODE_NOPALMBIOTYPE10 = "N10";

    /** 验证方式：不包含虹膜 */
    public static final String VERIFY_MODE_NOIRISBIOTYPE = "N4";

    // 日期格式错误
    public static final String PERSON_DATE_ERROR = "-1";

    // 日期格式错误
    public static final String PERSON_DATE_SETNULL = "-2";

    public static final String PERSON_BASE64_PREFIX = "data:image/jpg;base64,";

    /**
     * 名单库类型 3允许名单库,2禁止名单库
     */
    public static final String TYPE_ALLOW_LIST = "3";
    public static final String TYPE_BAND_LIST = "2";
    /**
     * 名单库类型 5访客允许名单库 6访客禁止名单库
     */
    public static final String TYPE_VISALLOW_LIST = "5";
    public static final String TYPE_VISBAND_LIST = "6";

    public static final List<String> TYPE_VIS_PERSONNELLIST = new ArrayList<>();
    static {
        TYPE_VIS_PERSONNELLIST.add("5");
        TYPE_VIS_PERSONNELLIST.add("6");
    }

    /**
     * 人员属性：人员，离职人员，访客
     */
    public static final String PERS_PERSON = "PERS_PERSON";
    public static final String PERS_LEAVEPERSON = "PERS_LEAVEPERSON";
    public static final String VIS_VISITOR = "VIS_VISITOR";

    /** 人员编号长度参数名 */
    public static final String PERS_PARAM_PINLEN = "pers.pinLen";
    /** 是否支持字母参数名 */
    public static final String PERS_PARAM_SUPPORT_LETTER = "pers.pinSupportLetter";
    /** 是否支持递增参数名 */
    public static final String PERS_PARAM_SUPPORT_INCREMENT = "pers.pinSupportIncrement";
    /** 人员编号长度参数名(云端) */
    public static final String CLOUD_PERS_PARAM_PINLEN = "pinMaxLength";
    /** 是否支持字母参数名(云端) */
    public static final String CLOUD_PARAM_SUPPORT_LETTER = "pinSupportLetter";
    /** 是否支持递增(云端) */
    public static final String CLOUD_PARAM_SUPPORT_INCREMENT = "pinSupportIncrement";

    /** 人员自助登记 0 禁用 */
    public static final String PERSON_SELFREGISTRATION_DISABLED = "0";
    /** 禁用人员自助登记 返回404 */
    public static final String RESPONSE_NOT_FOUND = "404";
    /** 二代身份证 */
    public static final String CERT_SECOND_ID = "2";

    // 手掌 bioType=10
    public static final Short PALM_BIO_TYPE_10 = Short.valueOf((short)10);
    // 虹膜
    public static final Short IRIS_BIO_TYPE = Short.valueOf((short)4);
    public static final String IRIS_BIO_VERSION = "13";

    // 掌静脉
    public static final Short PV_BIO_TYPE = Short.valueOf((short)6);

    // 启禁用安全等级
    public static final int ENABLED_CREDENTIAL_LEVEL = 1;

    public static final String ACMS = "ACMS";

    public static final String API_PERS_PWDISREPETITION = "PER20001";
    public static final String API_PERS_OLDPWDISERROR = "PER20002";
    // 信息待审核
    public static final String API_PERS_WAITREVIEWED = "PER20003";
    // API批量操作数据限制值
    public static final int API_OPERATE_DATALIMIT = 100;

    /** 登录获取token值 */
    public static final String TEMPLATE_SERVER_GETTOKEN = "/extract/getToken";
    /** 服务器状态检测 */
    public static final String TEMPLATE_SERVER_TESTSTATUS = "/extract/testServiceStatus";
    /** 提取人脸模板 */
    public static final String TEMPLATE_SERVER_GETFACETEMPLATE = "/extract/getFaceTemplate";

    public static Map<String, String> TEMPLATE_SERVER_ERROR_MAP = new HashMap<>();
    static {
        TEMPLATE_SERVER_ERROR_MAP.put("0", "common_op_succeed");
        TEMPLATE_SERVER_ERROR_MAP.put("-1", "pers_person_faceTemplateError1");
        TEMPLATE_SERVER_ERROR_MAP.put("-2", "pers_person_faceTemplateError2");
        TEMPLATE_SERVER_ERROR_MAP.put("-3", "pers_person_faceTemplateError3");
        TEMPLATE_SERVER_ERROR_MAP.put("-4", "pers_person_faceTemplateError4");
        TEMPLATE_SERVER_ERROR_MAP.put("-5", "pers_person_faceTemplateError5");
        TEMPLATE_SERVER_ERROR_MAP.put("-6", "pers_person_faceTemplateError6");
    }
}
