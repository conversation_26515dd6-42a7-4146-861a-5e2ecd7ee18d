package com.zkteco.zkbiosecurity.pers.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

/**
 * 人员信息缓存对象
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/9/14 13:32
 * @since 1.0.0
 */
@Getter
@Setter
public class PersPersonCacheItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String pin;

    private String name;

    private String lastName;

    /**
     * 身份证号
     */
    private String idCard;

    private String deptId;

    private String deptName;

    private String deptCode;

    private String positionId;

    private String positionName;

    /**
     * 入职时间
     */
    private Date hireDate;

    /**
     * 离职时间
     */
    private Date leaveDate;

    /** 照片路径 */
    private String photoPath;

    /** 邮箱 */
    private String email;

    /** 是否发送邮件 */
    private Boolean isSendMail;

    /** 是否发送短信 */
    private Boolean sendSMS;

    /** 联系电话 */
    private String mobilePhone;

    /** 性别 */
    private String gender;

    /** 是否发送whatsapp */
    private Boolean sendWhatsapp;

    /** whatsapp账号 */
    private String whatsappMobileNo;

    /** 启禁用凭证 */
    private Boolean enabledCredential;

    /** 是否推送app消息 */
    private Boolean sendApp;

    /**
     * 各个模块的附加属性集合
     * <P>
     * 格式：< "att":<"isAttendance":true, "groupId":"123">, "acc":<"xx":"xx", "xx":"xx">>
     * </p>
     */
    private Map<String, Map<String, Object>> extParams = new HashMap();

    public PersPersonCacheItem() {}

    public PersPersonCacheItem(String id, String pin, String name, String lastName, String idCard, String deptId,
        Date hireDate) {
        this.id = id;
        this.pin = pin;
        this.name = name;
        this.lastName = lastName;
        this.idCard = idCard;
        this.deptId = deptId;
        this.hireDate = hireDate;
    }

    public PersPersonCacheItem(String id, String pin, String name, String lastName, String idCard, String deptId,
        Date hireDate, Date leaveDate) {
        this.id = id;
        this.pin = pin;
        this.name = name;
        this.lastName = lastName;
        this.idCard = idCard;
        this.deptId = deptId;
        this.hireDate = hireDate;
        this.leaveDate = leaveDate;
    }
}
