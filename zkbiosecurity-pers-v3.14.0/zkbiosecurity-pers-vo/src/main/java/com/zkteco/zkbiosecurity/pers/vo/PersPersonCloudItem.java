package com.zkteco.zkbiosecurity.pers.vo;

import java.io.Serializable;
import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@From(after = "PERS_PERSON t" + " LEFT JOIN PERS_CERTIFICATE pc ON t.ID = pc.PERSON_ID"
    + " LEFT JOIN PERS_ATTRIBUTE_EXT pat ON pat.PERSON_ID = t.ID" + " LEFT JOIN PERS_POSITION p ON t.POSITION_ID=p.ID "
    + "LEFT join AUTH_DEPARTMENT d on t.auth_dept_id = d.id " + "LEFT JOIN AUTH_DEPARTMENT pd ON d.PARENT_ID = pd.ID")
@OrderBy(after = "t.UPDATE_TIME DESC,t.PIN DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersPersonCloudItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    private String id;

    /**
     * 部门ID
     */
    @Column(name = "t.AUTH_DEPT_ID", equalTag = "=")
    private String deptId;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN")
    private String pin;

    /**
     * 名字
     */
    @Column(name = "t.NAME")
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    private String lastName;

    /**
     * 部门编码
     */
    @Column(name = "d.CODE")
    private String deptCode;

    /**
     * 部门名称
     */
    @Column(name = "d.NAME")
    private String deptName;

    /**
     * 性别
     */
    @Column(name = "t.GENDER")
    private String gender;

    /**
     * 照片路径
     */
    @Column(name = "t.PHOTO_PATH")
    private String photoPath;

    /**
     * 状态
     */
    @Column(name = "t.STATUS")
    private Short status;

    /**
     * 人员类型
     */
    @Column(name = "t.PERSON_TYPE")
    private Short personType;

    /**
     * 出生日期
     */
    @Column(name = "t.BIRTHDAY")
    private Date birthday;

    /**
     * 联系电话
     */
    @Column(name = "t.MOBILE_PHONE", encryptConverter = true)
    private String mobilePhone;

    /**
     * 自助密码
     */
    @Column(name = "t.SELF_PWD", encryptConverter = true)
    private String selfPwd;

    /**
     * 设备验证密码
     */
    @Column(name = "t.PERSON_PWD", equalTag = "=", encryptConverter = true)
    private String personPwd;

    /**
     * 证件类型
     */
    @Column(name = "pc.CERT_TYPE")
    private String certType;

    /**
     * 证件号码
     */
    @Column(name = "pc.CERT_NUMBER", equalTag = "=", encryptConverter = true)
    private String certNumber;

    /**
     * 家庭地址
     */
    @Column(name = "pat.ATTR_VALUE12")
    private String homeAddress;

    /**
     * 异常标志位
     */
    @Column(name = "t.EXCEPTION_FLAG")
    private Short exceptionFlag;

    /**
     * 人员来源
     */
    @Column(name = "t.IS_FROM")
    private String isFrom;

    /**
     * appId
     */
    @Column(name = "t.APP_ID", equalTag = "=")
    private String appId;

    /**
     * 卡号
     */
    private String cardNos;

    /**
     * 入职日期
     */
    @Column(name = "t.HIRE_DATE")
    private Date hireDate;

    /**
     * 邮箱
     */
    @Column(name = "t.EMAIL", encryptConverter = true)
    private String email;

    /**
     * 缩略图地址
     */
    private String thumbPhotoPath;

    /**
     * 父部门编码
     */
    @Column(name = "pd.CODE")
    private String parentDeptCode;

    /**
     * 职位code
     */
    @Column(name = "p.CODE", equalTag = "=")
    private String positionCode;

    @Condition(value = "t.UPDATE_TIME", equalTag = ">")
    private Date lastUpdateDate;

    /**
     * 人员来源
     */
    @Condition(value = "(t.IS_FROM NOT IN (%s) OR t.IS_FROM IS NULL)", formatType = "quote")
    private String isFromNotIn;

    /**
     * 抠图地址
     */
    private String cropPhotoPath;

    @Column(name = "t.ENABLED_CREDENTIAL")
    private Boolean enabledCredential;

    /**
     * 是否更新云端照片路径
     */
    private Boolean updateCloudPhoto = false;
}
