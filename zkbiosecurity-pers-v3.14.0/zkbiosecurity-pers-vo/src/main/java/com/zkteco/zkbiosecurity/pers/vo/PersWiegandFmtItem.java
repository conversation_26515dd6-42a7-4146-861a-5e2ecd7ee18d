/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @author: GenerationTools
 * @date: 2018-02-24 上午09:38
 */
@From(after = "PERS_WIEGANDFMT t ")
@OrderBy(after = "t.CREATE_TIME")
@GridConfig(operate = true, idField = "id", winHeight = 520, winWidth = 920,
    operates = {
        @GridOperate(type = "edit", permission = "pers:wiegandFmt:edit", url = "persWiegandFmt.do?edit",
            label = "common_op_edit"),
        @GridOperate(type = "del", permission = "pers:wiegandFmt:del", url = "persWiegandFmt.do?del&names=(name)",
            label = "common_op_del", showConvertor = "delShowConvertor")})
@Getter
@Setter
@Accessors(chain = true)
public class PersWiegandFmtItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /**
     * BusinessID 下发设备中使用
     */
    @Column(name = "t.BUSINESS_ID")
    private Long businessId;

    /**
     * 名称
     */
    @Column(name = "t.NAME")
    @GridColumn(columnType = "edit", label = "common_name", width = "300", editPermission = "pers:wiegandFmt:edit",
        editUrl = "persWiegandFmt.do?edit&id=(id)")
    private String name;

    /**
     * 模式（0、自动匹配模式（内置隐藏），1、默认模式， 2、自定义模式）
     */
    @Column(name = "t.WIEGAND_MODE")
    @GridColumn(label = "pers_wiegandFmt_wiegandMode", width = "*",
        format = "1=pers_wiegandFmt_wiegandModeOne,2=pers_wiegandFmt_wiegandModeTwo")
    private Short wiegandMode;

    /**
     * 总位数
     */
    @Column(name = "t.WIEGAND_COUNT")
    private Short wiegandCount;

    /**
     * 卡校验格式: c s m p 命令格式
     */
    @Column(name = "t.CARD_FMT")
    private String cardFmt;

    /**
     * 奇偶校验格式: o e b命令格式
     */
    @Column(name = "t.PARITY_FMT")
    private String parityFmt;

    /**
     * 区位码
     */
    @Column(name = "t.SITE_CODE")
    @GridColumn(label = "pers_wiegandFmt_siteCode", width = "*")
    private String siteCode;

    /**
     * 自动匹配时这个韦根位数的默认格式
     */
    @Column(name = "t.IS_DEFAULT_FMT")
    @GridColumn(label = "pers_wiegandFmt_isDefaultFmt", format = "false=common_no,true=common_yes", width = "*")
    private Boolean isDefaultFmt;

    @Column(name = "t.INIT_FLAG")
    private Boolean initFlag;

    @Condition(value = "t.WIEGAND_MODE", equalTag = ">")
    private Short autoMode;

    @Condition(value = "t.ID", equalTag = "<>")
    private String notId;

    @Condition(value = "t.ID", equalTag = "in")
    private String ids;

    /**
     * 默认构造方法
     */
    public PersWiegandFmtItem() {
        super();
    }

    /**
     * 构造方法
     */
    public PersWiegandFmtItem(Boolean equals) {
        super(equals);
    }

    /**
     * @param id
     */
    public PersWiegandFmtItem(String id) {
        super(true);
        this.id = id;
    }

    /**
     * @param id
     * @param name
     * @param wiegandCount
     * @param wiegandMode
     * @param cardFmt
     * @param parityFmt
     * @param isDefaultFmt
     * @param siteCode
     * @param initFlag
     */
    public PersWiegandFmtItem(String id, String name, Short wiegandCount, Short wiegandMode, String cardFmt,
        String parityFmt, Boolean isDefaultFmt, String siteCode, Boolean initFlag) {
        super();
        this.id = id;
        this.name = name;
        this.wiegandCount = wiegandCount;
        this.wiegandMode = wiegandMode;
        this.cardFmt = cardFmt;
        this.parityFmt = parityFmt;
        this.isDefaultFmt = isDefaultFmt;
        this.siteCode = siteCode;
        this.initFlag = initFlag;
    }

    /**
     * @param name
     * @param wiegandMode
     * @param wiegandCount
     * @param cardFmt
     * @param parityFmt
     * @param isDefaultFmt
     * @param siteCode
     * @param initFlag
     */
    public PersWiegandFmtItem(String name, Short wiegandMode, Short wiegandCount, String cardFmt, String parityFmt,
        Boolean isDefaultFmt, String siteCode, Boolean initFlag) {
        this.name = name;
        this.wiegandMode = wiegandMode;
        this.wiegandCount = wiegandCount;
        this.cardFmt = cardFmt;
        this.parityFmt = parityFmt;
        this.isDefaultFmt = isDefaultFmt;
        this.siteCode = siteCode;
        this.initFlag = initFlag;
    }

    public boolean delShowConvertor() {
        return this.initFlag == null ? true : !this.initFlag;
    }
}