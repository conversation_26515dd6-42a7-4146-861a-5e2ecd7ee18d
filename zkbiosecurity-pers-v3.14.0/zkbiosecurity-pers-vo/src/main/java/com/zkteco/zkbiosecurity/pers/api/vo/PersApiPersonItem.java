package com.zkteco.zkbiosecurity.pers.api.vo;

import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人员-VO
 *
 * <AUTHOR>
 * @Date: 2018/11/7 17:10
 */
@Getter
@Setter
@Accessors(chain = true)
@ApiModel
public class PersApiPersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @ApiModelProperty(hidden = true)
    private String id;

    /**
     * 人员编号
     */
    @ApiModelProperty(allowableValues = "1234567")
    private String pin;

    /**
     * 部门编号
     */
    @ApiModelProperty(allowableValues = "1")
    private String deptCode;

    /**
     * 部门名称
     */
    @ApiModelProperty(hidden = true)
    private String deptName;

    /**
     * 人员姓名
     */
    @ApiModelProperty(allowableValues = "max")
    private String name;

    /**
     * 海外（人员姓氏）
     */
    @ApiModelProperty(allowableValues = "lastName")
    private String lastName;

    /**
     * 人员性别
     */
    @ApiModelProperty(value = "F:Female M:Male", allowableValues = "F")
    private String gender;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "YYYY-MM-DD", allowableValues = "2016-07-15")
    private String birthday;

    /**
     * 卡号
     */
    @ApiModelProperty(value = "main card", allowableValues = "123456789")
    private String cardNo;

    /**
     * 副卡
     */
    @ApiModelProperty(allowableValues = "987643", value = "card1,card2")
    private String supplyCards;

    /**
     * 人员照片（base64）
     */
    @ApiModelProperty(allowableValues = "Base64", value = "")
    private String personPhoto;

    /**
     * 自助密码
     */
    // @ApiModelProperty(allowableValues = "123456")
    @ApiModelProperty(hidden = true)
    private String selfPwd;

    /**
     * 邮件通知
     */
    private Boolean isSendMail = false;

    /**
     * 手机号码
     */
    @ApiModelProperty(allowableValues = "15123456789")
    private String mobilePhone;

    /**
     * 设备验证密码
     */
    @ApiModelProperty(allowableValues = "123456")
    private String personPwd;

    /**
     * 车牌
     */
    @ApiModelProperty(allowableValues = "闽_A12345")
    private String carPlate;

    /**
     * 邮件
     */
    @ApiModelProperty(allowableValues = "<EMAIL>")
    private String email;

    /**
     * 社会安全号
     */
    @ApiModelProperty(allowableValues = "111111")
    private String ssn;

    /**
     * 门禁权限组id
     */
    @ApiModelProperty(allowableValues = "1", value = "L1,L2")
    private String accLevelIds;

    /**
     * 门禁权限有效时间 起
     */
    @ApiModelProperty(allowableValues = "2018-07-14 08:56:00")
    private String accStartTime;

    /**
     * 门禁权限有效时间 止
     */
    @ApiModelProperty(allowableValues = "2019-07-14 08:56:00")
    private String accEndTime;

    /**
     * 证件类型
     */
    @ApiModelProperty(allowableValues = "2")
    private String certType;

    /**
     * 证件号码
     */
    @ApiModelProperty(allowableValues = "123456")
    private String certNumber;
    /**
     * 图片路径
     */
    // @ApiModelProperty(value = "图片路径", example = "/upload/pers/user")
    @ApiModelProperty(hidden = true)
    private String photoPath;

    /**
     * 入职时间
     */
    @ApiModelProperty(value = "入职时间", example = " 2019-06-10")
    private Date hireDate;

    /**
     * 禁止名单
     */
    @ApiModelProperty(value = "禁止名单", dataType = "Boolean", example = "false")
    private Boolean isDisabled;

    /**
     * 可见光人脸（base64）
     */
    @ApiModelProperty(hidden = true)
    private String vislightPhoto;

    /**
     * 可见光人脸路径
     */
    @ApiModelProperty(hidden = true)
    private String vislightPhotoPath;

    private String leaveId;
}
