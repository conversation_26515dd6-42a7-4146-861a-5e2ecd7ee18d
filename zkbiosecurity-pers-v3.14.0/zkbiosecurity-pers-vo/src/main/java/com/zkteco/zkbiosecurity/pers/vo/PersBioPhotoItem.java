package com.zkteco.zkbiosecurity.pers.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.From;
import com.zkteco.zkbiosecurity.base.annotation.OrderBy;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@From(after = "PERS_BIOPHOTO t")
@OrderBy(after = "t.CREATE_TIME DESC")
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class PersBioPhotoItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Column(name = "t.ID", equalTag = "=")
    private String id;

    /**
     * 人员id
     */
    @Column(name = "t.PERSON_ID", equalTag = "=")
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "t.PIN", equalTag = "=")
    private String personPin;

    @Column(name = "t.PHOTO_PATH")
    private String photoPath;

    @Column(name = "t.BIO_TYPE")
    private Short bioType;

    private String photoBase64;

}
