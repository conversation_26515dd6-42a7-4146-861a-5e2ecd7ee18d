package com.zkteco.zkbiosecurity.pers.vo;

import java.io.Serializable;
import java.util.Date;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020/7/24
 */
@Setter
@Getter
@Accessors(chain = true)
@From(after = "PERS_PERSONNALLIST_PERSON t left join PERS_PERSON p on t.PERSON_ID = p.id")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = true, idField = "id")
public class PersPersonnallistPersonItem extends BaseItem implements Serializable {

    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sort = "na")
    private String id;

    /** 名单库id */
    @Column(name = "t.PERSONNALLIST_ID", equalTag = "=")
    private String personnallistId;

    /** 人员id */
    @Column(name = "t.PERSON_ID")
    private String personId;

    /** 关联表，涉及 人员表PERS_PERSON，离职人员表PERS_LEAVEPERSON，访客表VIS_VISITOR */
    @Column(name = "t.LINK_TBL")
    private String linkTbl;

    /** 人员编号 访客编号或人员编号或离职人员编号 */
    @Column(name = "t.PERSON_PIN")
    @GridColumn(label = "pers_person_pin", width = "110", encryptMode = "${pers.pin.encryptMode}",
        permission = "pers:pin:encryptProp")
    private String personPin;

    /** 人员姓名 访客姓名或人员姓名或离职人员姓名 */
    @Column(name = "t.PERSON_NAME")
    @GridColumn(label = "pers_person_name", width = "110", encryptMode = "${pers.name.encryptMode}",
        permission = "pers:name:encryptProp")
    private String personName;

    /**
     * 姓氏
     */
    @Column(name = "t.LAST_NAME")
    @GridColumn(label = "pers_person_lastName", width = "110", showExpression = "#language!='zh_CN'",
        encryptMode = "${pers.lastName.encryptMode}", permission = "pers:name:encryptProp")
    private String lastName;

    /** 人员性别 */
    @Column(name = "t.PERSON_GENDER")
    private String personGender;

    /** 人员出生日期 */
    @Column(name = "t.PERSON_BIRTHDAY")
    private Date personBirthday;

    /*** 身份证 */
    @Column(name = "t.ID_CARD")
    private String idCard;

    /*** 职位名称 */
    @Column(name = "t.POSITION_NAME")
    private String positionName;

    /*** 联系电话 */
    @Column(name = "t.MOBILE_PHONE")
    private String mobilePhone;

    /*** 邮箱 */
    @Column(name = "t.EMAIL")
    private String email;

    @Condition(value = "t.ID", equalTag = "in")
    private String inId;

    @Condition(value = "t.PERSON_ID", equalTag = "in")
    private String inPersonId;

    @Condition(value = "t.PERSON_ID", equalTag = "not in")
    private String notInPersonId;

    @Condition(value = "t.PERSONNALLIST_ID", equalTag = "in")
    private String inPersonnallistId;

    @Condition(
        value = "p.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=''{0}'')")
    private String userId;

    @Condition(value = "(LOWER (p.NAME) LIKE LOWER (''%{0}%'') OR LOWER (p.LAST_NAME) LIKE LOWER (''%{0}%''))")
    private String likeName;

}
