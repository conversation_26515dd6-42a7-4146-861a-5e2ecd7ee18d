package com.zkteco.zkbiosecurity.pers.vo;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;


/**
 * 人员属性bean
 * <AUTHOR>
 * @since 2017年6月1日 上午9:09:11
 */
public final class PersAttributeRuleItem
{
	private String name;
	private int rowIndex;
	private int colIndex;
	private String regex;
	private String split;
	
	public PersAttributeRuleItem()
	{
		super();
	}
	
	public PersAttributeRuleItem(String name)
	{
		super();
		this.name = name;
	}

	public PersAttributeRuleItem(String name, int colIndex)
	{
		super();
		this.name = name;
		this.colIndex = colIndex;
	}

	public PersAttributeRuleItem(String text, int rowIndex, int colIndex) throws Exception
	{
		Pattern compile = Pattern.compile("\\(([a-zA-Z]+(\\.[0-9a-zA-Z]+)?(,[0-9a-zA-Z]+)*?)\\)");
		Matcher matcher = compile.matcher(text);
		if (matcher.find())
		{
			String group = matcher.group(1);
			String[] split = group.split(",");
			this.name = split[0];
		}
		else
		{
			throw new ZKBusinessException(I18nUtil.i18nCode("pers_import_commentFormat"));
		}
		this.rowIndex = rowIndex;
		this.colIndex = colIndex;
	}
	
	public String getName()
	{
		return name;
	}

	public void setName(String name)
	{
		this.name = name;
	}

	public int getRowIndex()
	{
		return rowIndex;
	}

	public void setRowIndex(int rowIndex)
	{
		this.rowIndex = rowIndex;
	}

	public int getColIndex()
	{
		return colIndex;
	}

	public void setColIndex(int colIndex)
	{
		this.colIndex = colIndex;
	}

	public String getRegex()
	{
		return regex;
	}

	public void setRegex(String regex)
	{
		this.regex = regex;
	}

	public String getSplit()
	{
		return split;
	}

	public void setSplit(String split)
	{
		this.split = split;
	}
}
