package com.zkteco.zkbiosecurity.pers.init;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

@Component
@Order(value = 22)
public class PersAttributeInit implements CommandLineRunner {
    @Autowired
    private PersAttributeService persAttributeService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public void run(String... args) throws Exception {
        boolean alreadyInit = baseSysParamService.getAlreadyInitModule("PersAttributeInit");
        if (!alreadyInit) {
            baseSysParamService.setAlreadyInitModule("PersAttributeInit");
            boolean alreadyInitAttributeItem = false;
            PersAttributeItem initItem = new PersAttributeItem();
            initItem.setSqlStr("init");
            initItem.setEquals(true);
            List<PersAttributeItem> initItems = persAttributeService.getByCondition(initItem);
            if (!initItems.isEmpty()) {
                alreadyInitAttributeItem = true;
            }
            if ("zh_CN".equals(LocaleMessageSourceUtil.language) && !alreadyInitAttributeItem) {
                String tempNation =
                    "汉族;蒙古族;回族;藏族;维吾尔族;苗族;彝族;壮族;布依族;朝鲜族;满族;侗族;瑶族;白族;土家族;哈尼族;哈萨克族;傣族;黎族;傈傈族;佤族;畲族;高山族;拉祜族;水族;东乡族;纳西族;景颇族;柯尔克孜族;土族;达斡尔族;仫佬族;羌族;布朗族;撒拉族;毛南族;仡佬族;锡伯族;阿昌族;普米族;塔吉克族;怒族;乌孜别克族;俄罗斯族;鄂温克族;德昂族;保安族;裕固族;京族;塔塔尔族;独龙族;鄂伦春族;赫哲族;门巴族;珞巴族;基诺族;穿青人族";
                String tempEmployType = "正式员工;试用员工";
                String tempJobTitle =
                    "教授;副教授;讲师;助教;高级讲师;助理讲师;教员;研究员;副研究员;助理研究员;研究实习员;高级实验师;实验师;助理实验师;实验员;高级工程师;工程师;助理工程师;技术员;高级农艺师;农艺师;助理农艺师;农业技术员;高级兽医师;兽医师;助理兽医师;兽医技术员;高级畜牧师;畜牧师;助理畜牧师;畜牧技术员;高级经济师;经济师;助理经济师;经济员;高级会计师;会计师;助理会计师;会计员;高级统计师;统计师;助理统计师;统计员;编审;副编审;编辑;助理编辑;技术编辑;助理技术编辑;技术设计员;研究馆员;副研究馆员;馆员;助理馆员;管理员;主任医师;副主任医师;主治医师;医师;医士;主任药师;副主任药师;主管药师;药师;药士;主任护师;副主任护师;主管护师;护师;护士;主任技师;副主任技师;主管技师;技师;技士;博士后;博士生;硕士生";
                String tempPoliticalAffiliation = "党员;团员;群众";
                // String tempCity =
                // "北京;天津;河北省;山西省;内蒙古自治区;辽宁省;吉林省;黑龙江省;上海;江苏省;浙江省;安徽省;福建省;江西省;山东省;河南省;湖北省;湖南省;广东省;广西壮族自治区;海南省;重庆市;四川省;贵州省;云南省;西藏自治区;陕西省;甘肃省;青海省;宁夏回族自治区;新疆维吾尔自治区;台湾省;香港特别行政区;澳门特别行政区";
                String tempHireType =
                    I18nUtil.i18nCode("pers_attr_inContract") + ";" + I18nUtil.i18nCode("pers_attr_outContract");
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_street"), "text", null,
                    "init", 5, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 1));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_nation"), "select",
                    tempNation, "init", 5, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 2));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_emp_type"), "select",
                    tempEmployType, "init", 3, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 3));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_office_address"),
                    "text", null, "init", 8, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 4));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_office_phone"), "text",
                    null, "init", 8, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 5));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_home_phone"), "text",
                    null, "init", 7, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 6));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_job_title"), "select",
                    tempJobTitle, "init", 4, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 7));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_birthplace"), "text",
                    null, "init", 6, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 8));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_polit_status"),
                    "select", tempPoliticalAffiliation, "init", 4, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 9));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_country"), "text",
                    null, "init", 6, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 10));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_hire_type"), "select",
                    tempHireType, "init", 3, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 11));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_home_address"), "text",
                    null, "init", 7, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 12));
            } else if (!alreadyInitAttributeItem) {
                String tempEmployType = I18nUtil.i18nCode("pers_person_officialStaff") + ";"
                    + I18nUtil.i18nCode("pers_person_probationStaff");
                String tempHireType =
                    I18nUtil.i18nCode("pers_attr_inContract") + ";" + I18nUtil.i18nCode("pers_attr_outContract");

                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_emp_type"), "select",
                    tempEmployType, "init", 3, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 1));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_hire_type"), "select",
                    tempHireType, "init", 3, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 2));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_job_title"), "text",
                    null, "init", 4, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 3));
                // persAttributeService.initData(new PersAttributeItem("politStatus", "select",
                // tempPoliticalAffiliation, null, 2, 2,PersConstUtil.PERSON_TYPE_EMPLOYEE,4));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_street"), "text", null,
                    "init", 4, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 5));
                // persAttributeService.initData(new PersAttributeItem("nation", "select",tempNation, null, 3,
                // 2,PersConstUtil.PERSON_TYPE_EMPLOYEE,6));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_birthplace"), "text",
                    null, "init", 5, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 7));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_country"), "text",
                    null, "init", 5, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 8));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_home_phone"), "text",
                    null, "init", 6, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 9));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_home_address"), "text",
                    null, "init", 6, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 10));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_office_phone"), "text",
                    null, "init", 7, 1, PersConstants.PERSON_TYPE_EMPLOYEE, 11));
                persAttributeService.initData(new PersAttributeItem(I18nUtil.i18nCode("pers_attr_office_address"),
                    "text", null, "init", 7, 2, PersConstants.PERSON_TYPE_EMPLOYEE, 12));
            }
        }
    }
}
