package com.zkteco.zkbiosecurity.pers.init;

import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryService;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryItem;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryValueItem;

@Component
@Order(value = 21)
public class PersDictionaryInit implements CommandLineRunner {
    @Autowired
    private BaseDictionaryService baseDictionaryService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public void run(String... args) throws Exception {
        boolean alreadyInit = baseSysParamService.getAlreadyInitModule("PersDictionaryInit");
        if (!alreadyInit) {

            baseSysParamService.setAlreadyInitModule("PersDictionaryInit");
            // 初始化人脸抠图不及格错误
            initPersFaceFailType();
            // 初始化名单库
            initPersonnallist();

            // 国籍
            BaseDictionaryItem baseDictionaryItem =
                new BaseDictionaryItem("PersNationality", "pers_person_nationality", false, "pers_module");
            baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("244", "pers_nationality_angola", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("93", "pers_nationality_afghanistan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("355", "pers_nationality_albania", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("213", "pers_nationality_algeria", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("1", "pers_nationality_america", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("376", "pers_nationality_andorra", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1264", "pers_nationality_anguilla", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1268", "pers_nationality_antAndBar", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("54", "pers_nationality_argentina", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("374", "pers_nationality_armenia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("297", "pers_nationality_aruba", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("247", "pers_nationality_ascension", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("61", "pers_nationality_australia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("43", "pers_nationality_austria", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("994", "pers_nationality_azerbaijan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1242", "pers_nationality_bahamas", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("973", "pers_nationality_bahrain", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("880", "pers_nationality_bangladesh", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1246", "pers_nationality_barbados", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("375", "pers_nationality_belarus", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("32", "pers_nationality_belgium", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("501", "pers_nationality_belize", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("229", "pers_nationality_benin", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1441", "pers_nationality_bermudaIs", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("975", "pers_nationality_bhutan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("591", "pers_nationality_bolivia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(new BaseDictionaryValueItem("387",
                "pers_nationality_bosnia_herzegovina", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("267", "pers_nationality_botswana", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("55", "pers_nationality_brazil", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("673", "pers_nationality_brunei", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("359", "pers_nationality_bulgaria", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("226", "pers_nationality_burkinaFaso", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("95", "pers_nationality_burma", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("257", "pers_nationality_burundi", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("237", "pers_nationality_cameroon", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("238", "pers_nationality_capeVerde", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("124", "pers_nationality_canada", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1345", "pers_nationality_caymanIs", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("236", "pers_nationality_cenAfrRepub", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("235", "pers_nationality_chad", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("56", "pers_nationality_chile", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("86", "pers_nationality_china", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("269", "pers_nationality_comoros", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("57", "pers_nationality_colombia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("242", "pers_nationality_congo", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("243", "pers_nationality_congoD", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("682", "pers_nationality_cookIs", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("506", "pers_nationality_costaRica", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("385", "pers_nationality_croatia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("53", "pers_nationality_cuba", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("357", "pers_nationality_cyprus", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("420", "pers_nationality_czechRep", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("45", "pers_nationality_denmark", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("253", "pers_nationality_djibouti", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1890", "pers_nationality_dominicaRep", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1767", "pers_nationality_dominica", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("593", "pers_nationality_ecuador", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("20", "pers_nationality_egypt", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("291", "pers_nationality_eritrea", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("503", "pers_nationality_eISalvador", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("44", "pers_nationality_england", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("372", "pers_nationality_estonia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("251", "pers_nationality_ethiopia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("679", "pers_nationality_fiji", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("358", "pers_nationality_finland", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("33", "pers_nationality_france", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("691", "pers_nationality_micronesia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("594", "pers_nationality_freGui", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("241", "pers_nationality_gabon", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("220", "pers_nationality_gambia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("995", "pers_nationality_georgia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("240", "pers_nationalit_equatorialGuinea", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("49", "pers_nationality_germany", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("233", "pers_nationality_ghana", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("350", "pers_nationality_gibraltarm", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("30", "pers_nationality_greece", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1809", "pers_nationality_grenada", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("1671", "pers_nationality_guam", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("502", "pers_nationality_guatemala", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("224", "pers_nationality_guinea", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("245", "pers_nationalit_guineaBissau", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("592", "pers_nationality_guyana", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("509", "pers_nationality_haiti", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("852", "pers_nationalit_hongkong", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("504", "pers_nationality_honduras", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("36", "pers_nationality_hungary", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("354", "pers_nationality_iceland", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("91", "pers_nationality_india", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("62", "pers_nationality_indonesia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("98", "pers_nationality_iran", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("964", "pers_nationality_iraq", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("353", "pers_nationality_ireland", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("972", "pers_nationality_israel", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1340", "pers_nationalit_virginIslands", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("39", "pers_nationality_italy", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(new BaseDictionaryValueItem("1284",
                "pers_nationalit_britishVirginIslands", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("225", "pers_nationality_ivoryCoast", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1876", "pers_nationality_jamaica", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("81", "pers_nationality_japan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("962", "pers_nationality_jordan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("855", "pers_nationality_cambodia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("254", "pers_nationality_kenya", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("82", "pers_nationality_korea", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("965", "pers_nationality_kuwait", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("331", "pers_nationality_kyrgyzstan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("686", "pers_nationalit_kiribati", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("856", "pers_nationality_laos", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("371", "pers_nationality_latvia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("961", "pers_nationality_lebanon", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("266", "pers_nationality_lesotho", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("231", "pers_nationality_liberia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("218", "pers_nationality_libya", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("423", "pers_nationality_liechtenstein", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("370", "pers_nationality_lithuania", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("352", "pers_nationality_luxembourg", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("261", "pers_nationality_madagascar", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("265", "pers_nationality_malawi", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("60", "pers_nationality_malaysia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("960", "pers_nationality_maldives", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("223", "pers_nationality_mali", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("356", "pers_nationality_malta", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1670", "pers_nationality_marianaIs", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("596", "pers_nationality_martinique", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("230", "pers_nationality_mauritius", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("222", "pers_nationalit_mauritania", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("52", "pers_nationality_mexico", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("976", "pers_nationalit_mongolia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("692", "pers_nationalit_marshall", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("389", "pers_nationalit_macedonia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("382", "pers_nationalit_montenegro", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("373", "pers_nationality_moldova", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("377", "pers_nationality_monaco", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1664", "pers_nationality_montseIs", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("212", "pers_nationality_morocco", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("258", "pers_nationality_mozambique", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("264", "pers_nationality_namibia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("674", "pers_nationality_nauru", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("977", "pers_nationality_nepal", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("599", "pers_nationality_netAnti", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("31", "pers_nationality_netherlands", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("64", "pers_nationality_newZealand", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("505", "pers_nationality_nicaragua", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("227", "pers_nationality_niger", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("234", "pers_nationality_nigeria", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("850", "pers_nationality_norKorea", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("47", "pers_nationality_norway", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("968", "pers_nationality_oman", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("92", "pers_nationality_pakistan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("507", "pers_nationality_panama", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("675", "pers_nationality_papNewCui", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("595", "pers_nationality_paraguay", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("51", "pers_nationality_peru", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("63", "pers_nationality_philippines", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("970", "pers_nationalit_palestine", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("680", "pers_nationalit_palau", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("48", "pers_nationality_poland", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("689", "pers_nationality_frenPolyne", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("351", "pers_nationality_portugal", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1787", "pers_nationality_puerRico", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("974", "pers_nationality_qatar", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("262", "pers_nationality_reunion", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("40", "pers_nationality_romania", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("7", "pers_nationality_russia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("250", "pers_nationalit_rwanda", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1758", "pers_nationality_saiLueia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1784", "pers_nationality_saintVinc", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("684", "pers_nationality_samoa_eastern", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("685", "pers_nationality_samoa_western", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("378", "pers_nationality_sanMarino", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("239", "pers_nationality_saoAndPrinc", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("966", "pers_nationality_sauArabia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("221", "pers_nationality_senegal", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("248", "pers_nationality_seychelles", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("232", "pers_nationality_sieLeone", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("65", "pers_nationality_singapore", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1869", "pers_nationalit_saintKittsNevis", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("421", "pers_nationality_slovakia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("386", "pers_nationality_slovenia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("677", "pers_nationality_solomonIs", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("252", "pers_nationality_somali", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("27", "pers_nationality_souAfrica", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("34", "pers_nationality_spain", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("94", "pers_nationality_sriLanka", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("249", "pers_nationality_sudan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("597", "pers_nationality_suriname", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("268", "pers_nationality_swaziland", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("46", "pers_nationality_sweden", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("41", "pers_nationality_switzerland", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("963", "pers_nationality_syria", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("992", "pers_nationality_tajikstan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("255", "pers_nationality_tanzania", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("66", "pers_nationality_thailand", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("670", "pers_nationalit_timorLeste", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("886", "pers_nationalit_taiwan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("228", "pers_nationality_togo", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("676", "pers_nationality_tonga", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("1809", "pers_nationality_triAndToba", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("216", "pers_nationality_tunisia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("90", "pers_nationality_turkey", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("688", "pers_nationalit_tuvalu", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("993", "pers_nationality_turkmenistan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("380", "pers_nationality_ukraine", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("971", "pers_nationality_uniArabEmira", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("256", "pers_nationality_uganda", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("598", "pers_nationality_uruguay", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("233", "pers_nationality_uzbekistan", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("678", "pers_nationalit_vanuatu", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("58", "pers_nationality_venezuela", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("84", "pers_nationality_vietnam", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("967", "pers_nationality_yemen", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("381", "pers_nationality_serbia", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService.initData(
                new BaseDictionaryValueItem("263", "pers_nationality_zimbabwe", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("260", "pers_nationality_zambia", 0, baseDictionaryItem.getId()));

            // 离职类型
            baseDictionaryItem = new BaseDictionaryItem("PersLeaveType", "pers_dimission_type", false, "pers_module");
            baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("1", "pers_dimission_volutary", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("2", "pers_dimission_resignat", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("3", "pers_dimission_dismiss", 0, baseDictionaryItem.getId()));
            baseDictionaryValueService
                .initData(new BaseDictionaryValueItem("4", "pers_dimission_shiftJob", 0, baseDictionaryItem.getId()));

            if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
                // 职务
                baseDictionaryItem = new BaseDictionaryItem("PersJobTitle", "职务", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("11", "教授", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("12", "副教授", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("13", "讲师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("14", "助教", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("21", "高级讲师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("24", "助理讲师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("25", "教员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("61", "研究员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("62", "副研究员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("63", "助理研究员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("64", "研究实习员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("71", "高级实验师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("73", "实验师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("74", "助理实验师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("75", "实验员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("81", "高级工程师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("83", "工程师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("84", "助理工程师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("85", "技术员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("91", "高级农艺师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("93", "农艺师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("94", "助理农艺师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("95", "农业技术员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("101", "高级兽医师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("103", "兽医师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("104", "助理兽医师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("105", "兽医技术员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("111", "高级畜牧师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("113", "畜牧师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("114", "助理畜牧师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("115", "畜牧技术员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("121", "高级经济师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("123", "经济师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("124", "助理经济师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("125", "经济员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("131", "高级会计师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("133", "会计师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("134", "助理会计师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("135", "会计员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("141", "高级统计师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("143", "统计师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("144", "助理统计师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("145", "统计员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("151", "编审", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("152", "副编审", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("153", "编辑", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("154", "助理编辑", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("163", "技术编辑", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("164", "助理技术编辑", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("165", "技术设计员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("211", "研究馆员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("212", "副研究馆员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("213", "馆员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("214", "助理馆员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("215", "管理员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("231", "主任医师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("232", "副主任医师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("233", "主治医师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("234", "医师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("235", "医士", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("241", "主任药师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("242", "副主任药师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("243", "主管药师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("244", "药师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("245", "药士", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("251", "主任护师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("252", "副主任护师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("253", "主管护师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("254", "护师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("255", "护士", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("261", "主任技师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("262", "副主任技师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("263", "主管技师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("264", "技师", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("265", "技士", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("273", "博士后", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("274", "博士生", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("275", "硕士生", 0, baseDictionaryItem.getId()));

                // 学历
                baseDictionaryItem = new BaseDictionaryItem("PersEducation", "学历", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "小学", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "中学", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("3", "高中", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("4", "大学", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("5", "硕士", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("6", "博士", 0, baseDictionaryItem.getId()));

                // 城市
                baseDictionaryItem = new BaseDictionaryItem("PersCity", "城市", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("110000", "北京", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("120000", "天津", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130000", "河北省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130100", "--石家庄市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130200", "--唐山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130300", "--秦皇岛市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130400", "--邯郸市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130500", "--邢台市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130600", "--保定市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130700", "--张家口市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130800", "--承德市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("130900", "--沧州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("131000", "--廊坊市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("131100", "--衡水市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140000", "山西省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140100", "--太原市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140200", "--大同市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140300", "--阳泉市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140400", "--长治市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140500", "--晋城市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140600", "--朔州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140700", "--晋中市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140800", "--运城市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("140900", "--忻州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("141000", "--临汾市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("141100", "--吕梁市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150000", "内蒙古自治区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150100", "--呼和浩特市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150200", "--包头市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150300", "--乌海市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150400", "--赤峰市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150500", "--通辽市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150600", "--鄂尔多斯市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150700", "--呼伦贝尔市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150800", "--巴彦淖尔市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("150900", "--乌兰察布市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("152200", "--兴安盟", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("152500", "--锡林郭勒盟", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("152900", "--阿拉善盟", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210000", "辽宁省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210100", "--沈阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210200", "--大连市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210300", "--鞍山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210400", "--抚顺市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210500", "--本溪市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210600", "--丹东市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210700", "--锦州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210800", "--营口市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("210900", "--阜新市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("211000", "--辽阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("211100", "--盘锦市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("211200", "--铁岭市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("211300", "--朝阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("211400", "--葫芦岛市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("220000", "吉林省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("220100", "--长春市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("220200", "--吉林市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("220300", "--四平市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("220400", "--辽源市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("220500", "--通化市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("220600", "--白山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("220700", "--松原市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("220800", "--白城市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("222400", "--延边朝鲜族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230000", "黑龙江省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230100", "--哈尔滨市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230200", "--齐齐哈尔市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230300", "--鸡西市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230400", "--鹤岗市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230500", "--双鸭山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230600", "--大庆市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230700", "--伊春市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230800", "--佳木斯市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("230900", "--七台河市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("231000", "--牡丹江市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("231100", "--黑河市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("231200", "--绥化市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("232700", "--大兴安岭地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("310000", "上海", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320000", "江苏省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320100", "--南京市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320200", "--无锡市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320300", "--徐州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320400", "--常州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320500", "--苏州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320600", "--南通市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320700", "--连云港市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320800", "--淮安市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("320900", "--盐城市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("321000", "--扬州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("321100", "--镇江市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("321200", "--泰州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("321300", "--宿迁市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330000", "浙江省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330100", "--杭州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330200", "--宁波市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330300", "--温州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330400", "--嘉兴市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330500", "--湖州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330600", "--绍兴市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330700", "--金华市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330800", "--衢州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("330900", "--舟山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("331000", "--台州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("331100", "--丽水市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("340000", "安徽省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("340100", "--合肥市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("340200", "--芜湖市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("340300", "--蚌埠市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("340400", "--淮南市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("340500", "--马鞍山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("340600", "--淮北市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("340700", "--铜陵市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("340800", "--安庆市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("341000", "--黄山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("341100", "--滁州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("341200", "--阜阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("341300", "--宿州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("341400", "--巢湖市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("341500", "--六安市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("341600", "--亳州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("341700", "--池州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("341800", "--宣城市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350000", "福建省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350100", "--福州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350200", "--厦门市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350300", "--莆田市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350400", "--三明市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350500", "--泉州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350600", "--漳州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350700", "--南平市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350800", "--龙岩市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("350900", "--宁德市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360000", "江西省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360100", "--南昌市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360200", "--景德镇市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360300", "--萍乡市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360400", "--九江市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360500", "--新余市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360600", "--鹰潭市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360700", "--赣州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360800", "--吉安市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("360900", "--宜春市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("361000", "--抚州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("361100", "--上饶市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370000", "山东省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370100", "--济南市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370200", "--青岛市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370300", "--淄博市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370400", "--枣庄市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370500", "--东营市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370600", "--烟台市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370700", "--潍坊市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370800", "--济宁市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("370900", "--泰安市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("371000", "--威海市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("371100", "--日照市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("371200", "--莱芜市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("371300", "--临沂市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("371400", "--德州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("371500", "--聊城市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("371600", "--滨州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("371700", "--荷泽市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410000", "河南省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410100", "--郑州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410200", "--开封市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410300", "--洛阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410400", "--平顶山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410500", "--安阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410600", "--鹤壁市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410700", "--新乡市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410800", "--焦作市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("410900", "--濮阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("411000", "--许昌市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("411100", "--漯河市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("411200", "--三门峡市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("411300", "--南阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("411400", "--商丘市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("411500", "--信阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("411600", "--周口市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("411700", "--驻马店市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("420000", "湖北省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("420100", "--武汉市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("420200", "--黄石市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("420300", "--十堰市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("420500", "--宜昌市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("420600", "--襄樊市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("420700", "--鄂州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("420800", "--荆门市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("420900", "--孝感市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("421000", "--荆州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("421100", "--黄冈市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("421200", "--咸宁市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("421300", "--随州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("422800", "--恩施土家族苗族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("429000", "--省直辖行政单位", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430000", "湖南省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430100", "--长沙市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430200", "--株洲市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430300", "--湘潭市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430400", "--衡阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430500", "--邵阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430600", "--岳阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430700", "--常德市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430800", "--张家界市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("430900", "--益阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("431000", "--郴州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("431100", "--永州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("431200", "--怀化市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("431300", "--娄底市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("433100", "--湘西土家族苗族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440000", "广东省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440100", "--广州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440200", "--韶关市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440300", "--深圳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440400", "--珠海市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440500", "--汕头市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440600", "--佛山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440700", "--江门市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440800", "--湛江市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("440900", "--茂名市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("441200", "--肇庆市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("441300", "--惠州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("441400", "--梅州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("441500", "--汕尾市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("441600", "--河源市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("441700", "--阳江市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("441800", "--清远市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("441900", "--东莞市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("442000", "--中山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("445100", "--潮州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("445200", "--揭阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("445300", "--云浮市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450000", "广西壮族自治区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450100", "--南宁市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450200", "--柳州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450300", "--桂林市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450400", "--梧州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450500", "--北海市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450600", "--防城港市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450700", "--钦州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450800", "--贵港市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("450900", "--玉林市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("451000", "--百色市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("451100", "--贺州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("451200", "--河池市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("451300", "--来宾市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("451400", "--崇左市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("460000", "海南省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("460100", "--海口市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("460200", "--三亚市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("469000", "--省直辖县级行政单位", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("500000", "重庆市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("510000", "四川省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("510100", "--成都市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("510300", "--自贡市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("510400", "--攀枝花市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("510500", "--泸州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("510600", "--德阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("510700", "--绵阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("510800", "--广元市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("510900", "--遂宁市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("511000", "--内江市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("511100", "--乐山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("511300", "--南充市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("511400", "--眉山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("511500", "--宜宾市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("511600", "--广安市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("511700", "--达州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("511800", "--雅安市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("511900", "--巴中市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("512000", "--资阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("513200", "--阿坝藏族羌族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("513300", "--甘孜藏族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("513400", "--凉山彝族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("520000", "贵州省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("520100", "--贵阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("520200", "--六盘水市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("520300", "--遵义市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("520400", "--安顺市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("522200", "--铜仁地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("522300", "--黔西南布依族苗族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("522400", "--毕节地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("522600", "--黔东南苗族侗族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("522700", "--黔南布依族苗族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("530000", "云南省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("530100", "--昆明市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("530300", "--曲靖市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("530400", "--玉溪市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("530500", "--保山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("530600", "--昭通市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("530700", "--丽江市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("530800", "--思茅市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("530900", "--临沧市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("532300", "--楚雄彝族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("532500", "--红河哈尼族彝族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("532600", "--文山壮族苗族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("532800", "--西双版纳傣族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("532900", "--大理白族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("533100", "--德宏傣族景颇族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("533300", "--怒江傈僳族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("533400", "--迪庆藏族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("540000", "西藏自治区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("540100", "--拉萨市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("542100", "--昌都地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("542200", "--山南地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("542300", "--日喀则地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("542400", "--那曲地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("542500", "--阿里地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("542600", "--林芝地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610000", "陕西省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610100", "--西安市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610200", "--铜川市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610300", "--宝鸡市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610400", "--咸阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610500", "--渭南市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610600", "--延安市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610700", "--汉中市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610800", "--榆林市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("610900", "--安康市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("611000", "--商洛市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620000", "甘肃省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620100", "--兰州市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620200", "--嘉峪关市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620300", "--金昌市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620400", "--白银市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620500", "--天水市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620600", "--武威市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620700", "--张掖市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620800", "--平凉市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("620900", "--酒泉市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("621000", "--庆阳市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("621100", "--定西市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("622900", "--临夏回族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("623000", "--甘南藏族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("621200", "--陇南市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("630000", "青海省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("630100", "--西宁市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("632100", "--海东地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("632200", "--海北藏族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("632300", "--黄南藏族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("632500", "--海南藏族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("632600", "--果洛藏族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("632700", "--玉树藏族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("632800", "--海西蒙古族藏族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("632900", "--青海省油田教育管理中心", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("640000", "宁夏回族自治区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("640100", "--银川市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("640200", "--石嘴山市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("640300", "--吴忠市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("640400", "--固原市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("640500", "--中卫市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("650000", "新疆维吾尔自治区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("650100", "--乌鲁木齐市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("650200", "--克拉玛依市", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("652100", "--吐鲁番地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("652200", "--哈密地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("652300", "--昌吉回族自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("652700", "--博尔塔拉蒙古自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("652800", "--巴音郭楞蒙古自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("652900", "--阿克苏地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("653000", "--克孜勒苏柯尔克孜自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("653100", "--喀什地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("653200", "--和田地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("654000", "--伊犁哈萨克自治州", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("654200", "--塔城地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("654300", "--阿勒泰地区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("659000", "--省直辖行政单位", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("710000", "台湾省", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("810000", "香港特别行政区", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("820000", "澳门特别行政区", 0, baseDictionaryItem.getId()));

                // 民族
                baseDictionaryItem = new BaseDictionaryItem("PersNation", "民族", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "汉族", 1, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "蒙古族", 2, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("3", "回族", 3, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("4", "藏族", 4, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("5", "维吾尔族", 5, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("6", "苗族", 6, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("7", "彝族", 7, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("8", "壮族", 8, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("9", "布依族", 9, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("10", "朝鲜族", 10, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("11", "满族", 11, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("12", "侗族", 12, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("13", "瑶族", 13, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("14", "白族", 14, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("15", "土家族", 15, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("16", "哈尼族", 16, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("17", "哈萨克族", 17, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("18", "傣族", 18, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("19", "黎族", 19, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("20", "傈傈族", 20, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("21", "佤族", 21, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("22", "畲族", 22, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("23", "高山族", 23, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("24", "拉祜族", 24, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("25", "水族", 25, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("26", "东乡族", 26, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("27", "纳西族", 27, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("28", "景颇族", 28, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("29", "柯尔克孜族", 29, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("30", "土族", 30, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("31", "达斡尔族", 31, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("32", "仫佬族", 32, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("33", "羌族", 33, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("34", "布朗族", 34, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("35", "撒拉族", 35, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("36", "毛南族", 36, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("37", "仡佬族", 37, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("38", "锡伯族", 38, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("39", "阿昌族", 39, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("40", "普米族", 40, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("41", "塔吉克族", 41, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("42", "怒族", 42, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("43", "乌孜别克族", 43, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("44", "俄罗斯族", 44, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("45", "鄂温克族", 45, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("46", "德昂族", 46, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("47", "保安族", 47, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("48", "裕固族", 48, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("49", "京族", 49, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("50", "塔塔尔族", 50, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("51", "独龙族", 51, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("52", "鄂伦春族", 52, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("53", "赫哲族", 53, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("54", "门巴族", 54, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("55", "珞巴族", 55, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("56", "基诺族", 56, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("57", "穿青人族", 57, baseDictionaryItem.getId()));

                // 人员类型
                baseDictionaryItem = new BaseDictionaryItem("PersPersonType", "人员类型", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "正式员工", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "临时员工", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("3", "借调人员", 0, baseDictionaryItem.getId()));

                // 政治面貌
                baseDictionaryItem = new BaseDictionaryItem("PersPoliticalAffiliation", "政治面貌", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("0", "党员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "团员", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "群众", 0, baseDictionaryItem.getId()));

                // 职位
                baseDictionaryItem = new BaseDictionaryItem("PersPosition", "职位", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("0", "部门经理", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "项目经理", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "部门主管", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "员工", 0, baseDictionaryItem.getId()));

                // 雇佣类型
                baseDictionaryItem = new BaseDictionaryItem("PersPersonloyType", "雇佣类型", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("0", "临时工", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "试用期", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "正式员工", 0, baseDictionaryItem.getId()));

                // 计算机等级
                baseDictionaryItem = new BaseDictionaryItem("PersComputerLevel", "计算机等级", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("0", "初级", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "中级", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "高级", 0, baseDictionaryItem.getId()));

                // 发薪资方式
                baseDictionaryItem = new BaseDictionaryItem("PersSalaryMode", "发薪资方式", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("0", "现金发放", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "银行发放", 0, baseDictionaryItem.getId()));

                // 职称
                baseDictionaryItem = new BaseDictionaryItem("PersTitle", "职称", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("0", "初级", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "中级", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "高级", 0, baseDictionaryItem.getId()));

                // 员工类型
                baseDictionaryItem = new BaseDictionaryItem("PersEmployType", "员工类型", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "长期", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "临时", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("3", "借调", 0, baseDictionaryItem.getId()));

                // 雇佣类型
                baseDictionaryItem = new BaseDictionaryItem("PersHireType", "雇佣类型", false, "pers_module");
                baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("1", "合同内", 0, baseDictionaryItem.getId()));
                baseDictionaryValueService
                    .initData(new BaseDictionaryValueItem("2", "合同外", 0, baseDictionaryItem.getId()));
            }
        }
    }

    private void initPersFaceFailType() {
        // 人脸抠图不及格错误
        BaseDictionaryItem baseDictionaryItem =
            new BaseDictionaryItem("PersFaceFailType", "pers_face_failType", false, "pers_module");
        baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5001", "pers_face_poorResolution", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5002", "pers_face_noFace", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5003", "pers_face_manyFace", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5005", "pers_face_smallFace", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5006", "pers_face_notColor", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5007", "pers_face_seriousBlur", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5008", "pers_face_seriousBlur", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService.initData(
            new BaseDictionaryValueItem("-5009", "pers_face_intensivelyLight", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5010", "pers_face_badIllumination", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5011", "pers_face_highNoise", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5012", "pers_face_highStretch", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5013", "pers_face_covered", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5014", "pers_face_smileOpenMouth", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5015", "pers_face_largeAngle", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5016", "pers_face_seriousBlur", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService.initData(
            new BaseDictionaryValueItem("-5017", "pers_face_criticalIllumination", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService.initData(
            new BaseDictionaryValueItem("-5018", "pers_face_criticalLargeAngle", 0, baseDictionaryItem.getId()));
    }

    /**
     * 初始化名单库类型
     *
     * <AUTHOR>
     * @date   2020/7/22 18:21
     */
    private void initPersonnallist() {
        //名单库类型(允许名单/禁止名单)
        BaseDictionaryItem baseDictionaryItem = new BaseDictionaryItem("PersPersonnallistType", "pers_personnal_list_type", false, "pers_module");
        baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
        baseDictionaryValueService.initData(new BaseDictionaryValueItem(PersConstants.TYPE_BAND_LIST, "pers_personnal_bannedList",0, baseDictionaryItem.getId()));
        baseDictionaryValueService.initData(new BaseDictionaryValueItem( PersConstants.TYPE_ALLOW_LIST, "pers_personnal_allowList",0, baseDictionaryItem.getId()));
    }
}
