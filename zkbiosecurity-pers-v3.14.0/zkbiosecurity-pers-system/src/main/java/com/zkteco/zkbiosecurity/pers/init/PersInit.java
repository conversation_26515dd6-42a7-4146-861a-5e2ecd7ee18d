package com.zkteco.zkbiosecurity.pers.init;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.base.utils.VersionUtil;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersPersonnalListService;
import com.zkteco.zkbiosecurity.pers.service.PersSupportFuncService;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;
import com.zkteco.zkbiosecurity.system.constants.BasePrintTemplateConstants;
import com.zkteco.zkbiosecurity.system.service.BasePrintParamService;
import com.zkteco.zkbiosecurity.system.service.BasePrintTemplateService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BasePrintParamItem;
import com.zkteco.zkbiosecurity.system.vo.BasePrintTemplateItem;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

@Component
@Order(value = 20)
public class PersInit implements CommandLineRunner {
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private PersWiegandFmtService persWiegandFmtService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BasePrintParamService basePrintParamService;
    @Autowired
    private BasePrintTemplateService basePrintTemplateService;
    @Autowired
    private PersSupportFuncService persSupportFuncService;
    @Autowired
    private PersPersonnalListService persPersonnalListService;
    @Value("${system.language:zh_CN}")
    private String language;
    @Value("${system.isCloud:false}")
    private Boolean isCloud;

    @Override
    public void run(String... args) throws Exception {
        boolean alreadyInit = baseSysParamService.getAlreadyInitModule("PersInit");
        if (!alreadyInit) {
            // 初始化权限
            initAuthPermission();
            // 初始化韦根
            initPersWiegandFmtItem();
            // 初始化名单库
            initPersonnalList();
            // 初始化人事模块参数
            initPersParams();
            if (persSupportFuncService.isSupportPrintTemplate()) {
                // 初始化制卡模板
                initPrintTemplate();
            }
            if (!isCloud) {
                // 初始化APP菜单
                initAppMenus();
            }
            initUpgradeVersion();
            baseSysParamService.setAlreadyInitModule("PersInit");
        }
    }

    /**
     * 初始化功能菜单
     */
    private void initAuthPermission() {
        AuthPermissionItem systemItem = new AuthPermissionItem("Pers", "pers_module", "pers",
            AuthContants.RESOURCE_TYPE_SYSTEM, ZKConstant.TRUE, 2);
        systemItem = authPermissionService.initData(systemItem);

        AuthPermissionItem subMenuItem1 = new AuthPermissionItem("PersPersonManager", "pers_person_manager",
            "pers:person:manager", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        subMenuItem1.setParentId(systemItem.getId());
        subMenuItem1.setActionLink("persPerson.do");
        subMenuItem1.setImg("pers.png");
        subMenuItem1.setImgHover("pers_over.png");
        subMenuItem1 = authPermissionService.initData(subMenuItem1);
        /** --------------------------- 人员 --------------------------- */
        AuthPermissionItem subMenuItem = new AuthPermissionItem("PersPerson", "pers_person", "pers:person",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 1);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persPerson.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        AuthPermissionItem subButtonItem = new AuthPermissionItem("PersPersonRefresh", "common_op_refresh",
            "pers:person:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonAdd", "common_op_new", "pers:person:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonEdit", "common_op_edit", "pers:person:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 11);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonLeave", "pers_person_leave", "pers:person:leave",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonDeptChange", "pers_person_departmentChange",
            "pers:person:deptChange", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem =
            new AuthPermissionItem("PersPersonDeptChangeAndChangeLevel", "pers_personDepartment_changeLevel",
                "pers:personDepartment:changeLevel", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonPositionChange", "pers_position_change",
            "pers:person:batchPositionChange", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonDel", "common_op_del", "pers:person:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonResetSelfPwd", "pers_person_resetSelfPwd",
            "pers:person:resetSelfPwd", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonExport", "pers_export_personInfo", "pers:person:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonImport", "pers_import_personInfo", "pers:person:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonDataCount", "pers_person_dataCount", "pers:person:dataCount",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 10);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonCardPrint", "pers_person_cardprint", "pers:person:cardPrint",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 11);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonExportBioTemplate", "pers_export_personBioTemplate",
            "pers:bioTemplate:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 12);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonExportPhoto", "pers_export_personPhoto",
            "pers:person:exportPhoto", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 13);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonImportBioTemplate", "pers_import_biologicalTemplate",
            "pers:bioTemplate:import", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 14);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonImportPhoto", "pers_import_personPhoto",
            "pers:person:importPhoto", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 15);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 人员模板导出 --------------------------- */
        subButtonItem = new AuthPermissionItem("PersPersonExportTemplate", "pers_export_personInfoTemplate",
            "pers:person:exportTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 16);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPersonDelBioTemplate", "pers_person_delBioTemplate",
            "pers:person:delBioTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 17);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 导入离职人员 --------------------------- */
        subButtonItem = new AuthPermissionItem("PersPersonImportLeavePerson", "pers_dimission_import",
            "pers:person:importLeavePerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 18);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 下载离职人员导入模板 --------------------------- */
        subButtonItem = new AuthPermissionItem("PersPersonExportLeavePersonTemplate", "pers_dimission_downloadTemplate",
            "pers:person:exportLeavePersonTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 19);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 查看比对照片 --------------------------- */
        subButtonItem = new AuthPermissionItem("PersPersonViewBioPhoto", "pers_person_cropFaceShow",
            "pers:person:viewBioPhoto", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 20);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 人员启用 --------------------------- */
        subButtonItem = new AuthPermissionItem("PersPersonEnabled", "common_enable", "pers:person:enabled",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 21);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        /** --------------------------- 人员禁用 --------------------------- */
        subButtonItem = new AuthPermissionItem("PersPersonDisable", "common_disable", "pers:person:disable",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 22);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        /** --------------------------- 启用APP登录 --------------------------- */
        subButtonItem = new AuthPermissionItem("PersPersonAppLoginEnabled", "pers_applogin_enabled",
            "pers:person:enabledApplogin", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 23);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        /** --------------------------- 禁用APP登录 --------------------------- */
        subButtonItem = new AuthPermissionItem("PersPersonAppLoginDisable", "pers_applogin_disable",
            "pers:person:disableApplogin", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 24);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        /** --------------------------- 提取人脸模板 --------------------------- */
        subButtonItem = new AuthPermissionItem("PersPersonFaceTemplate", "pers_person_extractFaceTemplate",
            "pers:person:batchFaceTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 25);
        subButtonItem.setParentId(subMenuItem.getId());
        authPermissionService.initData(subButtonItem);

        /** --------------------------- 同步人员到ACMS --------------------------- */
        if (!"zh_CN".equals(language)) {
            subButtonItem = new AuthPermissionItem("PersPersonSyncAcms", "pers_person_syncAcms", "pers:person:syncAcms",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 26);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }

        /** --------------------------- 部门 --------------------------- */
        subMenuItem = new AuthPermissionItem("AuthDepartmentItem", "pers_department", "auth:department",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("authDepartment.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("AuthDepartmentItemRefresh", "common_op_refresh",
            "auth:department:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AuthDepartmentItemAdd", "common_op_new", "auth:department:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AuthDepartmentItemEdit", "common_op_edit", "auth:department:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AuthDepartmentItemDel", "common_op_del", "auth:department:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AuthDepartmentItemExport", "common_op_export", "auth:department:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("AuthDepartmentItemImport", "common_op_import", "auth:department:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // 部门模板-导出
        subButtonItem = new AuthPermissionItem("AuthDepartmentExportTemplate", "auth_dept_exportTemplate",
            "auth:department:exportTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 职位 --------------------------- */
        subMenuItem = new AuthPermissionItem("PersPosition", "pers_position", "pers:position",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persPosition.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("PersPositionRefresh", "common_op_refresh", "pers:position:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPositionAdd", "common_op_new", "pers:position:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPositionEdit", "common_op_edit", "pers:position:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPositionDel", "common_op_del", "pers:position:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPositionExport", "common_op_export", "pers:position:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPositionImport", "common_op_import", "pers:position:import",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPositionExportTemplate", "pers_position_downloadTemplate",
            "pers:position:exportTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 离职人员 --------------------------- */
        subMenuItem = new AuthPermissionItem("PersLeave", "pers_leave", "pers:leavePerson",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persLeavePerson.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("PersLeaveRefresh", "common_op_refresh", "pers:leavePerson:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersLeaveEdit", "common_op_edit", "pers:leavePerson:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersLeaveReinstated", "pers_leavePerson_reinstated",
            "pers:leavePerson:reinstated", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersLeaveDel", "common_op_del", "pers:leavePerson:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersLeaveExport", "common_op_export", "pers:leavePerson:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 临时人员 --------------------------- */
        subMenuItem = new AuthPermissionItem("PersTempPerson", "pers_tempPerson", "pers:tempPerson",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 5);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persTempPerson.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("PersTempPersonRefresh", "common_op_refresh", "pers:tempPerson:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        subButtonItem = new AuthPermissionItem("PersTempPersonAudit", "pers_tempPerson_audit", "pers:tempPerson:audit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        subButtonItem = new AuthPermissionItem("PersTempPersonDel", "common_op_del", "pers:tempPerson:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        subButtonItem = new AuthPermissionItem("PersTempPersonView", "pers_tempPerson_view", "pers:tempPerson:view",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 自定义属性 --------------------------- */
        subMenuItem = new AuthPermissionItem("PersAttribute", "pers_attribute", "pers:attribute",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 6);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persAttribute.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("PersAttributeRefresh", "common_op_refresh", "pers:attribute:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersAttributeAdd", "common_op_new", "pers:attribute:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersAttributeEdit", "common_op_edit", "pers:attribute:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersAttributeDel", "common_op_del", "pers:attribute:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        // -----------------------------------------人员名单库-------------------------------------------//
        subMenuItem = new AuthPermissionItem("PersPersonnalList", "pers_personnal_list", "pers:personnallist",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persPersonnalList.do");
        subMenuItem = authPermissionService.initData(subMenuItem);
        // 创建按钮
        subButtonItem = new AuthPermissionItem("PersPersonnalListRefresh", "common_op_refresh",
            "pers:personnallist:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        subButtonItem = new AuthPermissionItem("PersPersonnalListAdd", "common_op_add", "pers:personnallist:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        subButtonItem = new AuthPermissionItem("PersPersonnalListEdit", "common_op_edit", "pers:personnallist:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        subButtonItem = new AuthPermissionItem("PersPersonnalListDel", "common_op_del", "pers:personnallist:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 名单库添加人员
        subButtonItem = new AuthPermissionItem("PersPersonnalListAddPerson", "common_op_addPerson",
            "pers:personnallist:addPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 人员刷新 add by cxq 20200916
        subButtonItem = new AuthPermissionItem("PersPersonnalListPersonRefresh", "common_op_refresh",
            "pers:personnallist:refreshPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
        // 名单库删除人员 add by cxq 20200916
        subButtonItem = new AuthPermissionItem("PersPersonnalListDelPerson", "pers_common_delPerson",
            "pers:personnallist:delPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 参数设置 --------------------------- */
        subMenuItem = new AuthPermissionItem("PersParams", "common_leftMenu_paramSet", "pers:params",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 8);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persParams.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("PersParamsEdit", "common_op_edit", "pers:params:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 卡管理 --------------------------- */
        subMenuItem1 = new AuthPermissionItem("PersCardManager", "pers_card_manager", "pers:card:manager",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        subMenuItem1.setParentId(systemItem.getId());
        subMenuItem1.setActionLink("persCard.do");
        subMenuItem1.setImg("pers_card.png");
        subMenuItem1.setImgHover("pers_card_over.png");
        subMenuItem1 = authPermissionService.initData(subMenuItem1);

        /** --------------------------- 卡 --------------------------- */
        subMenuItem = new AuthPermissionItem("PersCard", "pers_card", "pers:card", AuthContants.RESOURCE_TYPE_MENU,
            ZKConstant.TRUE, 1);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persCard.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("PersCardRefresh", "common_op_refresh", "pers:card:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersCardBatchIssueCard", "pers_batchIssCard_entity",
            "pers:card:batchIssueCard", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /*subButtonItem = new AuthPermissionItem("PersCardDel","common_op_del", "pers:card:del", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE,3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);*/

        subButtonItem = new AuthPermissionItem("PersCardLoss", "pers_lossCard_entity", "pers:card:batchCardLoss",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersCardRevert", "pers_revertCard_entity", "pers:card:batchCardRevert",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersCardExport", "common_op_export", "pers:card:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        if (!language.equalsIgnoreCase("zh_CN")) {
            subButtonItem = new AuthPermissionItem("PersCardAcms", "pers_batchIssCard_acms", "pers:card:acms",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
        }

        /** --------------------------- 韦根格式 --------------------------- */
        subMenuItem = new AuthPermissionItem("PersWiegandFmtItem", "pers_wiegandFmt", "pers:wiegandFmt",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 2);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persWiegandFmt.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("PersWiegandFmtItemRefresh", "common_op_refresh",
            "pers:wiegandFmt:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersWiegandFmtItemAdd", "common_op_new", "pers:wiegandFmt:add",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersWiegandFmtItemDel", "common_op_del", "pers:wiegandFmt:del",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersWiegandFmtItemTest", "pers_wgFmt_cardFormatTesting",
            "pers:wiegandFmt:test", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersWiegandFmtItemEdit", "common_op_edit", "pers:wiegandFmt:edit",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 发卡记录 --------------------------- */
        subMenuItem = new AuthPermissionItem("PersCardIssue", "pers_card_issue", "pers:issueCard",
            AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
        subMenuItem.setParentId(subMenuItem1.getId());
        subMenuItem.setActionLink("persIssueCard.do");
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("PersCardIssueRefresh", "common_op_refresh", "pers:issueCard:refresh",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersCardIssueExport", "common_op_export", "pers:issueCard:export",
            AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        /** --------------------------- 敏感信息保护控制 --------------------------- */
        subMenuItem = new AuthPermissionItem("PersEncryptProp", "common_param_infoProtection", "pers:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 999);
        subMenuItem.setParentId(systemItem.getId());
        subMenuItem = authPermissionService.initData(subMenuItem);

        subButtonItem = new AuthPermissionItem("PersPinEncryptProp", "pers_person_pin", "pers:pin:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 1);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersNameEncryptProp", "pers_person_wholeName", "pers:name:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 2);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersCardNoEncryptProp", "pers_card_cardNo", "pers:cardNo:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 3);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersGenderEncryptProp", "pers_person_gender", "pers:gender:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 4);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersBirthdayEncryptProp", "pers_person_birthday",
            "pers:birthday:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 5);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersMobilePhoneEncryptProp", "pers_person_mobilePhone",
            "pers:mobilePhone:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 6);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersEmailEncryptProp", "pers_person_email", "pers:email:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 7);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);

        subButtonItem = new AuthPermissionItem("PersPhotoEncryptProp", "pers_person_photo", "pers:photo:encryptProp",
            AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 8);
        subButtonItem.setParentId(subMenuItem.getId());
        subButtonItem = authPermissionService.initData(subButtonItem);
    }

    /**
     * 初始化韦根
     */
    public void initPersWiegandFmtItem() {
        persWiegandFmtService.initData(new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wiegandFmt_isDefaultFmt"),
            PersConstants.WG_AUTO_MODE, (short)0, "", "", false, "0", true));
        persWiegandFmtService
            .initData(new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wgFmt_wg") + "26", PersConstants.WG_DEFAULT_MODE,
                (short)26, "pccccccccccccccccccccccccp", "eeeeeeeeeeeeeooooooooooooo", true, "0", true));
        persWiegandFmtService
            .initData(new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wgFmt_wg") + "26a", PersConstants.WG_DEFAULT_MODE,
                (short)26, "pssssssssccccccccccccccccp", "eeeeeeeeeeeeeooooooooooooo", false, "0", true));
        persWiegandFmtService.initData(
            new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wgFmt_wg") + "34", PersConstants.WG_DEFAULT_MODE, (short)34,
                "pccccccccccccccccccccccccccccccccp", "eeeeeeeeeeeeeeeeeooooooooooooooooo", true, "0", true));
        persWiegandFmtService.initData(
            new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wgFmt_wg") + "34a", PersConstants.WG_DEFAULT_MODE, (short)34,
                "pssssssssssssssssccccccccccccccccp", "eeeeeeeeeeeeeeeeeooooooooooooooooo", false, "0", true));
        persWiegandFmtService.initData(
            new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wgFmt_wg") + "36", PersConstants.WG_DEFAULT_MODE, (short)36,
                "pssssssssssssssssccccccccccccccccccp", "oooooooooooooooeeeeeeeeeeeeeeeeeeeee", true, "0", true));
        persWiegandFmtService.initData(
            new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wgFmt_wg") + "37", PersConstants.WG_DEFAULT_MODE, (short)37,
                "psssssssssssssssscccccccccccccccccccp", "eeeeeeeeeeeeeeeeeeboooooooooooooooooo", true, "0", true));
        persWiegandFmtService.initData(
            new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wgFmt_wg") + "37a", PersConstants.WG_DEFAULT_MODE, (short)37,
                "pmmmmsssssssssssscccccccccccccccccccp", "eeeeeeeeeeeeeeeeeeooooooooooooooooooo", false, "0", true));
        persWiegandFmtService.initData(new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wgFmt_wg") + "50",
            PersConstants.WG_DEFAULT_MODE, (short)50, "pssssssssssssssssccccccccccccccccccccccccccccccccp",
            "eeeeeeeeeeeeeeeeeeeeeeeeeooooooooooooooooooooooooo", true, "0", true));
        persWiegandFmtService
            .initData(new PersWiegandFmtItem(I18nUtil.i18nCode("pers_wgFmt_wg") + "66", PersConstants.WG_DEFAULT_MODE,
                (short)66, "pccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccp",
                "eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeooooooooooooooooooooooooooooooooo", true, "0", true));
    }

    /**
     * 初始化参数
     */
    public void initPersParams() {
        baseSysParamService.initData(new BaseSysParamItem("pers.userDataFolder.photo", "/pers/photo", "人员照片"));
        baseSysParamService.initData(new BaseSysParamItem("pers.personSupportFakeDel", "true", "是否支持假删"));
        baseSysParamService.initData(new BaseSysParamItem("pers.pinLen", "9", "pin的长度"));
        baseSysParamService.initData(new BaseSysParamItem("pers.pinSupportLetter", "false", "拼号是否支持字母"));
        baseSysParamService.initData(new BaseSysParamItem("pers.pinSupportDefault", "false", "pin号是否默认"));
        baseSysParamService.initData(new BaseSysParamItem("pers.pinSupportIncrement", "true", "pin号是否自动增长"));
        baseSysParamService.initData(new BaseSysParamItem("pers.cardLen", "32", "卡号长度，默认32位"));
        baseSysParamService.initData(new BaseSysParamItem("pers.siteCodeLen", "20", "siteCode的长度"));
        baseSysParamService.initData(new BaseSysParamItem("pers.cardSupportLetter", "false", "卡号是否支持字母"));
        baseSysParamService.initData(new BaseSysParamItem("pers.readIdCard", "false", "刷身份证"));
        baseSysParamService.initData(new BaseSysParamItem("pers.physicalNoToCardNo", "false", "身份证物理卡号当卡号"));
        baseSysParamService.initData(new BaseSysParamItem("pers.cardHex", "0", "卡进制显示 0：为十进制、1：为十六进制"));
        baseSysParamService.initData(new BaseSysParamItem("pers.showDept", "true", "是否显示人员和部门的部门树"));
        baseSysParamService.initData(new BaseSysParamItem("pers.cardTempalteCode", "default", "默认制卡模板"));
        baseSysParamService.initData(new BaseSysParamItem("pers.cardsSupport", "false", "是否支持多卡"));
        baseSysParamService
            .initData(new BaseSysParamItem("pers.cardsReadMode", "1", "卡号读取方式 1：控制器读头、2：ID180（读身份证物理卡号）"));
        baseSysParamService.initData(new BaseSysParamItem("pers.IDReadMode", "1", "身份证读头方式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.ocrDriveVerson", "2", "ocr驱动版本"));
        baseSysParamService.initData(new BaseSysParamItem("pers.pinRetain", "true", "离职人员是否保留编号"));
        baseSysParamService.initData(new BaseSysParamItem("pers.employeeRegistrar", "true", "是否启用员工登记"));
        baseSysParamService.initData(new BaseSysParamItem("pers.tempPerson.audit", "1", "是否直接审核通过"));
        baseSysParamService.initData(new BaseSysParamItem("pers.ocrCertNoType", "1", "ocr回填证件号码类型"));
        baseSysParamService.initData(new BaseSysParamItem("pers.tempPerson.urlCreate", "", "二维码url"));
        baseSysParamService.initData(new BaseSysParamItem("pers.selfRegistration", "1", "启用自助登记"));

        // 敏感信息保护（true启用、false禁用、默认启用）
        // baseSysParamService.initData(new BaseSysParamItem("pers.name.encryptProp", "true", "姓名加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.name.encryptMode", "S1", "姓名加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.lastName.encryptProp", "true", "姓氏加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.lastName.encryptMode", "S1", "姓氏加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.mobilePhone.encryptProp", "true", "手机号加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.mobilePhone.encryptMode", "S2", "手机号加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.gender.encryptProp", "true", "性别加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.gender.encryptMode", "S0F1", "性别加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.certNumber.encryptProp", "true", "证件号加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.certNumber.encryptMode", "S2", "证件号加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.birthday.encryptProp", "true", "出生日期加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.birthday.encryptMode", "S2", "出生日期加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.email.encryptProp", "true", "邮箱加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.email.encryptMode", "S2", "邮箱加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.carPlate.encryptProp", "true", "车牌号码加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.carPlate.encryptMode", "S2", "车牌号码加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.pin.encryptProp", "true", "编号加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.pin.encryptMode", "S2", "编号加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.cardNo.encryptProp", "true", "卡号加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.cardNo.encryptMode", "S2", "卡号加密模式"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.headPortrait.encryptProp", "true", "头像加密显示"));
        // baseSysParamService.initData(new BaseSysParamItem("pers.bioPhoto.encryptProp", "true", "比对照片加密显示"));
        // 人脸模板服务器
        baseSysParamService.initData(new BaseSysParamItem("pers.facialTemplate.enable", "0", "启用人脸模板提取"));
        baseSysParamService.initData(new BaseSysParamItem("pers.facialTemplate.serverAddr", "", "人脸模板提取服务器地址"));
        baseSysParamService.initData(new BaseSysParamItem("pers.facialTemplate.username", "", "人脸模板提取服务器用户名"));
        baseSysParamService.initData(new BaseSysParamItem("pers.facialTemplate.pwd", "", "人脸模板提取服务器密码"));
        // 人脸后台比对服务器
        baseSysParamService.initData(new BaseSysParamItem("pers.faceVerify.mode", "0", "人脸后台比对模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.faceVerify.serverAddr", "", "人脸后台比对服务地址"));
        baseSysParamService.initData(new BaseSysParamItem("pers.faceVerify.serverSecret", "", "人脸后台比对服务秘钥"));
    }

    // 初始化打印模板
    public void initPrintTemplate() {
        BasePrintTemplateItem basePrintTemplateItem = basePrintTemplateService
            .initData(new BasePrintTemplateItem(I18nUtil.i18nCode("base_printTemplate_persDefalut"),
                BasePrintTemplateConstants.TEMPLATE_VERTICAL_DEFAULT + language, 635, 1010, "pers",
                BasePrintTemplateConstants.TEMPLATE_VERTICAL, true));
        // 初始化人事制卡竖向打印模板内容
        initVerticalPrintParam(basePrintTemplateItem.getId());
        // 初始化人事制卡横向打印模板内容
        basePrintTemplateItem = basePrintTemplateService
            .initData(new BasePrintTemplateItem(I18nUtil.i18nCode("base_printTemplate_persDefalutHZ"),
                BasePrintTemplateConstants.TEMPLATE_HORIZONTAL_DEFAULT + language, 1010, 635, "pers",
                BasePrintTemplateConstants.TEMPLATE_HORIZONTAL, true));
        initHorizontalPrintParam(basePrintTemplateItem.getId());
    }

    // 初始化人事制卡竖向打印模板内容
    private void initVerticalPrintParam(String templateId) {
        List<BasePrintParamItem> basePrintParamItemList = new ArrayList<>();
        if (!language.equalsIgnoreCase("zh_CN")) {
            basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "635", "1010", "background",
                "/images/printTemplate/defaultTempUS_front.jpg"));
            basePrintParamItemList.add(new BasePrintParamItem(templateId, "0", "635", "1010", "background",
                "/images/printTemplate/verticalUS_opposite.jpg"));
        } else {
            basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "635", "1010", "background",
                "/images/printTemplate/defaultTemp_front.jpg"));
            basePrintParamItemList.add(new BasePrintParamItem(templateId, "0", "635", "1010", "background",
                "/images/printTemplate/vertical_opposite.jpg"));
        }
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.photo", "100px", "140px",
            "100", "130", "photo", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("base_printTemplate_photo")));
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.name", "122px", "313px",
            "100", "20", "text", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("base_printTemplate_wholeName")));
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.department", "122px",
            "350px", "100", "20", "text", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("base_printTemplate_dept")));
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.pin", "122px", "385px",
            "100", "20", "text", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("pers_person_pin")));
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.entryDate", "122px",
            "420px", "100", "20", "text", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("base_printTemplate_entryDate")));

        basePrintParamService.saveItemList(basePrintParamItemList);
    }

    // 初始化人事制卡横向打印模板内容
    private void initHorizontalPrintParam(String templateId) {
        List<BasePrintParamItem> basePrintParamItemList = new ArrayList<>();
        if (!language.equalsIgnoreCase("zh_CN")) {
            basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "1010", "635", "background",
                "/images/printTemplate/defaultTempHUS_front.jpg"));
            basePrintParamItemList.add(new BasePrintParamItem(templateId, "0", "1010", "635", "background",
                "/images/printTemplate/horizontalUS_opposite.jpg"));
        } else {
            basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "1010", "635", "background",
                "/images/printTemplate/defaultTempH_front.jpg"));
            basePrintParamItemList.add(new BasePrintParamItem(templateId, "0", "1010", "635", "background",
                "/images/printTemplate/horizontal_opposite.jpg"));
        }
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.photo", "50px", "130px",
            "100", "130", "photo", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("base_printTemplate_photo")));
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.name", "282px", "142px",
            "100", "20", "text", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("base_printTemplate_wholeName")));
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.department", "282px",
            "177px", "100", "20", "text", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("base_printTemplate_dept")));
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.pin", "282px", "212px",
            "100", "20", "text", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("pers_person_pin")));
        basePrintParamItemList.add(new BasePrintParamItem(templateId, "1", "printData.person.entryDate", "282px",
            "247px", "100", "20", "text", "draggable ui-draggable", "0,0,0", "18px", "center", "Arial,微软雅黑",
            I18nUtil.i18nCode("base_printTemplate_entryDate")));

        basePrintParamService.saveItemList(basePrintParamItemList);
    }

    /**
     * 初始化管理员app权限
     */
    private void initAppMenus() {
        AuthPermissionItem appModuleItem = null;
        AuthPermissionItem appMenuItem = null;
        AuthPermissionItem appButtonItem = null;

        appModuleItem = authPermissionService.getItemByCode("App");
        if (null == appModuleItem) {
            // 管理员APP模块
            appModuleItem = new AuthPermissionItem("App", "app_module", "app", AuthContants.RESOURCE_TYPE_APP_SYSTEM,
                ZKConstant.TRUE, 998);
            authPermissionService.initData(appModuleItem);
        }
        appMenuItem = new AuthPermissionItem("AppPers", "app_pers", "app:APPpers", AuthContants.RESOURCE_TYPE_APP_MENU,
            ZKConstant.TRUE, 1);
        appMenuItem.setParentId(appModuleItem.getId());
        appMenuItem = authPermissionService.initData(appMenuItem);
        // 人事--人员
        appButtonItem = new AuthPermissionItem("AppPersList", "app_pers_list", "app:APPpersList",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 1);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
        // 人事--新增
        appButtonItem = new AuthPermissionItem("AppPersAdd", "app_pers_add", "app:APPpersAdd",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 2);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
        // 人事--设置
        appButtonItem = new AuthPermissionItem("AppPersSet", "app_pers_set", "app:APPpersSet",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 3);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.initData(appButtonItem);
    }

    /**
     * 初始化模块版本信息
     */
    private void initUpgradeVersion() {
        String curVersion = VersionUtil.getReleaseGitTags(ConstUtil.SYSTEM_MODULE_PERS);
        // 快照版取不到tag name 不执行需要执行以下代码
        if (StringUtils.isNotBlank(curVersion)) {
            BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("PersUpgradeVersion");
            if (StringUtils.isBlank(baseSysParamItem.getId())) {
                // 没有则初始化版本信息
                baseSysParamItem = new BaseSysParamItem("PersUpgradeVersion", curVersion, "Pers Upgrade Version", true);
            }
            baseSysParamService.saveItem(baseSysParamItem);
        }
    }

    /**
     * 初始化名单库
     *
     * <AUTHOR>
     * @date 2020/7/31 10:53
     */
    public void initPersonnalList() {
        persPersonnalListService.initData(new PersPersonnalListItem(I18nUtil.i18nCode("pers_personnal_passList"),
            PersConstants.TYPE_ALLOW_LIST, true));
        persPersonnalListService.initData(
            new PersPersonnalListItem(I18nUtil.i18nCode("pers_personnal_banList"), PersConstants.TYPE_BAND_LIST, true));
    }

}
