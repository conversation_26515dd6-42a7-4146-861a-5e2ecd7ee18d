/*
 * File Name: PersExtendJSAndCSS.java Created by juvenle.li Copyright:Copyright ? 1985-2017 ZKTeco Inc.All right
 * reserved.
 */
package com.zkteco.zkbiosecurity.pers.web.extend;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.PropertiesUtil;
import com.zkteco.zkbiosecurity.core.web.extend.ExtendJSAndCSS;

@Component
public class PersExtendJSAndCSS implements ExtendJSAndCSS {

    @Override
    public List<String> extend() {
        List<String> exts = new ArrayList<String>();
        // 添加js
        exts.add("js/i18n-keys-pers.js");
        exts.add("js/pers.js");
        // 添加css
        exts.add("css/person.css");
        if (PropertiesUtil.enableRTL()) {
            exts.add("css/personRTL.css");
        }
        return exts;
    }

}
