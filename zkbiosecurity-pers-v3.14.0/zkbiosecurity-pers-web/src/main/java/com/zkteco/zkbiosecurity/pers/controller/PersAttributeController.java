/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.remote.PersAttributeRemote;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * @author: GenerationTools
 * @date: 2018-02-23 下午03:48
 */
@Controller
public class PersAttributeController extends BaseController implements PersAttributeRemote {

    @Autowired
    private PersAttributeService persAttributeService;

    @RequiresPermissions("pers:attribute")
    @Override
    public ModelAndView index() {
        return new ModelAndView("pers/attribute/persAttribute");
    }

    @RequiresPermissions({"pers:attribute:edit", "pers:attribute:add"})
    @Override
    public ModelAndView edit(@RequestParam(value = "id", required = false) String id) {
        PersAttributeItem item = null;
        if (StringUtils.isNotBlank(id)) {
            item = persAttributeService.getItemById(id);
            String attrName = I18nUtil.i18nCode("pers_attr_" + item.getAttrName());
            if (!attrName.contains("pers_attr_")) {
                item.setAttrName(attrName);
            }
            request.setAttribute("item", item);

        }
        request.setAttribute("maxLimit", false);
        if (item == null) {
            List<PersAttributeItem> attrList = persAttributeService.getByCondition(new PersAttributeItem());
            if (attrList.size() >= 32) {
                request.setAttribute("maxLimit", true);
            }

            Integer defaultX = 1;
            Integer defaultY = 1;
            Integer defFiledIndex = 1;
            Set<Integer> filedIndexs =
                attrList.stream().map(PersAttributeItem::getFiledIndex).collect(Collectors.toSet());
            for (int i = 1; i <= 32; i++) {
                if (filedIndexs.add(i)) {
                    defFiledIndex = i;
                    break;
                }
            }
            Set<String> rowSet =
                attrList.stream().map(i -> i.getPositionX() + "_" + i.getPositionY()).collect(Collectors.toSet());
            for (int i = 6; i < 40; i++) {
                defaultX = i / 2;
                defaultY = i % 2 + 1;
                if (rowSet.add(defaultX + "_" + defaultY)) {
                    break;
                }
            }
            request.setAttribute("defaultX", defaultX);
            request.setAttribute("defaultY", defaultY);
            request.setAttribute("defFiledIndex", defFiledIndex);
        }

        return new ModelAndView("pers/attribute/editPersAttribute");
    }

    @RequiresPermissions({"pers:attribute:edit", "pers:attribute:add"})
    @LogRequest(module = "pers_module", object = "pers_attribute", opType = "common_op_edit",
        requestParams = {"attrName"}, opContent = "pers_attribute_attrName")
    @Override
    public ZKResultMsg save(PersAttributeItem item) {
        ZKResultMsg res = new ZKResultMsg();
        persAttributeService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("pers:attribute:refresh")
    @Override
    public DxGrid list(PersAttributeItem condition) {
        if (StringUtils.isNoneBlank(condition.getAttrName())) {
            List<PersAttributeItem> attrList = persAttributeService.getItemsByInit();
            List<String> idList = persAttributeService.findIdsByAttrName(condition.getAttrName());
            for (PersAttributeItem initAttr : attrList) {
                String attrName = I18nUtil.i18nCode("pers_attr_" + initAttr.getAttrName());
                if (StringUtils.isNoneBlank(attrName)
                    && attrName.toLowerCase().contains(condition.getAttrName().toLowerCase())) {
                    idList.add(initAttr.getId());
                }
            }
            if (!idList.isEmpty()) {
                condition.setAttrName(null);
                condition.setInId(StringUtils.join(idList, ","));
            }
        }
        Pager pager = persAttributeService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @LogRequest(module = "pers_module", object = "pers_attribute", opType = "common_op_del",
        requestParams = {"attrName"}, opContent = "pers_attribute_attrName")
    @Override
    public ZKResultMsg del(@RequestParam("ids") String ids) {
        persAttributeService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String isExist(@RequestParam("id") String id, @RequestParam("showName") String showName) {
        boolean isExist = false;
        if (StringUtils.isNoneBlank(showName)) {
            isExist = true;
            List<PersAttributeItem> attrList = persAttributeService.getByCondition(new PersAttributeItem());
            for (PersAttributeItem attr : attrList) {
                String attributeName = "";
                if ("init".equals(attr.getSqlStr())) {
                    String attrName = I18nUtil.i18nCode("pers_attr_" + attr.getAttrName());
                    if (StringUtils.isNotBlank(attrName) && !attrName.contains("pers_attr_")) {
                        attributeName = attrName;
                    } else {
                        attributeName = attr.getAttrName();
                    }
                } else {
                    attributeName = attr.getAttrName();
                }
                if (showName.equals(attributeName) && !attr.getId().toString().equals(id)) {
                    isExist = false;
                    break;
                }
            }
        } else {
            PersAttributeItem item = new PersAttributeItem();
            item.setAttrName(showName);
            isExist = persAttributeService.getByCondition(item).isEmpty();
        }
        return String.valueOf(isExist);
    }

    @Override
    public String isRowColExist(@RequestParam("id") String id, @RequestParam("positionX") Integer positionX,
        @RequestParam("positionY") Integer positionY) {
        boolean flag = true;
        PersAttributeItem condition = new PersAttributeItem();
        condition.setPositionX(positionX);
        condition.setPositionY(positionY);
        List<PersAttributeItem> items = persAttributeService.getByCondition(condition);
        for (PersAttributeItem item : items) {
            if (!item.getId().equals(id)) {
                flag = false;
                break;
            }
        }
        return String.valueOf(flag);
    }

    @Override
    public ZKResultMsg getAllPersAttrs() {
        List<PersAttributeItem> items = persAttributeService.getByCondition(new PersAttributeItem());
        HashMap<Integer, String> map = Maps.newHashMap();
        for (PersAttributeItem item : items) {
            String attrName = item.getAttrName();
            if ("init".equals(item.getSqlStr())) {
                String name = I18nUtil.i18nCode("pers_attr_" + attrName);
                if (StringUtils.isNotBlank(name) && !name.contains("pers_attr_")) {
                    attrName = name;
                }
            }
            map.put(item.getFiledIndex(), attrName);
        }
        return new ZKResultMsg(map);
    }

    @Override
    public String countAttrList() {
        boolean maxLimit = false;
        List<PersAttributeItem> attrList = persAttributeService.getByCondition(new PersAttributeItem());
        if (attrList.size() >= 32) {
            maxLimit = true;
        }
        return String.valueOf(maxLimit);
    }
}