/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.ExcelUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.remote.PersPositionRemote;
import com.zkteco.zkbiosecurity.pers.service.PersPositionService;
import com.zkteco.zkbiosecurity.pers.util.PersExportTemplateUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersPositionItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * @author: GenerationTools
 * @date: 2018-02-23 下午03:48
 */
@Controller
public class PersPositionController extends ExportController implements PersPositionRemote {
    @Autowired
    private PersPositionService persPositionService;
    @Autowired
    private PersExportTemplateUtil persExportTemplateUtil;
    @Autowired
    private ProgressCache progressCache;

    @RequiresPermissions("pers:position")
    @Override
    public ModelAndView index() {
        return new ModelAndView("pers/position/persPosition");
    }

    @RequiresPermissions({"pers:position:edit", "pers:position:add"})
    @Override
    public ModelAndView edit(@RequestParam(value = "id", required = false) String id) {
        if (StringUtils.isNotBlank(id)) {
            PersPositionItem item = persPositionService.getItemById(id);
            request.setAttribute("item", item);
        }
        return new ModelAndView("pers/position/editPersPosition");
    }

    @RequiresPermissions({"pers:position:edit", "pers:position:add"})
    @LogRequest(module = "pers_module", object = "pers_position", opType = "common_op_edit", requestParams = {"code"},
        opContent = "pers_position_code")
    @Override
    public ZKResultMsg save(PersPositionItem item) {
        ZKResultMsg res = new ZKResultMsg();
        persPositionService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("pers:position:refresh")
    @Override
    public DxGrid list(PersPositionItem condition) {
        if (StringUtils.isNotBlank(condition.getParentId())) {
            // 转换查询出父部门下的所有子部门
            condition.setInId(persPositionService.getPositionIdsByParentId(condition.getParentId()));
            condition.setParentId(null);
        }
        Pager pager = persPositionService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("pers:position:del")
    @LogRequest(module = "pers_module", object = "pers_position", opType = "common_op_del", requestParams = {"name"},
        opContent = "pers_position_name")
    @Override
    public ZKResultMsg del(@RequestParam("ids") String ids) {
        persPositionService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String isExist(@RequestParam("code") String code) {
        return String.valueOf(persPositionService.getItemByCode(code) == null);
    }

    @Override
    public ZKResultMsg isNameExist(@RequestParam("name") String name, @RequestParam("id") String id,
        @RequestParam("parentId") String parentId) {
        return I18nUtil.i18nMsg(new ZKResultMsg(this.persPositionService.isPositionNameExist(id, parentId, name)));
    }

    @Override
    public TreeItem tree() {
        List<PersPositionItem> demoDepartmentItems = persPositionService.getByCondition(new PersPositionItem());
        List<TreeItem> items = new ArrayList<TreeItem>();
        TreeItem item = null;
        TreeItem pItem = null;
        for (PersPositionItem depart : demoDepartmentItems) {
            item = new TreeItem();
            item.setId(depart.getId());
            item.setText(depart.getName());
            if (depart.getParentId() != null) {
                pItem = new TreeItem(depart.getParentId());
            } else {
                pItem = new TreeItem("0");
            }
            item.setParent(pItem);

            items.add(item);
        }
        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @Override
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonObject = JSONObject.parseObject(request.getParameter("jsonColumn"), Feature.OrderedField);
        Map<String, String> commentMap = new HashMap<>();
        for (String key : jsonObject.keySet()) {
            switch (key) {
                case "code":
                    commentMap.put(key, I18nUtil.i18nCode("common_jqMsg_required"));
                    break;
                case "name":
                    commentMap.put(key, I18nUtil.i18nCode("common_jqMsg_required"));
                    break;
                case "parentCode":
                    commentMap.put(key, I18nUtil.i18nCode("common_jqMsg_required"));
                    break;
            }
        }
        persExportTemplateUtil.templateExport(request, response, commentMap);
    }

    @Override
    public void export(HttpServletRequest req, HttpServletResponse resp) {
        PersPositionItem positionItem = new PersPositionItem();
        setConditionValue(positionItem);
        if (StringUtils.isNotBlank(positionItem.getParentId())) {
            String positionIds = persPositionService.getPositionIdsByParentId(positionItem.getParentId());
            if (StringUtils.isNotBlank(positionIds)) {
                positionItem.setInId(positionIds);
                positionItem.setParentId(null);
            }
        }
        List<PersPositionItem> list = (List<PersPositionItem>)persPositionService
            .getPersPositionItemData(PersPositionItem.class, positionItem, getBeginIndex(), getEndIndex());
        excelExport(list, PersPositionItem.class);
    }

    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<PersPositionItem> itemList = ExcelUtil.excelImport(upload.getInputStream(), PersPositionItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(persPositionService.importExcel(itemList));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import PersPosition Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }
}