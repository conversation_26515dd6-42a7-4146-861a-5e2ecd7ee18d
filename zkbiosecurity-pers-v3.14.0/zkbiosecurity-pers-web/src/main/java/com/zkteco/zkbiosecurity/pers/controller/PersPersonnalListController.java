/*
 * @author: GenerationTools
 * 
 * @date: 2020-07-22 16:35:16 Copyright:Copyright © 2016-2019 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.remote.PersPersonnalListRemote;
import com.zkteco.zkbiosecurity.pers.service.PersPersonnalListService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonPersonnalListSelectItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnallistPersonItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 名单库
 * 
 * @author: GenerationTools
 * @date: 2020-07-22 16:35:16
 */
@Controller
public class PersPersonnalListController extends BaseController implements PersPersonnalListRemote {
    @Autowired
    private PersPersonnalListService persPersonnalListService;
    @Autowired
    private ProgressCache progressCache;

    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2020-07-22 16:35:16
     * @return
     */
    @Override
    @RequiresPermissions("pers:personnallist")
    public ModelAndView index() {
        return new ModelAndView("pers/personnallist/persPersonnalList");
    }

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2020-07-22 16:35:16
     * @param id
     * @return
     */
    @Override
    @RequiresPermissions({"pers:personnallist:edit", "pers:personnallist:add"})
    public ModelAndView edit(String id) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", persPersonnalListService.getItemById(id));
        }
        return new ModelAndView("pers/personnallist/editPersPersonnalList");
    }

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2020-07-22 16:35:16
     * @param item
     * @return
     */
    @Override
    @RequiresPermissions({"pers:personnallist:edit", "pers:personnallist:add"})
    public ZKResultMsg save(PersPersonnalListItem item) {
        ZKResultMsg res = new ZKResultMsg();
        persPersonnalListService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @Override
    @RequiresPermissions("pers:personnallist:refresh")
    public DxGrid list(PersPersonnalListItem codition) {
        codition.setTypeNotIn(PersConstants.TYPE_VISALLOW_LIST + "," + PersConstants.TYPE_VISBAND_LIST);
        Pager pager = persPersonnalListService.getItemsByPage(codition, getPageNo(), getPageSize());
        List<PersPersonnalListItem> list = (List<PersPersonnalListItem>)pager.getData();
        // 获取人员数量
        Map<String, String> allPersonnelListCount =
            persPersonnalListService.getPersonCountByAuthUserFilter(request.getSession().getId());
        list.stream().forEach(item -> {
            String count = allPersonnelListCount.get(item.getId());
            if (StringUtils.isBlank(count)) {
                item.setPersonCount("0");
            } else {
                item.setPersonCount(count);
            }
        });
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    @RequiresPermissions("pers:personnallist:del")
    public ZKResultMsg delete(String ids) {
        return persPersonnalListService.delByIds(ids);
    }

    @Override
    public ZKResultMsg getPersonList(String id) {
        return new ZKResultMsg(persPersonnalListService.getPersonIdsById(id));
    }

    @Override
    @RequiresPermissions("pers:personnallist:refreshPerson")
    public DxGrid getPersonneList(PersPersonnallistPersonItem condition) {
        String sessionId = request.getSession().getId();
        Pager pager = persPersonnalListService.getPersonPager(sessionId, condition, getPageNo(), getPageSize());
        persPersonnalListService.protectPin((List<PersPersonnallistPersonItem>)pager.getData());
        return GridUtil.convert(pager, PersPersonnallistPersonItem.class);
    }

    @Override
    @RequiresPermissions("pers:personnallist:addPerson")
    public ZKResultMsg addPerson(String personnelListId, String personIds) {
        String clientId = request.getParameter("clientId");
        String deptIds = request.getParameter("deptIds");
        String checkVal = request.getParameter("checkVal");
        // 选择部门下人员
        if ("2".equals(checkVal) && StringUtils.isNotBlank(deptIds)) {
            personIds = "";
        } else {
            deptIds = "";
        }
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
        ZKResultMsg zkResultMsg = persPersonnalListService.addPerson(personnelListId, personIds, deptIds);
        progressCache.setProcess(new ProcessBean(99, 99), clientId);
        progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish") + "<br/>", "",
            I18nUtil.i18nCode("common_op_currProgress"));
        return I18nUtil.i18nMsg(zkResultMsg);

    }

    @Override
    public DxGrid selectPersonlist(PersPersonPersonnalListSelectItem codition) {
        Pager pager = new Pager();
        if (codition.getType().equals("noSelected")) {
            pager = this.persPersonnalListService.getNoExistPerson(this.request.getSession().getId(), codition,
                this.getPageNo(), this.getPageSize());
        } else if (codition.getType().equals("selected")) {
            pager.setData(new ArrayList());
        }
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    @RequiresPermissions("pers:personnallist:delPerson")
    public ZKResultMsg delPerson(String personnallistId, String ids) {
        return persPersonnalListService.deletePerson(personnallistId, ids);
    }

    @Override
    public Boolean verifyName(String name) {
        // 如果该名称在数据库不存在,返回true,否则返回false
        return persPersonnalListService.getItemByName(name) == null ? true : false;
    }

    @Override
    public ZKResultMsg getPersonnalListSelectData() {
        return new ZKResultMsg(persPersonnalListService.getPersonnalListSelectData());
    }
}
