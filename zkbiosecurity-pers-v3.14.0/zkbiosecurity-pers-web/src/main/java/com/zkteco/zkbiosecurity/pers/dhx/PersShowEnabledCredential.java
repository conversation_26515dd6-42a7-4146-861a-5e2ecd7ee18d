package com.zkteco.zkbiosecurity.pers.dhx;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.ShowGridColumn;
import com.zkteco.zkbiosecurity.core.utils.WebContextUtil;
import com.zkteco.zkbiosecurity.security.SecurityService;

@Component
public class PersShowEnabledCredential implements ShowGridColumn {
    @Autowired
    private SecurityService securityService;

    @Override
    public boolean isShow(Object obj) {
        // 启禁用的权限都没有时，列表不显示启用列
        return !(!securityService.checkPermission(WebContextUtil.getCurrentSessionId(), "pers:person:enabled")
            && !securityService.checkPermission(WebContextUtil.getCurrentSessionId(), "pers:person:disable"));
    }
}
