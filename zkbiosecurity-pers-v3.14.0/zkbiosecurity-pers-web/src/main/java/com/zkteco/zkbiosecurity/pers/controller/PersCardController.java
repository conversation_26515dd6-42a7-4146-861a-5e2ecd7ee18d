/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.pers.remote.PersCardRemote;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * @author: GenerationTools
 * @date: 2018-02-23 下午03:48
 */
@Controller
public class PersCardController extends ExportController implements PersCardRemote {
    @Autowired
    private PersCardService persCardService;

    @RequiresPermissions("pers:card")
    @Override
    public ModelAndView index() {
        return new ModelAndView("pers/card/persCard");
    }

    @RequiresPermissions("pers:card:refresh")
    @Override
    public DxGrid list(PersCardItem condition) {
        Pager pager = persCardService.loadPagerByAuthUserFilter(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        persCardService.protectPinAndCard((List<PersCardItem>)pager.getData());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("pers:card:batchCardLoss")
    @Override
    public ZKResultMsg batchCardLoss(@RequestParam("ids") String ids) {
        persCardService.batchCardLoss(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("pers:card:batchCardRevert")
    @Override
    public ZKResultMsg batchCardRevert(@RequestParam("ids") String ids) {
        persCardService.batchCardRevert(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String isExist(@RequestParam("cardNo") String cardNo,
        @RequestParam(value = "pin", required = false) String pin) {
        return String.valueOf(!persCardService.isExistByCardNoAndPinAndOtherFilter(cardNo, pin));
    }

    @Override
    public String checkCardBit(String cardNo) {
        return String.valueOf(persCardService.checkCardBit(cardNo));
    }

    @Override
    public Map<String, String> cardVerification(String cardNo) {
        return persCardService.cardVerification(cardNo);
    }

    @RequiresPermissions("pers:card:export")
    @LogRequest(module = "pers_module", object = "pers_card", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        PersCardItem persCardItem = new PersCardItem();
        setConditionValue(persCardItem);
        List<PersCardItem> itemList =
            (List<PersCardItem>)persCardService.getItemDataByAuthUserFilter(request.getSession().getId(),
                PersCardItem.class, persCardItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, PersCardItem.class);
    }
}