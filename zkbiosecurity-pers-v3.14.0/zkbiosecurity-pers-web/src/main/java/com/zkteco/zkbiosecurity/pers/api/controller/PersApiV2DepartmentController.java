package com.zkteco.zkbiosecurity.pers.api.controller;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiDepartmentItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersApiDepartmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.text.MessageFormat;

/**
 * 部门接口
 * <AUTHOR>
 * @Date: 2018/11/9 11:00
 */
@Controller
@RequestMapping(value = {"/api/v2/department"})
@Slf4j
@Api(tags = "PersDepartment ", description = "person department")
public class PersApiV2DepartmentController {
    @Autowired
    private PersApiDepartmentService persApiDepartmentService;
    /**
     * 根据code删除部门
     * @param code
     * @return
     */
    @ApiOperation(value = "Delete Department", notes = "Delete Department By Code", response = ApiResultMessage.class)
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"code"})
    public ApiResultMessage deleteByCode(@RequestParam String code) {
        ApiResultMessage rs = null;
        try {
            rs = persApiDepartmentService.deleteDeptByCode(code);
        }
        catch (ZKBusinessException e) {
            rs = ApiResultMessage.message(PersConstants.DEPT_DELETE_FAILED, MessageFormat.format(I18nUtil.i18nCode(e.getMessage()), e.getObjects()));
        }
        catch (Exception e) {
            log.error("api department/delete error ", e);
            rs = ApiResultMessage.failedMessage(ConstUtil.ESB_SEND_FAILURE);
        }
        return rs;
    }

    /**
     * 根据code获取部门
     * @param
     * @return
     */
    @ApiOperation(value = "Get Department", notes = "Get Department By Code", response = ApiResultMessage.class)
    @RequestMapping(value = "/get", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"code"})
    public ApiResultMessage getByCode(@RequestParam String code) {
        ApiResultMessage rs = null;
        try {
            rs = persApiDepartmentService.getDeptByCode(code);
        } catch (Exception e) {
            log.error("api department/get error ", e);
            rs = ApiResultMessage.failedMessage(ConstUtil.ESB_SEND_FAILURE);
        }
        return rs;
    }
}
