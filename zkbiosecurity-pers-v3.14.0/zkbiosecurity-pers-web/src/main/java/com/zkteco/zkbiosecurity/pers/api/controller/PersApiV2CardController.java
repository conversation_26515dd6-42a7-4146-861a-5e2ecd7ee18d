package com.zkteco.zkbiosecurity.pers.api.controller;
import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiCardItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 人员卡接口
 */

@Controller
@RequestMapping(value = {"/api/v2/card"})
@Slf4j
@Api(tags = "PersCard", description = "person card")
public class PersApiV2CardController {
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersParamsService persParamsService;
    /**
     * 获取人员卡号
     * @auther lambert.li
     * @date 2018/11/26 14:37
     * @param pin
     * @return
     */
    @ApiOperation(value = "Get Card List By Pin", notes = "Return Card List", response = ApiResultMessage.class)
    @RequestMapping(value = "/getCards", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage getCardsByPin(@RequestParam String pin) {
        ApiResultMessage rs = ApiResultMessage.successMessage();
        try {
            if (StringUtils.isBlank(pin)) {
                return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL, I18nUtil.i18nCode("pers_import_pinNotEmpty"));
            }
            PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
            if (persPersonItem == null) {
                return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST, I18nUtil.i18nCode("pers_api_personNotExist"));
            }
            List<PersCardItem> persCardItems = persCardService.getItemByPersonId(persPersonItem.getId());
            if (persCardItems != null && !persCardItems.isEmpty()) {
                List<PersApiCardItem> apiCardItems = Lists.newArrayList();
                persCardItems.forEach(persCardItem -> {
                    PersApiCardItem persApiCardItem = PersApiCardItem.createCard(persCardItem);
                    if (persApiCardItem != null) {
                        apiCardItems.add(persApiCardItem);
                    }
                });
                rs.setData(apiCardItems);
            }
        } catch (Exception e) {
            log.error("api card/getCards error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR, I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }
}
