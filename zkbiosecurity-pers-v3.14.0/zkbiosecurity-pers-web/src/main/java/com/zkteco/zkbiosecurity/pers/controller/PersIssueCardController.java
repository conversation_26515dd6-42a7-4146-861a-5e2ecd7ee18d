/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.remote.PersIssueCardRemote;
import com.zkteco.zkbiosecurity.pers.service.PersIssueCardService;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersIssueCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersNoCardPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonSelectItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 发卡记录Controller
 */
@Controller
public class PersIssueCardController extends ExportController implements PersIssueCardRemote {
    @Autowired
    private PersIssueCardService persIssueCardService;
    @Autowired
    private ProgressCache progressCache;

    @RequiresPermissions("pers:issueCard")
    @Override
    public ModelAndView index() {
        return new ModelAndView("pers/issueCard/persIssueCard");
    }

    @RequiresPermissions("pers:issueCard:refresh")
    @Override
    public DxGrid list(PersIssueCardItem condition) {
        Pager pager = persIssueCardService.getItemsByPage(condition, getPageNo(), getPageSize());
        persIssueCardService.protectIssuePinAndCard((List<PersIssueCardItem>)pager.getData());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("pers:person")
    @Override
    public DxGrid noCardPerson(PersNoCardPersonItem condition) {
        Pager pager = null;
        if ("noSelected".equals(condition.getType())) {
            pager = persIssueCardService.findNoCardPerson(request.getSession().getId(), condition, getPageNo(),
                getPageSize());
        } else if ("selected".equals(condition.getType())) {
            pager = new Pager(getPageNo(), getPageSize());
            pager.setData(new ArrayList<PersPersonSelectItem>());
        }
        return GridUtil.convert(pager, condition.getClass());
    }

    @LogRequest(module = "pers_module", object = "pers_card", opType = "pers_batchIssCard_entity",
        requestParams = {"pinCardListJson"}, opContent = "pers_person_pin")
    @RequiresPermissions("pers:card:batchIssueCard")
    @Override
    public ZKResultMsg batch(@RequestParam("cardListJson") String cardListJson) {
        List<PersCardItem> cardPersonItems = JSON.parseObject(cardListJson, new TypeReference<List<PersCardItem>>() {});
        persIssueCardService.batchIssueCard(cardPersonItems);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("pers:issueCard:export")
    @LogRequest(module = "pers_module", object = "pers_card", opType = "common_op_export",
        opContent = "common_op_export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        PersIssueCardItem persIssueCardItem = new PersIssueCardItem();
        setConditionValue(persIssueCardItem);
        List<PersIssueCardItem> itemList = (List<PersIssueCardItem>)persIssueCardService
            .getItemData(PersIssueCardItem.class, persIssueCardItem, getBeginIndex(), getEndIndex());
        excelExport(itemList, PersIssueCardItem.class);
    }

    @LogRequest(module = "pers_module", object = "pers_card", opType = "pers_batchIssCard_acms",
        requestParams = {"persPins"}, opContent = "pers_person_pin")
    @RequiresPermissions("pers:card:acms")
    @Override
    public ZKResultMsg batchAcmsCard() {
        String personIds = request.getParameter("personIds");
        String deptIds = request.getParameter("deptIds");
        String checkVal = request.getParameter("checkVal");
        // 选择部门下人员
        if ("2".equals(checkVal) && StringUtils.isNotBlank(deptIds)) {
            personIds = "";
        } else {
            deptIds = "";
        }
        progressCache.beginProcess(I18nUtil.i18nCode("common_op_startProcessing") + "<br/>");
        try {
            persIssueCardService.batchIssueAcmsCard(personIds, deptIds);
        } catch (Exception e) {
            String message = "pers_issueCard_error";
            if (e instanceof ZKBusinessException) {
                message = StringUtils.isNotBlank(e.getMessage()) ? e.getMessage() : "pers_issueCard_error";
            }
            progressCache.setProcess(new ProcessBean(30, 30,
                "<font color='red'>" + I18nUtil.i18nCode(message) + "</font><br/>", null, null));
            log.error("batchIssueAcmsCard  error", e);
        } finally {
            progressCache.setProcess(new ProcessBean(99, 99));
            progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish") + "<br/>", "",
                I18nUtil.i18nCode("common_op_currProgress"));
        }
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }
}