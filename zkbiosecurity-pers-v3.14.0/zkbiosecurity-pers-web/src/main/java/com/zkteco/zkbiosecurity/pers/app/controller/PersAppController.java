package com.zkteco.zkbiosecurity.pers.app.controller;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.app.vo.PersAppParamsItem;
import com.zkteco.zkbiosecurity.pers.app.vo.PersAppPersonItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersAppService;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * APP人员接口
 * 
 * <AUTHOR>
 * @Date: 2018/11/30 09:20
 */
@RestController
@RequestMapping(value = "/app/v1")
public class PersAppController {

    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersAppService persAppService;
    @Autowired
    private PersCardService persCardService;

    /**
     * 人员新增时，判断pin号是否要自增,如果要自增就将生成自增pin号给前端
     * 
     * @auther lambert.li
     * @date 2018/11/30 10:11
     * @return
     */
    @RequestMapping(value = "/getPersPin", method = RequestMethod.POST)
    public AppResultMessage getPersPin() {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        Map<String, String> paramMap = persParamsService.getPersParams();
        if (!paramMap.isEmpty()) {
            // 人员编号是否支持字母参数
            String pinSupportLetter = paramMap.get("pers.pinSupportLetter");
            // 人员编号是否自动递增参数
            String pinSupportIncrement = paramMap.get("pers.pinSupportIncrement");
            // 人员编号最大长度
            String pinLen = paramMap.get("pers.pinLen");
            // 新增页面;人员pin号不支持字母且支持pin号自增
            if ("false".equals(pinSupportLetter) && "true".equals(pinSupportIncrement)) {
                // 取redis中保存的自增人员编号
                String pin = persPersonService.getIncPoint();
                if (StringUtils.isNotEmpty(pin)) {
                    JSONObject pinObject = new JSONObject();
                    pinObject.put("pin", pin);
                    appResultMessage.setData(pinObject);
                }
            }
        }
        return appResultMessage;
    }

    /**
     * 新增/编辑人员
     * 
     * @auther lambert.li
     * @date 2018/12/3 8:39
     * @param personBean
     * @return
     */
    @RequestMapping(value = "/editPersPerson", method = RequestMethod.POST)
    public AppResultMessage editPersPerson(@RequestBody PersAppPersonItem personBean) {
        return persAppService.editAppPerson(personBean);
    }

    /**
     * 获取人员部门
     * 
     * @auther lambert.li
     * @date 2018/12/3 9:35
     * @param data {"token" : "8D17633A08DD24AED01E3BC60D6B2FC0C21C7063FDF5F7B3BB6C9D3EDDFB4740"}
     * @return
     */
    @RequestMapping(value = "/getDepts", method = RequestMethod.POST)
    public AppResultMessage getDepts(@RequestBody JSONObject data) {
        String token = data.getString("token");
        return persAppService.getDepts(token);
    }

    /**
     * 人员数量统计: 统计人员总数、人员持有卡总数、指纹总数、指静脉总数、人脸统计、性别统计
     * 
     * @auther lambert.li
     * @date 2018/12/3 11:38
     * @return
     */
    @RequestMapping(value = "/getPersonDataCount", method = RequestMethod.POST)
    @ResponseBody
    public AppResultMessage getPersonDataCount() {
        return persAppService.getPersonDataCount();
    }

    /**
     * 根据人员pin、姓名模糊查询人员 如果pin和姓名传入为空则查询该用户权限下的所有人员
     * 
     * @auther lambert.li
     * @date 2018/12/3 13:44
     * @param data
     *            {"filter":"sanzhang","pageNo":1,"pageSize":20,"token":"8D17633A08DD24AED01E3BC60D6B2FC0C21C7063FDF5F7B3BB6C9D3EDDFB4740"}
     * @return
     */
    @RequestMapping(value = "/getPersonByPinOrName", method = RequestMethod.POST)
    public AppResultMessage getPersonByPinOrName(@RequestBody JSONObject data) {
        String filter = data.getString("filter");
        String token = data.getString("token");
        int pageNo = data.containsKey("pageNo") ? data.getIntValue("pageNo") : 1;
        int pageSize = data.containsKey("pageSize") ? data.getIntValue("pageSize") : 20;
        return persAppService.getPersonByPinOrName(filter, token, pageNo, pageSize);
    }

    /**
     * 根据人员pin获取人员
     * 
     * @auther lambert.li
     * @date 2018/12/3 16:42
     * @param persAppPersonItem
     * @return
     */
    @RequestMapping(value = "/getPersonByPin", method = RequestMethod.POST)
    public AppResultMessage getPersonByPin(@RequestBody PersAppPersonItem persAppPersonItem) {
        String pin = persAppPersonItem.getPin();
        // 人员编号不能为空
        if (StringUtils.isBlank(pin)) {
            return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSONPIN_ISNULL));
        }
        return persAppService.getPersonByPin(pin, persAppPersonItem.getToken());
    }

    /**
     * 根据人员pin获取人员
     * 
     * @auther lambert.li
     * @date 2018/12/3 16:42
     * @param persAppPersonItem
     * @return
     */
    @RequestMapping(value = "/getAccLevelsByPin", method = RequestMethod.POST)
    public AppResultMessage getAccLevelsByPin(@RequestBody PersAppPersonItem persAppPersonItem) {
        String pin = persAppPersonItem.getPin();
        return persAppService.getAccLevelsByPin(pin, persAppPersonItem.getToken());
    }

    /**
     * 根据人员pin删除人员
     * 
     * @auther lambert.li
     * @date 2018/12/3 16:42
     * @param persAppPersonItem
     * @return
     */
    @RequestMapping(value = "/delRealById", method = RequestMethod.POST)
    public AppResultMessage delRealByPin(@RequestBody PersAppPersonItem persAppPersonItem) {
        String pin = persAppPersonItem.getPin();
        // 人员编号不能为空
        if (StringUtils.isBlank(pin)) {
            return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSONPIN_ISNULL));
        }
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem == null) {
            return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSONID_NOTEXIST));
        }
        persPersonService.deleteByIds(persPersonItem.getId());
        return AppResultMessage.successMessage();
    }

    /**
     * 获取人事参数设置
     * 
     * @auther lambert.li
     * @date 2018/12/3 17:10
     * @return
     */
    @RequestMapping(value = "/getParams", method = RequestMethod.POST)
    public String getParams() {
        JSONObject params = new JSONObject();
        Map<String, String> persParams = persParamsService.getPersParams();
        // pin最大长度
        params.put("pinLen", persParams.get("pers.pinLen"));
        // pin是否支持字母
        params.put("pinSupportLetter", persParams.get("pers.pinSupportLetter"));
        // pin是否默认
        params.put("pinSupportDefault", persParams.get("pers.pinSupportDefault"));
        // pin是否自动增长
        params.put("pinSupportIncrement", persParams.get("pers.pinSupportIncrement"));
        // 是否支持多卡
        params.put("cardsSupport", persParams.get("pers.cardsSupport"));
        // 离职人员新架构默认保留编号
        params.put("pinRetain", "true");
        // 卡号长度，默认32位
        params.put("cardLen", persParams.get("pers.cardLen"));
        // 卡进制显示 0：为十进制、1：为十六进制
        params.put("cardHex", persParams.get("pers.cardHex"));
        // 卡号读取方式 1：控制器读头、2：ID180（读身份证物理卡号）
        params.put("cardsReadMode", persParams.get("pers.cardsReadMode"));
        // 身份证读头方式
        params.put("IDReadMode", persParams.get("pers.IDReadMode"));
        return params.toJSONString();
    }

    /**
     * 设置人事参数
     * 
     * @auther lambert.li
     * @date 2018/12/3 17:10
     * @param persAppParamsItem
     * @return
     */
    @RequestMapping(value = "/setParams", method = RequestMethod.POST)
    public AppResultMessage setParams(@RequestBody PersAppParamsItem persAppParamsItem) {
        Map<String, String> persOldParams = persParamsService.getPersParams();
        Map<String, String> persParams = new HashMap<>();
        // pin最大长度
        if (StringUtils.isNotBlank(persAppParamsItem.getPinLen())) {
            long maxPinLen = persPersonService.getMaxPinLenth();
            int pinLen = Integer.parseInt(persAppParamsItem.getPinLen());
            if (pinLen < maxPinLen) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_PARAM_MAXPINLENGTH));
            }
            persParams.put("pers.pinLen", persAppParamsItem.getPinLen());
        }
        // pin是否支持字母
        if (StringUtils.isNotBlank(persAppParamsItem.getPinSupportLetter())) {
            Boolean pinSupportLetter = Boolean.parseBoolean(persAppParamsItem.getPinSupportLetter());
            if (StringUtils.isNotBlank(persAppParamsItem.getPinSupportIncrement())) {
                Boolean pinSupportIncrement = Boolean.parseBoolean(persAppParamsItem.getPinSupportIncrement());
                if (pinSupportIncrement && pinSupportLetter) {
                    // 人员编号支持自动递增，不能修改人员编号模式
                    if (Boolean.parseBoolean(persOldParams.get("pers.pinSupportIncrement"))) {
                        return AppResultMessage.failMessage()
                            .setRet(String.valueOf(PersConstants.PERSON_PARAM_CHANGEPINLETTERS));
                    } else {
                        // 人员编号支持包含字母，不能修改人员编号模式
                        return AppResultMessage.failMessage()
                            .setRet(String.valueOf(PersConstants.PERSON_PARAM_CHANGEPININCREMENT));
                    }
                }
            }
            // 不支持字母
            if (!pinSupportLetter) {
                int personCount = persPersonService.checkPinIsExistLetters();
                // 已有人员编号包含字母，不能更改人员编号模式。
                if (personCount > 0) {
                    return AppResultMessage.failMessage()
                        .setRet(String.valueOf(PersConstants.PERSON_PARAM_NOTCHANGEPIN));
                }
            }
            persParams.put("pers.pinSupportLetter", persAppParamsItem.getPinSupportLetter());
        }
        // pin是否默认
        if (StringUtils.isNotBlank(persAppParamsItem.getPinSupportDefault())) {
            persParams.put("pers.pinSupportDefault", persAppParamsItem.getPinSupportDefault());
        }
        // pin是否自动增长
        if (StringUtils.isNotBlank(persAppParamsItem.getPinSupportIncrement())) {
            if (StringUtils.isBlank(persAppParamsItem.getPinSupportLetter())) {
                Boolean pinSupportLetter = Boolean.parseBoolean(persOldParams.get("pers.pinSupportLetter"));
                Boolean pinSupportIncrement = Boolean.parseBoolean(persAppParamsItem.getPinSupportIncrement());
                // 人员编号支持自动递增，不能修改人员编号模式
                if (pinSupportIncrement && pinSupportLetter) {
                    return AppResultMessage.failMessage()
                        .setRet(String.valueOf(PersConstants.PERSON_PARAM_CHANGEPINLETTERS));
                }
            }
            persParams.put("pers.pinSupportIncrement", persAppParamsItem.getPinSupportIncrement());
        }
        // 是否支持多卡
        if (StringUtils.isNotBlank(persAppParamsItem.getCardsSupport())) {
            persParams.put("pers.cardsSupport", persAppParamsItem.getCardsSupport());
        }
        // 卡号长度，默认32位
        if (StringUtils.isNotBlank(persAppParamsItem.getCardLen())) {
            persParams.put("pers.cardLen", persAppParamsItem.getCardLen());
        }
        // 卡进制显示 0：为十进制、1：为十六进制
        if (StringUtils.isNotBlank(persAppParamsItem.getCardHex())) {
            String carHex = persOldParams.get("pers.cardHex");
            // 修改
            if (!carHex.equals(persAppParamsItem.getCardHex())) {
                long cardNum = persCardService.count();
                // 当前系统已存在卡号，不能更改卡格式显示。
                if (cardNum > 0) {
                    return AppResultMessage.failMessage()
                        .setRet(String.valueOf(PersConstants.PERSON_PARAM_HEXCHANGEWARN));
                }
                persParams.put("pers.cardHex", persAppParamsItem.getCardHex());
            }
        }
        // 卡号读取方式 1：控制器读头、2：ID180（读身份证物理卡号）
        if (StringUtils.isNotBlank(persAppParamsItem.getCardsReadMode())) {
            persParams.put("pers.cardsReadMode", persAppParamsItem.getCardsReadMode());
        }
        // 身份证读头方式
        if (StringUtils.isNotBlank(persAppParamsItem.getIDReadMode())) {
            persParams.put("pers.IDReadMode", persAppParamsItem.getIDReadMode());
        }
        persParamsService.saveItem(persParams);
        return AppResultMessage.successMessage();
    }

    @RequestMapping(value = "/getCardNoByPin", method = RequestMethod.POST)
    public String getCardNoByPin(@RequestBody JSONObject obj) {
        String pin = obj.getString("username");
        JSONObject json = new JSONObject();
        // 人员编号不能为空
        if (StringUtils.isNotBlank(pin)) {
            PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
            if (persPersonItem != null) {
                PersCardItem cardItem = persCardService.getMasterCardByPersonId(persPersonItem.getId());
                if (cardItem != null) {
                    String cardHex = persParamsService.getValByName("pers.cardHex");
                    String cardNo = cardItem.getCardNo();
                    if (StringUtils.isNotBlank(cardNo)) {
                        if (cardHex.equals(PersConstants.CARD_HEXADECIMAL)) {
                            cardNo = new BigInteger(cardNo, 16).toString(10);
                        }
                        json.put("cardNo", cardNo);
                    }
                }
            }
        }
        json.put("ret", "OK");
        return json.toString();
    }

    /**
     * 生成动态二维码
     * 
     * @param obj:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws @date 2020-11-13 16:07
     * @since 1.0.0
     */
    @RequestMapping(value = "/createEncryptedDynamicCode", method = RequestMethod.POST)
    public ZKResultMsg createEncryptedDynamicCode(@RequestBody JSONObject obj) {
        String pin = obj.getString("pin");
        // 人员编号不能为空
        String dynamicCode = persPersonService.createEncryptedDynamicCode(pin);
        if (StringUtils.isNotBlank(dynamicCode)) {
            ZKResultMsg msg = new ZKResultMsg();
            if (String.valueOf(PersConstants.PERSON_QRCODE_DISABLE).equals(dynamicCode)
                || String.valueOf(PersConstants.PERSON_QRCODE_NOCARD).equals(dynamicCode)
                || String.valueOf(PersConstants.PERSONID_NOTEXIST).equals(dynamicCode)) {
                msg.setRet(dynamicCode);
                return msg;
            }
            return new ZKResultMsg(dynamicCode);
        }
        return ZKResultMsg.failMsg("dynamic code encrypt fail");
    }
}