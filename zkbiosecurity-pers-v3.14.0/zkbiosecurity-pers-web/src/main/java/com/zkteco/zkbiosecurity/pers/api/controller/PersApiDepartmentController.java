package com.zkteco.zkbiosecurity.pers.api.controller;

import java.text.MessageFormat;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiDepartmentItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersApiDepartmentService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 部门接口
 * 
 * <AUTHOR>
 * @Date: 2018/11/9 11:00
 */
@Controller
@RequestMapping(value = {"/api/department"})
@Slf4j
@Api(tags = "PersDepartment ", description = "person department")
public class PersApiDepartmentController {

    @Autowired
    private PersApiDepartmentService persApiDepartmentService;

    /**
     * 新增/编辑部门
     * 
     * @auther lambert.li
     * @date 2018/11/26 14:37
     * @param department
     * @return
     */
    @ApiOperation(value = "Add Department", notes = "Create Or Update Department", response = ApiResultMessage.class)
    @RequestMapping(value = "/add", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin", "code", "name", "parentCode", "sortNo"})
    public ApiResultMessage add(@RequestBody PersApiDepartmentItem department) {
        ApiResultMessage rs = null;
        try {
            rs = persApiDepartmentService.updateDepartment(department);
        } catch (Exception e) {
            log.error("api department/add error ", e);
            rs = ApiResultMessage.failedMessage(ConstUtil.ESB_SEND_FAILURE);
        }
        return rs;
    }

    /**
     * 根据code删除部门
     * 
     * @auther lambert.li
     * @date 2018/11/26 14:37
     * @param code
     * @return
     */
    @ApiOperation(value = "Delete Department", notes = "Delete Department By Code", response = ApiResultMessage.class)
    @RequestMapping(value = "/delete/{code}", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"code"})
    public ApiResultMessage deleteByCode(@PathVariable String code) {
        ApiResultMessage rs = null;
        try {
            rs = persApiDepartmentService.deleteDeptByCode(code);
        } catch (ZKBusinessException e) {
            rs = ApiResultMessage.message(PersConstants.DEPT_DELETE_FAILED,
                MessageFormat.format(I18nUtil.i18nCode(e.getMessage()), e.getObjects()));
        } catch (Exception e) {
            log.error("api department/delete error ", e);
            rs = ApiResultMessage.failedMessage(ConstUtil.ESB_SEND_FAILURE);
        }
        return rs;
    }

    /**
     * 根据code获取部门
     * 
     * @auther lambert.li
     * @date 2018/11/26 14:38
     * @param
     * @return
     */
    @ApiOperation(value = "Get Department", notes = "Get Department By Code", response = ApiResultMessage.class)
    @RequestMapping(value = "/get/{code}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"code"})
    public ApiResultMessage getByCode(@PathVariable String code) {
        ApiResultMessage rs = null;
        try {
            rs = persApiDepartmentService.getDeptByCode(code);
        } catch (Exception e) {
            log.error("api department/get error ", e);
            rs = ApiResultMessage.failedMessage(ConstUtil.ESB_SEND_FAILURE);
        }
        return rs;
    }

    /**
     * 获取部门list
     *
     * @auther 31876
     * @date 18:12
     * @since 1.0.0
     */
    @ResponseBody
    @RequestMapping(value = "/getDepartmentList", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Get Department List By DeptCode", notes = "Return Departments List",
        response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"pins", "depCodes", "pageNo", "pageSize"})
    public ApiResultMessage getDepartmentList(@RequestParam(name = "pageNo") Integer pageNo,
        @RequestParam(name = "pageSize") Integer pageSize) {
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(PersConstants.PERSON_PAGE_NULL,
                I18nUtil.i18nCode("common_api_pageNotNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(PersConstants.PERSON_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(PersConstants.PERSON_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        Pager pager = persApiDepartmentService.getApiDepartmentByPage(pageNo, pageSize);
        return ApiResultMessage.successMessage(pager);
    }

}
