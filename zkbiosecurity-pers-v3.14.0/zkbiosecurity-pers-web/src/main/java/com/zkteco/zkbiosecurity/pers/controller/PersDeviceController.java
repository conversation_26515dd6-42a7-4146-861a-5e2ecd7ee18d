/*
 * @author:	GenerationTools
 * @date:	2018-03-02 下午02:15
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.pers.remote.PersDeviceRemote;
import com.zkteco.zkbiosecurity.pers.service.PersDeviceService;
import com.zkteco.zkbiosecurity.pers.vo.PersDeviceSelectItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;

/**
 * 考勤点添加门或停车设备
 *
 * @author: verber
 * @date:	2018-07-02 下午02:15
 */
@Controller
public class PersDeviceController extends ExportController implements PersDeviceRemote {

	@Autowired
	private PersDeviceService persDeviceService;

	@Override
	public DxGrid selectDeviceList(PersDeviceSelectItem condition) {
		if (StringUtils.isBlank(condition.getSelectId())){
			condition.setSelectId("-1");
		}
		if ("noSelected".equals(condition.getType())){
			condition.setNotInId(condition.getSelectId());
		}
		else if ("selected".equals(condition.getType())){
			condition.setInId(condition.getSelectId());
		}
		Pager pager = persDeviceService.getSelectDeviceItemByPage(condition, getPageNo(), getPageSize());
		return GridUtil.convert(pager, condition.getClass());
	}
}