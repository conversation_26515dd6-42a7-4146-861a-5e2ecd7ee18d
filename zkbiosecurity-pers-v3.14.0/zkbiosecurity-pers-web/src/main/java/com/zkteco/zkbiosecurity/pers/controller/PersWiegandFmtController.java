/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.math.BigInteger;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.remote.PersWiegandFmtRemote;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * @author: GenerationTools
 * @date: 2018-02-23 下午03:48
 */
@Controller
public class PersWiegandFmtController extends BaseController implements PersWiegandFmtRemote {
    @Autowired
    private PersWiegandFmtService persWiegandFmtService;
    @Autowired
    private PersParamsService persParamsService;

    @RequiresPermissions("pers:wiegandFmt")
    @Override
    public ModelAndView index() {
        return new ModelAndView("pers/wiegandFmt/persWiegandFmt");
    }

    @RequiresPermissions({"pers:wiegandFmt:edit", "pers:wiegandFmt:add"})
    @Override
    public ModelAndView edit(@RequestParam(value = "id", required = false) String id,
        @RequestParam(value = "cardFmt", required = false) String cardFmts,
        @RequestParam(value = "parityFmt", required = false) String parityFmts,
        @RequestParam(value = "wiegandCount", required = false) String wiegandCount,
        @RequestParam(value = "siteCode", required = false) String siteCodes) {
        PersWiegandFmtItem item = new PersWiegandFmtItem();
        if (StringUtils.isEmpty(id)) {
            if (StringUtils.isNotEmpty(cardFmts)) {
                item.setCardFmt(cardFmts);
                item.setParityFmt(parityFmts);
                item.setSiteCode(siteCodes);
                item.setWiegandCount(Short.valueOf(wiegandCount));
                // 检查推荐的格式是否和数据库中重复了
                List<PersWiegandFmtItem> list = persWiegandFmtService.getByCondition(item);
                if (list.size() > 0) {
                    item = list.get(0);
                }
            } else {
                request.setAttribute("siteCodeLen", persParamsService.getValByName("pers.siteCodeLen"));
                return new ModelAndView("pers/wiegandFmt/editPersWiegandFmt");
            }
        } else {
            item = persWiegandFmtService.getItemById(id);
        }
        request.setAttribute("item", item);

        String parityFmt = item.getParityFmt();
        String cardFmt = item.getCardFmt();
        int[] odd = getParity(parityFmt, PersConstants.ODD_PARITY);
        request.setAttribute("oStart", odd[0]);
        request.setAttribute("oCount", odd[1]);
        int[] even = getParity(parityFmt, PersConstants.EVEN_PARITY);
        request.setAttribute("eStart", even[0]);
        request.setAttribute("eCount", even[1]);
        int[] cid = getParity(cardFmt, PersConstants.CID);
        request.setAttribute("cStart", cid[0]);
        request.setAttribute("cCount", cid[1]);
        int[] siteCode = getParity(cardFmt, PersConstants.SITE_CODE);
        request.setAttribute("sStart", siteCode[0]);
        request.setAttribute("sCount", siteCode[1]);
        int[] fCode = getParity(cardFmt, PersConstants.FACILITY_CODE);
        request.setAttribute("fStart", fCode[0]);
        request.setAttribute("fCount", fCode[1]);
        int[] mCode = getParity(cardFmt, PersConstants.MANUFACTORY_CODE);
        request.setAttribute("mStart", mCode[0]);
        request.setAttribute("mCount", mCode[1]);
        int[] parity = getParity(cardFmt, PersConstants.ODD_OR_EVEN_PARITY);
        request.setAttribute("firstParity", parity[0]);
        request.setAttribute("secondParity", parity[1]);

        request.setAttribute("siteCodeLen", persParamsService.getValByName("pers.siteCodeLen"));
        return new ModelAndView("pers/wiegandFmt/editPersWiegandFmt");

    }

    @RequiresPermissions({"pers:wiegandFmt:edit", "pers:wiegandFmt:add"})
    @LogRequest(module = "pers_module", object = "pers_wiegandFmt", opType = "common_op_edit", requestParams = {"name"},
        opContent = "common_name")
    @Override
    public ZKResultMsg save(PersWiegandFmtItem item) {
        ZKResultMsg res = new ZKResultMsg();
        if (item.getIsDefaultFmt() == null) {
            item.setIsDefaultFmt(false);
        }
        if (item.getWiegandMode() == PersConstants.WG_DEFAULT_MODE) {
            String ret = wiegandFmtStr(item);
            String[] retArray = ret.split(":", 2);
            if (retArray.length == 2) {
                item.setCardFmt(retArray[0]);
                item.setParityFmt(retArray[1]);
            }
        }
        if (StringUtils.isEmpty(item.getSiteCode())) {
            item.setSiteCode("0");
        }
        persWiegandFmtService.saveItem(item);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("pers:wiegandFmt:refresh")
    @Override
    public DxGrid list(PersWiegandFmtItem condition) {
        condition.setAutoMode(PersConstants.WG_AUTO_MODE);
        Pager pager = persWiegandFmtService.getItemsByPage(condition, getPageNo(), getPageSize());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("pers:wiegandFmt:del")
    @LogRequest(module = "pers_module", object = "pers_wiegandFmt", opType = "common_op_del", requestParams = {"names"},
        opContent = "common_name")
    @Override
    public ZKResultMsg del(@RequestParam("ids") String ids) {
        persWiegandFmtService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    /**
     * 转换自定义韦根格式，返回组合后的字符串
     * 
     * @param wgFmt
     * @return pmmmmsssssssssssscccccccccccccccccccp:eeeeeeeeeeeeeeeeeeooooooooooooooooooo
     */
    public String wiegandFmtStr(PersWiegandFmtItem wgFmt) {
        String oStart = request.getParameter("oStart");// oddParityStart
        String oCount = request.getParameter("oCount");// oddParityCount
        String eStart = request.getParameter("eStart");// evenParityStart
        String eCount = request.getParameter("eCount");// evenParityCount
        String cStart = request.getParameter("cStart");// cidStart
        String cCount = request.getParameter("cCount");// cidCount
        // String fStart = request.getParameter("fStart");// facilityCodeStart
        // String fCount = request.getParameter("fCount");// facilityCodeCount
        String sStart = request.getParameter("sStart");// siteCodeStart
        String sCount = request.getParameter("sCount");// siteCodeCount
        String mStart = request.getParameter("mStart");// manufactoryCodeStart
        String mCount = request.getParameter("mCount");// manufactoryCodeCount
        String firstParity = request.getParameter("firstParity");// firstParity
        String secondParity = request.getParameter("secondParity");// secondParity

        char[] chr = new char[wgFmt.getWiegandCount()];// 组合字符用到的字符数组
        // 这个循环先使用填充字符
        for (int i = 0; i < wgFmt.getWiegandCount(); i++) {
            chr[i] = PersConstants.NOT_PARITY;
        }
        int oMin = -1;// 奇效验起始位
        int oMax = -1;// 奇效验结束位
        int eMin = -1;// 偶效验起始位
        int eMax = -1;// 偶效验结束位
        if (oStart != null && !"".equals(oStart) && oCount != null && !"".equals(oCount)) {
            oMin = Integer.parseInt(oStart) - 1;
            oMax = Integer.parseInt(oStart) + Integer.parseInt(oCount) - 2;
        }
        if (eStart != null && !"".equals(eStart) && eCount != null && !"".equals(eCount)) {
            eMin = Integer.parseInt(eStart) - 1;
            eMax = Integer.parseInt(eStart) + Integer.parseInt(eCount) - 2;
        }
        // 这个循环替换成奇效验、偶效验位数的字符
        for (int i = 0; i < wgFmt.getWiegandCount(); i++) {
            if (oMin <= i && i <= oMax) {
                chr[i] = PersConstants.ODD_PARITY;// 奇效验使用字符 o
            }
            if (eMin <= i && i <= eMax) {
                chr[i] =
                    chr[i] == PersConstants.ODD_PARITY ? PersConstants.ODD_AND_EVEN_PARITY : PersConstants.EVEN_PARITY;
            }
        }
        // 奇效验、偶效验组合字符串，将字符数组转成字符串
        String oddEvenStr = new String(chr);

        for (int i = 0; i < wgFmt.getWiegandCount(); i++) {
            chr[i] = PersConstants.NOT_PARITY;
        }

        int cMin = -1;// CID起始位
        int cMax = -1;// CID结束位
        // int fMin = -1;// 公司码起始位
        // int fMax = -1;// 公司码结束位
        int sMin = -1;
        int sMax = -1;
        int mMin = -1;
        int mMax = -1;
        if (cStart != null && !"".equals(cStart) && cCount != null && !"".equals(cCount)) {
            cMin = Integer.parseInt(cStart) - 1;
            cMax = Integer.parseInt(cStart) + Integer.parseInt(cCount) - 2;
        }
        if (sStart != null && !"".equals(sStart) && sCount != null && !"".equals(sCount)) {
            sMin = Integer.parseInt(sStart) - 1;
            sMax = Integer.parseInt(sStart) + Integer.parseInt(sCount) - 2;
        }
        /*if(fStart != null && !"".equals(fStart) && fCount != null && !"".equals(fCount)) 
        {
        	fMin = Integer.parseInt(fStart) - 1;
        	fMax = Integer.parseInt(fStart) + Integer.parseInt(fCount) - 2;
        }*/
        if (mStart != null && !"".equals(mStart) && mCount != null && !"".equals(mCount)) {
            mMin = Integer.parseInt(mStart) - 1;
            mMax = Integer.parseInt(mStart) + Integer.parseInt(mCount) - 2;
        }

        // 这个循环替换成CID、公司码位数的字符
        for (int i = 0; i < wgFmt.getWiegandCount(); i++) {
            if (cMin <= i && i <= cMax) {
                chr[i] = PersConstants.CID;// CID使用 c
            }
            /*if (fMin <= i && i <= fMax) 
            {
            	chr[i] = PersConstants.FACILITY_CODE;// 公司码使用 f
            }*/
            if (sMin <= i && i <= sMax) {
                chr[i] = PersConstants.SITE_CODE;// 区位码 使用 s
            }
            if (mMin <= i && i <= mMax) {
                chr[i] = PersConstants.MANUFACTORY_CODE;// 厂商代码 使用 f
            }
        }

        short fParity = (firstParity != null && !"".equals(firstParity)) ? Short.parseShort(firstParity) : 0;
        short sParity = (secondParity != null && !"".equals(secondParity)) ? Short.parseShort(secondParity) : 0;

        if (fParity != 0 && fParity <= wgFmt.getWiegandCount()) {
            chr[fParity - 1] = PersConstants.ODD_OR_EVEN_PARITY;
        }
        if (sParity != 0 && sParity <= wgFmt.getWiegandCount()) {
            chr[sParity - 1] = PersConstants.ODD_OR_EVEN_PARITY;
        }
        // CID、公司码组合字符串，将字符数组转成字符串
        String cidCompStr = new String(chr);

        // 返回结果
        return cidCompStr + ":" + oddEvenStr;
    }

    /**
     * 获取校验位：起始位、长度
     * 
     * @param fmt
     * @param chr
     * @return
     */
    public int[] getParity(String fmt, char chr) {
        String str = String.valueOf(chr);
        int[] tempArray = new int[2];
        if (PersConstants.ODD_OR_EVEN_PARITY == chr) {
            tempArray[0] = fmt.indexOf(str) + 1;
            tempArray[1] = fmt.lastIndexOf(str) + 1;
        } else {
            tempArray[0] = fmt.indexOf(str) + 1;
            int length = fmt.lastIndexOf(str) - fmt.indexOf(str);
            if (fmt.lastIndexOf(str) == -1) {
                tempArray[1] = 0;
            } else {
                tempArray[1] = length > 0 ? length + 1 : 1;
            }
            if (PersConstants.ODD_PARITY == chr || PersConstants.EVEN_PARITY == chr) {
                int bStart = fmt.indexOf(PersConstants.ODD_AND_EVEN_PARITY) + 1;
                int bLength =
                    fmt.lastIndexOf(PersConstants.ODD_AND_EVEN_PARITY) - fmt.indexOf(PersConstants.ODD_AND_EVEN_PARITY);
                if (bLength > 0) {
                    tempArray[1] = tempArray[1] + bLength + 1;
                    if (tempArray[0] > bStart) {
                        tempArray[0] = tempArray[0] - (bLength + 1);
                    }
                }
            }
        }
        return tempArray;
    }

    @Override
    public String isExist(String name) {
        boolean rs = !persWiegandFmtService.existsByName(name);
        return rs + "";
    }

    @Override
    public ZKResultMsg checkSiteCode(String siteCode, String siteCodeCount) {
        ZKResultMsg rsm = new ZKResultMsg();
        boolean ret = true;
        BigInteger siteCodeNum = new BigInteger(siteCode);
        BigInteger maxSiteCode = new BigInteger("2").pow(Integer.parseInt(siteCodeCount)).subtract(new BigInteger("1"));
        if (siteCodeNum.compareTo(maxSiteCode) > 0) {
            ret = false;
        }
        rsm.setRet(String.valueOf(ret));
        return I18nUtil.i18nMsg(rsm);
    }

    @Override
    public boolean isExistAcc() {
        return persWiegandFmtService.isExistAcc();
    }
}