/*
 * Project Name: zkbiosecurity-demo-web
 * File Name: DemoDepartmentDyna.java
 * Copyright: Copyright(C) 1985-2018 ZKTeco Inc. All rights reserved.
 */
package com.zkteco.zkbiosecurity.pers.web.extend;

import com.zkteco.zkbiosecurity.base.bean.GridColumnItem;
import com.zkteco.zkbiosecurity.core.utils.DynamicColumn;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * @author: juvenile.li
 * @date: 2018年4月18日 早上10:15:36
 */
@Component
public class PersPersonDyna implements DynamicColumn {

	@Autowired
	private PersAttributeService persAttributeService;

	@Override
	public List<GridColumnItem> getColumn(String fieldName) {
		List<GridColumnItem> columns = new ArrayList<GridColumnItem>();
		List<PersAttributeItem> attributes = persAttributeService.getItemsByShow();
		attributes.forEach(item ->{
			GridColumnItem c1 = new GridColumnItem();
			String attrName = I18nUtil.i18nCode("pers_attr_"+item.getAttrName());
			if(StringUtils.isNotBlank(attrName) && !attrName.contains("pers_attr_")) {
				c1.setLabel(attrName);
			}else{
				c1.setLabel(item.getAttrName());
			}
			c1.setName("attrValue"+item.getFiledIndex());
			c1.setColumnType("ro");
			c1.setWidth("100");
            c1.setFieldName(fieldName);
			columns.add(c1);
		});
		return columns;
	}
}
