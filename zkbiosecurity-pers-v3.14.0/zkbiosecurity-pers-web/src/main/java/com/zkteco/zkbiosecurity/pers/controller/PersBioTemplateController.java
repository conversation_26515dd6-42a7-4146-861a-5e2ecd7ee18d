/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.multipart.MultipartFile;

import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.ExcelUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.remote.PersBioTemplateRemote;
import com.zkteco.zkbiosecurity.pers.service.PersBioTemplateService;
import com.zkteco.zkbiosecurity.pers.util.ItemUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 * 人员生物模版Controller
 */
@Controller
public class PersBioTemplateController extends ExportController implements PersBioTemplateRemote {
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private ProgressCache progressCache;

    @RequiresPermissions("pers:bioTemplate:import")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_import_biologicalTemplate",
        requestParams = {}, opContent = "pers_import_biologicalTemplate")
    @Override
    public ZKResultMsg importExcel(MultipartFile upload) {
        Map<String, Map<String, String>> convertMap = new HashMap<>();
        // 生物类型
        Map<String, String> bioTypeMap = new HashMap<>();
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_universal"), "0");
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_regFinger"), "1");
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_infraredFace"), "2");
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_voice"), "3");
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_iris"), "4");
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_retina"), "5");
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_palmPrints"), "6");
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_regVein"), "7");
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_metacarpalVein"), "8");
        bioTypeMap.put(I18nUtil.i18nCode("pers_person_visibleFace"), "9");
        convertMap.put("bioTypeVal", bioTypeMap);
        // 是否有效
        Map<String, String> validTypeMap = new HashMap<>();
        validTypeMap.put(I18nUtil.i18nCode("pers_card_disabled"), "0");
        validTypeMap.put(I18nUtil.i18nCode("pers_card_effect"), "1");
        convertMap.put("validType", validTypeMap);
        // 是否胁迫
        Map<String, String> duressMap = new HashMap<>();
        duressMap.put(I18nUtil.i18nCode("common_yes"), "true");
        duressMap.put(I18nUtil.i18nCode("common_no"), "false");
        convertMap.put("duress", duressMap);
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));

            List<PersBioTemplateItem> itemList =
                ExcelUtil.excelImport(upload.getInputStream(), PersBioTemplateItem.class, convertMap);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            ZKResultMsg res = persBioTemplateService.importData(itemList);
            return I18nUtil.i18nMsg(res);
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import Bio Template Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }

    }

    @RequiresPermissions("pers:bioTemplate:export")
    @Override
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_export_personBioTemplate",
        opContent = "pers_export_personBioTemplate")
    public void export() {
        PersBioTemplateItem bioTemplateItem = new PersBioTemplateItem();
        setConditionValue(bioTemplateItem);
        List<PersBioTemplateItem> intemList = (List<PersBioTemplateItem>)persBioTemplateService
            .getItemData(bioTemplateItem, request.getSession().getId(), getBeginIndex(), getEndIndex());
        // 设置 jsonColumn
        Map<String, Map<String, String>> map = new HashMap<>();
        Map<String, String> jsonColumn = new HashMap<>();
        jsonColumn.put("jsonColumn", ItemUtil.itemFiledToJsonColumn(bioTemplateItem));
        map.put("jsonColumn", jsonColumn);
        excelExport(intemList, PersBioTemplateItem.class, map);
    }
}