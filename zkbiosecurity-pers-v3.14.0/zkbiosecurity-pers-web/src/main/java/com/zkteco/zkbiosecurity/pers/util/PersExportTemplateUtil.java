package com.zkteco.zkbiosecurity.pers.util;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.ExcelUtil;

/**
 * @Auther: alex.zhang
 * @Date: 2020/7/23 14:05
 * @Description: 下载导入模板通用方法
 */
@Component
public class PersExportTemplateUtil {

    private final static Logger logger = LoggerFactory.getLogger(PersExportTemplateUtil.class);

    public final static String EXPORT_RESULT = "export-result:";
    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;
    @Value("${security.session.timeout:1800}")
    private Long sessionTimeout;

    public <T> void templateExport(HttpServletRequest request, HttpServletResponse response,
        Map<String, String> commentMap) {
        String reportType = request.getParameter("reportType");
        if (StringUtils.isBlank(reportType)) {
            reportType = ExcelUtil.XLSX;
        }
        String fileNamePrefix = request.getParameter("tableNameParam");
        JSONObject jsonObject = JSONObject.parseObject(request.getParameter("jsonColumn"), Feature.OrderedField);
        if (jsonObject == null) {
            jsonObject = JSONObject.parseObject(commentMap.get("jsonColumn"), Feature.OrderedField);
        }
        reportType = reportType.toLowerCase();
        try {
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet sheet = workbook.createSheet(fileNamePrefix);
            sheet.setDefaultColumnWidth(jsonObject.size());
            HSSFPatriarch patriarch = sheet.createDrawingPatriarch();
            HSSFCellStyle style = workbook.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);
            HSSFFont font = workbook.createFont();
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setAlignment(HorizontalAlignment.CENTER);
            font.setFontHeightInPoints((short)14);// 设置字体大小
            style.setFont(font);

            // XSSFDrawing
            // 创建表头
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            cell.setCellValue(fileNamePrefix);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, jsonObject.size() - 1));
            cell.setCellStyle(style);
            row.setHeightInPoints(20);
            row.setHeight((short)(27 * 20));

            row = sheet.createRow(1);
            row.setHeightInPoints(20);
            row.setHeight((short)(27 * 20));

            int i = 0;
            for (String str : jsonObject.keySet()) {
                sheet.setColumnWidth(i, 150 * 36);
                cell = row.createCell(i);
                cell.setCellValue(jsonObject.getString(str));// 表头，字段名
                cell.setCellStyle(style);
                if (commentMap.containsKey(str)) {
                    String commentStr = commentMap.get(str);
                    HSSFComment comment =
                        patriarch.createCellComment(new HSSFClientAnchor(0, 0, 0, 0, (short)1, 2, (short)4, 8));
                    comment.setString(new HSSFRichTextString(commentStr));// 批注内容
                    cell.setCellComment(comment);
                }
                i += 1;
            }

            /*----------页面reponse设置----------*/
            response.setCharacterEncoding("UTF-8");
            String agent = request.getHeader("User-Agent");
            boolean isMSIE = (agent != null && (agent.indexOf("MSIE") != -1 || agent.indexOf("Trident") != -1));
            // IE浏览器
            if (isMSIE) {
                fileNamePrefix = java.net.URLEncoder.encode(fileNamePrefix, "UTF-8");
                fileNamePrefix = fileNamePrefix.replaceAll("\\+", " "); // IE会将空格转成+号
            } else {
                fileNamePrefix = new String(fileNamePrefix.getBytes("UTF-8"), "ISO-8859-1");
            }
            if (request.getHeader("User-Agent") != null && request.getHeader("User-Agent").indexOf("Edge") != -1) {// Edge浏览器兼容处理
                fileNamePrefix = fileNamePrefix.replaceAll(" ", "%20");// 空格预处理
                fileNamePrefix = URLEncoder.encode(fileNamePrefix, "ISO-8859-1");// 编码转换
                fileNamePrefix = fileNamePrefix.replace("%2520", " ");// 空格还原
            }
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileNamePrefix + "_"
                + (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date()) + "." + reportType.toLowerCase() + "\"");
            response.setContentType("application/vnd.ms-excel");
            OutputStream outStream = response.getOutputStream();
            workbook.write(outStream);
        } catch (Exception e) {
            logger.error(fileNamePrefix + " export error", e);
            response.setHeader("Content-Disposition", "");
            response.setContentType("application/json");
            throw new ZKBusinessException("common_report_exportFaild");
        } finally {
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
            stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
        }
    }
}
