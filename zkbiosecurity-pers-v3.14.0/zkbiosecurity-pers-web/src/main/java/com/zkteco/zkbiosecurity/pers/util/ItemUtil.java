package com.zkteco.zkbiosecurity.pers.util;

import com.alibaba.fastjson.JSON;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;

import java.lang.reflect.Field;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 14:00 2018/11/19
 */
public class ItemUtil {
    public static String itemFiledToJsonColumn(BaseItem baseItem){
        Class itemCls = baseItem.getClass();
        Field[] fields = itemCls.getDeclaredFields();
        Map map = new LinkedHashMap();
        for (Field field : fields) {
            GridColumn column = field.getAnnotation(GridColumn.class);
            if(Objects.nonNull(column)){
                if (!("#language!='zh_CN'".equals(column.showExpression()) && "zh_CN".equals(LocaleMessageSourceUtil.language)))
                {
                    map.put(field.getName(), I18nUtil.i18nCode(column.label()));
                }
            }
        }
        return JSON.toJSONString(map);
    }
}
