package com.zkteco.zkbiosecurity.pers.controller;

import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.remote.PersBasicDataRemote;
import com.zkteco.zkbiosecurity.pers.service.PersBasicDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR>
 * @Date: 2019/7/2 14:08
 */
@RequestMapping(value = {"/api/persBasicDataUpload"})
@Controller
public class PersBasicDataController extends BaseController implements PersBasicDataRemote {
    @Autowired
    private PersBasicDataService persBasicDataService;


    @Override
    @RequestMapping(value = "/syncAllPersonToCloud", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public ZKResultMsg syncAllPersonToCloud() {
        return I18nUtil.i18nMsg(persBasicDataService.syncAllPersonToCloud());
    }

    @Override
    @RequestMapping(value = "/syncAllPersonFromCloud", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public ZKResultMsg syncAllPersonFromCloud() {
        return I18nUtil.i18nMsg(persBasicDataService.syncAllPersonFromCloud());
    }
}
