/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;

import javax.imageio.ImageIO;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.remote.PersCardPrintTemplateRemote;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeExtService;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeService;
import com.zkteco.zkbiosecurity.pers.service.PersCardPrintTemplateService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeExtItem;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * @author: GenerationTools
 * @date: 2018-02-23 下午03:48
 */
@Controller
public class PersCardPrintTemplateController extends BaseController implements PersCardPrintTemplateRemote {
    @Autowired
    private PersCardPrintTemplateService persCardPrintTemplateService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersAttributeService persAttributeService;
    @Autowired
    private PersAttributeExtService persAttributeExtService;
    private BufferedImage buffImage = null;// 背景图
    private Graphics2D g = null;
    private Font font = new Font("Arial,微软雅黑", Font.PLAIN, 24);// 添加字体的属性设置
    private int x = 0;// x坐标
    private int y = 0;// y坐标

    @Override
    public ZKResultMsg getPersonData(@RequestParam("personId") String personId) {
        ZKResultMsg msg = new ZKResultMsg();
        JSONObject person = new JSONObject();
        JSONObject printData = new JSONObject();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        PersPersonItem persPersonItem = persPersonService.getItemById(personId);
        if (persPersonItem != null) {
            String perName = persPersonItem.getName() == null ? "" : persPersonItem.getName();
            String perLastName = persPersonItem.getLastName() == null ? "" : persPersonItem.getLastName();
            person.put("name", perName + " " + perLastName);
            person.put("pin", persPersonItem.getPin());
            person.put("gender", persPersonItem.getGender() == null ? "" : persPersonItem.getGender());
            person.put("entryDate",
                persPersonItem.getHireDate() == null ? "" : sdf.format(persPersonItem.getHireDate()));
            person.put("department", persPersonItem.getDeptName() == null ? "" : persPersonItem.getDeptName());
            person.put("mobilephone", persPersonItem.getMobilePhone() == null ? "" : persPersonItem.getMobilePhone());
            person.put("email", persPersonItem.getEmail() == null ? "" : persPersonItem.getEmail());
            // 图片解密设置base64数据
            String photoBase64 = "";
            if (StringUtils.isNotBlank(persPersonItem.getPhotoPath())) {
                photoBase64 = FileEncryptUtil.getDecryptFileBase64(persPersonItem.getPhotoPath());
                // 设置base64数据
                photoBase64 = PersConstants.PERSON_BASE64_PREFIX + photoBase64;
            }
            person.put("photo", photoBase64);
            person.put("birthday", persPersonItem.getBirthday() == null ? "" : persPersonItem.getBirthday());
            person.put("cardNo", persPersonItem.getCardNos() == null ? "" : persPersonItem.getCardNos());
            person.put("positionName",
                persPersonItem.getPositionName() == null ? "" : persPersonItem.getPositionName());

            PersAttributeExtItem extItem = persAttributeExtService.getItemByPersonId(personId);
            Map attr = null;
            if (Objects.nonNull(extItem)) {
                attr = BeanMap.create(extItem);
            }

            List<PersAttributeItem> attributes = persAttributeService.getByCondition(new PersAttributeItem());
            for (PersAttributeItem item : attributes) {
                if (Objects.nonNull(attr)) {
                    person.put("attrValue" + item.getFiledIndex(),
                        MapUtils.getString(attr, "attrValue" + item.getFiledIndex(), ""));
                }
            }
            printData.put("person", person);
        }
        msg.setData(printData);
        return msg;
    }

    @Override
    public ZKResultMsg getPicTemplate(@RequestParam(value = "templateId") String templateId,
        @RequestParam(value = "personId") String personId) {
        Map<String, String> imgBase64Map = new HashMap<>();
        Map<String, String> templateMap = persCardPrintTemplateService.getPrintTemplate(templateId);
        if (templateMap != null) {
            String imgBase64Str = "";
            String templateWidth = templateMap.get("width");
            String templateHeight = templateMap.get("height");
            String frontData = templateMap.get("frontData");
            String oppositeData = templateMap.get("oppositeData");
            Map<String, String> tranMap = persCardPrintTemplateService.getPersonData(personId);
            JSONArray frontJsonArray = JSONArray.parseArray(frontData);
            imgBase64Str = getCardTemplate(Integer.parseInt(templateWidth), Integer.parseInt(templateHeight),
                frontJsonArray, tranMap);
            imgBase64Map.put("front", imgBase64Str);
            JSONArray oppositeJsonArray = JSONArray.parseArray(oppositeData);
            imgBase64Str = getCardTemplate(Integer.parseInt(templateWidth), Integer.parseInt(templateHeight),
                oppositeJsonArray, tranMap);
            imgBase64Map.put("opposite", imgBase64Str);
            imgBase64Map.put("visEmpPin", tranMap.get("printData.visTransaction.visEmpPin"));
        }
        return new ZKResultMsg(imgBase64Map);
    }

    private String getCardTemplate(int templateWidth, int templateHeight, JSONArray jsonArrayData,
        Map<String, String> personMap) {
        int left = 0, top = 0, width = 0, height = 0, fontSize = 0;
        String leftPoint = "";
        String topPoint = "";
        String colorStr = "";
        String fontSizeStr = "";
        Color color = new Color(0, 0, 0);
        List<Integer> colors = new ArrayList<Integer>();
        // 获取Graphics2D对象
        this.buffImage = new BufferedImage(templateWidth, templateHeight, BufferedImage.TYPE_INT_RGB);
        this.g = buffImage.createGraphics();
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, buffImage.getWidth(), buffImage.getHeight());// 填充整个屏幕
        String imgBase64Str = "";
        JSONArray bgArray = new JSONArray();
        for (int i = 0; i < jsonArrayData.size(); i++) {
            JSONObject data = jsonArrayData.getJSONObject(i);
            // 先绘制背景图片，避免背景图片后面绘制覆盖已绘制的元素
            if ("background".equals(data.getString("printType"))) {
                if (StringUtils.isNotBlank(data.getString("printValue"))) {
                    BufferedImage b = loadImageLocal(data.getString("printValue"));
                    if (b != null) {
                        g.drawImage(b, 0, 0, buffImage.getWidth(), buffImage.getHeight(), null);
                    }
                }
                bgArray.add(data);
            }
        }
        jsonArrayData.removeAll(bgArray);
        for (int i = 0; i < jsonArrayData.size(); i++) {
            JSONObject data = jsonArrayData.getJSONObject(i);
            leftPoint = data.getString("leftPoint");
            left = Double.valueOf(leftPoint.substring(0, leftPoint.length() - 2)).intValue();
            topPoint = data.getString("topPoint");
            top = Double.valueOf(topPoint.substring(0, topPoint.length() - 2)).intValue();
            fontSizeStr = data.getString("fontSize");
            fontSize = Integer.parseInt(fontSizeStr.substring(0, fontSizeStr.length() - 2)) * 2;
            width = Double.valueOf(data.getString("width")).intValue();
            height = Double.valueOf(data.getString("height")).intValue();
            colorStr = data.getString("color");
            String[] cArray = colorStr.split(",");
            for (String c : cArray) {
                colors.add(Integer.parseInt(c.trim()));
            }
            color = new Color(colors.get(0), colors.get(1), colors.get(2));
            switch (data.getString("printType")) {
                case "photo":
                    String decryFilePath = personMap.get(data.getString("cell"));
                    String photoPath = decryFilePath.replaceAll("\\\\", "/");
                    // 生成解密图片
                    byte[] decPhoto = FileEncryptUtil.getDecryptFile(photoPath);
                    if (decPhoto != null) {
                        String fileName = photoPath.substring(photoPath.lastIndexOf("/") + 1);
                        decryFilePath = FileUtils.saveByte2Image(ConstUtil.SYSTEM_MODULE_PERS,
                            "printTemplate/cardPrintImages/decrypt", fileName, decPhoto);
                    }
                    BufferedImage d = loadImageLocal(decryFilePath);// 导入人员图片
                    if (d != null) {
                        d = imageZoom(d, width * 2, height * 2);
                        modifyImagetogeter(d, left * 2, top * 2);// 绘制人员图片
                    }
                    if (decPhoto != null) {
                        // 删除临时文件
                        FileUtils.deleteFile(FileUtil.getLocalFullPath(decryFilePath));
                    }
                    break;
                case "images":
                    BufferedImage imgs = loadImageLocal(data.getString("cell"));
                    if (imgs != null) {
                        imgs = imageZoom(imgs, width * 2, height * 2);
                        modifyImagetogeter(imgs, left * 2, top * 2);
                    }
                    break;
                case "line":
                    this.g.setColor(color);
                    g.drawLine(left * 2, top * 2, (left + width) * 2, (top + height) * 2);
                    break;
                case "txt":
                    this.g.setColor(color);
                    modifyImage(data.getString("cell"), left * 2, top * 2 + 25, data.getString("fontFamily"), fontSize);
                    break;
                case "text":
                    this.g.setColor(color);
                    modifyImage(personMap.get(data.getString("cell")), left * 2, top * 2 + 25,
                        data.getString("fontFamily"), fontSize);
                    break;

                default:
                    break;
            }
        }
        imgBase64Str = saveImage(System.currentTimeMillis() + ".jpg");

        return imgBase64Str.replaceAll("[\\s*\t\n\r]", "");
    }

    /**
     * 导入本地图片到缓冲区
     *
     * @param imgName
     * @return java.awt.image.BufferedImage
     * <AUTHOR>
     * @date 2019/2/13 10:33
     */
    private BufferedImage loadImageLocal(String imgName) {
        try {
            if (new File(FileUtil.systemFilePath + File.separator + imgName).exists()) {
                return ImageIO.read(new File(FileUtil.systemFilePath + File.separator + imgName));
            } else if (this.getClass().getResource("/static" + imgName) != null) {
                return ImageIO.read(this.getClass().getResource("/static" + imgName).openStream());
            }
        } catch (IOException e) {
            System.out.println(e.getMessage());
        }
        return null;
    }

    private void setFont(String fontStyle, int fontSize) {
        this.font = new Font("Arial,微软雅黑", Font.PLAIN, fontSize);
    }

    private void modifyImagetogeter(BufferedImage d, int x, int y) {
        int w = d.getWidth();
        int h = d.getHeight();
        g.drawImage(d, x, y, w, h, null);
    }

    private BufferedImage imageZoom(BufferedImage d, int newWidth, int newHeight) {
        int w = d.getWidth();
        int h = d.getHeight();
        float wRatio = (float)w / newWidth;// 新旧宽比率
        float hRatio = (float)h / newHeight;// 新旧高比率
        float newRatio = (float)newWidth / newHeight;// 新图宽高比率
        if (wRatio > hRatio)// 宽度比例大于高度比率
        {
            d = d.getSubimage((int)(w - h * newRatio) / 2, 0, (int)(h * newRatio), h);// 左右切图
        } else {
            d = d.getSubimage(0, (int)(h - w / newRatio) / 2, w, (int)(w / newRatio));//// 上下切图
        }

        BufferedImage r = new BufferedImage(newWidth, newHeight, BufferedImage.TYPE_INT_RGB);
        r.getGraphics().drawImage(d.getScaledInstance(newWidth, newHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);
        return r;
    }

    /**
     * 修改图片,返回修改后的图片缓冲区（只输出一行文本）
     *
     * @param content, x, y, fontStyle, fontSize
     * @return void
     * <AUTHOR>
     * @date 2019/2/13 10:45
     */
    private void modifyImage(Object content, int x, int y, String fontStyle, int fontSize) {
        setFont(fontStyle, fontSize);
        g.setFont(this.font);
        // 验证输出位置的纵坐标和横坐标
        this.x = x;
        this.y = y;
        if (content != null) {
            g.drawString(content.toString(), this.x, this.y);
        }
    }

    private String saveImage(String newImage) {
        String imgBase64Str = "";
        if (buffImage != null) {
            try {
                String xmlPath = FileUtil.uploadFilePath + FileUtil.separator + "pers" + FileUtil.separator
                    + "printTemplate/cardPrintImages" + FileUtil.separator;
                String fullPath = FileUtil.systemFilePath + File.separator + xmlPath;
                File outputfile = new File(new File(fullPath), newImage);
                if (!outputfile.getParentFile().exists()) {
                    outputfile.getParentFile().mkdirs();
                } else if (outputfile.exists()) {
                    outputfile.delete();
                }
                ImageIO.write(buffImage, "jpg", outputfile);
                imgBase64Str = ImgEncodeUtil.encodeBase64(outputfile.getAbsolutePath());
                g.dispose();
                g = null;
                buffImage.flush();
                buffImage = null;
                // 删除临时文件
                FileUtils.deleteFile(outputfile.getAbsolutePath());
            } catch (IOException e) {
                System.out.println(e.getMessage());
            }
        }
        return imgBase64Str;
    }

    @Override
    public Boolean isShowCardPrint() {
        return persCardPrintTemplateService.isShowCardPrint();
    }
}