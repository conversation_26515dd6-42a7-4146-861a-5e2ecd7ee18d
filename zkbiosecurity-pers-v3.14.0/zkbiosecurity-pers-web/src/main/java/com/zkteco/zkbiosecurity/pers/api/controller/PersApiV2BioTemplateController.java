package com.zkteco.zkbiosecurity.pers.api.controller;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiBioTemplateItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.Pers2OtherService;
import com.zkteco.zkbiosecurity.pers.service.PersBioTemplateService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * 人员生物模板接口
 * 
 * <AUTHOR>
 * @Date: 2018/11/9 14:50
 */

@Controller
@RequestMapping(value = {"/api/v2/bioTemplate"})
@Slf4j
@Api(tags = "PersBioTemplate", description = "person bioTemplate")
public class PersApiV2BioTemplateController {

    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private Pers2OtherService pers2OtherService;

    /**
     * 按Pin查询人员指纹模板接口
     * 
     * <AUTHOR>
     * @since 2019-09-16 16:52
     * @Param [pin]
     * @return
     */
    @ApiOperation(value = "Get BioTemplate List", notes = "Get BioTemplate List", response = ApiResultMessage.class)
    @RequestMapping(value = "/getFgListByPin", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage list(@ApiParam(name = "pin", value = "pin", required = true) @RequestParam String pin) {
        ApiResultMessage rs = ApiResultMessage.successMessage();
        List<PersApiBioTemplateItem> apiBioTemplateList = new ArrayList<PersApiBioTemplateItem>();
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem != null) {
            List<PersBioTemplateItem> persBioTemplateList =
                persBioTemplateService.getItemByPersonId(persPersonItem.getId());
            for (PersBioTemplateItem persBioTemplate : persBioTemplateList) {
                apiBioTemplateList.add(PersApiBioTemplateItem.createBioTemplate(persBioTemplate));
            }
            rs.setData(apiBioTemplateList);
        } else {
            rs = ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        return rs;
    }

    /**
     * 删除人员对应指纹模板接口
     * 
     * <AUTHOR>
     * @since 2019-09-16 16:54
     * @Param [pin, templateNo]
     * @return
     */
    @ApiOperation(value = "Delete BioTemplate By pin And templateNo",
        notes = "Delete BioTemplate By pin And templateNo", response = ApiResultMessage.class)
    @ResponseBody
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json")
    @ApiLogRequest(requestParams = {"pin", "templateNo"})
    public ApiResultMessage delete(@ApiParam(name = "pin", value = "pin") @RequestParam String pin,
        @ApiParam(name = "templateNo", value = "templateNo") @RequestParam Short templateNo) {
        ApiResultMessage rs = ApiResultMessage.successMessage();
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem != null) {
            int result = persBioTemplateService.deleteBioTemplateByCondition(persPersonItem, templateNo,
                BaseConstants.BaseBioType.FP_BIO_TYPE);
            if (result == 0) {
                rs = ApiResultMessage.message(PersConstants.PERSON_TEMPLATENO_NOTEXIST,
                    I18nUtil.i18nCode("pers_api_dataNotExist"));
            }
        } else {
            rs = ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        return rs;
    }

    /**
     * 删除人员所有指纹模板接口
     * 
     * <AUTHOR>
     * @since 2019-09-17 14:16
     * @Param [pin]
     * @return
     */
    @ApiOperation(value = "deleteByPin BioTemplate", notes = "deleteById BioTemplate", response = ResultMessage.class)
    @ResponseBody
    @RequestMapping(value = "/deleteByPin", method = RequestMethod.POST, produces = "application/json")
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage deleteAll(@ApiParam(name = "pin", value = "pin") @RequestParam String pin) {
        ApiResultMessage rs = new ApiResultMessage();
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem != null) {
            persBioTemplateService.deleteByPersonPinAndBioType(pin, BaseConstants.BaseBioType.FP_BIO_TYPE);
            List<Short> bioType = new ArrayList<>();
            bioType.add(BaseConstants.BaseBioType.FP_BIO_TYPE);
            // 只删除人员指纹
            pers2OtherService.deletePersonBioTemplate(StrUtil.strToList(persPersonItem.getId()), bioType);
        } else {
            rs = ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        return rs;
    }
}
