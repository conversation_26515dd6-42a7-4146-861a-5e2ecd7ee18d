package com.zkteco.zkbiosecurity.pers.app.controller;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.AppUserResetPasswordItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPhotoItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersAppService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * app 接口
 *
 * <AUTHOR>
 * @date 2023-11-27 18:18
 * @since 1.0.0
 */
@RestController
@RequestMapping(path = "/app/v2/user")
public class PersAppPersonV2Controller {

    @Autowired
    private PersPersonService persPersonService;

    @Autowired
    protected HttpServletRequest request;
    @Autowired
    private PersAppService persAppService;

    /**
     * 修改密码
     * 
     * @param appUserResetPasswordItem:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-11-27 17:41
     * @since 1.0.0
     */
    @RequestMapping(path = "/changePassword", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public AppResultMessage changePassword(@RequestBody AppUserResetPasswordItem appUserResetPasswordItem,
        @RequestBody JSONObject data) {
        PersPersonItem item = null;
        if (StringUtils.isNotBlank(appUserResetPasswordItem.getPin())) {
            item = persPersonService.getItemByPin(appUserResetPasswordItem.getPin());
        } else {
            item = persPersonService.getItemByPin(request.getParameter("user_name"));
        }
        String newPassword = appUserResetPasswordItem.getNewPassword();
        String oldPassword = appUserResetPasswordItem.getOldPassword();
        if (oldPassword.equals(newPassword)) {
            return AppResultMessage.message(PersConstants.API_PERS_PWDISREPETITION,
                I18nUtil.i18nCode("pers_h5_pwdIsRepetition"));
        }
        boolean isSucceed = persPersonService.changeSelfPwd(item.getPin(), oldPassword, newPassword, true);
        if (isSucceed) {
            return AppResultMessage.successMessage();
        }
        return AppResultMessage.message(PersConstants.API_PERS_OLDPWDISERROR,
            I18nUtil.i18nCode("pers_h5_oldPwdIsError"));
    }

    /**
     * 根据人员姓名、部门模糊查询人员
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2024-07-01 15:53
     * @since 1.0.0
     */
    @RequestMapping(value = "/getPersonByNameOrDeptName", method = RequestMethod.POST)
    public AppResultMessage getPersonByNameOrDeptName(@RequestBody JSONObject data) {
        String filter = data.getString("filter");
        int pageNo = data.containsKey("pageNo") ? data.getIntValue("pageNo") : 1;
        int pageSize = data.containsKey("pageSize") ? data.getIntValue("pageSize") : 20;
        PersPersonItem condition = new PersPersonItem();
        return persAppService.getPersonByNameOrDeptName(condition, filter, pageNo, pageSize);
    }

    /**
     * 更新人员信息（目前只更新人员照片）
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2024-07-17 17:43
     * @since 1.0.0
     */
    @RequestMapping(value = "/updatePersonInfo", method = RequestMethod.POST)
    public AppResultMessage updatePersonInfo(@RequestBody PersApiPhotoItem person) {
        String userName = request.getParameter("user_name");
        if (StringUtils.isBlank(person.getPin())) {
            person.setPin(userName);
        }
        return persAppService.updatePersonInfo(person);
    }

    /**
     * 获取员工列表
     * 
     * @param data:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2025-04-17 10:37
     * @since 1.0.0
     */
    @RequestMapping(value = "/getPersonList", method = RequestMethod.POST)
    public AppResultMessage getPersonList(@RequestBody JSONObject data) {
        String loginType = data.getString("loginType");
        // 页码
        int pageNo = data.containsKey("pageNo") ? data.getIntValue("pageNo") : 1;
        // 每页记录数
        int pageSize = data.containsKey("pageSize") ? data.getIntValue("pageSize") : 20;
        String name = data.getString("name");
        String userName = request.getParameter("user_name");
        PersPersonItem persPersonItem = new PersPersonItem();
        if (StringUtils.isNotBlank(name)) {
            persPersonItem.setName(name);
        }
        return persAppService.getPersonList(persPersonItem, pageNo - 1, pageSize, userName, loginType);
    }
}
