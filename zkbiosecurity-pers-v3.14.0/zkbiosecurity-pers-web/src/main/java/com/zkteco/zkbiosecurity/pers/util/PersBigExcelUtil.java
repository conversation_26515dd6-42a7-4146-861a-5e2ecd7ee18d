package com.zkteco.zkbiosecurity.pers.util;

import java.io.BufferedOutputStream;
import java.io.OutputStream;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.SpringContextUtil;
import com.zkteco.zkbiosecurity.core.web.bean.ExportParameterItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonExportItem;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryValueItem;

/**
 * <AUTHOR>
 * @date 2021/12/2 17:57
 * @since 1.0.0
 */
public class PersBigExcelUtil {
    private static final Logger log = LoggerFactory.getLogger(PersBigExcelUtil.class);

    public static boolean isBigExport() {
        String count = SpringContextUtil.getApplicationContext().getEnvironment().getProperty("system.maxExportCount");
        if (!StringUtils.isEmpty(count)) {
            return Integer.parseInt(count) > 30000;
        } else {
            return false;
        }
    }

    public static void export(List<PersPersonExportItem> data, Class<PersPersonExportItem> cls, String[] attrs,
        String fileName, OutputStream outputStream) throws Exception {
        long start = System.currentTimeMillis();
        if (isBigExport()) {
            try {
                ExportParameterItem<PersPersonExportItem> params =
                    convertToExportParameter(data, cls, fileName, outputStream);
                exportXls(params, attrs);
            } catch (Exception var18) {
                throw new ZKBusinessException("common_op_failed");
            } finally {
                if (outputStream != null) {
                    outputStream.flush();
                    outputStream.close();
                }

            }
        } else {
            BufferedOutputStream ops = new BufferedOutputStream(outputStream, 8192);
            try {
                HSSFWorkbook workbook = generateExcelAndFillData(data, attrs, fileName);
                workbook.write(ops);
            } catch (Exception var20) {
                throw new ZKBusinessException("common_op_failed");
            } finally {
                if (ops != null) {
                    ops.flush();
                    ops.close();
                }
                if (outputStream != null) {
                    outputStream.flush();
                    outputStream.close();
                }

            }
        }
        log.info("Export data cost " + (System.currentTimeMillis() - start) + "ms");
    }

    /**
     * 大数据人员导出
     * 
     * @param params:
     * @param attrs:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-12-07 16:17
     * @since 1.0.0
     */
    private static void exportXls(ExportParameterItem<PersPersonExportItem> params, String[] attrs) throws Exception {
        createHeaderForXls(params, attrs);
        createBodyForXls(params, attrs);
        params.getBook().write(params.getOutputStream());
        params.getBook().close();
    }

    private static ExportParameterItem<PersPersonExportItem> convertToExportParameter(List<PersPersonExportItem> data,
        Class<PersPersonExportItem> cls, String fileName, OutputStream outputStream) {
        ExportParameterItem<PersPersonExportItem> params = new ExportParameterItem();
        params.setData(data);
        params.setCls(cls);
        params.setTableName(fileName);
        params.setOutputStream(outputStream);
        return params;
    }

    /**
     * 设置excel表头
     * 
     * @param params:
     * @param attrs:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-12-07 16:18
     * @since 1.0.0
     */
    private static void createHeaderForXls(ExportParameterItem<PersPersonExportItem> params, String[] attrs) {
        SXSSFWorkbook book = new SXSSFWorkbook(1000);
        book.setCompressTempFiles(true);
        SXSSFSheet sheet = book.createSheet(params.getTableName());
        CellStyle headStyle = getHeadCellStyle(book);
        CellStyle dataStyle = getDataCellStyle(book);
        params.setBook(book);
        params.setSheet(sheet);
        params.setHeadStyle(headStyle);
        params.setDataStyle(dataStyle);
        int rowNum = 2;
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(params.getTableName().replaceAll("\\+", " "));
        titleCell.setCellStyle(params.getHeadStyle());
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, attrs.length - 1));
        Drawing drawing = sheet.createDrawingPatriarch();
        CreationHelper factory = book.getCreationHelper();
        ClientAnchor anchor = factory.createClientAnchor();
        anchor.setCol1(1);
        anchor.setCol2(4);
        anchor.setRow1(2);
        anchor.setRow2(8);
        anchor.setDx1(0);
        anchor.setDx2(0);
        anchor.setDy1(0);
        anchor.setDy2(0);
        Row headRow = sheet.createRow(1);
        Map<String, String> commentMap = getPersAttributeComment();
        for (int i = 0; i < attrs.length; ++i) {
            String[] attr = attrs[i].split("-");
            attr[0] = attr[0].replaceAll("\\+", " ");
            Cell cell = createCellForXls(sheet, headRow, i, attr[0], params.getDataStyle());
            String commentStr = I18nUtil.i18nCode("pers_export_templateCommentName", attr[1]);
            String attrComment = commentMap.get(attr[1]);
            if (!StringUtils.isEmpty(attrComment)) {
                commentStr += attrComment;
            }
            Comment comment = drawing.createCellComment(anchor);
            RichTextString str = factory.createRichTextString(commentStr);
            comment.setString(str);
            // Set the row and column here
            comment.setRow(cell.getRowIndex());
            comment.setColumn(cell.getColumnIndex());
            // Assign the comment to the cell
            cell.setCellComment(comment);
        }
        params.setHeadRowNumber(rowNum);
        sheet.createFreezePane(0, rowNum, 0, rowNum);
    }

    /**
     * 设置excel数据
     * 
     * @param params:
     * @param attrs:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-12-07 16:18
     * @since 1.0.0
     */
    private static void createBodyForXls(ExportParameterItem<PersPersonExportItem> params, String[] attrs)
        throws Exception {
        List<PersPersonExportItem> data = params.getData();
        Sheet sheet = params.getSheet();
        if (Objects.nonNull(data) && data.size() > 0) {
            for (int i = 0; i < data.size(); ++i) {
                PersPersonExportItem item = data.get(i);
                Row row = sheet.createRow(params.getHeadRowNumber() + i);
                for (int j = 0; j < attrs.length; ++j) {
                    String param = attrs[j].split("-")[1];
                    String[] paramArray = param.split("\\.");
                    Object paramValue = null;
                    if (paramArray.length < 2)// 没有关联属性，直接反射
                    {
                        paramValue = PersExportInfoUtil.getFieldValue(item, param);
                    } else {
                        if (item.getAttributeExt() != null) {
                            paramValue = PersExportInfoUtil.getFieldValue(item.getAttributeExt(), paramArray[1]);
                        }
                    }
                    String value = paramValue != null ? paramValue.toString().replaceAll(",", "&") : "";
                    createCellForXls(sheet, row, j, value, params.getDataStyle());
                }
            }
        }
    }

    /**
     * 设置表头样式
     * 
     * @param book:
     * @return org.apache.poi.ss.usermodel.CellStyle
     * <AUTHOR>
     * @throws @date 2021-12-07 16:18
     * @since 1.0.0
     */
    private static CellStyle getHeadCellStyle(Workbook book) {
        CellStyle headStyle = book.createCellStyle();
        Font font = book.createFont();
        font.setFontName("ARIAL");
        font.setFontHeightInPoints((short)10);
        font.setBold(true);
        headStyle.setFont(font);
        headStyle.setBorderBottom(BorderStyle.THIN);
        headStyle.setBorderLeft(BorderStyle.THIN);
        headStyle.setBorderTop(BorderStyle.THIN);
        headStyle.setBorderRight(BorderStyle.THIN);
        headStyle.setAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return headStyle;
    }

    /**
     * 设置数据列样式
     * 
     * @param book:
     * @return org.apache.poi.ss.usermodel.CellStyle
     * <AUTHOR>
     * @throws @date 2021-12-07 16:19
     * @since 1.0.0
     */
    private static CellStyle getDataCellStyle(Workbook book) {
        CellStyle dataStyle = book.createCellStyle();
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        dataStyle.setWrapText(true);
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        return dataStyle;
    }

    /**
     * 设置数据列数据
     * 
     * @param sheet:
     * @param row:
     * @param colInd:
     * @param attr:
     * @param style:
     * @return org.apache.poi.ss.usermodel.Cell
     * <AUTHOR>
     * @throws @date 2021-12-07 16:19
     * @since 1.0.0
     */
    private static Cell createCellForXls(Sheet sheet, Row row, int colInd, String attr, CellStyle style) {
        Cell cell = row.createCell(colInd);
        sheet.setColumnWidth(colInd, 150 * 36);
        cell.setCellValue(attr);// 表头，字段名
        cell.setCellStyle(style);
        return cell;
    }

    /**
     * 获取excel批注内容
     * 
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @throws @date 2021-12-07 16:20
     * @since 1.0.0
     */
    public static Map<String, String> getPersAttributeComment() {
        Map<String, String> map = new LinkedHashMap<>();

        BaseDictionaryValueService baseDictionaryValueService =
            SpringContextUtil.getBean(BaseDictionaryValueService.class);

        List<BaseDictionaryValueItem> certTypeList = baseDictionaryValueService.getDictionaryValues("certificateType");
        StringBuilder certName = new StringBuilder();
        for (BaseDictionaryValueItem certType : certTypeList) {
            certName.append(certType.getDictValue()).append(",");
        }
        // 性别的国际化，M男F女
        List<BaseDictionaryValueItem> genderList = baseDictionaryValueService.getDictionaryValues("sex");
        StringBuilder genderStr = new StringBuilder();
        for (BaseDictionaryValueItem gender : genderList) {
            genderStr.append(I18nUtil.i18nCode(gender.getDictValue())).append(",");
        }
        map.put("pin",
            " " + I18nUtil.i18nCode("common_jqMsg_required") + "\r\n" + I18nUtil.i18nCode("pers_person_pinFirstValid"));
        map.put("deptCode",
            " " + I18nUtil.i18nCode("common_jqMsg_required") + "\r\n" + I18nUtil.i18nCode("pers_export_dataExist"));
        map.put("deptName", "\r\n" + I18nUtil.i18nCode("pers_export_dataExist"));
        map.put("positionCode", "\r\n" + I18nUtil.i18nCode("pers_export_dataExist"));
        map.put("positionName", "\r\n" + I18nUtil.i18nCode("pers_export_dataExist"));
        map.put("certName",
            "\r\n" + I18nUtil.i18nCode("pers_cert_type") + ":" + certName.substring(0, certName.length() - 1));
        map.put("cardNos", "\r\n" + I18nUtil.i18nCode("pers_import_cardNoStartWithZero") + "\r\n"
            + I18nUtil.i18nCode("pers_export_cardNoTip"));
        map.put("birthday", "\r\n" + I18nUtil.i18nCode("base_datetime_timeFormat") + ":yyyy-MM-dd");
        map.put("certNumber", "\r\n" + I18nUtil.i18nCode("pers_export_certNumberComment"));
        map.put("carPlate", "\r\n" + I18nUtil.i18nCode("pers_carNumber_importTip"));
        map.put("gender",
            "\r\n" + I18nUtil.i18nCode("pers_person_gender") + ":" + genderStr.substring(0, genderStr.length() - 1));
        map.put("buildingName", "\r\n" + I18nUtil.i18nCode("pers_export_dataExist"));
        map.put("unitName", "\r\n" + I18nUtil.i18nCode("pers_export_dataExist"));
        return map;
    }

    /**
     * 非大数据人员导出
     * 
     * @param data:
     * @param attrs:
     * @param fileName:
     * @return org.apache.poi.hssf.usermodel.HSSFWorkbook
     * <AUTHOR>
     * @throws @date 2021-12-07 16:20
     * @since 1.0.0
     */
    public static HSSFWorkbook generateExcelAndFillData(List<PersPersonExportItem> data, String[] attrs,
        String fileName) throws Exception {
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFSheet sheet = workbook.createSheet(fileName);
        sheet.setDefaultColumnWidth(attrs.length);
        HSSFPatriarch patriarch = sheet.createDrawingPatriarch();

        HSSFCellStyle style = workbook.createCellStyle();
        style.setAlignment(HorizontalAlignment.CENTER);
        HSSFFont font = workbook.createFont();
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setAlignment(HorizontalAlignment.CENTER);
        font.setFontHeightInPoints((short)14);// 设置字体大小
        style.setFont(font);
        style.setWrapText(true);
        // XSSFDrawing
        // 创建表头
        HSSFRow row = sheet.createRow(0);
        HSSFCell cell = row.createCell(0);
        cell.setCellValue(fileName);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, attrs.length - 1));
        cell.setCellStyle(style);
        row.setHeightInPoints(20);
        row.setHeight((short)(27 * 20));

        row = sheet.createRow(1);
        row.setHeightInPoints(20);
        row.setHeight((short)(27 * 20));

        for (int i = 0; i < attrs.length; i++) {
            sheet.setColumnWidth(i, 150 * 36);
            cell = row.createCell(i);
            String[] attr = attrs[i].split("-");
            attr[0] = attr[0].replaceAll("\\+", " ");
            cell.setCellValue(attr[0]);// 表头，字段名
            cell.setCellStyle(style);

            String commentStr = I18nUtil.i18nCode("pers_export_templateCommentName", attr[1]);
            Map<String, String> commentMap = getPersAttributeComment();
            String attrComment = commentMap.get(attr[1]);
            if (!StringUtils.isEmpty(attrComment)) {
                commentStr += attrComment;
            }
            HSSFComment comment =
                patriarch.createCellComment(new HSSFClientAnchor(0, 0, 0, 0, (short)1, 2, (short)4, 8));
            comment.setString(new HSSFRichTextString(commentStr));// 批注内容
            cell.setCellComment(comment);
        }
        if (Objects.nonNull(data) && data.size() > 0) {
            for (PersPersonExportItem person : data) {

                int lastRowNum = sheet.getLastRowNum();// 获取excel表格当前的行数
                row = sheet.createRow(lastRowNum + 1);// 创建excel表格的下一行
                for (int i = 0; i < attrs.length; i++) {
                    cell = row.createCell(i);// 创建前端字段相应个数的表格
                    String param = attrs[i].split("-")[1];
                    String[] paramArray = param.split("\\.");
                    Object paramValue = null;
                    if (paramArray.length < 2)// 没有关联属性，直接反射
                    {
                        paramValue = PersExportInfoUtil.getFieldValue(person, param);
                    } else {
                        if (person.getAttributeExt() != null) {
                            paramValue = PersExportInfoUtil.getFieldValue(person.getAttributeExt(), paramArray[1]);
                        }
                    }
                    cell.setCellValue(paramValue != null ? paramValue.toString().replaceAll(",", "&") : "");
                    cell.setCellStyle(style);// 设置表格样式
                }
            }
        }
        return workbook;
    }

}