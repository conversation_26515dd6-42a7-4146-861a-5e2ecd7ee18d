package com.zkteco.zkbiosecurity.pers.dhx;

import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.ShowGridColumn;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.vo.PersTempPersonItem;

@Component
public class PersShowView implements ShowGridColumn {

    @Override
    public boolean isShow(Object persTempPersonItem) {
        if (((PersTempPersonItem)persTempPersonItem).getStatus() == PersConstants.TEMPPERSON_STATUS_APPROVED) {
            return true;
        }
        return false;
    }
}
