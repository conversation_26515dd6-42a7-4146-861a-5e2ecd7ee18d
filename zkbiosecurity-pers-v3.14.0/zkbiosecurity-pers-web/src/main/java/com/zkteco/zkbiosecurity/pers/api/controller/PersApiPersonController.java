package com.zkteco.zkbiosecurity.pers.api.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.ApiPersonBaseInfoItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPhotoItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersApiPersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 人员接口
 * 
 * <AUTHOR>
 * @Date: 2018/11/8 09:14
 */

@Controller
@RequestMapping(value = {"/api/person"})
@Slf4j
@Api(tags = "Person", description = "person")
public class PersApiPersonController {

    @Autowired
    private PersApiPersonService persApiPersonService;
    @Autowired
    private PersPersonService persPersonService;

    /**
     * 新增/编辑人员
     * 
     * @auther lambert.li
     * @date 2018/11/9 10:55
     * @param person
     * @return
     */
    @ApiOperation(value = "Add Person", notes = "Create Or Update Person", response = ApiResultMessage.class)
    @RequestMapping(value = "/add", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"accEndTime", "accLevelIds", "accStartTime", "birthday", "carPlate", "cardNo",
        "certNumber", "certType", "deptCode", "email", "gender", "hireDate", "isDisabled", "isSendMail", "lastName",
        "leaveId", "mobilePhone", "name", "personPhoto", "pin", "ssn", "supplyCards"})
    public ApiResultMessage addPerson(@RequestBody PersApiPersonItem person) {
        ApiResultMessage rs = null;
        try {
            rs = persApiPersonService.addApiPerson(person);
        } catch (Exception e) {
            log.error("api person/add error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR, e.getMessage());
        }
        return rs;
    }

    /**
     * 根据pin删除人员
     * 
     * @auther lambert.li
     * @date 2018/11/9 10:55
     * @param pin
     * @return
     */
    @ApiOperation(value = "Delete Person", notes = "Delete Person By Pin", response = ApiResultMessage.class)
    @RequestMapping(value = "/delete/{pin}", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage deleteByPin(@PathVariable String pin) {
        ApiResultMessage rs = new ApiResultMessage();
        if (StringUtils.isBlank(pin)) {
            // pin不能为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        try {
            PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
            if (persPersonItem == null) {
                return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                    I18nUtil.i18nCode("pers_api_personNotExist"));
            }
            persPersonService.deleteByIds(persPersonItem.getId());
        } catch (ZKBusinessException e) {
            rs = ApiResultMessage.message(PersConstants.PERSON_DELETE_FAILED, I18nUtil.i18nCode(e.getMessage()));
        } catch (Exception e) {
            log.error("api person/delete error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 根据pin获取人员信息
     * 
     * @auther lambert.li
     * @date 2018/11/9 10:56
     * @param pin
     * @return
     */
    @ApiOperation(value = "Get Person", notes = "Get Person By Pin", response = ApiResultMessage.class)
    @RequestMapping(value = "/get/{pin}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage getByPin(@PathVariable String pin) {
        ApiResultMessage rs = null;
        if (StringUtils.isBlank(pin)) {
            // pin不能为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        try {
            rs = persApiPersonService.getApiPersonByPin(pin);
        } catch (Exception e) {
            log.error("api person/get error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 根据pin获取动态二维码
     * 
     * @param pin:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2021-09-28 11:40
     * @since 1.0.0
     */
    @ApiOperation(value = "Get Dynamic QR code", notes = "Get Dynamic QR code By Pin",
        response = ApiResultMessage.class)
    @RequestMapping(value = "/getQrCode/{pin}", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage getQrCodeByPin(@PathVariable String pin) {
        ApiResultMessage rs = new ApiResultMessage();
        if (StringUtils.isBlank(pin)) {
            // pin不能为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        try {
            rs = persApiPersonService.getQrCodeByPin(pin);
        } catch (ZKBusinessException e) {
            rs = ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST, I18nUtil.i18nCode(e.getMessage()));
        } catch (Exception e) {
            log.error("api person/getQrCode error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    @ResponseBody
    @RequestMapping(value = "/leave", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Leave Person", notes = "Leave Person", response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"leaveDate", "leaveType", "pin"})
    public ApiResultMessage leave(@RequestBody PersApiLeavePersonItem persApiLeavePersonItem) {
        ApiResultMessage rs = ApiResultMessage.successMessage();
        try {
            if (StringUtils.isBlank(persApiLeavePersonItem.getPin())) {
                return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                    I18nUtil.i18nCode("pers_import_pinNotEmpty"));
            } else if (StringUtils.isBlank((persApiLeavePersonItem.getLeaveType()))) {
                return ApiResultMessage.message(PersConstants.PERSON_LEAVETYPE_ISNULL,
                    I18nUtil.i18nCode("pers_dimission_leaveType_noNull"));
            } else if (persApiLeavePersonItem.getLeaveDate() == null) {
                return ApiResultMessage.message(PersConstants.PERSON_LEAVEDATE_ISNULL,
                    I18nUtil.i18nCode("pers_dimission_date_noNull"));
            }
            rs = persApiPersonService.leaveApiPerson(persApiLeavePersonItem);
        } catch (ZKBusinessException e) {
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR, I18nUtil.i18nCode(e.getMessage()));
        } catch (Exception e) {
            log.error("api person/leave person error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 更新人员基础信息
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2021-11-16 14:00
     * @since 1.0.0
     */
    @ApiOperation(value = "Add Personnel Basic Information", notes = "Create Or Update Person",
        response = ApiResultMessage.class)
    @RequestMapping(value = "/addPersonnelBasicInfo", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"birthday", "cardNo", "certNumber", "certType", "deptCode", "email", "gender",
        "hireDate", "isSendMail", "lastName", "mobilePhone", "name", "pin", "ssn", "supplyCards"})
    public ApiResultMessage addPersonnelBasicInfo(@RequestBody ApiPersonBaseInfoItem person) {
        ApiResultMessage rs = null;
        try {
            rs = persApiPersonService.addPersonnelBasicInfo(person);
        } catch (Exception e) {
            log.error("api person/add error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 更新人员照片
     *
     * @param person:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2021-11-16 14:00
     * @since 1.0.0
     */
    @ApiOperation(value = "Update Personnel Photo", notes = "Update Personnel Photo", response = ApiResultMessage.class)
    @RequestMapping(value = "/updatePersonnelPhoto", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"personPhoto", "pin"})
    public ApiResultMessage updatePersonnelPhoto(@RequestBody PersApiPhotoItem person) {
        ApiResultMessage rs = null;
        try {
            rs = persApiPersonService.updatePersonnelPhoto(person);
        } catch (Exception e) {
            log.error("api person/add error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    @ResponseBody
    @RequestMapping(value = "/getPersonList", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Get Person List By PinList And DeptCode", notes = "Return Persons List",
        response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"pins", "depCodes", "pageNo", "pageSize"})
    public ApiResultMessage getPersonList(@RequestParam(name = "pins", required = false) String pins,
        @RequestParam(name = "deptCodes", required = false) String deptCodes,
        @RequestParam(name = "pageNo") Integer pageNo, @RequestParam(name = "pageSize") Integer pageSize) {
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(PersConstants.PERSON_PAGE_NULL,
                I18nUtil.i18nCode("common_api_pageNotNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(PersConstants.PERSON_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(PersConstants.PERSON_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        List<PersApiPersonItem> apiPersons = persApiPersonService.getApiPersonList(pins, deptCodes, pageNo, pageSize);
        return ApiResultMessage.successMessage(apiPersons);
    }

    /**
     * 人员复职
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-03-28 15:05
     * @since 1.0.0
     */
    @ApiOperation(value = "Reinstated", notes = "Reinstated Person", response = ApiResultMessage.class)
    @RequestMapping(value = "/reinstated", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"accEndTime", "accLevelIds", "accStartTime", "birthday", "carPlate", "cardNo",
        "certNumber", "certType", "deptCode", "email", "gender", "hireDate", "isDisabled", "isSendMail", "lastName",
        "leaveId", "mobilePhone", "name", "personPhoto", "pin", "ssn", "supplyCards"})
    public ApiResultMessage reinstatedPerson(@RequestBody PersApiPersonItem person) {
        ApiResultMessage rs = null;
        try {
            rs = persApiPersonService.reinstatedApiPerson(person);
        } catch (Exception e) {
            log.error("api person/reinstated error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR, e.getMessage());
        }
        return rs;
    }
}