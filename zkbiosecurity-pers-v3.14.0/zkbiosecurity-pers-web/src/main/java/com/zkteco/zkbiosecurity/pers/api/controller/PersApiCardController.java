package com.zkteco.zkbiosecurity.pers.api.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiCardItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersCardService;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 人员卡接口
 * 
 * <AUTHOR>
 * @Date: 2018/11/9 14:50
 */

@Controller
@RequestMapping(value = {"/api/card"})
@Slf4j
@Api(tags = "PersCard", description = "person card")
public class PersApiCardController {

    @Autowired
    private PersCardService persCardService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersParamsService persParamsService;

    /**
     * 设置人员卡号
     * 
     * @auther lambert.li
     * @date 2018/11/26 14:37
     * @param card
     * @return
     */
    @ApiOperation(value = "Set Card To Person", notes = "Return Result Object", response = ApiResultMessage.class)
    @RequestMapping(value = "/set", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin", "cardNo", "cardType", "pin"})
    public ApiResultMessage setCardToPerson(@RequestBody PersApiCardItem card) {
        ApiResultMessage rs = ApiResultMessage.successMessage();
        try {
            if (StringUtils.isBlank(card.getCardNo())) {
                return ApiResultMessage.message(PersConstants.PERSON_CART_EMPTY,
                    I18nUtil.i18nCode("pers_issueCard_cardEmptyNote"));
            }
            String cardsSupport = persParamsService.getValByName("pers.cardsSupport");
            if ("false".equals(cardsSupport) && String.valueOf(PersConstants.DEPUTY_CARD).equals(card.getCardType())) {
                return ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                    I18nUtil.i18nCode("pers_api_cardsPersSupport"));
            }
            PersPersonItem person = persPersonService.getItemByPin(card.getPin());
            if (person == null) {
                return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                    I18nUtil.i18nCode("pers_api_personNotExist"));
            }
            int ret = persCardService.isExitCardNo(person, card.getCardNo());
            if (ret < 0) {
                // 卡号重复
                return ApiResultMessage.message(PersConstants.CART_HASUSE, I18nUtil.i18nCode("pers_person_cardDuress"));
            }
            if (!String.valueOf(PersConstants.MAIN_CARD).equals(card.getCardType())
                && !String.valueOf(PersConstants.DEPUTY_CARD).equals(card.getCardType())) {
                // 卡号重复
                return ApiResultMessage.message(PersConstants.PERSON_CART_TYPEERROR,
                    I18nUtil.i18nCode("pers_api_cardTypeError"));
            }
            if (String.valueOf(PersConstants.MAIN_CARD).equals(card.getCardType())) {
                persCardService.saveMasterCard(card.getPin(), card.getCardNo());
            } else if (String.valueOf(PersConstants.DEPUTY_CARD).equals(card.getCardType())) {
                List<PersCardItem> persCardItems = persCardService.getItemByPersonId(person.getId());
                PersCardItem persCardItem = new PersCardItem();
                persCardItem.setPersonPin(card.getPin());
                persCardItem.setCardType(Short.valueOf(card.getCardType()));
                persCardItem.setCardNo(card.getCardNo());
                // TODO 暂时调用保存多卡，需要通知其他模块
                persCardItems.add(persCardItem);
                persCardService.saveMultiCard(persCardItems);
            }
        } catch (Exception e) {
            log.error("api card/set error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 获取人员卡号
     * 
     * @auther lambert.li
     * @date 2018/11/26 14:37
     * @param pin
     * @return
     */
    @ApiOperation(value = "Get Card List By Pin", notes = "Return Card List", response = ApiResultMessage.class)
    @RequestMapping(value = "/getCards/{pin}", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage getCardsByPin(@PathVariable String pin) {
        ApiResultMessage rs = ApiResultMessage.successMessage();
        try {
            if (StringUtils.isBlank(pin)) {
                return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                    I18nUtil.i18nCode("pers_import_pinNotEmpty"));
            }
            PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
            if (persPersonItem == null) {
                return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                    I18nUtil.i18nCode("pers_api_personNotExist"));
            }
            List<PersCardItem> persCardItems = persCardService.getItemByPersonId(persPersonItem.getId());
            if (persCardItems != null && !persCardItems.isEmpty()) {
                List<PersApiCardItem> apiCardItems = Lists.newArrayList();
                persCardItems.forEach(persCardItem -> {
                    PersApiCardItem persApiCardItem = PersApiCardItem.createCard(persCardItem);
                    if (persApiCardItem != null) {
                        apiCardItems.add(persApiCardItem);
                    }
                });
                rs.setData(apiCardItems);
            }
        } catch (Exception e) {
            log.error("api card/getCards error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }
}
