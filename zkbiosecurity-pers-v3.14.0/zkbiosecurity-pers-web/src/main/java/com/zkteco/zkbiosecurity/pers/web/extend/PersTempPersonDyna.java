package com.zkteco.zkbiosecurity.pers.web.extend;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.bean.GridColumnItem;
import com.zkteco.zkbiosecurity.core.utils.DynamicColumn;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * <AUTHOR>
 * @DATE 2020-06-09 10:05
 * @since 1.0.0
 */
@Component
public class PersTempPersonDyna implements DynamicColumn {
    @Autowired
    private PersAttributeService persAttributeService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public List<GridColumnItem> getColumn(String fieldName) {
        List<GridColumnItem> columns = new ArrayList<GridColumnItem>();

        // 启用健康申报,临时人员列表会显示防疫使用的自定义属性
        String enableHealthInfo = baseSysParamService.getValByName("pers.enableHealthInfo");
        if ("1".equals(enableHealthInfo)) {
            PersAttributeItem codition = new PersAttributeItem();
            // 显示的数据
            codition.setSqlStr("initHep");
            List<PersAttributeItem> attributes = persAttributeService.getByCondition(codition);
            attributes.forEach(item -> {
                GridColumnItem c1 = new GridColumnItem();
                c1.setLabel(item.getAttrName());
                c1.setName("attrValue" + item.getFiledIndex());
                c1.setColumnType("ro");
                c1.setWidth("100");
                c1.setFieldName(fieldName);
                columns.add(c1);
            });
        }
        return columns;
    }
}
