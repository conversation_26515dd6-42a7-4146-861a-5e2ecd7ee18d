/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.pers.remote.PersLeavePersonRemote;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;

/**
 *
 * @author: GenerationTools
 * @date: 2018-02-23 下午03:48
 */
@Controller
public class PersLeavePersonController extends ExportController implements PersLeavePersonRemote {
    @Autowired
    private PersLeavePersonService persLeavePersonService;

    @RequiresPermissions("pers:leavePerson")
    @Override
    public ModelAndView index() {
        return new ModelAndView("pers/leavePerson/persLeavePerson");
    }

    @RequiresPermissions({"pers:leavePerson:edit", "pers:person:leave"})
    @Override
    public ModelAndView edit(@RequestParam(value = "id", required = false) String id,
        @RequestParam(value = "ids", required = false) String ids,
        @RequestParam(value = "pins", required = false) String pins,
        @RequestParam(value = "names", required = false) String names) {
        if (StringUtils.isNotBlank(id)) {
            request.setAttribute("item", persLeavePersonService.getItemById(id));
            request.setAttribute("pin", persLeavePersonService.getItemById(id).getPin());
            request.setAttribute("name", persLeavePersonService.getItemById(id).getName());
        }
        if (StringUtils.isNotBlank(ids)) {
            request.setAttribute("ids", ids);
            request.setAttribute("pin", pins);
            request.setAttribute("name", names);
        }
        return new ModelAndView("pers/leavePerson/editPersLeavePerson");
    }

    @RequiresPermissions({"pers:leavePerson:edit", "pers:person:leave"})
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_leave", requestParams = {"pin", "name"},
        opContent = "pers_person_pin")
    @Override
    public ZKResultMsg save(PersLeavePersonItem item,
        @RequestParam(value = "personIds", required = false) String personIds) {
        ZKResultMsg res = new ZKResultMsg();
        if (StringUtils.isNoneBlank(item.getId())) {
            persLeavePersonService.saveItem(item);
        } else {
            persLeavePersonService.batchLeave(item, personIds);
        }
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("pers:leavePerson")
    @Override
    public DxGrid list(PersLeavePersonItem condition) {
        Pager pager = persLeavePersonService.loadPagerByAuthUserFilter(request.getSession().getId(), condition,
            getPageNo(), getPageSize());
        persLeavePersonService.protectPin((List<PersLeavePersonItem>)pager.getData());
        return GridUtil.convert(pager, condition.getClass());
    }

    @RequiresPermissions("pers:leavePerson:del")
    @LogRequest(module = "pers_module", object = "pers_leave", opType = "common_op_del", requestParams = {"pins"},
        opContent = "pers_person_pin")
    @Override
    public ZKResultMsg del(@RequestParam("ids") String ids) {
        persLeavePersonService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("pers:leavePerson:export")
    @Override
    public void export(HttpServletRequest req, HttpServletResponse resp) {
        String exportType = request.getParameter("exportType");
        String recordStart = request.getParameter("recordstart");
        String recordCount = request.getParameter("recordcount");
        int beginIndex = 0;
        int endIndex = 30000;
        if (exportType.equals("3") && StringUtils.isNotBlank(recordStart) && StringUtils.isNotBlank(recordCount)) {
            beginIndex = Integer.parseInt(recordStart);
            beginIndex = beginIndex > 0 ? beginIndex - 1 : 0;
            int maxCount = Integer.parseInt(recordCount) > 30000 ? 30000 : Integer.parseInt(recordCount);
            endIndex = beginIndex + maxCount - 1;
        }
        PersLeavePersonItem persLeavePersonItem = new PersLeavePersonItem();
        setConditionValue(persLeavePersonItem);
        persLeavePersonService.buildCondition(request.getSession().getId(), persLeavePersonItem);
        List<PersLeavePersonItem> list = (List<PersLeavePersonItem>)persLeavePersonService
            .getItemData(PersLeavePersonItem.class, persLeavePersonItem, beginIndex, endIndex);
        excelExport(list, PersLeavePersonItem.class);
    }
}