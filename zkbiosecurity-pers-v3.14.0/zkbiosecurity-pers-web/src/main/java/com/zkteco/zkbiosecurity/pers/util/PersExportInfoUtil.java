/*
 * File Name: PersExportInfoUtil
 * Created by caiyun.chen on 2019/7/3 11:18.
 * Copyright:Copyright © 1985-2017 ZKTeco Inc.All right reserved.
 */


package com.zkteco.zkbiosecurity.pers.util;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

import com.zkteco.zkbiosecurity.base.annotation.DateType;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeRuleItem;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
public class PersExportInfoUtil {

    /**
    * 获取批注信息
    * <AUTHOR>
    * @param
    * @return java.util.Map<java.lang.String,com.zkteco.zkbiosecurity.pers.vo.PersAttributeRuleItem>
    * @date 2019/7/3 11:21
    */
    public static Map<String, PersAttributeRuleItem> getPersAttributeRule()
    {
        Map<String, PersAttributeRuleItem> map = new LinkedHashMap<>();
        List<PersAttributeRuleItem> persAttributeRuleItemList = new ArrayList<>();
        persAttributeRuleItemList.add(new PersAttributeRuleItem("pin"));
        persAttributeRuleItemList.add(new PersAttributeRuleItem("dept.code"));
        persAttributeRuleItemList.add(new PersAttributeRuleItem("dept.name"));
        persAttributeRuleItemList.add(new PersAttributeRuleItem("validCardNo"));

        for (PersAttributeRuleItem persAttributeRuleItem : persAttributeRuleItemList)
        {
            map.put(persAttributeRuleItem.getName(), persAttributeRuleItem);
        }
        return map;
    }

    /**
     *
     * @param obj 访问对象
     * @param filedname 对象的属性
     * @return 返回对象的属性值
     * @throws Exception
     */
    public static Object getFieldValue(Object obj, String filedname) throws Exception
    {
        //反射出类型
        Class<?> cls = obj.getClass();
        //反射出类型字段
        Field field = cls.getDeclaredField(filedname);
        //获取属性时，压制Java对访问修饰符的检查
        field.setAccessible(true);
        //在对象obj上读取field属性的值
        Object val = field.get(obj);

//        设置日期格式数据
        if ((field.getType() == Date.class || field.getType() == Timestamp.class) && val != null) {
            DateType dateType = field.getAnnotation(DateType.class);
            String format = getDateFormt(dateType);
            val = (new SimpleDateFormat(format)).format(val);

        }
        return val;
    }

    public static String  getDateFormt(DateType dateType){
        String format = "yyyy-MM-dd HH:mm:ss";
        if (dateType != null) {
            switch (dateType.type()) {
                case "date":
                    format = "yyyy-MM-dd";
                    break;
                case "time":
                    format = "HH:mm:ss";
                    break;
                case "timestamp":
                    format = "yyyy-MM-dd HH:mm:ss";
                    break;
            }
        }
        return format;
    }
}
