/*
 * File Name: PersTempPersonController <NAME_EMAIL> on 2018/9/26 10:55. Copyright:Copyright ©
 * 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.core.utils.GridUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.remote.PersTempPersonRemote;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.service.PersTempPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersTempPersonItem;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

@Controller
public class PersTempPersonController extends BaseController implements PersTempPersonRemote {
    @Autowired
    private PersTempPersonService persTempPersonService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private PersPersonService persPersonService;

    @Override
    @RequiresPermissions("pers:tempPerson")
    public ModelAndView index() {
        request.setAttribute("isAudit", baseSysParamService.getValByName("pers.tempPerson.audit"));
        return new ModelAndView("pers/tempPerson/persTempPerson");
    }

    @Override
    @RequiresPermissions("pers:tempPerson:audit")
    @LogRequest(module = "pers_module", object = "pers_tempPerson", opType = "pers_tempPerson_audit",
        requestParams = {"pin"}, opContent = "pers_person_pin")
    public ZKResultMsg audit(PersTempPersonItem item) {
        ZKResultMsg resultMsg = persTempPersonService.audit(item);
        if (!resultMsg.isSuccess()) {
            resultMsg.setMsg(resultMsg.getMsg() + I18nUtil.i18nCode("pers_app_pinExist"));
        }
        return I18nUtil.i18nMsg(resultMsg);
    }

    @Override
    @RequiresPermissions("pers:tempPerson:view")
    public ModelAndView view(@RequestParam(value = "id", required = false) String id) {
        PersTempPersonItem item = persTempPersonService.getItemById(id);
        if (StringUtils.isNotBlank(item.getPhotoPath())) {
            // 图片解密
            String photoBase64 = FileEncryptUtil.getDecryptFileBase64(item.getPhotoPath());
            // 设置base64数据
            item.setPhotoPathBase64(PersConstants.PERSON_BASE64_PREFIX + photoBase64);
        }
        request.setAttribute("item", item);
        return new ModelAndView("pers/tempPerson/editPersTempPerson");
    }

    @Override
    @RequiresPermissions("pers:tempPerson:refresh")
    public DxGrid list(PersTempPersonItem codition) {
        Pager pager = persTempPersonService.loadPagerByAuthUserFilter(request.getSession().getId(), codition,
            getPageNo(), getPageSize());
        persTempPersonService.protectPin((List<PersTempPersonItem>)pager.getData());
        return GridUtil.convert(pager, codition.getClass());
    }

    @Override
    @RequiresPermissions("pers:tempPerson:del")
    @LogRequest(module = "pers_module", object = "pers_tempPerson", opType = "common_op_del", requestParams = {"pins"},
        opContent = "pers_person_pin")
    public ZKResultMsg delete(String ids) {
        ZKResultMsg retMsg = new ZKResultMsg();
        persTempPersonService.deleteByIds(ids);
        return I18nUtil.i18nMsg(retMsg);
    }

    @Override
    @RequiresPermissions("pers:tempPerson:audit")
    public ModelAndView auditView(@RequestParam(value = "id", required = false) String id) {
        PersTempPersonItem item = persTempPersonService.getItemById(id);
        if (StringUtils.isNotBlank(item.getPhotoPath())) {
            // 图片解密
            String photoBase64 = FileEncryptUtil.getDecryptFileBase64(item.getPhotoPath());
            // 设置base64数据
            item.setPhotoPathBase64(PersConstants.PERSON_BASE64_PREFIX + photoBase64);
        }
        request.setAttribute("item", item);
        request.setAttribute("editPage", "true");
        PersPersonItem person = persPersonService.getItemByPin(item.getPin());
        if (person != null) {
            return new ModelAndView("pers/tempPerson/auditUpdateTempPerson");
        }
        return new ModelAndView("pers/tempPerson/auditPersTempPerson");
    }

    @Override
    public ZKResultMsg getTempPersonById(String id) {
        ZKResultMsg retMsg = new ZKResultMsg();
        retMsg.setData(persTempPersonService.getItemById(id));
        return I18nUtil.i18nMsg(retMsg);
    }
}
