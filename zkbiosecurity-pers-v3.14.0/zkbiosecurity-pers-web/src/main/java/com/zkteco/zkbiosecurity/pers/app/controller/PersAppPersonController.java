package com.zkteco.zkbiosecurity.pers.app.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.AppPersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.AppUserCertTypeItem;
import com.zkteco.zkbiosecurity.pers.api.vo.AppUserResetPasswordItem;
import com.zkteco.zkbiosecurity.pers.service.PersAppBioService;
import com.zkteco.zkbiosecurity.pers.service.PersAppService;
import com.zkteco.zkbiosecurity.pers.service.PersCertificateService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.util.PersCheckUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;

/**
 * 
 * APP 兼容云和线下人员接口
 * 
 * <AUTHOR>
 * @since 2019年6月21日 下午2:11:59
 */
@RestController
@RequestMapping(path = "/api/v1/user")
public class PersAppPersonController {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersAppService persAppService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private PersAppBioService persAppBioService;
	@Autowired
	private PersCertificateService persCertificateService;

    /**
     * 修改用户信息
     * 
     * @param appPersonItem
     * @return
     */
    @RequestMapping(path = "/changeUserInfo", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public ZKResultMsg changeUserInfo(@RequestBody AppPersonItem appPersonItem) {
        PersCheckUtil.checkAppPerson(appPersonItem, null);
        return I18nUtil.i18nMsg(persAppBioService.changeUserInfo(appPersonItem));
    }

    /**
     * 证件类型
     * 
     * <AUTHOR>
     * @since 2019年6月6日 下午2:26:40
     * @return
     */
    @RequestMapping(path = "/getCertType", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public ZKResultMsg getCertType() {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        Map<String, String> certMap = baseDictionaryValueService.getDictionaryValuesMap("certificateType");
        List<AppUserCertTypeItem> appCertTypeItems = new ArrayList<>();
        if (!certMap.isEmpty()) {
            certMap.forEach((k, v) -> {
                AppUserCertTypeItem appCertTypeItem = new AppUserCertTypeItem();
                appCertTypeItem.setCertName(v);
                appCertTypeItem.setCertType(k);
                appCertTypeItems.add(appCertTypeItem);
            });
        }
        resultMsg.setData(appCertTypeItems);
        return I18nUtil.i18nMsg(resultMsg);
    }

    /**
     * 修改密码
     * 
     * <AUTHOR>
     * @since 2019年6月6日 下午3:08:23
     * @param oldPwd
     * @param newPwd
     * @return
     */
    @RequestMapping(path = "/changePassword", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public ZKResultMsg changePassword(@RequestBody AppUserResetPasswordItem appUserResetPasswordItem) {
        PersPersonItem item = persPersonService.getItemById(appUserResetPasswordItem.getCustomerId());
        String newPassword = appUserResetPasswordItem.getNewPassword();
        String oldPassword = appUserResetPasswordItem.getOldPassword();
        if (oldPassword.equals(newPassword)) {
            //新老密码密码重复
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_h5_pwdIsRepetition"));
        }
        boolean isSucceed = persPersonService.changeSelfPwd(item.getPin(), oldPassword, newPassword, true);
        if (isSucceed) {
            return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
        }
        return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_h5_oldPwdIsError"));
    }

    /**
     * 退出登录
     * 
     * <AUTHOR>
     * @since 2019年6月13日 上午11:56:29
     * @param token
     * @return
     */
    @RequestMapping(path = "/loginOut", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public ZKResultMsg logout(@RequestParam(name = "access_token") String token) {
        persAppBioService.persLoginOut(token);
        return ZKResultMsg.successMsg();
    }

    /**
     * 获取用户信息
     * 
     * @param customerId
     * @return
     */
    @RequestMapping(path = "/getUserInfo", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    public ZKResultMsg getUserInfo(@RequestParam(name = "customerId") String customerId) {
        if (StringUtils.isBlank(customerId)) {
            //人员ID不能为空
            throw ZKBusinessException.errorException("pers_h5_personIdNull");
        }
        ZKResultMsg zkResultMsg = ZKResultMsg.successMsg();
        AppPersonItem person = persAppBioService.getUserInfoDetail(customerId);
        if (person != null) {
            zkResultMsg.setData(person);
        }
        return I18nUtil.i18nMsg(zkResultMsg);
    }
    
    /**
     * 分页获取用户信息
     * 
     * <AUTHOR>
     * @since 2019年7月18日 下午4:15:46
     * @param data
     * @return
     */
    @RequestMapping(path = "/getUserPage", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    public ZKResultMsg getUserPage(@RequestBody JSONObject data) {
        String filter = data.getString("filter");
        String deptId = data.getString("deptId");
        Integer page = data.getInteger("page");
        Integer pageSize = data.getInteger("pageSize");
        if (page == null || pageSize == null) {
            //分页参数不能为空
            throw ZKBusinessException.errorException("pers_h5_pageNull");
        }
        ZKResultMsg resultMsg = persPersonService.getUserInfos(filter, deptId, page, pageSize);
        return I18nUtil.i18nMsg(resultMsg);
    }
    
	/**
	 * 检查证件
	 * 
	 * <AUTHOR>
	 * @since 2019年9月11日 上午10:16:38
	 * @param certNumber
	 * @param personId
	 * @param certType
	 * @return
	 */
	@RequestMapping(path = "/checkCertNumber", method = RequestMethod.GET, produces = "application/json")
	@ResponseBody
	public ZKResultMsg checkCertNumber(@RequestParam("certNumber") String certNumber, @RequestParam(value = "personId", required = false) String personId, @RequestParam("certType") String certType) {
		ZKResultMsg zkResultMsg = new ZKResultMsg();
		boolean flag = true;
		if (StringUtils.isNotBlank(certNumber)) {
			flag = persCertificateService.isExistByCertNumberAndCertTypeAndPersonIdNe(certNumber, certType, personId);
		}
		zkResultMsg.setData(flag);
		return zkResultMsg;
	}
}
