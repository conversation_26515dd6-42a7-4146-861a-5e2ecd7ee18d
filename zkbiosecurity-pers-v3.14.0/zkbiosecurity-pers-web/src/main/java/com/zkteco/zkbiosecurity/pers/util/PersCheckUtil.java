package com.zkteco.zkbiosecurity.pers.util;

import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.ApiPersPersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.AppPersonItem;
import com.zkteco.zkbiosecurity.pers.constants.ApiErrorConstant;

import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 人员数据校验工具类
 * 
 * <AUTHOR>
 * @Date: 2019/1/5 09:54
 */
public class PersCheckUtil {

    public static final int OP_SUCCESS = 0;
    /** 卡号长度 */
    private static final int CARD_MAX_LEN = 200;
    /** 姓名长度 */
    private static final int NAME_MAX_LEN = 50;
    /** 部门编号长度 */
    private static final int DEPT_CODE_MAX_LEN = 30;
    /** 部门名称长度 */
    private static final int DEPT_NAME_MAX_LEN = 100;
    /** 邮箱长度 */
    private static final int EMAIL_MAX_LEN = 100;
    /** 地址长度 */
    private static final int ADDRESS_MAX_LEN = 200;
    /** 身份证类型 */
    private static final String IDENTITY_CARD_TYPE = "2";
    /** 姓名验证 */
    private static final String REGEX_NAME = "^[A-Za-z0-9\\u4e00-\\u9fa5]+$";
    /** 字母验证 */
    private static final String REGEX_LETTER = "^[A-Za-z]+$";

    /**
     * 人员校验入口
     * 
     * @auther lambert.li
     * @date 2019/1/7 10:44
     * @param apiPersPersonItem
     * @param certMap
     * @return
     */
    public static int checkPersonValid(ApiPersPersonItem apiPersPersonItem, Map<String, String> certMap) {
        //校验pin（必填）
        int ret = checkPersonPin(apiPersPersonItem.getPin());
        if (ret < OP_SUCCESS) {
            return ret;
        }
        //校验人员姓名
        ret = checkPersonName(apiPersPersonItem.getName());
        if (ret < OP_SUCCESS) {
            return ret;
        }
        //校验人员姓氏（海外）
        ret = checkPersonLastName(apiPersPersonItem.getLastName());
        if (ret < OP_SUCCESS) {
            return ret;
        }
        //校验部门编号
        ret = checkDeptCode(apiPersPersonItem.getDeptCode());
        if (ret < OP_SUCCESS) {
            return ret;
        }
        //校验部门名称
        ret = checkDeptName(apiPersPersonItem.getDeptName());
        if (ret < OP_SUCCESS) {
            return ret;
        }
        ret = checkCert(apiPersPersonItem.getCertType(), apiPersPersonItem.getCertNumber(), certMap);
        if (ret < OP_SUCCESS) {
            return ret;
        }
        //校验性别
        if (StringUtils.isNotBlank(apiPersPersonItem.getGender()) &&
            !(apiPersPersonItem.getGender().equalsIgnoreCase("F") || apiPersPersonItem.getGender().equalsIgnoreCase("M"))) {
            //性别错误
            return ApiErrorConstant.ERROR_CODE_PERS_GENDER_INVALID;
        }
        //校验生日
        if (StringUtils.isNotBlank(apiPersPersonItem.getBirthday()) && !ValidatorsUtils.isDate(apiPersPersonItem.getBirthday())) {
            //人员生日格式错误
            return ApiErrorConstant.ERROR_CODE_PERS_BIRTHDAY_INVALID;
        }
        //校验手机号码
        if ("zh_CN".equals(LocaleMessageSourceUtil.language) && StringUtils.isNotBlank(apiPersPersonItem.getMobilePhone()) && !ValidatorsUtils.isMobilePhone(apiPersPersonItem.getMobilePhone())) {
            //中文环境验证手机号
            //人员手机号码格式错误
            return ApiErrorConstant.ERROR_CODE_PERS_PHONE_INVALID;
        }
        //邮箱非空
        //        if(StringUtils.isBlank(apiPersPersonItem.getEmail()))
        //        {
        //            return ApiErrorConstant.ERROR_CODE_PERS_EMAIL_NULL;
        //        }
        //校验手机号码-&& !ValidatorsUtils.isMobilePhone(apiPersPersonItem.getMobilePhone())
        //去掉手机号的合法性校验 国外的手机号并不一定是1XXXXX
        //        if (StringUtils.isNotBlank(apiPersPersonItem.getMobilePhone())) {
        //            //人员手机号码格式错误
        //            return ApiErrorConstant.ERROR_CODE_PERS_PHONE_INVALID;
        //        }
        //校验邮箱
        if (StringUtils.isNotBlank(apiPersPersonItem.getEmail()) && (!ValidatorsUtils.isEmail(apiPersPersonItem.getEmail()) || apiPersPersonItem.getEmail().length() > EMAIL_MAX_LEN)) {
            return ApiErrorConstant.ERROR_CODE_PERS_EMAIL_INVALID;
        }
        //校验入职日期
        if (StringUtils.isNotBlank(apiPersPersonItem.getHireDate()) && !ValidatorsUtils.isDate(apiPersPersonItem.getHireDate())) {
            return ApiErrorConstant.ERROR_CODE_PERS_HIRE_DATE_INVALID;
        }
        //校验卡号
        if (StringUtils.isNotBlank(apiPersPersonItem.getCardNos()) && apiPersPersonItem.getCardNos().length() > CARD_MAX_LEN) {
            //卡号长度太长
            return ApiErrorConstant.ERROR_CODE_PERS_CARD_NUMBER_TOO_LONG;
        }
        return ret;
    }

    /**
     * 验证人员编号
     * 
     * @auther lambert.li
     * @date 2019/1/5 9:59
     * @param pin 工号
     * @return
     */
    public static int checkPersonPin(String pin) {
        if (StringUtils.isBlank(pin)) {
            //PIN不能为空
            return ApiErrorConstant.ERROR_CODE_PERS_PIN_NULL;
        }
        if (!ValidatorsUtils.isAlphanumeric(pin)) {
            //PIN格式错误（只能含有数字和字母）
            return ApiErrorConstant.ERROR_CODE_PERS_PIN_INVALID;
        }
        if (pin.length() > 23) {
            //PIN长度太长
            return ApiErrorConstant.ERROR_CODE_PERS_PIN_TOO_LONG;
        }
        return OP_SUCCESS;
    }

    /**
     * 校验人员姓名
     * 
     * @auther lambert.li
     * @date 2019/1/7 8:56
     * @param name 名字
     * @return
     */
    public static int checkPersonName(String name) {
        if (StringUtils.isNotBlank(name)) {
            //校验人员姓名长度
            if (StringUtils.isNotBlank(name) && name.length() > NAME_MAX_LEN) {
                //卡号长度太长
                return ApiErrorConstant.ERROR_CODE_PERS_NAME_TOO_LONG;
            }
            //校验人员姓名规范不包含特殊字符
            if (ValidatorsUtils.isSpecialChar(name)) {
                return ApiErrorConstant.ERROR_CODE_PERS_NAME_INVALID;
            }
            //            if (!ValidatorsUtils.isRegexMatch(name, REGEX_NAME)) {
            //                //卡号长度太长
            //                return ApiErrorConstant.ERROR_CODE_PERS_NAME_INVALID;
            //            }
        }
        return OP_SUCCESS;
    }

    /**
     * 校验人员姓氏
     * 
     * @auther lambert.li
     * @date 2019/1/7 8:56
     * @param lastName 姓氏
     * @return
     */
    public static int checkPersonLastName(String lastName) {
        if (StringUtils.isNotBlank(lastName)) {
            //校验人员姓名长度
            if (lastName.length() > NAME_MAX_LEN) {
                //卡号长度太长
                return ApiErrorConstant.ERROR_CODE_PERS_LAST_NAME_TOO_LONG;
            }
            //校验人员姓名规范
            if (ValidatorsUtils.isSpecialChar(lastName)) {
                //姓氏格式错误
                return ApiErrorConstant.ERROR_CODE_PERS_LAST_NAME_INVALID;
            }
        }
        return OP_SUCCESS;
    }

    /**
     * 校验部门编号
     * 
     * @auther lambert.li
     * @date 2019/1/7 9:32
     * @param deptCode 部门编号
     * @return
     */
    public static int checkDeptCode(String deptCode) {
        //非空校验
        if (StringUtils.isBlank(deptCode)) {
            return ApiErrorConstant.ERROR_CODE_DEPT_CODE_NULL;
        }
        //长度校验
        if (deptCode.length() > DEPT_CODE_MAX_LEN) {
            return ApiErrorConstant.ERROR_CODE_DEPT_CODE_TOO_LONG;
        }
        //格式校验
        if (!ValidatorsUtils.isAlphanumeric(deptCode)) {
            return ApiErrorConstant.ERROR_CODE_DEPT_CODE_INVALID;
        }
        return OP_SUCCESS;
    }

    /**
     * 校验部门编号
     * 
     * @auther lambert.li
     * @date 2019/1/7 9:32
     * @param deptName
     * @return
     */
    public static int checkDeptName(String deptName) {
        if (StringUtils.isNotBlank(deptName)) {
            //长度校验
            if (deptName.length() > DEPT_NAME_MAX_LEN) {
                return ApiErrorConstant.ERROR_CODE_DEPT_NAME_TOO_LONG;
            }
            //格式校验
            if (ValidatorsUtils.isSpecialChar(deptName)) {
                return ApiErrorConstant.ERROR_CODE_DEPT_NAME_INVALID;
            }
        }
        return OP_SUCCESS;
    }

    /**
     * 校验证件
     * 
     * @auther lambert.li
     * @date 2019/1/7 10:32
     * @param certType 证件类型
     * @param certNumber 证件号码
     * @param certTypeMap 字典证件类型值
     * @return
     */
    public static int checkCert(String certType, String certNumber, Map<String, String> certTypeMap) {
        if (StringUtils.isNotBlank(certType) || StringUtils.isNotBlank(certNumber)) {
            if (StringUtils.isNotBlank(certType) && StringUtils.isBlank(certNumber)) {
                //证件号码为空
                return ApiErrorConstant.ERROR_CODE_PERS_CERT_NUMBER_NULL;
            }
            if (StringUtils.isNotBlank(certNumber) && StringUtils.isBlank(certType)) {
                //证件类型为空
                return ApiErrorConstant.ERROR_CODE_PERS_CERT_TYPE_NULL;
            }
            if (certTypeMap != null && certTypeMap.get(certType) == null) {
                //证件类型不存在
                return ApiErrorConstant.ERROR_CODE_PERS_CERT_TYPE_NOT_EXIST;
            }
            if (IDENTITY_CARD_TYPE.equals(certType) && !ValidatorsUtils.checkCertType(certNumber)) {
                //身份证格式错误
                return ApiErrorConstant.ERROR_CODE_PERS_IDENTITY_CARD_INVALID;
            }
        }
        return OP_SUCCESS;
    }

    /**
     * TODO 需要根据企业人员参数设置判断
     * 校验APP接口人员数据有效性
     * 
     * @auther lambert.li
     * @date 2019/1/16 8:54
     * @param appPersonItem
     * @return
     */
    public static void checkAppPerson(AppPersonItem appPersonItem, Map<String, String> paramMap) {
        String pin = appPersonItem.getPin();
        String mobile = appPersonItem.getMobile();
        String name = appPersonItem.getName();
        String lastName = appPersonItem.getLastName();
        String gender = appPersonItem.getGender();
        String familyAddress = appPersonItem.getFamilyAddress();
        String cardNos = appPersonItem.getCardNos();
        String email = appPersonItem.getEmail();
        //编号校验
        checkPin(pin);
        //手机号码与邮箱二选一
        if (StringUtils.isBlank(mobile) && StringUtils.isBlank(email)) {
            //手机号码不能为空
            throw ZKBusinessException.errorException("pers_h5_emailOrPhoneNull");
        }
        if ("zh_CN".equals(LocaleMessageSourceUtil.language) && StringUtils.isNotBlank(mobile) && !ValidatorsUtils.isMobilePhone(mobile)) {
            //中文环境校验手机号码格式
            // 手机号码格式错误
            throw ZKBusinessException.errorException("pers_h5_personMobileInvalid");
        }
        //校验邮箱
        if (StringUtils.isNotBlank(email) && (!ValidatorsUtils.isEmail(email) || email.length() > EMAIL_MAX_LEN)) {
            throw ZKBusinessException.errorException("common_email_inputEmailError");
        }
        //        if (StringUtils.isBlank(appPersonItem.getPassword())) {
        //密码不能为空
        //            throw ZKBusinessException.errorException("base_userService_setUser09");
        //        }
        //人员姓名校验
        if (StringUtils.isNotBlank(name)) {
            //校验人员姓名长度
            int ret = checkPersonName(name);
            if (ret < OP_SUCCESS && ret == ApiErrorConstant.ERROR_CODE_PERS_NAME_TOO_LONG) {
                //姓名长度太长
                throw ZKBusinessException.errorException("pers_h5_personNameTooLong");
            }
            if (ret < OP_SUCCESS && ret == ApiErrorConstant.ERROR_CODE_PERS_NAME_INVALID) {
                //姓名格式错误
                throw ZKBusinessException.errorException("pers_h5_personNameInvalid");
            }
        }
        //人员姓名校验
        if (StringUtils.isNotBlank(lastName)) {
            //校验人员姓名长度
            int ret = checkPersonLastName(lastName);
            if (ret < OP_SUCCESS && ret == ApiErrorConstant.ERROR_CODE_PERS_LAST_NAME_TOO_LONG) {
                //姓名长度太长
                throw ZKBusinessException.errorException("pers_h5_personLastNameTooLong");
            }
            if (ret < OP_SUCCESS && ret == ApiErrorConstant.ERROR_CODE_PERS_LAST_NAME_INVALID) {
                //姓名格式错误
                throw ZKBusinessException.errorException("pers_h5_personLastNameInvalid");
            }
        }
        //校验性别
        if (StringUtils.isNotBlank(gender) &&
            !(gender.equalsIgnoreCase("F") || gender.equalsIgnoreCase("M"))) {
            //性别错误
            throw ZKBusinessException.errorException("pers_h5_personGenderInvalid");
        }
        //校验生日
        if (StringUtils.isNotBlank(appPersonItem.getBirthday()) && !ValidatorsUtils.isDate(appPersonItem.getBirthday())) {
            //日期错误
            throw ZKBusinessException.errorException("pers_h5_dateInvalid");
        }
        //校验卡号
        if (StringUtils.isNotBlank(cardNos) && cardNos.length() > CARD_MAX_LEN) {
            //卡号长度太长
            throw ZKBusinessException.errorException("pers_h5_cardNoTooLong");
        }
        //家庭地址
        if (StringUtils.isNotBlank(familyAddress) && familyAddress.length() > ADDRESS_MAX_LEN) {
            //卡号长度太长
            throw ZKBusinessException.errorException("pers_h5_familyAddressTooLong");
        }
    }

    /**
     * 人员编号校验
     * 
     * @auther lambert.li
     * @date 2019/5/28 20:00
     * @param pin
     * @return
     */
    private static void checkPin(String pin) {
        //编号校验
        if (StringUtils.isBlank(pin)) {
            //人员编号不能为空
            throw ZKBusinessException.errorException("pers_import_pinNotEmpty");
        }
        if (pin.length() > 9) {
            //人员编号太长
            throw ZKBusinessException.errorException("pers_h5_personPinTooLong", 9);
        }
        //过滤访客和酒店使用的编号
        if (pin.length() == 9 && (pin.startsWith("8") || pin.startsWith("9"))) {
            throw ZKBusinessException.errorException("pers_h5_personPinInValid");
        }
        //过滤人员编号不能以0开头
        if (pin.startsWith("0")) {
            throw ZKBusinessException.errorException("pers_h5_personPinNotStartWithZero");
        }
        if (!ValidatorsUtils.isNumberStr(pin)) {
            //人员编号只能由数字或字母组成
            throw ZKBusinessException.errorException("pers_h5_personPinFormatNumber");
        }
    }
}
