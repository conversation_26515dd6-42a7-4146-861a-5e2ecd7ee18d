package com.zkteco.zkbiosecurity.pers.dhx;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.ShowGridColumn;
import com.zkteco.zkbiosecurity.core.utils.WebContextUtil;
import com.zkteco.zkbiosecurity.security.SecurityService;

@Component
public class PersShowAppAuthorization implements ShowGridColumn {
    @Autowired
    private SecurityService securityService;

    @Override
    public boolean isShow(Object obj) {
        // 启禁用app登录的权限都没有时，列表不显示列
        return !(!securityService.checkPermission(WebContextUtil.getCurrentSessionId(), "pers:person:enabledApplogin")
            && !securityService.checkPermission(WebContextUtil.getCurrentSessionId(), "pers:person:disableApplogin"));
    }
}
