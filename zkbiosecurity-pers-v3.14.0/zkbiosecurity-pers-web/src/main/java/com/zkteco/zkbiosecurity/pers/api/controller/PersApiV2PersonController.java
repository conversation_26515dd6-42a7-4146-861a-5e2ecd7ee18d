package com.zkteco.zkbiosecurity.pers.api.controller;

import java.io.File;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.base.annotation.ApiLogRequest;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.FileUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiFacePhotoItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPersonItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersApiPersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * 人员接口
 * 
 * <AUTHOR>
 * @Date: 2018/11/8 09:14
 */

@Controller
@RequestMapping(value = {"/api/v2/person"})
@Slf4j
@Api(tags = "Person", description = "person")
public class PersApiV2PersonController {

    @Autowired
    private PersApiPersonService persApiPersonService;
    @Autowired
    private PersPersonService persPersonService;

    @ResponseBody
    @RequestMapping(value = "/getPersonList", method = RequestMethod.POST, produces = "application/json")
    @ApiOperation(value = "Get Person List By PinList And DeptCode And Name", notes = "Return Persons List",
        response = ApiResultMessage.class)
    @ApiLogRequest(requestParams = {"pins", "deptCodes", "name", "pageNo", "pageSize"})
    public ApiResultMessage getPersonList(@RequestParam(name = "pins", required = false) String pins,
        @RequestParam(name = "deptCodes", required = false) String deptCodes,
        @RequestParam(name = "pageNo") Integer pageNo, @RequestParam(name = "pageSize") Integer pageSize,
        @RequestParam(name = "name", required = false) String name,
        @RequestParam(name = "lastName", required = false) String lastName) {
        if (pageNo == null || pageSize == null) {
            return ApiResultMessage.message(PersConstants.PERSON_PAGE_NULL,
                I18nUtil.i18nCode("common_api_pageNotNull"));
        }
        if (pageNo <= 0 || pageSize <= 0) {
            return ApiResultMessage.message(PersConstants.PERSON_WRONG_PAGE, I18nUtil.i18nCode("common_api_pageValid"));
        }
        if (pageSize > 1000) {
            return ApiResultMessage.message(PersConstants.PERSON_PAGE_OVERSIZE,
                I18nUtil.i18nCode("common_api_pageSizeOver"));
        }
        PersPersonItem persPersonItem = new PersPersonItem();
        if (StringUtils.isNotBlank(pins)) {
            persPersonItem.setInPin(pins);
        }
        if (StringUtils.isNotBlank(deptCodes)) {
            persPersonItem.setInDeptCode(deptCodes);
        }
        if (StringUtils.isNotBlank(name)) {
            persPersonItem.setName(name);
            persPersonItem.setEquals(true);
        }
        if (StringUtils.isNotBlank(lastName)) {
            persPersonItem.setLastName(lastName);
            persPersonItem.setEquals(true);
        }
        Pager pager = persApiPersonService.getApiPersonByPage(persPersonItem, pageNo, pageSize);
        return ApiResultMessage.successMessage(pager);
    }

    /**
     * 根据pin删除人员
     *
     * @param pin
     * @return
     */
    @ApiOperation(value = "Delete Person", notes = "Delete Person By Pin", response = ApiResultMessage.class)
    @RequestMapping(value = "/delete", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage deleteByPin(@RequestParam String pin) {
        ApiResultMessage rs = new ApiResultMessage();
        if (StringUtils.isBlank(pin)) {
            // pin不能为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        try {
            PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
            if (persPersonItem == null) {
                return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                    I18nUtil.i18nCode("pers_api_personNotExist"));
            }
            persPersonService.deleteByIds(persPersonItem.getId());
        } catch (ZKBusinessException e) {
            rs = ApiResultMessage.message(PersConstants.PERSON_DELETE_FAILED, I18nUtil.i18nCode(e.getMessage()));
        } catch (Exception e) {
            log.error("api person/delete error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 根据pin获取人员信息
     *
     * @param pin
     * @return
     */
    @ApiOperation(value = "Get Person", notes = "Get Person By Pin", response = ApiResultMessage.class)
    @RequestMapping(value = "/get", method = RequestMethod.GET, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage getByPin(@RequestParam String pin) {
        ApiResultMessage rs = null;
        if (StringUtils.isBlank(pin)) {
            // pin不能为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        try {
            rs = persApiPersonService.getApiPersonByPin(pin);
        } catch (Exception e) {
            log.error("api person/get error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 根据pin获取动态二维码
     *
     * @param pin:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * @since 1.0.0
     */
    @ApiOperation(value = "Get Dynamic QR code", notes = "Get Dynamic QR code By Pin",
        response = ApiResultMessage.class)
    @RequestMapping(value = "/getQrCode", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pin"})
    public ApiResultMessage getQrCodeByPin(@RequestParam String pin) {
        ApiResultMessage rs = new ApiResultMessage();
        if (StringUtils.isBlank(pin)) {
            // pin不能为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        try {
            rs = persApiPersonService.getQrCodeByPin(pin);
        } catch (ZKBusinessException e) {
            rs = ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST, I18nUtil.i18nCode(e.getMessage()));
        } catch (Exception e) {
            log.error("api person/getQrCode error ", e);
            rs = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                I18nUtil.i18nCode("common_api_programError"));
        }
        return rs;
    }

    /**
     * 根据pins批量删除人员
     * 
     * @param pins:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date 2024-01-08 14:39
     * @since 1.0.0
     */
    @ApiOperation(value = "Batch Delete Person", notes = "Delete Person By Pins", response = ApiResultMessage.class)
    @RequestMapping(value = "/deleteByPins", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"pins"})
    public ApiResultMessage deleteByPins(@RequestParam String pins) {
        ApiResultMessage rs = new ApiResultMessage();
        if (StringUtils.isBlank(pins)) {
            // pin不能为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        JSONArray array = new JSONArray();
        int error = 0;
        PersPersonItem persPersonItem = new PersPersonItem();
        List<String> pinList = StrUtil.strToList(pins);
        if (pinList.size() > PersConstants.API_OPERATE_DATALIMIT) {
            return ApiResultMessage.message(PersConstants.PERSON_OPERATE_OVERSIZE,
                I18nUtil.i18nCode("pers_api_dataLimit", pinList.size(), PersConstants.API_OPERATE_DATALIMIT));
        }
        for (String pin : pinList) {
            persPersonItem = persPersonService.getItemByPin(pin);
            if (persPersonItem == null) {
                array.add(pin + ":" + I18nUtil.i18nCode("pers_api_personNotExist"));
                error++;
                continue;
            }
            try {
                persPersonService.deleteByIds(persPersonItem.getId());
            } catch (ZKBusinessException e) {
                String name = StringUtils.trim(StringUtils.defaultIfEmpty(persPersonItem.getName(), "") + " "
                    + StringUtils.defaultIfEmpty(persPersonItem.getLastName(), ""));
                array.add(name + "-" + persPersonItem.getPin() + ":" + I18nUtil.i18nCode(e.getMessage()));
                error++;
            } catch (Exception e) {
                log.error("api person/delete error ", e);
                return ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR,
                    I18nUtil.i18nCode("common_api_programError"));
            }
        }
        int success = pinList.size() - error;
        rs.setMessage(I18nUtil.i18nCode("pers_import_result", success, error));
        rs.setData(array);
        return rs;
    }

    /**
     * 批量新增人员
     * 
     * @param persApiPersonItemList:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date 2024-01-10 15:16
     * @since 1.0.0
     */
    @ApiOperation(value = "Batch Add Person", notes = "Batch Create Or Update Person",
        response = ApiResultMessage.class)
    @RequestMapping(value = "/addPersons", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest(requestParams = {"persApiPersonItemList[pin, deptCode]"})
    public ApiResultMessage addPersons(@RequestBody List<PersApiPersonItem> persApiPersonItemList) {
        ApiResultMessage ars = new ApiResultMessage();
        ApiResultMessage rs = null;
        if (persApiPersonItemList != null && !persApiPersonItemList.isEmpty()) {
            if (persApiPersonItemList.size() > PersConstants.API_OPERATE_DATALIMIT) {
                return ApiResultMessage.message(PersConstants.PERSON_OPERATE_OVERSIZE, I18nUtil
                    .i18nCode("pers_api_dataLimit", persApiPersonItemList.size(), PersConstants.API_OPERATE_DATALIMIT));
            }
            JSONArray array = new JSONArray();
            int error = 0;
            for (PersApiPersonItem person : persApiPersonItemList) {
                try {
                    rs = persApiPersonService.addApiPerson(person);
                    if (0 != rs.getCode()) {
                        String name = StringUtils.trim(StringUtils.defaultIfEmpty(person.getName(), "") + " "
                            + StringUtils.defaultIfEmpty(person.getLastName(), ""));
                        array.add(name + "-" + person.getPin() + ":" + rs.getMessage());
                        error++;
                    }
                } catch (ZKBusinessException e) {
                    String name = StringUtils.trim(StringUtils.defaultIfEmpty(person.getName(), "") + " "
                        + StringUtils.defaultIfEmpty(person.getLastName(), ""));
                    array.add(name + "-" + person.getPin() + ":" + I18nUtil.i18nCode(e.getMessage()));
                    error++;
                } catch (Exception e) {
                    log.error("api person/add error ", e);
                    ars = ApiResultMessage.message(PersConstants.API_PROGRAM_ERROR, e.getMessage());
                }
            }
            int success = persApiPersonItemList.size() - error;
            ars.setMessage(I18nUtil.i18nCode("pers_import_result", success, error));
            ars.setData(array);
        }

        return ars;
    }

    /**
     * 检测人脸是否可以抠图
     * 
     * @param persApiFacePhotoItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date 2024-08-19 14:48
     * @since 1.0.0
     */
    @ApiOperation(value = "Detecting faces", notes = "Detecting faces", response = ApiResultMessage.class)
    @RequestMapping(value = "/detectFace", method = RequestMethod.POST, produces = "application/json")
    @ResponseBody
    @ApiLogRequest
    public ApiResultMessage detectFace(@RequestBody PersApiFacePhotoItem persApiFacePhotoItem) {
        ApiResultMessage rs = new ApiResultMessage();
        String personPhoto = persApiFacePhotoItem.getPersonPhoto();
        String photoPath = "";
        String filePath = FileUtil.createUploadFileRootPath("pers", "user/avatar");
        String photoName = System.currentTimeMillis() + ".jpg";
        if (StringUtils.isNotBlank(personPhoto)) {
            // 生成临时图片
            FileUtil.saveFile(filePath, photoName, personPhoto, false);
            photoPath = '/' + filePath + photoName;
        } else {
            // 不是图片
            return ApiResultMessage.message(PersConstants.PERSON_PHOTO_INVALID,
                I18nUtil.i18nCode("pers_api_selectPhotoInvalid"));
        }
        if (StringUtils.isNotBlank(photoPath)) {
            ZKResultMsg zkResultMsg = persPersonService.validCropFace(photoPath);
            if (zkResultMsg.isSuccess()) {
                rs.setData(zkResultMsg.getData());
            } else {
                rs.setCode(Integer.parseInt(zkResultMsg.getRet()));
                rs.setMessage(zkResultMsg.getMsg());
            }
            File tempFile = new File(FileUtil.getLocalFullPath(photoPath));
            if (tempFile.exists()) {
                // 删除临时图片
                tempFile.delete();
            }
        }
        return rs;
    }
}