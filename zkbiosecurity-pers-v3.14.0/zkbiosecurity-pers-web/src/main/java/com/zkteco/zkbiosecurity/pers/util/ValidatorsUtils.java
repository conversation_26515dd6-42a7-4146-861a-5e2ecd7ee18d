package com.zkteco.zkbiosecurity.pers.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.Calendar;
import java.util.regex.Pattern;

import javax.imageio.ImageIO;

import sun.misc.BASE64Decoder;

/**
 * @Auther: Abel.huang
 * @Date: 2019/1/5 09:48
 * @Description:
 */
public class ValidatorsUtils {

    /**
     * 简体中文的正则表达式。
     */
    private static final String REGEX_SIMPLE_CHINESE = "^[\u4E00-\u9FA5]+$";

    /**
     * 字母数字的正则表达式。
     */
    private static final String REGEX_ALPHANUMERIC = "[a-zA-Z0-9]+";

    /**
     * 整数或浮点数的正则表达式。
     */
    private static final String REGEX_NUMERIC = "(\\+|-){0,1}(\\d+)([.]?)(\\d*)";

    /**
     * 身份证号码的正则表达式。
     */
    private static final String REGEX_ID_CARD = "(\\d{14}|\\d{17})(\\d|x|X)";

    /**
     * 电子邮箱的正则表达式。
     */
    private static final String REGEX_EMAIL = ".+@.+\\.[a-z]+";

    /**
     * 手机号码的正则表达式。
     */
    private static final String REGEX_PHONE = "^(1[1-9])\\d{9}$";

    /**
     * 手机号码的正则表达式。
     */
    private static final String REGEX_NUMBER = "[0-9]*";

    private static final int[] factorArr = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1};
    private static final String[] parityBit = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};
    private static final String[] UNCHAR = {"&", "<", ">", "`", "~", "!", "@", "#", "$", "%", "^", "*", "?", "/", "|",
        "\\", ":", ";", "=", "\"", "\'", ","};

    /** 图片最大大小 10M （byte） */
    private static final int PHOTO_MAX_SIZE_BYTE = 10485760;

    /**
     * 判断字符串是否只包含字母和数字.
     *
     * @param str
     *            字符串
     * @return 如果字符串只包含字母和数字, 则返回 <code>true</code>, 否则返回 <code>false</code>.
     */
    public static boolean isAlphanumeric(String str) {
        return isRegexMatch(str, REGEX_ALPHANUMERIC);
    }

    /**
     * 是否是合法的日期字符串(类似格式:2017-03-01是合法的) Validators.isBlank(&quot;2017-03-01&quot;) = true
     * Validators.isBlank(&quot;2017-0301&quot;) = false
     *
     * @param str
     *            日期字符串
     * @return 是true，否则false
     */
    public static boolean isDate(String str) {
        if (isEmpty(str) || str.length() > 10) {
            return false;
        }

        String[] items = str.split("-");

        if (items.length != 3) {
            return false;
        }

        if (!isNumber(items[0], 1900, 9999) || !isNumber(items[1], 1, 12)) {
            return false;
        }
        int year = Integer.parseInt(items[0]);
        int month = Integer.parseInt(items[1]);

        return isNumber(items[2], 1, getLastDayOfMonth(year, month));
    }

    /**
     * 是否是合法的日期字符串(类似格式:2017-03-01是合法的) Validators.isBlank(&quot;2017-03-01&quot;) = true
     * Validators.isBlank(&quot;2017-0301&quot;) = false
     *
     * @param str
     *            日期字符串
     * @return 是true，否则false
     */
    public static boolean isMonth(String str) {
        if (isEmpty(str) || str.length() > 7) {
            return false;
        }
        String[] items = str.split("-");

        if (items.length != 2) {
            return false;
        }

        if (!isNumber(items[0], 1900, 9999) || !isNumber(items[1], 1, 12)) {
            return false;
        }
        return true;
    }

    /**
     * 获取某年某月的最后一天
     *
     * @param year
     *            目标年份
     * @param month
     *            目标月份
     * @return
     */
    public static int getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        // 设置年份
        cal.set(Calendar.YEAR, year);
        // 设置月份
        cal.set(Calendar.MONTH, month - 1);
        // 获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        return lastDay;
    }

    /**
     * 是否是合法的日期时间字符串 Validators.isDateTime(&quot;2017-03-01 12:03:01&quot;) = true
     * Validators.isDateTime(&quot;2017-03-01 12:0301&quot;) = false
     *
     * @param str
     *            日期时间字符串
     * @return 是true，否则false
     */
    public static boolean isDateTime(String str) {
        if (isEmpty(str) || str.length() > 20) {
            return false;
        }

        String[] items = str.split(" ");

        if (items.length != 2) {
            return false;
        }

        return isDate(items[0]) && isTime(items[1]);
    }

    /**
     * 判断字符串是否是合法的电子邮箱地址.
     *
     * @param str
     *            字符串
     * @return 是true，否则false
     */
    public static boolean isEmail(String str) {
        return isRegexMatch(str, REGEX_EMAIL);
    }

    /**
     * 当数组为<code>null</code>, 或者长度为0, 或者长度为1且元素的值为<code>null</code>时返回 <code>true</code>.
     *
     * @param args
     * @return true/false
     */
    public static boolean isEmpty(Object[] args) {
        return args == null || args.length == 0 || (args.length == 1 && args[0] == null);
    }

    /**
     * 字符串是否为Empty，null和空格都算是Empty
     *
     * @param str
     *            字符串
     * @return true/false
     */
    public static boolean isEmpty(String str) {
        return str == null || str.trim().length() == 0;
    }

    /**
     * 是否为数字的字符串。
     *
     * @param str
     *            字符串
     * @return true/false
     */
    public static boolean isNumber(String str) {
        if (isEmpty(str)) {
            return false;
        }

        for (int i = 0; i < str.length(); i++) {
            if (str.charAt(i) > '9' || str.charAt(i) < '0') {
                return false;
            }
        }
        return true;
    }

    /**
     * 是否是固定范围内的数字的字符串
     *
     * @param str
     * @param min
     * @param max
     * @return true/false
     */
    public static boolean isNumber(String str, int min, int max) {
        if (!isNumber(str)) {
            return false;
        }

        int number = Integer.parseInt(str);
        return number >= min && number <= max;
    }

    /**
     * 判断字符是否为整数或浮点数. <br>
     *
     * @param str
     *            字符串
     * @return 若为整数或浮点数则返回 <code>true</code>, 否则返回 <code>false</code>
     */
    public static boolean isNumeric(String str) {
        return isRegexMatch(str, REGEX_NUMERIC);
    }

    /**
     * 判断字符是否为符合精度要求的整数或浮点数。
     *
     * @param str
     *            字符串
     * @param fractionNum
     *            小数部分的最多允许的位数
     * @return 若为整数或浮点数则返回 <code>true</code>, 否则返回 <code>false</code>
     */
    public static boolean isNumeric(String str, int fractionNum) {
        if (isEmpty(str)) {
            return false;
        }

        // 整数或浮点数
        String regex = "(\\+|-){0,1}(\\d+)([.]?)(\\d{0," + fractionNum + "})";
        return Pattern.matches(regex, str);
    }

    /**
     * 判断是否是合法的邮编
     *
     * @param str
     *            字符串
     * @return true/false
     */
    public static boolean isPostcode(String str) {
        if (isEmpty(str)) {
            return false;
        }

        if (str.length() != 6 || !isNumber(str)) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否是固定长度范围内的字符串
     *
     * @param str
     * @param minLength
     * @param maxLength
     * @return true/false
     */
    public static boolean isString(String str, int minLength, int maxLength) {
        if (str == null) {
            return false;
        }

        if (minLength < 0) {
            return str.length() <= maxLength;
        } else if (maxLength < 0) {
            return str.length() >= minLength;
        } else {
            return str.length() >= minLength && str.length() <= maxLength;
        }
    }

    /**
     * 判断是否是合法的时间字符串。
     *
     * @param str
     *            字符串
     * @return true/false
     */
    public static boolean isTime(String str) {
        if (isEmpty(str) || str.length() > 8) {
            return false;
        }

        String[] items = str.split(":");

        if (items.length != 2 && items.length != 3) {
            return false;
        }

        for (int i = 0; i < items.length; i++) {
            if (items[i].length() != 2 && items[i].length() != 1) {
                return false;
            }
        }

        return !(!isNumber(items[0], 0, 23) || !isNumber(items[1], 0, 59)
            || (items.length == 3 && !isNumber(items[2], 0, 59)));
    }

    /**
     * 是否是简体中文字符串。
     *
     * @param str
     *            字符串
     * @return true/false
     */
    public static boolean isSimpleChinese(String str) {
        return isRegexMatch(str, REGEX_SIMPLE_CHINESE);
    }

    /**
     * 判断字符串是否匹配了正则表达式。
     *
     * @param str
     *            字符串
     * @param regex
     *            正则表达式
     * @return true/false
     */
    public static boolean isRegexMatch(String str, String regex) {
        return str != null && str.matches(regex);
    }

    /**
     * 判断字符串是否是合法的手机号码.
     *
     * @param str
     *            字符串
     * @return 是true，否则false
     */
    public static boolean isMobilePhone(String str) {
        return isRegexMatch(str, REGEX_PHONE);
    }

    /**
     * 校验二代身份证
     *
     * @param certNum
     * @return
     */
    public static boolean checkCertType(String certNum) {
        String[] varArray = new String[20];
        int lngProduct = 0;
        String intCheckDigit;
        // 证件长度
        int intStrLen = certNum.length();
        if (intStrLen != 18) {
            return false;
        }
        for (int i = 0; i < intStrLen; i++) {
            varArray[i] = String.valueOf(certNum.charAt(i));
            if ((i != 17) && (Integer.parseInt(varArray[i]) < 0 || Integer.parseInt(varArray[i]) > 9)) {
                return false;
            } else if (i < 17) {
                varArray[i] = String.valueOf(Integer.parseInt(varArray[i]) * factorArr[i]);
            }
        }
        String date = certNum.substring(6, 14);
        if (isData(date) == false) {
            return false;
        }

        for (int i = 0; i < 17; i++) {
            lngProduct = lngProduct + Integer.parseInt(varArray[i]);
        }
        intCheckDigit = parityBit[lngProduct % 11];
        if (!String.valueOf(varArray[17]).equals(intCheckDigit)) {
            return false;
        }
        return true;
    }

    /**
     * 校验二代身份证出生年月日格式
     *
     * @param data
     * @return
     */
    public static boolean isData(String data) {
        String regex = "[0-9]{6}";
        Boolean result = Pattern.compile(regex).matcher(data).find();
        if (!result) {
            return false;
        }
        int year, month, day;
        year = Integer.parseInt(data.substring(0, 4));
        month = Integer.parseInt(data.substring(4, 6));
        day = Integer.parseInt(data.substring(6, 8));
        int[] iaMonthDays = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        if (year < 1700 || year > 2500) {
            return false;
        }
        if (((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0)) {
            iaMonthDays[1] = 29;
        }
        if (month < 1 || month > 12) {
            return false;
        }
        if (day < 1 || day > iaMonthDays[month - 1]) {
            return false;
        }
        return true;
    }

    /**
     * 能打开图片就是有效图片
     * 
     * @param imgBase64
     * @return
     */
    public static boolean isImage(String imgBase64) {
        boolean isImage = false;
        BufferedImage bi = null;
        try {
            bi = ImageIO.read(new ByteArrayInputStream(new BASE64Decoder().decodeBuffer(imgBase64)));
            if (bi != null) {
                isImage = true;
            }
        } catch (IOException e) {
            isImage = false;
        } finally {
            if (bi != null) {
                bi.flush();
                bi = null;
            }
        }
        return isImage;
    }

    /**
     * 通过图片base64流判断图片等于多少字节 image 图片流
     */
    public static Integer imageSize(String image) {
        // 找到等号，把等号也去掉
        Integer equalIndex = image.indexOf("=");
        if (image.indexOf("=") > 0) {
            image = image.substring(0, equalIndex);
        }
        // 原来的字符流大小，单位为字节
        Integer strLength = image.length();
        // 计算后得到的文件流大小，单位为字节
        Integer size = strLength - (strLength / 8) * 2;
        return size;
    }

    /**
     * 判断图片是否超出限制大小 image 图片流
     */
    public static boolean isOverSize(String image) {
        return imageSize(image) > PHOTO_MAX_SIZE_BYTE;
    }

    /**
     * 判断字符串是否只包含数字和字母 str 字符串
     */
    public static boolean isNumberStr(String str) {
        return isRegexMatch(str, REGEX_ALPHANUMERIC);
    }

    /**
     * 判断字符串是否包含特殊字符 str 字符串
     */
    public static boolean isSpecialChar(String str) {
        str = str.trim();
        for (String charStr : UNCHAR) {
            if (str.indexOf(charStr) >= 0) {
                return true;
            }
        }
        return false;
    }

}
