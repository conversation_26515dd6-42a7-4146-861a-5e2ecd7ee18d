/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.util.Map;

import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.remote.PersParamsRemote;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import com.zkteco.zkbiosecurity.system.service.SystemPersInfo2CloudService;

/**
 * 
 * @author: GenerationTools
 * @date: 2018-02-23 下午03:48
 */
@Controller
public class PersParamsController extends BaseController implements PersParamsRemote {
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private SystemPersInfo2CloudService pers2CloudService;
    @Autowired
    private PersTemplateServerService persTemplateServerService;
    @Autowired
    private PersFaceVerifyService persFaceVerifyService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @RequiresPermissions("pers:params:edit")
    @LogRequest(module = "pers_module", object = "common_leftMenu_paramSet", opType = "common_leftMenu_paramSet",
        opContent = "common_leftMenu_paramSet")
    @Override
    public ZKResultMsg save(@RequestParam Map<String, String> params) {
        ZKResultMsg res = new ZKResultMsg();
        // 设置ocr回填证件号码类型
        params.put("pers.ocrCertNoType", params.get("certNoType"));
        params.put("pers.ocrDriveVerson", params.get("driverLink"));
        persParamsService.saveItem(params);
        pers2CloudService.syncPersParamToCloud();
        baseSysParamService.updateFaceVerifyParam2Other();
        return I18nUtil.i18nMsg(res);
    }

    @Override
    public ZKResultMsg getParams() {
        ZKResultMsg res = new ZKResultMsg(persParamsService.getPersParams());
        return I18nUtil.i18nMsg(res);
    }

    @Override
    public String checkMaxPinLenth(@RequestParam("pers.pinLen") int pinLen) {
        long maxPinLen = persPersonService.getMaxPinLenth();
        return String.valueOf(maxPinLen <= pinLen);
    }

    @Override
    public ZKResultMsg checkPinLetters(@RequestParam("pinSupportLetter") String pinSupportLetter) {
        long personCount = persPersonService.getPersonCount();
        if (personCount > 0) {
            return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("pers_param_pinSetWarn"));
        }
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public String checkPinIncrement(String pinSupportIncrement) {
        int count = persPersonService.checkPinIncrement(pinSupportIncrement);
        if (count > 0) {
            return String.valueOf(false);
        }
        return String.valueOf(true);
    }

    @Override
    public String checkIsExistCard() {
        long count = persCardService.count();
        return String.valueOf(count > 0);
    }

    @Override
    public String checkIsExistCards() {
        // 查询是否有副卡
        long count = persCardService.countByCardType(PersConstants.DEPUTY_CARD);
        return String.valueOf(count > 0);
    }

    @Override
    public String checkSingleAttSystem() {
        return String.valueOf(persParamsService.checkSingleAttSystem());
    }

    @RequiresPermissions("pers:params")
    @Override
    public ModelAndView index() {
        return new ModelAndView("pers/params/params");
    }

    @Override
    public ZKResultMsg loginTemplateServer() {
        String address = request.getParameter("address");
        String username = request.getParameter("userName");
        String pwd = request.getParameter("pwd");
        return persTemplateServerService.loginTemplateServer(address, username, pwd);
    }

    @Override
    public ZKResultMsg testTemplateServerConnect() {
        return persTemplateServerService.testTemplateServerStatus();
    }

    @Override
    public ZKResultMsg testFaceServerConnect() {
        return persFaceVerifyService.testFaceVerifyServerStatus();
    }
}