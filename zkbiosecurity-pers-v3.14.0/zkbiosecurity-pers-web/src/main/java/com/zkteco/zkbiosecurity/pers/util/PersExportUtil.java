package com.zkteco.zkbiosecurity.pers.util;

import java.io.*;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.beanutils.ConvertUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.DocumentFactoryHelper;
import org.apache.poi.poifs.filesystem.FileMagic;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.util.StringUtils;

import com.zkteco.zkbiosecurity.base.annotation.DateType;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeRuleItem;

import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.EncryptionMethod;

/**
 * <AUTHOR>
 * @Date: 2019/4/19 11:42
 */
@Slf4j
public class PersExportUtil {

    private static int maxCount = 30002;

    public static Workbook getImportWorkBook(InputStream inputStream) throws IOException {
        Workbook workbook = null;
        // 输入流必须支持mark/reset方法
        if (!inputStream.markSupported()) {
            inputStream = new BufferedInputStream(inputStream);
        }

        // 使用微软Office文件系统,Excel2003
        if (FileMagic.valueOf(inputStream) == FileMagic.OLE2) {
            workbook = new HSSFWorkbook(inputStream);
        } else if (DocumentFactoryHelper.hasOOXMLHeader(inputStream)) {
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            // 先将流读取出来 然后再写入到ByteArrayOutputStream中
            while ((len = inputStream.read(buffer)) > -1) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
            byteArrayOutputStream.flush();

            InputStream processStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            InputStream stream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            ExportReadUtil exportReadUtil = new ExportReadUtil();
            int rowCount = exportReadUtil.processOneSheet(processStream);
            processStream.close();
            processStream = null;
            byteArrayOutputStream.close();
            byteArrayOutputStream = null;
            if (rowCount > maxCount) {
                throw ZKBusinessException.errorException("common_report_maxCount");
            }
            workbook = new XSSFWorkbook(stream);
            stream.close();
            stream = null;
        }
        return workbook;
    }

    public static Workbook createImportWorkBook(InputStream inputStream) {
        Workbook workbook = null;
        try {
            workbook = getImportWorkBook(inputStream);
        } catch (Exception ex) {
            if (ex instanceof ZKBusinessException) {
                throw ZKBusinessException.errorException(ex.getMessage());
            } else {
                throw new ZKBusinessException("Open the EXCEL file flow failure!", ex);
            }
        }
        return workbook;
    }

    public static <T> List<T> excelImport(InputStream inputStream, Class<T> cls) {
        Workbook workbook = createImportWorkBook(inputStream);
        Sheet sheet = workbook.getSheetAt(0);
        if (sheet.getLastRowNum() >= maxCount) {
            throw ZKBusinessException.errorException("common_report_maxCount");
        }
        Field[] fields = cls.getDeclaredFields();
        List<Field> fieldList =
            Arrays.stream(fields).filter(f -> f.getAnnotation(GridColumn.class) != null || f.getType() == Map.class)
                .collect(Collectors.toList());
        Row columnNames = sheet.getRow(1);
        List<T> list = new ArrayList();

        PersAttributeRuleItem fieldAttr = null;
        HashMap<Integer, PersAttributeRuleItem> fieldAttrs = new HashMap<Integer, PersAttributeRuleItem>();// 字段属性标示 key

        Set<String> clunms = new HashSet<>();
        String text = null;
        Field field = null;
        Date date = null;
        Field fieldStr = null;
        DataFormatter formatter = new DataFormatter();
        try {
            for (Row row : sheet) {
                int rowNum = row.getRowNum();

                // 表头 检查批注信息
                if (rowNum == 1) {
                    int columnIndex = 0;

                    for (Cell cell : row) {
                        columnIndex = cell.getColumnIndex();
                        fieldAttr = fieldAttrs.get(columnIndex);

                        Cell columnNameCell = columnNames.getCell(columnIndex);
                        if (StringUtils.isEmpty(formatter.formatCellValue(columnNameCell))) {
                            throw new ZKBusinessException(
                                I18nUtil.i18nCode("pers_import_noComment", (rowNum + 1), (columnIndex + 1)));
                        }
                        Comment comment = columnNameCell.getCellComment();
                        if (StringUtils.isEmpty(comment)) {
                            throw new ZKBusinessException(
                                I18nUtil.i18nCode("pers_import_noComment", (rowNum + 1), (columnIndex + 1)));
                        }

                        text = comment.getString().getString();
                        fieldAttr = new PersAttributeRuleItem(text, rowNum, columnIndex);
                        fieldAttrs.put(columnIndex, fieldAttr);
                        if (!clunms.add(fieldAttr.getName())) {
                            throw new ZKBusinessException(I18nUtil.i18nCode("pers_import_fieldRepeat", (rowNum + 1),
                                (columnIndex + 1), fieldAttr.getName()));
                        }
                    }
                }

                // 导入数据
                else if (rowNum > 1) {
                    T t = cls.newInstance();
                    Map<String, Object> attrMap = new HashMap();
                    for (Cell cell : row) {
                        Cell columnNameCell = columnNames.getCell(cell.getColumnIndex());
                        if (StringUtils.isEmpty(formatter.formatCellValue(columnNameCell))) {
                            new ZKBusinessException("common_report_columnError");
                        }
                        if (columnNameCell != null) {
                            String columnName = formatter.formatCellValue(columnNameCell);
                            fieldAttr = fieldAttrs.get(cell.getColumnIndex());
                            if (fieldAttr.getName().contains("attrMap")) {
                                field = fieldList.stream()
                                    .filter(
                                        f -> (f.getAnnotation(GridColumn.class) == null && f.getType() == Map.class))
                                    .findFirst()
                                    .orElseThrow(() -> new ZKBusinessException("common_report_columnError"));
                            } else {
                                field = fieldList.stream()
                                    .filter(f -> (f.getAnnotation(GridColumn.class) != null && columnName
                                        .equals(I18nUtil.i18nCode(f.getAnnotation(GridColumn.class).label()))))
                                    .findFirst()
                                    .orElseThrow(() -> new ZKBusinessException("common_report_columnError"));
                            }
                            field.setAccessible(true);
                            if (field.getType() == Date.class || field.getType() == Timestamp.class) {
                                DateType dateType = field.getAnnotation(DateType.class);
                                String format = getDateFormt(dateType);
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    if (cell.getNumericCellValue() > 0) {
                                        date = org.apache.poi.ss.usermodel.DateUtil
                                            .getJavaDate(cell.getNumericCellValue());
                                        if (date == null) {
                                            fieldStr = cls.getDeclaredField(field.getName() + "Str");
                                            fieldStr.setAccessible(true);
                                            fieldStr.set(t, PersConstants.PERSON_DATE_ERROR);
                                        } else {
                                            field.set(t, date);
                                        }
                                    }
                                } else {
                                    if (!StringUtils.isEmpty(formatter.formatCellValue(cell))) {
                                        date = DateUtil.stringToDate(formatter.formatCellValue(cell), format);
                                        if (date == null) {
                                            fieldStr = cls.getDeclaredField(field.getName() + "Str");
                                            fieldStr.setAccessible(true);
                                            fieldStr.set(t, PersConstants.PERSON_DATE_ERROR);
                                        } else {
                                            field.set(t, date);
                                        }
                                    } else {
                                        fieldStr = cls.getDeclaredField(field.getName() + "Str");
                                        fieldStr.setAccessible(true);
                                        fieldStr.set(t, PersConstants.PERSON_DATE_SETNULL);
                                    }
                                }
                            } else if (field.getType() == Map.class) {
                                attrMap.put(fieldAttr.getName(), formatter.formatCellValue(cell).replaceAll("&", ","));
                                field.set(t, ConvertUtils.convert(attrMap, field.getType()));
                            } else {
                                if (!StringUtils.isEmpty(formatter.formatCellValue(cell))) {
                                    field.set(t,
                                        ConvertUtils.convert(formatter.formatCellValue(cell), field.getType()));
                                } else {
                                    field.set(t, "");
                                }
                            }
                        }
                    }
                    // 设置行数
                    field = cls.getDeclaredField("rowNum");
                    field.setAccessible(true);
                    field.set(t, ConvertUtils.convert(rowNum + 1, Integer.class));
                    list.add(t);
                }
            }
        } catch (ZKBusinessException e) {
            log.error("import business error", e);
            throw e;
        } catch (Exception e) {
            log.error("import error", e);
            throw new ZKBusinessException("common_op_failed");
        }
        return list;
    }

    public static String getDateFormt(DateType dateType) {
        String format = "yyyy-MM-dd HH:mm:ss";
        if (dateType != null) {
            switch (dateType.type()) {
                case "date":
                    format = "yyyy-MM-dd";
                    break;
                case "time":
                    format = "HH:mm:ss";
                    break;
                case "timestamp":
                    format = "yyyy-MM-dd HH:mm:ss";
                    break;
            }
        }
        return format;
    }

    /**
     * 将指定路径下的文件压缩至指定zip文件，并以指定密码加密,若密码为空，则不进行加密保护
     *
     * <AUTHOR>
     * @param tempPath, files, password, request, response
     * @return javax.servlet.http.HttpServletResponse
     * @date 2020/3/5 9:00
     */
    public static HttpServletResponse excelEncryptZip(String tempPath, ArrayList<File> files, String password,
        HttpServletRequest request, HttpServletResponse response) throws Exception {
        ZipParameters zipParameters = new ZipParameters();
        // 设置加密
        zipParameters.setEncryptFiles(true);
        // 设置加密方法
        zipParameters.setEncryptionMethod(EncryptionMethod.AES);
        // 设置AES加密强度
        zipParameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);

        ZipFile zipFile = new ZipFile(tempPath, password.toCharArray());
        zipFile.addFiles(files, zipParameters);
        return downloadZip(zipFile.getFile(), response);

    }

    public static HttpServletResponse downloadZip(File file, HttpServletResponse response) {
        InputStream fis = null;
        OutputStream toClient = null;
        try {
            fis = new BufferedInputStream(new FileInputStream(file.getPath()));
            response.reset();
            toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + file.getName());
            byte[] buffer = new byte[1024];

            int i;
            while ((i = fis.read(buffer)) != -1) {
                toClient.write(buffer, 0, i);
            }
            return response;
        } catch (IOException var9) {
            log.error("exception:", var9);
        } finally {
            try {
                fis.close();
                toClient.flush();
                toClient.close();
            } catch (IOException e) {
                log.error("exception:", e);
            }
        }
        return null;
    }
}
