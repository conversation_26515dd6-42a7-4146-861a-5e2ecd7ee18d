package com.zkteco.zkbiosecurity.pers.app.controller;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.AppPersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.AppUserPicItem;
import com.zkteco.zkbiosecurity.pers.service.PersAppBioService;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.util.PersCheckUtil;
import com.zkteco.zkbiosecurity.pers.util.ValidatorsUtils;

@Controller
@RequestMapping(value = {"/token/register"})
public class PersAppRegisterController extends BaseController {

	@Autowired
	private PersAppBioService persAppBioService;
	@Autowired
	private PersParamsService persParamsService;
	@Autowired
	private PersPersonService persPersonService;

	@RequestMapping(value = "/saveUser", method = RequestMethod.POST, produces = "application/json")
	@ResponseBody
	public ZKResultMsg register(@RequestBody AppPersonItem appPersonItem) {
		PersCheckUtil.checkAppPerson(appPersonItem, null);
		if (StringUtils.isBlank(appPersonItem.getPassword())) {
			//人员密码不能为空
			throw ZKBusinessException.errorException("base_userService_setUser09");
		}
		return I18nUtil.i18nMsg(persAppBioService.savePersonRegister(appPersonItem));
	}

	@RequestMapping(value = "/saveUserPic", method = RequestMethod.POST, produces = "application/json")
	@ResponseBody
	public ZKResultMsg saveUserPic(@RequestBody AppUserPicItem item) {
		if (StringUtils.isBlank(item.getMobile())) {
			return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_person_mobilePhoneValidate"));
		}
		if (StringUtils.isBlank(item.getPhotoBase64())) {
			return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_h5_personAvatarNotNull"));
		}
		String photoBase64 = item.getPhotoBase64().replaceAll("[\\s*\t\n\r]", "");
		if (!ValidatorsUtils.isImage(photoBase64)) {
			//无效图片
			return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_api_selectPhotoInvalid"));
		}
		if (ValidatorsUtils.isOverSize(photoBase64)) {
			//图片超出大小
			return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_h5_imgSizeError"));
		}
		return I18nUtil.i18nMsg(persAppBioService.savePersonPic(item.getMobile(), photoBase64));
	}

	/**
	 * 获取自增pin
	 * 
	 * <AUTHOR>
	 * @since 2019年6月25日 上午9:58:11
	 * @return
	 */
	@RequestMapping(value = "/getPersPin", method = RequestMethod.GET)
	@ResponseBody
	public ZKResultMsg getPersPin() {
		ZKResultMsg result = ZKResultMsg.successMsg();
		Map<String, String> paramMap = persParamsService.getPersParams();
		if (!paramMap.isEmpty()) {
			//人员编号是否支持字母参数
			String pinSupportLetter = paramMap.get("pers.pinSupportLetter");
			//人员编号是否自动递增参数
			String pinSupportIncrement = paramMap.get("pers.pinSupportIncrement");
			//新增页面;人员pin号不支持字母且支持pin号自增
			if ("false".equals(pinSupportLetter) && "true".equals(pinSupportIncrement)) {
				//取redis中保存的自增人员编号
				String pin = persPersonService.getIncPoint();
				if (StringUtils.isNotEmpty(pin)) {
					JSONObject pinObject = new JSONObject();
					pinObject.put("pin", pin);
					result.setData(pinObject);
				}
			}
		}
		return result;
	}

	/**
	 * 获取注册验证参数
	 * 
	 * <AUTHOR>
	 * @since 2019年6月25日 上午9:57:58
	 * @return
	 */
	@RequestMapping(path = "/getPersParams", method = RequestMethod.GET, produces = "application/json")
	@ResponseBody
	public ZKResultMsg getPersParams() {
		return persAppBioService.getPersParams();
	}

	/**
	 * 发送修改密码的邮件
	 * 
	 * <AUTHOR>
	 * @since 2019年6月27日 下午6:41:13
	 * @param pin
	 * @param email
	 * @return
	 */
	@RequestMapping(value = "/sendEmail", method = RequestMethod.POST, produces = "application/json")
	@ResponseBody
	public ZKResultMsg sendEmail(@RequestBody AppPersonItem appPersonItem) {
		String pin = appPersonItem.getPin();
		String email = appPersonItem.getEmail();
		if (StringUtils.isBlank(pin) || StringUtils.isBlank(email)) {
			return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_h5_pinOrEmailIsNull"));
		}
		return persAppBioService.sendEmailForResetPassword(pin, email, request.getHeader("Host"));
	}
}
