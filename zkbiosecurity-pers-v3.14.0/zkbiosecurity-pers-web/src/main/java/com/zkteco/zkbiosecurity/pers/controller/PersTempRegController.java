package com.zkteco.zkbiosecurity.pers.controller;

import java.util.Arrays;
import java.util.stream.Collectors;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.controller.BaseController;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.remote.PersTempRegRemote;
import com.zkteco.zkbiosecurity.pers.service.PerTempRegService;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.vo.PerTempRegItem;

/**
 * 临时人员注册以及移动端注册页面展示
 * 
 * <AUTHOR> href="mailto:<EMAIL>">colin.cheng</a>
 * @version V1.0
 * @date Created In 13:34 2019/3/28
 */
@Controller
public class PersTempRegController extends BaseController implements PersTempRegRemote {

    @Value("${system.language}")
    private String sysLanguage;
    @Autowired
    private PerTempRegService perRegSerice;
    @Autowired
    private PersParamsService persParamsService;

    @Override
    public ZKResultMsg registrarLogin(@RequestBody PerTempRegItem item) {
        // 过滤
        item.setPhone(item.getPhone().replaceAll("/[^\\d-]/g", ""));
        ZKResultMsg retMsg = new ZKResultMsg();
        perRegSerice.perReg(item);
        return I18nUtil.i18nMsg(retMsg);
    }

    @Override
    public ModelAndView regView() {
        String selfRegistration = persParamsService.getValByName("pers.selfRegistration");
        // 禁用自助登记
        if (PersConstants.PERSON_SELFREGISTRATION_DISABLED.equals(selfRegistration)) {
            return new ModelAndView(PersConstants.RESPONSE_NOT_FOUND);
        }
        String privacy = request.getParameter("privacy");
        if (!"zh_CN".equals(this.sysLanguage) && StringUtils.isBlank(privacy)) {
            ModelAndView mv = new ModelAndView("pers/tempPerson/persPrivacyAgreement");
            mv.addObject("language", this.sysLanguage);
            String privacyContent = perRegSerice.getPrivacyContent();
            mv.addObject("privacy", privacyContent);
            return mv;
        }

        String enableHealthInfo = persParamsService.getValByName("pers.enableHealthInfo");
        String exposure = request.getParameter("exposure");
        String[] symptom = request.getParameterValues("symptom");
        String visitCity = request.getParameter("visitCity");
        String healthRemarks = request.getParameter("healthRemarks");
        // 如果有启用健康信息申报,先跳转到健康信息申报的页面
        if ("1".equals(enableHealthInfo) && StringUtils.isBlank(exposure) && StringUtils.isBlank(visitCity)
            && (symptom == null || symptom.length == 0)) {
            ModelAndView mv = new ModelAndView("pers/tempPerson/persTempHealth");
            mv.addObject("language", sysLanguage);
            return mv;
        } else {
            String symptomStr = symptom != null ? Arrays.stream(symptom).collect(Collectors.joining(",")) : null;
            String pinLen = persParamsService.getValByName("pers.pinLen");
            String pinSupportLetter = persParamsService.getValByName("pers.pinSupportLetter");
            ModelAndView mv = new ModelAndView("pers/tempPerson/persTempReg");
            mv.addObject("language", sysLanguage);
            mv.addObject("pinLen", pinLen);
            mv.addObject("pinSupportLetter", pinSupportLetter);
            mv.addObject("enableHealthInfo", enableHealthInfo);
            mv.addObject("exposure", exposure);
            mv.addObject("symptom", symptomStr);
            mv.addObject("visitCity", visitCity);
            mv.addObject("healthRemarks", healthRemarks);
            return mv;
        }
    }

    /**
     * 获取cookie
     * 
     * @param name cookie名
     * @param request
     * @return
     */
    protected String getCookie(String name, HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(name)) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }
}
