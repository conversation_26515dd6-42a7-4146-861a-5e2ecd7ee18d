/*
 * @author: GenerationTools
 * 
 * @date: 2018-02-23 下午03:48 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.controller;

import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.base.annotation.LogRequest;
import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.format.TreeBuilder;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.ExportController;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.remote.PersPersonRemote;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.util.PersBigExcelUtil;
import com.zkteco.zkbiosecurity.pers.util.PersExportInfoUtil;
import com.zkteco.zkbiosecurity.pers.util.PersExportTemplateUtil;
import com.zkteco.zkbiosecurity.pers.util.PersExportUtil;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.security.annotation.RequiresPermissions;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * @author: GenerationTools
 * @date: 2018-02-23 下午03:48
 */
@Controller
public class PersPersonController extends ExportController implements PersPersonRemote {
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private PersCertificateService persCertificateService;
    @Autowired
    private PersAttributeService persAttributeService;
    @Autowired
    private PersAttributeExtService persAttributeExtService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Value("${security.session.timeout:1800}")
    private Long sessionTimeout;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private PersExportTemplateUtil persExportTemplateUtil;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private PersPersonnalListService persPersonnalListService;
    @Autowired
    private PersBioPhotoService persBioPhotoService;
    @Autowired
    private PersCardService persCardService;

    @Autowired
    private HttpServletRequest request;
    @Autowired
    private PersTemplateServerService persTemplateServerService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @RequiresPermissions("pers:person")
    @Override
    public ModelAndView index() {
        return new ModelAndView("pers/person/persPerson");
    }

    @RequiresPermissions({"pers:person:edit", "pers:person:add"})
    @Override
    public ModelAndView edit(@RequestParam(value = "id", required = false) String id,
        @RequestParam(value = "leaveId", required = false) String leaveId,
        @RequestParam(value = "deptId", required = false) String deptId,
        @RequestParam(value = "cardNo", required = false) String cardNo) {
        String bioTemplateJson = "";
        Map attr = null;
        Map<String, String> persParams = persParamsService.getPersParams();
        if (StringUtils.isNotBlank(id)) {
            PersPersonItem item = persPersonService.getItemById(id);
            if (Objects.isNull(item)) {
                throw new ZKBusinessException("common_prompt_exception_datanoexists");
            }
            String mobilePhone = item.getMobilePhone();
            if (!"zh_CN".equals(LocaleMessageSourceUtil.language) && StringUtils.isNotBlank(mobilePhone)) {
                if (mobilePhone.split("-").length > 1) {
                    item.setAreaCode(mobilePhone.split("-")[0]);
                    item.setMobilePhone(mobilePhone.split("-")[1]);
                }
            }
            // 设置缩略图处理，并且判断文件是否存在。
            if (StringUtils.isNotBlank(item.getPhotoPath())) {
                // 图片解密
                String thumbPath = FileUtil.getThumbPath(item.getPhotoPath());
                if (FileUtil.fileExists(thumbPath)) {
                    // 设置缩略图地址
                    item.setThumbPhotoPath(thumbPath);
                    String photoBase64 = FileEncryptUtil.getDecryptFileBase64(thumbPath);
                    // 设置base64数据
                    item.setPhotoBase64(PersConstants.PERSON_BASE64_PREFIX + photoBase64);
                } else {
                    // 设置base64数据
                    String photoBase64 = FileEncryptUtil.getDecryptFileBase64(thumbPath);
                    item.setPhotoBase64(PersConstants.PERSON_BASE64_PREFIX + photoBase64);
                }
            }

            List<PersBioPhotoItem> persBioPhotoItemList = persBioPhotoService.findByPersonIdIn(StrUtil.strToList(id));
            if (!persBioPhotoItemList.isEmpty()) {
                PersBioPhotoItem bioPhotoItem = persBioPhotoItemList.get(0);
                String cropPhotoBase64 = FileEncryptUtil.getDecryptFileBase64(bioPhotoItem.getPhotoPath());
                item.setCropPhotoBase64(cropPhotoBase64);
            }

            request.setAttribute("item", item);
            request.setAttribute("certificate", persCertificateService.getItemByPersonId(id));
            request.setAttribute("cardItems", persCardService.getAllCardByPersonIdList(StrUtil.strToList(id)));
            // request.setAttribute("cropFaceCount", FileUtil.getCropFaceCount(item.getPin()));
            PersAttributeExtItem extItem = persAttributeExtService.getItemByPersonId(id);
            if (Objects.nonNull(extItem)) {
                attr = BeanMap.create(extItem);
            }
            bioTemplateJson = JSON.toJSONString(persBioTemplateService.getItemMapByPersonId(id));
        } else if (StringUtils.isNotBlank(leaveId)) {
            // 离职复职
            PersLeavePersonItem leaveItem = persLeavePersonService.getItemById(leaveId);
            if (Objects.isNull(leaveItem)) {
                throw new ZKBusinessException("common_prompt_exception_datanoexists");
            }
            PersPersonItem item = new PersPersonItem();
            BeanUtils.copyProperties(leaveItem, item, "id");
            request.setAttribute("item", item);
            request.setAttribute("attGroupId", leaveItem.getAttGroupId());
            request.setAttribute("isAttendance", leaveItem.getIsAttendance());
            request.setAttribute("leaveId", leaveId);
        } else {
            // 不支持字符，并且开启pin自增
            if ("false".equals(persParams.get("pers.pinSupportLetter"))
                && "true".equals(persParams.get("pers.pinSupportIncrement"))) {
                request.setAttribute("defPin", persPersonService.getIncPoint());
            }
        }
        List<PersAttributeItem> attributes = persAttributeService.getByCondition(new PersAttributeItem());
        for (PersAttributeItem item : attributes) {
            String attrName = I18nUtil.i18nCode("pers_attr_" + item.getAttrName());
            if (StringUtils.isNotBlank(attrName) && !attrName.contains("pers_attr_")) {
                item.setShowName(attrName);
            }
            if (Objects.nonNull(attr)) {
                item.setVal(MapUtils.getString(attr, "attrValue" + item.getFiledIndex(), ""));
            }
        }

        request.setAttribute("attributes", attributes);
        request.setAttribute("persParams", persParams);
        request.setAttribute("treeDeptId", deptId);
        request.setAttribute("treeDeptName", request.getParameter("deptName"));
        request.setAttribute("cardNo", cardNo);
        request.setAttribute("bioTemplateJson", bioTemplateJson);

        if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
            return new ModelAndView("pers/person/editPersPersonZH");
        }
        request.setAttribute("showSMS", persPersonService.checkShowSMS());
        request.setAttribute("showWhatsapp", persPersonService.checkShowWhatsapp());
        return new ModelAndView("pers/person/editPersPerson");
    }

    @RequiresPermissions({"pers:person:edit", "pers:person:add"})
    @LogRequest(module = "pers_module", object = "pers_person", opType = "common_op_edit",
        requestParams = {"pin", "name", "lastName"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg save(PersPersonItem item, PersCertificateItem certificate, PersAttributeExtItem attributeExt,
        @RequestParam Map<String, String> extParams,
        @RequestParam(value = "personPhoto", required = false) MultipartFile file) {
        if (StringUtils.isBlank(item.getId()) && !persPersonService.persLicenseCheck()) {
            throw ZKBusinessException.warnException("common_license_maxCount");
        }
        if (StringUtils.isBlank(item.getId()) && item.getEnabledCredential() != null && item.getEnabledCredential()) {
            throw ZKBusinessException.warnException(I18nUtil.i18nCode("pers_person_disabledNotOp"));
        }
        ZKResultMsg res = new ZKResultMsg();
        certificate.setId(null);
        attributeExt.setId(null);
        if (Objects.nonNull(file) && !file.isEmpty()) {
            String photoPath = FileUtil.saveFileToServer("pers", "user/avatar", item.getPin() + ".jpg", file);
            item.setPhotoPath(photoPath);
        } else if (StringUtils.isNotBlank(extParams.get("personIdPhoto"))) {
            String photoPath = FileUtil.saveFileToServer("pers", "user/avatar", item.getPin() + ".jpg",
                extParams.get("personIdPhoto"));
            item.setPhotoPath(photoPath);
        }

        // 前端更新比对照片
        if (StringUtils.isNotBlank(item.getCropPhotoBase64())) {
            String cropPhotoPath =
                FileUtil.saveCropFaceToServer(item.getPin(), item.getPin() + ".jpg", item.getCropPhotoBase64());
            item.setCropPhotoPath(cropPhotoPath);
        } else {
            // 前端删除比对照片
            if (item.getCropPhotoDel() != null && item.getCropPhotoDel()) {
                item.setCropPhotoPath("");
            }
            // 比对照片没有更改
            else {
                item.setCropPhotoPath(null);
                // 前端更新头像不生成比对照片
                if (StringUtils.isNotBlank(item.getPhotoPath())) {
                    item.setCropPhotoBase64("");
                }
            }
        }

        if (StringUtils.isBlank(item.getId())) {
            // 手动新增，记录数据来源
            item.setIsFrom("PERS_USER_MANUALLY_ADDED");
        }
        String sessionId = request.getSession().getId();
        extParams.put("sessionId", sessionId);
        // 修改火狐浏览器记住密码人员新增页面邮箱信息自动填充为当前系统登录的用户名
        String email = extParams.get("mail");
        item.setEmail(email);
        persPersonService.saveItem(item, certificate, attributeExt, extParams);
        return I18nUtil.i18nMsg(res);
    }

    @RequiresPermissions("pers:person:del")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "common_op_del", requestParams = {"pins"},
        opContent = "pers_person_pin")
    @Override
    public ZKResultMsg del(@RequestParam("ids") String ids) {
        persPersonService.deleteByIds(ids);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("pers:person")
    @Override
    public DxGrid list(PersPersonItem condition) {
        condition.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        Pager pager = persPersonService.loadPagerByAuthUserFilter(request.getSession().getId(), condition, getPageNo(),
            getPageSize());
        persPersonService.protectPinAndCard((List<PersPersonItem>)pager.getData());
        return GridUtil.convert(pager, condition.getClass());
    }

    @Override
    public String checkPwd(@RequestParam("personId") String personId, @RequestParam("personPwd") String personPwd) {

        PersPersonItem item = new PersPersonItem(true);
        if (StringUtils.isNotBlank(personId)) {
            // 查找密码排除自己本人
            item.setNotInId(personId);
        }
        if (StringUtils.isNotBlank(personPwd)) {
            item.setPersonPwd(personPwd);
        }
        boolean persPersonflag = false;
        if (StringUtils.isNotBlank(personId)) {
            // 编辑时判断需要过滤人员ID
            persPersonflag = !persPersonService.checkPwd(personId, personPwd);
        } else {
            persPersonflag = !persPersonService.ckeckPwd(personPwd);
        }
        if (persPersonflag) {
            persPersonflag = !persPersonService.checkForcePwd(personPwd);
        }
        return String.valueOf(persPersonflag);
    }

    @Override
    public String isExist(@RequestParam("pin") String pin) {
        boolean isExist = true;
        if (!persPersonService.isExistPin(pin)) {
            String pinRetain = persParamsService.getValByName("pers.pinRetain");
            // 保留人员编号且离职人员id为空，校验人员离职表
            if (StringUtils.isBlank(request.getParameter("leavePersonId")) && "true".equals(pinRetain)) {
                PersLeavePersonItem leaveItem = persLeavePersonService.getItemByPin(pin);
                if (leaveItem != null) {
                    isExist = false;
                }
            }
        } else {
            isExist = false;
        }
        return String.valueOf(isExist);
    }

    @Override
    public ZKResultMsg dataCount() {
        String deptIds = request.getParameter("deptIds");
        Map<String, String> mapCount =
            persPersonService.dataCountByDeptIdAndAuthUserFilter(request.getSession().getId(), deptIds);
        return I18nUtil.i18nMsg(new ZKResultMsg(mapCount));
    }

    @Override
    public ModelAndView batch(@RequestParam("ids") String ids, @RequestParam("type") String type) {
        StringBuffer pinStrBuf = new StringBuffer();
        String[] idsArr = ids.split(",");
        for (int i = 0; i < idsArr.length; i++) {
            PersPersonItem tempPerson = persPersonService.getItemById(idsArr[i]);
            if (i != idsArr.length - 1) {
                pinStrBuf.append(tempPerson.getPin() + ";");
            } else {
                pinStrBuf.append(tempPerson.getPin());
            }
        }
        request.setAttribute("selectedPerson", pinStrBuf.toString());
        request.setAttribute("ids", ids);
        return new ModelAndView("pers/person/" + type);
    }

    @RequiresPermissions("pers:person:batchPositionChange")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_position_change",
        requestParams = {"pins", "positionName", "changeReason"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg batchPositionChange(@RequestParam("ids") String ids,
        @RequestParam("positionId") String positionId, @RequestParam("changeReason") String changeReason) {
        persPersonService.batchPositionChange(ids, positionId, changeReason);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @RequiresPermissions("pers:person:deptChange")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_person_departmentChange",
        requestParams = {"pins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg batchDeptChange(@RequestParam("ids") String ids, @RequestParam("deptId") String deptId,
        @RequestParam("changeReason") String changeReason, @RequestParam("flag") boolean flag) {
        persPersonService.batchDeptChange(ids, deptId, changeReason, flag);
        return I18nUtil.i18nMsg(new ZKResultMsg());
    }

    @Override
    public String checkCertNumber(@RequestParam("certNumber") String certNumber,
        @RequestParam(value = "personId", required = false) String personId,
        @RequestParam("certType") String certType) {
        boolean flag = true;
        if (StringUtils.isNotBlank(certNumber)) {
            flag = persCertificateService.isExistByCertNumberAndCertTypeAndPersonIdNe(certNumber, certType, personId);
        }
        return String.valueOf(flag);
    }

    @Override
    public String checkMailParam() {
        return String.valueOf(persPersonService.checkMailParam());
    }

    @Override
    public String checkEmailIsExist(String personId, String email) {
        PersPersonItem persPersonItem = new PersPersonItem();
        persPersonItem.setEmail(email);
        List<PersPersonItem> byCondition = persPersonService.getByCondition(persPersonItem);
        for (PersPersonItem item : byCondition) {
            if (!personId.equals(item.getId())) {
                return "false";
            }
        }
        return "true";
    }

    @RequiresPermissions("pers:person:import")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_import_personInfo", requestParams = {},
        opContent = "pers_import_personInfo")
    @Override
    public ZKResultMsg importExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<PersPersonImportItem> itemList =
                PersExportUtil.excelImport(upload.getInputStream(), PersPersonImportItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            // 是否更新已存在数据
            boolean updateExistData = false;
            String isupdateExistData = request.getParameter("updateExistData");
            if ("1".equals(isupdateExistData)) {
                updateExistData = true;
            }
            return I18nUtil.i18nMsg(persPersonService.importPersonInfo(itemList, updateExistData));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import Person Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @RequiresPermissions("pers:person:export")
    @Override
    public void export(HttpServletRequest request, HttpServletResponse response) {
        String sortName = request.getParameter("sortName");
        String sortOrder = request.getParameter("sortOrder");
        // 验证用户登录密码
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = persPersonService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }

        PersPersonItem persPersonItem = new PersPersonItem();
        setConditionValue(persPersonItem);
        persPersonItem.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        if (StringUtils.isNotBlank(sortName) && StringUtils.isNotBlank(sortOrder)) {
            persPersonItem.setSortName(sortName);
            persPersonItem.setSortOrder(sortOrder);
        }
        String[] attrs = request.getParameterValues("attrs");
        String reportType = request.getParameter("reportType");
        // 根据前端条件构造子部门相关的查询
        persPersonService.buildCondition(request.getSession().getId(), persPersonItem);
        List<PersPersonItem> itemList =
            persPersonService.getItemData(PersPersonItem.class, persPersonItem, getBeginIndex(), getEndIndex());
        List<PersPersonExportItem> persPersonExportItemList = persPersonService.analyzeExportData(itemList);
        // 数据集为空，提示用户无法导出
        if (persPersonExportItemList == null) {
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
            stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
            response.setHeader("Content-Disposition", "");
            response.setContentType("application/json");
            throw new ZKBusinessException("common_report_dataSourceNull");
        }
        try {
            if ("XLS".equals(reportType)) {
                String fileNamePrefix = "";
                // 先找下是否有查询的表名，xx的开门人员（双列表）
                if ((String)request.getParameter("tableNameSearch") != null
                    && !"".equals((String)request.getParameter("tableNameSearch"))) {
                    fileNamePrefix = (String)request.getParameter("tableNameSearch");
                } else if ((String)request.getParameter("tableNameParam") != null
                    && !"".equals((String)request.getParameter("tableNameParam"))) {
                    fileNamePrefix = (String)request.getParameter("tableNameParam");
                }
                // 如果出现加密的话，需要一个临时变量用来存储文件名，防止后续在做文件编码转化时报错
                String excelFileNamePrefix = fileNamePrefix;
                String isEncrypt = request.getParameter("isEncrypt");
                String encryptPassword = request.getParameter("encryptPassword");

                String agent = request.getHeader("User-Agent");
                boolean isMSIE = (agent != null && (agent.indexOf("MSIE") != -1 || agent.indexOf("Trident") != -1));
                // IE浏览器
                if (isMSIE) {
                    fileNamePrefix = java.net.URLEncoder.encode(fileNamePrefix, "UTF-8");
                    fileNamePrefix = fileNamePrefix.replaceAll("\\+", " "); // IE会将空格转成+号
                } else {
                    fileNamePrefix = new String(fileNamePrefix.getBytes("UTF-8"), "ISO-8859-1");
                }
                if (request.getHeader("User-Agent") != null && request.getHeader("User-Agent").indexOf("Edge") != -1) {// Edge浏览器兼容处理
                    fileNamePrefix = fileNamePrefix.replaceAll(" ", "%20");// 空格预处理
                    fileNamePrefix = URLEncoder.encode(fileNamePrefix, "ISO-8859-1");// 编码转换
                    fileNamePrefix = fileNamePrefix.replace("%2520", " ");// 空格还原
                }

                if (StringUtils.isNotBlank(isEncrypt) && ExcelUtil.ENCRYPT_PASSWORD_YES.equals(isEncrypt)) {
                    String fileName = ClassUtil.getRootPath() + File.separator + FileUtils.systemFilePath
                        + File.separator + "upload" + File.separator + excelFileNamePrefix + "_"
                        + (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date()) + "." + reportType.toLowerCase();
                    // 创建文件
                    File folder = new File(fileName);
                    if (!folder.getParentFile().exists()) {
                        folder.getParentFile().mkdirs();
                    }
                    FileOutputStream outputStream = new FileOutputStream(fileName);
                    try {
                        PersBigExcelUtil.export(persPersonExportItemList, PersPersonExportItem.class, attrs,
                            excelFileNamePrefix, outputStream);
                    } catch (Exception var18) {
                        throw new ZKBusinessException("common_op_failed");
                    } finally {
                        if (outputStream != null) {
                            outputStream.flush();
                            outputStream.close();
                        }

                    }
                    ArrayList<File> files = new ArrayList<>();
                    File file = new File(fileName);
                    if (file.exists()) {
                        files.add(file);
                    }
                    // 导出的压缩包路径
                    String tempPath = ClassUtil.getRootPath() + File.separator + FileUtils.systemFilePath
                        + File.separator + "upload" + File.separator + fileNamePrefix + "_"
                        + (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date()) + "." + "zip";

                    response = PersExportUtil.excelEncryptZip(tempPath, files, encryptPassword, request, response);
                    response.flushBuffer();
                    // 删除临时文件夹
                    File zipTempFile = new File(tempPath);
                    if (zipTempFile.exists()) {
                        // 删除临时压缩包
                        zipTempFile.delete();
                    }
                    for (File f : files) {
                        if (f.exists()) {
                            // 删除文件
                            f.delete();
                        }
                    }
                } else {
                    /*----------页面reponse设置----------*/
                    response.setCharacterEncoding("UTF-8");
                    response.setHeader("Content-Disposition",
                        "attachment;filename=\"" + fileNamePrefix + "_"
                            + (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date()) + "."
                            + reportType.toLowerCase() + "\"");
                    response.setContentType("application/octet-stream");
                    OutputStream outputStream = response.getOutputStream();
                    try {
                        PersBigExcelUtil.export(persPersonExportItemList, PersPersonExportItem.class, attrs,
                            excelFileNamePrefix, outputStream);
                    } catch (Exception var18) {
                        throw new ZKBusinessException("common_op_failed");
                    } finally {
                        if (outputStream != null) {
                            outputStream.flush();
                            outputStream.close();
                        }
                    }
                }
                stringRedisTemplate.opsForValue().set("export-result:" + request.getSession().getId(), "end");
                stringRedisTemplate.expire("export-result:" + request.getSession().getId(), this.sessionTimeout,
                    TimeUnit.SECONDS);
                return;
            }

            JSONObject jsonColumn = new JSONObject(true);
            Map<String, Map<String, String>> jsonCloumnMap = new HashMap<>();
            Map<String, String> jsonMap = new HashMap<>();

            Map<String, String> attributeExtMap = new LinkedHashMap<>();
            for (int i = 0; i < attrs.length; i++) {

                String[] attr = attrs[i].split("-");
                String[] paramArray = attr[1].split("\\.");
                if (paramArray.length < 2)// 没有关联属性，直接反射
                {
                    jsonColumn.put(attr[1], attr[0]);
                } else {
                    jsonColumn.put(paramArray[0], attr[0]);
                    attributeExtMap.put(attr[0], paramArray[1]);
                }
            }
            jsonMap.put("jsonColumn", jsonColumn.toJSONString());
            jsonCloumnMap.put("jsonColumn", jsonMap);

            Map<String, String> attrMap = null;
            Object attrValue = null;
            for (PersPersonExportItem person : persPersonExportItemList) {

                attrMap = new LinkedHashMap<>();
                // 自定义属性
                if (!attributeExtMap.isEmpty() && person.getAttributeExt() != null) {
                    for (String key : attributeExtMap.keySet()) {
                        attrValue =
                            PersExportInfoUtil.getFieldValue(person.getAttributeExt(), attributeExtMap.get(key));
                        attrMap.put(key, attrValue != null ? attrValue.toString().replaceAll(",", "&") : "");
                    }
                }
                person.setAttrMap(attrMap);
            }
            excelExport(persPersonExportItemList, PersPersonExportItem.class, jsonCloumnMap);
        } catch (Exception e) {
            log.error("export error", e);
            throw new ZKBusinessException("common_report_exportFaild");
        }
    }

    @RequiresPermissions("pers:person:exportTemplate")
    @Override
    public void exportTemplate(HttpServletRequest request, HttpServletResponse response) {
        try {
            String[] attrs = request.getParameterValues("attrs");
            String reportType = request.getParameter("reportType");
            Map<String, PersAttributeRuleItem> persAttributeRules = PersExportInfoUtil.getPersAttributeRule();
            String fileNamePrefix = I18nUtil.i18nCode("pers_export_templateFileName");
            HSSFWorkbook workbook = new HSSFWorkbook();
            HSSFSheet sheet = workbook.createSheet(fileNamePrefix);
            sheet.setDefaultColumnWidth(attrs.length);
            HSSFPatriarch patriarch = sheet.createDrawingPatriarch();

            HSSFCellStyle style = workbook.createCellStyle();
            style.setAlignment(HorizontalAlignment.CENTER);
            HSSFFont font = workbook.createFont();
            style.setVerticalAlignment(VerticalAlignment.CENTER);
            style.setAlignment(HorizontalAlignment.CENTER);
            font.setFontHeightInPoints((short)14);// 设置字体大小
            style.setFont(font);

            // XSSFDrawing
            // 创建表头
            HSSFRow row = sheet.createRow(0);
            HSSFCell cell = row.createCell(0);
            cell.setCellValue(fileNamePrefix);
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, attrs.length - 1));
            cell.setCellStyle(style);
            row.setHeightInPoints(20);
            row.setHeight((short)(27 * 20));

            row = sheet.createRow(1);
            row.setHeightInPoints(20);
            row.setHeight((short)(27 * 20));

            for (int i = 0; i < attrs.length; i++) {
                sheet.setColumnWidth(i, 150 * 36);
                cell = row.createCell(i);
                String[] attr = attrs[i].split("-");
                PersAttributeRuleItem persAttr = persAttributeRules.get(attr[1]);
                if (persAttr == null) {
                    persAttr = new PersAttributeRuleItem(attr[0]);
                }
                attr[0] = attr[0].replaceAll("\\+", " ");
                cell.setCellValue(attr[0]);// 表头，字段名
                cell.setCellStyle(style);

                String commentStr = I18nUtil.i18nCode("pers_export_templateCommentName", attr[1]);
                Map<String, String> commentMap = PersBigExcelUtil.getPersAttributeComment();
                String attrComment = commentMap.get(attr[1]);
                if (StringUtils.isNotBlank(attrComment)) {
                    commentStr += attrComment;
                }
                HSSFComment comment =
                    patriarch.createCellComment(new HSSFClientAnchor(0, 0, 0, 0, (short)1, 2, (short)4, 8));
                comment.setString(new HSSFRichTextString(commentStr));// 批注内容
                cell.setCellComment(comment);
            }

            /*----------页面reponse设置----------*/
            response.setCharacterEncoding("UTF-8");
            String agent = request.getHeader("User-Agent");
            boolean isMSIE = (agent != null && (agent.indexOf("MSIE") != -1 || agent.indexOf("Trident") != -1));
            // IE浏览器
            if (isMSIE) {
                fileNamePrefix = java.net.URLEncoder.encode(fileNamePrefix, "UTF-8");
                fileNamePrefix = fileNamePrefix.replaceAll("\\+", " "); // IE会将空格转成+号
            } else {
                fileNamePrefix = new String(fileNamePrefix.getBytes("UTF-8"), "ISO-8859-1");
            }
            if (request.getHeader("User-Agent") != null && request.getHeader("User-Agent").indexOf("Edge") != -1) {// Edge浏览器兼容处理
                fileNamePrefix = fileNamePrefix.replaceAll(" ", "%20");// 空格预处理
                fileNamePrefix = URLEncoder.encode(fileNamePrefix, "ISO-8859-1");// 编码转换
                fileNamePrefix = fileNamePrefix.replace("%2520", " ");// 空格还原
            }
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileNamePrefix + "_"
                + (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date()) + "." + reportType.toLowerCase() + "\"");
            response.setContentType("application/vnd.ms-excel");
            OutputStream outStream = response.getOutputStream();
            workbook.write(outStream);
            stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
            stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), this.sessionTimeout,
                TimeUnit.SECONDS);
            return;
        } catch (Exception e) {
            log.error("export error", e);
            throw new ZKBusinessException("common_report_exportFaild");
        }
    }

    @Override
    public String getDeptPins(String deptIds) {
        JSONObject jsonObject = new JSONObject();
        List<String> dptIds = Arrays.asList(deptIds.split(","));
        List<PersPersonItem> persPersonItems = persPersonService.getPersPersonByDeptIds(dptIds);
        StringBuilder pins = new StringBuilder();
        StringBuilder persIds = new StringBuilder();
        int index = 1;
        for (PersPersonItem persPersonItem : persPersonItems) {
            if (index != persPersonItems.size()) {
                pins.append(persPersonItem.getPin() + ",");
                persIds.append(persPersonItem.getId() + ",");
            } else {
                pins.append(persPersonItem.getPin());
                persIds.append(persPersonItem.getId());
            }
            index++;
        }
        jsonObject.put("pins", pins);
        jsonObject.put("persIds", persIds);
        return jsonObject.toJSONString();
    }

    @RequiresPermissions("pers:person:resetSelfPwd")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_person_resetSelfPwd",
        requestParams = {"pins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg resetSelfPwd(String ids) {
        persPersonService.resetSelfPwd(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("pers:person:importPhoto")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_import_personPhoto",
        requestParams = {"pin"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg savePhoto(@RequestParam("pin") String pin,
        @RequestParam(value = "photoQuality", defaultValue = "false") Boolean photoQuality,
        @RequestParam(value = "personPhoto") MultipartFile personPhoto) {
        if (persPersonService.isExistPin(pin)) {
            String importPhoto = request.getParameter("importPhoto");
            String importCropFace = request.getParameter("importCropFace");
            String importTemplateFace = request.getParameter("importTemplateFace");
            if (Objects.nonNull(personPhoto) && !personPhoto.isEmpty()) {
                if (personPhoto.getOriginalFilename().toLowerCase().matches(".*?(jpg|jpeg|png)$")) {
                    PersImportPhotoItem persImportPhotoItem = new PersImportPhotoItem();
                    persImportPhotoItem.setPin(pin);
                    persImportPhotoItem.setImportPhoto(Boolean.parseBoolean(importPhoto));
                    persImportPhotoItem.setImportCropFace(Boolean.parseBoolean(importCropFace));
                    persImportPhotoItem.setImportTemplateFace(Boolean.parseBoolean(importTemplateFace));
                    if (persImportPhotoItem.getImportPhoto()) {
                        String photoPath = FileUtil.saveFileToServer("pers", "user/avatar", pin + ".jpg", personPhoto);
                        persImportPhotoItem.setPhotoPath(photoPath);
                    } else if (persImportPhotoItem.getImportCropFace()) {
                        String cropPhotoPath = FileUtil.saveFileToServer("pers", "user/avatar",
                            System.currentTimeMillis() + ".jpg", personPhoto);
                        persImportPhotoItem.setCropPhotoPath(cropPhotoPath);
                    }
                    return I18nUtil.i18nMsg(persPersonService.uploadUserPhoto(persImportPhotoItem));
                } else {
                    return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("pers_cardTemplate_jpgFormat"));
                }
            } else {
                return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("pers_cardTemplate_uploadFail"));
            }
        } else {
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg("pers_person_pinError", pin));
        }
    }

    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_import_personPhoto", requestParams = {},
        opContent = "pers_import_personPhoto")
    @Override
    public ZKResultMsg saveLargePhoto(@RequestParam(value = "uploadFile") MultipartFile uploadFile) {
        if (Objects.nonNull(uploadFile) && !uploadFile.isEmpty()) {
            String[] originalFilename = uploadFile.getOriginalFilename().replace("\\", "/").split("/");
            if (originalFilename.length > 0) {
                String filename = originalFilename[originalFilename.length - 1];
                String filePath =
                    FileUtil.saveFileToServer("pers", "user" + File.separator + "zipPhotos", filename, uploadFile);
                // 解压上传的压缩包文件
                String zipPath = persPersonService.unZipToFile(filePath, File.separator + "upload" + File.separator
                    + "pers" + File.separator + "user" + File.separator + "zipPhotos" + File.separator);
                // 删除压缩包文件
                FileUtils.deleteFile(FileUtil.getLocalFullPath(filePath));

                String importPhoto = request.getParameter("importPhoto");
                String importCropFace = request.getParameter("importCropFace");
                String importTemplateFace = request.getParameter("importTemplateFace");
                PersImportPhotoItem persImportPhotoItem = new PersImportPhotoItem();
                persImportPhotoItem.setFilePath(zipPath);
                persImportPhotoItem.setImportPhoto(Boolean.parseBoolean(importPhoto));
                persImportPhotoItem.setImportCropFace(Boolean.parseBoolean(importCropFace));
                persImportPhotoItem.setImportTemplateFace(Boolean.parseBoolean(importTemplateFace));

                return I18nUtil.i18nMsg(persPersonService.handlerZipUserPhoto(persImportPhotoItem));
            }
            return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("common_op_failed"));
        } else {
            return I18nUtil.i18nMsg(ZKResultMsg.getFailMsg("common_op_failed"));
        }
    }

    @RequiresPermissions("pers:person:exportPhoto")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_export_personPhoto",
        opContent = "pers_export_personPhoto")
    @Override
    public void exportPhoto() throws Exception {
        // 设置导出结果的状态
        stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "start");
        stringRedisTemplate.expire(EXPORT_RESULT + request.getSession().getId(), sessionTimeout, TimeUnit.SECONDS);
        // 验证用户登录密码
        if (isNeedValid("pwd")) {
            try {
                String loginPwd = this.request.getParameter("loginPwd");
                boolean ret = persPersonService.verifyLoginPwd(request.getSession().getId(), loginPwd);
                if (!ret) {
                    throw new ZKBusinessException("auth_user_pwdIncorrect");
                }
            } catch (ZKBusinessException e) {
                throw e;
            } catch (Exception e) {
                this.log.error("", e);
            }
        }

        PersPersonPinItem condition = new PersPersonPinItem();
        setConditionValue(condition);
        Map<String, String> pinAndPhotoMap =
            persPersonService.getAllPersonPhotoByAuthUserFilter(request.getSession().getId(), condition);
        if (pinAndPhotoMap.isEmpty()) {
            throw ZKBusinessException.warnException("common_report_dataSourceNull");
        }

        ArrayList<File> files = new ArrayList<>();
        pinAndPhotoMap.forEach((pin, photoPath) -> {
            File file = new File(FileUtil.getLocalFullPath(photoPath));
            if (file.exists()) {
                // 生成解密图片
                byte[] decPhoto = FileEncryptUtil.getDecryptFile(photoPath);
                if (Objects.nonNull(decPhoto)) {
                    String fileName = photoPath.substring(photoPath.lastIndexOf("/") + 1);
                    String decryFilePath = FileUtils.saveByte2Image(ConstUtil.SYSTEM_MODULE_PERS, "user/exportDecrypt",
                        fileName, decPhoto);
                    File decryFile = new File(FileUtils.getLocalFullPath(decryFilePath));
                    if (decryFile.exists()) {
                        files.add(decryFile);
                    }
                } else {
                    files.add(file);
                }
            }
        });

        // 文件数量为0，不进行导出。
        if (files.isEmpty()) {
            throw ZKBusinessException.warnException("common_report_dataSourceNull");
        }

        String fileNamePrefix = I18nUtil.i18nCode("pers_import_photoType");
        // 如果出现加密的话，需要一个临时变量用来存储文件名，防止后续在做文件编码转化时报错
        String excelFileNamePrefix = fileNamePrefix;
        String isEncrypt = request.getParameter("isEncrypt");
        String encryptPassword = request.getParameter("encryptPassword");
        // 导出压缩包名称

        /*----------页面reponse设置----------*/
        String agent = request.getHeader("User-Agent");
        boolean isMSIE = (agent != null && (agent.indexOf("MSIE") != -1 || agent.indexOf("Trident") != -1));
        // IE浏览器
        if (isMSIE) {
            fileNamePrefix = java.net.URLEncoder.encode(fileNamePrefix, "UTF-8");
            // IE会将空格转成+号
            fileNamePrefix = fileNamePrefix.replaceAll("\\+", " ");
        } else {
            fileNamePrefix = new String(fileNamePrefix.getBytes("UTF-8"), "ISO-8859-1");
        }
        // Edge浏览器兼容处理
        if (request.getHeader("User-Agent") != null && request.getHeader("User-Agent").indexOf("Edge") != -1) {
            // 空格预处理
            fileNamePrefix = fileNamePrefix.replaceAll(" ", "%20");
            // 编码转换
            fileNamePrefix = URLEncoder.encode(fileNamePrefix, "ISO-8859-1");
            // 空格还原
            fileNamePrefix = fileNamePrefix.replace("%2520", " ");
        }
        // 导出的压缩包路径
        String tempPath = ClassUtil.getRootPath() + FileUtils.separator + FileUtils.systemFilePath + FileUtils.separator
            + "upload" + FileUtils.separator + "pers" + FileUtils.separator + fileNamePrefix + "_"
            + (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date()) + ".zip";

        // 如果有加密，则导出zip的加密文件
        if (StringUtils.isNotBlank(isEncrypt) && ExcelUtil.ENCRYPT_PASSWORD_YES.equals(isEncrypt)) {
            response = PersExportUtil.excelEncryptZip(tempPath, files, encryptPassword, request, response);
        } else {
            response = FileUtils.downLoadFiles(tempPath, files.toArray(new File[] {}), request, response);
        }
        response.flushBuffer();
        // 删除临时文件夹
        File zipTempFile = new File(tempPath);
        if (zipTempFile.exists()) {
            // 删除临时压缩包
            zipTempFile.delete();
        }
        // 删除存放解密图片的临时文件夹
        String exportDecrypt = FileUtils
            .getLocalFullPath(FileUtils.createUploadFileRootPath(ConstUtil.SYSTEM_MODULE_PERS, "user/exportDecrypt"));
        FileUtils.deleteDirectory(exportDecrypt);
        // 导出结束
        stringRedisTemplate.opsForValue().set(EXPORT_RESULT + request.getSession().getId(), "end");
    }

    @RequiresPermissions("pers:person:cardPrint")
    @Override
    public ModelAndView cardPrint(@RequestParam("ids") String ids) {
        List<PersPersonItem> persPersonItemList = persPersonService.getItemsByIds(ids);
        request.setAttribute("persPersonItemList", persPersonItemList);
        return new ModelAndView("pers/cardPrintTemplate/opCardPrinting");
    }

    @Override
    public ZKResultMsg getCountry(@RequestParam("countryKey") String countryKey) {
        return new ZKResultMsg(persPersonService.getCountry(countryKey));
    }

    @Override
    public String checkSMSModemParam() {
        return String.valueOf(persPersonService.checkSMSModemParam());
    }

    @Override
    public ZKResultMsg validPersonPhoto(@RequestParam(value = "personPhoto", required = false) MultipartFile file,
        @RequestParam(value = "personIdPhoto", required = false) String personIdPhoto) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String photoPath = "";
        String filePath = FileUtil.createUploadFileRootPath("pers", "user/avatar");
        String photoName = System.currentTimeMillis() + ".jpg";
        try {
            if (Objects.nonNull(file) && !file.isEmpty()) {
                InputStream inputStream = file.getInputStream();
                boolean jpgImage = ImgEncodeUtil.isSupportImageType(inputStream, FileType.JPEG, FileType.PNG);
                if (!jpgImage) {
                    zkResultMsg.setRet("fail");
                    zkResultMsg.setMsg(
                        I18nUtil.i18nCode("pers_face_validFailMsg") + I18nUtil.i18nCode("pers_face_photoFormatError")
                            + "</br>" + I18nUtil.i18nCode("pers_face_notUpdateMsg"));
                    return zkResultMsg;
                }

                // 生成临时图片
                String fullPath = FileUtil.systemFilePath + '/' + filePath;
                File fileImg = new File(new File(fullPath), photoName);
                if (!fileImg.getParentFile().exists()) {
                    fileImg.getParentFile().mkdirs();
                }
                if (fileImg.exists()) {
                    fileImg.delete();
                }
                file.transferTo(new File(fileImg.getAbsolutePath()));
                photoPath = '/' + filePath + photoName;
            } else if (StringUtils.isNotBlank(personIdPhoto)) {
                // 生成临时图片
                FileUtil.saveFile(filePath, photoName, personIdPhoto, false);
                photoPath = '/' + filePath + photoName;
            }
            if (StringUtils.isNotBlank(photoPath)) {
                zkResultMsg = persPersonService.validCropFace(photoPath);
                if (!zkResultMsg.isSuccess()) {
                    zkResultMsg.setMsg(zkResultMsg.getMsg() + "</br>" + I18nUtil.i18nCode("pers_face_notUpdateMsg"));
                }
                File tempFile = new File(FileUtil.getLocalFullPath(photoPath));
                if (tempFile.exists()) {
                    // 删除临时图片
                    tempFile.delete();
                }
            }
        } catch (IOException e) {
            log.error("pers validPersonPhoto error", e);
        }
        return zkResultMsg;
    }

    @Override
    public TreeItem verifyModeTree() {
        List<TreeItem> items = new ArrayList<TreeItem>();
        TreeItem item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_CARD);
        item.setText(I18nUtil.i18nCode("pers_card"));
        items.add(item);
        item = new TreeItem();
        item.setId(BaseConstants.BaseBioType.FP_BIO_TYPE.toString());
        item.setText(I18nUtil.i18nCode("pers_person_regFinger"));
        items.add(item);
        item = new TreeItem();
        item.setId(BaseConstants.BaseBioType.PALM_BIO_TYPE.toString());
        item.setText(I18nUtil.i18nCode("pers_person_metacarpalVein"));
        items.add(item);
        item = new TreeItem();
        item.setId(BaseConstants.BaseBioType.VEIN_BIO_TYPE.toString());
        item.setText(I18nUtil.i18nCode("pers_person_regVein"));
        items.add(item);
        item = new TreeItem();
        item.setId(BaseConstants.BaseBioType.FACE_BIO_TYPE.toString());
        item.setText(I18nUtil.i18nCode("pers_person_infraredFaceTemplate"));
        items.add(item);
        item = new TreeItem();
        item.setId(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.toString());
        item.setText(I18nUtil.i18nCode("pers_person_visibleFaceTemplate"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_CROPFACE);
        item.setText(I18nUtil.i18nCode("pers_person_visibleFacePhoto"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.PALM_BIO_TYPE_10.toString());
        item.setText(I18nUtil.i18nCode("pers_person_visiblePalm"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.IRIS_BIO_TYPE.toString());
        item.setText(I18nUtil.i18nCode("pers_person_iris"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_NOCARD);
        item.setText(I18nUtil.i18nCode("pers_person_notCard"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_NOREGFINGER);
        item.setText(I18nUtil.i18nCode("pers_person_notRegFinger"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_NOMETACARPALVEIN);
        item.setText(I18nUtil.i18nCode("pers_person_notMetacarpalVein"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_NOREGVEIN);
        item.setText(I18nUtil.i18nCode("pers_person_notRegVein"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_NOINFRAREDFACETEMPLATE);
        item.setText(I18nUtil.i18nCode("pers_person_notInfraredFaceTemplate"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_NOVISIBLEFACETEMPLATE);
        item.setText(I18nUtil.i18nCode("pers_person_notVisibleFaceTemplate"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_NOCROPFACE);
        item.setText(I18nUtil.i18nCode("pers_person_notVisibleFacePhoto"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_NOPALMBIOTYPE10);
        item.setText(I18nUtil.i18nCode("pers_person_notVisiblePalm"));
        items.add(item);
        item = new TreeItem();
        item.setId(PersConstants.VERIFY_MODE_NOIRISBIOTYPE);
        item.setText(I18nUtil.i18nCode("pers_person_notIris"));
        items.add(item);

        List<TreeItem> treeItems = TreeBuilder.newTreeBuilder(TreeItem.class, String.class).buildToTreeList(items);
        return new TreeItem("0", treeItems);
    }

    @Override
    public ModelAndView getBioTemplateView(@RequestParam("ids") String ids) {
        if (StringUtils.isNotBlank(ids)) {
            List<String> personList = StrUtil.strToList(ids);
            List<String> pinList = persPersonService.getPinsByIds(personList);
            if (pinList != null && !pinList.isEmpty()) {
                request.setAttribute("personIds", ids);
                request.setAttribute("pins", String.join(",", pinList));
            }
        }
        List<PersPersonItem> personItemList = persPersonService.getItemsByIds(ids);
        return new ModelAndView("pers/person/opDeleteBioTemplate");
    }

    @RequiresPermissions("pers:person:delBioTemplate")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_person_delBioTemplate",
        requestParams = {"pins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg deleteBioTemplate(@RequestParam Map<String, String> params) {
        String personIds = params.get("personIds");
        String personPins = params.get("pins");
        if (StringUtils.isNotBlank(personPins)) {
            String fp = params.get("fp");
            String face = params.get("face");
            String fv = params.get("fv");
            String palm = params.get("palm");
            String vislightPalm = params.get("vislightPalm");
            String vislight = params.get("vislight");
            String iris = params.get("iris");
            List<Short> bioType = new ArrayList<>();
            if (StringUtils.isNotBlank(fp)) {
                bioType.add(Short.parseShort(fp));
            }
            if (StringUtils.isNotBlank(face)) {
                bioType.add(Short.parseShort(face));
            }
            if (StringUtils.isNotBlank(fv)) {
                bioType.add(Short.parseShort(fv));
            }
            if (StringUtils.isNotBlank(palm)) {
                bioType.add(Short.parseShort(palm));
            }
            if (StringUtils.isNotBlank(vislightPalm)) {
                bioType.add(Short.parseShort(vislightPalm));
            }
            if (StringUtils.isNotBlank(vislight)) {
                bioType.add(Short.parseShort(vislight));
            }
            if (StringUtils.isNotBlank(iris)) {
                bioType.add(Short.parseShort(iris));
            }
            if (!bioType.isEmpty()) {
                List<String> personIdList = StrUtil.strToList(personIds);
                List<String> personPinList = StrUtil.strToList(personPins);
                persBioTemplateService.deleteByPersonPinsAndBioTypes(personIdList, personPinList, bioType);
            }
        }
        return new ZKResultMsg();
    }

    @Override
    public void exportLeavePersonTemplate(HttpServletRequest request, HttpServletResponse response) {
        JSONObject jsonObject = JSONObject.parseObject(request.getParameter("jsonColumn"), Feature.OrderedField);
        Map<String, String> commentMap = new HashMap<>();
        if (jsonObject == null) {
            jsonObject = new JSONObject(new LinkedHashMap());
            jsonObject.put("pin", I18nUtil.i18nCode("pers_person_pin"));
            jsonObject.put("name", I18nUtil.i18nCode("pers_person_name"));
            if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
                jsonObject.put("lastName", I18nUtil.i18nCode("pers_person_lastName"));
            }
            jsonObject.put("deptName", I18nUtil.i18nCode("pers_dept_deptName"));
            jsonObject.put("leaveDate", I18nUtil.i18nCode("pers_dimission_date"));
            jsonObject.put("leaveTypeString", I18nUtil.i18nCode("pers_dimission_type"));
            jsonObject.put("leaveReason", I18nUtil.i18nCode("pers_dimission_reason"));
            commentMap.put("jsonColumn", jsonObject.toJSONString());
        }
        for (String key : jsonObject.keySet()) {
            switch (key) {
                case "pin":
                    commentMap.put(key, I18nUtil.i18nCode("common_jqMsg_required"));
                    break;
                case "leaveDate":
                    commentMap.put(key, I18nUtil.i18nCode("pers_dimission_dateFormat"));
                    break;
                case "leaveTypeString":
                    commentMap.put(key, I18nUtil.i18nCode("pers_dimission_leaveType"));
                    break;
            }
        }
        persExportTemplateUtil.templateExport(request, response, commentMap);
    }

    @Override
    public ZKResultMsg getDeptIdByPersonId(@RequestParam(value = "personId") String personId) {
        PersPersonItem item = persPersonService.getItemById(personId);
        ZKResultMsg result = new ZKResultMsg();
        if (item != null) {
            result.setData(item.getDeptId());
        } else {
            result.setData(null);
        }
        return result;
    }

    @Override
    public ZKResultMsg importLeavePersonExcel(MultipartFile upload) throws IOException {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_uploadFileSuccess") + "<br/>"));
            List<PersLeavePersonItem> itemList =
                ExcelUtil.excelImport(upload.getInputStream(), PersLeavePersonItem.class);
            progress += 10;
            progressCache.setProcess(
                new ProcessBean(progress, progress, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
            return I18nUtil.i18nMsg(persLeavePersonService.importExcel(itemList));
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Import PersLeavePerson Info Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @Override
    public ModelAndView exportPage() {
        Enumeration<?> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = (String)paramNames.nextElement();
            request.setAttribute(paramName, request.getParameter(paramName));
        }
        List<PersAttributeItem> items = persAttributeService.getByCondition(new PersAttributeItem());
        HashMap<String, String> map = Maps.newHashMap();
        for (PersAttributeItem item : items) {
            String attrName = item.getAttrName();
            if ("init".equals(item.getSqlStr())) {
                String name = I18nUtil.i18nCode("pers_attr_" + attrName);
                if (StringUtils.isNotBlank(name) && !name.contains("pers_attr_")) {
                    attrName = name;
                }
            }
            map.put("" + item.getFiledIndex(), attrName);
        }
        request.setAttribute("customAttrsMap", map);
        return new ModelAndView("pers/person/opExportRecord");
    }

    @Override
    public ZKResultMsg getPersonPhotoBase64ByPin(String pin) {
        String photoPath = persPersonService.getPersonPhotoPathByPin(pin);
        if (StringUtils.isNotBlank(photoPath)) {
            String decryptPhoto = FileEncryptUtil.getDecryptFileBase64(photoPath);
            if (StringUtils.isNotBlank(decryptPhoto)) {
                return new ZKResultMsg(PersConstants.PERSON_BASE64_PREFIX + decryptPhoto);
            }
        }
        return ZKResultMsg.failMsg();
    }

    @Override
    public String checkMobileIsExist(String mobilePhone) {
        PersPersonItem persPersonItem = persPersonService.getItemByMobile(mobilePhone);
        return String.valueOf(persPersonItem == null);
    }

    @Override
    public ZKResultMsg getPersonCountByDeptIds(String deptIds) {
        int personCount = persPersonService.getPersonCountByDeptIds(deptIds);
        return I18nUtil.i18nMsg(new ZKResultMsg(personCount));
    }

    @RequiresPermissions("pers:person:enabled")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "common_enable", requestParams = {"pins"},
        opContent = "pers_person_pin")
    @Override
    public ZKResultMsg enabledPersCredential(String ids) {
        persPersonService.enabledCredential(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("pers:person:disable")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "common_disable", requestParams = {"pins"},
        opContent = "pers_person_pin")
    @Override
    public ZKResultMsg disableCredential(String ids) {
        persPersonService.disableCredential(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("pers:person:viewBioPhoto")
    @Override
    public ModelAndView viewBioPhoto(@RequestParam("ids") String ids) {
        List<PersBioPhotoItem> persBioPhotoItems =
            persBioPhotoService.getItemAndBase64ByPerIds(request.getSession().getId(), ids);
        request.setAttribute("bioPhotoItems", persBioPhotoItems);
        return new ModelAndView("pers/person/viewBioPhoto");
    }

    @Override
    public String checkWhatsappParam() {
        return String.valueOf(persPersonService.checkWhatsappParam());
    }

    @RequiresPermissions("pers:person:enabledApplogin")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_applogin_enabled",
        requestParams = {"pins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg enabledApplogin(String ids) {
        persPersonService.enabledApplogin(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @RequiresPermissions("pers:person:disableApplogin")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_applogin_disable",
        requestParams = {"pins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg disableApplogin(String ids) {
        persPersonService.disableApplogin(ids);
        return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
    }

    @Override
    public ZKResultMsg obtainAcmsCard() {
        String status = request.getParameter("status");
        String pageSize = request.getParameter("pageSize");
        return persPersonService.obtainAcmsCard(status, pageSize);
    }

    @Override
    public ZKResultMsg getFaceTemplate() {
        String cropPhoto = request.getParameter("cropPhoto");
        String photoPath = "";
        if (StringUtils.isNotBlank(cropPhoto)) {
            // 生成临时图片
            String filePath = FileUtil.createUploadFileRootPath("pers", "user/avatar");
            String photoName = System.currentTimeMillis() + ".jpg";
            FileUtil.saveFile(filePath, photoName, cropPhoto, false);
            photoPath = '/' + filePath + photoName;
            if (StringUtils.isNotBlank(photoPath)) {
                ZKResultMsg zkResultMsg = persTemplateServerService.getFaceTemplateByPhoto(photoPath);
                File tempFile = new File(FileUtil.getLocalFullPath(photoPath));
                if (tempFile.exists()) {
                    // 删除临时图片
                    tempFile.delete();
                }
                return zkResultMsg;
            }
        }
        return new ZKResultMsg();
    }

    @RequiresPermissions("pers:person:batchFaceTemplate")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_person_extractFaceTemplate",
        requestParams = {"pins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg batchFaceTemplate(@RequestParam("ids") String ids) {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(new ProcessBean(progress, progress));
            return persPersonService.batchFaceTemplateByIds(ids);
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("batchFaceTemplate Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }

    @Override
    public ZKResultMsg getCardQrCodeByCardNo(String cardNo) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        // 静态二维码
        String qrcodeType = baseSysParamService.getValByName("qrcode.type");
        if (BaseDataConstants.QRCODE_TYPE_STATIC.equals(qrcodeType)) {
            String cardQrCode = persPersonService.getPersCardQrCodeByCardNo(cardNo);
            zkResultMsg.setData(cardQrCode);
        }
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg isOpenStaticQrCode() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String qrcodeType = baseSysParamService.getValByName("qrcode.type");
        if (BaseDataConstants.QRCODE_TYPE_DISABLE.equals(qrcodeType)) {
            zkResultMsg.setData(false);
        } else if (BaseDataConstants.QRCODE_TYPE_DYNAMIC.equals(qrcodeType)) {
            zkResultMsg.setData(false);
        } else {
            zkResultMsg.setData(true);
        }

        return zkResultMsg;
    }

    @RequiresPermissions("pers:person:syncAcms")
    @LogRequest(module = "pers_module", object = "pers_person", opType = "pers_person_syncAcms",
        requestParams = {"pins"}, opContent = "pers_person_pin")
    @Override
    public ZKResultMsg syncPersonAcms(@RequestParam("ids") String ids) {
        int progress = 5;
        try {
            progressCache.beginProcess(I18nUtil.i18nCode("common_op_processing") + "...<br/>");
            progressCache.setProcess(new ProcessBean(progress, progress));
            return persPersonService.syncPersonAcmsByIds(ids);
        } catch (Exception e) {
            progress = 99;
            progressCache.setProcess(new ProcessBean(progress, progress,
                "<font color='red'>" + I18nUtil.i18nCode("common_prompt_dataError") + "</font><br/>"));
            if (e instanceof ZKBusinessException) {
                progressCache.setProcess(new ProcessBean(progress, progress, "<font color='red'>"
                    + I18nUtil.i18nCode(e.getMessage(), ((ZKBusinessException)e).objects) + "</font><br/>"));
            } else {
                progressCache.setProcess(
                    new ProcessBean(progress, progress, "<font color='red'>" + e.getMessage() + "</font><br/>"));
            }
            log.error("Synchronize personnel to ACMS Exception", e);
            return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
        } finally {
            progressCache.finishProcess(I18nUtil.i18nCode("common_dev_opComplish"));
        }
    }
}