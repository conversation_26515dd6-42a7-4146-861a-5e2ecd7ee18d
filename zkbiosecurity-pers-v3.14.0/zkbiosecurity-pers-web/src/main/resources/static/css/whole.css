body {
	margin: 0px;
	height: 100%;
	MIN-WIDTH: 1000px;
	WIDTH: 98%;
	margin: 0px auto;
	border-left: 1px solid;
	border-right: 1px solid;
}

body,td,th {
	font-family: Arial, Helvetica, sans-serif;
	font-size: 12px;
}

a:link,a:visited,a:active {
	color: #666666;
	text-decoration: none;
}

a:hover {
	color: #000000;
	text-decoration: none;
}

/*head begin */
.f_left {
	float: left;
}

.f_right {
	float: right;
}

.head_div {
	height: 70px;
	background-image: url('/zkeco/pers/images/head_logo_acc_oem.jpg');
	color: #FFFFFF;
}

.menu_div {
	height: 25px;
}

.menu_l_ul {
	padding: 0px;
	margin: 0px;
}

.menu_l_ul li {
	float: left;
	list-style-type: none;
	border-right: 1px solid #FFEEEE;
	padding: 0px 5px;
}

.menu_l_ul a {
	color: #666666;
	text-decoration: none;
}

.menu_l_ul a:hover {
	color: gray;
	text-decoration: none;
}

.menu_r_ul {
	padding: 0px;
	margin: 0px;
}

.menu_r_ul li {
	float: left;
	list-style-type: none;
	padding: 0px 5px;
}

.menu_r_ul a {
	color: #ffffff;
	text-decoration: none;
}

.menu_r_ul a:hover {
	color: gray;
	text-decoration: none;
}

.tape_div {
	height: 25px;
	background-image: url("/zkeco/pers/images/black.jpg");
}
/*head end*/

.but_border{
	border: 1px solid;
}

.warningImage{
	width: 18px;
	height: 15px;
	font-weight: bold;
	display: inline-block;
	margin-right: 3px;
	margin-left: 5px;
	vertical-align: bottom;
	background-image: url(/public/images/alert.png);
	background-repeat: no-repeat;
	background-position: 0 -1px;
}