@charset "utf-8";

.wiegandfmt_edit_table {
    width: 500px !important;
}

.wiegandfmt_edit_table th {
    width: 117px !important;
}

.wg_table_inner_mode_one {
    text-align: center;
    border: 1px solid #ccc;
}

.wg_table_inner_mode_one input {
    width: 50px !important;
}

.wg_table_inner_mode_two {
    border: 1px solid #ccc;
}

.wg_table_inner_mode_one tr td, .wg_table_inner_mode_two tr td {
    border: 1px solid #ebebeb;
    background-color: #eff4f8;
    color: black;
    colspan: 1;
}

.wg_table_inner_mode_two input {
    width: 550px !important;
}

.blank_bgcolor {
    background-color: #f7f7f7;
    width: 115px;
}

.out {
    border-top: 40px solid #A3BDD3; /*上边框宽度等于表格第一行行高*/
    width: 0px; /*让容器宽度为0*/
    height: 0px; /*让容器高度为0*/
    border-left: 115px solid #D2DDF0; /*左边框宽度等于表格第一行第一格宽度*/
    position: relative; /*让里面的两个子容器绝对定位*/
}

.out b {
    font-style: normal;
    display: block;
    position: absolute;
    top: -35px;
    left: -40px;
    width: 35px;
}

.out em {
    font-style: normal;
    display: block;
    position: absolute;
    top: -20px;
    left: -100px;
    width: 55px;
}
.persWgRadioClass{
    margin-left: -7px;
}
.wiegandfmtTooltip {
    position: relative;
    height: 22px;
    vertical-align: bottom;
}
.wiegandfmtTooltip .wiegandfmtSchtip{
    padding-left: 10px;
}
.wiegandfmtSchtip{
    position: absolute;
    left: 10px;
    right: 10px;
    top: 18px;
    height: auto;
    background-color: #f3f5f0;
    z-index: 10;
    padding: 10px;
    text-align: left;
    border-radius: 5px;
    visibility: hidden;
    font-size: 12px !important;
    line-height: 20px !important;
    width: 600px;
    padding-left: 25px;
}
