@charset "UTF-8";

.divEstopList {
	height: 355px;
	left: 0px;
	margin-top: -355px;
	width: 100%;
	display: none; 
	background-color: #EEEEEE;
	z-index:2;
	position: relative;
	opacity: 0.6;
	filter:alpha(opacity=60);
}

.btn_left, .btn_right {
    background: url("../images/dhxform_btns.gif");
    background-repeat: no-repeat;
    font-size: 1px;
    height: 21px;
    width: 5px;
}

.btn_left {
    background-position: 0 0;
}

.btn_right {
    background-position: -5px 0;
}

.btn_middle {
    background: url("../images/dhxform_btns.gif");
    background-position: 0 -21px;
    background-repeat: repeat-x;
    height: 21px;
}

.btn_middle .btn_txt.btn_txt_fixed_size {
    padding: 0;
    width: 100%;
    text-align: center;
}

.btnDiv {
	float: left;
	overflow: hidden; 
	width: 5%;
}

.personGridDiv {
	float: left;
	overflow: hidden; 
	background-color: white; 
	width: 100%;
}