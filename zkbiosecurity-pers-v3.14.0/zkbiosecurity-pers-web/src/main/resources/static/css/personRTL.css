/*-------------人员css样式分离------------*/
@charset "utf-8";
.countMessage {
    display: inline-block;
    margin: 0;
}

#multiCardDiv {
    overflow-y: auto;
    padding: 0px 10px 0px 10px;
    margin-top: 2px
}

div.multiCard {
    display: inline-block;
    width: 49%;
    float: right;
    margin: 0px 2px 2px 2px;
    padding: 2px;
    border-bottom: 1px dotted #e8eaeb;
}

div.multiCard label {
    display: inline-block;
    width: 120px;
}

div.multiCard input {
    width: 142px;
}

.readCard, .stopReadCard, .addCard, .delCard, .readIDCard {
    margin-right: 10px;
    cursor: pointer;
}

.readCard, .stopReadCard, .readIDCard {
    width: 21px;
    height: 18px;
}

.multiCard .stopReadCard {
    display: none;
}

div.multiCard .addCard {
    visibility: hidden;
}

.multiCard:last-child .addCard {
    visibility: visible;
}

.multiCard input.wait {
    background: #fff no-repeat right url('/images/base_wait.gif');
}

.customAttribute .attr {
    clear: both;
    width: 100%;
}

.customAttribute .attr .attrVal {
    float: right;
    display: inline-block;
    padding: 2px 0px 2px 10px;
    max-width: 250px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}

.customAttribute .attr .attrVal input {
    margin-left: -3px;
    margin-left: 5px;
}

.personLevel label {
    display: inline-block;
    max-width: 185px;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
}
#localImag{
    position: relative;
}
.persTooltip{
    top: 5px;
    right: 5px;
    position: absolute;
}
.persTooltip:hover .persCommonTipClass {
    visibility: visible;
}
.pers_icv{
    font-size: 15px;
    cursor: pointer;
}
.persCommonTipClass{
    right:auto;
    position: absolute;
    left: 100px;
    top: 18px;
    height: auto;
    background-color: #f3f5f0;
    z-index: 10;
    padding: 10px;
    text-align: right;
    border-radius: 5px;
    visibility: hidden;
    font-size: 12px !important;
    line-height: 20px !important;
    width: 600px;
    padding-right: 25px;
}
.persImagTop{
    display: flex;
    height: auto;
}
.persImagTopLeft{
    flex:0.7;
}
.persImagTopRight{
    flex:2;
}
.persExamplePic{
    width: 120px;
    height: 140px;
}
.persExamplePicTip{
    text-align: center;
    width: 120px;
}
.persImagBottom{
    display: flex;
    height: auto;
}
.persImagBottomLeft{
    flex:1;
}
.persImagBottomCenter{
    flex:1;
}
.persImagBottomRight{
    flex:1;
}
.persExamplePicErrorTitle{
    padding-top: 20px;
}
.persBioCommonTipClass{
    right:auto;
    position: absolute;
    height: auto;
    background-color: #f3f5f0;
    padding: 10px;
    text-align: right;
    border-radius: 5px;
    visibility: hidden;
    font-size: 12px !important;
    line-height: 20px !important;
    width: 600px;
    padding-right: 25px;
}
.persBioPicTip{
    top: 5px;
    right: 5px;
    position: absolute;
}