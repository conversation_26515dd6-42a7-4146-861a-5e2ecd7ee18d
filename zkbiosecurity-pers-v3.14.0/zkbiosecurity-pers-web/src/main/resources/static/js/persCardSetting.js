var persPrint_trObject = null;
var persPrint_leftElement = null;
var persPrint_topElement = null;
var persPrint_bgFlag = true;
var persPrint_bgMsg = "";
//拖动对象
function customDrag(obj)
{
	$(obj).draggable({
		containment : "#" +persPrint_trObject+ " .containment-wrapper",
		scroll : false
	}).bind('dragstart', function(event, ui){
		changeValue(this);
	}).bind('drag', function(event, ui){
		changeValue(this);
	}).bind('dragstop', function(event, ui){
		changeValue(this);
	}).mousedown(function(){
		$(".draggable.selected").removeClass("selected");
		$(this).addClass("selected");
		changeValue(this);
	});
}
function changeValue(element){
	$("#xValue").val(parseInt($(element).css('left')));
	$("#yValue").val(parseInt($(element).css('top')));
	$("#zValue").val($(element).css('z-index'));
	$("#wValue").val($(element).width());
	$("#hValue").val($(element).height());
	$("#cValue").val($(element).css('color'));
	$("#fsValue").val($(element).css('font-size'));
	$("#aValue").val('center');
	$("#fontFValue").val($(element).css('font-family'));
	if($(element).hasClass("text"))
	{
		$("#textValue").removeAttr("readonly");
	}
	else
	{
		$("#textValue").attr("readonly","readonly");
	}
	if($(element).attr("t") == "text" || $(element).attr("t") == "txt")
	{
		$("#fsValue").attr("disabled",false);
	}
	else
	{
		$("#fsValue").attr("disabled",true);
	}
	$("#textValue").val($(element).text());
}

//双击删除对象
function delImage(obj)
{
	obj.dblclick(function(e){
		e.stopPropagation();
		messageBox({
	        messageType : "confirm",
	        text : I18n.getValue("common_prompt_sureToDelThese"),
	        callback : function(result)
	        {
		        if (result)
		        {
					$("#"+ persPrint_trObject +" .draggable.selected").remove();
		        	return true;
		        }
	        }
	    });
	});
}

//双击删除背景图片
function delBgImage() {
	$("#" + persPrint_trObject + " .containment-wrapper").dblclick(function (e) {
			var bgImageName = "vertical_front";
			if ($(".checkSurface:checked").val() == "Vertical") {
				if (eval($("#persPrint_surface").val()) == "front") {
					bgImageName = "vertical_front";
				} else {
					if (sysCfg.language == "zh_CN") {
						bgImageName = "vertical_opposite";
					} else {
						bgImageName = "verticalUS_opposite";
					}
				}
			} else {
				if (eval($("#persPrint_surface").val()) == "front") {
					bgImageName = "horizontal_front";
				} else {
					if (sysCfg.language == "zh_CN") {
						bgImageName = "horizontal_opposite";
					} else {
						bgImageName = "horizontalUS_opposite";
					}
				}
			}
			if (eval($("#" + persPrint_trObject + " .containment-wrapper").css("backgroundImage").replace('url(', '').replace(')', '')).indexOf("/images/" + bgImageName + ".jpg") == -1) {
				messageBox({
					messageType: "confirm",
					text: I18n.getValue("common_prompt_sureToDelThese"),
					callback: function (result) {
						if (result) {
							$("#" + persPrint_trObject + " .containment-wrapper").attr("style", "background:transparent url('/images/" + bgImageName + ".jpg') no-repeat scroll 0 0;background-size: 100%;");
							$("#" + persPrint_trObject + " .containment-wrapper").attr("back", "/images/" + bgImageName + ".jpg");
							persPrint_bgFlag = true;
						}
					}
				});
			}
		});
}

function initPrintParams(trObj, leftElem, topElem, face)
{
	persPrint_trObject = trObj;
	persPrint_leftElement = leftElem;
	persPrint_topElement = topElem;
	delBgImage();

	var faceData = new Array();
	if(eval($("#persPrint_surface").val()) === "front"){
		faceData = persPrintFrontData;
	}
	else{
		faceData = persPrintOppositeData;
		$("#"+ persPrint_trObject +" .containment-wrapper").attr("style","background:transparent url('/images/"+face+"_opposite.jpg') no-repeat scroll 0 0;background-size: 100%;");
		$("#"+ persPrint_trObject +" .containment-wrapper").attr("back","/images/"+face+"_opposite.jpg");
		if(sysCfg.language != "zh_CN")
		{
			$("#"+ persPrint_trObject +" .containment-wrapper").attr("style","background:transparent url('/images/"+face+"US_opposite.jpg') no-repeat scroll 0 0;background-size: 100%;");
			$("#"+ persPrint_trObject +" .containment-wrapper").attr("back","/images/"+face+"_opposite.jpg");
		}
	}
	//字体大小列表
	fontSize = "['12px','14px','16px','18px','20px','22px','26px','28px','36px','48px','56px','72px']";
	fontSize = eval('('+ fontSize +')');
	$.each(fontSize, function(key, value)
	{
		$("#fsValue").append('<option value="'+ value +'" checked="checked">'+ value + '</option>');
	});
	
//	编辑模板
	if(eval($("#persPrint_surface").attr("act")) === "edit" && faceData != null && eval($("#persPrint_surface").attr("flag")) == "false"){
		for(i=0; i < faceData.length; i++)
		{
			switch(faceData[i].type){
				case "background":
					$("#"+ persPrint_trObject +" .containment-wrapper").attr("style","background:transparent url("+faceData[i].back+") no-repeat scroll 0 0;background-size: 100%;");
					$("#"+ persPrint_trObject +" .containment-wrapper").attr("back",faceData[i].back);
					break;
				case "images":
					var $f = $('<div class="draggable image" t="images" cloum="'+faceData[i].cell+'" style="padding:0px; width:'+faceData[i].width+'px; height:'+faceData[i].height+'px; left: '+ faceData[i].left+'; top: '+ faceData[i].top+'; background:url('+faceData[i].cell+') no-repeat scroll 0 0;background-size: 100%;"/>');
					$("#"+ persPrint_trObject +" .containment-wrapper").append($f);
					delImage($f);
					customDrag($f);
					break;
				case "photo":
					var $f = $('<div id="'+faceData[i].value+'" value="'+faceData[i].value+'" class="draggable photo" cloum="'+faceData[i].cell+'" t="'+faceData[i].type+'" style="left: '+ faceData[i].left+'; top: '+ faceData[i].top+';">'+ faceData[i].value +'</div>');
					$("#"+ persPrint_trObject +" .containment-wrapper").append($f);
					$("input[name='dataColumn'][c='"+faceData[i].cell+"']").prop("checked",true);
					customDrag($f);
					break;
				case "txt":    
					var txt = faceData[i].value;
					if(txt == undefined)
					{
						txt = "";
					}
					var $f = $('<div id="'+txt+'" value="'+txt+'" class="draggable text" cloum="'+faceData[i].cell+'" t="'+faceData[i].type+'" style="font-size:'+ faceData[i].fontSize+'; color:'+ faceData[i].color +'; left: '+ faceData[i].left+'; top: '+ faceData[i].top+';">'+ txt +'</div>');
					$("#"+ persPrint_trObject +" .containment-wrapper").append($f);
					delImage($f);
					customDrag($f);
					break;
				case "line":
					var $f = $('<div class="draggable line" cloum='+faceData[i].cell+' t='+faceData[i].type+' style="color:'+ faceData[i].color +'; left: '+ faceData[i].left+'; top: '+ faceData[i].top+';"></div>');
					$("#"+ persPrint_trObject +" .containment-wrapper").append($f);
					delImage($f);
					customDrag($f);
					break;
				case "text":
					var $f = $('<div id="'+faceData[i].value.split(" ")[0]+'" value="'+faceData[i].value+'" class="draggable" cloum="'+faceData[i].cell+'" t="'+faceData[i].type+'" style="font-size:'+ faceData[i].fontSize+'; color:'+ faceData[i].color +'; left: '+ faceData[i].left+'; top: '+ faceData[i].top+';">'+ faceData[i].value +'</div>');
					$("#"+ persPrint_trObject +" .containment-wrapper").append($f);
					$("input[name='dataColumn'][c='"+faceData[i].cell+"']").prop("checked",true);
					customDrag($f);
					break;
			}
		}
	}
}
$(function() {
	document.onkeydown = function(event){
		var maxTop = 505 - $(".draggable.selected").height() - 2 * $(".draggable.selected").css('padding-left').replace('px', '');
		var maxLeft = 318 - $(".draggable.selected").width() - 2 * $(".draggable.selected").css('padding-left').replace('px', '') - 2 * $(".draggable.selected").css('border-left-width').replace('px', '');
		if(persPrint_trObject == "trTranverse"){
			maxTop = 318 - $(".draggable.selected").height() - 2 * $(".draggable.selected").css('padding-left').replace('px', '');
			maxLeft = 505 - $(".draggable.selected").width() - 2 * $(".draggable.selected").css('padding-left').replace('px', '') - 2 * $(".draggable.selected").css('border-left-width').replace('px', '');
		}
		switch (event.which) 
		{
			case(37): //left键
				if($("#xValue").val() > 0)
				{
					$("#xValue").val(parseInt($("#xValue").val()) - 1).change();
				}
				break;
			case(38)://up键
				if($("#yValue").val()>0)
				{
					$("#yValue").val(parseInt($("#yValue").val()) - 1).change();
				}
				break;
			case(39)://right键
				if($("#xValue").val() < maxLeft)
				{
					$("#xValue").val(parseInt($("#xValue").val()) + 1).change();
				}
				break;
			case(40)://down键
				if($("#yValue").val() < maxTop)
				{
					$("#yValue").val(parseInt($("#yValue").val()) + 1).change();
				}
				break;
		}
	}
	
	//图片上传预览
	function setImagePreview(imgElement) 
	{
		var imageUrl = null;
		var docObj=imgElement;
	    var imgObjPreview=document.getElementById("persPrintId_bgImage");
	    if(docObj.files && docObj.files[0])
	    {
	    	var fileTypeArr = docObj.value.split('.');
	    	var fileType = fileTypeArr[fileTypeArr.length-1];
			if(fileType != null)
			{
				fileType = fileType.toLowerCase();
				if(fileType =='jpg')
				{
					$("#"+docObj.id+"Form").ajaxSubmit({
						dataType: "text",
						async: false,
						success : function(data) 
						{
							//上传图片像素错误弹出提示
							if(eval("("+data+")").ret != "ok")
							{
								persPrint_bgMsg = eval("("+data+")").msg;
								openMessage(msgType.warning, persPrint_bgMsg);
								persPrint_bgFlag = false;
							}
							else
							{
								persPrint_bgFlag = true;
								if(docObj.id == "persPrint_opImage"){
									$(".draggable[t='images']:last").attr("cloum",eval("("+data+")").data.imgUrl);
									$(".draggable[t='images']:last").css({'width':eval("("+data+")").data.width/2 + 'px',
																		  'height':eval("("+data+")").data.height/2  + 'px',
																		  'padding':'0px'});
								}
								else{
									$("#"+ persPrint_trObject +" .containment-wrapper").attr("back",eval("("+data+")").data.imgUrl);
								}
							}
						},
						error : function(XMLHttpRequest, textStatus, errorThrown) 
						{
							openMessage(msgType.warning, I18n.getValue("pers_cardTemplate_uploadFail"));
						}
					});
					imageUrl = window.URL.createObjectURL(docObj.files[0]);
				}else{
					openMessage(msgType.warning, I18n.getValue("pers_cardTemplate_jpgFormat"));
				}
			}
	    }
	     return imageUrl;
	}
	
	//插入背景图片
	$("#persPrint_bgImage").change(function(){
		var bgImageUrl = setImagePreview(this);
		if(bgImageUrl != null){
			$("#"+ persPrint_trObject +" .containment-wrapper").attr("style","background:transparent url('"+bgImageUrl+"') no-repeat scroll 0 0;background-size: 100%;");
		}
	});

	$("#fsValue").change(function(){
		$("#"+ persPrint_trObject +" .draggable.selected").css('font-size',$("#fsValue").val());
	});
	//插入图片
	$("#persPrint_opImage").change(function(){
		var imageUrl = setImagePreview(this);
		if(imageUrl != null){
			var $f = $('<div value="'+I18n.getValue("pers_cardTemplate_picture")+'" class="draggable image" t="images" style="left: 0px; top: 0px; background:url('+imageUrl+') no-repeat scroll 0 0;background-size: 100%;"/>');
			$("#"+ persPrint_trObject +" .containment-wrapper").append($f);
			delImage($f);
			customDrag($f);
		}
	});
	
	//插入下划线
	$("#persPrint_addUnderline").click(function(){
		var $drag = $('<div class="draggable line" cloum="draggable line" t="line" style="left: "'+ persPrint_leftElement +'"px; top: "'+ persPrint_topElement +'"px;"></div>');
		$("#"+ persPrint_trObject +" .containment-wrapper").append($drag);
		delImage($drag);
		customDrag($drag);
	});
	//插入文本
	$("#persPrint_addText").click(function(){
		var $drag = $('<div class="draggable text" cloum="" t="txt" style="left: "'+ persPrint_leftElement +'"px; top: "'+ persPrint_topElement +'"px;"></div>');
		$("#"+ persPrint_trObject +" .containment-wrapper").append($drag);
		delImage($drag);
		customDrag($drag);
	});
	//输入框更改值
	$("input[id$='Value']").change(function(){
		var $sel = $(".draggable.selected");
		switch($(this).attr("id")){
			case "xValue":
				$sel.css('left',$("#xValue").val()+"px");
				break;
			case "yValue":
				$sel.css('top',$("#yValue").val()+"px");
				break;
			case "zValue":
				$sel.css('z-index',$("#zValue").val());
				break;
			case "wValue":
				$sel.width($("#wValue").val());
				break;
			case "hValue":
				$sel.height($("#hValue").val());
				break;
			case "fsValue":
				$sel.css('font-size',$("#fsValue").val()+"px");
				break;
			case "textValue":
				$(".draggable.selected").attr("cloum",$("#textValue").val());
				$(".draggable.selected").attr("value",$("#textValue").val());
				$(".draggable.selected").text($("#textValue").val());
				break;
		}
	});
	$("#"+ persPrint_trObject +" .containment-wrapper .draggable:first").mousedown();
	
//	选择数据列
	$("input[type=checkbox]").click(function() {
		var $f = $('<div id="'+$(this).attr('value').split(" ")[0]+'" value="'+$(this).attr('value')+'" class="draggable" cloum="'+$(this).attr('c')+'" t="'+$(this).attr('t')+'" style="left: "'+ persPrint_leftElement +'"px; top: "'+ persPrint_topElement +'"px;">'+ $(this).attr('value') +'</div>');
		if($(this).attr('t') == "photo")
		{
			$f = $('<div id="'+$(this).attr('value')+'" value="'+$(this).attr('value')+'" class="draggable photo" cloum="'+$(this).attr('c')+'" t="'+$(this).attr('t')+'" style="left: "'+ persPrint_leftElement +'"px; top: "'+ persPrint_topElement +'"px;">'+ $(this).attr('value') +'</div>');
		}
		if($(this).is(':checked')){
			$("#"+ persPrint_trObject +" .containment-wrapper").append($f);
		}
		else{
			$("#"+$(this).attr('value').split(" ")[0]).remove();
		}
		customDrag($f);
	});
});

function onSubmit()
{
	if(!persPrint_bgFlag)
	{
		openMessage(msgType.warning, persPrint_bgMsg);
		return;
	}
	var data = new Array();
	data[0] = {
		type:"background",
		w:$("#"+ persPrint_trObject +" .containment-wrapper").width(),
		h:$("#"+ persPrint_trObject +" .containment-wrapper").height(),
		back:$("#"+ persPrint_trObject +" .containment-wrapper").attr("back"),
	}
	$("#"+ persPrint_trObject +" .containment-wrapper .draggable").each(function(i,v){
		$(this).removeClass("selected");
		var ele = {
			cell:$(this).attr("cloum"),
			left:$(this).css('left'),
			top:$(this).css('top'),
			zIndex:$(this).css('z-index'),
			width:$(this).width(),
			height:$(this).height(),
			type:$(this).attr("t"),
			classs:$(this).attr("class"),
			color:$(this).css("color"),
			fontSize:$(this).css("font-size"),
			align:$(this).css("text-align"),
			fontFamily:$(this).css("font-family"),
			value:$(this).attr("value"),
		}
		data[i+1] = ele;
	});
	if(eval($("#persPrint_surface").val()) === "front"){
		persPrintFrontData = data;
 		$("#editTemplateFront").show();
 		$("#addTemplateFront").hide();
 		$("#persPrint_templateCode").attr("frontf","t");
	}
	else{
		persPrintOppositeData = data;
 		$("#editTemplateOppo").show();
 		$("#addTemplateOppo").hide();
 		$("#persPrint_templateCode").attr("oppof","t");
	}
    DhxCommon.closeWindow();
}