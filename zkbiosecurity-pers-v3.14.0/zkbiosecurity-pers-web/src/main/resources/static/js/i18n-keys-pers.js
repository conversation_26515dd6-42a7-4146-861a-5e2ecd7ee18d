I18n.load([
    "pers_person_pinInteger",
    "pers_person_pinPrompt",
    "pers_person_pinFirstValid",
    "pers_person_pwdOnlyLetterNum",
    "pers_person_cardLengthDigit",
    "pers_person_cardLengthHexadecimal",
    "pers_person_emailError",
    "pers_card_note",
    "pers_person_certNumOnlyLetterNum",
    "pers_person_idCardValidate",
    "pers_cert_numberExist",
    "pers_person_msg3",
    "pers_card_deputyCardValid",
    "pers_card_notMoreThanSixteen",
    "pers_person_templateCount",
    "pers_person_faceTemplateCount",
    "pers_person_VeinTemplateCount",
    "pers_person_palmTemplateCount",
    "pers_person_cropFaceCount",
    "pers_person_faceBiodataCount",
    "pers_card_notDelAll",
    "pers_card_maxCard",
    "pers_import_cardNoNotNull",
    "pers_person_chooseDoor",
    "pers_dept_changeLevel",
    "pers_person_picMaxSize",
    "pers_person_picMinSize",
    "pers_cardTemplate_front",
    "pers_cardTemplate_opposite",
    "pers_person_male",
    "pers_person_female",
    "pers_cardTemplate_printing",
    "pers_cardTemplate_jpgFormat",
    "pers_cardTemplate_uploadFail",
    "pers_common_personCount",
    "pers_common_browsePerson",
    "pers_person_wholeName",
    "pers_dept_entity",
    "pers_card_cardNo",
    "pers_person_pin",
    "pers_dept_deptName",
    "pers_person_name",
    "pers_person_mobilePhone",
    "pers_person_internalCard",
    "pers_person_birthday",
    "pers_position_entity",
    "pers_attribute",
    "pers_attr_emp_type",
    "pers_attr_hire_type",
    "pers_attr_job_title",
    "pers_attr_street",
    "pers_attr_birthplace",
    "pers_attr_country",
    "pers_attr_home_phone",
    "pers_attr_home_address",
    "pers_attr_office_phone",
    "pers_attr_office_address",
    "pers_attr_polit_status",
    "pers_attr_nation",
    "pers_op_capture",
    "pers_import_phoneTooLong",
    "pers_common_addPerson",
    "pers_tempPerson_audit",
    "pers_tempPerson_view",
    "pers_leavePerson_reinstated",
    "pers_card",
    "pers_person_password",
    "pers_person_regFinger",
    "pers_person_regVein",
    "pers_person_metacarpalVein",
    "pers_person_infraredFace",
    "pers_person_cropFace",
    "pers_person_mobilePhoneValidate",
    "pers_person_photoUseCropFace",
    "pers_import_photoFormatError",
    "pers_person_readCard",
    "pers_card_delFirst",
    "pers_issueCard_error",
    "common_vdb_passwordValid",
    "common_vdb_extensionValid",
    "pers_person_readCarding",
    "pers_issueCard_mc5000",
    "pers_person_photoUseCropFaceAndTempalte",
    "pers_person_createCropFace",
    "pers_person_createFaceTempalte",
    "pers_person_photoUseTempalte",
    "pers_person_faceTempalte",
    "pers_person_createSuccess",
    "pers_person_createFail",
    "pers_person_iris",
    "pers_person_cropFaceUsePhoto",
    "base_qrcode_staticQrCode",
    "base_qrcode_openStaticQrCode"
], true);