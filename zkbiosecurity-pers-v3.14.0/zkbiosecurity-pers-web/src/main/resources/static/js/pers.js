var persParams = {};

function loadPersParams() {
    $.ajax({
        url: "/persParams.do?getParams",
        async: false,
        success: function (result) {
            if (result[sysCfg.ret] == sysCfg.success) {
                persParams = $.extend({}, persParams, result[sysCfg.data]);
            }
        }
    });
}

loadPersParams();

jQuery.validator.addMethod("pinNum", function (value, element) {
    if (persParams['pers.pinSupportLetter'] == 'false') {
        var pattenNum = /^[0-9]*$/;
        return this.optional(element) || (pattenNum.test(value));
    } else if (persParams['pers.pinSupportLetter'] == 'true') {
        var pattenChar = /^(?!0$|0+$)[a-zA-Z0-9]+$/;
        return this.optional(element) || (pattenChar.test(value));
    }
}, function () {
    if (persParams['pers.pinSupportLetter'] == 'false') {
        return I18n.getValue("pers_person_pinInteger");
    } else if (persParams['pers.pinSupportLetter'] == 'true') {
        return I18n.getValue("pers_person_pinPrompt");
    }
});
//pin号首位验证
jQuery.validator.addMethod("pinNumFirst", function (value, element) {
    if (value.length != 9) {
        return true;
    } else {
        var pattenNum = /^[A-Za-z0-7]/;
        return this.optional(element) || (pattenNum.test(value));
    }
}, function () {
    return I18n.getValue("pers_person_pinFirstValid");
});
//密码验证
jQuery.validator.addMethod("validPersonPwd", function (value, element) {
    var pattenChar = /^[A-Za-z0-9]{1,8}$/;  //  ^[A-Za-z0-9]{1,8}$
    return this.optional(element) || (pattenChar.test(value));

}, function () {
    return I18n.getValue("pers_person_pwdOnlyLetterNum");
});

jQuery.validator.addMethod("cardNum", function (value, element) {
    if (persParams['pers.cardHex'] == "1") {
        var pattenChar = /^[A-Fa-f0-9]*$/;//十六进制
        return this.optional(element) || (pattenChar.test(value));
    }
    var pattenNum = /^\d*$/;//十进制显示
    return this.optional(element) || (pattenNum.test(value));
}, function () {
    if (persParams['pers.cardHex'] == "1") {
        return I18n.getValue("pers_person_cardLengthHexadecimal");//十六进制
    }
    return I18n.getValue("pers_person_cardLengthDigit");//十进制显示
});

jQuery.validator.addMethod("cardLen", function (value, element) {
    	if(value==""){
    		return true;
    	}
    	var flag = true;
    	$.ajax({
    		url:"persCard.do?checkCardBit",
    		type:"post",
    		dataType:"json",
    		async :false,
    		data:{"cardNo": function(){return value;}},
    		success: function (data)
            {
     			flag = data;
            },
            error: function (XMLHttpRequest, textStatus, errorThrown)
            {
            	messageBox({messageType: "alert", title: I18n.getValue("common_prompt_title"), text: I18n.getValue("common_prompt_serverError")});
            }
    	});
    	return flag;
    },function(){
    	var maxValue = getMaxValue();
        if (persParams['pers.cardHex'] == "0") { //十进制显示
            return I18n.getValue("pers_card_maxCard", parseInt(maxValue, 16));
        }
        return I18n.getValue("pers_card_maxCard", maxValue);
});

function getMaxValue() {
    var maxValue = "", r = persParams['pers.cardLen'] % 4, f = (persParams['pers.cardLen'] - r) / 4,
        v = ["", "1", "3", "7"];
    maxValue = v[r];
    for (var i = 0; i < f; i++) {
        maxValue += "F";
    }
    return maxValue;
}

//邮件验证
//jQuery.validator.addMethod("validEmail", function(value, element){
//var pattenChar =  /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/;  //  ^[A-Za-z0-9]{1,6}$
//		var pattenChar =/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/;
//		return this.optional(element) || (pattenChar.test(value));
//	}, function(){
//	return I18n.getValue("pers_person_emailError");
//});

jQuery.validator.addMethod("cardNoExistValid", function (value, element) {
    if (value == "") {
        return true;
    }
    var isExist1 = true;
    var pin = "";
    if (typeof editPersonFormId != "undefined") {
        pin = $(editPersonFormId + " input[name='pin']").val();
    }
    $.ajax({
        url: "/persCard.do?isExist",
        async: false,
        data: {
            "cardNo": value,
            "pin": pin
        },
        success: function (data) {
            isExist1 = data;
        }
    });
    return isExist1;
}, function () {
    return I18n.getValue("pers_card_note");
});

//日期格式校验 YYYY-MM-DD
jQuery.validator.addMethod("dateValid", function (value, element) {
    var pattenChar = /^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$/;
    return this.optional(element) || (pattenChar.test(value));

}, function () {
    return I18n.getValue("base_db_backupDateError");
});

//证件号只能输入数字跟字母
jQuery.validator.addMethod("certNumberLetterNum", function (value, element) {
    var pattenChar = /^[A-Za-z0-9]{1,20}$/;
    return this.optional(element) || (pattenChar.test(value));

}, function () {
    return I18n.getValue("pers_person_certNumOnlyLetterNum");
});

//增加身份证验证
function isIdCardNo(num) {
    var factorArr = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1);
    var parityBit = new Array("1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2");
    var varArray = new Array();
    var intValue;
    var lngProduct = 0;
    var intCheckDigit;
    var intStrLen = num.length;
    var idNumber = num;
    // initialize
    if (intStrLen != 18) {
        return false;
    }
    // check and set value
    for (i = 0; i < intStrLen; i++) {
        varArray[i] = idNumber.charAt(i);
        if ((varArray[i] < '0' || varArray[i] > '9') && (i != 17)) {
            return false;
        } else if (i < 17) {
            varArray[i] = varArray[i] * factorArr[i];
        }
    }

    if (intStrLen == 18) {
        //check date
        var date8 = idNumber.substring(6, 14);
        if (isDate8(date8) == false) {
            return false;
        }
        // calculate the sum of the products
        for (i = 0; i < 17; i++) {
            lngProduct = lngProduct + varArray[i];
        }
        // calculate the check digit
        intCheckDigit = parityBit[lngProduct % 11];
        // check last digit
        if (varArray[17] != intCheckDigit) {
            return false;
        }
    } else {
        return false;
    }
    return true;
}

//15位身份证验证
function isDate6(sDate) {
    if (!/^[0-9]{6}$/.test(sDate))
        return false;

    var year = sDate.substring(0, 4), month = sDate.substring(4, 6);
    if (year < 1900 || year > new Date().getFullYear())
        return false;

    if (month < 1 || month > 12)
        return false;

    return true
}

//18位身份证验证
function isDate8(sDate) {
    if (!/^[0-9]{8}$/.test(sDate))
        return false;

    var year = sDate.substring(0, 4), month = sDate.substring(4, 6), day = sDate.substring(6, 8);
    if (year < 1900 || year > new Date().getFullYear())
        return false;

    if (month < 1 || month > 12)
        return false;

    var iaMonthDays = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
    if (((year % 4 == 0) && (year % 100 != 0)) || (year % 400 == 0))
        iaMonthDays[1] = 29;
    if (day < 1 || day > iaMonthDays[month - 1])
        return false;

    return true;
}

//证件号重复校验
var isIdCardNo1 = true;
var isPersExist1 = true;
jQuery.validator.addMethod("certNumberValid", function (value, element) {
    if ($(editPersonFormId + " input[name='certNumber']").val() == "") {
        return true;
    }
    if ($(editPersonFormId + " input[name='certType']").val() == "2") {
        isIdCardNo1 = isIdCardNo(value);
    } else {
        isIdCardNo1 = true;
    }

    if (isIdCardNo1) {
        $.ajax({
            url: "/persPerson.do?checkCertNumber",
            type: "post",
            dataType: "json",
            async: false,
            data: {
                "certNumber": function () {
                    return $(editPersonFormId + " input[name='certNumber']").val();
                },
                "personId": function () {
                    return $(editPersonFormId + " input[name='id']").val();
                },
                "certType": function () {
                    return $(editPersonFormId + " input[name='certType']").val();
                }
            },
            success: function (data) {
                isPersExist1 = data;
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                messageBox({
                    messageType: "alert",
                    title: I18n.getValue("common_prompt_title"),
                    text: I18n.getValue("common_prompt_serverError")
                });
            }
        });
        return isPersExist1;
    } else {
        return isIdCardNo1;
    }
}, function () {
    if (typeof(isIdCardNo1) != "undefined" && !isIdCardNo1) {
        return I18n.getValue("pers_person_idCardValidate");
    } else if (!isPersExist1) {
        return I18n.getValue("pers_cert_numberExist");
    }
});

//验证界面图片按钮，调出file控件
jQuery.validator.addMethod("personPhotoButton", function (value, element) {
    value = $(editPersonFormId + " #personPhoto").val();
    if (value != "") {
        param = typeof param == "string" ? param.replace(/,/g, '|') : "png|jpe?g";
        return this.optional(element) || value.match(new RegExp(".(" + param + ")$", "i"));
    }
    return true;
}, function () {
    return I18n.getValue("pers_person_msg3");
});

//主副卡验证，需要先填主卡才能填副卡
jQuery.validator.addMethod("deputyCardValid", function (value, element) {
    var flag = true;
    if (value == "") {
        $(editPersonFormId + " #multiCardDiv .multiCard input[name^='multiCards']").each(function () {
            if ($(this).val() != '') {
                flag = false;
                return false;
            }
        });
    }
    return flag;
}, I18n.getValue("pers_card_deputyCardValid"));

//副卡重复性验证
jQuery.validator.addMethod("repeatCardValid", function (value, element) {
    var flag = true;
    if (value != "") {
        $(editPersonFormId + " .multiCard input[name^='multiCards']").each(function () {
            if ($(this).val() != '' && this != element && value.toLowerCase() == $(this).val().toLowerCase()) {
                flag = false;
                return false;
            }
        });
        if (flag) {
            setTimeout(function () {
                $(editPersonFormId + " .multiCard input[name^='multiCards']").each(function () {
                    if ($(this).val() != '' && this != element && $(this).hasClass("error")) {
                        $(this).valid();
                    }
                });
            }, 50);
        }
    }
    return flag;
}, I18n.getValue("pers_card_note"));

jQuery.validator.addMethod("picSizeValid", function (value, file) {
    if (/\.(jpg|jpeg|png)$/ig.test(value)) {
        //读取图片数据
        var filePic = file.files[0];
        var reader = new FileReader();
        reader.onload = function (e) {
            var data = e.target.result;
            //加载图片获取图片真实宽度和高度
            var image = new Image();
            image.onload = function () {
                var width = image.width;
                var height = image.height;
                // 1024 * 5
                if (parseInt(width) * parseInt(height) > (4000 * 4000)) {
                    messageBox({
                        messageType: "alert",
                        text: I18n.getValue("pers_person_picMaxSize").format("4000x4000"),
                        callback: function () {
                            $("#id_img_pers").attr("src", "/images/"+((sysCfg.skin=="default"||!sysCfg.skin)?"":sysCfg.skin)+"/userImage.gif");
                        }
                    });
                    file.value = "";
                    return false;
                }
                else if (parseInt(width) * parseInt(height) < (100 * 100)) {
                    messageBox({
                        messageType: "alert",
                        text: I18n.getValue("pers_person_picMinSize").format("100x100"),
                        callback: function () {
                            $("#id_img_pers").attr("src", "/images/"+((sysCfg.skin=="default"||!sysCfg.skin)?"":sysCfg.skin)+"/userImage.gif");
                        }
                    });
                    file.value = "";
                    return false;
                }
            };
            image.src = data;
        };
        reader.readAsDataURL(filePic);
    }
    return true;
});

//手机号码校验
jQuery.validator.addMethod("mobilePhoneValid", function (value, element) {
    if (value != "" && sysCfg.language == "zh_CN") {
        var pattenChar = /^1[1-9][0-9]\d{8}$/;
        return pattenChar.test(value);
    }
    return true;
}, function () {
    return I18n.getValue("pers_person_mobilePhoneValidate");
});

//姓名特殊符号校验
jQuery.validator.addMethod("nameUnInputChar", function(c, b) {
    var d = ["<", ">", "`", "~", "!", "@", "#", "$", "%", "^", "*", "?", "/", "|", "\\", ":", ";", "=", '"', "'", ",", "--", "+", "，", "。", "、", "；", "‘", "’", "【", "】", "、", "！", "￥", "……", "&", "*", "（", "）", "——"];
    for (var a = 0; a < d.length; a++) {
        if (c.indexOf(d[a]) >= 0) {
            return false
        }
    }
    return true
}, function() {
    return I18n.getValue("common_prompt_unIncludeChar").format("'<','>','`','~','!','@','#','$','%','^','*','?','/','|','\\',':',';','=','\"',''',',','--','+','，','。','、','；','‘','’','【','】','、','！','￥','……','&','*','（','）','——'")
});

function delCards(obj) {
    if ($(editPersonFormId + " #multiCardDiv .multiCard").length > 1 && obj.id != 'delCard_0') {
        $(obj).parent().remove();
    } else {
        if($(obj).parent().find("input[name^='multiCards']").attr("readonly") == "readonly")
        {
            $(obj).parent().find("input[name^='multiCards']").attr("readonly", false);
            $(obj).parent().find("input[name^='multiCards']").attr("acms", "0");
            $(obj).parent().find("input[name^='multiCards']").val("");
        }
        else
        {
            if(obj.id != 'delCard_0')
            {
                messageBox({
                    messageType: "alert",
                    title: I18n.getValue("common_prompt_title"),
                    text: I18n.getValue("pers_card_notDelAll")
                });
            }
        }
    }
    if(obj.id == 'delCard_0')
    {
        $(obj).hide();
    }
}

function addCards(obj) {
    if ($(editPersonFormId + " #multiCardDiv .multiCard").length < 16) {
        var $card = $(obj).parent().clone(false);
        $card.find("input").val("");
        var $input = $card.find("input[name^='multiCards']");
        var $span = $card.find("span[class^='readCard icv-base_readCard']");
        $span.show();
        var $stopReadCardSpan = $card.find("span[class^='stopReadCard icv-base_stopReadCard']");
        $stopReadCardSpan.hide();
        $input.removeClass("wait");
        var $cardNo = $card.find("input[name^='cardNo']");
        $cardNo.removeClass("wait");
        var index = parseInt($input.attr("index")) + 1;
        $input.attr({"name": "multiCards_" + index, "index": index, "readonly":false, "acms":"0", "value":""});
        $span.attr({"id": "multiCards_" + index});
        $(editPersonFormId + " #multiCardDiv").append($card);
        $input.rules("add", {
            cardNum: true,
            maxlength: 50,
            repeatCardValid: true,
            cardNoExistValid: true
        });
        $input.valid();
        readCardMouse();
    } else {
        messageBox({
            messageType: "alert",
            title: I18n.getValue("common_prompt_title"),
            text: I18n.getValue("pers_card_notMoreThanSixteen")
        });
    }
}

function readCardMouse()
{
    var myPop = new dhtmlXPopup();
    if(sysCfg.language != "zh_CN")
    {
        popContent = "<a href='javascript:void(0);' onclick='readCard(\"{0}\")'>"+I18n.getValue("pers_person_readCard")+"</a></br><a href='javascript:void(0);' onclick='readCardByAcms(\"{0}\")'>"+I18n.getValue("system_acms_credentials")+"</a>";
    }
    if(sysCfg.language == "zh_CN")
    {
        popContent = "<a href='javascript:void(0);' onclick='readCard(\"{0}\")'>"+I18n.getValue("pers_person_readCard")+"</a>&nbsp;&nbsp;<a href='javascript:void(0);' onclick='readCardByMC5000(\"{0}\")'>"+I18n.getValue("pers_issueCard_mc5000")+"</a>";
    }
    var showPop = function(objId){
        if($("input[name='id']").val() != ""){
            if($("input[name='"+objId+"']").val()!=""){
               $.ajax({
                   url:"persPerson.do?isOpenStaticQrCode",
                   type:"post",
                   async:"false",
                   dataType:"json",
                   success: function (result) {
                       if(result.data==true){
                           if($("input[name='"+objId+"']").val() != ""){
                           var index = objId.split("_")[1];
                               popContents=popContent+"</br><a href='javascript:void("+index+");' onclick='cardQrCode(\""+objId+"\")'>"+I18n.getValue("base_qrcode_staticQrCode")+"</a>";
                               myPop.attachHTML(popContents.format(objId));
                           }else{
                               myPop.attachHTML(popContent.format(objId));
                           }
                       }else{
                           myPop.attachHTML(popContent.format(objId));
                       }
                   },
                   error: function (XMLHttpRequest, textStatus, errorThrown)
                   {
                       messageBox({messageType: "alert", title: I18n.getValue("common_prompt_title"), text: I18n.getValue("common_prompt_serverError")});
                   }
               });
           }else{
               myPop.attachHTML(popContent.format(objId));
           }
        }else{
            myPop.attachHTML(popContent.format(objId));
        }
    };
    var _t;
    $(".readCard").mouseover(function(){
        showPop(this.id);
        // collect coordinates and dimensions
        var x = window.dhx4.absLeft(this);
        var y = window.dhx4.absTop(this);
        var width = this.offsetWidth;
        var height = this.offsetHeight;
        // show popup
        myPop.show(x, y, width, height);
        if(_t)
        {
            clearTimeout(_t);
        }
    }).mouseout(function(){
        if(_t)
        {
            clearTimeout(_t);
        }
        _t = setTimeout(function(){
            myPop.hide();
        },8000);
    });
}

/**
 * 多卡值更改
 * @param obj
 */
function multiCardsChange(obj) {
    $(obj).prev().val(obj.value);
}

var persCurCardNoObj, persReaderCardTime;

/**
 * 读卡
 * @param
 */
function readCard(objId) {
    if($("input[name^='multiCards'],input[name^='cardNo']").hasClass("wait"))
    {
        openMessage(msgType.warning, I18n.getValue("pers_person_readCarding"));
        return;
    }
    persCurCardNoObj = $("#"+objId);
    if($(persCurCardNoObj).prev().attr("disabled") == "disabled")
    {
        openMessage(msgType.warning, I18n.getValue("pers_card_delFirst"));
        return;
    }
    $(persCurCardNoObj).prev().focus();
    var sure = $(persCurCardNoObj).attr("onSure") || "persSelectReaderHandler";
    var opts = {//选人控件弹窗配置
        path: "/skip.do?page=acc_reader_accSelectReaderContent",//弹窗路径
        title: I18n.getValue("pers_person_chooseDoor"),//弹窗标题
        width: 900,//窗口宽度
        height: 470,//窗口高度
        onSure: sure,//回调事件
        callback: function () {
            var sid = $(this.cell).find(".select_layout_box")[0];
            if (sid) {
                DhxCommon.initEvents(ZKUI.Select.get(sid.id), ZKUI.Select.suport_evts, opts);
            }
        }
    };
    DhxCommon.createWindow(opts);
}
var stopReadCardTimer;
function persSelectReaderHandler(value, text, event) {
    if (stopReadCardTimer) {
        window.clearTimeout(stopReadCardTimer);
    }
    $("input[name='"+$(persCurCardNoObj).attr("id")+"']").focus();
    hideReaderCard($(persCurCardNoObj).parent());
    pullCardNo(value, "cardNo", "");
    stopReadCardTimer = window.setTimeout(function () {
        stopReadCard(persCurCardNoObj);
    }, 60000);//1分钟卡号没上来，自动恢复
}

//拉取卡号
function pullCardNo(readerIds, type, time) {
    $.get("/accTransaction.do?readerCardNo", {readerIds: readerIds, type: type, time: time}, function (result) {
        time = result[sysCfg.data].time;
        if (result[sysCfg.ret] == sysCfg.success && result[sysCfg.data].cardNo != "") {
            $("input[name='"+$(persCurCardNoObj).attr("id")+"']").val(result[sysCfg.data].cardNo.toLowerCase()).blur();
            stopReadCard(persCurCardNoObj);
        } else {
            persReaderCardTime = window.setTimeout(function () {
                pullCardNo(readerIds, type, time);
            }, 2000);
        }
    });
}

function readCardByMC5000(objId) {
    persCurCardNoObj = $("#"+objId);
    if($(editPersonFormId+" .readIDCard").attr("installed")){
        var issInstall = "";
        if(getEnvProperty("system.osType","window") == "window")
        {
            issInstall = "<span class='warningColor'>"+I18n.getValue('common_issOnline_notInstalled').format(I18n.getValue('common_issOnline_deviceDriver')) + "<a href="+$(editPersonFormId+" .readIDCard").attr("pluginUrl")+" class='devWarningColor'>"+I18n.getValue('common_issOnline_driverDownload')+"</a></span>";
        }
        else if(getEnvProperty("system.osType","window") == "linux")
        {
            issInstall = "<span class='warningColor'>"+I18n.getValue('common_issOnline_notInstalled').format(I18n.getValue('common_issOnline_deviceDriver')) + "<a>"+I18n.getValue('common_issOnline_driverDownload')+"(<a href="+$(editPersonFormId+" .readIDCard").attr("pluginUrl")+">Windows</a>, <a href="+$(editPersonFormId+" .readIDCard").attr("pluginLinuxUrl")+">Linux</a>)</a></span>";
        }
        messageBox({messageType: "alert", title: I18n.getValue('common_prompt_title'), text: issInstall});
        return;
    }
    onLoading(function(){
        setTimeout(function () {
            if(!MC5000Port)
            {
                checkMC5000Connect();
            }
            if(!MC5000Port)
            {
                openMessage(msgType.error, I18n.getValue("pers_issueCard_error"));
                return;
            }
            getMC5000Template("","","cardNo", MC5000CardNo);
        }, 500);
    });
}

function MC5000CardNo(result) {
    if (result.ret == 0 && result.cardNo != "") {
        var objId = $(persCurCardNoObj).attr("id");
        if (persParams['pers.cardHex'] == "1") {
            $("input[name='"+objId+"']").val(BigInt(result.cardNo.toLowerCase()).toString(16));
        }
        else {
            $("input[name='"+objId+"']").val(result.cardNo.toLowerCase());
        }
    }
    else{
        openMessage(msgType.error, I18n.getValue("pers_issueCard_error"));
    }
}
function cardQrCode(objName) {
    var cardNo = $("input[name='"+objName+"']").val();
    if(cardNo != "") {
        $.ajax({
            url:"persPerson.do?getCardQrCodeByCardNo",
            type:"post",
            dataType:"json",
            data: {
                "cardNo": cardNo
            },
            success: function (data) {
                 var opts = {//弹窗配置对象
                    path: "skip.do?page=pers_card_persCardQrCode&cardQrCode=" + data[sysCfg.data],//设置弹窗路径
                    width: 500,//设置弹窗宽度
                    height: 500,//设置弹窗高度
                    title: I18n.getValue("base_qrcode_staticQrCode"),//设置弹窗标题
                    gridName: "gridbox"//设置grid
                };
                DhxCommon.createWindow(opts);
            },
            error: function (XMLHttpRequest, textStatus, errorThrown)
            {
                messageBox({messageType: "alert", title: I18n.getValue("common_prompt_title"), text: I18n.getValue("common_prompt_serverError")});
            }
        });
     }
}
function readCardByAcms(objName) {
    if(!($("input[name='"+objName+"']").val() == null || $("input[name='"+objName+"']").val() == ""))
    {
        openMessage(msgType.warning, I18n.getValue("pers_card_delFirst"));
        return;
    }

    if("1" == $("input[name='acmsCardNos']").attr("acms"))
    {
        var cardNoVal = $("input[name='acmsCardNos']").val();
        if(cardNoVal)
        {
           var cardNos = JSON.parse(cardNoVal);
           if(cardNos && cardNos.length > 0)
           {
                var acmsCardNo = cardNos[0];
                $("input[name='"+objName+"']").attr("readonly","readonly");
               $("input[name='"+objName+"']").val(acmsCardNo.cardNo);
               $("input[name='"+objName+"']").attr("bussinessId", acmsCardNo.bussinessId);
               $("input[name='"+objName+"']").attr("activationCode", acmsCardNo.activationCode);
               $("input[name='"+objName+"']").attr("status", acmsCardNo.status);
               $("input[name='"+objName+"']").attr("cardOpType", acmsCardNo.cardOpType);
               $("input[name='"+objName+"']").attr("acms", "1");
               cardNos.splice(0,1);
               $("input[name='acmsCardNos']").val(JSON.stringify(cardNos));
           }
           else
           {
               openMessage(msgType.warning, I18n.getValue("system_acms_stock_empty"));
           }
        }
        else{
            openMessage(msgType.warning, I18n.getValue("system_acms_stock_empty"));
        }
    }
    else{
        onLoading(function(){
            $.ajax({
                url:"persPerson.do?obtainAcmsCard",
                type:"post",
                dataType:"json",
                success: function (data)
                {
                    if (data.ret == "ok")
                    {
                        closeMessage();
                        var cardNos = data.data;
                        if(cardNos && cardNos.length > 0)
                        {
                            var acmsCardNo = cardNos[0];
                            $("input[name='"+objName+"']").attr("readonly","readonly");
                            $("input[name='"+objName+"']").val(acmsCardNo.cardNo);
                            $("input[name='"+objName+"']").attr("bussinessId", acmsCardNo.bussinessId);
                            $("input[name='"+objName+"']").attr("activationCode", acmsCardNo.activationCode);
                            $("input[name='"+objName+"']").attr("status", acmsCardNo.status);
                            $("input[name='"+objName+"']").attr("cardOpType", acmsCardNo.cardOpType);
                            $("input[name='"+objName+"']").attr("acms", "1");
                            cardNos.splice(0,1);
                            $("input[name='acmsCardNos']").val(JSON.stringify(cardNos));
                            $("input[name='acmsCardNos']").attr("acms", "1");
                        }
                        else
                        {
                            openMessage(msgType.warning, I18n.getValue("system_acms_stock_empty"));
                        }
                    }
                    else if(data.msg)
                    {
                        openMessage(msgType.error, data.msg);
                    }
                    else
                    {
                        openMessage(msgType.error);
                    }
                },
                error: function (XMLHttpRequest, textStatus, errorThrown)
                {
                    messageBox({messageType: "alert", title: I18n.getValue("common_prompt_title"), text: I18n.getValue("common_prompt_serverError")});
                }
            });
        });

    }
    if("multiCards_0" == objName)
    {
        $("#delCard_0").show();
    }
}

//部门变动修改
function editPersonDeptChange(deptId, columIndex, checked) {
    if (typeof(loadDeptAccLevel) == "undefined" && typeof(loadDeptEleLevel) == "undefined") {
        return;
    }
    if (oldPersDeptId != deptId) {
        oldPersDeptId = deptId;
        if (isPersEditMode) {//编辑模式下
            //部门切换提示
            messageBox({
                messageType: "confirm",
                text: I18n.getValue("pers_dept_changeLevel"),
                callback: function (result) {
                    if (result) {
                        loadPersonDeptLevel(deptId);
                    }
                }
            });
        } else {
            loadPersonDeptLevel(deptId);
        }
    }
}

function loadDeptLevel(deptId) {
    if ($("input[name='id']").val() == "" && deptId != null && deptId != "") {
        loadPersonDeptLevel(deptId);
    }
}

//加载人员部门权限
function loadPersonDeptLevel(deptId) {
    if (typeof(loadDeptAccLevel) != "undefined") {
        loadDeptAccLevel(deptId);
    }
    if (typeof(loadDeptEleLevel) != "undefined") {
        loadDeptEleLevel(deptId);
    }
}
function firstLoadDeptLevel(deptId) {
    if ($("input[name='id']").val() == "" && deptId != null && deptId != "") {
        firstLoadPersonDeptLevel(deptId);
    }
}
//加载人员部门权限
function firstLoadPersonDeptLevel(deptId) {
    var loadAccLevelIntervalId = setInterval(function() {
        if(systemModules.toLowerCase().indexOf("acc") != -1) {
            if (typeof(loadDeptAccLevel) != "undefined") {
              loadDeptAccLevel(deptId);
              // 在需要停止定时器的时候，调用clearInterval()函数
              clearInterval(loadAccLevelIntervalId);
            }
        }
    }, 200);
    var loadEleLevelIntervalId = setInterval(function() {
        if(systemModules.toLowerCase().indexOf("ele") != -1) {
            if (typeof(loadDeptEleLevel) != "undefined") {
              loadDeptEleLevel(deptId);
              // 在需要停止定时器的时候，调用clearInterval()函数
              clearInterval(loadEleLevelIntervalId);
            }
        }
    }, 200);
}
/**
 * 结束读卡
 * @param obj
 */
function stopReadCard(obj) {
    var $parent = $(obj).parents(".multiCard");
    showReaderCard($parent);
    persCurCardNoObj = undefined;
    if (persReaderCardTime) {
        window.clearTimeout(persReaderCardTime);
    }
    if (stopReadCardTimer) {
        window.clearTimeout(stopReadCardTimer);
    }
}

/** 关闭定时器 */
function closeReaderTimeout() {
    if (persReaderCardTime) {
        window.clearTimeout(persReaderCardTime);
    }
}

function showReaderCard($parent) {
    $(persCurCardNoObj).prev().focus();
    $parent.find(".stopReadCard").hide();
    $parent.find(".wait").removeClass("wait");
    $parent.find(".readCard").show();
}

function hideReaderCard($parent) {
    $parent.find(".stopReadCard").show();
    $parent.find("input[name^='multiCards'],input[name^='cardNo']").addClass("wait");
    $parent.find(".readCard").hide();
}

function setPersPhotoPreview(objSuffix) {
    var suffix = '';
    if(objSuffix)
    {
        suffix = objSuffix;
    }
    var docObj = document.getElementById("personPhoto"+suffix);
    var valid = $(docObj).valid();
    var docObjButton = document.getElementById("personPhotoButton"+suffix);
    valid = valid && $(docObjButton).valid();
    //验证不通过，不进行预览
    var imgObjPreview = document.getElementById("id_img_pers"+suffix);
    if (!valid) {
        $(docObjButton).valid();
        imgObjPreview.src = "/images/"+((sysCfg.skin=="default"||!sysCfg.skin)?"":sysCfg.skin)+"/userImage.gif";
        return;
    }
    if (docObj.files && docObj.files[0]) {
        var fileTypeArr = docObj.value.split('.');
        var fileType = fileTypeArr[fileTypeArr.length - 1];
        if (fileType != null) {
            fileType = fileType.toLowerCase();
            if (fileType == 'jpg' || fileType == 'jpeg' || fileType == 'png') {
                //火狐下，直接设img属性
                if(!suffix)
                {
                    imgObjPreview.style.display = 'block';
                }
                imgObjPreview.style.width = '120px';
                imgObjPreview.style.height = '140px';
                //imgObjPreview.src = docObj.files[0].getAsDataURL();
                //火狐7以上版本不能用上面的getAsDataURL()方式获取，需要一下方式
                imgObjPreview.src = window.URL.createObjectURL(docObj.files[0]);
            }
        }
    } else {
        //IE下，使用滤镜
        docObj.select();
        var imgSrc = document.selection.createRange().text;
        var localImagId = document.getElementById("localImag");
        //必须设置初始大小
        localImagId.style.width = "120px";
        localImagId.style.height = "140px";
        //图片异常的捕捉，防止用户修改后缀来伪造图片
        try {
            localImagId.style.filter = "progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)";
            localImagId.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src = imgSrc;
        } catch (e) {
            openMessage(msgType.error, I18n.getValue("pers_person_msg3"));
            return false;
        }
        imgObjPreview.style.display = 'none';
        document.selection.empty();
    }
    validPicSize(docObj, suffix);
    return true;
}

function validPicSize(docObj, suffix) {
    if(!suffix)
    {
        suffix = '';
    }
    if (/\.(jpg|jpeg|png)$/ig.test(docObj.value)) {
        //读取图片数据
        var filePic = docObj.files[0];
        var reader = new FileReader();
        reader.onload = function (e) {
            var data = e.target.result;
            //加载图片获取图片真实宽度和高度
            var image = new Image();
            image.onload = function () {
                var width = image.width;
                var height = image.height;
                // 1024 * 5
                if (parseInt(width) * parseInt(height) > (4000 * 4000)) {
                    messageBox({
                        messageType: "alert",
                        text: I18n.getValue("pers_person_picMaxSize").format("4000x4000"),
                        callback: function () {
                            if(suffix)
                            {
                                if($("#personIdPhoto"+suffix).val())
                                {
                                    $("#id_img_pers"+suffix).attr("src","data:image/jpg;base64,"+$("#personIdPhoto"+suffix).val());
                                }
                                else
                                {
                                    $("#id_img_pers"+suffix).attr("src", "/images/"+((sysCfg.skin=="default"||!sysCfg.skin)?"":sysCfg.skin)+"/userImage.gif");
                                }
                            }
                            else{
                                $("#id_img_pers").attr("src", $("#id_img_pers").attr("oldSrc"));
                            }
                        }
                    });
                    docObj.value = "";
                    return false;
                }
                else if (parseInt(width) * parseInt(height) < (100 * 100)) {
                    messageBox({
                        messageType: "alert",
                        text: I18n.getValue("pers_person_picMinSize").format("100x100"),
                        callback: function () {
                            if(suffix)
                            {
                                if($("#personIdPhoto"+suffix).val())
                                {
                                    $("#id_img_pers"+suffix).attr("src","data:image/jpg;base64,"+$("#personIdPhoto"+suffix).val());
                                }
                                else
                                {
                                    $("#id_img_pers"+suffix).attr("src", "/images/"+((sysCfg.skin=="default"||!sysCfg.skin)?"":sysCfg.skin)+"/userImage.gif");
                                }
                            }
                            else{
                                $("#id_img_pers").attr("src", $("#id_img_pers").attr("oldSrc"));
                            }
                        }
                    });
                    docObj.value = "";
                    return false;
                }
                if(!suffix)
                {
                    $("#personIdPhoto").val(data.substring(data.indexOf(",") + 1, data.length));
                }
                validPersonPhoto(suffix);
            };
            image.src = data;
        };
        reader.readAsDataURL(filePic);
    }
    return true;
}

function validPersonPhoto(suffix) {
    if(!suffix)
    {
        suffix = '';
    }
    onLoading(function () {
        $.ajaxFileUpload({
            url: '/persPerson.do?validPersonPhoto', //用于文件上传的服务器端请求地址
            secureuri: false, //是否需要安全协议，一般设置为false
            fileElementId: 'personPhoto'+suffix, //文件上传域的ID
            dataType: 'json', //返回值类型 一般设置为json
            data:
                {
                    'personIdPhoto': $("#personIdPhoto"+suffix).val()
                },
            fileParentElementId: document.getElementById("personPhoto"+suffix).parentElement.id,
            newElementFlag: false,
            success: function (data, status)  //服务器成功响应处理函数
            {
                if (data[sysCfg.ret] == sysCfg.success) {
                    var successColor = "#7ac143";
                    var systemSkin = (sysCfg.skin=='default' || !sysCfg.skin) ? "" : sysCfg.skin;
                    // 深色模式修改颜色
                    if (systemSkin == "techblue") {
                        successColor = "#42AFFE";
                    }
                    if(suffix){
                        $("#personIdPhoto"+suffix).val(data.data);
                        $("#id_img_pers"+suffix).attr("src","data:image/jpg;base64,"+data.data);
                        var bioData = $("#bioData").val();
                        if(bioData != "") {
                            bioData = eval("(" + bioData + ")");
                            bioData.vislightPhotoList=["data:image/jpg;base64,"+data.data];
                            $("#bioData").val(JSON.stringify(bioData));
                            if (persParams['pers.facialTemplate.enable'] == '1') {
                                var tipText = I18n.getValue("pers_person_photoUseTempalte") + "<br/>"
                                + "<div><span id='tempalteFaceSpan'></span>"+ I18n.getValue("pers_person_createFaceTempalte") +"</div>";
                                loadUIToDiv("input", "#tempalteFaceSpan", {
                                  useInputReq:true,
                                  type:"checkbox",
                                  id:"tempalteFaceDev",
                                  name:"tempalteFaceDev",
                                  value:"1",
                                  trueValue:"1",
                                  falseValue:"0",
                                  eventCheck:true,
                                  eventCheck:true,
                                  onchange:"changeTempalteFace()"
                                });
                                messageBox({messageType:"confirm", text: tipText, callback: function(result){
                                    if(result) {
                                        if($("#isTempalteFace").val() == "1")
                                        {
                                            $.ajax({
                                                url: "persPerson.do?getFaceTemplate",
                                                type: "post",
                                                dataType: "JSON",
                                                async: false,
                                                data: {
                                                    "cropPhoto": data.data
                                                },
                                                success: function (ret) {
                                                    var msgBox = "";
                                                    if (ret[sysCfg.ret] == "0") {
                                                        msgBox += I18n.getValue("pers_person_faceTempalte") +":<span style='color:"+successColor+"'>"+ I18n.getValue("pers_person_createSuccess")+"</span>";
                                                         bioData["vislightList"]["0_0_"+ret.data.version] = {
                                                                templateNo:"0",
                                                                templateNoIndex:"0",
                                                                template:ret.data.template,
                                                                version:ret.data.version
                                                            };
                                                            $("#bioData").val(JSON.stringify(bioData));
                                                    }
                                                    else{
                                                        msgBox += I18n.getValue("pers_person_faceTempalte") +":<span style='color:#FE6257'>"+ I18n.getValue("pers_person_createFail") + "," + ret.msg+"</span>";
                                                    }
                                                    messageBox({messageType: "alert", text: msgBox});

                                                },
                                                error: function (XMLHttpRequest, textStatus, errorThrown) {
                                                    messageBox({
                                                        messageType: "alert",
                                                        title: I18n.getValue("common_prompt_title"),
                                                        text: I18n.getValue("common_prompt_serverError")
                                                    });
                                                }
                                            });
                                        }
                                    }
                                }});
                            }
                        }
                    }
                    else{
                        var tipText = I18n.getValue("pers_person_photoUseCropFace");
                        if (persParams['pers.facialTemplate.enable'] == '1') {
                            tipText = I18n.getValue("pers_person_photoUseCropFaceAndTempalte") + "<br/>"
                            + "<div style='text-align: left;margin-left: 22px;'><span id='cropFaceSpan'></span>"+ I18n.getValue("pers_person_createCropFace") +"</div>"
                            + "<div style='text-align: left;margin-left: 42px;'><span id='tempalteFaceSpan'></span>"+ I18n.getValue("pers_person_createFaceTempalte") +"</div>";
                            loadUIToDiv("input", "#cropFaceSpan", {
                              useInputReq:true,
                              type:"checkbox",
                              id:"cropFaceDev",
                              name:"cropFaceDev",
                              value:"1",
                              trueValue:"1",
                              falseValue:"0",
                              eventCheck:true,
                              onchange:"changeCropFace()"
                            });
                            loadUIToDiv("input", "#tempalteFaceSpan", {
                              useInputReq:true,
                              type:"checkbox",
                              id:"tempalteFaceDev",
                              name:"tempalteFaceDev",
                              value:"1",
                              trueValue:"1",
                              falseValue:"0",
                              eventCheck:true,
                              eventCheck:true,
                              onchange:"changeTempalteFace()"
                            });
                        }
                        messageBox({messageType:"confirm", text: tipText, callback: function(result){
                            if(result && $("#isCropFace").val() == "1") {
                                var bioData = $("#bioTemplateJson").val();
                                if(bioData != "") {
                                    bioData = eval("(" + bioData + ")");
                                }
                                else{
                                    bioData = {
                                        fpCount:0,
                                        fpList:{},
                                        fvCount:0,
                                        fvList:{},
                                        faceCount:0,
                                        faceList:{},
                                        palmCount:0,
                                        palmList:{},
                                        vislightCount:0,
                                        vislightList:{}
                                    }
                                }
                                bioData.vislightPhotoList=new Array();
                                bioData.vislightPhotoList=["data:image/jpg;base64,"+data.data];
                                $("input[name='cropPhotoBase64']").val(data.data);
                                if(persParams['pers.facialTemplate.enable'] == '1' && $("#isTempalteFace").val() == "1")
                                {
                                    openMessage(msgType.loading);
                                    setTimeout(function () {
                                    $.ajax({
                                        url: "persPerson.do?getFaceTemplate",
                                        type: "post",
                                        dataType: "JSON",
                                        async: false,
                                        data: {
                                            "cropPhoto": data.data
                                        },
                                        success: function (ret) {
                                            closeMessage();
                                            var msgBox = I18n.getValue("pers_person_cropFace") +""+ "<br/>"
                                            msgBox = I18n.getValue("pers_person_cropFace") +":<span style='color:"+successColor+"'>"+ I18n.getValue("pers_person_createSuccess") + "</span><br/>";
                                            if (ret[sysCfg.ret] == "0") {
                                                msgBox += I18n.getValue("pers_person_faceTempalte") +":<span style='color:"+successColor+"'>"+ I18n.getValue("pers_person_createSuccess")+"</span>";
                                                 bioData["vislightList"]["0_0_"+ret.data.version] = {
                                                        templateNo:"0",
                                                        templateNoIndex:"0",
                                                        template:ret.data.template,
                                                        version:ret.data.version
                                                    };
                                                 $("#bioTemplateJson").val(JSON.stringify(bioData));
                                            }
                                            else{
                                                msgBox += I18n.getValue("pers_person_faceTempalte") +":<span style='color:#FE6257'>"+ I18n.getValue("pers_person_createFail") + "," + ret.msg+"</span>";
                                            }
                                            messageBox({messageType: "alert", text: msgBox});

                                        },
                                        error: function (XMLHttpRequest, textStatus, errorThrown) {
                                            closeMessage();
                                            messageBox({
                                                messageType: "alert",
                                                title: I18n.getValue("common_prompt_title"),
                                                text: I18n.getValue("common_prompt_serverError")
                                            });
                                        }
                                    });
                                    },500);
                                }
                                $("#bioTemplateJson").val(JSON.stringify(bioData));
                            }
                        }});
                    }
                    closeMessage();
                }
                else {
                    if(suffix){
                        if($("#personIdPhoto"+suffix).val())
                        {
                            $("#id_img_pers"+suffix).attr("src","data:image/jpg;base64,"+$("#personIdPhoto"+suffix).val());
                        }
                        else
                        {
                            $("#id_img_pers"+suffix).attr("src", "/images/"+((sysCfg.skin=="default"||!sysCfg.skin)?"":sysCfg.skin)+"/userImage.gif");
                        }
                    }
                    openMessage(msgType.warning, data[sysCfg.msg], 5000);
                }
            }
        });
    });
}

function changeCropFace()
{
    if($("#cropFaceDev").is(":checked"))
    {
         $("#isCropFace").val('1');
        $("#tempalteFaceDev").attr('disabled',false);
    }
    else{
        $("#isCropFace").val('0');
        $("#isTempalteFace").val('0');
        $("#tempalteFaceDev").attr('checked',false);
        $("#tempalteFaceDev").attr('disabled',true);
    }
}

function changeTempalteFace()
{
    if($("#tempalteFaceDev").is(":checked"))
    {
        $("#isTempalteFace").val('1');
    }
    else{
        $("#isTempalteFace").val('0');
    }
}

function persConvertVerifyMode(verifyMode) {
    var countArr = verifyMode.split("_");
    var html = "";
    if (countArr[7] > 0) {
        html += '<span class="icv-ico_card" style="display: inline-block;margin-right: 4px;cursor: pointer;" onmouseover="gridMouseOver(event,\''+I18n.getValue("pers_card")+'\')" onmouseout="gridMouseOut(event)"></span>';
    }
    if (countArr[6] > 0) {
        html += '<span class="icv-ico_pwd" style="display: inline-block;margin-right: 4px;cursor: pointer;" onmouseover="gridMouseOver(event,\''+I18n.getValue("pers_person_password")+'\')" onmouseout="gridMouseOut(event)"></span>';
    }
    if (countArr[0] > 0) {
        html += '<span class="icv-ico_fingerprint" style="display: inline-block;margin-right: 4px;cursor: pointer;" onmouseover="gridMouseOver(event,\''+I18n.getValue("pers_person_regFinger")+'\')" onmouseout="gridMouseOut(event)"></span>';
    }
    if (countArr[1] > 0) {
        html += '<span class="icv-ico_metric" style="display: inline-block;margin-right: 4px;cursor: pointer;" onmouseover="gridMouseOver(event,\''+I18n.getValue("pers_person_infraredFace")+'\')" onmouseout="gridMouseOut(event)"></span>';
    }
    if (countArr[2] > 0) {
        html += '<span class="icv-ico_finger_vein" style="display: inline-block;margin-right: 4px;cursor: pointer;" onmouseover="gridMouseOver(event,\''+I18n.getValue("pers_person_regVein")+'\')" onmouseout="gridMouseOut(event)"></span>';
    }
    if (countArr[3] > 0 || countArr[8] > 0 || countArr[9] > 0) {
        html += '<span class="icv-ico_palm" style="display: inline-block;margin-right: 4px;cursor: pointer;" onmouseover="gridMouseOver(event,\''+I18n.getValue("common_newVerify_mode_pv")+'\')" onmouseout="gridMouseOut(event)"></span>';
    }
    if (countArr[4] > 0 || countArr[5] > 0) {
        html += '<span class="icv-ico_biophoto" style="display: inline-block;margin-right: 4px;cursor: pointer;" onmouseover="gridMouseOver(event,\''+I18n.getValue("pers_person_cropFace")+'\')" onmouseout="gridMouseOut(event)"></span>';
    }
    if (countArr[10] > 0) {
        html += '<span class="icv-ico_iris" style="display: inline-block;margin-right: 4px;cursor: pointer;" onmouseover="gridMouseOver(event,\''+I18n.getValue("pers_person_iris")+'\')" onmouseout="gridMouseOut(event)"></span>';
    }
    return html;
}

// 邮箱重复校验 add by bob.liu 20190920
jQuery.validator.addMethod("emailIsExistValid", function (value, element) {
    var isExistValid = true;
    if ($(editPersonFormId + " input[name='mail']").val() == "") {
        return true;
    }
    $.ajax({
        url: "/persPerson.do?checkEmailIsExist",
        type: "post",
        dataType: "JSON",
        async: false,
        data: {
            "personId": function () {
                return $(editPersonFormId + " input[name='id']").val();
            },
            "email": function () {
                return $(editPersonFormId + " input[name='mail']").val();
            }
        },
        success: function (data) {
            isExistValid = data;
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            messageBox({
                messageType: "alert",
                title: I18n.getValue("common_prompt_title"),
                text: I18n.getValue("common_prompt_serverError")
            });
        }
    });
    return isExistValid;
}, I18n.getValue("common_jqMsg_remote"));

function openCaptureContent() {
    var opts = {
        path: "skip.do?page=pers_person_opCaptureContent",
        width: 465,
        height: 350,
        title: I18n.getValue("pers_op_capture")
    };
    DhxCommon.createWindow(opts);
}

function setSelectedLevelIdsBeforeSubmit() {
    if (typeof(accLevelGetSelectedIds) != "undefined") {
        accLevelGetSelectedIds();
    }
    if (typeof(eleLevelGetSelectedIds) != "undefined") {
        eleLevelGetSelectedIds();
    }
    if (typeof(psgLevelGetSelectedIds) != "undefined") {
        psgLevelGetSelectedIds();
    }
    if(typeof(vdbLevelGetSelectedIds) != "undefined") {
        vdbLevelGetSelectedIds();
    }
}

/** 提交前业务模块数据验证 */
function validDataBeforeSubmit() {
    var result = true;
    if (typeof(validAccDataBeforeSubmit) != "undefined") {
        result = validAccDataBeforeSubmit();
        if (!result) {
            return result;
        }
    }
    if (typeof(validEleDataBeforeSubmit) != "undefined") {
        result = validEleDataBeforeSubmit();
        return result;
    }
    return result;
}
function submitMC5000FacePhoto(cropPhotoBase64)
{
    var bioData = $("#bioTemplateJson").val();
    if(bioData != "") {
        bioData = eval("(" + bioData + ")");
    }
    else{
        bioData = {
            fpCount:0,
            fpList:{},
            fvCount:0,
            fvList:{},
            faceCount:0,
            faceList:{},
            palmCount:0,
            palmList:{}
        }
    }
    bioData.vislightPhotoList=new Array();
    bioData.vislightPhotoList=["data:image/jpg;base64,"+cropPhotoBase64];
    $("#bioTemplateJson").val(JSON.stringify(bioData));
    $("input[name='cropPhotoBase64']").val(cropPhotoBase64);
    changePersonPhoto(cropPhotoBase64);
}
function changePersonPhoto(cropPhotoBase64)
{
    messageBox({messageType:"confirm", text: I18n.getValue("pers_person_cropFaceUsePhoto"), callback: function(result){
        if(result) {
            $("#id_img_pers").attr("src", "data:image/jpg;base64,"+cropPhotoBase64);
           $("#personIdPhoto").val(cropPhotoBase64);
        }
    }});
}