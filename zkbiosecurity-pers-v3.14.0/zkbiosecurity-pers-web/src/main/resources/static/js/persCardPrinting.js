$(function(){
    ZKDevice.get(ZKDevType.CardPrinter).EnumDevices({
        ClientType:"8",
        callback:function(result){
            if(result.ret == -10046)
            {
                var $f = $('<div class="warningColor" style="padding-left: 10px;"><img src="/public/controls/dhtmlx/skins/web/imgs/dhxtree_web/comm_iconAlert.png" /><b> <font size="2px">'+I18n.getValue("common_issOnline_devBusy")+'</font> </b></div>');
                $("#persCarsPrinterWarnTd").append($f);
                $("#cardPrintButtonId").attr("disabled",true);
            }
            else if(result.ret == 0)
            {
                var printerDevices = result.data;
                ZKDevice.get(ZKDevType.NationCard).GetRegisteInfo({
                    nationID : "CardPrinter",
                    clientType : "8",
                    callback:function(result){
                        if(result.ret == 0 && result.registrationCode != "" && result.enable != "0")
                        {
                            $.ajax({
                                url:"/baseRegister.do?isActivationRegistCode",
                                type:"post",
                                dataType:"json",
                                async :false,
                                data:{"registrationCode":result.registrationCode, "clientType":"8"},
                                success: function (data)
                                {
                                    if(data.ret == "ok")
                                    {
                                        var printerOptions = new Array()
                                        for(i=0; i<printerDevices.length; i++){
                                            printerOptions.push({text:printerDevices[i].name, value:i})
                                        }
                                        ZKUI.Combo.get('persCardPrinter').addOption(printerOptions);
                                        if($.cookie('persCardPrinter') != undefined && $.cookie('persCardPrinter') != 'NaN' && $.cookie('persCardPrinter') != ZKUI.Combo.get('persCardPrinter').combo.getSelectedValue())
                                        {
                                            ZKUI.Combo.get('persCardPrinter').combo.setComboValue($.cookie('persCardPrinter'));
                                        }
                                        if(ZKUI.Combo.get('persCardPrinter').combo.getSelectedValue() == null) {
                                            ZKUI.Combo.get('persCardPrinter').combo.selectOption(0)
                                        }
                                        $("#cardPrintButtonId").attr("disabled",false);
                                    }
                                    else
                                    {
                                        $("#persCarsPrinterDriverDownload").attr("style","display:block");
                                        $("#persCardPrinterSpan").html(I18n.getValue("base_noAuthorization"));
                                        $("#cardPrintButtonId").attr("disabled",true);
                                    }
                                }
                            });
                        }

                    }});
            }
            else if(result.ret == -1011)
            {
                $("#persCarsPrinterDriverDownload").attr("style","display:block");
                $("#persCardPrinterSpan").html(I18n.getValue("base_noAuthorization"));
                $("#cardPrintButtonId").attr("disabled",true);
            }
        },
        errorback:function(result){
            $("#persCarsPrinterDriverDownload").attr("style","display:block");
            $("#persCardPrinterSpan").html(I18n.getValue("base_download_noDriveMsg").format("<a href='upload/ZKDeviceAuthOnline.exe' class='devWarningColor'>"+I18n.getValue("base_driver_downdriver")+"</a>"));
        }

    });

	var t;
	var tabbar = null;
	var printData = null;
	tabbar = new dhtmlXTabBar("persCardPrint_tabbar", "top");
	tabbar.addTab("persCardPrint_front", I18n.getValue("pers_cardTemplate_front"), "*");
	tabbar.tabs("persCardPrint_front").attachObject("persCardPrint_front",true);
	tabbar.tabs("persCardPrint_front").setActive();
	tabbar.addTab("persCardPrint_opposite", I18n.getValue("pers_cardTemplate_opposite"), "*");
	tabbar.tabs("persCardPrint_opposite").attachObject("persCardPrint_opposite",true);
	tabbar.attachEvent("onTabClick", function(id, lastId){
		if(id == "persCardPrint_front")
		{
			$("#persCardPrint_frontAttrGridbox").appendTo($("#persCardPrint_frontAttrLi"));
		}
		else
		{
			$("#persCardPrint_frontAttrGridbox").appendTo($("#persCardPrint_oppoAttrLi"));
		}
	});

    ZKUI.Combo.get('persCardPrintTemplate').combo.attachEvent("onChange", function(){
        xmlDataTest(ZKUI.Combo.get('persCardPrintTemplate').combo.getSelectedValue());
    });

    //模板预览
    function xmlDataTest(templateId){
        $.ajax({
            url:"/basePrintTemplate.do?getItemById",
            type: "post",
            dataType: "json",
            async : false,
            data:{"id":templateId},
            success: function(result)
            {
                var dataMap = new Map();
                dataMap.set("front",eval("("+result.data.frontData+")"));
                dataMap.set("opposite",eval("("+result.data.oppositeData+")"));
                var printWidth = 318;
                var printHeight = 505;
                // 横向
                if(result.data.orientation != 0)
                {
                    printWidth = 505;
                    printHeight = 318;
                }
                // $("#persCardPrint_previewFront, #persCardPrint_previewOppo").css({width:result.data.width+"px",height:result.data.height+"px"});
                dataMap.forEach(function(dataArray,key) {
                    var html = "";
                    var face = "#persCardPrint_previewFront";
                    if(key == "opposite"){
                        face = "#persCardPrint_previewOppo";
                    }
                    $face = $(face);
                    $face.attr("style","background-color:#ffffff;background-size: 100% 100%;margin: 5px auto;width:"+printWidth+"px;height:"+printHeight+"px");
                    dataArray.forEach(function (data, key) {
                        switch(data.printType){
                            case "background":
                                if(data.printValue != undefined)
                                {
                                    $face.attr("style","background:transparent url('"+ data.printValue +"') no-repeat scroll 0 0;background-size: 100% 100%;margin: 5px auto;width:"+printWidth+"px;height:"+printHeight+"px");
                                }
                                break;
                            case "photo":
                                if(eval(data.cell) == null || eval(data.cell) == "")
                                {
                                    html += '<div class="'+ data.style +'" style="left: '+ data.leftPoint +'; top: '+ data.topPoint +';"><img width="'+data.width+'" height="'+data.height+'" src="/images/userImage.gif"/></div>';
                                }
                                else
                                {
                                    html += '<div class="'+ data.style +'" style="left: '+ data.leftPoint +'; top: '+ data.topPoint +';"><img width="'+data.width+'" height="'+data.height+'" src="'+ eval(data.cell) +'" onerror="this.src=\'/images/userImage.gif\'"/></div>';
                                }
                                break;
                            case "images":
                                html += '<div class="'+ data.style +'" style="padding:0px; left: '+ data.leftPoint +'; top: '+ data.topPoint +';"><img width="'+ data.width +'" height="'+ data.height +'" src="'+ data.cell +'"/></div>';
                                break;
                            case "line":
                                html +='<div class="draggable line" style="color:'+ data.color +'; left: '+ data.leftPoint +'; top: '+ data.topPoint +';"></div>';
                                break;
                            case "txt":
                                html +='<div class="'+ data.style +'" style="font-size:'+ data.fontSize+'; color:'+ data.color +'; left: '+ data.leftPoint +'; top: '+ data.topPoint +';background-color: unset;border: none;">'+ data.cell +'</div>';
                                break;
                            case "text":
                                var cell = eval(data.cell);
                                if(cell != undefined)
                                {
                                    if(cell == "M")
                                    {
                                        cell = I18n.getValue("common_male");
                                    }
                                    else if(cell == "F")
                                    {
                                        cell = I18n.getValue("common_female");
                                    }
                                    html +='<div class="'+ data.style +'" style="font-size:'+ data.fontSize+'; color:'+ data.color +'; left: '+ data.leftPoint +'; top: '+ data.topPoint +';">'+ cell +'</div>';
                                }
                                break;
                        }
                    });
                    $face.html(html);
                });
            }
        });

    }

    // 	获取人员信息
    function getPersonData(personId){
        $.ajax({
            url:"/persCardPrintTemplate.do?getPersonData",
            type: "post",
            dataType: "json",
            async : false,
            data:{"personId":personId},
            success: function(data)
            {
                if(data.data != "")
                {
                    printData = data.data;
                }
                //获取选中模板数据
                xmlDataTest(ZKUI.Combo.get('persCardPrintTemplate').combo.getSelectedValue());
            }
        });
    }

    frontAttrGrid.attachEvent("onRowSelect", function(rId,cInd){
        getPersonData(rId);
    });

    frontAttrGrid.attachEvent("onCheck", function(rid, cind, state) {
        if (state) {
            checkState();
        }
        else {
            setCheckState(false);
        }
    });

    function checkState()
    {
        var all = true
        for (var i = 0; i < frontAttrGrid.rowsCol.length; i++) {
            if (!frontAttrGrid.cells(frontAttrGrid.rowsCol[i].idd, 0).isChecked()) {
                    all = false;
                    break;
            }
        }
        setCheckState(frontAttrGrid.rowsCol.length && all);
    }

    function setCheckState(state)
    {
        if (state) {
            $("#persCardPrint_frontAttrGridbox .hdr img[checkhead='true']").attr("checked", true);
            $("#persCardPrint_frontAttrGridbox .hdr img[checkhead='true']").attr("src", sysCfg.dhxImgPath + "/dhxgrid_web/item_chk1.gif")
        } else {
            $("#persCardPrint_frontAttrGridbox .hdr img[checkhead='true']").attr("checked", false);
            $("#persCardPrint_frontAttrGridbox .hdr img[checkhead='true']").attr("src", sysCfg.dhxImgPath + "/dhxgrid_web/item_chk0.gif")
        }
    }

    $("#persCardPrint_closeButton").click(function(){
        window.clearInterval(t);
        DhxCommon.closeWindow();
    });

    //	生成base64图片进行打印
    function cardPrinting(index, printSize, checkedIds, checkedPins)
    {
        var devFlag = false;
        if(checkedIds.length > 0)
        {
            $("#cardPrintButtonId").attr("disabled",true);
            $("input[type='button']").attr("disabled",true);
            ZKUI.Combo.get("persCardPrintTemplate").combo.disable();
            ZKUI.Combo.get("persCardPrinter").combo.disable();
            printProcess(printSize, checkedIds[0], index);

            $.ajax({
                url:"/persCardPrintTemplate.do?getPicTemplate",
                type:"post",
                async: false,
                dataType: "json",
                data:{"templateId":ZKUI.Combo.get('persCardPrintTemplate').combo.getSelectedValue(), "personId":checkedIds[0]},
                success:function(data){
                    if(data.data != null){
                        $("#frontImgBase64").val(data.data.front);
                        $("#oppoImgBase64").val(data.data.opposite);
                        var printImgData = {'front':$("#frontImgBase64").val(),'jobName':checkedPins[0]};
                        if($("input[name='persPrintSurface']:checked").val() == "0")
                        {
                            printImgData.back = $("#oppoImgBase64").val();
                        }
                        //打印
                        ZKDevice.get(ZKDevType.CardPrinter).DoPrint({
                            ClientType:"8",
                            DeviceName:ZKUI.Combo.get('persCardPrinter').combo.getComboText(),
                            TimeOut:100,
                            data:JSON.stringify(printImgData),
                            callback: function (result) {
                                devFlag = true;
                                printProcess(printSize, checkedIds[0], index, result.ret);
                            }
                        });
                    }
                },
                error : function(XMLHttpRequest, textStatus, errorThrown)
                {
                }
            });
            t = window.setInterval(function(){
                if(devFlag)
                {
                    checkedIds.splice(0,1);
                    checkedPins.splice(0,1);
                    index++;
                    cardPrinting(index, printSize, checkedIds, checkedPins);
                    devFlag = false;
                }
            },1000);
        }

    }

    // 	制卡
    $("#cardPrintButtonId").click(function(){
        var checkedIds = frontAttrGrid.getCheckedRows(0).split(',');
        var checkedPins = new Array();
        var index = 1;
        checkedIds.forEach(function(id){
            checkedPins.push(frontAttrGrid.cells(id,1).getValue());
        });
        cardPrinting(index, checkedIds.length, checkedIds, checkedPins);
        // 保存选中制卡模板
        $.cookie('persCardPrintTemplate', ZKUI.Combo.get('persCardPrintTemplate').combo.getSelectedValue());
        // 保存选中制卡打印机
        $.cookie('persCardPrinter', ZKUI.Combo.get('persCardPrinter').combo.getSelectedValue());
        // 保存打印选项
        $.cookie('persPrintSurface', $("input[name='persPrintSurface']:checked").val());
    });

    //打印进度
    function printProcess(printSize, obj, index, ret){
        if(ret == null || ret == undefined)
        {
            $("#"+obj).html(I18n.getValue("pers_cardTemplate_printing"));
            $("#"+obj+"Oppo").html(I18n.getValue("pers_cardTemplate_printing"));
            $("#"+obj).css({color:"red"});
            $("#"+obj+"Oppo").css({color:"red"});
            $("#cardPrintLoadingImg").show();
            $("#cardPrintLoadingImgOppo").show();
            setProgress("cardPrintTotalProgressId",index/printSize * 100 -5);
        }
        else
        {
            if(printSize == index)
            {
                $("#cardPrintLoadingImg").hide();
                $("#cardPrintLoadingImgOppo").hide();
                setProgress("cardPrintTotalProgressId",100);
                $("#cardPrintButtonId").attr("disabled",false);
                $("input[type='button']").attr("disabled",false);
                ZKUI.Combo.get("persCardPrintTemplate").combo.enable();
                ZKUI.Combo.get("persCardPrinter").combo.enable();
            }
            if(ret == 0)
            {
                $("#"+obj).html(I18n.getValue("common_succeed"));
                $("#"+obj+"Oppo").html(I18n.getValue("common_succeed"));
                $("#"+obj).css({color:"#7ac142"});
                $("#"+obj+"Oppo").css({color:"#7ac142"});
            }
            else
            {
                $("#"+obj).html(I18n.getValue("common_failed"));
                $("#"+obj+"Oppo").html(I18n.getValue("common_failed"));
            }
        }

        if(index != printSize)
        {
            return false;
        }

    }

    //设置进度
    function setProgress(node_id, progress)
    {
        if (node_id)
        {
            $("#" + node_id + " > span").css("width", String(progress) + "%");
            $("#cardPrintPercentNum").html(parseInt(progress) + "%");
            $("#cardPrintPercentNumOppo").html(parseInt(progress) + "%");
            //计算进度条数值位置
            $("#cardPrintPercentNum").css("left", $("#cardPrintTotalProgressId").position().left + $("#cardPrintTotalProgressId").width() / 2 - $("#cardPrintPercentNum").width() / 2);
        }
    }

});