<#assign editPage="true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action="persAttribute.do?save" method="post" id="${formId}" enctype="multipart/form-data">
	<input type="hidden" name="id" value="${(item.id)!}"/>
	<input id="attrName" name="attrName" type="hidden" value="${(item.attrName)!}" />
	<input type="hidden" name="filedIndex" value="${(item.filedIndex)!defFiledIndex}"/>
	<table class="tableStyle">
		<tr>
			<th><label><@i18n 'pers_attribute_attrName'/></label><span class="required">*</span></th>
			<td><input id="showName" name="showName" type="text" value="${(item.attrName)!}" <#if (item??&&item.sqlStr??&&(item.sqlStr?string == "init" || item.sqlStr?string == "initHep"))>disabled="disabled"</#if>/></td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_attribute_controlType'/></label><span class="required">*</span></th>
			<td>
				<@ZKUI.Combo empty="false" id="controlType${uuid}" name="controlType" width="148" hideLabel="true" value="${(item.controlType)!}">
		    		<option value="select"><@i18n 'pers_attrDefini_select'/></option>
					<option value="checkbox"><@i18n 'pers_attrDefini_check'/></option>
					<option value="radio"><@i18n 'pers_attrDefini_radio'/></option>
					<option value="text"><@i18n 'pers_attrDefini_text'/></option>
		    	</@ZKUI.Combo>
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_attribute_attrValue'/></label>
			<span id="valueRequired" class="required" style="display: hidden">*</span>
			</br><span style="word-break: break-all; width: 100px" class="warningColor">
			<@i18n 'pers_attrDefini_attrValue_split'/></span></th>
			<td><textarea name="attrValue" style="width: 148px;" rows="7" <#if (item??&&item.sqlStr??&&(item.sqlStr?string == "initHep"))>disabled="disabled"</#if>>${(item.attrValue)!}</textarea></td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_attribute_positionX'/></label><span class="required">*</span></th>
			<td><input id="positionX" name="positionX" type="text" value="${(item.positionX)!defaultX}"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_attribute_positionY'/></label><span class="required">*</span></th>
			<td><input id="positionY" name="positionY" maxlength="1" type="text" value="${(item.positionY)!defaultY}"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_attribute_showTable'/></label></th>
			<td><label>
				<#if (item??&&item.showTable?string == "true")>
				<@ZKUI.Input hideLabel="true" type="radio" name="showTable" value="true" checked="checked"/>
				<#else>
				<@ZKUI.Input hideLabel="true" type="radio" name="showTable" value="true"/>
				</#if>
				<@i18n 'common_yes'/>
				</label>
				<label>
				<#if (!item?? || item??&&item.showTable?string == "false")>
				<@ZKUI.Input hideLabel="true" type="radio" name="showTable" value="false" checked="checked"/>
				<#else>
				<@ZKUI.Input hideLabel="true" type="radio" name="showTable" value="false"/>
				</#if>
				<@i18n 'common_no'/></label>
			</td>
		</tr>
	</table>
</form>

<script type="text/javascript">
(function(){

    <#if (item??&&item.sqlStr??&&(item.sqlStr?string == "initHep"))>
    ZKUI.Combo.get("controlType${uuid}").combo.disable(true);
    </#if>

	$("#${formId} textarea[name='attrValue'],#${formId} #positionX,#${formId} #positionY").blur(function(){
		if($(this).val() !=""){
			$(this).valid();
		}
	});
	var $attrValue = $("#${formId} textarea[name='attrValue']");
	$attrValue.keyup(function(){
		this.value = this.value.replace(/\n/g, "");//不允许按回车键换行
	});
	var controlType = "${(item.controlType)!}",$controlTypeCombo = ZKUI.Combo.get("controlType${uuid}").combo;
	
	jQuery.validator.addMethod("isExistRowColValid", function(value, element){
		var isExist1 = true;
  		$.ajax({
  			url:"persAttribute.do?isRowColExist",
			type:"post",
			dataType:"json",
			async :false,
			data:{
				"positionX":function(){return  $("#${formId} #positionX").val(); },
				"positionY":function(){return  $("#${formId} #positionY").val();},
				"id":"${(item.id)!}"
			},
            success: function (data) 
            {
               	isExist1 = data;
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) 
            {
            	messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
            }
		});
		return isExist1;
	},function(){
		return  "<@i18n 'pers_attrDefini_msg2'/>";
	});
	
	jQuery.validator.addMethod("positionYNum", function(value, element){
		var pattenNum = /^[1-2]$/;
		return this.optional(element) || (pattenNum.test(value));  
	},function(){
		return "<@i18n 'pers_attrDefini_maxCol'/>"; 
	});
	
	//验证属性值分隔符“;”左右要有值
	jQuery.validator.addMethod("attrValueDelimiterValid",function(value,element){
		if($("#${formId} input[name='controlType']").val() == 'text'){
			return true;
		}
		
		var pattenChar = /^(?!;)(?!.*?;$).*$/;
		return this.optional(element) || (pattenChar.test(value));  
	},function(){
		return "<@i18n 'pers_person_attrValueDelimiterValid'/>"; 
	});
	//验证属性值是否重复
	jQuery.validator.addMethod("attrValueValid",function(value,element){
		if($controlTypeCombo.getSelectedValue() == 'text'){
			return true;
		}
		if(value==""){
			return true;
		}
		var attrValueArrayList=value.split(";");
		var tempArrayList=new Array();
		for(var i=0;i<attrValueArrayList.length;i++)
		{	
			//判断是否有重复的
			if((';'+tempArrayList+';').indexOf(';'+attrValueArrayList[i]+';')>-1)
			{
				return false;
			}
			else
			{
				tempArrayList.push(';'+attrValueArrayList[i]+';');
			}
		}
		return true;
	},function(){
		return "<@i18n 'pers_person_attrValueValid'/>"; 
	});

    jQuery.validator.addMethod("attrValueValidChar", function(value, element) {
        var unchar = [ '&', '<', '>', '`', '~', '!', '@', '#', '$', '%', '^', '*', '?', '/', '|', '\\', ':', '=', '"', '\'', ',' ];
        for (var i = 0; i < unchar.length; i++) {
            if (value.indexOf(unchar[i]) >= 0) {
                return false;
            }
        }
        return true;
    }, function() {
        return I18n.getValue("common_prompt_unIncludeChar");/*"不能包含特殊符号"*/
    });
	
	$("#${formId}").validate( {
		debug : true,
		rules :
		{
			"showName" :
			{
				required : true,
				unInputChar : true,
				maxlength:100,
				overRemote : ["persAttribute.do?isExist&id=${(item.id)!}", "${(item.attrName)!}"]
			},
			"controlType" :
			{
				required : true
			},
			"attrValue" :
			{
				required:function(){
					return $controlTypeCombo.getSelectedValue() != 'text';
				},
				attrValueDelimiterValid:true,
				attrValueValid:true,
                attrValueValidChar:true
			},
			"positionX" :
			{
				required:true,
				digits : true,
				range : [3,20],
				isExistRowColValid:true
			},
			"positionY" :
			{
				required:true,
				digits : true,
				range : [1,2],
				isExistRowColValid:true,
				positionYNum:true
			}
		},
		messages: {
			 "attrName":{
				 maxlength: "<@i18n 'pers_attrDefini_msg1'/>"
			 }
		},
		submitHandler : function()
		{
			ZKUI.Combo.get("controlType${uuid}").combo.disable(false);
			$("textarea[name='attrValue']").attr("disabled", false);
			<@submitHandler/>
		}
	});
	
	function changeControlType(){
		if($controlTypeCombo.getSelectedValue()=='text'){
			$('#${formId} #valueRequired').hide();
			$attrValue.valid();
			$attrValue.attr("readonly","readonly");
			$attrValue.attr("oldValue",$attrValue.val());
			$attrValue.val("");
		}else{
			$('#${formId} #valueRequired').show();
			$attrValue.removeAttr("readonly");
			if(undefined != $attrValue.attr("oldValue"))
			{
				$attrValue.val($attrValue.attr("oldValue"));
			}
		}
	}
	changeControlType();
	
	$controlTypeCombo.attachEvent("onChange", function(val){
		if(controlType != "" && controlType != val)
		{
			messageBox({messageType:"confirm", text: "<@i18n 'pers_attrDefini_modControlType'/>", 
				callback: function(result){
					if(!result)
					{
						$controlTypeCombo.setComboValue(controlType);
					}
					changeControlType();
				}
			});
		}
		else
		{
			changeControlType();
		}
	});
	
	$("#${formId} #showName").change(function(){
		$("#${formId} #attrName").val($(this).val());
	});
	
	if("true" == '${maxLimit?string}')
	{
		messageBox({messageType:"alert",text:"<@i18n 'pers_attrDefini_maxLimit'/>",callback:function(){
			DhxCommon.closeWindow();
		}});
	}
})();
</script>
</#macro>