<#assign gridName="persAttributeGrid${uuid!}">
<script type="text/javascript">
	function countAttrList(id, bar, opt, tid){
	$.ajax({
  			url:"persAttribute.do?countAttrList",
			type:"post",
			dataType:"json",
			async :false,
            success: function (data)
            {
               	if(data)
               	{
               		messageBox({messageType:"alert",text:"<@i18n 'pers_attrDefini_maxLimit'/>"});
               		return;
               	}
               	else
               	{
               		commonAdd(id, bar, opt, tid);
					return;
               	}
            }
		});
	}
</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="attrName"  maxlength="100" title="pers_attribute_attrName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="attrValue"  maxlength="2000" title="pers_attribute_attrValue" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="pers:attribute:refresh"/>
    	<@ZKUI.ToolItem id="persAttribute.do?edit" text="common_op_new" height="400" width="450" img="comm_add.png" action="countAttrList" permission="pers:attribute:add"/>
    	<@ZKUI.ToolItem id="persAttribute.do?del&attrName=(showName)" text="common_op_del" img="comm_del.png" action="commonDel" permission="pers:attribute:del"/>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem" query="persAttribute.do?list"/>
</@ZKUI.GridBox>