<#assign  gridName="persTempPersonGrid${uuid!}">
<script>
    function viewPersTempPerson(gridName, domObj, rid) {
        if (rid != "") {
            DhxCommon.createWindow("persTempPerson.do?view&id=" + rid + "^0^0^520^355^<@i18n 'pers_tempPerson_view'/>");
        }
    }
    function auditView(gridName, domObj, rid) {
        if (rid != "") {
            DhxCommon.createWindow("persTempPerson.do?auditView&id=" + rid + "^0^0^520^355^<@i18n 'pers_tempPerson_audit'/>^"+gridName);
        }
    }
    function convertAuditStatus(v) {
        if (v == "1") {
            v = "<span class='zk-msg-normal'><@i18n 'auth_company_auditpass'/></span>";
		}
        if (v == "0") {
            v = "<span class='zk-msg-error'><@i18n 'auth_company_pendingAudit'/></span>";
		}
		return v;
	}
</script>

<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
        <@ZKUI.SearchTop>
            <tr>
                <td valign="middle">
                    <@ZKUI.Input name="pin"  maxlength="30" title="pers_person_pin" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
                </td>
                <td valign="middle">
                    <@ZKUI.Input name="mobilePhone"  maxlength="30" title="pers_person_mobilePhone" type="text"/>
                </td>
            </tr>
        </@ZKUI.SearchTop>
        <@ZKUI.SearchBelow>
            <td valign="middle">
                <@ZKUI.Combo empty="true" name="status" title="common_status">
                    <option value="0"><@i18n 'auth_company_pendingAudit'/></option>
                    <option value="1"><@i18n 'auth_company_auditpass'/></option>
                </@ZKUI.Combo>
            </td>
        </@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
        <@ZKUI.ToolItem type="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="pers:tempPerson:refresh" ></@ZKUI.ToolItem>
        <@ZKUI.ToolItem type="delete" id="persTempPerson.do?del&pins=(pin)" permission="pers:tempPerson:del"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.pers.vo.PersTempPersonItem" query="persTempPerson.do?list"/>
</@ZKUI.GridBox>