<!DOCTYPE html>
<!-- saved from url=(0040)http://**************:18098/app/v1/visReg -->
<html xmlns="http://www.w3.org/1999/html" style="height: 100%;">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><@i18n 'pers_param_qrCodeUrlCreate'/></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">


    <script>
        var str=location.href; //取得整个地址栏
        var linkStr = "?";
        if(str.indexOf("?")!=-1){
            linkStr = "&";
        }
        var num=str.indexOf("lang=");
        if(num==-1){
            var lang = navigator.language||navigator.userLanguage||'${language!}';//常规浏览器语言和IE浏览器
            lang = lang.replace("-","_");
            lang = lang.toLowerCase();
            var langSearch={
                "zh_cn":"zh_CN",
                "th":"th",
                "en":"en_US",
                "es":"es",
                "id":"in_ID",
                "vi":"vi",
                "zh_tw":"zh_TW",
                "ar":"ar",
                "none":"none"
            };
            for(var k in langSearch){
                if(lang.indexOf(k)!=-1){
                    location.href=location.href+linkStr+'lang='+langSearch[k];
                    break;
                }
                if(langSearch[k]=='none'){
                    location.href=location.href+linkStr+'lang=${language!}';
                }
            }
        }

        function clickRegistrar() {
            $("#privacyForm").submit();
        }
    </script>

    <link type="text/css" rel="stylesheet" href="/css/jquery-weui.min.css">
    <link type="text/css" rel="stylesheet" href="/css/weui.min.css">
    <link rel="stylesheet" href="/css/baseRegisterApp.css">
    <script src="/js/jquery.js"></script>
    <style>
        #web_bg{
            position:fixed;
            top: 0;
            left: 0;
            width:100%;
            height:100%;
            min-width: 1000px;
            z-index:-10;
            zoom: 1;
            background-color: #fff;
            background-repeat: no-repeat;
            background-size: cover;
            -webkit-background-size: cover;
            -o-background-size: cover;
            background-position: center 0;
        }
        .weui-msg__icon-area{
            margin-top: 15%;
        }
        .weui-msg__title{
        ;
        }


        .mui-content {
            background-color:#f8f8f8;
            min-height:-webkit-calc(100% - 120px);
            min-height:-moz-calc(100% - 120px);
            min-height: calc(100% - 120px);
        }
    </style>

<body style="height: 100%;">
        <header class="mui-bar mui-bar-nav">
            <h1 class="mui-title"><@i18n 'auth_license_cloud_systemPolicy'/></h1>
        </header>
        <div class="mui-content weui-msg__opr-area">
            <form action="tokenAdreg" method="post" id="privacyForm" enctype="multipart/form-data" autocomplete="off">
                <p class="weui-btn-area">
                    ${privacy!}
                </p>
                <input type="hidden" name="privacy" value="1" />
            </form>
        </div>
        <div>
            <a style="background-color: #7ac143;margin: 0 20px" id="registrarButton" onclick="clickRegistrar()" class="weui-btn weui-btn_primary"><@i18n 'pers_h5_confirmAndContinue'/></a>
        </div>
</body>