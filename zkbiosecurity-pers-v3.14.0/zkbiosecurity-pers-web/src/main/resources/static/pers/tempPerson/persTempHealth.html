<!DOCTYPE html>
<!-- saved from url=(0040)http://192.168.214.145:8088/app/v1/adreg -->
<div>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title><@i18n 'pers_param_qrCodeUrlCreate'/></title>
		<meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<meta name="apple-mobile-web-app-status-bar-style" content="black">
		<link type="text/css" rel="stylesheet" href="/css/weui.min.css">
		<link rel="stylesheet" href="/css/baseRegisterApp.css">
		<link rel="stylesheet" href="/css/jquery-weui.min.css">
        <script type="text/javascript" src="/js/jquery.js"></script>
        <script type="text/javascript" src="/js/persTempRegExif.js"></script>
        <script type="text/javascript" src="/js/jquery-weui.min.js"></script>
		<style>
			.overlay {  
			    position: absolute;  
			    top: 0px;  
			    left: 0px;  
			    z-index: 10001;  
			    display:none;  
			    filter:alpha(opacity=60);  
			    background-color: #777;
			    opacity: 0.5;  
			    -moz-opacity: 0.5;  
			}
			.weui-cells__title{
				color: black;
			}
		</style>
        <#if language =="zh_CN">
		<script>
            var str=location.href; //取得整个地址栏
            var num=str.indexOf("lang=");
            if(num==-1){
                var lang = navigator.language||navigator.userLanguage||'${language!}';//常规浏览器语言和IE浏览器
                lang = lang.replace("-","_");
                lang = lang.toLowerCase();
                var langSearch={
                    "zh_cn":"zh_CN",
                    "th":"th",
                    "en":"en_US",
                    "es":"es",
                    "id":"in_ID",
                    "vi":"vi",
                    "zh_tw":"zh_TW",
                    "ar":"ar",
                    "none":"none"
                };
                for(var k in langSearch){
                    if(lang.indexOf(k)!=-1){
                        location.href=location.href+'?lang='+langSearch[k];
                        break;
                    }
                    if(langSearch[k]=='none'){
                        location.href=location.href+'?lang=${language!}';
                    }
                }
            }
		</script>
    </#if>
	</head>
	<body>
	 <div class="overlay"></div>
		<header class="mui-bar mui-bar-nav">
			<h1 class="mui-title"><@i18n 'pers_health_declaration'/></h1>
		</header>
		<div id="id_regmain" class="mui-content" style="background-color:#f8f8f8">
	 		<form action="/tokenAdreg" method="post" id="healthForm" enctype="multipart/form-data" autocomplete="off">
                <input type="hidden" name="privacy" value="1" />
                <!-- 是否接触病例 -->
                <div>
                    <div class="weui-cells__title"><@i18n 'pers_health_attrExposure' /><span style="color: red">*</span></div>
                    <div class="weui-cells weui-cells_radio">
                        <label class="weui-cell weui-cell_active weui-check__label" for="exposureYes">
                            <div class="weui-cell__bd">
                                <p><@i18n 'common_yes' /></p>
                            </div>
                            <div class="weui-cell__ft">
                                <input type="radio" class="weui-check" value="1" name="exposure" id="exposureYes"/>
                                <span class="weui-icon-checked"></span>
                            </div>
                        </label>
                        <label class="weui-cell weui-cell_active weui-check__label" for="exposureNo">
                            <div class="weui-cell__bd">
                                <p><@i18n 'common_no' /></p>
                            </div>
                            <div class="weui-cell__ft">
                                <input type="radio" class="weui-check"  value="0" name="exposure" id="exposureNo" checked="checked"/>
                                <span class="weui-icon-checked"></span>
                            </div>
                        </label>
                    </div>
                </div>
                <!-- 症状 -->
                <div>
                    <div class="weui-cells__title"><@i18n 'pers_health_attrSymptom' /><span style="color: red">*</span></div>
                    <div class="weui-cells weui-cells_checkbox">
                        <label class="weui-cell weui-check__label" for="symptomNone">
                            <div class="weui-cell__hd">
                                <input type="checkbox" class="weui-check" name="symptom" id="symptomNone" checked="checked" value="0">
                                <i class="weui-icon-checked"></i>
                            </div>
                            <div class="weui-cell__bd">
                                <p><@i18n 'common_none' /></p>
                            </div>
                        </label>
                        <label class="weui-cell weui-check__label" for="symptomCough">
                            <div class="weui-cell__hd">
                                <input type="checkbox" class="weui-check" name="symptom" id="symptomCough" value="1">
                                <i class="weui-icon-checked"></i>
                            </div>
                            <div class="weui-cell__bd">
                                <p><@i18n 'pers_health_symptomCough' /></p>
                            </div>
                        </label>
                        <label class="weui-cell weui-check__label" for="symptomFever">
                            <div class="weui-cell__hd">
                                <input type="checkbox" class="weui-check" name="symptom"  id="symptomFever" value="2">
                                <i class="weui-icon-checked"></i>
                            </div>
                            <div class="weui-cell__bd">
                                <p><@i18n 'pers_health_symptomFever' /></p>
                            </div>
                        </label>
                        <label class="weui-cell weui-check__label" for="symptomPolypena">
                            <div class="weui-cell__hd">
                                <input type="checkbox" class="weui-check" name="symptom" id="symptomPolypena" value="3">
                                <i class="weui-icon-checked"></i>
                            </div>
                            <div class="weui-cell__bd">
                                <p><@i18n 'pers_health_symptomPolypena' /></p>
                            </div>
                        </label>
                    </div>
                </div>
                <!-- 访问过的城市 -->
                <div>
                    <div class="weui-cells__title"><@i18n 'pers_health_attrVisitCity' /><span style="color: red">*</span></div>
                    <div class="weui-cells">
                        <div class="weui-cell">
                            <div class="weui-cell__bd">
                                <input id="visitCity" name="visitCity"  class="weui-input" type="text" maxlength="50"/>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 备注 -->
                <div class="weui-cells__title"><@i18n 'pers_health_attrRemarks'/></div>
                <div class="weui-cells">
                    <div class="weui-cell">
                        <div class="weui-cell__bd">
                            <input id="healthRemarks" name="healthRemarks" class="weui-input" maxlength="200" type="text">
                        </div>
                    </div>
                </div>
                <!-- 协议 -->
                <div class="weui-cells__tips">
                    <input id="agree" type="checkbox" style="vertical-align: middle;"/><@i18n 'pers_health_aggrement'/>
                </div>
	 		</form>
			<div style="margin-top:30px;">
				<a style="background-color: #7ac143;margin: 0 20px" id="registrarButton" onclick="clickRegistrar()" class="weui-btn weui-btn_primary"><@i18n 'common_nextStep'/></a>
			</div>								
		</div>
		<div class="loadding" id="loadingTip">
			<img src="/public/controls/dhtmlx/dhtmlxLayout/codebase/imgs/dhxlayout_dhx_skyblue/dhxlayout_progress.gif" width="40">
		</div>
	<script>
	function clickRegistrar() {
        showLoading();
        $("#registrarButton").attr("disabled", true);
		var exposure = $("input[name='exposure']:checked").val();
		var symptom = $("input[name='symptom']:checked").val();
		var visitCity = $("#visitCity").val();
		var agree = $("#agree").attr("checked");
		if (exposure && symptom && visitCity && agree == "checked") {
            $("#healthForm").submit();
		}
		else
		{
		    if(!visitCity)
            {
                $.modal({text: "<@i18n 'pers_health_visitCity_notEmpty'/>",title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
            }else if(agree != "checked"){
                $.modal({text: "<@i18n 'pers_health_notAgree'/>",title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
			}
		}
        $("#registrarButton").removeAttr("disabled");
        hideLoading();
	}

	/** 
	 * 显示遮罩层 
	 */  
	function showOverlay() {  
	    // 遮罩层宽高分别为页面内容的宽高  
	    $('.overlay').css({'height':$(document).height(),'width':$(document).width()});  
	    $('.overlay').show();  
	}  
	/** 
	 * 显示Loading提示 
	 */  
	function showLoading() {  
	    // 先显示遮罩层  
	    showOverlay();  
	    // Loading提示窗口居中  
	    $("#loadingTip").css('top',  
	            (getWindowInnerHeight() - $("#loadingTip").height()) / 2 + 'px');  
	    $("#loadingTip").css('left',  
	            (getWindowInnerWidth() - $("#loadingTip").width()) / 2 + 'px');  
	              
	    $("#loadingTip").show();  
	    $(document).scroll(function() {  
	        return false;  
	    });  
	}  
	/** 
	 * 隐藏Loading提示 
	 */  
	function hideLoading() {  
	    $('.overlay').hide();  
	    $("#loadingTip").hide();  
	    $(document).scroll(function() {  
	        return true;  
	    });  
	} 
	// 浏览器兼容 取得浏览器可视区高度  
	function getWindowInnerHeight() {  
    	var winHeight = window.innerHeight  
            || (document.documentElement && document.documentElement.clientHeight)  
            || (document.body && document.body.clientHeight);  
    	return winHeight;  
	}  
  
	// 浏览器兼容 取得浏览器可视区宽度  
	function getWindowInnerWidth() {  
   		 var winWidth = window.innerWidth  
            || (document.documentElement && document.documentElement.clientWidth)  
            || (document.body && document.body.clientWidth);  
   		 return winWidth;  
	}

	function validateValue(value) {
		var unchar = [ '&', '<', '>', '`', '~', '!', '@', '#', '$', '%', '^', '*', '?', '/', '|', '\\', ':', ';', '=', '"', '\'', ',' ];
		for (var i = 0; i < unchar.length; i++) {
			if (value.indexOf(unchar[i]) >= 0) {
				return false;
			}
		}
		return true;
	}

	$(function () {
		var style = "<style>\n" +
            "\tdiv{\n" +
            "\t\tdirection: rtl!important;\n" +
            "\t}\n" +
            "</style>";
        var sysLanguage = getQueryVariable("lang");
        if(sysLanguage==="ar"){
            $("head").append(style);
		}
    });

    function getQueryVariable(variable)
    {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i=0;i<vars.length;i++) {
            var pair = vars[i].split("=");
            if(pair[0] == variable){return pair[1];}
        }
        return(false);
    }

    $(function(){
        $("input[name='symptom']").change(function(){
            if ($(this).attr("checked") == "checked") {
                //第一个选项none, 不能和其他选项共同选中
                if($(this).attr("id") != "symptomNone"){
                    $("input[name='symptom']").eq(0).removeAttr("checked");
                }else{
                    $("input[name='symptom']").removeAttr("checked");
                    $("input[name='symptom']").eq(0).attr("checked","checked");
                }
            }else{
                //不选择任何选项, 则第一个选项none被自动选中
                var checkFlag = false;
                $("input[name='symptom']").each(function () {
                    if ($(this).attr("checked") == "checked") {
                        checkFlag = true;
                        return;
                    }
                });
                if(!checkFlag){
                    $("input[name='symptom']").eq(0).attr("checked","checked");
                }
            }
        })
	})

	//不允许输入特殊字符
    $($("#healthRemarks,#visitCity")).on("keyup", function(){
        if(this.value != ""){
            var unchar = [ '&', '<', '>', '`', '~', '!', '@', '#', '$', '%', '^', '*', '?', '/', '|', '\\', ':', ';', '=', '"', '\'', ',' ];
            for (var i = 0; i < unchar.length; i++) {
                if (this.value.indexOf(unchar[i]) >= 0) {
                    this.value = this.value.replace(eval("/\\"+unchar[i]+"/g"), "");
                }
            }
        }
    })
	</script>
</body>
</html>