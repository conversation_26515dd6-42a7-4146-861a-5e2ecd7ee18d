<#assign formId = "${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action="persTempPerson.do?audit" method="post" id="${formId}" enctype="multipart/form-data">
    <input type="hidden" name="id" value="${(item.id)!}"/>
    <fieldset>
        <legend><@i18n "pers_person"/></legend>
        <table cellpadding="8" class="tableStyle" border="0" cellspacing="0">
            <tr>
                <td rowspan="8" style="text-align: center;">
                    <div>
                        <img src='${(item.photoPathBase64)!"/images/${attr('system.skin','')}/userImage.gif"}' onerror="this.src='/images/userImage.gif'" style="width: 120px;height: 140px;max-height: 160px"/>
                    </div>
                    <span><@i18n 'pers_cardTemplate_photo'/></span>
                    <br>
                </td>
                <th>
                    <label><@i18n 'pers_person_pin'/></label>
                </th>
                <td>
                    <input type="text" name="pin" readonly="readonly" style="vertical-align:middle;" value="${(item.pin)!}"/>
                </td>
            </tr>
            <tr>
                <th>
                    <label><@i18n 'pers_person_name'/></label>
                </th>
                <td>
                    <input type="text" name="name" readonly="readonly" style="vertical-align:middle;" value="${(item.name)!}"/>
                </td>
            </tr>
            <#if "${Application['system.language']}" != "zh_CN">
                <tr>
                    <th>
                        <label><@i18n 'pers_person_lastName'/></label>
                    </th>
                    <td>
                        <input type="text" name="lastName" readonly="readonly" style="vertical-align:middle;" value="${(item.lastName)!}"/>
                    </td>
                </tr>
            </#if>
            <tr>
                <th>
                    <label><@i18n 'pers_person_mobilePhone'/></label>
                </th>
                <td>
                    <input name="mobilePhone" type="text" readonly="readonly" maxlength="20" value="${(item.mobilePhone)!}"/>
                </td>
            </tr>
            <tr>
                <th>
                    <label><@i18n 'pers_card_cardNo'/></label>
                </th>
                <td>
                    <input name="cardNos" type="text" readonly="readonly" value="${(item.cardNos)!}"/>
                </td>
            </tr>
            <tr>
                <th><label><@i18n 'pers_dept_entity'/></label><span class="required">*</span></th>
                <td>
                    <@ZKUI.ComboGrid width="148" autoFirst="true" queryField="name" hideLabel="true"  name="deptId" value="${(item.deptId)!}"
                    grid_showColumns="ra,code,name"
                    grid_vo="com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentSelectItem"
                    grid_query="authDepartment.do?listSelect" />
                </td>
            </tr>
            <tr <#if systemModules?lower_case?index_of("att")==-1&&systemModules?lower_case?index_of("ins")==-1&&systemModules?lower_case?index_of("ai")==-1>hidden="hidden"</#if>>
            <th><label><@i18n 'base_area_entity'/></label></th>
            <td>
                <@ZKUI.ComboTree width="148" url="authArea.do?tree" readonly="true"  hideLabel="true" autoFirst="true" name="areaId" cascade="true"/>
            </td>
            </tr>
            <#if systemModules?lower_case?index_of("acc")!=-1>
                <tr>
                    <th><label><@i18n 'pers_person_authGroup'/></label></th>
                    <td>
                        <input type="text" id="levelName" name="levelName" readonly="readonly" class="gray"/>
                        <input type="hidden" id="accLevelIds" name="accLevelIds"/>
                    </td>
                </tr>
            </#if>
        </table>
    </fieldset>
</form>
<script type="text/javascript">
    $(function() {
        $("#${formId}").validate( {
            debug : true,
            rules : {
                "deptId": {
                    required : true
                }
            },
            submitHandler : function() {
            <@submitHandler />
            }
        });
        var width = 850;
		if ("${attr('system.levelTime.support','false')}" === "true") {
			width = 1158;
		}
        var opts = {
            path: "skip.do?page=acc_personLevelByPerson_personSelectLevelContent",
            width: width,
            height: 470,
            title:"<@i18n 'common_level_addPersonLevel'/>",
            onSure:"selectLevelHandler"
        };
        selectContent("#${formId} #levelName", opts);//给按钮注册选人事件
    });

    function selectLevelHandler(value,text,event){
        //  获取权限组id;支持临时权限组装开始/结束时间
        var accLevelIds = value;
		var validTimeResult = true;
		var validTip = "";
		if ("${attr('system.levelTime.support','false')}" === "true") {
			var resultMap = accPersonLevelByPersonValidTime(value);
			validTimeResult = resultMap.get("timeValidResult");
			validTip = resultMap.get("validTip");
			accLevelIds = resultMap.get("accLevelIds");
		}
		if (validTimeResult) {
		    $("#${formId} #accLevelIds").val(accLevelIds);
            $("#${formId} #levelName").val(text);
            return true;
		}  else {
			openMessage(msgType.warning, validTip);
			return false;
		}
    }

    function accPersonLevelByPersonValidTime(value) {
    	// 支持临时权限
		var accTimeLevelIdsArray = [];
		var dhGrid = ZKUI.Select.get("accSelectPersonLevelGrid").rightGrid.grid;
		var timeValidResult = true;
		var validTip = "";
		var resultMap = new Map();
		for (var i = 0; i < dhGrid.rowsCol.length; i++) {
			var id = dhGrid.rowsCol[i].idd;
			var startTimeStr = dhGrid.cellById(id, 3).getValue();
			var endTimeStr = dhGrid.cellById(id, 4).getValue();
			var levelName = dhGrid.cellById(id, 1).getValue();
			if (endTimeStr != '' && startTimeStr != '' && endTimeStr <= startTimeStr) {
				timeValidResult = false;
				validTip = validTip + levelName + ":"+ "<@i18n 'common_dsTime_timeValid4'/>" + "</br>";
				dhGrid.cellById(id, 3).setValue("");
				dhGrid.cellById(id, 4).setValue("");
			} else if ((endTimeStr != '' && startTimeStr == '') || (endTimeStr == '' && startTimeStr != '')) {
				timeValidResult = false;
				validTip = validTip + levelName + ":"+ "<@i18n 'common_levelImport_timeNotNull'/>" + "</br>";
				dhGrid.cellById(id, 3).setValue("");
				dhGrid.cellById(id, 4).setValue("");
			} else {
				var accTimeLevelIds = {
                    "levelId": dhGrid.rowsCol[i].idd,
                    "startTime": startTimeStr,
                    "endTime": endTimeStr
                };
                accTimeLevelIdsArray.push(accTimeLevelIds);
			}
		}
		resultMap.set("timeValidResult",timeValidResult);
		resultMap.set("validTip", validTip);
		resultMap.set("accLevelIds",JSON.stringify(accTimeLevelIdsArray));
		return resultMap;
    }
</script>
</#macro>