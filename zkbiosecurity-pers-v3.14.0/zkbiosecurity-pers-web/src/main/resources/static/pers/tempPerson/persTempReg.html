<!DOCTYPE html>
<!-- saved from url=(0040)http://192.168.214.145:8088/app/v1/adreg -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

    <title><@i18n 'pers_param_qrCodeUrlCreate'/></title>
    <meta name="viewport" content="width=device-width, initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
	<link rel="stylesheet" href="/css/weui.min.css">
	<link rel="stylesheet" href="/css/baseRegisterApp.css">
    <link type="text/css" rel="stylesheet" href="/css/jquery-weui.min.css">
    <script type="text/javascript" src="/js/jquery.js"></script>
    <script src="/js/persTempRegExif.js"></script>
	<script type="text/javascript" src="/js/jquery-weui.min.js"></script>


	<style>
		.overlay {
			position: absolute;
			top: 0px;
			left: 0px;
			z-index: 10001;
			display:none;
			filter:alpha(opacity=60);
			background-color: #777;
			opacity: 0.5;
			-moz-opacity: 0.5;
		}
		.weui-cells__title{
			color: black;
		}
	</style>
    <#if language =="zh_CN" && enableHealthInfo!="1">
		<script>
            var str=location.href; //取得整个地址栏
            var num=str.indexOf("lang=");
            if(num==-1){
                var lang = navigator.language||navigator.userLanguage||'${language!}';//常规浏览器语言和IE浏览器
                lang = lang.replace("-","_");
                lang = lang.toLowerCase();
                var langSearch={
                    "zh_cn":"zh_CN",
					"th":"th",
                    "en":"en_US",
					"es":"es",
					"id":"in_ID",
					"vi":"vi",
					"zh_tw":"zh_TW",
					"ar":"ar",
					"none":"none"
                };
                for(var k in langSearch){
                    if(lang.indexOf(k)!=-1){
                        location.href=location.href+'?lang='+langSearch[k];
                        break;
					}
					if(langSearch[k]=='none'){
                        location.href=location.href+'?lang=${language!}';
					}
				}
            }
		</script>
		</#if>
	</head>
	<body>
	 <div class="overlay"></div>
		<header class="mui-bar mui-bar-nav">
			<h1 class="mui-title"><@i18n 'pers_param_qrCodeUrlCreate'/></h1>
		</header>
		<div id="id_regmain" class="mui-content" style="background-color:#f8f8f8">
			<input type="hidden" name="exposure" id="exposure" value="${exposure}" />
			<input type="hidden" name="symptom" id="symptom" value="${symptom}" />
			<input type="hidden" name="visitCity" id="visitCity" value="${visitCity}" />
			<input type="hidden" name="healthRemarks" id="healthRemarks" value="${healthRemarks}" />
			<div class="photo-item" style="position: relative;">
			    <input type="hidden" id="inputPhoto">
				<!--image/*;capture=camera-->
			    <input type="file" accept="image/jpeg;" style="display: none;" id="uploadImage" onchange="chooseImga(this);">
				<img id="takephoto" src="../../images/persTempRegCapture.png" onclick="uploadImage.click()" alt="Paris" style="margin-top:20px;border-radius:50%;border:2px solid;border-color:#e0e0e0;max-height: 140px;width:120px;height:120px;">
				<img id="rotatePhoto" src="../../images/ico_rotate.png" style="z-index:1;position: fixed;right: 10px;display: none;">
			</div>
			<div class="weui-cells__title"><@i18n 'pers_person_pin'/><span style="color: red">*</span></div>
			<div class="weui-cells">
				<div class="weui-cell">
	                <div class="weui-cell__bd">
						<input id="inputPin" name="pin" class="weui-input" autocomplete="off" placeholder="<@i18n 'pers_person_pin'/>" maxlength="${pinLen!}" type="<#if pinSupportLetter ="true">text<#else>tel</#if>" onkeyup="value=value.replace(/[^0-9<#if pinSupportLetter =="true">a-zA-Z</#if>>]/g,'')<#if pinSupportLetter !="true">.replace(/\b(0+)/gi,'')</#if>" onchange="value=value.replace(/[^0-9<#if pinSupportLetter =="true">a-zA-Z</#if>>]/g,'')<#if pinSupportLetter !="true">.replace(/\b(0+)/gi,'')</#if>">
	                </div>
	            </div>
			</div>
				<#if language !="zh_CN" >
				 <div class="weui-cells__title"><@i18n'pers_person_firstName'/><span style="color: red">*</span></div>
				 <div class="weui-cells">
					 <div class="weui-cell">
						 <div class="weui-cell__bd">
						 <input id="inputName" class="weui-input" autocomplete="off" placeholder="<@i18n'pers_person_firstName'/>" type="text" maxlength="25">
						 </div>
					 </div>
				 </div>
				<#else>
				 <div class="weui-cells__title"><@i18n'pers_person_name'/><span style="color: red">*</span></div>
				 <div class="weui-cells">
					 <div class="weui-cell">
						 <div class="weui-cell__bd">
						 <input id="inputName" class="weui-input" autocomplete="off" placeholder="<@i18n'pers_person_name'/>" type="text" maxlength="25">
						 </div>
					 </div>
				 </div>
				</#if>
				<#if language !="zh_CN" >
				<div class="weui-cells__title"><@i18n'pers_person_lastName'/></div>
				<div class="weui-cells">
					<div class="weui-cell">
						<div class="weui-cell__bd">
						<input id="inputLastName" class="weui-input" autocomplete="off" placeholder="<@i18n'pers_person_lastName'/>" type="text" maxlength="18">
						</div>
					</div>
				</div>
				</#if>
				<div class="weui-cells__title"><@i18n'pers_person_mobilePhone'/></div>
				<div class="weui-cells">
					<div class="weui-cell">
						<div class="weui-cell__bd">
						<input id="inputPhone" class="weui-input" autocomplete="off" placeholder="<@i18n'pers_person_mobilePhone'/>" type="text" maxlength="18">
						</div>
					</div>
				</div>
			<div style="margin-top:30px;">
				<a style="background-color: #7ac143;margin: 0 20px" id="registrarButton" onclick="clickRegistrar()" class="weui-btn weui-btn_primary"><@i18n 'pers_import_complete'/></a>
			</div>								
		</div>
		<div class="loadding" id="loadingTip">
			<img src="/public/controls/dhtmlx/dhtmlxLayout/codebase/imgs/dhxlayout_dhx_skyblue/dhxlayout_progress.gif" width="40">
		</div>
	

	<script>
	function chooseImga(fileObj) {
		$(fileObj).attr("disable","disable");
		var r = new FileReader();
		 var file = fileObj.files['0'];
		if(file) {
			r.readAsDataURL(file);
			r.onload = function(e) {
            var image = new Image();
            image.src = e.target.result;
            image.onload = function() {
            	var width = image.width;
				var height = image.height;
				var expectWidth = 480;//this.width;
				var expectHeight = expectWidth * height / width;
            	compress(this,expectWidth,expectHeight,1);
				$(fileObj).removeAttr("disable");
            }
		  };
		}
	}

    $($("#inputPhone")).on("keyup", function(ev){
        var keycode = ev.which;
        //alert(keycode);
        if(keycode == 37 || keycode == 39 || keycode == 8 || keycode == 46){

        }else{
            this.value = this.value.replace(/[^\d-]/g,""); //清除"数字"和"."以外的字符
			// if(this.value.length>=18){
			//     this.value = this.value.substr(0,18);
			// }
        }
    });

	//图片压缩
	function compress(img, width, height, ratio) {
		  var canvas = document.createElement('canvas');
	      canvas.width = width;
	      canvas.height = height;
	      var ctx = canvas.getContext("2d");
		  ctx.drawImage(img, 0, 0, width, height);

	      var img64 = canvas.toDataURL("image/jpeg", ratio);
		  $("#takephoto").attr("src", img64);
		  $("#inputPhoto").val(img64)

		  var rotate = 0;
		  $("#rotatePhoto").unbind("click").click(function(){
			rotate++;
			switch (rotate % 4) {
				case 1://90°
					canvas.width = height;
                  	canvas.height = width;
                  	ctx.rotate(Math.PI / 2);
                  	ctx.drawImage(img, 0, -height, width, height);
					break;
				case 2://180°
					canvas.width = width;
                  	canvas.height = height;
					ctx.rotate(Math.PI);
                  	ctx.drawImage(img, -width, -height, width, height);
					break;
				case 3://270°
					canvas.width = height;
                  	canvas.height = width;
                  	ctx.rotate(3 * Math.PI / 2);
                  	ctx.drawImage(img, -width, 0, width, height);
					break;
				default:
					canvas.width = width;
                  	canvas.height = height;
					ctx.drawImage(img, 0, 0, width, height);
					break;
			}

			var base64 = canvas.toDataURL("image/jpeg", ratio);
			$("#takephoto").attr("src", base64);
			$("#inputPhoto").val(base64);
		   }).show();
	}

	function clickRegistrar() {
		var pin = $("#inputPin").val();
		var name = $("#inputName").val();
		var phone = $("#inputPhone").val();
		var photo = $("#inputPhoto").val();
		var lastName =  $("#inputLastName").val();
        var exposure = $("#exposure").val();
        var symptom = $("#symptom").val();
        var visitCity = $("#visitCity").val();
        var healthRemarks = $("#healthRemarks").val();
		photo = photo.replace(/data:image\/\w*;base64,/,"");
		if (pin && name && photo) {
			var pp1 = validateValue(name);

			if(!pp1)
			{
                //姓名特殊字符 pers_person_noSpecialChar
                $.modal({text: "<@i18n 'pers_person_noSpecialChar'/>", title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
                return;
			}

            var pattenPin= /^[8,9]/;
			if(pin.length==9){
                if(pattenPin.test(pin)){
                    $.modal({text: "<@i18n 'pers_person_pinFirstValid'/>", title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
                    return;
                }
            }
            // 姓氏校验
			if(lastName && !validateValue(lastName)){
				$.modal({text: "<@i18n 'pers_person_noSpecialChar'/>",title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
				return;
			}
			
			$("#registrarButton").attr("disabled", true);
			$.ajax({
	            data: JSON.stringify({"pin": pin,"name": name,"photo": photo,"phone" : phone, "lastName":lastName,"exposure":exposure,"symptom":symptom,"visitCity":visitCity,"healthRemarks":healthRemarks}),
	            beforeSend:function(){
                    showLoading();
                },
	            contentType: "application/json",
	            dataType: "json",
	            type: "post",
	            url: "/tokenPersTempReg",
	            success: function(result) {
	            	if(typeof(result) == 'string')
	            	{
	            		result = JSON.parse(result);
	            	}
	            	if ("ok" == result.ret) {
                        $.toast("<@i18n 'common_op_succeed'/>");
	            		setTimeout(function() {
	            			window.location.href=window.location.href;
	            		},2000);
	            	}
	            	else
	            	{
                        $.modal({text: result.msg, title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
                    }
	            	$("#registrarButton").removeAttr("disabled");
	            	hideLoading();
	            },
				error: function(result) {
                if(typeof(result) == 'string')
                {
                    result = JSON.parse(result);
                }
                if ("ok" == result.ret) {
                    $.toast("<@i18n 'common_op_succeed'/>");
                    setTimeout(function() {
                        window.location.href=window.location.href;
                    },2000);
                }
                else
                {
                    $.modal({text: result.msg, title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
                }
                $("#registrarButton").removeAttr("disabled");
                hideLoading();
            }
	        });
		}
		else
		{
		    if(!pin)
		    {
                $.modal({text: "<@i18n 'pers_import_pinNotEmpty'/>", title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
            }
		    else if(!name)
		    {
                $.modal({text: "<@i18n 'pers_person_firstNameNotEmpty'/>", title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
		    }
		    else if(!photo)
		    {
                $.modal({text: "<@i18n 'pers_import_pleaseSelectPhoto'/>", title:"",buttons: [{ text: "<@i18n 'common_edit_ok'/>"}]});
		    }
            // else if(!phone){
             //     $.jBox.tip("<@i18n 'pers_person_mobilePhoneValidate'/>", 'error');
			// }
		    //手机号可选填
		}
	}
	/** 
	 * 显示遮罩层 
	 */  
	function showOverlay() {  
	    // 遮罩层宽高分别为页面内容的宽高  
	    $('.overlay').css({'height':$(document).height(),'width':$(document).width()});  
	    $('.overlay').show();  
	}  
	/** 
	 * 显示Loading提示 
	 */  
	function showLoading() {  
	    // 先显示遮罩层  
	    showOverlay();  
	    // Loading提示窗口居中  
	    $("#loadingTip").css('top',  
	            (getWindowInnerHeight() - $("#loadingTip").height()) / 2 + 'px');  
	    $("#loadingTip").css('left',  
	            (getWindowInnerWidth() - $("#loadingTip").width()) / 2 + 'px');  
	              
	    $("#loadingTip").show();  
	    $(document).scroll(function() {  
	        return false;  
	    });  
	}  
	/** 
	 * 隐藏Loading提示 
	 */  
	function hideLoading() {  
	    $('.overlay').hide();  
	    $("#loadingTip").hide();  
	    $(document).scroll(function() {  
	        return true;  
	    });  
	} 
	// 浏览器兼容 取得浏览器可视区高度  
	function getWindowInnerHeight() {  
    	var winHeight = window.innerHeight  
            || (document.documentElement && document.documentElement.clientHeight)  
            || (document.body && document.body.clientHeight);  
    	return winHeight;  
	}  
  
	// 浏览器兼容 取得浏览器可视区宽度  
	function getWindowInnerWidth() {  
   		 var winWidth = window.innerWidth  
            || (document.documentElement && document.documentElement.clientWidth)  
            || (document.body && document.body.clientWidth);  
   		 return winWidth;  
	}  
	function validateValue(value) {
		var unchar = [ '&', '<', '>', '`', '~', '!', '@', '#', '$', '%', '^', '*', '?', '/', '|', '\\', ':', ';', '=', '"', '\'', ',' ];
		for (var i = 0; i < unchar.length; i++) {
			if (value.indexOf(unchar[i]) >= 0) {
				return false;
			}
		}
		return true;
	}

	$(function () {
		var style = "<style>\n" +
            "\tdiv{\n" +
            "\t\tdirection: rtl!important;\n" +
            "\t}\n" +
            "</style>";
        var sysLanguage = getQueryVariable("lang");
        if(sysLanguage==="ar"){
            $("head").append(style);
		}
    });

    function getQueryVariable(variable)
    {
        var query = window.location.search.substring(1);
        var vars = query.split("&");
        for (var i=0;i<vars.length;i++) {
            var pair = vars[i].split("=");
            if(pair[0] == variable){return pair[1];}
        }
        return(false);
    }

	</script>
</body></html>