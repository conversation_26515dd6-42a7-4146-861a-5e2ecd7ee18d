<#assign formId = "${uuid!}">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action='persTempPerson.do?audit' method='post' id='${formId}' enctype='multipart/form-data'>
    <input type='hidden' name='id' value="${(item.id)!}"/>
    <fieldset>
        <legend><@i18n 'pers_person'/></legend>
        <table cellpadding="8" class="tableStyle" border="0" cellspacing="0">
            <tr>
                <td rowspan="8" style="text-align: center;">
                <div>
                    <img src='${(item.photoPathBase64)!"/images/${attr('system.skin','')}/userImage.gif"}' onerror="this.src='/images/userImage.gif'" style="width: 120px;height: 140px;max-height: 160px"/>
                </div>
                <span><@i18n 'pers_cardTemplate_photo'/></span>
                <br>
                </td>
            </tr>
            <tr>
                <!-- '人员编号' -->
                <th>
                    <label><@i18n 'pers_person_pin'/></label>
                </th>
                <td>
                    <input type="text" name="pin" readonly="readonly" style="vertical-align:middle;" value="${(item.pin)!}"/>
                </td>
            </tr>
            <tr>
                <!-- '姓名' -->
                <th>
                    <label><@i18n 'pers_person_name'/></label>
                </th>
                <td>
                    <input type="text" name="name" readonly="readonly" style="vertical-align:middle;" value="${(item.name)!}"/>
                </td>
            </tr>
            <#if "${Application['system.language']}" != "zh_CN">
            <tr>
                <th>
                    <label><@i18n 'pers_person_lastName'/></label>
                </th>
                <td>
                    <input type="text" name="lastName" readonly="readonly" style="vertical-align:middle;" value="${(item.lastName)!}"/>
                </td>
            </tr>
            </#if>
            <tr>
                <!-- '手机号'  -->
                <th>
                    <label><@i18n 'pers_person_mobilePhone'/></label>
                </th>
                <td>
                    <input name="mobilePhone" type="text" readonly="readonly" maxlength="20" value="${(item.mobilePhone)!}"/>
                </td>
            </tr>
            <tr>
                <th>
                    <label><@i18n 'pers_card_cardNo'/></label>
                </th>
                <td>
                    <input name="cardNos" type="text" readonly="readonly" value="${(item.cardNos)!}"/>
                </td>
            </tr>
        </table>
    </fieldset>
</form>
<script type="text/javascript">
    $(function() {
        $("#${formId}").validate({
            debug : true,
            submitHandler : function() {
            <@submitHandler />
            }
        });
    });
</script>
</#macro>