<#include '/public/template/editTemplate.html'>
<#assign closeButtonText = "common_close">
<#macro editContent>
<style type="text/css">
    .batchIssueCardGrid div.dhx_cell_hdr{
        border-radius:0px;
        background-color:#dcdfe2 !important;
    }
</style>

<div class="batchIssueCardGrid">
    <div>
        <table class="searchTable">
            <tr>
                <td valign="middle">
                    <!-- 部门名称 -->
                    <@ZKUI.Input id="dept${uuid!}" name="deptName" maxlength="30" title="pers_dept_deptName" type="text"/>
                </td>
                <td valign="middle">
                    <!-- 人员编号 -->
                    <@ZKUI.Input name="startPersonPin" id="startPersonPin${uuid!}" onkeyup="this.value=this.value.replace(/[^A-Za-z0-9]+/,'');" onafterpaste="this.value=this.value.replace(/[^A-Za-z0-9]+$/,'')" maxlength="${persParams['pers.pinLen']}" title="pers_batchIssCard_startPersNo" type="text"/>
                </td>
                <td valign="middle">
                    <!-- 人员编号 -->
                    <@ZKUI.Input name="endPersonPin" id="endPersonPin${uuid!}" onkeyup="this.value=this.value.replace(/[^A-Za-z0-9]+$/,'');" onafterpaste="this.value=this.value.replace(/[^A-Za-z0-9]+$/,'')" maxlength="${persParams['pers.pinLen']}" title="pers_batchIssCard_endPersNo" type="text"/>
                </td>
                <td style="padding-left: 0px">
                    <div id="queryNoCardPersonId" class="search_button_new" style="display: inline-block;" title="<@i18n 'common_search_query'/>">
                        <span class="icv-comm_search"></span>
                    </div>
                    <div id="clearQueryConditionId"  style="display: inline-block;margin-${leftRTL!'left'}: 20px;" title="<@i18n 'common_search_clearTitle'/>" class="search_clear_button_new">
                        <span class="icv-search-clear"></span>
                    </div>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 10px;">
                    <@ZKUI.Combo empty="false" id="issueWay${uuid}" name="issueWay" title="pers_batchIssCard_issueWay">
                        <option value="dispenserCard"><@i18n 'pers_batchIssCard_dispenCardIss'/></option>
                        <option value="readCard"><@i18n 'pers_batchIssCard_sendCard'/></option>
                    </@ZKUI.Combo>
                </td>
                <td style="padding-top: 10px;" class="multiCard">
                    <form  method="post" id="cardNoValidateForm" enctype="multipart/form-data">
                        <label class="search-label"><@i18n 'pers_batchIssCard_inputCardNum'/>
                        </label><input name="cardNo" type="text" class="search-input" value="" onkeyup="javascript:this.value=this.value.replace(/^0+/,'');"/>
                        <span class="readCard icv-base_readCard1" id="cardNo" style="display: none" onclick="readCard('cardNo')" onSure="persBatchIssueCardHander" title="<@i18n 'pers_batchIssCard_startReadCard'/>"></span>
                        <span class="stopReadCard icv-base_stopReadCard" onclick="stopReadCard(this)" title="<@i18n 'pers_person_stopRead'/>"></span>
                    </form>
                </td>
                <td style="padding-top: 10px;padding-${leftRTL!'left'}: 0px">
                    <a href="javascript:void(0)" id="batchAddCard"><@i18n 'pers_issueCard_entity'/></a>
                </td>
            </tr>
        </table>
    </div>
    <form action="persIssueCard.do?batch" method="post" id="${formId}" enctype="multipart/form-data">
        <div style="display: none;"><input type="hidden" id="batchCardListJson" name="cardListJson" value=""></div>
        <div style="display: none;"><input type="hidden" id="pinCardListJson" name="pinCardListJson" value=""></div>
        <div style="margin-top: 10px;">
        <@ZKUI.DGrid gridName="gridbox${uuid!}" style="height:360px;">
            <@ZKUI.LeftGrid title="pers_issueCard_noCardPerson">
                <@ZKUI.Grid setMode="true,true,false,true" onXLS="batchIssueCardGridInit" autoFirstOnly="true" vo="com.zkteco.zkbiosecurity.pers.vo.PersNoCardPersonItem" showColumns="!checkbox,cardNo" query="persIssueCard.do?noCardPerson&type=noSelected"/>
            </@ZKUI.LeftGrid>
            <@ZKUI.RightGrid title="pers_issueCard_waitPerson">
                <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.pers.vo.PersNoCardPersonItem" query="persIssueCard.do?noCardPerson&type=selected" originSort="false" nopaging="true" showColumns="!checkbox"/>
            </@ZKUI.RightGrid>
        </@ZKUI.DGrid>
        </div>
    </form>
</div>

</#macro>
<script type="text/javascript">
function batchIssueCardGridInit(dhxGrid){
    dhxGrid.setColWidth(3,"*");
}
;(function(){
     ZKUI.Combo.get("issueWay${uuid}").combo.attachEvent("onChange", function(val){
            $(".dispenserCard,.readCard,.stopReadCard").hide();
            $("."+val).show();
            if(undefined != persCurCardNoObj){
                stopReadCard(persCurCardNoObj);
            }
     })
    //移除保存继续
    $("#${formId}SaveContinue").remove();
    //默认禁用OK按钮，点击发卡会启用改按钮
    $("#${formId}OK").attr("disabled", "disabled");
    //点击查询
    $("#queryNoCardPersonId").click(function(){

        if($("#startPersonPin${uuid!}").val() == "" && $("#endPersonPin${uuid!}").val() != "")
		{
			openMessage(msgType.warning, "<@i18n 'pers_batchIssCard_startPinEmpty'/>");
			return;
		}
		if($("#startPersonPin${uuid!}").val() != "" && $("#endPersonPin${uuid!}").val() == "")
		{
			openMessage(msgType.warning, "<@i18n 'pers_batchIssCard_endPinEmpty'/>");
			return;
		}

        ZKUI.DGrid.get("gridbox${uuid!}").leftGrid.reload(function () {
            ZKUI.DGrid.get("gridbox${uuid!}").leftGrid.grid.selectRow(0);
        },{deptName: $("#dept${uuid!}").val()
            ,startPersonPin:$("#startPersonPin${uuid!}").val(),endPersonPin:$("#endPersonPin${uuid!}").val(),
            notInPersonId:ZKUI.DGrid.get("gridbox${uuid!}").rightGrid.grid.getAllRowIds()});
    });
    //清除条件
    $("#clearQueryConditionId").click(function(){
        $("#dept${uuid!}").val("");
        $("#startPersonPin${uuid!}").val("");
        $("#endPersonPin${uuid!}").val("");
        $("#queryNoCardPersonId").click();
    });
    //输入卡号结束回车添加
    $("#cardNoValidateForm input[name='cardNo']").keydown(function(event){
        if(event.keyCode == 13){
            $("#batchAddCard").click();
        }
    });
    var batchCardListJson = new Array(), cardsArr = new Array(), pinCardListJson = new Array();
    //批量添加卡
    $("#batchAddCard").click(function(){
        var $cardInpt = $("#cardNoValidateForm input[name='cardNo']");
        $cardInpt.focus();
        
        //验证表单
        if(!$("#cardNoValidateForm").valid()){
            return;
        }

        var cardNo = $cardInpt.val();
        if(cardNo == "")
        {
            openMessage(msgType.warning, "<@i18n 'pers_issueCard_cardEmptyNote'/>");
            return;
        }

        if(typeof cardsArr[cardNo] != "undefined"){
            openMessage(msgType.warning, "<@i18n 'pers_issueCard_cardHasBeenIssued'/>");
            return;
        }

        var leftGrid = ZKUI.DGrid.get("gridbox${uuid!}").leftGrid.grid,rightGrid=ZKUI.DGrid.get("gridbox${uuid!}").rightGrid.grid;
        var leftGidRowsNum = leftGrid.getRowsNum();
        if(leftGidRowsNum == 0){
            openMessage(msgType.warning, "<@i18n 'pers_batchIssCard_noPersonList'/>");
            return;
        }
        //启用OK按钮
        $("#${formId}OK").removeAttr("disabled");
        var rowId = leftGrid.getSelectedId(); //获取选中行ID
        if((typeof rowId) == "undefined" || rowId == ""){
            //当前页的第一行ID
            var page = leftGrid.getStateOfView()[0],pageSize = leftGrid.rowsBufferOutSize;
            rowId = leftGrid.getRowId((page-1) * pageSize);
        }
        //选中多条取选中的第一条
        if(rowId.split(",").length > 1){
            rowId = rowId.split(",")[0];
        }
        var rowDataJson = leftGrid.getRowData(rowId); // JSON
        var rowData = leftGrid.rowsAr[rowId]._attrs.data;
        rowData[leftGrid.getColIndexById("cardNo")] = cardNo;  // array  []
        rightGrid.addRow(rowId, rowData);
        cardsArr[cardNo] = rowDataJson.personPin;
        batchCardListJson.push({personId:rowDataJson.id, personPin:rowDataJson.personPin,cardNo:cardNo,cardType:0});
        pinCardListJson.push({personPin:rowDataJson.personPin,cardNo:cardNo});
        $cardInpt.val("");
        //左侧重新查询一次。
        $("#queryNoCardPersonId").click();
    });
    //卡验证表单用的。
    $("#cardNoValidateForm").validate( {
        debug : true,
        rules:{
            "cardNo":{
                cardNum : true,
                maxlength: 50,
                cardNoExistValid : true
            }
        },
        submitHandler : function()
        {
        },
        onkeyup:false
    });
    //提交表单
    $('#${formId}').validate( {
        debug : true,
        submitHandler : function()
        {
            if(batchCardListJson.length == 0){
                openMessage(msgType.warning, "<@i18n 'pers_issueCard_cardEmptyNote'/>");
                return;
            }
            $("#batchCardListJson").val(JSON.stringify(batchCardListJson));
            $("#pinCardListJson").val(JSON.stringify(pinCardListJson));
            <@submitHandler callBackFun="refreshPersCardPage()"/>
        }
    });

    DhxCommon.getCurrentWindow().attachEvent("onClose", function(){
        closeReaderTimeout();
        return true;
    });
})();

//批量发卡处理
function persBatchIssueCardHander(value, text ,event){
    $(persCurCardNoObj).prev().focus();
    hideReaderCard($(persCurCardNoObj).parent());
    pullBatchCardNo(value,"cardNo","")
}

//拉取卡号
function pullBatchCardNo(readerIds,type,time){
    $.get("accTransaction.do?readerCardNo",{readerIds:readerIds,type:type,time:time},function(result){
        time = result[sysCfg.data].time;
        if(result[sysCfg.ret] == sysCfg.success && result[sysCfg.data].cardNo != ""){
            var cardNo = result[sysCfg.data].cardNo.toLowerCase();
            $("#cardNoValidateForm input[name='cardNo']").val(cardNo);
            $("#batchAddCard").click();
            time = "";
        }
        persReaderCardTime = window.setTimeout(function(){
            pullBatchCardNo(readerIds,type,time);
        },2000);
    });
}
</script>




















