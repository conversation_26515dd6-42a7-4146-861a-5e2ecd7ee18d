<#include 'public/template/editTemplate.html'>
<#macro editContent>
<script type="text/javascript" src="/js/qrCodeWithLogo.min.js"></script>
<div style="text-align:center">
    <div id="qrCodeDiv">
    <#if cardQrCode??>
    <input name="cardQrCode" type="hidden" id="cardQrCode" value="${cardQrCode!}"/>
    <canvas id="canvas${uuid}" width="400" height="400"></canvas>
    <#else>
    <span><@i18n 'vis_message_accessQrCodeError' /></span>
    </div>
</div>
</#if>
</div>
</#macro>
<#macro buttonContent>
<button class='button-form' onclick="downloadQrcode()"><@i18n 'common_download'/></button>
<button class='button-form' onclick="DhxCommon.closeWindow()" id="closeButton${uuid}"><@i18n 'common_edit_cancel'/></button>
</#macro>

<script type="text/javascript">
    (function() {
        if($("input[name='cardQrCode']").val() != null && $("input[name='cardQrCode']").val() != "")
        {
            QrCodeWithLogo.toCanvas({
            canvas: document.getElementById('canvas${uuid}'),
            content: $("input[name='cardQrCode']").val(),
            width: 400
            });
        }
    })();
    
    // 下载二维码
    function downloadQrcode(){
    //获取二维码值，并修改响应头部。
    var data = $("#qrCodeDiv canvas")[0].toDataURL().replace("image/png", "image/octet-stream");;
    //保存的图片名称和格式，canvas默认使用的png格式。这个格式效果最好。
    var filename = "QRCode.png";
    var isIE = !!window.ActiveXObject || "ActiveXObject" in window;
    // IE 和 Edge 需要转换成 blob 才能保存文件。
    if (isIE || navigator.userAgent.indexOf("Edge") > -1) {
        var blob = convertBase64UrlToBlob({
            dataURL: data,
            type: 'image/png',
            ext: 'png'
        });
        if (isIE) {
            window.navigator.msSaveBlob(blob, filename);
        } else {
            var save_link = document.createElement('a');
            save_link.download = filename;
            save_link.href = URL.createObjectURL(blob);
            save_link.click();
        }
    } else {
        var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        save_link.href = data;
        save_link.download = filename;
        var event = document.createEvent('MouseEvents');
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        save_link.dispatchEvent(event);
    }
}
    /**
 *  将 base64 转换位 blob 对象
 *  blob 存储 2进制对象的容器
 *  @param base64
 *  @return Blob
 */
function convertBase64UrlToBlob(base64) {
    var parts = base64.dataURL.split(';base64,');
    var contentType = parts[0].split(':')[1];
    var raw = window.atob(parts[1]);
    var rawLength = raw.length;
    var uInt8Array = new Uint8Array(rawLength);
    for (var i = 0; i < rawLength; i++)
    {
        uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array], { type: contentType });
}
</script>