<#assign gridName="persCardGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
	    		<td valign="middle">
					<@ZKUI.Input name="cardNo"  maxlength="30" title="pers_card_cardNo" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="personPin"  maxlength="30" title="pers_person_pin" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="deptName"  maxlength="30" title="pers_dept_deptName" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
 			<tr>
 				<td valign="middle">
					<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Combo empty="true" name="cardState" title="pers_card_state">
			    		<option value="1"><@i18n 'pers_card_effect'/></option>
			    		<option value="3"><@i18n 'pers_card_disabled'/></option>
			    	</@ZKUI.Combo>
				</td>
 			</tr>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="pers:card:refresh"/>
    	<@ZKUI.ToolItem id="skip.do?page=pers_card_batchIssueCard" text="pers_batchIssCard_entity" img="pers_batchAddPersCard.png" action="batchIssueCardOpen" permission="pers:card:batchIssueCard" />
		<@ZKUI.ToolItem id="skip.do?page=pers_card_batchIssueAcmsCard" text="pers_batchIssCard_acms" img="pers_batchAddPersCard.png" action="batchIssueCardOpen" permission="pers:card:acms" isShow="#language!='zh_CN'"/>
		<@ZKUI.ToolItem id="persCard.do?batchCardLoss" text="pers_lossCard_entity" img="pers_loseCard.png" action="commonOperate" permission="pers:card:batchCardLoss"/>
    	<@ZKUI.ToolItem id="persCard.do?batchCardRevert" text="pers_revertCard_entity" img="pers_revertCard.png" action="commonOperate" permission="pers:card:batchCardRevert"/>
		<@ZKUI.ToolItem id="persCard.do?export" text="common_op_export" img="comm_export.png" type="export" permission="pers:card:export"></@ZKUI.ToolItem>
	</@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.pers.vo.PersCardItem" query="persCard.do?list"/>
</@ZKUI.GridBox>
<script type="text/javascript">
//批量发卡弹窗
function batchIssueCardOpen(id){
	var opts = {
		path: id,//设置弹窗路径
		width: 1050,//设置弹窗宽度
		height: 550,//设置弹窗高度
		title: "<@i18n 'pers_batchIssCard_entity'/>",//设置弹窗标题
		gridName: "gridbox"
	};
	DhxCommon.createWindow(opts);
}
//刷新页面
function refreshPersCardPage(){
    ZKUI.Grid.reloadGrid("${gridName}");
}
</script>