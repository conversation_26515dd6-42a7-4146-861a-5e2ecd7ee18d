<#include '/public/template/editTemplate.html'>
<#macro editContent>
<link href="/css/wiegandfmt.css" rel="stylesheet" type="text/css"/>
<form action="persWiegandFmt.do?save" method="post" id="${formId}" enctype="multipart/form-data">
	<input type="hidden" name="id" value="${(item.id)!}"/>
	<input type="hidden" value="" name="wgMode" id="idWGMode" />
	<table class="tableStyle">
		<tbody>
			<tr>
				<th style="width: 200px">
					<label><@i18n 'common_name'/></label><span class="required">*</span>
				</th>
				<td>
					<input name="name" value="${(item.name)!}" maxLength="30" type="text" />
				</td>
			</tr>
			<tr>
				<th>
					<label><@i18n 'pers_wgFmt_totalBit'/></label><span class="required">*</span>
				</th>
				<td>
					<input name="wiegandCount" id="idWGCount" value="${(item.wiegandCount)!}" maxLength="3" type="text" />
					<span class="persTooltip wiegandfmtTooltip"> 
						<span class="pers_icv icv-ic_que zk-colors-fg-green" />
						<span class="persCommonTipClass wiegandfmtSchtip" style="width:150px;">
							<p class="warningColor"><@i18n 'pers_wgFmt_supportDigitsNumber'/></p>
						</span>
					</span>
				</td>
			</tr>
			<tr>
				<th>
					<label><@i18n 'pers_wiegandFmt_siteCode'/></label>
				</th>
				<td>
					<input name="siteCode" id="idSiteCode" value="${(item.siteCode)!0}" maxLength="${siteCodeLen}" type="text"
					 onkeyup="javascript:this.value.substring(0,1)=='0' ? this.value='0' : void(0);" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/[^\d+\.+\/]/g,''))"/>
				</td>
			</tr>
			<tr id="divDefaultFmt">
				<th><label><@i18n 'pers_wiegandFmt_isDefaultFmt'/></label></th>
				<td>
					<#if (item.isDefaultFmt)?exists && item.isDefaultFmt?string == "true">
					<@ZKUI.Input hideLabel="true" type="checkbox" name="isDefaultFmt" id="defaultFmt" value="true" eventCheck=true/>
					<#else>
					<@ZKUI.Input hideLabel="true" type="checkbox" name="isDefaultFmt" id="defaultFmt" eventCheck=true/>
					</#if>
				</td>
			</tr>
			<tr>
				<td colspan="2">
					<span>
						<@ZKUI.Input hideLabel="true" type="radio" class="persWgRadioClass" name="wiegandMode" value="1" checked="checked" id="idMode1"/> <@i18n 'pers_wiegandFmt_wiegandModeOne'/>
					</span>
				</td>
			</tr>
			<tr>
				<th>
					<label><@i18n 'pers_wgFmt_firstParity'/></label>
				</th>
				<td>
					<input name="firstParity" value="${(firstParity)!}" id= "idFirstParity"  mode="wgModeOne" maxLength="3" type="text"/>
				</td>
			</tr>
			<tr>
				<th>
					<label><@i18n 'pers_wgFmt_secondParity'/></label>
				</th>
				<td>
					<input name="secondParity" value="${(secondParity)!}" id= "idSecondParity"  mode="wgModeOne" maxLength="3" type="text"/>
				</td>
			</tr>
		</tbody>
	</table>
	<table class="wg_table_inner_mode_one" style="width:880px;margin-left: 8px;padding-top:5px;">
		<tbody>
			<tr>
				<td width="150PX" colspan="2"><@i18n 'pers_wgFmt_oddPch'/></td>
				<td width="150PX" colspan="2"><@i18n 'pers_wgFmt_evenPck'/></td>
				<td width="150PX" colspan="2"><@i18n 'pers_wgFmt_CID'/></td>
				<td width="150PX" colspan="2"><@i18n 'pers_wgFmt_siteCode'/></td>
				<td colspan="2"><@i18n 'pers_wgFmt_manufactoryCode'/></td>
			</tr>
			<tr>
				<td><@i18n 'pers_wgFmt_startBit'/></td>
				<td><@i18n 'common_length'/></td>
				<td><@i18n 'pers_wgFmt_startBit'/></td>
				<td><@i18n 'common_length'/></td>
				<td><@i18n 'pers_wgFmt_startBit'/></td>
				<td><@i18n 'common_length'/></td>
				<td><@i18n 'pers_wgFmt_startBit'/></td>
				<td><@i18n 'common_length'/></td>
				<td><@i18n 'pers_wgFmt_startBit'/></td>
				<td><@i18n 'common_length'/></td>
			</tr>
			<tr> 
				<td>
					<input name="oStart" id="id_odd_start" mode="wgModeOne" class="inputValid" value="${(oStart)!}" maxLength="3" type="text"/>
				</td>
				<td>
					<input name="oCount" id="id_odd_count" mode="wgModeOne" class="inputValid" value="${(oCount)!}" maxLength="3" type="text" />
				</td>
				<td>
					<input name="eStart" id="id_even_start" mode="wgModeOne" class="inputValid" value="${(eStart)!}" maxLength="3" type="text"/>
				</td>
				<td>
					<input name="eCount" id="id_even_count" mode="wgModeOne" class="inputValid" value="${(eCount)!}" maxLength="3" type="text" />
				</td>
				<td>
					<input name="cStart" id="id_cid_start" mode="wgModeOne" class="inputValid" value="${(cStart)!}" maxLength="3" type="text"/>
				</td>
				<td>
					<input name="cCount" id="id_cid_count" mode="wgModeOne" class="inputValid" value="${(cCount)!}" maxLength="3" type="text" />
				</td>
				<td>
					<input name="sStart" id="id_site_start" mode="wgModeOne" class="inputValid" value="${(sStart)!}" maxLength="3" type="text" />
				</td>
				<td>
					<input name="sCount" id="id_site_count" mode="wgModeOne" class="inputValid" value="${(sCount)!}" maxLength="3" type="text" />
				</td>
				<td>
					<input name="mStart" id="id_comp_start" mode="wgModeOne" class="inputValid" value="${(mStart)!}" maxLength="3" type="text"/>
				</td>
				<td>
					<input name="mCount" id="id_comp_count" mode="wgModeOne" class="inputValid" value="${(mCount)!}" maxLength="3" type="text" />
				</td>
			</tr>
		</tbody>
	</table>
	<table class="tableStyle">
		<tr>
			<td colspan="2">
				<span>
					<@ZKUI.Input hideLabel="true" type="radio" class="persWgRadioClass" name="wiegandMode" value="2" id="idMode2"/> <@i18n 'pers_wiegandFmt_wiegandModeTwo'/>
				</span>
			</td>
		</tr>
	</table>
	<table class="wg_table_inner_mode_two" style="width:880px;margin-left: 8px;">
		<tr style="padding-left: 15px;">
			<td style="width:170px; padding-left:10px;">
				<label><@i18n 'pers_wgFmt_cardFmt'/></label><span class="required">*</span>
			</td>
			<td style="padding-left:10px;">
				<input name="cardFmt" id="idCardFmt" value="${(item.cardFmt)!}" type="text"/>
			</td>
		</tr>
		<tr>
			<td style="padding-left:10px;">
				<label><@i18n 'pers_wgFmt_parityFmt'/></label><span class="required">*</span>
			</td>
			<td style="padding-left:10px;">
				<input name="parityFmt" id="idParityFmt" value="${(item.parityFmt)!}" type="text" />
			</td>
		</tr>
	</table>
</form>
<div id="idInfo" align="center"  style="height: 13px; color: red; margin-top: 10px;">
	<ul class="errorlist">
		<li style="width:100%;"></li>
	</ul>
</div>

<script>
    <#if (item.isDefaultFmt)?exists && item.isDefaultFmt?string == "true">
        $("#defaultFmt").prop("checked", true);
    $("#defaultFmt").val(true);
    </#if>
    $("#defaultFmt").click(function(){
        if($(this).is(":checked"))
        {
            $("#defaultFmt").val(true);
        }
        else
        {
            $("#defaultFmt").val(false);
        }
    });
</script>
<script type="text/javascript">
//设置默认的韦根格式按钮为灰色
function setOkButton(){
	var modelId=parseInt($("#id_model_pk").val());
	if(modelId<11)
	{
		$("#editFormOK").attr("disabled","disabled");
	}
}

$(function() {
	<#if (item.wiegandMode)?exists && item.wiegandMode == 2>
	$("#idMode2").prop("checked", true);
	modelChange();
	</#if>
    $("#defaultFmt").change(function(){
        if($(this).is(':checked'))
        {
            $("#defaultFmt").val(true);
        }
        else
        {
            $("#defaultFmt").val(false);
        }
    });

    $("input[name='wiegandMode']").click(function(){
		modelChange();
	});

	//第一个奇偶校验位验证
	jQuery.validator.addMethod("FirstParityValidator", function(value, element){
		var idWGCount =parseInt($("#idWGCount").val());
		var idFirstParity=parseInt($("#idFirstParity").val());
		if(idFirstParity>idWGCount)
		{
			return false;
		}
		return true;
	}, "<@i18n 'pers_wgFmt_verify6'/>");
	//第二个奇偶校验位验证
	jQuery.validator.addMethod("SecondParityValidator", function(value, element){
		var idWGCount =parseInt($("#idWGCount").val());
		var idSecondParity=parseInt($("#idSecondParity").val());
		if(idSecondParity>idWGCount)
		{
			return false;
		}
		return true;
	},"<@i18n 'pers_wgFmt_verify7'/>");

	//整对值一起校验
	jQuery.validator.addMethod("countValid", function(value, element){
		var id = element.getAttribute("id");
		var nowId = "";
		var otherId = "";
		for(var i=0; i < idArray.length; i++)
		{
			var ida = "id_"+idArray[i]+"_start";
			var idb = "id_"+idArray[i]+"_count";
			if(id == ida || id == idb)
			{
				nowId = ida;
				otherId = idb;
			}
		}
		if(($("#"+nowId).val() != "" )&&($("#"+otherId).val() == ""))
		{
			return false;
		}
		if(($("#"+otherId).val() != "")&&($("#"+nowId).val() == ""))
		{
			return false;	
		}
		return true;
	}, "<@i18n 'common_jqMsg_required'/>");
	//整对值一起校验
	jQuery.validator.addMethod("isMaxCount", function(value, element){
		var id = element.getAttribute("id");
		var idWGCount =parseInt($("#idWGCount").val());
		if($("#"+id).val()!="" && parseInt($("#"+id).val())>idWGCount)
		{
			return false;
		}
		return true;
	}, "<@i18n 'pers_wgFmt_verify9'/>");
	//整对值和校验
	jQuery.validator.addMethod("sumValid", function(value, element){
		var id = element.getAttribute("id");
		var nowId = "";
		var otherId = "";
		for(var i=0; i < idArray.length; i++)
		{
			var ida = "id_"+idArray[i]+"_start";
			var idb = "id_"+idArray[i]+"_count";
			if(id == ida || id == idb)
			{
				nowId = (id == ida ? ida : idb);
				otherId = (id == ida ? idb : ida);
			}
		}
		
		if($("#"+nowId).val() != "" && $("#"+otherId).val() != "")
		{
			if((parseInt($("#"+nowId).val()) + parseInt($("#"+otherId).val()) - 1) > $("#idWGCount").val())
		    {
		    	//$("#"+otherId).valid();
		    	return false;
		    }
		}
		return true;
	}, "<@i18n 'pers_wgFmt_verify9'/>");
	//奇偶模块校验
	jQuery.validator.addMethod("oddEvenValid", function(value, element){
		var id = element.getAttribute("id");
		var nowStart = "";
		var nowCount = "";
		var otherStart = "";
		var otherCount = "";
		for(var i=0; i < idArray.length-2; i++)
		{
			var ida = "id_"+idArray[i]+"_start";
			var idb = "id_"+idArray[i]+"_count";
			if(id == ida)
			{
				nowStart = ida;
				nowCount = idb;
				if(id == "id_odd_start")
				{
					otherStart = "id_even_start";
					otherCount = "id_even_count";	
				}
				if(id == "id_even_start")
				{
					otherStart = "id_odd_start";
					otherCount = "id_odd_count";
				}
			}
		}
		if($("#"+nowStart).val() != "" && $("#"+otherStart).val() != "")
		{
			var nowStartVal = parseInt($("#"+nowStart).val());
			var otherStartVal = parseInt($("#"+otherStart).val());
			if(nowStartVal >= otherStartVal)
		    {
				if(nowStartVal <= otherStartVal + parseInt($("#"+otherCount).val()) - 1)
				{
					return false;
				}
		    }
			else
			{
				if(otherStartVal <= nowStartVal + parseInt($("#"+nowCount).val()) - 1)
				{
					return false;
				}
			}
		}
		return true;
	}, "<@i18n 'pers_wgFmt_verify3'/>");
	
		//奇偶校验总长度验证
	jQuery.validator.addMethod("oddEvenSumValid", function(value, element){
		var id = element.getAttribute("id");
		var wgCount=$("#idWGCount").val();
		var otherCount=""
		
		if(id == "id_even_count")
		{
			otherCount = "id_odd_count";	
		}
		if(id == "id_odd_count")
		{
			otherCount = "id_even_count";
		}
		
		if( $("#"+id).val()!="" && $("#"+otherCount).val()!="")
		{
			if(parseInt($("#"+id).val())+parseInt($("#"+otherCount).val())!=wgCount)
			{
				return false;
			}
		}
		if(idEvenCount!="")
		{
			if(parseInt($("#"+id).val())!=wgCount)
			{
				return false;
			}
		}
		
		return true;
	}, "<@i18n 'pers_wgFmt_verify3'/>");
	
		//cfsm模块验证
		jQuery.validator.addMethod("cfsmValid", function(value, element){
			for(var i=2; i < idArray.length; i++)
			{
				for(var j=2;j<idArray.length;j++)
				{
					if(idArray[i]!=idArray[j])
					{
						//当前模块
						var startValue=parseInt($("#id_"+idArray[i]+"_start").val());//当前模块起始值
						var startCountValue= parseInt($("#id_"+idArray[i]+"_start").val())+parseInt($("#id_"+idArray[i]+"_count").val());//当前模块最大值
						
						//其他模块最小值与最大值
						var minValue = parseInt($("#id_"+idArray[j]+"_start").val());//最小值
						var maxValue = parseInt($("#id_"+idArray[j]+"_start").val())+parseInt($("#id_"+idArray[j]+"_count").val());//最大值
						if(((startValue>=minValue) && (startValue<maxValue))||((startCountValue>minValue) && (startCountValue<=maxValue)))
						{
							return false;
						}
					}
				}
			}
			return true;
	}, "<@i18n 'pers_wgFmt_verify10'/>");
		
	$("#${formId}").validate( {
		debug : true,
		rules :
		{
			"name" : 
			{
				required : true,
				unInputChar:true,
				overRemote : ["persWiegandFmt.do?isExist", "${(item.name)!}"]
		  	},
		  	"wiegandCount" : {
		  		required : true,
		  		digits : true,
		  		min: 1,
		  		max: 128
		  	},
		  	"oStart" : {
		  		digits : true,
		  		min:1,
		  		isMaxCount:true,
		  		countValid:true,
		  		sumValid:true,
		  		oddEvenValid: true
		  	},
		  	"oCount" : {
		  		digits : true,
		  		min: 1,
		  		isMaxCount : true,
		  		countValid:true,
		  		sumValid: true
		  	},
		  	"eStart" : {
		  		digits : true,
		  		min: 1,
		  		isMaxCount : true,
		  		countValid:true,
		  		sumValid: true,
		  		oddEvenValid: true
		  	},
		  	"eCount" : {
		  		digits : true,
		  		min: 1,
		  		isMaxCount : true,
		  		countValid:true,
		  		sumValid: true
		  	},
		  	"cStart" : {
		  		required : true,
		  		digits : true,
		  		min: 1,
		  		isMaxCount : true,
		  		countValid:true,
		  		sumValid: true,
		  		cfsmValid:true
		  	},
		  	"cCount" : {
		  		required : true,
		  		digits : true,
		  		min: 1,
		  		isMaxCount : true,
		  		countValid:true,
		  		sumValid: true,
		  		cfsmValid:true
		  	},
		  	"sStart" : {
		  		digits : true,
		  		min: 0,
		  		isMaxCount : true,
		  		countValid:true,
		  		sumValid: true,
		  		cfsmValid:true
		  	},
		  	"sCount" : {
		  		digits : true,
		  		min: 0,
		  		isMaxCount : true,
		  		countValid:true,
		  		sumValid: true,
		  		cfsmValid:true
		  	},
		  	"mStart" : {
		  		digits : true,
		  		min: 0,
		  		isMaxCount : true,
		  		countValid:true,
		  		sumValid: true,
		  		cfsmValid:true
		  	},
		  	"mCount" : {
		  		digits : true,
		  		min: 0,
		  		isMaxCount : true,
		  		countValid:true,
		  		sumValid: true,
		  		cfsmValid:true
		  	}
		  	,
		  	"siteCode" :
		  	{
		  		digits : true
		  	}
		},
		submitHandler : function()
		{
			if(beforeSubmit() == true)
			{
				<@submitHandler/>
			}
		}
	});
	
	// 提交前验证韦根格式设置
	function beforeSubmit()
	{
		$("input[name^='wiegandMode']").each(function(){
		    if($(this).is(":checked"))
		    {
		        $("#idWGMode").val($(this).val());
		    }
		});
		var wgMode = $("#idWGMode").val();
		if(wgMode == 1)
		{
			// 模式一暂不做判断，后续需要补充
		}
		else
		{
			if($("#idCardFmt").val().length != $("#idWGCount").val())
			{
				$("#idInfo").show();
				$("#idInfo li").html("<@i18n 'pers_wgFmt_verify2'/>");
                return false;
			}
			if($("#idParityFmt").val().length != $("#idWGCount").val())
			{
				$("#idInfo").show();
				$("#idInfo li").html("<@i18n 'pers_wgFmt_verify3'/>");
                return false;
			}
		}
		return true;
	}

    //数字验证
    var idArray = ["odd", "even", "cid", "site", "comp"];//模块代号
    for(var i=0; i < idArray.length; i++)
    {
        $("#id_"+idArray[i]+"_start").keyup(function(){
            var id = $(this).attr("id");
            id = id.replace("start","count");
            $("#"+id).valid();
        });
        $("#id_"+idArray[i]+"_count").keyup(function(){
            var id = $(this).attr("id");
            id = id.replace("count","start");
            $("#"+id).valid();
        });
    }

    $(".inputValid").blur(function(){
        var idArray = ["odd", "even", "cid","dev","site", "comp"];//模块代号
        for(var i=0; i < idArray.length; i++)
        {
            $("#id_"+idArray[i]+"_start").valid();
            $("#id_"+idArray[i]+"_count").valid();
        }
    });
});

function modelChange(){
	
	if($("#idMode2").is(":checked")){
		$(".inputValid").attr("disabled","disabled");
		$("#idFirstParity").attr("disabled","disabled");
		$("#idSecondParity").attr("disabled","disabled");
		if($("#idFirstParity").hasClass("error"))
		{
			$("#idFirstParity").removeClass("error");
			$(".ts_box").remove();
		}
		if($("#idSecondParity").hasClass("error"))
		{
			$("#idSecondParity").removeClass("error");
			$(".ts_box").remove();
		}
		if($(".inputValid").hasClass("error"))
		{
			$(".inputValid").removeClass("error");
			$(".ts_box").remove();
		}
	}else{
		$(".inputValid").attr("disabled",false);
		$("#idFirstParity").attr("disabled",false);
		$("#idSecondParity").attr("disabled",false);
	
	//	$(".inputValid").trigger("blur");
	}
	
}

//sitecode验证切换时将sitecode的值置空
function changeSiteCode()
{
	var isVerified = $("#isVerified").val();
	if (isVerified == "true")
	{
		$("#idSiteCode").removeAttr("readonly");
	}
	else
	{
		$("#idSiteCode").val("0");
		$("#idSiteCode").attr("readonly", "readonly");
	}
}

</script>
</#macro>