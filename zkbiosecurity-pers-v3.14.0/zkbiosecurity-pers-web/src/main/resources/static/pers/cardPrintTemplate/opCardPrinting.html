<#include "/public/template/winTemplate.html">
<script type="text/javascript" src="/public/controls/jquery/jquery.ui.draggable.js"></script>
<#macro winContent>
<style type="text/css">
.containment-wrapper {
	background-color:#FFFFFF;
	background-size: 100% 100%;
	position: relative;
	border: 1px solid #ccc;
	border-radius:15px;
}
.horizontal{
	width: 505px;
	height: 318px;
	background-size: 100%;
}
.draggable {
	position: absolute;
	font-size: 18px;
	line-height: 80%;
	cursor: pointer;
	-moz-user-select: none;
	-webkit-user-select:none;
	user-select:none;
	white-space: nowrap;
}
.selected{
	background-color: #ccc;
}
.photo {
	height: 130px;
	line-height: 130px;	
}
.line{
	border-bottom: 1px solid; 
 	min-height: 0px; 
 	line-height：0px; 
 	width: 130px;
 	padding: 0 2px; 
 	color: #000;
}

.textCenter {
    display: inline;
    float: left;
    padding-left: 5px;
    padding-top: 5px;
}

</style>

<form id='${formId}'>
	<input type="hidden" id="personId${uuid}" name="persPerson" value="${(persPersonItemList[0].id)!}">
	<input type="hidden" id="frontImgBase64">
	<input type="hidden" id="oppoImgBase64">
	<table class="tableStyle" style="margin-bottom:10px;">
		<tr>
			<th style="min-width:60px;width:120px;"><@i18n 'pers_cardTemplate_tempSelect'/></th>
			<td align="left">
				<@ZKUI.Combo width="148" id="persCardPrintTemplate" empty="false" name="cardTemplate" hideLabel="true" readonly="readonly" title="pers_cardTemplate_tempSelect" onAfterLoad="setTemplateCookieValue" path="/basePrintTemplate.do?getPrintTemplateByModule&moduleCode=pers">
				</@ZKUI.Combo>
			</td>
			<th style="min-width:60px;width:120px;"><@i18n 'pers_cardTemplate_printOption'/></th>
			<td>
			<div><@ZKUI.Input hideLabel="true" type="radio" name="persPrintSurface" value="0"/>
			<span style="padding-right:8px;"><@i18n 'pers_cardTemplate_duplexPrint' /></span>
			<@ZKUI.Input hideLabel="true" type="radio" name="persPrintSurface" value="1"/>
				<span><@i18n 'pers_cardTemplate_frontOnly' /></span></div>
			</td>
		</tr>
		
		<tr>
			<th style="min-width:60px;width:120px;"><@i18n 'pers_cardTemplate_printerSelect'/></th>
			<td align="left" style="width:100px;">
				<@ZKUI.Combo width="148" id="persCardPrinter" empty="false" name="persCardPrinter" hideLabel="true" readonly="readonly" title="pers_cardTemplate_printerSelect">
				</@ZKUI.Combo>
			</td>
			<td id="persCarsPrinterWarnTd" colspan="2">
			<div id="persCarsPrinterDriverDownload" style="display:none"><span id="persCardPrinterSpan" class="warningColor"></span></div>
			</td>
		</tr>
	</table>
	<div id="persCardPrint_tabbar" style="padding:5px; min-height: 550px;"></div>
	<div id="persCardPrint_front" style="min-height: 510px;text-align: center;">
	<div style="display: inline-block;">
		<ul><li class="textCenter" >
				<div style="width: 507px;height: 517px;overflow:auto;">
					<div id="persCardPrint_previewFront" class="containment-wrapper" ></div>
				</div>
	    	</li>
			<li class="textCenter" id="persCardPrint_frontAttrLi">
				<div style="line-height: 15px; float:left;margin-right:5px;margin-top:18px;text-align:left;width:70px;">
                	<span id="totalProgressTitle${uuid}"><@i18n 'common_op_totalProgress'/></span>
                </div>
                <div id="cardPrintTotalProgressId" class="progressBar" style="width:350px; float:left; margin-top:15px; padding-right:6px;">
              		  <span></span>
                      <img src="/public/images/searching.gif" id="cardPrintLoadingImg" width="14px" style="margin-top: -15px;float: right;display: none"/>
                	<div id="cardPrintPercentNum" style="margin:-15px 0px 0px 25px;"></div>
                </div><br /><br /><br />
                <div id="persCardPrint_frontAttrGridbox" style="width: 435px;height:445px;float: left;padding:0px;">
		    	</div>
			</li>
		   </ul>
		   </div>
	</div>
    <div id="persCardPrint_opposite" style="min-height: 510px;text-align: center;">
    <div style="display: inline-block;">
	    <ul><li class="textCenter">
				<div style="width: 507px;height: 517px;overflow:auto;">
					<div id="persCardPrint_previewOppo" class="containment-wrapper"></div>
				</div>
			</li>
			<li class="textCenter" id="persCardPrint_oppoAttrLi">
				<div style="line-height: 15px; float:left;margin-right:5px;margin-top:18px;">
                	<span id="totalProgressTitle${uuid}"><@i18n 'common_op_totalProgress'/></span>
                </div>
                <div id="cardPrintTotalProgressId" class="progressBar" style="width:371px; float:left; margin-top:15px; padding-right:6px;">
              		  <span></span>
                      <img src="/public/images/searching.gif" id="cardPrintLoadingImgOppo" width="14px" style="margin-top: -15px;float: right;display: none"/>
                	<div id="cardPrintPercentNumOppo" style="z-index:2;position:relative; top:0;margin:-15px 0px 0px 25px;"></div>
                </div><br /><br /><br />
			
	  	    </li>
	  	    </ul>
	  	    </div>
    </div>
</form>
<script type='text/javascript'>

	function setTemplateCookieValue()
	{
		if($.cookie('persCardPrintTemplate') != undefined && $.cookie('persCardPrintTemplate') != 'NaN' && $.cookie('persCardPrintTemplate') != ZKUI.Combo.get('persCardPrintTemplate').combo.getSelectedValue())
		{
			this.setComboValue($.cookie('persCardPrintTemplate'));
		}
		if(ZKUI.Combo.get('persCardPrintTemplate').combo.getSelectedValue() == null) {
			this.selectOption(0)
		}
		if($.cookie('persPrintSurface') != undefined && $.cookie('persPrintSurface') != 'NaN')
		{
			$("input[name='persPrintSurface'][value="+$.cookie('persPrintSurface')+"]").attr("checked", "checked");
		}
		if(!$("input[name='persPrintSurface']:checked").val()){
			$("input[name='persPrintSurface'][value='0']").attr("checked", "checked");
		}
	}

	(function() {
		frontAttrGrid = new dhtmlXGridObject('persCardPrint_frontAttrGridbox');
		frontAttrGrid.setSkin(sysCfg.dhxSkin);
		frontAttrGrid.setImagePath(sysCfg.dhxImgPath);
        frontAttrGrid.setHeader("<img checkhead='true' src='" + getEnvProperty("system.dhxImg",sysCfg.dhxImgPath) + "dhxgrid_web/item_chk1.gif' onclick='onCustomCheckAll(event)'/>, <@i18n 'pers_person_pin'/>, <@i18n 'pers_person_wholeName'/>, <@i18n 'pers_dept_deptName'/>, <@i18n 'pers_cardTemplate_printStatus'/>");
		frontAttrGrid.setInitWidths("35,80,90,90,*");
		frontAttrGrid.setColAlign("center,left,left,left,left");
		frontAttrGrid.setColTypes("ch,ro,ro,ro,ro");
		frontAttrGrid.init();
	<#list persPersonItemList as personItem>
		frontAttrGrid.addRow("${(personItem.id)!}", ["",
			"${(personItem.pin)!}",
			"${(personItem.name)!} ${(personItem.lastName)!}","${(personItem.deptName)!}","<span id='${(personItem.id)!}'><@i18n 'pers_cardTemplate_waiting'/></span>"],0);
	</#list>
		includeScript("/js/persCardPrinting.js", function(){
			frontAttrGrid.checkAll();
			frontAttrGrid.selectRow(0,true,true,true);
		});
	})();

	function onCustomCheckAll(evt, gridName) {
		gridName="persCardPrint_frontAttrGridbox";
        var checked = $(evt.currentTarget).attr("checked");
		if (checked && checked == "checked") {
			frontAttrGrid.uncheckAll();
			$("#" + gridName + " .hdr img[checkhead='true']").attr("checked", false);
			$("#" + gridName + " .hdr img[checkhead='true']").attr("src", sysCfg.dhxImgPath + "/dhxgrid_web/item_chk0.gif");
		}
		else {
			frontAttrGrid.checkCurAll();
			$("#" + gridName + " .hdr img[checkhead='true']").attr("checked", true);
			$("#" + gridName + " .hdr img[checkhead='true']").attr("src", sysCfg.dhxImgPath + "/dhxgrid_web/item_chk1.gif");
		}
	}

</script>
</#macro>

<#macro operateButton>
    <button id="cardPrintButtonId" class='button-form' name="cardPrint" disabled="disabled"><@i18n 'pers_person_cardprint'/></button>
    <button class='button-form' id="persCardPrint_closeButton"><@i18n 'common_op_close'/></button>
</#macro>