<#assign editPage = "true">
<#include "public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
$(function(){
	var gridName = "${gridName}";
	var grid = ZKUI.Grid.get(gridName);
	var fieldData = grid.getDisplayField();
	document.getElementById("jsonColumn${uuid}").value = JSON.stringify(fieldData.columns);
	document.getElementById("queryConditions${uuid}").value = fieldData.qc;
	document.getElementById("records${uuid}").value = fieldData.records;
	document.getElementById("treeId${uuid}").value = grid.options.query;
	document.getElementById("pageXmlPath${uuid}").value = grid.options.vo;
	document.getElementById("sortName${uuid}").value = grid.grid.col_name;
	document.getElementById("sortOrder${uuid}").value = grid.grid.sort_order;
	if(window.top.system.tabbar) {
		$("#tableNameParam${uuid}").val(window.top.system.tabbar.cells(window.top.system.tabbar.getActiveTab()).getText());
	} else {
		var c = CONST.getValue("sysCode");
		var a = CONST.getValue(c + "_accId");
		var m = CONST.getValue(c + "_menuId");
		$("#tableNameParam${uuid}").val(system.myAcc.cells(a).dataObj.getItemText(m));
	}
	$("#${formId}").prop("action", "${actionName}");
	
	//设置双列表的文件名与表名 
	if(grid.options.title) {
		document.getElementById("tableNameSearch${uuid}").value = grid.options.title;
	}
});
</script>
<form method="post" action="" id="${formId}" enctype="multipart/form-data">
	<input type="hidden" name="jsonColumn" id="jsonColumn${uuid}"/>
	<input type="hidden" name="pageXmlPath" id="pageXmlPath${uuid}"/>
	<input type="hidden" name="queryConditions" id="queryConditions${uuid}"/>
	<input type="hidden" name="tableNameParam" id="tableNameParam${uuid}"/>
	<input type="hidden" name="tableNameSearch" id="tableNameSearch${uuid}"/>
	<input type="hidden" name="sortName" id="sortName${uuid}"/>
	<input type="hidden" name="sortOrder" id="sortOrder${uuid}"/>
	<input type="hidden" name="treeId" id="treeId${uuid}"/>
	<input type="hidden" name="records" id="records${uuid}"/>
	<input type="hidden" name="maxExportCount" id="maxExportCount${uuid}" value="${maxExportCount!'30000'}"/>
<!-- 	<input type="hidden" name="searchObjectId" id="searchObjectId${uuid}"/> -->
<!-- 	<input type="hidden" name="custom" id="custom${uuid}"/> -->
	
	<table class="tableStyle">
		<tbody>
			<tr id="loginPwdRow" hidden="hidden">
				<td><@i18n 'auth_user_userPwd'/><span class="required">*</span></td>
				<td>
					<div style="position:relative;">
						<input type="password" maxlength="18" id="loginPwd"/>
						<input type="hidden"  name="loginPwd" id="password_hidden"/>
					</div>
				</td>
			</tr>
			<!-- 新增行，启用加密导出（设置密码导出） add by sevenidea.lu 2021-4-1  -->
			<tr>
				<td width="120px"><@i18n 'common_file_encrypt' /></td>
				<td>
					<label><@ZKUI.Input hideLabel="true" name="isEncrypt" id="yes" type="radio" onchange="inputPassword()" value="1" checked="checked"/>
						<span><@i18n 'common_yes' /></span></label>
					<label><@ZKUI.Input hideLabel="true" name="isEncrypt" id="no" type="radio" onchange="inputPassword()" value="0" />
						<span><@i18n 'common_no' /></span></label>
				</td>
			</tr>
			<tr class="passwordCheck">
				<td><@i18n 'common_file_encrypt_pwd'/><span class="required">*</span></td>
				<td>
					<div style="position:relative;">
						<input type="password" maxlength="18" id="encryptPassword" name="encryptPassword"/>
						<span class="export-input-eye"></span>
					</div>
				</td>
			</tr>
			<tr>
				<td width="120px"><@i18n 'common_report_fileType'/></td>
				<td background="#f1fcfe">
					<@ZKUI.Combo hideLabel="true" width="148" empty="false" class="exportselect" name="reportType" id="reportType${uuid}" followType="close" followScroll=".content_div">
					<option value="ZIP">ZIP</option>
					</@ZKUI.Combo>
				</td>
			</tr>
		</tbody>
	</table>
</form>
</#macro>
<script type="text/javascript">
$().ready(function(){
	function getExportResult() {
		$.ajax({
			url:"skip.do?getExportResult",
			success: function(res) {
				if(res[sysCfg.data]=="end") {
					closeMessage();
					DhxCommon.closeWindow();
				} else {
					setTimeout(getExportResult, 3000);
				}
			}
		});
	}
	$("#${formId}").validate({
		debug : true,
		rules: {
		},
		messages:{
			"encryptPassword" : {
				noSpace:"<@i18n 'auth_user_noSpaceMsg'/>",
			}
		},
		submitHandler: function(form){
			openMessage(msgType.loading);
			$("#${formId}").ajaxSubmit({
				async:false,
				success: function(result){
					dealRetResult(result);
				}
			});
			setTimeout(getExportResult, 2000);	
		}
	});
});

function inputPassword() {
	var passwordEncrypt = $("input[name='isEncrypt']:checked").val();
	if(passwordEncrypt === "1") {
		$("input[name='encryptPassword']").rules("add", {required : true});
        $("input[name='encryptPassword']").rules("add", {rangelength : [ 4, 18 ]});
		$(".passwordCheck").show();
	} else {
		$("input[name='encryptPassword']").rules("remove");
		$(".passwordCheck").hide();
	}
};
inputPassword();
$(".export-input-eye").on("mousedown", function(e) {
	$(this).addClass("export-input-eye-open");
	$("#encryptPassword").attr("type", "text");
})

$(".export-input-eye").on("mouseup", function(e) {
	$(this).removeClass("export-input-eye-open");
	$("#encryptPassword").attr("type", "password");
})
$("#loginPwd").on("change", function() {
	if($("#loginPwd").val())
	{
		$("#password_hidden").val(hex_md5($("#loginPwd").val()));
	}
	else
	{
		$("#password_hidden").val("");
	}
})

isNeedValidUserForExport(function(ret) {
	if(ret && ret.ret === sysCfg.success) {
		$("#loginPwdRow").hide();
		$("input[name='loginPwd']").rules("remove");
	} else {
		$("#loginPwdRow").show();
		$("input[name='loginPwd']").rules("add", {required : true});
	}
})
</script>
