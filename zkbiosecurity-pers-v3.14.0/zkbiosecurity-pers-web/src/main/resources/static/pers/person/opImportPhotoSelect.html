<#assign editPage = "true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
    <form method="post" id="${formId}" enctype="multipart/form-data">
        <table class="tableStyle">
            <tr>
                <td><@i18n 'pers_import_type'/></td>
                <td>
                    <@ZKUI.Input hideLabel="true" type="radio" name="importType" checked="checked" value="1"/><@i18n 'pers_import_photoType'/>
                </td>
                <td>
                    <@ZKUI.Input hideLabel="true" type="radio" name="importType" value="2"/><@i18n 'pers_import_archiveType'/>
                </td>
            </tr>
            <#if facialTemplateEnable??  && facialTemplateEnable=="1">
            <tr>
                <td></td>
                <td colspan="2">
                    <@ZKUI.Input id="importPhoto" hideLabel="true" type="checkbox" name="importPhoto" checked="checked"/>
                    <span><@i18n 'pers_person_photo' /></span>
                    <@ZKUI.Input id="importCropFace" hideLabel="true" type="checkbox" name="importCropFace" checked="checked" onchange="changeImportCropFace()"/>
                    <span><@i18n 'pers_person_cropFace' /></span>
                    <@ZKUI.Input id="importTemplateFace" hideLabel="true" type="checkbox" name="importTemplateFace" checked="checked"/>
                    <span><@i18n 'pers_person_faceTempalte' /></span>
                </td>
            </tr>
            <#else>
            <tr>
                <td></td>
                <td>
                    <@ZKUI.Input id="importPhoto" hideLabel="true" type="checkbox" name="importPhoto" checked="checked"/>
                    <span><@i18n 'pers_person_photo' /></span>
                </td>
                <td>
                    <@ZKUI.Input id="importCropFace" hideLabel="true" type="checkbox" name="importCropFace" checked="checked"/>
                    <span><@i18n 'pers_person_cropFace' /></span>
                </td>
            </tr>
            </#if>
        </table>
    </form>
<script type="text/javascript">
    function uploadPhotoPageChange() {
        var importPhoto = $("#importPhoto").is(":checked");
        var importCropFace = $("#importCropFace").is(":checked");
        var importTemplateFace = $("#importTemplateFace").is(":checked");
        if(!importPhoto && !importCropFace)
        {
            openMessage(msgType.warning, "<@i18n 'pers_import_selectPhotoType'/>");
            return;
        }
        if(importTemplateFace)
        {
            if (persParams['pers.facialTemplate.enable'] != '1') {
                messageBox({messageType:"alert",text:"<@i18n 'pers_person_disableFaceTemplate'/>"});
                return;
            }
            testTemplateServerConnect(importPhoto, importCropFace, importTemplateFace);
        }
        else
        {
            uploadPhoto(importPhoto, importCropFace, importTemplateFace);
        }
    }

function uploadPhoto(importPhoto, importCropFace, importTemplateFace) {
    var importType = $("input:radio[name='importType']:checked").attr("value");
    var path;
    var opts;
    if (importType == 1) {
        path = "skip.do?page=pers_person_opImportPhoto&importPhoto="+importPhoto+"&importCropFace="+importCropFace+"&importTemplateFace="+importTemplateFace
        opts = {
            path:path,
            width: 810,
            height: 560,
            title:"<@i18n 'pers_import_personPhoto'/>",
            gridName:"gridBox"
        };
    }
    else if (importType == 2) {
        path = "skip.do?page=pers_person_opImportLargePhoto&importPhoto="+importPhoto+"&importCropFace="+importCropFace+"&importTemplateFace="+importTemplateFace
        opts = {
            path:path,
            width: 540,
            height: 540,
            title:"<@i18n 'pers_import_personPhoto'/>",
            gridName:"gridBox"
        };
    }
    DhxCommon.createWindow(opts);
}

function changeImportCropFace()
{
    if($("#importCropFace").is(":checked"))
    {
        $("#importTemplateFace").attr('disabled',false);
    }
    else{
        $("#importTemplateFace").attr('checked',false);
        $("#importTemplateFace").attr('disabled',true);
    }
}

function testTemplateServerConnect(importPhoto, importCropFace, importTemplateFace)
{
    onLoading(function () {
	$.ajax({
		type : "POST",
		url:"persParams.do?testTemplateServerConnect",
		success: function (result){
		    closeMessage();
			if (result.ret.trim() == "ok") {
                uploadPhoto(importPhoto, importCropFace, importTemplateFace);
			}
			else
			{
			     messageBox({
                    messageType : "confirm",
                    text : "<@i18n 'pers_param_templateServerOffline'/>",
                    callback : function(result)
                    {
                        if (result)
                        {
                            $("#importTemplateFace").attr('checked',false);
                            importTemplateFace = false;
                            uploadPhoto(importPhoto, importCropFace, importTemplateFace);
                        }
                        else
                        {
                            return false;
                        }
                    }
                });
			}
		},
		error : function(XMLHttpRequest, textStatus, errorThrown) {
		    closeMessage();
			messageBox({
                    messageType : "confirm",
                    text : "<@i18n 'pers_param_templateServerOffline'/>",
                    callback : function(result)
                    {
                        if (result)
                        {
                            $("#importTemplateFace").attr('checked',false);
                            importTemplateFace = false;
                            uploadPhoto(importPhoto, importCropFace, importTemplateFace);
                        }
                        else
                        {
                            return false;
                        }
                    }
                });
		}
	});
	});
}

</script>
</#macro>

<#macro buttonContent>
    <button class="button-form" onclick="uploadPhotoPageChange()"><@i18n 'common_edit_ok'/></button>
    <button class="button-form" onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/></button>
</#macro>