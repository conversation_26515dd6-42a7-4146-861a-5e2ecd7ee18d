<#include "/public/template/editTemplate.html">
<#macro editContent>
<style>
    #dataCount${uuid} td {
        padding: 1px 5px;
        border-bottom: 1px dotted #e8eaeb;
    }
    #dataCount${uuid} th {
        border-bottom: 1px dotted #e8eaeb;
    }
</style>
<script type='text/javascript'>
(function(){
    getDataCount();

})();

function getDataCount()
{
    var html = "";
	var deptIds = "";
	if(ZKUI.ComboTree.get("deptTree${uuid}"))
	{
	    deptIds = ZKUI.ComboTree.get("deptTree${uuid}").getValue();
	}
    onLoading(function(){
        $.ajax({
            url:"persPerson.do?dataCount",
            type:"post",
            dataType:"json",
            data:{"deptIds" : deptIds},
            success: function (data)
            {
                closeMessage(1);
                if(data[sysCfg.ret] == sysCfg.success) {
                    if (!$.isEmptyObject(data.data.maleCount))
                    {
                        html += getTemplateHtml(data.data.maleCount, "<@i18n 'pers_person_male'/>");
                    }
                    if (!$.isEmptyObject(data.data.feMaleCount))
                    {
                        html += getTemplateHtml(data.data.feMaleCount, "<@i18n 'pers_person_female'/>");
                    }
                    if (!$.isEmptyObject(data.data.unknownCount))
                    {
                        html += getTemplateHtml(data.data.unknownCount, "<@i18n 'common_unknown'/>");
                    }
                    if (!$.isEmptyObject(data.data.personCount))
                    {
                        html += getTemplateHtml(data.data.personCount, "<@i18n 'pers_person'/>");
                    }
                    if (!$.isEmptyObject(data.data.fpCount))
                    {
                        html += getTemplateHtml(data.data.fpCount, "<@i18n 'pers_person_regFinger'/>");
                    }
                    if (!$.isEmptyObject(data.data.faceCount))
                    {
                        html += getTemplateHtml(data.data.faceCount, "<@i18n 'pers_person_infraredFace'/>");
                    }
                    if (!$.isEmptyObject(data.data.fvCount))
                    {
                        html += getTemplateHtml(data.data.fvCount, "<@i18n 'pers_person_regVein'/>");
                    }
                    if (!$.isEmptyObject(data.data.palmCount))
                    {
                        html += getTemplateHtml(data.data.palmCount, "<@i18n 'pers_person_metacarpalVein'/>");
                    }
                    if (!$.isEmptyObject(data.data.vislightCount))
                    {
                        html += getTemplateHtml(data.data.vislightCount, "<@i18n 'pers_person_visibleFace'/>");
                    }
                    if (!$.isEmptyObject(data.data.cardCount))
                    {
                        html += getTemplateHtml(data.data.cardCount, "<@i18n 'pers_card'/>");
                    }
                    if (!$.isEmptyObject(data.data.cropFaceCount))
                    {
                        html += getTemplateHtml(data.data.cropFaceCount, "<@i18n 'pers_person_cropFace'/>");
                    }
                    if (!$.isEmptyObject(data.data.palm10Count))
                    {
                        html += getTemplateHtml(data.data.palm10Count, "<@i18n 'pers_person_visiblePalm'/>");
                    }
                    if (!$.isEmptyObject(data.data.irisCount))
                    {
                        html += getTemplateHtml(data.data.irisCount, "<@i18n 'pers_person_iris'/>");
                    }
                    var templateTr = $("tr[class='templateTr${uuid}']");
                    if(templateTr)
                    {
                        for(var i = 0; i < templateTr.length; i++)
                        {
                            removeObj(templateTr[i]);
                        }
                    }

                    $("#dataCount${uuid}").append(html);
                }
            }
        });
    });
}

//移除对象，解决IE不支持remove方法
function removeObj(obj)
{
	var parentObj = obj.parentNode;
	if(parentObj)
	{
		parentObj.removeChild(obj);
	}
}

function getTemplateHtml(count, title) {
    var html = "";
    var obj= null;
    var isJSONObj = isJSON(count);
    if(isJSONObj)
    {
        obj=JSON.parse(count);
        var keys = Object.keys(obj);
        // versionCount = keys.length;
        if(keys.length > 0)
        {
            html += '<tr class="templateTr${uuid}"><td style="vertical-align: middle;" rowspan="' + keys.length +'">' + title + '</td>';
        }
        else
        {
            html += '<tr class="templateTr${uuid}"><td colspan="2">' + title + '</td>';
        }
        if(keys.length == 0)
        {
            html += '<td>0</td></tr>';
        }
        for (var key in keys)
        {
            var versionStr = "";
            if(keys[key].indexOf(".") == -1)
            {
                versionStr = ".0"
            }
            if (key == 0)
            {
                html += '<td>V' + keys[key] + versionStr + '</td><td>'+ obj[keys[key]] + '</td>';
            }
            else
            {
                html += '<tr class="templateTr${uuid}"><td>V' + keys[key] + versionStr + '</td><td>'+ obj[keys[key]] + '</td></tr>';
            }
        }
        html += '</tr>';
    }
    else
    {
        html += '<tr class="templateTr${uuid}"><td colspan="2">' + title + '</td>';
        html += '<td>'+ count + '</td></tr>';
    }
    return html;
}

function isJSON(str) {
    if (typeof str == 'string') {
        try {
            var obj=JSON.parse(str);
            if(typeof obj == 'object' && obj ){
                return true;
            }else{
                return false;
            }

        } catch(e) {
            return false;
        }
    }
}
</script>
<style type="text/css">
	.persDataCountTable th{
		white-space: nowrap;
	}
</style>
<form id='${formId}'>
<div id="baseTab" style="overflow-y: auto;overflow-x: hidden;">
    <table class='tableStyle' id="dataCount${uuid}">
        <tr>
            <th colspan="2"><label><@i18n 'pers_dept_entity'/></label></th>
            <td style="padding-bottom:25px">
                <@ZKUI.ComboTree id="deptTree${uuid}" type="checkbox" url="authDepartment.do?tree" tree_onCheck="onTreeChecked" hideLabel="true" width="148" readonly="true" name="deptIds"/>
                <span class="icv-comm_search" id="search${uuid}" style="padding-left:2px;vertical-align:middle;cursor:pointer" onclick="getDataCount()"></span>
            </td>
        </tr>
        <tr>
            <th colspan="2"><@i18n 'pers_custField_StatisticalType'/></th>
            <th><@i18n 'common_currentTotal'/></th>
        </tr>
    </table>
</div>
</form>
</#macro>
<#macro buttonContent>
<button class='button-form' onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/></button>
</#macro>