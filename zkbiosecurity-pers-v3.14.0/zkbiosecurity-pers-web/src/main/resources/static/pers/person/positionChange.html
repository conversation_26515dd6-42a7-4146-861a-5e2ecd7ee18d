<#include "/public/template/editTemplate.html"> 
<#macro editContent>
<form id="${formId}" action="/persPerson.do?batchPositionChange" method="post" enctype="multipart/form-data">
	<input type="hidden" name="ids" value="${(ids)!}"/>
	<table class="tableStyle">
		<colgroup>
			 <col class="col1" width="100px"/>
			 <col class="col2" width="*"/>
		</colgroup>
		<tr>
			<th><label><@i18n 'pers_person_selectedPerson'/></label><span class="required">*</span></th>
			<th >
				<textarea name="pins" style="width:150px;height: 55px" readonly >${selectedPerson}</textarea>
			</th>
		</tr>
		<tr>
			<th>
				<@i18n 'pers_position_batchToPosition'/><span class="required">*</span>
			</th>
			<th>
				<input type="hidden" name="positionName" id="positionName">
				<@ZKUI.ComboTree width="148" type="radio" id='positionIdTree' onSelectionChange='assignmentPositionName' url="/persPosition.do?tree"  value="${(item.positionId)!}"  hideLabel="true" name="positionId"/>
			</th>
		</tr>
		<tr>
			<th>
				<@i18n 'pers_person_changeReason'/>
			</th>
			<th >
				<textarea style="width:150px;height: 55px" name="changeReason" maxlength="100"></textarea>
			</th>
		</tr>
	</table>
</form>
<script type="text/javascript">
(function(){
	$("#${formId}SaveContinue").remove();
	$("#${formId}").validate({
		debug : true,
		rules : {
		    "positionId" : {
				required :  true
			}
	    },
	    submitHandler : function() {
			<@submitHandler  callBackFun="reloadPersonTreeAndGrid()"/>
		}
	});
})();

function assignmentPositionName() {
	$("#positionName").val(ZKUI.ComboTree.get("positionIdTree").getText());
}
</script>
</#macro>