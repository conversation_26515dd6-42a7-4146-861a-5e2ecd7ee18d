<#include "/public/template/editTemplate.html">
<#macro editContent>
<script type="text/javascript">
    $().ready(function() {
        $("#${formId}").validate( {
            debug : true,
            rules :
            {
                "uploadFile" :
                {
                    required:true,
                    uploadFileFormatValid:true
                },
            },
            submitHandler : function()
            {
                // 清空日志显示
                $("#operatorDiv").empty();
                $("#errorLogDiv").empty();
                var formData = new FormData();
                // 获取文件对象
                var fileObj = ZKUI.Upload.get("upload${uuid}").getFile()[0];
                // 限制上传文件大小不能超过500M，500*1024*1024
                if (fileObj.size > 524288000) {
                    openMessage(msgType.warning, "<@i18n 'pers_import_uploadFileSize'/>".format("500M"))
                    return;
                }
                openMessage(msgType.loading, "<@i18n 'common_op_processing'/>")
                $("#${formId}").ajaxSubmit({
                    success: function(result)
                    {
                        if (result.ret == "ok") {
                            $("#operatorDiv").append(result.data.corrLog);
                            $("#errorLogDiv").append(result.data.errLog);
                            closeMessage();
                        }
                        else
                        {
                            ZKUI.Upload.get("#upload${uuid}").resetText();
                            openMessage(msgType.error,result.msg);
                        }
                        //导入结束之后刷新人员列表
                        if(typeof afterImportPersPerson == "function"){
                            afterImportPersPerson();
                        }
                    },
                    error: function(XMLHttpRequest, textStatus, errorThrown)
                    {
                        messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'common_prompt_serverError'/>"});
                    }
                });
            }
        });
    });
</script>

<form action="persPerson.do?saveLargePhoto" method="post" id="${formId}" enctype="multipart/form-data">
    <input type="hidden" name="importPhoto" value="${(importPhoto)!'true'}">
    <input type="hidden" name="importCropFace" value="${(importCropFace)!'true'}">
    <input type="hidden" name="importTemplateFace" value="${(importTemplateFace)!'true'}">
    <table class="imp_table" cellpadding="10">
        <tr>
            <td valign="middle">
                <@i18n 'common_dev_selectFile'/>
            </td>
            <td>
                <@ZKUI.Upload id="upload${uuid}" name="uploadFile" />
            </td>
        </tr>
    </table>
</form>
<div style="position:realative;margin-top:2%; height: 300px;">
    <div id="opLog" style="height:98%; width:49%;float: left">
        <span><@i18n 'pers_import_opera_log'/></span>
        <div id="operatorDiv" style="height: 98%;border: 1px solid #90cf5f; overflow-y:scroll;"/>
    </div>
    <div id="errorLog" style="height:98%; width:49%;float: right;">
        <span><@i18n 'pers_import_error_log'/></span>
        <div id="errorLogDiv" style="height: 98%;border: 1px solid red; overflow-y:scroll;"/>
    </div>
</div>
<div style="margin-top:15px;">
    <span class="warningImage"></span><span class="warningColor"><@i18n 'pers_import_uploadFileSizeLimit'/></span>
</div>
<div style="margin-top:5px;">
    <span class='warningImage'></span>
    <span class='warningColor'><@i18n 'pers_face_notUpdateMsg'/></span>
</div>

<script text="text/javascript">

    jQuery.validator.addMethod("uploadFileFormatValid", function(value, element) {
        var format = value.substring(value.lastIndexOf("."), value.length);
        if (format.toLowerCase() != ".zip") {
            return false;
        }
        return true;
    }, "<@i18n 'common_prompt_fileNameSuffix'/>".format("ZIP"));

    var currentWindow = DhxCommon.getCurrentWindow();
    currentWindow.attachEvent("onClose", function(win) {
        DhxCommon.closeWindow();
        return true;
    });

</script>
</#macro>

<#macro buttonContent>
    <button class="button-form" id="${formId}OK"><@i18n 'pers_import_startUpload'/>
    <button class="button-form" onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/>
</#macro>