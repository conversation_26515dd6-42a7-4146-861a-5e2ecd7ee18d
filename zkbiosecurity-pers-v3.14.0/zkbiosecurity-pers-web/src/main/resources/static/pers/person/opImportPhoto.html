<#include "/public/template/editTemplate.html">
<#macro editContent>

<form id="${formId}" onkeydown="if(event.keyCode==13){return false;}" enctype="multipart/form-data">
	<input type="hidden" name="importPhoto" value="${(importPhoto)!'true'}">
	<input type="hidden" name="importCropFace" value="${(importCropFace)!'true'}">
	<input type="hidden" name="importTemplateFace" value="${(importTemplateFace)!'true'}">
	<div style="display:none;">
		<input id="personPhoto" name="personPhotos" type="file" accept="image/jpeg,image/jpg,image/png" style="width:68px; cursor:pointer;"
			onclick="convertFileContent()" onchange="javascript:setImportPreview();" multiple="true"/>
	</div>
	<div id="img" style="position:relative;margin-top:10px; margin:0 auto;top:10px; border:2px solid #dcdfe2;width:760px;height:370px;overflow:auto;">
		<div id="midAddButton" style="position:relative;cursor:pointer; margin: 158px auto 0;text-align: center;">
			<button id="personPhotoButton" class="button-form" type="button" height="34" width="194" onclick="document.getElementById('personPhoto').click()">
				<@i18n 'pers_import_pleaseSelectPhoto'/>
			</button>
		</div>
		<div id="multipleTip" style="position: relative;width:250px; margin: 10px auto 0; text-align: center; color: #cccccc;"><@i18n 'pers_import_multipleSelectTip'/></div>
	</div>
	<div style="position:realative;margin-top:2%;margin-left:2%;">
		<span style=""><@i18n 'pers_import_totalNumber'/>：</span><label id="photoNum"></label>
		<div style="float:right; width:400px; text-align: center;"><@i18n 'pers_import_uploadTip'/></div>
	</div>
	<div style="position:realative;margin-top:1%;margin-left:2%;width:760px;">
		<span class='warningImage'></span><span class='warningColor'><@i18n 'pers_import_photoFormatRequirement'/></span>
		<span class='warningColor'><@i18n 'pers_import_photoSelectNumber'/></span>
	</div>
	<div style="margin-top:5px;margin-left:2%;width:760px;">
		<span class='warningImage'></span>
		<span class='warningColor'><@i18n 'pers_face_notUpdateMsg'/></span>
	</div>
</form>

<script type="text/javascript">

var countPhotoNum = 0;//记录图片的数量
var photoIndex = 0;//记录图片的下标
$().ready(function(){
	$("#photoNum").text(countPhotoNum);
})
var html = "";
var imgIndex = 0;
var fileArr = new Array();//文件数组
var fileNameArr = new Array();//存放预览照片的名称数组，即人员编号
var uploadNum = 0;//记录已上传的数量

//id="personPhoto"，onclick时把value值清空，保障预览图片onchange事件每次都能触发
function convertFileContent()
{
	var file=document.getElementById("personPhoto");
	file.value = '';
}

//上传的图片预览
function setImportPreview()
{
	var flag = true;//是否提示替换图片的条件
	var isReplace = false;//是否替换图片
	var docObj=document.getElementById("personPhoto");
	//限制选择照片不大于3000张
	if(parseInt(docObj.files.length) > 3000)
	{
		openMessage(msgType.warning, "<@i18n 'pers_import_photoSelectNumber'/>");
		return;
	}
	$("#addPhoto").remove();
	//预览的照片数量大于1,文件名不包含特殊字符
	if(docObj.files.length != 1 || checkSpecialCharacters(docObj.files[0]))
	{
		$("#midAddButton").hide();//隐藏页面中间的添加图片的图标
		$("#multipleTip").hide();//隐藏页面中间的复选提示
	}
 	html = "";
	for(var i=0; i< docObj.files.length; i++)
	{
		var previewFileName = docObj.files[i].name.split(".");
		//已预览的编号数组和选择的文件名重复,且未上传过
		if(JSON.stringify(fileNameArr).indexOf(JSON.stringify(previewFileName[0])) != -1 && flag && $("#totalProgressText"+(parseInt(photoIndex)-parseInt(1))).text() != "<@i18n 'common_succeed'/>" && $("#failTip"+(parseInt(photoIndex)-parseInt(1))).text() != "<@i18n 'common_failed'/>")
    	{
			flag = false;
			isReplace = true;
			messageBox({messageType:"confirm", text: "<@i18n 'pers_import_replacePhotoTip'/>",
				callback: function(result){
					if(result)//确定替换
					{
						var noRepeatNum = 0;//确定替换图片，记录没有重复的数量
	                	var repeatNum = 0;//记录重复的数量
	                	var errorNum = 0;//记录类型不正确的数量
						for(var i=0; i< docObj.files.length; i++)
						{
							var fileName = docObj.files[i].name.split(".");//获取文件名，不包含后缀
							if(JSON.stringify(fileNameArr).indexOf(JSON.stringify(fileName[0])) == -1)//已预览的编号数组和选择的文件名不重复
	                		{	
								if(getPhotoFormat(docObj.files[i]) && checkSpecialCharacters(docObj.files[i]))
								{
									//第一个参数，保证div的id参数是按照顺序
		                			previewPic(parseInt(photoIndex)+parseInt(i)-parseInt(repeatNum)-parseInt(errorNum), fileName, i, docObj);
		                			noRepeatNum ++;	
								}
								else
								{
									errorNum ++;
								}
	                		}
							else//重复
							{
								var list=document.getElementsByClassName("class_img_pers"+ fileName[0]);//获取class名称相同的元素
	                			var indexOver;
	                			var imgObjPreview;
	                			for(var j=0;j<list.length;j++)
	                			{
	                				indexOver = list[j].id;//获取id
	                				var idIndex = indexOver.replace('id_img_pers','');//截取id的下标
	                				if($("#totalProgressText"+idIndex).text() != "<@i18n 'common_succeed'/>" && $("#failTip"+idIndex).text() != "<@i18n 'common_failed'/>")//未上传的照片
	                				{
	                					imgObjPreview = document.getElementById(indexOver);
	                				    imgObjPreview.src = window.URL.createObjectURL(docObj.files[i]);//替换图片
	                				    fileArr.splice(parseInt(idIndex)-parseInt(uploadNum),1,docObj.files[i]);//替换起始下标为indexOver，长度为1的一个值为docObj.files[selectImgIndex]
	                					$(".class_personPin"+parseInt(idIndex)).html(docObj.files[i].name.split(".")[0]);//替换文件名
	                				}
	                			}
	                            repeatNum ++;
							}
						}
						countPhotoNum += parseInt(noRepeatNum);
	                	photoIndex += parseInt(noRepeatNum);
	                	$("#photoNum").text(countPhotoNum);
	                	$("#img").append("<img id='addPhoto' src='/images/ico_add_photo.png' width = '113px' height = '94px' onclick='personPhoto.click()'/>");
	                	//有格式不正确、文件名有特殊字符的照片，提示过滤信息
	                	if(errorNum > 0)
	                	{
	                		openMessage(msgType.warning, "<@i18n 'pers_import_filterTip'/>"+"<br>1.<@i18n 'pers_import_photoContainSpecialCharacters'/>"+"<br>2.<@i18n 'pers_import_photoFormatError'/>");
	                	}
					}
					else//取消替换照片
					{
						var noRepeatNum = 0;//确定替换图片，记录没有重复的数量
	                	var repeatNum = 0;//记录重复的数量
	                	var errorNum = 0;//记录类型不正确的数量
	                	for(var i=0; i< docObj.files.length; i++)
	                	{
	                	    var fileName = docObj.files[i].name.split(".");//获取文件名，不包含后缀
	                	    if(JSON.stringify(fileNameArr).indexOf(JSON.stringify(fileName[0])) == -1)
	                	    {
	                	    	if(getPhotoFormat(docObj.files[i]) && checkSpecialCharacters(docObj.files[i]))
								{
		                	    	previewPic(parseInt(photoIndex)+parseInt(i)-parseInt(repeatNum)-parseInt(errorNum), fileName, i, docObj);
	                	    		noRepeatNum ++;
								}
	                	    	else
	                	    	{
	                	    		errorNum ++;
	                	    	}
	                	    }
	                	    else
	                	    {
	                	    	repeatNum ++;
	                	    }
	                	}
	                	countPhotoNum += noRepeatNum;
	                	photoIndex += noRepeatNum;
	                	$("#photoNum").text(countPhotoNum);
	                	$("#img").append("<img id='addPhoto' src='/images/ico_add_photo.png' width = '113px' height = '94px' onclick='personPhoto.click()'/>");
	                	if(errorNum > 0)
	                	{
	                		openMessage(msgType.warning, "<@i18n 'pers_import_filterTip'/>"+"<br>1.<@i18n 'pers_import_photoContainSpecialCharacters'/>"+"<br>2.<@i18n 'pers_import_photoFormatError'/>");
	                	}
					}
				}
			});
    	}
	}
	// 不需要替换照片，限制未上传照片不大于3000张
	if(!isReplace && (parseInt(countPhotoNum) + parseInt(docObj.files.length) - parseInt(uploadNum)) > 3000)
	{
		openMessage(msgType.warning, "<@i18n 'pers_import_notUploadPhotoNumber'/>");
		$("#img").append("<img id='addPhoto' src='/images/ico_add_photo.png' width = '113px' height = '94px' onclick='personPhoto.click()'/>");
		return null;
	}
	if(!isReplace)
    {
		var correctNum = 0;
		var errorNum = 0;
    	for(var i=0; i< docObj.files.length; i++)
        {
    		if(getPhotoFormat(docObj.files[i]) && checkSpecialCharacters(docObj.files[i]))
    		{
    			var fileName = docObj.files[i].name.split(".");//获取文件名，不包含后缀
            	previewPic(parseInt(photoIndex) + parseInt(i) - parseInt(errorNum), fileName, i, docObj);
            	correctNum ++ ;
    		}  
    		else
    		{
    			errorNum ++;
    		}
        }
    	countPhotoNum += correctNum;
    	photoIndex += correctNum;
    	$("#photoNum").text(countPhotoNum);
    	if(document.getElementById("personPin"))
    	{
        	$("#img").append("<img id='addPhoto' src='/images/ico_add_photo.png' width = '113px' height = '94px' onclick='personPhoto.click()'/>");
    	}
    	//出现格式不正确或包含文件名包含特殊字符时提示
    	if(errorNum > 0)
    	{
    		openMessage(msgType.warning, "<@i18n 'pers_import_filterTip'/>"+"<br>1.<@i18n 'pers_import_photoContainSpecialCharacters'/>"+"<br>2.<@i18n 'pers_import_photoFormatError'/>");
    	}
    }
    if(!isReplace && $("#addPhoto").size() == 0)
    {
    	$("#midAddButton").show();
		$("#multipleTip").show();
    }
    return true;
}

//预览图片
function previewPic(i, fileName, selectImgIndex, docObj)
{
	html = "<div id='localImag"+i+"' style='position:relative; float:${leftRTL!'left'};text-align:center;margin-${rightRTL!'right'}:10px;'>"
	+ "<img id='deleteButton"+i+"' style='display:none; position:absolute; ${rightRTL!'right'}:0px;' onclick='removePhoto("+i+",\""+fileName[0]+"\")' onmouseenter='showDelButton("+i+")' onmouseleave='hideDelButton("+i+")'"
	+ "src='/images/ico_del.png' height='20px;' width='20px;'/>"
	+ "<div id='progressBar"+i+"' style='display:none'>"
	+ "<span id='failTip"+i+"' style='position:relative;width:113px;height:16px;text-align:center;line-height:16px;color:white'></span>"
	+ "<div id='totalProgressText"+i+"' style='position:relative;width:113px;height:16px;text-align:center;line-height:16px;color:white'></div></div>"
	+ "<div id='personPin' class='class_personPin"+i+"' style='position:absolute;bottom:0px;border:1px solid #eee;background:#FFF;text-align:center;width:113px;height:15px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;'>"+fileName[0]+"</div>"
	+ "<img id='id_img_pers"+i+"' class='class_img_pers"+fileName[0]+"' src='' onmouseenter='showDelButton("+i+")' onmouseleave='hideDelButton("+i+")'/></div>"
	$("#img").append(html);

	fileArr.push(docObj.files[selectImgIndex]);
	fileNameArr.push(fileName[0]);	

	var imgObjPreview = document.getElementById("id_img_pers"+i);
	if(docObj.files && docObj.files[selectImgIndex])
	{
		var fileType = fileName[1];
		if(fileType != null)
		{
			fileType = fileType.toLowerCase();
			if(fileType =='jpg' || fileType =='jpeg' || fileType =='png')
			{
				//火狐下，直接设img属性
				imgObjPreview.style.display = 'block';
				imgObjPreview.style.width = '113px';
				imgObjPreview.style.height = '94px';                    
				//火狐7以上版本不能用上面的getAsDataURL()方式获取，需要一下方式  
				imgObjPreview.src = window.URL.createObjectURL(docObj.files[selectImgIndex]);
			}
		}
	}
	else
	{
		//IE下，使用滤镜
    	docObj.select();
    	var imgSrc = document.selection.createRange().text;
    	var localImagId = document.getElementById("localImag"+i);
  		//必须设置初始大小
    	localImagId.style.width = "113px";
    	localImagId.style.height = "94px";
    	//图片异常的捕捉，防止用户修改后缀来伪造图片
		try
    	{
         	localImagId.style.filter="progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod=scale)";
         	localImagId.filters.item("DXImageTransform.Microsoft.AlphaImageLoader").src = imgSrc;
    	}
    	catch(e)
    	{
         	jAlert("<@i18n 'pers_person_msg3'/>","");
         	return false;
    	}
    	imgObjPreview.style.display = 'none';
    	document.selection.empty();
 	}
}

//隐藏删除图标
function hideDelButton(i)
{
	$("#deleteButton"+i).hide();
}

//显示删除图标
function showDelButton(i)
{
	$("#deleteButton"+i).show();
}

//删除选中的图标
function removePhoto(i,fileName)
{
	$("#localImag"+i).remove();
	var arrIndex = fileNameArr.indexOf(String(fileName));
	delete fileArr[arrIndex];
	delete fileNameArr[arrIndex];//删除预览照片对应的名称即人员编号
	countPhotoNum = parseInt(countPhotoNum)-parseInt(1)//图片剩余的数量
	$("#photoNum").text(countPhotoNum);
	if(!document.getElementById("personPin"))//拼接的图片内容为空时
	{
		$("#addPhoto").hide();
		$("#midAddButton").show();
		$("#multipleTip").show();
	}
}

//判断文件格式是否为图片类型
function getPhotoFormat(file)
{
    return /.*?(jpg|jpeg|png)$/i.test(file.name);
}

//判断文件名是否包含特殊字符
function checkSpecialCharacters(file)
{
	 var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？%\\\\]")
	 var fileName = file.name.substring(0,file.name.lastIndexOf(".")); 
	 return !pattern.test(fileName);
}

var failedName = new Array();
//开始上传
function startUpload()
{
	if(!document.getElementById("personPin"))//未选择照片时
	{
		openMessage(msgType.warning, "<@i18n 'pers_import_selectPhotoTip'/>");
		return;
	}
	var docObj = document.getElementById("personPhoto");//type=file对象
    if(uploadNum == photoIndex)//选择的照片已上传
    {
    	openMessage(msgType.warning, "<@i18n 'pers_import_addPhotoTip'/>");
		return;
    }
    var success = 0;//记录上传成功的数量
    var failed = 0;//记录上传失败的数量
    function _upload(i)//递归上传
    {
    	if(i == fileArr.length)
    	{
    		uploadNum = parseInt(uploadNum)+parseInt(fileArr.length);
			var resultDiv = "<div id='resultDiv' style='height:100%; overflow:auto;'>" +
							"<p style='margin-right:10px;'><@i18n 'common_succeed'/>:"+success+"</p>" +
							"<p><@i18n 'common_failed'/>:"+failed+"</p>";
			if(failedName.length > 0){
                resultDiv += "<@i18n 'common_prompt_wrong'/><@i18n 'common_prompt_title'/>:";
                var p = "<p style='color:red'>{0}. <@i18n 'pers_person_pin'/>: {1}&nbsp;&nbsp;{2}</p>";
                for (var j in failedName){
                    var f = failedName[j];
                    resultDiv += p.format(parseInt(j) + 1, f[0], f[1] ? f[1] : "<@i18n 'pers_import_photoNamePinNotCorrespond'/>");
                }
            }
            resultDiv += "</div>";
			resultDiv += '<script type="text/javascript">var currentWindow = DhxCommon.getCurrentWindow();' +
			'currentWindow.attachEvent("onClose", function(win) {var pre = DhxCommon.getPreWindow();if(pre){DhxCommon.closeWindow();}return true;});'+
            '<\/script>';

            fileArr = new Array();
            fileNameArr = new Array();
            failedName = new Array();
            var opts = {//弹窗配置对象
                width: 400,//设置弹窗宽度
                height: 250,//设置弹窗高度
                title: "<@i18n 'pers_import_uploadResult'/>",//设置弹窗标题
                html:resultDiv
            };
            DhxCommon.createWindow(opts);
            //导入结束之后刷新人员列表
            if(typeof afterImportPersPerson == "function"){
                afterImportPersPerson();
            }
            return;
    	}
    	var file = fileArr[i];//单个图片文件
	    if(typeof(file) != "undefined" || file != null)
	    {
            picSizeValid(file, i, function(filePic, index){
                // 5 * 1024 * 1024  文件限制在5M大小,超过之后不上传
                if (filePic.size > 5242880) {
                    $("#progressBar" + (parseInt(uploadNum) + parseInt(index))).show();
                    $("#progressBar" + (parseInt(uploadNum) + parseInt(index))).attr("style", "position:absolute;width:113px;height:16px;margin-top:30px;background:url('/images/progress_failed.png')");
                    $("#failTip" + (parseInt(uploadNum) + parseInt(index))).html("<@i18n 'common_failed'/>");
                    failed++;
                    failedName.push([filePic.name.split(".")[0], "<@i18n 'pers_import_fileMaxSize'/>"]);
                    _upload(++index);
                    return;
                }
                var formData = new FormData();
                formData.append("personPhoto", filePic);//文件对象
                formData.append("pin", filePic.name.replace(/\..*/,""));//定义上传的文件名为人员编号，去除文件后缀名即可
                formData.append("importPhoto", $("#${formId} input[name='importPhoto']").val());
                formData.append("importCropFace", $("#${formId} input[name='importCropFace']").val());
                formData.append("importTemplateFace", $("#${formId} input[name='importTemplateFace']").val());

                $.ajax({
                    url:"persPerson.do?savePhoto",
                    type:"post",
                    data: formData,
                    contentType: false,
                    processData: false,
                    async: false,
                    success: function (data)
                    {
                        if (data[sysCfg.ret] == sysCfg.success) {
                            $("#progressBar" + (parseInt(uploadNum) + parseInt(index))).show();
                            $("#progressBar" + (parseInt(uploadNum) + parseInt(index))).attr("style", "position:absolute;width:113px;height:16px;margin-top:30px;background:url('/images/progress_success.png')");
                            $("#totalProgressText" + (parseInt(uploadNum) + parseInt(index))).html("<@i18n 'common_succeed'/>");//设置成功提示
                            success++;
                        } else if (data[sysCfg.ret] == sysCfg.warning){
							$("#progressBar" + (parseInt(uploadNum) + parseInt(index))).show();
							$("#progressBar" + (parseInt(uploadNum) + parseInt(index))).attr("style", "position:absolute;width:113px;height:16px;margin-top:30px;background:url('/images/progress_success.png')");
							$("#totalProgressText" + (parseInt(uploadNum) + parseInt(index))).html("<@i18n 'common_succeed'/>");//设置成功提示
							success++;
							failedName.push([filePic.name.split(".")[0], data[sysCfg.msg]]);
						}
                        else {
                            $("#progressBar" + (parseInt(uploadNum) + parseInt(index))).show();
                            $("#progressBar" + (parseInt(uploadNum) + parseInt(index))).attr("style", "position:absolute;width:113px;height:16px;margin-top:30px;background:url('/images/progress_failed.png')");
                            $("#failTip" + (parseInt(uploadNum) + parseInt(index))).html("<@i18n 'common_failed'/>");
                            failed++;
                            failedName.push([filePic.name.split(".")[0], data[sysCfg.msg]]);
                        }
                        setTimeout(function () {
                            _upload(++index);
                        }, 1);
                    },
                    error: function (XMLHttpRequest, textStatus, errorThrown)
                    {
                        $("#progressBar" + (parseInt(uploadNum) + parseInt(index))).show();
                        $("#progressBar" + (parseInt(uploadNum) + parseInt(index))).attr("style", "position:absolute;width:113px;height:16px;margin-top:30px;background:url('/images/progress_failed.png')");
                        $("#failTip" + (parseInt(uploadNum) + parseInt(index))).html("<@i18n 'common_failed'/>");
                        failed++;
                        setTimeout(function () {
                            _upload(++index);
                        }, 1);
                    }
                });
            }, function(msg){
                $("#progressBar" + (parseInt(uploadNum) + parseInt(i))).show();
                $("#progressBar" + (parseInt(uploadNum) + parseInt(i))).attr("style", "position:absolute;width:113px;height:16px;margin-top:30px;background:url('/images/progress_failed.png')");
                $("#failTip" + (parseInt(uploadNum) + parseInt(i))).html("<@i18n 'common_failed'/>");
                failed++;
                failedName.push([file.name.split(".")[0], msg]);
                setTimeout(function () {
                    _upload(++i);
                }, 1);
            });
	    }
	    else
	    {
	    	setTimeout(function(){
    			_upload(++i);
			},1);
	    }
    }
    _upload(0);
}

function picSizeValid(filePic, index, callback, errorback){
    //读取图片数据
    var reader = new FileReader();
    reader.onload = function (e) {
        var data = e.target.result;
        //加载图片获取图片真实宽度和高度
        var image = new Image();
        image.onload = function(){
            var width = image.width;
            var height = image.height;
            // 1024 * 5
			if (parseInt(width)*parseInt(height) > (4000*4000)) {
				errorback(I18n.getValue("pers_person_picMaxSize").format("4000x4000"));
			} else if (parseInt(width)*parseInt(height) < (100*100)) {
                errorback(I18n.getValue("pers_person_picMinSize").format("100x100"));
            } else {
                callback(filePic, index);
            }
            return false;
        };
        image.src= data;
    };
    reader.readAsDataURL(filePic);
}

var currentWindow = DhxCommon.getCurrentWindow();
currentWindow.attachEvent("onClose", function(win) {
	setTimeout(function () {
        DhxCommon.closeWindow();
    },100)
	return true;
});
</script>

</#macro>

<#macro buttonContent>
	<button class='button-form' id="startUpload" onclick="startUpload()"><@i18n 'pers_import_startUpload'/></button>
    <button class='button-form' onclick="document.getElementById('personPhoto').click()"><@i18n 'pers_import_addMore'/></button>
    <button class='button-form' onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/></button>
</#macro>