<!-- 人员编辑下面的tabbar -->
<div id="personTabbar" class="dhx_cell_tabbar" style="height:340px;margin-top: 20px"></div>
<!-- 人员详细信息 -->
<div id="detailsDiv" style="overflow-y: auto;min-height: 250px;display: none">
    <table  class="tableStyle">
        <tbody class="customAttribute">
            <#if attributes?exists>
                <#list attributes as attr>
                    <#if attr_index % 2 == 0>
                    <tr>
                    </#if>
                        <th width="20%" style="word-break:break-all;vertical-align:middle">
                            <label>${attr.showName}</label>
                        </th>
                        <td width="30%" style="word-break:break-all;vertical-align:middle">
                            <#if (attr.controlType) == "text">
                                <input name="attrValue${attr.filedIndex}" attrName="${attr.attrName}" type="text" maxlength="100" value="${(attr.val)!}" unInputChar="true">
                            <#elseif (attr.controlType) == "select">
                                <@ZKUI.Combo id="attrValue${attr.filedIndex}${uuid}" hideLabel="true" width="148" empty="false" name="attrValue${attr.filedIndex}" attrName="${attr.attrName}">
                                    <option selected="selected" value="">----</option>
                                    <#list attr.attrValue?split(";") as val>
                                        <option <#if attr.val == val> selected="selected" </#if> value="${val}">${val}</option>
                                    </#list>
                                </@ZKUI.Combo>
                            <#else>
                                <div class="attr">
                                    <#list attr.attrValue?split(";") as val>
                                        <div class="attrVal" title="${val}">
                                            <label style="margin-left:2px;">
                                                <#if (","+attr.val+",")?index_of(","+val+",") != -1>
                                                <@ZKUI.Input type="${attr.controlType}" hideLabel="true" attrName="${attr.attrName}" checked="checked" name="attrValue${attr.filedIndex}" value="${val}"/>${val}
                                                <#else>
                                                <@ZKUI.Input type="${attr.controlType}" hideLabel="true" attrName="${attr.attrName}" name="attrValue${attr.filedIndex}" value="${val}"/>${val}
                                                </#if>
                                            </label>
                                        </div>
                                    </#list>
                                </div>
                            </#if>
                        </td>
                    <#if attr_index % 2 == 1>
                    </tr>
                    </#if>
                </#list>
            </#if>
        </tbody>
    </table>
</div>
<!-- 人员多卡信息 -->
<div id="multiCardDiv" style="display: none">
    <#if (item.cardNos)?split(",")?size < 2>
        <div class="multiCard">
            <label><@i18n 'pers_card_deputyCard'/></label>
            <input type="hidden" name="cardNos"/>
            <input type="text" name="multiCards_1" index="1" onblur="multiCardsChange(this);" onkeyup="javascript:this.value=this.value.replace(/^0+/,'');" onkeypress="return isNotSpace(event);"/>
            <span class="readCard icv-base_readCard1" id="multiCards_1" title="<@i18n 'pers_person_readCard'/>"></span>

            <span class="stopReadCard icv-base_stopReadCard" onclick="stopReadCard(this)" title="<@i18n 'pers_person_stopRead'/>"></span>
            <span class="delCard icv-ic_del" onclick="delCards(this)"></span>
            <span class="addCard icv-ic_add" onclick="addCards(this)"></span>
        </div>
    <#else>
        <#list cardItems as val>
            <#if val_index gt 0>
                <div class="multiCard">
                    <label><@i18n 'pers_card_deputyCard'/></label>
                    <input type="hidden" name="cardNos" value="${val}"/>
                    <input type="text" name="multiCards_${val_index}" index="${val_index}" value="${val.cardNo}" <#if val.isFrom=="ACMS">readonly="readonly"</#if> onblur="multiCardsChange(this);" onkeyup="javascript:this.value=this.value.replace(/^0+/,'');" onkeypress="return isNotSpace(event);"/>
                    <span class="readCard icv-base_readCard1" id="multiCards_${val_index}" title="<@i18n 'pers_person_readCard'/>"></span>
                    <span class="stopReadCard icv-base_stopReadCard" onclick="stopReadCard(this)" title="<@i18n 'pers_person_stopRead'/>"></span>
                    <span class="delCard icv-ic_del" onclick="delCards(this)"></span>
                    <span class="addCard icv-ic_add" onclick="addCards(this)"></span>
                </div>
            </#if>
        </#list>
    </#if>
</div>