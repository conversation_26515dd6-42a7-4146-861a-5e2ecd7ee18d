<script type='text/javascript'>
tabbar = new dhtmlXTabBar("personTabbar", "top");
var oldPersDeptId = "${(item.deptId)!treeDeptId!}",isPersEditMode="${(item.id)!}" != "";
var editPersonFormId = "#${formId}",isActive = false,moduleAuth="";
//onTabClick
tabbar.attachEvent("onSelect", function(id, lastId){
    $(tabbar.cells(id).cell).find("input[type='text']:first").focus();
    return true;
});
<@ZKUI.Permission name="pers:person:accEdit">
if ("${systemModules}".toLowerCase().indexOf("acc") != -1) {
    tabbar.addTab("accCode", "<@i18n 'pers_person_accSet'/>", "*");
    tabbar.tabs("accCode").attachURL("/accPerson.do?edit&personId=${item.id}", true);
    tabbar.tabs("accCode").setActive();
    isActive = true;
    moduleAuth += "acc,"
}
</@ZKUI.Permission>

<@ZKUI.Permission name="pers:person:attEdit">
if ("${systemModules}".toLowerCase().indexOf("att") != -1) {
    tabbar.addTab("attCode", "<@i18n 'pers_person_attSet'/>", "*");
    tabbar.tabs("attCode").attachURL("/attPerson.do?edit&personId=${item.id}", true);
    moduleAuth += "att,"
}
</@ZKUI.Permission>

<@ZKUI.Permission name="pers:person:eleEdit">
if ("${systemModules}".toLowerCase().indexOf("ele") != -1) {
    tabbar.addTab("eleCode", "<@i18n 'pers_person_eleSet'/>", "*");
    tabbar.tabs("eleCode").attachURL("/elePerson.do?edit&personId=${item.id}", true);
    moduleAuth += "ele,"
}
</@ZKUI.Permission>

<@ZKUI.Permission name="pers:person:parkEdit">
if ("${systemModules}".toLowerCase().indexOf("park") != -1) {
    tabbar.addTab("parkCode", "<@i18n 'pers_person_parkSet'/>", "*");
    tabbar.tabs("parkCode").attachURL("/parkPerson.do?edit&personId=${item.id}", true);
    moduleAuth += "park,"
}
</@ZKUI.Permission>

<@ZKUI.Permission name="pers:person:pidEdit">
if ("${systemModules}".toLowerCase().indexOf("pid") != -1) {
    tabbar.addTab("pidCode", "<@i18n 'pers_person_pidSet'/>", "*");
    tabbar.tabs("pidCode").attachURL("/pidPerson.do?edit&personId=${item.id}", true);
    moduleAuth += "pid,"
}
</@ZKUI.Permission>

<@ZKUI.Permission name="pers:person:psgEdit">
if ("${systemModules}".toLowerCase().indexOf("psg") != -1) {
    tabbar.addTab("psgCode", "<@i18n 'pers_person_psgSet'/>", "*");
    tabbar.tabs("psgCode").attachURL("/psgPerson.do?edit&personId=${item.id}", true);
    moduleAuth += "psg,"
}
</@ZKUI.Permission>

<@ZKUI.Permission name="pers:person:insEdit">
if ("${systemModules}".toLowerCase().indexOf("ins") != -1) {
    tabbar.addTab("insCode", "<@i18n 'pers_person_insSet'/>", "*");
    tabbar.tabs("insCode").attachURL("/insPerson.do?edit&personId=${item.id}", true);
    moduleAuth += "ins,"
}
</@ZKUI.Permission>

<@ZKUI.Permission name="pers:person:lockerEdit">
if ("${systemModules}".toLowerCase().indexOf("locker") != -1) {
    tabbar.addTab("lockerCode", "<@i18n 'pers_person_lockerSet'/>", "*");
    tabbar.tabs("lockerCode").attachURL("lockerDevice.do?editPersType&personId=${item.id}", true);
    moduleAuth += "locker,"
}
</@ZKUI.Permission>

<@ZKUI.Permission name="pers:person:sisEdit">
if ("${systemModules}".toLowerCase().indexOf("sis") != -1) {
    tabbar.addTab("sisCode", "<@i18n 'pers_person_sisSet'/>", "*");
    tabbar.tabs("sisCode").attachURL("/sisPerson.do?edit&personId=${item.id}", true);
    moduleAuth += "sis,"
}
</@ZKUI.Permission>

<@ZKUI.Permission name="pers:person:vdbEdit">
if ("${systemModules}".toLowerCase().indexOf("vdb") != -1) {
    tabbar.addTab("vdbCode", "<@i18n 'pers_person_vdbSet'/>", "*");
    tabbar.tabs("vdbCode").attachURL("vdbPerson.do?edit&personId=${item.id}&defPin=${defPin!}", true);
    moduleAuth += "vdb,"
}
</@ZKUI.Permission>


//启用多卡
if(persParams['pers.cardsSupport'] == "true") {
    tabbar.addTab("multiCard","<@i18n 'pers_card_multiCard'/>","*");
    tabbar.tabs("multiCard").attachObject("multiCardDiv",true);
}

<#if attributes?exists && (attributes?size>0)>
tabbar.addTab("detail","<@i18n 'pers_person_detailInfo'/>","*");
tabbar.tabs("detail").attachObject("detailsDiv",true);
if (!isActive){
    tabbar.tabs("detail").setActive();
}
</#if>

tabbar.attachEvent("onContentLoaded", function(id){
    if(!isPersEditMode){//非编辑模式下加载权限组
        if("accCode" == id && typeof(loadDeptAccLevel) != "undefined")
        {
            //loadDeptAccLevel(oldPersDeptId);
        }
        if("eleCode" == id && typeof(loadDeptEleLevel) != "undefined")
        {
            //loadDeptEleLevel(oldPersDeptId);
        }
    }
    if("payrollCode" == id && typeof(initPayrollValid) != "undefined")
    {
        initPayrollValid();
    }
});
(function(){
    if(isPersEditMode){//编辑模式下
        //禁用人员pin
        $(editPersonFormId+" input[name='pin']").attr("readonly","readonly");
    }
    $(editPersonFormId+" input[name='moduleAuth']").val(moduleAuth);
    window.setTimeout(function(){
        $(editPersonFormId+" input[name='name']").removeAttr("readonly");
    },50);

    $(editPersonFormId+" #checkMail").change(function(){
        $(editPersonFormId+" input[name='isSendMail']").val($(this).is(":checked"));
        if($(this).is(":checked")){
            $.get("/persPerson.do?checkMailParam",function(data){
                    if (!data)
                    {
                    <@ZKUI.Permission name="system:mail:getMailParam">
                        DhxCommon.createWindow("/baseMail.do?getMailParam^0^0^600^520^<@i18n 'base_mail_sendServerSetting'/>");
                    </@ZKUI.Permission>
                    }
                }
            );
        }
        $(editPersonFormId+" input[name='mail']").valid();
    });

    $(editPersonFormId+" #checkSMSModem").change(function(){
        $(editPersonFormId+" input[name='sendSMS']").val($(this).is(":checked"));
        if($(this).is(":checked")){
            $.get("persPerson.do?checkSMSModemParam",function(data){
                    if (!data)
                    {
                    <@ZKUI.Permission name="smsModem:message:paramSetting">
                        DhxCommon.createWindow("/smsModemMessage.do?getSmsModemMessageParam^0^0^500^520^<@i18n 'smsModem_message_paramSetting'/>");
                    </@ZKUI.Permission>
                    }
                }
            );
        }
        $(editPersonFormId+" input[name='mobilePhone']").valid();
        $(editPersonFormId+" input[name='areaCode']").valid();
    });

    $(editPersonFormId+" #checkWhatsApp").change(function(){
        $(editPersonFormId+" input[name='sendWhatsapp']").val($(this).is(":checked"));
        if($(this).is(":checked")){
            $.get("persPerson.do?checkWhatsappParam",function(data){
                    if (!data)
                    {
                        messageBox({messageType:"alert",text:"<@i18n 'base_whatsapp_setTip'/>", callback: function(result){
                                $("#checkWhatsApp").attr("checked", false);
                                $(editPersonFormId+" input[name='sendWhatsapp']").val($("#checkWhatsApp").is(":checked"));
                                $(editPersonFormId+" input[name='whatsappMobileNo']").siblings(".ts_box").remove();
                                $(editPersonFormId+" input[name='whatsappMobileNo']").valid();
                        }});
                    }
                }
            );
        }
        $(editPersonFormId+" input[name='whatsappMobileNo']").siblings(".ts_box").remove();
        $(editPersonFormId+" input[name='whatsappMobileNo']").valid();
    });

    initBioTemplate();

    DhxCommon.getCurrentWindow().attachEvent("onClose", function(){
        closeReaderTimeout();
        // 若开启高拍仪进行ocr识别，关闭窗口时关闭高拍仪
        if(typeof closeHighDevice == "function")
        {
            closeHighDevice();
        }
        return true;
    });
})();
</script>