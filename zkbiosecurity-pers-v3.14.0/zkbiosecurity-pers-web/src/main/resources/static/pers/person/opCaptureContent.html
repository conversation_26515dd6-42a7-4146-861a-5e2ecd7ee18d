<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<script src="js/baseUsbCameraWebSocket.js"></script>

<form method="post" id="tet" action="" onkeydown="if(event.keyCode==13){return false;}">
    <div id="visitorInput" style="height: 100%; overflow: visible;">
        <div id="driver_tip_div" style="width:100%;height:30px;padding-top: 10px;background:rgb(242, 221, 13);font-size: large; color:#000;text-align: center;display: none;">
            <span id="driver_tip_msg" style="width:100%;"></span>
        </div>

        <!-- '拍照' -->
        <div id="captureDiv" style="height: 100%; overflow: visible; padding-top: 5px;padding-${leftRTL!'left'}: 5px;display:inline;">
            <div id="headPortraitTab" style="display: flex;justify-content: center;">

                <table>
                    <tr>
                        <td style="padding-${leftRTL!'left'}: 18px;">
                            <div id="preview" style="padding-top: 2px;padding-${leftRTL!'left'}: 15px;padding-bottom:1px;width:120px;height:140px;">
                                <img id="id_capturePhoto_img"  width="120px" height="140px" src="/images/${attr('system.skin','')}/userImage.gif" onerror="this.src='/images/userImage.gif'"/>
                            </div>
                            <p align="center" style="margin-bottom:18px"><@i18n 'pers_capture_catchPhoto'/></p>
                            <input  id="capturePhoto"  name="capturePhoto" type="hidden" value=""/>
                        </td>
                        <td style="padding-${leftRTL!'left'}: 25px;">
                            <div id="id_cap_div" class="capture" style='margin-top: 2px;margin-bottom:5px;text-align: ${leftRTL!'left'};background:url(images/PC/${attr('system.skin','')}/noCamera.png) center no-repeat;width:187px;height:140px;">
                                <span class='warningImage' style='width:18px;'></span><p class='warningColor' style='padding-top:1px;'><@i18n 'common_camera_opening'/></p>
                            </div>
                            <div class="bottomDivR" style="margin-top:5px;padding-bottom:2px;" >
                                <input type="button" id="capture"  class="button-form" value="<@i18n 'pers_op_capture'/>" />
                            </div>
                            <div id="border"></div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    <div style="clear: both;"></div>
    <div style="display: flex;justify-content: center;align-items: center;">
        <label style="padding-${rightRTL!'right'}: 25px;"><@i18n 'pers_person_selectCamera' /></label>
        <@ZKUI.Combo id="usbDevice" name="usbDevice" hideLabel="true">
        </@ZKUI.Combo>
    </div>
    </div>
</form>
<div class="bottomDiv bottomDivR" id="BottomDiv" style="margin-top: 10px;z-index: 200" >
    <button type="button" id="submitButtonId" name="makeSureName" onclick="submitEvent()" class="button-form">
        <@i18n 'common_edit_ok'/>
    </button>
    <button class="button-form" type="button" id="closeButton" name="closeButton" onclick="DhxCommon.closeWindow();">
        <@i18n 'common_edit_cancel'/>
    </button>
    <div id="captureDevice" style="text-align: center; margin-top: 8px;"></div>
</div>
<script type="text/javascript">
    $().ready(function(){
        var issInstall = "";
        ZKDevice.get(ZKDevType.ScanInfo,function(IssControls){
            if(IssControls.installed)//已安装
            {
                if(IssControls.upgrade)//需要升级
                {
                     <#if "${Application['system.osType']!'window'}" == "window">
                    issInstall = "<span class='warningColor'><@i18n 'common_issOnline_needUpgrade' />".format("<@i18n 'common_issOnline_deviceDriver' />") + "</span><a href="+IssControls.url+" class='devWarningColor'><@i18n 'common_issOnline_driverDownload' /></a>";
                    <#elseif "${Application['system.osType']!'window'}" == "linux">
                         issInstall = "<span><@i18n 'common_issOnline_needUpgrade' />".format("<@i18n 'common_issOnline_deviceDriver' />") + "</span><a><@i18n 'common_issOnline_driverDownload'/>(<a href="+IssControls.url+">Windows</a>, <a href="+IssControls.linuxUrl+">Linux</a>)</a>";
                    </#if>
                }
                else
                {
                    issInstall = "<span class='warningColor'><@i18n 'common_issOnline_installedProperly' />".format("<@i18n 'common_issOnline_deviceDriver' />") + "</span>";
                }
            }
            else
            {
             <#if "${Application['system.osType']!'window'}" == "window">
                issInstall = "<span class='warningColor'><@i18n 'common_issOnline_notInstalled' />".format("<@i18n 'common_issOnline_deviceDriver' />") + "</span><a href="+IssControls.url+" class='devWarningColor'><@i18n 'common_issOnline_driverDownload' /></a>";
             <#elseif "${Application['system.osType']!'window'}" == "linux">
                 issInstall = "<span><@i18n 'common_issOnline_notInstalled' />".format("<@i18n 'common_issOnline_deviceDriver' />") + "</span><a><@i18n 'common_issOnline_driverDownload' />(<a href="+IssControls.url+">Windows</a>, <a href="+IssControls.linuxUrl+">Linux</a>)</a>";
             </#if>
            }
            $("#captureDevice").html(issInstall);
        });


        var paramArray = new Array();
        var photoParams =
        {
            container:"id_cap_div",
            imgPhoto:"id_cap_div_img",
            captureBorder:"border",
            containerWidth:"187",
            containerHeight:"140",
            previewWidth:"120",
            previewHeight:"140",
            usbDevice:"usbDevice",
            cookieUsbDevice:"persUsbDevice",
            defaultUsbDevice:true,
            openCamera:false,
            disableCamera:true
        }
        paramArray.push(photoParams);
        usbCameraWebsocketConnect(paramArray);
        $("#capture").click(function()
        {
            savePhoto("id_cap_div_img","id_capturePhoto_img","capturePhoto");
        });
        ZKUI.Combo.get("usbDevice").combo.attachEvent("onChange", function (value, text) {
            CloseCamera();
            if(ZKUI.Combo.get("usbDevice").combo.getSelectedValue() != "")
            {
                OpenCamera(value);
                $.cookie('persUsbDevice', value, { expires: 7 });
            }
        });
    });

    function submitEvent(){
        imgData = $("#capturePhoto").val();
        if(imgData!="")
        {
            $("#id_img_pers").attr("src","data:image/jpeg;base64,"+imgData);
            $("#personIdPhoto").val(imgData);
            $("#personPhoto").val("");
            validPersonPhoto();
        }
        DhxCommon.closeWindow();
    }

    function savePhoto(containerImg,id,base64)
    {
        var img = document.getElementById(containerImg);
		var canvas = document.createElement('canvas');
		canvas.width = 480;
		canvas.height = 640;
		context = canvas.getContext("2d");

		var sheight = 480 - 10;
		var swidth = sheight * 120/140;
		var sx = (640 - swidth)/2;
		var sy = 5;
        context.drawImage(img,sx,sy,swidth,sheight,0,0,480,640);
        var imgData = canvas.toDataURL("image/jpeg");
        var img = document.getElementById(id);//创建新的img标签保存图片
        img.src = imgData;
		var base64String = imgData.substr(23); //取得base64字串
		$("#capturePhoto").val(base64String);
    }

</script>
