<#assign editPage = "true">
<#if Application['system.maxExportCount']??>
 <#assign maxExportCount = "${Application['system.maxExportCount']!'30000'}">
</#if>
<#if !maxExportCount??>
 <#assign maxExportCount = "30000">
</#if>
<#include "/public/template/editTemplate.html">
<#macro editContent>
<style>
.persAttrArea{
	clear: both;
}
.tableStyle tr{
	line-height: 25px;
}
.persAttr{
	float: ${leftRTL!'left'};
	width:114px;
}
.persAttr label {
	overflow: hidden;
	word-break: keep-all;
	width: 90%;
	white-space: nowrap;
	display: inline-block;
	text-overflow: ellipsis;
}
.persAttr input[type='checkbox']{
	margin-${rightRTL!'right'}: 5px;
}
</style>
<script type="text/javascript">
$(function(){
	var gridName = "${gridName}";
	var grid = ZKUI.Grid.get(gridName);
	var fieldData = grid.getDisplayField();
	var tableNameParam = "${tableNameParam}";
	document.getElementById("jsonColumn${uuid}").value = JSON.stringify(fieldData.columns);
	document.getElementById("queryConditions${uuid}").value = fieldData.qc;
	document.getElementById("records${uuid}").value = fieldData.records;
	document.getElementById("treeId${uuid}").value = grid.options.query;
	document.getElementById("pageXmlPath${uuid}").value = grid.options.vo;
	document.getElementById("sortName${uuid}").value = grid.grid.col_name||"";
	document.getElementById("sortOrder${uuid}").value = grid.grid.sort_order||"";
	if(tableNameParam)
	{
		$("#tableNameParam${uuid}").val(tableNameParam);
	}
	else
	{
		if(window.top.system.tabbar)
		{
			$("#tableNameParam${uuid}").val(window.top.system.tabbar.cells(window.top.system.tabbar.getActiveTab()).getText());
		} else {
			var c = CONST.getValue("sysCode");
			var a = CONST.getValue(c + "_accId");
			var m = CONST.getValue(c + "_menuId");
			$("#tableNameParam${uuid}").val(system.myAcc.cells(a).dataObj.getItemText(m));
		}
	}
	$("#${formId}").prop("action", "${actionName}");

	//设置双列表的文件名与表名
	if(grid.options.title) {
		document.getElementById("tableNameSearch${uuid}").value = grid.options.title;
	}
	// 导出格式选择
	var typeStr = "${exportTypes}";
	if (typeStr)
	{
	    var typeArr = typeStr.split("|");
	    for (var i=0;i<typeArr.length;i++)
	    {
	    	if(typeArr[i]) {
	    		$("#reportType${uuid}").find("option[value="+typeArr[i]+"]").remove();
	    	}
	    }
	}
	var opt = DhxCommon.getCurrentWindow().opts;
	if(opt.reportType) {
		if(typeof(opt.reportType) == "string") {
			opt.reportType = eval("(" + opt.reportType+ ")");
		}
		if(Array.isArray(opt.reportType) && opt.reportType.length>0) {
			$("#reportType${uuid}").empty();
			var h = "";
			for(var i=0; i<opt.reportType.length; i++) {
				if(typeof(opt.reportType[i])=="object") {
					h =h + "<option value='" + opt.reportType[i].value +"'>" + opt.reportType[i].text + "</option>";
				} else {
					h =h + "<option value='" + opt.reportType[i] +"'>" + opt.reportType[i] + "</option>";
				}
			}
			$("#reportType${uuid}").html(h);
		}
	}
});
</script>

<form method="POST" action="" id="${formId}" enctype="multipart/form-data">
	<input style="display: none" name="jsonColumn" id="jsonColumn${uuid}"/>
	<input style="display: none" name="pageXmlPath" id="pageXmlPath${uuid}"/>
	<input style="display: none" name="queryConditions" id="queryConditions${uuid}"/>
	<input style="display: none" name="tableNameParam" id="tableNameParam${uuid}"/>
	<input style="display: none" name="tableNameSearch" id="tableNameSearch${uuid}"/>
	<input style="display: none" name="treeId" id="treeId${uuid}"/>
	<input type="hidden" name="records" id="records${uuid}"/>
	<input style="display: none" name="sortName" id="sortName${uuid}"/>
	<input style="display: none" name="sortOrder" id="sortOrder${uuid}"/>
	<input type="hidden" name="maxExportCount" id="maxExportCount${uuid}" value="${maxExportCount!'30000'}"/>
	<input style="display: none" name="customAttrs" id="customAttrs${uuid}"/>
	<table class="tableStyle">
		<tbody>
		<tr class="">
			<td width="120px"><label title="<@i18n 'base_certType_selectAll'/> / <@i18n 'base_certType_unselectAll'/>">
				<@ZKUI.Input hideLabel="true" type="checkbox" class="selectAll" name="selectAll" value="basicAttrArea${uuid}" checked="checked"/><@i18n 'pers_export_basicInfo'/></label></td>
			<td>
				<div id="basicAttrArea${uuid}" class="persAttrArea">
					<div class="persAttr">
						<label title="<@i18n 'pers_person_pin'/>">
							<input type="hidden" name="attrs" value="<@i18n 'pers_person_pin'/>-pin">
								<@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_pin')}-pin" checked="checked" disabled="disabled"/>
							<@i18n 'pers_person_pin'/>
						</label>
					</div>
					<div class="persAttr">
						<label title="<@i18n 'pers_person_name'/>">
							<input type="hidden" name="attrs" value="<@i18n 'pers_person_name'/>-name">
								<@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_name')}-name" checked="checked" disabled="disabled"/>
							<@i18n 'pers_person_name'/>
						</label>
					</div>
					<#if "${Application['system.language']}" != "zh_CN">
					<div class="persAttr">
						<label title="<@i18n 'pers_person_lastName'/>">
							<input type="hidden" name="attrs" value="<@i18n 'pers_person_lastName'/>-lastName">
								<@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_lastName')}-lastName" checked="checked" disabled="disabled"/>
							<@i18n 'pers_person_lastName'/>
						</label>
					</div>
				</#if>
				<div class="persAttr">
					<label title="<@i18n 'pers_dept_deptNo'/>">
						<input type="hidden" name="attrs" value="<@i18n 'pers_dept_deptNo'/>-deptCode">
								<@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_dept_deptNo')}-deptCode" checked="checked" disabled="disabled"/>
						<@i18n 'pers_dept_deptNo'/>
					</label>
				</div>
				<div class="persAttr">
					<label title="<@i18n 'pers_dept_deptName'/>">
						<input type="hidden" name="attrs" value="<@i18n 'pers_dept_deptName'/>-deptName">
								<@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_dept_deptName')}-deptName" checked="checked" disabled="disabled"/>
						<@i18n 'pers_dept_deptName'/>
					</label>
				</div>
						<div class="persAttr"><label title="<@i18n 'pers_person_gender'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_gender')}-gender"/><@i18n 'pers_person_gender'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_person_birthday'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_birthday')}-birthday"/><@i18n 'pers_person_birthday'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_person_mobilePhone'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_mobilePhone')}-mobilePhone"/><@i18n 'pers_person_mobilePhone'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_card_cardNo'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_card_cardNo')}-cardNos"/><@i18n 'pers_card_cardNo'/></label></div>
				<!-- 存在停车场模块显示车牌导出选项 -->
				<#if systemModules?lower_case?index_of("park")!=-1>
						<div class="persAttr"><label title="<@i18n 'pers_person_carPlate'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_carPlate')}-carPlate"/><@i18n 'pers_person_carPlate'/></label></div>
				</#if>
						<div class="persAttr"><label title="<@i18n 'pers_person_email'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_email')}-email"/><@i18n 'pers_person_email'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_cert_type'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_cert_type')}-certName"/><@i18n 'pers_cert_type'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_cert_number'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_cert_number')}-certNumber"/><@i18n 'pers_cert_number'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_position_code'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_position_code')}-positionCode"/><@i18n 'pers_position_code'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_position_name'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_position_name')}-positionName"/><@i18n 'pers_position_name'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_cardTemplate_entryDate'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_cardTemplate_entryDate')}-hireDate"/><@i18n 'pers_cardTemplate_entryDate'/></label></div>
						<!-- 存在可视对讲模块显示楼栋、单元、房号三个导出选项 -->
						<#if systemModules?lower_case?index_of("vdb")!=-1>
						<div class="persAttr"><label title="<@i18n 'pers_person_building'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_building')}-buildingName"/><@i18n 'pers_person_building'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_person_unitName'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_unitName')}-unitName"/><@i18n 'pers_person_unitName'/></label></div>
						<div class="persAttr"><label title="<@i18n 'pers_person_roomNo'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('pers_person_roomNo')}-roomNo"/><@i18n 'pers_person_roomNo'/></label></div>
					</#if>
						<div id="verifyModeDiv" class="persAttr"><label title="<@i18n 'common_verifyMode_entiy'/>"><@ZKUI.Input hideLabel="true" type="checkbox" name="attrs" value="${getI18nByCode('common_verifyMode_entiy')}-verifyMode"/><@i18n 'common_verifyMode_entiy'/></label></div>
			</div>
			</td>
		</tr>
		<tr class="">
			<td><label title="<@i18n 'base_certType_selectAll'/> / <@i18n 'base_certType_unselectAll'/>">
					<@ZKUI.Input hideLabel="true" type="checkbox" class="selectAll" name="selectAll" value="customAttrArea${uuid}"/><@i18n 'pers_export_customAttr'/></label></td>
			<td>
					<div id="customAttrArea${uuid}" class="persAttrArea">
						<#if customAttrsMap??>
							<#list customAttrsMap?keys as key>
								<div class="persAttr">
									<label title="${customAttrsMap[key]}">
										<@ZKUI.Input type="checkbox" hideLabel="true" name="attrs" checked="false" value="${customAttrsMap[key]}-attrMap.attrValue${key}"/>
										${customAttrsMap[key]}
									</label>
								</div>
							</#list>
						</#if>
					</div>
			</td>
		</tr>
		<tr><td></td></tr>
		<tr id="loginPwdRow" hidden="hidden">
			<td><@i18n 'auth_user_userPwd'/><span class="required">*</span></td>
			<td>
				<div style="position:relative;">
					<input type="password" maxlength="18" id="loginPwd"/>
					<input type="hidden"  name="loginPwd" id="password_hidden"/>
				</div>
			</td>
		</tr>
		<!-- 新增行，启用加密导出（设置密码导出） add by sevenidea.lu 2021-4-1  -->
		<tr class="encryptData">
			<td width="120px"><@i18n 'common_file_encrypt' /></td>
			<td>
				<label><@ZKUI.Input hideLabel="true" name="isEncrypt" id="yes" type="radio" onchange="inputPassword()" value="1" checked="checked"/>
					<span><@i18n 'common_yes' /></span></label>
				<label><@ZKUI.Input hideLabel="true" name="isEncrypt" id="no" type="radio" onchange="inputPassword()" value="0" />
					<span><@i18n 'common_no' /></span></label>
			</td>
		</tr>
		<tr class="passwordCheck">
			<td><@i18n 'common_file_encrypt_pwd'/><span class="required">*</span></td>
			<td>
				<div style="position:relative;">
					<input type="password" maxlength="18" id="encryptPassword" name="encryptPassword"/>
					<span class="export-input-eye"></span>
				</div>
			</td>
		</tr>
		<tr>
			<td><@i18n 'common_report_fileType'/></td>
			<td>
				<@ZKUI.Combo hideLabel="true" width="148" empty="false"  name="reportType" id="reportType${uuid}" followType="close" followScroll=".content_div">
				</@ZKUI.Combo>
			</td>
		</tr>
		<tr class="exportData">
			<td><@i18n 'common_report_exportType'/></td>
			<td>
				<div>
					<label>
					<@ZKUI.Input hideLabel="true" type="radio" checked="checked" value="1" name="exportType"/>
					<@ZKUI.Format text="common_report_allRecordAtMostx">
						<@ZKUI.Param>${maxExportCount!'30000'}</@ZKUI.Param>
					</@ZKUI.Format>
					</label>
				</div>
			<div id="id_div_choicerecords${uuid}" style="margin: 5px 0px;">
				<ul>
					<li id="id_li_records${uuid}" class="clearB">
						<label>
						<@ZKUI.Input hideLabel="true" type="radio" value="3" name="exportType"/>
						<@ZKUI.Format text="common_report_selCountToExportx">
							<@ZKUI.Param>${maxExportCount!'30000'}</@ZKUI.Param>
						</@ZKUI.Format>
						</label>
				<ul style="margin: 3px 3px 3px 16px;">
					<li id="selectCount${uuid}">
						<table>
							<tr>
								<td><@i18n 'common_report_selCount'/></td>
								<td><input type="text" maxlength="8" size="4" name="recordstart" value="1" style="width:45px" disabled="disabled"></td>
							</tr>
							<tr>
								<td><@i18n 'common_report_totalCount'/></td>
								<td><input type="text" maxlength="${(maxExportCount?string)?length}" size="4" name="recordcount" value="100" style="width:45px" disabled="disabled"></td>
							</tr>
						</table>
					</li>
				</ul>
				</li>
				</ul>
			</div>
			</td>
		</tr>
		</tbody>
	</table>
</form>
</#macro>
<script type="text/javascript">
function downloadForExport(url, params, fileName, finishFun, processFun) {
	var formData = new FormData();
	if(params) {
		for (var i in params) {
			for (var key in params[i]) {
				formData.append(key, params[i][key]);
			}
		}
	}
	if($.browser && $.browser.token) {
		formData.append("browserToken", $.browser.token);
		if(localStorage.getItem("passToken")) {
			formData.append("passToken", localStorage.getItem("passToken"));
        }
	}
	var xhr = new XMLHttpRequest();
	xhr.open('POST', url, true);    // 也可用POST方式
	xhr.responseType = "blob";
	xhr.onload = function () {
		if (this.status === 200) {
			var content = this.response;
			var headerName = xhr.getResponseHeader("Content-disposition");
			if(headerName == null || headerName == '' || headerName == undefined) {
				var reader = new FileReader();
				reader.readAsText(content, 'utf-8');
				reader.onload = function (e) {
					if(reader.result && reader.result.indexOf("{")==0) {
						openMessage(msgType.error,JSON.parse(reader.result).msg);
					} else {
						openMessage(msgType.error,"<@i18n 'common_report_exportFaild'/>");
					}
				}
				return;
			}
			headerName = decodeURIComponent(headerName);
			var fn = headerName.substring(headerName.indexOf("_") + 1, headerName.length - (headerName.charAt(headerName.length-1) === "\"" ? 1 : 0));
			fn = fileName + "_" + fn;
			if (navigator.msSaveBlob == null) {
				var aTag = document.createElement('a');
				var blob = new Blob([content]);
				aTag.download = fn;
				aTag.href = URL.createObjectURL(blob);
				$("body").append(aTag);    // 修复firefox中无法触发click
				aTag.click();
				URL.revokeObjectURL(blob);
				$(aTag).remove();
			} else {
				navigator.msSaveBlob(content, fn);
			}
		}
	};

	if(typeof finishFun == "function") {
		xhr.onloadend = finishFun;
	}
	if(typeof processFun == "function") {
		xhr.onprogress = processFun;
	}
	xhr.send(formData);
}

$().ready(function(){
	function getExportResult() {
		$.ajax({
			url:"skip.do?getExportResult",
			success: function(res) {
				if(res[sysCfg.data]=="end") {
					closeMessage();
				} else {
					setTimeout(getExportResult, 3000);
				}
			}
		});
	}

	function getTableName() {
		var t = $("#tableNameSearch${uuid}").val();
		if(!t) {
			t = $("#tableNameParam${uuid}").val();
		}
		return t;
	}


	// 导出格式选择
	var typeStr = "${typeStr}";
	var typeOptions;
	if (!typeStr||typeStr=="")
	{
		typeOptions = new Array({text:"<@i18n 'common_report_excel'/>", value:"XLS"},
			{text:"<@i18n 'common_report_pdf'/>", value:"PDF"},
			{text:"<@i18n 'common_report_csv'/>", value:"CSV"});

	}
	else
	{
	    var typeArr = typeStr.split("|");
	    for (var i=0;i<typeArr.length;i++)
	    {
	        if(typeArr[i]=="XLS")
	        {
				typeOptions = new Array({text:"<@i18n 'common_report_excel'/>", value:"XLS"});
	        }
	        else if(typeArr[i]=="PDF")
            {
				typeOptions = new Array({text:"<@i18n 'common_report_pdf'/>", value:"PDF"});
            }
	        else if(typeArr[i]=="CSV")
            {
				typeOptions = new Array({text:"<@i18n 'common_report_csv'/>", value:"CSV"});
            }
	    }
		$(".exportData").hide();
		$(".encryptData").hide();
		$("#loginPwdRow").hide();
		$(".passwordCheck").hide();
		$("#verifyModeDiv").hide();
		$("#verifyModeDiv").html("");
	}
	ZKUI.Combo.get('reportType${uuid}').combo.clearAll();
	ZKUI.Combo.get('reportType${uuid}').addOption(typeOptions);
	ZKUI.Combo.get('reportType${uuid}').combo.selectOption(0);
	$("#${formId}").validate({
		debug : true,
		rules: {
			"recordcount":{
				required:true,
				regExp:"^(-)?[\\d]+$",
				min:1,
				max:parseInt("${maxExportCount!'30000'}")
			},
			"recordstart":{
				required:true,
				regExp:"^(-)?[\\d]+$",
				min:0,
				recordStartValid:true
			}
		},
		messages:{
			"recordcount":{
				required:"<@i18n 'common_report_exportTypeNull'/>",
				regExp:"<@i18n 'common_report_exportTypeError'/>",
				min:"<@i18n 'common_report_dataSourceNull'/>",
				max:"<@ZKUI.Format text='common_report_maxCountx'><@ZKUI.Param>${maxExportCount!'30000'}</@ZKUI.Param></@ZKUI.Format>"
			},
			"recordstart":{
				required:"<@i18n 'common_report_exportTypeNull'/>",
				regExp:"<@i18n 'common_report_exportTypeError'/>",
				min:"<@i18n 'common_report_fromIndexNotZero'/>",
			},
			"encryptPassword" : {
				noSpace:"<@i18n 'auth_user_noSpaceMsg'/>"
			}
		},
		submitHandler: function(form){
			openMessage(msgType.loading);
			document.getElementById("queryConditions${uuid}").disabled=true;
			var params = ZKParseQuery(decodeURIComponent($("#${formId}").serialize()));
			document.getElementById("queryConditions${uuid}").disabled=false;
			var data = {queryConditions: document.getElementById("queryConditions${uuid}").value};
			params.push(data);
			downloadForExport("${actionName!}", params, getTableName(), function() {
				if(this.response.type.indexOf("json")!=-1) {
					return;
				}
				setTimeout(function() {
					DhxCommon.closeWindow();
				}, 3000);
			});
			setTimeout(function() {
				getExportResult();
				$("iframe[id^=jqFormIO]").remove();
			}, 2000);
		}
	});

	function ZKParseQuery(b) {
		var a = /([^=&\s]+)[=\s]*([^&\s]*)/g;
		var c = [];
		b = b.replace(/ /g, "%20");
		while (a.exec(b)) {

			var key = RegExp.$1;
			var val = decodeURIComponent(RegExp.$2);
			var data = {};
			data[key] = val;
			c.push(data)
		}
		return c
	}

	$(".persAttrArea input[name='attrs']").prop("checked",true);//全选
	$("#customAttrArea${uuid} input[name='attrs']").prop("checked",false);//全不选
	selectHandle();

	$(".selectAll").click(function(){
		if($(this).val())
		{
			$("#"+$(this).val()+" input[name='attrs']:enabled").prop("checked",$(this).is(":checked"));//全选
		}
		else
		{
			$("#input[name='attrs']:enabled").prop("checked",$(this).is(":checked"));//全选
		}
		selectHandle();
	});

	$(".persAttrArea input[name='attrs']").click(function(){
		var id = $(this).parents(".persAttrArea").attr("id");
		if($(this).is(":checked"))
		{
			if($("#"+id+" input[name='attrs']:checkbox").length == $("#"+id+" input[name='attrs']:checked").length)
			{
				$(".selectAll[value='"+id+"']").prop("checked",true);
			}
		}
		else
		{
			$(".selectAll[value='"+id+"']").prop("checked",false);
		}
		selectHandle();
	});

});
function selectHandle(){
	var jsonColumn = {};
	$(".persAttrArea input[name='attrs']:checked").each(function(){
		jsonColumn[$(this).val().split("-")[1]] = $(this).parent().attr("title");
	});
	$("#jsonColumn${uuid}").val(JSON.stringify(jsonColumn));
	var customAttrs = "";
	$("#customAttrArea${uuid} input[name='attrs']:checked").each(function(){
		customAttrs += ","+$(this).val().split("-")[1];
	});
	$("#customAttrs${uuid}").val(customAttrs);
}
jQuery.validator.addMethod("recordStartValid", function(value, element) {
	if (value) {
		var records = $("input[name='records']").val();
		if(/^[\d]+$/.test(records)) {
			return parseInt($("input[name='recordstart']").val()) <= parseInt(records);
		}
	}
	return true;
}, "<@i18n 'common_report_fromIndexGTSum'/>");
$(":radio[name='exportType']").click(function(){
	if($("input[name='exportType']:checked").attr("value") == "3")
	{
		$("input[name='recordstart']").attr("disabled",false);
		$("input[name='recordcount']").attr("disabled",false);
	}
	else
	{
		$("input[name='recordstart']").attr("disabled",true);
		$("input[name='recordcount']").attr("disabled",true);
	}
});

function inputPassword() {
	if (!"${typeStr}"||"${typeStr}"=="")
	{
		var passwordEncrypt = $("input[name='isEncrypt']:checked").val();
		if(passwordEncrypt === "1") {
			$("input[name='encryptPassword']").rules("add", {required : true});
			$("input[name='encryptPassword']").rules("add", {rangelength : [ 4, 18 ]});
			$(".passwordCheck").show();
		} else {
			$("input[name='encryptPassword']").rules("remove");
			$(".passwordCheck").hide();
		}
	}
};
inputPassword();
$(".export-input-eye").on("mousedown", function(e) {
	$(this).addClass("export-input-eye-open");
	$("#encryptPassword").attr("type", "text");
})

$(".export-input-eye").on("mouseup", function(e) {
	$(this).removeClass("export-input-eye-open");
	$("#encryptPassword").attr("type", "password");
})
$("#loginPwd").on("change", function() {
	if($("#loginPwd").val())
	{
		$("#password_hidden").val(hex_md5($("#loginPwd").val()));
	}
	else
	{
		$("#password_hidden").val("");
	}
})

isNeedValidUserForExport(function(ret) {
	if (!"${typeStr}"||"${typeStr}"=="")
	{
		if(ret && ret.ret === sysCfg.success) {
			$("#loginPwdRow").hide();
			$("input[name='loginPwd']").rules("remove");
		} else {
			$("#loginPwdRow").show();
			$("input[name='loginPwd']").rules("add", {required : true});
		}
	}
})
</script>