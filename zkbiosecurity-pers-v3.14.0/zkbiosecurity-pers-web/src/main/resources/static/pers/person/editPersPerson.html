<#include '/public/template/editTemplate.html'>
<#macro editContent>
<script type="text/javascript" src="/system/js/ajaxfileupload.js" charset="UTF-8"></script>
<form action="persPerson.do?save" method="post" id="${formId}" enctype="multipart/form-data" onkeydown="if(event.keyCode==13){return false;}" autocomplete="off" style="width: 100%;overflow: hidden;height:579px">
	<input type="hidden" name="id" value="${(item.id)!}"/>
	<input type="hidden" name="personIdPhoto" id="personIdPhoto" value=""/>
	<input name="idCardPhysicalNo" type="hidden" value="${(item.idCardPhysicalNo)!}"/>
	<input name="idCard" type="hidden" value="${(item.idCard)!}"/>
	<input name="bioTemplateJson" id="bioTemplateJson" type="hidden" value='${(bioTemplateJson)!}'/>
	<input name="leaveId" type="hidden" value="${leaveId!}"/>
	<input name="moduleAuth" type="hidden" value=""/>
	<input name="existsMobileUser" type="hidden" value="${(item.existsMobileUser)!}"/>
	<input name="cropPhotoBase64" type="hidden" oldValue="${(item.cropPhotoBase64)!}" value=""/>
	<input name="cropPhotoDel" type="hidden" value="false"/>
	<input name="enabledCredential" type="hidden" value="${(item.enabledCredential)!}"/>
	<input id="isTempalteFace" name="isTempalteFace" type="hidden" value="1"/>
	<input id="isCropFace" name="isCropFace" type="hidden" value="1"/>
	<input name="plamPhotoBase64" type="hidden" value=""/>
	<input name="plamPhotoDel" type="hidden" value="false"/>
<!--	<input type="hidden" name="businessId" value="${(item.businessId)!}"/>-->
	<table style="width: 100%">
		<tr><td>
			<table class="tableStyle">
				<tr>
					<th><label><@i18n 'pers_person_pin'/></label><span class="required">*</span></th>
					<td>
						<input name="pin" type="text" id="pers_pin_register_id" value="${defPin!(item.pin)!}" maxlength="${persParams['pers.pinLen']}"/>
					</td>
					<th><label><@i18n 'pers_dept_entity'/></label><span class="required">*</span></th>
					<td>
						<@ZKUI.ComboGrid autoFirst="true" id="deptId${uuid}" width="148" queryField="name" hideLabel="true" text="${(item.deptName)!treeDeptName!}" value="${(item.deptId)!treeDeptId!}" name="deptId"
						grid_showColumns="ra,name,parentName" grid_vo="com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentSelectItem"
						grid_onCheck="editPersonDeptChange" grid_query="authDepartment.do?listSelect" grid_onXLE="loadLevelByDept" />
					</td>
				</tr>
				<tr>
					<th><label><@i18n 'pers_person_name'/></label></th>
					<td>
						<input type="text" style="display:none"/>
						<input type="text" name="name" maxlength="25" value="${(item.name)!}" readonly autocomplete="off"/>
					</td>
					<th><label><@i18n 'pers_person_lastName'/></label></th>
					<td>
						<input type="text" style="display:none"/>
						<input type="text" name="lastName" maxlength="25" value="${(item.lastName)!}" autocomplete="off"/>
					</td>
				</tr>

				<tr>
					<th><label><@i18n 'pers_person_gender'/></label></th>
					<td>
						<@ZKUI.Combo empty="true" id="gender${uuid}" width="148" name="gender"  hideLabel="true" value="${(item.gender)!}" key="sex"/>
					</td>
					<th><label><@i18n 'pers_person_mobilePhone'/></label></th>
					<td>
						<input name="mobilePhone" maxlength="20" type="text" value="${(item.mobilePhone)!}" <#if (item.existsMobileUser)?exists && (item.existsMobileUser)>readonly="readonly"</#if>/>
					<input name="sendSMS" type="hidden" value="${(item.sendSMS?string('true','false'))!'false'}">
					<#if showSMS?exists && showSMS?string == "true">
					<#if (item.sendSMS)??&&(item.sendSMS)>
					<@ZKUI.Input hideLabel="true" id= "checkSMSModem" style="margin-${leftRTL!'left'}: 10px;" type="checkbox" checked="checked"/>
					<#else>
					<@ZKUI.Input hideLabel="true" id= "checkSMSModem" style="margin-${leftRTL!'left'}: 10px;" type="checkbox"/>
				</#if>
			</#if>
		</td>
		</tr>

		<tr>
			<th><label><@i18n 'pers_cert_type'/></label></th>
			<td>
				<@ZKUI.Combo empty="true" width="148" id="certType${uuid}" name="certType"  hideLabel="true" value="${(certificate.certType)!}" key="certificateType" onChange="changeCertType"/>
				<span id="regnizeImg${uuid}" style="display:none;" class="icv-base_regnize" class="mouseStyle" title="<@i18n 'base_certRecognition'/>"></span>
				<input id="regnizeId${uuid}" name="regnizeIdreader" type="hidden" value="">
			</td>
			<th><label><@i18n 'pers_cert_number'/></label></th>
			<td><input name="certNumber" type="text" value="${(certificate.certNumber)!}" maxlength="30"/></td>
		</tr>

		<tr>
			<th><label><@i18n 'pers_person_birthday'/></label></th>
			<td><@ZKUI.Input type="date" value="${((item.birthday)?string('yyyy-MM-dd'))!}" id="birthday${uuid}" name="birthday" hideLabel="true" max="today" readonly="false"/></td>
			<th><label><@i18n 'pers_person_email'/></label></th>
			<td>
				<input name="mail" type="text" value="${(item.email)!}"/>
				<input name="isSendMail" type="hidden" value="${(item.isSendMail?string('true','false'))!'false'}">
				<#if (item.isSendMail)??&&(item.isSendMail)>
				<@ZKUI.Input hideLabel="true" id= "checkMail" style="margin-${leftRTL!'left'}: 10px;" type="checkbox" checked="checked" title="${getI18nByCode('pers_person_IsSendMail')}"/>
				<#else>
				<@ZKUI.Input hideLabel="true" id= "checkMail" style="margin-${leftRTL!'left'}: 10px;" type="checkbox" title="${getI18nByCode('pers_person_IsSendMail')}"/>
			</#if>
			</td>
		</tr>

		<tr>
			<th><label><@i18n 'pers_cardTemplate_entryDate'/></label></th>
			<td><@ZKUI.Input type="date" value="${((item.hireDate)?string('yyyy-MM-dd'))!}" name="hireDate" max="today" hideLabel="true" readonly="false"/></td>
			<th><label><@i18n 'pers_position_name'/></label></th>
			<td>
				<@ZKUI.ComboTree width="148" type="radio" url="persPosition.do?tree" value="${(item.positionId)!}" hideLabel="true" name="positionId"/>
			</td>
		</tr>

	<tr>
		<th><label><@i18n 'pers_person_password'/></label></th>
		<td>
			<input type="password" style="display:none"/><!-- 解决火狐谷歌记住密码 -->
			<input name="personPwd" type="password" value="${(item.personPwd)!}" autocomplete="new-password"/>
		</td>
		<th><label><@i18n 'pers_card_cardNo'/></label></th>
		<td class="multiCard">
			<input name="cardNos" type="hidden" value="<#if cardItems?exists>${cardItems[0].cardNo}<#else>${cardNo}</#if>"/>
			<input name="multiCards_0" type="text" value="<#if cardItems?exists>${cardItems[0].cardNo}<#else>${cardNo}</#if>" <#if cardItems?exists && cardItems[0].isFrom=="ACMS">readonly="readonly"</#if> onblur="multiCardsChange(this);" onkeyup="this.value=this.value.replace(/^0+/,'');" onkeypress="return isNotSpace(event);"/>
			<input name="acmsCardNum" type="hidden" value=""/>
			<input name="acmsMasterCard" type="hidden" value="0"/>
			<input name="acmsCardNos" type="hidden" value=""/>
			<span class="readCard icv-base_readCard1" id="multiCards_0" title="<@i18n 'pers_person_readCard'/>"></span>
			<span class="stopReadCard icv-base_stopReadCard" onclick="stopReadCard(this)" title="<@i18n 'pers_person_stopRead'/>"></span>
			<span class="delCard icv-ic_del" id="delCard_0" onclick="delCards(this)" <#if !(cardItems?exists && cardItems[0].isFrom=="ACMS")>hidden="hidden"</#if> style="margin-left: 3px;"></span>
		</td>
	</tr>

	<tr>
		<th><label><@i18n 'pers_person_biometrics'/></label></th>
		<td>
			<div id="icon_biometrics" class="persBiometrics"></div>
			<div style="display: inline-block;height: 24px; vertical-align: top">
				<span class="icv-ico_reg" style="margin-${leftRTL!'left'}: 10px; cursor:pointer" title="<@i18n 'pers_person_reg'/>" onclick="submitRegister(this, initBiometricsIcon)" type="bioPers"></span>
				<span id="detail_biometrics" style="margin-${leftRTL!'left'}: 5px; display: none;cursor: pointer;" class="icv-ico_detail" title="<@i18n 'common_op_detailInfo'/>" onclick="getBiometricsInfo()"></span>
			</div>
		</td>
		<#if showWhatsapp?exists && showWhatsapp?string == "true">
			<th><label><@i18n 'base_model_whatsapp'/></label></th>
			<td>
				<input name="whatsappMobileNo" maxlength="100" type="text" value="${(item.whatsappMobileNo)!}" />
				<input name="sendWhatsapp" type="hidden" value="${(item.sendWhatsapp?string('true','false'))!'false'}">
				<#if (item.sendWhatsapp)??&&(item.sendWhatsapp)>
				<@ZKUI.Input hideLabel="true" id= "checkWhatsApp" style="margin-${leftRTL!'left'}: 10px;" type="checkbox" checked="checked"/>
				<#else>
				<@ZKUI.Input hideLabel="true" id= "checkWhatsApp" style="margin-${leftRTL!'left'}: 10px;" type="checkbox"/>
				</#if>

			</td>
		</#if>
	</tr>
	<tr>
		<th><label><@i18n 'app_push'/></label></th>
		<td>
			<input name="sendApp" type="hidden" value="${(item.sendApp?string('true','false'))!'false'}">
			<#if (item.sendApp)??&&!(item.sendApp)>
			<@ZKUI.Input hideLabel="true" id= "checkSendApp" type="checkbox"/>
			<#else>
			<@ZKUI.Input hideLabel="true" id= "checkSendApp" type="checkbox" checked="checked"/>
			</#if>
		</td>
	</tr>
	</table>
	</td>
	<td valign="top">
		<div class="div_img floatL" style="width: 120px;height: 140px;">
			<div id="localImag">
				<span class="persTooltip">
					<span class="pers_icv icv-ic_que zk-colors-fg-green" />
					<div class="persCommonTipClass zk-border-color">
						<div class="persImagTop">
							<div class="persImagTopLeft">
								<img class="persExamplePic" src="/images/persExamplePic/pers_example_pic.png"/>
								<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_description' /></span></div>
							</div>
							<div class="persImagTopRight">
								<p><@i18n 'pers_examplePic_Tip' /></p>
								<p><@i18n 'pers_examplePic_Tip1' /></p>
								<p><@i18n 'pers_examplePic_Tip2' /></p>
								<p><@i18n 'pers_examplePic_Tip3' /></p>
								<p><@i18n 'pers_examplePic_Tip4' /></p>
								<p><@i18n 'pers_examplePic_Tip5' /></p>
							</div>
						</div>
						<div class="persExamplePicErrorTitle"><span><@i18n 'pers_examplePic_error' /></span></div>
						<div class="persImagBottom">
							<div class="persImagBottomLeft">
								<img class="persExamplePic" src="/images/persExamplePic/pers_excessiveSmile.png"/>
								<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_error1' /></span></div>
							</div>
							<div class="persImagBottomCenter">
								<img class="persExamplePic" src="/images/persExamplePic/pers_lightTooDim.png"/>
								<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_error2' /></span></div>
							</div>
							<div class="persImagBottomRight">
								<img class="persExamplePic" src="/images/persExamplePic/pers_faceTooSmall.png"/>
								<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_error3' /></span></div>
							</div>
						</div>
					</div>
				</span>
				<img id="id_img_pers" style="width: 120px;height: 140px"
					 src="${(item.photoBase64)!'/images/${attr("system.skin","")}/userImage.gif'}" onerror="this.src='/images/userImage.gif'" oldSrc="${(item.photoBase64)!'/images/${attr("system.skin","")}/userImage.gif'}"/>
			</div>
		</div>
		<div class="clear"></div>
		<div>
			<div style="position: relative;width: 130px">
				<br>
				<div id="personPhotoDiv${uuid}" style="display: none;">
					<input id="personPhoto"  name="personPhoto" type="file" accept="image/jpeg,image/jpg,image/png"  style="width:68px; cursor:pointer;"
						   onchange="setPersPhotoPreview();"/>
				</div>
				<input id="personPhotoButton" value="<@i18n 'pers_person_browse'/>"  name="personPhotoButton" type="button" style="display:inline-block;min-width:60px;max-width:61px;height:24px;margin:0;font-size:12px;line-height:0px;padding:0px;" onClick="personPhoto.click()" class="button-form"/>
				<input type="button" value="<@i18n 'pers_op_capture'/>" style="display:inline-block;min-width:60px;max-width:65px;height:24px;margin:0;font-size:12px;line-height:0px;padding:0px;" id="captureButton" onclick="openCaptureContent();" class="button-form"/>
			</div>
		</div>
	</td></tr>
	</table>
	<!-- 引入公用Html -->
	<#include '/pers/person/personTabHtml.html'>
</form>
<!-- 引入公用JS -->
<#include '/pers/person/personTabJs.html'>
<script type="text/javascript">
(function(){
	$(editPersonFormId).validate( {
		debug : true,
		rules :
		{
			'pin' : {
				required : true,
                pinNum : true,
                pinNumFirst: true,
                rangelength: function(obj)
                {
                    $(obj).blur(function(){
                        if(persParams['pers.pinSupportLetter'] == 'false')
                    	{
                    		$(obj).val($.trim(this.value.replace(/\b(0+)/gi,"")));
                    	}
                    });
                },
				overRemote : ["persPerson.do?isExist", "${(item.pin)!}"]
			},
			'deptId': {
				required : true
			},
            "name": {
                nameUnInputChar:true
            },
			"lastName": {
                nameUnInputChar:true
            },
            "personPwd" : {
                digits : true,
                maxlength: 8,
                remote : {
                    url : "persPerson.do?checkPwd",
                    type : "post",
                    data : {"personId" : "${(item.id)!}"} ,
                    dataType : "json"
                }
            },
			/*"areaCode": {
				maxlength: 10,
				unInputChar: true,
				required: function (){return $(editPersonFormId+" #checkSMSModem").is(":checked");}
			},*/
            "mobilePhone": {
                //mobilePhoneValid: true
                // number : true,
				digits : true,
				// || $(editPersonFormId+" #persAreaCode").val() != ""
				required: function (){return $(editPersonFormId+" #checkSMSModem").is(":checked");},
                maxlength: 20,
                unInputChar: true,
                overRemote : ["persPerson.do?checkMobileIsExist", "${(item.mobilePhone)!}"]
            },
            "mail": {
                required: function (){return ($(editPersonFormId+" #checkMail").is(":checked") || $("input[name='acmsCardNum']").val() != "");},
                maxlength: 100,
                email:true,
                emailIsExistValid:true
            },
            "ssn":{
                maxlength:20
            },
            "carPlate":{
                maxlength:20
            },
            "personPhoto":{
                accept: "jpg|JPG|jpeg|JPEG|png|PNG"
            },
            "personPhotoButton": {
                personPhotoButton:true
            },
            "multiCards_0" : {
                 cardNum : true,
                 maxlength: 50,
                 repeatCardValid : true,
                 cardNoExistValid : true
            },
            "selfPwd":{
                validPersonPwd : true
            },
            "certNumber":{
                certNumberValid:true,
                required: function (element){
					if(ZKUI.Combo.get("certType${uuid}").combo.getSelectedValue()=="") {
						$($(element).parents("td")[0]).find(".ts_box").remove();
						return false;
					}
					return true;
				}
            },
            "birthday":{
                dateValid:true
            },
            "hireDate":{
                dateValid:true
            },
			"certType":{
				required: function (element){
					if($("input[name='certNumber']").val()=="") {
						$(element).parents(".dhtmlxComboxError").parent().find(".ts_box").remove();
						$(element).parents(".dhtmlxComboxError").parent().find(".dhtmlxComboxError").removeClass("dhtmlxComboxError");
						return false;
					}
					return true;
				}
			},
			"whatsappMobileNo" :{
				required: function (){return $(editPersonFormId+" #checkWhatsApp").is(":checked");}
			}
		},
		onkeyup:function(){},
		submitHandler : function()
		{
			$("input[name='sendApp']").val($("#checkSendApp").is(":checked"));
			if($("input[name='multiCards_0']").val() == "" && $("input[name='multiCards_0']").attr("readonly") == "readonly")
			{
				$("input[name='acmsMasterCard']").val("1");
			}

			//详细信息里的照片不提交，避免base64照片字符被平台拦截特殊字符
			if($("#bioTemplateJson").val() != "")
			{
				var bioTemplateValJson = JSON.parse($("#bioTemplateJson").val());
				if(typeof bioTemplateValJson.vislightPhotoList != "undefined")
				{
					delete bioTemplateValJson.vislightPhotoList;
					$("#bioTemplateJson").val(JSON.stringify(bioTemplateValJson));
				}
			}
            //防止用户用读卡器读的数据没有保存到隐藏域值
            var acmsCardNum = new Array();
            $(editPersonFormId + " .multiCard input[name^='multiCards']").each(function(){
                $(this).blur();
                if ($(this).attr("acms") == '1') {
                	var acmsCardJson = {
                		cardNo : $(this).val(),
                		bussinessId : $(this).attr("bussinessId"),
                		activationCode : $(this).attr("activationCode"),
                		status : $(this).attr("status"),
                		cardOpType : $(this).attr("cardOpType")
                	};
					acmsCardNum.push(acmsCardJson);
				}
            });
            if(acmsCardNum.length > 0)
            {
            	$("input[name='acmsCardNum']").val(JSON.stringify(acmsCardNum));
            	if($('input[name="mail"]').val() == "")
            	{
            		$('input[name="mail"]').valid();
            		return;
            	}
            }

            var photoFile = document.getElementById("personPhoto");
            // 5 * 1024 * 1024  文件限制在5M大小,超过之后不上传
            if(photoFile.files.length > 0 && photoFile.files[0].size > 5242880){
                openMessage(msgType.warning, "<@i18n 'pers_import_fileMaxSize'/>");
                return;
            }
			//提交之前确定选中的权限组
			setSelectedLevelIdsBeforeSubmit();
			// 提交前业务模块数据验证
			var result = validDataBeforeSubmit();
			if (result == false) {
				return;
			}
		    <#if (leaveId)?exists>
            	<@submitHandler callBackFun="reloadLeavePersonGrid()"/>
        	<#else>
        		<@submitHandler callBackFun="reloadPersonTreeAndGrid()"/>
            </#if>
		}
	});

	//开启多卡
    if(persParams['pers.cardsSupport'] == "true"){
        $(".multiCard input[name='multiCards_0']").rules("add", { deputyCardValid:true });//给主控添加验证副卡

        $("#multiCardDiv .multiCard input[name^='multiCards']").each(function(){
            $(this).rules("add",{
                cardNum : true,
                maxlength: 50,
                repeatCardValid : true,
                cardNoExistValid : true
            });
        });
    }

	// 点击识别图标
	$("#regnizeImg${uuid}").click(function(){
		// 判断是否已注册
		if($("#regnizeImg${uuid}").attr("registerInfo") != undefined && $("#regnizeImg${uuid}").attr("registerInfo") == "ocr")
		{
			// ocr识别
			cardRecog($("#regnizeImg${uuid}").attr("countryCode"), ZKUI.Combo.get('certType${uuid}').getValue(), $("#regnizeImg${uuid}").attr("clientType"), $("#regnizeImg${uuid}").attr("version"), perLoadRetValue);
			// cardRecog("CHL", "1", $("#regnizeImg${uuid}").attr("clientType"), $("#regnizeImg${uuid}").attr("version"));
		}
		else if($("#regnizeImg${uuid}").attr("registerInfo") != undefined && $("#regnizeImg${uuid}").attr("registerInfo") == "idReader")
		{
			// IDreader识别
			readIDCard($("#regnizeImg${uuid}").attr("countryCode"), ZKUI.Combo.get('certType${uuid}').getValue(), $("#regnizeImg${uuid}").attr("clientType"), perLoadRetValue);
		}
		else
		{
			openMessage(msgType.warn, "<@i18n 'base_noAuthorization'/>");
		}
	});

	$(editPersonFormId+" input[name='certNumber']").change(function(val){
		$(editPersonFormId+" input[name='certType']").valid();
	});
	ZKUI.Combo.get("certType${uuid}").combo.attachEvent("onAfterLoad", function(val){
		getLicenseMsg("pers", "regnizeImg${uuid}", "regnizeId${uuid}", ZKUI.Combo.get('certType${uuid}').getValue());
     })
	if(typeof (readCardMouse) == "function")
	{
		readCardMouse();
	}
})();

// 切换证件类型判断是否显示ocr识别图标
function changeCertType()
{
	if(typeof isCertLicensed == 'function' && ZKUI.Combo.get('certType${uuid}').getValue() != "" && ($("#regnizeImg${uuid}").attr("clientTypeStr") || $("#regnizeId${uuid}").attr("clientTypeStr")))
	{
		isCertLicensed("regnizeImg${uuid}", $("#regnizeImg${uuid}").attr("clientTypeStr") || $("#regnizeId${uuid}").attr("clientTypeStr") , ZKUI.Combo.get('certType${uuid}').getValue());
	}
	else {
		$("#regnizeImg${uuid}").css("display","none");
	}
}
// IDreader、ocr信息回填
function perLoadRetValue(result, isOcrRecog)
{
	if(result.certType != null && result.certType != "")
	{
		ZKUI.Combo.get("certType${uuid}").combo.setComboValue(result.certType);
	}
	if(result.firstName != undefined && result.firstName != "")
	{
		$("input[name='name']").val(result.firstName);
	}
	else if(result.GivenName != undefined && result.GivenName != "")
	{
		$("input[name='name']").val(result.GivenName);
	}
	if(result.lastName != undefined && result.lastName != "")
	{
		$("input[name='lastName']").val(result.lastName);
	}
	ZKUI.Combo.get("gender${uuid}").combo.setComboValue(result.gender);
// 		ocr识别判断证件号码回填类型
	if(isOcrRecog && ZKUI.Combo.get('certType${uuid}').getValue() == "1" && persParams['pers.ocrCertNoType'] == "2" && result['Personal Number'] != undefined && result['Personal Number'] != "")
	{
		$("input[name='certNumber']").val(result['Personal Number'].replace(/-/g,"").replace(/\./g,""));
	}
	else if(result.certNumber != undefined && result.certNumber != "")
	{
		$("input[name='certNumber']").val(result.certNumber.replace(/-/g,"").replace(/\./g,""));
	}
	if(result.dateOfBirth != undefined)
	{
		var date= new Date(Date.parse(result.dateOfBirth.replace(/-/g,   "/"))); //转换成Data();
		ZKUI.Input.get("birthday${uuid}").setDate(date);
	}
	if(result.placeOfBirth != undefined && result.placeOfBirth != "")
	{
		$("input[name='attrValue7']").val(result.placeOfBirth);
	}
	if(result.address != undefined && result.address != "")
	{
		$("input[name='attrValue10']").val((result.address).replaceAll("/"," ").replaceAll(","," "));
	}
	if(result.country != undefined && result.country != "")
	{
		$.ajax({
			url:"persPerson.do?getCountry",
			type:"post",
			dataType:"json",
			async :false,
			data:{"countryKey": getNationalityCode(result.country)},
			success: function (data)
			{
				if(data.ret == sysCfg.success)
				{
					$("input[name='attrValue8']").val(data.data);
				}
			}
		});

	}
// 		编辑有照片的人员时弹出提示
	if(result.faceImageBase64 != undefined && result.faceImageBase64 != "")
	{
		$("#id_img_pers").attr("src","data:image/jpg;base64,"+result.faceImageBase64);
		$("#personIdPhoto").val(result.faceImageBase64);
		$("#personPhoto").val("");
	}
	openMessage(msgType.success, "<@i18n 'base_recognize_success'/>");
}

function initBiometricsIcon() {
    var biometricsCount = $("#bioTemplateJson").val();
    var html = "";
    $("#icon_biometrics").empty();
    if(biometricsCount != ""){
        biometricsCount = eval("("+biometricsCount+")");
        if (!$.isEmptyObject(biometricsCount.fpList))
        {
            html += '<span class="icv-ico_fingerprint" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.faceList))
        {
            html += '<span class="icv-ico_metric" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.fvList))
        {
            html += '<span class="icv-ico_finger_vein" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.palmList) || !$.isEmptyObject(biometricsCount.vislightPalmList))
        {
            html += '<span class="icv-ico_palm" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.vislightList) || (biometricsCount.vislightPhotoList != undefined && biometricsCount.vislightPhotoList.length>0))
        {
            html += '<span class="icv-ico_biophoto" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.irisList))
        {
            html += '<span class="icv-ico_iris" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
    }
    $("#icon_biometrics").append(html);
	$("#detail_biometrics").show();
}
initBiometricsIcon();

function getBiometricsInfo()
{
    var data = $("#bioTemplateJson").val();
    var path = "skip.do?page=pers_person_personBiometricsInfo&bioData="+data;
    var opts = {
        path:path,
        width:540,
        height:500,
        title:"<@i18n 'common_op_detailInfo'/>"
    };
    DhxCommon.createWindow(opts);
}

var loadFlag = false;
var systemModules = "${systemModules}";
//ZKUI.ComboGrid.get("deptId${uuid}").combo.attachEvent("onChange", loadLevelByDept);
function loadLevelByDept() {
	if (ZKUI.ComboGrid.get("deptId${uuid}") != undefined) {
		var deptId = "${item.deptId}";
		if(deptId == "") {
			var first = this.rowsCol[0];
			if (first && first.idd) {
				deptId = first.idd;
			}
		}
		if(!loadFlag && deptId != "" && deptId != null) {
			firstLoadDeptLevel(deptId)
			loadFlag = true;
		}
	}
}
</script>
</#macro>