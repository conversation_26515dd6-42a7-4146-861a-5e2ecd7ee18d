<#include "/public/template/editTemplate.html">
<#macro editContent>
<form method="POST" action="persPerson.do?deleteBioTemplate" id="${formId}" enctype="multipart/form-data">
    <input type="hidden" name="personIds" value="${(personIds)!}">
    <input type="hidden" name="pins" value="${(pins)!}">
    <table class="tableStyle">
        <tr>
            <td>
                <@ZKUI.Input hideLabel="true" type="checkbox" id="bioTypeFp" name="fp" value="1"/>
                <@i18n 'pers_person_regFinger'/>
            </td>
            <td>
                <@ZKUI.Input hideLabel="true" type="checkbox" id="bioTypeFace" name="face" value="2"/>
                <@i18n 'pers_person_infraredFace'/>
            </td>
        </tr>
        <tr>
            <td>
                <@ZKUI.Input hideLabel="true" type="checkbox" id="bioTypeFv" name="fv" value="7"/>
                <@i18n 'pers_person_regVein'/>
            </td>
            <td>
                <@ZKUI.Input hideLabel="true" type="checkbox" id="bioTypePalm" name="palm" value="8"/>
                <@i18n 'pers_person_metacarpalVein'/>
            </td>
        </tr>
        <tr>
            <td>
                <@ZKUI.Input hideLabel="true" type="checkbox" id="bioTypeVislightPalm" name="vislightPalm" value="10"/>
                <@i18n 'pers_person_visiblePalm'/>
            </td>
            <td>
                <@ZKUI.Input hideLabel="true" type="checkbox" id="bioTypeVislight" name="vislight" value="9"/>
                <@i18n 'pers_person_visibleFace'/>
            </td>
        </tr>
        <tr>
            <td>
                <@ZKUI.Input hideLabel="true" type="checkbox" id="bioTypeIris" name="iris" value="4"/>
                <@i18n 'pers_person_iris'/>
            </td>
        </tr>
    </table>
</form>

<script type='text/javascript'>
    (function() {
        $('#${formId}').validate( {
            debug : true,
            rules :
            {
            },
            submitHandler : function()
            {
                if(!$("input[id^='bioType']:checked").size() > 0)
                {
                    messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'pers_person_delBioTemplateSelect'/>"});
                    return;
                }
                <@submitHandler/>
            }
        });
    })();
</script>
</#macro>
<#macro buttonContent>
<button class='button-form' id="${formId}OK"><@i18n 'common_op_del'/></button>
<button class='button-form' onclick="DhxCommon.closeWindow()"><@i18n 'common_op_close'/></button>
</#macro>