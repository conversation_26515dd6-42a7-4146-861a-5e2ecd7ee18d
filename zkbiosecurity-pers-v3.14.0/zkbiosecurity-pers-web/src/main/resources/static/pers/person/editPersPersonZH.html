<#include '/public/template/editTemplate.html'>
<#macro editContent>
<script type="text/javascript" src="/system/js/ajaxfileupload.js" charset="UTF-8"></script>
<form action="persPerson.do?save" method="post" id="${formId}" enctype="multipart/form-data" onkeydown="if(event.keyCode==13){return false;}" autocomplete="off" style="width: 100%;overflow: hidden;">
	<input type="hidden" name="id" value="${(item.id)!}"/>
	<input type="hidden" name="personIdPhoto" id="personIdPhoto" value=""/>
	<input name="idCardPhysicalNo" type="hidden" value="${(item.idCardPhysicalNo)!}"/>
	<input name="lastName" type="hidden" value="${(item.lastName)!}"/>
	<input name="idCard" type="hidden" value="${(item.idCard)!}"/>
	<input name="bioTemplateJson" id="bioTemplateJson" type="hidden" value='${(bioTemplateJson)!}'/>
	<input name="leaveId" type="hidden" value="${leaveId!}"/>
	<input name="moduleAuth" type="hidden" value=""/>
	<input name="existsMobileUser" type="hidden" value="${(item.existsMobileUser)!}"/>
	<input name="enabledCredential" type="hidden" value="${(item.enabledCredential)!}"/>
	<input name="cropPhotoBase64" type="hidden" oldValue="${(item.cropPhotoBase64)!}" value=""/>
	<input name="cropPhotoDel" type="hidden" value="false"/>
	<input id="isTempalteFace" name="isTempalteFace" type="hidden" value="1"/>
	<input id="isCropFace" name="isCropFace" type="hidden" value="1"/>
	<input name="plamPhotoBase64" type="hidden" value=""/>
	<input name="plamPhotoDel" type="hidden" value="false"/>
	<table style="width: 100%">
		<tr><td>
			<table class="tableStyle">
				<tr>
					<th><label><@i18n 'pers_person_pin'/></label><span class="required">*</span></th>
					<td>
						<input name="pin" id="pers_pin_register_id" type="text" value="${defPin!(item.pin)!}" maxlength="${persParams['pers.pinLen']}"/>
					</td>
					<th><label><@i18n 'pers_dept_entity'/></label><span class="required">*</span></th>
					<td>
						<@ZKUI.ComboGrid autoFirst="true" id="deptId${uuid}" width="148" queryField="name" hideLabel="true" text="${(item.deptName)!treeDeptName!}" value="${(item.deptId)!treeDeptId!}" name="deptId"
						grid_showColumns="ra,name,parentName" grid_vo="com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentSelectItem"
						grid_onCheck="editPersonDeptChange" grid_query="authDepartment.do?listSelect" grid_onXLE="loadLevelByDept" />
					</td>
				</tr>

				<tr>
					<th><label><@i18n 'pers_person_name'/></label></th>
					<td>
						<input type="text" style="display:none"/>
						<input type="text" name="name" maxlength="25" value="${(item.name)!}" readonly autocomplete="off"/>
					</td>
					<th><label><@i18n 'pers_person_mobilePhone'/></label></th>
					<td><input name="mobilePhone" type="text" value="${(item.mobilePhone)!}" <#if (item.existsMobileUser)?exists && (item.existsMobileUser)>readonly="readonly"</#if>/></td>
				</tr>

				<tr>
					<th><label><@i18n 'pers_person_gender'/></label></th>
					<td>
						<@ZKUI.Combo empty="true" id="gender${uuid}" width="148" name="gender"  hideLabel="true" value="${(item.gender)!}" key="sex"/>
					</td>
					<th><label><@i18n 'pers_card_cardNo'/></label></th>
					<td class="multiCard">
						<input name="cardNos" type="hidden" value="${cardNo!(item.cardNos)?split(',')[0]}"/>
						<input name="multiCards_0" type="text" value="${cardNo!(item.cardNos)?split(',')[0]}" onblur="multiCardsChange(this);" onkeyup="this.value=this.value.replace(/^0+/,'');" onkeypress="return isNotSpace(event);"/>
						<span class="readCard icv-base_readCard1" id="multiCards_0" title="<@i18n 'pers_person_readCard'/>"></span>
						<span class="stopReadCard icv-base_stopReadCard" onclick="stopReadCard(this)" title="<@i18n 'pers_person_stopRead'/>"></span>
					</td>
				</tr>

				<tr>
					<th><label><@i18n 'pers_cert_type'/></label></th>
					<td>
						<@ZKUI.Combo empty="true" width="148" id="certType${uuid}" name="certType"  hideLabel="true" value="${(certificate.certType)!}" key="certificateType"/>
					</td>
					<th><label><@i18n 'pers_cert_number'/></label></th>
					<td class="multiCard">
						<input name="certNumber" type="text" value="${(certificate.certNumber)!}" maxlength="30"/>
						<#if persParams["pers.IDReadMode"] =="1">
						<span class="readIDCard icv-base_readCard1" onclick="readIDCard(this)" title="<@i18n 'pers_person_ReadIDCard'/>" class="mouseStyle"></span>
					</#if>
					<#if persParams['pers.IDReadMode'] == "2">
					<span class="readIDCardByTcpReader icv-base_readCard1" title="<@i18n 'pers_person_ReadIDCard'/>" onclick="readIDCardByTcpReader(this)" style="margin-${leftRTL!'left'}: 8px;cursor: pointer;"></span>
					<span class="stopReadIDCardByTcpReader icv-base_stopReadCard" title="<@i18n 'pers_person_stopRead'/>" onclick="stopReadIDCardByTcpReader(this)" style="display: none;margin-${leftRTL!'left'}: 8px;cursor: pointer;"></span>
				</#if>
				</td>
				</tr>

				<tr>
					<th><label><@i18n 'pers_person_birthday'/></label></th>
					<td><@ZKUI.Input id="birthday${uuid}" type="date" value="${((item.birthday)?string('yyyy-MM-dd'))!}" name="birthday" hideLabel="true" max="today" readonly="false"/></td>
					<th><label><@i18n 'pers_person_email'/></label></th>
					<td>
						<input name="mail" type="text" value="${(item.email)!}"/>
						<input name="isSendMail" type="hidden" value="${(item.isSendMail?string('true','false'))!'false'}">
						<#if (item.isSendMail)??&&(item.isSendMail)>
						<@ZKUI.Input hideLabel="true" id= "checkMail" style="margin-${leftRTL!'left'}: 10px;" type="checkbox" checked="checked" title="${getI18nByCode('pers_person_IsSendMail')}"/>
						<#else>
						<@ZKUI.Input hideLabel="true" id= "checkMail" style="margin-${leftRTL!'left'}: 10px;" type="checkbox" title="${getI18nByCode('pers_person_IsSendMail')}"/>
					</#if>
					</td>
				</tr>

				<tr>
					<th><label><@i18n 'pers_cardTemplate_entryDate'/></label></th>
					<td><@ZKUI.Input type="date" value="${((item.hireDate)?string('yyyy-MM-dd'))!}" name="hireDate" max="today" hideLabel="true" readonly="false"/></td>
					<th><label><@i18n 'pers_position_name'/></label></th>
					<td>
						<@ZKUI.ComboTree width="148" type="radio" url="/persPosition.do?tree"  value="${(item.positionId)!}"  hideLabel="true" name="positionId"/>
					</td>
				</tr>

		<tr>
			<th><label><@i18n 'pers_person_password'/></label></th>
			<td>
				<input type="password" style="display:none"/><!-- 解决火狐谷歌记住密码 -->
				<input name="personPwd" type="password" value="${(item.personPwd)!}" autocomplete="new-password"/>
			</td>
			<th><label><@i18n 'pers_person_biometrics'/></label></th>
			<td>
				<div id="icon_biometrics" class="persBiometrics"></div>
				<div style="display: inline-block;height: 24px; vertical-align: top">
					<span class="icv-ico_reg" style="margin-${leftRTL!'left'}: 10px; cursor:pointer" title="<@i18n 'pers_person_reg'/>" onclick="submitRegister(this, initBiometricsIcon)" type="bio"></span>
					<span id="detail_biometrics" style="margin-${leftRTL!'left'}: 5px; display: none;cursor: pointer;" class="icv-ico_detail" title="<@i18n 'common_op_detailInfo'/>" onclick="getBiometricsInfo()"></span>
				</div>
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'app_push'/></label></th>
			<td>
				<input name="sendApp" type="hidden" value="${(item.sendApp?string('true','false'))!'false'}">
				<#if (item.sendApp)??&&!(item.sendApp)>
				<@ZKUI.Input hideLabel="true" id= "checkSendApp" type="checkbox"/>
				<#else>
				<@ZKUI.Input hideLabel="true" id= "checkSendApp" type="checkbox" checked="checked"/>
				</#if>
			</td>
		</tr>
	</table>
	</td>
	<td valign="top">
		<div class="div_img floatL" style="width:120px; height:140px;">
			<div id="localImag">
				<span class="persTooltip">
					<span class="pers_icv icv-ic_que zk-colors-fg-green" />
					<div class="persCommonTipClass zk-border-color">
						<div class="persImagTop">
							<div class="persImagTopLeft">
								<img class="persExamplePic" src="/images/persExamplePic/pers_example_pic.png"/>
								<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_description' /></span></div>
							</div>
							<div class="persImagTopRight">
								<p><@i18n 'pers_examplePic_Tip' /></p>
								<p><@i18n 'pers_examplePic_Tip1' /></p>
								<p><@i18n 'pers_examplePic_Tip2' /></p>
								<p><@i18n 'pers_examplePic_Tip3' /></p>
								<p><@i18n 'pers_examplePic_Tip4' /></p>
								<p><@i18n 'pers_examplePic_Tip5' /></p>
							</div>
						</div>
						<div class="persExamplePicErrorTitle"><span><@i18n 'pers_examplePic_error' /></span></div>
						<div class="persImagBottom">
							<div class="persImagBottomLeft">
								<img class="persExamplePic" src="/images/persExamplePic/pers_excessiveSmile.png"/>
								<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_error1' /></span></div>
							</div>
							<div class="persImagBottomCenter">
								<img class="persExamplePic" src="/images/persExamplePic/pers_lightTooDim.png"/>
								<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_error2' /></span></div>
							</div>
							<div class="persImagBottomRight">
								<img class="persExamplePic" src="/images/persExamplePic/pers_faceTooSmall.png"/>
								<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_error3' /></span></div>
							</div>
						</div>
					</div>
				</span>
				<img id="id_img_pers" width="120px" height="140px"
					 src="${(item.photoBase64)!'/images/${attr("system.skin","")}/userImage.gif'}" onerror="this.src='/images/userImage.gif'" oldSrc="${(item.photoBase64)!'/images/${attr("system.skin","")}/userImage.gif'}"/>
			</div>
		</div>
		<div class="clear"></div>
		<div>
			<div style="position: relative;">
				<br>
				<div id="personPhotoDiv${uuid}" style="display: none;">
					<input id="personPhoto" name="personPhoto" type="file" accept="image/jpeg,image/jpg,image/png"  style="width:68px; cursor:pointer;"
						   onchange="javascript:setPersPhotoPreview();"/>
				</div>
				<input id="personPhotoButton" value="<@i18n 'pers_person_browse'/>"  name="personPhotoButton" type="button" style="display:inline-block;min-width:60px;max-width:61px;height:24px;margin:0;font-size:12px;line-height:0px;padding:0px;" onClick="personPhoto.click()" class="button-form"/>
				<input type="button" value="<@i18n 'pers_op_capture'/>" style="display:inline-block;min-width:60px;max-width:65px;height:24px;margin:0;font-size:12px;line-height:0px;padding:0px;" id="captureButton" onclick="openCaptureContent();" class="button-form"/>
			</div>
		</td></tr>
	</table>
	<!-- 引入公用Html -->
	<#include '/pers/person/personTabHtml.html'>
</form>
<!-- 引入公用JS -->
<#include '/pers/person/personTabJs.html'>

<script type="text/javascript">
(function(){
	if(MC5000Port)
    {
        MC5000Port = null;
    }
    ZKDevice.get(ZKDevType.ScanInfo,function(IssControls){
        $(editPersonFormId+" .readIDCard").attr("pluginUrl",IssControls.url);
        $(editPersonFormId+" .readIDCard").attr("pluginLinuxUrl",IssControls.linuxUrl);
        if(IssControls.installed){
            $(editPersonFormId+" .readIDCard").removeAttr("installed");
        }else{
            $(editPersonFormId+" .readIDCard").attr("installed","installed");
            // 未安装驱动，先点击识别身份证按钮，后返回驱动安装情况弹出下载驱动提示框
			if($(editPersonFormId+" .readIDCard").attr("readIDCard")){
				closeMessage();
				var issInstall = "";
				<#if "${Application['system.osType']!'window'}" == "window">
					issInstall = "<span class='warningColor'><@i18n 'common_issOnline_notInstalled' />".format("<@i18n 'common_issOnline_deviceDriver' />") + "</span><a href="+IssControls.url+" class='devWarningColor'><@i18n 'common_issOnline_driverDownload' /></a>";
				<#elseif "${Application['system.osType']!'window'}" == "linux">
					issInstall = "<span><@i18n 'common_issOnline_notInstalled' />".format("<@i18n 'common_issOnline_deviceDriver' />") + "</span><a><@i18n 'common_issOnline_driverDownload' />(<a href="+IssControls.url+">Windows</a>, <a href="+IssControls.linuxUrl+">Linux</a>)</a>";
				</#if>
				messageBox({messageType: "alert", title: I18n.getValue('common_prompt_title'), text: issInstall});
			}
        }
    });

	$(editPersonFormId).validate( {
		debug : true,
		rules :
		{
			"pin" : {
				required : true,
                pinNum : true,
                pinNumFirst: true,
                rangelength: function(obj)
                {
                    $(obj).blur(function(){
                    	if(persParams['pers.pinSupportLetter'] == 'false')
                    	{
                    		$(obj).val($.trim(this.value.replace(/\b(0+)/gi,"")));
                    	}
                    });
                },
				overRemote : ["/persPerson.do?isExist", "${(item.pin)!}"]
			},
			"deptId": {
				required : true
			},
            "name": {
                maxlength: 25,
			    nameUnInputChar:true
            },
            "personPwd" : {
                digits : true,
                maxlength: 8,
                remote : {
                    url : "/persPerson.do?checkPwd",
                    type : "post",
                    data : {"personId" : "${(item.id)!}"} ,
                    dataType : "json"
                }
            },
            "mobilePhone": {
                mobilePhoneValid: true,
                // number : true,
				digits : true,
                maxlength: 20,
                unInputChar: true,
                overRemote : ["persPerson.do?checkMobileIsExist", "${(item.mobilePhone)!}"]
            },
            "mail": {
                required: function (){return $(editPersonFormId+" #checkMail").is(":checked");},
                maxlength: 100,
                email:true,
                emailIsExistValid:true
            },
            "ssn":{
                maxlength:20
            },
            "carPlate":{
                maxlength:20
            },
            "personPhoto":{
                accept: "jpg|JPG|jpeg|JPEG|png|PNG"
            },
            "personPhotoButton": {
                personPhotoButton:true
            },
            "multiCards_0" : {
                cardNum : true,
                maxlength: 50,
                repeatCardValid : true,
                cardNoExistValid : true
            },
            "selfPwd":{
                validPersonPwd : true
            },
            "certNumber":{
                certNumberValid:true,
                required: function (element){
					if(ZKUI.Combo.get("certType${uuid}").combo.getSelectedValue()=="") {
						$($(element).parents("td")[0]).find(".ts_box").remove();
						return false;
					}
					return true;
				}
            },
            "birthday":{
                dateValid:true
            },
            "hireDate":{
                dateValid:true
            },
			"certType":{
				required: function (element){
					if($("input[name='certNumber']").val()=="") {
						$(element).parents(".dhtmlxComboxError").parent().find(".ts_box").remove();
						$(element).parents(".dhtmlxComboxError").parent().find(".dhtmlxComboxError").removeClass("dhtmlxComboxError");
						return false;
					}
					return true;
				}
			}
		},
		messages:{
            "personPwd" : {
				remote : "<@i18n 'pers_person_duressPwdError'/>"
			}
        },
        onkeyup:function(){},
		submitHandler : function()
		{
			$("input[name='sendApp']").val($("#checkSendApp").is(":checked"));
			//详细信息里的照片不提交，避免base64照片字符被平台拦截特殊字符
			if($("#bioTemplateJson").val() != "")
			{
				var bioTemplateValJson = JSON.parse($("#bioTemplateJson").val());
				if(typeof bioTemplateValJson.vislightPhotoList != "undefined")
				{
					delete bioTemplateValJson.vislightPhotoList;
					$("#bioTemplateJson").val(JSON.stringify(bioTemplateValJson));
				}
			}
            //防止用户用读卡器读的数据没有保存到隐藏域值
			$(editPersonFormId + " .multiCard input[name^='multiCards']").each(function(){
        		$(this).blur();
            });

            var photoFile = document.getElementById("personPhoto");
            // 5 * 1024 * 1024  文件限制在5M大小,超过之后不上传
            if(photoFile.files.length > 0 && photoFile.files[0].size > 5242880){
                openMessage(msgType.warning, "<@i18n 'pers_import_fileMaxSize'/>");
                return;
            }
			//提交之前确定选中的权限组
			setSelectedLevelIdsBeforeSubmit();
			// 提交前业务模块数据验证
			var result = validDataBeforeSubmit();
			if (result == false) {
				return;
			}
		    <#if (leaveId)?exists>
                <@submitHandler callBackFun="reloadLeavePersonGrid()"/>
            <#else>
            	if (typeof reloadPersonTreeAndGrid == "function") {
					<@submitHandler callBackFun="reloadPersonTreeAndGrid()"/>
            	} else {
            <@submitHandler/>
		}
            </#if>
		}
	});

	if (sysCfg.securityLevel && sysCfg.securityLevel <= 3) {
		 $("#${formId} input[name='personPwd']").rules("add", {minlength:5});
	}

	// 开启多卡
    if(persParams['pers.cardsSupport'] == "true"){
        $(".multiCard input[name='multiCards_0']").rules("add", { deputyCardValid:true });//给主控添加验证副卡

        $("#multiCardDiv .multiCard input[name^='multiCards']").each(function(){
            $(this).rules("add",{
                cardNum : true,
                maxlength: 50,
                repeatCardValid : true,
                cardNoExistValid : true
			});
        });
    }
    // ZKUI.Combo.get("certType${uuid}").combo.setComboValue(2);//默认身份证
	var $certType = ZKUI.Combo.get("certType${uuid}").combo;
    $certType.attachEvent("onChange", function(val){
        if(val == "2"){
        	if (persParams['pers.IDReadMode'] == "2") {
				$(editPersonFormId+" .readIDCardByTcpReader").show();
			}else {
				$(editPersonFormId+" .readIDCard").show();
        	}
        }else{
			$(editPersonFormId+" .readIDCard").hide();
			$(editPersonFormId+" .readIDCardByTcpReader").hide();
			$(editPersonFormId+" .stopReadIDCardByTcpReader").hide();
			$(editPersonFormId+" input[name='idCard']").val("");
        }
    });
    $(editPersonFormId+" input[name='certNumber']").change(function(val){
        if($certType.getSelectedValue() == "2"){
            $(editPersonFormId+" input[name='idCard']").val($(editPersonFormId+" input[name='certNumber']").val());
        }
        else
        {
        	$(editPersonFormId+" input[name='idCard']").val("");
        }
		$(editPersonFormId+" input[name='certType']").valid();
    });

    $certType.callEvent("onChange",["${(certificate.certType)!''}"]);
	if(typeof (readCardMouse) == "function")
     {
     	readCardMouse();
     }
})();

/**
 * 读取身份证
 * @param obj
 */
function readIDCard(obj){
	//ID100或者ID180读取身份证
	if($(obj).attr("installed")){
		var issInstall = "";
		<#if "${Application['system.osType']!'window'}" == "window">
			issInstall = "<span class='warningColor'><@i18n 'common_issOnline_notInstalled' />".format("<@i18n 'common_issOnline_deviceDriver' />") + "</span><a href="+$(obj).attr("pluginUrl")+" class='devWarningColor'><@i18n 'common_issOnline_driverDownload' /></a>";
		<#elseif "${Application['system.osType']!'window'}" == "linux">
			issInstall = "<span><@i18n 'common_issOnline_notInstalled' />".format("<@i18n 'common_issOnline_deviceDriver' />") + "</span><a><@i18n 'common_issOnline_driverDownload' />(<a href="+$(obj).attr("pluginUrl")+">Windows</a>, <a href="+$(obj).attr("pluginLinuxUrl")+">Linux</a>)</a>";
		</#if>
		messageBox({messageType: "alert", title: I18n.getValue('common_prompt_title'), text: issInstall});
		return;
	}
// 点击过识别按钮标记
	$(obj).attr("readIDCard","readIDCard")

	openMessage(msgType.loading, "<@i18n 'pers_person_cardPrompt'/>");

	//  result 格式 { "ret": 0, "Certificate": { "Name": "张三", " " "Birthday": "1991.8.8", "Address": "福建省厦门市集美区软件园三期",
	 //    Sex": "男","IDNumber": "350524198905201314", "IDIssued": "集美公安局", "IssuedData": "2017.07.03","ValidDate": "2037.07.03",
	 //    Nation": "汉","Other": "", "CardNumber": "", "PhotoName": "C:\\Program Files (x86)\\FPOnline\\bin\\IDCardReader2\\zp.bmp",
	 //    "Base64Photo": "", "ImageName": "", "Base64Image": "(null)", "IDSNR": ""  } }
	ZKDevice.get(ZKDevType.ScanInfo).ReadIDCard(function(result){
					openMessage(msgType.success, I18n.getValue("common_issOnline_cardSuccess"));
					var info = result.Certificate;
					var birthday = info.Birthday;//生日
					birthday = birthday.substring(0,4)+"-"+birthday.substring(5,7)+"-"+birthday.substring(8);
					$(editPersonFormId+" input[name='name']").val(info.Name);
					var dictionarySex = Dictionary.get("sex");
					for (var key in dictionarySex) {
						if (info.Sex == dictionarySex[key]) {
							ZKUI.Combo.get("gender${uuid}").combo.setComboValue(key);
						}
					}
					ZKUI.Input.get('birthday${uuid}').setDate(new Date(birthday));
					ZKUI.Combo.get("certType${uuid}").combo.setComboValue(2);
					$(editPersonFormId+" input[name='certNumber']").val(info.IDNumber);
					$(editPersonFormId+" input[name='idCard']").val(info.IDNumber);
					ZKUI.Combo.get("attrValue2${uuid}").combo.setComboValue(info.Nation + "族");
					$(editPersonFormId+" input[name='attrValue12']").val(info.Address);
					$(editPersonFormId+" #id_img_pers").attr("src","data:image/jpeg;base64,"+info.Base64Photo);
					$(editPersonFormId+" #personIdPhoto").val(info.Base64Photo);
					$(editPersonFormId+" #personPhoto").val("");
					if(persParams['pers.IDReadMode'] == "1")
					{
						$("input[name='multiCards_0']").val(info.IDSNR);//物理卡号
					}
					validPersonPhoto();
				});
}

var certNumberObj;
function readIDCardByTcpReader(obj) {
	certNumberObj = obj;
	$(certNumberObj).prev().focus();
	var sure = $(obj).attr("onSure") || "startReadIDCard";
	var opts = {//选人控件弹窗配置
		path: "/skip.do?page=acc_reader_accSelectTcpReaderContentRadio",//弹窗路径
		title: I18n.getValue("pers_person_chooseDoor"),//弹窗标题
		width: 900,//窗口宽度
		height: 470,//窗口高度
		onSure: sure,//回调事件
        callback: function () {
            var sid = $(this.cell).find(".select_layout_box")[0];
            if (sid) {
                DhxCommon.initEvents(ZKUI.Select.get(sid.id), ZKUI.Select.suport_evts, opts);
            }
        }
	};
	DhxCommon.createWindow(opts);
}

//开始读身份证卡,用TCP读头
var stopPost;//定时器
var readCardTimer = null;//读卡定时器

function startReadIDCard(value, text, event)
{
	$(".readIDCardByTcpReader").hide();
	$(".stopReadIDCardByTcpReader").show();
	$(certNumberObj).parent().find("input[name^='certNumber']").addClass("wait");
	stopSendRequest();
	var time ="";
	var valueBackfill = function(data){
		if(data != undefined && data.identityCardInfo != undefined)
		{
			var info = JSON.parse(data.identityCardInfo);
			$(editPersonFormId+" input[name='name']").val(info.Name);
			//性别转换：1-M ； 2-F
			if (info.Gender == 1) {
				ZKUI.Combo.get("gender${uuid}").combo.setComboValue("M");
			} else if (info.Gender == 2){
				ZKUI.Combo.get("gender${uuid}").combo.setComboValue("F");
			}

			var birthday = info.Birthday;//生日
			birthday = birthday.substring(0,4)+"-"+birthday.substring(4,6)+"-"+birthday.substring(6,8);
			ZKUI.Input.get('birthday${uuid}').setDate(new Date(birthday));
			ZKUI.Combo.get("certType${uuid}").combo.setComboValue(2);
			$(editPersonFormId+" input[name='certNumber']").val(info.ID_Num);
			$(editPersonFormId+" input[name='idCard']").val(info.ID_Num);
			var nationName = ZKUI.Combo.get("attrValue2${uuid}").combo.getOptionByIndex(info.Nation).value
			ZKUI.Combo.get("attrValue2${uuid}").combo.setComboValue(nationName);
			$(editPersonFormId+" input[name='attrValue12']").val(info.Address);
			$(editPersonFormId+" #id_img_pers").attr("src","data:image/gif;base64,"+info.PhotoJPG);
			$(editPersonFormId+" #personIdPhoto").val(info.PhotoJPG);
			$(editPersonFormId+" #personPhoto").val("");
			$(editPersonFormId+" #idCardPhysicalNo").val(info.SN_Num);//物理卡号
			if(persParams['pers.IDReadMode'] == "1")
			{
				$("input[name='multiCards_0']").val(data.identityCardInfo.SN_Num);//物理卡号
			}
			$(".stopReadIDCardByTcpReader").click();
		}
	};
	$.ajax({
		type: "POST",
		url: "accReader.do?startReaderIDCard&readerId="+value,
		data: {},
		dataType: "json",
		async: false,
		success: function(result)
		{
			if(result[sysCfg.ret] == sysCfg.success)
			{
				//定时器
				readCardTimer = setTimeout(function(){
					$(".stopReadIDCardByTcpReader").click();
				}, 60*1000);
				if(result[sysCfg.data] != -1)
				{
					stopPost = setInterval(function(){
						$.ajax({
							url:"accDevice.do?getReadIDCardInfo",
							type:"post",
							dataType:"json",
							data:{
								cmdId:result[sysCfg.data],
								type:"identityCard"
							},
							success: function (retData)
							{
								if(retData[sysCfg.ret] == sysCfg.success)
								{
									var data = retData[sysCfg.data];
									valueBackfill(data);
								}
							}
						});
					}, 2000);
				}
				else
				{
					stopPost = setInterval(function(){
						$.get("accTransaction.do?readerCardNo", {readerIds: value, type: "IDCard", time: time}, function (result) {
							time = result[sysCfg.data].time;
							if(result[sysCfg.ret] == sysCfg.success)
							{
								var data = result[sysCfg.data];
								if(data.issuedCardFlag)
								{
									openMessage(msgType.warning, "<@i18n 'pers_issueCard_cardHasBeenIssued'/>");
									lastIssuedCardTime = data.issuedCardTime;
								}
								if(data.cardNo != "" && data.cardNo != undefined)
								{
									//将定时器关闭
									clearTimeout(readCardTimer);
									valueBackfill(data);
								}
							}
						});
					},2000);
				}
			}
		}
	});
}

function stopSendRequest() {
	clearInterval(stopPost);// 停止发送请求
}
function stopReadIDCardByTcpReader(obj) {
	$(".stopReadIDCardByTcpReader").hide();
	$(".readIDCardByTcpReader").show();
	$(certNumberObj).parent().find(".wait").removeClass("wait");
	stopSendRequest();
}

function initBiometricsIcon() {
    var biometricsCount = $("#bioTemplateJson").val();
    var html = "";
    $("#icon_biometrics").empty();
    if(biometricsCount != ""){
        biometricsCount = eval("("+biometricsCount+")");
        if (!$.isEmptyObject(biometricsCount.fpList))
        {
            html += '<span class="icv-ico_fingerprint" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.faceList))
        {
            html += '<span class="icv-ico_metric" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.fvList))
        {
            html += '<span class="icv-ico_finger_vein" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.palmList) || !$.isEmptyObject(biometricsCount.vislightPalmList))
        {
            html += '<span class="icv-ico_palm" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.vislightList) || (biometricsCount.vislightPhotoList != undefined && biometricsCount.vislightPhotoList.length>0))
        {
            html += '<span class="icv-ico_biophoto" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
        if (!$.isEmptyObject(biometricsCount.irisList))
        {
            html += '<span class="icv-ico_iris" style="margin-${rightRTL!'right'}: 4px"></span>';
        }
    }
    $("#icon_biometrics").append(html);
	$("#detail_biometrics").show();
}
initBiometricsIcon();

function getBiometricsInfo()
{
	var data = $("#bioTemplateJson").val();
    var path = "skip.do?page=pers_person_personBiometricsInfo&bioData="+data;
	var opts = {
		path:path,
		width:540,
		height:500,
        title:"<@i18n 'common_op_detailInfo'/>"
	};
    DhxCommon.createWindow(opts);
}
var loadFlag = false;
var systemModules = "${systemModules}";
//ZKUI.ComboGrid.get("deptId${uuid}").combo.attachEvent("onChange", loadLevelByDept);
function loadLevelByDept() {
	if (ZKUI.ComboGrid.get("deptId${uuid}") != undefined) {
		var deptId = "${item.deptId}";
		if(deptId == "") {
			var first = this.rowsCol[0];
			if (first && first.idd) {
				deptId = first.idd;
			}
		}
		if(!loadFlag && deptId != "" && deptId != null) {
			firstLoadDeptLevel(deptId)
			loadFlag = true;
		}
	}
}
</script>
</#macro>