<#assign editPage = "true">
<#include "/public/template/editTemplate.html">
<#macro editContent>
<style>
#biometricsInfo td {
    text-align: center;
}
#biometricsInfo th {
    text-align: center;
    font-weight: bold;
}
</style>
<table class='tableStyle' id="biometricsInfo">
    <input type="hidden" id="bioData" value=""/>
    <input type="hidden" name="personIdPhotoBio" id="personIdPhotoBio" value="" oldValue=""/>
    <tr>
        <th><@i18n 'pers_person_templateBioType'/></th>
        <th><@i18n 'pers_person_templateVersion'/></th>
        <th><@i18n 'pers_person_biotemplateCount'/></th>
    </tr>
</table>
<div id="templateTabbar" class="dhx_cell_tabbar" style="height:200px;margin-top: 20px"></div>
<div id="vislightPhotoTab" style="overflow-y: auto;min-height: 165px;display: none"></div>
<div id="palmPhotoTab" style="overflow-y: auto;min-height: 165px;display: none"></div>

<script type='text/javascript'>
$("#bioData").val('${bioData!}');
var persPicExampleTipHtml = '<div class="persBioCommonTipClass zk-border-color">';
    persPicExampleTipHtml += '<div class="persImagTop"><div class="persImagTopLeft"><img class="persExamplePic" src="/images/persExamplePic/pers_example_pic.png"/>';
    persPicExampleTipHtml += '<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_description' /></span></div></div>';
    persPicExampleTipHtml += '<div class="persImagTopRight"><p><@i18n 'pers_examplePic_Tip' /></p><p><@i18n 'pers_examplePic_Tip1' /></p><p><@i18n 'pers_examplePic_Tip2' /></p>';
    persPicExampleTipHtml += '<p>' + "<@i18n 'pers_examplePic_Tip3' />" + '</p><p><@i18n 'pers_examplePic_Tip4' /></p><p>'+ "<@i18n 'pers_examplePic_Tip5' />" +'</p></div></div>';
    persPicExampleTipHtml += '<div class="persExamplePicErrorTitle"><span>' + "<@i18n 'pers_examplePic_error' />"+'</span></div>';
    persPicExampleTipHtml += '<div class="persImagBottom"><div class="persImagBottomLeft"><img class="persExamplePic" src="/images/persExamplePic/pers_excessiveSmile.png"/>';
    persPicExampleTipHtml += '<div class="persExamplePicTip"><span><@i18n 'pers_examplePic_error1' /></span></div></div><div class="persImagBottomCenter">';
    persPicExampleTipHtml += '<img class="persExamplePic" src="/images/persExamplePic/pers_lightTooDim.png"/><div class="persExamplePicTip"><span><@i18n 'pers_examplePic_error2' /></span></div></div>';
    persPicExampleTipHtml += '<div class="persImagBottomRight"><img class="persExamplePic" src="/images/persExamplePic/pers_faceTooSmall.png"/><div class="persExamplePicTip"><span><@i18n 'pers_examplePic_error3' /></span></div>';
    persPicExampleTipHtml += '</div></div></div>';
function initBioData()
{
    var bioData = $("#bioData").val();
    var html = "";
    var vislightPhotoHtml = "";
    var palmPhotoHtml = "";
    if(bioData != "") {
        bioData = eval("(" + bioData + ")");
    }
    else{
        bioData = {
            fpList:{},
            fvList:{},
            faceList:{},
            palmList:{},
            vislightList:{},
            vislightPhotoList:{},
            vislightPalmList:{},
            palmPhotoList:{},
            irisList:{}
        }
        $("#bioData").val(JSON.stringify(bioData));
    }
        if (!$.isEmptyObject(bioData.fpList))
        {
            var fpArray = getTemplateArray(bioData.fpList);
            html += getTemplateHtml(fpArray, "<@i18n 'pers_person_regFinger'/>");
        }
        else
        {
            html += '<tr><td><@i18n "pers_person_regFinger"/></td><td>-</td><td>-</td></tr>';
        }
        if (!$.isEmptyObject(bioData.faceList))
        {
            var faceArray = getTemplateArray(bioData.faceList);
            html += getTemplateHtml(faceArray, "<@i18n 'pers_person_infraredFace'/>");
        }
        else
        {
            html += '<tr><td><@i18n "pers_person_infraredFace"/></td><td>-</td><td>-</td></tr>';
        }
        if (!$.isEmptyObject(bioData.fvList))
        {
            var fvArray = getTemplateArray(bioData.fvList);
            html += getTemplateHtml(fvArray, "<@i18n 'pers_person_regVein'/>");
        }
        else
        {
            html += '<tr><td><@i18n "pers_person_regVein"/></td><td>-</td><td>-</td></tr>';
        }
        if (!$.isEmptyObject(bioData.palmList))
        {
            var palmArray = getTemplateArray(bioData.palmList);
            html += getTemplateHtml(palmArray, "<@i18n 'pers_person_metacarpalVein'/>");
        }
        else
        {
            html += '<tr><td><@i18n "pers_person_metacarpalVein"/></td><td>-</td><td>-</td></tr>';
        }
        if (!$.isEmptyObject(bioData.vislightPalmList))
        {
            var palmArray = getTemplateArray(bioData.vislightPalmList);
            html += getTemplateHtml(palmArray, "<@i18n 'pers_person_visiblePalm'/>");
        }
        else
        {
            html += '<tr><td><@i18n "pers_person_visiblePalm"/></td><td>-</td><td>-</td></tr>';
        }
        if (!$.isEmptyObject(bioData.vislightList))
        {
            var vislightArray = getTemplateArray(bioData.vislightList);
            html += getTemplateHtml(vislightArray, "<@i18n 'pers_person_visibleFace'/>");
        }
        else
        {
            html += '<tr><td><@i18n "pers_person_visibleFace"/></td><td>-</td><td>-</td></tr>';
        }
        if (!$.isEmptyObject(bioData.irisList))
        {
            var irisArray = getTemplateArray(bioData.irisList);
            html += getTemplateHtml(irisArray, "<@i18n 'pers_person_iris'/>");
        }
        else
        {
            html += '<tr><td><@i18n "pers_person_iris"/></td><td>-</td><td>-</td></tr>';
        }
        if (bioData.vislightPhotoList != undefined && bioData.vislightPhotoList.length>0)
        {
            vislightPhotoHtml += '<div id="bioPhotoTr" style="text-align:center;margin-top:8px;">';
            for (var index in bioData.vislightPhotoList)
            {
                vislightPhotoHtml += '<div style="display:inline-block;position: relative;"><img id="id_img_persBio" style="max-height: 140px;max-width: 120px;" src="'+ bioData.vislightPhotoList[index] + '" onerror="this.src=\'/images/userImage.gif\'"/>';
                var base64String = bioData.vislightPhotoList[index].substr(22);
                $("#personIdPhotoBio").val(base64String);
                $("#personIdPhotoBio").attr("oldValue", base64String);
            }
            vislightPhotoHtml += '<span class="persBioPicTip"><span class="pers_icv icv-ic_que zk-colors-fg-green" />';
            vislightPhotoHtml += persPicExampleTipHtml + '</span></div>';
            vislightPhotoHtml += '<div style="display: inline-block;"><div style="margin-${leftRTL!'left'}:10px;"><div id="personPhotoDivBio" style="display: none;">';
            vislightPhotoHtml += '<input id="personPhotoBio" name="personPhoto" type="file" accept="image/jpeg,image/jpg,image/png"  style="width:68px; cursor:pointer;" onchange="validPhotoFormat();"/></div>';
            vislightPhotoHtml += '<input id="personPhotoButtonBio" value="<@i18n 'pers_person_browse'/>"  name="personPhotoButtonBio" type="button" onClick="personPhotoBio.click()" class="button-form"/></div>';
            vislightPhotoHtml += '<div style="margin-${leftRTL!'left'}:10px;"><input type="button" name="delBioPhoto" class="button-form" value="<@i18n 'common_op_del' />" style="margin-top:10px;" onclick="delBioPhoto()"/></div></div>';
            vislightPhotoHtml += '</div>';
        }
        else
        {
            vislightPhotoHtml += '<div id="bioPhotoTr" style="text-align:center;margin-top:8px;">';
            vislightPhotoHtml += '<div style="display:inline-block;position: relative;"><img id="id_img_persBio" style="height: 140px;width: 120px;" src="/images/${attr("system.skin","")}/userImage.gif" onerror="this.src=\'/images/userImage.gif\'"></img>';
            vislightPhotoHtml += '<span class="persBioPicTip"><span class="pers_icv icv-ic_que zk-colors-fg-green" />';
            vislightPhotoHtml += persPicExampleTipHtml + '</span></div>';
            vislightPhotoHtml += '<div style="display: inline-block;"><div style="margin-${leftRTL!'left'}:10px;"><div id="personPhotoDivBio" style="display: none;">';
            vislightPhotoHtml += '<input id="personPhotoBio"  name="personPhoto" type="file" accept="image/jpeg,image/jpg,image/png"  style="width:68px; cursor:pointer;" onchange="validPhotoFormat();"/></div>';
            vislightPhotoHtml += '<input id="personPhotoButtonBio" value="<@i18n 'pers_person_browse'/>"  name="personPhotoButtonBio" type="button" onClick="personPhotoBio.click()" class="button-form"/></div>';
            vislightPhotoHtml += '<div style="margin-${leftRTL!'left'}:10px;"><input type="button" name="delBioPhoto" class="button-form" value="<@i18n 'common_op_del' />" style="margin-top:10px;" onclick="delBioPhoto()"/></div></div>';
            vislightPhotoHtml += '</div>';
        }
        if (bioData.palmPhotoList != undefined && bioData.palmPhotoList.length>0)
        {
            palmPhotoHtml += '<div style="text-align:center;margin-top:8px;">';
            for (var index in bioData.palmPhotoList)
            {
                palmPhotoHtml += '<img id="id_img_persPalmBio" style="max-height: 140px;max-width: 120px;" src="'+ bioData.palmPhotoList[index] + '" onerror="this.src=\'/images/palmImage.png\'"/>';
                //var base64String = bioData.palmPhotoList[index].substr(22);
            }
            palmPhotoHtml += '</div>';
        }
        else
        {
            palmPhotoHtml += '<div style="text-align:center;margin-top:8px;">';
            palmPhotoHtml += '<img id="id_img_persBio" style="height: 140px;width: 120px;" src="/images/${attr("system.skin","")}/palmImage.png" onerror="this.src=\'/images/palmImage.png\'"></img>';
            palmPhotoHtml += '</div>';
        }
    $("#biometricsInfo").append(html);
    $("#vislightPhotoTab").html(vislightPhotoHtml);
    $("#palmPhotoTab").html(palmPhotoHtml);
    popupPersTooliTip("#bioPhotoTr .pers_icv","#bioPhotoTr .persBioCommonTipClass");
}
initBioData();

function initTab()
{
    var tabbar = new dhtmlXTabBar("templateTabbar", "top");
    tabbar.setSkin(sysCfg.dhxSkin);
    tabbar.addTab("vislightPhotoTab", "<@i18n 'pers_person_cropFacePhoto'/>", "*");
    tabbar.cells("vislightPhotoTab").attachObject("vislightPhotoTab");
    tabbar.tabs("vislightPhotoTab").setActive();
    $("#palmPhotoTab").show();
    tabbar.addTab("palmPhotoTab", "<@i18n 'pers_person_vislightPalmPhoto'/>", "*");
    tabbar.cells("palmPhotoTab").attachObject("palmPhotoTab");
}
initTab();
/**
 *
 * @param: dist 问号图标选择器，用于定位到问号图标
 * @param: infoDiv 信息弹窗选择器，
 * 如：popupPersTooliTip("#bioPhotoTr .pers_icv","#bioPhotoTr .persBioCommonTipClass")
 **/
function popupPersTooliTip(dist, infoDiv) {
    // 问号图标添加鼠标经过事件
    $(dist).on("mouseover", function(e) {
        //计算弹出位置，这里自己重算
        var divWidth = $(".persBioCommonTipClass").width();
        var divHeight = $(".persBioCommonTipClass").height();
        var right = document.body.clientWidth - e.pageX;
        var top = divHeight - e.pageY;
        if((e.pageX - 10 - divWidth) < 0) {
            $(".persBioCommonTipClass").css("left",(e.pageX+10) + "px");
        } else {
            $(".persBioCommonTipClass").css("left","auto");
            $(".persBioCommonTipClass").css("right",right+10 + "px");
        }
        if(e.pageY - divHeight - 5 >  0) {
           top = e.pageY- divHeight- 5;
        }
        $(".persBioCommonTipClass").css("top",top + "px");
        $(".persBioCommonTipClass").css("visibility","visible");
        $(".persBioCommonTipClass").css("z-index",9999);
    })
    // 问号图标添加鼠标移出事件
    $(dist).on("mouseout", function(e) {
        $(".persBioCommonTipClass").css("visibility","hidden");
    })
    //信息弹窗移到body
    $(infoDiv).appendTo(document.body);
}

function getTemplateArray(bioData) {
    var array = {};
    for (var key in bioData)
    {
        var tempData = bioData[key];
        var version = tempData.version;
        var index = tempData.templateNoIndex;
        if (index == 0)
        {
            if (array[version] == undefined)
            {
                array[version] = 1;
            }
            else
            {
                array[version] = array[version] + 1;
            }
        }
    }
    return array;
}

function getTemplateHtml(templateArray, title) {
    var html = "";
    var versionCount = Object.keys(templateArray).length;
    html += '<tr><td rowspan="' + versionCount +'">' + title + '</td>';
    var count = 0;
    for (var key in templateArray)
    {
        var version = key;
        if (version.indexOf(".") < 0) {
            version += ".0";
        }
        if (count == 0)
        {
            html += '<td>' + 'V' + version + '</td><td>'+ templateArray[key] + '</td></tr>';
        }
        else
        {
            html += '<tr><td>' + 'V' + version + '</td><td>'+ templateArray[key] + '</td></tr>';
        }

    }
    return html;
}
function delBioPhoto()
{
    if($("#personIdPhotoBio").val() == "")
    {
        openMessage("warning", "<@i18n 'pers_person_delCropFaceMsg'/>");
    }
    else
    {
        var bioData = $("#bioData").val();
        if(bioData != "") {
            bioData = eval("(" + bioData + ")");
            bioData.vislightPhotoList=new Array();
            $("#bioData").val(JSON.stringify(bioData));
            $("#bioPhotoTr").empty();
            var html = "";
            html += '<div style="display:inline-block;position: relative;"><img id="id_img_persBio" style="height: 140px;width: 120px;" src="/images/${attr("system.skin","")}/userImage.gif" onerror="this.src=\'/images/userImage.gif\'"/>';
            html += '<span class="persBioPicTip"><span class="pers_icv icv-ic_que zk-colors-fg-green" />';
            html += persPicExampleTipHtml + '</span></div>';
            html += '<div style="display: inline-block;"><div style="margin-${leftRTL!'left'}:10px;"><div id="personPhotoDivBio" style="display: none;">';
            html += '<input id="personPhotoBio"  name="personPhoto" type="file" accept="image/jpeg,image/jpg,image/png"  style="width:68px; cursor:pointer;" onchange="validPhotoFormat();"/></div>';
            html += '<input id="personPhotoButtonBio" value="<@i18n 'pers_person_browse'/>"  name="personPhotoButtonBio" type="button" onClick="personPhotoBio.click()" class="button-form"/></div>';
            html += '<div style="margin-${leftRTL!'left'}:10px;"><input type="button" name="delBioPhoto" class="button-form" value="<@i18n 'common_op_del' />" style="margin-top:10px;" onclick="delBioPhoto()"/></div></div>';
            $("#bioPhotoTr").html(html);
            $("#personIdPhotoBio").val("");
            popupPersTooliTip("#bioPhotoTr .pers_icv","#bioPhotoTr .persBioCommonTipClass");
        }
    }
}
function submitBioInfo() {
    if($("#personIdPhotoBio").attr("oldValue") != $("#personIdPhotoBio").val())
    {
        $("input[name='cropPhotoBase64']").val($("#personIdPhotoBio").val());
        $("#bioTemplateJson").val($("#bioData").val());
        if($("#personIdPhotoBio").val() != "")
        {
            $("input[name='cropPhotoDel']").val("false");
            messageBox({messageType:"confirm", text: "<@i18n 'pers_person_cropFaceUsePhoto'/>", callback: function(result){
                if(result) {
                    $("#id_img_pers").attr("src", "data:image/jpg;base64,"+$("#personIdPhotoBio").val());
                   $("#personIdPhoto").val($("#personIdPhotoBio").val());
                   DhxCommon.closeWindow();
                }
                else{
                    DhxCommon.closeWindow();
                }
            }});
        }
        else{
            $("input[name='cropPhotoDel']").val("true");
            DhxCommon.closeWindow();
        }
    }
    else
    {
        DhxCommon.closeWindow();
    }
}

function validPhotoFormat()
{
    var value = $("#personPhotoBio").val();
    if (value != "") {
        var param = typeof param == "string" ? param.replace(/,/g, '|') : "png|jpe?g";
        if(value.match(new RegExp(".(" + param + ")$", "i")))
        {
            setPersPhotoPreview("Bio");
        }
        else
        {
            openMessage(msgType.error,"<@i18n 'pers_import_photoFormatError'/>");
            return;
        }
    }
}
</script>
</#macro>
<#macro buttonContent>
<button class='button-form' onclick="submitBioInfo();"><@i18n 'common_edit_ok'/></button>
<button class='button-form button-close' onclick="DhxCommon.closeWindow();"><@i18n 'common_op_close'/></button>
</#macro>