<#include "/public/template/editTemplate.html"> 
<#macro editContent>
<form id="${formId}" action="/persPerson.do?batchDeptChange" method="post" enctype="multipart/form-data">
	<input type="hidden" name="ids" value="${(ids)!}"/>
	<input type="hidden" name="flag" value="false"/>
	<table class="tableStyle">
		<colgroup>
			 <col class="col1" width="100px"/>
			 <col class="col2" width="*"/>
		</colgroup>
		<tr>
			<th><label><@i18n 'pers_person_selectedPerson'/></label><span class="required">*</span></th>
			<th >
				<textarea name="pins" style="width:150px;height: 55px" readonly >${selectedPerson}</textarea>
			</th>
		</tr>
		<tr>
			<th >
				<@i18n 'pers_person_batchToDept'/><span class="required">*</span>
			</th>
			<th>
				<@ZKUI.ComboGrid id="deptId${uuid}" width="148" queryField="name" hideLabel="true" name="deptId"
				grid_showColumns="ra,name,parentName" grid_vo="com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentSelectItem"
				grid_query="authDepartment.do?listSelect"/>
			</th>
		</tr>
		<tr>
			<th>
				<@i18n 'pers_person_changeReason'/>
			</th>
			<th >
				<textarea style="width:150px;height: 55px" name="changeReason" maxlength="100"></textarea>
			</th>
		</tr>
	</table>
</form>
<script type="text/javascript">
(function(){
	$("#${formId}SaveContinue").remove();
	$("#${formId}").validate( {
		debug : true,
		rules : {
		     "deptId" : {
				required : true
			}
	    },
	  	submitHandler : function() {
	  		var hasPermission = false;
			<@ZKUI.Permission name="pers:personDepartment:changeLevel">
				hasPermission = true;
				messageBox({
					messageType : "confirm",
					text : "<@i18n 'pers_dept_changeLevel'/>",
					callback : function(result)
					{
						if (result){
							$("input[name='flag']").val("true");
						}
						<@submitHandler callBackFun="reloadPersonTreeAndGrid()"/>
					}
				});
			</@ZKUI.Permission>
			if(!hasPermission) {
				$("input[name='flag']").val("false");
				<@submitHandler callBackFun="reloadPersonTreeAndGrid()"/>
			}
		}
	});
})();
</script>
</#macro>