<#assign gridName="persPersonGrid${uuid!}">
<script src="js/registerClient.js"></script>
<script type="text/javascript">
function persPersonDeptTreeClick(id) {
	ZKUI.Grid.reloadGrid("${gridName}", function(){}, {deptId:id,inDeptId:""});
}

function personAdd(id, bar, opt, tid) {
    if (bar) {
        var treeId = "tree${uuid!}";
        var deptId = ZKUI.Tree.get(treeId).tree.getSelectedItemId();
        var deptName = ZKUI.Tree.get(treeId).tree.getSelectedItemText();
        var opts = $.extend({},opt,{
            path : id +"&deptId="+deptId+"&deptName="+deptName.replace(/\(\d+\)/,""),
            gridName : "${gridName}"
        });
        DhxCommon.createWindow(opts);
    }
}

function reloadPersonTreeAndGrid(){
	if (ZKUI.Tree.get("tree${uuid!}")) {
		ZKUI.Tree.get("tree${uuid!}").tree.refreshItem();
	}
    ZKUI.Grid.reloadGrid("${gridName}");
}

function afterImportPersPerson() {
    ZKUI.Grid.reloadGrid("${gridName}", function(){}, null);
}

// 导出人员信息
function exportPersRecord(id, bar, opts) {
	if (bar) {
		var gridName = bar.gridName;

		// 导出人员信息
		if (!opts.typeStr||opts.typeStr=="")
		{
			opts.path = opts.path || "persPerson.do?exportPage&gridName=" + gridName + "&actionName=" + encodeURIComponent(id);
		}
		// 导出人员信息模板
		else {
			opts.path = opts.path || "persPerson.do?exportPage&gridName=" + gridName + "&actionName=" + encodeURIComponent(id) + "&typeStr=" + opts.typeStr + "&tableNameParam=<@i18n "pers_export_templateFileName"/>";
		}

		if(opts.maxExportCount) {
			opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
		}
		opts.width = opts.width || 450;
		opts.height = opts.height || 150;
		DhxCommon.createWindow(opts);
	}
}

function exportPersPersonPhoto(id, bar, opts) {
    if (bar) {
        var gridName = bar.gridName;
        opts.path = opts.path || "skip.do?page=pers_person_opExportPhoto&gridName=" + gridName + "&actionName=" + encodeURIComponent(id);
        if(opts.maxExportCount) {
            opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
        }
        opts.width = opts.width || 450;
        opts.height = opts.height || 230;
        DhxCommon.createWindow(opts);
    }
}

function openImportPhotoSelect(id, bar, opts) {
    if (bar) {
        var gridName = bar.gridName;
        opts.path = "skip.do?page=pers_person_opImportPhotoSelect&facialTemplateEnable=" + persParams['pers.facialTemplate.enable'];
        opts.width = opts.width || 450;
        opts.height = opts.height || 230;
        DhxCommon.createWindow(opts);
    }
}

function exportPersPersonBiotemplate(id, bar, opts)
{
	if (bar) {
        var gridName = bar.gridName;
        opts.path = opts.path || "skip.do?page=pers_person_opExportBiotemplate&gridName=" + gridName + "&actionName=" + encodeURIComponent(id);
        if(opts.maxExportCount) {
            opts.path = opts.path + "&maxExportCount=" + opts.maxExportCount;
        }
        opts.width = opts.width || 550;
        opts.height = opts.height || 250;
        DhxCommon.createWindow(opts);
    }
}

function exportLeavePersonTemplate() {
    $("#leavePersonImportTemplateFrom").ajaxSubmit({
        async:false,
        success: function(result){
        }
    });
}

/**
* 根据人员id获取部门id
* @author:	chainzen.chen
* @date:	2018-11-17
* @param: id 人员id
*/
function findDeptBypersonId(id) {
	if(ZKUI.Tree.get("tree${uuid!}").findPositionById) {
		$.ajax({
			type:"get",
			url:"persPerson.do?getDeptIdByPersonId",
			data:{
				personId:id
			},
			success: function(res) {
				if(res.ret === sysCfg.success) {
					if(res.data) {
						ZKUI.Tree.get("tree${uuid!}").findPositionById(res.data);
					}
				}
			},
			error:function(error) {
				console.log(error);
			}
		})
	}
}

function convertAppAuthorization(v) {
	v = v + '';
	if (v == '1' || v == "true") {
		return '<div class="icon_state_yes"></div>';
	}
	else {
		return '<div class="icon_state_no"></div>';
	}
}

function persOpenProcess(id, bar, opt, faceTemplateFlag) {
	if(bar){
		var gridName = bar.gridName||"gridbox";
		var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
		if(ids == "") {
			messageBox({messageType:"alert",text:"<@i18n 'common_prompt_selectObj'/>"});
		}
		else {
			deleteConfirm(function(result){
				if(result){
					if (faceTemplateFlag == "1" && persParams['pers.facialTemplate.enable'] != '1') {
						messageBox({messageType:"alert",text:"<@i18n 'pers_person_disableFaceTemplate'/>"});
						return;
					}
					var param = splitURL(fillParamsFromGrid(gridName, ids, id));
					param.data.ids = ids;
					var opts = {
					dealPath: param.url,//进度处理请求
					data :param.data,
					type: "single",//进度类型，取值single,public,custom
					height:250,//弹窗高度
					useReq:true,//使用请求参数作为进度条参数
					autoClose:true,//自动关闭
					delay:5,//自动关闭时间(5秒后自动关闭)
					title:"<@i18n 'common_progress_proCmd'/>" //弹窗标题
					}
					openProcess(opts);
				}
			},"<@i18n 'common_prompt_executeOperate'/>".format(opt.text))
		}
	}
}

function batchFaceTemplate(id, bar, opt) {
	persOpenProcess(id, bar, opt, "1");
}

</script>
<!--导出模版表单-->
<form style="display: none" method="POST" action="persPerson.do?exportLeavePersonTemplate" id="leavePersonImportTemplateFrom" enctype="multipart/form-data">
	<input type="hidden" name="jsonColumn" id="jsonColumn${uuid}"/>
	<input type="hidden" name="reportType" value="XLS"/>
	<input type="hidden" name="tableNameParam" value="<@i18n "pers_dimission_importTemplate"/>"/>
</form>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
    			<td valign="middle">
					<@ZKUI.Input name="deptName"  maxlength="30" title="pers_dept_deptName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="pin"  maxlength="30" title="pers_person_pin" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
 			<tr>
 				<td valign="middle">
					<@ZKUI.Input name="email"  maxlength="30" title="pers_person_email" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="mobilePhone"  maxlength="30" title="pers_person_mobilePhone" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="likeCardNo"  maxlength="30" title="pers_card_cardNo" type="text"/>
				</td>
			</tr>
			<tr>
				<td valign="middle">
					<@ZKUI.Combo empty="true" name="exceptionFlag" title="common_status">
			    		<option value="0"><@i18n 'common_commStatus_normal'/></option>
			    		<option value="1"><@i18n 'pers_person_cardDuress'/></option>
			    		<option value="2"><@i18n 'pers_person_pwdException'/></option>
			    		<option value="3"><@i18n 'pers_person_pinException'/></option>
			    	</@ZKUI.Combo>
				</td>
				<td valign="middle">
					<@ZKUI.Combo empty="true" name="gender" title="common_gender" key="sex"/>
				</td>
				<td valign="middle">
					<@ZKUI.ComboTree title="common_verifyMode_entiy" url="persPerson.do?verifyModeTree" name="verifyMode" isTree="false"/>
				</td>
			</tr>
			<#if Application['system.productCode'] != 'ZKBioAccess'>
			<tr>
				<td valign="middle">
					<@ZKUI.Input name="positionName" maxlength="100" title="pers_position_name" type="text"/>
				</td>
			</tr>
			</#if>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;">
		<@ZKUI.Cell width="240" treeId="tree${uuid!}" hideArrow="true">
			<@ZKUI.Tree dynamic="true" id="tree${uuid!}" parentIdUri="authDepartment.do?getParentsIds" url="authDepartment.do?dynaTree&showPersonCount=true"  onClick="persPersonDeptTreeClick"></@ZKUI.Tree>
		</@ZKUI.Cell>
		<@ZKUI.Cell hideHeader="true">
		    <@ZKUI.Toolbar>
		    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="pers:person:refresh"/>
		    	<@ZKUI.ToolItem id="persPerson.do?edit" text="common_op_new" width="1000" height="670" img="comm_add.png" action="personAdd" permission="pers:person:add"/>
				<@ZKUI.ToolItem type="more" text="pers_person_change" img="pers_changeDept.png">
					<@ZKUI.ToolItem id="persPerson.do?batch&type=deptChange" text="pers_person_departmentChange" title="pers_person_departmentChange" width="370" height="320" img="pers_changeDept.png" action="commonOpenOperate" permission="pers:person:deptChange"/>
					<@ZKUI.ToolItem id="persPerson.do?batch&type=positionChange" text="pers_position_change" title="pers_position_change" width="370" height="320" img="pers_changePosition.png" action="commonOpenOperate" permission="pers:person:batchPositionChange"/>
					<@ZKUI.ToolItem id="persLeavePerson.do?edit&pins=(pin)&names=(name)" text="pers_person_leave" title="pers_dimission_writeInfomation" width="500" height="270" img="pers_personDimission.png" action="commonOpenOperate" permission="pers:person:leave"/>
				</@ZKUI.ToolItem>

				<@ZKUI.ToolItem type="more" text="common_op_del" img="comm_del.png">
					<@ZKUI.ToolItem id="persPerson.do?del&pins=(pin)" text="pers_common_delPerson" img="comm_del.png" action="commonDel" callback="reloadPersonTreeAndGrid" permission="pers:person:del"/>
					<@ZKUI.ToolItem id="persPerson.do?getBioTemplateView" text="pers_person_delBioTemplate" title="pers_person_delBioTemplate" width="350" height="250" img="comm_del.png" action="commonOpenOperate" permission="pers:person:delBioTemplate"/>
				</@ZKUI.ToolItem>

				<@ZKUI.ToolItem id="persPerson.do?resetSelfPwd&pins=(pin)" text="pers_person_resetSelfPwd" img="comm_set.png" action="commonOperate" permission="pers:person:resetSelfPwd" isShow="false"/>
				<@ZKUI.ToolItem type="more" text="common_op_export" img="comm_export.png">
					<@ZKUI.ToolItem id="persPerson.do?export" type="export" action="exportPersRecord" width="650" height="610" text="pers_export_personInfo" title="pers_export_personInfo" permission="pers:person:export" />
					<@ZKUI.ToolItem id="persBioTemplate.do?export" type="export" action="exportPersPersonBiotemplate" text="pers_export_personBioTemplate" title="pers_export_personBioTemplate" permission="pers:bioTemplate:export" />
					<@ZKUI.ToolItem id="persPerson.do?exportPhoto" type="export" action="exportPersPersonPhoto" text="pers_export_personPhoto" title="pers_export_personPhoto" permission="pers:person:exportPhoto" />
				</@ZKUI.ToolItem>
				<@ZKUI.ToolItem type="more" text="common_op_import" img="comm_import.png">
					<@ZKUI.ToolItem id="persPerson.do?import" type="import" showImportProcess="true" updateExistData="true" text="pers_import_personInfo" onFinish="reloadPersonTreeAndGrid" title="pers_import_personInfo" width="500" height="250" permission="pers:person:import" />
					<@ZKUI.ToolItem id="persBioTemplate.do?import" type="import" showImportProcess="true" text="pers_import_biologicalTemplate" onFinish="afterImportPersPerson" title="pers_import_biologicalTemplate" width="500" height="250" permission="pers:bioTemplate:import" />
					<@ZKUI.ToolItem id="skip.do?page=pers_person_opImportPhotoSelect" action="openImportPhotoSelect" text="pers_import_personPhoto" title="pers_import_personPhoto" img="comm_import.png" width="450" height="200" permission="pers:person:importPhoto" />
					<@ZKUI.ToolItem type="import" id="persPerson.do?importLeavePerson" text="pers_dimission_import"  titile="pers_dimission_import" showImportProcess="true" onFinish="reloadPersonTreeAndGrid" width="500" height="250" permission="pers:person:importLeavePerson" />
					<@ZKUI.ToolItem id="persPerson.do?exportTemplate" type="export" action="exportPersRecord" typeStr="XLS" width="650" height="450" text="pers_export_personInfoTemplate" img="common_download.png" title="pers_export_personInfoTemplate" permission="pers:person:exportTemplate" />
					<@ZKUI.ToolItem type="export" action="exportLeavePersonTemplate" id="persPerson.do?exportLeavePersonTemplate" text="pers_dimission_downloadTemplate" img="common_download.png" title="pers_dimission_downloadTemplate" permission="pers:person:exportLeavePersonTemplate" />
				</@ZKUI.ToolItem>
				<@ZKUI.ToolItem type="more" img="comm_moreActions.png">
					<@ZKUI.ToolItem id="skip.do?page=pers_person_opDataCount" text="pers_person_dataCount" title="pers_person_dataCount" width="400" height="450" img="pers_dataCount.png" action="commonAdd" permission="pers:person:dataCount"/>
					<@ZKUI.ToolItem id="persPerson.do?cardPrint" text="pers_person_cardprint" title="pers_person_cardprint" width="1000" height="725" img="pers_makeCard.png" action="commonOpenOperate" permission="pers:person:cardPrint" isShow="JAVA#persCardPrintTemplateController.isShowCardPrint"/>
					<@ZKUI.ToolItem id="persPerson.do?resetSelfPwd&pins=(pin)" text="pers_person_resetSelfPwd" img="comm_set.png" action="commonOperate" permission="pers:person:resetSelfPwd"/>
					<@ZKUI.ToolItem id="persPerson.do?viewBioPhoto" text="pers_person_cropFaceShow" title="pers_person_cropFaceShow" width="1165" height="680" img="comm_uploadTransaction.png" action="commonOpenOperate" permission="pers:person:viewBioPhoto"/>
					<@ZKUI.ToolItem id="persPerson.do?enabledPersCredential&pins=(pin)" text="common_enable" img="comm_enable.png" selectNumber="50" action="commonOperate" permission="pers:person:enabled"/>
					<@ZKUI.ToolItem id="persPerson.do?disableCredential&pins=(pin)" text="common_disable" img="comm_disable.png" selectNumber="50" action="commonOperate" permission="pers:person:disable"/>
					<@ZKUI.ToolItem id="persPerson.do?enabledApplogin&pins=(pin)" text="pers_applogin_enabled" img="comm_enable.png" action="commonOperate" permission="pers:person:enabledApplogin"/>
					<@ZKUI.ToolItem id="persPerson.do?disableApplogin&pins=(pin)" text="pers_applogin_disable" img="comm_disable.png" action="commonOperate" permission="pers:person:disableApplogin"/>
					<@ZKUI.ToolItem id="persPerson.do?batchFaceTemplate&pins=(pin)" text="pers_person_extractFaceTemplate" img="pers_extractFaceTemplate.png" action="batchFaceTemplate" permission="pers:person:batchFaceTemplate"/>
					<@ZKUI.ToolItem id="persPerson.do?syncPersonAcms&pins=(pin)" text="pers_person_syncAcms" img="comm_syncAllData.png" action="persOpenProcess" permission="pers:person:syncAcms" isShow="#language!='zh_CN'"/>
				</@ZKUI.ToolItem>
			</@ZKUI.Toolbar>
		    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.pers.vo.PersPersonItem" onRowSelect="findDeptBypersonId" query="persPerson.do?list" showColumns="!gender,birthday,mobilePhone,email,positionName,exceptionFlag" />
    	</@ZKUI.Cell>
	</@ZKUI.Layout>
</@ZKUI.GridBox>