<#assign gridName="persIssueCardGrid${uuid!}">
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
	    		<td valign="middle">
					<@ZKUI.Input name="cardNo"  maxlength="30" title="pers_card_cardNo" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Combo empty="true" name="operateType" title="pers_issueCard_operate">
			    		<option value="1"><@i18n 'pers_issueCard_entity'/></option>
						<option value="6"><@i18n 'pers_lossCard_entity'/></option>
						<option value="7"><@i18n 'pers_revertCard_entity'/></option>
						<option value="11"><@i18n 'pers_card_write'/></option>
						<option value="12"><@i18n 'pers_card_back'/></option>
						<option value="13"><@i18n 'pers_card_change'/></option>
			    	</@ZKUI.Combo>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
 			<tr>
				<td valign="middle">
					<@ZKUI.Input type="datetime" endId="endId${uuid}" id="startId${uuid}" name="opTimeBegin"  maxlength="30" todayRange="start" title="base_opLog_opTime" max="today" noOverToday="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="datetime" id="endId${uuid}" name="opTimeEnd"  maxlength="30" hideLabel="true" todayRange="end" title="common_to" max="today" noOverToday="true" readonly="false"/>
				</td>
 			</tr>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="pers:issueCard:refresh"/>
		<@ZKUI.ToolItem id="persIssueCard.do?export" text="common_op_export" img="comm_export.png" type="export" permission="pers:issueCard:export"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.pers.vo.PersIssueCardItem" query="persIssueCard.do?list"/>
</@ZKUI.GridBox>