<#assign gridName="persPositionGrid${uuid!}">
<#assign gridName="authDepartmentGrid${uuid!}">
<script type="text/javascript">
function persPositionTreeClick(id) {
	ZKUI.Grid.reloadGrid("${gridName}", function(){}, {parentId:id});
}

function reloadPositionTreeAndGrid(){
    ZKUI.Tree.get("tree${uuid!}").tree.refreshItem();
    ZKUI.Grid.reloadGrid("${gridName}");
}

function exportPositionTemplate() {
    var gridName = "${gridName}";
    var grid = ZKUI.Grid.get(gridName);
    var fieldData = grid.getDisplayField();
    document.getElementById("jsonColumn${uuid}").value = JSON.stringify(fieldData.columns);
    $("#positionImportTemplateFrom").ajaxSubmit({
        async:false,
        success: function(result){
        }
    });
}

function findPositionById(id) {
	if(ZKUI.Tree.get("tree${uuid!}").findPositionById) {
		ZKUI.Tree.get("tree${uuid!}").findPositionById(id);
	}
}

</script>
<!--导出模版表单-->
<form style="display: none" method="POST" action="persPosition.do?exportTemplate" id="positionImportTemplateFrom" enctype="multipart/form-data">
	<input type="hidden" name="jsonColumn" id="jsonColumn${uuid}"/>
	<input type="hidden" name="reportType" value="XLS"/>
	<input type="hidden" name="tableNameParam" value="<@i18n "pers_position_importTemplate"/>"/>
</form>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
    			<td valign="middle">
					<@ZKUI.Input name="code"  maxlength="10" title="pers_position_code" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="name"  maxlength="30" title="pers_position_name" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
    </@ZKUI.Searchbar>
    <@ZKUI.Layout style="position:absolute; top:70px;bottom:0px;left:0px;right:0px;height:auto;">
		<@ZKUI.Cell width="240" treeId="tree${uuid!}" hideArrow="true">
			<@ZKUI.Tree id="tree${uuid!}" url="persPosition.do?tree" onClick="persPositionTreeClick"></@ZKUI.Tree>
		</@ZKUI.Cell>
		<@ZKUI.Cell hideHeader="true">
		    <@ZKUI.Toolbar>
		    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="pers:position:refresh"/>
		    	<@ZKUI.ToolItem id="persPosition.do?edit" text="common_op_new" img="comm_add.png" action="commonAdd" permission="pers:position:add"/>
		    	<@ZKUI.ToolItem id="persPosition.do?del&name=(name)" text="common_op_del" img="comm_del.png" action="commonDel" callback="reloadPositionTreeAndGrid" permission="pers:position:del"/>
				<@ZKUI.ToolItem id="persPosition.do?export" type="export" permission="pers:position:export"></@ZKUI.ToolItem>
				<@ZKUI.ToolItem type="more" text="common_op_import" img="comm_import.png">
				<@ZKUI.ToolItem type="import" id="persPosition.do?import" showImportProcess="true" onFinish="reloadPositionTreeAndGrid" width="500" height="250" permission="pers:position:import"/>
				<@ZKUI.ToolItem type="export" action="exportPositionTemplate" id="persPosition.do?exportTemplate" img="common_download.png" text="pers_position_downloadTemplate" title="pers_position_downloadTemplate" permission="pers:position:exportTemplate"/>
				</@ZKUI.ToolItem>
			</@ZKUI.Toolbar>
		    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.pers.vo.PersPositionItem" onRowSelect="findPositionById" query="persPosition.do?list"/>
		</@ZKUI.Cell>
	</@ZKUI.Layout>
</@ZKUI.GridBox>