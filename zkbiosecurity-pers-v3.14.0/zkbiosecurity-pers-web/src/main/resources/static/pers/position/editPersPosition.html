<#assign editPage="true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>

<form action="/persPosition.do?save" method="post" id="${formId}" enctype="multipart/form-data">
	<input type="hidden" name="id" value="${(item.id)!}"/>
	<table class="tableStyle">
		<tr>
			<th><label><@i18n 'pers_position_code'/></label><span class='required'>*</span></th>
			<td><input name="code" type="text" value="${(item.code)!}" maxlength="30"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_position_name'/></label><span class="required">*</span></th>
			<td><input name="name" type="text" value="${(item.name)!}" maxlength="100"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_position_sortNo'/></label><span class="required">*</span></th>
			<td><input name="sortNo" type="text" value="${(item.sortNo)!99999}"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_position_parentName'/></label></th>
			<td>
				<@ZKUI.ComboTree id="position${uuid}" width="148" type="radio" url="persPosition.do?tree" disableNode="${(item.id)!}" value="${(item.parentId)!}"  hideLabel="true" name="parentId"/>
			</td>
		</tr>
	</table>
</form>

<script type='text/javascript'>
jQuery.validator.addMethod("validCode", function(value, element){
	var pattenChar = /^[A-Za-z0-9]{1,30}$/;
	return this.optional(element) || (pattenChar.test(value));
}, function(){
	return "<@i18n 'common_prompt_codeMsg'/>";
});

jQuery.validator.addMethod("spaceValid", function(value, element){
	var pattenChar1 = /(^\S)/;
	var pattenChar2 = /(\S$)/;
	return this.optional(element) || (pattenChar1.test(value)&& pattenChar2.test(value));
}, function(){
	return "<@i18n 'pers_position_nameNoSpace'/>";
});

(function() {
    var editPositionFormId = "#${formId}";
    if("${(item.code)!}" != ""){
        $(editPositionFormId+" input[name='code']").attr("readonly","readonly");

        var positionTree = ZKUI.ComboTree.get("position${uuid}").tree;
        positionTree.attachEvent("onClick",function(id){
            var subIds = "${(item.id)!}," + positionTree.getAllSubItems("${(item.id)!}");
            if(subIds.indexOf(id) != -1){
                openMessage(sysCfg.warning, "<@i18n 'pers_position_parentMenuMsg'/>");
            }
            return true;
        });
    }
    $(editPositionFormId).validate({
		debug : true,
		rules :
		{
			"name" :
			{
				required: true,
				spaceValid: true,
				unInputChar: true,
                persPositionNameValid : true,
			},
			"code" :
			{
				required: true,
				validCode:true,
				overRemote : ["persPosition.do?isExist", "${(item.code)!}"]
			},
			"sortNo" :
			{
                required: true,
                digits:true,
                range:[1,999999]
			}
		},
		messages:{
			
		},
		submitHandler : function()
		{
			<@submitHandler callBackFun="reloadPositionTreeAndGrid()"/>
		}
	});
})();

jQuery.validator.addMethod("persPositionNameValid", function(value, element){
    var name = $("#${formId} input[name='name']").val();
    var parentPositionId = $("#${formId} input[name='parentId']").val();
    var id = $("#${formId} input[name='id']").val();
    var flag = true;
    $.ajax({
        url:"persPosition.do?isNameExist",
        type:"post",
        dataType:"json",
        async :false,
        data:{
            "id": id,
            "parentId": parentPositionId,
            "name": name
        },
        success: function (result) {
            flag = result.data;
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            messageBox({messageType: "alert", title: "${common_prompt_title}", text: "${common_prompt_serverError}"});
        }
    });
    return flag;
}, function(){
    return "<@i18n 'pers_position_nameExist'/>";
});
</script>
</#macro>