<#include '/public/template/editTemplate.html'>
<#macro editContent>
<form action="persLeavePerson.do?save" method="post" id="${formId}" enctype="multipart/form-data">
	<input type="hidden" name="personIds" value="${(ids)!}"/>
	<input type="hidden" name="pin" value="${(pin)!}"/>
	<input type="hidden" name="name" value="${(name)!}"/>
	<input type="hidden" name="id" value="${(item.id)!}"/>
	<table class="tableStyle">
		<tr>
			<th><label><@i18n 'pers_dimission_date'/></label><span class="required">*</span></th>
			<td><@ZKUI.Input type="date" value="${((item.leaveDate)?string('yyyy-MM-dd'))!}"  readonly="readonly" style="width:250px;box-sizing: border-box;" max="today" min="${((item.hireDate)?string('yyyy-MM-dd'))!}" name="leaveDate" hideLabel="true" today="true"/></td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_dimission_type'/></label><span class="required">*</span></th>
			<td>
				<@ZKUI.Combo empty="true" width="250" name="leaveType" hideLabel="true" value="${(item.leaveType)!}" key="PersLeaveType"/>
			</td>
		</tr>
		<tr>
			<th><label><@i18n 'pers_dimission_reason'/></label></th>
			<td><textarea name="leaveReason" maxlength="200" style="width:250px;height:70px;" onkeydown="removeLeaveReasonEnterEvent(event)">${(item.leaveReason)!}</textarea>
			</td>
		</tr>
		<@ZKUI.Permission name="pers:personnallist">
		<#if "${(item.id)!}" == "">
		<tr>
			<th><label><@i18n 'pers_dimission_forbidden'/></label></th>
			<td>
				<@ZKUI.Input hideLabel="true" type="checkbox" name="forbidden" trueValue="1" falseValue="0" eventCheck="true"  value="1"/>
			</td>
		</tr>
		</#if>
		</@ZKUI.Permission>
	</table>
</form>

<script type="text/javascript">
(function() {
	$("#${formId}SaveContinue").remove();
	$("#${formId}").validate( {
		debug : true,
		rules :
		{
			"leaveDate" :
			{
				required : true
			},
			"leaveType" :
			{
				required : true
			},
            "leaveReason" :
			{
                unInputChar: true
			}
		},
		submitHandler : function()
		{
		    <#if (ids)?exists>
				<@submitHandler callBackFun="reloadPersonTreeAndGrid()"/>
			<#else>
        		<@submitHandler/>
			</#if>
		}
	});
})();
//屏蔽回车事件
    function removeLeaveReasonEnterEvent(e) {
        var code;
        if (!e) var e = window.event;
        if (e.keyCode) code = e.keyCode;
        else if (e.which) code = e.which;
        if (code == 13 && window.event) {
            e.returnValue = false;
        } else if (code == 13) {
            e.preventDefault();
        }
    }
</script>
</#macro>