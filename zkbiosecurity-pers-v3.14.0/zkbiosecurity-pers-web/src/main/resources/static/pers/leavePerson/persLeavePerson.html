<#assign gridName="persLeavePersonGrid${uuid!}">
<script type="text/javascript">
function persReinstated(gridName, domObj, leaveId) {
	var opts = {//弹窗配置对象
		path: "persPerson.do?edit&leaveId=" + leaveId,//设置弹窗路径
		width: 950,//设置弹窗宽度
		height: 650,//设置弹窗高度
		title: "<@i18n 'pers_leavePerson_reinstated'/>",//设置弹窗标题
    	editPage:true
	};
	DhxCommon.createWindow(opts);
}
function reloadLeavePersonGrid(){
	ZKUI.Grid.reloadGrid("${gridName}");
}

function exportLeavePersonTemplate() {
    var gridName = "${gridName}";
    var grid = ZKUI.Grid.get(gridName);
    var fieldData = grid.getDisplayField();
    document.getElementById("jsonColumn${uuid}").value = JSON.stringify(fieldData.columns);
    $("#leavePersonImportTemplateFrom").ajaxSubmit({
        async:false,
        success: function(result){
        }
    });
}

</script>
<@ZKUI.GridBox gridName="${gridName}">
    <@ZKUI.Searchbar>
    	<@ZKUI.SearchTop>
    		<tr>
				<td valign="middle">
					<@ZKUI.Input name="pin"  maxlength="30" title="pers_person_pin" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="likeName"  maxlength="30" title="pers_person_wholeName" type="text"/>
				</td>
				<td valign="middle">
					<@ZKUI.Input name="deptName"  maxlength="30" title="pers_dept_deptName" type="text"/>
				</td>
			</tr>
    	</@ZKUI.SearchTop>
 		<@ZKUI.SearchBelow>
 			<tr>
				<td valign="middle">
					<@ZKUI.Combo empty="true" name="leaveType" title="pers_dimission_type" key="PersLeaveType" />
				</td>
				<td valign="middle">
					<@ZKUI.Input type="date" endId="endId${uuid}" id="startId${uuid}" name="opTimeBegin"  maxlength="30" todayRange="start" title="pers_dimission_date" max="today" noOverToday="true" readonly="false"/>
					&nbsp;&nbsp;<@i18n 'common_to'/>&nbsp;&nbsp;
					<@ZKUI.Input type="date" id="endId${uuid}" name="opTimeEnd"  maxlength="30" hideLabel="true" todayRange="end" title="common_to" max="today" noOverToday="true" readonly="false"/>
				</td>
 			</tr>
 		</@ZKUI.SearchBelow>
    </@ZKUI.Searchbar>
    <@ZKUI.Toolbar>
    	<@ZKUI.ToolItem id="refresh" text="common_op_refresh" img="comm_refresh.png" action="commonRefresh" permission="pers:leavePerson:refresh"/>
    	<@ZKUI.ToolItem id="/persLeavePerson.do?del&pins=(pin)" text="common_op_del" img="comm_del.png" action="commonDel" permission="pers:leavePerson:del"/>
		<@ZKUI.ToolItem id="/persLeavePerson.do?export" type="export" permission="pers:leavePerson:export"></@ZKUI.ToolItem>
    </@ZKUI.Toolbar>
    <@ZKUI.Grid vo="com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem" query="persLeavePerson.do?list" />
</@ZKUI.GridBox>