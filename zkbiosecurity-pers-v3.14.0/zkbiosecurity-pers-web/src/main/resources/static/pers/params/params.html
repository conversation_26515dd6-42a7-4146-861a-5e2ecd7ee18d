<#assign leftRTL = "left">
<#assign rightRTL = "right">
<#if enableRTL?? && enableRTL=="true">
<!-- 定义全局对齐方式变量(针对阿拉伯页面) -->
<#assign leftRTL = "right">
<#assign rightRTL = "left">
</#if>
<#include "/public/template/setTemplate.html">
<link href="css/sidenavFieldset.css" rel="stylesheet" type="text/css" />
<script src="js/sidenavFieldset.js"></script>
<#macro setContent>
<style type="text/css">
.setFormTemplate .sectionMB17 {
    margin-bottom: 17px;
}
.setFormTemplate .divMT10 {
    margin-top: 10px;
    position: relative;
}

.setFormTemplate .inlineDiv {
	display: inline-block;
}

.setFormTemplate .inlineDiv35 {
	margin-right: 35px;
}
</style>
<div id="setTemplate">
	<div class="setFormTemplate">
		<form action="persParams.do?save" id="${formId}" onkeydown="if(event.keyCode==13){return false;}" method="post">
			<fieldset class="sidenavFieldset">
				<legend><@i18n 'pers_person_pinSet' /></legend>
				<div>
					<section class="sectionMB17">
						<span class="formTitle"><@i18n 'common_length' />:</span>
						<div class="divMT10">
							<input type="text" id="pinLen" style="ime-mode:disabled;width:480px;box-sizing:border-box;" onkeyup="this.value=this.value.replace(/\D/g,'')"
								   onafterpaste="this.value=this.value.replace(/\D/g,'')" name="pers.pinLen" maxlength="2" value="9"/>
						</div>
					</section>
					<section class="sectionMB17">
						<span class="formTitle"><@i18n 'pers_person_supportLetter' />:</span>
						<div class="divMT10">
							<div class="inlineDiv inlineDiv35">
								<@ZKUI.Input name="pers.pinSupportLetter" hideLabel="true" id="yes" type="radio" onchange="checkPinLetters()" value="true"/>
								<label style="margin-right: 10px;"><@i18n 'common_yes' /></label>
							</div>
							<div class="inlineDiv">
								<@ZKUI.Input name="pers.pinSupportLetter" hideLabel="true" id="no" type="radio" onchange="checkPinLetters()" value="false" checked="checked"/>
								<label><@i18n 'common_no' /></label>
							</div>
						</div>
					</section>
					<div style="display: none">
						<div><@i18n 'pers_person_SupportDefault' />:</div>
						<div>
							<label><@ZKUI.Input name="pers.pinSupportDefault" hideLabel="true" id="Defaultyes" type="radio" value="true"/>
								<span><@i18n 'common_yes' /></span></label>
							<label><@ZKUI.Input name="pers.pinSupportDefault" hideLabel="true" id="Defaultno" type="radio" value="false" checked="checked"/>
								<span><@i18n 'common_no' /></span></label>
						</div>
					</div>
					<section>
						<span class="formTitle"><@i18n 'pers_person_pinAutoIncrement' />:</span>
						<div class="divMT10">
							<div class="inlineDiv inlineDiv35">
								<@ZKUI.Input name="pers.pinSupportIncrement" hideLabel="true" id="pinIncrementYes" type="radio" onchange="checkPinIncrement()" value="true" checked="checked"/>
								<label style="margin-right: 10px;"><@i18n 'common_yes' /></label>
							</div>
							<div class="inlineDiv">
								<label><@ZKUI.Input name="pers.pinSupportIncrement" hideLabel="true" id="pinIncrementNo" type="radio" onchange="checkPinIncrement()" value="false"/>
									<label><@i18n 'common_no' /></label>
							</div>
						</div>
					</section>
				</div>
			</fieldset>

			<fieldset class="sidenavFieldset" id="id_cardSet">
				<legend><@i18n 'pers_person_cardSet' /></legend>
				<div>
					<section class="sectionMB17" hidden="hidden">
						<span class="formTitle"><@i18n 'common_length' />:</span>
						<div class="divMT10">
							<input type="text" id="cardLen" style="ime-mode:disabled;width:480px;box-sizing:border-box;" onkeyup="this.value=this.value.replace(/\D/g,'')"
								   onafterpaste="this.value=this.value.replace(/\D/g,'')" name="pers.cardLen" maxlength="3" value="32"/>
							<@i18n 'common_bit' />
						</div>
					</section>
					<section class="sectionMB17">
						<span class="formTitle"><@i18n 'pers_card_hex' />:</span>
						<div class="divMT10">
							<div class="inlineDiv inlineDiv35">
								<@ZKUI.Input name="pers.cardHex" hideLabel="true" id="decimal" type="radio" onchange="checkIsExistCard()"  value="0" checked="checked"/>
								<label style="margin-right: 10px;"><@i18n 'pers_card_decimal' /></label>
							</div>
							<div class="inlineDiv inlineDiv35">
								<@ZKUI.Input name="pers.cardHex" hideLabel="true" id="hexadecimal" type="radio" onchange="checkIsExistCard()" value="1"/>
								<label><@i18n 'pers_card_Hexadecimal' /></label>
							</div>
						</div>
					</section>
					<section class="sectionMB17">
						<div <#if "${Application['system.productCode']}" == "ZKBioAccess">style="display:none;"</#if>>
					<span class="formTitle"><@i18n 'pers_person_cardsSupport' />:</span>
					<div class="divMT10">
						<div class="inlineDiv inlineDiv35">
							<@ZKUI.Input name="pers.cardsSupport" hideLabel="true" id="cardsYes" type="radio" value="true" onchange="supportMultiCardAlert()"/>
							<label style="margin-right: 10px;"><@i18n 'common_yes' /></label>
						</div>
						<div class="inlineDiv inlineDiv35">
							<@ZKUI.Input name="pers.cardsSupport" hideLabel="true" id="cardsNo" type="radio" value="false" onchange="checkIsExistCards()" checked="checked"/>
							<label><@i18n 'common_no' /></label>
						</div>
					</div>
				</div>
				</section>
			<section <#if Application['system.language'] == "zh_CN">class="sectionMB17"</#if> style="display: none;">
			<span class="formTitle"><@i18n 'pers_person_cardsReadMode' />:</span>
			<div class="divMT10">
				<div class="inlineDiv inlineDiv35">
					<@ZKUI.Input name="pers.cardsReadMode" hideLabel="true" id="cardsReadModeReadHead" type="radio" value="1" checked="checked"/>
					<label style="margin-right: 10px;"><@i18n 'pers_person_cardsReadModeReadHead' /></label>
				</div>
				<div class="inlineDiv">
					<#if Application['system.language'] == "zh_CN">
					<@ZKUI.Input name="pers.cardsReadMode" hideLabel="true" id="cardsReadModeID180" type="radio" value="2"/>
					<label><@i18n 'pers_person_cardsReadModeID180' /></label>
				</#if>
			</div>
	</div>
	</section>
	<#if Application['system.language'] == "zh_CN">
	<section>
		<span class="formTitle"><@i18n 'pers_person_IDReadMode' />:</span>
		<div class="divMT10">
			<div class="inlineDiv inlineDiv35">
				<@ZKUI.Input name="pers.IDReadMode" hideLabel="true" id="IDCardReader" type="radio" value="1"/>
				<label style="margin-right: 10px;"><@i18n 'pers_person_IDReadModeIDCardReader' /></label>
			</div>
			<div class="inlineDiv">
				<@ZKUI.Input name="pers.IDReadMode" hideLabel="true" id="TcpReadHead" type="radio" value="2" checked="checked"/>
				<label><@i18n 'pers_person_IDReadModeTcpReadHead' /></label>
			</div>
		</div>
	</section>
	<div style="display: none;">
		<div><@i18n 'pers_person_physicalNoToCardNo' />:</div><!-- pers_person_physicalNoToCardNo -->
		<div>
			<label><@ZKUI.Input name="pers.physicalNoToCardNo" hideLabel="true" id="physicalNoYes" type="radio" value="true"/>
				<span><@i18n 'common_yes' /></span></label>
			<label><@ZKUI.Input name="pers.physicalNoToCardNo" hideLabel="true" id="physicalNo" type="radio" value="false" checked="checked"/>
				<span><@i18n 'common_no' /></span></label>
		</div>
	</div>
</#if>
</div>
</fieldset>
<fieldset class="sidenavFieldset" hidden="hidden">
	<legend><@i18n 'pers_leave' /></legend>
	<div>
		<span><@i18n 'pers_dimission_pinRetain' />:</span>
		<div>
			<@ZKUI.Input name="pers.pinRetain" hideLabel="true" id="pinYes" type="radio" value="true" checked="checked"/>
			<label style="margin-right: 10px;"><@i18n 'common_yes' /></label>
			<@ZKUI.Input hideLabel="true" name="pers.pinRetain" id="pinNo" type="radio" value="false"/>
			<label><@i18n 'common_no' /></label>
		</div>
	</div>
</fieldset>
<@ZKUI.Permission name="pers:tempPerson">
<fieldset class="sidenavFieldset">
	<legend><@i18n 'pers_person_tempPersonSet' /></legend>
	<div>
		<span class="formTitle"><@i18n 'pers_param_isAudit' />: </span>
		<div class="divMT10">
			<div class="inlineDiv inlineDiv35">
				<@ZKUI.Input hideLabel="true" name="pers.tempPerson.audit" id="tempPersonYes" type="radio" value="1" checked="checked"/>
				<label><@i18n 'common_yes' /></label>
			</div>
			<div class="inlineDiv">
				<@ZKUI.Input hideLabel="true" name="pers.tempPerson.audit" id="tempPersonNo" type="radio" value="0"/>
				<label><@i18n 'common_no' /></label>
			</div>
		</div>
	</div>
</fieldset>
</@ZKUI.Permission>
<!--mark:访客二维码-->
<@ZKUI.Permission name="pers:tempPerson">
<fieldset class="sidenavFieldset">
	<legend><@i18n 'pers_param_qrCodeUrlCreate' /></legend>
	<div>
		<div class="sectionMB17">
			<span class="formTitle"><@i18n 'pers_param_selfRegistration' />: </span>
			<div class="divMT10">
				<div class="inlineDiv inlineDiv35">
					<@ZKUI.Input hideLabel="true" name="pers.selfRegistration" id="selfRegistrationYes" type="radio" value="1" checked="checked" onchange="isOpenSelfRegistration()"/>
					<label><@i18n 'common_yes' /></label>
				</div>
				<div class="inlineDiv">
					<@ZKUI.Input hideLabel="true" name="pers.selfRegistration" id="selfRegistrationNo" type="radio" value="0" onchange="isOpenSelfRegistration()"/>
					<label><@i18n 'common_no' /></label>
				</div>
			</div>
		</div>
		<!-- 是否启用健康申报 -->
		<div id="selfRegistrationDiv${uuid}">
			<div id="healthTr${uuid}" hidden="hidden" class="sectionMB17">
				<span class="formTitle"><@i18n 'pers_health_enable' />: </span>
				<div class="divMT10">
					<div class="inlineDiv inlineDiv35">
						<@ZKUI.Input hideLabel="true" name="pers.enableHealthInfo" id="enableHealthInfoYes" type="radio" value="1" checked="checked"/>
						<label><@i18n 'common_yes' /></label>
					</div>
					<div class="inlineDiv">
						<@ZKUI.Input hideLabel="true" name="pers.enableHealthInfo" id="enableHealthInfoNo" type="radio" value="0"/>
						<label><@i18n 'common_no' /></label>
					</div>
				</div>
			</div>
			<span class="formTitle"><@i18n 'pers_param_qrCodeUrl'/>:</span>
			<div class="divMT10">
				<input id="inputCode" name="pers.tempPerson.urlCreate" onblur="urlMaker()" onclick="urlMaker()" type="text" style="width:480px;box-sizing:border-box;" placeholder="http://<@i18n 'pers_param_qrCodeUrlHref' />" />
			</div>
			<div id="tdCodeImg" style="display: none" class="divMT10">
				<span><a href="javascript:void(0);" onclick="downCodeImg()"><@i18n 'pers_param_downloadQRCodePic' /></a></span>
				<div style="display: none" id="codeImg"></div>
			</div>
			<div id="code" style="width:258px" class="divMT10"></div>
		</div>
	</div>
</fieldset>
</@ZKUI.Permission>

<fieldset class="sidenavFieldset">
	<legend><@i18n 'pers_param_templateServer' /></legend>
	<div>
		<div class="sectionMB17">
			<span class="formTitle"><@i18n 'pers_param_enableFacialTemplate' />: </span>
			<div class="divMT10">
				<div class="inlineDiv inlineDiv35">
					<@ZKUI.Input hideLabel="true" name="pers.facialTemplate.enable" id="enableFacialTemplateYes" type="radio" value="1" onchange="showTemplateServer(this.value, '1')"/>
					<label><@i18n 'common_yes' /></label>
				</div>
				<div class="inlineDiv">
					<@ZKUI.Input hideLabel="true" name="pers.facialTemplate.enable" id="enableFacialTemplateNo" type="radio" value="0" checked="checked" onchange="showTemplateServer(this.value)"/>
					<label><@i18n 'common_no' /></label>
				</div>
			</div>
		</div>
		<!-- 人脸模板提取 -->
		<div id="templateServerDiv${uuid}" hidden="hidden">
			<div class="sectionMB17">
				<span class="formTitle"><@i18n 'pers_param_templateServerAddr' />: </span>
				<div class="divMT10">
					<input name="pers.facialTemplate.serverAddr" type="text" style="width:480px;box-sizing:border-box;margin-bottom: 10px;" placeholder="https://<@i18n 'pers_param_qrCodeUrlHref' />" onchange="changeTestFlag()"/>
				</div>
				<span class="formTitle"><@i18n 'auth_user_username' />: </span>
				<div class="divMT10">
					<input name="pers.facialTemplate.username" type="text" style="width:480px;box-sizing:border-box;margin-bottom: 10px;" onchange="changeTestFlag()"/>
				</div>
				<span class="formTitle"><@i18n 'base_login_password' />: </span>
				<div class="divMT10">
					<input name="pers.facialTemplate.pwd" type="password" style="width:480px;box-sizing:border-box;" onchange="changeTestFlag()"/>
					<span class="export-input-eye"></span>
				</div>
			</div>
			<div class="divMT10">
				<button type="button" class="button-form" onclick="testTemplateServerConnect()"><@i18n 'base_mail_connectTest'/></button>
				<span id="serverStatus${uuid}" class="formTitle" style="color: #FE6257;" testFlag="0"><@i18n 'common_offline' /></span>
			</div>
		</div>
		<div class="divMT10">
			<label>
				<span class="warningImage"></span>
				<span style="line-height: 22px;" class="warningColor"><@i18n 'pers_param_templateServerWarnInfo1' /></span>
			</label>
		</div>
		<div class="divMT10">
			<label>
				<span class="warningImage"></span>
				<span style="line-height: 22px;" class="warningColor"><@i18n 'pers_param_templateServerWarnInfo' /></span>
			</label>
		</div>
	</div>
</fieldset>

<fieldset class="sidenavFieldset" id="persQrCode" hidden="hidden">
	<legend><@i18n 'pers_param_qrCode' /></legend>
	<div>
		<div class="sectionMB17">
			<span class="formTitle"><@i18n 'pers_param_employeeRegistrar' />:</span>
			<div class="divMT10">
				<div class="inlineDiv inlineDiv35">
					<@ZKUI.Input hideLabel="true" id="employeeRegistrarYes" name="pers.employeeRegistrar" type="radio" onchange="persCheckEmployeeRegistrar()" value="true" checked="checked"/>
					<label><@i18n 'common_yes' /></label>
				</div>
				<div class="inlineDiv">
					<@ZKUI.Input hideLabel="true" id="employeeRegistrarNo" name="pers.employeeRegistrar" type="radio" onchange="persCheckEmployeeRegistrar()"  value="false" />
					<label><@i18n 'common_no' /></label>
				</div>
			</div>
		</div>
		<div id="persQrCodeUrl${uuid}" style="display: none;">
			<span class="formTitle"><@i18n 'pers_param_qrCodeUrl'/>: </span><div class="divMT10"><input id="qrCodeUrl" readonly="readonly" type="text" style="width: 450px;"/></div>
			<div class="divMT10"><a href="javascript:downloadQRCodePic();"><@i18n 'pers_param_downloadQRCodePic'/></a></div>
		</div>
		<div id="persParamQrCode" class="divMT10"></div>
	</div>
</fieldset>

<fieldset class="sidenavFieldset">
	<legend><@i18n 'pers_param_faceServer' /></legend>
	<div>
		<div class="sectionMB17">
			<span class="formTitle"><@i18n 'pers_param_enableFaceVerify' />: </span>
			<div class="divMT10">
				<div class="inlineDiv inlineDiv35">
					<@ZKUI.Input hideLabel="true" name="pers.faceVerify.mode" id="enableFaceVerifyYes" type="radio" value="1" onchange="showFaceServer(this.value, '1')"/>
					<label><@i18n 'common_yes' /></label>
				</div>
				<div class="inlineDiv">
					<@ZKUI.Input hideLabel="true" name="pers.faceVerify.mode" id="enableFaceVerifyYesNo" type="radio" value="0" checked="checked" onchange="showFaceServer(this.value)"/>
					<label><@i18n 'common_no' /></label>
				</div>
			</div>
		</div>
		<div id="faceVerifyServerDiv${uuid}" hidden="hidden">
			<div class="sectionMB17">
				<span class="formTitle"><@i18n 'pers_param_faceServerAddr' />: </span>
				<div class="divMT10">
					<input name="pers.faceVerify.serverAddr" type="text" style="width:480px;box-sizing:border-box;margin-bottom: 10px;" placeholder="<@i18n 'pers_param_qrCodeUrlHref' />" onchange="changeFaceServerTestFlag()"/>
				</div>
			</div>
			<div class="sectionMB17">
				<span class="formTitle"><@i18n 'pers_param_faceServerSecret' />: </span>
				<div class="divMT10">
					<input name="pers.faceVerify.serverSecret" type="text" style="width:480px;box-sizing:border-box;margin-bottom: 10px;" onchange="changeFaceServerTestFlag()"/>
				</div>
			</div>
			<div class="divMT10">
				<button type="button" class="button-form" onclick="testFaceServerConnect()"><@i18n 'base_mail_connectTest'/></button>
				<span id="faceServerStatus${uuid}" class="formTitle" style="color: #FE6257;" testFlag="0"><@i18n 'common_offline' /></span>
			</div>
		</div>
	</div>
</fieldset>
<#include "/system/registerClient/registerClient.html">
</form>
</div>
<div class="sidenavContext"></div>
</div>
<script type="text/javascript">
//判断是否是单考勤系统
$(function () {
	loadPersParams();
    var singleAttSystem = false;
    $.ajax({
        url:"persParams.do?checkSingleAttSystem",
        async:false,
        success: function (data)
        {
            if(data){
                singleAttSystem = true;
            }
        }
    });
    if(singleAttSystem){
        $('#id_cardSet').attr("style","display:none;");
    }else{
        $('#id_cardSet').removeAttr("display");
	}
	if(persParams['existHepModule'] == "true")
	{
		$("#healthTr${uuid}").show();
	}

	/**
	 * 初始二维码
	 * */
	var appId = persParams["appId"];
    if (appId != null && appId != undefined && appId != ""){
        $("#${formId} #qrCodeUrl").val(persParams["cloudServerUrl"]);
        $("#${formId} #persQrCode").removeAttr("hidden");
	}
	includeScript("/js/jquery.qrcode.min.js", function(){
		urlMaker();
		if (appId != null && appId != undefined && appId != ""){
			$("#${formId} #persParamQrCode canvas").remove();
			persCheckEmployeeRegistrar();
		}
	});
})
var editPersParamsFromId = "#${formId}";

(function() {
    var $from = $(editPersParamsFromId);
    $from.find("input").each(function(){
        var name = $(this).attr("name");
        var paramValue = persParams[name];
        if(paramValue != undefined){
            var type = $(this).attr("type");
            if(type == "radio" || type == "checkbox"){
                if(paramValue == $(this).val()){
                    $(this).prop("checked",true);
                }
            }
            else{
                if(type == "hidden" && $(this).attr("id")){
					var id = $(this).attr("id").split("_")[0];
					$("#"+id).prop("checked",paramValue == "true");
				}
				$(this).val(paramValue);
            }
            if(name == "pers.selfRegistration")
            {
            	isOpenSelfRegistration();
            }
            if(name == "pers.facialTemplate.enable" && $(this).attr("id") == "enableFacialTemplateNo")
            {
            	showTemplateServer(paramValue);
            }
            if(name=='pers.facialTemplate.pwd')
            {
            	testTemplateServerConnect("1");
            }
            if(name == "pers.faceVerify.mode" && $(this).attr("id") == "enableFaceVerifyYesNo")
            {
            	showFaceServer(paramValue);
            }
        }
    });

	var registerMsg = jQuery.parseJSON(persParams["registerMsg"]);
	if(!($.isEmptyObject(registerMsg.cardPrintMsg) && $.isEmptyObject(registerMsg.ocrMsg) && $.isEmptyObject(registerMsg.idReaderMsg)))
	{
		$("#registerClientField").removeAttr("hidden");
	}

    $from.validate( {
        debug : true,
        rules :
        {
            "pers.pinLen":
            {
                required: true,
                range:[5,23],
                remote:{
                    url:"persParams.do?checkMaxPinLenth"
                }
            },
            "pers.pinSupportLetter":
            {
                required: true
            },
            "pers.cardLen":
            {
                required: true,
                range:[1,128]
            },
            "pers.tempPerson.audit":
			{
				required: true
			}
        },
        messages:
        {
            "pers.pinLen":
            {
                remote:"<@i18n 'pers_param_maxPinLength' />"
            }
        },
        submitHandler : function()
        {
        	if(checkTemplateServerSet())
        	{
        		<@submitHandler callBackFun="loadPersParams()"/>
        	}
        }
    });
})();



/**
 *url输入框回车事件
 * */
$('#inputCode').bind('keydown',function(event){
    if(event.keyCode == "13") {
        urlMaker();
    }
});

/**
 * 访客url生成
 * */
function urlMaker() {
    if ($("#inputCode").val() != "") {
        $("#code canvas").remove();
        if($('#inputCode').val() != null && $('#inputCode').val != ""){
            //匹配结尾
            var res = $('#inputCode').val().search("\/tokenAdreg$");
            if( res==-1){
                $('#inputCode').val($('#inputCode').val()+"/tokenAdreg")
            }
		}
        //$('#code').qrcode($('#inputCode').val()+"/tokenAdreg");
        var url = $('#inputCode').val();
        $("#code").qrcode({
            render: "canvas", //也可以替换为table
            width: 200,
            height: 200,
            text: url
        });
        $("#code").show();
        $("#tdCodeImg").show();
	}
}

/**
 * 下载自助登记的二维码
 * */
function downCodeImg(){
    var data = $("#code canvas")[0].toDataURL().replace("image/png", "image/octet-stream");
	var filename = "QRCode.png";
    var isIE = !!window.ActiveXObject || "ActiveXObject" in window;
    // IE 和 Edge 需要转换成 blob 才能保存文件。
    if (isIE || navigator.userAgent.indexOf("Edge") > -1) {
        var blob = convertBase64UrlToBlob({
            dataURL: data,
            type: 'image/png',
            ext: 'png'
        });
        if (isIE) {
            window.navigator.msSaveBlob(blob, filename);
        } else {
            var save_link = document.createElement('a');
            save_link.download = filename;
            save_link.href = URL.createObjectURL(blob);
            save_link.click();
        }
    } else {
        var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        save_link.href = data;
        save_link.download = filename;
        var event = document.createEvent('MouseEvents');
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        save_link.dispatchEvent(event);
    }
}

function checkPinLetters(){
    var pinSupportLetter = $(editPersParamsFromId + " :input[name='pers.pinSupportLetter']:checked").val();
    var pinSupportIncrement = $(editPersParamsFromId + " :input[name='pers.pinSupportIncrement']:checked").val();
    if(pinSupportIncrement == "true")
    {
        $(editPersParamsFromId + " #no").prop("checked", true);
        openMessage(msgType.warning, "<@i18n 'pers_param_changePinLettersTip' />");
        return;
    }
    if(pinSupportLetter == "true")
    {
        $(editPersParamsFromId + " #pinIncrementNo").prop("checked", true);
    }
    $.ajax({
        url:"persParams.do?checkPinLetters",
        data :{'pinSupportLetter' : pinSupportLetter},
        success: function (data)
        {
            if(data[sysCfg.ret] != sysCfg.success)
            {
                if (pinSupportLetter == "true")
                {
                    $(editPersParamsFromId + " #no").prop("checked", true);
                }
                else
                {
                    $(editPersParamsFromId + " #yes").prop("checked", true);
                }
                openMessage(msgType.warning, data[sysCfg.msg]);
            }
        }
    });
}
function checkPinIncrement(){
    var pinSupportLetter = $(editPersParamsFromId + " :input[name='pers.pinSupportLetter']:checked").val();
    var pinSupportIncrement = $(editPersParamsFromId + " :input[name='pers.pinSupportIncrement']:checked").val();
    if(pinSupportLetter == "true")
    {
        $(editPersParamsFromId+" #pinIncrementNo").prop("checked", true);
        openMessage(msgType.warning, "<@i18n 'pers_param_changePinIncrementTip' />");
        return;
    }
    if(pinSupportIncrement == "true")
    {
        $(editPersParamsFromId+" #no").prop("checked", true);
    }
    $.ajax({
        url:"persParams.do?checkPinIncrement",
        data :{'pinSupportIncrement' : pinSupportIncrement},
        success: function (data)
        {
            if(!data)
            {
                $(editPersParamsFromId+" #pinIncrementNo").prop("checked", true);
               openMessage(msgType.warning, "<@i18n 'pers_param_donotChangePin' />");
			}
        }
    });
}

//修改卡进制显示格式时，如果系统存在卡号则不允许修改参数设置
function checkIsExistCard(){
    $.ajax({
        url : "persParams.do?checkIsExistCard",
        success : function(data)
        {
            if (data)
            {
                if ($(editPersParamsFromId+" #decimal").is(":checked"))
                {
                    $(editPersParamsFromId+" #hexadecimal").prop("checked", true);
                }
                else
                {
                    $(editPersParamsFromId+" #decimal").prop("checked", true);
                }
                openMessage(msgType.warning, "<@i18n 'pers_param_hexChangeWarn' />");
            }
        }
    });
}

function checkIsExistCards(){
    $.ajax({
        url : "persParams.do?checkIsExistCards",
        success : function(data)
        {
            if (data)
            {
                if ($(editPersParamsFromId+" #cardsYes").is(":checked"))
                {
                    $(editPersParamsFromId+" #cardsNo").prop("checked", true);
                }
                else
                {
                    $(editPersParamsFromId+" #cardsYes").prop("checked", true);
                }
                openMessage(msgType.warning, "<@i18n 'pers_param_cardsChangeWarn' />");
            }
        }
     });
}

function supportMultiCardAlert()
{
	messageBox({
		messageType: "alert",
		title: "<@i18n 'common_prompt_title'/>",
		text: "<@i18n 'common_dev_supportMultiCardForDevTip'/>"
	});
}

function persCheckEmployeeRegistrar() {
    var employeeRegistrarUrl = persParams["cloudServerUrl"];
    var employeeRegistrar = $("#${formId} :input[name='pers.employeeRegistrar']:checked").val();
    if (employeeRegistrar == "true"){
        //生成180*150(宽度180，高度150)的二维码
        $("#${formId} #persParamQrCode").qrcode({
            render: "canvas", //也可以替换为table
            width: 200,
            height: 200,
            text: employeeRegistrarUrl
        });
        $("#persQrCodeUrl${uuid}").show();
	} else {
        $("#${formId} #persParamQrCode canvas").remove();
        $("#persQrCodeUrl${uuid}").hide();
	}
}

/**
 * 下载二维码图片
 */
function downloadQRCodePic(){
    //获取二维码值，并修改响应头部。
    var data = $("#${formId} #persParamQrCode canvas")[0].toDataURL().replace("image/png", "image/octet-stream");
    //保存的图片名称和格式，canvas默认使用的png格式。这个格式效果最好。
    var filename = "QRCode.png";
    var isIE = !!window.ActiveXObject || "ActiveXObject" in window;
    // IE 和 Edge 需要转换成 blob 才能保存文件。
    if (isIE || navigator.userAgent.indexOf("Edge") > -1) {
        var blob = convertBase64UrlToBlob({
            dataURL: data,
            type: 'image/png',
            ext: 'png'
        });
        if (isIE) {
            window.navigator.msSaveBlob(blob, filename);
        } else {
            var save_link = document.createElement('a');
            save_link.download = filename;
            save_link.href = URL.createObjectURL(blob);
            save_link.click();
        }
    } else {
        var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        save_link.href = data;
        save_link.download = filename;
        var event = document.createEvent('MouseEvents');
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        save_link.dispatchEvent(event);
    }
}

/**
 *  将 base64 转换位 blob 对象
 *  blob 存储 2进制对象的容器
 *  @param base64
 *  @return Blob
 */
function convertBase64UrlToBlob(base64) {
    var parts = base64.dataURL.split(';base64,');
    var contentType = parts[0].split(':')[1];
    var raw = window.atob(parts[1]);
    var rawLength = raw.length;
    var uInt8Array = new Uint8Array(rawLength);
    for (var i = 0; i < rawLength; i++)
    {
        uInt8Array[i] = raw.charCodeAt(i);
    }
    return new Blob([uInt8Array], { type: contentType });
}

function isOpenSelfRegistration()
{
	var val = $("input[name='pers.selfRegistration']:checked").val();
	if(val == "1")
	{
		$("#selfRegistrationDiv${uuid}").show();
	}
	else
	{
		$("#selfRegistrationDiv${uuid}").hide();
	}
}

$(".export-input-eye").on("mousedown", function(e) {
	$(this).addClass("export-input-eye-open");
	$("input[name='pers.facialTemplate.pwd']").attr("type", "text");
})

$(".export-input-eye").on("mouseup", function(e) {
	$(this).removeClass("export-input-eye-open");
	$("input[name='pers.facialTemplate.pwd']").attr("type", "password");
})

function showTemplateServer(objVal, testFlag)
{
	if(objVal == "1")
	{
		$("#templateServerDiv${uuid}").show();
		if(testFlag)
		{
			$("#serverStatus${uuid}").attr("testFlag", "1");
		}
	}
	else
	{
		$("#templateServerDiv${uuid}").hide();
	}
}

function showFaceServer(objVal, testFlag)
{
	if(objVal == "1")
	{
		$("#faceVerifyServerDiv${uuid}").show();
		if(testFlag)
		{
			$("#faceServerStatus${uuid}").attr("testFlag", "1");
		}
	}
	else
	{
		$("#faceVerifyServerDiv${uuid}").hide();
	}
}

function testTemplateServerConnect(testConnect)
{
	var address = $("input[name='pers.facialTemplate.serverAddr']").val();
    var userName = $("input[name='pers.facialTemplate.username']").val();
    var pwd = $("input[name='pers.facialTemplate.pwd']").val();
    if(!address || !userName || !pwd)
    {
    	if(testConnect)
    	{
    		$("#serverStatus${uuid}").css("color", "#FE6257");
    		$("#serverStatus${uuid}").html("<@i18n 'common_offline' />");
    	}
    	else
    	{
    		openMessage("warning", "<@i18n 'pers_person_serverConnectWarn' />");
    	}
    	return;
    }
    if(testConnect)
	{
		$("input[name='pers.facialTemplate.serverAddr']").attr("oldValue", address);
		$("input[name='pers.facialTemplate.username']").attr("oldValue", userName);
		$("input[name='pers.facialTemplate.pwd']").attr("oldValue", pwd);
		testServerConnect();
	}
	else
	{
		$("#serverStatus${uuid}").attr("testFlag", "0");
		loginTemplateServer(address, userName, pwd);
	}

}

function testFaceServerConnect(testConnect)
{
	var address = $("input[name='pers.faceVerify.serverAddr']").val();
    if(!address)
    {
    	if(testConnect)
    	{
    		$("#faceServerStatus${uuid}").css("color", "#FE6257");
    		$("#faceServerStatus${uuid}").html("<@i18n 'common_offline' />");
    	}
    	else
    	{
    		openMessage("warning", "<@i18n 'pers_person_serverConnectWarn' />");
    	}
    	return;
    }
    if(testConnect)
	{
		$("input[name='pers.faceVerify.serverAddr']").attr("oldValue", address);
		testFaceServer();
	}
	else
	{
		$("#faceServerStatus${uuid}").attr("testFlag", "0");
		testFaceServer();
	}

}

function loginTemplateServer(address, userName, pwd)
{
    onLoading(function(){
        $.ajax({
            type : "POST",
            url:"persParams.do?loginTemplateServer",
            data :{
                address : address,
                userName : userName,
                pwd : pwd,
            },
            async : true,
            success: function (result){
                closeMessage();
                if (result.ret.trim() == "ok") {
                    openMessage(msgType.success, result.msg);
                    var systemSkin = (sysCfg.skin=='default' || !sysCfg.skin) ? "" : sysCfg.skin;
					// 深色模式修改颜色
					if (systemSkin == "techblue") {
						$("#serverStatus${uuid}").css("color", "#42AFFE");
						$("#serverStatus${uuid}").html("<@i18n 'common_online' />");
					}
					else
					{
						$("#serverStatus${uuid}").css("color", "#7ac143");
						$("#serverStatus${uuid}").html("<@i18n 'common_online' />");
					}
                } else {
                    openMessage(msgType.error, result.msg);
                    $("#serverStatus${uuid}").css("color", "#FE6257");
                    $("#serverStatus${uuid}").html("<@i18n 'common_offline' />");
                }
            },
            error : function(XMLHttpRequest, textStatus, errorThrown) {
                closeMessage();
            }
        });
    });
}

function testServerConnect()
{
	$.ajax({
		type : "POST",
		url:"persParams.do?testTemplateServerConnect",
		async : true,
		success: function (result){
			if (result.ret.trim() == "ok") {
				var systemSkin = (sysCfg.skin=='default' || !sysCfg.skin) ? "" : sysCfg.skin;
				// 深色模式修改颜色
				if (systemSkin == "techblue") {
					$("#serverStatus${uuid}").css("color", "#42AFFE");
					$("#serverStatus${uuid}").html("<@i18n 'common_online' />");
				}
				else
				{
					$("#serverStatus${uuid}").css("color", "#7ac143");
					$("#serverStatus${uuid}").html("<@i18n 'common_online' />");
				}
			} else {
				$("#serverStatus${uuid}").css("color", "#FE6257");
				$("#serverStatus${uuid}").html("<@i18n 'common_offline' />");
			}
		},
		error : function(XMLHttpRequest, textStatus, errorThrown) {
			closeMessage();
		}
	});
}

function testFaceServer()
{
	$.ajax({
		type : "POST",
		url:"persParams.do?testFaceServerConnect",
		async : true,
		success: function (result){
			if (result.ret.trim() == "ok") {
				var systemSkin = (sysCfg.skin=='default' || !sysCfg.skin) ? "" : sysCfg.skin;
				// 深色模式修改颜色
				if (systemSkin == "techblue") {
					$("#faceServerStatus${uuid}").css("color", "#42AFFE");
					$("#faceServerStatus${uuid}").html("<@i18n 'common_online' />");
				}
				else
				{
					$("#faceServerStatus${uuid}").css("color", "#7ac143");
					$("#faceServerStatus${uuid}").html("<@i18n 'common_online' />");
				}
			} else {
				$("#faceServerStatus${uuid}").css("color", "#FE6257");
				$("#faceServerStatus${uuid}").html("<@i18n 'common_offline' />");
			}
		},
		error : function(XMLHttpRequest, textStatus, errorThrown) {
			closeMessage();
		}
	});
}

function checkTemplateServerSet()
{
	if($("input[name='pers.facialTemplate.enable']:checked").val() == "1")
	{
		var address = $("input[name='pers.facialTemplate.serverAddr']").val();
		var username = $("input[name='pers.facialTemplate.username']").val();
		var pwd = $("input[name='pers.facialTemplate.pwd']").val();
		if(!address || !username || !pwd)
		{
			openMessage("warning", "<@i18n 'pers_person_serverConnectWarn' />");
			return false;
		}
		if($("#serverStatus${uuid}").attr("testFlag") == "1")
		{
			openMessage("warning", "<@i18n 'pers_person_serverConnectInfo' />");
			return false;
		}
		var oldAddress = $("input[name='pers.facialTemplate.serverAddr']").attr("oldValue");
		var oldUserName = $("input[name='pers.facialTemplate.username']").attr("oldValue");
		var oldPwd = $("input[name='pers.facialTemplate.pwd']").attr("oldValue");
		//修改服务器连接信息并且测试连接不通过
		if((oldAddress != address || oldUserName != username || oldPwd != pwd) && $("#serverStatus${uuid}").html() == "<@i18n 'common_offline' />")
		{
			openMessage("warning", "<@i18n 'pers_person_serverOfflineWarn' />");
			return false;
		}
	}
	return true;
}

function changeTestFlag()
{
	$("#serverStatus${uuid}").attr("testFlag", "1");
}
function changeFaceServerTestFlag()
{
	$("#faceServerStatus${uuid}").attr("testFlag", "1");
}

</script>
</#macro>
