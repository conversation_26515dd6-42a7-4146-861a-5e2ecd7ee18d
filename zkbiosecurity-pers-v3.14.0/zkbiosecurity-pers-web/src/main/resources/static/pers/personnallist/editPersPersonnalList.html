<#assign editPage="true">
<#include '/public/template/editTemplate.html'>
<#macro editContent>
	<script type='text/javascript'>
        $(function() {
            var editErsonnalListFormId = $("#${formId}");
        $(editErsonnalListFormId).validate({
                debug : true,
                rules :{
                    "name" :{
                        required: true,
                        maxlength: 20,
                        unInputChar: true,
                        overRemote: ["persPersonnalList.do?verifyName", "${(item.name)!}"]
                    },
                    "type" :{
                        required : true,
                    }
                },
            submitHandler : function(){
					<@submitHandler/>
                }
            });
        });
        
        //过滤掉禁止名单库
        function reloadType() {
            ZKUI.Combo.get("type${uuid}").combo.deleteOption("2")
        }
    </script>
	<form action="persPersonnalList.do?save" method="post" id="${formId}" enctype="multipart/form-data">
        <input type="hidden" name="id" value='${(item.id)!}'/>
        <table class="tableStyle">
            <colgroup>
                <col width="35%" />
                <col width="*" />
            </colgroup>
         
                <tr>
                    <th><label><@i18n 'pers_personnal_list_name'/></label><span class="required">*</span></th>
                    <td><input name="name" id="name" type="text" value="${(item.name)!}"/></td>
                </tr>
                <tr>
                    <th><label><@i18n 'pers_personnal_list_type'/></label><span class="required">*</span></th>
                    <td>
                        <@ZKUI.Combo name="type" empty="true" id="type${uuid}" hideLabel="true" width="148" key="PersPersonnallistType" value="${(item.type)!}" onAfterLoad="reloadType"/>
                    </td>
                </tr>
                <tr>
                    <th><label><@i18n 'base_system_description'/></label></th>
                    <td><input name="description" maxlength="200" type="text" value="${(item.description)!}"/></td>
                </tr>
        </table>
    </form>
</#macro>
