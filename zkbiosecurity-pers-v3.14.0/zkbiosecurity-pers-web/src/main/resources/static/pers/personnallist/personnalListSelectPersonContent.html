
<div id="persDivChannelSearchType" style="height:25px;line-height:25px;display:block">
    <label style="margin-left:5px"><@ZKUI.Input hideLabel="true" name="searchType" value="1" checked="" onClick="persDeptSwith(1);" type="radio"/>&nbsp;<@i18n 'pers_widget_searchType1'/></label>
    <label style="margin-left:30px"><@ZKUI.Input hideLabel="true" name="searchType" value="2" type="radio" onClick="persDeptSwith(2);"/>&nbsp;<@i18n 'pers_widget_searchType2'/></label>
</div>
<div id="persMaskLayer${uuid}" style="position: absolute;width: 100%;display: none; top:30px;bottom:40px;z-index: 10000;opacity: 0.5;background-color: #dcdfe2;">
</div>
<@ZKUI.SelectContent onSure="addSelectPerson" setMode="true,true,false,true" uuid="${uuid}" gridName="groupSelectChannel${uuid}" showColumns="checkbox,name,pin,lastName,deptName" style="position:absolute;top:30px;bottom:0px;left:0px;right:0px;height:auto;" linkId="${linkId!}" linkName="${linkName!}" copy="true" textField="personPin" vo="com.zkteco.zkbiosecurity.pers.vo.PersPersonPersonnalListSelectItem" query="/persPersonnalList.do?selectPersonlist&personnelListId=${personnelListId!}&modelType=${modelType!}">
<div id="persDeptTree${uuid}" class="zk-content-bg-color" style="position: absolute;z-index: 10001;display: none;background-color: #f3f5f0;opacity:1;width:100%;height: 42px;padding-top: 15px;">
    <@ZKUI.ComboTree id="deptTree${uuid}" type="checkbox" url="authDepartment.do?tree" tree_onCheck="onTreeChecked" title="base_department_deptName" width="148" readonly="true" name="deptIds"/>
    <div style="display:inline-block;">
        <span style="color:#FF9900; position : relative; padding-left: 50px;"><@i18n 'pers_widget_deptHint'/></span>
    </div>
</div>
<@ZKUI.Searchbar>
<@ZKUI.SearchTop>
<tr>
    <td valign="middle">
        <@ZKUI.Input name="pin"  maxlength="30" title="pers_person_pin" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="likeName"  maxlength="50" title="pers_person_wholeName" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.ComboTree id="persPersonDeptTree${uuid}" type="checkbox" url="authDepartment.do?tree" title="base_department_deptName" width="148" readonly="true" name="inDeptId"/>
    </td>
</tr>
</@ZKUI.SearchTop>
</@ZKUI.Searchbar>
</@ZKUI.SelectContent>
<script type="text/javascript">
    //添加门，点确定之后调用的方法
    function addSelectPerson(value, text, event) {
        var personnelListId = "${personnelListId!}";


        var checkVal = $("#persDivChannelSearchType input[name='searchType']:checked").val();
        var deptIds = ZKUI.ComboTree.get("deptTree${uuid}").getValue();
    // 	点击部门未选择部门
        if(checkVal == "2" && deptIds == "")
        {
            messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'pers_widget_noDept'/>"});
            return false;
        }
        if(deptIds != undefined && deptIds != null && deptIds != ""){
            var personCount = getPersonCountByDeptIds(deptIds);
            if (parseInt(personCount) == 0){
                messageBox({messageType: "alert", title: "<@i18n 'common_prompt_title'/>", text: "<@i18n 'pers_widget_noDeptPerson'/>"});
                return false;
            }
        }


        DhxCommon.closeWindow();
        var opts = {
            dealPath: "persPersonnalList.do?addPerson",//进度处理请求
            type: "single",//进度类型，取值single,public,custom
            onCallback:"reloadAddPerson",//处理完后回调
            height:250,//弹窗高度
            title:"<@i18n 'common_progress_proCmd'/>", //弹窗标题
            useReq:true,//使用请求参数作为进度条参数
            autoClose:true,//自动关闭
            delay:5,//自动关闭时间(5秒后自动关闭)
            data:{
                personnelListId : personnelListId,
                personIds : value,
                "checkVal" : checkVal,
	            "deptIds" : deptIds
            }
        }
        openProcess(opts);
        return false;
    }
    function reloadAddPerson() {
        finishAddPersonProcess();
    }
    function persDeptSwith(t){
    	if(t == 1){
            ZKUI.ComboTree.get("deptTree${uuid}").clear();
            $("#persMaskLayer${uuid},#persDeptTree${uuid}").hide();
    	}
    	else if(t == 2){
        	$("#groupSelectChannel${uuid}ok").show();
            $("#persMaskLayer${uuid},#persDeptTree${uuid}").show();
            $("#"+ ZKUI.Select.get("groupSelectChannel${uuid}").options.gridName+"ok").attr("disabled", false);
        }
    };
    function getPersonCountByDeptIds(deptIds) {
    var personCount = 0;
    $.ajax({
        type: "post",
        dataType: "json",
        async:false,
        url: "persPerson.do?getPersonCountByDeptIds",
        data : {
            "deptIds" : deptIds
        },
        success: function (data)
        {
            personCount = data.data;
        }
    });
    return personCount;
}

</script>