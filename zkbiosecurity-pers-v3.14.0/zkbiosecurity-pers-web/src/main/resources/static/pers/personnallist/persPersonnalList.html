<#assign  gridName="persPersonnalListGrid${uuid!}">
<@ZKUI.DGrid gridName="${gridName}" >
<@ZKUI.LeftGrid title="pers_personnal_list">
<@ZKUI.Searchbar>
<@ZKUI.SearchTop>

<tr>
    <td valign="middle">
        <@ZKUI.Input name="name"  maxlength="10" title="pers_personnal_list_name" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Combo name="type" empty="true" id="type${uuid}" width="148" title="pers_personnal_list_type" key="PersPersonnallistType" value="${(item.type)!}" />
    </td>
</tr>

</@ZKUI.SearchTop>
<@ZKUI.SearchBelow>

</@ZKUI.SearchBelow>
</@ZKUI.Searchbar>
<@ZKUI.Toolbar>
<@ZKUI.ToolItem type="refresh" permission="pers:personnallist:refresh" ></@ZKUI.ToolItem>
<@ZKUI.ToolItem type="add" id="persPersonnalList.do?edit" permission="pers:personnallist:add" ></@ZKUI.ToolItem>
<@ZKUI.ToolItem type="delete" id="persPersonnalList.do?del" permission="pers:personnallist:del"></@ZKUI.ToolItem>
</@ZKUI.Toolbar>
<@ZKUI.Grid onRowSelect="leftGridcClick" autoFirst=true vo="com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem" query="persPersonnalList.do?list"/>
</@ZKUI.LeftGrid>
<@ZKUI.RightGrid title="pers_person" leftFieldName="personnallistId">
<@ZKUI.Searchbar>
<@ZKUI.SearchTop>
<tr>
    <td valign="middle">
        <@ZKUI.Input name="personPin" maxlength="30" title="pers_person_pin" type="text"/>
    </td>
    <td valign="middle">
        <@ZKUI.Input name="likeName" maxlength="30" title="pers_person_wholeName" type="text"/>
    </td>
</tr>
</@ZKUI.SearchTop>
<@ZKUI.SearchBelow>

</@ZKUI.SearchBelow>
</@ZKUI.Searchbar>
<@ZKUI.Toolbar>
<@ZKUI.ToolItem type="refresh" permission="pers:personnallist:refreshPerson"></@ZKUI.ToolItem>
<@ZKUI.ToolItem type="delete" id="persPersonnalList.do?delPerson&personnallistId=(@id:gs)&&ids=(id)" action="personRightGridCommonDel" text="pers_common_delPerson" img="comm_del.png" permission="pers:personnallist:delPerson"></@ZKUI.ToolItem>
</@ZKUI.Toolbar>
<@ZKUI.Grid vo="com.zkteco.zkbiosecurity.pers.vo.PersPersonnallistPersonItem" query="persPersonnalList.do?getPersonList"/>
</@ZKUI.RightGrid>
</@ZKUI.DGrid>
<script type="text/javascript">
    // 双列表右列表删除功能
    function personRightGridCommonDel(id, bar, opt) {
        if (bar) {
            var gridName = bar.gridName || "gridbox";
            var rightGrid = ZKUI.Grid.get(gridName);

            var leftGrid = ZKUI.DGrid.get(rightGrid.options.dGridName).leftGrid;
            var ids = ZKUI.Grid.GRID_PULL[gridName].grid.getCheckedRows(0);
            if (ids == "") {
                messageBox({messageType: "alert", text: "common_prompt_selectObj"});
            }
            else {
                deleteConfirm(function (result) {
                    if (result) {
                        openMessage(msgType.loading);
                        var param = splitURL(fillParamsFromGrid(gridName, ids, id));
                        $.ajax({
                            url: param.url,
                            type: "post",
                            data: param.data,
                            success: function (result) {
                                closeMessage();
                                dealRetResult(eval(result), function () {
                                    ZKUI.Grid.reloadGrid(gridName,function () {
                                        finishRightGridCommonDel();
                                    });
                                });

                            }
                        });
                    }
                }, "common_prompt_sureToDelThese");
            }
        }
    }

    //删除成功回调处理
    function finishRightGridCommonDel() {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
        var rowId = leftGrid.grid.getSelectedRowId();
        var colIndex = leftGrid.grid.getColIndexById("personCount");//人员数列索引
        var count = rightGrid.grid.getRowsNum();
        leftGrid.grid.cells(rowId, colIndex).cell.innerHTML = count;//更新个数
        rightGrid.reload();
    }

    //leftGrid行选中点击事件
    function leftGridcClick(rid, ind) {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var rightGrid = dbGrid.rightGrid;
        attSelectRow = ind;
        rightGrid.reload(function() {},{personnallistId: rid});
    }
    function reloadLeftGrid() {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        dbGrid.rightGrid.reload();
        dbGrid.leftGrid.reload();
    }
    function addPerson(gridName, domObj, rid) {
        var dbGrid = ZKUI.DGrid.get("${gridName}");
        var leftGrid = dbGrid.leftGrid;
        var row = leftGrid.grid.getRowData(rid);//获取左表格选中行数据
        var path = "skip.do?page=pers_personnallist_personnalListSelectPersonContent&personnelListId="+ rid ;
        var opts = {
            path:path,
            width: 880,
            height: 510,
            title:"<@i18n 'pers_common_addPerson'/>",
            gridName:"gridBox"
        };
        DhxCommon.createWindow(opts);
    }
    function finishAddPersonProcess() {
        var dbGrid = ZKUI.DGrid.get("${gridName}"); //双列表对象
        var leftGrid = dbGrid.leftGrid; //左表格对象
        var rightGrid = dbGrid.rightGrid; //右表格对象
        window.setTimeout(function(){
            leftGrid.reload();
            rightGrid.reload();
        },1000);
    }

</script>



