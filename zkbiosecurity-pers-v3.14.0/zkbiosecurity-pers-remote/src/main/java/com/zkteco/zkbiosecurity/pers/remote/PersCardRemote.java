/**
 * File Name: PersCard Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;

@RequestMapping(value = "persCard.do")
public interface PersCardRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(PersCardItem condition);

    /**
     * 批量挂失
     * 
     * @param ids
     * @return
     */
    @RequestMapping(params = "batchCardLoss")
    @ResponseBody
    ZKResultMsg batchCardLoss(@RequestParam("ids") String ids);

    /**
     * 批量解挂
     * 
     * @param ids
     * @return
     */
    @RequestMapping(params = "batchCardRevert")
    @ResponseBody
    ZKResultMsg batchCardRevert(@RequestParam("ids") String ids);

    /**
     * 检查cardNo的是否存在
     * 
     * @param cardNo
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    String isExist(@RequestParam("cardNo") String cardNo, @RequestParam(value = "pin", required = false) String pin);

    /**
     * 检查cardNo长度
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/23 16:10
     * @param cardNo
     * @return java.lang.String
     */
    @RequestMapping(params = "checkCardBit")
    @ResponseBody
    String checkCardBit(@RequestParam("cardNo") String cardNo);

    /**
     * 卡校验
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/8/10 13:59
     * @param cardNo
     * @return java.lang.String
     */
    @RequestMapping(params = "cardVerification")
    @ResponseBody
    Map<String, String> cardVerification(@RequestParam("cardNo") String cardNo);

    /**
     * 导出数据
     * 
     * @param request:
     * @param response:
     * @return void
     * <AUTHOR>
     * @throws @date 2022-01-14 14:21
     * @since 1.0.0
     */
    @RequestMapping(params = "export")
    @ResponseBody
    void export(HttpServletRequest request, HttpServletResponse response);
}