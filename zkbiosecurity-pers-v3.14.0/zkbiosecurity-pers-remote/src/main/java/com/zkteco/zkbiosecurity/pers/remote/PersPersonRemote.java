/**
 * File Name: PersPerson Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import java.io.IOException;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeExtItem;
import com.zkteco.zkbiosecurity.pers.vo.PersCertificateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

@RequestMapping(value = "persPerson.do")
public interface PersPersonRemote {
    /**
     * 默认页面跳转
     *
     * @return
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     *
     * @param id
     * @param deptId
     * @return
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id,
        @RequestParam(value = "leaveId", required = false) String leaveId,
        @RequestParam(value = "deptId", required = false) String deptId,
        @RequestParam(value = "cardNo", required = false) String cardNo);

    /**
     * 保存复合实体内容
     *
     * @param item
     * @param certificate
     * @param attributeExt
     * @param extParams
     * @param file
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(PersPersonItem item, PersCertificateItem certificate, PersAttributeExtItem attributeExt,
        @RequestParam Map<String, String> extParams,
        @RequestParam(value = "personPhoto", required = false) MultipartFile file);

    /**
     * 列表数据获取
     *
     * @param condition
     * @return
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(PersPersonItem condition);

    /**
     * 删除数据
     *
     * @param ids
     * @return
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam("ids") String ids);

    /**
     * 检查checkPwd的最大长度
     *
     * @param personId
     * @param personPwd
     * @return
     */
    @RequestMapping(params = "checkPwd")
    @ResponseBody
    String checkPwd(@RequestParam("personId") String personId, @RequestParam("personPwd") String personPwd);

    /**
     * 检查pin的是否存在
     *
     * @param pin
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    String isExist(@RequestParam("pin") String pin);

    /**
     * dataCount 数据统计
     *
     * @return
     */
    @RequestMapping(params = "dataCount")
    @ResponseBody
    ZKResultMsg dataCount();

    /**
     * 批次修改人员属性跳转
     *
     * @param ids
     * @param type
     * @return
     */
    @RequestMapping(params = "batch")
    ModelAndView batch(@RequestParam("ids") String ids, @RequestParam("type") String type);

    /**
     * 批量调整职位
     *
     * @param ids
     * @param positionId
     * @param changeReason
     * @return
     */
    @RequestMapping(params = "batchPositionChange")
    @ResponseBody
    ZKResultMsg batchPositionChange(@RequestParam("ids") String ids, @RequestParam("positionId") String positionId,
        @RequestParam("changeReason") String changeReason);

    /**
     * 批量调整部门
     *
     * @param ids
     * @param deptId
     * @param changeReason
     * @param flag
     * @return
     */
    @RequestMapping(params = "batchDeptChange")
    @ResponseBody
    ZKResultMsg batchDeptChange(@RequestParam("ids") String ids, @RequestParam("deptId") String deptId,
        @RequestParam("changeReason") String changeReason, @RequestParam("flag") boolean flag);

    /**
     * 检查证件
     *
     * @param certNumber
     * @param personId
     * @param certType
     * @return
     */
    @RequestMapping(params = "checkCertNumber")
    @ResponseBody
    String checkCertNumber(@RequestParam("certNumber") String certNumber,
        @RequestParam(value = "personId", required = false) String personId, @RequestParam("certType") String certType);

    /**
     * 检测邮箱设置
     *
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/10 14:58
     */
    @RequestMapping(params = "checkMailParam")
    @ResponseBody
    String checkMailParam();

    /**
     * 验证邮箱是否存在
     * 
     * @return
     * @return String
     * <AUTHOR>
     */
    @RequestMapping(params = "checkEmailIsExist")
    @ResponseBody
    String checkEmailIsExist(@RequestParam("personId") String personId, @RequestParam(value = "email") String email);

    /**
     * 导入数据
     *
     * @return
     * @author: verber
     * @date: 2018-08-02 16:02
     */
    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(@RequestParam("upload") MultipartFile upload) throws IOException;

    /**
     * 导出数据
     *
     * @return
     * @author: verber
     * @date: 2018-08-02 16:02
     */
    @RequestMapping(params = "export")
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导出人员信息模板
     * 
     * <AUTHOR>
     * @param request, response
     * @return void
     * @date 2019/7/9 9:48
     */
    @RequestMapping(params = "exportTemplate")
    void exportTemplate(HttpServletRequest request, HttpServletResponse response);

    /**
     * @param deptIds 部门id串，多个id由“,”分隔
     * @return 部门下的所有人员pin的拼接字符串
     * <AUTHOR> href="mailto:<EMAIL>">jinjie.you</a>
     * @date 2018/8/9 14:33
     */
    @RequestMapping(params = "getDeptPins")
    @ResponseBody
    String getDeptPins(String deptIds);

    /**
     * 重置员工自助登录密码
     *
     * @param ids
     * @return
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     */
    @RequestMapping(params = "resetSelfPwd")
    @ResponseBody
    ZKResultMsg resetSelfPwd(@RequestParam("ids") String ids);

    /**
     * 导入人员照片
     *
     * @param personPhoto
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/20 21:10
     */
    @RequestMapping(params = "savePhoto")
    @ResponseBody
    ZKResultMsg savePhoto(@RequestParam("pin") String pin,
        @RequestParam(value = "photoQuality", defaultValue = "false") Boolean photoQuality,
        @RequestParam(value = "personPhoto") MultipartFile personPhoto);

    /**
     * 处理上传的图片压缩包文件
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/7/25 16:09
     * @param uploadFile
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "saveLargePhoto")
    @ResponseBody
    ZKResultMsg saveLargePhoto(@RequestParam(value = "uploadFile") MultipartFile uploadFile);

    /**
     * 导出人员对比照片
     *
     * @param
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/26 10:36
     */
    @RequestMapping(params = "exportPhoto")
    void exportPhoto() throws Exception;

    /**
     * 制卡界面跳转
     * 
     * <AUTHOR>
     * @since 2018年12月14日 下午2:13:52
     * @param ids
     * @return
     */
    @RequestMapping(params = "cardPrint")
    ModelAndView cardPrint(@RequestParam("ids") String ids);

    /**
     * 根据key值获取国籍名称
     * 
     * <AUTHOR>
     * @param countryKey
     * @return java.lang.String
     * @date 2019/2/20 17:52
     */
    @RequestMapping(params = "getCountry")
    @ResponseBody
    ZKResultMsg getCountry(@RequestParam("countryKey") String countryKey);

    /**
     * 检测短信猫设置
     * 
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-14 15:50
     * @param
     * @return java.lang.String
     */
    @RequestMapping(params = "checkSMSModemParam")
    @ResponseBody
    String checkSMSModemParam();

    /**
     * 判断人员图片是否可抠图
     * 
     * <AUTHOR>
     * @param file, personIdPhoto
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @date 2020/4/1 9:05
     */
    @RequestMapping(params = "validPersonPhoto")
    @ResponseBody
    ZKResultMsg validPersonPhoto(@RequestParam(value = "personPhoto", required = false) MultipartFile file,
        @RequestParam(value = "personIdPhoto", required = false) String personIdPhoto);

    @RequestMapping(params = "verifyModeTree")
    @ResponseBody
    TreeItem verifyModeTree();

    /**
     * 删除人员生物模板页面跳转
     *
     * @param ids:
     * @return org.springframework.web.servlet.ModelAndView
     * <AUTHOR>
     * @throws @date 2020-09-24 11:57
     * @since 1.0.0
     */
    @RequestMapping(params = "getBioTemplateView")
    ModelAndView getBioTemplateView(@RequestParam("ids") String ids);

    @RequestMapping(params = "deleteBioTemplate")
    @ResponseBody
    ZKResultMsg deleteBioTemplate(@RequestParam Map<String, String> params);

    /**
     * 下载离职人员导入模版
     * 
     * <AUTHOR>
     * @Date 2020/7/23 11:42
     * @param request
     * @param response
     * @Return void
     */
    @RequestMapping(params = "exportLeavePersonTemplate")
    void exportLeavePersonTemplate(HttpServletRequest request, HttpServletResponse response);

    /**
     * 根据人员id获取部门id
     *
     * @param personId 要查找的人员id
     * @return 部门id
     * @throws <AUTHOR>
     * @date 2020/11/17 17:07
     * @since 1.1.1
     */
    @RequestMapping(params = "getDeptIdByPersonId")
    @ResponseBody
    ZKResultMsg getDeptIdByPersonId(@RequestParam(value = "personId") String personId);

    /**
     * 导入离职人员
     * 
     * <AUTHOR>
     * @Date 2020/7/26 15:36
     * @param upload
     * @Return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "importLeavePerson")
    @ResponseBody
    ZKResultMsg importLeavePersonExcel(@RequestParam("upload") MultipartFile upload) throws IOException;

    /**
     * 导出人员信息
     * 
     * @return org.springframework.web.servlet.ModelAndView
     * <AUTHOR>
     * @throws @date 2021-07-14 14:19
     * @since 1.0.0
     */
    @RequestMapping(params = "exportPage")
    ModelAndView exportPage();

    /**
     * 根据编号获取头像base64
     *
     * @param pin:人员编号
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-09-27 18:14
     * @since 1.0.0
     */
    @RequestMapping(params = "getPersonPhotoBase64ByPin")
    @ResponseBody
    ZKResultMsg getPersonPhotoBase64ByPin(String pin);

    /**
     * 检查手机号码是否存在
     * 
     * @param mobilePhone:
     * @return java.lang.String
     * <AUTHOR>
     * @throws @date 2021-09-29 11:26
     * @since 1.0.0
     */
    @RequestMapping(params = "checkMobileIsExist")
    @ResponseBody
    String checkMobileIsExist(@RequestParam("mobilePhone") String mobilePhone);

    /**
     * 根据部门查询人员数量
     * 
     * @param deptIds:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-07-05 15:47
     * @since 1.0.0
     */
    @RequestMapping(params = "getPersonCountByDeptIds")
    @ResponseBody
    ZKResultMsg getPersonCountByDeptIds(@RequestParam(value = "deptIds") String deptIds);

    /**
     * 启用凭证
     *
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-06-17 17:45
     * @since 1.0.0
     */
    @RequestMapping(params = "enabledPersCredential")
    @ResponseBody
    ZKResultMsg enabledPersCredential(@RequestParam("ids") String ids);

    /**
     * 禁用凭证
     *
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-06-17 17:45
     * @since 1.0.0
     */
    @RequestMapping(params = "disableCredential")
    @ResponseBody
    ZKResultMsg disableCredential(@RequestParam("ids") String ids);

    /**
     * 查看比对照片界面跳转
     *
     * @param ids:
     * @return org.springframework.web.servlet.ModelAndView
     * <AUTHOR>
     * @throws
     * @date 2023-03-13 14:44
     * @since 1.0.0
     */
    @RequestMapping(params = "viewBioPhoto")
    ModelAndView viewBioPhoto(@RequestParam(value = "ids") String ids);

    /**
     * 检测whatsapp设置
     * 
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2023-03-21 14:02
     * @since 1.0.0
     */
    @RequestMapping(params = "checkWhatsappParam")
    @ResponseBody
    String checkWhatsappParam();

    /**
     * 启用app登录
     *
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2023-10-10 11:56
     * @since 1.0.0
     */
    @RequestMapping(params = "enabledApplogin")
    @ResponseBody
    ZKResultMsg enabledApplogin(@RequestParam("ids") String ids);

    /**
     * 禁用app登录
     *
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2023-10-10 11:56
     * @since 1.0.0
     */
    @RequestMapping(params = "disableApplogin")
    @ResponseBody
    ZKResultMsg disableApplogin(@RequestParam("ids") String ids);

    /**
     * 获取ACMS中的卡
     * 
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-03-07 17:20
     * @since 1.0.0
     */
    @RequestMapping(params = "obtainAcmsCard")
    @ResponseBody
    ZKResultMsg obtainAcmsCard();

    /**
     * 提取人脸模板
     * 
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-04-30 10:49
     * @since 1.0.0
     */
    @RequestMapping(params = "getFaceTemplate")
    @ResponseBody
    ZKResultMsg getFaceTemplate();

    /**
     * 批量提取人脸模板
     * 
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-05-09 14:07
     * @since 1.0.0
     */
    @RequestMapping(params = "batchFaceTemplate")
    @ResponseBody
    ZKResultMsg batchFaceTemplate(@RequestParam("ids") String ids);

    /**
     * 获取卡号二维码并判断是否开启静态二维码
     * 
     * @auther 31876
     * @date 11:50
     * @since 1.0.0
     */
    @RequestMapping(params = "getCardQrCodeByCardNo")
    @ResponseBody
    ZKResultMsg getCardQrCodeByCardNo(@RequestParam("cardNo") String cardNo);

    /**
     * 判断是否开启二维码
     * 
     * @auther 31876
     * @date 11:50
     * @since 1.0.0
     */
    @RequestMapping(params = "isOpenStaticQrCode")
    @ResponseBody
    ZKResultMsg isOpenStaticQrCode();

    /**
     * 同步人员到ACMS
     * 
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-12-26 11:26
     * @since 1.0.0
     */
    @RequestMapping(params = "syncPersonAcms")
    @ResponseBody
    ZKResultMsg syncPersonAcms(@RequestParam("ids") String ids);
}