/**
 * File Name: PersAttribute Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;

@RequestMapping(value = "persAttribute.do")
public interface PersAttributeRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(PersAttributeItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(PersAttributeItem condition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam("ids") String ids);

    /**
     * 判断是否存在显示名称
     * 
     * @param showName
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    String isExist(@RequestParam("id") String id, @RequestParam("showName") String showName);

    /**
     * 判断行列是否存在
     * 
     * @param id
     * @param positionX
     * @param positionY
     * @return
     */
    @RequestMapping(params = "isRowColExist")
    @ResponseBody
    String isRowColExist(@RequestParam("id") String id, @RequestParam("positionY") Integer positionX,
        @RequestParam("positionY") Integer positionY);

    @RequestMapping(params = "getAllPersAttrs")
    @ResponseBody
    ZKResultMsg getAllPersAttrs();

    /**
     * 获取自定义属性数量
     * 
     * @return java.lang.String
     * <AUTHOR>
     * @throws @date
     *             2020-09-15 17:51
     * @since 1.0.0
     */
    @RequestMapping(params = "countAttrList")
    @ResponseBody
    String countAttrList();
}