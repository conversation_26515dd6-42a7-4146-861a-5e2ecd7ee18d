package com.zkteco.zkbiosecurity.pers.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PerTempRegItem;

/**
 * 移动端临时人员注册及页面展示
 * 
 * <AUTHOR> href="mailto:<EMAIL>">colin.cheng</a>
 * @version V1.0
 * @date Created In 11:40 2019/3/28
 */
public interface PersTempRegRemote {

    /**
     * 移动端注册
     * 
     * @param item
     * @return
     */
    @RequestMapping("tokenPersTempReg")
    @ResponseBody
    ZKResultMsg registrarLogin(PerTempRegItem item);

    /**
     * 获取移动端页面
     * 
     * @return
     */
    @RequestMapping("/tokenAdreg")
    ModelAndView regView();
}
