/**
 * File Name: AccLevel Created by GenerationTools on 2018-03-02 下午02:15 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.pers.vo.PersDeviceSelectItem;

@RequestMapping(value = "persDevice.do")
public interface PersDeviceRemote {

    /**
     * 卡格式测试：选择设备双列表
     * 
     * @param condition
     * @return
     */
    @RequestMapping(params = "selectDeviceList")
    @ResponseBody
    DxGrid selectDeviceList(PersDeviceSelectItem condition);

}