/**
 * File Name: PersWiegandFmt Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;

@RequestMapping(value = "persWiegandFmt.do")
public interface PersWiegandFmtRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id,
        @RequestParam(value = "cardFmt", required = false) String cardFmts,
        @RequestParam(value = "parityFmt", required = false) String parityFmts,
        @RequestParam(value = "wiegandCount", required = false) String wiegandCount,
        @RequestParam(value = "siteCode", required = false) String siteCodes);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(PersWiegandFmtItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(PersWiegandFmtItem condition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam("ids") String ids);

    /**
     * 判断名称是否存在
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/7/27 10:59
     * @param name
     * @return java.lang.String
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    String isExist(@RequestParam("name") String name);

    @RequestMapping(params = "checkSiteCode")
    @ResponseBody
    ZKResultMsg checkSiteCode(String siteCode, String siteCodeCount);

    /**
     * 是否存在门禁模块
     */
    @RequestMapping(params = "isExistAcc")
    @ResponseBody
    boolean isExistAcc();
}