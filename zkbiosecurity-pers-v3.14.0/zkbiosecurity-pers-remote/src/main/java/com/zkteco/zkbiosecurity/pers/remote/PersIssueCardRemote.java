/**
 * File Name: PersIssueCard Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersIssueCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersNoCardPersonItem;

@RequestMapping(value = "persIssueCard.do")
public interface PersIssueCardRemote {
    /**
     * 默认页面跳转
     *
     * @return
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 列表数据获取
     *
     * @param condition
     * @return
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(PersIssueCardItem condition);

    /**
     * 获取没有卡人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/21 14:43
     * @param condition
     * @return com.zkteco.zkbiosecurity.base.bean.DxGrid
     */
    @RequestMapping(params = "noCardPerson")
    @ResponseBody
    DxGrid noCardPerson(PersNoCardPersonItem condition);

    /**
     * 批量发卡 [{cardNo:1,pin:11,cardType:1}]
     * 
     * @param cardListJson
     * @return
     */
    @RequestMapping(params = "batch")
    @ResponseBody
    ZKResultMsg batch(@RequestParam("cardListJson") String cardListJson);

    /**
     * 导出数据
     * 
     * @param request:
     * @param response:
     * @return void
     * <AUTHOR>
     * @throws @date 2022-01-14 15:11
     * @since 1.0.0
     */
    @RequestMapping(params = "export")
    @ResponseBody
    void export(HttpServletRequest request, HttpServletResponse response);

    /**
     * ACMS发卡
     * 
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2023-08-23 11:30
     * @since 1.0.0
     */
    @RequestMapping(params = "batchAcmsCard")
    @ResponseBody
    ZKResultMsg batchAcmsCard();
}