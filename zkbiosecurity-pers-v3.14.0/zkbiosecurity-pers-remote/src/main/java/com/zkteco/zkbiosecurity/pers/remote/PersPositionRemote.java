/**
 * File Name: PersPosition Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersPositionItem;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RequestMapping(value = "persPosition.do")
public interface PersPositionRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param id
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(PersPositionItem item);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(PersPositionItem condition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam("ids") String ids);

    /**
     * 获取树列表
     * 
     * @author: juvenile.li
     * @date: 2018-03-01 下午19:38
     * @return
     */
    @RequestMapping(params = "tree")
    @ResponseBody
    TreeItem tree();

    /**
     * 检查code的是否存在
     * 
     * @param code
     * @return
     */
    @RequestMapping(params = "isExist")
    @ResponseBody
    String isExist(@RequestParam("code") String code);

    /**
     * 检查name的是否存在
     * 
     * @param name
     * @return
     */
    @RequestMapping(params = "isNameExist")
    @ResponseBody
    ZKResultMsg isNameExist(@RequestParam("name") String name, @RequestParam("id") String id,
        @RequestParam("parentId") String parentId);
    /*
     * 下载导入模版
     * <AUTHOR>
     * @Date 2020/7/26 15:26
     * @param request
     * @param response
     * @Return void
     */
    @RequestMapping(params = "exportTemplate")
    void exportTemplate(HttpServletRequest request, HttpServletResponse response);

    /*
     * 批量导出
     * <AUTHOR>
     * @Date 2020/7/26 15:36
     * @param req
     * @param resp
     * @Return void
     */
    @RequestMapping(params = "export")
    @ResponseBody
    void export(HttpServletRequest req, HttpServletResponse resp);

    /*
     * 导入职位
     * <AUTHOR>
     * @Date 2020/7/26 16:39
     * @param upload
     * @Return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(@RequestParam("upload") MultipartFile upload) throws IOException;
}