/*
 * File Name: PersLoginRemote
 * <NAME_EMAIL> on 2018/8/6 17:58.
 * Copyright:Copyright © 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">lining.bi</a>
 * @version v1.0
 */
@RequestMapping(value="/api/persLogin.do")
public interface PersLoginRemote {
    /**
     *  员工登录验证
     * <AUTHOR>
     * @date 2018/8/6 10:57
     * @param pin
     * @param password
     * @return 返回token
     */
    @RequestMapping(params = "persLogin")
    @ResponseBody
    ZKResultMsg persLogin(@RequestParam String pin, @RequestParam String password);

    /**
     * 根据pin值获取登录对象
     * <AUTHOR>
     * @date 2018/8/6 11:43
     * @param token
     */
    @RequestMapping(params = "getPersObjectByPin")
    @ResponseBody
    ZKResultMsg getPersObjectByPin(@RequestParam String token);

}
