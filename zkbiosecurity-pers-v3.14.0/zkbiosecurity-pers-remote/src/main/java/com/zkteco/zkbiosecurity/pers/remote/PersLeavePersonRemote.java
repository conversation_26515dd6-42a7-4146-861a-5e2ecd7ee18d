/**
 * File Name: Pers<PERSON>eave<PERSON>erson Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;

@RequestMapping(value = "persLeavePerson.do")
public interface PersLeavePersonRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 编辑界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param ids
     * @return
     */
    @RequestMapping(params = "edit")
    ModelAndView edit(@RequestParam(value = "id", required = false) String id,
        @RequestParam(value = "ids", required = false) String ids,
        @RequestParam(value = "pins", required = false) String pins,
        @RequestParam(value = "names", required = false) String names);

    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param item
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(PersLeavePersonItem item, @RequestParam(value = "personIds", required = false) String personIds);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param condition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(PersLeavePersonItem condition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg del(@RequestParam("ids") String ids);

    /**
     * 批量导出
     * 
     * @param req
     * @param resp
     */
    @RequestMapping(params = "export")
    @ResponseBody
    void export(HttpServletRequest req, HttpServletResponse resp);
}