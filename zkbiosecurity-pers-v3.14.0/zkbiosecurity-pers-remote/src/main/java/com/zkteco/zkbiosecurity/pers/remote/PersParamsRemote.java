/**
 * File Name: PersCard Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.Map;

@RequestMapping(value = "persParams.do")
public interface PersParamsRemote {
    /**
     * 保存复合实体内容
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @param params
     * @return
     */
    @RequestMapping(params = "save")
    @ResponseBody
    ZKResultMsg save(@RequestParam Map<String, String> params);

    /**
     * 获取人事参数
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/8 15:59
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    @RequestMapping(params = "getParams")
    @ResponseBody
    ZKResultMsg getParams();

    /**
     * 检查pin的最大长度
     * 
     * @param pinLen
     * @return
     */
    @RequestMapping(params = "checkMaxPinLenth")
    @ResponseBody
    String checkMaxPinLenth(@RequestParam("pers.pinLen") int pinLen);

    /**
     * 检查人员数量
     *
     * @param pinSupportLetter
     * @return
     */
    @RequestMapping(params = "checkPinLetters")
    @ResponseBody
    ZKResultMsg checkPinLetters(@RequestParam("pinSupportLetter") String pinSupportLetter);

    /**
     * 检测pin是否支持字符的设备
     *
     * @param pinSupportIncrement
     * @return
     */
    @RequestMapping(params = "checkPinIncrement")
    @ResponseBody
    String checkPinIncrement(@RequestParam("pinSupportIncrement") String pinSupportIncrement);

    /**
     * 检测系统是否存在卡
     * 
     * @return
     */
    @RequestMapping(params = "checkIsExistCard")
    @ResponseBody
    String checkIsExistCard();

    /**
     * 检测系统是否存在多卡
     * 
     * @return
     */
    @RequestMapping(params = "checkIsExistCards")
    @ResponseBody
    String checkIsExistCards();

    /**
     * 判断是否单考勤系统
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/20 15:56
     * @return: java.lang.String
     **/
    @RequestMapping(params = "checkSingleAttSystem")
    @ResponseBody
    String checkSingleAttSystem();

    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-02-24 上午09:38
     * @return
     */
    @RequestMapping
    ModelAndView index();

    @RequestMapping(params = "loginTemplateServer")
    @ResponseBody
    ZKResultMsg loginTemplateServer();

    /**
     * 测试人脸模板服务连接
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2023-08-07 17:40
     * @since 1.0.0
     */
    @RequestMapping(params = "testTemplateServerConnect")
    @ResponseBody
    ZKResultMsg testTemplateServerConnect();

    /**
     * 测试人脸后台比对服务连接
     *
     * @return
     */
    @RequestMapping(params = "testFaceServerConnect")
    @ResponseBody
    ZKResultMsg testFaceServerConnect();
}