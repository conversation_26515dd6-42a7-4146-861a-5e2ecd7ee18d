package com.zkteco.zkbiosecurity.pers.remote;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonPersonnalListSelectItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnallistPersonItem;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
 * 名单库页面接口
 * <AUTHOR>
 * @date:	2020-07-22 16:35:16
 * @version v1.0
 */
@RequestMapping(value="/persPersonnalList.do")
public interface PersPersonnalListRemote {
    /**
	 * 默认页面跳转
	 * @author:	GenerationTools
	 * @date:	2020-07-22 16:35:16
	 * @return
	 */
	@RequestMapping
	ModelAndView index();

	
	/**
	 * 编辑界面跳转
	 * @author:	GenerationTools
	 * @date:	2020-07-22 16:35:16
	 * @param id
	 * @return
	 */
	@RequestMapping(params = "edit")
	ModelAndView edit(@RequestParam(value = "id", required = false) String id);
	
	/**
	 * 保存复合实体内容
	 * @author:	GenerationTools
	 * @date:	2020-07-22 16:35:16
	 * @param item
	 * @return
	 */
	@RequestMapping(params = "save")
	@ResponseBody
	ZKResultMsg save(PersPersonnalListItem item) ;
	
	/**
	 * 列表数据获取
	 * @author:	GenerationTools
	 * @date:	2020-07-22 16:35:16
	 * @param codition
	 * @return
	 */
	@RequestMapping(params = "list")
	@ResponseBody
	DxGrid list(PersPersonnalListItem codition);

	/**
	 * 级联删除数据
	 * @author:	GenerationTools
	 * @date:	2020-07-22 16:35:16
	 * @param ids
	 * @return
	 */
	@RequestMapping(params = "del")
	@ResponseBody
	ZKResultMsg delete(@RequestParam(value = "ids") String ids);

	/**
	 * 根据名单库id取名单库里的人员id集合
	 *
	 * <AUTHOR>
	 * @date   2020/7/24 8:44
	 */
	@RequestMapping(params = "getPersonIdList")
	@ResponseBody
	ZKResultMsg getPersonList(String id);

	/**
	 * 获取人员分页数据
	 *
	 * <AUTHOR>
	 * @date   2020/7/24 9:19
	 */
	@RequestMapping(params = "getPersonList")
	@ResponseBody
	DxGrid getPersonneList(PersPersonnallistPersonItem condition);

	/**
	 * 添加人员
	 *
	 * <AUTHOR>
	 * @date   2020/7/24 9:39
	 */
	@RequestMapping(params = "addPerson")
	@ResponseBody
	ZKResultMsg addPerson(String personnelListId, String personIds);

	/**
	 * 获取人员信息
	 *
	 * <AUTHOR>
	 * @date   2020/7/24 14:56
	 */
	@RequestMapping(params = "selectPersonlist")
	@ResponseBody
	DxGrid selectPersonlist(PersPersonPersonnalListSelectItem codition);

	/**
	 * 删除人员
	 *
	 * <AUTHOR>
	 * @date   2020/7/24 14:56
	 */
	@RequestMapping(params = "delPerson")
	@ResponseBody
	ZKResultMsg delPerson(@RequestParam(value = "personnallistId") String personnallistId, @RequestParam(value = "ids") String ids);

	/**
	 * 名单库名称唯一性验证
	 *
	 * <AUTHOR>
	 * @date   2020/8/4 9:53
	 */
	@RequestMapping(params = "verifyName")
	@ResponseBody
	Boolean verifyName(String name);

	/**
	 * 获取名单库下拉数据
	 *
	 * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
	 * <AUTHOR>
	 * @date 2020-12-24 10:53
	 * @since 1.0.0
	 */
	@RequestMapping(params = "getPersonnalListSelectData")
	@ResponseBody
	ZKResultMsg getPersonnalListSelectData();
}
