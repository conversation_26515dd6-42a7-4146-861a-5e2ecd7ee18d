/**
 * File Name: PersBioTemplate Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@RequestMapping(value = "persBioTemplate.do")
public interface PersBioTemplateRemote {

    @RequestMapping(params = "import")
    @ResponseBody
    ZKResultMsg importExcel(MultipartFile upload);

    @RequestMapping(params = "export")
    void export();
}