package com.zkteco.zkbiosecurity.pers.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import com.zkteco.zkbiosecurity.base.bean.DxGrid;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersTempPersonItem;

@RequestMapping(value = "persTempPerson.do")
public interface PersTempPersonRemote {
    /**
     * 默认页面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-08-07 11:30:03
     * @return
     */
    @RequestMapping
    ModelAndView index();

    /**
     * 审核
     * 
     * @author: GenerationTools
     * @date: 2018-08-07 11:30:03
     * @param item
     * @return
     */
    @RequestMapping(params = "audit")
    @ResponseBody
    ZKResultMsg audit(PersTempPersonItem item);

    /**
     * 查看界面跳转
     * 
     * @author: GenerationTools
     * @date: 2018-08-07 11:30:03
     * @param id
     * @return
     */
    @RequestMapping(params = "view")
    ModelAndView view(@RequestParam(value = "id", required = false) String id);

    /**
     * 列表数据获取
     * 
     * @author: GenerationTools
     * @date: 2018-08-07 11:30:03
     * @param codition
     * @return
     */
    @RequestMapping(params = "list")
    @ResponseBody
    DxGrid list(PersTempPersonItem codition);

    /**
     * 级联删除数据
     * 
     * @author: GenerationTools
     * @date: 2018-08-07 11:30:03
     * @param ids
     * @return
     */
    @RequestMapping(params = "del")
    @ResponseBody
    ZKResultMsg delete(@RequestParam(value = "ids") String ids);

    /**
     * 审核页面跳转
     *
     * @param id
     * @return
     */
    @RequestMapping(params = "auditView")
    ModelAndView auditView(@RequestParam(value = "id", required = false) String id);

    @RequestMapping(params = "getTempPersonById")
    @ResponseBody
    ZKResultMsg getTempPersonById(@RequestParam(value = "id") String id);
}
