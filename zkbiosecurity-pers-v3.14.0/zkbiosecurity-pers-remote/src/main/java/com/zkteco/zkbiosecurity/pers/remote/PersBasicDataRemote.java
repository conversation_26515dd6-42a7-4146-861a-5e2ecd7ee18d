package com.zkteco.zkbiosecurity.pers.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * <AUTHOR>
 * @Date: 2019/7/2 14:05
 */
@RequestMapping(value = "persBasicData.do")
public interface PersBasicDataRemote {

    /**
     * 从二号项目迁移代码：同步人事基础数据到云端
     * 
     * @auther zbx.zhong
     * @date 2019/7/5 14:06
     * @return
     */
    @RequestMapping(params = "syncAllPersonToCloud")
    @ResponseBody
    ZKResultMsg syncAllPersonToCloud();

    /**
     * 从二号项目迁移代码：从云端同步人事基础数据
     * 
     * @auther zbx.zhong
     * @date 2019/7/5 14:06
     * @return
     */
    @RequestMapping(params = "syncAllPersonFromCloud")
    @ResponseBody
    ZKResultMsg syncAllPersonFromCloud();
}
