/**
 * File Name: PersCardPrintTemplate Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.remote;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 自定义制卡模板
 * 
 * <AUTHOR>
 * @since 2018/12/17 14:34
 */
@RequestMapping(value = "persCardPrintTemplate.do")
public interface PersCardPrintTemplateRemote {

    /**
     * 制卡模板获取人员信息
     * 
     * <AUTHOR>
     * @param personId
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @date 2018/12/17 18:03
     */
    @RequestMapping(params = "getPersonData")
    @ResponseBody
    ZKResultMsg getPersonData(@RequestParam("personId") String personId);

    /**
     * 生成模板图片
     * 
     * <AUTHOR>
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @date 2019/2/12 17:32
     */
    @RequestMapping(params = "getPicTemplate")
    @ResponseBody
    ZKResultMsg getPicTemplate(@RequestParam(value = "templateId") String templateId,
        @RequestParam(value = "personId") String personId);

    /**
     * 判断是否有制卡许可是否显示制卡按钮
     * 
     * <AUTHOR>
     * @param
     * @return java.lang.Boolean
     * @date 2019/2/28 15:09
     */
    @RequestMapping(params = "isShowCardPrint")
    @ResponseBody
    Boolean isShowCardPrint();
}