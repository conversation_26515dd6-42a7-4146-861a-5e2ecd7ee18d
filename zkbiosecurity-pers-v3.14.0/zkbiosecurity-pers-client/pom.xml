<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zkbiosecurity-pers</artifactId>
        <groupId>com.zkteco</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zkbiosecurity-pers-client</artifactId>
    <name>${project.artifactId}</name>
    <packaging>jar</packaging>
    <version>${project.parent.version}</version>

    <dependencies>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-auth-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-license-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-scheduler</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-pers-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-system-api</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-core</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-module-all</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-cloud-sdk</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.zkteco</groupId>
            <artifactId>zkbiosecurity-business-sdk</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>pl.project13.maven</groupId>
                <artifactId>git-commit-id-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>