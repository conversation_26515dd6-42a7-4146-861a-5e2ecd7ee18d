package com.zkteco.zkbiosecurity.pers.client.service.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.business.sdk.request.pers.*;
import com.zkteco.business.sdk.response.PersResponse;
import com.zkteco.business.sdk.vo.ApiPersParamsItem;
import com.zkteco.business.sdk.vo.PersApiPositiontItem;
import com.zkteco.cloud.sdk.DefaultZKCloudClient;
import com.zkteco.cloud.sdk.ZKCouldClient;
import com.zkteco.cloud.sdk.bean.FileBean;
import com.zkteco.cloud.sdk.resp.BaseResponse;
import com.zkteco.cloud.sdk.resp.ListResponse;
import com.zkteco.cloud.sdk.resp.ObjectResponse;
import com.zkteco.cloud.sdk.util.ZKCloudApiFileUploadUtil;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseClientService;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import com.zkteco.zkbiosecurity.pers.client.utils.PersUploadPageUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPositionService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCloudItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPositionItem;
import com.zkteco.zkbiosecurity.system.app.service.BaseAuthCloudMessageSendService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.SystemPersInfo2CloudService;
import com.zkteco.zkbiosecurity.system.vo.SystemPers2ApiPositiontItem;
import com.zkteco.zkbiosecurity.system.vo.SystemPerson2CloudItem;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2019/6/20 16:07
 **/
@Service
@Slf4j
public class PersCloudServiceImpl implements SystemPersInfo2CloudService {

    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private BaseLicenseClientService baseLicenseClientService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BaseAuthCloudMessageSendService baseAuthCloudMessageSendService;
    @Autowired
    private PersPositionService persPositionService;
    @Autowired
    private PersPersonService persPersonService;

    // @Value("http://127.0.0.1:8899/router/rest")
    @Value("${cloud.server.upload.url:https://zkcloud.xmzkteco.com/BiosecurityApp/router/rest}")
    private String serverUrl;

    @Value("${cloud.server.file.url:https://zfs.zkbiosecurity.com}")
    private String cloudServerFileUrl;

    private static final String PERS_PERSON_PUSH_LAST_TIME = "pers.person.lastPushTime";

    @Override
    public void asyncDelPersonToCloud(List<String> pinList) {
        // 判断是否能够推送基础数据。
        if (baseAuthCloudMessageSendService.isAllowSendBasicData() && !CollectionUtil.isEmpty(pinList)) {
            // 异步发送数据，避免调用超时影响正常业务
            CompletableFuture.runAsync(() -> {
                // 进行分组，30条一组
                List<List<String>> pinsList = CollectionUtil.split(pinList, PersUploadPageUtil.PAGE_SIZE);
                ZKMessage zkMessage = new ZKMessage();
                zkMessage.setModuleCode("pers");
                zkMessage.setMessageId("persCloudHandleMessage#delPersonByPin");
                zkMessage.setAppId(baseLicenseService.getAppId());
                pinsList.forEach(pins -> {
                    zkMessage.setListContent(pins);
                    baseLicenseClientService.sendMessage(zkMessage);
                });
            });
        }
    }

    @Override
    public void asyncLeavePersonToCloud(List<String> pinList) {
        // 判断是否能够推送基础数据。
        if (baseAuthCloudMessageSendService.isAllowSendBasicData() && !CollectionUtil.isEmpty(pinList)) {
            // 异步发送数据，避免调用超时影响正常业务
            CompletableFuture.runAsync(() -> {
                // 进行分组，30条一组
                List<List<String>> pinsList = CollectionUtil.split(pinList, PersUploadPageUtil.PAGE_SIZE);
                ZKMessage zkMessage = new ZKMessage();
                zkMessage.setModuleCode("pers");
                zkMessage.setMessageId("persCloudHandleMessage#leavePersonByPin");
                zkMessage.setAppId(baseLicenseService.getAppId());
                pinsList.forEach(pins -> {
                    zkMessage.setListContent(pins);
                    baseLicenseClientService.sendMessage(zkMessage);
                });
            });
        }
    }

    @Override
    public Long getPersonLastPushTime() {
        String lastPushTime = baseSysParamService.getValByName(PERS_PERSON_PUSH_LAST_TIME);
        return StringUtils.isNotBlank(lastPushTime) ? Long.parseLong(lastPushTime) : null;
    }

    @Override
    public void setPersonLastPushTime() {
        baseSysParamService.saveValueByName(PERS_PERSON_PUSH_LAST_TIME, System.currentTimeMillis() + "");
    }

    @Override
    public ZKResultMsg syncAllPositionToCloud() {
        List<PersPositionItem> persPersonCloudItemList = persPositionService.getByCondition(new PersPositionItem());
        List<PersApiPositiontItem> persApiPositiontItemList = buildApiPosition(persPersonCloudItemList);
        List<SystemPers2ApiPositiontItem> pers2ApiPositiontItemList =
            ModelUtil.copyListProperties(persApiPositiontItemList, SystemPers2ApiPositiontItem.class);
        return sendPersPositionToCloud(pers2ApiPositiontItemList);
    }

    @Override
    public void notifyCloudSyncPersonData() {
        if (StringUtils.isNotBlank(baseLicenseService.getAppId())) {
            ZKMessage zkMessage = new ZKMessage();
            zkMessage.setModuleCode("pers");
            zkMessage.setMessageId("persCloudHandleMessage#getAllPersons");
            baseLicenseClientService.sendMessage(zkMessage);
        }
    }

    @Override
    public ZKResultMsg syncAllPersonToCloud() {
        if (baseAuthCloudMessageSendService.isAllowSendBasicData()) {
            Long personCount = persPersonService.getPersonCount();
            int pageNo = PersUploadPageUtil.getPage(personCount);
            // 分批推送消息，避免上传的内容太大占带宽。
            for (int i = 0; i < pageNo; i++) {
                // 全量获取人员信息并推送到云端
                List<PersPersonCloudItem> persPersonItemList =
                    persPersonService.getPersonCloudItems(new PersPersonCloudItem(), i, PersUploadPageUtil.PAGE_SIZE);
                List<SystemPerson2CloudItem> persPerson2CloudItemList =
                    ModelUtil.copyListProperties(persPersonItemList, SystemPerson2CloudItem.class);
                asyncPushPersonToCloud(persPerson2CloudItemList);
            }
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public void asyncPushPersonToCloud(List<SystemPerson2CloudItem> persPersonCloudItemList) {
        // 判断是否能够推送基础数据。
        if (baseAuthCloudMessageSendService.isAllowSendBasicData()
            && !CollectionUtil.isEmpty(persPersonCloudItemList)) {
            // 进行分组，30条一组
            List<List<SystemPerson2CloudItem>> persPersonCloudItemsList =
                CollectionUtil.split(persPersonCloudItemList, PersUploadPageUtil.PAGE_SIZE);
            ZKMessage zkMessage = new ZKMessage();
            zkMessage.setModuleCode("pers");
            zkMessage.setMessageId("persCloudHandleMessage#personMessageHandle");
            zkMessage.setAppId(baseLicenseService.getAppId());
            persPersonCloudItemsList.forEach(persPersonCloudItems -> {
                buildCloudPhoto(persPersonCloudItems);
                zkMessage.setListContent(persPersonCloudItems);
                baseLicenseClientService.sendMessage(zkMessage);
            });
        }
    }

    @Override
    public ZKResultMsg syncAllPositionFromCloud() {
        if (StringUtils.isBlank(baseLicenseService.getAppId())) {
            return ZKResultMsg.failMsg("auth_not_register_cloud");
        }
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        String token = baseAuthCloudMessageSendService.createToken();
        log.info("------ getPosition access_token ------- " + token);
        ZKCouldClient couldClient = getSendMsgClient();
        StringBuffer msg = new StringBuffer();
        ApiPersPositionGetListRequest apiPersPositionGetListRequest = new ApiPersPositionGetListRequest();
        apiPersPositionGetListRequest.setPersApiPositiontItem(new PersApiPositiontItem());
        try {
            ListResponse<PersApiPositiontItem> rsp = couldClient.execute(apiPersPositionGetListRequest, token);
            Integer ret = Integer.parseInt(rsp.getCode());
            log.info("getDepartment ------- result : " + rsp.getCode() + "----- msg :" + rsp.getMsg());
            if (ret == -27) {
                token = baseAuthCloudMessageSendService.refreshToken();
                rsp = couldClient.execute(apiPersPositionGetListRequest, token);
                ret = Integer.parseInt(rsp.getCode());
            }

            if (ret < 0) {
                msg.append("ret ：" + rsp.getCode() + ", msg: " + rsp.getMsg()).append("</br>");
            } else {
                log.info("response : " + rsp.getList());
                if (!CollectionUtil.isEmpty(rsp.getList())) {
                    // 更新保存处理云端部门数据
                    List<PersPositionItem> persPositionItemList =
                        ModelUtil.copyListProperties(rsp.getList(), PersPositionItem.class);
                    persPositionService.batchSaveItemFromCloud(persPositionItemList);
                }
            }
        } catch (Exception e) {
            log.error("getDepartment error !", e);
            msg.append(I18nUtil.i18nCode("common_failed") + "!").append("</br>");
        }
        if (StringUtils.isNotBlank(msg.toString())) {
            resultMsg = ZKResultMsg.failMsg(msg.toString());
        }
        return resultMsg;
    }

    @Override
    public ZKResultMsg sendPersPositionToCloud(List<SystemPers2ApiPositiontItem> persApiPositiontItems) {
        if (StringUtils.isBlank(baseLicenseService.getAppId())) {
            return ZKResultMsg.failMsg("auth_not_register_cloud");
        }
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        if (baseAuthCloudMessageSendService.isMasterApp() && !CollectionUtil.isEmpty(persApiPositiontItems)) {
            StringBuffer msg = new StringBuffer();
            String token = baseAuthCloudMessageSendService.createToken();
            log.info("------ upload access_token ------- " + token);
            ZKCouldClient couldClient = getSendMsgClient();
            List<List<SystemPers2ApiPositiontItem>> apiAuthDepartmentItemList =
                CollectionUtil.split(persApiPositiontItems, 100);
            for (List<SystemPers2ApiPositiontItem> items : apiAuthDepartmentItemList) {
                ApiPersPositionAddListRequest apiPersPositionAddListRequest = new ApiPersPositionAddListRequest();
                List<PersApiPositiontItem> persApiPositiontItemList =
                    ModelUtil.copyListProperties(items, PersApiPositiontItem.class);
                apiPersPositionAddListRequest.setPersApiPositiontItems(persApiPositiontItemList);
                try {
                    ObjectResponse<PersResponse> rsp = couldClient.execute(apiPersPositionAddListRequest, token);
                    Integer ret = Integer.parseInt(rsp.getCode());
                    log.info(
                        "sendPersPositionToCloud ------- result : " + rsp.getCode() + "----- msg :" + rsp.getMsg());
                    if (ret == -27) {
                        token = baseAuthCloudMessageSendService.refreshToken();
                        rsp = couldClient.execute(apiPersPositionAddListRequest, token);
                        ret = Integer.parseInt(rsp.getCode());
                    }

                    if (ret < 0) {
                        String codes = CollectionUtil.getPropertys(items, SystemPers2ApiPositiontItem::getCode);
                        msg.append(codes + ", " + "ret ：" + rsp.getCode() + ", msg: " + rsp.getMsg()).append("</br>");
                    }
                } catch (Exception e) {
                    log.error("sendPersPositionToCloud error !", e);
                    String codes = CollectionUtil.getPropertys(items, SystemPers2ApiPositiontItem::getCode);
                    msg.append(codes + ", " + I18nUtil.i18nCode("common_failed") + "!").append("</br>");
                }
            }
            if (StringUtils.isNotBlank(msg.toString())) {
                resultMsg = ZKResultMsg.failMsg(msg.toString());
            }
        }
        return resultMsg;
    }

    @Override
    public ZKResultMsg delPersPositionToCloud(List<String> positionCodeList) {
        if (StringUtils.isBlank(baseLicenseService.getAppId())) {
            return ZKResultMsg.failMsg("auth_not_register_cloud");
        }
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        String appRelation = baseSysParamService.getValByName(BaseConstants.ZKTECO_CLOUD_APP_RELATION);
        // 增加主从判断 0从应用，1主应用 部门数据由主应用维护
        if (baseAuthCloudMessageSendService.isMasterApp() && !CollectionUtil.isEmpty(positionCodeList)) {
            StringBuffer msg = new StringBuffer();
            String token = baseAuthCloudMessageSendService.createToken();
            log.info("------ upload access_token ------- " + token);
            ZKCouldClient couldClient = getSendMsgClient();
            List<List<String>> positionCodes = CollectionUtil.split(positionCodeList, 100);
            for (List<String> codes : positionCodes) {
                List<PersApiPositiontItem> items = new ArrayList<PersApiPositiontItem>();
                for (String code : codes) {
                    PersApiPositiontItem persApiPositiontItem = new PersApiPositiontItem();
                    persApiPositiontItem.setCode(code);
                    items.add(persApiPositiontItem);
                }
                ApiPersPositionDeleteRequest apiPersPositionDeleteRequest = new ApiPersPositionDeleteRequest();
                apiPersPositionDeleteRequest.setPersApiPositiontItems(items);
                try {
                    ObjectResponse<PersResponse> rsp = couldClient.execute(apiPersPositionDeleteRequest, token);
                    Integer ret = Integer.parseInt(rsp.getCode());
                    log.info("delPersPositionToCloud ------- result : " + rsp.getCode() + "----- msg :" + rsp.getMsg());
                    if (ret == -27) {
                        token = baseAuthCloudMessageSendService.refreshToken();
                        rsp = couldClient.execute(apiPersPositionDeleteRequest, token);
                        ret = Integer.parseInt(rsp.getCode());
                    }

                    if (ret < 0) {
                        String codesStr = CollectionUtil.getPropertys(items, PersApiPositiontItem::getCode);
                        msg.append(codes + ", " + "ret ：" + rsp.getCode() + ", msg: " + rsp.getMsg()).append("</br>");
                    }
                } catch (Exception e) {
                    log.error("delPersPositionToCloud error !", e);
                    String codesStr = CollectionUtil.getPropertys(items, PersApiPositiontItem::getCode);
                    msg.append(codes + ", " + I18nUtil.i18nCode("common_failed") + "!").append("</br>");
                }
            }
            if (StringUtils.isNotBlank(msg.toString())) {
                resultMsg = ZKResultMsg.failMsg(msg.toString());
            }
        }
        return resultMsg;
    }

    /**
     * 从二号项目迁移代码：组装职位数据
     * 
     * @auther zbx.zhong
     * @date 2019/7/4 11:24
     * @param persPositionItemList
     * @return
     */
    private List<PersApiPositiontItem> buildApiPosition(List<PersPositionItem> persPositionItemList) {
        List<PersApiPositiontItem> persApiPositiontItemList = new ArrayList<>();
        if (!CollectionUtil.isEmpty(persPositionItemList)) {
            List<PersPositionItem> rootPositionList = new ArrayList<>();
            List<String> positionCode =
                (List<String>)CollectionUtil.getPropertyList(persPositionItemList, PersPositionItem::getCode, "-1");
            // 过滤没有上级部门的部门信息，对子部门进行分组
            Map<String, List<PersPositionItem>> positionMap = persPositionItemList.stream().filter(item -> {
                if (StringUtils.isBlank(item.getParentCode()) || !positionCode.contains(item.getParentCode())) {
                    rootPositionList.add(item);
                    return false;
                }
                return true;
            }).collect(Collectors.groupingBy(PersPositionItem::getParentCode));
            persApiPositiontItemList = ModelUtil.copyListProperties(rootPositionList, PersApiPositiontItem.class);
            for (PersPositionItem positionItem : rootPositionList) {
                // 判断是否存在子部门
                if (positionMap.get(positionItem.getCode()) != null
                    && !positionMap.get(positionItem.getCode()).isEmpty()) {
                    // 组装子部门数据
                    persApiPositiontItemList.addAll(buildChildPositionTree(positionItem, positionMap));
                }
            }
        }
        return persApiPositiontItemList;
    }

    /**
     * 从二号项目迁移代码：遍历下级职位数据
     * 
     * @auther zbx.zhong
     * @date 2019/7/4 11:15
     * @param positionItem
     * @param positionMap
     * @return
     */
    private List<PersApiPositiontItem> buildChildPositionTree(PersPositionItem positionItem,
        Map<String, List<PersPositionItem>> positionMap) {
        List<PersPositionItem> childrenPositionList = positionMap.get(positionItem.getCode());
        List<PersApiPositiontItem> persApiPositiontItems = new ArrayList<>();
        if (CollectionUtil.isEmpty(childrenPositionList)) {
            return persApiPositiontItems;
        }
        for (PersPositionItem item : childrenPositionList) {
            List<PersApiPositiontItem> persApiPositiontItemList = new ArrayList<>();
            PersApiPositiontItem persApiPositiontItem = new PersApiPositiontItem();
            persApiPositiontItem.setCode(item.getCode());
            persApiPositiontItem.setName(item.getName());
            persApiPositiontItem.setParentCode(item.getParentCode());
            persApiPositiontItemList.add(persApiPositiontItem);
            // 判断是否存在子职位
            if (positionMap.get(item.getCode()) != null && !positionMap.get(item.getCode()).isEmpty()) {
                // 组装子职位数据
                persApiPositiontItemList.addAll(buildChildPositionTree(item, positionMap));
            }
            persApiPositiontItems.addAll(persApiPositiontItemList);
        }
        return persApiPositiontItems;
    }

    /**
     * 从二号项目迁移代码：创建发送客户端
     * 
     * @auther zbx.zhong
     * @date 2019/7/4 13:23
     * @return
     */
    public ZKCouldClient getSendMsgClient() {
        String appId = baseLicenseService.getAppId();
        String appKey = baseSysParamService.getValByName("zkteco:cloud:appkey");
        ZKCouldClient couldClient = new DefaultZKCloudClient(serverUrl, appId, appKey);
        return couldClient;
    }

    @Override
    public ZKResultMsg syncPersParamToCloud() {
        if (StringUtils.isBlank(this.baseLicenseService.getAppId())) {
            return ZKResultMsg.failMsg("auth_not_register_cloud");
        } else {
            if (baseAuthCloudMessageSendService.isMasterApp()) {
                return uploadPersParamToCloud(baseSysParamService.getParamsByModule("pers"));
            }
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public ZKResultMsg syncPersParamFromCloud() {
        if (StringUtils.isBlank(baseLicenseService.getAppId())) {
            return ZKResultMsg.failMsg("auth_not_register_cloud");
        } else {
            ZKResultMsg resultMsg = ZKResultMsg.successMsg();
            String token = baseAuthCloudMessageSendService.createToken();
            log.info("------ getPersParams access_token ------- " + token);
            ZKCouldClient couldClient = getSendMsgClient();
            StringBuffer msg = new StringBuffer();
            ApiPersParamsGetListRequest apiPersParamsGetListRequest = new ApiPersParamsGetListRequest();
            try {
                ObjectResponse<PersResponse> rsp = couldClient.execute(apiPersParamsGetListRequest, token);
                Integer ret = Integer.parseInt(rsp.getCode());
                log.info("getPersParams ------- result : " + rsp.getCode() + "----- msg :" + rsp.getMsg());
                if (ret == -27) {
                    token = baseAuthCloudMessageSendService.refreshToken();
                    rsp = couldClient.execute(apiPersParamsGetListRequest, token);
                    ret = Integer.parseInt(rsp.getCode());
                }
                if (ret < 0) {
                    msg.append("ret ：" + rsp.getCode() + ", msg: " + rsp.getMsg()).append("</br>");
                } else {
                    log.info("response : " + rsp.getBody());
                    if (StringUtils.isNotBlank(rsp.getBody())) {
                        Map<String, Object> body = (Map)JSONObject.parseObject(rsp.getBody());
                        JSONObject jsonObject = (JSONObject)body.get("data");
                        Map<String, String> params = new HashMap<>();
                        params.put(PersConstants.PERS_PARAM_PINLEN,
                            jsonObject.getString(PersConstants.CLOUD_PERS_PARAM_PINLEN));
                        params.put(PersConstants.PERS_PARAM_SUPPORT_LETTER,
                            jsonObject.getString(PersConstants.CLOUD_PARAM_SUPPORT_LETTER));
                        params.put(PersConstants.PERS_PARAM_SUPPORT_INCREMENT,
                            jsonObject.getString(PersConstants.CLOUD_PARAM_SUPPORT_INCREMENT));
                        baseSysParamService.saveParams(params);
                    }
                }
            } catch (Exception e) {
                log.error("getPersParams error !", e);
                msg.append("msg:").append(I18nUtil.i18nCode("pers_api_sync_paramFail"));
            }

            if (StringUtils.isNotBlank(msg.toString())) {
                resultMsg = ZKResultMsg.failMsg(msg.toString());
            }
            return resultMsg;
        }
    }

    private ZKResultMsg uploadPersParamToCloud(Map<String, String> params) {
        ZKResultMsg resultMsg = ZKResultMsg.successMsg();
        StringBuffer msg = new StringBuffer();
        String token = baseAuthCloudMessageSendService.createToken();
        log.info("------ upload access_token ------- " + token);
        ZKCouldClient couldClient = this.getSendMsgClient();
        try {
            ApiPersParamsItem apiPersParamsItem = new ApiPersParamsItem();
            apiPersParamsItem.setPinMaxLength(params.get(PersConstants.PERS_PARAM_PINLEN));
            apiPersParamsItem.setPinSupportLetter(params.get(PersConstants.PERS_PARAM_SUPPORT_LETTER));
            apiPersParamsItem.setPinSupportIncrement(params.get(PersConstants.PERS_PARAM_SUPPORT_INCREMENT));
            ApiPersParamsAddRequest apiPersParamsAddRequest = new ApiPersParamsAddRequest();
            apiPersParamsAddRequest.setApiPersParamsItem(apiPersParamsItem);
            ObjectResponse<PersResponse> rsp = couldClient.execute(apiPersParamsAddRequest, token);
            Integer ret = Integer.parseInt(rsp.getCode());
            log.info("uploadPersParams ------- result : " + rsp.getCode() + "----- msg :" + rsp.getMsg());
            if (ret == -27) {
                token = baseAuthCloudMessageSendService.refreshToken();
                rsp = couldClient.execute(apiPersParamsAddRequest, token);
                ret = Integer.parseInt(rsp.getCode());
            }
            if (ret < 0) {
                msg.append("ret ：").append(rsp.getCode()).append(", msg: ").append(rsp.getMsg()).append("</br>");
            } else {
                if (StringUtils.isNotEmpty(rsp.getBody())) {
                    resultMsg.setData(rsp.getBody());
                } else {
                    msg.append("msg:").append(I18nUtil.i18nCode("pers_api_upload_paramFail"));
                }
            }
        } catch (Exception e) {
            log.error("-----upload pers params error---", e);
            msg.append(I18nUtil.i18nCode("pers_api_upload_paramFail")).append("!").append("</br>");
        }

        if (StringUtils.isNotBlank(msg.toString())) {
            resultMsg = ZKResultMsg.failMsg(msg.toString());
        }
        return resultMsg;
    }

    private String buildBioPhoto(String photoPath) {
        if (StringUtils.isNoneBlank(photoPath)) {

            String base64Photo = FileEncryptUtil.getDecryptFileBase64(photoPath);
            return base64Photo.replaceAll("\r\n", "").replaceAll("\t", "");
        }
        return "";
    }

    private ZKResultMsg upload(String photoPath) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String base64Image = buildBioPhoto(photoPath);
        try {
            FileBean fileBean = new FileBean();
            fileBean.setModuleName(ConstUtil.SYSTEM_MODULE_PERS);
            fileBean.setAppId(baseLicenseClientService.getAppid());
            fileBean.setAppKey(baseLicenseClientService.getAppKey());
            fileBean.setBase64Image(base64Image);
            fileBean.setCloudServerFileUrl(cloudServerFileUrl);

            BaseResponse response = ZKCloudApiFileUploadUtil.upload(fileBean);
            zkResultMsg = JSON.parseObject(response.getBody(), ZKResultMsg.class);
        } catch (Exception e) {
            zkResultMsg.setData("upload fail");
            zkResultMsg.setRet("fail");
        }
        return zkResultMsg;
    }

    /**
     * 封装云上所需的头像照片信息
     *
     * @param items:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonCloudItem>
     * <AUTHOR>
     * @throws
     * @date 2022-10-14 14:22
     * @since 1.0.0
     */
    private List<SystemPerson2CloudItem> buildCloudPhoto(List<SystemPerson2CloudItem> items) {
        if (!items.isEmpty()) {
            items.forEach(personCloudItem -> {
                // 头像
                if (StringUtils.isNotBlank(personCloudItem.getPhotoPath())) {
                    ZKResultMsg zkResultMsg = upload(personCloudItem.getPhotoPath());
                    if (Objects.nonNull(zkResultMsg.getData())) {
                        personCloudItem.setPhotoPath(zkResultMsg.getData().toString());
                    }
                }
            });
        }
        return items;
    }
}
