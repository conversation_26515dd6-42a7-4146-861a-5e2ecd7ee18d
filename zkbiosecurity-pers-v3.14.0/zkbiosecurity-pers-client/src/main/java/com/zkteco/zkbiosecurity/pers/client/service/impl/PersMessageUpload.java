package com.zkteco.zkbiosecurity.pers.client.service.impl;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.cloud.sdk.ApiException;
import com.zkteco.cloud.sdk.bean.FileBean;
import com.zkteco.cloud.sdk.resp.BaseResponse;
import com.zkteco.cloud.sdk.util.ZKCloudApiFileUploadUtil;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseClientService;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.message.bean.ZKMessage;
import com.zkteco.zkbiosecurity.pers.client.utils.PersUploadPageUtil;
import com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPositionService;
import com.zkteco.zkbiosecurity.pers.service.PersTempPersonService;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.system.app.service.BaseAuthCloudMessageSendService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.SystemPersInfo2CloudService;

@Component
public class PersMessageUpload {

    private final static Logger logger = LoggerFactory.getLogger(PersMessageUpload.class);

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersTempPersonService persTempPersonService;
    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private BaseLicenseClientService baseLicenseClientService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BaseAuthCloudMessageSendService baseAuthCloudMessageSendService;

    @Autowired
    private PersLeavePersonService persLeavePersonService;

    @Autowired
    private PersPositionService persPositionService;
    @Autowired
    private SystemPersInfo2CloudService pers2CloudService;
    @Value("${cloud.server.file.url:https://zfs.zkbiosecurity.com}")
    private String cloudServerFileUrl;

    /** 每隔一分钟，业务可以自己控制 */
    // @Scheduled(cron = "0 0 0/1 * * ?")
    @Async
    @Scheduled(cron = "0 0/5 * * * ?")
    public ZKResultMsg persMessageUpload() {
        // 判断是否已经推送基础数据
        boolean isUploadBasic = baseSysParamService.getAlreadyInitModule("BasicData");
        // 判断是否注册了云服务，注册才发送。
        if (StringUtils.isNotBlank(baseLicenseService.getAppId()) && isUploadBasic) {
            Long pushTime = pers2CloudService.getPersonLastPushTime();
            // 修复同步人员多时上传图片比较慢 这次还没推完 下次时间已经到了 又把上次的数据查询出来重新推了
            pers2CloudService.setPersonLastPushTime();
            ZKMessage zkMessage = new ZKMessage();
            // 平台分发处理 必须设置模块码和下列形式的消息id
            zkMessage.setModuleCode("pers");
            // 发送消息到云平台。
            zkMessage.setMessageId("persCloudHandleMessage#personMessageHandle");
            // 设置appId
            zkMessage.setAppId(baseLicenseService.getAppId());
            if (pushTime == null) {
                // 第一次全量更新部门数据
                baseAuthCloudMessageSendService.syncAllDepartmentToCloud();
                Long personCount = persPersonService.getPersonCount();
                int pageNo = PersUploadPageUtil.getPage(personCount);
                // 分批推送消息，避免上传的内容太大占带宽。
                for (int i = 0; i < pageNo; i++) {
                    // 全量获取人员信息并推送到云端
                    List<PersPersonCloudItem> persPersonItemList = persPersonService
                        .getPersonCloudItems(new PersPersonCloudItem(), i, PersUploadPageUtil.PAGE_SIZE);
                    buildCloudPhoto(persPersonItemList, null);
                    zkMessage.setListContent(persPersonItemList);

                    // kafka异步发送
                    baseLicenseClientService.sendMessage(zkMessage);
                }
            } else {
                Date lastUpdate = new Date(pushTime);
                lastUpdate = DateUtil.addMinute(lastUpdate, -1);
                // 将 Date 转换为 Instant
                Instant lastUpdateInstant = lastUpdate.toInstant();
                List<PersPersonCloudItem> persPersonCloudItemList = persPersonService.getUpdatePersonItem(lastUpdate);
                if (persPersonCloudItemList != null && !persPersonCloudItemList.isEmpty()) {
                    List<List<PersPersonCloudItem>> persCloudPersons =
                        CollectionUtil.split(persPersonCloudItemList, PersUploadPageUtil.PAGE_SIZE);
                    for (List<PersPersonCloudItem> persPersonCloudItems : persCloudPersons) {
                        buildCloudPhoto(persPersonCloudItems, lastUpdateInstant);
                        zkMessage.setListContent(persPersonCloudItems);
                        // kafka异步发送
                        baseLicenseClientService.sendMessage(zkMessage);
                    }
                }
            }
            pers2CloudService.setPersonLastPushTime();
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * @Description: 处理云服务传过来的人员数据
     * <AUTHOR>
     * @date 2018/9/7 14:11
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    public ZKResultMsg issuePersonMessage(ZKMessage zkMessage) {
        List<String> personMessageList = zkMessage.getListContent();
        if (personMessageList != null && personMessageList.size() > 0) {
            String appId = baseSysParamService.getValByName("zkteco:cloud:appid");
            String appKey = baseSysParamService.getValByName("zkteco:cloud:appkey");
            List<PersTempPersonItem> persTempPersonItems =
                JSONArray.parseArray(personMessageList.toString(), PersTempPersonItem.class);
            persTempPersonItems.forEach(persTempPersonItem -> {
                try {
                    if (StringUtils.isNotBlank(persTempPersonItem.getPhotoPath())
                        && !persTempPersonItem.getPhotoPath().startsWith("http")) {
                        // 云端照片加密，根据文件id获取照片base64
                        FileBean fileBean = new FileBean();
                        fileBean.setFileId(persTempPersonItem.getPhotoPath());
                        fileBean.setAppId(appId);
                        fileBean.setAppKey(appKey);
                        fileBean.setCloudServerFileUrl(cloudServerFileUrl);
                        String imgBase64 = ZKCloudApiFileUploadUtil.getDecryptFileBase64(fileBean).getBody();
                        persTempPersonItem.setCloudImgBase64(imgBase64);
                    }
                    if (StringUtils.isNotBlank(persTempPersonItem.getCropPhotoPath())
                        && !persTempPersonItem.getCropPhotoPath().startsWith("http")) {
                        // 云端抠图照片加密，根据文件id获取照片base64
                        FileBean fileBean = new FileBean();
                        fileBean.setFileId(persTempPersonItem.getCropPhotoPath());
                        fileBean.setAppId(appId);
                        fileBean.setAppKey(appKey);
                        fileBean.setCloudServerFileUrl(cloudServerFileUrl);
                        String imgBase64 = ZKCloudApiFileUploadUtil.getDecryptFileBase64(fileBean).getBody();
                        persTempPersonItem.setCloudCropImgBase64(imgBase64);
                    }
                    persTempPersonService.savePersonRegistrar(persTempPersonItem);
                } catch (ApiException e) {
                    logger.error("==== PersMessageUpload issuePersonMessage() get photo form cloud fail： photoPath="
                        + persTempPersonItem.getPhotoPath(), e);
                } catch (Exception e) {
                    logger.error("persCloud issuePersonMessage error: ", e);
                }
            });
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 处理云上下发5000修改人员自助密码
     * 
     * @auther lambert.li
     * @date 2019/1/4 17:16
     * @param zkMessage
     * @return
     */
    public ZKResultMsg updatePersonPassword(ZKMessage zkMessage) {
        Map<String, Object> dataMap = zkMessage.getContent();
        if (dataMap != null && dataMap.size() > 0) {
            String pin = dataMap.containsKey("pin") ? (String)dataMap.get("pin") : null;
            String password = dataMap.containsKey("password") ? (String)dataMap.get("password") : null;
            if (StringUtils.isNotBlank(pin) && StringUtils.isNotBlank(password)) {
                PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
                if (persPersonItem != null) {
                    persPersonService.updateSelfPwd(persPersonItem.getId(), password);
                }
            }
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 处理云服务传过来的人员数据
     * 
     * @auther lambert.li
     * @date 2019/6/21 15:45
     * @param zkMessage
     * @return
     */
    public ZKResultMsg syncPersonFromCloud(ZKMessage zkMessage) {
        List<String> personMessageList = zkMessage.getListContent();
        if (personMessageList != null && personMessageList.size() > 0) {
            List<PersTempPersonItem> persTempPersonItems =
                JSONArray.parseArray(personMessageList.toString(), PersTempPersonItem.class);
            String appId = baseSysParamService.getValByName("zkteco:cloud:appid");
            String appKey = baseSysParamService.getValByName("zkteco:cloud:appkey");
            persTempPersonItems.forEach(persTempPersonItem -> {
                try {
                    if (StringUtils.isNotBlank(persTempPersonItem.getPhotoPath())
                        && !persTempPersonItem.getPhotoPath().startsWith("http")) {
                        // 云端照片加密，根据文件id获取照片base64
                        FileBean fileBean = new FileBean();
                        fileBean.setFileId(persTempPersonItem.getPhotoPath());
                        fileBean.setAppId(appId);
                        fileBean.setAppKey(appKey);
                        fileBean.setCloudServerFileUrl(cloudServerFileUrl);
                        String imgBase64 = ZKCloudApiFileUploadUtil.getDecryptFileBase64(fileBean).getBody();
                        JSONObject json = JSONObject.parseObject(imgBase64);
                        if (json != null) {
                            imgBase64 = json.getString("data");
                            if (StringUtils.isNotBlank(imgBase64)) {
                                String[] img = imgBase64.split(",");
                                imgBase64 = img[img.length - 1];
                            }
                        }
                        persTempPersonItem.setCloudImgBase64(imgBase64);
                    }
                    if (StringUtils.isNotBlank(persTempPersonItem.getCropPhotoPath())
                        && !persTempPersonItem.getCropPhotoPath().startsWith("http")) {
                        // 云端抠图照片加密，根据文件id获取照片base64
                        FileBean fileBean = new FileBean();
                        fileBean.setFileId(persTempPersonItem.getCropPhotoPath());
                        fileBean.setAppId(appId);
                        fileBean.setAppKey(appKey);
                        fileBean.setCloudServerFileUrl(cloudServerFileUrl);
                        String imgBase64 = ZKCloudApiFileUploadUtil.getDecryptFileBase64(fileBean).getBody();
                        JSONObject json = JSONObject.parseObject(imgBase64);
                        if (json != null) {
                            imgBase64 = json.getString("data");
                            if (StringUtils.isNotBlank(imgBase64)) {
                                String[] img = imgBase64.split(",");
                                imgBase64 = img[img.length - 1];
                            }
                        }
                        persTempPersonItem.setCloudCropImgBase64(imgBase64);
                    }
                } catch (ApiException e) {
                    logger.error("==== PersMessageUpload syncPersonFromCloud() get photo form cloud fail： photoPath="
                        + persTempPersonItem.getPhotoPath(), e);
                }
            });
            persTempPersonService.syncPersonFromCloud(persTempPersonItems);
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 处理云上下发删除人员
     * 
     * <AUTHOR>
     * @Date 2019/6/13 11:11
     * @param zkMessage
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    public ZKResultMsg delPersonByPin(ZKMessage zkMessage) {
        List<String> pinList = zkMessage.getListContent();
        if (pinList != null && pinList.size() > 0) {
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(pinList);
            String persIds = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getId);
            if (!"-1".equals(persIds)) {
                persPersonService.deleteByIds(persIds);
            }
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 处理云上下发离职人员
     * 
     * <AUTHOR>
     * @Date 2019/8/8 18:15
     * @param zkMessage
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    public ZKResultMsg leavePersonByPin(ZKMessage zkMessage) {
        List<String> pinList = zkMessage.getListContent();
        if (pinList != null && pinList.size() > 0) {
            List<PersPersonItem> persPersonItemList = persPersonService.getItemsByPins(pinList);
            String persIds = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getId);
            PersLeavePersonItem persLeavePersonItem = new PersLeavePersonItem();
            // 设置离职时间为当前时间
            persLeavePersonItem.setLeaveDate(new Date());
            // 设置默认自离
            persLeavePersonItem.setLeaveType(1);
            if (!"-1".equals(persIds)) {
                persLeavePersonService.batchLeave(persLeavePersonItem, persIds);
            }
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 处理云上下发删除职位
     * 
     * <AUTHOR>
     * @Date 2019/6/13 11:11
     * @param zkMessage
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    public ZKResultMsg delPersPositionByCode(ZKMessage zkMessage) {
        List<String> codeList = zkMessage.getListContent();
        if (codeList != null && codeList.size() > 0) {
            List<PersPositionItem> persPositionItemList = persPositionService.getItemsByCodes(codeList);
            String positionIds = CollectionUtil.getPropertys(persPositionItemList, PersPositionItem::getId);
            if (!"-1".equals(positionIds)) {
                persPositionService.deleteByIds(positionIds);
            }
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 处理云上下发的职位信息
     * 
     * @auther xjing.huang
     * @date 2019/6/14 17:16
     * @param zkMessage
     * @return
     */
    public ZKResultMsg updatePersPosition(ZKMessage zkMessage) {
        List<String> positionList = zkMessage.getListContent();
        if (positionList != null && positionList.size() > 0) {
            List<PersPositionItem> persPositionItems =
                JSONArray.parseArray(positionList.toString(), PersPositionItem.class);
            persPositionService.updatePersPositionItem(persPositionItems);
        }
        return ZKResultMsg.successMsg();
    }

    public String buildBioPhoto(String photoPath) {
        if (StringUtils.isNoneBlank(photoPath)) {

            String base64Photo = FileEncryptUtil.getDecryptFileBase64(photoPath);
            return base64Photo.replaceAll("\r\n", "").replaceAll("\t", "");
        }
        return "";
    }

    private ZKResultMsg upload(String photoPath) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String base64Image = buildBioPhoto(photoPath);
        try {
            FileBean fileBean = new FileBean();
            fileBean.setModuleName(ConstUtil.SYSTEM_MODULE_PERS);
            fileBean.setAppId(baseLicenseClientService.getAppid());
            fileBean.setAppKey(baseLicenseClientService.getAppKey());
            fileBean.setBase64Image(base64Image);
            fileBean.setCloudServerFileUrl(cloudServerFileUrl);

            BaseResponse response = ZKCloudApiFileUploadUtil.upload(fileBean);
            zkResultMsg = JSON.parseObject(response.getBody(), ZKResultMsg.class);
        } catch (Exception e) {
            zkResultMsg.setData("upload fail");
            zkResultMsg.setRet("fail");
        }
        return zkResultMsg;
    }

    /**
     * 封装云上所需的头像照片信息
     *
     * @param items:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonCloudItem>
     * <AUTHOR>
     * @throws
     * @date 2022-10-14 14:22
     * @since 1.0.0
     */
    private List<PersPersonCloudItem> buildCloudPhoto(List<PersPersonCloudItem> items, Instant lastUpdate) {
        if (!items.isEmpty()) {
            items.forEach(personCloudItem -> {
                boolean updatePhoto = true;
                // 头像
                if (StringUtils.isNotBlank(personCloudItem.getPhotoPath())) {
                    if (lastUpdate != null) {
                        updatePhoto = isUpdatePhoto(personCloudItem.getPhotoPath(), lastUpdate);
                    }
                    if (updatePhoto) {
                        ZKResultMsg zkResultMsg = upload(personCloudItem.getPhotoPath());
                        if ((!"fail".equals(zkResultMsg.getRet())) && Objects.nonNull(zkResultMsg.getData())) {
                            personCloudItem.setPhotoPath(zkResultMsg.getData().toString());
                            personCloudItem.setUpdateCloudPhoto(true);
                        }
                    }
                }
            });
        }
        return items;
    }

    /**
     * 判断是否更新云端照片
     * 
     * @param photoPath:
     * @param lastUpdate:
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2024-09-24 11:17
     * @since 1.0.0
     */
    private boolean isUpdatePhoto(String photoPath, Instant lastUpdate) {
        boolean updatePhoto = false;
        try {
            Path path = Paths.get(FileUtils.getLocalFullPath(photoPath));
            Instant lastModifiedTime = Files.getLastModifiedTime(path).toInstant();
            if (lastModifiedTime.isAfter(lastUpdate)) {
                updatePhoto = true;
            }
        } catch (IOException e) {
            logger.error("", e);
        }
        return updatePhoto;
    }
}
