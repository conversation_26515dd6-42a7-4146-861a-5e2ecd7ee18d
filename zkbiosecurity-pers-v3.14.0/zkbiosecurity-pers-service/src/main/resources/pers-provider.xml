<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd ">
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersAttributeExtService" ref="persAttributeExtServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersAttributeService" ref="persAttributeServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersBioTemplateService" ref="persBioTemplateServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersCardPrintTemplateService" ref="persCardPrintTemplateServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersCardService" ref="persCardServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersCertificateService" ref="persCertificateServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersIdentityCardInfoService" ref="persIdentityCardInfoServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersIssueCardService" ref="persIssueCardServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersLeavePersonService" ref="persLeavePersonServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersParamsService" ref="persParamsServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersPersonChangeService" ref="persPersonChangeServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersPersonService" ref="persPersonServiceImpl" timeout="10000" version="1.0.0"/>
    <dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersPositionService" ref="persPositionServiceImpl" timeout="10000" version="1.0.0"/>
	<dubbo:service interface="com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService" ref="persWiegandFmtServiceImpl" timeout="10000" version="1.0.0"/>
</beans>