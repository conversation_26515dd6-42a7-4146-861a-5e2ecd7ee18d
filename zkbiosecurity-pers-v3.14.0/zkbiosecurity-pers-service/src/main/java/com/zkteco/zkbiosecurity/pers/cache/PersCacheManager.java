package com.zkteco.zkbiosecurity.pers.cache;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 14:14 2018/7/31
 */
@Component
public class PersCacheManager {
    private final static Logger logger = LoggerFactory.getLogger(PersCacheManager.class);
    @Autowired
    private RedisTemplate redisTemplate;
    public final static String PERS_PIN_AUTO_INC = "pers:pinautoinc";
    public final static String PERS_PIN_AUTO = "pers:pinauto";// 为了防止老数据产生影响使用新key
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private BaseSysParamService baseSysParamService;
    public static final String PERS_TEMPLATE_SERVER_TOKEN = "pers:templateServer:token";

    /**
     * 保存新增进去的pin
     * 
     * @param pin
     */
    public void savePersonPin(String pin) {
        List<String> list = Optional.ofNullable((List<String>)redisTemplate.opsForList().leftPop(PERS_PIN_AUTO_INC))
            .orElse(new ArrayList<>());
        if (StringUtils.hasLength(pin) && !list.contains(pin) && pin.length() < 19) {
            list.add(pin);
        }
        redisTemplate.opsForList().leftPush(PERS_PIN_AUTO_INC, list);
    }

    public void savePersonPin(List<String> pins) {
        List<String> list = Optional.ofNullable((List<String>)redisTemplate.opsForList().leftPop(PERS_PIN_AUTO_INC))
            .orElse(new ArrayList<>());
        for (String pin : pins) {
            if (StringUtils.hasLength(pin) && !list.contains(pin) && pin.length() < 19) {
                list.add(pin);
            }
        }
        redisTemplate.opsForList().leftPush(PERS_PIN_AUTO_INC, list);
    }

    /**
     * 初始化人员编号到redis
     */
    public void initPersonPin() {
        try {
            if (redisTemplate.opsForList().size(PERS_PIN_AUTO_INC) > 0) {
                List<String> list = (List<String>)redisTemplate.opsForList().leftPop(PERS_PIN_AUTO_INC);
                if (persPersonDao.count() > 0) {
                    List<String> pinList = persPersonDao.getPinByPinLetter(false);
                    list = listSort(pinList);
                }
                redisTemplate.opsForList().leftPush(PERS_PIN_AUTO_INC, list);
            }
        } catch (Exception e) {
            logger.error("init person pin error");
        }
    }

    /**
     * 排序
     * 
     * @param list
     * @return
     */
    private List<String> listSort(List<String> list) {
        return list.stream().sorted((v1, v2) -> {
            Long num1 = Long.parseLong(v1);
            Long num2 = Long.parseLong(v2);
            if (num1 > num2) {
                return 1;
            } else if (num1 < num2) {
                return -1;
            } else {
                return 0;
            }
        }).collect(Collectors.toList());
    }

    /**
     * 获取下一个自增pin值
     *
     * @param
     * @return int
     * <AUTHOR>
     * @date 2018/7/6 9:52
     */
    public Long getIncPoint() {
        Long incPoint = 1L;
        try {
            List<String> list = (List<String>)redisTemplate.opsForList().leftPop(PERS_PIN_AUTO_INC);
            redisTemplate.opsForList().leftPush(PERS_PIN_AUTO_INC, list);
            if (CollectionUtil.isEmpty(list)) {
                return incPoint;
            }
            list = listSort(list);
            if (list.size() > 1) {
                for (int i = 0; i < list.size() - 1; i++) {
                    if (Long.parseLong(list.get(i + 1)) - Long.parseLong(list.get(i)) > 1) {
                        incPoint = Long.parseLong(list.get(i)) + 1;
                        break;
                    }
                }
            } else {
                incPoint = Long.parseLong(list.get(0)) + 1;
            }
            if (incPoint == 1) {
                incPoint = Long.parseLong(list.get(list.size() - 1)) + 1;
            }
        } catch (Exception e) {
            logger.info("More than the largest self increasing pool, please manually fill in!");
        }
        return incPoint;
    }

    public void setAutoPin(String pin) {
        // 新增pin号数字判断，只有是数字类型的pin号放入才有意义 modified by max 20190916
        if (org.apache.commons.lang3.StringUtils.isNumeric(pin)) {
            redisTemplate.opsForValue().set(PERS_PIN_AUTO, pin);
        }
    }

    public String getAutoPin() {
        if (redisTemplate.hasKey(PERS_PIN_AUTO)) {
            Integer sysPinLength = Integer.parseInt(baseSysParamService.getValByName("pers.pinLen"));
            String tempPin = redisTemplate.opsForValue().get(PERS_PIN_AUTO).toString();
            // 是数字类型提取数据才有意义 modified by max 20190916
            if (org.apache.commons.lang3.StringUtils.isNumeric(tempPin)) {
                String autoPin = new BigInteger(redisTemplate.opsForValue().get(PERS_PIN_AUTO).toString())
                    .add(new BigInteger("1")).toString();
                if (autoPin.length() <= sysPinLength) {
                    return autoPin;
                }
            }
        }
        return null;
    }

    public void setTemplateServerTokenToRedis(String token) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(token)) {
            redisTemplate.opsForValue().set(PERS_TEMPLATE_SERVER_TOKEN, token, 3L, TimeUnit.MINUTES);
        }
    }

    public String getTemplateServerToken() {
        if (redisTemplate.hasKey(PERS_TEMPLATE_SERVER_TOKEN)) {
            return redisTemplate.opsForValue().get(PERS_TEMPLATE_SERVER_TOKEN).toString();
        }
        return null;
    }

    public void delTemplateServerToken() {
        redisTemplate.delete(PERS_TEMPLATE_SERVER_TOKEN);
    }
}
