/**
 * File Name: Pers<PERSON>eave<PERSON>erson
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 对应百傲瑞达实体 PersLeavePerson
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_LEAVEPERSON", indexes = {
        @Index(name = "PERS_LEAVEPERSON_PIN_IDX", columnList = "PIN"),
        @Index(name = "PERS_LEAVEPERSON_NAME_IDX", columnList = "NAME"),
        @Index(name = "PERS_LEAVEPERSON_DEPT_ID_IDX", columnList = "AUTH_DEPT_ID")})
@Getter
@Setter
@Accessors(chain = true)
public class PersLeave<PERSON>erson extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 部门名字
     */
    @Column(name = "AUTH_DEPT_NAME", length = 100, nullable = false)
    private String deptName;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50, nullable = false)
    private String deptId;

    /**
     * 部门编号
     */
    @Column(name = "AUTH_DEPT_CODE", length = 30, nullable = false)
    private String deptCode;

    /**
     * 人员编号
     */
    @Column(name = "PIN", length = 30, nullable = false)
    private String pin;

    /**
     * 离职人员名字
     */
    @Column(name = "NAME", length = 50)
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "LAST_NAME", length = 50)
    private String lastName;

    /**
     * 离职日期
     */
    @Column(name = "LEAVE_DATE", nullable = false)
    @Temporal(TemporalType.DATE)
    private Date leaveDate;

    /**
     * 离职原因
     */
    @Column(name = "LEAVE_REASON", length = 200)
    private String leaveReason;

    /**
     * 离职类型id
     */
    @Column(name = "LEAVETYPE_ID", nullable = false)
    private Integer leaveType;

    /**
     * 入职时间
     */
    @Column(name = "HIRE_DATE")
    @Temporal(TemporalType.DATE)
    private Date hireDate;

    /**
     * 是否考勤
     */
    @Column(name = "IS_ATTENDANCE")
    private Boolean isAttendance;

    /**
     * 分组id
     */
    @Column(name = "GROUP_ID")
    private String attGroupId;
}