/**
 * File Name: PersIssueCard Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 PersIssueCard
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_ISSUECARD")
@Getter
@Setter
@Accessors(chain = true)
public class PersIssueCard extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 人员编号
     */
    @Column(name = "PIN", length = 30)
    private String pin;

    /**
     * 名字
     */
    @Column(name = "NAME", length = 50)
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "LAST_NAME", length = 50)
    private String lastName;

    /**
     * 卡号
     */
    @Column(name = "CARD_NO", length = 250, nullable = false)
    @Convert(converter = EncryptConverter.class)
    private String cardNo;

    /**
     * 操作类型
     */
    @Column(name = "OPERATE_TYPE", nullable = false)
    private Short operateType;
}