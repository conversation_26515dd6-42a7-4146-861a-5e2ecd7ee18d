/**
 * File Name: PersPersonLink
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersPersonLink;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 对应百傲瑞达 PersPersonLinkDao
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersPersonLinkDao extends BaseDao<PersPersonLink, String> {
    /**
     * 根据人员ID删除
     * @param personId
     */
    void deleteByPersonId(String personId);

    /**
     * 查询是否存在
     * @param personId
     * @param linkId
     * @param type
     * @return
     */
    PersPersonLink findByPersonIdAndLinkIdAndType(String personId, String linkId, String type);

    /**
     * 查询权限组的人员
     * @param personIds
     * @param linkId
     * @param type
     * @return
     */
    List<PersPersonLink> findByPersonIdInAndLinkIdAndType(List<String> personIds, String linkId, String type);

    /**
     * 批量删除
     * @author: mingfa.zheng
     * @date: 2018/4/23 15:55
     * @return:
     */
    @Modifying
    @Query(value = "delete from PersPersonLink e where e.type=?1 and e.linkId in(?2) and e.personId in (?3)")
    void deleteBatchItem(String linkTypeEnum, List<String> linkIds, List<String> personIds);

    /**
     * 根据linkId批量删除
     * @author: mingfa.zheng
     * @date: 2018/5/10 15:26
     * @return:
     */
    @Modifying
    @Query(value = "delete from PersPersonLink e where e.type=?1 and e.linkId in(?2)")
    void deleteBatchItemByLinkId(String linkTypeEnum, List<String> linkIds);
}