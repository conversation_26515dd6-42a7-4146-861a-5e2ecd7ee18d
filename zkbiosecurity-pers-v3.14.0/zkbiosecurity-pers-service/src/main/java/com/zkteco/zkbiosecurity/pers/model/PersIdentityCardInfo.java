/**
 * File Name: PersIdentityCardInfo Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 PersIdentityCardInfo
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_IDENTITY_CARD_INFO",
    indexes = {@Index(name = "PERS_IDENTITY_CARD_IDCARD_IDX", columnList = "ID_CARD"),
        @Index(name = "PERS_PHYSICAL_NO_IDX", columnList = "PHYSICAL_NO")})
@Getter
@Setter
@Accessors(chain = true)
public class PersIdentityCardInfo extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @Column(name = "NAME", length = 20)
    private String name;

    /**
     * 性别
     */
    @Column(name = "GENDER", length = 10)
    private String gender;

    /**
     * 生日
     */
    @Column(name = "BIRTHDAY")
    @Temporal(TemporalType.DATE)
    private Date birthday;

    /**
     * 身份证号码
     */
    @Column(name = "ID_CARD", length = 255)
    @Convert(converter = EncryptConverter.class)
    private String idCard;

    /**
     * 物理卡号
     */
    @Column(name = "PHYSICAL_NO", length = 255)
    @Convert(converter = EncryptConverter.class)
    private String physicalNo;

    /**
     * 签发机关
     */
    @Column(name = "ISSUED_ORGAN", length = 255)
    private String issuedOrgan;

    /**
     * 有效开始时间
     */
    @Column(name = "START_DATE")
    @Temporal(TemporalType.DATE)
    private Date startDate;

    /**
     * 有效截止时间
     */
    @Column(name = "END_DATE")
    @Temporal(TemporalType.DATE)
    private Date endDate;

    /**
     * 民族
     */
    @Column(name = "NATION", length = 255)
    private String nation;

    /**
     * 地址
     */
    @Column(name = "ADDRESS", length = 255)
    @Convert(converter = EncryptConverter.class)
    private String address;

    /**
     * 照片
     */
    @Column(name = "PHOTO_PATH", length = 200)
    private String photoPath;

    /**
     * 指纹1
     */
    @Column(name = "TEMPLATE1", length = 3000)
    private String template1;

    /**
     * 指纹2
     */
    @Column(name = "TEMPLATE2", length = 3000)
    private String template2;
}