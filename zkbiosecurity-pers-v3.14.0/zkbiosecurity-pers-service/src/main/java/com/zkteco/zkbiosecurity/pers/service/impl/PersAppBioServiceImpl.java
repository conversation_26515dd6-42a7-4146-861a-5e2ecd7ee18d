package com.zkteco.zkbiosecurity.pers.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.acc.vo.Acc4PersonLevelItem;
import com.zkteco.zkbiosecurity.auth.service.Auth4PersService;
import com.zkteco.zkbiosecurity.auth.service.AuthForgotPwdService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthUserItem;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.utils.DateUtil.DateStyle;
import com.zkteco.zkbiosecurity.pers.api.vo.AppPersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPersonItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.vo.PersCertificateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersTempPersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseCropFaceService;
import com.zkteco.zkbiosecurity.system.service.BaseSendMailService;

@Service
public class PersAppBioServiceImpl implements PersAppBioService {

    private Logger logger = LoggerFactory.getLogger(PersAppBioServiceImpl.class);
    @Autowired
    private PersTempPersonService persTempPersonService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private PersApiPersonService persApiPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private PersCertificateService persCertificateService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RedisTemplate<String, AuthUserItem> redisTemplate;
    @Autowired
    private BaseSendMailService baseSendMailService;
    @Value("${security.require-ssl:false}")
    private String httpsEnable;
    @Value("${system.productCode}")
    private String sysName;
    @Autowired
    private AuthForgotPwdService forgetPwdService;
    @Autowired
    private BaseCropFaceService baseCropFaceService;
    @Autowired
    private Auth4PersService auth4PersService;

    @Override
    public ZKResultMsg savePersonRegister(AppPersonItem appPersonItem) {
        // 验证数据人员数据是否存在
        ZKResultMsg resultMsg =
            vaildPersonItem(appPersonItem.getMobile(), appPersonItem.getPin(), appPersonItem.getEmail());
        if ("ok".equals(resultMsg.getRet())) {
            PersTempPersonItem persTempPersonItem = buildPersTempPersonItem(appPersonItem);
            persTempPersonService.savePersonRegistrar(persTempPersonItem);
        }
        return ZKResultMsg.successMsg();
    }

    private PersTempPersonItem buildPersTempPersonItem(AppPersonItem appPersonItem) {
        PersTempPersonItem persTempPersonItem = persTempPersonService.getItemByPin(appPersonItem.getPin());
        if (persTempPersonItem == null) {
            persTempPersonItem = new PersTempPersonItem();
            persTempPersonItem.setStatus((short)1);
        }
        ModelUtil.copyProperties(appPersonItem, persTempPersonItem);
        persTempPersonItem.setPhotoPath(appPersonItem.getAvatarUrl());
        if (StringUtils.isNotBlank(appPersonItem.getMobile())) {
            // 设置手机
            persTempPersonItem.setMobilePhone(appPersonItem.getMobile());
        }
        if (StringUtils.isNotBlank(appPersonItem.getPassword())) {
            // 设置员工自助密码
            persTempPersonItem.setPersonPwd(appPersonItem.getPassword());
        }
        if (StringUtils.isNotBlank(appPersonItem.getAvatarUrl())) {
            // 设置人员头像
            persTempPersonItem.setPhotoPath(appPersonItem.getAvatarUrl());
            if (StringUtils.isBlank(appPersonItem.getCropUrl())) {
                // 抠图为空则设置空
                persTempPersonItem.setCropPhotoPath(null);
            } else {
                persTempPersonItem.setCropPhotoPath(appPersonItem.getCropUrl());
            }
        }
        if (StringUtils.isNotBlank(appPersonItem.getBirthday())) {
            // 设置人员生日
            persTempPersonItem
                .setBirthday(DateUtil.stringToDate(appPersonItem.getBirthday(), DateUtil.DateStyle.YYYY_MM_DD));
        }
        // 设置证件类型
        persTempPersonItem.setCertType(appPersonItem.getCertType());
        // 设置证件号
        persTempPersonItem.setCertNumber(appPersonItem.getCertNumber());
        // 标注为APP人员
        persTempPersonItem.setIsFrom("H5_APP");
        return persTempPersonItem;
    }

    private ZKResultMsg vaildPersonItem(String mobile, String pin, String email) {
        if (StringUtils.isNotBlank(mobile)) {
            boolean vaildMobile = vaildMobile(mobile);
            if (vaildMobile) {
                // 手机号码重复
                throw ZKBusinessException.errorException("pers_h5_personMobileRepeat");
            }
        }
        if (StringUtils.isNotBlank(pin)) {
            boolean vaildPin = vaildPin(pin);
            if (vaildPin) {
                // 人员编号重复
                throw ZKBusinessException.errorException("pers_app_pinExist");
            }
        }
        if (StringUtils.isNotBlank(email)) {
            boolean vaildEmail = vaildEmail(email);
            if (vaildEmail) {
                // 邮箱重复
                throw ZKBusinessException.errorException("pers_h5_personEmailRepeat");
            }
        }
        return ZKResultMsg.successMsg();
    }

    private boolean vaildEmail(String email) {
        // 取三张表的数据,验证邮箱是否存在
        PersTempPersonItem persTempPersonItem = persTempPersonService.getItemByEmail(email);
        AuthUserItem authUserItem = authUserService.getItemByEmail(email);
        PersPersonItem persPersonItem = persPersonService.getItemByEmail(email);
        if (persTempPersonItem != null || authUserItem != null || persPersonItem != null) {
            return true;
        }
        return false;
    }

    private boolean vaildPin(String pin) {
        // 取三张表的数据,验证人员pin是否存在
        PersTempPersonItem persTempPersonItem = persTempPersonService.getItemByPin(pin);
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persTempPersonItem != null || persPersonItem != null) {
            return true;
        }
        return false;
    }

    private boolean vaildMobile(String mobile) {
        // 取三张表的数据,验证手机号是否存在
        PersTempPersonItem persTempPersonItem = persTempPersonService.getItemByMobile(mobile);
        AuthUserItem authUserItem = authUserService.getItemByMobile(mobile);
        PersPersonItem persPersonItem = persPersonService.getItemByMobile(mobile);
        if (persTempPersonItem != null || authUserItem != null || persPersonItem != null) {
            return true;
        }
        return false;
    }

    @Override
    public ZKResultMsg savePersonPic(String mobilePhone, String photoBase64) {
        ZKResultMsg resultMsg = ZKResultMsg.failMsg();
        if (StringUtils.isNotBlank(photoBase64)) {
            resultMsg = ZKResultMsg.successMsg();
            JSONObject photoPath = new JSONObject();
            String filePath = FileUtil.saveFileToServer("pers", "user/avatar", mobilePhone + ".jpg", photoBase64);
            photoPath.put("avatarUrl", filePath);
            if (StringUtils.isNotBlank(filePath)) {
                // 抠图照片
                File sourceImg = new File(FileUtil.getLocalFullPath(filePath));
                BufferedImage src = null;
                try {
                    if (sourceImg.exists()) {
                        // 抠图服务使用原图
                        if (baseCropFaceService.loadDetectServiceSuccess()) {
                            baseCropFaceService.cropFace(FileUtil.getLocalFullPath(filePath), mobilePhone);
                        } else {
                            src = ImageIO.read(sourceImg);
                            if (src.getWidth() * src.getHeight() > 1024 * 1024) {// 超过1024*1024使用缩略图进行抠图，防止动态库挂
                                baseCropFaceService.cropFace(FileUtil.getLocalFullPath(FileUtil.getThumbPath(filePath)),
                                    mobilePhone);
                            } else {
                                baseCropFaceService.cropFace(FileUtil.getLocalFullPath(filePath), mobilePhone);
                            }
                        }
                    }
                    photoPath.put("cropUrl", FileUtil.getThumbPath(filePath));
                } catch (IOException e) {
                    logger.error("read img error", e);
                }
                resultMsg.setData(photoPath);
            }
        }
        return resultMsg;
    }

    @Override
    public void persLoginOut(String token) {
        String username = EncrypAESUtil.decryptToString(token).split("_")[0];
        stringRedisTemplate.delete(ConstUtil.AUTH_ACCESS_TOKEN + username);
    }

    @Override
    public AppPersonItem getUserInfoDetail(String customerId) {
        PersPersonItem personItem = persPersonService.getItemById(customerId);
        if (personItem != null) {
            // biotime无门禁
            // List<Acc4PersonLevelItem> acc4PersonLevelItemList =
            // acc4PersPersonService.getPersonLevelByPersonId(customerId, null);
            return buildAppPerson(personItem, null);
        }
        return null;
    }

    private AppPersonItem buildAppPerson(PersPersonItem personItem, List<Acc4PersonLevelItem> acc4PersonLevelItemList) {
        AppPersonItem appPerson = new AppPersonItem();
        ModelUtil.copyPropertiesIgnoreNull(personItem, appPerson);
        appPerson.setCustomerId(personItem.getId());
        appPerson.setHireDate(DateUtil.dateToString(personItem.getHireDate(), DateUtil.DateStyle.YYYY_MM_DD));
        PersTempPersonItem persTempPersonItem = persTempPersonService.getItemByPin(personItem.getPin());

        if (persTempPersonItem != null && StringUtils.isNotBlank(persTempPersonItem.getPhotoPath())) {// 头像
            appPerson.setAvatarUrl(persTempPersonItem.getPhotoPath());
        }
        if (persTempPersonItem != null && StringUtils.isNotBlank(persTempPersonItem.getCropPhotoPath())) {// 抠图
            appPerson.setAvatarUrl(persTempPersonItem.getCropPhotoPath());// 存在抠图设置抠图为头像
            appPerson.setCropUrl(persTempPersonItem.getCropPhotoPath());
        }
        // 找不到头像，设置默认头像
        if (StringUtils.isBlank(personItem.getPhotoPath())) {
            appPerson.setAvatarUrl("/images/appUser/userImage.png");
        }
        if (StringUtils.isNotBlank(personItem.getPhotoPath())) {
            appPerson.setAvatarUrl(personItem.getPhotoPath());
        }
        if (personItem.getBirthday() != null) {
            appPerson.setBirthday(DateUtil.dateToString(personItem.getBirthday(), DateUtil.DateStyle.YYYY_MM_DD));
        }
        // 设置人员手机
        appPerson.setMobile(personItem.getMobilePhone());
        // 设置人员能否登录
        appPerson.setEnabled(PersConstants.PERSON_ENABLE.equals(personItem.getStatus()) ? true : false);
        // 设置人员密码
        appPerson.setPassword(personItem.getSelfPwd());
        // 设置设备验证密码
        appPerson.setDevicePassword(personItem.getPersonPwd());
        // 设置重置密码
        appPerson.setResetPassword(false);
        if (acc4PersonLevelItemList != null && !acc4PersonLevelItemList.isEmpty()) {
            // 设置人员门禁权限
            appPerson
                .setAccLevelIds(CollectionUtil.getPropertys(acc4PersonLevelItemList, Acc4PersonLevelItem::getLevelId));
            appPerson.setAccLevelName(
                CollectionUtil.getPropertys(acc4PersonLevelItemList, Acc4PersonLevelItem::getLevelName));
        }

        // 设置证件资料
        PersCertificateItem persCertificateItem = persCertificateService.getItemByPersonId(personItem.getId());
        if (persCertificateItem != null) {
            appPerson.setCertType(persCertificateItem.getCertType());
            appPerson.setCertNumber(persCertificateItem.getCertNumber());
        }
        return appPerson;
    }

    @Override
    public ZKResultMsg changeUserInfo(AppPersonItem appPersonItem) {
        // 验证手机号码是否重复
        if (StringUtils.isNotBlank(appPersonItem.getMobile())) {
            boolean vaildPersonMobile = vaildPersonMobile(appPersonItem.getMobile(), appPersonItem.getPin());
            if (!vaildPersonMobile) {
                return ZKResultMsg.failMsg("pers_h5_personMobileRepeat");
            }
        }
        // 验证邮箱是否重复
        if (StringUtils.isNotBlank(appPersonItem.getEmail())) {
            boolean vaildPersonEmail = vaildPersonEmail(appPersonItem.getEmail(), appPersonItem.getPin());
            if (!vaildPersonEmail) {
                return ZKResultMsg.failMsg("pers_h5_personEmailRepeat");
            }
        }
        // 数据组装
        PersApiPersonItem persApiPersonItem = new PersApiPersonItem();
        ModelUtil.copyPropertiesIgnoreId(appPersonItem, persApiPersonItem);
        persApiPersonItem.setId(appPersonItem.getCustomerId());
        persApiPersonItem.setCardNo(appPersonItem.getCardNos());
        persApiPersonItem.setSelfPwd(appPersonItem.getPassword());
        persApiPersonItem.setMobilePhone(appPersonItem.getMobile());
        persApiPersonItem.setPersonPwd(appPersonItem.getDevicePassword());
        persApiPersonItem.setAccLevelIds(appPersonItem.getAccLevelIds());
        persApiPersonItem.setAccStartTime(appPersonItem.getIndateStart());
        persApiPersonItem.setAccEndTime(appPersonItem.getIndateEnd());
        persApiPersonItem.setPhotoPath(appPersonItem.getAvatarUrl());

        if (appPersonItem.getHireDate() != null) {
            persApiPersonItem.setHireDate(DateUtil.stringToDate(appPersonItem.getHireDate(), "yyyy-MM-dd"));
        }
        ApiResultMessage result = persApiPersonService.addApiPerson(persApiPersonItem);
        if (0 != result.getCode()) {
            return ZKResultMsg.failMsg(result.getMessage());
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 编辑验证手机号码
     * 
     * <AUTHOR>
     * @since 2019年6月25日 上午8:59:34
     * @param mobilePhone
     * @param pin
     * @return
     */
    private boolean vaildPersonMobile(String mobilePhone, String pin) {
        if (StringUtils.isNotBlank(mobilePhone)) {
            // 判断手机号码是否重复
            PersPersonItem persPersonItem = persPersonService.getItemByMobile(mobilePhone);
            if (persPersonItem != null && (!pin.equals(persPersonItem.getPin()))) {
                // 手机号码重复
                return false;
            }
            PersTempPersonItem itemByMobile = persTempPersonService.getItemByMobile(mobilePhone);
            if (itemByMobile != null && (!pin.equals(itemByMobile.getPin()))) {
                // 手机号码重复
                return false;
            }
        }
        return true;
    }

    /**
     * 编辑验证邮箱号码
     * 
     * <AUTHOR>
     * @since 2019年6月25日 上午8:59:53
     * @param email
     * @param pin
     * @return
     */
    private boolean vaildPersonEmail(String email, String pin) {
        if (StringUtils.isNotBlank(email)) {
            // 判断邮箱是否存在
            PersPersonItem persPersonItem = persPersonService.getItemByEmail(email);
            if (persPersonItem != null && (!persPersonItem.getPin().equals(pin))) {
                throw ZKBusinessException.errorException("pers_h5_personEmailRepeat");
            }

            PersTempPersonItem persTempPersonItem = persTempPersonService.getItemByEmail(email);
            if (persTempPersonItem != null && (!pin.equals(persTempPersonItem.getPin()))) {
                // 邮箱重复
                throw ZKBusinessException.errorException("pers_h5_personEmailRepeat");
            }
        }
        return true;
    }

    @Override
    public ZKResultMsg getPersParams() {
        Map<String, String> paramMap = persParamsService.getPersParams();
        if (!paramMap.isEmpty()) {
            ZKResultMsg result = new ZKResultMsg();
            result.setData(paramMap);
            return result;
        }
        return ZKResultMsg.failMsg();
    }

    @Override
    public ZKResultMsg sendEmailForResetPassword(String pin, String email, String Header) {
        AuthUserItem authUserItem = auth4PersService.findByPin(pin);
        if (authUserItem == null) {
            throw ZKBusinessException.errorException("pers_api_personNotExist");
        }
        if (!email.equals(authUserItem.getEmail())) {
            throw ZKBusinessException.errorException("pers_h5_persEmailNoExist");
        }
        String uuid = UUID.randomUUID().toString().replace("-", "");
        // 设置链接有效时间24小时
        redisTemplate.opsForValue().set(uuid, authUserItem, 24, TimeUnit.HOURS);
        String body = createSendMailBody(Header, uuid);
        if (forgetPwdService.sendEmail(email, I18nUtil.i18nCode("auth_mail_findPwd"), body, "")) {
            return ZKResultMsg.successMsg();
        }
        return ZKResultMsg.failMsg();
    }

    public String createSendMailBody(String host, String uuid) {
        String accessName = "ZKBioAccess";
        String httpMethod = httpsEnable.trim().equals("false") ? "http://" : "https://";
        uuid += "&view=defaultView";
        String lang = LocaleMessageSourceUtil.language;
        String url =
            httpMethod + host + "/portalForgetPwdResetPwd.do?account=" + uuid + "&lang=" + lang + "&loginType=PERS";
        String clickUrl = "<a href='" + url + "'>" + url + "<a/>";
        if (!sysName.equals(accessName)) {
            sysName = I18nUtil.i18nCode("common_sys_name");
        }
        String html = "<font>" + I18nUtil.i18nCode("auth_mail_resetUserAndPwdMsg1") + "</font><br/>"
            + "<div style='padding-left:30px;max-width:900px'>" + "<font>"
            + I18nUtil.i18nCode("auth_mail_resetUserAndPwdMsg2") + "</font>" + "<br/>" + clickUrl + "<br/>"
            + "<font style='color:red'>(" + I18nUtil.i18nCode("auth_mail_resetUserAndPwdMsg3") + ")</font>" + "<br/>"
            + I18nUtil.i18nCode("auth_mail_resetUserAndPwdMsg4") + "<br/><br/>"
            + I18nUtil.i18nCode("base_mail_sendLinkError") + "<br/>" + I18nUtil.i18nCode("base_mail_sendLinkErrorTip1")
            + "<br/>" + I18nUtil.i18nCode("base_mail_sendLinkErrorTip2") + "<br/>"
            + I18nUtil.i18nCode("auth_forgotPwd_sendLinkErrorTip") + "<br/>"
            + I18nUtil.i18nCode("base_mail_sendLinkErrorTip3") + "<br/><br/>" + "<div style='padding-left:500px'>"
            + sysName + "<br/>" + DateUtil.getDate(new Date(), DateStyle.YYYY_MM_DD_HH_MM_SS) + "<div/>" + "<div/>";
        return html;
    }
}
