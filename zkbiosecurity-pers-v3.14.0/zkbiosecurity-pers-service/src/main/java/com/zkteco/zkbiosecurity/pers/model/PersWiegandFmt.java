/**
 * File Name: PersWiegandFmt Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 PersWiegandFmt
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_WIEGANDFMT")
@Getter
@Setter
@Accessors(chain = true)
public class PersWiegandFmt extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;
    /**
     * BusinessID 下发设备中使用
     */
    @Column(name = "BUSINESS_ID")
    private Long businessId;

    /**
     * 名称
     */
    @Column(name = "NAME", length = 30, nullable = false)
    private String name;

    /**
     * 总位数
     */
    @Column(name = "WIEGAND_COUNT", nullable = false)
    private Short wiegandCount;

    /**
     * 模式（1、默认模式， 2、自定义模式）
     */
    @Column(name = "WIEGAND_MODE")
    private Short wiegandMode;

    /**
     * 卡校验格式: c s m p 命令格式
     */
    @Column(name = "CARD_FMT", length = 200)
    private String cardFmt;

    /**
     * 奇偶校验格式: o e b命令格式
     */
    @Column(name = "PARITY_FMT", length = 200)
    private String parityFmt;

    /**
     * 自动匹配时这个韦根位数的默认格式
     */
    @Column(name = "IS_DEFAULT_FMT")
    private Boolean isDefaultFmt;

    /**
     * 区位码
     */
    @Column(name = "SITE_CODE", length = 100)
    private String siteCode;

    /**
     * 用户区分初始化自动匹配的
     */
    @Column(name = "INIT_FLAG")
    private Boolean initFlag;
}