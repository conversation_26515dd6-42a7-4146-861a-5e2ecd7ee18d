package com.zkteco.zkbiosecurity.pers.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersBioPhoto;

public interface PersBioPhotoDao extends BaseDao<PersBioPhoto, String> {

    void deleteByPersonId(String personId);

    List<PersBioPhoto> findByPersonId(String personId);

    List<PersBioPhoto> findByPersonIdIn(List<String> personIds);

    List<PersBioPhoto> findByPersonIdInAndBioType(List<String> personIds, Short bioType);

    @Query(value = "SELECT count(0) FROM PERS_BIOPHOTO t LEFT JOIN PERS_PERSON p ON t.PERSON_ID=p.ID "
        + "WHERE p.auth_dept_id in (?1)", nativeQuery = true)
    long countByDeptIdIn(List<String> deptIdList);

    @Query(
        value = "SELECT count(0) FROM PERS_BIOPHOTO t LEFT JOIN PERS_PERSON p ON t.PERSON_ID=p.ID "
            + "WHERE p.auth_dept_id in (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=?1)",
        nativeQuery = true)
    long countByUserId(String userId);

    long countByPersonId(String personId);

    @Modifying
    @Query("delete from PersBioPhoto t where t.personPin in (?1) and t.bioType in (?2)")
    void deleteByPersonPinsAndBioTypes(List<String> personPins, List<Short> bioTypes);

    /**
     * 根据人员ids查询bioPhoto数量
     *
     * @return Object[] 下标 0:为 bioType 1：为 count 2:人员id
     */
    @Query(
        value = "SELECT p.BIO_TYPE,count(p.PERSON_ID),p.PERSON_ID FROM (SELECT t.BIO_TYPE,t.PERSON_ID FROM PERS_BIOPHOTO t WHERE t.PERSON_ID IN (?1) GROUP BY t.BIO_TYPE,t.PERSON_ID) p GROUP BY p.BIO_TYPE,p.PERSON_ID",
        nativeQuery = true)
    List<Object[]> countByPersonIdsAndGroupByBioType(Collection<String> personIds);

    List<PersBioPhoto> findByPersonIdAndBioType(String personId, Short bioType);

    @Query(value = "SELECT count(DISTINCT t.PERSON_ID) FROM PERS_BIOPHOTO t WHERE t.BIO_TYPE = ?1", nativeQuery = true)
    long countByBioTypeGroupByPersonId(Short bioType);

    long countByPersonIdAndBioType(String personId, Short bioType);

    @Query(value = "SELECT t.photoPath FROM PersBioPhoto t where t.personId = ?1 and t.bioType = ?2")
    List<String> findPhotoPathByPersonIdAndBioType(String personId, Short bioType);
}
