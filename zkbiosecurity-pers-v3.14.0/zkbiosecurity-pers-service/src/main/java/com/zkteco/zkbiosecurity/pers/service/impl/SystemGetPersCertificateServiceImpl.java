package com.zkteco.zkbiosecurity.pers.service.impl;

import com.zkteco.zkbiosecurity.pers.dao.PersCertificateDao;
import com.zkteco.zkbiosecurity.system.service.SystemGetPersCertificateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date Created In 18:12 2020/4/29
 */
@Service
public class SystemGetPersCertificateServiceImpl implements SystemGetPersCertificateService {

    @Autowired
    PersCertificateDao persCertificateDao;
    @Override
    public boolean isCertTypeInUse(String certCode) {
        return persCertificateDao.existsByCertType(certCode);
    }
}
