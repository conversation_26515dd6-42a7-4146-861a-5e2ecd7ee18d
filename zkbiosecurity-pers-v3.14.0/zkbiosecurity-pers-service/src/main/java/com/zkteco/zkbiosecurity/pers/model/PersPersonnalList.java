package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 名单库
 * 
 * <AUTHOR>
 * @date: 2018-03-08 下午06:03
 * @version v1.0
 */

@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "PERS_PERSONNAL_LIST")
public class PersPersonnalList extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 描述 */
    @Column(name = "DESCRIPTION", length = 250)
    private String description;

    /** 域模式 */
    @Column(name = "SCHEME", length = 50)
    private Integer scheme;

    /** 名单库名称 */
    @Column(name = "NAME", length = 250, unique = true)
    private String name;

    /** 名单库ID */
    @Column(name = "GROUP_STR_ID", length = 64)
    private String groupStrId;

    /** 用户自定义 */
    @Column(name = "TAG", length = 50)
    private String tag;

    /** 名单库类型(2:黑 3:白 4:红 5:访客允许名单库) */
    @Column(name = "TYPE", length = 50)
    private String type;

    /** 是否初始化标记 */
    @Column(name = "INIT_FLAG")
    private Boolean initFlag;

    /** 表与表关系 **/
    public PersPersonnalList() {
        super();
    }

    public PersPersonnalList(String id) {
        super();
        this.id = id;
    }
}
