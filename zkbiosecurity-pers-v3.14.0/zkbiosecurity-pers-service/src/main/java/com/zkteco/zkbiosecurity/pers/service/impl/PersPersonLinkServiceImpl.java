/**
 * File Name: PersPersonLinkServiceImpl
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonLinkDao;
import com.zkteco.zkbiosecurity.pers.model.PersPersonLink;
import com.zkteco.zkbiosecurity.pers.service.PersPersonLinkService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonLinkItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * 对应百傲瑞达 PersPersonLinkServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Service
@Transactional
public class PersPersonLinkServiceImpl implements PersPersonLinkService {
    @Autowired
    private PersPersonLinkDao persPersonLinkDao;

    @Override
    public PersPersonLinkItem saveItem(PersPersonLinkItem item) {
        PersPersonLink persPersonLink = persPersonLinkDao.findByPersonIdAndLinkIdAndType(item.getPersonId(), item.getLinkId(), item.getType());
        if (Objects.isNull(persPersonLink)) {
            persPersonLink = new PersPersonLink();
            ModelUtil.copyProperties(item, persPersonLink);
            persPersonLinkDao.save(persPersonLink);
        }
        item.setId(persPersonLink.getId());
        return item;
    }

    @Override
    public void saveBatchItem(PersPersonLinkItem.LinkTypeInterFace linkTypeEnum, String linkId, List<String> personIds) {
        List<PersPersonLink> personLinkList = persPersonLinkDao.findByPersonIdInAndLinkIdAndType(personIds,linkId,linkTypeEnum.name());
        Set<String> personLinkSet = CollectionUtil.listToPropertySet(personLinkList, p -> p.getPersonId() + "_" + p.getLinkId() + "_" + p.getType());
        personIds.stream().forEach(personId -> {
            if (!personLinkSet.contains(personId + "_" + linkId + "_" + linkTypeEnum.name())) {
                PersPersonLink persPersonLink = new PersPersonLink();
                persPersonLink.setLinkId(linkId);
                persPersonLink.setType(linkTypeEnum.name());
                persPersonLink.setPersonId(personId);
                persPersonLinkDao.save(persPersonLink);
            }
        });
    }

    @Override
    public List<PersPersonLinkItem> getByCondition(PersPersonLinkItem condition) {
        return (List<PersPersonLinkItem>) persPersonLinkDao.getItemsBySql(condition.getClass(),
                SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persPersonLinkDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public void deleteByPersonId(String personId) {
        persPersonLinkDao.deleteByPersonId(personId);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                persPersonLinkDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public PersPersonLinkItem getItemById(String id) {
        List<PersPersonLinkItem> items = getByCondition(new PersPersonLinkItem(id));
        return Optional.ofNullable(items).filter(i -> !i.isEmpty()).map(i -> i.get(0)).orElse(null);
    }

    @Override
    public PersPersonLinkItem getItemByPersonId(String personId) {
        PersPersonLinkItem item = new PersPersonLinkItem();
        item.setPersonId(personId);
        List<PersPersonLinkItem> items = getByCondition(item);
        return Optional.ofNullable(items).filter(i -> !i.isEmpty()).map(i -> i.get(0)).orElse(null);
    }

	@Override
	public List<PersPersonLinkItem> getItemByPersonIds(String personIds) {
		PersPersonLinkItem item = new PersPersonLinkItem();
		item.setPersonIdIn(personIds);
		return getByCondition(item);
	}

	@Override
	public void deletePerson(PersPersonLinkItem persPersonLinkItem) {
		List<PersPersonLinkItem> persPersonLinkItemList = getByCondition(persPersonLinkItem);
		persPersonLinkItemList.forEach(item -> {
			persPersonLinkDao.deleteById(item.getId());
		});
	}

    @Override
    public void deleteBatchItem(PersPersonLinkItem.LinkTypeInterFace linkTypeEnum, List<String> linkIds, List<String> personIds) {
        persPersonLinkDao.deleteBatchItem(linkTypeEnum.name(),linkIds, personIds);
    }

    @Override
    public void deleteBatchItemByLinkId(PersPersonLinkItem.LinkTypeInterFace linkTypeEnum, List<String> linkIds) {
        persPersonLinkDao.deleteBatchItemByLinkId(linkTypeEnum.name(),linkIds);
    }

    @Override
    public PersPersonLinkItem save(PersPersonLinkItem item) {
        PersPersonLink persPersonLink = Optional.ofNullable(item)
                .map(i->i.getId())
                .filter(StringUtils::isNotBlank)
                .flatMap(id->persPersonLinkDao.findById(id))
                .orElse(new PersPersonLink());
        ModelUtil.copyPropertiesIgnoreNullWithProperties(item, persPersonLink, "id");
        persPersonLinkDao.save(persPersonLink);
        item.setId(persPersonLink.getId());
        return item;
    }

    @Override
    public void saveBatchPersonLinkItem(List<PersPersonLinkItem> linkItemList) {
        if (!CollectionUtil.isEmpty(linkItemList)){
            List<PersPersonLink> persPersonLinkList = ModelUtil.copyListPropertiesWithIgnore(linkItemList, PersPersonLink.class);
            persPersonLinkDao.saveAll(persPersonLinkList);
        }
    }
}