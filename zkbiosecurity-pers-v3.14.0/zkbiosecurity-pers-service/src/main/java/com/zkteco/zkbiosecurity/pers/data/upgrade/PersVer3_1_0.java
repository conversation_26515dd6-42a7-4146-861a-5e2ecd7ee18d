package com.zkteco.zkbiosecurity.pers.data.upgrade;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersPersonnalListService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryService;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryItem;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryValueItem;

@Component
public class PersVer3_1_0 implements UpgradeVersionManager {
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private PersPersonnalListService persPersonnalListService;
    @Autowired
    private BaseDictionaryService baseDictionaryService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v3.1.0";
    }

    @Override
    public boolean executeUpgrade() {
        AuthPermissionItem subMenuItem1 = authPermissionService.getItemByCode("PersPersonManager");
        if (subMenuItem1 != null) {
            // -----------------------------------------人员名单库-------------------------------------------//
            AuthPermissionItem subMenuItem = new AuthPermissionItem("PersPersonnalList", "pers_personnal_list",
                "pers:personnallist", AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 7);
            subMenuItem.setParentId(subMenuItem1.getId());
            subMenuItem.setActionLink("persPersonnalList.do");
            subMenuItem = authPermissionService.initData(subMenuItem);
            // 创建按钮
            AuthPermissionItem subButtonItem = new AuthPermissionItem("PersPersonnalListRefresh", "common_op_refresh",
                "pers:personnallist:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
            subButtonItem = new AuthPermissionItem("PersPersonnalListAdd", "common_op_add", "pers:personnallist:add",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
            subButtonItem = new AuthPermissionItem("PersPersonnalListEdit", "common_op_edit", "pers:personnallist:edit",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
            subButtonItem = new AuthPermissionItem("PersPersonnalListDel", "common_op_del", "pers:personnallist:del",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
            // 名单库添加人员
            subButtonItem = new AuthPermissionItem("PersPersonnalListAddPerson", "common_op_addPerson",
                "pers:personnallist:addPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
            // 人员刷新 add by cxq 20200916
            subButtonItem = new AuthPermissionItem("PersPersonnalListPersonRefresh", "common_op_refresh",
                "pers:personnallist:refreshPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
            // 名单库删除人员 add by cxq 20200916
            subButtonItem = new AuthPermissionItem("PersPersonnalListDelPerson", "pers_common_delPerson",
                "pers:personnallist:delPerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 9);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            // 调整参数设置的位置
            subMenuItem = authPermissionService.getItemByCode("PersParams");
            if (Objects.nonNull(subMenuItem)) {
                subMenuItem.setOrderNo(8);
                authPermissionService.saveItem(subMenuItem);
            }
        }

        persPersonnalListService.initData(new PersPersonnalListItem(I18nUtil.i18nCode("pers_personnal_passList"),
            PersConstants.TYPE_ALLOW_LIST, true));
        persPersonnalListService.initData(
            new PersPersonnalListItem(I18nUtil.i18nCode("pers_personnal_banList"), PersConstants.TYPE_BAND_LIST, true));
        initPersonnallist();
        return true;
    }

    /**
     * 初始化名单库类型
     * 
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-04-01 15:03
     * @since 1.0.0
     */
    private void initPersonnallist() {
        // 名单库类型(允许名单/禁止名单)
        BaseDictionaryItem baseDictionaryItem =
            new BaseDictionaryItem("PersPersonnallistType", "pers_personnal_list_type", false, "pers_module");
        baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
        baseDictionaryValueService.initData(new BaseDictionaryValueItem(PersConstants.TYPE_BAND_LIST,
            "pers_personnal_bannedList", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService.initData(new BaseDictionaryValueItem(PersConstants.TYPE_ALLOW_LIST,
            "pers_personnal_allowList", 0, baseDictionaryItem.getId()));
    }
}
