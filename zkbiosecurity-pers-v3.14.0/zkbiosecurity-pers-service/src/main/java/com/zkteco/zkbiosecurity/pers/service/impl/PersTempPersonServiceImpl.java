/*
 * File Name: PersTempPersonServiceImpl <NAME_EMAIL> on 2018/9/26 10:37. Copyright:Copyright ©
 * 1999-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;

import com.zkteco.zkbiosecurity.acc.service.Acc4PersDeviceService;
import com.zkteco.zkbiosecurity.acc.service.Acc4PersPersonService;
import com.zkteco.zkbiosecurity.att.service.Att4PersPersonService;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.ins.service.Ins4PersPersonService;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPositionDao;
import com.zkteco.zkbiosecurity.pers.dao.PersTempPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.model.PersPosition;
import com.zkteco.zkbiosecurity.pers.model.PersTempPerson;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.utils.PersPersonUtil;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.pid.service.Pid4PersPersonService;
import com.zkteco.zkbiosecurity.system.app.service.BaseAuthCloudMessageSendService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

@Service
@Transactional
public class PersTempPersonServiceImpl implements PersTempPersonService {
    private final static Logger logger = LoggerFactory.getLogger(PersTempPersonServiceImpl.class);
    @Autowired
    private PersTempPersonDao persTempPersonDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired(required = false)
    private Acc4PersDeviceService acc4PersDeviceService;
    @Autowired(required = false)
    private Acc4PersPersonService acc4PersPersonService;
    @Autowired(required = false)
    private Att4PersPersonService att4PersPersonService;
    @Autowired(required = false)
    private Ins4PersPersonService ins4PersPersonService;
    @Autowired(required = false)
    private Pid4PersPersonService pid4PersPersonService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;

    @Autowired
    private PersPositionDao persPositionDao;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersAttributeExtService persAttributeExtService;
    @Autowired
    private BaseAuthCloudMessageSendService baseAuthCloudMessageSendService;
    @Autowired
    private PersBioPhotoService persBioPhotoService;

    @Override
    public PersTempPersonItem saveItem(PersTempPersonItem item) {
        PersTempPerson persTempPerson = Optional.ofNullable(item).map(i -> i.getPin()).filter(StringUtils::isNotBlank)
            .map(persTempPersonDao::findByPin).orElse(new PersTempPerson());
        ModelUtil.copyPropertiesWithIgnore(item, persTempPerson, "id");
        persTempPerson.setCompanyName(StringUtils.isNotBlank(item.getRemrak()) ? item.getRemrak() : "");
        persTempPersonDao.save(persTempPerson);
        item.setId(persTempPerson.getId());
        return item;
    }

    @Override
    public List<PersTempPersonItem> getByCondition(PersTempPersonItem condition) {
        return (List<PersTempPersonItem>)persTempPersonDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public PersTempPersonItem getItemById(String id) {
        List<PersTempPersonItem> items = getByCondition(new PersTempPersonItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public ZKResultMsg audit(PersTempPersonItem item) {
        // String ids, String deptId, String areaId, String levelId, String companyName, String certNumber, String
        // mobilePhone
        // 查询出未审核的人员登记
        List<PersTempPerson> persTempPersonList =
            persTempPersonDao.findByStatusAndIdIn((short)0, CollectionUtil.strToList(item.getId()));
        StringBuffer failMsg = new StringBuffer();
        StringBuffer sendCloudAudit = new StringBuffer();
        if (persTempPersonList != null && persTempPersonList.size() == 1) {
            PersTempPerson persTempPerson = persTempPersonList.get(0);
            PersTempPersonItem persTempPersonItem = new PersTempPersonItem();
            ModelUtil.copyPropertiesIgnoreNull(persTempPerson, persTempPersonItem);
            persTempPersonItem.setDeptId(item.getDeptId());
            persTempPersonItem.setRemrak(item.getRemrak());
            // persTempPersonItem.setCertType("2");
            persTempPersonItem.setMobilePhone(item.getMobilePhone());
            ZKResultMsg resultMsg = auditPerson(persTempPersonItem, item.getAreaId(), item.getAccLevelIds());
            if ("ok".equals(resultMsg.getRet())) {
                persTempPerson.setStatus((short)1);
                persTempPersonDao.save(persTempPerson);
                if (PersConstants.PERS_CLOUD_APP.equals(persTempPerson.getIsFrom())) {
                    // 用户版APP登记用户审批成功，需要推送数据到云端
                    sendCloudAudit.append(persTempPerson.getPin() + ",");
                }
            } else {
                failMsg.append(persTempPerson.getPin() + ",");
            }
        } else {
            persTempPersonList.forEach(persTempPerson -> {
                PersTempPersonItem persTempPersonItem = new PersTempPersonItem();
                ModelUtil.copyPropertiesIgnoreNull(persTempPerson, persTempPersonItem);
                persTempPersonItem.setDeptId(item.getDeptId());
                ZKResultMsg resultMsg = auditPerson(persTempPersonItem, item.getAreaId(), item.getAccLevelIds());
                if ("ok".equals(resultMsg.getRet())) {
                    persTempPerson.setStatus((short)1);
                    persTempPersonDao.save(persTempPerson);
                    if (PersConstants.PERS_CLOUD_APP.equals(persTempPerson.getIsFrom())) {
                        // 用户版APP登记用户审批成功，需要推送数据到云端
                        sendCloudAudit.append(persTempPerson.getPin() + ",");
                    }
                } else {
                    failMsg.append(persTempPerson.getPin() + ",");
                }
            });
        }
        if (failMsg.length() > 0) {
            return ZKResultMsg.failMsg(failMsg.substring(0, failMsg.length() - 1));
        }

        // if (sendCloudAudit.length() > 0) {
        // //推送审批成功人员数据到云
        // AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemById(deptId);
        // persCloudMessageSendService.sendPersonAudit(sendCloudAudit.substring(0, sendCloudAudit.length() - 1),
        // authDepartmentItem.getCode());
        // }
        return ZKResultMsg.successMsg();
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, PersTempPersonItem condition, int pageNo, int pageSize) {
        Pager pager = getItemsByPage(condition, pageNo, pageSize);
        buildAttribute(pager.getData());
        return pager;
    }

    /**
     * 封装自定义属性数据
     *
     * @param items
     * @return java.util.List<?>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:20
     */
    private List<?> buildAttribute(List<?> items) {
        List<PersTempPersonItem> list = (List<PersTempPersonItem>)items;
        List<List<String>> propertyList =
            CollectionUtil.getPropertyList(list, PersTempPersonItem::getId, CollectionUtil.splitSize);
        propertyList.forEach(personIds -> {
            List<PersAttributeExtItem> attrExtItems = persAttributeExtService.getItemByPersonIdList(personIds);
            Map<String, PersAttributeExtItem> attrExtItemMap =
                CollectionUtil.listToKeyMap(attrExtItems, PersAttributeExtItem::getPersonId);
            list.forEach(item -> {
                item.setAttrExtMap(new BeanMap(attrExtItemMap.get(item.getId())));
            });
        });
        return items;
    }

    @Override
    public ZKResultMsg savePersonRegistrar(PersTempPersonItem persTempPersonItem) {
        PersLeavePersonItem persLeavePersonItem = persLeavePersonService.getItemByPin(persTempPersonItem.getPin());
        if (persLeavePersonItem != null) {
            // 离职人员不为空，则返回异常,人员编号重复
            return ZKResultMsg.failMsg("pers_app_pinExist");
        }
        // 云端创建系统用户通知线下：直接通过审核，修改人员existsMobileUser状态为true，让手机号不可编辑
        if (Objects.nonNull(persTempPersonItem.getIsCloudCreateMobileUser())
            && persTempPersonItem.getIsCloudCreateMobileUser()) {
            ZKResultMsg resultMsg = auditPerson(persTempPersonItem, null, null);
            return resultMsg;
        }
        // 自动审批
        if ("1".equals(baseSysParamService.getValByName("pers.tempPerson.audit"))) {
            ZKResultMsg resultMsg = auditPerson(persTempPersonItem, null, null);
            if ("ok".equals(resultMsg.getRet())) {
                // 审批成功,修改人员状态
                persTempPersonItem.setStatus((short)1);
            } else {
                persTempPersonItem.setStatus((short)0);
            }
            if (StringUtils.isNotBlank(persTempPersonItem.getPhotoPathBase64())) {
                String photoPath = FileUtil.saveFileToServer("pers", "tempPerson", persTempPersonItem.getPin() + ".jpg",
                    persTempPersonItem.getPhotoPathBase64());
                persTempPersonItem.setPhotoPath(photoPath);
            }
            if (StringUtils.isNotBlank(persTempPersonItem.getCropPhotoPathBase64())) {
                String fileName = persTempPersonItem.getPin() + ".jpg";
                String filePath = FileUtil.getCropFacePath("pers/tempPerson", persTempPersonItem.getPin());
                FileUtil.saveFile(filePath, fileName, persTempPersonItem.getCropPhotoPathBase64(), false);
                persTempPersonItem.setCropPhotoPath("/" + filePath + "/" + fileName);
            }

        } else {
            persTempPersonItem.setStatus((short)0);
            if (StringUtils.isNotBlank(persTempPersonItem.getPhotoPath())) {
                String imgBase64 = getPhotoBase64(persTempPersonItem.getPhotoPath());
                if (StringUtils.isNotBlank(imgBase64)) {
                    String photoPath = FileUtil.saveFileToServer("pers", "tempPerson",
                        persTempPersonItem.getPin() + ".jpg", imgBase64);
                    persTempPersonItem.setPhotoPath(photoPath);
                }
            }
            if (StringUtils.isNotBlank(persTempPersonItem.getCropPhotoPath())) {
                String imgBase64 = getPhotoBase64(persTempPersonItem.getCropPhotoPath());
                if (StringUtils.isNotBlank(imgBase64)) {
                    String fileName = persTempPersonItem.getPin() + ".jpg";
                    String filePath = FileUtil.getCropFacePath("pers/tempPerson", persTempPersonItem.getPin());
                    FileUtil.saveFile(filePath, fileName, imgBase64, false);
                    persTempPersonItem.setCropPhotoPath("/" + filePath + "/" + fileName);
                }
            }
        }
        // 图片加密
        if (StringUtils.isNotBlank(persTempPersonItem.getPhotoPath())) {
            PersPersonUtil.encryptPersonPhoto(persTempPersonItem.getPhotoPath(), persTempPersonItem.getPin(),
                "pers/tempPerson");
        }
        // 判断是新增还是编辑
        PersPersonItem oldPersPersonItem = persPersonService.getItemByPin(persTempPersonItem.getPin());
        String code = persTempPersonItem.getDeptCode();
        AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemByCode(code);
        if (authDepartmentItem != null) {
            persTempPersonItem.setDeptId(authDepartmentItem.getId());
        } else if (oldPersPersonItem != null) {
            persTempPersonItem.setDeptId(oldPersPersonItem.getDeptId());
        } else {
            authDepartmentItem = authDepartmentService.getDefautDept();
            persTempPersonItem.setDeptId(authDepartmentItem.getId());
        }
        saveItem(persTempPersonItem);
        return ZKResultMsg.successMsg();
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persTempPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            Collection<String> list = CollectionUtil.strToList(ids);
            list.forEach(id -> {
                PersTempPerson persTempPerson = persTempPersonDao.getOne(id);
                if (StringUtils.isNotBlank(persTempPerson.getPhotoPath())
                    && !persTempPerson.getPhotoPath().contains("http")) {
                    FileUtil.deleteFile(FileUtil.getLocalFullPath(persTempPerson.getPhotoPath()));// 删除人员头像
                    FileUtil
                        .deleteFile(FileUtil.getLocalFullPath(FileUtil.getThumbPath(persTempPerson.getPhotoPath())));// 删除人员头像缩略图
                }
                // 删除抠图
                String cropPhotoPath = persTempPerson.getCropPhotoPath();
                if (StringUtils.isNotBlank(cropPhotoPath) && !cropPhotoPath.contains("http")
                    && cropPhotoPath.lastIndexOf("/") > 0) {
                    FileUtil.deleteDirectory(
                        FileUtil.getLocalFullPath(cropPhotoPath.substring(0, cropPhotoPath.lastIndexOf("/"))));
                }
                persTempPersonDao.deleteById(id);
            });
        }
        return false;
    }

    private ZKResultMsg auditPerson(PersTempPersonItem persTempPersonItem, String areaId, String levelId) {
        // 判断人数许可
        if (!persPersonService.persLicenseCheck()) {
            throw ZKBusinessException.warnException("common_license_maxCount");
        }
        PersPersonItem persPersonItem = new PersPersonItem();
        // 判断是新增还是编辑
        PersPersonItem oldPersPersonItem = persPersonService.getItemByPin(persTempPersonItem.getPin());
        if (oldPersPersonItem != null) {
            persPersonItem = ModelUtil.copyPropertiesIgnoreNull(oldPersPersonItem, persPersonItem);
            String code = persTempPersonItem.getDeptCode();
            AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemByCode(code);
            if (authDepartmentItem != null) {
                persPersonItem.setDeptId(authDepartmentItem.getId());
                persPersonItem.setDeptCode(authDepartmentItem.getCode());
                persPersonItem.setDeptName(authDepartmentItem.getName());
            }
        } else {
            PersLeavePersonItem leavePersonItem = persLeavePersonService.getItemByPin(persTempPersonItem.getPin());
            if (leavePersonItem != null) {
                throw ZKBusinessException
                    .warnException(I18nUtil.i18nCode("pers_import_pinLeaved", persTempPersonItem.getPin()));
            }
            String certNumber = persTempPersonItem.getCertNumber();
            if (StringUtils.isNotBlank(certNumber) && "2".equals(persTempPersonItem.getCertType())) {
                if (Integer.parseInt(certNumber.substring(16).substring(0, 1)) % 2 == 0) {// 判断性别
                    persPersonItem.setGender("F");
                } else {
                    persPersonItem.setGender("M");
                }
            }
        }
        ModelUtil.copyPropertiesIgnoreNullWithProperties(persTempPersonItem, persPersonItem, "id", "personPwd",
            "photoPath", "cropPhotoPath", "mobilePhone");
        if (StringUtils.isNotBlank(persTempPersonItem.getPersonPwd())) {
            // 设置云上密码为员工自助密码，不是门禁密码
            persPersonItem.setSelfPwd(persTempPersonItem.getPersonPwd());
        }
        if (StringUtils.isNotBlank(persTempPersonItem.getMobilePhone())) {
            persPersonItem.setMobilePhone(persTempPersonItem.getMobilePhone());
        }
        // 设置云上人员设备验证密码
        persPersonItem.setPersonPwd(persTempPersonItem.getDevicePwd());
        // persPersonItem.setId(null);
        persPersonItem.setIdCard(persTempPersonItem.getCertNumber());
        if (StringUtils.isBlank(levelId) && StringUtils.isNotBlank(persTempPersonItem.getAccLevelIds())) {
            logger.info("============云上levelIds=" + persTempPersonItem.getAccLevelIds());
            // 设置从云上设置的门禁权限组ID
            levelId = persTempPersonItem.getAccLevelIds();
        }
        Map<String, String> extParams = buildExtParamMap(oldPersPersonItem, levelId, areaId);
        persPersonItem.setStatus((short)0);
        if (StringUtils.isBlank(persPersonItem.getDeptId())) {
            AuthDepartmentItem authDepartmentItem = null;
            // 查询对应部门，不存在则设置为默认部门
            if (StringUtils.isNotBlank(persPersonItem.getDeptCode())) {
                authDepartmentItem = authDepartmentService.getItemByCode(persPersonItem.getDeptCode());
            }
            if (authDepartmentItem == null) {
                authDepartmentItem = authDepartmentService.getDefautDept();
            }
            persPersonItem.setDeptId(authDepartmentItem.getId());
        }

        // 处理职位数据
        if (StringUtils.isNotBlank(persTempPersonItem.getPositionCode())) {
            PersPosition persPosition = persPositionDao.findByCode(persTempPersonItem.getPositionCode());
            if (persPosition != null) {
                persPersonItem.setPositionId(persPosition.getId());
            }
        } else if (oldPersPersonItem != null && StringUtils.isNotBlank(oldPersPersonItem.getPositionId())) {
            persPersonItem.setPositionId(oldPersPersonItem.getPositionId());
        }

        // 需要对传过来的照片进行处理
        String imgBase64 = "";
        // 兼容以前的.jpg的照片
        if (StringUtils.isNotBlank(persTempPersonItem.getPhotoPath())
            && persTempPersonItem.getPhotoPath().startsWith("http")) {
            imgBase64 = getPhotoBase64(persTempPersonItem.getPhotoPath());
        } else if (StringUtils.isNotBlank(persTempPersonItem.getCloudImgBase64())) {
            imgBase64 = persTempPersonItem.getCloudImgBase64();
        }
        // 判断人员是否有照片，有则组装并放入待下发人员照片信息集合里
        if (StringUtils.isNotBlank(persTempPersonItem.getPhotoPath())) {
            if (StringUtils.isBlank(imgBase64)) {
                imgBase64 = getPhotoBase64(persTempPersonItem.getPhotoPath());
            }
            if (StringUtils.isNotBlank(imgBase64)) {
                String photoPath =
                    FileUtil.saveFileToServer("pers", "user/avatar", persPersonItem.getPin() + ".jpg", imgBase64);
                persPersonItem.setPhotoPath(photoPath);
                persTempPersonItem.setPhotoPathBase64(imgBase64);
            }
        }
        // 需要对传过来的抠图照片进行处理
        String imgCropBase64 = "";
        String cropPhotoPath = "";
        // 兼容以前的.jpg的照片
        if (StringUtils.isNotBlank(persTempPersonItem.getCropPhotoPath())
            && persTempPersonItem.getCropPhotoPath().startsWith("http")) {
            imgCropBase64 = getPhotoBase64(persTempPersonItem.getCropPhotoPath());
        } else if (StringUtils.isNotBlank(persTempPersonItem.getCloudCropImgBase64())) {
            imgCropBase64 = persTempPersonItem.getCloudCropImgBase64();
        }
        if (StringUtils.isNotBlank(persTempPersonItem.getCropPhotoPath())) {
            if (StringUtils.isBlank(imgCropBase64)) {
                imgCropBase64 = getPhotoBase64(persTempPersonItem.getCropPhotoPath());
            }
            if (StringUtils.isNotBlank(imgCropBase64)) {
                cropPhotoPath = FileUtil.saveCropFaceToServer(persPersonItem.getPin(), persPersonItem.getPin() + ".jpg",
                    imgCropBase64);
                cropPhotoPath = FileUtil.getCropFacePath(persPersonItem.getPin()) + FileUtil.separator
                    + persPersonItem.getPin() + ".jpg";
                persPersonItem.setCropPhotoPath(cropPhotoPath);
                persTempPersonItem.setCropPhotoPathBase64(imgCropBase64);
            }
        }
        // 兼容云端重新下发人员的情况：云端不下发人员抠图，线下在PersPersonServiceImpl#saveItem重新抠图，因此照片不做加密处理 ----modify by zhixiong.huang
        // 2022-08-26 0:04
        if (StringUtils.isNotBlank(persTempPersonItem.getPhotoPath())
            && StringUtils.isNotBlank(persTempPersonItem.getCropPhotoPath()) && StringUtils.isNotBlank(imgCropBase64)) {
            // 图片加密
            PersPersonUtil.encryptPersonPhoto(persPersonItem.getPhotoPath(), persPersonItem.getPin());
        }

        PersCertificateItem persCertificateItem = null;
        PersAttributeExtItem persAttributeExtItem = new PersAttributeExtItem();
        if (StringUtils.isNotBlank(persTempPersonItem.getCertType())
            && StringUtils.isNotBlank(persTempPersonItem.getCertNumber())) {
            // 设置人员证件信息
            persCertificateItem = new PersCertificateItem();
            persCertificateItem.setName(persPersonItem.getName());
            persCertificateItem.setLastName(persTempPersonItem.getLastName());
            persCertificateItem.setPin(persTempPersonItem.getPin());
            persCertificateItem.setCertType(persTempPersonItem.getCertType());
            persCertificateItem.setCertNumber(persTempPersonItem.getCertNumber());
        }

        persAttributeExtItem = persAttributeExtService.getItemByPersonId(persTempPersonItem.getId());
        if (StringUtils.isNotBlank(persTempPersonItem.getFamilyAddress())) {
            // 设置人员家庭地址
            persAttributeExtItem.setAttrValue12(persTempPersonItem.getFamilyAddress());
        }
        persPersonItem =
            persPersonService.saveItem(persPersonItem, persCertificateItem, persAttributeExtItem, extParams);
        return ZKResultMsg.successMsg();
    }

    private Map<String, String> buildExtParamMap(PersPersonItem persPersonItem, String levelId, String areaId) {
        Map<String, String> extParams = new HashMap<>();
        if (Objects.nonNull(persPersonItem)) { // 编辑人员
            logger.info("=======persPersonItem is not null");
            if (Objects.nonNull(acc4PersPersonService)) {
                logger.info("=======acc4PersPersonService is not null");
                // 编辑时需要获取门禁人员设置参数，避免编辑覆盖门禁人员参数
                Map<String, String> accExtParams = acc4PersPersonService.getAccPersonExtParam(persPersonItem.getId());
                extParams.putAll(accExtParams);
                if (StringUtils.isNotBlank(levelId)) {
                    logger.info("========levelId:" + levelId);
                    extParams.put("acc.personLevelIds", levelId);
                }
            }
            if (Objects.nonNull(att4PersPersonService)) {
                // 获取考勤参数
                Map<String, String> attExtParams = att4PersPersonService.getAttPersonExtParam(persPersonItem.getId());
                extParams.putAll(attExtParams);
            }
            if (Objects.nonNull(ins4PersPersonService)) {
                // 获取信息屏参数
                Map<String, String> insExtParams = ins4PersPersonService.getInsPersonExtParam(persPersonItem.getId());
                extParams.putAll(insExtParams);
            }
            if (Objects.nonNull(pid4PersPersonService)) {
                // 获取人证参数
                Map<String, String> pidExtParams = pid4PersPersonService.getPidPersonExtParam(persPersonItem.getId());
                extParams.putAll(pidExtParams);
            }
            if (StringUtils.isNotBlank(areaId)) {
                extParams.put("att.personAreas", areaId);
                extParams.put("ins.personAreas", areaId);
                extParams.put("pid.personAreas", areaId);
            }
        } else {
            // 将云服务传过来的人员设置默认考勤数据。
            if (areaId == null) {
                areaId = authAreaService.getItemByCode("1").getId();
            }
            extParams.put("att.personAreas", areaId);
            extParams.put("att.isAttendance", "true");
            extParams.put("att.perDevAuth", "0");
            // 云服务传过来的人员设置默认信息屏数据。
            extParams.put("ins.personAreas", areaId);
            extParams.put("ins.perDevAuth", "0");
            extParams.put("ins.category", "0");
            // 云服务传过来的人员设置默认ai区域。
            extParams.put("ai.personAreaIds", areaId);
            extParams.put("pid.personAreas", areaId);
            // 云服务传过来的人员添加到通用权限组
            if (Objects.nonNull(acc4PersDeviceService)) {
                if (StringUtils.isBlank(levelId)) {
                    levelId = acc4PersDeviceService.getMasterLevelId();
                }
                extParams.put("acc.personLevelIds", levelId);
            }
        }
        extParams.put("moduleAuth", "acc,att,ins,ai,pid");
        return extParams;
    }

    /**
     * 读取网络路径图片返回base64编码
     *
     * @param pathUrl
     * @return
     */
    private String getPhotoBase64(String pathUrl) {
        String imgBase64 = "";
        // 包含判断有缺陷，如果路径中包含http，就不一定是网络请求 modified by max 20190514
        if (pathUrl.startsWith("http")) {
            URL url = null;
            HttpURLConnection httpUrl = null;
            InputStream is = null;
            ByteArrayOutputStream outStream = null;
            try {
                url = new URL(pathUrl);
                httpUrl = (HttpURLConnection)url.openConnection();
                httpUrl.connect();
                is = httpUrl.getInputStream();
                // 图片不存在则 stream == null
                if (is != null) {
                    outStream = new ByteArrayOutputStream();
                    // 创建一个Buffer字符串
                    byte[] buffer = new byte[1024];
                    // 每次读取的字符串长度，如果为-1，代表全部读取完毕
                    int len = 0;
                    // 使用一个输入流从buffer里把数据读取出来
                    while ((len = is.read(buffer)) != -1) {
                        // 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                        outStream.write(buffer, 0, len);
                    }
                    imgBase64 = Base64Utils.encodeToString(outStream.toByteArray());
                }
            } catch (IOException e) {
                logger.error("Class PersTempPersonServiceImpl method auditPerson", e);
            } finally {
                try {
                    if (is != null) {
                        is.close();
                    }
                    if (outStream != null) {
                        outStream.close();
                    }
                    if (httpUrl != null) {
                        httpUrl.disconnect();
                    }
                } catch (Exception e) {
                    logger.error("Class PersTempPersonServiceImpl method auditPerson", e);
                }
            }
        } else {
            imgBase64 = FileEncryptUtil.getDecryptFileBase64(pathUrl);
        }
        return imgBase64;
    }

    @Override
    public PersTempPersonItem getItemByMobile(String mobile) {
        PersTempPersonItem persTempPersonItem = null;
        PersTempPerson persTempPerson = persTempPersonDao.findByMobilePhone(mobile);
        if (persTempPerson != null) {
            persTempPersonItem = new PersTempPersonItem();
            ModelUtil.copyProperties(persTempPerson, persTempPersonItem);
        }
        return persTempPersonItem;
    }

    @Override
    public PersTempPersonItem getItemByEmail(String email) {
        PersTempPersonItem persTempPersonItem = null;
        PersTempPerson persTempPerson = persTempPersonDao.findByEmail(email);
        if (persTempPerson != null) {
            persTempPersonItem = new PersTempPersonItem();
            ModelUtil.copyProperties(persTempPerson, persTempPersonItem);
        }
        return persTempPersonItem;
    }

    @Override
    public PersTempPersonItem getItemByPin(String pin) {
        PersTempPersonItem persTempPersonItem = null;
        PersTempPerson persTempPerson = persTempPersonDao.findByPin(pin);
        if (persTempPerson != null) {
            persTempPersonItem = new PersTempPersonItem();
            ModelUtil.copyProperties(persTempPerson, persTempPersonItem);
            return persTempPersonItem;
        }
        return null;
    }

    @Override
    public ZKResultMsg syncPersonFromCloud(List<PersTempPersonItem> persTempPersonItems) {
        if (!CollectionUtil.isEmpty(persTempPersonItems)) {
            List<String> pinList =
                (List<String>)CollectionUtil.getPropertyList(persTempPersonItems, PersTempPersonItem::getPin, "-1");
            List<String> deptCodeList = persTempPersonItems.stream().map(PersTempPersonItem::getDeptCode)
                .filter(code -> StringUtils.isNotBlank(code)).distinct().collect(Collectors.toList());
            // 处理人员职位数据
            List<String> positionCodeList = persTempPersonItems.stream().map(PersTempPersonItem::getPositionCode)
                .filter(code -> StringUtils.isNotBlank(code)).distinct().collect(Collectors.toList());

            List<PersPosition> persPositionItemList = new ArrayList<>();
            if (!CollectionUtil.isEmpty(pinList)) {
                List<PersTempPerson> persTempPersonList = persTempPersonDao.findByPinIn(pinList);
                Map<String, PersPosition> persPositionItemMap = new HashMap<>();
                if (!CollectionUtil.isEmpty(positionCodeList)) {
                    persPositionItemList = persPositionDao.findByCodeIn(positionCodeList);
                    persPositionItemMap = CollectionUtil.listToKeyMap(persPositionItemList, PersPosition::getCode);
                }
                List<PersPerson> persPersonList = persPersonDao.findByPinIn(pinList);
                Map<String, PersTempPerson> persTempPersonMap =
                    CollectionUtil.listToKeyMap(persTempPersonList, PersTempPerson::getPin);
                Map<String, PersPerson> persPersonMap = CollectionUtil.listToKeyMap(persPersonList, PersPerson::getPin);
                if (!deptCodeList.contains("1")) {
                    // 判断是否存在默认部门编号，不存在则设置默认部门编号
                    deptCodeList.add("1");
                }
                AuthDepartmentItem authDepartmentItem = new AuthDepartmentItem();
                authDepartmentItem.setInCode(StringUtils.join(deptCodeList, ","));
                List<AuthDepartmentItem> authDepartmentItems = authDepartmentService.getByCondition(authDepartmentItem);
                Map<String, AuthDepartmentItem> authDepartmentItemMap =
                    CollectionUtil.listToKeyMap(authDepartmentItems, AuthDepartmentItem::getCode);
                boolean isAudit = "1".equals(baseSysParamService.getValByName("pers.tempPerson.audit"));
                PersTempPerson tempPerson = null;
                for (PersTempPersonItem item : persTempPersonItems) {
                    try {
                        tempPerson = persTempPersonMap.get(item.getPin());
                        if (tempPerson == null) {
                            tempPerson = new PersTempPerson();
                        }
                        ModelUtil.copyPropertiesIgnoreNullWithProperties(item, tempPerson, "id", "cardNos", "devicePwd",
                            "certNumber", "accLevelIds");
                        tempPerson.setCompanyName(StringUtils.isNotBlank(item.getRemrak()) ? item.getRemrak() : "");
                        AuthDepartmentItem deptItem = authDepartmentItemMap.get(item.getDeptCode());
                        if (deptItem == null) {
                            deptItem = authDepartmentItemMap.get("1");
                        } // 处理部门
                        tempPerson.setDeptId(deptItem.getId());

                        // 需要对传过来的照片进行处理
                        String imgBase64 = "";
                        // 兼容以前的.jpg的照片
                        if (StringUtils.isNotBlank(item.getPhotoPath()) && item.getPhotoPath().startsWith("http")) {
                            imgBase64 = getPhotoBase64(item.getPhotoPath());
                        } else if (StringUtils.isNotBlank(item.getCloudImgBase64())) {
                            imgBase64 = item.getCloudImgBase64();
                        }
                        // 判断人员是否有照片，有则组装并放入待下发人员照片信息集合里
                        if (StringUtils.isNotBlank(item.getPhotoPath())) {
                            if (StringUtils.isBlank(imgBase64)) {
                                imgBase64 = getPhotoBase64(item.getPhotoPath());
                            }
                            if (StringUtils.isNotBlank(imgBase64)) {
                                String photoPath =
                                    FileUtil.saveFileToServer("pers", "tempPerson", item.getPin() + ".jpg", imgBase64);
                                item.setPhotoPath(photoPath);
                                tempPerson.setPhotoPath(photoPath);
                            }
                        }
                        // 需要对传过来的抠图照片进行处理
                        String imgCropBase64 = "";
                        // 兼容以前的.jpg的照片
                        if (StringUtils.isNotBlank(item.getCropPhotoPath())
                            && item.getCropPhotoPath().startsWith("http")) {
                            imgCropBase64 = getPhotoBase64(item.getCropPhotoPath());
                        } else if (StringUtils.isNotBlank(item.getCloudCropImgBase64())) {
                            imgCropBase64 = item.getCloudCropImgBase64();
                        }
                        if (StringUtils.isNotBlank(item.getCropPhotoPath())) {
                            if (StringUtils.isBlank(imgCropBase64)) {
                                imgCropBase64 = getPhotoBase64(item.getCropPhotoPath());
                            }
                            if (StringUtils.isNotBlank(imgCropBase64)) {
                                String fileName = item.getPin() + ".jpg";
                                String filePath = FileUtil.getCropFacePath("pers/tempPerson", item.getPin());
                                FileUtil.saveFile(filePath, fileName, imgCropBase64, false);
                                item.setCropPhotoPath("/" + filePath + "/" + fileName);
                                tempPerson.setCropPhotoPath("/" + filePath + "/" + fileName);
                            }
                        }
                        // 图片加密
                        if (StringUtils.isNotBlank(item.getPhotoPath())) {
                            PersPersonUtil.encryptPersonPhoto(item.getPhotoPath(), item.getPin(), "pers/tempPerson");
                        }
                        if (isAudit) {
                            // 自动审核
                            auditSyncPerson(item, persPersonMap, authDepartmentItemMap, persPositionItemMap);
                            tempPerson.setStatus((short)1);
                        } else {
                            tempPerson.setStatus((short)0);
                        }
                        persTempPersonDao.save(tempPerson);
                    } catch (Exception e) {

                    }
                }
            }
        }
        return ZKResultMsg.successMsg();
    }

    private void auditSyncPerson(PersTempPersonItem item, Map<String, PersPerson> persPersonMap,
        Map<String, AuthDepartmentItem> authDepartmentItemMap, Map<String, PersPosition> persPositionMap) {
        PersPerson persPerson = persPersonMap.get(item.getPin());
        AuthDepartmentItem authDepartmentItem = authDepartmentItemMap.get(item.getDeptCode());
        if (authDepartmentItem == null) {
            authDepartmentItem = authDepartmentItemMap.get("1");
        }
        if (persPerson == null) {
            persPerson = new PersPerson();
            persPerson.setPin(item.getPin());
            // ModelUtil.copyPropertiesIgnoreNullWithProperties(item, persPerson,"id", "photoPath", "personPwd");
            persPerson.setStatus(PersConstants.PERSON_NORMAL);
            persPerson.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
            // 是否包含字母
            if (item.getPin().matches(".*[a-zA-Z]+.*")) {
                persPerson.setPinLetter(true);
            } else {
                persPerson.setPinLetter(false);
            }
            persPerson.setExceptionFlag(PersConstants.PERSON_NORMAL);
        }

        if (StringUtils.isNotBlank(item.getPersonPwd())) {
            // 设置云上密码为员工自助密码，不是门禁密码
            persPerson.setSelfPwd(item.getPersonPwd());
        }
        // 处理人员职位
        if (persPositionMap != null && !persPositionMap.isEmpty()) {
            PersPosition persPosition = persPositionMap.get(item.getPositionCode());
            if (persPosition != null) {
                persPerson.setPersPosition(persPosition);
            }
        }
        // 处理人员部门
        persPerson.setDeptId(authDepartmentItem.getId());
        // 姓名
        if (StringUtils.isNotBlank(item.getName())) {
            persPerson.setName(item.getName());
        }
        // 性别
        if (StringUtils.isNotBlank(item.getGender())) {
            persPerson.setGender(item.getGender());
        }
        // 手机号
        if (StringUtils.isNotBlank(item.getMobilePhone())) {
            persPerson.setMobilePhone(item.getMobilePhone());
        }
        // 邮箱
        if (StringUtils.isNotBlank(item.getEmail())) {
            persPerson.setEmail(item.getEmail());
        }
        // 出生日期
        persPerson.setBirthday(item.getBirthday());
        // 入职日期
        persPerson.setHireDate(item.getHireDate());
        // 判断人员是否有照片，有则组装并放入待下发人员照片信息集合里
        if (StringUtils.isNotBlank(item.getPhotoPath())) {
            String imgBase64 = getPhotoBase64(item.getPhotoPath());
            if (StringUtils.isNotBlank(imgBase64)) {
                String photoPath = FileUtil.saveFileToServer("pers", "user/avatar", item.getPin() + ".jpg", imgBase64);
                persPerson.setPhotoPath(photoPath);
            }
        }
        if (StringUtils.isNotBlank(item.getCropPhotoPath())) {
            String imgBase64 = getPhotoBase64(item.getCropPhotoPath());
            if (StringUtils.isNotBlank(imgBase64)) {
                FileUtil.saveCropFaceToServer(item.getPin(), item.getPin() + ".jpg", imgBase64);
            }
        }

        // 修改同步云端从应用人员数据，启禁用没有默认值
        if (persPerson.getEnabledCredential() == null) {
            persPerson.setEnabledCredential(true);
        }
        persPerson = persPersonDao.save(persPerson);
        // 图片加密
        PersPersonUtil.encryptPersonPhoto(persPerson.getPhotoPath(), persPerson.getPin());
        if (FileUtil.fileExists(item.getCropPhotoPath()) && StringUtils.isNotBlank(item.getCropPhotoPath())) {
            // 更新人员抠图信息
            PersBioPhotoItem persBioPhotoItem = new PersBioPhotoItem();
            persBioPhotoItem.setPersonId(persPerson.getId());
            persBioPhotoItem.setPersonPin(persPerson.getPin());
            persBioPhotoItem.setBioType(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
            String cropFacePath =
                FileUtil.getCropFacePath(persPerson.getPin()) + FileUtil.separator + persPerson.getPin() + ".jpg";
            persBioPhotoItem.setPhotoPath(cropFacePath);
            persBioPhotoService.saveItemByPersonId(persBioPhotoItem);
        }
    }

    @Override
    public List<PersTempPersonItem> getItemByPins(List<String> pinList) {
        List<PersTempPersonItem> persTempPersonItemList = new ArrayList<>();
        List<PersTempPerson> persTempPersonList = persTempPersonDao.findByPinIn(pinList);
        if (!persTempPersonList.isEmpty()) {
            persTempPersonItemList = ModelUtil.copyListProperties(persTempPersonList, PersTempPersonItem.class);
        }
        return persTempPersonItemList;
    }

    @Override
    public List<PersTempPersonItem> protectPin(List<PersTempPersonItem> items) {
        if (persPersonService.isProtectData()) {
            items.forEach(item -> {
                String pin = item.getPin();
                item.setPin(PersRegularUtil.hideWithAsterisk(pin));
            });
        }
        return items;
    }
}
