/*
 * File Name: PersTempPerson <NAME_EMAIL> on 2018/9/26 10:18. Copyright:Copyright © 1999-2018 ZKTeco
 * Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "PERS_TEMP_PERSON ")
public class PersTempPerson extends BaseModel implements Serializable {
    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50)
    private String deptId;

    /**
     * 人员编号
     */
    @Column(name = "PIN", length = 30)
    private String pin;

    /**
     * 名字
     */
    @Column(name = "NAME", length = 50)
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "LAST_NAME", length = 50)
    private String lastName;

    /**
     * 性别
     */
    @Column(name = "GENDER", length = 1)
    private String gender;

    /**
     * 照片路径
     */
    @Column(name = "PHOTO_PATH", length = 200)
    private String photoPath;

    /**
     * 审核状态
     */
    @Column(name = "STATUS")
    private Short status;

    /**
     * 联系电话
     */
    @Column(name = "MOBILE_PHONE", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String mobilePhone;

    /**
     * 人员密码
     */
    @Column(name = "PERSON_PWD", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String personPwd;

    /**
     * 人员来源
     */
    @Column(name = "IS_FROM", length = 100, updatable = false)
    private String isFrom;

    @Column(name = "CROP_PHOTO_PATH")
    private String cropPhotoPath;

    /**
     * 证件类型
     */
    @Column(name = "CERT_TYPE")
    private String certType;

    /**
     * 证件号码
     */
    @Column(name = "CERT_NUMBER")
    @Convert(converter = EncryptConverter.class)
    private String certNumber;

    /**
     * 邮箱
     */
    @Column(name = "EMAIL")
    @Convert(converter = EncryptConverter.class)
    private String email;

    /**
     * 地址
     */
    @Column(name = "FAMILY_ADDRESS")
    @Convert(converter = EncryptConverter.class)
    private String familyAddress;

    /**
     * 出生日期
     */
    @Column(name = "BIRTHDAY")
    private Date birthday;

    /**
     * 门禁权限组ids
     */
    @Column(name = "ACC_LEVEL_IDS")
    private String accLevelIds;

    /**
     * 人员卡号
     */
    @Column(name = "CARD_NOS")
    @Convert(converter = EncryptConverter.class)
    private String cardNos;

    /*
     *公司名称
     */
    @Column(name = "COMPANY_NAME", length = 100)
    private String companyName;

    /**
     * 职位编号
     */
    @Column(name = "POSITION_CODE")
    private String positionCode;

    /**
     * 入职时间
     */
    @Column(name = "HIRE_DATE")
    @Temporal(TemporalType.DATE)
    private Date hireDate;

    /**
     * 人员设备验证密码
     */
    @Column(name = "DEVICE_PWD", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String devicePwd;

    /**
     * 标识是否开通移动端
     */
    @Column(name = "EXISTS_MOBILE_USER")
    private Boolean existsMobileUser;

    /**
     * 表与表关系
     **/
    public PersTempPerson() {
        super();
    }

    public PersTempPerson(String id) {
        super();
        this.id = id;
    }
}
