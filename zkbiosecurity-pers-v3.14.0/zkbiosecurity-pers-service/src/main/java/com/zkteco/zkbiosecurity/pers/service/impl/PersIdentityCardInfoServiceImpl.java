/**
 * File Name: PersIdentityCardInfoServiceImpl Created by GenerationTools on 2018-03-14 上午10:10 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersIdentityCardInfoDao;
import com.zkteco.zkbiosecurity.pers.model.PersIdentityCardInfo;
import com.zkteco.zkbiosecurity.pers.service.PersIdentityCardInfoService;
import com.zkteco.zkbiosecurity.pers.vo.PersIdentityCardInfoItem;

/**
 * 对应百傲瑞达 PersIdentityCardInfoServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 上午10:10
 * @version v1.0
 */
@Service
@Transactional
public class PersIdentityCardInfoServiceImpl implements PersIdentityCardInfoService {
    @Autowired
    private PersIdentityCardInfoDao persIdentityCardInfoDao;
    /** 是否开启数据加密 默认不开启 */
    @Value("${system.data.security-encrypt:false}")
    private boolean isOpenSecurityEncrypt;

    @Override
    public PersIdentityCardInfoItem saveItem(PersIdentityCardInfoItem item) {
        PersIdentityCardInfo persIdentityCardInfo =
            Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
                .flatMap(id -> persIdentityCardInfoDao.findById(id)).orElse(new PersIdentityCardInfo());
        ModelUtil.copyProperties(item, persIdentityCardInfo);
        persIdentityCardInfoDao.save(persIdentityCardInfo);
        item.setId(persIdentityCardInfo.getId());
        return item;
    }

    @Override
    public List<PersIdentityCardInfoItem> getByCondition(PersIdentityCardInfoItem condition) {
        return (List<PersIdentityCardInfoItem>)persIdentityCardInfoDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persIdentityCardInfoDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                persIdentityCardInfoDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public PersIdentityCardInfoItem getItemById(String id) {
        List<PersIdentityCardInfoItem> items = getByCondition(new PersIdentityCardInfoItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public PersIdentityCardInfoItem findByPhysicalNo(String physicalNo) {
        return Optional.ofNullable(physicalNo).filter(StringUtils::isNotBlank)
            .map(persIdentityCardInfoDao::findByPhysicalNo).filter(Objects::nonNull)
            .map(info -> ModelUtil.copyProperties(info, new PersIdentityCardInfoItem())).orElse(null);
    }

    @Override
    public PersIdentityCardInfoItem findByIdCard(String idCard) {
        return Optional.ofNullable(idCard).filter(StringUtils::isNotBlank).map(persIdentityCardInfoDao::findByIdCard)
            .filter(Objects::nonNull).map(info -> ModelUtil.copyProperties(info, new PersIdentityCardInfoItem()))
            .orElse(null);
    }

    @Override
    public void handlerTransfer(List<PersIdentityCardInfoItem> ideintityCardInfoItems) {
        ideintityCardInfoItems.forEach(persIdentityCardInfoItem -> {
            persIdentityCardInfoDao
                .save(ModelUtil.copyProperties(persIdentityCardInfoItem, new PersIdentityCardInfo()));
        });
    }

    @Override
    public void encryptPersIdentityCardInfo() {
        if (isOpenSecurityEncrypt) {
            List<PersIdentityCardInfo> persIdentityCardInfoList = persIdentityCardInfoDao.findAll();
            for (PersIdentityCardInfo persIdentityCardInfo : persIdentityCardInfoList) {
                persIdentityCardInfo.setIdCard(FoldexUtil.encryptByRandomSey(persIdentityCardInfo.getIdCard()));
                if (StringUtils.isNotBlank(persIdentityCardInfo.getPhysicalNo())) {
                    persIdentityCardInfo
                        .setPhysicalNo(FoldexUtil.encryptByRandomSey(persIdentityCardInfo.getPhysicalNo()));
                }
                if (StringUtils.isNotBlank(persIdentityCardInfo.getAddress())) {
                    persIdentityCardInfo.setAddress(FoldexUtil.encryptByRandomSey(persIdentityCardInfo.getAddress()));
                }
            }
            if (persIdentityCardInfoList.size() > 0) {
                List<List<PersIdentityCardInfo>> identityCardInfoList =
                    CollectionUtil.split(persIdentityCardInfoList, CollectionUtil.splitSize);
                identityCardInfoList.forEach(identityCardInfos -> {
                    persIdentityCardInfoDao.saveAll(identityCardInfos);
                });
            }
        }
    }
}