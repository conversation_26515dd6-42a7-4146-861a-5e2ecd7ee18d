/**
 * File Name: PersPersonChange
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 PersPersonChange
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_PERSONCHANGE")
@Getter
@Setter
@Accessors(chain = true)
public class PersPersonChange extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @ManyToOne
    @JoinColumn(name = "PERSON_ID")
    private PersPerson persPerson;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 100)
    private String deptId;

    /**
     * 修改原因
     */
    @Column(name = "CHANGE_REASON", length = 100)
    private String changeReason;
}