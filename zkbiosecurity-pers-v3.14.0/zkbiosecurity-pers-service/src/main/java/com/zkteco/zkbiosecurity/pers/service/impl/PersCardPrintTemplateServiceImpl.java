/**
 * File Name: PersCardPrintTemplateServiceImpl Created by GenerationTools on 2018-03-14 上午10:10 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeExtItem;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.BasePrintTemplateService;
import com.zkteco.zkbiosecurity.system.vo.BasePrintTemplateItem;

/**
 * 对应百傲瑞达 PersCardPrintTemplateServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-14 上午10:10
 */
@Service
@Transactional
public class PersCardPrintTemplateServiceImpl implements PersCardPrintTemplateService {
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private BasePrintTemplateService basePrintTemplateService;
    @Autowired
    private PersAttributeExtService persAttributeExtService;
    @Autowired
    private PersAttributeService persAttributeService;
    @Autowired
    private PersSupportFuncService persSupportFuncService;

    @Override
    public Map<String, String> getPrintTemplate(String templateId) {
        BasePrintTemplateItem basePrintTemplateItem = basePrintTemplateService.getItemById(templateId);

        if (basePrintTemplateItem != null) {
            Map<String, String> templateMap = new HashMap<>();
            templateMap.put("width", basePrintTemplateItem.getWidth().toString());
            templateMap.put("height", basePrintTemplateItem.getHeight().toString());
            templateMap.put("frontData", basePrintTemplateItem.getFrontData());
            templateMap.put("oppositeData", basePrintTemplateItem.getOppositeData());
            return templateMap;
        }
        return null;
    }

    /**
     * 获得用于预览的人员
     * 
     * <AUTHOR>
     * @since 2017年9月19日 下午4:41:26
     * @return
     */
    @Override
    public Map<String, String> getPersonData(String personId) {
        PersPersonItem persPersonItem = persPersonService.getItemById(personId);
        Map<String, String> personMap = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String perName = persPersonItem.getName() == null ? "" : persPersonItem.getName();
        String perLastName = persPersonItem.getLastName() == null ? "" : persPersonItem.getLastName();
        personMap.put("printData.person.name", perName + " " + perLastName);
        personMap.put("printData.person.pin", persPersonItem.getPin() == null ? "" : persPersonItem.getPin());
        personMap.put("printData.person.photo",
            persPersonItem.getPhotoPath() == null ? "/images/userImage.gif" : persPersonItem.getPhotoPath());
        personMap.put("printData.person.entryDate", sdf.format(persPersonItem.getCreateTime()));
        personMap.put("printData.person.department",
            persPersonItem.getDeptName() == null ? "" : persPersonItem.getDeptName());
        personMap.put("printData.person.mobilephone",
            persPersonItem.getMobilePhone() == null ? "" : persPersonItem.getMobilePhone());
        String gender = persPersonItem.getGender() == null ? "" : persPersonItem.getGender();
        if ("M".equals(gender)) {
            gender = I18nUtil.i18nCode("pers_person_male");
        } else if ("F".equals(gender)) {
            gender = I18nUtil.i18nCode("pers_person_female");
        }
        personMap.put("printData.person.gender", gender);
        personMap.put("printData.person.email", persPersonItem.getEmail() == null ? "" : persPersonItem.getEmail());

        personMap.put("printData.person.birthday",
            persPersonItem.getBirthday() == null ? "" : persPersonItem.getBirthday().toString());
        personMap.put("printData.person.cardNo",
            persPersonItem.getCardNos() == null ? "" : persPersonItem.getCardNos());
        personMap.put("printData.person.positionName",
            persPersonItem.getPositionName() == null ? "" : persPersonItem.getPositionName());

        PersAttributeExtItem extItem = persAttributeExtService.getItemByPersonId(personId);
        Map attr = null;
        if (Objects.nonNull(extItem)) {
            attr = BeanMap.create(extItem);
        }

        List<PersAttributeItem> attributes = persAttributeService.getByCondition(new PersAttributeItem());
        for (PersAttributeItem item : attributes) {
            if (Objects.nonNull(attr)) {
                personMap.put("printData.person.attrValue" + item.getFiledIndex(),
                    MapUtils.getString(attr, "attrValue" + item.getFiledIndex(), ""));
            }
        }

        return personMap;
    }

    @Override
    public Boolean isShowCardPrint() {
        return persSupportFuncService.isSupportPrintTemplate()
            && baseLicenseProvider.licenseAvaiable(ConstUtil.SYSTEM_MODULE_PRINT_CARD);
    }
}