package com.zkteco.zkbiosecurity.pers.data.upgrade;

import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class PersVer3_14_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Value("${system.language:zh_CN}")
    private String language;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getVersion() {
        return "v3.14.0";
    }

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public boolean executeUpgrade() {
        /** --------------------------- 同步人员到ACMS --------------------------- */
        if (!"zh_CN".equals(language)) {
            AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("PersPerson");
            if (subMenuItem != null) {
                AuthPermissionItem subButtonItem = new AuthPermissionItem("PersPersonSyncAcms", "pers_person_syncAcms",
                    "pers:person:syncAcms", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 26);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
            }
        }
        // 人脸后台比对服务器升级
        baseSysParamService.initData(new BaseSysParamItem("pers.faceVerify.mode", "0", "启用人脸后台比对"));
        baseSysParamService.initData(new BaseSysParamItem("pers.faceVerify.serverAddr", "", "人脸后台比对服务地址"));
        baseSysParamService.initData(new BaseSysParamItem("pers.faceVerify.serverSecret", "", "人脸后台比对服务秘钥"));
        return true;
    }
}
