package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonnallistPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersPersonnallistPerson;
import com.zkteco.zkbiosecurity.pers.service.PersPersonnallistPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnallistPersonItem;

/**
 * 名单库人员接口实现
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2020-07-22 16:35:16
 */
@Service
@Transactional
public class PersPersonnalListPersonServiceImpl implements PersPersonnallistPersonService {
    @Autowired
    private PersPersonnallistPersonDao persPersonnallistPersonDao;

    @Override
    public PersPersonnallistPersonItem saveItem(PersPersonnallistPersonItem item) {
        PersPersonnallistPerson persPersonnallistPerson =
            Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
                .flatMap(persPersonnallistPersonDao::findById).orElse(new PersPersonnallistPerson());
        ModelUtil.copyPropertiesIgnoreNull(item, persPersonnallistPerson);
        persPersonnallistPersonDao.save(persPersonnallistPerson);
        // 数据直接入库生效，以防下发名单库时查询人员无法查询到当前保存人员
        persPersonnallistPersonDao.flush();
        item.setId(persPersonnallistPerson.getId());
        return item;
    }

    @Override
    public List<PersPersonnallistPersonItem> getByCondition(PersPersonnallistPersonItem condition) {
        return (List<PersPersonnallistPersonItem>)persPersonnallistPersonDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public List<String> findPersonIdByPersonnallistId(String personnallistId) {
        return persPersonnallistPersonDao.findPersonIdByPersonnallistId(personnallistId);
    }

    @Override
    public void updatePersonListIdByPersonId(String personId, String oldPersonnelListId, String newPersonnelListId) {
        persPersonnallistPersonDao.updatePersonListIdByPersonId(personId, oldPersonnelListId, newPersonnelListId);
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persPersonnallistPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page,
            size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            Collection<String> list = CollectionUtil.strToList(ids);
            list.forEach(id -> {
                persPersonnallistPersonDao.deleteById(id);
            });
        }
        return false;
    }

    @Override
    public void batchSave(List<PersPersonnallistPersonItem> itemList) {
        List<List<PersPersonnallistPersonItem>> split = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        split.forEach(listPersonItemList -> {
            Collection<String> personPins =
                CollectionUtil.getPropertyList(listPersonItemList, PersPersonnallistPersonItem::getPersonPin, "-1");
            List<PersPersonnallistPerson> listPersonList = persPersonnallistPersonDao.findByPersonPinIn(personPins);
            if (!listPersonList.isEmpty()) {
                Map<String, PersPersonnallistPersonItem> itemMap = CollectionUtil.itemListToIdMap(listPersonItemList);
                listPersonList.forEach(listPerson -> {
                    PersPersonnallistPersonItem item = itemMap.get(listPerson.getId());
                    if (Objects.nonNull(item)) {
                        ModelUtil.copyProperties(item, listPerson);
                    }
                });
                persPersonnallistPersonDao.saveAll(listPersonList);
            }
        });
    }
}
