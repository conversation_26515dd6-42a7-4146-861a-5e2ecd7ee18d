package com.zkteco.zkbiosecurity.pers.utils;

import com.zkteco.zkbiosecurity.base.bean.ProcessBean;

/**
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 14:57 2019/1/4
 */
public class ProcessBeanUtil
{
    public static ProcessBean createNormalSingleProcess(int currentProgress, String progressContent) {
        return new ProcessBean(currentProgress, currentProgress, progressContent+"<br/>");
    }

    public static ProcessBean createWarningSingleProcess(int currentProgress, String progressContent) {
        return new ProcessBean(currentProgress, currentProgress, "<font color='#ebdd37'>"+progressContent+"</font><br/>");
    }

    public static ProcessBean createErrorSingleProcess(int currentProgress, String progressContent) {
        return new ProcessBean(currentProgress, currentProgress, "<font color='red'>"+progressContent+"</font><br/>");
    }
}
