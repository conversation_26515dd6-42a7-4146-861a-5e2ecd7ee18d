package com.zkteco.zkbiosecurity.pers.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.config.RestConfig;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersFaceVerifyService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.utils.SystemHttpUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Service
public class PersFaceVerifyServiceImpl implements PersFaceVerifyService {
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private RestTemplate restTemplate = RestConfig.getRestTemplate(5 * 1000, 5 * 1000);

    @Override
    public ZKResultMsg testFaceVerifyServerStatus() {
        String address = baseSysParamService.getValByName("pers.faceVerify.serverAddr");
        String secret = baseSysParamService.getValByName("pers.faceVerify.serverSecret");
        if (StringUtils.isNotBlank(address) && StringUtils.isNotBlank(secret)) {
            String url = address + "/version";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setConnection("keep-alive");
            headers.setBearerAuth(secret);
            HttpEntity request = new HttpEntity(headers);
            String response = restTemplate.postForObject(url, request, String.class);
            JSONObject resultData = JSONObject.parseObject(response);
            int status = Integer.parseInt(resultData.get("code").toString());
            if (status >= 0) {
                return new ZKResultMsg();
            }
        }
        return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_prompt_serverFailed"));
    }

    @Override
    public ZKResultMsg addFace2Server(String pin, String cropFacePath, boolean isAllowList) {
        String address = baseSysParamService.getValByName("pers.faceVerify.serverAddr");
        String secret = baseSysParamService.getValByName("pers.faceVerify.serverSecret");
        if (StringUtils.isNotBlank(address) && StringUtils.isNotBlank(secret)) {
            String url = address + "/enroll_img_white_json";
            if (!isAllowList) {
                url = address + "/enroll_img_black_json";
            }
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setConnection("keep-alive");
            headers.setBearerAuth(secret);
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("id", pin);
            requestData.put("type", "face");
            requestData.put("b64image", FileEncryptUtil.getDecryptFileBase64(cropFacePath));
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestData, headers);
            String response = restTemplate.postForObject(url, request, String.class);
            JSONObject resultData = JSONObject.parseObject(response);
            int status = Integer.parseInt(resultData.get("code").toString());
            if (status >= 0) {
                return new ZKResultMsg();
            }
        }
        return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_prompt_serverFailed"));
    }

    @Override
    public ZKResultMsg delFaceFromServer(String pin, boolean isAllowList) {
        String address = baseSysParamService.getValByName("pers.faceVerify.serverAddr");
        String secret = baseSysParamService.getValByName("pers.faceVerify.serverSecret");
        if (StringUtils.isNotBlank(address) && StringUtils.isNotBlank(secret)) {
            String url = address + "/delete_white";
            if (!isAllowList) {
                url = address + "/delete_black";
            }
            url += "?id=" + pin + "&type=face";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setConnection("keep-alive");
            headers.setBearerAuth(secret);
            HttpEntity request = new HttpEntity(headers);
            String response = restTemplate.postForObject(url, request, String.class);
            JSONObject resultData = JSONObject.parseObject(response);
            int status = Integer.parseInt(resultData.get("code").toString());
            if (status >= 0) {
                return new ZKResultMsg();
            }
        }
        return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_prompt_serverFailed"));
    }

    @Override
    public boolean getFaceVerifyEnable() {
        return "1".equals(baseSysParamService.getValByName("pers.faceVerify.mode"));
    }
}
