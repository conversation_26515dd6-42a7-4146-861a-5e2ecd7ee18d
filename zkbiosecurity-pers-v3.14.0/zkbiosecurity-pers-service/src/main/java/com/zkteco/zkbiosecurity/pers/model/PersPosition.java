/**
 * File Name: PersPosition
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 对应百傲瑞达实体 PersPosition
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_POSITION")
@Getter
@Setter
@Accessors(chain = true)
public class PersPosition extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 父节点id
     */
    @ManyToOne
    @JoinColumn(name = "PARENT_ID")
    private PersPosition parent;

    /**
     * 职位名称
     */
    @Column(name = "NAME", length = 100, nullable = false)
    private String name;

    /**
     * 职位编号
     */
    @Column(name = "CODE", length = 30, nullable = false)
    private String code;

    /**
     * 排序编号
     */
    @Column(name = "SORT")
    private Integer sortNo;

    /**  */
    @OneToMany(mappedBy = "parent")
    private List<PersPosition> children = new ArrayList<>();

    /**  */
    @OneToMany(mappedBy = "persPosition")
    private List<PersPerson> persPersonList = new ArrayList<>();
}