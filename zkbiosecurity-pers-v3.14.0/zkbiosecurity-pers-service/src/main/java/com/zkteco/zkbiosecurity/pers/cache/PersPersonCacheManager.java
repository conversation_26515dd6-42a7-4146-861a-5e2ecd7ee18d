package com.zkteco.zkbiosecurity.pers.cache;

import java.util.*;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.core.model.BaseModel;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersLeavePersonDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersLeavePerson;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheExtParamsService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 人员信息保存切面，保存到redis
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/9/14 12:01
 * @since 1.0.0
 */
@Slf4j
@Component
public class PersPersonCacheManager {

    public static final String PERSON_CACHE_KEY = "pers:person:";

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersLeavePersonDao persLeavePersonDao;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired(required = false)
    private PersPersonCacheExtParamsService[] persPersonCacheExtParamsServices;

    /**
     * 更新缓存信息（部门名称）
     *
     * @param personCacheItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/8/22 10:19
     * @since 1.0.0
     */
    public void updatePersonCacheItem(PersPersonCacheItem personCacheItem) {
        try {
            updatePersonCacheItem(personCacheItem, 0);
        } catch (Exception e) {
            log.error("updatePersonCacheItem error", e);
        }
    }

    /**
     * 切面保存人员时，更新人员信息到缓存
     * 
     * @param entity:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 12:06
     * @since 1.0.0
     */
    public void updatePersonCache(Object entity) {
        try {
            if ((entity instanceof BaseModel)) {
                PersPersonCacheItem personCacheItem = personToCacheItem((PersPerson)entity);
                updatePersonCacheItem(personCacheItem, 0);
            } else if (entity instanceof Iterable) {
                List<PersPersonCacheItem> personCacheItemList = personListToCacheItemList((List<PersPerson>)entity);
                updatePersonCacheItemList(personCacheItemList);
            }
        } catch (Exception e) {
            log.error("updatePersonCache error", e);
        }
    }

    /**
     * 切面删除人员时，删除缓存中的人员信息
     * 
     * @param entity:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:44
     * @since 1.0.0
     */
    public void deletePersonCache(Object entity) {
        try {
            if ((entity instanceof BaseModel)) {
                PersPerson persPerson = (PersPerson)entity;
                deletePersonCacheItem(persPerson.getPin());
            } else if (entity instanceof Iterable) {
                List<PersPerson> persPersonList = (List<PersPerson>)entity;
                Collection<String> pinList = CollectionUtil.getPropertyList(persPersonList, PersPerson::getPin, "-1");
                deletePersonCacheItemList(pinList);
            }
        } catch (Exception e) {
            log.error("deletePersonCache error", e);
        }
    }

    /**
     * 更新人员模块附加参数信息到缓存
     * 
     * @param module:
     * @param pinExtParamsMap:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:44
     * @since 1.0.0
     */
    public void updatePersonCacheExtParamsMap(String module, Map<String, Map<String, Object>> pinExtParamsMap) {
        Set<String> pingSet = pinExtParamsMap.keySet();
        List<PersPersonCacheItem> personCacheItemList = getPersonCacheByPins(new ArrayList<>(pingSet));
        if (personCacheItemList != null && personCacheItemList.size() > 0) {
            for (PersPersonCacheItem personCacheItem : personCacheItemList) {
                Map<String, Object> extParamsMap = pinExtParamsMap.get(personCacheItem.getPin());

                Map<String, Map<String, Object>> otherExtParams = personCacheItem.getExtParams();
                otherExtParams.put(module, extParamsMap);
                personCacheItem.setExtParams(otherExtParams);
            }
            updatePersonCacheItemList(personCacheItemList);
        }
    }

    /**
     * 获取缓存中的人员信息，如果缓存不存在，查询数据库，并且更新到缓存
     *
     * @param pin:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:45
     * @since 1.0.0
     */
    public PersPersonCacheItem getPersonCacheByPin(String pin) {
        PersPersonCacheItem personCacheItem =
            (PersPersonCacheItem)redisTemplate.opsForValue().get(PERSON_CACHE_KEY + pin);

        // 缓存获取不到数据，查询数据库、获取其他模块的附加属性，并且更新到缓存
        if (personCacheItem == null) {
            // 查询人员表
            PersPerson persPerson = persPersonDao.findByPin(pin);
            if (persPerson != null) {
                personCacheItem = personToCacheItem(persPerson);
                // 获取其他模块的附加属性
                buildExtParams(personCacheItem);
                // 保存到缓存
                updatePersonCacheItem(personCacheItem, 0);

            } else {
                // 查询离职人员
                PersLeavePerson persLeavePerson = persLeavePersonDao.findByPin(pin);
                if (persLeavePerson != null) {
                    personCacheItem = leavePersonToCacheItem(persLeavePerson);
                    // 获取其他模块的附加属性
                    buildExtParams(personCacheItem);
                    // 保存到缓存
                    updatePersonCacheItem(personCacheItem, 24);
                }
            }
        } else if (personCacheItem.getEnabledCredential() == null) {
            personCacheItem.setEnabledCredential(true);
        }
        return personCacheItem;
    }

    /**
     * 获取缓存中的人员信息过滤已离职人员，如果缓存不存在，查询数据库，并且更新到缓存
     *
     * @param pin:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:45
     * @since 1.0.0
     */
    public PersPersonCacheItem getPersonCacheFilterLeaveByPin(String pin) {
        PersPersonCacheItem personCacheItem = getPersonCacheByPin(pin);
        // 离职人员
        if (personCacheItem != null && personCacheItem.getLeaveDate() != null) {
            return null;
        }
        return personCacheItem;
    }

    /**
     * 获取其他模块的附加属性
     *
     * @param personCacheItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 16:35
     * @since 1.0.0
     */
    private void buildExtParams(PersPersonCacheItem personCacheItem) {
        if (persPersonCacheExtParamsServices != null && personCacheItem != null) {
            String pin = personCacheItem.getPin();
            for (PersPersonCacheExtParamsService personCacheExtParamsService : persPersonCacheExtParamsServices) {
                Map<String, Map<String, Object>> extParamsMap =
                    personCacheExtParamsService.getExtParamsMapByPins(Arrays.asList(pin));
                if (extParamsMap != null && extParamsMap.containsKey(pin)) {
                    Map<String, Map<String, Object>> extParams = personCacheItem.getExtParams();
                    String module = personCacheExtParamsService.getModule();
                    extParams.put(module, extParamsMap.get(pin));
                    personCacheItem.setExtParams(extParams);
                }
            }
        }
    }

    /**
     * 获取缓存中的人员信息，如果缓存不存在，查询数据库，并且更新到缓存（批量查询）
     * 
     * @param pinList:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:45
     * @since 1.0.0
     */
    public List<PersPersonCacheItem> getPersonCacheByPins(List<String> pinList) {

        List<PersPersonCacheItem> personCacheItemList = new ArrayList<>();
        // 分批处理
        List<List<String>> pinGroupList = CollectionUtil.split(pinList, 800);
        for (List<String> pins : pinGroupList) {
            // 组装key获取缓存数据
            List<String> keyList = new ArrayList<>();
            for (String pin : pins) {
                keyList.add(PERSON_CACHE_KEY + pin);
            }
            List<PersPersonCacheItem> existPersonCacheList = redisTemplate.opsForValue().multiGet(keyList);

            // 排除缓存中的pin号集合，即从缓存中获取不到的pin号集合
            if (existPersonCacheList != null && existPersonCacheList.size() > 0) {
                existPersonCacheList.removeAll(Collections.singleton(null));
                if (existPersonCacheList != null && existPersonCacheList.size() > 0) {

                    for (PersPersonCacheItem existPersonCache : existPersonCacheList) {
                        if (existPersonCache.getEnabledCredential() == null) {
                            existPersonCache.setEnabledCredential(true);
                        }
                    }
                    personCacheItemList.addAll(existPersonCacheList);
                    Collection<String> caChePinList =
                        CollectionUtil.getPropertyList(existPersonCacheList, PersPersonCacheItem::getPin, "-1");
                    pins.removeAll(caChePinList);
                }
            }

            // 缓存获取不到数据，查询数据库、获取其他模块的附加属性，并且更新到缓存
            if (pins.size() > 0) {

                List<PersPerson> persPersonList = persPersonDao.findByPinIn(pins);
                if (persPersonList != null && persPersonList.size() > 0) {
                    // 排除人员表的人员pin号,剩余的存在pin号就去离职表查找
                    Collection<String> personPinList =
                        CollectionUtil.getPropertyList(persPersonList, PersPerson::getPin, "-1");
                    pins.removeAll(personPinList);

                    List<PersPersonCacheItem> updatePersonCacheItemList = personListToCacheItemList(persPersonList);

                    // 获取其他模块的附加属性
                    buildExtParams(updatePersonCacheItemList);

                    // 保存到缓存
                    updatePersonCacheItemList(updatePersonCacheItemList);
                    personCacheItemList.addAll(updatePersonCacheItemList);
                }

                // 人员表获取不到数据再去离职表获取
                if (pins.size() > 0) {

                    List<PersLeavePerson> persLeavePersonList = persLeavePersonDao.findByPinIn(pins);
                    if (persLeavePersonList != null && persLeavePersonList.size() > 0) {
                        List<PersPersonCacheItem> updatePersonCacheItemList =
                            leavePersonListToCacheItemList(persLeavePersonList);

                        // 获取其他模块的附加属性
                        buildExtParams(updatePersonCacheItemList);

                        // 保存到缓存,并设置过期时间
                        // updatePersonCacheItemList(updatePersonCacheItemList);
                        for (PersPersonCacheItem cacheItem : updatePersonCacheItemList) {
                            updatePersonCacheItem(cacheItem, 24);
                        }
                        personCacheItemList.addAll(updatePersonCacheItemList);
                    }
                }
            }
        }

        return personCacheItemList;
    }

    /**
     * 获取缓存中的人员信息过滤离职人员，如果缓存不存在，查询数据库，并且更新到缓存（批量查询）
     *
     * @param pinList:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:45
     * @since 1.0.0
     */
    public List<PersPersonCacheItem> getPersonCacheFilterLeaveByPins(List<String> pinList) {
        List<PersPersonCacheItem> personCacheItemList = getPersonCacheByPins(pinList);
        List<PersPersonCacheItem> personCacheFilterLeaveList = new ArrayList<>();
        if (personCacheItemList != null && personCacheItemList.size() > 0) {
            for (PersPersonCacheItem persPersonCacheItem : personCacheItemList) {
                if (persPersonCacheItem.getLeaveDate() == null) {
                    personCacheFilterLeaveList.add(persPersonCacheItem);
                }
            }
        }
        return personCacheFilterLeaveList;
    }

    /**
     * 获取其他模块的附加属性批量
     *
     * @param updatePersonCacheItemList:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 16:35
     * @since 1.0.0
     */
    private void buildExtParams(List<PersPersonCacheItem> updatePersonCacheItemList) {

        // 获取其他模块的附加属性
        if (persPersonCacheExtParamsServices != null && updatePersonCacheItemList != null) {

            Collection<String> pins =
                CollectionUtil.getPropertyList(updatePersonCacheItemList, PersPersonCacheItem::getPin, "-1");
            // 遍历模块
            for (PersPersonCacheExtParamsService personCacheExtParamsService : persPersonCacheExtParamsServices) {
                Map<String, Map<String, Object>> extParamsMap = personCacheExtParamsService.getExtParamsMapByPins(pins);
                if (extParamsMap != null) {
                    // 遍历人员赋值
                    for (PersPersonCacheItem personCacheItem : updatePersonCacheItemList) {
                        if (extParamsMap.containsKey(personCacheItem.getPin())) {
                            Map<String, Map<String, Object>> extParams = personCacheItem.getExtParams();
                            String module = personCacheExtParamsService.getModule();
                            extParams.put(module, extParamsMap.get(personCacheItem.getPin()));
                            personCacheItem.setExtParams(extParams);
                        }
                    }
                }
            }
        }
    }

    /**
     * 保存人员信息到缓存
     *
     * @param personCacheItem:
     * @param hour:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:45
     * @since 1.0.0
     */
    private void updatePersonCacheItem(PersPersonCacheItem personCacheItem, long hour) {
        if (hour > 0) {
            redisTemplate.opsForValue().set(PERSON_CACHE_KEY + personCacheItem.getPin(), personCacheItem, hour,
                TimeUnit.HOURS);
        } else {
            redisTemplate.opsForValue().set(PERSON_CACHE_KEY + personCacheItem.getPin(), personCacheItem);
        }
    }

    /**
     * 保存人员信息到缓存（批量保存）
     *
     * @param personCacheItemList:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:45
     * @since 1.0.0
     */
    private void updatePersonCacheItemList(List<PersPersonCacheItem> personCacheItemList) {
        Map<String, PersPersonCacheItem> map = new HashMap<>();
        for (PersPersonCacheItem persInfoCacheItem : personCacheItemList) {
            map.put(PERSON_CACHE_KEY + persInfoCacheItem.getPin(), persInfoCacheItem);
        }
        redisTemplate.opsForValue().multiSet(map);
    }

    /**
     * PersPerson 组装成 PersPersonCacheItem
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:50
     * @since 1.0.0
     */
    private PersPersonCacheItem personToCacheItem(PersPerson person) {

        // 组装人员基础数据
        PersPersonCacheItem personCacheItem = buildPersonCacheItem(person);

        // 组装部门信息
        AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemById(person.getDeptId());
        if (authDepartmentItem != null) {
            personCacheItem.setDeptCode(authDepartmentItem.getCode());
            personCacheItem.setDeptName(authDepartmentItem.getName());
        }

        return personCacheItem;
    }

    /**
     * PersPersonList 组装成 PersPersonCacheItemList
     *
     * @param persPersonList:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:49
     * @since 1.0.0
     */
    private List<PersPersonCacheItem> personListToCacheItemList(List<PersPerson> persPersonList) {

        // 批量查询部门信息
        Collection<String> deptIdList = CollectionUtil.getPropertyList(persPersonList, PersPerson::getDeptId, "-1");
        List<AuthDepartmentItem> authDepartmentItemList = authDepartmentService.getItemsByIds(deptIdList);
        Map<String, AuthDepartmentItem> authDepartmentItemMap =
            CollectionUtil.listToKeyMap(authDepartmentItemList, AuthDepartmentItem::getId);

        List<PersPersonCacheItem> personCacheItemList = new ArrayList<>();
        for (PersPerson persPerson : persPersonList) {

            // 组装人员基础数据
            PersPersonCacheItem cacheItem = buildPersonCacheItem(persPerson);

            // 组装部门信息
            if (authDepartmentItemMap.containsKey(persPerson.getDeptId())) {
                AuthDepartmentItem authDepartmentItem = authDepartmentItemMap.get(persPerson.getDeptId());
                cacheItem.setDeptCode(authDepartmentItem.getCode());
                cacheItem.setDeptName(authDepartmentItem.getName());
            }

            personCacheItemList.add(cacheItem);
        }

        return personCacheItemList;
    }

    /**
     * 组装人员基础数据
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:49
     * @since 1.0.0
     */
    private PersPersonCacheItem buildPersonCacheItem(PersPerson person) {
        PersPersonCacheItem personCacheItem = new PersPersonCacheItem(person.getId(), person.getPin(), person.getName(),
            person.getLastName(), person.getIdCard(), person.getDeptId(), person.getHireDate());
        if (person.getPersPosition() != null) {
            personCacheItem.setPositionId(person.getPersPosition().getId());
            personCacheItem.setPositionName(person.getPersPosition().getName());
        } else {
            personCacheItem.setPositionId(null);
            personCacheItem.setPositionName(null);
        }
        if (person.getEnabledCredential() == null) {
            personCacheItem.setEnabledCredential(true);
        } else {
            personCacheItem.setEnabledCredential(person.getEnabledCredential());
        }
        personCacheItem.setPhotoPath(person.getPhotoPath());
        personCacheItem.setEmail(person.getEmail());
        personCacheItem.setIsSendMail(person.getIsSendMail());
        personCacheItem.setSendSMS(person.getSendSMS());
        personCacheItem.setMobilePhone(person.getMobilePhone());
        personCacheItem.setGender(person.getGender());
        personCacheItem.setSendWhatsapp(person.getSendWhatsapp());
        personCacheItem.setWhatsappMobileNo(person.getWhatsappMobileNo());
        personCacheItem.setSendApp(person.getSendApp());
        return personCacheItem;
    }

    /**
     * PersLeavePerson 组装成 PersPersonCacheItem
     *
     * @param leavePerson:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 16:10
     * @since 1.0.0
     */
    private PersPersonCacheItem leavePersonToCacheItem(PersLeavePerson leavePerson) {
        // 组装人员基础数据
        PersPersonCacheItem personCacheItem = buildPersonCacheItem(leavePerson);
        // 组装部门信息
        AuthDepartmentItem authDepartmentItem = authDepartmentService.getItemById(leavePerson.getDeptId());
        if (authDepartmentItem != null) {
            personCacheItem.setDeptCode(authDepartmentItem.getCode());
            personCacheItem.setDeptName(authDepartmentItem.getName());
        }
        return personCacheItem;
    }

    /**
     * PersLeavePersonList 组装成 PersPersonCacheItemList
     *
     * @param persLeavePersonList:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:49
     * @since 1.0.0
     */
    private List<PersPersonCacheItem> leavePersonListToCacheItemList(List<PersLeavePerson> persLeavePersonList) {

        // 批量查询部门信息
        Collection<String> deptIdList =
            CollectionUtil.getPropertyList(persLeavePersonList, PersLeavePerson::getDeptId, "-1");
        List<AuthDepartmentItem> authDepartmentItemList = authDepartmentService.getItemsByIds(deptIdList);
        Map<String, AuthDepartmentItem> authDepartmentItemMap =
            CollectionUtil.listToKeyMap(authDepartmentItemList, AuthDepartmentItem::getId);

        List<PersPersonCacheItem> personCacheItemList = new ArrayList<>();
        for (PersLeavePerson leavePerson : persLeavePersonList) {

            // 组装人员基础数据
            PersPersonCacheItem cacheItem = buildPersonCacheItem(leavePerson);

            // 组装部门信息
            if (authDepartmentItemMap.containsKey(leavePerson.getDeptId())) {
                AuthDepartmentItem authDepartmentItem = authDepartmentItemMap.get(leavePerson.getDeptId());
                cacheItem.setDeptCode(authDepartmentItem.getCode());
                cacheItem.setDeptName(authDepartmentItem.getName());
            }

            personCacheItemList.add(cacheItem);
        }

        return personCacheItemList;
    }

    /**
     * 组装人员基础数据
     * 
     * @param leavePerson:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 16:11
     * @since 1.0.0
     */
    private PersPersonCacheItem buildPersonCacheItem(PersLeavePerson leavePerson) {
        PersPersonCacheItem personCacheItem = new PersPersonCacheItem(leavePerson.getId(), leavePerson.getPin(),
            leavePerson.getName(), leavePerson.getLastName(), null, leavePerson.getDeptId(), leavePerson.getHireDate(),
            leavePerson.getLeaveDate());
        return personCacheItem;
    }

    /**
     * 删除缓存中的人员信息
     * 
     * @param pin:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:49
     * @since 1.0.0
     */
    private void deletePersonCacheItem(String pin) {
        redisTemplate.delete(PERSON_CACHE_KEY + pin);
    }

    /**
     * 删除缓存中的人员信息（批量删除）
     *
     * @param pinList:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:49
     * @since 1.0.0
     */
    private void deletePersonCacheItemList(Collection<String> pinList) {
        List<String> keyList = new ArrayList<>();
        for (String pin : pinList) {
            keyList.add(PERSON_CACHE_KEY + pin);
        }
        redisTemplate.delete(keyList);
    }
}
