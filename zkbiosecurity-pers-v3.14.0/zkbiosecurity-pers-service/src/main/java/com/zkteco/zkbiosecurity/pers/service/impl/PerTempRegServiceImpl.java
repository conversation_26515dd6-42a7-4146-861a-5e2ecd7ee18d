package com.zkteco.zkbiosecurity.pers.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Resource;
import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.FileUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.LocaleMessageSourceUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.dao.PersTempPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.model.PersTempPerson;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.utils.PersPersonUtil;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.system.service.BaseCropFaceService;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * 临时人员注册相关业务
 * 
 * <AUTHOR> href="mailto:<EMAIL>">colin.cheng</a>
 * @version V1.0
 * @date Created In 14:56 2019/3/28
 */
@Service
public class PerTempRegServiceImpl implements PerTempRegService {
    @Autowired
    private PersTempPersonDao persTempPersonDao;

    @Autowired
    private BaseSysParamService baseSysParamService;

    @Autowired
    private BaseCropFaceService baseCropFaceService;

    @Autowired
    private PersTempPersonService persTempPersonService;
    @Autowired
    private PersAttributeService persAttributeService;
    @Autowired
    private PersAttributeExtService persAttributeExtService;
    @Resource
    private MessageSource messageSource;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private PersPersonDao persPersonDao;

    @Override
    public String perReg(PerTempRegItem item) {
        // pin未重复
        if (checkPin(item.getPin())) {
            // 手机号判断重复
            if (StringUtils.isNotBlank(item.getPhone())) {
                PersPerson person = persPersonDao.findByMobilePhone(item.getPhone());
                if (person != null && !person.getPin().equals(item.getPin())) {
                    throw ZKBusinessException.warnException(I18nUtil.i18nCode("pers_h5_personMobileRepeat"));
                }
            }
            String imgpath = FileUtil.saveFileToServer("pers", "tempPerson", item.getPin() + ".jpg", item.getPhoto());
            ZKResultMsg cropFaceMsg = validCropPhoto(item.getPin(), imgpath);
            // 图片加密
            PersPersonUtil.encryptPersonPhoto(imgpath, item.getPin(), "pers/tempPerson");
            if (ZKResultMsg.successMsg().getRet().equals(cropFaceMsg.getRet())) {
                addTemp(item, imgpath);
                return "ok";
            }
            // 删除校验失败的图片，抠图会抠图的接口中自行删除
            deleteFile(FileUtil.getLocalFullPath(FileUtil.getThumbPath(imgpath)));
            deleteFile(FileUtil.getLocalFullPath(imgpath));
            throw ZKBusinessException.warnException(cropFaceMsg.getMsg());
        }
        throw ZKBusinessException.warnException(I18nUtil.i18nCode("pers_import_pinIsRepeat", item.getPin()));
    }

    /**
     * pin可用检验
     * 
     * @param pin
     * @return
     */
    @Override
    public Boolean checkPin(String pin) {
        PersTempPerson check = persTempPersonDao.findByPin(pin);
        Optional ob = Optional.ofNullable(check);
        if (!ob.isPresent()) {
            String pinRetain = baseSysParamService.getValByName("pers.pinRetain");
            // 校验人员离职表
            if ("true".equals(pinRetain)) {
                PersLeavePersonItem leaveItem = persLeavePersonService.getItemByPin(pin);
                if (leaveItem != null) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 验证图片
     *
     * @param pin
     * @param photoPath
     * @return
     */
    private ZKResultMsg validCropPhoto(String pin, String photoPath) {
        ZKResultMsg msg = ZKResultMsg.successMsg();
        int result = regPhoto(pin, photoPath);
        Map<String, String> faceFailTypeMap = baseDictionaryValueService.getDictionaryValuesMap("PersFaceFailType");
        String retStr = String.valueOf(result);
        if (faceFailTypeMap.containsKey(retStr)) {
            msg.setRet(retStr);
            msg.setMsg(I18nUtil.i18nCode("pers_face_validFailMsg") + I18nUtil.i18nCode(faceFailTypeMap.get(retStr)));
        } else if (result <= -200) {
            msg.setRet(retStr);
            msg.setMsg(I18nUtil.i18nCode("pers_import_cropFaceFail"));
        }
        return msg;
    }

    /**
     * 验证图片
     * 
     * @param pin
     * @param photoPath
     * @return
     */
    private int regPhoto(String pin, String photoPath) {
        File sourceImg = new File(FileUtil.getLocalFullPath(photoPath));
        BufferedImage src = null;
        int res = -200;
        try {
            if (sourceImg.exists()) {
                // 抠图服务使用原图
                if (baseCropFaceService.loadDetectServiceSuccess()) {
                    res = baseCropFaceService.cropFace(FileUtil.getLocalFullPath(photoPath), "pers/tempPerson", pin);
                } else {
                    src = ImageIO.read(sourceImg);
                    if (src.getWidth() * src.getHeight() > 1024 * 1024) {// 超过1024*1024使用缩略图进行抠图，防止动态库挂
                        res = baseCropFaceService.cropFace(FileUtil.getLocalFullPath(FileUtil.getThumbPath(photoPath)),
                            "pers/tempPerson", pin);
                    } else {
                        res =
                            baseCropFaceService.cropFace(FileUtil.getLocalFullPath(photoPath), "pers/tempPerson", pin);
                    }
                }
            }
        } catch (IOException e) {
            return -200;
        }
        return res;
    }

    /**
     * 增加信息
     * 
     * @param item
     */
    private void addTemp(PerTempRegItem item, String imgPath) {
        PersTempPerson persTempPerson = new PersTempPerson();
        persTempPerson.setMobilePhone(item.getPhone());
        persTempPerson.setLastName(item.getLastName());
        persTempPerson.setPin(item.getPin());
        persTempPerson.setName(item.getName());
        persTempPerson.setStatus((short)0);
        persTempPerson.setCropPhotoPath(
            "/" + FileUtil.getCropFacePath("pers/tempPerson", item.getPin()) + "/" + item.getPin() + ".jpg");
        persTempPerson.setPhotoPath(imgPath);
        persTempPerson.setIsFrom("h5_web");
        persTempPerson = persTempPersonDao.save(persTempPerson);
        PersAttributeExtItem persAttributeExtItem = new PersAttributeExtItem();
        // 保存防疫自定义属性
        try {
            if (StringUtils.isNotBlank(item.getExposure())) {
                String exposure = baseSysParamService.getValByName("pers.exposure");
                if (StringUtils.isNotBlank(exposure)) {
                    PersAttributeItem persAttributeItem = persAttributeService.getItemById(exposure);
                    String[] exposures = item.getExposure().split(",");
                    StringBuffer exposureStr = new StringBuffer();
                    for (String exposureVal : exposures) {
                        exposureStr.append(I18nUtil.getI18nByLanguage(LocaleMessageSourceUtil.language,
                            PersPersonUtil.EXPOSURE_MAP.get(exposureVal))).append(",");
                    }
                    MethodUtils.invokeMethod(persAttributeExtItem, "setAttrValue" + persAttributeItem.getFiledIndex(),
                        exposureStr.substring(0, exposureStr.length() - 1));

                }
            }

            if (StringUtils.isNotBlank(item.getSymptom())) {
                String symptom = baseSysParamService.getValByName("pers.symptom");
                if (StringUtils.isNotBlank(symptom)) {
                    PersAttributeItem persAttributeItem = persAttributeService.getItemById(symptom);
                    String[] symptoms = item.getSymptom().split(",");
                    StringBuffer symptomsStr = new StringBuffer();
                    for (String symptomVal : symptoms) {
                        symptomsStr.append(I18nUtil.getI18nByLanguage(LocaleMessageSourceUtil.language,
                            PersPersonUtil.SYMPTOM_MAP.get(symptomVal))).append(",");
                    }
                    MethodUtils.invokeMethod(persAttributeExtItem, "setAttrValue" + persAttributeItem.getFiledIndex(),
                        symptomsStr.substring(0, symptomsStr.length() - 1));
                }
            }

            if (StringUtils.isNotBlank(item.getVisitCity())) {
                String visitCity = baseSysParamService.getValByName("pers.visitCity");
                if (StringUtils.isNotBlank(visitCity)) {
                    PersAttributeItem persAttributeItem = persAttributeService.getItemById(visitCity);
                    MethodUtils.invokeMethod(persAttributeExtItem, "setAttrValue" + persAttributeItem.getFiledIndex(),
                        item.getVisitCity());
                }
            }
            if (StringUtils.isNotBlank(item.getHealthRemarks())) {
                String remarks = baseSysParamService.getValByName("pers.remarks");
                if (StringUtils.isNotBlank(remarks)) {
                    PersAttributeItem persAttributeItem = persAttributeService.getItemById(remarks);
                    MethodUtils.invokeMethod(persAttributeExtItem, "setAttrValue" + persAttributeItem.getFiledIndex(),
                        item.getHealthRemarks());
                }
            }

            // 保存扩展属性
            if (Objects.nonNull(persAttributeExtItem)) {
                persAttributeExtItem.setPersonId(persTempPerson.getId());
                persAttributeExtService.saveItem(persAttributeExtItem);
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InvocationTargetException e) {
            e.printStackTrace();
        }

        // 自动审核 pers.tempPerson.audit
        String audit = baseSysParamService.getValByName("pers.tempPerson.audit");
        if ("1".equals(audit)) {
            PersTempPersonItem persTempPersonItem = new PersTempPersonItem();
            persTempPersonItem = persTempPersonService.getItemByPin(item.getPin());
            persTempPersonService.audit(persTempPersonItem);
        }
    }

    /**
     * 删除错误图片及目录
     * 
     * @param fileName
     * @return
     */
    private boolean deleteFile(String fileName) {
        File file = new File(fileName);
        if (file.exists() && file.isFile() || file.exists() && file.isDirectory()) {
            if (file.delete()) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    @Override
    public String getPrivacyContent() {
        String privacyVal = baseSysParamService.getValByName("system.privacy");
        String privacyContent = baseSysParamService.getPrivacyContent(privacyVal);
        return privacyContent;
    }

}
