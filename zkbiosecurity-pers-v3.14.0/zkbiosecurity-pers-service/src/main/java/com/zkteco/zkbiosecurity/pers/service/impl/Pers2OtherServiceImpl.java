package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.acc.service.Acc4PersPersonService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4PersPersonItem;
import com.zkteco.zkbiosecurity.att.service.Att4PersPersonService;
import com.zkteco.zkbiosecurity.att.vo.Att4PersPersonItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.ele.service.Ele4PersPersonService;
import com.zkteco.zkbiosecurity.ele.vo.Ele4PersDirectSelectionItem;
import com.zkteco.zkbiosecurity.ele.vo.Ele4PersPersonItem;
import com.zkteco.zkbiosecurity.ins.service.Ins4PersPersonService;
import com.zkteco.zkbiosecurity.ins.vo.Ins4PersPersonItem;
import com.zkteco.zkbiosecurity.park.service.Park4PersPersonService;
import com.zkteco.zkbiosecurity.park.vo.Park4PersPersonItem;
import com.zkteco.zkbiosecurity.patrol.service.Patrol4PersPersonService;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.Pers2OtherService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonExtService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonExtendService;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.pid.service.Pid4PersPersonService;
import com.zkteco.zkbiosecurity.pid.vo.Pid4PersPersonItem;
import com.zkteco.zkbiosecurity.pos.service.Pos4PersCardService;
import com.zkteco.zkbiosecurity.pos.service.Pos4PersPersonService;
import com.zkteco.zkbiosecurity.pos.vo.Pos4PersPersonItem;
import com.zkteco.zkbiosecurity.posid.service.PosID4PersPersonService;
import com.zkteco.zkbiosecurity.posid.vo.PosID4PersPersonItem;
import com.zkteco.zkbiosecurity.psg.service.Psg4PersPersonService;
import com.zkteco.zkbiosecurity.psg.vo.Psg4PersPersonItem;
import com.zkteco.zkbiosecurity.sms.modem.service.SmsModem4OtherService;
import com.zkteco.zkbiosecurity.vis.service.Vis4PersPersonService;
import com.zkteco.zkbiosecurity.vms.service.Vms4PersPersonService;
import com.zkteco.zkbiosecurity.vms.vo.Vms4PersPersonItem;
import com.zkteco.zkbiosecurity.whatsapp.service.Whatsapp4OtherService;

/**
 * 人事通知其他模块人员信息管理
 * 
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 14:08 2018/11/29
 */
@Service
@Transactional
public class Pers2OtherServiceImpl implements Pers2OtherService {

    private final static Logger logger = LoggerFactory.getLogger(Pers2OtherServiceImpl.class);

    @Autowired(required = false)
    private Acc4PersPersonService acc4PersPersonService;
    @Autowired(required = false)
    private Ele4PersPersonService ele4PersPersonService;
    @Autowired(required = false)
    private Att4PersPersonService att4PersPersonService;
    @Autowired(required = false)
    private Pos4PersPersonService pos4PersPersonService;
    @Autowired(required = false)
    private PosID4PersPersonService posID4PersPersonService;
    @Autowired(required = false)
    private Park4PersPersonService park4PersPersonService;
    @Autowired(required = false)
    private Pid4PersPersonService pid4PersPersonService;
    @Autowired(required = false)
    private Patrol4PersPersonService patrol4PersPersonService;
    @Autowired(required = false)
    private Vis4PersPersonService vis4PersPersonService;
    @Autowired(required = false)
    private Ins4PersPersonService ins4PersPersonService;
    @Autowired(required = false)
    private PersPersonExtendService persPersonExtendService;
    @Autowired(required = false)
    private Pos4PersCardService pos4PersCardService;
    @Autowired(required = false)
    private Psg4PersPersonService psg4PersPersonService;
    @Autowired(required = false)
    private SmsModem4OtherService smsModem4OtherService;
    @Autowired(required = false)
    private Vms4PersPersonService vms4PersPersonService;
    @Autowired(required = false)
    private PersPersonExtService[] persPersonExtServices;
    @Autowired(required = false)
    private Whatsapp4OtherService whatsapp4OtherService;

    @Override
    public void pushEditPerson(PersPersonItem personItem, Map<String, String> extParams) {
        // editAccPersonExtInfo(personItem.getId(), extParams);
        // editElePersonExtInfo(personItem, extParams);
        editAttPersonExtInfo(personItem, extParams);
        editPosPersonExtInfo(personItem, extParams);
        editPosIDPersonExtInfo(personItem, extParams);
        // editParkPersonExtInfo(personItem, extParams);
        // editPidPersonExtInfo(personItem, extParams);
        editInsPersonExtInfo(personItem, extParams);
        // editPsgPersonExtInfo(personItem.getId(), extParams);
        editVmsPersonExtInfo(personItem, extParams);
        // 添加业务模块扩展调用 add by max 20200228
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.editPersonExt(personItem, extParams));
        }
    }

    /**
     * 删除其他模块人员
     *
     * @param personIds
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/4 14:32
     */
    @Override
    public boolean pushDelOtherModelPerson(String personIds) {
        // if (Objects.nonNull(park4PersPersonService) && !park4PersPersonService.checkDelPerson(personIds)) {
        //     throw new ZKBusinessException("park_authorize_authorizedNotDel");
        // }
        // 校验人员是否可以删除
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.checkDelPerson(personIds));
        }

        // 检查结束进行删除
        if (Objects.nonNull(acc4PersPersonService)) {
            acc4PersPersonService.delAccPerson(personIds);
        }
        if (Objects.nonNull(ele4PersPersonService)) {
            ele4PersPersonService.delElePerson(personIds);
        }
        if (Objects.nonNull(att4PersPersonService)) {
            att4PersPersonService.delAttPerson(personIds);
        }
        // if (Objects.nonNull(park4PersPersonService)) {
        //     park4PersPersonService.delParkPerson(personIds);
        // }
        if (Objects.nonNull(pos4PersPersonService)) {
            pos4PersPersonService.delPosPerson(personIds);
        }
        if (Objects.nonNull(posID4PersPersonService)) {
            posID4PersPersonService.delPosPerson(personIds);
        }
        if (Objects.nonNull(pid4PersPersonService)) {
            pid4PersPersonService.delPidPerson(personIds);
        }
        if (Objects.nonNull(patrol4PersPersonService)) {
            patrol4PersPersonService.delPatrolPerson(personIds);
        }
        if (Objects.nonNull(vis4PersPersonService)) {
            vis4PersPersonService.delVisitedPerson(personIds);
        }
        if (Objects.nonNull(ins4PersPersonService)) {
            ins4PersPersonService.delInsPerson(personIds);
        }
        if (Objects.nonNull(psg4PersPersonService)) {
            psg4PersPersonService.delPsgPerson(personIds);
        }
        if (Objects.nonNull(vms4PersPersonService)) {
            vms4PersPersonService.delVmsPerson(personIds);
        }

        // 添加业务模块扩展调用 add by max 20200228
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.delPersonExt(personIds));
        }
        return true;
    }

    @Override
    public void pushBatchDeptChange(List<String> personIds, String deptId, boolean flag) {
        // 切换部门权限
        if (flag) {
            if (Objects.nonNull(acc4PersPersonService)) {
                // 通知门禁模块调整部门权限
                acc4PersPersonService.batchDeptChange(personIds, deptId);
            }
            if (Objects.nonNull(ele4PersPersonService)) {
                // 通知梯控模块调整部门权限
                ele4PersPersonService.batchDeptChange(personIds, deptId);
            }
        }
        if (Objects.nonNull(pos4PersPersonService)) {
            // 通知消费模块调整部门权限
            pos4PersPersonService.batchDeptChange(personIds, deptId);
        }
        if (Objects.nonNull(att4PersPersonService)) {
            // 通知考勤模块调整部门权限
            att4PersPersonService.batchDeptChange(personIds, deptId);
        }
        if (Objects.nonNull(park4PersPersonService)) {
            // 通知停车模块调整部门权限
            park4PersPersonService.batchDeptChange(personIds, deptId);
        }
        if (Objects.nonNull(ins4PersPersonService)) {
            // 通知信息屏模块调整部门权限
            ins4PersPersonService.batchDeptChange(personIds, deptId);
        }
        if (Objects.nonNull(posID4PersPersonService)) {
            // 通知在线消费模块调整部门权限
            posID4PersPersonService.batchDeptChange(personIds, deptId);
        }

        // 添加业务模块扩展调用 add by max 20200228
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.batchDeptChangePermissonExt(personIds, deptId));
        }
    }

    @Override
    public void pushBatchSaveItem(List<PersPersonItem> personItems, String module) {
        // batchSavePerson2Att(personItems, module);
        // 其他模块的人员保存到信息屏
        // batchSavePerson2Ins(personItems,module);
        // 人员扩展服务，方便解决方案使用bean方式替换
        if (Objects.nonNull(persPersonExtendService)) {
            persPersonExtendService.editPersonExt(personItems, module);
        }
    }

    @Override
    public void pushBatchImportPerson(List<PersPersonItem> personItems) {
        // 添加业务模块扩展调用 add by max 20200228
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.batchImportPersonExt(personItems));
        }

        Map<String, Att4PersPersonItem> att4PersPersonItemMap = new HashMap<>();
        Map<String, Pid4PersPersonItem> pid4PersPersonItemMap = new HashMap<>();
        Map<String, Ins4PersPersonItem> ins4PersPersonItemMap = new HashMap<>();

        personItems.forEach(personItem -> {
            // 组装发送考勤的人员数据
            Att4PersPersonItem att4PersPersonItem = new Att4PersPersonItem();
            att4PersPersonItem.setPersonId(personItem.getId());
            att4PersPersonItem.setPersonPin(personItem.getPin());
            att4PersPersonItem.setPersonName(personItem.getName());
            att4PersPersonItem.setPersonLastName(personItem.getLastName());
            att4PersPersonItem.setDeptId(personItem.getDeptId());
            att4PersPersonItem.setHireDate(personItem.getHireDate());
            att4PersPersonItem.setIsAttendance(true);
            att4PersPersonItem.setPerDevAuth((short)0);
            att4PersPersonItemMap.put(att4PersPersonItem.getPersonPin(), att4PersPersonItem);

            // 组装发送人证的人员数据
            Pid4PersPersonItem pid4PersPersonItem = new Pid4PersPersonItem();
            pid4PersPersonItem.setPersonId(personItem.getId());
            pid4PersPersonItem.setPersonPin(personItem.getPin());
            pid4PersPersonItemMap.put(pid4PersPersonItem.getPersonPin(), pid4PersPersonItem);

            // 组装发送信息屏的人员数据
            Ins4PersPersonItem ins4PersPersonItem = new Ins4PersPersonItem();
            ins4PersPersonItem.setPersonId(personItem.getId());
            ins4PersPersonItem.setPersonPin(personItem.getPin());
            ins4PersPersonItem.setDeptId(personItem.getDeptId());
            ins4PersPersonItem.setPerDevAuth((short)0);
            // 默认的身份类别是普通
            ins4PersPersonItem.setCategory(PersConstants.CATEGORY_COMMON);
            ins4PersPersonItemMap.put(ins4PersPersonItem.getPersonPin(), ins4PersPersonItem);
        });

        // 发送考勤模块
        if (Objects.nonNull(att4PersPersonService)) {
            List<List<Att4PersPersonItem>> split =
                CollectionUtil.split(att4PersPersonItemMap.values(), CollectionUtil.splitSize);
            for (List<Att4PersPersonItem> att4PersPersonItemList : split) {
                att4PersPersonService.batchPerson(att4PersPersonItemList);
            }
        }

        // 发送人证模块
        if (Objects.nonNull(pid4PersPersonService)) {
            List<List<Pid4PersPersonItem>> split =
                CollectionUtil.split(pid4PersPersonItemMap.values(), CollectionUtil.splitSize);
            for (List<Pid4PersPersonItem> pid4PersPersonItemList : split) {
                pid4PersPersonService.batchImportPerson(pid4PersPersonItemList);
            }
        }

        // 发送信息屏模块
        if (Objects.nonNull(ins4PersPersonService)) {
            List<List<Ins4PersPersonItem>> split =
                CollectionUtil.split(ins4PersPersonItemMap.values(), CollectionUtil.splitSize);
            for (List<Ins4PersPersonItem> ins4PersPersonItemList : split) {
                ins4PersPersonService.batchPerson(ins4PersPersonItemList);
            }
        }
    }

    @Override
    public boolean checkForcePwd(String personPwd) {
        if (Objects.nonNull(acc4PersPersonService)) {
            return acc4PersPersonService.checkForcePwd(personPwd);
        }
        return false;
    }

    @Override
    public void checkIsUseCardNo(String cardNo) {
        if (Objects.nonNull(pos4PersCardService) && pos4PersCardService.checkUseCardNo(cardNo)) {
            // 该人员的主卡正在被消费模块使用，请去消费模块执行退卡操作！
            throw ZKBusinessException.warnException("pers_card_posUseCardNo");
        }
        // 以后若卡号有被其他模块使用，且不能删除，继续往这边添加即可。
    }

    private void batchSavePerson2Att(List<PersPersonItem> personItems, String module) {
        if (Objects.nonNull(att4PersPersonService) && !ConstUtil.SYSTEM_MODULE_ATT.equals(module)) {
            List<Att4PersPersonItem> attPersons = new ArrayList<Att4PersPersonItem>();
            personItems.forEach(persPersonItem -> {
                Att4PersPersonItem item = new Att4PersPersonItem();
                item.setPersonId(persPersonItem.getId());
                item.setPersonPin(persPersonItem.getPin());
                item.setPersonName(persPersonItem.getName());
                item.setPersonLastName(persPersonItem.getLastName());
                item.setDeptId(persPersonItem.getDeptId());
                item.setDeptCode(persPersonItem.getDeptCode());
                item.setDeptName(persPersonItem.getDeptName());
                item.setHireDate(persPersonItem.getHireDate());
                attPersons.add(item);
            });
            att4PersPersonService.batchPerson(attPersons);
        }
    }

    private void batchSavePerson2Ins(List<PersPersonItem> personItems, String module) {
        if (Objects.nonNull(ins4PersPersonService) && !ConstUtil.SYSTEM_MODULE_INS.equals(module)) {
            List<Ins4PersPersonItem> insPersons = Lists.newArrayList();
            personItems.forEach(persPersonItem -> {
                Ins4PersPersonItem item = new Ins4PersPersonItem();
                item.setPersonId(persPersonItem.getId());
                item.setPersonPin(persPersonItem.getPin());
                item.setPersonName(persPersonItem.getName());
                item.setPersonLastName(persPersonItem.getLastName());
                item.setDeptId(persPersonItem.getDeptId());
                item.setDeptCode(persPersonItem.getDeptCode());
                item.setDeptName(persPersonItem.getDeptName());
                insPersons.add(item);
            });
            ins4PersPersonService.batchPerson(insPersons);
        }
    }

    private void editAccPersonExtInfo(String personId, Map<String, String> extParams) {
        if (Objects.nonNull(acc4PersPersonService)) {
            Acc4PersPersonItem item = new Acc4PersPersonItem();
            item.setPersonId(personId);
            boolean updateAuth =
                MapUtils.getString(extParams, "moduleAuth", StringUtils.EMPTY).contains(ConstUtil.SYSTEM_MODULE_ACC);
            if (updateAuth) {
                item.setDelayPassage(MapUtils.getBoolean(extParams, "acc.delayPassage", false));
                item.setPersonLevelIds(MapUtils.getString(extParams, "acc.personLevelIds"));
                item.setSuperAuth(MapUtils.getShort(extParams, "acc.superAuth", (short)0));
                item.setDisabled(MapUtils.getBoolean(extParams, "acc.disabled", false));
                item.setPrivilege(MapUtils.getShort(extParams, "acc.privilege", (short)0));
                boolean isSetValidTime = MapUtils.getBoolean(extParams, "acc.isSetValidTime", false);
                item.setIsSetValidTime(isSetValidTime);
                if (isSetValidTime) {
                    item.setStartTime(DateUtil.stringToDate(MapUtils.getString(extParams, "acc.startTime"),
                        DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                    item.setEndTime(DateUtil.stringToDate(MapUtils.getString(extParams, "acc.endTime"),
                        DateUtil.DateStyle.YYYY_MM_DD_HH_MM_SS));
                }
            }
            item.setPersonModify(MapUtils.getBoolean(extParams, "personModify", false));
            // 放入标识是否页面有操作权限组标签页的权限
            item.setUpdateAccSet(updateAuth);
            logger.debug("-----------------------  start execute acc4PersPersonService.editAccPerson");
            acc4PersPersonService.editAccPerson(item);
        }
    }

    private void editElePersonExtInfo(PersPersonItem personItem, Map<String, String> extParams) {
        if (Objects.nonNull(ele4PersPersonService)) {
            Ele4PersPersonItem item = new Ele4PersPersonItem();
            item.setPersonId(personItem.getId());
            item.setPersonPin(personItem.getPin());
            boolean updateAuth =
                MapUtils.getString(extParams, "moduleAuth", StringUtils.EMPTY).contains(ConstUtil.SYSTEM_MODULE_ELE);
            if (updateAuth) {
                item.setPersonLevelIds(MapUtils.getString(extParams, "ele.personLevelIds"));
                item.setSuperAuth(MapUtils.getShort(extParams, "ele.superAuth", (short)0));
                item.setDisabled(MapUtils.getBoolean(extParams, "ele.disabled", false));
                boolean isSetValidTime = MapUtils.getBoolean(extParams, "ele.isSetValidTime", false);
                item.setIsSetValidTime(isSetValidTime);
                if (isSetValidTime) {
                    item.setStartTime(DateUtil.stringToDate(MapUtils.getString(extParams, "ele.startTime"),
                        DateUtil.DateStyle.YYYY_MM_DD));
                    item.setEndTime(DateUtil.stringToDate(MapUtils.getString(extParams, "ele.endTime"),
                        DateUtil.DateStyle.YYYY_MM_DD));
                }
                boolean isSetDirectSelection = MapUtils.getBoolean(extParams, "ele.isSetDirectSelection", false);
                item.setIsSetDirectSelection(isSetDirectSelection);
                if (isSetDirectSelection) {
                    buildElePersonDirectSelection(item, extParams);
                }
            }
            // 放入标识是否页面有操作权限组标签页的权限
            item.setUpdateEleSet(updateAuth);
            item.setPersonModify(MapUtils.getBoolean(extParams, "personModify", false));
            ele4PersPersonService.editElePerson(item);
        }
    }

    /**
     * 组装梯控人员直达选层权限
     *
     * @param item:
     * @param extParams:
     * @return void
     * <AUTHOR>
     * @date 2021-04-20 9:24
     * @since 1.0.0
     */
    private void buildElePersonDirectSelection(Ele4PersPersonItem item, Map<String, String> extParams) {
        String personDirectSelection = MapUtils.getString(extParams, "ele.personDirectSelectionInfo");
        List<Ele4PersDirectSelectionItem> persDirectSelectionItemList = new ArrayList<>();
        if (StringUtils.isNotBlank(personDirectSelection)) {
            JSONObject directSelectionInfo = JSON.parseObject(personDirectSelection);
            Iterator iter = directSelectionInfo.entrySet().iterator();
            while (iter.hasNext()) {
                Ele4PersDirectSelectionItem directSelectionItem = new Ele4PersDirectSelectionItem();
                Map.Entry entry = (Map.Entry)iter.next();
                JSONObject data = JSONObject.parseObject(entry.getValue() + "");
                directSelectionItem.setPersonPin(item.getPersonPin());
                directSelectionItem.setDeviceId(data.getString("deviceId"));
                directSelectionItem.setDirectFloorNo(data.getShort("directFloorNo"));
                directSelectionItem.setSelectionFloorNos(data.getString("selectionFloorNos"));
                directSelectionItem.setTimeSegId(data.getString("timeSegId"));
                persDirectSelectionItemList.add(directSelectionItem);
            }
        }
        item.setPersDirectSelectionItemList(persDirectSelectionItemList);
    }

    private void editAttPersonExtInfo(PersPersonItem person, Map<String, String> extParams) {
        if (Objects.nonNull(att4PersPersonService)) {
            Att4PersPersonItem item = new Att4PersPersonItem();
            item.setPersonId(person.getId());
            item.setPersonPin(person.getPin());
            item.setPersonName(person.getName());
            item.setPersonLastName(person.getLastName());
            item.setDeptId(person.getDeptId());
            item.setDeptCode(person.getDeptCode());
            item.setDeptName(person.getDeptName());
            item.setHireDate(person.getHireDate());
            boolean updateAuth =
                MapUtils.getString(extParams, "moduleAuth", StringUtils.EMPTY).contains(ConstUtil.SYSTEM_MODULE_ATT);
            // 有更新权限才能更新区域，是否考勤，设备权限等。
            if (updateAuth) {
                item.setDevSn(MapUtils.getString(extParams, "att.devSn"));
                item.setAreaIds(MapUtils.getString(extParams, "att.personAreas"));
                item.setIsAttendance(MapUtils.getBoolean(extParams, "att.isAttendance", true));
                item.setPerDevAuth(MapUtils.getShort(extParams, "att.perDevAuth", (short)0));
                item.setVerifyMode(MapUtils.getShort(extParams, "att.verifyMode"));
            }
            item.setUpdateAttSet(updateAuth);
            item.setExceptionFlag(person.getExceptionFlag());
            att4PersPersonService.editAttPerson(item);
        }
    }

    @Deprecated
    private void editParkPersonExtInfo(PersPersonItem person, Map<String, String> extParams) {
        if (Objects.nonNull(park4PersPersonService)) {
            Park4PersPersonItem item = new Park4PersPersonItem();
            item.setPersonId(person.getId());
            item.setPersonPin(person.getPin());
            item.setPersonName(person.getName());
            item.setPersonLastName(person.getLastName());
            item.setPersonMobilePhone(person.getMobilePhone());
            item.setDeptId(person.getDeptId());
            item.setDeptName(person.getDeptName());
            boolean updateAuth =
                MapUtils.getString(extParams, "moduleAuth", StringUtils.EMPTY).contains(ConstUtil.SYSTEM_MODULE_PARK);
            if (updateAuth) {
                item.setCarAreas(MapUtils.getString(extParams, "park.carAreas", ""));
                item.setParkCarNumbers(MapUtils.getString(extParams, "park.carNumbers", ""));
            }
            item.setUpdateParkSet(updateAuth);
            park4PersPersonService.editParkPerson(item);
        }
    }

    private void editPosPersonExtInfo(PersPersonItem person, Map<String, String> extParams) {
        if (Objects.nonNull(pos4PersPersonService)) {
            Pos4PersPersonItem item = new Pos4PersPersonItem();
            item.setPersonId(person.getId());
            item.setPersonPin(person.getPin());
            item.setPersonName(person.getName());
            item.setPersonLastName(person.getLastName());
            item.setDeptId(person.getDeptId());
            item.setDeptCode(person.getDeptCode());
            item.setDeptName(person.getDeptName());
            item.setCardNos(MapUtils.getString(extParams, "cardNos"));
            pos4PersPersonService.editPosPerson(item);
        }
    }

    private void editPosIDPersonExtInfo(PersPersonItem person, Map<String, String> extParams) {
        if (Objects.nonNull(posID4PersPersonService)) {
            PosID4PersPersonItem item = new PosID4PersPersonItem();
            item.setPersonId(person.getId());
            item.setPersonPin(person.getPin());
            item.setPersonName(person.getName());
            item.setPersonLastName(person.getLastName());
            item.setDeptId(person.getDeptId());
            item.setDeptCode(person.getDeptCode());
            item.setDeptName(person.getDeptName());
            item.setCardNos(MapUtils.getString(extParams, "cardNos"));
            posID4PersPersonService.editPosPerson(item);
        }
    }

    private void editPidPersonExtInfo(PersPersonItem person, Map<String, String> extParams) {
        if (Objects.nonNull(pid4PersPersonService)) {
            Pid4PersPersonItem item = new Pid4PersPersonItem();
            item.setPersonId(person.getId());
            item.setPersonPin(person.getPin());
            item.setCardNo(MapUtils.getString(extParams, "cardNos"));
            boolean updateAuth = MapUtils.getString(extParams, "moduleAuth", StringUtils.EMPTY)
                .contains(ConstUtil.SYSTEM_MODULE_IDENTIFICATION);
            // 放入标识是否页面有操作权限组标签页的权限
            if (updateAuth) {
                item.setIsIssued(MapUtils.getBoolean(extParams, "pid.isIssued", true));
            }
            pid4PersPersonService.editPidPerson(item);
        }
    }

    private void editInsPersonExtInfo(PersPersonItem person, Map<String, String> extParams) {
        if (Objects.nonNull(ins4PersPersonService)) {
            Ins4PersPersonItem item = new Ins4PersPersonItem();
            item.setPersonId(person.getId());
            item.setPersonPin(person.getPin());
            item.setPersonName(person.getName());
            item.setPersonLastName(person.getLastName());
            item.setDeptId(person.getDeptId());
            item.setDeptCode(person.getDeptCode());
            item.setDeptName(person.getDeptName());
            item.setHireDate(person.getHireDate());
            boolean updateAuth =
                MapUtils.getString(extParams, "moduleAuth", StringUtils.EMPTY).contains(ConstUtil.SYSTEM_MODULE_INS);
            // 有更新权限才能更新区域，是否考勤，设备权限等。
            if (updateAuth) {
                item.setDevSn(MapUtils.getString(extParams, "ins.devSn"));
                item.setAreaIds(MapUtils.getString(extParams, "ins.personAreas"));
                item.setPerDevAuth(MapUtils.getShort(extParams, "ins.perDevAuth", (short)0));
                // 修改身份类别会默认为空的问题 mod by amaze.wu 20190526
                if (StringUtils.isBlank(MapUtils.getString(extParams, "ins.category"))) {
                    item.setCategory("0");
                } else {
                    item.setCategory(MapUtils.getString(extParams, "ins.category"));
                }
            }
            item.setUpdateInsSet(updateAuth);
            ins4PersPersonService.editInsPerson(item);
        }
    }

    private void editPsgPersonExtInfo(String personId, Map<String, String> extParams) {
        if (Objects.nonNull(psg4PersPersonService)) {
            Psg4PersPersonItem item = new Psg4PersPersonItem();
            item.setPersonId(personId);
            boolean updateAuth = MapUtils.getString(extParams, "moduleAuth", StringUtils.EMPTY)
                .contains(ConstUtil.SYSTEM_MODULE_PASSAGE);
            if (updateAuth) {
                item.setPersonLevelIds(MapUtils.getString(extParams, "psg.personLevelIds"));
                item.setSuperAuth(MapUtils.getShort(extParams, "psg.superAuth", (short)0));
                item.setPrivilege(MapUtils.getShort(extParams, "psg.privilege", (short)0));
            }
            // 放入标识是否页面有操作权限组标签页的权限
            item.setUpdatePsgSet(updateAuth);
            logger.debug("-----------------------  start execute psg4PersPersonService.editPsgPerson");
            psg4PersPersonService.editPsgPerson(item);
        }
    }

    private void editVmsPersonExtInfo(PersPersonItem person, Map<String, String> exParams) {
        if (Objects.nonNull(vms4PersPersonService)) {
            Vms4PersPersonItem item = new Vms4PersPersonItem();
            item.setPersonId(person.getId());
            item.setPin(person.getPin());
            item.setName(person.getName());
            item.setLastName(person.getLastName());
            item.setPhone(person.getMobilePhone());
            item.setPhotoPath(person.getPhotoPath());
            vms4PersPersonService.editVmsPerson(item);
        }
    }

    @Override
    public boolean completeSMSModemInfo() {
        if (smsModem4OtherService != null) {
            return smsModem4OtherService.completeSMSModemInfo();
        }
        return false;
    }

    @Override
    public boolean checkShowSMS() {
        if (smsModem4OtherService != null) {
            return smsModem4OtherService.checkSmsModemLicense();
        }
        return false;
    }

    @Override
    public void deletePersonBioTemplate(Collection<String> personIds, Collection<Short> bioTemplateTypes) {
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.deletePersonBioTemplate(personIds, bioTemplateTypes));
        }
    }

    @Override
    public void setPersonInfo2OtherModule(Collection<PersPersonInfo2OtherItem> items, String module) {
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.setOtherPersonInfo2Dev(items, module));
        }
    }

    @Override
    public void setBioTemplate2OtherModule(Collection<PersBioTemplate2OtherItem> items, String module) {
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.setOtherBioTemplate2Dev(items, module));
        }
    }

    @Override
    public void setBioPhoto2OtherModule(Collection<PersBioPhoto2OtherItem> items, String module) {
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.setOtherBioPhoto2Dev(items, module));
        }
    }

    @Override
    public void setUserPic2OtherModule(Collection<PersUserPic2OtherItem> items, String module) {
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.setOtherUserPic2Dev(items, module));
        }
    }

    @Override
    public void pushLeavePerson(String leaveIds) {
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.leavePersonExt(leaveIds));
        }
    }

    @Override
    public void pushLeavePersonDel(String leaveIds) {
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.delPersonExt(leaveIds));
        }
    }

    @Override
    public void pushBioPhoto2OtherModules(PersBioPhoto2OtherItem item) {
        if (persPersonExtServices != null) {
            List<PersBioPhoto2OtherItem> items = new ArrayList<>();
            items.add(item);
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.pushBioPhoto2Devs(items));
        }
    }

    @Override
    public void pushUserPic2OtherModules(PersUserPic2OtherItem item) {
        if (persPersonExtServices != null) {
            List<PersUserPic2OtherItem> items = new ArrayList<>();
            items.add(item);
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.pushUserPic2Devs(items));
        }
    }

    @Override
    public boolean checkExistCardNo(String cardNo) {
        boolean existCard = false;
        if (persPersonExtServices != null && StringUtils.isNotBlank(cardNo)) {
            List<PersPersonExtService> persPersonExtServiceList =
                Arrays.stream(persPersonExtServices).collect(Collectors.toList());
            for (PersPersonExtService ps : persPersonExtServiceList) {
                existCard = ps.checkExistCardNo(cardNo);
                if (existCard) {
                    return existCard;
                }
            }
        }
        return existCard;
    }

    @Override
    public Boolean checkShowWhatsapp() {
        if (Objects.nonNull(whatsapp4OtherService)) {
            return whatsapp4OtherService.checkShowWhatsappLicense();
        }
        return false;
    }

    @Override
    public boolean completeWhatsappModemInfo() {
        if (Objects.nonNull(whatsapp4OtherService)) {
            return whatsapp4OtherService.completeWhatsAppModemInfo();
        }
        return false;
    }

    @Override
    public void pushLeaveOtherModelPerson(List<PersPushLeavePersonItem> persPushLeavePersonItems) {
        String personIds = CollectionUtil.getPropertys(persPushLeavePersonItems, PersPushLeavePersonItem::getPersonId);
        if (StringUtils.isNotBlank(personIds)) {
            if (Objects.nonNull(acc4PersPersonService)) {
                acc4PersPersonService.leaveAccPerson(personIds);
            }
            if (Objects.nonNull(ele4PersPersonService)) {
                ele4PersPersonService.leaveElePerson(personIds);
            }
            if (Objects.nonNull(att4PersPersonService)) {
                att4PersPersonService.leaveAttPerson(personIds);
            }
            if (Objects.nonNull(park4PersPersonService)) {
                park4PersPersonService.leaveParkPerson(personIds);
            }
            if (Objects.nonNull(pos4PersPersonService)) {
                pos4PersPersonService.leavePosPerson(personIds);
            }
            if (Objects.nonNull(pid4PersPersonService)) {
                pid4PersPersonService.leavePidPerson(personIds);
            }
            if (Objects.nonNull(patrol4PersPersonService)) {
                patrol4PersPersonService.leavePatrolPerson(personIds);
            }
            if (Objects.nonNull(vis4PersPersonService)) {
                vis4PersPersonService.leaveVisitedPerson(personIds);
            }
            if (Objects.nonNull(psg4PersPersonService)) {
                psg4PersPersonService.leavePsgPerson(personIds);
            }
            if (Objects.nonNull(vms4PersPersonService)) {
                vms4PersPersonService.leaveVmsPerson(personIds);
            }
        }
        if (!persPushLeavePersonItems.isEmpty() && persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.pushLeavePersonExt(persPushLeavePersonItems));
        }
    }

    @Override
    public void pushLeavePersonDel(List<PersPushLeavePersonItem> persPushLeavePersonItems) {
        if (!persPushLeavePersonItems.isEmpty() && persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.pushLeavePersonDel(persPushLeavePersonItems));
        }
    }
}
