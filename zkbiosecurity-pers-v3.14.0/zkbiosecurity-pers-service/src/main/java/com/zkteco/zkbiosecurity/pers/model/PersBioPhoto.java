package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人员比对照片
 *
 * <AUTHOR>
 * @date 2022/7/7 16:45
 * @since 1.0.0
 */
@Entity
@Table(name = "PERS_BIOPHOTO")
@Getter
@Setter
@Accessors(chain = true)
public class PersBioPhoto extends BaseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @Column(name = "PERSON_ID", length = 50, nullable = false)
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "PERSON_PIN", length = 50, nullable = false)
    private String personPin;

    /**
     * 照片路径
     */
    @Column(name = "PHOTO_PATH", length = 200)
    private String photoPath;

    /**
     * 生物特征类型
     */
    @Column(name = "BIO_TYPE")
    private Short bioType;
}
