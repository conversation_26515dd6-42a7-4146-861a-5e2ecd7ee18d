package com.zkteco.zkbiosecurity.pers.model;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 韦根业务ID表
 */
@Entity
@Table(name = "PERS_WIEGANDFMT_BID")
@Getter
@Setter
@Accessors(chain = true)
public class PersWiegandFmtBId implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "ID")
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_pers_wiegandfmtbid")
    @GenericGenerator(name = "seq_pers_wiegandfmtbid", strategy = "org.hibernate.id.enhanced.SequenceStyleGenerator", parameters = {
            @org.hibernate.annotations.Parameter(name = "sequence_name", value = "seq_pers_wiegandfmtbid")})
    private Long id;
}
