package com.zkteco.zkbiosecurity.pers.data.move;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.vo.*;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.annotation.DataMigration;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.system.service.DataMoveManager;
import com.zkteco.zkbiosecurity.system.service.SystemDataMigrationService;
import com.zkteco.zkbiosecurity.system.vo.SystemDataTransferProcess;
import com.zkteco.zkbiosecurity.system.vo.SystemDataUpgradeProcess;

/**
 * 人事模块数据迁移管理器
 * 主要功能：从3150进行数据迁移到新架构上
 *
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 15:20 2018/11/26
 */
@Component
@Slf4j
@DataMigration(order = 10)
public class PersDataMoveManagement implements DataMoveManager {
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersPositionService persPositionService;
    @Autowired
    private PersAttributeService persAttributeService;
    @Autowired
    private PersWiegandFmtService persWiegandFmtService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private PersCertificateService persCertificateService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private PersAttributeExtService persAttributeExtService;
    @Autowired
    private SystemDataMigrationService systemDataMigrationService;
    @Autowired
    private PersIdentityCardInfoService persIdentityCardInfoService;


    @Autowired
    private ProgressCache progressCache;

    @Override
    public boolean handlerTransfer(SystemDataTransferProcess process) {
        String transferType = "";
        long beginTime = System.currentTimeMillis();
        int beginProgress = process.getBeginProgress();
        int endProgress = process.getEndProgress();
        //共有6张表需要迁移
        int eachProgess = (endProgress-beginProgress)/6;


        String transferProgressMsg = "start Person Module data transfer......";



        //迁移职位数据
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Position Data Transfer"));
        positionHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Position Data Transfer End"));

        //迁移人员数据
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Person Data Transfer"));
        personHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Person Data Transfer End"));

        //人员卡数据迁移
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Card Data Transfer"));
        cardHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Card Data Transfer End"));

        //生物模版数据迁移
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "BioTemplate Data Transfer"));
        bioTemplateHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "BioTemplate Data Transfer End"));

        //人员证件数据迁移
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Certificate Data Transfer"));
        certificateHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Certificate Data Transfer End"));

        //离职人员迁移
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "LeavePerson Data Transfer"));
        leavePersonHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "LeavePerson Data Transfer End"));


        /*
        //韦根数据迁移
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "WiegandFmt Data Transfer"));
        wiegandFmtHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "WiegandFmt Data Transfer End"));
        
        //自定义属性数据迁移
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "Attribute Data Transfer"));
        attributeHandler();
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "Attribute Data Transfer End"));

        //自定义属性扩展数据迁移
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress + 1, "AttributeExt Data Transfer"));
        attributeExtHandler();*/
//        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress, "AttributeExt Data Transfer End"));
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(beginProgress+eachProgess*6+1, "Person Module Data End Transfer Time taken:"+(System.currentTimeMillis()-beginTime)+"ms"));
        return true;
    }

    @Override
    public void handlerUpgrade(SystemDataUpgradeProcess process) {

    }

    /**
     * 职位数据迁移
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/28 16:51
     */
    private void positionHandler() {
        //判断是否存在该表
        Boolean isExistTable=systemDataMigrationService.isExistTable("pers_position");
        if(isExistTable)
        {
            List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from pers_position");
            if(data != null){
                List<PersPositionItem> positionItems = new ArrayList<>();
                PersPositionItem positionItem = null;
                for (Map<String, Object> positionDatum : data) {
                    positionItem = new PersPositionItem();
                    positionItem.setId(MapUtils.getString(positionDatum, "id"));
                    positionItem.setCode(MapUtils.getString(positionDatum, "code"));
                    positionItem.setName(MapUtils.getString(positionDatum, "name"));
                    positionItem.setParentId(MapUtils.getString(positionDatum, "parent_id"));
                    positionItem.setSortNo(MapUtils.getInteger(positionDatum, "sort"));
                    positionItems.add(positionItem);
                }
                persPositionService.handlerTransfer(positionItems);
            }
        }

    }

    /**
     * 人员证件数据迁移
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/28 16:51
     */
    private void certificateHandler() {

            List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select pp.pin,pc.* from pers_certificate pc left join pers_person pp on pc.person_id=pp.id");
            if(data != null){
                List<PersCertificateItem> persCertificateItems = new ArrayList<>();
                PersCertificateItem persCertificateItem = null;
                for (Map<String, Object> positionDatum : data) {
                    persCertificateItem = new PersCertificateItem();
                    persCertificateItem.setId(MapUtils.getString(positionDatum, "id"));
                    persCertificateItem.setCertType(MapUtils.getString(positionDatum, "cert_type"));
                    persCertificateItem.setCertNumber(MapUtils.getString(positionDatum, "cert_number"));
                    persCertificateItem.setCertStatus(MapUtils.getShort(positionDatum, "cert_status"));
                    persCertificateItem.setPin(MapUtils.getString(positionDatum, "pin"));
                    persCertificateItems.add(persCertificateItem);
                }
                persCertificateService.handlerTransfer(persCertificateItems);
            }

    }


    /**
     * 离职人员迁移
     * <AUTHOR>
     * @Date 2019/8/14 18:05
     * @param
     * @return
     */
    private void leavePersonHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select pl.*,pd.code as dept_code from pers_leaveperson pl left join pers_department pd on pd.id = pl.dept_id");
        if(data != null){
            List<PersLeavePersonItem> persCertificateItems = new ArrayList<>();
            PersLeavePersonItem persLeavePersonItem = null;
            for (Map<String, Object> datum : data) {
                persLeavePersonItem = new PersLeavePersonItem();
                persLeavePersonItem.setId(MapUtils.getString(datum, "id"));
                persLeavePersonItem.setDeptCode(MapUtils.getString(datum, "dept_code"));
                persLeavePersonItem.setPin(MapUtils.getString(datum, "pin"));
                persLeavePersonItem.setName(MapUtils.getString(datum, "name"));
                persLeavePersonItem.setLastName(MapUtils.getString(datum, "last_name"));
                persLeavePersonItem.setLeaveDate(DateUtil.stringToDate(MapUtils.getString(datum, "leave_date")));
                persLeavePersonItem.setLeaveReason(MapUtils.getString(datum, "leave_reason"));
                persLeavePersonItem.setLeaveType(MapUtils.getInteger(datum, "leavetype_id"));
                persLeavePersonItem.setBeginDate(DateUtil.stringToDate(MapUtils.getString(datum, "hire_date")));
                persLeavePersonItem.setIsAttendance("1".equals(MapUtils.getString(datum, "is_attendance"))?true:false);
                persCertificateItems.add(persLeavePersonItem);
            }
            persLeavePersonService.handlerTransfer(persCertificateItems);
        }

    }


    /**
     * 韦根数据迁移
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/28 16:51
     */
    private void wiegandFmtHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from pers_wiegandfmt");
        List<PersWiegandFmtItem> wiegandFmtItems = new ArrayList<>();
        PersWiegandFmtItem wiegandFmtItem = null;
        for (Map<String, Object> wiegandFmtDatum : data) {
            wiegandFmtItem = new PersWiegandFmtItem();
            wiegandFmtItem.setId(MapUtils.getString(wiegandFmtDatum, "id"));
            wiegandFmtItem.setName(MapUtils.getString(wiegandFmtDatum, "name"));
            wiegandFmtItem.setWiegandCount(MapUtils.getShort(wiegandFmtDatum, "wiegand_count"));
            wiegandFmtItem.setWiegandMode(MapUtils.getShort(wiegandFmtDatum, "wiegand_mode"));
            wiegandFmtItem.setCardFmt(MapUtils.getString(wiegandFmtDatum, "card_fmt"));
            wiegandFmtItem.setParityFmt(MapUtils.getString(wiegandFmtDatum, "parity_fmt"));
            wiegandFmtItem.setIsDefaultFmt(MapUtils.getBoolean(wiegandFmtDatum, "is_default_fmt"));
            wiegandFmtItem.setSiteCode(MapUtils.getString(wiegandFmtDatum, "site_code"));
            wiegandFmtItems.add(wiegandFmtItem);
        }
        persWiegandFmtService.handlerTransfer(wiegandFmtItems);
    }

    /**
     * 自定义属性数据迁移
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/28 16:51
     */
    private void attributeHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from pers_attribute");
        List<PersAttributeItem> attributeItems = new ArrayList<>();
        PersAttributeItem attributeItem = null;
        for (Map<String, Object> attributeDatum : data) {
            attributeItem = new PersAttributeItem();
            attributeItem.setId(MapUtils.getString(attributeDatum, "id"));
            attributeItem.setAttrName(MapUtils.getString(attributeDatum, "attr_name"));
            attributeItem.setControlType(MapUtils.getString(attributeDatum, "control_type"));
            attributeItem.setAttrValue(MapUtils.getString(attributeDatum, "value_list"));
            attributeItem.setSqlStr(MapUtils.getString(attributeDatum, "sql_str"));
            attributeItem.setPositionX(MapUtils.getInteger(attributeDatum, "position_x"));
            attributeItem.setPositionY(MapUtils.getInteger(attributeDatum, "position_y"));
            attributeItem.setPersonType(MapUtils.getShort(attributeDatum, "person_type"));
            attributeItem.setFiledIndex(MapUtils.getInteger(attributeDatum, "filed_index"));
            attributeItem.setShowTable(MapUtils.getBoolean(attributeDatum, "show_table"));
            attributeItems.add(attributeItem);
        }
        persAttributeService.handlerTransfer(attributeItems);
    }

    /**
     * 自定义属性扩展数据迁移
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/28 16:51
     */
    private void attributeExtHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select * from pers_attribute_ext");
        List<PersAttributeExtItem> persAttributeExtItems = new ArrayList<>();
        PersAttributeExtItem attributeExtItem = null;
        for (Map<String, Object> attributeExtDatum : data) {
            attributeExtItem = new PersAttributeExtItem();
            attributeExtItem.setId(MapUtils.getString(attributeExtDatum, "id"));
            attributeExtItem.setPersonId(MapUtils.getString(attributeExtDatum, "person_id"));
            attributeExtItem.setAttrValue1(MapUtils.getString(attributeExtDatum, "attr_value1"));
            attributeExtItem.setAttrValue2(MapUtils.getString(attributeExtDatum, "attr_value2"));
            attributeExtItem.setAttrValue3(MapUtils.getString(attributeExtDatum, "attr_value3"));
            attributeExtItem.setAttrValue4(MapUtils.getString(attributeExtDatum, "attr_value4"));
            attributeExtItem.setAttrValue5(MapUtils.getString(attributeExtDatum, "attr_value5"));
            attributeExtItem.setAttrValue6(MapUtils.getString(attributeExtDatum, "attr_value6"));
            attributeExtItem.setAttrValue7(MapUtils.getString(attributeExtDatum, "attr_value7"));
            attributeExtItem.setAttrValue8(MapUtils.getString(attributeExtDatum, "attr_value8"));
            attributeExtItem.setAttrValue9(MapUtils.getString(attributeExtDatum, "attr_value9"));
            attributeExtItem.setAttrValue10(MapUtils.getString(attributeExtDatum, "attr_value10"));
            attributeExtItem.setAttrValue11(MapUtils.getString(attributeExtDatum, "attr_value11"));
            attributeExtItem.setAttrValue12(MapUtils.getString(attributeExtDatum, "attr_value12"));
            attributeExtItem.setAttrValue13(MapUtils.getString(attributeExtDatum, "attr_value13"));
            attributeExtItem.setAttrValue14(MapUtils.getString(attributeExtDatum, "attr_value14"));
            attributeExtItem.setAttrValue15(MapUtils.getString(attributeExtDatum, "attr_value15"));
            attributeExtItem.setAttrValue16(MapUtils.getString(attributeExtDatum, "attr_value16"));
            attributeExtItem.setAttrValue17(MapUtils.getString(attributeExtDatum, "attr_value17"));
            attributeExtItem.setAttrValue18(MapUtils.getString(attributeExtDatum, "attr_value18"));
            attributeExtItem.setAttrValue19(MapUtils.getString(attributeExtDatum, "attr_value19"));
            attributeExtItem.setAttrValue20(MapUtils.getString(attributeExtDatum, "attr_value20"));
            attributeExtItem.setAttrValue21(MapUtils.getString(attributeExtDatum, "attr_value21"));
            attributeExtItem.setAttrValue22(MapUtils.getString(attributeExtDatum, "attr_value22"));
            attributeExtItem.setAttrValue23(MapUtils.getString(attributeExtDatum, "attr_value23"));
            attributeExtItem.setAttrValue24(MapUtils.getString(attributeExtDatum, "attr_value24"));
            attributeExtItem.setAttrValue25(MapUtils.getString(attributeExtDatum, "attr_value25"));
            attributeExtItem.setAttrValue26(MapUtils.getString(attributeExtDatum, "attr_value26"));
            attributeExtItem.setAttrValue27(MapUtils.getString(attributeExtDatum, "attr_value27"));
            attributeExtItem.setAttrValue28(MapUtils.getString(attributeExtDatum, "attr_value28"));
            attributeExtItem.setAttrValue29(MapUtils.getString(attributeExtDatum, "attr_value29"));
            attributeExtItem.setAttrValue30(MapUtils.getString(attributeExtDatum, "attr_value30"));
            attributeExtItem.setAttrValue31(MapUtils.getString(attributeExtDatum, "attr_value31"));
            attributeExtItem.setAttrValue32(MapUtils.getString(attributeExtDatum, "attr_value32"));
            persAttributeExtItems.add(attributeExtItem);
        }
        persAttributeExtService.handlerTransfer(persAttributeExtItems);
    }

    /**
     * 卡数据迁移
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/28 16:51
     */
    private void cardHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select card.*,pers.pin from pers_card card left join pers_person pers on pers.id = card.person_id where pers.person_type!=1");
        if(data != null){
        	List<PersCardItem> cardItems = new ArrayList<>();
        	PersCardItem cardItem = null;
        	for (Map<String, Object> cardDatum : data) {
                cardItem = new PersCardItem();
                cardItem.setId(MapUtils.getString(cardDatum, "id"));
                cardItem.setPersonId(MapUtils.getString(cardDatum, "person_id"));
                cardItem.setPersonPin(MapUtils.getString(cardDatum, "pin"));
                cardItem.setCardType(MapUtils.getShort(cardDatum, "card_type"));
                cardItem.setCardOpType(MapUtils.getShort(cardDatum, "card_op_type"));
                cardItem.setCardState((short) (MapUtils.getShort(cardDatum, "card_state")==1?1:3));
                cardItem.setCardNo(MapUtils.getString(cardDatum, "card_no"));
                cardItem.setIssueTime(DateUtil.getCurrentTime());
//                cardItem.setStartTime(MapUtils.getString(cardDatum,"start_time"));
//                cardItem.setEndTime(MapUtils.getString(cardDatum,"end_time"));
                cardItem.setLogicalCardNo(MapUtils.getString(cardDatum, "logical_card_no"));
                cardItems.add(cardItem);
            }
            persCardService.handlerTransfer(cardItems);
        }
    }

    /**
     * 生物模板数据迁移
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/28 16:51
     */
    private void bioTemplateHandler() {
        List<Map<String, Object>> data = systemDataMigrationService.excuteSql("select bio.*,pers.pin from pers_biotemplate bio left join pers_person pers on pers.id = bio.person_id  where pers.person_type!=1");
        if(data != null){
        	List<PersBioTemplateItem> bioTemplateItems = new ArrayList<>();
            PersBioTemplateItem bioTemplateItem = null;
            for (Map<String, Object> bioTemplateDatum : data) {
                bioTemplateItem = new PersBioTemplateItem();
                bioTemplateItem.setId(MapUtils.getString(bioTemplateDatum, "id"));
                bioTemplateItem.setPersonId(MapUtils.getString(bioTemplateDatum, "person_id"));
//                bioTemplateItem.setc(MapUtils.getString(bioTemplateDatum, "create_operator"));
//                bioTemplateItem.setPersonId(MapUtils.getString(bioTemplateDatum, "create_time"));
                bioTemplateItem.setValidType(MapUtils.getShort(bioTemplateDatum, "valid_type"));
                short bioType = MapUtils.getShort(bioTemplateDatum, "bio_type");
                short templateNo = MapUtils.getShort(bioTemplateDatum, "template_no");
                short templateNoIndex = MapUtils.getShort(bioTemplateDatum, "template_no_index");
                bioTemplateItem.setBioType(bioType);
                //胁迫指纹的话 需要-16
                bioTemplateItem.setTemplateNo(templateNo>16? (short) (templateNo - 16) :templateNo);
                bioTemplateItem.setTemplateNoIndex(templateNoIndex);
                //旧架构数据转新架构数据需要对应做数据转换
                if(BaseConstants.BaseBioType.FACE_BIO_TYPE == bioType && PersConstants.PERS_BIOTEMPLATE_DEF_NO != templateNo){
                	 bioTemplateItem.setTemplateNo(PersConstants.PERS_BIOTEMPLATE_DEF_NO);
                }
                if(BaseConstants.BaseBioType.FP_BIO_TYPE == bioType && PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX != templateNoIndex){
                	bioTemplateItem.setTemplateNoIndex(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
               }
               //指静脉
                if(BaseConstants.BaseBioType.VEIN_BIO_TYPE == bioType && PersConstants.PERS_BIOTEMPLATE_DEF_DATA_NO_INDEX == templateNoIndex){
                    bioTemplateItem.setTemplateNoIndex(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
                }
                bioTemplateItem.setVersion(MapUtils.getString(bioTemplateDatum, "version"));
                bioTemplateItem.setTemplate(MapUtils.getString(bioTemplateDatum, "template"));
                bioTemplateItem.setPersonPin(MapUtils.getString(bioTemplateDatum, "pin"));
                //默认胁迫指纹 templateNo>16为胁迫指纹
                bioTemplateItem.setDuress(templateNo>16 ? true:false);
                bioTemplateItems.add(bioTemplateItem);
            }
            persBioTemplateService.handlerTransfer(bioTemplateItems);
        }
    }

    /**
     * 人员数据迁移
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/28 16:51
     */
    private void personHandler() {
        //判断是否存在职位表
        Boolean isExistTable=systemDataMigrationService.isExistTable("pers_position");
        String sql="";
        if(isExistTable)
        {
            //3150的数据库人员表
             sql="select  pers.*,dept.code as dept_code,pos.code as position_code from pers_person pers left join pers_department dept on dept.id = pers.dept_id left join pers_position pos on pos.id=pers.position_id where pers.person_type!=1 ";
        }
        else {
            sql="select  pers.*,dept.code as dept_code from pers_person pers left join pers_department dept on dept.id = pers.dept_id  where pers.person_type!=1 ";
        }

        List<Map<String, Object>> data = systemDataMigrationService.excuteSql(sql);
        if(data != null){
        	List<PersPersonItem> personItems = new ArrayList<>();
        	PersPersonItem personItem = null;
        	for (Map<String, Object> personDatum : data) {
                 personItem = new PersPersonItem();
                 personItem.setId(MapUtils.getString(personDatum, "id"));
                 personItem.setDeptId(MapUtils.getString(personDatum, "dept_id"));
                 personItem.setDeptCode(MapUtils.getString(personDatum, "dept_code"));
                 personItem.setPositionCode(isExistTable?MapUtils.getString(personDatum, "position_code"):"");
                 personItem.setPin(MapUtils.getString(personDatum, "pin"));
                 personItem.setName(MapUtils.getString(personDatum, "name"));
                 personItem.setLastName(MapUtils.getString(personDatum, "last_name"));
                 personItem.setGender(MapUtils.getString(personDatum, "gender"));
                 personItem.setPhotoPath(MapUtils.getString(personDatum, "photo_path"));
                 personItem.setStatus(MapUtils.getShort(personDatum, "status"));
                 personItem.setPersonType(MapUtils.getShort(personDatum, "person_type"));
                 personItem.setBirthday(DateUtil.stringToDate(MapUtils.getString(personDatum,"birthday")));
                 personItem.setMobilePhone(MapUtils.getString(personDatum, "mobile_phone"));
                 personItem.setEmail(MapUtils.getString(personDatum, "email"));
                 personItem.setPersonPwd(MapUtils.getString(personDatum, "person_pwd"));
                 personItem.setSsn(MapUtils.getString(personDatum, "ssn"));
                 personItem.setCarPlate(MapUtils.getString(personDatum, "car_plate"));
                 personItem.setSelfPwd(MapUtils.getString(personDatum, "self_pwd"));
                 personItem.setExceptionFlag(MapUtils.getShort(personDatum, "exception_flag"));
                 personItem.setIsSendMail(MapUtils.getBoolean(personDatum, "is_sendmail"));
                 personItem.setHireDate(DateUtil.stringToDate(MapUtils.getString(personDatum,"hire_date")));
                 personItem.setIdCard(MapUtils.getString(personDatum, "id_card"));
                 personItem.setIdCardPhysicalNo(MapUtils.getString(personDatum, "id_card_physical_no"));
                 personItem.setIsFrom(MapUtils.getString(personDatum, "is_from"));
                 personItem.setCreateTime(DateUtil.stringToDate(MapUtils.getString(personDatum, "create_time")));
                 personItems.add(personItem);
             }
             persPersonService.handlerTransfer(personItems);
        }
    }



    private void updateProcessInfo(int transferProgress, int totalTime, String transferProgressMsg, String msgColor) {

//        try {
//            //int lastTime = (int) ((100 - transferProgress) * 0.01 * totalTime);
//            int lastTime = 10;
//            log.info(transferProgressMsg);
//            progressCache.setProcess(new ProcessBean(transferProgress, transferProgress, lastTime,"<font color='"+ msgColor +"'>" + transferProgressMsg + "</font><br/>", transferProgressMsg));
//            Thread.sleep(300);
//        } catch (InterruptedException e) {
//            e.printStackTrace();
//        }
    }
}
