/**
 * File Name: PersPerson Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;

import javax.transaction.Transactional;

/**
 * 对应百傲瑞达 PersPersonDao
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
public interface PersPersonDao extends BaseDao<PersPerson, String> {
    /**
     * 根据ids查询
     *
     * @param ids
     * @return
     */
    List<PersPerson> findByIdIn(Collection<String> ids);

    /**
     * 根据ids查询
     *
     * @param pins
     * @return
     */
    List<PersPerson> findByPinIn(Collection<String> pins);

    /**
     * 根据Pin查询
     *
     * @param pin
     * @return
     */
    PersPerson findByPin(String pin);

    /**
     * 根据人员类型查询数量
     *
     * @param personType
     * @return
     */
    Integer countByPersonType(short personType);

    /**
     * 根据人员类型和部门id查询数量
     *
     * @param personType
     * @return
     */
    Integer countByPersonTypeAndDeptIdIn(short personType, List<String> deptIdList);

    /**
     * 根据人员类型和用户id查询数量
     *
     * @param personType
     * @return
     */
    @Query(
        value = "SELECT COUNT(0) FROM PERS_PERSON t where t.PERSON_TYPE=?1 and t.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=?2)",
        nativeQuery = true)
    Integer countByPersonTypeAndUserId(short personType, String userId);

    /**
     * 统计性别数量
     *
     * @return Object[] 下标0：为 maleCount(男) feMaleCount(女) 下标1：count(数量)
     */
    @Query(
        value = "SELECT case e.gender when 'M' then 'maleCount' when 'F' then 'feMaleCount' when 'U' then 'unknownCount' else '' end,COUNT(e.gender) FROM PersPerson  e WHERE e.status=0 and e.personType=0 GROUP BY e.gender")
    List<Object[]> countGender();

    @Query(
        value = "SELECT case e.gender when 'M' then 'maleCount' when 'F' then 'feMaleCount' when 'U' then 'unknownCount' else '' end,COUNT(e.gender) FROM PersPerson  e "
            + "WHERE e.status=0 and e.personType=0 and e.deptId in (?1) GROUP BY e.gender")
    List<Object[]> countGenderByDeptIds(List<String> deptIdList);

    @Query(
        value = "SELECT case e.gender when 'M' then 'maleCount' when 'F' then 'feMaleCount' when 'U' then 'unknownCount' else '' end,COUNT(e.gender) FROM PersPerson  e "
            + "WHERE e.status=0 and e.personType=0 and e.deptId in (SELECT dept.id FROM AuthDepartment dept LEFT JOIN dept.users u WHERE u.id=?1) GROUP BY e.gender")
    List<Object[]> countGenderByUserId(String userId);

    /**
     * 查询密码相同的数量
     *
     * @param personPwd
     * @return
     */
    Long countByPersonPwd(String personPwd);

    /**
     * 根据权限组ID和人员id查询人员信息
     *
     * @author: mingfa.zheng
     * @date: 2018/4/26 20:57
     * @return:
     */
    @Query(value = "select distinct p.ID from PERS_PERSON p "
        + "where p.ID in (?1) and p.ID not in (select ppl.PERSON_ID from PERS_PERSON_LINK ppl where ppl.TYPE='ACC_LEVEL' and ppl.LINK_ID in (?2))",
        nativeQuery = true)
    List<String> getPersonIdByPersonIdAndLevelId(List<String> personIds, List<String> levelIds);

    /**
     * 根据人员id、权限组id和关联其他模块的类型查询人员id
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/11/21 10:26
     * @param personIds 人员id
     * @param levelIds 权限组id
     * @param type 模块关联类型
     */
    @Query(value = "select distinct p.ID from PERS_PERSON p "
        + "where p.ID in (?1) and p.ID not in (select ppl.PERSON_ID from PERS_PERSON_LINK ppl where ppl.TYPE=(?3) and ppl.LINK_ID in (?2))",
        nativeQuery = true)
    List<String> getPersonIdByPersonIdAndLevelIdAndLinkType(List<String> personIds, List<String> levelIds, String type);

    /**
     * 计算该职位下的人员数
     *
     * @param positionId
     * @return long
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/9 21:03
     */
    Long countByPersPosition_Id(String positionId);

    /**
     * 计算部门下的人员数
     *
     * @param deptIds
     * @return long
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/9 21:26
     */
    Integer countByDeptIdIn(Collection<String> deptIds);

    /**
     * 统计所有部门下的人员数量
     *
     * @return
     */
    @Query("SELECT t.deptId,count(t.id) FROM PersPerson t GROUP BY t.deptId")
    List<Object[]> countOrderByDeptId();

    /**
     * 根据pin号、姓名拼音查找人员
     *
     * <AUTHOR>
     * @since 2018年6月19日 上午11:04:09
     * @param personType
     * @param status
     * @param pin
     * @param nameSpell
     * @param pageable
     * @return
     * <AUTHOR>
     * @since 2018年6月19日 上午11:04:09
     */
    @Query(
        value = "SELECT p FROM PersPerson p WHERE p.personType=?1 AND p.status=?2 AND (p.pin like %?3% OR p.nameSpell like %?4%)")
    List<PersPerson> getByPinAndNameSpell(short personType, short status, String pin, String nameSpell,
        Pageable pageable);

    /**
     * 获取pin最大长度
     *
     * @return long
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/6/20 20:49
     */
    @Query(value = "SELECT MAX(LENGTH(p.pin)) from PersPerson p")
    Long getMaxPinLength();

    /**
     * 获取最大pin
     *
     * @return long
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/6/20 20:49
     */
    @Query(value = "SELECT MAX(p.pin) from PersPerson p")
    Long getMaxPin();

    /**
     * 查询人员编码含有字母的人员数
     *
     * @param pinLetter pin是否包含字母
     * @return int
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/22 9:48
     */
    Integer countByPinLetter(boolean pinLetter);

    /**
     * 根据pin和name进行like 查询
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 21:18
     * @param pin
     * @param name
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersPerson>
     */
    List<PersPerson> findByPinLikeOrNameLike(String pin, String name);

    /**
     * 通过部门ID获取人员集合
     *
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/16 14:10
     * @param deptIds
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersPerson>
     */
    List<PersPerson> findByDeptIdIn(Collection<String> deptIds);

    /**
     * 查询pin是否字符进行查询编号
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 21:19
     * @param pinLetter
     * @return java.util.List<java.lang.String>
     */
    @Query("SELECT p.pin from PersPerson p where p.pinLetter = ?1")
    List<String> getPinByPinLetter(boolean pinLetter);

    /**
     * 查询过滤人员id相同密码的数量
     *
     * @param personPwd
     * @param personId
     * @return
     */
    Long countByPersonPwdAndIdNot(String personPwd, String personId);

    /**
     * 计算pin已存在的人员
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/18 17:38
     * @param pins
     * @return java.lang.Integer
     */
    Integer countByPinIn(Collection<String> pins);

    /**
     * 统计已设置密码人数
     *
     * @return
     */
    @Query(value = "SELECT count(p.id) FROM Pers_Person p WHERE p.person_type = ?1 AND p.person_pwd <>'' ",
        nativeQuery = true)
    List<Integer> getHasPwdPersonCountByPersonType(short personType);

    @Query("select count(t.id) from PersPerson t where t.personType = ?1 and t.photoPath is not null and t.photoPath <> ''")
    Long getHasPhotoCountByPersonType(short personType);

    /**
     * 根据部门id查询人员id集合
     *
     * @param deptIds
     * @return
     */
    @Query("SELECT p.id from PersPerson p where p.deptId in (?1)")
    List<String> getPersonIdsByDeptIdIn(List<String> deptIds);

    @Query("SELECT p.id from PersPerson p where p.deptId in (?1) and p.enabledCredential = true")
    List<String> getEnabledCredentialPersonIdsByDeptIds(List<String> deptIds);

    @Query(
        value = "select p.* from PERS_PERSON_LINK ppl left join PERS_PERSON p on p.ID = ppl.PERSON_ID "
            + "where (p.NAME like %?1% or p.MOBILE_PHONE like %?2% or p.AUTH_DEPT_ID in (?3)) and ppl.TYPE=?4",
        nativeQuery = true)
    List<PersPerson> findByNameOrMobilePhoneOrDeptId(String name, String mobilePhone, List<String> deptIdList,
        String type);

    /**
     * 根据人员id获取人员（针对下发使用）
     *
     * @param personIds
     * @return
     */
    @Query("SELECT new PersPerson (p.id, p.pin, p.name, p.lastName, p.personPwd, p.personType, p.photoPath, p.idCard, p.enabledCredential) from PersPerson p where p.id in (?1)")
    List<PersPerson> getPersonForDevByIds(Collection<String> personIds);

    /**
     * 根据人员编号查找照片路径
     */
    @Query("SELECT p.photoPath from PersPerson p where p.pin = ?1")
    String findPhotoPathByPin(String pin);

    /**
     * 根据部门id获取邮箱
     *
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/10 14:42
     * @param deptIds
     * @return java.util.List<java.lang.String>
     */
    @Query("SELECT DISTINCT (p.email) FROM PersPerson p WHERE p.deptId in (?1)")
    List<String> getEmailsByDeptIdIn(Collection<String> deptIds);

    /**
     * 根据区域id获取邮箱
     *
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/11 14:42
     * @param areaIds
     * @return java.util.List<java.lang.String>
     */
    @Query(
        value = "SELECT DISTINCT (p.EMAIL) FROM PERS_PERSON p LEFT JOIN PERS_PERSON_LINK t ON p.ID = t.PERSON_ID WHERE t.LINK_ID IN (?1)",
        nativeQuery = true)
    List<String> getEmailsByAreaIdIn(Collection<String> areaIds);

    /**
     * 根据手机号查询
     *
     * <AUTHOR>
     * @since 2019年6月21日 下午5:15:54
     * @param mobile
     */
    PersPerson findByMobilePhone(String mobile);

    /**
     * 根据邮箱查询
     *
     * <AUTHOR>
     * @since 2019年6月21日 下午5:54:04
     * @param email
     * @return
     */
    PersPerson findByEmail(String email);

    /**
     * 根据人员isFrom查找isFrom为空或者不为persFrom的人员的pin
     *
     * <AUTHOR>
     * @since 2019-08-23 11:42
     * @Param [persFrom]
     * @return
     */
    @Query(value = "select t.pin from PersPerson t where isFrom is null or t.isFrom != ?1")
    List<String> getPinIsNotFromAD(String persFrom);

    List<PersPerson> findByIsFrom(String persFrom);

    List<PersPerson> getByEmail(String email);

    List<PersPerson> getByEmailIn(Collection<String> emails);

    List<PersPerson> getByMobilePhoneIn(Collection<String> mobilePhones);

    @Query(value = "select t.pin from PersPerson t where t.id in (?1)")
    List<String> getPinsByIds(List<String> personIds);

    /**
     * 获取所有人员编号
     *
     * @return java.util.List<java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 20:48
     * @since 1.0.0
     */
    @Query("SELECT p.pin from PersPerson p")
    List<String> findAllPin();

    @Query("select t from PersPerson t where t.mobilePhone is not null and t.mobilePhone <> ''")
    List<PersPerson> findByMobilePhoneIsNotNull();

    @Query("select t from PersPerson t where t.email is not null and t.email <> ''")
    List<PersPerson> findByEmailIsNotNull();

    @Query("select t from PersPerson t where t.personPwd is not null and t.personPwd <> ''")
    List<PersPerson> findByPersonPwdIsNotNull();

    @Query("select t from PersPerson t where t.idCard is not null and t.idCard <> ''")
    List<PersPerson> findByIdCardIsNotNull();

    @Query("select t from PersPerson t where t.idCardPhysicalNo is not null and t.idCardPhysicalNo <> ''")
    List<PersPerson> findByIdCardPhysicalNoIsNotNull();

    /**
     * 根据人员id与照片非空判断获取人数
     *
     * @param personIds
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021-07-02 17:42
     * @since 1.0.0
     */
    @Query("select count(t.id) from PersPerson t where t.id in (?1) and t.photoPath is not null")
    Integer countByIdsAndPhotoIsNotNull(List<String> personIds);

    Integer countByCreateTimeGreaterThanEqualAndCreateTimeLessThanEqual(Date beginTime, Date endTime);

    @Query("select count(id) from PersPerson")
    Long countPerson();

    @Query("select p from PersPerson p order by createTime asc")
    List<PersPerson> getPersonByPage(Pageable pageable);

    /**
     * 通过卡号查找相关人员
     *
     * <AUTHOR>
     * @date 2021-08-26 14:24
     * @param cardNo
     * @since 1.0.0
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersPerson>
     */
    @Query("SELECT t FROM PersPerson t LEFT JOIN PersCard c ON t.id =c.personId WHERE c.cardNo in (?1) AND c.cardState=1")
    List<PersPerson> findByPersonCardNo(Collection<String> cardNo);

    @Query(
        value = "SELECT t from PersPerson t where t.deptId in (?1) and t.id not in (SELECT lp.personId from PersPersonnallistPerson lp "
            + "LEFT JOIN PersPersonnalList l on lp.personnallistId=l.id where l.type = ?2 and l.id <> ?3)")
    List<PersPerson> getPersonsByDeptIdInAndListTypeFiletrAndListIdFilter(List<String> deptIds,
        String personnalListType, String personnalListId);

    @Query("SELECT p.pin from PersPerson p where p.deptId = ?1")
    List<String> findPinsByDeptId(String deptId);

    @Query(
        value = "SELECT t.* FROM PERS_PERSON t WHERE t.AUTH_DEPT_ID IN (?1) AND t.PERSON_TYPE=0 AND t.ID NOT IN (SELECT c.PERSON_ID FROM PERS_CARD c WHERE c.CARD_STATE=1 GROUP BY c.PERSON_ID)",
        nativeQuery = true)
    List<PersPerson> findNoCardIdsByDeptIds(List<String> deptIds);

    @Modifying(clearAutomatically = true)
    @Query("update PersPerson set enabledCredential= :enabledCredential where enabledCredential is null")
    void updateEnabledCredential(@Param("enabledCredential") Boolean enabledCredential);

    Integer countByIdInAndEnabledCredential(List<String> personIds, Boolean enabledCredential);

    @Query("SELECT t FROM PersPerson t LEFT JOIN PersCard c ON t.id =c.personId WHERE t.updateTime >= ?1 and c.isFrom = ?2")
    List<PersPerson> findByUpdateTimeAndCardFrom(Date updateTime, String cardIsFrom);

    @Query("SELECT t.pin FROM PersPerson t LEFT JOIN PersCard c ON t.id =c.personId WHERE t.id in (?1) and c.isFrom = ?2")
    List<String> findPinByIdsAndCardFrom(Collection<String> idList, String cardIsFrom);

    @Query(
        value = "select t.* from PERS_PERSON t  where lower(t.NAME) like %?1% or lower(t.LAST_NAME) like %?1% or t.MOBILE_PHONE = ?2",
        nativeQuery = true)
    List<PersPerson> findByNameLikeOrMobilePhone(String nameLike, String mobilePhone);


    @Modifying
    @Transactional
    @Query(value = "update PERS_PERSON set wechat_open_id = ?2 where pin = ?1",nativeQuery = true)
    public void updatePersonByWechatOpenId(String pin ,String openid);

}
