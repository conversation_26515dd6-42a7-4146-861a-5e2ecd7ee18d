/**
 * File Name: PersCertificate Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 PersCertificate
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_CERTIFICATE")
@Getter
@Setter
@Accessors(chain = true)
public class PersCertificate extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @ManyToOne
    @JoinColumn(name = "PERSON_ID")
    private PersPerson persPerson;

    /**
     * 证件类型
     */
    @Column(name = "CERT_TYPE", length = 20)
    private String certType;

    /**
     * 证件号码
     */
    @Column(name = "CERT_NUMBER", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String certNumber;

    /**
     * 证件状态
     */
    @Column(name = "CERT_STATUS")
    private Short certStatus;
}