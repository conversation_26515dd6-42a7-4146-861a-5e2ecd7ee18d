package com.zkteco.zkbiosecurity.pers.service.impl;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonSelectItem;
import com.zkteco.zkbiosecurity.workflow.service.Workflow4PersPersonService;
import com.zkteco.zkbiosecurity.workflow.vo.Workflow4PersPersonItem;
import com.zkteco.zkbiosecurity.workflow.vo.Workflow4PersPersonSelectItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 工作流人事接口实现
 *
 * <AUTHOR>
 * @date 2019/5/29
 *
 */
@Service
public class Workflow4PersPersonServiceImpl implements Workflow4PersPersonService {

    @Autowired
    private PersPersonService persPersonService;

    @Override
    public Workflow4PersPersonItem getItemById(String companyId, String personId) {
        PersPersonItem persPersonItem = persPersonService.getItemById(personId);
        if(Objects.nonNull(persPersonItem)) {
            Workflow4PersPersonItem workflow4PersPersonItem = ModelUtil.copyProperties(persPersonItem, new Workflow4PersPersonItem());
            return workflow4PersPersonItem;
        }
        return null;
    }

    @Override
    public List<Workflow4PersPersonItem> getByCondition(Workflow4PersPersonItem condition) {
        PersPersonItem persPersonItem = ModelUtil.copyProperties(condition, new PersPersonItem());
        List<PersPersonItem> persPersonItemList = persPersonService.getByCondition(persPersonItem);
        List<Workflow4PersPersonItem> list = new ArrayList<>();
        if(!CollectionUtil.isEmpty(persPersonItemList)){
            list = ModelUtil.copyListProperties(persPersonItemList, Workflow4PersPersonItem.class);
        }
        return list;
    }

    @Override
    public List<Workflow4PersPersonItem> getByPinLikeOrNameLike(String companyId, String pin, String name) {
        List<PersPersonItem> persPersonItemList = persPersonService.getByPinLikeOrNameLike(pin, name);
        List<Workflow4PersPersonItem> list = new ArrayList<>();
        if(!CollectionUtil.isEmpty(persPersonItemList)) {
            list = ModelUtil.copyListProperties(persPersonItemList, Workflow4PersPersonItem.class);
        }
        return list;
    }

    @Override
    public Pager findPersonSelectItem(String sessionId, Workflow4PersPersonSelectItem condition, int pageNo, int pageSize) {
        PersPersonSelectItem persPersonSelectItem = ModelUtil.copyProperties(condition, new PersPersonSelectItem());
        Pager pager = persPersonService.findPersonSelectItem(sessionId, persPersonSelectItem, pageNo, pageSize);
        List<PersPersonSelectItem> data = (List<PersPersonSelectItem>) pager.getData();
        if(!CollectionUtil.isEmpty(data)){
            List<Workflow4PersPersonSelectItem> list = ModelUtil.copyListProperties(data, Workflow4PersPersonSelectItem.class);
            pager.setData(list);
        }
        return pager;
    }
}
