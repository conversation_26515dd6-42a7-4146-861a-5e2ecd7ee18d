/**
 * File Name: Pers<PERSON>eave<PERSON>erson Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersLeavePerson;

/**
 * 对应百傲瑞达 PersLeavePersonDao
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersLeavePersonDao extends BaseDao<PersLeavePerson, String> {
    /**
     * 根据人员Pin进行查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/9 14:00
     * @param pin
     * @return com.zkteco.zkbiosecurity.pers.model.PersLeavePerson
     */
    List<PersLeavePerson> findByPinOrderByCreateTimeDesc(String pin);

    /**
     * 根据部门Id获取离职人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/23 11:12
     * @param deptIds
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersLeavePerson>
     */
    List<PersLeavePerson> findByDeptIdIn(Collection<String> deptIds);

    /**
     * 根据部门id获取离职人总数
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/23 18:38
     * @param deptIds
     * @return java.lang.Integer
     */
    Integer countByDeptIdIn(Collection<String> deptIds);

    /**
     * 根据pin查询离职人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/29 18:13
     * @param pinList
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersLeavePerson>
     */
    List<PersLeavePerson> findByPinIn(Collection<String> pinList);

    /**
     * 根据pin查询离职人员pins
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/29 18:13
     * @param pinList
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersLeavePerson>
     */
    @Query(value = "select t.pin from PersLeavePerson t where t.pin in (?1)")
    List<String> getPinListByPinIn(Collection<String> pinList);

    PersLeavePerson findByPin(String pin);

    Integer countByCreateTimeGreaterThanEqualAndCreateTimeLessThanEqual(Date beginTime, Date endTime);
}