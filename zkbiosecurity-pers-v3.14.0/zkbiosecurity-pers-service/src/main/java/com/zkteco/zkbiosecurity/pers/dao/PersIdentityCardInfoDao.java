/**
 * File Name: PersIdentityCardInfo
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import com.zkteco.zkbiosecurity.pers.model.PersIdentityCardInfo;

/**
 * 对应百傲瑞达 PersIdentityCardInfoDao
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersIdentityCardInfoDao extends BaseDao<PersIdentityCardInfo, String> {
    /**
     * 根据物理卡号查询
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/11 10:48
     * @param physicalNo
     * @return com.zkteco.zkbiosecurity.pers.model.PersIdentityCardInfo
     */
    PersIdentityCardInfo findByPhysicalNo(String physicalNo);

    /**
     * 根据身份证号码查询
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/11 10:53
     * @param idCard
     * @return com.zkteco.zkbiosecurity.pers.model.PersIdentityCardInfo
     */
    PersIdentityCardInfo findByIdCard(String idCard);
}