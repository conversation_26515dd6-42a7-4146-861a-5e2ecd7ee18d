package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022-10-20 15:14
 * @since 1.0.0
 */
@Slf4j
@Component
public class PersVer3_11_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Value("${system.language:zh_CN}")
    private String language;
    @Autowired
    private PersPersonService persPersonService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v3.11.0";
    }

    @Override
    public boolean executeUpgrade() {

        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("PersPerson");
        if (subMenuItem != null) {
            /** --------------------------- 人员启用 --------------------------- */
            AuthPermissionItem subButtonItem = new AuthPermissionItem("PersPersonEnabled", "common_enable",
                "pers:person:enabled", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 21);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

            /** --------------------------- 人员禁用 --------------------------- */
            subButtonItem = new AuthPermissionItem("PersPersonDisable", "common_disable", "pers:person:disable",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 22);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

            /** --------------------------- 启用APP登录 --------------------------- */
            subButtonItem = new AuthPermissionItem("PersPersonAppLoginEnabled", "pers_applogin_enabled",
                "pers:person:enabledApplogin", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 23);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

            /** --------------------------- 禁用APP登录 --------------------------- */
            subButtonItem = new AuthPermissionItem("PersPersonAppLoginDisable", "pers_applogin_disable",
                "pers:person:disableApplogin", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 24);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

            /** --------------------------- 调整部门-是否有权限调整权限 --------------------------- */
            subButtonItem =
                new AuthPermissionItem("PersPersonDeptChangeAndChangeLevel", "pers_personDepartment_changeLevel",
                    "pers:personDepartment:changeLevel", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            /** --------------------------- 修复bioaccess升级 --------------------------- */
            subButtonItem = authPermissionService.getItemByCode("PersPersonLeave");
            if (subButtonItem == null) {
                subButtonItem = new AuthPermissionItem("PersPersonLeave", "pers_person_leave", "pers:person:leave",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
            }
            subButtonItem = authPermissionService.getItemByCode("PersPersonPositionChange");
            if (subButtonItem == null) {
                subButtonItem = new AuthPermissionItem("PersPersonPositionChange", "pers_position_change",
                    "pers:person:batchPositionChange", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
            }
            subButtonItem = authPermissionService.getItemByCode("PersPersonResetSelfPwd");
            if (subButtonItem == null) {
                subButtonItem = new AuthPermissionItem("PersPersonResetSelfPwd", "pers_person_resetSelfPwd",
                    "pers:person:resetSelfPwd", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }
            subButtonItem = authPermissionService.getItemByCode("PersPersonCardPrint");
            if (subButtonItem == null) {
                subButtonItem = new AuthPermissionItem("PersPersonCardPrint", "pers_person_cardprint",
                    "pers:person:cardPrint", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 11);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }
        }
        // 设置启禁用默认值
        persPersonService.updateEnabledCredential();
        // if (!language.equalsIgnoreCase("zh_CN")) {
        // subMenuItem = authPermissionService.getItemByCode("PersCard");
        // if (subMenuItem != null) {
        // AuthPermissionItem subButtonItem = new AuthPermissionItem("PersCardAcms", "pers_batchIssCard_acms",
        // "pers:card:acms", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
        // subButtonItem.setParentId(subMenuItem.getId());
        // authPermissionService.initData(subButtonItem);
        // }
        // }
        /** --------------------------- 修复bioaccess升级 --------------------------- */
        AuthPermissionItem subMenuItem1 = authPermissionService.getItemByCode("PersPersonManager");
        if (subMenuItem1 != null) {
            /** --------------------------- 职位 --------------------------- */
            subMenuItem = authPermissionService.getItemByCode("PersPosition");
            if (subMenuItem == null) {
                /** --------------------------- 职位 --------------------------- */
                subMenuItem = new AuthPermissionItem("PersPosition", "pers_position", "pers:position",
                    AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 3);
                subMenuItem.setParentId(subMenuItem1.getId());
                subMenuItem.setActionLink("persPosition.do");
                subMenuItem = authPermissionService.initData(subMenuItem);

                AuthPermissionItem subButtonItem = new AuthPermissionItem("PersPositionRefresh", "common_op_refresh",
                    "pers:position:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersPositionAdd", "common_op_new", "pers:position:add",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersPositionEdit", "common_op_edit", "pers:position:edit",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersPositionDel", "common_op_del", "pers:position:del",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersPositionExport", "common_op_export", "pers:position:export",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersPositionImport", "common_op_import", "pers:position:import",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersPositionExportTemplate", "pers_position_downloadTemplate",
                    "pers:position:exportTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }
            /** --------------------------- 离职人员 --------------------------- */
            subMenuItem = authPermissionService.getItemByCode("PersLeave");
            if (subMenuItem == null) {
                subMenuItem = new AuthPermissionItem("PersLeave", "pers_leave", "pers:leavePerson",
                    AuthContants.RESOURCE_TYPE_MENU, ZKConstant.TRUE, 4);
                subMenuItem.setParentId(subMenuItem1.getId());
                subMenuItem.setActionLink("persLeavePerson.do");
                subMenuItem = authPermissionService.initData(subMenuItem);

                AuthPermissionItem subButtonItem = new AuthPermissionItem("PersLeaveRefresh", "common_op_refresh",
                    "pers:leavePerson:refresh", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 1);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersLeaveEdit", "common_op_edit", "pers:leavePerson:edit",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersLeaveReinstated", "pers_leavePerson_reinstated",
                    "pers:leavePerson:reinstated", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 3);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersLeaveDel", "common_op_del", "pers:leavePerson:del",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);

                subButtonItem = new AuthPermissionItem("PersLeaveExport", "common_op_export", "pers:leavePerson:export",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }
        }
        /** --------------------------- 卡 挂失/解挂 --------------------------- */
        subMenuItem = authPermissionService.getItemByCode("PersCard");
        if (subMenuItem != null) {
            AuthPermissionItem subButtonItem = authPermissionService.getItemByCode("PersCardLoss");
            if (subButtonItem == null) {
                subButtonItem = new AuthPermissionItem("PersCardLoss", "pers_lossCard_entity",
                    "pers:card:batchCardLoss", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }
            subButtonItem = authPermissionService.getItemByCode("PersCardRevert");
            if (subButtonItem == null) {
                subButtonItem = new AuthPermissionItem("PersCardRevert", "pers_revertCard_entity",
                    "pers:card:batchCardRevert", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }
        }
        /** --------------------------- 韦根 卡格式测试 --------------------------- */
        subMenuItem = authPermissionService.getItemByCode("PersWiegandFmtItem");
        if (subMenuItem != null) {
            AuthPermissionItem subButtonItem = authPermissionService.getItemByCode("PersWiegandFmtItemTest");
            if (subButtonItem == null) {
                subButtonItem = new AuthPermissionItem("PersWiegandFmtItemTest", "pers_wgFmt_cardFormatTesting",
                    "pers:wiegandFmt:test", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 4);
                subButtonItem.setParentId(subMenuItem.getId());
                subButtonItem = authPermissionService.initData(subButtonItem);
            }
        }
        return true;
    }

}
