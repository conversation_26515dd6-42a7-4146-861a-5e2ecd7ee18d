/**
 * File Name: PersPosition
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersPosition;
import com.zkteco.zkbiosecurity.pers.vo.PersPositionItem;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/**
 * 对应百傲瑞达 PersPositionDao
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersPositionDao extends BaseDao<PersPosition, String> {

    /**
     * 根据名称进行查询
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/11 16:52
     * @param name
     * @return com.zkteco.zkbiosecurity.pers.model.PersPosition
     */
    PersPosition findByName(String name);

    /**
     * 查找子职位数量
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/9 20:44
     * @param parentId
     * @return int
     */
    long countByParent_Id(String parentId);

    /**
     * 查找code
     * @param code
     * @return
     */
    PersPosition findByCode(String code);

    /**
     * 根据职位编码进行in查询
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 20:54
     * @param codes
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersPosition>
     */
    List<PersPosition> findByCodeIn(Collection<String> codes);

    /**
     * 计算该职位名称是否重复，排除本身
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 20:55
     * @param name
     * @param parentId
     * @param id
     * @return java.lang.Integer
     */
    Integer countByNameAndAndParent_IdAndAndIdNot(String name, String parentId, String id);

    /**
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 20:55
     * @param name
     * @param id
     * @return java.lang.Integer
     */
    Integer countByNameAndParent_IdIsNullAndIdNot(String name, String id);

    /**
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 20:55
     * @param name
     * @param parentId
     * @return java.lang.Integer
     */
    Integer countByNameAndParent_Id(String name, String parentId);

    /**
     * 根据name
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 20:55
     * @param name
     * @return java.lang.Integer
     */
    Integer countByNameAndParent_IdIsNull(String name);

    /**
     * 根据父职位ID进行in查询
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 20:55
     * @param parentIdList
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersPosition>
     */
    List<PersPosition> findByParent_IdIn(List<String> parentIdList);

    @Query(value="SELECT t.name,d.code FROM PERS_POSITION t LEFT JOIN PERS_POSITION d ON t.PARENT_ID=d.ID",nativeQuery = true)
    List<Object[]> getAllNameAndParentCode();
}