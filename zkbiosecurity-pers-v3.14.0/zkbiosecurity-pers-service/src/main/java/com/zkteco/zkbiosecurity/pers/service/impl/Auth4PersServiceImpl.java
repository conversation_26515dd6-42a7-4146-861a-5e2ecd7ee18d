package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.auth.service.Auth4PersService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthUserItem;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;

/**
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 21:23 2018/5/9
 */
@Service
@Transactional
public class Auth4PersServiceImpl implements Auth4PersService {
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private AuthUserService authUserService;

    @Override
    public long countPersonByDeptId(String deptId) {
        return persPersonDao.countByDeptIdIn(Arrays.asList(deptId));
    }

    @Override
    public Map<String, Integer> getAllDeptPersonCount() {
        List<Object[]> objects = persPersonDao.countOrderByDeptId();
        return Optional.ofNullable(objects).filter(i -> !i.isEmpty())
            .map(i -> i.stream().collect(
                Collectors.toMap((Object[] o) -> o[0].toString(), (Object[] o) -> Integer.valueOf(o[1].toString()))))
            .orElse(Collections.emptyMap());
    }

    @Override
    public AuthUserItem getPersById(String id) {
        PersPerson person = persPersonDao.findById(id).orElse(null);
        AuthUserItem authUserItem = new AuthUserItem();
        authUserItem.getRoleSet().add("employee");
        authUserItem.setId(person.getId());
        authUserItem.setEmail(person.getEmail());
        authUserItem.setIsActive(true);
        authUserItem.setIsSuperuser(false);
        authUserItem.setName(person.getName());
        authUserItem.setLastName(person.getLastName());
        authUserItem.setUsername(person.getPin());
        authUserItem.setEmail(person.getEmail());
        authUserItem.setLoginPwd(person.getSelfPwd());
        authUserItem.setUserLoginLimit(person.getPersLoginLimit());
        if (person.getDeptId() == null) {
            authUserItem.setCompanyId("default");
        }
        authUserItem.setCompanyId(person.getDeptId());
        return authUserItem;
    }

    @Override
    public void saveItem(AuthUserItem item) {
        PersPerson persPerson = persPersonDao.findById(item.getId()).orElse(null);
        persPerson.setId(item.getId());
        if (StringUtils.isNotBlank(item.getLoginPwd())
            && !StringUtils.equals(persPerson.getSelfPwd(), item.getLoginPwd())) {
            persPerson.setSelfPwd(item.getLoginPwd());
            // 删除apptoken
            authUserService.delApiToken(persPerson.getPin());
        }
        persPerson.setPersLoginLimit(item.getUserLoginLimit());
        persPerson.setEmail(item.getEmail());
        persPerson.setName(item.getName());
        persPerson.setLastName(item.getLastName());
        persPersonDao.save(persPerson);
    }

    @Override
    public String getPersPassWordByPin(String userCode) {
        PersPerson persPerson = persPersonDao.findByPin(userCode);
        if (persPerson != null) {
            return persPerson.getSelfPwd();
        }
        return null;
    }

    @Override
    public AuthUserItem findByPin(String user) {
        PersPerson persPerson = persPersonDao.findByPin(user);
        if (persPerson != null) {
            AuthUserItem authUserItem = new AuthUserItem();
            authUserItem.getRoleSet().add("employee");
            authUserItem.setId(persPerson.getId());
            authUserItem.setEmail(persPerson.getEmail());
            authUserItem.setIsActive(true);
            authUserItem.setIsSuperuser(false);
            authUserItem.setName(persPerson.getName());
            authUserItem.setLastName(persPerson.getLastName());
            authUserItem.setUsername(persPerson.getPin());
            authUserItem.setUserLoginLimit(persPerson.getPersLoginLimit());
            if (persPerson.getDeptId() == null) {
                authUserItem.setCompanyId("default");
            }
            authUserItem.setCompanyId(persPerson.getDeptId());
            return authUserItem;
        }
        return null;
    }
}
