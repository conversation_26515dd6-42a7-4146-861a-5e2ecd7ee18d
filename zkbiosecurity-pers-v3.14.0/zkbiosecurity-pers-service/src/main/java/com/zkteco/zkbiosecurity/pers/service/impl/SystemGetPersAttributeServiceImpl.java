/**
 * <AUTHOR>
 * @date 2019/11/28 15:44
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;
import com.zkteco.zkbiosecurity.system.service.SystemGetPersAttributeService;
import com.zkteco.zkbiosecurity.system.vo.SystemGetPersAttributeItem;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@Service
public class SystemGetPersAttributeServiceImpl implements SystemGetPersAttributeService {

    @Autowired
    private PersAttributeService persAttributeService;

    @Override
    public List<SystemGetPersAttributeItem> getAllPersAttributes() {
        List<PersAttributeItem> persAttributeItemList = persAttributeService.getByCondition(new PersAttributeItem());
        List<SystemGetPersAttributeItem> systemGetPersAttributeItemList = new ArrayList<>();
        for (PersAttributeItem persAttributeItem : persAttributeItemList) {
            SystemGetPersAttributeItem systemGetPersAttributeItem = new SystemGetPersAttributeItem();
            if ("init".equals(persAttributeItem.getSqlStr())) {
                String attrName = I18nUtil.i18nCode("pers_attr_" + persAttributeItem.getAttrName());
                if (StringUtils.isNotBlank(attrName) && !attrName.contains("pers_attr_")) {
                    systemGetPersAttributeItem.setAttrName(attrName);
                } else {
                    systemGetPersAttributeItem.setAttrName(persAttributeItem.getAttrName());
                }
            } else {
                systemGetPersAttributeItem.setAttrName(persAttributeItem.getAttrName());
            }
            systemGetPersAttributeItem.setAttrValue("attrValue" + persAttributeItem.getFiledIndex());
            systemGetPersAttributeItem.setSqlStr(persAttributeItem.getSqlStr());
            systemGetPersAttributeItemList.add(systemGetPersAttributeItem);
        }
        return systemGetPersAttributeItemList;
    }
}
