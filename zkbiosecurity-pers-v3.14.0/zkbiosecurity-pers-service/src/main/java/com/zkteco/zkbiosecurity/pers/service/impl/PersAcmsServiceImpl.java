package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.core.utils.FileUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersCardDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersCard;
import com.zkteco.zkbiosecurity.pers.service.Pers2OtherService;
import com.zkteco.zkbiosecurity.pers.service.PersAcmsService;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.SystemAcmsService;
import com.zkteco.zkbiosecurity.system.utils.SystemAcmsUtil;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import com.zkteco.zkbiosecurity.system.vo.SystemAcmsItem;

@Service
@Transactional
public class PersAcmsServiceImpl implements PersAcmsService {
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private SystemAcmsService systemAcmsService;
    @Autowired
    private PersCardDao persCardDao;
    @Autowired
    private Pers2OtherService pers2OtherService;
    @Autowired
    private PersPersonDao persPersonDao;

    @Override
    public ZKResultMsg delAcmsCard(List<PersCardItem> cardList) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        String syncDelCard = baseSysParamService.getValByName("acms.sync.delCard");
        if ("1".equals(syncDelCard) && !cardList.isEmpty()) {
            // 获取ACMS平台信息
            SystemAcmsItem systemAcmsItem = new SystemAcmsItem();
            ZKResultMsg acmsPlatefromInfo = getAcmsPlatefromInfo();
            if ("ok".equals(acmsPlatefromInfo.getRet())) {
                systemAcmsItem = (SystemAcmsItem)acmsPlatefromInfo.getData();
            } else {
                return acmsPlatefromInfo;
            }
            for (PersCardItem card : cardList) {
                systemAcmsItem.setCardId(card.getBussinessId());
                // 可回收卡
                if (100 == card.getCardOpType()) {
                    systemAcmsItem.setOperation("3");
                    zkResultMsg = systemAcmsService.reassignCard(systemAcmsItem);
                    if (!"ok".equals(zkResultMsg.getRet())) {
                        return zkResultMsg;
                    }
                } else if (101 == card.getCardOpType()) {
                    zkResultMsg = systemAcmsService.delCard(systemAcmsItem);
                    if (!"ok".equals(zkResultMsg.getRet())) {
                        return zkResultMsg;
                    }
                }
            }
        }
        return zkResultMsg;
    }

    @Override
    public ZKResultMsg obtainAcmsCard(String status, String pageSize) {
        // 获取ACMS平台信息
        SystemAcmsItem systemAcmsItem = new SystemAcmsItem();
        ZKResultMsg acmsPlatefromInfo = getAcmsPlatefromInfo();
        if ("ok".equals(acmsPlatefromInfo.getRet())) {
            systemAcmsItem = (SystemAcmsItem)acmsPlatefromInfo.getData();
        } else {
            return acmsPlatefromInfo;
        }
        systemAcmsItem.setStatus(StringUtils.isNotBlank(status) ? status : SystemAcmsUtil.ACMS_CARD_STATUS_IDLE);
        systemAcmsItem.setPageSize(StringUtils.isNotBlank(pageSize) ? pageSize : SystemAcmsUtil.ACMS_PAGE_SIZE);
        return validAcmsCards(systemAcmsItem);
    }

    private ZKResultMsg validAcmsCards(SystemAcmsItem systemAcmsItem) {
        ZKResultMsg resultMsg = new ZKResultMsg();
        // 可以新增的ACMS卡列表
        JSONArray acmsCards = new JSONArray();
        int pageNumber = 1;
        boolean nullIdle = false;
        boolean firstNull = false;
        whileCount:
        while (acmsCards.size() < Integer.parseInt(SystemAcmsUtil.ACMS_PAGE_SIZE)) {
            ZKResultMsg zkResultMsg = new ZKResultMsg();
            systemAcmsItem.setPageNumber(pageNumber + "");
            if (!nullIdle) {
                zkResultMsg = systemAcmsService.obtainAcmsCard(systemAcmsItem);
            }
            // 如果闲置卡为空则去查询未激活卡
            if (zkResultMsg.getData() == null || !zkResultMsg.isSuccess()) {
                if (!nullIdle) {
                    nullIdle = true;
                    if (!firstNull) {
                        firstNull = true;
                    }
                }
            }
            // 闲置卡首次查询为空时，pageNumber重置为1
            if (firstNull) {
                pageNumber = 1;
                firstNull = false;
            }
            if (nullIdle) {
                systemAcmsItem.setPageNumber(pageNumber + "");
                systemAcmsItem.setStatus(SystemAcmsUtil.ACMS_CARD_STATUS_INACTIVE);
                zkResultMsg = systemAcmsService.obtainAcmsCard(systemAcmsItem);;
            }
            // 如果查询卡为空，则跳出循环
            if (zkResultMsg.isSuccess() && zkResultMsg.getData() != null) {
                Object data = zkResultMsg.getData();
                JSONArray jsonArray = JSONArray.parseArray(data.toString());
                // ACMS卡JSON集合
                List<JSONObject> jsonObjects = jsonArray.toJavaList(JSONObject.class);
                // ACMS卡Map
                Map<String, List<JSONObject>> cardNumberMap = jsonObjects.stream()
                    .collect(Collectors.groupingBy(jsonObject -> jsonObject.getString("cardNumber")));
                // ACMS卡编号集合
                List<String> acmsCardNumber = jsonObjects.stream().map(jsonObject -> jsonObject.getString("cardNumber"))
                    .collect(Collectors.toList());
                // 数据库卡号IN查询
                List<PersCard> existPersCardList = persCardDao.findByCardNoIn(acmsCardNumber);
                List<String> existCardNoList =
                    (List<String>)CollectionUtil.getPropertyList(existPersCardList, PersCard::getCardNo, "-1");
                acmsCardNumber.removeAll(existCardNoList);
                existCardNoList = new ArrayList<>();
                for (String acmsCardNo : acmsCardNumber) {
                    if (pers2OtherService.checkExistCardNo(acmsCardNo)) {
                        existCardNoList.add(acmsCardNo);
                    }
                }
                acmsCardNumber.removeAll(existCardNoList);
                for (String acmsCardNo : acmsCardNumber) {
                    JSONObject cardNumberJSON = cardNumberMap.get(acmsCardNo).get(0);
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("cardNo", acmsCardNo);
                    jsonObject.put("bussinessId", cardNumberJSON.getString("id"));
                    jsonObject.put("activationCode", cardNumberJSON.getString("activationCode"));
                    jsonObject.put("status", cardNumberJSON.getString("status"));
                    Boolean isRevoke = cardNumberJSON.getBoolean("isRevoke");
                    if (isRevoke != null && isRevoke) {
                        jsonObject.put("cardOpType", "100");
                    } else {
                        jsonObject.put("cardOpType", "101");
                    }
                    acmsCards.add(jsonObject);
                }
                if (acmsCards.size() >= Integer.parseInt(SystemAcmsUtil.ACMS_PAGE_SIZE)) {
                    break whileCount;
                }
            } else {
                break whileCount;
            }
            pageNumber = pageNumber + 1;
        }
        resultMsg.setData(acmsCards);
        return resultMsg;
    }

    @Override
    public ZKResultMsg reassignCard(String cardId) {
        // 获取ACMS平台信息
        SystemAcmsItem systemAcmsItem = new SystemAcmsItem();
        ZKResultMsg acmsPlatefromInfo = getAcmsPlatefromInfo();
        if ("ok".equals(acmsPlatefromInfo.getRet())) {
            systemAcmsItem = (SystemAcmsItem)acmsPlatefromInfo.getData();
        } else {
            return acmsPlatefromInfo;
        }
        systemAcmsItem.setCardId(cardId);
        return systemAcmsService.reassignCard(systemAcmsItem);
    }

    @Override
    public ZKResultMsg getAcmsPlatefromInfo() {
        // 获取ACMS平台信息
        Map<String, String> acmsParams = baseSysParamService.getParamsByModule("acms");
        String address = acmsParams.get("acms.address");
        SystemAcmsItem systemAcmsItem = new SystemAcmsItem(address, acmsParams.get("acms.companyCode"),
            acmsParams.get("acms.username"), acmsParams.get("acms.pwd"));
        return systemAcmsService.getAcmsPlatefromInfo(systemAcmsItem, false);
    }

    @Override
    public ZKResultMsg activeCredential(String activationCode, String email) {
        // 获取ACMS平台信息
        SystemAcmsItem systemAcmsItem = new SystemAcmsItem();
        ZKResultMsg acmsPlatefromInfo = getAcmsPlatefromInfo();
        if ("ok".equals(acmsPlatefromInfo.getRet())) {
            systemAcmsItem = (SystemAcmsItem)acmsPlatefromInfo.getData();
        } else {
            return acmsPlatefromInfo;
        }
        systemAcmsItem.setActivationCode(activationCode);
        systemAcmsItem.setEmail(email);
        return systemAcmsService.activeCredential(systemAcmsItem);
    }

    @Override
    public ZKResultMsg assignPersonCard(List<String> cardIdList, PersPersonItem persPersonItem) {
        if (!cardIdList.isEmpty() && StringUtils.isNotBlank(persPersonItem.getPin())) {
            // 获取ACMS平台信息
            SystemAcmsItem systemAcmsItem = new SystemAcmsItem();
            ZKResultMsg acmsPlatefromInfo = getAcmsPlatefromInfo();
            if ("ok".equals(acmsPlatefromInfo.getRet())) {
                systemAcmsItem = (SystemAcmsItem)acmsPlatefromInfo.getData();
            } else {
                return acmsPlatefromInfo;
            }
            systemAcmsItem.setCardIdList(cardIdList);
            systemAcmsItem.setPin(persPersonItem.getPin());
            systemAcmsItem.setFirstName(persPersonItem.getName());
            systemAcmsItem.setLastName(persPersonItem.getLastName());
            systemAcmsItem.setEmail(persPersonItem.getEmail());
            // systemAcmsItem.setMobilePhone(persPersonItem.getMobilePhone());
            systemAcmsItem.setPhotoBase64(persPersonItem.getPhotoBase64());
            return systemAcmsService.assignPersonCard(systemAcmsItem);
        }
        return new ZKResultMsg();
    }

    @Override
    public ZKResultMsg updateAcmsPersonByPin(PersPersonItem persPersonItem) {
        if (StringUtils.isNotBlank(persPersonItem.getPin())) {
            // 获取ACMS平台信息
            SystemAcmsItem systemAcmsItem = new SystemAcmsItem();
            ZKResultMsg acmsPlatefromInfo = getAcmsPlatefromInfo();
            if ("ok".equals(acmsPlatefromInfo.getRet())) {
                systemAcmsItem = (SystemAcmsItem)acmsPlatefromInfo.getData();
            } else {
                return acmsPlatefromInfo;
            }
            systemAcmsItem.setPin(persPersonItem.getPin());
            systemAcmsItem.setFirstName(persPersonItem.getName());
            systemAcmsItem.setLastName(persPersonItem.getLastName());
            String photoBase64 = persPersonItem.getPhotoBase64();
            if (StringUtils.isBlank(photoBase64) && StringUtils.isNotBlank(persPersonItem.getPhotoPath())) {
                // 设置缩略图处理，并且判断文件是否存在。
                // 图片解密
                String thumbPath = FileUtil.getThumbPath(persPersonItem.getPhotoPath());
                if (FileUtil.fileExists(thumbPath)) {
                    photoBase64 = FileEncryptUtil.getDecryptFileBase64(thumbPath);
                } else {
                    photoBase64 = FileEncryptUtil.getDecryptFileBase64(persPersonItem.getPhotoPath());
                }
            }
            systemAcmsItem.setPhotoBase64(photoBase64);
            return systemAcmsService.updatePersonByPin(systemAcmsItem);
        }
        return new ZKResultMsg();
    }

    @Override
    public ZKResultMsg deleteAcmsPerson(List<String> pinList) {
        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("acms.sync.persInfo");
        if (baseSysParamItem != null && "1".equals(baseSysParamItem.getParamValue())) {
            SystemAcmsItem systemAcmsItem = new SystemAcmsItem();
            ZKResultMsg acmsPlatefromInfo = getAcmsPlatefromInfo();
            if ("ok".equals(acmsPlatefromInfo.getRet())) {
                systemAcmsItem = (SystemAcmsItem)acmsPlatefromInfo.getData();
                systemAcmsItem.setPinList(pinList);
                systemAcmsService.deletePersonsByPinList(systemAcmsItem);
            }
        }
        return new ZKResultMsg();
    }
}