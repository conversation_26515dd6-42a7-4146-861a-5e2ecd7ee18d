package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022-10-20 15:14
 * @since 1.0.0
 */
@Slf4j
@Component
public class PersVer3_11_1 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v3.11.1";
    }

    @Override
    public boolean executeUpgrade() {

        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("PersParams");
        if (subMenuItem != null) {
            subMenuItem.setActionLink("persParams.do");
            authPermissionService.saveItem(subMenuItem);
        }
        return true;
    }

}
