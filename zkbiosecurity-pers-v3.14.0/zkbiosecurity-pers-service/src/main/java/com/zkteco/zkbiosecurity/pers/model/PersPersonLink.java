/**
 * File Name: PersAttributeExt
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 PersPersonLink
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_PERSON_LINK",
        indexes = {
                @Index(name = "PERS_LINK_CREATE_TIME_IDX", columnList = "CREATE_TIME")
        },
        uniqueConstraints = {
                @UniqueConstraint(name="PERS_PID_TYPE_LINK_ID_UQ",columnNames = {"PERSON_ID", "TYPE", "LINK_ID"})
        })
@Getter
@Setter
@Accessors(chain = true)
public class PersPersonLink extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @Column(name = "PERSON_ID", length = 50)
    private String personId;

    /**
     * 类型
     */
    @Column(name = "TYPE", length = 30)
    private String type;

    /**
     * 业务表的ID
     */
    @Column(name = "LINK_ID", length = 50)
    private String linkId;
}