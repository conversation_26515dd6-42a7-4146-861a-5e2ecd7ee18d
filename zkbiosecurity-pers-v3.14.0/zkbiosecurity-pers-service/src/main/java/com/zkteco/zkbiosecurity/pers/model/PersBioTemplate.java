/**
 * File Name: PersBioTemplate Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 PersBioTemplate
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_BIOTEMPLATE", indexes = {@Index(name = "PERS_BIO_TEMPLATE_PIN_IDX", columnList = "PERSON_PIN")},
    uniqueConstraints = {@UniqueConstraint(name = "PERS_ID_TYPE_VER_NO_INDEX_UQ",
        columnNames = {"PERSON_ID", "BIO_TYPE", "VERSION", "TEMPLATE_NO", "TEMPLATE_NO_INDEX"})})
@Getter
@Setter
@Accessors(chain = true)
public class PersBioTemplate extends BaseModel implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @Column(name = "PERSON_ID", length = 50)
    private String personId;

    /**
     * 人员编号
     */
    @Column(name = "PERSON_PIN", length = 50)
    private String personPin;

    /**
     * 是否有效
     */
    @Column(name = "VALID_TYPE", nullable = true)
    private Short validType;

    /**
     * 生物特征类型0：通用的1：指纹2：面部3：声纹4：虹膜5：视网膜6：掌纹7：指静脉 8：掌静脉
     */
    @Column(name = "BIO_TYPE")
    private Short bioType;

    /**
     * 生物特征版本
     */
    @Column(name = "VERSION", length = 20)
    private String version;

    /**
     * 生物特征模板内容
     */
    @Column(name = "TEMPLATE")
    @Lob
    private String template;

    /**
     * 生物特征模板编号，默认 0 指纹，指静脉：0-9 左手：小拇指/无名指/中指/食指/拇指, 右手：拇指/食指/中指/无名指/小拇指； 虹膜：0：左眼 1:右眼 掌静脉：0：左手 1:右手
     */
    @Column(name = "TEMPLATE_NO")
    private Short templateNo;

    /**
     * 生物特征模板对应索引，默认 1 若有多个值模板，索引从0开始， 例如指静脉：[0,2]，人脸[0,11]
     */
    @Column(name = "TEMPLATE_NO_INDEX")
    private Short templateNoIndex;

    /**
     * 是否胁迫
     */
    @Column(name = "DURESS")
    private Boolean duress;
}