package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiDepartmentItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersApiDepartmentService;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/16 13:58
 */
@Service
@Transactional
public class PersApiDepartmentServiceImpl implements PersApiDepartmentService {

    @Autowired
    private AuthDepartmentService authDepartmentService;

    @Override
    public ApiResultMessage updateDepartment(PersApiDepartmentItem department) {
        String code = department.getCode();
        String name = department.getName();
        Integer sortNo = department.getSortNo();
        String parentDeptCode = department.getParentCode();
        if (StringUtils.isBlank(code) || StringUtils.isBlank(name)) {
            // 部门编号或名称不能为空
            return ApiResultMessage.message(PersConstants.DEPT_CODEORNAME_ISNULL,
                I18nUtil.i18nCode("pers_api_department_codeOrNameNotNull"));
        }
        // 验证code中是否包含特殊字符
        if (PersRegularUtil.hasSpecialChar(code)) {
            return ApiResultMessage.message(PersConstants.DEPT_CODE_NOSPECIALCHAR,
                I18nUtil.i18nCode("pers_dept_noSpecialChar"));
        }
        // 验证部门名称是否包含特殊字符
        if (PersRegularUtil.hasSpecialChar(name)) {
            return ApiResultMessage.message(PersConstants.DEPT_NAME_NOSPECIALCHAR,
                I18nUtil.i18nCode("pers_dept_NameNoSpecialChar"));
        }
        if (sortNo == null) {
            // 排序不能为空
            return ApiResultMessage.message(PersConstants.DEPT_SORTNO_ISNULL,
                I18nUtil.i18nCode("pers_api_deptSortNoIsNull"));
        }
        if (!(sortNo > 0 && sortNo <= 999999)) {
            // 排序的值要介于1-999999之间
            return ApiResultMessage.message(PersConstants.DEPT_SORTNO_ISNULL,
                I18nUtil.i18nCode("pers_api_deptSortNoError"));
        }
        AuthDepartmentItem departmentItem = authDepartmentService.getItemByCode(code);
        AuthDepartmentItem parentDept = null;
        if (departmentItem == null) {
            // 新增
            if (!authDepartmentService.deptLicenseCheck()) {
                return ApiResultMessage.message(PersConstants.PERSON_lICENSE_MAXCOUNT,
                    I18nUtil.i18nCode("common_license_maxCount"));
            }
            departmentItem = new AuthDepartmentItem();
        }
        departmentItem.setCode(code);
        departmentItem.setName(name);
        departmentItem.setSortNo(department.getSortNo());
        if (StringUtils.isNotBlank(parentDeptCode)) {
            // 判断是否存在上级部门，存在则设置上级部门信息
            parentDept = authDepartmentService.getItemByCode(parentDeptCode);
        } else {
            departmentItem.setParentId("");
        }
        // 设置上级部门信息
        if (parentDept != null) {
            if (StringUtils.isNotBlank(departmentItem.getId())) {
                // 编辑时需要判断上级部门是否是本身或其下级部门
                boolean isChild =
                    authDepartmentService.isChild(Lists.newArrayList(departmentItem.getId()), parentDept.getId());
                if (departmentItem.getId().equals(parentDept.getId()) || isChild) {
                    // 上级部门不能是自身或其下级部门
                    return ApiResultMessage.message(PersConstants.DEPT_SELFNOT_PARENT,
                        I18nUtil.i18nCode("pers_dept_parentMenuMsg"));
                }
            }
            departmentItem.setParentId(parentDept.getId());
        }
        // 判断部门名称是否重复
        if (checkDepartName(departmentItem, parentDept)) {
            return ApiResultMessage.message(PersConstants.DEPT_NAME_EXIST, I18nUtil.i18nCode("pers_dept_nameExist"));
        }
        authDepartmentService.saveItem(departmentItem);
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage deleteDeptByCode(String code) {
        if (StringUtils.isBlank(code)) {
            // 部门编号不能为空;
            return ApiResultMessage.message(PersConstants.DEPT_CODEORNAME_ISNULL,
                I18nUtil.i18nCode("pers_api_department_codeOrNameNotNull"));
        }
        AuthDepartmentItem departmentItem = authDepartmentService.getItemByCode(code);
        if (departmentItem == null) {
            return ApiResultMessage.message(PersConstants.DEPT_CODE_NOTEXIST, I18nUtil.i18nCode("pers_app_deptIsNull"));
        }
        authDepartmentService.deleteByIds(departmentItem.getId());
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage getDeptByCode(String code) {
        if (StringUtils.isBlank(code)) {
            // 部门编号不能为空;
            return ApiResultMessage.message(PersConstants.DEPT_CODEORNAME_ISNULL,
                I18nUtil.i18nCode("pers_api_department_codeOrNameNotNull"));
        }
        AuthDepartmentItem departmentItem = authDepartmentService.getItemByCode(code);
        if (departmentItem == null) {
            return ApiResultMessage.message(PersConstants.DEPT_CODE_NOTEXIST, I18nUtil.i18nCode("pers_app_deptIsNull"));
        }
        ApiResultMessage rs = ApiResultMessage.successMessage();
        PersApiDepartmentItem persApiDepartmentItem = buildApiDepartment(departmentItem);
        rs.setData(persApiDepartmentItem);
        return rs;
    }

    /**
     * 组装部门信息接口返回
     * 
     * @auther lambert.li
     * @date 2018/11/9 14:41
     * @param departmentItem
     * @return
     */
    private PersApiDepartmentItem buildApiDepartment(AuthDepartmentItem departmentItem) {
        PersApiDepartmentItem persApiDepartmentItem = new PersApiDepartmentItem();
        ModelUtil.copyPropertiesIgnoreNull(departmentItem, persApiDepartmentItem);
        return persApiDepartmentItem;
    }

    /**
     * 判断部门名称是否存在
     * 
     * @auther lambert.li
     * @date 2018/11/9 14:12
     * @param departmentItem
     * @param parentDept
     * @return
     */
    private boolean checkDepartName(AuthDepartmentItem departmentItem, AuthDepartmentItem parentDept) {
        String deptId = StringUtils.isNotBlank(departmentItem.getId()) ? departmentItem.getId() : "";
        String parentDeptId = parentDept != null ? parentDept.getId() : "";
        return !authDepartmentService.isDepartmentNameExist(deptId, parentDeptId, departmentItem.getName());
    }

    @Override
    public Pager getApiDepartmentByPage(int pageNo, Integer pageSize) {
        AuthDepartmentItem authDepartmentItem = new AuthDepartmentItem();
        Pager pager = authDepartmentService.getItemsByPage(authDepartmentItem, pageNo - 1, pageSize);
        List<AuthDepartmentItem> authDepartmentItemList = (List<AuthDepartmentItem>)pager.getData();
        List<PersApiDepartmentItem> apiDepartmentItem = Lists.newArrayList();
        if (!authDepartmentItemList.isEmpty()) {
            authDepartmentItemList
                .forEach(AuthDepartmentItem -> apiDepartmentItem.add(buildApiDepartment(AuthDepartmentItem)));
        }
        pager.setData(apiDepartmentItem);
        return pager;
    }

}
