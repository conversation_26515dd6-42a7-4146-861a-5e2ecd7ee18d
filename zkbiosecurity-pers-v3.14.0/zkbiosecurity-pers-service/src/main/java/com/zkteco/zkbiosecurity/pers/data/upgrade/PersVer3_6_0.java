package com.zkteco.zkbiosecurity.pers.data.upgrade;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.FileUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersBioPhotoDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersBioPhoto;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

@Component
public class PersVer3_6_0 implements UpgradeVersionManager {
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersBioPhotoDao persBioPhotoDao;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getVersion() {
        return "v3.6.0";
    }

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public boolean executeUpgrade() {
        List<PersPerson> persPersonList = persPersonDao.findAll();
        List<PersBioPhoto> persBioPhotoList = new ArrayList<>();
        persPersonList.forEach(person -> {
            if (FileUtil.getCropFaceCount(person.getPin()) > 0) {
                PersBioPhoto persBioPhoto = new PersBioPhoto();
                String photoPath =
                    FileUtil.getCropFacePath(person.getPin()) + FileUtil.separator + person.getPin() + ".jpg";
                persBioPhoto.setPersonId(person.getId());
                persBioPhoto.setPersonPin(person.getPin());
                persBioPhoto.setBioType(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
                persBioPhoto.setPhotoPath(photoPath);
                persBioPhotoList.add(persBioPhoto);
            }
        });
        if (!persBioPhotoList.isEmpty()) {
            persBioPhotoDao.saveAll(persBioPhotoList);
        }

        String urlCreate = baseSysParamService.getValByName("pers.tempPerson.urlCreate");
        if (StringUtils.isNotBlank(urlCreate)) {
            String[] url = urlCreate.split("app/v1/");
            urlCreate = url[0] + "tokenAdreg";
            baseSysParamService.saveValueByName("pers.tempPerson.urlCreate", urlCreate);
        }
        return true;
    }
}
