package com.zkteco.zkbiosecurity.pers.utils;

import java.io.*;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileUnZipUtil {

    /**
     * 解压
     *
     * @param sourceFilePath
     * @param toFolder
     */
    public static String unZipToFile(String sourceFilePath, String toFolder) {
        File sourceFile = new File(sourceFilePath);
        // 解压目的文件
        String descDir = toFolder + File.separator;
        // 开始解压zip
        FileUnZipUtil.unZipFile(sourceFile, descDir);
        return descDir;
    }

    /**
     * 获取所有文件
     *
     * @param filelist
     * @param strPath
     * @return
     */
    public static List<File> getFileList(List<File> filelist, String strPath) {
        File dir = new File(strPath);
        File[] files = dir.listFiles(); // 该文件目录下文件全部放入数组
        if (files != null) {
            for (int i = 0; i < files.length; i++) {
                if (files[i].isDirectory()) { // 判断是文件还是文件夹
                    getFileList(filelist, files[i].getAbsolutePath()); // 获取文件绝对路径
                } else {
                    filelist.add(files[i]);
                    continue;
                }
            }

        }
        return filelist;
    }

    // 解压zip文件
    public static void unZipFile(File zipFile, String descDir) {
        ZipFile zip = null;
        InputStream in = null;
        OutputStream out = null;
        try {
            String zipFileName = zipFile.getName();
            File outFileDir = new File(descDir + zipFileName.substring(0, zipFileName.lastIndexOf(".")));
            if (!outFileDir.exists()) {
                outFileDir.mkdirs();
            }
            // 设置编码，解决压缩文件含中文报错问题
            zip = new ZipFile(zipFile, Charset.forName("GBK"));
            log.info("fileUnZipRar unZipFile start...");
            for (Enumeration enumeration = zip.entries(); enumeration.hasMoreElements();) {
                ZipEntry entry = (ZipEntry)enumeration.nextElement();
                String zipEntryName = entry.getName();
                // 修复CWE-22漏洞，过滤包含路径遍历字符(../),避免创建包含上级目录符号的文件夹
                if (zipEntryName.contains("..")) {
                    continue;
                }
                in = zip.getInputStream(entry);
                // 处理压缩文件包含文件夹的情况
                if (entry.isDirectory()) {
                    File fileDir = new File(descDir + zipEntryName);
                    fileDir.mkdir();
                    continue;
                }

                File file = new File(descDir, zipEntryName);
                file.createNewFile();
                out = new FileOutputStream(file);
                byte[] buff = new byte[1024];
                int len;
                while ((len = in.read(buff)) > 0) {
                    out.write(buff, 0, len);
                }
                out.close();
                in.close();
            }
            log.info("fileUnZipRar unZipFile finished!");
        } catch (IOException e) {
            log.error("compressed zip file failed", e);
        } finally {
            try {
                if (zip != null) {
                    zip.close();
                }
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (IOException e) {
                log.error("compressed zip file failed", e);
            }
        }
    }
}
