package com.zkteco.zkbiosecurity.pers.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersTempPerson;

import java.util.Collection;
import java.util.List;

/**
 * 临时人员
 * 
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @date 2018/12/6 21:20
 */
public interface PersTempPersonDao extends BaseDao<PersTempPerson, String> {

    /**
     * 根据状态和IDS查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 21:20
     * @param status
     * @param ids
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersTempPerson>
     */
    List<PersTempPerson> findByStatusAndIdIn(Short status, Collection<String> ids);

    /**
     * 根据编号查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 21:20
     * @param pin
     * @return com.zkteco.zkbiosecurity.pers.model.PersTempPerson
     */
    PersTempPerson findByPin(String pin);

    /**
     * 根据手机号查询
     * 
     * <AUTHOR>
     * @since 2019年6月21日 下午4:36:52
     * @param phone
     * @return
     */
    PersTempPerson findByMobilePhone(String phone);

    /**
     * 根据邮箱查询
     * 
     * <AUTHOR>
     * @since 2019年6月21日 下午5:50:51
     * @param email
     * @return
     */
    PersTempPerson findByEmail(String email);

    List<PersTempPerson> findByPinIn(List<String> pinList);
}
