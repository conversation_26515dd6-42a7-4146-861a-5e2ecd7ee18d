package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

@Component
public class PersVer3_0_4 implements UpgradeVersionManager {
    @Autowired
    private AuthPermissionService authPermissionService;
    @Value("${system.language:zh_CN}")
    private String language;
    @Value("${system.isCloud:false}")
    private Boolean isCloud;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v3.0.4";
    }

    @Override
    public boolean executeUpgrade() {
        if (!"zh_CN".equals(language) && !isCloud) {
            AuthPermissionItem appModuleItem = null;
            AuthPermissionItem appMenuItem = null;
            AuthPermissionItem appButtonItem = null;

            appModuleItem = authPermissionService.getItemByCode("App");
            if (null == appModuleItem) {
                // 管理员APP模块
                appModuleItem = new AuthPermissionItem("App", "app_module", "app",
                    AuthContants.RESOURCE_TYPE_APP_SYSTEM, ZKConstant.TRUE, 998);
                authPermissionService.initData(appModuleItem);
            }
            appMenuItem = new AuthPermissionItem("AppPers", "app_pers", "app:APPpers",
                AuthContants.RESOURCE_TYPE_APP_MENU, ZKConstant.TRUE, 1);
            appMenuItem.setParentId(appModuleItem.getId());
            appMenuItem = authPermissionService.initData(appMenuItem);
            // 人事--人员
            appButtonItem = new AuthPermissionItem("AppPersList", "app_pers_list", "app:APPpersList",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 1);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
            // 人事--新增
            appButtonItem = new AuthPermissionItem("AppPersAdd", "app_pers_add", "app:APPpersAdd",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 2);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
            // 人事--设置
            appButtonItem = new AuthPermissionItem("AppPersSet", "app_pers_set", "app:APPpersSet",
                AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 3);
            appButtonItem.setParentId(appMenuItem.getId());
            authPermissionService.initData(appButtonItem);
        }
        return true;
    }
}
