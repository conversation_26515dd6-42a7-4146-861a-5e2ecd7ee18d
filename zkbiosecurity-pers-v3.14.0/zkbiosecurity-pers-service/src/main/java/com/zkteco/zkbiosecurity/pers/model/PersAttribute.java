/**
 * File Name: PersAttribute Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 PersAttribute
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_ATTRIBUTE", indexes = {@Index(name = "PERS_ATTRIBUTE_ATTR_NAME_IDX", columnList = "ATTR_NAME")})
@Getter
@Setter
@Accessors(chain = true)
public class PersAttribute extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 参数名字
     */
    @Column(name = "ATTR_NAME", length = 100, unique = true)
    private String attrName;

    /**
     * 控件类型
     */
    @Column(name = "CONTROL_TYPE", length = 30)
    private String controlType;

    /**
     * 参数的值列表
     */
    @Column(name = "VALUE_LIST", length = 2000)
    private String attrValue;

    /**
     * 是否是初始化参数
     */
    @Column(name = "SQL_STR", length = 200)
    private String sqlStr;

    /**
     * 控件坐标x轴
     */
    @Column(name = "POSITION_X")
    private Integer positionX;

    /**
     * 控件坐标y轴
     */
    @Column(name = "POSITION_Y")
    private Integer positionY;

    /**
     * 人员类型
     */
    @Column(name = "PERSON_TYPE")
    private Short personType;

    /**
     * 列索引
     */
    @Column(name = "FILED_INDEX")
    private Integer filedIndex;

    /**
     * 是否在表格中显示
     */
    @Column(name = "SHOW_TABLE")
    private Boolean showTable;
}