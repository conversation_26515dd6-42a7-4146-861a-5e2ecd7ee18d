package com.zkteco.zkbiosecurity.pers.dao;


import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersPersonnalList;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 名单库
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-08 下午08:30
 */
public interface PersPersonnalListDao extends BaseDao<PersPersonnalList, String> {

    /**
     * 根据名称查找名单库
     *
     * <AUTHOR>
     * @date 2020/8/4 17:25
     */
    PersPersonnalList findByName(String name);

    /**
     * 根据类型与初始化标记查询名单库
     *
     * @param type
     * @param initFlag
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersPersonnalList>
     * <AUTHOR>
     * @date 2021-05-31 9:45
     * @since 1.0.0
     */
    List<PersPersonnalList> findByTypeAndInitFlag(String type, boolean initFlag);

    @Query("select t from PersPersonnalList t left join PersPersonnallistPerson plp with t.id = plp.personnallistId where plp.personId = ?1")
    List<PersPersonnalList> findByPersonId(String personId);
}