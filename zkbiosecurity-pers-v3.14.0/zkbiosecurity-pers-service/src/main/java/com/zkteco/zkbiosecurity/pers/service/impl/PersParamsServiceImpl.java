/**
 * File Name: PersCardServiceImpl Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.att.service.Att4PersPersonService;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.core.utils.ImgEncodeUtil;
import com.zkteco.zkbiosecurity.core.utils.StrUtil;
import com.zkteco.zkbiosecurity.license.service.BaseLicenseService;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.system.service.BaseRegisterService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.ModuleInfoService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

/**
 * 对应百傲瑞达 PersCardServiceImpl
 *
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
@Service
@Transactional
public class PersParamsServiceImpl implements PersParamsService {
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired(required = false)
    private Att4PersPersonService att4PersPersonService;
    @Autowired
    private BaseLicenseService baseLicenseService;
    @Autowired
    private ModuleInfoService moduleInfoService;
    @Autowired
    private BaseRegisterService baseRegisterService;

    @Override
    public void saveItem(Map<String, String> params) {
        baseSysParamService.saveParams(params);
    }

    @Override
    public Map<String, String> getPersParams() {
        Map<String, String> persMap = baseSysParamService.getParamsByModule("pers");
        persMap.put("appId", baseLicenseService.getAppId());
        persMap.put("cloudServerUrl", baseSysParamService.getValByName(BaseConstants.CLOUD_QRCODE_URL));
        boolean existHepModule = false;
        BaseSysParamItem sysParam = baseSysParamService.findByParamName("pers.enableHealthInfo");
        if (StringUtils.isNotBlank(sysParam.getId())) {
            existHepModule = true;
        }
        persMap.put("existHepModule", String.valueOf(existHepModule));
        // 判读是否显示客户端注册
        persMap.put("registerMsg",
            baseRegisterService.getLicenseMsg(ConstUtil.SYSTEM_MODULE_PERS).getData().toString());
        return persMap;
    }

    @Override
    public String getValByName(String name) {
        return baseSysParamService.getValByName(name);
    }

    @Override
    public ZKResultMsg getPersPinParams() {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        List<String> list = new ArrayList<String>();
        list.add("pers.pinLen");
        list.add("pers.pinSupportLetter");
        Map<String, String> paramsByNames = baseSysParamService.getParamsByNames(list);
        zkResultMsg.setData(paramsByNames);
        return zkResultMsg;
    }

    @Override
    public boolean checkSingleAttSystem() {
        // 存在考勤模块
        if (Objects.nonNull(att4PersPersonService)) {
            return att4PersPersonService.singleAttSystem();
        }
        return false;
    }

    @Override
    public boolean getEncryptPropByParamName(String paramName) {
        String enable = baseSysParamService.getValByName(paramName);
        return StringUtils.isNotBlank(enable) && "true".equals(enable);
    }

    @Override
    public String getDecryptBase64ByPhotoPathAndEncrypt(String photoPath, String encrypt) {
        String photoBase64 = FileEncryptUtil.getDecryptFileBase64(photoPath);
        if (StringUtils.isNotBlank(photoBase64) && getEncryptPropByParamName(encrypt)) {
            photoBase64 = ImgEncodeUtil.base64BoxBlurFilter(photoBase64);
        }
        return photoBase64;
    }

    @Override
    public String getEncryptName(String name) {
        return getEncryptProp(name, "pers.name.encryptProp", "pers.name.encryptMode");
    }

    @Override
    public String getEncryptLastName(String lastName) {
        return getEncryptProp(lastName, "pers.lastName.encryptProp", "pers.lastName.encryptMode");
    }

    @Override
    public String getEncryptCardNo(String cardNo) {
        return getEncryptProp(cardNo, "pers.cardNo.encryptProp", "pers.cardNo.encryptMode");
    }

    @Override
    public String getEncryptPin(String pin) {
        return getEncryptProp(pin, "pers.pin.encryptProp", "pers.pin.encryptMode");
    }

    @Override
    public String getEncryptProp(String value, String encryptProp, String encryptMode) {
        String result = "";
        if (StringUtils.isNotBlank(value)) {
            result = value;
            String enable = baseSysParamService.getValByName(encryptProp);
            String mode = baseSysParamService.getValByName(encryptMode);
            if (StringUtils.isNotBlank(enable) && "true".equals(enable)) {
                result = StrUtil.convertToEncrypt(value, mode);
            }
        }
        return result;
    }
}
