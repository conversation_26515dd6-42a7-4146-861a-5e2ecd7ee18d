package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class PersVer3_12_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;

    @Value("${system.language:zh_CN}")
    private String language;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getVersion() {
        return "v3.12.0";
    }

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public boolean executeUpgrade() {
        if (!language.equalsIgnoreCase("zh_CN")) {
            AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("PersCard");
            if (subMenuItem != null) {
                AuthPermissionItem subButtonItem = new AuthPermissionItem("PersCardAcms", "pers_batchIssCard_acms",
                    "pers:card:acms", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
            }
        }
        // 提取人脸模板菜单
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("PersPerson");
        if (subMenuItem != null) {
            AuthPermissionItem subButtonItem =
                new AuthPermissionItem("PersPersonFaceTemplate", "pers_person_extractFaceTemplate",
                    "pers:person:batchFaceTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 25);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }
        // 人脸模板服务器
        baseSysParamService.initData(new BaseSysParamItem("pers.facialTemplate.enable", "0", "启用人脸模板提取"));
        baseSysParamService.initData(new BaseSysParamItem("pers.facialTemplate.serverAddr", "", "人脸模板提取服务器地址"));
        baseSysParamService.initData(new BaseSysParamItem("pers.facialTemplate.username", "", "人脸模板提取服务器用户名"));
        baseSysParamService.initData(new BaseSysParamItem("pers.facialTemplate.pwd", "", "人脸模板提取服务器密码"));

        return true;
    }
}
