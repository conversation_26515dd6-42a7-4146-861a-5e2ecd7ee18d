package com.zkteco.zkbiosecurity.pers.utils;

import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

import javax.imageio.ImageIO;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.drew.imaging.ImageMetadataReader;
import com.drew.imaging.ImageProcessingException;
import com.drew.metadata.Metadata;
import com.drew.metadata.exif.ExifDirectoryBase;

/**
 * <AUTHOR>
 * @date 2021/12/17 17:39
 * @since 1.0.0
 */
public class RotateImageUtil {

    private static final Logger log = LoggerFactory.getLogger(RotateImageUtil.class);

    /**
     * 对图片进行旋转
     *
     * @param src 被旋转图片
     * @param angel 旋转角度
     * @return 旋转后的图片
     */
    public static BufferedImage Rotate(Image src, int angel) {
        int srcWidth = src.getWidth(null);
        int srcHeight = src.getHeight(null);
        // 计算旋转后图片的尺寸
        Rectangle rect_des = CalcRotatedSize(new Rectangle(new Dimension(srcWidth, srcHeight)), angel);
        BufferedImage res = null;
        res = new BufferedImage(rect_des.width, rect_des.height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2 = res.createGraphics();
        // 进行转换
        g2.translate((rect_des.width - srcWidth) / 2, (rect_des.height - srcHeight) / 2);
        g2.rotate(Math.toRadians(angel), srcWidth / 2, srcHeight / 2);

        g2.drawImage(src, null, null);
        return res;
    }

    /**
     * 计算旋转后的图片
     *
     * @param src 被旋转的图片
     * @param angel 旋转角度
     * @return 旋转后的图片
     */
    public static Rectangle CalcRotatedSize(Rectangle src, int angel) {
        // 如果旋转的角度大于90度做相应的转换
        if (angel >= 90) {
            if (angel / 90 % 2 == 1) {
                int temp = src.height;
                src.height = src.width;
                src.width = temp;
            }
            angel = angel % 90;
        }

        double r = Math.sqrt(src.height * src.height + src.width * src.width) / 2;
        double len = 2 * Math.sin(Math.toRadians(angel) / 2) * r;
        double angelAlpha = (Math.PI - Math.toRadians(angel)) / 2;
        double angelDeltaWidth = Math.atan((double)src.height / src.width);
        double angelDeltaHeight = Math.atan((double)src.width / src.height);

        int lenDeltaWidth = (int)(len * Math.cos(Math.PI - angelAlpha - angelDeltaWidth));
        int lenDeltaHeight = (int)(len * Math.cos(Math.PI - angelAlpha - angelDeltaHeight));
        int desWidth = src.width + lenDeltaWidth * 2;
        int desHeight = src.height + lenDeltaHeight * 2;
        return new Rectangle(new Dimension(desWidth, desHeight));
    }

    public static void rotateImage(File file) {
        try {
            Metadata metadata = ImageMetadataReader.readMetadata(file);
            StringBuilder description = new StringBuilder();
            metadata.getDirectories().forEach(directory -> {
                directory.getTags().forEach(tag -> {
                    if (tag.getTagType() == ExifDirectoryBase.TAG_ORIENTATION) {
                        description.append(tag.getDescription().replaceAll(" ", ""));
                    }
                });
            });
            if (description.length() > 0) {
                int rotateIndex = description.indexOf("Rotate");
                int cwIndex = description.indexOf("CW");
                if (rotateIndex >= 0 && cwIndex > 0 && rotateIndex < cwIndex) {
                    int angel = Integer.parseInt(description.substring(rotateIndex + 6, cwIndex));
                    BufferedImage oldImage = ImageIO.read(file);
                    BufferedImage newImage = Rotate(oldImage, angel);
                    ImageIO.write(newImage, "jpg", file);
                    newImage.getGraphics().dispose();
                    oldImage.getGraphics().dispose();
                }
            }
        } catch (ImageProcessingException e) {
            log.error("rotateImage error", e);
        } catch (IOException e) {
            log.error("rotateImage error", e);
        }

    }
}
