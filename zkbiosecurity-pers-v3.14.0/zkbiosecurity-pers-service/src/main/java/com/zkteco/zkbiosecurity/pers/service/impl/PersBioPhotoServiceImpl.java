package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.PersBioPhotoDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersBioPhoto;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.service.PersBioPhotoService;
import com.zkteco.zkbiosecurity.pers.service.PersParamsService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.utils.PersPersonUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoItem;
import com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoSaveItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.security.SecurityService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

@Service
@Transactional
public class PersBioPhotoServiceImpl implements PersBioPhotoService {
    @Autowired
    private PersBioPhotoDao persBioPhotoDao;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private SecurityService securityService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return null;
    }

    @Override
    public boolean deleteByIds(String ids) {
        return false;
    }

    @Override
    public PersBioPhotoItem saveItem(PersBioPhotoItem item) {
        PersBioPhoto persBioPhoto = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> persBioPhotoDao.findById(id)).orElse(new PersBioPhoto());
        ModelUtil.copyPropertiesIgnoreNull(item, persBioPhoto);
        persBioPhotoDao.save(persBioPhoto);
        item.setId(persBioPhoto.getId());
        return item;
    }

    @Override
    public PersBioPhotoItem saveItemByPersonId(PersBioPhotoItem item) {
        if (StringUtils.isNotBlank(item.getPersonId())) {
            PersBioPhoto persBioPhoto = new PersBioPhoto();
            List<PersBioPhoto> persBioPhotoList =
                persBioPhotoDao.findByPersonIdAndBioType(item.getPersonId(), item.getBioType());
            if (persBioPhotoList != null && !persBioPhotoList.isEmpty()) {
                persBioPhoto = persBioPhotoList.get(0);
            }
            ModelUtil.copyPropertiesIgnoreNullWithProperties(item, persBioPhoto, "id");
            persBioPhotoDao.save(persBioPhoto);
            item.setId(persBioPhoto.getId());
        }
        return item;
    }

    @Override
    public void deleteByPersonId(String personId) {
        persBioPhotoDao.deleteByPersonId(personId);
    }

    @Override
    public void saveBioPhoto(PersBioPhotoSaveItem item) {
        String pin = item.getPersonPin();
        PersPerson persPerson =
            Optional.ofNullable(pin).filter(StringUtils::isNotBlank).map(persPersonDao::findByPin).orElse(null);
        // 有效图片数据才更新抠图及表数据
        if (Objects.nonNull(persPerson) && StringUtils.isNotBlank(item.getPhotoBase64())
            && PersPersonUtil.isImage(item.getPhotoBase64())) {
            // 更新人员抠图信息
            PersBioPhotoItem persBioPhotoItem = new PersBioPhotoItem();
            persBioPhotoItem.setPersonId(persPerson.getId());
            persBioPhotoItem.setPersonPin(persPerson.getPin());
            if (item.getBioType() != null) {
                persBioPhotoItem.setBioType(item.getBioType());
            } else {
                persBioPhotoItem.setBioType(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
            }
            String filePath = null;
            if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.equals(persBioPhotoItem.getBioType())) {
                FileUtil.saveCropFaceToServer(pin, item.getFileName(), item.getPhotoBase64());
                filePath = FileUtil.getCropFacePath(pin) + FileUtil.separator + item.getFileName();
            } else {
                // 非人脸比对照片保存路径为：upload/pers/user/bioPhoto/bioType/pin
                String photoPath = "upload/pers/user/bioPhoto/" + persBioPhotoItem.getBioType() + "/"
                    + persBioPhotoItem.getPersonPin();
                FileUtil.saveFile(photoPath, item.getFileName(), item.getPhotoBase64(), false);
                filePath = photoPath + FileUtil.separator + item.getFileName();
            }
            FileEncryptUtil.encryptFileByPath(filePath);
            persBioPhotoItem.setPhotoPath(filePath);
            saveItemByPersonId(persBioPhotoItem);
            // 人员头像不存在，比对照片保存成人员头像
            if (StringUtils.isBlank(persPerson.getPhotoPath()) && item.getUpdateAvatar() != null
                && item.getUpdateAvatar()) {
                PersPersonItem persPersonItem = ModelUtil.copyProperties(persPerson, new PersPersonItem());
                persPersonItem.setPhotoBase64(item.getPhotoBase64());
                persPersonService.updateAvatar(persPersonItem);
            }
        }
    }

    @Override
    public List<PersBioPhotoItem> findByPersonIdIn(List<String> personIds) {
        List<PersBioPhotoItem> persBioPhotoItemList = new ArrayList<>();
        List<PersBioPhoto> persBioPhotoList = persBioPhotoDao.findByPersonIdIn(personIds);
        if (!persBioPhotoList.isEmpty()) {
            persBioPhotoList.forEach(persBioPhoto -> {
                // 图片路径upload前面不能带有"/",否则下发到门禁设备获取不到图片
                String photoPath = persBioPhoto.getPhotoPath();
                if (StringUtils.isNotBlank(photoPath) && photoPath.startsWith("/")) {
                    photoPath = photoPath.substring(1, photoPath.length());
                    persBioPhoto.setPhotoPath(photoPath);
                }
            });
            persBioPhotoItemList = ModelUtil.copyListProperties(persBioPhotoList, PersBioPhotoItem.class);
        }
        return persBioPhotoItemList;
    }

    @Override
    public void deleteByPersonPinsAndBioTypes(List<String> personPins, List<Short> bioTypes) {
        persBioPhotoDao.deleteByPersonPinsAndBioTypes(personPins, bioTypes);
    }

    @Override
    public Map<String, Map<String, Integer>> getBioTypeAndCountByPersonIdList(Collection<String> personIds) {
        List<Object[]> bioPhotoCount = persBioPhotoDao.countByPersonIdsAndGroupByBioType(personIds);
        // 0:为 bioType 1：为 count 2:人员id
        return bioPhotoCount.stream().collect(Collectors.groupingBy((Object[] o) -> o[2].toString(), Collectors
            .toMap((Object[] o) -> o[0].toString(), (Object[] o) -> Integer.valueOf(o[1].toString()), (x, y) -> y)));
    }

    @Override
    public List<PersBioPhotoItem> getItemAndBase64ByPerIds(String sessionId, String ids) {
        List<PersBioPhotoItem> itemList = new ArrayList<>();
        if (StringUtils.isNotBlank(ids)) {
            List<String> perIds = StrUtil.strToList(ids);
            List<String> perPins = persPersonService.getPinsByIds(perIds);
            Map<String, PersBioPhoto> persBioPhotoMap = new HashMap<>();
            boolean photoEncrypt = securityService.checkPermissionExceptSupperUser(sessionId, "pers:photo:encryptProp");
            boolean pinEncrypt = securityService.checkPermissionExceptSupperUser(sessionId, "acc:pin:encryptProp");
            String pinEncryptMode = baseSysParamService.getValByName("pers.pin.encryptMode");
            List<PersBioPhoto> persBioPhotoList =
                persBioPhotoDao.findByPersonIdInAndBioType(perIds, BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
            if (persBioPhotoList != null) {
                persBioPhotoMap = CollectionUtil.listToKeyMap(persBioPhotoList, PersBioPhoto::getPersonPin);
            }
            for (String pin : perPins) {
                PersBioPhotoItem item = new PersBioPhotoItem();
                item.setPersonPin(pin);
                if (StringUtils.isNotBlank(pin) && pinEncrypt) {
                    item.setPersonPin(StrUtil.convertToEncrypt(pin, pinEncryptMode));
                }
                if (persBioPhotoMap.containsKey(pin)) {
                    PersBioPhoto persBioPhoto = persBioPhotoMap.get(pin);
                    String photoBase64 = FileEncryptUtil.getDecryptFileBase64(persBioPhoto.getPhotoPath());
                    if (StringUtils.isNotBlank(photoBase64) && photoEncrypt) {
                        photoBase64 = ImgEncodeUtil.base64BoxBlurFilter(photoBase64);
                    }
                    item.setPhotoBase64(PersConstants.PERSON_BASE64_PREFIX + photoBase64);
                }
                itemList.add(item);
            }
        }
        return itemList;
    }

    @Override
    public List<PersBioPhotoItem> saveItemByPersonIds(List<PersBioPhotoItem> items) {
        if (items != null && !items.isEmpty()) {
            List<String> personIds =
                (List<String>)CollectionUtil.getPropertyList(items, PersBioPhotoItem::getPersonId, "-1");
            List<PersBioPhoto> savePersBioPhotoList = new ArrayList<>();
            List<PersBioPhoto> persBioPhotoList = persBioPhotoDao.findByPersonIdIn(personIds);
            Map<String, PersBioPhoto> persBioPhotoMap =
                CollectionUtil.listToKeyMap(persBioPhotoList, PersBioPhoto::getPersonId);
            for (PersBioPhotoItem item : items) {
                PersBioPhoto persBioPhoto = persBioPhotoMap.get(item.getPersonId());
                if (persBioPhoto == null) {
                    persBioPhoto = new PersBioPhoto();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(item, persBioPhoto, "id");
                savePersBioPhotoList.add(persBioPhoto);
            }
            persBioPhotoDao.saveAll(savePersBioPhotoList);

        }
        return items;
    }

    @Override
    public void saveBioPhotoAndUpdateAvatar(PersBioPhotoSaveItem item) {
        item.setUpdateAvatar(true);
        saveBioPhoto(item);
    }
}
