package com.zkteco.zkbiosecurity.pers.dao;

import java.util.Collection;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersPersonnallistPerson;

/**
 * 名单库人员
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-08 下午08:30
 */
public interface PersPersonnallistPersonDao extends BaseDao<PersPersonnallistPerson, String> {

    /**
     * 根据名单库id查找人员id集合
     *
     * <AUTHOR>
     * @date 2020/8/7 10:50
     */
    @Query("SELECT t.personId FROM PersPersonnallistPerson t where t.personnallistId = ?1")
    List<String> findPersonIdByPersonnallistId(String id);

    /**
     * 根据名单库id和人员id获取id
     *
     * @param personnallistId
     * @param personId
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021-01-06 11:18
     * @since 1.0.0
     */
    @Query("SELECT t.id FROM PersPersonnallistPerson t where t.personnallistId = ?1 and t.personId = ?2")
    List<String> findIdByPersonnallistIdAndPersonId(String personnallistId, String personId);

    /**
     * 根据名单库id和人员ids获取人员和名单库关系对象
     *
     * @param personnallistId
     * @param personIds
     * @return
     * <AUTHOR>
     * @date 2021-11-29 11:18
     * @since 1.0.0
     */
    List<PersPersonnallistPerson> findBypersonnallistIdAndPersonIdIn(String personnallistId, List<String> personIds);

    /**
     * 根据人员id查找所在名单库id集合
     *
     * <AUTHOR>
     * @date 2020/8/7 10:50
     */
    @Query("SELECT t.personnallistId FROM PersPersonnallistPerson t where t.personId in(?1)")
    List<String> findPersonnallistIdsByPersonId(List<String> personIds);

    /**
     * 根据名单库id和人员id删除记录
     *
     * <AUTHOR>
     * @date 2020/8/7 10:50
     */
    @Modifying
    @Transactional
    void deleteByPersonnallistIdAndPersonId(String personnallistId, String personId);

    /**
     * 以名单库id分组统计人员id数量
     *
     * <AUTHOR>
     * @date 2020/8/7 10:50
     */
    @Query(
        value = "SELECT t.personnallist_id,count(t.person_id) from PERS_PERSONNALLIST_PERSON t group by t.personnallist_id",
        nativeQuery = true)
    List<Object[]> getCountByPersonnelListGroup();

    /**
     * 以名单库id分组统计人员id数量
     * 
     * @param userId:
     * @return java.util.List<java.lang.Object[]>
     * <AUTHOR>
     * @throws
     * @date 2022-08-18 14:04
     * @since 1.0.0
     */
    @Query(
        value = "SELECT t.personnallist_id,count(t.person_id) from PERS_PERSONNALLIST_PERSON t left join PERS_PERSON p on t.PERSON_ID = p.id "
            + "where p.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=?1) group by t.personnallist_id",
        nativeQuery = true)
    List<Object[]> getCountByUserIdAndPersonnelListGroup(String userId);

    /**
     * 根据名单库id统计数据数量
     *
     * <AUTHOR>
     * @date 2020/8/7 10:51
     */
    Integer countPersPersonnallistPeopleByPersonnallistId(String personnallistId);

    /**
     * 根据ids获取人员
     * 
     * @param ids
     * @return
     */
    List<PersPersonnallistPerson> findByIdIn(List<String> ids);

    /**
     * 根据人员ids获取人员名单库与人员关系表的记录
     * 
     * @param personIds
     * @return
     */
    List<PersPersonnallistPerson> findByPersonIdIn(List<String> personIds);

    /**
     * 根据人员ID删除
     *
     * @param personIdList: 人员ID
     * @return a
     * @throws a
     * <AUTHOR>
     * @date 2020-0924 15:08:41
     * @since 1.0.0
     */
    void deleteByPersonIdIn(List<String> personIdList);

    /**
     * 根据名单库id获取集合对象
     *
     * @param personnallistId
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersPersonnallistPerson>
     * <AUTHOR>
     * @date 2021/5/20 11:38
     * @since 1.0.0
     */
    List<PersPersonnallistPerson> findByPersonnallistId(String personnallistId);

    /**
     * 根据名单库ids获取人员
     * 
     * @param personnelIdList
     * @return
     */
    List<PersPersonnallistPerson> findByPersonnallistIdIn(List<String> personnelIdList);

    /**
     * 根据人员id获取名单库人员数据
     *
     * @param personId
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersPersonnallistPerson>
     * <AUTHOR>
     * @date 2021-05-31 15:22
     * @since 1.0.0
     */
    List<PersPersonnallistPerson> findByPersonId(String personId);

    /**
     * 通过人员id更新名单库id
     *
     * @param personId
     * @param oldPersonnelListId
     * @param newPersonnelListId
     * @return void
     * <AUTHOR>
     * @date 2021-10-26 17:07
     * @since 1.0.0
     */
    @Modifying
    @Query("update PersPersonnallistPerson t set t.personnallistId =?3 where t.personId=?1 and t.personnallistId = ?2")
    void updatePersonListIdByPersonId(String personId, String oldPersonnelListId, String newPersonnelListId);

    @Query(
        value = "SELECT t.* from PERS_PERSONNALLIST_PERSON t left join PERS_PERSONNAL_LIST l on t.personnallist_id=l.id where l.TYPE=?1 and t.person_id in (?2)",
        nativeQuery = true)
    List<PersPersonnallistPerson> getByListTypeAndPersId(String personnallistType, List<String> personIds);

    @Modifying
    @Query(
        value = "delete from PERS_PERSONNALLIST_PERSON where person_id in (?2) and personnallist_id in(SELECT l.id from PERS_PERSONNAL_LIST l where l.TYPE=?1)",
        nativeQuery = true)
    void deleteByPersonnallistTypeAndPersonId(String personnallistType, List<String> personIds);

    List<PersPersonnallistPerson> findByPersonPinIn(Collection<String> personPins);
}
