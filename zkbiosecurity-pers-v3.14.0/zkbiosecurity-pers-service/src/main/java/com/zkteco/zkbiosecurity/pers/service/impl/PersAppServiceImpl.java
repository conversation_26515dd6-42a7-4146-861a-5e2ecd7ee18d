package com.zkteco.zkbiosecurity.pers.service.impl;

import java.io.File;
import java.math.BigInteger;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.service.Acc4PersPersonService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4PersonLevelItem;
import com.zkteco.zkbiosecurity.auth.constants.AppConstant;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.auth.vo.AuthUserItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPhotoItem;
import com.zkteco.zkbiosecurity.pers.app.vo.PersAppPersonItem;
import com.zkteco.zkbiosecurity.pers.app.vo.PersAppPersonLevelItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.PersCardDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.utils.PersPersonUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeExtItem;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersTempPersonItem;
import com.zkteco.zkbiosecurity.system.app.constants.BaseAppConstants;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;

/**
 * <AUTHOR>
 * @Date: 2018/11/30 11:07
 */
@Service
@Transactional
public class PersAppServiceImpl implements PersAppService {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private PersApiPersonService persApiPersonService;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private PersCardDao persCardDao;
    @Autowired(required = false)
    private Acc4PersPersonService acc4PersPersonService;
    @Autowired
    private PersTempPersonService persTempPersonService;
    @Autowired
    private PerTempRegService perTempRegService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    private static Pattern NUMBER_PATTERN = Pattern.compile("[0-9]*");
    private static Pattern HEX_PATTERN = Pattern.compile("[A-Fa-f0-9]*");
    private static Pattern NUMBER_LETTER_PATTERN = Pattern.compile("^(?!0$|0+$)[a-zA-Z0-9]+$");
    private static Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$");

    @Override
    public AppResultMessage editAppPerson(PersAppPersonItem persAppPersonItem) {
        String result = checkData(persAppPersonItem);
        if (StringUtils.isNotBlank(result)) {
            return AppResultMessage.failMessage().setRet(result);
        }
        String appPersonPin = persAppPersonItem.getPin();
        PersPersonItem persPersonItem = null;
        int ret = 0;
        if (StringUtils.isBlank(persAppPersonItem.getId())) {
            // 新增
            persPersonItem = new PersPersonItem();
            // 新增人员，进行许可校验
            if (!persPersonService.persLicenseCheck()) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_lICENSE_MAXCOUNT));
            }
            if (StringUtils.isBlank(appPersonPin)) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSONPIN_ISNULL));
            }
            PersPersonItem tempPersonItem = persPersonService.getItemByPin(appPersonPin);
            // 判断人员编号是否存在
            if (tempPersonItem != null) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_PIN_EXIST));
            }
            PersLeavePersonItem persLeavePersonItem = persLeavePersonService.getItemByPin(appPersonPin);
            // 离职人员保留编号，判断离职人员是否存在
            if (persLeavePersonItem != null) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_PIN_EXIST));
            }
        } else {
            persPersonItem = persPersonService.getItemById(persAppPersonItem.getId());
            // 编辑时找不到对应人员信息
            if (persPersonItem == null) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSONID_NOTEXIST));
            }
            // 人员禁用不可编辑
            if (persPersonItem.getEnabledCredential() != null && !persPersonItem.getEnabledCredential()) {
                return AppResultMessage.failMessage(I18nUtil.i18nCode("pers_person_disabledNotOp"))
                    .setRet(String.valueOf(PersConstants.PERSON_DISABLED_NOPOP));
            }
        }
        ModelUtil.copyPropertiesIgnoreNull(persAppPersonItem, persPersonItem);

        // 判断卡号重复
        if (StringUtils.isNotBlank(persAppPersonItem.getCardNo())) {
            ret = persCardService.isExitCardNo(persPersonItem, persAppPersonItem.getCardNo());
            if (ret < 0) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_CARDNO_DURESS));
            }
            persPersonItem.setCardNos(persAppPersonItem.getCardNo());
        }
        // 判断密码重复
        if (StringUtils.isNotBlank(persPersonItem.getPersonPwd())) {
            ret = persApiPersonService.checkPwd(persPersonItem);
            if (ret < 0) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_PWD_DURESS));
            }
        }

        // 判断手机号重复
        if (StringUtils.isNotBlank(persAppPersonItem.getMobilePhone())) {
            PersPersonItem persItem = persPersonService.getItemByMobile(persAppPersonItem.getMobilePhone());
            if (persItem != null && !persItem.getPin().equals(persPersonItem.getPin())) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_PHONE_REPEAT));
            }
        }
        // 判断邮箱重复
        if (StringUtils.isNotBlank(persAppPersonItem.getEmail())) {
            PersPersonItem persItem = persPersonService.getItemByEmail(persAppPersonItem.getEmail());
            if (persItem != null && !persItem.getPin().equals(persPersonItem.getPin())) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_EMAIL_REPEAT));
            }
        }

        if (StringUtils.isNotBlank(persAppPersonItem.getPersonPhoto())) {
            String photoPath = FileUtil.saveFileToServer("pers", "user/avatar", persPersonItem.getPin() + ".jpg",
                persAppPersonItem.getPersonPhoto());
            persPersonItem.setPhotoPath(photoPath);
        }
        // 管理版APP
        persPersonItem.setIsFrom("ADMIN_APP_ADD");
        AuthDepartmentItem authDepartmentItem = null;
        if (StringUtils.isNotBlank(persAppPersonItem.getDeptCode())) {
            authDepartmentItem = authDepartmentService.getItemByCode(persAppPersonItem.getDeptCode());
        }
        if (StringUtils.isNotBlank(persAppPersonItem.getDeptId())) {
            authDepartmentItem = authDepartmentService.getItemById(persAppPersonItem.getDeptId());
        }
        if (authDepartmentItem == null) {
            authDepartmentItem = authDepartmentService.getDefautDept();
        }
        persPersonItem.setDeptId(authDepartmentItem.getId());
        persPersonItem.setDeptCode(authDepartmentItem.getCode());
        persPersonItem.setDeptName(authDepartmentItem.getName());
        Map<String, String> extParam = buildExtParamMap(persAppPersonItem, persPersonItem);
        persPersonItem = persPersonService.saveItem(persPersonItem, null, new PersAttributeExtItem(), extParam);
        persAppPersonItem.setId(persPersonItem.getId());
        ModelUtil.copyPropertiesIgnoreNull(persPersonItem, persAppPersonItem);
        return AppResultMessage.successMessage().setData(persAppPersonItem);
    }

    @Override
    public AppResultMessage getDepts(String token) {
        String userInfo = EncrypAESUtil.decryptToString(token);
        String username = userInfo.split("_", 2)[0];
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        AuthUserItem authUserItem = authUserService.getItemByUsername(username);
        JSONArray departmentArray = new JSONArray();
        if (authUserItem != null) {
            List<AuthDepartmentItem> authDepartmentItemList = null;
            if (authUserItem.getIsSuperuser()) {
                // 超级用户获取所有部门信息
                authDepartmentItemList = authDepartmentService.getByCondition(new AuthDepartmentItem());
            } else if (StringUtils.isNotBlank(authUserItem.getDeptIds())) {
                // 非超级用户且有授权部门，获取授权部门信息
                authDepartmentItemList = authDepartmentService.getChildDeptItemsByDeptIds(authUserItem.getDeptIds());
            }

            if (authDepartmentItemList != null && !authDepartmentItemList.isEmpty()) {
                // 根部门列表
                List<AuthDepartmentItem> rootDeptList = new ArrayList<>();
                boolean isFirstDept = true;
                List<String> deptCode = (List<String>)CollectionUtil.getPropertyList(authDepartmentItemList,
                    AuthDepartmentItem::getCode, "-1");
                // 过滤没有上级部门的部门信息，对子部门进行分组
                Map<String, List<AuthDepartmentItem>> deptMap = authDepartmentItemList.stream().filter(item -> {
                    if (StringUtils.isBlank(item.getParentCode()) || !deptCode.contains(item.getParentCode())) {
                        rootDeptList.add(item);
                        return false;
                    }
                    return true;
                }).collect(Collectors.groupingBy(AuthDepartmentItem::getParentCode));
                for (AuthDepartmentItem departmentItem : rootDeptList) {
                    JSONObject parentObj = new JSONObject();
                    JSONArray retJsonChild = new JSONArray();
                    parentObj.put("id", departmentItem.getId());
                    parentObj.put("text", departmentItem.getName());
                    parentObj.put("code", departmentItem.getCode());
                    parentObj.put("selected", "false");
                    // 防止不同用户登录时，仍显示部门名称，所以让第一个部门选中
                    if (isFirstDept) {
                        parentObj.put("selected", "true");
                        isFirstDept = false;
                    }
                    // 判断是否存在子部门
                    if (deptMap.get(departmentItem.getCode()) != null
                        && !deptMap.get(departmentItem.getCode()).isEmpty()) {
                        // 组装子部门数据
                        retJsonChild = buildChildDeptTree(departmentItem, deptMap);
                    }
                    parentObj.put("children", retJsonChild);
                    departmentArray.add(parentObj);
                }
            }
        }
        appResultMessage.setData(departmentArray);
        return appResultMessage;
    }

    @Override
    public AppResultMessage getPersonDataCount() {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        Map<String, String> dataCount = persPersonService.dataCount();
        appResultMessage.setData(dataCount);
        return appResultMessage;
    }

    @Override
    public AppResultMessage getPersonByPinOrName(String filter, String token, int pageNo, int pageSize) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        List<PersAppPersonItem> persAppPersonItemList = new ArrayList<>();
        // 根据用户及过滤条件获取查询SQL
        String personQuery = buildQuerySql(filter, token);
        JSONObject dataJson = new JSONObject();
        if (StringUtils.isNotBlank(personQuery)) {
            Pager pager = persPersonDao.getItemsBySql(PersPersonItem.class, personQuery, pageNo - 1, pageSize);
            List<PersPersonItem> persPersonItemList = (List<PersPersonItem>)pager.getData();
            if (!persPersonItemList.isEmpty()) {
                String deptIds = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getDeptId);
                List<AuthDepartmentItem> authDepartmentItems = authDepartmentService.getItemsByIds(deptIds);
                Map<String, AuthDepartmentItem> authDepartmentItemMap =
                    CollectionUtil.itemListToIdMap(authDepartmentItems);
                persPersonItemList.forEach(personItem -> {
                    // 设置部门信息
                    AuthDepartmentItem authDepartmentItem = authDepartmentItemMap.get(personItem.getDeptId());
                    if (authDepartmentItem != null) {
                        personItem.setDeptName(authDepartmentItem.getName());
                        personItem.setDeptCode(authDepartmentItem.getCode());
                    }
                    // 设置人员卡号
                    List<String> cardNos = persCardDao.findByPersonPinOrderByCardType(personItem.getPin());
                    if (Objects.nonNull(cardNos) && !cardNos.isEmpty()) {
                        personItem.setCardNos(StringUtils.join(cardNos, ","));
                    }
                    // 避免app前端页面姓名为null，显示null
                    if (StringUtils.isBlank(personItem.getName())) {
                        personItem.setName("");
                    }
                    if (StringUtils.isBlank(personItem.getLastName())) {
                        personItem.setLastName("");
                    }
                    persAppPersonItemList.add(buildAppPerson(personItem, token));
                });
            }
            dataJson.put("totalCount", pager.getTotal());
        } else {
            dataJson.put("totalCount", 0);
        }
        dataJson.put("rows", persAppPersonItemList);
        appResultMessage.setData(dataJson);
        return appResultMessage;
    }

    @Override
    public AppResultMessage getPersonByPin(String pin, String token) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem != null) {
            appResultMessage.setData(buildAppPerson(persPersonItem, token));
        }
        return appResultMessage;
    }

    /**
     * 组装APP人员信息
     * 
     * @auther lambert.li
     * @date 2018/12/3 15:40
     * @param personItem
     * @return
     */
    private PersAppPersonItem buildAppPerson(PersPersonItem personItem, String token) {
        PersAppPersonItem persAppPersonItem = ModelUtil.copyPropertiesIgnoreNull(personItem, new PersAppPersonItem());
        // 图片解密
        if (StringUtils.isNotBlank(personItem.getPhotoPath())) {
            // 图片解密
            String thumbPath = FileUtil.getThumbPath(personItem.getPhotoPath());
            if (FileUtil.fileExists(thumbPath)) {
                // 设置缩略图地址
                String photoBase64 = FileEncryptUtil.getDecryptFileBase64(thumbPath);
                // 设置base64数据
                persAppPersonItem.setPhotoImgBase64(PersConstants.PERSON_BASE64_PREFIX + photoBase64);
            } else {
                // 设置base64数据
                String photoBase64 = FileEncryptUtil.getDecryptFileBase64(personItem.getPhotoPath());
                persAppPersonItem.setPhotoImgBase64(PersConstants.PERSON_BASE64_PREFIX + photoBase64);
            }
        }
        // 设置人员头像
        persAppPersonItem.setPhotoImg(StringUtils.isNotBlank(personItem.getPhotoPath())
            ? personItem.getPhotoPath() + "?t=" + System.currentTimeMillis() : "/images/userImage.gif");
        if (personItem.getBirthday() != null) {
            // 设置人员出生日期
            persAppPersonItem
                .setBirthday(DateUtil.dateToString(personItem.getBirthday(), DateUtil.DateStyle.YYYY_MM_DD));
        }
        persAppPersonItem.setCardNo("");
        if (StringUtils.isNotBlank(personItem.getCardNos())) {
            List<String> cardNoList = (List<String>)CollectionUtil.strToList(personItem.getCardNos());
            // 设置主卡卡号
            persAppPersonItem.setCardNo(cardNoList.get(0));
        }
        Map<Short, Integer> personBioTemplateMap =
            persBioTemplateService.countBioTemplateCountByPersonIdList(CollectionUtil.strToList(personItem.getId()));
        if (personBioTemplateMap != null && !personBioTemplateMap.isEmpty()) {
            // 设置人员指纹，面部，指静脉模板数量
            persAppPersonItem
                .setFingerTemplateCount(personBioTemplateMap.containsKey(BaseConstants.BaseBioType.FP_BIO_TYPE)
                    ? personBioTemplateMap.get(BaseConstants.BaseBioType.FP_BIO_TYPE) : 0);
            persAppPersonItem
                .setFaceTemplateCount(personBioTemplateMap.containsKey(BaseConstants.BaseBioType.FACE_BIO_TYPE)
                    ? personBioTemplateMap.get(BaseConstants.BaseBioType.FACE_BIO_TYPE) : 0);
            persAppPersonItem
                .setVeinTemplateCount(personBioTemplateMap.containsKey(BaseConstants.BaseBioType.VEIN_BIO_TYPE)
                    ? personBioTemplateMap.get(BaseConstants.BaseBioType.VEIN_BIO_TYPE) : 0);
        } else {
            persAppPersonItem.setFingerTemplateCount(0);
            persAppPersonItem.setFaceTemplateCount(0);
            persAppPersonItem.setVeinTemplateCount(0);
        }
        List<PersAppPersonLevelItem> persAppPersonLevelItemList = new ArrayList<>();
        if (Objects.nonNull(acc4PersPersonService)) {
            Map<String, String> extParams = acc4PersPersonService.getAccPersonExtParam(personItem.getId());
            if (extParams.containsKey("acc.privilege")) {
                // 设置是否是设备管理员
                persAppPersonItem.setOpAuth("14".equals(extParams.get("acc.privilege")) ? true : false);
            }
            // 获取人员权限信息
            List<Acc4PersonLevelItem> personLevelItemList =
                acc4PersPersonService.getPersonLevelByPersonId(personItem.getId(), token);
            if (!personLevelItemList.isEmpty()) {
                personLevelItemList.forEach(personLevel -> {
                    PersAppPersonLevelItem persAppPersonLevelItem = new PersAppPersonLevelItem();
                    persAppPersonLevelItem.setAccLevelId(personLevel.getLevelId());
                    persAppPersonLevelItem.setAccLevelName(personLevel.getLevelName());
                    persAppPersonLevelItem.setSelected(personLevel.getSelected());
                    persAppPersonLevelItemList.add(persAppPersonLevelItem);
                });
            }
        }
        persAppPersonItem.setAccLevels(persAppPersonLevelItemList);
        return persAppPersonItem;
    }

    /**
     * 递归组装子部门数据
     * 
     * @auther lambert.li
     * @date 2018/12/3 11:39
     * @param departmentItem
     * @param deptMap
     * @return
     */
    private JSONArray buildChildDeptTree(AuthDepartmentItem departmentItem,
        Map<String, List<AuthDepartmentItem>> deptMap) {
        List<AuthDepartmentItem> childrenDeptList = deptMap.get(departmentItem.getCode());
        JSONArray result = new JSONArray();
        if (childrenDeptList == null || childrenDeptList.isEmpty()) {
            return result;
        }
        for (AuthDepartmentItem item : childrenDeptList) {
            JSONArray retJsonChild = new JSONArray();
            JSONObject childObj = new JSONObject();
            childObj.put("id", item.getId());
            childObj.put("text", item.getName());
            childObj.put("code", item.getCode());
            // 判断是否存在子部门
            if (deptMap.get(item.getCode()) != null && !deptMap.get(item.getCode()).isEmpty()) {
                // 组装子部门数据
                retJsonChild = buildChildDeptTree(item, deptMap);
            }
            childObj.put("children", retJsonChild);
            result.add(childObj);
        }
        return result;
    }

    /**
     * 组装人员扩展参数
     * 
     * @auther lambert.li
     * @date 2018/12/3 10:10
     * @param persAppPersonItem
     * @param persPersonItem
     * @return
     */
    private Map<String, String> buildExtParamMap(PersAppPersonItem persAppPersonItem, PersPersonItem persPersonItem) {
        Map<String, String> extParams = Maps.newHashMap();
        String addLevelIds = persAppPersonItem.getAccAddAccLevelIds();
        String delLevelIds = persAppPersonItem.getAccDelAccLevelIds();
        if (StringUtils.isNotBlank(persPersonItem.getId())) {
            // 编辑
            if (Objects.nonNull(acc4PersPersonService)) {
                // 编辑时需要获取门禁人员设置参数，避免编辑覆盖门禁人员参数
                extParams = acc4PersPersonService.getAccPersonExtParam(persPersonItem.getId());
                extParams.put("moduleAuth", "acc");
                if (StringUtils.isNotBlank(extParams.get("acc.personLevelIds"))) {
                    List<String> levelIds = (List<String>)CollectionUtil.strToList(extParams.get("acc.personLevelIds"));
                    if (StringUtils.isNotBlank(addLevelIds)) {
                        List<String> addLevelIdList = (List<String>)CollectionUtil.strToList(addLevelIds);
                        levelIds.addAll(addLevelIdList);
                    }
                    if (StringUtils.isNotBlank(delLevelIds)) {
                        List<String> delLevelIdList = (List<String>)CollectionUtil.strToList(delLevelIds);
                        levelIds.removeAll(delLevelIdList);
                    }
                    extParams.put("acc.personLevelIds", StringUtils.join(levelIds, ","));
                } else if (StringUtils.isNotBlank(addLevelIds)) {
                    extParams.put("acc.personLevelIds", addLevelIds);
                }
            }
        } else {
            // 新增
            extParams.put("moduleAuth", "acc,att,park,pid,ele");
            if (StringUtils.isNotBlank(addLevelIds)) {
                extParams.put("acc.personLevelIds", addLevelIds);
            }
            // 没有字段则是不对开始时间和结束时间做处理
            extParams.put("acc.isSetValidTime", "false");
            if (StringUtils.isNotBlank(persAppPersonItem.getAccEndTime())
                && StringUtils.isNotBlank(persAppPersonItem.getAccStartTime())) {
                extParams.put("acc.startTime", persAppPersonItem.getAccStartTime());
                extParams.put("acc.endTime", persAppPersonItem.getAccEndTime());
                extParams.put("acc.isSetValidTime", "true");
            }
        }
        return extParams;
    }

    /**
     * 人员编辑数据格式校验方法
     * 
     * @auther lambert.li
     * @date 2018/11/30 17:27
     * @param personBean
     * @return
     */
    private String checkData(PersAppPersonItem personBean) {
        // pin号
        String pin = personBean.getPin();
        String pinCheck = checkPin(pin);
        if (StringUtils.isNotBlank(pinCheck)) {
            return pinCheck;
        }
        // 姓名
        String name = personBean.getName();
        if (StringUtils.isNotBlank(name)) {
            if (name.length() > 25) {
                return String.valueOf(PersConstants.PERSON_NAME_TOOLONG);
            }
            if (name.contains(",") || name.contains("，")) {
                // 中文逗号与英文逗号
                return String.valueOf(PersConstants.PERSON_NAME_NOCOMMA);
            }
        }
        // 姓
        String lastName = personBean.getLastName();
        if (StringUtils.isNotBlank(lastName)) {
            if (lastName.length() > 50) {
                return String.valueOf(PersConstants.PERSON_NAME_TOOLONG);
            }
            if (lastName.contains(",")) {
                return String.valueOf(PersConstants.PERSON_NAME_NOCOMMA);
            }
        }

        // 密码
        String personPwd = personBean.getPersonPwd();
        if (StringUtils.isNotBlank(personPwd)) {
            if (!NUMBER_PATTERN.matcher(personPwd).matches()) {
                return String.valueOf(PersConstants.PERSON_PWD_ONLYNUMBER);
            }
            if (personPwd.length() > 6) {
                return String.valueOf(PersConstants.PERSON_PWD_TOOLONG);
            }
        }
        // 电话号码
        String mobilePhone = personBean.getMobilePhone();
        if (StringUtils.isNotBlank(mobilePhone) && mobilePhone.length() > 20) {
            return String.valueOf(PersConstants.PERSON_PHONE_TOOLONG);
        }
        // 邮箱
        String email = personBean.getEmail();
        if (StringUtils.isNotBlank(email)) {
            if (email.length() > 100) {
                return String.valueOf(PersConstants.PERSON_EMAIL_TOOLONG);
            } else {
                Matcher mat = EMAIL_PATTERN.matcher(email);
                if (!mat.matches()) {
                    return String.valueOf(PersConstants.PERSON_EMAIL_FORMATERROR);
                }
            }
        }
        // 卡号校验
        String cardNo = personBean.getCardNo();
        if (StringUtils.isNotBlank(cardNo)) {
            if (cardNo.startsWith("&")) {
                return String.valueOf(PersConstants.PERSON_CARDNO_FORMATERROR);
            }
            String[] cardArray = cardNo.split(",");
            String cardsSupport = persParamsService.getValByName("pers.cardsSupport");
            if (cardArray.length > 1 && "false".equals(cardsSupport)) {
                return String.valueOf(PersConstants.PERSON_CARDS_NOTSUPPORT);
            }
            String cardNoCheck = null;
            for (String card : cardArray) {
                cardNoCheck = checkCardNo(card);
                if (null != cardNoCheck) {
                    return cardNoCheck;
                }
            }
        }
        return null;
    }

    /**
     * 校验人员卡号
     * 
     * @auther lambert.li
     * @date 2018/11/30 18:01
     * @param cardNo
     * @return
     */
    private String checkCardNo(String cardNo) {
        if ("0".equals(cardNo)) {
            return String.format(I18nUtil.i18nCode("pers_import_cardNoNotNull"));
        }
        String cardHex = persParamsService.getValByName("pers.cardHex");
        Integer cardBitLen = Integer.parseInt(persParamsService.getValByName("pers.cardLen"));
        Pattern pattern = NUMBER_PATTERN;
        String cardNoBinary = "";
        if (cardHex.equals(PersConstants.CARD_HEXADECIMAL)) {
            pattern = HEX_PATTERN;
        }
        if (!pattern.matcher(cardNo).matches()) {
            return String.format(I18nUtil.i18nCode("pers_import_cardNoFormatErrors"));
        }
        if (cardHex.equals(PersConstants.CARD_DECIMAL)) {
            // 卡格式为16进制
            cardNoBinary = new BigInteger(cardNo, 10).toString(2);
        } else {
            cardNoBinary = new BigInteger(cardNo, 16).toString(2);
        }
        if (cardNoBinary.length() > cardBitLen) {
            return MessageFormat.format(I18nUtil.i18nCode("pers_import_cardTooLong"), cardNo);
        }
        return null;
    }

    /**
     * 校验人员编号
     * 
     * @auther lambert.li
     * @date 2018/11/30 17:45
     * @param pin
     * @return
     */
    private String checkPin(String pin) {
        int pinLen = Integer.parseInt(persParamsService.getValByName("pers.pinLen"));
        if (StringUtils.isBlank(pin)) {
            // 不能为空
            return String.valueOf(PersConstants.PERSONPIN_ISNULL);
        }
        // 判断长度
        if (pin.length() > pinLen) {
            return String.valueOf(PersConstants.PERSON_PIN_TOOLONG);
        }
        // 过滤访客和酒店锁
        if ((9 == pin.length()) && (pin.startsWith("8") || pin.startsWith("9"))) {
            return String.valueOf(PersConstants.PERSON_PIN_FIRSTtVALID);
        }
        // 是否支持字母
        String supportLetter = persParamsService.getValByName("pers.pinSupportLetter");
        Pattern pattern = NUMBER_PATTERN;
        if ("true".equals(supportLetter)) {
            pattern = NUMBER_LETTER_PATTERN;
        } else if (pin.startsWith("0")) {
            return String.valueOf(PersConstants.PERSON_PIN_STARTWITHZERO);
        }
        Boolean formatBoolean = pattern.matcher(pin).matches();
        if (!formatBoolean) {
            if ("true".equals(supportLetter)) {
                return String.valueOf(PersConstants.PERSON_PIN_LETTERANDINT);
            }
            return String.valueOf(PersConstants.PERSON_PIN_INT);
        }
        return null;
    }

    /**
     * 设置人员查询过滤条件
     * 
     * @auther lambert.li
     * @date 2018/12/6 14:09
     * @param filter
     * @param token
     * @return
     */
    private String buildQuerySql(String filter, String token) {
        // 获取用户权限下所有人
        String userInfo = EncrypAESUtil.decryptToString(token);
        String username = userInfo.split("_", 2)[0];
        PersPersonItem persPersonItem = new PersPersonItem();
        persPersonItem.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        AuthUserItem authUserItem = authUserService.getItemByUsername(username);
        if (authUserItem == null) {
            // 用户不存在，返回空
            return null;
        }

        // 超级用户获取所有部门信息
        if (!authUserItem.getIsSuperuser()) {
            if (StringUtils.isBlank(authUserItem.getDeptIds())) {
                // 非超级用户，且未设置授权部门返回空
                return null;
            }
            // 非超级用户且有授权部门，获取授权部门信息
            List<AuthDepartmentItem> authDeptList =
                authDepartmentService.getChildDeptItemsByDeptIds(authUserItem.getDeptIds());
            persPersonItem.setInDeptId(CollectionUtil.getItemIds(authDeptList));
        }
        StringBuilder personQuery = new StringBuilder();
        personQuery.append(SQLUtil.getSqlByItem(persPersonItem));
        int orderByIndex = personQuery.indexOf("ORDER BY");
        if (StringUtils.isNotBlank(filter)) {
            // 存在过滤条件设置模糊查询过滤条件
            String query = MessageFormat.format("AND (t.PIN LIKE ''%{0}%'' OR t.NAME LIKE ''%{0}%''", filter);
            if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
                query += MessageFormat.format(" OR t.LAST_NAME LIKE ''%{0}%''", filter);
            }
            query += ") ";
            personQuery.insert(orderByIndex, query);
        }
        return personQuery.toString();
    }

    @Override
    public AppResultMessage getAccLevelsByPin(String pin, String token) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        List<PersAppPersonLevelItem> persAppPersonLevelItemList = new ArrayList<>();
        String personId = null;
        if (StringUtils.isNotBlank(pin) && persPersonItem != null) {
            personId = persPersonItem.getId();
        }
        if (Objects.nonNull(acc4PersPersonService)) {
            List<Acc4PersonLevelItem> personLevelItemList =
                acc4PersPersonService.getPersonLevelByPersonId(personId, token);
            if (!personLevelItemList.isEmpty()) {
                personLevelItemList.forEach(personLevel -> {
                    PersAppPersonLevelItem persAppPersonLevelItem = new PersAppPersonLevelItem();
                    persAppPersonLevelItem.setAccLevelId(personLevel.getLevelId());
                    persAppPersonLevelItem.setAccLevelName(personLevel.getLevelName());
                    persAppPersonLevelItem.setSelected(personLevel.getSelected());
                    persAppPersonLevelItemList.add(persAppPersonLevelItem);
                });
            }
        }
        appResultMessage.setData(persAppPersonLevelItemList);
        return appResultMessage;
    }

    @Override
    public AppResultMessage getPersonByNameOrDeptName(PersPersonItem condition, String filter, int pageNo,
        int pageSize) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        List<PersAppPersonItem> persAppPersonItemList = new ArrayList<>();

        StringBuilder personQuery = new StringBuilder();
        personQuery.append(SQLUtil.getSqlByItem(condition));
        int orderByIndex = personQuery.indexOf("ORDER BY");
        if (StringUtils.isNotBlank(filter)) {
            // 存在过滤条件设置模糊查询过滤条件
            String query = MessageFormat.format(
                "AND (LOWER(t.NAME) LIKE LOWER(''%{0}%'') OR LOWER(t.LAST_NAME) LIKE LOWER(''%{0}%'') OR LOWER(d.NAME) LIKE LOWER(''%{0}%''))",
                filter);
            personQuery.insert(orderByIndex, query);
        }

        // 根据用户及过滤条件获取查询SQL
        JSONObject dataJson = new JSONObject();
        Pager pager =
            persPersonDao.getItemsBySql(PersPersonItem.class, String.valueOf(personQuery), pageNo - 1, pageSize);
        List<PersPersonItem> persPersonItemList = (List<PersPersonItem>)pager.getData();
        if (!persPersonItemList.isEmpty()) {
            persAppPersonItemList = ModelUtil.copyListProperties(persPersonItemList, PersAppPersonItem.class);
        }
        dataJson.put("totalCount", pager.getTotal());
        dataJson.put("rows", persAppPersonItemList);
        appResultMessage.setData(dataJson);
        return appResultMessage;
    }

    @Override
    public AppResultMessage updatePersonInfo(PersApiPhotoItem person) {
        if (StringUtils.isBlank(person.getPin())) {
            // 人员编号为空
            return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSONPIN_ISNULL));
        } else {
            PersPersonItem persPersonItem = persPersonService.getItemByPin(person.getPin());
            if (persPersonItem == null) {
                // 人员不存在
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSONID_NOTEXIST));
            }
            // 人员禁用不可编辑
            if (persPersonItem.getEnabledCredential() != null && !persPersonItem.getEnabledCredential()) {
                return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_DISABLED_NOPOP));
            }
            // 判断图片有效性
            if (StringUtils.isNotBlank(person.getPersonPhoto())) {
                if (!PersPersonUtil.isImage(person.getPersonPhoto())) {
                    // 不是图片
                    return AppResultMessage.failMessage().setRet(String.valueOf(PersConstants.PERSON_PHOTO_INVALID));
                }

                String filePath = FileUtil.createUploadFileRootPath("pers", "user/avatar");
                String fileName = System.currentTimeMillis() + "";
                FileUtil.saveFile(filePath, fileName + ".jpg", person.getPersonPhoto(), false);
                String photoPath = "/" + filePath + fileName + ".jpg";

                if (StringUtils.isNotBlank(photoPath)) {
                    ZKResultMsg zkResultMsg = persPersonService.validCropFace(photoPath);
                    File tempFile = new File(FileUtil.getLocalFullPath(photoPath));
                    if (tempFile.exists()) {
                        // 删除临时图片
                        tempFile.delete();
                    }
                    if (!zkResultMsg.isSuccess()) {
                        return AppResultMessage.failMessage()
                            .setRet(String.valueOf(PersConstants.PERSON_CROPFACE_FAILE));
                    }
                    photoPath = FileUtil.saveFileToServer("pers", "tempPerson", persPersonItem.getPin() + ".jpg",
                        person.getPersonPhoto());
                }
                persPersonItem.setPhotoPath(photoPath);
            } else {
                persPersonItem.setPhotoPath(null);
            }
            if (StringUtils.isNotBlank(persPersonItem.getPhotoPath())) {

                PersTempPersonItem persTempPersonItem = ModelUtil.copyPropertiesIgnoreNullWithProperties(persPersonItem,
                    new PersTempPersonItem(), "id", "personPwd");
                persTempPersonService.savePersonRegistrar(persTempPersonItem);
                String appLang = person.getLang();
                if (!"1".equals(baseSysParamService.getValByName("pers.tempPerson.audit"))
                    && StringUtils.isNotBlank(appLang) && BaseAppConstants.APP_LANG_MAP.containsKey(appLang)) {
                    return AppResultMessage
                        .message(PersConstants.API_PERS_WAITREVIEWED, I18nUtil.getI18nByLanguage(
                            BaseAppConstants.APP_LANG_MAP.get(appLang), "pers_tempPerson_waitReview"))
                        .setData(persPersonItem.getPhotoPath());
                } else {
                    return AppResultMessage.successMessage().setData(persPersonItem.getPhotoPath());
                }
            }
        }
        return AppResultMessage.successMessage();
    }

    @Override
    public AppResultMessage getPersonList(PersPersonItem condition, int pageNo, int pageSize, String username,
        String loginType) {
        AppResultMessage appResultMessage = AppResultMessage.successMessage();
        List<PersAppPersonItem> persAppPersonItemList = new ArrayList<>();
        if (AppConstant.USER_LOGIN_TYPE.equals(loginType)) {
            AuthUserItem authUserItem = authUserService.getItemByUsername(username);
            if (authUserItem == null) {
                // 用户不存在，返回空
                return appResultMessage;
            }
            if (!authUserItem.getIsSuperuser()) {
                condition.setUserId(authUserItem.getId());
            }
        }
        Pager pager = persPersonService.getItemsByPage(condition, pageNo, pageSize);
        List<PersPersonItem> persPersonItemList = (List<PersPersonItem>)pager.getData();
        if (!persPersonItemList.isEmpty()) {
            persAppPersonItemList = ModelUtil.copyListProperties(persPersonItemList, PersAppPersonItem.class);
        }
        JSONObject data = new JSONObject();
        data.put("totalCount", pager.getTotal());
        data.put("rows", persAppPersonItemList);
        return appResultMessage.setData(data);
    }
}
