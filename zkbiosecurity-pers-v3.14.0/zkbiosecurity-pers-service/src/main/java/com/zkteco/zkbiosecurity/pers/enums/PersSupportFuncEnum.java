package com.zkteco.zkbiosecurity.pers.enums;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;

/**
 * 人事功能枚举
 *
 * <AUTHOR>
 * @date 2021-02-25 14:24
 * @since 1.0.0
 */
public enum PersSupportFuncEnum {

    /** 制卡功能 Access不支持 */
    PRINT_TEMPLATE(BaseConstants.ZKBIO_ACCESS),
    /** 离职功能 Access不支持 */
    PERSON_LEAVE(BaseConstants.ZKBIO_ACCESS),
    /** 职位 Access不支持 */
    POSITION(BaseConstants.ZKBIO_ACCESS),
    /** 卡挂失 Access不支持 */
    LOSS_CARD(BaseConstants.ZKBIO_ACCESS),
    /** 韦根格式测试 Access不支持 */
    WG_FMT_TEST(BaseConstants.ZKBIO_ACCESS),
    /** 重置自助密码 Access不支持 */
    RESET_SELF_PWD(BaseConstants.ZKBIO_ACCESS);

    /** 不支持此功能的产品编码 */
    private String[] notSupportProduct;

    /**
     * 指定不支持此功能的产品
     *
     * @param notSupportProduct
     */
    PersSupportFuncEnum(String... notSupportProduct) {
        this.notSupportProduct = notSupportProduct;
    }

    /**
     * 判断产品是否支持此功能
     *
     * @return
     */
    public boolean isSupport(String productCode) {
        for (String excludeProduct : notSupportProduct) {
            if (excludeProduct.equals(productCode)) {
                return false;
            }
        }
        return true;
    }
}
