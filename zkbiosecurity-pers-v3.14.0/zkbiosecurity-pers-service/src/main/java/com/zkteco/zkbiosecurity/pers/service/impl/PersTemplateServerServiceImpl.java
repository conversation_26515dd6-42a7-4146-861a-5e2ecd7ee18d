package com.zkteco.zkbiosecurity.pers.service.impl;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.FileUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.pers.cache.PersCacheManager;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.PersTemplateServerService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.utils.SystemHttpUtil;

@Service
@Transactional
public class PersTemplateServerServiceImpl implements PersTemplateServerService {

    @Autowired
    private PersCacheManager persCacheManager;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public ZKResultMsg loginTemplateServer(String address, String username, String pwd) {
        if (StringUtils.isBlank(address) || StringUtils.isBlank(username) || StringUtils.isBlank(pwd)) {
            throw new ZKBusinessException("system_acms_paramWarn");
        }
        String loginUrl =
            address + PersConstants.TEMPLATE_SERVER_GETTOKEN + "?username=" + username + "&password=" + pwd;
        try {
            String result = SystemHttpUtil.get(loginUrl, null, 5000);
            if (StringUtils.isNotBlank(result)) {
                ZKResultMsg resultMsg = JSONObject.parseObject(result, ZKResultMsg.class);
                if (resultMsg != null) {
                    if ("0".equals(resultMsg.getRet())) {
                        JSONObject jsonObject = (JSONObject)resultMsg.getData();
                        String token = jsonObject.getString("token");
                        if (StringUtils.isNotBlank(token)) {
                            persCacheManager.setTemplateServerTokenToRedis(token);
                        }
                        return new ZKResultMsg(token);
                    }
                }
            }
        } catch (Exception e) {
            persCacheManager.delTemplateServerToken();
            throw new ZKBusinessException("common_prompt_serverFailed");
        }
        return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_prompt_serverFailed"));
    }

    @Override
    public ZKResultMsg testTemplateServerStatus() {
        String token = getServerToken();
        if (StringUtils.isNotBlank(token)) {
            String address = baseSysParamService.getValByName("pers.facialTemplate.serverAddr");
            String loginUrl = address + PersConstants.TEMPLATE_SERVER_TESTSTATUS;
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("token", token);
            String params = JSONObject.toJSONString(paramMap);
            String result = SystemHttpUtil.post(loginUrl, params, null, 5000);
            if (StringUtils.isNotBlank(result)) {
                ZKResultMsg resultMsg = JSONObject.parseObject(result, ZKResultMsg.class);
                if (resultMsg != null) {
                    if ("0".equals(resultMsg.getRet())) {
                        return new ZKResultMsg();
                    }
                }
            }
        }
        return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_prompt_serverFailed"));
    }

    /**
     * 获取token值
     * 
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2024-05-10 15:52
     * @since 1.0.0
     */
    private String getServerToken() {
        String token = persCacheManager.getTemplateServerToken();
        if (StringUtils.isBlank(token)) {
            Map<String, String> paramMap = baseSysParamService.getParamsByModule("pers.facialTemplate");
            String serverAddr = paramMap.get("pers.facialTemplate.serverAddr");
            String username = paramMap.get("pers.facialTemplate.username");
            String pwd = paramMap.get("pers.facialTemplate.pwd");
            ZKResultMsg tokenMsg = loginTemplateServer(serverAddr, username, pwd);
            if ("ok".equals(tokenMsg.getRet())) {
                token = tokenMsg.getData().toString();
            }
        }
        return token;
    }

    @Override
    public ZKResultMsg getFaceTemplateByPhoto(String photoPath) {
        String enable = baseSysParamService.getValByName("pers.facialTemplate.enable");
        if ("1".equals(enable) && StringUtils.isNotBlank(photoPath)) {
            File file = new File(FileUtil.getLocalFullPath(photoPath));
            if (file.exists()) {
                String token = getServerToken();
                if (StringUtils.isNotBlank(token)) {
                    String address = baseSysParamService.getValByName("pers.facialTemplate.serverAddr");
                    String loginUrl = address + PersConstants.TEMPLATE_SERVER_GETFACETEMPLATE;
                    Map<String, String> paramMap = new HashMap<>();
                    paramMap.put("token", token);
                    // paramMap.put("picName", picName + ".jpg");
                    String result = SystemHttpUtil.postFile(loginUrl, paramMap, file, "photo_file", 5000);
                    if (StringUtils.isNotBlank(result)) {
                        ZKResultMsg resultMsg = JSONObject.parseObject(result, ZKResultMsg.class);
                        if (resultMsg != null) {
                            if ("0".equals(resultMsg.getRet())) {
                                return resultMsg;
                            } else {
                                String msg = PersConstants.TEMPLATE_SERVER_ERROR_MAP.get(resultMsg.getRet());
                                if (StringUtils.isNotBlank(msg)) {
                                    resultMsg.setMsg(I18nUtil.i18nCode(msg));
                                    return resultMsg;
                                } else {
                                    return ZKResultMsg.failMsg();
                                }
                            }
                        }
                    }
                }
            }

            return ZKResultMsg.failMsg(I18nUtil.i18nCode("common_prompt_serverFailed"));
        }
        return new ZKResultMsg();
    }

}
