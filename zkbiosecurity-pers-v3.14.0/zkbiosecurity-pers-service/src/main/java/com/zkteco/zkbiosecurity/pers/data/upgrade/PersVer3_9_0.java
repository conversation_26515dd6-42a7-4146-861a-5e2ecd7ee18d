package com.zkteco.zkbiosecurity.pers.data.upgrade;

import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.SpringContextUtil;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.DataCleanManager;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022-10-20 15:14
 * @since 1.0.0
 */
@Slf4j
@Component
public class PersVer3_9_0 implements UpgradeVersionManager {

    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private PersAttributeService persAttributeService;
    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v3.9.0";
    }

    @Override
    public boolean executeUpgrade() {
        // 没打防疫模块，删除防疫相关数据
        Map<String, DataCleanManager> mapBeans = SpringContextUtil.getBeansOfType(DataCleanManager.class);
        DataCleanManager dataCleanManager = mapBeans.get("hepReportDataCleanManagement");
        if (dataCleanManager == null) {
            BaseSysParamItem sysParam = baseSysParamService.findByParamName("pers.enableHealthInfo");
            if (StringUtils.isNotBlank(sysParam.getId())) {
                baseSysParamService.deleteValByName("pers.enableHealthInfo");
            }
            sysParam = baseSysParamService.findByParamName("pers.exposure");
            if (StringUtils.isNotBlank(sysParam.getId())) {
                baseSysParamService.deleteValByName("pers.exposure");
            }
            sysParam = baseSysParamService.findByParamName("pers.symptom");
            if (StringUtils.isNotBlank(sysParam.getId())) {
                baseSysParamService.deleteValByName("pers.symptom");
            }
            sysParam = baseSysParamService.findByParamName("pers.visitCity");
            if (StringUtils.isNotBlank(sysParam.getId())) {
                baseSysParamService.deleteValByName("pers.visitCity");
            }
            sysParam = baseSysParamService.findByParamName("pers.remarks");
            if (StringUtils.isNotBlank(sysParam.getId())) {
                baseSysParamService.deleteValByName("pers.remarks");
            }
            persAttributeService.delHepAttribute();
        }

        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("PersPerson");
        if (subMenuItem != null) {
            AuthPermissionItem subButtonItem =
                new AuthPermissionItem("PersPersonViewBioPhoto", "pers_person_cropFaceShow", "pers:person:viewBioPhoto",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 20);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }
        subMenuItem = authPermissionService.getItemByCode("PersLeave");
        if (subMenuItem != null) {
            AuthPermissionItem subButtonItem = new AuthPermissionItem("PersLeaveExport", "common_op_export",
                "pers:leavePerson:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 5);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }
        return true;
    }

}
