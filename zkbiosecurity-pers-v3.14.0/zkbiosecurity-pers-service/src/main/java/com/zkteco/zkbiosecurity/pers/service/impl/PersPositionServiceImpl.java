/**
 * File Name: PersPositionServiceImpl Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.zkteco.business.sdk.vo.PersApiPositiontItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPositionDao;
import com.zkteco.zkbiosecurity.pers.model.PersPosition;
import com.zkteco.zkbiosecurity.pers.service.PersPositionService;
import com.zkteco.zkbiosecurity.pers.utils.ProcessBeanUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersPositionItem;
import com.zkteco.zkbiosecurity.system.service.SystemPersInfo2CloudService;
import com.zkteco.zkbiosecurity.system.vo.SystemPers2ApiPositiontItem;

/**
 * 对应百傲瑞达 PersPositionServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Service
@Transactional
public class PersPositionServiceImpl implements PersPositionService {
    @Autowired
    private PersPositionDao persPositionDao;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private SystemPersInfo2CloudService persCloudService;
    @Autowired
    private ProgressCache progressCache;

    @Override
    public PersPositionItem saveItem(PersPositionItem item) {
        PersPosition persPosition = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(persPositionDao::findById).orElseGet(() -> {
                if (Objects.nonNull(persPositionDao.findByCode(item.getCode()))) {
                    throw ZKBusinessException
                        .warnException(I18nUtil.i18nCode("pers_position_codeExist", item.getCode()));
                }
                return new PersPosition();
            });
        PersPosition oldPosition = ModelUtil.copyProperties(persPosition, new PersPosition());
        persPosition.setName(item.getName()).setCode(item.getCode()).setSortNo(item.getSortNo());
        // 前端不填写排序值时要默认一个大的排序值，放在最后
        if (Objects.isNull(persPosition.getSortNo())) {
            persPosition.setSortNo(99999);
        }
        if (StringUtils.isNoneBlank(item.getParentId())) {
            if (Objects.isNull(persPosition.getParent()) || (Objects.nonNull(persPosition.getParent())
                && !persPosition.getParent().getId().equals(item.getParentId()))) {

                PersPosition parent = persPositionDao.findById(item.getParentId()).orElse(null);
                persPosition.setParent(parent);
                // 设置上级职位信息
                if (parent != null && StringUtils.isNotBlank(persPosition.getId())) {
                    // 编辑时需要判断上级职位是否是本身或其下级职位
                    boolean isChild = isChild(Lists.newArrayList(persPosition.getId()), parent.getId());
                    if (persPosition.getId().equals(parent.getId()) || isChild) {
                        // 上级部门不能是自身或其下级部门
                        throw ZKBusinessException.warnException(I18nUtil.i18nCode("pers_position_parentMenuMsg"));
                    }
                }
            }
        } else {
            persPosition.setParent(null);
        }
        // 判断职位名称是否重复
        Boolean isNameExist = isPositionNameExist(persPosition.getId(),
            persPosition.getParent() == null ? null : persPosition.getParent().getId(), persPosition.getName());
        if (!isNameExist) {
            throw ZKBusinessException.warnException(I18nUtil.i18nCode("pers_position_nameExist"));
        }
        persPositionDao.save(persPosition);
        item.setId(persPosition.getId());
        if (!equalsPosition(persPosition, oldPosition)) {
            sendPositionToCloud(persPosition);
        }
        return item;
    }

    @Override
    public Boolean checkAncestor(String positionId, String ancestorPositionId) {
        if (positionId == null) {
            return true;
        }
        if (!positionId.equals(ancestorPositionId)) {
            PersPosition position = persPositionDao.getOne(positionId);
            while (position != null && position.getParent() != null) {
                if (position.getParent().getId().equals(ancestorPositionId)) {
                    return true;
                } else {
                    position = position.getParent();
                }
            }
        }
        return false;
    }

    @Override
    public List<PersPositionItem> getByCondition(PersPositionItem condition) {
        buildCondition(condition);
        return (List<PersPositionItem>)persPositionDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        buildCondition((PersPositionItem)condition);
        return persPositionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    private void buildCondition(PersPositionItem condition) {
        PersPosition parent = null;
        if (StringUtils.isNotBlank(condition.getParentId())) {
            parent = persPositionDao.findById(condition.getParentId()).orElse(null);
            condition.setParentId(null);
        } else if (StringUtils.isNotBlank(condition.getParentName())) {
            parent = persPositionDao.findByName(condition.getParentName());
            condition.setParentName(null);
        }
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            Collection<String> list = CollectionUtil.strToList(ids);
            List<String> codes = new ArrayList<String>();
            list.forEach(id -> {
                PersPosition persPosition = persPositionDao.findById(id).get();
                long count = persPositionDao.countByParent_Id(id);
                if (count > 0) {
                    // 含有子职位不能删除
                    throw ZKBusinessException.warnException("pers_position_existSub", persPosition.getName());
                }
                count = persPersonDao.countByPersPosition_Id(id);
                if (count > 0) {
                    // 含有人员不能删除
                    throw ZKBusinessException.warnException("pers_position_existPerson", persPosition.getName());
                }
                persPositionDao.deleteById(id);
                codes.add(persPosition.getCode());
            });
            // 异步推送 删除云上职位 add by xjing.huang
            CompletableFuture.runAsync(() -> {
                persCloudService.delPersPositionToCloud(codes);
            });
        }
        return false;
    }

    @Override
    public PersPositionItem getItemById(String id) {
        return Optional.ofNullable(id).filter(StringUtils::isNotBlank).map(i -> getByCondition(new PersPositionItem(i)))
            .filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public PersPositionItem getItemByCode(String code) {
        return Optional.ofNullable(code).filter(StringUtils::isNotBlank).map(persPositionDao::findByCode)
            .map(persPosition -> ModelUtil.copyProperties(persPosition, new PersPositionItem())).orElse(null);
    }

    @Override
    public List<PersPositionItem> getItemByIds(String positionIds) {
        List<PersPositionItem> list = new ArrayList<PersPositionItem>();
        List<String> ids = (List<String>)CollectionUtil.strToList(positionIds);
        if (ids != null && ids.size() == 0) {
            return list;
        }

        List<PersPosition> persPositionList = persPositionDao.findByIdList(ids);

        persPositionList.forEach(item -> {
            PersPositionItem persPositionItem = new PersPositionItem();
            persPositionItem.setId(item.getId());
            persPositionItem.setCode(item.getCode());
            persPositionItem.setName(item.getName());
            list.add(persPositionItem);
        });
        return list;
    }

    @Override
    public Boolean isPositionNameExist(String id, String parentId, String name) {
        Boolean isNameExist = true;
        Integer count = 0;
        if (StringUtils.isNotBlank(id)) {
            if (StringUtils.isNotBlank(parentId)) {
                count = count + this.persPositionDao.countByNameAndAndParent_IdAndAndIdNot(name, parentId, id);
            } else {
                count = count + this.persPositionDao.countByNameAndParent_IdIsNullAndIdNot(name, id);
            }
        } else if (StringUtils.isNotBlank(parentId)) {
            count = count + this.persPositionDao.countByNameAndParent_Id(name, parentId);
        } else {
            count = count + this.persPositionDao.countByNameAndParent_IdIsNull(name);
        }

        if (count > 0) {
            isNameExist = false;
        }

        return isNameExist;
    }

    @Override
    public String getPositionIdsByParentId(String parentId) {
        List<PersPositionItem> persPositions = new ArrayList<>();
        PersPosition persPosition = persPositionDao.findById(parentId).orElse(null);
        if (persPosition != null) {
            persPositions.add(ModelUtil.copyPropertiesIgnoreNull(persPosition, new PersPositionItem()));
            addChildList(Arrays.asList(persPosition.getId()), persPositions);
        }
        return CollectionUtil.getItemIds(persPositions);
    }

    /**
     * 递归加载子节点
     *
     * @param parentIdList
     * @param persPositionItems
     */
    private void addChildList(List<String> parentIdList, List<PersPositionItem> persPositionItems) {
        List<List<String>> split = CollectionUtil.split(parentIdList, CollectionUtil.splitSize);
        split.forEach(parentIds -> {
            List<PersPosition> childs = persPositionDao.findByParent_IdIn(parentIds);
            if (!CollectionUtil.isEmpty(childs)) {
                List<String> idList = new ArrayList<>();
                childs.forEach(child -> {
                    persPositionItems.add(ModelUtil.copyPropertiesIgnoreNull(child, new PersPositionItem()));
                    idList.add(child.getId());
                });
                addChildList(idList, persPositionItems);
            }
        });
    }

    @Override
    public PersPositionItem initData(PersPositionItem item) {
        PersPosition persPosition = persPositionDao.findByName(item.getName());
        return persPosition == null ? this.saveItem(item) : null;
    }

    @Override
    public List<?> getPersPositionItemData(Class<?> cls, BaseItem condition, int beginIndex, int endIndex) {
        return persPositionDao.getItemsDataBySql(cls, SQLUtil.getSqlByItem(condition), beginIndex, endIndex, true);
    }

    @Override
    public void handlerTransfer(List<PersPositionItem> positionItems) {
        // 先将所有的数据保存后在设置树的关系，以防大数据分批处理
        Map<String, PersPosition> id2parentId = new HashMap<String, PersPosition>();
        List<List<PersPositionItem>> positionItemList = CollectionUtil.split(positionItems, CollectionUtil.splitSize);
        for (List<PersPositionItem> items : positionItemList) {
            Collection<String> codes = CollectionUtil.getPropertyList(items, PersPositionItem::getCode, "-1");
            // 批量查出已有的数据
            List<PersPosition> persPositions = persPositionDao.findByCodeIn(codes);
            Map<String, PersPosition> persPositionMap =
                CollectionUtil.listToKeyMap(persPositions, PersPosition::getCode);
            for (PersPositionItem positionItem : items) {
                PersPosition persPosition = persPositionMap.remove(positionItem.getCode());
                if (Objects.isNull(persPosition)) {
                    persPosition = new PersPosition();
                }
                persPosition = ModelUtil.copyPropertiesIgnoreNullWithProperties(positionItem, persPosition, "id");
                persPositionDao.saveAndFlush(persPosition);
                id2parentId.put(positionItem.getId(), persPosition);
            }
        }
        // 设置职位的树的关系
        for (PersPositionItem item : positionItems) {
            if (item.getParentId() != null) {
                PersPosition persPosition = id2parentId.get(item.getId());
                persPosition.setParent(id2parentId.get(item.getParentId()));
                persPositionDao.saveAndFlush(persPosition);
            }
        }
        positionItemList = null;
        id2parentId = null;
    }

    @Override
    public void batchSaveItemFromCloud(List<PersPositionItem> persPositionItemList) {
        if (!CollectionUtil.isEmpty(persPositionItemList)) {
            List<String> codes =
                (List<String>)CollectionUtil.getPropertyList(persPositionItemList, PersPositionItem::getCode, "-1");
            List<List<String>> codeList = CollectionUtil.split(codes, CollectionUtil.splitSize);
            List<PersPosition> persPositions = new ArrayList<>();
            codeList.forEach(positionCodes -> {
                List<PersPosition> positions = persPositionDao.findByCodeIn(positionCodes);
                persPositions.addAll(positions);
            });
            Map<String, PersPosition> positionMap = new HashMap<>();
            positionMap.putAll(CollectionUtil.listToKeyMap(persPositions, PersPosition::getCode));
            for (PersPositionItem item : persPositionItemList) {
                PersPosition persPosition = positionMap.get(item.getCode());
                if (persPosition == null) {
                    persPosition = new PersPosition();
                    // 排序值时要默认一个大的排序值，放在最后
                    persPosition.setSortNo(99999);
                }
                // 判断云端是否存在父部门，有则查询出父部门ID并赋值。
                if (StringUtils.isNotBlank(item.getParentCode())) {
                    PersPosition parentPosition = positionMap.get(item.getParentCode());
                    if (parentPosition != null) {
                        persPosition.setParent(parentPosition);
                    } else {
                        persPosition.setParent(null);
                    }
                } else {
                    persPosition.setParent(null);
                }
                persPosition.setName(item.getName()).setCode(item.getCode());
                persPosition = persPositionDao.save(persPosition);
                // 更新map值
                positionMap.put(persPosition.getCode(), persPosition);
            }
        }
    }

    @Override
    public List<PersPositionItem> getItemsByCodes(Collection<String> codes) {
        PersPositionItem condition = new PersPositionItem();
        condition.setInCode(StringUtils.join(codes, ","));
        return getByCondition(condition);
    }

    @Override
    public void updatePersPositionItem(List<PersPositionItem> persPositionItems) {
        Collection<String> codes = CollectionUtil.getPropertyList(persPositionItems, PersPositionItem::getCode, "-1");
        List<PersPosition> exitPersPositions = persPositionDao.findByCodeIn(codes);
        Map<String, PersPosition> persPositionMap =
            CollectionUtil.listToKeyMap(exitPersPositions, PersPosition::getCode);
        PersPosition persPosition = new PersPosition();
        for (PersPositionItem item : persPositionItems) {
            if (StringUtils.isBlank(item.getCode())) {
                // 部门编号不能为空
                continue;
            }
            persPosition = persPositionMap.get(item.getCode());
            if (persPosition == null) {
                persPosition = new PersPosition();
            }
            // 判断云端是否存在父职位，有则查询出父职位ID并赋值。
            if (StringUtils.isNotBlank(item.getParentCode())) {
                PersPosition persPositionDept = persPositionDao.findByCode(item.getParentCode());
                if (persPositionDept != null) {
                    persPosition.setParent(persPositionDept);
                } else {
                    persPosition.setParent(null);
                }
            } else {
                persPosition.setParent(null);
            }
            persPosition.setName(item.getName()).setCode(item.getCode());
            if (Objects.isNull(persPosition.getSortNo())) {
                persPosition.setSortNo(99999);
            }
            persPositionDao.save(persPosition);
        }
    }

    private boolean equalsPosition(PersPosition newPosition, PersPosition oldPosition) {
        if ((StringUtils.isNotBlank(newPosition.getName()) && StringUtils.isBlank(oldPosition.getName()))
            || (StringUtils.isBlank(newPosition.getName()) && StringUtils.isNotBlank(oldPosition.getName()))
            || (StringUtils.isNotBlank(newPosition.getName()) && StringUtils.isNotBlank(oldPosition.getName())
                && !newPosition.getName().equals(oldPosition.getName()))) {
            return false;
        }
        if ((newPosition.getParent() != null && oldPosition.getParent() == null)
            || (newPosition.getParent() == null && oldPosition.getParent() != null)
            || (newPosition.getParent() != null && oldPosition.getParent() != null
                && !newPosition.getParent().getCode().equals(oldPosition.getParent().getCode()))) {
            return false;
        }
        return true;
    }

    public void sendPositionToCloud(PersPosition persPosition) {
        // 异步推送
        CompletableFuture.runAsync(() -> {
            PersApiPositiontItem persApiPositiontItem = new PersApiPositiontItem();
            persApiPositiontItem.setCode(persPosition.getCode());
            persApiPositiontItem.setName(persPosition.getName());
            if (persPosition.getParent() != null) {
                persApiPositiontItem.setParentCode(persPosition.getParent().getCode());
            }
            List<PersApiPositiontItem> persApiPositiontItemList = Collections.singletonList(persApiPositiontItem);
            List<SystemPers2ApiPositiontItem> pers2ApiPositiontItemList =
                ModelUtil.copyListProperties(persApiPositiontItemList, SystemPers2ApiPositiontItem.class);
            persCloudService.sendPersPositionToCloud(pers2ApiPositiontItemList);
        });
    }

    @Override
    public ZKResultMsg importExcel(List<PersPositionItem> itemList) {
        // 基础变量定义
        // 职位编号只支持数字和字母
        Pattern pattern = Pattern.compile("[0-9a-zA-Z]+");
        // 分批次待导入的职位编号集合
        List<String> importPositionCodeList = null;
        // 已经存在的职位集合，key为职位编号
        Map<String, PersPosition> existPositionMap = null;
        // 自身编号和上级职位编号的集合，map里面key为自身编号，value为上级职位编号
        List<Map<String, String>> selfParentCodeMapList = new ArrayList<>();
        // 自身编号和上级职位编号的集合，key为自身编号，value为上级职位编号
        Map<String, String> selfParentCodeMap = null;
        // 职位编号集合，用于过滤重复编号的数据
        List<String> tempCodeList = new ArrayList<>();
        // 编号重复的职位集合，key为编号，value为名称
        Map<String, String> codeRepeatMap = new HashMap<>();
        // 上级职位编号为自身的职位集合，key为编号，value为名称
        Map<String, String> parentCodeErrorMap = new HashMap<>();
        // 待入库的职位集合
        List<PersPosition> persPositionList = null;
        PersPosition position = null;
        String code = StringUtils.EMPTY;
        String name = StringUtils.EMPTY;
        String parentCode = StringUtils.EMPTY;

        // 自身职位名称和上级职位编号拼装的集合（职位名称_上级职位编号），用于判断同级别职位名称是否重复
        Set<String> positionNameParentCodeSet = new HashSet<>();
        List<Object[]> allNameAndParentCodeList = persPositionDao.getAllNameAndParentCode();
        if (!CollectionUtil.isEmpty(allNameAndParentCodeList)) {
            positionNameParentCodeSet.addAll(allNameAndParentCodeList.stream()
                .map((Object[] o) -> o[0] + "_" + (o.length > 1 && Objects.nonNull(o[1]) ? o[1] : ""))
                .collect(Collectors.toSet()));
        }

        // 已存在职位条数
        int existPositionSize = 0;
        int importSize = itemList.size();
        // 导入的合法职位数据
        List<List<PersPosition>> allPersPositionImportList = new ArrayList<>();
        int beginProgress = 20;
        // 导入数据是第几行行数
        int index = 2;
        // 分割导入的数据，每次处理800个，先保存所有职位，后面再处理职位层级关系
        List<List<PersPositionItem>> persPositionImportItemsList =
            CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<PersPositionItem> positionItemList : persPositionImportItemsList) {
            // 取出待导入的职位编号
            importPositionCodeList =
                (List<String>)CollectionUtil.getPropertyList(positionItemList, PersPositionItem::getCode, "-1");
            // 根据职位编号查出已经存在的职位
            existPositionMap = getPositionMapByCode(importPositionCodeList);
            existPositionSize = existPositionSize + existPositionMap.size();

            // 处理导入的数据
            persPositionList = new ArrayList<>();
            selfParentCodeMap = new HashMap<>();
            for (PersPositionItem importItem : positionItemList) {
                index++;
                code = importItem.getCode();
                name = importItem.getName();
                parentCode =
                    Objects.nonNull(importItem.getParentCode()) ? importItem.getParentCode() : StringUtils.EMPTY;

                // 编号是必填项，没有的则过滤
                if (StringUtils.isBlank(code)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("pers_position_codeNotEmpty"))));
                    continue;
                }
                // 名称不能空
                if (StringUtils.isBlank(name)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_fail", index, I18nUtil.i18nCode("pers_position_nameNotEmpty"))));
                    continue;
                } else {
                    // 移除换行\r、回车\n、制表\t符
                    name = name.replaceAll("[\t\n\r]", "");
                }
                // 过滤名称包含特殊字符
                if (StringUtils.isNotBlank(name) && ValidateUtils.isSpecialChar(name)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", index, I18nUtil.i18nCode("pers_position_nameNoSpecialChar", name))));
                    continue;
                }
                // 过滤编号包含特殊字符
                if (!pattern.matcher(code).matches()) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("pers_position_noSpecialChar", code))));
                    continue;
                }
                // 编码长度超出30位
                if (code.length() > 30) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("pers_position_codeLength", code))));
                    continue;
                }
                // 名称长度超过100的数据
                if (name.length() > 100) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", index, I18nUtil.i18nCode("pers_position_nameLength", name, "100"))));
                    continue;
                }
                // 过滤编号重复的数据
                if (tempCodeList.contains(code)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("pers_position_codeExist", code))));
                    codeRepeatMap.put(code, name);
                    continue;
                }
                // 同级别职位名称重复过滤
                if (!existPositionMap.containsKey(code) && !positionNameParentCodeSet.add(name + "_" + parentCode)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("pers_position_nameExist", name))));
                    parentCodeErrorMap.put(code, name);
                    continue;
                }
                // 判断上级职位编号是否为自身
                if (StringUtils.isNotBlank(parentCode) && code.equals(parentCode)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                        .i18nCode("pers_import_fail", index, I18nUtil.i18nCode("pers_position_parentMenuMsg", code))));
                    parentCodeErrorMap.put(code, name);
                    continue;
                }

                tempCodeList.add(code);

                position = existPositionMap.get(code);
                if (position == null) {
                    position = new PersPosition();
                    position.setCode(code);
                    position.setSortNo(99999);
                }
                position.setName(name);
                // 去除上级职位
                position.setParent(null);

                // 保存职位层级关系
                if (StringUtils.isNotBlank(parentCode)) {
                    selfParentCodeMap.put(code, parentCode);
                }
                persPositionList.add(position);
            }

            // 保存这批次的职位层级关系，方便后面分批处理
            if (!selfParentCodeMap.isEmpty()) {
                selfParentCodeMapList.add(selfParentCodeMap);
            }

            // 保存这批次的职位
            if (persPositionList.size() > 0) {
                allPersPositionImportList.add(persPositionList);
            }
        }
        // 执行入库，保存职位
        int saveSize = 0;
        float avgProgress = (float)60 / allPersPositionImportList.size();
        int i = 0;
        progressCache.setProcess(
            ProcessBeanUtil.createNormalSingleProcess(beginProgress + 70, I18nUtil.i18nCode("common_op_processing")));
        for (List<PersPosition> positionImportList : allPersPositionImportList) {
            int progress = beginProgress + 10 + (int)(avgProgress * i++);
            progressCache.setProcess(new ProcessBean(progress, progress, ""));
            saveSize += positionImportList.size();
            persPositionDao.save(positionImportList);
        }

        // 处理职位层级关系
        if (!selfParentCodeMapList.isEmpty()) {
            // 职位自身的编号集合。
            List<String> selfCodeList = null;
            // 上级职位编号集合。
            List<String> parentCodeList = null;
            // 根据职位自身编号查出来的职位集合，key为职位编号
            Map<String, PersPosition> selfCodePositionMap = null;
            // 根据上级职位编号查出来的职位集合，key为职位编号
            Map<String, PersPosition> parentCodePositiontMap = null;

            for (Map<String, String> selfParentMap : selfParentCodeMapList) {

                selfCodeList = new ArrayList<>(selfParentMap.keySet());
                parentCodeList = new ArrayList<>(selfParentMap.values());
                selfCodePositionMap = getPositionMapByCode(selfCodeList);
                parentCodePositiontMap = getPositionMapByCode(parentCodeList);
                // 待重新入库保存的职位集合
                persPositionList = new ArrayList<>();
                // 解析并设置职位的上级职位
                for (Map.Entry<String, String> deptEntry : selfParentMap.entrySet()) {
                    position = selfCodePositionMap.get(deptEntry.getKey());
                    position.setParent(parentCodePositiontMap.get(deptEntry.getValue()));
                    persPositionList.add(position);
                }
                // 重新入库
                persPositionDao.save(persPositionList);
            }
        }
        // 失败数量
        int faildCount = importSize - saveSize;
        if (existPositionSize > 0) {
            // 成功：%s 条，更新：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result3", saveSize, existPositionSize, faildCount)));
        } else if (faildCount > 0 || saveSize > 0) {
            // 成功：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result", saveSize, faildCount)));
        }
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    private Map<String, PersPosition> getPositionMapByCode(List<String> positionCodeList) {
        Map<String, PersPosition> persPositionMap = new HashMap<>();
        List<PersPosition> persPositionList = persPositionDao.findByCodeIn(positionCodeList);
        if (persPositionList.size() > 0) {
            persPositionMap = CollectionUtil.listToKeyMap(persPositionList, PersPosition::getCode);
        }
        return persPositionMap;
    }

    @Override
    public Boolean isChild(List<String> ids, String parentId) {
        List<PersPosition> childList = persPositionDao.findByParent_IdIn(ids);
        List<List<String>> propertyList =
            CollectionUtil.getPropertyList(childList, PersPosition::getId, CollectionUtil.splitSize);
        for (List<String> parentIds : propertyList) {
            if (parentIds.contains(parentId)) {
                return true;
            }
            if (isChild(parentIds, parentId)) {
                return true;
            }
        }
        return false;
    }
}