package com.zkteco.zkbiosecurity.pers.service.impl;

import com.zkteco.zkbiosecurity.pers.cache.PersPersonCacheManager;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.auth.service.AuthDepartment4OtherService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;

import java.util.List;

/**
 * 部门更新回调
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/3/12 9:48
 * @since 1.0.0
 */
@Slf4j
@Service
public class PersDepartmentExtServiceImpl implements AuthDepartment4OtherService {

    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersPersonCacheManager persPersonCacheManager;

    /**
     * 更新人事缓存部门名称
     *
     * @param item:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/8/22 10:20
     * @since 1.0.0
     */
    @Override
    public void editAuthDepartmentInfo(AuthDepartmentItem item) {
        try {
            if (StringUtils.isNotBlank(item.getId()) && StringUtils.isNotBlank(item.getName())) {
                List<String> persPersonList = persPersonDao.findPinsByDeptId(item.getId());
                if (persPersonList != null && persPersonList.size() > 0) {
                    List<PersPersonCacheItem> persPersonCacheItemList = persPersonCacheManager.getPersonCacheByPins(persPersonList);
                    if (persPersonCacheItemList != null && persPersonCacheItemList.size() > 0) {
                        for (PersPersonCacheItem persPersonCacheItem : persPersonCacheItemList) {
                            persPersonCacheItem.setDeptName(item.getName());
                            persPersonCacheManager.updatePersonCacheItem(persPersonCacheItem);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("editAuthDepartmentInfo error", e);
        }
    }
}
