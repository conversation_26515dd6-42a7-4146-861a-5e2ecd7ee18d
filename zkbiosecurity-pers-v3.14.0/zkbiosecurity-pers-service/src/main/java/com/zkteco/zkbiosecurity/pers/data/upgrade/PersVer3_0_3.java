package com.zkteco.zkbiosecurity.pers.data.upgrade;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.utils.PersPersonUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

@Component
public class PersVer3_0_3 implements UpgradeVersionManager {
    @Autowired
    private PersPersonDao persPersonDao;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v3.0.3";
    }

    @Override
    public boolean executeUpgrade() {
        long personCount = persPersonDao.countPerson();
        if (personCount > 0) {
            int splitSize = 1000;
            int pageCount = (int)(personCount / splitSize);
            if (personCount % splitSize > 0) {
                pageCount++;
            }
            for (int i = 0; i < pageCount; i++) {
                Pageable pageable = PageRequest.of(i, splitSize);
                List<PersPerson> personList = persPersonDao.getPersonByPage(pageable);
                if (personList != null && personList.size() > 0) {
                    for (PersPerson p : personList) {
                        p.setNumberPin(PersPersonUtil.createNumberPin(p.getPin()));
                    }
                    persPersonDao.saveAll(personList);
                }
            }
        }
        return true;
    }
}
