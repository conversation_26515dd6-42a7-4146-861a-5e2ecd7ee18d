package com.zkteco.zkbiosecurity.pers.service.impl;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.pers.enums.PersSupportFuncEnum;
import com.zkteco.zkbiosecurity.pers.service.PersSupportFuncService;

/**
 * 人事功能判断实现
 *
 * <AUTHOR>
 * @date 2021-02-25 14:22
 * @since 1.0.0
 */
@Service
public class PersSupportFuncServiceImpl implements PersSupportFuncService {

    /** 产品编码 */
    @Value("${system.productCode}")
    private String productCode;

    @Override
    public boolean isSupportPrintTemplate() {
        return PersSupportFuncEnum.PRINT_TEMPLATE.isSupport(productCode);
    }

    @Override
    public boolean isSupportPersonLeave() {
        return PersSupportFuncEnum.PERSON_LEAVE.isSupport(productCode);
    }

    @Override
    public boolean isSupportPosition() {
        return PersSupportFuncEnum.POSITION.isSupport(productCode);
    }

    @Override
    public boolean isSupportLossCard() {
        return PersSupportFuncEnum.LOSS_CARD.isSupport(productCode);
    }

    @Override
    public boolean isSupportWGFmtTest() {
        return PersSupportFuncEnum.WG_FMT_TEST.isSupport(productCode);
    }

    @Override
    public boolean isSupportResetSelfPwd() {
        return PersSupportFuncEnum.RESET_SELF_PWD.isSupport(productCode);
    }
}
