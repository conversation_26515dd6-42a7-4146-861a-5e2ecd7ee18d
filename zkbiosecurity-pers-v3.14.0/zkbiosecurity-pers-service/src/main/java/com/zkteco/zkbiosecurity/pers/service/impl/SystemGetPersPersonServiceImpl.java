package com.zkteco.zkbiosecurity.pers.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.core.utils.MD5Util;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeExtItem;
import com.zkteco.zkbiosecurity.pers.vo.PersCertificateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.system.service.SystemGetPersPersonService;
import com.zkteco.zkbiosecurity.system.vo.SystemGetPersPersonItem;

/**
 * <AUTHOR>
 * @DATE 2020-08-04 12:00
 * @since 1.0.0
 */
@Service
public class SystemGetPersPersonServiceImpl implements SystemGetPersPersonService {
    @Autowired
    private PersPersonService persPersonService;

    @Override
    public SystemGetPersPersonItem getPersPersonByPin(String pin) {
        PersPersonItem personItem = persPersonService.getItemByPin(pin);
        if (personItem != null) {
            SystemGetPersPersonItem item = ModelUtil.copyProperties(personItem, new SystemGetPersPersonItem());
            return item;
        }
        return null;
    }

    @Override
    public void editAppPerson(SystemGetPersPersonItem systemGetPersPersonItem) {
        PersPersonItem personItem = persPersonService.getItemByPin(systemGetPersPersonItem.getPin());
        if (personItem != null) {
            personItem.setName(systemGetPersPersonItem.getName());
            if (StringUtils.isNotBlank(systemGetPersPersonItem.getPassword())) {
                personItem.setSelfPwd(MD5Util.entype(systemGetPersPersonItem.getPassword()));
            }
            personItem.setEmail(systemGetPersPersonItem.getEmail());
        }
        persPersonService.saveItem(personItem, new PersCertificateItem(), new PersAttributeExtItem(),
            Maps.newHashMap());
    }
}
