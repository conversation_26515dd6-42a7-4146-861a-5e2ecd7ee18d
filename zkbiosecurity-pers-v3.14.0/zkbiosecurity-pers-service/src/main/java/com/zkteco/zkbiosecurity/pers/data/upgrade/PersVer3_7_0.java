package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 新增参数个人敏感信息保护
 *
 * <AUTHOR>
 * @date 2022-10-20 15:14
 * @since 1.0.0
 */
@Slf4j
@Component
public class PersVer3_7_0 implements UpgradeVersionManager {

    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v3.7.0";
    }

    @Override
    public boolean executeUpgrade() {
        baseSysParamService.initData(new BaseSysParamItem("pers.name.encryptProp", "true", "姓名加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.name.encryptMode", "S1", "姓名加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.lastName.encryptProp", "true", "姓氏加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.lastName.encryptMode", "S1", "姓氏加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.mobilePhone.encryptProp", "true", "手机号加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.mobilePhone.encryptMode", "S2", "手机号加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.gender.encryptProp", "true", "性别加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.gender.encryptMode", "S0F1", "性别加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.certNumber.encryptProp", "true", "证件号加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.certNumber.encryptMode", "S2", "证件号加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.birthday.encryptProp", "true", "出生日期加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.birthday.encryptMode", "S2", "出生日期加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.email.encryptProp", "true", "邮箱加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.email.encryptMode", "S2", "邮箱加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.carPlate.encryptProp", "true", "车牌号码加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.carPlate.encryptMode", "S2", "车牌号码加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.pin.encryptProp", "true", "编号加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.pin.encryptMode", "S2", "编号加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.cardNo.encryptProp", "true", "卡号加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.cardNo.encryptMode", "S2", "卡号加密模式"));
        baseSysParamService.initData(new BaseSysParamItem("pers.headPortrait.encryptProp", "true", "头像加密显示"));
        baseSysParamService.initData(new BaseSysParamItem("pers.bioPhoto.encryptProp", "true", "比对照片加密显示"));
        return true;
    }

}
