package com.zkteco.zkbiosecurity.pers.data.upgrade;

import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

@Component
public class PersVer2_9_0 implements UpgradeVersionManager {
    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;
    @Autowired
    private AuthPermissionService authPermissionService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v2.9.0";
    }

    @Override
    public boolean executeUpgrade() {
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("PersPerson");
        if (Objects.nonNull(subMenuItem)) {
            /** --------------------------- 删除人员模版 --------------------------- */
            AuthPermissionItem subButtonItem =
                new AuthPermissionItem("PersPersonDelBioTemplate", "pers_person_delBioTemplate",
                    "pers:person:delBioTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 17);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

            /** --------------------------- 导入离职人员 --------------------------- */
            subButtonItem = new AuthPermissionItem("PersPersonImportLeavePerson", "pers_dimission_import",
                "pers:person:importLeavePerson", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 18);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

            /** --------------------------- 下载离职人员导入模板 --------------------------- */
            subButtonItem =
                new AuthPermissionItem("PersPersonExportLeavePersonTemplate", "pers_dimission_downloadTemplate",
                    "pers:person:exportLeavePersonTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 19);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }

        subMenuItem = authPermissionService.getItemByCode("PersPosition");
        if (Objects.nonNull(subMenuItem)) {
            AuthPermissionItem subButtonItem = new AuthPermissionItem("PersPositionExport", "common_op_export",
                "pers:position:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersPositionImport", "common_op_import", "pers:position:import",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersPositionExportTemplate", "pers_position_downloadTemplate",
                "pers:position:exportTemplate", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 8);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }

        jdbcOperateTemplate.alterTableColumnCharToTexts("PERS_BIOTEMPLATE", "TEMPLATE");
        return true;
    }
}
