/**
 * File Name: PersPersonChangeServiceImpl
 * Created by GenerationTools on 2018-03-14 上午10:10
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonChangeDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersPersonChange;
import com.zkteco.zkbiosecurity.pers.service.PersPersonChangeService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonChangeItem;

/**
 * 对应百傲瑞达 PersPersonChangeServiceImpl
 * <AUTHOR>
 * @date:	2018-03-14 上午10:10
 * @version v1.0
 */
@Service
@Transactional
public class PersPersonChangeServiceImpl implements PersPersonChangeService {
	@Autowired
	private PersPersonChangeDao persPersonChangeDao;
	@Autowired
	private PersPersonDao persPersonDao;

	@Override
	public PersPersonChangeItem saveItem(PersPersonChangeItem item){
		PersPersonChange persPersonChange = Optional.ofNullable(item)
						.map(i->i.getId())
						.filter(StringUtils::isNotBlank)
						.flatMap(id->persPersonChangeDao.findById(id))
						.orElse(new PersPersonChange());
		ModelUtil.copyProperties(item, persPersonChange);
		//保存人员
		if (StringUtils.isNotBlank(item.getPersonId())) {
			persPersonChange.setPersPerson(persPersonDao.findById(item.getPersonId()).get());
		}
		persPersonChangeDao.save(persPersonChange);
		item.setId(persPersonChange.getId());
		return item;
	}

	@Override
	public List<PersPersonChangeItem> getByCondition(PersPersonChangeItem condition){
		return (List<PersPersonChangeItem>) persPersonChangeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
	}

	@Override
	public Pager getItemsByPage(BaseItem condition, int page, int size) {
		return persPersonChangeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
	}

	@Override
	public boolean deleteByIds(String ids) {
		if (StringUtils.isNotEmpty(ids)) {
			String[] idArray = StringUtils.split(ids, ",");
			for (String id : idArray) {
				persPersonChangeDao.deleteById(id);
			}
		}
		return false;
	}

	@Override
	public PersPersonChangeItem getItemById(String id) {
		List<PersPersonChangeItem> items = getByCondition(new PersPersonChangeItem(id));
		return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
	}

	@Override
	public void deleteByPersonId(String personId) {
		persPersonChangeDao.deleteByPersPersonId(personId);
	}
}