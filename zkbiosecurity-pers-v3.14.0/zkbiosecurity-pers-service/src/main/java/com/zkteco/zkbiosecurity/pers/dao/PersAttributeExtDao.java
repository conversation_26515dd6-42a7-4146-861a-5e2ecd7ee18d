/**
 * File Name: PersAttributeExt
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import com.zkteco.zkbiosecurity.pers.model.PersAttributeExt;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * 对应百傲瑞达 PersAttributeExtDao
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersAttributeExtDao extends BaseDao<PersAttributeExt, String> {

    /**
     * 根据人员ID删除
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/8/13 14:06
     * @param personId
     * @return void
     */
    void deleteByPersonId(String personId);

    /**
     * 根据人员ID查询
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/8/13 14:00
     * @param personId
     * @return com.zkteco.zkbiosecurity.pers.model.PersAttributeExt
     */
    PersAttributeExt findByPersonId(String personId);

    /**
     * 根据人员IDList查询
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/8/13 14:00
     * @param personIds
     * @return com.zkteco.zkbiosecurity.pers.model.PersAttributeExt
     */
    List<PersAttributeExt> findByPersonIdIn(List<String> personIds);
}