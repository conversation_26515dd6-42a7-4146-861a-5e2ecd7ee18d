package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

/**
 * <AUTHOR>
 * @date 2021/4/1 16:46
 * @since 1.0.0
 */
@Component
public class PersVer3_0_0 implements UpgradeVersionManager {
    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private PersCertificateService persCertificateService;
    @Autowired
    private PersIdentityCardInfoService persIdentityCardInfoService;
    @Autowired
    private PersIssueCardService persIssueCardService;
    @Autowired
    private PersPersonService persPersonService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v3.0.0";
    }

    @Override
    public boolean executeUpgrade() {
        // 修改字段长度
        jdbcOperateTemplate.alterTableCharLen("PERS_CARD", "CARD_NO", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_CERTIFICATE", "CERT_NUMBER", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_ISSUECARD", "CARD_NO", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_PERSON", "MOBILE_PHONE", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_PERSON", "EMAIL", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_PERSON", "PERSON_PWD", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_PERSON", "SELF_PWD", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_PERSON", "ID_CARD", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_PERSON", "ID_CARD_PHYSICAL_NO", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_TEMP_PERSON", "MOBILE_PHONE", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_TEMP_PERSON", "DEVICE_PWD", "250");
        jdbcOperateTemplate.alterTableCharLen("PERS_TEMP_PERSON", "PERSON_PWD", "250");

        // 字段加密
        persCardService.encryptPersCard();
        persCertificateService.encryptPersCertificate();
        persIdentityCardInfoService.encryptPersIdentityCardInfo();
        persIssueCardService.encryptPersIssueCard();
        persPersonService.encryptPersPerson();
        return true;
    }
}
