/**
 * File Name: PersWiegandFmt Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import java.util.List;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersWiegandFmt;

/**
 * 对应百傲瑞达 PersWiegandFmtDao
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersWiegandFmtDao extends BaseDao<PersWiegandFmt, String> {
    /**
     * 根据名称查询韦根
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 21:21
     * @param name
     * @return com.zkteco.zkbiosecurity.pers.model.PersWiegandFmt
     */
    PersWiegandFmt findByName(String name);

    /**
     * 根据韦根模式查询
     * 
     * @param wiegandMode
     * @return
     */
    PersWiegandFmt findByWiegandMode(short wiegandMode);

    /**
     * 根据名称查询是否存着
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 21:21
     * @param name
     * @return boolean
     */
    boolean existsByName(String name);

    /**
     * 根据是否自动匹配获取韦根格式
     * 
     * @param defaultFmt
     * @return
     */
    List<PersWiegandFmt> findByIsDefaultFmt(boolean defaultFmt);

    /**
     * 根据韦根位数获取韦根格式
     * 
     * @param wiegandCount
     * @return
     */
    List<PersWiegandFmt> findByWiegandCount(short wiegandCount);

    /**
     * 统计自动匹配韦根格式的数量
     * 
     * @param defaultFmt
     * @return
     */
    long countByIsDefaultFmt(boolean defaultFmt);
}