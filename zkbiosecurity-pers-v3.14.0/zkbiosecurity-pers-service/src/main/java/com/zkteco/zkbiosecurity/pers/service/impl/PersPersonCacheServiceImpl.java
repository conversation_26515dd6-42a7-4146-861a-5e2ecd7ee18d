package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.pers.cache.PersPersonCacheManager;
import com.zkteco.zkbiosecurity.pers.service.PersPersonCacheService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;

/**
 * 人员信息缓存类
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/9/14 13:35
 * @since 1.0.0
 */
@Service
@Transactional
public class PersPersonCacheServiceImpl implements PersPersonCacheService {

    @Autowired
    private PersPersonCacheManager persPersonCacheManager;

    @Override
    public PersPersonCacheItem getPersonCacheByPin(String pin) {
        return persPersonCacheManager.getPersonCacheByPin(pin);
    }

    @Override
    public PersPersonCacheItem getPersonCacheFilterLeaveByPin(String pin) {
        return persPersonCacheManager.getPersonCacheFilterLeaveByPin(pin);
    }

    @Override
    public List<PersPersonCacheItem> getPersonCacheFilterLeaveByPins(List<String> pinList) {
        return persPersonCacheManager.getPersonCacheFilterLeaveByPins(pinList);
    }

    @Override
    public List<PersPersonCacheItem> getPersonCacheByPins(List<String> pinList) {
        return persPersonCacheManager.getPersonCacheByPins(pinList);
    }

    @Override
    public void updatePersonCacheExtParamsMap(String module, Map<String, Map<String, Object>> pinExtParamsMap) {
        persPersonCacheManager.updatePersonCacheExtParamsMap(module, pinExtParamsMap);
    }
}
