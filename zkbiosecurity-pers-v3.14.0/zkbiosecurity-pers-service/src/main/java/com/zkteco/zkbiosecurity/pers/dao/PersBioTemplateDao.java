/**
 * File Name: PersBioTemplate Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersBioTemplate;

/**
 * 对应百傲瑞达 PersBioTemplateDao
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
public interface PersBioTemplateDao extends BaseDao<PersBioTemplate, String> {
    /**
     * 根据人员ID和生物模板查询
     *
     * @param personId
     * @param bioType
     * @return
     */
    List<PersBioTemplate> findByPersonIdAndBioType(String personId, Short bioType);

    /**
     * 用于指纹数据不对，更新过来
     *
     * @param bioType
     * @param defTemplateNo
     * @return
     */
    List<PersBioTemplate> findByBioTypeAndTemplateNoNot(Short bioType, Short defTemplateNo);

    /**
     * 用于人脸数据不对，更新过来
     *
     * @param bioType
     * @param defTemplateNoIndex
     * @return
     */
    List<PersBioTemplate> findByBioTypeAndTemplateNoIndexNot(Short bioType, Short defTemplateNoIndex);

    /**
     * 用于pin之前没有字段，添加字段值更新过来
     *
     * @return
     */
    List<PersBioTemplate> findByPersonPinIsNull();

    /**
     * 根据人员ID
     *
     * @param personId
     * @return
     */
    List<PersBioTemplate> findByPersonId(String personId);

    /**
     * 根据人员ID
     *
     * @param personIds
     * @return
     */
    List<PersBioTemplate> findByPersonIdIn(Collection<String> personIds);

    /**
     * 根据人员编号
     *
     * @param personPins
     * @return
     */
    List<PersBioTemplate> findByPersonPinIn(Collection<String> personPins);

    /**
     * 统计所有的人员生物模板数量
     *
     * @return Object[] 下标 0:为 bioType 1：为 count
     */
    @Query(
        value = "SELECT p.BIO_TYPE,count(p.PERSON_ID) FROM (SELECT t.BIO_TYPE,t.PERSON_ID FROM PERS_BIOTEMPLATE t WHERE t.TEMPLATE_NO_INDEX=?1 GROUP BY t.BIO_TYPE,t.TEMPLATE_NO,t.VERSION,t.PERSON_ID) p GROUP BY p.BIO_TYPE",
        nativeQuery = true)
    List<Object[]> countByTemplateNoIndexAndGroupByBioType(Short defNoIndex);

    /**
     * 统计所有的人员生物模板数量
     *
     * @return Object[] 下标 0:为 bioType 1：为 count
     */
    @Query(
        value = "SELECT p.BIO_TYPE,count(p.PERSON_ID),p.VERSION FROM (SELECT t.BIO_TYPE,t.PERSON_ID,t.version FROM PERS_BIOTEMPLATE t WHERE t.TEMPLATE_NO_INDEX=?1 GROUP BY t.BIO_TYPE,t.TEMPLATE_NO,t.VERSION,t.PERSON_ID) p GROUP BY p.BIO_TYPE,p.version",
        nativeQuery = true)
    List<Object[]> countByTemplateNoIndexAndGroupByBioTypeAndVersion(Short defNoIndex);

    @Query(
        value = "SELECT p.BIO_TYPE,count(p.PERSON_ID),p.VERSION FROM (SELECT t.BIO_TYPE,t.PERSON_ID,t.version FROM PERS_BIOTEMPLATE t LEFT JOIN PERS_PERSON pp ON t.PERSON_ID=pp.ID "
            + "WHERE t.TEMPLATE_NO_INDEX=?1 AND pp.auth_dept_id in (?2) GROUP BY t.BIO_TYPE,t.TEMPLATE_NO,t.VERSION,t.PERSON_ID) p GROUP BY p.BIO_TYPE,p.version",
        nativeQuery = true)
    List<Object[]> countByTemplateNoIndexAndGroupByBioTypeAndVersionAndDeptIds(Short defNoIndex,
        List<String> deptIdList);

    @Query(
        value = "SELECT p.BIO_TYPE,count(p.PERSON_ID),p.VERSION FROM (SELECT t.BIO_TYPE,t.PERSON_ID,t.version FROM PERS_BIOTEMPLATE t LEFT JOIN PERS_PERSON pp ON t.PERSON_ID=pp.ID "
            + "WHERE t.TEMPLATE_NO_INDEX=?1 AND pp.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=?2) GROUP BY t.BIO_TYPE,t.TEMPLATE_NO,t.VERSION,t.PERSON_ID) p GROUP BY p.BIO_TYPE,p.version",
        nativeQuery = true)
    List<Object[]> countByTemplateNoIndexAndGroupByBioTypeAndVersionAndUserId(Short defNoIndex, String userId);

    /**
     * 根据人员ids查询所有的生物模版数量
     *
     * @return Object[] 下标 0:为 bioType 1：为 count
     */
    @Query(
        value = "SELECT p.BIO_TYPE,count(p.PERSON_ID) FROM (SELECT t.BIO_TYPE,t.PERSON_ID FROM PERS_BIOTEMPLATE t WHERE t.PERSON_ID IN (?1) AND t.TEMPLATE_NO_INDEX=?2 GROUP BY t.BIO_TYPE,t.TEMPLATE_NO,t.VERSION,t.PERSON_ID) p GROUP BY p.BIO_TYPE",
        nativeQuery = true)
    List<Object[]> countByTemplateNoAndPersonIdsAndGroupByBioType(Collection<String> personIds, Short defNoIndex);

    /**
     * 根据人员ids查询所有的生物模版数量
     *
     * @return Object[] 下标 0:为 bioType 1：为 count 2:版本 3:人员id
     */
    @Query(
        value = "SELECT p.BIO_TYPE,count(p.PERSON_ID), p.VERSION,p.PERSON_ID FROM (SELECT t.BIO_TYPE,t.PERSON_ID,t.version FROM PERS_BIOTEMPLATE t WHERE t.PERSON_ID IN (?1) AND t.TEMPLATE_NO_INDEX=?2 GROUP BY t.BIO_TYPE,t.TEMPLATE_NO,t.VERSION,t.PERSON_ID) p GROUP BY p.BIO_TYPE,p.version,p.PERSON_ID",
        nativeQuery = true)
    List<Object[]> countByTemplateNoAndPersonIdsAndGroupByBioTypeAndVersion(Collection<String> personIds,
        Short defNoIndex);

    /**
     * 根据人员ids查询各自人员的生物模版数量
     *
     * @return Object[] 下标 0:为 personId 1：为 bioType 2：为 count
     */
    @Query(
        value = "SELECT p.PERSON_ID,p.BIO_TYPE,count(p.PERSON_ID) FROM (SELECT t.BIO_TYPE,t.PERSON_ID FROM PERS_BIOTEMPLATE t WHERE t.PERSON_ID IN (?1) AND t.TEMPLATE_NO_INDEX=?2 GROUP BY t.BIO_TYPE,t.TEMPLATE_NO,t.VERSION,t.PERSON_ID) p GROUP BY p.BIO_TYPE,p.PERSON_ID",
        nativeQuery = true)
    List<Object[]> countByBioTypeAndTemplateNoAndPersonIdIn(Collection<String> personIds, Short defNoIndex);

    /**
     * 统计各生物模板的人员数
     *
     * @return Object[] 下标 0:为 bioType 1：为 count
     */
    @Query(
        value = "SELECT p.BIO_TYPE, count(DISTINCT p.PERSON_ID) FROM PERS_BIOTEMPLATE p WHERE p.TEMPLATE_NO_INDEX = ?1 GROUP BY p.BIO_TYPE",
        nativeQuery = true)
    List<Object[]> countGroupByBioType(Short defNoIndex);

    // 根据人员pin和生物特征模板编号查找人员模板信息
    List<PersBioTemplate> findByPersonPinAndTemplateNo(String pin, Short templateNo);

    // 根据人员pin和生物特征模板编号查找人员模板信息
    List<PersBioTemplate> findByPersonPinAndTemplateNoIn(String pin, List<Short> tempTemplateNos);

    @Modifying
    @Query("delete from PersBioTemplate t where t.personPin =?1")
    void deleteByPersonPin(String pin);

    @Modifying
    @Query("delete from PersBioTemplate t where t.personPin =?1 and t.bioType=?2")
    void deleteByPersonPinAndBioType(String pin, Short bioType);

    @Modifying
    @Query("delete from PersBioTemplate t where t.personPin in (?1) and t.bioType in (?2)")
    void deleteByPersonPinsAndBioTypes(List<String> personPins, List<Short> bioTypes);

    @Query(
        value = "SELECT p.BIO_TYPE, p.VERSION, count(DISTINCT p.PERSON_ID) FROM PERS_BIOTEMPLATE p WHERE p.TEMPLATE_NO_INDEX = ?1 GROUP BY p.BIO_TYPE,p.VERSION",
        nativeQuery = true)
    List<Object[]> countGroupByBioTypeAndVersion(Short defNoIndex);

    List<PersBioTemplate> findByPersonPinAndTemplateNoAndBioType(String pin, Short templateNo, Short fpBioType);

    @Modifying
    @Query("delete from PersBioTemplate t where t.personPin in (?1) and t.bioType=?2")
    void deleteByPersonPinsAndBioType(List<String> pins, Short bioType);
}