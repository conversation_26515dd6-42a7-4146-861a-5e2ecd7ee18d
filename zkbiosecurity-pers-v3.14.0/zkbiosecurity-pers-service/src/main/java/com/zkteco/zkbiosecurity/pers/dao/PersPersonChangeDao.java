/**
 * File Name: PersPersonChange
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import com.zkteco.zkbiosecurity.pers.model.PersPersonChange;

/**
 * 对应百傲瑞达 PersPersonChangeDao
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersPersonChangeDao extends BaseDao<PersPersonChange, String> {
	/**
	 * 根据人员ID进行删除
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/12/6 21:18
	 * @param personId
	 * @return void
	 */
	void deleteByPersPersonId(String personId);
}