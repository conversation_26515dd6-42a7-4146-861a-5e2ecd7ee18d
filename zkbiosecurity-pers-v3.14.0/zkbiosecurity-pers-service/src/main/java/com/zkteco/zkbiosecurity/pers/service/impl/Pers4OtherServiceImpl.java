package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.dao.*;
import com.zkteco.zkbiosecurity.pers.service.Pers4OtherService;

@Service
public class Pers4OtherServiceImpl implements Pers4OtherService {

    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersCardDao persCardDao;
    @Autowired
    private PersBioPhotoDao persBioPhotoDao;
    @Autowired
    private PersPositionDao persPositionDao;
    @Autowired
    private PersBioTemplateDao persBioTemplateDao;

    @Override
    public List getPersonDatasByItem(BaseItem condition) {
        return persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public List getPerCardByItem(BaseItem condition) {
        return persCardDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public List getPerBioPhotoByItem(BaseItem condition) {
        return persBioPhotoDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public List getPersPositionByItem(BaseItem condition) {
        return persPositionDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public List getPersBioTemplateByItem(BaseItem condition) {
        return persBioTemplateDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }
}
