package com.zkteco.zkbiosecurity.pers.aspect;

import com.zkteco.zkbiosecurity.pers.cache.PersPersonCacheManager;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 人员信息保存切面，保存到redis
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/9/14 12:00
 * @since 1.0.0
 */
@Aspect
@Component
public class PersPersonSaveAspect {

    @Autowired
    private PersPersonCacheManager persInfoCacheManager;

    @Pointcut("execution(* com.zkteco.zkbiosecurity.pers.dao.PersPersonDao.save(..)) ")
    public void persSaveAfter() {}

    @After("persSaveAfter() && args(entity)")
    public void persSaveAfter(JoinPoint point, Object entity) {
        // 更新人员信息到缓存
        persInfoCacheManager.updatePersonCache(entity);
    }

}
