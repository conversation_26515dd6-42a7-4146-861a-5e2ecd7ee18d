/**
 * File Name: PersCertificateServiceImpl Created by GenerationTools on 2018-03-14 上午10:10 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersCertificateDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersCertificate;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.service.PersCertificateService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersCertificateItem;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryValueItem;

/**
 * 对应百傲瑞达 PersCertificateServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 上午10:10
 * @version v1.0
 */
@Service
@Transactional
public class PersCertificateServiceImpl implements PersCertificateService {
    @Autowired
    private PersCertificateDao persCertificateDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    /** 是否开启数据加密 默认不开启 */
    @Value("${system.data.security-encrypt:false}")
    private boolean isOpenSecurityEncrypt;

    @Override
    public PersCertificateItem saveItem(PersCertificateItem item) {
        PersCertificate persCertificate = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> persCertificateDao.findById(id)).filter(Objects::isNull).orElseGet(() -> {
                return Optional.ofNullable(item).map(PersCertificateItem::getPersonId).filter(StringUtils::isNotBlank)
                    .map(persCertificateDao::findByPersonId).orElse(new PersCertificate());
            });
        ModelUtil.copyPropertiesIgnoreNull(item, persCertificate);
        PersPerson persPerson = persPersonDao.findById(item.getPersonId()).get();
        persCertificate.setPersPerson(persPerson);
        persCertificateDao.save(persCertificate);
        item.setId(persCertificate.getId());
        return item;
    }

    @Override
    public void batchSaveCertificates(List<PersCertificateItem> itemList) {
        // 人员id
        Collection<String> persPersonIdList =
            CollectionUtil.getPropertyList(itemList, PersCertificateItem::getPersonId, "-1");
        List<List<String>> personIdList = CollectionUtil.split(persPersonIdList, CollectionUtil.splitSize);
        List<PersPerson> persPersonList = new ArrayList<>();
        personIdList.forEach(personIds -> {
            persPersonList.addAll(persPersonDao.findByIdIn(personIds));
        });
        Map<String, PersPerson> personIdAndPersPerson =
            persPersonList.stream().collect(Collectors.toMap(PersPerson::getId, PersPerson -> PersPerson));
        List<PersCertificate> persCertificateList = new ArrayList<>();
        List<PersCertificate> delCertificateList = new ArrayList<>();
        itemList.forEach(item -> {
            PersCertificate persCertificate = new PersCertificate();
            PersPerson person = personIdAndPersPerson.get(item.getPersonId());
            if (person != null) {
                List<PersCertificate> persCertificates = person.getPersCertificateList();
                if (persCertificates != null && persCertificates.size() > 0) {
                    persCertificate = persCertificates.get(0);
                    if (!(StringUtils.isNotBlank(item.getCertNumber()) && StringUtils.isNotBlank(item.getCertType()))) {
                        persCertificate.setPersPerson(null);
                        delCertificateList.add(persCertificate);
                    }
                } else {
                    persCertificate.setPersPerson(person);
                }
            }
            if (StringUtils.isNotBlank(item.getCertNumber()) && StringUtils.isNotBlank(item.getCertType())) {
                ModelUtil.copyPropertiesIgnoreNull(item, persCertificate);
                persCertificateList.add(persCertificate);
            }
        });
        if (delCertificateList.size() > 0) {
            List<List<PersCertificate>> certificateList =
                CollectionUtil.split(delCertificateList, CollectionUtil.splitSize);
            certificateList.forEach(certList -> {
                persCertificateDao.deleteAll(certList);
            });
        }

        if (persCertificateList.size() > 0) {
            List<List<PersCertificate>> certificateList =
                CollectionUtil.split(persCertificateList, CollectionUtil.splitSize);
            certificateList.forEach(certList -> {
                persCertificateDao.saveAll(certList);
            });
        }
    }

    @Override
    public List<PersCertificateItem> getByCondition(PersCertificateItem condition) {
        return (List<PersCertificateItem>)persCertificateDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persCertificateDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                persCertificateDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public PersCertificateItem getItemById(String id) {
        List<PersCertificateItem> items = getByCondition(new PersCertificateItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public PersCertificateItem getItemByPersonId(String personId) {
        PersCertificateItem item = new PersCertificateItem();
        item.setPersonId(personId);
        List<PersCertificateItem> items = getByCondition(item);
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public boolean isExistByCertNumberAndCertTypeAndPersonIdNe(String certNumber, String certType, String personId) {
        PersCertificateItem item = new PersCertificateItem(true);
        item.setCertNumber(certNumber);
        item.setCertType(certType);
        if (StringUtils.isNotBlank(personId)) {
            item.setPersonIdNe(personId);
        }
        return CollectionUtil.isEmpty(getByCondition(item));
    }

    @Override
    public void handlerTransfer(List<PersCertificateItem> certificateItems) {
        // 数据量大的时候处理，分批处理
        List<List<PersCertificateItem>> list = CollectionUtil.split(certificateItems, CollectionUtil.splitSize);
        // 获取数据库已存在的门禁人员数据
        List<PersCertificate> persCertificates = persCertificateDao.findAll();
        Map<String, PersCertificate> persCertificateMap =
            CollectionUtil.listToKeyMap(persCertificates, PersCertificate::getCertNumber);
        for (List<PersCertificateItem> itemList : list) {
            // 获取人员集合 根据pin
            Collection<String> pins = CollectionUtil.getPropertyList(itemList, PersCertificateItem::getPin, "-1");
            List<PersPerson> personItems = persPersonDao.findByPinIn(pins);
            Map<String, PersPerson> persPersonItemMap = CollectionUtil.listToKeyMap(personItems, PersPerson::getPin);
            // 获取多人开门人员对象
            for (PersCertificateItem persCertificateItem : itemList) {
                PersCertificate persCertificate = persCertificateMap.remove(persCertificateItem.getCertNumber());
                if (Objects.isNull(persCertificate)) {
                    persCertificate = new PersCertificate();
                }
                PersPerson persPerson = persPersonItemMap.get(persCertificateItem.getPin());
                if (Objects.nonNull(persPerson)) {
                    ModelUtil.copyPropertiesIgnoreNullWithProperties(persCertificateItem, persCertificate, "id");
                    persCertificate.setPersPerson(persPerson);
                    persCertificateDao.saveAndFlush(persCertificate);
                }
            }
            persPersonItemMap = null;
        }

    }

    @Override
    public PersCertificateItem getItemByTypeAndNumberAndPersonPinNe(String certType, String certNumber,
        String personPin) {
        PersCertificate persCertificate =
            persCertificateDao.findByCertTypeAndCertNumberAndPersPerson_PinNot(certType, certNumber, personPin);
        if (persCertificate != null) {
            return ModelUtil.copyProperties(persCertificate, new PersCertificateItem());
        }
        return null;
    }

    @Override
    public List<PersCertificateItem> getItemByPersonPinIn(Collection<String> pins) {
        List<PersCertificate> persCertificateList = persCertificateDao.findByPersPerson_PinIn(pins);
        if (persCertificateList != null) {
            List<PersCertificateItem> persCertificateItemList = new ArrayList<>();
            for (PersCertificate persCertificate : persCertificateList) {
                if (StringUtils.isNotBlank(persCertificate.getCertNumber())) {
                    PersCertificateItem persCertificateItem =
                        ModelUtil.copyProperties(persCertificate, new PersCertificateItem());
                    persCertificateItem.setPersonId(persCertificate.getPersPerson().getId());
                    persCertificateItem.setPin(persCertificate.getPersPerson().getPin());
                    persCertificateItem.setName(persCertificate.getPersPerson().getName());
                    persCertificateItem.setLastName(persCertificate.getPersPerson().getLastName());
                    persCertificateItemList.add(persCertificateItem);
                }
            }
            return persCertificateItemList;
        }
        return null;
    }

    @Override
    public List<PersCertificateItem> getItemByPersonIdIn(Collection<String> ids) {
        List<PersCertificate> persCertificateList =
            persCertificateDao.findByPersPerson_IdInAndCertNumberNotNullAndCertTypeNotNull(ids);
        if (persCertificateList != null) {
            List<BaseDictionaryValueItem> certTypeList =
                baseDictionaryValueService.getDictionaryValues("certificateType");
            // 系统已存在证件 key:dictValue value:code
            Map<String, String> certMap = new HashMap<>();
            for (BaseDictionaryValueItem certType : certTypeList) {
                certMap.put(certType.getCode(), certType.getDictValue());
            }

            List<PersCertificateItem> persCertificateItemList = new ArrayList<>();
            for (PersCertificate persCertificate : persCertificateList) {
                if (StringUtils.isNotBlank(persCertificate.getCertType())
                    && StringUtils.isNotBlank(persCertificate.getCertNumber())) {
                    PersCertificateItem persCertificateItem =
                        ModelUtil.copyProperties(persCertificate, new PersCertificateItem());
                    persCertificateItem.setPersonId(persCertificate.getPersPerson().getId());
                    persCertificateItem.setPin(persCertificate.getPersPerson().getPin());
                    persCertificateItem.setName(persCertificate.getPersPerson().getName());
                    persCertificateItem.setLastName(persCertificate.getPersPerson().getLastName());
                    persCertificateItem.setCertName(certMap.get(persCertificate.getCertType()));
                    persCertificateItemList.add(persCertificateItem);
                }
            }
            return persCertificateItemList;
        }
        return null;
    }

    @Override
    public void encryptPersCertificate() {
        if (isOpenSecurityEncrypt) {
            List<PersCertificate> persCertificateList = persCertificateDao.findAll();
            for (PersCertificate persCertificate : persCertificateList) {
                persCertificate.setCertNumber(FoldexUtil.encryptByRandomSey(persCertificate.getCertNumber()));
            }
            if (persCertificateList.size() > 0) {
                List<List<PersCertificate>> certificateList =
                    CollectionUtil.split(persCertificateList, CollectionUtil.splitSize);
                certificateList.forEach(certificates -> {
                    persCertificateDao.saveAll(certificates);
                });
            }
        }

    }
}