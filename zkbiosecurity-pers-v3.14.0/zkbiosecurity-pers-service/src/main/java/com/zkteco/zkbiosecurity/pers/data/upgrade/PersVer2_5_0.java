package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryService;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryItem;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryValueItem;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class PersVer2_5_0 implements UpgradeVersionManager {
    @Autowired
    private BaseDictionaryService baseDictionaryService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v2.5.0";
    }

    @Override
    public boolean executeUpgrade() {
        // 人脸抠图不及格错误
        BaseDictionaryItem baseDictionaryItem =
            new BaseDictionaryItem("PersFaceFailType", "pers_face_failType", false, "pers_module");
        baseDictionaryItem = baseDictionaryService.initData(baseDictionaryItem);
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5001", "pers_face_poorResolution", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5002", "pers_face_noFace", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5003", "pers_face_manyFace", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5005", "pers_face_smallFace", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5006", "pers_face_notColor", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5007", "pers_face_seriousBlur", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5008", "pers_face_seriousBlur", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService.initData(
            new BaseDictionaryValueItem("-5009", "pers_face_intensivelyLight", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5010", "pers_face_badIllumination", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5011", "pers_face_highNoise", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5012", "pers_face_highStretch", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5013", "pers_face_covered", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5014", "pers_face_smileOpenMouth", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5015", "pers_face_largeAngle", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService
            .initData(new BaseDictionaryValueItem("-5016", "pers_face_seriousBlur", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService.initData(
            new BaseDictionaryValueItem("-5017", "pers_face_criticalIllumination", 0, baseDictionaryItem.getId()));
        baseDictionaryValueService.initData(
            new BaseDictionaryValueItem("-5018", "pers_face_criticalLargeAngle", 0, baseDictionaryItem.getId()));
        return true;
    }
}
