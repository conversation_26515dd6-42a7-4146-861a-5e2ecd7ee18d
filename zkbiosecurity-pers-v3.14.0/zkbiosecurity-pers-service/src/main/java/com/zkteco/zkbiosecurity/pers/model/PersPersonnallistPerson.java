package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 名单库跟人关系
 * 
 * <AUTHOR>
 * @date 2020/7/24
 */
@Setter
@Getter
@Accessors(chain = true)
@Entity
@Table(name = "PERS_PERSONNALLIST_PERSON",
    uniqueConstraints = @UniqueConstraint(columnNames = {"PERSONNALLIST_ID", "PERSON_ID"}))
public class PersPersonnallistPerson extends BaseModel implements Serializable {

    /** 名单库id */
    @Column(name = "PERSONNALLIST_ID", length = 50)
    private String personnallistId;

    /** 人员id，离职人员id，访客id */
    @Column(name = "PERSON_ID", length = 50)
    private String personId;

    /** 关联表，涉及 人员表PERS_PERSON，离职人员表PERS_LEAVEPERSON，访客表VIS_VISITOR */
    @Column(name = "LINK_TBL", length = 100)
    private String linkTbl;

    /** 人员编号 访客编号或人员编号或离职人员编号 */
    @Column(name = "PERSON_PIN", length = 200)
    private String personPin;

    /** 人员姓名 访客姓名或人员姓名或离职人员姓名 */
    @Column(name = "PERSON_NAME", length = 200)
    private String personName;

    @Column(name = "LAST_NAME", length = 200)
    private String lastName;

    /** 人员性别 */
    @Column(name = "PERSON_GENDER", length = 1)
    private String personGender;

    /** 人员出生日期 */
    @Column(name = "PERSON_BIRTHDAY")
    @Temporal(TemporalType.DATE)
    private Date personBirthday;

    /*** 身份证 */
    @Column(name = "ID_CARD", length = 20)
    private String idCard;

    /*** 职位名称 */
    @Column(name = "POSITION_NAME", length = 100)
    private String positionName;

    /*** 联系电话 */
    @Column(name = "MOBILE_PHONE", length = 20)
    private String mobilePhone;

    /*** 邮箱 */
    @Column(name = "EMAIL", length = 100)
    private String email;

    public PersPersonnallistPerson(String personnallistId, String personId) {
        this.personnallistId = personnallistId;
        this.personId = personId;
    }

    public PersPersonnallistPerson() {}
}
