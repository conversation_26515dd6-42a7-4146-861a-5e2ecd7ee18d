package com.zkteco.zkbiosecurity.pers.service.impl;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.zkteco.zkbiosecurity.auth.exception.ZKAuthException;
import com.zkteco.zkbiosecurity.auth.service.AuthenticationService;
import com.zkteco.zkbiosecurity.auth.vo.AuthUserItem;
import com.zkteco.zkbiosecurity.pers.service.PersBioTemplateService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 认证器实现 Author: liuyuyi Mail:<EMAIL> Time:2018/12/19 Desc:
 */
@Service
public class PersAuthenticationServiceImpl implements AuthenticationService {
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;

    @Override
    public String getLoginType() {
        return "PERS";
    }

    @Override
    public AuthUserItem login(String userName, String pwd) throws ZKAuthException {
        if (StringUtils.isEmpty(userName)) {
            throw new ZKAuthException("auth_login_PinNotNull");
        }
        if (StringUtils.isEmpty(pwd)) {
            throw new ZKAuthException("base_login_inputPass");
        }
        PersPersonItem itemByPin = persPersonService.getItemByPin(userName);
        if (itemByPin == null || !pwd.equals(itemByPin.getSelfPwd())) {
            // auth_login_userNotExist转成base_mail_userOrPwdWrong
            throw new ZKAuthException("auth_login_userOrPwdError");
        }

        AuthUserItem item = new AuthUserItem();
        item.getRoleSet().add("employee");
        item.setId(itemByPin.getId());
        item.setEmail(itemByPin.getEmail());
        item.setIsActive(true);
        item.setIsSuperuser(false);
        item.setName(itemByPin.getName());
        item.setLastName(itemByPin.getLastName());
        item.setUsername(itemByPin.getPin());
        item.setIsStaff(true);
        item.setUserLoginLimit(itemByPin.getPersLoginLimit());
        // 是否授权员工移动端登录标识 add by xjiang.huang 2023-07-19
        item.setAppAuthorization(itemByPin.getAppAuthorization());
        if (itemByPin.getDeptId() == null) {
            item.setCompanyId("default");
        }
        item.setCompanyId(itemByPin.getDeptId());
        // 由于“用户”菜单没有开放权限分配，但是用户需要编辑自身信息功能需要额外放一个用户编辑权限
        item.getPermissionSet().add("auth:user:edit");
        /*
         * String bioTemplateJson = JSON.toJSONString(persBioTemplateService.getItemMapByPersonId(itemByPin.getId()));
         * item.setBioTemplateJson(bioTemplateJson);
         */
        return item;
    }

    @Override
    public Boolean modifyPwd(String userName, String oldPassword, String newPassword, boolean checkPassword) {
        boolean b = persPersonService.changeSelfPwd(userName, oldPassword, newPassword, checkPassword);
        return b;
    }

}
