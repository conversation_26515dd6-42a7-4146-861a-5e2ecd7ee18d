/*
 * File Name: VisInitTask Created by caiyun.chen on 2019/9/11 15:06. Copyright:Copyright © 1985-2017 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.pers.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@Component
@Order(value = 23)
public class PersInitTask implements CommandLineRunner {
    @Autowired
    private PersSyncPersonAcmsTask persSyncPersonAcmsTask;

    @Override
    public void run(String... strings) throws Exception {
        // 开启定时同步人员信息到ACMS
        persSyncPersonAcmsTask.syncPersonAcms();
    }
}
