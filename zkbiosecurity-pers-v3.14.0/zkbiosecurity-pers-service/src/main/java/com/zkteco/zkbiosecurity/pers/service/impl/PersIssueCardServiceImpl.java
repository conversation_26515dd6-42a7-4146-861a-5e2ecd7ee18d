/**
 * File Name: PersIssueCardServiceImpl Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.PersCardDao;
import com.zkteco.zkbiosecurity.pers.dao.PersIssueCardDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersIssueCard;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.utils.PersPersonUtil;
import com.zkteco.zkbiosecurity.pers.utils.ProcessBeanUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersIssueCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersNoCardPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 对应百傲瑞达 PersIssueCardServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
@Service
@Transactional
public class PersIssueCardServiceImpl implements PersIssueCardService {
    @Autowired
    private PersIssueCardDao persIssueCardDao;
    @Autowired
    private PersCardDao persCardDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersCardService persCardService;
    /** 是否开启数据加密 默认不开启 */
    @Value("${system.data.security-encrypt:false}")
    private boolean isOpenSecurityEncrypt;
    @Autowired(required = false)
    private PersPersonExtService[] persPersonExtServices;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersAcmsService persAcmsService;
    @Autowired
    private ProgressCache progressCache;

    @Override
    public PersIssueCardItem saveItem(PersIssueCardItem item) {
        PersIssueCard persIssueCard = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> persIssueCardDao.findById(id)).orElse(new PersIssueCard());
        ModelUtil.copyProperties(item, persIssueCard);
        persIssueCardDao.save(persIssueCard);
        item.setId(persIssueCard.getId());
        return item;
    }

    @Override
    public void savePersIssueCard(String cardNo, Short operateType, PersPersonItem personItem) {
        PersIssueCard persIssueCard = new PersIssueCard();
        persIssueCard.setCardNo(cardNo);
        persIssueCard.setOperateType(operateType);
        if (Objects.nonNull(personItem)) {
            persIssueCard.setPin(personItem.getPin());
            persIssueCard.setName(personItem.getName());
            persIssueCard.setLastName(personItem.getLastName());
        }
        persIssueCardDao.save(persIssueCard);
    }

    @Override
    public void createIssueInfo(String pin, String name, String lastName, String cardNo, short operateType) {
        PersIssueCard persIssueCard = new PersIssueCard();
        persIssueCard.setOperateType(operateType);
        persIssueCard.setPin(pin);
        persIssueCard.setName(name);
        persIssueCard.setLastName(lastName);
        persIssueCard.setCardNo(cardNo);
        persIssueCardDao.save(persIssueCard);
    }

    @Override
    public List<PersIssueCardItem> getByCondition(PersIssueCardItem condition) {
        return (List<PersIssueCardItem>)persIssueCardDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persIssueCardDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                persIssueCardDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public PersIssueCardItem getItemById(String id) {
        List<PersIssueCardItem> items = getByCondition(new PersIssueCardItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public Pager findNoCardPerson(String sessionId, PersNoCardPersonItem condition, int page, int size) {
        if (StringUtils.isNotBlank(condition.getStartPersonPin())) {
            condition.setStartNumberPin(PersPersonUtil.createNumberPin(condition.getStartPersonPin()));
        }
        if (StringUtils.isNotBlank(condition.getEndPersonPin())) {
            condition.setEndNumberPin(PersPersonUtil.createNumberPin(condition.getEndPersonPin()));
        }
        Long startNumberPin = condition.getStartNumberPin();
        Long endNumberPin = condition.getEndNumberPin();
        if (startNumberPin != null && endNumberPin != null && startNumberPin > endNumberPin) {
            throw ZKBusinessException.warnException("pers_batchIssCard_startPinLargeThanEndPin");
        }
        return persPersonService.findNoCardPerson(sessionId, condition, page, size);
    }

    @Override
    public void batchIssueCard(List<PersCardItem> cardItemList) {
        persCardService.saveMultiCard(cardItemList);
        // 通知其他模块下发发卡命令
        if (persPersonExtServices != null) {
            Arrays.stream(persPersonExtServices).forEach(ps -> ps.issuedCard(cardItemList));
        }
    }

    @Override
    public void batchIssueAcmsCard(String personIds, String deptIds) {
        List<PersCardItem> persCardItemList = new ArrayList<>();
        List<PersPerson> personList = new ArrayList<>();
        if (StringUtils.isNotBlank(deptIds)) {
            personList = persPersonDao.findNoCardIdsByDeptIds(StrUtil.strToList(deptIds));
        } else if (StringUtils.isNotBlank(personIds)) {
            personList = persPersonDao.findByIdIn(StrUtil.strToList(personIds));
        }
        if (!personList.isEmpty()) {
            List<PersPersonItem> persPersonItemList = ModelUtil.copyListProperties(personList, PersPersonItem.class);
            int size = persPersonItemList.size();
            ZKResultMsg cardMsg = persAcmsService.obtainAcmsCard("", size + "");
            if (!ZKResultMsg.successMsg().getRet().equals(cardMsg.getRet())) {
                String message = StringUtils.isNotBlank(cardMsg.getMsg()) ? cardMsg.getMsg() : "pers_issueCard_error";
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(30, I18nUtil.i18nCode(message)));
                return;
            }
            String cardMsgData = cardMsg.getData().toString();
            List<PersCardItem> acmsCardNumList = new ArrayList<>();
            if (StringUtils.isNotBlank(cardMsgData)) {
                acmsCardNumList = JSONArray.parseArray(cardMsgData, PersCardItem.class);
            }
            if (acmsCardNumList.size() < size) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(30, I18nUtil.i18nCode("system_acms_stock_empty")));
            } else {
                for (PersPersonItem persPersonItem : persPersonItemList) {
                    PersCardItem cardItem = acmsCardNumList.get(0);
                    cardItem.setPersonId(persPersonItem.getId());
                    cardItem.setPersonPin(persPersonItem.getPin());
                    cardItem.setCardType(PersConstants.MAIN_CARD);
                    cardItem.setCardState(PersConstants.CARD_VALID);
                    cardItem.setIsFrom(PersConstants.ACMS);
                    persCardItemList.add(cardItem);
                    acmsCardNumList.remove(0);
                }

            }
        }
        if (!persCardItemList.isEmpty()) {
            batchIssueCard(persCardItemList);
        }
    }

    @Override
    public void encryptPersIssueCard() {
        if (isOpenSecurityEncrypt) {
            List<PersIssueCard> persIssueCardList = persIssueCardDao.findAll();
            for (PersIssueCard persIssueCard : persIssueCardList) {
                persIssueCard.setCardNo(FoldexUtil.encryptByRandomSey(persIssueCard.getCardNo()));
            }
            if (persIssueCardList.size() > 0) {
                List<List<PersIssueCard>> issueCardList =
                    CollectionUtil.split(persIssueCardList, CollectionUtil.splitSize);
                issueCardList.forEach(issueCards -> {
                    persIssueCardDao.saveAll(issueCards);
                });
            }
        }
    }

    @Override
    public List<?> getItemData(Class<PersIssueCardItem> persIssueCardItemClass, BaseItem condition, int beginIndex,
        int endIndex) {
        return persIssueCardDao.getItemsDataBySql(persIssueCardItemClass, SQLUtil.getSqlByItem(condition), beginIndex,
            endIndex, true);
    }

    @Override
    public List<PersNoCardPersonItem> protectPinAndCard(List<PersNoCardPersonItem> items) {
        if (persPersonService.isProtectData()) {
            items.forEach(item -> {
                String pin = item.getPersonPin();
                item.setPersonPin(PersRegularUtil.hideWithAsterisk(pin));
                String cardNo = item.getCardNo();
                item.setCardNo(PersRegularUtil.hideWithAsterisk(cardNo));
            });
        }
        return items;
    }

    @Override
    public List<PersIssueCardItem> protectIssuePinAndCard(List<PersIssueCardItem> items) {
        if (persPersonService.isProtectData()) {
            items.forEach(item -> {
                String pin = item.getPin();
                item.setPin(PersRegularUtil.hideWithAsterisk(pin));
                String cardNo = item.getCardNo();
                item.setCardNo(PersRegularUtil.hideWithAsterisk(cardNo));
            });
        }
        return items;
    }
}