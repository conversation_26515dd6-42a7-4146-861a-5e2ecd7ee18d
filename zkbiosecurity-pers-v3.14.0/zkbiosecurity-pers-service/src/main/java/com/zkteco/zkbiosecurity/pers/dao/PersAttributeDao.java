/**
 * File Name: PersAttribute
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import java.util.List;

import org.springframework.data.jpa.repository.Query;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;

import com.zkteco.zkbiosecurity.pers.model.PersAttribute;

/**
 * 对应百傲瑞达 PersAttributeDao
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersAttributeDao extends BaseDao<PersAttribute, String> {
	/**
	 * 根据名称查询
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/12/6 21:16
	 * @param attrName
	 * @return com.zkteco.zkbiosecurity.pers.model.PersAttribute
	 */
	PersAttribute findByAttrName(String attrName);

	/**
	 * 根据初始化字段查询
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/12/6 21:16
	 * @param sqlStr
	 * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersAttribute>
	 */
	List<PersAttribute> findBySqlStr(String sqlStr);

	/**
	 * 根据名称查询IDS
	 * @param attrName
	 * @return
	 */
	@Query(value = "SELECT t.ID FROM PERS_ATTRIBUTE t WHERE t.ATTR_NAME like ?1", nativeQuery = true)
	List<String> findIdsByAttrName(String attrName);

	//根据控件类型查询
	List<PersAttribute> findByControlType(String controlType);
}