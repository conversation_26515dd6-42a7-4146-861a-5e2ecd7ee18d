package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.dashboard.service.Dashboard4PersService;
import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PersPersonTrendsItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.*;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2019/1/4
 */
@Service
@Transactional
public class Dashboard4PersServiceImpl implements Dashboard4PersService {

    @Autowired
    private PersBioTemplateDao persBioTemplateDao;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersCardDao persCardDao;
    @Autowired
    private PersLeavePersonDao persLeavePersonDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersBioPhotoDao persBioPhotoDao;

    /**
     * 获取dashboard生物模板数据及人员总数
     * 
     * <AUTHOR>
     * @Date 2019/1/5 9:09
     * @param
     * @return
     */
    @Override
    public Map<String, String> getBaseBioData() {
        // Object[] 下标 0:为 bioType 1：为 count
        List<Object[]> bioTemplateCount =
            persBioTemplateDao.countGroupByBioType(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
        // 人员总数
        int personCount = persPersonDao.countByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        Map<String, String> dataCount = new HashMap<String, String>();
        // 已录指纹人数
        int hasFpTotal = 0;
        // 已录指静脉人数
        int hasFvTotal = 0;
        // 已录人脸人数
        int hasFaceTotal = 0;
        // 已录掌纹人数
        int hasPlamTotal = 0;
        // 已录可见光手掌人数
        int hasVisiblePlamTotal = 0;
        // 已录可见光人脸数
        int hasVislightTotal = 0;
        for (Object[] objArray : bioTemplateCount) {
            if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.FP_BIO_TYPE) {
                hasFpTotal = Integer.parseInt(objArray[1] + "");
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.VEIN_BIO_TYPE) {
                hasFvTotal = Integer.parseInt(objArray[1] + "");
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.FACE_BIO_TYPE) {
                hasFaceTotal = Integer.parseInt(objArray[1] + "");
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.PALM_BIO_TYPE) {
                hasPlamTotal = Integer.parseInt(objArray[1] + "");
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE) {
                hasVislightTotal = Integer.parseInt(objArray[1] + "");
            } else if (Integer.parseInt(objArray[0].toString()) == PersConstants.PALM_BIO_TYPE_10) {
                hasVisiblePlamTotal = Integer.parseInt(objArray[1] + "");
            }
        }
        // 已录指纹人数
        dataCount.put("hasFpTotal", hasFpTotal + "");
        // 未录指纹人数
        dataCount.put("noFpTotal", (personCount - hasFpTotal) + "");
        // 已录指静脉人数
        dataCount.put("hasFvTotal", hasFvTotal + "");
        // 未录指静脉人数
        dataCount.put("noFvTotal", (personCount - hasFvTotal) + "");
        // 已录人脸人数
        dataCount.put("hasFaceTotal", hasFaceTotal + "");
        // 未录人脸人数
        dataCount.put("noFaceTotal", (personCount - hasFaceTotal) + "");
        // 已录掌纹人数
        dataCount.put("hasPlamTotal", hasPlamTotal + "");
        // 未录掌纹人数
        dataCount.put("noPlamTotal", (personCount - hasPlamTotal) + "");
        // 已录可见光手掌人数
        dataCount.put("hasVisiblePlamTotal", hasVisiblePlamTotal + "");
        // 未录可见光手掌人数
        dataCount.put("noVisiblePlamTotal", (personCount - hasVisiblePlamTotal) + "");
        // 以发卡 未发卡人数
        List<Integer> cardCount = persCardDao.getHasCardPersonCount();
        dataCount.put("hasCardTotal", cardCount.get(0) + "");
        dataCount.put("noCardTotal", (personCount - Integer.parseInt(cardCount.get(0) + "") + ""));

        List<Integer> pwdCount =
            persPersonDao.getHasPwdPersonCountByPersonType(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
        // 以设密码 未设密码人数
        dataCount.put("hasPswTotal", pwdCount.get(0) + "");
        dataCount.put("noPswTotal", (personCount - Integer.parseInt(pwdCount.get(0) + "") + ""));
        // 已录可见光人脸人数
        dataCount.put("hasVislightTotal", hasVislightTotal + "");
        // 未录可见光人脸人数
        dataCount.put("noVislightTotal",
            (personCount - hasVislightTotal < 0 ? 0 : personCount - hasVislightTotal) + "");

        long hasBiophotoTotal =
            persBioPhotoDao.countByBioTypeGroupByPersonId(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        // 已录比对照片人数
        dataCount.put("hasBiophotoTotal", hasBiophotoTotal + "");
        // 未录比对照片人数
        dataCount.put("noBiophotoTotal",
            (personCount - hasBiophotoTotal < 0 ? 0 : personCount - hasBiophotoTotal) + "");
        // 已录照片人数
        long hasPortraitphotoTotal = persPersonDao.getHasPhotoCountByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        if (hasPortraitphotoTotal > 0) {
            dataCount.put("hasPortraitphotoTotal", hasPortraitphotoTotal + "");
            // 未录照片人数
            dataCount.put("noPortraitphotoTotal",
                (personCount - hasPortraitphotoTotal < 0 ? 0 : personCount - hasPortraitphotoTotal) + "");
        }
        // 人员总数
        dataCount.put("personCount", personCount + "");
        return dataCount;
    }

    @Override
    public Dashboard4PersPersonTrendsItem getPersPersonTrends() {
        Dashboard4PersPersonTrendsItem item = new Dashboard4PersPersonTrendsItem();
        int year = DateUtil.getYear(new Date());
        Integer personCount = 0;
        Integer leavePersonCount = 0;
        Map<String, String> personMap = new LinkedHashMap<>();
        Map<String, String> leavePersonMap = new LinkedHashMap<>();
        for (int i = 1; i <= 12; i++) {
            Date startDate = DateUtil.getStartMonthDate(year, i);
            Date endDate = DateUtil.getEndMonthDate(year, i);
            personCount = persPersonDao.countByCreateTimeGreaterThanEqualAndCreateTimeLessThanEqual(startDate, endDate);
            personMap.put(i + I18nUtil.i18nCode("common_month"), personCount.toString());
            leavePersonCount =
                persLeavePersonDao.countByCreateTimeGreaterThanEqualAndCreateTimeLessThanEqual(startDate, endDate);
            leavePersonMap.put(i + I18nUtil.i18nCode("common_month"), leavePersonCount.toString());
        }
        item.setPersonData(personMap);
        item.setLeavePersonData(leavePersonMap);
        return item;
    }

    @Override
    public Map<String, String> getPersBioData() {
        // Object[] 下标 0:为 bioType 1：为version 2：为 count
        List<Object[]> bioTemplateCount =
            persBioTemplateDao.countGroupByBioTypeAndVersion(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
        // 人员总数
        int personCount = persPersonDao.countByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        Map<String, String> dataCount = new HashMap<String, String>();
        // 已录指纹人数
        Map<String, Integer> fpMap = new HashMap<>();
        // 已录指静脉人数
        int hasFvTotal = 0;
        // 已录人脸人数
        int hasFaceTotal = 0;
        // 已录掌纹人数
        int hasPlamTotal = 0;
        // 已录可见光手掌人数
        int hasVisiblePlamTotal = 0;
        // 已录可见光人脸数
        Map<String, Integer> vislightMap = new HashMap<>();
        for (Object[] objArray : bioTemplateCount) {
            if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.FP_BIO_TYPE) {
                if (fpMap.containsKey(objArray[1].toString())) {
                    fpMap.put(objArray[1].toString(),
                        fpMap.get(objArray[1].toString()) + Integer.parseInt(objArray[2].toString()));
                } else {
                    fpMap.put(objArray[1].toString(), Integer.parseInt(objArray[2].toString()));
                }
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.VEIN_BIO_TYPE) {
                hasFvTotal = Integer.parseInt(objArray[2] + "");
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.FACE_BIO_TYPE) {
                hasFaceTotal = Integer.parseInt(objArray[2] + "");
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.PALM_BIO_TYPE) {
                hasPlamTotal = Integer.parseInt(objArray[2] + "");
            } else if (Integer.parseInt(objArray[0].toString()) == PersConstants.PALM_BIO_TYPE_10) {
                hasVisiblePlamTotal = Integer.parseInt(objArray[2] + "");
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE) {
                if (vislightMap.containsKey(objArray[1].toString())) {
                    vislightMap.put(objArray[1].toString(),
                        vislightMap.get(objArray[1].toString()) + Integer.parseInt(objArray[2].toString()));
                } else {
                    vislightMap.put(objArray[1].toString(), Integer.parseInt(objArray[2].toString()));
                }
            }
        }
        Set<String> fpKeys = fpMap.keySet();
        if (!fpKeys.isEmpty()) {
            for (String key : fpKeys) {
                // 已录指纹人数
                dataCount.put("hasFp_" + key, fpMap.get(key).toString());
                // 未录指纹人数
                dataCount.put("noFp_" + key, (personCount - fpMap.get(key)) + "");
            }
            dataCount.put("fpVersions", StringUtils.join(fpKeys, ","));
        }
        if (hasFvTotal > 0) {
            // 已录指静脉人数
            dataCount.put("hasFvTotal", hasFvTotal + "");
            // 未录指静脉人数
            dataCount.put("noFvTotal", (personCount - hasFvTotal) + "");
        }
        if (hasFaceTotal > 0) {
            // 已录人脸人数
            dataCount.put("hasFaceTotal", hasFaceTotal + "");
            // 未录人脸人数
            dataCount.put("noFaceTotal", (personCount - hasFaceTotal) + "");
        }
        if (hasPlamTotal > 0) {
            // 已录掌纹人数
            dataCount.put("hasPlamTotal", hasPlamTotal + "");
            // 未录掌纹人数
            dataCount.put("noPlamTotal", (personCount - hasPlamTotal) + "");
        }
        if (hasVisiblePlamTotal > 0) {
            // 已录可见光手掌人数
            dataCount.put("hasVisiblePlamTotal", hasVisiblePlamTotal + "");
            // 未录可见光手掌人数
            dataCount.put("noVisiblePlamTotal", (personCount - hasVisiblePlamTotal) + "");
        }
        // 以发卡 未发卡人数
        List<Integer> cardCount = persCardDao.getHasCardPersonCount();
        if (cardCount.get(0) > 0) {
            dataCount.put("hasCardTotal", cardCount.get(0) + "");
            dataCount.put("noCardTotal", (personCount - Integer.parseInt(cardCount.get(0) + "") + ""));
        }

        List<Integer> pwdCount = persPersonDao.getHasPwdPersonCountByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        if (pwdCount.get(0) > 0) {
            // 以设密码 未设密码人数
            dataCount.put("hasPswTotal", pwdCount.get(0) + "");
            dataCount.put("noPswTotal", (personCount - Integer.parseInt(pwdCount.get(0) + "") + ""));
        }

        Set<String> vislightKeys = vislightMap.keySet();
        if (!vislightKeys.isEmpty()) {
            for (String key : vislightKeys) {
                // 已录可见光人脸人数
                dataCount.put("hasVislight_" + key, vislightMap.get(key).toString());
                // 未录可见光人脸人数
                int noVislight = (personCount - vislightMap.get(key)) < 0 ? 0 : personCount - vislightMap.get(key);
                dataCount.put("noVislight_" + key, noVislight + "");
            }
            dataCount.put("vislightVersions", StringUtils.join(vislightKeys, ","));
        }

        long hasBiophotoTotal =
            persBioPhotoDao.countByBioTypeGroupByPersonId(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        if (hasBiophotoTotal > 0) {
            // 已录比对照片人数
            dataCount.put("hasBiophotoTotal", hasBiophotoTotal + "");
            // 未录比对照片人数
            dataCount.put("noBiophotoTotal",
                (personCount - hasBiophotoTotal < 0 ? 0 : personCount - hasBiophotoTotal) + "");
        }
        // 已录照片人数
        long hasPortraitphotoTotal = persPersonDao.getHasPhotoCountByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        if (hasPortraitphotoTotal > 0) {
            dataCount.put("hasPortraitphotoTotal", hasPortraitphotoTotal + "");
            // 未录照片人数
            dataCount.put("noPortraitphotoTotal",
                (personCount - hasPortraitphotoTotal < 0 ? 0 : personCount - hasPortraitphotoTotal) + "");
        }
        // 人员总数
        dataCount.put("personCount", personCount + "");
        return dataCount;
    }

    @Override
    public String getPersonPhotoPathByPin(String pin) {
        return persPersonService.getPersonPhotoPathByPin(pin);
    }
}
