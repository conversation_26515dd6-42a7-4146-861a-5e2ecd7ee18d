package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtBIdDao;
import com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtDao;
import com.zkteco.zkbiosecurity.pers.model.PersWiegandFmt;
import com.zkteco.zkbiosecurity.pers.model.PersWiegandFmtBId;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmt4OtherService;
import com.zkteco.zkbiosecurity.pers.service.PersWiegandFmtService;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;
import com.zkteco.zkbiosecurity.system.service.ModuleInfoService;

@Service
public class PersWiegandFmtServiceImpl implements PersWiegandFmtService {
    @Autowired
    private PersWiegandFmtDao persWiegandFmtDao;
    @Autowired
    private PersWiegandFmtBIdDao persWiegandFmtBIdDao;
    @Autowired
    private ModuleInfoService moduleInfoService;
    @Autowired(required = false)
    private PersWiegandFmt4OtherService[] persWiegandFmt4OtherServices;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PersWiegandFmtItem saveItem(PersWiegandFmtItem item) {
        PersWiegandFmt persWiegandFmt = Optional.ofNullable(item).map(PersWiegandFmtItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(id -> persWiegandFmtDao.findById(id)).orElseGet(() -> {
                PersWiegandFmt fmt = new PersWiegandFmt();
                fmt.setInitFlag(false);
                return fmt;
            });
        ModelUtil.copyPropertiesIgnoreNull(item, persWiegandFmt);
        if (Objects.isNull(persWiegandFmt.getBusinessId())) {
            persWiegandFmt.setBusinessId(createBId());
        }
        // 如果是改为非自动匹配，需要判断系统中至少有一个自动匹配的韦根格式
        if (!persWiegandFmt.getIsDefaultFmt() && persWiegandFmtDao.countByIsDefaultFmt(true) == 0) {
            throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN, "pers_wgFmt_atLeastDefaultFmt");
        }
        // 相同位数的韦根格式，只能有一个是默认格式
        if (item.getIsDefaultFmt()) {
            List<PersWiegandFmt> wfList = persWiegandFmtDao.findAll();
            List<PersWiegandFmt> wiegandFmts = new ArrayList<>();
            for (PersWiegandFmt pwf : wfList) {
                if (!pwf.getId().equals(item.getId()) && pwf.getIsDefaultFmt()
                    && pwf.getWiegandCount().equals(item.getWiegandCount())) {
                    wiegandFmts.add(pwf);
                }
            }
            for (PersWiegandFmt wf : wiegandFmts) {
                if (wf.getCardFmt().equals(item.getCardFmt()) && wf.getParityFmt().equals(item.getParityFmt())) {
                    if (wf.getSiteCode().equals(item.getSiteCode())) {
                        // 提示相同韦根格式无法同时设置为自动匹配
                        throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN,
                            "pers_wgFmt_defaultFmtError2");
                    }
                } else {
                    // 提示相同位数中含有其他格式的韦根格式，无法编辑
                    throw new ZKBusinessException(ZKBusinessException.EXCEPTIONLEVEL_WARN,
                        "pers_wgFmt_defaultFmtError1");
                }
            }
        }

        persWiegandFmtDao.save(persWiegandFmt);
        item.setId(persWiegandFmt.getId());

        if (persWiegandFmt4OtherServices != null) {
            Arrays.stream(persWiegandFmt4OtherServices).forEach(
                persWiegandFmt4OtherService -> persWiegandFmt4OtherService.editWiegandFmt(persWiegandFmt.getId()));
        }
        return item;
    }

    private Long createBId() {
        PersWiegandFmtBId persWiegandFmtBId = new PersWiegandFmtBId();
        persWiegandFmtBId = persWiegandFmtBIdDao.save(persWiegandFmtBId);
        return persWiegandFmtBId.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PersWiegandFmtItem initData(PersWiegandFmtItem item) {
        PersWiegandFmt wiegandFmt = persWiegandFmtDao.findByName(item.getName());
        if (wiegandFmt == null) {
            wiegandFmt = new PersWiegandFmt();
            ModelUtil.copyPropertiesIgnoreNull(item, wiegandFmt);
            if (Objects.isNull(wiegandFmt.getBusinessId())) {
                wiegandFmt.setBusinessId(createBId());
            }
            persWiegandFmtDao.save(wiegandFmt);
            item.setId(wiegandFmt.getId());
        }
        return item;
    }

    @Override
    public List<PersWiegandFmtItem> getByCondition(PersWiegandFmtItem condition) {
        return (List<PersWiegandFmtItem>)persWiegandFmtDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persWiegandFmtDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(String ids) {
        List<String> idList = (List<String>)CollectionUtil.strToList(ids);
        if (idList.size() > 0) {
            if (persWiegandFmt4OtherServices != null) {
                Arrays.stream(persWiegandFmt4OtherServices)
                    .forEach(persWiegandFmt4OtherService -> persWiegandFmt4OtherService.checkUseWiegandFmt(ids));
            }
            List<PersWiegandFmtItem> wgList = getItemsByIds(idList);
            for (PersWiegandFmtItem wg : wgList) {
                if (wg.getInitFlag()) {
                    throw new ZKBusinessException("pers_wgFmt_verify4");
                }
                persWiegandFmtDao.delete(wg.getId());
            }
            if (persWiegandFmt4OtherServices != null) {
                Arrays.stream(persWiegandFmt4OtherServices)
                    .forEach(persWiegandFmt4OtherService -> persWiegandFmt4OtherService.delWiegandFmt(wgList));
            }
        }
        return true;
    }

    @Override
    public PersWiegandFmtItem getItemById(String id) {
        return Optional.ofNullable(id).filter(StringUtils::isNotBlank).flatMap(persWiegandFmtDao::findById)
            .map(wiegandFmt -> ModelUtil.copyProperties(wiegandFmt, new PersWiegandFmtItem())).orElse(null);
    }

    @Override
    public List<PersWiegandFmtItem> getItemsByIds(List<String> ids) {
        return Optional.ofNullable(ids).filter(i -> !i.isEmpty()).map(persWiegandFmtDao::findByIdList)
            .map(list -> ModelUtil.copyListProperties(list, PersWiegandFmtItem.class)).orElse(new ArrayList<>());
    }

    @Override
    public PersWiegandFmtItem getDefAutoMatch() {
        return Optional.ofNullable(persWiegandFmtDao.findByWiegandMode(PersConstants.WG_AUTO_MODE))
            .filter(Objects::nonNull)
            .map(persWiegandFmt -> ModelUtil.copyProperties(persWiegandFmt, new PersWiegandFmtItem())).orElse(null);
    }

    @Override
    public List<PersWiegandFmtItem> getAutoMatch() {
        return Optional.ofNullable(persWiegandFmtDao.findByIsDefaultFmt(true)).filter(Objects::nonNull)
            .map(list -> ModelUtil.copyListProperties(list, PersWiegandFmtItem.class)).orElse(new ArrayList<>());
    }

    @Override
    public boolean existsByName(String name) {
        return persWiegandFmtDao.existsByName(name);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerTransfer(List<PersWiegandFmtItem> wiegandFmtItems) {
        wiegandFmtItems.forEach(persWiegandFmtItem -> {
            PersWiegandFmt persWiegandFmt = ModelUtil.copyProperties(persWiegandFmtItem, new PersWiegandFmt());
            persWiegandFmtDao.save(persWiegandFmt);
        });
    }

    @Override
    public List<PersWiegandFmtItem> getItemsByWiegandCount(Short wiegandCount) {
        return Optional.ofNullable(persWiegandFmtDao.findByWiegandCount(wiegandCount)).filter(Objects::nonNull)
            .map(list -> ModelUtil.copyListProperties(list, PersWiegandFmtItem.class)).orElse(new ArrayList<>());
    }

    @Override
    public boolean isExistAcc() {
        return moduleInfoService.isExistModuleByCode(BaseConstants.ACC);
    }

}