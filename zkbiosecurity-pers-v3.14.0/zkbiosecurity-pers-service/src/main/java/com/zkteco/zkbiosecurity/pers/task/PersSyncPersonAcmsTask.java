package com.zkteco.zkbiosecurity.pers.task;

import java.util.Date;
import java.util.concurrent.ScheduledFuture;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.core.utils.DateUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.scheduler.ScheduleService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

@Component
public class PersSyncPersonAcmsTask {
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private ScheduleService scheduleService;
    private ScheduledFuture<?> scedulefuture;

    public void syncPersonAcms() {
        if (scedulefuture != null) {
            scedulefuture.cancel(true);
        }
        try {
            BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("acms.sync.persInfo");
            if (baseSysParamItem != null && "1".equals(baseSysParamItem.getParamValue())) {
                // 每天凌晨0点同步人员信息
                scedulefuture = scheduleService.startScheduleTask(() -> updateAcmsPerson(), "0 0 0 * * ?");
            }
        } catch (Exception e) {
        }
    }

    private void updateAcmsPerson() {
        Date yesterday = DateUtil.getDateBefore(new Date(), 1);
        Date beginTime = DateUtil.getDayBeginTime(yesterday);
        persPersonService.updateAcmsPersonByUpdateTime(beginTime);
    }

}
