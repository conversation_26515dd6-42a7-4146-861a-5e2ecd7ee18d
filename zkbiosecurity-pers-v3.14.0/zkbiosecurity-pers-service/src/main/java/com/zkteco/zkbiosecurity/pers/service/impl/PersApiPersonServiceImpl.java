package com.zkteco.zkbiosecurity.pers.service.impl;

import java.io.File;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zkteco.zkbiosecurity.acc.service.Acc4PersPersonService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.park.service.Park4PersPersonService;
import com.zkteco.zkbiosecurity.park.vo.Park4PersPersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.ApiPersonBaseInfoItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPhotoItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.utils.PersPersonUtil;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.pid.service.Pid4PersPersonService;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryValueItem;

/**
 * 人员API ServiceImpl
 * 
 * @Auther: lambert.li
 * @Date: 2018/11/29 15:46
 */
@Service
@Transactional
public class PersApiPersonServiceImpl implements PersApiPersonService {

    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired(required = false)
    private Acc4PersPersonService acc4PersPersonService;
    @Autowired
    private PersCertificateService persCertificateService;
    @Autowired(required = false)
    private Park4PersPersonService park4PersPersonService;
    @Autowired(required = false)
    private Pid4PersPersonService pid4PersPersonService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private Pers2OtherService pers2OtherService;

    @Override
    public ApiResultMessage addApiPerson(PersApiPersonItem person) {
        if (StringUtils.isBlank(person.getPin())) {
            // 人员编号为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        // 验证pin号中是否包含特殊字符
        if (PersRegularUtil.hasSpecialChar(person.getPin())) {
            return ApiResultMessage.message(PersConstants.PERSON_PIN_NOSPECIALCHAR,
                I18nUtil.i18nCode("pers_pin_noSpecialChar"));
        }
        int maxNameLen = 25;
        if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
            maxNameLen = 25;
        }
        // 验证姓名中是否包含特殊字符
        if (StringUtils.isNotBlank(person.getName())) {
            if (person.getName().length() > maxNameLen) {
                return ApiResultMessage.message(PersConstants.PERSON_NAME_TOOLONG,
                    I18nUtil.i18nCode("pers_import_nameTooLong", person.getName()));
            }
            if (PersRegularUtil.hasSpecialChar(person.getName())) {
                return ApiResultMessage.message(PersConstants.PERSON_NAME_NOSPECIALCHAR,
                    I18nUtil.i18nCode("pers_person_noSpecialChar"));
            }
        }
        // 验证姓中是否包含特殊字符
        if (StringUtils.isNotBlank(person.getLastName())) {
            if (person.getLastName().length() > maxNameLen) {
                return ApiResultMessage.message(PersConstants.PERSON_NAME_TOOLONG,
                    I18nUtil.i18nCode("pers_import_nameTooLong", person.getLastName()));
            }
            if (PersRegularUtil.hasSpecialChar(person.getLastName())) {
                return ApiResultMessage.message(PersConstants.PERSON_NAME_NOSPECIALCHAR,
                    I18nUtil.i18nCode("pers_person_noSpecialChar"));
            }
        }
        PersPersonItem persPersonItem = persPersonService.getItemByPin(person.getPin());
        String tempPhotoPath = "";
        try {
            // 新增
            if (persPersonItem == null) {
                // 保留人员编号，校验人员离职表
                String pinRetain = persParamsService.getValByName("pers.pinRetain");
                if ("true".equals(pinRetain)) {
                    PersLeavePersonItem leaveItem = persLeavePersonService.getItemByPin(person.getPin());
                    if (leaveItem != null && !leaveItem.getId().equals(person.getLeaveId())) {
                        return ApiResultMessage.message(PersConstants.PERSON_PIN_EXIST,
                            I18nUtil.i18nCode("pers_app_pinExist"));
                    }
                }
                // 验证许可点数
                if (!persPersonService.persLicenseCheck()) {
                    return ApiResultMessage.message(PersConstants.PERSON_lICENSE_MAXCOUNT,
                        I18nUtil.i18nCode("common_license_maxCount"));
                }
                persPersonItem = new PersPersonItem();
            }

            ModelUtil.copyPropertiesIgnoreNullWithProperties(person, persPersonItem, "carPlate", "mobilePhone");
            // 人员禁用不可编辑
            if (persPersonItem.getEnabledCredential() != null && !persPersonItem.getEnabledCredential()) {
                return ApiResultMessage.message(PersConstants.PERSON_DISABLED_NOPOP,
                    I18nUtil.i18nCode("pers_person_disabledNotOp"));
            }
            if (person.getBirthday() != null) {
                persPersonItem.setBirthday(DateUtil.stringToDate(person.getBirthday(), DateUtil.DateStyle.YYYY_MM_DD));
            }
            // 设置人员数据来源
            persPersonItem.setIsFrom("API");
            // 设置人员状态
            persPersonItem.setExceptionFlag(PersConstants.PERSON_ISNOTEXCEPTION);
            AuthDepartmentItem departmentItem = null;
            if (StringUtils.isNotBlank(person.getDeptCode())) {
                departmentItem = authDepartmentService.getItemByCode(person.getDeptCode());
                if (departmentItem == null) {
                    return ApiResultMessage.message(PersConstants.DEPT_CODE_NOTEXIST,
                        I18nUtil.i18nCode("pers_import_deptNotExist"));
                }
            } else if (StringUtils.isNotBlank(persPersonItem.getDeptId())) {
                departmentItem = authDepartmentService.getItemById(persPersonItem.getDeptId());
            }

            if (departmentItem == null) {
                // 部门不存在则设置为默认部门
                departmentItem = authDepartmentService.getDefautDept();
            }
            // 设置部门信息
            persPersonItem.setDeptId(departmentItem.getId());
            persPersonItem.setDeptCode(departmentItem.getCode());
            persPersonItem.setDeptName(departmentItem.getName());
            String existCardNos = persPersonItem.getCardNos();
            String existMainCardNo = null;
            String existDeputyCardNo = null;
            if (StringUtils.isNotBlank(existCardNos)) {
                List<String> existCardNoList = StrUtil.strToList(existCardNos);
                existMainCardNo = existCardNoList.get(0);
                List<String> existDeputyCardNoList = existCardNoList.subList(1, existCardNoList.size());
                if (existDeputyCardNoList != null && existDeputyCardNoList.size() > 0) {
                    existDeputyCardNo = StringUtils.join(existDeputyCardNoList, ",");
                }
            }
            String cardNos = person.getCardNo();
            if (cardNos == null && StringUtils.isNotBlank(existMainCardNo)) {
                cardNos = existMainCardNo;
            }
            if (StringUtils.isNotBlank(person.getSupplyCards())) {
                if (StringUtils.isBlank(cardNos)) {
                    // 副卡不为空且主卡为空，返回异常
                    return ApiResultMessage.message(PersConstants.PERSON_MASTERCARD_ISNULL,
                        I18nUtil.i18nCode("pers_card_deputyCardValid"));
                }
                cardNos = cardNos + "," + person.getSupplyCards();
            }
            // 清空副卡，只有当用户设置了""空字符串时才清空，格式为"supplyCards":""
            if (person.getSupplyCards() == null && StringUtils.isNotBlank(existDeputyCardNo)) {
                cardNos = cardNos + "," + existDeputyCardNo;
            }
            // 清空卡号，只有当用户设置了""空字符串时才清空，格式为"cardNo":"" modify by seven.wu 20210913
            if ("".equals(cardNos)) {
                persPersonItem.setCardNos(",");
            }
            int ret = checkPin(persPersonItem.getPin());
            if (ret < 0) {
                return ApiResultMessage.message(ret, I18nUtil.i18nCode("pers_person_pinException"));
            }
            // 判断卡号重复
            if (StringUtils.isNotBlank(cardNos)) {
                List<String> cardNoList = StrUtil.strToList(cardNos);
                long cardNoCount = cardNoList.stream().distinct().count();
                if (cardNoCount < cardNoList.size()) {
                    return ApiResultMessage.message(ret, I18nUtil.i18nCode("pers_person_cardDuress"));
                }
                ret = persCardService.isExitCardNo(persPersonItem, cardNos);
                if (ret < 0) {
                    return ApiResultMessage.message(ret, I18nUtil.i18nCode("pers_person_cardDuress"));
                }
                persPersonItem.setCardNos(cardNos);
            }
            // 判断密码重复
            if (StringUtils.isNotBlank(persPersonItem.getPersonPwd())) {
                ret = checkPwd(persPersonItem);
                if (ret < 0) {
                    return ApiResultMessage.message(ret, I18nUtil.i18nCode("pers_person_duressPwdError"));
                }
            }
            if (StringUtils.isNotBlank(person.getMobilePhone())
                && !person.getMobilePhone().equals(persPersonItem.getMobilePhone())
                && persPersonItem.getExistsMobileUser() != null && persPersonItem.getExistsMobileUser()) {
                return ApiResultMessage.message(PersConstants.PERSON_PHONE_NOTMODIFIED,
                    I18nUtil.i18nCode("pers_person_notModified"));
            }
            persPersonItem.setMobilePhone(person.getMobilePhone());
            // 判断手机号重复
            if (StringUtils.isNotBlank(persPersonItem.getMobilePhone())) {
                PersPersonItem persItem = persPersonService.getItemByMobile(persPersonItem.getMobilePhone());
                if (persItem != null && !persItem.getPin().equals(persPersonItem.getPin())) {
                    return ApiResultMessage.message(PersConstants.PERSON_PHONE_REPEAT,
                        I18nUtil.i18nCode("pers_h5_personMobileRepeat"));
                }
                if ("zh_CN".equals(LocaleMessageSourceUtil.language)
                    && !PersRegularUtil.isValidMobilePhone(persPersonItem.getMobilePhone())) {
                    return ApiResultMessage.message(PersConstants.PERSON_PHONE_FORMATERROR,
                        I18nUtil.i18nCode("pers_import_mobilePhoneErrors"));
                }
            }
            // 判断邮箱重复
            if (StringUtils.isNotBlank(persPersonItem.getEmail())) {
                PersPersonItem persItem = persPersonService.getItemByEmail(persPersonItem.getEmail());
                if (persItem != null && !persItem.getPin().equals(persPersonItem.getPin())) {
                    return ApiResultMessage.message(PersConstants.PERSON_EMAIL_REPEAT,
                        I18nUtil.i18nCode("pers_h5_personEmailRepeat"));
                }
            }
            // 判断门禁权限有效时间段是否有效
            if (StringUtils.isNotBlank(person.getAccStartTime()) && StringUtils.isNotBlank(person.getAccEndTime())) {
                Date startDate = DateUtil.stringToDate(person.getAccStartTime());
                Date endDate = DateUtil.stringToDate(person.getAccEndTime());
                if (startDate == null || endDate == null) {
                    // 时间格式错误
                    return ApiResultMessage.message(PersConstants.API_DATE_ERROR,
                        I18nUtil.i18nCode("common_jqMsg_date"));
                }
                if (startDate.getTime() > endDate.getTime()) {
                    // 开始时间不能大于结束时间
                    return ApiResultMessage.message(PersConstants.API_DATE_STARTTIME_LARGE,
                        I18nUtil.i18nCode("common_dsTime_timeValid4"));
                }
            }
            if (StringUtils.isNotBlank(person.getAccStartTime()) && StringUtils.isBlank(person.getAccEndTime())) {
                // 开始时间和结束时间不允许存在一个为空，一个不为空的情况
                return ApiResultMessage.message(PersConstants.API_DATE_ERROR, I18nUtil.i18nCode("common_jqMsg_date"));

            } else if (StringUtils.isBlank(person.getAccStartTime())
                && StringUtils.isNotBlank(person.getAccEndTime())) {
                // 开始时间和结束时间不允许存在一个为空，一个不为空的情况
                return ApiResultMessage.message(PersConstants.API_DATE_ERROR, I18nUtil.i18nCode("common_jqMsg_date"));
            }
            // 车牌校验
            if (Objects.nonNull(park4PersPersonService)) {
                ApiResultMessage apiResultMessage = checkPersonPlate(person);
                if (Objects.nonNull(apiResultMessage)) {
                    return apiResultMessage;
                }
            }

            Map<String, String> extParam = buildExtParamMap(person, persPersonItem);

            // 设置入职日期
            if (person.getHireDate() != null) {
                persPersonItem.setHireDate(person.getHireDate());
            }

            // 设置身份证相关信息 add by bob.liu 20190614
            PersCertificateItem persCertificateItem = null;
            if (StringUtils.isNotBlank(person.getCertNumber())) {
                persCertificateItem = new PersCertificateItem();
                boolean exist = persCertificateService.isExistByCertNumberAndCertTypeAndPersonIdNe(
                    person.getCertNumber(), person.getCertType(), persPersonItem.getId());
                if (!exist) {
                    return ApiResultMessage.message(PersConstants.PERSON_CERTNUM_EXIST,
                        I18nUtil.i18nCode("pers_cert_numberExist"));
                }
                persCertificateItem.setCertType(person.getCertType());
                persCertificateItem.setCertNumber(person.getCertNumber());
                if (PersConstants.CERT_SECOND_ID.equals(person.getCertType())) {
                    if (StringUtils.isNotBlank(person.getCertNumber())
                        && !PersRegularUtil.idNumberCheck(person.getCertNumber())) {
                        return ApiResultMessage.message(PersConstants.PERSON_IDNUMBER_ERROR,
                            I18nUtil.i18nCode("pers_person_idCardValidate"));
                    }
                    persPersonItem.setIdCard(person.getCertNumber());
                }
            }
            if (StringUtils.isNotBlank(persPersonItem.getGender())) {
                // 性别的国际化，M男F女
                if (!("M".equals(persPersonItem.getGender()) || "F".equals(persPersonItem.getGender())
                    || "U".equals(persPersonItem.getGender()))) {
                    persPersonItem.setGender("");
                }
            }
            // 判断图片有效性
            if (StringUtils.isNotBlank(person.getPersonPhoto())) {
                if (!PersPersonUtil.isImage(person.getPersonPhoto())) {
                    // 不是图片
                    return ApiResultMessage.message(PersConstants.PERSON_PHOTO_INVALID,
                        I18nUtil.i18nCode("pers_api_selectPhotoInvalid"));
                }
                String oldPhotoPath = persPersonItem.getPhotoPath();
                if (StringUtils.isNotBlank(oldPhotoPath)) {
                    String photoBase64 = FileEncryptUtil.getDecryptFileBase64(oldPhotoPath);
                    String filePath = FileUtil.createUploadFileRootPath("pers", "user/avatar");
                    String photoName = System.currentTimeMillis() + ".jpg";
                    FileUtil.saveFile(filePath, photoName, photoBase64, false);
                    tempPhotoPath = '/' + filePath + photoName;
                }
                String photoPath = FileUtil.saveFileToServer("pers", "user/avatar", persPersonItem.getPin() + ".jpg",
                    person.getPersonPhoto());
                persPersonItem.setPhotoPath(photoPath);
            } else {
                persPersonItem.setPhotoPath(null);
            }
            if (StringUtils.isNotBlank(persPersonItem.getPhotoPath())) {
                ZKResultMsg zkResultMsg = persPersonService.validCropFace(persPersonItem.getPhotoPath());
                if (!zkResultMsg.isSuccess()) {
                    return ApiResultMessage.message(PersConstants.PERSON_CROPFACE_FAILE, zkResultMsg.getMsg());
                }
            }
            persPersonService.saveItem(persPersonItem, persCertificateItem, new PersAttributeExtItem(), extParam);
        } catch (Exception e) {
            // 人员修改信息失败，头像会被替换
            if (StringUtils.isNotBlank(tempPhotoPath)) {
                String photoBase64 = FileEncryptUtil.getDecryptFileBase64(tempPhotoPath);
                FileUtil.saveFileToServer("pers", "user/avatar", persPersonItem.getPin() + ".jpg", photoBase64);
            }
            if (e instanceof ZKBusinessException) {
                throw new ZKBusinessException(e.getMessage());
            }
        } finally {
            if (StringUtils.isNotBlank(tempPhotoPath)) {
                File tempFile = new File(FileUtil.getLocalFullPath(tempPhotoPath));
                if (tempFile.exists()) {
                    // 删除临时图片
                    tempFile.delete();
                }
            }
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage getApiPersonByPin(String pin) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem == null) {
            // 人员不存在
            return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        return ApiResultMessage.successMessage(buildApiPerson(persPersonItem));
    }

    @Override
    public int checkPin(String pin) {
        String pinLen = persParamsService.getValByName("pers.pinLen");
        String supportLetter = persParamsService.getValByName("pers.pinSupportLetter");
        boolean pinSupportLetter = false;
        String regex = "^[0-9]+$";
        if ("true".equals(supportLetter)) {
            regex = "^[a-z0-9A-Z]+$";
            pinSupportLetter = true;
        }
        if ((Integer.parseInt(pinLen) < pin.length()) || (!pin.matches(regex))
            || (pin.startsWith("0") && !pinSupportLetter)
            || ((9 == pin.length()) && (pin.startsWith("8") || pin.startsWith("9")))) {
            // 人员编号异常
            return PersConstants.PERSON_PIN_ERROR;
        }
        return PersConstants.OP_SUCCESS;
    }

    @Override
    public int checkPwd(PersPersonItem persPersonItem) {
        boolean isExistPwd = false;
        if (StringUtils.isNotBlank(persPersonItem.getId())) {
            // 编辑时判断需要过滤人员ID
            isExistPwd = persPersonService.checkPwd(persPersonItem.getId(), persPersonItem.getPersonPwd());
            if (isExistPwd) {
                return PersConstants.PERSON_PWD_HASUSE;
            }
        } else {
            isExistPwd = persPersonService.ckeckPwd(persPersonItem.getPersonPwd());
            if (isExistPwd) {
                return PersConstants.PERSON_PWD_HASUSE;
            }
        }

        if (persPersonService.checkForcePwd(persPersonItem.getPersonPwd())) {
            return PersConstants.PERSON_PWD_HASUSE;
        }
        return PersConstants.OP_SUCCESS;
    }

    /**
     * @Description: 人员车牌校验
     *
     * @param person:
     * @return: com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date: 2023/3/31 16:39
     * @since: 1.0.0
     */
    private ApiResultMessage checkPersonPlate(PersApiPersonItem person) {
        if (StringUtils.isNotBlank(person.getCarPlate())) {
            // 车牌不能超过6个
            List<String> plateList = StrUtil.strToList(person.getCarPlate());
            List<String> newPlateList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(plateList)) {
                if (plateList.size() > 6) {
                    return ApiResultMessage.message(PersConstants.PERSON_CARPLATE_OVERSIZE,
                        I18nUtil.i18nCode("pers_import_personPlateMax"));
                }
                boolean plateFormat = false;
                List<String> dictValueList = null;
                if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
                    List<BaseDictionaryValueItem> provinceList =
                        baseDictionaryValueService.getDictionaryValues("parkProvince");
                    dictValueList = (List<String>)CollectionUtil.getPropertyList(provinceList,
                        BaseDictionaryValueItem::getDictValue, "-1");
                }
                for (String plate : plateList) {
                    if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
                        if (plate.contains("_")) {
                            String carArea = plate.split("_")[0];
                            String carNumber = plate.replace("_", "");
                            if (carNumber.length() > 8 || !dictValueList.contains(carArea)) {
                                plateFormat = true;
                                break;
                            }
                            newPlateList.add(carNumber);
                        }
                    } else {
                        if (plate.length() > 20) {
                            plateFormat = true;
                            break;
                        }
                        newPlateList.add(plate);
                    }
                }
                if (plateFormat) {
                    return ApiResultMessage.message(PersConstants.PERSON_CARPLATE_FORMAT,
                        I18nUtil.i18nCode("pers_import_personPlateFormat", person.getCarPlate()));
                }
                if (Objects.nonNull(park4PersPersonService) && CollectionUtils.isNotEmpty(newPlateList)) {
                    List<Park4PersPersonItem> existsCarPlateList =
                        park4PersPersonService.getParkCarNumberIn(newPlateList);
                    if (CollectionUtils.isNotEmpty(existsCarPlateList)) {
                        for (Park4PersPersonItem personItem : existsCarPlateList) {
                            if (!personItem.getPersonPin().equals(person.getPin())) {
                                return ApiResultMessage.message(PersConstants.PERSON_CARPLATE_EXIST,
                                    I18nUtil.i18nCode("pers_import_personPlateRepeat", person.getCarPlate()));
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    /**
     * 组装人员扩展数据
     * 
     * @auther lambert.li
     * @date 2018/11/8 17:54
     * @param
     * @return
     */
    private Map<String, String> buildExtParamMap(PersApiPersonItem apiPerson, PersPersonItem persPersonItem) {
        Map<String, String> extParams = Maps.newHashMap();
        boolean isExistAcc = true;
        if (Objects.isNull(apiPerson.getAccStartTime()) && Objects.isNull(apiPerson.getAccEndTime())
            && Objects.isNull(apiPerson.getAccLevelIds()) && Objects.isNull(apiPerson.getIsDisabled())) {
            isExistAcc = false;
        }
        // 编辑
        if (Objects.nonNull(acc4PersPersonService) && isExistAcc) {
            // 没有字段则是不对开始时间和结束时间做处理
            extParams.put("acc.isSetValidTime", "false");
            if (StringUtils.isNotBlank(apiPerson.getAccEndTime())
                && StringUtils.isNotBlank(apiPerson.getAccStartTime())) {
                extParams.put("acc.startTime", apiPerson.getAccStartTime());
                extParams.put("acc.endTime", apiPerson.getAccEndTime());
                extParams.put("acc.isSetValidTime", "true");
            }
            if (!Objects.isNull(apiPerson.getAccLevelIds())) {
                extParams.put("acc.personLevelIds", apiPerson.getAccLevelIds());
            }

            if (!Objects.isNull(apiPerson.getIsDisabled())) {
                extParams.put("acc.disabled",
                    apiPerson.getIsDisabled() != null ? apiPerson.getIsDisabled().toString() : "false");
            }
            extParams.put("moduleAuth", "acc");
        }
        if (Objects.nonNull(park4PersPersonService)) {
            if (StringUtils.isNotBlank(apiPerson.getCarPlate())) {
                // 更新车牌信息
                extParams.put("moduleAuth", getModuleAuthByModuleName("park", extParams.get("moduleAuth")));
                String carAreas = "";
                String carNumbers = "";
                if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
                    for (String carPlate : StrUtil.strToList(apiPerson.getCarPlate())) {
                        if (carPlate.split("_").length > 1) {
                            carAreas =
                                (StringUtils.isNotBlank(carAreas) ? carAreas + "," : "") + carPlate.split("_")[0];
                            carNumbers =
                                (StringUtils.isNotBlank(carNumbers) ? carNumbers + "," : "") + carPlate.split("_")[1];
                        }
                    }
                    extParams.put("park.carAreas", carAreas);
                    extParams.put("park.carNumbers", carNumbers);
                } else if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
                    extParams.put("park.carNumbers", apiPerson.getCarPlate());
                }
            } else {
                // 车牌=""删除该人员车牌，否则不对车牌处理
                if (StringUtils.isNotBlank(persPersonItem.getId()) && Objects.nonNull(apiPerson.getCarPlate())
                    && "".equals(apiPerson.getCarPlate().trim())) {
                    extParams.put("moduleAuth", getModuleAuthByModuleName("park", extParams.get("moduleAuth")));
                }
            }
        }
        if (StringUtils.isNotBlank(apiPerson.getLeaveId())) {
            extParams.put("leaveId", apiPerson.getLeaveId());
        }
        return extParams;
    }

    /**
     * 设置模块权限
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-03-20 9:41
     * @param moduleName
     * @param moduleAuth
     * @return java.lang.String
     */
    private String getModuleAuthByModuleName(String moduleName, String moduleAuth) {
        if (StringUtils.isNotBlank(moduleAuth)) {
            moduleAuth = moduleAuth + "," + moduleName;
        } else {
            moduleAuth = moduleName;
        }
        return moduleAuth;
    }

    @Override
    public ApiResultMessage getQrCodeByPin(String pin) {
        PersPersonItem persPersonItem = persPersonService.getItemByPin(pin);
        if (persPersonItem == null) {
            // 人员不存在
            return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                I18nUtil.i18nCode("pers_api_personNotExist"));
        }
        String cardNo = "";
        if (persPersonItem != null) {
            PersCardItem cardItem = persCardService.getMasterCardByPersonId(persPersonItem.getId());
            if (cardItem != null) {
                String cardHex = baseSysParamService.getValByName("pers.cardHex");
                cardNo = cardItem.getCardNo();
                if (StringUtils.isNotBlank(cardNo)) {
                    if (cardHex.equals(PersConstants.CARD_HEXADECIMAL)) {
                        cardNo = new BigInteger(cardNo, 16).toString(10);
                    }
                }
            }
        }

        // 动态二维码
        StringBuffer content = new StringBuffer();
        content.append(pin + "\t");
        if (StringUtils.isNotBlank(cardNo)) {
            content.append(cardNo);
        }
        // 获取当前零时区日期
        Calendar createCalendar = Calendar.getInstance(TimeZone.getTimeZone("UTC+0"));
        // 二维码创建时间 UNIX格式时间戳
        String createTime = String.valueOf(createCalendar.getTimeInMillis() / 1000L);
        // 二维码有效时长
        String validTime = baseSysParamService.getValByName("qrcode.validTime");
        if (StringUtils.isBlank(validTime)) {
            validTime = "30";
        }
        content.append("\t" + createTime + "\t" + validTime + "\t");
        String qrCode = baseSysParamService.createEncryptQrcode(content.toString());
        return ApiResultMessage.successMessage(qrCode);
    }

    @Override
    public ApiResultMessage leaveApiPerson(PersApiLeavePersonItem persApiLeavePersonItem) {
        String leaveType = persApiLeavePersonItem.getLeaveType();
        String leaveTypeStr = baseDictionaryValueService.getDictionaryValuesMap("PersLeaveType").get(leaveType);
        if (StringUtils.isBlank(leaveTypeStr)) {
            return ApiResultMessage.message(PersConstants.PERSON_LEAVETYPE_NOTEXIST,
                I18nUtil.i18nCode("pers_dimission_leaveType_noExist"));
        }
        PersPersonItem personItem = persPersonService.getItemByPin(persApiLeavePersonItem.getPin());
        if (personItem != null) {
            PersLeavePersonItem item = ModelUtil.copyProperties(persApiLeavePersonItem, new PersLeavePersonItem());
            item.setLeaveType(Integer.parseInt(persApiLeavePersonItem.getLeaveType()));
            persLeavePersonService.batchLeave(item, personItem.getId());
        } else {
            // 编辑离职人员
            PersLeavePersonItem item = persLeavePersonService.getItemByPin(persApiLeavePersonItem.getPin());
            if (item != null) {
                item = ModelUtil.copyProperties(persApiLeavePersonItem, item);
                item.setLeaveType(Integer.parseInt(persApiLeavePersonItem.getLeaveType()));
                persLeavePersonService.saveItem(item);
            } else {
                // 人员不存在
                return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                    I18nUtil.i18nCode("pers_api_personNotExist"));
            }
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage addPersonnelBasicInfo(ApiPersonBaseInfoItem person) {
        return addApiPerson(ModelUtil.copyPropertiesIgnoreNull(person, new PersApiPersonItem()));
    }

    /**
     * 校验人员信息
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2021-11-17 13:57
     * @since 1.0.0
     */
    public ApiResultMessage checkPersonInfo(PersPersonItem person) {
        if (StringUtils.isBlank(person.getPin())) {
            // 人员编号为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        // 验证pin号中是否包含特殊字符
        if (PersRegularUtil.hasSpecialChar(person.getPin())) {
            return ApiResultMessage.message(PersConstants.PERSON_PIN_NOSPECIALCHAR,
                I18nUtil.i18nCode("pers_pin_noSpecialChar"));
        }
        // 验证姓名中是否包含特殊字符
        if (StringUtils.isNotBlank(person.getName())) {
            if (PersRegularUtil.hasSpecialChar(person.getName())) {
                return ApiResultMessage.message(PersConstants.PERSON_NAME_NOSPECIALCHAR,
                    I18nUtil.i18nCode("pers_person_noSpecialChar"));
            }
        }
        int ret = checkPin(person.getPin());
        if (ret < 0) {
            return ApiResultMessage.message(ret, I18nUtil.i18nCode("pers_person_pinException"));
        }
        // 判断密码重复
        if (StringUtils.isNotBlank(person.getPersonPwd())) {
            ret = checkPwd(person);
            if (ret < 0) {
                return ApiResultMessage.message(ret, I18nUtil.i18nCode("pers_person_duressPwdError"));
            }
        }
        // 判断手机号重复
        if (StringUtils.isNotBlank(person.getMobilePhone())) {
            PersPersonItem persItem = persPersonService.getItemByMobile(person.getMobilePhone());
            if (persItem != null && !persItem.getPin().equals(person.getPin())) {
                return ApiResultMessage.message(ret, I18nUtil.i18nCode("pers_h5_personMobileRepeat"));
            }
        }
        if (person.getEnabledCredential() != null && !person.getEnabledCredential()) {
            return ApiResultMessage.message(PersConstants.PERSON_DISABLED_NOPOP,
                I18nUtil.i18nCode("pers_person_disabledNotOp"));
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ApiResultMessage updatePersonnelPhoto(PersApiPhotoItem person) {
        if (StringUtils.isBlank(person.getPin())) {
            // 人员编号为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        } else {
            PersPersonItem persPersonItem = persPersonService.getItemByPin(person.getPin());
            if (persPersonItem == null) {
                // 人员不存在
                return ApiResultMessage.message(PersConstants.PERSONID_NOTEXIST,
                    I18nUtil.i18nCode("pers_api_personNotExist"));
            }
            // 人员禁用不可编辑
            if (persPersonItem.getEnabledCredential() != null && !persPersonItem.getEnabledCredential()) {
                return ApiResultMessage.message(PersConstants.PERSON_DISABLED_NOPOP,
                    I18nUtil.i18nCode("pers_person_disabledNotOp"));
            }
            // 判断图片有效性
            if (StringUtils.isNotBlank(person.getPersonPhoto())) {
                if (!PersPersonUtil.isImage(person.getPersonPhoto())) {
                    // 不是图片
                    return ApiResultMessage.message(PersConstants.PERSON_PHOTO_INVALID,
                        I18nUtil.i18nCode("pers_api_selectPhotoInvalid"));
                }
                String photoPath = FileUtil.saveFileToServer("pers", "user/avatar", persPersonItem.getPin() + ".jpg",
                    person.getPersonPhoto());
                persPersonItem.setPhotoPath(photoPath);
            } else {
                persPersonItem.setPhotoPath(null);
            }
            if (StringUtils.isNotBlank(persPersonItem.getPhotoPath())) {
                return persPersonService.updateUserPhoto(persPersonItem, person.getPersonPhoto());
            }
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public List<PersApiPersonItem> getApiPersonList(String pins, String deptCodes, int pageNo, int pageSize) {
        Pager pager = getApiPersonByPage(pins, deptCodes, pageNo, pageSize);
        return (List<PersApiPersonItem>)pager.getData();
    }

    @Override
    public Pager getApiPersonByPage(String pins, String deptCodes, int pageNo, Integer pageSize) {
        PersPersonItem persPersonItem = new PersPersonItem();
        if (StringUtils.isNotBlank(pins)) {
            persPersonItem.setInPin(pins);
        }
        if (StringUtils.isNotBlank(deptCodes)) {
            persPersonItem.setInDeptCode(deptCodes);
        }
        return getApiPersonByPage(persPersonItem, pageNo, pageSize);
    }

    @Override
    public Pager getApiPersonByPage(PersPersonItem persPersonItem, int pageNo, Integer pageSize) {
        Pager pager = new Pager();
        if (persPersonItem != null) {
            pager = persPersonService.getItemsByPage(persPersonItem, pageNo - 1, pageSize);
            List<PersPersonItem> persPersonItemList = (List<PersPersonItem>)pager.getData();
            List<PersApiPersonItem> apiPersonItemList = Lists.newArrayList();
            if (!persPersonItemList.isEmpty()) {
                persPersonItemList.forEach(person -> apiPersonItemList.add(buildApiPerson(person)));
            }
            pager.setData(apiPersonItemList);
        }
        return pager;
    }

    /**
     * 组装人员数据
     * 
     * @param persPersonItem:
     * @return com.zkteco.zkbiosecurity.pers.api.vo.PersApiPersonItem
     * <AUTHOR>
     * @throws @date 2021-11-24 16:07
     * @since 1.0.0
     */
    private PersApiPersonItem buildApiPerson(PersPersonItem persPersonItem) {
        PersApiPersonItem apiPersonItem = new PersApiPersonItem();
        ModelUtil.copyPropertiesIgnoreNull(persPersonItem, apiPersonItem);
        if (StringUtils.isNotBlank(persPersonItem.getPhotoPath())) {
            // 转换人员照片数据
            // 图片解密
            String base64Str = FileEncryptUtil.getDecryptFileBase64(persPersonItem.getPhotoPath());
            apiPersonItem.setPersonPhoto(base64Str);
            apiPersonItem.setPhotoPath(persPersonItem.getPhotoPath());
        }
        String visilightPhotoPath = FileUtil.getCropFacePath(persPersonItem.getPin());
        File visilightPhotoFile = new File(FileUtil.getLocalFullPath(visilightPhotoPath));
        File[] visilightFiles = visilightPhotoFile.listFiles();// 获取目录下的所有文件或文件夹
        if (visilightFiles != null && visilightFiles.length > 0) {
            // 遍历，目录下的所有文件
            for (File f : visilightFiles) {
                if (f.isFile()) {
                    apiPersonItem.setVislightPhotoPath(
                        (File.separator + visilightPhotoPath + File.separator + f.getName()).replace("\\", "/"));
                    // 转换人员照片数据
                    String base64Str = FileEncryptUtil.getDecryptFileBase64(apiPersonItem.getVislightPhotoPath());
                    apiPersonItem.setVislightPhoto(base64Str);
                    break;
                }
            }
        }
        if (persPersonItem.getBirthday() != null) {
            // 设置人员出生日期
            apiPersonItem
                .setBirthday(DateUtil.dateToString(persPersonItem.getBirthday(), DateUtil.DateStyle.YYYY_MM_DD));
        }
        if (StringUtils.isNotBlank(persPersonItem.getCardNos())) {
            List<String> cardNoList =
                Arrays.asList(StringUtils.split(persPersonItem.getCardNos(), PersConstants.IMPORT_CARD_SPLIT)).stream()
                    .distinct().collect(Collectors.toList());
            // 设置主卡卡号
            apiPersonItem.setCardNo(cardNoList.get(0));
            // 存在多卡数据
            if (cardNoList.size() > 1) {
                cardNoList.remove(0);
                apiPersonItem.setSupplyCards(StringUtils.join(cardNoList, ","));
            }
        }
        PersCertificateItem persCertificateItem = persCertificateService.getItemByPersonId(persPersonItem.getId());
        if (persCertificateItem != null) {
            apiPersonItem.setCertType(persCertificateItem.getCertType());
            apiPersonItem.setCertNumber(persCertificateItem.getCertNumber());
        }
        if (Objects.nonNull(acc4PersPersonService)) {
            Map<String, String> extParams = acc4PersPersonService.getAccPersonExtParam(persPersonItem.getId());
            // 设置门禁有效时间
            if (extParams.containsKey("acc.startTime") && extParams.containsKey("acc.endTime")) {
                apiPersonItem.setAccStartTime(extParams.get("acc.startTime"));
                apiPersonItem.setAccEndTime(extParams.get("acc.endTime"));
            }
            // 设置门禁权限组ID
            if (extParams.containsKey("acc.personLevelIds")) {
                apiPersonItem.setAccLevelIds(extParams.get("acc.personLevelIds"));
            }
            if (extParams.containsKey("acc.disabled")) {
                apiPersonItem.setIsDisabled(Boolean.parseBoolean(extParams.get("acc.disabled")));
            }
        }
        if (Objects.nonNull(park4PersPersonService)) {
            List<Park4PersPersonItem> park4PersPersonItemList =
                park4PersPersonService.findbyPersPersonPinIn(StrUtil.strToList(persPersonItem.getPin()));
            if (park4PersPersonItemList != null && park4PersPersonItemList.size() > 0) {
                String carPlates =
                    CollectionUtil.getPropertys(park4PersPersonItemList, Park4PersPersonItem::getParkCarNumbers);
                apiPersonItem.setCarPlate(carPlates);
            }
        }
        return apiPersonItem;
    }

    @Override
    public ApiResultMessage reinstatedApiPerson(PersApiPersonItem person) {
        if (StringUtils.isBlank(person.getPin())) {
            // 人员编号为空
            return ApiResultMessage.message(PersConstants.PERSONPIN_ISNULL,
                I18nUtil.i18nCode("pers_import_pinNotEmpty"));
        }
        PersLeavePersonItem leaveItem = persLeavePersonService.getItemByPin(person.getPin());
        if (Objects.isNull(leaveItem)) {
            return ApiResultMessage.message(PersConstants.PERSON_LEAVEPIN_ISNULL,
                I18nUtil.i18nCode("pers_dimission_person_noExist"));
        }
        person.setLeaveId(leaveItem.getId());
        if (person.getName() == null) {
            person.setName(leaveItem.getName());
        }
        if (person.getLastName() == null) {
            person.setLastName(leaveItem.getLastName());
        }
        if (person.getDeptCode() == null) {
            person.setDeptCode(leaveItem.getDeptCode());
        }
        return addApiPerson(person);
    }
}
