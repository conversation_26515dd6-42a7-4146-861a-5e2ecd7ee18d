/**
 * File Name: PersAttributeExt
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import com.zkteco.zkbiosecurity.core.model.BaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 对应百傲瑞达实体 PersAttributeExt
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_ATTRIBUTE_EXT", indexes = {
        @Index(name = "PERS_ATTR_EXT_CREATE_TIME_IDX", columnList = "CREATE_TIME")
})
@Getter
@Setter
@Accessors(chain = true)
public class PersAttributeExt extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @Column(name = "PERSON_ID", unique = true)
    private String personId;

    /**
     * 值1
     */
    @Column(name = "ATTR_VALUE1")
    private String attrValue1;

    /**
     * 值2
     */
    @Column(name = "ATTR_VALUE2")
    private String attrValue2;

    /**
     * 值3
     */
    @Column(name = "ATTR_VALUE3")
    private String attrValue3;

    /**
     * 值4
     */
    @Column(name = "ATTR_VALUE4")
    private String attrValue4;

    /**
     * 值5
     */
    @Column(name = "ATTR_VALUE5")
    private String attrValue5;

    /**
     * 值6
     */
    @Column(name = "ATTR_VALUE6")
    private String attrValue6;

    /**
     * 值7
     */
    @Column(name = "ATTR_VALUE7")
    private String attrValue7;

    /**
     * 值8
     */
    @Column(name = "ATTR_VALUE8")
    private String attrValue8;

    /**
     * 值9
     */
    @Column(name = "ATTR_VALUE9")
    private String attrValue9;

    /**
     * 值10
     */
    @Column(name = "ATTR_VALUE10")
    private String attrValue10;

    /**
     * 值11
     */
    @Column(name = "ATTR_VALUE11")
    private String attrValue11;

    /**
     * 值12
     */
    @Column(name = "ATTR_VALUE12")
    private String attrValue12;

    /**
     * 值13
     */
    @Column(name = "ATTR_VALUE13")
    private String attrValue13;

    /**
     * 值14
     */
    @Column(name = "ATTR_VALUE14")
    private String attrValue14;

    /**
     * 值15
     */
    @Column(name = "ATTR_VALUE15")
    private String attrValue15;

    /**
     * 值16
     */
    @Column(name = "ATTR_VALUE16")
    private String attrValue16;

    /**
     * 值17
     */
    @Column(name = "ATTR_VALUE17")
    private String attrValue17;

    /**
     * 值18
     */
    @Column(name = "ATTR_VALUE18")
    private String attrValue18;

    /**
     * 值19
     */
    @Column(name = "ATTR_VALUE19")
    private String attrValue19;

    /**
     * 值20
     */
    @Column(name = "ATTR_VALUE20")
    private String attrValue20;

    /**
     * 值21
     */
    @Column(name = "ATTR_VALUE21")
    private String attrValue21;

    /**
     * 值22
     */
    @Column(name = "ATTR_VALUE22")
    private String attrValue22;

    /**
     * 值23
     */
    @Column(name = "ATTR_VALUE23")
    private String attrValue23;

    /**
     * 值24
     */
    @Column(name = "ATTR_VALUE24")
    private String attrValue24;

    /**
     * 值25
     */
    @Column(name = "ATTR_VALUE25")
    private String attrValue25;

    /**
     * 值26
     */
    @Column(name = "ATTR_VALUE26")
    private String attrValue26;

    /**
     * 值27
     */
    @Column(name = "ATTR_VALUE27")
    private String attrValue27;

    /**
     * 值28
     */
    @Column(name = "ATTR_VALUE28")
    private String attrValue28;

    /**
     * 值29
     */
    @Column(name = "ATTR_VALUE29")
    private String attrValue29;

    /**
     * 值30
     */
    @Column(name = "ATTR_VALUE30")
    private String attrValue30;

    /**
     * 值31
     */
    @Column(name = "ATTR_VALUE31")
    private String attrValue31;

    /**
     * 值32
     */
    @Column(name = "ATTR_VALUE32")
    private String attrValue32;
}