/**
 * File Name: PersCertificate
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersCertificate;
import org.springframework.data.jpa.repository.Query;

import java.util.Collection;
import java.util.List;

/**
 * 对应百傲瑞达 PersCertificateDao
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersCertificateDao extends BaseDao<PersCertificate, String> {
    /**
     * 根据人员ID查找证件
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 21:17
     * @param personId
     * @return com.zkteco.zkbiosecurity.pers.model.PersCertificate
     */
    @Query(value = "SELECT t FROM PersCertificate t WHERE t.persPerson.id in ?1")
    PersCertificate findByPersonId(String personId);

    PersCertificate findByCertTypeAndCertNumberAndPersPerson_PinNot(String CertType, String CertNumber, String personPin);

    List<PersCertificate> findByPersPerson_PinIn(Collection<String> pins);

    List<PersCertificate> findByPersPerson_IdInAndCertNumberNotNullAndCertTypeNotNull(Collection<String> ids);

    List<PersCertificate> findByIdIn(Collection<String> ids);

    List<PersCertificate> findByPersPerson_IdIn(Collection<String> ids);

    boolean existsByCertType(String certType);
}