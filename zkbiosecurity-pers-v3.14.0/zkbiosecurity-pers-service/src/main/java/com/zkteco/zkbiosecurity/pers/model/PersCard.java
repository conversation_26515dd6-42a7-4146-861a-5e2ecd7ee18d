/**
 * File Name: PersCard Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 PersCard
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_CARD",
    indexes = {@Index(name = "PERS_CARD_PERSON_ID_IDX", columnList = "PERSON_ID"),
        @Index(name = "PERS_CARD_PERSON_PIN_IDX", columnList = "PERSON_PIN"),
        @Index(name = "PERS_CARD_NO_IDX", columnList = "CARD_NO"),
        @Index(name = "PERS_LOGICAL_CARD_NO_IDX", columnList = "LOGICAL_CARD_NO"),
        @Index(name = "PERS_CARD_CREATE_TIME_IDX", columnList = "CREATE_TIME"),
        @Index(name = "PERS_CARD_UPDATE_TIME_IDX", columnList = "UPDATE_TIME")})
@Getter
@Setter
@Accessors(chain = true)
public class PersCard extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 人员id
     */
    @Column(name = "PERSON_ID", length = 50, nullable = false)
    private String personId;
    /**
     * 人员编号
     */
    @Column(name = "PERSON_PIN", length = 50, nullable = false)
    private String personPin;

    /**
     * 卡类型 主卡：0;副卡：1
     */
    @Column(name = "CARD_TYPE", nullable = false)
    private Short cardType;

    /**
     * 卡操作类型;时间卡,授权卡等 ACMS可回收卡100，不可回收101
     */
    @Column(name = "CARD_OP_TYPE")
    private Short cardOpType;

    /**
     * 卡状态
     */
    @Column(name = "CARD_STATE", nullable = false)
    private Short cardState;

    /**
     * 卡号
     */
    @Column(name = "CARD_NO", length = 250, nullable = false)
    @Convert(converter = EncryptConverter.class)
    private String cardNo;

    /**
     * 发卡时间
     */
    @Column(name = "ISSUE_TIME", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date issueTime;

    /**
     * 卡有效期的开始时间
     */
    @Column(name = "START_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date startTime;

    /**
     * 卡有效期的截止时间
     */
    @Column(name = "END_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date endTime;

    /**
     * 逻辑卡号
     */
    @Column(name = "LOGICAL_CARD_NO", length = 50)
    private String logicalCardNo;

    /**
     * 卡号来源
     */
    @Column(name = "IS_FROM", length = 100, updatable = false)
    private String isFrom;

    /** 业务id */
    @Column(name = "BUSSINESS_ID")
    private String bussinessId;
}