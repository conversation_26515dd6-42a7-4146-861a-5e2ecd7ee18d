/**
 * File Name: PersPersonServiceImpl Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.math.BigInteger;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.imageio.ImageIO;

import org.apache.commons.beanutils.BeanMap;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.ai.center.service.Other2AiCenterService;
import com.zkteco.zkbiosecurity.auth.service.AuthAreaService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthSecurityParamsService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthAreaItem;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.config.DataSourceConfig;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.license.provider.BaseLicenseProvider;
import com.zkteco.zkbiosecurity.license.vo.bean.ResultCode;
import com.zkteco.zkbiosecurity.park.service.Park4PersPersonService;
import com.zkteco.zkbiosecurity.park.vo.Park4PersPersonItem;
import com.zkteco.zkbiosecurity.pers.cache.PersCacheManager;
import com.zkteco.zkbiosecurity.pers.common.ZKPageResultMsg;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.*;
import com.zkteco.zkbiosecurity.pers.model.*;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.utils.FileUnZipUtil;
import com.zkteco.zkbiosecurity.pers.utils.PersPersonUtil;
import com.zkteco.zkbiosecurity.pers.utils.ProcessBeanUtil;
import com.zkteco.zkbiosecurity.pers.utils.RotateImageUtil;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.pos.service.Pos4PersCardService;
import com.zkteco.zkbiosecurity.security.SessionManager;
import com.zkteco.zkbiosecurity.system.app.constants.BaseAppConstants;
import com.zkteco.zkbiosecurity.system.constants.BaseDataConstants;
import com.zkteco.zkbiosecurity.system.service.*;
import com.zkteco.zkbiosecurity.system.vo.BaseDictionaryValueItem;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;
import com.zkteco.zkbiosecurity.vdb.service.Vdb4PersPersonService;
import com.zkteco.zkbiosecurity.vdb.vo.Vdb4PersPersonItem;
import com.zkteco.zkbiosecurity.vdb.vo.VdbBuilding2otherItem;
import com.zkteco.zkbiosecurity.vdb.vo.VdbUnit2otherItem;

/**
 * 对应百傲瑞达 PersPersonServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Service
@Transactional
public class PersPersonServiceImpl implements PersPersonService {
    private final static Logger logger = LoggerFactory.getLogger(PersPersonServiceImpl.class);
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersLeavePersonDao persLeavePersonDao;
    @Autowired
    private PersCardDao persCardDao;
    @Autowired
    private PersPositionDao persPositionDao;
    @Autowired
    private PersBioTemplateDao persBioTemplateDao;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private PersAttributeExtService persAttributeExtService;
    @Autowired
    private PersCardService persCardService;
    @Autowired
    private PersBioTemplateService persBioTemplateService;
    @Autowired
    private PersCertificateService persCertificateService;
    @Autowired
    private PersPersonChangeService persPersonChangeService;
    @Autowired
    private PersLeavePersonService persLeavePersonService;
    @Autowired
    private PersPersonLinkService persPersonLinkService;
    @Autowired
    private BaseMailService baseMailService;
    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private PersCacheManager persCacheManager;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private AuthAreaService authAreaService;
    @Autowired
    private Pers2OtherService pers2OtherService;
    @Autowired
    private BaseLicenseProvider baseLicenseProvider;
    @Autowired
    private BaseCropFaceService baseCropFaceService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private PersParamsService persParamsService;
    @Autowired
    private PersAttributeDao persAttributeDao;
    @Autowired(required = false)
    private Park4PersPersonService park4PersPersonService;
    @Autowired
    private PersAttributeExtDao persAttributeExtDao;
    @Autowired
    private SystemPersInfo2CloudService pers2CloudService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private AuthSecurityParamsService authSecurityParamsService;
    @Autowired
    private SessionManager sessionManager;
    @Autowired(required = false)
    private Pers2ProService pers2ProService;
    /**
     * 是否开启数据加密 默认不开启
     */
    @Value("${system.data.security-encrypt:false}")
    private boolean isOpenSecurityEncrypt;
    @Autowired
    private PersPersonnallistPersonDao persPersonnallistPersonDao;
    @Autowired
    private PersPersonnalListService persPersonnalListService;
    @Autowired(required = false)
    private Other2AiCenterService other2AiCenterService;
    @Autowired(required = false)
    private Pos4PersCardService pos4PersCardService;
    @Autowired(required = false)
    private PersPersonExtService[] persPersonExtServices;
    @Autowired
    private PersBioPhotoDao persBioPhotoDao;
    @Autowired
    private PersBioPhotoService persBioPhotoService;
    @Value("${system.securityLevel:0}")
    private int securityLevel;
    @Autowired
    private PersPersonnallistPersonService persPersonnallistPersonService;
    @Autowired
    private PersAcmsService persAcmsService;
    @Autowired
    private PersPersonCacheService persPersonCacheService;

    @Autowired(required = false)
    private Vdb4PersPersonService vdb4PersPersonService;
    @Autowired
    private PersTemplateServerService persTemplateServerService;
    @Autowired
    private PersFaceVerifyService persFaceVerifyService;
    @Autowired
    private PersPersonnalListDao persPersonnalListDao;
    @Autowired
    private BaseRegisterService baseRegisterService;

    @Override
    public PersPersonItem saveItem(PersPersonItem item, PersCertificateItem certificate,
        PersAttributeExtItem attributeExt, Map<String, String> extParams) {
        if (StringUtils.isBlank(item.getPin())) {
            throw ZKBusinessException.warnException("pers_import_pinNotEmpty");
        }
        if (StringUtils.isNotBlank(item.getName())) {
            item.setName(item.getName().trim().replaceAll("\t", ""));
        }
        if (StringUtils.isNotBlank(item.getLastName())) {
            item.setLastName(item.getLastName().trim().replaceAll("\t", ""));
        }
        // 如果photo为空字符串，也转换成null
        if (Objects.nonNull(item) && StringUtils.isBlank(item.getPhotoPath())) {
            item.setPhotoPath(null);
        }
        AuthDepartmentItem dept = authDepartmentService.getItemById(item.getDeptId());
        if (Objects.isNull(dept)) {
            throw ZKBusinessException.warnException("pers_import_deptNotExist");
        }
        boolean isNewPerson = StringUtils.isBlank(item.getId());// 表示是否新增的人员
        PersPerson persPerson = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> persPersonDao.findById(id)).orElseGet(() -> {
                if (Objects.nonNull(persPersonDao.findByPin(item.getPin()))) {
                    throw ZKBusinessException.warnException("pers_app_pinExist");
                }
                PersPerson person = new PersPerson();
                person.setStatus(PersConstants.PERSON_NORMAL);
                person.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
                // 是否包含字母
                if (pinIsContainLetter(item.getPin())) {
                    person.setPinLetter(true);
                } else {
                    person.setPinLetter(false);
                }
                person.setExceptionFlag(PersConstants.PERSON_NORMAL);
                person.setIsSendMail(false);
                person.setSendSMS(false);
                // 新增默认密码123456
                person.setSelfPwd(PersConstants.PERSON_SELFPWD);
                person.setEnabledCredential(true);
                person.setAppAuthorization(false);
                person.setSendApp(true);
                return person;
            });

        if (item.getEnabledCredential() == null && persPerson.getEnabledCredential() == null) {
            persPerson.setEnabledCredential(true);
        }
        if (persPerson.getEnabledCredential() != null && !persPerson.getEnabledCredential()) {
            throw ZKBusinessException.warnException(I18nUtil.i18nCode("pers_person_disabledNotOp"));
        }

        if (item.getAppAuthorization() == null && persPerson.getAppAuthorization() == null) {
            persPerson.setAppAuthorization(false);
        }
        if (item.getSendApp() == null && persPerson.getSendApp() == null) {
            persPerson.setSendApp(true);
        }

        // 复职时，leaveId不为null
        if (StringUtils.isNotBlank(extParams.get("leaveId"))) {
            persLeavePersonService.deleteByIds(extParams.get("leaveId"));
            // 从离职人员中复职
            item.setIsFrom(PersConstants.PERS_USER_LEAVE_ADDED);
        }
        // 保存职位
        if (StringUtils.isNotBlank(item.getPositionId())) {
            PersPosition persPosition = persPositionDao.findById(item.getPositionId()).orElse(null);
            if (Objects.isNull(persPosition)) {
                throw ZKBusinessException.warnException("pers_position_notExist");
            }
            persPerson.setPersPosition(persPosition);
        } else {
            persPerson.setPersPosition(null);
        }

        // ACMS发卡
        List<PersCardItem> persCardItemList = new ArrayList<>();
        String masterCardNo = "";
        String acmsCardNum = item.getAcmsCardNum();
        List<PersCardItem> acmsCardNumList = new ArrayList<>();
        if (StringUtils.isNotBlank(acmsCardNum)) {
            acmsCardNumList = JSONArray.parseArray(acmsCardNum, PersCardItem.class);
        }
        Map<String, PersCardItem> acmsCardMap = CollectionUtil.listToKeyMap(acmsCardNumList, PersCardItem::getCardNo);
        String cardNos = item.getCardNos();
        if (StringUtils.isNotBlank(cardNos)) {
            List<String> cardNoList = StrUtil.strToList(cardNos);
            for (int i = 0; i < cardNoList.size(); i++) {
                if (StringUtils.isNotBlank(cardNoList.get(i))) {
                    short cardType = PersConstants.DEPUTY_CARD;
                    // 第一张卡需设置为主卡
                    if (i == 0) {
                        cardType = PersConstants.MAIN_CARD;
                        masterCardNo = cardNoList.get(i).trim();
                    }
                    if (acmsCardMap.containsKey(cardNoList.get(i))) {
                        PersCardItem persCardItem = acmsCardMap.get(cardNoList.get(i));
                        persCardItem.setCardType(cardType);
                        persCardItem.setCardState(PersConstants.CARD_VALID);
                        persCardItem.setIsFrom(PersConstants.ACMS);
                        persCardItemList.add(persCardItem);
                    } else {
                        persCardItemList
                            .add(new PersCardItem(cardNoList.get(i).trim(), cardType, PersConstants.CARD_VALID));
                    }
                }
            }
        }
        // 编辑的情况下，需检查主卡是否发生变化，若发生变化，需判断该卡是否已被
        if (StringUtils.isNotBlank(persPerson.getId())) {
            PersCardItem masterCard = persCardService.getMasterCardByPersonId(persPerson.getId());
            if (Objects.nonNull(masterCard)) {
                // 主卡卡号跟原有的卡号不相同。
                if (StringUtils.isBlank(masterCardNo) || !masterCard.getCardNo().equals(masterCardNo)) {
                    pers2OtherService.checkIsUseCardNo(masterCard.getCardNo());
                }
            }
        }
        // 人员属性是否修改
        boolean personModify = checkPersonChange(item, persPerson, extParams.get("bioTemplateJson"));

        if (StringUtils.isNotBlank(item.getName())) {
            // 处理姓名转拼音
            persPerson.setNameSpell(PinyinUtil.converterToSpell(item.getName()));
        }

        // 此处会导致注册用户自定义密码重置为123456 modified by bob.liu 20190614
        // 编辑不处理自助登陆密码
        // if (StringUtils.isNotBlank(item.getSelfPwd())) {
        // item.setSelfPwd(null);
        // }
        // 默认正常人员
        if (Objects.isNull(item.getExceptionFlag())) {
            item.setExceptionFlag(PersConstants.PERSON_NORMAL);
        }

        // 海外版本电话号码为 区号-电话号码
        if (!"zh_CN".equals(LocaleMessageSourceUtil.language) && StringUtils.isNotBlank(item.getAreaCode())
            && StringUtils.isNotBlank(item.getMobilePhone())) {
            item.setMobilePhone(item.getAreaCode() + "-" + item.getMobilePhone());
        }
        // 人员信息设置身份证号，避免前端先输入身份证号码后输入身份证类型未赋值idCard
        if (Objects.nonNull(certificate) && StringUtils.isNotBlank(certificate.getCertNumber())
            && PersConstants.CERT_SECOND_ID.equals(certificate.getCertType())) {
            item.setIdCard(certificate.getCertNumber());
        } else {
            item.setIdCard("");
        }
        ModelUtil.copyPropertiesIgnoreNull(item, persPerson);
        item.setEnabledCredential(persPerson.getEnabledCredential());
        item.setAppAuthorization(persPerson.getAppAuthorization());
        item.setSendApp(persPerson.getSendApp());
        persPerson.setBirthday(item.getBirthday());
        persPerson.setHireDate(item.getHireDate());
        if (isNewPerson) {
            persPerson.setNumberPin(PersPersonUtil.createNumberPin(persPerson.getPin()));
        }
        persPersonDao.save(persPerson);

        // 判断是否为更新人员
        if (!isNewPerson) {
            // 更新人员则同步更新信息至所有下发的名单库中
            List<PersPersonnallistPerson> personnallistPersonList =
                persPersonnallistPersonDao.findByPersonId(persPerson.getId());
            personnallistPersonList.forEach(persPersonnallistPerson -> {
                buildPersonInfo(persPersonnallistPerson, persPerson);
                persPersonnallistPersonDao.save(persPersonnallistPerson);
            });

        } else {
            // 将人员下发至通行名单库 add by cxq 20210115
            PersPersonnalListItem passPersonnalListItem =
                persPersonnalListService.getInitItemByType(PersConstants.TYPE_ALLOW_LIST);
            if (passPersonnalListItem != null) {
                PersPersonnallistPerson persPersonnallistPerson = new PersPersonnallistPerson();
                // 构建名单库人员信息
                buildPersonInfo(persPersonnallistPerson, persPerson);
                persPersonnallistPerson.setPersonnallistId(passPersonnalListItem.getId());
                persPersonnallistPersonDao.save(persPersonnallistPerson);
            }
        }
        item.setId(persPerson.getId());
        // 修改人员威胁等级
        if (Objects.nonNull(pers2ProService)) {
            boolean res = pers2ProService.setPersonThreatLevel(persPerson.getId(), item.getThreatLevel());
            if (!personModify) {
                personModify = res;
            }
        }

        // 保存证件
        if (Objects.nonNull(certificate)) {
            if (StringUtils.isNotBlank(certificate.getCertNumber())
                && StringUtils.isNotBlank(certificate.getCertType())) {
                certificate.setPersonId(persPerson.getId());
                persCertificateService.saveItem(certificate);
            }
            // 有证件类型/证件号任一为空不进行保存
            else {
                PersCertificateItem certificateItem = persCertificateService.getItemByPersonId(persPerson.getId());
                if (certificateItem != null && StringUtils.isNotBlank(certificateItem.getId())) {
                    persCertificateService.deleteByIds(certificateItem.getId());
                }
            }
        }

        // 保存扩展属性
        if (Objects.nonNull(attributeExt)) {
            attributeExt.setPersonId(persPerson.getId());
            persAttributeExtService.saveItem(attributeExt);
        }

        // 保存卡号
        persCardService.updatePersonCardInfo(persPerson.getId(), persCardItemList);

        // item中如果传入抠图路径且文件存在，则不再使用原图来抠图
        if (!FileUtil.fileExists(item.getCropPhotoPath()) && StringUtils.isNotBlank(item.getPhotoPath())
            && item.getCropPhotoBase64() != "") {
            File sourceImg = new File(FileUtil.getLocalFullPath(item.getPhotoPath()));
            if (sourceImg.exists()) {
                int result = 0;
                // 抠图服务使用原图
                if (baseCropFaceService.loadDetectServiceSuccess()) {
                    // 判断图片是否需要旋转，需要即旋转至正向
                    RotateImageUtil.rotateImage(sourceImg);
                    result =
                        baseCropFaceService.cropFace(FileUtil.getLocalFullPath(item.getPhotoPath()), item.getPin());
                }
                // 删除旧人脸模板，下发时才会下照片(下发规则：有模板下模板，没模板下照片)
                persBioTemplateService.deleteByPersonPinAndBioType(item.getPin(),
                    BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
                // 图片加密
                PersPersonUtil.encryptPersonPhoto(item.getPhotoPath(), item.getPin());
                // 推送人脸照片
                if (result > 0) {
                    item.setCropPhotoPath(FileUtil.getCropFacePath(item.getPin()));
                }
            }
        }

        if (FileUtil.fileExists(item.getCropPhotoPath()) && StringUtils.isNotBlank(item.getCropPhotoPath())) {
            // 删除旧人脸模板，下发时才会下照片(下发规则：有模板下模板，没模板下照片)
            persBioTemplateService.deleteByPersonPinAndBioType(item.getPin(),
                BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
            // 图片加密
            PersPersonUtil.encryptPersonPhoto(item.getPhotoPath(), item.getPin());
            // 推送人脸照片
            opFaceImgToFaceServer(persPerson);
            // 更新人员抠图信息
            updatePersBioPhoto(persPerson);
            personModify = true;
        }
        if ("".equals(item.getCropPhotoPath())) {
            // 删除旧人脸模板，下发时才会下照片(下发规则：有模板下模板，没模板下照片)
            persBioTemplateService.deleteByPersonPinAndBioType(item.getPin(),
                BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
            // 删除人员抠图信息
            delPersBioPhoto(persPerson);
            personModify = true;
        }
        // 修改前端上传照片不生成比对照片，图片未加密
        if (StringUtils.isNotBlank(item.getPhotoPath()) && item.getCropPhotoPath() == null) {
            PersPersonUtil.encryptPersonPhoto(item.getPhotoPath(), item.getPin());
        }
        if (!personModify && StringUtils.isNotBlank(item.getPhotoPath())) {
            personModify = true;
        }
        // 保存可见光掌纹照片
        if (StringUtils.isNotBlank(item.getPlamPhotoBase64())) {
            savePersBioPhoto(persPerson, item.getPlamPhotoBase64(), PersConstants.PALM_BIO_TYPE_10);
            personModify = true;
        } else if (item.getPlamPhotoDel() != null && item.getPlamPhotoDel()) {
            // 前端删除比对照片
            List<String> palmPhotoPathList =
                persBioPhotoDao.findPhotoPathByPersonIdAndBioType(persPerson.getId(), PersConstants.PALM_BIO_TYPE_10);
            if (!palmPhotoPathList.isEmpty()) {
                // 删除人员可见光掌纹照片表
                List<Short> bioTypes = new ArrayList<>();
                bioTypes.add(PersConstants.PALM_BIO_TYPE_10);
                persBioPhotoService.deleteByPersonPinsAndBioTypes(StrUtil.strToList(persPerson.getPin()), bioTypes);
                // 删除手掌照片
                for (String palmPhotoPath : palmPhotoPathList) {
                    FileUtil.deleteFile(FileUtil.getLocalFullPath(palmPhotoPath));
                }
            }
        }
        extParams.put("personModify", String.valueOf(personModify));

        // 保存生物模板
        persBioTemplateService.saveBioTemplateJson(persPerson.getId(), extParams.get("bioTemplateJson"));

        if (persPerson != null && persPerson.getId() != null) {
            // 通知其他业务模块人员扩展信息处理,传map
            item.setDeptName(dept.getName());
            item.setDeptCode(dept.getCode());
            pers2OtherService.pushEditPerson(item, extParams);
        }
        // 在系统设置人员pin不包含字符且开启pin自增功能时，新增人员的非字母pin号记录到redis中
        if ("false".equals(baseSysParamService.getValByName("pers.pinSupportLetter"))
            && "true".equals(baseSysParamService.getValByName("pers.pinSupportIncrement")) && isNewPerson
            && !pinIsContainLetter(item.getPin())) {
            persCacheManager.setAutoPin(item.getPin());
        }
        return item;
    }

    /**
     * 更新人员抠图信息
     *
     * @param persPerson:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022-07-27 17:22
     * @since 1.0.0
     */
    private void updatePersBioPhoto(PersPerson persPerson) {
        PersBioPhotoItem persBioPhotoItem = new PersBioPhotoItem();
        persBioPhotoItem.setPersonId(persPerson.getId());
        persBioPhotoItem.setPersonPin(persPerson.getPin());
        persBioPhotoItem.setBioType(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        String cropFacePath =
            FileUtil.getCropFacePath(persPerson.getPin()) + FileUtil.separator + persPerson.getPin() + ".jpg";
        persBioPhotoItem.setPhotoPath(cropFacePath);
        persBioPhotoService.saveItemByPersonId(persBioPhotoItem);
        // 开启人员人脸后台服务配置后需要下发给服务
        if (persFaceVerifyService.getFaceVerifyEnable()) {
            List<PersPersonnalList> personnalLists = persPersonnalListDao.findByPersonId(persPerson.getId());
            for (PersPersonnalList personnalList : personnalLists) {
                if (personnalList.getInitFlag()) {
                    if (PersConstants.TYPE_ALLOW_LIST.equals(personnalList.getType())) {
                        persFaceVerifyService.addFace2Server(persPerson.getPin(), cropFacePath, true);
                    } else if (PersConstants.TYPE_BAND_LIST.equals(personnalList.getType())) {
                        persFaceVerifyService.addFace2Server(persPerson.getPin(), cropFacePath, false);
                    }
                }
            }
        }
    }

    /**
     * 删除人员抠图信息
     *
     * @param persPerson
     */
    private void delPersBioPhoto(PersPerson persPerson) {
        List<Short> bioTypes = new ArrayList<>();
        bioTypes.add(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
        persBioPhotoService.deleteByPersonPinsAndBioTypes(StrUtil.strToList(persPerson.getPin()), bioTypes);
        // 删除抠图，不然会影响人脸数量统计
        FileUtil.deleteDirectory(FileUtil.getLocalFullPath(FileUtil.getCropFacePath(persPerson.getPin())));
        // 开启人员人脸后台服务配置后需要下发删除给服务
        if (persFaceVerifyService.getFaceVerifyEnable()) {
            List<PersPersonnalList> personnalLists = persPersonnalListDao.findByPersonId(persPerson.getId());
            for (PersPersonnalList personnalList : personnalLists) {
                if (personnalList.getInitFlag()) {
                    if (PersConstants.TYPE_ALLOW_LIST.equals(personnalList.getType())) {
                        persFaceVerifyService.delFaceFromServer(persPerson.getPin(), true);
                    } else if (PersConstants.TYPE_BAND_LIST.equals(personnalList.getType())) {
                        persFaceVerifyService.delFaceFromServer(persPerson.getPin(), false);
                    }
                }
            }
        }
    }

    /**
     * 保存人员比对照片
     *
     * @param persPerson:
     * @param photoBase64:
     * @param BioType:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2024-05-16 11:31
     * @since 1.0.0
     */
    private void savePersBioPhoto(PersPerson persPerson, String photoBase64, Short BioType) {
        PersBioPhotoSaveItem persBioPhotoSaveItem = new PersBioPhotoSaveItem();
        persBioPhotoSaveItem.setPersonPin(persPerson.getPin());
        persBioPhotoSaveItem.setBioType(BioType);
        persBioPhotoSaveItem.setPhotoBase64(photoBase64);
        persBioPhotoSaveItem.setFileName(persPerson.getPin() + ".jpg");
        persBioPhotoService.saveBioPhoto(persBioPhotoSaveItem);
    }

    /**
     * 更新人员抠图信息
     *
     * @param persPersons:
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2024-01-25 16:05
     * @since 1.0.0
     */
    private void updatePersBioPhotos(List<PersPerson> persPersons) {
        List<PersBioPhotoItem> persBioPhotoItemList = new ArrayList<>();
        for (PersPerson persPerson : persPersons) {
            PersBioPhotoItem persBioPhotoItem = new PersBioPhotoItem();
            persBioPhotoItem.setPersonId(persPerson.getId());
            persBioPhotoItem.setPersonPin(persPerson.getPin());
            persBioPhotoItem.setBioType(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
            String cropFacePath =
                FileUtil.getCropFacePath(persPerson.getPin()) + FileUtil.separator + persPerson.getPin() + ".jpg";
            persBioPhotoItem.setPhotoPath(cropFacePath);
            persBioPhotoItemList.add(persBioPhotoItem);
        }
        persBioPhotoService.saveItemByPersonIds(persBioPhotoItemList);
    }

    private ZKResultMsg updateBioTemplate(PersPerson persPerson) {
        String cropFacePath =
            FileUtil.getCropFacePath(persPerson.getPin()) + FileUtil.separator + persPerson.getPin() + ".jpg";
        ZKResultMsg faceTemplate = persTemplateServerService.getFaceTemplateByPhoto(cropFacePath);
        if (faceTemplate != null && "0".equals(faceTemplate.getRet())) {
            Map<String, String> faceTemplateMap = (Map<String, String>)faceTemplate.getData();
            if (faceTemplateMap != null) {
                String template = faceTemplateMap.get("template");
                String version = faceTemplateMap.get("version");
                if (StringUtils.isNotBlank(template) && StringUtils.isNotBlank(version)) {
                    List<PersBioTemplateItem> persBioTemplateItemList = new ArrayList<>();
                    PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
                    persBioTemplateItem.setPersonId(persPerson.getId());
                    persBioTemplateItem.setPersonPin(persPerson.getPin());
                    persBioTemplateItem.setTemplate(template);
                    persBioTemplateItem.setVersion(version);
                    persBioTemplateItem.setBioType(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
                    persBioTemplateItem.setTemplateNo((short)0);
                    persBioTemplateItem.setTemplateNoIndex((short)0);
                    persBioTemplateItemList.add(persBioTemplateItem);
                    persBioTemplateService.updateBioTemplate(persBioTemplateItemList);
                }
            }
        }
        return faceTemplate;
    }

    /**
     * 判断人员基本属性是否更改
     *
     * @param item:
     * @param persPerson:
     * @param bioTemplateJson:
     * @return boolean
     * @throws @date 2021-12-01 15:32
     * <AUTHOR>
     * @since 1.0.0
     */
    private boolean checkPersonChange(PersPersonItem item, PersPerson persPerson, String bioTemplateJson) {
        boolean personModify = false;
        if (StringUtils.isBlank(item.getId())) {
            personModify = true;
        } else {
            // 修改人员姓名
            if (!(item.getName() + "").equals(persPerson.getName() + "")) {
                personModify = true;
            }
            // 修改人员姓氏
            else if (!(item.getLastName() + "").equals(persPerson.getLastName() + "")) {
                personModify = true;
            }
            // 修改身份证号
            else if (!(item.getIdCard() + "").equals(persPerson.getIdCard() + "")) {
                personModify = true;
            }
            // 修改密码
            else if (!(item.getPersonPwd() + "").equals(persPerson.getPersonPwd() + "")) {
                personModify = true;
            }
            // 修改照片
            else if (StringUtils.isNotBlank(item.getPhotoPath())) {
                personModify = true;
            }
            if (!personModify) {
                // 修改副卡
                List<PersCard> cardList =
                    persCardDao.findByPersonIdInOrderByCardType(Arrays.asList(persPerson.getId()));
                Map<String, PersCard> persCardMap = CollectionUtil.listToKeyMap(cardList, PersCard::getCardNo);
                if (StringUtils.isNotBlank(item.getCardNos())) {
                    List<String> cardNoList = (List<String>)CollectionUtil.strToList(item.getCardNos());
                    if (!cardNoList.isEmpty()) {
                        if (cardList.isEmpty()) {
                            personModify = true;
                        } else {
                            if (cardNoList.size() != cardList.size()) {
                                personModify = true;
                            } else {
                                for (int i = 0; i < cardNoList.size(); i++) {
                                    PersCard persCard = persCardMap.get(cardNoList.get(i));
                                    if (i == 0) {
                                        if (!(persCard != null
                                            && persCard.getCardType().equals(PersConstants.MAIN_CARD))) {
                                            personModify = true;
                                            break;
                                        }
                                    } else {
                                        if (!(persCard != null
                                            && persCard.getCardType().equals(PersConstants.DEPUTY_CARD))) {
                                            personModify = true;
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    } else if (!cardList.isEmpty()) {
                        personModify = true;
                    }

                } else if (!cardList.isEmpty()) {
                    personModify = true;
                }
            }
            // 修改生物模板
            if (!personModify) {
                if (StringUtils.isNotBlank(bioTemplateJson)) {
                    Map<String, Object> bioTemplateMap = JSON.parseObject(bioTemplateJson);
                    if (!((((JSONObject)bioTemplateMap).getJSONObject("fpList") == null
                        || (((JSONObject)bioTemplateMap).getJSONObject("fpList")).size() == 0)
                        && (((JSONObject)bioTemplateMap).getJSONObject("fvList") == null
                            || (((JSONObject)bioTemplateMap).getJSONObject("fvList")).size() == 0)
                        && (((JSONObject)bioTemplateMap).getJSONObject("faceList") == null
                            || (((JSONObject)bioTemplateMap).getJSONObject("faceList")).size() == 0)
                        && (((JSONObject)bioTemplateMap).getJSONObject("palmList") == null
                            || (((JSONObject)bioTemplateMap).getJSONObject("palmList")).size() == 0)
                        && (((JSONObject)bioTemplateMap).getJSONObject("vislightList") == null
                            || (((JSONObject)bioTemplateMap).getJSONObject("vislightList")).size() == 0)
                        && (((JSONObject)bioTemplateMap).getJSONObject("irisList") == null
                            || (((JSONObject)bioTemplateMap).getJSONObject("irisList")).size() == 0))) {
                        personModify = true;
                    }
                    // 前端人员删除生物模板传到后台数据格式为：{"fpList":{},"faceList":{},"vislightList":{},"fvList":{},"palmList":{},"fpCount":null}
                    else {
                        List<PersBioTemplate> bioTemplateList = persBioTemplateDao.findByPersonId(persPerson.getId());
                        if (!bioTemplateList.isEmpty()) {
                            personModify = true;
                        }

                    }
                } else {
                    List<PersBioTemplate> bioTemplateList = persBioTemplateDao.findByPersonId(persPerson.getId());
                    if (!bioTemplateList.isEmpty()) {
                        personModify = true;
                    }
                }
            }
        }
        return personModify;
    }

    @Override
    public void resetSelfPwd(String personIds) {
        List<PersPerson> personList = persPersonDao.findByIdIn(CollectionUtil.strToList(personIds));
        personList.forEach(person -> {
            person.setSelfPwd(PersConstants.PERSON_SELFPWD);
            persPersonDao.save(person);
        });
    }

    @Override
    public void updateSelfPwd(String personId, String selfPwd) {
        PersPerson person = persPersonDao.findById(personId).orElse(null);
        if (Objects.nonNull(person)) {
            if (StringUtils.isBlank(person.getSelfPwd()) || !person.getSelfPwd().equals(selfPwd)) {
                person.setSelfPwd(selfPwd);
                persPersonDao.save(person);
            }
        } else {
            throw ZKBusinessException.errorException("pers_app_personNull");
        }
    }

    @Override
    public void handlerTransfer(List<PersPersonItem> persPersonItems) {
        // 以防大数据时进行分批数据
        List<List<PersPersonItem>> personItemList = CollectionUtil.split(persPersonItems, CollectionUtil.splitSize);
        // 获取部门集合 add by hql 20190805
        List<AuthDepartmentItem> authDepartmentItems = authDepartmentService.getByCondition(new AuthDepartmentItem());
        Map<String, AuthDepartmentItem> authDepartmentItemMap =
            CollectionUtil.listToKeyMap(authDepartmentItems, AuthDepartmentItem::getCode);
        // 获取职位的集合 add by hql 20190805
        List<PersPosition> persPositions = persPositionDao.findAll();
        Map<String, PersPosition> persPositionMap = CollectionUtil.listToKeyMap(persPositions, PersPosition::getCode);
        for (List<PersPersonItem> personItems : personItemList) {
            // 得到数据库中已有的人员信息，一次性取出后获取
            Collection<String> pins = CollectionUtil.getPropertyList(personItems, PersPersonItem::getPin, "-1");
            List<PersPerson> personList = persPersonDao.findByPinIn(pins);
            Map<String, PersPerson> personMap = CollectionUtil.listToKeyMap(personList, PersPerson::getPin);
            for (PersPersonItem persPersonItem : personItems) {
                PersPerson person = personMap.remove(persPersonItem.getPin());
                if (Objects.isNull(person)) {
                    person = new PersPerson();
                    // 来源于数据迁移
                    person.setIsFrom("PERS_DATA_MOVE").setSelfPwd(PersConstants.PERSON_SELFPWD);
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(persPersonItem, person, "id");
                // 设置职位
                if (StringUtils.isNotBlank(persPersonItem.getPositionCode())) {
                    person.setPersPosition(persPositionMap.get(persPersonItem.getPositionCode()));
                }
                // 设置部分
                AuthDepartmentItem departmentItem = authDepartmentItemMap.get(persPersonItem.getDeptCode());
                person.setDeptId(departmentItem != null ? departmentItem.getId() : null);
                person.setPinLetter(!StringUtils.isNumeric(person.getPin()))
                    .setNameSpell(PinyinUtil.converterToSpell(person.getName()));
                if (StringUtils.isNotBlank(persPersonItem.getSelfPwd())) {
                    try {
                        person.setSelfPwd(MD5Util.getMD5String(persPersonItem.getSelfPwd()));
                    } catch (Exception e) {
                    }
                }
                persPersonDao.save(person);
            }
        }
    }

    private <T> void group(HashMap<String, List<T>> map, String key, T t) {
        List<T> list = map.get(key);
        if (CollectionUtil.isEmpty(list)) {
            list = new ArrayList<>();
            map.put(key, list);
        }
        list.add(t);
    }

    /**
     * 人事许可校验
     *
     * @param
     * @return boolean
     * <AUTHOR>
     * @date 2018/7/9 10:23
     */
    @Override
    public boolean persLicenseCheck() {
        boolean licenseResult = false;
        Integer personSize = persPersonDao.countByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        ResultCode resultCode = baseLicenseProvider.isCountOutRangePers(personSize + 1);
        if (ResultCode.SUCCESS.equals(resultCode)) {
            licenseResult = true;
        }
        return licenseResult;

    }

    @Override
    public List<PersPersonItem> batchSaveItem(List<PersPersonItem> personItems, String module) {
        if (!CollectionUtil.isEmpty(personItems)) {
            List<PersPersonItem> savePersonItems = new ArrayList<PersPersonItem>();
            // CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
            Collection<String> pins = CollectionUtil.getPropertyList(personItems, PersPersonItem::getPin, "-1");
            List<PersPerson> personList = persPersonDao.findByPinIn(pins);
            Map<String, PersPersonItem> itemMap = CollectionUtil.listToKeyMap(personItems, PersPersonItem::getPin);
            personList.forEach(person -> {
                PersPersonItem item = itemMap.remove(person.getPin());
                // 编辑
                ModelUtil.copyPropertiesIgnoreNull(item, person);
                if (StringUtils.isNotBlank(item.getName())) {
                    person.setNameSpell(PinyinUtil.converterToSpell(item.getName()));
                }
                if (person.getEnabledCredential() == null) {
                    person.setEnabledCredential(true);
                }
                if (person.getSendApp() == null) {
                    person.setSendApp(true);
                }
                persPersonDao.save(person);
                ModelUtil.copyPropertiesIgnoreNull(person, item);
                savePersonItems.add(item);
            });
            // 新增
            if (!CollectionUtil.isEmpty(itemMap)) {
                AuthDepartmentItem defautDept = authDepartmentService.getDefautDept();
                String isSupportLetter = baseSysParamService.getValByName("pers.pinSupportLetter");
                itemMap.forEach((pin, item) -> {
                    PersPerson person = ModelUtil.copyProperties(item, new PersPerson());
                    person.setStatus(PersConstants.PERSON_NORMAL);
                    person.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
                    // 是否包含字母
                    if (pinIsContainLetter(item.getPin())) {
                        person.setPinLetter(true);
                    } else {
                        person.setPinLetter(false);
                        // persCacheManager.savePersonPin(person.getPin());
                    }
                    // 含有字母且参数设置不支持字母，需要把人员标记为pin异常
                    if (person.getPinLetter() && "false".equals(isSupportLetter)) {
                        person.setExceptionFlag(PersConstants.PERSON_PIN_EXCEPTION);
                    } else if (person.getExceptionFlag() == null) {
                        // 没有异常状态的时候赋值为正常
                        person.setExceptionFlag(PersConstants.PERSON_NORMAL);
                    }
                    person.setIsSendMail(false);
                    person.setSelfPwd(PersConstants.PERSON_SELFPWD);

                    if (StringUtils.isNotBlank(item.getName())) {
                        // 处理姓名拼音
                        person.setNameSpell(PinyinUtil.converterToSpell(item.getName()));
                    }
                    // 没有部门需要默认部门ID
                    if (StringUtils.isBlank(item.getDeptId())) {
                        person.setDeptId(defautDept.getId());
                        item.setDeptId(defautDept.getId());
                    }
                    person.setNumberPin(PersPersonUtil.createNumberPin(person.getPin()));
                    if (item.getEnabledCredential() == null) {
                        person.setEnabledCredential(true);
                    }
                    if (item.getSendApp() == null) {
                        person.setSendApp(true);
                    }
                    persPersonDao.save(person);

                    item.setId(person.getId());
                    savePersonItems.add(item);
                });
            }

            pers2OtherService.pushBatchSaveItem(savePersonItems, module);
        }
        return personItems;
    }

    @Override
    public void saveUserPhoto(String pin, String photoBase64) {
        PersPerson persPerson =
            Optional.ofNullable(pin).filter(StringUtils::isNotBlank).map(persPersonDao::findByPin).orElse(null);
        if (Objects.nonNull(persPerson) && StringUtils.isNotBlank(photoBase64)) {
            String photoPath =
                FileUtil.saveFileToServer("pers", "user/avatar", persPerson.getPin() + ".jpg", photoBase64);
            persPerson.setPhotoPath(photoPath);
            PersPersonUtil.encryptPersonPhoto(photoPath, pin);
            persPersonDao.save(persPerson);
        }
    }

    @Override
    public ZKResultMsg uploadUserPhoto(String pin, String photoPath) {
        PersImportPhotoItem persImportPhotoItem = new PersImportPhotoItem();
        persImportPhotoItem.setPin(pin);
        persImportPhotoItem.setPhotoPath(photoPath);
        persImportPhotoItem.setImportPhoto(true);
        persImportPhotoItem.setImportCropFace(true);
        return uploadUserPhoto(persImportPhotoItem);
    }

    @Override
    public ZKResultMsg uploadUserPhoto(PersImportPhotoItem persImportPhotoItem) {
        String pin = persImportPhotoItem.getPin();
        String photoPath = persImportPhotoItem.getPhotoPath();
        String cropPhotoPath = persImportPhotoItem.getCropPhotoPath();
        Boolean importPhoto = persImportPhotoItem.getImportPhoto();
        Boolean importCropFace = persImportPhotoItem.getImportCropFace();
        Boolean importTemplateFace = persImportPhotoItem.getImportTemplateFace();
        PersPerson persPerson =
            Optional.ofNullable(pin).filter(StringUtils::isNotBlank).map(persPersonDao::findByPin).orElse(null);

        if (Objects.nonNull(persPerson)) {
            ZKResultMsg msg = ZKResultMsg.successMsg();
            File sourceImg = null;
            if (importPhoto && StringUtils.isNotBlank(photoPath)) {
                persPerson.setPhotoPath(photoPath);
                persPersonDao.save(persPerson);
            }
            if (importCropFace) {
                String fullPath = FileUtil.getLocalFullPath(photoPath);
                String ret = "fail";
                if (importPhoto && StringUtils.isNotBlank(photoPath)) {
                    sourceImg = new File(fullPath);
                } else if (StringUtils.isNotBlank(cropPhotoPath)) {
                    fullPath = FileUtil.getLocalFullPath(cropPhotoPath);
                    sourceImg = new File(fullPath);
                }
                BufferedImage src = null;
                int result = -100;
                Map<String, String> faceFailTypeMap =
                    baseDictionaryValueService.getDictionaryValuesMap("PersFaceFailType");
                faceFailTypeMap.put("-101", "pers_face_photoFormatError");
                try {
                    if (sourceImg.exists()) {
                        // 抠图服务使用原图
                        if (baseCropFaceService.loadDetectServiceSuccess()) {
                            boolean jpgImage = ImgEncodeUtil.isSupportImageType(sourceImg, FileType.JPEG, FileType.PNG);
                            if (!jpgImage) {
                                result = -101;
                            } else {
                                // 判断图片是否需要旋转，需要即旋转至正向
                                RotateImageUtil.rotateImage(sourceImg);
                                result = baseCropFaceService.cropFace(fullPath, pin);
                            }

                        } else {
                            src = ImageIO.read(sourceImg);
                            if (src.getWidth() * src.getHeight() > 1024 * 1024) {// 超过1024*1024使用缩略图进行抠图，防止动态库挂
                                result = baseCropFaceService
                                    .cropFace(FileUtil.getLocalFullPath(FileUtil.getThumbPath(photoPath)), pin);
                            } else {
                                result = baseCropFaceService.cropFace(FileUtil.getLocalFullPath(photoPath), pin);
                            }
                        }
                        persBioTemplateService.deleteByPersonPinAndBioType(pin,
                            BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);

                        if (StringUtils.isNotBlank(cropPhotoPath)) {
                            File tempFile = new File(FileUtil.getLocalFullPath(cropPhotoPath));
                            if (tempFile.exists()) {
                                // 删除临时图片
                                tempFile.delete();
                            }
                        }
                    }
                    String retStr = String.valueOf(result);
                    if (faceFailTypeMap.containsKey(retStr)) {
                        msg.setRet(ret);
                        msg.setMsg(I18nUtil.i18nCode("pers_face_validFailMsg")
                            + I18nUtil.i18nCode(faceFailTypeMap.get(retStr)));
                    } else if (result <= -100) {
                        msg.setRet(ret);
                        msg.setMsg("pers_import_cropFaceFail");
                    }
                    // 抠图成功
                    if (result > 0) {
                        // 推送人脸照片
                        opFaceImgToFaceServer(persPerson);
                        // 更新人员抠图信息
                        updatePersBioPhoto(persPerson);
                        if (importTemplateFace) {
                            ZKResultMsg faceTemplate = updateBioTemplate(persPerson);
                            if (faceTemplate != null && !"0".equals(faceTemplate.getRet())) {
                                String failMsg = PersConstants.TEMPLATE_SERVER_ERROR_MAP.get(faceTemplate.getRet());
                                if (StringUtils.isBlank(failMsg)) {
                                    failMsg = I18nUtil.i18nCode("pers_person_serverOffline");
                                }
                                msg.setRet("fail");
                                msg.setMsg(I18nUtil.i18nCode("pers_person_faceTemplateError6") + ","
                                    + I18nUtil.i18nCode("auth_license_reason") + failMsg);
                            }
                        }
                    }
                } catch (IOException e) {
                    logger.error("read img error", e);
                }
            }
            PersPersonUtil.encryptPersonPhoto(photoPath, pin);

            return msg;
        }
        return ZKResultMsg.failMsg("pers_person_pinError", pin);
    }

    @Override
    public List<PersPersonItem> getByCondition(PersPersonItem condition) {
        buildCondition(null, condition);
        return (List<PersPersonItem>)buildDept(
            buildCard(persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition))));
    }

    @Override
    public List<PersPersonItem> getByAuthUserAndCondition(String sessionId, PersPersonItem condition) {
        buildCondition(sessionId, condition);
        return (List<PersPersonItem>)buildDept(
            buildCard(persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition))));
    }

    @Deprecated
    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return loadPagerByAuthUserFilter(null, condition, page, size);
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, BaseItem condition, int pageNo, int pageSize) {
        buildCondition(sessionId, (PersPersonItem)condition);
        Pager pager = persPersonDao.getItemsBySql(condition.getClass(),
            buildVerifyModeSqlByItem((PersPersonItem)condition), pageNo, pageSize);
        buildCard(pager.getData());
        buildAttribute(pager.getData());
        buildVerifyMode(pager.getData());
        return pager;
    }

    /**
     * 封装验证方式查询语句
     *
     * @param item:
     * @return java.lang.String
     * @throws @date 2020-12-01 18:44
     * <AUTHOR>
     * @since 1.0.0
     */
    private String buildVerifyModeSqlByItem(BaseItem item) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(SQLUtil.getSqlByItem(item));
        int orderByIndex = stringBuilder.indexOf("ORDER BY");
        try {
            Field field = item.getClass().getDeclaredField("verifyMode");
            field.setAccessible(true);
            Object val = field.get(item);
            if (val != null) {
                String verifyMode = val.toString();
                if (StringUtils.isNotBlank(verifyMode)) {
                    List<String> verifyModes = new ArrayList<>();
                    List<String> verifyModeReverses = new ArrayList<>();
                    for (String str : verifyMode.split(",")) {
                        if (str.contains("N")) {
                            verifyModeReverses.add(str.substring(str.indexOf("N") + 1));
                        } else {
                            verifyModes.add(str);
                        }
                    }
                    // 包含查询
                    StringBuilder contain = new StringBuilder();
                    if (verifyModes.contains(PersConstants.VERIFY_MODE_CARD)) {
                        verifyModes.remove(PersConstants.VERIFY_MODE_CARD);
                        contain.append(" t.ID IN(SELECT c.PERSON_ID FROM PERS_CARD c GROUP BY c.PERSON_ID)");
                    }
                    if (verifyModes.contains(PersConstants.VERIFY_MODE_CROPFACE)) {
                        verifyModes.remove(PersConstants.VERIFY_MODE_CROPFACE);
                        if (contain.length() > 0) {
                            contain.append(" OR");
                        }
                        contain.append(" t.ID IN(SELECT p.PERSON_ID FROM PERS_BIOPHOTO p GROUP BY p.PERSON_ID)");
                    }
                    if (!verifyModes.isEmpty()) {
                        if (contain.length() > 0) {
                            contain.append(" OR");
                        }
                        contain.append(" t.ID IN(SELECT b.PERSON_ID FROM PERS_BIOTEMPLATE b WHERE b.BIO_TYPE IN ("
                            + StringUtils.join(verifyModes, ",") + ") GROUP BY b.PERSON_ID)");
                    }
                    if (contain.length() > 0) {
                        stringBuilder.insert(orderByIndex, " AND (" + contain + ")");
                    }
                    // 不包含查询
                    StringBuilder noContain = new StringBuilder();
                    if (verifyModeReverses.contains(PersConstants.VERIFY_MODE_CARD)) {
                        verifyModeReverses.remove(PersConstants.VERIFY_MODE_CARD);
                        noContain.append(" t.ID NOT IN(SELECT c.PERSON_ID FROM PERS_CARD c GROUP BY c.PERSON_ID)");
                    }
                    if (verifyModeReverses.contains(PersConstants.VERIFY_MODE_CROPFACE)) {
                        verifyModeReverses.remove(PersConstants.VERIFY_MODE_CROPFACE);
                        if (noContain.length() > 0) {
                            noContain.append(" AND");
                        }
                        noContain.append(" t.ID NOT IN(SELECT p.PERSON_ID FROM PERS_BIOPHOTO p GROUP BY p.PERSON_ID)");
                    }
                    if (!verifyModeReverses.isEmpty()) {
                        if (noContain.length() > 0) {
                            noContain.append(" AND");
                        }
                        noContain.append(" t.ID NOT IN(SELECT b.PERSON_ID FROM PERS_BIOTEMPLATE b WHERE b.BIO_TYPE IN ("
                            + StringUtils.join(verifyModeReverses, ",") + ") GROUP BY b.PERSON_ID)");
                    }
                    if (noContain.length() > 0) {
                        stringBuilder.insert(orderByIndex, " AND (" + noContain + ")");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return stringBuilder.toString();
    }

    @Override
    public List<String> getPersonPinByDeptPersAuthUserFilter(String sessionId, PersPersonItem condition) {
        // 权限过滤
        buildCondition(sessionId, condition);
        // 查询正常人员 pin name
        List<PersPersonItem> personItemList =
            (List<PersPersonItem>)persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        // 获取人员Pin的数据
        List<String> persPersonPinList = personItemList.stream().map(PersPersonItem::getPin)
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return persPersonPinList;
    }

    @Override
    public Map<String, String> getAllPersonPhotoByAuthUserFilter(String sessionId, PersPersonPinItem condition) {
        // 封装部门条件
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        if (StringUtils.isBlank(condition.getInDeptId())) {
            if (StringUtils.isNotBlank(condition.getDeptId())) {
                List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getDeptId());
                condition.setInDeptId(StrUtil.collectionToStr(deptIdList));
            }
        }
        condition.setDeptId(null);
        List<PersPersonPinItem> personPinItems = (List<PersPersonPinItem>)persPersonDao
            .getItemsBySql(condition.getClass(), buildVerifyModeSqlByItem(condition));
        if (!CollectionUtil.isEmpty(personPinItems)) {
            return personPinItems.stream().filter(p -> StringUtils.isNotBlank(p.getPhotoPath()))
                .collect(Collectors.toMap(PersPersonPinItem::getPin, PersPersonPinItem::getPhotoPath, (x, y) -> y));
        }
        return Collections.emptyMap();
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            pers2OtherService.pushDelOtherModelPerson(ids);
        }
        return deleteByIdsAndType(ids, PersConstants.PERSON_OPERATE_TYPE_DEL);
    }

    @Override
    public boolean deleteByIdsAndType(String ids, String opType) {
        if (StringUtils.isNotEmpty(ids)) {
            List<PersPerson> persPersonList = persPersonDao.findByIdList(CollectionUtil.strToList(ids));
            List<String> pinList =
                (List<String>)CollectionUtil.getPropertyList(persPersonList, PersPerson::getPin, "-1");
            // 异步通知云端根据pin号删除/离职人员
            if (Objects.nonNull(pers2CloudService)) {
                pers2CloudService.asyncDelPersonToCloud(pinList);
                // if (PersConstants.PERSON_OPERATE_TYPE_DEL.equals(opType)) {
                // } else if (PersConstants.PERSON_OPERATE_TYPE_LEAVE.equals(opType)) {
                // persCloudService.asyncLeavePersonToCloud(pinList);
                // }
            }
            // 删除ACMS上人员
            persAcmsService.deleteAcmsPerson(pinList);
            for (PersPerson persPerson : persPersonList) {
                String id = persPerson.getId();
                // 先删除
                persCardService.deleteByPersonId(id);
                // 删除生物模板
                persBioTemplateService.deleteByPersonPin(persPerson.getPin());
                // 再删除人员中间表
                persPersonLinkService.deleteByPersonId(id);
                // 删除人员自定义属性表
                persAttributeExtService.deleteByPersonId(id);
                List<String> palmPhotoPathList =
                    persBioPhotoDao.findPhotoPathByPersonIdAndBioType(id, PersConstants.PALM_BIO_TYPE_10);
                // 删除人员比对照片表
                persBioPhotoService.deleteByPersonId(id);
                PersPerson person = persPersonDao.findById(id).orElse(null);
                if (StringUtils.isNotBlank(person.getPhotoPath())) {
                    FileUtil.deleteFile(FileUtil.getLocalFullPath(person.getPhotoPath()));// 删除人员头像
                    FileUtil.deleteFile(FileUtil.getLocalFullPath(FileUtil.getThumbPath(person.getPhotoPath())));// 删除人员头像缩略图
                }
                // 删除抠图，不然会影响人脸数量统计
                FileUtil.deleteDirectory(FileUtil.getLocalFullPath(FileUtil.getCropFacePath(person.getPin())));
                // 删除手掌照片
                for (String palmPhotoPath : palmPhotoPathList) {
                    FileUtil.deleteFile(FileUtil.getLocalFullPath(palmPhotoPath));
                }
                // 最后删除人员
                persPersonDao.delete(person);
                // 删掉相关session add by bob.liu ********
                sessionManager.deleteUserSessionByAccount(persPerson.getPin() + "PERS");
                // 清除登录错误记录,避免再加一个PIN号相同的人,错误记录被继承
                authSecurityParamsService.clearLoginErrorCount(persPerson.getPin() + "PERS");
                // 删除apptoken
                authUserService.delApiToken(persPerson.getPin());
                // 删除，离职需删除客户端，并删除对应token
                baseRegisterService.delByClientNameAndClientType(persPerson.getPin(), BaseAppConstants.APP_PERS);
            }
            // 删除名单库下的人员
            persPersonnalListService.delPersonByPersonId(ids);
            // 调查发现暂时没有这块逻辑
            /*if (!CollectionUtil.isEmpty(pinList)) {
                for (String pin : pinList) {
                    AuthUserItem authUserItem = authUserService.getSimpleItemByUserName(pin);
                    if (authUserItem != null) {
                        authUserService.deleteByIds(authUserItem.getId());
                    }
                }
            }*/
            return true;
        }
        return false;
    }

    @Override
    public PersPersonItem getItemById(String id) {
        return Optional.ofNullable(id).filter(StringUtils::isNotBlank).flatMap(persPersonDao::findById)
            .filter(Objects::nonNull).map(person -> {
                PersPersonItem item = ModelUtil.copyProperties(person, new PersPersonItem());
                if (Objects.nonNull(person.getPersPosition())) {
                    item.setPositionId(person.getPersPosition().getId());
                    item.setPositionName(person.getPersPosition().getName());
                }
                return item;
            }).map(i -> buildCard(i)).map(i -> buildDept(i)).orElse(null);
    }

    @Override
    public PersPersonItem getSimpleItemById(String id) {
        return Optional.ofNullable(id).filter(StringUtils::isNotBlank).flatMap(persPersonDao::findById)
            .filter(Objects::nonNull).map(person -> {
                PersPersonItem item = ModelUtil.copyProperties(person, new PersPersonItem());
                if (Objects.nonNull(person.getPersPosition())) {
                    item.setPositionId(person.getPersPosition().getId());
                }
                return item;
            }).orElse(null);
    }

    @Override
    public PersPersonItem getItemByPin(String pin) {
        return Optional.ofNullable(pin).filter(StringUtils::isNotBlank).map(persPersonDao::findByPin)
            .filter(Objects::nonNull).map(person -> {
                PersPersonItem item = ModelUtil.copyProperties(person, new PersPersonItem());
                if (Objects.nonNull(person.getPersPosition())) {
                    item.setPositionId(person.getPersPosition().getId());
                    item.setPositionName(person.getPersPosition().getName());
                }
                return item;
            }).map(i -> buildCard(i)).map(i -> buildDept(i)).orElse(null);
    }

    @Override
    public Boolean isExistPin(String pin) {
        return Optional.ofNullable(pin).filter(StringUtils::isNotBlank).map(persPersonDao::findByPin)
            .map(Objects::nonNull).orElse(false);
    }

    @Override
    public List<PersPersonItem> getItemsByIds(Collection<String> ids) {
        PersPersonItem condition = new PersPersonItem();
        condition.setInId(StringUtils.join(ids, ","));
        return getByCondition(condition);
    }

    @Override
    public List<PersPersonItem> getSimpleItemsByIds(Collection<String> ids) {
        return Optional.ofNullable(ids).filter(i -> !i.isEmpty()).map(persPersonDao::findByIdIn)
            .map(list -> ModelUtil.copyListProperties(list, PersPersonItem.class))
            .orElse(new ArrayList<PersPersonItem>());
    }

    @Override
    public List<PersPersonItem> getItemsByIds(String ids) {
        PersPersonItem condition = new PersPersonItem();
        condition.setInId(ids);
        return getByCondition(condition);
    }

    @Override
    public List<PersPersonItem> getItemsByPins(Collection<String> pins) {
        PersPersonItem condition = new PersPersonItem();
        condition.setInPin(StringUtils.join(pins, ","));
        return getByCondition(condition);
    }

    @Override
    public List<PersPersonItem> getItemsByPins(String pins) {
        PersPersonItem condition = new PersPersonItem();
        condition.setInPin(pins);
        return getByCondition(condition);
    }

    @Override
    public List<PersPersonItem> getItemsByCardNos(String cardNos) {
        // PersPersonItem condition = new PersPersonItem();
        // condition.setCardNos(cardNos);
        // return getByCondition(condition);
        // 修复因卡号cardNos没有进行加密处理，导致无法查询出人员信息问题 ----modify by zhixiong.huang 2021-08-20 16:54
        List<PersPersonItem> personItemList = new ArrayList<>();
        if (StringUtils.isNotBlank(cardNos)) {
            List<PersPerson> persPersonList = persPersonDao.findByPersonCardNo(CollectionUtil.strToList(cardNos));
            if (!CollectionUtil.isEmpty(persPersonList)) {
                personItemList = ModelUtil.copyListProperties(persPersonList, PersPersonItem.class);
            }
        }
        return personItemList;
    }

    @Override
    public Integer getAllPersonCount() {
        return persPersonDao.countByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
    }

    @Override
    public Integer getPersonCountByDeptIds(String deptId) {
        Integer personCount = 0;
        List<List<String>> deptIdList =
            CollectionUtil.split(CollectionUtil.strToList(deptId), CollectionUtil.splitSize);
        for (List<String> deptIds : deptIdList) {
            personCount = personCount + persPersonDao.countByDeptIdIn(deptIds);
        }
        return personCount;
    }

    @Override
    public Map<String, String> dataCount() {
        Map<String, String> dataCount = new HashMap<String, String>();
        // 人员的统计
        dataCount.put("personCount",
            String.valueOf(persPersonDao.countByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE)));
        // 卡统计
        dataCount.put("cardCount", String.valueOf(persCardDao.countByCardState(PersConstants.CARD_VALID)));

        List<Object[]> bioTemplateCount = persBioTemplateDao
            .countByTemplateNoIndexAndGroupByBioTypeAndVersion(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);// Object[]
        dataCount.putAll(countBioTemplate(bioTemplateCount));

        // 性别统计
        // 默认初始化男女人数，性别不是必填的，男女人数的和并不等于总人数
        dataCount.put("maleCount", "0");
        dataCount.put("feMaleCount", "0");
        dataCount.put("unknownCount", "0");
        // 此处查询出有没用的名称数据，不影响统计 add by max
        List<Object[]> genderCount = persPersonDao.countGender();
        for (Object[] objArray : genderCount) {
            // objArray[0] = maleCount （男） objArray[0] = feMaleCount （女）
            dataCount.put(objArray[0] + "", objArray[1] + "");
        }
        dataCount.put("cropFaceCount", String.valueOf(persBioPhotoDao.count()));
        return dataCount;
    }

    @Override
    public Map<String, String> dataCountByAuthUserFilter(String userId) {
        Map<String, String> dataCount = new HashMap<String, String>();

        // 人员的统计
        dataCount.put("personCount",
            String.valueOf(persPersonDao.countByPersonTypeAndUserId(PersConstants.PERSON_TYPE_EMPLOYEE, userId)));
        // 卡统计
        dataCount.put("cardCount",
            String.valueOf(persCardDao.countByCardStateAndUserId(PersConstants.CARD_VALID, userId)));

        List<Object[]> bioTemplateCount = persBioTemplateDao.countByTemplateNoIndexAndGroupByBioTypeAndVersionAndUserId(
            PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX, userId);// Object[]
        dataCount.putAll(countBioTemplate(bioTemplateCount));

        // 性别统计
        // 默认初始化男女人数，性别不是必填的，男女人数的和并不等于总人数
        dataCount.put("maleCount", "0");
        dataCount.put("feMaleCount", "0");
        dataCount.put("unknownCount", "0");
        // 此处查询出有没用的名称数据，不影响统计 add by max
        List<Object[]> genderCount = persPersonDao.countGenderByUserId(userId);
        for (Object[] objArray : genderCount) {
            // objArray[0] = maleCount （男） objArray[0] = feMaleCount （女）
            dataCount.put(objArray[0] + "", objArray[1] + "");
        }
        dataCount.put("cropFaceCount", String.valueOf(persBioPhotoDao.countByUserId(userId)));
        return dataCount;
    }

    @Override
    public Map<String, String> dataCountByDeptIdAndAuthUserFilter(String sessionId, String deptIds) {
        Map<String, String> dataCount = new HashMap<String, String>();

        if (StringUtils.isNotBlank(deptIds)) {
            List<List<String>> deptIdList =
                CollectionUtil.split(CollectionUtil.strToList(deptIds), CollectionUtil.splitSize);
            Integer personCount = 0;
            long cardCount = 0;
            List<Object[]> bioTemplateCounts = new ArrayList<>();
            Integer maleCount = 0;
            Integer feMaleCount = 0;
            Integer unknownCount = 0;
            long cropFaceCount = 0;
            for (List<String> deptId : deptIdList) {
                personCount = personCount
                    + persPersonDao.countByPersonTypeAndDeptIdIn(PersConstants.PERSON_TYPE_EMPLOYEE, deptId);
                cardCount = cardCount + persCardDao.countByCardStateAndDeptIdIn(PersConstants.CARD_VALID, deptId);
                List<Object[]> bioTemplateCount =
                    persBioTemplateDao.countByTemplateNoIndexAndGroupByBioTypeAndVersionAndDeptIds(
                        PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX, deptId);
                bioTemplateCounts.addAll(bioTemplateCount);
                // 此处查询出有没用的名称数据，不影响统计 add by max
                List<Object[]> genderCount = persPersonDao.countGenderByDeptIds(deptId);
                for (Object[] objArray : genderCount) {
                    // objArray[0] = maleCount （男） objArray[0] = feMaleCount （女）
                    switch (objArray[0] + "") {
                        case "maleCount":
                            maleCount = maleCount + Integer.parseInt(objArray[1].toString());
                            break;
                        case "feMaleCount":
                            feMaleCount = feMaleCount + Integer.parseInt(objArray[1].toString());
                            break;
                        case "unknownCount":
                            unknownCount = unknownCount + Integer.parseInt(objArray[1].toString());
                            break;
                    }
                }
                cropFaceCount = cropFaceCount + persBioPhotoDao.countByDeptIdIn(deptId);
            }

            // 人员的统计
            dataCount.put("personCount", String.valueOf(personCount));
            // 卡统计
            dataCount.put("cardCount", String.valueOf(cardCount));

            dataCount.putAll(countBioTemplate(bioTemplateCounts));

            // 性别统计
            // 默认初始化男女人数，性别不是必填的，男女人数的和并不等于总人数
            dataCount.put("maleCount", maleCount.toString());
            dataCount.put("feMaleCount", feMaleCount.toString());
            dataCount.put("unknownCount", unknownCount.toString());
            dataCount.put("cropFaceCount", String.valueOf(cropFaceCount));
        } else {
            String userId = authUserService.getUserIdBySessionId(sessionId);
            if (StringUtils.isNotBlank(userId)) {
                dataCount = dataCountByAuthUserFilter(userId);
            } else {
                dataCount = dataCount();
            }
        }
        return dataCount;
    }

    private Map<String, String> countBioTemplate(List<Object[]> bioTemplateCount) {
        Map<String, String> dataCount = new HashMap<String, String>();
        // 生物模板数量统计
        Map<String, String> fpVersion = new HashMap<>();
        Map<String, String> fvVersion = new HashMap<>();
        Map<String, String> faceVersion = new HashMap<>();
        Map<String, String> plamVersion = new HashMap<>();
        Map<String, String> vislightVersion = new HashMap<>();
        Map<String, String> plam10Version = new HashMap<>();
        Map<String, String> irisVersion = new HashMap<>();
        Integer count = 0;
        // 下标 0:为 bioType, 1：为 count, 2:version
        for (Object[] objArray : bioTemplateCount) {
            if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.FP_BIO_TYPE) {
                if (fpVersion.containsKey(objArray[2].toString())) {
                    count = Integer.parseInt(objArray[1].toString())
                        + Integer.parseInt(fpVersion.get(objArray[2].toString()));
                    fpVersion.put(objArray[2].toString(), count.toString());
                } else {
                    fpVersion.put(objArray[2].toString(), objArray[1].toString());
                }
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.VEIN_BIO_TYPE) {
                if (fvVersion.containsKey(objArray[2].toString())) {
                    count = Integer.parseInt(objArray[1].toString())
                        + Integer.parseInt(fvVersion.get(objArray[2].toString()));
                    fvVersion.put(objArray[2].toString(), count.toString());
                } else {
                    fvVersion.put(objArray[2].toString(), objArray[1].toString());
                }
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.FACE_BIO_TYPE) {
                if (faceVersion.containsKey(objArray[2].toString())) {
                    count = Integer.parseInt(objArray[1].toString())
                        + Integer.parseInt(faceVersion.get(objArray[2].toString()));
                    faceVersion.put(objArray[2].toString(), count.toString());
                } else {
                    faceVersion.put(objArray[2].toString(), objArray[1].toString());
                }
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.PALM_BIO_TYPE) {
                if (plamVersion.containsKey(objArray[2].toString())) {
                    count = Integer.parseInt(objArray[1].toString())
                        + Integer.parseInt(plamVersion.get(objArray[2].toString()));
                    plamVersion.put(objArray[2].toString(), count.toString());
                } else {
                    plamVersion.put(objArray[2].toString(), objArray[1].toString());
                }
            } else if (Integer.parseInt(objArray[0].toString()) == BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE) {
                if (vislightVersion.containsKey(objArray[2].toString())) {
                    count = Integer.parseInt(objArray[1].toString())
                        + Integer.parseInt(vislightVersion.get(objArray[2].toString()));
                    vislightVersion.put(objArray[2].toString(), count.toString());
                } else {
                    vislightVersion.put(objArray[2].toString(), objArray[1].toString());
                }
            } else if (Integer.parseInt(objArray[0].toString()) == PersConstants.PALM_BIO_TYPE_10) {
                if (plam10Version.containsKey(objArray[2].toString())) {
                    count = Integer.parseInt(objArray[1].toString())
                        + Integer.parseInt(plam10Version.get(objArray[2].toString()));
                    plam10Version.put(objArray[2].toString(), count.toString());
                } else {
                    plam10Version.put(objArray[2].toString(), objArray[1].toString());
                }
            } else if (Integer.parseInt(objArray[0].toString()) == PersConstants.IRIS_BIO_TYPE) {
                if (irisVersion.containsKey(objArray[2].toString())) {
                    count = Integer.parseInt(objArray[1].toString())
                        + Integer.parseInt(irisVersion.get(objArray[2].toString()));
                    irisVersion.put(objArray[2].toString(), count.toString());
                } else {
                    irisVersion.put(objArray[2].toString(), objArray[1].toString());
                }
            }
        }

        dataCount.put("fpCount", JSON.toJSONString(fpVersion));
        dataCount.put("fvCount", JSON.toJSONString(fvVersion));
        dataCount.put("faceCount", JSON.toJSONString(faceVersion));
        dataCount.put("palmCount", JSON.toJSONString(plamVersion));
        dataCount.put("vislightCount", JSON.toJSONString(vislightVersion));
        dataCount.put("palm10Count", JSON.toJSONString(plam10Version));
        dataCount.put("irisCount", JSON.toJSONString(irisVersion));
        return dataCount;
    }

    @Override
    public Long getMaxPinLenth() {
        Long maxPinLenth = persPersonDao.getMaxPinLength();
        return Objects.isNull(maxPinLenth) ? 0 : maxPinLenth;
    }

    @Override
    public Long getMaxPin() {
        Long maxPin = persPersonDao.getMaxPin();
        return Objects.isNull(maxPin) ? 0 : maxPin;
    }

    @Override
    public String getIncPoint() {
        return persCacheManager.getAutoPin();
    }

    @Override
    public void batchPositionChange(String ids, String positionId, String changeReason) {
        PersPosition position = persPositionDao.findById(positionId).get();
        List<PersPerson> pers = persPersonDao.findByIdIn(CollectionUtil.strToList(ids));
        pers.stream().forEach(p -> {
            p.setPersPosition(position);
            persPersonDao.save(p);
        });
    }

    @Override
    public void batchDeptChange(String ids, String deptId, String changeReason, boolean flag) {
        Collection<String> personIdList = CollectionUtil.strToList(ids);
        List<String> personIds = new ArrayList<>(personIdList.size());
        Integer count = persPersonDao.countByIdInAndEnabledCredential((List<String>)personIdList, false);
        if (count > 0) {
            throw ZKBusinessException.warnException("pers_person_selectDisabledNotOp");

        }
        // CompletableFuture<Boolean> future = CompletableFuture.supplyAsync(() -> {
        List<PersPerson> persons = persPersonDao.findByIdIn(personIdList);
        persons.stream().forEach(person -> {
            // 部门不同
            if (!deptId.equals(person.getDeptId())) {
                person.setDeptId(deptId);
                persPersonDao.save(person);

                // 变动人员部门信息
                PersPersonChangeItem persPersonChange = new PersPersonChangeItem();
                persPersonChange.setDeptId(deptId);
                persPersonChange.setPersonId(person.getId());
                persPersonChange.setChangeReason(changeReason);
                persPersonChangeService.saveItem(persPersonChange);

            }
            personIds.add(person.getId());
        });
        // return true;
        // });
        //
        // try {
        // boolean b = future.get();
        // if (b) {
        // //通知其他模块部门调整
        pers2OtherService.pushBatchDeptChange(personIds, deptId, flag);
        // }
        // } catch (Exception e) {
        // logger.error("edit save person error", e);
        // if (e.getCause() instanceof ZKBusinessException) {
        // throw new ZKBusinessException(e.getCause().getMessage());
        // }
        // }
    }

    @Override
    public List<PersPersonSelectItem> findNoCardPersonItem(String sessionId, PersPersonSelectItem condition) {
        buildCondition(sessionId, condition);
        StringBuilder stringBuilder = new StringBuilder();
        condition.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        stringBuilder.append(SQLUtil.getSqlByItem(condition));
        int orderByIndex = stringBuilder.indexOf("ORDER BY");
        stringBuilder.insert(orderByIndex,
            " AND t.ID NOT IN(SELECT c.PERSON_ID FROM PERS_CARD c GROUP BY c.PERSON_ID)");
        Pager pager = persPersonDao.getItemsBySql(condition.getClass(), stringBuilder.toString(), 0, 300);
        List<PersPersonSelectItem> list = (List<PersPersonSelectItem>)pager.getData();
        String deptIds = CollectionUtil.getPropertys(list, PersPersonSelectItem::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        list.forEach(item -> {
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
        });
        return (List<PersPersonSelectItem>)pager.getData();
    }

    @Override
    public Pager findNoCardPerson(String sessionId, PersNoCardPersonItem condition, int page, int size) {
        condition.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        condition.setCardState(PersConstants.CARD_VALID);
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager pager = persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        List<PersNoCardPersonItem> list = (List<PersNoCardPersonItem>)pager.getData();
        String deptIds = CollectionUtil.getPropertys(list, PersNoCardPersonItem::getDeptId);

        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        list.forEach(item -> {
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptName(dept.getName());
        });
        return pager;
    }

    @Override
    public Pager findPersonSelectItem(String sessionId, PersPersonSelectItem condition, int page, int size) {
        buildCondition(sessionId, condition);
        return getPersonSelectItemPager(condition, page, size);
    }

    @Override
    public Pager findIsNotNullPersonSelectItem(String sessionId, PersPersonSelectItem condition, int page, int size) {
        buildCondition(sessionId, condition);
        StringBuilder stringBuilder = new StringBuilder();
        condition.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        stringBuilder.append(SQLUtil.getSqlByItem(condition));
        if (StringUtils.isNotBlank(condition.getIsNotNullField())) {
            String[] notNullFields = condition.getIsNotNullField().split(",");
            StringBuilder sqlStr = new StringBuilder();

            int orderByIndex = stringBuilder.indexOf("ORDER BY");
            String dataSource = DataSourceConfig.getDbType();
            if (ZKConstant.ORACLE.equals(dataSource)) {
                for (int i = 0; i < notNullFields.length; i++) {
                    if (i == 0) {
                        sqlStr.append("t." + notNullFields[i] + " is not null ");
                    } else {
                        sqlStr.append(" OR t." + notNullFields[i] + " is not null ");
                    }
                }
            } else {
                for (int i = 0; i < notNullFields.length; i++) {
                    if (i == 0) {
                        sqlStr.append("t." + notNullFields[i] + " is not null AND t." + notNullFields[i] + " <> '' ");
                    } else {
                        sqlStr
                            .append(" OR t." + notNullFields[i] + " is not null AND t." + notNullFields[i] + " <> '' ");
                    }
                }
            }
            stringBuilder.insert(orderByIndex, " AND (" + sqlStr + ")");
        }
        Pager pager = persPersonDao.getItemsBySql(condition.getClass(), stringBuilder.toString(), page, size);
        List<PersPersonSelectItem> list = (List<PersPersonSelectItem>)pager.getData();
        String deptIds = CollectionUtil.getPropertys(list, PersPersonSelectItem::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        list.forEach(item -> {
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
        });
        return pager;
    }

    private Pager getPersonSelectItemPager(PersPersonSelectItem condition, int page, int size) {
        StringBuilder stringBuilder = new StringBuilder();
        condition.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        stringBuilder.append(SQLUtil.getSqlByItem(condition));
        if (StringUtils.isNotBlank(condition.getType())) {
            int orderByIndex = stringBuilder.indexOf("ORDER BY");
            if (StringUtils.isNotBlank(condition.getLinkId())) {
                stringBuilder.insert(orderByIndex, String.format(
                    " AND EXISTS (SELECT ppl.PERSON_ID FROM PERS_PERSON_LINK ppl WHERE t.ID = ppl.PERSON_ID AND ppl.type = '%s' AND ppl.LINK_ID = '%s') ",
                    condition.getType(), condition.getLinkId()));
            } else if (StringUtils.isNotBlank(condition.getNotInlinkId())) {
                stringBuilder.insert(orderByIndex, String.format(
                    " AND NOT EXISTS (SELECT ppl.PERSON_ID FROM PERS_PERSON_LINK ppl WHERE t.ID = ppl.PERSON_ID AND ppl.type = '%s' AND ppl.LINK_ID in ('%s')) ",
                    condition.getType(), condition.getNotInlinkId()));
            }
        }

        Pager pager = persPersonDao.getItemsBySql(condition.getClass(), stringBuilder.toString(), page, size);
        List<PersPersonSelectItem> list = (List<PersPersonSelectItem>)pager.getData();
        String deptIds = CollectionUtil.getPropertys(list, PersPersonSelectItem::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        List<String> personList = (List<String>)CollectionUtil.getItemIdsList(list);
        List<PersCardItem> persCards = persCardService.getMasterCardByPersonIdList(personList);
        Map<String, PersCardItem> persCardMap = CollectionUtil.listToKeyMap(persCards, PersCardItem::getPersonId);
        list.forEach(item -> {
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
            PersCardItem card = persCardMap.get(item.getId());
            if (Objects.nonNull(card)) {
                item.setCardNo(card.getCardNo());
            }
        });
        return pager;
    }

    @Override
    public Pager findPersonSelectItemAndIsIncludeLower(String sessionId, PersPersonSelectItem condition, int page,
        int size) {
        buildDeptByIsIncludeLower(sessionId, condition);
        return getPersonSelectItemPager(condition, page, size);
    }

    @Override
    public Pager findItemByOrConditionForAtt(PersPersonSelectItem condition, int pageNo, int pageSize) {
        condition.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        StringBuffer sqlBuffer = new StringBuffer();
        if (StringUtils.isNotBlank(condition.getInId()) && StringUtils.isNotBlank(condition.getInlinkId())
            && StringUtils.isNotBlank(condition.getOrInDeptId())) {
            // 置空条件，自定义组装OR条件查询
            String inId = condition.getInId();
            condition.setInId("");
            String inlinkId = condition.getInlinkId();
            condition.setInlinkId("");
            String orInDeptId = condition.getOrInDeptId();
            condition.setOrInDeptId("");
            // 组装SQL语句
            sqlBuffer.append(SQLUtil.getSqlByItem(condition));
            // OR条件
            StringBuffer insertBuffer = new StringBuffer(" AND (");
            insertBuffer.append(String.format(
                " EXISTS (SELECT ppl.PERSON_ID FROM PERS_PERSON_LINK ppl WHERE t.ID = ppl.PERSON_ID AND ppl.type = '%s'",
                condition.getType()));
            insertBuffer.append(buildSQLIn("AND", "ppl.LINK_ID", "IN", inlinkId));
            insertBuffer.append(")");
            insertBuffer.append(buildSQLIn("OR", "t.ID", "IN", inId));
            insertBuffer.append(buildSQLIn("OR", "t.AUTH_DEPT_ID", "IN", orInDeptId));
            insertBuffer.append(")");

            int orderByIndex = sqlBuffer.indexOf("ORDER BY");
            sqlBuffer.insert(orderByIndex, insertBuffer.toString());
        } else {
            sqlBuffer.append(SQLUtil.getSqlByItem(condition));
            int orderByIndex = sqlBuffer.indexOf("ORDER BY");
            if (StringUtils.isNotBlank(condition.getLinkId())) {
                sqlBuffer.insert(orderByIndex, String.format(
                    " AND EXISTS (SELECT ppl.PERSON_ID FROM PERS_PERSON_LINK ppl WHERE t.ID = ppl.PERSON_ID AND ppl.type = '%s' AND ppl.LINK_ID = '%s') ",
                    condition.getType(), condition.getLinkId()));
            } else if (StringUtils.isNotBlank(condition.getNotInlinkId())) {
                StringBuffer insertBuffer = new StringBuffer("");
                insertBuffer.append(String.format(
                    " AND NOT EXISTS (SELECT ppl.PERSON_ID FROM PERS_PERSON_LINK ppl WHERE t.ID = ppl.PERSON_ID AND ppl.type = '%s'",
                    condition.getType()));
                insertBuffer.append(buildSQLIn("AND", "ppl.LINK_ID", "IN", condition.getNotInlinkId()));
                insertBuffer.append(")");
                sqlBuffer.insert(orderByIndex, insertBuffer.toString());
            }
        }
        return persPersonDao.getItemsBySql(condition.getClass(), sqlBuffer.toString(), pageNo, pageSize);
    }

    /**
     * 组装IN和NOT IN语句超过1000
     *
     * @param linkTag AND、OR
     * @param name T.*
     * @param tag IN、NOT IN
     * @param fieldValue "1,2,3,4,5"
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/8 10:08
     */
    private static String buildSQLIn(String linkTag, String name, String tag, String fieldValue) {
        // 如果超过一千
        String[] array = fieldValue.split(",");
        int length = array.length;
        if (array.length < 1000) {
            return " " + linkTag + " " + name + " " + tag + " (" + StrUtil.withSingleQuote(fieldValue) + ")";
        } else {
            List<String> fieldValueList = new ArrayList<>();
            StringBuffer sb = new StringBuffer();
            for (int i = 0; i < length; i++) {
                sb.append(array[i] + ",");
                if ((i + 1) % 999 == 0) {
                    fieldValueList.add(sb.toString());
                    sb = new StringBuffer();
                }
            }
            if (sb.length() > 0) {
                fieldValueList.add(sb.toString());
            }

            if ("IN".equals(StrUtil.toUpperCase(tag))) {
                sb = new StringBuffer(" " + linkTag + " (1=0 ");
                for (String fl : fieldValueList) {
                    sb.append("OR (" + name + " " + tag + " (" + StrUtil.withSingleQuote(fl) + "))");
                }
                sb.append(")");
            } else if ("NOT IN".equals(StrUtil.toUpperCase(tag))) {
                sb = new StringBuffer("");
                for (String fl : fieldValueList) {
                    sb.append(" " + linkTag + " (" + name + " " + tag + " (" + StrUtil.withSingleQuote(fl) + "))");
                }
            }
            return sb.toString();
        }
    }

    private void buildDeptByIsIncludeLower(String sessionId, PersPersonSelectItem condition) {
        // 部门的处理
        if (StringUtils.isBlank(condition.getDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, null,
                condition.getDeptCode(), condition.getDeptName()));
        } else {
            // 是否包含子部门
            if ("true".equals(condition.getIsIncludeLower())) {
                condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId,
                    condition.getDeptId(), condition.getDeptCode(), condition.getDeptName()));
                condition.setDeptId(null);
            }
        }
    }

    @Override
    public List<PersPersonSelectItem> getPersonSelectItem(String sessionId, PersPersonSelectItem condition,
        int beginIndex, int endIndex) {
        buildCondition(sessionId, condition);
        StringBuilder stringBuilder = new StringBuilder();
        condition.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        stringBuilder.append(SQLUtil.getSqlByItem(condition));
        if (StringUtils.isNotBlank(condition.getType())) {
            int orderByIndex = stringBuilder.indexOf("ORDER BY");
            if (StringUtils.isNotBlank(condition.getLinkId())) {
                stringBuilder.insert(orderByIndex, String.format(
                    " AND t.ID IN(SELECT ppl.PERSON_ID FROM PERS_PERSON_LINK ppl WHERE ppl.type = '%s' AND ppl.LINK_ID = '%s')",
                    condition.getType(), condition.getLinkId()));
            } else if (StringUtils.isNotBlank(condition.getNotInlinkId())) {
                stringBuilder.insert(orderByIndex, String.format(
                    " AND t.ID NOT IN(SELECT ppl.PERSON_ID FROM PERS_PERSON_LINK ppl WHERE ppl.type = '%s' AND ppl.LINK_ID = '%s')",
                    condition.getType(), condition.getNotInlinkId()));
            }
        }
        List<PersPersonSelectItem> list =
            persPersonDao.getItemsDataBySql(condition.getClass(), stringBuilder.toString(), beginIndex, endIndex, true);
        // 分割部门处理
        Collection<String> deptIdList = CollectionUtil.getPropertyList(list, PersPersonSelectItem::getDeptId, "-1");
        List<List<String>> deptIdsList = CollectionUtil.split(deptIdList, CollectionUtil.splitSize);
        List<AuthDepartmentItem> deptItems = new ArrayList<>(deptIdList.size());
        deptIdsList.forEach(deptIds -> {
            deptItems.addAll(authDepartmentService.getItemsByIds(deptIds));
        });
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);

        // 分割查询卡号
        List<List<String>> personList =
            CollectionUtil.getPropertyList(list, PersPersonSelectItem::getId, CollectionUtil.splitSize);
        List<PersCardItem> persCards = new ArrayList<>();
        personList.forEach(personIds -> {
            List<PersCardItem> masterCards = persCardService.getMasterCardByPersonIdList(personIds);
            if (!CollectionUtil.isEmpty(masterCards)) {
                persCards.addAll(masterCards);
            }
        });
        Map<String, PersCardItem> persCardMap = CollectionUtil.listToKeyMap(persCards, PersCardItem::getPersonId);
        list.forEach(item -> {
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
            PersCardItem card = persCardMap.get(item.getId());
            if (Objects.nonNull(card)) {
                item.setCardNo(card.getCardNo());
            }
        });
        return list;
    }

    @Override
    public Pager findPersonLinkSearchItem(String sessionId, PersPersonLinkSearchItem condition, int page, int size) {
        buildCondition(sessionId, condition);
        Pager pager = persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        List<PersPersonLinkSearchItem> list = (List<PersPersonLinkSearchItem>)pager.getData();
        String deptIds = CollectionUtil.getPropertys(list, PersPersonLinkSearchItem::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        list.forEach(item -> {
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
        });
        return pager;
    }

    @Override
    public List<PersPersonToAttAreaItem> findPersonToAttAreaItem(String sessionId, PersPersonToAttAreaItem condition) {
        buildCondition(sessionId, condition);
        List<PersPersonToAttAreaItem> items = (List<PersPersonToAttAreaItem>)persPersonDao
            .getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        String deptIds = CollectionUtil.getPropertys(items, PersPersonToAttAreaItem::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        items.forEach(item -> {
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
        });
        buildMasterCard(items);
        buildAttBioTemplateCount(items);
        return items;
    }

    @Override
    public boolean ckeckPwd(String personPwd) {
        return persPersonDao.countByPersonPwd(personPwd) > 0;
    }

    @Override
    public boolean checkPwd(String personId, String personPwd) {
        return persPersonDao.countByPersonPwdAndIdNot(personPwd, personId) > 0;
    }

    @Override
    public boolean checkForcePwd(String personPwd) {
        return pers2OtherService.checkForcePwd(personPwd);
    }

    @Override
    public boolean checkMailParam() {
        return baseMailService.completeMailInfo();
    }

    @Override
    public Map<String, String> getPinsByPersonIds(Collection<String> personIds) {
        PersPersonPinItem condition = new PersPersonPinItem();
        condition.setInId(StringUtils.join(personIds, ","));
        List<PersPersonPinItem> items =
            (List<PersPersonPinItem>)persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty())
            .map(list -> list.stream().collect(Collectors.toMap(PersPersonPinItem::getId, PersPersonPinItem::getPin)))
            .orElse(Collections.emptyMap());
    }

    @Override
    public Map<String, String> getPinsAndIdsByPins(Collection<String> pinList) {
        PersPersonPinItem condition = new PersPersonPinItem();
        condition.setInPin(StringUtils.join(pinList, ","));
        List<PersPersonPinItem> items =
            (List<PersPersonPinItem>)persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty())
            .map(list -> list.stream()
                .collect(Collectors.toMap(PersPersonPinItem::getPin, PersPersonPinItem::getId, (x, y) -> y)))
            .orElse(Collections.emptyMap());
    }

    @Override
    public List<String> getPersonIdByPersonIdAndLevelIdForAcc(List<String> personIds, List<String> levelIds) {
        return persPersonDao.getPersonIdByPersonIdAndLevelIdAndLinkType(personIds, levelIds, "ACC_LEVEL");
    }

    @Override
    public List<String> getPersonIdByPersonIdAndLevelIdForEle(List<String> personIds, List<String> levelIds) {
        return persPersonDao.getPersonIdByPersonIdAndLevelIdAndLinkType(personIds, levelIds, "ELE_LEVEL");
    }

    @Override
    public List<String> getPersonIdByPersonIdAndLevelIdForPsg(List<String> personIds, List<String> levelIds) {
        return persPersonDao.getPersonIdByPersonIdAndLevelIdAndLinkType(personIds, levelIds, "PSG_LEVEL");
    }

    @Override
    public int checkPinIncrement(String pinSupportIncrement) {
        int personCount = 0;
        if (Boolean.valueOf(baseSysParamService.getValByName("pers.pinSupportIncrement"))) {
            personCount = persPersonDao.countByPinLetter(true);
        }
        return personCount;
    }

    @Override
    public int checkPinIsExistLetters() {
        return persPersonDao.countByPinLetter(true);
    }

    @Override
    public List<PersPersonItem> getByPinLikeOrNameLike(String pin, String name) {
        if (StringUtils.isNotBlank(pin)) {
            pin = "%" + pin + "%";
        }
        if (StringUtils.isNotBlank(name)) {
            name = "%" + name + "%";
        }
        List<PersPerson> persPersonList = persPersonDao.findByPinLikeOrNameLike(pin, name);
        List<PersPersonItem> persPersonItemList = ModelUtil.copyListProperties(persPersonList, PersPersonItem.class);
        return persPersonItemList;
    }

    @Override
    public List<PersPersonItem> getPersPersonByDeptIds(Collection<String> deptIds) {
        PersPersonItem condition = new PersPersonItem();
        condition.setInDeptId(StrUtil.collectionToStr(deptIds));
        return (List<PersPersonItem>)persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public ZKResultMsg importData(List<PersPersonItem> itemList) {
        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("pers_import_overData", itemList.size());
        }
        // 基础变量定义
        // 系统支持的人员pin号长度
        int pinLength = Integer.parseInt(baseSysParamService.getValByName("pers.pinLen"));
        // pin号是否支持字母
        String pinSupportLetter = baseSysParamService.getValByName("pers.pinSupportLetter");
        // 支持多卡
        String cardsSupport = baseSysParamService.getValByName("pers.cardsSupport");
        // 卡号进制
        String cardHex = baseSysParamService.getValByName("pers.cardHex");
        // 卡号二进制长度
        int cardLen = Integer.parseInt(baseSysParamService.getValByName("pers.cardLen"));
        // 卡号最大值cardLen
        BigInteger maxCardNo = new BigInteger("2").pow(cardLen);
        // 性别的国际化，M男F女
        Map<String, String> genderMap = baseDictionaryValueService.getDictionaryValuesMap("sex");
        // 默认部门
        AuthDepartmentItem defautDept = authDepartmentService.getDefautDept();

        // 导入人员pin号的集合，用于过滤重复的pin号
        Set<String> uniquePinSet = new HashSet<>();
        // 导入数据中所有人员的卡号集合，用于去重,key: cardNo value:pin
        HashMap<String, String> cardNoAndPinMap = new HashMap<>(itemList.size());
        // 人员工号跟卡的关系，key:pin value: cardNos
        HashMap<String, List<String>> pinAndCardsMap = new HashMap<>(itemList.size());
        // 部门编码收集
        Set<String> deptCodeSet = new HashSet<>();
        // 部门编码收集
        Set<String> deptNameSet = new HashSet<>();
        int importSize = itemList.size();
        int beginProgress = 20;
        // 做数据校验检查并移除异常数据以及收集关键数据，方便in查询
        Iterator<PersPersonItem> itemIterator = itemList.iterator();
        while (itemIterator.hasNext()) {
            PersPersonItem personItem = itemIterator.next();

            // 人员编号非空校验
            if (StringUtils.isBlank(personItem.getPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinNotEmpty")));
                itemIterator.remove();
                continue;
            }

            if (PersRegularUtil.zeroPattern.matcher(personItem.getPin()).matches()) {
                // 不能以0开头
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinStartWithZero")));
                itemIterator.remove();
                continue;
            }

            // 判断人员pin号长度
            if (personItem.getPin().length() > pinLength) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinTooLong", personItem.getPin())));
                itemIterator.remove();
                continue;
            }

            if ("false".equals(pinSupportLetter) && !StringUtils.isNumeric(personItem.getPin())) {
                // 人员编号不支持字母！人员编号为：{0}
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinNotSupportLetter", personItem.getPin())));
                itemIterator.remove();
                continue;
            }
            if ("true".equals(pinSupportLetter) && !PersRegularUtil.hasNumberOrLetter(personItem.getPin())) {
                // 人员编号只支持数字和字母组合。人员编号为：{0}
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinNotSupportNonAlphabetic", personItem.getPin())));
                itemIterator.remove();
                continue;
            }
            // 唯一校验
            if (!uniquePinSet.add(personItem.getPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinIsRepeat", personItem.getPin())));
                itemIterator.remove();
                continue;
            }

            // 姓名不能包含特殊字符
            if (StringUtils.isNotBlank(personItem.getName()) && ValidateUtils.isSpecialChar(personItem.getName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_nameError", personItem.getPin())));
                itemIterator.remove();
                continue;
            }

            // 姓名不能包含特殊字符
            if (StringUtils.isNotBlank(personItem.getLastName())
                && ValidateUtils.isSpecialChar(personItem.getLastName())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_nameError", personItem.getPin())));
                itemIterator.remove();
                continue;
            }

            // 卡号处理。
            if (StringUtils.isNotBlank(personItem.getCardNos())) {
                String[] cardNoAry = personItem.getCardNos().split(",");
                if ("false".equals(cardsSupport) && cardNoAry.length > 1) {
                    // 当前系统不支持多卡
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_cardsNotSupport", personItem.getPin())));
                    itemIterator.remove();
                    continue;
                }
                List<String> cardNoList = new ArrayList<>();
                for (String cardNo : cardNoAry) {
                    // 过滤卡号为空的数据
                    if (StringUtils.isBlank(cardNo)) {
                        continue;
                    }

                    if (PersRegularUtil.zeroPattern.matcher(cardNo).matches()) {
                        // 不能以0开头
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_cardNoStartWithZero")));
                        break;
                    }

                    // 参数设置为十进制卡，不允许导入16进制卡号
                    if (PersConstants.CARD_DECIMAL.equals(cardHex)) {
                        // 过滤掉不是数字的数据
                        if (!StringUtils.isNumeric(cardNo)) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_cardNoFormatErrors", cardNo)));
                            break;
                        }
                        // 卡号超出最大值的数据
                        if ((new BigInteger(cardNo)).compareTo(maxCardNo) > 0) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_cardTooLong", cardNo)));
                            break;
                        }
                    } else if (PersConstants.CARD_HEXADECIMAL.equals(cardHex)) {
                        // 过滤掉不匹配十六进制的数据
                        if (!PersRegularUtil.isHex(cardNo)) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_cardNoFormatErrors", cardNo)));
                            break;
                        }
                        // 过滤卡号超出最大值的数据
                        if ((new BigInteger(cardNo, 16)).compareTo(maxCardNo) > 0) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_cardTooLong", cardNo)));
                            break;
                        }
                    }

                    // 人员卡号数量超过17个，主卡1张，副卡16张
                    if (cardNoList.size() >= 17) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_card_notMoreThanSixteen")));
                        break;
                    }

                    // 如果卡号跟其他人重复了，不允许导入数据
                    if (Objects.nonNull(cardNoAndPinMap.put(cardNo, personItem.getPin()))) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_cardExist", cardNo)));
                        break;
                    }
                    cardNoList.add(cardNo);
                }
                // 卡号数据有异常，这条人员数据就不导入
                if (cardNoAry.length != cardNoList.size()) {
                    itemIterator.remove();
                    continue;
                }

                if (!CollectionUtil.isEmpty(cardNoList)) {
                    pinAndCardsMap.put(personItem.getPin(), cardNoList);
                }
            }
            // 部门数据收集,有部门编码以部门编码为主
            if (StringUtils.isNotBlank(personItem.getDeptCode())) {
                deptCodeSet.add(personItem.getDeptCode());
            } else if (StringUtils.isNotBlank(personItem.getDeptName())) {
                deptNameSet.add(personItem.getDeptName());
            }
        }
        // 失败数量
        int faildCount = importSize - itemList.size();
        // 已存在数量
        int existCount = 0;
        // 保存的人员数量
        int saveCount = 0;

        // 查询人员数量
        Integer personSize = persPersonDao.countByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        // 加导入的人员数量
        personSize = personSize + uniquePinSet.size();

        Set<String> removePins = new HashSet<>();
        List<List<String>> pinsList = CollectionUtil.split(uniquePinSet, CollectionUtil.splitSize);
        for (List<String> pins : pinsList) {
            existCount += persPersonDao.countByPinIn(pins);
            List<String> leavePins = persLeavePersonDao.getPinListByPinIn(pins);
            if (!CollectionUtil.isEmpty(leavePins)) {
                removePins.addAll(leavePins);
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinLeaved", leavePins.get(0))));
            }
        }

        // 检查卡号是否已经在数据库当中，并且是否跟其他人的卡号重复了。
        if (!CollectionUtil.isEmpty(cardNoAndPinMap)) {
            List<List<String>> cardNos = CollectionUtil.split(cardNoAndPinMap.keySet(), CollectionUtil.splitSize);
            cardNos.forEach(cards -> {
                List<PersCard> existsCards = persCardDao.findByCardNoIn(cards);
                if (!CollectionUtil.isEmpty(existsCards)) {
                    existsCards.forEach(persCard -> {
                        String pin = cardNoAndPinMap.get(persCard.getCardNo());
                        if (!pin.equals(persCard.getPersonPin())) {
                            removePins.add(pin);
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_cardExist", persCard.getCardNo())));
                        }
                    });
                }
            });
        }

        // 把跟数据库冲突需要移除掉的数据进行移除
        if (!CollectionUtil.isEmpty(removePins)) {
            itemIterator = itemList.iterator();
            while (itemIterator.hasNext()) {
                PersPersonItem personItem = itemIterator.next();
                if (removePins.contains(personItem.getPin())) {
                    itemIterator.remove();
                    pinAndCardsMap.remove(personItem.getPin());
                }
            }
            // 失败数量
            faildCount += removePins.size();
        }

        if (!CollectionUtil.isEmpty(itemList)) {
            // 去除已存在的人员数量
            personSize = personSize - existCount;
            // 许可数量控制
            ResultCode resultCode = baseLicenseProvider.isCountOutRangePers(personSize);
            if (!ResultCode.SUCCESS.equals(resultCode)) {
                throw ZKBusinessException.warnException("pers_import_exceedLicense");
            }

            // 部门编码和ID的关系
            HashMap<String, String> deptCodeAndIdMap = new HashMap<>(deptCodeSet.size());
            if (!CollectionUtil.isEmpty(deptCodeSet)) {
                List<AuthDepartmentItem> itemByCodeIn = authDepartmentService.findItemByCodeIn(deptCodeSet);
                itemByCodeIn.forEach(item -> {
                    deptCodeAndIdMap.put(item.getCode(), item.getId());
                });
            }
            // 部门名称和ID的关系
            HashMap<String, String> deptNameAndIdMap = new HashMap<>(deptNameSet.size());
            if (!CollectionUtil.isEmpty(deptNameSet)) {
                List<AuthDepartmentItem> itemByNameIn = authDepartmentService.findItemByNameIn(deptNameSet);
                itemByNameIn.forEach(item -> {
                    // 此方式，由于部门名称不是唯一，导入的数据没有code的情况下，只有名称，会随机取其中名称的部门ID。
                    deptNameAndIdMap.put(item.getName(), item.getId());
                });
            }
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                I18nUtil.i18nCode("pers_import_dataCheck")));

            // 人员pin号和id的对应关系集合
            Map<String, String> pinAndIdMap = new HashMap<>(itemList.size());
            // 导入的数据中，所有pin已经存在的人员，key为pin号
            Map<String, PersPerson> existPersPersonMap = null;
            // 分批的人员pin号集合，用于批量保存到redis的pin号自增的key里
            List<String> pins = null;
            // 待入库的人员集合
            List<PersPerson> persPersonList = null;
            PersPerson persPerson = null;

            // 把导入的数据分割，分批处理，一次处理800人
            List<List<PersPersonItem>> personImportItemsList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
            float avgProgress = (float)60 / personImportItemsList.size();
            int i = 0;
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                I18nUtil.i18nCode("common_op_processing")));
            for (List<PersPersonItem> importItemList : personImportItemsList) {
                int progress = beginProgress + 10 + (int)(avgProgress * i++);
                progressCache.setProcess(new ProcessBean(progress, progress, ""));
                // 取出待导入数据中人员的pin号
                Collection<String> importPins =
                    CollectionUtil.getPropertyList(importItemList, PersPersonItem::getPin, "-1");

                // 根据pin号查出已经存在的人员
                existPersPersonMap = new HashMap<>(CollectionUtil.splitSize);
                List<PersPerson> existPersonList = persPersonDao.findByPinIn(importPins);
                if (existPersonList != null && existPersonList.size() > 0) {
                    existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPerson::getPin));
                }
                // 分批的人员pin号集合，用于批量保存到redis的pin号自增的key里
                pins = new ArrayList<>(CollectionUtil.splitSize);
                // 待入库的人员集合
                persPersonList = new ArrayList<>(CollectionUtil.splitSize);
                for (PersPersonItem importItem : importItemList) {

                    // 当pin号长度为9时，判断pin号是否“8”或“9”开头，这是访客和酒店用的
                    if (importItem.getPin().length() == 9
                        && (importItem.getPin().startsWith("8") || importItem.getPin().startsWith("9"))) {
                        continue;
                    }

                    if (StringUtils.isNumeric(importItem.getPin())) {
                        pins.add(importItem.getPin());
                    }

                    // 判断人员是否已经存在，不存在则新增
                    // 已存在的人员暂不进行设置部门ID，否则会有存在部门变更情况
                    persPerson = existPersPersonMap.get(importItem.getPin());
                    if (persPerson == null) {
                        persPerson = new PersPerson();
                        persPerson.setPin(importItem.getPin());
                        // 改人员为导入数据
                        persPerson.setIsFrom("PERS_USER_IMPORT_DATA");
                    }

                    // 导入的数据，默认以部门编码为优先，部门名称次之，前两个都没值，则设置默认部门
                    if (StringUtils.isNotBlank(importItem.getDeptCode())
                        && Objects.nonNull(deptCodeAndIdMap.get(importItem.getDeptCode()))) {
                        persPerson.setDeptId(deptCodeAndIdMap.get(importItem.getDeptCode()));
                    } else if (StringUtils.isNotBlank(importItem.getDeptName())
                        && Objects.nonNull(deptNameAndIdMap.get(importItem.getDeptName()))) {
                        persPerson.setDeptId(deptNameAndIdMap.get(importItem.getDeptName()));
                    } else {
                        persPerson.setDeptId(defautDept.getId());
                    }

                    // 人员的基本信息
                    persPerson.setName(importItem.getName() == null ? "" : importItem.getName())
                        .setLastName(importItem.getLastName() == null ? "" : importItem.getLastName())
                        .setNameSpell(
                            importItem.getName() == null ? "" : PinyinUtil.converterToSpell(importItem.getName()))
                        .setGender(converterToGender(importItem.getGender(), genderMap))
                        .setIdCard(importItem.getIdCard() == null ? "" : importItem.getIdCard())
                        .setMobilePhone(importItem.getMobilePhone() == null ? "" : importItem.getMobilePhone())
                        .setCarPlate(importItem.getCarPlate())
                        .setEmail(importItem.getEmail() == null ? "" : importItem.getEmail())
                        .setPersonPwd(importItem.getPersonPwd() == null ? "" : importItem.getPersonPwd())
                        .setSelfPwd(
                            importItem.getSelfPwd() == null ? PersConstants.PERSON_SELFPWD : importItem.getSelfPwd())
                        .setBirthday(importItem.getBirthday()).setHireDate(importItem.getHireDate())
                        .setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE).setStatus(PersConstants.PERSON_NORMAL)
                        .setExceptionFlag(PersConstants.PERSON_ISNOTEXCEPTION).setIsSendMail(false)
                        .setPinLetter(!StringUtils.isNumeric(importItem.getPin()));

                    persPersonList.add(persPerson);
                    saveCount++;
                }

                // 人员入库。
                if (persPersonList.size() > 0) {
                    persPersonDao.save(persPersonList);
                }

                // 保存人员pin和id的对应关系
                persPersonList.forEach(person -> {
                    pinAndIdMap.put(person.getPin(), person.getId());
                });
            }

            // 处理人员卡号
            if (!CollectionUtil.isEmpty(pinAndCardsMap)) {
                persCardService.batchSaveCards(pinAndCardsMap, pinAndIdMap);
            }
        }

        if (existCount > 0) {
            // 成功：%s 条，更新：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result3", saveCount, existCount, faildCount)));
        } else {
            // 成功：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result", saveCount, faildCount)));
        }
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Deprecated
    @Override
    public ZKResultMsg importPersonIno(List<PersPersonImportItem> itemList) {
        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("pers_import_overData", itemList.size());
        }
        // 基础变量定义
        // 系统支持的人员pin号长度
        int pinLength = Integer.parseInt(baseSysParamService.getValByName("pers.pinLen"));
        // pin号是否支持字母
        String pinSupportLetter = baseSysParamService.getValByName("pers.pinSupportLetter");
        // 支持多卡
        String cardsSupport = baseSysParamService.getValByName("pers.cardsSupport");
        // 卡号进制
        String cardHex = baseSysParamService.getValByName("pers.cardHex");
        // 卡号二进制长度
        int cardLen = Integer.parseInt(baseSysParamService.getValByName("pers.cardLen"));
        // 离职人员保留pin号
        String pinRetain = baseSysParamService.getValByName("pers.pinRetain");
        // 卡号最大值cardLen
        BigInteger maxCardNo = new BigInteger("2").pow(cardLen);
        // 性别的国际化，M男F女
        Map<String, String> genderMap = baseDictionaryValueService.getDictionaryValuesMap("sex");
        // 导入人员pin号的集合，用于过滤重复的pin号
        Set<String> uniquePinSet = new HashSet<>();
        // 离职人员pin
        Set<String> leavePins = new HashSet<>();
        // 导入数据中所有人员的卡号集合，用于去重,key: cardNo value:pin
        HashMap<String, String> cardNoAndPinMap = new HashMap<>(itemList.size());
        // 人员工号跟卡的关系，key:pin value: cardNos
        HashMap<String, List<String>> pinAndCardsMap = new HashMap<>(itemList.size());

        // 人员工号跟自定义属性的关系，key:pin value: PersAttributeExtItem
        HashMap<String, PersAttributeExtItem> pinAndAttrExtMap = new HashMap<>(itemList.size());
        List<PersAttributeExtItem> persAttributeExtItemList = new ArrayList<>();
        // 人员工号跟自定义属性的关系，key:pin value: PersCertificateItem
        HashMap<String, PersCertificateItem> pinAndCertMap = new HashMap<>(itemList.size());
        List<PersCertificateItem> persCertificateItemList = new ArrayList<>();
        List<BaseDictionaryValueItem> certTypeList = baseDictionaryValueService.getDictionaryValues("certificateType");
        HashMap<String, String> certNumbers = new HashMap<>(itemList.size());
        String certNumber = null;
        String certType = null;
        // 系统已存在证件 key:dictValue value:code
        Map<String, String> certMap = new HashMap<>();
        for (BaseDictionaryValueItem certTypeItem : certTypeList) {
            certMap.put(certTypeItem.getDictValue(), certTypeItem.getCode());
        }
        // 导入数据中所有人员的车牌集合，用于去重,key: carPlate value:pin
        HashMap<String, String> carPlateAndPinMap = new HashMap<>(itemList.size());
        // 人员工号跟车牌的关系，key:pin value: carPlates
        HashMap<String, List<String>> pinAndCarPlatesMap = new HashMap<>(itemList.size());

        // 导入数据中所有人员的邮箱集合，用于去重,key: email value:pin
        HashMap<String, String> emailAndPinMap = new HashMap<>(itemList.size());
        // 导入数据中所有人员的手机号码集合，用于去重,key: mobilePhone value:pin
        HashMap<String, String> mobilePhoneAndPinMap = new HashMap<>(itemList.size());

        int importSize = itemList.size();
        int beginProgress = 20;

        // 导入的数据中，所有pin已经存在的人员，key为pin号
        Map<String, PersPerson> existPersPersonMap = new HashMap<>(itemList.size());
        // 导入的数据中，所有pin已经存在的卡号，key为pin号
        Map<String, PersCard> existsCardMap = new HashMap<>(itemList.size());
        // 导入的数据中，所有pin已经存在的证件，key为pin号
        Map<String, PersCertificateItem> existsCertificateItemMap = new HashMap<>(itemList.size());
        // 导入的数据中，所有pin已经存在的自定义属性，key为pin号
        Map<String, PersAttributeExt> existsAttributeExtMap = new HashMap<>(itemList.size());
        // 导入的数据中，所有pin已经存在的车牌，key为pin号
        Map<String, Park4PersPersonItem> existsPark4PersPersonItemMap = new HashMap<>(itemList.size());
        // 数据库中，已经存在的邮箱
        Set<String> existsEmails = new HashSet<>();
        // 数据库中，已经存在的手机号码
        Set<String> existsMobilePhones = new HashSet<>();
        // 数据库中，已经存在的卡号
        Set<String> existsCardNos = new HashSet<>();
        // 数据库中，已经存在的车牌
        Set<String> existsCarPlates = new HashSet<>();

        // 已存在数量
        int existCount = 0;
        // 判断是否存在停车模块
        boolean park4PersPersonServiceExist = Objects.nonNull(park4PersPersonService);

        // 分批处理，一次处理800人 已存在人员只更新未赋值字段
        List<List<PersPersonImportItem>> personImportItemsList =
            CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<PersPersonImportItem> importItemList : personImportItemsList) {
            // 取出待导入数据中人员的pin号
            Collection<String> importPins =
                CollectionUtil.getPropertyList(importItemList, PersPersonImportItem::getPin, "-1");
            // 根据pin号查出已经存在的人员
            List<PersPerson> existPersonList = persPersonDao.findByPinIn(importPins);
            if (existPersonList != null && existPersonList.size() > 0) {
                existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPerson::getPin));
            }

            // 离职人员保留pin号
            if ("true".equals(pinRetain)) {
                List<String> leavePinList = persLeavePersonDao.getPinListByPinIn(importPins);
                if (!CollectionUtil.isEmpty(leavePinList)) {
                    leavePins.addAll(leavePinList);
                }
            }

            // 根据pin号查出已经存在的卡号
            List<PersCard> existsCardList = persCardDao.findByPersonPinIn(importPins);
            if (existsCardList != null && existsCardList.size() > 0) {
                existsCardMap.putAll(CollectionUtil.listToKeyMap(existsCardList, PersCard::getPersonPin));
            }

            Collection<String> importCardNos = CollectionUtil.getPropertyList(importItemList,
                persItem -> (persItem.getCardNos() != null ? persItem.getCardNos().toLowerCase() : null), "-1");
            List<PersCard> existsPersonCardNos = persCardDao.findByCardNoIn(importCardNos);
            if (!CollectionUtil.isEmpty(existsPersonCardNos)) {
                existsCardNos.addAll(CollectionUtil.getPropertyList(existsPersonCardNos, PersCard::getCardNo, "-1"));
            }

            // 根据pin号查出已经存在的证件
            List<PersCertificateItem> existsCertificateList = persCertificateService.getItemByPersonPinIn(importPins);
            if (existsCertificateList != null && existsCertificateList.size() > 0) {
                existsCertificateItemMap
                    .putAll(CollectionUtil.listToKeyMap(existsCertificateList, PersCertificateItem::getPin));
            }

            // 根据人员id查出已经存在的自定义属性
            Collection<String> existPersonIdList =
                CollectionUtil.getPropertyList(existPersonList, PersPerson::getId, "-1");
            // 已存在人员id与人员关系 key为id
            Map<String, PersPerson> existIdAndPersPersonMap = new HashMap<>(existPersonIdList.size());
            existIdAndPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPerson::getId));
            List<PersAttributeExt> persAttributeExtList =
                persAttributeExtDao.findByPersonIdIn((List<String>)existPersonIdList);
            persAttributeExtList.forEach(persAttributeExt -> {
                if (existIdAndPersPersonMap.containsKey(persAttributeExt.getPersonId())) {
                    existsAttributeExtMap.put(existIdAndPersPersonMap.get(persAttributeExt.getPersonId()).getPin(),
                        persAttributeExt);
                }

            });
            // 根据人员pin查出已经存在的车牌
            if (park4PersPersonServiceExist) {
                List<Park4PersPersonItem> park4PersPersonItemList =
                    park4PersPersonService.findbyPersPersonPinIn((List<String>)importPins);
                if (park4PersPersonItemList != null && park4PersPersonItemList.size() > 0) {
                    existsPark4PersPersonItemMap.putAll(
                        CollectionUtil.listToKeyMap(park4PersPersonItemList, Park4PersPersonItem::getPersonPin));
                    existsCarPlates.addAll(CollectionUtil.getPropertyList(park4PersPersonItemList,
                        Park4PersPersonItem::getParkCarNumbers, "-1"));
                }
            }
            // 查出已经存在的邮箱
            Collection<String> importEmails =
                CollectionUtil.getPropertyList(importItemList, PersPersonImportItem::getEmail, "-1");
            List<PersPerson> existsPersonEmails = persPersonDao.getByEmailIn(importEmails);
            if (!CollectionUtil.isEmpty(existsPersonEmails)) {
                existsEmails.addAll(CollectionUtil.getPropertyList(existsPersonEmails, PersPerson::getEmail, "-1"));
            }
            // 查出已经存在的手机
            Collection<String> importMobilePhones =
                CollectionUtil.getPropertyList(importItemList, PersPersonImportItem::getMobilePhone, "-1");
            List<PersPerson> existsPersonMobilePhones = persPersonDao.getByMobilePhoneIn(importMobilePhones);
            if (!CollectionUtil.isEmpty(existsPersonMobilePhones)) {
                existsMobilePhones
                    .addAll(CollectionUtil.getPropertyList(existsPersonMobilePhones, PersPerson::getMobilePhone, "-1"));
            }

        }

        PersPerson persPerson = null;
        // pin号与待入库的人员关系 key为pin
        HashMap<String, PersPerson> pinAndPersPersonMap = new HashMap<>(itemList.size());

        // 部门编码收集
        Collection<String> deptCodeSet =
            CollectionUtil.getPropertyList(itemList, PersPersonImportItem::getDeptCode, "-1");
        // 部门ID和名称的关系
        HashMap<String, String> deptIdAndNameMap = new HashMap<>(deptCodeSet.size());
        // 部门编码和ID的关系
        HashMap<String, String> deptCodeAndIdMap = new HashMap<>(deptCodeSet.size());
        if (!CollectionUtil.isEmpty(deptCodeSet)) {
            List<List<String>> deptCodeList = CollectionUtil.split(deptCodeSet, CollectionUtil.splitSize);
            for (List<String> deptCodes : deptCodeList) {
                List<AuthDepartmentItem> itemByCodeIn = authDepartmentService.findItemByCodeIn(deptCodes);
                itemByCodeIn.forEach(item -> {
                    deptCodeAndIdMap.put(item.getCode(), item.getId());
                    deptIdAndNameMap.put(item.getId(), item.getName());
                });
            }
        }

        // 职位编码收集
        Collection<String> positionCodeSet =
            CollectionUtil.getPropertyList(itemList, PersPersonImportItem::getPositionCode, "-1");
        // 职位编码和ID的关系
        HashMap<String, PersPosition> codeAndPositionMap = new HashMap<>(positionCodeSet.size());
        if (!CollectionUtil.isEmpty(positionCodeSet)) {
            List<List<String>> positionCodeList = CollectionUtil.split(positionCodeSet, CollectionUtil.splitSize);
            for (List<String> positionCodes : positionCodeList) {
                List<PersPosition> positionByCodeIn = persPositionDao.findByCodeIn(positionCodes);
                positionByCodeIn.forEach(position -> {
                    codeAndPositionMap.put(position.getCode(), position);
                });
            }
        }

        // 做数据校验检查并移除异常数据以及收集关键数据，方便in查询
        // pin号校验
        Iterator<PersPersonImportItem> itemIterator = itemList.iterator();
        String carNumber = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        while (itemIterator.hasNext()) {
            PersPersonImportItem personItem = itemIterator.next();

            // 人员编号非空校验
            if (StringUtils.isBlank(personItem.getPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinNotEmpty"))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Personnel ID is empty");
                continue;
            }
            // 判断人员pin号长度
            if (personItem.getPin().length() > pinLength) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                        personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinTooLong", personItem.getPin()))));
                itemIterator.remove();
                logger
                    .info("Row " + personItem.getRowNum() + ": Personnel ID " + personItem.getPin() + " is too long!");
                continue;
            }
            if ("false".equals(pinSupportLetter)) {
                if (PersRegularUtil.zeroPattern.matcher(personItem.getPin()).matches()) {
                    // 不能以0开头
                    progressCache.setProcess(
                        ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                            personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinStartWithZero"))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": Personnel ID start with zero");
                    continue;
                } else if (!StringUtils.isNumeric(personItem.getPin())) {
                    // 人员编号只支持数字！人员编号为：{0}
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                            I18nUtil.i18nCode("pers_import_pinSupportNumber", personItem.getPin()))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum()
                        + ": Personnel ID only supports numbers! The Personnel ID is: " + personItem.getPin());
                    continue;
                }
            }
            if ("true".equals(pinSupportLetter) && !PersRegularUtil.hasNumberOrLetter(personItem.getPin())) {
                // 人员编号只支持数字和字母组合。人员编号为：{0}
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                        I18nUtil.i18nCode("pers_import_pinNotSupportNonAlphabetic", personItem.getPin()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum()
                    + ": Personnel ID does not support non-English letters!The personnel ID is: "
                    + personItem.getPin());
                continue;
            }
            // 唯一校验
            if (!uniquePinSet.add(personItem.getPin())) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                        personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinIsRepeat", personItem.getPin()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Personnel ID " + personItem.getPin() + " is repeated");
                continue;
            }
            // 离职人员保留pin号
            if (leavePins.contains(personItem.getPin())) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                        personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinLeaved", personItem.getPin()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Personnel ID " + personItem.getPin() + " has left");
                continue;
            }
            // 当pin号长度为9时，判断pin号是否“8”或“9”开头，这是访客和酒店用的
            if (personItem.getPin().length() == 9
                && (personItem.getPin().startsWith("8") || personItem.getPin().startsWith("9"))) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_person_pinFirstValid"))));
                itemIterator.remove();
                logger.info(
                    "Row " + personItem.getRowNum() + ": First character of the personnel number can not be 8 or 9");
                continue;
            }

            // 判断人员是否已经存在，不存在则新增
            // 已存在的人员已有值的字段不进行更新
            persPerson = existPersPersonMap.get(personItem.getPin());
            if (persPerson == null) {
                persPerson = new PersPerson();
                persPerson.setPin(personItem.getPin());
                persPerson.setNumberPin(PersPersonUtil.createNumberPin(persPerson.getPin()));
                // 人员为导入数据
                persPerson.setIsFrom("PERS_USER_IMPORT_DATA");
            }
            /**
             * 对导入的新数据和没有设置密码的旧数据设置默认密码 add by bob.liu 20190920
             */
            if (StringUtils.isBlank(persPerson.getSelfPwd())) {
                persPerson.setSelfPwd(PersConstants.PERSON_SELFPWD);
            }
            // 姓名校验
            if (StringUtils.isBlank(persPerson.getName())) {
                // 姓名不能包含特殊字符
                if (StringUtils.isNotBlank(personItem.getName()) && ValidateUtils.isSpecialChar(personItem.getName())) {
                    progressCache.setProcess(
                        ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                            personItem.getRowNum(), I18nUtil.i18nCode("pers_import_nameError", personItem.getPin()))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum()
                        + ": Lastname and Firstname can not contain special symbols!The personnel id is: "
                        + personItem.getPin());
                    continue;
                }

                // 姓名不能包含特殊字符
                if (StringUtils.isNotBlank(personItem.getLastName())
                    && ValidateUtils.isSpecialChar(personItem.getLastName())) {
                    progressCache.setProcess(
                        ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                            personItem.getRowNum(), I18nUtil.i18nCode("pers_import_nameError", personItem.getPin()))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum()
                        + ": Lastname and Firstname can not contain special symbols!The personnel id is: "
                        + personItem.getPin());
                    continue;
                }

                if (StringUtils.isNotBlank(personItem.getName())) {
                    personItem.setName(personItem.getName().trim().replaceAll("\t", ""));
                    persPerson.setName(personItem.getName());
                }
                if (StringUtils.isNotBlank(personItem.getLastName())) {
                    personItem.setLastName(personItem.getLastName().trim().replaceAll("\t", ""));
                    persPerson.setLastName(personItem.getLastName());
                }

            }

            // 生日日期校验
            if (persPerson.getBirthday() == null) {
                if (personItem.getBirthday() != null) {
                    persPerson.setBirthday(personItem.getBirthday());
                } else if (PersConstants.PERSON_DATE_ERROR.equals(personItem.getBirthdayStr())) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_import_birthdayError"))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": Date of Birth format error");
                    continue;
                }
            }

            // 邮箱校验
            if (StringUtils.isBlank(persPerson.getEmail())) {
                if (StringUtils.isNotBlank(personItem.getEmail())) {
                    if (!PersRegularUtil.emailPattern.matcher(personItem.getEmail()).matches()) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                            .i18nCode("pers_import_emailErrors", personItem.getRowNum(), personItem.getEmail())));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": Email address " + personItem.getEmail()
                            + " format error");
                        continue;
                    } else {
                        // 如果邮箱跟其他人重复了，不允许导入数据
                        if (Objects.nonNull(emailAndPinMap.put(personItem.getEmail(), personItem.getPin()))) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                                .i18nCode("pers_import_emailIsRepeat", personItem.getRowNum(), personItem.getEmail())));
                            itemIterator.remove();
                            logger.info("Row " + personItem.getRowNum() + ": The internal email address of the file "
                                + personItem.getEmail() + " has been repeated");
                            continue;
                        }

                        if (existsEmails.contains(personItem.getEmail())) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_emailIsExist", personItem.getEmail()))));
                            itemIterator.remove();
                            logger.info("Row " + personItem.getRowNum() + ": Email address " + personItem.getEmail()
                                + " already exists");
                            continue;
                        }

                        persPerson.setEmail(personItem.getEmail());
                    }
                }
            }

            if (StringUtils.isBlank(persPerson.getMobilePhone())) {
                if (StringUtils.isNotBlank(personItem.getMobilePhone())) {
                    // 如果手机号码跟其他人重复了，不允许导入数据
                    if (Objects.nonNull(mobilePhoneAndPinMap.put(personItem.getMobilePhone(), personItem.getPin()))) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fileMobilePhoneRepeat", personItem.getRowNum(),
                                personItem.getMobilePhone())));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": The internal mobilePhone of the file "
                            + personItem.getMobilePhone() + " has been repeated");
                        continue;
                    }

                    if (existsMobilePhones.contains(personItem.getMobilePhone())) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_h5_personMobileRepeat"))));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": mobilePhone " + personItem.getMobilePhone()
                            + " already exists");
                        continue;
                    }

                    persPerson.setMobilePhone(personItem.getMobilePhone());
                }
            }

            if (StringUtils.isBlank(persPerson.getDeptId())) {
                // 导入的数据，默认以部门编码为优先,部门编码没值，则设置默认部门
                if (StringUtils.isNotBlank(personItem.getDeptCode())
                    && Objects.nonNull(deptCodeAndIdMap.get(personItem.getDeptCode()))) {
                    persPerson.setDeptId(deptCodeAndIdMap.get(personItem.getDeptCode()));
                } else {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_import_deptNotExist"))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": The department does not exist");
                    continue;
                }
            }

            if (persPerson.getPersPosition() == null && StringUtils.isNotBlank(personItem.getPositionCode())) {
                // 导入的数据，默认以使用职位编码
                if (Objects.nonNull(codeAndPositionMap.get(personItem.getPositionCode()))) {
                    persPerson.setPersPosition(codeAndPositionMap.get(personItem.getPositionCode()));
                } else {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_position_notExist"))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": The position does not exist");
                    continue;
                }
            }

            // 证件处理
            if (existsCertificateItemMap.get(personItem.getPin()) == null) {
                certNumber = personItem.getCertNumber();
                certType = certMap.get(personItem.getCertName());
                // 只有证件号没有证件类型
                if (StringUtils.isNotBlank(certNumber) && StringUtils.isBlank(certType)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_certTypeNotNull", personItem.getPin())));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": Personnel id is: " + personItem.getPin()
                        + " certType cannot be empty");
                    continue;
                }
                if (StringUtils.isNotBlank(certNumber) && StringUtils.isNotBlank(certType)) {
                    // 文件内部的证件类型、证件号重复
                    if (!(certNumbers.containsKey(certNumber) && certNumbers.get(certNumber).equals(certType))) {

                        // 证件号码超长
                        if (certNumber.length() > 30) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                                .i18nCode("pers_import_certNumberTooLong", personItem.getRowNum(), certNumber)));
                            itemIterator.remove();
                            logger
                                .info("Row " + personItem.getRowNum() + ": certNumber " + certNumber + " is too long");
                            continue;
                        }
                        // 二代身份证校验
                        if ("2".equals(certType) && !PersRegularUtil.idNumberCheck(certNumber)) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_idNumberErrors", personItem.getRowNum(), certNumber)));
                            itemIterator.remove();
                            logger
                                .info("Row " + personItem.getRowNum() + ": certNumber " + certNumber + " is malformed");
                            continue;
                        }

                        // 与数据库的证件号重复
                        PersCertificateItem certificateItem = persCertificateService
                            .getItemByTypeAndNumberAndPersonPinNe(certType, certNumber, personItem.getPin());
                        if (certificateItem != null) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_certNumberExist", certNumber))));
                            itemIterator.remove();
                            logger.info(
                                "Row " + personItem.getRowNum() + ": certNumber " + certNumber + " already Exists");
                            continue;
                        }

                        certNumbers.put(certNumber, certType);
                        PersCertificateItem persCertificateItem = new PersCertificateItem();
                        persCertificateItem.setCertType(certType);
                        persCertificateItem.setCertNumber(certNumber);
                        pinAndCertMap.put(personItem.getPin(), persCertificateItem);
                    } else {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_certNumberExist", certNumber))));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": certNumber " + certNumber + " already Exists");
                        continue;
                    }
                }
            }

            // 卡号处理。
            if (existsCardMap.get(personItem.getPin()) == null && StringUtils.isNotBlank(personItem.getCardNos())) {
                String[] cardNoAry = personItem.getCardNos().toLowerCase().split(PersConstants.IMPORT_CARD_SPLIT);
                if ("false".equals(cardsSupport) && cardNoAry.length > 1) {
                    // 当前系统不支持多卡
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                            I18nUtil.i18nCode("pers_import_cardsNotSupport", personItem.getPin()))));
                    itemIterator.remove();
                    logger.info(
                        "Row " + personItem.getRowNum() + ": The feature [Multiple Cards per Person] is disabled");
                    continue;
                }
                List<String> cardNoList = new ArrayList<>();
                for (String cardNo : cardNoAry) {
                    // 过滤卡号为空的数据
                    if (StringUtils.isBlank(cardNo)) {
                        continue;
                    }

                    if (PersRegularUtil.zeroPattern.matcher(cardNo).matches()) {
                        // 不能以0开头
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_cardNoStartWithZero"))));
                        logger.info("Row " + personItem.getRowNum() + ": Card number start with zero");
                        break;
                    }

                    // 有pro模块时, 卡号当字符串处理
                    if (Objects.nonNull(pers2ProService)) {
                        // 检查字符串长度不超过50
                        if (cardNo.length() > 50) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_cardTooLong", cardNo))));
                            logger.info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " is too long");
                            break;
                        }
                        // 检查字符, 只能数字或字母
                        if (!PersRegularUtil.hasNumberOrLetter(cardNo)) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_cardNoFormatErrors", cardNo))));
                            logger.info("Row " + personItem.getRowNum() + ": The format of Card number " + cardNo
                                + " is incorrect");
                            break;
                        }
                    } else {
                        // 参数设置为十进制卡，不允许导入16进制卡号
                        if (PersConstants.CARD_DECIMAL.equals(cardHex)) {
                            // 过滤掉不是数字的数据
                            if (!StringUtils.isNumeric(cardNo)) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_cardNoFormatErrors", cardNo))));
                                logger.info("Row " + personItem.getRowNum() + ": The format of Card number " + cardNo
                                    + " is incorrect");
                                break;
                            }
                            // 卡号超出最大值的数据
                            if ((new BigInteger(cardNo)).compareTo(maxCardNo) >= 0) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_cardTooLong", cardNo))));
                                logger
                                    .info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " is too long");
                                break;
                            }
                        } else if (PersConstants.CARD_HEXADECIMAL.equals(cardHex)) {
                            // 过滤掉不匹配十六进制的数据
                            if (!PersRegularUtil.isHex(cardNo)) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_cardNoFormatErrors", cardNo))));
                                logger.info("Row " + personItem.getRowNum() + ": The format of Card number " + cardNo
                                    + " is incorrect");
                                break;
                            }
                            // 过滤卡号超出最大值的数据
                            if ((new BigInteger(cardNo, 16)).compareTo(maxCardNo) >= 0) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_cardTooLong", cardNo))));
                                logger
                                    .info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " is too long");
                                break;
                            }
                        }
                    }

                    // 人员卡号数量超过17个，主卡1张，副卡16张
                    if (cardNoList.size() >= 17) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_card_notMoreThanSixteen"))));
                        logger.info("Row " + personItem.getRowNum()
                            + ": The quantity of the secondary cards cannot be greater than 16");
                        break;
                    }

                    // 如果卡号跟其他人重复了，不允许导入数据
                    if (Objects.nonNull(cardNoAndPinMap.put(cardNo, personItem.getPin()))) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_cardExist", cardNo))));
                        logger.info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " already Exists");
                        break;
                    }
                    // 检查卡号是否已经在数据库当中，并且是否跟其他人的卡号重复了。
                    if (existsCardNos.contains(cardNo)) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_cardExist", cardNo))));
                        logger.info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " already exists");
                        break;
                    }
                    cardNoList.add(cardNo);
                }
                // 卡号数据有异常，这条人员数据就不导入
                if (cardNoAry.length != cardNoList.size()) {
                    itemIterator.remove();
                    continue;
                }

                if (!CollectionUtil.isEmpty(cardNoList)) {
                    pinAndCardsMap.put(personItem.getPin(), cardNoList);
                }
            }
            try {
                if (personItem.getAttrMap() != null) {
                    Map<String, Object> attrMap = personItem.getAttrMap();
                    PersAttributeExtItem persAttributeExtItem = new PersAttributeExtItem();
                    boolean attrExtExist = false;
                    for (String key : personItem.getAttrMap().keySet()) {
                        String[] paramArray = key.split("\\.");
                        if (paramArray.length > 1 && StringUtils.isNotBlank(attrMap.get(key).toString())) {
                            if (existsAttributeExtMap.get(personItem.getPin()) == null) {
                                attrExtExist = true;
                                BeanUtils.setProperty(persAttributeExtItem, paramArray[1], attrMap.get(key).toString());
                            } else {
                                String paramValue = BeanUtils
                                    .getProperty(existsAttributeExtMap.get(personItem.getPin()), paramArray[1]);
                                if (StringUtils.isBlank(paramValue)) {
                                    attrExtExist = true;
                                    BeanUtils.setProperty(persAttributeExtItem, paramArray[1],
                                        attrMap.get(key).toString());
                                }
                            }
                        }

                    }
                    if (attrExtExist) {
                        pinAndAttrExtMap.put(personItem.getPin(), persAttributeExtItem);
                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            }
            // 车牌导入校验
            if (park4PersPersonServiceExist && existsPark4PersPersonItemMap.get(personItem.getPin()) == null) {
                if (StringUtils.isNotBlank(personItem.getCarPlate())) {
                    String[] carPlateArray = personItem.getCarPlate().split(PersConstants.IMPORT_CARD_SPLIT);
                    if (carPlateArray.length > 6) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_personPlateMax"))));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum()
                            + ": The number of personnel license plates exceeds the maximum of 6");
                        continue;
                    } else {
                        boolean carPlateValid = true;
                        for (String carPlate : carPlateArray) {
                            carNumber = carPlate.replace(getAreaFormCarNumber(carPlate), "");
                            // 国内车牌长度不能超过8位数
                            int plateLen = 7;
                            if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
                                plateLen = 20;
                                if (ValidateUtils.isSpecialChar(carNumber) || carNumber.contains(" ")
                                    || carNumber.contains("\n")) {
                                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                        I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                            I18nUtil.i18nCode("pers_import_personPlateFormat", personItem.getPin()))));
                                    itemIterator.remove();
                                    logger.info("Row " + personItem.getRowNum() + ": Personnel license plate "
                                        + carPlate + " format is incorrect");
                                    carPlateValid = false;
                                    break;
                                }
                            }
                            if (carNumber.length() > plateLen) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_personPlateFormat", carPlate))));
                                itemIterator.remove();
                                logger.info("Row " + personItem.getRowNum() + ": Personnel license plate " + carPlate
                                    + " format is incorrect");
                                carPlateValid = false;
                                break;
                            }

                            // 海外的车牌不需要校验
                            if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
                                if (StringUtils.isBlank(getAreaFormCarNumber(carPlate))) {
                                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                        I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                            I18nUtil.i18nCode("pers_import_personPlateFormat", carPlate))));
                                    itemIterator.remove();
                                    logger.info("Row " + personItem.getRowNum() + ": Personnel license plate "
                                        + carPlate + " format is incorrect");
                                    carPlateValid = false;
                                    break;
                                }

                                // 除省份外验证
                                Matcher m = PersRegularUtil.carnumberPattern
                                    .matcher(carPlate.replace(getAreaFormCarNumber(carPlate), ""));
                                if (!m.matches()) {
                                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                        I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                            I18nUtil.i18nCode("pers_import_personPlateFormat", carPlate))));
                                    itemIterator.remove();
                                    logger.info("Row " + personItem.getRowNum() + ": Personnel license plate "
                                        + carPlate + " format is incorrect");
                                    carPlateValid = false;
                                    break;
                                }
                            }
                            // 如果车牌跟其他人重复了或者自身车牌重复，不允许导入数据
                            if (Objects.nonNull(carPlateAndPinMap.put(carPlate, personItem.getPin()))) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_filePlateRepeat", carPlate))));
                                itemIterator.remove();
                                logger.info("Row " + personItem.getRowNum() + ": File inside the license plate "
                                    + carPlate + " duplicate");
                                carPlateValid = false;
                                break;
                            }
                            // 检查车牌是否已经在数据库当中，并且是否跟其他人的车牌重复了。
                            if (existsCarPlates.contains(carPlate)) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_personPlateRepeat", carPlate))));
                                itemIterator.remove();
                                logger.info("Row " + personItem.getRowNum() + ": Personnel license plate " + carPlate
                                    + " duplicate");
                                carPlateValid = false;
                                break;
                            }
                        }
                        if (!carPlateValid) {
                            // 车牌异常不导入数据，删掉之前存在需要导入的卡号
                            if (pinAndCardsMap.get(personItem.getPin()) != null) {
                                pinAndCardsMap.remove(personItem.getPin());
                            }
                            continue;
                        }
                        pinAndCarPlatesMap.put(personItem.getPin(), Arrays.asList(carPlateArray));
                    }
                }
            }
            if (StringUtils.isBlank(persPerson.getNameSpell())) {
                persPerson.setNameSpell(
                    personItem.getName() == null ? "" : PinyinUtil.converterToSpell(personItem.getName()));
            }
            if (StringUtils.isBlank(persPerson.getGender())) {
                persPerson.setGender(converterToGender(personItem.getGender(), genderMap));
            }
            if (persPerson.getPersonType() == null) {
                persPerson.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
            }
            if (persPerson.getStatus() == null) {
                persPerson.setStatus(PersConstants.PERSON_NORMAL);
            }
            if (persPerson.getExceptionFlag() == null) {
                persPerson.setExceptionFlag(PersConstants.PERSON_ISNOTEXCEPTION);
            }
            if (persPerson.getIsSendMail() == null) {
                persPerson.setIsSendMail(false);
            }
            if (persPerson.getPinLetter() == null) {
                persPerson.setPinLetter(!StringUtils.isNumeric(personItem.getPin()));
            }
            pinAndPersPersonMap.put(persPerson.getPin(), persPerson);
            if (existPersPersonMap.get(personItem.getPin()) != null) {
                existCount++;
            }
        }

        // 失败数量
        int faildCount = importSize - itemList.size();
        // 人员pin号和Park4PersPersonItem的对应关系集合
        Map<String, Park4PersPersonItem> pinAndPark4PersPersonItemMap = new HashMap<>(itemList.size());
        if (!CollectionUtil.isEmpty(pinAndPersPersonMap)) {
            // 查询人员数量
            Integer personSize = persPersonDao.countByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
            // 加导入的人员数量
            personSize = personSize + pinAndPersPersonMap.size() - existCount;
            // 许可数量控制
            ResultCode resultCode = baseLicenseProvider.isCountOutRangePers(personSize);
            if (!ResultCode.SUCCESS.equals(resultCode)) {
                throw ZKBusinessException.warnException("pers_import_exceedLicense");
            }

            // 把导入的数据分割，分批处理，一次处理800人
            List<List<PersPerson>> personList =
                CollectionUtil.split(pinAndPersPersonMap.values(), CollectionUtil.splitSize);
            float avgProgress = (float)60 / personImportItemsList.size();
            int i = 0;
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                I18nUtil.i18nCode("common_op_processing")));

            // 人员pin号和id的对应关系集合
            Map<String, String> pinAndIdMap = new HashMap<>(itemList.size());

            for (List<PersPerson> persList : personList) {
                int progress = beginProgress + 10 + (int)(avgProgress * i++);
                progressCache.setProcess(new ProcessBean(progress, progress, ""));
                persPersonDao.save(persList);

                // 保存人员pin和id的对应关系
                persList.forEach(person -> {
                    pinAndIdMap.put(person.getPin(), person.getId());
                    PersAttributeExtItem persAttributeExtItem = pinAndAttrExtMap.get(person.getPin());
                    if (persAttributeExtItem != null) {
                        persAttributeExtItem.setPersonId(person.getId());
                        persAttributeExtItemList.add(persAttributeExtItem);
                    }

                    PersCertificateItem persCertificateItem = pinAndCertMap.get(person.getPin());
                    if (persCertificateItem != null) {
                        persCertificateItem.setPersonId(person.getId());
                        persCertificateItemList.add(persCertificateItem);
                    }
                    if (pinAndCarPlatesMap.containsKey(person.getPin())) {
                        Park4PersPersonItem park4PersPersonItem = new Park4PersPersonItem();
                        park4PersPersonItem.setPersonId(person.getId());
                        park4PersPersonItem.setPersonPin(person.getPin());
                        park4PersPersonItem.setPersonName(person.getName());
                        park4PersPersonItem.setPersonLastName(person.getLastName());
                        park4PersPersonItem.setDeptId(person.getDeptId());
                        park4PersPersonItem.setDeptName(deptIdAndNameMap.get(person.getDeptId()));
                        pinAndPark4PersPersonItemMap.put(person.getPin(), park4PersPersonItem);
                    }
                });
            }

            // 处理人员卡号
            if (!CollectionUtil.isEmpty(pinAndCardsMap)) {
                persCardService.batchSaveCards(pinAndCardsMap, pinAndIdMap);
            }

            // 保存证件
            if (!CollectionUtil.isEmpty(pinAndCertMap)) {
                persCertificateService.batchSaveCertificates(persCertificateItemList);
            }

            // 保存扩展属性
            if (!CollectionUtil.isEmpty(persAttributeExtItemList)) {
                persAttributeExtService.batchSaveAttributeExts(persAttributeExtItemList);
            }

            // 保存车牌
            if (park4PersPersonServiceExist && !CollectionUtil.isEmpty(pinAndCarPlatesMap)
                && !CollectionUtil.isEmpty(pinAndPark4PersPersonItemMap)) {
                park4PersPersonService.importPersonData(pinAndCarPlatesMap, pinAndPark4PersPersonItemMap);
            }
        }
        if (existCount > 0) {
            // 成功：%s 条，更新：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result3", pinAndPersPersonMap.size(), existCount, faildCount)));
        } else {
            // 成功：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result", pinAndPersPersonMap.size(), faildCount)));
        }
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    /**
     * 根据国际化转换人员的性别
     *
     * @param gender
     * @param genderMap
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/5 11:26
     */
    private String converterToGender(String gender, Map<String, String> genderMap) {
        String personGender = "";
        if (StringUtils.isNotBlank(gender)) {
            if (I18nUtil.i18nCode(genderMap.get("M")).equals(gender)) {
                personGender = "M";
            } else if (I18nUtil.i18nCode(genderMap.get("F")).equals(gender)) {
                personGender = "F";
            } else if (I18nUtil.i18nCode(genderMap.get("U")).equals(gender)) {
                personGender = "U";
            }
        }
        return personGender;
    }

    @Override
    public List<PersPersonItem> getItemData(Class<PersPersonItem> persPersonItemClass, PersPersonItem persPersonItem,
        int beginIndex, int endIndex) {
        List<PersPersonItem> itemList = persPersonDao.getItemsDataBySql(persPersonItemClass,
            buildVerifyModeSqlByItem(persPersonItem), beginIndex, endIndex, true);
        buildCard(itemList);
        buildGender(itemList);
        // buildAttribute(itemList);
        return itemList;
    }

    @Override
    public String getDeptIdsByAuthFilter(String sessionId) {
        return authDepartmentService.getDeptIdsByAuthFilter(sessionId);
    }

    @Override
    public void updatePersonExceptionFlag(String pin, Short exceptionFlag) {
        PersPerson person = persPersonDao.findByPin(pin);
        if (Objects.nonNull(person) && person.getExceptionFlag().shortValue() != exceptionFlag) {
            person.setExceptionFlag(exceptionFlag);
            persPersonDao.save(person);
        }
    }

    @Override
    public Long getPersonCount() {
        return persPersonDao.count();
    }

    @Override
    public List<PersPersonCloudItem> getPersonCloudItems(PersPersonCloudItem condition, int pageNo, int pageSize) {
        Pager pager =
            persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        List<PersPersonCloudItem> persPersonCloudItems = (List<PersPersonCloudItem>)pager.getData();
        buildCloudCard(persPersonCloudItems);
        return persPersonCloudItems;
    }

    @Override
    public boolean changeSelfPwd(String pin, String oldPassword, String newPassword, boolean checkPassword) {
        boolean change = false;
        PersPerson person = persPersonDao.findByPin(pin);// 查询需要修改密码人员
        if (person != null) {
            if (!checkPassword || oldPassword.equals(person.getSelfPwd())) { // 判断前端的旧密码与人员密码是否一致，不一致则返回错误
                person.setSelfPwd(newPassword);
                persPersonDao.save(person);
                change = true;
            }
        }
        return change;
    }

    @Override
    public boolean editAppPerson(PersPersonItem personItem, PersCertificateItem persCertificateItem,
        PersAttributeExtItem persAttributeExtItem) {
        PersPerson person = persPersonDao.findByPin(personItem.getPin());
        if (person != null) {
            // 保存人员信息
            ModelUtil.copyPropertiesIgnoreNull(personItem, person);
            persPersonDao.save(person);
            // 处理人员卡号数据？需要更新到对应设备中
            persCardService.updatePersonCardInfo(person.getId(), personItem.getCardNos());
            // 处理人员证件数据
            persCertificateItem.setPersonId(person.getId());
            persCertificateService.saveItem(persCertificateItem);
            // 处理人员扩展数据
            persAttributeExtItem.setPersonId(person.getId());
            persAttributeExtService.saveItem(persAttributeExtItem);
            return true;
        }
        return false;
    }

    @Override
    public List<PersPersonItem> getByPinAndNamePell(Integer pin, String nameSpell) {
        Pageable pageable = PageRequest.of(pin, 5);
        List<PersPerson> persPersonList = persPersonDao.getByPinAndNameSpell(PersConstants.PERSON_TYPE_EMPLOYEE,
            PersConstants.PERSON_NORMAL, nameSpell, PinyinUtil.converterToSpell(nameSpell), pageable);
        List<PersPersonItem> persPersonItemList = ModelUtil.copyListProperties(persPersonList, PersPersonItem.class);
        String deptIds = CollectionUtil.getPropertys(persPersonList, PersPerson::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        persPersonItemList.forEach(item -> {
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptName(dept.getName());
        });
        return persPersonItemList;
    }

    /**
     * 判断人员编号是否包含字母
     *
     * @param pin
     * @return java.lang.Boolean
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/22 10:02
     */
    private Boolean pinIsContainLetter(String pin) {
        return pin.matches(".*[a-zA-Z]+.*");
    }

    /**
     * 根据姓名查询人员信息
     *
     * @param name
     * @return
     */
    @Override
    public PersPersonItem getItemByName(String name) {
        PersPersonItem condition = new PersPersonItem();
        condition.setName(name);
        condition.setEquals(true);
        List<PersPersonItem> items = getByCondition(condition);
        if (items.size() > 1) {
            logger.warn("search not only record,search person name=" + name);
        }
        return !items.isEmpty() ? items.get(0) : null;
    }

    @Override
    public List<PersPersonToInsAreaItem> findPersonToInsAreaItem(String sessionId, PersPersonToInsAreaItem condition) {
        buildInsDeptCondition(sessionId, condition);
        List<PersPersonToInsAreaItem> items = (List<PersPersonToInsAreaItem>)persPersonDao
            .getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        String deptIds = CollectionUtil.getPropertys(items, PersPersonToInsAreaItem::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        items.forEach(item -> {
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
        });
        return items;
    }

    @Override
    public List<PersPersonItem> findByPinsAndHireDate(String pins, Date beginDate, Date endDate) {
        PersPersonItem condition = new PersPersonItem();
        condition.setInPin(pins);
        condition.setBeginDate(beginDate);
        condition.setEndDate(endDate);
        return getByCondition(condition);
    }

    @Override
    public Pager findPersonAreaItem(String sessionId, PersPersonAreaItem condition, int page, int size) {
        // 构建查询条件
        buildCondition(sessionId, condition);
        condition.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        Pager pager = persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        List<PersPersonAreaItem> list = (List<PersPersonAreaItem>)pager.getData();
        // 查询部门
        String deptIds = CollectionUtil.getPropertys(list, PersPersonAreaItem::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        // 查询区域
        String areaIds = CollectionUtil.getPropertys(list, PersPersonAreaItem::getAreaId);
        List<AuthAreaItem> areaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> areaItemMap = CollectionUtil.itemListToIdMap(areaItems);
        // 查询主卡
        List<String> personList = (List<String>)CollectionUtil.getItemIdsList(list);
        List<PersCardItem> persCards = persCardService.getMasterCardByPersonIdList(personList);
        Map<String, PersCardItem> persCardMap = CollectionUtil.listToKeyMap(persCards, PersCardItem::getPersonId);
        // 查询生物模版数量
        Map<String, Map<Short, Integer>> bioTemplateCount =
            persBioTemplateService.getBioTemplateCountByPersonIdList(personList);
        // 填充数据
        list.forEach(item -> {
            // 填充部门数据
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
            // 填充区域数据
            AuthAreaItem areaItem = areaItemMap.get(item.getAreaId());
            if (Objects.nonNull(areaItem)) {
                item.setAreaCode(areaItem.getCode());
                item.setAreaName(areaItem.getName());
            }

            // 填充主卡数据
            PersCardItem card = persCardMap.get(item.getId());
            if (Objects.nonNull(card)) {
                item.setCardNo(card.getCardNo());
            }
            // 填充生物模版数量
            Map<Short, Integer> perBioTemplateCount = bioTemplateCount.get(item.getId());
            // fpCount + "_" + faceCount + "_" + fvCount + "_" + palmCount + "_" + cropFaceCount;
            item.setBioTemplateCount(MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.FP_BIO_TYPE, 0)
                + "_" + MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.FACE_BIO_TYPE, 0) + "_"
                + MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.VEIN_BIO_TYPE, 0) + "_"
                + MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.PALM_BIO_TYPE, 0) + "_"
                + FileUtil.getCropFaceCount(item.getPersonPin()));
        });
        return pager;
    }

    /**
     * -------------------------------分割线，以下是进行条件封装或者数据封装-----------------------------------------------------------
     */

    @Override
    public void buildCondition(String sessionId, PersPersonItem condition) {
        // 封装部门条件
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        if (StringUtils.isBlank(condition.getInDeptId())) {
            if (StringUtils.isNotBlank(condition.getDeptId())) {
                List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getDeptId());
                condition.setInDeptId(StrUtil.collectionToStr(deptIdList));
            }
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(condition.getInDeptId());
        }
        condition.setDeptId(null);
        // 卡号转化成小写在查询
        if (StringUtils.isNotBlank(condition.getLikeCardNo())) {
            condition.setLikeCardNo(condition.getLikeCardNo().toLowerCase().replaceFirst("^0*", ""));
        }
        if ("pin".equals(condition.getSortName())) {
            condition.setSortName("numberPin");
        }
    }

    /**
     * 封装条件部门名称查询
     *
     * @param condition
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:18
     */
    private void buildCondition(String sessionId, PersPersonToAttAreaItem condition) {
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }
        // 卡号转化成小写在查询
        if (StringUtils.isNotBlank(condition.getCardNo())) {
            condition.setCardNo(condition.getCardNo().toLowerCase());
        }
    }

    /**
     * 封装条件 部门名称查询
     *
     * @param condition
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:18
     */
    private void buildCondition(String sessionId, PersPersonSelectItem condition) {
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }
        // 卡号转化成小写在查询
        if (StringUtils.isNotBlank(condition.getCardNo())) {
            condition.setCardNo(condition.getCardNo().toLowerCase());
        }
        if ("persPin".equals(condition.getSortName())) {
            condition.setSortName("numberPin");
        }
    }

    /**
     * 封装条件 部门 区域 查询
     *
     * @param condition
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:18
     */
    private void buildCondition(String sessionId, PersPersonAreaItem condition) {
        // 部门的处理
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }

        // 区域的处理(等于空不带条件过滤 by jinxian.huang)
        if (StringUtils.isBlank(condition.getAreaId())) {
            condition.setInAreaId(authAreaService.getAreaIdsByIdAndCodeAndName(sessionId, condition.getAreaId(),
                condition.getAreaCode(), condition.getAreaName()));
        }

        // 卡号转化成小写在查询
        if (StringUtils.isNotBlank(condition.getCardNo())) {
            condition.setCardNo(condition.getCardNo().toLowerCase());
        }
    }

    /**
     * 封装条件 部门名称查询
     *
     * @param condition
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:18
     */
    private void buildCondition(String sessionId, PersPersonLinkSearchItem condition) {
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }
        // 卡号转化成小写在查询
        if (StringUtils.isNotBlank(condition.getCardNo())) {
            condition.setCardNo(condition.getCardNo().toLowerCase());
        }
    }

    /**
     * 封装多卡数据
     *
     * @param item
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonItem
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:19
     */
    private PersPersonItem buildCard(PersPersonItem item) {
        List<String> cardNos = persCardDao.findByPersonPinOrderByCardType(item.getPin());
        if (Objects.nonNull(cardNos) && !cardNos.isEmpty()) {
            item.setCardNos(StringUtils.join(cardNos, ","));
        }
        return item;
    }

    /**
     * 封装多卡数据
     *
     * @param items
     * @return java.util.List<?>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:19
     */
    private List<?> buildCard(List<?> items) {
        List<PersPersonItem> list = (List<PersPersonItem>)items;
        List<List<String>> personIdList =
            CollectionUtil.getPropertyList(list, PersPersonItem::getId, CollectionUtil.splitSize);
        Map<String, PersPersonItem> personItems = CollectionUtil.itemListToIdMap(list);
        personIdList.forEach(personIds -> {
            List<PersCard> cards = persCardDao.findByPersonIdInOrderByCardType(personIds);
            if (!CollectionUtil.isEmpty(cards)) {
                Map<String, List<PersCard>> cardMap =
                    cards.stream().collect(Collectors.groupingBy(card -> card.getPersonId()));
                cardMap.forEach((personId, cardList) -> {
                    PersPersonItem item = personItems.get(personId);
                    item.setCardNos(cardList.stream().map(PersCard::getCardNo)
                        .collect(Collectors.joining(PersConstants.IMPORT_CARD_SPLIT)));
                });
            }
        });
        return items;
    }

    /**
     * 封装主卡数据
     *
     * @param items
     * @return java.util.List<?>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:19
     */
    private List<PersPersonToAttAreaItem> buildMasterCard(List<PersPersonToAttAreaItem> items) {
        List<List<String>> personIdList =
            CollectionUtil.getPropertyList(items, PersPersonToAttAreaItem::getId, CollectionUtil.splitSize);
        Map<String, PersPersonToAttAreaItem> personItems = CollectionUtil.itemListToIdMap(items);
        personIdList.forEach(personIds -> {
            List<PersCardItem> cards = persCardService.getMasterCardByPersonIdList(personIds);
            if (!CollectionUtil.isEmpty(cards)) {
                cards.forEach(card -> {
                    PersPersonToAttAreaItem item = personItems.get(card.getPersonId());
                    item.setCardNo(card.getCardNo());
                });
            }
        });
        return items;
    }

    /**
     * 封装部门数据
     *
     * @param item
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonItem
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:19
     */
    private PersPersonItem buildDept(PersPersonItem item) {
        AuthDepartmentItem dept = authDepartmentService.getItemById(item.getDeptId());
        item.setDeptCode(dept.getCode());
        item.setDeptName(dept.getName());
        return item;
    }

    /**
     * 封装部门数据
     *
     * @param items
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonItem
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:19
     */
    private List<?> buildDept(List<?> items) {
        List<PersPersonItem> list = (List<PersPersonItem>)items;
        Map<String, List<PersPersonItem>> deptPersonMap =
            list.stream().collect(Collectors.groupingBy(PersPersonItem::getDeptId));
        Collection<String> allDeptIdList = CollectionUtil.getPropertyList(list, PersPersonItem::getDeptId, "-1");// 先获取所有部门ID，并去重
        List<List<String>> deptIdList = CollectionUtil.split(allDeptIdList, CollectionUtil.splitSize);
        deptIdList.forEach(deptIds -> {
            List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
            if (!CollectionUtil.isEmpty(deptItems)) {
                deptItems.forEach(dept -> {
                    List<PersPersonItem> personItems = deptPersonMap.get(dept.getId());
                    personItems.forEach(item -> {
                        item.setDeptCode(dept.getCode());
                        item.setDeptName(dept.getName());
                    });
                });
            }
        });
        return items;
    }

    /**
     * 封装云服务需要的部门数据
     *
     * @param items
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonItem
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:19
     */
    private List<PersPersonCloudItem> buildCloudDept(List<PersPersonCloudItem> items) {
        Map<String, List<PersPersonCloudItem>> deptPersonMap =
            items.stream().collect(Collectors.groupingBy(PersPersonCloudItem::getDeptId));
        Collection<String> allDeptIdList = CollectionUtil.getPropertyList(items, PersPersonCloudItem::getDeptId, "-1");// 先获取所有部门ID，并去重
        List<List<String>> deptIdList = CollectionUtil.split(allDeptIdList, CollectionUtil.splitSize);
        deptIdList.forEach(deptIds -> {
            List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
            if (!CollectionUtil.isEmpty(deptItems)) {
                deptItems.forEach(dept -> {
                    List<PersPersonCloudItem> personItems = deptPersonMap.get(dept.getId());
                    personItems.forEach(item -> {
                        item.setParentDeptCode(dept.getParentCode());
                        item.setDeptCode(dept.getCode());
                        item.setDeptName(dept.getName());
                    });
                });
            }
        });
        return items;
    }

    /**
     * 封装云服务需要的卡号数据
     *
     * @param items
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonItem
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:19
     */
    private List<PersPersonCloudItem> buildCloudCard(List<PersPersonCloudItem> items) {
        List<List<String>> personIdList =
            CollectionUtil.getPropertyList(items, PersPersonCloudItem::getId, CollectionUtil.splitSize);
        Map<String, PersPersonCloudItem> personItems = CollectionUtil.listToKeyMap(items, PersPersonCloudItem::getPin);
        personIdList.forEach(personIds -> {
            List<PersCard> cards = persCardDao.findByPersonIdInOrderByCardType(personIds);
            if (!CollectionUtil.isEmpty(cards)) {
                Map<String, List<PersCard>> cardMap =
                    cards.stream().collect(Collectors.groupingBy(card -> card.getPersonPin()));
                cardMap.forEach((personPin, cardList) -> {
                    PersPersonCloudItem item = personItems.get(personPin);
                    item.setCardNos(cardList.stream().map(PersCard::getCardNo).collect(Collectors.joining(",")));
                });
            }
        });
        return items;
    }

    /**
     * 转换性别数据
     *
     * @param items
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonItem
     * <AUTHOR>
     * @date 2018/08/08 11:19
     */
    private List<?> buildGender(List<?> items) {
        Map<String, String> genderMap = baseDictionaryValueService.getDictionaryValuesMap("sex");
        List<PersPersonItem> list = (List<PersPersonItem>)items;
        list.forEach(item -> {
            if (StringUtils.isNotBlank(item.getGender())) {
                item.setGender(I18nUtil.i18nCode(genderMap.get(item.getGender())));
            }
        });
        return items;
    }

    /**
     * 组装人员具有的设备识别手段
     *
     * @param items
     * @return
     */
    private List<?> buildVerifyMode(List<?> items) {
        List<PersPersonItem> list = (List<PersPersonItem>)items;
        Map<String, Map<Short, Integer>> bioTemplateCount =
            persBioTemplateService.getBioTemplateCountByPersonIdList(CollectionUtil.getItemIdsList(list));
        list.forEach(item -> {
            Map<Short, Integer> perBioTemplateCount = bioTemplateCount.get(item.getId());
            item.setVerifyMode(MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.FP_BIO_TYPE, 0) + "_"
                + MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.FACE_BIO_TYPE, 0) + "_"
                + MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.VEIN_BIO_TYPE, 0) + "_"
                + MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.PALM_BIO_TYPE, 0) + "_"
                + persBioPhotoDao.countByPersonIdAndBioType(item.getId(), BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE)
                + "_" + MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE, 0) + "_"
                + (StringUtils.isNotBlank(item.getPersonPwd()) ? 1 : 0) + "_"
                + (StringUtils.isNotBlank(item.getCardNos()) ? 1 : 0) + "_"
                + MapUtils.getInteger(perBioTemplateCount, PersConstants.PALM_BIO_TYPE_10, 0) + "_"
                + MapUtils.getInteger(perBioTemplateCount, PersConstants.PV_BIO_TYPE, 0) + "_"
                + MapUtils.getInteger(perBioTemplateCount, PersConstants.IRIS_BIO_TYPE, 0));
        });
        return items;
    }

    /**
     * 封装生物模板数量
     *
     * @param items
     * @return java.util.List<?>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:20
     */
    private List<PersPersonToAttAreaItem> buildAttBioTemplateCount(List<PersPersonToAttAreaItem> items) {
        Map<String, Map<Short, Integer>> bioTemplateCount =
            persBioTemplateService.getBioTemplateCountByPersonIdList(CollectionUtil.getItemIdsList(items));
        items.forEach(item -> {
            Map<Short, Integer> perBioTemplateCount = bioTemplateCount.get(item.getId());
            // fpCount + "_" + faceCount + "_" + fvCount+"_" + cropFaceCount;
            item.setBioTemplateCount(MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.FP_BIO_TYPE, 0)
                + "_" + MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.FACE_BIO_TYPE, 0) + "_"
                + MapUtils.getInteger(perBioTemplateCount, BaseConstants.BaseBioType.VEIN_BIO_TYPE, 0) + "_"
                + FileUtil.getCropFaceCount(item.getPin()));
        });
        return items;
    }

    /**
     * 封装自定义属性数据
     *
     * @param items
     * @return java.util.List<?>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:20
     */
    private List<?> buildAttribute(List<?> items) {
        List<PersPersonItem> list = (List<PersPersonItem>)items;
        List<List<String>> propertyList =
            CollectionUtil.getPropertyList(list, PersPersonItem::getId, CollectionUtil.splitSize);
        propertyList.forEach(personIds -> {
            List<PersAttributeExtItem> attrExtItems = persAttributeExtService.getItemByPersonIdList(personIds);
            Map<String, PersAttributeExtItem> attrExtItemMap =
                CollectionUtil.listToKeyMap(attrExtItems, PersAttributeExtItem::getPersonId);
            list.forEach(item -> {
                item.setAttrExtMap(new BeanMap(attrExtItemMap.get(item.getId())));
            });
        });
        return items;
    }

    /**
     * 封装信息屏部门条件
     *
     * @param sessionId
     * @param condition
     */
    private void buildInsDeptCondition(String sessionId, PersPersonToInsAreaItem condition) {
        if (StringUtils.isBlank(condition.getInDeptId())) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByIdAndCodeAndName(sessionId, condition.getDeptId(),
                condition.getDeptCode(), condition.getDeptName()));
            condition.setDeptId(null);
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(authDepartmentService.getDeptIdsByAuthAndIds(sessionId, condition.getInDeptId()));
            condition.setDeptId(null);
        }
    }

    @Override
    public String getCountry(String countryKey) {
        return baseDictionaryValueService.getDictionaryValuesMap("PersNationality").get(countryKey);
    }

    @Override
    public List<String> getPersonIdsByDeptIds(List<String> deptIds) {
        return persPersonDao.getPersonIdsByDeptIdIn(deptIds);
    }

    @Override
    public List<String> getEnabledCredentialPersonIdsByDeptIds(List<String> deptIds) {
        return persPersonDao.getEnabledCredentialPersonIdsByDeptIds(deptIds);
    }

    @Override
    public List<PersPersonEmpItem> getEmpByNameOrPhoneOrDept(String name, String mobilePhone, List<String> deptIdList,
        String type) {
        // 获取人员信息
        List<PersPerson> persPersonList =
            persPersonDao.findByNameOrMobilePhoneOrDeptId(name, mobilePhone, deptIdList, type);
        List<PersPersonEmpItem> visitedPersonItemList = new ArrayList<>();
        // 获取公司号码
        PersAttribute persAttribute = persAttributeDao.findByAttrName("office_phone");
        if (persAttribute == null) {
            persAttribute = persAttributeDao.findByAttrName(I18nUtil.i18nCode("pers_attr_office_phone"));
        }
        Collection<String> allPersonIdList = CollectionUtil.getPropertyList(persPersonList, PersPerson::getId, "-1");
        List<PersAttributeExtItem> persAttributeExtItemList =
            persAttributeExtService.getItemByPersonIdList((List<String>)allPersonIdList);
        Map<String, PersAttributeExtItem> persAttributeExtMap =
            CollectionUtil.listToKeyMap(persAttributeExtItemList, PersAttributeExtItem::getPersonId);
        for (PersPerson persPerson : persPersonList) {
            PersPersonEmpItem item = new PersPersonEmpItem();
            item.setId(persPerson.getId());
            item.setPersonId(persPerson.getId());
            item.setPersonPin(persPerson.getPin());
            item.setPersonName(persPerson.getName());
            item.setPersonLastName(persPerson.getLastName());
            item.setMobilePhone(persPerson.getMobilePhone());
            item.setDeptId(persPerson.getDeptId());
            try {
                if (persAttribute != null) {
                    String fieldValue = BeanUtils.getProperty(persAttributeExtMap.get(persPerson.getId()),
                        "attrValue" + persAttribute.getFiledIndex());
                    item.setOfficePhone(fieldValue);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            visitedPersonItemList.add(item);
        }
        // 获取部门code、部门名称
        Map<String, List<PersPersonEmpItem>> deptPersonMap =
            visitedPersonItemList.stream().collect(Collectors.groupingBy(PersPersonEmpItem::getDeptId));
        Collection<String> allDeptIdList =
            CollectionUtil.getPropertyList(visitedPersonItemList, PersPersonEmpItem::getDeptId, "-1");// 先获取所有部门ID，并去重
        List<List<String>> deptmentIdList = CollectionUtil.split(allDeptIdList, CollectionUtil.splitSize);
        deptmentIdList.forEach(deptIds -> {
            List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
            if (!CollectionUtil.isEmpty(deptItems)) {
                deptItems.forEach(dept -> {
                    List<PersPersonEmpItem> personItems = deptPersonMap.get(dept.getId());
                    personItems.forEach(item -> {
                        item.setDeptCode(dept.getCode());
                        item.setDeptName(dept.getName());
                    });
                });
            }
        });
        return visitedPersonItemList;
    }

    @Override
    public List<PersPersonItem> getPersonItemForDevByIds(Collection<String> personIds) {
        return Optional.ofNullable(personIds).filter(i -> !i.isEmpty()).map(persPersonDao::getPersonForDevByIds)
            .map(list -> ModelUtil.copyListProperties(list, PersPersonItem.class)).orElse(new ArrayList<>());
    }

    @Override
    public String getPersonPhotoPathByPin(String pin) {
        String photoPath = "";
        if (StringUtils.isNotBlank(pin)) {
            photoPath = persPersonDao.findPhotoPathByPin(pin);
        }
        return photoPath;
    }

    @Override
    public Pager findSimplePersonAreaItem(String sessionId, PersPersonAreaItem condition, int pageNo, int pageSize) {
        // 构建查询条件
        buildCondition(sessionId, condition);
        condition.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
        Pager pager =
            persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo, pageSize);
        List<PersPersonAreaItem> list = (List<PersPersonAreaItem>)pager.getData();
        // 查询部门
        String deptIds = CollectionUtil.getPropertys(list, PersPersonAreaItem::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        // 查询区域
        String areaIds = CollectionUtil.getPropertys(list, PersPersonAreaItem::getAreaId);
        List<AuthAreaItem> areaItems = authAreaService.getItemsByIds(areaIds);
        Map<String, AuthAreaItem> areaItemMap = CollectionUtil.itemListToIdMap(areaItems);
        // 填充数据
        list.forEach(item -> {
            // 填充部门数据
            AuthDepartmentItem dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
            // 填充区域数据
            AuthAreaItem areaItem = areaItemMap.get(item.getAreaId());
            if (Objects.nonNull(areaItem)) {
                item.setAreaCode(areaItem.getCode());
                item.setAreaName(areaItem.getName());
            }
        });
        return pager;
    }

    @Override
    public List<PersPersonExportItem> analyzeExportData(List<PersPersonItem> persPersonItemList) {
        if (persPersonItemList != null && persPersonItemList.size() > 0) {
            List<PersPersonExportItem> persPersonExportItemList =
                ModelUtil.copyListProperties(persPersonItemList, PersPersonExportItem.class);
            Map<String, PersPersonExportItem> idAndItemMap = new HashMap<>(persPersonItemList.size());
            idAndItemMap.putAll(CollectionUtil.listToKeyMap(persPersonExportItemList, PersPersonExportItem::getId));

            Collection<String> personIdList =
                CollectionUtil.getPropertyList(persPersonExportItemList, PersPersonExportItem::getId, "-1");
            List<List<String>> idList = CollectionUtil.split(personIdList, CollectionUtil.splitSize);
            boolean park4PersPersonServiceExist = Objects.nonNull(park4PersPersonService);
            // 判断是否存在可视对讲模块
            boolean vdb4PersPersonServiceExist = Objects.nonNull(vdb4PersPersonService);
            idList.forEach(ids -> {
                // 获取车牌
                if (park4PersPersonServiceExist) {
                    List<Park4PersPersonItem> park4PersPersonItemList =
                        park4PersPersonService.findbyPersPersonIdIn(ids);
                    if (!CollectionUtil.isEmpty(park4PersPersonItemList)) {
                        park4PersPersonItemList.forEach(park4PersPersonItem -> {
                            // 获取车牌
                            PersPersonExportItem item = idAndItemMap.get(park4PersPersonItem.getPersonId());
                            if (item != null) {
                                if (StringUtils.isNotBlank(item.getCarPlate())) {
                                    item.setCarPlate(item.getCarPlate() + PersConstants.IMPORT_CARD_SPLIT
                                        + park4PersPersonItem.getParkCarNumbers());
                                } else {
                                    item.setCarPlate(park4PersPersonItem.getParkCarNumbers());
                                }

                            }
                        });
                    }
                }
                if (vdb4PersPersonServiceExist) {
                    List<Vdb4PersPersonItem> vdb4PersPersonItemList = vdb4PersPersonService.findByPersonIdIn(ids);
                    if (!CollectionUtil.isEmpty(vdb4PersPersonItemList)) {
                        vdb4PersPersonItemList.forEach(vdb4PersPersonItem -> {
                            // 获取楼栋名称、房号、单元名称等信息
                            PersPersonExportItem item = idAndItemMap.get(vdb4PersPersonItem.getPersonId());
                            if (item != null) {
                                item.setBuildingName(vdb4PersPersonItem.getBuildingName());
                                item.setUnitName(vdb4PersPersonItem.getUnitName());
                                item.setRoomNo(vdb4PersPersonItem.getRoomNo());
                            }
                        });
                    }
                }
                // 获取证件类型
                List<PersCertificateItem> persCertificateItemList = persCertificateService.getItemByPersonIdIn(ids);
                if (!CollectionUtil.isEmpty(persCertificateItemList)) {
                    persCertificateItemList.forEach(persCertificateItem -> {
                        // 获取证件
                        PersPersonExportItem item = idAndItemMap.get(persCertificateItem.getPersonId());
                        if (item != null) {
                            item.setCertName(persCertificateItem.getCertName());
                            item.setCertNumber(persCertificateItem.getCertNumber());

                        }
                    });
                }

                // 获取自定义属性
                List<PersAttributeExtItem> persAttributeExtItemList =
                    persAttributeExtService.getItemByPersonIdList(ids);
                if (!CollectionUtil.isEmpty(persAttributeExtItemList)) {
                    persAttributeExtItemList.forEach(persAttributeExtItem -> {
                        // 获取自定义属性
                        PersPersonExportItem item = idAndItemMap.get(persAttributeExtItem.getPersonId());
                        if (item != null) {
                            item.setAttributeExt(persAttributeExtItem);
                        }
                    });

                }

                // 获取验证方式
                // Map<id, Map<type&version, count>>
                Map<String, Map<String, Integer>> personAndBioMap =
                    persBioTemplateService.getBioTypeAndCountByPersonIdList(ids);

                for (String personId : personAndBioMap.keySet()) {
                    PersPersonExportItem item = idAndItemMap.get(personId);
                    if (item != null) {
                        StringBuilder sb = new StringBuilder();
                        Map<String, Integer> bioTypeAndCountMap = personAndBioMap.get(personId);
                        if (!bioTypeAndCountMap.isEmpty()) {
                            for (String key : bioTypeAndCountMap.keySet()) {
                                String[] typeAndVersion = key.split(PersConstants.IMPORT_CARD_SPLIT);
                                if (BaseConstants.BaseBioType.FP_BIO_TYPE == Short.parseShort(typeAndVersion[0])) {
                                    sb.append(I18nUtil.i18nCode("pers_person_regFinger") + " "
                                        + bioTypeAndCountMap.get(key) + " V" + typeAndVersion[1] + ".0\r\n");
                                }
                                if (BaseConstants.BaseBioType.VEIN_BIO_TYPE == Short.parseShort(typeAndVersion[0])) {
                                    sb.append(I18nUtil.i18nCode("pers_person_regVein") + " "
                                        + bioTypeAndCountMap.get(key) + " V" + typeAndVersion[1] + ".0\r\n");
                                }
                                if (BaseConstants.BaseBioType.FACE_BIO_TYPE == Short.parseShort(typeAndVersion[0])) {
                                    sb.append(I18nUtil.i18nCode("pers_person_infraredFace") + " "
                                        + bioTypeAndCountMap.get(key) + " V" + typeAndVersion[1] + ".0\r\n");
                                }
                                if (BaseConstants.BaseBioType.PALM_BIO_TYPE == Short.parseShort(typeAndVersion[0])) {
                                    sb.append(I18nUtil.i18nCode("pers_person_metacarpalVein") + " "
                                        + bioTypeAndCountMap.get(key) + " V" + typeAndVersion[1] + ".0\r\n");
                                }
                                if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE == Short
                                    .parseShort(typeAndVersion[0])) {
                                    sb.append(I18nUtil.i18nCode("pers_person_visibleFace") + " "
                                        + bioTypeAndCountMap.get(key) + " V" + typeAndVersion[1] + ".0\r\n");
                                }
                                if (PersConstants.PALM_BIO_TYPE_10 == Short.parseShort(typeAndVersion[0])) {
                                    sb.append(I18nUtil.i18nCode("pers_person_visiblePalm") + " "
                                        + bioTypeAndCountMap.get(key) + " V" + typeAndVersion[1] + ".0\r\n");
                                }

                            }
                        }
                        item.setVerifyMode(sb.toString());
                    }
                }
                // 获取比对照片
                // Map<id, Map<type, count>>
                Map<String, Map<String, Integer>> personAndBioPhototMap =
                    persBioPhotoService.getBioTypeAndCountByPersonIdList(ids);
                for (String personId : personAndBioPhototMap.keySet()) {
                    PersPersonExportItem item = idAndItemMap.get(personId);
                    if (item != null) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(StringUtils.isNotBlank(item.getVerifyMode()) ? item.getVerifyMode() : "");
                        Map<String, Integer> bioTypeAndCountMap = personAndBioPhototMap.get(personId);
                        if (!bioTypeAndCountMap.isEmpty()) {
                            for (String key : bioTypeAndCountMap.keySet()) {
                                if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE == Short.parseShort(key)) {
                                    sb.append(I18nUtil.i18nCode("pers_person_cropFacePhoto") + " "
                                        + bioTypeAndCountMap.get(key) + "\r\n");
                                }
                                if (PersConstants.PALM_BIO_TYPE_10 == Short.parseShort(key)) {
                                    sb.append(I18nUtil.i18nCode("pers_person_vislightPalmPhoto") + " "
                                        + bioTypeAndCountMap.get(key) + "\r\n");
                                }

                            }
                        }
                        item.setVerifyMode(sb.toString());
                    }
                }
            });

            return persPersonExportItemList;
        }
        return null;
    }

    @Override
    public ZKResultMsg handlerZipUserPhoto(String filePath) {
        PersImportPhotoItem item = new PersImportPhotoItem();
        item.setFilePath(filePath);
        item.setImportPhoto(true);
        item.setImportCropFace(true);
        return handlerZipUserPhoto(item);
    }

    @Override
    public ZKResultMsg handlerZipUserPhoto(PersImportPhotoItem item) {
        ZKResultMsg zkResultMsg = new ZKResultMsg();
        StringBuilder corrLog = new StringBuilder();
        StringBuilder errLog = new StringBuilder();
        String filePath = item.getFilePath();
        Boolean importPhoto = item.getImportPhoto();
        Boolean importCropFace = item.getImportCropFace();
        Boolean importTemplateFace = item.getImportTemplateFace();
        try {
            if (StringUtils.isNotBlank(filePath)) {
                String pin = "";
                String fileName = "";
                String realPersPhotoPath = "";
                List<File> files = new ArrayList<>();
                // 获取目录下的所有文件
                files = FileUnZipUtil.getFileList(files, filePath);
                if (files != null && files.size() > 0) {
                    // 分批操作
                    List<List<File>> filesList = CollectionUtil.split(files, CollectionUtil.splitSize);
                    Set<String> pinSet = null;
                    Map<String, File> fileMap = null;
                    BufferedImage img = null;
                    for (List<File> fileList : filesList) {
                        pinSet = new HashSet<>();
                        fileMap = new HashMap<>();
                        for (File file : fileList) {
                            fileName = file.getName();
                            // 判断文件是否为照片格式
                            if (fileName.toLowerCase().matches(".*?(jpg|jpeg|png)$")) {
                                // 截取文件名
                                pin = fileName.substring(0, fileName.lastIndexOf("."));
                                // 文件限制不得大于5M大小，防止抠图错误
                                if (file.length() > 5242880) {
                                    // 文件名包含特殊字符
                                    errLog.append(
                                        fileName + "," + I18nUtil.i18nCode("pers_import_fileMaxSize") + "</br>");
                                } else if (!ValidateUtils.isSpecialChar(pin)) {// 判断文件名是否包含特殊字符
                                    img = ImageIO.read(file);
                                    if (img != null) {
                                        if (img.getWidth() * img.getHeight() > 4000 * 4000) {// 超过1024*1024使用缩略图进行抠图，防止动态库挂
                                            errLog.append(fileName + ","
                                                + I18nUtil.i18nCode("pers_person_picMaxSize", "4000 * 4000") + "</br>");
                                        } else {
                                            pinSet.add(pin);
                                            fileMap.put(pin, file);
                                        }
                                    } else {
                                        // 文件格式不正确
                                        errLog.append(fileName + "," + I18nUtil.i18nCode("pers_import_photoFormatError")
                                            + "<br>");
                                    }
                                } else {
                                    // 文件名包含特殊字符
                                    errLog.append(fileName + ","
                                        + I18nUtil.i18nCode("pers_import_photoContainSpecialCharacters") + "</br>");
                                }
                            } else {
                                // 文件格式不正确
                                errLog
                                    .append(fileName + "," + I18nUtil.i18nCode("pers_face_photoFormatError") + "<br>");
                            }
                        }
                        List<PersPerson> persPersonList = persPersonDao.findByPinIn(pinSet);
                        Map<String, PersPerson> persPersonMap =
                            CollectionUtil.listToKeyMap(persPersonList, PersPerson::getPin);
                        List<PersPerson> savePersonList = new ArrayList<>();
                        List<PersPerson> updateBioPhotoList = new ArrayList<>();
                        List<String> delBioPinList = new ArrayList<>();
                        File file = null;
                        File sourceImg = null;
                        BufferedImage src = null;
                        Map<String, String> faceFailTypeMap =
                            baseDictionaryValueService.getDictionaryValuesMap("PersFaceFailType");
                        faceFailTypeMap.put("-101", "pers_face_photoFormatError");
                        for (String personPin : fileMap.keySet()) {
                            PersPerson person = persPersonMap.get(personPin);
                            file = fileMap.get(personPin);
                            if (Objects.nonNull(person)) {
                                String persPhotoPath = "";
                                if (importPhoto) {
                                    // 保存为用户头像
                                    persPhotoPath = FileUtil.saveFileToServer("pers", "user/avatar", personPin + ".jpg",
                                        FileUtil.getFileBase64Str(file.getPath()));
                                    person.setPhotoPath(persPhotoPath);
                                    savePersonList.add(person);
                                    if (!importCropFace) {
                                        corrLog.append(
                                            file.getName() + "," + I18nUtil.i18nCode("common_op_succeed") + "</br>");
                                    }
                                } else if (importCropFace) {
                                    persPhotoPath = FileUtil.saveFileToServer("pers", "user/avatar",
                                        System.currentTimeMillis() + ".jpg", FileUtil.getFileBase64Str(file.getPath()));
                                    item.setCropPhotoPath(persPhotoPath);
                                }
                                if (importCropFace && StringUtils.isNotBlank(persPhotoPath)) {
                                    // 保存为比对照片
                                    realPersPhotoPath = FileUtil.getLocalFullPath(persPhotoPath);
                                    sourceImg = new File(realPersPhotoPath);
                                    int result = -100;
                                    if (sourceImg.exists()) {
                                        // 抠图服务使用原图
                                        if (baseCropFaceService.loadDetectServiceSuccess()) {
                                            boolean jpgImage = ImgEncodeUtil.isSupportImageType(sourceImg,
                                                FileType.JPEG, FileType.PNG);
                                            if (!jpgImage) {
                                                result = -101;
                                            } else {
                                                // 判断图片是否需要旋转，需要即旋转至正向
                                                RotateImageUtil.rotateImage(sourceImg);
                                                result = baseCropFaceService
                                                    .cropFace(FileUtil.getLocalFullPath(persPhotoPath), personPin);
                                            }
                                        } else {
                                            src = ImageIO.read(sourceImg);
                                            if (src.getWidth() * src.getHeight() > 1024 * 1024) {// 超过1024*1024使用缩略图进行抠图，防止动态库挂
                                                result = baseCropFaceService.cropFace(
                                                    FileUtil.getLocalFullPath(FileUtil.getThumbPath(persPhotoPath)),
                                                    personPin);
                                            } else {
                                                result = baseCropFaceService.cropFace(realPersPhotoPath, personPin);
                                            }
                                        }
                                    }
                                    // 抠图失败
                                    String retStr = String.valueOf(result);
                                    if (faceFailTypeMap.containsKey(retStr)) {
                                        errLog.append(file.getName() + "," + I18nUtil.i18nCode("pers_face_validFailMsg")
                                            + I18nUtil.i18nCode(faceFailTypeMap.get(retStr)) + "</br>");
                                    } else if (result <= -100) {
                                        errLog.append(file.getName() + ","
                                            + I18nUtil.i18nCode("pers_import_cropFaceFail") + "</br>");
                                    } else {
                                        corrLog.append(
                                            file.getName() + "," + I18nUtil.i18nCode("common_op_succeed") + "</br>");
                                    }
                                    // 抠图失败删除已有比对照片
                                    boolean delBioTemplate = true;
                                    if (result > 0) {
                                        // 推送人脸照片
                                        opFaceImgToFaceServer(person);
                                        // 更新人员抠图信息
                                        updateBioPhotoList.add(person);
                                        if (importTemplateFace) {
                                            ZKResultMsg faceTemplate = updateBioTemplate(person);
                                            if (faceTemplate != null && !"0".equals(faceTemplate.getRet())) {
                                                String failMsg =
                                                    PersConstants.TEMPLATE_SERVER_ERROR_MAP.get(faceTemplate.getRet());
                                                if (StringUtils.isBlank(failMsg)) {
                                                    failMsg = I18nUtil.i18nCode("pers_person_serverOffline");
                                                }
                                                errLog.append(file.getName() + ","
                                                    + I18nUtil.i18nCode("pers_person_faceTemplateError6") + ","
                                                    + I18nUtil.i18nCode("auth_license_reason") + failMsg + "</br>");
                                            } else {
                                                delBioTemplate = false;
                                            }
                                        }
                                    }
                                    if (delBioTemplate) {
                                        delBioPinList.add(personPin);
                                    }

                                    if (StringUtils.isNotBlank(item.getCropPhotoPath())) {
                                        File tempFile = new File(FileUtil.getLocalFullPath(item.getCropPhotoPath()));
                                        if (tempFile.exists()) {
                                            // 删除临时图片
                                            tempFile.delete();
                                        }
                                    }
                                }
                                PersPersonUtil.encryptPersonPhoto(persPhotoPath, personPin);

                            } else {
                                // 人员编号不存在
                                errLog.append(file.getName() + ","
                                    + I18nUtil.i18nCode("pers_person_pinError", personPin) + "</br>");
                            }
                        }
                        if (!savePersonList.isEmpty()) {
                            persPersonDao.save(savePersonList);
                        }
                        if (!delBioPinList.isEmpty()) {
                            persBioTemplateService.deleteByPersonPinsAndBioType(delBioPinList,
                                BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
                        }
                        if (!updateBioPhotoList.isEmpty()) {
                            updatePersBioPhotos(updateBioPhotoList);
                        }

                    }
                } else {
                    errLog.append(I18nUtil.i18nCode("pers_import_zipFileNotPhoto"));
                }
                // 删除解压缩产生的文件
                FileUtil.deleteDirectory(filePath);
            }
        } catch (Exception e) {
            e.printStackTrace();
            zkResultMsg.setRet("fail");
            zkResultMsg.setMsg("common_op_failed");
        }
        Map<String, StringBuilder> map = new HashMap<>();
        map.put("corrLog", corrLog);
        map.put("errLog", errLog);
        zkResultMsg.setData(map);
        return I18nUtil.i18nMsg(zkResultMsg);
    }

    @Override
    public String unZipToFile(String filePath, String outPath) {
        String newFilePath = FileUtil.getLocalFullPath(filePath);
        // 解压的目录位置
        String newOutPath = FileUtil.getLocalFullPath(outPath);
        // 解压上传的压缩包文件
        return FileUnZipUtil.unZipToFile(newFilePath, newOutPath);
    }

    @Override
    public List<PersPersonCloudItem> getUpdatePersonItem(Date lastUpdate) {
        PersPersonCloudItem persPersonItem = new PersPersonCloudItem();
        persPersonItem.setLastUpdateDate(lastUpdate);
        // // 过滤离职人员恢复的
        // persPersonItem.setIsFromNotIn(PersConstants.PERS_USER_LEAVE_ADDED);
        List<PersPersonCloudItem> persPersonCloudItems = (List<PersPersonCloudItem>)persPersonDao
            .getItemsBySql(persPersonItem.getClass(), SQLUtil.getSqlByItem(persPersonItem));
        buildCloudCard(persPersonCloudItems);
        return persPersonCloudItems;
    }

    @Override
    public List<String> getEmailsByDeptIds(Collection<String> deptIds) {
        return persPersonDao.getEmailsByDeptIdIn(deptIds);
    }

    @Override
    public List<String> getEmailsByAreaIds(Collection<String> areaIds) {
        return persPersonDao.getEmailsByAreaIdIn(areaIds);
    }

    @Override
    public ZKResultMsg getPersPinParams() {
        return persParamsService.getPersPinParams();
    }

    @Override
    public PersPersonItem getItemByMobile(String mobile) {
        PersPersonItem persPersonItem = null;
        PersPerson findByMobilePhone = persPersonDao.findByMobilePhone(mobile);
        if (findByMobilePhone != null) {
            persPersonItem = new PersPersonItem();
            ModelUtil.copyPropertiesIgnoreId(findByMobilePhone, persPersonItem);
        }
        return persPersonItem;
    }

    @Override
    public PersPersonItem getItemByEmail(String email) {
        PersPersonItem persPersonItem = null;
        PersPerson findByMobileEmail = persPersonDao.findByEmail(email);
        if (findByMobileEmail != null) {
            persPersonItem = new PersPersonItem();
            ModelUtil.copyPropertiesIgnoreId(findByMobileEmail, persPersonItem);
        }
        return persPersonItem;
    }

    @Override
    public ZKPageResultMsg getUserInfos(String filter, String deptId, Integer page, Integer pageSize) {
        ZKPageResultMsg resultMsg = ZKPageResultMsg.successMsg();
        PersPersonItem persPersonItem = new PersPersonItem();
        StringBuilder personQuery = new StringBuilder();
        if (StringUtils.isNotBlank(deptId)) {
            // 根据部门id过滤
            List<AuthDepartmentItem> authDepartmentItems = authDepartmentService.getItemAndChildById(deptId);
            if (!authDepartmentItems.isEmpty()) {
                String deptIds = CollectionUtil.getPropertys(authDepartmentItems, AuthDepartmentItem::getId);
                persPersonItem.setDeptId(deptIds);
            }
        }
        // 设置根据姓名拼写升序排列
        persPersonItem.setSortOrder("asc");
        persPersonItem.setSortName("nameSpell");
        personQuery.append(SQLUtil.getSqlByItem(persPersonItem));
        int orderByIndex = personQuery.indexOf("ORDER BY");
        if (StringUtils.isNotBlank(filter)) {
            // 存在过滤条件设置模糊查询过滤条件
            String query = MessageFormat.format("AND (t.PIN LIKE ''%{0}%'' OR t.NAME LIKE ''%{0}%''", filter);
            if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
                query += MessageFormat.format(" OR t.LAST_NAME LIKE ''%{0}%''", filter);
            }
            query += ") ";
            personQuery.insert(orderByIndex, query);
        }
        Pager pager =
            persPersonDao.getItemsBySql(persPersonItem.getClass(), personQuery.toString(), page - 1, pageSize);
        List<PersPersonItem> persPersonItemList = (List<PersPersonItem>)pager.getData();
        for (PersPersonItem persPerson : persPersonItemList) {
            persPerson.setDeptName(authDepartmentService.getItemById(persPerson.getDeptId()).getName());
        }
        /**
         * 没做离职人员处理
         */
        // List<AppPersonItem> appPersonItems = new ArrayList<>();
        // if (persPersonItemList != null && persPersonItemList.size() > 0) {
        // String pins = CollectionUtil.getPropertys(persPersonItemList, PersPersonItem::getPin);
        // List<PersPersonItem> PersPersonList = getItemsByPins(pins);
        // }
        // 设置分页参数
        resultMsg.setData(persPersonItemList);
        resultMsg.setPage(page);
        resultMsg.setPageSize(pager.getSize());
        resultMsg.setTotal(pager.getTotal());
        return resultMsg;
    }

    /**
     * 获取车牌中的省份
     *
     * @param carNumber
     * @return java.lang.String
     * <AUTHOR>
     * @date 2019/8/13 11:19
     */
    private String getAreaFormCarNumber(String carNumber) {
        String area = "";
        // 海外不保存车牌省份
        if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
            List<BaseDictionaryValueItem> provinceList = baseDictionaryValueService.getDictionaryValues("parkProvince");
            for (BaseDictionaryValueItem baseDictionaryValueItem : provinceList) {
                if (carNumber.startsWith(baseDictionaryValueItem.getDictValue())) {
                    area = baseDictionaryValueItem.getDictValue();
                    break;
                }
            }
        }
        return area;
    }

    @Override
    public List<String> getPinNotFromAD(String persFrom) {
        return persPersonDao.getPinIsNotFromAD(persFrom);
    }

    @Override
    public List<PersPersonItem> findByIsFrom(String persFrom) {
        List<PersPerson> persPersonList = persPersonDao.findByIsFrom(persFrom);
        List<PersPersonItem> persPersonItemList = new ArrayList<>();
        if (persPersonList != null && persPersonList.size() > 0) {
            persPersonItemList = ModelUtil.copyListProperties(persPersonList, PersPersonItem.class);
            buildCard(persPersonItemList);
        }
        return persPersonItemList;
    }

    @Override
    public List<PersPersonItem> getByEmail(String email) {
        List<PersPerson> persPersonList = persPersonDao.getByEmail(email);
        return ModelUtil.copyListProperties(persPersonList, PersPersonItem.class);
    }

    @Override
    public boolean checkSMSModemParam() {
        return pers2OtherService.completeSMSModemInfo();
    }

    @Override
    public boolean checkShowSMS() {
        return pers2OtherService.checkShowSMS();
    }

    @Override
    public ZKResultMsg validCropFace(String photoPath) {
        int result = -100;
        Map<String, String> faceFailTypeMap = baseDictionaryValueService.getDictionaryValuesMap("PersFaceFailType");
        String localFullPath = FileUtil.getLocalFullPath(photoPath);
        File sourceImg = new File(localFullPath);
        // 判断图片是否需要旋转，需要即旋转至正向
        RotateImageUtil.rotateImage(sourceImg);
        ZKResultMsg msg = baseCropFaceService.validCropFace(localFullPath, BaseDataConstants.CROP_TYEP_BASE64);
        String retStr = msg.getRet();
        result = Integer.parseInt(retStr);
        if (faceFailTypeMap.containsKey(retStr)) {
            msg.setRet(retStr);
            msg.setMsg(I18nUtil.i18nCode("pers_face_validFailMsg") + I18nUtil.i18nCode(faceFailTypeMap.get(retStr)));
        } else if (result <= -100) {
            msg.setRet(retStr);
            msg.setMsg(I18nUtil.i18nCode("pers_import_cropFaceFail"));
        } else {
            msg.setRet("ok");
        }
        return msg;
    }

    /**
     * 删除抠图照片
     *
     * @param personPin
     * @return void
     * <AUTHOR>
     * @date 2020/4/27 16:27
     */
    @Override
    public void deleteCropFace(String personPin) {
        String cropFacePath = FileUtil.getCropFacePath(personPin);
        File cropFaceFile = new File(FileUtil.getLocalFullPath(cropFacePath));
        if (cropFaceFile.exists()) {
            // 如果是文件
            if (cropFaceFile.isFile()) {
                cropFaceFile.delete();
            } else if (cropFaceFile.isDirectory()) {
                File[] childFiles = cropFaceFile.listFiles();
                // 文件夹没有内容,删除文件夹
                if (childFiles == null || childFiles.length == 0) {
                    cropFaceFile.delete();
                } else {
                    // 删除文件夹内容
                    for (File cropFaceFiles : cropFaceFile.listFiles()) {
                        cropFaceFiles.delete();
                    }
                    // 删除文件夹
                    cropFaceFile.delete();
                }
            }
        }
    }

    @Override
    public boolean saveItemByTempPerson(PersPersonItem persPersonItem) {
        PersPerson persPerson = null;
        if (StringUtils.isNotBlank(persPersonItem.getPin())) {
            persPerson = persPersonDao.findByPin(persPersonItem.getPin());
        }
        if (persPerson == null) {
            persPerson = new PersPerson();
        }
        ModelUtil.copyProperties(persPersonItem, persPerson);
        if (StringUtils.isNotBlank(persPersonItem.getPositionCode())) {
            PersPosition persPosition = persPositionDao.findByCode(persPersonItem.getPositionCode());
            if (persPosition != null) {
                persPerson.setPersPosition(persPosition);
            }
        }
        persPersonDao.save(persPerson);
        return false;
    }

    @Override
    public List<String> getPinsByIds(List<String> personIds) {
        if (!personIds.isEmpty()) {
            return persPersonDao.getPinsByIds(personIds);
        }
        return null;
    }

    @Override
    public List<String> getAllPinList() {
        return persPersonDao.findAllPin();
    }

    @Override
    public String createEncryptedDynamicCode(String pin) {
        if (StringUtils.isNotBlank(pin)) {
            PersPersonItem persPersonItem = getItemByPin(pin);
            String cardNo = "";
            if (persPersonItem != null) {
                PersCardItem cardItem = persCardService.getMasterCardByPersonId(persPersonItem.getId());
                if (cardItem != null) {
                    String cardHex = baseSysParamService.getValByName("pers.cardHex");
                    cardNo = cardItem.getCardNo();
                    if (StringUtils.isNotBlank(cardNo)) {
                        if (cardHex.equals(PersConstants.CARD_HEXADECIMAL)) {
                            cardNo = new BigInteger(cardNo, 16).toString(10);
                        }
                    }
                }
            } else {
                return String.valueOf(PersConstants.PERSONID_NOTEXIST);
            }

            String qrcodeType = baseSysParamService.getValByName("qrcode.type");
            // 动态二维码
            if (BaseDataConstants.QRCODE_TYPE_DYNAMIC.equals(qrcodeType)) {
                StringBuffer content = new StringBuffer();
                content.append(pin + "\t");
                if (StringUtils.isNotBlank(cardNo)) {
                    content.append(cardNo);
                }
                // 获取当前零时区日期
                Calendar createCalendar = Calendar.getInstance(TimeZone.getTimeZone("UTC+0"));
                // 二维码创建时间 UNIX格式时间戳
                String createTime = String.valueOf(createCalendar.getTimeInMillis() / 1000L);
                // 二维码有效时长
                String validTime = baseSysParamService.getValByName("qrcode.validTime");
                if (StringUtils.isBlank(validTime)) {
                    validTime = "30";
                }
                content.append("\t" + createTime + "\t" + validTime + "\t");
                return baseSysParamService.createEncryptQrcode(content.toString());
            } else if (BaseDataConstants.QRCODE_TYPE_DISABLE.equals(qrcodeType)) {
                return String.valueOf(PersConstants.PERSON_QRCODE_DISABLE);
            } else {
                if (StringUtils.isNotBlank(cardNo)) {
                    return cardNo;
                }
                return String.valueOf(PersConstants.PERSON_QRCODE_NOCARD);
            }
        }
        return null;
    }

    @Override
    public void encryptPersPerson() {
        if (isOpenSecurityEncrypt) {
            Set<PersPerson> allPersonList = new HashSet<>();
            List<PersPerson> persPersonList = persPersonDao.findByMobilePhoneIsNotNull();
            for (PersPerson persPerson : persPersonList) {
                persPerson.setMobilePhone(FoldexUtil.encryptByRandomSey(persPerson.getMobilePhone()));
            }
            if (persPersonList.size() > 0) {
                allPersonList.addAll(persPersonList);
            }
            persPersonList = persPersonDao.findByEmailIsNotNull();
            for (PersPerson persPerson : persPersonList) {
                persPerson.setEmail(FoldexUtil.encryptByRandomSey(persPerson.getEmail()));
            }
            if (persPersonList.size() > 0) {
                allPersonList.addAll(persPersonList);
            }
            persPersonList = persPersonDao.findByPersonPwdIsNotNull();
            for (PersPerson persPerson : persPersonList) {
                persPerson.setPersonPwd(FoldexUtil.encryptByRandomSey(persPerson.getPersonPwd()));
            }
            if (persPersonList.size() > 0) {
                allPersonList.addAll(persPersonList);
            }
            persPersonList = persPersonDao.findByIdCardIsNotNull();
            for (PersPerson persPerson : persPersonList) {
                persPerson.setIdCard(FoldexUtil.encryptByRandomSey(persPerson.getIdCard()));
            }
            if (persPersonList.size() > 0) {
                allPersonList.addAll(persPersonList);
            }
            persPersonList = persPersonDao.findByIdCardPhysicalNoIsNotNull();
            for (PersPerson persPerson : persPersonList) {
                persPerson.setIdCardPhysicalNo(FoldexUtil.encryptByRandomSey(persPerson.getIdCardPhysicalNo()));
            }
            if (persPersonList.size() > 0) {
                allPersonList.addAll(persPersonList);
            }
            if (allPersonList.size() > 0) {
                List<List<PersPerson>> personList = CollectionUtil.split(allPersonList, CollectionUtil.splitSize);
                personList.forEach(persons -> {
                    persPersonDao.saveAll(persons);
                });
            }
        }
    }

    @Override
    public Pager getItemsByPhotoFilter(PersPersonItem condition, int page, int size) {
        String sqlByItem = SQLUtil.getSqlByItem(condition);
        StringBuffer insertBuffer = new StringBuffer(sqlByItem);
        int orderByIndex = insertBuffer.lastIndexOf("ORDER BY");
        insertBuffer.insert(orderByIndex, " AND t.PHOTO_PATH IS NOT NULL ");
        return persPersonDao.getItemsBySql(condition.getClass(), insertBuffer.toString(), page, size);
    }

    @Override
    public Integer countByIdsAndPhotoIsNotNull(List<String> personIds) {
        return persPersonDao.countByIdsAndPhotoIsNotNull(personIds);
    }

    /**
     * 构建名单库人员信息
     *
     * @param persPersonnallistPerson
     * @param persPerson
     * @return void
     * <AUTHOR>
     * @date 2021-05-31 15:20
     * @since 1.0.0
     */
    private void buildPersonInfo(PersPersonnallistPerson persPersonnallistPerson, PersPerson persPerson) {
        persPersonnallistPerson.setPersonId(persPerson.getId());
        persPersonnallistPerson.setLinkTbl(PersConstants.PERS_PERSON);
        persPersonnallistPerson.setPersonName(persPerson.getName());
        persPersonnallistPerson.setPersonPin(persPerson.getPin());
        persPersonnallistPerson.setPersonGender(persPerson.getGender());
        persPersonnallistPerson.setPersonBirthday(persPerson.getBirthday());
        persPersonnallistPerson.setIdCard(persPerson.getIdCard());
        persPersonnallistPerson.setLastName(persPerson.getLastName());
        if (Objects.nonNull(persPerson.getPersPosition())) {
            persPersonnallistPerson.setPositionName(persPerson.getPersPosition().getName());
        }
        persPersonnallistPerson.setMobilePhone(persPerson.getMobilePhone());
        persPersonnallistPerson.setEmail(persPerson.getEmail());
    }

    @Override
    public String getPersonPinByCardNo(String cardNo) {
        if (StringUtils.isNotBlank(cardNo)) {
            final List<PersCard> findByCardNo = persCardDao.findByCardNo(cardNo);
            if (findByCardNo != null && findByCardNo.size() > 0) {
                return findByCardNo.get(0).getPersonPin();
            }
        }
        return "";
    }

    /**
     * 推送人脸到人脸服务
     *
     * @param person:
     * @return void
     * @throws @date 2021-09-06 9:36
     * <AUTHOR>
     * @since 1.0.0
     */
    private void opFaceImgToFaceServer(PersPerson person) {
        if (Objects.nonNull(other2AiCenterService)) {
            // 推送人脸照片到人脸服务
            String cropFacePath =
                FileUtil.getCropFacePath(person.getPin()) + FileUtil.separator + person.getPin() + ".jpg";
            other2AiCenterService.pushFaceByPersonIdAndImg(person.getId(), cropFacePath);
        }
    }

    @Override
    public ApiResultMessage updateUserPhoto(PersPersonItem item, String personPhoto) {
        String pin = item.getPin();
        String photoPath = item.getPhotoPath();
        File sourceImg = new File(FileUtil.getLocalFullPath(photoPath));
        BufferedImage src = null;
        try {
            if (sourceImg.exists()) {
                int result = 0;
                // 抠图服务使用原图
                if (baseCropFaceService.loadDetectServiceSuccess()) {
                    // 判断图片是否需要旋转，需要即旋转至正向
                    RotateImageUtil.rotateImage(sourceImg);
                    result = baseCropFaceService.cropFace(FileUtil.getLocalFullPath(photoPath), pin);
                } else {
                    src = ImageIO.read(sourceImg);
                    if (src.getWidth() * src.getHeight() > 1024 * 1024) {// 超过1024*1024使用缩略图进行抠图，防止动态库挂
                        result = baseCropFaceService
                            .cropFace(FileUtil.getLocalFullPath(FileUtil.getThumbPath(photoPath)), pin);
                    } else {
                        result = baseCropFaceService.cropFace(FileUtil.getLocalFullPath(photoPath), pin);
                    }
                }
                if (result < 0) {
                    return ApiResultMessage.message(PersConstants.PERSON_CROPFACE_FAILE,
                        I18nUtil.i18nCode("pers_import_cropFaceFail"));
                }
                persBioTemplateService.deleteByPersonPinAndBioType(pin, BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
                // 图片加密
                PersPersonUtil.encryptPersonPhoto(item.getPhotoPath(), pin);
                // 推送人脸照片
                if (result > 0) {
                    PersPerson persPerson = persPersonDao.findByPin(pin);
                    persPerson.setPhotoPath(photoPath);
                    persPersonDao.save(persPerson);
                    // 更新抠图
                    updatePersBioPhoto(persPerson);
                    opFaceImgToFaceServer(persPerson);
                    // 通知其他模块更新人员头像
                    PersUserPic2OtherItem persUserPic2OtherItem = new PersUserPic2OtherItem();
                    persUserPic2OtherItem.setPersonId(persPerson.getId());
                    persUserPic2OtherItem.setPin(persPerson.getPin());
                    persUserPic2OtherItem.setContent(personPhoto);
                    persUserPic2OtherItem.setPhotoPath(photoPath);
                    pers2OtherService.pushUserPic2OtherModules(persUserPic2OtherItem);

                    PersBioPhoto2OtherItem persBioPhoto2OtherItem = new PersBioPhoto2OtherItem();
                    persBioPhoto2OtherItem.setBioType((short)9);
                    persBioPhoto2OtherItem.setPin(pin);
                    persBioPhoto2OtherItem.setPersonId(persPerson.getId());
                    String cropFacePath = FileUtil.getCropFacePath(pin) + FileUtil.separator + pin + ".jpg";
                    persBioPhoto2OtherItem.setBioPhotoPath(cropFacePath);
                    pers2OtherService.pushBioPhoto2OtherModules(persBioPhoto2OtherItem);

                }
            }
        } catch (IOException e) {
            logger.error("read img error", e);
        }
        return ApiResultMessage.successMessage();
    }

    @Override
    public ZKResultMsg importPersonInfo(List<PersPersonImportItem> itemList, boolean updateExistData) {

        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("pers_import_overData", itemList.size());
        }
        // 基础变量定义
        // 系统支持的人员pin号长度
        int pinLength = Integer.parseInt(baseSysParamService.getValByName("pers.pinLen"));
        // pin号是否支持字母
        String pinSupportLetter = baseSysParamService.getValByName("pers.pinSupportLetter");
        // 支持多卡
        String cardsSupport = baseSysParamService.getValByName("pers.cardsSupport");
        // 卡号进制
        String cardHex = baseSysParamService.getValByName("pers.cardHex");
        // 离职人员保留pin号
        String pinRetain = baseSysParamService.getValByName("pers.pinRetain");
        // 性别的国际化，M男F女
        Map<String, String> genderMap = baseDictionaryValueService.getDictionaryValuesMap("sex");
        // 导入人员pin号的集合，用于过滤重复的pin号
        Set<String> uniquePinSet = new HashSet<>();
        // 离职人员pin
        Set<String> leavePins = new HashSet<>();
        // 导入数据中所有人员的卡号集合，用于去重,key: cardNo value:pin
        HashMap<String, String> cardNoAndPinMap = new HashMap<>(itemList.size());
        // 人员工号跟卡的关系，key:pin value: cardNos
        HashMap<String, List<String>> pinAndCardsMap = new HashMap<>(itemList.size());

        // 人员工号跟自定义属性的关系，key:pin value: PersAttributeExtItem
        HashMap<String, PersAttributeExtItem> pinAndAttrExtMap = new HashMap<>(itemList.size());
        List<PersAttributeExtItem> persAttributeExtItemList = new ArrayList<>();
        // 人员工号跟自定义属性的关系，key:pin value: PersCertificateItem
        HashMap<String, PersCertificateItem> pinAndCertMap = new HashMap<>(itemList.size());
        List<PersCertificateItem> persCertificateItemList = new ArrayList<>();
        List<BaseDictionaryValueItem> certTypeList = baseDictionaryValueService.getDictionaryValues("certificateType");
        HashMap<String, String> certNumbers = new HashMap<>(itemList.size());
        // 系统已存在证件 key:dictValue value:code
        Map<String, String> certMap = new HashMap<>();
        for (BaseDictionaryValueItem certTypeItem : certTypeList) {
            certMap.put(certTypeItem.getDictValue(), certTypeItem.getCode());
        }
        // 导入数据中所有人员的车牌集合，用于去重,key: carPlate value:pin
        HashMap<String, String> carPlateAndPinMap = new HashMap<>(itemList.size());
        // 人员工号跟车牌的关系，key:pin value: carPlates
        HashMap<String, List<String>> pinAndCarPlatesMap = new HashMap<>(itemList.size());

        // 导入数据中所有人员的邮箱集合，用于去重,key: email value:pin
        HashMap<String, String> emailAndPinMap = new HashMap<>(itemList.size());
        // 导入数据中所有人员的手机号码集合，用于去重,key: mobilePhone value:pin
        HashMap<String, String> mobilePhoneAndPinMap = new HashMap<>(itemList.size());
        // 部门ID和名称的关系
        HashMap<String, String> deptIdAndNameMap = new HashMap<>(itemList.size());
        // 部门编码和ID的关系
        HashMap<String, String> deptCodeAndIdMap = new HashMap<>(itemList.size());
        // 职位编码和ID的关系
        HashMap<String, PersPosition> codeAndPositionMap = new HashMap<>(itemList.size());

        int importSize = itemList.size();
        int notUpdateSize = 0;
        int beginProgress = 20;

        // 导入的数据中，所有pin已经存在的人员，key为pin号
        Map<String, PersPerson> existPersPersonMap = new HashMap<>(itemList.size());
        // 导入的数据中，所有pin已经存在的名单库人员，key为pin号
        Map<String, List<PersPersonnallistPerson>> existListPersonMap = new HashMap<>(itemList.size());
        List<PersPersonnallistPersonItem> updateListPersonList = new ArrayList<>();
        // 导入的数据中，所有pin已经存在的证件，key为pin号
        Map<String, PersCertificateItem> existsCertificateItemMap = new HashMap<>(itemList.size());
        // 数据库中，已经存在的邮箱
        Set<String> existsEmails = new HashSet<>();
        // 数据库中，已经存在的手机号码
        Set<String> existsMobilePhones = new HashSet<>();
        // 数据库中，已经存在的卡号
        Set<String> existsCardNos = new HashSet<>();
        // 数据库中，已经存在的车牌
        Set<String> existsCarPlates = new HashSet<>();
        // 数据库中，存在的楼栋信息
        Map<String, VdbBuilding2otherItem> existVdbBuildingItemMap = new HashMap<>();
        // 数据库中，存在的单元信息
        Map<String, VdbUnit2otherItem> existVdbUnitItemMap = new HashMap<>();
        // 卡号和pin的关系
        HashMap<String, String> existsCardNoAndPinMap = new HashMap<>(itemList.size());
        // 数据库已存在pin和主卡的关系，用于判断是否被消费模块使用
        HashMap<String, String> existsPinAndMasterCardNoMap = new HashMap<>(itemList.size());
        // 车牌和pin的关系
        Map<String, String> existsCarPlateAndPinMap = new HashMap<>(itemList.size());
        // 已经存在的人员住宅信息与id之间的关系,
        Map<String, Vdb4PersPersonItem> existsPersPinAndVdbInfoMap = new HashMap<>(itemList.size());
        // 人员与住宅信息之间的导入关系
        Map<String, Vdb4PersPersonItem> persPinAndVdbInfoMap = new HashMap<>(itemList.size());
        // 已存在数量
        int existCount = 0;
        // 判断是否存在停车模块
        boolean park4PersPersonServiceExist = Objects.nonNull(park4PersPersonService);
        // 判断是否存在可视对讲模块
        boolean vdb4PersPersonServiceExist = Objects.nonNull(vdb4PersPersonService);
        String dataSource = DataSourceConfig.getDbType();
        // sql数据库不区分大小写
        boolean pinLowerCase = false;
        if (ZKConstant.SQLSERVER.equals(dataSource)) {
            pinLowerCase = true;
        }

        // 分批处理，一次处理800人 已存在人员判断是否更新数据
        List<List<PersPersonImportItem>> personImportItemsList =
            CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<PersPersonImportItem> importItemList : personImportItemsList) {
            // 取出待导入数据中人员的pin号
            Collection<String> importPins =
                CollectionUtil.getPropertyList(importItemList, PersPersonImportItem::getPin, "-1");
            // 根据pin号查出已经存在的人员
            List<PersPerson> existPersonList = persPersonDao.findByPinIn(importPins);
            if (existPersonList != null && existPersonList.size() > 0) {
                if (pinLowerCase) {
                    existPersPersonMap
                        .putAll(CollectionUtil.listToKeyMap(existPersonList, p -> (p.getPin()).toLowerCase()));
                } else {
                    existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPerson::getPin));
                }

            }

            List<PersPersonnallistPerson> existListPersonList =
                persPersonnallistPersonDao.findByPersonPinIn(importPins);
            if (existListPersonList != null && existListPersonList.size() > 0) {
                existListPersonMap.putAll(
                    existListPersonList.stream().collect(Collectors.groupingBy(PersPersonnallistPerson::getPersonPin)));
            }

            // 离职人员保留pin号
            if ("true".equals(pinRetain)) {
                List<String> leavePinList = persLeavePersonDao.getPinListByPinIn(importPins);
                if (!CollectionUtil.isEmpty(leavePinList)) {
                    leavePins.addAll(leavePinList);
                }
            }

            // 部门编码收集
            Collection<String> deptCodeSet =
                CollectionUtil.getPropertyList(importItemList, PersPersonImportItem::getDeptCode, "-1");
            List<AuthDepartmentItem> deptItemByCodeIn = authDepartmentService.findItemByCodeIn(deptCodeSet);
            deptItemByCodeIn.forEach(item -> {
                deptCodeAndIdMap.put(item.getCode(), item.getId());
                deptIdAndNameMap.put(item.getId(), item.getName());
            });

            // 职位编码收集
            Collection<String> positionCodeSet =
                CollectionUtil.getPropertyList(itemList, PersPersonImportItem::getPositionCode, "-1");
            List<List<String>> positionCodeList = CollectionUtil.split(positionCodeSet, CollectionUtil.splitSize);
            for (List<String> positionCodes : positionCodeList) {
                List<PersPosition> positionByCodeIn = persPositionDao.findByCodeIn(positionCodes);
                if (positionByCodeIn != null && positionByCodeIn.size() > 0) {
                    codeAndPositionMap.putAll(CollectionUtil.listToKeyMap(positionByCodeIn, PersPosition::getCode));
                }
            }

            // 根据pin号查出已经存在的卡号
            List<PersCard> existsCardList = persCardDao.findByPersonPinIn(importPins);
            existsCardList.forEach(persCard -> {
                existsCardNoAndPinMap.put(persCard.getCardNo(), persCard.getPersonPin());
            });
            List<String> importCardNos = new ArrayList<>();
            importItemList.forEach(persItem -> {
                if (StringUtils.isNotBlank(persItem.getCardNos())) {
                    for (String cardNo : persItem.getCardNos().split("&")) {
                        importCardNos.add(cardNo);
                    }
                }
            });

            List<PersCard> existsPersonCardNos = persCardDao.findByCardNoIn(importCardNos);
            if (!CollectionUtil.isEmpty(existsPersonCardNos)) {
                existsCardNos.addAll(CollectionUtil.getPropertyList(existsPersonCardNos, PersCard::getCardNo, "-1"));
            } // 根据pin号查出已经存在的主卡
            List<PersCard> existsMasterCardList =
                persCardDao.findByPersonPinInAndCardType(importPins, PersConstants.MAIN_CARD);
            existsMasterCardList.forEach(existsMasterCard -> {
                existsPinAndMasterCardNoMap.put(existsMasterCard.getPersonPin(), existsMasterCard.getCardNo());
            });

            // 根据pin号查出已经存在的证件
            List<PersCertificateItem> existsCertificateList = persCertificateService.getItemByPersonPinIn(importPins);
            if (existsCertificateList != null && existsCertificateList.size() > 0) {
                existsCertificateItemMap
                    .putAll(CollectionUtil.listToKeyMap(existsCertificateList, PersCertificateItem::getPin));
            }

            // 根据人员id查出已经存在的自定义属性
            // 已存在人员id与人员关系 key为id
            Map<String, PersPerson> existIdAndPersPersonMap = new HashMap<>();
            existIdAndPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPerson::getId));

            // 根据人员pin查出已经存在的车牌
            if (park4PersPersonServiceExist) {
                List<Park4PersPersonItem> park4PersPersonItemList =
                    park4PersPersonService.findbyPersPersonPinIn((List<String>)importPins);
                park4PersPersonItemList.forEach(park4PersPersonItem -> {
                    existsCarPlateAndPinMap.put(park4PersPersonItem.getParkCarNumbers(),
                        park4PersPersonItem.getPersonPin());
                });

                List<String> importCarPlates = new ArrayList<>();
                importItemList.forEach(persItem -> {
                    if (StringUtils.isNotBlank(persItem.getCarPlate())) {
                        for (String carPlate : persItem.getCarPlate().split("&")) {
                            importCarPlates.add(carPlate);
                        }
                    }
                });

                List<Park4PersPersonItem> existsCarPlateList =
                    park4PersPersonService.getParkCarNumberIn((List<String>)importCarPlates);
                if (!CollectionUtil.isEmpty(existsCarPlateList)) {
                    existsCarPlates.addAll(CollectionUtil.getPropertyList(existsCarPlateList,
                        Park4PersPersonItem::getParkCarNumbers, "-1"));
                }
            }
            Set<String> existPersonIds = existIdAndPersPersonMap.keySet();
            // 查询已经存在住宅信息的人员信息
            if (vdb4PersPersonServiceExist) {
                // 取出待导入数据中人员的楼栋名称信息
                Collection<String> importBuildingNames =
                    CollectionUtil.getPropertyList(importItemList, PersPersonImportItem::getVdbBuildingName, "-1");
                List<VdbBuilding2otherItem> existVdbBuildingItems =
                    vdb4PersPersonService.getVdbBuildingByNames(importBuildingNames);
                if (existVdbBuildingItems != null && existVdbBuildingItems.size() > 0) {
                    existVdbBuildingItemMap =
                        CollectionUtil.listToKeyMap(existVdbBuildingItems, VdbBuilding2otherItem::getBuildingName);
                }
                // 取出待导入数据中人员的单元名称信息
                Collection<String> importUnitNames =
                    CollectionUtil.getPropertyList(importItemList, PersPersonImportItem::getVdbUnitName, "-1");
                // 根据单元名称查出已经存在的单元信息
                List<VdbUnit2otherItem> existVdbUnitItems = vdb4PersPersonService.getVdbUnitByNames(importUnitNames);
                existVdbUnitItemMap = CollectionUtil.listToKeyMap(existVdbUnitItems, VdbUnit2otherItem::getUnitName);
                List<Vdb4PersPersonItem> vdb4PersPersonItems =
                    vdb4PersPersonService.findByPersonIdIn(existPersonIds.stream().collect(Collectors.toList()));
                if (vdb4PersPersonItems != null && vdb4PersPersonItems.size() > 0) {
                    vdb4PersPersonItems.forEach(vdb4PersPersonItem -> {
                        // 人员可视对讲信息与pin之间的关系
                        existsPersPinAndVdbInfoMap.put(
                            existIdAndPersPersonMap.get(vdb4PersPersonItem.getPersonId()).getPin(), vdb4PersPersonItem);
                    });
                }
            }
            // 查出已经存在的邮箱
            Collection<String> importEmails =
                CollectionUtil.getPropertyList(importItemList, PersPersonImportItem::getEmail, "-1");
            List<PersPerson> existsPersonEmails = persPersonDao.getByEmailIn(importEmails);
            if (!CollectionUtil.isEmpty(existsPersonEmails)) {
                existsEmails.addAll(CollectionUtil.getPropertyList(existsPersonEmails, PersPerson::getEmail, "-1"));
            }
            // 查出已经存在的手机
            Collection<String> importMobilePhones =
                CollectionUtil.getPropertyList(importItemList, PersPersonImportItem::getMobilePhone, "-1");
            List<PersPerson> existsPersonMobilePhones = persPersonDao.getByMobilePhoneIn(importMobilePhones);
            if (!CollectionUtil.isEmpty(existsPersonMobilePhones)) {
                existsMobilePhones
                    .addAll(CollectionUtil.getPropertyList(existsPersonMobilePhones, PersPerson::getMobilePhone, "-1"));
            }

        }

        PersPerson persPerson = null;
        // pin号与待入库的人员关系 key为pin
        HashMap<String, PersPerson> pinAndPersPersonMap = new HashMap<>(itemList.size());

        // 做数据校验检查并移除异常数据以及收集关键数据，方便in查询
        // pin号校验
        Iterator<PersPersonImportItem> itemIterator = itemList.iterator();
        String carNumber = null;
        Date date = new Date();
        int maxNameLen = 25;
        if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
            maxNameLen = 25;
        }
        while (itemIterator.hasNext()) {
            PersPersonImportItem personItem = itemIterator.next();

            // 人员编号非空校验
            if (StringUtils.isBlank(personItem.getPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinNotEmpty"))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Personnel ID is empty");
                continue;
            }
            // 不更新已存在数据
            if (!updateExistData && existPersPersonMap.containsKey(personItem.getPin())) {
                notUpdateSize++;
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Do not update existing data");
                continue;
            }
            // 判断人员pin号长度
            if (personItem.getPin().length() > pinLength) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                        personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinTooLong", personItem.getPin()))));
                itemIterator.remove();
                logger
                    .info("Row " + personItem.getRowNum() + ": Personnel ID " + personItem.getPin() + " is too long!");
                continue;
            }
            if ("false".equals(pinSupportLetter)) {
                if (PersRegularUtil.zeroPattern.matcher(personItem.getPin()).matches()) {
                    // 不能以0开头
                    progressCache.setProcess(
                        ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                            personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinStartWithZero"))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": Personnel ID start with zero");
                    continue;
                } else if (!StringUtils.isNumeric(personItem.getPin())) {
                    // 人员编号只支持数字！人员编号为：{0}
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                            I18nUtil.i18nCode("pers_import_pinSupportNumber", personItem.getPin()))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum()
                        + ": Personnel ID only supports numbers! The Personnel ID is: " + personItem.getPin());
                    continue;
                }
            }
            if ("true".equals(pinSupportLetter) && !PersRegularUtil.hasNumberOrLetter(personItem.getPin())) {
                // 人员编号只支持数字和字母组合。人员编号为：{0}
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                        I18nUtil.i18nCode("pers_import_pinNotSupportNonAlphabetic", personItem.getPin()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum()
                    + ": Personnel ID does not support non-English letters!The personnel ID is: "
                    + personItem.getPin());
                continue;
            }
            String origin = personItem.getPin();
            // 唯一校验
            if (pinLowerCase) {
                origin = origin.toLowerCase();
            }
            if (!uniquePinSet.add(origin)) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                        personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinIsRepeat", personItem.getPin()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Personnel ID " + personItem.getPin() + " is repeated");
                continue;
            }
            // 离职人员保留pin号
            if (leavePins.contains(personItem.getPin())) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                        personItem.getRowNum(), I18nUtil.i18nCode("pers_import_pinLeaved", personItem.getPin()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Personnel ID " + personItem.getPin() + " has left");
                continue;
            }
            // 当pin号长度为9时，判断pin号是否“8”或“9”开头，这是访客和酒店用的
            if (personItem.getPin().length() == 9
                && (personItem.getPin().startsWith("8") || personItem.getPin().startsWith("9"))) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_person_pinFirstValid"))));
                itemIterator.remove();
                logger.info(
                    "Row " + personItem.getRowNum() + ": First character of the personnel number can not be 8 or 9");
                continue;
            }

            // 判断人员是否已经存在，不存在则新增
            // 已存在的人员更新所有字段
            persPerson = new PersPerson();
            if (existPersPersonMap.get(origin) != null) {
                // 修改人员校验不通过，未保存人员set相关属性后还是会更新到数据库
                ModelUtil.copyProperties(existPersPersonMap.get(origin), persPerson);
            } else {
                persPerson.setPin(personItem.getPin());
                persPerson.setNumberPin(PersPersonUtil.createNumberPin(persPerson.getPin()));
                // 人员为导入数据
                persPerson.setIsFrom("PERS_USER_IMPORT_DATA");
            }
            /**
             * 对导入的新数据和没有设置密码的旧数据设置默认密码 add by bob.liu 20190920
             */
            if (StringUtils.isBlank(persPerson.getSelfPwd())) {
                persPerson.setSelfPwd(PersConstants.PERSON_SELFPWD);
            }
            if (persPerson.getEnabledCredential() != null && !persPerson.getEnabledCredential()) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_person_disabledNotOp"))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum()
                    + ": Lastname and Firstname can not contain special symbols!The personnel id is: "
                    + personItem.getPin());
                continue;
            }
            // 姓名校验
            // 姓名不能包含特殊字符
            if (personItem.getName() != null && PersRegularUtil.hasSpecialChar(personItem.getName())) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                        personItem.getRowNum(), I18nUtil.i18nCode("pers_import_nameError", personItem.getPin()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum()
                    + ": Lastname and Firstname can not contain special symbols!The personnel id is: "
                    + personItem.getPin());
                continue;
            }
            if (personItem.getName() != null && personItem.getName().length() > maxNameLen) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                        personItem.getRowNum(), I18nUtil.i18nCode("pers_import_nameTooLong", personItem.getName()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Firstname is too long!The personnel id is: "
                    + personItem.getPin());
                continue;
            }

            // 姓氏不能包含特殊字符
            if (personItem.getLastName() != null && PersRegularUtil.hasSpecialChar(personItem.getLastName())) {
                progressCache.setProcess(
                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                        personItem.getRowNum(), I18nUtil.i18nCode("pers_import_nameError", personItem.getPin()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum()
                    + ": Lastname and Firstname can not contain special symbols!The personnel id is: "
                    + personItem.getPin());
                continue;
            }
            if (personItem.getLastName() != null && personItem.getLastName().length() > maxNameLen) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                        I18nUtil.i18nCode("pers_import_nameTooLong", personItem.getLastName()))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Lastname is too long!The personnel id is: "
                    + personItem.getPin());
                continue;
            }

            if (personItem.getName() != null) {
                personItem.setName(personItem.getName().trim().replaceAll("\t", ""));
                persPerson.setName(personItem.getName());
            }
            if (personItem.getLastName() != null) {
                personItem.setLastName(personItem.getLastName().trim().replaceAll("\t", ""));
                persPerson.setLastName(personItem.getLastName());
            }

            // 生日日期校验
            if (personItem.getBirthday() != null) {
                persPerson.setBirthday(personItem.getBirthday());
            } else if (PersConstants.PERSON_DATE_SETNULL.equals(personItem.getBirthdayStr())) {
                // 导入文件有生日日期该列但没有数据，值更新为null
                persPerson.setBirthday(null);
            } else if (PersConstants.PERSON_DATE_ERROR.equals(personItem.getBirthdayStr())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_import_birthdayError"))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Date of Birth format error");
                continue;
            }

            // 邮箱校验
            if (personItem.getEmail() != null) {
                if (personItem.getEmail() == "") {
                    persPerson.setEmail(personItem.getEmail());
                } else {
                    if (!PersRegularUtil.emailPattern.matcher(personItem.getEmail()).matches()) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                            .i18nCode("pers_import_emailErrors", personItem.getRowNum(), personItem.getEmail())));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": Email address " + personItem.getEmail()
                            + " format error");
                        continue;
                    } else {
                        // 如果邮箱跟其他人重复了，不允许导入数据
                        if (Objects.nonNull(emailAndPinMap.put(personItem.getEmail(), personItem.getPin()))) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                                .i18nCode("pers_import_emailIsRepeat", personItem.getRowNum(), personItem.getEmail())));
                            itemIterator.remove();
                            logger.info("Row " + personItem.getRowNum() + ": The internal email address of the file "
                                + personItem.getEmail() + " has been repeated");
                            continue;
                        }

                        if (!personItem.getEmail().equals(persPerson.getEmail())
                            && existsEmails.contains(personItem.getEmail())) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_emailIsExist", personItem.getEmail()))));
                            itemIterator.remove();
                            logger.info("Row " + personItem.getRowNum() + ": Email address " + personItem.getEmail()
                                + " already exists");
                            continue;
                        }

                        persPerson.setEmail(personItem.getEmail());
                    }
                }
            }

            if (personItem.getMobilePhone() != null) {
                if (personItem.getMobilePhone() != "") {
                    if (!Pattern.matches("\\d+", personItem.getMobilePhone())) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_mobilePhoneErrors"))));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": MobilePhone " + personItem.getMobilePhone()
                            + " format error");
                        continue;
                    }
                    if ("zh_CN".equals(LocaleMessageSourceUtil.language)
                        && !PersRegularUtil.isValidMobilePhone(personItem.getMobilePhone())) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_mobilePhoneErrors"))));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": MobilePhone " + personItem.getMobilePhone()
                            + " format error");
                        continue;
                    }
                    // 如果手机号码跟其他人重复了，不允许导入数据
                    if (Objects.nonNull(mobilePhoneAndPinMap.put(personItem.getMobilePhone(), personItem.getPin()))) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fileMobilePhoneRepeat", personItem.getRowNum(),
                                personItem.getMobilePhone())));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": The internal mobilePhone of the file "
                            + personItem.getMobilePhone() + " has been repeated");
                        continue;
                    }

                    if (!personItem.getMobilePhone().equals(persPerson.getMobilePhone())
                        && existsMobilePhones.contains(personItem.getMobilePhone())) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_h5_personMobileRepeat"))));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": mobilePhone " + personItem.getMobilePhone()
                            + " already exists");
                        continue;
                    }
                }
                persPerson.setMobilePhone(personItem.getMobilePhone());
            }
            // 入职日期校验
            if (personItem.getHireDate() != null) {
                if (personItem.getHireDate().compareTo(date) > 0) {
                    progressCache.setProcess(
                        ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                            personItem.getRowNum(), I18nUtil.i18nCode("pers_import_hireDateLaterCurrent"))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": Date of Hire format error");
                    continue;
                }
                persPerson.setHireDate(personItem.getHireDate());
            } else if (PersConstants.PERSON_DATE_SETNULL.equals(personItem.getHireDateStr())) {
                // 导入文件有入职日期该列但没有数据，值更新为null
                persPerson.setHireDate(null);
            } else if (PersConstants.PERSON_DATE_ERROR.equals(personItem.getHireDateStr())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_import_hireDateError"))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": Date of Hire format error");
                continue;
            }

            // 导入的数据，默认以部门编码为优先
            if (StringUtils.isNotBlank(personItem.getDeptCode())
                && Objects.nonNull(deptCodeAndIdMap.get(personItem.getDeptCode()))) {
                persPerson.setDeptId(deptCodeAndIdMap.get(personItem.getDeptCode()));
            } else {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                    "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_import_deptNotExist"))));
                itemIterator.remove();
                logger.info("Row " + personItem.getRowNum() + ": The department does not exist");
                continue;
            }

            if (personItem.getPositionCode() != null) {
                if (personItem.getPositionCode() == "") {
                    // 导入文件有职位该列但没有数据，值更新为null
                    persPerson.setPersPosition(null);
                } else {
                    // 导入的数据，默认使用职位编码
                    if (Objects.nonNull(codeAndPositionMap.get(personItem.getPositionCode()))) {
                        persPerson.setPersPosition(codeAndPositionMap.get(personItem.getPositionCode()));
                    } else {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_position_notExist"))));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": The position does not exist");
                        continue;
                    }
                }
            }

            // 证件处理
            String certNumber = null;
            if (StringUtils.isNotBlank(personItem.getCertNumber())) {
                certNumber = personItem.getCertNumber().trim();
                personItem.setCertNumber(certNumber);
            }
            String certType = certMap.get(personItem.getCertName());
            if (certNumber != null && personItem.getCertName() != null) {
                // 只有证件类型没有证件号
                if (certNumber == "" && StringUtils.isNotBlank(certType)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                            I18nUtil.i18nCode("pers_import_certNumNotNull", personItem.getPin()))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": Personnel id is: " + personItem.getPin()
                        + " certNumber cannot be empty");
                    continue;
                } else {
                    // 只有证件号没有证件类型
                    if (StringUtils.isNotBlank(certNumber) && StringUtils.isBlank(certType)) {
                        if (StringUtils.isNotBlank(personItem.getCertName())) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_certTypeNotExist", personItem.getPin()))));
                        } else {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_certTypeNotNull", personItem.getPin()))));
                        }
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum() + ": Personnel id is: " + personItem.getPin()
                            + " certType cannot be empty");
                        continue;
                    }
                    if (StringUtils.isNotBlank(certNumber) && StringUtils.isNotBlank(certType)) {
                        // 文件内部的证件类型、证件号重复
                        if (!(certNumbers.containsKey(certNumber) && certNumbers.get(certNumber).equals(certType))) {
                            // 证件号码超长
                            if (certNumber.length() > 30) {
                                progressCache.setProcess(
                                    ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                                        "pers_import_certNumberTooLong", personItem.getRowNum(), certNumber)));
                                itemIterator.remove();
                                logger.info(
                                    "Row " + personItem.getRowNum() + ": certNumber " + certNumber + " is too long");
                                continue;
                            }
                            // 二代身份证校验
                            if ("2".equals(certType) && !PersRegularUtil.idNumberCheck(certNumber)) {
                                progressCache
                                    .setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil
                                        .i18nCode("pers_import_idNumberErrors", personItem.getRowNum(), certNumber)));
                                itemIterator.remove();
                                logger.info(
                                    "Row " + personItem.getRowNum() + ": certNumber " + certNumber + " is malformed");
                                continue;
                            }

                            // 与数据库的证件号重复
                            PersCertificateItem certificateItem = persCertificateService
                                .getItemByTypeAndNumberAndPersonPinNe(certType, certNumber, personItem.getPin());
                            if (certificateItem != null) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_certNumberExist", certNumber))));
                                itemIterator.remove();
                                logger.info(
                                    "Row " + personItem.getRowNum() + ": certNumber " + certNumber + " already Exists");
                                continue;
                            }

                            certNumbers.put(certNumber, certType);
                            PersCertificateItem persCertificateItem = new PersCertificateItem();
                            persCertificateItem.setCertType(certType);
                            persCertificateItem.setCertNumber(certNumber);
                            // 修改导入人员身份证 人员信息idCard未赋值
                            if (PersConstants.CERT_SECOND_ID.equals(certType)) {
                                persPerson.setIdCard(certNumber);
                            } else {
                                persPerson.setIdCard("");
                            }
                            pinAndCertMap.put(personItem.getPin(), persCertificateItem);
                        } else {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_certNumberExist", certNumber))));
                            itemIterator.remove();
                            logger.info(
                                "Row " + personItem.getRowNum() + ": certNumber " + certNumber + " already Exists");
                            continue;
                        }
                    }
                }
            }

            // 卡号处理。
            // 导入未传卡号，不进行处理，传了卡号，会更新卡号信息
            if (StringUtils.isNotBlank(personItem.getCardNos())) {
                String[] cardNoAry = personItem.getCardNos().toLowerCase().split(PersConstants.IMPORT_CARD_SPLIT);
                if ("false".equals(cardsSupport) && cardNoAry.length > 1) {
                    // 当前系统不支持多卡
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                        I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                            I18nUtil.i18nCode("pers_import_cardsNotSupport", personItem.getPin()))));
                    itemIterator.remove();
                    logger.info(
                        "Row " + personItem.getRowNum() + ": The feature [Multiple Cards per Person] is disabled");
                    continue;
                }
                List<String> cardNoList = new ArrayList<>();
                for (String cardNo : cardNoAry) {
                    // 过滤卡号为空的数据
                    if (StringUtils.isBlank(cardNo)) {
                        continue;
                    }

                    if (PersRegularUtil.zeroPattern.matcher(cardNo).matches()) {
                        // 不能以0开头
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_cardNoStartWithZero"))));
                        logger.info("Row " + personItem.getRowNum() + ": Card number start with zero");
                        break;
                    }

                    // 有pro模块时, 卡号当字符串处理
                    if (Objects.nonNull(pers2ProService)) {
                        // 检查字符串长度不超过50
                        if (cardNo.length() > 50) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_cardTooLong", cardNo))));
                            logger.info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " is too long");
                            break;
                        }
                        // 检查字符, 只能数字或字母
                        if (!PersRegularUtil.hasNumberOrLetter(cardNo)) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_cardNoFormatErrors", cardNo))));
                            logger.info("Row " + personItem.getRowNum() + ": The format of Card number " + cardNo
                                + " is incorrect");
                            break;
                        }
                    } else {
                        // 参数设置为十进制卡，不允许导入16进制卡号
                        if (PersConstants.CARD_DECIMAL.equals(cardHex)) {
                            // 过滤掉不是数字的数据
                            if (!StringUtils.isNumeric(cardNo)) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_cardNoFormatErrors", cardNo))));
                                logger.info("Row " + personItem.getRowNum() + ": The format of Card number " + cardNo
                                    + " is incorrect");
                                break;
                            }
                            // 卡号超出最大值的数据
                            if (cardNo.length() > 50) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_cardTooLong", cardNo))));
                                logger
                                    .info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " is too long");
                                break;
                            }
                        } else if (PersConstants.CARD_HEXADECIMAL.equals(cardHex)) {
                            // 过滤掉不匹配十六进制的数据
                            if (!PersRegularUtil.isHex(cardNo)) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_cardNoFormatErrors", cardNo))));
                                logger.info("Row " + personItem.getRowNum() + ": The format of Card number " + cardNo
                                    + " is incorrect");
                                break;
                            }
                            // 过滤卡号超出最大值的数据
                            if (cardNo.length() > 50) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_cardTooLong", cardNo))));
                                logger
                                    .info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " is too long");
                                break;
                            }
                        }
                    }

                    // 人员卡号数量超过17个，主卡1张，副卡16张
                    if (cardNoList.size() >= 17) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_card_notMoreThanSixteen"))));
                        logger.info("Row " + personItem.getRowNum()
                            + ": The quantity of the secondary cards cannot be greater than 16");
                        break;
                    }

                    // 如果卡号跟其他人重复了，不允许导入数据
                    if (Objects.nonNull(cardNoAndPinMap.put(cardNo, personItem.getPin()))) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_cardExist", cardNo))));
                        logger.info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " already Exists");
                        break;
                    }
                    // 检查卡号是否已经在数据库当中，并且是否跟其他人的卡号重复了。
                    if (!personItem.getPin().equals(existsCardNoAndPinMap.get(cardNo))
                        && existsCardNos.contains(cardNo)) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_cardExist", cardNo))));
                        logger.info("Row " + personItem.getRowNum() + ": Card number " + cardNo + " already exists");
                        break;
                    }
                    cardNoList.add(cardNo);
                }
                // 卡号数据有异常，这条人员数据就不导入
                if (cardNoAry.length != cardNoList.size()) {
                    itemIterator.remove();
                    continue;
                }

                if (!CollectionUtil.isEmpty(cardNoList)) {
                    String existsMasterCardNo = existsPinAndMasterCardNoMap.get(personItem.getPin());
                    if (existsPinAndMasterCardNoMap.containsKey(personItem.getPin())
                        && !cardNoList.get(0).equals(existsMasterCardNo) && Objects.nonNull(pos4PersCardService)
                        && pos4PersCardService.checkUseCardNo(existsMasterCardNo)) {
                        // 该人员的主卡正在被消费模块使用，请去消费模块执行退卡操作！
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_card_posUseCardNo"))));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum()
                            + ": The person's main card is being used by the consumer module.");
                        continue;
                    }
                    pinAndCardsMap.put(personItem.getPin(), cardNoList);
                }
            }
            try {
                if (personItem.getAttrMap() != null) {
                    Map<String, Object> attrMap = personItem.getAttrMap();
                    PersAttributeExtItem persAttributeExtItem = new PersAttributeExtItem();
                    // boolean attrExtExist = false;
                    for (String key : personItem.getAttrMap().keySet()) {
                        String[] paramArray = key.split("\\.");
                        if (paramArray.length > 1 && attrMap.get(key).toString() != null) {
                            BeanUtils.setProperty(persAttributeExtItem, paramArray[1], attrMap.get(key).toString());
                        }

                    }
                    pinAndAttrExtMap.put(personItem.getPin(), persAttributeExtItem);
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                e.printStackTrace();
            }
            // 车牌导入校验
            // 导入未传车牌，不进行处理
            if (park4PersPersonServiceExist && StringUtils.isNotBlank(personItem.getCarPlate())) {
                String[] carPlateArray = personItem.getCarPlate().split(PersConstants.IMPORT_CARD_SPLIT);
                if (carPlateArray.length > 6) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_import_personPlateMax"))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum()
                        + ": The number of personnel license plates exceeds the maximum of 6");
                    continue;
                } else {
                    boolean carPlateValid = true;
                    for (String carPlate : carPlateArray) {
                        carNumber = carPlate.replace(getAreaFormCarNumber(carPlate), "");
                        // 国内车牌长度不能超过8位数
                        int plateLen = 7;
                        if (!"zh_CN".equals(LocaleMessageSourceUtil.language)) {
                            plateLen = 20;
                            if (ValidateUtils.isSpecialChar(carNumber) || carNumber.contains(" ")
                                || carNumber.contains("\n")) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_personPlateFormat", personItem.getPin()))));
                                itemIterator.remove();
                                logger.info("Row " + personItem.getRowNum() + ": Personnel license plate " + carPlate
                                    + " format is incorrect");
                                carPlateValid = false;
                                break;
                            }
                        }
                        if (carNumber.length() > plateLen) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_personPlateFormat", carPlate))));
                            itemIterator.remove();
                            logger.info("Row " + personItem.getRowNum() + ": Personnel license plate " + carPlate
                                + " format is incorrect");
                            carPlateValid = false;
                            break;
                        }

                        // 海外的车牌不需要校验
                        if ("zh_CN".equals(LocaleMessageSourceUtil.language)) {
                            if (StringUtils.isBlank(getAreaFormCarNumber(carPlate))) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_personPlateFormat", carPlate))));
                                itemIterator.remove();
                                logger.info("Row " + personItem.getRowNum() + ": Personnel license plate " + carPlate
                                    + " format is incorrect");
                                carPlateValid = false;
                                break;
                            }

                            // 除省份外验证
                            Matcher m = PersRegularUtil.carnumberPattern
                                .matcher(carPlate.replace(getAreaFormCarNumber(carPlate), ""));
                            if (!m.matches()) {
                                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                    I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                        I18nUtil.i18nCode("pers_import_personPlateFormat", carPlate))));
                                itemIterator.remove();
                                logger.info("Row " + personItem.getRowNum() + ": Personnel license plate " + carPlate
                                    + " format is incorrect");
                                carPlateValid = false;
                                break;
                            }
                        }
                        // 如果车牌跟其他人重复了或者自身车牌重复，不允许导入数据
                        if (Objects.nonNull(carPlateAndPinMap.put(carPlate, personItem.getPin()))) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_filePlateRepeat", carPlate))));
                            itemIterator.remove();
                            logger.info("Row " + personItem.getRowNum() + ": File inside the license plate " + carPlate
                                + " duplicate");
                            carPlateValid = false;
                            break;
                        }
                        // 检查车牌是否已经在数据库当中，并且是否跟其他人的车牌重复了。
                        if (!personItem.getPin().equals(existsCarPlateAndPinMap.get(carPlate))
                            && existsCarPlates.contains(carPlate)) {
                            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                                I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                    I18nUtil.i18nCode("pers_import_personPlateRepeat", carPlate))));
                            itemIterator.remove();
                            logger.info("Row " + personItem.getRowNum() + ": Personnel license plate " + carPlate
                                + " duplicate");
                            carPlateValid = false;
                            break;
                        }
                    }
                    if (!carPlateValid) {
                        // 车牌异常不导入数据，删掉之前存在需要导入的卡号
                        if (pinAndCardsMap.get(personItem.getPin()) != null) {
                            pinAndCardsMap.remove(personItem.getPin());
                        }
                        continue;
                    }
                    pinAndCarPlatesMap.put(personItem.getPin(), Arrays.asList(carPlateArray));
                }
            }
            if (vdb4PersPersonServiceExist) {
                String buildingName = personItem.getVdbBuildingName();
                String unitName = personItem.getVdbUnitName();
                // 判断人员的楼栋单元是否存在
                if (StringUtils.isNotBlank(buildingName) && !existVdbBuildingItemMap.containsKey(buildingName)) {
                    progressCache.setProcess(
                        ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                            personItem.getRowNum(), I18nUtil.i18nCode("pers_import_buildingNotExist"))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": The building does not exist");
                    continue;
                }
                if (StringUtils.isNotBlank(unitName) && !existVdbUnitItemMap.containsKey(unitName)) {
                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode(
                        "pers_import_fail", personItem.getRowNum(), I18nUtil.i18nCode("pers_import_unitNotExist"))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": The unit does not exist");
                    continue;
                }

                if (StringUtils.isNotBlank(unitName) && StringUtils.isBlank(buildingName)) {
                    progressCache.setProcess(
                        ProcessBeanUtil.createErrorSingleProcess(beginProgress, I18nUtil.i18nCode("pers_import_fail",
                            personItem.getRowNum(), I18nUtil.i18nCode("pers_import_vdbBuildingFail", unitName))));
                    itemIterator.remove();
                    logger.info("Row " + personItem.getRowNum() + ": The building cannot be empty");
                    continue;
                }

                if (StringUtils.isNotBlank(unitName) && existVdbUnitItemMap.containsKey(unitName)) {
                    String relationBuildAndUnit = buildingName + "_" + unitName;
                    VdbUnit2otherItem vdbUnit2otherItem = existVdbUnitItemMap.get(unitName);
                    String vdbBuildingUnit =
                        vdbUnit2otherItem.getBuildingName() + "_" + vdbUnit2otherItem.getUnitName();
                    if (!vdbBuildingUnit.equals(relationBuildAndUnit)) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_vdbInfoFail", buildingName, unitName))));
                        itemIterator.remove();
                        logger.info(
                            "Row " + personItem.getRowNum() + ": This unit information does not exist in the building");
                        continue;
                    }
                }

                if (StringUtils.isNotBlank(personItem.getRoomNo())) {
                    // 0,9899
                    if (!StringUtils.isNumeric(personItem.getRoomNo())
                        || Integer.parseInt(personItem.getRoomNo()) <= 0) {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                            I18nUtil.i18nCode("pers_import_fail", personItem.getRowNum(),
                                I18nUtil.i18nCode("pers_import_vdbRoomNoFail", unitName))));
                        itemIterator.remove();
                        logger.info("Row " + personItem.getRowNum()
                            + ": The Room number must be a numeric value between 0 and 9899!");
                        continue;
                    }
                }
                // 人员住宅信息为空，则不保存到住宅人员
                if (!(StringUtils.isBlank(buildingName) && StringUtils.isBlank(unitName)
                    && StringUtils.isBlank(personItem.getRoomNo()))) {
                    Vdb4PersPersonItem vdb4PersPersonItem = new Vdb4PersPersonItem();
                    if (StringUtils.isNotBlank(buildingName)) {
                        vdb4PersPersonItem.setBuildingId(existVdbBuildingItemMap.get(buildingName).getBuildingId());
                        vdb4PersPersonItem.setBuildingName(buildingName);
                    }
                    if (StringUtils.isNotBlank(unitName)) {
                        vdb4PersPersonItem.setUnitId(existVdbUnitItemMap.get(unitName).getUnitId());
                        vdb4PersPersonItem.setUnitName(unitName);
                    }
                    vdb4PersPersonItem.setRoomNo(personItem.getRoomNo());
                    // 更新的数据
                    if (existsPersPinAndVdbInfoMap.containsKey(personItem.getPin())) {
                        Vdb4PersPersonItem temp = existsPersPinAndVdbInfoMap.get(personItem.getPin());
                        vdb4PersPersonItem.setPersonId(temp.getPersonId());
                        vdb4PersPersonItem.setId(temp.getId());
                    }
                    // 更新的
                    if (StringUtils.isNotBlank(vdb4PersPersonItem.getId())) {
                        existsPersPinAndVdbInfoMap.put(personItem.getPin(), vdb4PersPersonItem);
                    } else {
                        persPinAndVdbInfoMap.put(personItem.getPin(), vdb4PersPersonItem);
                    }
                }
            }
            persPerson
                .setNameSpell(personItem.getName() == null ? "" : PinyinUtil.converterToSpell(personItem.getName()));
            if (personItem.getGender() != null) {
                persPerson.setGender(converterToGender(personItem.getGender(), genderMap));
            }
            if (persPerson.getPersonType() == null) {
                persPerson.setPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
            }
            if (persPerson.getStatus() == null) {
                persPerson.setStatus(PersConstants.PERSON_NORMAL);
            }
            if (persPerson.getExceptionFlag() == null) {
                persPerson.setExceptionFlag(PersConstants.PERSON_ISNOTEXCEPTION);
            }
            if (persPerson.getIsSendMail() == null) {
                persPerson.setIsSendMail(false);
            }
            if (persPerson.getPinLetter() == null) {
                persPerson.setPinLetter(!StringUtils.isNumeric(personItem.getPin()));
            }
            if (persPerson.getEnabledCredential() == null) {
                persPerson.setEnabledCredential(true);
            }
            if (persPerson.getSendApp() == null) {
                persPerson.setSendApp(true);
            }
            pinAndPersPersonMap.put(persPerson.getPin(), persPerson);
            if (existPersPersonMap.get(personItem.getPin()) != null) {
                existCount++;
            }
        }

        // 失败数量
        int faildCount = importSize - itemList.size() - notUpdateSize;
        // 人员pin号和Park4PersPersonItem的对应关系集合
        Map<String, Park4PersPersonItem> pinAndPark4PersPersonItemMap = new HashMap<>(itemList.size());
        Map<String, Vdb4PersPersonItem> vdb4PersPersonItemMap = new HashMap<>(itemList.size());
        if (!CollectionUtil.isEmpty(pinAndPersPersonMap)) {
            // 查询人员数量
            Integer personSize = persPersonDao.countByPersonType(PersConstants.PERSON_TYPE_EMPLOYEE);
            // 加导入的人员数量
            personSize = personSize + pinAndPersPersonMap.size() - existCount;
            // 许可数量控制
            ResultCode resultCode = baseLicenseProvider.isCountOutRangePers(personSize);
            if (!ResultCode.SUCCESS.equals(resultCode)) {
                throw ZKBusinessException.warnException("pers_import_exceedLicense");
            }

            // 把导入的数据分割，分批处理，一次处理800人
            List<List<PersPerson>> personList =
                CollectionUtil.split(pinAndPersPersonMap.values(), CollectionUtil.splitSize);
            float avgProgress = (float)60 / personImportItemsList.size();
            int i = 0;
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                I18nUtil.i18nCode("common_op_processing")));

            // 人员pin号和id的对应关系集合
            Map<String, String> pinAndIdMap = new HashMap<>(itemList.size());

            for (List<PersPerson> persList : personList) {
                int progress = beginProgress + 10 + (int)(avgProgress * i++);
                progressCache.setProcess(new ProcessBean(progress, progress, ""));
                persPersonDao.save(persList);

                // 保存人员pin和id的对应关系
                persList.forEach(person -> {
                    pinAndIdMap.put(person.getPin(), person.getId());
                    PersAttributeExtItem persAttributeExtItem = pinAndAttrExtMap.get(person.getPin());
                    if (persAttributeExtItem != null) {
                        persAttributeExtItem.setPersonId(person.getId());
                        persAttributeExtItemList.add(persAttributeExtItem);
                    }

                    PersCertificateItem persCertificateItem = pinAndCertMap.get(person.getPin());
                    if (persCertificateItem != null) {
                        persCertificateItem.setPersonId(person.getId());
                        persCertificateItemList.add(persCertificateItem);
                    }
                    if (pinAndCarPlatesMap.containsKey(person.getPin())) {
                        Park4PersPersonItem park4PersPersonItem = new Park4PersPersonItem();
                        park4PersPersonItem.setPersonId(person.getId());
                        park4PersPersonItem.setPersonPin(person.getPin());
                        park4PersPersonItem.setPersonName(person.getName());
                        park4PersPersonItem.setPersonLastName(person.getLastName());
                        park4PersPersonItem.setPersonMobilePhone(person.getMobilePhone());
                        park4PersPersonItem.setDeptId(person.getDeptId());
                        park4PersPersonItem.setDeptName(deptIdAndNameMap.get(person.getDeptId()));
                        pinAndPark4PersPersonItemMap.put(person.getPin(), park4PersPersonItem);
                    }
                    // 更新人员名单库信息
                    if (existListPersonMap.containsKey(person.getPin())) {
                        List<PersPersonnallistPerson> listPersonList = existListPersonMap.get(person.getPin());
                        boolean updateListPerson = updateListPerson(listPersonList.get(0), person);
                        if (updateListPerson) {
                            for (PersPersonnallistPerson listPerson : listPersonList) {
                                PersPersonnallistPersonItem persPersonnallistPersonItem =
                                    new PersPersonnallistPersonItem();
                                persPersonnallistPersonItem.setId(listPerson.getId());
                                persPersonnallistPersonItem.setPersonnallistId(listPerson.getPersonnallistId());
                                persPersonnallistPersonItem = buildPersonInfo(persPersonnallistPersonItem, person);
                                updateListPersonList.add(persPersonnallistPersonItem);
                            }
                        }
                    }
                    if (persPinAndVdbInfoMap.containsKey(person.getPin())) {
                        Vdb4PersPersonItem vdb4PersPersonItem = persPinAndVdbInfoMap.get(person.getPin());
                        vdb4PersPersonItem.setPersonId(person.getId());
                        vdb4PersPersonItemMap.put(person.getId(), vdb4PersPersonItem);
                    }
                });
            }

            // 处理人员卡号
            if (!CollectionUtil.isEmpty(pinAndCardsMap)) {
                persCardService.batchSaveCards(pinAndCardsMap, pinAndIdMap);
            }

            // 保存证件
            if (!CollectionUtil.isEmpty(pinAndCertMap)) {
                persCertificateService.batchSaveCertificates(persCertificateItemList);
            }

            // 保存扩展属性
            if (!CollectionUtil.isEmpty(persAttributeExtItemList)) {
                persAttributeExtService.batchSaveAttributeExts(persAttributeExtItemList);
            }

            // 保存车牌
            if (park4PersPersonServiceExist && !CollectionUtil.isEmpty(pinAndCarPlatesMap)
                && !CollectionUtil.isEmpty(pinAndPark4PersPersonItemMap)) {
                park4PersPersonService.importPersonData(pinAndCarPlatesMap, pinAndPark4PersPersonItemMap);
            }
            // 更新名单库数据
            if (!CollectionUtil.isEmpty(updateListPersonList)) {
                persPersonnallistPersonService.batchSave(updateListPersonList);
            }
            // 保存人员住宅信息
            if (vdb4PersPersonServiceExist) {
                vdb4PersPersonService.importPersonData(vdb4PersPersonItemMap, existsPersPinAndVdbInfoMap);
            }
        }
        if (existCount > 0 && updateExistData) {
            // 成功：%s 条，更新：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result3", pinAndPersPersonMap.size(), existCount, faildCount)));
        } else {
            // 成功：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result", pinAndPersPersonMap.size() + notUpdateSize, faildCount)));
        }
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public void enabledCredential(String personIds) {
        if (StringUtils.isNotBlank(personIds)) {
            saveEnabledCredential(personIds, true);
            if (persPersonExtServices != null) {
                Arrays.stream(persPersonExtServices).forEach(ps -> ps.enabledCredential(personIds));
            }
        }
    }

    @Override
    public void disableCredential(String personIds) {
        if (StringUtils.isNotBlank(personIds)) {
            saveEnabledCredential(personIds, false);
            if (persPersonExtServices != null) {
                Arrays.stream(persPersonExtServices).forEach(ps -> ps.disableCredential(personIds));
            }
        }
    }

    private void saveEnabledCredential(String personIds, boolean enabledCredential) {
        if (StringUtils.isNotBlank(personIds)) {
            List<String> personIdList = (List<String>)CollectionUtil.strToList(personIds);
            List<PersPerson> persPersonList = persPersonDao.findByIdIn(personIdList);
            for (PersPerson persPerson : persPersonList) {
                persPerson.setEnabledCredential(enabledCredential);
                if (!enabledCredential) {
                    persPerson.setAppAuthorization(false);
                    // 禁用人员需删除客户端，并删除对应token
                    baseRegisterService.delByClientNameAndClientType(persPerson.getPin(), BaseAppConstants.APP_PERS);
                }
            }
            persPersonDao.save(persPersonList);
            // 更改卡状态
            Short cardState = PersConstants.CARD_VALID;
            if (!enabledCredential) {
                cardState = PersConstants.CARD_LOSS;
            }
            persCardDao.updateCardState(cardState, personIdList);
        }
    }

    @Override
    public List<String> getPersonIdByDisableCredential(String sessionId, PersPersonItem condition) {
        if (Objects.isNull(condition)) {
            condition = new PersPersonItem();
        }
        condition.setEnabledCredential(false);
        // 权限过滤
        buildCondition(sessionId, condition);
        // 查询正常人员 pin name
        List<PersPersonItem> personItemList =
            (List<PersPersonItem>)persPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
        // 获取人员id
        List<String> persPersonIdList = personItemList.stream().map(PersPersonItem::getId)
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        return persPersonIdList;
    }

    @Override
    public Boolean isProtectData() {
        String val = baseSysParamService.getValByName("system.data.protection");
        return "1".equals(val) ? true : false;
    }

    @Override
    public List<PersPersonItem> protectPinAndCard(List<PersPersonItem> items) {
        if (isProtectData()) {
            items.forEach(item -> {
                String pin = item.getPin();
                item.setPin(PersRegularUtil.hideWithAsterisk(pin));
                String cardNos = item.getCardNos();
                if (StringUtils.isNotBlank(cardNos)) {
                    String[] cardNoList = cardNos.split(PersConstants.IMPORT_CARD_SPLIT);
                    for (int i = 0; i < cardNoList.length; i++) {
                        cardNoList[i] = PersRegularUtil.hideWithAsterisk(cardNoList[i]);
                    }
                    item.setCardNos(StringUtils.join(cardNoList, PersConstants.IMPORT_CARD_SPLIT));
                }
            });
        }
        return items;
    }

    @Override
    public Boolean checkShowWhatsapp() {
        return pers2OtherService.checkShowWhatsapp();
    }

    @Override
    public boolean checkWhatsappParam() {
        return pers2OtherService.completeWhatsappModemInfo();
    }

    /**
     * 是否更新名单库人员信息
     *
     * @param listPerson:
     * @param person:
     * @return boolean
     * @throws
     * <AUTHOR>
     * @date 2022-09-15 10:12
     * @since 1.0.0
     */
    private boolean updateListPerson(PersPersonnallistPerson listPerson, PersPerson person) {
        boolean updateListPerson = false;
        String name = listPerson.getPersonName();
        String gender = listPerson.getPersonGender();
        Date birthday = listPerson.getPersonBirthday();
        String idCard = listPerson.getIdCard();
        String mobilePhone = listPerson.getMobilePhone();
        String email = listPerson.getEmail();
        String positionName = listPerson.getPositionName();
        PersPosition position = person.getPersPosition();
        if ((StringUtils.isNotBlank(name) && !name.equals(person.getName()))
            || (StringUtils.isBlank(name) && StringUtils.isNotBlank(person.getName()))) {
            updateListPerson = true;
        } else if ((StringUtils.isNotBlank(gender) && !gender.equals(person.getGender()))
            || (StringUtils.isBlank(gender) && StringUtils.isNotBlank(person.getGender()))) {
            updateListPerson = true;
        } else if (!((Objects.nonNull(birthday) && Objects.nonNull(person.getBirthday())
            && birthday.getTime() == person.getBirthday().getTime())
            || (Objects.isNull(person.getBirthday()) && Objects.isNull(birthday)))) {
            updateListPerson = true;
        } else if ((StringUtils.isNotBlank(idCard) && !idCard.equals(person.getIdCard()))
            || (StringUtils.isBlank(idCard) && StringUtils.isNotBlank(person.getIdCard()))) {
            updateListPerson = true;
        } else if ((StringUtils.isNotBlank(mobilePhone) && !mobilePhone.equals(person.getMobilePhone()))
            || (StringUtils.isBlank(mobilePhone) && StringUtils.isNotBlank(person.getMobilePhone()))) {
            updateListPerson = true;
        } else if ((StringUtils.isNotBlank(email) && !email.equals(person.getEmail()))
            || (StringUtils.isBlank(email) && StringUtils.isNotBlank(person.getEmail()))) {
            updateListPerson = true;
        } else if ((Objects.nonNull(position) && !position.getName().equals(positionName))
            || (Objects.isNull(position) && StringUtils.isNotBlank(positionName))) {
            updateListPerson = true;
        }
        return updateListPerson;
    }

    /**
     * 构建名单库人员信息
     *
     * @param persPersonnallistPersonItem:
     * @param persPerson:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonnallistPersonItem
     * @throws
     * <AUTHOR>
     * @date 2022-09-15 9:31
     * @since 1.0.0
     */
    private PersPersonnallistPersonItem buildPersonInfo(PersPersonnallistPersonItem persPersonnallistPersonItem,
        PersPerson persPerson) {
        persPersonnallistPersonItem.setPersonId(persPerson.getId());
        persPersonnallistPersonItem.setLinkTbl(PersConstants.PERS_PERSON);
        persPersonnallistPersonItem.setPersonName(persPerson.getName());
        persPersonnallistPersonItem.setPersonPin(persPerson.getPin());
        persPersonnallistPersonItem.setPersonGender(persPerson.getGender());
        persPersonnallistPersonItem.setPersonBirthday(persPerson.getBirthday());
        persPersonnallistPersonItem.setIdCard(persPerson.getIdCard());
        if (Objects.nonNull(persPerson.getPersPosition())) {
            persPersonnallistPersonItem.setPositionName(persPerson.getPersPosition().getName());
        }
        persPersonnallistPersonItem.setMobilePhone(persPerson.getMobilePhone());
        persPersonnallistPersonItem.setEmail(persPerson.getEmail());
        return persPersonnallistPersonItem;
    }

    @Override
    public void updateEnabledCredential() {
        persPersonDao.updateEnabledCredential(true);
    }

    /**
     * 验证当前用户登陆密码
     *
     * @param loginPwd
     * @return
     * @author: train.chen
     * @date: 2018年5月30日 下午3:25:37
     */
    @Override
    public Boolean verifyLoginPwd(String sessionId, String loginPwd) {
        boolean ret = false;
        if (StringUtils.isNotBlank(sessionId) && StringUtils.isNotBlank(loginPwd)) {
            ret = authUserService.verifySecurity(sessionId, loginPwd, "pwd");
        }
        return ret;
    }

    @Override
    public List<String> getPersonIdsByPins(List<String> pinList) {
        List<String> personIdList = new ArrayList<>();
        if (!pinList.isEmpty()) {
            List<PersPersonCacheItem> personCacheItemList = persPersonCacheService.getPersonCacheByPins(pinList);
            personIdList = personCacheItemList.stream().map(PersPersonCacheItem::getId).collect(Collectors.toList());
        }
        return personIdList;
    }

    @Override
    public void enabledApplogin(String personIds) {
        if (StringUtils.isNotBlank(personIds)) {
            Collection<String> personIdList = CollectionUtil.strToList(personIds);
            Integer count = persPersonDao.countByIdInAndEnabledCredential((List<String>)personIdList, false);
            if (count > 0) {
                throw ZKBusinessException.warnException("pers_person_selectDisabledNotOp");
            }
            saveEnabledApplogin(personIds, true);
        }
    }

    @Override
    public void disableApplogin(String personIds) {
        if (StringUtils.isNotBlank(personIds)) {
            saveEnabledApplogin(personIds, false);
        }
    }

    private void saveEnabledApplogin(String personIds, boolean enabledApplogin) {
        if (StringUtils.isNotBlank(personIds)) {
            List<String> personIdList = (List<String>)CollectionUtil.strToList(personIds);
            List<PersPerson> persPersonList = persPersonDao.findByIdIn(personIdList);
            for (PersPerson persPerson : persPersonList) {
                persPerson.setAppAuthorization(enabledApplogin);
                if (!enabledApplogin) {
                    // 禁用人员app登录需删除客户端，并删除对应token
                    baseRegisterService.delByClientNameAndClientType(persPerson.getPin(), BaseAppConstants.APP_PERS);
                }
            }
            persPersonDao.save(persPersonList);
        }
    }

    @Override
    public ZKResultMsg obtainAcmsCard(String status, String pageSize) {
        return persAcmsService.obtainAcmsCard(status, pageSize);
    }

    @Override
    public ZKResultMsg batchFaceTemplateByIds(String ids) {
        int beginProgress = 10;
        // 检测服务器是否在线
        ZKResultMsg statusMsg = persTemplateServerService.testTemplateServerStatus();
        if ("ok".equals(statusMsg.getRet())) {
            if (StringUtils.isNotBlank(ids)) {
                List<String> idList = StrUtil.strToList(ids);
                List<PersPerson> persPersonList = persPersonDao.findByIdIn(idList);
                List<PersBioPhotoItem> persBioPhotoItemList = persBioPhotoService.findByPersonIdIn(idList);
                Map<String, PersBioPhotoItem> persBioPhotoItemMap =
                    CollectionUtil.listToKeyMap(persBioPhotoItemList, PersBioPhotoItem::getPersonId);
                progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                    I18nUtil.i18nCode("common_op_processing")));
                List<PersBioTemplateItem> persBioTemplateItemList = new ArrayList<>();
                float avgProgress = (float)60 / persPersonList.size();
                int i = 0;
                for (PersPerson persPerson : persPersonList) {
                    int progress = beginProgress + 10 + (int)(avgProgress * i++);
                    progressCache.setProcess(new ProcessBean(progress, progress, ""));
                    if (persBioPhotoItemMap.containsKey(persPerson.getId())) {
                        PersBioPhotoItem bioPhotoItem = persBioPhotoItemMap.get(persPerson.getId());

                        String cropPhotoBase64 = FileEncryptUtil.getDecryptFileBase64(bioPhotoItem.getPhotoPath());
                        String photoPath = "";
                        if (StringUtils.isNotBlank(cropPhotoBase64)) {
                            // 生成临时图片
                            String filePath = FileUtil.createUploadFileRootPath("pers", "user/avatar");
                            String photoName = System.currentTimeMillis() + ".jpg";
                            FileUtil.saveFile(filePath, photoName, cropPhotoBase64, false);
                            photoPath = '/' + filePath + photoName;
                            if (StringUtils.isNotBlank(photoPath)) {
                                ZKResultMsg faceTemplate = persTemplateServerService.getFaceTemplateByPhoto(photoPath);
                                // 保存模板
                                if (faceTemplate != null && "0".equals(faceTemplate.getRet())) {
                                    Map<String, String> faceTemplateMap = (Map<String, String>)faceTemplate.getData();
                                    if (faceTemplateMap != null) {
                                        String template = faceTemplateMap.get("template");
                                        String version = faceTemplateMap.get("version");
                                        if (StringUtils.isNotBlank(template) && StringUtils.isNotBlank(version)) {
                                            PersBioTemplateItem persBioTemplateItem = new PersBioTemplateItem();
                                            persBioTemplateItem.setPersonId(bioPhotoItem.getPersonId());
                                            persBioTemplateItem.setPersonPin(bioPhotoItem.getPersonPin());
                                            persBioTemplateItem.setTemplate(template);
                                            persBioTemplateItem.setVersion(version);
                                            persBioTemplateItem.setBioType(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE);
                                            persBioTemplateItem.setTemplateNo((short)0);
                                            persBioTemplateItem.setTemplateNoIndex((short)0);
                                            persBioTemplateItemList.add(persBioTemplateItem);
                                        }
                                    }
                                } else {
                                    String msg = PersConstants.TEMPLATE_SERVER_ERROR_MAP.get(faceTemplate.getRet());
                                    if (StringUtils.isNotBlank(msg)) {
                                        msg = I18nUtil.i18nCode(msg);
                                    } else {
                                        msg = I18nUtil.i18nCode("pers_person_faceTemplateError6");
                                    }
                                    progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(progress,
                                        I18nUtil.i18nCode("pers_person_pin") + persPerson.getPin()
                                            + I18nUtil.i18nCode("pers_person_faceTemplateError6") + ","
                                            + I18nUtil.i18nCode("auth_license_reason") + msg));
                                }
                            }
                            File tempFile = new File(FileUtil.getLocalFullPath(photoPath));
                            if (tempFile.exists()) {
                                // 删除临时图片
                                tempFile.delete();
                            }
                        }

                    } else {
                        progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(progress,
                            I18nUtil.i18nCode("pers_person_pin") + persPerson.getPin()
                                + I18nUtil.i18nCode("pers_person_faceTemplateError6") + ","
                                + I18nUtil.i18nCode("auth_license_reason")
                                + I18nUtil.i18nCode("pers_person_cropFaceNoExist")));
                    }
                }
                if (!persBioTemplateItemList.isEmpty()) {
                    persBioTemplateService.updateBioTemplate(persBioTemplateItemList);
                }
                // 成功：%s 条，失败：%s 条。
                int faildCount = persPersonList.size() - persBioTemplateItemList.size();
                progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                    I18nUtil.i18nCode("pers_import_result", persBioTemplateItemList.size(), faildCount)));
            }

        } else {
            progressCache
                .setProcess(ProcessBeanUtil.createErrorSingleProcess(99, I18nUtil.i18nCode(statusMsg.getMsg())));
        }

        return ZKResultMsg.successMsg();
    }

    @Override
    public String getWholeNameByPin(String pin) {
        PersPersonCacheItem personCacheItem = persPersonCacheService.getPersonCacheFilterLeaveByPin(pin);
        if (personCacheItem != null) {
            return buildPersonWholeName(personCacheItem.getName(), personCacheItem.getLastName());
        }
        return StringUtils.EMPTY;
    }

    @Override
    public String getWholeNameByPersonId(String personId) {
        PersPersonItem personItem = getSimpleItemById(personId);
        if (personItem != null) {
            return buildPersonWholeName(personItem.getName(), personItem.getLastName());
        }
        return StringUtils.EMPTY;
    }

    private String buildPersonWholeName(String name, String lastName) {
        String wholeName = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(name)) {
            wholeName = name;
        }
        if (!LocaleMessageSourceUtil.language.equals("zh_CN") && StringUtils.isNotBlank(lastName)) {
            // 中文下不显示lastName
            wholeName = name + " " + lastName;
        }
        return wholeName;
    }

    @Override
    public String getPersCardQrCodeByCardNo(String cardNo) {
        if (cardNo != null) {
            StringBuffer content = new StringBuffer();
            content.append(cardNo);
            return String.valueOf(content);
        }
        return null;
    }

    @Override
    public PersPersonItem updateAvatar(PersPersonItem item) {
        PersPerson persPerson = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> persPersonDao.findById(id)).orElse(null);
        if (Objects.nonNull(persPerson) && StringUtils.isNotBlank(item.getPhotoBase64())) {
            String photoPath =
                FileUtil.saveFileToServer("pers", "user/avatar", persPerson.getPin() + ".jpg", item.getPhotoBase64());
            PersPersonUtil.encryptPhoto(photoPath, null);
            persPerson.setPhotoPath(photoPath);
            item.setPhotoPath(photoPath);
        }
        return item;
    }

    @Override
    public ZKResultMsg syncPersonAcmsByIds(String ids) {
        int beginProgress = 10;
        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("acms.sync.persInfo");
        if (baseSysParamItem != null && "1".equals(baseSysParamItem.getParamValue())) {
            if (StringUtils.isNotBlank(ids)) {
                List<PersPersonItem> persPersonItemList = getSimpleItemsByIds(StrUtil.strToList(ids));
                progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                    I18nUtil.i18nCode("pers_person_startUpdate")));
                float avgProgress = (float)60 / persPersonItemList.size();
                int i = 0;
                int faildCount = 0;
                for (PersPersonItem persPersonItem : persPersonItemList) {
                    int progress = beginProgress + 10 + (int)(avgProgress * i++);
                    progressCache.setProcess(new ProcessBean(progress, progress, ""));
                    ZKResultMsg zkResultMsg = persAcmsService.updateAcmsPersonByPin(persPersonItem);
                    if (!"ok".equals(zkResultMsg.getRet())) {
                        faildCount++;
                        progressCache.setProcess(
                            ProcessBeanUtil.createErrorSingleProcess(progress, I18nUtil.i18nCode("pers_person_pin")
                                + persPersonItem.getPin() + I18nUtil.i18nCode("pers_person_updateFailed")));
                    }
                }
                // 成功：%s 条，失败：%s 条。
                int successCount = persPersonItemList.size() - faildCount;
                progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                    I18nUtil.i18nCode("pers_import_result", successCount, faildCount)));

            }
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public void updateAcmsPersonByUpdateTime(Date updateTime) {
        List<PersPerson> persPersonList = persPersonDao.findByUpdateTimeAndCardFrom(updateTime, PersConstants.ACMS);
        if (!persPersonList.isEmpty()) {
            List<PersPersonItem> persPersonItemList =
                ModelUtil.copyListProperties(persPersonList, PersPersonItem.class);
            persPersonItemList.forEach(persPersonItem -> {
                persAcmsService.updateAcmsPersonByPin(persPersonItem);
            });
        }
    }

    @Override
    public List<PersPersonItem> findByNameLikeOrMobilePhone(String nameLike, String mobilePhone) {
        List<PersPersonItem> persPersonItemList = new ArrayList<>();
        List<PersPerson> persPersonList = persPersonDao.findByNameLikeOrMobilePhone(nameLike, mobilePhone);
        if (persPersonList != null && !persPersonList.isEmpty()) {
            persPersonItemList = ModelUtil.copyListProperties(persPersonList, PersPersonItem.class);
        }
        return persPersonItemList;
    }

    @Override
    public boolean checkSelfPwd(PersPersonItem personItem, String selfPwd) {

       if("123456".equals(selfPwd)){
            selfPwd=PersConstants.PERSON_SELFPWD;
       }

        return personItem.getSelfPwd().equals(selfPwd);
    }

    public void updatePersonByWechatOpenId(String pin ,String openid){

        persPersonDao.updatePersonByWechatOpenId(pin,openid);
    }
}
