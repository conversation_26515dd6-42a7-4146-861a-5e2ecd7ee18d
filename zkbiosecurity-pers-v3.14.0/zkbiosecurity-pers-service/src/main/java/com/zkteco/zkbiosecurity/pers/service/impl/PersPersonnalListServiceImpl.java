package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.auth.exception.ZKAuthException;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonnalListDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonnallistPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.model.PersPersonnalList;
import com.zkteco.zkbiosecurity.pers.model.PersPersonnallistPerson;
import com.zkteco.zkbiosecurity.pers.service.Pers2OtherService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonExtService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonnalListService;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.utils.ProcessBeanUtil;
import com.zkteco.zkbiosecurity.pers.vo.*;
import com.zkteco.zkbiosecurity.vid.service.Hw2OtherService;

/**
 * 名单库接口实现
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2020-07-22 16:35:16
 */
@Service
@Transactional
public class PersPersonnalListServiceImpl implements PersPersonnalListService {
    @Autowired
    private PersPersonnalListDao persPersonnalListDao;
    @Autowired
    private PersPersonnallistPersonDao persPersonnallistPersonDao;
    @Autowired(required = false)
    private PersPersonService persPersonService;
    @Autowired(required = false)
    private Hw2OtherService hw2OtherService;
    @Autowired(required = false)
    private PersPersonExtService[] persPersonExtServices;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private PersPersonnalListService persPersonnalListService;
    @Autowired(required = false)
    private Pers2OtherService pers2OtherService;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private AuthUserService authUserService;

    @Override
    public PersPersonnalListItem saveItem(PersPersonnalListItem item) {
        PersPersonnalList persPersonnalList = Optional.ofNullable(item).map(i -> i.getId())
            .filter(StringUtils::isNotBlank).flatMap(persPersonnalListDao::findById).orElse(new PersPersonnalList());
        ModelUtil.copyPropertiesIgnoreNull(item, persPersonnalList);
        persPersonnalListDao.save(persPersonnalList);
        item.setId(persPersonnalList.getId());
        return item;
    }

    @Override
    public List<PersPersonnalListItem> getByCondition(PersPersonnalListItem condition) {
        return (List<PersPersonnalListItem>)persPersonnalListDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persPersonnalListDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public ZKResultMsg delByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            Collection<String> list = CollectionUtil.strToList(ids);
            // 判断设备下是否存在已下发的名单库 add by hql 20210521
            if (hw2OtherService != null && hw2OtherService.existsByPersonnelListIds(list)) {
                throw new ZKBusinessException(
                    String.format(I18nUtil.i18nCode("pers_personnal_databaseHasBeenDistributed")));
            }
            List<String> existPerson = new ArrayList<>();
            list.forEach(id -> {
                // 初始化名单库不能删除
                PersPersonnalList persPersonnalList = persPersonnalListDao.findById(id).get();
                if (null != persPersonnalList.getInitFlag() && persPersonnalList.getInitFlag()) {
                    throw new ZKAuthException("common_prompt_initDataCanNotDel");
                }
                Integer count = persPersonnallistPersonDao.countPersPersonnallistPeopleByPersonnallistId(id);
                if (count != 0) {
                    PersPersonnalListItem item = getItemById(id);
                    if (item != null) {
                        existPerson.add(item.getName());
                    }
                }
            });
            // 如果选择的名单库都没有人员,才进行删除
            if (existPerson.isEmpty()) {
                list.forEach(id -> {
                    persPersonnalListDao.deleteById(id);
                });
            } else {
                // 提示哪些名单库存在人员
                String listNames = String.join(",", existPerson);
                return I18nUtil.i18nMsg(
                    ZKResultMsg.failMsg(listNames + ":" + I18nUtil.i18nCode("pers_personnallist_notDel_existPerson")));
            }
            return I18nUtil.i18nMsg(ZKResultMsg.successMsg());
        }
        return I18nUtil.i18nMsg(ZKResultMsg.failMsg());
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            Collection<String> list = CollectionUtil.strToList(ids);
            list.forEach(id -> {
                persPersonnalListDao.deleteById(id);
            });
        }
        return false;
    }

    @Override
    public PersPersonnalListItem getItemById(String id) {
        if (StringUtils.isNotBlank(id)) {
            List<PersPersonnalListItem> items = getByCondition(new PersPersonnalListItem(id));
            return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
        }
        return null;
    }

    @Override
    public List<String> getPersonIdsById(String id) {
        return persPersonnallistPersonDao.findPersonIdByPersonnallistId(id);
    }

    @Override
    public Pager getPersonPager(String sessionId, PersPersonnallistPersonItem condition, int page, int pageSize) {
        // 从名单库人员表中查询人员数据
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        Pager pager = persPersonnallistPersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition),
            page, pageSize);
        return pager;
    }

    /**
     * 封装条件和查询结果 获取人员分页数据（不包括黑名单中已存在的人员数据）
     *
     * <AUTHOR>
     * @date 2020/7/25 10:59
     */
    private Pager getPerPersonItemsByPager(String sessionId, PersPersonPersonnalListSelectItem condition, int page,
        int pageSize) {
        PersPersonSelectItem item = new PersPersonSelectItem();
        ModelUtil.copyPropertiesIgnoreNull(condition, item);
        // 过滤黑名单中存在的人员
        // 查出黑名单对象
        List<PersPersonnalListItem> bandPersonList = getItemByType(PersConstants.TYPE_BAND_LIST);
        // 获取黑名单中人员id集合
        List<String> bandPersonIdList =
            persPersonnallistPersonDao.findPersonIdByPersonnallistId(bandPersonList.get(0).getId());
        if (StringUtils.isBlank(condition.getNotInId())) {
            item.setNotInId(StrUtil.collectionToStr(bandPersonIdList));
        } else {
            item.setNotInId(condition.getNotInId() + "," + StrUtil.collectionToStr(bandPersonIdList));
        }
        item.setPersName(condition.getName());
        item.setPersPin(condition.getPin());
        item.setDeptName(condition.getDeptName());
        item.setInId(condition.getInId());
        item.setInDeptId(condition.getInDeptId());
        if ("pin".equals(condition.getSortName())) {
            item.setSortName("persPin");
        } else if ("name".equals(condition.getSortName())) {
            item.setSortName("persName");
        }
        Pager pager = persPersonService.findPersonSelectItem(sessionId, item, page, pageSize);
        List<PersPersonSelectItem> persPersonSelectItems = (List<PersPersonSelectItem>)pager.getData();
        List<PersPersonPersonnalListSelectItem> list = new ArrayList<>();
        persPersonSelectItems.stream().forEach(persPersonSelectItem -> {
            PersPersonPersonnalListSelectItem personSelectItem = new PersPersonPersonnalListSelectItem();
            personSelectItem.setId(persPersonSelectItem.getId());
            personSelectItem.setPin(persPersonSelectItem.getPersPin());
            personSelectItem.setName(persPersonSelectItem.getPersName());
            personSelectItem.setDeptName(persPersonSelectItem.getDeptName());
            list.add(personSelectItem);
        });
        pager.setData(list);
        return pager;
    }

    @Override
    public ZKResultMsg addPerson(String id, String personIds) {
        if (StringUtils.isNotBlank(personIds) && StringUtils.isNotBlank(id)) {
            List<PersPerson> persPersonList = persPersonDao.findByIdIn(StrUtil.strToList(personIds));
            PersPersonnalList list = persPersonnalListDao.findById(id).orElse(null);
            return addPerson(list, persPersonList);
        }
        return ZKResultMsg.failMsg();
    }

    @Override
    public ZKResultMsg addPerson(String id, String personIds, String deptIds) {
        if (StringUtils.isNotBlank(id)) {
            PersPersonnalList list = persPersonnalListDao.findById(id).orElse(null);
            if (list != null) {
                List<PersPerson> persPersonList = new ArrayList<>();
                if (StringUtils.isNotBlank(deptIds)) {
                    // 过滤黑名单中存在的人员
                    List<List<String>> deptIdList =
                        CollectionUtil.split(CollectionUtil.strToList(deptIds), CollectionUtil.splitSize);
                    for (List<String> ids : deptIdList) {
                        List<PersPerson> persPersons =
                            persPersonDao.getPersonsByDeptIdInAndListTypeFiletrAndListIdFilter(ids,
                                PersConstants.TYPE_BAND_LIST, id);
                        persPersonList.addAll(persPersons);
                    }
                } else if (StringUtils.isNotBlank(personIds)) {
                    persPersonList = persPersonDao.findByIdIn(StrUtil.strToList(personIds));
                }
                progressCache
                    .setProcess(new ProcessBean(10, 10, I18nUtil.i18nCode("pers_import_resolutionComplete") + "<br/>"));
                return addPerson(list, persPersonList);
            }
        }
        return ZKResultMsg.failMsg();
    }

    private ZKResultMsg addPerson(PersPersonnalList persPersonnalList, List<PersPerson> persPersons) {
        if (!persPersons.isEmpty() && persPersonnalList != null) {
            // 判断该名单库是否为黑名单，且需要下发的人员存不存在于黑名单
            // 分批处理，一次处理800人 已存在人员判断是否更新数据
            int beginProgress = 20;
            List<List<PersPerson>> persPersonList = CollectionUtil.split(persPersons, CollectionUtil.splitSize);

            float avgProgress = (float)60 / persPersonList.size();
            int i = 0;
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 10,
                I18nUtil.i18nCode("common_op_processing")));

            for (List<PersPerson> personList : persPersonList) {
                int progress = beginProgress + 10 + (int)(avgProgress * i++);
                progressCache.setProcess(new ProcessBean(progress, progress, ""));
                List<String> personIds = (List<String>)CollectionUtil.getModelIdsList(personList);
                // 查询人员-名单库关系表，防止并发操作产生的数据重复
                List<PersPersonnallistPerson> persPersonnallistPersons =
                    persPersonnallistPersonDao.findBypersonnallistIdAndPersonIdIn(persPersonnalList.getId(), personIds);
                Map<String, PersPersonnallistPerson> persPersonnallistPersonMap = persPersonnallistPersons.stream()
                    .collect(Collectors.toMap(PersPersonnallistPerson::getPersonId, Function.identity()));
                List<String> newPersonIds = new ArrayList<>();
                List<PersPersonnallistPerson> personnallistPersonList = new ArrayList<>();

                for (PersPerson person : personList) {
                    // 过滤人员-名单库关系表已存在的人员关系
                    if (Objects.isNull(persPersonnallistPersonMap.get(person.getId()))) {
                        // 名单库新增人员信息填充
                        PersPersonnallistPerson personnallistPerson = new PersPersonnallistPerson();
                        personnallistPerson.setPersonnallistId(persPersonnalList.getId());
                        personnallistPerson.setPersonId(person.getId());
                        personnallistPerson.setLinkTbl(PersConstants.PERS_PERSON);
                        personnallistPerson.setPersonName(person.getName());
                        personnallistPerson.setPersonPin(person.getPin());
                        personnallistPerson.setPersonGender(person.getGender());
                        personnallistPerson.setPersonBirthday(person.getBirthday());
                        personnallistPerson.setIdCard(person.getIdCard());
                        personnallistPerson.setPositionName(
                            person.getPersPosition() != null ? person.getPersPosition().getName() : "");
                        personnallistPerson.setMobilePhone(person.getMobilePhone());
                        personnallistPerson.setEmail(person.getEmail());
                        personnallistPersonList.add(personnallistPerson);
                        newPersonIds.add(person.getId());
                    }
                }
                if (PersConstants.TYPE_BAND_LIST.equals(persPersonnalList.getType()) && !newPersonIds.isEmpty()) {
                    // 添加名单为黑名单，同步删除允许名单内人员
                    delAllowListPerson(newPersonIds);
                }
                persPersonnallistPersonDao.saveAll(personnallistPersonList);
                if (Objects.nonNull(persPersonExtServices) && !newPersonIds.isEmpty()) {
                    // 通知其他模块同步下发人员
                    // 查询出名单库信息
                    try {
                        Arrays.stream(persPersonExtServices).forEach(ps -> ps
                            .addPersonnelListPersonExt(persPersonnalList.getId(), StringUtils.join(newPersonIds, ",")));
                    } catch (Exception e) {
                        progressCache.finishProcess(I18nUtil.i18nCode("common_progress_finish") + "<br/>", "",
                            I18nUtil.i18nCode("common_op_currProgress"));
                        return ZKResultMsg.failMsg();
                    }

                }
            }
            return ZKResultMsg.successMsg();

        }
        return ZKResultMsg.failMsg();
    }

    @Override
    public Pager getNoExistPerson(String sessionId, PersPersonPersonnalListSelectItem condition, int page,
        int pageSize) {
        if (Objects.nonNull(condition)) {
            if (StringUtils.isNotBlank(condition.getPersonnelListId())) {
                // 过滤该名单下已存在的人员id
                condition.setExistPersonId(condition.getPersonnelListId());
            }
            condition.setBandPersonId(PersConstants.TYPE_BAND_LIST);
            String userId = authUserService.getUserIdBySessionId(sessionId);
            if (StringUtils.isNotBlank(userId)) {
                condition.setUserId(userId);
            }
            condition.setEnabledCredential(true);
        }
        return persPersonnalListDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page,
            pageSize);
    }

    @Override
    public ZKResultMsg deletePerson(String personnalListId, String ids) {
        if (StringUtils.isNotBlank(ids)) {
            // 添加业务模块扩展调用
            if (persPersonExtServices != null) {
                // 删除通知视频设备删除人员
                List<PersPersonnallistPerson> list =
                    persPersonnallistPersonDao.findByIdIn((List<String>)CollectionUtil.strToList(ids));
                Collection<String> personIdList =
                    CollectionUtil.getPropertyList(list, PersPersonnallistPerson::getPersonId, "-1");
                Arrays.stream(persPersonExtServices).forEach(
                    ps -> ps.delPersonnelListPersonExt(personnalListId, StrUtil.collectionToStr(personIdList)));
            }
            // 删除名单库下的人员
            List<String> idList = (List<String>)CollectionUtil.strToList(ids);
            for (String id : idList) {
                persPersonnallistPersonDao.deleteById(id);
            }
            return ZKResultMsg.successMsg();
        }
        return ZKResultMsg.failMsg();
    }

    @Override
    public PersPersonnalListItem getItemByName(String name) {
        PersPersonnalList persPersonnalList = persPersonnalListDao.findByName(name);
        if (persPersonnalList != null) {
            PersPersonnalListItem item = new PersPersonnalListItem();
            ModelUtil.copyPropertiesIgnoreNull(persPersonnalList, item);
            return item;
        }
        return null;
    }

    @Override
    public Map<String, String> getPersonnelListPersonCount() {
        List<Object[]> list = persPersonnallistPersonDao.getCountByPersonnelListGroup();
        Map<String, String> allPersonListCountMap = new HashMap<>();
        list.stream().forEach(obj -> {
            allPersonListCountMap.put(obj[0].toString(), obj[1].toString());
        });
        return allPersonListCountMap;
    }

    @Override
    public Map<String, String> getPersonCountByAuthUserFilter(String sessionId) {
        // 封装部门条件
        List<Object[]> list = new ArrayList<>();
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            list = persPersonnallistPersonDao.getCountByUserIdAndPersonnelListGroup(userId);
        } else {
            list = persPersonnallistPersonDao.getCountByPersonnelListGroup();
        }
        Map<String, String> allPersonListCountMap = new HashMap<>();
        list.stream().forEach(obj -> {
            allPersonListCountMap.put(obj[0].toString(), obj[1].toString());
        });
        return allPersonListCountMap;
    }

    @Override
    public PersPersonnalListItem initData(PersPersonnalListItem item) {
        PersPersonnalListItem persPersonnalListItem = getItemByName(item.getName());
        // 如果没有初始化过,则初始化
        if (Objects.isNull(persPersonnalListItem)) {
            return saveItem(item);
        }
        item.setId(persPersonnalListItem.getId());
        return item;
    }

    @Override
    public List<SelectItem> getPersonnalListSelectData() {
        List<SelectItem> items = new ArrayList<>();
        PersPersonnalListItem persPersonnalListItem = new PersPersonnalListItem();
        List<PersPersonnalListItem> itemList = getByCondition(persPersonnalListItem);
        if (itemList.size() > 0) {
            for (PersPersonnalListItem item : itemList) {
                SelectItem selectItem = new SelectItem(item.getName(), item.getId());
                items.add(selectItem);
            }
        }
        return items;
    }

    @Override
    public ZKResultMsg delPersonByPersonId(String personIds) {
        if (StringUtils.isNotBlank(personIds)) {
            List<String> personIdList = (List<String>)CollectionUtil.strToList(personIds);
            persPersonnallistPersonDao.deleteByPersonIdIn(personIdList);
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public ZKResultMsg deletePersonnallistPerson(String personnallistId, String personId) {
        if (StringUtils.isNotBlank(personnallistId) && StringUtils.isNotBlank(personId)) {
            persPersonnallistPersonDao.deleteByPersonnallistIdAndPersonId(personnallistId, personId);
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public List<PersPersonItem> getPersonList(PersPersonnalListItem persPersonnalListItem, int page, int size) {
        // 根据名单库id获取人员id
        List<PersPersonnallistPerson> list =
            persPersonnallistPersonDao.findByPersonnallistId(persPersonnalListItem.getId());
        PersPersonItem condition = new PersPersonItem();
        if (!CollectionUtil.isEmpty(list)) {
            Collection<String> personIdList =
                CollectionUtil.getPropertyList(list, PersPersonnallistPerson::getPersonId, "-1");
            condition.setInId(StrUtil.collectionToStr(personIdList));
        } else {
            condition.setInId("-1");
        }
        // 根据分页参数获取有照片数据的人员列表信息
        Pager pager = persPersonService.getItemsByPhotoFilter(condition, page, size);
        List<PersPersonItem> persPersonItems = (List<PersPersonItem>)pager.getData();
        return persPersonItems;
    }

    @Override
    public Integer getPersonCount(PersPersonnalListItem persPersonnalListItem) {
        // 根据名单库id获取人员id
        List<PersPersonnallistPerson> list =
            persPersonnallistPersonDao.findByPersonnallistId(persPersonnalListItem.getId());
        List<String> personIdList =
            (List<String>)CollectionUtil.getPropertyList(list, PersPersonnallistPerson::getPersonId, "-1");
        // 获取照片不为空的人员数量
        return persPersonService.countByIdsAndPhotoIsNotNull(personIdList);
    }

    @Override
    public List<PersPersonnallistPersonItem> getPersonDataByPersonId(String personIds) {
        List<PersPersonnallistPersonItem> filterPersonList = new ArrayList<>();
        Map<String, PersPersonnallistPersonItem> filterPersonMap = new HashMap<>();
        PersPersonnallistPersonItem condition = new PersPersonnallistPersonItem();
        condition.setInPersonId(personIds);
        List<PersPersonnallistPersonItem> persPersonnallistPersonItems =
            (List<PersPersonnallistPersonItem>)persPersonnallistPersonDao
                .getItemsBySql(PersPersonnallistPersonItem.class, SQLUtil.getSqlByItem(condition));
        // 如果人员id重复，则不重复添加
        persPersonnallistPersonItems.forEach(item -> {
            if (!filterPersonMap.containsKey(item.getPersonId())) {
                filterPersonMap.put(item.getPersonId(), item);
                filterPersonList.add(item);
            }
        });
        return filterPersonList;
    }

    @Override
    public List<PersPersonnalListItem> getItemByType(String type) {
        PersPersonnalListItem persPersonnalListItem = new PersPersonnalListItem();
        persPersonnalListItem.setType(type);
        List<PersPersonnalListItem> persPersonnalListItems = getByCondition(persPersonnalListItem);
        return persPersonnalListItems;
    }

    @Override
    public PersPersonnalListItem getInitItemByType(String type) {
        // 根据初始化标签与类型获取名单库
        List<PersPersonnalList> persPersonnalLists = persPersonnalListDao.findByTypeAndInitFlag(type, true);
        PersPersonnalList persPersonnalList = Optional.ofNullable(persPersonnalLists).filter(list -> !list.isEmpty())
            .map(list -> list.get(0)).orElse(null);
        if (persPersonnalList != null) {
            PersPersonnalListItem item = new PersPersonnalListItem();
            ModelUtil.copyPropertiesIgnoreNull(persPersonnalList, item);
            return item;
        }
        return null;
    }

    @Override
    public List<PersPersonnalListItem> findByIdList(List<String> ids) {
        List<PersPersonnalListItem> items = new ArrayList<>();
        List<PersPersonnalList> persPersonnalLists = persPersonnalListDao.findByIdList(ids);
        if (!CollectionUtil.isEmpty(persPersonnalLists) && persPersonnalLists.size() > 0) {
            items = ModelUtil.copyListPropertiesWithIgnore(persPersonnalLists, PersPersonnalListItem.class);
            return items;
        }
        return null;
    }

    @Override
    public List<PersPersonnallistPersonItem> findByPersonnallistIdIn(List<String> ids) {
        List<PersPersonnallistPersonItem> items = new ArrayList<>();
        List<PersPersonnallistPerson> persPersonnallistPeoples =
            persPersonnallistPersonDao.findByPersonnallistIdIn(ids);
        if (!CollectionUtil.isEmpty(persPersonnallistPeoples) && persPersonnallistPeoples.size() > 0) {
            items = ModelUtil.copyListPropertiesWithIgnore(persPersonnallistPeoples, PersPersonnallistPersonItem.class);
            return items;
        }
        return null;
    }

    /**
     * 删除允许名单内人员
     *
     * <AUTHOR>
     * @date 2021-11-22 16:28
     * @since 1.0.0
     */
    public void delAllowListPerson(List<String> personIds) {
        List<PersPersonnallistPerson> personnallistPersonList =
            persPersonnallistPersonDao.getByListTypeAndPersId(PersConstants.TYPE_ALLOW_LIST, personIds);

        if (!personnallistPersonList.isEmpty()) {
            Map<String, List<PersPersonnallistPerson>> personnelListPersonMap = personnallistPersonList.stream()
                .collect(Collectors.groupingBy(PersPersonnallistPerson::getPersonnallistId));
            // 将人员从允许名单中删除
            if (Objects.nonNull(persPersonExtServices)) {
                personnelListPersonMap.forEach((listId, listPersonList) -> {
                    // 通知其他模块设备同步删除人员
                    Arrays.stream(persPersonExtServices).forEach(ps -> ps.delPersonnelListPersonExt(listId,
                        CollectionUtil.getPropertys(listPersonList, PersPersonnallistPerson::getPersonId)));
                });
            }
            persPersonnallistPersonDao.deleteByPersonnallistTypeAndPersonId(PersConstants.TYPE_ALLOW_LIST, personIds);
        }
    }

    @Override
    public List<PersPersonnallistPersonItem> protectPin(List<PersPersonnallistPersonItem> items) {
        if (persPersonService.isProtectData()) {
            items.forEach(item -> {
                String pin = item.getPersonPin();
                item.setPersonPin(PersRegularUtil.hideWithAsterisk(pin));
            });
        }
        return items;
    }
}
