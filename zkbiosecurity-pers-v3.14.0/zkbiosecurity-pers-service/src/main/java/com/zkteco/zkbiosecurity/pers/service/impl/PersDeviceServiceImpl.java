/**
 * File Name: AccDoorServiceImpl
 * Created by GenerationTools on 2018-03-03 上午11:59
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import com.zkteco.zkbiosecurity.acc.service.Acc4PersDeviceService;
import com.zkteco.zkbiosecurity.acc.service.Acc4PersWiegandFmtService;
import com.zkteco.zkbiosecurity.acc.vo.Acc4PersDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceConditionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersDeviceService;
import com.zkteco.zkbiosecurity.pers.vo.PersDeviceSelectItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 对应百傲瑞达 AccDoorServiceImpl
 * <AUTHOR>
 * @date:	2018-03-03 上午11:59
 * @version v1.0
 */
@Service
@Transactional
public class PersDeviceServiceImpl implements PersDeviceService {

	@Autowired(required = false)
	private Acc4PersWiegandFmtService acc4PersWiegandFmtService;
	@Autowired(required = false)
	private Acc4PersDeviceService acc4PersDeviceService;

	@Override
	public Pager getSelectDeviceItemByPage(PersDeviceSelectItem condition, int page, int size) {
		if(Objects.nonNull(acc4PersDeviceService)){
			Pager pager = new Pager();
			AccDeviceConditionItem accDeviceConditionItem = new AccDeviceConditionItem();
			ModelUtil.copyPropertiesIgnoreNull(condition,accDeviceConditionItem);
			List<Acc4PersDeviceItem> acc4PersDeviceItemList= acc4PersDeviceService.getItemsByPage(accDeviceConditionItem, page, size);
			List<PersDeviceSelectItem> itemList=new ArrayList<>();
			acc4PersDeviceItemList.forEach(item -> {
				PersDeviceSelectItem persDeviceSelectItem = new PersDeviceSelectItem();
				ModelUtil.copyPropertiesIgnoreNull(item, persDeviceSelectItem);
				itemList.add(persDeviceSelectItem);
			});
			pager.setData(itemList);
			return pager;
		}else{
			Pager pager = new Pager();
			return pager;
		}
	}

	@Override
	public Pager getItemsByPage(BaseItem baseItem, int i, int i1) {
		Pager pager = new Pager();
		return pager;
	}

	@Override
	public boolean deleteByIds(String ids) {
		return true;
	}


}