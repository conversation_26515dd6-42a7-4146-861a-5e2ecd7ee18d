/**
 * File Name: PersLeavePersonServiceImpl Created by GenerationTools on 2018-03-14 上午10:10 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.common.base.Joiner;
import com.zkteco.zkbiosecurity.att.vo.AttPers4PersLeaveItem;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.PersLeavePersonDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonnallistPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersLeavePerson;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.model.PersPersonnallistPerson;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.utils.ProcessBeanUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPushLeavePersonItem;
import com.zkteco.zkbiosecurity.system.service.BaseDictionaryValueService;

/**
 * 对应百傲瑞达 PersLeavePersonServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-03-14 上午10:10
 * @version v1.0
 */
@Service
@Transactional
public class PersLeavePersonServiceImpl implements PersLeavePersonService {
    @Autowired
    private PersLeavePersonDao persLeavePersonDao;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private BaseDictionaryValueService baseDictionaryValueService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private ProgressCache progressCache;
    @Autowired
    private PersPersonnalListService persPersonnalListService;
    @Autowired
    private PersPersonnallistPersonDao persPersonnallistPersonDao;
    @Autowired
    private Pers2OtherService pers2OtherService;
    @Autowired(required = false)
    private PersPersonExtService[] persPersonExtServices;

    @Override
    public PersLeavePersonItem saveItem(PersLeavePersonItem item) {
        PersLeavePerson persLeavePerson = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> persLeavePersonDao.findById(id)).orElse(new PersLeavePerson());
        ModelUtil.copyPropertiesIgnoreNull(item, persLeavePerson);
        if (StringUtils.isNotBlank(persLeavePerson.getLeaveReason())) {
            // 移除换行\r、回车\n、制表\t符，避免出现字符超长问题
            persLeavePerson.setLeaveReason(persLeavePerson.getLeaveReason().replaceAll("[\t\n\r]", ""));
        }
        persLeavePersonDao.save(persLeavePerson);
        item.setId(persLeavePerson.getId());
        return item;
    }

    @Override
    public List<PersLeavePersonItem> getByCondition(PersLeavePersonItem condition) {
        return (List<PersLeavePersonItem>)persLeavePersonDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persLeavePersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, BaseItem condition, int pageNo, int pageSize) {
        buildCondition(sessionId, (PersLeavePersonItem)condition);
        return persLeavePersonDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), pageNo,
            pageSize);
    }

    @Override
    public List<String> getPersonPinByDeptPersAuthUserFilter(String sessionId, PersLeavePersonItem condition) {
        buildCondition(sessionId, condition);
        List<PersLeavePersonItem> persLeavePersonList = (List<PersLeavePersonItem>)persLeavePersonDao
            .getItemsBySql(PersLeavePersonItem.class, SQLUtil.getSqlByItem(condition));
        // 获取人员Pin的数据
        List<String> persPersonPinList = persLeavePersonList.stream().map(PersLeavePersonItem::getPin)
            .filter(StringUtils::isNotBlank).collect(Collectors.toList());

        return persPersonPinList;

    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            List<PersPushLeavePersonItem> persPushLeavePersonItems = new ArrayList<>();
            List<PersLeavePerson> persLeavePersonList = persLeavePersonDao.findByIdList(StrUtil.strToList(ids));
            persLeavePersonList.forEach(persLeavePerson -> {
                PersPushLeavePersonItem persPushLeavePersonItem = new PersPushLeavePersonItem();
                persPushLeavePersonItem.setLeaveId(persLeavePerson.getId());
                persPushLeavePersonItem.setPin(persLeavePerson.getPin());
                persPushLeavePersonItems.add(persPushLeavePersonItem);
            });
            String pins = CollectionUtil.getPropertys(persLeavePersonList, PersLeavePerson::getPin);
            persLeavePersonDao.deleteAll(persLeavePersonList);
            if (StringUtils.isNotEmpty(pins)) {
                String[] pinArray = StringUtils.split(pins, ",");
                for (String pin : pinArray) {
                    // 删除离职人员照片
                    FileUtil.deleteFile(
                        FileUtils.getLocalFullPath("upload/pers/personnallist/leavePerson/" + pin + ".jpg"));
                }
            }
            // 通知离职人员删除
            pers2OtherService.pushLeavePersonDel(persPushLeavePersonItems);
        }
        // 根据人员id删除名单库人员
        if (StringUtils.isNotBlank(ids)) {
            List<String> personIdList = (List<String>)CollectionUtil.strToList(ids);
            persPersonnallistPersonDao.deleteByPersonIdIn(personIdList);
        }
        return false;
    }

    @Override
    public PersLeavePersonItem getItemById(String id) {
        return Optional.ofNullable(id).filter(StringUtils::isNotBlank)
            .map(i -> getByCondition(new PersLeavePersonItem(i))).filter(list -> !list.isEmpty())
            .map(list -> list.get(0)).orElse(null);
    }

    @Override
    public PersLeavePersonItem getItemByPin(String pin) {
        return Optional.ofNullable(pin).filter(StringUtils::isNotBlank)
            .map(persLeavePersonDao::findByPinOrderByCreateTimeDesc).filter(l -> !CollectionUtil.isEmpty(l))
            .map(l -> l.get(0)).map(leavePerson -> ModelUtil.copyProperties(leavePerson, new PersLeavePersonItem()))
            .orElse(null);
    }

    @Override
    public List<PersLeavePersonItem> getItemByPins(Collection<String> pins) {
        return Optional.ofNullable(pins).filter(i -> !CollectionUtil.isEmpty(i)).map(persLeavePersonDao::findByPinIn)
            .map(list -> ModelUtil.copyListProperties(list, PersLeavePersonItem.class)).orElse(new ArrayList<>());
    }

    @Override
    public List<PersLeavePersonItem> getItemByDeptIds(Collection<String> deptIds) {
        List<PersLeavePersonItem> persLeavePersonItemList = new ArrayList<PersLeavePersonItem>();
        if (CollectionUtil.isEmpty(deptIds)) {
            return persLeavePersonItemList;
        }
        List<List<String>> splitDeptIds = CollectionUtil.split(deptIds, CollectionUtil.splitSize);
        splitDeptIds.forEach(splitDeptId -> {
            List<PersLeavePerson> persLeavePersonList = persLeavePersonDao.findByDeptIdIn(splitDeptId);
            persLeavePersonList.forEach(persLeavePerson -> {
                PersLeavePersonItem persLeavePersonItem =
                    ModelUtil.copyProperties(persLeavePerson, new PersLeavePersonItem());
                persLeavePersonItemList.add(persLeavePersonItem);
            });
        });
        return persLeavePersonItemList;
    }

    @Override
    public Integer getAllPersonLeaveCount() {
        long leavePersCount = persLeavePersonDao.count();
        return Math.toIntExact(leavePersCount);
    }

    @Override
    public Integer getPersonLeaveCountByDeptIds(String deptIds) {
        return persLeavePersonDao.countByDeptIdIn(CollectionUtil.strToList(deptIds));
    }

    @Override
    public List<PersLeavePersonItem> findByPinsAndLeaveDate(String pins, Date beginDate, Date endDate) {
        PersLeavePersonItem condition = new PersLeavePersonItem();
        condition.setInPersonPins(pins);
        condition.setBeginDate(beginDate);
        condition.setEndDate(endDate);
        return getByCondition(condition);
    }

    @Override
    public void batchLeave(PersLeavePersonItem item, String personIds) {
        List<PersPushLeavePersonItem> persPushLeavePersonItems = new ArrayList<>();
        Map<String, AttPers4PersLeaveItem> attPersonGroupId = new HashMap<String, AttPers4PersLeaveItem>();
        PersLeavePerson leavePerson = null;
        List<PersPerson> persons = persPersonDao.findByIdIn(CollectionUtil.strToList(personIds));
        String deptIds = CollectionUtil.getPropertys(persons, PersPerson::getDeptId);
        List<AuthDepartmentItem> items = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> maps = CollectionUtil.itemListToIdMap(items);
        PersPersonnalListItem bandPersonnalListItem =
            persPersonnalListService.getInitItemByType(PersConstants.TYPE_BAND_LIST);
        List<PersPersonnalListItem> passPersonnalListItems =
            persPersonnalListService.getItemByType(PersConstants.TYPE_ALLOW_LIST);
        List<String> bandPersonIds = new ArrayList<>();
        List<String> passPersonIds = new ArrayList<>();
        for (PersPerson person : persons) {
            PersPushLeavePersonItem persPushLeavePersonItem = new PersPushLeavePersonItem();
            persPushLeavePersonItem.setPersonId(person.getId());
            persPushLeavePersonItem.setPin(person.getPin());
            leavePerson = new PersLeavePerson();
            ModelUtil.copyProperties(item, leavePerson);
            leavePerson.setName(person.getName());
            leavePerson.setLastName(person.getLastName());
            leavePerson.setPin(person.getPin());
            AuthDepartmentItem dept = maps.get(person.getDeptId());
            leavePerson.setDeptId(dept.getId());
            leavePerson.setDeptName(dept.getName());
            leavePerson.setDeptCode(dept.getCode());
            AttPers4PersLeaveItem attPersLeaveItem = attPersonGroupId.get(person.getId());
            // 没有考勤模块，这个对象为null
            if (Objects.nonNull(attPersLeaveItem)) {
                leavePerson.setAttGroupId(attPersLeaveItem.getGroupId());
                leavePerson.setIsAttendance(attPersLeaveItem.getIsAttendance());
            }
            if (Objects.nonNull(person.getHireDate())) {
                leavePerson.setHireDate(person.getHireDate());
            }
            // 人员如有抠图照片，则先进行备份用于下发至禁止名单库时使用 add by cxq 20200121
            String imgStr =
                FileUtils.getFileBase64Str(FileUtils.getLocalFullPath("upload/pers/user/" + FileUtils.cropFacePath
                    + FileUtils.separator + person.getPin() + FileUtils.separator + person.getPin() + ".jpg"));
            if (StringUtils.isNotEmpty(imgStr)) {
                String filePath = "upload/pers/personnallist/leavePerson/";
                FileUtils.saveFile(filePath, person.getPin() + ".jpg", imgStr, false);
            }
            // 删除apptoken
            authUserService.delApiToken(person.getPin());
            if (StringUtils.isNotBlank(leavePerson.getLeaveReason())) {
                // 移除换行\r、回车\n、制表\t符，避免出现字符超长问题
                leavePerson.setLeaveReason(leavePerson.getLeaveReason().replaceAll("[\t\n\r]", ""));
            }
            persLeavePersonDao.save(leavePerson);
            persPushLeavePersonItem.setLeaveId(leavePerson.getId());
            persPushLeavePersonItems.add(persPushLeavePersonItem);
            // 禁止名单库未下发过该人员则将该人员下发至禁止名单库 add by cxq 20210115
            List<String> id = persPersonnallistPersonDao
                .findIdByPersonnallistIdAndPersonId(bandPersonnalListItem.getId(), person.getId());
            if (CollectionUtil.isEmpty(id) && "1".equals(item.getForbidden())) {
                PersPersonnallistPerson persPersonnallistPerson = new PersPersonnallistPerson();
                persPersonnallistPerson.setPersonId(leavePerson.getId());
                persPersonnallistPerson.setPersonnallistId(bandPersonnalListItem.getId());
                persPersonnallistPerson.setPersonName(leavePerson.getName());
                persPersonnallistPerson.setPersonPin(leavePerson.getPin());
                persPersonnallistPerson.setLastName(leavePerson.getLastName());
                persPersonnallistPerson.setLinkTbl(PersConstants.PERS_LEAVEPERSON);
                persPersonnallistPersonDao.save(persPersonnallistPerson);
                persPersonnallistPersonDao.flush();
                bandPersonIds.add(leavePerson.getId());
            }
            if (!CollectionUtils.isEmpty(passPersonnalListItems) && passPersonnalListItems.size() > 0) {
                passPersonnalListItems.forEach(persPersonnalListItem -> {
                    // 将人员从允许名单中删除
                    persPersonnallistPersonDao.deleteByPersonnallistIdAndPersonId(persPersonnalListItem.getId(),
                        person.getId());
                });
                passPersonIds.add(person.getId());
            }

        }
        // 需先通知其他模块人员离职，再进行删除，有些模块根据人员id查询人员信息，若先删除再通知，模块查询不到人员信息会有问题
        // 通知其他模块离职
        pers2OtherService.pushLeaveOtherModelPerson(persPushLeavePersonItems);
        // 删除人员
        if (StringUtils.isNotBlank(personIds)) {
            persPersonService.deleteByIdsAndType(personIds, PersConstants.PERSON_OPERATE_TYPE_LEAVE);
        }

        if (Objects.nonNull(persPersonExtServices)) {
            // 下发到禁止名单
            if (!bandPersonIds.isEmpty() && bandPersonnalListItem != null) {
                Arrays.stream(persPersonExtServices).forEach(ps -> ps
                    .addPersonnelListPersonExt(bandPersonnalListItem.getId(), StringUtils.join(bandPersonIds, ",")));
            }
            // 从允许名单中删除
            if (!passPersonIds.isEmpty() && passPersonnalListItems != null && passPersonnalListItems.size() > 0) {
                passPersonnalListItems.forEach(persPersonnalListItem -> {
                    Arrays.stream(persPersonExtServices)
                        .forEach(ps -> ps.delPersonnelListPersonExt(bandPersonnalListItem.getId(),
                            StringUtils.join(passPersonIds, ",")));
                });
            }
        }
    }

    @Override
    public void buildCondition(String sessionId, PersLeavePersonItem condition) {
        // 封装部门条件
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        if (StringUtils.isBlank(condition.getInDeptId())) {
            if (StringUtils.isNotBlank(condition.getDeptId())) {
                List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getDeptId());
                condition.setInDeptId(StrUtil.collectionToStr(deptIdList));
            }
        } else if (StringUtils.isNotBlank(sessionId)) {
            condition.setInDeptId(condition.getInDeptId());
        }
        condition.setDeptId(null);
    }

    @Override
    public List<?> getItemData(Class targetClass, BaseItem codition, int begin, int end) {
        List<PersLeavePersonItem> items =
            persLeavePersonDao.getItemsDataBySql(targetClass, SQLUtil.getSqlByItem(codition), begin, end, true);
        for (PersLeavePersonItem item : items) {
            item.setLeaveTypeString(findBaseDicValueByKey("PersLeaveType", item.getLeaveTypeString()));
        }
        return items;
    }

    @Override
    public void handlerTransfer(List<PersLeavePersonItem> persLeavePersonItems) {
        // 以防大数据时进行分批数据
        List<List<PersLeavePersonItem>> personItemList =
            CollectionUtil.split(persLeavePersonItems, CollectionUtil.splitSize);
        // 获取部门集合 add by hql 20190805
        List<AuthDepartmentItem> authDepartmentItems = authDepartmentService.getByCondition(new AuthDepartmentItem());
        Map<String, AuthDepartmentItem> authDepartmentItemMap =
            CollectionUtil.listToKeyMap(authDepartmentItems, AuthDepartmentItem::getCode);
        // 获取职位的集合 add by hql 20190805
        List<PersLeavePerson> persLeavePeople = persLeavePersonDao.findAll();
        Map<String, PersLeavePerson> persLeavePersonMap =
            CollectionUtil.listToKeyMap(persLeavePeople, PersLeavePerson::getPin);
        for (List<PersLeavePersonItem> leavePersonItems : personItemList) {
            // 得到数据库中已有的人员信息，一次性取出后获取
            for (PersLeavePersonItem persLeavePersonItem : leavePersonItems) {
                PersLeavePerson persLeavePerson = persLeavePersonMap.remove(persLeavePersonItem.getPin());
                if (Objects.isNull(persLeavePerson)) {
                    persLeavePerson = new PersLeavePerson();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(persLeavePersonItem, persLeavePerson, "id");
                // 设置部门
                AuthDepartmentItem departmentItem = authDepartmentItemMap.get(persLeavePersonItem.getDeptCode());
                if (Objects.nonNull(departmentItem)) {
                    persLeavePerson.setDeptId(departmentItem.getId());
                    persLeavePerson.setDeptCode(departmentItem.getCode());
                    persLeavePerson.setDeptName(departmentItem.getName());
                }
                persLeavePersonDao.save(persLeavePerson);
            }
        }

    }

    /** 根据键获取字典值 */
    private String findBaseDicValueByKey(String dicKey, String key) {
        // 通过dicKey找到值的集合
        Map<String, String> valueMap = baseDictionaryValueService.getDictionaryValuesMap(dicKey);
        if (!valueMap.isEmpty() && valueMap.size() > 0) {
            return I18nUtil.i18nCode(valueMap.get(key));
        }
        return null;
    }

    @Override
    public ZKResultMsg importExcel(List<PersLeavePersonItem> itemList) {
        if (itemList.size() > 30000) {
            // 一次性导入只能30000，超过要分批次导入
            throw ZKBusinessException.warnException("pers_import_overData", itemList.size());
        }
        int importSize = itemList.size();
        int beginProgress = 20;
        // 导入人员pin号的集合，用于过滤重复的pin号
        Set<String> uniquePinSet = new HashSet<>();
        // 导入的人员要求人事已存在
        Map<String, PersPersonItem> existPersPersonMap = new HashMap<>();
        // 取出待导入数据中人员的pin号
        Collection<String> importPins = CollectionUtil.getPropertyList(itemList, PersLeavePersonItem::getPin, "-1");
        // 分批处理，一次处理800人
        List<List<String>> pinsList = CollectionUtil.split(importPins, CollectionUtil.splitSize);
        for (List<String> pins : pinsList) {
            // 根据pin号查出人事人员
            List<PersPersonItem> existPersonList = persPersonService.getItemsByPins(pins);
            if (existPersonList != null && existPersonList.size() > 0) {
                existPersPersonMap.putAll(CollectionUtil.listToKeyMap(existPersonList, PersPersonItem::getPin));
            }
        }
        Map<String, String> leaveTypeMap = baseDictionaryValueService.getDictionaryValuesMap("PersLeaveType");
        Map<String, String> persLeaveType = new HashMap<>();
        for (String key : leaveTypeMap.keySet()) {
            persLeaveType.put(I18nUtil.i18nCode(leaveTypeMap.get(key)), key);
        }
        Iterator<PersLeavePersonItem> itemIterator = itemList.iterator();
        // 先剔除无效数据
        while (itemIterator.hasNext()) {
            PersLeavePersonItem item = itemIterator.next();

            // pin唯一校验
            if (!uniquePinSet.add(item.getPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinIsRepeat", item.getPin())));
                itemIterator.remove();
                continue;
            }

            // 人员编号非空校验
            if (StringUtils.isBlank(item.getPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinNotEmpty", item.getPin())));
                itemIterator.remove();
                continue;
            }
            PersPersonItem persPersonItem = existPersPersonMap.get(item.getPin());
            // 判断软件是否有这个人
            if (Objects.isNull(persPersonItem)) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_app_personNull", item.getPin())));
                itemIterator.remove();
                continue;
            }
            // 时间空校验
            if (Objects.isNull(item.getLeaveDate())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_dimission_date_error", item.getPin())));
                itemIterator.remove();
                continue;
            }

            // 离职类型校验
            if (StringUtils.isBlank(item.getLeaveTypeString())
                || !persLeaveType.containsKey(item.getLeaveTypeString())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_dimission_leaveType_noExist", item.getPin())));
                itemIterator.remove();
                continue;
            }
        }
        // 剩下的可以插入数据库,分批处理，一次处理800条
        List<List<PersLeavePersonItem>> attLeaveInsertList = CollectionUtil.split(itemList, CollectionUtil.splitSize);
        for (List<PersLeavePersonItem> insertItemList : attLeaveInsertList) {
            List<PersPersonItem> insertPersonList = new ArrayList<>();
            Collection<String> insertPins =
                CollectionUtil.getPropertyList(insertItemList, PersLeavePersonItem::getPin, "-1");
            for (String pin : insertPins) {
                insertPersonList.add(existPersPersonMap.get(pin));
            }
            Collection<String> insertPersonIds =
                CollectionUtil.getPropertyList(insertPersonList, PersPersonItem::getId, "-1");
            String personIds = Joiner.on(",").join(insertPersonIds);
            Map<String, AttPers4PersLeaveItem> attPersonGroupId = new HashMap<String, AttPers4PersLeaveItem>();
            List<PersPushLeavePersonItem> persPushLeavePersonItems = new ArrayList<>();
            PersLeavePerson leavePerson = null;
            PersPersonItem persPersonItem = null;
            for (PersLeavePersonItem persLeavePersonItem : insertItemList) {
                leavePerson = new PersLeavePerson();
                persPersonItem = existPersPersonMap.get(persLeavePersonItem.getPin());
                // 保存入库
                leavePerson.setName(persPersonItem.getName());
                leavePerson.setLastName(persPersonItem.getLastName());
                leavePerson.setPin(persPersonItem.getPin());
                leavePerson.setDeptId(persPersonItem.getDeptId());
                leavePerson.setDeptName(persPersonItem.getDeptName());
                leavePerson.setDeptCode(persPersonItem.getDeptCode());
                leavePerson.setLeaveType(Integer.parseInt(persLeaveType.get(persLeavePersonItem.getLeaveTypeString())));
                leavePerson.setLeaveReason(persLeavePersonItem.getLeaveReason());
                leavePerson.setLeaveDate(persLeavePersonItem.getLeaveDate());
                AttPers4PersLeaveItem attPersLeaveItem = attPersonGroupId.get(persPersonItem.getId());
                // 没有考勤模块，这个对象为null
                if (Objects.nonNull(attPersLeaveItem)) {
                    leavePerson.setAttGroupId(attPersLeaveItem.getGroupId());
                    leavePerson.setIsAttendance(attPersLeaveItem.getIsAttendance());
                }
                if (persLeavePersonItem.getHireDate() != null) {
                    leavePerson.setHireDate(persLeavePersonItem.getHireDate());
                } else if (Objects.nonNull(persPersonItem.getHireDate())) {
                    leavePerson.setHireDate(persPersonItem.getHireDate());
                }
                leavePerson = persLeavePersonDao.save(leavePerson);
                // 通知其他模块离职
                PersPushLeavePersonItem persPushLeavePersonItem = new PersPushLeavePersonItem();
                persPushLeavePersonItem.setPersonId(persPersonItem.getId());
                persPushLeavePersonItem.setPin(persPersonItem.getPin());
                persPushLeavePersonItem.setLeaveId(leavePerson.getId());
                persPushLeavePersonItems.add(persPushLeavePersonItem);
            }
            // 需先通知其他模块人员离职，再进行删除，有些模块根据人员id查询人员信息，若先删除再通知，模块查询不到人员信息会有问题
            if (!persPushLeavePersonItems.isEmpty()) {
                pers2OtherService.pushLeaveOtherModelPerson(persPushLeavePersonItems);
                persPushLeavePersonItems.forEach(leavePersonItem -> {
                    persPersonService.deleteByIdsAndType(leavePersonItem.getPersonId(),
                        PersConstants.PERSON_OPERATE_TYPE_LEAVE);
                });
            }
        }
        // 失败数量
        int faildCount = importSize - itemList.size();
        // 成功：%s 条，失败：%s 条。
        progressCache.setProcess(ProcessBean.createNormalSingleProcess(99,
            I18nUtil.i18nCode("pers_import_result", itemList.size(), faildCount)));
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public List<PersLeavePersonItem> protectPin(List<PersLeavePersonItem> items) {
        if (persPersonService.isProtectData()) {
            items.forEach(item -> {
                String pin = item.getPin();
                item.setPin(PersRegularUtil.hideWithAsterisk(pin));
            });
        }
        return items;
    }
}