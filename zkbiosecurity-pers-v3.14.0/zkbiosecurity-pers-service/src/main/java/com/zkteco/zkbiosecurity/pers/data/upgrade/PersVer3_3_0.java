package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.pers.service.PersSupportFuncService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

@Component
public class PersVer3_3_0 implements UpgradeVersionManager {
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private PersSupportFuncService persSupportFuncService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v3.3.0";
    }

    @Override
    public boolean executeUpgrade() {
        if (persSupportFuncService.isSupportResetSelfPwd()) {
            AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("PersPerson");
            if (subMenuItem != null) {
                AuthPermissionItem subButtonItem =
                    new AuthPermissionItem("PersPersonResetSelfPwd", "pers_person_resetSelfPwd",
                        "pers:person:resetSelfPwd", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 7);
                subButtonItem.setParentId(subMenuItem.getId());
                authPermissionService.initData(subButtonItem);
            }
        }
        AuthPermissionItem subMenuItem = authPermissionService.getItemByCode("PersCard");
        if (subMenuItem != null) {
            AuthPermissionItem subButtonItem =
                new AuthPermissionItem("PersCardBatchIssueCard", "pers_batchIssCard_entity", "pers:card:batchIssueCard",
                    AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersCardExport", "common_op_export", "pers:card:export",
                AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 6);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);

        }

        subMenuItem = authPermissionService.getItemByCode("PersCardIssue");
        if (subMenuItem != null) {
            AuthPermissionItem subButtonItem = new AuthPermissionItem("PersCardIssueExport", "common_op_export",
                "pers:issueCard:export", AuthContants.RESOURCE_TYPE_BUTTON, ZKConstant.TRUE, 2);
            subButtonItem.setParentId(subMenuItem.getId());
            authPermissionService.initData(subButtonItem);
        }
        // 启用自助登记
        BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("pers.selfRegistration");
        if (StringUtils.isBlank(baseSysParamItem.getId())) {
            baseSysParamService.initData(new BaseSysParamItem("pers.selfRegistration", "1", "启用自助登记"));

        }
        return true;
    }
}
