/**
 * File Name: PersAttributeServiceImpl Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersAttributeDao;
import com.zkteco.zkbiosecurity.pers.model.PersAttribute;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeExtService;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;

/**
 * 对应百傲瑞达 PersAttributeServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
@Service
@Transactional
public class PersAttributeServiceImpl implements PersAttributeService {
    @Autowired
    private PersAttributeDao persAttributeDao;
    @Autowired
    private PersAttributeExtService persAttributeExtService;

    @Override
    public PersAttributeItem saveItem(PersAttributeItem item) {
        PersAttribute persAttribute = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> persAttributeDao.findById(id)).orElse(new PersAttribute());
        // 变更输入类型
        if (StringUtils.isNotBlank(persAttribute.getId())
            && !(persAttribute.getControlType().equals(item.getControlType()))) {
            // 清空所有人员当前字段的数据
            persAttributeExtService.deleteAttrValueByFiledIndex(persAttribute.getFiledIndex());
        }
        ModelUtil.copyPropertiesIgnoreNull(item, persAttribute);
        persAttributeDao.save(persAttribute);
        item.setId(persAttribute.getId());
        return item;
    }

    @Override
    public PersAttributeItem initData(PersAttributeItem item) {
        PersAttribute attribute = persAttributeDao.findByAttrName(item.getAttrName());
        if (attribute == null) {
            return saveItem(item);
        }
        return null;
    }

    @Override
    public List<PersAttributeItem> getByCondition(PersAttributeItem condition) {
        return (List<PersAttributeItem>)persAttributeDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public List<PersAttributeItem> getItemsByInit() {
        List<PersAttribute> attrList = persAttributeDao.findBySqlStr("init");
        return ModelUtil.copyListProperties(attrList, PersAttributeItem.class);
    }

    @Override
    public List<PersAttributeItem> getItemsByShow() {
        PersAttributeItem codition = new PersAttributeItem();
        // 显示的数据
        codition.setShowTable(true);
        return getByCondition(codition);
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persAttributeDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {

            List<PersAttribute> persAttributeList =
                persAttributeDao.findByIdList((List<String>)CollectionUtil.strToList(ids));
            for (PersAttribute persAttribute : persAttributeList) {
                if ("initHep".equals(persAttribute.getSqlStr())) {
                    throw ZKBusinessException.warnException("common_prompt_initDataCanNotDel");
                }
            }

            for (PersAttribute persAttribute : persAttributeList) {
                persAttributeExtService.deleteAttrValueByFiledIndex(persAttribute.getFiledIndex());
                persAttributeDao.delete(persAttribute);
            }
        }
        return false;
    }

    @Override
    public PersAttributeItem getItemById(String id) {
        List<PersAttributeItem> items = getByCondition(new PersAttributeItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public List<String> findIdsByAttrName(String attrName) {
        return persAttributeDao.findIdsByAttrName(attrName);
    }

    @Override
    public void handlerTransfer(List<PersAttributeItem> attributeItems) {

    }

    @Override
    public List<PersAttributeItem> getItemsByControlType(String controlType) {
        List<PersAttribute> attrList = persAttributeDao.findByControlType("text");
        return ModelUtil.copyListProperties(attrList, PersAttributeItem.class);
    }

    @Override
    public void delHepAttribute() {
        List<PersAttribute> persAttributeList = persAttributeDao.findBySqlStr("initHep");
        for (PersAttribute persAttribute : persAttributeList) {
            persAttributeExtService.deleteAttrValueByFiledIndex(persAttribute.getFiledIndex());
            persAttributeDao.delete(persAttribute);
        }
    }
}