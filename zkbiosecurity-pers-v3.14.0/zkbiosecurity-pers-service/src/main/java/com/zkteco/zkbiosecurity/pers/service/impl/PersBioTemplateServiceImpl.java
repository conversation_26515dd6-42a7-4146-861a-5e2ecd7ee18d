/**
 * File Name: PersBioTemplateServiceImpl Created by GenerationTools on 2018-03-14 上午10:10 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.ProcessBean;
import com.zkteco.zkbiosecurity.base.constants.BaseConstants;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.core.web.cache.ProgressCache;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiBioTemplateItem;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.PersBioPhotoDao;
import com.zkteco.zkbiosecurity.pers.dao.PersBioTemplateDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersBioPhoto;
import com.zkteco.zkbiosecurity.pers.model.PersBioTemplate;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.service.Pers2OtherService;
import com.zkteco.zkbiosecurity.pers.service.PersBioPhotoService;
import com.zkteco.zkbiosecurity.pers.service.PersBioTemplateService;
import com.zkteco.zkbiosecurity.pers.service.PersPersonService;
import com.zkteco.zkbiosecurity.pers.utils.ProcessBeanUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersBioTemplate2OtherItem;
import com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonBioTemplateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 对应百傲瑞达 PersBioTemplateServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-14 上午10:10
 */
@Service
@Transactional
public class PersBioTemplateServiceImpl implements PersBioTemplateService {
    @Autowired
    private PersBioTemplateDao persBioTemplateDao;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersPersonService persPersonService;
    @Autowired
    private ProgressCache progressCache;

    @Autowired
    private AuthDepartmentService authDepartmentService;
    @Autowired
    private Pers2OtherService pers2OtherService;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private PersBioPhotoDao persBioPhotoDao;
    @Autowired
    private PersBioPhotoService persBioPhotoService;
    /**
     * 分割符常量,本类中使用
     */
    private static String split = "_";

    @Override
    public PersBioTemplateItem saveItem(PersBioTemplateItem item) {
        PersBioTemplate persBioTemplate = Optional.ofNullable(item).map(i -> i.getId()).filter(StringUtils::isNotBlank)
            .flatMap(id -> persBioTemplateDao.findById(id)).orElse(new PersBioTemplate());
        ModelUtil.copyPropertiesIgnoreNull(item, persBioTemplate);
        persBioTemplateDao.save(persBioTemplate);
        item.setId(persBioTemplate.getId());
        return item;
    }

    @Override
    public void saveBioTemplateJson(String personId, String bioTemplateJson) {
        if (StringUtils.isNotBlank(bioTemplateJson)) {
            PersPerson person = persPersonDao.findById(personId).orElse(null);
            List<PersBioTemplate> bioTemplateList = persBioTemplateDao.findByPersonId(personId);
            Map<Short, List<PersBioTemplate>> bioTemplateListMap =
                bioTemplateList.stream().collect(Collectors.groupingBy(PersBioTemplate::getBioType));
            Map<String, Object> bioTemplateMap = JSON.parseObject(bioTemplateJson);

            if (bioTemplateMap.containsKey("fpList")) {
                Map<String, PersBioTemplateItem> fpList = JSON.parseObject(bioTemplateMap.get("fpList").toString(),
                    new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.FP_BIO_TYPE,
                    BaseConstants.BaseBioType.FP_BIO_VERSION, bioTemplateListMap, fpList, false);
            }

            if (bioTemplateMap.containsKey("fvList")) {
                Map<String, PersBioTemplateItem> fvList = JSON.parseObject(bioTemplateMap.get("fvList").toString(),
                    new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.VEIN_BIO_TYPE,
                    BaseConstants.BaseBioType.VEIN_BIO_VERSION, bioTemplateListMap, fvList, false);
            }
            if (bioTemplateMap.containsKey("faceList")) {
                Map<String, PersBioTemplateItem> faceList = JSON.parseObject(bioTemplateMap.get("faceList").toString(),
                    new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.FACE_BIO_TYPE,
                    BaseConstants.BaseBioType.FACE_BIO_VERSION, bioTemplateListMap, faceList, false);
            }
            if (bioTemplateMap.containsKey("palmList")) {
                Map<String, PersBioTemplateItem> palmList = JSON.parseObject(bioTemplateMap.get("palmList").toString(),
                    new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.PALM_BIO_TYPE,
                    BaseConstants.BaseBioType.PALM_BIO_VERSION, bioTemplateListMap, palmList, false);
            }
            if (bioTemplateMap.containsKey("vislightList")) {
                Map<String, PersBioTemplateItem> vislightList =
                    JSON.parseObject(bioTemplateMap.get("vislightList").toString(),
                        new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE,
                    BaseConstants.BaseBioType.BIOPHOTO_BIO_VERSION, bioTemplateListMap, vislightList, false);
            }
            if (bioTemplateMap.containsKey("vislightPalmList")) {
                Map<String, PersBioTemplateItem> vislightPalmList =
                    JSON.parseObject(bioTemplateMap.get("vislightPalmList").toString(),
                        new TypeReference<Map<String, PersBioTemplateItem>>() {});
                Set<String> keySet = vislightPalmList.keySet();
                String version = "";
                if (!keySet.isEmpty()) {
                    String key = keySet.iterator().next();
                    version = vislightPalmList.get(key).getVersion();
                }
                saveBioTemplate(personId, person.getPin(), PersConstants.PALM_BIO_TYPE_10, version, bioTemplateListMap,
                    vislightPalmList, false);
            }
            if (bioTemplateMap.containsKey("irisList")) {
                Map<String, PersBioTemplateItem> irisList = JSON.parseObject(bioTemplateMap.get("irisList").toString(),
                    new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), PersConstants.IRIS_BIO_TYPE, PersConstants.IRIS_BIO_VERSION,
                    bioTemplateListMap, irisList, false);
            }
        }
    }

    @Override
    public void updateBioTemplateJson(String personId, String bioTemplateJson) {
        if (StringUtils.isNotBlank(bioTemplateJson)) {
            PersPerson person = persPersonDao.findById(personId).orElse(null);
            List<PersBioTemplate> bioTemplateList = persBioTemplateDao.findByPersonId(personId);
            Map<Short, List<PersBioTemplate>> bioTemplateListMap =
                bioTemplateList.stream().collect(Collectors.groupingBy(PersBioTemplate::getBioType));
            Map<String, Object> bioTemplateMap = JSON.parseObject(bioTemplateJson);
            // 指纹
            if (bioTemplateMap.containsKey("fpList")) {
                Map<String, PersBioTemplateItem> fpList = JSON.parseObject(bioTemplateMap.get("fpList").toString(),
                    new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.FP_BIO_TYPE,
                    BaseConstants.BaseBioType.FP_BIO_VERSION, bioTemplateListMap, fpList, true);
            }
            // 指静脉
            if (bioTemplateMap.containsKey("fvList")) {
                Map<String, PersBioTemplateItem> fvList = JSON.parseObject(bioTemplateMap.get("fvList").toString(),
                    new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.VEIN_BIO_TYPE,
                    BaseConstants.BaseBioType.VEIN_BIO_VERSION, bioTemplateListMap, fvList, true);
            }
            // 人脸
            if (bioTemplateMap.containsKey("faceList")) {
                Map<String, PersBioTemplateItem> faceList = JSON.parseObject(bioTemplateMap.get("faceList").toString(),
                    new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.FACE_BIO_TYPE,
                    BaseConstants.BaseBioType.FACE_BIO_VERSION, bioTemplateListMap, faceList, true);
            }
            // 掌静脉
            if (bioTemplateMap.containsKey("palmList")) {
                Map<String, PersBioTemplateItem> palmList = JSON.parseObject(bioTemplateMap.get("palmList").toString(),
                    new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.PALM_BIO_TYPE,
                    BaseConstants.BaseBioType.PALM_BIO_VERSION, bioTemplateListMap, palmList, true);
            }
            // 可见光
            if (bioTemplateMap.containsKey("vislightList")) {
                Map<String, PersBioTemplateItem> vislightList =
                    JSON.parseObject(bioTemplateMap.get("vislightList").toString(),
                        new TypeReference<Map<String, PersBioTemplateItem>>() {});
                saveBioTemplate(personId, person.getPin(), BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE,
                    BaseConstants.BaseBioType.BIOPHOTO_BIO_VERSION, bioTemplateListMap, vislightList, true);
            }
        }
    }

    private void saveBioTemplate(String personId, String personPin, Short bioType, String defVersion,
        Map<Short, List<PersBioTemplate>> bioTemplateListMap, Map<String, PersBioTemplateItem> bioTemplateItemMap,
        boolean isUpdate) {
        List<PersBioTemplate> bioTemplateList = bioTemplateListMap.get(bioType);
        Map<String, PersBioTemplate> bioTemplateMap = CollectionUtil.listToKeyMap(bioTemplateList,
            p -> p.getTemplateNo() + split + p.getTemplateNoIndex() + split + p.getVersion());
        bioTemplateItemMap.forEach((templateNoAndIndex, bioTemplateItem) -> {
            PersBioTemplate bioTemplate = null;
            // 没有版本，用默认的版本
            if (StringUtils.isBlank(bioTemplateItem.getVersion())) {
                bioTemplate = bioTemplateMap.remove(bioTemplateItem.getTemplateNo() + split
                    + bioTemplateItem.getTemplateNoIndex() + split + defVersion);
            } else {
                bioTemplate = bioTemplateMap.remove(bioTemplateItem.getTemplateNo() + split
                    + bioTemplateItem.getTemplateNoIndex() + split + bioTemplateItem.getVersion());
            }
            // 新增
            if (Objects.isNull(bioTemplate)) {
                bioTemplate = ModelUtil.copyProperties(bioTemplateItem, new PersBioTemplate());
                bioTemplate.setPersonId(personId);
                bioTemplate.setPersonPin(personPin);
                bioTemplate.setValidType(BaseConstants.BaseBioType.VALID_FLAG_ENABLE);
                if (StringUtils.isBlank(bioTemplate.getVersion())) {
                    bioTemplate.setVersion(defVersion);
                }
                if (Objects.isNull(bioTemplate.getDuress())) {
                    bioTemplate.setDuress(false);
                }
                bioTemplate.setBioType(bioType);
                persBioTemplateDao.save(bioTemplate);
            } else {
                // 编辑修改
                if (!bioTemplate.getTemplate().equals(bioTemplateItem.getTemplate())
                    || (Objects.nonNull(bioTemplateItem.getDuress())
                        && bioTemplate.getDuress().booleanValue() != bioTemplateItem.getDuress())) {
                    bioTemplate.setTemplate(bioTemplateItem.getTemplate());
                    if (Objects.nonNull(bioTemplateItem.getDuress())) {
                        bioTemplate.setDuress(bioTemplateItem.getDuress());
                    }
                    persBioTemplateDao.save(bioTemplate);
                }
            }
        });
        // 非更新，并且还有多余的数据需要进行删除
        if (!isUpdate && !bioTemplateMap.isEmpty()) {
            bioTemplateMap.forEach((k, v) -> {
                // 数量为0，即不存在多个版本的生物模板数据，删除该旧的数据
                if (bioTemplateItemMap.keySet().stream().filter(templateNoAndIndex -> k.startsWith(templateNoAndIndex))
                    .count() == 0) {
                    persBioTemplateDao.delete(v);
                }
            });
        }
    }

    @Override
    public List<PersBioTemplateItem> getByCondition(PersBioTemplateItem condition) {
        return (List<PersBioTemplateItem>)persBioTemplateDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persBioTemplateDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                persBioTemplateDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public PersBioTemplateItem getItemById(String id) {
        List<PersBioTemplateItem> items = getByCondition(new PersBioTemplateItem(id));
        return Optional.ofNullable(items).filter(list -> !list.isEmpty()).map(list -> list.get(0)).orElse(null);
    }

    @Override
    public List<PersBioTemplateItem> getItemByPersonId(String personId) {
        return Optional.ofNullable(personId).filter(StringUtils::isNotBlank).map(persBioTemplateDao::findByPersonId)
            .map(list -> ModelUtil.copyListProperties(list, PersBioTemplateItem.class)).orElse(Collections.emptyList());
    }

    @Override
    public List<PersBioTemplateItem> getItemByPersonIds(String personIds) {
        return Optional.ofNullable(personIds).map(CollectionUtil::strToList).filter(i -> !i.isEmpty())
            .map(persBioTemplateDao::findByPersonIdIn)
            .map(list -> ModelUtil.copyListProperties(list, PersBioTemplateItem.class)).orElse(Collections.emptyList());
    }

    @Override
    public List<PersBioTemplateItem> getItemByPersonIdList(List<String> personIds) {
        return Optional.ofNullable(personIds).filter(i -> !i.isEmpty()).map(persBioTemplateDao::findByPersonIdIn)
            .map(list -> ModelUtil.copyListProperties(list, PersBioTemplateItem.class)).orElse(Collections.emptyList());
    }

    @Override
    public Map<String, Object> getItemMapByPersonId(String personId) {
        Map<String, Object> map = new HashMap<>();
        PersPerson persPerson = persPersonDao.getOne(personId);
        List<PersBioTemplateItem> bioTemplateItems = getItemByPersonId(personId);
        Map<String, PersPersonBioTemplateItem> fpList = new HashMap<>();
        Map<String, PersPersonBioTemplateItem> fvList = new HashMap<>();
        Map<String, PersPersonBioTemplateItem> fcList = new HashMap<>();
        Map<String, PersPersonBioTemplateItem> palmList = new HashMap<>();
        Map<String, PersPersonBioTemplateItem> vislightList = new HashMap<>();
        Map<String, PersPersonBioTemplateItem> vislightPalmList = new HashMap<>();
        Map<String, PersPersonBioTemplateItem> irisList = new HashMap<>();
        if (!bioTemplateItems.isEmpty()) {
            for (PersBioTemplateItem bioTemplate : bioTemplateItems) {
                // 判断是否是指纹
                if (BaseConstants.BaseBioType.FP_BIO_TYPE.equals(bioTemplate.getBioType())) {
                    // 10版本和12版本同时存在的情况下，只需要展示一个版本即可
                    fpList.put(
                        bioTemplate.getTemplateNo() + split + bioTemplate.getTemplateNoIndex() + split
                            + bioTemplate.getVersion(),
                        ModelUtil.copyProperties(bioTemplate, new PersPersonBioTemplateItem()));
                }
                // 判断是否指静脉，获取指静脉数
                else if (BaseConstants.BaseBioType.VEIN_BIO_TYPE.equals(bioTemplate.getBioType())) {
                    fvList.put(
                        bioTemplate.getTemplateNo() + split + bioTemplate.getTemplateNoIndex() + split
                            + bioTemplate.getVersion(),
                        ModelUtil.copyProperties(bioTemplate, new PersPersonBioTemplateItem()));
                }
                // 判断是否面部，获取面部数
                else if (BaseConstants.BaseBioType.FACE_BIO_TYPE.equals(bioTemplate.getBioType())) {
                    fcList.put(
                        bioTemplate.getTemplateNo() + split + bioTemplate.getTemplateNoIndex() + split
                            + bioTemplate.getVersion(),
                        ModelUtil.copyProperties(bioTemplate, new PersPersonBioTemplateItem()));
                }
                // 判断是否掌静脉，获取掌静脉数
                else if (BaseConstants.BaseBioType.PALM_BIO_TYPE.equals(bioTemplate.getBioType())
                    || PersConstants.PV_BIO_TYPE.equals(bioTemplate.getBioType())) {
                    palmList.put(
                        bioTemplate.getTemplateNo() + split + bioTemplate.getTemplateNoIndex() + split
                            + bioTemplate.getVersion(),
                        ModelUtil.copyProperties(bioTemplate, new PersPersonBioTemplateItem()));
                } else if (PersConstants.PALM_BIO_TYPE_10.equals(bioTemplate.getBioType())) {
                    vislightPalmList.put(
                        bioTemplate.getTemplateNo() + split + bioTemplate.getTemplateNoIndex() + split
                            + bioTemplate.getVersion(),
                        ModelUtil.copyProperties(bioTemplate, new PersPersonBioTemplateItem()));
                } else if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.equals(bioTemplate.getBioType())) {
                    vislightList.put(
                        bioTemplate.getTemplateNo() + split + bioTemplate.getTemplateNoIndex() + split
                            + bioTemplate.getVersion(),
                        ModelUtil.copyProperties(bioTemplate, new PersPersonBioTemplateItem()));
                } else if (PersConstants.IRIS_BIO_TYPE.equals(bioTemplate.getBioType())) {
                    irisList.put(
                        bioTemplate.getTemplateNo() + split + bioTemplate.getTemplateNoIndex() + split
                            + bioTemplate.getVersion(),
                        ModelUtil.copyProperties(bioTemplate, new PersPersonBioTemplateItem()));
                }
            }
        }
        List<String> vislightPhotoList = new ArrayList<>();
        List<String> palmPhotoList = new ArrayList<>();
        List<PersBioPhoto> bioPhotoList = persBioPhotoDao.findByPersonId(personId);
        if (bioPhotoList != null && !bioPhotoList.isEmpty()) {
            for (PersBioPhoto bioPhoto : bioPhotoList) {
                String photoBase64 = FileEncryptUtil.getDecryptFileBase64(bioPhoto.getPhotoPath());
                if (PersConstants.PALM_BIO_TYPE_10.equals(bioPhoto.getBioType())) {
                    palmPhotoList.add(PersConstants.PERSON_BASE64_PREFIX + photoBase64);
                } else {
                    vislightPhotoList.add(PersConstants.PERSON_BASE64_PREFIX + photoBase64);
                }
            }
        }
        map.put("fpList", fpList);
        map.put("fvList", fvList);
        map.put("faceList", fcList);
        map.put("palmList", palmList);
        map.put("vislightPalmList", vislightPalmList);
        map.put("vislightList", vislightList);
        map.put("vislightPhotoList", vislightPhotoList);
        map.put("palmPhotoList", palmPhotoList);
        map.put("irisList", irisList);
        return map;
    }

    @Override
    public Map<String, Map<Short, Integer>> getBioTemplateCountByPersonIdList(Collection<String> personIds) {
        List<Object[]> bioTemplateCount = persBioTemplateDao.countByBioTypeAndTemplateNoAndPersonIdIn(personIds,
            PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
        return bioTemplateCount.stream()
            .collect(Collectors.groupingBy((Object[] o) -> o[0].toString(),
                Collectors.toMap((Object[] o) -> Short.valueOf(o[1].toString()),
                    (Object[] o) -> Integer.valueOf(o[2].toString()), (x, y) -> y)));
    }

    @Override
    public Map<Short, Integer> countBioTemplateCountByPersonIdList(Collection<String> personIds) {
        List<Object[]> bioTemplateCount = persBioTemplateDao.countByTemplateNoAndPersonIdsAndGroupByBioType(personIds,
            PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
        return bioTemplateCount.stream().collect(Collectors.toMap((Object[] o) -> Short.valueOf(o[0].toString()),
            (Object[] o) -> Integer.valueOf(o[1].toString()), (x, y) -> y));
    }

    @Override
    public ZKResultMsg importData(List<PersBioTemplateItem> itemList) {
        Iterator<PersBioTemplateItem> iterator = itemList.iterator();
        int beginProgress = 20, importSize = itemList.size();
        Set<String> pinSets = new HashSet<>(importSize);
        while (iterator.hasNext()) {
            PersBioTemplateItem bioTemplateItem = iterator.next();
            if (StringUtils.isBlank(bioTemplateItem.getPersonPin())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_import_pinNotEmpty")));
                iterator.remove();
                continue;
            }
            // 新增了生物模板类型编号，兼容以前版本的excel
            if (Objects.isNull(bioTemplateItem.getBioType())) {
                if (!Objects.isNull(bioTemplateItem.getBioTypeVal())) {
                    bioTemplateItem.setBioType(bioTemplateItem.getBioTypeVal());
                }
            }
            if (Objects.isNull(bioTemplateItem.getBioType())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_person_templateBioTypeNoNull", bioTemplateItem.getPersonPin())));
                iterator.remove();
                continue;
            }
            if (StringUtils.isBlank(bioTemplateItem.getVersion())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_person_templateVersionNoNull", bioTemplateItem.getPersonPin())));
                iterator.remove();
                continue;
            }
            if (Objects.isNull(bioTemplateItem.getTemplateNo())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_person_templateNoNoNull", bioTemplateItem.getPersonPin())));
                iterator.remove();
                continue;
            }
            if (Objects.isNull(bioTemplateItem.getTemplateNoIndex())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_person_templateNoIndexNoNull", bioTemplateItem.getPersonPin())));
                iterator.remove();
                continue;
            }
            if (StringUtils.isBlank(bioTemplateItem.getTemplate())) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_person_templateNoNull", bioTemplateItem.getPersonPin())));
                iterator.remove();
                continue;
            }
            double d = bioTemplateItem.getTemplate().trim().length() * 0.75;
            if (100 * (int)d - 100 * d < 0) {
                progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress,
                    I18nUtil.i18nCode("pers_person_templateError", bioTemplateItem.getPersonPin())));
                iterator.remove();
                continue;
            }
            pinSets.add(bioTemplateItem.getPersonPin());
        }

        Map<String, String> pinsAndIdsMap = new HashMap<>();
        List<List<String>> pinLists = CollectionUtil.split(pinSets, CollectionUtil.splitSize);
        for (List<String> pins : pinLists) {
            pinsAndIdsMap.putAll(persPersonService.getPinsAndIdsByPins(pins));
        }
        iterator = itemList.iterator();
        Set<String> removePins = new HashSet<>();
        while (iterator.hasNext()) {
            PersBioTemplateItem bioTemplateItem = iterator.next();
            if (!pinsAndIdsMap.containsKey(bioTemplateItem.getPersonPin())) {
                removePins.add(bioTemplateItem.getPersonPin());
                iterator.remove();
            }
        }
        // 通过人员编号找不到对应的人员
        if (!CollectionUtil.isEmpty(removePins)) {
            progressCache.setProcess(ProcessBeanUtil.createErrorSingleProcess(beginProgress + 10,
                I18nUtil.i18nCode("pers_person_pinError", StringUtils.join(removePins, ","))));
        }

        // 失败数量
        int faildCount = importSize - itemList.size();
        // 已存在数量
        AtomicInteger existCount = new AtomicInteger(0);

        if (!CollectionUtil.isEmpty(itemList)) {
            // 按人员编号分组， key：pin value:该人员的所有生物模板
            Map<String, List<PersBioTemplateItem>> bioTemplateMap =
                itemList.stream().collect(Collectors.groupingBy(PersBioTemplateItem::getPersonPin));
            // 优先处理有生物模板的人员
            List<List<String>> pinsList = CollectionUtil.split(pinsAndIdsMap.keySet(), CollectionUtil.splitSize);
            float avgProgress = (float)60 / pinsList.size();
            int i = 0;
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(beginProgress + 70,
                I18nUtil.i18nCode("common_op_processing")));
            for (List<String> pins : pinsList) {
                int progress = beginProgress + 10 + (int)(avgProgress * i++);
                progressCache.setProcess(new ProcessBean(progress, progress, ""));

                // 从数据库查找pins的生物模板
                List<PersBioTemplate> existsBioTemplates = persBioTemplateDao.findByPersonPinIn(pins);
                // 按人员编号分组， key：pin value:该人员的所有生物模板
                Map<String, List<PersBioTemplate>> existsBioTemplateMap =
                    existsBioTemplates.stream().collect(Collectors.groupingBy(PersBioTemplate::getPersonPin));
                pins.forEach(pin -> {
                    String personId = pinsAndIdsMap.get(pin);
                    // 人员生物模板重复检测用的
                    Set<String> uniqueKey = new HashSet<>();
                    // 待入库的人员生物模板列表
                    List<PersBioTemplateItem> bioTemplateItemList = bioTemplateMap.remove(pin);
                    //
                    if (existsBioTemplateMap.containsKey(pin)) {
                        // 已存在的人员生物模版列表
                        List<PersBioTemplate> bioTemplateList = existsBioTemplateMap.remove(pin);
                        // key : bioType_templateNo_templateNoIndex_version
                        Map<String,
                            PersBioTemplate> templateMap = bioTemplateList.stream()
                                .collect(Collectors.toMap(
                                    bioTemplate -> bioTemplate.getBioType() + split + bioTemplate.getTemplateNo()
                                        + split + bioTemplate.getTemplateNoIndex() + split + bioTemplate.getVersion(),
                                    Function.identity(), (x, y) -> y));
                        bioTemplateItemList.forEach(bioTemplateItem -> {
                            PersBioTemplate persBioTemplate = templateMap
                                .remove(bioTemplateItem.getBioType() + split + bioTemplateItem.getTemplateNo() + split
                                    + bioTemplateItem.getTemplateNoIndex() + split + bioTemplateItem.getVersion());
                            // 非空即数据存在，做更新
                            if (Objects.nonNull(persBioTemplate)) {
                                existCount.getAndIncrement();
                                if (!persBioTemplate.getTemplate().equals(bioTemplateItem.getTemplate())
                                    || (Objects.nonNull(bioTemplateItem.getDuress())
                                        && persBioTemplate.getDuress().booleanValue() != bioTemplateItem.getDuress())) {
                                    persBioTemplate.setTemplate(bioTemplateItem.getTemplate());
                                    if (Objects.nonNull(bioTemplateItem.getDuress())) {
                                        persBioTemplate.setDuress(bioTemplateItem.getDuress());
                                    }
                                    persBioTemplateDao.save(persBioTemplate);
                                }
                            } else {
                                if (uniqueKey.add(bioTemplateItem.getBioType() + split + bioTemplateItem.getTemplateNo()
                                    + split + bioTemplateItem.getTemplateNoIndex() + split
                                    + bioTemplateItem.getVersion())) {
                                    persBioTemplate = ModelUtil.copyProperties(bioTemplateItem, new PersBioTemplate());
                                    persBioTemplate.setPersonId(personId);
                                    persBioTemplate.setPersonPin(pin);
                                    if (Objects.isNull(persBioTemplate.getDuress())) {
                                        persBioTemplate.setDuress(false);
                                    }
                                    persBioTemplateDao.save(persBioTemplate);
                                } else {
                                    progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(progress,
                                        I18nUtil.i18nCode("pers_import_templateIsRepeat", pin)));
                                }
                            }
                        });
                    } else {
                        bioTemplateItemList.forEach(bioTemplateItem -> {
                            // key : bioType_templateNo_templateNoIndex_version
                            if (uniqueKey
                                .add(bioTemplateItem.getBioType() + split + bioTemplateItem.getTemplateNo() + split
                                    + bioTemplateItem.getTemplateNoIndex() + split + bioTemplateItem.getVersion())) {
                                PersBioTemplate persBioTemplate =
                                    ModelUtil.copyProperties(bioTemplateItem, new PersBioTemplate());
                                persBioTemplate.setPersonId(personId);
                                persBioTemplate.setPersonPin(pin);
                                if (Objects.isNull(persBioTemplate.getDuress())) {
                                    persBioTemplate.setDuress(false);
                                }
                                if (Objects.isNull(persBioTemplate.getValidType())) {
                                    persBioTemplate.setValidType(BaseConstants.BaseBioType.VALID_FLAG_ENABLE);
                                }
                                persBioTemplateDao.save(persBioTemplate);
                            } else {
                                progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(progress,
                                    I18nUtil.i18nCode("pers_import_templateIsRepeat", pin)));
                            }
                        });
                    }
                });
            }
        }

        int saveSize = importSize - faildCount;
        if (existCount.get() > 0) {
            // 成功：%s 条，更新：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result3", saveSize, existCount.get(), faildCount)));
        } else {
            // 成功：%s 条，失败：%s 条。
            progressCache.setProcess(ProcessBeanUtil.createNormalSingleProcess(99,
                I18nUtil.i18nCode("pers_import_result", saveSize, faildCount)));
        }
        if (faildCount > 0) {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public List<?> getItemData(PersBioTemplateItem condition, String sessionId, int beginIndex, int endIndex) {
        // 封装部门条件
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            condition.setUserId(userId);
        }
        if (StringUtils.isBlank(condition.getInDeptId())) {
            if (StringUtils.isNotBlank(condition.getDeptId())) {
                List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(condition.getDeptId());
                condition.setDeptId("");
                condition.setInDeptId(StrUtil.collectionToStr(deptIdList));
            }
        }
        return persBioTemplateDao.getItemsDataBySql(condition.getClass(), buildVerifyModeSqlByItem(condition),
            beginIndex, endIndex, true);
    }

    /**
     * 封装验证方式查询语句
     *
     * @param item:
     * @return java.lang.String
     * <AUTHOR>
     * @throws @date 2020-12-01 18:44
     * @since 1.0.0
     */
    private String buildVerifyModeSqlByItem(PersBioTemplateItem item) {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(SQLUtil.getSqlByItem(item));
        int orderByIndex = stringBuilder.indexOf("ORDER BY");
        String verifyMode = item.getVerifyMode();
        if (StringUtils.isNotBlank(verifyMode)) {
            List<String> verifyModes = new ArrayList<>();
            List<String> verifyModeReverses = new ArrayList<>();
            for (String str : verifyMode.split(",")) {
                if (str.contains("N")) {
                    verifyModeReverses.add(str.substring(str.indexOf("N") + 1));
                } else {
                    verifyModes.add(str);
                }
            }
            // 包含查询
            StringBuilder contain = new StringBuilder();
            if (verifyModes.contains(PersConstants.VERIFY_MODE_CARD)) {
                verifyModes.remove(PersConstants.VERIFY_MODE_CARD);
                contain.append(" t.PERSON_ID IN(SELECT c.PERSON_ID FROM PERS_CARD c GROUP BY c.PERSON_ID)");
            }
            if (verifyModes.contains(PersConstants.VERIFY_MODE_CROPFACE)) {
                verifyModes.remove(PersConstants.VERIFY_MODE_CROPFACE);
                if (contain.length() > 0) {
                    contain.append(" OR");
                }
                contain.append(" t.PERSON_ID IN(SELECT p.PERSON_ID FROM PERS_BIOPHOTO p GROUP BY p.PERSON_ID)");
            }

            if (!verifyModes.isEmpty()) {
                if (contain.length() > 0) {
                    contain.append(" OR");
                }
                contain.append(" t.PERSON_ID IN (SELECT b.PERSON_ID FROM PERS_BIOTEMPLATE b WHERE b.BIO_TYPE IN ("
                    + StringUtils.join(verifyModes, ",") + ") GROUP BY b.PERSON_ID)");
            }
            if (contain.length() > 0) {
                stringBuilder.insert(orderByIndex, " AND (" + contain + ")");
            }

            // 不包含查询
            StringBuilder noContain = new StringBuilder();
            if (verifyModeReverses.contains(PersConstants.VERIFY_MODE_CARD)) {
                verifyModeReverses.remove(PersConstants.VERIFY_MODE_CARD);
                noContain.append(" t.PERSON_ID NOT IN(SELECT c.PERSON_ID FROM PERS_CARD c GROUP BY c.PERSON_ID)");
            }
            if (verifyModeReverses.contains(PersConstants.VERIFY_MODE_CROPFACE)) {
                verifyModeReverses.remove(PersConstants.VERIFY_MODE_CROPFACE);
                if (noContain.length() > 0) {
                    noContain.append(" AND");
                }
                noContain.append(" t.PERSON_ID NOT IN(SELECT p.PERSON_ID FROM PERS_BIOPHOTO p GROUP BY p.PERSON_ID)");
            }
            if (!verifyModeReverses.isEmpty()) {
                if (noContain.length() > 0) {
                    noContain.append(" AND");
                }
                noContain.append(" t.PERSON_ID NOT IN(SELECT b.PERSON_ID FROM PERS_BIOTEMPLATE b WHERE b.BIO_TYPE IN ("
                    + StringUtils.join(verifyModeReverses, ",") + ") GROUP BY b.PERSON_ID)");
            }
            if (noContain.length() > 0) {
                stringBuilder.insert(orderByIndex, " AND (" + noContain + ")");
            }
        }
        return stringBuilder.toString();
    }

    @Override
    public void initUpgradeData() {
        // 旧的人脸数量会错误更新过来
        List<PersBioTemplate> templates = persBioTemplateDao.findByBioTypeAndTemplateNoNot(
            BaseConstants.BaseBioType.FACE_BIO_TYPE, PersConstants.PERS_BIOTEMPLATE_DEF_NO);
        if (!CollectionUtil.isEmpty(templates)) {
            templates.forEach(template -> {
                template.setTemplateNo(PersConstants.PERS_BIOTEMPLATE_DEF_NO);
                persBioTemplateDao.save(template);
            });
        }
        // 旧的指纹数据会错误，需要更新过来
        templates = persBioTemplateDao.findByBioTypeAndTemplateNoIndexNot(BaseConstants.BaseBioType.FP_BIO_TYPE,
            PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
        if (!CollectionUtil.isEmpty(templates)) {
            templates.forEach(template -> {
                template.setTemplateNoIndex(PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);
                persBioTemplateDao.save(template);
            });
        }

        // 旧的指纹数据没有人员编号
        templates = persBioTemplateDao.findByPersonPinIsNull();
        if (!CollectionUtil.isEmpty(templates)) {
            Collection<String> personIdAllList =
                CollectionUtil.getPropertyList(templates, PersBioTemplate::getPersonId, "-1");
            Map<String, List<PersBioTemplate>> personBioTemplateMap =
                templates.stream().collect(Collectors.groupingBy(PersBioTemplate::getPersonId));
            List<List<String>> personIdSplitList = CollectionUtil.split(personIdAllList, CollectionUtil.splitSize);
            personIdSplitList.forEach(personIdList -> {
                List<PersPerson> persPersonList = persPersonDao.findByIdIn(personIdList);
                persPersonList.forEach(persPerson -> {
                    List<PersBioTemplate> persBioTemplates = personBioTemplateMap.remove(persPerson.getId());
                    persBioTemplates.forEach(template -> {
                        template.setPersonPin(persPerson.getPin());
                        persBioTemplateDao.save(template);
                    });
                });
            });
        }
    }

    @Override
    public void handlerTransfer(List<PersBioTemplateItem> bioTemplateItems) {
        // 数据量大的时候处理，分批处理
        List<List<PersBioTemplateItem>> tempalteItemList =
            CollectionUtil.split(bioTemplateItems, CollectionUtil.splitSize);
        // 数据量大的时候处理，分批处理
        // 按人员编号分组， key：pin value:该人员的所有生物模板
        Map<String, List<PersBioTemplateItem>> bioTemplateMap =
            bioTemplateItems.stream().collect(Collectors.groupingBy(PersBioTemplateItem::getPersonPin));
        // 获取数据库中原有的指纹模版，用于比较
        List<List<String>> pinsList = CollectionUtil.split(bioTemplateMap.keySet(), CollectionUtil.splitSize);
        Map<String, PersBioTemplate> templateMap = new HashMap<String, PersBioTemplate>();
        for (List<String> pins : pinsList) {
            List<PersBioTemplate> bioTemplates = persBioTemplateDao.findByPersonPinIn(pins);
            for (PersBioTemplate bioTemplate : bioTemplates) {
                String key = bioTemplate.getPersonPin() + split + bioTemplate.getBioType() + split
                    + bioTemplate.getTemplateNo() + split + bioTemplate.getTemplateNoIndex() + split
                    + bioTemplate.getVersion() + split + bioTemplate.getDuress();
                templateMap.put(key, bioTemplate);
            }
        }
        // 检查已存在的人员模版后保存
        for (List<PersBioTemplateItem> items : tempalteItemList) {
            for (PersBioTemplateItem item : items) {
                String key = item.getPersonPin() + split + item.getBioType() + split + item.getTemplateNo() + split
                    + item.getTemplateNoIndex() + split + item.getVersion() + split + item.getDuress();
                PersBioTemplate template = templateMap.remove(key);
                if (Objects.isNull(template)) {
                    template = new PersBioTemplate();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(item, template, "id");
                PersPerson person = persPersonDao.findByPin(item.getPersonPin());
                template.setPersonId(person.getId());
                template.setPersonPin(person.getPin());
                persBioTemplateDao.save(template);
            }
        }
        bioTemplateMap = null;
        pinsList = null;
        templateMap = null;
    }

    @Override
    public int deleteBioTemplateByPinAndTemplateNo(String pin, Short templateNo) {
        List<PersBioTemplate> persBioTemplates = persBioTemplateDao.findByPersonPinAndTemplateNo(pin, templateNo);
        int count = 0;
        if (!persBioTemplates.isEmpty()) {
            persBioTemplateDao.delete(persBioTemplates);
            count++;
        }
        return count;
    }

    @Override
    public int addBioTemplate(PersApiBioTemplateItem apiBioTemplate) {
        int totalCount = 0;
        String pin = apiBioTemplate.getPin();
        if (StringUtils.isNotBlank(pin)) {
            if (StringUtils.isBlank(apiBioTemplate.getTemplate())) {
                return PersConstants.PERSON_TEMPLATE_ISNULL;
            }
            PersPersonItem persPerson = persPersonService.getItemByPin(pin);
            if (persPerson != null) {
                // 生物特征模板编号
                List<String> templateNos = StrUtil.strToList(apiBioTemplate.getTemplateNo());
                // 生物特征模板内容
                List<String> templates = StrUtil.strToList(apiBioTemplate.getTemplate());
                List<String> versions = new ArrayList<String>();
                if (StringUtils.isNotBlank(apiBioTemplate.getVersion())) {
                    versions = StrUtil.strToList(apiBioTemplate.getVersion());
                }
                List<Short> tempTemplateNos = new ArrayList<Short>();
                Map<String, String> templateMap = new HashMap<String, String>();
                String version = null;
                for (int i = 0; i < templateNos.size(); i++) {
                    if (Short.parseShort(templateNos.get(i)) > 9 || Short.parseShort(templateNos.get(i)) < 0) {
                        templates.remove(i);
                        continue;
                    }
                    if (versions.size() > i) {
                        version = versions.get(i);
                    } else {
                        version = "10";
                    }
                    templateMap.put(templateNos.get(i) + split + version, templates.get(i));
                    tempTemplateNos.add(Short.valueOf(templateNos.get(i)));
                }
                if (templateNos.size() > 0 && tempTemplateNos.size() == 0) {
                    // 所有的templateNo 不在0-9
                    return PersConstants.TEMPLATENO_NO_RANGE_0_9;
                }
                List<PersBioTemplate> persBioTemplateList =
                    persBioTemplateDao.findByPersonPinAndTemplateNoIn(pin, tempTemplateNos);
                Map<String, PersBioTemplate> persBioTemplateMap =
                    CollectionUtil.listToKeyMap(persBioTemplateList, p -> p.getTemplateNo() + split + p.getVersion());

                // if (persBioTemplateList.size() == tempTemplateNos.size()) {
                // // 表示该人的该指纹已经录入进去了
                // return PersConstants.PERSONID_FINGER_EXIST;
                // }
                List<PersBioTemplate2OtherItem> bioTemplate2OtherItems = new ArrayList<>();
                for (Map.Entry<String, String> entry : templateMap.entrySet()) {
                    String key = entry.getKey();
                    String[] keys = key.split(split);
                    PersBioTemplateItem persBioTemplate = new PersBioTemplateItem();
                    if (persBioTemplateMap.containsKey(entry.getKey())) {
                        persBioTemplate =
                            ModelUtil.copyProperties(persBioTemplateMap.get(entry.getKey()), persBioTemplate);
                    } else {
                        persBioTemplate.setBioType((short)1);
                        persBioTemplate.setTemplateNo(Short.parseShort(keys[0]));
                        persBioTemplate.setPersonId(persPerson.getId());
                        persBioTemplate.setPersonPin(persPerson.getPin());
                        persBioTemplate.setTemplateNoIndex((short)0);
                        if (StringUtils.isNotBlank(keys[1])) {
                            if (!(Pattern.compile("[0-9]*").matcher(keys[1]).matches())) {
                                return PersConstants.PERSON_VERSION_ISNOTNUMBER;
                            }
                            persBioTemplate.setVersion(keys[1]);
                        } else {
                            persBioTemplate.setVersion("10");
                        }
                    }
                    persBioTemplate.setTemplate(entry.getValue());
                    if ("1".equals(apiBioTemplate.getValidType()) || "3".equals(apiBioTemplate.getValidType())) {
                        persBioTemplate.setValidType(Short.valueOf(apiBioTemplate.getValidType()));
                    } else {
                        // 如果 validtype不是为1 或3，添加失败
                        return PersConstants.PERSONID_VALIDTYPE_INCORRECT;
                    }
                    persBioTemplate.setDuress(false);
                    saveItem(persBioTemplate);
                    // 组装下发的指纹
                    PersBioTemplate2OtherItem bioTemplate2OtherItem = new PersBioTemplate2OtherItem();
                    ModelUtil.copyProperties(persBioTemplate, bioTemplate2OtherItem);
                    bioTemplate2OtherItem.setPin(persBioTemplate.getPersonPin());
                    bioTemplate2OtherItems.add(bioTemplate2OtherItem);
                }
                pers2OtherService.setBioTemplate2OtherModule(bioTemplate2OtherItems, ConstUtil.SYSTEM_MODULE_PERS);
            } else {
                // 该人员不存在 -22
                totalCount = PersConstants.PERSONID_NOTEXIST;
            }
        } else {
            totalCount = PersConstants.PERSONPIN_ISNULL;
        }
        return totalCount;
    }

    @Override
    public void deleteByPersonPin(String pin) {
        persBioTemplateDao.deleteByPersonPin(pin);
    }

    @Override
    public void deleteByPersonPinAndBioType(String pin, Short bioType) {
        persBioTemplateDao.deleteByPersonPinAndBioType(pin, bioType);
    }

    @Override
    public void deleteByPersonPinsAndBioTypes(List<String> personIds, List<String> personPins, List<Short> bioTypes) {
        persBioTemplateDao.deleteByPersonPinsAndBioTypes(personPins, bioTypes);
        if (bioTypes.contains(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE)) {
            // 删除人员比对照片表
            persBioPhotoService.deleteByPersonPinsAndBioTypes(personPins,
                Arrays.asList(BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE));
            for (String pin : personPins) {
                persPersonService.deleteCropFace(pin);
            }
        }
        if (bioTypes.contains(PersConstants.PALM_BIO_TYPE_10)) {
            // 删除人员可见光手掌照片
            persBioPhotoService.deleteByPersonPinsAndBioTypes(personPins,
                Arrays.asList(PersConstants.PALM_BIO_TYPE_10));
            for (String pin : personPins) {
                String photoPath = "upload/pers/user/bioPhoto/" + PersConstants.PALM_BIO_TYPE_10 + "/" + pin;
                FileUtil.deleteDirectory(FileUtil.getLocalFullPath(photoPath));
            }
        }
        pers2OtherService.deletePersonBioTemplate(personIds, bioTypes);
    }

    @Override
    public void updateBioTemplate(List<PersBioTemplateItem> bioTemplateItemList) {
        if (CollectionUtil.isEmpty(bioTemplateItemList)) {
            return;
        }

        // 业务模块不需要传人员ID
        Collection<String> pinList =
            CollectionUtil.getPropertyList(bioTemplateItemList, PersBioTemplateItem::getPersonPin, "-1");
        Map<String, String> pinAndIdMap = persPersonService.getPinsAndIdsByPins(pinList);
        if (CollectionUtil.isEmpty(pinAndIdMap)) {
            return;
        }

        // 根据人员分组处理
        Map<String, List<PersBioTemplateItem>> pinAndBioTemplateListMap =
            bioTemplateItemList.stream().collect(Collectors.groupingBy(PersBioTemplateItem::getPersonPin));
        for (Map.Entry<String, List<PersBioTemplateItem>> pinAndBioTemplateList : pinAndBioTemplateListMap.entrySet()) {

            // 人员不存在不处理
            String personPin = pinAndBioTemplateList.getKey();
            if (!pinAndIdMap.containsKey(personPin)) {
                continue;
            }

            // 已存在的模版
            String personId = pinAndIdMap.get(personPin);
            List<PersBioTemplate> bioTemplateList = persBioTemplateDao.findByPersonId(personId);
            Map<Short, List<PersBioTemplate>> bioTemplateListMap =
                bioTemplateList.stream().collect(Collectors.groupingBy(PersBioTemplate::getBioType));

            // 再根据类型分组处理
            List<PersBioTemplateItem> pinBioTemplateItems = pinAndBioTemplateList.getValue();
            Map<Short, List<PersBioTemplateItem>> bioTypeAndBioTemplateListMap =
                pinBioTemplateItems.stream().collect(Collectors.groupingBy(PersBioTemplateItem::getBioType));

            for (Map.Entry<Short, List<PersBioTemplateItem>> bioTypeAndBioTemplateLis : bioTypeAndBioTemplateListMap
                .entrySet()) {
                Short bioType = bioTypeAndBioTemplateLis.getKey();
                List<PersBioTemplateItem> bioTypeBioTemplateItems = bioTypeAndBioTemplateLis.getValue();
                Map<String,
                    PersBioTemplateItem> bioTemplateItemMap = bioTypeBioTemplateItems.stream()
                        .collect(Collectors.toMap(item -> item.getTemplateNo() + "_" + item.getTemplateNoIndex(),
                            Function.identity(), (x, y) -> y));

                String defVersion = "";
                if (BaseConstants.BaseBioType.FACE_BIO_TYPE.equals(bioType)) {
                    // 人脸
                    defVersion = BaseConstants.BaseBioType.FACE_BIO_VERSION;
                } else if (BaseConstants.BaseBioType.VEIN_BIO_TYPE.equals(bioType)) {
                    // 指静脉
                    defVersion = BaseConstants.BaseBioType.VEIN_BIO_VERSION;
                } else if (BaseConstants.BaseBioType.FP_BIO_TYPE.equals(bioType)) {
                    // 指纹
                    defVersion = BaseConstants.BaseBioType.FP_BIO_VERSION;
                } else if (BaseConstants.BaseBioType.PALM_BIO_TYPE.equals(bioType)) {
                    // 掌静脉
                    defVersion = BaseConstants.BaseBioType.PALM_BIO_VERSION;
                } else if (BaseConstants.BaseBioType.BIOPHOTO_BIO_TYPE.equals(bioType)) {
                    // 可见光人脸一体化模板
                    defVersion = BaseConstants.BaseBioType.BIOPHOTO_BIO_VERSION;
                } else if (PersConstants.IRIS_BIO_VERSION.equals(bioType)) {
                    // 虹膜
                    defVersion = PersConstants.IRIS_BIO_VERSION;
                }

                saveBioTemplate(personId, personPin, bioType, defVersion, bioTemplateListMap, bioTemplateItemMap, true);
            }
        }
    }

    @Override
    public Map<String, Map<String, Integer>> getBioTypeAndCountByPersonIdList(Collection<String> personIds) {
        List<Object[]> bioTemplateCount = persBioTemplateDao.countByTemplateNoAndPersonIdsAndGroupByBioTypeAndVersion(
            personIds, PersConstants.PERS_BIOTEMPLATE_DEF_NO_INDEX);

        // 0:为 bioType 1：为 count 2:版本 3:人员id
        return bioTemplateCount.stream()
            .collect(Collectors.groupingBy((Object[] o) -> o[3].toString(),
                Collectors.toMap((Object[] o) -> o[0].toString() + PersConstants.IMPORT_CARD_SPLIT + o[2].toString(),
                    (Object[] o) -> Integer.valueOf(o[1].toString()), (x, y) -> y)));
    }

    @Override
    public int deleteBioTemplateByCondition(PersPersonItem personItem, Short templateNo, Short fpBioType) {
        List<PersBioTemplate> persBioTemplates = persBioTemplateDao.findByPersonPinAndTemplateNoAndBioType(
            personItem.getPin(), templateNo, BaseConstants.BaseBioType.FP_BIO_TYPE);
        int count = 0;
        if (!persBioTemplates.isEmpty()) {
            persBioTemplateDao.delete(persBioTemplates);
            List<Short> bioTypes = new ArrayList<>();
            for (PersBioTemplate persBioTemplate : persBioTemplates) {
                bioTypes.add(persBioTemplate.getBioType());
            }
            // 删除人员指纹
            pers2OtherService.deletePersonBioTemplate(StrUtil.strToList(personItem.getId()), bioTypes);
            // 重新下发
            List<PersBioTemplate> persBioTemplateList =
                persBioTemplateDao.findByPersonIdAndBioType(personItem.getId(), BaseConstants.BaseBioType.FP_BIO_TYPE);
            if (persBioTemplateList != null && persBioTemplateList.size() > 0) {
                List<PersBioTemplate2OtherItem> persBioTemplate2OtherItems = new ArrayList<>();
                for (PersBioTemplate persBioTemplate : persBioTemplateList) {
                    PersBioTemplate2OtherItem persBioTemplate2OtherItem = new PersBioTemplate2OtherItem();
                    ModelUtil.copyProperties(persBioTemplate, persBioTemplate2OtherItem);
                    persBioTemplate2OtherItem.setPin(persBioTemplate.getPersonPin());
                    persBioTemplate2OtherItems.add(persBioTemplate2OtherItem);
                }
                pers2OtherService.setBioTemplate2OtherModule(persBioTemplate2OtherItems, ConstUtil.SYSTEM_MODULE_PERS);
            }
            count++;
        }
        return count;
    }

    @Override
    public void deleteByPersonPinsAndBioType(List<String> pins, Short bioType) {
        persBioTemplateDao.deleteByPersonPinsAndBioType(pins, bioType);
    }
}