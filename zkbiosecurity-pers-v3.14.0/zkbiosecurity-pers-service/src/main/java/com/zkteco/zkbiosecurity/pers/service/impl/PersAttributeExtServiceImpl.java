/**
 * File Name: PersAttributeExtServiceImpl Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright ©
 * 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.core.utils.SQLUtil;
import com.zkteco.zkbiosecurity.pers.dao.PersAttributeExtDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersAttributeExt;
import com.zkteco.zkbiosecurity.pers.service.PersAttributeExtService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeExtItem;

/**
 * 对应百傲瑞达 PersAttributeExtServiceImpl
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
@Service
@Transactional
public class PersAttributeExtServiceImpl implements PersAttributeExtService {
    @Autowired
    private PersAttributeExtDao persAttributeExtDao;
    @Autowired
    private PersPersonDao persPersonDao;
    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public PersAttributeExtItem saveItem(PersAttributeExtItem item) {
        PersAttributeExt persAttributeExt =
            Optional.ofNullable(item).map(PersAttributeExtItem::getId).filter(StringUtils::isNotBlank)
                .flatMap(persAttributeExtDao::findById).filter(Objects::isNull).orElseGet(() -> {
                    return Optional.ofNullable(item).map(PersAttributeExtItem::getPersonId)
                        .filter(StringUtils::isNotBlank).map(persAttributeExtDao::findByPersonId)
                        .orElse(new PersAttributeExt());
                });
        if (StringUtils.isNotBlank(persAttributeExt.getId())) {
            ModelUtil.copyPropertiesIgnoreId(item, persAttributeExt);
        } else {
            ModelUtil.copyProperties(item, persAttributeExt);
        }
        persAttributeExtDao.save(persAttributeExt);
        item.setId(persAttributeExt.getId());
        return item;
    }

    @Override
    public void batchSaveAttributeExts(List<PersAttributeExtItem> itemList) {
        // 人员id
        Collection<String> persPersonIdList =
            CollectionUtil.getPropertyList(itemList, PersAttributeExtItem::getPersonId, "-1");
        List<List<String>> personIdList = CollectionUtil.split(persPersonIdList, CollectionUtil.splitSize);
        List<PersAttributeExt> persAttributeExtList = new ArrayList<>();
        personIdList.forEach(personIds -> {
            persAttributeExtList.addAll(persAttributeExtDao.findByPersonIdIn(personIds));
        });
        Map<String, PersAttributeExt> personIdAndPersAttributeExt = persAttributeExtList.stream()
            .collect(Collectors.toMap(PersAttributeExt::getPersonId, PersAttributeExt -> PersAttributeExt));

        List<PersAttributeExt> attributeExtList = new ArrayList<>();
        itemList.forEach(item -> {
            PersAttributeExt persAttributeExt = personIdAndPersAttributeExt.get(item.getPersonId());
            if (persAttributeExt == null) {
                persAttributeExt = new PersAttributeExt();
            }
            ModelUtil.copyPropertiesIgnoreNull(item, persAttributeExt);
            attributeExtList.add(persAttributeExt);
        });

        if (attributeExtList.size() > 0) {
            List<List<PersAttributeExt>> attrExtList = CollectionUtil.split(attributeExtList, CollectionUtil.splitSize);
            attrExtList.forEach(attrList -> {
                persAttributeExtDao.saveAll(attrList);
            });
        }
    }

    @Override
    public List<PersAttributeExtItem> getByCondition(PersAttributeExtItem condition) {
        return (List<PersAttributeExtItem>)persAttributeExtDao.getItemsBySql(condition.getClass(),
            SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        return persAttributeExtDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
    }

    @Override
    public boolean deleteByIds(String ids) {
        if (StringUtils.isNotEmpty(ids)) {
            String[] idArray = StringUtils.split(ids, ",");
            for (String id : idArray) {
                persAttributeExtDao.deleteById(id);
            }
        }
        return false;
    }

    @Override
    public PersAttributeExtItem getItemById(String id) {
        List<PersAttributeExtItem> items = getByCondition(new PersAttributeExtItem(id));
        return Optional.ofNullable(items).filter(i -> !i.isEmpty()).map(i -> i.get(0)).orElse(null);
    }

    @Override
    public void deleteByPersonId(String personId) {
        persAttributeExtDao.deleteByPersonId(personId);
    }

    @Override
    public PersAttributeExtItem getItemByPersonId(String personId) {
        return Optional.ofNullable(personId).filter(StringUtils::isNotBlank).map(persAttributeExtDao::findByPersonId)
            .filter(Objects::nonNull)
            .map(attributeExt -> ModelUtil.copyProperties(attributeExt, new PersAttributeExtItem())).orElse(null);
    }

    @Override
    public List<PersAttributeExtItem> getItemByPersonIds(String personIds) {
        PersAttributeExtItem item = new PersAttributeExtItem();
        item.setPersonIdIn(personIds);
        return getByCondition(item);
    }

    @Override
    public List<PersAttributeExtItem> getItemByPersonIdList(List<String> personIdList) {
        PersAttributeExtItem item = new PersAttributeExtItem();
        item.setPersonIdIn(StringUtils.join(personIdList, ","));
        return getByCondition(item);
    }

    @Override
    public void handlerTransfer(List<PersAttributeExtItem> attributeExtItems) {
        attributeExtItems.forEach(persAttributeExtItem -> {
            PersAttributeExt persAttributeExt = ModelUtil.copyProperties(persAttributeExtItem, new PersAttributeExt());
            persAttributeExtDao.save(persAttributeExt);
        });
    }

    @Override
    public void deleteAttrValueByFiledIndex(int filedIndex) {
        Query query = entityManager
            .createQuery(String.format("update PersAttributeExt attrExt set attrExt.attrValue%d=''", filedIndex));
        query.executeUpdate();
    }
}