package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.base.utils.VersionUtil;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.BaseUpgradeVerHandlerService;
import com.zkteco.zkbiosecurity.system.vo.BaseSysParamItem;

import lombok.extern.slf4j.Slf4j;

/**
 * 人事升级
 */
@Slf4j
@Component
@Order(1501)
public class PersUpgradeInit implements CommandLineRunner {

    @Autowired
    private BaseSysParamService baseSysParamService;
    @Autowired
    private BaseUpgradeVerHandlerService baseUpgradeVerHandlerService;

    @Override
    public void run(String... strings) throws Exception {
        try {
            upgrade();
        } catch (Exception e) {
            log.error("PersUpgradeInit Exception", e);
        }
    }

    void upgrade() {
        // 需要升级的目标版本，取git上面的tag版本
        String curVersion = VersionUtil.getReleaseGitTags(ConstUtil.SYSTEM_MODULE_PERS);
        if (StringUtils.isNotBlank(curVersion)) {
            // 当前版本
            BaseSysParamItem baseSysParamItem = baseSysParamService.findByParamName("PersUpgradeVersion");
            String curDbVersion = baseSysParamItem.getParamValue();

            // 如果获取不到版本给个默认值
            if (StringUtils.isBlank(curDbVersion)) {
                curDbVersion = "v2.3.0";
            }

            baseUpgradeVerHandlerService.upgrade(curDbVersion, ConstUtil.SYSTEM_MODULE_PERS);
            if (!curVersion.equals(curDbVersion)) {
                // 升级后更新当前版本为目标版本
                if (StringUtils.isBlank(baseSysParamItem.getId())) {
                    // 没有则初始化版本信息
                    baseSysParamItem =
                        new BaseSysParamItem("PersUpgradeVersion", curVersion, "Pers Upgrade Version", true);
                } else {
                    // 存在则更新为新版本
                    baseSysParamItem.setParamValue(curVersion);
                }
                baseSysParamService.saveItem(baseSysParamItem);
            }
        }

    }
}
