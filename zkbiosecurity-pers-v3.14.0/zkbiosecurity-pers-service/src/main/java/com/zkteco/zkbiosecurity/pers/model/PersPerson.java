/**
 * File Name: Pers<PERSON>erson Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.pers.model;

import java.io.Serializable;
import java.util.*;

import javax.persistence.*;

import com.zkteco.zkbiosecurity.core.convert.EncryptConverter;
import com.zkteco.zkbiosecurity.core.model.BaseModel;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 PersPerson
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Entity
@Table(name = "PERS_PERSON",
    indexes = {@Index(name = "PERS_PERSON_PIN_IDX", columnList = "PIN"),
        @Index(name = "PERS_PERSON_NAME_IDX", columnList = "NAME"),
        @Index(name = "PERS_PERSON_DEPT_ID_IDX", columnList = "AUTH_DEPT_ID"),
        @Index(name = "PERS_PERSON_CREATE_TIME_IDX", columnList = "CREATE_TIME"),
        @Index(name = "PERS_PERSON_UPDATE_TIME_IDX", columnList = "UPDATE_TIME")})
@Getter
@Setter
@Accessors(chain = true)
public class PersPerson extends BaseModel implements Serializable {

    /** */
    private static final long serialVersionUID = 1L;

    /**
     * 部门id
     */
    @Column(name = "AUTH_DEPT_ID", length = 50, nullable = false)
    private String deptId;

    /**
     * 职位id
     */
    @ManyToOne
    @JoinColumn(name = "POSITION_ID")
    private PersPosition persPosition;

    /**
     * 人员编号
     */
    @Column(name = "PIN", length = 30, nullable = false, updatable = false, unique = true)
    private String pin;

    /**
     * 名字
     */
    @Column(name = "NAME", length = 50)
    private String name;

    /**
     * 姓氏
     */
    @Column(name = "LAST_NAME", length = 50)
    private String lastName;

    /**
     * 性别
     */
    @Column(name = "GENDER", length = 1)
    private String gender;

    /**
     * 照片路径
     */
    @Column(name = "PHOTO_PATH", length = 200)
    private String photoPath;

    /**
     * 状态
     */
    @Column(name = "STATUS")
    private Short status;

    /**
     * 姓名拼音
     */
    @Column(name = "NAME_SPELL", length = 420)
    private String nameSpell;

    /**
     * 人员类型
     */
    @Column(name = "PERSON_TYPE")
    private Short personType;

    /**
     * 出生日期
     */
    @Column(name = "BIRTHDAY")
    @Temporal(TemporalType.DATE)
    private Date birthday;

    /**
     * 联系电话
     */
    @Column(name = "MOBILE_PHONE", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String mobilePhone;

    /**
     * 邮箱
     */
    @Column(name = "EMAIL", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String email;

    /**
     * 人员密码
     */
    @Column(name = "PERSON_PWD", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String personPwd;

    /**
     * 社会安全号
     */
    @Column(name = "SSN", length = 20)
    private String ssn;

    /**
     * 车牌号
     */
    @Column(name = "CAR_PLATE", length = 20)
    private String carPlate;

    /**
     * 自助密码
     */
    @Column(name = "SELF_PWD", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String selfPwd;

    /**
     * 异常标志位
     */
    @Column(name = "EXCEPTION_FLAG")
    private Short exceptionFlag;

    /**
     * 是否发送邮件
     */
    @Column(name = "IS_SENDMAIL")
    private Boolean isSendMail;

    /**
     * 入职时间
     */
    @Column(name = "HIRE_DATE")
    @Temporal(TemporalType.DATE)
    private Date hireDate;

    /**
     * 身份证
     */
    @Column(name = "ID_CARD", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String idCard;

    /**
     * 身份证物理卡号
     */
    @Column(name = "ID_CARD_PHYSICAL_NO", length = 250)
    @Convert(converter = EncryptConverter.class)
    private String idCardPhysicalNo;

    /**
     * 人员来源
     */
    @Column(name = "IS_FROM", length = 100, updatable = false)
    private String isFrom;

    /**
     * 人员编号是否包含字母
     */
    @Column(name = "PIN_LETTER", updatable = false)
    private Boolean pinLetter;

    /**
     * 限制连接数
     */
    @Column(name = "PERS_LOGIN_LIMIT")
    private Integer persLoginLimit;

    /**
     * 是否发送短信
     */
    @Column(name = "SEND_SMS")
    private Boolean sendSMS;

    @Column(name = "NUMBER_PIN")
    private Long numberPin;

    /**
     * 标识是否开通移动端
     */
    @Column(name = "EXISTS_MOBILE_USER")
    private Boolean existsMobileUser;

    /**
     * 启禁用凭证
     */
    @Column(name = "ENABLED_CREDENTIAL")
    private Boolean enabledCredential;

    @Column(name = "SEND_WHATSAPP")
    private Boolean sendWhatsapp;

    @Column(name = "WHATSAPP_MOBILENO", length = 100)
    private String whatsappMobileNo;

    //微信openid

    @com.zkteco.zkbiosecurity.base.annotation.Column(name = "wechat_open_id")
    private String wechatOpenId;

    /**
     * app 登录授权 add by xjiang.huang 2023-08-19
     */
    @Column(name = "APP_AUTHORIZATION")
    private Boolean appAuthorization;

    /**
     * app 推送
     */
    @Column(name = "SEND_APP")
    private Boolean sendApp;

    /**  */
    @OneToMany(mappedBy = "persPerson", cascade = CascadeType.ALL)
    private List<PersPersonChange> persPersonChangeList = new ArrayList<>();

    /**  */
    @OneToMany(mappedBy = "persPerson", cascade = CascadeType.ALL)
    private List<PersCertificate> persCertificateList = new ArrayList<>();

    /**  */
    @OneToMany
    @JoinColumn(name = "PERSON_ID")
    private Set<PersBioTemplate> persBioTemplateSet = new HashSet<>();

    public PersPerson() {}

    public PersPerson(String id, String pin, String name, String lastName, String personPwd, Short personType,
        String photoPath, String idCard) {
        this.id = id;
        this.pin = pin;
        this.name = name;
        this.lastName = lastName;
        this.photoPath = photoPath;
        this.personPwd = personPwd;
        this.idCard = idCard;
        this.personType = personType;
    }

    public PersPerson(String id, String pin, String name, String lastName, String personPwd, Short personType,
        String photoPath, String idCard, Boolean enabledCredential) {
        this.id = id;
        this.pin = pin;
        this.name = name;
        this.lastName = lastName;
        this.photoPath = photoPath;
        this.personPwd = personPwd;
        this.idCard = idCard;
        this.personType = personType;
        this.enabledCredential = enabledCredential;
    }
}