package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.constants.AuthContants;
import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.auth.vo.AuthPermissionItem;
import com.zkteco.zkbiosecurity.core.constant.ZKConstant;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class PersVer3_13_0 implements UpgradeVersionManager {

    @Autowired
    private AuthPermissionService authPermissionService;
    @Value("${system.isCloud:false}")
    private Boolean isCloud;
    @Value("${system.language:zh_CN}")
    private String language;

    @Override
    public String getVersion() {
        return "v3.13.0";
    }

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public boolean executeUpgrade() {
        AuthPermissionItem systemItem = authPermissionService.getItemByCode("Pers");
        if (systemItem != null) {
            AuthPermissionItem subMenuItem = new AuthPermissionItem("PersEncryptProp", "common_param_infoProtection",
                "pers:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 999);
            subMenuItem.setParentId(systemItem.getId());
            subMenuItem = authPermissionService.initData(subMenuItem);

            AuthPermissionItem subButtonItem = new AuthPermissionItem("PersPinEncryptProp", "pers_person_pin",
                "pers:pin:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 1);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersNameEncryptProp", "pers_person_wholeName",
                "pers:name:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 2);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersCardNoEncryptProp", "pers_card_cardNo",
                "pers:cardNo:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 3);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersGenderEncryptProp", "pers_person_gender",
                "pers:gender:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 4);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersBirthdayEncryptProp", "pers_person_birthday",
                "pers:birthday:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 5);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersMobilePhoneEncryptProp", "pers_person_mobilePhone",
                "pers:mobilePhone:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 6);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersEmailEncryptProp", "pers_person_email",
                "pers:email:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 7);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);

            subButtonItem = new AuthPermissionItem("PersPhotoEncryptProp", "pers_person_photo",
                "pers:photo:encryptProp", AuthContants.RESOURCE_TYPE_SENSITIVE_INFO_PROTECT, ZKConstant.TRUE, 8);
            subButtonItem.setParentId(subMenuItem.getId());
            subButtonItem = authPermissionService.initData(subButtonItem);
        }

        if ("zh_CN".equals(language) && !isCloud) {
            initAppMenus();
        }

        return true;
    }

    /**
     * 初始化管理员app权限
     */
    private void initAppMenus() {
        AuthPermissionItem appModuleItem = authPermissionService.getItemByCode("App");
        if (null == appModuleItem) {
            // 管理员APP模块
            appModuleItem = new AuthPermissionItem("App", "app_module", "app", AuthContants.RESOURCE_TYPE_APP_SYSTEM,
                ZKConstant.TRUE, 998);
            authPermissionService.initData(appModuleItem);
        }
        AuthPermissionItem appMenuItem = new AuthPermissionItem("AppPers", "app_pers", "app:APPpers",
            AuthContants.RESOURCE_TYPE_APP_MENU, ZKConstant.TRUE, 1);
        appMenuItem.setParentId(appModuleItem.getId());
        appMenuItem = authPermissionService.saveItem(appMenuItem);
        // 人事--新增
        AuthPermissionItem appButtonItem = new AuthPermissionItem("AppPersAdd", "app_pers_add", "app:APPpersAdd",
            AuthContants.RESOURCE_TYPE_APP_BUTTON, ZKConstant.TRUE, 2);
        appButtonItem.setParentId(appMenuItem.getId());
        authPermissionService.saveItem(appButtonItem);
    }
}
