package com.zkteco.zkbiosecurity.pers.utils;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.util.*;

import javax.imageio.ImageIO;

import org.apache.commons.lang3.StringUtils;

import com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil;
import com.zkteco.zkbiosecurity.core.utils.FileUtil;
import com.zkteco.zkbiosecurity.core.utils.FileUtils;

import sun.misc.BASE64Decoder;

/**
 * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @date $date$ $time$ $params$
 * @return $returns$
 */
public class PersPersonUtil {
    /**
     * 进制卡号转换
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/23 17:55
     * @param cardNo
     * @param beforeRadix
     * @param afterRadix
     * @return java.lang.String
     */
    public static String convertCardNo(String cardNo, int beforeRadix, int afterRadix) {
        cardNo = new BigInteger(cardNo, beforeRadix).toString(afterRadix);
        return cardNo;
    }

    /**
     * 能打开图片就是有效图片
     * 
     * @param imgBase64
     * @return
     */
    public static boolean isImage(String imgBase64) {
        boolean isImage = false;
        BufferedImage bi = null;
        try {
            bi = ImageIO.read(new ByteArrayInputStream(new BASE64Decoder().decodeBuffer(imgBase64)));
            if (bi != null) {
                isImage = true;
            }
        } catch (IOException e) {
            isImage = false;
        } finally {
            if (bi != null) {
                bi.flush();
                bi = null;
            }
        }
        return isImage;
    }

    public static String collectionToStr(Collection<String> strs) {
        if (strs != null && !strs.isEmpty()) {
            StringBuffer sb = new StringBuffer();
            Iterator var2 = strs.iterator();

            while (var2.hasNext()) {
                String s = (String)var2.next();
                sb.append(s + ",");
            }

            return sb.toString().substring(0, sb.toString().length() - 1);
        } else {
            return "";
        }
    }

    /**
     * 访客预约常量
     *
     * <AUTHOR>
     * @DATE 2020-06-01 14:26
     * @since 1.0.0
     */
    public static final Map<String, String> SYMPTOM_MAP;
    public static final Map<String, String> EXPOSURE_MAP;
    static {
        Map<String, String> tempMap = new HashMap<>();
        tempMap.put("0", "common_none");
        tempMap.put("1", "pers_health_symptomCough");
        tempMap.put("2", "pers_health_symptomFever");
        tempMap.put("3", "pers_health_symptomPolypena");
        SYMPTOM_MAP = Collections.unmodifiableMap(tempMap);

        Map<String, String> tempMap1 = new HashMap<>();
        tempMap1.put("0", "common_no");
        tempMap1.put("1", "common_yes");
        EXPOSURE_MAP = Collections.unmodifiableMap(tempMap1);
    }

    /**
     * 统计抠图照片数量
     * 
     * @return int
     * <AUTHOR>
     * @throws @date 2021-04-06 10:56
     * @since 1.0.0
     */
    public static int getCropFaceCount() {
        int cropFaceCount = 0;
        File file = new File(FileUtils.systemFilePath + FileUtils.separator + "upload" + FileUtils.separator + "pers"
            + FileUtils.separator + "user" + FileUtils.separator + "cropface");
        if (file.exists()) {
            File[] var2 = file.listFiles();
            int var3 = var2.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                File files = var2[var4];
                String fileName;
                String extension = "";
                int index;
                for (File f : files.listFiles()) {
                    fileName = f.getName();
                    index = fileName.lastIndexOf('.');
                    if (index > 0) {
                        extension = fileName.substring(index + 1);
                    }
                    if ("jpg".equals(extension)) {
                        cropFaceCount++;
                    }
                }
            }
        }
        return cropFaceCount;
    }

    /**
     * 统计头像照片数量
     * 
     * @return int
     * <AUTHOR>
     * @throws @date 2021-08-05 11:43
     * @since 1.0.0
     */
    public static long getPortraitPhotoCount() {
        int photoCount = 0;
        File file = new File(FileUtils.systemFilePath + FileUtils.separator + "upload" + FileUtils.separator + "pers"
            + FileUtils.separator + "user" + FileUtils.separator + "avatar");
        if (file.exists()) {
            File[] var2 = file.listFiles();
            int var3 = var2.length;

            for (int var4 = 0; var4 < var3; ++var4) {
                File files = var2[var4];
                String fileName;
                String extension = "";
                int index;
                if (files.isDirectory()) {
                    for (File f : files.listFiles()) {
                        fileName = f.getName();
                        index = fileName.lastIndexOf('.');
                        if (index > 0) {
                            extension = fileName.substring(index + 1);
                        }
                        if ("jpg".equals(extension)) {
                            photoCount++;
                        }
                    }
                }
            }
        }
        return photoCount;
    }

    /**
     * 图片、缩略图、抠图照片加密
     * 
     * @param photoPath:
     * @param pin:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-05-27 18:03
     * @since 1.0.0
     */
    public static void encryptPersonPhoto(String photoPath, String pin) {
        String cropFacePath = FileUtil.getCropFacePath(pin) + FileUtil.separator + pin + ".jpg";
        encryptPhoto(photoPath, cropFacePath);
    }

    public static void encryptPersonPhoto(String photoPath, String pin, String business) {
        String cropFacePath = FileUtil.getCropFacePath(business, pin) + FileUtil.separator + pin + ".jpg";
        encryptPhoto(photoPath, cropFacePath);
    }

    public static void encryptPhoto(String photoPath, String cropFacePath) {
        if (StringUtils.isNotBlank(photoPath)) {
            FileEncryptUtil.encryptFileByPath(photoPath);
            // 对缩略图进行加密
            FileEncryptUtil.encryptFileByPath(FileUtil.getThumbPath(photoPath));
        }
        if (StringUtils.isNotBlank(cropFacePath)) {
            // 获取抠图路径
            File cropFile = new File(FileUtils.systemFilePath + FileUtil.separator + cropFacePath);
            if (cropFile.exists()) {
                // 对抠图进行加密
                FileEncryptUtil.encryptFileByPath(cropFacePath);
            }
        }
    }

    /**
     * 根据原始pin创建数字
     * 
     * @param pin
     * @return
     */
    public static Long createNumberPin(String pin) {
        StringBuffer targetPin = new StringBuffer();
        String originPin = pin.toUpperCase();
        boolean containLetter = pin.matches(".*[a-zA-Z]+.*");
        if (containLetter) {
            targetPin.append(2);
        } else {
            targetPin.append(1);
        }
        for (int i = 0; i < originPin.length(); i++) {
            char c = originPin.charAt(i);
            // 将英文字母转换为1-26
            if (c > 64 && c < 91) {
                targetPin.append((int)c);
            } else {
                if (containLetter) {
                    targetPin.append(0);
                }
                targetPin.append(c);
            }
        }
        if (containLetter) {
            while (targetPin.length() < 19) {
                targetPin.append(0);
            }
        }
        return Long.parseLong(targetPin.substring(0, targetPin.length() >= 19 ? 19 : targetPin.length()));
    }
}
