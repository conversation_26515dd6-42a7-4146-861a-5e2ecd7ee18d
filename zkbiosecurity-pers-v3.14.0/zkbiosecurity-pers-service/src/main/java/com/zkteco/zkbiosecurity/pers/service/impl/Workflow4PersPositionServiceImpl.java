package com.zkteco.zkbiosecurity.pers.service.impl;

import com.zkteco.zkbiosecurity.core.utils.CollectionUtil;
import com.zkteco.zkbiosecurity.core.utils.ModelUtil;
import com.zkteco.zkbiosecurity.pers.service.PersPositionService;
import com.zkteco.zkbiosecurity.pers.vo.PersPositionItem;
import com.zkteco.zkbiosecurity.workflow.service.Workflow4PersPositionService;
import com.zkteco.zkbiosecurity.workflow.vo.Workflow4PersPositionItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 工作流人事职位接口实现
 * <AUTHOR>
 * @date 2019/6/19
 */
@Service
public class Workflow4PersPositionServiceImpl implements Workflow4PersPositionService {

    @Autowired
    PersPositionService persPositionService;

    @Override
    public Workflow4PersPositionItem getItemById(String id) {
        PersPositionItem persPositionItem = persPositionService.getItemById(id);
        if (Objects.nonNull(persPositionItem)) {
            Workflow4PersPositionItem workflow4PersPositionItem = ModelUtil.copyProperties(persPositionItem, new Workflow4PersPositionItem());
            return workflow4PersPositionItem;
        }
        return null;
    }


    @Override
    public List<Workflow4PersPositionItem> getByCondition(Workflow4PersPositionItem condition) {
        PersPositionItem persPositionItem = ModelUtil.copyProperties(condition, new PersPositionItem());
        List<PersPositionItem> persPositionItemList = persPositionService.getByCondition(persPositionItem);
        List<Workflow4PersPositionItem> workflow4PersPositionItemList = new ArrayList<>();
        if(!CollectionUtil.isEmpty(persPositionItemList)){
            workflow4PersPositionItemList = ModelUtil.copyListProperties(persPositionItemList, Workflow4PersPositionItem.class);
        }
        return workflow4PersPositionItemList;
    }
}
