package com.zkteco.zkbiosecurity.pers.service.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.business.sdk.vo.ApiAuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.service.PersBasicDataService;
import com.zkteco.zkbiosecurity.system.app.service.BaseAuthCloudMessageSendService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.service.SystemPersInfo2CloudService;

/**
 * <AUTHOR>
 * @Date: 2019/7/2 14:10
 */
@Service
@Transactional
public class PersBasicDataServiceImpl implements PersBasicDataService {

    @Autowired
    private BaseAuthCloudMessageSendService baseAuthCloudMessageSendService;
    @Autowired
    private SystemPersInfo2CloudService persCloudService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Override
    public ZKResultMsg syncAllPersonToCloud() {
        if (baseAuthCloudMessageSendService.isAllowSendBasicData()) {
            // 同步所有部门数据到云端
            ZKResultMsg resultMsg = baseAuthCloudMessageSendService.syncAllDepartmentToCloud();
            if (!"ok".equals(resultMsg.getRet())) {
                resultMsg.setMsg("system_api_upload_deptFail");
                return resultMsg;
            }
            // 同步职位数据到云端
            // resultMsg = persCloudService.syncAllPositionToCloud();
            // if (!"ok".equals(resultMsg.getRet())) {
            // resultMsg.setMsg("pers_api_upload_positionFail");
            // return resultMsg;
            // }
            // 同步所有人员数据到云端
            persCloudService.syncAllPersonToCloud();
            // 设置已经同步基础数据
            baseSysParamService.setAlreadyInitModule("BasicData");
            // 设置人员最后同步时间
            persCloudService.setPersonLastPushTime();
        } else {
            return ZKResultMsg.failMsg();
        }
        return ZKResultMsg.successMsg();
    }

    @Override
    public ZKResultMsg syncAllPersonFromCloud() {
        // if (!baseAuthCloudMessageSendService.isMasterApp()) {
        // 从云端同步所有部门数据更新
        ZKResultMsg resultMsg =
            baseAuthCloudMessageSendService.syncAuthDepartmentFromCloud(new ApiAuthDepartmentItem());
        if (!"ok".equals(resultMsg.getRet())) {
            resultMsg.setMsg("system_api_sync_deptFail");
            return resultMsg;
        }
        // 更新人员职位
        resultMsg = persCloudService.syncAllPositionFromCloud();
        if (!"ok".equals(resultMsg.getRet())) {
            resultMsg.setMsg("system_api_sync_positionFail");
            return resultMsg;
        }
        // 通知云端更新人员数据
        persCloudService.notifyCloudSyncPersonData();
        baseSysParamService.setAlreadyInitModule("BasicData");
        // } else {
        // return ZKResultMsg.failMsg();
        // }
        return ZKResultMsg.successMsg();
    }
}
