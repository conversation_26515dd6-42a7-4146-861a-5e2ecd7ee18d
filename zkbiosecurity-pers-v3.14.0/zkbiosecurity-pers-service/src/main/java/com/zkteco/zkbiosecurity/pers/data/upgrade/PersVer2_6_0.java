package com.zkteco.zkbiosecurity.pers.data.upgrade;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.zkteco.zkbiosecurity.auth.service.AuthPermissionService;
import com.zkteco.zkbiosecurity.core.jdbc.JdbcOperateTemplate;
import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.system.service.UpgradeVersionManager;

/**
 * <AUTHOR>
 * @date 2021/2/8 10:03
 * @since 1.0.0
 */
@Component
public class PersVer2_6_0 implements UpgradeVersionManager {
    @Autowired
    private AuthPermissionService authPermissionService;
    @Autowired
    private JdbcOperateTemplate jdbcOperateTemplate;

    @Override
    public String getModule() {
        return ConstUtil.SYSTEM_MODULE_PERS;
    }

    @Override
    public String getVersion() {
        return "v2.6.0";
    }

    @Override
    public boolean executeUpgrade() {
        jdbcOperateTemplate.alterTableCharLen("PERS_ATTRIBUTE", "ATTR_NAME", "100");
        return true;
    }
}
