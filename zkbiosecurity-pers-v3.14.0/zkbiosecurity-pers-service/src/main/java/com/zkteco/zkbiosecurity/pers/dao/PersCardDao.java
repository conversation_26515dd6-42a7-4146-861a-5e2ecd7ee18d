/**
 * File Name: PersCard Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.pers.dao;

import java.util.Collection;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.zkteco.zkbiosecurity.core.dao.BaseDao;
import com.zkteco.zkbiosecurity.pers.model.PersCard;

/**
 * 对应百傲瑞达 PersCardDao
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
public interface PersCardDao extends BaseDao<PersCard, String> {
    /**
     * 根据ids查询
     *
     * @param ids
     * @return
     */
    List<PersCard> findByIdIn(Collection<String> ids);

    /**
     * 根据personIds查询
     *
     * @param personIds
     * @return
     */
    List<PersCard> findByPersonIdInOrderByCardType(Collection<String> personIds);

    /**
     * 根据人员ID和卡类型查询
     *
     * @param personId
     * @param cardType
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersCard>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/7/27 10:38
     */
    List<PersCard> findByPersonIdAndCardType(String personId, short cardType);

    /**
     * 根据人员IDS，卡状态查询
     *
     * @param personIds
     * @param cardState
     * @return
     */
    List<PersCard> findByPersonIdInAndCardState(Collection<String> personIds, short cardState);

    /**
     * 根据人员IDS，卡状态查询
     *
     * @param personIds
     * @param cardType
     * @param cardState
     * @return
     */
    List<PersCard> findByPersonIdInAndCardTypeAndCardState(Collection<String> personIds, short cardType,
        short cardState);

    /**
     * 根据人员pin查询卡号
     *
     * @param pins
     * @return
     */
    List<PersCard> findByPersonPinIn(Collection<String> pins);

    /**
     * 根据多卡查询
     *
     * @param cardNos
     * @return
     */
    List<PersCard> findByCardNoIn(Collection<String> cardNos);

    /**
     * 根据多卡查询
     *
     * @param cardNos
     * @return
     */
    List<PersCard> findByCardNoInAndCardTypeAndCardState(Collection<String> cardNos, short cardType, short cardState);

    /**
     * 根据卡号查询
     *
     * @param cardNo
     * @return
     */
    List<PersCard> findByCardNo(String cardNo);

    /**
     * 根据卡号模糊查询
     *
     * @param cardNo
     * @return
     */

    @Query(value = "SELECT t FROM PersCard t WHERE t.cardNo LIKE %:cardNo%")
    List<PersCard> findByCardNOLike(@Param("cardNo") String cardNo);

    /**
     * 根据卡号和pin查询
     *
     * @param cardNo
     * @param pinNot
     * @return
     */
    Long countByCardNoAndPersonPinNot(String cardNo, String pinNot);

    /**
     * 根据逻辑卡
     *
     * @param logicalCardNo
     * @return
     */
    PersCard findByLogicalCardNo(String logicalCardNo);

    /**
     * 根据人员pin查询卡号
     *
     * @param pin
     * @return
     */
    @Query(value = "SELECT t.cardNo FROM PersCard t WHERE t.personPin = ?1 ORDER BY t.cardType")
    List<String> findByPersonPinOrderByCardType(String pin);

    /**
     * 根据卡状态和人员IDS查询卡号
     *
     * @param cardState
     * @param personIds
     * @return java.util.List<java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/4/25 17:59
     */
    @Query(value = "SELECT t.cardNo FROM PersCard t WHERE t.cardState = ?1 AND t.personId in ?2")
    List<String> findCardNoByCardStateAndPersonIdIn(Short cardState, Collection<String> personIds);

    /***
     * 根据人员pin和卡类型查找卡号
     *
     * @param pin
     * @param cardType
     * @return
     */
    @Query(value = "SELECT t.cardNo FROM PersCard t WHERE t.personPin = ?1 AND t.cardType = ?2")
    List<String> findByPersonPinAndCardType(String pin, short cardType);

    /**
     * 根据卡类型查询数量
     *
     * @param cardType
     * @return
     */
    long countByCardType(short cardType);

    /**
     * 查询有效卡
     *
     * @param cardState
     * @return
     */
    long countByCardState(short cardState);

    @Query(value = "SELECT count(0) FROM PERS_CARD t LEFT JOIN PERS_PERSON p ON t.PERSON_ID=p.ID "
        + "WHERE t.CARD_STATE = ?1 AND p.auth_dept_id in (?2)", nativeQuery = true)
    long countByCardStateAndDeptIdIn(short cardState, List<String> deptIdList);

    @Query(value = "SELECT count(0) FROM PERS_CARD t LEFT JOIN PERS_PERSON p ON t.PERSON_ID=p.ID "
        + "WHERE t.CARD_STATE = ?1 AND p.AUTH_DEPT_ID IN (SELECT ud.AUTH_DEPT_ID FROM AUTH_USER_DEPT ud WHERE ud.AUTH_USER_ID=?2)",
        nativeQuery = true)
    long countByCardStateAndUserId(short cardState, String userId);

    /**
     * 根据人员Id删除卡号
     *
     * @param personId
     */
    void deleteByPersonId(String personId);

    /**
     * 根据人员Id删除卡号
     *
     * @param personIds
     */
    void deleteByPersonIdIn(Collection<String> personIds);

    /**
     * 根据人员编号进行删除卡
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/6 21:17
     * @param personPins
     * @return void
     */
    void deleteByPersonPinIn(Collection<String> personPins);

    /**
     * 获取卡和人员ID，pin
     * 
     * @param cardNos
     * @param cardState
     * @return Object[] 0:cardNo 1:personId 2:personPin
     */
    @Query(
        value = "SELECT t.cardNo,t.personId,t.personPin FROM PersCard t WHERE t.cardNo in ?1 AND t.cardState = ?2 order by t.updateTime")
    List<Object[]> findCardNoAndPersonByCardNosAndCardState(Collection<String> cardNos, Short cardState);

    /**
     * 统计已发卡人数
     * 
     * @return Object[] 下标 0:为 count
     */
    @Query(value = "SELECT count(DISTINCT p.PERSON_ID) FROM PERS_CARD p", nativeQuery = true)
    List<Integer> getHasCardPersonCount();

    /**
     * 根据人员pin和卡类型查询
     *
     * @param personPins:
     * @param cardType:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.model.PersCard>
     * <AUTHOR>
     * @throws
     * @date 2022-03-01 15:52
     * @since 1.0.0
     */
    List<PersCard> findByPersonPinInAndCardType(Collection<String> personPins, short cardType);

    @Modifying
    @Query("update PersCard t set t.cardState=?1 where t.personId in (?2)")
    void updateCardState(Short cardState, List<String> personIds);
}