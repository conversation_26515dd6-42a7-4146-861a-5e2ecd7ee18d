/**
 * File Name: PersCardServiceImpl Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service.impl;

import java.math.BigInteger;
import java.security.SecureRandom;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.zkteco.zkbiosecurity.acc.service.Acc4PersCardService;
import com.zkteco.zkbiosecurity.acc.service.Acc4PersPersonService;
import com.zkteco.zkbiosecurity.att.service.Att4PersCardService;
import com.zkteco.zkbiosecurity.att.service.Att4PersPersonService;
import com.zkteco.zkbiosecurity.auth.service.AuthDepartmentService;
import com.zkteco.zkbiosecurity.auth.service.AuthUserService;
import com.zkteco.zkbiosecurity.auth.vo.AuthDepartmentItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.core.utils.*;
import com.zkteco.zkbiosecurity.ele.service.Ele4PersCardService;
import com.zkteco.zkbiosecurity.ele.service.Ele4PersPersonService;
import com.zkteco.zkbiosecurity.guard.foldex.utils.FoldexUtil;
import com.zkteco.zkbiosecurity.ins.service.Ins4PersCardService;
import com.zkteco.zkbiosecurity.ins.service.Ins4PersPersonService;
import com.zkteco.zkbiosecurity.pers.constants.PersConstants;
import com.zkteco.zkbiosecurity.pers.dao.PersCardDao;
import com.zkteco.zkbiosecurity.pers.dao.PersPersonDao;
import com.zkteco.zkbiosecurity.pers.model.PersCard;
import com.zkteco.zkbiosecurity.pers.model.PersPerson;
import com.zkteco.zkbiosecurity.pers.service.*;
import com.zkteco.zkbiosecurity.pers.util.PersRegularUtil;
import com.zkteco.zkbiosecurity.pers.utils.PersPersonUtil;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pos.service.Pos4PersCardService;
import com.zkteco.zkbiosecurity.posid.service.PosID4PersCardService;
import com.zkteco.zkbiosecurity.psg.service.Psg4PersCardService;
import com.zkteco.zkbiosecurity.psg.service.Psg4PersPersonService;
import com.zkteco.zkbiosecurity.system.service.BaseSysParamService;
import com.zkteco.zkbiosecurity.system.utils.SystemAcmsUtil;

/**
 * 对应百傲瑞达 PersCardServiceImpl
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
@Service
@Transactional
public class PersCardServiceImpl implements PersCardService {
    @Autowired
    private PersCardDao persCardDao;
    @Autowired
    private PersPersonDao persPersonDao;
    @Autowired
    private PersIssueCardService persIssueCardService;

    @Autowired
    private AuthDepartmentService authDepartmentService;

    @Autowired
    private PersPersonService persPersonService;

    @Autowired
    private PersCardService persCardService;
    @Autowired
    private BaseSysParamService baseSysParamService;

    @Autowired(required = false)
    private Acc4PersPersonService acc4PersPersonService;
    @Autowired(required = false)
    private Acc4PersCardService acc4PersCardService;
    @Autowired(required = false)
    private Ele4PersCardService ele4PersCardService;
    @Autowired(required = false)
    private Ele4PersPersonService ele4PersPersonService;
    @Autowired(required = false)
    private Pos4PersCardService pos4PersCardService;
    @Autowired(required = false)
    private PosID4PersCardService posID4PersCardService;
    @Autowired(required = false)
    private Psg4PersPersonService psg4PersPersonService;
    @Autowired(required = false)
    private Att4PersCardService att4PersCardService;
    @Autowired(required = false)
    private Ins4PersCardService ins4PersCardService;
    @Autowired(required = false)
    private Psg4PersCardService psg4PersCardService;
    @Autowired(required = false)
    private Att4PersPersonService att4PersPersonService;
    @Autowired(required = false)
    private Ins4PersPersonService ins4PersPersonService;
    @Autowired(required = false)
    private PersPersonExtService[] persPersonExtServices;
    /** 是否开启数据加密 默认不开启 */
    @Value("${system.data.security-encrypt:false}")
    private boolean isOpenSecurityEncrypt;
    @Autowired
    private AuthUserService authUserService;
    @Autowired
    private Pers2OtherService pers2OtherService;
    @Autowired
    private PersAcmsService persAcmsService;

    private SecureRandom random = new SecureRandom();

    @Override
    public PersCardItem saveItem(PersCardItem item) {
        if (StringUtils.isBlank(item.getCardNo())) {
            throw ZKBusinessException.warnException("pers_issueCard_cardEmptyNote");
        }
        if (StringUtils.isBlank(item.getId()) && StringUtils.isBlank(item.getPersonPin())) {
            throw ZKBusinessException.warnException("pers_import_pinNotEmpty");
        }
        PersCard persCard = Optional.ofNullable(item).map(PersCardItem::getId).filter(StringUtils::isNotBlank)
            .flatMap(persCardDao::findById).orElseGet(() -> {
                List<PersCard> cards = persCardDao.findByCardNo(item.getCardNo());
                if (!CollectionUtil.isEmpty(cards)) {
                    return cards.stream().filter(card -> item.getPersonPin().equals(card.getPersonPin())).findFirst()
                        .orElse(null);
                }
                return null;
            });
        PersPerson persPerson = persPersonDao.findByPin(item.getPersonPin());
        if (Objects.isNull(persCard)) {
            createCard(persPerson, item);
        } else {
            if (!persCard.getPersonPin().equals(item.getPersonPin())) {
                // 该卡已被其他人员使用了。把这个人设置成卡号重复异常
                persPersonService.updatePersonExceptionFlag(persPerson.getPin(), PersConstants.PERSON_ISEXCEPTION);
                // 该卡变更为无效卡
                item.setCardState(PersConstants.CARD_LOSS);
                createCard(persPerson, item);
            } else if (persCard.getCardType().shortValue() != item.getCardType().shortValue()) {
                // 主副卡变化了
                if (persCard.getCardType().shortValue() == PersConstants.MAIN_CARD) {
                    // 原卡为主卡，变为副卡
                    persCard.setCardType(PersConstants.DEPUTY_CARD);
                } else {
                    // 原卡为副卡，变为主卡,需要查询该人员的主卡，并且变更为副卡。
                    persCard.setCardType(PersConstants.MAIN_CARD);

                    List<PersCard> masterCard =
                        persCardDao.findByPersonIdAndCardType(persPerson.getId(), PersConstants.MAIN_CARD);
                    if (!CollectionUtil.isEmpty(masterCard)) {
                        // 把主卡变更为副卡
                        persCardDao.save(masterCard.get(0).setCardType(PersConstants.DEPUTY_CARD));
                    }
                }
                persCardDao.save(persCard);
            }
        }
        return item;
    }

    @Override
    public void saveMasterCard(String pin, String cardNo) {
        if (StringUtils.isNotBlank(cardNo)) {
            List<PersCard> cards = persCardDao.findByCardNo(cardNo);
            PersCard card = cards.stream().filter(l -> pin.equals(l.getPersonPin())).findFirst().orElse(null);
            PersPerson persPerson = persPersonDao.findByPin(pin);
            // 查询该人员的主卡
            List<PersCard> cardList =
                persCardDao.findByPersonIdAndCardType(persPerson.getId(), PersConstants.MAIN_CARD);
            boolean issueCard = false;
            // 该人员没有卡
            if (CollectionUtil.isEmpty(cardList)) {
                // 但是根据卡号查询是有卡的，说明卡号重复，需设置人员卡号重复异常
                if (!CollectionUtil.isEmpty(cards)) {
                    persPersonService.updatePersonExceptionFlag(pin, PersConstants.PERSON_ISEXCEPTION);
                    // 卡号重复，需设置为无效卡
                    createCard(persPerson, new PersCardItem(cardNo, PersConstants.MAIN_CARD, PersConstants.CARD_LOSS));
                } else {
                    createCard(persPerson, new PersCardItem(cardNo, PersConstants.MAIN_CARD, PersConstants.CARD_VALID));
                }
                issueCard = true;
            } else {
                PersCard masterCard = cardList.get(0);
                if (masterCard.getCardNo().equals(cardNo)) {
                    // 相同主卡-不处理
                } else {
                    // 不相同主卡 先创建旧卡退卡记录
                    persIssueCardService.createIssueInfo(persPerson.getPin(), persPerson.getName(),
                        persPerson.getLastName(), masterCard.getCardNo(), PersConstants.INEXISTENCE);
                    persCardDao.delete(masterCard);
                    // 同一个人，但是该卡为副卡,变更为主卡
                    if (Objects.nonNull(card) && card.getPersonPin().equals(pin)) {
                        card.setCardType(PersConstants.MAIN_CARD);
                        persCardDao.save(card);
                    } else {
                        // 重新发卡
                        createCard(persPerson,
                            new PersCardItem(cardNo, PersConstants.MAIN_CARD, PersConstants.CARD_VALID));
                    }
                    issueCard = true;
                }
            }
            // 通知其他模块下发发卡命令
            if (issueCard && persPersonExtServices != null) {
                List<PersCardItem> cardItemList = new ArrayList<>();
                PersCardItem cardItem = new PersCardItem();
                cardItem.setPersonId(persPerson.getId());
                cardItem.setCardNo(cardNo);
                cardItem.setPersonPin(pin);
                cardItemList.add(cardItem);
                Arrays.stream(persPersonExtServices).forEach(ps -> ps.issuedCard(cardItemList));
            }
        }
    }

    @Override
    public void saveMultiCard(List<PersCardItem> items) {
        if (CollectionUtil.isEmpty(items)) {
            return;
        }

        Collection<String> pins = CollectionUtil.getPropertyList(items, PersCardItem::getPersonPin, "-1");
        Collection<String> cardNos = CollectionUtil.getPropertyList(items, PersCardItem::getCardNo, "-1");
        // 查找所有人员
        List<PersPerson> personList = persPersonDao.findByPinIn(pins);
        // 查找各自人员的所有卡
        List<PersCard> personCardList = persCardDao.findByPersonPinIn(pins);
        // 查找已存在的卡
        List<PersCard> allExistCardList = persCardDao.findByCardNoIn(cardNos);

        Map<String, List<PersCardItem>> itemMap =
            items.stream().collect(Collectors.groupingBy(PersCardItem::getPersonPin));
        Map<String, PersPerson> personMap = CollectionUtil.listToKeyMap(personList, PersPerson::getPin);
        Map<String, List<PersCard>> cardMap =
            personCardList.stream().collect(Collectors.groupingBy(card -> card.getPersonPin()));
        Map<String, PersCard> allExistCardMap = CollectionUtil.listToKeyMap(allExistCardList, PersCard::getCardNo);

        itemMap.forEach((k, v) -> {
            saveMuiltCard(v, personMap.get(k), cardMap.get(k), allExistCardMap);
        });
    }

    private void saveMuiltCard(List<PersCardItem> items, PersPerson persPerson, List<PersCard> personCardList,
        Map<String, PersCard> allExistCardMap) {
        Map<String, PersCard> cardMap = CollectionUtil.listToKeyMap(personCardList, PersCard::getCardNo);
        items.forEach(item -> {
            String cardNo = item.getCardNo();
            PersCard existCard = allExistCardMap.get(cardNo);
            if (Objects.nonNull(existCard)) {
                PersCard persCard = cardMap.remove(cardNo);
                if (Objects.nonNull(persCard)) {
                    personCardList.remove(persCard);
                    if (item.getCardType().shortValue() != persCard.getCardType().shortValue()) {
                        persCard.setCardType(item.getCardType());
                        if (StringUtils.isNotBlank(item.getIsFrom())) {
                            persCard.setIsFrom(item.getIsFrom());
                        }
                        if (StringUtils.isNotBlank(item.getBussinessId())) {
                            persCard.setBussinessId(item.getBussinessId());
                        }
                        if (item.getCardOpType() != null) {
                            persCard.setCardOpType(item.getCardOpType());
                        }
                        persCardDao.save(persCard);
                    }
                } else {
                    // 卡号重复，但是不在该人员的卡中，要把这个人设置成卡号重复异常
                    persPersonService.updatePersonExceptionFlag(persPerson.getPin(), PersConstants.PERSON_ISEXCEPTION);
                    // 插入数据，但是无效卡
                    createCard(persPerson,
                        new PersCardItem(cardNo, item.getCardType(), PersConstants.CARD_LOSS,
                            StringUtils.isNotBlank(item.getIsFrom()) ? item.getIsFrom() : "",
                            StringUtils.isNotBlank(item.getBussinessId()) ? item.getBussinessId() : "",
                            item.getCardOpType() != null ? item.getCardOpType() : null));
                }
            } else {
                // 新增卡
                createCard(persPerson,
                    new PersCardItem(cardNo, item.getCardType(), item.getCardState(),
                        StringUtils.isNotBlank(item.getIsFrom()) ? item.getIsFrom() : "",
                        StringUtils.isNotBlank(item.getBussinessId()) ? item.getBussinessId() : "",
                        item.getCardOpType() != null ? item.getCardOpType() : null));
            }
        });
        // 激活acms卡
        activeAcmsCard(items, persPerson);
        // 删除的卡
        if (personCardList != null && personCardList.size() > 0) {
            List<PersCardItem> acmsCards = new ArrayList<>();
            personCardList.forEach(personCard -> {
                if (PersConstants.ACMS.equals(personCard.getIsFrom())
                    && StringUtils.isNotBlank(personCard.getBussinessId())) {
                    PersCardItem acmsCard = new PersCardItem();
                    acmsCard.setBussinessId(personCard.getBussinessId());
                    acmsCard.setCardNo(personCard.getCardNo());
                    acmsCard.setCardOpType(personCard.getCardOpType());
                    acmsCards.add(acmsCard);
                }
                persIssueCardService.createIssueInfo(persPerson.getPin(), persPerson.getName(),
                    persPerson.getLastName(), personCard.getCardNo(), PersConstants.INEXISTENCE);
                persCardDao.delete(personCard);
            });
            // 删除ACMS卡
            delteCardFromAcms(acmsCards);
        }
    }

    /**
     * 激活acms卡
     * 
     * @param persCardItemList:
     * @param persPerson:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-03-14 17:19
     * @since 1.0.0
     */
    private void activeAcmsCard(List<PersCardItem> persCardItemList, PersPerson persPerson) {
        PersPersonItem persPersonItem = ModelUtil.copyProperties(persPerson, new PersPersonItem());
        // 设置缩略图处理，并且判断文件是否存在。
        if (StringUtils.isNotBlank(persPersonItem.getPhotoPath())) {
            // 图片解密
            String thumbPath = FileUtil.getThumbPath(persPersonItem.getPhotoPath());
            if (FileUtil.fileExists(thumbPath)) {
                // 设置base64数据
                persPersonItem.setPhotoBase64(FileEncryptUtil.getDecryptFileBase64(thumbPath));
            } else {
                // 设置base64数据
                persPersonItem.setPhotoBase64(FileEncryptUtil.getDecryptFileBase64(persPersonItem.getPhotoPath()));
            }
        }
        List<String> acmsCardIdList = new ArrayList<>();
        for (PersCardItem cardItem : persCardItemList) {
            // 空闲卡修改卡号为未激活
            if (PersConstants.ACMS.equals(cardItem.getIsFrom())) {
                if (SystemAcmsUtil.ACMS_CARD_STATUS_IDLE.equals(cardItem.getStatus())) {
                    ZKResultMsg reassignCard = persAcmsService.reassignCard(cardItem.getBussinessId());
                    if (reassignCard.isSuccess()) {
                        persAcmsService.activeCredential(cardItem.getActivationCode(), persPersonItem.getEmail());
                    }
                } else {
                    // 发送邮箱激活此卡号
                    persAcmsService.activeCredential(cardItem.getActivationCode(), persPersonItem.getEmail());
                }
                acmsCardIdList.add(cardItem.getBussinessId());
            }
        }
        if (!acmsCardIdList.isEmpty()) {
            // 人员绑定卡号
            persAcmsService.assignPersonCard(acmsCardIdList, persPersonItem);
        }

    }

    /**
     * 删除ACMS上的卡
     * 
     * @param cardList:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-08-22 10:44
     * @since 1.0.0
     */
    private void delteCardFromAcms(List<PersCardItem> cardList) {
        if (!cardList.isEmpty()) {
            persAcmsService.delAcmsCard(cardList);
        }

    }

    @Override
    public void updatePersonCardInfo(String personId, String cardNos) {
        if (StringUtils.isBlank(cardNos)) {
            return;
        }
        String[] cardNoArr = cardNos.split(",");
        List<PersCardItem> items = new ArrayList<>(cardNoArr.length);
        for (int i = 0; i < cardNoArr.length; i++) {
            if (StringUtils.isNotBlank(cardNoArr[i])) {
                short cardType = PersConstants.DEPUTY_CARD;
                // 第一张卡需设置为主卡
                if (i == 0) {
                    cardType = PersConstants.MAIN_CARD;
                }
                items.add(new PersCardItem(cardNoArr[i].trim(), cardType, PersConstants.CARD_VALID));
            }
        }
        // 查找已存在的卡
        List<PersCard> allExistCardList = persCardDao.findByCardNoIn(CollectionUtil.strToList(cardNos));
        if (!CollectionUtil.isEmpty(allExistCardList)) {
            allExistCardList.stream().forEach(card -> {
                if (StringUtils.isNotBlank(card.getPersonId()) && !personId.equals(card.getPersonId())) {
                    throw ZKBusinessException.warnException("pers_import_cardExist", card.getCardNo());
                }
            });
        }
        Map<String, PersCard> allExistCardMap = CollectionUtil.listToKeyMap(allExistCardList, PersCard::getCardNo);
        PersPerson persPerson = persPersonDao.findById(personId).get();
        List<PersCard> cardList = persCardDao.findByPersonIdInOrderByCardType(Arrays.asList(personId));
        saveMuiltCard(items, persPerson, cardList, allExistCardMap);
    }

    @Override
    public void updatePersonCardInfo(String personId, List<PersCardItem> items) {
        List<String> cardNos = (List<String>)CollectionUtil.getPropertyList(items, PersCardItem::getCardNo, "-1");
        // 查找已存在的卡
        List<PersCard> allExistCardList = persCardDao.findByCardNoIn(cardNos);
        if (!CollectionUtil.isEmpty(allExistCardList)) {
            allExistCardList.stream().forEach(card -> {
                if (StringUtils.isNotBlank(card.getPersonId()) && !personId.equals(card.getPersonId())) {
                    throw ZKBusinessException.warnException("pers_import_cardExist", card.getCardNo());
                }
            });
        }
        Map<String, PersCard> allExistCardMap = CollectionUtil.listToKeyMap(allExistCardList, PersCard::getCardNo);
        PersPerson persPerson = persPersonDao.findById(personId).get();
        List<PersCard> cardList = persCardDao.findByPersonIdInOrderByCardType(Arrays.asList(personId));
        saveMuiltCard(items, persPerson, cardList, allExistCardMap);
    }

    private void createCard(PersPerson person, PersCardItem item) {
        if (StringUtils.isNotBlank(item.getCardNo())) {
            // 发放主卡逻辑
            PersCard card = new PersCard();
            card.setCardType(item.getCardType());
            card.setCardNo(item.getCardNo().toLowerCase().trim());
            card.setIssueTime(new Date());
            // 卡状态为空设置默认 有效卡
            card.setCardState(Objects.isNull(item.getCardState()) ? PersConstants.CARD_VALID : item.getCardState());
            card.setPersonId(person.getId());
            card.setPersonPin(person.getPin());
            if (StringUtils.isBlank(card.getIsFrom())) {
                card.setIsFrom(StringUtils.isNotBlank(item.getIsFrom()) ? item.getIsFrom() : "");
            }
            if (StringUtils.isBlank(card.getBussinessId())) {
                card.setBussinessId(StringUtils.isNotBlank(item.getBussinessId()) ? item.getBussinessId() : "");
            }
            if (card.getCardOpType() == null) {
                card.setCardOpType(item.getCardOpType() != null ? item.getCardOpType() : null);
            }
            // 创建卡的时候，自动生成逻辑卡，避免离线梯空或其它模块需要用到逻辑卡出现空情况，add by max 20161202
            String generateLogicalCardNo = card.getLogicalCardNo();
            if (StringUtils.isNotBlank(generateLogicalCardNo)) {
                card.setLogicalCardNo(generateLogicalCardNo());
            }
            persCardDao.save(card);
            item.setId(card.getId());
            persIssueCardService.createIssueInfo(person.getPin(), person.getName(), person.getLastName(),
                card.getCardNo(), Short.valueOf("1"));
        }
    }

    /**
     * 获取逻辑卡号
     */
    private String generateLogicalCardNo() {
        String tempLogicalCardNo = String.valueOf(getRandomInt());
        while (Objects.nonNull(persCardDao.findByLogicalCardNo(tempLogicalCardNo))) {
            tempLogicalCardNo = String.valueOf(getRandomInt());
        }
        return tempLogicalCardNo;
    }

    private int getRandomInt() {
        return random.nextInt(2147483647);
    }

    @Override
    public List<PersCardItem> getByCondition(PersCardItem condition) {
        return (List<PersCardItem>)persCardDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition));
    }

    @Override
    public Pager getItemsByPage(BaseItem condition, int page, int size) {
        buildCondition(null, (PersCardItem)condition);
        Pager pager = persCardDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        buildDept(pager.getData());
        return pager;
    }

    @Override
    public Pager loadPagerByAuthUserFilter(String sessionId, BaseItem condition, int page, int size) {
        buildCondition(sessionId, (PersCardItem)condition);
        Pager pager = persCardDao.getItemsBySql(condition.getClass(), SQLUtil.getSqlByItem(condition), page, size);
        return pager;
    }

    @Override
    public boolean deleteByIds(String ids) {
        String[] idArray = StringUtils.split(ids, ",");
        for (String id : idArray) {
            persCardDao.deleteById(id);
        }
        return true;
    }

    @Override
    public PersCardItem getItemById(String id) {
        return Optional.ofNullable(id).filter(StringUtils::isNotBlank).flatMap(persCardDao::findById)
            .filter(Objects::nonNull).map(card -> ModelUtil.copyProperties(card, new PersCardItem())).orElse(null);
    }

    @Override
    public List<PersCardItem> getItemByPersonId(String personId) {
        PersCardItem item = new PersCardItem(true);
        item.setPersonId(personId);
        item.setCardState(PersConstants.CARD_VALID);
        return getByCondition(item);
    }

    @Override
    public PersCardItem getMasterCardByPersonId(String personId) {
        // 根据人员ID查询主卡
        List<PersCard> cardList = persCardDao.findByPersonIdAndCardType(personId, PersConstants.MAIN_CARD);
        return Optional.ofNullable(cardList).filter(i -> !i.isEmpty()).map(i -> i.get(0))
            .map(card -> ModelUtil.copyProperties(card, new PersCardItem())).orElse(null);
    }

    @Override
    public List<PersCardItem> getItemByPersonIds(String personIds) {
        return getItemByPersonIdList(CollectionUtil.strToList(personIds));
    }

    @Override
    public List<PersCardItem> getItemByPersonIdList(Collection<String> personIds) {
        // 根据人员Id查询有效卡
        List<PersCard> cardList = persCardDao.findByPersonIdInAndCardState(personIds, PersConstants.CARD_VALID);
        return Optional.ofNullable(cardList).filter(i -> !i.isEmpty())
            .map(cards -> ModelUtil.copyListProperties(cards, PersCardItem.class)).orElse(Collections.emptyList());
    }

    @Override
    public List<PersCardItem> getAllCardByPersonIdList(Collection<String> personIds) {
        // 根据人员Id查询卡号
        List<PersCard> cardList = persCardDao.findByPersonIdInOrderByCardType(personIds);
        return Optional.ofNullable(cardList).filter(i -> !i.isEmpty())
            .map(cards -> ModelUtil.copyListProperties(cards, PersCardItem.class)).orElse(Collections.emptyList());
    }

    @Override
    public List<PersCardItem> getMasterCardByPersonIdList(Collection<String> personIds) {
        // 查询主卡且有效卡
        List<PersCard> cardList = persCardDao.findByPersonIdInAndCardTypeAndCardState(personIds,
            PersConstants.MAIN_CARD, PersConstants.CARD_VALID);
        return Optional.ofNullable(cardList).filter(i -> !i.isEmpty())
            .map(cards -> ModelUtil.copyListProperties(cards, PersCardItem.class)).orElse(Collections.emptyList());
    }

    @Override
    public List<PersCardItem> getMasterCardByCardNos(Collection<String> cardNoList) {
        // 查询主卡且有效卡
        List<PersCard> cardList = persCardDao.findByCardNoInAndCardTypeAndCardState(cardNoList, PersConstants.MAIN_CARD,
            PersConstants.CARD_VALID);
        return Optional.ofNullable(cardList).filter(i -> !i.isEmpty())
            .map(cards -> ModelUtil.copyListProperties(cards, PersCardItem.class)).orElse(Collections.emptyList());
    }

    @Override
    public List<PersCardItem> getCardByCardNos(Collection<String> cardNoList) {
        PersCardItem item = new PersCardItem();
        item.setCardNoIn(StringUtils.join(cardNoList, ",")).setCardState(PersConstants.CARD_VALID);
        return getByCondition(item);
    }

    @Override
    public PersCardItem getItemByCardNo(String cardNo) {
        return Optional.ofNullable(cardNo).filter(StringUtils::isNotBlank).map(String::toLowerCase)
            .map(persCardDao::findByCardNo).filter(cards -> !cards.isEmpty()).map(cards -> {
                if (cards.size() > 1) {
                    // 有多张卡，过滤有效卡即可
                    return cards.stream().filter(l -> l.getCardState() == PersConstants.CARD_VALID).findFirst()
                        .orElse(null);
                }
                return cards.get(0);
            }).map(card -> ModelUtil.copyProperties(card, new PersCardItem())).orElse(null);
    }

    @Override
    public Map<String, String> getPinsByCardNos(List<String> cardNos) {
        return Optional.ofNullable(cardNos).filter(i -> !i.isEmpty())
            .map(c -> persCardDao.findCardNoAndPersonByCardNosAndCardState(c, PersConstants.CARD_VALID))
            .map(
                l -> l.stream().filter((Object[] o) -> o.length > 1 && StringUtils.isNotBlank(o[1].toString())).collect(
                    Collectors.toMap((Object[] o) -> o[0].toString(), (Object[] o) -> o[2].toString(), (x, y) -> y)))
            .orElse(Collections.emptyMap());
    }

    @Override
    public Map<String, String> getPersonIdsByCardNos(List<String> cardNos) {
        return getPersonIdsByCardNosAndCardState(cardNos, PersConstants.CARD_VALID);
    }

    @Override
    public Map<String, String> getPersonIdsByCardNosAndCardState(List<String> cardNos, Short cardState) {
        return Optional.ofNullable(cardNos).filter(i -> !i.isEmpty())
            .map(c -> persCardDao.findCardNoAndPersonByCardNosAndCardState(c, cardState))
            .map(
                l -> l.stream().filter((Object[] o) -> o.length > 1 && StringUtils.isNotBlank(o[1].toString())).collect(
                    Collectors.toMap((Object[] o) -> o[0].toString(), (Object[] o) -> o[1].toString(), (x, y) -> y)))
            .orElse(Collections.emptyMap());
    }

    @Override
    public List<PersCardItem> getItemByCardNoLike(String cardNo) {
        List<PersCardItem> persCardItemList = new ArrayList<PersCardItem>();
        List<PersCard> persCardList = persCardDao.findByCardNOLike(cardNo);
        if (!CollectionUtil.isEmpty(persCardList)) {
            persCardItemList = ModelUtil.copyListProperties(persCardList, PersCardItem.class);
        }
        return persCardItemList;
    }

    @Override
    public boolean isExistByCardNoAndPin(String cardNo, String pin) {
        cardNo = cardNo.toLowerCase();
        if (StringUtils.isBlank(pin)) {
            // 新增时没有pin，用卡号查询即可
            return !CollectionUtil.isEmpty(persCardDao.findByCardNo(cardNo));
        }
        return persCardDao.countByCardNoAndPersonPinNot(cardNo, pin) > 0;
    }

    @Override
    public boolean isExistByCardNoAndPinAndOtherFilter(String cardNo, String pin) {
        boolean existCard = false;
        cardNo = cardNo.toLowerCase();
        if (StringUtils.isBlank(pin)) {
            // 新增时没有pin，用卡号查询即可
            existCard = !CollectionUtil.isEmpty(persCardDao.findByCardNo(cardNo));
            if (!existCard) {
                existCard = pers2OtherService.checkExistCardNo(cardNo);
            }
            return existCard;
        }
        existCard = persCardDao.countByCardNoAndPersonPinNot(cardNo, pin) > 0;
        if (!existCard) {
            existCard = pers2OtherService.checkExistCardNo(cardNo);
        }
        return existCard;
    }

    @Override
    public void batchCardLoss(String ids) {
        batchOperateCard(ids, PersConstants.CARD_LOSS, PersConstants.CARD_INVALID);
    }

    @Override
    public void batchCardRevert(String ids) {
        batchOperateCard(ids, PersConstants.CARD_VALID, PersConstants.CARD_SOLUTLINKED);
    }

    /**
     * 批量操作卡
     *
     * @param ids
     * @param cardState 卡状态，有效卡，无效卡
     * @param operateType 发卡状态 ，挂失，解挂
     */
    private void batchOperateCard(String ids, short cardState, short operateType) {
        List<PersCard> cards = persCardDao.findByIdIn(CollectionUtil.strToList(ids));
        if (!CollectionUtil.isEmpty(cards)) {
            String cardNos = CollectionUtil.getPropertys(cards, PersCard::getCardNo);
            Collection<String> personIdList = CollectionUtil.getPropertyList(cards, PersCard::getPersonId, "-1");
            List<PersPersonItem> persons = persPersonService.getSimpleItemsByIds(personIdList);
            if (PersConstants.CARD_SOLUTLINKED == operateType) {
                List<Boolean> enabledCredentialList =
                    (List<Boolean>)CollectionUtil.getPropertyList(persons, PersPersonItem::getEnabledCredential, true);
                // 存在禁用人员，不能解挂
                if (enabledCredentialList.contains(false)) {
                    throw ZKBusinessException.warnException("pers_card_disablePersonWarn");
                }
            }
            Map<String, PersPersonItem> personMap = CollectionUtil.itemListToIdMap(persons);

            for (PersCard persCard : cards) {
                // 改变卡状态
                persCard.setCardState(cardState);

                // 保存卡记录
                persIssueCardService.savePersIssueCard(persCard.getCardNo(), operateType,
                    personMap.get(persCard.getPersonId()));

                persCardDao.save(persCard);

            }
            // 通知其他模块挂失解挂
            sendOtherModule(operateType, cardNos);
        }
    }

    /**
     * 通知其他模块挂失解挂
     *
     * @param operateType
     * @param cardNos
     */
    private void sendOtherModule(short operateType, String cardNos) {
        if (Objects.nonNull(acc4PersCardService)) {
            if (operateType == PersConstants.CARD_INVALID) {
                acc4PersCardService.lossCard(cardNos);
            } else {
                acc4PersCardService.revertCard(cardNos);
            }
        }
        if (Objects.nonNull(ele4PersCardService)) {
            if (operateType == PersConstants.CARD_INVALID) {
                ele4PersCardService.lossCard(cardNos);
            } else {
                ele4PersCardService.revertCard(cardNos);
            }
        }
        if (Objects.nonNull(pos4PersCardService)) {
            if (operateType == PersConstants.CARD_INVALID) {
                pos4PersCardService.lossCard(cardNos);
            } else {
                pos4PersCardService.revertCard(cardNos);
            }
        }
        if (Objects.nonNull(posID4PersCardService)) {
            if (operateType == PersConstants.CARD_INVALID) {
                posID4PersCardService.lossCard(cardNos);
            } else {
                posID4PersCardService.revertCard(cardNos);
            }
        }
        if (Objects.nonNull(att4PersCardService)) {
            if (operateType == PersConstants.CARD_INVALID) {
                att4PersCardService.lossCard(cardNos);
            } else {
                att4PersCardService.revertCard(cardNos);
            }
        }
        if (Objects.nonNull(psg4PersCardService)) {
            if (operateType == PersConstants.CARD_INVALID) {
                psg4PersCardService.lossCard(cardNos);
            } else {
                psg4PersCardService.revertCard(cardNos);
            }
        }
        if (Objects.nonNull(ins4PersCardService)) {
            if (operateType == PersConstants.CARD_INVALID) {
                ins4PersCardService.lossCard(cardNos);
            } else {
                ins4PersCardService.revertCard(cardNos);
            }
        }
        // 添加业务模块扩展调用
        if (Objects.nonNull(persPersonExtServices)) {
            if (operateType == PersConstants.CARD_INVALID) {
                Arrays.stream(persPersonExtServices).forEach(ps -> ps.lossCard(cardNos));
            } else {
                Arrays.stream(persPersonExtServices).forEach(ps -> ps.revertCard(cardNos));
            }
        }
    }

    @Override
    public long count() {
        return persCardDao.count();
    }

    @Override
    public long countByCardType(short cardType) {
        return persCardDao.countByCardType(cardType);
    }

    @Override
    public List<String> findCardNoByCardStateAndPersonIds(Short cardState, Collection<String> personIds) {
        return persCardDao.findCardNoByCardStateAndPersonIdIn(cardState, personIds);
    }

    @Override
    public boolean checkCardBit(String cardNo) {
        // 如果超过了计算支持的最大卡号,暂不传到前端
        if (!checkCardisBeyond(cardNo)) {
            // 计算当前长度最大卡号
            BigInteger maxCard = new BigInteger("2")
                .pow(Integer.parseInt(baseSysParamService.getValByName("pers.cardLen"))).subtract(new BigInteger("1"));
            String maxCardNo = "";
            if (PersConstants.CARD_HEXADECIMAL.equals(baseSysParamService.getValByName("pers.cardHex"))) {
                maxCardNo = maxCard.toString(16);
            } else {
                maxCardNo = maxCard.toString();
            }
        }
        return checkCardisBeyond(cardNo);
    }

    @Override
    public Map<String, String> cardVerification(String cardNo) {
        Map<String, String> refJson = new HashMap<String, String>();
        // 判断是否还有空格
        String tempCardNo = cardNo.replace(" ", "");
        if (cardNo.length() > tempCardNo.length()) {
            refJson.put("result", "cardValidate");
        }
        // 判断是否支持字母
        String cardLetter = baseSysParamService.getValByName("pers.cardSupportLetter");
        if ("false".equals(cardLetter) && !StringUtils.isNumeric(cardNo)) {
            refJson.put("result", "cardLetter");
        }
        // 判断卡号是否存在
        List<PersCard> temPersCardList = persCardDao.findByCardNo(cardNo);
        if (!CollectionUtil.isEmpty(temPersCardList)) {
            refJson.put("result", String.valueOf(false));
        } else {
            refJson.put("result", "success");
        }
        return refJson;
    }

    @Override
    public void batchSaveCards(Map<String, List<String>> pinAndCardsMap, Map<String, String> pinAndIdMap) {
        Date issueTime = new Date();
        List<List<String>> pinsList = CollectionUtil.split(pinAndCardsMap.keySet(), CollectionUtil.splitSize);
        pinsList.forEach(pins -> {
            List<PersCard> cardList = persCardDao.findByPersonPinIn(pins);
            Set<String> pinSet = new HashSet<>(pins);
            Map<String, List<PersCard>> pinAndPersCards =
                cardList.stream().collect(Collectors.groupingBy(PersCard::getPersonPin));
            pinAndPersCards.forEach((pin, persCardList) -> {
                List<String> newCards = pinAndCardsMap.remove(pin);
                pinSet.remove(pin);
                Map<String, PersCard> cardNoAndPersCardMap = persCardList.stream()
                    .collect(Collectors.toMap(PersCard::getCardNo, Function.identity(), (x, y) -> y));
                for (int i = 0; i < newCards.size(); i++) {
                    String cardNo = newCards.get(i);
                    PersCard persCard = cardNoAndPersCardMap.remove(cardNo);
                    if (Objects.isNull(persCard)) {
                        persCard = new PersCard();
                        persCard.setPersonPin(pin).setPersonId(pinAndIdMap.get(pin))
                            .setCardState(PersConstants.CARD_VALID).setIssueTime(issueTime).setCardNo(cardNo)
                            .setCardType(i == 0 ? PersConstants.MAIN_CARD : PersConstants.DEPUTY_CARD);
                        persCardDao.save(persCard);
                    } else {
                        if (i == 0) {
                            // 原卡不是主卡，变更为主卡。需添加变更卡记录
                            if (PersConstants.MAIN_CARD != persCard.getCardType()) {
                                persCard.setCardType(PersConstants.MAIN_CARD);
                                persCardDao.save(persCard);
                            }
                        } else {
                            // 原卡为主卡，变更为副卡。需添加变更卡记录
                            if (PersConstants.DEPUTY_CARD != persCard.getCardType()) {
                                persCard.setCardType(PersConstants.DEPUTY_CARD);
                                persCardDao.save(persCard);
                            }
                        }
                    }
                }
                // 多余的卡要删除
                if (!CollectionUtil.isEmpty(pinAndPersCards)) {
                    cardNoAndPersCardMap.forEach((p, card) -> {
                        persCardDao.delete(card);
                    });
                }
            });

            // 剩余的人员的卡都是新增
            if (!CollectionUtil.isEmpty(pinSet)) {
                pinSet.forEach(pin -> {
                    List<String> newCards = pinAndCardsMap.remove(pin);
                    PersCard persCard = null;
                    for (int i = 0; i < newCards.size(); i++) {
                        String cardNo = newCards.get(i);
                        persCard = new PersCard();
                        persCard.setPersonPin(pin).setPersonId(pinAndIdMap.get(pin))
                            .setCardState(PersConstants.CARD_VALID).setIssueTime(issueTime).setCardNo(cardNo)
                            .setCardType(i == 0 ? PersConstants.MAIN_CARD : PersConstants.DEPUTY_CARD);
                        persCardDao.save(persCard);
                    }
                });
            }
        });
    }

    @Override
    public void deleteByPersonPinIn(Collection<String> pins) {
        persCardDao.deleteByPersonPinIn(pins);
    }

    @Override
    public void deleteByPersonId(String personId) {
        List<PersCard> persCardList = persCardDao.findByPersonIdInOrderByCardType(Arrays.asList(personId));
        persCardDao.deleteByPersonId(personId);
        if (!persCardList.isEmpty()) {
            List<PersCardItem> acmsCards = new ArrayList<>();
            persCardList.forEach(personCard -> {
                if (PersConstants.ACMS.equals(personCard.getIsFrom())
                    && StringUtils.isNotBlank(personCard.getBussinessId())) {
                    PersCardItem acmsCard = new PersCardItem();
                    acmsCard.setBussinessId(personCard.getBussinessId());
                    acmsCard.setCardNo(personCard.getCardNo());
                    acmsCard.setCardOpType(personCard.getCardOpType());
                    acmsCards.add(acmsCard);
                }
            });
            delteCardFromAcms(acmsCards);
        }
    }

    @Override
    public int isExitCardNo(PersPersonItem persPersonItem, String cardNos) {
        String cardhex = baseSysParamService.getValByName("pers.cardHex");
        Collection<String> cardNoList = CollectionUtil.strToList(cardNos);
        int ret = PersConstants.OP_SUCCESS;
        for (String cardNo : cardNoList) {
            if (PersConstants.CARD_HEXADECIMAL.equals(cardhex)) {
                // 比较时去除开头的0
                cardNo = new BigInteger(cardNo, 16).toString(16);
            } else {
                // 比较时去除开头的0
                cardNo = new BigInteger(cardNo).toString();
            }
            // 判断卡号是否已经在使用
            if (isExistByCardNoAndPin(cardNo, persPersonItem.getPin())) {
                // 设置为卡号重复异常
                ret = PersConstants.CART_HASUSE;
            }
        }
        return ret;
    }

    @Override
    public void handlerTransfer(List<PersCardItem> cardItems) {
        // 数据量大的时候处理，分批处理
        List<List<PersCardItem>> cardItemList = CollectionUtil.split(cardItems, CollectionUtil.splitSize);
        // 获取人员集合 add by hql 20190805
        List<PersPerson> personList = persPersonDao.findAll();
        Map<String, PersPerson> persPersonMap = CollectionUtil.listToKeyMap(personList, PersPerson::getPin);
        for (List<PersCardItem> items : cardItemList) {
            // 获取已存在的卡号，批量获取，减少不必要的查询
            Collection<String> cardNos = CollectionUtil.getPropertyList(items, PersCardItem::getCardNo, "-1");
            List<PersCard> cardList = persCardDao.findByCardNoIn(cardNos);
            Map<String, PersCard> cardMap = new HashMap<>();
            for (PersCard persCard : cardList) {
                String key = persCard.getPersonPin() + "_" + persCard.getCardNo();
                cardMap.put(key, persCard);
            }
            for (PersCardItem cardItem : items) {
                PersCard card = cardMap.remove(cardItem.getPersonPin() + "_" + cardItem.getCardNo());
                if (Objects.isNull(card)) {
                    card = new PersCard();
                }
                ModelUtil.copyPropertiesIgnoreNullWithProperties(cardItem, card, "id");
                PersPerson person = persPersonMap.get(cardItem.getPersonPin());
                card.setPersonId(person.getId());
                card.setPersonPin(person.getPin());
                persCardDao.save(card);
            }
        }
    }

    /**
     * 校验卡号是否符合当前控制的卡号长度
     *
     * @param cardNo
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/23 18:06
     */
    private boolean checkCardisBeyond(String cardNo) {
        boolean isBeyond = false;
        Integer cardBitLen = Integer.parseInt(baseSysParamService.getValByName("pers.cardLen"));
        String cardHex = baseSysParamService.getValByName("pers.cardHex");
        String cardNoBinary = "";
        if (cardHex.equals(PersConstants.CARD_DECIMAL)) {
            cardNoBinary = PersPersonUtil.convertCardNo(cardNo, 10, 2);
        } else {
            cardNoBinary = PersPersonUtil.convertCardNo(cardNo, 16, 2);
        }
        isBeyond = cardNoBinary.length() > cardBitLen;
        return !isBeyond;
    }

    /**
     * 封装条件
     *
     * @param persCardItem
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:18
     */
    private void buildCondition(String sessionId, PersCardItem persCardItem) {
        // 封装部门条件
        String userId = authUserService.getUserIdBySessionId(sessionId);
        if (StringUtils.isNotBlank(userId)) {
            persCardItem.setUserId(userId);
        }
        if (StringUtils.isBlank(persCardItem.getInDeptId())) {
            if (StringUtils.isNotBlank(persCardItem.getDeptId())) {
                List<String> deptIdList = authDepartmentService.getChildDeptIdsByDeptIds(persCardItem.getDeptId());
                persCardItem.setInDeptId(StrUtil.collectionToStr(deptIdList));
            }
        } else if (StringUtils.isNotBlank(sessionId)) {
            persCardItem.setInDeptId(persCardItem.getInDeptId());
        }
        persCardItem.setDeptId(null);
        // 条件卡号转换成小写在进去查询
        if (StringUtils.isNotBlank(persCardItem.getCardNo())) {
            persCardItem.setCardNo(persCardItem.getCardNo().toLowerCase());
        }
    }

    private PersCardItem buildDept(PersCardItem item) {
        AuthDepartmentItem dept = authDepartmentService.getItemById(item.getDeptId());
        item.setDeptCode(dept.getCode());
        item.setDeptName(dept.getName());
        return item;
    }

    private List<?> buildDept(List<?> items) {
        List<PersCardItem> list = (List<PersCardItem>)items;
        String deptIds = CollectionUtil.getPropertys(list, PersCardItem::getDeptId);
        List<AuthDepartmentItem> deptItems = authDepartmentService.getItemsByIds(deptIds);
        Map<String, AuthDepartmentItem> deptMap = CollectionUtil.itemListToIdMap(deptItems);
        AuthDepartmentItem dept = null;
        for (PersCardItem item : list) {
            dept = deptMap.get(item.getDeptId());
            item.setDeptCode(dept.getCode());
            item.setDeptName(dept.getName());
        }
        return items;
    }

    @Override
    public void saveCards(List<PersCardItem> persCardItemList) {
        for (PersCardItem persCardItem : persCardItemList) {
            PersCard persCard = null;
            if (StringUtils.isNotBlank(persCardItem.getId())) {
                persCard = persCardDao.findById(persCardItem.getId()).orElse(persCard);
            } else {
                persCard = new PersCard();
            }
            if (persCard != null) {
                ModelUtil.copyProperties(persCardItem, persCard);
                persCardDao.save(persCard);
            }
        }
    }

    @Override
    public void encryptPersCard() {
        if (isOpenSecurityEncrypt) {
            List<PersCard> persCardList = persCardDao.findAll();
            for (PersCard persCard : persCardList) {
                persCard.setCardNo(FoldexUtil.encryptByRandomSey(persCard.getCardNo()));
            }
            if (persCardList.size() > 0) {
                List<List<PersCard>> cardList = CollectionUtil.split(persCardList, CollectionUtil.splitSize);
                cardList.forEach(cards -> {
                    persCardDao.saveAll(cards);
                });
            }
        }
    }

    @Override
    public List<?> getItemData(Class<PersCardItem> persCardItemClass, BaseItem condition, int beginIndex,
        int endIndex) {
        return persCardDao.getItemsDataBySql(persCardItemClass, SQLUtil.getSqlByItem(condition), beginIndex, endIndex,
            true);
    }

    @Override
    public List<?> getItemDataByAuthUserFilter(String sessionId, Class<PersCardItem> persCardItemClass,
        BaseItem condition, int beginIndex, int endIndex) {
        buildCondition(sessionId, (PersCardItem)condition);
        return persCardDao.getItemsDataBySql(persCardItemClass, SQLUtil.getSqlByItem(condition), beginIndex, endIndex,
            true);
    }

    @Override
    public List<PersCardItem> protectPinAndCard(List<PersCardItem> items) {
        if (persPersonService.isProtectData()) {
            items.forEach(item -> {
                String pin = item.getPersonPin();
                item.setPersonPin(PersRegularUtil.hideWithAsterisk(pin));
                String cardNo = item.getCardNo();
                item.setCardNo(PersRegularUtil.hideWithAsterisk(cardNo));
            });
        }
        return items;
    }

    @Override
    public void updateCardState(Short cardState, List<String> personIds) {
        if (cardState != null && !personIds.isEmpty()) {
            persCardDao.updateCardState(cardState, personIds);
        }

    }
}