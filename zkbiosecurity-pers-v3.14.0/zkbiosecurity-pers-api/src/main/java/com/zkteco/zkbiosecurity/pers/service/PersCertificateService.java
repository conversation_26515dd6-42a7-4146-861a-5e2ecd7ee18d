/**
 * File Name: PersCertificate Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.List;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersCertificateItem;

/**
 * 对应百傲瑞达 PersCertificateService
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersCertificateService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    PersCertificateItem saveItem(PersCertificateItem item);

    /**
     * 批量保存item实体
     * 
     * <AUTHOR>
     * @param itemList
     * @return void
     * @date 2019/7/10 15:50
     */
    void batchSaveCertificates(List<PersCertificateItem> itemList);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<PersCertificateItem> getByCondition(PersCertificateItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    PersCertificateItem getItemById(String id);

    /**
     * 根据人员ID查询证件
     * 
     * @param personId
     * @return
     */
    PersCertificateItem getItemByPersonId(String personId);

    /**
     * 根据证件号码和类型和非personId查询
     * 
     * @param certNumber
     * @param certType
     * @param personId
     * @return
     */
    boolean isExistByCertNumberAndCertTypeAndPersonIdNe(String certNumber, String certType, String personId);

    /**
     * 证件数据迁移
     *
     * @param certificateItems
     */
    void handlerTransfer(List<PersCertificateItem> certificateItems);

    /**
     * 根据证件号码和类型和非personPin查询
     * 
     * <AUTHOR>
     * @param certType, certNumber, personPin
     * @return com.zkteco.zkbiosecurity.pers.vo.PersCertificateItem
     * @date 2019/7/11 16:22
     */
    PersCertificateItem getItemByTypeAndNumberAndPersonPinNe(String certType, String certNumber, String personPin);

    /**
     * 根据人员pin获取证件
     * 
     * <AUTHOR>
     * @param pins
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersCertificateItem>
     * @date 2019/7/18 9:29
     */
    List<PersCertificateItem> getItemByPersonPinIn(Collection<String> pins);

    /**
     * 根据人员id获取证件
     * 
     * <AUTHOR>
     * @param ids
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersCertificateItem>
     * @date 2019/7/22 9:29
     */
    List<PersCertificateItem> getItemByPersonIdIn(Collection<String> ids);

    /**
     * 加密certNumber字段
     * 
     * @return void
     * <AUTHOR>
     * @throws @date 2021-06-23 10:52
     * @since 1.0.0
     */
    void encryptPersCertificate();
}