/**
 * File Name: AccDoor
 * Created by GenerationTools on 2018-03-03 上午11:59
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersDeviceSelectItem;

/**
 * 考勤点门双列表接口
 * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @date 2018/6/19 20:35
 * @return
 */
public interface PersDeviceService extends BaseService {

    Pager getSelectDeviceItemByPage(PersDeviceSelectItem condition, int page, int size);

	
}