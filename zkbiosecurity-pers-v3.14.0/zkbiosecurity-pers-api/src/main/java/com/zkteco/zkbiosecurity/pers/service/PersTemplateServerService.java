package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

public interface PersTemplateServerService {

    /**
     * 登录获取token值
     * 
     * @param address:
     * @param username:
     * @param pwd:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-04-24 17:24
     * @since 1.0.0
     */
    ZKResultMsg loginTemplateServer(String address, String username, String pwd);

    /**
     * 服务器状态检测
     * 
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-04-24 17:24
     * @since 1.0.0
     */
    ZKResultMsg testTemplateServerStatus();

    /**
     * 根据照片路径提取人脸模板
     * 
     * @param photoPath:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-05-09 14:17
     * @since 1.0.0
     */
    ZKResultMsg getFaceTemplateByPhoto(String photoPath);
}
