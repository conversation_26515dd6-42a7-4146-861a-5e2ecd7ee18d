/**
 * File Name: PersPerson Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.*;

/**
 * 对应百傲瑞达 PersPersonService
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
public interface PersPersonService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    PersPersonItem saveItem(PersPersonItem item, PersCertificateItem certificate, PersAttributeExtItem attributeExt,
        Map<String, String> extParams);

    /**
     * 批量保存人员
     *
     * @param personItems
     * @param module ConstUtil.SYSTEM_MODULE_ACC ConstUtil.SYSTEM_MODULE_ATT ConstUtil.SYSTEM_MODULE_IDENTIFICATION
     */
    List<PersPersonItem> batchSaveItem(List<PersPersonItem> personItems, String module);

    /**
     * 保存人员头像,给各个业务模块，设备上传保存头像用的。
     *
     * @param pin
     * @param photoBase64
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/21 10:41
     */
    void saveUserPhoto(String pin, String photoBase64);

    /**
     * 用于批量上传人员头像，并通知其他业务模块更新
     *
     * @param pin
     * @param photoPath
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/21 10:41
     */
    ZKResultMsg uploadUserPhoto(String pin, String photoPath);

    /**
     * 用于批量上传人员头像和抠图，并通知其他业务模块更新
     * 
     * @param persImportPhotoItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2023-03-17 17:39
     * @since 1.0.0
     */
    ZKResultMsg uploadUserPhoto(PersImportPhotoItem persImportPhotoItem);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<PersPersonItem> getByCondition(PersPersonItem condition);

    /**
     * 根据权限用户和ID查询
     *
     * @param sessionId
     * @param condition
     * @return
     */
    List<PersPersonItem> getByAuthUserAndCondition(String sessionId, PersPersonItem condition);

    /**
     * 根据权限用户查询人员
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthUserFilter(String sessionId, BaseItem condition, int pageNo, int pageSize);

    /**
     * 根据登录sessionId,部门名称，部门编号，人员编号，人员名称 获取对应的人员PIN
     *
     * @param sessionId
     * @param condition
     * @return java.util.List<java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/14 18:14
     */
    List<String> getPersonPinByDeptPersAuthUserFilter(String sessionId, PersPersonItem condition);

    /**
     * 获取所有有权限的人员，用于导出人员的对比照片
     *
     * @param sessionId
     * @param condition
     * @return java.util.HashMap<String , String>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/29 10:54
     */
    Map<String, String> getAllPersonPhotoByAuthUserFilter(String sessionId, PersPersonPinItem condition);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    PersPersonItem getItemById(String id);

    /**
     * 根据ID查询,简单的人员数据，没有卡和部门其他属性的数据
     *
     * @param id
     * @return
     */
    PersPersonItem getSimpleItemById(String id);

    /**
     * 人事许可校验
     *
     * @param
     * @return boolean
     * <AUTHOR>
     * @date 2018/7/9 10:23
     */
    boolean persLicenseCheck();

    /**
     * 根据ids查询列表
     *
     * @param ids
     * @return
     */
    List<PersPersonItem> getItemsByIds(Collection<String> ids);

    /**
     * 根据ids查询列表,只返回本表的数据，不返回其他关联表数据
     *
     * @param ids
     * @return
     */
    List<PersPersonItem> getSimpleItemsByIds(Collection<String> ids);

    /**
     * 根据ids查询列表
     *
     * @param ids
     * @return
     */
    List<PersPersonItem> getItemsByIds(String ids);

    /**
     * 根据pins查询列表
     *
     * @param pins
     * @return
     */
    List<PersPersonItem> getItemsByPins(Collection<String> pins);

    /**
     * 根据pins查询列表
     *
     * @param pins
     * @return
     */
    List<PersPersonItem> getItemsByPins(String pins);

    /**
     * 根据cardNos查询列表
     *
     * @param cardNos
     * @return
     */
    List<PersPersonItem> getItemsByCardNos(String cardNos);

    /**
     * 根据pin查询
     *
     * @param pin
     * @return
     */
    PersPersonItem getItemByPin(String pin);

    /**
     * 根据工号人员是否存在
     *
     * @param pin
     * @return java.lang.Boolean
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/21 10:09
     */
    Boolean isExistPin(String pin);

    /**
     * 获取所有人员的数量
     *
     * @return
     */
    Integer getAllPersonCount();

    /**
     * 根据部门查询人员数量
     *
     * @param deptIds
     * @return
     */
    Integer getPersonCountByDeptIds(String deptIds);

    /**
     * 数据统计
     *
     * @return
     */
    Map<String, String> dataCount();

    /**
     * 根据用户权限统计数据
     *
     * @return
     */
    Map<String, String> dataCountByAuthUserFilter(String userId);

    /**
     * 根据部门id 用户权限统计数据
     * 
     * @param sessionId:
     * @param deptIds:
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @throws
     * @date 2022-08-18 14:30
     * @since 1.0.0
     */
    Map<String, String> dataCountByDeptIdAndAuthUserFilter(String sessionId, String deptIds);

    /**
     * 获取最大pin长度
     *
     * @return
     */
    Long getMaxPinLenth();

    /**
     * 获取最大pin
     *
     * @return
     */
    Long getMaxPin();

    /**
     * 获取下一个自增pin值
     *
     * @param
     * @return int
     * <AUTHOR>
     * @date 2018/7/6 9:52
     */
    String getIncPoint();

    /**
     * 批量调整职位
     *
     * @param ids
     * @param positionId
     * @param changeReason
     * @return
     */
    void batchPositionChange(String ids, String positionId, String changeReason);

    /**
     * 批量调整部门
     *
     * @param ids
     * @param deptId
     * @param changeReason
     * @param flag
     * @return
     */
    void batchDeptChange(String ids, String deptId, String changeReason, boolean flag);

    /**
     * 查询没有卡的人员
     *
     * @param condition
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonSelectItem>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/4/25 20:20
     */
    @Deprecated
    List<PersPersonSelectItem> findNoCardPersonItem(String sessionId, PersPersonSelectItem condition);

    /**
     * 查询没有卡的人员 按分页查找
     *
     * @param sessionId
     * @param condition
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/21 14:10
     */
    Pager findNoCardPerson(String sessionId, PersNoCardPersonItem condition, int page, int size);

    /**
     * 根据人员选择条件查询
     *
     * @param sessionId
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager findPersonSelectItem(String sessionId, PersPersonSelectItem condition, int page, int size);

    /**
     * 根据人员选择条件查询
     *
     * <AUTHOR>
     * @Param sessionId
     * @Param condition
     * @Param page
     * @Param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * @date 2020/5/25 15:12
     */
    Pager findIsNotNullPersonSelectItem(String sessionId, PersPersonSelectItem condition, int page, int size);

    /**
     * 根据人员选择条件查询（部门是否包含下级）
     *
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/20 18:47
     * @param sessionId
     * @param condition
     * @param page
     * @param size
     * @return: com.zkteco.zkbiosecurity.base.bean.Pager
     **/
    Pager findPersonSelectItemAndIsIncludeLower(String sessionId, PersPersonSelectItem condition, int page, int size);

    /**
     * 支持InId、InlinkId、InDeptId的OR查询（用于已排班、未排班人员）
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/7 19:01
     * @param persPersonSelectItem
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager findItemByOrConditionForAtt(PersPersonSelectItem persPersonSelectItem, int pageNo, int pageSize);

    /**
     * 根据人员选择条件查询获取导出报表数据
     *
     * @param sessionId 登陆用户
     * @param condition 条件
     * @param beginIndex 开始索引 从0开始
     * @param endIndex 结束索引
     * @author: mingfa.zheng
     * @date: 2018/6/27 20:57
     * @return:
     */
    List<PersPersonSelectItem> getPersonSelectItem(String sessionId, PersPersonSelectItem condition, int beginIndex,
        int endIndex);

    /**
     * 根据业务ID和类型查询
     *
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager findPersonLinkSearchItem(String sessionId, PersPersonLinkSearchItem condition, int page, int size);

    /**
     * 查询区域人员，考勤用
     *
     * @param condition
     * @return
     */
    @Deprecated
    List<PersPersonToAttAreaItem> findPersonToAttAreaItem(String sessionId, PersPersonToAttAreaItem condition);

    /**
     * 根据权限组ID和人员id查询人员信息
     *
     * @author: mingfa.zheng
     * @date: 2018/4/26 20:57
     * @return:
     */
    List<String> getPersonIdByPersonIdAndLevelIdForAcc(List<String> personIds, List<String> levelIds);

    /**
     * 判断密码是否存在
     *
     * @return
     */
    boolean ckeckPwd(String personPwd);

    /**
     * 判断密码是否存在
     *
     * @return
     */
    boolean checkPwd(String personId, String personPwd);

    /**
     * 检测是否存在胁迫密码
     *
     * @param personPwd
     * @return
     */
    boolean checkForcePwd(String personPwd);

    /**
     * @param
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/10 15:06
     */
    boolean checkMailParam();

    /**
     * 获取人员pins
     *
     * @param personIds
     * @return java.util.Map<java.lang.String , java.lang.String>,key为pin,value为id
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/21 13:54
     */
    Map<String, String> getPinsByPersonIds(Collection<String> personIds);

    /**
     * 根据pin号获取人员pinh和id的对应关系集合
     *
     * @param pinList
     * @return java.util.Map<java.lang.String , java.lang.String>
     * <AUTHOR>
     * @date 2018/8/06 13:54
     */
    Map<String, String> getPinsAndIdsByPins(Collection<String> pinList);

    /**
     * 根据pin号、姓名拼音查找人员
     *
     * @param pin
     * @param nameSpell
     * @return
     * <AUTHOR>
     * @since 2018年6月19日 上午11:20:03
     */
    List<PersPersonItem> getByPinAndNamePell(Integer pin, String nameSpell);

    /**
     * 根据权限组ID、人员id和人员关联的梯控模块类型查询人员信息
     */
    List<String> getPersonIdByPersonIdAndLevelIdForEle(List<String> personIds, List<String> levelIds);

    /**
     * 根据权限组ID、人员id和人员关联的通道模块类型查询人员信息
     */
    List<String> getPersonIdByPersonIdAndLevelIdForPsg(List<String> personIds, List<String> levelIds);

    /**
     * 查询pin是否含有字母，检查pin是否支持自增
     *
     * @return int
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/22 9:31
     */
    int checkPinIncrement(String pinSupportIncrement);

    /**
     * 判断Pin是否存在字母
     *
     * @param
     * @return int
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/22 10:25
     */
    int checkPinIsExistLetters();

    /**
     * 根据人员编号或者姓名获取人员集合
     *
     * @param pin
     * @param name
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonItem>
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/6/25 14:53
     */
    List<PersPersonItem> getByPinLikeOrNameLike(String pin, String name);

    /**
     * 通过部门ID获取人员集合
     *
     * @param deptIds
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonItem>
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/16 14:13
     */
    List<PersPersonItem> getPersPersonByDeptIds(Collection<String> deptIds);

    /**
     * 通过人事姓名获取人员
     *
     * @param name
     * @return
     */
    PersPersonItem getItemByName(String name);

    /**
     * 导入人员数据
     *
     * @param itemList
     * @return
     */
    ZKResultMsg importData(List<PersPersonItem> itemList);

    /**
     * 导入人员数据及自定义属性
     *
     * <AUTHOR>
     * @param itemList
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @date 2019/7/10 11:35
     */
    ZKResultMsg importPersonIno(List<PersPersonImportItem> itemList);

    /**
     * 获取导出数据
     *
     * @return
     */
    List<PersPersonItem> getItemData(Class<PersPersonItem> persPersonItemClass, PersPersonItem persPersonItem,
        int beginIndex, int endIndex);

    /**
     * 根据登陆用户获取授权的部门id
     *
     * @param sessionId
     * @return
     */
    String getDeptIdsByAuthFilter(String sessionId);

    /**
     * 更新人员异常状态
     *
     * @param pin
     * @param exceptionFlag
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/8/13 19:44
     */
    void updatePersonExceptionFlag(String pin, Short exceptionFlag);

    /**
     * 获取当前软件的人员数量
     *
     * @return
     */
    Long getPersonCount();

    /**
     * 获取云服务所需要的人员数据
     *
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     */
    List<PersPersonCloudItem> getPersonCloudItems(PersPersonCloudItem condition, int pageNo, int pageSize);

    /**
     * 手机app修改用户自助密码
     *
     * @param pin
     * @param oldPassword
     * @param newPassword
     * @param checkPassword
     * @return
     */
    boolean changeSelfPwd(String pin, String oldPassword, String newPassword, boolean checkPassword);

    /**
     * 手机app修改用户信息
     *
     * @param person
     * @param persCertificateItem
     * @param persAttributeExtItem
     * @return
     */
    boolean editAppPerson(PersPersonItem person, PersCertificateItem persCertificateItem,
        PersAttributeExtItem persAttributeExtItem);

    /**
     * 删除人员
     *
     * @param ids
     * @return
     */
    @Override
    boolean deleteByIds(String ids);

    /**
     * 重置员工自助登录密码
     *
     * @param personIds
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/10/10 10:38
     */
    void resetSelfPwd(String personIds);

    /**
     * 修改员工自助登陆密码，给手机app调用
     *
     * @param personId
     * @param selfPwd
     */
    void updateSelfPwd(String personId, String selfPwd);

    /**
     * 人员数据迁移
     *
     * @param persPersonItems
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/28 14:55
     */
    void handlerTransfer(List<PersPersonItem> persPersonItems);

    /**
     * 查询区域人员，信息屏用
     *
     * @param sessionId
     * @param persPersonToInsAreaItem
     * @return
     */
    @Deprecated
    List<PersPersonToInsAreaItem> findPersonToInsAreaItem(String sessionId,
        PersPersonToInsAreaItem persPersonToInsAreaItem);

    /**
     * 根据人员编号和时间段查询这段时间入职的人员
     *
     * @param pins
     * @param beginDate
     * @param endDate
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonItem>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2019/2/11 16:27
     */
    List<PersPersonItem> findByPinsAndHireDate(String pins, Date beginDate, Date endDate);

    /**
     * 根据人员区域对象条件查询
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2019/2/13 15:23
     * @param sessionId
     * @param condition
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager findPersonAreaItem(String sessionId, PersPersonAreaItem condition, int page, int size);

    /**
     * 根据key值获取国籍名称
     *
     * <AUTHOR>
     * @param countryKey
     * @return java.lang.String
     * @date 2019/2/20 17:52
     */
    String getCountry(String countryKey);

    /**
     * 根据部门id获取人员id
     *
     * @param deptIds
     * @return
     */
    List<String> getPersonIdsByDeptIds(List<String> deptIds);

    /**
     * 根据部门id获取启用凭证人员id
     *
     * @param deptIds:
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2023-10-30 17:28
     * @since 1.0.0
     */
    List<String> getEnabledCredentialPersonIdsByDeptIds(List<String> deptIds);

    /**
     * 根据姓名，手机号，部门模糊查询人员信息
     *
     * <AUTHOR>
     * @param name, mobilePhone, deptIdList, type
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonEmpItem>
     * @date 2019/5/7 14:54
     */
    List<PersPersonEmpItem> getEmpByNameOrPhoneOrDept(String name, String mobilePhone, List<String> deptIdList,
        String type);

    /**
     * 根据人员id获取人员vo（针对下发使用）
     *
     * @param personIds
     * @return
     */
    List<PersPersonItem> getPersonItemForDevByIds(Collection<String> personIds);

    /**
     * 封装条件
     *
     * @param condition
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/14 14:18
     */
    void buildCondition(String sessionId, PersPersonItem condition);

    /**
     * 根据Pin查询人员照片路径
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/5/23 15:36
     * @param pin
     * @return java.lang.String
     */
    String getPersonPhotoPathByPin(String pin);

    /**
     * 功能描述:根据人员区域对象条件查询简单的区域人员信息
     *
     * <AUTHOR>
     * @since 2019-05-31 18:44
     * @Param [sessionId, persPersonAreaItem, pageNo, pageSize]
     * @return
     */
    Pager findSimplePersonAreaItem(String sessionId, PersPersonAreaItem persPersonAreaItem, int pageNo, int pageSize);

    /**
     * 根据最后一次推送后更新的人员
     *
     * @auther zbx.zhong
     * @date 2019/7/8 17:43
     * @param lastUpdate
     * @return
     */
    List<PersPersonCloudItem> getUpdatePersonItem(Date lastUpdate);

    /**
     * 组装导出数据 加证件类型 自定义属性
     *
     * <AUTHOR>
     * @param persPersonItemList
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonExportItem>
     * @date 2019/7/3 17:57
     */
    List<PersPersonExportItem> analyzeExportData(List<PersPersonItem> persPersonItemList);

    /**
     * 处理上传的照片压缩包文件
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/7/23 18:05
     * @param filePath 文件路径
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg handlerZipUserPhoto(String filePath);

    /**
     * 处理上传的照片压缩包文件
     * 
     * @param item:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2023-03-20 16:31
     * @since 1.0.0
     */
    ZKResultMsg handlerZipUserPhoto(PersImportPhotoItem item);

    /**
     * 解压上传的zip压缩包
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/7/24 15:42
     * @param filePath 压缩包路径
     * @param outPath 解压路径
     * @return java.lang.String
     */
    String unZipToFile(String filePath, String outPath);

    /**
     * 根据部门id获取邮箱(供考勤自动导出使用)
     *
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/11 14:43
     * @param deptIds
     * @return java.util.List<java.lang.String>
     */
    List<String> getEmailsByDeptIds(Collection<String> deptIds);

    /**
     * 根据区域id获取邮箱(供考勤自动导出使用)
     *
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/5/12 14:44
     * @param areaIds
     * @return java.util.List<java.lang.String>
     */
    List<String> getEmailsByAreaIds(Collection<String> areaIds);

    /**
     * 获取人事pin号参数
     *
     * <AUTHOR>
     * @since 2019年6月21日 上午10:15:53
     * @return
     */
    ZKResultMsg getPersPinParams();

    /**
     * 根据mobile查询
     *
     * <AUTHOR>
     * @since 2019年6月21日 下午5:13:45
     * @param mobile
     * @return
     */
    PersPersonItem getItemByMobile(String mobile);

    /**
     * 根据邮箱查询
     *
     * <AUTHOR>
     * @since 2019年6月21日 下午5:51:44
     * @param email
     * @return
     */
    PersPersonItem getItemByEmail(String email);

    /**
     * 分页获取人员信息
     *
     * <AUTHOR>
     * @since 2019年7月18日 下午4:17:44
     * @param filter
     * @param deptId
     * @param page
     * @param pageSize
     * @return
     */
    ZKResultMsg getUserInfos(String filter, String deptId, Integer page, Integer pageSize);

    /**
     * 找出不是来自AD的人员编号
     *
     * <AUTHOR>
     * @since 2019-08-23 9:42
     * @Param []
     * @return
     */
    List<String> getPinNotFromAD(String persFrom);

    /**
     * 根据isFrom查找人员
     *
     * <AUTHOR>
     * @since 2019-08-23 11:18
     * @Param [persFrom]
     * @return
     */
    List<PersPersonItem> findByIsFrom(String persFrom);

    /**
     * 根据人员id和操作类型删除人员
     *
     * @auther lambert.li
     * @date 2019/5/16 15:47
     * @param ids 人员ids
     * @param opType 操作类型 （del : 删除，leave：离职）
     * @return
     */
    boolean deleteByIdsAndType(String ids, String opType);

    /**
     * 通过邮箱获取list
     *
     * @param email
     * @return
     * @return List<PersPersonItem>
     * <AUTHOR>
     */
    List<PersPersonItem> getByEmail(String email);

    /**
     * 检测是否设置了短信猫
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-21 11:42
     * @param
     * @return boolean
     */
    boolean checkSMSModemParam();

    /**
     * 检测是否显示勾选发送短信的选择框
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-22 16:05
     * @param
     * @return boolean
     */
    boolean checkShowSMS();

    /**
     * 验证人员图片是否可抠图
     *
     * <AUTHOR>
     * @param photoPath
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @date 2020/4/1 10:31
     */
    ZKResultMsg validCropFace(String photoPath);

    /*
     * 人员保存
     * <AUTHOR>
     * @Date 2020/5/7 11:22
     * @param persPersonItem
     * @Return
     */
    boolean saveItemByTempPerson(PersPersonItem persPersonItem);

    /**
     * 根据人员pin删除抠图照片
     *
     * @param personPin:
     * @return void
     * <AUTHOR>
     * @throws @date 2020-09-24 16:17
     * @since 1.0.0
     */
    void deleteCropFace(String personPin);

    /**
     *
     *
     * @param personIds:
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @throws @date 2020-09-25 15:32
     * @since 1.0.0
     */
    List<String> getPinsByIds(List<String> personIds);

    /**
     * 获取所有人员编号
     *
     * @return java.util.List<java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/16 20:46
     * @since 1.0.0
     */
    List<String> getAllPinList();

    /**
     * 根据人员pin获取动态二维码
     *
     * @param pin:
     * @return java.lang.String
     * <AUTHOR>
     * @throws @date 2020-11-20 17:22
     * @since 1.0.0
     */
    String createEncryptedDynamicCode(String pin);

    /**
     * 加密mobilePhone/email/personPwd/selfPwd/idCard/idCardPhysicalNo字段
     *
     * @return void
     * <AUTHOR>
     * @throws @date 2021-06-23 14:32
     * @since 1.0.0
     */
    void encryptPersPerson();

    /**
     * 获取有照片数据的人员列表数据（根据分页参数）
     *
     * @param condition
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2020-12-17 11:00
     * @since 1.0.0
     */
    Pager getItemsByPhotoFilter(PersPersonItem condition, int page, int size);

    /**
     * 根据人员id获取照片不为空的人员数量
     *
     * @param personIds
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021-07-02 17:35
     * @since 1.0.0
     */
    Integer countByIdsAndPhotoIsNotNull(List<String> personIds);

    /**
     * 通过卡号获取人员pin
     *
     * @param cardNo
     * @return
     */
    String getPersonPinByCardNo(String cardNo);

    /**
     * 更新人员照片
     *
     * @param persPersonItem :
     * @param personPhoto
     * @return void
     * <AUTHOR>
     * @throws @date 2021-11-17 14:48
     * @since 1.0.0
     */
    ApiResultMessage updateUserPhoto(PersPersonItem persPersonItem, String personPhoto);

    /**
     * 导入人员数据及自定义属性,及判断是否更新已存在数据
     *
     * <AUTHOR>
     * @param itemList
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @date 2019/7/10 11:35
     */
    ZKResultMsg importPersonInfo(List<PersPersonImportItem> itemList, boolean updateExistData);

    /**
     * 启用人员凭证
     * 
     * @param personIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-06-17 17:50
     * @since 1.0.0
     */
    void enabledCredential(String personIds);

    /**
     * 禁用人员凭证
     * 
     * @param personIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-06-17 17:50
     * @since 1.0.0
     */
    void disableCredential(String personIds);

    /**
     * @Description: 获取禁用人员凭证人员id
     *
     * @param sessionId:
     * @param condition:
     * @return: java.util.List<java.lang.String>
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date: 2023/9/25 16:08
     * @since: 1.0.0
     */
    List<String> getPersonIdByDisableCredential(String sessionId, PersPersonItem condition);

    /**
     * 是否启用数据保护
     * 
     * @return java.lang.Boolean
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 15:40
     * @since 1.0.0
     */
    Boolean isProtectData();

    /**
     * pin 卡号隐藏部分字符
     * 
     * @param items:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonItem>
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 15:41
     * @since 1.0.0
     */
    List<PersPersonItem> protectPinAndCard(List<PersPersonItem> items);

    /**
     * 判断是否有WhatsApp许可
     * 
     * @return java.lang.Boolean
     * <AUTHOR>
     * @throws
     * @date 2023-03-21 10:01
     * @since 1.0.0
     */
    Boolean checkShowWhatsapp();

    /**
     * 判断WhatsApp是否配置
     * 
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2023-03-21 14:05
     * @since 1.0.0
     */
    boolean checkWhatsappParam();

    /**
     * 设置启禁用默认值，用于升级
     * 
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-09-12 16:58
     * @since 1.0.0
     */
    void updateEnabledCredential();

    /**
     *
     * 验证当前用户登陆密码
     *
     * @author: train.chen
     * @date: 2018年5月30日 下午3:25:37
     * @param loginPwd
     * @return
     */
    Boolean verifyLoginPwd(String sessionId, String loginPwd);

    /**
     * 根据人员编号获取人员id
     *
     * @param pinList:
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2023-10-23 11:23
     * @since 1.0.0
     */
    List<String> getPersonIdsByPins(List<String> pinList);

    /**
     * 启用app登录
     *
     * @param personIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-10-10 14:14
     * @since 1.0.0
     */
    void enabledApplogin(String personIds);

    /**
     * 禁用app登录
     *
     * @param personIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-10-10 14:14
     * @since 1.0.0
     */
    void disableApplogin(String personIds);

    /**
     * 获取ACMS中的卡
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-03-07 17:20
     * @since 1.0.0
     */
    ZKResultMsg obtainAcmsCard(String status, String pageSize);

    /**
     * 批量提取人脸模板
     * 
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-05-09 14:11
     * @since 1.0.0
     */
    ZKResultMsg batchFaceTemplateByIds(String ids);

    /**
     * 根据人员编号获取人员姓名
     *
     * @param pin: 人员编号
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024-07-02 14:20
     * @since 1.0.0
     */
    String getWholeNameByPin(String pin);

    /**
     * 根据人员ID获取人员姓名
     * 
     * @param personId:人员ID
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024-10-12 11:27
     * @since 1.0.0
     */
    String getWholeNameByPersonId(String personId);

    /**
     * 根据卡号获取二维码
     * 
     * @auther 31876
     * @date 14:02
     * @since 1.0.0
     */
    String getPersCardQrCodeByCardNo(String cardNo);

    /**
     * 更新人员头像
     * 
     * @param item:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonItem
     * <AUTHOR>
     * @throws
     * @date 2024-11-19 14:57
     * @since 1.0.0
     */
    PersPersonItem updateAvatar(PersPersonItem item);

    /**
     * 同步人员到ACMS
     * 
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-12-26 11:28
     * @since 1.0.0
     */
    ZKResultMsg syncPersonAcmsByIds(String ids);

    /**
     * 根据更新时间同步人员信息到ACMS
     * 
     * @param updateTime:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonItem>
     * <AUTHOR>
     * @throws
     * @date 2024-12-27 10:14
     * @since 1.0.0
     */
    void updateAcmsPersonByUpdateTime(Date updateTime);

    /**
     * 根据姓名手机号查询人员
     * 
     * @param nameLike:
     * @param mobilePhone:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonItem>
     * <AUTHOR>
     * @throws
     * @date 2025-04-28 16:38
     * @since 1.0.0
     */
    List<PersPersonItem> findByNameLikeOrMobilePhone(String nameLike, String mobilePhone);


    public boolean checkSelfPwd(PersPersonItem personItem, String selfPwd);

    public void updatePersonByWechatOpenId(String pin ,String openid);
}