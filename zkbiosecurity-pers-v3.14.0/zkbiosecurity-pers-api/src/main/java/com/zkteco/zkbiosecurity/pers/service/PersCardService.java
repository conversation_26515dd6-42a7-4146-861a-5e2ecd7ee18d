/**
 * File Name: PersCard Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 对应百傲瑞达 PersCardService
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersCardService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    PersCardItem saveItem(PersCardItem item);

    /**
     * 保存主卡
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/8/13 19:17
     * @param pin
     * @param cardNo
     * @return void
     */
    void saveMasterCard(String pin, String cardNo);

    /**
     * 保存多卡 pin,cardNo,cardType,cardState
     * 
     * @param items
     */
    void saveMultiCard(List<PersCardItem> items);

    /**
     * 保存多卡
     * 
     * @param personId
     * @param cardNos
     */
    void updatePersonCardInfo(String personId, String cardNos);

    /**
     * 保存多卡
     * 
     * @param personId:
     * @param items:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-08-18 15:18
     * @since 1.0.0
     */
    void updatePersonCardInfo(String personId, List<PersCardItem> items);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<PersCardItem> getByCondition(PersCardItem condition);

    /**
     * 根据当前用户过滤数据
     * 
     * @param sessionId
     * @param condition
     * @param page
     * @param size
     * @return
     */
    Pager loadPagerByAuthUserFilter(String sessionId, BaseItem condition, int page, int size);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    PersCardItem getItemById(String id);

    /**
     * 根据人员ID查询多卡
     * 
     * @param personId
     * @return
     */
    List<PersCardItem> getItemByPersonId(String personId);

    /**
     * 根据人员ID查询主卡
     * 
     * @param personId
     * @return
     */
    PersCardItem getMasterCardByPersonId(String personId);

    /**
     * 根据人员ids查询卡号
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/16 14:38
     * @param personIds
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersCardItem>
     */
    List<PersCardItem> getItemByPersonIds(String personIds);

    /**
     * 根据人员ids查询主卡
     * 
     * @param personIds
     * @return
     */
    List<PersCardItem> getMasterCardByPersonIdList(Collection<String> personIds);

    /**
     * 根据人员卡号查询主卡
     * 
     * @param cardNoList
     * @return
     */
    List<PersCardItem> getMasterCardByCardNos(Collection<String> cardNoList);

    /**
     * 根据人员卡号查询卡
     * 
     * @param cardNoList
     * @return
     */
    List<PersCardItem> getCardByCardNos(Collection<String> cardNoList);

    /**
     * 根据人员ids查询卡号，只有卡信息
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/16 14:42
     * @param personIds
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersCardItem>
     */
    List<PersCardItem> getItemByPersonIdList(Collection<String> personIds);

    /**
     * @Description 根据人员ids查询出全部卡号，包含无效卡
     * <AUTHOR>
     * @Date 2019/1/3 14:52
     * @Param personIds
     * @Return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersCardItem>
     */
    List<PersCardItem> getAllCardByPersonIdList(Collection<String> personIds);

    /**
     * 根据cardNo查询
     * 
     * @param cardNo
     * @return
     */
    PersCardItem getItemByCardNo(String cardNo);

    /**
     * 获取人员pins根据卡号,有效卡
     * 
     * @param cardNos
     * @return
     */
    Map<String, String> getPinsByCardNos(List<String> cardNos);

    /**
     * 获取人员ids根据卡号
     * 
     * @param cardNos
     * @return
     */
    Map<String, String> getPersonIdsByCardNos(List<String> cardNos);

    /**
     * 获取人员pins根据卡号
     * 
     * @param cardNos
     * @return
     */
    Map<String, String> getPersonIdsByCardNosAndCardState(List<String> cardNos, Short cardState);

    /**
     * 根据卡号模糊查询获取列表 因为Spring-Data-JPA 传过的值，不会自动添加模糊查询符号 需要你自己添加要模糊查询的规则。 eg: 参数若是 123 JPA 解析成 like '123' 改造参数 123% JPA
     * 会解析成 like '123%'
     * 
     * @param cardNo
     * @return
     */
    List<PersCardItem> getItemByCardNoLike(String cardNo);

    /**
     * 根据cardNo查询是否存在使用
     * 
     * @param cardNo
     * @param pin
     * @return
     */
    boolean isExistByCardNoAndPin(String cardNo, String pin);

    /**
     * 查询人事及其他模块（访客）cardNo是否存在使用
     * 
     * @param cardNo:
     * @param pin:
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2022-10-10 14:57
     * @since 1.0.0
     */
    boolean isExistByCardNoAndPinAndOtherFilter(String cardNo, String pin);

    /**
     * 批量挂失
     * 
     * @param ids
     * @return
     */
    void batchCardLoss(String ids);

    /**
     * 批量解挂
     * 
     * @param ids
     * @return
     */
    void batchCardRevert(String ids);

    /**
     * 查询数量
     * 
     * @return
     */
    long count();

    /**
     * 根据卡类型查找数量
     * 
     * @return
     */
    long countByCardType(short cardType);

    /**
     * 根据卡状态和人员ids查找卡号
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/4/25 20:24
     * @param cardState 卡状态
     * @param personIds 人员IDS
     * @return java.util.List<java.lang.String>
     */
    List<String> findCardNoByCardStateAndPersonIds(Short cardState, Collection<String> personIds);

    /**
     * 检查cardNo的长度
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @date 2018/6/23 16:16
     * @param cardNo
     * @return boolean
     */
    boolean checkCardBit(String cardNo);

    /**
     * 删除人员的卡
     * 
     * @param personId
     */
    void deleteByPersonId(String personId);

    Map<String, String> cardVerification(String cardNo);

    /**
     * 批里处理导入人员的卡号
     * 
     * @param pinAndCardsMap 人员编号对应卡号列表
     * @param pinAndIdMap 人员编号对应人员ID
     */
    void batchSaveCards(Map<String, List<String>> pinAndCardsMap, Map<String, String> pinAndIdMap);

    /**
     * 根据pin号删除人员的卡
     * 
     * @param pins
     */
    void deleteByPersonPinIn(Collection<String> pins);

    /**
     * 判断卡号是否重复
     * 
     * @auther lambert.li
     * @date 2018/11/8 15:39
     * @param persPersonItem
     * @param cardNos
     * @return
     */
    int isExitCardNo(PersPersonItem persPersonItem, String cardNos);

    /**
     * 卡数据迁移
     *
     * @param cardItems
     */
    void handlerTransfer(List<PersCardItem> cardItems);

    /**
     * 保存人员卡号-提供给获取人员信息或者登记机上传人员信息保存使用
     * 
     * <AUTHOR>
     * @since 2019-01-03 16:08
     * @Param [persCardItemList]
     * @return
     */
    void saveCards(List<PersCardItem> persCardItemList);

    /**
     * 加密cardNo字段
     * 
     * @return void
     * <AUTHOR>
     * @throws @date 2021-06-23 10:38
     * @since 1.0.0
     */
    void encryptPersCard();

    /**
     * 获取导出数据
     * 
     * @param persCardItemClass:
     * @param condition:
     * @param beginIndex:
     * @param endIndex:
     * @return java.util.List<?>
     * <AUTHOR>
     * @throws @date 2022-01-14 14:46
     * @since 1.0.0
     */
    List<?> getItemData(Class<PersCardItem> persCardItemClass, BaseItem condition, int beginIndex, int endIndex);

    /**
     * 获取导出数据
     * 
     * @param sessionId:
     * @param persCardItemClass:
     * @param condition:
     * @param beginIndex:
     * @param endIndex:
     * @return java.util.List<?>
     * <AUTHOR>
     * @throws
     * @date 2024-03-12 15:21
     * @since 1.0.0
     */
    List<?> getItemDataByAuthUserFilter(String sessionId, Class<PersCardItem> persCardItemClass, BaseItem condition,
        int beginIndex, int endIndex);

    /**
     * pin 卡号隐藏部分字符
     * 
     * @param items:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersCardItem>
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 15:54
     * @since 1.0.0
     */
    List<PersCardItem> protectPinAndCard(List<PersCardItem> items);

    /**
     * 修改卡状态
     * 
     * @param cardState:
     * @param personIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-05-28 19:43
     * @since 1.0.0
     */
    void updateCardState(Short cardState, List<String> personIds);
}