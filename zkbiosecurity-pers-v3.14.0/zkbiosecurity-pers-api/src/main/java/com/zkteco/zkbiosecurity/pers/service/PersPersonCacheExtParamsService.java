package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.Map;

/**
 * 人员信息缓存附加属性获取，各模块有需要附加属性需要实现该接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/9/14 14:00
 * @since 1.0.0
 */
public interface PersPersonCacheExtParamsService {

    /**
     * 模块编码，用于标识模块附加属性的key
     *
     * @return java.lang.String
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:59
     * @since 1.0.0
     */
    default String getModule() {
        return null;
    }

    /**
     * 各个模块的附加属性集合
     * <P>
     * 格式：< "pin" : <"xx":"xx", "xx":"xx"> >
     * </p>
     * 
     * @param pinList:
     * @return java.util.Map<java.lang.String,java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 14:02
     * @since 1.0.0
     */
    default Map<String, Map<String, Object>> getExtParamsMapByPins(Collection<String> pinList) {
        return null;
    }
}