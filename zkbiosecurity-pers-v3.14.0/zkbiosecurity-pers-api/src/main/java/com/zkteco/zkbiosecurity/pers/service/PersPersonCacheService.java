package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem;

/**
 * 人员信息缓存接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2020/9/14 13:35
 * @since 1.0.0
 */
public interface PersPersonCacheService {

    /**
     * 获取缓存中的人员信息，如果缓存不存在，查询数据库，并且更新到缓存
     * 
     * @param pin:人员编号
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:35
     * @since 1.0.0
     */
    PersPersonCacheItem getPersonCacheByPin(String pin);

    /**
     * 获取缓存中的人员信息过滤已离职人员，如果缓存不存在，查询数据库，并且更新到缓存
     * 
     * @param pin:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem
     * <AUTHOR>
     * @throws
     * @date 2024-04-03 11:21
     * @since 1.0.0
     */
    PersPersonCacheItem getPersonCacheFilterLeaveByPin(String pin);

    /**
     * 获取缓存中的人员信息，如果缓存不存在，查询数据库，并且更新到缓存（批量查询）
     * 
     * @param pinList:人员编号集合
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:36
     * @since 1.0.0
     */
    List<PersPersonCacheItem> getPersonCacheByPins(List<String> pinList);

    /**
     * 获取缓存中的人员信息过滤离职人员，如果缓存不存在，查询数据库，并且更新到缓存（批量查询）
     * 
     * @param pinList:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonCacheItem>
     * <AUTHOR>
     * @throws
     * @date 2024-04-03 11:21
     * @since 1.0.0
     */
    List<PersPersonCacheItem> getPersonCacheFilterLeaveByPins(List<String> pinList);

    /**
     * 更新人员缓存的模块附加参数
     * 
     * @param module:附加属性字段名称，各模块自定义，一般为模块编码
     * @param pinExtParamsMap:人员对应的附加参数集合<"pin":<"xx":"xx","xx":"xx">>
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2020/9/14 13:36
     * @since 1.0.0
     */
    void updatePersonCacheExtParamsMap(String module, Map<String, Map<String, Object>> pinExtParamsMap);

}