package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 供其他模块调用统一处理
 *
 * <AUTHOR>
 * @date 2024/6/20 9:28
 * @since 1.0.0
 */
public interface Pers4OtherService {

    /**
     * 查询人员数据
     * 
     * @param condition:
     * @return java.util.List<?>
     * <AUTHOR>
     * @throws
     * @date 2024-06-20 9:32
     * @since 1.0.0
     */
    List getPersonDatasByItem(BaseItem condition);

    /**
     * 查询卡数据
     * 
     * @param condition:
     * @return java.util.List
     * <AUTHOR>
     * @throws
     * @date 2024-06-24 17:16
     * @since 1.0.0
     */
    List getPerCardByItem(BaseItem condition);

    /**
     * 查询比对照片数据
     * 
     * @param condition:
     * @return java.util.List
     * <AUTHOR>
     * @throws
     * @date 2024-06-24 17:19
     * @since 1.0.0
     */
    List getPerBioPhotoByItem(BaseItem condition);

    /**
     * 查询职位数据
     * 
     * @param condition:
     * @return java.util.List
     * <AUTHOR>
     * @throws
     * @date 2024-06-27 17:48
     * @since 1.0.0
     */
    List getPersPositionByItem(BaseItem condition);

    /**
     * 查询生物模板数据
     *
     * @param condition:
     * @return java.util.List
     * <AUTHOR>
     * @throws
     * @date 2024-06-27 17:48
     * @since 1.0.0
     */
    List getPersBioTemplateByItem(BaseItem condition);
}
