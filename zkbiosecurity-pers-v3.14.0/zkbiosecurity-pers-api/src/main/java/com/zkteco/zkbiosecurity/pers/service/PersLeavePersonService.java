/**
 * File Name: PersLeavePerson Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem;

/**
 * 对应百傲瑞达 PersLeavePersonService
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
public interface PersLeavePersonService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    PersLeavePersonItem saveItem(PersLeavePersonItem item);

    /**
     * 批量离职
     *
     * @param item
     * @param personIds
     */
    void batchLeave(PersLeavePersonItem item, String personIds);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<PersLeavePersonItem> getByCondition(PersLeavePersonItem condition);

    /**
     * 根据登陆用户过滤部门离职人员
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthUserFilter(String sessionId, BaseItem condition, int pageNo, int pageSize);

    /**
     * 根据登录sessionId,部门名称，部门编号，人员编号，人员名称 获取对应的人员PIN
     *
     * @param sessionId
     * @param condition
     * @return java.util.List<java.lang.String>
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/12/17 18:29
     */
    List<String> getPersonPinByDeptPersAuthUserFilter(String sessionId, PersLeavePersonItem condition);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    PersLeavePersonItem getItemById(String id);

    /**
     * 根据pin查询
     *
     * @param pin
     * @return
     */
    PersLeavePersonItem getItemByPin(String pin);

    /**
     * 根据pins查询
     *
     * @param pins
     * @return
     */
    List<PersLeavePersonItem> getItemByPins(Collection<String> pins);

    /**
     * 根据部门ID获取人员集合 有根据deptIds进行分批查询
     *
     * @param deptIds
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttPersonItem>
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/18 14:20
     */
    List<PersLeavePersonItem> getItemByDeptIds(Collection<String> deptIds);

    /**
     * 获取离职人员总人数
     *
     * @param
     * @return java.lang.Integer
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/23 18:36
     */
    Integer getAllPersonLeaveCount();

    /**
     * 根据部门id获取离职人员人数
     *
     * @param
     * @return java.lang.Integer
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/23 18:36
     */
    Integer getPersonLeaveCountByDeptIds(String deptIds);

    /**
     * 根据人员编号和时间段查询这段时间离职的人员
     *
     * @param pins
     * @param beginDate
     * @param endDate
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonItem>
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2019/2/11 16:27
     */
    List<PersLeavePersonItem> findByPinsAndLeaveDate(String pins, Date beginDate, Date endDate);

    /**
     * 导出数据的获取
     * 
     * @param targetClass
     * @param codition
     * @param begin
     * @param end
     * @return
     */
    List<?> getItemData(Class targetClass, BaseItem codition, int begin, int end);

    /**
     * 离职人员数据迁移
     * 
     * <AUTHOR>
     * @Date 2019/8/14 18:08
     * @param
     * @return
     */
    void handlerTransfer(List<PersLeavePersonItem> persLeavePersonItems);

    /*
     * 导入离职人员
     * <AUTHOR>
     * @Date 2020/7/23 16:23
     * @param itemList
     * @Return
     */
    ZKResultMsg importExcel(List<PersLeavePersonItem> itemList);

    /**
     * pin隐藏部分字符
     * 
     * @param items:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersLeavePersonItem>
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 15:44
     * @since 1.0.0
     */
    List<PersLeavePersonItem> protectPin(List<PersLeavePersonItem> items);

    /**
     * 封装条件
     * 
     * @param sessionId:
     * @param condition:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-06-26 15:37
     * @since 1.0.0
     */
    void buildCondition(String sessionId, PersLeavePersonItem condition);
}