package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

public interface PersFaceVerifyService {
    /**
     * 测试人脸服务器连接状态
     *
     * @return
     */
    ZKResultMsg testFaceVerifyServerStatus();

    /**
     * 下发人脸到人脸比对服务器
     *
     * @param pin
     * @param cropFacePath
     * @param isAllowList
     * @return
     */
    ZKResultMsg addFace2Server(String pin, String cropFacePath, boolean isAllowList);

    /**
     * 从人脸比对服务器删除人脸
     *
     * @param pin
     * @param isAllowList
     * @return
     */
    ZKResultMsg delFaceFromServer(String pin, boolean isAllowList);

    /**
     * 获取是否启用人脸比对服务配置
     *
     * @return
     */
    boolean getFaceVerifyEnable();
}
