/**
 * File Name: PersPersonChange
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonChangeItem;

import java.util.List;

/**
 * 对应百傲瑞达 PersPersonChangeService
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersPersonChangeService extends BaseService {

	/**
	 * 保存item实体，一般会有复杂业务逻辑
	 * @param item
	 */
	PersPersonChangeItem saveItem(PersPersonChangeItem item);

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<PersPersonChangeItem> getByCondition(PersPersonChangeItem condition);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	PersPersonChangeItem getItemById(String id);
	
	/**
	 * 根据人员ID进行删除
	 * @param personId
	 */
	void deleteByPersonId(String personId);
}