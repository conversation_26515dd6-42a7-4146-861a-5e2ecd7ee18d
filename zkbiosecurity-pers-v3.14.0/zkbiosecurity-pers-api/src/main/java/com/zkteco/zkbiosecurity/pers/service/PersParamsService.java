/**
 * File Name: PersCard Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco Inc.All
 * right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.Map;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 对应百傲瑞达 PersCardService
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersParamsService {

    /**
     * 保存参数
     */
    void saveItem(Map<String, String> params);

    /**
     * 查询所有人事参数
     * 
     * @return
     */
    Map<String, String> getPersParams();

    /**
     * 根据参数名称获取值
     * 
     * @param name
     * @return
     */
    String getValByName(String name);

    /**
     * 获取人事编号参数
     * 
     * <AUTHOR>
     * @since 2019年6月21日 上午10:19:11
     * @return
     */
    ZKResultMsg getPersPinParams();

    /**
     * 检测是否是单考勤系统
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/20 15:59
     * @return: boolean
     **/
    boolean checkSingleAttSystem();

    /**
     * 是否开启模糊处理
     * 
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2022-08-26 9:54
     * @since 1.0.0
     */
    boolean getEncryptPropByParamName(String paramName);

    /**
     * 根据照片路径、照片是否加密获取base64数据
     * 
     * @param photoPath:
     * @param encrypt:
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2022-08-26 10:47
     * @since 1.0.0
     */
    String getDecryptBase64ByPhotoPathAndEncrypt(String photoPath, String encrypt);

    /**
     * 获取加密显示姓名
     *
     * @param name:姓名
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-09-30 10:22
     * @since 1.0.0
     */
    String getEncryptName(String name);

    /**
     * 获取加密显示姓氏
     *
     * @param lastName:姓氏
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-09-30 10:23
     * @since 1.0.0
     */
    String getEncryptLastName(String lastName);

    /**
     * 获取加密显示卡号
     *
     * @param cardNo:卡号
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-09-30 10:23
     * @since 1.0.0
     */
    String getEncryptCardNo(String cardNo);

    /**
     * 获取加密显示人员编号
     *
     * @param pin:人员编号
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022-09-30 10:23
     * @since 1.0.0
     */
    String getEncryptPin(String pin);

    /**
     * 根据指定参数获取字段加密与否的最终数据
     * 
     * @param value:
     * @param encryptProp:
     * @param encryptMode:
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2023-03-30 16:56
     * @since 1.0.0
     */
    String getEncryptProp(String value, String encryptProp, String encryptMode);
}