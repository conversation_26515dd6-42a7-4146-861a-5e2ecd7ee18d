/**
 * File Name: PersIdentityCardInfo Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersIdentityCardInfoItem;

/**
 * 对应百傲瑞达 PersIdentityCardInfoService
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersIdentityCardInfoService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    PersIdentityCardInfoItem saveItem(PersIdentityCardInfoItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<PersIdentityCardInfoItem> getByCondition(PersIdentityCardInfoItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    PersIdentityCardInfoItem getItemById(String id);

    /**
     * 根据物理卡号查询身份证
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/11 10:51
     * @param physicalNo
     * @return com.zkteco.zkbiosecurity.pers.vo.PersIdentityCardInfoItem
     */
    PersIdentityCardInfoItem findByPhysicalNo(String physicalNo);

    /**
     * 根据身份证号码查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/11 10:54
     * @param idCard
     * @return com.zkteco.zkbiosecurity.pers.vo.PersIdentityCardInfoItem
     */
    PersIdentityCardInfoItem findByIdCard(String idCard);

    /**
     * 身份证信息数据迁移
     *
     * @param ideintityCardInfoItems
     */
    void handlerTransfer(List<PersIdentityCardInfoItem> ideintityCardInfoItems);

    /**
     * 加密idCard/physicalNo/address字段
     * 
     * @return void
     * <AUTHOR>
     * @throws @date 2021-06-23 11:45
     * @since 1.0.0
     */
    void encryptPersIdentityCardInfo();
}