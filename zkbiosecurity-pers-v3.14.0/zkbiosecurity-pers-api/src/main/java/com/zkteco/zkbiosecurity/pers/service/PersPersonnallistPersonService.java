package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnallistPersonItem;

/**
 * 名单库人员接口定义
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2020-07-22 16:35:16
 */
public interface PersPersonnallistPersonService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    PersPersonnallistPersonItem saveItem(PersPersonnallistPersonItem item);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<PersPersonnallistPersonItem> getByCondition(PersPersonnallistPersonItem condition);

    /**
     * 根据库id获取人员id集合
     *
     * @param personnallistId
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2021/5/20 17:11
     * @since 1.0.0
     */
    List<String> findPersonIdByPersonnallistId(String personnallistId);

    /**
     * 通过人员id更新名单库id
     *
     * @param personId
     * @param oldPersonnelListId
     * @param newPersonnelListId
     * @return void
     * <AUTHOR>
     * @date 2021-10-26 16:53
     * @since 1.0.0
     */
    void updatePersonListIdByPersonId(String personId, String oldPersonnelListId, String newPersonnelListId);

    /**
     * 批量保存
     * 
     * @param itemList:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-09-14 15:00
     * @since 1.0.0
     */
    void batchSave(List<PersPersonnallistPersonItem> itemList);
}
