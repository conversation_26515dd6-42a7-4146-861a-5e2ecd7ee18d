package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoItem;
import com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoSaveItem;

public interface PersBioPhotoService extends BaseService {

    /**
     * 保存人员比对照片
     * 
     * @param item:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoItem
     * <AUTHOR>
     * @throws
     * @date 2022-07-07 17:08
     * @since 1.0.0
     */
    PersBioPhotoItem saveItem(PersBioPhotoItem item);

    /**
     * 通过人员id保存人员比对照片
     * 
     * @param item:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoItem
     * <AUTHOR>
     * @throws
     * @date 2022-07-07 18:04
     * @since 1.0.0
     */
    PersBioPhotoItem saveItemByPersonId(PersBioPhotoItem item);

    /**
     * 根据人员id删除
     * 
     * @param personId:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-07-07 17:22
     * @since 1.0.0
     */
    void deleteByPersonId(String personId);

    /**
     * 保存人员比对照片,给各个业务模块，设备上传保存比对照片用的。
     * 
     * @param item:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-07-13 16:02
     * @since 1.0.0
     */
    void saveBioPhoto(PersBioPhotoSaveItem item);

    /**
     * 根据人员id List获取item
     * 
     * @param personIds:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoItem>
     * <AUTHOR>
     * @throws
     * @date 2022-07-12 15:42
     * @since 1.0.0
     */
    List<PersBioPhotoItem> findByPersonIdIn(List<String> personIds);

    /**
     * 根据人员pin和生物类型删除
     * 
     * @param personPins:
     * @param bioTypes:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-10-27 15:27
     * @since 1.0.0
     */
    void deleteByPersonPinsAndBioTypes(List<String> personPins, List<Short> bioTypes);

    /**
     * 获取bioPhoto数量
     *
     * @param personIds
     * @return
     */
    Map<String, Map<String, Integer>> getBioTypeAndCountByPersonIdList(Collection<String> personIds);

    /**
     * 根据人员id获取item和比对照片base64数据
     * 
     * @param ids:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoItem>
     * <AUTHOR>
     * @throws
     * @date 2023-03-13 15:46
     * @since 1.0.0
     */
    List<PersBioPhotoItem> getItemAndBase64ByPerIds(String sessionId, String ids);

    /**
     * 通过人员id保存人员比对照片
     * 
     * @param items:
     * @return com.zkteco.zkbiosecurity.pers.vo.PersBioPhotoItem
     * <AUTHOR>
     * @throws
     * @date 2024-01-24 17:56
     * @since 1.0.0
     */
    List<PersBioPhotoItem> saveItemByPersonIds(List<PersBioPhotoItem> items);

    /**
     * 保存人员比对照片并更新头像,给各个业务模块，设备上传保存比对照片用的。
     *
     * @param item:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-07-13 16:02
     * @since 1.0.0
     */
    void saveBioPhotoAndUpdateAvatar(PersBioPhotoSaveItem item);
}
