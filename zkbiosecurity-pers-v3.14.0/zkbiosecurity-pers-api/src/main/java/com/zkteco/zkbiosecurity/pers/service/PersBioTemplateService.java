/**
 * File Name: PersBioTemplate Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiBioTemplateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 对应百傲瑞达 PersBioTemplateService
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersBioTemplateService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    PersBioTemplateItem saveItem(PersBioTemplateItem item);

    /**
     * 保存生物模板
     * 
     * @param personId
     * @param bioTemplateJson
     */
    void saveBioTemplateJson(String personId, String bioTemplateJson);

    /**
     * 更新生物模板
     * 
     * @param personId
     * @param bioTemplateJson
     */
    void updateBioTemplateJson(String personId, String bioTemplateJson);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<PersBioTemplateItem> getByCondition(PersBioTemplateItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    PersBioTemplateItem getItemById(String id);

    /**
     * 获取人员的所有模板
     * 
     * @param personId
     * @return
     */
    List<PersBioTemplateItem> getItemByPersonId(String personId);

    /**
     * 根据人员ids查询生物模板
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/16 13:58
     * @param personIds
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem>
     */
    List<PersBioTemplateItem> getItemByPersonIds(String personIds);

    /**
     * 根据人员ids查询生物模板
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/16 13:58
     * @param personIds
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersBioTemplateItem>
     */
    List<PersBioTemplateItem> getItemByPersonIdList(List<String> personIds);

    /**
     * 获取人员的所有模板
     * 
     * @param personId
     * @return
     */
    Map<String, Object> getItemMapByPersonId(String personId);

    /**
     * 获取生物模板数量
     * 
     * @param personIds
     * @return
     */
    Map<String, Map<Short, Integer>> getBioTemplateCountByPersonIdList(Collection<String> personIds);

    /**
     * 根据人员ids统计生物模板数量
     * 
     * @param personIds
     * @return
     */
    Map<Short, Integer> countBioTemplateCountByPersonIdList(Collection<String> personIds);

    /**
     *
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/15 15:50
     * @param itemList
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg importData(List<PersBioTemplateItem> itemList);

    /**
     * 获取数据
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/15 15:50
     * @param condition
     * @param sessionId
     * @param beginIndex
     * @param endIndex
     * @return java.util.List<?>
     */
    List<?> getItemData(PersBioTemplateItem condition, String sessionId, int beginIndex, int endIndex);

    /**
     * 用于已经发布出去的，由于defTeampleNo,或者defTeamplateNoIndex数据不对校验更新过来
     */
    void initUpgradeData();

    /**
     * 生物模板数据迁移
     * 
     * @param bioTemplateItems
     */
    void handlerTransfer(List<PersBioTemplateItem> bioTemplateItems);

    /**
     * 根据人员pin和生物特征模板编号删除人员模板信息
     * 
     * <AUTHOR>
     * @since 2019-09-16 17:36
     * @Param [pin, templateNo]
     * @return
     */
    int deleteBioTemplateByPinAndTemplateNo(String pin, Short templateNo);

    /**
     * 新增人员生物模板信息
     * 
     * <AUTHOR>
     * @since 2019-09-17 10:06
     * @Param [apiBioTemplate]
     * @return
     */
    int addBioTemplate(PersApiBioTemplateItem apiBioTemplate);

    /**
     * 根据pin删除人员所有生物模板信息
     * 
     * <AUTHOR>
     * @since 2019-09-17 14:25
     * @Param [pin]
     * @return
     */
    void deleteByPersonPin(String pin);

    /**
     * 根据人员pin和模板类型删除模板
     * 
     * <AUTHOR>
     * @Param pin
     * @Param bioType
     * @return void
     * @date 2020/6/3 17:17
     */
    void deleteByPersonPinAndBioType(String pin, Short bioType);

    /**
     * 根据人员id和模板类型删除模板
     * 
     * @param personIds:
     * @param personPins:
     * @param bioTypes:
     * @return void
     * <AUTHOR>
     * @throws @date 2020-09-29 16:45
     * @since 1.0.0
     */
    void deleteByPersonPinsAndBioTypes(List<String> personIds, List<String> personPins, List<Short> bioTypes);

    /**
     * 更新生物模板
     *
     * @param bioTemplateItemList:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/4/11 14:55
     * @since 1.0.0
     */
    void updateBioTemplate(List<PersBioTemplateItem> bioTemplateItemList);

    /**
     * 获取生物模板数量
     *
     * @param personIds
     * @return
     */
    Map<String, Map<String, Integer>> getBioTypeAndCountByPersonIdList(Collection<String> personIds);

    /**
     * 根据生物模板指定的参数删除
     *
     * @param personItem:
     * @param templateNo:
     * @param fpBioType:
     * @return int
     * <AUTHOR>
     * @throws
     * @date 2024-01-11 10:00
     * @since 1.0.0
     */
    int deleteBioTemplateByCondition(PersPersonItem personItem, Short templateNo, Short fpBioType);

    /**
     * 根据人员pin集合和模板类型删除模板
     * 
     * @param pins:
     * @param bioType:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-01-24 17:52
     * @since 1.0.0
     */
    void deleteByPersonPinsAndBioType(List<String> pins, Short bioType);
}