package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.pers.vo.*;

/**
 * 人员扩展接口，人事模块本身不实现，给具体业务模块实现
 *
 * <AUTHOR>
 * @date 2020/2/28 15:03
 */
public interface PersPersonExtService {

    /**
     * 为后续模块提供人员编辑扩展调用，后续无需在module-api自定义扩展
     *
     * @param personItem
     * @param extParams
     * @return
     */
    default void editPersonExt(PersPersonItem personItem, Map<String, String> extParams) {}

    /**
     * 人事删除接口扩展
     *
     * @param personIds
     */
    default void delPersonExt(String personIds) {}

    /**
     * 人事添加名单库人员接口扩展
     *
     * @param personnelListId
     * @param personIds
     */
    default void addPersonnelListPersonExt(String personnelListId, String personIds) {}

    /**
     * 人事删除名单库人员接口扩展
     *
     * @param personnelListId
     * @param personIds
     */
    default void delPersonnelListPersonExt(String personnelListId, String personIds) {}

    /**
     * 批量调整人员归属部门权限
     *
     * @param personIds
     * @param deptId
     */
    default void batchDeptChangePermissonExt(List<String> personIds, String deptId) {}

    /**
     * 批量导入人员信息
     *
     * @param personItems
     */
    default void batchImportPersonExt(List<PersPersonItem> personItems) {}

    /**
     * 删除系统中人员生物识别数据，分发给业务模块进行设备删除
     *
     * @param personIds
     * @param bioTemplateTypes
     */
    default void deletePersonBioTemplate(Collection<String> personIds, Collection<Short> bioTemplateTypes) {}

    /**
     * 接收各模块机器上传的人员信息，并分发给其他模块
     *
     * @param items
     * @param module
     */
    default void setOtherPersonInfo2Dev(Collection<PersPersonInfo2OtherItem> items, String module) {}

    /**
     * 接收各模块机器上传的人员模版信息，并分发给其他模块
     *
     * @param items
     * @param module
     */
    default void setOtherBioTemplate2Dev(Collection<PersBioTemplate2OtherItem> items, String module) {}

    /**
     * 接收各模块机器上传的人员比对照片，并分发给其他模块
     *
     * @param items
     * @param module
     */
    default void setOtherBioPhoto2Dev(Collection<PersBioPhoto2OtherItem> items, String module) {}

    /**
     * 接收各模块机器上传的人员头像照片，并分发给其他模块
     *
     * @param items
     * @param module
     */
    default void setOtherUserPic2Dev(Collection<PersUserPic2OtherItem> items, String module) {}

    /**
     * 通知其他模块人员离职
     *
     * @param leaveIds
     * @return void
     * <AUTHOR>
     * @date 2021-01-19 16:54
     * @since 1.0.0
     */
    default void leavePersonExt(String leaveIds) {};

    /**
     * 校验人员是否可以删除
     * 
     * @param personIds
     * @return
     */
    default boolean checkDelPerson(String personIds) {
        return true;
    };

    /**
     * 更新人员抠图信息，并下发到设备
     *
     * @param items:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-11-18 10:29
     * @since 1.0.0
     */
    default void pushBioPhoto2Devs(Collection<PersBioPhoto2OtherItem> items) {};

    /**
     * 更新人员照片信息，并下发到设备
     *
     * @param items:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-11-18 10:29
     * @since 1.0.0
     */
    default void pushUserPic2Devs(Collection<PersUserPic2OtherItem> items) {};

    /**
     * 下发发卡命令
     * 
     * @param persCardItems:
     * @return void
     * <AUTHOR>
     * @throws @date 2022-01-24 13:40
     * @since 1.0.0
     */
    default void issuedCard(List<PersCardItem> persCardItems) {};

    /**
     * 挂失
     *
     * @param cardNos: 卡号
     * @return void
     * <AUTHOR>
     * @date 2022-04-25 15:20
     * @since 1.0.0
     */
    default void lossCard(String cardNos) {};

    /**
     * 解挂
     *
     * @param cardNos:卡号
     * @return void
     * <AUTHOR>
     * @date 2022-04-25 15:20
     * @since 1.0.0
     */
    default void revertCard(String cardNos) {};

    /**
     * 启用人员凭证
     *
     * @param personIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-06-17 17:50
     * @since 1.0.0
     */
    default void enabledCredential(String personIds) {};

    /**
     * 禁用人员凭证
     *
     * @param personIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-06-17 17:50
     * @since 1.0.0
     */
    default void disableCredential(String personIds) {};

    /**
     * 检测卡号是否已存在
     *
     * @param cardNo:
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2022-09-21 17:28
     * @since 1.0.0
     */
    default boolean checkExistCardNo(String cardNo) {
        return false;
    }

    /**
     * 通知其他模块人员离职
     * 
     * @param persPushLeavePersonItems:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-03-29 11:24
     * @since 1.0.0
     */
    default void pushLeavePersonExt(List<PersPushLeavePersonItem> persPushLeavePersonItems) {};

    /**
     * 通知其他模块删除离职人员
     * 
     * @param persPushLeavePersonItems:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-03-29 14:40
     * @since 1.0.0
     */
    default void pushLeavePersonDel(List<PersPushLeavePersonItem> persPushLeavePersonItems) {};
}
