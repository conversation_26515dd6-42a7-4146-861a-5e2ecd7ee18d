/**
 * File Name: PersWiegandFmt
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;

import java.util.List;

/**
 * 对应百傲瑞达 PersWiegandFmtService
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
public interface PersWiegandFmtService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    PersWiegandFmtItem saveItem(PersWiegandFmtItem item);

    /**
     * 初始化韦根数据
     *
     * @param item
     * @return
     */
    PersWiegandFmtItem initData(PersWiegandFmtItem item);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<PersWiegandFmtItem> getByCondition(PersWiegandFmtItem condition);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    PersWiegandFmtItem getItemById(String id);

    /**
     * 根据ids查询
     *
     * @param ids
     * @return
     */
    List<PersWiegandFmtItem> getItemsByIds(List<String> ids);

    /**
     * 获取默认自定匹配的
     *
     * @return
     */
    PersWiegandFmtItem getDefAutoMatch();

    /**
     * 获取自动匹配韦根
     *
     * @return
     */
    List<PersWiegandFmtItem> getAutoMatch();

    /**
     * 名字校验
     *
     * @param name
     * @return
     */
    boolean existsByName(String name);

    /**
     * 韦根数据迁移
     *
     * @param wiegandFmtItems
     */
    void handlerTransfer(List<PersWiegandFmtItem> wiegandFmtItems);

    /**
     * 根据韦根位数查询获取韦根格式
     * @param wiegandCount
     * @return
     */
    List<PersWiegandFmtItem> getItemsByWiegandCount(Short wiegandCount);

    /**
     * 是否存在门禁模块
     */
    boolean isExistAcc();
}