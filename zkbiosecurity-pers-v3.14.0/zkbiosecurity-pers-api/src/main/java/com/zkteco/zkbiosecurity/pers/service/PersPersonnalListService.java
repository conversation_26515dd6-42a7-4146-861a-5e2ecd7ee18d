package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonPersonnalListSelectItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonnallistPersonItem;

/**
 * 名单库接口定义
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2020-07-22 16:35:16
 */
public interface PersPersonnalListService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    PersPersonnalListItem saveItem(PersPersonnalListItem item);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<PersPersonnalListItem> getByCondition(PersPersonnalListItem condition);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    PersPersonnalListItem getItemById(String id);

    /**
     * 根据id获取该库下的人员id
     *
     * <AUTHOR>
     * @date 2020/7/24 8:48
     */
    List<String> getPersonIdsById(String id);

    /**
     * 根据ids批量删除名单库
     *
     * <AUTHOR>
     * @date 2020/8/7 11:11
     */
    ZKResultMsg delByIds(String ids);

    /**
     * 根据id获取该库下的人员集合
     *
     * <AUTHOR>
     * @date 2020/7/24 8:53
     */
    Pager getPersonPager(String sessionId, PersPersonnallistPersonItem condition, int page, int pageSize);

    /**
     * 名单库增加人员
     *
     * <AUTHOR>
     * @date 2020/7/24 9:46
     */
    ZKResultMsg addPerson(String id, String personIds);

    /**
     * 名单库增加人员
     *
     * @param id:
     * @param personIds:
     * @param deptIds:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-07-05 16:30
     * @since 1.0.0
     */
    ZKResultMsg addPerson(String id, String personIds, String deptIds);

    /**
     * 获取未下发到名单库中的人员
     * 
     * <AUTHOR>
     * @date 2020/7/25 10:39
     */
    Pager getNoExistPerson(String sessionId, PersPersonPersonnalListSelectItem condition, int page, int pageSize);

    /**
     * 删除人员
     *
     * <AUTHOR>
     * @date 2020/7/25 13:02
     */
    ZKResultMsg deletePerson(String personnalListId, String ids);

    /**
     * 根据名称获取item
     *
     * <AUTHOR>
     * @date 2020/8/4 9:56
     */
    PersPersonnalListItem getItemByName(String name);

    /**
     * 查询出名单库对应的人员数量 key为名单库id,value为数量
     *
     * <AUTHOR>
     * @date 2020/8/4 15:16
     */
    Map<String, String> getPersonnelListPersonCount();

    /**
     * 查询出名单库对应的人员数量 key为名单库id,value为数量
     * 
     * @param sessionId:
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @throws
     * @date 2022-08-18 13:58
     * @since 1.0.0
     */
    Map<String, String> getPersonCountByAuthUserFilter(String sessionId);

    /**
     * 初始化名单库
     *
     * <AUTHOR>
     * @date 2020/8/25 9:56
     */
    PersPersonnalListItem initData(PersPersonnalListItem persPersonnalListItem);

    /**
     * 获取名单库下拉数据
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     * <AUTHOR>
     * @date 2020-12-18 16:59
     * @since 1.0.0
     */
    List<SelectItem> getPersonnalListSelectData();

    /**
     * 根据人员ID删除
     *
     * @param personIds: 人员ID
     * @return a
     * @thros a
     * <AUTHOR>
     * @date 020-09-24 15:00:05
     * @since 1.0.0
     */
    ZKResultMsg delPersonByPersonId(String personIds);

    /**
     * 根据名单库id和人员id删除
     *
     * @param personnallistId
     * @param personId
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-01-21 15:34
     * @since 1.0.0
     */
    ZKResultMsg deletePersonnallistPerson(String personnallistId, String personId);

    /**
     * 获取名单库下有照片数据的人员列表信息
     *
     * @param persPersonnalListItem
     * @param page
     * @param size
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem>
     * <AUTHOR>
     * @date 2021/5/20 11:31
     * @since 1.0.0
     */
    List<PersPersonItem> getPersonList(PersPersonnalListItem persPersonnalListItem, int page, int size);

    /**
     * 获取名单库下有照片数据的人员数量
     *
     * @param persPersonnalListItem
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020-12-01 20:33
     * @since 1.0.0
     */
    Integer getPersonCount(PersPersonnalListItem persPersonnalListItem);

    /**
     * 从名单库人员表中获取人员信息
     *
     * @param personIds
     * @return java.util.List<HwPersonnallistPersonItem>
     * <AUTHOR>
     * @date 2021/5/20 14:42
     * @since 1.0.0
     */
    List<PersPersonnallistPersonItem> getPersonDataByPersonId(String personIds);

    /**
     * 根据名单库类型获取名单库集合
     *
     * @param type
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem>
     * <AUTHOR>
     * @date 2021/5/20 14:49
     * @since 1.0.0
     */
    List<PersPersonnalListItem> getItemByType(String type);

    /**
     * 根据名单库类型获取初始化允许、禁止名单库信息
     *
     * @param type
     * @return com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem
     * <AUTHOR>
     * @date 2021-05-28 17:38
     * @since 1.0.0
     */
    PersPersonnalListItem getInitItemByType(String type);

    /**
     * 根据ids获取集合对象
     *
     * @param ids
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem>
     * <AUTHOR>
     * @date 2021/5/20 16:21
     * @since 1.0.0
     */
    List<PersPersonnalListItem> findByIdList(List<String> ids);

    /**
     * 根据ids获取名单库人员集合对象
     *
     * @param ids
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonnalListItem>
     * <AUTHOR>
     * @date 2021/5/20 16:21
     * @since 1.0.0
     */
    List<PersPersonnallistPersonItem> findByPersonnallistIdIn(List<String> ids);

    /**
     * pin隐藏部分字符
     * 
     * @param items:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPersonnallistPersonItem>
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 15:51
     * @since 1.0.0
     */
    List<PersPersonnallistPersonItem> protectPin(List<PersPersonnallistPersonItem> items);

}
