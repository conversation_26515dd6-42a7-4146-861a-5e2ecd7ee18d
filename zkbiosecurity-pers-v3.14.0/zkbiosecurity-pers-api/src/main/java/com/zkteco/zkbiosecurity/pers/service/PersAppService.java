package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.vo.AppResultMessage;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPhotoItem;
import com.zkteco.zkbiosecurity.pers.app.vo.PersAppPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * <AUTHOR>
 * @Date: 2018/11/30 11:06
 */
public interface PersAppService {

    /**
     * 新增/编辑APP人员
     * 
     * @auther lambert.li
     * @date 2018/11/30 11:59
     * @param personBean
     * @return
     */
    AppResultMessage editAppPerson(PersAppPersonItem personBean);

    /**
     * 根据登录用户权限获取部门信息
     * 
     * @auther lambert.li
     * @date 2018/12/3 10:07
     * @param token
     * @return
     */
    AppResultMessage getDepts(String token);

    /**
     * 人员数量统计: 统计人员总数、人员持有卡总数、指纹总数、指静脉总数、人脸统计、性别统计
     * 
     * @auther lambert.li
     * @date 2018/12/3 11:38
     * @return
     */
    AppResultMessage getPersonDataCount();

    /**
     * 根据人员pin、姓名模糊查询人员 如果pin和姓名传入为空则查询该用户权限下的所有人员
     * 
     * @auther lambert.li
     * @date 2018/12/3 13:57
     * @param filter
     * @param token
     * @param pageNo
     * @param pageSize
     * @return
     */
    AppResultMessage getPersonByPinOrName(String filter, String token, int pageNo, int pageSize);

    /**
     * 根据人员编号获取人员信息
     * 
     * @auther lambert.li
     * @date 2018/12/3 16:31
     * @param pin
     * @param token
     * @return
     */
    AppResultMessage getPersonByPin(String pin, String token);

    /**
     * 根据人员编号获取人员权限组
     * 
     * @auther lambert.li
     * @date 2018/12/11 18:17
     * @param pin
     * @param token
     * @return
     */
    AppResultMessage getAccLevelsByPin(String pin, String token);

    /**
     * 根据人员姓名、部门模糊查询人员
     * 
     * @param condition:
     * @param filter:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2024-07-01 16:07
     * @since 1.0.0
     */
    AppResultMessage getPersonByNameOrDeptName(PersPersonItem condition, String filter, int pageNo, int pageSize);

    /**
     * 更新人员信息（目前只更新人员照片）
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2024-07-17 17:29
     * @since 1.0.0
     */
    AppResultMessage updatePersonInfo(PersApiPhotoItem person);

    /**
     * 获取员工列表
     * 
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @param username:
     * @param loginType:
     * @return com.zkteco.zkbiosecurity.base.vo.AppResultMessage
     * <AUTHOR>
     * @throws
     * @date 2025-04-17 10:24
     * @since 1.0.0
     */
    AppResultMessage getPersonList(PersPersonItem condition, int pageNo, int pageSize, String username,
        String loginType);
}
