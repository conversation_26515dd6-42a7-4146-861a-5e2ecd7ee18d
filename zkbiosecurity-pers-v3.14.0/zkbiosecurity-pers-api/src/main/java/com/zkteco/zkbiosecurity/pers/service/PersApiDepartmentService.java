package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiDepartmentItem;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/16 13:58
 */
public interface PersApiDepartmentService {

    /**
     * 新增/编辑部门
     * 
     * @auther lambert.li
     * @date 2018/11/9 14:21
     * @param
     * @return
     */
    ApiResultMessage updateDepartment(PersApiDepartmentItem department);

    /**
     * 根据部门编号删除部门
     * 
     * @auther lambert.li
     * @date 2018/11/9 14:21
     * @param
     * @return
     */
    ApiResultMessage deleteDeptByCode(String code);

    /**
     * 根据部门编号获取部门信息
     * 
     * @auther lambert.li
     * @date 2018/11/9 14:22
     * @param
     * @return
     */
    ApiResultMessage getDeptByCode(String code);

    Pager getApiDepartmentByPage(int pageNo, Integer pageSize);
}
