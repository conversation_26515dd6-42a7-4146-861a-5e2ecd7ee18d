/**
 * File Name: PersPosition Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.List;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersPositionItem;

/**
 * 对应百傲瑞达 PersPositionService
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersPositionService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    PersPositionItem saveItem(PersPositionItem item);

    /**
     * 检查是否是祖先结点
     * 
     * @param positionId
     * @param ancestorPositionId
     * @return
     */
    Boolean checkAncestor(String positionId, String ancestorPositionId);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<PersPositionItem> getByCondition(PersPositionItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    PersPositionItem getItemById(String id);

    /**
     * 根据code查询
     * 
     * @param code
     * @return
     */
    PersPositionItem getItemByCode(String code);

    /**
     * 根据 positionIds获取职位列表
     * 
     * @param positionIds
     * @return
     */
    List<PersPositionItem> getItemByIds(String positionIds);

    /**
     * 判断职位名称是否存在
     * 
     * @param id
     * @param parentId
     * @param name
     * @return
     */
    Boolean isPositionNameExist(String id, String parentId, String name);

    /**
     * 根据父id获取所有的职位id
     * 
     * @param parentId
     * @return
     */
    String getPositionIdsByParentId(String parentId);

    /**
     * 初始化职位
     * 
     * @param item
     * @return
     */
    PersPositionItem initData(PersPositionItem item);

    /**
     * 为导出职位提供数据
     * 
     * @param cls
     * @param condition
     * @param beginIndex
     * @param endIndex
     * @return
     */
    List<?> getPersPositionItemData(Class<?> cls, BaseItem condition, int beginIndex, int endIndex);

    /**
     * 职位数据迁移
     *
     * @param positionItems
     */
    void handlerTransfer(List<PersPositionItem> positionItems);

    /**
     * 从二号项目迁移代码：批量保存职位数据
     * 
     * @auther zbx.zhong
     * @date 2019/7/4 11:33
     * @param persPositionItemList
     * @return
     */
    void batchSaveItemFromCloud(List<PersPositionItem> persPositionItemList);

    /**
     * 根据codes查询列表
     * 
     * <AUTHOR>
     * @Date 2019/6/14 14:58
     * @param codes
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersPositionItem>
     */
    List<PersPositionItem> getItemsByCodes(Collection<String> codes);

    /**
     * 更新职位信息 用于云端职位下发
     * 
     * <AUTHOR>
     * @Date 2019/6/17 9:21
     * @param persPositionItems
     * @return void
     */
    void updatePersPositionItem(List<PersPositionItem> persPositionItems);

    /*
     * 导入职位
     * <AUTHOR>
     * @Date 2020/7/26 16:45
     * @param itemList
     * @Return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg importExcel(List<PersPositionItem> itemList);

    /**
     * 判断父类职位是否是自己或是自己的下级
     * 
     * @param ids:
     * @param parentId:
     * @return java.lang.Boolean
     * <AUTHOR>
     * @throws
     * @date 2024-02-01 15:35
     * @since 1.0.0
     */
    Boolean isChild(List<String> ids, String parentId);
}