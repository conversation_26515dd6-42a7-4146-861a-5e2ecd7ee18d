/**
 * File Name: PersIssueCard Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersIssueCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersNoCardPersonItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * 对应百傲瑞达 PersIssueCardService
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersIssueCardService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    PersIssueCardItem saveItem(PersIssueCardItem item);

    /**
     * 根据卡保存发卡记录
     * 
     * @param cardNo
     * @param operateType
     * @param personItem
     */
    void savePersIssueCard(String cardNo, Short operateType, PersPersonItem personItem);

    /**
     * 创建发卡记录
     * 
     * @param pin
     * @param name
     * @param lastName
     * @param cardNo
     * @param operateType 操作类型：1发卡，3：挂失，4:卡过期，5：停用，6：无效卡，7：解挂，10：写管理卡，11：写卡，12：退卡，13：换卡；
     */
    void createIssueInfo(String pin, String name, String lastName, String cardNo, short operateType);

    /**
     * 根据条件查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/4/25 20:08
     * @param condition
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersIssueCardItem>
     */
    List<PersIssueCardItem> getByCondition(PersIssueCardItem condition);

    /**
     * 根据ID查询
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/4/25 20:08
     * @param id
     * @return com.zkteco.zkbiosecurity.pers.vo.PersIssueCardItem
     */
    PersIssueCardItem getItemById(String id);

    /**
     * 查询没有卡的人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/4/25 20:00
     * @param sessionId
     * @param condition
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersNoCardPersonItem>
     */
    Pager findNoCardPerson(String sessionId, PersNoCardPersonItem condition, int page, int size);

    /**
     * 批量发卡
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/12/21 16:31
     * @param cardItemList
     * @return void
     */
    void batchIssueCard(List<PersCardItem> cardItemList);

    /**
     * ACMS发卡
     * 
     * @param personIds:
     * @param deptIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-08-23 11:36
     * @since 1.0.0
     */
    void batchIssueAcmsCard(String personIds, String deptIds);

    /**
     * 加密cardNo字段
     * 
     * @return void
     * <AUTHOR>
     * @throws @date 2021-06-23 11:56
     * @since 1.0.0
     */
    void encryptPersIssueCard();

    /**
     * 获取导出数据
     * 
     * @param persIssueCardItemClass:
     * @param condition:
     * @param beginIndex:
     * @param endIndex:
     * @return java.util.List<?>
     * <AUTHOR>
     * @throws @date 2022-01-14 15:16
     * @since 1.0.0
     */
    List<?> getItemData(Class<PersIssueCardItem> persIssueCardItemClass, BaseItem condition, int beginIndex,
        int endIndex);

    /**
     * pin 卡号隐藏部分字符
     * 
     * @param items:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersNoCardPersonItem>
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 15:59
     * @since 1.0.0
     */
    List<PersNoCardPersonItem> protectPinAndCard(List<PersNoCardPersonItem> items);

    /**
     * pin 卡号隐藏部分字符
     * 
     * @param items:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersIssueCardItem>
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 16:01
     * @since 1.0.0
     */
    List<PersIssueCardItem> protectIssuePinAndCard(List<PersIssueCardItem> items);
}