package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersCardItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

public interface PersAcmsService {

    /**
     * ACMS删除卡号
     *
     * @param cardList:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2023-08-21 11:54
     * @since 1.0.0
     */
    ZKResultMsg delAcmsCard(List<PersCardItem> cardList);

    /**
     * 获取ACMS中的卡
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-03-07 17:20
     * @since 1.0.0
     */
    ZKResultMsg obtainAcmsCard(String status, String pageSize);

    /**
     * 重新分配卡号接口（闲置卡需要先重新分配才能激活）
     * 
     * @param cardId:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-03-13 14:34
     * @since 1.0.0
     */
    ZKResultMsg reassignCard(String cardId);

    /**
     * 获取ACMS服务平台参数
     * 
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-03-13 14:54
     * @since 1.0.0
     */
    ZKResultMsg getAcmsPlatefromInfo();

    /**
     * 通知ACMS发送邮件，激活卡号
     * 
     * @param activationCode:
     * @param email:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-03-13 15:47
     * @since 1.0.0
     */
    ZKResultMsg activeCredential(String activationCode, String email);

    /**
     * 人员绑定卡号
     * 
     * @param cardIdList:
     * @param persPersonItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-12-25 14:55
     * @since 1.0.0
     */
    ZKResultMsg assignPersonCard(List<String> cardIdList, PersPersonItem persPersonItem);

    /**
     * 根据人员编号更新人员信息
     * 
     * @param persPersonItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-12-26 11:47
     * @since 1.0.0
     */
    ZKResultMsg updateAcmsPersonByPin(PersPersonItem persPersonItem);

    /**
     * 删除ACMS上人员
     * 
     * @param pinList:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-12-27 14:44
     * @since 1.0.0
     */
    ZKResultMsg deleteAcmsPerson(List<String> pinList);

}
