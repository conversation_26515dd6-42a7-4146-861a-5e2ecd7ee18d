package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ApiResultMessage;
import com.zkteco.zkbiosecurity.pers.api.vo.ApiPersonBaseInfoItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiLeavePersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPersonItem;
import com.zkteco.zkbiosecurity.pers.api.vo.PersApiPhotoItem;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

/**
 * @Auther: lambert.li
 * @Date: 2018/11/29 15:54
 */
public interface PersApiPersonService {

    /**
     * API添加人员
     * 
     * @auther lambert.li
     * @date 2018/11/16 14:27
     * @param person
     * @return
     */
    ApiResultMessage addApiPerson(PersApiPersonItem person);

    /**
     * 根据pin获取接口人员信息
     * 
     * @auther lambert.li
     * @date 2018/11/16 16:15
     * @param pin
     * @return
     */
    ApiResultMessage getApiPersonByPin(String pin);

    /**
     * 判断人员编号是否异常
     * 
     * @auther lambert.li
     * @date 2018/11/8 16:16
     * @param pin
     * @return
     */
    int checkPin(String pin);

    /**
     * 判断密码是否重复
     * 
     * @auther lambert.li
     * @date 2018/11/8 16:16
     * @param persPersonItem
     * @return
     */
    int checkPwd(PersPersonItem persPersonItem);

    /**
     * 根据pin获取动态二维码
     * 
     * @param pin:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2021-09-27 10:01
     * @since 1.0.0
     */
    ApiResultMessage getQrCodeByPin(String pin);

    /**
     * API人员离职
     * 
     * @param persApiLeavePersonItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2021-11-11 16:36
     * @since 1.0.0
     */
    ApiResultMessage leaveApiPerson(PersApiLeavePersonItem persApiLeavePersonItem);

    /**
     * 添加/编辑人员基础信息
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2021-11-16 13:59
     * @since 1.0.0
     */
    ApiResultMessage addPersonnelBasicInfo(ApiPersonBaseInfoItem person);

    /**
     * 更新人员照片信息
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws @date 2021-11-17 13:53
     * @since 1.0.0
     */

    ApiResultMessage updatePersonnelPhoto(PersApiPhotoItem person);

    /**
     * 根据人员pin数组和部门code数组分页获取人员信息
     * 
     * @param pins:
     * @param deptCodes:
     * @param pageNo:
     * @param pageSize:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.api.vo.PersApiPersonItem>
     * <AUTHOR>
     * @throws @date 2021-11-24 15:55
     * @since 1.0.0
     */
    List<PersApiPersonItem> getApiPersonList(String pins, String deptCodes, int pageNo, int pageSize);

    /**
     * 分页获取API人员
     *
     * @param pins:
     * @param deptCodes:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date 2022-11-11 16:23
     * @since 1.0.0
     */
    Pager getApiPersonByPage(String pins, String deptCodes, int pageNo, Integer pageSize);

    /**
     * 分页获取API人员
     * 
     * @param persPersonItem:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date 2025-03-05 14:51
     * @since 1.0.0
     */
    Pager getApiPersonByPage(PersPersonItem persPersonItem, int pageNo, Integer pageSize);

    /**
     * API复职员
     * 
     * @param person:
     * @return com.zkteco.zkbiosecurity.base.vo.ApiResultMessage
     * <AUTHOR>
     * @throws
     * @date 2023-03-28 14:59
     * @since 1.0.0
     */
    ApiResultMessage reinstatedApiPerson(PersApiPersonItem person);
}
