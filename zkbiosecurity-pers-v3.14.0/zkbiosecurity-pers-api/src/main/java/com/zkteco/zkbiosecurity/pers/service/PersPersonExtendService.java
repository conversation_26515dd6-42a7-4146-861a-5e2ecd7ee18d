/**
 * File Name: PersAttributeExt
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.pers.vo.PersPersonItem;

import java.util.List;

/**
 * 无效接口定义，禁止使用、废弃
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
@Deprecated
public interface PersPersonExtendService {

	void editPersonExt(List<PersPersonItem> personItems, String module);
}