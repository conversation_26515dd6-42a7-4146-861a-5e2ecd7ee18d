package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.api.vo.AppPersonItem;

/**
 * 兼容云和线下服务
 * 
 * <AUTHOR>
 * @version TODO 添加版本
 * @since 2019年6月21日 下午2:26:55
 */
public interface PersAppBioService {
	/**
	 * 注册
	 * 
	 * <AUTHOR>
	 * @since 2019年6月24日 上午10:47:54
	 * @param appPersonItem
	 * @return
	 */
	public ZKResultMsg savePersonRegister(AppPersonItem appPersonItem);

	/**
	 * 保存照片
	 * 
	 * <AUTHOR>
	 * @since 2019年6月24日 上午10:48:01
	 * @param mobile
	 * @param photoBase64
	 * @return
	 */
	public ZKResultMsg savePersonPic(String mobile, String photoBase64);

	/**
	 * 退出登录
	 * 
	 * <AUTHOR>
	 * @since 2019年6月24日 下午2:44:29
	 * @param token
	 */
	public void persLoginOut(String token);

	/**
	 * 获取用户个人信息
	 * 
	 * <AUTHOR>
	 * @since 2019年6月24日 下午2:49:07
	 * @param customerId
	 * @return
	 */
	public AppPersonItem getUserInfoDetail(String customerId);

	/**
	 * 编辑信息
	 * 
	 * <AUTHOR>
	 * @since 2019年6月24日 下午5:23:54
	 * @param appPersonItem
	 * @return
	 */
	public ZKResultMsg changeUserInfo(AppPersonItem appPersonItem);

	/**
	 * 获取人事参数
	 * 
	 * <AUTHOR>
	 * @since 2019年6月25日 上午9:10:31
	 * @return
	 */
	public ZKResultMsg getPersParams();

	/**
	 * 发送修改密码的邮件
	 * 
	 * <AUTHOR>
	 * @since 2019年6月27日 下午6:41:27
	 * @param pin
	 * @param email
	 * @param header http/https
	 * @return
	 */
	public ZKResultMsg sendEmailForResetPassword(String pin, String email,String header);
}
