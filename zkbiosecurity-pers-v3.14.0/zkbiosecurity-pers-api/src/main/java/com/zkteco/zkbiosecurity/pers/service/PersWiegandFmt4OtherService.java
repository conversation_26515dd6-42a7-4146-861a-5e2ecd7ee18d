package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.pers.vo.PersWiegandFmtItem;

/**
 * <AUTHOR>
 * @date 2021/10/11 11:21
 * @since 1.0.0
 */
public interface PersWiegandFmt4OtherService {

    /**
     * 判断该韦根格式是否被其他模块使用 若被使用，抛出异常throw ZKBusinessException.warnException(msg);
     *
     * @param wiegandFmtIds:韦根格式ids
     * @return void
     * <AUTHOR>
     * @date 2021-10-11 11:23
     * @since 1.0.0
     */
    default void checkUseWiegandFmt(String wiegandFmtIds) {}

    /**
     * 编辑韦根格式
     *
     * @param wiegandFmtId:韦根格式id
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2021-10-11 11:38
     * @since 1.0.0
     */
    default void editWiegandFmt(String wiegandFmtId) {}

    /**
     * 删除韦根格式
     *
     * @param wiegandFmts
     * @return void
     * <AUTHOR>
     * @date 2021-10-11 11:38
     * @since 1.0.0
     */
    default void delWiegandFmt(List<PersWiegandFmtItem> wiegandFmts) {}
}
