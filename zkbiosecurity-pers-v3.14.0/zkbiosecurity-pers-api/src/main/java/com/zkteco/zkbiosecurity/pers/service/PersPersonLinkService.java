/**
 * File Name: PersAttributeExt
 * Created by GenerationTools on 2018-02-24 上午09:38
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersPersonLinkItem;

import java.util.List;

/**
 * 对应百傲瑞达 PersAttributeExtService
 * <AUTHOR>
 * @date:	2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersPersonLinkService extends BaseService {

	/**
	 * 保存item实体，一般会有复杂业务逻辑
	 * @param item
	 */
	PersPersonLinkItem saveItem(PersPersonLinkItem item);

	/**
	 * 批量保存,该接口，一次性不能保存太多数据，超过数据需要分批次调用保存
	 * @param linkTypeEnum
	 * @param linkId
	 * @param personIds
	 */
	void saveBatchItem(PersPersonLinkItem.LinkTypeInterFace linkTypeEnum, String linkId, List<String> personIds);

	/**
	 * 根据条件查询
	 * @param condition
	 * @return
	 */
	List<PersPersonLinkItem> getByCondition(PersPersonLinkItem condition);

    /**
     * 根据人员ID删除
     * @param personId
     */
	void deleteByPersonId(String personId);

	/**
	 * 根据ID查询
	 * @param id
	 * @return
	 */
	PersPersonLinkItem getItemById(String id);

	/**
	 * 根据人员ID查询
	 * @param personId
	 * @return
	 */
	PersPersonLinkItem getItemByPersonId(String personId);

	/**
	 * 根据人员personIds查询
	 * @param personIds
	 * @return
	 */
	List<PersPersonLinkItem> getItemByPersonIds(String personIds);

	/**
	 * 其他模块删除人员调用的接口
	 * @author: mingfa.zheng
	 * @date: 2018/4/20 16:48
	 * @return:
	 */
	void deletePerson(PersPersonLinkItem persPersonLinkItem);

	/**
	 * 批量删除
	 * @param linkTypeEnum
	 * @param linkIds
	 * @param personIds
	 * @author: mingfa.zheng
	 * @date: 2018/4/23 16:51
	 * @return:
	 */
	void deleteBatchItem(PersPersonLinkItem.LinkTypeInterFace linkTypeEnum, List<String> linkIds, List<String> personIds);

	/**
	 * 根据linkId批量删除
	 * @author: mingfa.zheng
	 * @date: 2018/5/10 15:24
	 * @return:
	 */
	void deleteBatchItemByLinkId(PersPersonLinkItem.LinkTypeInterFace linkTypeEnum, List<String> linkIds);

	/**
     * 保存逻辑（不做重复判断逻辑）
	 * <AUTHOR>
	 * @Date 2019/8/7 17:04
	 * @param
	 * @return
	 */
    PersPersonLinkItem save(PersPersonLinkItem item);

    /**
     * 批量保存(供考勤数据迁移时调用)
     * <AUTHOR> href="mailto:<EMAIL>">jinxian.huang</a>
     * @date 2019/8/12 18:24
     * @param linkItemList
     * @return void
     */
    void saveBatchPersonLinkItem(List<PersPersonLinkItem> linkItemList);
}