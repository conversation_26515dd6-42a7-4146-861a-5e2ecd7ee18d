package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.pers.vo.PerTempRegItem;

/**
 * 移动端注册相关业务
 * 
 * <AUTHOR> href="mailto:<EMAIL>">colin.cheng</a>
 * @version V1.0
 * @date Created In 14:51 2019/3/28
 */
public interface PerTempRegService {

    /**
     * 注册临时人员
     * 
     * @return
     */
    String perReg(PerTempRegItem item);

    /**
     * 检验pin是否重复
     * 
     * @param pin
     * @return
     */
    Boolean checkPin(String pin);

    /**
     * 获取隐私协议内容
     *
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date 2023-09-07 10:42
     * @since 1.0.0
     */
    String getPrivacyContent();

}
