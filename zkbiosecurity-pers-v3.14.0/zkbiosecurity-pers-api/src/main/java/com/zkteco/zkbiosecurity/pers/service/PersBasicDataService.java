package com.zkteco.zkbiosecurity.pers.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 *
 * <AUTHOR>
 * @Date: 2019/7/2 14:10
 */
public interface PersBasicDataService {
    /**
     * 从二号项目迁移代码：同步所有人事基础数据到云端
     * @auther zbx.zhong
     * @date 2019/7/7 14:12
     * @return
     */
    ZKResultMsg syncAllPersonToCloud();

    /**
     * 从二号项目迁移代码：从云端同步所有人事基础数据
     * @auther zbx.zhong
     * @date 2019/7/3 14:12
     * @return
     */
    ZKResultMsg syncAllPersonFromCloud();
}
