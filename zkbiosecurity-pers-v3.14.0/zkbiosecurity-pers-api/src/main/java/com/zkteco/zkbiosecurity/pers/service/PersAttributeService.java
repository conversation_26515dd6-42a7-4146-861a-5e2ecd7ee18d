/**
 * File Name: PersAttribute Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeItem;

/**
 * 对应百傲瑞达 PersAttributeService
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersAttributeService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    PersAttributeItem saveItem(PersAttributeItem item);

    /**
     * 初始化数据用
     * 
     * @param item
     * @return
     */
    PersAttributeItem initData(PersAttributeItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<PersAttributeItem> getByCondition(PersAttributeItem condition);

    /**
     * 获取默认初始化的数据
     * 
     * @return
     */
    List<PersAttributeItem> getItemsByInit();

    /**
     * 获取所有显示要显示的自定义属性数据
     * 
     * @return
     */
    List<PersAttributeItem> getItemsByShow();

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    PersAttributeItem getItemById(String id);

    /**
     * 根据属性名查找ids
     * 
     * @param attrName
     * @return
     */
    List<String> findIdsByAttrName(String attrName);

    /**
     * 自定义属性数据迁移
     *
     * @param attributeItems
     */
    void handlerTransfer(List<PersAttributeItem> attributeItems);

    /**
     * 获取初始化且控件类型为text的属性列表
     * 
     * <AUTHOR>
     * @since 2019-09-02 16:58
     * @Param [controlType]
     * @return
     */
    List<PersAttributeItem> getItemsByControlType(String controlType);

    /**
     * 删除防疫相关自定义属性
     * 
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-03-17 14:47
     * @since 1.0.0
     */
    void delHepAttribute();
}