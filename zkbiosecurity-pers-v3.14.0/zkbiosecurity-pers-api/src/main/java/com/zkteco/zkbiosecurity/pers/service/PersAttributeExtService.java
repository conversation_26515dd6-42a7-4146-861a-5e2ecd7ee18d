/**
 * File Name: PersAttributeExt Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.pers.vo.PersAttributeExtItem;

/**
 * 对应百傲瑞达 PersAttributeExtService
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-02-24 上午09:38
 */
public interface PersAttributeExtService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     *
     * @param item
     */
    PersAttributeExtItem saveItem(PersAttributeExtItem item);

    /**
     * 批量保存item实体
     * 
     * <AUTHOR>
     * @param itemList
     * @return void
     * @date 2019/7/10 15:50
     */
    void batchSaveAttributeExts(List<PersAttributeExtItem> itemList);

    /**
     * 根据条件查询
     *
     * @param condition
     * @return
     */
    List<PersAttributeExtItem> getByCondition(PersAttributeExtItem condition);

    /**
     * 根据ID查询
     *
     * @param id
     * @return
     */
    PersAttributeExtItem getItemById(String id);

    /**
     * 删除人员的自定义属性
     *
     * @param personId
     */
    void deleteByPersonId(String personId);

    /**
     * 根据人员ID查询
     *
     * @param personId
     * @return
     */
    PersAttributeExtItem getItemByPersonId(String personId);

    /**
     * 根据人员personIds查询
     *
     * @param personIds
     * @return
     */
    List<PersAttributeExtItem> getItemByPersonIds(String personIds);

    /**
     * 根据人员personIdList查询
     *
     * @param personIdList
     * @return
     */
    List<PersAttributeExtItem> getItemByPersonIdList(List<String> personIdList);

    /**
     * 自定义属性扩展数据迁移
     *
     * @param attributeExtItems
     */
    void handlerTransfer(List<PersAttributeExtItem> attributeExtItems);

    /**
     * 根据列索引将值置为空
     * 
     * @param filedIndex:
     * @return void
     * <AUTHOR>
     * @throws @date
     *             2021-01-14 15:56
     * @since 1.0.0
     */
    void deleteAttrValueByFiledIndex(int filedIndex);

}