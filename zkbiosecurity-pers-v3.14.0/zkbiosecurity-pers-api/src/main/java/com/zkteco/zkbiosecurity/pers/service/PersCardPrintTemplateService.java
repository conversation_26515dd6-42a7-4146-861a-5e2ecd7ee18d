/**
 * File Name: PersCardPrintTemplate Created by GenerationTools on 2018-02-24 上午09:38 Copyright:Copyright © 1985-2018
 * ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.pers.service;

import java.util.Map;

/**
 * 对应百傲瑞达 PersCardPrintTemplateService
 * 
 * <AUTHOR>
 * @date: 2018-02-24 上午09:38
 * @version v1.0
 */
public interface PersCardPrintTemplateService {

    /**
     * 获取模板信息
     * 
     * <AUTHOR>
     * @param templateId
     * @return java.lang.String
     * @date 2019/2/12 17:45
     */
    Map<String, String> getPrintTemplate(String templateId);

    /**
     * 判断是否有制卡许可是否显示制卡按钮
     * 
     * <AUTHOR>
     * @param
     * @return java.lang.Boolean
     * @date 2019/2/28 15:12
     */
    Boolean isShowCardPrint();

    /**
     * 获得用于预览的人员
     * 
     * <AUTHOR>
     * @param personId
     * @return java.util.Map<java.lang.String,java.lang.String>
     * @date 2019/9/3 15:46
     */
    Map<String, String> getPersonData(String personId);
}