package com.zkteco.zkbiosecurity.pers.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.pers.vo.*;

/**
 * 通知其他模块进行统一管理
 * 
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 14:06 2018/11/29
 */
public interface Pers2OtherService {
    /**
     * 通知其他模块，进行了编辑人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/29 14:52
     * @param personItem
     * @param extParams
     * @return void
     */
    void pushEditPerson(PersPersonItem personItem, Map<String, String> extParams);

    /**
     * 删除其他模块人员
     *
     * @param personIds
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/4 14:32
     */
    boolean pushDelOtherModelPerson(String personIds);

    /**
     * 批量调整部门
     * 
     * @param personIds
     * @param deptId
     * @param flag
     */
    void pushBatchDeptChange(List<String> personIds, String deptId, boolean flag);

    /**
     * 通知其他模块批量保存人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/29 14:33
     * @param personItems
     * @param module
     * @return void
     */
    void pushBatchSaveItem(List<PersPersonItem> personItems, String module);

    /**
     * 批量导入人员，通知其他模块
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/11/29 14:43
     * @param personItems
     * @return void
     */
    void pushBatchImportPerson(List<PersPersonItem> personItems);

    /**
     * 检测胁迫密码
     * 
     * @param personPwd
     * @return
     */
    boolean checkForcePwd(String personPwd);

    /**
     * 检测卡是否被使用。
     * 
     * @param cardNo
     * @return
     */
    void checkIsUseCardNo(String cardNo);

    /**
     * 检测短信猫参数设置
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-14 17:14
     * @param
     * @return boolean
     */
    boolean completeSMSModemInfo();

    /**
     * 检测是否显示勾选发送短信的控件
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-01-22 16:07
     * @param
     * @return boolean
     */
    boolean checkShowSMS();

    /**
     * 通知其他模块，删除人员生物模板
     * 
     * @param personIds:
     * @param bioTemplateTypes:
     * @return void
     * <AUTHOR>
     * @throws @date 2020-09-29 9:50
     * @since 1.0.0
     */
    void deletePersonBioTemplate(Collection<String> personIds, Collection<Short> bioTemplateTypes);

    /**
     * 用于模块上传人员数据分发给其他模块
     * 
     * @param items
     * @param module
     */
    void setPersonInfo2OtherModule(Collection<PersPersonInfo2OtherItem> items, String module);

    /**
     * 用于模块上传人员模版数据分发给其他模块
     * 
     * @param items
     * @param module
     */
    void setBioTemplate2OtherModule(Collection<PersBioTemplate2OtherItem> items, String module);

    /**
     * 用于模块上传人员比对照片分发给其他模块
     * 
     * @param items
     * @param module
     */
    void setBioPhoto2OtherModule(Collection<PersBioPhoto2OtherItem> items, String module);

    /**
     * 用于模块上传人员头像照片分发给其他模块
     * 
     * @param items
     * @param module
     */
    void setUserPic2OtherModule(Collection<PersUserPic2OtherItem> items, String module);

    /**
     * 通知其他模块人员离职
     *
     * @param leaveIds
     * @return void
     * <AUTHOR>
     * @date 2021-01-19 16:54
     * @since 1.0.0
     */
    void pushLeavePerson(String leaveIds);

    /**
     * 通知其他模块离职人员删除
     *
     * @param leaveIds
     * @return void
     * <AUTHOR>
     * @date 2021-02-04 10:27
     * @since 1.0.0
     */
    void pushLeavePersonDel(String leaveIds);

    /**
     * 通知其他模块更新用户抠图照片
     * 
     * @param item:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-11-18 10:25
     * @since 1.0.0
     */
    void pushBioPhoto2OtherModules(PersBioPhoto2OtherItem item);

    /**
     * 通知其他模块更新用户照片
     * 
     * @param item:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-11-18 10:25
     * @since 1.0.0
     */
    void pushUserPic2OtherModules(PersUserPic2OtherItem item);

    /**
     * 检测卡号是否已存在
     * 
     * @param cardNo:
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2022-09-21 17:28
     * @since 1.0.0
     */
    boolean checkExistCardNo(String cardNo);

    /**
     * 判断WhatsApp许可
     * 
     * @return java.lang.Boolean
     * <AUTHOR>
     * @throws
     * @date 2023-03-21 10:03
     * @since 1.0.0
     */
    Boolean checkShowWhatsapp();

    /**
     * 检测whatsapp参数设置
     * 
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2023-03-21 14:07
     * @since 1.0.0
     */
    boolean completeWhatsappModemInfo();

    /**
     * 离职通知其他模块
     * 
     * @param persPushLeavePersonItems:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-03-29 11:32
     * @since 1.0.0
     */
    void pushLeaveOtherModelPerson(List<PersPushLeavePersonItem> persPushLeavePersonItems);

    /**
     * 通知其他模块删除离职人员
     *
     * @param persPushLeavePersonItems:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2023-03-29 14:40
     * @since 1.0.0
     */
    void pushLeavePersonDel(List<PersPushLeavePersonItem> persPushLeavePersonItems);
}
