package com.zkteco.zkbiosecurity.pers.service;

/**
 * 人事功能判断
 *
 * <AUTHOR>
 * @date 2021-02-25 14:21
 * @since 1.0.0
 */
public interface PersSupportFuncService {

    /**
     * 是否支持制卡
     * 
     * @return
     */
    boolean isSupportPrintTemplate();

    /**
     * 是否支持离职功能
     * 
     * @return
     */
    boolean isSupportPersonLeave();

    /**
     * 是否支持职位
     * 
     * @return
     */
    boolean isSupportPosition();

    /**
     * 是否支持卡挂失
     * 
     * @return
     */
    boolean isSupportLossCard();

    /**
     * 是否支持韦根格式测试
     * 
     * @return
     */
    boolean isSupportWGFmtTest();

    /**
     * 是否支持重置自助登录密码
     * 
     * @return
     */
    boolean isSupportResetSelfPwd();
}
