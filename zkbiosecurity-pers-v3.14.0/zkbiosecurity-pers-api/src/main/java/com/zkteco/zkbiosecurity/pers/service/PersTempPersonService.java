package com.zkteco.zkbiosecurity.pers.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.service.BaseService;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.pers.vo.PersTempPersonItem;

public interface PersTempPersonService extends BaseService {

    /**
     * 保存item实体，一般会有复杂业务逻辑
     * 
     * @param item
     */
    PersTempPersonItem saveItem(PersTempPersonItem item);

    /**
     * 根据条件查询
     * 
     * @param condition
     * @return
     */
    List<PersTempPersonItem> getByCondition(PersTempPersonItem condition);

    /**
     * 根据ID查询
     * 
     * @param id
     * @return
     */
    PersTempPersonItem getItemById(String id);

    /**
     * 人员审核
     *
     * @param item
     */
    ZKResultMsg audit(PersTempPersonItem item);

    /**
     * 根据权限用户查询人员
     *
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthUserFilter(String sessionId, PersTempPersonItem condition, int pageNo, int pageSize);

    /**
     * 人员登记
     *
     * @param persTempPersonItem
     * @return
     */
    ZKResultMsg savePersonRegistrar(PersTempPersonItem persTempPersonItem);

    /**
     * 根据mobile查询
     * 
     * <AUTHOR>
     * @since 2019年6月21日 下午5:43:45
     * @param mobile
     * @return
     */
    PersTempPersonItem getItemByMobile(String mobile);

    /**
     * 根据邮箱查询
     * 
     * <AUTHOR>
     * @since 2019年6月21日 下午5:49:27
     * @param email
     * @return
     */
    PersTempPersonItem getItemByEmail(String email);

    /**
     * 根据pin查询
     * 
     * <AUTHOR>
     * @since 2019年6月21日 下午5:59:32
     * @param pin
     * @return
     */
    PersTempPersonItem getItemByPin(String pin);

    /**
     * 从云端同步人员信息
     * 
     * @auther lambert.li
     * @date 2019/6/20 14:38
     * @param persTempPersonItems
     * @return
     */
    ZKResultMsg syncPersonFromCloud(List<PersTempPersonItem> persTempPersonItems);

    /*
     * 根据Pin集合查询
     * <AUTHOR>
     * @Date 2020/5/7 11:21
     * @param pinList
     * @Return
     */
    List<PersTempPersonItem> getItemByPins(List<String> pinList);

    /**
     * pin隐藏部分字符
     * 
     * @param items:
     * @return java.util.List<com.zkteco.zkbiosecurity.pers.vo.PersTempPersonItem>
     * <AUTHOR>
     * @throws
     * @date 2022-07-21 15:47
     * @since 1.0.0
     */
    List<PersTempPersonItem> protectPin(List<PersTempPersonItem> items);

}
