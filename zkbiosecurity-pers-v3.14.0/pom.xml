<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zkteco</groupId>
        <artifactId>zkbiosecurity-pom</artifactId>
        <version>3.3.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.zkteco</groupId>
    <artifactId>zkbiosecurity-pers</artifactId>
    <name>zkbiosecurity-pers</name>
    <packaging>pom</packaging>
    <version>${revision}</version>

    <!-- 必须在此处定义模块名, 否则软件版本显示会异常 -->
    <properties>
        <module.name>pers</module.name>
        <revision>3.14.0-RELEASE_YFDZ2025071400104</revision>
        <boot.version>3.12.0-RELEASE</boot.version>
        <module.api.version>3.13.0-RELEASE</module.api.version>
        <business.sdk.version>3.1.0-RELEASE</business.sdk.version>
        <cloud.sdk.version>3.0.2-RELEASE</cloud.sdk.version>
        <auth.version>3.11.0-RELEASE</auth.version>
        <license.version>3.10.0-RELEASE</license.version>
        <system.version>3.14.0-RELEASE</system.version>
        <foldex.version>2.5.0-RELEASE</foldex.version>
    </properties>

    <modules>
        <module>zkbiosecurity-pers-vo</module>
        <module>zkbiosecurity-pers-api</module>
        <module>zkbiosecurity-pers-remote</module>
        <module>zkbiosecurity-pers-service</module>
        <module>zkbiosecurity-pers-web</module>
        <module>zkbiosecurity-pers-system</module>
        <module>zkbiosecurity-pers-i18n</module>
        <module>zkbiosecurity-pers-client</module>
    </modules>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-system-api</artifactId>
                <version>${system.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-auth-api</artifactId>
                <version>${auth.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-license-api</artifactId>
                <version>${license.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-module-all</artifactId>
                <version>${module.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-base</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-cloud-sdk</artifactId>
                <version>${cloud.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-business-sdk</artifactId>
                <version>${business.sdk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-scheduler</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-core</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-model</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-redis</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-security</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-web</artifactId>
                <version>${boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-foldex</artifactId>
                <version>${foldex.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-scheduler</artifactId>
                <version>${boot.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <!-- 引入maven release 插件, 用于正式版本的自动发布 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!-- gitlab地址, 用于生产环境自动打TAG -->
    <scm>
        <developerConnection>scm:git:*******************************:kiosecurity/zkbiosecurity-pers.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>

    <repositories>
        <repository>
            <id>zkteco-internal-repository-releases</id>
            <name>zkteco-internal-repository-releases</name>
            <url>http://192.168.200.31:8080/artifactory/zkteco-internal-repository</url>
        </repository>
        <repository>
            <id>zkteco-internal-repository-snapshots</id>
            <name>zkteco-internal-repository-snapshots</name>
            <url>http://192.168.200.31:8080/artifactory/zkteco-internal-repository</url>
        </repository>
    </repositories>
</project>