#pers common.
#here other module can also use the label from pers.
pers_module=Персонал
pers_common_addPerson=Добавить сотрудника
pers_common_delPerson=Удалить сотрудника
pers_common_personCount=Число сотрудников
pers_common_browsePerson=Найти сотрудника
#左侧菜单
pers_person_manager=Управление сотрудниками
pers_person=Сотрудник
pers_department=Отдел
pers_leave=Уволенные сотрудники
pers_tempPerson=Ожидающие рассмотрения
pers_attribute=Дополнительные данные
pers_card_manager=Управление RFID картами
pers_card=Карты
pers_card_issue=Журнал выданных карт
pers_wiegandFmt=Тип Wiegand
pers_position=Должность
#人员
pers_person_female=Женщина
pers_person_male=Мужчина
pers_person_pin=ID сотрудника
pers_person_departmentChange=Изменить отдел
pers_personDepartment_changeLevel=Разрешение на смену отдела
pers_person_gender=Пол
pers_person_detailInfo=Детали
pers_person_accSet=Контроль доступа
pers_person_accSetting=Настройки контроля доступа
pers_person_attSet=Учет рабочего времени
pers_person_eleSet=Управление лифтом
pers_person_eleSetting=Настройки управления лифтом
pers_person_parkSet=Добавленные гос. номера
pers_person_pidSet=Настройка документа сотрудника
pers_person_insSet=FaceKiosk
pers_person_aiSet=Face Intellect
pers_person_payrollSet=Настройка оплаты труда работника
pers_person_psgSet=Настройка прохода
pers_person_lockerSet=Настройка шкафчика
pers_person_sisSet=установка безопасности
pers_person_vdbSet=Параметры визуального диалога
pers_person_firstName=Имя
pers_person_lastName=Фамилия
pers_person_name=Имя
pers_person_wholeName=Имя
pers_person_fullName=Имя Отчество Фамилия
pers_person_cardNum=Количество выданных карт
pers_person_deptNum=Количество вовлеченных отделов
pers_person_dataCount=Статистика
pers_person_regFinger=Отпечаток пальца
pers_person_reg=Регистрация
pers_person_password=Пароль
pers_person_personDate=Данные сотрудника
pers_person_birthday=День рождения
pers_person_mobilePhone=Мобильный номер
pers_person_personDevAuth=Разрешения сотрудников и устройств
pers_person_email=Эл. почта
pers_person_browse=Путь
pers_person_authGroup=Уровни доступа
pers_person_setEffective=Установить время действия
pers_person_attArea=Зона учета
pers_person_isAtt=Учет рабочего времени
pers_person_officialStaff=Постоянный сотрудник
pers_person_probationStaff=На исп. сроке
pers_person_identity_category=Наименование категории
pers_person_devOpAuth=Работа с устройством
pers_person_msg1=Фактический номер карты и код объекта должны быть заполнены одновременно!
pers_person_msg2=Пожалуйста, введите 3-4 цифры.
pers_person_msg3=Ошибка формата!
pers_person_imgPixel=(Оптимальный  размер 120*140).
pers_person_cardLengthDigit=Пожалуйста, укажите число.
pers_person_cardLengthHexadecimal=Введите номер или букву abcdef! 
pers_person_to=К
pers_person_templateCount=Количество отп. пальцев
pers_person_biotemplateCount=Количество био шаблонов
pers_person_regFace=Лицо
pers_person_regVein=Вены пальца
pers_person_faceTemplateCount=Количество шаблонов лиц
pers_person_VeinTemplateCount=Количество рисунков вен
pers_person_palmTemplateCount=Количество шабл. ладоней
pers_person_cropFace=Фото сотрудника
pers_person_cropFaceCount=Количество шаблонов лиц
pers_person_faceBiodataCount=Количество лиц(vis)
pers_person_irisCount=Число радужной оболочки
pers_person_batchToDept=Новый отдел
pers_person_changeReason=Причина перевода
pers_person_selectedPerson=Выбранный сотрудник
pers_person_duressPwdError=Пароль используется
pers_person_completeDelPerson=Удалить
pers_person_recover=Восстановить
pers_person_nameNoComma=Не должно содержать запятые.
pers_person_firstNameNotEmpty=Имя не может быть пустым.
pers_person_lastNameNotEmpty=Фамилия не может быть пустой.
pers_person_mobilePhoneValidate=Введите действительный номер мобильного телефона.
pers_person_phoneNumberValidate=Введите действительный номер телефона.
pers_person_postcodeValidate=Введите действительный индекс.
pers_person_idCardValidate=Введите действительный номер ID карты.
pers_person_pwdOnlyLetterNum=Пароль может содержать только цифры или буквы.
pers_person_certNumOnlyLetterNum=Номер документа может содержать только цифры и буквы.
pers_person_oldDeptEqualsNewDept=Новый отдел который вы отредактировали не может совпадать с существующим отделом.
pers_person_disabled=Доступ запрещен
pers_person_emailError=Введите действительный адрес e-mail.
pers_person_driverPrompt=Установите драйвер устройства! Нажмите ОК для скачивания.
pers_person_readIDCardFailed=Ошибка считывания!
pers_person_cardPrompt=Сохранить ID карты в области...
pers_person_iDCardReadOpenFailed=Считыватель ID карт не обнаружен!
pers_person_iDCardNotFound=ID карта не обнаружена, попробуйте снова!
pers_person_nameValid=Поддерживается английский, китайский, цифры, '-', '_'.
pers_person_nameValidForEN=Поддерживается английский, цифры, '-', '_'.
pers_person_pinPrompt=Пожалуйста, введите символы из английского алфавита, цифры.
pers_person_pinSet=Настройки ID сотрудника
pers_person_supportLetter=Поддержка букв
pers_person_cardsSupport=Несколько карт на сотрудника
pers_person_SupportDefault=Авто-создание ID сотруднику
pers_person_noSpecialChar=Вы не можете ввести специальные символы!
pers_person_pinInteger=Пожалуйста, введите цифры.
pers_pin_noSpecialChar=Личный номер не может содержать особые символы!
pers_op_capture=Снимок
pers_person_cardDuress=Номер карты существует
pers_person_pwdException=Исключение пароля
pers_person_systemCheckTip=Проверьте корректность формата вводимых данных!
pers_person_immeHandle=Обработать сразу
pers_person_cardSet=Настройки карты
pers_person_tempPersonSet=Настройки временных сотрудников
pers_person_cardsReadMode=Режим считывания карт
pers_person_cardsReadModeReadHead=Считать контроллером
pers_person_cardsReadModeID180=Считать через ID180
pers_person_IDReadMode=Режим считывания ID карт
pers_person_IDReadModeIDCardReader=Считыватель ID карт
pers_person_IDReadModeTcpReadHead=Считыватель TCP/IP
pers_person_physicalNo=Реальный номер ID карты
pers_person_physicalNoToCardNo=Использовать реальный номер ID карты
pers_person_ReadIDCard=Считать ID карту
pers_person_templateBioTypeNumber=Номер типа биометрического шаблона
pers_person_templateValidType=Срок действия биометрического шаблона
pers_person_templateBioType=Тип биометрического шаблона
pers_person_templateVersion=Версия биометрического шаблона
pers_person_template=Биометрический шаблон
pers_person_templateNo=Биометрический шаблон №
pers_person_templateNoIndex=Показатель биометрического шаблона
pers_person_templateDataUpdate=Обновить данные биометрического шаблона после:
pers_person_templateValidTypeNoNull=Срок действия биометрического шаблона не может быть пустым!
pers_person_templateBioTypeNoNull=Тип биометрического шаблона не может быть пустым!
pers_person_templateVersionNoNull=Версия биометрического шаблона не может быть пустым!
pers_person_templateNoNull=Биометрический шаблон не может быть пустым!
pers_person_templateNoNoNull=Номер биометрического шаблона не может быть пустым!
pers_person_templateNoIndexNoNull=Показатель биометрического шаблона не может быть пустым!
pers_person_templateError=Биометрический шаблон неверен!
pers_person_bioDuress=Охрана
pers_person_universal=Общие
pers_person_voice=Печать голосом
pers_person_iris=АРД
pers_person_retina=Сетчатка
pers_person_palmPrints=Ладонь
pers_person_metacarpalVein=Вены ладони
pers_person_visibleFace=Лица(vis)
pers_person_pinError=ID сотрудника {0} не существует, невозможно обработать эти данные!
pers_person_pinException=Исключение ID сотрудника
pers_person_pinAutoIncrement=Авто-назначение ID сотрудника
pers_person_resetSelfPwd=Сбросить пароль для входа в систему
pers_person_picMaxSize=Разрешение изображения слишком высокое, рекомендуется разрешение меньше {0}.
pers_person_picMinSize=Разрешение изображения слишком низкое, рекомендуется разрешение больше {0}.
pers_person_cropFaceShow=Просмотр изображения лица
pers_person_faceNoFound=Нераспознанные лица
pers_person_biometrics=Тип биометрии
pers_person_photo=Фото сотрудника
pers_person_visibleFaceTemplate=Видимый шаблон лица
pers_person_infraredFaceTemplate=Шаблон лица в ближнем инфракрасном диапазоне
pers_person_delBioTemplate=Удалить биометрические данные
pers_person_delBioTemplateSelect=Выберите биологический шаблон для удаления!
pers_person_infraredFace=Лицо в ближнем инфракрасном диапазоне
pers_person_notCard=Не включает карту
pers_person_notRegFinger=Не включает отпечаток пальца
pers_person_notMetacarpalVein=Без ладонной вены
pers_person_notRegVein=Не включает вены пальцев
pers_person_notIris=Не содержит радужной оболочки.
pers_person_notInfraredFaceTemplate=Не включает шаблон лица в ближнем инфракрасном диапазоне
pers_person_notVisibleFaceTemplate=Не включает шаблон лица с видимым светом
pers_person_notVisibleFacePhoto=Не включает видимые фотографии лица
pers_person_visibleFacePhoto=Видимая фотография лица
pers_person_change=Корректировка персонала
pers_person_cropFaceUsePhoto=Нужно ли показывать сравнительные фотографии как головы?
pers_person_photoUseCropFace=Нужно ли использовать изображение головы для создания контрастных фотографий?
pers_person_selectCamera=Выберите фотоаппарат
pers_person_delCropFaceMsg=Нет фотографий контраста, которые можно удалить!
pers_person_disabledNotOp=Этот человек был запрещен и не может работать!
pers_person_visiblePalm=Рука видимая
pers_person_notVisiblePalm=Без видимых лучей.
pers_person_selectDisabledNotOp=В выбранном персонале есть запрещенный персонал, который не может работать!
pers_person_photoUseCropFaceAndTempalte=Используется ли аватар для создания шаблонов сравнения фотографий и лиц?
pers_person_photoUseTempalte=Используются ли фотографии для создания шаблонов лиц?
pers_person_createCropFace=Создать сравнение фотографий
pers_person_createFaceTempalte=Создать шаблон лица
pers_person_faceTempalte=Шаблоны: Лицо
pers_person_extractFaceTemplate=Шаблоны: Извлечение лиц
pers_person_createSuccess=Создание успешно
pers_person_createFail=Ошибка генерации
pers_person_serverConnectWarn=Адрес сервера, имя пользователя, пароль не могут быть пустыми!
pers_person_serverOffline=Шаблон извлечения лица из сервера
pers_person_faceTemplateError1=Ошибка обнаружения лица.
pers_person_faceTemplateError2=Лицо закрывается.
pers_person_faceTemplateError3=Недостаточная ясность.
pers_person_faceTemplateError4=Слишком большое лицо.
pers_person_faceTemplateError5=Ошибка обнаружения живого тела
pers_person_faceTemplateError6=Ошибка извлечения шаблона лица
pers_person_cropFaceNoExist=Фотографии не существует.
pers_person_disableFaceTemplate=Не включена функция извлечения шаблона лица, невозможно извлечь шаблон!
pers_person_cropFacePhoto=Сравнение лиц фото
pers_person_vislightPalmPhoto=Сравнительные фотографии ладони.
pers_person_serverOfflineWarn=Сервер извлечения шаблонов лиц отключен и не может включить эту функцию!
pers_person_serverConnectInfo=Пожалуйста, проверьте подключение к серверу извлечения шаблонов лиц онлайн?
pers_person_notModified=Номер мобильного телефона нельзя изменять.
pers_person_syncAcms=Синхронизировать персонал с ACMS
pers_person_startUpdate=Начать обновление информации о сотрудниках.
pers_person_updateFailed=Обновление информации о сотрудниках не удалось!
#控制器发卡
pers_person_readCard=Выпустить карту через устройство
pers_person_stopRead=Остановить выпуск
pers_person_chooseDoor=Выберите дверь
pers_person_readCarding=Отправка карты, попробуйте позже!
#抓拍照片
pers_capture_catchPhoto=Сделать фото
pers_capture_preview=Пред просмотр
#部门
pers_dept_entity=Отдел
pers_dept_deptNo=Отдел №
pers_dept_deptName=Имя отдела
pers_dept_parentDeptNo=№ родительского отдела
pers_dept_parentDeptName=Имя родительского отдела
pers_dept_parentDept=Родительский отдел
pers_dept_note=Если новый отдел не отображается в списке, обратитесь к администратору, чтобы повторно авторизовать пользователя для редактирования отделов!
pers_dept_exit=Включает
pers_dept_auth=Связанный пользователь системы
pers_dept_parentMenuMsg=Верхний отдел не может быть одновременно выбрано и нижним отделом.
pers_dept_initDept=Основные
pers_dept_deptMarket=Отдел маркетинга
pers_dept_deptRD=Отдел разработок
pers_dept_deptFinancial=Финансовый отдел
pers_dept_nameNoSpace=Название отдела не может начинаться и заканчиваться пробелами.
pers_dept_nameExist=Название отдела уже существует
pers_dept_changeLevel=Переключиться ли на уровень этого отдела
pers_dept_noSpecialChar=Номера отделов не могут содержать особые символы!
pers_dept_NameNoSpecialChar=Название департамента не может содержать особые символы!
pers_dept_noModifiedParent=Вышестоящий отдел не может быть изменен!
#职位
pers_position_entity=Должность
pers_position_code=Должность №
pers_position_name=Название должности
pers_position_notExist=Должность не существует!
pers_position_sortNo=Уровень
pers_position_parentName=Вышестоящая должность
pers_position_parentCode=Вышестоящая должность №
pers_position_batchToPosition=Новая должность
pers_position_nameExist=Имя должности уже существует
pers_position_change=Изменить должность
pers_position_parentMenuMsg=Вышестоящая должность не может быть выбрана в качестве нижестоящей.
pers_position_nameNoSpace=Название должности не может начинаться и заканчиваться пробелами.
pers_position_existSub={0}: Содержит вложенные посты нельзя удалить
pers_position_existPerson={0}: сотрудник не может быть удален
pers_position_importTemplate=Шаблон импорта позиции
pers_position_downloadTemplate=Скачать и импортировать шаблон
pers_position_codeNotEmpty=Номер позиции не может быть пустым
pers_position_nameNotEmpty=Название позиции не может быть пустым
pers_position_nameNoSpecialChar=Имя позиции {0} не может содержать специальные символы!
pers_position_noSpecialChar=Номер позиции {0} не может быть специальным символом!
pers_position_codeLength=Номер позиции {0} превышает 30 цифр
pers_position_nameLength=Данные, чье имя позиции {0} длиннее {1}
pers_position_codeExist=номер позиции {0} уже существует
#证件
pers_cert_type=Тип документа
pers_cert_number=Номер документа
pers_cert_name=Имя документа
pers_cert_numberExist=Номер документа уже существует.
#导出
pers_export_allPersPerson=Все сотрудники
pers_export_curPersPerson=Текущий сотрудник
pers_export_template=Экспорт шаблонов
pers_export_personInfo=Экспорт сотрудников
pers_export_personInfoTemplate=Скачать шаблон импорта сотрудников
pers_export_personBioTemplate=Экспорт биом-шаблонов
pers_export_basicInfo=Общая информация
pers_export_customAttr=Дополнительные данные
pers_export_templateComment=Имя поля, первичный ключ, уникальный, разрешить нулевые значения,({0},{1},{2},{3})
pers_export_templateFileName=Шаблон импорта сотрудников
pers_export_bioTemplateFileName=Биометрический шаблон сотрудников
pers_export_deptInfo=Экспорт отдела
pers_export_deptTemplate=Скачать шаблон импорта отдела
pers_export_deptTemplateFileName=Шаблон импорта отдела
pers_export_personPhoto=Экспорт фото сотрудников
pers_export_allPhotos=Все фото (выбрать всех)
pers_export_selectPhotoToExport=Выберите начальный и конечный ID для экспорта фотографий сотрудников.
pers_export_fromId=От ID
pers_export_toId=До
pers_export_certNumberComment=Тип сертификата требуется после ввода номера сертификата.
pers_export_templateCommentName=Имя поля: ({0})
pers_export_dataExist=Убедитесь, что импортированные данные уже существуют в системе.
pers_export_cardNoTip=Несколько номеров разделены &.
pers_carNumber_importTip=Гос. номер (несколько гос. номеров& разделенных)
#导入
pers_import_certNumberExist=Номер документа {0} уже существует.
pers_import_complete=Завершить
pers_import_password=Пароль сотрудника
pers_import_fail=Ошибка строки {0}: {1}
pers_import_overData=Вы импортируете {0} сотрудников; Система поддерживает импорт только до 30 000 сотрудников!
pers_import_pinTooLong=ID сотрудника ({0}) слишком длинный!
pers_import_pinExist=ID сотрудника ({0}) уже существует!
pers_import_pinIsRepeat=ID сотрудника ({0}) повторяется!
pers_import_pinError=ID сотрудника ({0}) ошибка!
pers_import_pinNotSupportLetter=ID сотрудника не поддерживает английские буквы!
pers_import_pinNotSupportNonAlphabetic=ID сотрудника не поддерживает не английские буквы!
pers_import_pinNotNull=ID сотрудника не может быть только из нулей!
pers_import_pinStartWithZero=ID сотрудника не может начинаться с нуля!
pers_import_cardNoNotSupportLetter=Номер карты не может содержать буквы!
pers_import_cardNoNotNull=Номер карты не может быть только из нулей!
pers_import_cardNoStartWithZero=Номер карты не может начинаться с нуля!
pers_import_cardTooLong=Номер карты {0} слишком длинный!
pers_import_cardExist=Номер карты {0} ошибка!
pers_import_personPwdOnlyNumber=Пароль сотрудника поддерживает только цифры!
pers_import_personPwdTooLong=Пароль сотрудника {0} слишком длинный!
pers_import_personDuressPwd=Пароль сотрудника {0} повторяется!
pers_import_emailTooLong=E-mail {0} слишком длинный!
pers_import_nameTooLong=Имя {0} слишком длинное!
pers_import_genderError=Ошибка формата пола!
pers_import_personDateError=Ошибка формата даты трудоустройства!
pers_import_createTimeError=Ошибка формата времени создания!
pers_import_phoneError=Ошибка формата телефона!
pers_import_phoneTooLong=Номера телефона не может превышать 20 символов!
pers_import_emailError=Ошибка формата e-mail!
pers_import_birthdayError=Ошибка формата дня рождения!
pers_import_nameError=Фамилия и Имя не могут содержать специальные символы! ID: {0}
pers_import_firstnameError=Имя и фамилия не могут содержать
pers_import_firstnameNotNull=Имя не может быть пустым!
pers_import_dataCheck=Ошибка проверки данных!
pers_import_dataSaveFail=Ошибка сохранение данных!
pers_import_allSucceed=Все данные успешно импортированы!
pers_import_result=Завершено: {0}, Ошибка: {1}.
pers_import_result2=Результат импорта
pers_import_result3=Завершено: {0}, Обновлено {1}, Ошибка: {2}.
pers_import_notSupportFormat=Этот формат не поддерживается!
pers_import_selectCorrectFile=Пожалуйста, выберите правильный файл.
pers_import_fileFormat=Формат файла
pers_import_targetFile=Файл назначения
pers_import_startRow=Начальные строки заголовка
pers_import_startRowNote=По умолчанию вторая строка.
pers_import_delimiter=Разделитель
pers_import_importingDataFields=Поля базы данных
pers_import_dataSourceFields=Импорт данных из полей
pers_import_total=Всего
pers_import_dataUpdate=Обновить существующий ID сотрудника в системе:
pers_import_dataIsNull=В файле нет данных!
pers_import_deptNotExist=Отдел не существует!
pers_import_deptIsNotNull=Имя отдела не может быть пустым!
pers_import_pinNotEmpty=ID сотрудника не может быть пустым!
pers_import_nameNotEmpty=Имя персонала не может быть пустым!
pers_import_siteCodeOnlyLetterNum=Ошибка формата кода объекта.
pers_import_cardNoFormatErrors=Ошибка формата карты.
pers_import_cardsNotSupport=Функция [Несколько карт на сотрудника] отключена, нельзя выдать более одной карты на сотрудника.
pers_import_personInfo=Импорт сотрудников
pers_import_commentFormat=Неправильный формат комментария!
pers_import_noComment=Данные в строке {0} и столбце {1} без комментариев.
pers_import_fieldRepeat=Данные в строке {0} и столбце {1}, имя строки в комментарии повторяется. {2}
pers_import_primaryKey=Должно быть, как минимум одно поле с основным ключом.
pers_import_templateIsRepeat=Биометрический шаблон ({0}) повторяется! 
pers_import_biologicalTemplate=Импорт био-шаблона
pers_import_uploadFileSuccess=Успешно загружено, начинаем анализировать данные, подождите...
pers_import_resolutionComplete=Данные проанализированы, начинаю обновлять базу данных.
pers_import_mustField=Импортируемый файл должен содержать столбец {0}.
pers_import_bioTemplateSuccess=Импортируйте биометрический шаблон, эти данные необходимо вручную синхронизировать с устройством из каждого рабочего-модуля.
pers_import_personPhoto=Импорт фото сотрудников
pers_import_opera_log=Журнал действий
pers_import_error_log=Журнал ошибок
pers_import_uploadFileSize=Пожалуйста, загрузите файл размером не более {0}!
pers_import_uploadFileSizeLimit=Для импорта, пожалуйста, загрузите файл размером не более 500Мб!
pers_import_type=Режим импорта
pers_import_photoType=Фото
pers_import_archiveType=Сжатый пакет
pers_import_startUpload=Начать загрузку
pers_import_addMore=Добавить еще
pers_import_photoQuality=Качество фото
pers_import_original=Оригинал
pers_import_adaptive=Адаптация
pers_import_adaptiveSize=(Размер 120 *140)
pers_import_totalNumber=Всего
pers_import_uploadTip=(Пожалуйста, не удаляйте фото при загрузке)
pers_import_addPhotoTip=Пожалуйста, добавьте фото для загрузки!
pers_import_selectPhotoTip=Пожалуйста, выберите фото для загрузки!
pers_import_uploadResult=Результат загрузки фото
pers_import_pleaseSelectPhoto=Пожалуйста, выберите фото
pers_import_multipleSelectTip=Нажмите ctrl для выделения нескольких объектов
pers_import_replacePhotoTip=Выбранная фотография уже находится в списке загрузки, вы хотите заменить ее?
pers_import_photoNamePinNotCorrespond=Название фото и ID сотрудника не соответствуют.
pers_import_photoFormatRequirement=Пожалуйста, назовите фото в соответствии с ID сотрудника. Поддерживаемые форматы: JPG/PNG. Убедитесь, что имя фото не содержит специальных символов.
pers_import_filterTip=Некоторые из выбранных вами фото не могут быть просмотрены, по следующим причинам:
pers_import_photoContainSpecialCharacters=Имя фото со специализированными символами.
pers_import_photoFormatError=Формат фото не правильный.
pers_import_photoSelectNumber=Не выбирайте больше, чем 3000 изображений!
pers_import_photoSelectNumberLimit=Не выбирайте более {0} изображений!
pers_import_fileMaxSize=Изображение слишком большое, загрузите файл размером менее 5Mб.
pers_import_notUploadPhotoNumber=Нельзя загрузить больше, чем 500 изображений!
pers_import_zipFileNotPhoto=В файле zip нет фотографии этого человека, пожалуйста, выберите заново и импортируйте!
pers_import_personPlateRepeat=Гос. номер авто сотрудника {0} повторяется!
pers_import_filePlateRepeat=Файл, включающий гос. номер {0} повторяется!
pers_import_personPlateFormat=Гос. номер авто сотрудника {0} не верный!
pers_import_personPlateMax=Количество гос. авто номеров сотрудников превышает максимально допустимое значение 6!
pers_import_cropFaceFail=Не удалось создать фото для сравнения!
pers_import_pinLeaved=Сотрудник номер: {0} ушел.
pers_import_exceedLicense=Импорт превышающего лицензии числа людей запрещен.
pers_import_bioTemplateNotNull=Номер сотрудника, тип создания, био-ID, био-индекс, содержание, версия не могут быть пустыми!
pers_import_certTypeNotNull=Номер сотрудника: {0} Тип документа не может быть пустым
pers_import_certTypeNotExist=Персональный номер: {0} Тип документа не существует
pers_import_certNumNotNull=Номер персонала: 0: Номер сертификата не может быть пустым
pers_import_certNumberTooLong=Строка {0}: номер ID {1} слишком длинный!
pers_import_idNumberErrors=Строка {0}: номер ID {1} поврежден!
pers_import_emailErrors=Строка {0}: ошибка формата адреса эл. почты {1}!
pers_import_emailIsExist=Адрес эл. почты {0} уже существует!
pers_import_emailIsRepeat=Строка {0}: внутренний адрес эл. почты в файле {1} повторяется!
pers_import_fileMobilePhoneRepeat=Строка {0}: внутренний номер мобильного телефона файла {1} был повторен!
pers_import_mobilePhoneErrors=Ошибка формата номера мобильного телефона!
pers_import_hireDateError=Введите неправильный формат даты!
pers_import_selectPhotoType=Выберите тип импортированной фотографии!
pers_import_hireDateLaterCurrent=Дата найма не может быть позже текущей даты!
pers_import_buildingNotExist=Здание не существует!
pers_import_unitNotExist=Юнит не существует!
pers_import_vdbInfoFail=В здании {0} нет информации об объекте с именем {1}!
pers_import_vdbBuildingFail=Информация о здании объекта {0} не может быть пустой!
pers_import_vdbRoomNoFail=Номер комнаты должен быть числом больше 0!
#人员离职
pers_person_leave=Отстраненные
pers_dimission_date=Дата увольнения
pers_dimission_type=Тип увольнения
pers_dimission_reason=Причина увольнения
pers_dimission_volutary=Сокращение
pers_dimission_dismiss=Увольнение
pers_dimission_resignat=Отставка
pers_dimission_shiftJob=Перевод
pers_dimission_leave=Сохранение работы без заработной платы
pers_dimission_recovery=Восстановить
pers_dimission_sureToRecovery=Вы уверены, что хотите выполнить восстановить сотрудника?
pers_dimission_backCard=Карта была возвращена?
pers_dimission_isForbidAction=Отключить права доступа прямо сейчас?
pers_dimission_writeInfomation=Введите информацию об увольнении
pers_dimission_pinRetain=Сохранять ID уволенного сотрудника?
pers_dimission_downloadTemplate=Скачать шаблон импорта увольнения
pers_dimission_import=Импорт упущений
pers_dimission_importTemplate=Шаблон импорта Dimission
pers_dimission_date_noNull=Дата увольнения не может быть пустой
pers_dimission_leaveType_noExist=Тип отказа не существует
pers_dimission_dateFormat=Обязательное поле, формат времени - гггг-мм-дд, например: 2020-07-22
pers_dimission_leaveType=Обязательное поле, например: Сокращение, Увольнение, Отставка, Перевод
pers_dimission_forbidden=Отключить
pers_dimission_leaveType_noNull=Тип отпуска не может быть пустым!
pers_dimission_person_noExist=Увольняемых не существует!
pers_dimission_date_error=Дата увольнения не указана или неправильная форма
#临时人员
pers_tempPerson_audit=Проверка
pers_tempPerson_view=Просмотр
pers_tempPerson_waitReview=Ожидается проверка администратором
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Номер карты
pers_card_state=Состояние карты
pers_card_effect=Действующая
pers_card_disabled=Не действительная
pers_card_past=Просроченная
pers_card_back=Возращенная карта
pers_card_change=Смена карты
pers_card_note=Номер карты уже существует.
pers_card_numTooBig=Исходный номер карты слишком длинный.
pers_issueCard_entity=Выпустить карту
pers_issueCard_operator=Оператор
pers_issueCard_operate=Действие
pers_issueCard_note=Операции выпуска карты работают с данными зарегистрированных сотрудников, но пользователь не зарегистрирован!
pers_issueCard_date=Карта выпущена
pers_issueCard_changeTime=Время изменения
pers_issueCard_cardValidate=Нельзя использовать пробелы.
pers_issueCard_cardEmptyNote=Номер карты не может быть пустым
pers_issueCard_cardHasBeenIssued=Эта карта уже была выпущена!
pers_issueCard_noCardPerson=Не выданная карта
pers_issueCard_waitPerson=Лицо выпустившее карту
pers_issueCard_mc5000=Скачать MC5000
pers_batchIssCard_entity=Групповой выпуск карт
pers_batchIssCard_startPersNo=Начальный ID сотрудника
pers_batchIssCard_endPersNo=Конечный ID сотрудника
pers_batchIssCard_issCardNum=Количество выпущенных карт
pers_batchIssCard_notIssCardNum=Количество сотрудников без карты
pers_batchIssCard_generateList=Сформировать список
pers_batchIssCard_startRead=Начать считывать
pers_batchIssCard_swipCard=Положение считывания карты
pers_batchIssCard_sendCard=Устройство
pers_batchIssCard_dispenCardIss=Считыватель USB
pers_batchIssCard_usbEncoder=Устр. кодирования USB
pers_batchIssCard_note=ID сотрудника поддерживает только введенные значения, и отображает данные сотрудника без выданных карт (до 300)!
pers_batchIssCard_startPinEmpty=Начальный ID сотрудника не может быть пустым!
pers_batchIssCard_endPinEmpty=Конечный ID сотрудника не может быть пустым!
pers_batchIssCard_startPinLargeThanEndPin=Начальный ID сотрудника не может быть больше, чем конечный ID
pers_batchIssCard_numberParagraphNoPerson=Не существует сотрудника без выданной карты внутри всего сегмента ID!
pers_batchIssCard_inputCardNum=Введите номер карты
pers_batchIssCard_cardLetter=Карта не поддерживает буквы!
pers_batchIssCard_cardNoTooLong=Номер карты слишком длинный!
pers_batchIssCard_issueWay=Тип регистрации карты
pers_batchIssCard_noPersonList=Список сотрудников еще не сформирован.
pers_batchIssCard_startReadCard=Начать считывать карту
pers_batchIssCard_swipePosition=Позиция считывания
pers_batchIssCard_chooseSwipePosition=Пожалуйста, выберите положение считывания.
pers_batchIssCard_readCardTip=Устройство считывает незарегистрированную карту только, если выбранный тип регистрации - выбранный считыватель.
pers_batchIssCard_notIssCardNo=Количество не выпущенных карт
pers_batchIssCard_totalNumOfCards=Общее количество карт
pers_batchIssCard_acms=Карточка ACMS
pers_lossCard_entity=Потерянные карты
pers_lossCard_lost=Эта карта была зарегистрирована потерянной, нельзя повторить операцию!
pers_losscard_note2=После того, как вы записали карту управления, вы должны поднести ее к считывателю лифта, чтобы убедиться в том, что другие карты больше не активны в устройстве.
pers_revertCard_entity=Восстановить потерянную карту
pers_revertCard_setReport=Пожалуйста, сначала отметьте карту потерянной!
pers_revertcard_note2=После того, как вы записали карту управления, вы должны поднести ее к считывателю лифта, чтобы убедиться в том, что другие карты снова активны в устройстве.
pers_issueCard_success=Карта выпущена успешно!
pers_issueCard_error=Ошибка выпуска карты!
pers_cardData_error=Отказ считанного формата данных карты!
pers_analysis_error=Отказ анализа данных карты!
pers_cardOperation_error=Отказ при работе с картой!
pers_cardPacket_error=Отказ пакета команд при работе с картой!
pers_card_write=Запись карты
pers_card_init=Инициализация карты
pers_card_loss=Утерянная карта
pers_card_revert=Восстановить карту
pers_card_writeMgr=Записать карту управления
pers_initCard_tip=После инициализации все данные с карты удалятся!
pers_initCard_prepare=Готов к инициализации карты ...
pers_initCard_process=Инициализация карты...
pers_initCard_success=Успешная инициализация карты...
pers_mgrCard_prepare=Подготовка к записи данных карты управления ...
pers_mgrCard_process=Запись карты управления...
pers_mgrCard_success=Карта управления успешно записана
pers_userCard_prepare=Подготовка к записи карты пользователя...
pers_userCard_process=Запись карты пользователя...
pers_userCard_success=Карта пользователя успешно записана
pers_userCard_tip=Пожалуйста, установите время начала и время окончания на странице редактирования сотрудника, а затем запишите карту.
pers_userCard_tip2=Данные по разрешениям пустые, нельзя записать карты.
pers_userCard_tip3=Полномочия группы более двух связанных устройств, данные потеряны.
pers_writeCard_tip=Убедитесь, что вы подключили устр. кодирования, установили драйвер и поместили в него карту.
pers_writeMgrCard_tip=Количество потерянных и восстановленных карт не может быть больше 18
pers_writeMgrCard_tip2=Номер потерянной карты и восстановленной карты:
pers_card_writeToMgr=Записано на карту управления
pers_card_hex=Формат отображения карты
pers_card_decimal=Десятичный
pers_card_Hexadecimal=Шестнадцатеричный
pers_card_IssuedCommandFail=Сбой команды выпуска:
pers_card_multiCard=Больше карт
pers_card_deputyCard=Дополнительная карта
pers_card_deputyCardValid=Пожалуйста, введите сначала основную карту!
pers_card_writePinFormat=Привязать к карте можно только ID сотрудника без букв!
pers_card_notMoreThanSixteen=Количество дополнительных карт не может быть больше 16.
pers_card_notDelAll=Невозможно удалить все дополнительные карты.
pers_card_maxCard=Номер карты не может превышать {0}.
pers_card_posUseCardNo=Основная карта сотрудника используется модулем ПО - Потребление. Пожалуйста, перейдите в модуль потребление, чтобы выполнить операцию вывода денег с карты!
pers_card_delFirst=Пожалуйста, удалите номер карты перед отправкой!
pers_card_disablePersonWarn=Номер выбранной карты содержит инвалида и не может работать!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Код объекта
pers_wiegandFmt_wiegandMode=Режим
pers_wiegandFmt_wiegandModeOne=Режим 1
pers_wiegandFmt_wiegandModeTwo=Режим 2
pers_wiegandFmt_isDefaultFmt=Авто
pers_wgFmt_entity=Формат Wiegand
pers_wgFmt_in=Формат ввода Wiegand
pers_wgFmt_out=Формат вывода Wiegand
pers_wgFmt_inType=Тип ввода Wiegand
pers_wgFmt_outType=Тип вывода Wiegand
pers_wgFmt_wg=Формат Wiegand
pers_wgFmt_totalBit=Всего бит
pers_wgFmt_oddPch=Контр. битов нечетности(o)
pers_wgFmt_evenPck=Контр. битов четности(e)
pers_wgFmt_CID=ID владельца(c)
pers_wgFmt_facilityCode=Код помещения(f)
pers_wgFmt_siteCode=Код объекта(s)
pers_wgFmt_manufactoryCode=Код производителя(m)
pers_wgFmt_firstParity=Первый контр. бит(p)
pers_wgFmt_secondParity=Второй контр. бит(p)
pers_wgFmt_cardFmt=Формат проверки карты
pers_wgFmt_parityFmt=Формат проверки четности
pers_wgFmt_startBit=Стартовый бит
pers_wgFmt_test=Тест формата карты
pers_wgFmt_error=Ошибка формата карты!
pers_wgFmt_verify1=Всего бит не может быть больше 80!
pers_wgFmt_verify2=Убедитесь, что длина формата карты равна общему количеству битов!
pers_wgFmt_verify3=Длина формата четности должна быть равна общему количеству цифр!
pers_wgFmt_verify4=Инициализированные данные не могут быть удалены!
pers_wgFmt_verify5=Формат карты используется, нельзя удалить!
pers_wgFmt_verify6=Первый бит четности не может быть больше общего числа!
pers_wgFmt_verify7=Второй бит четности не может быть больше общего числа!
pers_wgFmt_verify8=Неверный формат начала и максимальной длины!
pers_wgFmt_verify9=Длина формата проверки карты не может быть больше общего числа!
pers_wgFmt_verify10=Функция проверки контрольной цифры не может пересекаться!
pers_wgFmt_verify11=Код объекта превышает установленный диапазон!
pers_wgFmt_verify=Проверять
pers_wgFmt_unverify=Не проверять
pers_wgFmt_atLeastDefaultFmt=Пожалуйста, сохраните хотя бы один автоматический формат определения карты!
pers_wgFmt_defaultFmtError1=Есть некоторые другие форматы карт с такими же битами номеров карт, которые нельзя установить на автоматическое определение формат карты!
pers_wgFmt_defaultFmtError2=При наличии такого же автоматического определения формата карты, операция неудачна!
pers_wgFmt_cardFormats=Форматы карт
pers_wgFmt_cardFormatTesting=Тест форматов карт
pers_wgFmt_checkIsUsed=Формат карты используется в {0} и не может быть удален!
pers_wgFmt_supportDigitsNumber=Введите количество цифр, поддерживаемых устройством
#选人控件
pers_widget_selectPerson=Выберите сотрудника
pers_widget_searchType1=Запрос
pers_widget_searchType2=Отдел
pers_widget_deptHint=Примечание: Импорт всех сотрудников выбранных отделов
pers_widget_noPerson=Не выбрано ни одного сотрудника.
pers_widget_noDept=Пожалуйста, укажите отдел.
pers_widget_noDeptPerson=Нет сотрудников в этом отделе, пожалуйста, выберите другой!
#人员属性
pers_person_carPlate=Гос. номер
pers_person_socialSecurity=Номер соц. страхования
pers_person_msg4=Максимальная длина не должна превышать 20!
pers_person_msg5=Имя не может содержать более 50 символов!
pers_person_type=Тип сотрудника
pers_person_reseCode=Пароль для персонального доступа
pers_person_IsSendMail=Уведомление о событии
pers_person_inactive=Неактивен
pers_person_active=Активен
pers_person_employee=Работник
pers_person_isSendMailMsg=Чтобы использовать функцию [Уведомление о событии], сначала введите адрес электронной почты.
pers_person_createTime=Время создания
pers_person_pinFirstValid=Первый символ индивидуального номера не может начинаться с 8 или 9.
pers_person_attrValueValid=Значение в поле не может быть повторено.
pers_person_attrValueDelimiterValid=Разделитель должен быть посередине.
pers_person_isSendSMS=SMS-уведомление
pers_person_building=Название здания
pers_person_unitName=Имя отряда
pers_person_roomNo=Номер комнаты
#动态属性
pers_attr_emp_type=Тип работника
pers_attr_street=Улица
pers_attr_nation=Нация
pers_attr_office_address=Рабочий адрес
pers_attr_postcode=Индекс
pers_attr_office_phone=Рабочий телефон
pers_attr_home_phone=Личный телефон
pers_attr_job_title=Должность
pers_attr_birthplace=Место рождения
pers_attr_polit_status=Политический статус
pers_attr_country=Страна
pers_attr_home_address=Домашний адрес
pers_attr_hire_type=Тип занятости
pers_attr_inContract=С договором
pers_attr_outContract=Без договора
#属性自定义
pers_attribute_attrName=Показываемое имя
pers_attribute_attrValue=Значение
pers_attribute_controlType=Тип ввода
pers_attribute_positionX=Строка
pers_attribute_positionY=Столбец
pers_attribute_showTable=Показывать во вкладке детали
pers_attrDefini_deletemsg=Это свойство было использовано. Вы уверены, что хотите это удалить?
pers_attrDefini_reserved=Имя системного зарезервированного поля.
pers_attrDefini_msg1=Максимальная длина не более 30!
pers_attrDefini_msg2=Строки столбца уже существуют, пожалуйста, перейдите в другое позицию!
pers_attrDefini_attrValue_split=Использовать \' ; \' как разделитесь.
pers_attrDefini_attrName=Имя атрибута
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=ID атрибута
pers_attrDefini_select=Выпадающий список
pers_attrDefini_check=Несколько вариантов
pers_attrDefini_radio=Один вариант
pers_attrDefini_text=Текст
pers_attrDefini_maxCol=Не более, чем 2 столбца.
pers_attrDefini_maxLimit=Дополнительные атрибуты достигли максимального количества!
pers_attrDefini_modControlType=Изменение типа ввода удалит данные текущего поля для всех сотрудников в системе. Продолжить?
#leavePerson
pers_leavePerson_reinstated=Восстановить
#opExample
pers_example_newRecode=Получить новые записи
pers_example_allRecode=Доступ ко всем записям
pers_custField_StatisticalType=Тип статистики
#人员参数修改
pers_param_isAudit=Авто-одобрение временных сотрудников
pers_param_donotChangePin=Существующий ID сотрудника содержит букву(ы), нельзя отключить функцию [Поддержка букв].
pers_param_hexChangeWarn=В системе уже есть номера карт, нельзя изменить режим отображения формата карты.
pers_param_cardsChangeWarn=Некоторые пользователи, имеют более одной карты в системе, нельзя отключить функцию [Несколько карт на сотрудника].
pers_param_maxPinLength=Длина нового ID сотрудника не может быть меньше длины уже существующего ID сотрудника в системе.
pers_param_pinBeyondDevLength=Максимальная длина ID сотрудника, поддерживаемого устройством в системе {0}, введите целое число менее {1}.
pers_param_cardBeyondDevLength=Максимальная длина номера карты, поддерживаемого устройством в системе {0}, введите целое число менее чем {1}.
pers_param_checkIsExistNoAudit=В системе есть лица, не прошедшие одобрение их регистрации, нельзя изменить на авто-одобрение!
pers_param_noSupportPinLetter=В системе есть устройства, которые не поддерживают ID сотрудников содержащие буквы, и нельзя включить функцию [Поддержка букв].
pers_param_changePinLettersTip=Разрешено автоматическое назначение номера сотрудникам, нельзя изменить режимы выдачи номера
pers_param_changePinIncrementTip=Номера сотрудников включает в себя буквы, нельзя изменить режим номера сотрудника
pers_param_qrCode=Корпоративный QR-код
pers_param_employeeRegistrar=Включить регистрацию сотрудников через Облако
pers_param_downloadQRCodePic=Скачать изображение QR-кода
pers_param_qrCodeUrl=QR Code URL
pers_param_qrCodeUrlCreate=Самостоятельная регистрация
pers_param_qrCodeUrlHref=Адрес сервера: порт
pers_param_pinSetWarn=В текущей системе уже есть персонал, и режим табельного номера изменить нельзя.
pers_param_selfRegistration=Включить самостоятельную регистрацию
pers_param_infoProtection=Защита личной конфиденциальной информации
pers_param_infoProtectionWarnMsg=После включения опции защиты личной конфиденциальной информации конфиденциальные личные данные, задействованные в этом модуле, будут десенсибилизированы или скрыты, включая, помимо прочего, имена, номера карт, идентификационные номера, фотографии и т. д.
pers_param_templateServer=Сервер извлечения шаблонов
pers_param_enableFacialTemplate=Включить извлечение шаблонов
pers_param_templateServerAddr=Шаблон для извлечения адреса сервера
pers_param_templateServerWarnInfo=При включении извлечения шаблона лица, когда сервер извлечения шаблона лица онлайн и пользователь проверяет его прохождение, люди извлекают шаблон лица по умолчанию при сравнении фотографий; Когда сервер извлечения шаблона лица находится в автономном режиме, не извлекайте шаблон лица!
pers_param_templateServerWarnInfo1=При включении извлечения шаблона лица необходимо подключить устройство, поддерживающее извлечение шаблона лица!
pers_param_templateServerOffline=Сервер извлечения шаблонов лица отключен, шаблоны лица не могут быть извлечены! Продолжится ли?
pers_param_faceServer=Фоновое сравнение лиц
pers_param_enableFaceVerify=Включить фоновое сравнение лиц
pers_param_faceServerAddr=Фоновый адрес для сравнения лиц
pers_param_faceServerSecret=Фоновый ключ для сравнения лиц
#国籍
pers_person_nationality=Национальность
pers_nationality_angola=Анголезец
pers_nationality_afghanistan=Афганец
pers_nationality_albania=Албанец
pers_nationality_algeria=Алжирец
pers_nationality_america=Американец
pers_nationality_andorra=Андорранец
pers_nationality_anguilla=Ангильянец
pers_nationality_antAndBar=Антигуа и Барбуды
pers_nationality_argentina=Аргетинец
pers_nationality_armenia=Армянин
pers_nationality_ascension=Остров Вознесения
pers_nationality_australia=Австралиец
pers_nationality_austria=Австриец
pers_nationality_azerbaijan=Азербайджанец
pers_nationality_bahamas=Багамец
pers_nationality_bahrain=Бахрейнец
pers_nationality_bangladesh=Бангладешец
pers_nationality_barbados=Барбадосец
pers_nationality_belarus=Белорус
pers_nationality_belgium=Бельгиец
pers_nationality_belize=Белизец
pers_nationality_benin=Бенинец
pers_nationality_bermudaIs=Бермудец
pers_nationality_bolivia=Боливиец
pers_nationality_botswana=Ботсванец
pers_nationality_brazil=Бразилец
pers_nationality_brunei=Брунеец
pers_nationality_bulgaria=Болгарин
pers_nationality_burkinaFaso=Буркина Фасо
pers_nationality_burma=Мьянмиец
pers_nationality_burundi=Бурундиец
pers_nationality_cameroon=Камерунец
pers_nationality_canada=Канадец
pers_nationality_caymanIs=Кайманские острова
pers_nationality_cenAfrRepub=Центр. Африканская республика
pers_nationality_chad=Чадиец
pers_nationality_chile=Чилиец
pers_nationality_china=Китаец
pers_nationality_colombia=Колумбиец
pers_nationality_congo=Республика Конго
pers_nationality_cookIs=Острова Кука
pers_nationality_costaRica=Костариканец
pers_nationality_cuba=Кубинец
pers_nationality_cyprus=Киприот
pers_nationality_czechRep=Чех
pers_nationality_denmark=Датчанин
pers_nationality_djibouti=Джибутиец
pers_nationality_dominicaRep=Доминиканец
pers_nationality_ecuador=Эквадорец
pers_nationality_egypt=Египтянин
pers_nationality_eISalvador=Сальвадорец
pers_nationality_england=Британец
pers_nationality_estonia=Эстонец
pers_nationality_ethiopia=Эфиоп
pers_nationality_fiji=Фиджиец
pers_nationality_finland=Финн
pers_nationality_france=Француз
pers_nationality_freGui=Гвинеец
pers_nationality_gabon=Габонец
pers_nationality_gambia=Гамбиец
pers_nationality_georgia=Грузин
pers_nationality_germany=Немец
pers_nationality_ghana=Ганиец
pers_nationality_gibraltarm=Гибралтарец
pers_nationality_greece=Грек
pers_nationality_grenada=Гренадец
pers_nationality_guam=Гуамец
pers_nationality_guatemala=Гватемалец
pers_nationality_guinea=Гвинеец
pers_nationality_guyana=Гайанец
pers_nationality_haiti=Гаитянин
pers_nationality_honduras=Гондурасец
pers_nationality_hungary=Венгр
pers_nationality_iceland=Исландец
pers_nationality_india=Индиец
pers_nationality_indonesia=Индонезиец
pers_nationality_iran=Иранец
pers_nationality_iraq=Иракец
pers_nationality_ireland=Ирландец
pers_nationality_israel=Израильтянин
pers_nationality_italy=Итальянец
pers_nationality_ivoryCoast=Ивуариец
pers_nationality_jamaica=Ямаец
pers_nationality_japan=Японец
pers_nationality_jordan=Иорданец
pers_nationality_kenya=Кениец
pers_nationality_korea=Кореец
pers_nationality_kuwait=Кувейтец
pers_nationality_kyrgyzstan=Киргиз
pers_nationality_laos=Лаос
pers_nationality_latvia=Латвиец
pers_nationality_lebanon=Ливанец
pers_nationality_lesotho=Лесотец
pers_nationality_liberia=Либериец
pers_nationality_libya=Ливиец
pers_nationality_liechtenstein=Лихтенштейнец
pers_nationality_lithuania=Литовец
pers_nationality_luxembourg=Люксембургец
pers_nationality_madagascar=Малагасиец
pers_nationality_malawi=Малавиец
pers_nationality_malaysia=Малазиец
pers_nationality_maldives=Мальдивец
pers_nationality_mali=Малиец
pers_nationality_malta=Мальтиец
pers_nationality_marianaIs=Марианские острова
pers_nationality_martinique=Мартиникиец
pers_nationality_mauritius=Мавриканец
pers_nationality_mexico=Мексиканец
pers_nationality_moldova=Молдованин
pers_nationality_monaco=Монегаск
pers_nationality_montseIs=Монтсеррат
pers_nationality_morocco=Марокканец
pers_nationality_mozambique=Мозамбиканец
pers_nationality_namibia=Намибиец
pers_nationality_nauru=Науриец
pers_nationality_nepal=Непалец
pers_nationality_netAnti=Нидерландские Антильские о.
pers_nationality_netherlands=Голландец
pers_nationality_newZealand=Новозеландец
pers_nationality_nicaragua=Никарагуанец
pers_nationality_niger=Нигериец
pers_nationality_nigeria=Нигерия
pers_nationality_norKorea=Северо кореец
pers_nationality_norway=Норвежец
pers_nationality_oman=Оманец
pers_nationality_pakistan=Пакистанец
pers_nationality_panama=Панамец
pers_nationality_papNewCui=Папуа - Новая Гвинеец
pers_nationality_paraguay=Парагваец
pers_nationality_peru=Перуанец
pers_nationality_philippines=Филиппинец
pers_nationality_poland=Поляк
pers_nationality_frenPolyne=Полинезиец
pers_nationality_portugal=Португалец
pers_nationality_puerRico=Пуэрториканец
pers_nationality_qatar=Катарец
pers_nationality_reunion=Реюньон
pers_nationality_romania=Румын
pers_nationality_russia=Русский
pers_nationality_saiLueia=Сент-Люсия
pers_nationality_saintVinc=Сент-Винсент
pers_nationality_samoa_eastern=Американское Самоа
pers_nationality_samoa_western=Самоанец
pers_nationality_sanMarino=Санмариинец
pers_nationality_saoAndPrinc=Сан-Томе и Принсипи
pers_nationality_sauArabia=Саудоаравиец
pers_nationality_senegal=Сенегалец
pers_nationality_seychelles=Сейшельские Острова
pers_nationality_sieLeone=Сьера-леонец
pers_nationality_singapore=Сингапурец
pers_nationality_slovakia=Словак
pers_nationality_slovenia=Словенец
pers_nationality_solomonIs=Соломоновы острова
pers_nationality_somali=Сомалиец
pers_nationality_souAfrica=Южно африканец
pers_nationality_spain=Испанец
pers_nationality_sriLanka=Шриланкиец
pers_nationality_sudan=Суданец
pers_nationality_suriname=Суринамец
pers_nationality_swaziland=Свазилендец
pers_nationality_sweden=Швед
pers_nationality_switzerland=Швейцарец
pers_nationality_syria=Сириец
pers_nationality_tajikstan=Таджик
pers_nationality_tanzania=Танзаниец
pers_nationality_thailand=Таец
pers_nationality_togo=Тогиец
pers_nationality_tonga=Тонгиец
pers_nationality_triAndToba=Тринидад и Тобаго
pers_nationality_tunisia=Тунисец
pers_nationality_turkey=Турок
pers_nationality_turkmenistan=Туркмен
pers_nationality_uganda=Угандец
pers_nationality_ukraine=Украинец
pers_nationality_uniArabEmira=ОАЭ
pers_nationality_uruguay=Уругваец
pers_nationality_uzbekistan=Узбек
pers_nationality_venezuela=Венесуэлец
pers_nationality_vietnam=Вьетнамец
pers_nationality_yemen=Йеменец
pers_nationality_serbia=Серб
pers_nationality_zimbabwe=Зимбабвиец
pers_nationality_zambia=Замбиец
pers_nationality_aruba=Арубиец
pers_nationality_bhutan=Бутанец
pers_nationality_bosnia_herzegovina=Босния и Герцеговина
pers_nationality_cambodia=Камбоджиец
pers_nationality_congoD=Конгиец
pers_nationality_comoros=Союз Коморских Островов
pers_nationality_capeVerde=Республика Кабо-Верде
pers_nationality_croatia=Хорват
pers_nationality_dominica=Доминиканец
pers_nationality_eritrea=Эритреец
pers_nationality_micronesia=Микронезиец
pers_nationalit_guineaBissau=Гвинея-Бисау
pers_nationalit_equatorialGuinea=Гвинеец
pers_nationalit_hongkong=Гонконгец
pers_nationalit_virginIslands=Виргинские Острова США
pers_nationalit_britishVirginIslands=Виргинские Острова Британии
pers_nationalit_kiribati=Кирибати
pers_nationalit_mongolia=Монгол
pers_nationalit_marshall=Маршалловы Острова
pers_nationalit_macedonia=Македонец
pers_nationalit_montenegro=Черногорец
pers_nationalit_mauritania=Мавританец
pers_nationalit_palestine=Палестинец
pers_nationalit_palau=Палау
pers_nationalit_rwanda=Руанда
pers_nationalit_saintKittsNevis=Сент-Китс и Невис
pers_nationalit_timorLeste=Восточный Тимор
pers_nationalit_taiwan=Тайванец
pers_nationalit_tuvalu=Тувалу
pers_nationalit_vanuatu=Вануату
#制卡
pers_person_cardprint=Печать карты
pers_cardTemplate_tempSelect=Шаблон карты
pers_cardTemplate_printerSelect=Принтер
pers_cardTemplate_front=Спереди
pers_cardTemplate_opposite=Сзади
pers_cardTemplate_entryDate=Дата устройства на работу
pers_cardTemplate_photo=Фотография
pers_cardTemplate_uploadFail=Ошибка загрузки изображения!
pers_cardTemplate_jpgFormat=Поддерживается загрузка только в формате JPG!
pers_cardTemplate_printStatus=Статус печати
pers_cardTemplate_waiting=Ожидайте
pers_cardTemplate_printing=Печатаю
pers_cardTemplate_printOption=Параметры печати
pers_cardTemplate_duplexPrint=Двойная печать
pers_cardTemplate_frontOnly=Печать только спереди
#app
pers_app_delPers=На сервере нет сотрудников, которых вы хотите удалить
pers_app_deptIsNull=Не могу найти номер отдела или название соответствующее отделу
pers_app_personNull=Не могу найти сотрудников
pers_app_pinExist=ID сотрудника уже существует
pers_app_dateError=Неверный формат даты, используйте правильный формат: 2016-08-08
#api
pers_api_selectPhotoInvalid=Неверное фото, пожалуйста загрузите снова
pers_api_dateError=Неверный формат даты
pers_api_personNotExist=Сотрудник не существует
pers_api_cardsPersSupport=В системе не разрешен выпуск несколько карт на сотрудника, выдача дополнительной карты недействительна!
pers_api_department_codeOrNameNotNull=Номер отдела или название отдела не может быть пустым
pers_api_deptSortNoIsNull=Сортировка по отделам не может быть пустой!
pers_api_deptSortNoError=Значение сортировки по отделам должно быть между 1-999999!
pers_api_dataLimit=Текущее число операций {0} превышает лимит {1}. Пожалуйста, действуйте по частям!
pers_api_cardTypeError=Ошибка типа карты
#人员生物模板API
pers_api_fingerprintExisted=От. пальца сотрудника уже существует
pers_api_validtypeIncorrect=Это значение атрибута допустимого типа неверно
pers_api_dataNotExist=Шаблон № не существует
pers_api_templateNoRang=Пожалуйста, введите правильное значение № шаблона в диапазоне 0-9
pers_api_templateIsNull=Шаблоны не могут быть пустыми!
pers_api_versionIsNumber=Версия может вводить только цифры!
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=ID может быть только числом!
#biotime
pers_h5_personAvatarNotNull=Аватар пуст
pers_h5_personMobileRepeat=Мобильный номер уже существует
pers_h5_personEmailRepeat=Почтовый ящик уже существует
pers_h5_pwdIsRepetition=Новый и старый пароль повторяются
pers_h5_personIdNull=ID сотрудника пуст
pers_h5_pinOrEmailIsNull=Пожалуйста, заполните номер и адрес электронной почты
pers_emailTitle_resetPassword=Изменить пароль
pers_emailContent_resetPassword=Ссылка действительна в течение 24 часов, скопируйте ссылку в браузер, чтобы сменить пароль:
pers_h5_tokenIsNull=Ваучер пуст
pers_h5_tokenError=Ошибка ваучера
pers_h5_oldPwdIsError=Старый пароль заполнен неверно
pers_api_resetPasswordSuccess=Пароль был обновлен
pers_api_resetPasswordFail=Смена пароля не удалась
pers_api_forgetPassword=Забыл пароль
pers_api_confirmSubmit=Подтвердить подачу
pers_api_confirmPwdCaution=Нажмите [OK], чтобы подтвердить новый пароль.
pers_api_pwdRule=Пароль должен содержать хотя бы одну букву или цифру и быть длиной не менее 8-12 символов
pers_h5_personPinFormatNumber=ID может состоять только из цифр или букв
pers_h5_persEmailNoExist=Ошибка заполнения почтового ящика
pers_h5_pageNull=Ошибка параметра подкачки
pers_h5_personPinNotStartWithZero=ID сотрудника не может начинаться с 0
pers_h5_personPinTooLong=ID сотрудника слишком длинный
pers_h5_personPinInValid=Этот ID уже используется
pers_h5_imgSizeError=Пожалуйста, загрузите изображение размером не более 10Мб!
pers_h5_confirmAndContinue=Подтвердить и продолжить
#人脸抠图不及格错误
pers_face_poorResolution=Разрешение изображения ниже 80000 пикселей
pers_face_noFace=Лицо не обнаружено
pers_face_manyFace=Обнаружено несколько лиц
pers_face_smallFace=Размер лица слишком мал
pers_face_notColor=Изображение не цветное
pers_face_seriousBlur=Изображение размыто
pers_face_intensivelyLight=Изображение слишком засвечено
pers_face_badIllumination=Изображение слишком темное
pers_face_highNoise=Изображение с высоким уровнем шума
pers_face_highStretch=Лицо растянуто
pers_face_covered=Лицо закрыто
pers_face_smileOpenMouth=С открытым ртом
pers_face_largeAngle=Большой угол наклона лица
pers_face_criticalIllumination=Высокая яркость изображения
pers_face_criticalLargeAngle=Большой угол отклонения лица
pers_face_validFailMsg=Обнаружение лица не удалось из-за:
pers_face_failType=Тип отказа отсутствует лицо
pers_face_photoFormatError=Неверный формат фотографии. Загрузите файл в формате JPG / PNG.
pers_face_notUpdateMsg=Не удалось сгенерировать изображение лица, не обновляйте изображение лица.
#健康申报
pers_health_enable=Включить заявление о состоянии здоровья
pers_health_attrExposure=Контакты со случаями возможного заражения
pers_health_attrSymptom=Симптомы за последние 14 дней
pers_health_attrVisitCity=Города посещенные за последние 14 дней
pers_health_attrRemarks=Доп. информация о здоровье
pers_health_symptomCough=Кашель
pers_health_symptomFever=Жар
pers_health_symptomPolypena=Насморк
pers_health_declaration=Заявление о здоровье
pers_health_aggrement=Я соглашаюсь с тем, что пользователи системы которые внесли заведомо ложные данные о состоянии своего здоровья, несут ответственность в соответствии с законом РФ.
pers_health_visitCity_notEmpty=Посещенный город не может быть пустым!
pers_health_notAgree=Необходимо принять соглашение, прежде чем продолжить!
#人员名单库
pers_personnal_list_manager=менеджер списков
pers_personnal_list=список библиотеки
pers_personnal_list_scheme=Режим домена
pers_personnal_list_name=имя личного списка
pers_personnal_list_group_str_id=идентификатор группы списка
pers_personnal_list_personCount=Количество человек
pers_personnal_list_tag=Определяется пользователем
pers_personnal_list_type=Тип личного списка
pers_personnallist_addPerson_repo=Добавить человека в репо
pers_personnallist_sendPersonnallist=Библиотека распределенных списков
pers_personnallist_sendPerson=Эмитент
pers_personnallist_notDel_existPerson=Библиотека списка содержит людей и не может быть удалена
pers_personnallist_peopleInRoster=В библиотеке списков все еще есть люди
pers_personnallist_associationNotExist=Связь между основным устройством и библиотекой списка не существует
pers_personnal_list_person=человек из личного списка
pers_personnal_list_dev=Список разрешений библиотеки
pers_personnal_list_addDev=Добавить устройство
pers_personnal_list_name_isExist=Имя уже существует
pers_personnal_bannedList=библиотека запрещенных списков
pers_personnal_allowList=Библиотека разрешенных списков
pers_personnal_redList=Библиотека Красного списка
pers_personnal_attGroup=Группа посещаемости
pers_personnal_passList=список паролей
pers_personnal_banList=Запрещенный список
pers_personnal_visPassList=Список пропусков для посетителей
pers_personnal_visBanList=Список запрещенных посетителей
pers_personnal_databaseHasBeenDistributed=Выбранная библиотека списков выпущена и не может быть удалена
pers_personnel_sendError_dueTo=Сбой доставки Причина отказа:
pers_personnel_sendError_reson={0} находится в заблокированном списке, удалите и добавьте
#比对照片-样片示例
pers_examplePic_Tip=Должен соответствовать следующим требованиям:
pers_examplePic_Tip1=1. цвет фона чисто белый, персонал носит тёмную одежду;
pers_examplePic_Tip2=2. Электронные фотографии в формате JPG, PNG, JPEG, рекомендуемый диапазон пикселей: 480*640 < пикселей < 1080*1920;
pers_examplePic_Tip3=3. Портреты на электронных фотографиях должны открывать глаза и смотреть прямо перед собой, а зрачки должны быть хорошо видны;
pers_examplePic_Tip4=4. У портрета на электронной фотографии должно быть нейтральное выражение лица, можно улыбаться, но не показывать зубы;
pers_examplePic_Tip5=5. Портрет на электронной фотографии должен быть четким, с естественными цветами, насыщенными слоями и без явных искажений. Никаких теней, бликов или отражений на портретных лицах или фонах; контрастность и яркость соответствуют требованиям.
pers_examplePic_description=Правильный пример
pers_examplePic_error=Пример ошибки:
pers_examplePic_error1=Преувеличенное выражение (чрезмерная улыбка)
pers_examplePic_error2=Свет слишком темный
pers_examplePic_error3=Лицо слишком маленькое (разрешение слишком маленькое)
pers_applogin_enabled=Включить вход в приложение
pers_applogin_disable=Отключить вход в приложение
pers_applogin_status=Приложение Состояние входа в систему