#pers common.
#here other module can also use the label from pers.
pers_module=Personal
pers_common_addPerson=Personal hinzufügen
pers_common_delPerson=Personal löschen
pers_common_personCount=Personal Anzahl
pers_common_browsePerson=Personal durchsuchen
#左侧菜单
pers_person_manager=Personal
pers_person=Person
pers_department=Abteilung
pers_leave=Entlassenes Personal
pers_tempPerson=Aushilfspersonal
pers_attribute=Benutzerdefinierte Attribute
pers_card_manager=Kartenverwaltung
pers_card=Karte
pers_card_issue=Erstellte Kartenaufzeichnung
pers_wiegandFmt=Wiegand-Format
pers_position=Position
#人员
pers_person_female=Weiblich
pers_person_male=Männlich
pers_person_pin=Personal ID
pers_person_departmentChange=Abteilung anpassen
pers_personDepartment_changeLevel=Berechtigung zum Abteilungswechsel
pers_person_gender=Geschlecht
pers_person_detailInfo=Personal Detail
pers_person_accSet=Zugangskontrolle
pers_person_accSetting=Zugangskontroll-Einstellung
pers_person_attSet=Zeiterfassung
pers_person_eleSet=Aufzugskontrolle
pers_person_eleSetting=Aufzugskontroll-Einstellung
pers_person_parkSet=Kennzeichenerfassung
pers_person_pidSet=Personalzertifikat
pers_person_insSet=FaceKiosk
pers_person_aiSet=Face Intellect
pers_person_payrollSet=Gehalt-Konfiguration
pers_person_psgSet=Durchgangskonfiguration
pers_person_lockerSet=Schließfacheinstellung
pers_person_sisSet=Sicherheitsüberprüfungseinstellungen
pers_person_vdbSet=Einstellungen der visuellen Gegensprechanlage
pers_person_firstName=Vorname
pers_person_lastName=Nachname
pers_person_name=Vorname
pers_person_wholeName=Name
pers_person_fullName=Vor Zweit Nach
pers_person_cardNum=Anzahl eigener Karten
pers_person_deptNum=Anzahl involvierter Abteilungen
pers_person_dataCount=Statistiken
pers_person_regFinger=Fingerabdruck
pers_person_reg=Erfassung
pers_person_password=Geräteprüfpasswort
pers_person_personDate=Beschäftigungsdatum
pers_person_birthday=Geburtstag
pers_person_mobilePhone=Mobiltelefonnummer
pers_person_personDevAuth=Personen- und Geräteberechtigungen
pers_person_email=Email
pers_person_browse=Durchsuchen
pers_person_authGroup=Zugangsebenen
pers_person_setEffective=Zeitraum festlegen
pers_person_attArea=Anwesenheitsgebiet
pers_person_isAtt=Anwesenheitsberechnung
pers_person_officialStaff=Amtlicher Mitarbeiter
pers_person_probationStaff=Mitarbeiter in Probezeit
pers_person_identity_category=Personaltyp
pers_person_devOpAuth=Funktion Gerätebedienung
pers_person_msg1=Die Kartennummer und der Adressschlüssel sollten gleichzeitig eingetragen werden!
pers_person_msg2=Bitte eine 3-4 Ziffer eingeben.
pers_person_msg3=Format-Fehler
pers_person_imgPixel=(Optimale Größe 120*140).
pers_person_cardLengthDigit=Bitte Zahl eingeben.
pers_person_cardLengthHexadecimal=Bitte geben Sie Zahlen oder abcdef Buchstaben ein!
pers_person_to=Bis
pers_person_templateCount=Anzahl der Fingerabdrücke
pers_person_biotemplateCount=Anzahl
pers_person_regFace=Gesicht
pers_person_regVein=Fingervene
pers_person_faceTemplateCount=Anzahl der Gesichter
pers_person_VeinTemplateCount=Anzahl der Fingervenen
pers_person_palmTemplateCount=Anzahl der Handflächen
pers_person_cropFace=Gesichtsbild
pers_person_cropFaceCount=Anzahl der Gesichtsbilder
pers_person_faceBiodataCount=Visible Face Menge
pers_person_irisCount=Anzahl der Iris
pers_person_batchToDept=Neue Abteilung
pers_person_changeReason=Übertragungsgrund
pers_person_selectedPerson=Ausgewählte Person
pers_person_duressPwdError=Wiederholtes Passwort
pers_person_completeDelPerson=Löschen
pers_person_recover=Wiederherstellen
pers_person_nameNoComma=Darf kein Komma enthalten.
pers_person_firstNameNotEmpty=Vorname darf nicht frei bleiben
pers_person_lastNameNotEmpty=Nachname darf nicht frei bleiben
pers_person_mobilePhoneValidate=Bitte gültige Mobiltelefonnummer eingeben.
pers_person_phoneNumberValidate=Bitte geben Sie eine gültige Telefonnummer ein.
pers_person_postcodeValidate=Bitte geben Sie eine gültige Postleitzahl ein.
pers_person_idCardValidate=Bitte geben Sie eine gültige Ausweis-Kartennummer ein.
pers_person_pwdOnlyLetterNum=Das Passwort darf nur Buchstaben oder Zahlen enthalten.
pers_person_certNumOnlyLetterNum=Die Zertifikatsnummer darf nur Buchstaben oder Zahlen enthalten.
pers_person_oldDeptEqualsNewDept=Die neu eingestellte Abteilung darf nicht mit der Original-Abteilung identisch sein.
pers_person_disabled=Zugriff deaktiviert
pers_person_emailError=Bitte geben Sie eine gültige E-Mail-Adresse ein.
pers_person_driverPrompt=Bitte Gerätetreiber installieren! Zum Download des Treibers OK klicken.
pers_person_readIDCardFailed=Swipe fehlgeschlagen!
pers_person_cardPrompt=Ausweis-Karte in den Bereich halten...
pers_person_iDCardReadOpenFailed=Kein Ausweis-Lesegerät gefunden!
pers_person_iDCardNotFound=Ausweis-Karte nicht gefunden, bitte erneut versuchen!
pers_person_nameValid=Unterstützt Chinesisch, Englisch, Zahlen, \'-\', \'_\'.
pers_person_nameValidForEN=Unterstützt Englisch, Zahlen, \'-\', \'_\', \'.\'.
pers_person_pinPrompt=Bitte Zeichen aus dem englischen Alphabet eingeben, Zahlen.
pers_person_pinSet=Personal-ID-Konfiguration
pers_person_supportLetter=Unterstützt Buchstaben
pers_person_cardsSupport=Mehrere Karten pro Person
pers_person_SupportDefault=Personal-ID selbst erstellen
pers_person_noSpecialChar=Sonderzeichen nicht erlaubt!
pers_person_pinInteger=Bitte die Ziffern eingeben.
pers_pin_noSpecialChar=Die personalnummer darf kein zeichen enthalten!
pers_op_capture=Erfassung
pers_person_cardDuress=Kartennummer wiederholt
pers_person_pwdException=Passwort-Ausnahme
pers_person_systemCheckTip=Personaldaten auf richtiges Format prüfen!
pers_person_immeHandle=Sofortige Handhabe
pers_person_cardSet=Karteneinstellung
pers_person_tempPersonSet=Konfiguration Aushilfspersonal
pers_person_cardsReadMode=Kartenlesemodus
pers_person_cardsReadModeReadHead=Von Controller gelesen
pers_person_cardsReadModeID180=Von ID180 gelesen
pers_person_IDReadMode=Ausweis-Kartenlesemodus
pers_person_IDReadModeIDCardReader=Ausweis-Kartenleser
pers_person_IDReadModeTcpReadHead=TCP/IP-Lesegerät
pers_person_physicalNo=Ausweiskarte Physische Kartennummer
pers_person_physicalNoToCardNo=Ausweiskarte Physische Kartennummer benutzen
pers_person_ReadIDCard=Ausweis-Karte lesen
pers_person_templateBioTypeNumber=Typnummer der biometrischen Vorlage
pers_person_templateValidType=Gültigkeit Biometrische Vorlage
pers_person_templateBioType=Typ Biometrische Vorlage
pers_person_templateVersion=Version Biometrische Vorlage
pers_person_template=Biometrische Vorlage
pers_person_templateNo=Biometrische Vorlage Nr.
pers_person_templateNoIndex=Biometrische Vorlage Index
pers_person_templateDataUpdate=Wenn Biometrische Vorlage vorhanden, Daten aktualisieren:
pers_person_templateValidTypeNoNull=Gültigkeit Biometrische Vorlage darf nicht frei bleiben!
pers_person_templateBioTypeNoNull=Personal-ID: {0}, Biometrische Vorlage Typ darf nicht frei bleiben!
pers_person_templateVersionNoNull=Personal-ID: {0}, Version Biometrische Vorlage darf nicht frei bleiben!
pers_person_templateNoNull=Personal-ID: {0}, Biometrische Vorlage darf nicht frei bleiben!
pers_person_templateNoNoNull=Personal-ID: {0}, Biometrische Vorlage Nr. darf nicht frei bleiben!
pers_person_templateNoIndexNoNull=Personal-ID: {0}, Biometrische Vorlage Index darf nicht frei bleiben!
pers_person_templateError=Personal-ID: {0}, Biometrische Vorlage nicht korrekt!
pers_person_bioDuress=Zwang
pers_person_universal=Gewöhnlich
pers_person_voice=Stimmabdruck
pers_person_iris=Iris
pers_person_retina=Netzhaut
pers_person_palmPrints=Handfläche
pers_person_metacarpalVein=Handflächenvene
pers_person_visibleFace=Sichtfläche
pers_person_pinError=Personal-ID {0} existiert nicht, Daten können nicht verarbeitet werden!
pers_person_pinException=Personal-ID-Ausnahme
pers_person_pinAutoIncrement=Personal-ID Auto-Inkrement
pers_person_resetSelfPwd=Passwort für die Selbstanmeldung zurücksetzen
pers_person_picMaxSize=Die Bildauflösung ist zu hoch, empfohlene Auflösung unter {0}.
pers_person_picMinSize=Die Bildauflösung ist zu niedrig, empfohlene Auflösung über {0}.
pers_person_cropFaceShow=Gesichtsbild ansehen
pers_person_faceNoFound=Nicht erkanntes Gesicht
pers_person_biometrics=Biometrie-Typ
pers_person_photo=Persönliche Fotos
pers_person_visibleFaceTemplate=Sichtbare Gesichtsvorlage
pers_person_infraredFaceTemplate=Statische Infrarot-Gesichtsvorlage
pers_person_delBioTemplate=Biometrische Daten löschen
pers_person_delBioTemplateSelect=Bitte wählen Sie die zu löschende biologische Vorlage aus!
pers_person_infraredFace=Statisches Infrarotgesicht
pers_person_notCard=Enthält keine Karte
pers_person_notRegFinger=Enthält keinen Fingerabdruck
pers_person_notMetacarpalVein=Enthält keine Handflächenvene
pers_person_notRegVein=Enthält keine Fingervenen
pers_person_notIris=Ohne Iris
pers_person_notInfraredFaceTemplate=Enthält keine Nahinfrarot-Gesichtsvorlage
pers_person_notVisibleFaceTemplate=Enthält keine Gesichtsvorlage für sichtbares Licht
pers_person_notVisibleFacePhoto=Enthält keine sichtbaren Gesichtsfotos
pers_person_visibleFacePhoto=Sichtbares Gesichtsfoto
pers_person_change=Personalanpassungen
pers_person_cropFaceUsePhoto=Möchten Sie das Vergleichsfoto als Avatar anzeigen?
pers_person_photoUseCropFace=Möchten Sie mit dem Avatar ein Vergleichsfoto erstellen?
pers_person_selectCamera=Aufnahmekamera auswählen
pers_person_delCropFaceMsg=Es gibt keine Vergleichsfotos zu löschen!
pers_person_disabledNotOp=Die Person wurde behindert und kann nicht operieren!
pers_person_visiblePalm=Sichtbare helle Handfläche
pers_person_notVisiblePalm=Enthält nicht sichtbares Licht Handfläche
pers_person_selectDisabledNotOp=Es gibt behindertes Personal im ausgewählten Personal, das nicht bedienen kann!
pers_person_photoUseCropFaceAndTempalte=Möchten Sie mithilfe von Avataren Vergleichsfotos und Gesichtsvorlagen erstellen?
pers_person_photoUseTempalte=Möchten Sie Fotos verwenden, um Gesichtsvorlagen zu erstellen?
pers_person_createCropFace=Vergleichsfotos generieren
pers_person_createFaceTempalte=Gesichtsvorlagen generieren
pers_person_faceTempalte=Gesichtsvorlagen
pers_person_extractFaceTemplate=Gesichtsvorlagen extrahieren
pers_person_createSuccess=Erfolgreich generiert
pers_person_createFail=Generierung fehlgeschlagen
pers_person_serverConnectWarn=Serveradresse, Benutzername und Passwort können nicht leer sein!
pers_person_serverOffline=Offline-Gesichtsvorlagenextraktionsserver
pers_person_faceTemplateError1=Gesichtserkennung fehlgeschlagen
pers_person_faceTemplateError2=Gesichtsverschluss
pers_person_faceTemplateError3=Unzureichende Klarheit
pers_person_faceTemplateError4=Gesichtswinkel zu groß
pers_person_faceTemplateError5=Live-Erkennung fehlgeschlagen
pers_person_faceTemplateError6=Extraktion der Gesichtsvorlage fehlgeschlagen
pers_person_cropFaceNoExist=Das Vergleichsfoto existiert nicht
pers_person_disableFaceTemplate=Die Gesichtsvorlagenextraktionsfunktion ist nicht aktiviert, die Vorlage kann nicht extrahiert werden!
pers_person_cropFacePhoto=Gesichtsvergleichsfoto
pers_person_vislightPalmPhoto=Palm Vergleichsfoto
pers_person_serverOfflineWarn=Der Gesichtsvorlagenextraktionsserver ist offline und kann diese Funktion nicht aktivieren!
pers_person_serverConnectInfo=Bitte testen Sie, ob der Gesichtsvorlagenextraktionsserver online ist?
pers_person_notModified=Die Handynummer kann nicht geändert werden.
pers_person_syncAcms=Synchronisieren Sie die Mitarbeiter mit ACMS
pers_person_startUpdate=Das Aktualisieren der Personalinformationen beginnt.
pers_person_updateFailed=Das Aktualisieren der Personalinformationen ist fehlgeschlagen!
#控制器发卡
pers_person_readCard=Karte aus Gerät ausgeben
pers_person_stopRead=Ausgabe stoppen
pers_person_chooseDoor=Tür wählen
pers_person_readCarding=Bitte versuchen Sie es später erneut!
#抓拍照片
pers_capture_catchPhoto=Foto erfassen
pers_capture_preview=Vorschau
#部门
pers_dept_entity=Abteilung
pers_dept_deptNo=Abteilungsnummer
pers_dept_deptName=Abteilungsname
pers_dept_parentDeptNo=Nummer von übergeordneter Abteilung
pers_dept_parentDeptName=Name von übergeordneter Abteilung
pers_dept_parentDept=Übergeordnete Abteilung
pers_dept_note=Erscheint die neue Abteilung nicht in der Liste, beauftragen Sie bitte den Administrator damit, den Nutzer für die Bearbeitung der Abteilung zu autorisieren!
pers_dept_exit=Enthalt
pers_dept_auth=Verbundener System-Benutzer
pers_dept_parentMenuMsg=Die übergeordnete Abteilung kann nicht mit der untergeordneten Abteilung identisch sein.
pers_dept_initDept=Allgemein
pers_dept_deptMarket=Marketingabteilung
pers_dept_deptRD=Entwicklungsabteilung
pers_dept_deptFinancial=Finanzabteilung
pers_dept_nameNoSpace=Abteilungsname kann nicht mit einem Leerzeichen beginnen oder enden.
pers_dept_nameExist=Abteilungsname existiert bereits
pers_dept_changeLevel=Zur Ebene dieser Abteilung wechseln
pers_dept_noSpecialChar=Die sektornummern können kein zeichen enthalten!
pers_dept_NameNoSpecialChar=Eine abteilungsbezeichnung darf kein zeichen enthalten!
pers_dept_noModifiedParent=Die übergeordnete Abteilung kann nicht geändert werden!
#职位
pers_position_entity=Position
pers_position_code=Positionsnummer
pers_position_name=Positionsbezeichnung
pers_position_notExist=Die Position existiert nicht!
pers_position_sortNo=Ordnen
pers_position_parentName=Übergeordnete Position
pers_position_parentCode=Nummer von übergeordneter Position
pers_position_batchToPosition=Neue Position
pers_position_nameExist=Diese Positionsbezeichnung existiert bereits
pers_position_change=Position ändern
pers_position_parentMenuMsg=Die übergeordnete Position kann nicht als untergeordnete Position festgelegt werden.
pers_position_nameNoSpace=Positionsbezeichnung kann nicht mit einem Leerzeichen beginnen oder enden.
pers_position_existSub={0}: Enthält Unterposten kann nicht gelöscht werden
pers_position_existPerson={0}: Personal kann nicht gelöscht werden
pers_position_importTemplate=Jobimportvorlage
pers_position_downloadTemplate=Importvorlage herunterladen
pers_position_codeNotEmpty=Die Positionsnummer darf nicht leer sein
pers_position_nameNotEmpty=Der Positionsname darf nicht leer sein
pers_position_nameNoSpecialChar=Positionsname {0} darf keine Sonderzeichen enthalten!
pers_position_noSpecialChar=Positionsnummer {0} darf kein Sonderzeichen sein!
pers_position_codeLength=Die Positionsnummer {0} ist länger als 30 Stellen
pers_position_nameLength=Daten, deren Positionsname {0} länger als {1} ist
pers_position_codeExist=Die Job-ID {0} ist bereits vorhanden
#证件
pers_cert_type=Zertifikatstyp
pers_cert_number=Zertifikatsnummer
pers_cert_name=Zertifikatsname
pers_cert_numberExist=Die Zertifikatsnummer existiert bereits
#导出
pers_export_allPersPerson=Alle Personen
pers_export_curPersPerson=Aktuelle Person
pers_export_template=Vorlage exportieren
pers_export_personInfo=Personal exportieren
pers_export_personInfoTemplate=Personal herunterladen Vorlage importieren
pers_export_personBioTemplate=Biometrische Vorlage exportieren
pers_export_basicInfo=Grundlegende Informationen
pers_export_customAttr=Benutzerdefinierte Attribute
pers_export_templateComment=Feldname, Primärschlüssel?einzigartig?Nullwerte erlauben?({0},{1},{2},{3})
pers_export_templateFileName=Personal-Vorlage importieren
pers_export_bioTemplateFileName=Biometrische Vorlage Personal
pers_export_deptInfo=Abteilung exportieren
pers_export_deptTemplate=Abteilung herunterladen Vorlage importieren
pers_export_deptTemplateFileName=Vorlage Abteilung importieren
pers_export_personPhoto=Personal-Foto exportieren
pers_export_allPhotos=Alle Fotos (alle Personen auswählen)
pers_export_selectPhotoToExport=Start-Ausweisnummer und End-Ausweisnummer auswählen, um Personalfotos zu exportieren.
pers_export_fromId=Von Ausweisnummer
pers_export_toId=Bis
pers_export_certNumberComment=Der Zertifikatstyp wird nach Eingabe der Zertifikatsnummer benötigt
pers_export_templateCommentName=Feldname:({0})
pers_export_dataExist=Bitte stellen Sie sicher, dass die importierten Daten bereits im System vorhanden sind
pers_export_cardNoTip=Mehrere Kartennummern getrennt durch &
pers_carNumber_importTip=Nummernschild (bei mehreren Nummernschilder& getrennt)
#导入
pers_import_certNumberExist=Die Zertifikatsnummer {0} existiert bereits.
pers_import_complete=Beenden
pers_import_password=Person Passwort
pers_import_fail=Zeile {0} fehlgeschlagen {1}
pers_import_overData=Sie möchten {0} Personen importieren, das System unterstützt nur den Import von 30.000 Personen!
pers_import_pinTooLong=Personal-Ausweisnummer {0} ist zu lang!
pers_import_pinExist=Personal-Ausweisnummer {0} existiert bereits!
pers_import_pinIsRepeat=Personal-Ausweisnummer {0} ist zu lang!
pers_import_pinError=Personal-Ausweisnummer {0} Fehler!
pers_import_pinSupportNumber=The personnel number only supports numbers! The personnel number is: {0}
pers_import_pinNotSupportNonAlphabetic=Personal-Ausweisnummer unterstützt keine nicht-englischen Buchstaben! Die Personalnummer ist: {0}
pers_import_pinNotNull=Personal-Ausweisnummer darf nicht nur aus Nullen bestehen!
pers_import_pinStartWithZero=Personal-Ausweisnummer darf nicht mit Null beginnen!
pers_import_cardNoNotSupportLetter=Kartennummer unterstützt keine Buchstaben!
pers_import_cardNoNotNull=Kartennummer darf nicht nur aus Nullen bestehen!
pers_import_cardNoStartWithZero=Kartennummer darf nicht mit Null beginnen!
pers_import_cardTooLong=Kartennummer {0} ist zu lang!
pers_import_cardExist=Kartennummer {0} existiert bereits!
pers_import_personPwdOnlyNumber=Persönliches Passwort unterstützt nur Zahlen (keine Buchstaben)!
pers_import_personPwdTooLong=Persönliches Passwort {0} ist zu lang!
pers_import_personDuressPwd=Persönliches Passwort ist mehrfach vorhanden!
pers_import_emailTooLong=E-Mail-Adresse {0} ist zu lang
pers_import_nameTooLong=Name {0} ist zu lang
pers_import_genderError=Geschlecht Format-Fehler
pers_import_personDateError=Einstellungsdatum Format-Fehler
pers_import_createTimeError=Zeitraum erstellen Format-Fehler
pers_import_phoneError=Telefonnummer Format-Fehler
pers_import_phoneTooLong=Telefonnummer darf nicht länger als 20 Zeichen sein!
pers_import_emailError=E-Mail-Adresse Format-Fehler
pers_import_birthdayError=Geburtsdatum Format-Fehler!
pers_import_nameError=Nachname und Vorname dürfen keine Sonderzeichen enthalten! Die Personalnummer ist: {0}
pers_import_firstnameError=Nachname und Vorname dürfen kein ',' enthalten!
pers_import_firstnameNotNull=Vorname darf nicht frei bleiben!
pers_import_dataCheck=Die Datenüberprüfung ist abgeschlossen
pers_import_dataSaveFail=Daten importieren fehlgeschlagen!
pers_import_allSucceed=Alle Daten erfolgreich importiert!
pers_import_result=Erfolgt: {0}, Fehlgeschlagen: {1}.
pers_import_result2=Ergebnis importieren
pers_import_result3=Erfolgt: {0}, Aktualisiert: {1}, Fehlgeschlagen: {2}.
pers_import_notSupportFormat=Dieses Format wird nicht unterstützt!
pers_import_selectCorrectFile=Bitte passende Datei auswählen.
pers_import_fileFormat=Dateiformat
pers_import_targetFile=Zieldatei
pers_import_startRow=Überschrift Start-Zeilen
pers_import_startRowNote=Die erste Zeile des Datenformats ist der Tabellenname, die zweite die Überschrift , die dritte Zeile die Einfuhrdaten, bitte erst Datei überprüfen und dann importieren.
pers_import_delimiter=Trennzeichen
pers_import_importingDataFields=Datenbankfelder
pers_import_dataSourceFields=Datenfelder importieren
pers_import_total=Gesamt
pers_import_dataUpdate=Im System bestehende Personal-Ausweisnummern aktualisieren:
pers_import_dataIsNull=Keine Daten in der Datei!
pers_import_deptNotExist=Die Abteilung existiert nicht!
pers_import_deptIsNotNull=Abteilungsname darf nicht frei bleiben!
pers_import_pinNotEmpty=Personal-Ausweisnummer kann nicht leer bleiben!
pers_import_nameNotEmpty=Personalname darf nicht leer sein!
pers_import_siteCodeOnlyLetterNum=Adressschlüssel Format-Fehler
pers_import_cardNoFormatErrors=Das Format der Kartennummer {0} ist nicht korrekt.
pers_import_cardsNotSupport=Die Funktion [Mehrere Karten pro Person] ist deaktiviert, es kann nicht mehr als eine Karte pro Person programmiert werden.
pers_import_personInfo=Personal importieren
pers_import_commentFormat=Das Kommentar-Format ist nicht korrekt!
pers_import_noComment=Die Daten in {0} Zeile und {1} Spalte sind nicht kommentiert!
pers_import_fieldRepeat=Die Daten in {0} Zeile und {1} Spalte, der Feldname in der Spalte ist wiederholt {2}
pers_import_primaryKey=Mindestens ein Feld als Primärschlüssel nötig.
pers_import_templateIsRepeat=Personal-ID: Die Bio-Vorlage-Daten für {0} wurden dupliziert!
pers_import_biologicalTemplate=Biometrische Vorlage Importieren
pers_import_uploadFileSuccess=Hochladen erfolgreich, Datenanalyse gestartet, bitte warten...
pers_import_resolutionComplete=Die Daten wurden analysiert, Datenbankaktualisierung gestartet.
pers_import_mustField=Die importierte Datei muss die Spalte {0} enthalten.
pers_import_bioTemplateSuccess=Biometrische Vorlage importieren, diese Daten müssen von jedem Geschäftsmodul manuell mit dem Gerät synchronisiert werden.
pers_import_personPhoto=Personal-Foto importieren
pers_import_opera_log=Operation log
pers_import_error_log=Fehler log
pers_import_uploadFileSize=Bitte laden Sie eine Datei mit einer Größe von nicht größer als{0}!
pers_import_uploadFileSizeLimit=Für einen einmaligen Import laden Sie bitte eine Datei mit einer Größe von nicht größer als 500M!
pers_import_type=Importmodus
pers_import_photoType=Foto
pers_import_archiveType=Komprimiertes Paket
pers_import_startUpload=Upload starten
pers_import_addMore=Weitere hinzufügen
pers_import_photoQuality=Fotoqualität
pers_import_original=Original
pers_import_adaptive=Anpassungsfähig
pers_import_adaptiveSize=(Größe 480 * 640)
pers_import_totalNumber=Gesamt
pers_import_uploadTip=(Bitte das Foto nicht während des Hochladens löschen)
pers_import_addPhotoTip=Bitte Fotos zum Hochladen hinzufügen!
pers_import_selectPhotoTip=Bitte wählen Sie das Foto aus, das sie hochladen möchten!
pers_import_uploadResult=Foto-Upload Ergebnis
pers_import_pleaseSelectPhoto=Bitte Foto auswählen
pers_import_multipleSelectTip=Drücken Sie Ctrl, um mehrere gleichzeitig auszuwählen
pers_import_replacePhotoTip=Das gewählte Foto befindet sich bereits in der Liste, möchten Sie es ersetzen?
pers_import_photoNamePinNotCorrespond=Fotoname und persönliche Ausweisnummer stimmen nicht überein
pers_import_photoFormatRequirement=Bitte Foto mit der Ausweisnummer des Mitarbeiters benennen. Das korrekte Format ist JPG/PNG. Vergewissern Sie sich, dass der Fotoname keine Sonderzeichen enthält.
pers_import_filterTip=Vorschau einiger ausgewählter Fotos eventuell aus folgenden Gründen nicht möglich:
pers_import_photoContainSpecialCharacters=Fotoname enthält Sonderzeichen.
pers_import_photoFormatError=Falsches Fotoformat.
pers_import_photoSelectNumber=Wählen Sie nicht mehr als 3000 Bilder aus!
pers_import_photoSelectNumberLimit=Wählen Sie nicht mehr als {0} Bilder!
pers_import_fileMaxSize=Das Bild ist zu groß, bitte laden Sie eine Bilddatei hoch, die kleiner als 5M ist.
pers_import_notUploadPhotoNumber=Es können nicht mehr als 3000 Bilder hochgeladen werden!
pers_import_zipFileNotPhoto=Es gibt kein Foto der Person in der Zip-Datei, bitte erneut auswählen und importieren!
pers_import_personPlateRepeat=Personal-Nummernschild {0} duplizieren!
pers_import_filePlateRepeat=Datei innerhalb des Nummernschildes {0} duplizieren!
pers_import_personPlateFormat=Personal-Nummernschild {0} Format fehlerhaft!
pers_import_personPlateMax=Die Anzahl der Personal-Nummernschilder übersteigt das Maximum von 6!
pers_import_cropFaceFail=Erstellen von Vergleichsfoto fehlgeschlagen!
pers_import_pinLeaved=Personal-ID: {0} ist gegangen.
pers_import_exceedLicense=Importe nicht erlaubt für die die Software Lizenz übersteigende Anzahl von Personen.
pers_import_bioTemplateNotNull=Personal-ID, Biometrische Vorlage, Ausweisnummer oder Index und Inhalt, Version dürfen nicht frei bleiben!
pers_import_certTypeNotNull=Die Personalnummer lautet: {0} Dokumenttyp kann nicht leer sein
pers_import_certTypeNotExist=Personalnummer ist: {0} Belegtyp existiert nicht
pers_import_certNumNotNull=Personalnummer lautet: {0} Zertifikatsnummer kann nicht leer sein
pers_import_certNumberTooLong=Row {0}:ID-Nummer {1} ist zu lang!
pers_import_idNumberErrors=Row {0}:Ausweisnummer {1} ist missgestaltet!
pers_import_emailErrors=Row {0}:E-Mail-Adresse {1} Formatfehler!
pers_import_emailIsExist=E-Mail-Adresse {0} existiert bereits!
pers_import_emailIsRepeat=Row {0}: Die interne E-Mail-Adresse der Datei {1} wurde wiederholt!
pers_import_fileMobilePhoneRepeat=Row {0}: Die interne Handynummer der Datei {1} wurde wiederholt!
pers_import_mobilePhoneErrors=Fehler im Format der Mobiltelefonnummer!
pers_import_hireDateError=Das Format des Anmeldedatums ist falsch!
pers_import_selectPhotoType=Bitte wählen Sie den Typ des importierten Fotos aus!
pers_import_hireDateLaterCurrent=Das Mietdatum darf nicht später als das aktuelle Datum sein!
pers_import_buildingNotExist=Das Gebäude existiert nicht!
pers_import_unitNotExist=Einheit existiert nicht!
pers_import_vdbInfoFail=Es gibt keine Einheiteninformationen mit dem Namen {1} im Gebäude {0}!
pers_import_vdbBuildingFail=Die Gebäudeinformationen der Einheit {0} dürfen nicht leer sein!
pers_import_vdbRoomNoFail=Die Zimmernummer muss eine Zahl größer als 0 sein!
#人员离职
pers_person_leave=Kündigung
pers_dimission_date=Kündigungsdatum
pers_dimission_type=Art der Kündigung
pers_dimission_reason=Kündigungsgrund
pers_dimission_volutary=Freiwilliges Ausscheiden
pers_dimission_dismiss=Gekündigt
pers_dimission_resignat=Rücktritt
pers_dimission_shiftJob=Versetzung
pers_dimission_leave=Arbeitsplatz ohne Vergütung erhalten
pers_dimission_recovery=Wiedereinstellung
pers_dimission_sureToRecovery=Sind Sie sicher, dass Sie den Wiedereinstellungsvorgang durchführen möchten?
pers_dimission_backCard=Wurde die Karte zurückgegeben?
pers_dimission_isForbidAction=Zugangsrechte sofort deaktivieren?
pers_dimission_writeInfomation=Geben Sie die Kündigungsinformationen ein
pers_dimission_pinRetain=Personal-ID für den entlassenen Mitarbeiter behalten?
pers_dimission_downloadTemplate=Download-Importvorlage herunterladen
pers_dimission_import=Importentlassungen
pers_dimission_importTemplate=Importvorlage für Entlassung
pers_dimission_date_noNull=Das Datum der Entlassung kann nicht leer sein
pers_dimission_leaveType_noExist=Entlassungsart existiert nicht
pers_dimission_dateFormat=Erforderliches Feld, Zeitformat ist JJJJ-MM-TT, z. B.: 2020-07-22
pers_dimission_leaveType=Erforderliches Feld, z. B.: Freiwilliges Ausscheiden, Gekündigt, Rücktritt, Versetzung
pers_dimission_forbidden=In Schwarzliste hinzufügen
pers_dimission_leaveType_noNull=Der Urlaubstyp darf nicht leer sein!
pers_dimission_person_noExist=Die Kündigungsperson existiert nicht!
pers_dimission_date_error=、Kündigungsdatum nicht ausgefüllt oder falsch formatiert
#临时人员
pers_tempPerson_audit=Überprüfen
pers_tempPerson_view=Ansehen
pers_tempPerson_waitReview=Warten auf die Prüfung durch den Administrator
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Kartennummer
pers_card_state=Kartenzustand
pers_card_effect=Wirksam
pers_card_disabled=Ungültig
pers_card_past=Abgelaufen
pers_card_back=Karte zurückgegeben
pers_card_change=Karte ausgetauscht
pers_card_note=Die Kartennummer existiert bereits.
pers_card_numTooBig=Original Kartennummer zu lang
pers_issueCard_entity=Karte ausstellen
pers_issueCard_operator=Betreiber
pers_issueCard_operate=Handlung
pers_issueCard_note=Kartenausgeber arbeiten für registriertes Personalakten, nicht aber die Registrierkarten Account Mitarbeiter!
pers_issueCard_date=Kartendatum erstellen
pers_issueCard_changeTime=Zeit ändern
pers_issueCard_cardValidate=Kein Leerzeichen erlaubt.
pers_issueCard_cardEmptyNote=Die Karte darf nicht leer sein.
pers_issueCard_cardHasBeenIssued=Diese Karte wurde erstellt!
pers_issueCard_noCardPerson=Nicht eingeschriebene Person
pers_issueCard_waitPerson=Derzeitig eingeschriebene Person
pers_issueCard_mc5000=MC5000 Kartenausgabe
pers_batchIssCard_entity=Batch Karte ausstellen
pers_batchIssCard_startPersNo=Start-Personal-ID
pers_batchIssCard_endPersNo=End-Personal-ID
pers_batchIssCard_issCardNum=Anzahl erstellter Karten
pers_batchIssCard_notIssCardNum=Anzahl der Personen ohne ausgegebene Karte
pers_batchIssCard_generateList=Liste erstellen
pers_batchIssCard_startRead=Lesevorgang starten
pers_batchIssCard_swipCard=Position der Magnetstreifenkarte
pers_batchIssCard_sendCard=Gerät
pers_batchIssCard_dispenCardIss=USB-Lesegerät
pers_batchIssCard_usbEncoder=USB-Encoder
pers_batchIssCard_note=Personal-ID unterstützt nur Eingabewerte und zeigt nur Personen ohne ausgestellte Karte an (max. 300)!
pers_batchIssCard_startPinEmpty=Start-Personal-ID darf nicht frei bleiben!
pers_batchIssCard_endPinEmpty=End-Personal-ID darf nicht frei bleiben!
pers_batchIssCard_startPinLargeThanEndPin=Start-Personal-ID darf nicht länger sein als End-Personal-ID!
pers_batchIssCard_numberParagraphNoPerson=Person ohne ausgestellte Karte existiert nicht innerhalb der Start-End- ID-Segmentnummer!
pers_batchIssCard_inputCardNum=Kartennummer eingeben
pers_batchIssCard_cardLetter=Karte unterstützt keine Buchstaben!
pers_batchIssCard_cardNoTooLong=Die Kartennummer ist zu lang!
pers_batchIssCard_issueWay=Kartenregistrierungsverfahren
pers_batchIssCard_noPersonList=Personalliste ist noch nicht erstellt.
pers_batchIssCard_startReadCard=Karte lesen starten
pers_batchIssCard_swipePosition=Magnetkartendurchzugsposition
pers_batchIssCard_chooseSwipePosition=Bitte Magnetkartendurchzugsposition wählen.
pers_batchIssCard_readCardTip=Das Gerät liest die nicht registrierte Karte nur, wenn die Erstellungsmethode ein ausgewähltes Lesegerät ist.
pers_batchIssCard_notIssCardNo=Anzahl nicht erstellter Karten
pers_batchIssCard_totalNumOfCards=Kartengesamtzahl
pers_batchIssCard_acms=Ausstellung der ACMS-Karte
pers_lossCard_entity=Karte verloren gemeldet
pers_lossCard_lost=Diese Karte wurde gemeldet, Vorgang kann nicht wiederholt werden!
pers_losscard_note2=Nachdem Sie die Management-Karte überschrieben haben, müssen Sie diese Karte durch das Lesegerät am Aufzug ziehen um sicherzustellen, das die Karten im Gerät ihre Wirkung verlieren.
pers_revertCard_entity=Verlorene Karte Reaktivieren
pers_revertCard_setReport=Bitte erst Kartenverlust melden!
pers_revertcard_note2=Nachdem Sie die Management-Karte überschrieben haben, müssen Sie diese Karte durch das Lesegerät am Aufzug ziehen um sicherzustellen, dass Sie die Karte wieder benutzen können.
pers_issueCard_success=Kartenausgabe erfolgreich!
pers_issueCard_error=Kartenausgabe fehlgeschlagen!
pers_cardData_error=Format-Ausnahme beim Lesen von Kartendaten!
pers_analysis_error=Ausnahme beim Analysieren von Kartendaten!
pers_cardOperation_error=Ausnahme beim Kartenbetrieb!
pers_cardPacket_error=Ausnahme bei Befehl Paket Kartenbetrieb!
pers_card_write=Karte schreiben
pers_card_init=Karte initialisieren
pers_card_loss=Verlorene Karte
pers_card_revert=Karte wiederherstellen
pers_card_writeMgr=Management-Karte schreiben
pers_initCard_tip=Nach der Initialisierung wird die Karte zur leeren Karte!
pers_initCard_prepare=Bereit für die Initialisierung der Karte...
pers_initCard_process=Karte wird initialisiert...
pers_initCard_success=Karte erfolgreich initialisieren
pers_mgrCard_prepare=Bereit für das Erstellen von Management-Kartendaten...
pers_mgrCard_process=Erstellt Management-Kartendaten...
pers_mgrCard_success=Management-Kartendaten erfolgreich erstellt
pers_userCard_prepare=Bereit zum erstellen einer Benutzerkarte...
pers_userCard_process=Erstellt Benutzekartendaten...
pers_userCard_success=Benutzerkarte erfolgreich erstellt
pers_userCard_tip=Bitte Start- und Endzeit auf der Seite Person bearbeiten festlegen und dann Kartenvorgang erstellen.
pers_userCard_tip2=Berechtigungsdaten leer, Karte kann nicht erstellt werden.
pers_userCard_tip3=Das der Berechtigungsgruppe zugewiesene Gerät mehr als zwei Einheiten, Datenverlust.
pers_writeCard_tip=Bitte stellen Sie sicher, dass der Encoder angeschlossen und der Treiber installiert ist und legen Sie die Karte auf den Encoder.
pers_writeMgrCard_tip=Die Anzahl verlorener Karten und wiederhergestellter Karten darf nicht höher als 18 sein.
pers_writeMgrCard_tip2=Die Nummer der verlorenen Karte und wiederhergestellten Karte:
pers_card_writeToMgr=Der Management-Karte zugeschrieben
pers_card_hex=Anzeige Kartenformat
pers_card_decimal=Dezimal
pers_card_Hexadecimal=Hexadezimal
pers_card_IssuedCommandFail=Erteilter Befehl fehlgeschlagen:
pers_card_multiCard=Mehr Karten
pers_card_deputyCard=Zweitkarten
pers_card_deputyCardValid=Bitte führen Sie zuerst die Hauptkarte ein!
pers_card_writePinFormat=System kann nur Personal-ID ohne Buchstaben zur Karte zuordnen!
pers_card_notMoreThanSixteen=Die Anzahl an Zweitkarten kann nicht größer sein als 16.
pers_card_notDelAll=Nicht alle Zweitkarten können eliminiert werden.
pers_card_maxCard=Die Kartennummer darf {0} nicht überschreiten.
pers_card_posUseCardNo=Die Hauptkarte der Person wird vom Verbrauchermodul benutzt Bitte begeben Sie sich zum Verbrauchermodul um den Kartenentnahmevorgang durchzuführen!
pers_card_delFirst=Bitte löschen Sie die ausgestellte Kartennummer vor der Ausstellung der Karte!
pers_card_disablePersonWarn=Die gewählte Kartennummer enthält behindertes Personal und kann nicht bedient werden!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Adressschlüssel
pers_wiegandFmt_wiegandMode=Modus
pers_wiegandFmt_wiegandModeOne=Modus Eins
pers_wiegandFmt_wiegandModeTwo=Modus Zwei
pers_wiegandFmt_isDefaultFmt=Auto
pers_wgFmt_entity=Wiegand-Format
pers_wgFmt_in=Wiegand-Input-Format
pers_wgFmt_out=Wiegand-Output-Format
pers_wgFmt_inType=Wiegand-Input-Typ
pers_wgFmt_outType=Wiegand-Output-Typ
pers_wgFmt_wg=Wiegand-Format
pers_wgFmt_totalBit=Gesamt Bit
pers_wgFmt_oddPch=Ungeradzahlige Paritätsüberprüfung(o)
pers_wgFmt_evenPck=Geradzahlige Paritätsüberprüfung(e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=Einrichtungscode(f)
pers_wgFmt_siteCode=Adressschlüssel(s)
pers_wgFmt_manufactoryCode=Herstellercode(m)
pers_wgFmt_firstParity=Erste Paritätsüberprüfung(p)
pers_wgFmt_secondParity=Zweite Paritätsüberprüfung(p)
pers_wgFmt_cardFmt=Kartenprüfformat
pers_wgFmt_parityFmt=Paritätsüberprüfungsformat
pers_wgFmt_startBit=Start-Bit
pers_wgFmt_test=Kartenformat-Test
pers_wgFmt_error=Kartenformat-Fehler!
pers_wgFmt_verify1=Gesamt-Bit darf nicht über 80 liegen!
pers_wgFmt_verify2=Formatlänge der Testkarte muss mit Gesamtanzahl von Bits identisch sein!
pers_wgFmt_verify3=Paritätsformatslänge muss mit Gesamtanzahl von Ziffern identisch sein!
pers_wgFmt_verify4=Die initialisierten Daten können nicht gelöscht werden!
pers_wgFmt_verify5=Kartenformat wird benutzt, kann nicht löschen!
pers_wgFmt_verify6=Erstes Paritäts-Bit kann nicht größer sein als Gesamtzahl von!
pers_wgFmt_verify7=Zweites Paritäts-Bit kann nicht größer sein als Gesamtzahl von!
pers_wgFmt_verify8=Start- und Maximallängenformat sind nicht korrekt!
pers_wgFmt_verify9=Die Länge des Kartenprüfformats kann nicht größer sein als Gesamtzahl von!
pers_wgFmt_verify10=Ziffernfunktion der Kartenprüfung kann nicht kreuzen!
pers_wgFmt_verify11=Dieser Adressschlüssel übersteigt den festgelegten Bereich!
pers_wgFmt_verify=Überprüfung
pers_wgFmt_unverify=Keine Überprüfung
pers_wgFmt_atLeastDefaultFmt=Bitte mindestens ein automatisch übereinstimmendes Kartenformat einhalten!
pers_wgFmt_defaultFmtError1=Andere Karten sind mit der selben Kartennummern-Bits formatiert, können nicht als automatisch übereinstimmendes Kartenformat festgelegt werden!
pers_wgFmt_defaultFmtError2=Identisches Kartenformat beim automatischen Abgleich vorhanden, Vorgang fehlgeschlagen!
pers_wgFmt_cardFormats=Kartenformate
pers_wgFmt_cardFormatTesting=Kartenformate-Test
pers_wgFmt_checkIsUsed=Das Kartenformat wird in {0} verwendet und kann nicht gelöscht werden!
pers_wgFmt_supportDigitsNumber=Bitte geben Sie die Anzahl der vom Gerät unterstützten Ziffern ein
#选人控件
pers_widget_selectPerson=Personal auswählen
pers_widget_searchType1=Abfrage
pers_widget_searchType2=Abteilung
pers_widget_deptHint=Bemerkung: Gesamtes Personal der ausgewählten Abteilungen wird importiert
pers_widget_noPerson=Kein Personal ausgewählt.
pers_widget_noDept=Bitte Abteilung auswählen.
pers_widget_noDeptPerson=Keine Person unter ausgewählter Abteilung, bitte erneut auswählen!
#人员属性
pers_person_carPlate=Nummernschild
pers_person_socialSecurity=Sozialversicherungsnummer
pers_person_msg4=Die maximale Länge darf 20 nicht überschreiten!
pers_person_msg5=Der Name kann nicht mehr als 50 Zeichen enthalten!
pers_person_type=Personentyp
pers_person_reseCode=Self-Login-Passwort
pers_person_IsSendMail=E-Mail-Benachrichtigung
pers_person_inactive=Inaktiv
pers_person_active=Aktiv
pers_person_employee=Mitarbeiter
pers_person_isSendMailMsg=Um die Funktion [Ereignisbenachrichtigung] zu benutzen, müssen Sie erst die E-Mail-Adresse eingeben.
pers_person_createTime=Zeitpunkt erstellen
pers_person_pinFirstValid=Erstes Zeichen der Personalnummer kann nicht 8 oder 9 sein.
pers_person_attrValueValid=Der Wert des Feldes kann nicht wiederholt werden.
pers_person_attrValueDelimiterValid=Das Trennzeichen sollte in der Mitte sein.
pers_person_isSendSMS=SMS-Benachrichtigung
pers_person_building=Gebäudename
pers_person_unitName=Einheitsname
pers_person_roomNo=Raumnummer
#动态属性
pers_attr_emp_type=Mitarbeitertyp
pers_attr_street=Straße
pers_attr_nation=Land
pers_attr_office_address=Geschäftsadresse
pers_attr_postcode=Postleitzahl
pers_attr_office_phone=Geschäftstelefon
pers_attr_home_phone=Private Telefonnummer
pers_attr_job_title=Positionsbezeichnung
pers_attr_birthplace=Geburtsort
pers_attr_polit_status=Politischer Status
pers_attr_country=Land
pers_attr_home_address=Privatadresse
pers_attr_hire_type=Anstellungsverhältnis
pers_attr_inContract=Vertragsarbeiter
pers_attr_outContract=Arbeiter ohne Vertrag
#属性自定义
pers_attribute_attrName=Name anzeigen
pers_attribute_attrValue=Wert zuordnen
pers_attribute_controlType=Input-Typ
pers_attribute_positionX=Zeile
pers_attribute_positionY=Spalte
pers_attribute_showTable=In Personenliste anzeigen
pers_attrDefini_deletemsg=Diese Eigenschaft wurde angewendet. Sind Sie sicher, dass Sie löschen möchten?
pers_attrDefini_reserved=System hat Feldname freigehalten.
pers_attrDefini_msg1=Die maximale Länge nicht mehr als 30!
pers_attrDefini_msg2=Die Zeilen der Spalte existieren bereits, bitte wechseln Sie zu einem anderen Ort!
pers_attrDefini_attrValue_split=Benutzung von einem \' ; \' Trennzeichen.
pers_attrDefini_attrName=Attributname
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=Attribute-Id
pers_attrDefini_select=Pull-Down-Liste
pers_attrDefini_check=Mehrfachauswahl
pers_attrDefini_radio=Einfache Auswahl
pers_attrDefini_text=Text
pers_attrDefini_maxCol=Nicht mehr als 2 Spalten.
pers_attrDefini_maxLimit=Benutzerdefinierte Attribute hat Höchstzahl erreicht!
pers_attrDefini_modControlType=Veränderung des Input-Typ löscht aktuelle Felddaten für gesamtes Personal, möchten Sie fortfahren?
#leavePerson
pers_leavePerson_reinstated=Wiedereinstellung
#opExample
pers_example_newRecode=Erwerb neuer Datensätze
pers_example_allRecode=Zugang zu allen Datensätzen
pers_custField_StatisticalType=Statistisches Verfahren
#人员参数修改
pers_param_isAudit=Aushilfspersonal Selbst-Überprüfung aktivieren
pers_param_donotChangePin=Die vorhandene Personal-ID enthält Buchstabe(n), kann Funktion nicht deaktivieren [Support Letters].
pers_param_hexChangeWarn=Das aktuelle System verfügt bereits über die Kartennummern, Anzeigemodus Kartenformat kann nicht geändert werden.
pers_param_cardsChangeWarn=Es gibt Personen mit mehr als einer Karte im System, Funktion kann nicht deaktiviert werden [Mehrere Karten pro Person].
pers_param_maxPinLength=Die neue Personal-ID kann nicht kürzer als die bereits im System vorhandene Personal-ID sein.
pers_param_pinBeyondDevLength=Die maximale vom im System vorhandenen Gerät unterstützte Länge der Personal-ID ist {0}, bitte eine ganze Zahl unter {1} eingeben.
pers_param_cardBeyondDevLength=Die maximale vom im System vorhandenen Gerät unterstützte Länge der Kartennummer ist {0}, bitte eine ganze Zahl unter {1} eingeben.
pers_param_checkIsExistNoAudit=Es sind ungeprüfte Registrierungspflichtige im aktuellen System und können nicht zu automatischer Prüfung modifiziert werden!
pers_param_noSupportPinLetter=Es sind Geräte im System, die nicht die in der Personal-ID enthaltenen Buchstaben unterstützt, Funktion kann nicht aktiviert werden [Support Letters].
pers_param_changePinLettersTip=Personalnummer unterstützt Auto-Inkrement, Personalnummernmodus kann nicht modifiziert werden
pers_param_changePinIncrementTip=Personalnummer unterstützt enthält Buchstaben, Personalnummernmodus kann nicht geändert werden
pers_param_qrCode=Enterprise-QR-Code
pers_param_employeeRegistrar=Mitarbeiter-Cloud-Registrierung aktivieren
pers_param_downloadQRCodePic=QR-Code-Image herunterladen
pers_param_qrCodeUrl=QR-Code-URL
pers_param_qrCodeUrlCreate=Self-Service-Registrierung
pers_param_qrCodeUrlHref=Serveradresse: Anschluss
pers_param_pinSetWarn=Im aktuellen System ist bereits Personal vorhanden und der Personalnummernmodus kann nicht geändert werden.
pers_param_selfRegistration=Selbstregistrierung aktivieren
pers_param_infoProtection=Schutz personenbezogener sensibler Informationen
pers_param_infoProtectionWarnMsg=Nach Aktivierung der Option zum Schutz personenbezogener sensibler Informationen werden die in diesem Modul enthaltenen sensiblen personenbezogenen Daten desensibilisiert oder verschleiert, einschließlich, aber nicht beschränkt auf Namen, Kartennummern, ID-Nummern, Fotos usw.
pers_param_templateServer=Facial Template Extraction Server
pers_param_enableFacialTemplate=Gesichtsvorlagenextraktion aktivieren
pers_param_templateServerAddr=Serveradresse für die Extraktion von Gesichtsvorlagen
pers_param_templateServerWarnInfo=Wenn die Gesichtsvorlagenextraktion aktiviert ist, wenn der Gesichtsvorlagenextraktionsserver online ist und die Benutzerverifizierung bestanden wurde, extrahiert das Personal standardmäßig Gesichtsvorlagen beim Vergleich von Fotos; Wenn sich der Gesichtsvorlagenextraktionsserver im Offline-Modus befindet, extrahieren Sie keine Gesichtsvorlagen!
pers_param_templateServerWarnInfo1=Wenn Sie die Gesichtsvorlagenextraktion aktivieren, muss ein Gerät angeschlossen werden, das die Gesichtsvorlagenextraktion unterstützt!
pers_param_templateServerOffline=Der Gesichtsvorlagenextraktionsserver ist offline und kann keine Gesichtsvorlagen extrahieren! Willst du weitermachen?
pers_param_faceServer=Facial Backend Vergleichsservice
pers_param_enableFaceVerify=Gesichtsbackend-Vergleich aktivieren
pers_param_faceServerAddr=Adresse des Backend-Vergleichsdienstes für Gesicht
pers_param_faceServerSecret=Backend-Vergleichsschlüssel für Gesicht
#国籍
pers_person_nationality=Staatsangehörigkeit
pers_nationality_angola=angolanisch
pers_nationality_afghanistan=afghanisch
pers_nationality_albania=albanisch
pers_nationality_algeria=algerisch
pers_nationality_america=amerikanisch
pers_nationality_andorra=andorranisch
pers_nationality_anguilla=anguillanisch
pers_nationality_antAndBar=antiguanisch
pers_nationality_argentina=argentinisch
pers_nationality_armenia=armernisch
pers_nationality_ascension=Ascension
pers_nationality_australia=australianisch
pers_nationality_austria=österreichisch
pers_nationality_azerbaijan=aserbaidschanisch
pers_nationality_bahamas=Bahamas
pers_nationality_bahrain=Bahrain
pers_nationality_bangladesh=Bangladesch
pers_nationality_barbados=barbadisch
pers_nationality_belarus=Weißrussland
pers_nationality_belgium=Belgien
pers_nationality_belize=belizisch
pers_nationality_benin=beninisch
pers_nationality_bermudaIs=Bermuda Inseln
pers_nationality_bolivia=Bolivien
pers_nationality_botswana=botswanisch
pers_nationality_brazil=Brasilien
pers_nationality_brunei=bruneiisch
pers_nationality_bulgaria=bulgarisch
pers_nationality_burkinaFaso=Burkina Faso
pers_nationality_burma=burmesisch
pers_nationality_burundi=burundisch
pers_nationality_cameroon=kamerunisch
pers_nationality_canada=kanadisch
pers_nationality_caymanIs=Kaimaninseln
pers_nationality_cenAfrRepub=Zentralafrikanische Republik
pers_nationality_chad=tschadisch
pers_nationality_chile=Chile
pers_nationality_china=Chinesisch
pers_nationality_colombia=kolumbianisch
pers_nationality_congo=kongolesisch
pers_nationality_cookIs=Cookinseln
pers_nationality_costaRica=costaricanisch
pers_nationality_cuba=kubanisch
pers_nationality_cyprus=Zypern
pers_nationality_czechRep=Tschechien
pers_nationality_denmark=Dänemark
pers_nationality_djibouti=dschibutisch
pers_nationality_dominicaRep=dominikanisch
pers_nationality_ecuador=Ecuador
pers_nationality_egypt=ägyptisch
pers_nationality_eISalvador=salvadorianisch
pers_nationality_england=britisch
pers_nationality_estonia=estnisch
pers_nationality_ethiopia=äthiopisch
pers_nationality_fiji=Fidschi
pers_nationality_finland=Finnland
pers_nationality_france=Frankreich
pers_nationality_freGui=Französisch Guayana
pers_nationality_gabon=gabunisch
pers_nationality_gambia=Gambia
pers_nationality_georgia=georgisch
pers_nationality_germany=deutsch
pers_nationality_ghana=ghanaisch
pers_nationality_gibraltarm=gibraltarisch
pers_nationality_greece=griechisch
pers_nationality_grenada=Grenada
pers_nationality_guam=Guam
pers_nationality_guatemala=guatemaltekisch
pers_nationality_guinea=guineisch
pers_nationality_guyana=Guyana
pers_nationality_haiti=Haiti
pers_nationality_honduras=Honduras
pers_nationality_hungary=ungarisch
pers_nationality_iceland=isländisch
pers_nationality_india=indisch
pers_nationality_indonesia=indonesisch
pers_nationality_iran=iranisch
pers_nationality_iraq=irakisch
pers_nationality_ireland=irisch
pers_nationality_israel=israelisch
pers_nationality_italy=italienisch
pers_nationality_ivoryCoast=ivorisch
pers_nationality_jamaica=jamaikanisch
pers_nationality_japan=Japan
pers_nationality_jordan=Jordanien
pers_nationality_kenya=kenianisch
pers_nationality_korea=koreanisch
pers_nationality_kuwait=kuwaitisch
pers_nationality_kyrgyzstan=Kirgisistan
pers_nationality_laos=laotisch
pers_nationality_latvia=lettisch
pers_nationality_lebanon=libanesisch
pers_nationality_lesotho=mesotho
pers_nationality_liberia=liberianisch
pers_nationality_libya=libysch
pers_nationality_liechtenstein=liechtensteinisch
pers_nationality_lithuania=litauisch
pers_nationality_luxembourg=luxemburgisch
pers_nationality_madagascar=madagassisch
pers_nationality_malawi=malawisch
pers_nationality_malaysia=Malaysia
pers_nationality_maldives=Malediven
pers_nationality_mali=Mali
pers_nationality_malta=maltesisch
pers_nationality_marianaIs=Marianen Inseln
pers_nationality_martinique=Martinique
pers_nationality_mauritius=mauritisch
pers_nationality_mexico=mexikanisch
pers_nationality_moldova=Moldawien
pers_nationality_monaco=Monaco
pers_nationality_montseIs=Montserrat
pers_nationality_morocco=marokkanisch
pers_nationality_mozambique=mosambikisch
pers_nationality_namibia=naimibisch
pers_nationality_nauru=Nauru
pers_nationality_nepal=Nepal
pers_nationality_netAnti=Niederländische Antillen
pers_nationality_netherlands=Holland
pers_nationality_newZealand=neuseeländisch
pers_nationality_nicaragua=Nicaragua
pers_nationality_niger=nigrisch
pers_nationality_nigeria=Nigeria
pers_nationality_norKorea=Nord Korea
pers_nationality_norway=norwegisch
pers_nationality_oman=omanisch
pers_nationality_pakistan=pakistanisch
pers_nationality_panama=Panama
pers_nationality_papNewCui=papua-neuguineisch
pers_nationality_paraguay=Paraguay
pers_nationality_peru=Peru
pers_nationality_philippines=Philippinen
pers_nationality_poland=Polen
pers_nationality_frenPolyne=polynesisch
pers_nationality_portugal=portugiesisch
pers_nationality_puerRico=Puerto Rico
pers_nationality_qatar=katarisch
pers_nationality_reunion=Reunion
pers_nationality_romania=rumänisch
pers_nationality_russia=russisch
pers_nationality_saiLueia=lucianisch
pers_nationality_saintVinc=St. Vincent
pers_nationality_samoa_eastern=Amerikanisch Samoa
pers_nationality_samoa_western=Samoa
pers_nationality_sanMarino=Sanmarinesisch
pers_nationality_saoAndPrinc=sao-toméisch
pers_nationality_sauArabia=Saudi Arabien
pers_nationality_senegal=senegalesisch
pers_nationality_seychelles=seychellisch
pers_nationality_sieLeone=Sierra Leone
pers_nationality_singapore=Singapur
pers_nationality_slovakia=slowakisch
pers_nationality_slovenia=slowenisch
pers_nationality_solomonIs=Salomoninseln
pers_nationality_somali=somalisch
pers_nationality_souAfrica=südafrikanisch
pers_nationality_spain=Spanisch
pers_nationality_sriLanka=srilankisch
pers_nationality_sudan=Sudan
pers_nationality_suriname=Suriname
pers_nationality_swaziland=swasiländisch
pers_nationality_sweden=schwedisch
pers_nationality_switzerland=schweizerisch
pers_nationality_syria=syrisch
pers_nationality_tajikstan=tadschikisch
pers_nationality_tanzania=tansanisch
pers_nationality_thailand=Thailändisch
pers_nationality_togo=togoisch
pers_nationality_tonga=tongaisch
pers_nationality_triAndToba=Trinidad und Tobago
pers_nationality_tunisia=tunesisch
pers_nationality_turkey=türkisch
pers_nationality_turkmenistan=turkmenisch
pers_nationality_uganda=ugandisch
pers_nationality_ukraine=ukrainisch
pers_nationality_uniArabEmira=Vereinigte Arabische Emirate
pers_nationality_uruguay=Uruguay
pers_nationality_uzbekistan=Usbekistan
pers_nationality_venezuela=Venezuela
pers_nationality_vietnam=Vietnamesisch
pers_nationality_yemen=jemenitisch
pers_nationality_serbia=Serbien
pers_nationality_zimbabwe=simbabwisch
pers_nationality_zambia=sambisch
pers_nationality_aruba=Aruba
pers_nationality_bhutan=Bhutan
pers_nationality_bosnia_herzegovina=bosnisch
pers_nationality_cambodia=Kambodscha
pers_nationality_congoD=Demokratische Republik Kongo
pers_nationality_comoros=komorisch
pers_nationality_capeVerde=kap-verdisch
pers_nationality_croatia=Kroatien
pers_nationality_dominica=dominikanisch
pers_nationality_eritrea=Eritrea
pers_nationality_micronesia=Föderierte Staaten von Mikronesien
pers_nationalit_guineaBissau=Guinea-Bissau
pers_nationalit_equatorialGuinea=Äquatorialguinea
pers_nationalit_hongkong=Hongkong
pers_nationalit_virginIslands=Amerikanische Jungferninseln
pers_nationalit_britishVirginIslands=Britische Jungferninseln
pers_nationalit_kiribati=kiribatisch
pers_nationalit_mongolia=Mongolei
pers_nationalit_marshall=marshallisch
pers_nationalit_macedonia=Mazedonien
pers_nationalit_montenegro=Montenegro
pers_nationalit_mauritania=Mauretanien
pers_nationalit_palestine=Palästina
pers_nationalit_palau=palaisch
pers_nationalit_rwanda=Ruanda
pers_nationalit_saintKittsNevis=St. Kitt und Nevis´
pers_nationalit_timorLeste=Timor-Leste
pers_nationalit_taiwan=Taiwan
pers_nationalit_tuvalu=Tuvalu
pers_nationalit_vanuatu=Vanuatu
#制卡
pers_person_cardprint=Karte drucken
pers_cardTemplate_tempSelect=Kartenvorlage
pers_cardTemplate_printerSelect=Drucker
pers_cardTemplate_front=Front
pers_cardTemplate_opposite=Zurück
pers_cardTemplate_entryDate=Einstellungsdatum
pers_cardTemplate_photo=Foto
pers_cardTemplate_uploadFail=Bild hochgeladen fehlgeschlagen!
pers_cardTemplate_jpgFormat=Unterstützt nur das Hochladen von Bildern im JPG-Format!
pers_cardTemplate_printStatus=Druckstatus
pers_cardTemplate_waiting=Warten
pers_cardTemplate_printing=Drucken
pers_cardTemplate_printOption=Druckoption
pers_cardTemplate_duplexPrint=Duplexdruck
pers_cardTemplate_frontOnly=Nur Vorderseite drucken
#app
pers_app_delPers=Es sind keine Personen auf dem Server, den Sie löschen möchten
pers_app_deptIsNull=Abteilungsnummer oder entsprechender Abteilungsname nicht gefunden
pers_app_personNull=Person existiert nicht
pers_app_pinExist=Die Personal-ID existiert bereits
pers_app_dateError=Das Datumsformat ist nicht korrekt, bitte beachten Sie das richtige Format:2016-08-08
#api
pers_api_selectPhotoInvalid=Das Foto ist ungültig, bitte erneut hochladen
pers_api_dateError=Das Datumsformat ist falsch
pers_api_personNotExist=Person existiert nicht
pers_api_cardsPersSupport=Das System öffnet keine Karte. Karte ist ungültig.
pers_api_department_codeOrNameNotNull=Abteilungscode oder Abteilungsname dürfen nicht leer sein
pers_api_deptSortNoIsNull=Die Abteilungssortierung darf nicht leer sein!
pers_api_deptSortNoError=Der Wert der Abteilungssortierung muss zwischen 1-999999 liegen!
pers_api_dataLimit=Die aktuelle Anzahl der Operationen ist {0} und überschreitet die Grenze von {1}. Bitte in Chargen arbeiten!
pers_api_cardTypeError=Kartentyp-Fehler
# 人员 生物 模板 API
pers_api_fingerprintExisted=Der Fingerabdruck der Person ist bereits vorhanden
pers_api_validtypeIncorrect=Dieser validtype-Attributwert ist falsch
pers_api_dataNotExist=TemplateNo nicht vorhanden
pers_api_templateNoRang=Bitte geben Sie den korrekten templateNo-Wert im Bereich 0-9 ein
pers_api_templateIsNull=Vorlage kann nicht leer sein!
pers_api_versionIsNumber=Version kann nur Zahlen eingeben!
# 临时 人员 自助 注册
pers_tempPersion_pinOnlyNumber=Pin kann nur eine Zahl sein!
#biotime
pers_h5_personAvatarNotNull=Der Avatar ist leer
pers_h5_personMobileRepeat=Handynummer existiert bereits
pers_h5_personEmailRepeat=Das Postfach ist bereits vorhanden
pers_h5_pwdIsRepetition=Neue und alte Passwortwiederholung
pers_h5_personIdNull=Mitarbeiter-ID ist leer
pers_h5_pinOrEmailIsNull=Bitte geben Sie die Nummer und die E-Mail-Adresse ein
pers_emailTitle_resetPassword=Passwort ändern
pers_emailContent_resetPassword=Der Link ist 24 Stunden gültig. Kopieren Sie den Link in den Browser, um das Passwort zu ändern:
pers_h5_tokenIsNull=Gutschein ist leer
pers_h5_tokenError=Belegfehler
pers_h5_oldPwdIsError=Altes Passwort wurde falsch eingegeben
pers_api_resetPasswordSuccess=Passwort wurde aktualisiert
pers_api_resetPasswordFail=Kennwortänderung fehlgeschlagen
pers_api_forgetPassword=Passwort vergessen
pers_api_confirmSubmit=Übermittlung bestätigen
pers_api_confirmPwdCaution=Klicken Sie auf [OK], um das neue Passwort zu bestätigen.
pers_api_pwdRule=Das Passwort muss mindestens ein Symbol oder eine Zahl enthalten und mindestens 8-12 Zeichen lang sein
pers_h5_personPinFormatNumber=Die Personalnummer kann nur aus Zahlen oder Buchstaben bestehen
pers_h5_persEmailNoExist=Fehler beim Ausfüllen der Mailbox
pers_h5_pageNull=Paging-Parameterfehler
pers_h5_personPinNotStartWithZero=Die Personalnummer kann nicht mit 0 beginnen
pers_h5_personPinTooLong=Die Personalnummer ist zu lang
pers_h5_personPinInValid=Diese Nummer wird bereits verwendet
pers_h5_imgSizeError=Bitte laden Sie ein Bild hoch, das nicht größer als 10 Millionen ist!
pers_h5_confirmAndContinue=Bestätigen und fortfahren
# 人 脸 抠图 不 及格 错误
pers_face_poorResolution=Bildauflösung unter 80000 Pixel
pers_face_noFace=Kein Gesicht erkannt
pers_face_manyFace=Mehrere Gesichter erkannt
pers_face_smallFace=Das Gesichtsverhältnis ist zu klein
pers_face_notColor=Das Bild ist nicht farbig
pers_face_seriousBlur=Bild ist unscharf
pers_face_intensivelyLight=Das Bild ist stark belichtet
pers_face_badIllumination=Bild ist zu dunkel
pers_face_highNoise=Bilder mit hohem Rauschen
pers_face_highStretch=Überdehntes Gesicht
pers_face_covered=Gesicht ist bedeckt
pers_face_smileOpenMouth=Übermäßiges Lächeln
pers_face_largeAngle=Der Gesichtsablenkwinkel ist zu groß
pers_face_criticalIllumination=Bildhelligkeit kritisch
pers_face_criticalLargeAngle=Kritischer Ablenkwinkel
pers_face_validFailMsg=Gesichtserkennung fehlgeschlagen aufgrund:
pers_face_failType=Fehlertyp des Gesichtsausschnitts
pers_face_photoFormatError=Das Fotoformat ist falsch. Bitte laden Sie eine Datei im JPG / PNG-Format hoch.
pers_face_notUpdateMsg=Erstellung des Gesichtsbildes fehlgeschlagen, aktualisieren Sie das Gesichtsbild nicht.
# 健康 申报
pers_health_enable=Aktiviert die Deklaration von Gesundheitsinformationen
pers_health_attrExposure=Jede Exposition gegenüber Verdachtsfällen
pers_health_attrSymptom=Alle Symptome in den letzten 14 Tagen
pers_health_attrVisitCity=Stadt, die in den letzten 14 Tagen besucht wurde
pers_health_attrRemarks=Hinweise zur Gesundheit
pers_health_symptomCough=Husten
pers_health_symptomFever=Fieber
pers_health_symptomPolypena=Atemprobleme
pers_health_declaration=Gesundheitserklärung
pers_health_aggrement=Ich habe zugestimmt, dass Personen, die die Informationen nicht wie erforderlich ausfüllen, keinen Zugriff erhalten. Besucher, die diese Informationen nicht wahrheitsgemäß gemeldet haben, können ihren Besuch nicht fortsetzen und müssen die entsprechenden rechtlichen Verantwortlichkeiten tragen.
pers_health_visitCity_notEmpty=Die besuchte Stadt darf nicht leer sein!
pers_health_notAgree=Bitte überprüfen Sie die Vereinbarung, um fortzufahren.
#人员名单库
pers_personnal_list_manager=Listenmanager
pers_personnal_list=Persönliche Liste
pers_personnal_list_scheme=Domain-Modus
pers_personnal_list_name=Name der persönlichen Liste
pers_personnal_list_group_str_id=Listengruppen-ID
pers_personnal_list_personCount=Anzahl der Personen
pers_personnal_list_tag=Benutzerdefiniert
pers_personnal_list_type=Persönlicher Listentyp
pers_personnallist_addPerson_repo=Eine Person zum Repository hinzufügen
pers_personnallist_sendPersonnallist=Verteilte Listenbibliothek
pers_personnallist_sendPerson=Person senden
pers_personnallist_notDel_existPerson=Die Listenbibliothek enthält Personen und kann nicht gelöscht werden
pers_personnallist_peopleInRoster=Es befinden sich noch Personen in der Listenbibliothek
pers_personnallist_associationNotExist=Die Verknüpfung zwischen dem Hauptgerät und der Listenbibliothek existiert nicht
pers_personnal_list_person=Person auf der persönlichen Liste
pers_personnal_list_dev=Bibliotheksberechtigungen auflisten
pers_personnal_list_addDev=Gerät hinzufügen
pers_personnal_list_name_isExist=Der Name existiert bereits
pers_personnal_bannedList=Bibliothek für gesperrte Listen
pers_personnal_allowList=Zulassungslistenbibliothek
pers_personnal_redList=Bibliothek der Roten Liste
pers_personnal_attGroup=Anwesenheitsgruppe
pers_personnal_passList=Passliste
pers_personnal_banList=Verbotene Liste
pers_personnal_visPassList=Visitor Pass List
pers_personnal_visBanList=Liste der verbotenen Besucher
pers_personnal_databaseHasBeenDistributed=Die ausgewählte Listenbibliothek wurde verteilt und kann nicht gelöscht werden
pers_personnel_sendError_dueTo=Lieferfehler Grund für den Fehler:
pers_personnel_sendError_reson={0} existiert in der Verbotsliste, bitte löschen und hinzufügen
#比对照片-样片示例
pers_examplePic_Tip=Sollte die folgenden Anforderungen erfüllen:
pers_examplePic_Tip1=1. die Hintergrundfarbe ist reines Weiß und das Personal trägt dunkle Kleidung;
pers_examplePic_Tip2=2. Elektronische Fotos liegen im JPG-, PNG- und JPEG-Dateiformat vor. Der empfohlene Pixelbereich ist: 480*640 < Pixel < 1080*1920;
pers_examplePic_Tip3=3. Die Porträts in elektronischen Fotos sollten die Augen öffnen und geradeaus schauen und darauf achten, dass die Pupillen deutlich sichtbar sind;
pers_examplePic_Tip4=4. Das Porträt im elektronischen Foto sollte einen neutralen Ausdruck haben und Sie können lächeln, aber Sie sollten Ihre Zähne nicht zeigen;
pers_examplePic_Tip5=5. Das Porträt im elektronischen Foto sollte klar sein, mit natürlichen Farben, satten Ebenen und ohne offensichtliche Verzerrung. Keine Schatten, Glanzlichter oder Reflexionen auf Porträtgesichtern oder -hintergründen; Kontrast und Helligkeit sind angemessen.
pers_examplePic_description=Richtiges Beispiel
pers_examplePic_error=Fehlerbeispiel:
pers_examplePic_error1=Übertriebener Gesichtsausdruck (übermäßiges Lächeln)
pers_examplePic_error2=Das Licht ist zu dunkel
pers_examplePic_error3=Das Gesicht ist zu klein (Auflösung ist zu klein)
pers_applogin_enabled=App-Anmeldung aktivieren
pers_applogin_disable=App-Anmeldung deaktivieren
pers_applogin_status=Status der App-Anmeldung aktivieren