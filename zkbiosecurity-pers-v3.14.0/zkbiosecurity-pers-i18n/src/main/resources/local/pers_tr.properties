#pers common.
#here other module can also use the label from pers.
pers_module=Personel
pers_common_addPerson=Personel Ekle
pers_common_delPerson=Personeli Sil
pers_common_personCount=Personel Sayısı
pers_common_browsePerson=Personele Göz At
#左侧菜单
pers_person_manager=Personel
pers_person=Kişi
pers_department=Departman
pers_leave=Görevden Alınan Personel
pers_tempPerson=Geçici Personel
pers_attribute=Özel Özellikler
pers_card_manager=Kart Yönetimi
pers_card=Kart
pers_card_issue=Verilen Kart Kaydı
pers_wiegandFmt=Wiegand Formatı
pers_position=Pozisyon
#人员
pers_person_female=Kadın
pers_person_male=Erkek
pers_person_pin=Personel ID
pers_person_departmentChange=Departmanı Ayarla
pers_personDepartment_changeLevel=Bölüm değiştirme izni
pers_person_gender=Cinsiyet
pers_person_detailInfo=Personel Detayı
pers_person_accSet=Erişim Kontrol
pers_person_accSetting=Erişim Kontrol Ayarı
pers_person_attSet=Zaman Kontrol
pers_person_eleSet=Asansör Kontrol
pers_person_eleSetting=Asansör Kontrol Ayarı
pers_person_parkSet=Plaka Kaydı
pers_person_pidSet=Personel Sertifikası
pers_person_insSet=FaceKiosk
pers_person_aiSet=Face Intellect
pers_person_payrollSet=Maaş Ayarı
pers_person_psgSet=Geçiş Ayarı
pers_person_lockerSet=Dolap Ayarı
pers_person_sisSet=Güvenlik kontrol ayarları
pers_person_vdbSet=Görsel interom ayarları
pers_person_firstName=İsim
pers_person_lastName=Soyad
pers_person_name=İsim
pers_person_wholeName=Ad
pers_person_fullName=İlk Orta Son
pers_person_cardNum=Tutulan Kart Sayısı
pers_person_deptNum=İlgili departman sayısı
pers_person_dataCount=İstatistikler
pers_person_regFinger=Parmak İzi
pers_person_reg=Kayıt Ol
pers_person_password=Cihaz Doğrulama Parolası
pers_person_personDate=Çalışma Tarihi
pers_person_birthday=Doğum Günü
pers_person_mobilePhone=Mobil Telefon
pers_person_personDevAuth=Kişi ve Cihaz İzinleri
pers_person_email=Email
pers_person_browse=Araştır
pers_person_authGroup=Erişim Seviyeleri
pers_person_setEffective=Etkili Zamanı Ayarla
pers_person_attArea=Katılım Alanı
pers_person_isAtt=Katılım Hesaplaması
pers_person_officialStaff=Resmi Kadro
pers_person_probationStaff=Denetimli Serbestlik Personeli
pers_person_identity_category=Personel Tipi
pers_person_devOpAuth=Cihaz İşlem Rolü
pers_person_msg1=Gerçek kart numarası ve site kodu aynı anda doldurulmalıdır!
pers_person_msg2=Lütfen 3-4 basamak girin.
pers_person_msg3=Format hatası!
pers_person_imgPixel=(Önerilen boyut 120*140).
pers_person_cardLengthDigit=Lütfen numarayı girin.
pers_person_cardLengthHexadecimal=Lütfen numaraları ya da abcdef harfleri girin!
pers_person_to=İçin
pers_person_templateCount=Parmak İzi Miktarı
pers_person_biotemplateCount=Biyolojik Şablon Adeti
pers_person_regFace=Yüz
pers_person_regVein=Parmak Damar
pers_person_faceTemplateCount=Yüz Adeti
pers_person_VeinTemplateCount=Parmak Damarı Adeti
pers_person_palmTemplateCount=Avuç İçi Adeti
pers_person_cropFace=Fotoğraf
pers_person_cropFaceCount=Yüz Resmi Miktarı
pers_person_faceBiodataCount=Görünür Yüz Adeti
pers_person_irisCount=Iris sayısı
pers_person_batchToDept=Yeni Departman
pers_person_changeReason=Aktarım Nedeni
pers_person_selectedPerson=Seçilmiş Kişi
pers_person_duressPwdError=Tekrarlanan Şifre
pers_person_completeDelPerson=Sil
pers_person_recover=Kurtar
pers_person_nameNoComma=Virgül içermemelidir.
pers_person_firstNameNotEmpty=Ad boş olmamalıdır.
pers_person_lastNameNotEmpty=Soyadı boş olmamalıdır.
pers_person_mobilePhoneValidate=Geçerli bir cep telefonu numarası girin.
pers_person_phoneNumberValidate=Geçerli bir telefon numarası giriniz.
pers_person_postcodeValidate=Lütfen geçerli bir posta kodu girin.
pers_person_idCardValidate=Lütfen geçerli bir kimlik kartı numarası girin.
pers_person_pwdOnlyLetterNum=Parola yalnızca harf veya rakam içerebilir.
pers_person_certNumOnlyLetterNum=Sertifika Numarası yalnızca harf veya rakam içerebilir.
pers_person_oldDeptEqualsNewDept=Ayarladığınız yeni departman, orijinal departmanla aynı olmamalıdır.
pers_person_disabled=Erişim Kapalı
pers_person_emailError=Lütfen geçerli bir e-posta adresi girin
pers_person_driverPrompt=Lütfen aygıt sürücüsünü yükleyin! Sürücüyü indirmek için Tamam'a tıklayın.
pers_person_readIDCardFailed=Kaydırma hatası!
pers_person_cardPrompt=Kimlik kartını bölgeye sakla ...
pers_person_iDCardReadOpenFailed=ID kart okuyucu algılanmadı!
pers_person_iDCardNotFound=Kimlik kartı bulunamadı, lütfen tekrar deneyin!
pers_person_nameValid=Çince, İngilizce, sayılar, '-', '_'. destekler
pers_person_nameValidForEN=İngilizce, sayılar, '-', '_', '.'. destekler.
pers_person_pinPrompt=Lütfen İngiliz alfabesindeki karakterleri, sayıları girin.
pers_person_pinSet=Personel ID Ayarı
pers_person_supportLetter=Destek Mektupları
pers_person_cardsSupport=Kişi Başına Birden Çok Kart
pers_person_SupportDefault=Personel ID otomatik oluştur
pers_person_noSpecialChar=İsim özel karakterler giremez!
pers_person_pinInteger=Lütfen rakamları girin.
pers_pin_noSpecialChar=Personel numarasında özel karakter olamaz!
pers_op_capture=Yakala
pers_person_cardDuress=Tekrarlanan Kart Numarası
pers_person_pwdException=Şifre İstisnası
pers_person_systemCheckTip=Doğru format için personel bilgilerini kontrol edin!
pers_person_immeHandle=Hemen Halledin
pers_person_cardSet=Kart Ayarı
pers_person_tempPersonSet=Geçici Personel Ayarı
pers_person_cardsReadMode=Kart Okuma Modu
pers_person_cardsReadModeReadHead=Denetleyiciyle Oku
pers_person_cardsReadModeID180=Oku ID180
pers_person_IDReadMode=Kimlik Kartı Okuma Modu
pers_person_IDReadModeIDCardReader=ID Kart Okuyucu
pers_person_IDReadModeTcpReadHead=TCP/IP Okuyucu
pers_person_physicalNo=ID Kartı Fiziksel Kart Numarası
pers_person_physicalNoToCardNo=Kimlik kartı fiziksel kart numarasını kullan
pers_person_ReadIDCard=ID kartı oku
pers_person_templateBioTypeNumber=Biyometrik Şablon Türü Numarası
pers_person_templateValidType=Biyometrik Şablon Geçerliliği
pers_person_templateBioType=Biyometrik Şablon Türü
pers_person_templateVersion=Biyometrik Şablon Sürümü
pers_person_template=Biyometrik Şablon
pers_person_templateNo=Biyometrik Şablon No.
pers_person_templateNoIndex=Biyometrik Şablon Dizini
pers_person_templateDataUpdate=Biyometrik şablon mevcut olduğunda verilerin güncellenmesi:
pers_person_templateValidTypeNoNull=Biyometrik şablon geçerliliği boş olmamalıdır!
pers_person_templateBioTypeNoNull=PersonelKimliği: {0}, Biyometrik şablon türü boş olmamalıdır!
pers_person_templateVersionNoNull=Personel Kimliği: {0}, Biyometrik şablon sürümü boş olmamalıdır!
pers_person_templateNoNull=Personel Kimliği: {0}, Biyometrik şablon boş olmamalıdır!
pers_person_templateNoNoNull=Personel Kimliği: {0}, Biyometrik şablon No. boş olmamalıdır!
pers_person_templateNoIndexNoNull=Personel Kimliği: {0}, Biyometrik şablon dizini boş olmamalıdır!
pers_person_templateError=Personel Kimliği: {0}, Biyometrik şablon yanlış!
pers_person_bioDuress=Zorlama
pers_person_universal=Genel
pers_person_voice=Ses izi
pers_person_iris=Iris
pers_person_retina=Retina
pers_person_palmPrints=Avuç İçi
pers_person_metacarpalVein=Avuç İçi Damarı
pers_person_visibleFace=Görünür Yüz
pers_person_pinError={0} personel kimliği mevcut değil, bu veriler işlenemiyor!
pers_person_pinException=Personel Kimliği İstisnası
pers_person_pinAutoIncrement=Personel Kimliği Otomatik artış
pers_person_resetSelfPwd=Kendi Kendine Giriş Parolasını Sıfırla
pers_person_picMaxSize=Görüntü çözünürlüğü çok yüksek ve çözünürlük {0} altında önerilir.
pers_person_picMinSize=Görüntü çözünürlüğü çok düşük ve çözünürlüğün {0} üstünde olması önerilir.
pers_person_cropFaceShow=Yüz Resmini Görüntüle
pers_person_faceNoFound=Makedonya
pers_person_biometrics=Biyometri Türü
pers_person_photo=Personel Resimleri
pers_person_visibleFaceTemplate=Akıllı Yüz Şablosu
pers_person_infraredFaceTemplate=Yakın Kızılötesi Yüz Şablonu
pers_person_delBioTemplate=Biyometrik Verileri Sil
pers_person_delBioTemplateSelect=Lütfen silinecek biyolojik şablonu seçin!
pers_person_infraredFace=Yakın Kızılötesi Yüz
pers_person_notCard=Kartı içermez
pers_person_notRegFinger=Parmak izi içermez
pers_person_notMetacarpalVein=Avuç içi damarını içermez
pers_person_notRegVein=Parmak damarlarını içermez
pers_person_notIris=Iris dışında
pers_person_notInfraredFaceTemplate=Kızılötesine yakın yüz şablonunu içermez
pers_person_notVisibleFaceTemplate=Görünür ışık yüzü şablonunu içermez
pers_person_notVisibleFacePhoto=Görünür yüz fotoğrafları içermez
pers_person_visibleFacePhoto=Görünür yüz fotoğrafı
pers_person_change=Personel ayarlamaları
pers_person_cropFaceUsePhoto=Karşılaştırma fotoğrafını avatar olarak göstermek ister misiniz?
pers_person_photoUseCropFace=Karşılaştırma fotoğrafı oluşturmak için avatar ı kullanmak ister misiniz?
pers_person_selectCamera=Yakalama kamerasını seç
pers_person_delCropFaceMsg=Silecek karşılaştırma fotoğrafları yok!
pers_person_disabledNotOp=İnsan etkisiz olmuş ve çalışamaz!
pers_person_visiblePalm=Görünüşen ışık palm ı
pers_person_notVisiblePalm=Görünüşe göre ışık palmanı dahil etmiyor
pers_person_selectDisabledNotOp=Seçilmiş personel içinde çalışmayacak özgür personel var!
pers_person_photoUseCropFaceAndTempalte=Do you want to use avatars to generate comparison photos and facial templates?
pers_person_photoUseTempalte=Yüz örnekleri oluşturmak için fotoğrafları kullanmak ister misiniz?
pers_person_createCropFace=Karşılaştırma fotoğrafları oluştur
pers_person_createFaceTempalte=Yüz şablonları oluştur
pers_person_faceTempalte=Facial Şablonlar
pers_person_extractFaceTemplate=Yüz şablonları çıkarılıyor
pers_person_createSuccess=Başarılı üretildi
pers_person_createFail=Generation failed
pers_person_serverConnectWarn=Sunucu adresi, kullanıcı adı ve parola boş olamaz!
pers_person_serverOffline=Çevrimli yüz şablonu çıkarma sunucusu
pers_person_faceTemplateError1=Face detection failed
pers_person_faceTemplateError2=Facial closure
pers_person_faceTemplateError3=Yeterince açık
pers_person_faceTemplateError4=Yüz açısı çok büyük
pers_person_faceTemplateError5=Canlı tanıma başarısız oldu
pers_person_faceTemplateError6=Facial şablon çıkarması başarısız oldu
pers_person_cropFaceNoExist=Karşılaştırma fotoğrafı yok.
pers_person_disableFaceTemplate=Yüz şablonu çıkarma fonksiyonu etkinleştirilmedi, şablonu çıkarmadı!
pers_person_cropFacePhoto=Yüzü Karşılaştırma Fotoğrafı
pers_person_vislightPalmPhoto=Palm Karşılaştırma Fotoğrafı
pers_person_serverOfflineWarn=Yüz şablonu çıkarma sunucusu devre dışı ve bu özelliği etkinleştiremez!
pers_person_serverConnectInfo=Lütfen yüz şablonu çıkarma sunucusu online olup olmadığını test edin.
pers_person_notModified=Cep telefonu numarası değiştirilemez.
pers_person_syncAcms=Personeli ACMS ile senkronize edin
pers_person_startUpdate=Personel bilgilerini güncellemeye başlayın.
pers_person_updateFailed=Personel bilgilerinin güncellenmesi başarısız oldu!
#控制器发卡
pers_person_readCard=Cihazdan Sayı Kartı
pers_person_stopRead=Sorunu Durdur
pers_person_chooseDoor=Kapıyı Seç
pers_person_readCarding=Kart davası, lütfen sonra tekrar deneyin!
#抓拍照片
pers_capture_catchPhoto=Fotoğrafı Yakala
pers_capture_preview=Önizleme
#部门
pers_dept_entity=Departman
pers_dept_deptNo=Departman Numarası
pers_dept_deptName=Departman Adı
pers_dept_parentDeptNo=Veli Bölüm Numarası
pers_dept_parentDeptName=Veli Bölüm Adı
pers_dept_parentDept=Ana Departman
pers_dept_note=Yeni departman listede görünmüyorsa, kullanıcıyı departmanı düzenlemesi için yeniden yetkilendirmek üzere lütfen yöneticiye başvurun!
pers_dept_exit=İçeren
pers_dept_auth=Bağlı sistem kullanıcısı
pers_dept_parentMenuMsg=Üst bölüm alt bölümle aynı şekilde ayarlanamaz.
pers_dept_initDept=Genel
pers_dept_deptMarket=Pazarlama Departmanı
pers_dept_deptRD=Geliştirme Departmanı
pers_dept_deptFinancial=Finans departmanı
pers_dept_nameNoSpace=Departman Adı boşluklarla başlayamaz veya bitemez.
pers_dept_nameExist=Departman adı zaten var
pers_dept_changeLevel=Bu bölümün seviyesine geçilip değiştirilmeyeceği
pers_dept_noSpecialChar=Departman numaraları özel karakterler içeremez!
pers_dept_NameNoSpecialChar=Bölüm adları özel karakterler içeremez!
pers_dept_noModifiedParent=Üst bölüm değiştirilemez!
#职位
pers_position_entity=Pozisyon
pers_position_code=Pozisyon Numarası
pers_position_name=Pozisyon Adı
pers_position_notExist=Pozisyon mevcut değil!
pers_position_sortNo=Çeşit
pers_position_parentName=Ana Konumu
pers_position_parentCode=Üst Konum Numarası
pers_position_batchToPosition=Yeni Pozisyon
pers_position_nameExist=Bu konum adı zaten var
pers_position_change=Pozisyon Değiştir
pers_position_parentMenuMsg=Üst konum alt konum olarak ayarlanamaz.
pers_position_nameNoSpace=Konum adı boşluklarla başlayamaz veya bitemez.
pers_position_existSub={0}: Alt yazılar içeriyor silinemez
pers_position_existPerson={0}: Personel silinemez
pers_position_importTemplate=İş içe aktarma şablonu
pers_position_downloadTemplate=İçe aktarma şablonunu indirin
pers_position_codeNotEmpty=Pozisyon numarası boş olamaz
pers_position_nameNotEmpty=Pozisyon adı boş olamaz
pers_position_nameNoSpecialChar=Konum adı {0} özel karakterler içeremez!
pers_position_noSpecialChar=Konum numarası {0} özel bir karakter olamaz!
pers_position_codeLength=Konum numarası {0} 30 haneyi aşıyor
pers_position_nameLength=Konum adı {0}, {1} 'den uzun olan veriler
pers_position_codeExist={0} iş kimliği zaten mevcut
#证件
pers_cert_type=Sertifika Türü
pers_cert_number=Sertifika Numarası
pers_cert_name=Sertifika Adı
pers_cert_numberExist=Sertifika numarası zaten var.
#导出
pers_export_allPersPerson=Tüm Kişiler
pers_export_curPersPerson=Mevcut Kişi
pers_export_template=Şablonu Dışa Aktar
pers_export_personInfo=Personeli Dışa Aktar
pers_export_personInfoTemplate=Personel İçe Aktarma Şablonunu İndir
pers_export_personBioTemplate=Biyometrik ŞAblonu Dışa Aktar
pers_export_basicInfo=Temel Bilgiler
pers_export_customAttr=Özel Özellikler
pers_export_templateComment=Alan adı, birincil anahtar? Benzersiz? Boş değerlere izin ver? ({0}, {1}, {2}, {3})
pers_export_templateFileName=Personel İçe Aktarma Şablonu
pers_export_bioTemplateFileName=Personel Biyometrik Şablonu
pers_export_deptInfo=İhracat Departmanı
pers_export_deptTemplate=Departman İçe Aktarma Şablonunu İndirin
pers_export_deptTemplateFileName=Departman İçe Aktarma Şablonu
pers_export_personPhoto=Personel fotoğrafını içe aktar
pers_export_allPhotos=Tüm Fotoğraflar (tüm kişileri seç)
pers_export_selectPhotoToExport=Personel fotoğraflarını dışa aktarmak için başlangıç ​​kimliğini ve bitiş kimliğini seçin.
pers_export_fromId=ID'den
pers_export_toId=İçin
pers_export_certNumberComment=Sertifika numarası doldurulduktan sonra sertifika türü gereklidir
pers_export_templateCommentName=Alan adı: ({0})
pers_export_dataExist=Lütfen içe aktarılan verilerin sistemde zaten mevcut olduğundan emin olun
pers_export_cardNoTip=& ile bölünen çoklu kart numaraları
pers_carNumber_importTip=Plaka numarası (çoklu plakalar& ayrılmış)
#导入
pers_import_certNumberExist={0} sertifika numarası zaten var.
pers_import_complete=Tamamlyanız
pers_import_password=Kişisel Şifre
pers_import_fail={0} satırı başarısız oldu: {1}
pers_import_overData={0} kişiyi içe aktarıyorsunuz; sistemi sadece 30.000 kişinin ithalatını destekleyebilir!
pers_import_pinTooLong={0} personel kimliği çok uzun!
pers_import_pinExist={0} personel kimliği zaten var!
pers_import_pinIsRepeat={0} personel kimliği tekrarlandı!
pers_import_pinError=Personel kimliği {0} hatası!
pers_import_pinSupportNumber=Personel numarası sadece sayıları destekler! Personel numarası: {0}
pers_import_pinNotSupportNonAlphabetic=Personel kimliği İngilizce olmayan harfleri desteklemez! Personel numarası: {0}
pers_import_pinNotNull=Personel ID tamamen sıfır olmamalıdır!
pers_import_pinStartWithZero=Personel kimliği sıfır ile başlayamaz!
pers_import_cardNoNotSupportLetter=Kart numarası harfleri desteklemiyor!
pers_import_cardNoNotNull=Kart numarası sıfır olamaz!
pers_import_cardNoStartWithZero=Kart numarası sıfır ile başlayamaz!
pers_import_cardTooLong={0} kart numarası çok uzun!
pers_import_cardExist={0} kart numarası zaten var!
pers_import_personPwdOnlyNumber=Kişisel şifre sadece rakamları destekler (harf yok)!
pers_import_personPwdTooLong=Kişisel şifre {0} çok uzun!
pers_import_personDuressPwd=Kişisel şifre {0} tekrarlandı!
pers_import_emailTooLong={0} e-postası çok uzun!
pers_import_nameTooLong={0} adı çok uzun!
pers_import_genderError=Cinsiyet biçimi hatası!
pers_import_personDateError=İşe alım tarihi biçimi hatası!
pers_import_createTimeError=Saat formatı hatası!
pers_import_phoneError=Telefon numarası biçimi hatası!
pers_import_phoneTooLong=Telefon numarası uzunluğu 20 karakterden uzun olamaz!
pers_import_emailError=Email format hatası!
pers_import_birthdayError=Doğum tarihi hatası!
pers_import_nameError=Soyadı ve Adı özel simgeler içeremez! Personel numarası: {0}
pers_import_firstnameError=Soyadı veya adı ',' içeremez!
pers_import_firstnameNotNull=Ad boş olmamalıdır!
pers_import_dataCheck=Veri kontrolü bitti
pers_import_dataSaveFail=Veri içe aktarılamadı!
pers_import_allSucceed=Tüm veriler başarıyla içe aktarıldı!
pers_import_result=Başarılı: {0}, Başarısız: {1}.
pers_import_result2=İçe Aktarma Sonucu
pers_import_result3=Başarılı: {0}, Güncellendi: {1}, Başarısız: {2}.
pers_import_notSupportFormat=Bu Biçim desteklenmiyor!
pers_import_selectCorrectFile=Lütfen doğru dosyayı seçin.
pers_import_fileFormat=Dosya formatı
pers_import_targetFile=Hedef dosyası
pers_import_startRow=Üstbilgi Başlangıç ​​Satırları
pers_import_startRowNote=Veri formatının ilk satırı tablo adı, ikinci satırı başlık, üçüncü satırı içe aktarma verileri, lütfen dosyayı kontrol edip içe aktarın.
pers_import_delimiter=Sınırlayıcı
pers_import_importingDataFields=Veritabanı Alanları
pers_import_dataSourceFields=Veri alanlarını içe aktarma
pers_import_total=Toplam
pers_import_dataUpdate=Sistemdeki mevcut Personel Kimliğini güncelleyin:
pers_import_dataIsNull=Dosyada veri yok!
pers_import_deptNotExist=Departman mevcut değil!
pers_import_deptIsNotNull=Departman adı boş bırakılamaz!
pers_import_pinNotEmpty=Personel kimliği boş bırakılamaz!
pers_import_nameNotEmpty=Personel adı boş bırakılamaz!
pers_import_siteCodeOnlyLetterNum=Site kodu biçimi hatası.
pers_import_cardNoFormatErrors={0} kart numarasının biçimi yanlış.
pers_import_cardsNotSupport=[Kişi Başına Birden Çok Kart] özelliği devre dışıdır, kişi başına birden fazla kart programlayamaz.
pers_import_personInfo=Personel İçe Aktar
pers_import_commentFormat=Yorum biçimi doğru değil!
pers_import_noComment={0} satırındaki ve {1} sütunundaki veriler yorumlanmaz.
pers_import_fieldRepeat={0} satırındaki ve {1} sütundaki veriler, yorumdaki alan adı tekrarlanır: {2}
pers_import_primaryKey=Birincil anahtar olarak en az bir alan olmalıdır.
pers_import_templateIsRepeat=Personel Kimliği: {0} için biyo-şablon verileri kopyalandı!
pers_import_biologicalTemplate=Biyometrik Şablonu İçe Aktar
pers_import_uploadFileSuccess=Yükleme başarılı, verileri ayrıştırmaya başlıyor, lütfen bekleyin ...
pers_import_resolutionComplete=Veri, veritabanı güncellemeleri başlatılarak ayrıştırıldı.
pers_import_mustField=İçe aktarılan dosya {0} sütununu içermelidir.
pers_import_bioTemplateSuccess=Biyometrik şablonu içe aktarın, bu verilerin her iş modülünden cihaza manuel olarak senkronize edilmesi gerekir.
pers_import_personPhoto=Personel Fotoğrafını İçe Aktar
pers_import_opera_log=Operasyonu Kaydı
pers_import_error_log=Hata Kaydı
pers_import_uploadFileSize=Lütfen {0} boyutundan büyük olmayan bir dosya yükleyin!
pers_import_uploadFileSizeLimit=Tek bir içe aktarma için lütfen 500MB'den büyük olmayan bir dosya yükleyin!
pers_import_type=İçe Aktarma Modu
pers_import_photoType=Resim
pers_import_archiveType=Sıkıştırılmış paket
pers_import_startUpload=Yüklemeye Başla
pers_import_addMore=Daha Fazla Ekle
pers_import_photoQuality=Fotoğraf Adeti
pers_import_original=Orjinal
pers_import_adaptive=Adaptif
pers_import_adaptiveSize=(Boyut 480*640)
pers_import_totalNumber=Toplam
pers_import_uploadTip=(Yüklerken lütfen fotoğrafı silmeyin)
pers_import_addPhotoTip=Yüklemek için lütfen fotoğraf ekleyin!
pers_import_selectPhotoTip=Lütfen yüklemek istediğiniz fotoğrafı seçin!
pers_import_uploadResult=Fotoğraf Sonucu Yükle
pers_import_pleaseSelectPhoto=Lütfen Fotoğraf Seçin
pers_import_multipleSelectTip=Birden fazla seçim yapmak için Ctrl tuşuna basın
pers_import_replacePhotoTip=Seçilen fotoğraf zaten yükleme listesinde, değiştirmek istiyor musunuz?
pers_import_photoNamePinNotCorrespond=Fotoğraf adı ve personel kimliği uyuşmazlığı
pers_import_photoFormatRequirement=Lütfen fotoğrafı personel kimliğiyle adlandırın. Doğru biçim JPG/PNG'dir.Fotoğraf adının özel karakterler içermediğinden emin olun.
pers_import_filterTip=Seçtiğiniz bazı fotoğraflar önizlenemiyor ve aşağıdaki nedenler olabilir:
pers_import_photoContainSpecialCharacters=Fotoğraf adında özel karakterler var.
pers_import_photoFormatError=Fotoğraf biçimi yanlış.
pers_import_photoSelectNumber=Tek bir içe aktarımda 3000'den fazla resim seçmeyin!
pers_import_photoSelectNumberLimit={0} taneden fazla resim seçmeyin!
pers_import_fileMaxSize=Resim çok büyük, lütfen 5MB'den küçük bir resim dosyası yükleyin.
pers_import_notUploadPhotoNumber=3000'den fazla resim yüklenemez!
pers_import_zipFileNotPhoto=Zip dosyasında kişinin fotoğrafı yok, lütfen tekrar seçin ve içe aktarın!
pers_import_personPlateRepeat=Personel plakası {0} kopya!
pers_import_filePlateRepeat={0} plaka içindeki dosyayı kopyalayın!
pers_import_personPlateFormat=Personel plaka {0} biçimi yanlış!
pers_import_personPlateMax=Personel plaka sayısı maksimum 6'yı aşıyor!
pers_import_cropFaceFail=Karşılaştırma fotoğrafı oluşturulamadı!
pers_import_pinLeaved=Personel kimliği: {0} ayrıldı.
pers_import_exceedLicense=Yazılım lisansını aşan kişi sayısı için ithalat yapılmasına izin verilmez.
pers_import_bioTemplateNotNull=Personel Kimliği, Biyometrik Şablon Türü, Kimlik veya Dizin ve İçerik, Versiyonun boş olmasına izin verilmez!
pers_import_certTypeNotNull=Personel numarası: {0} Belge türü boş olamaz
pers_import_certTypeNotExist=Kişisel numarası: {0} Belge tipi yok
pers_import_certNumNotNull=Şahsiy numarası: {0} Sertifik numarası boş olamaz
pers_import_certNumberTooLong=Satır {0}: {1} kimlik numarası çok uzun!
pers_import_idNumberErrors=Satır {0}: {1} kimlik kartı numarası hatalı!
pers_import_emailErrors=Satır {0}: E-posta adresi {1} biçim hatası!
pers_import_emailIsExist={0} e-posta adresi zaten var!
pers_import_emailIsRepeat=Satır {0}: {1} dosyasının dahili e-posta adresi tekrarlandı!
pers_import_fileMobilePhoneRepeat=Satır {0}: {1} dosyasının dahili cep telefonu numarası tekrarlandı!
pers_import_mobilePhoneErrors=Cep telefonu numarası biçimlendirme hatası!
pers_import_hireDateError=Giriş tarihinin format ı yanlış!
pers_import_selectPhotoType=Lütfen import fotoğrafının türünü seçin!
pers_import_hireDateLaterCurrent=Unutma tarihi şu tarihinden sonra olamaz!
pers_import_buildingNotExist=Bina mevcut değil!
pers_import_unitNotExist=Birim mevcut değil!
pers_import_vdbInfoFail={0} binasında {1} adlı birim bilgisi yok!
pers_import_vdbBuildingFail={0} biriminin bina bilgisi boş olamaz!
pers_import_vdbRoomNoFail=Oda numarası 0'dan büyük bir sayı olmalıdır!
#人员离职
pers_person_leave=İşten Çıkarma
pers_dimission_date=İşten Çıkarılma Tarihi
pers_dimission_type=İşten Çıkarma Türü
pers_dimission_reason=İşten Çıkarma Nedeni
pers_dimission_volutary=Gönüllü Artıklık
pers_dimission_dismiss=İşten Ayrılma
pers_dimission_resignat=İstifa
pers_dimission_shiftJob=Transfer
pers_dimission_leave=İşi Maaşsız Tut
pers_dimission_recovery=Eski Görevini Ver
pers_dimission_sureToRecovery=Eski durumuna getirme işlemini gerçekleştirmek istediğinizden emin misiniz?
pers_dimission_backCard=Kart iade edildi mi?
pers_dimission_isForbidAction=Erişim kontrol hakları hemen devre dışı bırakılsın mı?
pers_dimission_writeInfomation=İşten çıkarma bilgilerini girin
pers_dimission_pinRetain=İşten çıkarılan çalışan için personel kimliği tutulmalı mı?
pers_dimission_downloadTemplate=Reddetme İçe Aktarma Şablonunu İndir
pers_dimission_import=İçe Aktarma İşlemleri
pers_dimission_importTemplate=İçe aktarma şablonunu reddet
pers_dimission_date_noNull=İhraç tarihi boş olamaz
pers_dimission_leaveType_noExist=Dismission türü mevcut değil
pers_dimission_dateFormat=Zorunlu alan, zaman biçimi yyyy-MM-dd şeklindedir, örneğin: 2020-07-22
pers_dimission_leaveType=Zorunlu alan, örneğin: Gönüllü Artıklık, İşten Ayrılma, İstifa, Transfer
pers_dimission_forbidden=Devre dışı
pers_dimission_leaveType_noNull=İzin türü boş olamaz!
pers_dimission_person_noExist=Kurtarma kişi yok!
pers_dimission_date_error=Gönderme tarihi yanlış ya da format ı doldurulmadı
#临时人员
pers_tempPerson_audit=Gözden Geçir
pers_tempPerson_view=Görünüm
pers_tempPerson_waitReview=Yöneticinin incelemesini bekliyor
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Kart Numarası
pers_card_state=Kart Durumu
pers_card_effect=Etkin
pers_card_disabled=Geçersiz
pers_card_past=Süresi Doldu
pers_card_back=Kart İade Edildi
pers_card_change=Kart Değişimi
pers_card_note=Kart numarası zaten var.
pers_card_numTooBig=Orijinal kart numarasının uzunluğu çok uzun.
pers_issueCard_entity=Sorun Kartı
pers_issueCard_operator=Operatör
pers_issueCard_operate=Aksiyon
pers_issueCard_note=Kayıtlı personel verileri için faaliyet gösteren kartlar, ancak kayıt kartı hesap personeli için değil!
pers_issueCard_date=Çıkış Kartı Tarihi
pers_issueCard_changeTime=Zamanı değiştir
pers_issueCard_cardValidate=Boşluğa izin verilmiyor.
pers_issueCard_cardEmptyNote=Kart boş olmamalıdır.
pers_issueCard_cardHasBeenIssued=Bu kart verildi!
pers_issueCard_noCardPerson=Çıkarılmamış kişi
pers_issueCard_waitPerson=Mevcut sayıdaki kişi
pers_issueCard_mc5000=MC5000 kart çıkış
pers_batchIssCard_entity=Toplu Kart Sorunu
pers_batchIssCard_startPersNo=Personel ID Başlat
pers_batchIssCard_endPersNo=Son Personel ID
pers_batchIssCard_issCardNum=Verilen Kart Sayısı
pers_batchIssCard_notIssCardNum=Kartı Çıkarmayan Kişi Sayısı
pers_batchIssCard_generateList=Liste Oluştur
pers_batchIssCard_startRead=Okumaya başlayın
pers_batchIssCard_swipCard=Kartın konumu
pers_batchIssCard_sendCard=Cihaz
pers_batchIssCard_dispenCardIss=USB Okuyucu
pers_batchIssCard_usbEncoder=USB Kodlayıcı
pers_batchIssCard_note=Personel Kimliği yalnızca giriş değerlerini destekler ve yalnızca kartı verilmemiş kişileri gösterir (en fazla 300)!
pers_batchIssCard_startPinEmpty=Başlangıç ​​Personel ID boş olamaz!
pers_batchIssCard_endPinEmpty=Son Personel Kimliği boş olmamalıdır!
pers_batchIssCard_startPinLargeThanEndPin=Başlangıç ​​Personeli ID, Bitiş Personeli ID'sinden daha büyük olamaz!
pers_batchIssCard_numberParagraphNoPerson=Kartı verilmiş olmayan kişi, baştan sona kimlik segment numarası içinde mevcut değil!
pers_batchIssCard_inputCardNum=Giriş Kartı Numarası
pers_batchIssCard_cardLetter=Kart harfleri desteklemiyor!
pers_batchIssCard_cardNoTooLong=Kart numarası uzunluğu çok uzun!
pers_batchIssCard_issueWay=Kart Kayıt Yöntemi
pers_batchIssCard_noPersonList=Personel listesi henüz oluşturulmadı.
pers_batchIssCard_startReadCard=Kart okumayı başlat
pers_batchIssCard_swipePosition=Pozisyon Değiştir
pers_batchIssCard_chooseSwipePosition=Lütfen kaydırma konumunu seçin.
pers_batchIssCard_readCardTip=Aygıt yalnızca kayıt yöntemi seçili bir okuyucu olduğunda kayıtlı olmayan kartı okur.
pers_batchIssCard_notIssCardNo=Düzenlenmemiş kart sayısı
pers_batchIssCard_totalNumOfCards=Toplam kart sayısı
pers_batchIssCard_acms=ACMS Kart İstasyonu
pers_lossCard_entity=Bildirilen Kayıp Kart
pers_lossCard_lost=Bu kart bildirildi, işlemi tekrarlayamıyor!
pers_losscard_note2=Yönetim kartını yazdıktan sonra, kartların cihazda etkisini kaybettiğinden emin olmak için bu kartı asansör okuyucusunda kaydırmanız gerekir.
pers_revertCard_entity=Kayıp Kartı Yeniden Etkinleştir
pers_revertCard_setReport=Lütfen önce kart kaybını bildirin!
pers_revertcard_note2=Yönetim kartını yazdıktan sonra, kartları tekrar kullanabileceğinizden emin olmak için bu kartı asansör okuyucusunda kaydırmanız gerekir.
pers_issueCard_success=Kart çıkarma başarılı!
pers_issueCard_error=Kart çıkarma hatası!
pers_cardData_error=Kart veri formatı istisnasını okuyun!
pers_analysis_error=Kart veri ayrıştırma istisnası!
pers_cardOperation_error=Kart işlemi istisnası!
pers_cardPacket_error=Kart işlemi komut paketi istisnası!
pers_card_write=Kart Yaz
pers_card_init=Kartı başlat
pers_card_loss=Kayıp Kart
pers_card_revert=Kartı geri alma
pers_card_writeMgr=Yönetim kartı yaz
pers_initCard_tip=Başlatma işleminden sonra kart boş bir kart haline gelecektir!
pers_initCard_prepare=Kartı başlatmaya hazır ...
pers_initCard_process=Kart başlatılıyor ...
pers_initCard_success=Kartı başarıyla başlat
pers_mgrCard_prepare=Yönetim kartı verileri yazmaya hazırlanın ...
pers_mgrCard_process=Yönetim kartı verileri yazıyor ...
pers_mgrCard_success=Yönetim kartı başarıyla yazıldı.
pers_userCard_prepare=Bir kullanıcı kartı yazmaya hazırlanın ...
pers_userCard_process=Kullanıcı kartı verileri yazılıyor ...
pers_userCard_success=Kullanıcı kartı yazma başarılı
pers_userCard_tip=Lütfen kişi düzenleme sayfasında başlangıç ​​saatini ve bitiş saatini ayarlayın ve ardından kart işlemini yazın.
pers_userCard_tip2=İzinler verileri boş, kartlar yazılamıyor.
pers_userCard_tip3=Yetki grubu ilişkili cihaz ikiden fazla birim, veri kaybı.
pers_writeCard_tip=Lütfen kodlayıcıyı bağladığınızdan ve sürücüyü yüklediğinizden emin olun ve kartı kodlayıcıya yerleştirin.
pers_writeMgrCard_tip=Kayıp kartların ve geri döndürülen kartların sayısı 18'den fazla olamaz
pers_writeMgrCard_tip2=Kayıp kart ve geri alma kartı sayısı:
pers_card_writeToMgr=Yönetim kartına yazılmıştır
pers_card_hex=Kart Format Ekranı
pers_card_decimal=Ondalık
pers_card_Hexadecimal=Onaltılık
pers_card_IssuedCommandFail=Verilen Komut Başarısız Oldu:
pers_card_multiCard=Daha Fazla Kart
pers_card_deputyCard=İkinci Kart
pers_card_deputyCardValid=Lütfen önce ana kartı girin!
pers_card_writePinFormat=Sistem, Kartlı Harfsiz Personel Kimliklerini yalnızca ilişkilendirebilir!
pers_card_notMoreThanSixteen=İkincil kartların miktarı 16'dan fazla olamaz.
pers_card_notDelAll=Tüm ikincil kartlar kaldırılamaz.
pers_card_maxCard=Kart numarası {0} 'ı aşamaz.
pers_card_posUseCardNo=Kişinin ana kartı tüketici modülü tarafından kullanılıyor. Kart çıkarma işlemini gerçekleştirmek için lütfen tüketici modülüne gidin!
pers_card_delFirst=Lütfen kartı çıkarmadan önce çıkarılmış kart numarasını silin!
pers_card_disablePersonWarn=Seçili kart numarası etkisiz kişileri içerir ve çalışamaz!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Alan Kodu
pers_wiegandFmt_wiegandMode=Mod
pers_wiegandFmt_wiegandModeOne=Mod Bir
pers_wiegandFmt_wiegandModeTwo=Mod İki
pers_wiegandFmt_isDefaultFmt=Oto
pers_wgFmt_entity=Wiegand Format
pers_wgFmt_in=Wiegand Giriş Formatı
pers_wgFmt_out=Wiegand Çıkış Formatı
pers_wgFmt_inType=Wiegand Giriş Tipi
pers_wgFmt_outType=Wiegand Çıkış Tipi
pers_wgFmt_wg=Wiegand Format
pers_wgFmt_totalBit=Toplam Bit
pers_wgFmt_oddPch=Tek Parite Kontrolü (o)
pers_wgFmt_evenPck=Çift Parite Kontrolü (e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=Tesis Kodu (f)
pers_wgFmt_siteCode=Site Kodları
pers_wgFmt_manufactoryCode=Üretici Kodu (m)
pers_wgFmt_firstParity=İlk Parite Kontrolü (p)
pers_wgFmt_secondParity=İkinci Parite Kontrolü (p)
pers_wgFmt_cardFmt=Kart Kontrol Formatı
pers_wgFmt_parityFmt=Eşlik Kontrol Formatı
pers_wgFmt_startBit=Başlangıç Bit
pers_wgFmt_test=Kart Format Testi
pers_wgFmt_error=Kart Formatı Hatası!
pers_wgFmt_verify1=Toplam bit 80'den fazla olmamalıdır!
pers_wgFmt_verify2=Çek kartı format uzunluğu toplam bit sayısına eşit olmalıdır!
pers_wgFmt_verify3=Eşlik biçimi uzunluğu, toplam basamak sayısına eşit olmalıdır!
pers_wgFmt_verify4=Başlatılan veriler silinemez!
pers_wgFmt_verify5=Kart formatı kullanılıyor, silinemiyor!
pers_wgFmt_verify6=İlk parite biti toplam sayısından fazla olamaz!
pers_wgFmt_verify7=İkinci parite biti toplam sayıdan fazla olamaz!
pers_wgFmt_verify8=Başlangıç ​​ve maksimum uzunluk formatı doğru değil!
pers_wgFmt_verify9=Kart kontrol formatı uzunluğu toplam sayısından fazla olamaz!
pers_wgFmt_verify10=Kart kontrol basamak fonksiyonu geçemez!
pers_wgFmt_verify11=Site kodu ayarlanan aralığı aşıyor!
pers_wgFmt_verify=Kontrol
pers_wgFmt_unverify=Kontrol Edilmedi
pers_wgFmt_atLeastDefaultFmt=Lütfen en az bir otomatik eşleşen kart biçimini saklayın!
pers_wgFmt_defaultFmtError1=Aynı kart numarası bitlerine sahip başka bir kart formatı vardır, otomatik olarak eşleşen kart formatına ayarlanamaz!
pers_wgFmt_defaultFmtError2=Otomatik eşleme için aynı kart formatının varlığı, işlem başarısız!
pers_wgFmt_cardFormats=Kart Formatları
pers_wgFmt_cardFormatTesting=Kart Formatları Testi
pers_wgFmt_checkIsUsed=Kart biçimi {0} içinde kullanılıyor ve silinemez!
pers_wgFmt_supportDigitsNumber=Lütfen aygıt tarafından desteklenmiş rakamlar sayısını girin
#选人控件
pers_widget_selectPerson=Personel Seç
pers_widget_searchType1=Sorgu
pers_widget_searchType2=Departman
pers_widget_deptHint=Not: Seçilen departmanların tüm personelinin alınması
pers_widget_noPerson=Hiçbir personel seçilmedi.
pers_widget_noDept=Lütfen departman seçiniz.
pers_widget_noDeptPerson=Seçilen departmanın altındaki kimse yok, lütfen yeniden seçin!
#人员属性
pers_person_carPlate=Plaka
pers_person_socialSecurity=Sosyal Güvenlik Numarası
pers_person_msg4=Maksimum uzunluk 20'yi geçmemelidir!
pers_person_msg5=İsim 50 karakterden fazla olamaz!
pers_person_type=Kişi Türü
pers_person_reseCode=Self-Login Giriş Şifresi
pers_person_IsSendMail=E-mail Bildirimi
pers_person_inactive=Pasif
pers_person_active=Aktif
pers_person_employee=Çalışan
pers_person_isSendMailMsg=[Olay Bildirimi] özelliğini kullanmak için önce e-postayı girmelisiniz.
pers_person_createTime=Oluşturma Zamanı
pers_person_pinFirstValid=Personel numarasının ilk karakteri 8 veya 9 olamaz.
pers_person_attrValueValid=Alanın değeri tekrarlanamaz.
pers_person_attrValueDelimiterValid=Ayırıcı ortada olmalıdır.
pers_person_isSendSMS=SMS Bildirimi
pers_person_building=Bina Adı
pers_person_unitName=Birim Adı
pers_person_roomNo=Oda Numarası
#动态属性
pers_attr_emp_type=Çalışan tipi
pers_attr_street=Cadde
pers_attr_nation=Millet
pers_attr_office_address=Ofis Adresi
pers_attr_postcode=Posta kodu
pers_attr_office_phone=Ofis Telefonu
pers_attr_home_phone=Ev Telefonu
pers_attr_job_title=İş ismi
pers_attr_birthplace=Doğum yeri
pers_attr_polit_status=Politik durum
pers_attr_country=Ülke
pers_attr_home_address=Ev Adresi
pers_attr_hire_type=Kiralama Türü
pers_attr_inContract=Sözleşmeli işçi
pers_attr_outContract=Sözleşmesiz Çalışan
#属性自定义
pers_attribute_attrName=Ekran adı
pers_attribute_attrValue=Özellik Değeri
pers_attribute_controlType=Giriş Tipi
pers_attribute_positionX=Satır
pers_attribute_positionY=Sütun
pers_attribute_showTable=Kişi Listesinde Göster
pers_attrDefini_deletemsg=Bu özellik kullanıldı. Silmek istediğinizden emin misiniz?
pers_attrDefini_reserved=Sistem ayrılmış alan adı.
pers_attrDefini_msg1=Maksimum uzunluk 30'dan fazla değil!
pers_attrDefini_msg2=Sütun satırları zaten var, lütfen başka bir konuma değiştirin!
pers_attrDefini_attrValue_split=';' kullanarak sınırlayın
pers_attrDefini_attrName=Özellik Adı
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=Özellik Kimliği
pers_attrDefini_select=Açılır Liste
pers_attrDefini_check=Çoklu Seçim
pers_attrDefini_radio=Tek Seçim
pers_attrDefini_text=Metin
pers_attrDefini_maxCol=En fazla 2 sütun.
pers_attrDefini_maxLimit=Özel özellikler maksimum sayıya ulaştı!
pers_attrDefini_modControlType=Giriş tipinin değiştirilmesi, sistemdeki tüm personel için mevcut saha verilerini temizler.
#leavePerson
pers_leavePerson_reinstated=Eski Görevini Ver
#opExample
pers_example_newRecode=Yeni kayıtların alınması
pers_example_allRecode=Tüm kayıtlara erişim
pers_custField_StatisticalType=İstatistik Türü
#人员参数修改
pers_param_isAudit=Geçici Personel Otomatik Denetimini Etkinleştir
pers_param_donotChangePin=Mevcut Personel Kimliği harf (ler) içeriyor, [Destek Mektupları] özelliğini devre dışı bırakamıyor.
pers_param_hexChangeWarn=Mevcut sistemde zaten kart numaraları var, kart formatı görüntüleme modunu değiştiremiyor.
pers_param_cardsChangeWarn=Sistemde birden fazla kartı olan bazı kişiler vardır, [Kişi Başına Birden Çok Kart] özelliğini devre dışı bırakamazlar.
pers_param_maxPinLength=Yeni Personel Kimliğinin uzunluğu, sistemdeki mevcut Personel Kimliğinin uzunluğundan az olamaz.
pers_param_pinBeyondDevLength=Cihazda sistem tarafından desteklenen maksimum Personel Kimliği uzunluğu {0} 'dır, lütfen {1}' den daha az bir tam sayı girin.
pers_param_cardBeyondDevLength=Cihazda sistem tarafından desteklenen maksimum kart numarası uzunluğu {0} 'dır, lütfen {1}' den daha az bir tam sayı girin.
pers_param_checkIsExistNoAudit=Mevcut sistemde gözden geçirilmemiş tescil ettirenler var ve otomatik incelemeye değiştirilemez!
pers_param_noSupportPinLetter=Sistemde Personel Kimliği harfleri içermeyen, [Destek Mektupları] özelliğini etkinleştiremeyen cihazları desteklemez.
pers_param_changePinLettersTip=Personel numarası otomatik artışı destekler, personel sayısı modlarını değiştiremez
pers_param_changePinIncrementTip=Kişi numarası desteği harf içerir, personel numarası modunu değiştiremez
pers_param_qrCode=Kurumsal QR kodu
pers_param_employeeRegistrar=Bulut çalışan kaydını etkinleştir
pers_param_downloadQRCodePic=QR kodu resmini indir
pers_param_qrCodeUrl=QR Kod URL'si
pers_param_qrCodeUrlCreate=Self Servis Kayıt
pers_param_qrCodeUrlHref=Sunucu adresi: Port
pers_param_pinSetWarn=Mevcut sistemde zaten personel var ve personel numarası modu değiştirilemez.
pers_param_selfRegistration=Kendi Kendine Kaydı Etkinleştir
pers_param_infoProtection=Kişisel hassas bilgi koruması
pers_param_infoProtectionWarnMsg=Kişisel hassas bilgi güvenliği koruma seçeneğini etkinleştirdikten sonra, bu modülde yer alan hassas kişisel veriler, adlar, kart numaraları, kimlik numaraları, fotoğraflar vb. dahil ancak bunlarla sınırlı olmamak üzere duyarsızlaştırılacak veya gizlenecektir.
pers_param_templateServer=Facial Şablon Çıkarma Sunucusu
pers_param_enableFacialTemplate=Facial Şablon Çıkarmasını etkinleştir
pers_param_templateServerAddr=Facial şablon çıkarma sunucusu adresi
pers_param_templateServerWarnInfo=When facial template extraction is enabled, when the facial template extraction server is online and the user verification is passed, personnel will default to extracting facial templates when comparing photos;  Yüz örnek çıkarma sunucusu devre dışı modunda olduğunda, yüz örneklerini çıkarmayın!
pers_param_templateServerWarnInfo1=Yüz şablonu çıkarmasını etkinleştirirken yüz şablonu çıkarmasını destekleyen bir cihaz bağlanmalı!
pers_param_templateServerOffline=Yüz şablonu çıkarma sunucusu devre dışı ve yüz şablonlarını çıkartamaz! Devam etmek ister misin?
pers_param_faceServer=Facial arka uç karşılaştırma hizmeti
pers_param_enableFaceVerify=Yüz arka uç karşılaştırmasını etkinleştir
pers_param_faceServerAddr=Yüz arka uç karşılaştırma hizmet adresi
pers_param_faceServerSecret=Yüz Arka Sahne Karşılaştırma Hizmet Anahtarı
#国籍
pers_person_nationality=Ülke/Bölge
pers_nationality_angola=Angola
pers_nationality_afghanistan=Afgan
pers_nationality_albania=Arnavutça
pers_nationality_algeria=Cezayir
pers_nationality_america=Amerika
pers_nationality_andorra=Andora
pers_nationality_anguilla=Anguillan
pers_nationality_antAndBar=Antigua ve Barbuda
pers_nationality_argentina=Arjantinli
pers_nationality_armenia=Ermenistan
pers_nationality_ascension=Ascension
pers_nationality_australia=Avustralyalı
pers_nationality_austria=Avusturya
pers_nationality_azerbaijan=Azerbaycan
pers_nationality_bahamas=Bahama
pers_nationality_bahrain=Bahreyn
pers_nationality_bangladesh=Bangladeş
pers_nationality_barbados=Barbados
pers_nationality_belarus=Belarus
pers_nationality_belgium=Belçika
pers_nationality_belize=Belizean
pers_nationality_benin=Beninese
pers_nationality_bermudaIs=Bermuda Is.
pers_nationality_bolivia=Bolivya
pers_nationality_botswana=Botsvana
pers_nationality_brazil=Brezilya
pers_nationality_brunei=Brunei
pers_nationality_bulgaria=Bulgaristan
pers_nationality_burkinaFaso=Burkine Faso
pers_nationality_burma=Burma
pers_nationality_burundi=Burundi
pers_nationality_cameroon=Kamerunlu
pers_nationality_canada=Kanada
pers_nationality_caymanIs=Cayman Adaları
pers_nationality_cenAfrRepub=Afrika Cumhuriyeti
pers_nationality_chad=Çadiyen
pers_nationality_chile=Şili
pers_nationality_china=Çin
pers_nationality_colombia=Kolombiyalı
pers_nationality_congo=Kongo Cumhuriyeti
pers_nationality_cookIs=Cook Is.
pers_nationality_costaRica=Kostarika
pers_nationality_cuba=Küba
pers_nationality_cyprus=Kıbrıs
pers_nationality_czechRep=Çek Cumhuriyeti
pers_nationality_denmark=Danimarka
pers_nationality_djibouti=Djiboutis
pers_nationality_dominicaRep=Dominik
pers_nationality_ecuador=Ekvator
pers_nationality_egypt=Mısır
pers_nationality_eISalvador=El Salvador
pers_nationality_england=İngiliz
pers_nationality_estonia=Estoyna
pers_nationality_ethiopia=Etiyopyalı
pers_nationality_fiji=Fijice
pers_nationality_finland=Finlandiya
pers_nationality_france=Fransa
pers_nationality_freGui=Fransız Guyanası
pers_nationality_gabon=Gabonlu
pers_nationality_gambia=Gambiya
pers_nationality_georgia=Gürcü
pers_nationality_germany=Alman
pers_nationality_ghana=Ganalı
pers_nationality_gibraltarm=Cebelitarık
pers_nationality_greece=Yunan
pers_nationality_grenada=Grenada
pers_nationality_guam=Guam
pers_nationality_guatemala=Guetelama
pers_nationality_guinea=Gine
pers_nationality_guyana=Guyana
pers_nationality_haiti=Haiti
pers_nationality_honduras=Honduras
pers_nationality_hungary=Macarca
pers_nationality_iceland=İzlanda
pers_nationality_india=Hindistan
pers_nationality_indonesia=Endonezya
pers_nationality_iran=İran
pers_nationality_iraq=Irak
pers_nationality_ireland=İrlandalı
pers_nationality_israel=İsrail
pers_nationality_italy=İtalya
pers_nationality_ivoryCoast=Lvoirian
pers_nationality_jamaica=Jamaika
pers_nationality_japan=Japon
pers_nationality_jordan=Sudan
pers_nationality_kenya=Kenya
pers_nationality_korea=Korece
pers_nationality_kuwait=Kuveyt
pers_nationality_kyrgyzstan=Kırgızistan
pers_nationality_laos=Laos
pers_nationality_latvia=Letonya
pers_nationality_lebanon=Lübnan
pers_nationality_lesotho=Mesotho
pers_nationality_liberia=Liberyalı
pers_nationality_libya=Libya
pers_nationality_liechtenstein=Liechtensteiner
pers_nationality_lithuania=Litvanyalı
pers_nationality_luxembourg=Lüksemburg
pers_nationality_madagascar=Madagaskar
pers_nationality_malawi=Malavi
pers_nationality_malaysia=Malezya
pers_nationality_maldives=Maldivler
pers_nationality_mali=Mali
pers_nationality_malta=Malta
pers_nationality_marianaIs=Mariana mı?
pers_nationality_martinique=Martinik
pers_nationality_mauritius=Mauritius
pers_nationality_mexico=Meksika
pers_nationality_moldova=Moldova
pers_nationality_monaco=Monacan
pers_nationality_montseIs=Montserrat Is.
pers_nationality_morocco=Fas
pers_nationality_mozambique=Mozambik
pers_nationality_namibia=Namibya
pers_nationality_nauru=Nauru
pers_nationality_nepal=Nepal
pers_nationality_netAnti=Hollanda Antilleri
pers_nationality_netherlands=Hollanda
pers_nationality_newZealand=Yeni Zelanda
pers_nationality_nicaragua=Nikaragua
pers_nationality_niger=Nijerya
pers_nationality_nigeria=Nijerya
pers_nationality_norKorea=Kuzey Kore
pers_nationality_norway=Norveç
pers_nationality_oman=Omani
pers_nationality_pakistan=Pakistan
pers_nationality_panama=Panama
pers_nationality_papNewCui=Papua Yeni Gine
pers_nationality_paraguay=Paraguay
pers_nationality_peru=Peru
pers_nationality_philippines=Filipinler
pers_nationality_poland=Polonya
pers_nationality_frenPolyne=Fransız Polinezyası
pers_nationality_portugal=Portekiz
pers_nationality_puerRico=Porto Riko
pers_nationality_qatar=Katar
pers_nationality_reunion=Birleşme
pers_nationality_romania=Romanyalı
pers_nationality_russia=Rusya
pers_nationality_saiLueia=Saint Lueia
pers_nationality_saintVinc=Saint Vincent
pers_nationality_samoa_eastern=Doğu Samoa
pers_nationality_samoa_western=Batı Samoa
pers_nationality_sanMarino=San Marinese
pers_nationality_saoAndPrinc=Sao Tome ve Principne
pers_nationality_sauArabia=Suudi Arabistan
pers_nationality_senegal=Senegal
pers_nationality_seychelles=Seyşeller
pers_nationality_sieLeone=Sierra Leone
pers_nationality_singapore=Singapur
pers_nationality_slovakia=Slovakça
pers_nationality_slovenia=Slovenya
pers_nationality_solomonIs=Solomon Adalı
pers_nationality_somali=Somali
pers_nationality_souAfrica=Güney Afrika
pers_nationality_spain=İspanyolca
pers_nationality_sriLanka=Sri Lanka
pers_nationality_sudan=Sudan
pers_nationality_suriname=Surinam
pers_nationality_swaziland=Swazi
pers_nationality_sweden=İsveç
pers_nationality_switzerland=İsviçre
pers_nationality_syria=Suriye
pers_nationality_tajikstan=Tacik
pers_nationality_tanzania=Tanzanya
pers_nationality_thailand=Tayland
pers_nationality_togo=Togo
pers_nationality_tonga=Tonga
pers_nationality_triAndToba=Trinidad ve Tobago
pers_nationality_tunisia=Tunus
pers_nationality_turkey=Türk
pers_nationality_turkmenistan=Türkmenistan
pers_nationality_uganda=Uganda
pers_nationality_ukraine=Ukrayna
pers_nationality_uniArabEmira=Birleşik Arap Emirlikleri
pers_nationality_uruguay=Uruguay
pers_nationality_uzbekistan=Özbekistan
pers_nationality_venezuela=Venezuela
pers_nationality_vietnam=Vietnam
pers_nationality_yemen=Yemen
pers_nationality_serbia=Sırbistan
pers_nationality_zimbabwe=Zimbabve
pers_nationality_zambia=Zambiya
pers_nationality_aruba=Aruba
pers_nationality_bhutan=Butan
pers_nationality_bosnia_herzegovina=Bosna-Hersek
pers_nationality_cambodia=Kamboçya
pers_nationality_congoD=Kongo Cumhuriyeti
pers_nationality_comoros=Komorlar Birliği
pers_nationality_capeVerde=Cape Verde Cumhuriyeti
pers_nationality_croatia=Hırvatistan
pers_nationality_dominica=Dominik
pers_nationality_eritrea=Eritre
pers_nationality_micronesia=Mikronezya
pers_nationalit_guineaBissau=Guinea-Bissau
pers_nationalit_equatorialGuinea=Ekvator Ginesi
pers_nationalit_hongkong=Hongkong,Çin
pers_nationalit_virginIslands=Virgin Adaları Birlesik Devletleri 
pers_nationalit_britishVirginIslands=İngiliz Virgin Adaları
pers_nationalit_kiribati=Kiribati
pers_nationalit_mongolia=Moğolistan
pers_nationalit_marshall=Marshall Adası
pers_nationalit_macedonia=Makedonya
pers_nationalit_montenegro=Karadağ
pers_nationalit_mauritania=Moritinya
pers_nationalit_palestine=Filistin
pers_nationalit_palau=Palau
pers_nationalit_rwanda=Ruanda
pers_nationalit_saintKittsNevis=Saint Kitts ve Nevis
pers_nationalit_timorLeste=Timor Leste
pers_nationalit_taiwan=Tayvan,Çin
pers_nationalit_tuvalu=Tuvalu
pers_nationalit_vanuatu=Vanuatu
#制卡
pers_person_cardprint=Baskı Kartı
pers_cardTemplate_tempSelect=Kart Şablonu
pers_cardTemplate_printerSelect=Yazıcı
pers_cardTemplate_front=Ön
pers_cardTemplate_opposite=Geri
pers_cardTemplate_entryDate=İşe Alınma Tarihi
pers_cardTemplate_photo=Fotoğraf
pers_cardTemplate_uploadFail=Resim yüklenemedi!
pers_cardTemplate_jpgFormat=Yalnızca JPG formatında resim yüklemeyi destekler!
pers_cardTemplate_printStatus=Yazdırma Durumu
pers_cardTemplate_waiting=Bekliyor
pers_cardTemplate_printing=Yazdırıyor
pers_cardTemplate_printOption=Yazım Seçenekleri
pers_cardTemplate_duplexPrint=Çift Yazım
pers_cardTemplate_frontOnly=Sadece önüne bastır
#app
pers_app_delPers=Sunucuda silmek istediğiniz kişi yok
pers_app_deptIsNull=Departmana karşılık gelen departman numarası veya adı bulunamıyor
pers_app_personNull=Kişi mevcut değil
pers_app_pinExist=Personel Kimliği zaten var
pers_app_dateError=Tarih biçimi yanlış, lütfen doğru biçime bakın: 2016-08-08
#api
pers_api_selectPhotoInvalid=Fotoğraf geçersiz, lütfen tekrar yükleyin
pers_api_dateError=Tarih biçimi yanlış
pers_api_personNotExist=Kişi mevcut değil
pers_api_cardsPersSupport=Sistem kartı açmıyor; kart geçersiz.
pers_api_department_codeOrNameNotNull=Departman kodu veya departman adı boş olamaz
pers_api_deptSortNoIsNull=Departman sıralaması boş olamaz!
pers_api_deptSortNoError=Departman sıralamasının değeri 1-999999 arasında olmalıdır!
pers_api_dataLimit=Ağımdaki operasyon sayısı {0}, {1} sınırı aşıyor. Lütfen gruplarda çalışın!
pers_api_cardTypeError=Kart türü hatası
#人员生物模板API
pers_api_fingerprintExisted=Kişinin parmak izi zaten var
pers_api_validtypeIncorrect=Bu geçerli tip özelliği değeri yanlış
pers_api_dataNotExist=Şablon yok
pers_api_templateNoRang=Lütfen doğru şablonu girin 0-9 aralığında değer yok
pers_api_templateIsNull=Şablon boş olamaz!
pers_api_versionIsNumber=Sürüm sadece numaraları girebilir!
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=Pin sadece bir sayı olabilir!
#biotime
pers_h5_personAvatarNotNull=Avatar boş
pers_h5_personMobileRepeat=Cep telefonu numarası zaten var
pers_h5_personEmailRepeat=Posta kutusu zaten var
pers_h5_pwdIsRepetition=Yeni ve eski şifre tekrarı
pers_h5_personIdNull=Personel kimliği boş
pers_h5_pinOrEmailIsNull=Lütfen numarayı ve e-posta adresini girin
pers_emailTitle_resetPassword=Şifre Değiştir
pers_emailContent_resetPassword=Bağlantı 24 saat geçerlidir, şifreyi değiştirmek için bağlantıyı tarayıcıya kopyalayın:
pers_h5_tokenIsNull=Fiş Boş
pers_h5_tokenError=Kupon hatası
pers_h5_oldPwdIsError=Eski şifre yanlış doldurulmuş
pers_api_resetPasswordSuccess=Şifre güncellendi
pers_api_resetPasswordFail=Şifre değişikliği başarısız oldu
pers_api_forgetPassword=Şifreyi unut
pers_api_confirmSubmit=gönderimi onayla
pers_api_confirmPwdCaution=Yeni şifreyi onaylamak için [Tamam] 'a tıklayın.
pers_api_pwdRule=Parola en az bir simge veya sayı içermeli ve en az 8-12 karakter uzunluğunda olmalıdır
pers_h5_personPinFormatNumber=Personel numarası yalnızca rakamlardan veya harflerden oluşabilir
pers_h5_persEmailNoExist=Posta kutusu doldurma hatası
pers_h5_pageNull=Sayfalama parametre hatası
pers_h5_personPinNotStartWithZero=Personel numarası 0 ile başlayamaz
pers_h5_personPinTooLong=Personel numarası çok uzun
pers_h5_personPinInValid=Bu numara zaten kullanılıyor
pers_h5_imgSizeError=Lütfen 10 MB'den büyük olmayan bir resim yükleyin!
pers_h5_confirmAndContinue=Teyit ve devam et
# 人 脸 抠图 不 及格 错误
pers_face_poorResolution=80000 pikselin altındaki resim çözünürlüğü
pers_face_noFace=Yüz algılanmadı
pers_face_manyFace=Birden fazla yüz algılandı
pers_face_smallFace=Yüz oranı çok küçük
pers_face_notColor=Resim renkli olmayan bir resim
pers_face_seriousBlur=Görüntü bulanık
pers_face_intensivelyLight=Görüntü çok açık durumda
pers_face_badIllumination=Resim çok karanlık
pers_face_highNoise=Yüksek gürültülü resim
pers_face_highStretch=Aşırı gerilmiş yüz
pers_face_covered=Yüz kaplıdır
pers_face_smileOpenMouth=Aşırı gülümseme
pers_face_largeAngle=Yüz sapma açısı çok büyük
pers_face_criticalIllumination=Görüntü parlaklığı kritik
pers_face_criticalLargeAngle=Kritik yüz sapma açısı
pers_face_validFailMsg=Yüz algılama şu nedenle başarısız oldu:
pers_face_failType=Yüz kesme hatası türü
pers_face_photoFormatError=Fotoğraf formatı yanlış, lütfen JPG / PNG formatlı bir dosya yükleyin.
pers_face_notUpdateMsg=Yüz resmi oluşturulamadı, yüz resmini güncellemeyin.
# 健康 申报
pers_health_enable=Sağlık bilgisi beyanını etkinleştirin
pers_health_attrExposure=Şüpheli vakalara maruz kalma
pers_health_attrSymptom=Son 14 gündeki herhangi bir semptom
pers_health_attrVisitCity=Son 14 günde ziyaret edilen şehir
pers_health_attrRemarks=Sağlıkla ilgili açıklamalar
pers_health_symptomCough=Öksürük
pers_health_symptomFever=Ateş
pers_health_symptomPolypena=Solunum Sorunları
pers_health_declaration=Sağlık Beyanı
pers_health_aggrement=Bilgileri gerektiği gibi doldurmayan kişilerin erişmesine izin verilmeyeceği konusunda hemfikirim ve bunu doğru bir şekilde bildirmeyen ziyaretçiler ziyaretlerine devam edemezler ve ilgili yasal sorumlulukları üstlenmeleri gerekir.
pers_health_visitCity_notEmpty=Ziyaret edilen şehir boş olamaz!
pers_health_notAgree=Devam etmek için lütfen anlaşmayı kontrol edin.
#人员名单库
pers_personnal_list_manager=Liste yöneticisi
pers_personnal_list=Kişisel Liste
pers_personnal_list_scheme=Alan modu
pers_personnal_list_name=Personal liste adı
pers_personnal_list_group_str_id=Liste grubu kimliği
pers_personnal_list_personCount=Kişi sayısı
pers_personnal_list_tag=Kullanıcı tanımlı
pers_personnal_list_type=Kişisel liste türü
pers_personnallist_addPerson_repo=Depoya bir kişi ekle
pers_personnallist_sendPersonnallist=Dağıtılmış liste kitaplığı
pers_personnallist_sendPerson=GönderPerson
pers_personnallist_notDel_existPerson=Liste kitaplığı kişiler içeriyor ve silinemez
pers_personnallist_peopleInRoster=Liste kitaplığında hala insanlar var
pers_personnallist_associationNotExist=Ana cihaz ile liste kitaplığı arasındaki ilişki mevcut değil
pers_personnal_list_person=Personal liste kişisi
pers_personnal_list_dev=Kitaplık izinlerini listele
pers_personnal_list_addDev=Cihaz ekle
pers_personnal_list_name_isExist=Ad zaten var
pers_personnal_bannedList=Yasaklanmış liste kitaplığı
pers_personnal_allowList=Liste kitaplığına izin ver
pers_personnal_redList=Kırmızı Liste Kitaplığı
pers_personnal_attGroup=Katılım Grubu
pers_personnal_passList=Geçiş listesi
pers_personnal_banList=Yasak liste
pers_personnal_visPassList=Ziyaretçi Gezi Listesi
pers_personnal_visBanList=Ziyaretçiler Yasak Listesi
pers_personnal_databaseHasBeenDistributed=Seçilen liste kitaplığı dağıtıldı ve silinemez
pers_personnel_sendError_dueTo=Teslimat hatası Başarısızlık nedeni:
pers_personnel_sendError_reson={0} yasaklılar listesinde var, lütfen silin ve ekleyin
#比对照片-样片示例
pers_examplePic_Tip=Aşağıdaki gereksinimleri karşılamalıdır:
pers_examplePic_Tip1=1. arka plan rengi bembeyaz ve personel koyu renk giysiler giyiyor;
pers_examplePic_Tip2=2. Elektronik fotoğraflar JPG, PNG, JPEG dosya biçimindedir, önerilen piksel aralığı: 480*640 < piksel < 1080*1920;
pers_examplePic_Tip3=3. Elektronik fotoğraflardaki portreler gözlerini açıp dümdüz karşıya bakmalı ve göz bebeklerinin net bir şekilde görülebilmesini sağlamalıdır;
pers_examplePic_Tip4=4. Elektronik fotoğraftaki portre nötr bir ifadeye sahip olmalı ve gülümseyebilirsin ama dişlerini göstermemelisin;
pers_examplePic_Tip5=5. Elektronik fotoğraftaki portre, doğal renklerle, zengin katmanlarla ve belirgin bir bozulma olmadan net olmalıdır. Portre yüzlerinde veya arka planlarda gölge, vurgu veya yansıma yok; kontrast ve parlaklık uygundur.
pers_examplePic_description=Doğru örnek
pers_examplePic_error=Hata örneği:
pers_examplePic_error1=Abartılı ifade (aşırı gülümseme)
pers_examplePic_error2=Işık çok karanlık
pers_examplePic_error3=Yüz çok küçük (çözünürlük çok küçük)
pers_applogin_enabled=Uygulama girişini etkinleştir
pers_applogin_disable=Uygulama girişini etkinleştir
pers_applogin_status=Uygulama giriş durumu etkinleştir