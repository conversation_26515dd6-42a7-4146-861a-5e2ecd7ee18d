#pers common.
#here other module can also use the label from pers.
pers_module=Personal
pers_common_addPerson=Agregar Personal
pers_common_delPerson=Borrar Personal
pers_common_personCount=Cantidad de Usuarios
pers_common_browsePerson=Búsqueda de Usuarios
#左侧菜单
pers_person_manager=Personal
pers_person=Usuarios
pers_department=Departamentos
pers_leave=Terminación de Personal
pers_tempPerson=Personal que requiere revisado
pers_attribute=Atributos Personalizables
pers_card_manager=Gestión de Tarjetas
pers_card=Tarjetas
pers_card_issue=Bitácora de Tarjetas
pers_wiegandFmt=Formato Wiegand
pers_position=Puesto
#人员
pers_person_female=Femenino
pers_person_male=Masculino
pers_person_pin=ID
pers_person_departmentChange=Cambiar de Departamento
pers_personDepartment_changeLevel=Permiso para cambiar de departamento
pers_person_gender=Género
pers_person_detailInfo=Detalles
pers_person_accSet=Control de Acceso
pers_person_accSetting=Control de Acceso
pers_person_attSet=Configuración de Asistencia
pers_person_eleSet=Control de Elevador
pers_person_eleSetting=Control de Elevador
pers_person_parkSet=Placa Vehicular
pers_person_pidSet=Certificado Personal
pers_person_insSet=Quiosco de la Cara
pers_person_aiSet=Rostro Inteligente
pers_person_payrollSet=Ajuste de salario
pers_person_psgSet=Ajuste de canal
pers_person_lockerSet=Configuración de casillero
pers_person_sisSet=Configuración de seguridad
pers_person_vdbSet=Configuración de intercomunicación visual
pers_person_firstName=Nombre
pers_person_lastName=Apellido
pers_person_name=Nombre
pers_person_wholeName=Nombre
pers_person_fullName=Nombre, Apellido
pers_person_cardNum=Tarjetas Emitidas
pers_person_deptNum=Departamentos Implicados
pers_person_dataCount=Estadísticas
pers_person_regFinger=Huella
pers_person_reg=Registrar
pers_person_password=Contraseña Verificación Dispositivo
pers_person_personDate=Fecha de Empleo
pers_person_birthday=Cumpleaños
pers_person_mobilePhone=Celular
pers_person_personDevAuth=Privilegio de Usuarios y Dispositivos
pers_person_email=Email
pers_person_browse=Examinar
pers_person_authGroup=Niveles de Acceso
pers_person_setEffective=Vigencia
pers_person_attArea=Área de Asistencia
pers_person_isAtt=Cálculo de Asistencia
pers_person_officialStaff=Personal de Base
pers_person_probationStaff=Personal de Prueba o practicante
pers_person_identity_category=Categoría de identidad
pers_person_devOpAuth=Tipo de Usuario
pers_person_msg1=Introduzca el número de tarjeta y código de sitio.
pers_person_msg2=Introduzca de 3 a 4 dígitos.
pers_person_msg3=Error de formato.
pers_person_imgPixel=(Tamaño óptimo 120 × 140 pixeles).
pers_person_cardLengthDigit=Introduzca el número.
pers_person_cardLengthHexadecimal=¡¡ introduzca un número o una letra abcdef!
pers_person_to=A
pers_person_templateCount=Huellas
pers_person_biotemplateCount=Plantillas
pers_person_regFace=Rostro
pers_person_regVein=Vena Del Dedo
pers_person_faceTemplateCount=Rostros
pers_person_VeinTemplateCount=Plantillas de la Venas
pers_person_palmTemplateCount=Cantidad de plantillas de Palmas
pers_person_cropFace=Imagen del rostro
pers_person_cropFaceCount=Compara la cantidad de fotos
pers_person_faceBiodataCount=Cantidad de plantillas Visible light 
pers_person_irisCount=Número de iris
pers_person_batchToDept=Nuevo Departamento
pers_person_changeReason=Razón de Transferencia
pers_person_selectedPerson=Seleccionar Usuario
pers_person_duressPwdError=Contraseña repetida
pers_person_completeDelPerson=Borrar
pers_person_recover=Recuperar
pers_person_nameNoComma=No debe contener comas.
pers_person_firstNameNotEmpty=Introduzca el nombre.
pers_person_lastNameNotEmpty=Introduzca el apellido.
pers_person_mobilePhoneValidate=Introduzca un número de celular válido.
pers_person_phoneNumberValidate=Introduzca un número de teléfono válido.
pers_person_postcodeValidate=Introduzca un número de código postal válido.
pers_person_idCardValidate=Introduzca un número de tarjeta válido.
pers_person_pwdOnlyLetterNum=La contraseña solo soporta letras y números.
pers_person_certNumOnlyLetterNum=La tarjeta de identificación solo puede incluir letras y números.
pers_person_oldDeptEqualsNewDept=El nuevo departamento no puede ser el departamento original.
pers_person_disabled=Acceso desactivado
pers_person_emailError=Introduzca un email válido.
pers_person_driverPrompt=Instale el driver del dispositivo. De clic en OK para descargarlo.
pers_person_readIDCardFailed=Operación fallida.
pers_person_cardPrompt=Coloque la tarjeta en el lector...
pers_person_iDCardReadOpenFailed=No se ha detectado el lector de tarjetas.
pers_person_iDCardNotFound=No se encontró el lector de tarjetas, intente de nuevo.
pers_person_nameValid=Soporta letras y números.
pers_person_nameValidForEN=Soporta letras y números.
pers_person_pinPrompt=Introduzca caracteres del alfabeto Español.
pers_person_pinSet=Configuración de ID de Usuario
pers_person_supportLetter=Soportar Letras
pers_person_cardsSupport=Soportar Múltiples Tarjetas por Usuario
pers_person_SupportDefault=ID Automático
pers_person_noSpecialChar=¡El nombre no puede contener caracteres especiales!
pers_person_pinInteger=Introduzca números.
pers_pin_noSpecialChar=¡El número de pentagrama no debe contener caracteres especiales!
pers_op_capture=Capturar
pers_person_cardDuress=Número de Tarjeta Repetido
pers_person_pwdException=Excepción de Contraseña
pers_person_systemCheckTip=Compruebe los datos de usuarios.
pers_person_immeHandle=Modificar Ahora
pers_person_cardSet=Ajuste de Tarjetas
pers_person_tempPersonSet=Configuraciones de personal pendientes
pers_person_cardsReadMode=Modo de Lectura
pers_person_cardsReadModeReadHead=Leer por Panel
pers_person_cardsReadModeID180=Leer por ID180
pers_person_IDReadMode=Modo de Lectura de Tarjeta
pers_person_IDReadModeIDCardReader=Lector de Tarjeta
pers_person_IDReadModeTcpReadHead=Lector TCP/IP
pers_person_physicalNo=Lector
pers_person_physicalNoToCardNo=Usar Número de Tarjeta Físico
pers_person_ReadIDCard=Leer Tarjeta
pers_person_templateBioTypeNumber=Typnummer der biometrischen Vorlage
pers_person_templateValidType=Validez de Plantilla
pers_person_templateBioType=Tipo de Plantilla
pers_person_templateVersion=Versión
pers_person_template=Plantilla Biométrica
pers_person_templateNo=No. de Plantilla
pers_person_templateNoIndex=Índice de Plantilla
pers_person_templateDataUpdate=Plantillas biométricas ya existentes:
pers_person_templateValidTypeNoNull=Validez de la plantilla no puede estar vacío.
pers_person_templateBioTypeNoNull=ID de personal: {0}, El tipo de plantilla no puede estar vacío.
pers_person_templateVersionNoNull=ID de personal: {0}, La versión de la plantilla no puede estar vacía.
pers_person_templateNoNull=ID de personal: {0}, La plantilla no puede estar vacía.
pers_person_templateNoNoNull=ID de personal: {0}, El número de plantilla no puede estar vacío.
pers_person_templateNoIndexNoNull=ID de personal: {0}, El índice de plantilla no puede estar vacío.
pers_person_templateError=ID de personal: {0}, Plantilla biométrica incorrecta.
pers_person_bioDuress=Coacción
pers_person_universal=Común
pers_person_voice=Voz
pers_person_iris=Iris
pers_person_retina=Retina
pers_person_palmPrints=Palma
pers_person_metacarpalVein=Vena De La Palma
pers_person_visibleFace=Cara visible
pers_person_pinError=El ID de usuario {0} no existe. No es posible procesar.
pers_person_pinException=Excepción de ID de Usuario
pers_person_pinAutoIncrement=Auto-Incrementar ID
pers_person_resetSelfPwd=Restablecer contraseña de inicio de sesión automático
pers_person_picMaxSize=La resolución de la imagen es demasiado alta y la resolución se recomienda debajo de {0}.
pers_person_picMinSize=La resolución de la imagen es demasiado baja y se recomienda que la resolución esté por encima de {0}.
pers_person_cropFaceShow=Ver plantilla de rostro
pers_person_faceNoFound=Rostro no reconocido
pers_person_biometrics=Tipo de biometría
pers_person_photo=Fotos personales
pers_person_visibleFaceTemplate=Plantilla de cara visible
pers_person_infraredFaceTemplate=Plantilla de reconocimiento facial por infrarrojo
pers_person_delBioTemplate=Eliminar datos biométricos
pers_person_delBioTemplateSelect=¡Seleccione la plantilla biológica para eliminar!
pers_person_infraredFace=Plantilla de rostro de infrarrojos cercanos
pers_person_notCard=No incluye tarjeta
pers_person_notRegFinger=No incluye huella digital
pers_person_notMetacarpalVein=No incluye la vena de la palma
pers_person_notRegVein=No incluye las venas de los dedos
pers_person_notIris=No contiene Iris
pers_person_notInfraredFaceTemplate=No incluye plantilla de rostro infrarrojo cercano
pers_person_notVisibleFaceTemplate=No incluye plantilla de rostro visible light
pers_person_notVisibleFacePhoto=No incluye fotos de rostros visibles
pers_person_visibleFacePhoto=Imagen de rostro visible
pers_person_change=Ajustes de personal
pers_person_cropFaceUsePhoto=¿¿ quieres mostrar las fotos comparativas como avatares?
pers_person_photoUseCropFace=¿¿ quieres usar avatares para generar fotos de contraste?
pers_person_selectCamera=Elija la Cámara para disparar
pers_person_delCropFaceMsg=¡¡ no hay fotos de contraste borrables!
pers_person_disabledNotOp=¡Esta persona ha sido desactivada y no se puede operar!
pers_person_visiblePalm=Palma de luz visible
pers_person_notVisiblePalm=Excluyendo las palmas visibles
pers_person_selectDisabledNotOp=¡¡ hay personas discapacitadas en el personal seleccionado y no se puede operar!
pers_person_photoUseCropFaceAndTempalte=¿¿ se utilizan avatares para generar fotos comparativas y plantillas faciales?
pers_person_photoUseTempalte=¿¿ se utiliza la foto para generar una plantilla facial?
pers_person_createCropFace=Generar fotos de comparación
pers_person_createFaceTempalte=Generar una plantilla facial
pers_person_faceTempalte=Plantilla facial
pers_person_extractFaceTemplate=Extraer plantilla facial
pers_person_createSuccess=Generación exitosa
pers_person_createFail=Falló la generación
pers_person_serverConnectWarn=¡¡ la dirección del servidor, el nombre de usuario y la contraseña no pueden estar vacíos!
pers_person_serverOffline=El servidor de extracción de plantillas faciales está fuera de línea
pers_person_faceTemplateError1=Falló la detección de la cara
pers_person_faceTemplateError2=Oclusión facial
pers_person_faceTemplateError3=Falta de claridad
pers_person_faceTemplateError4=El ángulo de la cara es demasiado grande
pers_person_faceTemplateError5=Falló la prueba viva
pers_person_faceTemplateError6=Falló la extracción de la plantilla facial
pers_person_cropFaceNoExist=Las fotos comparativas no existen
pers_person_disableFaceTemplate=¡¡ la función de extracción de plantillas faciales no está habilitada y no se puede extraer la plantilla!
pers_person_cropFacePhoto=Foto de contraste facial
pers_person_vislightPalmPhoto=Foto de contraste de Palmas
pers_person_serverOfflineWarn=¡¡ el servidor de extracción de plantillas faciales está fuera de línea y no se puede activar esta función!
pers_person_serverConnectInfo=Por favor, pruebe si el servidor de extracción de plantillas faciales está en línea.
pers_person_notModified=El número de teléfono móvil no puede ser modificado.
pers_person_syncAcms=Sincronizar personal con ACMS
pers_person_startUpdate=Comienza a actualizar la información de personal.
pers_person_updateFailed=¡La actualización de la información de personal ha fallado!
#控制器发卡
pers_person_readCard=Panel de Acceso
pers_person_stopRead=Detener
pers_person_chooseDoor=Seleccione la Puerta
pers_person_readCarding=¡Emisión de tarjetas, ¡ por favor, vuelva a intentarlo más tarde!
#抓拍照片
pers_capture_catchPhoto=Capturar Foto
pers_capture_preview=Vista Previa
#部门
pers_dept_entity=Departamento
pers_dept_deptNo=ID de Departamento
pers_dept_deptName=Nombre de Departamento
pers_dept_parentDeptNo=ID de Departamento Superior
pers_dept_parentDeptName=Nombre de Departamento Superior
pers_dept_parentDept=Departamento Superior
pers_dept_note=Si no se muestran los departamentos, contacte al administrador para establecer los privilegios.
pers_dept_exit=Contiene
pers_dept_auth=Usuario de sistema enlazado
pers_dept_parentMenuMsg=El departamento superior no puede ser el mismo o uno inferior.
pers_dept_initDept=General
pers_dept_deptMarket=Administración
pers_dept_deptRD=Ingeniería
pers_dept_deptFinancial=Contabilidad
pers_dept_nameNoSpace=El nombre de departamento no puede comenzar o terminar con espacios.
pers_dept_nameExist=El nombre de departamento ya existe.
pers_dept_changeLevel=Cambiar el nivel de este departamento
pers_dept_noSpecialChar=¡El número de departamento no puede contener caracteres especiales!
pers_dept_NameNoSpecialChar=¡El nombre del departamento no debe contener caracteres especiales!
pers_dept_noModifiedParent=¡El departamento superior no se puede modificar!
#职位
pers_position_entity=Puesto
pers_position_code=ID de Puesto
pers_position_name=Nombre de Puesto
pers_position_notExist=¡La posición no existe!
pers_position_sortNo=Clasificación
pers_position_parentName=Puesto Superior
pers_position_parentCode=ID de Puesto Superior
pers_position_batchToPosition=Nuevo Puesto
pers_position_nameExist=El puesto ya existe.
pers_position_change=Cambiar de Puesto
pers_position_parentMenuMsg=El puesto superior no puede ser el mismo o uno inferior.
pers_position_nameNoSpace=El nombre del puesto no puede iniciar o terminar con espacios.
pers_position_existSub={0} :Contiene sub-publicaciones no pueden ser eliminados
pers_position_existPerson={0} :El personal no puede ser eliminado
pers_position_importTemplate=Plantilla de importación
pers_position_downloadTemplate=Descargar e importar plantilla
pers_position_codeNotEmpty=El número de posición no puede estar vacío
pers_position_nameNotEmpty=El nombre de la posición no puede estar vacío
pers_position_nameNoSpecialChar=¡El nombre de posición {0} no puede contener caracteres especiales!
pers_position_noSpecialChar=¡El número de posición {0} no puede ser un carácter especial!
pers_position_codeLength=El número de posición {0} supera los 30 dígitos de longitud
pers_position_nameLength=Datos cuyo nombre de posición {0} es más largo que {1}
pers_position_codeExist=El número de posición {0} ya existe
#证件
pers_cert_type=Tipo de Documento
pers_cert_number=Documento / Cédula
pers_cert_name=Nombre de Identificación
pers_cert_numberExist=El número de documento ya existe.
#导出
pers_export_allPersPerson=Todos los Usuarios
pers_export_curPersPerson=Usuarios Actuales
pers_export_template=Exportar Plantilla
pers_export_personInfo=Exportar Personal
pers_export_personInfoTemplate=Descargar Plantilla de Importación
pers_export_personBioTemplate=Exportar Plantilla Biométrica
pers_export_basicInfo=Información Básica
pers_export_customAttr=Atributos Personalizables
pers_export_templateComment=Nombre de campo, clave primaria?, único?, permitir valores nulos?({0},{1},{2},{3})
pers_export_templateFileName=Plantilla de Importación de Personal
pers_export_bioTemplateFileName=Plantillas Biométricas del Personal
pers_export_deptInfo=Exportar Departamentos
pers_export_deptTemplate=Plantilla de Importación de Departamentos
pers_export_deptTemplateFileName=Plantilla de Departamentos
pers_export_personPhoto=Exportar Fotos del Personal
pers_export_allPhotos=Todas (Selecciona todo el personal)
pers_export_selectPhotoToExport=Seleccione el ID inicial y final para exportar las fotos
pers_export_fromId=Desde ID
pers_export_toId=Hasta
pers_export_certNumberComment=Se requiere el tipo de certificado después de completar el número de certificado
pers_export_templateCommentName=Nombre de campo:({0})
pers_export_dataExist=Asegúrese de que los datos importados ya existan en el sistema.
pers_export_cardNoTip=Varios números de tarjeta separados por &
pers_carNumber_importTip=Número de matrícula (varias matrículas& separadas)
#导入
pers_import_certNumberExist=El número de documento {0} ya existe.
pers_import_complete=Completado
pers_import_password=Contraseña de Usuario
pers_import_fail=La fila {0} fallo: {1}
pers_import_overData=Está tratando de importar {0} usuarios. Solo es posible importar 30,000 usuarios en cada operación.
pers_import_pinTooLong=El ID {0} es demasiado largo.
pers_import_pinExist=El ID {0} ya existe.
pers_import_pinIsRepeat=El ID {0} se repite.
pers_import_pinError=ID {0} con error.
pers_import_pinSupportNumber=El número de personal solo admite números! El número de personal es: {0}
pers_import_pinNotSupportNonAlphabetic=El número de persona solo admite números y combinaciones de letras. El número de personal es: {0}
pers_import_pinNotNull=El número de ID de usuario no puede ser ceros.
pers_import_pinStartWithZero=El número de ID no puede empezar con cero.
pers_import_cardNoNotSupportLetter=El número de tarjeta no puede contener letras.
pers_import_cardNoNotNull=El número de tarjeta no pueden ser todos ceros.
pers_import_cardNoStartWithZero=El número de tarjeta no puede comenzar con cero!
pers_import_cardTooLong=El número de tarjeta {0} excede el límite.
pers_import_cardExist=El número de tarjeta {0} ya existe.
pers_import_personPwdOnlyNumber=La contraseña solo pueden ser números.
pers_import_personPwdTooLong=La contraseña {0} es demasiado larga.
pers_import_personDuressPwd=La contraseña {0} ya existe.
pers_import_emailTooLong=El email {0} es demasiado largo.
pers_import_nameTooLong=El nombre {0} es demasiado largo.
pers_import_genderError=Formato de género incorrecto.
pers_import_personDateError=Error en formato de fecha de empleo.
pers_import_createTimeError=Error en formato de hora de creación.
pers_import_phoneError=Error de formato de número de teléfono.
pers_import_phoneTooLong=El número de teléfono no puede ser mayor de 20 dígitos.
pers_import_emailError=Formato de email incorrecto.
pers_import_birthdayError=Formato de cumpleaños incorrecto.
pers_import_nameError=Apellido y nombre no pueden contener símbolos especiales.El número de personal es: {0}
pers_import_firstnameError=El nombre o apellido no puede contener comas.
pers_import_firstnameNotNull=Debe introducir el nombre.
pers_import_dataCheck=Fin de la verificación de datos
pers_import_dataSaveFail=Error al guardar los datos.
pers_import_allSucceed=Datos importados exitosamente.
pers_import_result=Correctos: {0}. Fallidos: {1}.
pers_import_result2=Resultado de la Importación
pers_import_result3=Correctos: {0}, Actualizados: {1}, Fallidos {2}.
pers_import_notSupportFormat=Este formato no es soportado.
pers_import_selectCorrectFile=Seleccione el archivo correcto.
pers_import_fileFormat=Formato de Archivo
pers_import_targetFile=Archivo de Importación
pers_import_startRow=Fila de Inicio
pers_import_startRowNote=Por defecto es la segunda fila.
pers_import_delimiter=Separador
pers_import_importingDataFields=Campos de Base de Datos
pers_import_dataSourceFields=Campos de Importación
pers_import_total=Total
pers_import_dataUpdate=Actualizar el ID de usuario existente en el sistema:
pers_import_dataIsNull=No hay datos en el archivo.
pers_import_deptNotExist=¡El departamento no existe!
pers_import_deptIsNotNull=Introduzca el nombre del departamento.
pers_import_pinNotEmpty=Debe introducir el ID de Usuario.
pers_import_nameNotEmpty=¡El nombre del personal no puede estar vacío!
pers_import_siteCodeOnlyLetterNum=Error de formato de código de sitio.
pers_import_cardNoFormatErrors=El número de tarjeta {0} está mal formado
pers_import_cardsNotSupport=La función [Multi-Tarjetas] está desactivada, no puede importar más de una tarjeta por usuario.
pers_import_personInfo=Importar Información de Personal
pers_import_commentFormat=Formato incorrecto.
pers_import_noComment=Los datos en la fila {0}, columna {1}, no tienen anotaciones.
pers_import_fieldRepeat=Los datos en la fila {0}, columna {1}, la anotación del nombre de campo: {2} está repetida.
pers_import_primaryKey=Debe haber al menos un campo como clave primaria.
pers_import_templateIsRepeat=Número de personal: los datos de la plantilla biológica de {0} se han duplicado.
pers_import_biologicalTemplate=Importar Plantillas Biométricas
pers_import_uploadFileSuccess=Datos cargados exitosamente. Procesando...
pers_import_resolutionComplete=El análisis se ha completado, se iniciará la actualización de la base de datos.
pers_import_mustField=El archivo de importación debe contener la columna {0}.
pers_import_bioTemplateSuccess=La plantilla biométrica requiere ser sincronizada manualmente al dispositivo de cada módulo.
pers_import_personPhoto=Importar Foto del Personal
pers_import_opera_log=Registro de operaciones
pers_import_error_log=Registro de errores
pers_import_uploadFileSize=¡Por favor, cargue un archivo con un tamaño que no sea mayor que {0}!
pers_import_uploadFileSizeLimit=Para una sola importación, cargue un archivo que no supere los 500M.
pers_import_type=Modo de importación
pers_import_photoType=Photo
pers_import_archiveType=Paquete comprimido
pers_import_startUpload=Iniciar Carga
pers_import_addMore=Agregar Más
pers_import_photoQuality=Calidad de Foto
pers_import_original=Original
pers_import_adaptive=Responsivo
pers_import_adaptiveSize=(Tamaño 640 x 480)
pers_import_totalNumber=Total
pers_import_uploadTip=(No borrar las fotos mientras se cargan)
pers_import_addPhotoTip=Borre las fotos cargadas previamente o seleccione nuevas fotos para cargar.
pers_import_selectPhotoTip=Seleccione la foto para cargar
pers_import_uploadResult=Resultado de la Carga de Fotos
pers_import_pleaseSelectPhoto=Seleccione la Foto
pers_import_multipleSelectTip=Presione Ctrl para selección múltiple
pers_import_replacePhotoTip=La foto seleccionada ya está en la lista de carga, desea reemplazarla?
pers_import_photoNamePinNotCorrespond=Razón: El nombre de la foto y el ID de Usuario no coinciden.
pers_import_photoFormatRequirement=El nombre de la foto debe ser igual al ID de usuario correspondiente. Los formatos deben ser JPG/PNG. Verifique que el nombre de la foto no contenga caracteres especiales.
pers_import_filterTip=Algunas de las fotos seleccionadas no se pueden previsualizar por alguna de las siguientes razones:
pers_import_photoContainSpecialCharacters=El nombre de la foto contiene caracteres especiales.
pers_import_photoFormatError=El formato es incorrecto.
pers_import_photoSelectNumber=¡No seleccione más de 3000 imágenes en una sola importación!
pers_import_photoSelectNumberLimit=¡No elijas más de {0} imágenes!
pers_import_fileMaxSize=La imagen es demasiado grande, suba un archivo de imagen menor a 5M.
pers_import_notUploadPhotoNumber=¡No se pueden subir más de 3000 imágenes!
pers_import_zipFileNotPhoto=No hay una foto de la persona en el archivo zip, ¡vuelva a seleccionar e importar!
pers_import_personPlateRepeat=La placa {0} se repite.
pers_import_filePlateRepeat=El archivo de la placa {0} se repite.
pers_import_personPlateFormat=El formato de la placa {0} es incorrecto.
pers_import_personPlateMax=El número de la placa excede el máximo de 6 caraceres.
pers_import_cropFaceFail=Error al generar la comparación de la foto!
pers_import_pinLeaved=Número de personal: {0} se ha ido.
pers_import_exceedLicense=No se permiten importaciones para la cantidad de personas que exceden la licencia del software.
pers_import_bioTemplateNotNull=¡El número de personal, tipo de identificación biométrica, índice de bio, contenido, versión no pueden estar vacíos!
pers_import_certTypeNotNull=El número de personal es: {0} El tipo de documento no puede estar vacío
pers_import_certTypeNotExist=El número de persona es: {0} El tipo de documento no existe
pers_import_certNumNotNull=El número de personal es: {0} El número de certificado no puede estar vacío
pers_import_certNumberTooLong=Fila {0}: ¡el número de identificación {1} es demasiado largo!
pers_import_idNumberErrors=Fila {0}: ¡El número de tarjeta de identificación {1} está mal formado!
pers_import_emailErrors=Fila {0}: ¡Dirección de correo electrónico {1} error de formato!
pers_import_emailIsExist=¡La dirección de correo electrónico {0} ya existe!
pers_import_emailIsRepeat=Fila {0}: ¡Se repitió la dirección de correo electrónico interna del archivo {1}!
pers_import_fileMobilePhoneRepeat=Fila {0}: ¡Se ha repetido el número de teléfono móvil interno del archivo {1}!
pers_import_mobilePhoneErrors=¡Error de formato de número de teléfono móvil!
pers_import_hireDateError=¡¡ el formato de la fecha de entrada no es correcto!
pers_import_selectPhotoType=¡¡ por favor, elija el tipo de foto importada!
pers_import_hireDateLaterCurrent=¡¡ la fecha de empleo no puede ser posterior a la fecha actual!
pers_import_buildingNotExist=¡El edificio no existe!
pers_import_unitNotExist=¡La unidad no existe!
pers_import_vdbInfoFail=¡No hay información de la unidad llamada {1} en el edificio {0}!
pers_import_vdbBuildingFail=¡La información del edificio de la unidad {0} no puede estar vacía!
pers_import_vdbRoomNoFail=¡El número de habitación debe ser un valor numérico mayor que 0!
#人员离职
pers_person_leave=Terminación
pers_dimission_date=Fecha de Terminación
pers_dimission_type=Tipo de Terminación
pers_dimission_reason=Causa de Terminación
pers_dimission_volutary=Terminación Voluntaria
pers_dimission_dismiss=Despido
pers_dimission_resignat=Renuncia
pers_dimission_shiftJob=Transferencia
pers_dimission_leave=Permanecer de baja sin sueldo
pers_dimission_recovery=Reintegración Laboral
pers_dimission_sureToRecovery=¿Seguro de realizar la reintegración laboral?
pers_dimission_backCard=¿Retornar tarjeta?
pers_dimission_isForbidAction=¿Deshabilitar niveles de acceso ahora?
pers_dimission_writeInfomation=Introduzca la información de la terminación.
pers_dimission_pinRetain=Conservar el ID del Personal con Terminación
pers_dimission_downloadTemplate=Descarga de plantilla de importación erronea
pers_dimission_import=Importar dimisiones
pers_dimission_importTemplate=Plantilla de importación de dimisión
pers_dimission_date_noNull=La fecha de dimisión no puede estar vacía
pers_dimission_leaveType_noExist=El tipo de dimisión no existe
pers_dimission_dateFormat=Campo obligatorio, el formato de hora es aaaa-MM-dd, como: 2020-07-22
pers_dimission_leaveType=Campo obligatorio, como: Terminación Voluntaria, Despido, Renuncia, Transferencia
pers_dimission_forbidden=Deshabilitar
pers_dimission_leaveType_noNull=¡El tipo de licencia no puede estar vacío!
pers_dimission_person_noExist=¡¡ el personal despedido no existe!
pers_dimission_date_error=Fecha de despido no rellenada o formato incorrecto
#临时人员
pers_tempPerson_audit=Revisar
pers_tempPerson_view=Ver
pers_tempPerson_waitReview=Esperando la revisión del administrador
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Tarjeta
pers_card_state=Estado de Tarjeta
pers_card_effect=Efectivo
pers_card_disabled=Inválido
pers_card_past=Expirado
pers_card_back=Retorno de Tarjeta
pers_card_change=Cambió de Tarjeta
pers_card_note=El número de tarjeta ya existe.
pers_card_numTooBig=El código de sitio es demasiado grande.
pers_issueCard_entity=Registro de Tarjeta
pers_issueCard_operator=Operador
pers_issueCard_operate=Acción
pers_issueCard_note=El personal se encuentra registrado pero no cuenta con tarjeta asignada.
pers_issueCard_date=Fecha de Registro
pers_issueCard_changeTime=Modificado
pers_issueCard_cardValidate=No debe contener espacios.
pers_issueCard_cardEmptyNote=Debe introducir el número de tarjeta.
pers_issueCard_cardHasBeenIssued=Tarjeta ya asignada.
pers_issueCard_noCardPerson=Tarjeta no emitida
pers_issueCard_waitPerson=Esta tarjeta emisora
pers_issueCard_mc5000=Tarjeta MC5000
pers_batchIssCard_entity=Registro de Tarjetas por Lote
pers_batchIssCard_startPersNo=ID de Usuario Inicial
pers_batchIssCard_endPersNo=ID de Usuario Final
pers_batchIssCard_issCardNum=Tarjetas Asignadas
pers_batchIssCard_notIssCardNum=Usuarios Sin Tarjeta Asignada
pers_batchIssCard_generateList=Generar Lista
pers_batchIssCard_startRead=Iniciar Lectura
pers_batchIssCard_swipCard=Punto de Captura
pers_batchIssCard_sendCard=Lector
pers_batchIssCard_dispenCardIss=Lector USB
pers_batchIssCard_usbEncoder=Codificador USB
pers_batchIssCard_note=El ID de usuario debe ser número y un máximo de 300 usuarios a la vez.
pers_batchIssCard_startPinEmpty=Introduzca el ID de usuario inicial.
pers_batchIssCard_endPinEmpty=Introduzca el ID de usuario final.
pers_batchIssCard_startPinLargeThanEndPin=El ID inicial no puede ser mayor que el ID final.
pers_batchIssCard_numberParagraphNoPerson=El número de ID no existe.
pers_batchIssCard_inputCardNum=Introduzca el número de tarjeta.
pers_batchIssCard_cardLetter=El número de tarjeta no debe contener letras.
pers_batchIssCard_cardNoTooLong=El número de tarjeta es demasiado largo.
pers_batchIssCard_issueWay=Modo de Registro
pers_batchIssCard_noPersonList=Genere la lista de usuarios.
pers_batchIssCard_startReadCard=Iniciar Captura
pers_batchIssCard_swipePosition=Punto de Captura
pers_batchIssCard_chooseSwipePosition=Seleccione una tarjeta.
pers_batchIssCard_readCardTip=Dispositivos de registro.
pers_batchIssCard_notIssCardNo=Tarjetas Sin Asignar
pers_batchIssCard_totalNumOfCards=Total de Tarjetas
pers_batchIssCard_acms=Emisión de tarjetas ACMS
pers_lossCard_entity=Tarjeta Perdida
pers_lossCard_lost=Tarjeta reportada pérdida
pers_losscard_note2=Después de escribir la tarjeta para la gestión de equipos de control de lectura en la escalera de la cabeza con esta gestión de tarjeta, que entró en vigor el funcionamiento va de la pérdida.
pers_revertCard_entity=Reactivar Tarjeta Perdida
pers_revertCard_setReport=Reporte de tarjeta perdida.
pers_revertcard_note2=Después de escribir la tarjeta de elevador debe presentar la tarjeta en el lector para asegurar el uso de la tarjeta nuevamente.
pers_issueCard_success=Tarjeta emitida exitosamente.
pers_issueCard_error=Error al emitir tarjeta.
pers_cardData_error=Error en el formato de datos de la tarjeta.
pers_analysis_error=Error en análisis de datos de la tarjeta.
pers_cardOperation_error=Error de la operación de la tarjeta.
pers_cardPacket_error=Error de la operación de la tarjeta.
pers_card_write=Escribir tarjeta
pers_card_init=Inicializar tarjeta
pers_card_loss=Tarjeta perdida
pers_card_revert=Revertir tarjeta
pers_card_writeMgr=Escribir tarjeta de administrador
pers_initCard_tip=Después de inicializar la tarjeta quedará en blanco.
pers_initCard_prepare=Listo para inicializar la tarjeta...
pers_initCard_process=Inicialiazando la tarjeta...
pers_initCard_success=Tarjeta inicializada exitosamente.
pers_mgrCard_prepare=Preparando para escribir tarjeta de administrador...
pers_mgrCard_process=Escribiendo tarjeta de administrador...
pers_mgrCard_success=Tarjeta de administrador realizada exitosamente.
pers_userCard_prepare=Preparando para escribir tarjeta de usuario...
pers_userCard_process=Escribiendo tarjeta de usuario...
pers_userCard_success=Tarjeta de usuario realizada exitosamente.
pers_userCard_tip=Por favor, establecer el tiempo de inicio y fin en el editar persona pagina y luego escribir la operación de tarjetas de
pers_userCard_tip2=Sin privilegios para escribir tarjetas.
pers_userCard_tip3=Los permisos se asocian a más de dos dispositivos y se pierden datos.
pers_writeCard_tip=Asegúrese de que el driver se ha instalado y posiciona la tarjeta correctamente
pers_writeMgrCard_tip=El número de tarjetas perdidas y revertidas es mayor de 18
pers_writeMgrCard_tip2=Número de la tarjeta perdida y revertida
pers_card_writeToMgr=Escrito en la tarjeta de administrador
pers_card_hex=Formato de Tarjeta
pers_card_decimal=Decimal
pers_card_Hexadecimal=Hexadecimal
pers_card_IssuedCommandFail=Error de Ejecución de Comando:
pers_card_multiCard=Más Tarjetas
pers_card_deputyCard=Tarjeta Secundaria
pers_card_deputyCardValid=Introduzca la tarjeta principal primero.
pers_card_writePinFormat=El sistema solo soporta escribir la tarjeta del personal sin letras en el ID de Usuario.
pers_card_notMoreThanSixteen=La cantidad de tarjetas adicionales no puede ser mayor de 16.
pers_card_notDelAll=No puede remover todas las tarjetas adicionales.
pers_card_maxCard=El número de tarjeta no puede exceder de {0}.
pers_card_posUseCardNo=El módulo del consumidor está utilizando la tarjeta principal de la persona. ¡Vaya al módulo del consumidor para realizar la operación de extracción de la tarjeta!
pers_card_delFirst=¡¡ por favor, elimine el número de tarjeta emitido antes de emitir la tarjeta!
pers_card_disablePersonWarn=¡El número de tarjeta seleccionado contiene personas con discapacidad, ¡ no se puede operar!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Código de Sitio
pers_wiegandFmt_wiegandMode=Modo
pers_wiegandFmt_wiegandModeOne=Modo Uno
pers_wiegandFmt_wiegandModeTwo=Modo Dos
pers_wiegandFmt_isDefaultFmt=Auto
pers_wgFmt_entity=Formato Wiegand
pers_wgFmt_in=Formato de Entrada Wiegand
pers_wgFmt_out=Formato de Salida Wiegand
pers_wgFmt_inType=Tipo de Entrada Wiegand
pers_wgFmt_outType=Tipo de Salida Wiegand
pers_wgFmt_wg=Formato Wiegand
pers_wgFmt_totalBit=Total de Bits
pers_wgFmt_oddPch=Comprobación de paridad impar(o)
pers_wgFmt_evenPck=Comprobación de paridad par (e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=Facility Code(f)
pers_wgFmt_siteCode=Código de Sitio(s)
pers_wgFmt_manufactoryCode=Código de Fabricante(m)
pers_wgFmt_firstParity=Primer Bit de Paridad(p)
pers_wgFmt_secondParity=Segundo Bit de Paridad(p)
pers_wgFmt_cardFmt=Formato de Tarjeta
pers_wgFmt_parityFmt=Formato de Paridad
pers_wgFmt_startBit=Bit de Inicio
pers_wgFmt_test=Prueba de Formato Wiegand
pers_wgFmt_error=Error de Formato Wiegand
pers_wgFmt_verify1=El total de bits no debe exceder de 80.
pers_wgFmt_verify2=Compruebe que la longitud del formato debe ser igual al número total de bits.
pers_wgFmt_verify3=La longitud del formato de paridad debe ser igual al número total de dígitos.
pers_wgFmt_verify4=Los datos inicializados no pueden ser eliminados.
pers_wgFmt_verify5=El formato Wiegand se encuentra en uso y no puede ser eliminado.
pers_wgFmt_verify6=El primer bit de paridad no puede ser mayor que el número total de bits.
pers_wgFmt_verify7=El segundo bit de paridad no puede ser mayor que el número total de bits.
pers_wgFmt_verify8=El formato de inicio y longitud son incorrectos.
pers_wgFmt_verify9=Formato de registro de entrada excede el número total de bits.
pers_wgFmt_verify10=Comprobación de dígitos de la tarjeta no se puede cruzar.
pers_wgFmt_verify11=El código de sitio sobrepasa el rango.
pers_wgFmt_verify=Verificar
pers_wgFmt_unverify=No Verificar
pers_wgFmt_atLeastDefaultFmt=Conserve al menos un formato Wiegand automático.
pers_wgFmt_defaultFmtError1=El mismo número de bits contenidos en otros formatos formato Wiegand y no se puede ajustar para que coincida de forma automática!
pers_wgFmt_defaultFmtError2=Presencia del mismo formato Wiegand en coincidencia automática. Operación fallida.
pers_wgFmt_cardFormats=Formatos de Tarjeta
pers_wgFmt_cardFormatTesting=Probar Formato de Tarjeta
pers_wgFmt_checkIsUsed=¡El formato de la tarjeta se usa en {0} y no se puede eliminar!
pers_wgFmt_supportDigitsNumber=Introduzca la longitud de los dígitos soportados por el dispositivo
#选人控件
pers_widget_selectPerson=Seleccionar personal
pers_widget_searchType1=Consulta
pers_widget_searchType2=Departamento
pers_widget_deptHint=Nota: Importará todo el personal de los departamentos seleccionados.
pers_widget_noPerson=No ha seleccionado usuarios.
pers_widget_noDept=Seleccione un departamento.
pers_widget_noDeptPerson=No hay usuarios en el departamento seleccionado.
#人员属性
pers_person_carPlate=Placa Vehicular
pers_person_socialSecurity=Cédula / Seguro Social
pers_person_msg4=Longitud máxima de 20 caracteres.
pers_person_msg5=El nombre no debe exceder 50 caracteres.
pers_person_type=Tipo de Usuario
pers_person_reseCode=Código de Auto-Gestión
pers_person_IsSendMail=Notificación por correo
pers_person_inactive=Inactivo
pers_person_active=Activo
pers_person_employee=Usuario
pers_person_isSendMailMsg=Para usar la función de [Notificación de Evento] debe primero introducir el email.
pers_person_createTime=Creado
pers_person_pinFirstValid=El primer caracter del ID no puede ser 8 ó 9.
pers_person_attrValueValid=El valor del campo no puede repetirse.
pers_person_attrValueDelimiterValid=El separador debe estar en la mitad.
pers_person_isSendSMS=Notificación por SMS
pers_person_building=Nombre del edificio
pers_person_unitName=Nombre de la unidad
pers_person_roomNo=Número de habitación
#动态属性
pers_attr_emp_type=Tipo de Usuario
pers_attr_street=Calle
pers_attr_nation=País
pers_attr_office_address=Dirección de Oficina
pers_attr_postcode=C.P.
pers_attr_office_phone=Teléfono de Oficina
pers_attr_home_phone=Teléfono de Casa
pers_attr_job_title=Puesto
pers_attr_birthplace=Lugar de Nacimiento
pers_attr_polit_status=Estado Civil
pers_attr_country=País
pers_attr_home_address=Dirección de Casa
pers_attr_hire_type=Contratación
pers_attr_inContract=Bajo Contrato
pers_attr_outContract=Sin Contrato
#属性自定义
pers_attribute_attrName=Nombre de Atributo
pers_attribute_attrValue=Valor de Atributo
pers_attribute_controlType=Tipo de Entrada
pers_attribute_positionX=Fila
pers_attribute_positionY=Columna
pers_attribute_showTable=Mostrar en Interfaz de Usuario
pers_attrDefini_deletemsg=Esta propiedad ha sido usada. ¿Seguro de eliminar?
pers_attrDefini_reserved=Campo reservado por el sistema.
pers_attrDefini_msg1=Longitud máxima de 30 caracteres.
pers_attrDefini_msg2=Las filas de la columna ya existen, seleccione otra ubicación.
pers_attrDefini_attrValue_split=Utilizando un ' ; ' como delimitador.
pers_attrDefini_attrName=Nombre de Atributo
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=ID de Atributo
pers_attrDefini_select=Lista Desplegable
pers_attrDefini_check=Opción Múltiple
pers_attrDefini_radio=Opción Única
pers_attrDefini_text=Texto
pers_attrDefini_maxCol=No más de 2 columnas.
pers_attrDefini_maxLimit=Se ha alcanzado el máximo de atributos personalizables.
pers_attrDefini_modControlType=Al cambiar el tipo de atributo se eliminarán los datos relacionados de todos los usuarios en el sistema. ¿Continuar?
#leavePerson
pers_leavePerson_reinstated=Reintegrar
#opExample
pers_example_newRecode=Descargar Eventos Nuevos
pers_example_allRecode=Descargar Todos los Eventos
pers_custField_StatisticalType=Tipo de Estadística
#人员参数修改
pers_param_isAudit=Habilitar auditoría automática
pers_param_donotChangePin=El ID contiene letras, no se puede cambiar el modo a números.
pers_param_hexChangeWarn=El sistema actual ya contiene los números de tarjetas, no se puede cambiar el modo de visualizar el formato de la tarjeta.
pers_param_cardsChangeWarn=Existen usuarios con multi-tarjeta en el sistema, no se puede desactivar la función.
pers_param_maxPinLength=La longitud de ID del personal nuevo no puede ser menor que el ID del personal ya existente en el sistema.
pers_param_pinBeyondDevLength=La longitud máxima de ID del personal admitida por el dispositivo en el sistema es de {0}. Introduzca un número entero menor que {1}.
pers_param_cardBeyondDevLength=La longitud máxima de número de tarjeta admitida por el dispositivo en el sistema es {0}. Introduzca un número entero menor que {1}.
pers_param_checkIsExistNoAudit=¡Hay registros sin revisar en el sistema y no se pueden modificar para revisión automática!
pers_param_noSupportPinLetter=Existen dispositivos en el sistema que no soportan que el ID de personal contenga letras. No se puede activar la función de Soportar Letras.
pers_param_changePinLettersTip=El ID de Usuario soporta auto-incremento y no se puede modificar el modo de ID.
pers_param_changePinIncrementTip=El ID de Usuario incluye letras y no se puede modificar el modo de ID.
pers_param_qrCode=Código QR de la empresa
pers_param_employeeRegistrar=Habilitar registro de empleados en la nube
pers_param_downloadQRCodePic=Descargar imagen de código QR
pers_param_qrCodeUrl=Código QR URL
pers_param_qrCodeUrlCreate=Registro de Autoservicio
pers_param_qrCodeUrlHref=Dirección del servidor:Puerto
pers_param_pinSetWarn=Ya hay personal en el sistema actual y no se puede cambiar el modo de número de personal.
pers_param_selfRegistration=Habilitar registro automático
pers_param_infoProtection=Protección de información confidencial
pers_param_infoProtectionWarnMsg=Después de habilitar la opción de protección de seguridad de la información confidencial personal, los datos personales confidenciales involucrados en este módulo se ocultarán, incluidos, entre otros, nombres, números de tarjeta, números de identificación, fotos, etc.
pers_param_templateServer=Servidor de extracción de plantillas faciales
pers_param_enableFacialTemplate=Activar la extracción de plantillas faciales
pers_param_templateServerAddr=Plantilla facial para extraer la dirección del servidor
pers_param_templateServerWarnInfo=Cuando se activa la extracción de la plantilla facial, cuando el servidor de extracción de la plantilla facial está en línea y el usuario aprueba la verificación, el personal extrae la plantilla facial por defecto al comparar fotos; ¡¡ no extraiga la plantilla facial cuando el servidor de extracción de la plantilla facial está en modo offline!
pers_param_templateServerWarnInfo1=¡¡ al activar la extracción de plantillas faciales, es necesario conectar dispositivos que admitan la extracción de plantillas faciales!
pers_param_templateServerOffline=¡¡ el servidor de extracción de plantillas faciales está fuera de línea y no se puede extraer la plantilla facial! ¿¿ quieres continuar?
pers_param_faceServer=Servicio de comparación de fondo facial
pers_param_enableFaceVerify=Activar la comparación de fondo facial
pers_param_faceServerAddr=Dirección del Servicio de comparación de fondo facial
pers_param_faceServerSecret=Clave secreta del Servicio de comparación de fondo facial
#国籍
pers_person_nationality=País/Región
pers_nationality_angola=Angola
pers_nationality_afghanistan=Afganistán
pers_nationality_albania=Albania
pers_nationality_algeria=Argelia
pers_nationality_america=Estados Unidos
pers_nationality_andorra=Andorra
pers_nationality_anguilla=Anguilla
pers_nationality_antAndBar=Antigua y Barbuda
pers_nationality_argentina=Argentina
pers_nationality_armenia=Armenia
pers_nationality_ascension=Ascensión
pers_nationality_australia=Australia
pers_nationality_austria=Austria
pers_nationality_azerbaijan=Azerbaiyán
pers_nationality_bahamas=Bahamas
pers_nationality_bahrain=Bahrein
pers_nationality_bangladesh=Bangladesh
pers_nationality_barbados=Barbados
pers_nationality_belarus=Belarús
pers_nationality_belgium=Bélgica
pers_nationality_belize=Belice
pers_nationality_benin=Benin
pers_nationality_bermudaIs=Bermuda
pers_nationality_bolivia=Bolivia
pers_nationality_botswana=Botswana
pers_nationality_brazil=Brasil
pers_nationality_brunei=Brunei
pers_nationality_bulgaria=Búlgaro
pers_nationality_burkinaFaso=Burkina Faso
pers_nationality_burma=Myanmar
pers_nationality_burundi=Burundi
pers_nationality_cameroon=Camerún
pers_nationality_canada=Canadá
pers_nationality_caymanIs=Islas Caimán
pers_nationality_cenAfrRepub=República Centroafricana
pers_nationality_chad=Chad
pers_nationality_chile=Chile
pers_nationality_china=China
pers_nationality_colombia=Colombia
pers_nationality_congo=República del Congo
pers_nationality_cookIs=Islas cook
pers_nationality_costaRica=Costa Rica
pers_nationality_cuba=Cuba
pers_nationality_cyprus=Chipre
pers_nationality_czechRep=Checo
pers_nationality_denmark=Dinamarca
pers_nationality_djibouti=Djibouti
pers_nationality_dominicaRep=República Dominicana
pers_nationality_ecuador=Ecuador
pers_nationality_egypt=Egipto
pers_nationality_eISalvador=El Salvador
pers_nationality_england=Inglés
pers_nationality_estonia=Estonia
pers_nationality_ethiopia=Etiopía
pers_nationality_fiji=Fiji
pers_nationality_finland=Finlandia
pers_nationality_france=Francia
pers_nationality_freGui=Guayana Francesa
pers_nationality_gabon=Gabón
pers_nationality_gambia=Gambia
pers_nationality_georgia=Georgia
pers_nationality_germany=Alemania
pers_nationality_ghana=Ghana
pers_nationality_gibraltarm=Gibraltar
pers_nationality_greece=Grecia
pers_nationality_grenada=Granada
pers_nationality_guam=Guam
pers_nationality_guatemala=Guatemala
pers_nationality_guinea=Guinea
pers_nationality_guyana=Guayana
pers_nationality_haiti=Haití
pers_nationality_honduras=Honduras
pers_nationality_hungary=Hungría
pers_nationality_iceland=Islandia
pers_nationality_india=India
pers_nationality_indonesia=Indonesia
pers_nationality_iran=Irán
pers_nationality_iraq=Irak
pers_nationality_ireland=Irlanda
pers_nationality_israel=Israel
pers_nationality_italy=Italia
pers_nationality_ivoryCoast=Costa de Marfil
pers_nationality_jamaica=Jamaica
pers_nationality_japan=Japón
pers_nationality_jordan=Jordania
pers_nationality_kenya=Kenia
pers_nationality_korea=Corea
pers_nationality_kuwait=Kuwait
pers_nationality_kyrgyzstan=Kirguistán
pers_nationality_laos=Laos
pers_nationality_latvia=Letonia
pers_nationality_lebanon=Líbano
pers_nationality_lesotho=Lesoto
pers_nationality_liberia=Liberia
pers_nationality_libya=Libia
pers_nationality_liechtenstein=Liechtenstein
pers_nationality_lithuania=Lituania
pers_nationality_luxembourg=Luxemburgo
pers_nationality_madagascar=Madagascar
pers_nationality_malawi=Malawi
pers_nationality_malaysia=Malasia
pers_nationality_maldives=Maldivas
pers_nationality_mali=Mali
pers_nationality_malta=Malta
pers_nationality_marianaIs=Islas Mariana
pers_nationality_martinique=Martinica
pers_nationality_mauritius=Mauricio
pers_nationality_mexico=México
pers_nationality_moldova=Moldavia
pers_nationality_monaco=Mónaco
pers_nationality_montseIs=Montserrat
pers_nationality_morocco=Marruecos
pers_nationality_mozambique=Mozambique
pers_nationality_namibia=Namibia
pers_nationality_nauru=Nauru
pers_nationality_nepal=Nepal
pers_nationality_netAnti=Antillas holandesas
pers_nationality_netherlands=Países Bajos
pers_nationality_newZealand=Nueva Zelandia
pers_nationality_nicaragua=Nicaragua
pers_nationality_niger=Níger
pers_nationality_nigeria=Nigeria
pers_nationality_norKorea=Corea
pers_nationality_norway=Noruega
pers_nationality_oman=Omán
pers_nationality_pakistan=Pakistán
pers_nationality_panama=Panamá
pers_nationality_papNewCui=Papua Nueva Guinea
pers_nationality_paraguay=Paraguay
pers_nationality_peru=Perú
pers_nationality_philippines=Filipinas
pers_nationality_poland=Polonia
pers_nationality_frenPolyne=Polinesia francés
pers_nationality_portugal=Portugal
pers_nationality_puerRico=Puerto Rico
pers_nationality_qatar=Katar
pers_nationality_reunion=Reunión
pers_nationality_romania=Rumania
pers_nationality_russia=Rusia
pers_nationality_saiLueia=Santa Lucía
pers_nationality_saintVinc=San Vicente
pers_nationality_samoa_eastern=Samoa Oriental
pers_nationality_samoa_western=Samoa Occidental
pers_nationality_sanMarino=San Marino
pers_nationality_saoAndPrinc=Santo Tomé y Príncipe
pers_nationality_sauArabia=Arabia Saudita
pers_nationality_senegal=Senegal
pers_nationality_seychelles=Seychelles
pers_nationality_sieLeone=Sierra Leona
pers_nationality_singapore=Singapur
pers_nationality_slovakia=Eslovaquia
pers_nationality_slovenia=Eslovenia
pers_nationality_solomonIs=islas Salomón
pers_nationality_somali=Somalia
pers_nationality_souAfrica=Sudáfrica
pers_nationality_spain=España
pers_nationality_sriLanka=Sri Lanka
pers_nationality_sudan=Sudán
pers_nationality_suriname=Surinam
pers_nationality_swaziland=Swazilandia
pers_nationality_sweden=Suecia
pers_nationality_switzerland=Suiza
pers_nationality_syria=Siria
pers_nationality_tajikstan=Tayikistán
pers_nationality_tanzania=Tanzania
pers_nationality_thailand=Tailandia
pers_nationality_togo=Togo
pers_nationality_tonga=Tonga
pers_nationality_triAndToba=Trinidad y Tobago
pers_nationality_tunisia=Túnez
pers_nationality_turkey=Turquía
pers_nationality_turkmenistan=Turkmenistán
pers_nationality_uganda=Uganda
pers_nationality_ukraine=Ucrania
pers_nationality_uniArabEmira=Emiratos árabes Unidos
pers_nationality_uruguay=Uruguay
pers_nationality_uzbekistan=Uzbekistán
pers_nationality_venezuela=Venezuela
pers_nationality_vietnam=Vietnam
pers_nationality_yemen=Yemen
pers_nationality_serbia=Serbia
pers_nationality_zimbabwe=Zimbabue
pers_nationality_zambia=Zambia
pers_nationality_aruba=Aruba
pers_nationality_bhutan=Bhután
pers_nationality_bosnia_herzegovina=Bosnia y Herzegovina
pers_nationality_cambodia=Camboya
pers_nationality_congoD=República Democrática del Congo
pers_nationality_comoros=La Unión de las Comoras
pers_nationality_capeVerde=La República de Cabo Verde
pers_nationality_croatia=Croacia
pers_nationality_dominica=Dominica
pers_nationality_eritrea=Eritrea
pers_nationality_micronesia=Micronesia
pers_nationalit_guineaBissau=Guinea-Bissau
pers_nationalit_equatorialGuinea=Guinea Ecuatorial
pers_nationalit_hongkong=Hong Kong, China
pers_nationalit_virginIslands=Las Islas Vírgenes de los Estados Unidos
pers_nationalit_britishVirginIslands=Las Islas Vírgenes Británicas
pers_nationalit_kiribati=Kiribati
pers_nationalit_mongolia=Mongolia
pers_nationalit_marshall=Islas Marshall
pers_nationalit_macedonia=Macedonia
pers_nationalit_montenegro=Montenegro
pers_nationalit_mauritania=Mauritania
pers_nationalit_palestine=Palestina
pers_nationalit_palau=Palau
pers_nationalit_rwanda=Rwanda
pers_nationalit_saintKittsNevis=Saint Kitts y Nevis
pers_nationalit_timorLeste=Timor Leste
pers_nationalit_taiwan=Taiwán, China
pers_nationalit_tuvalu=Tuvalu
pers_nationalit_vanuatu=Vanuatu
#制卡
pers_person_cardprint=Imprimir Credencial
pers_cardTemplate_tempSelect=Selección de plantilla
pers_cardTemplate_printerSelect=Impresora
pers_cardTemplate_front=Parte Frontal
pers_cardTemplate_opposite=Parte Trasera
pers_cardTemplate_entryDate=Fecha Contratación
pers_cardTemplate_photo=Foto
pers_cardTemplate_uploadFail=Error al cargar la imagen.
pers_cardTemplate_jpgFormat=Sólo permite subir archivos en formato JPG.
pers_cardTemplate_printStatus=Estado de Impresión
pers_cardTemplate_waiting=En Espera
pers_cardTemplate_printing=Imprimiendo
pers_cardTemplate_printOption=Opciones de impresión
pers_cardTemplate_duplexPrint=Impresión de doble cara
pers_cardTemplate_frontOnly=Imprimir solo la parte delantera
#app
pers_app_delPers=No hay usuarios en el servidor que desea borrar.
pers_app_deptIsNull=No se encontró el nombre o ID del departamento.
pers_app_personNull=Persona no existe
pers_app_pinExist=El ID de Usuario ya existe.
pers_app_dateError=El formato de la fecha es incorrecto, por favor refiérase al formato correcto：2016-08-08
#api
pers_api_selectPhotoInvalid=La foto no es válida, por favor vuelva a subirla.
pers_api_dateError=El formato de la fecha es incorrecto
pers_api_personNotExist=Persona no existe
pers_api_cardsPersSupport=El sistema no abre una tarjeta, establece que la tarjeta no es válida.
pers_api_department_codeOrNameNotNull=El número o el nombre del departamento no pueden estar vacíos
pers_api_deptSortNoIsNull=¡El nombre de departamento no puede estar vacío!
pers_api_deptSortNoError=¡El valor de la clasificación de departamentos debe estar entre 1-999999!
pers_api_dataLimit=El operando actual es {0}, superando el límite de {1}. ¡Por favor, opere en lotes!
pers_api_cardTypeError=Tipo de tarjeta incorrecto
#人员生物模板API
pers_api_fingerprintExisted=La huella digital de la persona ya existe
pers_api_validtypeIncorrect=Este valor de atributo de tipo válido es incorrecto
pers_api_dataNotExist=TemplateNo inconsistente
pers_api_templateNoRang=Por favor, introduzca el dedo correcto valor en la gama 0 - 9
pers_api_templateIsNull=¡¡ la plantilla no puede estar vacía!
pers_api_versionIsNumber=¡¡ la versión solo puede introducir números!
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=El pin sólo puede ser numérico
#biotime
pers_h5_personAvatarNotNull=El avatar esta vacío
pers_h5_personMobileRepeat=El número de móvil ya existe
pers_h5_personEmailRepeat=El buzón ya existe
pers_h5_pwdIsRepetition=Repetición de contraseñas nuevas y antiguas
pers_h5_personIdNull=La identificación del personal está vacía
pers_h5_pinOrEmailIsNull=Por favor ingrese el número o la dirección de correo electrónico
pers_emailTitle_resetPassword=Cambiar contraseña
pers_emailContent_resetPassword=El enlace es válido por 24 horas, copie el enlace al navegador para cambiar la contraseña:
pers_h5_tokenIsNull=El token está vacío
pers_h5_tokenError=Error del token
pers_h5_oldPwdIsError=La contraseña antigua se ha ingresado incorrectamente
pers_api_resetPasswordSuccess=Cambiar la contraseña con éxito
pers_api_resetPasswordFail=Error de cambio de contraseña
pers_api_forgetPassword=Olvidaste tu contraseña
pers_api_confirmSubmit=Confirmar envio
pers_api_confirmPwdCaution=Haga clic en [Aceptar] para confirmar la nueva contraseña.
pers_api_pwdRule=La contraseña debe contener al menos un símbolo o número y tener al menos 8-12 caracteres de longitud.
pers_h5_personPinFormatNumber=El número de personal solo puede consistir en números o letras
pers_h5_persEmailNoExist=Error de correo electrónico
pers_h5_pageNull=Error de parámetro de paginación
pers_h5_personPinNotStartWithZero=El número de personal no puede comenzar con 0
pers_h5_personPinTooLong=El número de personal es demasiado largo.
pers_h5_personPinInValid=Este número ya está en uso.
pers_h5_imgSizeError=¡Cargue una imagen que no sea mayor de 10M!
pers_h5_confirmAndContinue=Confirmar y continuar
#人脸抠图不及格错误
pers_face_poorResolution=Resolución de imagen inferior a 80000 píxeles
pers_face_noFace=No se detectó el rostro
pers_face_manyFace=Múltiples caras detectadas
pers_face_smallFace=La relación de rostro es demasiado pequeña
pers_face_notColor=Imagen descolorida
pers_face_seriousBlur=La imagen está borrosa
pers_face_intensivelyLight=La imagen está muy expuesta
pers_face_badIllumination=La imagen está muy oscura
pers_face_highNoise=Imágenes con mucho ruido
pers_face_highStretch=Cara sobre estirada
pers_face_covered=La cara está cubierta
pers_face_smileOpenMouth=Sonrisa excesiva
pers_face_largeAngle=El ángulo de desviación de la cara es demasiado grande
pers_face_criticalIllumination=Brillo de imagen excesivo
pers_face_criticalLargeAngle=Ángulo crítico de desviación de la cara
pers_face_validFailMsg=La detección de rostros falló debido a:
pers_face_failType=Tipo de falla de reconocimiento facial
pers_face_photoFormatError=El formato de la foto es incorrecto, cargue un archivo en formato JPG / PNG.
pers_face_notUpdateMsg=No se pudo generar la imagen del rostro
#健康申报
pers_health_enable=Habilitar la declaración de información de salud
pers_health_attrExposure=¿Alguna vez ha estado en contacto con casos sospechosos o confirmados
pers_health_attrSymptom=Síntomas en los últimos 14 días
pers_health_attrVisitCity=Ciudad visitada en los últimos 14 días
pers_health_attrRemarks=Notas del estado de salud
pers_health_symptomCough=Tos
pers_health_symptomFever=Fiebre
pers_health_symptomPolypena=Desaliento
pers_health_declaration=Declaración de salud
pers_health_aggrement=He aceptado que las personas que no completen la información según se requiera tendrán prohibido el acceso, y los visitantes que no informen la información de manera veraz no pueden continuar accediendo y deben asumir la responsabilidad legal correspondiente.
pers_health_visitCity_notEmpty=¡La ciudad visitada no puede estar vacía!
pers_health_notAgree=Necesita aceptar el acuerdo antes de continuar
#人员名单库
pers_personnal_list_manager=Administrador de listas
pers_personnal_list=Biblioteca de listas
pers_personnal_list_scheme=Modo de dominio
pers_personnal_list_name=Nombre de la lista personal
pers_personnal_list_group_str_id=Lista de ID de grupo
pers_personnal_list_personCount=Número de personas
pers_personnal_list_tag=Definido por el usuario
pers_personnal_list_type=Tipo de lista de personal
pers_personnallist_addPerson_repo=Agregar una persona al repositorio
pers_personnallist_sendPersonnallist=Biblioteca de listas distribuidas
pers_personnallist_sendPerson=EnviarPersona
pers_personnallist_notDel_existPerson=La  lista contiene personas y no se puede eliminar
pers_personnallist_peopleInRoster=Todavía hay personas en la biblioteca de listas
pers_personnallist_associationNotExist=La asociación entre el dispositivo principal y la biblioteca de listas no existe
pers_personnal_list_person=Persona de la lista personal
pers_personnal_list_dev=Lista de permisos
pers_personnal_list_addDev=Agregar dispositivo
pers_personnal_list_name_isExist=El nombre ya existe
pers_personnal_bannedList=Biblioteca de listas prohibidas
pers_personnal_allowList=Biblioteca de listas de permisos
pers_personnal_redList=Biblioteca de la Lista Roja
pers_personnal_attGroup=Grupo de asistencia
pers_personnal_passList=Lista de contraseñas
pers_personnal_banList=Lista prohibida
pers_personnal_visPassList=Lista de pases de visitantes
pers_personnal_visBanList=Lista de visitantes prohibidos
pers_personnal_databaseHasBeenDistributed=La lista seleccionada ha sido asignada y no puede ser borrada
pers_personnel_sendError_dueTo=Error de entrega Motivo del error:
pers_personnel_sendError_reson={0} existe en la lista de prohibidos, elimínelos y añádalos
#比对照片-样片示例
pers_examplePic_Tip=Debe cumplir los siguientes requisitos:
pers_examplePic_Tip1=1. el color de fondo es blanco puro y el personal usa ropa oscura;
pers_examplePic_Tip2=2. Las fotos electrónicas están en formato de archivo JPG, PNG, JPEG, el rango de píxeles recomendado: 480*640 < píxel <1080*1920;
pers_examplePic_Tip3=3. Los retratos en fotografías electrónicas deben abrir los ojos y mirar al frente y asegurarse de que las pupilas sean claramente visibles;
pers_examplePic_Tip4=4. El retrato en la foto electrónica debe tener una expresión neutra y puede sonreír, pero no debe mostrar los dientes;
pers_examplePic_Tip5=5. El retrato de la fotografía electrónica debe ser claro, con colores naturales, capas ricas y sin distorsión evidente. No hay sombras, reflejos ni reflejos en los rostros o fondos de los retratos; el contraste y el brillo son adecuados.
pers_examplePic_description=Ejemplo correcto
pers_examplePic_error=Ejemplo de error:
pers_examplePic_error1=Expresión exagerada (sonrisa excesiva)
pers_examplePic_error2=La luz es demasiado oscura
pers_examplePic_error3=La cara es demasiado pequeña (la resolución es demasiado pequeña)
pers_applogin_enabled=Activar el inicio de sesión de la aplicación
pers_applogin_disable=Desactivar el inicio de sesión de la aplicación
pers_applogin_status=Estado habilitado de inicio de sesión de la aplicación