#pers common.
#here other module can also use the label from pers.
pers_module=Personnel
pers_common_addPerson=Ajouter Personnel
pers_common_delPerson=Supprimer Personnel
pers_common_personCount=Quantité de personnel
pers_common_browsePerson=Parcourir le personnel
#左侧菜单
pers_person_manager=Personnel
pers_person=Personne
pers_department=Département
pers_leave=Personnel licencié
pers_tempPerson=En attente d'examination
pers_attribute=Attributs personnalisés
pers_card_manager=Gestion des cartes
pers_card=Cartes
pers_card_issue=Enregistrement de la carte émise
pers_wiegandFmt=Format Wiegand
pers_position=Position
#人员
pers_person_female=Femme
pers_person_male=Homme
pers_person_pin=ID Personnel
pers_person_departmentChange=Ajuster le département
pers_personDepartment_changeLevel=Autorisation de changement de département
pers_person_gender=Genre
pers_person_detailInfo=Détails du personnel
pers_person_accSet=Contrôle d'accès
pers_person_accSetting=Paramètre de contrôle d'accès
pers_person_attSet=Temps et présence
pers_person_eleSet=Contrôle d'ascenseur
pers_person_eleSetting=Réglage du contrôle d'ascenseur
pers_person_parkSet=Enregistrement de plaque
pers_person_pidSet=Certificat du personnel
pers_person_insSet=FaceKiosk
pers_person_aiSet=Face Intellect
pers_person_payrollSet=Réglage du salaire
pers_person_psgSet=Réglage du passage
pers_person_lockerSet=Paramètre du casier
pers_person_sisSet=Paramètres de contrôle de sécurité
pers_person_vdbSet=Paramètres d'interphone visuel
pers_person_firstName=Prénom
pers_person_lastName=Nom de famille
pers_person_name=Prénom
pers_person_wholeName=Nom
pers_person_fullName=Premier milieu dernier
pers_person_cardNum=Nombre de cartes détenues
pers_person_deptNum=Nombre de départements impliqués
pers_person_dataCount=Statistiques
pers_person_regFinger=Empreinte digitale
pers_person_reg=S'inscrire
pers_person_password=Mot de passe de vérification du périphérique
pers_person_personDate=Date d'emploi
pers_person_birthday=Date d'anniversaire
pers_person_mobilePhone=Téléphone portable
pers_person_personDevAuth=Autorisations de personne et de périphérique
pers_person_email=Email
pers_person_browse=Naviguer
pers_person_authGroup=Niveaux d'accès
pers_person_setEffective=Définir l'heure effective
pers_person_attArea=Zone de présence
pers_person_isAtt=Calcul de présence
pers_person_officialStaff=Personnel officiel
pers_person_probationStaff=Personnel de probation
pers_person_identity_category=Type de personnel
pers_person_devOpAuth=Rôle d'opération du périphérique
pers_person_msg1=Le numéro de carte et le code du site doivent être remplis en même temps!
pers_person_msg2=Veuillez saisir un 3-4 chiffre.
pers_person_msg3=Erreur de format!
pers_person_imgPixel=(Taille optimale 120*140).
pers_person_cardLengthDigit=Veuillez saisir le numéro.
pers_person_cardLengthHexadecimal=Veuillez entrer un chiffre ou une lettre abcdef!
pers_person_to=À
pers_person_templateCount=Quantité d'empreintes digitales
pers_person_biotemplateCount=Quantité de modèle biologique
pers_person_regFace=Visage
pers_person_regVein=Veine de doigt
pers_person_faceTemplateCount=Quantité de visage
pers_person_VeinTemplateCount=Quantité de veine de doigt
pers_person_palmTemplateCount=Quantité de paume
pers_person_cropFace=Images de visages
pers_person_cropFaceCount=Quantité d'image de visage
pers_person_faceBiodataCount=Visible Face Quantity
pers_person_irisCount=Nombre d'Iris
pers_person_batchToDept=Nouveau département
pers_person_changeReason=Motif du transfert
pers_person_selectedPerson=Personne sélectionnée
pers_person_duressPwdError=Mot de passe répété
pers_person_completeDelPerson=Supprimer
pers_person_recover=Recouvrer
pers_person_nameNoComma=Ne doit pas inclure la virgule.
pers_person_firstNameNotEmpty=Le prénom ne doit pas être vide.
pers_person_lastNameNotEmpty=Le nom de famille ne doit pas être vide.
pers_person_mobilePhoneValidate=Veuillez entrer un numéro de téléphone mobile valide.
pers_person_phoneNumberValidate=Veuillez entrer un numéro de téléphone valide.
pers_person_postcodeValidate=Veuillez entrer un code postal valide.
pers_person_idCardValidate=Veuillez entrer un numéro id de Carte valide.
pers_person_pwdOnlyLetterNum=Le mot de passe ne peut contenir que des lettres ou des chiffres.
pers_person_certNumOnlyLetterNum=Le numéro de certificat ne peut contenir que des lettres ou des chiffres.
pers_person_oldDeptEqualsNewDept=Le nouveau département que vous avez ajusté ne doit pas être le même que le département d'origine.
pers_person_disabled=L'accès est désactivé
pers_person_emailError=Veuillez entrer une adresse email valide.
pers_person_driverPrompt=Veuillez installer le pilote du périphérique ! Cliquez sur OK pour télécharger le pilote.
pers_person_readIDCardFailed=Échec du glissage!
pers_person_cardPrompt=Gardez la carte d'identité dans la région ...
pers_person_iDCardReadOpenFailed=Aucun lecteur de carte d'identité n'est détecté!
pers_person_iDCardNotFound=Carte d'identité introuvable, veuillez réessayer!
pers_person_nameValid=Supporte le Chinois, l'Anglais, les chiffres, \'-\', \'_\'.
pers_person_nameValidForEN=Supporte l'Anglais, les nombres, \'-\', \'_\', \'.\'.
pers_person_pinPrompt=Veuillez saisir les caractères de l'alphabet anglais, les chiffres.
pers_person_pinSet=Paramètre d'identification du personnel
pers_person_supportLetter=Supporte les lettres
pers_person_cardsSupport=Plusieurs cartes par personne
pers_person_SupportDefault=Création automatique d'ID de personnel
pers_person_noSpecialChar=Vous ne pouvez pas saisir de caractères spéciaux!
pers_person_pinInteger=Veuillez saisir les chiffres.
pers_pin_noSpecialChar=Le numéro personnel ne doit pas contenir de caractères spéciaux!
pers_op_capture=Capture
pers_person_cardDuress=Numéro de carte répété
pers_person_pwdException=Exception de mot de passe
pers_person_systemCheckTip=Vérifiez les informations du personnel pour le format approprié!
pers_person_immeHandle=Manipuler immédiatement
pers_person_cardSet=Réglage de carte
pers_person_tempPersonSet=Paramètres du personnel en attente
pers_person_cardsReadMode=Mode de lecture de carte
pers_person_cardsReadModeReadHead=Lire par le contrôleur
pers_person_cardsReadModeID180=Lire par ID180
pers_person_IDReadMode=Mode de lecture de la carte ID
pers_person_IDReadModeIDCardReader=Lecteur de carte ID
pers_person_IDReadModeTcpReadHead=Lecteur TCP/IP
pers_person_physicalNo=Numéro de carte physique de la carte ID
pers_person_physicalNoToCardNo=Utiliser le numéro de carte physique de la carte ID
pers_person_ReadIDCard=Lire carte ID
pers_person_templateBioTypeNumber=Numéro de type de modèle biométrique
pers_person_templateValidType=Validité du modèle biométrique
pers_person_templateBioType=Type de modèle biométrique
pers_person_templateVersion=Version du modèle biométrique
pers_person_template=Modèle biométrique
pers_person_templateNo=Numéro du Modèle biométrique.
pers_person_templateNoIndex=Index du modèle biométrique
pers_person_templateDataUpdate=Mise à jour des données lorsqu'un modèle biométrique existe:
pers_person_templateValidTypeNoNull=La validité du modèle biométrique ne doit pas être vide!
pers_person_templateBioTypeNoNull=ID personnel: {0}, Le type de modèle biométrique ne doit pas être vide!
pers_person_templateVersionNoNull=ID personnel: {0}, La version du modèle biométrique ne doit pas être vide!
pers_person_templateNoNull=ID personnel: {0}, Le modèle biométrique ne doit pas être vide!
pers_person_templateNoNoNull=ID personnel: {0}, Le numéro de modèle biométrique ne doit pas être vide!
pers_person_templateNoIndexNoNull=ID personnel: {0}, L'index du modèle biométrique ne doit pas être vide!
pers_person_templateError=ID personnel: {0}, Modèle biométrique incorrect!
pers_person_bioDuress=Contrainte
pers_person_universal=Commun
pers_person_voice=Empreinte vocale
pers_person_iris=Iris
pers_person_retina=Rétine
pers_person_palmPrints=Paume
pers_person_metacarpalVein=Veine de Paume
pers_person_visibleFace=Visage visible
pers_person_pinError=ID personnel {0} n'existe pas, impossible de traiter ces données!
pers_person_pinException=Exception de l'ID du personnel
pers_person_pinAutoIncrement=ID personnel incrémentation automatique
pers_person_resetSelfPwd=Réinitialiser le mot de passe d'auto-connexion
pers_person_picMaxSize=La résolution de l'image est trop élevée et la résolution est recommandée sous {0}.
pers_person_picMinSize=La résolution de l'image est trop faible et la résolution est recommandée d'être supérieure {0}.
pers_person_cropFaceShow=Voir l'image du visage
pers_person_faceNoFound=Visage non-reconnu
pers_person_biometrics=Type de biométrie
pers_person_photo=Photos personnelles
pers_person_visibleFaceTemplate=Modèle de visage visible
pers_person_infraredFaceTemplate=Modèle de visage proche infrarouge
pers_person_delBioTemplate=Supprimer les données biométriques
pers_person_delBioTemplateSelect=Veuillez sélectionner le modèle biologique à supprimer!
pers_person_infraredFace=Visage proche infrarouge
pers_person_notCard=N'inclut pas la carte
pers_person_notRegFinger=N'inclut pas les empreintes digitales
pers_person_notMetacarpalVein=N'inclut pas la veine de la paume
pers_person_notRegVein=N'inclut pas les veines des doigts
pers_person_notIris=Ne contient pas d'Iris
pers_person_notInfraredFaceTemplate=N'inclut pas le modèle de visage proche infrarouge
pers_person_notVisibleFaceTemplate=N'inclut pas le modèle de visage de lumière visible
pers_person_notVisibleFacePhoto=N'inclut pas les photos de visage visible
pers_person_visibleFacePhoto=Photo du visage visible
pers_person_change=Ajustements de personnel
pers_person_cropFaceUsePhoto=Voulez - vous afficher la photo de comparaison comme avatar?
pers_person_photoUseCropFace=Voulez - vous utiliser un avatar pour générer des photos comparatives?
pers_person_selectCamera=Choisir une caméra de prise de vue
pers_person_delCropFaceMsg=Pas de photos de contraste à supprimer!
pers_person_disabledNotOp=Cette personne est désactivée et ne peut pas fonctionner!
pers_person_visiblePalm=Paume de la main visible
pers_person_notVisiblePalm=Paume visible non incluse
pers_person_selectDisabledNotOp=La personne désactivée est présente dans la personne sélectionnée et ne peut pas fonctionner!
pers_person_photoUseCropFaceAndTempalte=Utilisez - vous des avatars pour générer des photos d'alignement, des modèles de visages?
pers_person_photoUseTempalte=Utilisez - vous des photos pour générer des modèles de visages?
pers_person_createCropFace=Générer une photo d'alignement
pers_person_createFaceTempalte=Générer un modèle de visage
pers_person_faceTempalte=Modèle de visage
pers_person_extractFaceTemplate=Extraire un modèle de visage
pers_person_createSuccess=Générer avec succès
pers_person_createFail=La génération a échoué
pers_person_serverConnectWarn=L'adresse du serveur, le nom d'utilisateur, le mot de passe ne peuvent pas être vides!
pers_person_serverOffline=Serveur d'extraction de modèle de visage hors ligne
pers_person_faceTemplateError1=Échec de la détection des visages
pers_person_faceTemplateError2=Masque de visage
pers_person_faceTemplateError3=Pas assez de clarté
pers_person_faceTemplateError4=Angle de visage trop grand
pers_person_faceTemplateError5=échec de la détection vivante
pers_person_faceTemplateError6=L'extraction du modèle de visage a échoué
pers_person_cropFaceNoExist=La photo n'existe pas
pers_person_disableFaceTemplate=La fonction d'extraction de modèle de visage n'est pas activée, vous ne pouvez pas extraire le modèle!
pers_person_cropFacePhoto=Photos de visages contrastés
pers_person_vislightPalmPhoto=Photos comparatives de paumes
pers_person_serverOfflineWarn=Le serveur d'extraction de modèles de visage est hors ligne et cette fonctionnalité ne peut pas être activée!
pers_person_serverConnectInfo=S'il vous plaît tester si le serveur d'extraction de modèle de visage est en ligne?
pers_person_notModified=Le numéro de téléphone mobile ne peut pas être modifié.
pers_person_syncAcms=Synchroniser le personnel avec ACMS
pers_person_startUpdate=Commencez à mettre à jour les informations sur le personnel.
pers_person_updateFailed=La mise à jour des informations sur le personnel a échoué!
#控制器发卡
pers_person_readCard=Émettre la carte du périphérique
pers_person_stopRead=Arrêter l'émission
pers_person_chooseDoor=Choisissez la porte
pers_person_readCarding=Émettez la carte, réessayez plus tard!
#抓拍照片
pers_capture_catchPhoto=Capturer Photo
pers_capture_preview=Aperçu
#部门
pers_dept_entity=Département
pers_dept_deptNo=Numéro de département
pers_dept_deptName=Nom du département
pers_dept_parentDeptNo=Numéro du département parent
pers_dept_parentDeptName=Nom du département parent
pers_dept_parentDept=Département parent
pers_dept_note=Si le nouveau département n'apparaît pas dans la liste, veuillez contacter l'administrateur pour autoriser à nouveau l'utilisateur à modifier le département!
pers_dept_exit=Contient
pers_dept_auth=Utilisateur du système lié
pers_dept_parentMenuMsg=Le département supérieur ne peut pas être défini de la même manière qu'un département inférieur.
pers_dept_initDept=Général
pers_dept_deptMarket=Département Marketing
pers_dept_deptRD=Département de développement
pers_dept_deptFinancial=Département financier
pers_dept_nameNoSpace=Le nom du département ne peut ni commencer ni se terminer par des espaces.
pers_dept_nameExist=Le nom du département existe déjà
pers_dept_changeLevel=S'il faut passer au niveau de ce département
pers_dept_noSpecialChar=Le numéro de département ne doit pas contenir de caractères spéciaux!
pers_dept_NameNoSpecialChar=Le nom du département ne doit pas contenir de caractères spéciaux!
pers_dept_noModifiedParent=Le service supérieur ne peut pas être modifié !
#职位
pers_position_entity=Position
pers_position_code=Numéro de position
pers_position_name=Nom de position
pers_position_notExist=La position n'existe pas!
pers_position_sortNo=Trier
pers_position_parentName=Position Parent
pers_position_parentCode=Numéro de position parent
pers_position_batchToPosition=Nouvelle position
pers_position_nameExist=Ce nom de position existe déjà
pers_position_change=Changer de position
pers_position_parentMenuMsg=La position parent ne peut pas être définie comme une sous-position.
pers_position_nameNoSpace=Le nom de position ne peut ni commencer ni se terminer par des espaces.
pers_position_existSub={0} :Contient des sous-publications ne peut pas être supprimé
pers_position_existPerson={0} :Le personnel ne peut pas être supprimé
pers_position_importTemplate Modèle d'importation de position
pers_position_downloadTemplate=Télécharger et importer le modèle
pers_position_codeNotEmpty=Le numéro de position ne peut pas être vide
pers_position_nameNotEmpty=Le nom de la position ne peut pas être vide
pers_position_nameNoSpecialChar=Le nom de la position {0} ne peut pas contenir de caractères spéciaux!
pers_position_noSpecialChar=Le numéro de position {0} ne peut pas être un caractère spécial!
pers_position_codeLength=Le numéro de position {0} dépasse 30 chiffres
pers_position_nameLength=Données dont le nom de position {0} est plus long que {1}
pers_position_codeExist=Le numéro de position {0} existe déjà
#证件
pers_cert_type=Type de certificat
pers_cert_number=Numéro de certificat
pers_cert_name=Nom du certificat
pers_cert_numberExist=Le numéro de certificat existe déjà.
#导出
pers_export_allPersPerson=Toutes les personnes
pers_export_curPersPerson=Personne actuelle
pers_export_template=Exporter le modèle
pers_export_personInfo=Exporter le Personnel
pers_export_personInfoTemplate=Télécharger le modèle d'importation de personnel
pers_export_personBioTemplate=Exporter un modèle biométrique
pers_export_basicInfo=Information de base
pers_export_customAttr=Attributs personnalisés
pers_export_templateComment=Nom du champ, clé primaire? Unique? Autoriser les valeurs nulles?({0},{1},{2},{3})
pers_export_templateFileName=Modèle d'importation de personnel
pers_export_bioTemplateFileName=Modèle biométrique du personnel
pers_export_deptInfo=Exporter Département
pers_export_deptTemplate=Télécharger le modèle d'importation de département
pers_export_deptTemplateFileName=Modèle d'importation de département
pers_export_personPhoto=Exporter photo du personnel
pers_export_allPhotos=Toutes les photos (sélectionner toutes les personnes)
pers_export_selectPhotoToExport=Sélectionnez l'ID de début et l'ID de fin pour exporter les photos du personnel.
pers_export_fromId=De l'ID
pers_export_toId=À
pers_export_certNumberComment=Le type de certificat est requis après avoir rempli le numéro de certificat
pers_export_templateCommentName=Nom du champ:({0})
pers_export_dataExist=Veuillez vous assurer que les données importées existent déjà dans le système
pers_export_cardNoTip=Plusieurs numéros de carte séparés par &
pers_carNumber_importTip=Numéro de plaque d'immatriculation (plaques d'immatriculation multiple& séparé)
#导入
pers_import_certNumberExist=Le numéro de certificat {0} existe déjà.
pers_import_complete=Terminer
pers_import_password=Mot de passe de la personne
pers_import_fail=La {0} ligne a échoué: {1}
pers_import_overData=Vous importez {0} personnes; le système ne peut prendre en charge que l'importation de 30 000 personnes!
pers_import_pinTooLong=ID personnel {0} est trop long!
pers_import_pinExist=ID personnel {0} existe déjà!
pers_import_pinIsRepeat=ID personnel {0} est répété!
pers_import_pinError=ID personnel {0} erreur!
pers_import_pinNotSupportLetter=ID personnel ne supporte pas les lettres anglaises!Le numéro de personnel est: {0}
pers_import_pinNotSupportNonAlphabetic=L'identification du personnel ne prend pas en charge les lettres non anglaises!Le numéro de personnel est: {0}
pers_import_pinNotNull=L'ID personnel ne doit pas être composé uniquement de zéros!
pers_import_pinStartWithZero=L'ID personnel ne peut pas commencer par zéro!
pers_import_cardNoNotSupportLetter=Le numéro de carte ne prend pas en charge les lettres!
pers_import_cardNoNotNull=Le numéro de carte ne peut pas être composé uniquement de zéros!
pers_import_cardNoStartWithZero=Le numéro de carte ne peut pas commencer par zéro!
pers_import_cardTooLong=Numéro de carte {0} est trop long!
pers_import_cardExist=Numéro de carte {0} existe déjà!
pers_import_personPwdOnlyNumber=Le mot de passe personnel ne prend en charge que les chiffres (pas de lettres)!
pers_import_personPwdTooLong=Mot de passe personnel {0} est trop long!
pers_import_personDuressPwd=Mot de passe personnel {0} est répété!
pers_import_emailTooLong=Email {0} est trop long!
pers_import_nameTooLong=Nom {0} est trop long!
pers_import_genderError=Erreur de format du genre!
pers_import_personDateError=Erreur de format de date d'embauche!
pers_import_createTimeError=Erreur de format de création d'heure!
pers_import_phoneError=Erreur de format du numéro de téléphone!
pers_import_phoneTooLong=La longueur du numéro de téléphone ne peut pas dépasser 20 caractères!
pers_import_emailError=Erreur de format de l'email!
pers_import_birthdayError=Erreur de format de date de naissance!
pers_import_nameError=Le nom et le prénom ne peuvent pas contenir de symboles spéciaux!Le numéro de personnel est: {0}
pers_import_firstnameError=Le nom ou le prénom ne peut pas contenir ','!
pers_import_firstnameNotNull=Le prénom ne doit pas être vide!
pers_import_dataCheck=La vérification des données est terminée
pers_import_dataSaveFail=Echec de l'importation des données!
pers_import_allSucceed=Toutes les données ont été importées avec succès!
pers_import_result=Réussi: {0}, Échoué: {1}.
pers_import_result2=Importer Résultat
pers_import_result3=Réussi: {0}, Mis à jour: {1}, Échoué: {2}.
pers_import_notSupportFormat=This Format is not supported!
pers_import_selectCorrectFile=Veuillez sélectionner le bon fichier.
pers_import_fileFormat=Format de fichier
pers_import_targetFile=Fichier de destination
pers_import_startRow=Lignes de début d'en-tête
pers_import_startRowNote=La première ligne du format de données est le nom de la table, la deuxième ligne est l'en-tête, la troisième ligne est les données d'importation, veuillez vérifier le fichier puis importer.
pers_import_delimiter=Délimiteur
pers_import_importingDataFields=Champs de base de données
pers_import_dataSourceFields=Importation de champs de données
pers_import_total=Total
pers_import_dataUpdate=Mettre à jour l'ID personnel existant dans le système:
pers_import_dataIsNull=No data in the file!
pers_import_deptNotExist=Le département n'existe pas!
pers_import_deptIsNotNull=Le nom du département ne doit pas être vide!
pers_import_pinNotEmpty=L'ID personnel ne peut pas être vierge!
pers_import_nameNotEmpty=Le nom du personnel ne peut pas être vide!
pers_import_siteCodeOnlyLetterNum=Erreur de format du code du site.
pers_import_cardNoFormatErrors=Le format du numéro de carte {0} est incorrect.
pers_import_cardsNotSupport=La fonctionnalité [Cartes multiples par personne] est désactivé, ne peut pas programmer plus d'une carte par personne.
pers_import_personInfo=Importer personnel
pers_import_commentFormat=Le format du commentaire n'est pas correct!
pers_import_noComment=Les données en {0} ligne et {1} colonne ne sont pas commentées.
pers_import_fieldRepeat=Les données en {0} ligne et {1} colonne,le nom du champ dans le commentaire est répété:{2}
pers_import_primaryKey=Il doit y avoir au moins un champ comme clé primaire.
pers_import_templateIsRepeat=ID Personnel: Les données du modèle biométrique pour {0} a été dupliqué!
pers_import_biologicalTemplate=Importer un modèle biométrique
pers_import_uploadFileSuccess=Importation réussie, début de l'analyse des données, veuillez patienter...
pers_import_resolutionComplete=Les données ont été analysées, démarrage des mises à jour de la base de données.
pers_import_mustField=Le fichier d'importation doit contenir la colonne {0}.
pers_import_bioTemplateSuccess=Importez modèle biométrique, ces données doivent être synchronisées manuellement avec le périphérique à partir de chaque module métier.
pers_import_personPhoto=Importer photo du personnel
pers_import_opera_log=Journal d'opérations
pers_import_error_log=Journal d'erreur
pers_import_uploadFileSize=Veuillez télécharger un fichier dont la taille ne dépasse pas {0}!
pers_import_uploadFileSizeLimit=Pour une seule importation, veuillez télécharger un fichier dont la taille ne dépasse pas 500M!
pers_import_type=Mode d'importation
pers_import_photoType=Photo
pers_import_archiveType=Package compressé
pers_import_startUpload=Commencer le téléchargement
pers_import_addMore=Ajouter plus
pers_import_photoQuality=Qualité de la photo
pers_import_original=Original
pers_import_adaptive=Adaptatif
pers_import_adaptiveSize=(Taille 480 * 640)
pers_import_totalNumber=Total
pers_import_uploadTip=(Veuillez ne pas supprimer de photo lors de l'importation)
pers_import_addPhotoTip=Veuillez ajouter des photos à importer!
pers_import_selectPhotoTip=Veuillez sélectionner la photo que vous souhaitez importer!
pers_import_uploadResult=Résultat de l'importation de photo
pers_import_pleaseSelectPhoto=Veuillez sélectionner photo
pers_import_multipleSelectTip=Appuyez sur Ctrl pour effectuer plusieurs sélections
pers_import_replacePhotoTip=La photo sélectionnée est déjà dans la liste d'importation, voulez-vous la remplacer?
pers_import_photoNamePinNotCorrespond=Incohérence entre le nom de la photo et l'ID du personnel
pers_import_photoFormatRequirement=Veuillez nommer la photo avec l'ID de l'employé.Le format correct est JPG/PNG.Assurez-vous que le nom de la photo ne contient pas de caractères spéciaux.
pers_import_filterTip=Certaines des photos que vous avez sélectionnées ne peuvent pas être prévisualisées, et il peut y avoir les raisons suivantes:
pers_import_photoContainSpecialCharacters=Le nom de la photo a des caractères spéciaux.
pers_import_photoFormatError=Le format de la photo est incorrect.
pers_import_photoSelectNumber=Ne choisissez pas plus de 3000 photos!
pers_import_photoSelectNumberLimit=Ne choisissez pas plus de {0} images!
pers_import_fileMaxSize=L'image est trop grande, veuillez télécharger un fichier image inférieur à 5M.
pers_import_notUploadPhotoNumber=Pas plus de 3000 images peuvent être importées!
pers_import_zipFileNotPhoto=Il n'y a pas de photo de la personne dans le fichier zip, veuillez resélectionner et importer!
pers_import_personPlateRepeat=Plaque d'immatriculation du personnel {0} dupliqué!
pers_import_filePlateRepeat=Dossier à l'intérieur de la plaque d'immatriculation {0} dupliqué!
pers_import_personPlateFormat=Plaque d'immatriculation du personnel {0} format incorrect!
pers_import_personPlateMax=Le nombre de plaques d'immatriculation du personnel dépasse le maximum de 6!
pers_import_cropFaceFail=Échec de la génération de la photo de comparaison!
pers_import_pinLeaved=ID Personnel: {0} est parti.
pers_import_exceedLicense=Les importations ne sont pas autorisées pour le nombre de personnes qui dépassent la licence du logiciel.
pers_import_bioTemplateNotNull=L'ID du personnel, le type de modèle biométrique, l'ID ou l'index et le contenu, la version ne peuvent pas être vides!
pers_import_certTypeNotNull=Le numéro de personnel est: {0} type de document ne peut pas être vide
pers_import_certTypeNotExist=Le numéro de personne est: {0} le type de document n'existe pas
pers_import_certNumNotNull=Le numéro de personne est: {0} le numéro de certificat ne peut pas être vide
pers_import_certNumberTooLong=Rangée {0}:Numéro ID {1} est trop long!
pers_import_idNumberErrors=Rangée {0}:Numéro de carte ID {1} est mal formé!
pers_import_emailErrors=Rangée {0}:Adresse Email {1} erreur de format!
pers_import_emailIsExist=Adresse Email {0} existe déjà!
pers_import_emailIsRepeat=Rangée {0}: L'adresse e-mail interne du fichier {1} a été répété!
pers_import_fileMobilePhoneRepeat=Rangée {0}: Le numéro de téléphone mobile interne du fichier {1} a été répété !
pers_import_mobilePhoneErrors=Erreur de format de numéro de téléphone portable !
pers_import_hireDateError=TLe format de la date saisie est incorrect!
pers_import_selectPhotoType=Veuillez choisir le type de photos à importer!
pers_import_hireDateLaterCurrent=La date d'embauche ne peut pas être postérieure à la date actuelle!
pers_import_buildingNotExist=Le bâtiment n'existe pas !
pers_import_unitNotExist=L'unité n'existe pas!
pers_import_vdbInfoFail=Il n'y a aucune information sur l'unité nommée {1} dans le bâtiment {0} !
pers_import_vdbBuildingFail=Les informations sur le bâtiment de l'unité {0} ne peuvent pas être vides!
pers_import_vdbRoomNoFail=Le numéro de chambre doit être un nombre supérieur à 0 !
#人员离职
pers_person_leave=Licenciement
pers_dimission_date=Date de licenciement
pers_dimission_type=Type de licenciement
pers_dimission_reason=Motif du licenciement
pers_dimission_volutary=Redondance volontaire
pers_dimission_dismiss=Licencié
pers_dimission_resignat=Démission
pers_dimission_shiftJob=Transfert
pers_dimission_leave=Conserver l'emploi sans salaire
pers_dimission_recovery=Réintégration
pers_dimission_sureToRecovery=Voulez-vous vraiment effectuer l'opération de réintégration?
pers_dimission_backCard=La carte a-t-elle été retournée?
pers_dimission_isForbidAction=Désactivez immédiatement les droits de contrôle d'accès?
pers_dimission_writeInfomation=Entrez les informations de licenciement
pers_dimission_pinRetain=Conserver l'ID personnel de l'employé licencié?
pers_dimission_downloadTemplate=Télécharger le modèle d'importation dimission
pers_dimission_import=Importer des dimissions
pers_dimission_importTemplate=Modèle d'importation Dimission
pers_dimission_date_noNull=La date de dimission ne peut pas être vide
pers_dimission_leaveType_noExist=Le type de dimission n'existe pas
pers_dimission_dateFormat=Champ obligatoire, le format de l'heure est aaaa-MM-jj, tel que: 2020-07-22
pers_dimission_leaveType=Champ obligatoire, tel que: Redondance volontaire, Licencié, Démission, Transfert
pers_dimission_forbidden=Désactiver
pers_dimission_leaveType_noNull=Le type de congé ne peut pas être vide!
pers_dimission_person_noExist=Le licenciement n'existe pas!
pers_dimission_date_error=Date de licenciement non remplie ou Format incorrect
#临时人员
pers_tempPerson_audit=Réviser
pers_tempPerson_view=Afficher
pers_tempPerson_waitReview=En attente de la validation de l'administrateur
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Numéro de carte
pers_card_state=État de la carte
pers_card_effect=Effective
pers_card_disabled=Invalide
pers_card_past=Expiré
pers_card_back=Carte retournée
pers_card_change=Changement de carte
pers_card_note=Le numéro de carte existe déjà.
pers_card_numTooBig=La longueur du numéro de carte d'origine est trop longue.
pers_issueCard_entity=Emettre carte
pers_issueCard_operator=Opérateur
pers_issueCard_operate=Action
pers_issueCard_note=Les émetteurs de cartes opérant pour les données du personnel enregistré mais pas le personnel du compte de la carte d'enregistrement!
pers_issueCard_date=Date d'émission de la carte
pers_issueCard_changeTime=Changer temps
pers_issueCard_cardValidate=Pas d'espace autorisé.
pers_issueCard_cardEmptyNote=La carte ne doit pas être vide.
pers_issueCard_cardHasBeenIssued=Cette carte a été émise!
pers_issueCard_noCardPerson=Personne non émise
pers_issueCard_waitPerson=Personne courante émise
pers_issueCard_mc5000=Carte d'émission MC5000
pers_batchIssCard_entity=Emission de Carte par lots
pers_batchIssCard_startPersNo=Commencer ID Personnel
pers_batchIssCard_endPersNo=Terminer ID Personnel
pers_batchIssCard_issCardNum=Nombre de cartes émises
pers_batchIssCard_notIssCardNum=Nombre de personnes sans carte émise
pers_batchIssCard_generateList=Générer liste
pers_batchIssCard_startRead=Commencez à lire
pers_batchIssCard_swipCard=Position de glissage de la carte
pers_batchIssCard_sendCard=Périphérique
pers_batchIssCard_dispenCardIss=Lecteur USB
pers_batchIssCard_usbEncoder=Encodeur USB
pers_batchIssCard_note=L'ID personnel ne prend en charge que les valeurs saisies et ne montre que les personnes sans carte (max. 300)!
pers_batchIssCard_startPinEmpty=L'ID personnel de départ ne peut pas être vide!
pers_batchIssCard_endPinEmpty=L'ID du personnel final ne doit pas être vide!
pers_batchIssCard_startPinLargeThanEndPin=L'ID du personnel de départ ne peut pas être supérieur à l'ID du personnel final!
pers_batchIssCard_numberParagraphNoPerson=La personne sans carte délivrée n'existe pas dans le numéro de segment d'identification du début à la fin!
pers_batchIssCard_inputCardNum=Saisir numéro de carte
pers_batchIssCard_cardLetter=La carte ne prend pas en charge les lettres!
pers_batchIssCard_cardNoTooLong=La longueur du numéro de carte est trop longue!
pers_batchIssCard_issueWay=Méthode d'inscription de la carte
pers_batchIssCard_noPersonList=Liste du personnel pas encore générée.
pers_batchIssCard_startReadCard=Commencer à lire la carte
pers_batchIssCard_swipePosition=Position de glissage
pers_batchIssCard_chooseSwipePosition=Veuillez choisir la position de glissage.
pers_batchIssCard_readCardTip=Le périphérique ne lit la carte non enregistrée que lorsque la méthode d'émission est un lecteur sélectionné.
pers_batchIssCard_notIssCardNo=Nombre de cartes non émises
pers_batchIssCard_totalNumOfCards=Le nombre total de cartes
pers_batchIssCard_acms=ACMS émet des cartes
pers_lossCard_entity=Carte perdue signalée
pers_lossCard_lost=Cette carte a été signalée, ne peut pas répéter l'opération!
pers_losscard_note2=Après avoir écrit la carte de gestion, vous devez glisser cette carte dans le lecteur d'ascenseur pour vous assurer que les cartes perdent leur effet dans le périphérique.
pers_revertCard_entity=Réactiver carte perdue
pers_revertCard_setReport=Veuillez d'abord signaler la perte de la carte!
pers_revertcard_note2=Après avoir écrit la carte de gestion, vous devez glisser cette carte sur le lecteur d'ascenseur pour vous assurer que vous pouvez réutiliser les cartes.
pers_issueCard_success=L'émission de la carte a réussi!
pers_issueCard_error=Échec de l'émission de la carte!
pers_cardData_error=Exception de la lecture du format des données de la carte!
pers_analysis_error=Exception d'analyse des données de la carte!
pers_cardOperation_error=Exception d'opération de la carte!
pers_cardPacket_error=Exception de package de commande d'opération de carte!
pers_card_write=Écrire la carte
pers_card_init=Initialiser la carte
pers_card_loss=Carte perdue
pers_card_revert=Rétablir la carte
pers_card_writeMgr=Ecrire la Carte de gestion
pers_initCard_tip=Après l'initialisation, la carte deviendra une carte vierge!
pers_initCard_prepare=Prêt à initialiser la carte...
pers_initCard_process=Initialisation de la carte...
pers_initCard_success=Initialisation de la carte avec succès
pers_mgrCard_prepare=Préparez-vous à écrire les données de la carte de gestion...
pers_mgrCard_process=Écrit des données de carte de gestion...
pers_mgrCard_success=Réussite de l'écriture de la carte de gestion
pers_userCard_prepare=Préparation de l'écriture de carte d'utilisateur...
pers_userCard_process=Écriture des données de la carte utilisateur...
pers_userCard_success=Écriture de la carte d'utilisateur avec succès
pers_userCard_tip=Veuillez définir l'heure de début et l'heure de fin dans la page d'édition de la personne, puis écrire l'opération de la carte.
pers_userCard_tip2=Les données d'autorisation sont vides, impossible d'écrire des cartes.
pers_userCard_tip3=Appareil associé au groupe d'autorités de plus de deux unités, perte de données.
pers_writeCard_tip=Veuillez vous assurer que vous avez connecté l'encodeur et installé le pilote, et placez la carte sur l'encodeur.
pers_writeMgrCard_tip=La quantité de cartes perdues et de cartes rétablies ne peut pas être supérieure à 18
pers_writeMgrCard_tip2=Le numéro de la carte perdue et de la carte rétablie:
pers_card_writeToMgr=Écrit sur la carte de gestion
pers_card_hex=Affichage du format de la carte
pers_card_decimal=Décimal
pers_card_Hexadecimal=Hexadécimal
pers_card_IssuedCommandFail=Échec de la commande émise:
pers_card_multiCard=Plus de cartes
pers_card_deputyCard=Carte secondaire
pers_card_deputyCardValid=Veuillez d'abord saisir la carte principale!
pers_card_writePinFormat=Le système peut uniquement associer des ID personnels sans lettres à la carte!
pers_card_notMoreThanSixteen=La quantité de cartes secondaires ne peut pas être supérieure à 16.
pers_card_notDelAll=Ne peut pas retirer toutes les cartes secondaires.
pers_card_maxCard=Le numéro de carte ne peut pas dépasser {0}.
pers_card_posUseCardNo=La carte principale de la personne est utilisée par le module consommateur. Veuillez accéder au module consommateur pour effectuer l'opération de retrait de la carte!
pers_card_delFirst=Veuillez supprimer le numéro de carte émis avant de l'émettre!
pers_card_disablePersonWarn=Le numéro de carte sélectionné contient une personne handicapée et ne peut pas fonctionner!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Code du site
pers_wiegandFmt_wiegandMode=Mode
pers_wiegandFmt_wiegandModeOne=Mode un
pers_wiegandFmt_wiegandModeTwo=Mode deux
pers_wiegandFmt_isDefaultFmt=Auto
pers_wgFmt_entity=Format Wiegand
pers_wgFmt_in=Format d'entrée Wiegand
pers_wgFmt_out=Format de sortie Wiegand
pers_wgFmt_inType=Type d'entrée Wiegand
pers_wgFmt_outType=Type de sortie Wiegand
pers_wgFmt_wg=Format Wiegand
pers_wgFmt_totalBit=Bit total
pers_wgFmt_oddPch=Contrôle de parité impaire(o)
pers_wgFmt_evenPck=Contrôle de parité pair(e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=Code de facilité(f)
pers_wgFmt_siteCode=Code du site(s)
pers_wgFmt_manufactoryCode=Code fabricant(m)
pers_wgFmt_firstParity=Premier contrôle de parité(p)
pers_wgFmt_secondParity=Deuxième contrôle de parité(p)
pers_wgFmt_cardFmt=Format de vérification de carte
pers_wgFmt_parityFmt=Format de contrôle de parité
pers_wgFmt_startBit=Bit de départ
pers_wgFmt_test=Test de format de carte
pers_wgFmt_error=Erreur de format de carte!
pers_wgFmt_verify1=Le bit total ne doit pas dépasser 80!
pers_wgFmt_verify2=Vérifier que la longueur du format de la carte est égale au nombre total de bits!
pers_wgFmt_verify3=La longueur du format de parité doit être égale au nombre total de chiffres!
pers_wgFmt_verify4=Les données initialisées ne peuvent pas être supprimées!
pers_wgFmt_verify5=Le format de la carte est utilisé, ne peut pas être supprimé!
pers_wgFmt_verify6=Le premier bit de parité ne peut pas être supérieur au nombre total de!
pers_wgFmt_verify7=Le deuxième bit de parité ne peut pas être supérieur au nombre total de!
pers_wgFmt_verify8=Le format de début et de longueur maximale n'est pas correct!
pers_wgFmt_verify9=La longueur du format de vérification de la carte ne peut pas être supérieure au nombre total de!
pers_wgFmt_verify10=La fonction de chiffre de contrôle de la carte ne peut pas traverser!
pers_wgFmt_verify11=Le code du site dépasse la plage définie!
pers_wgFmt_verify=Vérifier
pers_wgFmt_unverify=Pas Vérifier
pers_wgFmt_atLeastDefaultFmt=Veuillez conserver au moins un format de carte correspondant automatiquement!
pers_wgFmt_defaultFmtError1=Il existe un autre format de carte avec les mêmes bits de numéro de carte, ne peut pas être défini sur le format de carte correspondant automatiquement!
pers_wgFmt_defaultFmtError2=Présence du même format de carte à la correspondance automatique, l'opération a échoué!
pers_wgFmt_cardFormats=Formats de carte
pers_wgFmt_cardFormatTesting=Test des formats de carte
pers_wgFmt_checkIsUsed=Le format de la carte est utilisé dans {0} et ne peut pas être supprimé!
pers_wgFmt_supportDigitsNumber=Veuillez entrer la longueur du nombre de chiffres pris en charge par votre appareil
#选人控件
pers_widget_selectPerson=Sélectionnez personnel
pers_widget_searchType1=Requête
pers_widget_searchType2=Département
pers_widget_deptHint=Note: Importation de tout le personnel des départements sélectionnés
pers_widget_noPerson=Aucun personnel n'a été sélectionné.
pers_widget_noDept=Veuillez sélectionner un département.
pers_widget_noDeptPerson=Aucune personne sous le département sélectionné, veuillez resélectionner!
#人员属性
pers_person_carPlate=Plaque d'immatriculation
pers_person_socialSecurity=Numéro de sécurité sociale
pers_person_msg4=La longueur maximale ne doit pas dépasser 20!
pers_person_msg5=Le nom ne peut pas contenir plus de 50 caractères!
pers_person_type=Type de personne
pers_person_reseCode=Mot de passe d'auto-connexion
pers_person_IsSendMail=Notification email
pers_person_inactive=Inactif
pers_person_active=Actif
pers_person_employee=Employé
pers_person_isSendMailMsg=Utiliser la fonction [Notification d'événement], vous devez d'abord saisir l'e-mail.
pers_person_createTime=Créer temps
pers_person_pinFirstValid=Le premier caractère du numéro de personnel ne peut pas être 8 ou 9.
pers_person_attrValueValid=La valeur du champ ne peut pas être répétée.
pers_person_attrValueDelimiterValid=Le séparateur doit être au milieu.
pers_person_isSendSMS=SMS notification
pers_person_building=Nom du bâtiment
pers_person_unitName=Nom de l'unité
pers_person_roomNo=Numéro de la chambre
#动态属性
pers_attr_emp_type=Type d'employé
pers_attr_street=Rue
pers_attr_nation=Nation
pers_attr_office_address=Adresse de bureau
pers_attr_postcode=Code postal
pers_attr_office_phone=Téléphone de bureau
pers_attr_home_phone=Téléphone de domicile
pers_attr_job_title=Profession
pers_attr_birthplace=Lieu de naissance
pers_attr_polit_status=Statut politique
pers_attr_country=Pays
pers_attr_home_address=Adresse du domicile
pers_attr_hire_type=Type d'embauche
pers_attr_inContract=Travailleur contractuel
pers_attr_outContract=Travailleur non contractuel
#属性自定义
pers_attribute_attrName=Afficher nom
pers_attribute_attrValue=Valeur d'attribut
pers_attribute_controlType=Type d'entrée
pers_attribute_positionX=Rangée
pers_attribute_positionY=Colonne
pers_attribute_showTable=Afficher dans la liste des personnes
pers_attrDefini_deletemsg=Cette propriété a été utilisée. Etes-vous sûr de vouloir le supprimer?
pers_attrDefini_reserved=Nom de champ réservé au système.
pers_attrDefini_msg1=La longueur maximale ne dépassant pas 30!
pers_attrDefini_msg2=Les lignes de la colonne existent déjà, veuillez changer d'emplacement!
pers_attrDefini_attrValue_split=Utilisant un  \' ; \' délimiteur.
pers_attrDefini_attrName=Nom d'attribut
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=ID d'attribut
pers_attrDefini_select=Liste déroulante
pers_attrDefini_check=Choix multiple
pers_attrDefini_radio=Choix unique
pers_attrDefini_text=Texte
pers_attrDefini_maxCol=Pas plus de 2 colonnes.
pers_attrDefini_maxLimit=Les attributs personnalisés ont atteint le nombre maximum!
pers_attrDefini_modControlType=La modification du type d'entrée effacera les données de champ actuelles pour tout le personnel du système.s'il faut continuer?
#leavePerson
pers_leavePerson_reinstated=Réintégration
#opExample
pers_example_newRecode=Acquisition de nouveaux enregistrements
pers_example_allRecode=Accès à tous les enregistrements
pers_custField_StatisticalType=Type statistique
#人员参数修改
pers_param_isAudit=Activer l'audit automatique
pers_param_donotChangePin=L'ID personnel existant contient lettre(s), ne peut pas désactiver la fonction [Supporte les Lettres].
pers_param_hexChangeWarn=Le système actuel a déjà les numéros de carte, ne peut pas changer le mode d'affichage du format de la carte.
pers_param_cardsChangeWarn=Il y a des gens avec plus d'une carte dans le système, ne peut pas désactiver la fonction [Cartes Multiples par Personne].
pers_param_maxPinLength=La longueur du nouvel ID personnel ne peut pas être inférieure à la longueur de l'ID personnel existant dans le système.
pers_param_pinBeyondDevLength=La longueur maximale de l'ID du personnel prise en charge par le périphérique dans le système est de {0}, veuillez saisir un entier inférieur à {1}.
pers_param_cardBeyondDevLength=La longueur maximale du numéro de carte prise en charge par l'appareil dans le système est {0}, veuillez saisir un entier inférieur à {1}.
pers_param_checkIsExistNoAudit=Il y a des inscrits non révisés dans le système actuel et ne peuvent pas être modifiés pour un examen automatique!
pers_param_noSupportPinLetter=Il y a des périphériques dans le système qui ne prennent pas en charge les lettres contenant l'ID personnel, ne peuvent pas activer la fonction [Supporte Lettres].
pers_param_changePinLettersTip=Le numéro personnel prend en charge l'incrémentation automatique, ne peut pas modifier les modes de numéro personnel
pers_param_changePinIncrementTip=La prise en charge du numéro de personne comprend des lettres, ne peut pas être modifié en mode numéro de personnel
pers_param_qrCode=Code QR d'Entreprise
pers_param_employeeRegistrar=Activer l'enregistrement cloud des employés
pers_param_downloadQRCodePic=Télécharger l'image du code QR
pers_param_qrCodeUrl=URL du code QR
pers_param_qrCodeUrlCreate=Inscription en libre-service
pers_param_qrCodeUrlHref=Adresse du serveur:Port
pers_param_pinSetWarn=Il y a déjà du personnel dans le système actuel, et le mode du numéro de personnel ne peut pas être modifié.
pers_param_selfRegistration=Activer l'auto-inscription
pers_param_infoProtection=Protection des informations personnelles sensibles
pers_param_infoProtectionWarnMsg=Après avoir activé l'option de protection de la sécurité des informations personnelles sensibles, les données personnelles sensibles impliquées dans ce module seront désensibilisées ou masquées, y compris, mais sans s'y limiter, les noms, numéros de carte, numéros d'identification, photos, etc.
pers_param_templateServer=Serveur d'extraction de modèle de visage
pers_param_enableFacialTemplate=Activer l'extraction de modèle de visage
pers_param_templateServerAddr=Modèle de visage extraire l'adresse du serveur
pers_param_templateServerWarnInfo=Lorsque l'extraction de modèle de visage est activée, la personne extrait le modèle de visage par défaut lors du contraste de photos lorsque le serveur d'extraction de modèle de visage est en ligne et que la vérification de l'utilisateur est validée; Ne pas extraire les modèles de visage lorsque le serveur d'extraction de modèles de visage est en mode hors ligne!
pers_param_templateServerWarnInfo1=Lorsque vous activez l'extraction de modèles de visage, vous devez connecter un appareil qui prend en charge l'extraction de modèles de visage!
pers_param_templateServerOffline=Le serveur d'extraction de modèles de visage est hors ligne et ne peut pas extraire les modèles de visage! Voulez - vous continuer?
pers_param_faceServer=Service de comparaison de back office de visage
pers_param_enableFaceVerify=Activer l'alignement en arrière - plan des visages
pers_param_faceServerAddr=Comparaison d'arrière - plan de visage adresse de service
pers_param_faceServerSecret=Comparaison de back office de visage clé de service
#国籍
pers_person_nationality=Nationalité
pers_nationality_angola=Angolais
pers_nationality_afghanistan=Afghan
pers_nationality_albania=albanais
pers_nationality_algeria=Algérien
pers_nationality_america=Américain
pers_nationality_andorra=Andorran
pers_nationality_anguilla=Anguillais
pers_nationality_antAndBar=Antigua-et-Barbuda
pers_nationality_argentina=Argentin
pers_nationality_armenia=Arméniens
pers_nationality_ascension=Ascensois
pers_nationality_australia=Australien
pers_nationality_austria=Autrichien
pers_nationality_azerbaijan=Azerbaïdjanais
pers_nationality_bahamas=Bahamien
pers_nationality_bahrain=Bahreinien
pers_nationality_bangladesh=Bangladais
pers_nationality_barbados=Barbadien
pers_nationality_belarus=Biélorusse
pers_nationality_belgium=Belge
pers_nationality_belize=Belizien
pers_nationality_benin=Béninois
pers_nationality_bermudaIs=Bermudien
pers_nationality_bolivia=Bolivien
pers_nationality_botswana=Botswanais
pers_nationality_brazil=Brésilien
pers_nationality_brunei=Brunéien
pers_nationality_bulgaria=Bulgare
pers_nationality_burkinaFaso=Burkinabé
pers_nationality_burma=Birman
pers_nationality_burundi=Burundais
pers_nationality_cameroon=Camerounais
pers_nationality_canada=Canadien
pers_nationality_caymanIs=Caïmanien
pers_nationality_cenAfrRepub=Centrafricain
pers_nationality_chad=Tchadien
pers_nationality_chile=Chilien
pers_nationality_china=Chinois
pers_nationality_colombia=Colombien
pers_nationality_congo=Congolais
pers_nationality_cookIs=Cookien
pers_nationality_costaRica=Costaricain
pers_nationality_cuba=Cubain
pers_nationality_cyprus=Chypriote
pers_nationality_czechRep=Tchèque
pers_nationality_denmark=Danois
pers_nationality_djibouti=Djiboutien
pers_nationality_dominicaRep=Dominicain
pers_nationality_ecuador=Équatorien
pers_nationality_egypt=Égyptien
pers_nationality_eISalvador=Salvadorien
pers_nationality_england=Britanique
pers_nationality_estonia=Estonien
pers_nationality_ethiopia=Ethiopien
pers_nationality_fiji=Fidjien
pers_nationality_finland=Finlandais
pers_nationality_france=Français
pers_nationality_freGui=Guyanais
pers_nationality_gabon=Gabonais
pers_nationality_gambia=Gambien
pers_nationality_georgia=Georgien
pers_nationality_germany=Allemand
pers_nationality_ghana=Ghanéen
pers_nationality_gibraltarm=Gibraltarien
pers_nationality_greece=Grec
pers_nationality_grenada=Grenadien
pers_nationality_guam=Guamien
pers_nationality_guatemala=guatémaltèque
pers_nationality_guinea=Guinéen
pers_nationality_guyana=Guyanien
pers_nationality_haiti=Haïtien
pers_nationality_honduras=Hondurien
pers_nationality_hungary=Hongrois
pers_nationality_iceland=Islandais
pers_nationality_india=Indien
pers_nationality_indonesia=Indonésien
pers_nationality_iran=Iranien
pers_nationality_iraq=Irakien
pers_nationality_ireland=Irlandais
pers_nationality_israel=Israélien
pers_nationality_italy=Italien
pers_nationality_ivoryCoast=Ivoirien
pers_nationality_jamaica=Jamaïquain
pers_nationality_japan=Japonais
pers_nationality_jordan=Jordanien
pers_nationality_kenya=Kenyan
pers_nationality_korea=Coréen
pers_nationality_kuwait=Koweïtien
pers_nationality_kyrgyzstan=Kirghizistan
pers_nationality_laos=Laotien
pers_nationality_latvia=letton
pers_nationality_lebanon=libanais
pers_nationality_lesotho=Lesothan
pers_nationality_liberia=Libérien
pers_nationality_libya=Libyen
pers_nationality_liechtenstein=Liechtensteinois
pers_nationality_lithuania=Lituanien
pers_nationality_luxembourg=Luxembourgeois
pers_nationality_madagascar=Malgache
pers_nationality_malawi=Malawien
pers_nationality_malaysia=Malaisien
pers_nationality_maldives=Maldivien
pers_nationality_mali=Malien
pers_nationality_malta=maltais
pers_nationality_marianaIs=Mariana Is.
pers_nationality_martinique=Martiniquais
pers_nationality_mauritius=Mauricien
pers_nationality_mexico=Mexicain
pers_nationality_moldova=Moldave
pers_nationality_monaco=Monegasque
pers_nationality_montseIs=Montserrat Is.
pers_nationality_morocco=Marocain
pers_nationality_mozambique=Mozambicain
pers_nationality_namibia=Namibien
pers_nationality_nauru=Nauruan
pers_nationality_nepal=Népalais
pers_nationality_netAnti=Antillais (Néerlandais)
pers_nationality_netherlands=Néerlandais
pers_nationality_newZealand=Néo-Zélandais
pers_nationality_nicaragua=Nicaraguayen
pers_nationality_niger=Nigérien
pers_nationality_nigeria=Nigérian
pers_nationality_norKorea=Nord coréen
pers_nationality_norway=Norvégien
pers_nationality_oman=Omanais
pers_nationality_pakistan=Pakistanais
pers_nationality_panama=Panaméen
pers_nationality_papNewCui=Papouasie-Nouvelle-Guinée
pers_nationality_paraguay=Paraguayen
pers_nationality_peru=Péruvien
pers_nationality_philippines=Philippin
pers_nationality_poland=Polonais
pers_nationality_frenPolyne=Polynésien
pers_nationality_portugal=Portugais
pers_nationality_puerRico=Portoricain
pers_nationality_qatar=Qatari
pers_nationality_reunion=Réunionnais
pers_nationality_romania=Roumain
pers_nationality_russia=Russe
pers_nationality_saiLueia=Saint-Lucien
pers_nationality_saintVinc=Antéjacien
pers_nationality_samoa_eastern=Samoan (Orientale)
pers_nationality_samoa_western=Samoan (Occidentale)
pers_nationality_sanMarino=San Marinaise
pers_nationality_saoAndPrinc=Sao Tomé et Principe
pers_nationality_sauArabia=Saoudien
pers_nationality_senegal=Sénégalais
pers_nationality_seychelles=Seychellois
pers_nationality_sieLeone=Sierra-Léonais
pers_nationality_singapore=Singapourien
pers_nationality_slovakia=Slovaque
pers_nationality_slovenia=Slovène
pers_nationality_solomonIs=Salomonais
pers_nationality_somali=Somalien
pers_nationality_souAfrica=Sud africain
pers_nationality_spain=Espagnol
pers_nationality_sriLanka=Sri lankais
pers_nationality_sudan=soudanais
pers_nationality_suriname=Surinamais
pers_nationality_swaziland=Swazi
pers_nationality_sweden=Suédois
pers_nationality_switzerland=Suisse
pers_nationality_syria=Syrien
pers_nationality_tajikstan=Tadjik
pers_nationality_tanzania=Tanzanien
pers_nationality_thailand=Thaïlandais
pers_nationality_togo=Togolais
pers_nationality_tonga=Tonguien
pers_nationality_triAndToba=Trinidadien
pers_nationality_tunisia=Tunisien
pers_nationality_turkey=Turc
pers_nationality_turkmenistan=Turkmène
pers_nationality_uganda=Ougandais
pers_nationality_ukraine=Ukrainien
pers_nationality_uniArabEmira=Émirien
pers_nationality_uruguay=Uruguayen
pers_nationality_uzbekistan=Ouzbék
pers_nationality_venezuela=Vénézuélien
pers_nationality_vietnam=Vietnamien
pers_nationality_yemen=Yéménite
pers_nationality_serbia=Serbe
pers_nationality_zimbabwe=Zimbabwéen
pers_nationality_zambia=Zambien
pers_nationality_aruba=Arubais
pers_nationality_bhutan=Bhoutanais
pers_nationality_bosnia_herzegovina=Bosnien
pers_nationality_cambodia=Cambodgien
pers_nationality_congoD=République Démocratique du Congo
pers_nationality_comoros=Union des Comores
pers_nationality_capeVerde=République du Cap-Vert
pers_nationality_croatia=Croate
pers_nationality_dominica=Dominique
pers_nationality_eritrea=Érythréen
pers_nationality_micronesia=Micronésien
pers_nationalit_guineaBissau=Bissao-Guinéen
pers_nationalit_equatorialGuinea=Équato-Guinéen
pers_nationalit_hongkong=Hongkongais
pers_nationalit_virginIslands=Les îles Vierges américaines
pers_nationalit_britishVirginIslands=Les îles Vierges britanniques
pers_nationalit_kiribati=Kiribatien
pers_nationalit_mongolia=Mongole
pers_nationalit_marshall=Ile Marshall
pers_nationalit_macedonia=Macédonien
pers_nationalit_montenegro=Monténégro
pers_nationalit_mauritania=Mauritanien
pers_nationalit_palestine=Palestinien
pers_nationalit_palau=Palaosien
pers_nationalit_rwanda=Rwandais
pers_nationalit_saintKittsNevis=Saint-Christophe-et-Niévès
pers_nationalit_timorLeste=Timor Leste
pers_nationalit_taiwan=Taïwanais
pers_nationalit_tuvalu=Tuvaluan
pers_nationalit_vanuatu=Vanuatuan
#制卡
pers_person_cardprint=Imprimer la carte
pers_cardTemplate_tempSelect=Modèle de carte
pers_cardTemplate_printerSelect=Imprimante
pers_cardTemplate_front=De face
pers_cardTemplate_opposite=Retour
pers_cardTemplate_entryDate=Date d'embauche
pers_cardTemplate_photo=Photo
pers_cardTemplate_uploadFail=Échec du téléchargement de l'image!
pers_cardTemplate_jpgFormat=Prend uniquement en charge le téléchargement d'images au format JPG!
pers_cardTemplate_printStatus=Statut d'impression
pers_cardTemplate_waiting=En attente
pers_cardTemplate_printing=Impression
pers_cardTemplate_printOption=Options d'impression
pers_cardTemplate_duplexPrint=Impression double face
pers_cardTemplate_frontOnly=Imprimer uniquement le recto
#app
pers_app_delPers=Il n'y a personne sur le serveur que vous souhaitez supprimer
pers_app_deptIsNull=Ne peut pas trouver le numéro ou le nom du département correspondant au département
pers_app_personNull=La personne n'existe pas
pers_app_pinExist=L'ID personnel existe déjà
pers_app_dateError=Le format de la date est incorrect, veuillez vous référer au format correct:2016-08-08
#api
pers_api_selectPhotoInvalid=La photo n'est pas valide, veuillez la télécharger à nouveau
pers_api_dateError=Le format de date est incorrect
pers_api_personNotExist=La personne n'existe pas
pers_api_cardsPersSupport=Le système n'ouvre pas la carte; la carte n'est pas valide.
pers_api_department_codeOrNameNotNull=Le code ou le nom du service ne peut pas être vide
pers_api_deptSortNoIsNull=Le tri par département ne peut pas être vide !
pers_api_deptSortNoError=La valeur du tri par rayon doit être comprise entre 1-999999!
pers_api_dataLimit=L'opérande actuel est {0}, ce qui dépasse la limite de {1}. Veuillez opérer par lots!
pers_api_cardTypeError=Erreur de type de carte
#人员生物模板API
pers_api_fingerprintExisted=L'empreinte digitale de la personne existe déjà
pers_api_validtypeIncorrect=Cette valeur d'attribut validtype est incorrecte
pers_api_dataNotExist=Numéro de Modèle n'existe pas
pers_api_templateNoRang=Veuillez saisir la bonne valeur de No. de modèle dans la plage 0-9
pers_api_templateIsNull=Le Template ne peut pas être vide!
pers_api_versionIsNumber=Version seuls les chiffres peuvent être saisis!
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=Pin ne peut être qu'un nombre!
#biotime
pers_h5_personAvatarNotNull=L'avatar est vide
pers_h5_personMobileRepeat=Le numéro de mobile existe déjà
pers_h5_personEmailRepeat=La boîte aux lettres existe déjà
pers_h5_pwdIsRepetition=Répétition de Nouveau et ancien mot de passe
pers_h5_personIdNull=ID du Staff est vide
pers_h5_pinOrEmailIsNull=Veuillez saisir le numéro et l'adresse e-mail
pers_emailTitle_resetPassword=changer le mot de passe
pers_emailContent_resetPassword=Le lien est valable 24 heures, copiez le lien dans le navigateur pour changer le mot de passe:
pers_h5_tokenIsNull=Le bon est vide
pers_h5_tokenError=Erreur de bon
pers_h5_oldPwdIsError=L'ancien mot de passe n'est pas renseigné correctement
pers_api_resetPasswordSuccess=le mot de passe a été mis à jour
pers_api_resetPasswordFail=Le changement de mot de passe a échoué
pers_api_forgetPassword=mot de passe oublié
pers_api_confirmSubmit=confirmer la soumission
pers_api_confirmPwdCaution=Cliquez [OK] pour confirmer le nouveau mot de passe.
pers_api_pwdRule=Le mot de passe doit contenir au moins un symbole ou un chiffre et comporter au moins 8 à 12 caractères
pers_h5_personPinFormatNumber=Le numéro personnel ne peut être composé que de chiffres ou de lettres
pers_h5_persEmailNoExist=Erreur de remplissage de la boîte aux lettres
pers_h5_pageNull=Erreur de paramètre de pagination
pers_h5_personPinNotStartWithZero=Le numéro de personnel ne peut pas commencer par 0
pers_h5_personPinTooLong=Le numéro personnel est trop long
pers_h5_personPinInValid=Ce numéro est déjà utilisé
pers_h5_imgSizeError=Veuillez télécharger une image qui n'est pas plus grande que 10M!
pers_h5_confirmAndContinue=Confirmer et continuer
#人脸抠图不及格错误
pers_face_poorResolution=Picture resolution below 80000 pixels
pers_face_noFace=No face detected
pers_face_manyFace=Multiple faces detected
pers_face_smallFace=Face ratio is too small
pers_face_notColor=The picture is a non-color picture
pers_face_seriousBlur=Image is blurred
pers_face_intensivelyLight=Image is heavily exposed
pers_face_badIllumination=Picture is too dark
pers_face_highNoise=Pictures with high noise
pers_face_highStretch=Overstretched face
pers_face_covered=Face is covered
pers_face_smileOpenMouth=Excessive smile
pers_face_largeAngle=The face deflection angle is too large
pers_face_criticalIllumination=Image brightness critical
pers_face_criticalLargeAngle=Critical face deflection angle
pers_face_validFailMsg=Face detection failed due to:
pers_face_failType=Type d'échec de découpe de face
pers_face_photoFormatError=Le format de la photo est incorrect, veuillez télécharger un fichier au format JPG / PNG.
pers_face_notUpdateMsg=Échec de la génération de l'image du visage, ne mettez pas à jour l'image du visage.
#健康申报
pers_health_enable=Activer la déclaration d'informations sur la santé
pers_health_attrExposure=Toute exposition à des cas suspects
pers_health_attrSymptom=Quels sont les symptômes des 14 derniers jours
pers_health_attrVisitCity=Villes visitées au cours des 14 derniers jours
pers_health_attrRemarks=Remarques sur la santé
pers_health_symptomCough=toux
pers_health_symptomFever=Fiebre
pers_health_symptomPolypena=Mauvaise respiration
pers_health_declaration=Déclaration de santé
pers_health_aggrement=J'ai accepté que les personnes qui ne remplissent pas les informations requises seront interdites d'accès, et les visiteurs qui ne rapportent pas les informations de manière véridique ne peuvent pas continuer à accéder et devront assumer les responsabilités légales correspondantes.
pers_health_visitCity_notEmpty=La ville visitée ne peut pas être vide!
pers_health_notAgree=Vous devez choisir d'accepter l'accord pour continuer
#人员名单库
pers_personnal_list_manager=Gestionnaire de liste
pers_personnal_list=Bibliothèque de listes
pers_personnal_list_scheme=Mode domaine
pers_personnal_list_name=Nom de la liste personnelle
pers_personnal_list_group_str_id=ID du groupe de liste
pers_personnal_list_personCount=Nombre de personnes
pers_personnal_list_tag=Défini par l'utilisateur
pers_personnal_list_type=Type de liste personnelle
pers_personnallist_addPerson_repo=Ajouter une personne au dépôt
pers_personnallist_sendPersonnallist=Bibliothèque de listes distribuées
pers_personnallist_sendPerson=Émetteur
pers_personnallist_notDel_existPerson=La bibliothèque de listes contient des personnes et ne peut pas être supprimée
pers_personnallist_peopleInRoster=Il y a encore des personnes dans la bibliothèque de listes
pers_personnallist_associationNotExist=L'association entre le périphérique principal et la bibliothèque de listes n'existe pas
pers_personnal_list_person=Personne de la liste personnelle
pers_personnal_list_dev=Liste des permissions de la bibliothèque
pers_personnal_list_addDev=Ajouter un appareil
pers_personnal_list_name_isExist=Le nom existe déjà
pers_personnal_bannedList=Bibliothèque de listes interdites
pers_personnal_allowList=Bibliothèque de listes autorisées
pers_personnal_redList=Bibliothèque de la liste rouge
pers_personnal_attGroup=Groupe de présence
pers_personnal_passList=liste de passe
pers_personnal_banList=Liste interdite
pers_personnal_visPassList=Liste des pass visiteurs
pers_personnal_visBanList=Liste des visiteurs interdits
pers_personnal_databaseHasBeenDistributed=La bibliothèque de listes sélectionnée a été distribuée et ne peut pas être supprimée
pers_personnel_sendError_dueTo=Échec de la livraison Raison de l'échec :
pers_personnel_sendError_reson={0} existe dans la liste des interdits, veuillez supprimer et ajouter
#比对照片-样片示例
pers_examplePic_Tip=Devrait répondre aux exigences suivantes :
pers_examplePic_Tip1=1. la couleur de fond est blanc pur et le personnel porte des vêtements sombres ;
pers_examplePic_Tip2=2. Les photos électroniques sont au format de fichier JPG, PNG, JPEG, la plage de pixels recommandée : 480*640 < pixel < 1080*1920 ;
pers_examplePic_Tip3=3. Les portraits sur les photos électroniques doivent ouvrir les yeux et regarder droit devant et s'assurer que les pupilles sont clairement visibles ;
pers_examplePic_Tip4=4. Le portrait sur la photo électronique doit avoir une expression neutre et vous pouvez sourire, mais vous ne devez pas montrer vos dents ;
pers_examplePic_Tip5=5. Le portrait de la photo électronique doit être clair, avec des couleurs naturelles, des calques riches et aucune distorsion évidente. Pas d'ombres, de hautes lumières ou de reflets sur les portraits ou les arrière-plans ; le contraste et la luminosité sont appropriés.
pers_examplePic_description=Exemple correct
pers_examplePic_error=Exemple d'erreur :
pers_examplePic_error1=Expression exagérée (sourire excessif)
pers_examplePic_error2=La lumière est trop sombre
pers_examplePic_error3=Le visage est trop petit (la résolution est trop petite)
pers_applogin_enabled=Activer le login app
pers_applogin_disable=Désactiver app login
pers_applogin_status=App login État d'activation