#pers common.
#here other module can also use the label from pers.
pers_module=人事
pers_common_addPerson=新增人員
pers_common_delPerson=刪除人員
pers_common_personCount=人員數
pers_common_browsePerson=瀏覽人員
#左侧菜单
pers_person_manager=人員管理
pers_person=人員
pers_department=部門
pers_leave=離職人員
pers_tempPerson=待審核人員
pers_attribute=自訂屬性
pers_card_manager=卡管理
pers_card=卡
pers_card_issue=發卡記錄
pers_wiegandFmt=韋根格式
pers_position=職位
#人员
pers_person_female=女
pers_person_male=男
pers_person_pin=人員編號
pers_person_departmentChange=部門調整
pers_personDepartment_changeLevel=部門切換權限
pers_person_gender=性別
pers_person_detailInfo=詳細訊息
pers_person_accSet=門禁設定
pers_person_accSetting=門禁設定
pers_person_attSet=考勤設定
pers_person_eleSet=梯控設定
pers_person_eleSetting=梯控設定
pers_person_parkSet=車牌登記
pers_person_pidSet=人證設定
pers_person_insSet=訊息屏設定
pers_person_aiSet=人臉感知設定
pers_person_payrollSet=薪資設定
pers_person_psgSet=通道設定
pers_person_lockerSet=儲物櫃設定
pers_person_sisSet=安檢設定
pers_person_vdbSet=可視對講設定
pers_person_firstName=名
pers_person_lastName=姓
pers_person_name=姓名
pers_person_wholeName=姓名
pers_person_fullName=全稱
pers_person_cardNum=卡數量
pers_person_deptNum=涉及部門數量
pers_person_dataCount=資料統計
pers_person_regFinger=指紋
pers_person_reg=登記
pers_person_password=設備驗證密碼
pers_person_personDate=聘用日期
pers_person_birthday=出生日期
pers_person_mobilePhone=手機號碼
pers_person_personDevAuth=人員設備權限
pers_person_email=信箱
pers_person_browse=瀏覽
pers_person_authGroup=權限組
pers_person_setEffective=設定有效時間
pers_person_attArea=考勤區域
pers_person_isAtt=是否考勤
pers_person_officialStaff=正式員工
pers_person_probationStaff=試用員工
pers_person_identity_category=身份類別
pers_person_devOpAuth=設備操作權限
pers_person_msg1=物理卡號和區位碼必須同時輸入！
pers_person_msg2=請輸入3-4位的數字。
pers_person_msg3=格式錯誤!
pers_person_imgPixel=(最佳尺寸為120×140)
pers_person_cardLengthDigit=請輸入數字！
pers_person_cardLengthHexadecimal=請輸入數字或abcdef字母！
pers_person_to=至
pers_person_templateCount=指紋數
pers_person_biotemplateCount=生物範本數
pers_person_regFace=臉部
pers_person_regVein=指靜脈
pers_person_faceTemplateCount=臉部數
pers_person_VeinTemplateCount=指靜脈數
pers_person_palmTemplateCount=掌靜脈數
pers_person_cropFace=對比照片
pers_person_cropFaceCount=對比照片數
pers_person_faceBiodataCount=可見光面部數
pers_person_irisCount=虹膜數
pers_person_batchToDept=調動到的部門
pers_person_changeReason=調動原因
pers_person_selectedPerson=選取人員
pers_person_duressPwdError=密碼重複
pers_person_completeDelPerson=刪除
pers_person_recover=恢復
pers_person_nameNoComma=不能包括逗號
pers_person_firstNameNotEmpty=名字不能為空
pers_person_lastNameNotEmpty=姓氏不能為空
pers_person_mobilePhoneValidate=請輸入有效的手機號碼
pers_person_phoneNumberValidate=請輸入有效的電話號碼
pers_person_postcodeValidate=請輸入有效的郵編號碼
pers_person_idCardValidate=請輸入有效的身份證號碼
pers_person_pwdOnlyLetterNum=密碼只能包括字母或是數字
pers_person_certNumOnlyLetterNum=證件號碼只能包括字母或是數字
pers_person_oldDeptEqualsNewDept=原部門和調動部門相同
pers_person_disabled=禁止名單
pers_person_emailError=信箱格式不正確
pers_person_driverPrompt=請安裝身份證硬體驅動！點擊確定下載驅動。
pers_person_readIDCardFailed=刷卡失敗！
pers_person_cardPrompt=請將二代身份證放到讀卡區域...
pers_person_iDCardReadOpenFailed=未檢驗到二代身份證閱讀器！
pers_person_iDCardNotFound=未讀取到身份證，請重新刷卡！
pers_person_nameValid=支援中文、英文、數字、「-」、「_」
pers_person_nameValidForEN=支援英文、數字、「-」、「_」、「.」
pers_person_pinPrompt=請輸入由英文字母、數字組成的字元
pers_person_pinSet=人員編號設定
pers_person_supportLetter=支援字母
pers_person_cardsSupport=啟用一人多卡
pers_person_SupportDefault=自動編號
pers_person_noSpecialChar=人員姓名不能輸入特殊字元！
pers_person_pinInteger=請輸入由數字組成的字元
pers_pin_noSpecialChar=人員編號不能包含特殊字符！
pers_op_capture=抓拍
pers_person_cardDuress=卡號重複
pers_person_pwdException=密碼異常
pers_person_systemCheckTip=檢查到人員訊息有異常！
pers_person_immeHandle=立即處理
pers_person_cardSet=卡設定
pers_person_tempPersonSet=待審核人員設置
pers_person_cardsReadMode=卡號讀取模式
pers_person_cardsReadModeReadHead=控制器讀頭
pers_person_cardsReadModeID180=ID180（讀身份證物理卡號）
pers_person_IDReadMode=身份證讀取模式
pers_person_IDReadModeIDCardReader=身份證閱讀器
pers_person_IDReadModeTcpReadHead=TCP讀頭
pers_person_physicalNo=身份證物理卡號
pers_person_physicalNoToCardNo=身份證物理卡號當卡號
pers_person_ReadIDCard=讀取身份證
pers_person_templateBioTypeNumber=生物模板類型編號
pers_person_templateValidType=生物特徵是否有效
pers_person_templateBioType=生物特徵類型
pers_person_templateVersion=生物特徵版本
pers_person_template=生物特徵內容
pers_person_templateNo=生物特徵編號
pers_person_templateNoIndex=生物特徵對應索引
pers_person_templateDataUpdate=生物範本存在時更新資料 :
pers_person_templateValidTypeNoNull=生物特徵是否有效不能為空！
pers_person_templateBioTypeNoNull=人員編號：{0}，生物特徵類型不能為空！
pers_person_templateVersionNoNull=人員編號：{0}，生物特徵版本不能為空！
pers_person_templateNoNull=人員編號：{0}，生物特徵內容不能為空！
pers_person_templateNoNoNull=人員編號：{0}，生物特徵編號不能為空！
pers_person_templateNoIndexNoNull=人員編號：{0}，生物特徵對應索引不能為空！
pers_person_templateError=人員編號：{0}，生物特徵內容有誤！
pers_person_bioDuress=是否脅迫
pers_person_universal=通用
pers_person_voice=聲紋
pers_person_iris=虹膜
pers_person_retina=視網膜
pers_person_palmPrints=掌紋
pers_person_metacarpalVein=掌靜脈
pers_person_visibleFace=可見光人臉
pers_person_pinError=不存在人員編號為 {0} 的人員！
pers_person_pinException=人員編號異常
pers_person_pinAutoIncrement=人員編號自動遞增
pers_person_resetSelfPwd=重置自助登錄密碼
pers_person_picMaxSize=圖片解析度太高，解析度建議在{0}以下。
pers_person_picMinSize=圖片解析度太低，解析度建議在{0}以上。
pers_person_cropFaceShow=查看比對照片
pers_person_faceNoFound=未識別到人臉
pers_person_biometrics=生物識別類型
pers_person_photo=人員照片
pers_person_visibleFaceTemplate=可見光人臉模板
pers_person_infraredFaceTemplate=近紅外人臉模板
pers_person_delBioTemplate=刪除生物識別數據
pers_person_delBioTemplateSelect=請選擇要刪除的生物模板！
pers_person_infraredFace=近紅外人臉
pers_person_notCard=不包含卡
pers_person_notRegFinger=不包含指紋
pers_person_notMetacarpalVein=不包含掌靜脈
pers_person_notRegVein=不包含指靜脈
pers_person_notIris=不包含虹膜
pers_person_notInfraredFaceTemplate=不包含近紅外人臉模板
pers_person_notVisibleFaceTemplate=不包含可見光人臉模板
pers_person_notVisibleFacePhoto=不包含可見光人臉照片
pers_person_visibleFacePhoto=可見光人臉照片
pers_person_change=人員調整
pers_person_cropFaceUsePhoto=是否把比對照片作為頭像顯示？
pers_person_photoUseCropFace=是否使用頭像生成比對照片？
pers_person_selectCamera=選擇抓拍監視器
pers_person_delCropFaceMsg=沒有比對照片可删除！
pers_person_disabledNotOp=該人員已被禁用，無法操作！
pers_person_visiblePalm=可見光手掌
pers_person_notVisiblePalm=不包含可見光手掌
pers_person_selectDisabledNotOp=選擇的人員中存在已被禁用人員，無法操作！
pers_person_photoUseCropFaceAndTempalte=是否使用頭像生成比對照片、人臉範本？
pers_person_photoUseTempalte=是否使用照片生成人臉範本？
pers_person_createCropFace=生成比對照片
pers_person_createFaceTempalte=生成人臉範本
pers_person_faceTempalte=人臉範本
pers_person_extractFaceTemplate=選取人臉範本
pers_person_createSuccess=生成成功
pers_person_createFail=生成失敗
pers_person_serverConnectWarn=伺服器地址、用戶名、密碼不能為空！
pers_person_serverOffline=人臉範本選取服務器離線
pers_person_faceTemplateError1=探測人臉失敗
pers_person_faceTemplateError2=人臉遮擋
pers_person_faceTemplateError3=清晰度不够
pers_person_faceTemplateError4=人臉角度太大
pers_person_faceTemplateError5=活體檢測失敗
pers_person_faceTemplateError6=人臉範本選取失敗
pers_person_cropFaceNoExist=比對照片不存在
pers_person_disableFaceTemplate=未啟用人臉範本選取功能，無法選取範本！
pers_person_cropFacePhoto=人臉比對照片
pers_person_vislightPalmPhoto=手掌比對照片
pers_person_serverOfflineWarn=人臉範本選取服務器離線，無法啟用該功能！
pers_person_serverConnectInfo=請測試連接人臉範本選取服務器是否線上？
pers_person_notModified=手機號碼不能修改。
pers_person_syncAcms=將人員同步至 ACMS
pers_import_startUpdate=開始更新人員資訊。
pers_person_updateFailed=人員資訊更新失敗！
#控制器发卡
pers_person_readCard=控制器發卡
pers_person_stopRead=停止發卡
pers_person_chooseDoor=選取門
pers_person_readCarding=髮卡中，請稍後重試！
#抓拍照片
pers_capture_catchPhoto=抓拍照片
pers_capture_preview=預覽
#部门
pers_dept_entity=部門
pers_dept_deptNo=部門編號
pers_dept_deptName=部門名稱
pers_dept_parentDeptNo=上級部門編號
pers_dept_parentDeptName=上級部門名稱
pers_dept_parentDept=上級部門
pers_dept_note=若果新增的部門在部門清單中未能顯示，請聯繫管理員到使用者編輯中重新授權部門！
pers_dept_exit=存在
pers_dept_auth=綁定的系統使用者
pers_dept_parentMenuMsg=上級部門不能是自身或其下級部門
pers_dept_initDept=部門名稱
pers_dept_deptMarket=市場部
pers_dept_deptRD=研發部
pers_dept_deptFinancial=財務部
pers_dept_nameNoSpace=部門名稱不能以空格開頭或結尾！
pers_dept_nameExist=該部門名稱已存在
pers_dept_changeLevel=是否切換為該部門的權限
pers_dept_noSpecialChar=部門編號不能包含特殊字符！
pers_dept_NameNoSpecialChar=部門名稱不能包含特殊字符！
pers_dept_noModifiedParent=不能修改上級部門！
#职位
pers_position_entity=職位
pers_position_code=職位編號
pers_position_name=職位名稱
pers_position_notExist=職位不存在!
pers_position_sortNo=排序編號
pers_position_parentName=上級職位
pers_position_parentCode=上級職位編號
pers_position_batchToPosition=調動到的職位
pers_position_nameExist=該職位名稱已存在
pers_position_change=職位調整
pers_position_parentMenuMsg=上級職位不能是自身或其下級職位
pers_position_nameNoSpace=職位名稱不能以空格開頭或結尾！
pers_position_existSub={0} :含有子職位不能刪除
pers_position_existPerson={0} :含有人員不能刪除
pers_position_importTemplate=職位導入模板
pers_position_downloadTemplate=下載導入模板
pers_position_codeNotEmpty=職位編號不能為空
pers_position_nameNotEmpty=職位名稱不能為空
pers_position_nameNoSpecialChar=職位名稱 {0} 不能包含特殊字符！
pers_position_noSpecialChar=職位編號 {0} 不能特殊字符！
pers_position_codeLength=職位編號 {0} 長度超出30位
pers_position_nameLength=職位名稱 {0} 長度超過 {1} 的數據
pers_position_codeExist=該職位編號 {0} 已存在
#证件
pers_cert_type=證件類型
pers_cert_number=證件號碼
pers_cert_name=證件名
pers_cert_numberExist=證件號碼已存在
#导出
pers_export_allPersPerson=全部人員
pers_export_curPersPerson=現用的人員
pers_export_template=匯出範本
pers_export_personInfo=匯出人員訊息
pers_export_personInfoTemplate=下載人員訊息導入模板
pers_export_personBioTemplate=匯出人員生物範本資料
pers_export_basicInfo=配置人員基本訊息
pers_export_customAttr=配置自訂屬性
pers_export_templateComment=欄位名稱，是否主鍵，是否唯一，是否容許空({0},{1},{2},{3})
pers_export_templateFileName=人員訊息範本
pers_export_bioTemplateFileName=人員生物範本資料
pers_export_deptInfo=匯出部門訊息
pers_export_deptTemplate=匯出部門範本
pers_export_deptTemplateFileName=部門範本
pers_export_personPhoto=匯出人員照片
pers_export_allPhotos=所有照片(選取所有人員)
pers_export_selectPhotoToExport=選取開始人員編號和結束人員編號以匯出人員照片
pers_export_fromId=人員編號從
pers_export_toId=到
pers_export_certNumberComment=填寫證件號碼後證件類型為必填項
pers_export_templateCommentName=字段名稱:({0})
pers_export_dataExist=請確保導入數據在系統中已存在
pers_export_cardNoTip=多個卡號&隔開
pers_carNumber_importTip=車牌號碼（多個車牌&隔開）
#导入
pers_import_certNumberExist=證件號碼 {0} 已存在
pers_import_complete=完成
pers_import_password=人員密碼
pers_import_fail=第{0}行失敗 : {1}
pers_import_overData=現用的匯入人數為{0},超過限制的30000條，請分批匯入!
pers_import_pinTooLong=人員編號 {0} 超長！
pers_import_pinExist=人員編號 {0} 已存在！
pers_import_pinIsRepeat=人員編號 {0} 已重複！
pers_import_pinError=人員編號 {0} 格式錯誤！
pers_import_pinSupportNumber=人員編號只支持數字！人員編號為：{0}
pers_import_pinNotSupportNonAlphabetic=人員編號只支援數字和字母組合。人員編號為：{0}
pers_import_pinNotNull=人員編號不能全為0！
pers_import_pinStartWithZero=人員編號不能以0開頭！
pers_import_cardNoNotSupportLetter=卡號不支援字母！
pers_import_cardNoNotNull=卡號不能全為0！
pers_import_cardNoStartWithZero=卡號不能以0開頭！
pers_import_cardTooLong=卡號 {0} 超長！
pers_import_cardExist=卡號 {0} 已存在！
pers_import_personPwdOnlyNumber=人員密碼僅支援數字！
pers_import_personPwdTooLong=人員密碼 {0} 超長！
pers_import_personDuressPwd=人員密碼 {0} 重複！
pers_import_emailTooLong=信箱 {0} 超長！
pers_import_nameTooLong=姓名 {0} 超長！
pers_import_genderError=性別格式錯誤！
pers_import_personDateError=聘用日期格式錯誤！
pers_import_createTimeError=建立時間格式錯誤！
pers_import_phoneError=電話號碼格式錯誤！
pers_import_phoneTooLong=電話號碼長度超過20位！
pers_import_emailError=信箱格式錯誤！
pers_import_birthdayError=出生日期格式錯誤！
pers_import_nameError=姓名不能包括特殊字元！人員編號為：{0}
pers_import_firstnameError=姓名不能包括逗號！
pers_import_firstnameNotNull=姓氏不能為空！
pers_import_dataCheck=資料檢查結束！
pers_import_dataSaveFail=資料匯入失敗！
pers_import_allSucceed=資料全部匯入成功！
pers_import_result=成功： {0} 條，失敗：{1} 條。
pers_import_result2=匯入結果
pers_import_result3=成功：{0} 條，更新：{1} 條，失敗：{2} 條。
pers_import_notSupportFormat=暫時不支援此種格式！
pers_import_selectCorrectFile=請選取格式正確的檔案！
pers_import_fileFormat=檔案格式
pers_import_targetFile=目的檔案
pers_import_startRow=表頭起始行數
pers_import_startRowNote=資料格式第一行表名，第二行表頭，第三行往後是匯入資料，請核對檔案後再匯入。
pers_import_delimiter=分隔設定
pers_import_importingDataFields=資料庫欄位
pers_import_dataSourceFields=匯入報表欄位
pers_import_total=共
pers_import_dataUpdate=人員編號存在時更新資料 :
pers_import_dataIsNull=檔案中沒有資料！
pers_import_deptNotExist=部門不存在！
pers_import_deptIsNotNull=部門名稱不能為空！
pers_import_pinNotEmpty=人員編號不能為空！
pers_import_nameNotEmpty=人員姓名不能為空！
pers_import_siteCodeOnlyLetterNum=區位碼格式錯誤。
pers_import_cardNoFormatErrors=卡號 {0} 格式錯誤。
pers_import_cardsNotSupport=現用的系統不支援多卡
pers_import_personInfo=匯入人員訊息
pers_import_commentFormat=註解的格式不正確！
pers_import_noComment=第 {0} 行 {1} 列資料，沒有註解
pers_import_fieldRepeat=第 {0} 行 {1} 列資料，註解中的欄位名稱重複: {2}
pers_import_primaryKey=至少要有一個欄位為主鍵
pers_import_templateIsRepeat=人員編號：{0}的生物範本資料已重複！
pers_import_biologicalTemplate=匯入人員生物範本資料
pers_import_uploadFileSuccess=上傳檔案成功，開始解析檔案資料,請稍候...
pers_import_resolutionComplete=解析完畢，開始更新資料庫。
pers_import_mustField=匯入檔案必須含有{0}的列。
pers_import_bioTemplateSuccess=匯入生物範本資料，該資料需要到各業務模組手動同步資料才會下發到設備。
pers_import_personPhoto=匯入人員照片
pers_import_opera_log=操作日誌
pers_import_error_log=錯誤日誌
pers_import_uploadFileSize=請上傳大小不超過{0}的文件！
pers_import_uploadFileSizeLimit=單次導入請上傳大小不超過500M的文件！
pers_import_type=導入方式
pers_import_photoType=照片
pers_import_archiveType=壓縮包
pers_import_startUpload=開始上傳
pers_import_addMore=新增更多
pers_import_photoQuality=照片質量
pers_import_original=原始
pers_import_adaptive=自適應
pers_import_adaptiveSize=(大小 480 * 640)
pers_import_totalNumber=總數
pers_import_uploadTip=(請勿在上傳時刪除照片)
pers_import_addPhotoTip=請清除之前處理過的照片或是重新上傳新照片！
pers_import_selectPhotoTip=請先選取要上傳的照片！
pers_import_uploadResult=圖片上傳結果
pers_import_pleaseSelectPhoto=請選取照片
pers_import_multipleSelectTip=按下Ctrl鍵進行多項選取
pers_import_replacePhotoTip=等待上傳的圖片中已經存在選中圖片,確定取代嗎？
pers_import_photoNamePinNotCorrespond=原因：照片命名和人員編號不對應
pers_import_photoFormatRequirement=請使用人员编号為照片命名。 正確的格式是JPG/PNG。確保照片名稱不包括特殊字元。
pers_import_filterTip=所選的部分照片無法預覽，可能有以下原因：
pers_import_photoContainSpecialCharacters=照片名稱有特殊字元
pers_import_photoFormatError=照片格式不正確
pers_import_photoSelectNumber=單次導入請勿選取超過3000張圖片！
pers_import_photoSelectNumberLimit=請勿選擇超過{0}張圖片！
pers_import_fileMaxSize=圖片太大，請上傳小於5M的圖片檔案。
pers_import_notUploadPhotoNumber=未上傳圖片不得超過3000張！
pers_import_zipFileNotPhoto=壓縮包中沒有人員照片，請重新選擇後導入！
pers_import_personPlateRepeat=人員車牌 {0} 重複！
pers_import_filePlateRepeat=檔案內定車牌 {0} 重複！
pers_import_personPlateFormat=人員車牌 {0} 格式不正確！
pers_import_personPlateMax=人員車牌超過最大數6個！
pers_import_cropFaceFail=生成比對照片失敗！
pers_import_pinLeaved=人員編號：{0}已離職。
pers_import_exceedLicense=超出軟體許可的人員數量，不容許匯入。
pers_import_bioTemplateNotNull=人員編號,生物類型，生物id,生物索引，內容，版本不容許為空！
pers_import_certTypeNotNull=人員編號為：{0} 證件類型不能為空
pers_import_certTypeNotExist=人員編號為：{0}證件類型不存在
pers_import_certNumNotNull=人員編號為：{0}證件號碼不能為空
pers_import_certNumberTooLong=第{0}行:證件號碼 {1} 超長！
pers_import_idNumberErrors=第{0}行:身份證號碼 {1} 格式錯誤！
pers_import_emailErrors=第{0}行:郵箱地址 {1} 格式錯誤！
pers_import_emailIsExist=郵箱地址 {0} 已存在！
pers_import_emailIsRepeat=第{0}行: 文件內部郵箱地址 {1} 已重複！
pers_import_fileMobilePhoneRepeat=第{0}行：文件內部手機號碼 {1} 重複！
pers_import_mobilePhoneErrors=手機號碼格式錯誤！
pers_import_hireDateError=入職日期格式錯誤！
pers_import_selectPhotoType=請選擇導入照片類型！
pers_import_hireDateLaterCurrent=入職日期不能大於當前日期！
pers_import_buildingNotExist=該樓棟不存在！
pers_import_unitNotExist=單位不存在！
pers_import_vdbInfoFail=樓棟{0}中沒有名為{1}的單位資訊！
pers_import_vdbBuildingFail=單元{0}的樓棟資訊不能為空！
pers_import_vdbRoomNoFail=房號只能是大於0的數字！
#人员离职
pers_person_leave=離職
pers_dimission_date=離職日期
pers_dimission_type=離職類型
pers_dimission_reason=離職原因
pers_dimission_volutary=自離
pers_dimission_dismiss=辭退
pers_dimission_resignat=辭職
pers_dimission_shiftJob=調離
pers_dimission_leave=留職停薪
pers_dimission_recovery=離職恢復
pers_dimission_sureToRecovery=你確定要執行離職恢復操作嗎？
pers_dimission_backCard=是否歸還卡？
pers_dimission_isForbidAction=是否關閉門禁？
pers_dimission_writeInfomation=填寫離職訊息
pers_dimission_pinRetain=離職人員保留編號
pers_dimission_downloadTemplate=下載離職人員導入模板
pers_dimission_import=導入離職人員
pers_dimission_importTemplate=離職人員導入模板
pers_dimission_date_noNull=離職日期不能為空
pers_dimission_leaveType_noExist=離職類型不存在
pers_dimission_dateFormat=必填字段，時間格式為yyyy-MM-dd，如：2020-07-22
pers_dimission_leaveType=必填字段，如：自離、辭退、辭職、調離
pers_dimission_forbidden=加入禁止名單
pers_dimission_leaveType_noNull=離職類型不能為空！
pers_dimission_person_noExist=離職人員不存在！
pers_dimission_date_error=離職日期未填寫或格式不正確
#临时人员
pers_tempPerson_audit=審核
pers_tempPerson_view=檢視
pers_tempPerson_waitReview=等待管理員審核
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=卡號
pers_card_state=卡狀態
pers_card_effect=有效
pers_card_disabled=無效
pers_card_past=過期
pers_card_back=退卡
pers_card_change=換卡
pers_card_note=卡號已存在
pers_card_numTooBig=卡號超出
pers_issueCard_entity=發卡
pers_issueCard_operator=操作員
pers_issueCard_operate=操作類型
pers_issueCard_note=操作物件為已登記人事資料但未登記卡帳號人員！
pers_issueCard_date=發卡日期
pers_issueCard_changeTime=修改時間
pers_issueCard_cardValidate=卡號不能有空格
pers_issueCard_cardEmptyNote=卡號不能為空
pers_issueCard_cardHasBeenIssued=此卡已發過卡！
pers_issueCard_noCardPerson=未發卡人員
pers_issueCard_waitPerson=本次發卡人員
pers_issueCard_mc5000=MC5000髮卡
pers_batchIssCard_entity=批量發卡
pers_batchIssCard_startPersNo=起始人員編號
pers_batchIssCard_endPersNo=結束人員編號
pers_batchIssCard_issCardNum=已發卡數
pers_batchIssCard_notIssCardNum=未發卡人員數
pers_batchIssCard_generateList=生成人員清單
pers_batchIssCard_startRead=開始讀取
pers_batchIssCard_swipCard=發卡位置
pers_batchIssCard_sendCard=讀頭
pers_batchIssCard_dispenCardIss=發卡器
pers_batchIssCard_usbEncoder=desfire發卡器
pers_batchIssCard_note=人員編號只支援輸入數字，僅顯示未發過卡的人員(最多300人)！
pers_batchIssCard_startPinEmpty=起始人員編號不能為空！
pers_batchIssCard_endPinEmpty=結束人員編號不能為空！
pers_batchIssCard_startPinLargeThanEndPin=起始人員編號不能大於結束人員編號！
pers_batchIssCard_numberParagraphNoPerson=該編號段內不存在未發卡人員！
pers_batchIssCard_inputCardNum=輸入卡號
pers_batchIssCard_cardLetter=卡號不支援字母！
pers_batchIssCard_cardNoTooLong=卡號長度過長
pers_batchIssCard_issueWay=發卡模式
pers_batchIssCard_noPersonList=未生成人員清單
pers_batchIssCard_startReadCard=開始讀卡
pers_batchIssCard_swipePosition=刷卡位置
pers_batchIssCard_chooseSwipePosition=請選取刷卡位置
pers_batchIssCard_readCardTip=發卡模式為讀頭時，僅讀取設備中未註冊的卡。
pers_batchIssCard_notIssCardNo=未發卡數
pers_batchIssCard_totalNumOfCards=總卡數
pers_batchIssCard_acms=ACMS髮卡
pers_lossCard_entity=掛失
pers_lossCard_lost=卡已掛失，不能重複操作！
pers_losscard_note2=寫入管理卡後需在梯控設備的讀頭上刷卡，掛失操作才會生效。
pers_revertCard_entity=解掛
pers_revertCard_setReport=請先掛失卡！
pers_revertcard_note2=寫入管理卡後需在梯控設備的讀頭上刷此管理卡，解掛操作才會生效。
pers_issueCard_success=發卡成功！
pers_issueCard_error=發卡異常！
pers_cardData_error=讀取卡資料格式異常！
pers_analysis_error=卡資料解析異常！
pers_cardOperation_error=卡操作異常！
pers_cardPacket_error=卡操作指令封裝異常！
pers_card_write=寫卡
pers_card_init=起始化卡
pers_card_loss=掛失卡
pers_card_revert=解掛卡
pers_card_writeMgr=寫管理卡
pers_initCard_tip=起始化後，該卡將會變成一張空白卡！
pers_initCard_prepare=準備起始化卡...
pers_initCard_process=正在起始化卡...
pers_initCard_success=起始化卡成功
pers_mgrCard_prepare=準備寫管理卡資料...
pers_mgrCard_process=正在寫管理卡資料...
pers_mgrCard_success=寫管理卡成功
pers_userCard_prepare=準備寫使用者卡...
pers_userCard_process=正在寫使用者卡資料...
pers_userCard_success=寫使用者卡成功
pers_userCard_tip=請在人員編輯版面設定開始時間和結束時間後再做寫卡操作
pers_userCard_tip2=權限資料為空，不予寫卡。
pers_userCard_tip3=權限關聯設備超過兩台，資料丟失。
pers_writeCard_tip=請確保連線發卡器或驅動已安裝，並將卡放在發卡器上
pers_writeMgrCard_tip=掛失和解掛卡的數量不能超過18
pers_writeMgrCard_tip2=現用的掛失卡和解掛卡的數量：
pers_card_writeToMgr=寫入管理卡
pers_card_hex=卡格式顯示
pers_card_decimal=十進位
pers_card_Hexadecimal=十六進位
pers_card_IssuedCommandFail=下發指令失敗：
pers_card_multiCard=多卡登記
pers_card_deputyCard=副卡
pers_card_deputyCardValid=請先填寫主卡號！
pers_card_writePinFormat=系統不支援寫入帶字母的人員編號到卡中，需為數字類型！
pers_card_notMoreThanSixteen=副卡不能超過16個。
pers_card_notDelAll=不能刪除所有副卡。
pers_card_maxCard=卡號大小不能超過{0}！
pers_card_posUseCardNo=該人員的主卡正在被消費模組使用，請去消費模組執行退卡操作！
pers_card_delFirst=請先删除已發卡號，再進行髮卡！
pers_card_disablePersonWarn=選擇的卡號中存在所屬人員為禁用人員，無法操作！
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=區位碼
pers_wiegandFmt_wiegandMode=模式
pers_wiegandFmt_wiegandModeOne=模式一
pers_wiegandFmt_wiegandModeTwo=模式二
pers_wiegandFmt_isDefaultFmt=自動符合
pers_wgFmt_entity=韋根卡格式
pers_wgFmt_in=韋根輸入格式
pers_wgFmt_out=韋根輸出格式
pers_wgFmt_inType=韋根輸入類型
pers_wgFmt_outType=韋根輸出類型
pers_wgFmt_wg=韋根
pers_wgFmt_totalBit=總位數
pers_wgFmt_oddPch=奇校驗(o)
pers_wgFmt_evenPck=偶校驗(e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=設備代碼(f)
pers_wgFmt_siteCode=區位碼(s)
pers_wgFmt_manufactoryCode=廠商代碼(m)
pers_wgFmt_firstParity=第一個奇偶校驗位(p)
pers_wgFmt_secondParity=第二個奇偶校驗位(p)
pers_wgFmt_cardFmt=卡校驗格式
pers_wgFmt_parityFmt=奇偶校驗格式
pers_wgFmt_startBit=起始位
pers_wgFmt_test=測試韋根格式
pers_wgFmt_error=韋根格式錯誤！
pers_wgFmt_verify1=總位數不能超過80！
pers_wgFmt_verify2=卡校驗格式長度必須等於總位數！
pers_wgFmt_verify3=奇偶校驗格式長度必須等於總位數！
pers_wgFmt_verify4=起始化的資料不能刪除！
pers_wgFmt_verify5=該卡格式正在使用，不能刪除！
pers_wgFmt_verify6=第一個奇偶校驗位不能大於總位數！
pers_wgFmt_verify7=第二個奇偶校驗位不能大於總位數！
pers_wgFmt_verify8=起始位與最大長度位格式不正確！
pers_wgFmt_verify9=校驗格式超過總位數！
pers_wgFmt_verify10=卡校驗位數功能不能交叉！
pers_wgFmt_verify11=區位碼超過設定範圍！
pers_wgFmt_verify=校驗
pers_wgFmt_unverify=不校驗
pers_wgFmt_atLeastDefaultFmt=請至少保留一個自動符合的卡格式！
pers_wgFmt_defaultFmtError1=相同位數中含有其他格式的卡格式，無法設定為自動符合！
pers_wgFmt_defaultFmtError2=存在相同的卡格式為自動符合，操作失敗！
pers_wgFmt_cardFormats=卡格式
pers_wgFmt_cardFormatTesting=卡格式測試
pers_wgFmt_checkIsUsed=卡格式在{0}中使用，不能刪除！
pers_wgFmt_supportDigitsNumber=請輸入裝置支持的位數長度
#选人控件
pers_widget_selectPerson=選取人員
pers_widget_searchType1=條件查詢
pers_widget_searchType2=部門
pers_widget_deptHint=註：匯入所選部門下所有人員
pers_widget_noPerson=沒有選取任何人
pers_widget_noDept=請選取部門
pers_widget_noDeptPerson=所選部門下沒有人員，請重新選取部門！
#人员属性
pers_person_carPlate=車牌號
pers_person_socialSecurity=社保號
pers_person_msg4=最大長度不能超過20位！
pers_person_msg5=名字長度不能超過50位！
pers_person_type=人員類型
pers_person_reseCode=自助密碼
pers_person_IsSendMail=信件知會
pers_person_inactive=停用
pers_person_active=有效
pers_person_employee=員工
pers_person_isSendMailMsg=事件知會必須填寫信箱
pers_person_createTime=建立時間
pers_person_pinFirstValid=人員編號首位不能為8、9。
pers_person_attrValueValid=欄位值不能重複
pers_person_attrValueDelimiterValid=分號要放在中間
pers_person_isSendSMS=短信通知
pers_person_building=樓棟名稱
pers_person_unitName=單位名稱
pers_person_roomNo=房號
#动态属性
pers_attr_emp_type=員工類型
pers_attr_street=街道
pers_attr_nation=民族
pers_attr_office_address=公司位址
pers_attr_postcode=郵編
pers_attr_office_phone=公司電話
pers_attr_home_phone=家庭電話
pers_attr_job_title=職稱
pers_attr_birthplace=出生地
pers_attr_polit_status=政治面貌
pers_attr_country=地區
pers_attr_home_address=家庭位址
pers_attr_hire_type=僱傭類型
pers_attr_inContract=合同內
pers_attr_outContract=合同外
#属性自定义
pers_attribute_attrName=顯示名稱
pers_attribute_attrValue=欄位取值
pers_attribute_controlType=輸入類型
pers_attribute_positionX=行
pers_attribute_positionY=列
pers_attribute_showTable=顯示在人員清單
pers_attrDefini_deletemsg=該屬性已被使用，是否確認刪除？
pers_attrDefini_reserved=系統預留欄位名
pers_attrDefini_msg1=最大長度不能超過30位！
pers_attrDefini_msg2=該行該列已經存在，請更換到其他位置！
pers_attrDefini_attrValue_split=請用英文分號「 ; 」分隔
pers_attrDefini_attrName=屬性名
pers_attrDefini_sql=sql語句
pers_attrDefini_attrId=屬性編號
pers_attrDefini_select=下拉清單
pers_attrDefini_check=多選
pers_attrDefini_radio=單選
pers_attrDefini_text=輸入框
pers_attrDefini_maxCol=不能超過2列
pers_attrDefini_maxLimit=自訂屬性已達最大數量限制！
pers_attrDefini_modControlType=變更輸入類型會清理系統所有人員現用的欄位的資料，是否繼續？
#leavePerson
pers_leavePerson_reinstated=復職
#opExample
pers_example_newRecode=取得新記錄
pers_example_allRecode=取得所有紀錄
pers_custField_StatisticalType=統計類型
#人员参数修改
pers_param_isAudit=啟用自動審核
pers_param_donotChangePin=已有人員編號包括字母，不能變更人員編號模式。
pers_param_hexChangeWarn=現用的系統已存在卡號，不能變更卡格式顯示。
pers_param_cardsChangeWarn=現用的系統已存在多卡，不能變更多卡設定。
pers_param_maxPinLength=新設定的人員編號的長度不能小於已存在的人員編號的長度。
pers_param_pinBeyondDevLength=系統中存在設備支援的人員編號最大長度為{0}，請輸入小於{1}的整數！
pers_param_cardBeyondDevLength=系統中存在設備支援的卡號最大長度為{0}，請輸入小於{1}的整數！
pers_param_checkIsExistNoAudit=現用的系統存在未審核的登記人員，不能修改為自動審核！
pers_param_noSupportPinLetter=系統中存在設備不支援包括字母的人員編號，不能變更人員編號模式。
pers_param_changePinLettersTip=人員編號支援自動遞增，不能修改人員編號模式
pers_param_changePinIncrementTip=人員編號支援包括字母，不能修改人員編號模式
pers_param_qrCode=企業二維碼
pers_param_employeeRegistrar=啟用雲員工登記
pers_param_downloadQRCodePic=下載二維碼圖片
pers_param_qrCodeUrl=二維碼Url
pers_param_qrCodeUrlCreate=人員自助登記
pers_param_qrCodeUrlHref=服務器地址:端口
pers_param_pinSetWarn=當前系統已存在人員，不能更改人員編號模式。
pers_param_selfRegistration=啟用自助登記
pers_param_infoProtection=個人敏感信息保護
pers_param_infoProtectionWarnMsg=啟用個人敏感信息安全保護選項之後，本模塊涉及個人敏感數據將進行脫敏或模糊化處理，包括且不僅限於如姓名、卡號、證件號、照片等。
pers_param_templateServer=人臉範本選取服務器
pers_param_enableFacialTemplate=啟用人臉範本選取
pers_param_templateServerAddr=人臉範本選取伺服器地址
pers_param_templateServerWarnInfo=啟用人臉範本選取時，當人臉範本選取服務器線上且用戶校驗通過時，人員比對照片默認需要選取人臉範本； 當人臉範本選取服務器離線模式時，不選取人臉範本！
pers_param_templateServerWarnInfo1=啟用人臉範本選取時，需接入支持人臉範本選取功能的設備！
pers_param_templateServerOffline=人臉範本選取服務器離線，無法選取人臉範本！ 是否繼續？
pers_param_faceServer=人臉後臺比對服務
pers_param_enableFaceVerify=啟用人臉後臺比對
pers_param_faceServerAddr=人臉後臺比對服務地址
pers_param_faceServerSecret=人臉後臺比對服務秘鑰
#国籍
pers_person_nationality=國家/地區
pers_nationality_angola=安哥拉
pers_nationality_afghanistan=阿富汗
pers_nationality_albania=阿爾巴尼亞
pers_nationality_algeria=阿爾及利亞
pers_nationality_america=美國
pers_nationality_andorra=安道爾共和國
pers_nationality_anguilla=安圭拉島
pers_nationality_antAndBar=安提瓜和巴布達
pers_nationality_argentina=阿根廷
pers_nationality_armenia=亞美尼亞
pers_nationality_ascension=阿森松
pers_nationality_australia=澳大利亞
pers_nationality_austria=奧地利
pers_nationality_azerbaijan=阿塞拜疆
pers_nationality_bahamas=巴哈馬
pers_nationality_bahrain=巴林
pers_nationality_bangladesh=孟加拉國
pers_nationality_barbados=巴巴多斯
pers_nationality_belarus=白俄羅斯
pers_nationality_belgium=比利時
pers_nationality_belize=伯利茲
pers_nationality_benin=貝寧
pers_nationality_bermudaIs=百慕大群島
pers_nationality_bolivia=玻利維亞
pers_nationality_botswana=博茨瓦納
pers_nationality_brazil=巴西
pers_nationality_brunei=文萊
pers_nationality_bulgaria=保加利亞
pers_nationality_burkinaFaso=布基納法索
pers_nationality_burma=緬甸
pers_nationality_burundi=布隆迪
pers_nationality_cameroon=喀麥隆
pers_nationality_canada=加拿大
pers_nationality_caymanIs=開曼群島
pers_nationality_cenAfrRepub=中非共和國
pers_nationality_chad=乍得
pers_nationality_chile=智利
pers_nationality_china=中國
pers_nationality_colombia=哥倫比亞
pers_nationality_congo=剛果（布）
pers_nationality_cookIs=庫克群島
pers_nationality_costaRica=哥斯達黎加
pers_nationality_cuba=古巴
pers_nationality_cyprus=塞浦路斯
pers_nationality_czechRep=捷克
pers_nationality_denmark=丹麥
pers_nationality_djibouti=吉布提
pers_nationality_dominicaRep=多米尼加共和國
pers_nationality_ecuador=厄瓜多爾
pers_nationality_egypt=埃及
pers_nationality_eISalvador=薩爾瓦多
pers_nationality_england=英國
pers_nationality_estonia=愛沙尼亞
pers_nationality_ethiopia=埃塞俄比亞
pers_nationality_fiji=斐濟
pers_nationality_finland=芬蘭
pers_nationality_france=法國
pers_nationality_freGui=法屬圭亞那
pers_nationality_gabon=加蓬
pers_nationality_gambia=岡比亞
pers_nationality_georgia=格魯吉亞
pers_nationality_germany=德國
pers_nationality_ghana=加納
pers_nationality_gibraltarm=直布羅陀
pers_nationality_greece=希臘
pers_nationality_grenada=格林納達
pers_nationality_guam=關島
pers_nationality_guatemala=危地馬拉
pers_nationality_guinea=幾內亞
pers_nationality_guyana=圭亞那
pers_nationality_haiti=海地
pers_nationality_honduras=洪都拉斯
pers_nationality_hungary=匈牙利
pers_nationality_iceland=冰島
pers_nationality_india=印度
pers_nationality_indonesia=印度尼西亞
pers_nationality_iran=伊朗
pers_nationality_iraq=伊拉克
pers_nationality_ireland=愛爾蘭
pers_nationality_israel=以色列
pers_nationality_italy=意大利
pers_nationality_ivoryCoast=科特迪瓦
pers_nationality_jamaica=牙買加
pers_nationality_japan=日本
pers_nationality_jordan=約旦
pers_nationality_kenya=肯尼亞
pers_nationality_korea=韓國
pers_nationality_kuwait=科威特
pers_nationality_kyrgyzstan=吉爾吉斯坦
pers_nationality_laos=老撾
pers_nationality_latvia=拉脫維亞
pers_nationality_lebanon=黎巴嫩
pers_nationality_lesotho=萊索托
pers_nationality_liberia=利比裡亞
pers_nationality_libya=利比亞
pers_nationality_liechtenstein=列支敦士登
pers_nationality_lithuania=立陶宛
pers_nationality_luxembourg=盧森堡
pers_nationality_madagascar=馬達加斯加
pers_nationality_malawi=馬拉維
pers_nationality_malaysia=馬來西亞
pers_nationality_maldives=馬爾代夫
pers_nationality_mali=馬裡
pers_nationality_malta=馬耳他
pers_nationality_marianaIs=馬裡亞那群島
pers_nationality_martinique=馬提尼克
pers_nationality_mauritius=毛里求斯
pers_nationality_mexico=墨西哥
pers_nationality_moldova=摩爾多瓦
pers_nationality_monaco=摩納哥
pers_nationality_montseIs=蒙特塞拉特島
pers_nationality_morocco=摩洛哥
pers_nationality_mozambique=莫桑比克
pers_nationality_namibia=納米比亞
pers_nationality_nauru=瑙魯
pers_nationality_nepal=尼泊爾
pers_nationality_netAnti=荷屬安的列斯
pers_nationality_netherlands=荷蘭
pers_nationality_newZealand=新西蘭
pers_nationality_nicaragua=尼加拉瓜
pers_nationality_niger=尼日爾
pers_nationality_nigeria=尼日利亞
pers_nationality_norKorea=朝鮮
pers_nationality_norway=挪威
pers_nationality_oman=阿曼
pers_nationality_pakistan=巴基斯坦
pers_nationality_panama=巴拿馬
pers_nationality_papNewCui=巴布亞新幾內亞
pers_nationality_paraguay=巴拉圭
pers_nationality_peru=秘魯
pers_nationality_philippines=菲律賓
pers_nationality_poland=波蘭
pers_nationality_frenPolyne=法屬玻利尼西亞
pers_nationality_portugal=葡萄牙
pers_nationality_puerRico=波多黎各
pers_nationality_qatar=卡塔爾
pers_nationality_reunion=留尼旺
pers_nationality_romania=羅馬尼亞
pers_nationality_russia=俄羅斯
pers_nationality_saiLueia=聖盧西亞
pers_nationality_saintVinc=聖文森特島
pers_nationality_samoa_eastern=東薩摩亞
pers_nationality_samoa_western=西薩摩亞
pers_nationality_sanMarino=聖馬力諾
pers_nationality_saoAndPrinc=聖多美和普林西比
pers_nationality_sauArabia=沙特阿拉伯
pers_nationality_senegal=塞內加爾
pers_nationality_seychelles=塞舌爾
pers_nationality_sieLeone=塞拉利昂
pers_nationality_singapore=新加坡
pers_nationality_slovakia=斯洛伐克
pers_nationality_slovenia=斯洛文尼亞
pers_nationality_solomonIs=所羅門群島
pers_nationality_somali=索馬裡
pers_nationality_souAfrica=南非
pers_nationality_spain=西班牙
pers_nationality_sriLanka=斯里蘭卡
pers_nationality_sudan=蘇丹
pers_nationality_suriname=蘇裡南
pers_nationality_swaziland=斯威士蘭
pers_nationality_sweden=瑞典
pers_nationality_switzerland=瑞士
pers_nationality_syria=敘利亞
pers_nationality_tajikstan=塔吉克斯坦
pers_nationality_tanzania=坦桑尼亞
pers_nationality_thailand=泰國
pers_nationality_togo=多哥
pers_nationality_tonga=湯加
pers_nationality_triAndToba=特立尼達和多巴哥
pers_nationality_tunisia=突尼斯
pers_nationality_turkey=土耳其
pers_nationality_turkmenistan=土庫曼斯坦
pers_nationality_uganda=烏干達
pers_nationality_ukraine=烏克蘭
pers_nationality_uniArabEmira=阿拉伯聯合酋長國
pers_nationality_uruguay=烏拉圭
pers_nationality_uzbekistan=烏茲別克斯坦
pers_nationality_venezuela=委內瑞拉
pers_nationality_vietnam=越南
pers_nationality_yemen=也門
pers_nationality_serbia=塞爾維亞
pers_nationality_zimbabwe=津巴布韋
pers_nationality_zambia=贊比亞
pers_nationality_aruba=阿魯巴
pers_nationality_bhutan=不丹
pers_nationality_bosnia_herzegovina=波斯尼亞和黑塞哥維那
pers_nationality_cambodia=柬埔寨
pers_nationality_congoD=剛果（金）
pers_nationality_comoros=科摩羅
pers_nationality_capeVerde=佛得角
pers_nationality_croatia=克羅地亞
pers_nationality_dominica=多米尼克
pers_nationality_eritrea=厄立特裡亞
pers_nationality_micronesia=密克羅尼西亞
pers_nationalit_guineaBissau=幾內亞比紹
pers_nationalit_equatorialGuinea=赤道幾內亞
pers_nationalit_hongkong=中國香港
pers_nationalit_virginIslands=美屬維爾京群島
pers_nationalit_britishVirginIslands=英屬維爾京群島
pers_nationalit_kiribati=基裡巴斯
pers_nationalit_mongolia=蒙古
pers_nationalit_marshall=馬紹爾群島
pers_nationalit_macedonia=馬其頓
pers_nationalit_montenegro=黑山
pers_nationalit_mauritania=毛裡塔尼亞
pers_nationalit_palestine=巴勒斯坦
pers_nationalit_palau=帕勞
pers_nationalit_rwanda=盧旺達
pers_nationalit_saintKittsNevis=聖基茨和尼維斯聯邦
pers_nationalit_timorLeste=東帝汶
pers_nationalit_taiwan=中國台灣
pers_nationalit_tuvalu=圖瓦盧
pers_nationalit_vanuatu=瓦努阿圖
#制卡
pers_person_cardprint=制卡
pers_cardTemplate_tempSelect=範本選取
pers_cardTemplate_printerSelect=印表機選取
pers_cardTemplate_front=正面
pers_cardTemplate_opposite=反面
pers_cardTemplate_entryDate=入職日期
pers_cardTemplate_photo=照片
pers_cardTemplate_uploadFail=圖片上傳失敗!
pers_cardTemplate_jpgFormat=只容許上傳格式為jpg的檔案!
pers_cardTemplate_printStatus=列印狀態
pers_cardTemplate_waiting=等待列印
pers_cardTemplate_printing=列印中
pers_cardTemplate_printOption=列印選項
pers_cardTemplate_duplexPrint=雙面列印
pers_cardTemplate_frontOnly=僅列印正面
#app
pers_app_delPers=伺服器上不存在你要刪除的人員
pers_app_deptIsNull=找不到該部門編號或名稱對應的部門
pers_app_personNull=人員不存在
pers_app_pinExist=人員編號已存在
pers_app_dateError=日期格式有誤，請參考正確格式：2016-08-08
#api
pers_api_selectPhotoInvalid=照片為無效照片，請重新上傳
pers_api_dateError=日期格式有誤
pers_api_personNotExist=人員不存在
pers_api_cardsPersSupport=系統未開啟一人多卡，設定副卡無效
pers_api_department_codeOrNameNotNull=部門編號或部門名稱不能為空
pers_api_deptSortNoIsNull=部門排序不能為空！
pers_api_deptSortNoError=部門排序的值要介於1-999999之間!
pers_api_dataLimit=當前操作數量為{0}，超過限制的{1}條，請分批操作！
pers_api_cardTypeError=卡類型錯誤
#人员生物模板API
pers_api_fingerprintExisted=該人員的指紋已經存在！
pers_api_validtypeIncorrect=validtype填寫有誤！
pers_api_dataNotExist=templateNo不存在！
pers_api_templateNoRang=指紋模板號碼的範圍是0-9！
pers_api_templateIsNull=template不能為空！
pers_api_versionIsNumber=version只能輸入數位！
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=人員編號只能是數位
#biotime
pers_h5_personAvatarNotNull=頭像為空
pers_h5_personMobileRepeat=手機號碼重複
pers_h5_personEmailRepeat=郵箱重複
pers_h5_pwdIsRepetition=新老密碼重複
pers_h5_personIdNull=人員id為空
pers_h5_pinOrEmailIsNull=請填寫編號和郵箱
pers_emailTitle_resetPassword=修改密碼
pers_emailContent_resetPassword=該鏈接有效時間24小時,複製該鏈接到瀏覽器修改密碼：
pers_h5_tokenIsNull=憑證為空
pers_h5_tokenError=憑證錯誤
pers_h5_oldPwdIsError=舊密碼填寫錯誤
pers_api_resetPasswordSuccess=修改密碼成功
pers_api_resetPasswordFail=修改密碼失敗
pers_api_forgetPassword=忘記密碼
pers_api_confirmSubmit=確認提交
pers_api_confirmPwdCaution=點擊[確定]確認新的密碼
pers_api_pwdRule=密碼必須包含至少一個符號或者數字，且長度至少為8-12個字符
pers_h5_personPinFormatNumber=人員編號只能由數字或字母組成
pers_h5_persEmailNoExist=郵箱填寫錯誤
pers_h5_pageNull=分頁參數錯誤
pers_h5_personPinNotStartWithZero=人員編號不能以0開頭
pers_h5_personPinTooLong=人員編號太長
pers_h5_personPinInValid=該編號已在使用中
pers_h5_imgSizeError=請上傳大小不超過10M的圖片！
pers_h5_confirmAndContinue=確認並繼續
#人脸抠图不及格错误
pers_face_poorResolution=圖片分辨率低於80000像素
pers_face_noFace=未檢測到人臉
pers_face_manyFace=檢測到多張人臉
pers_face_smallFace=人臉比例太小
pers_face_notColor=圖片為非彩色圖片
pers_face_seriousBlur=圖片模糊
pers_face_intensivelyLight=圖片曝光嚴重
pers_face_badIllumination=圖片亮度太暗
pers_face_highNoise=圖片高噪聲
pers_face_highStretch=人臉拉伸過度
pers_face_covered=人臉被遮擋
pers_face_smileOpenMouth=微笑過度
pers_face_largeAngle=臉偏轉角度過大
pers_face_criticalIllumination=圖片亮度臨界
pers_face_criticalLargeAngle=人臉偏轉角度臨界
pers_face_validFailMsg=檢測人臉失敗，原因：
pers_face_failType=人臉摳圖不及格類型
pers_face_photoFormatError=照片格式不正確，請上傳JPG/PNG格式文件。
pers_face_notUpdateMsg=生成比對照片失敗，不更新比對照片。
#健康申报
pers_health_enable=啟用健康信息聲明
pers_health_attrExposure=是否有接觸過可疑或確診病例
pers_health_attrSymptom=過去14天有哪些症狀
pers_health_attrVisitCity=過去14天訪問過的城市
pers_health_attrRemarks=健康狀況備註
pers_health_symptomCough=咳嗽
pers_health_symptomFever=發燒
pers_health_symptomPolypena=呼吸不暢
pers_health_declaration=健康聲明
pers_health_aggrement=我已同意未按要求填寫信息的人將被禁止訪問，並且未如實報告信息的訪問者無法繼續訪問並需要承擔相應的法律責任。
pers_health_visitCity_notEmpty=訪問的城市不能為空！
pers_health_notAgree=需要選擇同意協議,才能繼續
#人员名单库
pers_personnal_list_manager=名單庫
pers_personnal_list=名單庫
pers_personnal_list_scheme=域模式
pers_personnal_list_name=名單庫名稱
pers_personnal_list_group_str_id=名單庫ID
pers_personnal_list_personCount=人員數量
pers_personnal_list_tag=用戶自定義
pers_personnal_list_type=名單庫類型
pers_personnallist_addPerson_repo=添加人員至名單庫
pers_personnallist_sendPersonnallist=下發名單庫
pers_personnallist_sendPerson=下發人員
pers_personnallist_notDel_existPerson=名單庫含有人員，不能删除
pers_personnallist_peopleInRoster=名單庫還有人員
pers_personnallist_associationNotExist=主設備和名單庫關聯不存在
pers_personnal_list_person=名單庫人員
pers_personnal_list_dev=名單庫許可權
pers_personnal_list_addDev=添加設備
pers_personnal_list_name_isExist=該名稱已存在
pers_personnal_bannedList=禁止名單庫
pers_personnal_allowList=允許名單庫
pers_personnal_redList=紅名單庫
pers_personnal_attGroup=考勤組
pers_personnal_passList=通行名單
pers_personnal_banList=禁止名單
pers_personnal_visPassList=訪客通行名單
pers_personnal_visBanList=訪客禁止名單
pers_personnal_databaseHasBeenDistributed=選擇的名單庫已下發，不允許删除
pers_personnel_sendError_dueTo=下發失敗 失敗原因:
pers_personnel_sendError_reson={0}存在於禁止名單，請先刪除再添加
#比對照片-樣片示例
pers_examplePic_Tip=應符合以下要求：
pers_examplePic_Tip1=1、背景顏色為純白，人員穿深色衣服；
pers_examplePic_Tip2=2、電子照片為JPG，PNG，JPEG文件格式，建議像素範圍：480*640<像素<1080*1920；
pers_examplePic_Tip3=3、電子照片中的人像應雙眼睜開正視前方並保證瞳孔清晰可見；
pers_examplePic_Tip4=4、電子照片中的人像應為中性表情，可微笑，但不宜露齒；
pers_examplePic_Tip5=5、電子照片中的人像應清晰、色彩自然、層次豐富、無明顯畸變。在人像臉部或背景上無陰影、亮點或反光；對比度和亮度適當。
pers_examplePic_description=正確示例
pers_examplePic_error=錯誤示例：
pers_examplePic_error1=表情誇張（過度微笑）
pers_examplePic_error2=光線太暗
pers_examplePic_error3=人臉過小（分辨率太小）
pers_applogin_enabled=啟用app登入
pers_applogin_disable=禁用app登入
pers_applogin_status=app登入啟用狀態