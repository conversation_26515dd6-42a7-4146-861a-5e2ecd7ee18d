#pers common.
#here other module can also use the label from pers.
pers_module=ユーザー
pers_common_addPerson=ユーザー追加
pers_common_delPerson=ユーザー削除
pers_common_personCount=ユーザー数
pers_common_browsePerson=ユーザー閲覧
#左侧菜单
pers_person_manager=ユーザー管理
pers_person=ユーザー
pers_department=部署
pers_leave=離職ユーザー
pers_tempPerson=臨時ユーザー
pers_attribute=カスタム属性
pers_card_manager=カード管理
pers_card=カード
pers_card_issue=カード登録履歴
pers_wiegandFmt=Wiegandフォーマット
pers_position=ポジション
#人员
pers_person_female=女性
pers_person_male=男性
pers_person_pin=ユーザーID
pers_person_departmentChange=部署移動
pers_personDepartment_changeLevel=部門切り替え権限
pers_person_gender=性別
pers_person_detailInfo=ユーザー詳細
pers_person_accSet=アクセスコントロール
pers_person_accSetting=アクセスコントロール設定
pers_person_attSet=勤怠管理
pers_person_eleSet=エレベータコントロール
pers_person_eleSetting=エレベータコントロール設定
pers_person_parkSet=ナンバープレート登録
pers_person_pidSet=PID設定
pers_person_insSet=Ins設定
pers_person_aiSet=AI設定
pers_person_payrollSet=給料設定
pers_person_psgSet=チャネル設定
pers_person_lockerSet=ロッカー設定
pers_person_sisSet=セキュリティチェックの設定
pers_person_vdbSet=ビジュアルインタラクティブ設定
pers_person_firstName=名
pers_person_lastName=姓
pers_person_name=名
pers_person_wholeName=名
pers_person_fullName=ミドルネーム
pers_person_cardNum=カード登録数
pers_person_deptNum=関連部署数
pers_person_dataCount=統計
pers_person_regFinger=指紋
pers_person_reg=登録
pers_person_password=パスワード
pers_person_personDate=入社日
pers_person_birthday=誕生日
pers_person_mobilePhone=携帯電話
pers_person_personDevAuth=デバイスアクセス許可
pers_person_email=Email
pers_person_browse=開く
pers_person_authGroup=アクセスレベル
pers_person_setEffective=有効期間設定
pers_person_attArea=アクセスエリア
pers_person_isAtt=勤怠計算
pers_person_officialStaff=正社員
pers_person_probationStaff=派遣社員
pers_person_identity_category=身分タイプ
pers_person_devOpAuth=デバイス操作権限
pers_person_msg1=実際のカードNo.とサイトコードは同時に入力する必要があります！
pers_person_msg2=3-4桁で入力してください
pers_person_msg3=フォーマットエラー
pers_person_imgPixel=(最適サイズ 120*140).
pers_person_cardLengthDigit=ナンバーを入力してください
pers_person_cardLengthHexadecimal=数字またはabcdef文字を入力してください！
pers_person_to=to
pers_person_templateCount=指紋数
pers_person_biotemplateCount=生体テンプレート数
pers_person_regFace=顔
pers_person_regVein=指静脈
pers_person_faceTemplateCount=顔数
pers_person_VeinTemplateCount=指静脈数
pers_person_palmTemplateCount=手のひら数
pers_person_cropFace=ユーザー写真
pers_person_cropFaceCount=比較写真数
pers_person_faceBiodataCount=可視光顔数
pers_person_irisCount=虹彩数
pers_person_batchToDept=新しい部署
pers_person_changeReason=移動理由
pers_person_selectedPerson=ユーザー選択
pers_person_duressPwdError=パスワード重複
pers_person_completeDelPerson=削除
pers_person_recover=回復
pers_person_nameNoComma=カンマは含めないでください
pers_person_firstNameNotEmpty=姓は未入力にできません
pers_person_lastNameNotEmpty=名は未入力にできません
pers_person_mobilePhoneValidate=有効な携帯電話を入力してください
pers_person_phoneNumberValidate=有効な電話番号を入力してください
pers_person_postcodeValidate=有効な郵便番号を入力してください
pers_person_idCardValidate=有効なIDカードNo.を入力してください
pers_person_pwdOnlyLetterNum=パスワードは、アルファベットか数字を使用できます
pers_person_certNumOnlyLetterNum=ID No.は、アルファベットか数字を使用できます
pers_person_oldDeptEqualsNewDept=調整する部署は元の部署と同じにできません
pers_person_disabled=アクセスが無効になっています
pers_person_emailError=有効なEmailアドレスを入力してください
pers_person_driverPrompt=デバイスドライバをインストールしてください！ OKをクリックしてドライバをダウンロードします
pers_person_readIDCardFailed=読み取りエラー！
pers_person_cardPrompt=IDカード領域に保存中...
pers_person_iDCardReadOpenFailed=IDカードリーダ未検出
pers_person_iDCardNotFound=IDカードが見つかりません、再試行してください
pers_person_nameValid=中国語、英語、数字、 '-'、 '_'をサポートしています。
pers_person_nameValidForEN=英語、数字、 '-'、 '_'、 '.'をサポートしています。
pers_person_pinPrompt=アルファベットか数字を入力してください
pers_person_pinSet=ユーザーID設定
pers_person_supportLetter=文字サポート
pers_person_cardsSupport=マルチカードユーザー
pers_person_SupportDefault=ユーザーID自動生成
pers_person_noSpecialChar=特別な文字入力はできません！
pers_person_pinInteger=数字を入力してください。
pers_pin_noSpecialChar=人員番号に特殊文字は入れない!
pers_op_capture=キャプチャー
pers_person_cardDuress=カードNo.重複
pers_person_pwdException=例外パスワード
pers_person_systemCheckTip=異常ユーザー情報チェック
pers_person_immeHandle=すぐに処理
pers_person_cardSet=カード設定
pers_person_tempPersonSet=一時ユーザー設定
pers_person_cardsReadMode=カード読み取りモード
pers_person_cardsReadModeReadHead=コントローラで読み取り
pers_person_cardsReadModeID180=ID180から読み取り
pers_person_IDReadMode=IDカード読み取りモード
pers_person_IDReadModeIDCardReader=IDカードリーダ
pers_person_IDReadModeTcpReadHead=TCP / IPリーダ
pers_person_physicalNo=IDカード物理カードNo.
pers_person_physicalNoToCardNo=IDカードの物理カードNo.を使用
pers_person_ReadIDCard=IDカードを読む
pers_person_templateBioTypeNumber=生体認証テンプレートタイプ番号
pers_person_templateValidType=有効生体テンプレート
pers_person_templateBioType=生体テンプレートタイプ
pers_person_templateVersion=生体テンプレートヴァージョン
pers_person_template=生体テンプレート
pers_person_templateNo=生体テンプレートNo.
pers_person_templateNoIndex=生体テンプレートインデックス
pers_person_templateDataUpdate=生体テンプレートが存在する場合のデータの更新：
pers_person_templateValidTypeNoNull=生体テンプレートの有効期限は未入力にできません！
pers_person_templateBioTypeNoNull=生体テンプレートタイプは、未入力にできません！
pers_person_templateVersionNoNull=生体テンプレートバージョンは、未入力にできません！
pers_person_templateNoNull=生体テンプレートは未入力にできません！
pers_person_templateNoNoNull=生体テンプレートNo.は未入力にできません！
pers_person_templateNoIndexNoNull=生体テンプレートインデックスは、未入力にできません！
pers_person_templateError=生体テンプレートが不正です！
pers_person_bioDuress=強制するかどうか
pers_person_universal=一般
pers_person_voice=音声案内
pers_person_iris=虹彩
pers_person_retina=網膜
pers_person_palmPrints=静脈
pers_person_metacarpalVein=手のひら静脈
pers_person_visibleFace=可視光顔
pers_person_pinError=ユーザーID{0}は存在しません,データ処理できません!
pers_person_pinException=例外ユーザーID
pers_person_pinAutoIncrement=ユーザーID自動追加
pers_person_resetSelfPwd=セルフログインパスワードをリセットする
pers_person_picMaxSize=画像の解像度が高すぎます。解像度は{0}未満にすることをお勧めします。
pers_person_picMinSize=画像の解像度が低すぎます。解像度を{0}以上にすることをお勧めします。
pers_person_cropFaceShow=比較写真表示
pers_person_faceNoFound=顔検出しません
pers_person_biometrics=生体タイプ
pers_person_photo=個人写真
pers_person_visibleFaceTemplate=表示される顔テンプレート
pers_person_infraredFaceTemplate=近赤外線顔テンプレート
pers_person_delBioTemplate=生体認証データを削除する
pers_person_delBioTemplateSelect=削除する生物学的テンプレートを選択してください！
pers_person_infraredFace=近赤外面
pers_person_notCard=カードは含まれません
pers_person_notRegFinger=指紋は含まれません
pers_person_notMetacarpalVein=手のひら静脈は含まれません
pers_person_notRegVein=指の静脈は含まれません
pers_person_notIris=虹彩を含まない
pers_person_notInfraredFaceTemplate=近赤外線顔テンプレートは含まれません
pers_person_notVisibleFaceTemplate=可視光の顔テンプレートは含まれません
pers_person_notVisibleFacePhoto=目に見える顔の写真は含まれていません
pers_person_visibleFacePhoto=目に見える顔の写真
pers_person_change=人事調整
pers_person_cropFaceUsePhoto=比較写真をアバターとして表示しますか？
pers_person_photoUseCropFace=アバターを使用してコントラスト写真を生成しますか？
pers_person_selectCamera=カメラを選択
pers_person_delCropFaceMsg=削除できる比較写真はありません！
pers_person_disabledNotOp=この人は無効になっていて、操作できません！
pers_person_visiblePalm=可視光手のひら
pers_person_notVisiblePalm=可視光手のひらを除く
pers_person_selectDisabledNotOp=選択したユーザに無効なユーザが存在し、操作できません！
pers_person_photoUseCropFaceAndTempalte=アバターを使用して比較写真、顔テンプレートを生成しますか？
pers_person_photoUseTempalte=写真を使って顔テンプレートを生成しますか？
pers_person_createCropFace=たいひしゃしんを生成する
pers_person_createFaceTempalte=顔テンプレートの作成
pers_person_faceTempalte=顔テンプレート
pers_person_extractFaceTemplate=顔テンプレートの抽出
pers_person_createSuccess=生成成功
pers_person_createFail=生成に失敗しました
pers_person_serverConnectWarn=サーバーアドレス、ユーザー名、パスワードは空白にできません！
pers_person_serverOffline=顔テンプレート抽出サーバーがオフライン
pers_person_faceTemplateError1=顔の検出に失敗しました
pers_person_faceTemplateError2=人の顔がさえぎる
pers_person_faceTemplateError3=解像度が足りない
pers_person_faceTemplateError4=顔の角度が大きすぎる
pers_person_faceTemplateError5=生体検出に失敗しました
pers_person_faceTemplateError6=顔テンプレートの抽出に失敗しました
pers_person_cropFaceNoExist=照合写真は存在しません
pers_person_disableFaceTemplate=顔テンプレート抽出機能が有効になっておらず、テンプレートを抽出できません！
pers_person_cropFacePhoto=顔のコントラスト写真
pers_person_vislightPalmPhoto=手のひらの写真比較
pers_person_serverOfflineWarn=顔テンプレート抽出サーバーはオフラインで、この機能を有効にできません！
pers_person_serverConnectInfo=接続顔テンプレート抽出サーバーがオンラインかどうかをテストしてください。
pers_person_notModified=携帯電話番号を変更することはできません。
pers_person_syncAcms=ACMS と人員を同期する
pers_person_startUpdate=従業員情報の更新を開始します。
pers_person_updateFailed=従業員情報の更新に失敗しました！
#控制器发卡
pers_person_readCard=コントローラからカード登録
pers_person_stopRead=登録停止
pers_person_chooseDoor=ドア選択
pers_person_readCarding=カードを出して、後で再試行してください！
#抓拍照片
pers_capture_catchPhoto=画像撮影
pers_capture_preview=プレビュー
#部门
pers_dept_entity=部署
pers_dept_deptNo=部署No.
pers_dept_deptName=部署名
pers_dept_parentDeptNo=上位部署No.
pers_dept_parentDeptName=上位部署名
pers_dept_parentDept=上位部署
pers_dept_note=部署内の新しい部署がリスト表示できなかった場合は、管理者に連絡して部署を編集するための再認証を依頼してください。
pers_dept_exit=コンテンツ
pers_dept_auth=バウンドシステムユーザー
pers_dept_parentMenuMsg=上位部署は下位部署と同じにできません
pers_dept_initDept=一般
pers_dept_deptMarket=営業部
pers_dept_deptRD=開発部
pers_dept_deptFinancial=財務部
pers_dept_nameNoSpace=部署名の先頭と末尾にスペースは使用できません。
pers_dept_nameExist=部署名は既に存在します
pers_dept_changeLevel=この部署のレベルに切り替えるかどうか
pers_dept_noSpecialChar=部署番号に特殊文字を入れることはできません!
pers_dept_NameNoSpecialChar=部署名に特殊文字を入れてはいけません!
pers_dept_noModifiedParent=上位部門は変更できません！
#职位
pers_position_entity=ポジション
pers_position_code=ポジションNo.
pers_position_name=ポジション名
pers_position_notExist=ポジションがありません！
pers_position_sortNo=ソート
pers_position_parentName=上位ポジション
pers_position_parentCode=上位ポジションNo.
pers_position_batchToPosition=新規ポジション
pers_position_nameExist=このポジション名は既に存在します
pers_position_change=ポジションを変更
pers_position_parentMenuMsg=上位ポジションをサブポジションとして設定することはできません。
pers_position_nameNoSpace=ポジション名の始めと終わりにスペース使用することはできません！
pers_position_existSub={0}：サブポジションがあるので、削除できません
pers_position_existPerson={0}：ユーザーがいるので、削除できません
pers_position_importTemplate=位置インポートテンプレート
pers_position_downloadTemplate=テンプレートをダウンロードしてインポートする
pers_position_codeNotEmpty=ポジション番号を空にすることはできません
pers_position_nameNotEmpty=位置名を空にすることはできません
pers_position_nameNoSpecialChar=位置名{0}に特殊文字を含めることはできません！
pers_position_noSpecialChar=位置番号{0}を特殊文字にすることはできません！
pers_position_codeLength=位置番号{0}の長さが30桁を超えています
pers_position_nameLength=位置名{0}が{1}より長いデータ
pers_position_codeExist=ポジション番号{0}はすでに存在します
#证件
pers_cert_type=IDタイプ
pers_cert_number=ID No.
pers_cert_name=ID名
pers_cert_numberExist=このID名は既に存在します
#导出
pers_export_allPersPerson=全ユーザー
pers_export_curPersPerson=現在のユーザー
pers_export_template=テンプレートエクスポート
pers_export_personInfo=ユーザーエクスポート
pers_export_personInfoTemplate=ユーザーインポートテンプレートダウンロード
pers_export_personBioTemplate=生体テンプレートエクスポート
pers_export_basicInfo=基本情報
pers_export_customAttr=カスタム属性
pers_export_templateComment=フィールド名,主キー?ユニーク？nul値を許可しますか?({0},{1},{2},{3})
pers_export_templateFileName=ユーザーインポートテンプレート
pers_export_bioTemplateFileName=ユーザー生体テンプレート
pers_export_deptInfo=部署エクスポート
pers_export_deptTemplate=部署インポートテンプレートダウンロード
pers_export_deptTemplateFileName=部署インポートテンプレート
pers_export_personPhoto=ユーザー写真のエクスポート
pers_export_allPhotos=すべての写真（すべてのユーザーを選択）
pers_export_selectPhotoToExport=開始IDと終了IDを選択して、ユーザー写真をエクスポートします。
pers_export_fromId=IDから
pers_export_toId=to
pers_export_certNumberComment=証明書番号を入力した後、証明書の種類が必要になります
pers_export_templateCommentName=フィールド名：({0})
pers_export_dataExist=インポートしたデータがシステムにすでに存在することを確認してください
pers_export_cardNoTip=複数のカード番号を&で区切る
pers_carNumber_importTip=ナンバープレート番号（複数のナンバープレート&分離）
#导入
pers_import_certNumberExist=証明書番号％sは既に存在します。
pers_import_complete=完了
pers_import_password=ユーザーパスワード
pers_import_fail={0}行が失敗しました：{1}
pers_import_overData=ユーザー{0}行が失敗しました：{1}インポート,システムは最大30000 ユーザーをインポートできます!
pers_import_pinTooLong=ユーザーID{0}は長すぎます!
pers_import_pinExist=ユーザーID{0}既に存在します!
pers_import_pinIsRepeat=ユーザーID{0}が重複しています!
pers_import_pinError=ユーザーID{0}エラー!
pers_import_pinSupportNumber=ユーザーIDは番号のみをサポートしています！例：{0}
pers_import_pinNotSupportNonAlphabetic=ユーザーID 英文字をサポートしていません!例：{0}
pers_import_pinNotNull=ユーザーIDはすべて0をサポートしていません!
pers_import_pinStartWithZero=ユーザーIDの先頭は0をサポートしていません!
pers_import_cardNoNotSupportLetter=カードNo.は文字をサポートしていません!
pers_import_cardNoNotNull=カードNo.はすべて0をサポートしていません！
pers_import_cardNoStartWithZero=カード番号の先頭は0をサポートしていません!
pers_import_cardTooLong=カードNo.{0}が長すぎます!
pers_import_cardExist=カードNo.{0}は既に存在します!
pers_import_personPwdOnlyNumber=ユーザーパスワードは数字のみサポートします
pers_import_personPwdTooLong=ユーザーパスワード{0}は長すぎます！
pers_import_personDuressPwd=ユーザーパスワード{0}は重複しています！
pers_import_emailTooLong=Email{0}は長すぎます!
pers_import_nameTooLong=名前{0}は長すぎます!
pers_import_genderError=性別フォーマットエラー！
pers_import_personDateError=日付フォーマットエラー！
pers_import_createTimeError=生成時間フォーマットエラー！
pers_import_phoneError=電話番号フォーマットエラー！
pers_import_phoneTooLong=電話番号は20桁以下にしてください！
pers_import_emailError=Emailフォーマットエラー!
pers_import_birthdayError=誕生日フォーマットエラー！
pers_import_nameError=姓と名は、nullまたは英語以外の文字または数字であってはなりません。
pers_import_firstnameError=姓と名に 「、」を含めることはできません!
pers_import_firstnameNotNull=名は未入力にできません！
pers_import_dataCheck=データチェック失敗！
pers_import_dataSaveFail=データ保存失敗！
pers_import_allSucceed=全データインポート成功！
pers_import_result=成功: {0}, 失敗: {1}
pers_import_result2=インポート結果
pers_import_result3=成功: {0}、アップデート済み:{1}、失敗:{3}
pers_import_notSupportFormat=このフォーマットをサポートしていません！
pers_import_selectCorrectFile=正しいファイルを選択してください！
pers_import_fileFormat=ファイルフォーマット
pers_import_targetFile=ファイル
pers_import_startRow=ヘッダースタート行
pers_import_startRowNote=デフォルトは第二行
pers_import_delimiter=区切り文字
pers_import_importingDataFields=データベースフィールド
pers_import_dataSourceFields=インポートデータフィールド
pers_import_total=合計
pers_import_dataUpdate=システム内の既存ユーザーIDをアップデートします。
pers_import_dataIsNull=ファイルにデータがありません！
pers_import_deptNotExist=部署は存在しません!
pers_import_deptIsNotNull=部署名は未入力にできません!
pers_import_pinNotEmpty=ユーザーIDはブランクにできません！
pers_import_nameNotEmpty=人名を空にすることはできません！
pers_import_siteCodeOnlyLetterNum=サイトコードフォーマットエラー
pers_import_cardNoFormatErrors=カードNo.フォーマットエラー
pers_import_cardsNotSupport=〔複数カード登録ユーザー〕機能が無効になっているため、複数のカードをユーザーにインポートできません
pers_import_personInfo=ユーザーインポート
pers_import_commentFormat=コメント形式が不正です！
pers_import_noComment={0}行と{1}列のデータにはコメントがありません.
pers_import_fieldRepeat={0}行と{1}列のデータは、コメント内のフィールド名が重複しています:{2}
pers_import_primaryKey=主キーとして少なくとも1つのフィールドが必要です
pers_import_templateIsRepeat=生体テンプレート{0}は重複しています!
pers_import_biologicalTemplate=生体テンプレートインポート
pers_import_uploadFileSuccess=アップロード成功、データ解析中、お待ちください...
pers_import_resolutionComplete=データが解析され、データベースのアップデートが開始されます
pers_import_mustField=インポートファイルには{0}列が含まれている必要があります。
pers_import_bioTemplateSuccess=生体テンプレートをインポート後は、各モジュールからデバイスに手動で同期する必要があります。
pers_import_personPhoto=ユーザー写真インポート
pers_import_opera_log=操作ログ
pers_import_error_log=エラーログ
pers_import_uploadFileSize=サイズが{0}以下のファイルをアップロードしてください！
pers_import_uploadFileSizeLimit=1回のインポートでは、500M以下のファイルをアップロードしてください。
pers_import_type=インポート方法
pers_import_photoType=写真
pers_import_archiveType=圧縮パッケージ
pers_import_startUpload=アップロードを開始
pers_import_addMore=さらに追加
pers_import_photoQuality=写真の品質
pers_import_original=オリジナル
pers_import_adaptive=適応
pers_import_adaptiveSize=（サイズ480*640）
pers_import_totalNumber=合計
pers_import_uploadTip=（アップロード中に写真を削除しないでください）
pers_import_addPhotoTip=アップロードする写真を追加してください。
pers_import_selectPhotoTip=アップロードしたい写真を選択してください。
pers_import_uploadResult=写真アップロード結果
pers_import_pleaseSelectPhoto=写真を選択してください
pers_import_multipleSelectTip=複数選択はCtrlキーを押してください。
pers_import_replacePhotoTip=選択した写真は既にアップロードリストに含まれています。置き換えますか？
pers_import_photoNamePinNotCorrespond=写真名とユーザーID不一致
pers_import_photoFormatRequirement=写真名にユーザーIDを付けてください。正しい形式はJPG/PNGです。写真名に特殊文字が含まれていないことを確認してください。
pers_import_filterTip=選択した写真の中にはプレビューできないものがあり、次のような理由が考えられます。
pers_import_photoContainSpecialCharacters=写真名に特殊文字が含まれている。
pers_import_photoFormatError=写真のフォーマットが正しくない。
pers_import_photoSelectNumber=写真を500枚以上選択しないでください。
pers_import_photoSelectNumberLimit={0}枚を超える写真を選択しないでください！
pers_import_fileMaxSize=画像が大きすぎます。5M未満の画像ファイルをアップロードしてください。
pers_import_notUploadPhotoNumber=アップロードされた画像は3000枚以下です！
pers_import_zipFileNotPhoto=圧縮パッケージにユーザー写真がありません。再選択してインポートしてください！
pers_import_personPlateRepeat=ナンバープレート{0}重複！
pers_import_filePlateRepeat=ファイル内のナンバープレート{0}重複！
pers_import_personPlateFormat=ナンバープレート{0}フォーマットエラー！
pers_import_personPlateMax=ナンバープレートが最大の6個を超えます！
pers_import_cropFaceFail=比較写真を生成できませんでした！
pers_import_pinLeaved=ユーザーID：{0}が辞任しました。
pers_import_exceedLicense=ソフトウェアライセンスを超える人数の輸入はできません。
pers_import_bioTemplateNotNull=ユーザーID、生体タイプ、生体ID、生体インデックス、内容、バージョンが空にできません！
pers_import_certTypeNotNull=ユーザーID：{0} IDタイプは空にできません
pers_import_certTypeNotExist=個人番号は次のとおりです:{0}文書型が存在しません
pers_import_certNumNotNull=ユーザ番号は次のとおりです{0}証明書番号は空にできません。
pers_import_certNumberTooLong=行{0}：ID番号{1}が長すぎます！
pers_import_idNumberErrors=行{0}：ID番号{1}の形式が間違っています！
pers_import_emailErrors=行{0}：メールアドレス{1}の形式が間違っています！
pers_import_emailIsExist=メールアドレス{0}はすでに存在します！
pers_import_emailIsRepeat=行{0}：ファイルの内部メールアドレス{1}が重複！
pers_import_fileMobilePhoneRepeat=行{0}：ファイル{1}の内部携帯電話番号が繰り返されました！
pers_import_mobilePhoneErrors=携帯電話番号のフォーマットエラー！
pers_import_hireDateError=入力日のフォーマットが正しくありません！
pers_import_selectPhotoType=写真をインポートするタイプを選択してください！
pers_import_hireDateLaterCurrent=採用日に現在の日付より後の日付は指定できません！
pers_import_buildingNotExist=建物は存在しません!
pers_import_unitNotExist=ユニットが存在しません!
pers_import_vdbInfoFail=建物 {0} には {1} という名前のユニット情報がありません。
pers_import_vdbBuildingFail=ユニット {0} の建物情報を空にすることはできません。
pers_import_vdbRoomNoFail=部屋番号は0より大きい数字でなければなりません！
#人员离职
pers_person_leave=辞任
pers_dimission_date=辞任日付け
pers_dimission_type=辞任タイプ
pers_dimission_reason=辞任理由
pers_dimission_volutary=希望退職
pers_dimission_dismiss=却下
pers_dimission_resignat=辞任
pers_dimission_shiftJob=転職
pers_dimission_leave=無給勤怠
pers_dimission_recovery=復職
pers_dimission_sureToRecovery=復職操作を実行しますか？
pers_dimission_backCard=返却カードか否か？
pers_dimission_isForbidAction=すぐにアクセスレベルを無効にしますか？
pers_dimission_writeInfomation=退職情報を入力してください
pers_dimission_pinRetain=退職したユーザーのIDを保持しますか？
pers_dimission_downloadTemplate=排出インポートテンプレートをダウンロードします
pers_dimission_import=ディスミッションのインポート
pers_dimission_importTemplate=排出インポートテンプレート
pers_dimission_date_noNull=終了日を空にすることはできません
pers_dimission_leaveType_noExist=排出タイプは存在しません
pers_dimission_dateFormat=必須フィールド、時間形式はyyyy-MM-dd（例：2020-07-22）
pers_dimission_leaveType=必須フィールド（希望退職、却下、辞任、転職）
pers_dimission_forbidden=ブラックリストに参加
pers_dimission_leaveType_noNull=休暇タイプを空にすることはできません！
pers_dimission_person_noExist=辞退者は存在しません！
pers_dimission_date_error=辞退日が記入されていないか、形式が正しくありません
#临时人员
pers_tempPerson_audit=審査
pers_tempPerson_view=表示
pers_tempPerson_waitReview=管理者の審査を待っています
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=カードNo.
pers_card_state=カード状況
pers_card_effect=有効
pers_card_disabled=無効
pers_card_past=期限切れ
pers_card_back=カード返却
pers_card_change=カード交換
pers_card_note=カードNo.は既に存在します
pers_card_numTooBig=カードNo.が長すぎます
pers_issueCard_entity=カード登録
pers_issueCard_operator=オペレータ
pers_issueCard_operate=アクション
pers_issueCard_note=カード登録は、権限のあるオペレータのみ可能です。
pers_issueCard_date=カード登録日
pers_issueCard_changeTime=変更日
pers_issueCard_cardValidate=スペースは許可されません
pers_issueCard_cardEmptyNote=カードは未入力にできません
pers_issueCard_cardHasBeenIssued=このカードは登録されました！
pers_issueCard_noCardPerson=カード無しユーザー
pers_issueCard_waitPerson=今回カード登録ユーザー
pers_issueCard_mc5000=MC5000ピン
pers_batchIssCard_entity=カード一括登録
pers_batchIssCard_startPersNo=開始ユーザーID
pers_batchIssCard_endPersNo=終了ユーザーID
pers_batchIssCard_issCardNum=カード登録数
pers_batchIssCard_notIssCardNum=カード未登録ユーザー数
pers_batchIssCard_generateList=生成リスト
pers_batchIssCard_startRead=読み取り開始
pers_batchIssCard_swipCard=カード読み取り方法
pers_batchIssCard_sendCard=デバイス
pers_batchIssCard_dispenCardIss=登録用リーダ
pers_batchIssCard_usbEncoder=USBエンコーダ
pers_batchIssCard_note=ユーザーIDは入力された数字のみサポートされ、カード未登録ユーザー（最大300）のみ表示します。
pers_batchIssCard_startPinEmpty=開始ユーザーIDは未入力未入力にできません！
pers_batchIssCard_endPinEmpty=終了ユーザーIDは、未入力にできません！
pers_batchIssCard_startPinLargeThanEndPin=開始ユーザーIDは、終了ユーザーIDより小さくなくてはなりません！
pers_batchIssCard_numberParagraphNoPerson=カード未登録ユーザーは、セグメント内に存在しません！
pers_batchIssCard_inputCardNum=カードNo.入力
pers_batchIssCard_cardLetter=カードNo.は文字をサポートしません！
pers_batchIssCard_cardNoTooLong=カードNo.が長すぎます！
pers_batchIssCard_issueWay=カード登録方法
pers_batchIssCard_noPersonList=未生成ユーザーリスト
pers_batchIssCard_startReadCard=カード読取り開始
pers_batchIssCard_swipePosition=読取り方法
pers_batchIssCard_chooseSwipePosition=読取り方法を選択してください
pers_batchIssCard_readCardTip=読取り方法がデバイスの場合、未登録カードのみ読み取ります。
pers_batchIssCard_notIssCardNo=未登録カード数
pers_batchIssCard_totalNumOfCards=合計カード数
pers_batchIssCard_acms=ACMSカード
pers_lossCard_entity=紛失カード数
pers_lossCard_lost=このカードは報告済みで、操作を繰り返すことはできません！
pers_losscard_note2=管理カードを書き込んだ後、このカードをエレベータリーダーで読み取り、カードデバイスで有効でないことを確認する必要があります。
pers_revertCard_entity=紛失カード再有効化
pers_revertCard_setReport=紛失カードを最初に報告してください！
pers_revertcard_note2=管理カード登録後、エレベーターリーダーでこのカードを読み取り、カードを再度使用できることを確認する必要があります。
pers_issueCard_success=カード登録成功！
pers_issueCard_error=カード登録失敗！
pers_cardData_error=例外カードフォーマット読み取り！
pers_analysis_error=例外カードデータ解析！
pers_cardOperation_error=例外カード操作！
pers_cardPacket_error=例外カードコマンドパッケージ操作
pers_card_write=カード書き込み
pers_card_init=カード初期化
pers_card_loss=紛失カード
pers_card_revert=カード再利用
pers_card_writeMgr=管理カード書き込み
pers_initCard_tip=初期化後、カードは空のカードになります
pers_initCard_prepare=カード初期化準備完了...
pers_initCard_process=カード初期化中...
pers_initCard_success=カード初期化成功
pers_mgrCard_prepare=管理カード書き込み準備中...
pers_mgrCard_process=管理カード書き込み中...
pers_mgrCard_success=管理カード書き込み成功
pers_userCard_prepare=ユーザーカード書き込み準備中...
pers_userCard_process=ユーザーカードデータ書き込み中..
pers_userCard_success=ユーザーカード書き込み
pers_userCard_tip=ユーザー編集ページで、開始時間と終了時間を設定してカードに書き込んでください
pers_userCard_tip2=権限データが空なので、カードに書き込みできません
pers_userCard_tip3=権限グループが、2台以上の関連デバイスで、データ損失しています
pers_writeCard_tip=エンコーダーのドライバをインストールして、エンコーダーを接続してください
pers_writeMgrCard_tip=紛失カードと再登録カード数は、18以上にできません
pers_writeMgrCard_tip2=紛失カードと再登録カード数：
pers_card_writeToMgr=管理カードに書き込み
pers_card_hex=カードフォーマット表示
pers_card_decimal=デシマル
pers_card_Hexadecimal=Hex
pers_card_IssuedCommandFail=登録コマンド失敗：
pers_card_multiCard=その他のカード
pers_card_deputyCard=複数カード
pers_card_deputyCardValid=最初にメインカードを入力してください！
pers_card_writePinFormat=システムは、カードにユーザーIDのみ書き込み可能で、文字は書き込みできません！
pers_card_notMoreThanSixteen=複数カードは、最大16まで設定可能です。
pers_card_notDelAll=すべての複数カードを削除できません
pers_card_maxCard=カードは%sを超えることはできません.
pers_card_posUseCardNo=本人のメインカードがコンシューマモジュールで使用されています。コンシューマモジュールに移動して、カードの払い戻し操作を実行してください。
pers_card_delFirst=カードを発行する前にカード番号を削除してください！
pers_card_disablePersonWarn=選択したカード番号には障害者が含まれており、操作できません！
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=サイトコード
pers_wiegandFmt_wiegandMode=モード
pers_wiegandFmt_wiegandModeOne=モード1
pers_wiegandFmt_wiegandModeTwo=モード2
pers_wiegandFmt_isDefaultFmt=自動マッチ
pers_wgFmt_entity=Wiegandフォーマット
pers_wgFmt_in=Wiegand入力フォーマット
pers_wgFmt_out=Wiegand出力フォーマット
pers_wgFmt_inType=Wiegand入力タイプ
pers_wgFmt_outType=Wiegand出力タイプ
pers_wgFmt_wg=Wiegand
pers_wgFmt_totalBit=合計ビット
pers_wgFmt_oddPch=オッドパリティチェック(o)
pers_wgFmt_evenPck=イーブンパリティチェック(e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=ファシリティコード(f)
pers_wgFmt_siteCode=サイトコード(s)
pers_wgFmt_manufactoryCode=マニファクチャーコード(m)
pers_wgFmt_firstParity=ファーストパリティチェック(p)
pers_wgFmt_secondParity=セカンドパリティチェック(p)
pers_wgFmt_cardFmt=カードチェックフォーマット
pers_wgFmt_parityFmt=パリティチェックフォーマット
pers_wgFmt_startBit=スタートビット
pers_wgFmt_test=カードフォーマットテスト
pers_wgFmt_error=カードフォーマットテストエラー!
pers_wgFmt_verify1=総ビット数は80を超えてはいけません！
pers_wgFmt_verify2=Check card format length must be equal to the total number of bits!
pers_wgFmt_verify3=Parity format length must be equal to the total number of digits!
pers_wgFmt_verify4=The initialized data can not be deleted!
pers_wgFmt_verify5=カードフォーマットが使用されているため、削除できません！
pers_wgFmt_verify6=The first parity bit can not be greater than the total number of!
pers_wgFmt_verify7=Second parity bit can not be greater than the total number of!
pers_wgFmt_verify8=Start and maximum length format is not correct!
pers_wgFmt_verify9=Card check format length can not be greater than the total number of!
pers_wgFmt_verify10=Card check digit function can not cross!
pers_wgFmt_verify11=The site code exceeds the set range!
pers_wgFmt_verify=Check
pers_wgFmt_unverify=Non Check
pers_wgFmt_atLeastDefaultFmt=Please keep at least one automatically matching card format!
pers_wgFmt_defaultFmtError1=There are some other card format with the same card number bits,can not be set to automatically matching card format!
pers_wgFmt_defaultFmtError2=Presence of the same card format to automatic matching, the operation fails!
pers_wgFmt_cardFormats=カードフォーマット
pers_wgFmt_cardFormatTesting=カードフォーマットテスト
pers_wgFmt_checkIsUsed=カード形式は{0}で使用されており、削除できません。
pers_wgFmt_supportDigitsNumber=デバイスがサポートするビット数の長さを入力してください
#选人控件
pers_widget_selectPerson=ユーザー選択
pers_widget_searchType1=クエリ
pers_widget_searchType2=部署
pers_widget_deptHint=注：選択した部門のすべてのユーザーをインポートします
pers_widget_noPerson=ユーザー未選択
pers_widget_noDept=部署を選択してください
pers_widget_noDeptPerson=選択された部署にユーザーが存在しません。再選択してください！
#人员属性
pers_person_carPlate=ナンバープレート
pers_person_socialSecurity=社会保障番号
pers_person_msg4=最大長は20です！
pers_person_msg5=名前の最大数は50文字です！
pers_person_type=ユーザータイプ
pers_person_reseCode=予約コード
pers_person_IsSendMail=イベント通知
pers_person_inactive=無効
pers_person_active=有効
pers_person_employee=職員
pers_person_isSendMailMsg=[イベント通知]機能を有効にするためには、最初にEmailを入力してください
pers_person_createTime=生成時間
pers_person_pinFirstValid=ユーザーNo.の最初を8または9にすることはできません
pers_person_attrValueValid=フィールドの値は重複できません
pers_person_attrValueDelimiterValid=区切り文字は中央にある必要があります。
pers_person_isSendSMS=SMS通知
pers_person_building=建物名
pers_person_unitName=ユニット名
pers_person_roomNo=部屋番号
#动态属性
pers_attr_emp_type=ユーザータイプ
pers_attr_street=雇用先
pers_attr_nation=国籍
pers_attr_office_address=会社住所
pers_attr_postcode=郵便番号
pers_attr_office_phone=会社電話
pers_attr_home_phone=自宅電話
pers_attr_job_title=役職
pers_attr_birthplace=出身地
pers_attr_polit_status=本籍地
pers_attr_country=雇用地
pers_attr_home_address=現住所
pers_attr_hire_type=雇用形態
pers_attr_inContract=契約職員
pers_attr_outContract=正職員
#属性自定义
pers_attribute_attrName=表示名
pers_attribute_attrValue=属性値
pers_attribute_controlType=入力タイプ
pers_attribute_positionX=行
pers_attribute_positionY=列
pers_attribute_showTable=ユーザーリストに表示
pers_attrDefini_deletemsg=このプロパティは使用中ですが、削除しますか？
pers_attrDefini_reserved=システム予約フィールド名
pers_attrDefini_msg1=最大長は30です！
pers_attrDefini_msg2=列の行は既に存在します、別の場所に変更してください！
pers_attrDefini_attrValue_split=区切り文字「;」を使用。
pers_attrDefini_attrName=属性名
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=属性ID
pers_attrDefini_select=プルダウンリスト
pers_attrDefini_check=複数選択
pers_attrDefini_radio=単独選択
pers_attrDefini_text=テキスト
pers_attrDefini_maxCol=2列以下
pers_attrDefini_maxLimit=カスタム属性が最大値に達しました！
pers_attrDefini_modControlType=入力タイプを変更すると、システム内のすべてのユーザーの現在のフィールドデータが消去されますが続行しますか？
#leavePerson
pers_leavePerson_reinstated=復職
#opExample
pers_example_newRecode=新しいトランザクション取得
pers_example_allRecode=全トランザクション取得
pers_custField_StatisticalType=統計タイプ
#人员参数修改
pers_param_isAudit=臨時スタッフの自動レビューを有効にする
pers_param_donotChangePin=存在するユーザー IDには文字が含まれていますが、[文字サポート]機能を無効にすることはできません。
pers_param_hexChangeWarn=システムには既にカードNoがあり、カードフォーマット表示モードを変更することはできません。
pers_param_cardsChangeWarn=システムに複数のカードを持つユーザーが存在するので、[複数カードユーザー]機能を無効にすることはできません。
pers_param_maxPinLength=新しいユーザーIDの長さは、システム内に存在するユーザーIDの長さよりも短くすることはできません。
pers_param_pinBeyondDevLength=システムのデバイスでサポートされているユーザーID最大長は{0}です、{1}未満の整数を入力してください
pers_param_cardBeyondDevLength=システムのデバイスでサポートされているカードNo最大長は{0}です、{1}未満の整数を入力してください
pers_param_checkIsExistNoAudit=現在のシステムには未登録の登録済み担当者がおり、自動レビューに変更できません！
pers_param_noSupportPinLetter=システムに文字を含む個人番号をサポートしていないデバイスがあり、個人番号モードを変更できません。
pers_param_changePinLettersTip=ユーザーIDは自動インクリメントをサポートし、ユーザーIDモードは変更できません
pers_param_changePinIncrementTip=ユーザーIDサポートには文字が含まれており、ユーザーIDモードは変更できません
pers_param_qrCode=企業QRコード
pers_param_employeeRegistrar=クラウドユーザー登録有効
pers_param_downloadQRCodePic=QRコード画像をダウンロード
pers_param_qrCodeUrl=QRコードUrl
pers_param_qrCodeUrlCreate=セルフサービス登録
pers_param_qrCodeUrlHref=サーバーアドレス：ポート
pers_param_pinSetWarn=現在のシステムにはすでに人員がおり、人員番号モードを変更することはできません。
pers_param_selfRegistration=自己登録を有効にする
pers_param_infoProtection=個人の機密情報の保護
pers_param_infoProtectionWarnMsg=個人の機密情報セキュリティ保護オプションを有効にした後、このモジュールに含まれる機密の個人データは、名前、カード番号、ID 番号、写真などを含むがこれらに限定されず、鈍感化または隠蔽されます。
pers_param_templateServer=顔テンプレート抽出サーバ
pers_param_enableFacialTemplate=顔テンプレート抽出を有効にする
pers_param_templateServerAddr=顔テンプレート抽出サーバアドレス
pers_param_templateServerWarnInfo=顔テンプレート抽出が有効になっている場合、顔テンプレート抽出サーバーがオンラインでユーザーの検証に合格した場合、人は写真を比較する際に顔テンプレートをデフォルトで抽出します。顔テンプレート抽出サーバーがオフラインモードの場合は、顔テンプレートを抽出しないでください！
pers_param_templateServerWarnInfo1=顔テンプレート抽出を有効にするには、顔テンプレート抽出をサポートするデバイスを接続する必要があります！
pers_param_templateServerOffline=顔テンプレート抽出サーバーはオフラインで、顔テンプレートを抽出できません！続行しますか？
pers_param_faceServer=顔楽屋照合サービス
pers_param_enableFaceVerify=顔のバックグラウンド照合を有効にする
pers_param_faceServerAddr=顔楽屋照合サービスアドレス
pers_param_faceServerSecret=顔楽屋照合サービス鍵
#国籍
pers_person_nationality=国籍
pers_nationality_angola=Angolan
pers_nationality_afghanistan=Afghan
pers_nationality_albania=Albanian
pers_nationality_algeria=Algerian
pers_nationality_america=American
pers_nationality_andorra=Andorran
pers_nationality_anguilla=Anguillan
pers_nationality_antAndBar=Antigua and Barbuda
pers_nationality_argentina=Argentinan
pers_nationality_armenia=Armenians
pers_nationality_ascension=Ascension
pers_nationality_australia=Australian
pers_nationality_austria=Austrian
pers_nationality_azerbaijan=Azerbaijani
pers_nationality_bahamas=Bahamas
pers_nationality_bahrain=Bahrain
pers_nationality_bangladesh=Bangladesh
pers_nationality_barbados=Barbadian
pers_nationality_belarus=Belarus
pers_nationality_belgium=Belgium
pers_nationality_belize=Belizean
pers_nationality_benin=Beninese
pers_nationality_bermudaIs=Bermuda Is.
pers_nationality_bolivia=Bolivia
pers_nationality_botswana=Botswanan
pers_nationality_brazil=Brazil
pers_nationality_brunei=Brunei
pers_nationality_bulgaria=Bulgarian
pers_nationality_burkinaFaso=Burkina Faso
pers_nationality_burma=Burma
pers_nationality_burundi=Burundian
pers_nationality_cameroon=Cameroonian
pers_nationality_canada=Canadian
pers_nationality_caymanIs=Cayman Islands
pers_nationality_cenAfrRepub=Central African Republic
pers_nationality_chad=Chadian
pers_nationality_chile=Chile
pers_nationality_china=Chinese
pers_nationality_colombia=Colombian
pers_nationality_congo=Republic of the Congo
pers_nationality_cookIs=Cook Is.
pers_nationality_costaRica=Costarican
pers_nationality_cuba=Cuban
pers_nationality_cyprus=Cyprus
pers_nationality_czechRep=Czech Republic
pers_nationality_denmark=Denmark
pers_nationality_djibouti=Djiboutis
pers_nationality_dominicaRep=Dominica Rep.
pers_nationality_ecuador=Ecuador
pers_nationality_egypt=Egyptian
pers_nationality_eISalvador=EI Salvador
pers_nationality_england=British
pers_nationality_estonia=Estonian
pers_nationality_ethiopia=Ethiopian
pers_nationality_fiji=Fijian
pers_nationality_finland=Finland
pers_nationality_france=France
pers_nationality_freGui=French Guiana
pers_nationality_gabon=Gabonese
pers_nationality_gambia=Gambia
pers_nationality_georgia=Georgian
pers_nationality_germany=German
pers_nationality_ghana=Ghanaian
pers_nationality_gibraltarm=Gibraltar
pers_nationality_greece=Greek
pers_nationality_grenada=Grenada
pers_nationality_guam=Guam
pers_nationality_guatemala=Guatemalan
pers_nationality_guinea=Guinean
pers_nationality_guyana=Guyana
pers_nationality_haiti=Haiti
pers_nationality_honduras=Honduras
pers_nationality_hungary=Hungarian
pers_nationality_iceland=Icelander
pers_nationality_india=Indian
pers_nationality_indonesia=Indonesians
pers_nationality_iran=Iranian
pers_nationality_iraq=Iraqi
pers_nationality_ireland=Irish
pers_nationality_israel=Israeli
pers_nationality_italy=Italian
pers_nationality_ivoryCoast=Lvoirian
pers_nationality_jamaica=Jamaican
pers_nationality_japan=Japan
pers_nationality_jordan=Jordan
pers_nationality_kenya=Kenyan
pers_nationality_korea=Korean
pers_nationality_kuwait=Kuwaiti
pers_nationality_kyrgyzstan=Kyrgyzstan
pers_nationality_laos=Laotian
pers_nationality_latvia=Latvian
pers_nationality_lebanon=Lebanese
pers_nationality_lesotho=Mesotho
pers_nationality_liberia=Liberian
pers_nationality_libya=Libyan
pers_nationality_liechtenstein=Liechtensteiner
pers_nationality_lithuania=Lithuanian
pers_nationality_luxembourg=Luxemburgues
pers_nationality_madagascar=Malagasy
pers_nationality_malawi=Malawian
pers_nationality_malaysia=Malaysia
pers_nationality_maldives=Maldives
pers_nationality_mali=Mali
pers_nationality_malta=Maltese
pers_nationality_marianaIs=Mariana Is.
pers_nationality_martinique=Martinique
pers_nationality_mauritius=Mauritian
pers_nationality_mexico=Mexican
pers_nationality_moldova=Moldova
pers_nationality_monaco=Monacan
pers_nationality_montseIs=Montserrat Is.
pers_nationality_morocco=Moroccan
pers_nationality_mozambique=Mozambican
pers_nationality_namibia=Namibian
pers_nationality_nauru=Nauruan
pers_nationality_nepal=Nepal
pers_nationality_netAnti=Netheriands Antilles
pers_nationality_netherlands=Netherlands
pers_nationality_newZealand=New Zealander
pers_nationality_nicaragua=Nicaragua
pers_nationality_niger=Nigerien
pers_nationality_nigeria=Nigeria
pers_nationality_norKorea=North Korea
pers_nationality_norway=Norwegian
pers_nationality_oman=Omani
pers_nationality_pakistan=Pakistani
pers_nationality_panama=Panama
pers_nationality_papNewCui=Papua New Guinean
pers_nationality_paraguay=Paraguay
pers_nationality_peru=Peru
pers_nationality_philippines=Philippines
pers_nationality_poland=Poland
pers_nationality_frenPolyne=French Polynesia
pers_nationality_portugal=Portuguese
pers_nationality_puerRico=Puerto Rico
pers_nationality_qatar=Qatari
pers_nationality_reunion=Reunion
pers_nationality_romania=Romanian
pers_nationality_russia=Russian
pers_nationality_saiLueia=Saint Lueia
pers_nationality_saintVinc=Saint Vincent
pers_nationality_samoa_eastern=Samoa Eastern
pers_nationality_samoa_western=Samoa Western
pers_nationality_sanMarino=San Marinese
pers_nationality_saoAndPrinc=Sao Tome and Principne
pers_nationality_sauArabia=Saudi Arabia
pers_nationality_senegal=Senegalese
pers_nationality_seychelles=Seychellese
pers_nationality_sieLeone=Sierra Leone
pers_nationality_singapore=Singapore
pers_nationality_slovakia=Slovakian
pers_nationality_slovenia=Slovenian
pers_nationality_solomonIs=Solomon islander
pers_nationality_somali=Somali
pers_nationality_souAfrica=South African
pers_nationality_spain=Spanish
pers_nationality_sriLanka=Sri Lankan
pers_nationality_sudan=Sudan
pers_nationality_suriname=Suriname
pers_nationality_swaziland=Swazi
pers_nationality_sweden=Swedish
pers_nationality_switzerland=Swiss
pers_nationality_syria=Syrian
pers_nationality_tajikstan=Tajik
pers_nationality_tanzania=Tanzanian
pers_nationality_thailand=Thai
pers_nationality_togo=Togolese
pers_nationality_tonga=Tongan
pers_nationality_triAndToba=Trinidad and Tobago
pers_nationality_tunisia=Tunisian
pers_nationality_turkey=Turk
pers_nationality_turkmenistan=Turkmenistan
pers_nationality_uganda=Ugandan
pers_nationality_ukraine=Ukrainian
pers_nationality_uniArabEmira=United Arab Emirates
pers_nationality_uruguay=Uruguay
pers_nationality_uzbekistan=Uzbekistan
pers_nationality_venezuela=Venezuela
pers_nationality_vietnam=Vietnamese
pers_nationality_yemen=Yemeni
pers_nationality_serbia=Serbia
pers_nationality_zimbabwe=Zimbabwean
pers_nationality_zambia=Zambian
pers_nationality_aruba=Aruba
pers_nationality_bhutan=Bhutan
pers_nationality_bosnia_herzegovina=Bosnia and Herzegovina
pers_nationality_cambodia=Cambodia
pers_nationality_congoD=Democratic Republic of the Congo
pers_nationality_comoros=La Unión de las Comoras
pers_nationality_capeVerde=Republic of Cape Verde
pers_nationality_croatia=Croatia
pers_nationality_dominica=Dominica
pers_nationality_eritrea=Eritrea
pers_nationality_micronesia=Micronesia
pers_nationalit_guineaBissau=Guinea-Bissau
pers_nationalit_equatorialGuinea=Equatorial Guinea
pers_nationalit_hongkong=Hongkong
pers_nationalit_virginIslands=The United States Virgin Islands 
pers_nationalit_britishVirginIslands=The British Virgin Islands
pers_nationalit_kiribati=Kiribati
pers_nationalit_mongolia=Mongolia
pers_nationalit_marshall=Marshall Island
pers_nationalit_macedonia=Macedonia
pers_nationalit_montenegro=Montenegro
pers_nationalit_mauritania=Mauritania
pers_nationalit_palestine=Palestine
pers_nationalit_palau=Palau
pers_nationalit_rwanda=Rwanda
pers_nationalit_saintKittsNevis=Saint Kitts and Nevis
pers_nationalit_timorLeste=Timor Leste
pers_nationalit_taiwan=Taiwan
pers_nationalit_tuvalu=Tuvalu
pers_nationalit_vanuatu=Vanuatu
#制卡
pers_person_cardprint=カード印刷
pers_cardTemplate_tempSelect=テンプレート選択
pers_cardTemplate_printerSelect=プリンター選択
pers_cardTemplate_front=前
pers_cardTemplate_opposite=後
pers_cardTemplate_entryDate=雇用日
pers_cardTemplate_photo=写真
pers_cardTemplate_uploadFail=画像アップロード失敗!
pers_cardTemplate_jpgFormat=アップロード画像は、JPGフォーマットのみサポートします!
pers_cardTemplate_printStatus=印刷状況
pers_cardTemplate_waiting=お待ちください
pers_cardTemplate_printing=印刷中
pers_cardTemplate_printOption=印刷オプション
pers_cardTemplate_duplexPrint=両面印刷
pers_cardTemplate_frontOnly=前面のみ印刷
#app
pers_app_delPers=サーバに削除したいユーザーが存在しません
pers_app_deptIsNull=部署に対応する部署No.または名前が見つかりません
pers_app_personNull=ユーザーが見つかりません
pers_app_pinExist=このユーザーIDは既に存在します
pers_app_dateError=日付のフォーマットが正しくありません。正しいフォーマットを参照してください：2016-08-08
#api
pers_api_selectPhotoInvalid=写真が無効です。もう一度アップロードしてください
pers_api_dateError=日付の形式が正しくありません
pers_api_personNotExist=人は存在しません
pers_api_cardsPersSupport=システムが1人のマルチカードをオンにしない、セカンダリカードが無効に設定されている
pers_api_department_codeOrNameNotNull=部門コードまたは部門名は空にできません
pers_api_deptSortNoIsNull=部門の並べ替えを空にすることはできません！
pers_api_deptSortNoError=部門の並べ替えの値は1〜999999の間でなければなりません！
pers_api_dataLimit=現在のオペランドは{0}で、{1}の制限を超えています。バッチ操作してください！
pers_api_cardTypeError=カードタイプエラー
#人员生物模板API
pers_api_fingerprintExisted=その人の指紋はすでに存在しています！
pers_api_validtypeIncorrect=認証タイプが正しくありません！
pers_api_dataNotExist=templateNoは存在しません！
pers_api_templateNoRang=指紋テンプレート番号の範囲は0〜9です。
pers_api_templateIsNull=テンプレートを空にすることはできません！
pers_api_versionIsNumber=バージョンには数字しか入力できません！
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=ユーザーIDは数字のみ入力できます！
#biotime
pers_h5_personAvatarNotNull=アバターが空です
pers_h5_personMobileRepeat=電話番号はすでに存在します
pers_h5_personEmailRepeat=メールボックスはすでに存在します
pers_h5_pwdIsRepetition=古いパスワードと新しいパスワードの重複
pers_h5_personIdNull=ユーザーIDが空です
pers_h5_pinOrEmailIsNull=番号とメールアドレスを入力してください
pers_emailTitle_resetPassword=パスワードを変更する
pers_emailContent_resetPassword=リンクは24時間有効です。リンクをブラウザーにコピーして、パスワードを変更します。
pers_h5_tokenIsNull=証明書が空です
pers_h5_tokenError=無効または無効な証明書
pers_h5_oldPwdIsError=古いパスワードが間違っています
pers_api_resetPasswordSuccess=パスワードが正常に変更されました
pers_api_resetPasswordFail=パスワードの変更に失敗しました
pers_api_forgetPassword=パスワードをリセット
pers_api_confirmSubmit=送信を確認
pers_api_confirmPwdCaution=「OK」をクリックして新しいパスワードを確認します
pers_api_pwdRule=パスワードには、少なくとも1つの記号または数字が含まれ、長さが8〜12文字以上である必要があります
pers_h5_personPinFormatNumber=ユーザーIDは数字または文字のみで構成できます
pers_h5_persEmailNoExist=間違ったメール
pers_h5_pageNull=ページネーションパラメータエラー
pers_h5_personPinNotStartWithZero=ユーザーIDを0から始めることはできません
pers_h5_personPinTooLong=ユーザーIDが長すぎます
pers_h5_personPinInValid=番号はすでに使用されています
pers_h5_imgSizeError=サイズが10M以下の画像をアップロードしてください。
pers_h5_confirmAndContinue=確認して続行
#人脸抠图不及格错误
pers_face_poorResolution=80000ピクセル未満の画像解像度
pers_face_noFace=顔が検出されませんでした
pers_face_manyFace=複数の顔が検出されました
pers_face_smallFace=顔の比率が小さすぎます
pers_face_notColor=画像は非カラー画像です
pers_face_seriousBlur=画像がぼやけている
pers_face_intensivelyLight=画像がかなり露出している
pers_face_badIllumination=画像が暗すぎる
pers_face_highNoise=ノイズの多い写真
pers_face_highStretch=伸ばした顔
pers_face_covered=顔が覆われています
pers_face_smileOpenMouth=過剰な笑顔
pers_face_largeAngle=顔の偏向角度が大きすぎます
pers_face_criticalIllumination=画像の明るさが重要
pers_face_criticalLargeAngle=臨界面偏向角
pers_face_validFailMsg=次の理由で顔検出に失敗しました：
pers_face_failType=面切り不良タイプ
pers_face_photoFormatError=写真の形式が正しくありません。JPG/ PNG形式のファイルをアップロードしてください。
pers_face_notUpdateMsg=顔写真の生成に失敗しました。顔写真を更新しないでください。
#健康申报
pers_health_enable=健康情報の宣言を有効にする
pers_health_attrExposure=疑われる症例への接触
pers_health_attrSymptom=過去14日間の症状
pers_health_attrVisitCity=過去14日間に訪れた市
pers_health_attrRemarks=健康について
pers_health_symptomCough=咳
pers_health_symptomFever=発熱
pers_health_symptomPolypena=呼吸不良
pers_health_declaration=健康情報ステートメント
pers_health_aggrement=必要事項をご記入いただけない場合はアクセスをお断りさせていただきますので、誠にご報告をいただけない場合は、来館を継続できず、法的責任を負うものとします。
pers_health_visitCity_notEmpty=訪れた都市は空にすることはできません！
pers_health_notAgree=続行するには契約を確認してください。
#人员名单库
pers_personnal_list_manager=リストマネージャー
pers_personnal_list=リストライブラリ
pers_personnal_list_scheme=ドメインモード
pers_personnal_list_name=個人リスト名
pers_personnal_list_group_str_id=リストグループID
pers_personnal_list_personCount=人数
pers_personnal_list_tag=ユーザー定義
pers_personnal_list_type=個人リストタイプ
pers_personnallist_addPerson_repo=リストリポジトリに人を追加する
pers_personnallist_sendPersonnallist=分散リストライブラリ
pers_personnallist_sendPerson=発行者
pers_personnallist_notDel_existPerson=リストライブラリには人が含まれているため、削除できません
pers_personnallist_peopleInRoster=リストライブラリにはまだ人がいます
pers_personnallist_associationNotExist=メインデバイスとリストライブラリ間の関連付けが存在しません
pers_personnal_list_person=個人リストの人
pers_personnal_list_dev=ライブラリのアクセス許可を一覧表示します
pers_personnal_list_addDev=デバイスを追加
pers_personnal_list_name_isExist=名前はすでに存在します
pers_personnal_bannedList=禁止リストライブラリ
pers_personnal_allowList=リストライブラリを許可する
pers_personnal_redList=レッドリストライブラリ
pers_personnal_attGroup=出席グループ
pers_personnal_passList=パスリスト
pers_personnal_banList=禁止リスト
pers_personnal_visPassList=ビジターパスリスト
pers_personnal_visBanList=ほうもんしゃきんしリスト
pers_personnal_databaseHasBeenDistributed=選択したリストライブラリはすでに下送されました。削除は許可されません。
pers_personnel_sendError_dueTo=配信の失敗失敗の理由：
pers_personnel_sendError_reson={0}は禁止リストに存在します。削除して追加してください
#比对照片-样片示例
pers_examplePic_Tip=次の要件を満たす必要があります:
pers_examplePic_Tip1=1.背景色は純白で、職員は黒い服を着ています。
pers_examplePic_Tip2=2. 電子写真は JPG、PNG、JPEG ファイル形式で、推奨ピクセル範囲: 480*640 < ピクセル < 1080*1920。
pers_examplePic_Tip3=3. 電子写真のポートレートは、目を開けてまっすぐ前を見て、瞳孔がはっきりと見えるようにする必要があります。
pers_examplePic_Tip4=4. 電子写真の肖像画は中立的な表情をしている必要があり、微笑むことはできますが、歯を見せてはいけません。
pers_examplePic_Tip5=5. 電子写真のポートレートは、自然な色、豊富なレイヤー、明らかな歪みがなく、鮮明である必要があります。 肖像画の顔や背景に影、ハイライト、反射がなく、コントラストと明るさが適切です。
pers_examplePic_description=正しい例
pers_examplePic_error=エラーの例:
pers_examplePic_error1=大げさな表現 (過剰な笑顔)
pers_examplePic_error2=ライトが暗すぎます
pers_examplePic_error3=顔が小さすぎます (解像度が小さすぎます)
pers_applogin_enabled=appログインを有効にする
pers_applogin_disable=appログインを無効にする
pers_applogin_status=appログイン有効状態