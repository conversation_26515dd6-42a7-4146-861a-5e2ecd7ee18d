#pers common.
#here other module can also use the label from pers.
pers_module=Personel
pers_common_addPerson=Dodawanie personelu
pers_common_delPerson=Usunięcie personelu
pers_common_personCount=Il<PERSON><PERSON>ć personeli
pers_common_browsePerson=Przeglądanie personeli 
#Menu w lewo
pers_person_manager=Zarządzanie personelem 
pers_person=Personel
pers_department=Dział
pers_leave=Personel zrezygnowany 
pers_tempPerson=Personel do potwierdzenia
pers_attribute=Przystosowanie atrybutu
pers_card_manager=Zarządzanie kartą
pers_card=Karta
pers_card_issue=Zapis wytwarzania karty
pers_wiegandFmt=Format Wiegand
pers_position=Stanowisko 
#Personel
pers_person_female=Kobieta
pers_person_male=Mężczyzna
pers_person_pin=Numer personelu
pers_person_departmentChange=Dostosowanie działów
pers_personDepartment_changeLevel=Zmiana działu
pers_person_gender=<PERSON><PERSON><PERSON><PERSON>
pers_person_detailInfo=Szczegóły informacji
pers_person_accSet=Ustawienia kontroli bramy
pers_person_accSetting=Ustawienia kontroli bramy
pers_person_attSet=Ustawienia kontroli obecności
pers_person_eleSet=Ustawienia kontroli windy
pers_person_eleSetting=Ustawienia kontroli windy
pers_person_parkSet=zarejestrowanie tablicy rejestracyjnej
pers_person_pidSet=Ustawienia dopasowania dokumentu do człowieka
pers_person_insSet=Ustawienia ekranu informacyjnego
pers_person_aiSet=Ustawienia percepcji twarzy 
pers_person_payrollSet=Ustawienia wynagrodzenia
pers_person_psgSet=Ustawienia kanałów 
pers_person_lockerSet=Ustawienia szafki
pers_person_sisSet=Ustawienia kontroli bezpieczeństwa
pers_person_vdbSet=Ustawienia interkomu wizualnego
pers_person_firstName=Imię
pers_person_lastName=Nazwisko
pers_person_name=Imię i nazwisko
pers_person_wholeName=Imię i nazwisko
pers_person_fullName=Nazwa 
pers_person_cardNum=Ilość karty
pers_person_deptNum=Ilość działów dotyczących 
pers_person_dataCount=Dane statystyczne
pers_person_regFinger=Odcisk palca
pers_person_reg=Rejestrowanie 
pers_person_password=Kod weryfikacji urządzenia 
pers_person_personDate=Data zatrudniona 
pers_person_birthday=Data urodzenia 
pers_person_mobilePhone=Numer komórki
pers_person_personDevAuth=Uprawienie urządzenia personelu
pers_person_email=Email
pers_person_browse=Przeglądanie
pers_person_authGroup=Grupa uprawień
pers_person_setEffective=Ustawienia czasu ważnego
pers_person_attArea=Obszar kontroli obecności
pers_person_isAtt=Czy włączać kontrolę obecności
pers_person_officialStaff=Regularny pracownik 
pers_person_probationStaff=Próbny pracownik 
pers_person_identity_category=Kategoria pracownika
pers_person_devOpAuth=Uprawienie do obsługiwania urządzenia 
pers_person_msg1=Należy wprowadzić fizyczny numer karty i numer kierunkowy jednocześnie.
pers_person_msg2=Należy wprowadzić numer z 3-4 liczb.
pers_person_msg3=Błąd formatu!
pers_person_imgPixel=(Najlepszy rozmiar jest 120*140)
pers_person_cardLengthDigit=Należy wprowadzić liczbę!
pers_person_cardLengthHexadecimal=Należy wprowadzić liczby lub litery abcdef!
pers_person_to=Do
pers_person_templateCount=Ilość odcisków palców
pers_person_biotemplateCount=Ilość szablonów biologicznych
pers_person_regFace=Twarz
pers_person_regVein=Żyła palcowa
pers_person_faceTemplateCount=Ilość twarzy
pers_person_VeinTemplateCount=Ilość żył palcowych
pers_person_palmTemplateCount=Ilość odbitek dłoni
pers_person_cropFace=Zdjęcia do dopasowania
pers_person_cropFaceCount=Ilość zdjęć dopasowanych
pers_person_faceBiodataCount=Ilość twarzy widocznych
pers_person_irisCount=Liczba irisów
pers_person_batchToDept=Dział po transferze
pers_person_changeReason=Przyczyna transferu
pers_person_selectedPerson=Należy wybrać personelu
pers_person_duressPwdError=Powtarzalne hasło
pers_person_completeDelPerson=Usunięcie
pers_person_recover=Uzyskiwanie
pers_person_nameNoComma=Nie może zawierać przecinków
pers_person_firstNameNotEmpty=Imię nie może być puste
pers_person_lastNameNotEmpty=Nazwisko nie może być puste
pers_person_mobilePhoneValidate=Należy wprowadzić ważny numer komórki
pers_person_phoneNumberValidate=Należy wprowadzić ważny numer telefonu
pers_person_postcodeValidate=Należy wprowadzić ważny kod pocztowy
pers_person_idCardValidate=Należy wprowadzić ważny numer dowodu osobistego
pers_person_pwdOnlyLetterNum=Hasło może zawierać tylko litery lub liczby
pers_person_certNumOnlyLetterNum=Numer dokumentu może zawierać tylko litery lub liczby
pers_person_oldDeptEqualsNewDept=Oryginalny dział i dział do transferu są takie samy
pers_person_disabled=Dostęp wyłączony
pers_person_emailError=Nieprawidłowy adres mailowy
pers_person_driverPrompt=Należy zainstalować sterownik sprzętowy do rozpoznania dowodu osobistego! Należy kliknąć potwierdzenie do pobrania sterownika.
pers_person_readIDCardFailed=Awaria przyciągnięcia karty!
pers_person_cardPrompt=Należy położyć chiński dowód osobisty drugiej generalicji do obszaru odczytu karty…
pers_person_iDCardReadOpenFailed=Czytnik do dowodu osobistego chińskiego drugiej generacji nie został wykryty!
pers_person_iDCardNotFound=Dowód osobisty nie został czytane, należy przesunąć ponownie.
pers_person_nameValid=Przysługuje chińskie znaki, litery angielskie, liczby, \'-\', \'_\'.
pers_person_nameValidForEN=Przysługuje litery angielskie, liczby, \'-\', \'_\', \'.\'.
pers_person_pinPrompt=Należy wprowadzić kombinację z liter angielskich i liczb.
pers_person_pinSet=Ustawienia numeru personelu
pers_person_supportLetter=Przysługuje litery
pers_person_cardsSupport=Włączenie trybu wielokartowego dla jednego personelu
pers_person_SupportDefault=Ponumerowanie automatyczne
pers_person_noSpecialChar=Nie można wprowadzić specjalnych znaków w nazwach personelu.
pers_person_pinInteger=Należy wprowadzić kombinację liczb
pers_pin_noSpecialChar=Numer osoby nie może zawierać znaków specjalnych!
pers_op_capture=Uchwycenie
pers_person_cardDuress=Numer karta powtarzalna
pers_person_pwdException=Anomalia hasła
pers_person_systemCheckTip=Anomalia informacji o personelu została wykryta!
pers_person_immeHandle=Wytwarzanie natychmiast
pers_person_cardSet=Ustawienia karty
pers_person_tempPersonSet=Ustawienia personelu tymczasowego
pers_person_cardsReadMode=Metoda odczytu numeru karty
pers_person_cardsReadModeReadHead=Głowica odczytu sterownika
pers_person_cardsReadModeID180=ID180 (odczytanie fizycznego numeru dowodu osobistego)
pers_person_IDReadMode=Metoda odczytu dowodu osobistego
pers_person_IDReadModeIDCardReader=Czytelnik dowodu osobistego
pers_person_IDReadModeTcpReadHead=Głowica odczytu TCP
pers_person_physicalNo=Fizyczny numer dowodu osobistego
pers_person_physicalNoToCardNo=Fizyczny numer dowodu osobistego oznaczony jako numer karty
pers_person_ReadIDCard=Odczytywanie dowodu osobistego
pers_person_templateBioTypeNumber=Numer typu szablonu biometrycznego
pers_person_templateValidType=Uzasadnienie cech biologicznych 
pers_person_templateBioType=Typ szablonu biologicznego 
pers_person_templateVersion=Wersja szablonu biologicznego
pers_person_template=Szczegół cech biologicznych
pers_person_templateNo=Numer cech biologicznych
pers_person_templateNoIndex=Odpowiedny indeks do cech biologicznych
pers_person_templateDataUpdate=Należy aktualizować dane w przypadku, gdy szablon biologiczny istnieje:
pers_person_templateValidTypeNoNull=Uzasadnienie cech biologicznych nie może być puste!
pers_person_templateBioTypeNoNull=Numer pracownika: {0}, typ cech biologicznych nie może być pusty!
pers_person_templateVersionNoNull=Numer pracownika: {0}, wersja typu cech biologicznych nie może być pusta!
pers_person_templateNoNull=Numer pracownika: {0}, treść cech biologicznych nie może być pusta!
pers_person_templateNoNoNull=Numer pracownika: {0}, numer cech biologicznych nie może być pusty!
pers_person_templateNoIndexNoNull=Numer pracownika: {0}, indeks odpowiedny cech biologicznych nie może być pusty!
pers_person_templateError=Numer pracownika: {0}, błąd cech biologicznych!
pers_person_bioDuress=Czy jest przymuszony
pers_person_universal=Uniwersalny 
pers_person_voice=Odcisk wokalny
pers_person_iris=Tęczówka oka
pers_person_retina=Siatkówka oka
pers_person_palmPrints=Odbitka dłoni
pers_person_metacarpalVein=Żyła dłoniowa
pers_person_visibleFace=Twarzy widoczne
pers_person_pinError=Nie istnieje personel z numerem {0}!
pers_person_pinException=Anomalia numeru personelu
pers_person_pinAutoIncrement=Automatyczne stopniowe zwiększanie numerów personelu
pers_person_resetSelfPwd=Zresetuj hasło do samodzielnego logowania
pers_person_picMaxSize=Rozdzielczość obrazu jest zbyt wysoka i zaleca się, aby była ona niższa niż {0}.
pers_person_picMinSize=Rozdzielczość obrazu jest zbyt niska i zaleca się, aby była ona wyższa niż {0}.
pers_person_cropFaceShow=Przyglądanie zdjęcia dopasowania
pers_person_faceNoFound=Nie rozpoznano żadnej twarzy.
pers_person_biometrics=Typ rozpoznania biologicznego
pers_person_photo=Zdjęcie personelu
pers_person_visibleFaceTemplate=Widoczny szablon twarzy
pers_person_infraredFaceTemplate=Statyczny szablon twarzy na podczerwień
pers_person_delBioTemplate=Usuń dane biometryczne
pers_person_delBioTemplateSelect=Wybierz szablon biologiczny do usunięcia!
pers_person_infraredFace=Statyczna twarz w podczerwieni
pers_person_notCard=Nie zawiera karty
pers_person_notRegFinger=Nie zawiera odcisku palca
pers_person_notMetacarpalVein=Nie zawiera żyły dłoni
pers_person_notRegVein=Nie zawiera żył palców
pers_person_notIris=Z wyłączeniem tęczówki
pers_person_notInfraredFaceTemplate=Nie zawiera szablonu twarzy bliskiej podczerwieni
pers_person_notVisibleFaceTemplate=Nie zawiera szablonu widocznej jasnej twarzy
pers_person_notVisibleFacePhoto=Nie obejmuje widocznych zdjęć twarzy
pers_person_visibleFacePhoto=Widoczne zdjęcie twarzy
pers_person_change=Dostosowanie personelu
pers_person_cropFaceUsePhoto=Czy chcesz wyświetlić zdjęcie porównawcze jako awatar?
pers_person_photoUseCropFace=Czy chcesz użyć awatara do wygenerowania zdjęcia porównawczego?
pers_person_selectCamera=Wybierz kamerę rejestrującą
pers_person_delCropFaceMsg=Nie ma żadnych porównawczych zdjęć do usunięcia!
pers_person_disabledNotOp=Osoba została niepełnosprawna i nie może operować!
pers_person_visiblePalm=Dłoń światła widocznego
pers_person_notVisiblePalm=Nie obejmuje dłoni światła widzialnego
pers_person_selectDisabledNotOp=W wybranym personelu są osoby niepełnosprawne, niezdolne do obsługi!
pers_person_photoUseCropFaceAndTempalte=Chcesz używać awatarów do generowania zdjęć porównawczych i szablonów twarzy?
pers_person_photoUseTempalte=Czy chcesz używać zdjęć do generowania szablonów twarzy?
pers_person_createCropFace=Generuj zdjęcia porównawcze
pers_person_createFaceTempalte=Generuj szablony twarzy
pers_person_faceTempalte=Szablony twarzy
pers_person_extractFaceTemplate=Wyodrębnianie szablonów twarzy
pers_person_createSuccess=Pomyślnie wygenerowane
pers_person_createFail=Generowanie nie powiodło się
pers_person_serverConnectWarn=Adres serwera, nazwa użytkownika i hasło nie mogą być puste!
pers_person_serverOffline=Serwer ekstrakcji szablonów twarzy offline
pers_person_faceTemplateError1=Wykrywanie twarzy nie powiodło się
pers_person_faceTemplateError2=Okluzja twarzy
pers_person_faceTemplateError3=Niewystarczająca jasność
pers_person_faceTemplateError4=Kąt twarzy zbyt duży
pers_person_faceTemplateError5=Wykrywanie na żywo nie powiodło się
pers_person_faceTemplateError6=Niepowodzenie ekstrakcji szablonu twarzy
pers_person_cropFaceNoExist=Zdjęcie porównawcze nie istnieje
pers_person_disableFaceTemplate=Funkcja ekstrakcji szablonu twarzy nie jest włączona, nie można wyodrębnić szablonu!
pers_person_cropFacePhoto=Zdjęcie porównywania twarzy
pers_person_vislightPalmPhoto=Zdjęcie porównywania dłoni
pers_person_serverOfflineWarn=Serwer ekstrakcji szablonów twarzy jest offline i nie może włączyć tej funkcji!
pers_person_serverConnectInfo=Proszę sprawdzić, czy serwer ekstrakcji szablonów twarzy jest online?
pers_person_notModified=Numer telefonu komórkowego nie może być zmodyfikowany.
pers_person_syncAcms=Synchronizuj pracowników z ACMS
pers_person_startUpdate=Rozpocznij aktualizację informacji o PERSONELU.
pers_person_updateFailed=Aktualizacja informacji o PERSONELU nie powiodła się!
#Utworzenie karty sterownika
pers_person_readCard=Utworzenie karty sterownika
pers_person_stopRead=Zatrzymanie utworzenia karty
pers_person_chooseDoor=Należy wybrać bramę
pers_person_readCarding=Wystawiam kartę, spróbuj ponownie później!
#Zdjęcie przechwytywania
pers_capture_catchPhoto=Zdjęcie przechwytywania
pers_capture_preview=Podglądanie
#Dział
pers_dept_entity=Dział
pers_dept_deptNo=Numer działu
pers_dept_deptName=Nazwa działu
pers_dept_parentDeptNo=Numer działu nadrzędnego
pers_dept_parentDeptName=Nazwa działu nadrzędnego
pers_dept_parentDept=Dział nadrzędny
pers_dept_note=w przypadku, gdy nowododawany dział nie pojawił się na liście działów, należy skontaktować się z administratorem w sprawie autoryzacji działu w edytowaniu użytkownika!
pers_dept_exit=Istnieje
pers_dept_auth=Powiązany użytkownik w systemie
pers_dept_parentMenuMsg=Dział nadrzędny nie może byś działem własnym albo jego działem podrzędnym
pers_dept_initDept=Nazwa działu
pers_dept_deptMarket=Dział marketingowy
pers_dept_deptRD=Dział badania i rozwoju
pers_dept_deptFinancial=Dział finansowy
pers_dept_nameNoSpace=Nazwa działów nie może zaczynać i kończyć się spacją!
pers_dept_nameExist=Nazwa danego działu już istnieje
pers_dept_changeLevel=Czy przełączyć się na uprawienie danego departamentu
pers_dept_noSpecialChar=Numery działów nie mogą zawierać znaków specjalnych!
pers_dept_NameNoSpecialChar=Nazwy działów nie mogą zawierać znaków specjalnych!
pers_dept_noModifiedParent=Wydział nadrzędny nie może być modyfikowany!
#Stanowisko
pers_position_entity=Stanowisko
pers_position_code=Numer stanowiska
pers_position_name=Nazwa stanowiska
pers_position_notExist=Stanowisko nie istnieje!
pers_position_sortNo=Numer porządkowania
pers_position_parentName=Stanowisko nadrzędne
pers_position_parentCode=Numer stanowiska nadrzędnego
pers_position_batchToPosition=Stanowisko po transferze
pers_position_nameExist=Nazwa stanowiska już istnieje
pers_position_change=Zmiana stanowiska
pers_position_parentMenuMsg=Stanowisko nadrzędne nie może być stanowiskiem własnym lub jego podrzędnym
pers_position_nameNoSpace=Nazwa stanowiska nie zaczyna się lub kończy się spacją!
pers_position_existSub={0}: Istnieje pozycja nadrzędna, usunięcie stanowiska jest niedozwolone
pers_position_existPerson={0}: istnieje personel, usunięcie stanowiska jest niedozwolone
pers_position_importTemplate=Szablon importu zadań
pers_position_downloadTemplate=Pobierz szablon importu
pers_position_codeNotEmpty=Numer pozycji nie może być pusty
pers_position_nameNotEmpty=Nazwa pozycji nie może być pusta
pers_position_nameNoSpecialChar=Nazwa pozycji {0} nie może zawierać znaków specjalnych!
pers_position_noSpecialChar=Numer pozycji {0} nie może być znakiem specjalnym!
pers_position_codeLength=Numer pozycji {0} ma długość ponad 30 cyfr
pers_position_nameLength=Dane, których nazwa pozycji {0} jest dłuższa niż {1}
pers_position_codeExist=Identyfikator zadania {0} już istnieje
#Dokument
pers_cert_type=Typ dowodu osobistego
pers_cert_number=Numer dokumentu
pers_cert_name=Nazwa dokumentu
pers_cert_numberExist=Numer dokument już istnieje
#Eksportowanie
pers_export_allPersPerson=Cały personel
pers_export_curPersPerson=Aktualny personel
pers_export_template=Eksportowanie szablonu
pers_export_personInfo=Eksportowanie informacji o personelu
pers_export_personInfoTemplate=Eksportowanie szablonu informacji o personelu
pers_export_personBioTemplate=Eksportowanie danych szablonu informacji biologicznej o personelu
pers_export_basicInfo=Ustawienia podstawowej informacji o personelu
pers_export_customAttr=Ustawienia atrybutu dostosowywania
pers_export_templateComment=Nazwa pola, czy jest głównym kluczym, czy jest wyłączny, czy wartość pusta jest dopuszczalna ({0},{1},{2},{3})
pers_export_templateFileName=Szablon informacji o personelu
pers_export_bioTemplateFileName=Dane szablonu informacji biologicznej o personelu
pers_export_deptInfo=Eksportowanie informacji o dziale
pers_export_deptTemplate=Eksportowanie formy działu
pers_export_deptTemplateFileName=Forma działu
pers_export_personPhoto=Eksportowanie zdjęcia personelu
pers_export_allPhotos=Wszystkie zdjęcia (należy wybrać całego personelu)
pers_export_selectPhotoToExport=Należy wybrać numer personelu rozpoczęcia i zakończenia eksportowania ich zdjęć
pers_export_fromId=Numer personelu od
pers_export_toId=Do
pers_export_certNumberComment=Typ certyfikatu jest wymagany po wpisaniu numeru certyfikatu
pers_export_templateCommentName=Nazwa pola:({0})
pers_export_dataExist=Upewnij się, że zaimportowane dane już istnieją w systemie
pers_export_cardNoTip=Wiele numerów kart oddzielonych przez &
pers_carNumber_importTip=Numer tablicy rejestracyjnej (należy oddzielić przycinkiem „&” w przypadku wielu tablic rejestracyjnych)
#Importowanie
pers_import_certNumberExist=Numer dokumentu {0} już istnieje
pers_import_complete=Zakończenie
pers_import_password=Hasło personelu 
pers_import_fail=Awaria rzędu {0}: {1}
pers_import_overData=Aktualna liczba personeli importowanych jest {0}, która przekracza limit o 30000, należy importować w partiach! 
pers_import_pinTooLong=Długość numeru personelu {0} przekracza dopuszczalną długością!
pers_import_pinExist=Numer personelu {0} już istnieje!
pers_import_pinIsRepeat=Numer personelu {0} już jest powtarzalny!
pers_import_pinError=Błąd formatu numeru personelu {0}!
pers_import_pinSupportNumber=Numer personelu przysługuje tylko liczby! Numer personelu jest: {0}
pers_import_pinNotSupportNonAlphabetic=Numer personelu przysługuje tylko kombinację liczby i litery. Numer personelu jest: {0}
pers_import_pinNotNull=Numer personelu nie może być czystymi zerami!
pers_import_pinStartWithZero=Numer personelu nie zaczyna się od zera!
pers_import_cardNoNotSupportLetter=Numer karty nie przysługuje litery!
pers_import_cardNoNotNull=Numer karty nie może być czystymi zerami!
pers_import_cardNoStartWithZero=Numer karty nie zaczyna się od zera!
pers_import_cardTooLong=Długość numeru karty {0} przekracza dopuszczalną długością!
pers_import_cardExist=Numer karty {0} już istnieje!
pers_import_personPwdOnlyNumber=Hasło personelu przysługuje tylko liczbę.
pers_import_personPwdTooLong=Długość hasła personelu {0} przekracza dopuszczalną długością!
pers_import_personDuressPwd=Hasło personelu {0} jest powtarzalne!
pers_import_emailTooLong=Długość znaków adresu mailowego {0} przekracza dopuszczalną długość!
pers_import_nameTooLong=Długość nazwa personelu {0} przekracza dopuszczalną długością!
pers_import_genderError=Błąd formatu płci!
pers_import_personDateError=Błąd formatu daty zatrudnienia!
pers_import_createTimeError=Błąd formatu godziny otworzenia!
pers_import_phoneError=Błąd formatu numeru telefonu!
pers_import_phoneTooLong=Numer telefonu ma więcej niż 20 cyfr!
pers_import_emailError=Błąd formatu adresu mailowego!
pers_import_birthdayError=Błąd formatu daty urodzenia!
pers_import_nameError=Imię I nazwisko nie mogą zawierać specjalnych znaków! Numer personelu jest: {0}
pers_import_firstnameError=Imię I nazwisko nie mogą zawierać przecinku!
pers_import_firstnameNotNull=Nazwisko nie może być puste! 
pers_import_dataCheck=Sprawdzenie danych zostało zakończone!
pers_import_dataSaveFail=Awaria importowania danych!
pers_import_allSucceed=Skuteczne importowanie danych!
pers_import_result=Skutecznych:  {0} , nieskutecznych: {1} 
pers_import_result2=Wynik importowania
pers_import_result3=Skutecznych: {0} , aktualizowanych: {1} , nieskutecznych: {2} sztuk.
pers_import_notSupportFormat=Ten format nie jest obecnie obsługiwany!
pers_import_selectCorrectFile=Należy wybrać plik z prawidłowym formatem!
pers_import_fileFormat=Format pliku
pers_import_targetFile=Plik celowy
pers_import_startRow=Liczba rzędu od nagłówka
pers_import_startRowNote=Pierwsza linia formatu danych to nazwa tabeli, druga linia to nagłówek tabeli, a trzecia linia po niej to dane do importowania, należy sprawdzić plik przed importem.
pers_import_delimiter=Delimiter
pers_import_importingDataFields=Pola bazy danych 
pers_import_dataSourceFields=Importowania pól raportów 
pers_import_total=W sumie
pers_import_dataUpdate=Należy aktualizować danych w przypadku istnienia numeru personelu:
pers_import_dataIsNull=Brak danych w pliku!
pers_import_deptNotExist=Dział nie istnieje!
pers_import_deptIsNotNull=Nazwa dział nie może być pusta!
pers_import_pinNotEmpty=Numer personelu nie może być pusty!
pers_import_nameNotEmpty=Nazwa personelu nie może być pusta!
pers_import_siteCodeOnlyLetterNum=Błąd formatu numeru kierunkowego. 
pers_import_cardNoFormatErrors=Błąd formatu karty {0}.
pers_import_cardsNotSupport=aktualny systemu nie przysługuje trybu wielokartowego 
pers_import_personInfo=Importowanie informacji o personelu
pers_import_commentFormat=Błąd formatu adnotacji!
pers_import_noComment=Brak adnotacji do danych z rzędu {0} i kolumny {1} 
pers_import_fieldRepeat=Dane z rzędu {0} i kolumny {1}, Powtarzalna nazwa pola adnotacji: {2}
pers_import_primaryKey=Musi istnieć co najmniej jedno pole, które jest kluczem podstawowym
pers_import_templateIsRepeat=Numer personelu: dane szablonu biologicznego {0} zostały powtarzalne! 
pers_import_biologicalTemplate=Importowanie danych szablonu biologicznego personelu
pers_import_uploadFileSuccess=Plik został przesłany pomyślnie, a dane z pliku zostały przeanalizowane, należy poczekać
pers_import_resolutionComplete=Dane zostały przeanalizowane, rozpocznie aktualizację bazy danych.
pers_import_mustField=Plik importowy musi zawierać kolumnę {0}
pers_import_bioTemplateSuccess=Zaimportowanie danych szablonów biologicznych, które muszą przejść do każdego modułu biznesowego w celu ręcznej synchronizacji danych przed ich wysłaniem do urządzenia.
pers_import_personPhoto=Importowanie zdjęć personelu
pers_import_opera_log=Zapis obsługiwania
pers_import_error_log=Dziennik błędów 
pers_import_uploadFileSize=Należy przesłać plik zawierający nie więcej niż {0}!
pers_import_uploadFileSizeLimit=Należy przesłać plik o rozmiarze nie większym niż 500M w jednym imporcie!
pers_import_type=Metoda importowania
pers_import_photoType=Zdjęcie
pers_import_archiveType=Plik RAR
pers_import_startUpload=Rozpoczęcie nadania
pers_import_addMore=Dodanie więcej 
pers_import_photoQuality=Jakość zdjęcia 
pers_import_original=Oryginalna 
pers_import_adaptive=Adaptacja
pers_import_adaptiveSize=(rozmiar 480*640)
pers_import_totalNumber=Suma
pers_import_uploadTip=(Nie należy usunąć zdjęcie podczas jego nadania)
pers_import_addPhotoTip=Należy wyczyścić wcześniej przetworzone zdjęcia lub przesłać nowe!
pers_import_selectPhotoTip=Należy wybrać zdjęcie do przesyłania! 
pers_import_uploadResult=Wynik przesyłania obrazu  
pers_import_pleaseSelectPhoto=Należy wybrać zdjęcie
pers_import_multipleSelectTip=Nacisnąć klawisz Ctrl, aby dokonać wielokrotnego wyboru
pers_import_replacePhotoTip=Wybrane zdjęcie już istnieją w zdjęciach czekających na przesłanie, czy na pewno je zastąpić?
pers_import_photoNamePinNotCorrespond=Przyczyna: nazwa zdjęcia nie odpowiada numerom personelu 
pers_import_photoFormatRequirement=Należy wpisać nazwę zdjęcia używając numeru osoby. Prawidłowy format zdjęcia jest JPG/PNG. Należy upewnić się, że nazwa zdjęcia nie zawiera znaków specjalnych.
pers_import_filterTip=Niektóre z wybranych zdjęć nie mogły być podglądane z następujących możliwych przyczyn.
pers_import_photoContainSpecialCharacters=Nazwa zdjęcie zawiera specjalne znaki
pers_import_photoFormatError=Nieprawidłowy format zdjęcia
pers_import_photoSelectNumber=Nie należy wybierać więcej niż 3.000 zdjęć w jednym imporcie!
pers_import_photoSelectNumberLimit=Nie należy wybierać więcej niż {0} zdjęć!
pers_import_fileMaxSize=Obraz jest zbyt duży, należy przesłać plik obrazu mniejszy niż 5M.
pers_import_notUploadPhotoNumber=Ilość zdjęć czekających do przesyłania nie przekracza 3.000!
pers_import_zipFileNotPhoto=W paczce nie znajdują się żadne zdjęcia osób, należy wybrać ponownie i potem importować!
pers_import_personPlateRepeat=Powtarzalna tablica rejestracyjna {0} personelu!
pers_import_filePlateRepeat=Powtarzalna tablica rejestracyjna {0} w pliku zewnętrznym!
pers_import_personPlateFormat=Nieprawidłowy format tablicy rejestracyjnej {0}! 
pers_import_personPlateMax=Ilość tablicy rejestracyjnej personelu przekracza maksymalną liczbę 6! 
pers_import_cropFaceFail=Awaria wygenerowania zdjęcia do dopasowania! 
pers_import_pinLeaved=Numer personelu: {0} już zrezygnował.
pers_import_exceedLicense=Ilość personeli przekracza limit licencji, importowanie nie jest dozwolone! 
pers_import_bioTemplateNotNull=Numer personelu, typ biologiczny, id biologiczny, indeks biologiczny, treść i wersja nie mogą być puste! 
pers_import_certTypeNotNull=Numer personelu jest: {0} Typ dokumentu nie może być pusty
pers_import_certTypeNotExist=Numer personelu to: {0} Typ dokumentu nie istnieje
pers_import_certNumNotNull=Numer personelu to: {0} Numer certyfikatu nie może być pusty
pers_import_certNumberTooLong=Rząd {0}: numer dokumentu {1} przekracza dopuszczalną długość! 
pers_import_idNumberErrors=Rząd {0}: błąd formatu numer dowodu osobistego {1}!
pers_import_emailErrors=Rząd {0}: błąd formatu adresu mailowego {1}!
pers_import_emailIsExist=Adres mailowy {0} już istnieje!
pers_import_emailIsRepeat=Rząd {0}:  Powtarzalny adres mailowy {1} w pliku zewnętrznym!
pers_import_fileMobilePhoneRepeat=Wiersz {0}: Wewnętrzny numer telefonu komórkowego pliku {1} został powtórzony!
pers_import_mobilePhoneErrors=Błąd formatu numeru telefonu komórkowego!
pers_import_hireDateError=Format daty wpisu jest nieprawidłowy!
pers_import_selectPhotoType=Proszę wybrać typ importowanego zdjęcia!
pers_import_hireDateLaterCurrent=Data wynajmu nie może być późniejsza niż aktualna data!
pers_import_buildingNotExist=Budynek nie istnieje!
pers_import_unitNotExist=Jednostka nie istnieje!
pers_import_vdbInfoFail=Brak informacji o jednostce o nazwie {1} w budynku {0}!
pers_import_vdbBuildingFail=Informacje o budynku jednostki {0} nie mogą być puste!
pers_import_vdbRoomNoFail=Numer pokoju musi być liczbą większą niż 0!
#Zrezygnowanie personelu
pers_person_leave=Zrezygnowanie
pers_dimission_date=Data zrezygnowania
pers_dimission_type=Rodzaj zrezygnowania
pers_dimission_reason=Przyczyna zrezygnowania
pers_dimission_volutary=Zrezygnowany dobrowolnie
pers_dimission_dismiss=Zwolniony 
pers_dimission_resignat=Zrezygnowany
pers_dimission_shiftJob=Transferowany 
pers_dimission_leave=Zatrzymania stanowiska bez wynagrodzenia
pers_dimission_recovery=Przywrócenie stanowiska
pers_dimission_sureToRecovery=Czy na pewno wykonać przywrócenie stanowiska?
pers_dimission_backCard=Czy oddać kartę? 
pers_dimission_isForbidAction=Czy wyłączyć dostęp? 
pers_dimission_writeInfomation=Wpisanie informacji o rezygnacji 
pers_dimission_pinRetain=Zatrzymanie numeru dla personelu zrezygnowanych 
pers_dimission_downloadTemplate=Pobierz szablon importu zwolnień
pers_dimission_import=Import Dismissions
pers_dimission_importTemplate=Szablon importu zwolnień
pers_dimission_date_noNull=Data zwolnienia nie może być pusta
pers_dimission_leaveType_noExist=Typ odrzucenia nie istnieje
pers_dimission_dateFormat=Wymagane pole, format czasu to rrrr-MM-dd, na przykład: 2020-07-22
pers_dimission_leaveType=Wymagane pole, takie jak: Zrezygnowany dobrowolnie, Zwolniony, Zrezygnowany, Transferowany
pers_dimission_forbidden=Dodanie do listy czarnej
pers_dimission_leaveType_noNull=Typ urlopu nie może być pusty!
pers_dimission_person_noExist=Osoba zwolnienia nie istnieje!
pers_dimission_date_error=Data zwolnienia nie wypełniona lub nieprawidłowa formuła
#Personel tymczasowy
pers_tempPerson_audit=Sprawdzenie
pers_tempPerson_view=Przeglądanie
pers_tempPerson_waitReview=Oczekiwanie na recenzję administratora
# Nazywanie się itd. card/issueCard/lossCard/revertCard/
#Karta
pers_card_cardNo=Numer karty
pers_card_state=Stan karty
pers_card_effect=Ważna godzina
pers_card_disabled=Nieuzasadniona
pers_card_past=Wygasły
pers_card_back=Zwrotu karty
pers_card_change=Zmiana karty
pers_card_note=Numer karty już istnieje
pers_card_numTooBig=Numer karty przekracza
pers_issueCard_entity=Wytwarzanie karty
pers_issueCard_operator=Operator
pers_issueCard_operate=Typ operacji
pers_issueCard_note=Operacja jest przeznaczona dla personli, które zarejestrowali swoje dane osobowe, ale nie numer karty!
pers_issueCard_date=Data wytwarzania karty
pers_issueCard_changeTime=Czas modyfikacji
pers_issueCard_cardValidate=Numer karty nie może zawierać spacji
pers_issueCard_cardEmptyNote=Numer karty nie może być pusty
pers_issueCard_cardHasBeenIssued=Aktualna karta już została wytwarzana!
pers_issueCard_noCardPerson=Personel bez wytwarzania karty
pers_issueCard_waitPerson=Personel do wytwarzania karty tym razem
pers_issueCard_mc5000=Wydawanie karty MC5000
pers_batchIssCard_entity=Wytwarzanie karty w partii
pers_batchIssCard_startPersNo=Numer personelu rozpoczętego
pers_batchIssCard_endPersNo=Numer personelu zakończonego
pers_batchIssCard_issCardNum=Ilość kart wytwarzanych 
pers_batchIssCard_notIssCardNum=Ilość personeli do wytwarzania karty
pers_batchIssCard_generateList=Wygenerowanie listy personelu
pers_batchIssCard_startRead=Rozpoczęcie odczytu
pers_batchIssCard_swipCard=Lokalizacja do wytwarzania karty
pers_batchIssCard_sendCard=Głowica odczytu
pers_batchIssCard_dispenCardIss=Sprzęt do wytwarzania karty
pers_batchIssCard_usbEncoder=Sprzęt do wytwarzania karty desfire
pers_batchIssCard_note=Numery personelu obsługują tylko wpisywanie numerów i pokazują tylko personel, któremu nie wydano karty (do 300)!
pers_batchIssCard_startPinEmpty=Numer personelu rozpoczętego nie może być pusty!
pers_batchIssCard_endPinEmpty=Numer personelu zakończonego nie może być pusty!
pers_batchIssCard_startPinLargeThanEndPin=Numer personelu rozpoczętego nie może być większy niż numer personelu zakończonego!
pers_batchIssCard_numberParagraphNoPerson=W tym przedziale numerowania nie istnieje personel do wytwarzania karty!
pers_batchIssCard_inputCardNum=Wprowadzenie numeru karty
pers_batchIssCard_cardLetter=Numer karty nie przysługuje litery!
pers_batchIssCard_cardNoTooLong=Numer karty jest zbyt długi
pers_batchIssCard_issueWay=Metoda wytwarzania karty
pers_batchIssCard_noPersonList=Lista personelu nie została wygenerowana
pers_batchIssCard_startReadCard=Rozpoczęcie odczytu karty
pers_batchIssCard_swipePosition=Miejsce do odczytu karty
pers_batchIssCard_chooseSwipePosition=Należy wybrać miejsce do odczytu karty
pers_batchIssCard_readCardTip=w przypadku wytwarzania karty głowicą odczytu, tylko odczytuje karty niezarejestrowane w sprzęcie.
pers_batchIssCard_notIssCardNo=Ilość karty niewytwarzanych
pers_batchIssCard_totalNumOfCards=Suma karty
pers_batchIssCard_acms=Wydawanie karty ACMS
pers_lossCard_entity=Zgłoszenie zagubienia 
pers_lossCard_lost=Karta już zgłoszona zagubienie, obsługiwanie powtarzalne jest niedozwolone!
pers_losscard_note2=Po zapisaniu do karty zarządzania, należy przesunąć kartę na głowicy czytnika urządzenia kontroli drabiny, zanim operacja jej zgłoszenia zagubienia stanie się skuteczna.
pers_revertCard_entity=Anulowanie zgubionych kart
pers_revertCard_setReport=Należy najpierw zgłaszać zagubienie karty!
pers_revertcard_note2=Po zapisaniu do karty zarządzania, należy przesunąć kartę na głowicy czytnika urządzenia kontroli drabiny, zanim operacja jej anulowania stanie się skuteczna.
pers_issueCard_success=Skuteczne wytwarzanie karty!
pers_issueCard_error=Anomalia wytwarzania karty!
pers_cardData_error=Anomalia formatu danych karty odczytowej!
pers_analysis_error=Anomalia przeanalizowania danych karty!
pers_cardOperation_error=Anomalia obsługiwania karty!
pers_cardPacket_error=Anomalia operacji enkapsulacji poleceń karty!
pers_card_write=Zapisanie karty
pers_card_init=Inicjonowanie karty 
pers_card_loss=Zgłoszenie zagubienia karty
pers_card_revert=Anulowanie zgubionych kart
pers_card_writeMgr=Zapisanie karty zarządzania
pers_initCard_tip=Karta stanie się pustą kartą po inicjalizacji!
pers_initCard_prepare=Przygotowanie do inicjalizacji karty...
pers_initCard_process=W trakcie inicjalizacja karty...
pers_initCard_success=Skuteczna inicjalizacja karty
pers_mgrCard_prepare=Przygotowanie do zapisania karty zarządzania...
pers_mgrCard_process=W trakcie zapisania karty zarządzania...
pers_mgrCard_success=Skuteczne zapisanie karty zarządzania
pers_userCard_prepare=Przygotowanie do zapisania karty użytkownika…
pers_userCard_process=W trakcie zapisania karty użytkownika…
pers_userCard_success=Skuteczne zapisanie karty użytkownika
pers_userCard_tip=Należy ustawić czas rozpoczęcia i zakończenia na stronie edycji personelu przed napisaniem karty.
pers_userCard_tip2=Dane uprawień są puste, zapisanie karty jest niedozwolone.
pers_userCard_tip3=Uprawnienia są związane z więcej niż dwoma urządzeniami i dane są tracone.
pers_writeCard_tip=Należy upewnić się, że karta jest podłączona do sprzętu do wytwarzania kart lub zainstalowany jest jego sterownik, a karta jest umieszczona na sprzęcie 
pers_writeMgrCard_tip=Liczba kart zgłoszonych i anulowanych zagubienia nie przekracza 18
pers_writeMgrCard_tip2=Aktualna liczba kart zgłoszonych i anulowanych zagubienia:
pers_card_writeToMgr=Zapisanie karty zarządzania
pers_card_hex=Wyświetlenie formatu karty
pers_card_decimal=Dziesiętny 
pers_card_Hexadecimal=Szesnastkowy
pers_card_IssuedCommandFail=Awaria wysyłania zleceń:
pers_card_multiCard=Rejestracja wielokartowa
pers_card_deputyCard=Karta dodatkowa
pers_card_deputyCardValid=Należy najpierw wpisać numer karty głównej!
pers_card_writePinFormat=System nie obsługuje zapisu alfanumerycznych numerów personeli na karcie, muszą być to liczby!
pers_card_notMoreThanSixteen=Ilość kart dodatkowych nie przekracza 16.
pers_card_notDelAll=Nie można usunąć wszystkich kart dodatkowych.
pers_card_maxCard=Długość numeru karty nie przekracza {0}!
pers_card_posUseCardNo=Główna karta danego personelu jest używana przez moduł wydatkowy, należy przejść do tego modułu w celu wykonania zwrotu karty!
pers_card_delFirst=Prosimy usunąć numer wydanej karty przed wydaniem karty!
pers_card_disablePersonWarn=Wybrany numer karty zawiera personel niepełnosprawny i nie może być obsługiwany!
#Format Wiegand
#Format Wiegand 
pers_wiegandFmt_siteCode=Numer kierunkowy
pers_wiegandFmt_wiegandMode=Tryb
pers_wiegandFmt_wiegandModeOne=Tryb 1
pers_wiegandFmt_wiegandModeTwo=Tryb 2
pers_wiegandFmt_isDefaultFmt=Automatyczne dopasowanie
pers_wgFmt_entity=Format karty Wiegand
pers_wgFmt_in=Format wejścia Wiegand
pers_wgFmt_out=Format wyjścia Wiegand
pers_wgFmt_inType=Typ wejścia Wiegand
pers_wgFmt_outType=Typ wyjścia Wiegand
pers_wgFmt_wg=Wiegand
pers_wgFmt_totalBit=łączna liczba cyfr
pers_wgFmt_oddPch=Weryfikacja parytetu nieparzystego(o)
pers_wgFmt_evenPck=Weryfikacja parytetu parzystego(e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=Kod sprzętu(f)
pers_wgFmt_siteCode=Numer kierunkowy(s)
pers_wgFmt_manufactoryCode=Kod producenta(m)
pers_wgFmt_firstParity=Pierwsza cyfra do weryfikacji parytetu
pers_wgFmt_secondParity=Druga cyfra do weryfikacji parytetu
pers_wgFmt_cardFmt=Format weryfikacji karty
pers_wgFmt_parityFmt=Format paragrafu parytetowego
pers_wgFmt_startBit=Cyfra rozpoczęcia
pers_wgFmt_test=Przetestowanie formatu Wiegand
pers_wgFmt_error=Błąd formatu Wiegand!
pers_wgFmt_verify1=Liczba cyfr nie przekracza 80!
pers_wgFmt_verify2=Długość formatu weryfikacji karty musi być równa do liczby cyfr!
pers_wgFmt_verify3=Długość formatu weryfikacji parytetu musi być równa do liczby cyfr!
pers_wgFmt_verify4=Nie można usunąć danych zainicjowanych!
pers_wgFmt_verify5=Format odczytu karty jest w użyciu, jego usunięcie jest niedozwolone!
pers_wgFmt_verify6=Pierwsza cyfra do weryfikacji parytetu nie może być większa niż liczba cyfr!
pers_wgFmt_verify7=Druga cyfra do weryfikacji parytetu nie może być większa niż liczba cyfr!
pers_wgFmt_verify8=Nieprawidłowy format cyfry rozpoczęcia i maksymalnej długości!
pers_wgFmt_verify9=Format weryfikacji przekracza suma cyfr!
pers_wgFmt_verify10=Nie można przekraczać funkcji sprawdzania cyfr karty!
pers_wgFmt_verify11=Numer kierunkowy przekracza okres ustawień!
pers_wgFmt_verify=Weryfikowanie
pers_wgFmt_unverify=Nieweryfikowanie
pers_wgFmt_atLeastDefaultFmt=Należy zatrzymać co najmniej jeden format karty do dopasowania automatycznego!
pers_wgFmt_defaultFmtError1=Formaty kart zawierające inne formaty z tymi samymi cyframi nie mogą być ustawione na automatyczne dopasowanie!
pers_wgFmt_defaultFmtError2=Awaria obsługiwania z powodu istnienia tego samego formatu karty do dopasowania automatycznego!
pers_wgFmt_cardFormats=Format karty
pers_wgFmt_cardFormatTesting=Przetestowanie formaty karty
pers_wgFmt_checkIsUsed=Format karty jest używany w {0} i nie można go usunąć!
pers_wgFmt_supportDigitsNumber=Proszę wprowadzić liczbę cyfr obsługiwanych przez urządzenie
#Sterownik do wyboru personelu
pers_widget_selectPerson=Należy wybrać personelu
pers_widget_searchType1=Wyszukiwanie z warunkami
pers_widget_searchType2=Dział
pers_widget_deptHint=Uwaga: Importowanie wszystkich personelu w wybranych działach
pers_widget_noPerson=Nikt nie został wybrany.
pers_widget_noDept=Należy wybrać dział
pers_widget_noDeptPerson=Brak personelu w ramach wybranego dział, należy wybrać ponownie!
#Atrybut personelu
pers_person_carPlate=Numer tablicy rejestracyjnej
pers_person_socialSecurity=Numer ubezpieczenia społecznego
pers_person_msg4=Maksymalna długość nie przekracza 20 liczb! 
pers_person_msg5=Nazwa imienia i nazwiska nie przekracza 50 znaków!
pers_person_type=Kategoria personelu 
pers_person_reseCode=Hasło samo obsługiwane 
pers_person_IsSendMail=Powiadomienie mailem 
pers_person_inactive=Wstrzymany
pers_person_active=Ważna godzina
pers_person_employee=Personel
pers_person_isSendMailMsg=Należy obowiązkowo wprowadzić adres mailowy do wiadomości zdarzenia 
pers_person_createTime=Czas tworzenia
pers_person_pinFirstValid=Numer personelu nie zaczyna się od liczby 8 lub 9.
pers_person_attrValueValid=Wartość pola nie może być powtarzalna
pers_person_attrValueDelimiterValid=Należy umieścić cudzoziemiec w środku
pers_person_isSendSMS=Powiadomienie SMS-em
pers_person_building=Nazwa budynku
pers_person_unitName=Nazwa jednostki
pers_person_roomNo=Numer pokoju
#Atrybut dynamiczny
pers_attr_emp_type=Typ personelu
pers_attr_street=Ulica
pers_attr_nation=Naród
pers_attr_office_address=Adres firmy 
pers_attr_postcode=Kod pocztowy
pers_attr_office_phone=Numer telefonu firmy
pers_attr_home_phone=Numer telefonu domowego
pers_attr_job_title=Tytuł
pers_attr_birthplace=Miejsce urodzenia
pers_attr_polit_status=Profil polityczny
pers_attr_country=Region
pers_attr_home_address=Adres domowy
pers_attr_hire_type=Rodzaj zatrudnienia
pers_attr_inContract=W terminie umowy 
pers_attr_outContract=Poza terminem umowy
#Przystosowanie atrybutu
pers_attribute_attrName=Nazwa wyświetlona
pers_attribute_attrValue=Wartość pola
pers_attribute_controlType=Typ wprowadzenia
pers_attribute_positionX=Linia
pers_attribute_positionY=Rząd
pers_attribute_showTable=Wyświetlenie na liście personelu
pers_attrDefini_deletemsg=Dany atrybut został używany, na pewno go usunąć?
pers_attrDefini_reserved=Nazwa pola zastrzeżona w systemie
pers_attrDefini_msg1=Maksymalna długość nie przekracza 30 liczb!
pers_attrDefini_msg2=Dane już istnieją w tym rzędzie i kolumnie, należy przejść do innej lokalizacji!
pers_attrDefini_attrValue_split=Należy oddzielić cudzoziemcem angielskim \' ; \'
pers_attrDefini_attrName=Nazwa atrybutu
pers_attrDefini_sql=Zdanie sql
pers_attrDefini_attrId=Numer atrybutu
pers_attrDefini_select=Lista rozwijana
pers_attrDefini_check=Wielokrotny wybór
pers_attrDefini_radio=Jedyny wybór
pers_attrDefini_text=Pole do wprowadzenia
pers_attrDefini_maxCol=Nie przekracza 2 kolumny
pers_attrDefini_maxLimit=Ilość dostosowanych atrybutów osiągnęła do maksymalnej!
pers_attrDefini_modControlType=Zmiana typu wprowadzenia spowoduje kasowanie danych aktualnych całego personelu w systemie, czy kontynuować?
#Personel zrezygnowany
pers_leavePerson_reinstated=Przywrócenie do starego stanowiska
#opExample
pers_example_newRecode=Pobranie nowych zapisów
pers_example_allRecode=Uzyskanie wszystkich zapisów
pers_custField_StatisticalType=Typ statystyki
#Zmiana parametrów personelu
pers_param_isAudit=Włączenie automatycznej weryfikacji tymczasowego personelu
pers_param_donotChangePin=Istnieje numer personelu zawierając litery, zmiana wzoru ponumerowania jest niedozwolona.
pers_param_hexChangeWarn=Już istnieje numer karty w aktualnym systemie.
pers_param_cardsChangeWarn=Już istnieje tryb wielokartowy, zmiana ustawień wielokartowych jest niedozwolone.
pers_param_maxPinLength=Długość nowo utworzonego numeru osobowego nie może być mniejsza niż długość już istniejącego numeru osobowego.
pers_param_pinBeyondDevLength=Maksymalna długość numeru personelu obsługiwanego przez sprzęt w systemie to {0}, należy wpisać liczbę całkowitą mniejszą niż {1}!
pers_param_cardBeyondDevLength=Maksymalna długość numeru karty obsługiwanego przez sprzęt w systemie to {0}, należy wpisać liczbę całkowitą mniejszą niż {1}!
pers_param_checkIsExistNoAudit=W aktualnym systemie istnieje nieweryfikowany zarejestrowany personel i nie może być modyfikowany do weryfikacji automatycznej! 
pers_param_noSupportPinLetter=W systemie znajdują się urządzenia, które nie obsługują numerów osobowych zawierających litery i nie można zmienić wzoru numeracji personelu.
pers_param_changePinLettersTip=Numerowanie personelu obsługuje automatyczne przyrosty, zmiana wzoru numerowania jest niedozwolona
pers_param_changePinIncrementTip=Numerowanie personelu obsługuje zwierania liter, zmiana wzoru numerowania jest niedozwolona
pers_param_qrCode=Kod dwuwymiarowy firmy
pers_param_employeeRegistrar=Włączenie zarejestrowania personelu na chmurze
pers_param_downloadQRCodePic=Pobranie zdjęcia kodu dwuwymiarowego
pers_param_qrCodeUrl=Kod dwuwymiarowy Url
pers_param_qrCodeUrlCreate=Auto obsługiwane rejestrowanie personelu
pers_param_qrCodeUrlHref=Adres serwera: port
pers_param_pinSetWarn=W bieżącym systemie jest już personel i nie można zmienić trybu numeru personelu.
pers_param_selfRegistration=Włącz samodzielną rejestrację
pers_param_infoProtection=Ochrona poufnych informacji osobistych
pers_param_infoProtectionWarnMsg=Po włączeniu opcji ochrony poufnych informacji osobistych, wrażliwe dane osobowe zaangażowane w ten moduł zostaną usunięte lub zasłonięte, w tym między innymi nazwiska, numery kart, numery ID, zdjęcia itp.
pers_param_templateServer=Serwer ekstrakcji szablonów twarzy
pers_param_enableFacialTemplate=Włącz ekstrakcję szablonu twarzy
pers_param_templateServerAddr=Adres serwera ekstrakcji szablonów twarzy
pers_param_templateServerWarnInfo=Gdy ekstrakcja szablonów twarzy jest włączona, gdy serwer ekstrakcji szablonów twarzy jest online i weryfikacja użytkownika zostanie przekazana, personel domyślnie wyodrębni szablony twarzy podczas porównywania zdjęć; Gdy serwer ekstrakcji szablonów twarzy jest w trybie offline, nie wyodrębniaj szablonów twarzy!
pers_param_templateServerWarnInfo1=Podczas włączania ekstrakcji szablonów twarzy należy podłączyć urządzenie obsługujące ekstrakcję szablonów twarzy!
pers_param_templateServerOffline=Serwer ekstrakcji szablonów twarzy jest offline i nie może wyodrębnić szablonów twarzy! Chcesz kontynuować?
pers_param_faceServer=Usługa porównywania zaplecza twarzy
pers_param_enableFaceVerify=Włącz porównanie zaplecza twarzy
pers_param_faceServerAddr=Adres serwisu porównywania twarzy backend
pers_param_faceServerSecret=Klucz usługi porównywania twarzy backend
#Obywatelskie
pers_person_nationality=Kraj/region
pers_nationality_angola=Angola
pers_nationality_afghanistan=Afganistan
pers_nationality_albania=Albania
pers_nationality_algeria=Algieria
pers_nationality_america=Stany Jednoczone
pers_nationality_andorra=Andora
pers_nationality_anguilla=Anguilla
pers_nationality_antAndBar=Antigua i Barbuda
pers_nationality_argentina=Argentyna
pers_nationality_armenia=Armenia
pers_nationality_ascension=Wniebowstąpienie
pers_nationality_australia=Australia
pers_nationality_austria=Austria
pers_nationality_azerbaijan=Azerbejdżan
pers_nationality_bahamas=Bahamy
pers_nationality_bahrain=Bahrajn
pers_nationality_bangladesh=Bangladesz
pers_nationality_barbados=Barbados
pers_nationality_belarus=Białoruś
pers_nationality_belgium=Belgia
pers_nationality_belize=Belizean
pers_nationality_benin=Benin
pers_nationality_bermudaIs=Bermudy
pers_nationality_bolivia=Boliwia
pers_nationality_botswana=Botswana
pers_nationality_brazil=Brazylia
pers_nationality_brunei=Brunei Darussalam
pers_nationality_bulgaria=Bułgaria
pers_nationality_burkinaFaso=Burkina Faso
pers_nationality_burma=Birma
pers_nationality_burundi=Burundi
pers_nationality_cameroon=Kamerun
pers_nationality_canada=Kanada
pers_nationality_caymanIs=Kajmany
pers_nationality_cenAfrRepub=Republika Środkowoafrykańska
pers_nationality_chad=Czadyjskie
pers_nationality_chile=Chile
pers_nationality_china=Chiny
pers_nationality_colombia=Kolumbia
pers_nationality_congo=Kongo (Brazzaville)
pers_nationality_cookIs=Wyspy Cooka
pers_nationality_costaRica=Kostaryka
pers_nationality_cuba=Kuba
pers_nationality_cyprus=Cypr
pers_nationality_czechRep=Republika Czeska
pers_nationality_denmark=Dania
pers_nationality_djibouti=Dżibuti
pers_nationality_dominicaRep=Republika Dominikańska
pers_nationality_ecuador=Ekwador
pers_nationality_egypt=Egipt
pers_nationality_eISalvador=Salwador
pers_nationality_england=WIELKA BRYTANIA
pers_nationality_estonia=Estonia
pers_nationality_ethiopia=Etiopia
pers_nationality_fiji=Fidżi
pers_nationality_finland=Finlandia
pers_nationality_france=Franja
pers_nationality_freGui=Gujana Francuska
pers_nationality_gabon=Gabon
pers_nationality_gambia=Gambia
pers_nationality_georgia=Gruzja
pers_nationality_germany=Niemcy
pers_nationality_ghana=Ghana
pers_nationality_gibraltarm=Gibraltar
pers_nationality_greece=Grecja
pers_nationality_grenada=Grenada
pers_nationality_guam=Guam
pers_nationality_guatemala=Gwatemala
pers_nationality_guinea=Gwinea
pers_nationality_guyana=Gihuana
pers_nationality_haiti=Haiti
pers_nationality_honduras=Honduras
pers_nationality_hungary=Węgry
pers_nationality_iceland=Islandia
pers_nationality_india=Indie
pers_nationality_indonesia=Indonezja
pers_nationality_iran=Iran
pers_nationality_iraq=Irak
pers_nationality_ireland=Irlandia
pers_nationality_israel=Izrael
pers_nationality_italy=Włochy
pers_nationality_ivoryCoast=Wybrzeże Kości Słoniowej
pers_nationality_jamaica=Jamajka
pers_nationality_japan=Japonia
pers_nationality_jordan=Jordania
pers_nationality_kenya=Kenia
pers_nationality_korea=Korea Południowa
pers_nationality_kuwait=Kuwejt
pers_nationality_kyrgyzstan=Kirgizja
pers_nationality_laos=Laos
pers_nationality_latvia=Łotwa
pers_nationality_lebanon=Liban
pers_nationality_lesotho=Lesotho
pers_nationality_liberia=Liberia
pers_nationality_libya=Libijskie
pers_nationality_liechtenstein=Liechtenstein
pers_nationality_lithuania=Litwa
pers_nationality_luxembourg=Luksemburg
pers_nationality_madagascar=Madagaskar
pers_nationality_malawi=Malawi,
pers_nationality_malaysia=Malezja
pers_nationality_maldives=Malediwy
pers_nationality_mali=Mali
pers_nationality_malta=Malta
pers_nationality_marianaIs=Mariany na Pacyfiku
pers_nationality_martinique=Martinika
pers_nationality_mauritius=Mauritius
pers_nationality_mexico=Meksyk
pers_nationality_moldova=Mołdawia
pers_nationality_monaco=Monako
pers_nationality_montseIs=Montserrat
pers_nationality_morocco=Maroko
pers_nationality_mozambique=Mozambik
pers_nationality_namibia=Namibia
pers_nationality_nauru=Nauru
pers_nationality_nepal=Nepal
pers_nationality_netAnti=Antyle Holenderskie
pers_nationality_netherlands=Holandia
pers_nationality_newZealand=Nowa Zelandia
pers_nationality_nicaragua=Nikaragua
pers_nationality_niger=Niger
pers_nationality_nigeria=Nigeria
pers_nationality_norKorea=Korea Północna
pers_nationality_norway=Norwegia
pers_nationality_oman=Oman
pers_nationality_pakistan=Pakistan
pers_nationality_panama=Panama
pers_nationality_papNewCui=Papua-Nowa Gwinea
pers_nationality_paraguay=Paragwaj
pers_nationality_peru=Peru
pers_nationality_philippines=Filipiny
pers_nationality_poland=Polska
pers_nationality_frenPolyne=Polinezja Francuska
pers_nationality_portugal=Portugalia
pers_nationality_puerRico=Puerto Rico
pers_nationality_qatar=Katar
pers_nationality_reunion=La Réunion
pers_nationality_romania=Rumunia
pers_nationality_russia=Rosja
pers_nationality_saiLueia=Święta Łucja
pers_nationality_saintVinc=Saint Vincent
pers_nationality_samoa_eastern=Samoa Wschodnie
pers_nationality_samoa_western=Samoa Zachodnie
pers_nationality_sanMarino=San Marino
pers_nationality_saoAndPrinc=Wyspy Świętego Tomasza i Książęca
pers_nationality_sauArabia=Arabia Saudyjska
pers_nationality_senegal=Senegal
pers_nationality_seychelles=Seszele
pers_nationality_sieLeone=Sierra Leone
pers_nationality_singapore=Singapur
pers_nationality_slovakia=Słowacja
pers_nationality_slovenia=Słowenia
pers_nationality_solomonIs=Wyspy Salomona
pers_nationality_somali=Somalia
pers_nationality_souAfrica=Republika Południowej Afryki
pers_nationality_spain=Hiszpania
pers_nationality_sriLanka=Sri Lanka
pers_nationality_sudan=Sudańczyk
pers_nationality_suriname=Surinam
pers_nationality_swaziland=Suazi
pers_nationality_sweden=Szwecja
pers_nationality_switzerland=Szwajcaria
pers_nationality_syria=Syria
pers_nationality_tajikstan=Tadżykistan
pers_nationality_tanzania=Tanzania
pers_nationality_thailand=Tajlandia
pers_nationality_togo=Togo
pers_nationality_tonga=Tonga
pers_nationality_triAndToba=Trynidad i Tobago
pers_nationality_tunisia=Tunezja
pers_nationality_turkey=Turcja
pers_nationality_turkmenistan=Turkmenistan
pers_nationality_uganda=Uganda
pers_nationality_ukraine=Ukraina
pers_nationality_uniArabEmira=Zjednoczone Emiraty Arabskie
pers_nationality_uruguay=Urugwaj
pers_nationality_uzbekistan=Uzbekistan
pers_nationality_venezuela=Wenezuela
pers_nationality_vietnam=Wietnam
pers_nationality_yemen=Jemen
pers_nationality_serbia=Serbia
pers_nationality_zimbabwe=Zimbabwe
pers_nationality_zambia=ZAMBIA
pers_nationality_aruba=Aruba
pers_nationality_bhutan=Bhutan
pers_nationality_bosnia_herzegovina=Bośnia i Hercegowina
pers_nationality_cambodia=Kambodża
pers_nationality_congoD=Kongo (Kinszasa)
pers_nationality_comoros=Komory
pers_nationality_capeVerde=Republika Zielonego Przylądka
pers_nationality_croatia=Chorwacja
pers_nationality_dominica=Dominika
pers_nationality_eritrea=Erytrea
pers_nationality_micronesia=Mikronezja
pers_nationalit_guineaBissau=Gwinea Bissau
pers_nationalit_equatorialGuinea=Gwinea Równikowa
pers_nationalit_hongkong=Hongkong, Chiny
pers_nationalit_virginIslands=Wyspy Dziewicze Stanów Zjednoczonych (USVI)
pers_nationalit_britishVirginIslands=Brytyjskie Wyspy Dziewicze
pers_nationalit_kiribati=Kiribati
pers_nationalit_mongolia=Mongolia
pers_nationalit_marshall=Wyspy Marshalla
pers_nationalit_macedonia=Macedonia
pers_nationalit_montenegro=Czarnogóra
pers_nationalit_mauritania=Mauretania
pers_nationalit_palestine=Palestyna
pers_nationalit_palau=Palau
pers_nationalit_rwanda=Rwandan
pers_nationalit_saintKittsNevis=Federacja Saint Kitts i Nevis
pers_nationalit_timorLeste=Timor Wschodni
pers_nationalit_taiwan=Tajwan, Chiny
pers_nationalit_tuvalu=Tuvalu
pers_nationalit_vanuatu=Vanuatu
#Wygenerowanie karty
pers_person_cardprint=Wygenerowanie karty
pers_cardTemplate_tempSelect=Wybieranie szablonu
pers_cardTemplate_printerSelect=Wybieranie drukarki
pers_cardTemplate_front=Strona pozytywna
pers_cardTemplate_opposite=Strona tylna
pers_cardTemplate_entryDate=Data rozpoczęcia pracy
pers_cardTemplate_photo=Zdjęcie
pers_cardTemplate_uploadFail=Awaria nadanie zdjęć!
pers_cardTemplate_jpgFormat=Należy wysłać zdjęcia w formacie jpg!
pers_cardTemplate_printStatus=Stan wydrukowania
pers_cardTemplate_waiting=W trakcie czekania do wydrukowania
pers_cardTemplate_printing=W trakcie wydrukowania
pers_cardTemplate_printOption=Opcja drukowania
pers_cardTemplate_duplexPrint=Drukowanie dwustronne
pers_cardTemplate_frontOnly=Drukuj tylko przód
#app
pers_app_delPers=Nie istnieje personel do usunięcia na serwerze
pers_app_deptIsNull=Nie można było znaleźć numeru lub nazwy odpowiadającej danemu działowi.
pers_app_personNull=Personel nie istnieje
pers_app_pinExist=Numer personelu już istnieje
pers_app_dateError=Nieprawidłowy format daty, należy zwrócić uwagę na prawidłowy format
#API
pers_api_selectPhotoInvalid=Nieuzasadnione zdjęcie, należy nadać ponownie
pers_api_dateError=Nieprawidłowy format daty
pers_api_personNotExist=Personel nie istnieje
pers_api_cardsPersSupport=Tyb wielokartowy dla jednego personelu nie jest włączony w systemie, ustawienie drugiej karty jest nieuzdatnione
pers_api_department_codeOrNameNotNull=Numer lub nazwa działu nie może być pusty
pers_api_deptSortNoIsNull=Sortowanie działów nie może być puste!
pers_api_deptSortNoError=Wartość sortowania działów musi wynosić od 1-999999!
pers_api_dataLimit=Obecna liczba operacji wynosi {0}, przekraczając limit {1}. Proszę działać w partiach!
pers_api_cardTypeError=Błąd typu karty
#API szablonu biologiczny personelu
pers_api_fingerprintExisted=Istnieją odciski palców danego personelu!
pers_api_validtypeIncorrect=Błędne wprowadzenie typu uzasadnienia!
pers_api_dataNotExist=Numer szablonu nie istnieje!
pers_api_templateNoRang=Zakres numeru szablonu odcisku jest 0-9!
pers_api_templateIsNull=Szablon nie może być pusty!
pers_api_versionIsNumber=Wersja może wprowadzać tylko numery!
#Samo obsługiwane zarejestrowanie dla tymczasowego personelu
pers_tempPersion_pinOnlyNumber=Należy wprowadzić tylko liczby do numeru personelu! 
#biotime
pers_h5_personAvatarNotNull=Awatar jest pusty
pers_h5_personMobileRepeat=Numer telefonu już istnieje
pers_h5_personEmailRepeat=Adres mailowy już istnieje
pers_h5_pwdIsRepetition=Powtarzalne hasła do starego
pers_h5_personIdNull=Nazwa personelu jest pusta
pers_h5_pinOrEmailIsNull=Należy wprowadzić numer i adres mailowy
pers_emailTitle_resetPassword=Zmiana hasła
pers_emailContent_resetPassword=Dany link jest ważny w ciągu 24 godzin, należy skopiować go do przeglądarki do zmiany hasła:
pers_h5_tokenIsNull=Dowód jest pusty
pers_h5_tokenError=Błędny lub nieważny dowód
pers_h5_oldPwdIsError=Błędne wprowadzenie starego hasła
pers_api_resetPasswordSuccess=Skuteczna zmiana hasła
pers_api_resetPasswordFail=Awaria zmiany hasła
pers_api_forgetPassword=Zresetowanie hasła
pers_api_confirmSubmit=Potwierdzenie wysyłania
pers_api_confirmPwdCaution=Kliknąć „potwierdzić” do potwierdzenia nowego hasła 
pers_api_pwdRule=Hasło zawiera obowiązkowo co najmniej jeden znak lub liczbę, długość hasła zawiera przynamniej 8-12 znaków
pers_h5_personPinFormatNumber=Numer personelu tylko przysługuje kombinację liczb z literami
pers_h5_persEmailNoExist=Błędne wprowadzenie adresu mailowego
pers_h5_pageNull=błąd parametru paginacji
pers_h5_personPinNotStartWithZero=Numer personelu nie zaczyna się od zera
pers_h5_personPinTooLong=Numer personelu jest zbyt długi
pers_h5_personPinInValid=Dany numer już jest w trakcie użycia
pers_h5_imgSizeError=Należy wysłać zdjęcia w rozmiarze do 10M!
pers_h5_confirmAndContinue=Potwierdź i kontynuuj
#Nieprawidłowe wycięcie na twarzy
pers_face_poorResolution=Rozdzielczość zdjęcia jest mniejsza niż 80000 piksel
pers_face_noFace=Nie wykryto żadnej twarzy
pers_face_manyFace=Wykryto wiele twarzy
pers_face_smallFace=Proporcja twarzy jest zbyt mała
pers_face_notColor=Niekolorowe zdjęcie 
pers_face_seriousBlur=Rozmyte zdjęcie
pers_face_intensivelyLight=Zdjęcia przedstawiające dużą ekspozycję 
pers_face_badIllumination=Zdjęcie przedstawiające zbyt ciemne
pers_face_highNoise=Zdjęcia przedstawiające wysoki poziom hałasu
pers_face_highStretch=Przeciążenie twarzy
pers_face_covered=Zablokowanie twarzy
pers_face_smileOpenMouth=Nadmierny uśmiech
pers_face_largeAngle=Nadmierny kąt przechylenia twarzy 
pers_face_criticalIllumination=Granica jasności zdjęcia
pers_face_criticalLargeAngle=Granica kąta przechylenia twarzy
pers_face_validFailMsg=Awaria wykrycia twarzy, przyczyny:
pers_face_failType=Typ błędu wycięcia twarzy
pers_face_photoFormatError=Format zdjęcia jest nieprawidłowy, prześlij plik w formacie JPG / PNG.
pers_face_notUpdateMsg=Nie udało się wygenerować zdjęcia twarzy, nie aktualizuj zdjęcia twarzy.
# 健康 申报
pers_health_enable=Włącz deklarację informacji o stanie zdrowia
pers_health_attrExposure=Jakiekolwiek narażenie na podejrzane przypadki
pers_health_attrSymptom=Wszelkie objawy w ciągu ostatnich 14 dni
pers_health_attrVisitCity=Miasto odwiedzone w ciągu ostatnich 14 dni
pers_health_attrRemarks=Uwagi na temat zdrowia
pers_health_symptomCough=Kaszel
pers_health_symptomFever=Gorączka
pers_health_symptomPolypena=Problemy z oddychaniem
pers_health_declaration=Deklaracja zdrowia
pers_health_aggrement=Zgodziłem się, że osoby, które nie wypełnią wymaganych informacji, nie będą miały dostępu, a odwiedzający, którzy nie zgłosili ich zgodnie z prawdą, nie mogą kontynuować swojej wizyty i muszą ponosić odpowiednie zobowiązania prawne.
pers_health_visitCity_notEmpty=Odwiedzone miasto nie może być puste!
pers_health_notAgree=Sprawdź umowę, aby kontynuować.
#人员名单库
pers_personnal_list_manager=Menedżer listy
pers_personnal_list=Lista osobista
pers_personnal_list_scheme=Tryb domeny
pers_personnal_list_name=Osoba nazwa listy
pers_personnal_list_group_str_id=Lista ID grupy group
pers_personnal_list_personCount=Liczba osób
pers_personnal_list_tag=Zdefiniowane przez użytkownika
pers_personnal_list_type=Typ listy osobistej
pers_personnallist_addPerson_repo=Dodaj osobę do repozytorium
pers_personnallist_sendPersonnallist=Rozproszona biblioteka list
pers_personnallist_sendPerson=Wyślij osobę
pers_personnallist_notDel_existPerson=Biblioteka list zawiera osoby i nie można jej usunąć
pers_personnallist_peopleInRoster=W bibliotece list wciąż są osoby
pers_personnallist_associationNotExist=Powiązanie między głównym urządzeniem a biblioteką list nie istnieje
pers_personnal_list_person=Osoba z listy osób
pers_personnal_list_dev=Lista uprawnień do biblioteki
pers_personnal_list_addDev=Dodaj urządzenie
pers_personnal_list_name_isExist=Nazwa już istnieje
pers_personnal_bannedList=zabroniona biblioteka list
pers_personnal_allowList=Zezwól na bibliotekę list
pers_personnal_redList=Biblioteka czerwonych list
pers_personnal_attGroup=Grupa obecności
pers_personnal_passList=Lista przepustek
pers_personnal_banList=Zakazana lista
pers_personnal_visPassList=Lista kart odwiedzających
pers_personnal_visBanList=Lista zakazanych odwiedzających
pers_personnal_databaseHasBeenDistributed=Wybrana biblioteka list została rozesłana i nie można jej usunąć
pers_personnel_sendError_dueTo=Niepowodzenie dostawy Przyczyna niepowodzenia:
pers_personnel_sendError_reson={0} istnieje na liście zablokowanych, usuń i dodaj
#比对照片-样片示例
pers_examplePic_Tip=Powinna spełniać następujące wymagania:
pers_examplePic_Tip1=1. kolor tła jest czysto biały, a personel nosi ciemne ubrania;
pers_examplePic_Tip2=2. Zdjęcia elektroniczne są w formacie JPG, PNG, JPEG, zalecany zakres pikseli: 480*640 < piksel < 1080*1920;
pers_examplePic_Tip3=3. Portrety na zdjęciach elektronicznych powinny mieć otwarte oczy i patrzeć prosto przed siebie oraz mieć pewność, że źrenice są dobrze widoczne;
pers_examplePic_Tip4=4. Portret na zdjęciu elektronicznym powinien mieć neutralny wyraz twarzy i możesz się uśmiechać, ale nie pokazuj zębów;
pers_examplePic_Tip5=5. Portret na zdjęciu elektronicznym powinien być wyraźny, z naturalnymi kolorami, bogatymi warstwami i bez widocznych zniekształceń. Brak cieni, świateł lub odbić na portretowanych twarzach lub tłach; kontrast i jasność są odpowiednie.
pers_examplePic_description=Poprawny przykład
pers_examplePic_error=Przykład błędu:
pers_examplePic_error1=Przesadny wyraz twarzy (nadmierny uśmiech)
pers_examplePic_error2=Światło jest za ciemne
pers_examplePic_error3=Twarz jest za mała (rozdzielczość jest za mała)
pers_applogin_enabled=Włącz logowanie aplikacji
pers_applogin_disable=Wyłącz logowanie aplikacji
pers_applogin_status=Status logowania aplikacji