#pers common.
#here other module can also use the label from pers.
pers_module=Personale
pers_common_addPerson=Aggiungi personale
pers_common_delPerson=Elimina personale
pers_common_personCount=Quantità personale
pers_common_browsePerson=Sfoglia personale
#左侧菜单
pers_person_manager=Personale
pers_person=Persona
pers_department=Reparto
pers_leave=Personale licenziato
pers_tempPerson=Personale a tempo determinato
pers_attribute=Attributi personalizzati
pers_card_manager=Gestione carte
pers_card=Carta
pers_card_issue=Registro carte emesse
pers_wiegandFmt=Formato Wiegand
pers_position=Position
#人员
pers_person_female=Femminile
pers_person_male=Maschile
pers_person_pin=ID personale
pers_person_departmentChange=Regola reparto
pers_personDepartment_changeLevel=Autorizzazione cambio dipartimento
pers_person_gender=Sesso
pers_person_detailInfo=Dettaglio personale
pers_person_accSet=Controllo accessi
pers_person_accSetting=Impostazione controllo accessi
pers_person_attSet=Rilevazione presenze
pers_person_eleSet=Controllo ascensori
pers_person_eleSetting=Impostazione controllo ascensori
pers_person_parkSet=Registrazione targhe
pers_person_pidSet=Certificato personale
pers_person_insSet=FaceKiosk
pers_person_aiSet=Face Intellect
pers_person_payrollSet=Impostazione retribuzione
pers_person_psgSet=Impostazione passaggio
pers_person_lockerSet=Impostazione armadietto
pers_person_sisSet=Impostazioni del controllo di sicurezza
pers_person_vdbSet=Impostazioni interfono visivo
pers_person_firstName=Nome
pers_person_lastName=Cognome
pers_person_name=Nome
pers_person_wholeName=Nome
pers_person_fullName=Nome, secondo nome, cognome
pers_person_cardNum=Numero di carte possedute
pers_person_deptNum=Numero di reparti coinvolti
pers_person_dataCount=Statistiche
pers_person_regFinger=Impronta digitale
pers_person_reg=Registra
pers_person_password=Password verifica dispositivo
pers_person_personDate=Data impiego
pers_person_birthday=Compleanno
pers_person_mobilePhone=Cellulare
pers_person_personDevAuth=Autorizzazioni persone e dispositivi
pers_person_email=Email
pers_person_browse=Sfoglia
pers_person_authGroup=Livelli di accesso
pers_person_setEffective=Imposta ora di entrata in vigore
pers_person_attArea=Area presenze
pers_person_isAtt=Calcolo presenze
pers_person_officialStaff=Personale regolare
pers_person_probationStaff=Personale in prova
pers_person_identity_category=Tipo di personale
pers_person_devOpAuth=Ruolo che utilizza i dispositivi
pers_person_msg1=Il numero effettivo della carta e il codice del sito devono essere inseriti contemporaneamente.
pers_person_msg2=Inserire 3-4 cifre.
pers_person_msg3=Errore di formato.
pers_person_imgPixel=(dimensione ottimale 120*140).
pers_person_cardLengthDigit=Please enter the number.
pers_person_cardLengthHexadecimal=Inserisci numeri o lettere abcdef!
pers_person_to=A
pers_person_templateCount=Numero di impronte digitali
pers_person_biotemplateCount=Numero di modelli biologici
pers_person_regFace=Volto
pers_person_regVein=Vena del dito
pers_person_faceTemplateCount=Numero di volti
pers_person_VeinTemplateCount=Numero di vene del dito
pers_person_palmTemplateCount=Numero di palmi
pers_person_cropFace=Foto
pers_person_cropFaceCount=Numero di foto del volto
pers_person_faceBiodataCount=Visible Face Quantity
pers_person_irisCount=Numero di Iris
pers_person_batchToDept=Nuovo reparto
pers_person_changeReason=Ragione trasferimento
pers_person_selectedPerson=Persona selezionata
pers_person_duressPwdError=Password ripetuta
pers_person_completeDelPerson=Elimina
pers_person_recover=Recupera
pers_person_nameNoComma=Non includere la virgola.
pers_person_firstNameNotEmpty=Il campo del nome non può essere vuoto.
pers_person_lastNameNotEmpty=Il campo del cognome non può essere vuoto.
pers_person_mobilePhoneValidate=Inserire un numero valido di telefono cellulare.
pers_person_phoneNumberValidate=Inserire un numero di telefono valido.
pers_person_postcodeValidate=Inserire un CAP valido.
pers_person_idCardValidate=Inserire un numero di carta d’identità valido.
pers_person_pwdOnlyLetterNum=La password può contenere solo numeri o lettere.
pers_person_certNumOnlyLetterNum=Il numero di certificato può contenere solo numeri o lettere.
pers_person_oldDeptEqualsNewDept=Il nuovo reparto regolato non può coincidere con il reparto originario.
pers_person_disabled=Accesso disabilitato
pers_person_emailError=Inserire un indirizzo e-mail valido.
pers_person_driverPrompt=Installare il driver del dispositivo. Fare clic su OK per scaricare il driver.
pers_person_readIDCardFailed=Lettura non eseguita.
pers_person_cardPrompt=Mantenere la carta identificativa nella regione...
pers_person_iDCardReadOpenFailed=Nessun lettore di carte identificative rilevato.
pers_person_iDCardNotFound=Carta identificativa non trovata; riprovare.
pers_person_nameValid=Supporta cinese, inglese, numeri, \'-\', \'_\'.
pers_person_nameValidForEN=Supporta inglese, numeri, \'-\', \'_\', \'.\'.
pers_person_pinPrompt=Inserire caratteri dell’alfabeto inglese, numeri.
pers_person_pinSet=Impostazione ID personale
pers_person_supportLetter=Supporto lettere
pers_person_cardsSupport=Più carte per persona
pers_person_SupportDefault=Crea automaticamente ID personale
pers_person_noSpecialChar=Non è possibile inserire caratteri speciali.
pers_person_pinInteger=Inserire i caratteri.
pers_pin_noSpecialChar=Il numero del personale non deve contenere caratteri speciali!
pers_op_capture=Acquisisci
pers_person_cardDuress=Numero carta ripetuto
pers_person_pwdException=Eccezione password
pers_person_systemCheckTip=Verificare che i dati del personale siano nel formato corretto.
pers_person_immeHandle=Gestione immediata
pers_person_cardSet=Impostazione carta
pers_person_tempPersonSet=Impostazione personale a tempo determinato
pers_person_cardsReadMode=Modalità lettura carta
pers_person_cardsReadModeReadHead=Lettura da unità di controllo
pers_person_cardsReadModeID180=Lettura da ID180
pers_person_IDReadMode=Modalità lettura carta identificativa
pers_person_IDReadModeIDCardReader=Lettore di carte identificative
pers_person_IDReadModeTcpReadHead=Lettore TCP/IP
pers_person_physicalNo=Numero carta fisica per carta identificativa
pers_person_physicalNoToCardNo=Usa numero carta fisica per carta identificativa
pers_person_ReadIDCard=Leggi carta identificativa
pers_person_templateBioTypeNumber=Numero del tipo di modello biometrico
pers_person_templateValidType=Validità modello biometrico
pers_person_templateBioType=Tipo modello biometrico
pers_person_templateVersion=Versione modello biometrico
pers_person_template=Modello biometrico
pers_person_templateNo=N. modello biometrico
pers_person_templateNoIndex=Indice modello biometrico
pers_person_templateDataUpdate=Aggiornamento dei dati quando esiste un modello biometrico:
pers_person_templateValidTypeNoNull=Il campo della validità del modello biometrico non può essere vuoto.
pers_person_templateBioTypeNoNull=ID personale: {0}, il campo del tipo di modello biometrico non può essere vuoto.
pers_person_templateVersionNoNull=ID personale: {0}, il campo della versione del modello biometrico non può essere vuoto.
pers_person_templateNoNull=ID personale: {0}, il campo del modello biometrico non può essere vuoto.
pers_person_templateNoNoNull=ID personale: {0}, il campo del numero del modello biometrico non può essere vuoto.
pers_person_templateNoIndexNoNull=ID personale: {0}, il campo dell’indice del modello biometrico non può essere vuoto.
pers_person_templateError=ID personale: {0}, modello biometrico errato.
pers_person_bioDuress=Duress
pers_person_universal=Comune
pers_person_voice=Impronta vocale
pers_person_iris=Iride
pers_person_retina=Retina
pers_person_palmPrints=Palmo
pers_person_metacarpalVein=Vena del palmo
pers_person_visibleFace=Volto visibile
pers_person_pinError=L’ID personale {0} non esiste; impossibile elaborare questi dati.
pers_person_pinException=Eccezione ID personale
pers_person_pinAutoIncrement=Incremento automatico ID personale
pers_person_resetSelfPwd=Reimposta password di accesso automatico
pers_person_picMaxSize=La risoluzione dell’immagine è troppo alta; quella raccomandata è inferiore a {0}.
pers_person_picMinSize=La risoluzione dell’immagine è troppo bassa; quella raccomandata è superiore a {0}.
pers_person_cropFaceShow=Visualizza foto volto
pers_person_faceNoFound=Volto non riconosciuto
pers_person_biometrics=Tipo biometrico
pers_person_photo=Foto personali
pers_person_visibleFaceTemplate=Modello viso visibile
pers_person_infraredFaceTemplate=Modello viso statico a infrarossi
pers_person_delBioTemplate=Elimina dati biometrici
pers_person_delBioTemplateSelect=Seleziona il modello biologico da eliminare!
pers_person_infraredFace=Viso statico a infrarossi
pers_person_notCard=Non include la carta
pers_person_notRegFinger=Non include l'impronta digitale
pers_person_notMetacarpalVein=Non include la vena del palmo
pers_person_notRegVein=Non include le vene delle dita
pers_person_notIris=Escluso iride
pers_person_notInfraredFaceTemplate=Non include il modello viso nel vicino infrarosso
pers_person_notVisibleFaceTemplate=Non include il modello del viso a luce visibile
pers_person_notVisibleFacePhoto=Non include foto del viso visibili
pers_person_visibleFacePhoto=Foto del viso visibile
pers_person_change=Adeguamenti del personale
pers_person_cropFaceUsePhoto=Vuoi visualizzare la foto di confronto come avatar?
pers_person_photoUseCropFace=Vuoi usare l'avatar per generare una foto di confronto?
pers_person_selectCamera=Seleziona telecamera di cattura
pers_person_delCropFaceMsg=Non ci sono foto di confronto da eliminare!
pers_person_disabledNotOp=La persona è stata disabile e non può operare!
pers_person_visiblePalm=Palmo luminoso visibile
pers_person_notVisiblePalm=Non include il palmo della luce visibile
pers_person_selectDisabledNotOp=Ci sono persone disabili nel personale selezionato, incapace di operare!
pers_person_photoUseCropFaceAndTempalte=Vuoi usare avatar per generare foto di confronto e modelli facciali?
pers_person_photoUseTempalte=Vuoi usare le foto per generare modelli di viso?
pers_person_createCropFace=Genera foto di confronto
pers_person_createFaceTempalte=Genera modelli facciali
pers_person_faceTempalte=Modelli facciali
pers_person_extractFaceTemplate=Estrazione dei modelli facciali
pers_person_createSuccess=Generato con successo
pers_person_createFail=Generazione fallita
pers_person_serverConnectWarn=Indirizzo del server, nome utente e password non possono essere vuoti!
pers_person_serverOffline=Server di estrazione del modello facciale offline
pers_person_faceTemplateError1=Rilevamento facciale fallito
pers_person_faceTemplateError2=Occlusione facciale
pers_person_faceTemplateError3=Insufficiente chiarezza
pers_person_faceTemplateError4=Angolo della faccia troppo grande
pers_person_faceTemplateError5=Rilevamento live non riuscito
pers_person_faceTemplateError6=Estrazione del modello facciale non riuscita
pers_person_cropFaceNoExist=La foto di confronto non esiste
pers_person_disableFaceTemplate=La funzione di estrazione del modello facciale non è abilitata, non è possibile estrarre il modello!
pers_person_cropFacePhoto=Foto di confronto viso
pers_person_vislightPalmPhoto=Foto di confronto delle palme
pers_person_serverOfflineWarn=Il server di estrazione del modello facciale è offline e non può abilitare questa funzione!
pers_person_serverConnectInfo=Si prega di verificare se il server di estrazione del modello facciale è online?
pers_person_notModified=Il numero di cellulare non può essere modificato.
pers_person_syncAcms=Sincronizzare il personale con ACMS
pers_person_startUpdate=Inizia a aggiornare le informazioni sul personale.
pers_person_updateFailed=L'aggiornamento delle informazioni sul personale è fallito!
#控制器发卡
pers_person_readCard=Emetti carta dal dispositivo
pers_person_stopRead=Arresta emissione
pers_person_chooseDoor=Scegli la porta
pers_person_readCarding=Carta di emissione, si prega di riprovare più tardi!
#抓拍照片
pers_capture_catchPhoto=Acquisisci foto
pers_capture_preview=Anteprima
#部门
pers_dept_entity=Reparto
pers_dept_deptNo=Numero reparto
pers_dept_deptName=Nome reparto
pers_dept_parentDeptNo=Numero reparto principale
pers_dept_parentDeptName=Nome reparto principale
pers_dept_parentDept=Reparto principale
pers_dept_note=Se il nuovo reparto non è presente nell’elenco, contattare l’amministratore per riautorizzare l’utente a modificare il reparto.
pers_dept_exit=Contiene
pers_dept_auth=Utente di sistema associato
pers_dept_parentMenuMsg=Il reparto superiore non può essere impostato come un reparto inferiore.
pers_dept_initDept=Generale
pers_dept_deptMarket=Reparto Marketing
pers_dept_deptRD=Reparto Sviluppo
pers_dept_deptFinancial=Reparto Finanza
pers_dept_nameNoSpace=Il nome del reparto non può iniziare o terminare con degli spazi.
pers_dept_nameExist=Nome reparto già esistente
pers_dept_changeLevel=Passare al livello di questo reparto?
pers_dept_noSpecialChar=Il numero del servizio non può contenere un carattere speciale!
pers_dept_NameNoSpecialChar=Il nome del servizio non deve contenere caratteri speciali!
pers_dept_noModifiedParent=Il dipartimento superiore non può essere modificato!
#职位
pers_position_entity=Posizione
pers_position_code=Numero posizione
pers_position_name=Nome posizione
pers_position_notExist=La posizione non esiste.
pers_position_sortNo=Ordina
pers_position_parentName=Posizione principale
pers_position_parentCode=Numero posizione principale
pers_position_batchToPosition=Nuova posizione
pers_position_nameExist=Nome posizione già esistente
pers_position_change=Cambia posizione
pers_position_parentMenuMsg=La posizione principale non può essere impostata come posizione secondaria.
pers_position_nameNoSpace=Il nome della posizione non può iniziare o terminare con degli spazi.
pers_position_existSub={0} :Contiene postazioni secondarie e non può essere eliminato
pers_position_existPerson={0} :Il personale non può essere eliminato
pers_position_importTemplate=Modello di importazione del lavoro
pers_position_downloadTemplate=Scarica modello di importazione
pers_position_codeNotEmpty=Il numero della posizione non può essere vuoto
pers_position_nameNotEmpty=Il nome della posizione non può essere vuoto
pers_position_nameNoSpecialChar=Il nome della posizione {0} non può contenere caratteri speciali!
pers_position_noSpecialChar=Il numero di posizione {0} non può essere un carattere speciale!
pers_position_codeLength=Il numero di posizione {0} supera le 30 cifre di lunghezza
pers_position_nameLength=Dati il cui nome di posizione {0} è più lungo di {1}
pers_position_codeExist=L'ID lavoro {0} esiste già
#证件
pers_cert_type=Tipo certificato
pers_cert_number=Numero certificato
pers_cert_name=Nome certificato
pers_cert_numberExist=Numero di certificato già presente.
#导出
pers_export_allPersPerson=Tutte le persone
pers_export_curPersPerson=Persona corrente
pers_export_template=Esporta modello
pers_export_personInfo=Esporta personale
pers_export_personInfoTemplate=Scarica modello importazione personale
pers_export_personBioTemplate=Esporta modello biometrico
pers_export_basicInfo=Informazioni di base
pers_export_customAttr=Attributi personalizzati
pers_export_templateComment=Nome campo,chiave principale?univoca?consentire valori null?({0},{1},{2},{3})
pers_export_templateFileName=Modello importazione personale
pers_export_bioTemplateFileName=Modello biometrico personale
pers_export_deptInfo=Esporta reparto
pers_export_deptTemplate=Scarica modello importazione reparto
pers_export_deptTemplateFileName=Modello importazione reparto
pers_export_personPhoto=Esporta foto personale
pers_export_allPhotos=Tutte le foto (selezionare tutte le persone)
pers_export_selectPhotoToExport=Selezionare ID iniziale e ID finale per esportare le foto del personale.
pers_export_fromId=Da ID
pers_export_toId=A
pers_export_certNumberComment=Il tipo di certificato è richiesto dopo aver inserito il numero del certificato
pers_export_templateCommentName=Nome campo:({0})
pers_export_dataExist=Assicurati che i dati importati esistano già nel sistema
pers_export_cardNoTip=Numeri multipli di carte separati da &
pers_carNumber_importTip=Numero di targa (più targhe utilizzate& separate)
#导入
pers_import_certNumberExist=Numero di certificato {0} già presente.
pers_import_complete=Completa
pers_import_password=Password persona
pers_import_fail=Errore riga {0}: {1}
pers_import_overData=Importazione di {0} persone; il sistema supporta l’importazione di sole 30.000 persone.
pers_import_pinTooLong=ID personale {0} troppo lungo.
pers_import_pinExist=ID Personale {0} già presente.
pers_import_pinIsRepeat=ID Personale {0} ripetuto.
pers_import_pinError=Errore ID Personale {0}.
pers_import_pinNotSupportLetter=L’ID del personale non supporta i caratteri inglesi. Il numero del personale è: {0}
pers_import_pinNotSupportNonAlphabetic=L’ID del personale non supporta i caratteri non inglesi. Il numero del personale è: {0}
pers_import_pinNotNull=L’ID del personale non può essere costituito da soli zeri.
pers_import_pinStartWithZero=L’ID del personale non può iniziare con uno zero.
pers_import_cardNoNotSupportLetter=Il numero della carta non supporta le lettere.
pers_import_cardNoNotNull=Il numero della carta non può essere costituito da soli zeri.
pers_import_cardNoStartWithZero=Il numero della carta non può iniziare con uno zero.
pers_import_cardTooLong=Numero carta {0} troppo lungo.
pers_import_cardExist=Numero carta {0} già presente.
pers_import_personPwdOnlyNumber=La password del personale supporta soltanto i numeri (non le lettere).
pers_import_personPwdTooLong=La password del personale {0} è troppo lunga.
pers_import_personDuressPwd=La password del personale {0} è ripetuta.
pers_import_emailTooLong=L’indirizzo e-mail {0} è troppo lungo.
pers_import_nameTooLong=Il nome {0} è troppo lungo.
pers_import_genderError=Errore formato sesso.
pers_import_personDateError=Errore formato data assunzione.
pers_import_createTimeError=Errore formato ora creazione.
pers_import_phoneError=Errore formato numero di telefono.
pers_import_phoneTooLong=La lunghezza del numero di telefono non può superare i 20 caratteri.
pers_import_emailError=Errore formato e-mail.
pers_import_birthdayError=Errore formato data di nascita.
pers_import_nameError=Cognome e nome non possono includere simboli speciali. Il numero del personale è: {0}
pers_import_firstnameError=Il cognome e il nome non possono includere “,”.
pers_import_firstnameNotNull=Il campo del nome non può essere vuoto.
pers_import_dataCheck=Il controllo dei dati è terminato
pers_import_dataSaveFail=Impossibile importare i dati.
pers_import_allSucceed=Tutti i dati importati.
pers_import_result=Riuscito: {0}, non riuscito: {1}.
pers_import_result2=Risultato importazione
pers_import_result3=Riuscito: {0}, aggiornato: {1}, non riuscito: {2}.
pers_import_notSupportFormat=Formato non supportato.
pers_import_selectCorrectFile=Selezionare il file corretto.
pers_import_fileFormat=Formato file
pers_import_targetFile=File di destinazione
pers_import_startRow=Righe iniziali intestazione
pers_import_startRowNote=La prima riga del formato dei dati è il nome della tabella, la seconda riga è l’intestazione, la terza riga corrisponde ai dati di importazione; verificare il file e procedere con l’importazione.
pers_import_delimiter=Delimitatore
pers_import_importingDataFields=Campi database
pers_import_dataSourceFields=Importazione campi dati
pers_import_total=Totale
pers_import_dataUpdate=Aggiornare l’ID personale presente nel sistema:
pers_import_dataIsNull=Nessun dato nel file.
pers_import_deptNotExist=Il reparto non esiste.
pers_import_deptIsNotNull=Il campo del nome del reparto non può essere vuoto.
pers_import_pinNotEmpty=Il campo dell’ID del personale non può essere vuoto.
pers_import_nameNotEmpty=Il nome del personale non può essere vuoto!
pers_import_siteCodeOnlyLetterNum=Errore di formato del codice del sito.
pers_import_cardNoFormatErrors=Il formato del numero della carta {0} è errato.
pers_import_cardsNotSupport=La funzionalità [Più carte per persona] è disabilitata; impossibile programmare più di una carta per persona.
pers_import_personInfo=Importa personale
pers_import_commentFormat=Il formato del commento è errato.
pers_import_noComment=I dati nella riga {0} e nella colonna {1} non sono commentati.
pers_import_fieldRepeat=I dati nella riga {0} e nella colonna {1},il nome del campo nel commento è ripetuto:{2}
pers_import_primaryKey=Deve essere presente almeno un campo come chiave principale.
pers_import_templateIsRepeat=ID personale: Dati modello biometrico per {0} duplicati.
pers_import_biologicalTemplate=Importa modello biometrico
pers_import_uploadFileSuccess=Caricamento riuscito; avvio dell’analisi dei dati, attendere...
pers_import_resolutionComplete=Dati analizzati; avvio degli aggiornamenti del database.
pers_import_mustField=Il file di importazione deve contenere la colonna {0}.
pers_import_bioTemplateSuccess=Importa modello biometrico; questi dati devono essere sincronizzati manualmente sul dispositivo da ciascun modulo aziendale.
pers_import_personPhoto=Importa foto personale
pers_import_opera_log=Operation log
pers_import_error_log=Error log
pers_import_uploadFileSize=Please upload a file with a size no larger than {0}!
pers_import_uploadFileSizeLimit=For a single import, please upload a file with a size no larger than 500M!
pers_import_type=Import mode
pers_import_photoType=Photo
pers_import_archiveType=Compressed package
pers_import_startUpload=Start Upload
pers_import_addMore=Add More
pers_import_photoQuality=Photo Quality
pers_import_original=Original
pers_import_adaptive=Adaptive
pers_import_adaptiveSize=(Size 480 * 640)
pers_import_totalNumber=Total
pers_import_uploadTip=(Please do not delete photo while uploading)
pers_import_addPhotoTip=Please add photos to upload!
pers_import_selectPhotoTip=Please select the photo you want to upload!
pers_import_uploadResult=Upload Photo Result
pers_import_pleaseSelectPhoto=Please Select Photo
pers_import_multipleSelectTip=Press Ctrl to make multiple selections
pers_import_replacePhotoTip=The selected photo is already in the upload list, do you want to replace it?
pers_import_photoNamePinNotCorrespond=Photo name and personnel id mismatch
pers_import_photoFormatRequirement=Please name the photo with personnel ID.The correct format is JPG/PNG.Make sure the photo name does not contain special characters.
pers_import_filterTip=Some of the photos you selected cannot be previewed, and there may be the following reasons:
pers_import_photoContainSpecialCharacters=Photo name has special characters.
pers_import_photoFormatError=Photo format is incorrect.
pers_import_photoSelectNumber=Do not choose more than 3000 pictures in a single import!
pers_import_photoSelectNumberLimit=Don't choose more than {0} images!
pers_import_fileMaxSize=The picture is too large, please upload an image file smaller than 5M.
pers_import_notUploadPhotoNumber=No more than 3000 images can be uploaded!
pers_import_zipFileNotPhoto=There is no photo of the person in the zip file, please re-select and import!
pers_import_personPlateRepeat=Personnel license plate {0} duplicate!
pers_import_filePlateRepeat=File inside the license plate {0} duplicate!
pers_import_personPlateFormat=Personnel license plate {0} format is incorrect!
pers_import_personPlateMax=The number of personnel license plates exceeds the maximum of 6!
pers_import_cropFaceFail=Failed to generate comparison photo!
pers_import_pinLeaved=Personnel ID: {0} has left.
pers_import_exceedLicense=Imports are not allowed for the number of people who exceed the software license.
pers_import_bioTemplateNotNull=Personnel ID, Biometric Template Type, ID or Index and Content, Version are not allowed to be empty!
pers_import_certTypeNotNull=Personnel number is: {0} Document type cannot be empty
pers_import_certTypeNotExist=Il numero del personale è: {0} Il tipo di documento non esiste
pers_import_certNumNotNull=Il numero del personale è: {0} Il numero del certificato non può essere vuoto
pers_import_certNumberTooLong=Row {0}:ID number {1} is too long!
pers_import_idNumberErrors=Row {0}:ID card number {1} is malformed!
pers_import_emailErrors=Row {0}:Email address {1} format error!
pers_import_emailIsExist=Email address {0} already exists!
pers_import_emailIsRepeat=Row {0}: The internal email address of the file {1} has been repeated!
pers_import_fileMobilePhoneRepeat=Riga {0}: Il numero di cellulare interno del file {1} è stato ripetuto!
pers_import_mobilePhoneErrors=Errore nel formato del numero di cellulare!
pers_import_hireDateError=Il formato della data di inserimento non è corretto!
pers_import_selectPhotoType=Seleziona il tipo di foto importata!
pers_import_hireDateLaterCurrent=La data di noleggio non può essere successiva alla data corrente!
pers_import_buildingNotExist=L'edificio non esiste!
pers_import_unitNotExist=L'unità non esiste!
pers_import_vdbInfoFail=Non ci sono informazioni sull'unità denominata {1} nell'edificio {0}!
pers_import_vdbBuildingFail=Le informazioni sull'edificio dell'unità {0} non possono essere vuote!
pers_import_vdbRoomNoFail=Il numero della stanza deve essere un valore numerico maggiore di 0!
#人员离职
pers_person_leave=Licenziamento
pers_dimission_date=Data licenziamento
pers_dimission_type=Tipo licenziamento
pers_dimission_reason=Causa licenziamento
pers_dimission_volutary=Dimissioni volontarie per esubero
pers_dimission_dismiss=Licenziato
pers_dimission_resignat=Dimissione
pers_dimission_shiftJob=Trasferimento
pers_dimission_leave=Mantenimento del lavoro senza stipendio
pers_dimission_recovery=Reintegrazione
pers_dimission_sureToRecovery=Eseguire l’operazione di reintegrazione?
pers_dimission_backCard=La carta è stata restituita?
pers_dimission_isForbidAction=Disabilitare immediatamente i diritti di controllo accessi?
pers_dimission_writeInfomation=Inserire le informazioni sul licenziamento
pers_dimission_pinRetain=Mantenere l’ID personale del dipendente licenziato?
pers_dimission_downloadTemplate=Scarica modello importazione distribuzione
pers_dimission_import=Import Dismissioni
pers_dimission_importTemplate=Modello importazione autorizzazione
pers_dimission_date_noNull=La data di rilascio non può essere vuota
pers_dimission_leaveType_noExist=Il tipo di rilascio non esiste
pers_dimission_dateFormat=Campo obbligatorio, il formato dell'ora è aaaa-MM-gg, ad esempio: 22-07-2020
pers_dimission_leaveType=Campo obbligatorio, ad esempio: Dimissioni volontarie per esubero, Licenziato, Dimissione, Trasferimento
pers_dimission_forbidden=Iscriviti alla lista nera
pers_dimission_leaveType_noNull=Il tipo di permesso non può essere vuoto!
pers_dimission_person_noExist=La persona che licenzia non esiste!
pers_dimission_date_error=Data di rifiuto non compilata o formato errato
#临时人员
pers_tempPerson_audit=Rivedi
pers_tempPerson_view=Visualizza
pers_tempPerson_waitReview=In attesa della revisione dell'amministratore
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Numero carta
pers_card_state=Stato carta
pers_card_effect=Validità
pers_card_disabled=Non valida
pers_card_past=Scaduta
pers_card_back=Carta restituita
pers_card_change=Sostituzione carta
pers_card_note=Numero di carta già esistente.
pers_card_numTooBig=Il numero della carta originale è troppo lungo.
pers_issueCard_entity=Emetti carta
pers_issueCard_operator=Operatore
pers_issueCard_operate=Azione
pers_issueCard_note=Scrittori di carte in funzione per i dati del personale registrato ma non per il personale con account della carta di registrazione.
pers_issueCard_date=Data emissione carta
pers_issueCard_changeTime=Ora modifica
pers_issueCard_cardValidate=Non sono consentiti spazi.
pers_issueCard_cardEmptyNote=Il campo della carta non può essere vuoto.
pers_issueCard_cardHasBeenIssued=La carta è stata emessa.
pers_issueCard_noCardPerson=Persona con mancata emissione
pers_issueCard_waitPerson=Persona con emissione corrente
pers_issueCard_mc5000=Emissione di carte MC5000
pers_batchIssCard_entity=Emetti carta per lotto
pers_batchIssCard_startPersNo=ID personale iniziale
pers_batchIssCard_endPersNo=ID personale finale
pers_batchIssCard_issCardNum=Numero di carte emesse
pers_batchIssCard_notIssCardNum=Numero di persone senza carte emesse
pers_batchIssCard_generateList=Genera elenco
pers_batchIssCard_startRead=Avvia lettura
pers_batchIssCard_swipCard=Posizione della carta da leggere
pers_batchIssCard_sendCard=Dispositivo
pers_batchIssCard_dispenCardIss=Lettore USB
pers_batchIssCard_usbEncoder=Codificatore USB
pers_batchIssCard_note=L’ID personale supporta soltanto valori di input e mostra esclusivamente le persone senza carte emesse (max. 300).
pers_batchIssCard_startPinEmpty=Il campo ID personale iniziale non può essere vuoto.
pers_batchIssCard_endPinEmpty=Il campo ID personale finale non può essere vuoto.
pers_batchIssCard_startPinLargeThanEndPin=L’ID personale iniziale non può essere superiore all’ID personale finale.
pers_batchIssCard_numberParagraphNoPerson=La persona senza carte emesse non è presente nell’intervallo compreso fra ID iniziale e finale.
pers_batchIssCard_inputCardNum=Immetti numero carta
pers_batchIssCard_cardLetter=La carta non supporta le lettere.
pers_batchIssCard_cardNoTooLong=Numero carta troppo lungo.
pers_batchIssCard_issueWay=Metodo registrazione carta
pers_batchIssCard_noPersonList=Elenco personale non ancora generato.
pers_batchIssCard_startReadCard=Avvia lettura carta
pers_batchIssCard_swipePosition=Posizione lettura
pers_batchIssCard_chooseSwipePosition=Scegliere la posizione di lettura.
pers_batchIssCard_readCardTip=Il dispositivo legge la carta non registrata solo se è stato selezionato un lettore come metodo di emissione.
pers_batchIssCard_notIssCardNo=Numero di carte non emesse
pers_batchIssCard_totalNumOfCards=Numero totale di carte
pers_batchIssCard_acms=Emissione della carta ACMS
pers_lossCard_entity=Segnalazione carta smarrita
pers_lossCard_lost=La carta è stata segnalata e non è possibile ripetere l’operazione.
pers_losscard_note2=Dopo la scrittura della carta gestionale, passarla nel lettore dell’ascensore affinché l’effetto sia annullato nel dispositivo.
pers_revertCard_entity=Riattiva carta smarrita
pers_revertCard_setReport=Segnalare prima lo smarrimento della carta.
pers_revertcard_note2=Dopo la scrittura della carta gestionale, passarla nel lettore dell’ascensore affinché sia possibile utilizzarla nuovamente.
pers_issueCard_success=Carta emessa.
pers_issueCard_error=Carta non emessa.
pers_cardData_error=Eccezione lettura formato dati carta.
pers_analysis_error=Eccezione analisi dati carta.
pers_cardOperation_error=Eccezione funzionamento carta.
pers_cardPacket_error=Eccezione pacchetto comandi funzionamento carta.
pers_card_write=Scrivi carta
pers_card_init=Inizializza carta
pers_card_loss=Carta smarrita
pers_card_revert=Ripristina carta
pers_card_writeMgr=Scrivi carta gestionale
pers_initCard_tip=Dopo l’inizializzazione, la carta risulta vuota.
pers_initCard_prepare=Carta pronta per l’inizializzazione...
pers_initCard_process=Inizializzazione della carta in corso...
pers_initCard_success=Carta inizializzata
pers_mgrCard_prepare=Preparazione alla scrittura dei dati della carta gestionale...
pers_mgrCard_process=Scrittura dei dati della carta gestionale in corso...
pers_mgrCard_success=Scrittura della carta gestionale riuscita
pers_userCard_prepare=Preparazione alla scrittura di una carta utente...
pers_userCard_process=Scrittura dei dati della carta utente in corso...
pers_userCard_success=Scrittura della carta utente riuscita
pers_userCard_tip=Impostare l’ora iniziale e l’ora finale nella pagina di modifica della persona, quindi scrivere l’operazione della carta.
pers_userCard_tip2=Dati autorizzazioni non presenti; impossibile scrivere le carte.
pers_userCard_tip3=Il gruppo autorità ha associato al dispositivo più di due unità; perdita di dati.
pers_writeCard_tip=Verificare che il codificatore sia stato collegato e che il driver sia stato installato, quindi posizionare la carta sul codificatore.
pers_writeMgrCard_tip=Il numero di carte smarrite e di carte ripristinate non può essere superiore a 18
pers_writeMgrCard_tip2=Numero di carte smarrite e di carte ripristinate:
pers_card_writeToMgr=Scrittura sulla carta gestionale avvenuta
pers_card_hex=Visualizzazione formato carta
pers_card_decimal=Decimale
pers_card_Hexadecimal=Esadecimale
pers_card_IssuedCommandFail=Errore comando inviato:
pers_card_multiCard=Più carte
pers_card_deputyCard=Carta secondaria
pers_card_deputyCardValid=Immettere prima la carta principale.
pers_card_writePinFormat=Il sistema può associare alla carta soltanto ID personali senza lettere.
pers_card_notMoreThanSixteen=Il numero di carte secondarie non può essere superiore a 16.
pers_card_notDelAll=Impossibile rimuovere tutte le carte secondarie.
pers_card_maxCard=Il numero di carta non può superare {0}.
pers_card_posUseCardNo=La carta principale della persona è utilizzata dal modulo consumatore. Andare al modulo consumatore per eseguire l’operazione di ritiro della carta.
pers_card_delFirst=Si prega di cancellare il numero della carta emessa prima di emettere la carta!
pers_card_disablePersonWarn=Il numero di tessera selezionato contiene personale disabili e non può essere utilizzato!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Codice sito
pers_wiegandFmt_wiegandMode=Modalità
pers_wiegandFmt_wiegandModeOne=Modalità 1
pers_wiegandFmt_wiegandModeTwo=Modalità 2
pers_wiegandFmt_isDefaultFmt=Automatica
pers_wgFmt_entity=Formato Wiegand
pers_wgFmt_in=Formato ingresso Wiegand
pers_wgFmt_out=Formato uscita Wiegand
pers_wgFmt_inType=Tipo ingresso Wiegand
pers_wgFmt_outType=Tipo uscita Wiegand
pers_wgFmt_wg=Formato Wiegand
pers_wgFmt_totalBit=Bit totali
pers_wgFmt_oddPch=Controllo parità dispari(o)
pers_wgFmt_evenPck=Controllo parità pari(e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=Codice struttura(f)
pers_wgFmt_siteCode=Codice sito(s)
pers_wgFmt_manufactoryCode=Codice produttore(m)
pers_wgFmt_firstParity=Primo controllo parità(p)
pers_wgFmt_secondParity=Secondo controllo parità(p)
pers_wgFmt_cardFmt=Formato controllo carta
pers_wgFmt_parityFmt=Formato controllo parità
pers_wgFmt_startBit=Bit iniziale
pers_wgFmt_test=Test formato carta
pers_wgFmt_error=Errore formato carta.
pers_wgFmt_verify1=I bit totali non devono essere più di 80.
pers_wgFmt_verify2=La lunghezza del formato di controllo carta deve essere pari al numero totale di bit.
pers_wgFmt_verify3=La lunghezza del formato parità deve essere pari al numero totale di cifre.
pers_wgFmt_verify4=I dati inizializzati non possono essere eliminati.
pers_wgFmt_verify5=Formato carta in uso; impossibile eliminarlo.
pers_wgFmt_verify6=Il primo bit di parità non può essere superiore al relativo numero totale.
pers_wgFmt_verify7=Il secondo bit di parità non può essere superiore al relativo numero totale.
pers_wgFmt_verify8=Il formato della lunghezza massima e iniziale è errato.
pers_wgFmt_verify9=La lunghezza del formato controllo carta non può essere superiore al relativo numero totale.
pers_wgFmt_verify10=La funzione di verifica dei caratteri della carta non può essere incrociata.
pers_wgFmt_verify11=Il codice del sito non rientra nell’intervallo impostato.
pers_wgFmt_verify=Controlla
pers_wgFmt_unverify=Non controllare
pers_wgFmt_atLeastDefaultFmt=Mantenere almeno un formato carta con corrispondenza automatica.
pers_wgFmt_defaultFmtError1=Sono presenti altri formati carta con gli stessi bit del numero di carta; non è possibile impostarli per la corrispondenza automatica.
pers_wgFmt_defaultFmtError2=Presenza dello stesso formato carta per la corrispondenza automatica; l’operazione non andrà a buon fine.
pers_wgFmt_cardFormats=Formati carta
pers_wgFmt_cardFormatTesting=Test formati carta
pers_wgFmt_checkIsUsed=Il formato della carta è utilizzato in {0} e non può essere cancellato!
pers_wgFmt_supportDigitsNumber=Inserisci il numero di cifre supportate dal dispositivo
#选人控件
pers_widget_selectPerson=Seleziona personale
pers_widget_searchType1=Interroga
pers_widget_searchType2=Reparto
pers_widget_deptHint=Nota: Importazione di tutto il personale dei reparti selezionati
pers_widget_noPerson=Nessun membro del personale selezionato.
pers_widget_noDept=Selezionare un reparto.
pers_widget_noDeptPerson=Nessuna persona nel reparto selezionato; riselezionare.
#人员属性
pers_person_carPlate=Targa
pers_person_socialSecurity=Numero previdenza sociale
pers_person_msg4=La lunghezza massima è 20.
pers_person_msg5=Il nome non può contenere più di 50 caratteri.
pers_person_type=Tipo di persona
pers_person_reseCode=Password di accesso autogestito
pers_person_IsSendMail=Notifica e-mail
pers_person_inactive=Inattivo
pers_person_active=Attivo
pers_person_employee=Dipendente
pers_person_isSendMailMsg=Per usare la funzione [Notifica eventi], immettere prima l’indirizzo e-mail.
pers_person_createTime=Ora creazione
pers_person_pinFirstValid=Il primo carattere del numero personale non può essere 8 o 9.
pers_person_attrValueValid=Il valore del campo non può essere ripetuto.
pers_person_attrValueDelimiterValid=Il separatore deve trovarsi al centro.
pers_person_isSendSMS=SMS notification
pers_person_building=Nome dell'edificio
pers_person_unitName=Nome unità
pers_person_roomNo=Numero della stanza
#动态属性
pers_attr_emp_type=Tipo di dipendente
pers_attr_street=Via
pers_attr_nation=Nazione
pers_attr_office_address=Indirizzo ufficio
pers_attr_postcode=Codice postale
pers_attr_office_phone=Telefono ufficio
pers_attr_home_phone=Telefono abitazione
pers_attr_job_title=Qualifica
pers_attr_birthplace=Luogo di nascita
pers_attr_polit_status=Status politico
pers_attr_country=Paese
pers_attr_home_address=Indirizzo abitazione
pers_attr_hire_type=Tipo assunzione
pers_attr_inContract=Lavoratore a contratto
pers_attr_outContract=Lavoratore non a contratto
#属性自定义
pers_attribute_attrName=Visualizza nome
pers_attribute_attrValue=Valore attributo
pers_attribute_controlType=Tipo ingresso
pers_attribute_positionX=Riga
pers_attribute_positionY=Colonna
pers_attribute_showTable=Visualizza nell’elenco di persone
pers_attrDefini_deletemsg=Questa proprietà è stata usata. Eliminarla?
pers_attrDefini_reserved=Nome campo riservato al sistema.
pers_attrDefini_msg1=La lunghezza massima è 30.
pers_attrDefini_msg2=Le righe della colonna sono già presenti; scegliere una posizione diversa.
pers_attrDefini_attrValue_split=Utilizzo di un delimitatore \' ; \'.
pers_attrDefini_attrName=Nome attributo
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=ID attributo
pers_attrDefini_select=Elenco a discesa
pers_attrDefini_check=Scelta multipla
pers_attrDefini_radio=Scelta singola
pers_attrDefini_text=Testo
pers_attrDefini_maxCol=Non più di 2 colonne.
pers_attrDefini_maxLimit=Numero massimo di attributi personalizzati raggiunto.
pers_attrDefini_modControlType=La modifica del tipo di ingresso comporta la cancellazione dei dati correnti del campo per tutto il personale incluso nel sistema. Continuare?
#leavePerson
pers_leavePerson_reinstated=Reintegrazione
#opExample
pers_example_newRecode=Acquisizione di nuovi record
pers_example_allRecode=Accedi a tutti i record
pers_custField_StatisticalType=Tipo statistico
#人员参数修改
pers_param_isAudit=Abilita verifica automatica personale a tempo determinato
pers_param_donotChangePin=L’ID personale esistente contiene una o più lettere; impossibile disabilitare la funzionalità [Supporto lettere].
pers_param_hexChangeWarn=Il sistema corrente contiene già numeri di carte; impossibile modificare la modalità di visualizzazione del formato carta.
pers_param_cardsChangeWarn=Alcune persone nel sistema hanno più di una carta; impossibile disabilitare la funzione [Più carte per persona].
pers_param_maxPinLength=La lunghezza del nuovo ID personale non può essere inferiore a quella dell’ID personale già presente nel sistema.
pers_param_pinBeyondDevLength=La lunghezza massima dell’ID personale supportata dal dispositivo nel sistema è pari a {0}; inserire un valore intero minore di {1}.
pers_param_cardBeyondDevLength=La lunghezza massima del numero di carta supportata dal dispositivo nel sistema è pari a {0}; inserire un valore intero minore di {1}.
pers_param_checkIsExistNoAudit=Nel sistema corrente sono presenti candidati non ancora esaminati e non è possibile passare all’esame automatico.
pers_param_noSupportPinLetter=Nel sistema sono presenti dispositivi che non supportano gli ID personale contenenti lettere; non è possibile abilitare la funzione [Supporto lettere].
pers_param_changePinLettersTip=Il numero personale supporta l’incremento automatico; non è possibile modificare le modalità relative al numero del personale
pers_param_changePinIncrementTip=Il numero persona supporta l’inclusione delle lettere; non è possibile modificare la modalità relativa al numero del personale
pers_param_qrCode=Codice QR organizzazione
pers_param_employeeRegistrar=Abilita registrazione dipendente su cloud
pers_param_downloadQRCodePic=Scarica immagine codice QR
pers_param_qrCodeUrl=URL codice QR
pers_param_qrCodeUrlCreate=Registrazione fai da te
pers_param_qrCodeUrlHref=Indirizzo server:Porta
pers_param_pinSetWarn=Ci sono già personale nel sistema attuale e la modalità del numero del personale non può essere modificata.
pers_param_selfRegistration=Abilita la registrazione automatica
pers_param_infoProtection=Protezione delle informazioni personali sensibili
pers_param_infoProtectionWarnMsg=Dopo aver abilitato l'opzione di protezione della sicurezza delle informazioni personali sensibili, i dati personali sensibili coinvolti in questo modulo verranno desensibilizzati o oscurati, inclusi ma non limitati a nomi, numeri di carte, numeri ID, foto, ecc.
pers_param_templateServer=Server di estrazione del modello facciale
pers_param_enableFacialTemplate=Abilita estrazione del modello facciale
pers_param_templateServerAddr=Indirizzo del server di estrazione del modello facciale
pers_param_templateServerWarnInfo=Quando l'estrazione del modello facciale è abilitata, quando il server di estrazione del modello facciale è online e la verifica dell'utente è passata, il personale estrarrà di default i modelli facciali durante il confronto delle foto; Quando il server di estrazione del modello facciale è in modalità offline, non estrarre i modelli facciali!
pers_param_templateServerWarnInfo1=Quando si abilita l'estrazione del modello facciale, un dispositivo che supporta l'estrazione del modello facciale deve essere collegato!
pers_param_templateServerOffline=Il server di estrazione dei modelli facciali è offline e non può estrarre i modelli facciali! Vuoi continuare?
pers_param_faceServer=Servizio di comparazione del backend facciale
pers_param_enableFaceVerify=Abilita il confronto del backend facciale
pers_param_faceServerAddr=Indirizzo del servizio di confronto backend faccia
pers_param_faceServerSecret=Chiave del servizio di confronto backend faccia
#国籍
pers_person_nationality=Nazionalità
pers_nationality_angola=Angola
pers_nationality_afghanistan=Afghanistan
pers_nationality_albania=Albania
pers_nationality_algeria=Algeria
pers_nationality_america=Stati Uniti d’America
pers_nationality_andorra=Andorra
pers_nationality_anguilla=Anguilla
pers_nationality_antAndBar=Antigua e Barbuda
pers_nationality_argentina=Argentina
pers_nationality_armenia=Armenia
pers_nationality_ascension=Ascension
pers_nationality_australia=Australia
pers_nationality_austria=Austria
pers_nationality_azerbaijan=Azerbaijan
pers_nationality_bahamas=Bahamas
pers_nationality_bahrain=Bahrein
pers_nationality_bangladesh=Bangladesh
pers_nationality_barbados=Barbados
pers_nationality_belarus=Bielorussia
pers_nationality_belgium=Belgio
pers_nationality_belize=Belize
pers_nationality_benin=Benin
pers_nationality_bermudaIs=Bermuda
pers_nationality_bolivia=Bolivia
pers_nationality_botswana=Botswana
pers_nationality_brazil=Brasile
pers_nationality_brunei=Brunei
pers_nationality_bulgaria=Bulgaria
pers_nationality_burkinaFaso=Burkina Faso
pers_nationality_burma=Burma
pers_nationality_burundi=Burundi
pers_nationality_cameroon=Camerun
pers_nationality_canada=Canada
pers_nationality_caymanIs=Isole Cayman
pers_nationality_cenAfrRepub=Repubblica Centrafricana
pers_nationality_chad=Ciad
pers_nationality_chile=Cile
pers_nationality_china=Cina
pers_nationality_colombia=Colombia
pers_nationality_congo=Repubblica del Congo
pers_nationality_cookIs=Isole Cook
pers_nationality_costaRica=Costa Rica
pers_nationality_cuba=Cuba
pers_nationality_cyprus=Cipro
pers_nationality_czechRep=Repubblica Ceca
pers_nationality_denmark=Danimarca
pers_nationality_djibouti=Gibuti
pers_nationality_dominicaRep=Repubblica Dominicana
pers_nationality_ecuador=Ecuador
pers_nationality_egypt=Egitto
pers_nationality_eISalvador=El Salvador
pers_nationality_england=Regno Unito
pers_nationality_estonia=Estonia
pers_nationality_ethiopia=Etiopia
pers_nationality_fiji=Figi
pers_nationality_finland=Finlandia
pers_nationality_france=Francia
pers_nationality_freGui=Guyana francese
pers_nationality_gabon=Gabon
pers_nationality_gambia=Gambia
pers_nationality_georgia=Georgia
pers_nationality_germany=Germania
pers_nationality_ghana=Ghana
pers_nationality_gibraltarm=Gibilterra
pers_nationality_greece=Grecia
pers_nationality_grenada=Grenada
pers_nationality_guam=Guam
pers_nationality_guatemala=Guatemala
pers_nationality_guinea=Guinea
pers_nationality_guyana=Guyana
pers_nationality_haiti=Haiti
pers_nationality_honduras=Honduras
pers_nationality_hungary=Ungheria
pers_nationality_iceland=Islanda
pers_nationality_india=India
pers_nationality_indonesia=Indonesia
pers_nationality_iran=Iran
pers_nationality_iraq=Iraq
pers_nationality_ireland=Irlanda
pers_nationality_israel=Israele
pers_nationality_italy=Italia
pers_nationality_ivoryCoast=Costa d’Avorio
pers_nationality_jamaica=Giamaica
pers_nationality_japan=Giappone
pers_nationality_jordan=Giordania
pers_nationality_kenya=Kenya
pers_nationality_korea=Corea
pers_nationality_kuwait=Kuwait
pers_nationality_kyrgyzstan=Kirghizistan
pers_nationality_laos=Laos
pers_nationality_latvia=Lettonia
pers_nationality_lebanon=Libano
pers_nationality_lesotho=Lesotho
pers_nationality_liberia=Liberia
pers_nationality_libya=Libia
pers_nationality_liechtenstein=Liechtenstein
pers_nationality_lithuania=Lituania
pers_nationality_luxembourg=Lussemburgo
pers_nationality_madagascar=Madagascar
pers_nationality_malawi=Malawi
pers_nationality_malaysia=Malesia
pers_nationality_maldives=Maldive
pers_nationality_mali=Mali
pers_nationality_malta=Malta
pers_nationality_marianaIs=Isole Marianne
pers_nationality_martinique=Martinica
pers_nationality_mauritius=Mauritania
pers_nationality_mexico=Messico
pers_nationality_moldova=Moldavia
pers_nationality_monaco=Monaco
pers_nationality_montseIs=Montserrat
pers_nationality_morocco=Marocco
pers_nationality_mozambique=Mozambico
pers_nationality_namibia=Namibia
pers_nationality_nauru=Nauru
pers_nationality_nepal=Nepal
pers_nationality_netAnti=Antille Olandesi
pers_nationality_netherlands=Paesi Bassi
pers_nationality_newZealand=Nuova Zelanda
pers_nationality_nicaragua=Nicaragua
pers_nationality_niger=Niger
pers_nationality_nigeria=Nigeria
pers_nationality_norKorea=Corea del Nord
pers_nationality_norway=Norvegia
pers_nationality_oman=Oman
pers_nationality_pakistan=Pakistan
pers_nationality_panama=Panama
pers_nationality_papNewCui=Papua Nuova Guinea
pers_nationality_paraguay=Paraguay
pers_nationality_peru=Perù
pers_nationality_philippines=Filippine
pers_nationality_poland=Polonia
pers_nationality_frenPolyne=Polinesia Francese
pers_nationality_portugal=Portogallo
pers_nationality_puerRico=Portorico
pers_nationality_qatar=Qatar
pers_nationality_reunion=Riunione
pers_nationality_romania=Romania
pers_nationality_russia=Russia
pers_nationality_saiLueia=Santa Lucia
pers_nationality_saintVinc=Saint Vincent
pers_nationality_samoa_eastern=Samoa orientali
pers_nationality_samoa_western=Samoa
pers_nationality_sanMarino=San Marino
pers_nationality_saoAndPrinc=São Tomé e Príncipe
pers_nationality_sauArabia=Arabia Saudita
pers_nationality_senegal=Senegal
pers_nationality_seychelles=Seychelles
pers_nationality_sieLeone=Sierra Leone
pers_nationality_singapore=Singapore
pers_nationality_slovakia=Slovacchia
pers_nationality_slovenia=Slovenia
pers_nationality_solomonIs=Isole Salomone
pers_nationality_somali=Somalia
pers_nationality_souAfrica=Sudafrica
pers_nationality_spain=Spagna
pers_nationality_sriLanka=Sri Lanka
pers_nationality_sudan=Sudan
pers_nationality_suriname=Suriname
pers_nationality_swaziland=Swaziland
pers_nationality_sweden=Svezia
pers_nationality_switzerland=Svizzera
pers_nationality_syria=Siria
pers_nationality_tajikstan=Tagikistan
pers_nationality_tanzania=Tanzania
pers_nationality_thailand=Thailandia
pers_nationality_togo=Togo
pers_nationality_tonga=Tonga
pers_nationality_triAndToba=Trinidad e Tobago
pers_nationality_tunisia=Tunisia
pers_nationality_turkey=Turchia
pers_nationality_turkmenistan=Turkmenistan
pers_nationality_uganda=Uganda
pers_nationality_ukraine=Ucraina
pers_nationality_uniArabEmira=Emirati Arabi Uniti
pers_nationality_uruguay=Uruguay
pers_nationality_uzbekistan=Uzbekistan
pers_nationality_venezuela=Venezuela
pers_nationality_vietnam=Vietnam
pers_nationality_yemen=Yemen
pers_nationality_serbia=Serbia
pers_nationality_zimbabwe=Zimbabwe
pers_nationality_zambia=Zambia
pers_nationality_aruba=Aruba
pers_nationality_bhutan=Bhutan
pers_nationality_bosnia_herzegovina=Bosnia ed Erzegovina
pers_nationality_cambodia=Cambogia
pers_nationality_congoD=Repubblica Democratica del Congo
pers_nationality_comoros=Unione delle Comore
pers_nationality_capeVerde=Repubblica di Capo Verde
pers_nationality_croatia=Croazia
pers_nationality_dominica=Dominica
pers_nationality_eritrea=Eritrea
pers_nationality_micronesia=Micronesia
pers_nationalit_guineaBissau=Guinea-Bissau
pers_nationalit_equatorialGuinea=Guinea Equatoriale
pers_nationalit_hongkong=Hong Kong
pers_nationalit_virginIslands=Isole Vergini americane
pers_nationalit_britishVirginIslands=Isole Vergini britanniche
pers_nationalit_kiribati=Kiribati
pers_nationalit_mongolia=Mongolia
pers_nationalit_marshall=Isole Marshall
pers_nationalit_macedonia=Macedonia
pers_nationalit_montenegro=Montenegro
pers_nationalit_mauritania=Mauritania
pers_nationalit_palestine=Palestina
pers_nationalit_palau=Palau
pers_nationalit_rwanda=Ruanda
pers_nationalit_saintKittsNevis=Saint Kitts e Nevis
pers_nationalit_timorLeste=Timor-Leste
pers_nationalit_taiwan=Taiwan
pers_nationalit_tuvalu=Tuvalu
pers_nationalit_vanuatu=Vanuatu
#制卡
pers_person_cardprint=Stampa biglietto
pers_cardTemplate_tempSelect=Modello di carta
pers_cardTemplate_printerSelect=Stampante
pers_cardTemplate_front=Anteriore
pers_cardTemplate_opposite=Indietro
pers_cardTemplate_entryDate=Data di assunzione
pers_cardTemplate_photo=Foto
pers_cardTemplate_uploadFail=Caricamento immagine fallito!
pers_cardTemplate_jpgFormat=Supporta solo il caricamento di immagini in formato JPG!
pers_cardTemplate_printStatus=Stato di stampa
pers_cardTemplate_waiting=In attesa
pers_cardTemplate_printing=Stampa
pers_cardTemplate_printOption=Opzione di stampa
pers_cardTemplate_duplexPrint=Stampa duplex
pers_cardTemplate_frontOnly=Stampa solo davanti
#app
pers_app_delPers=Non vi sono persone sul server che si desidera eliminare
pers_app_deptIsNull=Impossibile trovare il numero o il nome corrispondente al reparto
pers_app_personNull=La persona non esiste
pers_app_pinExist=ID personale già presente
pers_app_dateError=Il formato della data è errato; utilizzare il formato corretto:2016-08-08
#api
pers_api_selectPhotoInvalid=The photo is invalid, please re-upload
pers_api_dateError=The date format is incorrect
pers_api_personNotExist=Person does not exist
pers_api_cardsPersSupport=The system does not open card; card is invalid.
pers_api_department_codeOrNameNotNull=Il codice o il nome del dipartimento non può essere vuoto
pers_api_deptSortNoIsNull=L'ordinamento del reparto non può essere vuoto!
pers_api_deptSortNoError=Il valore dell'ordinamento del reparto deve essere compreso tra 1-999999!
pers_api_dataLimit=Il numero attuale di operazioni è {0}, superando il limite di {1}. Operate in lotti!
pers_api_cardTypeError=Errore del tipo di scheda
#人员生物模板API
pers_api_fingerprintExisted=The fingerprint of the person already exists
pers_api_validtypeIncorrect=This validtype attribute value is incorrect
pers_api_dataNotExist=TemplateNo not exist
pers_api_templateNoRang=Please enter the correct templateNo value in the range 0-9
pers_api_templateIsNull=template non può essere vuoto!
pers_api_versionIsNumber=La versione può inserire solo numeri!
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=Pin can only be a number!
#biotime
pers_h5_personAvatarNotNull=The avatar is empty
pers_h5_personMobileRepeat=Mobile number already exists
pers_h5_personEmailRepeat=The mailbox already exists
pers_h5_pwdIsRepetition=New and old password repetition
pers_h5_personIdNull=Staff id is empty
pers_h5_pinOrEmailIsNull=Please fill in the number and email address
pers_emailTitle_resetPassword=change Password
pers_emailContent_resetPassword=The link is valid for 24 hours, copy the link to the browser to change the password:
pers_h5_tokenIsNull=Voucher is empty
pers_h5_tokenError=Voucher error
pers_h5_oldPwdIsError=Old password is incorrectly filled in
pers_api_resetPasswordSuccess=password has been updated
pers_api_resetPasswordFail=Password change failed
pers_api_forgetPassword=forget password
pers_api_confirmSubmit=confirm submission
pers_api_confirmPwdCaution=Click [OK] to confirm the new password.
pers_api_pwdRule=The password must contain at least one symbol or number and be at least 8-12 characters long
pers_h5_personPinFormatNumber=Personnel number can only consist of numbers or letters
pers_h5_persEmailNoExist=Mailbox filling error
pers_h5_pageNull=Paging parameter error
pers_h5_personPinNotStartWithZero=Personnel number cannot start with 0
pers_h5_personPinTooLong=Personnel number is too long
pers_h5_personPinInValid=This number is already in use
pers_h5_imgSizeError=Please upload an image that is no larger than 10M!
pers_h5_confirmAndContinue=Conferma e continua
# 人 脸 抠图 不 及格 错误
pers_face_poorResolution=Risoluzione dell'immagine inferiore a 80000 pixel
pers_face_noFace=Nessun volto rilevato
pers_face_manyFace=Più volti rilevati
pers_face_smallFace=Il rapporto della faccia è troppo piccolo
pers_face_notColor=L'immagine non è a colori
pers_face_seriousBlur=L'immagine è sfocata
pers_face_intensivelyLight=L'immagine è molto esposta
pers_face_badIllumination=L'immagine è troppo scura
pers_face_highNoise=Immagini con rumore elevato
pers_face_highStretch=Faccia troppo stirata
pers_face_covered=La faccia è coperta
pers_face_smileOpenMouth=Sorriso eccessivo
pers_face_largeAngle=L'angolo di deflessione della faccia è troppo grande
pers_face_criticalIllumination=La luminosità dell'immagine è critica
pers_face_criticalLargeAngle=Angolo di deflessione della faccia critica
pers_face_validFailMsg=Rilevamento volti non riuscito a causa di:
pers_face_failType=Tipo di errore di ritaglio della faccia
pers_face_photoFormatError=Il formato della foto non è corretto, carica un file in formato JPG / PNG.
pers_face_notUpdateMsg=Impossibile generare l'immagine del viso, non aggiornare l'immagine del viso.
# 健康 申报
pers_health_enable=Abilita la dichiarazione di informazioni sanitarie
pers_health_attrExposure=Qualsiasi esposizione a casi sospetti
pers_health_attrSymptom=Qualsiasi sintomo negli ultimi 14 giorni
pers_health_attrVisitCity=Città visitata negli ultimi 14 giorni
pers_health_attrRemarks=Osservazioni sulla salute
pers_health_symptomCough=Tosse
pers_health_symptomFever=Febbre
pers_health_symptomPolypena=Problemi respiratori
pers_health_declaration=Dichiarazione di integrità
pers_health_aggrement=Ho concordato con le persone che non compilano le informazioni come richiesto non sarà consentito l'accesso e i visitatori che non lo hanno segnalato in modo veritiero non possono continuare la loro visita e devono assumersi le corrispondenti responsabilità legali.
pers_health_visitCity_notEmpty=La città visitata non può essere vuota!
pers_health_notAgree=Controlla l'accordo per continuare.
#人员名单库
pers_personnal_list_manager=Gestore della lista
pers_personnal_list=Elenco personale
pers_personnal_list_scheme=Modalità dominio
pers_personnal_list_name=Nome elenco personale
pers_personnal_list_group_str_id=Elenco ID gruppo
pers_personnal_list_personCount=Numero di persone
pers_personnal_list_tag=Definito dall'utente
pers_personnal_list_type=Tipo di elenco personale
pers_personnallist_addPerson_repo=Aggiungi una persona al repository
pers_personnallist_sendPersonnallist=InviaPersonnallist=InviaPersonnallist
pers_personnallist_sendPerson=InviaPersona
pers_personnallist_notDel_existPerson=La libreria elenco contiene persone e non può essere eliminata
pers_personnallist_peopleInRoster=Ci sono ancora persone nella libreria della lista
pers_personnallist_associationNotExist=L'associazione tra il dispositivo principale e la libreria della lista non esiste
pers_personnal_list_person=Persona dell'elenco personale
pers_personnal_list_dev=Elenco permessi libreria library
pers_personnal_list_addDev=Aggiungi dispositivo
pers_personnal_list_name_isExist=Il nome esiste già
pers_personnal_bannedList=Libreria lista vietata
pers_personnal_allowList=Consenti libreria elenco
pers_personnal_redList=Libreria Lista Rossa
pers_personnal_attGroup=Gruppo di partecipazione
pers_personal_passList=Passlist
pers_personnal_banList=Lista proibita
pers_personnal_visPassList=Elenco pass visitatori
pers_personnal_visBanList=Lista dei visitatori vietati
pers_personnal_databaseHasBeenDistributed=La libreria elenco selezionata è stata distribuita e non può essere eliminata
pers_personnel_sendError_dueTo=Mancata consegna Motivo della mancata consegna:
pers_personnel_sendError_reson={0} esiste nell'elenco vietato, elimina e aggiungi
#比对照片-样片示例
pers_examplePic_Tip=Dovrebbe soddisfare i seguenti requisiti:
pers_examplePic_Tip1=1. il colore di sfondo è bianco puro e il personale indossa abiti scuri;
pers_examplePic_Tip2=2. Le foto elettroniche devono essere nei formati di file JPG, PNG, JPEG, Si consiglia un intervallo di pixel: 480×640 < pixel < 1080×1920;
pers_examplePic_Tip3=3. I ritratti nelle foto elettroniche dovrebbero aprire gli occhi e guardare dritto davanti a sé e assicurarsi che le pupille siano chiaramente visibili;
pers_examplePic_Tip4=4. La persona raffigurata nella foto elettronica dovrebbe avere un aspetto espressivo neutro. È possibile sorridere, ma non è consigliabile mostrare i denti.
pers_examplePic_Tip5=5. Il ritratto nella foto elettronica deve essere nitido, con colori naturali, strati ricchi e nessuna distorsione evidente. Nessuna ombra, luce o riflesso sui volti o sullo sfondo del ritratto; il contrasto e la luminosità sono appropriati.
pers_examplePic_description=Esempio corretto
pers_examplePic_error=Esempio di errore:
pers_examplePic_error1=Espressione esagerata (sorriso eccessivo)
pers_examplePic_error2=La luce è troppo scura
pers_examplePic_error3=La faccia è troppo piccola (la risoluzione è troppo piccola)
pers_applogin_enabled=Abilita l'accesso all'app
pers_applogin_disable=Disabilita l'accesso all'app
pers_applogin_status=Stato abilita l'accesso all'app