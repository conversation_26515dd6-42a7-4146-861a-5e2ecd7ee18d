#pers common.
#here other module can also use the label from pers.
pers_module=Personnel
pers_common_addPerson=Add Personnel
pers_common_delPerson=Delete Personnel
pers_common_personCount=Personnel Quantity
pers_common_browsePerson=Browse Personnel
#左侧菜单
pers_person_manager=Personnel
pers_person=Person
pers_department=Department
pers_leave=Dismissed Personnel
pers_tempPerson=Pending Review
pers_attribute=Custom Attributes
pers_card_manager=Card Management
pers_card=Card
pers_card_issue=Issued Card Record
pers_wiegandFmt=Wiegand Format
pers_position=Position
#人员
pers_person_female=Female
pers_person_male=Male
pers_person_pin=Personnel ID
pers_person_departmentChange=Adjust Department
pers_personDepartment_changeLevel=Switch to department level
pers_person_gender=Gender
pers_person_detailInfo=Personnel Detail
pers_person_accSet=Access Control
pers_person_accSetting=Access Control Setting
pers_person_attSet=Time Attendance
pers_person_eleSet=Elevator Control
pers_person_eleSetting=Elevator Control Setting
pers_person_parkSet=Plate Register
pers_person_pidSet=Personal Certificate
pers_person_insSet=FaceKiosk
pers_person_aiSet=Face Intellect
pers_person_payrollSet=Salary Setting
pers_person_psgSet=Passage Setting
pers_person_lockerSet=Locker Setting
pers_person_sisSet=Security Setting
pers_person_vdbSet=Video Intercom Setting
pers_person_firstName=First Name
pers_person_lastName=Last Name
pers_person_name=First Name
pers_person_wholeName=Name
pers_person_fullName=First Middle Last
pers_person_cardNum=Number of Cards Held
pers_person_deptNum=Number of departments involved
pers_person_dataCount=Statistics
pers_person_regFinger=Fingerprint
pers_person_reg=Register
pers_person_password=Device Verification Password
pers_person_personDate=Employment Date
pers_person_birthday=Birthday
pers_person_mobilePhone=Mobile Phone
pers_person_personDevAuth=Person and Device Permissions
pers_person_email=Email
pers_person_browse=Browse
pers_person_authGroup=Access Levels
pers_person_setEffective=Set Effective Time
pers_person_attArea=Attendance Area
pers_person_isAtt=Attendance Calculation
pers_person_officialStaff=Official Staff
pers_person_probationStaff=Probation Staff
pers_person_identity_category=Personnel Type
pers_person_devOpAuth=Device Operation Role
pers_person_msg1=The actual card number and the site code should be filled in at the same time!
pers_person_msg2=Please enter a 3-4 digit.
pers_person_msg3=Format error!
pers_person_imgPixel=(Optimal Size 120*140).
pers_person_cardLengthDigit=Please enter the number.
pers_person_cardLengthHexadecimal=Please enter numbers or abcdef letters!
pers_person_to=To
pers_person_templateCount=Fingerprint Quantity
pers_person_biotemplateCount=Biological Template Quantity
pers_person_regFace=Face
pers_person_regVein=Finger Vein
pers_person_faceTemplateCount=Face Quantity
pers_person_VeinTemplateCount=Finger Vein Quantity
pers_person_palmTemplateCount=Palm Quantity
pers_person_cropFace=Face Picture
pers_person_cropFaceCount=Face Picture Quantity
pers_person_faceBiodataCount=Visible Face Quantity
pers_person_irisCount=Iris Count
pers_person_batchToDept=New Department
pers_person_changeReason=Transfer Reason
pers_person_selectedPerson=Selected Person
pers_person_duressPwdError=Repeated Password
pers_person_completeDelPerson=Delete
pers_person_recover=Recover
pers_person_nameNoComma=Must not include the comma.
pers_person_firstNameNotEmpty=First name must not be empty.
pers_person_lastNameNotEmpty=Last name must not be empty.
pers_person_mobilePhoneValidate=Please enter a valid mobile phone number.
pers_person_phoneNumberValidate=Please enter a valid phone number.
pers_person_postcodeValidate=Please enter a valid zip code.
pers_person_idCardValidate=Please enter a valid idCard number.
pers_person_pwdOnlyLetterNum=Password can only contain letters or numbers.
pers_person_certNumOnlyLetterNum=Certificate Number can only contain letters or numbers.
pers_person_oldDeptEqualsNewDept=The new department you have adjusted must not be the same as the original department.
pers_person_disabled=Access Disabled
pers_person_emailError=Please enter a valid email address.
pers_person_driverPrompt=Please install the device driver ! Click OK to download the driver.
pers_person_readIDCardFailed=Swipe failure!
pers_person_cardPrompt=Keep ID card into the region ...
pers_person_iDCardReadOpenFailed=No ID card reader is detected!
pers_person_iDCardNotFound=ID card not found, please try again!
pers_person_nameValid=Support Chinese, English, numbers, \'-\', \'_\'.
pers_person_nameValidForEN=Support English, numbers, \'-\', \'_\', \'.\'.
pers_person_pinPrompt=Please enter the characters from the English alphabet, numbers.
pers_person_pinSet=Personnel ID Setting
pers_person_supportLetter=Support Letters
pers_person_cardsSupport=Multiple Cards per Person
pers_person_SupportDefault=Auto-Create Personnel ID
pers_person_noSpecialChar=Name cannot enter special characters!
pers_person_pinInteger=Please enter the digits.
pers_pin_noSpecialChar=Personnel numbers cannot contain special characters!
pers_op_capture=Capture
pers_person_cardDuress=Card Number Repeated
pers_person_pwdException=Password Exception
pers_person_systemCheckTip=Check the personnel information for proper format!
pers_person_immeHandle=Immediately Handle
pers_person_cardSet=Card Setting
pers_person_tempPersonSet=Pending Personnel Settings
pers_person_cardsReadMode=Card Reading Mode
pers_person_cardsReadModeReadHead=Read By Controller
pers_person_cardsReadModeID180=Read By ID180
pers_person_IDReadMode=ID Card Reading Mode
pers_person_IDReadModeIDCardReader=ID Card Reader
pers_person_IDReadModeTcpReadHead=TCP/IP Reader
pers_person_physicalNo=ID Card Physical Card Number
pers_person_physicalNoToCardNo=Use ID card physical card number
pers_person_ReadIDCard=Read ID card
pers_person_templateBioTypeNumber=Biometric Template Type Number
pers_person_templateValidType=Biometric Template Validity
pers_person_templateBioType=Biometric Template Type
pers_person_templateVersion=Biometric Template Version
pers_person_template=Biometric Template
pers_person_templateNo=Biometric Template No.
pers_person_templateNoIndex=Biometric Template Index
pers_person_templateDataUpdate=Updating data when biometric template exists:
pers_person_templateValidTypeNoNull=Biometric template validity must not be empty!
pers_person_templateBioTypeNoNull=Personnel ID: {0}, Biometric template type must not be empty!
pers_person_templateVersionNoNull=Personnel ID: {0}, Biometric template version must not be empty!
pers_person_templateNoNull=Personnel ID: {0}, Biometric template must not be empty!
pers_person_templateNoNoNull=Personnel ID: {0}, Biometric template No. must not be empty!
pers_person_templateNoIndexNoNull=Personnel ID: {0}, Biometric template index must not be empty!
pers_person_templateError=Personnel ID: {0}, Biometric template incorrect!
pers_person_bioDuress=Duress
pers_person_universal=Common
pers_person_voice=Voiceprint
pers_person_iris=Iris
pers_person_retina=Retina
pers_person_palmPrints=Palm
pers_person_metacarpalVein=Palm Vein
pers_person_visibleFace=Visible Face
pers_person_pinError=Personnel ID {0} does not exist,unable to process this data!
pers_person_pinException=Personnel ID Exception
pers_person_pinAutoIncrement=Personnel ID Auto-increment
pers_person_resetSelfPwd=Reset Self Login Password
pers_person_picMaxSize=The image resolution is too high and the resolution is recommended below {0}.
pers_person_picMinSize=The image resolution is too low and the resolution is recommended to be above {0}.
pers_person_cropFaceShow=View Face Picture
pers_person_faceNoFound=Unrecognized face
pers_person_biometrics=Biometrics Type
pers_person_photo=Personal Photos
pers_person_visibleFaceTemplate=Visible face template
pers_person_infraredFaceTemplate=Near Infrared Face Template
pers_person_delBioTemplate=Delete Biometric Data
pers_person_delBioTemplateSelect=Please select the biological template to delete!
pers_person_infraredFace=Near Infrared Face
pers_person_notCard=Does not include card
pers_person_notRegFinger=Does not include fingerprint
pers_person_notMetacarpalVein=Does not include palm vein
pers_person_notRegVein=Does not include finger veins
pers_person_notIris=Excluding iris
pers_person_notInfraredFaceTemplate=Does not include near infrared face template
pers_person_notVisibleFaceTemplate=Does not include visible light face template
pers_person_notVisibleFacePhoto=Does not include visible face photos
pers_person_visibleFacePhoto=Visible face photo
pers_person_change=Personnel Adjustments
pers_person_cropFaceUsePhoto=Do you want to display the comparison photo as a avatar?
pers_person_photoUseCropFace=Do you want to use the avatar to generate a comparison photo?
pers_person_selectCamera=Select capture camera
pers_person_delCropFaceMsg=There are no comparison photos to delete!
pers_person_disabledNotOp=The person has been disabled and cannot operate!
pers_person_visiblePalm=Visible light palm
pers_person_notVisiblePalm=Does not include visible light palm
pers_person_selectDisabledNotOp=There are disabled personnel in the selected personnel, unable to operate!
pers_person_photoUseCropFaceAndTempalte=Do you want to use avatars to generate comparison photos and facial templates?
pers_person_photoUseTempalte=Do you want to use photos to generate face templates?
pers_person_createCropFace=Generate comparison photos
pers_person_createFaceTempalte=Generate facial templates
pers_person_faceTempalte=Facial templates
pers_person_extractFaceTemplate=Extracting Facial Templates
pers_person_createSuccess=Successfully generated
pers_person_createFail=Generation failed
pers_person_serverConnectWarn=Server address, username, and password cannot be empty!
pers_person_serverOffline=Offline facial template extraction server
pers_person_faceTemplateError1=Face detection failed
pers_person_faceTemplateError2=Facial occlusion
pers_person_faceTemplateError3=Insufficient clarity
pers_person_faceTemplateError4=Face angle too large
pers_person_faceTemplateError5=Live detection failed
pers_person_faceTemplateError6=Facial template extraction failed
pers_person_cropFaceNoExist=The comparison photo does not exist
pers_person_disableFaceTemplate=The facial template extraction function is not enabled, unable to extract the template!
pers_person_cropFacePhoto=Face Comparison Photo
pers_person_vislightPalmPhoto=Palm Comparison Photo
pers_person_serverOfflineWarn=The facial template extraction server is offline and cannot enable this feature!
pers_person_serverConnectInfo=Please test if the facial template extraction server is online?
pers_person_readCard=Issue Card from Device
pers_person_stopRead=Stop Issue
pers_person_chooseDoor=Choose the Door
pers_person_readCarding=Issuing card, please try again later!
pers_person_notModified=The mobile phone number cannot be modified.
pers_person_syncAcms=Synchronize personnel with ACMS
pers_person_startUpdate=Start updating personnel information.
pers_person_updateFailed=Personnel information update failed!
#抓拍照片
pers_capture_catchPhoto=Capture Photo
pers_capture_preview=Preview
#部门
pers_dept_entity=Department
pers_dept_deptNo=Department Number
pers_dept_deptName=Department Name
pers_dept_parentDeptNo=Parent Department Number
pers_dept_parentDeptName=Parent Department Name
pers_dept_parentDept=Parent Department
pers_dept_note=If the new department does not appear in the list, please contact the administrator to re-authorize the user to edit the department!
pers_dept_exit=Contains
pers_dept_auth=Bound system user
pers_dept_parentMenuMsg=The superior department cannot be set the same as a lower department.
pers_dept_initDept=General
pers_dept_deptMarket=Marketing Department
pers_dept_deptRD=Development Department
pers_dept_deptFinancial=Financial Department
pers_dept_nameNoSpace=Department Name can not begin or end with spaces.
pers_dept_nameExist=Department name already exists
pers_dept_changeLevel=Whether to switch to the level of this department
pers_dept_noSpecialChar=Department numbers cannot contain special characters!
pers_dept_NameNoSpecialChar=Department names cannot contain special characters!
pers_dept_noModifiedParent=The superior department cannot be modified!
#职位
pers_position_entity=Position
pers_position_code=Position Number
pers_position_name=Position Name
pers_position_notExist=The position does not exist!
pers_position_sortNo=Sort
pers_position_parentName=Parent Position
pers_position_parentCode=Parent Position Number
pers_position_batchToPosition=New Position
pers_position_nameExist=This position name already exists
pers_position_change=Change Position
pers_position_parentMenuMsg=The parent position cannot be set as a sub-position.
pers_position_nameNoSpace=Position name can not begin or end with spaces.
pers_position_existSub={0} :Contains sub-posts cannot be deleted
pers_position_existPerson={0} :Personnel cannot be deleted
pers_position_importTemplate=Job import template
pers_position_downloadTemplate=Download import template
pers_position_codeNotEmpty=The position number cannot be empty
pers_position_nameNotEmpty=The position name cannot be empty
pers_position_nameNoSpecialChar=Position name {0} cannot contain special characters!
pers_position_noSpecialChar=Position number {0} cannot be a special character!
pers_position_codeLength=The position number {0} exceeds 30 digits in length
pers_position_nameLength=Data whose position name {0} is longer than {1}
pers_position_codeExist=The job ID {0} already exists
#证件
pers_cert_type=Certificate Type
pers_cert_number=Certificate Number
pers_cert_name=Certificate Name
pers_cert_numberExist=The certificate number already Exists.
#导出
pers_export_allPersPerson=All Persons
pers_export_curPersPerson=Current Person
pers_export_template=Export Template
pers_export_personInfo=Export Personnel
pers_export_personInfoTemplate=Download Personnel Import Template
pers_export_personBioTemplate=Export Biometric Template
pers_export_basicInfo=Basic Information
pers_export_customAttr=Custom Attributes
pers_export_templateComment=Field name,primary key?unique?allow null values?({0},{1},{2},{3})
pers_export_templateFileName=Personnel Import Template
pers_export_bioTemplateFileName=Personnel Biometric Template
pers_export_deptInfo=Export Department
pers_export_deptTemplate=Download Department Import Template
pers_export_deptTemplateFileName=Department Import Template
pers_export_personPhoto=Export Personnel Photo
pers_export_allPhotos=All Photos (select all people)
pers_export_selectPhotoToExport=Select the start ID and end ID to export the personnel photos.
pers_export_fromId=From ID
pers_export_toId=To
pers_export_certNumberComment=The certificate type is required after filling in the certificate number
pers_export_templateCommentName=Field name:({0})
pers_export_dataExist=Please make sure that the imported data already exists in the system
pers_export_cardNoTip=Multiple card numbers separated by &
pers_carNumber_importTip=License plate number (multiple license plates& separated)
#导入
pers_import_certNumberExist=The certificate number {0} already exists.
pers_import_complete=Complete
pers_import_password=Person Password
pers_import_fail=The {0} row failed: {1}
pers_import_overData=You are importing {0} people; system can only support import of 30,000 people!
pers_import_pinTooLong=Personnel ID {0} is too long!
pers_import_pinExist=Personnel ID {0} already exists!
pers_import_pinIsRepeat=Personnel ID {0} is repeated!
pers_import_pinError=Personnel ID {0} error!
pers_import_pinSupportNumber=The personnel number only supports numbers! The personnel number is: {0}
pers_import_pinNotSupportNonAlphabetic=Personnel ID does not support non-English letters!The personnel number is: {0}
pers_import_pinNotNull=Personnel ID must not be all zeros!
pers_import_pinStartWithZero=Personnel ID can not start with zero!
pers_import_cardNoNotSupportLetter=Card number does not support letters!
pers_import_cardNoNotNull=Card number can not be all zeros!
pers_import_cardNoStartWithZero=Card number can not start with zero!
pers_import_cardTooLong=Card number {0} is too long!
pers_import_cardExist=Card number {0} already Exists!
pers_import_personPwdOnlyNumber=Personal password only supports digits (no letters)!
pers_import_personPwdTooLong=Personal password {0} is too long!
pers_import_personDuressPwd=Personal password {0} is repeated!
pers_import_emailTooLong=Email {0} is too long!
pers_import_nameTooLong=Name {0} is too long!
pers_import_genderError=Gender format error!
pers_import_personDateError=Hire date format error!
pers_import_createTimeError=Create Time format error!
pers_import_phoneError=Phone number format error!
pers_import_phoneTooLong=Phone number length can not be longer than 20 characters!
pers_import_emailError=Email format error!
pers_import_birthdayError=Date of Birth format error!
pers_import_nameError=Lastname and Firstname can not contain special symbols!The personnel number is: {0}
pers_import_firstnameError=Lastname or firstname cannot contain ','!
pers_import_firstnameNotNull=Firstname must not be empty!
pers_import_dataCheck=The data check is over
pers_import_dataSaveFail=Failed to import data!
pers_import_allSucceed=All data imported successfully!
pers_import_result=Succeed: {0}, Failed: {1}.
pers_import_result2=Import Result
pers_import_result3=Succeed: {0}, Updated: {1}, Failed: {2}.
pers_import_notSupportFormat=This Format is not supported!
pers_import_selectCorrectFile=Please select the correct file.
pers_import_fileFormat=File Format
pers_import_targetFile=Destination File
pers_import_startRow=Header Start Rows
pers_import_startRowNote=The first line of the data format is table name, the second line is header, the third line is the import data, please check the file and then import.
pers_import_delimiter=Delimiter
pers_import_importingDataFields=Database Fields
pers_import_dataSourceFields=Importing data fields
pers_import_total=Total
pers_import_dataUpdate=Update the existing Personnel ID in the system:
pers_import_dataIsNull=No data in the file!
pers_import_deptNotExist=The department does not exist!
pers_import_deptIsNotNull=Department name must not be empty!
pers_import_pinNotEmpty=Personnel ID can not be blank!
pers_import_nameNotEmpty=Personnel name cannot be empty!
pers_import_siteCodeOnlyLetterNum=Site code format error.
pers_import_cardNoFormatErrors=The format of Card number {0} is incorrect.
pers_import_cardsNotSupport=The feature [Multiple Cards per Person] is disabled, cannot program more than one card per person.
pers_import_personInfo=Import Personnel
pers_import_commentFormat=The comment format is not correct!
pers_import_noComment=The data in {0} row and {1} column is not commented.
pers_import_fieldRepeat=The data in {0} row and {1} column, the field name in the comment is repeated:{2}
pers_import_primaryKey=There should be at least one field as the primary key.
pers_import_templateIsRepeat=Personnel ID: The bio-template data for {0} has been duplicated!
pers_import_biologicalTemplate=Import Biometric Template
pers_import_uploadFileSuccess=Upload successful, starting to parse data, please wait...
pers_import_resolutionComplete=The data has been parsed, starting database updates.
pers_import_mustField=The import file must contain the column {0}.
pers_import_bioTemplateSuccess=Import biometric template,this data need to be manually synchronized to the device from each business module.
pers_import_personPhoto=Import Personnel Photo
pers_import_opera_log=Operation log
pers_import_error_log=Error log
pers_import_uploadFileSize=Please upload a file with a size no larger than {0}!
pers_import_uploadFileSizeLimit=For a single import, please upload a file with a size no larger than 500M!
pers_import_type=Import mode
pers_import_photoType=Photo
pers_import_archiveType=Compressed package
pers_import_startUpload=Start Upload
pers_import_addMore=Add More
pers_import_photoQuality=Photo Quality
pers_import_original=Original
pers_import_adaptive=Adaptive
pers_import_adaptiveSize=(Size 480 * 640)
pers_import_totalNumber=Total
pers_import_uploadTip=(Please do not delete photo while uploading)
pers_import_addPhotoTip=Please add photos to upload!
pers_import_selectPhotoTip=Please select the photo you want to upload!
pers_import_uploadResult=Upload Photo Result
pers_import_pleaseSelectPhoto=Please Select Photo
pers_import_multipleSelectTip=Press Ctrl to make multiple selections
pers_import_replacePhotoTip=The selected photo is already in the upload list, do you want to replace it?
pers_import_photoNamePinNotCorrespond=Photo name and personnel id mismatch
pers_import_photoFormatRequirement=Please name the photo with personnel ID.The correct format is JPG/PNG.Make sure the photo name does not contain special characters.
pers_import_filterTip=Some of the photos you selected cannot be previewed, and there may be the following reasons:
pers_import_photoContainSpecialCharacters=Photo name has special characters.
pers_import_photoFormatError=Photo format is incorrect.
pers_import_photoSelectNumber=Do not choose more than 3000 pictures in a single import!
pers_import_photoSelectNumberLimit=Don't choose more than {0} images!
pers_import_fileMaxSize=The picture is too large, please upload an image file smaller than 5M.
pers_import_notUploadPhotoNumber=No more than 3000 images can be uploaded!
pers_import_zipFileNotPhoto=There is no photo of the person in the zip file, please re-select and import!
pers_import_personPlateRepeat=Personnel license plate {0} duplicate!
pers_import_filePlateRepeat=File inside the license plate {0} duplicate!
pers_import_personPlateFormat=Personnel license plate {0} format is incorrect!
pers_import_personPlateMax=The number of personnel license plates exceeds the maximum of 6!
pers_import_cropFaceFail=Failed to generate comparison photo!
pers_import_pinLeaved=Personnel ID: {0} has left.
pers_import_exceedLicense=Imports are not allowed for the number of people who exceed the software license.
pers_import_bioTemplateNotNull=Personnel ID, Biometric Template Type, ID or Index and Content, Version are not allowed to be empty!
pers_import_certTypeNotNull=Personnel number is: {0} Document type cannot be empty
pers_import_certTypeNotExist=Personnel number is: {0} Document type does not exist
pers_import_certNumNotNull=Personnel number is: {0} Certificate number cannot be empty
pers_import_certNumberTooLong=Row {0}:ID number {1} is too long!
pers_import_idNumberErrors=Row {0}:ID card number {1} is malformed!
pers_import_emailErrors=Row {0}:Email address {1} format error!
pers_import_emailIsExist=Email address {0} already exists!
pers_import_emailIsRepeat=Row {0}: The internal email address of the file {1} has been repeated!
pers_import_fileMobilePhoneRepeat=Row {0}: The internal mobile phone number of the file {1} has been repeated!
pers_import_mobilePhoneErrors=Mobile phone number format error!
pers_import_hireDateError=The format of the hire date is incorrect!
pers_import_selectPhotoType=Please select the type of imported photo!
pers_import_hireDateLaterCurrent=The hire date cannot be later than the current date!
pers_import_buildingNotExist=The building does not exist!
pers_import_unitNotExist=Unit does not exist!
pers_import_vdbInfoFail=There is no unit information named {1} in building {0}!
pers_import_vdbBuildingFail=The building information of unit {0} cannot be empty!
pers_import_vdbRoomNoFail=Room number must be a numeric value greater than 0!
#人员离职
pers_person_leave=Dismissal
pers_dimission_date=Dismissal Date
pers_dimission_type=Dismissal Type
pers_dimission_reason=Dismissal Reason
pers_dimission_volutary=Voluntary Redundancy
pers_dimission_dismiss=Dismissed
pers_dimission_resignat=Resignation
pers_dimission_shiftJob=Transfer
pers_dimission_leave=Retain Job without Salary
pers_dimission_recovery=Reinstatement
pers_dimission_sureToRecovery=Are you sure you want to perform the reinstatement operation?
pers_dimission_backCard=Was card returned?
pers_dimission_isForbidAction=Disable access control rights immediately?
pers_dimission_writeInfomation=Input the dismissal information
pers_dimission_pinRetain=Keep the personnel id for the dismissed employee?
pers_dimission_downloadTemplate=Download Dismission Import Template
pers_dimission_import=Import Dismissions
pers_dimission_importTemplate=Dismission import template
pers_dimission_date_noNull=Dismission date cannot be empty
pers_dimission_leaveType_noExist=Dismission type does not exist
pers_dimission_dateFormat=Required field, time format is yyyy-MM-dd, such as: 2020-07-22
pers_dimission_leaveType=Required field, such as: Voluntary Redundancy, Dismissed, Resignation, Transfer
pers_dimission_forbidden=Disable
pers_dimission_leaveType_noNull=The leave type cannot be empty!
pers_dimission_person_noExist=The dismissal person does not exist!
pers_dimission_date_error=Dismission date not filled in or format incorrect
#临时人员
pers_tempPerson_audit=Review
pers_tempPerson_view=View
pers_tempPerson_waitReview=Waiting for the administrator's review
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Card Number
pers_card_state=Card State
pers_card_effect=Effective
pers_card_disabled=Invalid
pers_card_past=Expired
pers_card_back=Card Returned
pers_card_change=Card Change
pers_card_note=The card number already exists.
pers_card_numTooBig=The length of original card number is too long.
pers_issueCard_entity=Issue Card
pers_issueCard_operator=Operator
pers_issueCard_operate=Action
pers_issueCard_note=Card issuers operating for registered personnel data but not the registration card account staff!
pers_issueCard_date=Issue Card Date
pers_issueCard_changeTime=Change Time
pers_issueCard_cardValidate=No space is allowed.
pers_issueCard_cardEmptyNote=The card must not be empty.
pers_issueCard_cardHasBeenIssued=This card has been Issued!
pers_issueCard_noCardPerson=Unissued person
pers_issueCard_waitPerson=Current issue person
pers_issueCard_mc5000=MC5000 Card Issuance
pers_batchIssCard_entity=Batch Issue Card
pers_batchIssCard_startPersNo=Start Personnel ID
pers_batchIssCard_endPersNo=End Personnel ID
pers_batchIssCard_issCardNum=Number of Issued Cards
pers_batchIssCard_notIssCardNum=Number of Persons with No Card Issued
pers_batchIssCard_generateList=Generate List
pers_batchIssCard_startRead=Start to read
pers_batchIssCard_swipCard=Position of swiping card
pers_batchIssCard_sendCard=Device
pers_batchIssCard_dispenCardIss=USB Reader
pers_batchIssCard_usbEncoder=USB Encoder
pers_batchIssCard_note=Personnel ID only supports input values, and only shows persons with no card issued (max. 300)!
pers_batchIssCard_startPinEmpty=Start Personnel ID can't be empty!
pers_batchIssCard_endPinEmpty=End Personnel ID must not be empty!
pers_batchIssCard_startPinLargeThanEndPin=Start Personnel ID can't be larger than the End Personnel ID!
pers_batchIssCard_numberParagraphNoPerson=Person without issued card does not exist within the start-to-end ID segment number!
pers_batchIssCard_inputCardNum=Input Card Number
pers_batchIssCard_cardLetter=Card does not support letters!
pers_batchIssCard_cardNoTooLong=The card number length is too long!
pers_batchIssCard_issueWay=Card Enrollment Method
pers_batchIssCard_noPersonList=Personnel list not yet generated.
pers_batchIssCard_startReadCard=Start Reading Card
pers_batchIssCard_swipePosition=Swipe Position
pers_batchIssCard_chooseSwipePosition=Please choose swipe position.
pers_batchIssCard_readCardTip=The device only reads the unregistered card when the issuing method is a selected reader.
pers_batchIssCard_notIssCardNo=Number of non-issued cards
pers_batchIssCard_totalNumOfCards=The total number of cards
pers_batchIssCard_acms=ACMS Card Issuance
pers_lossCard_entity=Reported Lost Card
pers_lossCard_lost=This card has been reported, cannot repeat the operation!
pers_losscard_note2=After you write the management card,you must swipe this card at the elevator reader to make sure the cards lose effect in the device.
pers_revertCard_entity=Reactivate Lost Card
pers_revertCard_setReport=Please report the card loss first!
pers_revertcard_note2=After you write the management card, you must swipe this card on the elevator reader to make sure you can use the cards again.
pers_issueCard_success=Card issuing successful!
pers_issueCard_error=Failure of card issuing!
pers_cardData_error=Read card data format exception!
pers_analysis_error=Card data parsing exception!
pers_cardOperation_error=Card operation exception!
pers_cardPacket_error=Card operation command package exception!
pers_card_write=Write Card
pers_card_init=Initialize card
pers_card_loss=Lost Card
pers_card_revert=Revert card
pers_card_writeMgr=Write management card
pers_initCard_tip=After initialization, the card will become a blank card!
pers_initCard_prepare=Ready to initialize the card...
pers_initCard_process=Initializing the card...
pers_initCard_success=Initialize the card successfully
pers_mgrCard_prepare=Prepare to write management card data...
pers_mgrCard_process=Is writing management card data...
pers_mgrCard_success=Write management card success
pers_userCard_prepare=Prepare to write a user card...
pers_userCard_process=Writing user card data...
pers_userCard_success=Write user card success
pers_userCard_tip=Please set the start time and end time in the edit person page and then write card operation.
pers_userCard_tip2=Permissions data is empty, can't write cards.
pers_userCard_tip3=Authority group associated device more than two units, data loss.
pers_writeCard_tip=Please make sure you have connected the encoder and installed the driver, and put the card on the encoder.
pers_writeMgrCard_tip=The quantity of Lost cards and reverted cards can not be greater than 18
pers_writeMgrCard_tip2=The number of the lost card and revert card:
pers_card_writeToMgr=Written to the management card
pers_card_hex=Card Format Display
pers_card_decimal=Decimal
pers_card_Hexadecimal=Hexadecimal
pers_card_IssuedCommandFail=Issued Command Failed:
pers_card_multiCard=More Cards
pers_card_deputyCard=Secondary Card
pers_card_deputyCardValid=Please input the main card first!
pers_card_writePinFormat=System can only associate Personnel ID's without letters to the card!
pers_card_notMoreThanSixteen=The quantity of the secondary cards cannot be greater than 16.
pers_card_notDelAll=Cannot remove all secondary cards.
pers_card_maxCard=The card number can not exceed {0}.
pers_card_posUseCardNo=The person's main card is being used by the consumer module. Please go to the consumer module to perform the card withdrawal operation!
pers_card_delFirst=Please delete the issued card number before issuing the card!
pers_card_disablePersonWarn=The selected card number contains disabled personnel and cannot be operated!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Site Code
pers_wiegandFmt_wiegandMode=Mode
pers_wiegandFmt_wiegandModeOne=Mode One
pers_wiegandFmt_wiegandModeTwo=Mode Two
pers_wiegandFmt_isDefaultFmt=Auto
pers_wgFmt_entity=Wiegand Format
pers_wgFmt_in=Wiegand Input Format
pers_wgFmt_out=Wiegand Output Format
pers_wgFmt_inType=Wiegand Input Type
pers_wgFmt_outType=Wiegand Output Type
pers_wgFmt_wg=Wiegand Format
pers_wgFmt_totalBit=Total Bit
pers_wgFmt_oddPch=Odd Parity Check(o)
pers_wgFmt_evenPck=Even Parity Check(e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=Facility Code(f)
pers_wgFmt_siteCode=Site Code(s)
pers_wgFmt_manufactoryCode=Manufacturer Code(m)
pers_wgFmt_firstParity=First Parity Check(p)
pers_wgFmt_secondParity=Second Parity Check(p)
pers_wgFmt_cardFmt=Card Check Format
pers_wgFmt_parityFmt=Parity Check Format
pers_wgFmt_startBit=Start Bit
pers_wgFmt_test=Card Format Test
pers_wgFmt_error=Card Format Error!
pers_wgFmt_verify1=Total bit must not be more than 80!
pers_wgFmt_verify2=Check card format length must be equal to the total number of bits!
pers_wgFmt_verify3=Parity format length must be equal to the total number of digits!
pers_wgFmt_verify4=The initialized data can not be deleted!
pers_wgFmt_verify5=Card format is being used, can not delete!
pers_wgFmt_verify6=The first parity bit can not be greater than the total number of!
pers_wgFmt_verify7=Second parity bit can not be greater than the total number of!
pers_wgFmt_verify8=Start and maximum length format is not correct!
pers_wgFmt_verify9=Card check format length can not be greater than the total number of!
pers_wgFmt_verify10=Card check digit function can not cross!
pers_wgFmt_verify11=The site code exceeds the set range!
pers_wgFmt_verify=Check
pers_wgFmt_unverify=Non Check
pers_wgFmt_atLeastDefaultFmt=Please keep at least one automatically matching card format!
pers_wgFmt_defaultFmtError1=There are some other card format with the same card number bits,can not be set to automatically matching card format!
pers_wgFmt_defaultFmtError2=Presence of the same card format to automatic matching, the operation fails!
pers_wgFmt_cardFormats=Card Formats
pers_wgFmt_cardFormatTesting=Card Formats Testing
pers_wgFmt_checkIsUsed=The card format is used in {0} and cannot be deleted!
pers_wgFmt_supportDigitsNumber=Please enter the number of digits supported by the device
#选人控件
pers_widget_selectPerson=Select personnel
pers_widget_searchType1=Query
pers_widget_searchType2=Department
pers_widget_deptHint=Note: Importing all the personnel of selected departments
pers_widget_noPerson=No personnel has been selected.
pers_widget_noDept=Please select a department.
pers_widget_noDeptPerson=No person under the selected department, please reselect!
#人员属性
pers_person_carPlate=License Plate
pers_person_socialSecurity=Social Security Number
pers_person_msg4=The maximum length must not exceed 20!
pers_person_msg5=The name can not contain more than 50 characters!
pers_person_type=Person Type
pers_person_reseCode=Self Login Password
pers_person_IsSendMail=E-mail Notification
pers_person_inactive=Inactive
pers_person_active=Active
pers_person_employee=Employee
pers_person_isSendMailMsg=To use the feature [Event Notification], you must input the email first.
pers_person_createTime=Create Time
pers_person_pinFirstValid=First character of the personnel number can not be 8 or 9.
pers_person_attrValueValid=The value of the field can not be repeated.
pers_person_attrValueDelimiterValid=The separator should be in the middle.
pers_person_isSendSMS=SMS notification
pers_person_building=Building Name
pers_person_unitName=Unit Name
pers_person_roomNo=Room Number
#动态属性
pers_attr_emp_type=Employee Type
pers_attr_street=Street
pers_attr_nation=Nation
pers_attr_office_address=Office Address
pers_attr_postcode=Post code
pers_attr_office_phone=Office Phone
pers_attr_home_phone=Home Phone
pers_attr_job_title=Job Title
pers_attr_birthplace=Birthplace
pers_attr_polit_status=Politic Status
pers_attr_country=Country
pers_attr_home_address=Home Address
pers_attr_hire_type=Hire Type
pers_attr_inContract=Contract Worker
pers_attr_outContract=Non Contract Worker
#属性自定义
pers_attribute_attrName=Display Name
pers_attribute_attrValue=Attribute Value
pers_attribute_controlType=Input Type
pers_attribute_positionX=Row
pers_attribute_positionY=Column
pers_attribute_showTable=Display in Person List
pers_attrDefini_deletemsg=This property has been used. Are you sure you want to delete it?
pers_attrDefini_reserved=System reserved field name.
pers_attrDefini_msg1=The maximum length of not more than 30!
pers_attrDefini_msg2=The rows of the column already exists, please change to another location!
pers_attrDefini_attrValue_split=Using a  \' ; \' delimiter.
pers_attrDefini_attrName=Attribute Name
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=Attribute Id
pers_attrDefini_select=Pull-down List
pers_attrDefini_check=Multiple Choice
pers_attrDefini_radio=Single Choice
pers_attrDefini_text=Text
pers_attrDefini_maxCol=Not more than 2 columns.
pers_attrDefini_maxLimit=Custom attributes has reached the maximum number!
pers_attrDefini_modControlType=Changing the input type will clear the current field data for all personnel in the system.whether to continue?
#leavePerson
pers_leavePerson_reinstated=Reinstatement
#opExample
pers_example_newRecode=The acquisition of new records
pers_example_allRecode=Access to all records
pers_custField_StatisticalType=Statistical Type
#人员参数修改
pers_param_isAudit=Enable Auto-audit
pers_param_donotChangePin=The existing Personnel ID contains letter(s), cannot disable the feature [Support Letters].
pers_param_hexChangeWarn=The current system already has the card numbers, cannot change the card format display mode.
pers_param_cardsChangeWarn=There are some people with more than one card in the system, can not disable the feature [Multiple Cards per Person].
pers_param_maxPinLength=The length of the new Personnel ID can not be less than the length of the existing Personnel ID in the system.
pers_param_pinBeyondDevLength=The maximum length of Personnel ID supported by the device in the system is {0}, please enter an integer less than {1}.
pers_param_cardBeyondDevLength=The maximum length of card number supported by the device in the system is {0}, please enter an integer less than {1}.
pers_param_checkIsExistNoAudit=There are unreviewed registrants in the current system and cannot be modified to automatic review!
pers_param_noSupportPinLetter=There are devices in the system that do not support Personnel ID's containing letters, can not enable the feature [Support Letters].
pers_param_changePinLettersTip=Personnel number supports auto increment, can not modify staff number modes
pers_param_changePinIncrementTip=Person number support includes letters, cannot be amend staff number mode
pers_param_qrCode=Enterprise QR code
pers_param_employeeRegistrar=Enable cloud employee registration
pers_param_downloadQRCodePic=Download QR code image
pers_param_qrCodeUrl=QR Code URL
pers_param_qrCodeUrlCreate=Self-service Registration
pers_param_qrCodeUrlHref=Server address:Port
pers_param_pinSetWarn=There are already personnel in the current system, and the personnel number mode cannot be changed.
pers_param_selfRegistration=Enable Self Registration
pers_param_infoProtection=Personal sensitive information protection
pers_param_infoProtectionWarnMsg=After enabling the personal sensitive information security protection option, the sensitive personal data involved in this module will be desensitized or obscured, including but not limited to names, card numbers, ID numbers, photos, etc.
pers_param_templateServer=Facial Template Extraction Server
pers_param_enableFacialTemplate=Enable Facial Template Extraction
pers_param_templateServerAddr=Facial template extraction server address
pers_param_templateServerWarnInfo=When facial template extraction is enabled, when the facial template extraction server is online and the user verification is passed, personnel will default to extracting facial templates when comparing photos; When the facial template extraction server is in offline mode, do not extract facial templates!
pers_param_templateServerWarnInfo1=When enabling facial template extraction, a device that supports facial template extraction needs to be connected!
pers_param_templateServerOffline=The facial template extraction server is offline and cannot extract facial templates! Do you want to continue?
pers_param_faceServer=Facial Backend Comparison Server
pers_param_enableFaceVerify=Enable facial backend comparison
pers_param_faceServerAddr=Face backend comparison service address
pers_param_faceServerSecret=Face backend comparison service key
#国籍
pers_person_nationality=Country/Region
pers_nationality_angola=Angolan
pers_nationality_afghanistan=Afghan
pers_nationality_albania=Albanian
pers_nationality_algeria=Algerian
pers_nationality_america=American
pers_nationality_andorra=Andorran
pers_nationality_anguilla=Anguillan
pers_nationality_antAndBar=Antigua and Barbuda
pers_nationality_argentina=Argentinan
pers_nationality_armenia=Armenians
pers_nationality_ascension=Ascension
pers_nationality_australia=Australian
pers_nationality_austria=Austrian
pers_nationality_azerbaijan=Azerbaijani
pers_nationality_bahamas=Bahamas
pers_nationality_bahrain=Bahrain
pers_nationality_bangladesh=Bangladesh
pers_nationality_barbados=Barbadian
pers_nationality_belarus=Belarus
pers_nationality_belgium=Belgium
pers_nationality_belize=Belizean
pers_nationality_benin=Beninese
pers_nationality_bermudaIs=Bermuda Is.
pers_nationality_bolivia=Bolivia
pers_nationality_botswana=Botswanan
pers_nationality_brazil=Brazil
pers_nationality_brunei=Brunei
pers_nationality_bulgaria=Bulgarian
pers_nationality_burkinaFaso=Burkina Faso
pers_nationality_burma=Burma
pers_nationality_burundi=Burundian
pers_nationality_cameroon=Cameroonian
pers_nationality_canada=Canadian
pers_nationality_caymanIs=Cayman Islands
pers_nationality_cenAfrRepub=Central African Republic
pers_nationality_chad=Chadian
pers_nationality_chile=Chile
pers_nationality_china=China
pers_nationality_colombia=Colombian
pers_nationality_congo=Republic of the Congo
pers_nationality_cookIs=Cook Is.
pers_nationality_costaRica=Costarican
pers_nationality_cuba=Cuban
pers_nationality_cyprus=Cyprus
pers_nationality_czechRep=Czech Republic
pers_nationality_denmark=Denmark
pers_nationality_djibouti=Djiboutis
pers_nationality_dominicaRep=Dominica Rep.
pers_nationality_ecuador=Ecuador
pers_nationality_egypt=Egyptian
pers_nationality_eISalvador=EI Salvador
pers_nationality_england=British
pers_nationality_estonia=Estonian
pers_nationality_ethiopia=Ethiopian
pers_nationality_fiji=Fijian
pers_nationality_finland=Finland
pers_nationality_france=France
pers_nationality_freGui=French Guiana
pers_nationality_gabon=Gabonese
pers_nationality_gambia=Gambia
pers_nationality_georgia=Georgian
pers_nationality_germany=German
pers_nationality_ghana=Ghanaian
pers_nationality_gibraltarm=Gibraltar
pers_nationality_greece=Greek
pers_nationality_grenada=Grenada
pers_nationality_guam=Guam
pers_nationality_guatemala=Guatemalan
pers_nationality_guinea=Guinean
pers_nationality_guyana=Guyana
pers_nationality_haiti=Haiti
pers_nationality_honduras=Honduras
pers_nationality_hungary=Hungarian
pers_nationality_iceland=Icelander
pers_nationality_india=Indian
pers_nationality_indonesia=Indonesians
pers_nationality_iran=Iranian
pers_nationality_iraq=Iraqi
pers_nationality_ireland=Irish
pers_nationality_israel=Israeli
pers_nationality_italy=Italian
pers_nationality_ivoryCoast=Lvoirian
pers_nationality_jamaica=Jamaican
pers_nationality_japan=Japan
pers_nationality_jordan=Jordan
pers_nationality_kenya=Kenyan
pers_nationality_korea=Korean
pers_nationality_kuwait=Kuwaiti
pers_nationality_kyrgyzstan=Kyrgyzstan
pers_nationality_laos=Laotian
pers_nationality_latvia=Latvian
pers_nationality_lebanon=Lebanese
pers_nationality_lesotho=Mesotho
pers_nationality_liberia=Liberian
pers_nationality_libya=Libyan
pers_nationality_liechtenstein=Liechtensteiner
pers_nationality_lithuania=Lithuanian
pers_nationality_luxembourg=Luxemburgues
pers_nationality_madagascar=Malagasy
pers_nationality_malawi=Malawian
pers_nationality_malaysia=Malaysia
pers_nationality_maldives=Maldives
pers_nationality_mali=Mali
pers_nationality_malta=Maltese
pers_nationality_marianaIs=Mariana Is.
pers_nationality_martinique=Martinique
pers_nationality_mauritius=Mauritian
pers_nationality_mexico=Mexican
pers_nationality_moldova=Moldova
pers_nationality_monaco=Monacan
pers_nationality_montseIs=Montserrat Is.
pers_nationality_morocco=Moroccan
pers_nationality_mozambique=Mozambican
pers_nationality_namibia=Namibian
pers_nationality_nauru=Nauruan
pers_nationality_nepal=Nepal
pers_nationality_netAnti=Netheriands Antilles
pers_nationality_netherlands=Netherlands
pers_nationality_newZealand=New Zealander
pers_nationality_nicaragua=Nicaragua
pers_nationality_niger=Nigerien
pers_nationality_nigeria=Nigeria
pers_nationality_norKorea=North Korea
pers_nationality_norway=Norwegian
pers_nationality_oman=Omani
pers_nationality_pakistan=Pakistani
pers_nationality_panama=Panama
pers_nationality_papNewCui=Papua New Guinean
pers_nationality_paraguay=Paraguay
pers_nationality_peru=Peru
pers_nationality_philippines=Philippines
pers_nationality_poland=Poland
pers_nationality_frenPolyne=French Polynesia
pers_nationality_portugal=Portuguese
pers_nationality_puerRico=Puerto Rico
pers_nationality_qatar=Qatari
pers_nationality_reunion=Reunion
pers_nationality_romania=Romanian
pers_nationality_russia=Russian
pers_nationality_saiLueia=Saint Lueia
pers_nationality_saintVinc=Saint Vincent
pers_nationality_samoa_eastern=Samoa Eastern
pers_nationality_samoa_western=Samoa Western
pers_nationality_sanMarino=San Marinese
pers_nationality_saoAndPrinc=Sao Tome and Principne
pers_nationality_sauArabia=Saudi Arabia
pers_nationality_senegal=Senegalese
pers_nationality_seychelles=Seychellese
pers_nationality_sieLeone=Sierra Leone
pers_nationality_singapore=Singapore
pers_nationality_slovakia=Slovakian
pers_nationality_slovenia=Slovenian
pers_nationality_solomonIs=Solomon islander
pers_nationality_somali=Somali
pers_nationality_souAfrica=South African
pers_nationality_spain=Spanish
pers_nationality_sriLanka=Sri Lankan
pers_nationality_sudan=Sudan
pers_nationality_suriname=Suriname
pers_nationality_swaziland=Swazi
pers_nationality_sweden=Swedish
pers_nationality_switzerland=Swiss
pers_nationality_syria=Syrian
pers_nationality_tajikstan=Tajik
pers_nationality_tanzania=Tanzanian
pers_nationality_thailand=Thai
pers_nationality_togo=Togolese
pers_nationality_tonga=Tongan
pers_nationality_triAndToba=Trinidad and Tobago
pers_nationality_tunisia=Tunisian
pers_nationality_turkey=Turk
pers_nationality_turkmenistan=Turkmenistan
pers_nationality_uganda=Ugandan
pers_nationality_ukraine=Ukrainian
pers_nationality_uniArabEmira=United Arab Emirates
pers_nationality_uruguay=Uruguay
pers_nationality_uzbekistan=Uzbekistan
pers_nationality_venezuela=Venezuela
pers_nationality_vietnam=Vietnamese
pers_nationality_yemen=Yemeni
pers_nationality_serbia=Serbia
pers_nationality_zimbabwe=Zimbabwean
pers_nationality_zambia=Zambian
pers_nationality_aruba=Aruba
pers_nationality_bhutan=Bhutan
pers_nationality_bosnia_herzegovina=Bosnia and Herzegovina
pers_nationality_cambodia=Cambodia
pers_nationality_congoD=Democratic Republic of the Congo
pers_nationality_comoros=Union of the Comoros
pers_nationality_capeVerde=Republic of Cape Verde
pers_nationality_croatia=Croatia
pers_nationality_dominica=Dominica
pers_nationality_eritrea=Eritrea
pers_nationality_micronesia=Micronesia
pers_nationalit_guineaBissau=Guinea-Bissau
pers_nationalit_equatorialGuinea=Equatorial Guinea
pers_nationalit_hongkong=Hongkong,China
pers_nationalit_virginIslands=The United States Virgin Islands
pers_nationalit_britishVirginIslands=The British Virgin Islands
pers_nationalit_kiribati=Kiribati
pers_nationalit_mongolia=Mongolia
pers_nationalit_marshall=Marshall Island
pers_nationalit_macedonia=Macedonia
pers_nationalit_montenegro=Montenegro
pers_nationalit_mauritania=Mauritania
pers_nationalit_palestine=Palestine
pers_nationalit_palau=Palau
pers_nationalit_rwanda=Rwanda
pers_nationalit_saintKittsNevis=Saint Kitts and Nevis
pers_nationalit_timorLeste=Timor Leste
pers_nationalit_taiwan=Taiwan,China
pers_nationalit_tuvalu=Tuvalu
pers_nationalit_vanuatu=Vanuatu
#制卡
pers_person_cardprint=Print Card
pers_cardTemplate_tempSelect=Card Template
pers_cardTemplate_printerSelect=Printer
pers_cardTemplate_front=Front
pers_cardTemplate_opposite=Back
pers_cardTemplate_entryDate=Hire Date
pers_cardTemplate_photo=Photo
pers_cardTemplate_uploadFail=Upload picture failed!
pers_cardTemplate_jpgFormat=Only supports uploading pictures in JPG format!
pers_cardTemplate_printStatus=Print Status
pers_cardTemplate_waiting=Waiting
pers_cardTemplate_printing=Printing
pers_cardTemplate_printOption=Print Option
pers_cardTemplate_duplexPrint=Duplex Print
pers_cardTemplate_frontOnly=Print Front Only
#app
pers_app_delPers=There are no people on the server you want to delete
pers_app_deptIsNull=Can not find the department number or name corresponding to the department
pers_app_personNull=Person does not exist
pers_app_pinExist=The Personnel ID already exists
pers_app_dateError=The date format is incorrect, please refer to the correct format:2016-08-08
#api
pers_api_selectPhotoInvalid=The photo is invalid, please re-upload
pers_api_dateError=The date format is incorrect
pers_api_personNotExist=Person does not exist
pers_api_cardsPersSupport=The system does not open card; card is invalid.
pers_api_department_codeOrNameNotNull=Department code or department name cannot be empty
pers_api_deptSortNoIsNull=The department sort cannot be empty!
pers_api_deptSortNoError=The value of department sorting must be between 1-999999!
pers_api_dataLimit=The current number of operations is {0}, exceeding the limit of {1}. Please operate in batches!
pers_api_cardTypeError=Card type error
#人员生物模板API
pers_api_fingerprintExisted=The fingerprint of the person already exists
pers_api_validtypeIncorrect=This validtype attribute value is incorrect
pers_api_dataNotExist=TemplateNo not exist
pers_api_templateNoRang=Please enter the correct templateNo value in the range 0-9
pers_api_templateIsNull=template cannot be empty!
pers_api_versionIsNumber=Version can only enter numbers!
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=Pin can only be a number!
#biotime
pers_h5_personAvatarNotNull=The avatar is empty
pers_h5_personMobileRepeat=Mobile number already exists
pers_h5_personEmailRepeat=The mailbox already exists
pers_h5_pwdIsRepetition=New and old password repetition
pers_h5_personIdNull=Staff id is empty
pers_h5_pinOrEmailIsNull=Please fill in the number and email address
pers_emailTitle_resetPassword=change Password
pers_emailContent_resetPassword=The link is valid for 24 hours, copy the link to the browser to change the password:
pers_h5_tokenIsNull=Voucher is empty
pers_h5_tokenError=Voucher error
pers_h5_oldPwdIsError=Old password is incorrectly filled in
pers_api_resetPasswordSuccess=password has been updated
pers_api_resetPasswordFail=Password change failed
pers_api_forgetPassword=forget password
pers_api_confirmSubmit=confirm submission
pers_api_confirmPwdCaution=Click [OK] to confirm the new password.
pers_api_pwdRule=The password must contain at least one symbol or number and be at least 8-12 characters long
pers_h5_personPinFormatNumber=Personnel number can only consist of numbers or letters
pers_h5_persEmailNoExist=Mailbox filling error
pers_h5_pageNull=Paging parameter error
pers_h5_personPinNotStartWithZero=Personnel number cannot start with 0
pers_h5_personPinTooLong=Personnel number is too long
pers_h5_personPinInValid=This number is already in use
pers_h5_imgSizeError=Please upload an image that is no larger than 10M!
pers_h5_confirmAndContinue=Confirm and Continue
#人脸抠图不及格错误
pers_face_poorResolution=Picture resolution below 80000 pixels
pers_face_noFace=No face detected
pers_face_manyFace=Multiple faces detected
pers_face_smallFace=Face ratio is too small
pers_face_notColor=The picture is a non-color picture
pers_face_seriousBlur=Image is blurred
pers_face_intensivelyLight=Image is heavily exposed
pers_face_badIllumination=Picture is too dark
pers_face_highNoise=Pictures with high noise
pers_face_highStretch=Overstretched face
pers_face_covered=Face is covered
pers_face_smileOpenMouth=Excessive smile
pers_face_largeAngle=The face deflection angle is too large
pers_face_criticalIllumination=Image brightness critical
pers_face_criticalLargeAngle=Critical face deflection angle
pers_face_validFailMsg=Face detection failed due to:
pers_face_failType=Face cutout failure type
pers_face_photoFormatError=The photo format is incorrect, please upload a JPG/PNG format file.
pers_face_notUpdateMsg=Failed to generate the face picture, do not update the face picture.
#健康申报
pers_health_enable=Enable the declaration of health information
pers_health_attrExposure=Any exposure to suspected cases
pers_health_attrSymptom=Any symptoms in the last 14 days
pers_health_attrVisitCity=City visited in past 14 days
pers_health_attrRemarks=Remarks on health
pers_health_symptomCough=Cough
pers_health_symptomFever=Fever
pers_health_symptomPolypena=Respiratory Issues
pers_health_declaration=Health Declaration
pers_health_aggrement=I have agreed with people who do not fill out the information as required will not be allowed to access, and visitors who failed to report it truthfully cannot continue their visiting and need to bear corresponding legal responsibilities.
pers_health_visitCity_notEmpty=City visited can not be empty!
pers_health_notAgree=Please check the agreement to continue.
#人员名单库
pers_personnal_list_manager=List manager
pers_personnal_list=List Library
pers_personnal_list_scheme=Domain mode
pers_personnal_list_name=List Library Name
pers_personnal_list_group_str_id=List group ID
pers_personnal_list_personCount=Number of persons
pers_personnal_list_tag=User-defined
pers_personnal_list_type=List Library Type
pers_personnallist_addPerson_repo=Add a person to the repo
pers_personnallist_sendPersonnallist=Distributed list library
pers_personnallist_sendPerson=SendPerson
pers_personnallist_notDel_existPerson=The list library contains persons and cannot be deleted
pers_personnallist_peopleInRoster=There are still people in the list library
pers_personnallist_associationNotExist=The association between the main device and the list library does not exist
pers_personnal_list_person=Personnal list person
pers_personnal_list_dev=List library permissions
pers_personnal_list_addDev=Add device
pers_personnal_list_name_isExist=The name already exists
pers_personnal_bannedList=Banned list library
pers_personnal_allowList=Allow List Library
pers_personnal_redList=Red List Library
pers_personnal_attGroup=Attendance Group
pers_personnal_passList=Pass List
pers_personnal_banList=Forbidden List
pers_personnal_visPassList=Visitor Pass List
pers_personnal_visBanList=Visitor Forbidden List
pers_personnal_databaseHasBeenDistributed=The selected list library has been issued and is not allowed to be deleted
pers_personnel_sendError_dueTo=Delivery failure Reason for failure:
pers_personnel_sendError_reson={0} exist in the banned list, please delete and add
#比对照片-样片示例
pers_examplePic_Tip=Comply the following requirements:
pers_examplePic_Tip1=1. White background with dark-coloured apparel.
pers_examplePic_Tip2=2. Electronic photos are in JPG, PNG, JPEG file format, the recommended pixel range: 480*640 < pixel < 1080*1920;
pers_examplePic_Tip3=3. The captured person should be eyes-open and with clearly seen iris.
pers_examplePic_Tip4=4. Plain face or smile is preferred, showing teeth is not preferred.
pers_examplePic_Tip5=5. The capture person should be clearly seen, natural in color, and without image obvious twist, no shadow, light spot or reflection in face or background, and appropriate contrast and lightness level.
pers_examplePic_description=Example Pic
pers_examplePic_error=Error Example:
pers_examplePic_error1=Excessive Smile(excessive smile)
pers_examplePic_error2=Light too dim
pers_examplePic_error3=Face too Small(resolution is too small)
pers_applogin_enabled=Enable App Login
pers_applogin_disable=Disable App Login
pers_applogin_status=App Login Enable Status