#pers common.
#here other module can also use the label from pers.
pers_module=Персонал
pers_common_addPerson=Додати персонал
pers_common_delPerson=Видалити персонал
pers_common_personCount=Кількість персоналу
pers_common_browsePerson=Перегляд персоналу
#左侧菜单
pers_person_manager=Персонал
pers_person=Особа
pers_department=Відділ
pers_leave=Звільнений персонал
pers_tempPerson=Очікує на розгляд
pers_attribute=Спеціальні атрибути
pers_card_manager=Керування картками
pers_card=Картка
pers_card_issue=Запис про видану картку
pers_wiegandFmt=Формат Wiegand
pers_position=Посада
#人员
pers_person_female=Жіноча
pers_person_male=Чоловіча
pers_person_pin=Персональний ID
pers_person_departmentChange=Налаштування відділу
pers_personDepartment_changeLevel=Дозвіл на зміну відділу
pers_person_gender=Стать
pers_person_detailInfo=Відомості про персонал
pers_person_accSet=Управління доступом
pers_person_accSetting=Налаштування контролю доступу
pers_person_attSet=Відвідування робочого часу
pers_person_eleSet=Керування ліфтом
pers_person_eleSetting=Налаштування керування ліфтом
pers_person_parkSet=Реєстрація номерів
pers_person_pidSet=Особистий сертифікат
pers_person_insSet=FaceKiosk
pers_person_aiSet=Face Intellect
pers_person_payrollSet=Налаштування зарплати
pers_person_psgSet=Налаштування проходу
pers_person_lockerSet=Налаштування шафки
pers_person_sisSet=Параметри перевірки безпеки
pers_person_vdbSet=Параметри візуального інтеркому
pers_person_firstName=Ім'я
pers_person_lastName=Прізвище
pers_person_name=Ім'я
pers_person_wholeName=Повне Ім'я
pers_person_fullName=ПІБ
pers_person_cardNum=Кількість карток
pers_person_deptNum=Кількість залучених відділів
pers_person_dataCount=Статистика
pers_person_regFinger=Відбиток пальця
pers_person_reg=Реєстрація
pers_person_password=Пароль перевірки пристрою
pers_person_personDate=Дата працевлаштування
pers_person_birthday=День народження
pers_person_mobilePhone=Мобільний телефон
pers_person_personDevAuth=Дозволи для осіб і пристроїв
pers_person_email=Email
pers_person_browse=Огляд
pers_person_authGroup=Рівні доступу
pers_person_setEffective=Установіть ефективний час
pers_person_attArea=Зона відвідування
pers_person_isAtt=Розрахунок відвідуваності
pers_person_officialStaff=Офіційний персонал
pers_person_probationStaff=Персонал на випробувальному терміні
pers_person_identity_category=Тип персоналу
pers_person_devOpAuth=Призначення пристроя експлуатації
pers_person_msg1=Фактичний номер картки та код сайту необхідно заповнити одночасно!
pers_person_msg2=Будь ласка, введіть 3-4 цифри.
pers_person_msg3=Помилка формату!
pers_person_imgPixel=(Оптимальний розмір 120*140).
pers_person_cardLengthDigit=Будь ласка, введіть номер.
pers_person_cardLengthHexadecimal=Будь ласка, введіть числа або літери abcdef!
pers_person_to=До
pers_person_templateCount=Кількість відбитків пальців
pers_person_biotemplateCount=Кількість біологічних шаблонів
pers_person_regFace=Обличчя
pers_person_regVein=Вена пальця
pers_person_faceTemplateCount=Кількість обличч
pers_person_VeinTemplateCount=Кількість вен пальця
pers_person_palmTemplateCount=Кількість долонь
pers_person_cropFace=Зображення обличчя
pers_person_cropFaceCount=Кількість зображення облич
pers_person_faceBiodataCount=Кількість біометричних данних облич
pers_person_irisCount=Кількість ірісів
pers_person_batchToDept=Новий відділ
pers_person_changeReason=Причина трансфера
pers_person_selectedPerson=Вибрана особа
pers_person_duressPwdError=Повторний пароль
pers_person_completeDelPerson=Видалити
pers_person_recover=Відновити
pers_person_nameNoComma=Не має містити кому.
pers_person_firstNameNotEmpty=Поле "Ім’я" не повинно бути порожнім.
pers_person_lastNameNotEmpty=Поле "Прізвище" не повинно бути порожнім.
pers_person_mobilePhoneValidate=Введіть дійсний номер мобільного телефону.
pers_person_phoneNumberValidate=Будь ласка введіть дійсний номер телефону.
pers_person_postcodeValidate=Введіть дійсний поштовий індекс.
pers_person_idCardValidate=Будь ласка, введіть дійсний номер ідентифікаційної картки.
pers_person_pwdOnlyLetterNum=Пароль може містити лише букви або цифри.
pers_person_certNumOnlyLetterNum=Номер сертифіката може містити лише літери або цифри.
pers_person_oldDeptEqualsNewDept=Новий відділ, який ви налаштували, не повинен збігатися з початковим відділом.
pers_person_disabled=Доступ вимкнено
pers_person_emailError=Будь ласка, введіть дійсну адресу електронної пошти.
pers_person_driverPrompt=Будь ласка, встановіть драйвер пристрою! Натисніть OK, щоб завантажити драйвер.
pers_person_readIDCardFailed=Помилка прокрутки!
pers_person_cardPrompt=Зберігайте ID картку в регіоні ...
pers_person_iDCardReadOpenFailed=Пристрій зчитування ідентифікаційних карт не виявлено!
pers_person_iDCardNotFound=ID картку не знайдено, спробуйте ще раз!
pers_person_nameValid=Підтримка Chinese, English, позначки, \'-\', \'_\'.
pers_person_nameValidForEN=Підтримка English, позначки, \'-\', \'_\', \'.\'.
pers_person_pinPrompt=Будь ласка, введіть символи англійського алфавіту, цифри.
pers_person_pinSet=Налаштування ID персоналу
pers_person_supportLetter=Підтримка букв
pers_person_cardsSupport=Кілька карток на особу
pers_person_SupportDefault=Автоматичне створення ідентифікатора персоналу
pers_person_noSpecialChar=В поле "Ім'я" не може вводити спеціальні символи!
pers_person_pinInteger=Будь ласка, введіть цифри.
pers_pin_noSpecialChar=Порядкові номери персоналу не можуть містити спеціальні символи!
pers_op_capture=Захоплення
pers_person_cardDuress=Номер картки повторюється
pers_person_pwdException=Виняток пароля
pers_person_systemCheckTip=Перевірте правильність формату інформації про персонал!
pers_person_immeHandle=Негайно обробіть
pers_person_cardSet=Налаштування картки
pers_person_tempPersonSet=Очікувані налаштування персоналу
pers_person_cardsReadMode=Режим читання карт
pers_person_cardsReadModeReadHead=Читання контролером
pers_person_cardsReadModeID180=Читати за ID180
pers_person_IDReadMode=Режим читання ID картки
pers_person_IDReadModeIDCardReader=Зчитувач ID карт
pers_person_IDReadModeTcpReadHead=Зчитувач TCP/IP 
pers_person_physicalNo=ID картка Фізичний номер картки
pers_person_physicalNoToCardNo=Використовуйте номер фізичної картки ID
pers_person_ReadIDCard=Прочитати ID картку
pers_person_templateBioTypeNumber=Номер типу біометричного шаблону
pers_person_templateValidType=Дійсність біометричного шаблону
pers_person_templateBioType=Тип біометричного шаблону
pers_person_templateVersion=Версія біометричного шаблону
pers_person_template=Біометричний шаблон
pers_person_templateNo=Біометричний шаблон №
pers_person_templateNoIndex=Індекс біометричних шаблонів
pers_person_templateDataUpdate=Оновлення даних, якщо існує біометричний шаблон:
pers_person_templateValidTypeNoNull=Термін дії біометричного шаблону не повинен бути порожнім!
pers_person_templateBioTypeNoNull=ID персоналу: {0}, Тип біометричного шаблону не має бути порожнім!
pers_person_templateVersionNoNull=ID персоналу: {0}, Версія біометричного шаблону не повинна бути порожньою!
pers_person_templateNoNull=ID персоналу: {0}, Біометричний шаблон не повинен бути порожнім!
pers_person_templateNoNoNull=ID персоналу: {0}, Біометричний шаблон No. не повинен бути порожнім!
pers_person_templateNoIndexNoNull=ID персоналу: {0}, Індекс біометричного шаблону не повинен бути порожнім!
pers_person_templateError=ID персоналу: {0}, Неправильний біометричний шаблон!
pers_person_bioDuress=Примушення
pers_person_universal=Загальний
pers_person_voice=Голосовий відбиток
pers_person_iris=Iris
pers_person_retina=Сітківка ока
pers_person_palmPrints=Долонь
pers_person_metacarpalVein=Венозна сітка долоні
pers_person_visibleFace=Видиме обличчя
pers_person_pinError=ID персоналу {0} не існує, неможливо обробити ці дані!
pers_person_pinException=Виняток для ID персоналу
pers_person_pinAutoIncrement=Автоматичне визначення ID персоналу
pers_person_resetSelfPwd=Скинути пароль
pers_person_picMaxSize=Роздільна здатність зображення зависока, тому роздільність рекомендована нижче {0}.
pers_person_picMinSize=Роздільна здатність зображення занадто низька, тому роздільну здатність рекомендується бути вище {0}.
pers_person_cropFaceShow=Переглянути зображення обличчя
pers_person_faceNoFound=Непізнане обличчя
pers_person_biometrics=Біометричний тип
pers_person_photo=Особисті фото
pers_person_visibleFaceTemplate=Шаблон видимого обличчя
pers_person_infraredFaceTemplate=Шаблон обличчя біля інфрачервоного випромінювання
pers_person_delBioTemplate=Видалити біометричні дані
pers_person_delBioTemplateSelect=Виберіть біологічний шаблон для видалення!
pers_person_infraredFace=Біля інфрачервоне обличчя
pers_person_notCard=Не включає картку
pers_person_notRegFinger=Не включає відбиток пальця
pers_person_notMetacarpalVein=Не включає вени долоні
pers_person_notRegVein=Не включає вени пальців
pers_person_notIris=Виключаючи іріс
pers_person_notInfraredFaceTemplate=Не включає шаблон обличчя ближнього інфрачервоного випромінювання
pers_person_notVisibleFaceTemplate=Не включає шаблон обличчя з видимим світлом
pers_person_notVisibleFacePhoto=Не містить видимих фотографій облич
pers_person_visibleFacePhoto=Видиме фото обличчя
pers_person_change=Коригування персоналу
pers_person_cropFaceUsePhoto=Бажаєте показувати фото порівняння як аватар?
pers_person_photoUseCropFace=Ви бажаєте використовувати аватар для створення фотографії порівняння?
pers_person_selectCamera=Виберіть фотоапарат
pers_person_delCropFaceMsg=Немає ніяких порівняльних фотографій, які слід вилучити!
pers_person_disabledNotOp=Людина була вимкнена і не може працювати!
pers_person_visiblePalm=Бачильна світлова пальма
pers_person_notVisiblePalm=Не включає видимої світлової пальми
pers_person_selectDisabledNotOp=У вибраному персоналі є інвалідний персонал, який не може працювати!
pers_person_photoUseCropFaceAndTempalte=Ви бажаєте використовувати аватари для створення порівняльних фотографій та шаблонів обличчя?
pers_person_photoUseTempalte=Ви бажаєте використовувати фото для створення шаблонів обличчя?
pers_person_createCropFace=Створити фотографії порівняння
pers_person_createFaceTempalte=Створити шаблони обличчя
pers_person_faceTempalte=Шаблони обличчя
pers_person_extractFaceTemplate=Вилучення шаблонів обличчя
pers_person_createSuccess=Успішно створено
pers_person_createFail=Спроба створення зазнала невдачі
pers_person_serverConnectWarn=Адреса сервера, ім’ я користувача і пароль не можуть бути порожні!
pers_person_serverOffline=Сервер вилучення шаблонів обличчя з мережі
pers_person_faceTemplateError1=Спроба визначення обличчя зазнала невдачі
pers_person_faceTemplateError2=Оклузія обличчя
pers_person_faceTemplateError3=Недостатньо ясності
pers_person_faceTemplateError4=Кут обличчя занадто великий
pers_person_faceTemplateError5=Не вдалося визначити живе
pers_person_faceTemplateError6=Спроба вилучення обличчя шаблону зазнала невдачі
pers_person_cropFaceNoExist=Фотографія порівняння не існує
pers_person_disableFaceTemplate=Функція вилучення шаблону обличчя не увімкнена, не вдалося вилучити шаблон!
pers_person_cropFacePhoto=Фотографія порівняння обличчя
pers_person_vislightPalmPhoto=Фотографія порівняння Palm
pers_person_serverOfflineWarn=Сервер видобування шаблонів обличчя вимкнено і не може увімкнути цю можливість!
pers_person_serverConnectInfo=Будь ласка, перевірте, чи сервер видобування шаблонів обличчя є онлайн?
pers_person_notModified=Номер мобільного телефону не можна змінювати.
pers_person_syncAcms=Синхронізуйте персонал з ACMS
pers_person_startUpdate=Почніть оновлювати інформацію про працівників.
pers_person_updateFailed=Оновлення інформації про працівників невдалось!
#控制器发卡
pers_person_readCard=Випуск картки з пристрою
pers_person_stopRead=Зупинити випуск
pers_person_chooseDoor=Виберіть двері
pers_person_readCarding=Видавання карти, будь ласка, спробуйте ще раз пізніше!
#抓拍照片
pers_capture_catchPhoto=Зробити фотографію
pers_capture_preview=Попередній перегляд
#部门
pers_dept_entity=Відділ
pers_dept_deptNo=Номер відділу
pers_dept_deptName=Назва відділу
pers_dept_parentDeptNo=Номер батьківського відділу
pers_dept_parentDeptName=Назва батьківського відділу
pers_dept_parentDept=Батьківський відділ
pers_dept_note=Якщо нового відділу немає у списку, зверніться до адміністратора, щоб повторно авторизувати користувача на редагування відділу!
pers_dept_exit=Містить
pers_dept_auth=Зв'язаний користувач системи
pers_dept_parentMenuMsg=Вищий відділ не може бути однаковим із нижчим відділом.
pers_dept_initDept=Головне
pers_dept_deptMarket=Відділ маркетингу
pers_dept_deptRD=Відділ розвитку
pers_dept_deptFinancial=Відділ фінансів
pers_dept_nameNoSpace=Назва відділу не може починатися або закінчуватися пробілами.
pers_dept_nameExist=Назва відділу вже існує
pers_dept_changeLevel=Чи переходити на рівень цього відділу
pers_dept_noSpecialChar=Номери відділів не можуть містити спеціальні символи!
pers_dept_NameNoSpecialChar=Назви відділів не можуть містити спеціальні символи!
pers_dept_noModifiedParent=Вищий відділ не може бути змінений!
#职位
pers_position_entity=Позиція
pers_position_code=Номер позиції
pers_position_name=Назва посади
pers_position_notExist=Позиції не існує!
pers_position_sortNo=Сортувати
pers_position_parentName=Батьківська позиція
pers_position_parentCode=Батьківський номер посади
pers_position_batchToPosition=Нова позиція
pers_position_nameExist=Ця назва позиції вже існує
pers_position_change=Змінити позицію
pers_position_parentMenuMsg=Батьківську позицію не можна встановити як підпозицію.
pers_position_nameNoSpace=Назва позиції не може починатися або закінчуватися пробілами.
pers_position_existSub={0} :Містить піддописи не можна видалити
pers_position_existPerson={0} :Персонал не може бути видаленний
pers_position_importTemplate=Шаблон імпорту роботи
pers_position_downloadTemplate=Завантажити шаблон імпорту
pers_position_codeNotEmpty=Номер позиції не може бути порожнім
pers_position_nameNotEmpty=Назва позиції не може бути порожньою
pers_position_nameNoSpecialChar=Назва позиції {0} не може містити спеціальні символи!
pers_position_noSpecialChar=Назва позиції {0} не може бути спеціальним символом!
pers_position_codeLength=Номер позиції {0} довжина перевищує 30 цифр
pers_position_nameLength=Дані, назва позиції яких {0} довше ніж {1}
pers_position_codeExist=Робота ID {0} вже існує
#证件
pers_cert_type=Тип сертифіката
pers_cert_number=Номер сертифіката
pers_cert_name=Назва сертифіката
pers_cert_numberExist=Номер сертифіката вже існує.
#导出
pers_export_allPersPerson=Усі особи
pers_export_curPersPerson=Поточна особа
pers_export_template=Експортувати шаблон
pers_export_personInfo=Експортний персонал
pers_export_personInfoTemplate=Завантажити шаблон імпорту персоналу
pers_export_personBioTemplate=Експортувати біометричний шаблон
pers_export_basicInfo=Основна інформація
pers_export_customAttr=Спеціальні атрибути
pers_export_templateComment=Ім'я поля, первинний ключ?унікальний?дозволяти нульові значення?({0},{1},{2},{3})
pers_export_templateFileName=Шаблон імпорту персоналу
pers_export_bioTemplateFileName=Біометричний шаблон персоналу
pers_export_deptInfo=Експортний відділ
pers_export_deptTemplate=Завантажити шаблон імпорту відділу
pers_export_deptTemplateFileName=Шаблон імпорту відділу
pers_export_personPhoto=Експорт фото персоналу
pers_export_allPhotos=Усі фотографії (вибрати всіх людей)
pers_export_selectPhotoToExport=Виберіть початковий та кінцевий ідентифікатор, щоб експортувати фотографії персоналу.
pers_export_fromId=Від ID
pers_export_toId=До
pers_export_certNumberComment=Тип сертифіката обов’язковий після заповнення номера сертифіката
pers_export_templateCommentName=Ім'я поля:({0})
pers_export_dataExist=Переконайтеся, що імпортовані дані вже існують у системі
pers_export_cardNoTip=Декілька номерів карт відокремлених &
pers_carNumber_importTip=Номерний знак (кілька номерних знаків& розділені)
#导入
pers_import_certNumberExist=номер сертифіката {0} вже існує.
pers_import_complete=Завершено
pers_import_password=Особистий пароль
pers_import_fail=Помилка {0} рядка: {1}
pers_import_overData=Ви імпортуєте людей: {0}; система може підтримувати імпорт лише 30 000 людей!
pers_import_pinTooLong=Ідентифікатор персоналу {0} задовгий!
pers_import_pinExist=Ідентифікатор персоналу {0} вже існує!
pers_import_pinIsRepeat=ID персоналу {0} повторюється!
pers_import_pinError=ID персоналу {0} помилка!
pers_import_pinSupportNumber=Персональний номер підтримує лише цифри! Номер персоналу: {0}
pers_import_pinNotSupportNonAlphabetic=ID персоналу не підтримує неанглійські літери! Номер персоналу: {0}
pers_import_pinNotNull=ID персоналу не повинен бути нульовим!
pers_import_pinStartWithZero=ID персоналу не може починатися з нуля!
pers_import_cardNoNotSupportLetter=Номер картки не підтримує літери!
pers_import_cardNoNotNull=Номер картки не може бути всіма нулями!
pers_import_cardNoStartWithZero=Номер картки не може починатися з нуля!
pers_import_cardTooLong=Номер картки {0} занадто довгий!
pers_import_cardExist=Номер картки {0} вже існує!
pers_import_personPwdOnlyNumber=Особистий пароль підтримує лише цифри (без літер)!
pers_import_personPwdTooLong=Особистий пароль {0} задовгий!
pers_import_personDuressPwd=Особистий пароль {0} повторюється!
pers_import_emailTooLong=Електронний лист {0} задовгий!
pers_import_nameTooLong=Ім’я {0} задовге!
pers_import_genderError=Помилка формату статі!
pers_import_personDateError=Помилка формату дати найму!
pers_import_createTimeError=Створена помилка формату часу!
pers_import_phoneError=Помилка формату номера телефону!
pers_import_phoneTooLong=Довжина номера телефону не може перевищувати 20 символів!
pers_import_emailError=Помилка формату електронної пошти!
pers_import_birthdayError=Помилка формату дати народження!
pers_import_nameError=Прізвище та ім’я не можуть містити спеціальних символів! Номер персоналу: {0}
pers_import_firstnameError=Прізвище чи ім’я не може містити ','!
pers_import_firstnameNotNull=Ім’я не повинно бути порожнім!
pers_import_dataCheck=Перевірка даних закінчена
pers_import_dataSaveFail=Не вдалося імпортувати дані!
pers_import_allSucceed=Усі дані імпортовано успішно!
pers_import_result=Успішно: {0}, Не вдалося: {1}.
pers_import_result2=Результат імпорту
pers_import_result3=Успішно: {0}, Оновлено: {1}, Не вдалося: {2}.
pers_import_notSupportFormat=Цей формат не підтримується!
pers_import_selectCorrectFile=Будь ласка, виберіть правильний файл.
pers_import_fileFormat=Формат файлу
pers_import_targetFile=Файл призначення
pers_import_startRow=Початкові рядки заголовка
pers_import_startRowNote=Перший рядок формату даних - це ім'я таблиці, другий рядок - заголовок, третій рядок - дані імпорту, перевірте файл, а потім імпортуйте.
pers_import_delimiter=Розмежувач
pers_import_importingDataFields=Поля бази даних
pers_import_dataSourceFields=Імпорт полів даних
pers_import_total=Всього
pers_import_dataUpdate=Оновіть наявний ідентифікатор персоналу в системі:
pers_import_dataIsNull=У файлі немає даних!
pers_import_deptNotExist=Відділу не існує!
pers_import_deptIsNotNull=Назва відділу не повинна бути пустою!
pers_import_pinNotEmpty=ID персоналу не може бути пустим!
pers_import_nameNotEmpty=Ім’я персоналу не може бути порожнім!
pers_import_siteCodeOnlyLetterNum=Помилка формату коду сайту.
pers_import_cardNoFormatErrors=Формат номера картки {0} неправильний
pers_import_cardsNotSupport=Функція [Кілька карток на особу] вимкнена, не можна програмувати більше однієї картки на людину.
pers_import_personInfo=Імпорт персоналу
pers_import_commentFormat=Формат коментаря неправильний!
pers_import_noComment=Дані в рядку {0} і стовпці {1} не коментуються.
pers_import_fieldRepeat=Дані в рядку {0} і стовпці {1}, назва поля в коментарі повторюється:{2}
pers_import_primaryKey=Має бути принаймні одне поле як первинний ключ.
pers_import_templateIsRepeat=Ідентифікатор персоналу: дані біошаблона для {0} дублюються!
pers_import_biologicalTemplate=Імпорт біометричного шаблону
pers_import_uploadFileSuccess=Завантаження успішно, починається синтаксичний аналіз даних, зачекайте...
pers_import_resolutionComplete=Дані проаналізовано, розпочато оновлення бази даних.
pers_import_mustField=Файл імпорту повинен містити стовпець {0}.
pers_import_bioTemplateSuccess=Імпортуйте біометричний шаблон, ці дані потрібно вручну синхронізувати з пристроєм з кожного бізнес-модуля.
pers_import_personPhoto=Імпорт фотографій персоналу
pers_import_opera_log=Журнал операцій
pers_import_error_log=Журнал помилок
pers_import_uploadFileSize=Будь ласка, завантажте файл розміром не більше {0}!
pers_import_uploadFileSizeLimit=Для одного імпорту завантажте файл розміром не більше 500 МБ!
pers_import_type=Режим імпорту
pers_import_photoType=Фото
pers_import_archiveType=Стиснутий пакет
pers_import_startUpload=Почніть завантаження
pers_import_addMore=Додати більше
pers_import_photoQuality=Якість фото
pers_import_original=Оригінал
pers_import_adaptive=Адаптивний
pers_import_adaptiveSize=(Розмір 480 * 640)
pers_import_totalNumber=Всього
pers_import_uploadTip=(Будь ласка, не видаляйте фото під час завантаження)
pers_import_addPhotoTip=Будь ласка, додайте фотографії для завантаження!
pers_import_selectPhotoTip=Виберіть фотографію, яку хочете завантажити!
pers_import_uploadResult=Завантажити зроблене фото
pers_import_pleaseSelectPhoto=Виберіть фотографію
pers_import_multipleSelectTip=Натисніть Ctrl, щоб зробити кілька виділень
pers_import_replacePhotoTip=Вибрана фотографія вже є у списку завантаження, замінити її?
pers_import_photoNamePinNotCorrespond=Невідповідність імені фотографії та ідентифікатора персоналу
pers_import_photoFormatRequirement=Будь ласка, назвіть фотографію з ідентифікатором персоналу. Правильний формат: JPG/GIF/BMP/PNG. Переконайтеся, що назва фотографії не містить спеціальних символів.
pers_import_filterTip=Деякі з вибраних фотографій неможливо попередньо переглянути, тому це може бути з таких причин:
pers_import_photoContainSpecialCharacters=Назва фотографії має спеціальні символи.
pers_import_photoFormatError=Неправильний формат фото.
pers_import_photoSelectNumber=Не вибирайте більше 3000 зображень за один імпорт!
pers_import_photoSelectNumberLimit=Не вибирайте більше ніж {0} зображень!
pers_import_fileMaxSize=Зображення завелике, завантажте файл зображення розміром менше 5 МБ.
pers_import_notUploadPhotoNumber=Можна завантажити не більше 3000 зображень!
pers_import_zipFileNotPhoto=У zip-файлі немає фотографії особи, виберіть ще раз та імпортуйте!
pers_import_personPlateRepeat=Кадровий номерний знак {0} дублікат!
pers_import_filePlateRepeat=Файл всередині номерного знака {0} дублікат!
pers_import_personPlateFormat=Формат номерного знака персоналу {0} неправильний!
pers_import_personPlateMax=Кількість номерних знаків перевищує максимальне значення 6!
pers_import_cropFaceFail=Не вдалося створити порівняльне фото!
pers_import_pinLeaved=ID персоналу: {0} залишив.
pers_import_exceedLicense=Імпорт заборонений для кількості людей, які перевищують ліцензію на програмне забезпечення.
pers_import_bioTemplateNotNull=ID персоналу, тип біометричного шаблону, ідентифікатор або індекс і вміст, версія не можуть бути пустими!
pers_import_certTypeNotNull=Номер персоналу: {0} Тип документа не може бути порожнім
pers_import_certTypeNotExist=Номер персоналу: {0} Тип документа не існує
pers_import_certNumNotNull=Номер персоналу: {0} Номер сертифіката не може бути порожнім
pers_import_certNumberTooLong=Рядок {0}: ідентифікаційний номер {1} задовгий!
pers_import_idNumberErrors=Рядок {0}: номер ідентифікаційної картки {1} неправильний!
pers_import_emailErrors=Рядок {0}: Помилка формату адреси електронної пошти {1}!
pers_import_emailIsExist=Адреса електронної пошти {0} вже існує!
pers_import_emailIsRepeat=Row {0}: Внутрішня електронна адреса файлу {1} ​​повторена!
pers_import_fileMobilePhoneRepeat=Рядок {0}: внутрішній номер мобільного телефону файлу {1} повторено!
pers_import_mobilePhoneErrors=Помилка формату номера мобільного телефону!
pers_import_hireDateError=Формат дати запису неправильний!
pers_import_selectPhotoType=Будь ласка, виберіть тип імпортованої фотографії!
pers_import_hireDateLaterCurrent=Дата найми не може бути пізніша за поточну дату!
pers_import_buildingNotExist=Будівля не існує!
pers_import_unitNotExist=Блок не існує!
pers_import_vdbInfoFail=Немає інформації про підрозділ під назвою {1} у будівлі {0}!
pers_import_vdbBuildingFail=Інформація про будівництво підрозділу {0} не може бути порожньою!
pers_import_vdbRoomNoFail=Номер кімнати має бути більшим за 0!
#人员离职
pers_person_leave=Звільнення
pers_dimission_date=Дата звільнення
pers_dimission_type=Тип звільнення
pers_dimission_reason=Причина звільнення
pers_dimission_volutary=Voluntary Redundancy
pers_dimission_dismiss=Звільнено
pers_dimission_resignat=Відставка
pers_dimission_shiftJob=Передача
pers_dimission_leave=Зберегти роботу без зарплати
pers_dimission_recovery=Відновлення
pers_dimission_sureToRecovery=Ви впевнені, що хочете виконати операцію відновлення?
pers_dimission_backCard=Картку повернули?
pers_dimission_isForbidAction=Негайно відключити права контролю доступу?
pers_dimission_writeInfomation=Введіть інформацію про звільнення
pers_dimission_pinRetain=Зберігати кадрове посвідчення на звільненого працівника?
pers_dimission_downloadTemplate=Завантажьте шаблон імпорту звільнених
pers_dimission_import=Імпорт звільнених
pers_dimission_importTemplate=Шаблон імпорту звільнення
pers_dimission_date_noNull=Поле для дати звільнення не може бути порожнім
pers_dimission_leaveType_noExist=Тип звільнення не існує
pers_dimission_dateFormat=Обов'язкове поле, формат часу yyyy-MM-dd, як от: 2020-07-22
pers_dimission_leaveType=Обов’язкове поле, наприклад: Voluntary Redundancy, Звільнено, Відставка, Передача
pers_dimission_forbidden=Вимкнути
pers_dimission_leaveType_noNull=Тип відпустки не може бути порожнім!
pers_dimission_person_noExist=Людина з відпуску не існує!
pers_dimission_date_error=Дата вилучення не заповнена або форматується неправильно
#临时人员
pers_tempPerson_audit=Огляд
pers_tempPerson_view=Переглянути
pers_tempPerson_waitReview=Чекає на перевірку адміністратором
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Номер картки
pers_card_state=Стан картки
pers_card_effect=Ефективний
pers_card_disabled=Недійсний
pers_card_past=Термін дії закінчився
pers_card_back=Картку повернуто
pers_card_change=Зміна карти
pers_card_note=Номер картки вже існує.
pers_card_numTooBig=Номер оригінальної картки задовгий.
pers_issueCard_entity=Картка видачі
pers_issueCard_operator=Оператор
pers_issueCard_operate=Дія
pers_issueCard_note=Емітенти карток, які працюють із зареєстрованими даними про персонал, але не з персоналом облікових записів реєстраційної картки!
pers_issueCard_date=Дата видачі картки
pers_issueCard_changeTime=Змінити час
pers_issueCard_cardValidate=Без доступу.
pers_issueCard_cardEmptyNote=Картка не повинна бути порожньою.
pers_issueCard_cardHasBeenIssued=Цю картку видано!
pers_issueCard_noCardPerson=Неоформлена особа
pers_issueCard_waitPerson=Випуск данних поточної особи
pers_issueCard_mc5000=Випуск карт MC5000
pers_batchIssCard_entity=Картка випуску партії
pers_batchIssCard_startPersNo=Початок ID персоналу
pers_batchIssCard_endPersNo=Кінець ID персоналу
pers_batchIssCard_issCardNum=Кількість випущених карток
pers_batchIssCard_notIssCardNum=Кількість осіб без виданої картки
pers_batchIssCard_generateList=Створити список
pers_batchIssCard_startRead=Старт читання
pers_batchIssCard_swipCard=Розташування карти для свайпа
pers_batchIssCard_sendCard=Пристрій
pers_batchIssCard_dispenCardIss=USB зчитувач
pers_batchIssCard_usbEncoder=USB кодер
pers_batchIssCard_note=ID персоналу підтримує лише введені значення та показує лише осіб, які не видали картки (макс. 300)!
pers_batchIssCard_startPinEmpty=Початок ID персоналу не повинен бути порожнім!
pers_batchIssCard_endPinEmpty=Кінець ID персоналу не повинен бути порожнім!
pers_batchIssCard_startPinLargeThanEndPin=ID початкового персоналу не може бути більшим за ID кінцевого персоналу!
pers_batchIssCard_numberParagraphNoPerson=Особа без виданої картки не існує в сегменті від початку до кінця!
pers_batchIssCard_inputCardNum=Введіть номер картки
pers_batchIssCard_cardLetter=Картка не підтримує літери!
pers_batchIssCard_cardNoTooLong=Довжина номера картки задовга!
pers_batchIssCard_issueWay=Спосіб реєстрації картки
pers_batchIssCard_noPersonList=Список персоналу ще не створений.
pers_batchIssCard_startReadCard=Почніть зчитувати картку
pers_batchIssCard_swipePosition=Позиція для проведення
pers_batchIssCard_chooseSwipePosition=Будь ласка, виберіть позицію для проведення.
pers_batchIssCard_readCardTip=Пристрій зчитує незареєстровану картку лише тоді, коли методом видачі є обраний зчитувач.
pers_batchIssCard_notIssCardNo=Кількість невипущених карток
pers_batchIssCard_totalNumOfCards=Загальна кількість карток
pers_batchIssCard_acms=Видавання карт ACMS
pers_lossCard_entity=Повідомлено про втрату картки
pers_lossCard_lost=Про цю картку повідомлено, не можна повторити операцію!
pers_losscard_note2=Після того, як ви внесете зміни, ви повинні провести цією карткою до зчитувача ліфта, щоб переконатися, що картки втратили дію в пристрої.
pers_revertCard_entity=Повторно активуйте втрачену картку
pers_revertCard_setReport=Спочатку повідомте про втрату картки!
pers_revertcard_note2=Після того, як ви внесете зміни, ви повинні провести цією карткою на зчитувачі ліфта, щоб переконатися, що ви можете знову використовувати картки.
pers_issueCard_success=Видача картки успішна!
pers_issueCard_error=Помилка видачі картки!
pers_cardData_error=Виняток формату даних картки для читання!
pers_analysis_error=Виняток для аналізу даних картки!
pers_cardOperation_error=Виняток операцій з карткою!
pers_cardPacket_error=Виняток з пакету команд роботи з карткою!
pers_card_write=Записати картку
pers_card_init=Ініціалізуйте картку
pers_card_loss=Втрачена карта
pers_card_revert=Повернути картку
pers_card_writeMgr=Записати картку упавління
pers_initCard_tip=Після ініціалізації карта стане порожньою!
pers_initCard_prepare=Готово до ініціалізації карти...
pers_initCard_process=Ініціалізація карти...
pers_initCard_success=Ініціалізація картки успішна
pers_mgrCard_prepare=Підготуйтеся до запису даних картки в систему керування...
pers_mgrCard_process=Записує дані картки...
pers_mgrCard_success=Успішний запис картки в систему
pers_userCard_prepare=Підготуйтеся до запису картки користувача...
pers_userCard_process=Запис даних картки користувача...
pers_userCard_success=Успішний запис картки користувача
pers_userCard_tip=Будь ласка, встановіть час початку та закінчення на сторінці редагування особи, а потім проведіть операцію запису картки.
pers_userCard_tip2=Дані дозволів пусті, не можна записувати картки.
pers_userCard_tip3=Пристрій, пов’язаний із групою повноважень, більше двох одиниць, втрата даних.
pers_writeCard_tip=Переконайтеся, що ви підключили кодер і встановили драйвер, а також вставте картку в кодер.
pers_writeMgrCard_tip=Кількість втрачених та повернутих карток не може перевищувати 18
pers_writeMgrCard_tip2=Номер втраченої та поверненої картки:
pers_card_writeToMgr=Картку записано в систему управління
pers_card_hex=Вид формату картки
pers_card_decimal=Десятковий
pers_card_Hexadecimal=Шістнадцятковий
pers_card_IssuedCommandFail=Помилка виданої команди:
pers_card_multiCard=Більше карток
pers_card_deputyCard=Вторинна карта
pers_card_deputyCardValid=Спочатку введіть основну картку!
pers_card_writePinFormat=Система може пов’язувати з карткою лише ідентифікатори персоналу без літер!
pers_card_notMoreThanSixteen=Кількість додаткових карток не може перевищувати 16.
pers_card_notDelAll=Неможливо видалити всі вторинні картки.
pers_card_maxCard=Номер картки не може перевищувати{0}.
pers_card_posUseCardNo=Основна картка особи використовується споживчим модулем. Будь ласка, перейдіть до споживчого модуля, щоб виконати операцію зняття картки!
pers_card_delFirst=Будь ласка, вилучіть номер виданої карти перед виданням карти!
pers_card_disablePersonWarn=У вибраному номері карти міститься неактивний персонал і його не можна використовувати!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Код сайту
pers_wiegandFmt_wiegandMode=Режим
pers_wiegandFmt_wiegandModeOne=Перший режим
pers_wiegandFmt_wiegandModeTwo=Другий режим
pers_wiegandFmt_isDefaultFmt=Авто
pers_wgFmt_entity=Формат Wiegand
pers_wgFmt_in=Вхідний формат Wiegand 
pers_wgFmt_out=Вихідний формат Wiegand 
pers_wgFmt_inType=Тип входу Wiegand
pers_wgFmt_outType=Тип виходу Wiegand
pers_wgFmt_wg=Формат Wiegand 
pers_wgFmt_totalBit=Загалом Bit
pers_wgFmt_oddPch=Перевірка непарної парності(o)
pers_wgFmt_evenPck=Перевірка на парність(e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=Код закладу(f)
pers_wgFmt_siteCode=Сайт код(s)
pers_wgFmt_manufactoryCode=Код виробника(m)
pers_wgFmt_firstParity=Перша перевірка на парність(p)
pers_wgFmt_secondParity=Друга перевірка на парність(p)
pers_wgFmt_cardFmt=Формат перевірки картки
pers_wgFmt_parityFmt=Формат перевірки парності
pers_wgFmt_startBit=Стартовий біт
pers_wgFmt_test=Тест формату картки
pers_wgFmt_error=Помилка формату картки!
pers_wgFmt_verify1=Загальний бітність не повина перевищувати 80!
pers_wgFmt_verify2=Довжина формату картки повинна дорівнювати загальній кількості бітів!
pers_wgFmt_verify3=Довжина формату парності повинна дорівнювати загальній кількості цифр!
pers_wgFmt_verify4=Ініціалізовані дані не можна видалити!
pers_wgFmt_verify5=Формат картки використовується, не можна видалити!
pers_wgFmt_verify6=Перший біт парності не може бути більшим за загальну кількість!
pers_wgFmt_verify7=Другий біт парності не може бути більшим за загальну кількість!
pers_wgFmt_verify8=Формат початку та максимальної довжини неправильний!
pers_wgFmt_verify9=Довжина формату чека картки не може перевищувати загальну кількість!
pers_wgFmt_verify10=Функція контрольної цифри картки не може перетинатися!
pers_wgFmt_verify11=Код сайту перевищує встановлений діапазон!
pers_wgFmt_verify=Перевірте
pers_wgFmt_unverify=Без перевірки
pers_wgFmt_atLeastDefaultFmt=Будь ласка, зберігайте принаймні один формат картки, що автоматично відповідає!
pers_wgFmt_defaultFmtError1=Існують інші формати картки з такими ж бітами номера картки, які не можна встановити на автоматичний відповідний формат картки!
pers_wgFmt_defaultFmtError2=Наявність картки однакового формату для автоматичного зіставлення, операція не вдається!
pers_wgFmt_cardFormats=Формати карток
pers_wgFmt_cardFormatTesting=Тестування форматів карток
pers_wgFmt_checkIsUsed=Формат картки використовується в {0} і не може бути видалений!
pers_wgFmt_supportDigitsNumber=Будь ласка, введіть кількість цифр, які підтримуються пристроєм
#选人控件
pers_widget_selectPerson=Виберіть персонал
pers_widget_searchType1=Запит
pers_widget_searchType2=Відділ
pers_widget_deptHint=Примітка: Імпорт усього персоналу вибраних відділів
pers_widget_noPerson=Персонал не обрано.
pers_widget_noDept=Будь ласка, виберіть відділ.
pers_widget_noDeptPerson=У вибраному відділі немає осіб, будь ласка, виберіть повторно!
#人员属性
pers_person_carPlate=Номерний знак
pers_person_socialSecurity=Номер соціального страхування
pers_person_msg4=Максимальна довжина не повинна перевищувати 20!
pers_person_msg5=Ім'я не може містити більше 50 символів!
pers_person_type=Тип особи
pers_person_reseCode=Пароль для самостійного входу
pers_person_IsSendMail=E-mail повідомлення
pers_person_inactive=Неактивний
pers_person_active=Активний
pers_person_employee=Співробітник
pers_person_isSendMailMsg=Щоб скористатися функцією [Повідомлення про подію], спочатку потрібно ввести електронну адресу.
pers_person_createTime=Створіть часові настройки
pers_person_pinFirstValid=Перший символ особового номера не може бути 8 або 9.
pers_person_attrValueValid=Значення поля не може повторюватися.
pers_person_attrValueDelimiterValid=Роздільник повинен бути посередині.
pers_person_isSendSMS=SMS повідомлення
pers_person_building=Назва будівлі
pers_person_unitName=Назва підрозділу
pers_person_roomNo=Номер кімнати
#动态属性
pers_attr_emp_type=Тип співробітника
pers_attr_street=Вулиця
pers_attr_nation=Національність
pers_attr_office_address=Адреса офісу
pers_attr_postcode=Поштовий індекс
pers_attr_office_phone=Офісний телефон
pers_attr_home_phone=Домашній телефон
pers_attr_job_title=Назва посади
pers_attr_birthplace=Місце народження
pers_attr_polit_status=Політичний статус
pers_attr_country=Країна
pers_attr_home_address=Домашня адреса
pers_attr_hire_type=Тип найму
pers_attr_inContract=Контрактник
pers_attr_outContract=Неконтрактний працівник
#属性自定义
pers_attribute_attrName=Відображуване ім'я
pers_attribute_attrValue=Значення атрибута
pers_attribute_controlType=Тип введення
pers_attribute_positionX=Рядок
pers_attribute_positionY=Колонка
pers_attribute_showTable=Відображення в списку осіб
pers_attrDefini_deletemsg=Ця властивість була використана. Ви впевнені, що хочете видалити його?
pers_attrDefini_reserved=Ім'я зарезервованого системою поля.
pers_attrDefini_msg1=Максимальна довжина не більше 30!
pers_attrDefini_msg2=Рядки стовпця вже існують, перейдіть на інше місце!
pers_attrDefini_attrValue_split=Використання \' ; \' роздільника.
pers_attrDefini_attrName=Назва атрибута
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=Id атрибута
pers_attrDefini_select=Розкривний список
pers_attrDefini_check=Широкий вибір
pers_attrDefini_radio=Єдиний вибір
pers_attrDefini_text=Текст
pers_attrDefini_maxCol=Не більше 2 колонок.
pers_attrDefini_maxLimit=Спеціальні атрибути досягли максимальної кількості!
pers_attrDefini_modControlType=Зміна типу введення очистить поточні дані поля для всього персоналу в системі. Продовжувати?
#leavePerson
pers_leavePerson_reinstated=Відновлення
#opExample
pers_example_newRecode=Здобуття нових записів
pers_example_allRecode=Доступ до всіх записів
pers_custField_StatisticalType=Статистичний тип
#人员参数修改
pers_param_isAudit=Увімкнути автоматичний аудит
pers_param_donotChangePin=Існуючий ідентифікатор персоналу містить літеру(и), не може вимкнути функцію [Листи підтримки].
pers_param_hexChangeWarn=Поточна система вже має номери карток, не може змінити режим відображення формату картки.
pers_param_cardsChangeWarn=Деякі люди з кількома картками в системі, не можуть вимкнути функцію [Кілька карток на людину].
pers_param_maxPinLength=Довжина нового ідентифікатора персоналу не може бути меншою за довжину існуючого ідентифікатора персоналу в системі.
pers_param_pinBeyondDevLength=Максимальна довжина ідентифікатора персоналу, яку підтримує пристрій у системі, становить {0}, будь ласка, введіть ціле число менше ніж {1}.
pers_param_cardBeyondDevLength=Максимальна довжина номера картки, яку підтримує пристрій у системі, становить {0}, введіть ціле число менше ніж {1}.
pers_param_checkIsExistNoAudit=У поточній системі є неперевірені реєстранти, і їх не можна змінити на автоматичну перевірку!
pers_param_noSupportPinLetter=У системі є пристрої, які не підтримують ідентифікатори персоналу, що містять літери, не можуть увімкнути функцію [Листи підтримки].
pers_param_changePinLettersTip=Номер персоналу підтримує автоматичне збільшення, не може змінювати режими чисельності персоналу
pers_param_changePinIncrementTip=Номер персони не може включати літери, не можна змінити режим номеру персоналу
pers_param_qrCode=QR-код підприємства
pers_param_employeeRegistrar=Увімкнути реєстрацію співробітників у хмарі
pers_param_downloadQRCodePic=Завантажте зображення QR-коду
pers_param_qrCodeUrl=URL-адреса QR-коду
pers_param_qrCodeUrlCreate=Реєстрація самообслуговування
pers_param_qrCodeUrlHref=Адреса сервера: порт
pers_param_pinSetWarn=У поточній системі вже є персонал, і режим номеру персоналу змінити неможливо.
pers_param_selfRegistration=Увімкніть самореєстрацію
pers_param_infoProtection=Захист конфіденційної особистої інформації
pers_param_infoProtectionWarnMsg=Після ввімкнення опції захисту особистої конфіденційної інформації, конфіденційні персональні дані, задіяні в цьому модулі, будуть десенсибілізовані або приховані, включаючи, але не обмежуючись іменами, номерами карток, ідентифікаційними номерами, фотографіями тощо.
pers_param_templateServer=Сервер вилучення обличчя шаблонів
pers_param_enableFacialTemplate=Увімкнути екстракцію обличчя шаблону
pers_param_templateServerAddr=Адреса сервера вилучення обличчя шаблону
pers_param_templateServerWarnInfo=Якщо буде позначено вилучення шаблонів обличчя, якщо сервер вилучення шаблонів обличчя буде онлайн і передано перевірку користувача, персонал типово вилучатиме шаблони обличчя під час порівняння фотографій; Якщо сервер вилучення шаблонів обличчя знаходиться у режимі вилучення, не вилучити шаблони обличчя!
pers_param_templateServerWarnInfo1=Під час увімкнення вилучення шаблону обличчя слід з’ єднати пристрій, який підтримує вилучення шаблону обличчя!
pers_param_templateServerOffline=Сервер вилучення шаблонів обличчя вимкнено і не може вилучити шаблони обличчя! Хочете продовжувати?
pers_param_faceServer=Служба порівняння обличчя сервера
pers_param_enableFaceVerify=Увімкнути порівняння сервера обличчя
pers_param_faceServerAddr=Адреса сервісу порівняння сервера Face
pers_param_faceServerSecret=Ключ служби порівняння сервера Face
#国籍
pers_person_nationality=Країна/Регіон
pers_nationality_angola=Ангола
pers_nationality_afghanistan=Афганістан
pers_nationality_albania=Албанія
pers_nationality_algeria=Алжир
pers_nationality_america=США
pers_nationality_andorra=Андора
pers_nationality_anguilla=Ангілья
pers_nationality_antAndBar=Антигуа і Барбуда
pers_nationality_argentina=Аргентина
pers_nationality_armenia=Вірменія
pers_nationality_ascension=Острови Вознесіння
pers_nationality_australia=Австралія
pers_nationality_austria=Австрія
pers_nationality_azerbaijan=Азербайджан
pers_nationality_bahamas=Багамські острови
pers_nationality_bahrain=Бахрейн
pers_nationality_bangladesh=Бангладеш
pers_nationality_barbados=Барбадос
pers_nationality_belarus=Білорусь
pers_nationality_belgium=Бельгія
pers_nationality_belize=Беліз
pers_nationality_benin=Бенін
pers_nationality_bermudaIs=Бермудські острови
pers_nationality_bolivia=Болівія
pers_nationality_botswana=Ботсвана
pers_nationality_brazil=Бразилія
pers_nationality_brunei=Брюней
pers_nationality_bulgaria=Болгарія 
pers_nationality_burkinaFaso=Буркіна-Фасо
pers_nationality_burma=Бірма
pers_nationality_burundi=Бурундія
pers_nationality_cameroon=Камеруе
pers_nationality_canada=Канада
pers_nationality_caymanIs=Кайманські острови
pers_nationality_cenAfrRepub=ЦАР
pers_nationality_chad=Чад
pers_nationality_chile=Чилі
pers_nationality_china=Китай
pers_nationality_colombia=Колумбія
pers_nationality_congo=Республіка Конго
pers_nationality_cookIs=Острова Кука
pers_nationality_costaRica=Костарикан
pers_nationality_cuba=Куба
pers_nationality_cyprus=Кіпр
pers_nationality_czechRep=Чеська Республіка
pers_nationality_denmark=Данія
pers_nationality_djibouti=Джібуті
pers_nationality_dominicaRep=Домініканська Республіка.
pers_nationality_ecuador=Еквадор
pers_nationality_egypt=Єгипет
pers_nationality_eISalvador=Сальвадор
pers_nationality_england=Британія
pers_nationality_estonia=Естонія 
pers_nationality_ethiopia=Ефіопія
pers_nationality_fiji=Фіджі
pers_nationality_finland=Фінляндія
pers_nationality_france=Франція
pers_nationality_freGui=Французька Гвіана
pers_nationality_gabon=Габона
pers_nationality_gambia=Гамбія
pers_nationality_georgia=Грузія
pers_nationality_germany=Германія
pers_nationality_ghana=Ганна
pers_nationality_gibraltarm=Гібралтар
pers_nationality_greece=Греція
pers_nationality_grenada=Гренада
pers_nationality_guam=Гуам
pers_nationality_guatemala=Гватемала
pers_nationality_guinea=Гвінея
pers_nationality_guyana=Гайана
pers_nationality_haiti=Гаїті
pers_nationality_honduras=Гондурас
pers_nationality_hungary=Угорщина
pers_nationality_iceland=Ісландія
pers_nationality_india=Індія
pers_nationality_indonesia=Індонезія
pers_nationality_iran=Іран
pers_nationality_iraq=Ірак
pers_nationality_ireland=Ірландія
pers_nationality_israel=Ізраїль
pers_nationality_italy=Італія
pers_nationality_ivoryCoast=Кот-д'Івуар
pers_nationality_jamaica=Ямайка
pers_nationality_japan=Японія
pers_nationality_jordan=Йорданія
pers_nationality_kenya=Кенія
pers_nationality_korea=Корея
pers_nationality_kuwait=Кувейт
pers_nationality_kyrgyzstan=Киргистан
pers_nationality_laos=Лаос
pers_nationality_latvia=Латвія
pers_nationality_lebanon=Ліван
pers_nationality_lesotho=Мезото
pers_nationality_liberia=Ліберія
pers_nationality_libya=Лівія
pers_nationality_liechtenstein=Ліхтенштейн
pers_nationality_lithuania=Литва
pers_nationality_luxembourg=Люксембург
pers_nationality_madagascar=Малагасія
pers_nationality_malawi=Малавія
pers_nationality_malaysia=Малайзія
pers_nationality_maldives=Мальдиви
pers_nationality_mali=Малі
pers_nationality_malta=Мальта
pers_nationality_marianaIs=Маріанськи острови
pers_nationality_martinique=Мартініка
pers_nationality_mauritius=Мавританія
pers_nationality_mexico=Мексика
pers_nationality_moldova=Молдова
pers_nationality_monaco=Монако
pers_nationality_montseIs=Острів Монтсеррат
pers_nationality_morocco=Морокко
pers_nationality_mozambique=Мозамбік
pers_nationality_namibia=Намібія
pers_nationality_nauru=Науран
pers_nationality_nepal=Непал
pers_nationality_netAnti=Нетеріанські Антильські острови
pers_nationality_netherlands=Нідерланди
pers_nationality_newZealand=Нова Зеландія
pers_nationality_nicaragua=Нікарауга
pers_nationality_niger=Нігер
pers_nationality_nigeria=Нігерія
pers_nationality_norKorea=Північна Корея
pers_nationality_norway=Норвегія
pers_nationality_oman=Оман
pers_nationality_pakistan=Пакістан
pers_nationality_panama=Панама
pers_nationality_papNewCui=Папуа Нова Гвінея
pers_nationality_paraguay=Парагвай
pers_nationality_peru=Перу
pers_nationality_philippines=Філіппіни
pers_nationality_poland=Польща
pers_nationality_frenPolyne=Французька Полінезія
pers_nationality_portugal=Португалія
pers_nationality_puerRico=Пуерто Ріко
pers_nationality_qatar=Катар
pers_nationality_reunion=Реюньон
pers_nationality_romania=Румунія
pers_nationality_russia=Росія
pers_nationality_saiLueia=Сент-Люсія
pers_nationality_saintVinc=Сент-Вінсент
pers_nationality_samoa_eastern=Східне Самоа
pers_nationality_samoa_western=Західне Самоа
pers_nationality_sanMarino=Сан-Марінезі
pers_nationality_saoAndPrinc=Сан-Томе і Принсипні
pers_nationality_sauArabia=Саудівська Аравія
pers_nationality_senegal=сенегальська
pers_nationality_seychelles=Сейшельські острови
pers_nationality_sieLeone=Сьєрра-Леоне
pers_nationality_singapore=Сінгапур
pers_nationality_slovakia=Словаччина
pers_nationality_slovenia=Словенія
pers_nationality_solomonIs=Соломонові острови
pers_nationality_somali=Сомалі
pers_nationality_souAfrica=Південна Африка
pers_nationality_spain=Іспанія
pers_nationality_sriLanka=Шрі-Ланка
pers_nationality_sudan=Судан
pers_nationality_suriname=Сурінам
pers_nationality_swaziland=Свазі
pers_nationality_sweden=Швеція
pers_nationality_switzerland=Швейцарія
pers_nationality_syria=Сирія
pers_nationality_tajikstan=Таджикістан
pers_nationality_tanzania=Танзанія
pers_nationality_thailand=Таіланд
pers_nationality_togo=Того
pers_nationality_tonga=Тонга
pers_nationality_triAndToba=Тринідад і Тобаго
pers_nationality_tunisia=Туніс
pers_nationality_turkey=Турція
pers_nationality_turkmenistan=Туркменістан
pers_nationality_uganda=Уганда
pers_nationality_ukraine=Україна
pers_nationality_uniArabEmira=ОАЕ
pers_nationality_uruguay=Уругвай
pers_nationality_uzbekistan=Узбекістан
pers_nationality_venezuela=Венесуелла
pers_nationality_vietnam=В'єтнам
pers_nationality_yemen=Ємен
pers_nationality_serbia=Сербія
pers_nationality_zimbabwe=Зімбабве
pers_nationality_zambia=Замбія
pers_nationality_aruba=Аруба
pers_nationality_bhutan=Бутан
pers_nationality_bosnia_herzegovina=Боснія і Герцеговина
pers_nationality_cambodia=Камбоджа
pers_nationality_congoD=Демократична Республіка Конго
pers_nationality_comoros=Союз Коморських островів
pers_nationality_capeVerde=Республіка Кабо-Верде
pers_nationality_croatia=Хорватія
pers_nationality_dominica=Домініка
pers_nationality_eritrea=Еритрея
pers_nationality_micronesia=Мікронезія
pers_nationalit_guineaBissau=Гвінея-Бісау
pers_nationalit_equatorialGuinea=Екваторіальна Гвінея
pers_nationalit_hongkong=Гонконг, Китай
pers_nationalit_virginIslands=Віргінські острови Сполучених Штатів
pers_nationalit_britishVirginIslands=Британські Віргінські острови
pers_nationalit_kiribati=Кірібаті
pers_nationalit_mongolia=Монголія
pers_nationalit_marshall=Маршаллов острів
pers_nationalit_macedonia=Македонія
pers_nationalit_montenegro=Чорногорія
pers_nationalit_mauritania=Мавританії
pers_nationalit_palestine=Палестина
pers_nationalit_palau=Палау
pers_nationalit_rwanda=Руанда
pers_nationalit_saintKittsNevis=Сент-Кітс і Невіс
pers_nationalit_timorLeste=Східний Тимор
pers_nationalit_taiwan=Тайвань, Китай
pers_nationalit_tuvalu=Тувалу
pers_nationalit_vanuatu=Вануату
#制卡
pers_person_cardprint=Роздрукувати картку
pers_cardTemplate_tempSelect=Шаблон картки
pers_cardTemplate_printerSelect=Принтер
pers_cardTemplate_front=Спереду
pers_cardTemplate_opposite=Назад
pers_cardTemplate_entryDate=Дата найму
pers_cardTemplate_photo=Фото
pers_cardTemplate_uploadFail=Не вдалося завантажити зображення!
pers_cardTemplate_jpgFormat=Підтримує лише завантаження зображень у форматі JPG!
pers_cardTemplate_printStatus=Статус друку
pers_cardTemplate_waiting=Очікування
pers_cardTemplate_printing=Друкування
pers_cardTemplate_printOption=Параметр друку
pers_cardTemplate_duplexPrint=Дуплексний друк
pers_cardTemplate_frontOnly=Только перед друком
#app
pers_app_delPers=На сервері немає людей, яких ви хочете видалити
pers_app_deptIsNull=Не вдається знайти номер або назву відділу, що відповідає відділу
pers_app_personNull=Особи не існує
pers_app_pinExist=ID персоналу вже існує
pers_app_dateError=Формат дати неправильний, зверніться до правильного формату: 2016-08-08
#api
pers_api_selectPhotoInvalid=Фото недійсне, завантажте ще раз
pers_api_dateError=Формат дати неправильний
pers_api_personNotExist=Особи не існує
pers_api_cardsPersSupport=Система не відкриває картку; картка недійсна.
pers_api_department_codeOrNameNotNull=Код відділу або назва відділу не може бути порожнім
pers_api_deptSortNoIsNull=Сортування відділу не може бути порожнім!
pers_api_deptSortNoError=Значення сортування відділів має бути між 1-999999!
pers_api_dataLimit=Поточна кількість операцій дорівнює {0}, перевищуючи межу {1}. Будь ласка, працюйте у партах!
pers_api_cardTypeError=Помилка типу карти
#人员生物模板API
pers_api_fingerprintExisted=Відбиток пальця людини вже існує
pers_api_validtypeIncorrect=Це неправильне значення атрибута дійсного типу
pers_api_dataNotExist=Шаблон № не існує
pers_api_templateNoRang=Будь ласка, введіть правильний шаблонне значення в діапазоні 0-9
pers_api_templateIsNull=шаблон не може бути порожнім!
pers_api_versionIsNumber=Версія може ввести лише числа!
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=Pin може бути тільки числом!
#biotime
pers_h5_personAvatarNotNull=Аватар порожній
pers_h5_personMobileRepeat=Номер мобільного телефону вже існує
pers_h5_personEmailRepeat=Поштова скринька вже існує
pers_h5_pwdIsRepetition=Повторення нового і старого пароля
pers_h5_personIdNull=ID персоналу порожній
pers_h5_pinOrEmailIsNull=Будь ласка, заповніть номер та адресу електронної пошти
pers_emailTitle_resetPassword=змінити пароль
pers_emailContent_resetPassword=Посилання дійсне 24 години, скопіюйте посилання в браузер, щоб змінити пароль:
pers_h5_tokenIsNull=Ваучер порожній
pers_h5_tokenError=Помилка ваучера
pers_h5_oldPwdIsError=Невірно введений старий пароль
pers_api_resetPasswordSuccess=пароль оновлено
pers_api_resetPasswordFail=Не вдалося змінити пароль
pers_api_forgetPassword=забути пароль
pers_api_confirmSubmit=підтвердити подання
pers_api_confirmPwdCaution=Натисніть [OK], щоб підтвердити новий пароль.
pers_api_pwdRule=Пароль повинен містити принаймні один символ або цифру і містити не менше 8-12 символів
pers_h5_personPinFormatNumber=Номер персоналу може складатися тільки з цифр або літер
pers_h5_persEmailNoExist=Помилка заповнення поштової скриньки
pers_h5_pageNull=Помилка параметра сторінки
pers_h5_personPinNotStartWithZero=Номер персоналу не може починатися з 0
pers_h5_personPinTooLong=Номер персоналу задовгий
pers_h5_personPinInValid=Цей номер уже використовується
pers_h5_imgSizeError=Будь ласка, завантажте зображення розміром не більше 10M!
pers_h5_confirmAndContinue=Підтверджувати і продовжити
#人脸抠图不及格错误
pers_face_poorResolution=Роздільна здатність зображення нижче 80000 пікселів
pers_face_noFace=Обличчя не виявлено
pers_face_manyFace=Виявлено кілька облич
pers_face_smallFace=Коефіцієнт обличчя замалий
pers_face_notColor=Зображення некольорове
pers_face_seriousBlur=Зображення розмито
pers_face_intensivelyLight=Зображення сильно експонується
pers_face_badIllumination=Зображення занадто темне
pers_face_highNoise=Картинки з високим рівнем шуму
pers_face_highStretch=Перенапружене обличчя
pers_face_covered=Обличчя закрите
pers_face_smileOpenMouth=Надмірна посмішка
pers_face_largeAngle=Кут відхилення обличчя занадто великий
pers_face_criticalIllumination=Яскравість зображення критична
pers_face_criticalLargeAngle=Критичний кут відхилення грані
pers_face_validFailMsg=Не вдалося розпізнати обличчя через:
pers_face_failType=Тип несправності вирізу торця
pers_face_photoFormatError=Формат фотографії неправильний, завантажте файл у форматі JPG/PNG.
pers_face_notUpdateMsg=Не вдалося створити зображення обличчя, не оновлюйте зображення обличчя.
#健康申报
pers_health_enable=Увімкніть декларацію інформації про стан здоров’я
pers_health_attrExposure=Будь-який контакт із підозрілими випадками
pers_health_attrSymptom=Будь-які симптоми за останні 14 днів
pers_health_attrVisitCity=Місто відвідане за останні 14 днів
pers_health_attrRemarks=Зауваження по здоров'ю
pers_health_symptomCough=Cough
pers_health_symptomFever=Лихоманка
pers_health_symptomPolypena=Проблеми з диханням
pers_health_declaration=Декларація про стан здоров'я
pers_health_aggrement=Я погодився з тим, що люди, які не заповнять інформацію відповідно до вимог, не будуть мати доступ, а відвідувачі, які не повідомили про це правдиво, не можуть продовжити відвідування і повинні нести відповідну юридичну відповідальність.
pers_health_visitCity_notEmpty=Відвідане місто не може бути порожнім!
pers_health_notAgree=Будь ласка, перевірте угоду, щоб продовжити.
#人员名单库
pers_personnal_list_manager=Менеджер списків
pers_personnal_list=Бібліотека списку
pers_personnal_list_scheme=Режим домену
pers_personnal_list_name=Список назви бібліотеки
pers_personnal_list_group_str_id=Ідентифікатор групи списку
pers_personnal_list_personCount=Кількість осіб
pers_personnal_list_tag=Визначений користувачем
pers_personnal_list_type=Список типів бібліотеки
pers_personnallist_addPerson_repo=Додайте людину до репо
pers_personnallist_sendPersonnallist=Бібліотека розподіленого списку
pers_personnallist_sendPerson=SendPerson
pers_personnallist_notDel_existPerson=Бібліотека списків містить осіб і не може бути видалена
pers_personnallist_peopleInRoster=У бібліотеці списку ще є люди
pers_personnallist_associationNotExist=Зв'язок між основним пристроєм і бібліотекою списків не існує
pers_personnal_list_person=Особа з кадрового розпису
pers_personnal_list_dev=Список дозволів бібліотеки
pers_personnal_list_addDev=Додати пристрій
pers_personnal_list_name_isExist=Назва вже існує
pers_personnal_bannedList=Бібліотека заборонених списків
pers_personnal_allowList=Дозволити бібліотеку списку
pers_personnal_redList=Бібліотека Червоної книги
pers_personnal_attGroup=Група відвідування
pers_personnal_passList=Список пропусків
pers_personnal_banList=Заборонений список
pers_personnal_visPassList=Список проходів відвідувачів
pers_personnal_visBanList=Список заборонених відвідувачів
pers_personnal_databaseHasBeenDistributed=Вибрану бібліотеку списку видано, її не можна видалити
pers_personnel_sendError_dueTo=Помилка доставки Причина збою:
pers_personnel_sendError_reson={0} є у списку заборонених, будь ласка, видаліть та додайте
#比对照片-样片示例
pers_examplePic_Tip=Повинен відповідати наступним вимогам:
pers_examplePic_Tip1=1. колір фону чисто білий, а персонал одягнений в темний одяг;
pers_examplePic_Tip2=2. Електронні фотографії у форматі JPG, PNG, JPEG, рекомендований діапазон пікселів: 480*640 < піксель < 1080*1920;
pers_examplePic_Tip3=3. Портрети на електронних фотографіях мають розплющити очі та дивитися прямо перед собою та переконатися, що зіниці чітко видно;
pers_examplePic_Tip4=4. Портрет на електронній фотографії повинен мати нейтральне вираз обличчя, можна посміхатися, але не показувати зуби;
pers_examplePic_Tip5=5. Портрет на електронній фотографії має бути чітким, з природним кольором, насиченими шарами та без явних спотворень. Жодних тіней, світлих ділянок або відблисків на портретних обличчях або фоні; контраст і яскравість доречні.
pers_examplePic_description=Правильний приклад
pers_examplePic_error=Приклад помилки:
pers_examplePic_error1=Перебільшений вираз (надмірна посмішка)
pers_examplePic_error2=Світло занадто темне
pers_examplePic_error3=Обличчя занадто маленьке (роздільна здатність занадто мала)
pers_applogin_enabled=Увімкнути вход до програми
pers_applogin_disable=Вимкнути вход до програми
pers_applogin_status=Стан увімкнення входу до програми