#pers common.
#here other module can also use the label from pers.
pers_module=人事
pers_common_addPerson=添加人员
pers_common_delPerson=删除人员
pers_common_personCount=人员数
pers_common_browsePerson=浏览人员
#左侧菜单
pers_person_manager=人员管理
pers_person=人员
pers_department=部门
pers_leave=离职人员
pers_tempPerson=待审核人员
pers_attribute=自定义属性
pers_card_manager=卡管理
pers_card=卡
pers_card_issue=发卡记录
pers_wiegandFmt=韦根格式
pers_position=职位
#人员
pers_person_female=女
pers_person_male=男
pers_person_pin=人员编号
pers_person_departmentChange=部门调整
pers_personDepartment_changeLevel=部门切换权限
pers_person_gender=性别
pers_person_detailInfo=详细信息
pers_person_accSet=门禁设置
pers_person_accSetting=门禁设置
pers_person_attSet=考勤设置
pers_person_eleSet=梯控设置
pers_person_eleSetting=梯控设置
pers_person_parkSet=车牌登记
pers_person_pidSet=人证设置
pers_person_insSet=信息屏设置
pers_person_aiSet=人脸感知设置
pers_person_payrollSet=薪资设置
pers_person_psgSet=通道设置
pers_person_lockerSet=储物柜设置
pers_person_sisSet=安检设置
pers_person_vdbSet=可视对讲设置
pers_person_firstName=名
pers_person_lastName=姓
pers_person_name=姓名
pers_person_wholeName=姓名
pers_person_fullName=全称
pers_person_cardNum=卡数量
pers_person_deptNum=涉及部门数量
pers_person_dataCount=数据统计
pers_person_regFinger=指纹
pers_person_reg=登记
pers_person_password=设备验证密码
pers_person_personDate=聘用日期
pers_person_birthday=出生日期
pers_person_mobilePhone=手机号码
pers_person_personDevAuth=人员设备权限
pers_person_email=邮箱
pers_person_browse=浏览
pers_person_authGroup=权限组
pers_person_setEffective=设置有效时间
pers_person_attArea=考勤区域
pers_person_isAtt=是否考勤
pers_person_officialStaff=正式员工
pers_person_probationStaff=试用员工
pers_person_identity_category=身份类别
pers_person_devOpAuth=设备操作权限
pers_person_msg1=物理卡号和区位码必须同时输入！
pers_person_msg2=请输入3-4位的数字。
pers_person_msg3=格式错误！
pers_person_imgPixel=(最佳尺寸为120×140)
pers_person_cardLengthDigit=请输入数字！
pers_person_cardLengthHexadecimal=请输入数字或abcdef字母！
pers_person_to=至
pers_person_templateCount=指纹数
pers_person_biotemplateCount=生物模板数
pers_person_regFace=面部
pers_person_regVein=指静脉
pers_person_faceTemplateCount=面部数
pers_person_VeinTemplateCount=指静脉数
pers_person_palmTemplateCount=掌静脉数
pers_person_cropFace=比对照片
pers_person_cropFaceCount=比对照片数
pers_person_faceBiodataCount=可见光面部数
pers_person_irisCount=虹膜数
pers_person_batchToDept=调动到的部门
pers_person_changeReason=调动原因
pers_person_selectedPerson=选择人员
pers_person_duressPwdError=密码重复
pers_person_completeDelPerson=删除
pers_person_recover=恢复
pers_person_nameNoComma=不能包含逗号
pers_person_firstNameNotEmpty=名字不能为空
pers_person_lastNameNotEmpty=姓氏不能为空
pers_person_mobilePhoneValidate=请输入有效的手机号码
pers_person_phoneNumberValidate=请输入有效的电话号码
pers_person_postcodeValidate=请输入有效的邮编号码
pers_person_idCardValidate=请输入有效的身份证号码
pers_person_pwdOnlyLetterNum=密码只能包含字母或者数字
pers_person_certNumOnlyLetterNum=证件号码只能包含字母或者数字
pers_person_oldDeptEqualsNewDept=原部门和调动部门相同
pers_person_disabled=禁止名单
pers_person_emailError=邮箱格式不正确
pers_person_driverPrompt=请安装身份证硬件驱动！点击确定下载驱动。
pers_person_readIDCardFailed=刷卡失败！
pers_person_cardPrompt=请将二代身份证放到读卡区域...
pers_person_iDCardReadOpenFailed=未检测到二代身份证阅读器！
pers_person_iDCardNotFound=未读取到身份证，请重新刷卡！
pers_person_nameValid=支持中文、英文、数字、“-”、“_”
pers_person_nameValidForEN=支持英文、数字、“-”、“_”、“.”
pers_person_pinPrompt=请输入由英文字母、数字组成的字符
pers_person_pinSet=人员编号设置
pers_person_supportLetter=支持字母
pers_person_cardsSupport=启用一人多卡
pers_person_SupportDefault=自动编号
pers_person_noSpecialChar=人员姓名不能输入特殊字符！
pers_person_pinInteger=请输入由数字组成的字符
pers_pin_noSpecialChar=人员编号不能包含特殊字符！
pers_op_capture=抓拍
pers_person_cardDuress=卡号重复
pers_person_pwdException=密码异常
pers_person_systemCheckTip=检查到人员信息有异常！
pers_person_immeHandle=立即处理
pers_person_cardSet=卡设置
pers_person_tempPersonSet=待审核人员设置
pers_person_cardsReadMode=卡号读取方式
pers_person_cardsReadModeReadHead=控制器读头
pers_person_cardsReadModeID180=ID180（读身份证物理卡号）
pers_person_IDReadMode=身份证读取方式
pers_person_IDReadModeIDCardReader=身份证阅读器
pers_person_IDReadModeTcpReadHead=TCP读头
pers_person_physicalNo=身份证物理卡号
pers_person_physicalNoToCardNo=身份证物理卡号当卡号
pers_person_ReadIDCard=读取身份证
pers_person_templateBioTypeNumber=生物模板类型编号
pers_person_templateValidType=生物特征是否有效
pers_person_templateBioType=生物模板类型
pers_person_templateVersion=生物模板版本
pers_person_template=生物特征内容
pers_person_templateNo=生物特征编号
pers_person_templateNoIndex=生物特征对应索引
pers_person_templateDataUpdate=生物模板存在时更新数据 :
pers_person_templateValidTypeNoNull=生物特征是否有效不能为空！
pers_person_templateBioTypeNoNull=人员编号：{0}，生物特征类型不能为空！
pers_person_templateVersionNoNull=人员编号：{0}，生物特征版本不能为空！
pers_person_templateNoNull=人员编号：{0}，生物特征内容不能为空！
pers_person_templateNoNoNull=人员编号：{0}，生物特征编号不能为空！
pers_person_templateNoIndexNoNull=人员编号：{0}，生物特征对应索引不能为空！
pers_person_templateError=人员编号：{0}，生物特征内容有误！
pers_person_bioDuress=是否胁迫
pers_person_universal=通用
pers_person_voice=声纹
pers_person_iris=虹膜
pers_person_retina=视网膜
pers_person_palmPrints=掌纹
pers_person_metacarpalVein=掌静脉
pers_person_visibleFace=可见光人脸
pers_person_pinError=不存在人员编号为 {0} 的人员！
pers_person_pinException=人员编号异常
pers_person_pinAutoIncrement=人员编号自动递增
pers_person_resetSelfPwd=重置自助登录密码
pers_person_picMaxSize=图片分辨率太高，分辨率建议在{0}以下。
pers_person_picMinSize=图片分辨率太低，分辨率建议在{0}以上。
pers_person_cropFaceShow=查看比对照片
pers_person_faceNoFound=未识别到人脸
pers_person_biometrics=生物识别类型
pers_person_photo=人员照片
pers_person_visibleFaceTemplate=可见光人脸模板
pers_person_infraredFaceTemplate=近红外人脸模板
pers_person_delBioTemplate=删除生物识别数据
pers_person_delBioTemplateSelect=请选择要删除的生物模板！
pers_person_infraredFace=近红外人脸
pers_person_notCard=不包含卡
pers_person_notRegFinger=不包含指纹
pers_person_notMetacarpalVein=不包含掌静脉
pers_person_notRegVein=不包含指静脉
pers_person_notIris=不包含虹膜
pers_person_notInfraredFaceTemplate=不包含近红外人脸模板
pers_person_notVisibleFaceTemplate=不包含可见光人脸模板
pers_person_notVisibleFacePhoto=不包含可见光人脸照片
pers_person_visibleFacePhoto=可见光人脸照片
pers_person_change=人员调整
pers_person_cropFaceUsePhoto=是否把比对照片作为头像显示？
pers_person_photoUseCropFace=是否使用头像生成比对照片？
pers_person_selectCamera=选择抓拍摄像头
pers_person_delCropFaceMsg=没有比对照片可删除！
pers_person_disabledNotOp=该人员已被禁用，无法操作！
pers_person_visiblePalm=可见光手掌
pers_person_notVisiblePalm=不包含可见光手掌
pers_person_selectDisabledNotOp=选择的人员中存在已被禁用人员，无法操作！
pers_person_photoUseCropFaceAndTempalte=是否使用头像生成比对照片、人脸模板？
pers_person_photoUseTempalte=是否使用照片生成人脸模板？
pers_person_createCropFace=生成比对照片
pers_person_createFaceTempalte=生成人脸模板
pers_person_faceTempalte=人脸模板
pers_person_extractFaceTemplate=提取人脸模板
pers_person_createSuccess=生成成功
pers_person_createFail=生成失败
pers_person_serverConnectWarn=服务器地址、用户名、密码不能为空！
pers_person_serverOffline=人脸模板提取服务器离线
pers_person_faceTemplateError1=探测人脸失败
pers_person_faceTemplateError2=人脸遮挡
pers_person_faceTemplateError3=清晰度不够
pers_person_faceTemplateError4=人脸角度太大
pers_person_faceTemplateError5=活体检测失败
pers_person_faceTemplateError6=人脸模板提取失败
pers_person_cropFaceNoExist=比对照片不存在
pers_person_disableFaceTemplate=未启用人脸模板提取功能，无法提取模板！
pers_person_cropFacePhoto=人脸比对照片
pers_person_vislightPalmPhoto=手掌比对照片
pers_person_serverOfflineWarn=人脸模板提取服务器离线，无法启用该功能！
pers_person_serverConnectInfo=请测试连接人脸模板提取服务器是否在线？
pers_person_notModified=手机号码不能修改
pers_person_syncAcms=同步人员到ACMS
pers_person_startUpdate=开始更新人员信息
pers_person_updateFailed=人员信息更新失败！
#控制器发卡
pers_person_readCard=控制器发卡
pers_person_stopRead=停止发卡
pers_person_chooseDoor=选择门
pers_person_readCarding=发卡中，请稍后重试！
#抓拍照片
pers_capture_catchPhoto=抓拍照片
pers_capture_preview=预览
#部门
pers_dept_entity=部门
pers_dept_deptNo=部门编号
pers_dept_deptName=部门名称
pers_dept_parentDeptNo=上级部门编号
pers_dept_parentDeptName=上级部门名称
pers_dept_parentDept=上级部门
pers_dept_note=如果新增的部门在部门列表中未能显示，请联系管理员到用户编辑中重新授权部门！
pers_dept_exit=存在
pers_dept_auth=绑定的系统用户
pers_dept_parentMenuMsg=上级部门不能是自身或其下级部门
pers_dept_initDept=部门名称
pers_dept_deptMarket=市场部
pers_dept_deptRD=研发部
pers_dept_deptFinancial=财务部
pers_dept_nameNoSpace=部门名称不能以空格开头或结尾！
pers_dept_nameExist=该部门名称已存在
pers_dept_changeLevel=是否切换为该部门的权限
pers_dept_noSpecialChar=部门编号不能包含特殊字符！
pers_dept_NameNoSpecialChar=部门名称不能包含特殊字符！
pers_dept_noModifiedParent=不能修改上级部门！
#职位
pers_position_entity=职位
pers_position_code=职位编号
pers_position_name=职位名称
pers_position_notExist=职位不存在！
pers_position_sortNo=排序编号
pers_position_parentName=上级职位
pers_position_parentCode=上级职位编号
pers_position_batchToPosition=调动到的职位
pers_position_nameExist=该职位名称已存在
pers_position_change=职位调整
pers_position_parentMenuMsg=上级职位不能是自身或其下级职位
pers_position_nameNoSpace=职位名称不能以空格开头或结尾！
pers_position_existSub={0} :含有子职位不能删除
pers_position_existPerson={0} :含有人员不能删除
pers_position_importTemplate=职位导入模板
pers_position_downloadTemplate=下载导入模板
pers_position_codeNotEmpty=职位编号不能为空
pers_position_nameNotEmpty=职位名称不能为空
pers_position_nameNoSpecialChar=职位名称 {0} 不能包含特殊字符！
pers_position_noSpecialChar=职位编号 {0} 不能特殊字符！
pers_position_codeLength=职位编号 {0} 长度超出30位
pers_position_nameLength=职位名称 {0} 长度超过 {1} 的数据
pers_position_codeExist=该职位编号 {0} 已存在
#证件
pers_cert_type=证件类型
pers_cert_number=证件号码
pers_cert_name=证件名
pers_cert_numberExist=证件号码已存在
#导出
pers_export_allPersPerson=全部人员
pers_export_curPersPerson=当前人员
pers_export_template=导出模板
pers_export_personInfo=导出人员信息
pers_export_personInfoTemplate=下载人员信息导入模板
pers_export_personBioTemplate=导出人员生物模板数据
pers_export_basicInfo=配置人员基本信息
pers_export_customAttr=配置自定义属性
pers_export_templateComment=字段名称，是否主键，是否唯一，是否允许空({0},{1},{2},{3})
pers_export_templateFileName=人员信息模板
pers_export_bioTemplateFileName=人员生物模板数据
pers_export_deptInfo=导出部门信息
pers_export_deptTemplate=导出部门模板
pers_export_deptTemplateFileName=部门模板
pers_export_personPhoto=导出人员照片
pers_export_allPhotos=所有照片(选择所有人员)
pers_export_selectPhotoToExport=选择开始人员编号和结束人员编号以导出人员照片
pers_export_fromId=人员编号从
pers_export_toId=到
pers_export_certNumberComment=填写证件号码后证件类型为必填项
pers_export_templateCommentName=字段名称:({0})
pers_export_dataExist=请确保导入数据在系统中已存在
pers_export_cardNoTip=多个卡号&隔开
pers_carNumber_importTip=车牌号码（多个车牌&隔开）
#导入
pers_import_certNumberExist=证件号码 {0} 已存在
pers_import_complete=完成
pers_import_password=人员密码
pers_import_fail=第{0}行失败 : {1}
pers_import_overData=当前导入人数为{0}，超过限制的30000条，请分批导入！
pers_import_pinTooLong=人员编号 {0} 超长！
pers_import_pinExist=人员编号 {0} 已存在！
pers_import_pinIsRepeat=人员编号 {0} 已重复！
pers_import_pinError=人员编号 {0} 格式错误！
pers_import_pinSupportNumber=人员编号只支持数字！人员编号为：{0}
pers_import_pinNotSupportNonAlphabetic=人员编号只支持数字和字母组合。人员编号为：{0}
pers_import_pinNotNull=人员编号不能全为0！
pers_import_pinStartWithZero=人员编号不能以0开头！
pers_import_cardNoNotSupportLetter=卡号不支持字母！
pers_import_cardNoNotNull=卡号不能全为0！
pers_import_cardNoStartWithZero=卡号不能以0开头！
pers_import_cardTooLong=卡号 {0} 超长！
pers_import_cardExist=卡号 {0} 已存在！
pers_import_personPwdOnlyNumber=人员密码仅支持数字！
pers_import_personPwdTooLong=人员密码 {0} 超长！
pers_import_personDuressPwd=人员密码 {0} 重复！
pers_import_emailTooLong=邮箱 {0} 超长！
pers_import_nameTooLong=姓名 {0} 超长！
pers_import_genderError=性别格式错误！
pers_import_personDateError=聘用日期格式错误！
pers_import_createTimeError=创建时间格式错误！
pers_import_phoneError=电话号码格式错误！
pers_import_phoneTooLong=电话号码长度超过20位！
pers_import_emailError=邮箱格式错误！
pers_import_birthdayError=出生日期格式错误！
pers_import_nameError=姓名不能包含特殊字符！人员编号为：{0}
pers_import_firstnameError=姓名不能包含逗号！
pers_import_firstnameNotNull=姓氏不能为空！
pers_import_dataCheck=数据检查结束！
pers_import_dataSaveFail=数据导入失败！
pers_import_allSucceed=数据全部导入成功！
pers_import_result=成功： {0} 条，失败：{1} 条。
pers_import_result2=导入结果
pers_import_result3=成功：{0} 条，更新：{1} 条，失败：{2} 条。
pers_import_notSupportFormat=暂时不支持此种格式！
pers_import_selectCorrectFile=请选择格式正确的文件！
pers_import_fileFormat=文件格式
pers_import_targetFile=目标文件
pers_import_startRow=表头起始行数
pers_import_startRowNote=数据格式第一行表名，第二行表头，第三行往后是导入数据，请核对文件后再导入。
pers_import_delimiter=分隔符
pers_import_importingDataFields=数据库字段
pers_import_dataSourceFields=导入报表字段
pers_import_total=共
pers_import_dataUpdate=人员编号存在时更新数据 :
pers_import_dataIsNull=文件中没有数据！
pers_import_deptNotExist=部门不存在！
pers_import_deptIsNotNull=部门名称不能为空！
pers_import_pinNotEmpty=人员编号不能为空！
pers_import_nameNotEmpty=人员姓名不能为空！
pers_import_siteCodeOnlyLetterNum=区位码格式错误。
pers_import_cardNoFormatErrors=卡号 {0} 格式错误。
pers_import_cardsNotSupport=当前系统不支持多卡
pers_import_personInfo=导入人员信息
pers_import_commentFormat=批注的格式不正确！
pers_import_noComment=第 {0} 行 {1} 列数据，没有批注
pers_import_fieldRepeat=第 {0} 行 {1} 列数据，批注中的字段名称重复: {2}
pers_import_primaryKey=至少要有一个字段为主键
pers_import_templateIsRepeat=人员编号：{0}的生物模板数据已重复！
pers_import_biologicalTemplate=导入人员生物模板数据
pers_import_uploadFileSuccess=上传文件成功，开始解析文件数据,请稍候...
pers_import_resolutionComplete=解析完毕，开始更新数据库。
pers_import_mustField=导入文件必须含有{0}的列。
pers_import_bioTemplateSuccess=导入生物模板数据，该数据需要到各业务模块手动同步数据才会下发到设备。
pers_import_personPhoto=导入人员照片
pers_import_opera_log=操作日志
pers_import_error_log=错误日志
pers_import_uploadFileSize=请上传大小不超过{0}的文件！
pers_import_uploadFileSizeLimit=单次导入请上传大小不超过500M的文件！
pers_import_type=导入方式
pers_import_photoType=照片
pers_import_archiveType=压缩包
pers_import_startUpload=开始上传
pers_import_addMore=添加更多
pers_import_photoQuality=照片质量
pers_import_original=原始
pers_import_adaptive=自适应
pers_import_adaptiveSize=(大小 480 * 640)
pers_import_totalNumber=总数
pers_import_uploadTip=(请勿在上传时删除照片)
pers_import_addPhotoTip=请清除之前处理过的照片或者重新上传新照片！
pers_import_selectPhotoTip=请先选择要上传的照片！
pers_import_uploadResult=图片上传结果
pers_import_pleaseSelectPhoto=请选择照片
pers_import_multipleSelectTip=按下Ctrl键进行多项选择
pers_import_replacePhotoTip=等待上传的图片中已经存在选中图片,确定替换吗？
pers_import_photoNamePinNotCorrespond=原因：照片命名和人员编号不对应
pers_import_photoFormatRequirement=请使用人员编号为照片命名。 正确的格式是JPG/PNG。确保照片名称不包含特殊字符。
pers_import_filterTip=所选的部分照片无法预览，可能有以下原因：
pers_import_photoContainSpecialCharacters=照片名称有特殊字符
pers_import_photoFormatError=照片格式不正确
pers_import_photoSelectNumber=单次导入请勿选择超过3000张图片！
pers_import_photoSelectNumberLimit=请勿选择超过{0}张图片！
pers_import_fileMaxSize=图片太大，请上传小于5M的图片文件。
pers_import_notUploadPhotoNumber=未上传图片不得超过3000张！
pers_import_zipFileNotPhoto=压缩包中没有人员照片，请重新选择后导入！
pers_import_personPlateRepeat=人员车牌 {0} 重复！
pers_import_filePlateRepeat=文件内部车牌 {0} 重复！
pers_import_personPlateFormat=人员车牌 {0} 格式不正确！
pers_import_personPlateMax=人员车牌超过最大数6个！
pers_import_cropFaceFail=生成比对照片失败！
pers_import_pinLeaved=人员编号：{0}已离职。
pers_import_exceedLicense=超出软件许可的人员数量，不允许导入。
pers_import_bioTemplateNotNull=人员编号,生物类型，生物id,生物索引，内容，版本不允许为空！
pers_import_certTypeNotNull=人员编号为：{0} 证件类型不能为空
pers_import_certTypeNotExist=人员编号为：{0} 证件类型不存在
pers_import_certNumNotNull=人员编号为：{0} 证件号码不能为空
pers_import_certNumberTooLong=第{0}行:证件号码 {1} 超长！
pers_import_idNumberErrors=第{0}行:身份证号码 {1} 格式错误！
pers_import_emailErrors=第{0}行:邮箱地址 {1} 格式错误！
pers_import_emailIsExist=邮箱地址 {0} 已存在！
pers_import_emailIsRepeat=第{0}行: 文件内部邮箱地址 {1} 已重复！
pers_import_fileMobilePhoneRepeat=第{0}行：文件内部手机号码 {1} 重复！
pers_import_mobilePhoneErrors=手机号码格式错误！
pers_import_hireDateError=入职日期格式错误！
pers_import_selectPhotoType=请选择导入照片类型！
pers_import_hireDateLaterCurrent=入职日期不能大于当前日期！
pers_import_buildingNotExist=楼栋不存在！
pers_import_unitNotExist=单元不存在！
pers_import_vdbInfoFail=楼栋{0}不存在名称为{1}的单元信息！
pers_import_vdbBuildingFail=单元{0}的楼栋信息不能为空！
pers_import_vdbRoomNoFail=房号只能是大于0的数字！
#人员离职
pers_person_leave=离职
pers_dimission_date=离职日期
pers_dimission_type=离职类型
pers_dimission_reason=离职原因
pers_dimission_volutary=自离
pers_dimission_dismiss=辞退
pers_dimission_resignat=辞职
pers_dimission_shiftJob=调离
pers_dimission_leave=留职停薪
pers_dimission_recovery=离职恢复
pers_dimission_sureToRecovery=你确定要执行离职恢复操作吗？
pers_dimission_backCard=是否归还卡？
pers_dimission_isForbidAction=是否关闭门禁？
pers_dimission_writeInfomation=填写离职信息
pers_dimission_pinRetain=离职人员保留编号
pers_dimission_downloadTemplate=下载离职人员导入模板
pers_dimission_import=导入离职人员
pers_dimission_importTemplate=离职人员导入模板
pers_dimission_date_noNull=离职日期不能为空
pers_dimission_leaveType_noExist=离职类型不存在
pers_dimission_dateFormat=必填字段，时间格式为yyyy-MM-dd，如：2020-07-22
pers_dimission_leaveType=必填字段，如：自离、辞退、辞职、调离
pers_dimission_forbidden=加入禁止名单
pers_dimission_leaveType_noNull=离职类型不能为空！
pers_dimission_person_noExist=离职人员不存在！
pers_dimission_date_error=离职日期未填写或格式不正确
#临时人员
pers_tempPerson_audit=审核
pers_tempPerson_view=查看
pers_tempPerson_waitReview=等待管理员审核
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=卡号
pers_card_state=卡状态
pers_card_effect=有效
pers_card_disabled=无效
pers_card_past=过期
pers_card_back=退卡
pers_card_change=换卡
pers_card_note=卡号已存在
pers_card_numTooBig=卡号超出
pers_issueCard_entity=发卡
pers_issueCard_operator=操作员
pers_issueCard_operate=操作类型
pers_issueCard_note=操作对象为已登记人事资料但未登记卡账号人员！
pers_issueCard_date=发卡日期
pers_issueCard_changeTime=修改时间
pers_issueCard_cardValidate=卡号不能有空格
pers_issueCard_cardEmptyNote=卡号不能为空
pers_issueCard_cardHasBeenIssued=此卡已发过卡！
pers_issueCard_noCardPerson=未发卡人员
pers_issueCard_waitPerson=本次发卡人员
pers_issueCard_mc5000=MC5000发卡
pers_batchIssCard_entity=批量发卡
pers_batchIssCard_startPersNo=起始人员编号
pers_batchIssCard_endPersNo=结束人员编号
pers_batchIssCard_issCardNum=已发卡数
pers_batchIssCard_notIssCardNum=未发卡人员数
pers_batchIssCard_generateList=生成人员列表
pers_batchIssCard_startRead=开始读取
pers_batchIssCard_swipCard=发卡位置
pers_batchIssCard_sendCard=读头
pers_batchIssCard_dispenCardIss=发卡器
pers_batchIssCard_usbEncoder=desfire发卡器
pers_batchIssCard_note=人员编号只支持输入数字，仅显示未发过卡的人员(最多300人)！
pers_batchIssCard_startPinEmpty=起始人员编号不能为空！
pers_batchIssCard_endPinEmpty=结束人员编号不能为空！
pers_batchIssCard_startPinLargeThanEndPin=起始人员编号不能大于结束人员编号！
pers_batchIssCard_numberParagraphNoPerson=该编号段内不存在未发卡人员！
pers_batchIssCard_inputCardNum=输入卡号
pers_batchIssCard_cardLetter=卡号不支持字母！
pers_batchIssCard_cardNoTooLong=卡号长度过长
pers_batchIssCard_issueWay=发卡方式
pers_batchIssCard_noPersonList=未生成人员列表
pers_batchIssCard_startReadCard=开始读卡
pers_batchIssCard_swipePosition=刷卡位置
pers_batchIssCard_chooseSwipePosition=请选择刷卡位置
pers_batchIssCard_readCardTip=发卡方式为读头时，仅读取设备中未注册的卡。
pers_batchIssCard_notIssCardNo=未发卡数
pers_batchIssCard_totalNumOfCards=总卡数
pers_batchIssCard_acms=ACMS发卡
pers_lossCard_entity=挂失
pers_lossCard_lost=卡已挂失，不能重复操作！
pers_losscard_note2=写入管理卡后需在梯控设备的读头上刷卡，挂失操作才会生效。
pers_revertCard_entity=解挂
pers_revertCard_setReport=请先挂失卡！
pers_revertcard_note2=写入管理卡后需在梯控设备的读头上刷此管理卡，解挂操作才会生效。
pers_issueCard_success=发卡成功！
pers_issueCard_error=发卡异常！
pers_cardData_error=读取卡数据格式异常！
pers_analysis_error=卡数据解析异常！
pers_cardOperation_error=卡操作异常！
pers_cardPacket_error=卡操作命令封装异常！
pers_card_write=写卡
pers_card_init=初始化卡
pers_card_loss=挂失卡
pers_card_revert=解挂卡
pers_card_writeMgr=写管理卡
pers_initCard_tip=初始化后，该卡将会变成一张空白卡！
pers_initCard_prepare=准备初始化卡...
pers_initCard_process=正在初始化卡...
pers_initCard_success=初始化卡成功
pers_mgrCard_prepare=准备写管理卡数据...
pers_mgrCard_process=正在写管理卡数据...
pers_mgrCard_success=写管理卡成功
pers_userCard_prepare=准备写用户卡...
pers_userCard_process=正在写用户卡数据...
pers_userCard_success=写用户卡成功
pers_userCard_tip=请在人员编辑页面设置开始时间和结束时间后再做写卡操作
pers_userCard_tip2=权限数据为空，不予写卡。
pers_userCard_tip3=权限关联设备超过两台，数据丢失。
pers_writeCard_tip=请确保连接发卡器或驱动已安装，并将卡放在发卡器上
pers_writeMgrCard_tip=挂失和解挂卡的数量不能超过18
pers_writeMgrCard_tip2=当前挂失卡和解挂卡的数量：
pers_card_writeToMgr=写入管理卡
pers_card_hex=卡格式显示
pers_card_decimal=十进制
pers_card_Hexadecimal=十六进制
pers_card_IssuedCommandFail=下发指令失败：
pers_card_multiCard=多卡登记
pers_card_deputyCard=副卡
pers_card_deputyCardValid=请先填写主卡号！
pers_card_writePinFormat=系统不支持写入带字母的人员编号到卡中，需为数字类型！
pers_card_notMoreThanSixteen=副卡不能超过16个。
pers_card_notDelAll=不能删除所有副卡。
pers_card_maxCard=卡号大小不能超过{0}！
pers_card_posUseCardNo=该人员的主卡正在被消费模块使用，请去消费模块执行退卡操作！
pers_card_delFirst=请先删除已发卡号，再进行发卡！
pers_card_disablePersonWarn=选择的卡号中存在所属人员为禁用人员，无法操作！
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=区位码
pers_wiegandFmt_wiegandMode=模式
pers_wiegandFmt_wiegandModeOne=模式一
pers_wiegandFmt_wiegandModeTwo=模式二
pers_wiegandFmt_isDefaultFmt=自动匹配
pers_wgFmt_entity=韦根卡格式
pers_wgFmt_in=韦根输入格式
pers_wgFmt_out=韦根输出格式
pers_wgFmt_inType=韦根输入类型
pers_wgFmt_outType=韦根输出类型
pers_wgFmt_wg=韦根
pers_wgFmt_totalBit=总位数
pers_wgFmt_oddPch=奇校验(o)
pers_wgFmt_evenPck=偶校验(e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=设备代码(f)
pers_wgFmt_siteCode=区位码(s)
pers_wgFmt_manufactoryCode=厂商代码(m)
pers_wgFmt_firstParity=第一个奇偶校验位(p)
pers_wgFmt_secondParity=第二个奇偶校验位(p)
pers_wgFmt_cardFmt=卡校验格式
pers_wgFmt_parityFmt=奇偶校验格式
pers_wgFmt_startBit=起始位
pers_wgFmt_test=测试韦根格式
pers_wgFmt_error=韦根格式错误！
pers_wgFmt_verify1=总位数不能超过80！
pers_wgFmt_verify2=卡校验格式长度必须等于总位数！
pers_wgFmt_verify3=奇偶校验格式长度必须等于总位数！
pers_wgFmt_verify4=初始化的数据不能删除！
pers_wgFmt_verify5=该卡格式正在使用，不能删除！
pers_wgFmt_verify6=第一个奇偶校验位不能大于总位数！
pers_wgFmt_verify7=第二个奇偶校验位不能大于总位数！
pers_wgFmt_verify8=起始位与最大长度位格式不正确！
pers_wgFmt_verify9=校验格式超过总位数！
pers_wgFmt_verify10=卡校验位数功能不能交叉！
pers_wgFmt_verify11=区位码超过设置范围！
pers_wgFmt_verify=校验
pers_wgFmt_unverify=不校验
pers_wgFmt_atLeastDefaultFmt=请至少保留一个自动匹配的卡格式！
pers_wgFmt_defaultFmtError1=相同位数中含有其他格式的卡格式，无法设置为自动匹配！
pers_wgFmt_defaultFmtError2=存在相同的卡格式为自动匹配，操作失败！
pers_wgFmt_cardFormats=卡格式
pers_wgFmt_cardFormatTesting=卡格式测试
pers_wgFmt_checkIsUsed=卡格式在{0}中使用，不能删除！
pers_wgFmt_supportDigitsNumber=请输入设备支持的位数长度
#选人控件
pers_widget_selectPerson=选择人员
pers_widget_searchType1=条件查询
pers_widget_searchType2=部门
pers_widget_deptHint=注：导入所选部门下所有人员
pers_widget_noPerson=没有选择任何人
pers_widget_noDept=请选择部门
pers_widget_noDeptPerson=所选部门下没有人员，请重新选择部门！
#人员属性
pers_person_carPlate=车牌号
pers_person_socialSecurity=社保号
pers_person_msg4=最大长度不能超过20位！
pers_person_msg5=名字长度不能超过50位！
pers_person_type=人员类型
pers_person_reseCode=自助密码
pers_person_IsSendMail=邮件通知
pers_person_inactive=停用
pers_person_active=有效
pers_person_employee=员工
pers_person_isSendMailMsg=事件通知必须填写邮箱
pers_person_createTime=创建时间
pers_person_pinFirstValid=人员编号首位不能为8、9。
pers_person_attrValueValid=字段值不能重复
pers_person_attrValueDelimiterValid=分号要放在中间
pers_person_isSendSMS=短信通知
pers_person_building=楼栋名称
pers_person_unitName=单元名称
pers_person_roomNo=房号
#动态属性
pers_attr_emp_type=员工类型
pers_attr_street=街道
pers_attr_nation=民族
pers_attr_office_address=公司地址
pers_attr_postcode=邮编
pers_attr_office_phone=公司电话
pers_attr_home_phone=家庭电话
pers_attr_job_title=职称
pers_attr_birthplace=出生地
pers_attr_polit_status=政治面貌
pers_attr_country=地区
pers_attr_home_address=家庭地址
pers_attr_hire_type=雇佣类型
pers_attr_inContract=合同内
pers_attr_outContract=合同外
#属性自定义
pers_attribute_attrName=显示名称
pers_attribute_attrValue=字段取值
pers_attribute_controlType=输入类型
pers_attribute_positionX=行
pers_attribute_positionY=列
pers_attribute_showTable=显示在人员列表
pers_attrDefini_deletemsg=该属性已被使用，是否确认删除？
pers_attrDefini_reserved=系统预留字段名
pers_attrDefini_msg1=最大长度不能超过30位！
pers_attrDefini_msg2=该行该列已经存在，请更换到其他位置！
pers_attrDefini_attrValue_split=请用英文分号“ ; ”分隔
pers_attrDefini_attrName=属性名
pers_attrDefini_sql=sql语句
pers_attrDefini_attrId=属性编号
pers_attrDefini_select=下拉列表
pers_attrDefini_check=多选
pers_attrDefini_radio=单选
pers_attrDefini_text=输入框
pers_attrDefini_maxCol=不能超过2列
pers_attrDefini_maxLimit=自定义属性已达最大数量限制！
pers_attrDefini_modControlType=变更输入类型会清空系统所有人员当前字段的数据，是否继续？
#leavePerson
pers_leavePerson_reinstated=复职
#opExample
pers_example_newRecode=获取新记录
pers_example_allRecode=获取所有纪录
pers_custField_StatisticalType=统计类型
#人员参数修改
pers_param_isAudit=启用自动审核
pers_param_donotChangePin=已有人员编号包含字母，不能更改人员编号模式。
pers_param_hexChangeWarn=当前系统已存在卡号，不能更改卡格式显示。
pers_param_cardsChangeWarn=当前系统已存在多卡，不能更改多卡设置。
pers_param_maxPinLength=新设置的人员编号的长度不能小于已存在的人员编号的长度。
pers_param_pinBeyondDevLength=系统中存在设备支持的人员编号最大长度为{0}，请输入小于{1}的整数！
pers_param_cardBeyondDevLength=系统中存在设备支持的卡号最大长度为{0}，请输入小于{1}的整数！
pers_param_checkIsExistNoAudit=当前系统存在未审核的登记人员，不能修改为自动审核！
pers_param_noSupportPinLetter=系统中存在设备不支持包含字母的人员编号，不能更改人员编号模式。
pers_param_changePinLettersTip=人员编号支持自动递增，不能修改人员编号模式
pers_param_changePinIncrementTip=人员编号支持包含字母，不能修改人员编号模式
pers_param_qrCode=企业二维码
pers_param_employeeRegistrar=启用云员工登记
pers_param_downloadQRCodePic=下载二维码图片
pers_param_qrCodeUrl=二维码Url
pers_param_qrCodeUrlCreate=人员自助登记
pers_param_qrCodeUrlHref=服务器地址:端口
pers_param_pinSetWarn=当前系统已存在人员，不能更改人员编号模式。
pers_param_selfRegistration=启用自助登记
pers_param_infoProtection=个人敏感信息保护
pers_param_infoProtectionWarnMsg=启用个人敏感信息安全保护选项之后，本模块涉及个人敏感数据将进行脱敏或模糊化处理，包括且不仅限于如姓名、卡号、证件号、照片等。
pers_param_templateServer=人脸模板提取服务器
pers_param_enableFacialTemplate=启用人脸模板提取
pers_param_templateServerAddr=人脸模板提取服务器地址
pers_param_templateServerWarnInfo=启用人脸模板提取时，当人脸模板提取服务器在线且用户校验通过时，人员比对照片默认需要提取人脸模板；当人脸模板提取服务器离线模式时，不提取人脸模板！
pers_param_templateServerWarnInfo1=启用人脸模板提取时，需接入支持人脸模板提取功能的设备！
pers_param_templateServerOffline=人脸模板提取服务器离线，无法提取人脸模板！是否继续？
pers_param_faceServer=人脸后台比对服务
pers_param_enableFaceVerify=启用人脸后台比对
pers_param_faceServerAddr=人脸后台比对服务地址
pers_param_faceServerSecret=人脸后台比对服务秘钥
#国籍
pers_person_nationality=国家/地区
pers_nationality_angola=安哥拉
pers_nationality_afghanistan=阿富汗
pers_nationality_albania=阿尔巴尼亚
pers_nationality_algeria=阿尔及利亚
pers_nationality_america=美国
pers_nationality_andorra=安道尔共和国
pers_nationality_anguilla=安圭拉岛
pers_nationality_antAndBar=安提瓜和巴布达
pers_nationality_argentina=阿根廷
pers_nationality_armenia=亚美尼亚
pers_nationality_ascension=阿森松
pers_nationality_australia=澳大利亚
pers_nationality_austria=奥地利
pers_nationality_azerbaijan=阿塞拜疆
pers_nationality_bahamas=巴哈马
pers_nationality_bahrain=巴林
pers_nationality_bangladesh=孟加拉国
pers_nationality_barbados=巴巴多斯
pers_nationality_belarus=白俄罗斯
pers_nationality_belgium=比利时
pers_nationality_belize=伯利兹
pers_nationality_benin=贝宁
pers_nationality_bermudaIs=百慕大群岛
pers_nationality_bolivia=玻利维亚
pers_nationality_botswana=博茨瓦纳
pers_nationality_brazil=巴西
pers_nationality_brunei=文莱
pers_nationality_bulgaria=保加利亚
pers_nationality_burkinaFaso=布基纳法索
pers_nationality_burma=缅甸
pers_nationality_burundi=布隆迪
pers_nationality_cameroon=喀麦隆
pers_nationality_canada=加拿大
pers_nationality_caymanIs=开曼群岛
pers_nationality_cenAfrRepub=中非共和国
pers_nationality_chad=乍得
pers_nationality_chile=智利
pers_nationality_china=中国
pers_nationality_colombia=哥伦比亚
pers_nationality_congo=刚果（布）
pers_nationality_cookIs=库克群岛
pers_nationality_costaRica=哥斯达黎加
pers_nationality_cuba=古巴
pers_nationality_cyprus=塞浦路斯
pers_nationality_czechRep=捷克
pers_nationality_denmark=丹麦
pers_nationality_djibouti=吉布提
pers_nationality_dominicaRep=多米尼加共和国
pers_nationality_ecuador=厄瓜多尔
pers_nationality_egypt=埃及
pers_nationality_eISalvador=萨尔瓦多
pers_nationality_england=英国
pers_nationality_estonia=爱沙尼亚
pers_nationality_ethiopia=埃塞俄比亚
pers_nationality_fiji=斐济
pers_nationality_finland=芬兰
pers_nationality_france=法国
pers_nationality_freGui=法属圭亚那
pers_nationality_gabon=加蓬
pers_nationality_gambia=冈比亚
pers_nationality_georgia=格鲁吉亚
pers_nationality_germany=德国
pers_nationality_ghana=加纳
pers_nationality_gibraltarm=直布罗陀
pers_nationality_greece=希腊
pers_nationality_grenada=格林纳达
pers_nationality_guam=关岛
pers_nationality_guatemala=危地马拉
pers_nationality_guinea=几内亚
pers_nationality_guyana=圭亚那
pers_nationality_haiti=海地
pers_nationality_honduras=洪都拉斯
pers_nationality_hungary=匈牙利
pers_nationality_iceland=冰岛
pers_nationality_india=印度
pers_nationality_indonesia=印度尼西亚
pers_nationality_iran=伊朗
pers_nationality_iraq=伊拉克
pers_nationality_ireland=爱尔兰
pers_nationality_israel=以色列
pers_nationality_italy=意大利
pers_nationality_ivoryCoast=科特迪瓦
pers_nationality_jamaica=牙买加
pers_nationality_japan=日本
pers_nationality_jordan=约旦
pers_nationality_kenya=肯尼亚
pers_nationality_korea=韩国
pers_nationality_kuwait=科威特
pers_nationality_kyrgyzstan=吉尔吉斯坦
pers_nationality_laos=老挝
pers_nationality_latvia=拉脱维亚
pers_nationality_lebanon=黎巴嫩
pers_nationality_lesotho=莱索托
pers_nationality_liberia=利比里亚
pers_nationality_libya=利比亚
pers_nationality_liechtenstein=列支敦士登
pers_nationality_lithuania=立陶宛
pers_nationality_luxembourg=卢森堡
pers_nationality_madagascar=马达加斯加
pers_nationality_malawi=马拉维
pers_nationality_malaysia=马来西亚
pers_nationality_maldives=马尔代夫
pers_nationality_mali=马里
pers_nationality_malta=马耳他
pers_nationality_marianaIs=马里亚那群岛
pers_nationality_martinique=马提尼克
pers_nationality_mauritius=毛里求斯
pers_nationality_mexico=墨西哥
pers_nationality_moldova=摩尔多瓦
pers_nationality_monaco=摩纳哥
pers_nationality_montseIs=蒙特塞拉特岛
pers_nationality_morocco=摩洛哥
pers_nationality_mozambique=莫桑比克
pers_nationality_namibia=纳米比亚
pers_nationality_nauru=瑙鲁
pers_nationality_nepal=尼泊尔
pers_nationality_netAnti=荷属安的列斯
pers_nationality_netherlands=荷兰
pers_nationality_newZealand=新西兰
pers_nationality_nicaragua=尼加拉瓜
pers_nationality_niger=尼日尔
pers_nationality_nigeria=尼日利亚
pers_nationality_norKorea=朝鲜
pers_nationality_norway=挪威
pers_nationality_oman=阿曼
pers_nationality_pakistan=巴基斯坦
pers_nationality_panama=巴拿马
pers_nationality_papNewCui=巴布亚新几内亚
pers_nationality_paraguay=巴拉圭
pers_nationality_peru=秘鲁
pers_nationality_philippines=菲律宾
pers_nationality_poland=波兰
pers_nationality_frenPolyne=法属玻利尼西亚
pers_nationality_portugal=葡萄牙
pers_nationality_puerRico=波多黎各
pers_nationality_qatar=卡塔尔
pers_nationality_reunion=留尼旺
pers_nationality_romania=罗马尼亚
pers_nationality_russia=俄罗斯
pers_nationality_saiLueia=圣卢西亚
pers_nationality_saintVinc=圣文森特岛
pers_nationality_samoa_eastern=东萨摩亚
pers_nationality_samoa_western=西萨摩亚
pers_nationality_sanMarino=圣马力诺
pers_nationality_saoAndPrinc=圣多美和普林西比
pers_nationality_sauArabia=沙特阿拉伯
pers_nationality_senegal=塞内加尔
pers_nationality_seychelles=塞舌尔
pers_nationality_sieLeone=塞拉利昂
pers_nationality_singapore=新加坡
pers_nationality_slovakia=斯洛伐克
pers_nationality_slovenia=斯洛文尼亚
pers_nationality_solomonIs=所罗门群岛
pers_nationality_somali=索马里
pers_nationality_souAfrica=南非
pers_nationality_spain=西班牙
pers_nationality_sriLanka=斯里兰卡
pers_nationality_sudan=苏丹
pers_nationality_suriname=苏里南
pers_nationality_swaziland=斯威士兰
pers_nationality_sweden=瑞典
pers_nationality_switzerland=瑞士
pers_nationality_syria=叙利亚
pers_nationality_tajikstan=塔吉克斯坦
pers_nationality_tanzania=坦桑尼亚
pers_nationality_thailand=泰国
pers_nationality_togo=多哥
pers_nationality_tonga=汤加
pers_nationality_triAndToba=特立尼达和多巴哥
pers_nationality_tunisia=突尼斯
pers_nationality_turkey=土耳其
pers_nationality_turkmenistan=土库曼斯坦
pers_nationality_uganda=乌干达
pers_nationality_ukraine=乌克兰
pers_nationality_uniArabEmira=阿拉伯联合酋长国
pers_nationality_uruguay=乌拉圭
pers_nationality_uzbekistan=乌兹别克斯坦
pers_nationality_venezuela=委内瑞拉
pers_nationality_vietnam=越南
pers_nationality_yemen=也门
pers_nationality_serbia=塞尔维亚
pers_nationality_zimbabwe=津巴布韦
pers_nationality_zambia=赞比亚
pers_nationality_aruba=阿鲁巴
pers_nationality_bhutan=不丹
pers_nationality_bosnia_herzegovina=波斯尼亚和黑塞哥维那
pers_nationality_cambodia=柬埔寨
pers_nationality_congoD=刚果（金）
pers_nationality_comoros=科摩罗
pers_nationality_capeVerde=佛得角
pers_nationality_croatia=克罗地亚
pers_nationality_dominica=多米尼克
pers_nationality_eritrea=厄立特里亚
pers_nationality_micronesia=密克罗尼西亚
pers_nationalit_guineaBissau=几内亚比绍
pers_nationalit_equatorialGuinea=赤道几内亚
pers_nationalit_hongkong=中国香港
pers_nationalit_virginIslands=美属维尔京群岛
pers_nationalit_britishVirginIslands=英属维尔京群岛
pers_nationalit_kiribati=基里巴斯
pers_nationalit_mongolia=蒙古
pers_nationalit_marshall=马绍尔群岛
pers_nationalit_macedonia=马其顿
pers_nationalit_montenegro=黑山
pers_nationalit_mauritania=毛里塔尼亚
pers_nationalit_palestine=巴勒斯坦
pers_nationalit_palau=帕劳
pers_nationalit_rwanda=卢旺达
pers_nationalit_saintKittsNevis=圣基茨和尼维斯联邦
pers_nationalit_timorLeste=东帝汶
pers_nationalit_taiwan=中国台湾
pers_nationalit_tuvalu=图瓦卢
pers_nationalit_vanuatu=瓦努阿图
#制卡
pers_person_cardprint=制卡
pers_cardTemplate_tempSelect=模板选择
pers_cardTemplate_printerSelect=打印机选择
pers_cardTemplate_front=正面
pers_cardTemplate_opposite=反面
pers_cardTemplate_entryDate=入职日期
pers_cardTemplate_photo=照片
pers_cardTemplate_uploadFail=图片上传失败！
pers_cardTemplate_jpgFormat=只允许上传格式为jpg的文件！
pers_cardTemplate_printStatus=打印状态
pers_cardTemplate_waiting=等待打印
pers_cardTemplate_printing=打印中
pers_cardTemplate_printOption=打印选项
pers_cardTemplate_duplexPrint=双面打印
pers_cardTemplate_frontOnly=仅打印正面
#app
pers_app_delPers=服务器上不存在你要删除的人员
pers_app_deptIsNull=找不到该部门编号或名称对应的部门
pers_app_personNull=人员不存在
pers_app_pinExist=人员编号已存在
pers_app_dateError=日期格式有误，请参考正确格式：2016-08-08
#api
pers_api_selectPhotoInvalid=照片为无效照片，请重新上传
pers_api_dateError=日期格式有误
pers_api_personNotExist=人员不存在
pers_api_cardsPersSupport=系统未开启一人多卡，设置副卡无效
pers_api_department_codeOrNameNotNull=部门编号或部门名称不能为空
pers_api_deptSortNoIsNull=部门排序不能为空！
pers_api_deptSortNoError=部门排序的值要介于1-999999之间!
pers_api_dataLimit=当前操作数量为{0}，超过限制的{1}条，请分批操作！
pers_api_cardTypeError=卡类型错误
#人员生物模板API
pers_api_fingerprintExisted=该人员的指纹已经存在！
pers_api_validtypeIncorrect=validtype填写有误！
pers_api_dataNotExist=templateNo不存在！
pers_api_templateNoRang=指纹模板号码的范围是0-9！
pers_api_templateIsNull=template不能为空！
pers_api_versionIsNumber=version只能输入数字！
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=人员编号仅能输入数字！
#biotime
pers_h5_personAvatarNotNull=头像为空
pers_h5_personMobileRepeat=手机号码已存在
pers_h5_personEmailRepeat=邮箱已存在
pers_h5_pwdIsRepetition=新老密码重复
pers_h5_personIdNull=人员id为空
pers_h5_pinOrEmailIsNull=请填写编号和邮箱
pers_emailTitle_resetPassword=修改密码
pers_emailContent_resetPassword=该链接有效时间24小时,复制该链接到浏览器修改密码：
pers_h5_tokenIsNull=凭证为空
pers_h5_tokenError=凭证错误或已过期
pers_h5_oldPwdIsError=旧密码填写错误
pers_api_resetPasswordSuccess=修改密码成功
pers_api_resetPasswordFail=修改密码失败
pers_api_forgetPassword=重置密码
pers_api_confirmSubmit=确认提交
pers_api_confirmPwdCaution=点击[确定]确认新的密码
pers_api_pwdRule=密码必须包含至少一个符号或者数字,且长度至少为8-12个字符
pers_h5_personPinFormatNumber=人员编号只能由数字或字母组成
pers_h5_persEmailNoExist=邮箱填写错误
pers_h5_pageNull=分页参数错误
pers_h5_personPinNotStartWithZero=人员编号不能以0开头
pers_h5_personPinTooLong=人员编号太长
pers_h5_personPinInValid=该编号已在使用中
pers_h5_imgSizeError=请上传大小不超过10M的图片！
pers_h5_confirmAndContinue=确认并继续
#人脸抠图不及格错误
pers_face_poorResolution=图片分辨率低于80000像素
pers_face_noFace=未检测到人脸
pers_face_manyFace=检测到多张人脸
pers_face_smallFace=人脸比例太小
pers_face_notColor=图片为非彩色图片
pers_face_seriousBlur=图片模糊
pers_face_intensivelyLight=图片曝光严重
pers_face_badIllumination=图片亮度太暗
pers_face_highNoise=图片高噪声
pers_face_highStretch=人脸拉伸过度
pers_face_covered=人脸被遮挡
pers_face_smileOpenMouth=微笑过度
pers_face_largeAngle=人脸偏转角度过大
pers_face_criticalIllumination=图片亮度临界
pers_face_criticalLargeAngle=人脸偏转角度临界
pers_face_validFailMsg=检测人脸失败，原因：
pers_face_failType=人脸抠图不及格类型
pers_face_photoFormatError=照片格式不正确，请上传JPG/PNG格式文件。
pers_face_notUpdateMsg=生成比对照片失败，不更新比对照片。
#健康申报
pers_health_enable=启用健康信息声明
pers_health_attrExposure=是否接触过可疑或确诊病例
pers_health_attrSymptom=过去14天有哪些症状
pers_health_attrVisitCity=过去14天访问过的城市
pers_health_attrRemarks=健康状况备注
pers_health_symptomCough=咳嗽
pers_health_symptomFever=发烧
pers_health_symptomPolypena=呼吸不畅
pers_health_declaration=健康声明
pers_health_aggrement=我已同意未按要求填写信息的人将被禁止访问，并且未如实报告信息的访问者无法继续访问并需要承担相应的法律责任。
pers_health_visitCity_notEmpty=访问的城市不能为空！
pers_health_notAgree=需要选择同意协议,才能继续
#人员名单库
pers_personnal_list_manager=名单库
pers_personnal_list=名单库
pers_personnal_list_scheme=域模式
pers_personnal_list_name=名单库名称
pers_personnal_list_group_str_id=名单库ID
pers_personnal_list_personCount=人员数量
pers_personnal_list_tag=用户自定义
pers_personnal_list_type=名单库类型
pers_personnallist_addPerson_repo=添加人员至名单库
pers_personnallist_sendPersonnallist=下发名单库
pers_personnallist_sendPerson=下发人员
pers_personnallist_notDel_existPerson=名单库含有人员,不能删除
pers_personnallist_peopleInRoster=名单库还有人员
pers_personnallist_associationNotExist=主设备和名单库关联不存在
pers_personnal_list_person=名单库人员
pers_personnal_list_dev=名单库权限
pers_personnal_list_addDev=添加设备
pers_personnal_list_name_isExist=该名称已存在
pers_personnal_bannedList=禁止名单库
pers_personnal_allowList=允许名单库
pers_personnal_redList=红名单库
pers_personnal_attGroup=考勤组
pers_personnal_passList=通行名单
pers_personnal_banList=禁止名单
pers_personnal_visPassList=访客通行名单
pers_personnal_visBanList=访客禁止名单
pers_personnal_databaseHasBeenDistributed=选择的名单库已下发,不允许删除
pers_personnel_sendError_dueTo=下发失败 失败原因:
pers_personnel_sendError_reson={0}存在于禁止名单，请先删除再添加
#比对照片-样片示例
pers_examplePic_Tip=应符合以下要求：
pers_examplePic_Tip1=1、背景颜色为纯白，人员穿深色衣服；
pers_examplePic_Tip2=2、电子照片为JPG，PNG，JPEG文件格式，建议像素范围：480*640<像素<1080*1920；
pers_examplePic_Tip3=3、电子照片中的人像应双眼睁开正视前方并保证瞳孔清晰可见；
pers_examplePic_Tip4=4、电子照片中的人像应为中性表情，可微笑，但不宜露齿；
pers_examplePic_Tip5=5、电子照片中的人像应清晰、色彩自然、层次丰富、无明显畸变。在人像脸部或背景上无阴影、亮点或反光；对比度和亮度适当。
pers_examplePic_description=正确示例
pers_examplePic_error=错误示例：
pers_examplePic_error1=表情夸张（过度微笑）
pers_examplePic_error2=光线太暗
pers_examplePic_error3=人脸过小（分辨率太小）
pers_applogin_enabled=启用app登录
pers_applogin_disable=禁用app登录
pers_applogin_status=app登录启用状态