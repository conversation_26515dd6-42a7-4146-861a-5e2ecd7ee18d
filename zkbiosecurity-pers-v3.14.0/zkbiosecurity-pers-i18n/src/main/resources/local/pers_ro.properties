#Persoane comune.
#Aici alte module pot folosi eticheta din Persoane.
pers_module=Personal
pers_common_addPerson=Adaugă persoane
pers_common_delPerson=Ștergeți persoană
pers_common_personCount=Număr de persoane
pers_common_browsePerson=Vizualizați persoane
#Meniu stânga
pers_person_manager=Administrarea persoanelor
pers_person=Personal
pers_department=Departament
pers_leave=Persoane care și-au dat demisia
pers_tempPerson=Persoane care urmează să fie examinate
pers_attribute=Atribut definire proprie
pers_card_manager=Administrarea cardurilor
pers_card=Card
pers_card_issue=Registru de Probleme cu Cardurile
pers_wiegandFmt=Format wiegan
pers_position=Poziție
#Personal
pers_person_female=Femeie
pers_person_male=Bărbat
pers_person_pin=Numărul de angajat
pers_person_departmentChange=Ajustare departament
pers_personDepartment_changeLevel=Permisiunea de schimbare a departamentului
pers_person_gender=Sex
pers_person_detailInfo=Detalii
pers_person_accSet=Setări de control al accesului
pers_person_accSetting=Setări de control al accesului
pers_person_attSet=Setări de prezență
pers_person_eleSet=Setări control elevator
pers_person_eleSetting=Setări control elevator
pers_person_parkSet=Înmatriculare a plăcuței auto
pers_person_pidSet=Setări de CID personal
pers_person_insSet=Setări ecran informații
pers_person_aiSet=Setări percepție fețe
pers_person_payrollSet=Setări salarii
pers_person_psgSet=Setări canal
pers_person_lockerSet=Setarea dulapului
pers_person_sisSet=Setări de verificare a securității
pers_person_vdbSet=Setări interfon vizual
pers_person_firstName=Prenume
pers_person_lastName=Numele de familie
pers_person_name=Numele complet
pers_person_wholeName=Numele complet
pers_person_fullName=Numelele întreg
pers_person_cardNum=Număr de carduri
pers_person_deptNum=Număr de departament implicate
pers_person_dataCount=Statistici
pers_person_regFinger=Amprentă
pers_person_reg=Înregistrare
pers_person_password=Parolă verificare dispozitiv
pers_person_personDate=Dată angajare
pers_person_birthday=Data nașterii
pers_person_mobilePhone=Număr telefon mobil
pers_person_personDevAuth=Permisiuni dispozitive personal
pers_person_email=Adresă email
pers_person_browse=Caută
pers_person_authGroup=Grupuri de nivel
pers_person_setEffective=Setați timp valabil
pers_person_attArea=Zonă frecventare
pers_person_isAtt=Frecventare
pers_person_officialStaff=Personal oficial
pers_person_probationStaff=Personal de Probă
pers_person_identity_category=Categorie identitate
pers_person_devOpAuth=Autorizație operare dispozitiv
pers_person_msg1=Numărul cardului fizic și codul de câmp trebuie adăugate simultan!
pers_person_msg2=Vă rugăm să introduceți 3-4 cifre.
pers_person_msg3=Format greșit!
pers_person_imgPixel=(Mărimea ideală este 120x140)
pers_person_cardLengthDigit=Vă rugăm introduceţi numărul!
pers_person_cardLengthHexadecimal=Vă rugăm să introduceți Numelere sau litere abcdef!
pers_person_to=La
pers_person_templateCount=Numărul amprentelor
pers_person_biotemplateCount=Numărul șabloanelor biologice
pers_person_regFace=Facială
pers_person_regVein=Venele degetelor
pers_person_faceTemplateCount=Numărul fețelor
pers_person_VeinTemplateCount=Numărul venelor din deget
pers_person_palmTemplateCount=Numărul venelor din palmă
pers_person_cropFace=Compară poze
pers_person_cropFaceCount=Număr de compară poze
pers_person_faceBiodataCount=Număr de fețe vizibile
pers_person_irisCount=Numărul de iris
pers_person_batchToDept=Departament transferat la
pers_person_changeReason=Motivul transfer
pers_person_selectedPerson=Alegeți o persoană
pers_person_duressPwdError=Repetați parola
pers_person_completeDelPerson=Şterge
pers_person_recover=Recuperare
pers_person_nameNoComma=Nu poate conține virgule
pers_person_firstNameNotEmpty=PreNumelele nu poate fi gol
pers_person_lastNameNotEmpty=Numelele de familie nu poate fi gol
pers_person_mobilePhoneValidate=Vă rugăm să introduceți un număr de telefon mobil valid
pers_person_phoneNumberValidate=Vă rugăm să introduceți un număr de telefon valid
pers_person_postcodeValidate=Vă rugăm să introduceți un cod poștal valid
pers_person_idCardValidate=Vă rugăm să introduceți un număr de CID valid
pers_person_pwdOnlyLetterNum=Parola poate conține doar litere sau Numelere
pers_person_certNumOnlyLetterNum=CID poate conține doar litere sau Numelere
pers_person_oldDeptEqualsNewDept=Departamentul original și departamentul nou sunt identice
pers_person_disabled=Acces dezactivat
pers_person_emailError=Formatul e-mailului este incorect
pers_person_driverPrompt=Vă rugăm să instalați driverul pentru CID! Faceți clic pe OK pentru a descărca driverul.
pers_person_readIDCardFailed=Glisarea cardului eșuată!
pers_person_cardPrompt=Vă rugăm să plasați CID-ul de a doua generație în zona de citire a cardurilor...
pers_person_iDCardReadOpenFailed=Nu a fost detectat cititor de cărți de identitate din a doua generație!
pers_person_iDCardNotFound=Nu se citeşte cartea de identitate, vă rugăm re-glisaţi!
pers_person_nameValid=Suportă chineză, engleză, Numelere”-”,”_”
pers_person_nameValidForEN=Suportă engleză, Numelere, “-“,”_”,”.”.
pers_person_pinPrompt=Vă rugăm să introduceți caractere compuse din litere englezești și Numelere
pers_person_pinSet=Setări numărul de angajat
pers_person_supportLetter=Litere suportate
pers_person_cardsSupport=Activați multi-card pentru o persoană
pers_person_SupportDefault=Numărare automată
pers_person_noSpecialChar=Caracterele speciale nu pot fi introduse în Numelele personalului!
pers_person_pinInteger=Vă rugăm să introduceți caractere consistând în cifre
pers_pin_noSpecialChar=Numărul persoanei nu poate conține caractere speciale!
pers_op_capture=Instantaneu
pers_person_cardDuress=Număr de card duplicat
pers_person_pwdException=Parola este anormală
pers_person_systemCheckTip=S-a constatat faptul că informația personalului este anormală!
pers_person_immeHandle=Procesare imediată
pers_person_cardSet=Setări card
pers_person_tempPersonSet=Setări pentru personal temporar
pers_person_cardsReadMode=Metodă de citire a numărului de card
pers_person_cardsReadModeReadHead=capul de citire de controller
pers_person_cardsReadModeID180=ID180 (citiți numărul fizic al CID)
pers_person_IDReadMode=Metodă de citire a CID
pers_person_IDReadModeIDCardReader=Cititor card ID
pers_person_IDReadModeTcpReadHead=Capul de citire TCP
pers_person_physicalNo=Număr CID
pers_person_physicalNoToCardNo=Număr fizic de card al CID ca număr de card
pers_person_ReadIDCard=Citire CID
pers_person_templateBioTypeNumber=Numărul tipului șablonului biometric
pers_person_templateValidType=Dacă tipurile bio sunt valide
pers_person_templateBioType=Tip șablon biologic
pers_person_templateVersion=Versiune șablon biologic
pers_person_template=Conținut biometric
pers_person_templateNo=Număr biometric
pers_person_templateNoIndex=Index de Corespondență Biometrică
pers_person_templateDataUpdate=Actualizați datele atunci când există un șablon biologic:
pers_person_templateValidTypeNoNull=Dacă datele biometrice sunt valide nu poate fi gol!
pers_person_templateBioTypeNoNull=Numărul de angajat: {0}, tipul biometric nu poate fi gol!
pers_person_templateVersionNoNull=Numărul de angajat: {0}, versiunea biometrică nu poate fi goală!
pers_person_templateNoNull=Numărul de angajat: {0}, conținutul biometric nu poate fi gol!
pers_person_templateNoNoNull=Numărul de angajat: {0}, numărul biometric nu poate fi gol!
pers_person_templateNoIndexNoNull=Numărul de angajat: {0}, indexul biometric corespunzător nu poate fi gol!
pers_person_templateError=Numărul de angajat: {0}, conținutul biometric este greșit!
pers_person_bioDuress=Dacă se reține
pers_person_universal=Universal
pers_person_voice=Amprentă vocală
pers_person_iris=Iris
pers_person_retina=Retină
pers_person_palmPrints=Amprenta palmei
pers_person_metacarpalVein=Vene palmă
pers_person_visibleFace=Față umană vizibilă
pers_person_pinError=Nu există nicio persoană cu numărul de personal {0}!
pers_person_pinException=Număr de personal anormal
pers_person_pinAutoIncrement=Numărul persoanei se incrementează automat
pers_person_resetSelfPwd=Resetați parola de autentificare automată
pers_person_picMaxSize=Rezoluția pozei este prea mare, este recomandat ca rezoluția să fie mai mică decât {0}.
pers_person_picMinSize=Rezoluția pozei este prea mică, este recomandat ca rezoluția să fie mai mare decât {0}.
pers_person_cropFaceShow=Vizualizați poze care se potrivesc
pers_person_faceNoFound=Nicio față deschisă
pers_person_biometrics=Tipuri biometrice
pers_person_photo=Poze personal
pers_person_visibleFaceTemplate=Șablon de față vizibil
pers_person_infraredFaceTemplate=Șablon de față infraroșu static
pers_person_delBioTemplate=Ștergeți datele biometrice
pers_person_delBioTemplateSelect=Vă rugăm să selectați șablonul biologic de șters!
pers_person_infraredFace=Față statică cu infraroșu
pers_person_notCard=Nu include cardul
pers_person_notRegFinger=Nu include amprenta
pers_person_notMetacarpalVein=Nu include vena de palmier
pers_person_notRegVein=Nu include venele degetelor
pers_person_notIris=Cu excepția irisului
pers_person_notInfraredFaceTemplate=Nu include șablonul de față în infraroșu apropiat
pers_person_notVisibleFaceTemplate=Nu include fotografii vizibile ale feței
pers_person_notVisibleFacePhoto=Nici o fotografie vizibilă a feței
pers_person_visibleFacePhoto=Fotografie vizibilă a feţei
pers_person_cropFaceUsePhoto=Doriți să afișați fotografia de comparație ca un avatar?
pers_person_photoUseCropFace=Doriți să utilizați avatarul pentru a genera o fotografie de comparație?
pers_person_selectCamera=Selectează camera de captare
pers_person_delCropFaceMsg=Nu există fotografii de comparat pentru a șterge!
pers_person_disabledNotOp=Persoana a fost dezactivată şi nu poate opera!
pers_person_visiblePalm=Palmă luminoasă vizibilă
pers_person_notVisiblePalm=Nu include palma cu lumină vizibilă
pers_person_selectDisabledNotOp=Există personal cu dizabilităţi în personalul selectat, incapabil să opereze!
pers_person_photoUseCropFaceAndTempalte=Doriți să utilizați avatare pentru a genera fotografii de comparație și șabloane faciale?
pers_person_photoUseTempalte=Doriți să utilizați fotografii pentru a genera șabloane de față?
pers_person_createCropFace=Generați fotografii de comparare
pers_person_createFaceTempalte=Generați șabloane faciale
pers_person_faceTempalte=Șabloane faciale
pers_person_extractFaceTemplate=Extragerea șabloanelor faciale
pers_person_createSuccess=Generat cu succes
pers_person_createFail=Generare eșuată
pers_person_serverConnectWarn=Adresa serverului, numele de utilizator și parola nu pot fi goale!
pers_person_serverOffline=Server de extragere a șabloanelor faciale offline
pers_person_faceTemplateError1=Detectarea feței eșuată
pers_person_faceTemplateError2=Ocluzie facială
pers_person_faceTemplateError3=Claritate insuficientă
pers_person_faceTemplateError4=Unghiul feţei prea mare
pers_person_faceTemplateError5=Detectarea live a eșuat
pers_person_faceTemplateError6=Extragerea șablonului facial a eșuat
pers_person_cropFaceNoExist=Fotografia de comparație nu există
pers_person_disableFaceTemplate=Funcția de extragere a șablonului facial nu este activată, nu se poate extrage șablonul!
pers_person_cropFacePhoto=Fotografie de comparare a feței
pers_person_vislightPalmPhoto=Fotografie de comparare a palmei
pers_person_serverOfflineWarn=Serverul de extragere a șabloanelor faciale este offline și nu poate activa această caracteristică!
pers_person_serverConnectInfo=Vă rugăm să verificați dacă serverul de extragere a șabloanelor faciale este online?
pers_person_notModified=Numărul de telefon mobil nu poate fi modificat.
pers_person_syncAcms=Sincronizați personalul cu ACMS
pers_person_startUpdate=Începeți actualizarea informațiilor despre personal.
pers_person_updateFailed=Actualizarea informațiilor despre personal a eșuat!
#Emitere card controller
pers_person_readCard=Emitere card controller
pers_person_stopRead=Încetați emiterea de carduri
pers_person_chooseDoor=Selectați ușa
pers_person_readCarding=Emiterea cardului, vă rugăm să încercați din nou mai târziu!
#Captați o fotografie
pers_capture_catchPhoto=Captați o fotografie
pers_capture_preview=Previzualizare
#Departament
pers_dept_entity=Departament
pers_dept_deptNo=Numărul departamentului
pers_dept_deptName=Numele departamentului
pers_dept_parentDeptNo=Numărul departamentului principal
pers_dept_parentDeptName=Numelele departamentului principal
pers_dept_parentDept=Departament principal
pers_dept_note=Dacă departamentul nou nu apare pe lista departamentelor, contactați administratorul pentru reautorizarea departamentului la “Editare utilizator”!
pers_dept_exit=Există
pers_dept_auth=Utilizator sistem legat
pers_dept_parentMenuMsg=Departamentul principal nu poate fi el însăși sau departamentul secundar al său
pers_dept_initDept=Numele departamentului
pers_dept_deptMarket=Departament marketing
pers_dept_deptRD=Departament R&D
pers_dept_deptFinancial=Departament financiar
pers_dept_nameNoSpace=Numelele departamentelor nu poate începe și sfârși cu spațiu!
pers_dept_nameExist=Numelele departamentului există deja
pers_dept_changeLevel=Schimbarea nivelului acestui departament
pers_dept_noSpecialChar=Numerele de departament nu pot conține caractere speciale!
pers_dept_NameNoSpecialChar=Numele departamentelor nu pot conține caractere speciale!
pers_person_change=Ajustări de personal
pers_dept_noModifiedParent=Departamentul superior nu poate fi modificat!
#Poziție
pers_position_entity=Poziție
pers_position_code=Număr poziție
pers_position_name=Numelele poziție
pers_position_notExist=Poziția nu există!
pers_position_sortNo=Număr sortare
pers_position_parentName=Poziție superioară
pers_position_parentCode=Număr de poziție superioară
pers_position_batchToPosition=Poziții schimbate în
pers_position_nameExist=Numelele poziției există deja
pers_position_change=Ajustare de poziție
pers_position_parentMenuMsg=Poziția Superioară nu poate fi însăși ea sau una dintre pozițiile sale subordonate
pers_position_nameNoSpace=Numelele poziției nu poate începe și nu se poate încheia cu un spațiu!
pers_position_existSub={0}: Nu se pot șterge sub-postări
pers_position_existPerson={0}: Nu se poate șterge persoana
pers_position_importTemplate=Șablon de import de job
pers_position_downloadTemplate=Descarcă șablonul de import
pers_position_codeNotEmpty=Numărul poziției nu poate fi gol
pers_position_nameNotEmpty=Numele poziției nu poate fi gol
pers_position_nameNoSpecialChar=Numele poziției {0} nu poate conține caractere speciale!
pers_position_noSpecialChar=Numărul poziției {0} nu poate fi un caracter special!
pers_position_codeLength=Numărul poziției {0} depășește 30 de cifre în lungime
pers_position_nameLength=Date al căror nume de poziție {0} este mai lung decât {1}
pers_position_codeExist=ID-ul jobului {0} există deja
#Certificat
pers_cert_type=Tip certificat
pers_cert_number=Număr ID
pers_cert_name=Numele certificat
pers_cert_numberExist=Numelele certificatului există deja
#Export
pers_export_allPersPerson=Toate persoanele
pers_export_curPersPerson=Persoana curentă
pers_export_template=Exportați șablon
pers_export_personInfo=Exportați informații personal
pers_export_personInfoTemplate=Exportați șablon informații personal
pers_export_personBioTemplate=Exportați date șablon biologic personal
pers_export_basicInfo=Configurați informațiile de bază ale personalului
pers_export_customAttr=Configurați proprietățile definire proprie
pers_export_templateComment=Numelele câmp, dacă este o cheie primarăm dacă este unică, dacă se poate să fie goală ({0},{1},{2},{3})
pers_export_templateFileName=Șablon informații personal
pers_export_bioTemplateFileName=Date șablon biologic personal
pers_export_deptInfo=Export informații departament
pers_export_deptTemplate=Export model departament
pers_export_deptTemplateFileName=Model departament
pers_export_personPhoto=Exportați poze personal
pers_export_allPhotos=Toate pozele (selectați toate persoanele)
pers_export_selectPhotoToExport=Selectați numărul persoanei de start și numărul persoanei de încheiere pentru a exporta pozele persoanelor
pers_export_fromId=Numărul de angajat de la
pers_export_toId=Până la
pers_export_certNumberComment=Tipul certificatului este necesar după completarea numărului certificatului
pers_export_templateCommentName=Numele câmpului: ({0})
pers_export_dataExist=Vă rugăm să vă asigurați că datele importate există deja în sistem
pers_export_cardNoTip=Numere multiple de carduri separate cu &
pers_carNumber_importTip=Numărul plăcuței de înmatriculare auto (mai multe plăcuțe de înmatriculare auto& separate)
#Import
pers_import_certNumberExist=Numărul de certificat {0} există deja
pers_import_complete=Execută
pers_import_password=Parolă personal
pers_import_fail=Linia {0} eșec: {1}
pers_import_overData=Numărul curent de personal importat este {0}, depășind limita de 30.000, vă rugăm importați în loturi!
pers_import_pinTooLong=Numărul de angajat {0} Prea lung!
pers_import_pinExist=Numărul de angajat {0} Deja există!
pers_import_pinIsRepeat=Numărul de angajat {0} Repetat!
pers_import_pinError=Numărul de angajat {0} Format incorect!
pers_import_pinSupportNumber=Numărul personal suportă doar cifre! Numărul de angajat este: {0}
pers_import_pinNotSupportNonAlphabetic=Numărul de angajat suportă doar cifre și litere. Numărul de angajat este: {0}
pers_import_pinNotNull=Numărul de angajat nu poate fi format doar din 0!
pers_import_pinStartWithZero=Numărul de angajat nu poate începe cu 0!
pers_import_cardNoNotSupportLetter=Numărul cardului nu suportă litere!
pers_import_cardNoNotNull=Numărul cardului nu poate fi format doar din 0!
pers_import_cardNoStartWithZero=Numărul cardului nu poate începe cu 0!
pers_import_cardTooLong=Numărul cardului {0} Prea lung!
pers_import_cardExist=Numărul cardului {0} Există deja!
pers_import_personPwdOnlyNumber=Parola personalului suportă doar cifre!
pers_import_personPwdTooLong=Parola personalului {0} Prea lungă!
pers_import_personDuressPwd=Parola personalului {0} Repetați!
pers_import_emailTooLong=Căsuță poștală {0} Prea lungă!
pers_import_nameTooLong=Numelele {0} Prea lung!
pers_import_genderError=Formatul de gen este greșit!
pers_import_personDateError=Data angajării este greșită!
pers_import_createTimeError=Timpul de creare este greșit!
pers_import_phoneError=Eroare de format al numărului de telefon!
pers_import_phoneTooLong=Numărul de telefon este mai lung de 20 de cifre!
pers_import_emailError=Eroare de format a căsuței poștale!
pers_import_birthdayError=Eroare de format a datei nașterii!
pers_import_nameError=Numelele nu poate conține caractere speciale! Numărul de angajat este: {0}
pers_import_firstnameError=Numelele nu poate conține virgule!
pers_import_firstnameNotNull=Numelele de familie nu poate fi gol!
pers_import_dataCheck=Verificarea de date completă!
pers_import_dataSaveFail=Importul de date a eșuat!
pers_import_allSucceed=Toate datele au fost importate cu reușit !
pers_import_result=Reușit : {0}, Bucată, eșec: {1} Bucată.
pers_import_result2=Rezultatele importului
pers_import_result3=Reușit:{0} Bucată, actualizare: {1} Bucată, eșec: {2} Bucată.
pers_import_notSupportFormat=Acest format nu este suportat!
pers_import_selectCorrectFile=Rugăm selectați un format de fișier corect!
pers_import_fileFormat=Format fișier
pers_import_targetFile=Fișier țintă
pers_import_startRow=Numărul rândului de început al antetului tabelului
pers_import_startRowNote=Formatul datelor primului rând al Numelelor din tabel, celui de-al doilea rând de anteturi de tabel și după al treilea rând sunt datele de import, vă rugăm să verificați fișierul înaintea importării.
pers_import_delimiter=Delimitator
pers_import_importingDataFields=Câmp din baza de date
pers_import_dataSourceFields=Importați câmpurile de raport
pers_import_total=Total
pers_import_dataUpdate=Actualizați datele acolo unde există număr de personal:
pers_import_dataIsNull=Nu există date în fișier!
pers_import_deptNotExist=Departamentul este inexistent!
pers_import_deptIsNotNull=Numelele departamentului nu poate rămâne go!
pers_import_pinNotEmpty=Numărul personalului nu poate fi gol!
pers_import_nameNotEmpty=Numele personalului nu poate fi gol!
pers_import_siteCodeOnlyLetterNum=Formatul de cod al câmpului este incorect.
pers_import_cardNoFormatErrors=Număr de card {0} Eroare de formatare.
pers_import_cardsNotSupport=Sistemul curent nu suportă multi-card
pers_import_personInfo=Importați informațiile despre personal
pers_import_commentFormat=Formatul comentariului este incorect!
pers_import_noComment={0} rând {1} date de coloană, fără comentariu
pers_import_fieldRepeat={0} rând {1} date de coloană, Numelele câmpurilor repetate în comentarii: {2}
pers_import_primaryKey=Cel puțin un câmp trebuie să fie cheie primară
pers_import_templateIsRepeat=Numărul personalului: {0} șablonul datelor biologice a fost repetat!
pers_import_biologicalTemplate=Importați datele șablonului biologic ale personalului
pers_import_uploadFileSuccess=Fișier încărcat cu reușit  și datele au fost analizate. Rugăm așteptați...
pers_import_resolutionComplete=După finalizarea analizei, baza de date este actualizată.
pers_import_mustField=Fișierul de import trebuie să conțină o coloană de {0}.
pers_import_bioTemplateSuccess=Importați datele de șablon biologic. Aceste date trebuie sincronizate manuală de către fiecare modul de business înainte să fie livrate pe dispozitiv.
pers_import_personPhoto=Importați pozele persoanelor
pers_import_opera_log=Jurnalul de operații
pers_import_error_log=Log de erori
pers_import_uploadFileSize=Vă rugăm să încărcați un fișier mai mic decât {0}!
pers_import_uploadFileSizeLimit=Pentru un singur import, vă rugăm să încărcați un fișier mai mic decât 500MB!
pers_import_type=Metodă de import
pers_import_photoType=Fotografie
pers_import_archiveType=Arhivă
pers_import_startUpload=Start încărcare
pers_import_addMore=Adăugați mai multe
pers_import_photoQuality=Calitate poză
pers_import_original=Original
pers_import_adaptive=Adaptivă
pers_import_adaptiveSize=(Mărime 480 * 640)
pers_import_totalNumber=Total
pers_import_uploadTip=(Nu ștergeți pozele în timp ce se încarcă)
pers_import_addPhotoTip=Vă rugăm să ștergeți pozele procesate anterior și să încărcați noile poze din nou!
pers_import_selectPhotoTip=Vă rugăm să selectați pozele de încărcare mai întâi!
pers_import_uploadResult=Rezultatele încărcării de poze
pers_import_pleaseSelectPhoto=Vă rugăm să selectați o poză
pers_import_multipleSelectTip=Apăsați tasta Ctrl pentru a efectua selectare multiplă
pers_import_replacePhotoTip=Poza selectată există deja printre pozele care așteaptă să fie încărcate. Sunteți siguri că vreți să o înlocuiți?
pers_import_photoNamePinNotCorrespond=Motivul: Numelele pozei și numărul personalului nu se potrivesc
pers_import_photoFormatRequirement=Vă rugăm să folosiți numărul personalului pentru a numi poza. Formatul corect este JPG/PNG. Asigurați-vă că Numelele pozei nu conține caractere speciale.
pers_import_filterTip=Unele dintre pozele selectate nu pot fi vizualizate din unul dintre următoarele motive:
pers_import_photoContainSpecialCharacters=Numelele pozei conține caractere speciale
pers_import_photoFormatError=Formatul pozei este incorect
pers_import_photoSelectNumber=Nu alegeți mai mult de 3000 de poze într-un singur import!
pers_import_photoSelectNumberLimit=Nu alegeți mai mult de {0} poze!
pers_import_fileMaxSize=Poza este prea mare, vă rugăm să încărcați o poză mai mică de 5MB.
pers_import_notUploadPhotoNumber=Pozele neîncărcate nu ar trebui să fie mai multe de 3.000!
pers_import_zipFileNotPhoto=Nu există nicio poză de persoană în pachetul comprimat, vă rugăm să reselectați și să importați!
pers_import_personPlateRepeat=Număr de înmatriculare personal {0} Repetați!
pers_import_filePlateRepeat=Număr de înmatriculare din interiorul fișierului {0} Repetați!
pers_import_personPlateFormat=Număr de înmatriculare personal {0} Formatul este incorect! 
pers_import_personPlateMax=Numărul plăcuțelor de înmatriculare ale angajaților depășește 6!
pers_import_cropFaceFail=Nu s-a reușit generarea de fotografii potrivite!
pers_import_pinLeaved=Angajatul cu numărul: {0} și-a dat demisia.
pers_import_exceedLicense=Numărul de persoane cu care se depășește licența de software nu poate fi importat.
pers_import_bioTemplateNotNull=Număr de angajat, tip biologic, id biologic, index biologic, conținut, versiunea nu pot fi goale!
pers_import_certTypeNotNull=Numărul de angajat este: {0} Tipul certificatului nu poate fi gol
pers_import_certTypeNotExist=Numărul personal este: {0} Tipul documentului nu există
pers_import_certNumNotNull=Numărul de personal este: {0} Numărul certificatului nu poate fi gol
pers_import_certNumberTooLong=Linia {0}: Numărul certificatului {1} Prea lung!
pers_import_idNumberErrors=Linia {0}: Număr de identificare {1} Eroare de format!
pers_import_emailErrors=Linia {0}: Adresa de e-mail {1} Format incorect!
pers_import_emailIsExist=Adresa de e-mail {0} Deja există!
pers_import_emailIsRepeat=Linia {0}: Adresa de e-mail internă a fișierului {1} Se repetă!
pers_import_fileMobilePhoneRepeat=Rândul {0}: numărul de telefon mobil intern al fișierului {1} a fost repetat!
pers_import_mobilePhoneErrors=Eroare de formatare a numărului de telefon mobil!
pers_import_hireDateError=Formatul datei de intrare este incorect!
pers_import_selectPhotoType=Vă rugăm să selectați tipul de fotografie importată!
pers_import_hireDateLaterCurrent=Data închirierii nu poate fi mai târziu decât data curentă!
pers_import_buildingNotExist=Clădirea nu există!
pers_import_unitNotExist=Unitatea nu există!
pers_import_vdbInfoFail=Nu există informații despre unitate numită {1} în clădirea {0}!
pers_import_vdbBuildingFail=Informaţiile despre clădire ale unităţii {0} nu pot fi goale!
pers_import_vdbRoomNoFail=Numărul camerei trebuie să fie mai mare decât 0!
#Demisie persona
pers_person_leave=Demisie
pers_dimission_date=Data demisiei
pers_dimission_type=Timpul plecării
pers_dimission_reason=Motivul plecării
pers_dimission_volutary=Plecare voluntară
pers_dimission_dismiss=Destituire
pers_dimission_resignat=Demisie
pers_dimission_shiftJob=Schimbarea locului de muncă
pers_dimission_leave=Reținere fără plată
pers_dimission_recovery=Recuperare demisie
pers_dimission_sureToRecovery=Sigur doriți să efectuați operațiunea de recuperare a demisiei?
pers_dimission_backCard=Doriți să recuperați pe card?
pers_dimission_isForbidAction=Doriți să blocați accesul ușii?
pers_dimission_writeInfomation=Completați informațiile privind demisia
pers_dimission_pinRetain=Numărul de retenție a personalului rămas
pers_dimission_downloadTemplate=Descărcați șablonul de import de renunțare
pers_dimission_import=Import Respinge
pers_dimission_importTemplate=Șablonul de import de renunțare
pers_dimission_date_noNull=Data demiterii nu poate fi goală
pers_dimission_leaveType_noExist=Tipul de renunțare nu există
pers_dimission_dateFormat=Câmp obligatoriu, formatul orei este aaaa-LL-zz, cum ar fi: 22-07-2020
pers_dimission_leaveType=Câmp obligatoriu, cum ar fi: Plecare voluntară, Destituire, Demisie, Schimbarea locului de muncă
pers_dimission_forbidden=Adăugați la lista neagră
pers_dimission_leaveType_noNull=Tipul de concediu nu poate fi gol!
pers_dimission_person_noExist=Persoana concediată nu există!
pers_dimission_date_error=Data demisiei nu a fost completată sau formatată incorect
#Personal temporar
pers_tempPerson_audit=Audit
pers_tempPerson_view=Vizualizare
pers_tempPerson_waitReview=Așteptând revizuirea administratorului
#Card--Xiao Xiaojun l-a ajutat pe Zheng Zhouwu să reorganizeze următoarea secțiune. Denumire, etc. Card/eliberarecard./cardpierdut/recuperarecard/
#Card
pers_card_cardNo=Număr de card
pers_card_state=Starea cardului
pers_card_effect=Efectiv
pers_card_disabled=Invalid
pers_card_past=Expirat
pers_card_back=Recuperare pe card
pers_card_change=Schimbare card
pers_card_note=Numărul cardului deja există 
pers_card_numTooBig=Numărul de carduri a fost depășit
pers_issueCard_entity=Emiterea cardului
pers_issueCard_operator=Operator
pers_issueCard_operate=Tip operare
pers_issueCard_note=Operațiunea este destinată persoanei care a înregistrat informații despre personal, dar nu a înregistrat numărul de cont al cardului!
pers_issueCard_date=Data de eliberare a cardului
pers_issueCard_changeTime=Schimbați ora
pers_issueCard_cardValidate=Numărul cardului nu poate avea spații
pers_issueCard_cardEmptyNote=Numărul cardului nu poate fi gol
pers_issueCard_cardHasBeenIssued=Acest card a fost deja emis!
pers_issueCard_noCardPerson=Persoană neemisă
pers_issueCard_waitPerson=Persoane cu carduri emise la acest moment
pers_issueCard_mc5000=Emiterea cardului MC5000
pers_batchIssCard_entity=Emitere carduri în lot
pers_batchIssCard_startPersNo=Începând de la persoana cu numărul
pers_batchIssCard_endPersNo=Până la persoana cu numărul
pers_batchIssCard_issCardNum=Număr de carduri emise
pers_batchIssCard_notIssCardNum=Număr de persoane neemise
pers_batchIssCard_generateList=Generați o listă de persoane
pers_batchIssCard_startRead=Începeți să citiți
pers_batchIssCard_swipCard=Emitere poziție card
pers_batchIssCard_sendCard=Citirea numărului plăcii auto
pers_batchIssCard_dispenCardIss=Emitentul cardului
pers_batchIssCard_usbEncoder=Emitent card desfire
pers_batchIssCard_note=Numărul de angajat acceptă numai introducerea cifrelor și afișează doar angajatul căruia nu i s-a eliberat cardul (până la 300 de persoane)!
pers_batchIssCard_startPinEmpty=Numărul angajatului de început nu poate fi gol!
pers_batchIssCard_endPinEmpty=Numărul angajatului de final nu poate fi gol!
pers_batchIssCard_startPinLargeThanEndPin=Numărul angajatului de început nu poate fi mai mare decât numărul angajatului de final!
pers_batchIssCard_numberParagraphNoPerson=Nu există personal neemis în această secțiune Numelerotată!
pers_batchIssCard_inputCardNum=Introduceți numărul cardului
pers_batchIssCard_cardLetter=Numărul cardului nu suportă litere!
pers_batchIssCard_cardNoTooLong=Numărul cardului este prea lung
pers_batchIssCard_issueWay=Metoda de emitere
pers_batchIssCard_noPersonList=Nicio listă de persoane nu a fost generată
pers_batchIssCard_startReadCard=Începeți citirea cardurilor
pers_batchIssCard_swipePosition=Glisați poziția cardului
pers_batchIssCard_chooseSwipePosition=Vă rugăm să selectați poziția de glisare a cardului
pers_batchIssCard_readCardTip=Când metoda de emitere a cardului este capul de citire, sunt citite doar cardurile neînregistrate din dispozitiv.
pers_batchIssCard_notIssCardNo=Număr de carduri neemise
pers_batchIssCard_totalNumOfCards=Total carduri
pers_batchIssCard_acms=Eliberarea cardului ACMS
pers_lossCard_entity=Raportare pierdere
pers_lossCard_lost=Cardul a fost pierdut și nu poate fi repetat!
pers_losscard_note2=După scrierea pe cardul de management, trebuie să glisați cardul pe capul de citire al dispozitivului de control al scării pentru ca operația de raportare a pierderii să intre în vigoare.
pers_revertCard_entity=Anulați raportarea pierderii
pers_revertCard_setReport=Vă rugăm să raportați cardul pierdut mai întâi!
pers_revertcard_note2=După scrierea pe cardul de management, trebuie să glisați cardul pe capul de citire al dispozitivului de control al scării pentru ca operația de anulare a raportării pierderii să intre în vigoare.
pers_issueCard_success=Cardul a fost emis cu reușit !
pers_issueCard_error=Emiterea cardului este anormală!
pers_cardData_error=Formatul datelor citite de pe card este anormal!
pers_analysis_error=Analizarea datelor de pe card este anormală!
pers_cardOperation_error=Funcționarea cardului este anormală!
pers_cardPacket_error=Pachetul de comandă a funcționării cardului este anormal!
pers_card_write=Scrieți cardul
pers_card_init=Inițializați cardul
pers_card_loss=Raportați card
pers_card_revert=Anulați raportarea de card pierdut
pers_card_writeMgr=Scrieți cardul de management
pers_initCard_tip=După inițializare, cardul va deveni un card gol!
pers_initCard_prepare=Pregătire pentru inițializarea cardului...
pers_initCard_process=Inițializare card...
pers_initCard_success=Inițializarea cardului s-a efectuat cu reușit 
pers_mgrCard_prepare=Gata pentru a scrie datele pe cardul de management...
pers_mgrCard_process=Scriere date pe cardul de management...
pers_mgrCard_success=Cardul de management a fost scris cu reușit 
pers_userCard_prepare=Gata pentru a scrie cardul de utilizator...
pers_userCard_process=Scriere date pe cardul de utilizator...
pers_userCard_success=Cardul de utilizator a fost scris cu reușit 
pers_userCard_tip=Vă rugăm să setați ora de început și ora de final pe pagina de editare a personalului înainte de operația de scriere a cardului
pers_userCard_tip2=Datele de autoritate sunt goale și nu este permisă scrierea niciunui card.
pers_userCard_tip3=Sunt mai mult de două dispozitive asociate cu această autoritate, iar datele sunt pierdute.
pers_writeCard_tip=Vă rugăm să vă asigurați că emitentul cardului sau driverul sunt instalate și puneți cardul pe emitentul acestuia
pers_writeMgrCard_tip=Numărul raportării de pierdere și anularea raportării de pierdere a cardului nu poate depăși 18
pers_writeMgrCard_tip2=Numărul curent de carduri „raportate pierdute” și carduri anulate ca „raportate pierdute”:
pers_card_writeToMgr=Scrieți pe cardul de management
pers_card_hex=Afișare format card
pers_card_decimal=Zecimal
pers_card_Hexadecimal=Hex
pers_card_IssuedCommandFail=Eroare la emiterea instrucțiunii:
pers_card_multiCard=Înregistrare multi-card
pers_card_deputyCard=Card secundar
pers_card_deputyCardValid=Vă rugăm să completați mai întâi numărul cardului principal!
pers_card_writePinFormat=Sistemul nu acceptă scrierea numărului de angajat cu litere pe card, deoarece acesta trebuie să fie de tip Numeleric!
pers_card_notMoreThanSixteen=Nu mai mult de 16 carduri secundare.
pers_card_notDelAll=Nu puteți șterge toate cardurile secundare.
pers_card_maxCard=Dimensiunea cardului nu poate depăși {0}!
pers_card_posUseCardNo=Cardul principal al persoanei este utilizat de modulul consumator, vă rugăm să mergeți la modulul consumator pentru a efectua operația de recuperare pe card!
pers_card_delFirst=Vă rugăm să ștergeți numărul cardului emis înainte de emiterea cardului!
pers_card_disablePersonWarn=Numărul cardului selectat conţine personal cu handicap şi nu poate fi operat!
#Format wiegan
#Formatul wiegand
pers_wiegandFmt_siteCode=Cod câmp
pers_wiegandFmt_wiegandMode=Mod
pers_wiegandFmt_wiegandModeOne=Modul unu
pers_wiegandFmt_wiegandModeTwo=Modul doi
pers_wiegandFmt_isDefaultFmt=Potrivire automată
pers_wgFmt_entity=Format de card Wiegand
pers_wgFmt_in=Format de intrare Wiegand
pers_wgFmt_out=Format de ieșire Wiegand
pers_wgFmt_inType=Tip de intrare Wiegand
pers_wgFmt_outType=Tip de ieșire Wiegand
pers_wgFmt_wg=Wigan
pers_wgFmt_totalBit=Total biți
pers_wgFmt_oddPch=Paritate impară (o)
pers_wgFmt_evenPck=Paritate egală (e)
pers_wgFmt_CID=CID(c)
pers_wgFmt_facilityCode=Cod dispozitiv (f)
pers_wgFmt_siteCode=Cod câmp (s)
pers_wgFmt_manufactoryCode=Cod producător (m)
pers_wgFmt_firstParity=Primul bit cu paritate impar și pară (p)
pers_wgFmt_secondParity=Al doilea bit cu paritate impar și pară (p)
pers_wgFmt_cardFmt=Formatul de verificare a cardului
pers_wgFmt_parityFmt=Format de paritate
pers_wgFmt_startBit=Bitul de începere
pers_wgFmt_test=Testare format Wiegand
pers_wgFmt_error=Formatul Wiegand este greșit!
pers_wgFmt_verify1=Numărul total de biți nu poate depăși 80!
pers_wgFmt_verify2=Lungimea formatului de verificare a cardului trebuie să fie egală cu numărul total de cifre!
pers_wgFmt_verify3=Lungimea formatului de paritate trebuie să fie egală cu totalul biților!
pers_wgFmt_verify4=Datele inițializate nu pot fi șterse!
pers_wgFmt_verify5=Formatul cardului este utilizat și nu poate fi șters!
pers_wgFmt_verify6=Primul bit de paritate nu poate fi mai mare decât totalul biților!
pers_wgFmt_verify7=Al doilea bit de paritate nu poate fi mai mare decât totalul biților!
pers_wgFmt_verify8=Formatul bitului de început și lungimea maximă a bitului sunt incorecte!
pers_wgFmt_verify9=Formatul de verificare depășește totalul biților!
pers_wgFmt_verify10=Funcția bitului de verificare card nu poate fi încrucișată!
pers_wgFmt_verify11=Codul de câmp depășește domeniul de setare!
pers_wgFmt_verify=Verificați
pers_wgFmt_unverify=Nu verificați
pers_wgFmt_atLeastDefaultFmt=Vă rugăm să păstrați cel puțin un format de card cu potrivire automată!
pers_wgFmt_defaultFmtError1=Formatele cardurilor cu alte formate în același număr de biți nu pot fi setate să se potrivească automat!
pers_wgFmt_defaultFmtError2=Dacă există același format de card, acesta se va potrivi automat și operația eșuează!
pers_wgFmt_cardFormats=Formatare card
pers_wgFmt_cardFormatTesting=Test formatare card
pers_wgFmt_checkIsUsed=Formatul cardului este utilizat în {0} și nu poate fi șters!
pers_wgFmt_supportDigitsNumber=Vă rugăm să introduceți numărul de cifre acceptate de dispozitiv
#Selectați controlul persoanei
pers_widget_selectPerson=Alegeți o persoană
pers_widget_searchType1=Interogare condiționată
pers_widget_searchType2=Departament
pers_widget_deptHint=Notă: importați tot personalul departamentului selectat
pers_widget_noPerson=Nu este selectat nimeni
pers_widget_noDept=Vă rugăm să selectați un departament
pers_widget_noDeptPerson=Nu există persoane în departamentul selectat, vă rugăm să selectați din nou departamentul!
#Atributele personalului
pers_person_carPlate=Placă de înmatriculare auto
pers_person_socialSecurity=Cod Numeleric personal
pers_person_msg4=Lungimea maximă nu poate depăși 20 de biți!
pers_person_msg5=Lungimea Numelelui nu poate depăși 50 de biți!
pers_person_type=Tipul de persoană
pers_person_reseCode=Parolă auto-servisare
pers_person_IsSendMail=Notificare prin e-mail
pers_person_inactive=Dezactivați
pers_person_active=Efectiv
pers_person_employee=Angajat
pers_person_isSendMailMsg=Notificarea evenimentului trebuie să completeze căsuța de e-mail
pers_person_createTime=Data de creare
pers_person_pinFirstValid=Primul bit din numărul de angajat nu poate fi 8 sau 9.
pers_person_attrValueValid=Valoarea câmpului nu poate fi repetată
pers_person_attrValueDelimiterValid=Semnul punct și virgulă trebuie așezat la mijloc
pers_person_isSendSMS=Notificare prin SMS
pers_person_building=Numele clădirii
pers_person_unitName=Numele unitatii
pers_person_roomNo=Numărul camerei
#Atribute dinamice
pers_attr_emp_type=Tipul angajatului
pers_attr_street=Stradă
pers_attr_nation=Naționalitate
pers_attr_office_address=Adresa companiei
pers_attr_postcode=Cod poștal
pers_attr_office_phone=Telefon companie
pers_attr_home_phone=Telefon fix
pers_attr_job_title=Denumirea funcției
pers_attr_birthplace=Locul nasterii
pers_attr_polit_status=Statut politic
pers_attr_country=Zonă
pers_attr_home_address=Adresa familială
pers_attr_hire_type=Tip de angajare
pers_attr_inContract=În contract
pers_attr_outContract=În afara contractului
#Definit de atribute
pers_attribute_attrName=Arătați numele
pers_attribute_attrValue=Valoarea câmpului
pers_attribute_controlType=Tip de intrare
pers_attribute_positionX=Rând
pers_attribute_positionY=Coloană
pers_attribute_showTable=Afișați în lista de personal
pers_attrDefini_deletemsg=Acest atribut a fost deja utilizat. Sunteți sigur că doriți să îl ștergeți?
pers_attrDefini_reserved=Numelele de câmp rezervate sistemului
pers_attrDefini_msg1=Lungimea maximă nu poate depăși 30 de biți!
pers_attrDefini_msg2=Rândul și coloana deja există, vă rugăm să treceți la o altă poziție!
pers_attrDefini_attrValue_split=Vă rugăm să folosiți semnul punct și virgulă în varianta engleză ";" pentru a separa
pers_attrDefini_attrName=Numelele atributului
pers_attrDefini_sql=Declarație sql
pers_attrDefini_attrId=Numărul atributului
pers_attrDefini_select=Listă verticală
pers_attrDefini_check=Selectare multiplă
pers_attrDefini_radio=Selecție unică
pers_attrDefini_text=Caseta de intrări
pers_attrDefini_maxCol=Nu poate depăși 2 coloane
pers_attrDefini_maxLimit=A fost atins numărul maxim de atribute pentru definirea automată!
pers_attrDefini_modControlType=Modificarea tipului de intrare va șterge datele din câmpul curent al întregului personal din sistem. Doriți să continuați?
#Persoana care pleacă
pers_leavePerson_reinstated=Reintegrare
#Opexample
pers_example_newRecode=Obțineți înregistrări noi
pers_example_allRecode=Obțineți toate înregistrările
pers_custField_StatisticalType=Tip statistic
#Modificarea parametrilor personalului
pers_param_isAudit=Activați revizuirea automată a angajaților temporari
pers_param_donotChangePin=Numărul de angajat existent conține litere, iar modul personal nu poate fi modificat.
pers_param_hexChangeWarn=Numărul cardului există deja în sistemul curent, iar afișarea formatului cardului nu poate fi modificată.
pers_param_cardsChangeWarn=Există deja mai multe carduri în sistem și nu puteți modifica setările multi-card.
pers_param_maxPinLength=Lungimea numărului de angajat nou setat nu poate fi mai mică decât lungimea numărului de angajat existent.
pers_param_pinBeyondDevLength=Lungimea maximă a numărului de angajat acceptată de Dispozitiv în sistem este {0}, vă rugăm să introduceți un număr întreg mai mic decât {1}!
pers_param_cardBeyondDevLength=Lungimea maximă a numărului de card acceptată de dispozitiv care există în sistem este {0}, vă rugăm să introduceți un număr întreg mai mic decât {1}!
pers_param_checkIsExistNoAudit=În sistemul curent există personal înregistrat neauditat, care nu poate fi schimbat la audit automat!
pers_param_noSupportPinLetter=Există dispozitive în sistem care nu acceptă Numelere de angajat ce conțin litere, iar modul de număr de angajat nu poate fi schimbat.
pers_param_changePinLettersTip=Numărul de angajat acceptă un increment automat, iar modul de număr de angajat nu poate fi modificat.
pers_param_changePinIncrementTip=Suportul pentru numărul de angajat conține litere, iar modul de număr de angajat nu poate fi modificat.
pers_param_qrCode=Cod QR companie
pers_param_employeeRegistrar=Activați înregistrarea angajaților în cloud
pers_param_downloadQRCodePic=Descărcați imaginea codului QR
pers_param_qrCodeUrl=Url cod QR
pers_param_qrCodeUrlCreate=Înregistrare auto-servisare
pers_param_qrCodeUrlHref=Adresă server: port
pers_param_pinSetWarn=Există deja personal în sistemul curent, iar modul de număr de personal nu poate fi schimbat.
pers_param_selfRegistration=Activează înregistrarea automată
pers_param_infoProtection=Protecția informațiilor personale sensibile
pers_param_infoProtectionWarnMsg=După activarea opțiunii de protecție a securității informațiilor personale sensibile, datele personale sensibile implicate în acest modul vor fi desensibilizate sau ascunse, inclusiv, dar fără a se limita la nume, numere de card, numere de identitate, fotografii etc.
pers_param_templateServer=Server de extragere a șabloanelor faciale
pers_param_enableFacialTemplate=Activează extragerea șablonului facial
pers_param_templateServerAddr=Adresa serverului de extragere a șablonului facial
pers_param_templateServerWarnInfo=Atunci când extragerea șabloanelor faciale este activată, când serverul de extragere a șabloanelor faciale este online și verificarea utilizatorului este trecută, personalul va extrage implicit șabloanele faciale atunci când compară fotografiile; Când serverul de extragere a șabloanelor faciale este în modul offline, nu extrageți șabloanele faciale!
pers_param_templateServerWarnInfo1=Atunci când activați extragerea șabloanelor faciale, un dispozitiv care acceptă extragerea șabloanelor faciale trebuie conectat!
pers_param_templateServerOffline=Serverul de extragere a șabloanelor faciale este offline și nu poate extrage șabloane faciale! Vrei să continui?
pers_param_faceServer=Serviciul de comparare a backend-ului facial
pers_param_enableFaceVerify=Activează compararea backend-ului facial
pers_param_faceServerAddr=Adresa serviciului de comparare a backend-ului față
pers_param_faceServerSecret=Cheia serviciului de comparare a backend-ului față
#Țara sau cetățenia
pers_person_nationality=Țara/zona
pers_nationality_angola=Angola
pers_nationality_afghanistan=Afganistan
pers_nationality_albania=Albania
pers_nationality_algeria=Algeria
pers_nationality_america=Statele Unite
pers_nationality_andorra=Andorra
pers_nationality_anguilla=Anguilla
pers_nationality_antAndBar=Antigua si Barbuda
pers_nationality_argentina=Argentina
pers_nationality_armenia=Armenia
pers_nationality_ascension=Ascension
pers_nationality_australia=Australia
pers_nationality_austria=Austria
pers_nationality_azerbaijan=Azerbaijan
pers_nationality_bahamas=Bahamas
pers_nationality_bahrain=Bahrain
pers_nationality_bangladesh=Bangladesh
pers_nationality_barbados=Barbados
pers_nationality_belarus=Belarus
pers_nationality_belgium=Belgia
pers_nationality_belize=Belize
pers_nationality_benin=Benin
pers_nationality_bermudaIs=Insulele Bermude
pers_nationality_bolivia=Bolivia
pers_nationality_botswana=Botswana
pers_nationality_brazil=Brazilia
pers_nationality_brunei=Brunei
pers_nationality_bulgaria=Bulgaria
pers_nationality_burkinaFaso=Burkina Faso
pers_nationality_burma=Myanmar
pers_nationality_burundi=Burundi
pers_nationality_cameroon=Camerun
pers_nationality_canada=Canada
pers_nationality_caymanIs=Insulele cayman
pers_nationality_cenAfrRepub=Republica centrafricană
pers_nationality_chad=Chad
pers_nationality_chile=Chile
pers_nationality_china=China
pers_nationality_colombia=Columbia
pers_nationality_congo=Republica Congo
pers_nationality_cookIs=Insulă
pers_nationality_costaRica=Costa rica
pers_nationality_cuba=Cuba
pers_nationality_cyprus=Cipru
pers_nationality_czechRep=Republica Cehă
pers_nationality_denmark=Danemarca
pers_nationality_djibouti=Djibouti
pers_nationality_dominicaRep=Republica Dominicană
pers_nationality_ecuador=Ecuador
pers_nationality_egypt=Egipt
pers_nationality_eISalvador=Salvador
pers_nationality_england=Regatul Unit
pers_nationality_estonia=Estonia
pers_nationality_ethiopia=Etiopia
pers_nationality_fiji=Fiji
pers_nationality_finland=Finlanda
pers_nationality_france=Franţa
pers_nationality_freGui=Guyana Franceză
pers_nationality_gabon=Gabon
pers_nationality_gambia=Gambia
pers_nationality_georgia=Georgia
pers_nationality_germany=Germania
pers_nationality_ghana=Ghana
pers_nationality_gibraltarm=Gibraltar
pers_nationality_greece=Grecia
pers_nationality_grenada=Grenada
pers_nationality_guam=Guam
pers_nationality_guatemala=Guatemala
pers_nationality_guinea=Guinea
pers_nationality_guyana=Guyana
pers_nationality_haiti=Haiti
pers_nationality_honduras=Honduras
pers_nationality_hungary=Ungaria
pers_nationality_iceland=Islanda
pers_nationality_india=India
pers_nationality_indonesia=Indonezia
pers_nationality_iran=Iran
pers_nationality_iraq=Irak
pers_nationality_ireland=Irlanda
pers_nationality_israel=Israel
pers_nationality_italy=Italia
pers_nationality_ivoryCoast=Coasta de Fildeş
pers_nationality_jamaica=Jamaica
pers_nationality_japan=Japonia
pers_nationality_jordan=Jordan
pers_nationality_kenya=Kenya
pers_nationality_korea=Coreea de Sud
pers_nationality_kuwait=Kuwait
pers_nationality_kyrgyzstan=Kârgâzstan
pers_nationality_laos=Laos
pers_nationality_latvia=Latvia
pers_nationality_lebanon=Liban
pers_nationality_lesotho=Lesoto
pers_nationality_liberia=Liberia
pers_nationality_libya=Libia
pers_nationality_liechtenstein=Liechtenstein
pers_nationality_lithuania=Lituania
pers_nationality_luxembourg=Luxemburg
pers_nationality_madagascar=Madagascar
pers_nationality_malawi=Malawi
pers_nationality_malaysia=Malaezia
pers_nationality_maldives=Maldive
pers_nationality_mali=Mali
pers_nationality_malta=Malta
pers_nationality_marianaIs=Insulele Mariane
pers_nationality_martinique=Martinica
pers_nationality_mauritius=Mauritius
pers_nationality_mexico=Mexic
pers_nationality_moldova=Moldova
pers_nationality_monaco=Monaco
pers_nationality_montseIs=Montserrat
pers_nationality_morocco=Maroc
pers_nationality_mozambique=Mozambic
pers_nationality_namibia=Namibia
pers_nationality_nauru=Nauru
pers_nationality_nepal=Nepal
pers_nationality_netAnti=Antilele Olandeze
pers_nationality_netherlands=Olanda
pers_nationality_newZealand=Noua Zeelandă
pers_nationality_nicaragua=Nicaragua
pers_nationality_niger=Niger
pers_nationality_nigeria=Nigeria
pers_nationality_norKorea=Coreea
pers_nationality_norway=Norvegia
pers_nationality_oman=Oman
pers_nationality_pakistan=Pakistan
pers_nationality_panama=Panama
pers_nationality_papNewCui=Papua Noua Guinee
pers_nationality_paraguay=Paraguay
pers_nationality_peru=Peru
pers_nationality_philippines=Filipine
pers_nationality_poland=Polonia
pers_nationality_frenPolyne=Polinezia Franceză
pers_nationality_portugal=Portugalia
pers_nationality_puerRico=Puerto rico
pers_nationality_qatar=Qatar
pers_nationality_reunion=Reunion
pers_nationality_romania=România
pers_nationality_russia=Rusia
pers_nationality_saiLueia=Sfânta lucia
pers_nationality_saintVinc=Saint vincent
pers_nationality_samoa_eastern=Samoa de Est
pers_nationality_samoa_western=Samoa de Vest
pers_nationality_sanMarino=San marino
pers_nationality_saoAndPrinc=Sao Tome şi Principe
pers_nationality_sauArabia=Arabia Saudită
pers_nationality_senegal=Senegal
pers_nationality_seychelles=Seychelles
pers_nationality_sieLeone=Sierra Leone
pers_nationality_singapore=Singapore
pers_nationality_slovakia=Slovacia
pers_nationality_slovenia=Slovenia
pers_nationality_solomonIs=Insulele Solomon
pers_nationality_somali=Somalia
pers_nationality_souAfrica=Africa de Sud
pers_nationality_spain=Spania
pers_nationality_sriLanka=Sri lanka
pers_nationality_sudan=Sudan
pers_nationality_suriname=Suriname
pers_nationality_swaziland=Swaziland
pers_nationality_sweden=Suedia
pers_nationality_switzerland=Elveţia
pers_nationality_syria=Siria
pers_nationality_tajikstan=Tajikistan
pers_nationality_tanzania=Tanzania
pers_nationality_thailand=Tailanda
pers_nationality_togo=Togo
pers_nationality_tonga=Tonga
pers_nationality_triAndToba=Trinidad şi Tobago
pers_nationality_tunisia=Tunisia
pers_nationality_turkey=Turcia
pers_nationality_turkmenistan=Turkmenistan
pers_nationality_uganda=Uganda
pers_nationality_ukraine=Ucraina
pers_nationality_uniArabEmira=Emiratele Arabe Unite
pers_nationality_uruguay=Uruguay
pers_nationality_uzbekistan=Uzbekistan
pers_nationality_venezuela=Venezuela
pers_nationality_vietnam=Vietnam
pers_nationality_yemen=Yemen
pers_nationality_serbia=Serbia
pers_nationality_zimbabwe=Zimbabwe
pers_nationality_zambia=Zambia
pers_nationality_aruba=Aruba
pers_nationality_bhutan=Bhutan
pers_nationality_bosnia_herzegovina=Bosnia şi Herţegovina
pers_nationality_cambodia=Cambodgia
pers_nationality_congoD=Congo (RDC)
pers_nationality_comoros=Comoros
pers_nationality_capeVerde=Capul Verde
pers_nationality_croatia=Croaţia
pers_nationality_dominica=Dominica
pers_nationality_eritrea=Eritrea
pers_nationality_micronesia=Micronezia
pers_nationalit_guineaBissau=Guinea-Bissau
pers_nationalit_equatorialGuinea=Guineea Ecuatorială
pers_nationalit_hongkong=Hong Kong China
pers_nationalit_virginIslands=Insulele Virgine ale Statelor Unite
pers_nationalit_britishVirginIslands=Insulele Virgine Britanice
pers_nationalit_kiribati=Kiribati
pers_nationalit_mongolia=Mongolia
pers_nationalit_marshall=Insulele Marshall
pers_nationalit_macedonia=Macedonia
pers_nationalit_montenegro=Muntenegru
pers_nationalit_mauritania=Mauritania
pers_nationalit_palestine=Palestina
pers_nationalit_palau=Palau
pers_nationalit_rwanda=Ruanda
pers_nationalit_saintKittsNevis=Federația Saint Kitts și Nevis
pers_nationalit_timorLeste=Timorul de Est
pers_nationalit_taiwan=Taiwan, China
pers_nationalit_tuvalu=Tuvalu
pers_nationalit_vanuatu=Vanuatu
#Listare cartelă
pers_person_cardprint=Listare cartelă
pers_cardTemplate_tempSelect=Selectare șablon
pers_cardTemplate_printerSelect=Selectare imprimantă
pers_cardTemplate_front=Latura pozitiva
pers_cardTemplate_opposite=Partea inversă
pers_cardTemplate_entryDate=Data de intrare a lucrării
pers_cardTemplate_photo=Fotografie
pers_cardTemplate_uploadFail=Încărcarea imaginii a eșuat!
pers_cardTemplate_jpgFormat=Încărcați doar fișiere în format jpg!
pers_cardTemplate_printStatus=Starea tipăririi
pers_cardTemplate_waiting=În așteptarea tipăririi
pers_cardTemplate_printing=Tipărire
pers_cardTemplate_printOption=Opțiune de tipărire
pers_cardTemplate_duplexPrint=Imprimare duplex
pers_cardTemplate_frontOnly=Tipărește numai față
#app
pers_app_delPers=Persoana pe care doriți să o ștergeți nu există pe server
pers_app_deptIsNull=Departamentul corespunzător numărului sau Numelelui de departament nu poate fi găsit
pers_app_personNull=Persoana nu există
pers_app_pinExist=Numărul persoanei există deja
pers_app_dateError=Formatul datei este greșit, vă rugăm să respectați formatul corect: 2016-08-08
#Api
pers_api_selectPhotoInvalid=Fotografia nu este validă, vă rugăm să încărcați din nou
pers_api_dateError=Formatul datei este incorect
pers_api_personNotExist=Persoana nu există
pers_api_cardsPersSupport=Sistemul nu pornește cu multi-card al unei persoane, selectarea cardului secundar este invalidă
pers_api_department_codeOrNameNotNull=Codul departamentului sau numele departamentului nu pot fi goale
pers_api_deptSortNoIsNull=Sortarea departamentului nu poate fi goală!
pers_api_deptSortNoError=Valoarea sortării departamentului trebuie să fie între 1-999999!
pers_api_dataLimit=Numărul actual de operațiuni este {0}, depășind limita de {1}. Vă rugăm să operați în loturi!
pers_api_cardTypeError=Eroare de tip card
#API Șablon biologic personal
pers_api_fingerprintExisted=Amprenta persoanei există deja!
pers_api_validtypeIncorrect=Valabilitatea este incorectă!
pers_api_dataNotExist=Templateno nu există!
pers_api_templateNoRang=Gama de șabloane pentru amprente digitale este între 0-9!
pers_api_templateIsNull=Șablonul nu poate fi gol!
pers_api_versionIsNumber=Versiunea poate introduce numai numere!
#Auto-înregistrarea temporară a angajaților
pers_tempPersion_pinOnlyNumber=Numărul de angajat poate conține doar cifre!
#Biotimp
pers_h5_personAvatarNotNull=Avatarul este gol
pers_h5_personMobileRepeat=Numărul de telefon există deja
pers_h5_personEmailRepeat=Cutia poștală există deja
pers_h5_pwdIsRepetition=Parole vechi și noi repetate
pers_h5_personIdNull=ID-ul de angajat este gol
pers_h5_pinOrEmailIsNull=Vă rugăm să completați numărul și e-mailul
pers_emailTitle_resetPassword=Modifică parolă
pers_emailContent_resetPassword=Link-ul este valabil 24 de Oră, copiați linkul în browser pentru a modifica parola:
pers_h5_tokenIsNull=Certificatul este gol
pers_h5_tokenError=Certificat incorect sau invalid
pers_h5_oldPwdIsError=Parola veche este incorectă
pers_api_resetPasswordSuccess=Parola a fost actualizată
pers_api_resetPasswordFail=Schimbarea parolă nu a reușit
pers_api_forgetPassword=Resetare parolă
pers_api_confirmSubmit=Confirmați trimiterea
pers_api_confirmPwdCaution=Faceți clic pe [OK] pentru a confirma noua parolă
pers_api_pwdRule=Parola trebuie să conțină cel puțin un simbol sau un număr și să aibă o lungime de cel puțin 8-12 caractere
pers_h5_personPinFormatNumber=Numărul de angajat poate conține doar cifre sau litere
pers_h5_persEmailNoExist=E-mailul este completat incorect
pers_h5_pageNull=Eroare parametru paginare
pers_h5_personPinNotStartWithZero=Numărul de angajat nu poate începe cu 0
pers_h5_personPinTooLong=Numărul persoanei este prea lung
pers_h5_personPinInValid=Numărul este deja folosit
pers_h5_imgSizeError=Rugăm încărcați o imagine până la limita de 10M!
pers_h5_confirmAndContinue=Confirmă și continuă
#Eroare de încadrare față
pers_face_poorResolution=Rezoluția imaginii este sub 80.000 pixeli
pers_face_noFace=Nu a fost detectată fața
pers_face_manyFace=Mai multe fețe detectate
pers_face_smallFace=Raportul feței este prea mic
pers_face_notColor=Imaginea nu are culoare
pers_face_seriousBlur=Imaginea este încețoșată
pers_face_intensivelyLight=Imaginea este expusă prea puternic 
pers_face_badIllumination=Luminozitatea imaginii este prea scăzută
pers_face_highNoise=Imaginea are zgomot ridicat
pers_face_highStretch=Fața este prea întinsă
pers_face_covered=Fața este blocată
pers_face_smileOpenMouth=Zâmbiți prea mult
pers_face_largeAngle=Unghiul de deviere a feței este prea mare
pers_face_criticalIllumination=Luminozitatea imaginii este critică
pers_face_criticalLargeAngle=Unghiul de deviere a feței este critic
pers_face_validFailMsg=Nu s-au putut detecta fețe, motivele sunt:
pers_face_failType=Tipul de eroare de decupare a feței
pers_face_photoFormatError=Formatul fotografiei este incorect, vă rugăm să încărcați un fișier în format JPG / PNG.
pers_face_notUpdateMsg=Nu s-a generat imaginea feței, nu actualizați imaginea feței.
# 健康 申报
pers_health_enable=Activați declarația informațiilor despre sănătate
pers_health_attrExposure=Orice expunere la cazuri suspecte
pers_health_attrSymptom=Orice simptom din ultimele 14 zile
pers_health_attrVisitCity=Oraș vizitat în ultimele 14 zile
pers_health_attrRemarks=Observații privind sănătatea
pers_health_symptomCough=Tuse
pers_health_symptomFever=Febra
pers_health_symptomPolypena=Probleme respiratorii
pers_health_declaration=Declarație de sănătate
pers_health_aggrement=Am fost de acord cu persoanele care nu completează informațiile conform cerințelor nu li se va permite accesul, iar vizitatorii care nu au raportat-o cu adevărat nu își pot continua vizita și trebuie să-și asume responsabilitățile legale corespunzătoare.
pers_health_visitCity_notEmpty=Orașul vizitat nu poate fi gol!
pers_health_notAgree=Vă rugăm să verificați acordul pentru a continua.
#人员名单库
pers_personnal_list_manager=Manager listă
pers_personnal_list=Listă de personal
pers_personnal_list_scheme=Mod domeniu
pers_personnal_list_name=Numele listei personale
pers_personnal_list_group_str_id=ID grup de listă
pers_personnal_list_personCount=Numărul de persoane
pers_personnal_list_tag=Definit de utilizator
pers_personnal_list_type=Tipul listei de personal
pers_personnallist_addPerson_repo=Adăugați o persoană la repo
pers_personnallist_sendPersonnallist=Bibliotecă listă distribuită
pers_personnallist_sendPerson=Emitent
pers_personnallist_notDel_existPerson=Biblioteca de liste conține persoane și nu poate fi ștearsă
pers_personnallist_peopleInRoster=Există încă persoane în biblioteca de liste
pers_personnallist_associationNotExist=Asocierea dintre dispozitivul principal și biblioteca de liste nu există
pers_personnal_list_person=Listă personală persoană
pers_personnal_list_dev=Listează permisiunile bibliotecii
pers_personnal_list_addDev=Adaugă dispozitiv
pers_personnal_list_name_isExist=Numele există deja
pers_personnal_bannedList=Bibliotecă listă interzisă
pers_personnal_allowList=Permite biblioteca de liste
pers_personnal_redList=Biblioteca Lista Roșie
pers_personnal_attGroup=Grup de prezență
pers_personnal_passList=Lista de trecere
pers_personnal_banList=Lista interzisă
pers_personnal_visPassList=Lista permiselor de vizitator
pers_personnal_visBanList=Lista de vizitatori interzisi
pers_personnal_databaseHasBeenDistributed=Biblioteca de liste selectată a fost distribuită și nu poate fi ștearsă
pers_personnel_sendError_dueTo=Eșec la livrare Motivul eșecului:
pers_personnel_sendError_reson={0} există în lista interzisă, vă rugăm să ștergeți și să adăugați
#比对照片-样片示例
pers_examplePic_Tip=Ar trebui să îndeplinească următoarele cerințe:
pers_examplePic_Tip1=1. culoarea de fundal este alb pur, iar personalul poartă haine închise la culoare;
pers_examplePic_Tip2=2. Fotografiile electronice sunt în format de fișier JPG, PNG, JPEG, intervalul de pixeli recomandat: 480*640 < pixeli < 1080*1920;
pers_examplePic_Tip3=3. Portretele din fotografiile electronice ar trebui să deschidă ochii și să privească drept înainte și să se asigure că pupilele sunt clar vizibile;
pers_examplePic_Tip4=4. Portretul din fotografia electronică ar trebui să aibă o expresie neutră, iar tu poți zâmbi, dar nu trebuie să-ți arăți dinții;
pers_examplePic_Tip5=5. Portretul din fotografia electronică trebuie să fie clar, cu culori naturale, straturi bogate și fără distorsiuni evidente. Fără umbre, lumini sau reflexii pe fețele sau fundalurile portretelor; contrastul și luminozitatea sunt adecvate.
pers_examplePic_description=Exemplu corect
pers_examplePic_error=Exemplu de eroare:
pers_examplePic_error1=Expresie exagerată (zâmbet excesiv)
pers_examplePic_error2=Lumina este prea întunecată
pers_examplePic_error3=Fața este prea mică (rezoluția este prea mică)
pers_applogin_enabled=Activează autentificarea aplicației
pers_applogin_disable=Dezactivează autentificarea aplicației
pers_applogin_status=Starea activării conectării aplicației