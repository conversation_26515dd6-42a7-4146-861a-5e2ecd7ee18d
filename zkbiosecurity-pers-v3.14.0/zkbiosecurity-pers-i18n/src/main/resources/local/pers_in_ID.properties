#pers common.
#here other module can also use the label from pers.
pers_module=Personil
pers_common_addPerson=Tambah Personil
pers_common_delPerson=Hapus Personil
pers_common_personCount=Jumlah Personil
pers_common_browsePerson=Jelajahi Personil
#左侧菜单
pers_person_manager=Manajemen Personalia
pers_person=Personel
pers_department=Departemen
pers_leave=Personil yang Diberhentikan
pers_tempPerson=menunda ulasan
pers_attribute=Atribut Khusus
pers_card_manager=Manajemen Kartu
pers_card=Kartu
pers_card_issue=Catatan Kartu yang Diterbitkan
pers_wiegandFmt=Format Wiegand
pers_position=Posisi
#人员
pers_person_female=Perempuan
pers_person_male=Pria
pers_person_pin=ID Personel
pers_person_departmentChange=Sesuaikan Departemen
pers_personDepartment_changeLevel=Izin beralih departemen
pers_person_gender=<PERSON><PERSON>
pers_person_detailInfo=Detail Personel
pers_person_accSet=Kontrol Akses
pers_person_accSetting=Pengaturan Kontrol Akses
pers_person_attSet=Waktu Kehadiran
pers_person_eleSet=Kontrol Elevator
pers_person_eleSetting=Pengaturan Kontrol Elevator
pers_person_parkSet=Daftar Plat
pers_person_pidSet=Sertifikat Pribadi
pers_person_insSet=Kios Wajah
pers_person_aiSet=Kecerdasan Wajah
pers_person_payrollSet=Pengaturan Gaji
pers_person_psgSet=Pengaturan saluran
pers_person_lockerSet=Pengaturan Loker
pers_person_sisSet=Pengaturan pemeriksaan keamanan
pers_person_vdbSet=Pengaturan interkom visual
pers_person_firstName=Nama Depan
pers_person_lastName=Nama Belakang
pers_person_name=Nama Depan
pers_person_wholeName=Nama
pers_person_fullName=Pertama menengah terakhir
pers_person_cardNum=Jumlah Kartu yang Dimiliki
pers_person_deptNum=Jumlah departemen yang terlibat
pers_person_dataCount=Statistik
pers_person_regFinger=Sidik jari
pers_person_reg=Daftar
pers_person_password=Kata Sandi Verifikasi Perangkat
pers_person_personDate=Tanggal Pekerjaan
pers_person_birthday=Ulang Tahun
pers_person_mobilePhone=Ponsel
pers_person_personDevAuth=Izin Orang dan Perangkat
pers_person_email=Email
pers_person_browse=Jelajahi
pers_person_authGroup=Tingkat Akses
pers_person_setEffective=Tetapkan Waktu Efektif
pers_person_attArea=Area Kehadiran
pers_person_isAtt=Perhitungan Kehadiran
pers_person_officialStaff=Staf Resmi
pers_person_probationStaff=Staf Percobaan
pers_person_identity_category=Kategori Identitas
pers_person_devOpAuth=Peran Operasi Perangkat
pers_person_msg1=Nomor kartu aktual dan kode situs harus diisi pada saat yang sama!
pers_person_msg2=Silakan masukkan angka 3-4.
pers_person_msg3=Kesalahan format!
pers_person_imgPixel=(Ukuran Optimal 120 * 140).
pers_person_cardLengthDigit=Silakan masukkan nomornya.
pers_person_cardLengthHexadecimal=Silakan masukkan angka atau abcdef huruf!
pers_person_to=Kepada
pers_person_templateCount=Jumlah Sidik Jari
pers_person_biotemplateCount=Jumlah Templat Biologis
pers_person_regFace=Wajah
pers_person_regVein=Vena Jari
pers_person_faceTemplateCount=Jumlah Wajah
pers_person_VeinTemplateCount=Kuantitas Vena Jari
pers_person_palmTemplateCount=Kuantitas Palm
pers_person_cropFace=Gambar Wajah
pers_person_cropFaceCount=Jumlah Gambar Wajah
pers_person_faceBiodataCount=Nomor wajah yang terlihat
pers_person_irisCount=Jumlah Iris
pers_person_batchToDept=Departemen Baru
pers_person_changeReason=Alasan Transfer
pers_person_selectedPerson=Orang yang Dipilih
pers_person_duressPwdError=Kata Sandi Berulang
pers_person_completeDelPerson=Hapus
pers_person_recover=Pulihkan
pers_person_nameNoComma=Tidak boleh menyertakan koma.
pers_person_firstNameNotEmpty=Nama depan tidak boleh kosong.
pers_person_lastNameNotEmpty=Nama belakang tidak boleh kosong.
pers_person_mobilePhoneValidate=Silakan masukkan nomor ponsel yang valid.
pers_person_phoneNumberValidate=Silakan masukkan nomor telepon yang valid.
pers_person_postcodeValidate=Silakan masukkan kode pos yang valid.
pers_person_idCardValidate=Silakan masukkan nomor idCard yang valid.
pers_person_pwdOnlyLetterNum=Kata sandi hanya dapat berisi huruf atau angka.
pers_person_certNumOnlyLetterNum=Nomor Sertifikat hanya dapat berisi huruf atau angka.
pers_person_oldDeptEqualsNewDept=Departemen baru yang Anda sesuaikan tidak boleh sama dengan departemen asli.
pers_person_disabled=Akses Dilumpuhkan
pers_person_emailError=Silakan masukkan alamat email yang valid.
pers_person_driverPrompt=Silakan instal driver perangkat! Klik OK untuk mengunduh driver.
pers_person_readIDCardFailed=Kegagalan gesek!
pers_person_cardPrompt=Simpan ID card di wilayah ...
pers_person_iDCardReadOpenFailed=Tidak ada pembaca kartu ID yang terdeteksi!
pers_person_iDCardNotFound=Kartu ID tidak ditemukan, silakan coba lagi!
pers_person_nameValid=Mendukung Bahasa Mandarin, Bahasa Inggris, angka, '-', '_'.
pers_person_nameValidForEN=Mendukung bahasa Inggris, angka, '-', '_', '.'.
pers_person_pinPrompt=Silakan masukkan karakter dari alfabet bahasa Inggris, angka.
pers_person_pinSet=Pengaturan ID Personel
pers_person_supportLetter=Surat Dukungan
pers_person_cardsSupport=Beberapa Kartu per Orang
pers_person_SupportDefault=Auto-Buat ID Personil
pers_person_noSpecialChar=Nama tidak dapat memasukkan karakter khusus!
pers_person_pinInteger=Silakan masukkan digit.
pers_pin_noSpecialChar=Nomor pribadi tidak boleh berisi karakter khusus!
pers_op_capture=Tangkap
pers_person_cardDuress=Nomor Kartu Diulang
pers_person_pwdException=Pengecualian Kata Sandi
pers_person_systemCheckTip=Periksa informasi personel untuk format yang tepat!
pers_person_immeHandle=Segera Tangani
pers_person_cardSet=Pengaturan Kartu
pers_person_tempPersonSet=Pengaturan personel yang tertunda
pers_person_cardsReadMode=Mode Membaca Kartu
pers_person_cardsReadModeReadHead=Baca Oleh Pengendali
pers_person_cardsReadModeID180=Baca Oleh ID180
pers_person_IDReadMode=Mode Membaca Kartu ID
pers_person_IDReadModeIDCardReader=Pembaca Kartu ID
pers_person_IDReadModeTcpReadHead=TCP / IP Reader
pers_person_physicalNo=Nomor Kartu Fisik Kartu ID
pers_person_physicalNoToCardNo=Gunakan nomor kartu fisik kartu ID
pers_person_ReadIDCard=Baca kartu ID
pers_person_templateBioTypeNumber=Nomor Jenis Template Biometrik
pers_person_templateValidType=Validitas Templat Biometrik
pers_person_templateBioType=Jenis Templat Biometrik
pers_person_templateVersion=Versi Templat Biometrik
pers_person_template=Template Biometrik
pers_person_templateNo=No. Templat Biometrik
pers_person_templateNoIndex=Indeks Templat Biometrik
pers_person_templateDataUpdate=Memperbarui data saat template biometrik ada:
pers_person_templateValidTypeNoNull=Validitas templat biometrik tidak boleh kosong!
pers_person_templateBioTypeNoNull=ID Personel: {0}, tipe templat biometrik tidak boleh kosong!
pers_person_templateVersionNoNull=ID Personel: {0}, versi templat biometrik tidak boleh kosong!
pers_person_templateNoNull=ID Personel: {0}, templat biometrik tidak boleh kosong!
pers_person_templateNoNoNull=ID Personel: {0}, No. templat biometrik tidak boleh kosong!
pers_person_templateNoIndexNoNull=ID Personel: {0}, indeks templat biometrik tidak boleh kosong!
pers_person_templateError=ID Personel: {0}, templat biometrik salah!
pers_person_bioDuress=Paksaan
pers_person_universal=Umum
pers_person_voice=Cetak suara
pers_person_iris=Iris
pers_person_retina=Retina
pers_person_palmPrints=Palm
pers_person_metacarpalVein=Palm Vein
pers_person_visibleFace=Wajah Terlihat
pers_person_pinError=ID Personel {0} tidak ada, tidak dapat memproses data ini!
pers_person_pinException=Pengecualian ID Personil
pers_person_pinAutoIncrement=Peningkatan Personil ID Otomatis
pers_person_resetSelfPwd=Setel Ulang Kata Sandi Masuk Sendiri
pers_person_picMaxSize=Resolusi gambar terlalu tinggi dan resolusi direkomendasikan di bawah {0}.
pers_person_picMinSize=Resolusi gambar terlalu rendah dan resolusi disarankan di atas {0}.
pers_person_cropFaceShow=Lihat foto perbandingan
pers_person_faceNoFound=Wajah tidak dikenal
pers_person_biometrics=Jenis Biometrik
pers_person_photo=Foto pribadi
pers_person_visibleFaceTemplate=Template wajah yang terlihat
pers_person_infraredFaceTemplate=Dekat Template Wajah Inframerah
pers_person_delBioTemplate=Hapus Data Biometrik
pers_person_delBioTemplateSelect=Silakan pilih templat biologis yang akan dihapus!
pers_person_infraredFace=Dekat wajah inframerah
pers_person_notCard=Tidak termasuk kartu
pers_person_notRegFinger=Tidak termasuk sidik jari
pers_person_notMetacarpalVein=Tidak termasuk urat telapak tangan
pers_person_notRegVein=Tidak termasuk urat jari
pers_person_notIris=Kecuali iris
pers_person_notInfraredFaceTemplate=Tidak termasuk template wajah inframerah dekat
pers_person_notVisibleFaceTemplate=Tidak termasuk template wajah cahaya tampak
pers_person_notVisibleFacePhoto=Tidak termasuk foto wajah yang terlihat
pers_person_visibleFacePhoto=Foto wajah yang terlihat
pers_person_change=Penyesuaian personel
pers_person_cropFaceUsePhoto=Apakah Anda ingin menampilkan foto perbandingan sebagai avatar?
pers_person_photoUseCropFace=Apakah Anda ingin menggunakan avatar untuk menghasilkan foto perbandingan?
pers_person_selectCamera=Pilih kamera tangkap
pers_person_delCropFaceMsg=Tidak ada foto perbandingan untuk dihapus!
pers_person_disabledNotOp=Orang itu telah dinonaktifkan dan tidak bisa beroperasi!
pers_person_visiblePalm=Palm cahaya tampak
pers_person_notVisiblePalm=Tidak termasuk palm cahaya terlihat
pers_person_selectDisabledNotOp=Ada personel cacat di pihak yang dipilih, tidak bisa beroperasi!
pers_person_photoUseCropFaceAndTempalte=Apakah Anda ingin menggunakan avatar untuk menghasilkan foto perbandingan dan templat wajah?
pers_person_photoUseTempalte=Apakah Anda ingin menggunakan foto untuk menghasilkan templat wajah?
pers_person_createCropFace=Hasilkan foto perbandingan
pers_person_createFaceTempalte=Hasilkan templat wajah
pers_person_faceTempalte=Templat wajah
pers_person_extractFaceTemplate=Mengekstrak templat wajah
pers_person_createSuccess=Berhasil dihasilkan
pers_person_createFail=Generasi gagal
pers_person_serverConnectWarn=Alamat server, nama pengguna, dan kata sandi tidak dapat kosong!
pers_person_serverOffline=Server ekstraksi templat wajah offline
pers_person_faceTemplateError1=Deteksi wajah gagal
pers_person_faceTemplateError2=Oklusi wajah
pers_person_faceTemplateError3=Tidak cukup jelas
pers_person_faceTemplateError4=Sudut wajah terlalu besar
pers_person_faceTemplateError5=Deteksi langsung gagal
pers_person_faceTemplateError6=Ekstrasi templat wajah gagal
pers_person_cropFaceNoExist=Foto perbandingan tidak ada
pers_person_disableFaceTemplate=Fungsi ekstraksi templat wajah tidak diaktifkan, tidak dapat ekstraksi templat!
pers_person_cropFacePhoto=Foto Perbandingan Wajah
pers_person_vislightPalmPhoto=Foto Perbandingan Palm
pers_person_serverOfflineWarn=Server ekstraksi templat wajah diluar talian dan tidak dapat mengaktifkan fitur ini!
pers_person_serverConnectInfo=Tolong uji apakah server ekstraksi templat wajah online?
pers_person_notModified=Nomor telepon seluler tidak dapat diubah.
pers_person_syncAcms=Sinkronkan staf dengan ACMS
pers_person_startUpdate=Mulai memperbarui informasi karyawan.
pers_person_updateFailed=Pembaruan informasi karyawan gagal!
#控制器发卡
pers_person_readCard=Kartu Masalah dari Perangkat
pers_person_stopRead=Hentikan Masalah
pers_person_chooseDoor=Pilih Pintu
pers_person_readCarding=Kartu mengeluarkan, tolong coba lagi nanti!
#抓拍照片
pers_capture_catchPhoto=Ambil Foto
pers_capture_preview=Pratinjau
#部门
pers_dept_entity=Departemen
pers_dept_deptNo=Nomor Departemen
pers_dept_deptName=Nama Departemen
pers_dept_parentDeptNo=Nomor Departemen Induk
pers_dept_parentDeptName=Nama Departemen Induk
pers_dept_parentDept=Departemen Induk
pers_dept_note=Jika departemen baru tidak muncul dalam daftar, silakan hubungi administrator untuk mengotorisasi ulang pengguna untuk mengedit departemen!
pers_dept_exit=Berisi
pers_dept_auth=Pengguna sistem terikat
pers_dept_parentMenuMsg=Departemen superior tidak dapat ditetapkan sama dengan departemen yang lebih rendah.
pers_dept_initDept=Umum
pers_dept_deptMarket=Departemen Pemasaran
pers_dept_deptRD=Departemen Pengembangan
pers_dept_deptFinancial=Departemen Keuangan
pers_dept_nameNoSpace=Nama Departemen tidak dapat memulai atau mengakhiri dengan spasi.
pers_dept_nameExist=Nama departemen sudah ada
pers_dept_changeLevel=Apakah akan beralih ke level departemen ini
pers_dept_noSpecialChar=Nomor departemen tidak boleh berisi karakter khusus!
pers_dept_NameNoSpecialChar=Nama departemen tidak boleh berisi karakter khusus!
pers_dept_noModifiedParent=Departemen superior tidak dapat diubah!
#职位
pers_position_entity=Posisi
pers_position_code=Nomor Posisi
pers_position_name=Nama Posisi
pers_position_notExist=Posisi tidak ada!
pers_position_sortNo=Sortir
pers_position_parentName=Posisi Induk
pers_position_parentCode=Nomor Posisi Induk
pers_position_batchToPosition=Posisi Baru
pers_position_nameExist=Nama posisi ini sudah ada
pers_position_change=Ubah Posisi
pers_position_parentMenuMsg=Posisi induk tidak dapat ditetapkan sebagai sub-posisi.
pers_position_nameNoSpace=Nama posisi tidak dapat dimulai atau diakhiri dengan spasi.
pers_position_existSub={0} :Berisi sub-posting tidak dapat dihapus
pers_position_existPerson={0} :Personil tidak dapat dihapus
pers_position_importTemplate=Templat impor posisi
pers_position_downloadTemplate=Unduh dan impor template
pers_position_codeNotEmpty=Nomor posisi tidak boleh kosong
pers_position_nameNotEmpty=Nama posisi tidak boleh kosong
pers_position_nameNoSpecialChar=Nama posisi {0} tidak boleh mengandung karakter khusus!
pers_position_noSpecialChar=Nomor posisi {0} tidak boleh karakter khusus!
pers_position_codeLength=Nomor posisi {0} panjangnya melebihi 30 digit
pers_position_nameLength=Data yang nama posisinya {0} lebih panjang dari {1}
pers_position_codeExist=Nomor posisi {0} sudah ada
#证件
pers_cert_type=Jenis Sertifikat
pers_cert_number=Nomor Sertifikat
pers_cert_name=Nama Sertifikat
pers_cert_numberExist=Nomor sertifikat sudah ada.
#导出
pers_export_allPersPerson=Semua Orang
pers_export_curPersPerson=Orang Sekarang
pers_export_template=Ekspor Template
pers_export_personInfo=Personel Ekspor
pers_export_personInfoTemplate=Unduh Template Impor Personil
pers_export_personBioTemplate=Ekspor Template Biometrik
pers_export_basicInfo=Informasi Dasar
pers_export_customAttr=Atribut Khusus
pers_export_templateComment=Nama bidang, kunci utama? unik? izinkan nilai nol? ({0},{1},{2},{3})
pers_export_templateFileName=Templat Impor Personil
pers_export_bioTemplateFileName=Templat Biometrik Personel
pers_export_deptInfo=Departemen Ekspor
pers_export_deptTemplate=Unduh Templat Impor Departemen
pers_export_deptTemplateFileName=Templat Impor Departemen
pers_export_personPhoto=Foto Ekspor Personel
pers_export_allPhotos=Semua Foto (pilih semua orang)
pers_export_selectPhotoToExport=Pilih ID awal dan akhir ID untuk mengekspor foto personel.
pers_export_fromId=Dari ID
pers_export_toId=Kepada
pers_export_certNumberComment=Jenis sertifikat diperlukan setelah mengisi nomor sertifikat
pers_export_templateCommentName=Nama bidang: ({0})
pers_export_dataExist=Harap pastikan bahwa data yang diimpor sudah ada di sistem
pers_export_cardNoTip=Nomor kartu berbilang dipisahkan dengan &
pers_carNumber_importTip=Nomor plat nomor (beberapa plat nomor& dipisahkan)
#导入
pers_import_certNumberExist=Nomor sertifikat {0} sudah ada.
pers_import_complete=Lengkap
pers_import_password=Kata Sandi Orang
pers_import_fail=Baris {0} gagal: {1}
pers_import_overData=Anda mengimpor {0} orang; sistem hanya dapat mendukung impor 30.000 orang!
pers_import_pinTooLong=ID Personel {0} terlalu panjang!
pers_import_pinExist=ID Personel {0} sudah ada!
pers_import_pinIsRepeat=ID Personel {0} diulang!
pers_import_pinError=kesalahan Personalia {0} kesalahan!
pers_import_pinSupportNumber=Nomor personel hanya mendukung angka! Nomor personel adalah: {0}
pers_import_pinNotSupportNonAlphabetic=ID Personel tidak mendukung huruf non-Inggris! Nomor personelnya adalah: {0}
pers_import_pinNotNull=ID personel tidak boleh semuanya nol!
pers_import_pinStartWithZero=ID personel tidak dapat memulai dengan nol!
pers_import_cardNoNotSupportLetter=Nomor kartu tidak mendukung surat!
pers_import_cardNoNotNull=Nomor kartu tidak boleh semuanya nol!
pers_import_cardNoStartWithZero=Nomor kartu tidak dapat dimulai dengan nol!
pers_import_cardTooLong=Nomor kartu {0} terlalu panjang!
pers_import_cardExist=Nomor kartu {0} sudah ada!
pers_import_personPwdOnlyNumber=Kata sandi pribadi hanya mendukung angka (tidak ada surat)!
pers_import_personPwdTooLong=Kata sandi pribadi {0} terlalu panjang!
pers_import_personDuressPwd=Kata sandi pribadi {0} diulang!
pers_import_emailTooLong=Email {0} terlalu panjang!
pers_import_nameTooLong=Nama {0} terlalu panjang!
pers_import_genderError=Kesalahan format gender!
pers_import_personDateError=Kesalahan format tanggal perekrutan!
pers_import_createTimeError=Buat kesalahan format waktu!
pers_import_phoneError=Kesalahan format nomor telepon!
pers_import_phoneTooLong=Panjang nomor telepon tidak boleh lebih dari 20 karakter!
pers_import_emailError=Kesalahan format email!
pers_import_birthdayError=Kesalahan format tanggal lahir!
pers_import_nameError=Nama Belakang dan Nama Depan tidak boleh mengandung simbol khusus! Nomor personelnya adalah: {0}
pers_import_firstnameError=Nama belakang atau nama depan tidak boleh mengandung ','!
pers_import_firstnameNotNull=Nama depan tidak boleh kosong!
pers_import_dataCheck=Akhir dari pemeriksaan data
pers_import_dataSaveFail=Gagal menyimpan data!
pers_import_allSucceed=Semua data berhasil diimpor!
pers_import_result=Berhasil: {0}, Gagal: {1}.
pers_import_result2=Hasil Impor
pers_import_result3=Berhasil: {0}, Diperbarui: {1}, Gagal: {2}.
pers_import_notSupportFormat=Format ini tidak didukung!
pers_import_selectCorrectFile=Silakan pilih file yang benar.
pers_import_fileFormat=Format File
pers_import_targetFile=File Tujuan
pers_import_startRow=Baris Mulai Header
pers_import_startRowNote=Defaultnya adalah baris kedua.
pers_import_delimiter=Pembatas
pers_import_importingDataFields=Bidang Database
pers_import_dataSourceFields=Mengimpor bidang data
pers_import_total=Total
pers_import_dataUpdate=Perbarui ID Personil yang ada dalam sistem:
pers_import_dataIsNull=Tidak ada data di file!
pers_import_deptNotExist=Departemen tidak ada!
pers_import_deptIsNotNull=Nama departemen tidak boleh kosong!
pers_import_pinNotEmpty=ID Personel tidak boleh kosong!
pers_import_nameNotEmpty=Nama personel tidak boleh kosong!
pers_import_siteCodeOnlyLetterNum=Kesalahan format kode situs.
pers_import_cardNoFormatErrors=Nomor kartu {0} salah format
pers_import_cardsNotSupport=Fitur [Beberapa Kartu per Orang] dinonaktifkan, tidak dapat memprogram lebih dari satu kartu per orang.
pers_import_personInfo=Impor Personel
pers_import_commentFormat=Format komentar tidak benar!
pers_import_noComment=Data dalam baris {0} dan kolom {1} tidak dikomentari.
pers_import_fieldRepeat=Data dalam baris {0} dan kolom {1}, nama bidang dalam komentar diulang: {2}
pers_import_primaryKey=Harus ada setidaknya satu bidang sebagai kunci utama.
pers_import_templateIsRepeat=Jumlah personel: Data bio-template untuk {0} telah diduplikasi!
pers_import_biologicalTemplate=Impor Template Biometrik
pers_import_uploadFileSuccess=Berhasil mengunggah, mulai mengurai data, harap tunggu ...
pers_import_resolutionComplete=Data telah diuraikan, mulai pembaruan basis data.
pers_import_mustField=File impor harus mengandung kolom {0}.
pers_import_bioTemplateSuccess=Impor templat biometrik, data ini perlu disinkronkan secara manual ke perangkat dari setiap modul bisnis.
pers_import_personPhoto=Impor Foto Personil
pers_import_opera_log=Log operasi
pers_import_error_log=Log kesalahan
pers_import_uploadFileSize=Harap unggah file dengan ukuran tidak lebih dari {0}!
pers_import_uploadFileSizeLimit=Untuk satu impor, harap unggah file tidak lebih dari 500 juta!
pers_import_type=Mode impor
pers_import_photoType=Foto
pers_import_archiveType=Paket terkompresi
pers_import_startUpload=Mulai Unggah
pers_import_addMore=Tambah Lainnya
pers_import_photoQuality=Kualitas Foto
pers_import_original=Asli
pers_import_adaptive=Adaptif
pers_import_adaptiveSize=(Ukuran 480 * 640)
pers_import_totalNumber=Total
pers_import_uploadTip=(Tolong jangan hapus foto saat mengunggah)
pers_import_addPhotoTip=Silakan tambahkan foto untuk diunggah!
pers_import_selectPhotoTip=Silakan pilih foto yang ingin Anda unggah!
pers_import_uploadResult=Unggah Hasil Foto
pers_import_pleaseSelectPhoto=Silakan Pilih Foto
pers_import_multipleSelectTip=Tekan Ctrl untuk membuat banyak pilihan
pers_import_replacePhotoTip=Foto yang dipilih sudah ada dalam daftar unggahan, apakah Anda ingin menggantinya?
pers_import_photoNamePinNotCorrespond=Ketidakcocokan nama foto dan personil
pers_import_photoFormatRequirement=Silakan beri nama foto dengan ID personel. Format yang benar adalah JPG/PNG. Pastikan nama foto tidak mengandung karakter khusus.
pers_import_filterTip=Beberapa foto yang Anda pilih tidak dapat dipratinjau, dan mungkin ada alasan berikut:
pers_import_photoContainSpecialCharacters=Nama foto memiliki karakter khusus.
pers_import_photoFormatError=Format foto salah.
pers_import_photoSelectNumber=Jangan memilih lebih dari 3000 gambar dalam sekali impor!
pers_import_photoSelectNumberLimit=Jangan pilih lebih dari {0} gambar
pers_import_fileMaxSize=Gambar terlalu besar, unggah file gambar lebih kecil dari 5M.
pers_import_notUploadPhotoNumber=Tidak lebih dari 500 gambar dapat diunggah!
pers_import_zipFileNotPhoto=Tidak ada foto orang tersebut di file zip, silakan pilih kembali dan impor!
pers_import_personPlateRepeat=Duplikat plat personel {0}!
pers_import_filePlateRepeat=File di dalam duplikat plat {0}!
pers_import_personPlateFormat=Format plat personel {0} salah!
pers_import_personPlateMax=Jumlah plat nomor personel melebihi maksimum 6!
pers_import_cropFaceFail=Kegagalan wajah gagal!
pers_import_pinLeaved=Jumlah personel: {0} telah pergi.
pers_import_exceedLicense=Impor tidak diizinkan untuk jumlah orang yang melebihi lisensi perangkat lunak.
pers_import_bioTemplateNotNull=Jumlah personil, jenis makhluk, bio id, indeks bio, konten, versi tidak boleh kosong!
pers_import_certTypeNotNull=Nomor personil adalah: {0} Jenis dokumen tidak boleh kosong
pers_import_certTypeNotExist=Nomor personal adalah: {0} Tipe dokumen tidak ada
pers_import_certNumNotNull=Nomor pribadi adalah: {0} Nomor sijil tidak dapat kosong
pers_import_certNumberTooLong=Baris {0}: Nomor ID {1} terlalu panjang!
pers_import_idNumberErrors=Baris {0}: Nomor kartu ID {1} salah format!
pers_import_emailErrors=Baris {0}: Alamat email format kesalahan {1}!
pers_import_emailIsExist=Alamat email {0} sudah ada!
pers_import_emailIsRepeat=Baris {0}: Alamat email internal file {1} telah diulang!
pers_import_fileMobilePhoneRepeat=Baris {0}: Nomor ponsel internal file {1} telah diulang!
pers_import_mobilePhoneErrors=Kesalahan format nomor ponsel!
pers_import_hireDateError=Format tanggal masukan tidak benar!
pers_import_selectPhotoType=Silakan pilih jenis foto diimpor!
pers_import_hireDateLaterCurrent=Tanggal sewa tidak dapat lebih lambat dari tanggal saat ini!
pers_import_buildingNotExist=Bangunan tersebut tidak ada!
pers_import_unitNotExist=Unit tidak ada!
pers_import_vdbInfoFail=Tidak ada informasi unit bernama {1} di gedung {0}!
pers_import_vdbBuildingFail=Informasi bangunan unit {0} tidak boleh kosong!
pers_import_vdbRoomNoFail=Nomor kamar harus berupa angka lebih besar dari 0!
#人员离职
pers_person_leave=Pemberhentian
pers_dimission_date=Tanggal Pemberhentian
pers_dimission_type=Jenis Pemberhentian
pers_dimission_reason=Alasan Pemberhentian
pers_dimission_volutary=Redundansi Sukarela
pers_dimission_dismiss=Diabaikan
pers_dimission_resignat=Pengunduran diri
pers_dimission_shiftJob=Transfer
pers_dimission_leave=Pertahankan Pekerjaan tanpa Gaji
pers_dimission_recovery=Pemulihan kembali
pers_dimission_sureToRecovery=Anda yakin ingin melakukan operasi pemulihan?
pers_dimission_backCard=Apakah kartu dikembalikan?
pers_dimission_isForbidAction=Segera nonaktifkan hak kontrol akses?
pers_dimission_writeInfomation=Masukkan informasi pemecatan
pers_dimission_pinRetain=Simpan id personil untuk karyawan yang diberhentikan?
pers_dimission_downloadTemplate=Unduh templat impor dimensi
pers_dimission_import=Dimensi impor
pers_dimission_importTemplate=Template impor dimensi
pers_dimission_date_noNull=Tanggal dimensi tidak boleh kosong
pers_dimission_leaveType_noExist=Jenis dimensi tidak ada
pers_dimission_dateFormat=Kolom wajib diisi, format waktu adalah yyyy-MM-dd, seperti: 2020-07-22
pers_dimission_leaveType=Bidang wajib diisi, seperti: Redundansi Sukarela, Diabaikan, Pengunduran diri, Transfer
pers_dimission_forbidden=Nonaktifkan
pers_dimission_leaveType_noNull=Tipe cuti tidak boleh kosong!
pers_dimission_person_noExist=Le licenciement n'existe pas!
pers_dimission_date_error=Tanggal pengusiran tidak diisi atau format salah
#临时人员
pers_tempPerson_audit=Tinjau
pers_tempPerson_view=Lihat
pers_tempPerson_waitReview=Menunggu pemeriksaan oleh administrator
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Nomor Kartu
pers_card_state=Status Kartu
pers_card_effect=Efektif
pers_card_disabled=Tidak valid
pers_card_past=Kadaluwarsa
pers_card_back=Kartu Dikembalikan
pers_card_change=Ganti Kartu
pers_card_note=Nomor kartu sudah ada.
pers_card_numTooBig=Panjang nomor kartu asli terlalu panjang.
pers_issueCard_entity=Kartu Masalah
pers_issueCard_operator=Operator
pers_issueCard_operate=Aksi
pers_issueCard_note=Penerbit kartu yang beroperasi untuk data personil terdaftar tetapi bukan staf akun kartu pendaftaran!
pers_issueCard_date=Tanggal Kartu Terbit
pers_issueCard_changeTime=Ubah Waktu
pers_issueCard_cardValidate=Tidak ada ruang yang diizinkan.
pers_issueCard_cardEmptyNote=Kartu tidak boleh kosong.
pers_issueCard_cardHasBeenIssued=Kartu ini telah Diterbitkan!
pers_issueCard_noCardPerson=Kartu tidak diterbitkan
pers_issueCard_waitPerson=Penerbit kartu ini
pers_issueCard_mc5000=Keluaran kartu MC5000
pers_batchIssCard_entity=Kartu Masalah Batch
pers_batchIssCard_startPersNo=Mulai Personel ID
pers_batchIssCard_endPersNo=Akhiri ID Personil
pers_batchIssCard_issCardNum=Jumlah Kartu yang Dikeluarkan
pers_batchIssCard_notIssCardNum=Jumlah Orang tanpa Kartu yang Dikeluarkan
pers_batchIssCard_generateList=Hasilkan Daftar
pers_batchIssCard_startRead=Mulai membaca
pers_batchIssCard_swipCard=Posisi kartu swiping
pers_batchIssCard_sendCard=Perangkat
pers_batchIssCard_dispenCardIss=Pembaca USB
pers_batchIssCard_usbEncoder=USB Encoder
pers_batchIssCard_note=ID Personel hanya mendukung nilai input, dan hanya menampilkan orang tanpa kartu yang dikeluarkan (maks. 300)!
pers_batchIssCard_startPinEmpty=Mulai ID Personel tidak boleh kosong!
pers_batchIssCard_endPinEmpty=Akhir ID Personel tidak boleh kosong!
pers_batchIssCard_startPinLargeThanEndPin=Mulai ID Personel tidak boleh lebih besar dari End Personil ID!
pers_batchIssCard_numberParagraphNoPerson=Orang tanpa kartu yang dikeluarkan tidak ada dalam nomor segmen ID awal-ke-akhir!
pers_batchIssCard_inputCardNum=Nomor Kartu Input
pers_batchIssCard_cardLetter=Kartu tidak mendukung surat!
pers_batchIssCard_cardNoTooLong=Panjang nomor kartu terlalu panjang!
pers_batchIssCard_issueWay=Metode Pendaftaran Kartu
pers_batchIssCard_noPersonList=Daftar personil belum dibuat.
pers_batchIssCard_startReadCard=Mulai Membaca Kartu
pers_batchIssCard_swipePosition=Geser Posisi
pers_batchIssCard_chooseSwipePosition=Silakan pilih posisi geser.
pers_batchIssCard_readCardTip=Perangkat hanya membaca kartu yang tidak terdaftar ketika metode penerbitan adalah pembaca yang dipilih.
pers_batchIssCard_notIssCardNo=Jumlah kartu yang tidak dikeluarkan
pers_batchIssCard_totalNumOfCards=Jumlah total kartu
pers_batchIssCard_acms=Pengiriman Kartu ACMS
pers_lossCard_entity=Kartu Hilang yang Dilaporkan
pers_lossCard_lost=Kartu ini telah dilaporkan, tidak dapat mengulangi operasinya!
pers_losscard_note2=Setelah Anda menulis kartu manajemen, Anda harus menggesek kartu ini ke pembaca elevator untuk memastikan kartu kehilangan efek pada perangkat.
pers_revertCard_entity=Aktifkan Kembali Kartu yang Hilang
pers_revertCard_setReport=Silakan laporkan kehilangan kartu lebih dulu!
pers_revertcard_note2=Setelah Anda menulis kartu manajemen, Anda harus menggesek kartu ini pada pembaca elevator untuk memastikan Anda dapat menggunakan kartu lagi.
pers_issueCard_success=Penerbitan kartu berhasil!
pers_issueCard_error=Kegagalan mengeluarkan kartu!
pers_cardData_error=Baca pengecualian format data kartu!
pers_analysis_error=Pengecualian parsing data kartu!
pers_cardOperation_error=Pengecualian operasi kartu!
pers_cardPacket_error=Pengecualian paket perintah operasi kartu!
pers_card_write=Tulis Kartu
pers_card_init=Inisialisasi kartu
pers_card_loss=Kartu Hilang
pers_card_revert=Kembalikan kartu
pers_card_writeMgr=Tulis kartu manajemen
pers_initCard_tip=Setelah inisialisasi, kartu akan menjadi kartu kosong!
pers_initCard_prepare=Siap menginisialisasi kartu ...
pers_initCard_process=Menginisialisasi kartu ...
pers_initCard_success=Berhasil menginisialisasi kartu
pers_mgrCard_prepare=Bersiaplah untuk menulis data kartu manajemen ...
pers_mgrCard_process=Sedang menulis data kartu manajemen ...
pers_mgrCard_success=Sukses menulis kartu manajemen
pers_userCard_prepare=Bersiaplah untuk menulis kartu pengguna ...
pers_userCard_process=Menulis data kartu pengguna ...
pers_userCard_success=Sukses menulis kartu pengguna
pers_userCard_tip=Silakan setel waktu mulai dan waktu selesai di halaman edit orang lalu tulis operasi kartu.
pers_userCard_tip2=Data izin kosong, tidak bisa menulis kartu.
pers_userCard_tip3=Grup terkait perangkat otoritas lebih dari dua unit, kehilangan data.
pers_writeCard_tip=Pastikan Anda telah menghubungkan encoder dan menginstal driver, dan letakkan kartu pada encoder.
pers_writeMgrCard_tip=Jumlah kartu yang hilang dan kartu yang dikembalikan tidak boleh lebih dari 18
pers_writeMgrCard_tip2=Jumlah kartu yang hilang dan mengembalikan kartu:
pers_card_writeToMgr=Ditulis ke kartu manajemen
pers_card_hex=Tampilan Format Kartu
pers_card_decimal=Desimal
pers_card_Hexadecimal=Heksadesimal
pers_card_IssuedCommandFail=Perintah yang Diberikan Gagal:
pers_card_multiCard=Lebih Banyak Kartu
pers_card_deputyCard=Kartu Sekunder
pers_card_deputyCardValid=Silakan masukkan kartu utama terlebih dahulu!
pers_card_writePinFormat=Sistem hanya dapat mengaitkan ID Personil tanpa surat ke kartu!
pers_card_notMoreThanSixteen=Jumlah kartu sekunder tidak boleh lebih dari 16.
pers_card_notDelAll=Tidak dapat menghapus semua kartu sekunder.
pers_card_maxCard=Nomor kartu tidak boleh melebihi {0}.
pers_card_posUseCardNo=Kartu utama seseorang sedang digunakan oleh modul konsumen. Silakan buka modul konsumen untuk melakukan operasi penarikan kartu!
pers_card_delFirst=Silakan hapus nomor kartu yang diberikan sebelum mengeluarkan kartu!
pers_card_disablePersonWarn=Nomor kartu yang dipilih mengandung personel yang dilumpuhkan dan tidak dapat beroperasi!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Kode Situs
pers_wiegandFmt_wiegandMode=Mode
pers_wiegandFmt_wiegandModeOne=Mode Satu
pers_wiegandFmt_wiegandModeTwo=Mode Dua
pers_wiegandFmt_isDefaultFmt=Otomatis
pers_wgFmt_entity=Format Wiegand
pers_wgFmt_in=Format Input Wiegand
pers_wgFmt_out=Format Output Wiegand
pers_wgFmt_inType=Jenis Input Wiegand
pers_wgFmt_outType=Jenis Output Wiegand
pers_wgFmt_wg=Format Wiegand
pers_wgFmt_totalBit=Total Bit
pers_wgFmt_oddPch=Pemeriksaan Paritas Ganjil (o)
pers_wgFmt_evenPck=Pemeriksaan Paritas Merata (e)
pers_wgFmt_CID=CID (c)
pers_wgFmt_facilityCode=Kode Fasilitas (f)
pers_wgFmt_siteCode=Kode Situs
pers_wgFmt_manufactoryCode=Kode Pabrikan (m)
pers_wgFmt_firstParity=Pemeriksaan Paritas Pertama (p)
pers_wgFmt_secondParity=Pemeriksaan Paritas Kedua (p)
pers_wgFmt_cardFmt=Format Pemeriksaan Kartu
pers_wgFmt_parityFmt=Format Pemeriksaan Paritas
pers_wgFmt_startBit=Mulai Bit
pers_wgFmt_test=Tes Format Kartu
pers_wgFmt_error=Kesalahan Format Kartu!
pers_wgFmt_verify1=Total bit tidak boleh lebih dari 80!
pers_wgFmt_verify2=Periksa panjang format kartu harus sama dengan jumlah total bit!
pers_wgFmt_verify3=Panjang format paritas harus sama dengan jumlah total digit!
pers_wgFmt_verify4=Data yang diinisialisasi tidak dapat dihapus!
pers_wgFmt_verify5=Format kartu sedang digunakan, tidak bisa dihapus!
pers_wgFmt_verify6=Bit paritas pertama tidak boleh lebih besar dari jumlah total!
pers_wgFmt_verify7=Bit paritas kedua tidak boleh lebih dari jumlah total!
pers_wgFmt_verify8=Mulai dan format panjang maksimum tidak benar!
pers_wgFmt_verify9=Panjang format pemeriksaan kartu tidak boleh lebih dari jumlah total!
pers_wgFmt_verify10=Fungsi digit periksa kartu tidak dapat dilewati!
pers_wgFmt_verify11=Kode situs melebihi rentang yang ditentukan!
pers_wgFmt_verify=Periksa
pers_wgFmt_unverify=Non Cek
pers_wgFmt_atLeastDefaultFmt=Harap simpan setidaknya satu format kartu yang secara otomatis cocok!
pers_wgFmt_defaultFmtError1=Ada beberapa format kartu lain dengan bit nomor kartu yang sama, tidak dapat diatur untuk secara otomatis mencocokkan format kartu!
pers_wgFmt_defaultFmtError2=Kehadiran format kartu yang sama untuk pencocokan otomatis, operasi gagal!
pers_wgFmt_cardFormats=Format Kartu
pers_wgFmt_cardFormatTesting=Pengujian Format Kartu
pers_wgFmt_checkIsUsed=Format kartu digunakan di {0} dan tidak dapat dihapus!
pers_wgFmt_supportDigitsNumber=Silakan masukkan jumlah digit yang didukung oleh perangkat
#选人控件
pers_widget_selectPerson=Pilih personil
pers_widget_searchType1=Permintaan
pers_widget_searchType2=Departemen
pers_widget_deptHint=Catatan: Mengimpor semua personil departemen yang dipilih
pers_widget_noPerson=Tidak ada personil yang dipilih.
pers_widget_noDept=Silakan pilih departemen.
pers_widget_noDeptPerson=Tidak ada orang di bawah departemen yang dipilih, silakan pilih kembali!
#人员属性
pers_person_carPlate=Plat Lisensi
pers_person_socialSecurity=Nomor Jaminan Sosial
pers_person_msg4=Panjang maksimum tidak boleh lebih dari 20!
pers_person_msg5=Nama tidak boleh mengandung lebih dari 50 karakter!
pers_person_type=Jenis Orang
pers_person_reseCode=Kode Pemesanan
pers_person_IsSendMail=Pemberitahuan Email
pers_person_inactive=Tidak aktif
pers_person_active=Aktif
pers_person_employee=Karyawan
pers_person_isSendMailMsg=Untuk menggunakan fitur [Pemberitahuan Acara], Anda harus memasukkan email terlebih dahulu.
pers_person_createTime=Buat Waktu
pers_person_pinFirstValid=Karakter pertama dari nomor personel tidak boleh 8 atau 9.
pers_person_attrValueValid=Nilai bidang tidak dapat diulang.
pers_person_attrValueDelimiterValid=Pemisah harus di tengah.
pers_person_isSendSMS=Pemberitahuan SMS
pers_person_building=Nama Gedung
pers_person_unitName=Nama Satuan
pers_person_roomNo=Nomor Kamar
#动态属性
pers_attr_emp_type=Jenis Karyawan
pers_attr_street=Jalan
pers_attr_nation=Bangsa
pers_attr_office_address=Alamat Kantor
pers_attr_postcode=Kode pos
pers_attr_office_phone=Telepon Kantor
pers_attr_home_phone=Telepon Rumah
pers_attr_job_title=Judul Pekerjaan
pers_attr_birthplace=Tempat Kelahiran
pers_attr_polit_status=Status Politik
pers_attr_country=Negara
pers_attr_home_address=Alamat Rumah
pers_attr_hire_type=Jenis Perekrutan
pers_attr_inContract=Pekerja Kontrak
pers_attr_outContract=Pekerja Bukan Kontrak
#属性自定义
pers_attribute_attrName=Nama Tampilan
pers_attribute_attrValue=Nilai Atribut
pers_attribute_controlType=Jenis Input
pers_attribute_positionX=Baris
pers_attribute_positionY=Kolom
pers_attribute_showTable=Tampilkan di Daftar Orang
pers_attrDefini_deletemsg=Properti ini telah digunakan. Anda yakin ingin menghapusnya?
pers_attrDefini_reserved=Nama bidang yang dicadangkan sistem.
pers_attrDefini_msg1=Panjang maksimal tidak lebih dari 30!
pers_attrDefini_msg2=Baris kolom sudah ada, silakan ubah ke lokasi lain!
pers_attrDefini_attrValue_split=Menggunakan a '; pembatas.
pers_attrDefini_attrName=Nama Atribut
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=Id Atribut
pers_attrDefini_select=Daftar Tarik
pers_attrDefini_check=Pilihan Ganda
pers_attrDefini_radio=Pilihan Tunggal
pers_attrDefini_text=Teks
pers_attrDefini_maxCol=Tidak lebih dari 2 kolom.
pers_attrDefini_maxLimit=Atribut khusus telah mencapai jumlah maksimum!
pers_attrDefini_modControlType=Mengubah jenis input akan menghapus data bidang saat ini untuk semua personel dalam sistem.apakah akan melanjutkan?
#leavePerson
pers_leavePerson_reinstated=Penginstalan kembali
#opExample
pers_example_newRecode=Akuisisi catatan baru
pers_example_allRecode=Akses ke semua catatan
pers_custField_StatisticalType=Jenis Statistik
#人员参数修改
pers_param_isAudit=Aktifkan audit otomatis
pers_param_donotChangePin=ID Personel yang ada berisi huruf, tidak dapat menonaktifkan fitur [Surat Dukungan].
pers_param_hexChangeWarn=Sistem saat ini sudah memiliki nomor kartu, tidak dapat mengubah mode tampilan format kartu.
pers_param_cardsChangeWarn=Ada beberapa orang dengan lebih dari satu kartu dalam sistem, tidak dapat menonaktifkan fitur [Beberapa Kartu per Orang].
pers_param_maxPinLength=Panjang ID Personel baru tidak boleh kurang dari panjang ID Personel yang ada dalam sistem.
pers_param_pinBeyondDevLength=Panjang maksimum ID Personel yang didukung oleh perangkat dalam sistem adalah {0}, harap masukkan bilangan bulat kurang dari {1}.
pers_param_cardBeyondDevLength=Panjang maksimum nomor kartu yang didukung oleh perangkat dalam sistem adalah {0}, harap masukkan bilangan bulat kurang dari {1}.
pers_param_checkIsExistNoAudit=Ada pendaftar yang tidak direview dalam sistem saat ini dan tidak dapat dimodifikasi untuk ditinjau otomatis!
pers_param_noSupportPinLetter=Ada perangkat dalam sistem yang tidak mendukung surat yang berisi ID Personel, tidak dapat mengaktifkan fitur [Surat Dukungan].
pers_param_changePinLettersTip=Jumlah personel mendukung peningkatan otomatis, tidak dapat mengubah mode nomor staf
pers_param_changePinIncrementTip=Dukungan nomor orang termasuk surat, tidak dapat diubah mode nomor staf
pers_param_qrCode=Kode perusahaan QR
pers_param_employeeRegistrar=Aktifkan pendaftaran karyawan cloud
pers_param_downloadQRCodePic=Unduh gambar kode QR
pers_param_qrCodeUrl=Url kode QR
pers_param_qrCodeUrlCreate=Registrasi mandiri
pers_param_qrCodeUrlHref=Alamat server: port
pers_param_pinSetWarn=Sudah ada personel dalam sistem saat ini, dan mode nomor personel tidak dapat diubah.
pers_param_selfRegistration=Aktifkan Registrasi Mandiri
pers_param_infoProtection=Perlindungan informasi sensitif pribadi
pers_param_infoProtectionWarnMsg=Setelah mengaktifkan opsi perlindungan keamanan informasi sensitif pribadi, data pribadi sensitif yang terlibat dalam modul ini akan dihilangkan kepekaannya atau dikaburkan, termasuk namun tidak terbatas pada nama, nomor kartu, nomor ID, foto, dll.
pers_param_templateServer=Server Ekstraksi Templat Muka
pers_param_enableFacialTemplate=Aktifkan Ekstraksi Templat Muka
pers_param_templateServerAddr=Alamat server ekstraksi templat wajah
pers_param_templateServerWarnInfo=Ketika ekstraksi templat wajah diaktifkan, ketika server ekstraksi templat wajah online dan verifikasi pengguna telah lulus, staf akan standar untuk ekstraksi templat wajah ketika membandingkan foto; Ketika server ekstraksi templat wajah dalam mode offline, jangan ekstrak templat wajah!
pers_param_templateServerWarnInfo1=Ketika memungkinkan ekstraksi templat wajah, perangkat yang mendukung ekstraksi templat wajah perlu terhubung!
pers_param_templateServerOffline=Server ekstraksi templat wajah diluar talian dan tidak dapat ekstraksi templat wajah! Apakah Anda ingin melanjutkan?
pers_param_faceServer=Layanan perbandingan di belakang wajah
pers_param_enableFaceVerify=Aktifkan perbandingan latar belakang wajah
pers_param_faceServerAddr=Alamat Layanan Perbandingan Wajah
pers_param_faceServerSecret=Kunci layanan perbandingan backend wajah
#国籍
pers_person_nationality=Negara/Wilayah
pers_nationality_angola=Angolan
pers_nationality_afghanistan=Afghan
pers_nationality_albania=Albanian
pers_nationality_algeria=Algerian
pers_nationality_america=American
pers_nationality_andorra=Andorran
pers_nationality_anguilla=Anguillan
pers_nationality_antAndBar=Antigua and Barbuda
pers_nationality_argentina=Argentinan
pers_nationality_armenia=Armenians
pers_nationality_ascension=Ascension
pers_nationality_australia=Australian
pers_nationality_austria=Austrian
pers_nationality_azerbaijan=Azerbaijani
pers_nationality_bahamas=Bahamas
pers_nationality_bahrain=Bahrain
pers_nationality_bangladesh=Bangladesh
pers_nationality_barbados=Barbadian
pers_nationality_belarus=Belarus
pers_nationality_belgium=Belgium
pers_nationality_belize=Belizean
pers_nationality_benin=Beninese
pers_nationality_bermudaIs=Bermuda Is.
pers_nationality_bolivia=Bolivia
pers_nationality_botswana=Botswanan
pers_nationality_brazil=Brazil
pers_nationality_brunei=Brunei
pers_nationality_bulgaria=Bulgarian
pers_nationality_burkinaFaso=Burkina Faso
pers_nationality_burma=Burma
pers_nationality_burundi=Burundian
pers_nationality_cameroon=Cameroonian
pers_nationality_canada=Canadian
pers_nationality_caymanIs=Cayman Islands
pers_nationality_cenAfrRepub=Central African Republic
pers_nationality_chad=Chadian
pers_nationality_chile=Chile
pers_nationality_china=Chinese
pers_nationality_colombia=Colombian
pers_nationality_congo=Republic of the Congo
pers_nationality_cookIs=Cook Is.
pers_nationality_costaRica=Costarican
pers_nationality_cuba=Cuban
pers_nationality_cyprus=Cyprus
pers_nationality_czechRep=Czech Republic
pers_nationality_denmark=Denmark
pers_nationality_djibouti=Djiboutis
pers_nationality_dominicaRep=Dominica Rep.
pers_nationality_ecuador=Ecuador
pers_nationality_egypt=Egyptian
pers_nationality_eISalvador=EI Salvador
pers_nationality_england=British
pers_nationality_estonia=Estonian
pers_nationality_ethiopia=Ethiopian
pers_nationality_fiji=Fijian
pers_nationality_finland=Finland
pers_nationality_france=France
pers_nationality_freGui=French Guiana
pers_nationality_gabon=Gabonese
pers_nationality_gambia=Gambia
pers_nationality_georgia=Georgian
pers_nationality_germany=German
pers_nationality_ghana=Ghanaian
pers_nationality_gibraltarm=Gibraltar
pers_nationality_greece=Greek
pers_nationality_grenada=Grenada
pers_nationality_guam=Guam
pers_nationality_guatemala=Guatemalan
pers_nationality_guinea=Guinean
pers_nationality_guyana=Guyana
pers_nationality_haiti=Haiti
pers_nationality_honduras=Honduras
pers_nationality_hungary=Hungarian
pers_nationality_iceland=Icelander
pers_nationality_india=Indian
pers_nationality_indonesia=Indonesians
pers_nationality_iran=Iranian
pers_nationality_iraq=Iraqi
pers_nationality_ireland=Irish
pers_nationality_israel=Israeli
pers_nationality_italy=Italian
pers_nationality_ivoryCoast=Lvoirian
pers_nationality_jamaica=Jamaican
pers_nationality_japan=Japan
pers_nationality_jordan=Jordan
pers_nationality_kenya=Kenyan
pers_nationality_korea=Korean
pers_nationality_kuwait=Kuwaiti
pers_nationality_kyrgyzstan=Kyrgyzstan
pers_nationality_laos=Laotian
pers_nationality_latvia=Latvian
pers_nationality_lebanon=Lebanese
pers_nationality_lesotho=Mesotho
pers_nationality_liberia=Liberian
pers_nationality_libya=Libyan
pers_nationality_liechtenstein=Liechtensteiner
pers_nationality_lithuania=Lithuanian
pers_nationality_luxembourg=Luxemburgues
pers_nationality_madagascar=Malagasy
pers_nationality_malawi=Malawian
pers_nationality_malaysia=Malaysia
pers_nationality_maldives=Maldives
pers_nationality_mali=Mali
pers_nationality_malta=Maltese
pers_nationality_marianaIs=Mariana Is.
pers_nationality_martinique=Martinique
pers_nationality_mauritius=Mauritian
pers_nationality_mexico=Mexican
pers_nationality_moldova=Moldova
pers_nationality_monaco=Monacan
pers_nationality_montseIs=Montserrat Is.
pers_nationality_morocco=Moroccan
pers_nationality_mozambique=Mozambican
pers_nationality_namibia=Namibian
pers_nationality_nauru=Nauruan
pers_nationality_nepal=Nepal
pers_nationality_netAnti=Netheriands Antilles
pers_nationality_netherlands=Netherlands
pers_nationality_newZealand=New Zealander
pers_nationality_nicaragua=Nicaragua
pers_nationality_niger=Nigerien
pers_nationality_nigeria=Nigeria
pers_nationality_norKorea=North Korea
pers_nationality_norway=Norwegian
pers_nationality_oman=Omani
pers_nationality_pakistan=Pakistani
pers_nationality_panama=Panama
pers_nationality_papNewCui=Papua New Guinean
pers_nationality_paraguay=Paraguay
pers_nationality_peru=Peru
pers_nationality_philippines=Philippines
pers_nationality_poland=Poland
pers_nationality_frenPolyne=French Polynesia
pers_nationality_portugal=Portuguese
pers_nationality_puerRico=Puerto Rico
pers_nationality_qatar=Qatari
pers_nationality_reunion=Reunion
pers_nationality_romania=Romanian
pers_nationality_russia=Russian
pers_nationality_saiLueia=Saint Lueia
pers_nationality_saintVinc=Saint Vincent
pers_nationality_samoa_eastern=Samoa Eastern
pers_nationality_samoa_western=Samoa Western
pers_nationality_sanMarino=San Marinese
pers_nationality_saoAndPrinc=Sao Tome and Principne
pers_nationality_sauArabia=Saudi Arabia
pers_nationality_senegal=Senegalese
pers_nationality_seychelles=Seychellese
pers_nationality_sieLeone=Sierra Leone
pers_nationality_singapore=Singapore
pers_nationality_slovakia=Slovakian
pers_nationality_slovenia=Slovenian
pers_nationality_solomonIs=Solomon islander
pers_nationality_somali=Somali
pers_nationality_souAfrica=South African
pers_nationality_spain=Spanish
pers_nationality_sriLanka=Sri Lankan
pers_nationality_sudan=Sudan
pers_nationality_suriname=Suriname
pers_nationality_swaziland=Swazi
pers_nationality_sweden=Swedish
pers_nationality_switzerland=Swiss
pers_nationality_syria=Syrian
pers_nationality_tajikstan=Tajik
pers_nationality_tanzania=Tanzanian
pers_nationality_thailand=Thai
pers_nationality_togo=Togolese
pers_nationality_tonga=Tongan
pers_nationality_triAndToba=Trinidad and Tobago
pers_nationality_tunisia=Tunisian
pers_nationality_turkey=Turk
pers_nationality_turkmenistan=Turkmenistan
pers_nationality_uganda=Ugandan
pers_nationality_ukraine=Ukrainian
pers_nationality_uniArabEmira=United Arab Emirates
pers_nationality_uruguay=Uruguay
pers_nationality_uzbekistan=Uzbekistan
pers_nationality_venezuela=Venezuela
pers_nationality_vietnam=Vietnamese
pers_nationality_yemen=Yemeni
pers_nationality_serbia=Serbia
pers_nationality_zimbabwe=Zimbabwean
pers_nationality_zambia=Zambian
pers_nationality_aruba=Aruba
pers_nationality_bhutan=Bhutan
pers_nationality_bosnia_herzegovina=Bosnia and Herzegovina
pers_nationality_cambodia=Cambodia
pers_nationality_congoD=Democratic Republic of the Congo
pers_nationality_comoros=La Unión de las Comoras
pers_nationality_capeVerde=Republic of Cape Verde
pers_nationality_croatia=Croatia
pers_nationality_dominica=Dominica
pers_nationality_eritrea=Eritrea
pers_nationality_micronesia=Micronesia
pers_nationalit_guineaBissau=Guinea-Bissau
pers_nationalit_equatorialGuinea=Equatorial Guinea
pers_nationalit_hongkong=Hong Kong, Cina
pers_nationalit_virginIslands=The United States Virgin Islands
pers_nationalit_britishVirginIslands=The British Virgin Islands
pers_nationalit_kiribati=Kiribati
pers_nationalit_mongolia=Mongolia
pers_nationalit_marshall=Marshall Island
pers_nationalit_macedonia=Macedonia
pers_nationalit_montenegro=Montenegro
pers_nationalit_mauritania=Mauritania
pers_nationalit_palestine=Palestine
pers_nationalit_palau=Palau
pers_nationalit_rwanda=Rwanda
pers_nationalit_saintKittsNevis=Saint Kitts and Nevis
pers_nationalit_timorLeste=Timor Leste
pers_nationalit_taiwan=Taiwan, Cina
pers_nationalit_tuvalu=Tuvalu
pers_nationalit_vanuatu=Vanuatu
#制卡
pers_person_cardprint=Kartu Cetak
pers_cardTemplate_tempSelect=Template Kartu
pers_cardTemplate_printerSelect=Printer
pers_cardTemplate_front=Depan
pers_cardTemplate_opposite=Kembali
pers_cardTemplate_entryDate=Tanggal Perekrutan
pers_cardTemplate_photo=Foto
pers_cardTemplate_uploadFail=Gagal mengunggah gambar!
pers_cardTemplate_jpgFormat=Hanya mendukung mengunggah gambar dalam format JPG!
pers_cardTemplate_printStatus=Status Cetak
pers_cardTemplate_waiting=Menunggu
pers_cardTemplate_printing=Mencetak
pers_cardTemplate_printOption=Opsi Cetak
pers_cardTemplate_duplexPrint=Cetak Duplex
pers_cardTemplate_frontOnly=Cetak Hadapan Hanya
#app
pers_app_delPers=Tidak ada orang di server yang ingin Anda hapus.
pers_app_deptIsNull=Nomor departemen atau nama yang sesuai dengan departemen tidak dapat ditemukan
pers_app_personNull=Orang tidak ada
pers_app_pinExist=Nomor personel sudah ada
pers_app_dateError=Format tanggal salah, silakan merujuk ke format yang benar: 2016-08-08.
#api
pers_api_selectPhotoInvalid=Foto tidak valid, unggah kembali
pers_api_dateError=Format tanggal salah
pers_api_personNotExist=Orang tidak ada
pers_api_cardsPersSupport=Sistem tidak membuka satu kartu multi-orang, menetapkan kartu sekunder tidak valid.
pers_api_department_codeOrNameNotNull=Nomor departemen atau nama departemen tidak boleh kosong
pers_api_deptSortNoIsNull=Urutan departemen tidak boleh kosong!
pers_api_deptSortNoError=Nilai penyortiran departemen harus antara 1-999999!
pers_api_dataLimit=Nomor operasi saat ini adalah {0}, melebihi batas {1}. Silakan beroperasi dalam batch!
pers_api_cardTypeError=Galat tipe kartu
#人员生物模板API
pers_api_fingerprintExisted=Sidik jari orang tersebut sudah ada
pers_api_validtypeIncorrect=Nilai atribut tipe valid ini salah
pers_api_dataNotExist=Templat Tidak ada
pers_api_templateNoRang=Masukkan nilai templat yang benarTidak ada dalam rentang 0-9
pers_api_templateIsNull=templat tidak dapat kosong!
pers_api_versionIsNumber=Versi hanya dapat memasukkan nomor!
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=Nomor personil hanya nomor
#biotime
pers_h5_personAvatarNotNull=Avatar itu kosong
pers_h5_personMobileRepeat=Nomor ponsel sudah ada
pers_h5_personEmailRepeat=Kotak surat sudah ada
pers_h5_pwdIsRepetition=Pengulangan kata sandi baru dan lama
pers_h5_personIdNull=ID staf kosong
pers_h5_pinOrEmailIsNull=Silakan isi nomor dan alamat email
pers_emailTitle_resetPassword=Ubah kata sandi
pers_emailContent_resetPassword=Tautan ini berlaku selama 24 jam, salin tautan ke browser untuk mengubah kata sandi:
pers_h5_tokenIsNull=Voucher kosong
pers_h5_tokenError=Kesalahan voucher
pers_h5_oldPwdIsError=Kata sandi lama tidak diisi dengan benar
pers_api_resetPasswordSuccess=Ubah kata sandi dengan sukses
pers_api_resetPasswordFail=Perubahan kata sandi gagal
pers_api_forgetPassword=Lupa kata sandi Anda
pers_api_confirmSubmit=Konfirmasikan pengiriman
pers_api_confirmPwdCaution=Klik [OK] untuk mengonfirmasi kata sandi baru.
pers_api_pwdRule=Kata sandi harus mengandung setidaknya satu simbol atau angka dan panjangnya minimal 8-12 karakter
pers_h5_personPinFormatNumber=Nomor personil hanya dapat terdiri dari angka atau huruf
pers_h5_persEmailNoExist=Kesalahan pengisian kotak surat
pers_h5_pageNull=Kesalahan parameter paging
pers_h5_personPinNotStartWithZero=Nomor personil tidak dapat dimulai dengan 0
pers_h5_personPinTooLong=Jumlah personil terlalu panjang
pers_h5_personPinInValid=Nomor ini sudah digunakan
pers_h5_imgSizeError=Unggah gambar yang tidak lebih besar dari 10 jt!
pers_h5_confirmAndContinue=Konfirmasi dan Lanjutkan
#人脸抠图不及格错误
pers_face_poorResolution=Resolusi gambar di bawah 80000 piksel
pers_face_noFace=Tidak ada wajah yang terdeteksi
pers_face_manyFace=Beberapa wajah terdeteksi
pers_face_smallFace=Rasio wajah terlalu kecil
pers_face_notColor=Gambar ini adalah gambar yang tidak berwarna
pers_face_seriousBlur=Gambar buram
pers_face_intensivelyLight=Gambar terpapar berat
pers_face_badIllumination=Gambar terlalu gelap
pers_face_highNoise=Gambar dengan derau tinggi
pers_face_highStretch=Wajah overtretched
pers_face_covered=Wajah tertutup
pers_face_smileOpenMouth=Senyum berlebihan
pers_face_largeAngle=Sudut defleksi wajah terlalu besar
pers_face_criticalIllumination=Kecerahan gambar kritis
pers_face_criticalLargeAngle=Sudut defleksi wajah kritis
pers_face_validFailMsg=Deteksi wajah gagal karena:
pers_face_failType=Jenis kegagalan guntingan wajah
pers_face_photoFormatError=Format foto salah, harap unggah file format JPG / PNG.
pers_face_notUpdateMsg=Gagal membuat gambar wajah, jangan perbarui gambar wajah.
#健康申报
pers_health_enable=Aktifkan pernyataan informasi kesehatan
pers_health_attrExposure=Pernahkah Anda berhubungan dengan kasus yang mencurigakan atau dikonfirmasi
pers_health_attrSymptom=Apa saja gejalanya dalam 14 hari terakhir
pers_health_attrVisitCity=Kota dikunjungi dalam 14 hari terakhir
pers_health_attrRemarks=Catatan status kesehatan
pers_health_symptomCough=Batuk
pers_health_symptomFever=Demam
pers_health_symptomPolypena=Masalah Pernafasan
pers_health_declaration=Deklarasi kesehatan
pers_health_aggrement=Saya telah setuju bahwa orang yang gagal mengisi informasi yang diperlukan akan dilarang mengakses, dan pengunjung yang gagal melaporkan informasi dengan jujur tidak dapat terus mengakses dan perlu memikul tanggung jawab hukum yang sesuai.
pers_health_visitCity_notEmpty=Kota yang dikunjungi tidak boleh kosong!
pers_health_notAgree=Perlu menyetujui perjanjian sebelum melanjutkan
#人员名单库
pers_personnal_list_manager=Pengelola daftar
pers_personnal_list=Pustaka daftar
pers_personnal_list_scheme=Mode domain
pers_personnal_list_name=Nama daftar personnal
pers_personnal_list_group_str_id=Daftar ID grup
pers_personnal_list_personCount=Jumlah orang
pers_personnal_list_tag=Buatan pengguna
pers_personnal_list_type=Jenis daftar orang
pers_personnallist_addPerson_repo=Tambahkan orang ke repo
pers_personnallist_sendPersonnallist=Pustaka daftar terdistribusi
pers_personnallist_sendPerson=SendPerson
pers_personnallist_notDel_existPerson=Pustaka daftar berisi orang dan tidak bisa dihapus
pers_personnallist_peopleInRoster=Masih ada orang di pustaka daftar
pers_personnallist_associationNotExist=Hubungan antara perangkat utama dan pustaka daftar tidak ada
pers_personnal_list_person=Orang daftar orang
pers_personnal_list_dev=Buat daftar izin perpustakaan
pers_personnal_list_addDev=Tambah perangkat
pers_personnal_list_name_isExist=Nama sudah ada
pers_personnal_bannedList=Pustaka daftar terlarang
pers_personnal_allowList=Izinkan Perpustakaan Daftar
pers_personnal_redList=Perpustakaan Daftar Merah
pers_personnal_attGroup=Grup Kehadiran
pers_personnal_passList=Daftar sandi
pers_personnal_banList=Daftar terlarang
pers_personnal_visPassList=Senarai Pass Pengunjun
pers_personnal_visBanList=Daftar Pengunjung Dilarang
pers_personnal_databaseHasBeenDistributed=Perpustakaan daftar yang dipilih telah didistribusikan dan tidak dapat dihapus
pers_personnel_sendError_dueTo=Kegagalan pengiriman Alasan kegagalan:
pers_personnel_sendError_reson={0} ada dalam daftar terlarang, harap hapus dan tambahkan
#比对照片-样片示例
pers_examplePic_Tip=Harus memenuhi persyaratan berikut:
pers_examplePic_Tip1=1. warna latar belakang putih bersih, dan personel mengenakan pakaian gelap;
pers_examplePic_Tip2=2. Foto elektronik dalam format file JPG, PNG, JPEG, rentang piksel yang disarankan: 480*640 < piksel < 1080*1920;
pers_examplePic_Tip3=3. Potret dalam foto elektronik harus membuka mata dan melihat lurus ke depan dan memastikan bahwa pupil terlihat jelas;
pers_examplePic_Tip4=4. Potret pada foto elektronik harus memiliki ekspresi netral, dan Anda dapat tersenyum, tetapi Anda tidak boleh menunjukkan gigi Anda;
pers_examplePic_Tip5=5. Potret pada foto elektronik harus jelas, dengan warna alami, lapisan yang kaya dan tidak ada distorsi yang jelas. Tidak ada bayangan, sorotan, atau pantulan pada wajah potret atau latar belakang; kontras dan kecerahan sesuai.
pers_examplePic_description=Contoh yang benar
pers_examplePic_error=Contoh kesalahan:
pers_examplePic_error1=Ekspresi berlebihan (senyum berlebihan)
pers_examplePic_error2=Cahaya terlalu gelap
pers_examplePic_error3=Wajah terlalu kecil (resolusi terlalu kecil)
pers_applogin_enabled=Aktifkan daftar masuk aplikasi
pers_applogin_disable=Lumpuhkan daftar masuk aplikasi
pers_applogin_status=Status aktif daftar masuk aplikasi