#
pers_module=사용자
pers_common_addPerson=사용자 추가
pers_common_delPerson=사용자 삭제
pers_common_personCount=사용자 수
pers_common_browsePerson=사용자 검색
pers_person_manager=사용자 관리
pers_person=사용자
pers_department=부서
pers_leave=퇴사 인원 관리
pers_tempPerson=사용자 승인 검토
pers_attribute=사용자 추가 정보
pers_card_manager=카드 관리
pers_card=카드
pers_card_issue=카드 관리 이력
pers_wiegandFmt=Wiegand 형식
pers_position=직급
pers_person_female=여자
pers_person_male=남자
pers_person_pin=사용자 ID
pers_person_departmentChange=부서 조정
pers_personDepartment_changeLevel=부서 전환 권한
pers_person_gender=성별
pers_person_detailInfo=세부 사항
pers_person_accSet=출입통제 설정
pers_person_accSetting=출입통제 설정
pers_person_attSet=근태 설정
pers_person_eleSet=엘리베이터 제어
pers_person_eleSetting=엘리베이터 제어 설정
pers_person_parkSet=차량 번호판 등록
pers_person_pidSet=개인 인증서
pers_person_insSet=얼굴인식 키오스크
pers_person_aiSet=얼굴인식 설정
pers_person_payrollSet=급여 설정
pers_person_psgSet=통로 설정
pers_person_lockerSet=보관함 설정
pers_person_sisSet=보안 설정
pers_person_vdbSet=시각적 대화 설정
pers_person_firstName=이름
pers_person_lastName=회사명
pers_person_name=이름
pers_person_wholeName=이름
pers_person_fullName=이름
pers_person_cardNum=카드 수량
pers_person_deptNum=관련된 부서 수
pers_person_dataCount=통계
pers_person_regFinger=지문
pers_person_reg=등록
pers_person_password=단말기 인증 비밀번호
pers_person_personDate=입사일자
pers_person_birthday=생년월일
pers_person_mobilePhone=전화번호
pers_person_personDevAuth=사용자 단말기 권한
pers_person_email=Email
pers_person_browse=검색
pers_person_authGroup=권한 그룹
pers_person_setEffective=기간 설정
pers_person_attArea=근태 구역
pers_person_isAtt=근태 여부
pers_person_officialStaff=정규직
pers_person_probationStaff=비 정규직
pers_person_identity_category=사용자 유형
pers_person_devOpAuth=장치 관리자
pers_person_msg1=실제 카드 번호와 사이트 코드를 동시에 입력해야 합니다
pers_person_msg2=3~4 자리 숫자를 입력 하십시오
pers_person_msg3=형식 오류
pers_person_imgPixel=(최적 크기 : 120×140)
pers_person_cardLengthDigit=숫자를 입력 하십시오
pers_person_cardLengthHexadecimal=숫자 또는 abcdef 문자를 입력하십시오!
pers_person_to=To
pers_person_templateCount=지문 갯수
pers_person_biotemplateCount=생체 인식 템플릿 갯수
pers_person_regFace=얼굴
pers_person_regVein=손가락 정맥
pers_person_faceTemplateCount=얼굴 갯수
pers_person_VeinTemplateCount=손가락 정맥 갯수
pers_person_palmTemplateCount=손바닥 정맥 갯수
pers_person_cropFace=사용자 사진
pers_person_cropFaceCount=얼굴사진 갯수
pers_person_faceBiodataCount=딥러닝 얼굴 갯수
pers_person_irisCount=홍채 수
pers_person_batchToDept=변경 부서
pers_person_changeReason=변경 사유
pers_person_selectedPerson=사용자를 선택 하십시오
pers_person_duressPwdError=중복 비밀번호
pers_person_completeDelPerson=삭제
pers_person_recover=복구
pers_person_nameNoComma=쉼표를 포함할 수 없습니다
pers_person_firstNameNotEmpty=이름은 비워둘 수 없습니다
pers_person_lastNameNotEmpty=이름은 비워둘 수 없습니다
pers_person_mobilePhoneValidate=전화 번호를 입력 하십시오
pers_person_phoneNumberValidate=전화 번호를 입력 하십시오
pers_person_postcodeValidate=우편 번호를 입력 하십시오
pers_person_idCardValidate=주민등록번호를 입력 하십시오
pers_person_pwdOnlyLetterNum=비밀번호는 문자, 숫자만 입력 가능합니다
pers_person_certNumOnlyLetterNum=문서 번호는 숫자와 문자만 지원합니다
pers_person_oldDeptEqualsNewDept=기존 부서와 변경 부서가 동일합니다
pers_person_disabled=액세스 해제
pers_person_emailError=E-Mail 형식 오류 입니다
pers_person_driverPrompt=장치 드라이버를 설치해야 합니다 / [확인]을 클릭하면 설치가 시작됩니다
pers_person_readIDCardFailed=카드 인증 실패
pers_person_cardPrompt=구역에 카드번호 저장
pers_person_iDCardReadOpenFailed=카드 단말기를 찾을 수가 없습니다
pers_person_iDCardNotFound=카드 번호 읽기 실패 했습니다 / 다시 시도 하십시오
pers_person_nameValid=영어, 숫자, '-', '_'를 지원합니다
pers_person_nameValidForEN=영어, 숫자, "-", "_", "."  입력 가능합니다
pers_person_pinPrompt=영문과 숫자로 조합하여 입력 하십시오
pers_person_pinSet=사용자 ID 설정
pers_person_supportLetter=지원되는 문자
pers_person_cardsSupport=2장 이상 카드등록 여부
pers_person_SupportDefault=사용자 ID 카드 자동 생성
pers_person_noSpecialChar=사용자 이름에 특수 문자를 포함할 수 없습니다
pers_person_pinInteger=숫자를 입력 하십시오
pers_pin_noSpecialChar=사용자 ID 에는 특수 문자가 포함될 수 없습니다
pers_op_capture=캡쳐
pers_person_cardDuress=중복된 카드 번호
pers_person_pwdException=비밀번호 오류
pers_person_systemCheckTip=사용자 정보 형식을 재 확인 하십시오
pers_person_immeHandle=즉시 처리
pers_person_cardSet=카드 설정
pers_person_tempPersonSet=원격으로 등록된 사용자 사진
pers_person_cardsReadMode=카드인증 모드
pers_person_cardsReadModeReadHead=컨트롤러별 읽기
pers_person_cardsReadModeID180=ID180 (등록기)
pers_person_IDReadMode=카드 읽기 모드
pers_person_IDReadModeIDCardReader=카드 단말기
pers_person_IDReadModeTcpReadHead=TCP/IP 단말기
pers_person_physicalNo=주민등록번호
pers_person_physicalNoToCardNo=ID 카드 번호를 등록하여 사용합니다
pers_person_ReadIDCard=신분증 읽기
pers_person_templateBioTypeNumber=생체 인식 템플릿 유형 번호
pers_person_templateValidType=생체 인식 템플릿 유효성
pers_person_templateBioType=생체 인식 템플릿 유형
pers_person_templateVersion=생체 인식 템플릿 버전
pers_person_template=생체 인식 템플릿
pers_person_templateNo=생체 인식 템플릿 번호
pers_person_templateNoIndex=생체 인식 템플릿 색인
pers_person_templateDataUpdate=생체 인식 템플릿이 있는 경우 데이터 업데이트 :
pers_person_templateValidTypeNoNull=생체 인식 템플릿은 비워둘 수 없습니다
pers_person_templateBioTypeNoNull=사용자 ID : {0}, 생체 인식 템플릿 유형은 비워둘 수 없습니다
pers_person_templateVersionNoNull=사용자 ID : {0} / 생체 인식 템플릿 버전은 비워둘 수 없습니다
pers_person_templateNoNull=사용자 ID : {0}, 생체 템플릿은 비워둘 수 없습니다
pers_person_templateNoNoNull=사용자 ID : {0} / 생체 인식 템플릿 번호는 비워둘 수 없습니다
pers_person_templateNoIndexNoNull=사용자 ID : {0}, 생체 인식 템플릿은 비워둘 수 없습니다
pers_person_templateError=사용자 ID : {0}, 생체 인식 템플릿이 잘못 되었습니다
pers_person_bioDuress=협박
pers_person_universal=공통
pers_person_voice=음성 인식
pers_person_iris=홍채
pers_person_retina=망막
pers_person_palmPrints=손바닥 정맥
pers_person_metacarpalVein=손바닥 정맥
pers_person_visibleFace=딥러닝 얼굴
pers_person_pinError=사용자 ID {0} 가 등록되어 있지 않아 데이터를 처리할 수 없습니다
pers_person_pinException=사용자 ID 예외
pers_person_pinAutoIncrement=사용자 ID 자동 추가
pers_person_resetSelfPwd=자체 로그인 비밀번호 재설정
pers_person_picMaxSize=이미지 해상도를 {0} 이하로 설정 하십시오
pers_person_picMinSize=사진 해상도가 낮습니다 / 해상도는 {0} 이상으로 설정해 주십시오
pers_person_cropFaceShow=일치하는 사진 확인
pers_person_faceNoFound=인식할 수 없는 얼굴입니다
pers_person_biometrics=생체 인증 유형
pers_person_photo=사진
pers_person_visibleFaceTemplate=딥러닝 얼굴 템플릿
pers_person_infraredFaceTemplate=적외선 얼굴 템플릿
pers_person_delBioTemplate=생체 인식 데이터 삭제
pers_person_delBioTemplateSelect=삭제할 생체 인식 템플릿을 선택 하십시오
pers_person_infraredFace=근적외선 얼굴
pers_person_notCard=카드를 포함하지 않음
pers_person_notRegFinger=지문을 포함하지 않음
pers_person_notMetacarpalVein=손바닥 정맥을 포함하지 않음
pers_person_notRegVein=손가락 정맥을 포함하지 않음
pers_person_notIris=홍채 포함 안 함
pers_person_notInfraredFaceTemplate=근적외선 얼굴 템플릿을 포함하지 않습니다
pers_person_notVisibleFaceTemplate=딥러닝 얼굴 템플릿을 포함하지 않습니다
pers_person_notVisibleFacePhoto=보이는 얼굴 사진은 포함하지 않습니다.
pers_person_visibleFacePhoto=보이는 얼굴 사진
pers_person_change=인사 조정
pers_person_cropFaceUsePhoto=비교 사진을 프로필 사진으로 표시하시겠습니까?
pers_person_photoUseCropFace=프로필 사진을 사용하여 비교 사진을 생성하시겠습니까?
pers_person_selectCamera=카메라 선택
pers_person_delCropFaceMsg=삭제 가능한 비교 사진이 없습니다!
pers_person_disabledNotOp=이 사람은 이미 비활성화되어 조작할 수 없습니다!
pers_person_visiblePalm=가시광선 손바닥
pers_person_notVisiblePalm=가시광선 손바닥 제외
pers_person_selectDisabledNotOp=선택한 사람 중 비활성화된 사람이 있어 조작할 수 없습니다!
pers_person_photoUseCropFaceAndTempalte=프로필 사진을 사용하여 비교 사진, 얼굴 템플릿을 생성하시겠습니까?
pers_person_photoUseTempalte=사진을 사용하여 얼굴 템플릿을 생성하시겠습니까?
pers_person_createCropFace=비교 사진 생성
pers_person_createFaceTempalte=얼굴 템플릿 생성
pers_person_faceTempalte=얼굴 템플릿
pers_person_extractFaceTemplate=얼굴 템플릿 추출
pers_person_createSuccess=생성 성공
pers_person_createFail=생성 실패
pers_person_serverConnectWarn=서버 주소, 사용자 이름, 암호는 비워둘 수 없습니다!
pers_person_serverOffline=얼굴 템플릿 추출 서버 오프라인
pers_person_faceTemplateError1=얼굴 탐지 실패
pers_person_faceTemplateError2=얼굴 가리기
pers_person_faceTemplateError3=선명도가 부족하다
pers_person_faceTemplateError4=얼굴 각도가 너무 커요
pers_person_faceTemplateError5=생체 검사 실패
pers_person_faceTemplateError6=얼굴 템플릿 추출 실패
pers_person_cropFaceNoExist=비교 사진이 존재하지 않습니다.
pers_person_disableFaceTemplate=얼굴 템플릿 추출이 활성화되지 않았습니다. 템플릿을 추출할 수 없습니다!
pers_person_cropFacePhoto=얼굴 비교 사진
pers_person_vislightPalmPhoto=손바닥 비교 사진
pers_person_serverOfflineWarn=얼굴 템플릿 추출 서버가 오프라인 상태입니다. 이 기능을 사용할 수 없습니다!
pers_person_serverConnectInfo=얼굴 템플릿 추출 서버 연결이 온라인 상태인지 확인하십시오.
pers_person_notModified=휴대폰 번호를 수정할 수 없습니다.
pers_person_readCard=장치에서 카드를 발급합니다
pers_person_stopRead=카드발급 중지
pers_person_chooseDoor=출입문 선택
pers_person_readCarding=머리핀, 나중에 다시 시도하십시오!
pers_person_syncAcms=ACMS와 인원을 동기화하다
pers_person_startUpdate=직원 정보 업데이트를 시작합니다.
pers_person_updateFailed=직원 정보 업데이트에 실패했습니다!
pers_capture_catchPhoto=사진 캡쳐
pers_capture_preview=미리보기
pers_dept_entity=부서
pers_dept_deptNo=부서 번호
pers_dept_deptName=부서명
pers_dept_parentDeptNo=상위 부서번호
pers_dept_parentDeptName=상위 부서명
pers_dept_parentDept=상위 부서
pers_dept_note=신규 추가된 부서가 리스트에 표시되지 않으면 관리자에 문의하여 부서를 편집할 수 있도록 권한을 수정 하십시오
pers_dept_exit=포함
pers_dept_auth=바운드 시스템 사용자
pers_dept_parentMenuMsg=상위 부서는 자체 또는 하위부서로 설정할 수 없습니다
pers_dept_initDept=부서명
pers_dept_deptMarket=마케팅
pers_dept_deptRD=R&D 부서
pers_dept_deptFinancial=관리 부서
pers_dept_nameNoSpace=부서명은 공란으로 시작하거나 끝낼 수 없습니다
pers_dept_nameExist=해당 부서명이 등록되어 있습니다
pers_dept_changeLevel=부서 권한으로 전환할지 여부
pers_dept_noSpecialChar=부서 번호에 특수 문자가 포함될 수 없습니다
pers_dept_NameNoSpecialChar=부서명에 특수 문자가 포함될 수 없습니다
pers_dept_noModifiedParent=상위 부서는 수정할 수 없습니다
pers_position_entity=직급
pers_position_code=직급 번호
pers_position_name=직책
pers_position_notExist=설정되지 않은 경로 입니다
pers_position_sortNo=분류
pers_position_parentName=상위 직급
pers_position_parentCode=상위 직급번호
pers_position_batchToPosition=변경 직급
pers_position_nameExist=해당 직급이 등록되어 있습니다
pers_position_change=직급 변경
pers_position_parentMenuMsg=상위 위치는 하위 위치로 설정할 수 없습니다
pers_position_nameNoSpace=직급명은 비워둘 수 없습니다
pers_position_existSub={0} : 하위 게시물이 포함될 경우 삭제할 수 없습니다
pers_position_existPerson={0} : 사용자를 삭제할 수 없습니다
pers_position_importTemplate=업무 가져오기 템플릿
pers_position_downloadTemplate=템플릿 다운로드 및 가져오기
pers_position_codeNotEmpty=위치 번호는 비워 둘 수 없습니다
pers_position_nameNotEmpty=위치 이름은 비워 둘 수 없습니다
pers_position_nameNoSpecialChar=위치 이름 {0}은 특수 문자를 포함할 수 없습니다
pers_position_noSpecialChar=위치 번호 {0}은 (는) 특수 문자로 구성할 수 없습니다
pers_position_codeLength=위치 번호 {0}의 길이가 30 자리를 초과합니다
pers_position_nameLength=위치명이 {0} 인 데이터가 {1}보다 깁니다
pers_position_codeExist=등록되어 있는 위치 번호 {0} 입니다
pers_cert_type=인증서 유형
pers_cert_number=인증서 번호
pers_cert_name=인증서 이름
pers_cert_numberExist=인증서 번호가 등록되어 있습니다
pers_export_allPersPerson=전체 사용자
pers_export_curPersPerson=현재 사용자
pers_export_template=템플릿 내 보내기
pers_export_personInfo=사용자 정보 내 보내기
pers_export_personInfoTemplate=사용자 가져오기 형식 다운로드
pers_export_personBioTemplate=생체 인식 템플릿 내 보내기
pers_export_basicInfo=사용자 기본정보
pers_export_customAttr=사용자 정보 변경
pers_export_templateComment=필드명, 기본키, 고유값 [null] 값을 허용합니까?({0},{1},{2},{3})
pers_export_templateFileName=사용자 정보 가져오기 템플릿
pers_export_bioTemplateFileName=사용자 생체 인식 템플릿
pers_export_deptInfo=부서 정보 내 보내기
pers_export_deptTemplate=부서 정보 가져오기 형식 다운로드
pers_export_deptTemplateFileName=부서 정보 가져오기
pers_export_personPhoto=사용자 사진 내 보내기
pers_export_allPhotos=전체 사진 (전체 사용자 선택)
pers_export_selectPhotoToExport=사용자 사진 내보내기를 실행하려면 시작 ID, 종료 ID를 선택 하십시오
pers_export_fromId=시작 ID
pers_export_toId=To
pers_export_certNumberComment=인증서 유형은 인증서 번호를 입력한 후 필요합니다
pers_export_templateCommentName=필드 이름:({0})
pers_export_dataExist=가져온 데이터가 시스템에 등록되어 있는지 확인 하십시오
pers_export_cardNoTip=여러 카드 번호는 & 으로 구분
pers_carNumber_importTip=번호판 번호 (여러 번호판& 분리)
pers_import_certNumberExist=인증서 번호 {0}가 등록되어 있습니다
pers_import_complete=완료
pers_import_password=사용자 비밀번호
pers_import_fail={0} 행 실패 : {1}
pers_import_overData={0} 명 정보를 가져오고 있습니다 / 시스템에서 최대 30,000명 정보 입력이 가능합니다
pers_import_pinTooLong=사용자 ID {0} 자릿수를 초과 하였습니다
pers_import_pinExist=사용자 ID {0}가 등록되어 있습니다
pers_import_pinIsRepeat=사용자 ID {0} 중복 되었습니다
pers_import_pinError=사용자 ID {0} 오류
pers_import_pinSupportNumber=사용자 번호는 숫자만 입력 가능합니다 / 사용자 번호 : {0}
pers_import_pinNotSupportNonAlphabetic=사용자 ID는 숫자와 문자만 지원합니다 / 사용자 ID : {0}
pers_import_pinNotNull=사용자 ID는 [0] 으로 설정할 수 없습니다
pers_import_pinStartWithZero=사용자 ID는 [0]으로 시작할 수 없습니다
pers_import_cardNoNotSupportLetter=카드 번호는 문자를 지원하지 않습니다
pers_import_cardNoNotNull=카드 번호는 [0] 으로 설정할 수 없습니다
pers_import_cardNoStartWithZero=카드 번호는 [0]으로 시작할 수 없습니다
pers_import_cardTooLong=카드 번호 {0} 입력범위를 초과 하였습니다
pers_import_cardExist=카드 번호 {0} 가 등록되어 있습니다
pers_import_personPwdOnlyNumber=사용자 비밀번호는 숫자만 지원합니다
pers_import_personPwdTooLong=사용자 비밀번호 {0} 자릿수를 초과 하였습니다
pers_import_personDuressPwd=사용자 비밀번호 {0}가 중복 되었습니다
pers_import_emailTooLong=E-Mail {0} 입력범위를 초과 하였습니다
pers_import_nameTooLong=이름 {0} 입력 범위를 초과 하였습니다
pers_import_genderError=성별 형식 오류
pers_import_personDateError=입사일자 형식 오류
pers_import_createTimeError=시간형식 오류
pers_import_phoneError=전화번호 형식 오류
pers_import_phoneTooLong=전화번호 입력 범위를 초과 하였습니다 (최대 20자)
pers_import_emailError=E-Mail 형식 오류
pers_import_birthdayError=생년월일 형식 오류
pers_import_nameError=이름은 특수 문자를 포함할 수 없습니다 / 사용자 ID : {0}
pers_import_firstnameError=이름에 [,]를 포함할 수 없습니다
pers_import_firstnameNotNull=이름은 비워둘 수 없습니다
pers_import_dataCheck=데이터 확인 완료
pers_import_dataSaveFail=데이터 불러오기 실패
pers_import_allSucceed=전체 데이터 가져오기 완료
pers_import_result=성공 : {0}, 실패 : {1}
pers_import_result2=가져오기 결과
pers_import_result3=성공 : {0}, 업데이트 : {1}, 실패 : {2}
pers_import_notSupportFormat=지원되지 않는 형식입니다
pers_import_selectCorrectFile=파일을 선택 하십시오
pers_import_fileFormat=파일 형식
pers_import_targetFile=대상 파일
pers_import_startRow=시작 행
pers_import_startRowNote=데이터 형식의 1 번째 행은 테이블 이름, 2 번째 행은 헤더, 3 번째 행은 가져오는 데이터 입니다 / 파일을 확인 후 실행 하십시오
pers_import_delimiter=구분 기호
pers_import_importingDataFields=데이터베이스 필드
pers_import_dataSourceFields=데이터 필드 가져오기
pers_import_total=총계
pers_import_dataUpdate=사용자 ID가 등록되어 있는 경우 데이터 업데이트 :
pers_import_dataIsNull=파일에 데이터가 없습니다
pers_import_deptNotExist=등록되어 있지 않은 부서입니다
pers_import_deptIsNotNull=부서명은 비워둘 수 없습니다
pers_import_pinNotEmpty=사용자 ID는 비워둘 수 없습니다
pers_import_nameNotEmpty=개인 이름은 비워 둘 수 없습니다!
pers_import_siteCodeOnlyLetterNum=Site Code 형식 오류
pers_import_cardNoFormatErrors=카드 번호 {0} 형식 오류 입니다
pers_import_cardsNotSupport=현재 시스템 설정에는 2장 이상 카드 등록을 지원하지 않습니다
pers_import_personInfo=사용자 가져오기
pers_import_commentFormat=설명 형식이 잘못 되었습니다
pers_import_noComment={0} 행과 {1} 열의 데이터는 주석 처리되지 않았습니다
pers_import_fieldRepeat={0} 행 및 {1} 열의 데이터가 반복됩니다 / {2}
pers_import_primaryKey=기본 키로 한개 이상의 필드가 있어야 합니다
pers_import_templateIsRepeat=사용자 ID : {0} 생체 템플릿 데이터가 중복 되었습니다
pers_import_biologicalTemplate=생체 인식 템플릿 가져오기
pers_import_uploadFileSuccess=업로드 완료 되었습니다 / 데이터를 분석하고 있습니다 / 잠시만 기다려 주십시오
pers_import_resolutionComplete=파일 분석 후 데이터베이스 업데이트를 시작합니다
pers_import_mustField=가져오기 파일은 {0} 열을 포함해야 합니다
pers_import_bioTemplateSuccess=생체 인식 템플릿 가져오면 각 항목 메뉴에서 장치에 수동으로 동기화 하여야 합니다
pers_import_personPhoto=사용자 사진 가져오기
pers_import_opera_log=작업 로그
pers_import_error_log=에러 로그
pers_import_uploadFileSize={0} 이하 파일을 업로드 하십시오
pers_import_uploadFileSizeLimit=한 번에 가져 오려면 500M 이하의 크기로 파일을 업로드 하십시오
pers_import_type=가져오기 모드
pers_import_photoType=사진
pers_import_archiveType=압축 패키지
pers_import_startUpload=업로드 시작
pers_import_addMore=추가
pers_import_photoQuality=사진 품질
pers_import_original=원본
pers_import_adaptive=조정
pers_import_adaptiveSize=(크기 480×640)
pers_import_totalNumber=총계
pers_import_uploadTip=(업로드 중에는 사진을 삭제하지 마십시오)
pers_import_addPhotoTip=업로드 할 사진을 추가 하십시오
pers_import_selectPhotoTip=업로드 할 사진을 선택 하십시오
pers_import_uploadResult=사진 업로드 결과
pers_import_pleaseSelectPhoto=사진을 선택 하십시오
pers_import_multipleSelectTip=2개 이상 선택 시 [Ctrl] 을 눌러 선택 하십시오
pers_import_replacePhotoTip=업로드 사진 목록에 등록되어 있는 사용자가 포함되어 있습니다 / 변경 하시겠습니까?
pers_import_photoNamePinNotCorrespond=사진 이름과 사용자 ID 일치하지 않음
pers_import_photoFormatRequirement=사진 이름을 사용자 ID로 설정 하십시오 / 사진 형식은 JPG/PNG를 지원합니다
pers_import_filterTip=선택한 사진은 미리보기를 지원하지 않습니다 / 지원하지 않는 사유 :
pers_import_photoContainSpecialCharacters=사진 이름에 특수 문자가 포함되어 있습니다
pers_import_photoFormatError=사진 형식 오류입니다
pers_import_photoSelectNumber=3,000장 이상 사진을 선택할 수 없습니다
pers_import_photoSelectNumberLimit={0} 개 이상의 이미지를 선택할 수 없습니다
pers_import_fileMaxSize=5M 이하 이미지 파일을 업로드 하십시오
pers_import_notUploadPhotoNumber=사진은 최대 3,000장까지 업로드 가능합니다
pers_import_zipFileNotPhoto=zip 파일에 사진이 없습니다 / 다시 선택하여 시도 하십시오
pers_import_personPlateRepeat=차량 번호판 {0}가 등록되어 있습니다
pers_import_filePlateRepeat=차량 번호판 {0} 번호가 등록되어 있습니다
pers_import_personPlateFormat=사용자 차량번호판 {0} 형식이 잘못 되었습니다
pers_import_personPlateMax=등록 가능한 차량 번호판 갯수를 초과 하였습니다 (최대 6개)
pers_import_cropFaceFail=일치하는 사진을 생성하지 못했습니다
pers_import_pinLeaved=사용자 ID : {0}가 퇴사처리 되었습니다
pers_import_exceedLicense=라이선스를 초과한 사용자는 등록할 수 없습니다
pers_import_bioTemplateNotNull=개인 ID, 생체 템플릿 유형, ID 또는 색인 및 내용, 버전은 비워둘 수 없습니다
pers_import_certTypeNotNull=사용자 번호 : {0} ID 유형은 비워둘 수 없습니다
pers_import_certTypeNotExist=직원 번호: {0} 문서 유형이 없습니다.
pers_import_certNumNotNull=인원 번호: {0} 인증서 번호는 비워둘 수 없습니다.
pers_import_certNumberTooLong=행 {0} : ID 번호 {1} 자릿수를 초과 하였습니다
pers_import_idNumberErrors=행 {0} : ID 카드번호 {1}이 형식오류 입니다
pers_import_emailErrors=행 {0} : E-Mail 주소 {1} 형식 오류
pers_import_emailIsExist=E-Mail 주소 {0} 가 등록되어 있습니다
pers_import_emailIsRepeat=행 {0} : 파일 {1}의 E-Mail 주소가 중복되어 있습니다
pers_import_fileMobilePhoneRepeat={0}행: {1} 파일의 내부 휴대폰 번호가 반복되었습니다!
pers_import_mobilePhoneErrors=휴대전화 번호 형식 오류!
pers_import_hireDateError=날짜를 잘못 입력했습니다!
pers_import_selectPhotoType=사진을 가져올 유형을 선택하십시오!
pers_import_hireDateLaterCurrent=채용 날짜는 현재 날짜보다 늦으면 안 됩니다!
pers_import_buildingNotExist=건물이 존재하지 않습니다!
pers_import_unitNotExist=유닛이 존재하지 않습니다!
pers_import_vdbInfoFail={0} 건물에 {1}이라는 유닛 정보가 없습니다!
pers_import_vdbBuildingFail={0} 유닛의 건물 정보는 비워둘 수 없습니다!
pers_import_vdbRoomNoFail=방 번호는 0보다 큰 숫자만 가능합니다!
pers_person_leave=퇴사
pers_dimission_date=퇴사 일자
pers_dimission_type=퇴사 유형
pers_dimission_reason=퇴사 사유
pers_dimission_volutary=자진 퇴사
pers_dimission_dismiss=퇴사
pers_dimission_resignat=퇴사
pers_dimission_shiftJob=이직
pers_dimission_leave=무급 휴직
pers_dimission_recovery=복직
pers_dimission_sureToRecovery=복직 기능을 실행 하시겠습니까?
pers_dimission_backCard=카드를 반납 하시겠습니까?
pers_dimission_isForbidAction=출입통제 권한을 비활성화 하시겠습니까?
pers_dimission_writeInfomation=퇴사 정보 입력
pers_dimission_pinRetain=퇴사 처리한 사용자 ID를 유지 하시겠습니까?
pers_dimission_downloadTemplate=퇴사 처리된 사용자 가져오기 템플릿 다운로드
pers_dimission_import=퇴사 처리된 사용자
pers_dimission_importTemplate=퇴사 처리된 사용자 가져오기 템플릿
pers_dimission_date_noNull=종료 일자는 비워둘 수 없습니다
pers_dimission_leaveType_noExist=등록되지 않은 퇴사 유형입니다
pers_dimission_dateFormat=필수 필드, 시간 형식은 yyyy-MM-dd입니다. 예 : 2020-07-22
pers_dimission_leaveType=필수 필드 (예 : 자진 퇴사, 퇴사, 퇴사, 이직)
pers_dimission_forbidden=비활성화
pers_dimission_leaveType_noNull=휴가 유형은 비워둘 수 없습니다!
pers_dimission_person_noExist=해고자는 존재하지 않습니다!
pers_dimission_date_error=사퇴 날짜가 작성되지 않았거나 형식이 올바르지 않음
pers_tempPerson_audit=검토
pers_tempPerson_view=보기
pers_tempPerson_waitReview=관리자 검토를 기다리는 중입니다
pers_card_cardNo=카드 번호
pers_card_state=카드 상태
pers_card_effect=유효
pers_card_disabled=사용 안함
pers_card_past=만료
pers_card_back=카드 반환
pers_card_change=카드 변경
pers_card_note=등록되어 있는 카드입니다
pers_card_numTooBig=카드번호 입력 범위를 초과 하였습니다
pers_issueCard_entity=발급 카드
pers_issueCard_operator=관리자
pers_issueCard_operate=작업 유형
pers_issueCard_note=사용자 정보 관리자로 설정되어 있으며 카드 운영 권한은 설정되어 있지 않습니다
pers_issueCard_date=카드 발급일자
pers_issueCard_changeTime=변경 시간
pers_issueCard_cardValidate=카드 번호는 공란을 포함할 수 없습니다
pers_issueCard_cardEmptyNote=카드 번호는 비워둘 수 없습니다
pers_issueCard_cardHasBeenIssued=발급된 카드입니다
pers_issueCard_noCardPerson=미 발급 사용자
pers_issueCard_waitPerson=카드 발급 관리자
pers_issueCard_mc5000=MC5000 머리띠
pers_batchIssCard_entity=일괄 카드등록
pers_batchIssCard_startPersNo=시작 사용자 ID
pers_batchIssCard_endPersNo=종료 사용자 ID
pers_batchIssCard_issCardNum=발급된 카드
pers_batchIssCard_notIssCardNum=카드 미 발급된 사용자
pers_batchIssCard_generateList=추가된 사용자 목록
pers_batchIssCard_startRead=읽기 시작
pers_batchIssCard_swipCard=카드 인증위치
pers_batchIssCard_sendCard=단말기
pers_batchIssCard_dispenCardIss=카드 등록기
pers_batchIssCard_usbEncoder=Desfire 카드 등록기
pers_batchIssCard_note=사용자 ID는 숫자 입력만 지원하며 카드 발행되지 않은 사용자는 300명까지 표시됩니다
pers_batchIssCard_startPinEmpty=시작 사용자 ID 번호는 비워둘 수 없습니다
pers_batchIssCard_endPinEmpty=종료 사용자 ID는 비워둘 수 없습니다
pers_batchIssCard_startPinLargeThanEndPin=시작 사용자 ID 가 종료 사용자 ID 값보다 크게 설정 되었습니다
pers_batchIssCard_numberParagraphNoPerson=설정한 ID 범위 내 카드 미 발급자가 없습니다
pers_batchIssCard_inputCardNum=카드번호 입력 하십시오
pers_batchIssCard_cardLetter=카드 번호는 문자를 지원하지 않습니다
pers_batchIssCard_cardNoTooLong=카드 입력 범위를 초과 하였습니다
pers_batchIssCard_issueWay=카드 등록 방법
pers_batchIssCard_noPersonList=사용자 정보가 생성되지 않았습니다
pers_batchIssCard_startReadCard=카드 읽기 시작
pers_batchIssCard_swipePosition=카드인식 위치
pers_batchIssCard_chooseSwipePosition=카드 인증위치를 선택 하십시오
pers_batchIssCard_readCardTip=단말기를 통해 카드를 등록 시 등록되지 않은 카드만 읽을 수 있습니다
pers_batchIssCard_notIssCardNo=발급되지 않은 카드 수량
pers_batchIssCard_totalNumOfCards=총 카드 수
pers_batchIssCard_acms=ACMS 카드 발급
pers_lossCard_entity=카드 비활성화
pers_lossCard_lost=등록되어 있는 카드입니다
pers_losscard_note2=관리 카드를 등록한 후 엘리베이터 단말기에 등록해야 합니다
pers_revertCard_entity=카드 활성화
pers_revertCard_setReport=분실 카드로 등록 하십시오
pers_revertcard_note2=카드 등록 후 엘리베이터 모듈에 해당 카드를 추가로 등록 하십시오
pers_issueCard_success=카드 등록 완료
pers_issueCard_error=카드발급 실패
pers_cardData_error=카드 데이터 형식이 일치하지 않습니다
pers_analysis_error=카드 데이터 분석 예외
pers_cardOperation_error=카드 작업 예외
pers_cardPacket_error=카드동작 Cmd 오류
pers_card_write=카드 쓰기
pers_card_init=카드 초기화
pers_card_loss=카드 분실
pers_card_revert=카드 되돌리기
pers_card_writeMgr=관리 카드 쓰기
pers_initCard_tip=초기화하면 해당 카드는 사용할 수 없습니다
pers_initCard_prepare=카드 초기화 준비 완료 입니다
pers_initCard_process=카드 초기화 중 입니다
pers_initCard_success=카드를 초기화 합니다
pers_mgrCard_prepare=관리 카드 데이터 등록 준비 중 입니다
pers_mgrCard_process=관리 카드 데이터를 작성 중입니다
pers_mgrCard_success=관리카드 등록 성공
pers_userCard_prepare=사용자 카드 작성 준비 중 입니다
pers_userCard_process=사용자 카드 데이터 작성 중 입니다
pers_userCard_success=사용자 카드 쓰기 성공
pers_userCard_tip=사용자 편집에서 시작 시간과 종료 시간을 설정한 다음 카드 작업을 작성 하십시오
pers_userCard_tip2=권한 데이터가 비어 있어 카드를 쓸 수 없습니다
pers_userCard_tip3=권한 그룹 내 2 대 이상 장치 설정되어 데이터가 손실 되었습니다
pers_writeCard_tip=등록기 연결 및 드라이버 설치 후 카드를 등록기에 인식 하십시오
pers_writeMgrCard_tip=분실 카드와 삭제 카드 수량은 18을 초과할 수 없습니다
pers_writeMgrCard_tip2=분실 카드 및 취소 카드 수량 :
pers_card_writeToMgr=관리 카드로 등록
pers_card_hex=카드 형식 표시
pers_card_decimal=10 진수
pers_card_Hexadecimal=16 진수
pers_card_IssuedCommandFail=Cmd 실행 실패 :
pers_card_multiCard=카드추가 등록
pers_card_deputyCard=보조 카드
pers_card_deputyCardValid=메인 카드를 입력 하십시오
pers_card_writePinFormat=사용자 ID에 숫자만 입력 가능합니다
pers_card_notMoreThanSixteen=16장 이상 카드를 등록 할 수 없습니다
pers_card_notDelAll=추가된 카드를 삭제할 수 없습니다
pers_card_maxCard=카드 번호는 {0}을 초과할 수 없습니다
pers_card_posUseCardNo=사용자 메인 카드는 식수 모듈에서 사용 중 입니다 / 식수 모듈에서 카드를 삭제 하십시오
pers_card_delFirst=카드를 보내기 전에 이미 보낸 카드 번호를 삭제하세요!
pers_card_disablePersonWarn=선택한 카드 번호에 장애인이 포함되어 있어 조작할 수 없습니다!
pers_wiegandFmt_siteCode=Site Code
pers_wiegandFmt_wiegandMode=모드
pers_wiegandFmt_wiegandModeOne=모드 1
pers_wiegandFmt_wiegandModeTwo=모드 2
pers_wiegandFmt_isDefaultFmt=자동 매칭
pers_wgFmt_entity=Wiegand 형식
pers_wgFmt_in=Wiegand 입력 형식
pers_wgFmt_out=Wiegand 출력 형식
pers_wgFmt_inType=Wiegand 입력 유형
pers_wgFmt_outType=Wiegand 출력 유형
pers_wgFmt_wg=Wiegand 형식
pers_wgFmt_totalBit=총 비트수
pers_wgFmt_oddPch=홀수 패리티 (o)
pers_wgFmt_evenPck=짝수 패리티 (e)
pers_wgFmt_CID=CID (c)
pers_wgFmt_facilityCode=Device code (f)
pers_wgFmt_siteCode=Site Code(s)
pers_wgFmt_manufactoryCode=Manufacturer Code(m)
pers_wgFmt_firstParity=시작 패리티 (p)
pers_wgFmt_secondParity=두 번째 패리티 비트 (p)
pers_wgFmt_cardFmt=카드 형식 확인
pers_wgFmt_parityFmt=패리티 체크 형식
pers_wgFmt_startBit=시작 비트
pers_wgFmt_test=카드 형식 테스트
pers_wgFmt_error=Wiegand 형식 오류
pers_wgFmt_verify1=자리수는 80을 초과할 수 없습니다
pers_wgFmt_verify2=카드 확인 형식의 길이는 총 자리수와 같아야 합니다
pers_wgFmt_verify3=패리티 형식 길이는 총 비트수와 같아야 합니다
pers_wgFmt_verify4=초기화 된 데이터는 삭제할 수 없습니다
pers_wgFmt_verify5=해당 카드 형식을 사용 중이며 삭제할 수 없습니다
pers_wgFmt_verify6=첫 번째 패리티 비트는 총 비트수 보다 클 수 없습니다
pers_wgFmt_verify7=두 번째 패리티 비트는 총 비트수 보다 클 수 없습니다
pers_wgFmt_verify8=시작 비트 및 최대 길이 비트 형식이 일치하지 않습니다
pers_wgFmt_verify9=카드 확인 형식이 총 자릿수를 초과 합니다
pers_wgFmt_verify10=카드 확인 숫자 기능은 교차할 수 없습니다
pers_wgFmt_verify11=Site Code가 설정 범위를 초과 하였습니다
pers_wgFmt_verify=확인
pers_wgFmt_unverify=확인 안함
pers_wgFmt_atLeastDefaultFmt=자동으로 일치하는 카드 형식을 1 개 이상 설정 하십시오
pers_wgFmt_defaultFmtError1=카드 비트가 동일한 다른 카드 형식이 있습니다 / 카드 형식을 자동으로 일치하도록 설정할 수 없습니다
pers_wgFmt_defaultFmtError2=자동 일치와 동일한 카드 형식이 있으면 실행되지 않습니다
pers_wgFmt_cardFormats=카드 형식
pers_wgFmt_cardFormatTesting=카드포맷 테스트
pers_wgFmt_checkIsUsed=카드 형식은 {0}에서 사용되며 삭제할 수 없습니다!
pers_wgFmt_supportDigitsNumber=장치에서 지원하는 자릿수 길이를 입력하십시오
pers_widget_selectPerson=사용자를 선택 하십시오
pers_widget_searchType1=검색 조건
pers_widget_searchType2=부서
pers_widget_deptHint=참고 : 선택한 부서 모든 사용자 정보 가져오기
pers_widget_noPerson=선택된 사용자가 없습니다
pers_widget_noDept=부서를 선택 하십시오
pers_widget_noDeptPerson=선택한 부서에 사용자가 없습니다 / 새 부서를 선택 하십시오
pers_person_carPlate=차량 번호판
pers_person_socialSecurity=주민등록번호
pers_person_msg4=최대 길이는 20자를 초과할 수 없습니다
pers_person_msg5=이름은 50자를 초과할 수 없습니다
pers_person_type=사용자 유형
pers_person_reseCode=개인 비밀번호
pers_person_IsSendMail=E-Mail 알림
pers_person_inactive=비활성화
pers_person_active=활성화
pers_person_employee=직원
pers_person_isSendMailMsg=[로그 알림] 기능을 사용하려면 E-Mail을 입력 하십시오
pers_person_createTime=생성 시간
pers_person_pinFirstValid=사용자 ID 시작 번호는 8, 9로 입력 할 수 없습니다
pers_person_attrValueValid=필드값을 복사할 수 없습니다
pers_person_attrValueDelimiterValid=분리기호(세미콜론)를 중간에 입력 하십시오
pers_person_isSendSMS=SMS 알림
pers_person_building=건물 이름
pers_person_unitName=유닛 이름
pers_person_roomNo=방 번호
pers_attr_emp_type=사용자 유형
pers_attr_street=거리
pers_attr_nation=국가
pers_attr_office_address=회사 주소
pers_attr_postcode=우편 번호
pers_attr_office_phone=회사 전화번호
pers_attr_home_phone=집 전화번호
pers_attr_job_title=직급
pers_attr_birthplace=출생지
pers_attr_polit_status=-
pers_attr_country=국가
pers_attr_home_address=자택 주소
pers_attr_hire_type=고용 유형
pers_attr_inContract=계약직
pers_attr_outContract=비 정규직
pers_attribute_attrName=이름 표시
pers_attribute_attrValue=필드 값
pers_attribute_controlType=입력 유형
pers_attribute_positionX=행
pers_attribute_positionY=열
pers_attribute_showTable=화면 목록에 표시
pers_attrDefini_deletemsg=해당 속성은 사용 중입니다 / 삭제 하시겠습니까?
pers_attrDefini_reserved=시스템 예약 필드명
pers_attrDefini_msg1=최대 30자리 이하로 입력 가능합니다
pers_attrDefini_msg2=행과 열이 등록되어 있습니다 / 다른 위치로 변경 하십시오
pers_attrDefini_attrValue_split=세미콜론 \'; \'구분 기호
pers_attrDefini_attrName=속성 이름
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=속성 번호
pers_attrDefini_select=목록 접기
pers_attrDefini_check=2개 이상 선택
pers_attrDefini_radio=1개 선택
pers_attrDefini_text=직접 입력
pers_attrDefini_maxCol=2열을 초과할 수 없습니다
pers_attrDefini_maxLimit=사용자 필드 값을 최대로 설정 하였습니다
pers_attrDefini_modControlType=입력 유형을 변경하면 시스템에 저장된 모든 사용자의 현재 필드 데이터가 삭제됩니다 / 계속 하시겠습니까?
pers_leavePerson_reinstated=복직
pers_example_newRecode=신규 로그 가져오기
pers_example_allRecode=전체 로그 가져오기
pers_custField_StatisticalType=통계 유형
pers_param_isAudit=자동으로 사용자 추가
pers_param_donotChangePin=등록되어 있는 사용자 ID에 문자가 포함되어 있어 [영/숫자 설정] 기능을 비활성화 할 수 없습니다
pers_param_hexChangeWarn=해당 카드는 등록되어 있어 카드형식 모드를 변경할 수 없습니다
pers_param_cardsChangeWarn=2장 이상 카드가 등록된 사용자가 있어 [2장 이상 카드등록] 기능을 비활성화 할 수 없습니다
pers_param_maxPinLength=신규 사용자 ID는 기존 등록된 사용자 ID보다 작게 설정할 수 없습니다
pers_param_pinBeyondDevLength=시스템에서 장치가 지원하는 사용자 ID 자릿수는 {0} 입니다 / {1}보다 작은 정수를 입력 하십시오
pers_param_cardBeyondDevLength=시스템에서 장치가 지원하는 최대 카드 번호는 {0}입니다 / {1}보다 작은 정수를 입력 하십시오
pers_param_checkIsExistNoAudit=시스템에 등록되지 않은 사용자가 있으며 자동으로 수정할 수 없습니다
pers_param_noSupportPinLetter=문자가 포함된 사용자 ID를 지원하지 않는 장치가 있어 사용자 ID 모드를 변경할 수 없습니다
pers_param_changePinLettersTip=사용자 ID는 자동으로 추가되며 [영/숫자] 모드로 변경할 수 없습니다
pers_param_changePinIncrementTip=ID에 문자를 포함한 사용자가 저장되어 있어 숫자 모드로 변경할 수 없습니다
pers_param_qrCode=엔터프라이즈 QR 코드
pers_param_employeeRegistrar=클라우드 사용자 등록 활성화
pers_param_downloadQRCodePic=QR코드 이미지 다운로드
pers_param_qrCodeUrl=QR 코드 URL
pers_param_qrCodeUrlCreate=셀프 서비스 등록
pers_param_qrCodeUrlHref=서버 주소 : 포트
pers_param_pinSetWarn=현재 시스템에 이미 인원이 있으므로 인원 수 모드를 변경할 수 없습니다.
pers_param_selfRegistration=자체 등록 활성화
pers_param_infoProtection=개인 민감한 정보 보호
pers_param_infoProtectionWarnMsg=개인 민감 정보 보호 옵션을 활성화 시, 중요 개인 데이터(이름, 카드번호, ID 번호, 사진 등)가 가려지거나, 숨김 처리 됩니다.
pers_param_templateServer=얼굴 템플릿 추출 서버
pers_param_enableFacialTemplate=얼굴 템플릿 추출 사용
pers_param_templateServerAddr=얼굴 템플릿 추출 서버 주소
pers_param_templateServerWarnInfo=얼굴 템플릿 추출을 활성화하면 얼굴 템플릿 추출 서버가 온라인 상태이고 사용자 인증이 통과되면 사진을 비교할 때 얼굴 템플릿을 추출합니다. 만약 얼굴 템플릿 추출 서버가 오프라인 모드인 경우, 얼굴 템플릿을 추출하지 않습니다.
pers_param_templateServerWarnInfo1=얼굴 템플릿 추출을 사용할 경우 얼굴 템플릿 추출을 지원하는 장치를 연결해야 합니다!
pers_param_templateServerOffline=얼굴 템플릿 추출 서버가 오프라인 상태입니다. 얼굴 템플릿을 추출할 수 없습니다!계속하시겠습니까?
pers_param_faceServer=얼굴 백그라운드 비교 서비스
pers_param_enableFaceVerify=얼굴 백그라운드 비교 활성화
pers_param_faceServerAddr=얼굴 백그라운드 비교 서비스 주소
pers_param_faceServerSecret=얼굴 백그라운드 비교 서비스 비밀 키
pers_person_nationality=국가 / 지역
pers_nationality_angola=앙골라
pers_nationality_afghanistan=아프가니스탄
pers_nationality_albania=알바니아
pers_nationality_algeria=알제리
pers_nationality_america=미국
pers_nationality_andorra=안도라
pers_nationality_anguilla=안길란
pers_nationality_antAndBar=앤티가 바부 다
pers_nationality_argentina=아르헨티나
pers_nationality_armenia=아르메니아
pers_nationality_ascension=승진
pers_nationality_australia=호주
pers_nationality_austria=오스트리아
pers_nationality_azerbaijan=아제르바이잔
pers_nationality_bahamas=바하마
pers_nationality_bahrain=바레인
pers_nationality_bangladesh=방글라데시
pers_nationality_barbados=바베이도스
pers_nationality_belarus=벨로루시
pers_nationality_belgium=벨기에
pers_nationality_belize=벨리지
pers_nationality_benin=베네시안
pers_nationality_bermudaIs=버뮤다 제도
pers_nationality_bolivia=볼리비아
pers_nationality_botswana=보츠와나
pers_nationality_brazil=브라질
pers_nationality_brunei=브루나이
pers_nationality_bulgaria=불가리아
pers_nationality_burkinaFaso=부르키나 파소
pers_nationality_burma=미얀마
pers_nationality_burundi=부룬디
pers_nationality_cameroon=카메룬
pers_nationality_canada=캐나다
pers_nationality_caymanIs=케이맨 제도
pers_nationality_cenAfrRepub=중앙 아프리카 공화국
pers_nationality_chad=차드
pers_nationality_chile=칠레
pers_nationality_china=중국
pers_nationality_colombia=콜롬비아
pers_nationality_congo=콩고
pers_nationality_cookIs=쿡 제도
pers_nationality_costaRica=코스타리카
pers_nationality_cuba=쿠바
pers_nationality_cyprus=키프로스
pers_nationality_czechRep=체코
pers_nationality_denmark=덴마크
pers_nationality_djibouti=지부티
pers_nationality_dominicaRep=도미니카 공화국
pers_nationality_ecuador=에콰도르
pers_nationality_egypt=이집트
pers_nationality_eISalvador=엘 살바도르
pers_nationality_england=영국
pers_nationality_estonia=에스토니아
pers_nationality_ethiopia=에티오피아
pers_nationality_fiji=피지
pers_nationality_finland=핀란드
pers_nationality_france=프랑스
pers_nationality_freGui=프랑스 령 기아나
pers_nationality_gabon=가봉
pers_nationality_gambia=감비아
pers_nationality_georgia=조지아
pers_nationality_germany=독일
pers_nationality_ghana=가나
pers_nationality_gibraltarm=지브롤터
pers_nationality_greece=그리스
pers_nationality_grenada=그레나다
pers_nationality_guam=괌
pers_nationality_guatemala=과테말라
pers_nationality_guinea=기니
pers_nationality_guyana=가이아나
pers_nationality_haiti=아이티
pers_nationality_honduras=온두라스
pers_nationality_hungary=헝가리
pers_nationality_iceland=아이슬란드
pers_nationality_india=인도
pers_nationality_indonesia=인도네시아
pers_nationality_iran=이란
pers_nationality_iraq=이라크
pers_nationality_ireland=아일랜드
pers_nationality_israel=이스라엘
pers_nationality_italy=이탈리아
pers_nationality_ivoryCoast=코트디 부아르
pers_nationality_jamaica=자메이카
pers_nationality_japan=일본
pers_nationality_jordan=요르단
pers_nationality_kenya=케냐
pers_nationality_korea=한국
pers_nationality_kuwait=쿠웨이트
pers_nationality_kyrgyzstan=키르기스스탄
pers_nationality_laos=라오스
pers_nationality_latvia=라트비아
pers_nationality_lebanon=레바논
pers_nationality_lesotho=레소토
pers_nationality_liberia=라이베리아
pers_nationality_libya=리비아
pers_nationality_liechtenstein=리히텐슈타인
pers_nationality_lithuania=리투아니아
pers_nationality_luxembourg=룩셈부르크
pers_nationality_madagascar=마다가스카르
pers_nationality_malawi=말라위
pers_nationality_malaysia=말레이시아
pers_nationality_maldives=몰디브
pers_nationality_mali=말리
pers_nationality_malta=몰타
pers_nationality_marianaIs=마리아나
pers_nationality_martinique=마르티니크
pers_nationality_mauritius=모리셔스
pers_nationality_mexico=멕시코
pers_nationality_moldova=몰도바
pers_nationality_monaco=모나코
pers_nationality_montseIs=몬트세라트
pers_nationality_morocco=모로코
pers_nationality_mozambique=모잠비크
pers_nationality_namibia=나미비아
pers_nationality_nauru=나우루
pers_nationality_nepal=네팔
pers_nationality_netAnti=네덜란드
pers_nationality_netherlands=네덜란드
pers_nationality_newZealand=뉴질랜드
pers_nationality_nicaragua=니카라과
pers_nationality_niger=니제르
pers_nationality_nigeria=나이지리아
pers_nationality_norKorea=북한
pers_nationality_norway=노르웨이
pers_nationality_oman=오만
pers_nationality_pakistan=파키스탄
pers_nationality_panama=파나마
pers_nationality_papNewCui=파푸아 뉴기니
pers_nationality_paraguay=파라과이
pers_nationality_peru=페루
pers_nationality_philippines=필리핀
pers_nationality_poland=폴란드
pers_nationality_frenPolyne=프랑스 령 폴리네시아
pers_nationality_portugal=포르투갈
pers_nationality_puerRico=푸에르토 리코
pers_nationality_qatar=카타르
pers_nationality_reunion=재 결합
pers_nationality_romania=루마니아
pers_nationality_russia=러시아
pers_nationality_saiLueia=세인트 루시아
pers_nationality_saintVinc=세인트 빈센트
pers_nationality_samoa_eastern=사모아 동부
pers_nationality_samoa_western=사모아 서부
pers_nationality_sanMarino=산 마리노
pers_nationality_saoAndPrinc=상투메 프린시페
pers_nationality_sauArabia=사우디 아라비아
pers_nationality_senegal=세네갈
pers_nationality_seychelles=세이셸
pers_nationality_sieLeone=시에라 리온
pers_nationality_singapore=싱가포르
pers_nationality_slovakia=슬로바키아
pers_nationality_slovenia=슬로베니아
pers_nationality_solomonIs=솔로몬 제도
pers_nationality_somali=소말리아
pers_nationality_souAfrica=남 아프리카
pers_nationality_spain=스페인
pers_nationality_sriLanka=스리랑카
pers_nationality_sudan=수단
pers_nationality_suriname=수리남
pers_nationality_swaziland=스와질랜드
pers_nationality_sweden=스웨덴
pers_nationality_switzerland=스위스
pers_nationality_syria=시리아
pers_nationality_tajikstan=타지키스탄
pers_nationality_tanzania=탄자니아
pers_nationality_thailand=태국
pers_nationality_togo=토고
pers_nationality_tonga=통가
pers_nationality_triAndToba=트리니다드 토바고
pers_nationality_tunisia=튀니지
pers_nationality_turkey=터키
pers_nationality_turkmenistan=투르크 메니스탄
pers_nationality_uganda=우간다
pers_nationality_ukraine=칠레
pers_nationality_uniArabEmira=아랍 에미리트
pers_nationality_uruguay=우루과이
pers_nationality_uzbekistan=우즈베키스탄
pers_nationality_venezuela=베네수엘라
pers_nationality_vietnam=베트남
pers_nationality_yemen=예멘
pers_nationality_serbia=세르비아
pers_nationality_zimbabwe=짐바브웨
pers_nationality_zambia=잠비아
pers_nationality_aruba=아루바
pers_nationality_bhutan=부탄
pers_nationality_bosnia_herzegovina=보스니아
pers_nationality_cambodia=캄보디아
pers_nationality_congoD=콩고
pers_nationality_comoros=코모로
pers_nationality_capeVerde=카보 베르테
pers_nationality_croatia=크로아티아
pers_nationality_dominica=도미니카
pers_nationality_eritrea=에리트리아
pers_nationality_micronesia=미크로네시아
pers_nationalit_guineaBissau=기니 비사우
pers_nationalit_equatorialGuinea=기니
pers_nationalit_hongkong=홍콩
pers_nationalit_virginIslands=미국령 버진 아일랜드
pers_nationalit_britishVirginIslands=아일랜드
pers_nationalit_kiribati=키리바시
pers_nationalit_mongolia=몽골
pers_nationalit_marshall=마셜 섬
pers_nationalit_macedonia=마케도니아
pers_nationalit_montenegro=몬테네그로
pers_nationalit_mauritania=모리타니
pers_nationalit_palestine=팔레스타인
pers_nationalit_palau=팔라우
pers_nationalit_rwanda=르완다
pers_nationalit_saintKittsNevis=세인트 키츠 네비스
pers_nationalit_timorLeste=동티모르
pers_nationalit_taiwan=대만
pers_nationalit_tuvalu=투발루
pers_nationalit_vanuatu=바누아투
pers_person_cardprint=프린트 카드
pers_cardTemplate_tempSelect=템플릿 선택
pers_cardTemplate_printerSelect=프린터 선택
pers_cardTemplate_front=앞
pers_cardTemplate_opposite=뒤
pers_cardTemplate_entryDate=입사일자
pers_cardTemplate_photo=사진
pers_cardTemplate_uploadFail=사진 업로드 실패
pers_cardTemplate_jpgFormat=JPG 형식 파일만 업로드 가능합니다
pers_cardTemplate_printStatus=프린트 상태
pers_cardTemplate_waiting=대기 중
pers_cardTemplate_printing=인쇄
pers_cardTemplate_printOption=인쇄 옵션
pers_cardTemplate_duplexPrint=양면 인쇄
pers_cardTemplate_frontOnly=전면만 인쇄
pers_app_delPers=삭제하려는 사용자가 등록되어 있지 않습니다
pers_app_deptIsNull=부서번호 또는 부서명에 해당하는 부서를 찾을 수 없습니다
pers_app_personNull=등록되지 않는 사용자
pers_app_pinExist=등록되어 있는 사용자 ID 입니다
pers_app_dateError=일자 형식이 잘못 되었습니다 (ex. 2020-01-01)
pers_api_selectPhotoInvalid=사진이 유효하지 않습니다 / 다시 업로드 하십시오
pers_api_dateError=일자 형식 오류
pers_api_personNotExist=등록되지 않은 사용자
pers_api_cardsPersSupport=2장 이상 카드등록 기능 비활성화 상태로 카드 추가되지 않습니다
pers_api_department_codeOrNameNotNull=부서 번호 또는 부서 이름은 비워 둘 수 없습니다
pers_api_deptSortNoIsNull=부서 정렬은 비워둘 수 없습니다
pers_api_deptSortNoError=부서 정렬 값은 1-999999 사이여야 합니다
pers_api_dataLimit=현재 작업 수는 {0}으로 {1} 제한을 초과했습니다.나누어 조작하세요!
pers_api_cardTypeError=카드 유형 오류
pers_api_fingerprintExisted=등록되어 있는 지문입니다
pers_api_validtypeIncorrect=유형 입력 오류입니다
pers_api_dataNotExist=템플릿 번호가 등록되어 있지 않습니다
pers_api_templateNoRang=0~9 범위 내 지문 템플릿 번호를 입력 하십시오
pers_api_templateIsNull=템플릿은 비워둘 수 없습니다!
pers_api_versionIsNumber=버전은 숫자만 입력할 수 있습니다!
pers_tempPersion_pinOnlyNumber=사용자 ID는 숫자만 입력할 수 있습니다
pers_h5_personAvatarNotNull=얼굴 정보가 비어 있습니다
pers_h5_personMobileRepeat=전화번호가 등록되어 있습니다
pers_h5_personEmailRepeat=해당 E-Mail이 등록되어 있습니다
pers_h5_pwdIsRepetition=신규 비밀번호와 이전 비밀번호가 중복됩니다
pers_h5_personIdNull=사용자 ID가 비어 있습니다
pers_h5_pinOrEmailIsNull=번호와 E-Mail 주소를 입력 하십시오
pers_emailTitle_resetPassword=비밀번호 변경
pers_emailContent_resetPassword=링크는 24시간 동안 유효합니다 / 링크를 복사하여 비밀번호를 변경 하십시오
pers_h5_tokenIsNull=바우처가 비어 있습니다
pers_h5_tokenError=라이선스 오류
pers_h5_oldPwdIsError=기존 비밀번호가 잘못 입력 되었습니다
pers_api_resetPasswordSuccess=비밀번호가 변경 되었습니다
pers_api_resetPasswordFail=비밀번호 변경 실패
pers_api_forgetPassword=비밀번호 재 설정
pers_api_confirmSubmit=제출 확인
pers_api_confirmPwdCaution=[확인]을 클릭하여 새 비밀번호를 확인 하십시오
pers_api_pwdRule=비밀번호는 하나 이상의 기호 또는 숫자를 포함해야 하며 8-12 자 이상이어야 합니다
pers_h5_personPinFormatNumber=사용자 번호는 숫자 또는 문자로만 구성할 수 있습니다
pers_h5_persEmailNoExist=E-Mail 입력 오류
pers_h5_pageNull=페이징 매개 변수 오류
pers_h5_personPinNotStartWithZero=사용자 ID는 0으로 시작할 수 없습니다
pers_h5_personPinTooLong=사용자 ID 입력 범위를 초과 하였습니다
pers_h5_personPinInValid=등록되어 있는 번호 입니다
pers_h5_imgSizeError=10M 이하의 이미지를 업로드 하십시오
pers_h5_confirmAndContinue=확인 및 계속
pers_face_poorResolution=80,000 픽셀 이하 사진 해상도
pers_face_noFace=얼굴이 감지되지 않았습니다
pers_face_manyFace=여러 얼굴 감지
pers_face_smallFace=얼굴 비율이 너무 작습니다
pers_face_notColor=사진이 흑백 사진입니다
pers_face_seriousBlur=이미지가 흐려짐
pers_face_intensivelyLight=이미지가 많이 노출 되었습니다
pers_face_badIllumination=그림이 너무 어둡습니다
pers_face_highNoise=노이즈가 높은 사진
pers_face_highStretch=과장된 얼굴
pers_face_covered=얼굴이 덮여 있습니다
pers_face_smileOpenMouth=과도한 미소
pers_face_largeAngle=얼굴 좌우각이 너무 큽니다
pers_face_criticalIllumination=이미지 밝기 중요
pers_face_criticalLargeAngle=임계면 처짐 각도
pers_face_validFailMsg=다음으로 인해 얼굴 인식에 실패했습니다 :
pers_face_failType=페이스 컷 아웃 실패 유형
pers_face_photoFormatError=사진 형식이 잘못되었습니다  JPG / PNG 형식 파일을 업로드하세요
pers_face_notUpdateMsg=얼굴 사진 생성에 실패했습니다. 얼굴 사진을 업데이트하지 마십시오.
pers_health_enable=건강 정보 설명 사용
pers_health_attrExposure=의심 스럽거나 확인 된 사례와 접촉 한 적이 있습니까
pers_health_attrSymptom=지난 14 일 동안 증상은 무엇입니까
pers_health_attrVisitCity=지난 14 일 동안 방문한 도시
pers_health_attrRemarks=건강 상태 메모
pers_health_symptomCough=기침
pers_health_symptomFever=열
pers_health_symptomPolypena=숨가쁨
pers_health_declaration=이상없음
pers_health_aggrement=정보를 입력하지 않은 사용자는 출입할 수 없으며, 거짓 정보를 입력한 사용자는 지속적으로 출입할 수 없습니다 / 이에 상응하는 법적 책임이 필요합니다
pers_health_visitCity_notEmpty=방문한 도시는 비워 둘 수 없습니다
pers_health_notAgree=계속하기 전에 계약에 동의해야 합니다
pers_personnal_list_manager=목록 관리자
pers_personnal_list=목록 라이브러리
pers_personnal_list_scheme=도메인 모드
pers_personnal_list_name=개인 목록 이름
pers_personnal_list_group_str_id=목록 그룹 ID
pers_personnal_list_personCount=사람 수
pers_personnal_list_tag=사용자 정의
pers_personnal_list_type=개인 목록 유형
pers_personnallist_addPerson_repo=저장소에 사람 추가
pers_personnallist_sendPersonnallist=분산 목록 라이브러리
pers_personnallist_sendPerson=발행자
pers_personnallist_notDel_existPerson=목록 라이브러리에는 개인이 포함되어 있으며 삭제할 수 없습니다.
pers_personnallist_peopleInRoster=목록 라이브러리에 여전히 사람이 있습니다
pers_personnallist_associationNotExist=주 장치와 목록 라이브러리 간의 연결이 존재하지 않습니다.
pers_personnal_list_person=개인 목록 사람
pers_personnal_list_dev=라이브러리 권한 나열
pers_personnal_list_addDev=장치 추가
pers_personnal_list_name_isExist=이름이 이미 존재합니다
pers_personnal_bannedList=금지 목록 라이브러리
pers_personnal_allowList=허용 목록 라이브러리
pers_personnal_redList=레드리스트 라이브러리
pers_personnal_attGroup=출석 그룹
pers_personnal_passList=패스 목록
pers_personnal_banList=금지 목록
pers_personnal_visPassList=게스트 패스 목록
pers_personnal_visBanList=방문객 금지 목록
pers_personnal_databaseHasBeenDistributed=선택 한 리스트 라 이브 러 리 가 이미 발송 되 었 으 므 로 삭제 할 수 없습니다.
pers_personnel_sendError_dueTo=배송 실패 실패 이유:
pers_personnel_sendError_reson={0}이(가) 금지 목록에 있습니다. 삭제하고 추가하세요.
pers_examplePic_Tip=다음 요구 사항을 충족해야 합니다.
pers_examplePic_Tip1=1. 배경색은 순백색이고 인원은 어두운 옷을 입습니다.
pers_examplePic_Tip2=2. 전자 사진은 JPG, PNG, JPEG 파일 형식이며 권장 픽셀 범위: 480*640 < 픽셀 < 1080*1920;
pers_examplePic_Tip3=3. 전자 사진의 인물 사진은 눈을 뜨고 정면을 바라보고 동공이 선명하게 보이는지 확인해야 합니다.
pers_examplePic_Tip4=4. 전자 사진의 초상화는 중립적인 표정이어야 하고 웃을 수 있지만 치아를 보여서는 안 됩니다.
pers_examplePic_Tip5=5. 전자 사진의 인물 사진은 선명하고 자연스러운 색상, 풍부한 레이어 및 눈에 띄는 왜곡이 없어야 합니다. 인물 얼굴이나 배경에 그림자, 하이라이트 또는 반사가 없으며 대비와 밝기가 적절합니다.
pers_examplePic_description=올바른 예
pers_examplePic_error=오류 예:
pers_examplePic_error1=과장된 표현(과도한 미소)
pers_examplePic_error2=조명이 너무 어둡습니다
pers_examplePic_error3=얼굴이 너무 작음(해상도가 너무 작음)
pers_applogin_enabled=app 로그인 사용
pers_applogin_disable=app 로그인 사용 안 함
pers_applogin_status=app 로그인 활성화 상태