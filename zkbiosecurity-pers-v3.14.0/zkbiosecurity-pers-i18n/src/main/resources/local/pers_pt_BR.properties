#pers common.
#here other module can also use the label from pers.
pers_module=Pessoal
pers_common_addPerson=Adicionar <PERSON>essoa
pers_common_delPerson=Excluir Pessoa
pers_common_personCount=Quantidade de Pessoas
pers_common_browsePerson=Procurar Pessoas
#左侧菜单
pers_person_manager=Gestão Pessoal
pers_person=Pessoa
pers_department=Departamento
pers_leave=Pessoal Dispensado
pers_tempPerson=Pendente de Revisão
pers_attribute=Campos Personalizados
pers_card_manager=Gestão de Cartões
pers_card=Cartão
pers_card_issue=Registro de Cartão Emitido
pers_wiegandFmt=Formato Wiegand
pers_position=Cargo
#人员
pers_person_female=Feminino
pers_person_male=Masculino
pers_person_pin=ID Pessoal
pers_person_departmentChange=Ajustar Departamento
pers_personDepartment_changeLevel=Permissão de troca de departamento
pers_person_gender=Gênero
pers_person_detailInfo=Outras informações
pers_person_accSet=Controle de Acesso
pers_person_accSetting=Configuração do Controle de Acesso
pers_person_attSet=Controle de Presença
pers_person_eleSet=Controle de Elevador
pers_person_eleSetting=Configuração do Controle de Elevador
pers_person_parkSet=Registro de Placa
pers_person_pidSet=Certificado Pessoal
pers_person_insSet=FaceKiosk
pers_person_aiSet=Intelecto Facial
pers_person_payrollSet=Configuração Salarial
pers_person_psgSet=Configuração de Passagem
pers_person_lockerSet=Configuração do armário
pers_person_sisSet=Configuração da verificação de segurança
pers_person_vdbSet=Configuração visual do intercomunicador
pers_person_firstName=Nome
pers_person_lastName=Sobrenome
pers_person_name=Nome
pers_person_wholeName=Nome Completo
pers_person_fullName=Nome do Meio
pers_person_cardNum=Número de Cartões Retidos
pers_person_deptNum=Número de departamentos envolvidos
pers_person_dataCount=Estatísticas
pers_person_regFinger=Impressão Digital
pers_person_reg=Registro
pers_person_password=Senha
pers_person_personDate=Data de Contratação
pers_person_birthday=Data de Nascimento
pers_person_mobilePhone=Celular
pers_person_personDevAuth=Permissões da Pessoa e Dispositivo
pers_person_email=Email
pers_person_browse=Navegar
pers_person_authGroup=Níveis de Acesso
pers_person_setEffective=Definir Tempo Efetivo
pers_person_attArea=Área de Presença
pers_person_isAtt=Cálculo de Presença
pers_person_officialStaff=Equipe Oficial
pers_person_probationStaff=Equipe de Experiência
pers_person_identity_category=Tipo de Pessoal
pers_person_devOpAuth=Função no Dispositivo
pers_person_msg1=O número atual do cartão e o código do centro devem ser preenchidos ao mesmo tempo!
pers_person_msg2=Insira um de 3-4 dígitos.
pers_person_msg3=Erro de formato!
pers_person_imgPixel=(Tamanho ideal 120*140).
pers_person_cardLengthDigit=Por favor insira o número.
pers_person_cardLengthHexadecimal=Insira números ou letras.
pers_person_to=Para
pers_person_templateCount=Quantid. Impressões Digitais
pers_person_biotemplateCount=Quantidade de Modelos Biológicos
pers_person_regFace=Face
pers_person_regVein=Veias do Dedo
pers_person_faceTemplateCount=Quantidade de Faces
pers_person_VeinTemplateCount=Quantidade da Veias do dedo
pers_person_palmTemplateCount=Quantidade de Palmas
pers_person_cropFace=Foto
pers_person_cropFaceCount=Quantid. Imagens de Face
pers_person_faceBiodataCount=Quantidade de face visível
pers_person_irisCount=Número de íris
pers_person_batchToDept=Novo Departamento
pers_person_changeReason=Motivo da Transferência
pers_person_selectedPerson=Pessoa Selecionada
pers_person_duressPwdError=Senha Repetida
pers_person_completeDelPerson=Excluir
pers_person_recover=Recuperar
pers_person_nameNoComma=Não deve conter vírgula.
pers_person_firstNameNotEmpty=O nome não deve estar vazio.
pers_person_lastNameNotEmpty=O sobrenome não deve estar vazio.
pers_person_mobilePhoneValidate=Insira um número de celular válido.
pers_person_phoneNumberValidate=Insira um número de telefone válido.
pers_person_postcodeValidate=Insira um CEP válido.
pers_person_idCardValidate=Insira um número de carteira de identidade válido.
pers_person_pwdOnlyLetterNum=A senha pode conter apenas letras ou números.
pers_person_certNumOnlyLetterNum=O Número do Certificado pode conter apenas letras ou números.
pers_person_oldDeptEqualsNewDept=O novo departamento que você ajustou não pode ser o mesmo do departamento original.
pers_person_disabled=Desativado
pers_person_emailError=Insira um endereço de e-mail válido.
pers_person_driverPrompt=Instale o driver do dispositivo! Clique em OK para baixar o driver.
pers_person_readIDCardFailed=A leitura falhou!
pers_person_cardPrompt=Mantenha o cartão ID na área...
pers_person_iDCardReadOpenFailed=Nenhum leitor de cartão ID foi detectado!
pers_person_iDCardNotFound=Cartão ID não encontrado, tente novamente!
pers_person_nameValid=Suporte a chinês, inglês, números, '-', '_'.
pers_person_nameValidForEN=Suporte a inglês, números, '-', '_', '.'.
pers_person_pinPrompt=Insira os caracteres do alfabeto inglês, números.
pers_person_pinSet=Configuração do ID Pessoal
pers_person_supportLetter=Alfanumérico
pers_person_cardsSupport=Múltiplos Cartões por Pessoa
pers_person_SupportDefault=Criação Automática de ID Pessoal
pers_person_noSpecialChar=Você não pode inserir caracteres especiais!
pers_person_pinInteger=Insira os dígitos.
pers_pin_noSpecialChar=O número da pessoa não deve conter caracteres especiais!
pers_op_capture=Capturar
pers_person_cardDuress=Número de Cartão Repetido
pers_person_pwdException=Exceção de Senha
pers_person_systemCheckTip=Verifique as informações de pessoas para obter o formato adequado!
pers_person_immeHandle=Gerencie Imediatamente
pers_person_cardSet=Configuração do Cartão
pers_person_tempPersonSet=Configurações de Pesoas Pendentes de Revisão
pers_person_cardsReadMode=Modo de Leitura de Cartão
pers_person_cardsReadModeReadHead=Lido Pelo Controlador
pers_person_cardsReadModeID180=Lido pelo ID180
pers_person_IDReadMode=Modo de Leitura do Cartão ID
pers_person_IDReadModeIDCardReader=Leitor de Cartão ID
pers_person_IDReadModeTcpReadHead=Leitor TCP/IP
pers_person_physicalNo=Número do Cartão Físico do Cartão ID
pers_person_physicalNoToCardNo=Usar o número do Cartão Físico do Cartão ID
pers_person_ReadIDCard=Ler o Cartão ID
pers_person_templateBioTypeNumber=Número do tipo de modelo biométrico
pers_person_templateValidType=Validade do Modelo Biométrico
pers_person_templateBioType=Tipo de Modelo Biométrico
pers_person_templateVersion=Versão do Modelo Biométrico
pers_person_template=Modelo Biométrico
pers_person_templateNo=Nº do Modelo Biométrico
pers_person_templateNoIndex=Índice de Modelos Biométricos
pers_person_templateDataUpdate=Atualizar dados quando existe um modelo biométrico:
pers_person_templateValidTypeNoNull=A validade do modelo biométrico não deve estar vazia!
pers_person_templateBioTypeNoNull=ID Pessoal: {0}, O tipo de modelo biométrico não deve estar vazio!
pers_person_templateVersionNoNull=ID Pessoal: {0}, A versão de modelo biométrico não deve estar vazio!
pers_person_templateNoNull=ID Pessoal: {0}, O modelo biométrico não deve estar vazio!
pers_person_templateNoNoNull=ID Pessoal: {0}, O número do modelo biométrico não deve estar vazio!
pers_person_templateNoIndexNoNull=ID Pessoal: {0}, O índice do modelo biométrico não deve estar vazio!
pers_person_templateError=ID Pessoal: {0}, Modelo biométrico incorreto!
pers_person_bioDuress=Coação
pers_person_universal=Comum
pers_person_voice=Detecção de Voz
pers_person_iris=Íris
pers_person_retina=Retina
pers_person_palmPrints=Palma
pers_person_metacarpalVein=Veias da Palma
pers_person_visibleFace=Face Visível
pers_person_pinError=O ID Pessoal {0} não existe, não é possível processar esses dados!
pers_person_pinException=Exceção de ID Pessoal
pers_person_pinAutoIncrement=Incremento Automático
pers_person_resetSelfPwd=Resetar Senha do APP
pers_person_picMaxSize=A resolução da imagem é muito alta e a resolução é recomendada é abaixo de {0}.
pers_person_picMinSize=A resolução da imagem é muito baixa e a resolução é recomendada é acima de {0}.
pers_person_cropFaceShow=Ver Imagem da Face
pers_person_faceNoFound=Face não reconhecido
pers_person_biometrics=Tipo de Biometria
pers_person_photo=Fotos pessoais
pers_person_visibleFaceTemplate=Modelo de Rosto Visível
pers_person_infraredFaceTemplate=Modelo Estático de Face Infravermelha
pers_person_delBioTemplate=Excluir dados biométricos
pers_person_delBioTemplateSelect=Selecione o modelo biológico a ser excluído!
pers_person_infraredFace=Face infravermelho
pers_person_notCard=Não inclui cartão
pers_person_notRegFinger=Não inclui impressão digital
pers_person_notMetacarpalVein=Não inclui veia da palma
pers_person_notRegVein=Não inclui veias dos dedos
pers_person_notIris=Excluindo íris
pers_person_notInfraredFaceTemplate=Não inclui modelo de rosto próximo ao infravermelho
pers_person_notVisibleFaceTemplate=Não inclui modelo de rosto claro visível
pers_person_notVisibleFacePhoto=Não inclui fotos de rosto visíveis
pers_person_visibleFacePhoto=Foto de rosto visível
pers_person_change=Ajustes de Pessoal
pers_person_cropFaceUsePhoto=Você quer exibir a foto de comparação como um avatar?
pers_person_photoUseCropFace=Você quer usar o avatar para gerar uma foto de comparação?
pers_person_selectCamera=Seleccionar a câmara de captura
pers_person_delCropFaceMsg=Não há fotos de comparação para excluir!
pers_person_disabledNotOp=A pessoa foi deficiente e não pode operar!
pers_person_visiblePalm=Palma clara visível
pers_person_notVisiblePalm=Não inclui a palma clara visível
pers_person_selectDisabledNotOp=Há pessoas deficientes no pessoal selecionado, incapazes de operar!
pers_person_photoUseCropFaceAndTempalte=Você quer usar avatares para gerar fotos de comparação e modelos faciais?
pers_person_photoUseTempalte=Você quer usar fotos para gerar modelos de rosto?
pers_person_createCropFace=Gerar fotos de comparação
pers_person_createFaceTempalte=Gerar modelos faciais
pers_person_faceTempalte=Modelos faciais
pers_person_extractFaceTemplate=A extrair modelos faciais
pers_person_createSuccess=Gerado com sucesso
pers_person_createFail=A geração falhou
pers_person_serverConnectWarn=Endereço do servidor, nome de usuário e senha não podem estar vazios!
pers_person_serverOffline=Servidor de extracção de modelos faciais offline
pers_person_faceTemplateError1=A detecção da cara falhou
pers_person_faceTemplateError2=Oclusão facial
pers_person_faceTemplateError3=Falta de clareza
pers_person_faceTemplateError4=Ângulo da face demasiado grande
pers_person_faceTemplateError5=A detecção ao vivo falhou
pers_person_faceTemplateError6=A extracção do modelo facial falhou
pers_person_cropFaceNoExist=A foto de comparação não existe
pers_person_disableFaceTemplate=A função de extração de modelo facial não está habilitada, incapaz de extrair o modelo!
pers_person_cropFacePhoto=Foto de Comparação de Rostos
pers_person_vislightPalmPhoto=Foto de Comparação da Palma
pers_person_serverOfflineWarn=O servidor de extração de template facial está offline e não pode ativar esse recurso!
pers_person_serverConnectInfo=Por favor, teste se o servidor de extração de modelo facial está online?
pers_person_notModified=O número de telefone celular não pode ser modificado.
pers_person_syncAcms=Sincronizar o pessoal com ACMS
pers_person_startUpdate=Comece a atualizar as informações dos funcionários.
pers_person_updateFailed=A atualização das informações dos funcionários falhou!
#控制器发卡
pers_person_readCard=Emitir Cartão do Dispositivo
pers_person_stopRead=Parar emissão
pers_person_chooseDoor=Escolha a Porta
pers_person_readCarding=Cartão emissor, tente novamente mais tarde!
#抓拍照片
pers_capture_catchPhoto=Foto Capturada
pers_capture_preview=Pré-visualização
#部门
pers_dept_entity=Departamento
pers_dept_deptNo=Número de Departamento
pers_dept_deptName=Nome do Departamento
pers_dept_parentDeptNo=Número do Departamento Principal
pers_dept_parentDeptName=Nome do Departamento Principal
pers_dept_parentDept=Departamento Principal
pers_dept_note=Se o novo departamento não aparecer na lista, entre em contato com o administrador para autorizar novamente o usuário a editar o departamento!
pers_dept_exit=Contém
pers_dept_auth=Usuário do sistema vinculado
pers_dept_parentMenuMsg=O departamento superior não pode ser definido da mesma forma que um departamento inferior.
pers_dept_initDept=Geral
pers_dept_deptMarket=Departamento de Marketing
pers_dept_deptRD=Departamento de Desenvolvimento
pers_dept_deptFinancial=Departamento Financeiro
pers_dept_nameNoSpace=O Nome do Departamento não pode começar ou terminar com espaços.
pers_dept_nameExist=O Nome do Departamento já existe
pers_dept_changeLevel=Se deseja mudar para o nível deste departamento
pers_dept_noSpecialChar=O número do departamento não pode conter caracteres especiais!
pers_dept_NameNoSpecialChar=O nome do departamento não pode conter caracteres especiais!
pers_dept_noModifiedParent=O departamento superior não pode ser modificado!
#职位
pers_position_entity=Cargo
pers_position_code=Número do Cargo
pers_position_name=Nome do Cargo
pers_position_notExist=O Cargo não existe!
pers_position_sortNo=Ordenar
pers_position_parentName=Cargo Principal
pers_position_parentCode=Número do Cargo Principal
pers_position_batchToPosition=Novo Cargo
pers_position_nameExist=O nome deste Cargo já existe
pers_position_change=Mudança de Cargo
pers_position_parentMenuMsg=O Cargo principal não pode ser definida como uma subposição.
pers_position_nameNoSpace=O nome do Cargo não pode começar ou terminar com espaços.
pers_position_existSub={0} :Contém subposições e não podem ser excluídas
pers_position_existPerson={0} :O pessoal não pode ser excluído
pers_position_importTemplate=Modelo de importação de Cargo
pers_position_downloadTemplate=Baixar e importar modelo
pers_position_codeNotEmpty=O número do Cargo não pode estar vazio
pers_position_nameNotEmpty=O nome do Cargo não pode estar vazio
pers_position_nameNoSpecialChar=O nome do Cargo {0} não pode conter caracteres especiais!
pers_position_noSpecialChar=O número do Cargo {0} não pode ser um caractere especial!
pers_position_codeLength=O número do Cargo {0} excede 30 dígitos
pers_position_nameLength=Dados cujo nome do Cargo {0} é maior que {1}
pers_position_codeExist=O número do Cargo {0} já existe
#证件
pers_cert_type=Tipo de Documento
pers_cert_number=Número do Documento
pers_cert_name=Nome do documento
pers_cert_numberExist=O número do documento já existe.
#导出
pers_export_allPersPerson=Todo o Pessoal
pers_export_curPersPerson=Pessoa Atual
pers_export_template=Exportar Modelo
pers_export_personInfo=Exportar Pessoal
pers_export_personInfoTemplate=Baixar Modelo de Importação de Pessoal
pers_export_personBioTemplate=Exportar Modelo Biométrico
pers_export_basicInfo=Informações Básica
pers_export_customAttr=Campos Personalizados
pers_export_templateComment=Nome do campo, chave primária? Único? Permitir valores nulos? ({0},{1},{2},{3})
pers_export_templateFileName=Modelo de Importação de Pessoal
pers_export_bioTemplateFileName=Modelo Biométrico de Pessoal
pers_export_deptInfo=Exportar Departamento
pers_export_deptTemplate=Baixar Modelo de Importação de Departamento
pers_export_deptTemplateFileName=Modelo de Importação de Departamento
pers_export_personPhoto=Exportar Foto Pessoal
pers_export_allPhotos=Todas as Fotos (selecione todas as pessoas)
pers_export_selectPhotoToExport=Selecione o ID inicial e o ID final para exportar as fotos pessoais.
pers_export_fromId=Do ID
pers_export_toId=Para
pers_export_certNumberComment=O tipo de certificado é necessário após preencher o número do certificado
pers_export_templateCommentName=Nome do campo:({0})
pers_export_dataExist=Certifique-se de que os dados importados já existem no sistema
pers_export_cardNoTip=Vários números de cartões separados por &
pers_carNumber_importTip=Número da placa do veículo (múltiplas placas& separadas)
#导入
pers_import_certNumberExist=O número do certificado {0} já existe.
pers_import_complete=Concluído
pers_import_password=Senha da Pessoa
pers_import_fail=A linha {0} falhou: {1}
pers_import_overData=Você está importando {0} pessoas; o sistema pode suportar apenas a importação de 30.000 pessoas!
pers_import_pinTooLong=O ID pessoal {0} é muito longo!
pers_import_pinExist=O ID pessoal {0} já existe!
pers_import_pinIsRepeat=O ID pessoal {0} está repetido!
pers_import_pinError=Erro do ID Pessoal {0}!
pers_import_pinSupportNumber=O número pessoal suporta apenas números! O número pessoal é:{0}
pers_import_pinNotSupportNonAlphabetic=O ID pessoal não suporta letras que não sejam o inglês! O número  pessoal é:{0}
pers_import_pinNotNull=O ID pessoal não deve ser composto só por zeros!
pers_import_pinStartWithZero=O ID pessoal não pode começar com zero!
pers_import_cardNoNotSupportLetter=O número do cartão não suporta letras!
pers_import_cardNoNotNull=O número do cartão não deve ser composto só por zeros!
pers_import_cardNoStartWithZero=O número do cartão não pode começar com zero!
pers_import_cardTooLong=O número do cartão {0} é muito longo!
pers_import_cardExist=O número do cartão {0} já existe!
pers_import_personPwdOnlyNumber=A senha pessoal suporta apenas dígitos (sem letras)!
pers_import_personPwdTooLong=A senha pessoal {0} é muito longa!
pers_import_personDuressPwd=A senha pessoal {0} é repetida!
pers_import_emailTooLong=O e-mail {0} é muito longo!
pers_import_nameTooLong=O nome {0} é muito longo!
pers_import_genderError=Erro de formato de gênero!
pers_import_personDateError=Erro no formato da data de contratação!
pers_import_createTimeError=Erro no formato de Criar hora!
pers_import_phoneError=Erro no formato do número de telefone!
pers_import_phoneTooLong=O tamanho do número de telefone não pode conter mais de 20 caracteres!
pers_import_emailError=Erro no formato do e-mail!
pers_import_birthdayError=Erro no formato da Data de Nascimento!
pers_import_nameError=O sobrenome e o nome não podem conter símbolos especiais! O número pessoal é:{0}
pers_import_firstnameError=O sobrenome ou o primeiro nome não podem conter ','!
pers_import_firstnameNotNull=O nome não pode estar vazio!
pers_import_dataCheck=A verificação de dados foi concluída
pers_import_dataSaveFail=Falha ao importar dados!
pers_import_allSucceed=Todos os dados foram importados com sucesso!
pers_import_result=Sucesso: {0}, Falha: {1}.
pers_import_result2=Resultado da Importação
pers_import_result3=Sucesso: {0}, Atualizado: {1}, Falha: {2}.
pers_import_notSupportFormat=Este formato não é suportado!
pers_import_selectCorrectFile=Por favor, selecione o arquivo correto.
pers_import_fileFormat=Formato do Arquivo
pers_import_targetFile=Arquivo de Destino
pers_import_startRow=Linhas de Início do Cabeçalho
pers_import_startRowNote=A primeira linha do formato de dados é o nome da tabela, a segunda linha é o cabeçalho, a terceira linha são os dados de importação, verifique o arquivo e depois importe.
pers_import_delimiter=Delimitador
pers_import_importingDataFields=Campos do CVSecurity
pers_import_dataSourceFields=Importando Campos de Dados
pers_import_total=Total
pers_import_dataUpdate=Atualize o ID Pessoal existente no sistema:
pers_import_dataIsNull=Não há dados no arquivo!
pers_import_deptNotExist=O departamento não existe!
pers_import_deptIsNotNull=O nome do departamento não deve estar vazio!
pers_import_pinNotEmpty=O ID pessoal não pode ficar em branco!
pers_import_nameNotEmpty=O nome do pessoal não pode estar vazio!
pers_import_siteCodeOnlyLetterNum=Erro no formato do código do centro.
pers_import_cardNoFormatErrors=O formato do número do cartão {0} está incorreto.
pers_import_cardsNotSupport=O recurso [Múltiplos Cartões por Pessoa] está desativado, não pode programar mais de um cartão por pessoa.
pers_import_personInfo=Importar Pessoal
pers_import_commentFormat=O formato do comentário está incorreto!
pers_import_noComment=Os dados na linha {0} e na coluna {1} não estão comentados.
pers_import_fieldRepeat=Os dados na linha {0} e na coluna {1}, o nome do campo no comentário estão repetidos: {2}
pers_import_primaryKey=Deve haver pelo menos um campo como chave primária.
pers_import_templateIsRepeat=ID Pessoal: Os dados do modelo biométricos para {0} estão duplicados!
pers_import_biologicalTemplate=Importar Modelo Biométrico
pers_import_uploadFileSuccess=Upload bem-sucedido, começando a analisar dados, aguarde...
pers_import_resolutionComplete=Os dados foram analisados, iniciando as atualizações do banco de dados.
pers_import_mustField=O arquivo de importação deve conter a coluna {0}.
pers_import_bioTemplateSuccess=Importar modelo biométrico, esses dados precisam ser sincronizados manualmente com o dispositivo de cada módulo de negócios.
pers_import_personPhoto=Importar Foto Pessoal
pers_import_opera_log=Log de operação
pers_import_error_log=Log de erro
pers_import_uploadFileSize=Faça o upload de um arquivo com um tamanho não superior a {0}!
pers_import_uploadFileSizeLimit=Para uma única importação, faça o upload de um arquivo com um tamanho não superior a 500M!
pers_import_type=Modo de importação
pers_import_photoType=Foto
pers_import_archiveType=Pacote compactado
pers_import_startUpload=Iniciar Envio
pers_import_addMore=Adicionar mais
pers_import_photoQuality=Qualidade da Foto
pers_import_original=Original
pers_import_adaptive=Adaptativa
pers_import_adaptiveSize=(Tamanho 480 * 640)
pers_import_totalNumber=Total
pers_import_uploadTip=(Não exclua a foto durante o upload)
pers_import_addPhotoTip=Adicione as fotos a serem enviadas!
pers_import_selectPhotoTip=Selecione a foto que você deseja enviar!
pers_import_uploadResult=Carregar Resultado da Foto
pers_import_pleaseSelectPhoto=Selecione a Foto
pers_import_multipleSelectTip=Pressione Ctrl para fazer várias seleções
pers_import_replacePhotoTip=A foto selecionada já está na lista de upload, deseja substituí-la?
pers_import_photoNamePinNotCorrespond=Nome da foto e incompatibilidade de identificação pessoal
pers_import_photoFormatRequirement=Nomeie a foto com o ID do funcionário. O formato correto é JPG/PNG. Verifique se o nome da foto não contém caracteres especiais.
pers_import_filterTip=Algumas das fotos selecionadas não podem ser visualizadas e pode ser devido os seguintes motivos:
pers_import_photoContainSpecialCharacters=O nome da foto possui caracteres especiais.
pers_import_photoFormatError=O formato da foto está incorreto.
pers_import_photoSelectNumber=Não escolha mais de 3000 fotos!
pers_import_photoSelectNumberLimit=Não escolha mais do que {0} imagens!
pers_import_fileMaxSize=A imagem é muito grande; faça o upload de um arquivo de imagem menor que 5M.
pers_import_notUploadPhotoNumber=Não é possível carregar mais de 3000 imagens!
pers_import_zipFileNotPhoto=Não há foto da pessoa no arquivo zip, selecione novamente e importe!
pers_import_personPlateRepeat=A placa do veículo {0} está duplicada!
pers_import_filePlateRepeat=O arquivo dentro da placa do veículo {0} está duplicada!
pers_import_personPlateFormat=O formato da placa do veículo {0} está incorreto!
pers_import_personPlateMax=O número de placas de veículos pessoais excede o máximo de 6!
pers_import_cropFaceFail=Falha ao gerar foto de comparação!
pers_import_pinLeaved=ID Pessoal: {0} saiu.
pers_import_exceedLicense=Não são permitidas importações para o número de pessoas que excedem a licença do software.
pers_import_bioTemplateNotNull=O ID do pessoal, o Tipo de Modelo Biométrico, o ID ou o Índice e o Conteúdo, Versão não podem estar vazios!
pers_import_certTypeNotNull=O número pessoal é: {0}. O tipo de documento não pode estar vazio
pers_import_certTypeNotExist=Número de pessoal é: {0} Tipo de documento não existe
pers_import_certNumNotNull=O número do pessoal é: {0} O número do certificado não pode estar vazio
pers_import_certNumberTooLong=Linha {0}: o número de identificação {1} é muito longo!
pers_import_idNumberErrors=Linha {0}: o número do cartão de identificação {1} está incorreto!
pers_import_emailErrors=Linha {0}: erro de formato do endereço de email {1}!
pers_import_emailIsExist=O endereço de email {0} já existe!
pers_import_emailIsRepeat=Linha {0}: o endereço de email interno do arquivo {1} foi repetido!
pers_import_fileMobilePhoneRepeat=Linha {0}: O número de telefone celular interno do arquivo {1} foi repetido!
pers_import_mobilePhoneErrors=Erro no formato do número do celular!
pers_import_hireDateError=O formato da data de entrada está incorreto!
pers_import_selectPhotoType=Seleccione por favor o tipo de fotografia importada!
pers_import_hireDateLaterCurrent=A data de aluguer não pode ser posterior à data actual!
pers_import_buildingNotExist=O edifício não existe!
pers_import_unitNotExist=A unidade não existe!
pers_import_vdbInfoFail=Não há nenhuma informação de unidade chamada {1} no edifício {0}!
pers_import_vdbBuildingFail=As informações de construção da unidade {0} não podem estar vazias!
pers_import_vdbRoomNoFail=O número do quarto deve ser um valor numérico maior que 0!
#人员离职
pers_person_leave=Demissão
pers_dimission_date=Data de Demissão
pers_dimission_type=Tipo de Demissão
pers_dimission_reason=Motivo da Demissão
pers_dimission_volutary=Redundância Voluntária
pers_dimission_dismiss=Demitido
pers_dimission_resignat=Pedido de Demissão
pers_dimission_shiftJob=Transferência
pers_dimission_leave=Trabalho mantido Sem Salário
pers_dimission_recovery=Reintegração
pers_dimission_sureToRecovery=Você tem certeza que deseja executar a operação de reintegração?
pers_dimission_backCard=O cartão foi devolvido?
pers_dimission_isForbidAction=Desativar direitos de controle de acesso imediatamente?
pers_dimission_writeInfomation=Insira as informações de demissão
pers_dimission_pinRetain=Manter o ID Pessoal do colaborador demitido?
pers_dimission_downloadTemplate=Baixar modelo de importação de desistência
pers_dimission_import=Dimensões de importação
pers_dimission_importTemplate=Modelo de importação de dimensão
pers_dimission_date_noNull=A data de dimissão não pode estar vazia
pers_dimission_leaveType_noExist=Tipo de dimissão não existe
pers_dimission_dateFormat=Campo obrigatório, o formato da hora é aaaa-MM-dd, como: 2020-07-22
pers_dimission_leaveType=Campo obrigatório, como: Redundância Voluntária, Demitido, Pedido de Demissão, Transferência
pers_dimission_forbidden=Desativar
pers_dimission_leaveType_noNull=O tipo de licença não pode estar vazio!
pers_dimission_person_noExist=A pessoa dispensada não existe!
pers_dimission_date_error=Data de demissão não preenchida ou formato incorreto
#临时人员
pers_tempPerson_audit=Revisar
pers_tempPerson_view=Visualizar
pers_tempPerson_waitReview=Aguardando a revisão do administrador
#卡--肖小军协助郑周武将下面的部分重新整理一遍。命名等。card/issueCard/lossCard/revertCard/
#卡
pers_card_cardNo=Número do Cartão
pers_card_state=Status do Cartão
pers_card_effect=Efetivo
pers_card_disabled=Inválido
pers_card_past=Expirado
pers_card_back=Cartão Devolvido
pers_card_change=Troca de Cartão
pers_card_note=O número do cartão já existe.
pers_card_numTooBig=O tamanho do número do cartão original é muito longo.
pers_issueCard_entity=Emitir Cartão
pers_issueCard_operator=Operador
pers_issueCard_operate=Ação
pers_issueCard_note=Operação de emissão cartão para dados pessoais registrados mas não para registro de cartão de conta da equipe!
pers_issueCard_date=Data de Emissão do Cartão
pers_issueCard_changeTime=Alterar Hora
pers_issueCard_cardValidate=Não é permitido espaço.
pers_issueCard_cardEmptyNote=O cartão não pode estar vazio.
pers_issueCard_cardHasBeenIssued=Este cartão foi emitido!
pers_issueCard_noCardPerson=Pessoa não emitida
pers_issueCard_waitPerson=Pessoa atual emitida
pers_issueCard_mc5000=Emissão de cartões MC5000
pers_batchIssCard_entity=Emissão de Cartão em Lote
pers_batchIssCard_startPersNo=ID Pessoal Inicial
pers_batchIssCard_endPersNo=ID Pessoal Final
pers_batchIssCard_issCardNum=Número de Cartões Emitidos
pers_batchIssCard_notIssCardNum=Número de Pessoas Sem Cartão Emitido
pers_batchIssCard_generateList=Gerar Lista
pers_batchIssCard_startRead=Iniciar leitura
pers_batchIssCard_swipCard=Posição de leitura do cartão
pers_batchIssCard_sendCard=Dispositivo
pers_batchIssCard_dispenCardIss=Leitor USB
pers_batchIssCard_usbEncoder=Codificador USB
pers_batchIssCard_note=O ID Pessoal suporta apenas valores de entrada e mostra apenas pessoas sem cartão emitido (máx. 300)!
pers_batchIssCard_startPinEmpty=O ID Pessoal inicial não pode estar vazio!
pers_batchIssCard_endPinEmpty=O ID Pessoal final não pode estar vazio!
pers_batchIssCard_startPinLargeThanEndPin=O ID Pessoal Inicial não pode ser maior que o ID Pessoal Final!
pers_batchIssCard_numberParagraphNoPerson=A pessoa sem cartão emitido não existe no número do segmento de identificação do início ao fim!
pers_batchIssCard_inputCardNum=Inserir Número do Cartão
pers_batchIssCard_cardLetter=O cartão não suporta letras!
pers_batchIssCard_cardNoTooLong=O tamanho do número do cartão é muito longo!
pers_batchIssCard_issueWay=Método de Cadastro no cartão
pers_batchIssCard_noPersonList=Lista de pessoal ainda não gerada.
pers_batchIssCard_startReadCard=Iniciar Leitura do Cartão
pers_batchIssCard_swipePosition=Posição de Leitura
pers_batchIssCard_chooseSwipePosition=Por favor, escolha a posição de leitura
pers_batchIssCard_readCardTip=O dispositivo só lê o cartão não registrado quando o método de emissão for um leitor selecionado.
pers_batchIssCard_notIssCardNo=Número de cartões não emitidos
pers_batchIssCard_totalNumOfCards=Número total de cartões
pers_batchIssCard_acms=Emissão de Cartões ACMS
pers_lossCard_entity=Relato de Cartão Perdido
pers_lossCard_lost=Este cartão foi relatado, não é possível repetir a operação!
pers_losscard_note2=Depois de gravar o cartão de gerenciamento, você deve passar esse cartão no leitor do elevador para garantir que os cartões parem de funcionar no dispositivo.
pers_revertCard_entity=Reativar o Cartão Perdido
pers_revertCard_setReport=Relate a perda do cartão primeiro!
pers_revertcard_note2=Depois de gravar o cartão de gerenciamento, você deve passar esse cartão no leitor do elevador para garantir que os cartões podem ser usados novamente.
pers_issueCard_success=Cartão emitido com sucesso!
pers_issueCard_error=Falha na emissão do cartão!
pers_cardData_error=Exceção de formato de data de leitura do cartão!
pers_analysis_error=Exceção de análise de dados do cartão!
pers_cardOperation_error=Exceção de operação do cartão!
pers_cardPacket_error=Exceção do pacote de comandos da operação do cartão!
pers_card_write=Gravar Cartão
pers_card_init=Inicializar Cartão
pers_card_loss=Cartão Perdido
pers_card_revert=Reverter Cartão
pers_card_writeMgr=Gravar Cartão de Gerenciamento
pers_initCard_tip=Após a inicialização, o cartão se tornará um cartão em branco!
pers_initCard_prepare=Pronto para inicializar o cartão...
pers_initCard_process=Inicializando o cartão...
pers_initCard_success=Cartão inicializado com sucesso
pers_mgrCard_prepare=Preparar para gravar dados do cartão de gerenciamento...
pers_mgrCard_process=Gravando dados do cartão de gerenciamento...
pers_mgrCard_success=Gravação do cartão de gerenciamento realizada com sucesso
pers_userCard_prepare=Preparar para gravar um cartão de usuário...
pers_userCard_process=Gravando dados do cartão de usuário...
pers_userCard_success=Gravação do cartão do usuário realizada com sucesso
pers_userCard_tip=Defina a hora de início e de fim na página de edição de pessoa e depois realize a operação de gravação de cartão.
pers_userCard_tip2=Os dados de permissões estão vazios, não é possível gravar os cartões.
pers_userCard_tip3=Grupo de permissão associado ao dispositivo em mais de duas unidades, há perda de dados.
pers_writeCard_tip=Verifique se você conectou o codificador e instalou o driver, e coloque o cartão no codificador.
pers_writeMgrCard_tip=A quantidade de cartões perdidos e revertidos não pode ser maior que 18
pers_writeMgrCard_tip2=O número do cartão perdido e do cartão reverso:
pers_card_writeToMgr=Gravação no cartão de gerenciamento
pers_card_hex=Exibição do Formato do Cartão
pers_card_decimal=Decimal
pers_card_Hexadecimal=Hexadecimal
pers_card_IssuedCommandFail=Falha no Comando de Emissão:
pers_card_multiCard=Mais Cartões
pers_card_deputyCard=Cartão Secundário
pers_card_deputyCardValid=Por favor, insira o cartão principal primeiro!
pers_card_writePinFormat=O sistema pode associar apenas IDs pessoais sem letras no cartão!
pers_card_notMoreThanSixteen=A quantidade de cartões secundários não pode ser maior que 16.
pers_card_notDelAll=Não é possível remover todos os cartões secundários.
pers_card_maxCard=O número de cartões não pode exceder {0}.
pers_card_posUseCardNo=O cartão principal da pessoa está sendo usado pelo módulo de cliente. Vá ao módulo do cliente para executar a operação de remoção do cartão!
pers_card_delFirst=Por favor, apague o número do cartão emitido antes de emitir o cartão!
pers_card_disablePersonWarn=O número do cartão selecionado contém pessoas com deficiência e não pode ser operado!
#韦根格式
#wiegand format
pers_wiegandFmt_siteCode=Código do Centro
pers_wiegandFmt_wiegandMode=Modo
pers_wiegandFmt_wiegandModeOne=Modo um
pers_wiegandFmt_wiegandModeTwo=Modo Dois
pers_wiegandFmt_isDefaultFmt=Auto
pers_wgFmt_entity=Formato Wiegand
pers_wgFmt_in=Formato de Entrada Wiegand
pers_wgFmt_out=Formato de Saída Wiegand
pers_wgFmt_inType=Tipo de Entrada Wiegand
pers_wgFmt_outType=Tipo de Saída Wiegand
pers_wgFmt_wg=Formato Wiegand
pers_wgFmt_totalBit=Total de Bits
pers_wgFmt_oddPch=Verificação de Paridade Ímpar (o)
pers_wgFmt_evenPck=Verificação de Paridade Par (e)
pers_wgFmt_CID=CID (c)
pers_wgFmt_facilityCode=Código da Instalação (f)
pers_wgFmt_siteCode=Código do Centro (s) 
pers_wgFmt_manufactoryCode=Código do Fabricante (m)
pers_wgFmt_firstParity=Primeira Verificação de Paridade (p)
pers_wgFmt_secondParity=Segunda Verificação de Paridade (p)
pers_wgFmt_cardFmt=Formato de Verificação do Cartão
pers_wgFmt_parityFmt=Formato de Verificação de Paridade
pers_wgFmt_startBit=Bits Iniciais
pers_wgFmt_test=Teste de Formato de Cartão
pers_wgFmt_error=Erro no Formato do Cartão!
pers_wgFmt_verify1=O total de bits não deve ser superior a 80!
pers_wgFmt_verify2=O tamanho do formato do cartão de verificação deve ser igual ao número total de bits!
pers_wgFmt_verify3=O tamanho do formato de paridade deve ser igual ao número total de bits!
pers_wgFmt_verify4=Os dados inicializados não podem ser excluídos.
pers_wgFmt_verify5=O formato do cartão está sendo usado, não é possível excluir!
pers_wgFmt_verify6=O primeiro bit de paridade não pode ser maior que o número total de bits!
pers_wgFmt_verify7=O segundo bit de paridade não pode ser maior que o número total de bits!
pers_wgFmt_verify8=O formato inicial e o comprimento máximo estão incorretos!
pers_wgFmt_verify9=O tamanho do formato de verificação do cartão não pode ser maior que o número total de bits!
pers_wgFmt_verify10=Função de dígito de verificação do cartão não pode ser um X!
pers_wgFmt_verify11=O código do centro excede o intervalo definido!
pers_wgFmt_verify=Verificar
pers_wgFmt_unverify=Não Verificar
pers_wgFmt_atLeastDefaultFmt=Mantenha pelo menos um formato de cartão que corresponda automaticamente!
pers_wgFmt_defaultFmtError1=Existem outros formatos de cartão com os mesmos bits de número de cartão, que não podem ser configurados para corresponder automaticamente ao formato do cartão!
pers_wgFmt_defaultFmtError2=Presença do mesmo formato de cartão na correspondência automática, a operação falhou!
pers_wgFmt_cardFormats=Formatos dos Cartões
pers_wgFmt_cardFormatTesting=Teste de Formatos dos Cartões
pers_wgFmt_checkIsUsed=O formato do cartão é usado em {0} e não pode ser excluído!
pers_wgFmt_supportDigitsNumber=Insira o número de dígitos suportados pelo dispositivo
#选人控件
pers_widget_selectPerson=Selecionar pessoal
pers_widget_searchType1=Pesquisa
pers_widget_searchType2=Departamento
pers_widget_deptHint=Observação: Importando todo o pessoal dos departamentos selecionados
pers_widget_noPerson=Nenhum pessoal foi selecionado.
pers_widget_noDept=Por favor, selecione um departamento.
pers_widget_noDeptPerson=Não há nenhuma pessoa no departamento selecionado, selecione novamente!
#人员属性
pers_person_carPlate=Placa do Veículo
pers_person_socialSecurity=Número da Segurança Social
pers_person_msg4=O tamanho máximo não deve exceder 20!
pers_person_msg5=O nome não pode conter mais de 50 caracteres!
pers_person_type=Tipo de Pessoa
pers_person_reseCode=Senha de Login
pers_person_IsSendMail=Notificação por Email
pers_person_inactive=Inativo
pers_person_active=Ativo
pers_person_employee=Colaborador
pers_person_isSendMailMsg=Para usar o recurso [Notificação de eventos], você deve inserir o e-mail primeiro.
pers_person_createTime=Criar Horário
pers_person_pinFirstValid=O primeiro caractere do número pessoal não pode ser 8 ou 9.
pers_person_attrValueValid=O valor do campo não pode ser repetido.
pers_person_attrValueDelimiterValid=O separador deve estar no meio.
pers_person_isSendSMS=Notificação por SMS
pers_person_building=Nome do Edifício
pers_person_unitName=Nome da Unidade
pers_person_roomNo=Número do quarto
#动态属性
pers_attr_emp_type=Tipo de Colaborador
pers_attr_street=Rua
pers_attr_nation=Nação
pers_attr_office_address=Endereço Comercial
pers_attr_postcode=CEP
pers_attr_office_phone=Telefone Comercial
pers_attr_home_phone=Telefone Residencial
pers_attr_job_title=Titulo do Trabalho
pers_attr_birthplace=Naturalidade
pers_attr_polit_status=Status Político
pers_attr_country=País
pers_attr_home_address=Endereço Residencial
pers_attr_hire_type=Tipo de Contratação
pers_attr_inContract=Colaborador Contratado
pers_attr_outContract=Colaborador Sem Contrato
#属性自定义
pers_attribute_attrName=Nome Exibido
pers_attribute_attrValue=Valor do Atributo
pers_attribute_controlType=Tipo de Entrada
pers_attribute_positionX=Linha
pers_attribute_positionY=Coluna
pers_attribute_showTable=Exibir Lista de Pessoas
pers_attrDefini_deletemsg=Esta propriedade foi usada. Tem certeza de que deseja excluí-la?
pers_attrDefini_reserved=Nome do campo reservado pelo sistema.
pers_attrDefini_msg1=O tamanho máximo não pode ser maior que 30!
pers_attrDefini_msg2=As linhas da coluna já existem, altere para outro local!
pers_attrDefini_attrValue_split=Usando um  ';'  como delimitador.
pers_attrDefini_attrName=Nome do Atributo
pers_attrDefini_sql=SQL
pers_attrDefini_attrId=ID do Atributo
pers_attrDefini_select=Lista Suspensa
pers_attrDefini_check=Múltipla Escolha
pers_attrDefini_radio=Escolha Única
pers_attrDefini_text=Texto
pers_attrDefini_maxCol=Não mais que 2 colunas.
pers_attrDefini_maxLimit=Os atributos personalizados alcançaram a quantidade máxima!
pers_attrDefini_modControlType=Alterar o tipo de entrada limpará os dados atuais do campo para todas as pessoas do sistema, deseja continuar?
#leavePerson
pers_leavePerson_reinstated=Reintegração
#opExample
pers_example_newRecode=Aquisição de novos registros
pers_example_allRecode=Acesso a todos os registros
pers_custField_StatisticalType=Tipo de Estatística
#人员参数修改
pers_param_isAudit=Revisão Automática
pers_param_donotChangePin=Existem ID de pessoas contendo letra (s), por isso não é possível desativar o recurso [Suporte a letras e números].
pers_param_hexChangeWarn=O sistema atual já possui os números do cartão, não é possível alterar o modo de exibição do formato do cartão.
pers_param_cardsChangeWarn=Existem pessoas com mais de um cartão no sistema, não é possível desativar o recurso [Múltiplos Cartões por Pessoa].
pers_param_maxPinLength=O tamanho do novo ID Pessoal não pode ser menor que o comprimento do ID Pessoal existente no sistema.
pers_param_pinBeyondDevLength=O tamanho máximo do ID Pessoal suportado pelo dispositivo no sistema é {0}, insira um número inteiro menor que {1}.
pers_param_cardBeyondDevLength=O tamanho máximo do número do cartão suportado pelo dispositivo no sistema é {0}, insira um número inteiro menor que {1}.
pers_param_checkIsExistNoAudit=Existem registradores não revisados no sistema atual e não podem ser modificados pela revisão automática!
pers_param_noSupportPinLetter=Existem dispositivos no sistema que não suportam o ID Pessoal com letras, não é possível ativar o recurso [ID pode conter letras e números].
pers_param_changePinLettersTip=O número pessoal suporta o incremento automático, não é possível modificar os modos de número do pessoal
pers_param_changePinIncrementTip=O suporte ao número da pessoa inclui letras, não é possível alterar o modo do número do pessoal
pers_param_qrCode=Código QR da empresa
pers_param_employeeRegistrar=Ativar registro de colaborador na nuvem
pers_param_downloadQRCodePic=Download da imagem do código QR
pers_param_qrCodeUrl=URL do código QR
pers_param_qrCodeUrlCreate=Registro de Auto-Atendimento
pers_param_qrCodeUrlHref=Endereço do servidor: Porta
pers_param_pinSetWarn=Já existe pessoal no sistema atual e o modo de número pessoal não pode ser alterado.
pers_param_selfRegistration=Ativar Auto-Registro
pers_param_infoProtection=Proteção de Informações Sensíveis
pers_param_infoProtectionWarnMsg=Após ativar a opção de proteção de segurança de informações pessoais confidenciais, os dados pessoais confidenciais envolvidos neste módulo serão dessensibilizados ou obscurecidos, incluindo, entre outros, nomes, números de cartão, números de identificação, fotos, etc.
pers_param_templateServer=Servidor de Extração de Modelos Faciais
pers_param_enableFacialTemplate=Ativar a Extracção do Modelo Facial
pers_param_templateServerAddr=Endereço do servidor de extracção de modelos faciais
pers_param_templateServerWarnInfo=Quando a extração de modelo facial é ativada, quando o servidor de extração de modelo facial está on-line e a verificação do usuário é passada, o pessoal irá padrão para extrair modelos faciais ao comparar fotos; Quando o servidor de extração de template facial estiver no modo offline, não extraia templates faciais!
pers_param_templateServerWarnInfo1=Ao ativar a extração de modelo facial, um dispositivo que suporte a extração de modelo facial precisa ser conectado!
pers_param_templateServerOffline=O servidor de extração de templates faciais está offline e não pode extrair templates faciais! Queres continuar?
pers_param_faceServer=Serviço de comparação de infra-estruturas faciais
pers_param_enableFaceVerify=Activar a comparação da infra- estrutura facial
pers_param_faceServerAddr=Endereço do serviço de comparação da infra- estrutura facial
pers_param_faceServerSecret=Chave de serviço de comparação da infra- estrutura face
#国籍
pers_person_nationality=País/Região
pers_nationality_angola=Angolano
pers_nationality_afghanistan=Afegão
pers_nationality_albania=Albanês
pers_nationality_algeria=Argelino
pers_nationality_america=Americano
pers_nationality_andorra=Andorrano
pers_nationality_anguilla=Anguilano
pers_nationality_antAndBar=Antiguano
pers_nationality_argentina=Argentino
pers_nationality_armenia=Armênios
pers_nationality_ascension=Ascensoriano
pers_nationality_australia=Australiano
pers_nationality_austria=Austríaco
pers_nationality_azerbaijan=Azerbaijanos
pers_nationality_bahamas=Bahamenho
pers_nationality_bahrain=Bareinita
pers_nationality_bangladesh=Bangladechiano
pers_nationality_barbados=Barbadiano
pers_nationality_belarus=Bielorrusso
pers_nationality_belgium=Belga
pers_nationality_belize=Belizenho
pers_nationality_benin=Beninense
pers_nationality_bermudaIs=Bermudense
pers_nationality_bolivia=Boliviano
pers_nationality_botswana=Botsuano
pers_nationality_brazil=Brasileiro
pers_nationality_brunei=Bruneano
pers_nationality_bulgaria=Búlgaro
pers_nationality_burkinaFaso=Burquinense
pers_nationality_burma=Birmanês
pers_nationality_burundi=Burundiano
pers_nationality_cameroon=Camaronês
pers_nationality_canada=Canadense
pers_nationality_caymanIs=Caimanês
pers_nationality_cenAfrRepub=Centro-africano
pers_nationality_chad=Chadiano
pers_nationality_chile=Chileno
pers_nationality_china=China
pers_nationality_colombia=Colombiano
pers_nationality_congo=Congolês
pers_nationality_cookIs=Cookense
pers_nationality_costaRica=Costa Riquenho
pers_nationality_cuba=Cubano
pers_nationality_cyprus=Cipriota
pers_nationality_czechRep=Tcheco
pers_nationality_denmark=Dinamarquês
pers_nationality_djibouti=Djibutiense
pers_nationality_dominicaRep=Dominicano
pers_nationality_ecuador=Equatoriano
pers_nationality_egypt=Egípcio
pers_nationality_eISalvador=Salvadorenho
pers_nationality_england=Britânico
pers_nationality_estonia=Estoniano
pers_nationality_ethiopia=Etíope
pers_nationality_fiji=Fijiano
pers_nationality_finland=Finlandês
pers_nationality_france=Francês
pers_nationality_freGui=Guianense
pers_nationality_gabon=Gabonense
pers_nationality_gambia=Gambiano
pers_nationality_georgia=Georgiano
pers_nationality_germany=Alemão
pers_nationality_ghana=Ganense
pers_nationality_gibraltarm=Gibraltino
pers_nationality_greece=Grego
pers_nationality_grenada=Granadino
pers_nationality_guam=Guamês
pers_nationality_guatemala=Guatemalteco
pers_nationality_guinea=Guineano
pers_nationality_guyana=Guianense
pers_nationality_haiti=Haitiano
pers_nationality_honduras=Hondurenho
pers_nationality_hungary=Húngaro
pers_nationality_iceland=Islandês
pers_nationality_india=Indiano
pers_nationality_indonesia=Indonésio
pers_nationality_iran=Iraniano
pers_nationality_iraq=Iraquiano
pers_nationality_ireland=Irlandês
pers_nationality_israel=Israelense
pers_nationality_italy=Italiano
pers_nationality_ivoryCoast=Costa-marfinense
pers_nationality_jamaica=Jamaicano
pers_nationality_japan=Japonês
pers_nationality_jordan=Jordano
pers_nationality_kenya=Queniano
pers_nationality_korea=Coreano
pers_nationality_kuwait=Kuwaitiano
pers_nationality_kyrgyzstan=Quirguistanês
pers_nationality_laos=Laosiano
pers_nationality_latvia=Letão
pers_nationality_lebanon=Libanês
pers_nationality_lesotho=Lesotiano
pers_nationality_liberia=Liberiano
pers_nationality_libya=Líbio
pers_nationality_liechtenstein=Liechtensteinense
pers_nationality_lithuania=Lituano
pers_nationality_luxembourg=Luxemburguês
pers_nationality_madagascar=Malgaxe
pers_nationality_malawi=Malauiano
pers_nationality_malaysia=Malaio
pers_nationality_maldives=Maldivío
pers_nationality_mali=Maliano
pers_nationality_malta=Maltês
pers_nationality_marianaIs=Marianense
pers_nationality_martinique=Martinicano
pers_nationality_mauritius=Mauriciano
pers_nationality_mexico=Mexicano
pers_nationality_moldova=Moldavo
pers_nationality_monaco=Monacan
pers_nationality_montseIs=Monserratense
pers_nationality_morocco=Marroquino
pers_nationality_mozambique=Moçambicano
pers_nationality_namibia=Namíbio
pers_nationality_nauru=Nauruano
pers_nationality_nepal=Nepalês
pers_nationality_netAnti=Antilhano
pers_nationality_netherlands=Holandês
pers_nationality_newZealand=Neozelandês
pers_nationality_nicaragua=Nicaraguense
pers_nationality_niger=Nigerense
pers_nationality_nigeria=Nigeriano
pers_nationality_norKorea=Norte Coreano
pers_nationality_norway=Norueguês
pers_nationality_oman=Omani
pers_nationality_pakistan=Paquistanês
pers_nationality_panama=Panamenho
pers_nationality_papNewCui=Papuásio
pers_nationality_paraguay=Paraguaio
pers_nationality_peru=Peruano
pers_nationality_philippines=Filipino
pers_nationality_poland=Polonês
pers_nationality_frenPolyne=Polinésio
pers_nationality_portugal=Português
pers_nationality_puerRico=Porto Riquenho
pers_nationality_qatar=Catariano
pers_nationality_reunion=Reunionense
pers_nationality_romania=Romeno
pers_nationality_russia=Russo
pers_nationality_saiLueia=Santa-lucense
pers_nationality_saintVinc=São-vicentino
pers_nationality_samoa_eastern=Samoano Oriental
pers_nationality_samoa_western=Samoano Ocidental
pers_nationality_sanMarino=São-marinhense
pers_nationality_saoAndPrinc=São-tomense
pers_nationality_sauArabia=Saudita
pers_nationality_senegal=Senegalês
pers_nationality_seychelles=Seichelense
pers_nationality_sieLeone=Serra-leonês
pers_nationality_singapore=Singapuriano
pers_nationality_slovakia=Eslovaco
pers_nationality_slovenia=Esloveno
pers_nationality_solomonIs=Salomônico
pers_nationality_somali=Somali
pers_nationality_souAfrica=Sul-africano
pers_nationality_spain=Espanhol
pers_nationality_sriLanka=Srilankês
pers_nationality_sudan=Sudanês
pers_nationality_suriname=Surinamês
pers_nationality_swaziland=Suazi
pers_nationality_sweden=Sueco
pers_nationality_switzerland=Suíço
pers_nationality_syria=Sírio
pers_nationality_tajikstan=Tadjique
pers_nationality_tanzania=Tanzaniano
pers_nationality_thailand=Tailandês
pers_nationality_togo=Togolês
pers_nationality_tonga=Tonganês
pers_nationality_triAndToba=Trinitário-Tobagense
pers_nationality_tunisia=Tunisiano
pers_nationality_turkey=Turco
pers_nationality_turkmenistan=Turcomeno
pers_nationality_uganda=Ugandense
pers_nationality_ukraine=Ucraniano
pers_nationality_uniArabEmira=Árabe
pers_nationality_uruguay=Uruguaio
pers_nationality_uzbekistan=Usbeque
pers_nationality_venezuela=Venezuelano
pers_nationality_vietnam=Vietnamita
pers_nationality_yemen=Iemenita
pers_nationality_serbia=Sérvio
pers_nationality_zimbabwe=Zimbabuano
pers_nationality_zambia=Zambiano
pers_nationality_aruba=Arubano
pers_nationality_bhutan=Butanês
pers_nationality_bosnia_herzegovina=Bósnio-Herzegovino
pers_nationality_cambodia=Cambojano
pers_nationality_congoD=Congolês
pers_nationality_comoros=Comorense
pers_nationality_capeVerde=Cabo-Verdiano
pers_nationality_croatia=Croata
pers_nationality_dominica=Dominiquês
pers_nationality_eritrea=Eritreu
pers_nationality_micronesia=Micronésio
pers_nationalit_guineaBissau=Guinéu
pers_nationalit_equatorialGuinea=Guinéu-equatoriano
pers_nationalit_hongkong=Hong Kong, China
pers_nationalit_virginIslands=Virginense
pers_nationalit_britishVirginIslands=Virginense
pers_nationalit_kiribati=Quiribatiano
pers_nationalit_mongolia=Mongol
pers_nationalit_marshall=Marshallino
pers_nationalit_macedonia=Macedônio
pers_nationalit_montenegro=Montenegrino
pers_nationalit_mauritania=Mauritano
pers_nationalit_palestine=Palestino
pers_nationalit_palau=Palauano
pers_nationalit_rwanda=Ruandês
pers_nationalit_saintKittsNevis=São-cristovense
pers_nationalit_timorLeste=Timorense
pers_nationalit_taiwan=Taiwan, China
pers_nationalit_tuvalu=Tuvaluano
pers_nationalit_vanuatu=Vanuatuense
#制卡
pers_person_cardprint=Imprimir Cartão
pers_cardTemplate_tempSelect=Modelo de Cartão
pers_cardTemplate_printerSelect=Impressora
pers_cardTemplate_front=Frente
pers_cardTemplate_opposite=Verso
pers_cardTemplate_entryDate=Data de Contratação
pers_cardTemplate_photo=Foto
pers_cardTemplate_uploadFail=Falha no upload da foto!
pers_cardTemplate_jpgFormat=Suporte apenas para o upload de imagens no formato JPG!
pers_cardTemplate_printStatus=Status da Impressão
pers_cardTemplate_waiting=Esperando
pers_cardTemplate_printing=Imprimindo
pers_cardTemplate_printOption=Opção de Impressão
pers_cardTemplate_duplexPrint=Impressão Duplex
pers_cardTemplate_frontOnly=Imprimir Apenas Frente
#app
pers_app_delPers=As pessoas que você quer excluir não estão no servidor
pers_app_deptIsNull=Não é possível encontrar o número ou o nome do departamento correspondente ao departamento
pers_app_personNull=A pessoa não existe
pers_app_pinExist=O ID Pessoal já existe
pers_app_dateError=O formato da data está incorreto; consulte o formato correto: 2016-08-08
#api
pers_api_selectPhotoInvalid=A foto é inválida, faça o upload novamente
pers_api_dateError=O formato da data está incorreto
pers_api_personNotExist=A pessoa não existe
pers_api_cardsPersSupport=O sistema não abre o cartão; cartão inválido.
pers_api_department_codeOrNameNotNull=O código ou o nome do departamento não pode estar vazio
pers_api_deptSortNoIsNull=A classificação do departamento não pode estar vazia!
pers_api_deptSortNoError=O valor da classificação do departamento deve estar entre 1-999999!
pers_api_dataLimit=O número atual de operações é {0}, excedendo o limite de {1}. Por favor, operem em lotes!
pers_api_cardTypeError=Erro do tipo de cartão
#人员生物模板API
pers_api_fingerprintExisted=A impressão digital da pessoa já existe
pers_api_validtypeIncorrect=Este valor de atributo validtype está incorreto
pers_api_dataNotExist=Modelo Não existe
pers_api_templateNoRang=Insira o modelo corretoNenhum valor no intervalo 0-9
pers_api_templateIsNull=O modelo não pode estar vazio!
pers_api_versionIsNumber=A versão só pode introduzir números!
#临时人员自助注册
pers_tempPersion_pinOnlyNumber=O pin pode ser apenas um número!
#biotime
pers_h5_personAvatarNotNull=O avatar está vazio
pers_h5_personMobileRepeat=O número de celular já existe
pers_h5_personEmailRepeat=A caixa de correio já existe
pers_h5_pwdIsRepetition=Repetição de senha nova e antiga
pers_h5_personIdNull=O ID da equipe está vazio
pers_h5_pinOrEmailIsNull=Por favor, preencha o número e endereço de e-mail
pers_emailTitle_resetPassword=Alterar senha
pers_emailContent_resetPassword=O link é válido por 24 horas, copie o link para o navegador para alterar a senha:
pers_h5_tokenIsNull=O comprovante está vazio
pers_h5_tokenError=Erro de comprovante
pers_h5_oldPwdIsError=A senha antiga foi preenchida incorretamente
pers_api_resetPasswordSuccess=A senha foi atualizada
pers_api_resetPasswordFail=Falha na alteração de senha
pers_api_forgetPassword=esqueci a senha
pers_api_confirmSubmit=confirmar envio
pers_api_confirmPwdCaution=Clique em [OK] para confirmar a nova senha.
pers_api_pwdRule=A senha deve conter pelo menos um símbolo ou número e ter pelo menos de 8 a 12 caracteres
pers_h5_personPinFormatNumber=O número do pessoal pode consistir apenas em números ou letras
pers_h5_persEmailNoExist=Erro de preenchimento da caixa de correio
pers_h5_pageNull=Erro no parâmetro de paginação
pers_h5_personPinNotStartWithZero=O número do pessoal não pode começar com 0
pers_h5_personPinTooLong=O número de pessoa é muito longo
pers_h5_personPinInValid=Este número já está em uso
pers_h5_imgSizeError=Faça o upload de uma imagem que não exceda 10Mb
pers_h5_confirmAndContinue=Confirmar e Continuar
#人脸抠图不及格错误
pers_face_poorResolution=Resolução da imagem abaixo de 80000 pixels
pers_face_noFace=Nenhum rosto detectado
pers_face_manyFace=Múltiplas faces detectadas
pers_face_smallFace=A proporção da face é muito pequena
pers_face_notColor=A imagem é não colorida
pers_face_seriousBlur=A imagem está desfocada
pers_face_intensivelyLight=A imagem está muito exposta
pers_face_badIllumination=A imagem está muito escura
pers_face_highNoise=Imagens com alto ruído
pers_face_highStretch=Rosto sobrecarregado
pers_face_covered=O rosto está coberto
pers_face_smileOpenMouth=Sorriso excessivo
pers_face_largeAngle=O ângulo de deflexão da face é muito grande
pers_face_criticalIllumination=Brilho da imagem crítico
pers_face_criticalLargeAngle=Ângulo crítico de deflexão da face
pers_face_validFailMsg=A detecção de rosto falhou devido a:
pers_face_failType=Tipo de falha de recorte de face
pers_face_photoFormatError=O formato da foto está incorreto, envie um arquivo no formato JPG / PNG.
pers_face_notUpdateMsg=Falha ao gerar a imagem do rosto, não atualize a imagem do rosto.
#健康申报
pers_health_enable=Ativar declaração de informações de integridade
pers_health_attrExposure=Você já teve contato com casos suspeitos ou confirmados
pers_health_attrSymptom=Quais são os sintomas nos últimos 14 dias
pers_health_attrVisitCity=Cidades visitadas nos últimos 14 dias
pers_health_attrRemarks=Observações sobre saúde
pers_health_symptomCough=tosse
pers_health_symptomFever=febre
pers_health_symptomPolypena=Má respiração
pers_health_declaration=Declaração de saúde
pers_health_aggrement=Concordei que as pessoas que não preencherem as informações necessárias serão proibidas de acessar e os visitantes que não reportarem as informações com sinceridade não poderão continuar a acessar e precisarão assumir as responsabilidades legais correspondentes.
pers_health_visitCity_notEmpty=A cidade visitada não pode estar vazia!
pers_health_notAgree=Você precisa optar por concordar com o contrato para continuar
#人员名单库
pers_personnal_list_manager=Gerente de lista
pers_personnal_list=Biblioteca de Listas
pers_personnal_list_scheme=Modo de domínio
pers_personnal_list_name=Nome da Lista
pers_personnal_list_group_str_id=lista de ID de grupo
pers_personnal_list_personCount=Número de Pessoas
pers_personnal_list_tag=Definido pelo usuário
pers_personnal_list_type=Tipo de Lista
pers_personnallist_addPerson_repo=Adicionar uma pessoa ao repo
pers_personnallist_sendPersonnallist=Biblioteca de lista distribuída
pers_personnallist_sendPerson=Emitente
pers_personnallist_notDel_existPerson=A biblioteca da lista contém pessoas e não pode ser excluída
pers_personnallist_peopleInRoster=Ainda há pessoas na biblioteca da lista
pers_personnallist_associationNotExist=A associação entre o dispositivo principal e a biblioteca da lista não existe
pers_personnal_list_person=Pessoa da lista pessoal
pers_personnal_list_dev=Listar permissões da biblioteca
pers_personnal_list_addDev=Adicionar dispositivo
pers_personnal_list_name_isExist=O nome já existe
pers_personnal_bannedList=biblioteca de lista de banidos
pers_personnal_allowList=Biblioteca de lista de permissões
pers_personnal_redList=Biblioteca da Lista Vermelha
pers_personnal_attGroup=Grupo de Atendimento
pers_personnal_passList=Lista de Permitidos
pers_personnal_banList=Lista Proibidos
pers_personnal_visPassList=Lista de Visitantes			
pers_personnal_visBanList=Lista de Visitantes Proibidos
pers_personnal_databaseHasBeenDistributed=A Biblioteca Da lista selecionada FOI distribuída e não Pode ser apagada
pers_personnel_sendError_dueTo=Falha na entrega Motivo da falha:
pers_personnel_sendError_reson={0} existe na lista de banidos, exclua e adicione
#比对照片-样片示例
pers_examplePic_Tip=Deve atender aos seguintes requisitos:
pers_examplePic_Tip1=1. a cor de fundo é branco puro e o pessoal usa roupas escuras;
pers_examplePic_Tip2=2. As fotos eletrônicas estão em formato de arquivo JPG, PNG, JPEG, o intervalo de pixels recomendado: 480*640 < pixel < 1080*1920;
pers_examplePic_Tip3=3. Os retratos em fotos eletrônicas devem abrir os olhos e olhar para a frente e garantir que as pupilas estejam bem visíveis;
pers_examplePic_Tip4=4. O retrato na foto eletrônica deve ter uma expressão neutra, e você pode sorrir, mas não deve mostrar os dentes;
pers_examplePic_Tip5=5. O retrato na foto eletrônica deve ser claro, com cores naturais, camadas ricas e sem distorção óbvia. Sem sombras, destaques ou reflexos em rostos ou fundos de retratos; contraste e brilho são apropriados.
pers_examplePic_description=Exemplo correto
pers_examplePic_error=Exemplo de erro:
pers_examplePic_error1=Expressão exagerada (sorriso excessivo)
pers_examplePic_error2=A luz está muito escura
pers_examplePic_error3=O rosto é muito pequeno (a resolução é muito pequena)
pers_applogin_enabled=Ativar Login do APP
pers_applogin_disable=Desativar Login do APP
pers_applogin_status=Estado de activação da autenticação da aplicação