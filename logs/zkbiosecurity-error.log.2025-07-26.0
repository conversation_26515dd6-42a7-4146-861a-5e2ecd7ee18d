2025-07-26 14:06:31.380 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-26 14:06:31.380 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 14:06:31.395 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 14:06:32.488 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 14:06:32.800 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 14:11:15.768 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 14:11:26.770 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 14:23:58.812 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-26 14:23:58.814 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 14:23:58.815 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 14:23:58.815 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 14:23:58.815 [AccSavePersonThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:100) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccSavePersonThread.run(AccCommunicationDataProcessor.java:86)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-26 14:35:02.246 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 14:35:06.428 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 14:53:19.945 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 14:53:24.049 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 14:57:57.272 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 14:58:03.300 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 15:17:20.448 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 15:17:26.165 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 15:23:13.485 [http-nio-8098-exec-21] ERROR com.zkteco.zkbiosecurity.adms.service.impl.AdmsAuthDeviceServiceImpl-(AdmsAuthDeviceServiceImpl.java:822) - java.io.FileNotFoundException: D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104\BioSecurityFile\upload\adms\projectDeviceControl\pdc.lic (系统找不到指定的路径。)
2025-07-26 15:24:13.488 [http-nio-8098-exec-28] ERROR com.zkteco.zkbiosecurity.auth.controller.LoginController-(LoginController.java:254) - login error
2025-07-26 15:40:47.044 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 15:40:51.555 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 15:50:29.200 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 15:51:19.028 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadAttBioDataKeyList(AttCacheManager.java:132)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttBioData(AttCommunicationDataProcessor.java:208)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:88)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 15:51:20.390 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 15:59:02.078 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 16:01:15.954 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 16:01:15.954 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-26 16:08:05.785 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 16:08:06.287 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 16:08:06.333 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 16:08:06.333 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 16:12:33.124 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 16:12:37.547 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 16:22:20.587 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 16:22:25.012 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 16:22:58.252 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 17:40:34.961 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 17:40:39.313 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 17:52:22.780 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 17:52:28.026 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 17:59:13.963 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 17:59:29.683 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.hasDeviceInfo(AdmsCacheManager.java:970)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:66)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 20 common frames omitted
2025-07-26 18:01:13.232 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:530)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$94059db4.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.getRequest(AccPushProcessor.java:739)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:136)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 39 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 5 common frames omitted
2025-07-26 18:01:13.229 [defaultEventExecutorGroup-11-4] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.setDevRunState(AdmsCacheManager.java:523)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:238)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$94059db4.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.heartbeat(AccPushProcessor.java:956)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:168)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 39 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 5 common frames omitted
2025-07-26 18:01:17.351 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/registry?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 5 common frames omitted
2025-07-26 18:01:41.262 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/cdata?SN=7273212700013&options=all&pushver=3.1.2&DeviceType=acc&PushOptionsFlag=1	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.hasDeviceInfo(AdmsCacheManager.java:970)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:66)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 20 common frames omitted
2025-07-26 18:01:48.234 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 18:01:48.234 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 18:02:11.500 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadAttPhotoKeyList(AttCacheManager.java:123)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttPhoto(AttCommunicationDataProcessor.java:186)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:102)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 18:07:14.594 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 5 common frames omitted
2025-07-26 18:07:22.885 [CustomThreadPoolExecutor1] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:299) - ---------------------batch save accTransaction to DB error---------------key:acc:accTransactionsToDb--------value:{"areaId":"828080819836ddd2019836df10430003","areaName":"区域名称","capturePhotoPath":"/upload/event/photo/7273212700013/20250726180707.jpg","cardNo":"","deptCode":"2","deptId":"82808081983a613101983a6ce1860026","deptName":"搬砖部门","description":"","devAlias":"**************","devId":"828080819839ffd201983a0893df0019","devSn":"7273212700013","doorId":"828080819839ffd201983a0894840066","doorName":"**************-1","equals":false,"eventAddr":1,"eventLevel":0,"eventName":"acc_newEventNo_0","eventNo":0,"eventPointId":"828080819839ffd201983a0894840066","eventPointName":"**************-1","eventPointType":0,"eventTime":1753524427000,"id":"82808081984624f201984633776a0066","lastName":"","logId":735,"maskFlag":"255","name":"测试","persPersonId":"828080819839ffd201983a08fff60153","pin":"23211","readerId":"828080819839ffd201983a0894850068","readerName":"**************-1-出","readerState":1,"temperature":"255","uniqueKey":"7273212700013_735_2025-07-26 18:07:07","verifyModeName":"acc_verify_mode_onlyface","verifyModeNo":15}
2025-07-26 18:07:22.885 [CustomThreadPoolExecutor1] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:301) - -----------------------------------get acctransaction error message
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseSysParamServiceImpl.getValByName(BaseSysParamServiceImpl.java:123)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseSysParamServiceImpl$$FastClassBySpringCGLIB$$20c88444.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseSysParamServiceImpl$$EnhancerBySpringCGLIB$$7aca41f.getValByName(<generated>)
	at com.zkteco.zkbiosecurity.license.service.impl.BaseLicenseServiceImpl.getAppId(BaseLicenseServiceImpl.java:1969)
	at com.zkteco.zkbiosecurity.acc.client.service.impl.AccCloudServiceImpl.sendAlarmMsgToWxMiniPrograms(AccCloudServiceImpl.java:122)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.saveTransactionToDb(AccCommunicationDataProcessor.java:290)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.lambda$saveRtTransaction$0(AccCommunicationDataProcessor.java:265)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1142)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:617)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 27 common frames omitted
2025-07-26 18:07:22.886 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 18:07:41.546 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 18:07:41.567 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 5 common frames omitted
2025-07-26 18:08:03.253 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 18:08:03.259 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadAttLogKeyList(AttCacheManager.java:114)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttLog(AttCommunicationDataProcessor.java:131)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:95)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 18:08:42.499 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 18:08:42.500 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 18:08:42.501 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:159)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lLen(DefaultedRedisConnection.java:649)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lLen(DefaultStringRedisConnection.java:694)
	at org.springframework.data.redis.core.DefaultListOperations.lambda$size$5(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.size(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.DefaultBoundListOperations.size(DefaultBoundListOperations.java:118)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevCmdCount(AdmsCacheManager.java:509)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:535)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$94059db4.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.heartbeat(AccPushProcessor.java:956)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:168)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.llen(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:157)
	... 40 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 5 common frames omitted
2025-07-26 18:08:42.501 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 18:09:17.488 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/cdata?SN=7273212700013&table=rtstate	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDeviceInfo(AdmsCacheManager.java:207)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevEnabledState(AdmsCacheManager.java:980)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:70)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 23 common frames omitted
2025-07-26 18:09:17.489 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lTrim(LettuceListCommands.java:207)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lTrim(DefaultedRedisConnection.java:663)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lTrim(DefaultStringRedisConnection.java:758)
	at org.springframework.data.redis.core.DefaultListOperations$9.inRedis(DefaultListOperations.java:346)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.trim(DefaultListOperations.java:342)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.removeAccTransaction(AccCacheManager.java:309)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.getAccTransactions(AccCommunicationDataProcessor.java:346)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.saveRtTransaction(AccCommunicationDataProcessor.java:257)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:240)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.ltrim(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lTrim(LettuceListCommands.java:205)
	... 12 common frames omitted
2025-07-26 18:09:17.488 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOpLogKeyList(AttCacheManager.java:167)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttOpLog(AttCommunicationDataProcessor.java:237)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:109)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 18:09:17.489 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDeviceInfo(AdmsCacheManager.java:207)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevEnabledState(AdmsCacheManager.java:980)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:70)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 23 common frames omitted
2025-07-26 18:09:30.947 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 18:09:30.948 [AccQueryDataThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:153) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccQueryDataThread.run(AccCommunicationDataProcessor.java:141)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-26 18:09:30.949 [AccSavePersonThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:100) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccSavePersonThread.run(AccCommunicationDataProcessor.java:86)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-26 18:09:30.949 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/cdata?SN=7273212700013&table=tabledata&tablename=ATTPHOTO&count=1	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-26 18:09:30.950 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-26 18:09:30.952 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 18:09:30.951 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 18:09:30.952 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-26 18:10:55.136 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lTrim(LettuceListCommands.java:207)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lTrim(DefaultedRedisConnection.java:663)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lTrim(DefaultStringRedisConnection.java:758)
	at org.springframework.data.redis.core.DefaultListOperations$9.inRedis(DefaultListOperations.java:346)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.trim(DefaultListOperations.java:342)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.removeAccTransaction(AccCacheManager.java:309)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.getAccTransactions(AccCommunicationDataProcessor.java:346)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.saveRtTransaction(AccCommunicationDataProcessor.java:257)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:240)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.ltrim(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lTrim(LettuceListCommands.java:205)
	... 12 common frames omitted
2025-07-26 18:10:55.136 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOpLogKeyList(AttCacheManager.java:167)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttOpLog(AttCommunicationDataProcessor.java:237)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:109)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 18:12:40.099 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDeviceInfo(AdmsCacheManager.java:207)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevEnabledState(AdmsCacheManager.java:980)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:70)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 23 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 5 common frames omitted
2025-07-26 18:12:40.300 [AccSavePersonThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:100) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccSavePersonThread.run(AccCommunicationDataProcessor.java:86)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 18:12:40.300 [AccQueryDataThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:153) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccQueryDataThread.run(AccCommunicationDataProcessor.java:141)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 18:12:40.450 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.hasDeviceInfo(AdmsCacheManager.java:970)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:66)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 20 common frames omitted
2025-07-26 18:12:40.681 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 18:20:49.524 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.hasDeviceInfo(AdmsCacheManager.java:970)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:66)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 20 common frames omitted
2025-07-26 18:20:49.842 [defaultEventExecutorGroup-11-4] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.hasDeviceInfo(AdmsCacheManager.java:970)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:66)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 20 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 5 common frames omitted
2025-07-26 18:22:48.913 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getIVideoDigifortLinkage(AccCacheManager.java:664)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.getAccTransactions(AccCommunicationDataProcessor.java:316)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.saveRtTransaction(AccCommunicationDataProcessor.java:257)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:240)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-26 18:22:48.913 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadAttPhotoKeyList(AttCacheManager.java:123)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttPhoto(AttCommunicationDataProcessor.java:186)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:102)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 18:22:48.913 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDeviceInfo(AdmsCacheManager.java:207)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevEnabledState(AdmsCacheManager.java:980)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:70)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 23 common frames omitted
2025-07-26 18:22:48.913 [defaultEventExecutorGroup-11-5] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.hasDeviceInfo(AdmsCacheManager.java:970)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:66)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 20 common frames omitted
2025-07-26 18:23:22.003 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOpLogKeyList(AttCacheManager.java:167)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttOpLog(AttCommunicationDataProcessor.java:237)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:109)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 18:23:22.004 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lTrim(LettuceListCommands.java:207)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lTrim(DefaultedRedisConnection.java:663)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lTrim(DefaultStringRedisConnection.java:758)
	at org.springframework.data.redis.core.DefaultListOperations$9.inRedis(DefaultListOperations.java:346)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.trim(DefaultListOperations.java:342)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.removeAccTransaction(AccCacheManager.java:309)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.getAccTransactions(AccCommunicationDataProcessor.java:346)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.saveRtTransaction(AccCommunicationDataProcessor.java:257)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:240)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.ltrim(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lTrim(LettuceListCommands.java:205)
	... 12 common frames omitted
2025-07-26 18:23:22.004 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDeviceInfo(AdmsCacheManager.java:207)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevEnabledState(AdmsCacheManager.java:980)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:70)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 23 common frames omitted
2025-07-26 18:24:13.287 [defaultEventExecutorGroup-11-2] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:530)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$94059db4.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.getRequest(AccPushProcessor.java:739)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:136)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 39 common frames omitted
2025-07-26 18:30:17.145 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.hasDeviceInfo(AdmsCacheManager.java:970)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:66)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 20 common frames omitted
2025-07-26 18:30:39.026 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadAttLogKeyList(AttCacheManager.java:114)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttLog(AttCommunicationDataProcessor.java:131)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:95)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 18:32:01.958 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 18:32:06.397 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 18:33:55.502 [AccSavePersonThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:100) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lPop(LettuceListCommands.java:331)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lPop(DefaultedRedisConnection.java:698)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lPop(DefaultStringRedisConnection.java:703)
	at org.springframework.data.redis.core.DefaultListOperations$2.inRedis(DefaultListOperations.java:67)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.leftPop(DefaultListOperations.java:63)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getLeftFirstValue(AccCacheManager.java:106)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccSavePersonThread.run(AccCommunicationDataProcessor.java:90)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.lpop(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lPop(LettuceListCommands.java:329)
	... 10 common frames omitted
2025-07-26 18:33:55.502 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-26 18:38:22.227 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:159)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lLen(DefaultedRedisConnection.java:649)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lLen(DefaultStringRedisConnection.java:694)
	at org.springframework.data.redis.core.DefaultListOperations.lambda$size$5(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.size(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.DefaultBoundListOperations.size(DefaultBoundListOperations.java:118)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevCmdCount(AdmsCacheManager.java:509)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:535)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$19979707.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.getRequest(AccPushProcessor.java:739)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:136)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.llen(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:157)
	... 40 common frames omitted
2025-07-26 18:38:47.716 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOpLogKeyList(AttCacheManager.java:167)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttOpLog(AttCommunicationDataProcessor.java:237)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:109)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-26 18:38:53.348 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lTrim(LettuceListCommands.java:207)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lTrim(DefaultedRedisConnection.java:663)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lTrim(DefaultStringRedisConnection.java:758)
	at org.springframework.data.redis.core.DefaultListOperations$9.inRedis(DefaultListOperations.java:346)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.trim(DefaultListOperations.java:342)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.removeAccTransaction(AccCacheManager.java:309)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.getAccTransactions(AccCommunicationDataProcessor.java:346)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.saveRtTransaction(AccCommunicationDataProcessor.java:257)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:240)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.ltrim(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lTrim(LettuceListCommands.java:205)
	... 12 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-26 18:38:53.352 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:159)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lLen(DefaultedRedisConnection.java:649)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lLen(DefaultStringRedisConnection.java:694)
	at org.springframework.data.redis.core.DefaultListOperations.lambda$size$5(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.size(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.DefaultBoundListOperations.size(DefaultBoundListOperations.java:118)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevCmdCount(AdmsCacheManager.java:510)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:535)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$19979707.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.getRequest(AccPushProcessor.java:739)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:136)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.llen(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:157)
	... 40 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	... 5 common frames omitted
2025-07-26 18:42:06.079 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-26 18:42:10.496 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-26 18:44:34.366 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
