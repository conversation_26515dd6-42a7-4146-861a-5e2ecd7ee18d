2025-07-25 08:57:52.116 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-25 08:57:52.042 [defaultEventExecutorGroup-11-2] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/exchange?SN=7273212700013&type=factors	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-25 08:57:52.284 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-25 08:57:52.374 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-25 08:57:52.696 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-25 13:33:19.464 [defaultEventExecutorGroup-10-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpBasedHandler-(HttpBasedHandler.java:102) - exceptionCaught
java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:380)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1134)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-25 13:33:21.072 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-25 13:33:20.224 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-25 13:33:19.846 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-25 13:33:21.689 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-25 13:33:19.603 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-25 14:20:23.740 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-25 14:20:50.613 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
java.lang.NullPointerException: null
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.getWechatUserList(WechatServiceImpl.java:106)
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.wechatSendMessageTest(WechatServiceImpl.java:163)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.putAccTransactionsToCache(AccTransactionServiceImpl.java:914)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.handleRTLog(AccTransactionServiceImpl.java:635)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$FastClassBySpringCGLIB$$b735b69a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$EnhancerBySpringCGLIB$$d0258ddb.handleRTLog(<generated>)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.handlerAccLog(AccCommunicationDataProcessor.java:216)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:194)
2025-07-25 14:22:45.091 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
java.lang.NullPointerException: null
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.getWechatUserList(WechatServiceImpl.java:106)
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.wechatSendMessageTest(WechatServiceImpl.java:163)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.putAccTransactionsToCache(AccTransactionServiceImpl.java:914)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.handleRTLog(AccTransactionServiceImpl.java:635)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$FastClassBySpringCGLIB$$b735b69a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$EnhancerBySpringCGLIB$$d0258ddb.handleRTLog(<generated>)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.handlerAccLog(AccCommunicationDataProcessor.java:216)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:194)
2025-07-25 14:22:45.527 [CustomThreadPoolExecutor1] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:295) - ---------------FirstSaveExceptionTransactionHandle_ConstraintViolationException maybe transaction is repeat ,e=could not execute statement; SQL [n/a]; constraint [null]; nested exception is org.hibernate.exception.ConstraintViolationException: could not execute statement
2025-07-25 14:23:10.804 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-25 14:23:10.804 [AccSavePersonThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:100) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lPop(LettuceListCommands.java:331)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lPop(DefaultedRedisConnection.java:698)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lPop(DefaultStringRedisConnection.java:703)
	at org.springframework.data.redis.core.DefaultListOperations$2.inRedis(DefaultListOperations.java:67)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.leftPop(DefaultListOperations.java:63)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getLeftFirstValue(AccCacheManager.java:106)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccSavePersonThread.run(AccCommunicationDataProcessor.java:90)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.lpop(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lPop(LettuceListCommands.java:329)
	... 10 common frames omitted
2025-07-25 14:23:10.803 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-25 14:23:12.034 [defaultEventExecutorGroup-11-4] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:159)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lLen(DefaultedRedisConnection.java:649)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lLen(DefaultStringRedisConnection.java:694)
	at org.springframework.data.redis.core.DefaultListOperations.lambda$size$5(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.size(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.DefaultBoundListOperations.size(DefaultBoundListOperations.java:118)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevCmdCount(AdmsCacheManager.java:509)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:535)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$8691fd53.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.heartbeat(AccPushProcessor.java:956)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:168)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.llen(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:157)
	... 40 common frames omitted
2025-07-25 14:25:06.425 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
java.lang.NullPointerException: null
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.getWechatUserList(WechatServiceImpl.java:106)
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.wechatSendMessageTest(WechatServiceImpl.java:163)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.putAccTransactionsToCache(AccTransactionServiceImpl.java:914)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.handleRTLog(AccTransactionServiceImpl.java:635)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$FastClassBySpringCGLIB$$b735b69a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$EnhancerBySpringCGLIB$$d0258ddb.handleRTLog(<generated>)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.handlerAccLog(AccCommunicationDataProcessor.java:216)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:194)
2025-07-25 14:25:22.547 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
java.lang.NullPointerException: null
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.getWechatUserList(WechatServiceImpl.java:106)
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.wechatSendMessageTest(WechatServiceImpl.java:163)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.putAccTransactionsToCache(AccTransactionServiceImpl.java:914)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.handleRTLog(AccTransactionServiceImpl.java:635)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$FastClassBySpringCGLIB$$b735b69a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$EnhancerBySpringCGLIB$$d0258ddb.handleRTLog(<generated>)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.handlerAccLog(AccCommunicationDataProcessor.java:216)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:194)
2025-07-25 14:26:38.886 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-25 14:26:57.782 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:530)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$1988ccc5.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.heartbeat(AccPushProcessor.java:956)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:168)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 39 common frames omitted
2025-07-25 14:26:57.782 [defaultEventExecutorGroup-11-2] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:530)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$1988ccc5.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.getRequest(AccPushProcessor.java:739)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:136)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 39 common frames omitted
2025-07-25 14:27:12.443 [defaultEventExecutorGroup-11-2] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/registry?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-25 14:27:12.444 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-25 14:27:12.446 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
java.lang.NullPointerException: null
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.getWechatUserList(WechatServiceImpl.java:106)
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.wechatSendMessageTest(WechatServiceImpl.java:163)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.putAccTransactionsToCache(AccTransactionServiceImpl.java:914)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.handleRTLog(AccTransactionServiceImpl.java:635)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$FastClassBySpringCGLIB$$b735b69a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$EnhancerBySpringCGLIB$$34d058b9.handleRTLog(<generated>)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.handlerAccLog(AccCommunicationDataProcessor.java:216)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:194)
2025-07-25 14:28:14.061 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
java.lang.NullPointerException: null
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.getWechatUserList(WechatServiceImpl.java:106)
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.wechatSendMessageTest(WechatServiceImpl.java:163)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.putAccTransactionsToCache(AccTransactionServiceImpl.java:914)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.handleRTLog(AccTransactionServiceImpl.java:635)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$FastClassBySpringCGLIB$$b735b69a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$EnhancerBySpringCGLIB$$34d058b9.handleRTLog(<generated>)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.handlerAccLog(AccCommunicationDataProcessor.java:216)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:194)
2025-07-25 14:28:31.060 [defaultEventExecutorGroup-11-2] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.hasDeviceInfo(AdmsCacheManager.java:970)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:66)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 20 common frames omitted
2025-07-25 14:28:31.059 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/cdata?SN=7273212700013&table=tabledata&tablename=ATTPHOTO&count=1	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDeviceInfo(AdmsCacheManager.java:207)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevEnabledState(AdmsCacheManager.java:980)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:70)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 23 common frames omitted
2025-07-25 14:28:39.350 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
java.lang.NullPointerException: null
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.getWechatUserList(WechatServiceImpl.java:104)
	at com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl.wechatSendMessageTest(WechatServiceImpl.java:161)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.putAccTransactionsToCache(AccTransactionServiceImpl.java:914)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl.handleRTLog(AccTransactionServiceImpl.java:635)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$FastClassBySpringCGLIB$$b735b69a.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl$$EnhancerBySpringCGLIB$$34d058b9.handleRTLog(<generated>)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.handlerAccLog(AccCommunicationDataProcessor.java:216)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:194)
2025-07-25 14:29:33.174 [defaultEventExecutorGroup-11-2] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-25 14:29:34.216 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/cdata?SN=7273212700013&table=tabledata&tablename=ATTPHOTO&count=1	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-25 14:29:55.399 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.convertLettuceAccessException(LettuceListCommands.java:490)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:159)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.lLen(DefaultedRedisConnection.java:649)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.lLen(DefaultStringRedisConnection.java:694)
	at org.springframework.data.redis.core.DefaultListOperations.lambda$size$5(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultListOperations.size(DefaultListOperations.java:160)
	at org.springframework.data.redis.core.DefaultBoundListOperations.size(DefaultBoundListOperations.java:118)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevCmdCount(AdmsCacheManager.java:510)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:535)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$1988ccc5.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.heartbeat(AccPushProcessor.java:956)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:168)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.llen(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceListCommands.lLen(LettuceListCommands.java:157)
	... 40 common frames omitted
2025-07-25 14:30:46.933 [defaultEventExecutorGroup-11-2] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:530)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$1988ccc5.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.getRequest(AccPushProcessor.java:739)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:136)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 39 common frames omitted
2025-07-25 14:30:46.934 [defaultEventExecutorGroup-11-1] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.hasDeviceInfo(AdmsCacheManager.java:970)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:66)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 20 common frames omitted
2025-07-25 15:44:26.288 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-25 15:44:31.655 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-25 15:58:27.569 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-25 15:58:27.569 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-25 15:58:27.569 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-25 15:58:27.569 [defaultEventExecutorGroup-11-4] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-25 15:58:27.570 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-25 15:58:27.571 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadAttBioDataKeyList(AttCacheManager.java:132)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttBioData(AttCommunicationDataProcessor.java:208)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:88)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-25 15:58:27.571 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-25 15:58:57.602 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevRunState(AdmsCacheManager.java:530)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.updateDevOperateState(AdmsDeviceServiceImpl.java:230)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$a48fb58c.updateDevOperateState(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.getRequest(AccPushProcessor.java:739)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:136)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 39 common frames omitted
2025-07-25 15:59:13.492 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOpLogKeyList(AttCacheManager.java:167)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttOpLog(AttCommunicationDataProcessor.java:237)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:109)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-25 15:59:46.565 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-25 15:59:48.536 [AccSavePersonThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:100) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccSavePersonThread.run(AccCommunicationDataProcessor.java:86)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-25 15:59:59.383 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/push?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:68)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.get(DefaultedRedisConnection.java:266)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.get(DefaultStringRedisConnection.java:405)
	at org.springframework.data.redis.core.DefaultValueOperations$1.inRedis(DefaultValueOperations.java:57)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:53)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDeviceInfo(AdmsCacheManager.java:207)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.getDevEnabledState(AdmsCacheManager.java:980)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:70)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.get(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.get(LettuceStringCommands.java:66)
	... 23 common frames omitted
2025-07-25 15:59:59.384 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadAttLogKeyList(AttCacheManager.java:114)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerAttLog(AttCommunicationDataProcessor.java:131)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:95)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-25 16:00:12.025 [AccQueryDataThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:153) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccQueryDataThread.run(AccCommunicationDataProcessor.java:141)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-25 16:00:18.118 [defaultEventExecutorGroup-11-3] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor-(AccPushProcessor.java:340) - reg device error sn=7273212700013
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccDeviceServiceImpl.updateDevInfo(AccDeviceServiceImpl.java:1071)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccDeviceServiceImpl.setPushDevice(AccDeviceServiceImpl.java:812)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccDeviceServiceImpl$$FastClassBySpringCGLIB$$3816b27c.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.acc.service.impl.AccDeviceServiceImpl$$EnhancerBySpringCGLIB$$610351dd.setPushDevice(<generated>)
	at com.zkteco.zkbiosecurity.acc.service.impl.Adms4AccServiceImpl.addDevice(Adms4AccServiceImpl.java:54)
	at com.zkteco.zkbiosecurity.acc.service.impl.Adms4AccServiceImpl$$FastClassBySpringCGLIB$$ba7879e1.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.acc.service.impl.Adms4AccServiceImpl$$EnhancerBySpringCGLIB$$2a14a071.addDevice(<generated>)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl.regAccDevice(AdmsDeviceServiceImpl.java:518)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$FastClassBySpringCGLIB$$2d6a7adc.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:779)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:367)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:118)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:95)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:750)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:692)
	at com.zkteco.zkbiosecurity.adms.service.impl.AdmsDeviceServiceImpl$$EnhancerBySpringCGLIB$$a48fb58c.regAccDevice(<generated>)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.registry(AccPushProcessor.java:308)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor.handleReqData(AccPushProcessor.java:126)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:77)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 62 common frames omitted
2025-07-25 16:09:08.446 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-25 16:09:12.188 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-25 16:25:51.395 [main] ERROR com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl-(BaseCropFaceServiceImpl.java:193) - Connect the face service failed!
org.springframework.web.client.ResourceAccessException: I/O error on GET request for "http://localhost:26110/ZKDetectFace/info": Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect; nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:746)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:672)
	at org.springframework.web.client.RestTemplate.getForObject(RestTemplate.java:313)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.getUriInfo(BaseCropFaceServiceImpl.java:191)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.existDetectFaceServer(BaseCropFaceServiceImpl.java:216)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl.run(BaseCropFaceServiceImpl.java:51)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.apache.http.conn.HttpHostConnectException: Connect to localhost:26110 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused: connect
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:156)
	at org.apache.http.impl.conn.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:376)
	at org.apache.http.impl.execchain.MainClientExec.establishRoute(MainClientExec.java:393)
	at org.apache.http.impl.execchain.MainClientExec.execute(MainClientExec.java:236)
	at org.apache.http.impl.execchain.ProtocolExec.execute(ProtocolExec.java:186)
	at org.apache.http.impl.execchain.RetryExec.execute(RetryExec.java:89)
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:110)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:56)
	at org.springframework.http.client.HttpComponentsClientHttpRequest.executeInternal(HttpComponentsClientHttpRequest.java:87)
	at org.springframework.http.client.AbstractBufferingClientHttpRequest.executeInternal(AbstractBufferingClientHttpRequest.java:48)
	at org.springframework.http.client.AbstractClientHttpRequest.execute(AbstractClientHttpRequest.java:53)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:737)
	... 14 common frames omitted
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.connect0(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:79)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:350)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:206)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:188)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:172)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:392)
	at java.net.Socket.connect(Socket.java:589)
	at org.apache.http.conn.socket.PlainConnectionSocketFactory.connectSocket(PlainConnectionSocketFactory.java:75)
	at org.apache.http.impl.conn.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:142)
	... 27 common frames omitted
2025-07-25 16:25:58.428 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-25 16:25:59.080 [main] ERROR com.zkteco.zkbiosecurity.att.data.upgrade.AttVer3_0_0-(AttVer3_0_0.java:385) - att_transaction att_tran_pin_datetime_idx is already delete 
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE att_transaction DROP CONSTRAINT att_tran_pin_datetime_idx]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "att_transaction" 的 约束"att_tran_pin_datetime_idx" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttVer3_0_0.upgradeTransaction(AttVer3_0_0.java:383)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttVer3_0_0.executeUpgrade(AttVer3_0_0.java:104)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttUpgradeInit.upgrade(AttUpgradeInit.java:58)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttUpgradeInit.run(AttUpgradeInit.java:38)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "att_transaction" 的 约束"att_tran_pin_datetime_idx" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-25 16:25:59.220 [main] ERROR com.zkteco.zkbiosecurity.att.data.upgrade.AttVer3_0_0-(AttVer3_0_0.java:138) - att_tempsch_shift constraint is already delete 
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE ATT_TEMPSCH_SHIFT DROP CONSTRAINT fkd8vl8mbglgo79e6hli3b88a5j]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "att_tempsch_shift" 不存在
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttVer3_0_0.upgradeTempSch(AttVer3_0_0.java:135)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttVer3_0_0.executeUpgrade(AttVer3_0_0.java:119)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttUpgradeInit.upgrade(AttUpgradeInit.java:58)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttUpgradeInit.run(AttUpgradeInit.java:38)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "att_tempsch_shift" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-25 16:25:59.221 [main] ERROR com.zkteco.zkbiosecurity.att.data.upgrade.AttVer3_0_0-(AttVer3_0_0.java:145) - att_tempsch_shift constraint is already delete 
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE ATT_TEMPSCH_SHIFT DROP CONSTRAINT att_tempsch_shift_shift_id_fkey]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "att_tempsch_shift" 不存在
	at org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:239)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttVer3_0_0.upgradeTempSch(AttVer3_0_0.java:142)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttVer3_0_0.executeUpgrade(AttVer3_0_0.java:119)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttUpgradeInit.upgrade(AttUpgradeInit.java:58)
	at com.zkteco.zkbiosecurity.att.data.upgrade.AttUpgradeInit.run(AttUpgradeInit.java:38)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "att_tempsch_shift" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-25 17:24:55.598 [AccQueryDataThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:153) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccQueryDataThread.run(AccCommunicationDataProcessor.java:141)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-25 17:24:55.600 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-25 17:24:55.600 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-25 17:24:55.600 [defaultEventExecutorGroup-11-5] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/ping?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-25 17:24:55.600 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-25 17:24:55.598 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-25 17:24:55.600 [AccSavePersonThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:100) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccSavePersonThread.run(AccCommunicationDataProcessor.java:86)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-25 17:24:55.600 [defaultEventExecutorGroup-11-2] ERROR com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler-(HttpServerHandler.java:129) - clientIP=**************	uri=/iclock/getrequest?SN=7273212700013	sn=7273212700013 netty handle data error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.convertLettuceAccessException(LettuceStringCommands.java:799)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:148)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.set(DefaultedRedisConnection.java:287)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.set(DefaultStringRedisConnection.java:974)
	at org.springframework.data.redis.core.DefaultValueOperations$3.inRedis(DefaultValueOperations.java:240)
	at org.springframework.data.redis.core.AbstractOperations$ValueDeserializingRedisCallback.doInRedis(AbstractOperations.java:60)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:96)
	at org.springframework.data.redis.core.DefaultValueOperations.set(DefaultValueOperations.java:236)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.updateDevHeartbeat(AdmsCacheManager.java:371)
	at com.zkteco.zkbiosecurity.adms.distributor.push.processor.PushProcessor.requestData(PushProcessor.java:60)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:115)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead0(HttpServerHandler.java:35)
	at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:99)
	at com.zkteco.zkbiosecurity.adms.distributor.push.http.HttpServerHandler.channelRead(HttpServerHandler.java:150)
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:379)
	at io.netty.channel.AbstractChannelHandlerContext.access$600(AbstractChannelHandlerContext.java:61)
	at io.netty.channel.AbstractChannelHandlerContext$7.run(AbstractChannelHandlerContext.java:370)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.set(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceStringCommands.set(LettuceStringCommands.java:146)
	... 22 common frames omitted
2025-07-25 17:24:55.599 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy324.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
