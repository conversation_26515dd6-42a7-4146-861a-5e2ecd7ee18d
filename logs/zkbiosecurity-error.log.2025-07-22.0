2025-07-22 08:57:56.796 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-22 08:57:56.797 [AccQueryDataThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:153) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccQueryDataThread.run(AccCommunicationDataProcessor.java:141)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-22 08:57:56.798 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-22 08:57:56.797 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 08:57:56.798 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-22 09:22:12.231 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 09:22:12.891 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-22 11:39:00.801 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-22 11:39:00.818 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-22 11:39:07.211 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-22 11:39:08.925 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-22 11:39:23.337 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy277.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-22 13:53:04.254 [main] ERROR com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit-(AccUpgradeInit.java:39) - AccUpgradeInit Exception {}
org.springframework.jdbc.BadSqlGrammarException: StatementCallback; bad SQL grammar [ALTER TABLE acc_interlock DROP CONSTRAINT uk_a9ndse24x6wt65yb3nxhl18a9]; nested exception is org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:81)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1443)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:388)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:418)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.updateAccInterlock(AccVer3_11_0.java:118)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccVer3_11_0.executeUpgrade(AccVer3_11_0.java:70)
	at com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl.upgrade(BaseUpgradeVerHandlerServiceImpl.java:51)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.upgrade(AccUpgradeInit.java:57)
	at com.zkteco.zkbiosecurity.acc.data.upgrade.AccUpgradeInit.run(AccUpgradeInit.java:37)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:792)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:776)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:319)
	at com.zkteco.zkbiosecurity.BiosecurityUIApp.main(BiosecurityUIApp.java:13)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at com.intellij.rt.execution.CommandLineWrapper.main(CommandLineWrapper.java:65)
Caused by: org.postgresql.util.PSQLException: 错误: 关系 "acc_interlock" 的 约束"uk_a9ndse24x6wt65yb3nxhl18a9" 不存在
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2565)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2297)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:322)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:481)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:401)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:322)
	at org.postgresql.jdbc.PgStatement.executeCachedSql(PgStatement.java:308)
	at org.postgresql.jdbc.PgStatement.executeWithFlags(PgStatement.java:284)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:279)
	at com.alibaba.druid.pool.DruidPooledStatement.execute(DruidPooledStatement.java:632)
	at org.springframework.jdbc.core.JdbcTemplate$1ExecuteStatementCallback.doInStatement(JdbcTemplate.java:409)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:376)
	... 15 common frames omitted
2025-07-22 14:04:52.332 [http-nio-8098-exec-67] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:199 cont:"scheduleType":"0"
2025-07-22 14:04:52.332 [http-nio-8098-exec-67] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:198 cont:{"
2025-07-22 14:04:52.333 [http-nio-8098-exec-67] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:199 cont:"scheduleType":"0"
2025-07-22 14:04:52.333 [http-nio-8098-exec-67] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:198 cont:{"
2025-07-22 14:05:49.495 [http-nio-8098-exec-9] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:160 cont:"cycleType":"2","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:49.495 [http-nio-8098-exec-9] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:159 cont:{"
2025-07-22 14:05:49.496 [http-nio-8098-exec-9] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:160 cont:"cycleType":"2","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:49.497 [http-nio-8098-exec-9] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:159 cont:{"
2025-07-22 14:05:52.056 [http-nio-8098-exec-15] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:160 cont:"cycleType":"2","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"8280808198174b5f0198174c61040004","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:52.057 [http-nio-8098-exec-15] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:159 cont:{"
2025-07-22 14:05:52.057 [http-nio-8098-exec-15] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:160 cont:"cycleType":"2","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"8280808198174b5f0198174c61040004","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:52.057 [http-nio-8098-exec-15] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:159 cont:{"
2025-07-22 14:05:53.912 [http-nio-8098-exec-23] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:158 cont:"cycleType":"1","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"8280808198174b5f0198174c61040004","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:53.913 [http-nio-8098-exec-23] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:157 cont:{"
2025-07-22 14:05:53.913 [http-nio-8098-exec-23] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:158 cont:"cycleType":"1","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"8280808198174b5f0198174c61040004","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:53.913 [http-nio-8098-exec-23] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:157 cont:{"
2025-07-22 14:05:54.648 [http-nio-8098-exec-52] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:159 cont:"tempType":"2","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"8280808198174b5f0198174c61040004","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:54.648 [http-nio-8098-exec-52] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:158 cont:{"
2025-07-22 14:05:54.649 [http-nio-8098-exec-52] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:159 cont:"tempType":"2","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"8280808198174b5f0198174c61040004","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:54.649 [http-nio-8098-exec-52] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:158 cont:{"
2025-07-22 14:05:56.064 [http-nio-8098-exec-75] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:160 cont:"cycleType":"2","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"8280808198174b5f0198174c61040004","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:56.064 [http-nio-8098-exec-75] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:159 cont:{"
2025-07-22 14:05:56.064 [http-nio-8098-exec-75] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:160 cont:"cycleType":"2","startTime":"2025-07-22","endTime":"2025-08-22","deptId":"8280808198174b5f0198174c61040004","personPin":"","deptName":"","groupName":"","isIncludeLower"
2025-07-22 14:05:56.064 [http-nio-8098-exec-75] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:159 cont:{"
2025-07-22 14:27:16.125 [http-nio-8098-exec-32] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:199 cont:"scheduleType":"0"
2025-07-22 14:27:16.126 [http-nio-8098-exec-32] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:198 cont:{"
2025-07-22 14:27:16.126 [http-nio-8098-exec-32] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:199 cont:"scheduleType":"0"
2025-07-22 14:27:16.126 [http-nio-8098-exec-32] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:198 cont:{"
2025-07-22 14:38:50.698 [http-nio-8098-exec-10] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:199 cont:"scheduleType":"0"
2025-07-22 14:38:50.698 [http-nio-8098-exec-10] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:198 cont:{"
2025-07-22 14:38:50.698 [http-nio-8098-exec-10] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:199 cont:"scheduleType":"0"
2025-07-22 14:38:50.698 [http-nio-8098-exec-10] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:198 cont:{"
2025-07-22 14:39:02.053 [http-nio-8098-exec-50] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:199 cont:"scheduleType":"0"
2025-07-22 14:39:02.053 [http-nio-8098-exec-50] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:198 cont:{"
2025-07-22 14:39:02.054 [http-nio-8098-exec-50] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:199 cont:"scheduleType":"0"
2025-07-22 14:39:02.054 [http-nio-8098-exec-50] ERROR com.zkteco.zkbiosecurity.security.PatternConstants-(PatternConstants.java:213) - pos:198 cont:{"
2025-07-22 16:26:01.455 [AccQueryDataThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:153) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccQueryDataThread.run(AccCommunicationDataProcessor.java:141)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-22 16:36:23.842 [AccSavePersonThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:100) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccSavePersonThread.run(AccCommunicationDataProcessor.java:86)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-22 16:40:06.910 [AccQueryDataThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:153) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccQueryDataThread.run(AccCommunicationDataProcessor.java:141)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-22 17:49:24.320 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-22 17:49:24.358 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 17:49:24.601 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-22 17:49:25.096 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-22 17:49:25.097 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-22 17:56:48.241 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 17:56:55.075 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 17:56:55.201 [AccSavePersonThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:100) - AccSavePersonThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccSavePersonThread.run(AccCommunicationDataProcessor.java:86)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-22 17:56:59.808 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 18:11:08.691 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 18:11:08.959 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-22 18:11:09.549 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-22 18:11:09.549 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-22 18:11:26.342 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-22 18:11:26.349 [AttCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread-(AttCommunicationDataProcessor.java:116) - AttCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.att.cache.AttCacheManager.loadOperLogKeyList(AttCacheManager.java:105)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.handlerUserInfo(AttCommunicationDataProcessor.java:156)
	at com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread.run(AttCommunicationDataProcessor.java:81)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 9 common frames omitted
2025-07-22 18:11:26.661 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
2025-07-22 18:11:26.662 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-22 18:11:27.367 [AccRTLogSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:245) - Exception AccTransactionSaveThread : AccRTLogSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.exists(AccCacheManager.java:132)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccTransactionSaveThread.run(AccCommunicationDataProcessor.java:238)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-22 18:11:27.368 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 18:12:37.080 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 18:12:55.421 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
2025-07-22 18:12:55.422 [AccZonePersonSaveToDBThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor-(AccAdvancedCommunicationDataProcessor.java:52) - Exception AccZonePersonSaveThread : AccZonePersonSaveToDBThreaddeal rtLog error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:77)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:68)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:342)
	at org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$6(RedisTemplate.java:773)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:773)
	at com.zkteco.zkbiosecurity.acc.cache.AccAdvancedCacheManager.existsZonePerson(AccAdvancedCacheManager.java:154)
	at com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor$AccZonePersonSaveThread.run(AccAdvancedCommunicationDataProcessor.java:44)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.exists(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:75)
	... 8 common frames omitted
2025-07-22 18:12:55.521 [AccCommunicationDataHandlerThread] ERROR com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor-(AccCommunicationDataProcessor.java:200) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.acc.cache.AccCacheManager.getCacheKeySet(AccCacheManager.java:123)
	at com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor$AccCommunicationDataHandlerThread.run(AccCommunicationDataProcessor.java:191)
Caused by: io.lettuce.core.RedisCommandTimeoutException: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:131)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 8 common frames omitted
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.protocol.CommandExpiryWriter.lambda$potentiallyExpire$0(CommandExpiryWriter.java:172)
	at io.netty.util.concurrent.PromiseTask.runTask(PromiseTask.java:98)
	at io.netty.util.concurrent.ScheduledFutureTask.run(ScheduledFutureTask.java:170)
	at io.netty.util.concurrent.DefaultEventExecutor.run(DefaultEventExecutor.java:66)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:745)
2025-07-22 18:13:03.118 [AdmsCmdResultHandlerThread] ERROR com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask-(AdmsCmdReturnTask.java:75) - AccCommunicationDataHandlerThread Handler Data Error
org.springframework.dao.QueryTimeoutException: Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:70)
	at org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)
	at org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:44)
	at org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:42)
	at org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:275)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.convertLettuceAccessException(LettuceKeyCommands.java:809)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:226)
	at org.springframework.data.redis.connection.DefaultedRedisConnection.keys(DefaultedRedisConnection.java:110)
	at org.springframework.data.redis.connection.DefaultStringRedisConnection.keys(DefaultStringRedisConnection.java:658)
	at org.springframework.data.redis.core.RedisTemplate.lambda$keys$13(RedisTemplate.java:887)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:228)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:188)
	at org.springframework.data.redis.core.RedisTemplate.keys(RedisTemplate.java:887)
	at com.zkteco.zkbiosecurity.adms.cache.AdmsCacheManager.loadCmdReuslt(AdmsCacheManager.java:1019)
	at com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask$1.run(AdmsCmdReturnTask.java:48)
	at java.util.TimerThread.mainLoop(Timer.java:555)
	at java.util.TimerThread.run(Timer.java:505)
Caused by: io.lettuce.core.RedisCommandTimeoutException: Command timed out after 1 second(s)
	at io.lettuce.core.ExceptionFactory.createTimeoutException(ExceptionFactory.java:51)
	at io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:119)
	at io.lettuce.core.FutureSyncInvocationHandler.handleInvocation(FutureSyncInvocationHandler.java:75)
	at io.lettuce.core.internal.AbstractInvocationHandler.invoke(AbstractInvocationHandler.java:79)
	at com.sun.proxy.$Proxy290.keys(Unknown Source)
	at org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.keys(LettuceKeyCommands.java:224)
	... 10 common frames omitted
