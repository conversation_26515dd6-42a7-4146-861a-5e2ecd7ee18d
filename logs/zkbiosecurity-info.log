2025-08-04 10:45:11.145 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
2025-08-04 10:45:11.188 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - Starting BiosecurityUIApp on <PERSON> with PID 11300 (D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104\zkbiosecurity-startup\zkbiosecurity-startup-WioSecurityV6600_4.5.x_R\zkbiosecurity-startup-ui\target\classes started by maker in D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104)
2025-08-04 10:45:11.188 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - No active profile set, falling back to default profiles: default
2025-08-04 10:45:38.707 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:45:38.708 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04 10:45:40.937 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2217ms. Found 175 JPA repository interfaces.
2025-08-04 10:45:41.275 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:45:41.277 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 10:45:41.890 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusChildrenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.890 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.890 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.890 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiTokenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.891 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAppDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.892 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAreaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.892 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.893 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthCompanyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.893 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthDepartmentDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.893 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthPermissionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.893 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthRoleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.894 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthSecurityParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.894 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthShortCutMenuDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.894 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthUserDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.894 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.895 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportFieldDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.895 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDbBackupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.895 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.896 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryValueDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.896 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.896 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageResourceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.896 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.896 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMediaFileDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.896 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMessageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.897 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseOpLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.897 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.897 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.897 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseRegisterDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.897 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseSysParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.898 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.898 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegSliceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.898 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemClientInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.898 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemModuleInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.898 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemZoomDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.899 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAccDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.899 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAttDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.899 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAuthDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.899 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsCmdIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.899 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDevCmdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.899 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.900 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.900 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosAllowLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.900 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosBuyLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.900 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosFullLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.900 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsProductDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.900 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.901 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeExtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.901 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioPhotoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.901 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.902 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.902 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCertificateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.902 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIdentityCardInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.902 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIssueCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.902 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersLeavePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.903 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonChangeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.903 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.903 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonLinkDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.903 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnalListDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.903 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnallistPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.904 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPositionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.904 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersTempPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.904 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.904 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.904 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorBEventNumDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.904 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.905 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorHistoryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.905 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAntiPassbackDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.905 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAppTopDoorByPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.905 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.905 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.905 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenCombDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.906 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.906 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.906 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.906 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.906 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.907 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.907 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.907 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.907 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceEventDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.907 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.907 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceVerifyModeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.908 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.908 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.908 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccExceptionRecordDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.908 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccExtDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.908 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstInLastOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.908 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.908 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccHolidayDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.909 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccInOutRecordDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.909 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.909 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.909 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.909 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.909 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.910 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.910 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.910 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageInOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.910 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIndexDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.910 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.910 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.911 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.911 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.911 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapPosDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.911 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.911 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.911 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.911 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLastAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.912 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.912 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.912 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.912 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.912 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.912 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.913 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.913 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.913 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.913 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.913 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.913 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.913 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.914 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockMidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.914 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.914 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.914 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.914 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.914 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.915 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkagePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.915 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.915 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.915 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.915 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.915 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.915 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointSignPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.916 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccOccupancyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.916 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.916 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.916 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.916 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.916 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.916 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDetailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.917 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.917 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.917 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZonePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.917 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttAdjustDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.917 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttAreaPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.917 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttAutoExportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.918 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttBreakTimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.918 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttClassDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.918 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttCycleSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.918 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDayCardDetailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.918 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeptSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.919 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.919 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeviceOpLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.919 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.919 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.919 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttGroupSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.919 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttHolidayDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.920 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttLeaveDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.920 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttLeaveTypeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.920 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.920 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttOvertimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.920 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.921 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttPersonSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.921 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.921 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttRecordDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.921 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttShiftDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.921 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttSignAddressAreaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.921 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttSignAddressDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.921 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttSignDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.922 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTempSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.922 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTimeSlotDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.922 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTimingDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.922 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTransactionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.922 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTripDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:45:41.922 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 632ms. Found 0 Redis repository interfaces.
2025-08-04 10:45:42.465 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webParamValidateConfig' of type [com.zkteco.zkbiosecurity.core.config.WebParamValidateConfig$$EnhancerBySpringCGLIB$$76afbfd5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:45:42.484 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:45:43.621 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8098 (http)
2025-08-04 10:45:43.836 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 32591 ms
2025-08-04 10:45:43.971 [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-04 10:45:44.583 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-04 10:45:44.950 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04 10:45:45.100 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
2025-08-04 10:45:45.527 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-04 10:45:45.938 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: com.zkteco.zkbiosecurity.core.config.PostgreDialect
2025-08-04 10:45:51.603 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-04 10:45:51.628 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04 10:45:55.166 [main] INFO  com.zkteco.zkbiosecurity.core.web.listener.WebAppInitListener - WebAppInitListener init......
2025-08-04 10:45:57.366 [main] INFO  org.redisson.Version - Redisson 3.14.0
2025-08-04 10:45:58.884 [redisson-netty-4-15] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6380
2025-08-04 10:45:58.900 [redisson-netty-4-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6380
2025-08-04 10:46:06.617 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientInboundChannelExecutor'
2025-08-04 10:46:06.637 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-08-04 10:46:22.292 [main] INFO  com.zkteco.zkbiosecurity.core.config.LocaleConfig -  current  Locale = zh  LocaleTag=zh-CN
2025-08-04 10:46:22.293 [main] INFO  com.zkteco.zkbiosecurity.core.config.LocaleConfig - change current  Locale = zh change LocaleTag=zh-CN
2025-08-04 10:46:36.953 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-08-04 10:46:37.111 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'scheduler'
2025-08-04 10:46:37.190 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-08-04 10:46:37.438 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'brokerChannelExecutor'
2025-08-04 10:46:39.183 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'brokerChannelExecutor'
2025-08-04 10:46:39.184 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'messageBrokerTaskScheduler'
2025-08-04 10:46:39.185 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'scheduler'
2025-08-04 10:46:39.302 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'clientOutboundChannelExecutor'
2025-08-04 10:46:39.302 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'clientInboundChannelExecutor'
2025-08-04 10:46:39.577 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04 10:46:39.582 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-08-04 10:46:39.595 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-08-04 10:46:39.606 [main] INFO  com.zkteco.zkbiosecurity.core.web.listener.WebAppInitListener - WebAppInitListener destroy......
2025-08-04 10:46:39.628 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 10:50:31.955 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
2025-08-04 10:50:32.052 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - Starting BiosecurityUIApp on Yang with PID 13992 (D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104\zkbiosecurity-startup\zkbiosecurity-startup-WioSecurityV6600_4.5.x_R\zkbiosecurity-startup-ui\target\classes started by maker in D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104)
2025-08-04 10:50:32.054 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - No active profile set, falling back to default profiles: default
2025-08-04 10:50:38.476 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:50:38.477 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04 10:50:40.750 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2261ms. Found 175 JPA repository interfaces.
2025-08-04 10:50:41.142 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 10:50:41.144 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 10:50:41.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusChildrenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.814 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.814 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiTokenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.815 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAppDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.815 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAreaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.816 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.816 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthCompanyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.816 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthDepartmentDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.816 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthPermissionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.817 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthRoleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.817 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthSecurityParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.817 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthShortCutMenuDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.817 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthUserDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.817 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.818 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportFieldDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.818 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDbBackupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.818 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.819 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryValueDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.819 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.819 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageResourceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.819 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.819 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMediaFileDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMessageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseOpLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseRegisterDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.820 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseSysParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegSliceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemClientInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemModuleInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemZoomDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAccDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.821 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAttDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.822 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAuthDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.822 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsCmdIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.822 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDevCmdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.822 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.822 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.822 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosAllowLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.823 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosBuyLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.823 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosFullLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.823 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsProductDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.823 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.824 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeExtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.824 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioPhotoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.824 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.824 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.824 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCertificateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.824 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIdentityCardInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.825 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIssueCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.825 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersLeavePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.825 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonChangeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.825 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.825 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonLinkDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.825 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnalListDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.826 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnallistPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.826 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPositionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.826 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersTempPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.826 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.826 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.826 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorBEventNumDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.827 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.827 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorHistoryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.827 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAntiPassbackDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.827 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAppTopDoorByPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.827 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.827 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.828 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenCombDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.828 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.828 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.828 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.828 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.828 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.829 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.829 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.829 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.829 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceEventDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.829 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.829 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceVerifyModeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.829 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.830 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.830 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccExceptionRecordDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.830 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccExtDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.830 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstInLastOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.830 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.830 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccHolidayDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccInOutRecordDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.831 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.832 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.832 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageInOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.832 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIndexDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.832 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.832 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.832 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.832 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.833 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapPosDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.833 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.833 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.833 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.833 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLastAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.833 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.833 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.833 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.834 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.834 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.834 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.834 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.834 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.834 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.834 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.835 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.835 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.835 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.835 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockMidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.835 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.835 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.835 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.836 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.836 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.836 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkagePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.836 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.836 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.836 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.836 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.837 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.837 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointSignPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.837 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccOccupancyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.837 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.837 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.837 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.837 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.838 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.838 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDetailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.838 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.838 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.838 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZonePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.838 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttAdjustDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.838 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttAreaPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.839 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttAutoExportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.839 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttBreakTimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.839 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttClassDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.839 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttCycleSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDayCardDetailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeptSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeviceOpLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.840 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttGroupSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.841 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttHolidayDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.841 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttLeaveDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.841 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttLeaveTypeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.841 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.841 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttOvertimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.841 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttPersonSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttRecordDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttShiftDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttSignAddressAreaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttSignAddressDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttSignDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.842 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTempSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.843 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTimeSlotDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.843 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTimingDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.843 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTransactionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.843 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTripDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 10:50:41.843 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 684ms. Found 0 Redis repository interfaces.
2025-08-04 10:50:42.364 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webParamValidateConfig' of type [com.zkteco.zkbiosecurity.core.config.WebParamValidateConfig$$EnhancerBySpringCGLIB$$33f6d859] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:50:42.383 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 10:50:43.524 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8098 (http)
2025-08-04 10:50:43.740 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 11610 ms
2025-08-04 10:50:43.877 [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-04 10:50:44.460 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-04 10:50:44.818 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04 10:50:44.949 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
2025-08-04 10:50:45.436 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-04 10:50:45.851 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: com.zkteco.zkbiosecurity.core.config.PostgreDialect
2025-08-04 10:50:51.191 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-04 10:50:51.218 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04 10:50:54.584 [main] INFO  com.zkteco.zkbiosecurity.core.web.listener.WebAppInitListener - WebAppInitListener init......
2025-08-04 10:50:56.750 [main] INFO  org.redisson.Version - Redisson 3.14.0
2025-08-04 10:50:58.280 [redisson-netty-4-22] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6380
2025-08-04 10:50:58.296 [redisson-netty-4-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6380
2025-08-04 10:51:06.237 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientInboundChannelExecutor'
2025-08-04 10:51:06.255 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-08-04 10:51:21.900 [main] INFO  com.zkteco.zkbiosecurity.core.config.LocaleConfig -  current  Locale = zh  LocaleTag=zh-CN
2025-08-04 10:51:21.901 [main] INFO  com.zkteco.zkbiosecurity.core.config.LocaleConfig - change current  Locale = zh change LocaleTag=zh-CN
2025-08-04 10:51:35.985 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-08-04 10:51:36.119 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'scheduler'
2025-08-04 10:51:36.195 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-08-04 10:51:36.411 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'brokerChannelExecutor'
2025-08-04 10:51:38.131 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8098 (http) with context path ''
2025-08-04 10:51:41.020 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-08-04 10:51:41.021 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [DefaultSubscriptionRegistry[cache[0 destination(s)], registry[0 sessions]]]]
2025-08-04 10:51:41.022 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Started.
2025-08-04 10:51:41.057 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - More than one TaskScheduler bean exists within the context, and none is named 'taskScheduler'. Mark one of them as primary or name it 'taskScheduler' (possibly as an alias); or implement the SchedulingConfigurer interface and call ScheduledTaskRegistrar#setScheduler explicitly within the configureTasks() callback: [scheduler, messageBrokerTaskScheduler]
2025-08-04 10:51:41.085 [main] INFO  com.zkteco.zkbiosecurity.guard.biz.LicenseCheckBiz -  trigger cron config 59 59 23 * * ?
2025-08-04 10:51:41.098 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: a****s
2025-08-04 10:51:41.099 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: a******a
2025-08-04 10:51:41.099 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: w*********y
2025-08-04 10:51:41.099 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: a*******s
2025-08-04 10:51:41.099 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: h****i
2025-08-04 10:51:41.099 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: l********n
2025-08-04 10:51:41.099 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: t******e
2025-08-04 10:51:41.121 [main] INFO  com.zkteco.zkbiosecurity.system.web.listener.SystemParamsInitListener - Init custom date format...
2025-08-04 10:51:41.132 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - Started BiosecurityUIApp in 69.786 seconds (JVM running for 71.758)
2025-08-04 10:51:41.696 [main] INFO  com.zkteco.guard.security.LicenseLoader - create license context success
2025-08-04 10:51:41.747 [main] INFO  com.zkteco.guard.security.LicenseLoader - check app control count match
2025-08-04 10:51:41.749 [main] INFO  com.zkteco.guard.security.LicenseLoader - check apppers control count match
2025-08-04 10:51:41.750 [main] INFO  com.zkteco.guard.security.LicenseLoader - check printcard control count match
2025-08-04 10:51:41.751 [main] INFO  com.zkteco.guard.security.LicenseLoader - check visprintcard control count match
2025-08-04 10:51:41.753 [main] INFO  com.zkteco.guard.security.LicenseLoader - check hotelidreader control count match
2025-08-04 10:51:41.754 [main] INFO  com.zkteco.guard.security.LicenseLoader - check persidreader control count match
2025-08-04 10:51:41.756 [main] INFO  com.zkteco.guard.security.LicenseLoader - check visidreader control count match
2025-08-04 10:51:41.757 [main] INFO  com.zkteco.guard.security.LicenseLoader - check hotelocr control count match
2025-08-04 10:51:41.759 [main] INFO  com.zkteco.guard.security.LicenseLoader - check ocr control count match
2025-08-04 10:51:41.760 [main] INFO  com.zkteco.guard.security.LicenseLoader - check visocr control count match
2025-08-04 10:51:41.761 [main] INFO  com.zkteco.guard.security.LicenseLoader - check vissignature control count match
2025-08-04 10:51:41.892 [main] INFO  com.zkteco.guard.security.LicenseLoader - pullGateCount:0,pullDevCount:0,pushGateCount:2,pushDevCount:2,hasC3:0
2025-08-04 10:51:41.892 [main] INFO  com.zkteco.guard.security.LicenseLoader - pullControlCount:0,pushControlCount:2,isSupportC3:false
2025-08-04 10:51:41.892 [main] INFO  com.zkteco.guard.security.LicenseLoader - check acc control count match
2025-08-04 10:51:42.067 [main] INFO  com.zkteco.guard.security.LicenseLoader - check att control count match
2025-08-04 10:51:42.068 [main] INFO  com.zkteco.zkbiosecurity.license.init.LicenseInit - LicenseId is null,so can not start  cloud websocket!
2025-08-04 10:51:46.940 [main] INFO  com.zkteco.zkbiosecurity.auth.service.impl.AuthBioVerifyServiceImpl - Connect the finger service failed!
2025-08-04 10:51:51.127 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl - ---------------- detect face service already exists is: false
2025-08-04 10:51:51.943 [main] INFO  com.zkteco.zkbiosecurity.att.task.AttTimingDateCleanTask - AttTimingDateCleanTask Init ... 
2025-08-04 10:51:51.956 [main] INFO  com.zkteco.zkbiosecurity.att.task.AttTimingUpdateDayCardTask - AttTimingUpdateDayCardTask Init
2025-08-04 10:51:51.966 [main] INFO  com.zkteco.zkbiosecurity.att.task.AttRealTimeCalculationTask - AttRealTimeCalculationTask Init
2025-08-04 10:51:51.976 [main] INFO  com.zkteco.zkbiosecurity.att.task.AttUpdateRealTimeCacheTask - AttUpdateRealTimeCacheTask Init
2025-08-04 10:51:52.142 [main] INFO  com.zkteco.zkbiosecurity.adms.service.impl.AdmsSdkServiceImpl - Kill RS485Process failed by PULL!
2025-08-04 10:51:52.549 [main] INFO  com.zkteco.zkbiosecurity.adms.task.AdmsCmdDataSaveTask - AdmsCmdDataHandlerThread Start Handler Data......
2025-08-04 10:51:52.554 [main] INFO  com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask - AdmsCmdResultHandlerThread Start Handler Data......
2025-08-04 10:51:53.220 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = system current version = v3.14.0
2025-08-04 10:51:53.324 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.5.0 ->  v2.6.0. Looking for module system update success!
2025-08-04 10:51:53.627 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.6.0 ->  v2.9.0. Looking for module system update success!
2025-08-04 10:51:53.634 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.9.0 ->  v2.10.0. Looking for module system update success!
2025-08-04 10:51:53.638 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.10.0 ->  v3.0.1. Looking for module system update success!
2025-08-04 10:51:53.837 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.0.1 ->  v3.1.0. Looking for module system update success!
2025-08-04 10:51:53.891 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.1.0 ->  v3.2.0. Looking for module system update success!
2025-08-04 10:51:53.892 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.2.0 ->  v3.2.2. Looking for module system update success!
2025-08-04 10:51:54.068 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.2.2 ->  v3.3.2. Looking for module system update success!
2025-08-04 10:51:54.071 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.3.2 ->  v3.4.0. Looking for module system update success!
2025-08-04 10:51:54.076 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.4.0 ->  v3.6.0. Looking for module system update success!
2025-08-04 10:51:56.184 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.6.0 ->  v3.7.0. Looking for module system update success!
2025-08-04 10:51:56.271 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.7.0 ->  v3.8.0. Looking for module system update success!
2025-08-04 10:51:56.281 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.8.0 ->  v3.9.0. Looking for module system update success!
2025-08-04 10:51:56.295 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.9.0 ->  v3.10.0. Looking for module system update success!
2025-08-04 10:51:56.303 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.10.0 ->  v3.10.2. Looking for module system update success!
2025-08-04 10:51:56.377 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.10.2 ->  v3.11.0. Looking for module system update success!
2025-08-04 10:51:56.471 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.11.0 ->  v3.12.0. Looking for module system update success!
2025-08-04 10:51:56.488 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.12.0 ->  v3.13.0. Looking for module system update success!
2025-08-04 10:51:56.947 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = adms current version = v3.13.0
2025-08-04 10:51:56.948 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = pers current version = v3.14.0
2025-08-04 10:51:56.950 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = acc current version = v3.13.0
2025-08-04 10:51:56.957 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v1.0.0 ->  v2.4.0. Looking for module acc update success!
2025-08-04 10:51:56.960 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.4.0 ->  v2.6.0. Looking for module acc update success!
2025-08-04 10:51:57.057 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.6.0 ->  v3.0.0. Looking for module acc update success!
2025-08-04 10:51:57.057 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.0.0 ->  v3.0.3. Looking for module acc update success!
2025-08-04 10:51:57.057 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.0.3 ->  v3.1.1. Looking for module acc update success!
2025-08-04 10:51:57.097 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.1.1 ->  v3.1.2. Looking for module acc update success!
2025-08-04 10:51:57.201 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.1.2 ->  v3.2.0. Looking for module acc update success!
2025-08-04 10:51:57.202 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.2.0 ->  v3.3.0. Looking for module acc update success!
2025-08-04 10:51:57.246 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.3.0 ->  v3.5.0. Looking for module acc update success!
2025-08-04 10:51:57.246 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.5.0 ->  v3.6.0. Looking for module acc update success!
2025-08-04 10:51:57.476 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.6.0 ->  v3.8.0. Looking for module acc update success!
2025-08-04 10:51:57.486 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.8.0 ->  v3.10.0. Looking for module acc update success!
2025-08-04 10:51:57.885 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = acc-advanced current version = v3.12.0
2025-08-04 10:51:57.886 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = att current version = null
2025-08-04 10:51:57.902 [main] INFO  com.zkteco.zkbiosecurity.adms.init.AdmsDeviceCacheInit - adms staring init device info......
2025-08-04 10:51:57.939 [main] INFO  com.zkteco.zkbiosecurity.adms.init.AdmsDeviceCacheInit - adms end init device info
2025-08-04 10:51:57.943 [PullServer-Thread] INFO  com.zkteco.zkbiosecurity.adms.distributor.pull.PullServer - Pull Server start...
2025-08-04 10:51:57.948 [PushServer-Thread] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.PushServer - Push Server listening on 8088 port...
2025-08-04 10:51:57.948 [PushServer-Thread] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.PushServer - Starting Push Server ......
2025-08-04 10:51:58.516 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - pers-permission.xml ZKBioSecurity Element is empty
2025-08-04 10:51:58.577 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - acc-permission.xml ZKBioSecurity Element is empty
2025-08-04 10:51:58.602 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - att-permission.xml ZKBioSecurity Element is empty
2025-08-04 10:51:58.605 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - system-permission.xml ZKBioSecurity Element is empty
2025-08-04 10:52:00.015 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 10:52:00.154 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttRealTimeCalculationTask - AttRealTimeCalculationTask realTimeCalculation isCalculateAll
2025-08-04 10:52:00.581 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:52:00.581 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:52:00.582 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:52:00.582 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:52:00.837 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 10:52:01.046 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.service.impl.AttRecordServiceImpl - delAndSaveRecord pinList = [23212, 23211, 23213, 23214]
2025-08-04 10:52:01.150 [AccCommunicationDataHandlerThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccCommunicationDataHandlerThread Start Handler Data......
2025-08-04 10:52:01.162 [AccRTLogSaveToDBThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccTransactionSaveThread Start Handler Data......
2025-08-04 10:52:01.519 [AccZonePersonSaveToDBThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor - AccZonePersonSaveThread start......
2025-08-04 10:52:21.164 [AccSavePersonThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccSavePersonThread start ......
2025-08-04 10:52:21.737 [AttCommunicationDataHandlerThread] INFO  com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread - AttCommunicationDataHandlerThread Start Handler Data......
2025-08-04 10:52:26.164 [AccQueryDataThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccQueryDataThread start ......
2025-08-04 10:52:36.424 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-04 10:53:00.005 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 10:53:00.012 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:53:00.013 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:53:00.014 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:53:00.014 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:53:00.028 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 10:54:00.004 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 10:54:00.008 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:54:00.009 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:54:00.012 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:54:00.012 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:54:00.020 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 10:55:00.022 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 10:55:00.031 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:55:00.032 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:55:00.032 [pool-2-thread-1] INFO  org.springframework.scheduling.annotation.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [scheduler, clientInboundChannelExecutor, clientOutboundChannelExecutor, brokerChannelExecutor, messageBrokerTaskScheduler]
2025-08-04 10:55:00.035 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:55:00.035 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:55:00.058 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 10:56:00.013 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 10:56:00.023 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:56:00.023 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:56:00.037 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 10:57:00.009 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 10:57:00.019 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:57:00.019 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:57:00.035 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 10:58:00.008 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 10:58:00.018 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:58:00.018 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:58:00.026 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 10:59:00.016 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 10:59:00.019 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:59:00.019 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 10:59:00.029 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:00:00.015 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:00:00.022 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:00:00.022 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:00:00.039 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:01:00.012 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:01:00.019 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:01:00.019 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:01:00.031 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:02:00.002 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:02:00.009 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:02:00.009 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:02:00.017 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:03:00.010 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:03:00.024 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:03:00.069 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:03:00.069 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:03:55.753 [http-nio-8098-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 11:03:55.798 [http-nio-8098-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 45 ms
2025-08-04 11:04:00.002 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:04:00.005 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:04:00.005 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:04:00.012 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:04:12.401 [http-nio-8098-exec-27] INFO  com.zkteco.zkbiosecurity.auth.service.impl.AuthBioVerifyServiceImpl - Connect the finger service failed!
2025-08-04 11:05:00.015 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:05:00.017 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:05:00.017 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:05:00.028 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:05:36.523 [http-nio-8098-exec-76] INFO  com.zkteco.zkbiosecurity.auth.service.impl.AuthBioVerifyServiceImpl - Connect the finger service failed!
2025-08-04 11:06:00.013 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:06:00.019 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:06:00.020 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - 已经推送给课长 推送完成了 不再进行检测，等待人员进入才能完成闭环
2025-08-04 11:06:00.025 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:06:07.627 [http-nio-8098-exec-3] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 请求微信接口获取openid，URL: https://api.weixin.qq.com/sns/oauth2/access_token?appid=wxc71eb85bdaee3f9e&secret=2e6d9203214db1c5412adbf9f70327d2&code=081Khq0009tbJU15W22009VLyu2Khq0N&grant_type=authorization_code
2025-08-04 11:06:08.181 [http-nio-8098-exec-3] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 微信接口响应: {"access_token":"94_Y0linsYJxFg5a4FQsSm0BO35X2IKXj9-tHHiykIDIb0tp9UdPVdU59QRRypnodxQkAmfwg2umU53cTd1hUpC0OerYehQ_Z8-0Tzl-iaWhh0","expires_in":7200,"refresh_token":"94_1RrimDTNJKP8f4HsDUx3rTHJTRQ2RdAT_0b-BzZ3hb0_-DdxMXMBalclgFz5JQKsgbyGMt2Gymkj0X3sjo7Ga1Cc9gB3YHebUbP1KnI1RpU","openid":"o5NekvgHdhsSCEJX4WBf2X9ihMpc","scope":"snsapi_base"}
2025-08-04 11:06:08.181 [http-nio-8098-exec-3] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 成功获取openid: o5NekvgHdhsSCEJX4WBf2X9ihMpc
2025-08-04 11:06:08.184 [http-nio-8098-exec-3] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 成功保存用户openid，pin: 23212, openId: o5NekvgHdhsSCEJX4WBf2X9ihMpc
2025-08-04 11:06:55.515 [http-nio-8098-exec-26] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 成功清除用户openid，pin: 23212
2025-08-04 11:07:00.016 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:07:00.056 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:07:19.663 [http-nio-8098-exec-19] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 请求微信接口获取openid，URL: https://api.weixin.qq.com/sns/oauth2/access_token?appid=wxc71eb85bdaee3f9e&secret=2e6d9203214db1c5412adbf9f70327d2&code=061Gy02w3FZ8q53SbU2w3F2l5f3Gy02u&grant_type=authorization_code
2025-08-04 11:07:19.916 [http-nio-8098-exec-19] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 微信接口响应: {"access_token":"94_vlQPFmDpmHzf8KU8_M_qGXE3FmgRTYDEMpgeI_2XArz3cp_innekBu9HGaYmmMGWFlgQP86Td95CYWysIfRqqTLybzJhPFlEaiaeAujyOzE","expires_in":7200,"refresh_token":"94_Y0linsYJxFg5a4FQsSm0BI0MHN_uVS8eGFpfhXVnEwNLGsY8h1YFPjRNhauLF9nfnOUsz5nh2643ZKiM8z2btKbMlf4ffpT0m7_fhFi87JI","openid":"o5NekvgHdhsSCEJX4WBf2X9ihMpc","scope":"snsapi_base"}
2025-08-04 11:07:19.916 [http-nio-8098-exec-19] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 成功获取openid: o5NekvgHdhsSCEJX4WBf2X9ihMpc
2025-08-04 11:07:19.918 [http-nio-8098-exec-19] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 成功保存用户openid，pin: 23212, openId: o5NekvgHdhsSCEJX4WBf2X9ihMpc
2025-08-04 11:07:44.107 [http-nio-8098-exec-29] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 成功清除用户openid，pin: 23212
2025-08-04 11:08:00.012 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:08:00.028 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:09:00.008 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:09:00.020 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:10:00.019 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:10:00.034 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:11:00.018 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:11:00.029 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:12:00.006 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:12:00.019 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:13:00.015 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:13:00.024 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:14:00.013 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:14:00.022 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:15:00.013 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:15:00.022 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:16:00.003 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:16:00.013 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:17:00.016 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:17:00.026 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:18:00.010 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:18:00.025 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:19:00.007 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:19:00.016 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:20:00.012 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:20:00.025 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:21:00.013 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:21:00.023 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:22:00.012 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:22:00.021 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:22:36.435 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-08-04 11:23:00.027 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:23:00.044 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:24:00.007 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:24:00.017 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:25:00.014 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:25:00.022 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:26:00.008 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:26:00.019 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:27:00.009 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:27:00.019 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:28:00.135 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:28:00.162 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:29:00.030 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:29:00.038 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:30:00.017 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:30:00.031 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:31:00.013 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:31:00.021 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:32:00.016 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:32:00.029 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:33:00.056 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:33:00.094 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:34:00.011 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:34:00.035 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:35:00.009 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:35:00.025 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:36:00.015 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:36:00.025 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:37:00.004 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:37:00.014 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:38:00.015 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:38:00.026 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:39:00.003 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:39:00.017 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:40:00.009 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:40:00.020 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:41:00.015 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:41:00.025 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:42:00.004 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:42:00.017 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:43:00.010 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:43:00.021 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:44:00.005 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:44:00.015 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:45:00.011 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:45:00.036 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:46:00.003 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:46:00.015 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:47:00.016 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:47:00.025 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:48:00.004 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:48:00.015 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:49:00.014 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:49:00.025 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:50:00.001 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:50:00.016 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:51:00.010 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:51:00.022 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:52:00.009 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:52:00.020 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:52:36.440 [MessageBroker-2] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-08-04 11:53:00.012 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:53:00.024 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:54:00.004 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:54:00.015 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:55:00.012 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:55:00.024 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:56:00.005 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:56:00.017 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:57:00.015 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:57:00.030 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:58:00.010 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:58:00.022 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 11:59:00.012 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 11:59:00.023 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:00:00.002 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:00:00.017 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:01:00.009 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:01:00.047 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:02:00.010 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:02:00.022 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:03:00.006 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:03:00.018 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:04:00.003 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:04:00.015 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:05:00.006 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:05:00.017 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:06:00.004 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:06:00.017 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:07:00.013 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:07:00.024 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:08:00.013 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:08:00.026 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:09:00.004 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:09:00.015 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:10:00.020 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:10:00.032 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:11:00.004 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:11:00.016 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:12:00.007 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:12:00.019 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:13:00.013 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:13:00.024 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:14:00.015 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:14:00.025 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:15:00.003 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:15:00.016 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:16:00.012 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:16:00.023 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:17:00.004 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:17:00.015 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:18:00.017 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:18:00.027 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:19:00.004 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:19:00.016 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:20:00.008 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:20:00.019 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:21:00.011 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:21:00.022 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:22:00.016 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:22:00.027 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:22:36.447 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-08-04 12:23:00.002 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:23:00.017 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:24:00.011 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:24:00.022 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:25:00.006 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:25:00.018 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:26:00.015 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:26:00.028 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:27:00.012 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:27:00.024 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:28:00.006 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:28:00.016 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:29:00.006 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:29:00.019 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:30:00.010 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:30:00.024 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:31:00.005 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:31:00.019 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:32:00.004 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:32:00.014 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:33:00.003 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:33:00.018 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:34:00.006 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:34:00.016 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:35:00.017 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:35:00.028 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:36:00.006 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:36:00.017 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:37:00.018 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:37:00.031 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:38:00.002 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:38:00.013 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:39:00.012 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:39:00.024 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:40:00.011 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:40:00.025 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:41:00.012 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:41:00.028 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:42:00.014 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:42:00.026 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:43:00.011 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:43:00.023 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:44:00.013 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:44:00.025 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:45:00.016 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:45:00.027 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:46:00.008 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:46:00.019 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:47:00.002 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:47:00.014 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:48:00.014 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:48:00.024 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:49:00.012 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:49:00.023 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:50:00.009 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:50:00.021 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:51:00.005 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:51:00.019 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:52:00.013 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:52:00.025 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:52:36.455 [MessageBroker-3] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-08-04 12:53:00.015 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:53:00.026 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:54:00.015 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:54:00.026 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:55:00.014 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:55:00.026 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:56:00.012 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:56:00.023 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:57:00.010 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:57:00.021 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:58:00.012 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:58:00.031 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 12:59:00.015 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 12:59:00.031 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:00:00.003 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:00:00.016 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:01:00.017 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:01:00.029 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:02:00.001 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:02:00.017 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:03:00.007 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:03:00.018 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:04:00.006 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:04:00.021 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:05:00.015 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:05:00.027 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:06:00.013 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:06:00.025 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:07:00.011 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:07:00.022 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:31:40.902 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:31:40.914 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:32:02.231 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:32:02.242 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:33:00.010 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:33:00.021 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:34:00.007 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:34:00.019 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:35:00.014 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:35:00.026 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:36:00.010 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:36:00.022 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:37:00.016 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:37:00.027 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:38:00.011 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:38:00.022 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:39:00.007 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:39:00.159 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:40:00.005 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:40:00.016 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:41:00.071 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:41:00.081 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:42:00.007 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:42:00.020 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:43:00.002 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:43:00.014 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:44:00.009 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:44:00.021 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:45:00.010 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:45:00.022 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:45:21.550 [SpringContextShutdownHook] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-08-04 13:45:21.550 [SpringContextShutdownHook] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [DefaultSubscriptionRegistry[cache[0 destination(s)], registry[0 sessions]]]]
2025-08-04 13:45:21.550 [SpringContextShutdownHook] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-08-04 13:45:35.232 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
2025-08-04 13:45:35.279 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - Starting BiosecurityUIApp on Yang with PID 24912 (D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104\zkbiosecurity-startup\zkbiosecurity-startup-WioSecurityV6600_4.5.x_R\zkbiosecurity-startup-ui\target\classes started by maker in D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104)
2025-08-04 13:45:35.280 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - No active profile set, falling back to default profiles: default
2025-08-04 13:45:42.201 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 13:45:42.201 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04 13:45:44.515 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2298ms. Found 175 JPA repository interfaces.
2025-08-04 13:45:44.910 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 13:45:44.912 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 13:45:45.607 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusChildrenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.608 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.608 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.608 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiTokenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.609 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAppDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.610 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAreaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.612 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.613 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthCompanyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.613 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthDepartmentDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.613 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthPermissionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.613 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthRoleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.613 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthSecurityParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.614 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthShortCutMenuDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.614 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthUserDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.614 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.615 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportFieldDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.615 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDbBackupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.615 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.615 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryValueDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.616 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.616 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageResourceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.616 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.616 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMediaFileDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.616 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMessageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.617 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseOpLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.617 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.617 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.617 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseRegisterDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.617 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseSysParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.617 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.618 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegSliceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.618 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemClientInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.618 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemModuleInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.618 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemZoomDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.618 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAccDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.618 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAttDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.618 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAuthDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.619 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsCmdIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.619 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDevCmdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.619 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.619 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.619 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosAllowLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.619 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosBuyLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.619 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosFullLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.620 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsProductDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.620 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.620 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeExtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.620 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioPhotoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.620 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.620 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.621 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCertificateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.621 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIdentityCardInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.621 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIssueCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.621 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersLeavePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.621 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonChangeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.621 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.622 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonLinkDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.622 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnalListDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.622 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnallistPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.622 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPositionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.622 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersTempPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.622 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorBEventNumDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorHistoryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAntiPassbackDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAppTopDoorByPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.624 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.624 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenCombDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.624 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.624 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.624 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.624 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.625 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.626 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.626 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.626 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.627 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceEventDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.627 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.627 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceVerifyModeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.627 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.627 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.627 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccExceptionRecordDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.627 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccExtDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.628 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstInLastOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.628 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.628 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccHolidayDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.628 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccInOutRecordDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.628 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.628 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.628 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.629 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.629 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.629 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.629 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.629 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageInOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.629 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIndexDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.630 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.630 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.630 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.630 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.630 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapPosDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.630 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.630 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLastAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.631 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.632 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.632 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.632 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.632 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.632 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.632 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.632 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.633 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.633 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockMidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.633 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.633 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.633 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.633 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.634 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.634 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkagePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.634 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.634 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.634 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.634 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.635 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.635 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointSignPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.635 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccOccupancyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.635 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.635 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.635 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.635 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.636 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.636 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDetailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.636 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.636 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.636 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZonePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.636 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttAdjustDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.636 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttAreaPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.637 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttAutoExportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.637 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttBreakTimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.637 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttClassDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.637 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttCycleSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.637 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDayCardDetailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.637 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeptSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeviceOpLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttGroupSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttHolidayDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.638 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttLeaveDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.639 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttLeaveTypeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.639 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.639 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttOvertimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.639 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.639 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttPersonSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.640 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.640 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttRecordDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.640 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttShiftDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.640 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttSignAddressAreaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.640 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttSignAddressDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.640 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttSignDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.641 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTempSchDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.641 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTimeSlotDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.641 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTimingDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.641 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTransactionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.641 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.att.dao.AttTripDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-08-04 13:45:45.641 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 713ms. Found 0 Redis repository interfaces.
2025-08-04 13:45:46.234 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webParamValidateConfig' of type [com.zkteco.zkbiosecurity.core.config.WebParamValidateConfig$$EnhancerBySpringCGLIB$$4846dd7f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 13:45:46.254 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 13:45:47.546 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8098 (http)
2025-08-04 13:45:47.797 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 12446 ms
2025-08-04 13:45:47.958 [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-08-04 13:45:48.611 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-08-04 13:45:48.995 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04 13:45:49.136 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
2025-08-04 13:45:49.627 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-04 13:45:50.116 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: com.zkteco.zkbiosecurity.core.config.PostgreDialect
2025-08-04 13:45:55.955 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-04 13:45:55.982 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04 13:45:59.593 [main] INFO  com.zkteco.zkbiosecurity.core.web.listener.WebAppInitListener - WebAppInitListener init......
2025-08-04 13:46:01.861 [main] INFO  org.redisson.Version - Redisson 3.14.0
2025-08-04 13:46:03.553 [redisson-netty-4-22] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6380
2025-08-04 13:46:03.572 [redisson-netty-4-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6380
2025-08-04 13:46:11.518 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientInboundChannelExecutor'
2025-08-04 13:46:11.536 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-08-04 13:46:31.916 [main] INFO  com.zkteco.zkbiosecurity.core.config.LocaleConfig -  current  Locale = zh  LocaleTag=zh-CN
2025-08-04 13:46:31.916 [main] INFO  com.zkteco.zkbiosecurity.core.config.LocaleConfig - change current  Locale = zh change LocaleTag=zh-CN
2025-08-04 13:46:54.313 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-08-04 13:46:54.472 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'scheduler'
2025-08-04 13:46:54.570 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-08-04 13:46:54.835 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'brokerChannelExecutor'
2025-08-04 13:46:56.705 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8098 (http) with context path ''
2025-08-04 13:46:59.694 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-08-04 13:46:59.694 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [DefaultSubscriptionRegistry[cache[0 destination(s)], registry[0 sessions]]]]
2025-08-04 13:46:59.694 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Started.
2025-08-04 13:46:59.718 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - More than one TaskScheduler bean exists within the context, and none is named 'taskScheduler'. Mark one of them as primary or name it 'taskScheduler' (possibly as an alias); or implement the SchedulingConfigurer interface and call ScheduledTaskRegistrar#setScheduler explicitly within the configureTasks() callback: [scheduler, messageBrokerTaskScheduler]
2025-08-04 13:46:59.747 [main] INFO  com.zkteco.zkbiosecurity.guard.biz.LicenseCheckBiz -  trigger cron config 59 59 23 * * ?
2025-08-04 13:46:59.761 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: a****s
2025-08-04 13:46:59.761 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: a******a
2025-08-04 13:46:59.761 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: w*********y
2025-08-04 13:46:59.761 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: a*******s
2025-08-04 13:46:59.762 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: h****i
2025-08-04 13:46:59.762 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: l********n
2025-08-04 13:46:59.762 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: t******e
2025-08-04 13:46:59.784 [main] INFO  com.zkteco.zkbiosecurity.system.web.listener.SystemParamsInitListener - Init custom date format...
2025-08-04 13:46:59.797 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - Started BiosecurityUIApp in 85.022 seconds (JVM running for 86.136)
2025-08-04 13:47:00.349 [main] INFO  com.zkteco.guard.security.LicenseLoader - create license context success
2025-08-04 13:47:00.375 [main] INFO  com.zkteco.guard.security.LicenseLoader - check app control count match
2025-08-04 13:47:00.376 [main] INFO  com.zkteco.guard.security.LicenseLoader - check apppers control count match
2025-08-04 13:47:00.378 [main] INFO  com.zkteco.guard.security.LicenseLoader - check printcard control count match
2025-08-04 13:47:00.379 [main] INFO  com.zkteco.guard.security.LicenseLoader - check visprintcard control count match
2025-08-04 13:47:00.380 [main] INFO  com.zkteco.guard.security.LicenseLoader - check hotelidreader control count match
2025-08-04 13:47:00.382 [main] INFO  com.zkteco.guard.security.LicenseLoader - check persidreader control count match
2025-08-04 13:47:00.383 [main] INFO  com.zkteco.guard.security.LicenseLoader - check visidreader control count match
2025-08-04 13:47:00.384 [main] INFO  com.zkteco.guard.security.LicenseLoader - check hotelocr control count match
2025-08-04 13:47:00.385 [main] INFO  com.zkteco.guard.security.LicenseLoader - check ocr control count match
2025-08-04 13:47:00.387 [main] INFO  com.zkteco.guard.security.LicenseLoader - check visocr control count match
2025-08-04 13:47:00.388 [main] INFO  com.zkteco.guard.security.LicenseLoader - check vissignature control count match
2025-08-04 13:47:00.433 [main] INFO  com.zkteco.guard.security.LicenseLoader - pullGateCount:0,pullDevCount:0,pushGateCount:2,pushDevCount:2,hasC3:0
2025-08-04 13:47:00.433 [main] INFO  com.zkteco.guard.security.LicenseLoader - pullControlCount:0,pushControlCount:2,isSupportC3:false
2025-08-04 13:47:00.433 [main] INFO  com.zkteco.guard.security.LicenseLoader - check acc control count match
2025-08-04 13:47:00.531 [main] INFO  com.zkteco.guard.security.LicenseLoader - check att control count match
2025-08-04 13:47:00.532 [main] INFO  com.zkteco.zkbiosecurity.license.init.LicenseInit - LicenseId is null,so can not start  cloud websocket!
2025-08-04 13:47:05.466 [main] INFO  com.zkteco.zkbiosecurity.auth.service.impl.AuthBioVerifyServiceImpl - Connect the finger service failed!
2025-08-04 13:47:09.578 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl - ---------------- detect face service already exists is: false
2025-08-04 13:47:10.445 [main] INFO  com.zkteco.zkbiosecurity.att.task.AttTimingDateCleanTask - AttTimingDateCleanTask Init ... 
2025-08-04 13:47:10.461 [main] INFO  com.zkteco.zkbiosecurity.att.task.AttTimingUpdateDayCardTask - AttTimingUpdateDayCardTask Init
2025-08-04 13:47:10.475 [main] INFO  com.zkteco.zkbiosecurity.att.task.AttRealTimeCalculationTask - AttRealTimeCalculationTask Init
2025-08-04 13:47:10.485 [main] INFO  com.zkteco.zkbiosecurity.att.task.AttUpdateRealTimeCacheTask - AttUpdateRealTimeCacheTask Init
2025-08-04 13:47:10.716 [main] INFO  com.zkteco.zkbiosecurity.adms.service.impl.AdmsSdkServiceImpl - Kill RS485Process failed by PULL!
2025-08-04 13:47:11.165 [main] INFO  com.zkteco.zkbiosecurity.adms.task.AdmsCmdDataSaveTask - AdmsCmdDataHandlerThread Start Handler Data......
2025-08-04 13:47:11.171 [main] INFO  com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask - AdmsCmdResultHandlerThread Start Handler Data......
2025-08-04 13:47:11.248 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = system current version = v3.14.0
2025-08-04 13:47:11.267 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.5.0 ->  v2.6.0. Looking for module system update success!
2025-08-04 13:47:11.475 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.6.0 ->  v2.9.0. Looking for module system update success!
2025-08-04 13:47:11.480 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.9.0 ->  v2.10.0. Looking for module system update success!
2025-08-04 13:47:11.485 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.10.0 ->  v3.0.1. Looking for module system update success!
2025-08-04 13:47:11.558 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.0.1 ->  v3.1.0. Looking for module system update success!
2025-08-04 13:47:11.563 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.1.0 ->  v3.2.0. Looking for module system update success!
2025-08-04 13:47:11.564 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.2.0 ->  v3.2.2. Looking for module system update success!
2025-08-04 13:47:11.590 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.2.2 ->  v3.3.2. Looking for module system update success!
2025-08-04 13:47:11.598 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.3.2 ->  v3.4.0. Looking for module system update success!
2025-08-04 13:47:11.608 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.4.0 ->  v3.6.0. Looking for module system update success!
2025-08-04 13:47:13.837 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.6.0 ->  v3.7.0. Looking for module system update success!
2025-08-04 13:47:13.849 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.7.0 ->  v3.8.0. Looking for module system update success!
2025-08-04 13:47:13.858 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.8.0 ->  v3.9.0. Looking for module system update success!
2025-08-04 13:47:13.872 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.9.0 ->  v3.10.0. Looking for module system update success!
2025-08-04 13:47:13.880 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.10.0 ->  v3.10.2. Looking for module system update success!
2025-08-04 13:47:13.929 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.10.2 ->  v3.11.0. Looking for module system update success!
2025-08-04 13:47:14.007 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.11.0 ->  v3.12.0. Looking for module system update success!
2025-08-04 13:47:14.023 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.12.0 ->  v3.13.0. Looking for module system update success!
2025-08-04 13:47:14.315 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = adms current version = v3.13.0
2025-08-04 13:47:14.317 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = pers current version = v3.14.0
2025-08-04 13:47:14.318 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = acc current version = v3.13.0
2025-08-04 13:47:14.325 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v1.0.0 ->  v2.4.0. Looking for module acc update success!
2025-08-04 13:47:14.327 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.4.0 ->  v2.6.0. Looking for module acc update success!
2025-08-04 13:47:14.335 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v2.6.0 ->  v3.0.0. Looking for module acc update success!
2025-08-04 13:47:14.335 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.0.0 ->  v3.0.3. Looking for module acc update success!
2025-08-04 13:47:14.336 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.0.3 ->  v3.1.1. Looking for module acc update success!
2025-08-04 13:47:14.371 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.1.1 ->  v3.1.2. Looking for module acc update success!
2025-08-04 13:47:14.429 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.1.2 ->  v3.2.0. Looking for module acc update success!
2025-08-04 13:47:14.430 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.2.0 ->  v3.3.0. Looking for module acc update success!
2025-08-04 13:47:14.465 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.3.0 ->  v3.5.0. Looking for module acc update success!
2025-08-04 13:47:14.466 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.5.0 ->  v3.6.0. Looking for module acc update success!
2025-08-04 13:47:14.546 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.6.0 ->  v3.8.0. Looking for module acc update success!
2025-08-04 13:47:14.554 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseUpgradeVerHandlerServiceImpl - Upgrade needed: v3.8.0 ->  v3.10.0. Looking for module acc update success!
2025-08-04 13:47:14.867 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = acc-advanced current version = v3.12.0
2025-08-04 13:47:14.868 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = att current version = null
2025-08-04 13:47:14.885 [main] INFO  com.zkteco.zkbiosecurity.adms.init.AdmsDeviceCacheInit - adms staring init device info......
2025-08-04 13:47:14.917 [main] INFO  com.zkteco.zkbiosecurity.adms.init.AdmsDeviceCacheInit - adms end init device info
2025-08-04 13:47:14.921 [PullServer-Thread] INFO  com.zkteco.zkbiosecurity.adms.distributor.pull.PullServer - Pull Server start...
2025-08-04 13:47:14.925 [PushServer-Thread] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.PushServer - Push Server listening on 8088 port...
2025-08-04 13:47:14.926 [PushServer-Thread] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.PushServer - Starting Push Server ......
2025-08-04 13:47:15.324 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - pers-permission.xml ZKBioSecurity Element is empty
2025-08-04 13:47:15.346 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - acc-permission.xml ZKBioSecurity Element is empty
2025-08-04 13:47:15.351 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - att-permission.xml ZKBioSecurity Element is empty
2025-08-04 13:47:15.354 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - system-permission.xml ZKBioSecurity Element is empty
2025-08-04 13:47:19.603 [AccCommunicationDataHandlerThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccCommunicationDataHandlerThread Start Handler Data......
2025-08-04 13:47:19.619 [AccRTLogSaveToDBThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccTransactionSaveThread Start Handler Data......
2025-08-04 13:47:20.033 [AccZonePersonSaveToDBThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor - AccZonePersonSaveThread start......
2025-08-04 13:47:39.609 [AccSavePersonThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccSavePersonThread start ......
2025-08-04 13:47:40.255 [AttCommunicationDataHandlerThread] INFO  com.zkteco.zkbiosecurity.att.processor.AttCommunicationDataProcessor$AttCommunicationDataHandlerThread - AttCommunicationDataHandlerThread Start Handler Data......
2025-08-04 13:47:44.627 [AccQueryDataThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccQueryDataThread start ......
2025-08-04 13:47:54.837 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-08-04 13:48:00.019 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:48:00.835 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:49:00.014 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:49:00.045 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:49:03.317 [http-nio-8098-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 13:49:03.356 [http-nio-8098-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 39 ms
2025-08-04 13:49:10.588 [http-nio-8098-exec-30] INFO  com.zkteco.zkbiosecurity.auth.service.impl.AuthBioVerifyServiceImpl - Connect the finger service failed!
2025-08-04 13:49:46.289 [http-nio-8098-exec-39] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 请求微信接口获取openid，URL: https://api.weixin.qq.com/sns/oauth2/access_token?appid=wxc71eb85bdaee3f9e&secret=2e6d9203214db1c5412adbf9f70327d2&code=001Rjl1w34Iwp53EnC3w39V5DZ3Rjl1-&grant_type=authorization_code
2025-08-04 13:49:46.887 [http-nio-8098-exec-39] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 微信接口响应: {"access_token":"94_1DgKQZh1v0VKTrcGW5-UlnHPO2mFgeaD1UNr_LYHciwPUFTeciVjf8OTCUaeN3-xqcuiVAJ_z6KJHZC49bbcnakpBa3PJ033ySaIHmXq61c","expires_in":7200,"refresh_token":"94_CMR4y2zazPPE_PoAv_R5_0CsuHvHaxOQyRnoXmfO56Qbh_Wag_jA6D3ZhB6wZPGGQqFMd93cBJtrzntDtHSj0XCTYJ9rnhcZzM4lPtGMZp8","openid":"o5NekvgHdhsSCEJX4WBf2X9ihMpc","scope":"snsapi_base"}
2025-08-04 13:49:46.888 [http-nio-8098-exec-39] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 成功获取openid: o5NekvgHdhsSCEJX4WBf2X9ihMpc
2025-08-04 13:49:46.890 [http-nio-8098-exec-39] INFO  com.zkteco.zkbiosecurity.acc.service.impl.WechatServiceImpl - 成功保存用户openid，pin: 23218, openId: o5NekvgHdhsSCEJX4WBf2X9ihMpc
2025-08-04 13:50:00.010 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:50:00.023 [pool-2-thread-1] INFO  org.springframework.scheduling.annotation.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [scheduler, clientInboundChannelExecutor, clientOutboundChannelExecutor, brokerChannelExecutor, messageBrokerTaskScheduler]
2025-08-04 13:50:00.031 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:51:00.011 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:51:00.022 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:52:00.012 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:52:00.024 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:53:00.015 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:53:00.027 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:54:00.013 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:54:00.024 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:55:00.010 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:55:00.021 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:56:00.002 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:56:00.013 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:57:00.008 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:57:00.018 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:58:00.015 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:58:00.027 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 13:59:00.015 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 13:59:00.026 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:00:00.016 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:00:00.029 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:01:00.016 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:01:00.026 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:02:00.016 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:02:00.026 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:03:00.010 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:03:00.056 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:04:00.002 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:04:00.012 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:05:00.004 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:05:00.016 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:06:00.016 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:06:00.026 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:07:00.006 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:07:00.018 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:08:00.006 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:08:00.017 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:09:00.015 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:09:00.025 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:10:00.006 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:10:00.024 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:11:00.007 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:11:00.017 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:12:00.009 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:12:00.019 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:13:00.008 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:13:00.017 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:14:00.016 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:14:00.026 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:15:00.008 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:15:00.022 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:16:00.017 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:16:00.027 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:17:00.007 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:17:00.015 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:17:54.842 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-08-04 14:18:00.007 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:18:00.016 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:19:00.019 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:19:00.030 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:20:00.005 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:20:00.016 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:21:00.005 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:21:00.014 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:22:00.012 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:22:00.023 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:23:00.012 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:23:00.021 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:24:00.016 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:24:00.027 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:25:00.011 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:25:00.021 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:26:00.014 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:26:00.025 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:27:00.018 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:27:00.028 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:28:00.018 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:28:00.028 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:29:00.008 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:29:00.017 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:30:00.010 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:30:00.021 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:31:00.009 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:31:00.020 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:32:00.006 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:32:00.016 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:33:00.012 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:33:00.022 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:34:00.017 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:34:00.027 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:35:00.013 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:35:00.025 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:36:00.012 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:36:00.022 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:37:00.009 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:37:00.020 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:38:00.017 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:38:00.026 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:39:00.005 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:39:00.016 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:40:00.011 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:40:00.022 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:41:00.014 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:41:00.023 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:42:00.012 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:42:00.031 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:43:00.001 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:43:00.019 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:44:00.003 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:44:00.014 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:45:00.012 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:45:00.025 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:46:00.010 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:46:00.018 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:47:00.020 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:47:00.032 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:47:54.843 [MessageBroker-2] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-08-04 14:48:00.016 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:48:00.066 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:49:00.014 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:49:00.025 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:50:00.017 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:50:00.035 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:51:00.009 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:51:00.025 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:52:00.010 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:52:00.022 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:53:00.002 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:53:00.013 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:54:00.014 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:54:00.025 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:55:00.004 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:55:00.019 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:56:00.008 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:56:00.019 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:57:00.005 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:57:00.016 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:58:00.007 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:58:00.018 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 14:59:00.013 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 14:59:00.024 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:00:00.014 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:00:00.027 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:01:00.012 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:01:00.028 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:02:00.010 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:02:00.021 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:03:00.010 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:03:00.021 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:04:00.012 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:04:00.025 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:05:00.015 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:05:00.028 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:06:00.014 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:06:00.028 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:07:00.005 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:07:00.017 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:08:00.014 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:08:00.027 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:09:00.014 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:09:00.025 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:10:00.003 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:10:00.014 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:11:00.016 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:11:00.026 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:12:00.008 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:12:00.021 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:13:00.010 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:13:00.022 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:14:00.010 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:14:00.023 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:15:00.017 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:15:00.033 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:16:00.003 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:16:00.016 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:17:00.010 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:17:00.021 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:17:54.860 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-08-04 15:18:00.012 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:18:00.023 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:19:00.004 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:19:00.016 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:20:00.014 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:20:00.030 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:21:00.002 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:21:00.014 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:22:00.002 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:22:00.017 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:23:00.004 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:23:00.015 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:24:00.003 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:24:00.015 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:25:00.003 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:25:00.016 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:26:00.003 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:26:00.014 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:27:00.002 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:27:00.014 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:28:00.002 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:28:00.014 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:29:00.002 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:29:00.013 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:30:00.004 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:30:00.016 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:31:00.001 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:31:00.016 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:32:00.002 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:32:00.015 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:33:00.002 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:33:00.013 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:34:00.002 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:34:00.016 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:35:00.002 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:35:00.018 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:36:00.002 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:36:00.014 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:37:00.002 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:37:00.015 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:38:00.004 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:38:00.017 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:39:00.002 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:39:00.014 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:40:00.004 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:40:00.015 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:41:00.008 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:41:00.018 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:42:00.014 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:42:00.025 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:43:00.001 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:43:00.013 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:44:00.012 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:44:00.023 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:45:00.014 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:45:00.029 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:46:00.010 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:46:00.027 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:47:00.007 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:47:00.024 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:47:54.875 [MessageBroker-3] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-08-04 15:48:00.002 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:48:00.018 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:49:00.011 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:49:00.024 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:50:00.015 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:50:00.026 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:51:00.007 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:51:00.018 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:52:00.017 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:52:00.027 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:53:00.006 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:53:00.017 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:54:00.003 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:54:00.016 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:55:00.012 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:55:00.028 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:56:00.017 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:56:00.029 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:57:00.002 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:57:00.018 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:58:00.003 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:58:00.015 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 15:59:00.007 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 15:59:00.019 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:00:00.007 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:00:00.021 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:01:00.010 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:01:00.023 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:02:00.010 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:02:00.023 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:03:00.006 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:03:00.018 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:04:00.016 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:04:00.028 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:05:00.014 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:05:00.033 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:06:00.014 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:06:00.026 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:07:00.012 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:07:00.029 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:08:00.008 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:08:00.019 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:09:00.002 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:09:00.013 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:10:00.016 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:10:00.032 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:11:00.003 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:11:00.015 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:12:00.006 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:12:00.018 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:13:00.010 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:13:00.022 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:14:00.012 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:14:00.025 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:15:00.007 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:15:00.020 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:16:00.006 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:16:00.017 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:17:00.014 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:17:00.027 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:17:54.883 [MessageBroker-2] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-08-04 16:18:00.009 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:18:00.020 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:19:00.011 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:19:00.022 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:20:00.002 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:20:00.015 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:21:00.005 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:21:00.018 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:22:00.014 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:22:00.025 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:23:00.019 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:23:00.040 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:24:00.010 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:24:00.021 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:25:00.015 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:25:00.026 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:26:00.006 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:26:00.017 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:27:00.007 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:27:00.019 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:28:00.020 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:28:00.085 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:29:00.010 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:29:00.022 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:30:00.016 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:30:00.029 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:31:00.003 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:31:00.015 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:32:00.018 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:32:00.030 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:33:00.012 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:33:00.024 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:34:00.006 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:34:00.019 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:35:00.012 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:35:00.023 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:36:00.004 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:36:00.019 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:37:00.011 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:37:00.025 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:38:00.006 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:38:00.042 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:39:00.018 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:39:00.029 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:40:00.005 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:40:00.016 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:41:00.013 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:41:00.023 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:42:00.007 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:42:00.021 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:43:00.001 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:43:00.014 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:44:00.005 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:44:00.017 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:45:00.003 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:45:00.015 [poolScheduler10] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:46:00.005 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:46:00.026 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:47:00.017 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:47:00.028 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:47:54.889 [MessageBroker-2] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-08-04 16:48:00.005 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:48:00.017 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:49:00.027 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:49:00.063 [poolScheduler9] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:50:00.268 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:50:00.290 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:51:00.001 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:51:00.013 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:52:00.007 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:52:00.020 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:53:00.012 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:53:00.023 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:54:00.006 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:54:00.019 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:55:00.007 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:55:00.023 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:56:00.013 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:56:00.030 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:57:00.014 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:57:00.024 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:58:00.033 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:58:00.043 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 16:59:00.004 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 16:59:00.024 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:00:00.009 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:00:00.033 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:01:00.013 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:01:00.038 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:02:00.012 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:02:00.021 [poolScheduler8] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:03:00.060 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:03:00.076 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:04:00.006 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:04:00.018 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:05:00.048 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:05:00.084 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:06:00.077 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:06:00.095 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:07:00.020 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:07:00.115 [poolScheduler6] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:08:00.083 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:08:00.095 [poolScheduler5] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:09:00.182 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:09:00.194 [poolScheduler3] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:10:00.016 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:10:00.026 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:11:00.306 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:11:00.517 [poolScheduler4] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:12:00.340 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:12:00.664 [poolScheduler1] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:13:00.107 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:13:00.346 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:14:00.022 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:14:00.097 [poolScheduler7] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
2025-08-04 17:15:00.109 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask start ...
2025-08-04 17:15:00.189 [poolScheduler2] INFO  com.zkteco.zkbiosecurity.att.task.AttPointPullTransactionTask - AttPointPullTransactionTask end ...
