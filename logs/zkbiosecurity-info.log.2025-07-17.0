2025-07-17 15:27:35.089 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
2025-07-17 15:27:35.120 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - Starting BiosecurityUIApp on Yang with PID 27908 (D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104\zkbiosecurity-startup\zkbiosecurity-startup-WioSecurityV6600_4.5.x_R\zkbiosecurity-startup-ui\target\classes started by maker in D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104)
2025-07-17 15:27:35.121 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - No active profile set, falling back to default profiles: default
2025-07-17 15:27:44.799 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-17 15:27:44.799 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-17 15:27:46.385 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1571ms. Found 142 JPA repository interfaces.
2025-07-17 15:27:46.733 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-17 15:27:46.735 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-17 15:27:47.087 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusChildrenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.088 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.088 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.088 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiTokenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.089 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAppDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.089 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAreaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.090 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.090 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthCompanyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.091 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthDepartmentDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.091 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthPermissionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.091 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthRoleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.091 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthSecurityParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.091 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthShortCutMenuDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.092 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthUserDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.092 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.092 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportFieldDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.093 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDbBackupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.093 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.093 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryValueDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.093 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.093 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageResourceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.093 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.094 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMediaFileDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.094 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMessageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.094 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseOpLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.095 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.095 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.095 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseRegisterDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.095 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseSysParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.095 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.096 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegSliceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.096 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemClientInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.096 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemModuleInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.096 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemZoomDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.096 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAccDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.096 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAttDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.096 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAuthDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.097 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsCmdIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.097 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDevCmdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.097 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.097 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.097 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosAllowLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.098 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosBuyLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.098 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosFullLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.098 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsProductDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.098 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.098 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeExtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.098 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioPhotoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.099 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.099 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.099 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCertificateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.099 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIdentityCardInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.099 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIssueCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.100 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersLeavePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.100 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonChangeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.100 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.100 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonLinkDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.100 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnalListDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.100 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnallistPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.101 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPositionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.101 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersTempPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.101 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.101 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.101 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorBEventNumDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.101 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.102 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorHistoryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.102 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAntiPassbackDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.102 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAppTopDoorByPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.102 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.102 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.102 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenCombDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.102 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.102 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.103 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.103 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.103 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.103 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.103 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.103 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.103 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceEventDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.104 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.104 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceVerifyModeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.104 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.104 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.104 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccExtDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.104 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstInLastOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.104 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.105 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccHolidayDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.105 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.105 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.105 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.105 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.105 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.105 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.106 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.106 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageInOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.106 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIndexDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.106 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.106 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.106 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.106 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.107 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapPosDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.107 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.107 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.107 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.107 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLastAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.107 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.107 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.108 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.108 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.108 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.108 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.108 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.108 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.108 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.109 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.109 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.109 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.109 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.109 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockMidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.109 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.109 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.110 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.110 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.110 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.110 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkagePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.111 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.111 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.111 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.111 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.111 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.112 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointSignPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.112 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccOccupancyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.112 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.112 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.112 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.112 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.112 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.113 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDetailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.113 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.113 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.113 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZonePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:27:47.113 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 363ms. Found 0 Redis repository interfaces.
2025-07-17 15:27:47.611 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webParamValidateConfig' of type [com.zkteco.zkbiosecurity.core.config.WebParamValidateConfig$$EnhancerBySpringCGLIB$$de3a90ea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 15:27:47.633 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 15:27:48.793 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8098 (http)
2025-07-17 15:27:48.985 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 13816 ms
2025-07-17 15:27:49.090 [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-17 15:27:49.568 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-17 15:27:49.843 [main] INFO  org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-07-17 15:27:49.872 [Druid-ConnectionPool-Create-157446616] INFO  com.alibaba.druid.pool.DruidAbstractDataSource - {dataSource-1} failContinuous is true
2025-07-17 15:31:00.769 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final
2025-07-17 15:31:00.827 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - Starting BiosecurityUIApp on Yang with PID 13256 (D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104\zkbiosecurity-startup\zkbiosecurity-startup-WioSecurityV6600_4.5.x_R\zkbiosecurity-startup-ui\target\classes started by maker in D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104)
2025-07-17 15:31:00.828 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - No active profile set, falling back to default profiles: default
2025-07-17 15:31:05.855 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-17 15:31:05.855 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-17 15:31:07.086 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1217ms. Found 142 JPA repository interfaces.
2025-07-17 15:31:07.433 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-07-17 15:31:07.435 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-17 15:31:07.790 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusChildrenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.791 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.app.dao.AuthAppMenusDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.791 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.791 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthApiTokenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.792 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAppDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.792 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthAreaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.793 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.793 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthCompanyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.794 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthDepartmentDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.794 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthPermissionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.794 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthRoleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.794 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthSecurityParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.794 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthShortCutMenuDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.795 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.auth.dao.AuthUserDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.795 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.795 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseCustomReportFieldDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.796 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDbBackupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.796 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.796 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseDictionaryValueDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.796 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.796 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseLanguageResourceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.796 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.798 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMediaFileDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.798 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseMessageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.798 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseOpLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.798 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.798 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BasePrintTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.799 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseRegisterDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.799 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseSysParamDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.799 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.799 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.BaseTimeSegSliceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.799 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemClientInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.799 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemModuleInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.800 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.system.dao.SystemZoomDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.800 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAccDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.800 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAttDeviceLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.800 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsAuthDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.800 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsCmdIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.800 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDevCmdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.800 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.801 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.801 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosAllowLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.801 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosBuyLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.801 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsPosFullLogDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.801 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.adms.dao.AdmsProductDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.801 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.802 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersAttributeExtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.802 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioPhotoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.802 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersBioTemplateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.802 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.802 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersCertificateDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.802 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIdentityCardInfoDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.803 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersIssueCardDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.803 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersLeavePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.803 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonChangeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.803 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.803 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonLinkDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.803 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnalListDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.804 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPersonnallistPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.804 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersPositionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.804 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersTempPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.804 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.804 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.pers.dao.PersWiegandFmtDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.804 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorBEventNumDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAlarmMonitorHistoryDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAntiPassbackDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAppTopDoorByPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccAuxOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.805 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenCombDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.806 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.806 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.806 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.806 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.806 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.806 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDSTimeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.806 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.807 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.807 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceEventDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.807 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.807 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDeviceVerifyModeDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.807 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.807 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccDoorVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.807 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccExtDeviceDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.808 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstInLastOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.808 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.808 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccHolidayDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.808 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.808 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.808 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.808 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.809 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLevelPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.809 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.809 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.809 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageInOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.809 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageIndexDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.809 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.809 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.810 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.810 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.810 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMapPosDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.810 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonCombOpenPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.810 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.810 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonFirstOpenDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.810 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLastAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.811 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.811 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.811 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderOptionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.811 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegBIdDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.811 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTimeSegDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.811 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTransactionDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.811 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupAddrDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.812 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccTriggerGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.812 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccVerifyModeRuleDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.812 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.812 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.812 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockDoorDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.812 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockGroupDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalInterlockMidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageIasDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageInDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageMediaDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageOutDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.813 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkagePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.814 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageTriggerDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.814 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccGlobalLinkageVidDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.814 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.814 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointDeptDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.814 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointReportDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.814 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccMusterPointSignPointDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.814 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccOccupancyDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.815 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonGlobalApbDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.815 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.815 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.815 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.815 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.815 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccPersonPersonLimitZoneDetailDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.816 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccReaderZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.816 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZoneDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.816 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationExtensionSupport - Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.zkteco.zkbiosecurity.acc.dao.AccZonePersonDao. If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository.
2025-07-17 15:31:07.816 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 365ms. Found 0 Redis repository interfaces.
2025-07-17 15:31:08.301 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webParamValidateConfig' of type [com.zkteco.zkbiosecurity.core.config.WebParamValidateConfig$$EnhancerBySpringCGLIB$$7ea6f8cb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 15:31:08.316 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-07-17 15:31:09.380 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8098 (http)
2025-07-17 15:31:09.568 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 8675 ms
2025-07-17 15:31:09.687 [main] INFO  com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-07-17 15:31:10.269 [main] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-07-17 15:31:10.584 [main] INFO  org.hibernate.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-17 15:31:10.705 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.4.32.Final
2025-07-17 15:31:11.038 [main] INFO  org.hibernate.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-07-17 15:31:11.369 [main] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: com.zkteco.zkbiosecurity.core.config.PostgreDialect
2025-07-17 15:31:39.095 [main] INFO  org.hibernate.engine.transaction.jta.platform.internal.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-07-17 15:31:39.122 [main] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-17 15:31:41.696 [main] INFO  com.zkteco.zkbiosecurity.core.web.listener.WebAppInitListener - WebAppInitListener init......
2025-07-17 15:31:43.369 [main] INFO  org.redisson.Version - Redisson 3.14.0
2025-07-17 15:31:44.694 [redisson-netty-4-21] INFO  org.redisson.connection.pool.MasterPubSubConnectionPool - 1 connections initialized for /127.0.0.1:6380
2025-07-17 15:31:44.710 [redisson-netty-4-19] INFO  org.redisson.connection.pool.MasterConnectionPool - 24 connections initialized for /127.0.0.1:6380
2025-07-17 15:31:52.538 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientInboundChannelExecutor'
2025-07-17 15:31:52.555 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'clientOutboundChannelExecutor'
2025-07-17 15:32:00.525 [main] INFO  com.zkteco.zkbiosecurity.core.config.LocaleConfig -  current  Locale = zh  LocaleTag=zh-CN
2025-07-17 15:32:00.525 [main] INFO  com.zkteco.zkbiosecurity.core.config.LocaleConfig - change current  Locale = zh change LocaleTag=zh-CN
2025-07-17 15:32:11.823 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService
2025-07-17 15:32:11.904 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'scheduler'
2025-07-17 15:32:11.966 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'messageBrokerTaskScheduler'
2025-07-17 15:32:12.136 [main] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'brokerChannelExecutor'
2025-07-17 15:32:13.498 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8098 (http) with context path ''
2025-07-17 15:32:15.730 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Starting...
2025-07-17 15:32:15.730 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=true, SimpleBrokerMessageHandler [DefaultSubscriptionRegistry[cache[0 destination(s)], registry[0 sessions]]]]
2025-07-17 15:32:15.730 [main] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Started.
2025-07-17 15:32:15.750 [main] INFO  org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor - More than one TaskScheduler bean exists within the context, and none is named 'taskScheduler'. Mark one of them as primary or name it 'taskScheduler' (possibly as an alias); or implement the SchedulingConfigurer interface and call ScheduledTaskRegistrar#setScheduler explicitly within the configureTasks() callback: [scheduler, messageBrokerTaskScheduler]
2025-07-17 15:32:15.773 [main] INFO  com.zkteco.zkbiosecurity.guard.biz.LicenseCheckBiz -  trigger cron config 59 59 23 * * ?
2025-07-17 15:32:15.819 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: a****s
2025-07-17 15:32:15.839 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: a******a
2025-07-17 15:32:15.840 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: w*********y
2025-07-17 15:32:15.840 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: a*******s
2025-07-17 15:32:15.861 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: h****i
2025-07-17 15:32:15.862 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: l********n
2025-07-17 15:32:15.862 [main] INFO  com.zkteco.zkbiosecurity.core.web.cache.SkinPropertyCache - load skin property: t******e
2025-07-17 15:32:15.878 [main] INFO  com.zkteco.zkbiosecurity.system.web.listener.SystemParamsInitListener - Init custom date format...
2025-07-17 15:32:15.885 [main] INFO  com.zkteco.zkbiosecurity.BiosecurityUIApp - Started BiosecurityUIApp in 75.507 seconds (JVM running for 76.828)
2025-07-17 15:32:16.519 [main] INFO  com.zkteco.guard.security.LicenseLoader - create license context success
2025-07-17 15:32:16.539 [main] INFO  com.zkteco.guard.security.LicenseLoader - check app control count match
2025-07-17 15:32:16.540 [main] INFO  com.zkteco.guard.security.LicenseLoader - check apppers control count match
2025-07-17 15:32:16.541 [main] INFO  com.zkteco.guard.security.LicenseLoader - check printcard control count match
2025-07-17 15:32:16.543 [main] INFO  com.zkteco.guard.security.LicenseLoader - check visprintcard control count match
2025-07-17 15:32:16.544 [main] INFO  com.zkteco.guard.security.LicenseLoader - check hotelidreader control count match
2025-07-17 15:32:16.545 [main] INFO  com.zkteco.guard.security.LicenseLoader - check persidreader control count match
2025-07-17 15:32:16.546 [main] INFO  com.zkteco.guard.security.LicenseLoader - check visidreader control count match
2025-07-17 15:32:16.548 [main] INFO  com.zkteco.guard.security.LicenseLoader - check hotelocr control count match
2025-07-17 15:32:16.550 [main] INFO  com.zkteco.guard.security.LicenseLoader - check ocr control count match
2025-07-17 15:32:16.551 [main] INFO  com.zkteco.guard.security.LicenseLoader - check visocr control count match
2025-07-17 15:32:16.553 [main] INFO  com.zkteco.guard.security.LicenseLoader - check vissignature control count match
2025-07-17 15:32:16.591 [main] INFO  com.zkteco.guard.security.LicenseLoader - pullGateCount:0,pullDevCount:0,pushGateCount:0,pushDevCount:0,hasC3:0
2025-07-17 15:32:16.591 [main] INFO  com.zkteco.guard.security.LicenseLoader - pullControlCount:0,pushControlCount:2,isSupportC3:false
2025-07-17 15:32:16.591 [main] INFO  com.zkteco.guard.security.LicenseLoader - check acc control count match
2025-07-17 15:32:16.591 [main] INFO  com.zkteco.zkbiosecurity.license.init.LicenseInit - LicenseId is null,so can not start  cloud websocket!
2025-07-17 15:32:21.025 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = system current version = v3.14.0
2025-07-17 15:32:21.078 [main] INFO  com.zkteco.zkbiosecurity.system.service.impl.BaseCropFaceServiceImpl - ---------------- detect face service already exists is: true
2025-07-17 15:32:21.604 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = pers current version = v3.14.0
2025-07-17 15:32:23.259 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = acc current version = null
2025-07-17 15:32:23.450 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = acc-advanced current version = v3.12.0
2025-07-17 15:32:23.672 [main] INFO  com.zkteco.zkbiosecurity.adms.service.impl.AdmsSdkServiceImpl - Kill RS485Process failed by PULL!
2025-07-17 15:32:24.130 [main] INFO  com.zkteco.zkbiosecurity.adms.task.AdmsCmdDataSaveTask - AdmsCmdDataHandlerThread Start Handler Data......
2025-07-17 15:32:24.136 [main] INFO  com.zkteco.zkbiosecurity.adms.task.AdmsCmdReturnTask - AdmsCmdResultHandlerThread Start Handler Data......
2025-07-17 15:32:24.229 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = adms current version = v3.13.0
2025-07-17 15:32:24.311 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = system current version = v3.14.0
2025-07-17 15:32:24.424 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = adms current version = v3.13.0
2025-07-17 15:32:24.425 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = pers current version = v3.14.0
2025-07-17 15:32:24.426 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = acc current version = null
2025-07-17 15:32:24.426 [main] INFO  com.zkteco.zkbiosecurity.base.utils.VersionUtil - module = acc-advanced current version = v3.12.0
2025-07-17 15:32:24.432 [main] INFO  com.zkteco.zkbiosecurity.adms.init.AdmsDeviceCacheInit - adms staring init device info......
2025-07-17 15:32:24.630 [main] INFO  com.zkteco.zkbiosecurity.adms.init.AdmsDeviceCacheInit - adms end init device info
2025-07-17 15:32:24.634 [PullServer-Thread] INFO  com.zkteco.zkbiosecurity.adms.distributor.pull.PullServer - Pull Server start...
2025-07-17 15:32:24.673 [PushServer-Thread] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.PushServer - Push Server listening on 8088 port...
2025-07-17 15:32:24.681 [PushServer-Thread] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.PushServer - Starting Push Server ......
2025-07-17 15:32:25.103 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - pers-permission.xml ZKBioSecurity Element is empty
2025-07-17 15:32:25.144 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - acc-permission.xml ZKBioSecurity Element is empty
2025-07-17 15:32:25.147 [main] INFO  com.zkteco.zkbiosecurity.system.init.SystemRolePermissionInit - system-permission.xml ZKBioSecurity Element is empty
2025-07-17 15:32:33.263 [AccCommunicationDataHandlerThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccCommunicationDataHandlerThread Start Handler Data......
2025-07-17 15:32:33.266 [AccRTLogSaveToDBThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccTransactionSaveThread Start Handler Data......
2025-07-17 15:32:33.459 [AccZonePersonSaveToDBThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccAdvancedCommunicationDataProcessor - AccZonePersonSaveThread start......
2025-07-17 15:32:35.646 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:32:35.727 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:32:49.931 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:32:49.940 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:32:53.270 [AccSavePersonThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccSavePersonThread start ......
2025-07-17 15:32:58.272 [AccQueryDataThread] INFO  com.zkteco.zkbiosecurity.acc.processor.AccCommunicationDataProcessor - AccQueryDataThread start ......
2025-07-17 15:33:04.940 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:33:04.941 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:33:12.135 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 1, active threads = 1, queued tasks = 0, completed tasks = 0]
2025-07-17 15:33:19.812 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:33:19.812 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:33:34.934 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:33:34.935 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:33:49.988 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:33:49.988 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:34:04.946 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:34:04.946 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:34:19.841 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:34:19.842 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:34:34.832 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:34:34.833 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:34:49.817 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:34:49.817 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:35:00.004 [pool-2-thread-1] INFO  org.springframework.scheduling.annotation.AnnotationAsyncExecutionInterceptor - More than one TaskExecutor bean found within the context, and none is named 'taskExecutor'. Mark one of them as primary or name it 'taskExecutor' (possibly as an alias) in order to use it for async processing: [scheduler, clientInboundChannelExecutor, clientOutboundChannelExecutor, brokerChannelExecutor, messageBrokerTaskScheduler]
2025-07-17 15:35:04.896 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:35:04.897 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:35:19.868 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:35:19.869 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:35:34.857 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:35:34.857 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:35:49.839 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:35:49.840 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:36:04.823 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:36:04.823 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:36:19.966 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:36:19.966 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:36:34.907 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:36:34.908 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:36:49.936 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:36:49.936 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:37:04.819 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:37:04.820 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:37:19.890 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:37:19.892 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:37:34.998 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:37:34.999 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:37:49.966 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:37:49.967 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:38:04.930 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:38:04.930 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:38:19.875 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:38:19.876 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:38:34.846 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:38:34.847 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:38:49.864 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:38:49.865 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:39:04.839 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:39:04.840 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:39:19.813 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:39:19.814 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:39:34.883 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:39:34.883 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:39:49.853 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:39:49.853 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:40:04.792 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:40:04.792 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:40:19.866 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:40:19.866 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:40:34.834 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:40:34.835 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:40:49.970 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:40:49.971 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:41:04.931 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:41:04.931 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:41:19.877 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:41:19.877 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:41:34.858 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:41:34.858 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:41:49.866 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:41:49.866 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:42:04.940 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:42:04.941 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:42:19.936 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:42:19.938 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:42:34.900 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:42:34.900 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:42:49.819 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:42:49.819 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:43:04.799 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:43:04.799 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:43:19.926 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:43:19.927 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:43:34.924 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:43:34.924 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:43:49.916 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:43:49.916 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:44:04.859 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:44:04.860 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:44:20.465 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:44:20.663 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:44:34.845 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:44:34.845 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:44:49.836 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:44:49.837 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:45:04.785 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:45:04.786 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:45:19.986 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:45:19.987 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:45:34.966 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:45:34.967 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:45:49.802 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:45:49.803 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:46:04.869 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:46:04.869 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:46:19.869 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:46:19.869 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:46:34.804 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:46:34.805 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:46:49.799 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:46:49.800 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:47:04.865 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:47:04.865 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:47:19.855 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:47:19.855 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:47:34.817 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:47:34.818 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:47:49.784 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:47:49.784 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:48:04.849 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:48:04.849 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:48:19.816 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:48:19.816 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:48:34.790 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:48:34.791 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:48:49.850 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:48:49.851 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:49:04.825 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:49:04.825 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:49:19.825 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:49:19.826 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:49:34.798 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:49:34.799 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:49:49.858 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:49:49.858 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:50:04.827 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:50:04.828 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:50:19.793 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:50:19.794 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:50:34.858 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:50:34.858 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:50:49.820 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:50:49.821 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:51:04.801 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:51:04.801 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:51:19.910 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:51:19.911 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:51:34.929 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:51:34.930 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:51:49.906 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:51:49.907 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:52:04.801 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:52:04.801 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:52:19.793 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:52:19.794 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:52:34.864 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:52:34.864 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:52:49.831 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:52:49.832 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:53:04.810 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:53:04.811 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:53:19.782 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:53:19.782 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:53:34.901 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:53:34.901 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:53:49.917 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:53:49.918 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:54:04.928 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:54:04.928 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:54:19.844 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:54:19.844 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:54:34.820 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:54:34.820 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:54:49.810 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:54:49.810 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:55:04.797 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:55:04.798 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:55:19.769 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:55:19.770 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:55:34.830 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:55:34.831 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:55:49.794 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:55:49.795 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:56:04.935 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:56:04.935 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:56:19.883 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:56:19.883 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:56:34.891 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:56:34.891 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:56:49.869 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:56:49.870 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:57:04.839 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:57:04.839 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:57:19.839 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:57:19.840 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:57:34.901 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:57:34.902 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:57:49.913 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:57:49.914 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:58:04.783 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:58:04.784 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:58:19.863 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:58:19.864 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:58:34.832 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:58:34.833 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:58:49.797 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:58:49.799 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:59:04.783 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:59:04.783 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:59:19.851 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:59:19.851 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:59:34.834 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:59:34.834 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 15:59:49.871 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 15:59:49.871 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:00:04.897 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:00:04.898 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:00:19.827 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:00:19.828 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:00:34.999 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:00:35.000 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:00:49.970 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:00:49.971 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:01:04.991 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:01:04.992 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:01:20.103 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:01:20.104 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:01:34.962 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:01:34.962 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:01:49.947 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:01:49.947 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:02:05.053 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:02:05.054 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:02:20.032 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:02:20.033 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:02:35.024 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:02:35.025 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:02:49.957 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:02:49.958 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:03:05.053 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:03:05.053 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:03:12.137 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 2, active threads = 1, queued tasks = 0, completed tasks = 1]
2025-07-17 16:03:20.039 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:03:20.040 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:03:35.003 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:03:35.004 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:03:49.983 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:03:49.983 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:04:04.956 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:04:04.957 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:04:19.935 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:04:19.936 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:04:34.912 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:04:34.913 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:04:50.073 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:04:50.073 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:05:05.015 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:05:05.016 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:05:20.085 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:05:20.086 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:05:34.922 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:05:34.922 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:05:49.942 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device registry info：DeviceType=acc,~DeviceName=T08,FirmVer=ZAM200-NF80VF-Ver1.4.42,PushVersion=Ver 2.0.33S-20220119,CommType=ethernet,MaxPackageSize=2048000,LockCount=1,ReaderCount=2,AuxInCount=1,AuxOutCount=0,MachineType=101,~IsOnlyRFMachine=0,~MaxUserCount=500,~MaxAttLogCount=100,~MaxUserFingerCount=10,MThreshold=35,IPAddress=**************,GATEIPAddress=**************,NetMask=*************,~ZKFPVersion=10,IclockSvrFun=1,OverallAntiFunOn=0,~REXInputFunOn=0,~CardFormatFunOn=0,~SupAuthrizeFunOn=0,~ReaderCFGFunOn=0,~ReaderLinkageFunOn=0,~RelayStateFunOn=1,~Ext485ReaderFunOn=1,~TimeAPBFunOn=0,~CtlAllRelayFunOn=0,~LossCardFunOn=0,SimpleEventType=1,VerifyStyles=9d880600,EventTypes=FF0FF03DBA26018033060000F00000000000000000000000007703F007860100,NewNormalEventTypes=,NewErrorEventTypes=,NewWarningEventTypes=,DisableUserFunOn=0,DeleteAndFunOn=0,LogIDFunOn=1,DateFmtFunOn=0,DelAllLossCardFunOn=0,AutoClearDay=0,FirstDelayDay=0,DelayDay=0,StopAllVerify=0,FvFunOn=0,FaceFunOn=1,FingerFunOn=0,CameraOpen=1,SubcontractingUpgradeFunOn=1,UserPicURLFunOn=1,~SerialNumber=7273212700013,IsSupportNFC=0,~MaxFingerCount=30,AccSupportFunList=001000001010100000000000100000000010001110100000000000000000000000000000000000000000000000000000,~DSTF=1,Reader1IOState=1,MultiCardInterTimeFunOn=1,MachineTZFunOn=1,BioPhotoFun=1,VisilightFun=1,~MaxFvCount=10,~MaxBioPhotoCount=30000,AutoServerFunOn=1,MultiBioDataSupport=0:0:1:0:0:0:0:0:0:1,MultiBioPhotoSupport=0:0:0:0:0:0:0:0:0:1,MultiBioVersion=0:0:12.0:0:0:0:0:0:0:59.2,MaxMultiBioPhotoCount=0:0:0:0:0:0:0:0:0:30000,MaxMultiBioDataCount=0:0:30000:0:0:0:0:0:0:30000,IRTempDetectionFunOn=0,MaskDetectionFunOn=1
2025-07-17 16:05:49.943 [defaultEventExecutorGroup-11-1] INFO  com.zkteco.zkbiosecurity.adms.distributor.push.processor.AccPushProcessor - acc device sn=7273212700013 no auth ,request reject
2025-07-17 16:06:50.504 [AccCommunicationDataHandlerThread] INFO  com.zkteco.zkbiosecurity.acc.service.impl.AccTransactionServiceImpl - ************AccTransactionServiceImpl getFieldMap logInfo : 
2025-07-17 16:06:50.709 [AccCommunicationDataHandlerThread] INFO  com.zkteco.zkbiosecurity.core.utils.FileEncryptUtil - ======AesPhotoUtil getDecPhoto() decode file not exixt!FilePath:BioSecurityFile\images/lightgreen/userImage.gif=======
2025-07-17 16:33:12.138 [MessageBroker-2] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 3, active threads = 1, queued tasks = 0, completed tasks = 2]
2025-07-17 17:03:12.141 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 3]
2025-07-17 17:33:12.145 [MessageBroker-3] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 4]
2025-07-17 18:03:12.147 [MessageBroker-2] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 5]
2025-07-17 18:33:12.153 [MessageBroker-2] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 6]
2025-07-17 19:03:12.158 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 7]
2025-07-17 19:33:12.162 [MessageBroker-1] INFO  org.springframework.web.socket.config.WebSocketMessageBrokerStats - WebSocketSession[0 current WS(0)-HttpStream(0)-HttpPoll(0), 0 total, 0 closed abnormally (0 connect failure, 0 send limit, 0 transport error)], stompSubProtocol[processed CONNECT(0)-CONNECTED(0)-DISCONNECT(0)], stompBrokerRelay[null], inboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], outboundChannel[pool size = 0, active threads = 0, queued tasks = 0, completed tasks = 0], sockJsScheduler[pool size = 6, active threads = 1, queued tasks = 0, completed tasks = 8]
2025-07-17 19:42:30.473 [SpringContextShutdownHook] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopping...
2025-07-17 19:42:30.473 [SpringContextShutdownHook] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - BrokerAvailabilityEvent[available=false, SimpleBrokerMessageHandler [DefaultSubscriptionRegistry[cache[0 destination(s)], registry[0 sessions]]]]
2025-07-17 19:42:30.473 [SpringContextShutdownHook] INFO  org.springframework.messaging.simp.broker.SimpleBrokerMessageHandler - Stopped.
2025-07-17 19:42:30.676 [SpringContextShutdownHook] INFO  com.zkteco.zkbiosecurity.core.web.listener.WebAppInitListener - WebAppInitListener destroy......
2025-07-17 19:42:30.723 [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'brokerChannelExecutor'
2025-07-17 19:42:30.725 [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'messageBrokerTaskScheduler'
2025-07-17 19:42:30.726 [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'scheduler'
2025-07-17 19:42:30.818 [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'clientOutboundChannelExecutor'
2025-07-17 19:42:30.818 [SpringContextShutdownHook] INFO  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'clientInboundChannelExecutor'
2025-07-17 19:42:31.091 [SpringContextShutdownHook] INFO  org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-17 19:42:31.099 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-07-17 19:42:31.128 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
