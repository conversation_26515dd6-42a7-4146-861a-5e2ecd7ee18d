package com.zkteco.zkbiosecurity.ad.service;

import com.zkteco.zkbiosecurity.ad.vo.Ad4AccLevelItem;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface Ad4AccLevelService {

    /**
    * 获取所有权限组名称
    * <AUTHOR>
    * @since 2019-08-27 14:32
    * @Param []
    * @return
     */
    List<String> getAllAccLevelName();

    /**
    * 获取初始化权限组
    * <AUTHOR>
    * @since 2019-08-27 14:32
    * @Param []
    * @return
     */
    Ad4AccLevelItem getMasterLevel();

    /**
    * 根据id删除权限组：将在ad中已经删除的权限组在软件上做相应的删除操作
    * <AUTHOR>
    * @since 2019-08-27 15:21
    * @Param [delLevelIds]
    * @return
     */
    void delLevelByIds(List<String> delLevelIds);

    /**
    * 保存AD同步过来的权限组
    * <AUTHOR>
    * @since 2019-08-27 15:49
    * @Param [ad4AccLevelItem]
    * @return
     */
    Ad4AccLevelItem saveItem(Ad4AccLevelItem ad4AccLevelItem);

    /**
    * 根据人员id获取人员所在的权限组
    * <AUTHOR>
    * @since 2019-08-27 17:05
    * @Param [personId]
    * @return
     */
    List<Ad4AccLevelItem> getLevelByPersonId(String personId);

    /**
    * 获取门禁权限组和id的map集合
    * <AUTHOR>
    * @since 2019-08-27 17:29
    * @Param []
    * @return
     */
    Map<String, String> getLevelNameIdMap();

    /**
    * 批量删除人员权限组
    * <AUTHOR>
    * @since 2019-08-27 17:46
    * @Param [delAccLevelIds, personId]
    * @return
     */
    void immeDelPersonLevel(List<String> delAccLevelIds, String personId);

    /**
    * 根据权限组id和人员id，将人员添加到权限组
    * <AUTHOR>
    * @since 2019-08-27 17:47
    * @Param [accLevelIds, personId]
    * @return
     */
    void addPersonLevel(List<String> accLevelIds, String personId);
}
