package com.zkteco.zkbiosecurity.ad.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class Ad4AccLevelItem implements Serializable {
    private String id;
    private String name;

    /** 系统区域id */
    private String authAreaId;

    private String authAreaName;

    private String timeSegId;

    /**  */
    private String timeSegName;

    private String doorCount;

    private Boolean initFlag;

    private String inId;

    private String notInId;

    private Boolean changeLevel;

    private String areaIds;
}
