package com.zkteco.zkbiosecurity.vid.service;

import com.zkteco.zkbiosecurity.vid.vo.Vid4OtherDeviceItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/6 16:41
 * @since 1.0.0
 */
public interface Vid4OtherDeviceService {
    /**
     * 获取已启用的视频设备
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.vid.vo.Vid4OtherDeviceItem>
     * <AUTHOR>
     * @date 2020-11-06 16:43
     * @since 1.0.0
     */
    default List<Vid4OtherDeviceItem> getEnabledVidDevice() {
        return null;
    }
}
