package com.zkteco.zkbiosecurity.vid.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.vid.vo.Vid4OtherChannelItem;

/**
 * 其他模块根据用户登录权限过滤显示摄像头API类
 *
 * <AUTHOR>
 * @date 2020/11/6 8:58
 * @since 1.0.0
 */
public interface Vid4OtherGetVidChannelService {

   /**
    * 根据用户登录权限过滤显示摄像头
    *
    * @param sessionId: sessionId
    * @param vid4OtherChannelItem: 其他模块调用Vid视频通道通用Item类
    * @param pageNo: 页码
    * @param pageSize: 每页数目
    * @return com.zkteco.zkbiosecurity.base.bean.Pager
    * <AUTHOR>
    * @throws
    * @date  2020/11/6 9:21
    * @since 1.0.0
    */
    Pager loadPagerByAuthFilter(String sessionId, Vid4OtherChannelItem vid4OtherChannelItem, int pageNo, int pageSize);

}
