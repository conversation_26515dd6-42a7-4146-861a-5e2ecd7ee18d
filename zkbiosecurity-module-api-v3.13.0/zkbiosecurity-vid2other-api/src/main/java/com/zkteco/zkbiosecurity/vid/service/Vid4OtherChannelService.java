package com.zkteco.zkbiosecurity.vid.service;

import java.util.Collection;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
/**
 * 其他模块调用Vid视频通道通用类
 *
 * <AUTHOR>
 * @date 2020/11/6 8:58
 * @since 1.0.0
 */
public interface Vid4OtherChannelService {

    /**
     * 判断视频模块是否存在通道（摄像头）
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date  2020/11/6 9:15
     * @since 1.0.0
     */
    ZKResultMsg isExistVidChannel();

    /**
     * 根据实体id和实体名称获取绑定的摄像头id
     *
     * @param entityIds: 触发联动的实体类id
     * @param entityClassName: 触发联动的实体类类型
     * @return java.lang.String
     * <AUTHOR>
     * @throws
     * @date  2020/11/6 9:16
     * @since 1.0.0
     */
    String getBindChannelIds(Collection<String> entityIds, String entityClassName);

    /**
     * 根据实体id和实体名称绑定/解绑视频通道(摄像头)
     *
     * @param channelIds: 通道IDs
     * @param entityName: 触发联动的实体类类型
     * @param entityId: 触发联动的实体类id
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date  2020/11/6 9:17
     * @since 1.0.0
     */
    ZKResultMsg bindOrUnbindChannel(String channelIds, String entityName, String entityId);
}
