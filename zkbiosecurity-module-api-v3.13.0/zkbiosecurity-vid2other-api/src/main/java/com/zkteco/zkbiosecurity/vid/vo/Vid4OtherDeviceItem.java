package com.zkteco.zkbiosecurity.vid.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/11/6 16:42
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Vid4OtherDeviceItem implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;

    /** 设备名称 */
    private String alias;

    /** 设备品牌 */
    private Short brand;

    /** 序列号 */
    private String sn;

    /** 区域名称 */
    private String areaName;

    /** 区域id */
    private String areaId;

    /** 主机地址 */
    private String host;

    /** 端口 */
    private Integer port;

    /** 禁用启用 */
    private Boolean enabled;

    /** 协议类型 */
    private Short protocol;

    /** 设备型号 */
    private String deviceType;

    /** 设备固件版本 */
    private String fwVersion;

    /** 厂商SDK类别 */
    private Short sdkType;

    /** Web端口 */
    private Integer webPort;

    /** 子网掩码 */
    private String subnetMask;

    /** 网关 */
    private String gateway;

    /** 用户名 */
    private String username;

    /** 通信密码 */
    private String commPwd;

    /** 设备通道数 */
    private Short channelCount;

    // 辅助属性
    private boolean online;// 是否在线
    private int count = 0;// 次数累计
    private long nextTime = 0;
}
