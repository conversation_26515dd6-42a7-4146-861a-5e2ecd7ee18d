package com.zkteco.zkbiosecurity.vid.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;
import java.util.Map;

/**
 * 其他模块调用视频联动API类
 *
 * <AUTHOR>
 * @date 2020/11/6 8:58
 * @since 1.0.0
 */
public interface Vid4OtherGetVidLinkageService {

    /**
     * 联动抓图接口
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月2日 下午8:09:33
     * @param vidLinkageHandle 抓图句柄
     * @param entityIds 触发联动的实体类id
     * @param entityClassName 触发联动的实体类类型
     * @return
     */
    @Deprecated
    default boolean getLinkageCapture(String vidLinkageHandle, String entityIds, String entityClassName) {
        return false;
    }

    /**
     * 联动录像接口
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月3日 上午10:41:48
     * @param vidLinkageHandle 录像句柄
     * @param entityIds 触发联动的实体类id
     * @param entityClassName 触发联动的实体类类型
     * @param actionTime 录像时间
     * @return
     */
    @Deprecated
    default boolean getLinkageRecord(String vidLinkageHandle, String entityIds, String entityClassName,
        int actionTime) {
        return false;
    }

    /**
     * 联动弹出视频
     *
     * @param entityIds: 触发联动的实体类id
     * @param entityClassName: 触发联动的实体类类型
     * @param actionTime: 录像时间
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     * <AUTHOR>
     * @throws @date 2020/11/6 9:27
     * @since 1.0.0
     */
    List<Map<String, Object>> getLinkagePopUpVideo(String entityIds, String entityClassName, int actionTime);

    /**
     * 联动抓图接口（返回文件路径）
     *
     * @param vidLinkageHandle: 抓图句柄
     * @param entityIds: 触发联动的实体类id
     * @param entityClassName: 触发联动的实体类类型
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws @date 2020/11/6 9:27
     * @since 1.0.0
     */
    ZKResultMsg getVidLinkageCapture(String vidLinkageHandle, String entityIds, String entityClassName);

    /**
     * 联动录像接口(返回文件路径)
     *
     * @param vidLinkageHandle: 录像句柄
     * @param entityIds: 触发联动的实体类id
     * @param entityClassName: 触发联动的实体类类型
     * @param actionTime: 录像时间
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws @date 2020/11/6 9:28
     * @since 1.0.0
     */
    ZKResultMsg getVidLinkageRecord(String vidLinkageHandle, String entityIds, String entityClassName, int actionTime);

    /**
     * 联动录像接口(返回文件路径) 可控开始时间偏移量
     *
     * @param vidLinkageHandle:
     * @param entityIds:
     * @param entityClassName:
     * @param offsetTime:
     * @param actionTime:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date  2024/2/5 10:02
     * @since 1.0.0
     */
    default ZKResultMsg getVidLinkageRecord(String vidLinkageHandle, String entityIds, String entityClassName, int offsetTime, int actionTime) {
        return null;
    }

    /**
     * 处理上传IPC联动信息
     *
     * @param dataMap: IPC联动信息
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2024-10-24 14:15
     * @since 1.0.0
     */
    default ZKResultMsg handleIPCLinkPhotoInfo(String vidLinkageHandle, String entityIds, String entityClassName, Map<String, String> dataMap) {
        return null;
    }
}
