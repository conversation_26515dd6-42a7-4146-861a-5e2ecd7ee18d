package com.zkteco.zkbiosecurity.vid.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 其他模块调用Vid视频通道通用Item类
 *
 * <AUTHOR>
 * @date 2020/11/6 8:58
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class Vid4OtherChannelItem extends BaseItem {
    private static final long serialVersionUID = 1L;
    /** 主键 */
    private String id;

    /** 通道名称 */
    private String name;

    /** 通道编号 */
    private Short channelNo;

    /** 区域名称 */
    private String areaName;

    /** 区域id */
    private String areaId;

    /** 设备名称 */
    private String alias;

    /** 设备id */
    private String deviceId;

    /** * 禁用启用 */
    private Boolean enabled;

    /** 设备序列号 */
    private String sn;

    /** 查询条件:Id被包含 */
    private String inId;

    /** 查询条件:Id不被包含 */
    private String notInId;

    /*** 域编码，NVR唯一编号，相当于SN */
    private String domainCode;

    /** 设备过滤类型 */
    private String filterTypes;
}
