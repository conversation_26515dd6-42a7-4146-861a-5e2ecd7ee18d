package com.zkteco.zkbiosecurity.vid.service;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/11/9 10:04
 * @since 1.0.0
 */
public interface Vid4OtherGetVidTransactionService {

    /**
     * 视频事件下载录像文件前验证
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/29 10:56
     * @param transactionId
     *            事件记录id
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    default Map<String, Object> getVideoFileValidate(String transactionId) {
        return null;
    }
}
