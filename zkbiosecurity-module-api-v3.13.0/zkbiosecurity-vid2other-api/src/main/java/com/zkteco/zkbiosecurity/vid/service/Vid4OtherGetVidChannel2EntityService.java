package com.zkteco.zkbiosecurity.vid.service;

import com.zkteco.zkbiosecurity.vid.vo.Vid4OtherChannelItem;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 其他模块获取Vid视频通道名称API类
 *
 * <AUTHOR>
 * @date 2020/11/6 8:58
 * @since 1.0.0
 */
public interface Vid4OtherGetVidChannel2EntityService {

    /**
     * 获取绑定的摄像头名称(通道名称)
     *
     * @param entityIds: 触发联动的实体类id
     * @param entityClassName: 触发联动的实体类类型
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @throws @date 2020/11/6 9:19
     * @since 1.0.0
     */
    Map<String, String> getBindChannelNames(Collection<String> entityIds, String entityClassName);

    /**
     * 获取摄像头节点名称
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月27日 上午9:00:56
     * @param entityId
     * @return
     */
    String getVidChannel2EntityName(String entityId);

    /**
     * 根据通道id获取通道名称
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/1/18 18:26
     * @param channelId
     * @return java.lang.String
     */
    String getVidChannelNameById(String channelId);

    /**
     * 根据通道id获取视频通道信息
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2020/7/22 15:57
     * @param channelId 通道id
     * @return com.zkteco.zkbiosecurity.acc.vo.Acc4VidChannelItem
     */
    default Vid4OtherChannelItem getVidChannelById(String channelId) {
        return null;
    }

    /**
     * 获取绑定的通道（摄像头）ids
     *
     * @param entityId:绑定的实体id
     * @param entityClassName:实体类名称
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-08-25 17:22
     * @since 1.0.0
     */
    default List<String> getBindChannelIds(String entityId, String entityClassName) {
        return null;
    }
}
