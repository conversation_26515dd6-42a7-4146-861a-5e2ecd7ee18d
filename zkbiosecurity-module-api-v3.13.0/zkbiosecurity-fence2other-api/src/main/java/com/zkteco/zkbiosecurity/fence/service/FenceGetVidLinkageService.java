package com.zkteco.zkbiosecurity.fence.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;
import java.util.Map;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2020/10/19 15:08
 * @since 1.0.0
 */
@Deprecated
public interface FenceGetVidLinkageService {

    /**
     * 迁移电子围栏相关接口：联动抓图接口
     *
     * @param vidLinkageHandle 抓图句柄
     * @param entityIds        触发联动的实体类id
     * @param entityClassName  触发联动的实体类类型
     * @return
     * <AUTHOR>
     * @since 2020年10月19日 上午10:41:48
     */
    boolean getLinkageCapture(String vidLinkageHandle, String entityIds, String entityClassName);

    /**
     * 迁移电子围栏相关接口：联动录像接口
     *
     * @param vidLinkageHandle 录像句柄
     * @param entityIds        触发联动的实体类id
     * @param entityClassName  触发联动的实体类类型
     * @param actionTime       录像时间
     * @return
     * <AUTHOR>
     * @since 2020年10月19日 上午10:41:48
     */
    boolean getLinkageRecord(String vidLinkageHandle, String entityIds, String entityClassName, int actionTime);

    /**
     * 迁移电子围栏相关接口：联动弹出视频
     *
     * @param entityIds       触发联动的实体类id
     * @param entityClassName 触发联动的实体类类型
     * @param actionTime      录像时间
     * @return
     * <AUTHOR>
     * @since 2020年10月19日 上午10:41:48
     */
    List<Map<String, Object>> getLinkagePopUpVideo(String entityIds, String entityClassName, int actionTime);

    /**
     * 方法描述 联动抓图接口（返回文件路径）
     *
     * @param vidLinkageHandle:抓图句柄
     * @param entityIds:触发联动的实体类id
     * @param entityClassName:触发联动的实体类类型
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2020/10/27 18:22
     * @since 1.0.0
     */
    ZKResultMsg getVidLinkageCapture(String vidLinkageHandle, String entityIds, String entityClassName);
    
    /**
     * 方法描述 联动录像接口(返回文件路径)
     *
     * @param vidLinkageHandle:录像句柄
     * @param entityIds:触发联动的实体类id
     * @param entityClassName:触发联动的实体类类型
     * @param actionTime:录像时间
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2020/10/27 18:23
     * @since 1.0.0
     */
    ZKResultMsg getVidLinkageRecord(String vidLinkageHandle, String entityIds, String entityClassName, int actionTime);
}
