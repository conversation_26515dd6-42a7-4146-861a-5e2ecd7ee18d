package com.zkteco.zkbiosecurity.fence.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.Collection;

@Deprecated
public interface Fence4VidChannelService {

    /**
     * 方法描述 判断视频模块是否存在通道（摄像头）
     *
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2020/10/28 18:06
     * @since 1.0.0
     */
    ZKResultMsg isExistVidChannel();

    /**
     * 方法描述 根据实体id和实体名称获取绑定的摄像头id
     *
     * @param entityIds:实体id
     * @param entityClassName:实体名称
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/10/28 18:07
     * @since 1.0.0
     */
    String getBindChannelIds(Collection<String> entityIds, String entityClassName);
    
    /**
     * 方法描述 绑定/解绑视频通道(摄像头)
     *
     * @param channelIds:已绑定的通道id
     * @param entityName:实体名称
     * @param entityId:实体id
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2020/10/28 18:07
     * @since 1.0.0
     */
    ZKResultMsg bindOrUnbindChannel(String channelIds, String entityName, String entityId);
}
