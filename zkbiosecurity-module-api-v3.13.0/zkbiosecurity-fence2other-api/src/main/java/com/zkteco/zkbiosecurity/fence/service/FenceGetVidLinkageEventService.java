package com.zkteco.zkbiosecurity.fence.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@Deprecated
public interface FenceGetVidLinkageEventService {

    /**
     * 方法描述 获取视频联动产生文件的路径，用于发送邮件（仅抓拍文件）
     *
     * @param vidLinkageHandle:句柄
     * @return java.lang.String
     * <AUTHOR>
     * @date 2020/10/30 11:01
     * @since 1.0.0
     */
    String getCaputureFilePath(String vidLinkageHandle);
    
    /**
     * 方法描述 获取事件记录对应的视频联动信息
     *
     * @param vidLinkageHandle:视频联动句柄
     * @param fileType:文件类型（1录像，2抓拍）
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2020/10/30 11:01
     * @since 1.0.0
     */
    ZKResultMsg getVidLinkageEventData(String vidLinkageHandle, String fileType);
}
