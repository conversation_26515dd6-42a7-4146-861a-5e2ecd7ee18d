package com.zkteco.zkbiosecurity.fence.service;

import java.util.Collection;
import java.util.Map;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2020/10/19 15:40
 * @since 1.0.0
 */
@Deprecated
public interface FenceGetVidChannel2EntityService {
    /**
     * 获取电子围栏防区绑定的摄像头名称(通道名称)
     * 
     * @Return: java.util.Map<java.lang.String,java.lang.String>
     * @Author: wml.wu
     * @Date: 2020/2/5 10:51
     */
    Map<String, String> getFenceZoneBindChannelNames(Collection<String> entityIds);

    /**
     * 获取摄像头节点名称
     * 
     * @Return: java.lang.String
     * @Author: wml.wu
     * @Date: 2020/2/5 10:51
     */
    String getVidChannel2EntityName(String entityId);

    /**
     * 根据通道id获取通道名称
     * 
     * @Return: java.lang.String
     * @Author: wml.wu
     * @Date: 2020/2/5 10:50
     */
    String getVidChannelNameById(String channelId);
}
