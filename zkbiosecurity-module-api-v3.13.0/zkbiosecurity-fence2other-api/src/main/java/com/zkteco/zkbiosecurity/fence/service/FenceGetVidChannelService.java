package com.zkteco.zkbiosecurity.fence.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.fence.vo.Fence4VidChannelItem;

@Deprecated
public interface FenceGetVidChannelService {

    /**
     * 方法描述 根据用户登录权限过滤显示摄像头
     *
     * @param sessionId:
     * @param fence4VidChannelItem:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2020/10/28 18:09
     * @since 1.0.0
     */
    Pager loadPagerByAuthFilter(String sessionId, Fence4VidChannelItem fence4VidChannelItem, int pageNo, int pageSize);
}
