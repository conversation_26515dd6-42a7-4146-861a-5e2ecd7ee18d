<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>com.zkteco</groupId>
		<artifactId>zkbiosecurity-module-api</artifactId>
		<version>${revision}</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>zkbiosecurity-workflow2other-api</artifactId>
	<name>${project.artifactId}</name>
	<packaging>jar</packaging>
	<version>${project.parent.version}</version>
	<dependencies>
		<dependency>
			<groupId>com.zkteco</groupId>
			<artifactId>zkbiosecurity-base</artifactId>
			<optional>true</optional>
		</dependency>
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>pl.project13.maven</groupId>
				<artifactId>git-commit-id-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>