package com.zkteco.zkbiosecurity.workflow.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.workflow.vo.Workflow4OtherFlowItem;

/**
 * 工作流业务接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2023/8/29 9:40
 * @since 1.0.0
 */
public interface Workflow4OtherService {

    /**
     * 根据ID获取流程设置
     * 
     * @param id:
     * @return com.zkteco.zkbiosecurity.workflow.vo.Workflow4OtherFlowItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/29 10:39
     * @since 1.0.0
     */
    default Workflow4OtherFlowItem getFlowItemById(String id) {
        return null;
    }

    /**
     * 保存流程设置
     * 
     * @param workflow4OtherFlowItem:
     * @return com.zkteco.zkbiosecurity.workflow.vo.Workflow4OtherFlowItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/29 10:40
     * @since 1.0.0
     */
    default Workflow4OtherFlowItem saveFlowItem(Workflow4OtherFlowItem workflow4OtherFlowItem) {
        return null;
    }

    /**
     * 查询流程设置
     * 
     * @param workflow4OtherFlowItem:
     * @return java.util.List<com.zkteco.zkbiosecurity.workflow.vo.Workflow4OtherFlowItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/29 10:40
     * @since 1.0.0
     */
    default List<Workflow4OtherFlowItem> getFlowItemByCondition(Workflow4OtherFlowItem workflow4OtherFlowItem) {
        return null;
    }

    /**
     * 分页查询流程设置
     * 
     * @param workflow4OtherFlowItem:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/29 10:40
     * @since 1.0.0
     */
    default Pager getFlowItemByPage(Workflow4OtherFlowItem workflow4OtherFlowItem, int page, int size) {
        return new Pager();
    }

    /**
     * 按流程设置ID查询正在运行时相关流程
     * 
     * @param id:
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2023-11-22 9:45
     * @since 1.0.0
     */
    default boolean checkRuntimeProcessByFlowId(String id) {
        return false;
    }

}
