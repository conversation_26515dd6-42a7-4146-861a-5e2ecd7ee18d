package com.zkteco.zkbiosecurity.workflow.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 流程实例信息
 * 
 * <AUTHOR>
 * @date 2019/5/16
 */
@Getter
@Setter
@ToString
public class WorkflowProcessInfoItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 企业ID
     */
    private String companyId;
    /**
     * 流程类型
     */
    private String flowType;

    /**
     * 流程类型集合
     */
    private List<String> flowTypeList;

    /**
     * 申请人ID
     */
    private String applyUserId;

    /**
     * 申请人pin号
     */
    private String pin;
    /**
     * 审批人ID
     */
    private String approvalUserId;

    /**
     * 流程状态 1:未结束 2:已结束
     */
    private Integer flowStatus;
    /**
     * 流程businessKey
     */
    private String businessKey;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 任务状态 0:审批-同意 1:待审批 2:审批-驳回 3:撤销
     */
    private String taskStatus;
    /**
     * 知会人ID集合
     */
    private String notifierUserIds;

    /**
     * 流程任务节点信息
     */
    private List<WorkflowTaskInfoItem> taskInfoItemList;

    /**
     * 按创建时间排序 (asc/desc) 默认降序
     */
    private String orderByCreateTime;

    /**
     * 流程开始时间
     */
    private Date applyTime;

    /**
     * 流程结束时间
     */
    private Date approveTime;
    /**
     * 控制详情展示
     */
    private Boolean detail;
    /**
     * 控制审批展示
     */
    private Boolean approve;
    /**
     * 知会人信息map
     */
    private Map<String, String> notifierPers;
    /**
     * 流程删除理由
     */
    private String deleteReason;
    /**
     * 流程结束时间
     */
    private Date endTime;

    /** 审批时间 */
    private Date auditTime;

    /**
     * 关键字模糊查询
     */
    private String filter;
}
