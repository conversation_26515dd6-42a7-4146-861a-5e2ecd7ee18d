package com.zkteco.zkbiosecurity.workflow.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 流程人事职位
 * <AUTHOR>
 * @date 2019/04/04 16:02
 *
 */
@Getter
@Setter
public class Workflow4PersPositionItem  implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String code;

    private String name;

    private String parentId;

    private String inId;

    private String companyId;
    

}
