package com.zkteco.zkbiosecurity.workflow.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 流程审批提交信息
 * <AUTHOR>
 * @date 2019/5/20
 */
@Setter
@Getter
public class WorkflowCompleteInfoItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    private String companyId;

    /**
     * 任务节点ID
     */
    private String taskId;

    /**
     * 审批人
     */
    private String approvalUserId;

    /**
     * 审批结果 true/false
     */
    private String approval;

    /**
     * 审批批注
     */
    private String comment;

    /**
     * 知会人ID集合
     */
    private String notifierUserIds;
}
