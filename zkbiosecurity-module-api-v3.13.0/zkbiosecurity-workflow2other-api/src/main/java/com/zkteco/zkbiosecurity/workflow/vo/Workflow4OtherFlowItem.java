package com.zkteco.zkbiosecurity.workflow.vo;

import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 流程
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2023/8/29 10:15
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class Workflow4OtherFlowItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 流程编号 */
    private String flowNo;

    /** 流程名称 */
    private String flowName;

    /** 部门id */
    private String deptId;

    /** 部门编号 */
    private String deptCode;

    /** 部门名称 */
    private String deptName;

    /** 职位ID */
    private String positionId;

    /** 职位编码 */
    private String positionCode;

    /** 职位名称 */
    private String positionName;

    /** 流程类 */
    private String flowType;

    /** 节点审批描述 */
    private String wfFlowNodes;

    /**有效标识：0=启用、1=禁用 */
    private Short validFlag;

    /** 知会人ID集合 */
    private String notifierPerIds;

    /** 知会人类型：1=指定人、2=按职位 */
    private Short notifierType;

    private String companyId;

    private String flowTypeIn;

    private String inPositionId;

    /** 节点集合 */
    List<Workflow4OtherFlowNodeItem> wfflowNodeItems;

    /** 知会人名称集合 */
    private String notifierPerNames;

    /** 所属模块 */
    private String moduleCode;
}