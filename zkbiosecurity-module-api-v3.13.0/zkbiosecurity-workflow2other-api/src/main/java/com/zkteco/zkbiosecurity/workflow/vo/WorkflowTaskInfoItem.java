package com.zkteco.zkbiosecurity.workflow.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程任务节点信息
 * <AUTHOR>
 * @date 2019/5/16
 */
@Setter
@Getter
public class WorkflowTaskInfoItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 任务节点
     */
    private String taskName;
    /**
     * 流程状态
     */
    private String taskStatus;
    /**
     * 审批人ID
     */
    private String approvalUserId;
    
    /**
     * 审批人姓名
     */
    private String approvalUserName;
    /**
     * 审批时间
     */
    private Date approvalDateTime;
    /**
     * 审批批注
     */
    private String comment;

}
