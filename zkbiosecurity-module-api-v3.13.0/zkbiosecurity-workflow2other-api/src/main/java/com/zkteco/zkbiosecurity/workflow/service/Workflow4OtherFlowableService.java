package com.zkteco.zkbiosecurity.workflow.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowApplyInfoItem;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowCompleteInfoItem;
import com.zkteco.zkbiosecurity.workflow.vo.WorkflowProcessInfoItem;

import java.util.List;
import java.util.Map;

/**
 * 工作流相关
 * <AUTHOR>
 * @date 2019/5/16
 */
public interface Workflow4OtherFlowableService {

    /**
     * 获取审批人列表
     * @param wfApplyInfoItem
     * @return list [1,2,3]
     */
    List<String> getWorkflowApprove(WorkflowApplyInfoItem wfApplyInfoItem);

    /**
     * 获取第一节点知会人
     * @param wfApplyInfoItem
     * @return string "1,2,3"
     */
    String getFirstNotifier(WorkflowApplyInfoItem wfApplyInfoItem);

    /**
     * 创建流程
     * @param wfApplyInfoItem
     * @return businessKey
     */
    String start(WorkflowApplyInfoItem wfApplyInfoItem);

    /**
     * 查询流程实例 (我的申请)
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findProcessInstance(WorkflowProcessInfoItem condition, Integer pageNo, Integer pageSize);

    /**
     * 查询流程运行任务 (我的待办)
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findRunTimeTaskInstance(WorkflowProcessInfoItem condition, Integer pageNo, Integer pageSize);

    /**
     * 查询流程任务 (我的已办)
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager finHistoryTaskInstance(WorkflowProcessInfoItem condition, Integer pageNo, Integer pageSize);

    /**
     * 根据businessKey查询流程实例
     * @param businessKey
     * @return
     */
    WorkflowProcessInfoItem findProcessByBusinessKey(String businessKey);

    /**
     * 根据taskId 查询流程任务
     * @param taskId
     * @return
     */
    WorkflowProcessInfoItem findHistoricTaskByTaskId(String taskId);

    /***
     * 审批任务
     * @param workflowCompleteInfoItem
     */
    void complete(WorkflowCompleteInfoItem workflowCompleteInfoItem);

    /**
     * 撤销流程
     * @param businessKey
     * @param companyId
     * @param userId
     * @param revokeReason
     */
    ZKResultMsg revokeProcess(String businessKey, String companyId, String userId, String revokeReason);


    /**
     * 删除流程实例信息
     * @param businessKey
     * @return
     */
    ZKResultMsg deleteProcessInstance(String businessKey);

    /**
     * 根据businessKey查找流程参数
     * create by park.huang 2019/6/10
     **/
    Map<String, Object> findProcessVariablesByBusinessKey(String businessKey);

    /**
     * 删除流程
     * create by park.huang 2019/6/19
     **/
    Boolean deleteProcess(String businessKey, String deleteReason);

    /**
     * 获取流程下个节点审批人
     * create by park.huang 2019/07/24
     **/
    String getProcessNextApproverIds(String businessKey);

    /**
     * 获取流程当前的taskId
     * create by park.huang 2019/07/24
     **/
    String getRuntimeProcessTaskId(String businessKey);


}
