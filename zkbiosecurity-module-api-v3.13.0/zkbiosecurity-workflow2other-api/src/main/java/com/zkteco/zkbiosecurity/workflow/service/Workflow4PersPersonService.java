package com.zkteco.zkbiosecurity.workflow.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.workflow.vo.Workflow4PersPersonItem;
import com.zkteco.zkbiosecurity.workflow.vo.Workflow4PersPersonSelectItem;

import java.util.List;

/**
 * 流程人事接口
 * <AUTHOR>
 * @date 2019/04/04 16:02
 */
public interface Workflow4PersPersonService {

    /**
     * 根据personId查询人员
     * @param personId
     * @return
     */
    Workflow4PersPersonItem getItemById(String companyId, String personId);

    /**
     * 按条件查询人员
     * @param condition
     * @return
     */
    List<Workflow4PersPersonItem> getByCondition(Workflow4PersPersonItem condition);

    /**
     * 根据人员编号或者姓名获取人员集合
     * @param pin
     * @param name
     * @return
     */
    List<Workflow4PersPersonItem> getByPinLikeOrNameLike(String companyId, String pin, String name);

    /**
     * 根据人员选择条件查询
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager findPersonSelectItem(String sessionId, Workflow4PersPersonSelectItem condition, int pageNo, int pageSize);
}
