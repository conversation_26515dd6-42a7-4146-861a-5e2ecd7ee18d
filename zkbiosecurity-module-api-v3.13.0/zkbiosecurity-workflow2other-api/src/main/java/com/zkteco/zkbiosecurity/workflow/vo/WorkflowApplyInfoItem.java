package com.zkteco.zkbiosecurity.workflow.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * 流程申请信息
 * 
 * <AUTHOR>
 * @date 2019/5/20
 */
@Getter
@Setter
public class WorkflowApplyInfoItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 申请人 */
    private String applyUserId;

    /** 申请人pin号 */
    private String pin;

    /** 申请人姓名 */
    private String applyName;

    /** 被访人id */
    private String visitedId;

    /** 流程类型 */
    private String flowType;

    /** 流程类型名称 */
    private String flowTypeName;

    /** 申请开始时间 */
    private String applyStartDatetime;

    /** 申请结束时间 */
    private String applyEndDatetime;

    /** 请假时长 */
    private String leaveLong;

    /** 业务表单记录ID */
    private String formRecordId;

    /** 知会人ID集合 */
    private String notifierUserIds;

    /** 企业ID */
    private String companyId;
}
