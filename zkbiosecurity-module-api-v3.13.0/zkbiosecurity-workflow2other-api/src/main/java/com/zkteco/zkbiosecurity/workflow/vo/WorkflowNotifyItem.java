package com.zkteco.zkbiosecurity.workflow.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * 流程监听通知消息内容
 * <AUTHOR>
 * @date 2019/5/14
 *
 */
@Getter
@Setter
@ToString
public class WorkflowNotifyItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程定义key (流程类型)
     */
    private String processDefinitionKey;
    /**
     * 流程 businessKey
     */
    private String businessKey;
    /**
     * 流程实例ID
     */
    private String processInstanceId;
    /**
     * 任务ID
     */
    private String taskId;
    /**
     * 审批结果
     */
    private String approvalStatus;
    /**
     * 公司ID
     */
    private String companyId;
    /**
     * 申请人ID
     */
    private String applyUserId;
    /**
     * 审批人ID集合
     */
    private String approvalUserIds;
    /**
     * 知会人ID集合
     */
    private String notifierUserIds;
    /**
     * 参数扩展
     */
    private Map<String,Object> transientVariables;
}
