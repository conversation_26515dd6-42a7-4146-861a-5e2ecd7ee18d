package com.zkteco.zkbiosecurity.workflow.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 人事选择框
 * <AUTHOR>
 * @date 2019/04/08 10:42
 */
@Getter
@Setter
public class Workflow4PersPersonSelectItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String persPin;

    private String persName;

    private String persLastName;

    private Short personType;

    private String gender;

    private String deptCode;

    private String deptName;

    private String cardNo;

    private String deptId;

    private String idCard;

    private String type;

    private String linkId;

    private String notInlinkId;

    private String inId;

    private String inDeptId;

    private String notInId;

    private String likeName;
}
