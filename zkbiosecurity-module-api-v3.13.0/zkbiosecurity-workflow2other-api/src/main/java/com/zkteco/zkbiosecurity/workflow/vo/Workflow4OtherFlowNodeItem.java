package com.zkteco.zkbiosecurity.workflow.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 流程节点
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2023/8/29 10:16
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class Workflow4OtherFlowNodeItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 节点ID */
    public String id;

    /** 节点名称 */
    private String nodeName;

    /** 节点类型：0=直属领导、1=指定人、2=按职位、3=被访人、4=被访人上级 */
    private String nodeType;

    /** 结点类型名称 */
    private String nodeTypeName;

    /** 结点审批人ID */
    private String personId;

    /** 结点审批人pin */
    private String pin;

    /** 结点审批人姓名 */
    private String personName;

    /** 职位ID */
    private String positionId;

    /** 职位名称 */
    private String positionName;

    /** 知会人类型：1=指定人、2=按职位 */
    private Short notifierType;

    /** 知会人ID集合 */
    private String notifierPerIds;

    /** 知会人姓名集合 */
    private String notifierPerNames;

    /** 节点排序 */
    private String sortNo;
}