package com.zkteco.zkbiosecurity.workflow.service;

import com.zkteco.zkbiosecurity.workflow.vo.WorkflowNotifyItem;

/**
 * 工作流监听通知
 * <AUTHOR>
 * @date 2019/9/24
 */
public interface Workflow4OtherNotifyService {

    /**
     * 流程创建结束通知
     * @param workflowNotifyItem
     */
    void workflowApplyEndNotify(WorkflowNotifyItem workflowNotifyItem);

    /**
     * 审批节点创建通知
     * @param workflowNotifyItem
     */
    void workflowApprovalCreateNotify(WorkflowNotifyItem workflowNotifyItem);

    /**
     * 流程审批通知
     * @param workflowNotifyItem
     */
    void workflowApprovalEndNotify(WorkflowNotifyItem workflowNotifyItem);

    /**
     * 流程结束通知
     * @param workflowNotifyItem
     */
    void workflowFinishNotify(WorkflowNotifyItem workflowNotifyItem);

}
