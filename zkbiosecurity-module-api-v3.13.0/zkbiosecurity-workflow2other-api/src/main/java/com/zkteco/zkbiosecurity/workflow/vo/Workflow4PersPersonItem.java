package com.zkteco.zkbiosecurity.workflow.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 流程人事
 * <AUTHOR>
 * @date 2019/04/04 16:02
 *
 */
@Getter
@Setter
@Accessors(chain = true)
public class Workflow4PersPersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String companyId;

    private String id;

    private String pin;

    private String name;

    private String lastName;

    private String deptId;

    private String deptCode;

    private String deptName;

    private String positionId;

    private String inId;
    private String notInId;

    private String inPin;
    private String notInPin;

    private String inDeptId;
    private String notInDeptId;

    private String inPositionId;

    private Boolean equals = false;
}
