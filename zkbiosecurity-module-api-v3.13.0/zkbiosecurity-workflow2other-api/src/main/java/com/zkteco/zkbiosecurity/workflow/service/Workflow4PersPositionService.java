package com.zkteco.zkbiosecurity.workflow.service;


import com.zkteco.zkbiosecurity.workflow.vo.Workflow4PersPositionItem;

import java.util.List;

/**
 * 流程职位接口
 * <AUTHOR>
 * @date 2019/04/04 16:02
 */
public interface Workflow4PersPositionService {

    /**
     * 根据ID获取职位对象
     *
     * @param id
     * @return
     */
    Workflow4PersPositionItem getItemById(String id);

    /**
     * 按条件查询职位
     * @param condition
     * @return
     */
    List<Workflow4PersPositionItem> getByCondition(Workflow4PersPositionItem condition);
}
