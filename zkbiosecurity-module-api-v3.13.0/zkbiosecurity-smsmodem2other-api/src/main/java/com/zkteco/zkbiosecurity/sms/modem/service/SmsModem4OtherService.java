package com.zkteco.zkbiosecurity.sms.modem.service;

/**
 * sms提供给其他模块相关接口
 * 
 * <AUTHOR>
 * @DATE 2020-05-29 15:25
 * @since 1.0.0
 */
public interface SmsModem4OtherService {
    /**
     * 检测是否设置SMS
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-11-28 17:05
     * @param
     * @return boolean
     */
    boolean completeSMSModemInfo();

    /**
     * 发送事件短信
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-11-28 17:44
     * @param receiver
     * @param content
     * @param module
     * @return void
     */
    void sendSMS(String receiver, String content, String module);

    /**
     * 检查短信猫许可，判断是否在前端显示短信设置
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-02-18 10:27
     * @param
     * @return boolean
     */
    boolean checkSmsModemLicense();
}
