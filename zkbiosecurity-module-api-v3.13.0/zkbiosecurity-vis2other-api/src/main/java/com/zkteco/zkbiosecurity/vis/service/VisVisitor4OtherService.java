package com.zkteco.zkbiosecurity.vis.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.vis.vo.VisVisitCountItem;
import com.zkteco.zkbiosecurity.vis.vo.VisVisitorIInfo;

public interface VisVisitor4OtherService {
    /**
     * 根据访客pin获取访客信息
     *
     * <AUTHOR>
     * @date 2020/3/5 10:42
     * @param pin
     * @return
     */
    default VisVisitorIInfo getItemByPin(String pin) {
        return null;
    }

    /**
     * 获取访客信息列表，分页
     *
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws @date 2021-03-11 17:50
     * @since 1.0.0
     */
    default Pager getItemsByPage(VisVisitorIInfo condition, int page, int size) {
        return null;
    }

    /**
     * 获取访客信息列表
     *
     * @param visVisitorIInfo:
     * @return java.util.List<com.zkteco.zkbiosecurity.vis.vo.VisVisitorIInfo>
     * <AUTHOR>
     * @throws @date 2021-03-15 18:12
     * @since 1.0.0
     */
    default List<VisVisitorIInfo> getByCondition(VisVisitorIInfo visVisitorIInfo) {
        return null;
    }

    /**
     * 获取未签离访客信息
     * 
     * @return java.util.List<com.zkteco.zkbiosecurity.vis.vo.VisVisitorIInfo>
     * <AUTHOR>
     * @throws
     * @date 2023-07-13 9:49
     * @since 1.0.0
     */
    default List<VisVisitorIInfo> getCheckInVisitorInfo() {
        return null;
    }

    /**
     * 分页获取未签离访客信息
     * 
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date 2023-08-11 9:35
     * @since 1.0.0
     */
    default Pager getVisVisitorInfoByPage(VisVisitorIInfo condition, int page, int size) {
        return null;
    }

    /**
     * 根据记录id获取访客信息
     * 
     * @param visTransacitonId:
     * @return com.zkteco.zkbiosecurity.vis.vo.VisVisitorIInfo
     * <AUTHOR>
     * @throws
     * @date 2023-08-11 10:04
     * @since 1.0.0
     */
    default VisVisitorIInfo getVisTransactionInfoById(String visTransacitonId) {
        return null;
    }

    /**
     * 今日访问量
     * 
     * @return com.zkteco.zkbiosecurity.vis.vo.VisVisitCountItem
     * <AUTHOR>
     * @throws
     * @date 2024-09-02 10:00
     * @since 1.0.0
     */
    default VisVisitCountItem getVisTodayVisitCount() {
        return null;
    }

    /**
     * 获取访客信息
     * 
     * @param condition:
     * @return java.util.List<com.zkteco.zkbiosecurity.vis.vo.VisVisitorIInfo>
     * <AUTHOR>
     * @throws
     * @date 2025-05-14 10:10
     * @since 1.0.0
     */
    default List<VisVisitorIInfo> getVisitorInfoByCondition(VisVisitorIInfo condition) {
        return null;
    }
}
