package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.vis.vo.VisTransaction4OtherItem;

/**
 * <AUTHOR>
 * @date 2021/3/15 18:23
 * @since 1.0.0
 */
public interface VisTransaction4OtherService {
    /**
     * 访客登记信息推送
     * 
     * @param visTransaction4OtherItem:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-03-15 18:40
     * @since 1.0.0
     */
    default void registerVisTransaction(VisTransaction4OtherItem visTransaction4OtherItem) {

    }

    /**
     * 访客签离信息推送
     * 
     * @param visTransaction4OtherItem:
     * @return void
     * <AUTHOR>
     * @throws @date 2021-03-15 18:40
     * @since 1.0.0
     */
    default void exitVisTransaction(VisTransaction4OtherItem visTransaction4OtherItem) {

    }

}