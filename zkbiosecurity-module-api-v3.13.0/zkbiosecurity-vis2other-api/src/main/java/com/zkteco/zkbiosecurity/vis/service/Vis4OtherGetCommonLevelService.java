package com.zkteco.zkbiosecurity.vis.service;

import java.util.ArrayList;
import java.util.List;

/**
 * 访客通用权限组对外接口
 *
 * <AUTHOR>
 * @date 2021/7/27 17:40
 * @since 1.0.0
 */
public interface Vis4OtherGetCommonLevelService {

    /**
     * 根据pin获取通用权限组ids
     *
     * <AUTHOR>
     * @date  2021/7/27 17:56
     * @since 1.0.0
     */
    default List<String> getCommonLevelIdsByPin(String pin,String modul){
        return new ArrayList<>();
    }
}
