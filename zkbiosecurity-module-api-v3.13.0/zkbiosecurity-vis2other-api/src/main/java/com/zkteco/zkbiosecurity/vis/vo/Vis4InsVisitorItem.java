/*
 * File Name: Vis4InsVisitorItem Created by caiyun.chen on 2019/3/8 18:28. Copyright:Copyright © 1985-2017 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.vis.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Vis4InsVisitorItem implements Serializable {

    /** 访客id */
    // private String visId;

    /** 访客编号 */
    private String pin;

    /** 访客姓名 */
    private String name;

    /** 访客姓氏 */
    private String lastName;

    /** 证件类型 */
    private String certType;

    /** 证件号码 */
    private String certNumber;

    /** 性别 */
    private String gender;

    /** 单位 */
    private String company;

    /** 进入照片 */
    private String enterPhotoPath;

    /** 证件照片 */
    private String certPhotoPath;

    /** 来访事由 */
    private String visitReason;

    /** 携带物品 */
    private String carriedGoods;

    /** 车牌号 */
    private String carPlate;

    /** 手机号码 */
    private String phone;

    /** 被访人编号 */
    private String visitedEmpPin;

    /** 被访人姓名 */
    private String visitedEmpName;

    /** 被访人姓氏 */
    private String visitedEmpLastName;

    /** 被访部门 */
    private String visitedEmpDept;

    /** 被访部门ID */
    // private String visitedEmpDeptId;

    /** 权限开始时间 */
    private Date visitDate;

    /** 权限结束时间 */
    private Date visitEndDate;

    /** 来访状态 */
    private String visitStatus;

    private List<String> areaIdList;

    private List<String> deviceSnList;

    /** 卡号 */
    private String cardNo;

    private String email;

}
