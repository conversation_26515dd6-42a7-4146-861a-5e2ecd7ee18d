package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.vis.vo.VisGetEleLevelItem;

import java.util.List;

public interface VisGetEleLevelService {

    /**
     * 根据当前登录用户过滤权限数据
     * <AUTHOR>
     * @since 2018年11月22日 下午2:33:38
     * @param sessionId
     * @param visGetEleLevelItem
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, VisGetEleLevelItem visGetEleLevelItem, int pageNo, int pageSize);

    /**
     * @Description 获取梯控权限供管理员app调用
     * <AUTHOR>
     * @Date 2018/12/7 16:21
     * @Param 
     * @return 
     **/
    ZKResultMsg getAllEleLevels(String token, List<String> visLevelIds, String levelName, int pageNo, int pageSize);

    /**
     *获取梯控权限组
     * <AUTHOR>
     * @Date 2019/3/18 18:35
     * @param visGetEleLevelItem
     * @param pageNo
     * @param pageSize
     * @return Pager
     */
    Pager getItemsPager(VisGetEleLevelItem visGetEleLevelItem, int pageNo, int pageSize);
}
