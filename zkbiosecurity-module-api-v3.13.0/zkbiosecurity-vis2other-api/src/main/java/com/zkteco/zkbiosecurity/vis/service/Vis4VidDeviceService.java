package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.vis.vo.VisVisitorIInfo;

/**
 *
 * <AUTHOR>
 * @date 2021-06-16 18:12
 * @since 1.0.0
 */
public interface Vis4VidDeviceService {

    /**
     * 访客登记信息推送
     *
     * @param visVisitorIInfo
     * @return void
     * <AUTHOR>
     * @date 2021-01-20 16:08
     * @since 1.0.0
     */
    default void registerVisitorInfo(VisVisitorIInfo visVisitorIInfo) {

    }

    /**
     *访客签离信息推送
     *
     * @param visVisitorIInfo
     * @return void
     * <AUTHOR>
     * @date 2021-01-20 16:08
     * @since 1.0.0
     */
    default void exitVisitorInfo(VisVisitorIInfo visVisitorIInfo) {

    }
}
