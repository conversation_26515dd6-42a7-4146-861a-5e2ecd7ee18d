/**
 * @author:	GenerationTools
 * @date:	2018-03-13 上午11:35
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.vis.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 *
 * 
 * @author: GenerationTools
 * @date: 2018-03-13 上午11:35
 */
@Getter
@Setter
@Accessors(chain=true)
public class Vis4AccReaderItem extends BaseItem {

	/** 主键 */
	private String id;

	/**  */
	private String name;

	/**  */
	private String doorId;

	/**  */
	private String doorName;

	/**  */
	private Short readerNo;

	/** 读头类型 按位：0:不配置，1:RS485，2:韦根，3:RS485/韦根  4:网络读头，5:zigbee读头 */
	private Short commType;

	/** RS485 Address */
	private Short commAddress;

	/**  */
	private Short readerState;

	/** 绑定的摄像头 */
	private String channelId;

	private String channelName;


	/** ip地址 */
	private String ip;

	/** 端口 */
	private Short port;

	/** mac地址 */
	private String mac;

	/** 组播地址 */
	private String multicast;

	/** 读头加密 */
	private Boolean readerEncrypt;

	private String areaIdIn;

	/**设备SN**/
	private String deviceSn;

	/**
	 * 默认构造方法
	 */
	public Vis4AccReaderItem() {
		super();
	}

	/**
	 * 构造方法
	 */
	public Vis4AccReaderItem(Boolean equals) {
		super(equals);
	}

	/**
	 * @param id
	 */
	public Vis4AccReaderItem(String id) {
		super(true);
		this.id = id;
	}

	/**
	 * @param id
	 * @param name
	 * @param readerNo
	 * @param readerState
	 * @param commType
	 * @param commAddress
	 * @param ip
	 * @param port
	 * @param mac
	 * @param multicast
	 * @param readerEncrypt
	 */
	public Vis4AccReaderItem(String id, String name, Short readerNo, Short readerState, Short commType, Short commAddress, String ip, Short port, String mac, String multicast, Boolean readerEncrypt) {
		super();
		this.id = id;
		this.name = name;
		this.readerNo = readerNo;
		this.readerState = readerState;
		this.commType = commType;
		this.commAddress = commAddress;
		this.ip = ip;
		this.port = port;
		this.mac = mac;
		this.multicast = multicast;
		this.readerEncrypt = readerEncrypt;
	}
}