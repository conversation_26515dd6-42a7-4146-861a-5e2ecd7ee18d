/*
 * File Name: VisGetAccReaderZoneService
 * Created by caiyun.chen on 2018/12/25 11:37.
 * Copyright:Copyright © 1985-2017 ZKTeco Inc.All right reserved.
 */


package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.vis.vo.VisGetAccReaderZoneItem;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
public interface VisGetAccReaderZoneService {

    /**
    * 根据读头id获取门禁读头定义
    * <AUTHOR>
    * @param [readerId]
    * @return com.zkteco.zkbiosecurity.vis.vo.VisGetAccReaderZoneItem
    * @date 2018/12/25 11:59
    */
    VisGetAccReaderZoneItem getAccReaderZoneByReaderId(String readerId);
}
