package com.zkteco.zkbiosecurity.vis.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 获取信息屏相关信息
 *
 * <AUTHOR>
 * @date 2020/12/8 15:59
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Vis4GetInsDeviceItem implements Serializable {
    private String id;

    /** 设备序列号 */
    private String sn;

    /** 设备名称 */
    private String devName;

    /** 设备型号 */
    private String devModel;

    /** ip地址 */
    private String ipAddress;

    /** 是否登记机 */
    private Boolean isRegDevice;

    /** 区域id */
    private String authAreaId;
}
