/**
 * @author: GenerationTools
 * @date: 2018-03-13 上午11:35 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.vis.vo;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *
 * 
 * @author: GenerationTools
 * @date: 2018-03-13 上午11:35
 */
@Getter
@Setter
@Accessors(chain = true)
public class VisVisitorIInfo extends BaseItem {

    /** 主键 */
    private String id;

    /** 访客编号 */
    private String pin;

    /** 姓名 */
    private String name;

    /** 访客姓氏 */
    private String lastName;

    /** 证件名称 */
    private String certName;

    /** 证件号码 */
    private String certNumber;

    /** 手机号码 */
    private String phone;

    /** 国籍 */
    private Integer nationality;

    /** 姓名拼音 */
    private String nameSpell;

    /** 性别 */
    private String gender;

    private String idIn;

    private String notInId;

    private String headPortrait;

    private String inPin;

    /** 邮箱 */
    private String email;

    private String likeName;

    private String notInGender;

    private Short visitStateNot;

    private Date startEnterTime;

    private Date endEnterTime;
}