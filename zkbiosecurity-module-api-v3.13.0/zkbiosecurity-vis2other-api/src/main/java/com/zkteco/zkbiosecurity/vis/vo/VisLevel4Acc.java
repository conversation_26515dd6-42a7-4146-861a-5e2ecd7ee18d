/**
 * @author:	GenerationTools
 * @date:	2018-03-08 下午06:03
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.vis.vo;

import java.io.Serializable;
import java.util.Date;

/**
 * TODO
 * 
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:30
 */

public class VisLevel4Acc implements Serializable{
	private static final long serialVersionUID = 1L;

	/** 主键 */
	private String id;

	/** 权限组名称 */
	private String levelName;
	
	/** 权限模块名称 */
	private String levelModule;
	
	/** 修改时间 */
	private Date changeTime;
	
	/** 访客数 */
	private String personCount;
	
	/** 权限模块id */
	private String moduleId;

	/** 修改者 */
	private String changeOperator;

	private String inId;


	/**
	 * @return id 主键
	 */
	public String getId()
	{
		return id;
	}

	/**
	 * @param id 要设置的 主键
	 */
	public void setId(String id)
	{
		this.id = id;
	}

	/**
	 * @return moduleId 
	 */
	public String getModuleId()
	{
		return moduleId;
	}

	/**
	 * @param moduleId 要设置的 
	 */
	public void setModuleId(String moduleId)
	{
		this.moduleId = moduleId;
	}

	/**
	 * @return levelModule 
	 */
	public String getLevelModule()
	{
		return levelModule;
	}

	/**
	 * @param levelModule 要设置的 
	 */
	public void setLevelModule(String levelModule)
	{
		this.levelModule = levelModule;
	}

	/**
	 * @return levelName 
	 */
	public String getLevelName()
	{
		return levelName;
	}

	/**
	 * @param levelName 要设置的 
	 */
	public void setLevelName(String levelName)
	{
		this.levelName = levelName;
	}

	/**
	 * @return changeOperator 
	 */
	public String getChangeOperator()
	{
		return changeOperator;
	}

	/**
	 * @param changeOperator 要设置的 
	 */
	public void setChangeOperator(String changeOperator)
	{
		this.changeOperator = changeOperator;
	}

	/**
	 * @return changeTime 
	 */
	public Date getChangeTime()
	{
		return changeTime;
	}

	/**
	 * @param changeTime 要设置的 
	 */
	public void setChangeTime(Date changeTime)
	{
		this.changeTime = changeTime;
	}

	public String getPersonCount()
	{
		return personCount;
	}

	public void setPersonCount(String personCount)
	{
		this.personCount = personCount;
	}

	public String getInId()
	{
		return inId;
	}

	public void setInId(String inId)
	{
		this.inId = inId;
	}
	
}