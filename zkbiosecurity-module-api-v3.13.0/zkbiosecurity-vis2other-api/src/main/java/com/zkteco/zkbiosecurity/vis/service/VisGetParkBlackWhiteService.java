package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.vis.vo.VisGetParkBlackWhiteItem;

/**
 * <AUTHOR>
 * @date 2018-11-16 19:17
 */
public interface VisGetParkBlackWhiteService {
    /**
     * 判断是否黑白名单
     * 
     * <AUTHOR>
     * @since 2018年11月23日 下午5:39:22
     * @param carPlate
     * @param status
     * @return
     */
    boolean checkBlackWhite(String carPlate, short status);

    void deleteByIds(String parkBlackWhiteId);

    /**
     * 根据车牌号码获取黑白名单信息
     * 
     * <AUTHOR>
     * @since 2018年11月23日 下午5:45:24
     * @param carNumber
     * @return
     */
    VisGetParkBlackWhiteItem getParkBlackWhiteItemsByCarNumber(String carNumber);

    /**
     * 根据黑白名单id获取黑白名单信息
     * 
     * <AUTHOR>
     * @since 2018年11月23日 下午5:45:24
     * @param parkBlackWhiteId
     * @return
     */
    default VisGetParkBlackWhiteItem getParkBlackWhiteItemById(String parkBlackWhiteId) {
        return null;
    }

    ZKResultMsg saveItem(VisGetParkBlackWhiteItem vis4ParkBlackWhiteItem);

    /**
     * 获取车牌默认省份
     * 
     * <AUTHOR>
     * @param
     * @return java.lang.String
     * @date 2019/7/25 11:47
     */
    String getDefaultCarPlateProvince();
}
