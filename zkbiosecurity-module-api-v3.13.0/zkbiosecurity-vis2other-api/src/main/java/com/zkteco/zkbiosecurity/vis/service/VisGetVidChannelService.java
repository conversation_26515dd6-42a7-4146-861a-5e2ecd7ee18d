package com.zkteco.zkbiosecurity.vis.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.vis.vo.VisGetVidChannelItem;

/**
 * 访客桥接视频通道
 *
 * <AUTHOR>
 * @date 2018-11-16 18:22
 */
public interface VisGetVidChannelService {
    List<SelectItem> getChannelList();

    Map<String, Object> getVidCapture(Map<String, Object> param);

    /**
     * 根据ids查询列表
     * 
     * @param ids
     * @return
     */
    List<VisGetVidChannelItem> getItemsByIds(List<String> ids);

    /**
     * 获取rtsp流
     * 
     * @param channelItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2022-07-20 14:48
     * @since 1.0.0
     */
    default ZKResultMsg getRTSPStream(VisGetVidChannelItem channelItem) {
        return null;
    }
}
