package com.zkteco.zkbiosecurity.vis.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 消息中心获取访客详情vo
 *
 * <AUTHOR>
 * @date 2024/9/20 10:33
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class VisVisitor4AppItem implements Serializable {
    // 访客姓名
    private String name;
    // 邮箱
    private String email;
    private String phone;
    // 来访事由
    private String reason;
    private String visitStartDate;
    private String visitEndDate;
    private String photoPath;
}
