/*
 * File Name: VisGetAccReaderZoneItem
 * Created by caiyun.chen on 2018/12/25 11:46.
 * Copyright:Copyright © 1985-2017 ZKTeco Inc.All right reserved.
 */


package com.zkteco.zkbiosecurity.vis.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain=true)
public class VisGetAccReaderZoneItem extends BaseItem {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    private String accZoneFromId;

    private String accZoneToId;

    private String readerId;

}
