package com.zkteco.zkbiosecurity.vis.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class Vis4OtherPersonOpt implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;// 人员id
    private String pin;// 工号
    private String name;// 姓名
    private String lastName;// 姓
    private String personPwd;// 人员密码
    private String idCard;// 身份证
    private String cardNo;// 卡号
    private Date startTime;// 开始日期
    private Date endTime;// 结束日期
    private Short superAuth;// 超级用户
    private Long groupId;// 多人开门组id
    private Boolean disabled;// 禁用--黑名单
    private Short privilege;// 设备操作员
    private Boolean delayPassage;// 是否是残疾人
    private Boolean verifyStyle;// 个人验证方式
    private Short cardType;// 主副卡
    private Boolean tempUser;// 是否临时人员
    private Short cardState;// 卡状态
    private String photoPath; // 照片路径
    private String bioPhotoPath; // 比对照片路径
    private String nation; // 国籍
    private String gender; // 性别
    private String mobilePhone; // 手机号
    /** 新增默认楼层ID */
    private String addDefaultFloorIds;
    /** 删除默认楼层ID */
    private String delDefaultFloorIds;

}
