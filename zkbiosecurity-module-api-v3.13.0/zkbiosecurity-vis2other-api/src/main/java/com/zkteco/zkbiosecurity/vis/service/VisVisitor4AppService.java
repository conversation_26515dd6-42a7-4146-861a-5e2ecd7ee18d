package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.vis.vo.VisVisitor4AppItem;

/**
 *
 * <AUTHOR>
 * @date 2024/9/20 15:50
 * @since 1.0.0
 */
public interface VisVisitor4AppService {
    /**
     * 消息中心根据业务id及类型获取详情
     * 
     * @param businessId:
     * @param businessType:
     * @return com.zkteco.zkbiosecurity.vis.vo.VisVisitor4AppItem
     * <AUTHOR>
     * @throws
     * @date 2024-09-24 16:17
     * @since 1.0.0
     */
    default VisVisitor4AppItem getDetailByIdAndType(String businessId, String businessType) {
        return null;
    }
}
