package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.vis.vo.VisGetAccLevelItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-11-16 18:51
 */
public interface VisGetAccLevelService {
	/**
	 * 根据当前登录用户过滤权限数据
	 * <AUTHOR>
	 * @since 2018年11月22日 下午2:32:10
	 * @param sessionId
	 * @param visGetAccLevelItem
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
    Pager loadPagerByAuthFilter(String sessionId, VisGetAccLevelItem visGetAccLevelItem, int pageNo, int pageSize);

//    void setVisitorLevelToDev(List<Vis4AccPersonInfo> accPersonInfoItemList, List<String> strings, List<String> strings1);

    Object getItemById(String moduleId);

    /**
     * @Description 获取门禁权限供管理员app调用
     * <AUTHOR>
     * @Date 2018/12/7 15:42
     * @Param
     * @return
     **/
	ZKResultMsg getAllAccLevels(String token, List<String> visLevelIds, String levelName, int pageNo, int pageSize);

	/**
	* 获取门禁权限组列表
	* <AUTHOR>
	* @param visGetAccLevelItem, pageNo, pageSize
	* @return com.zkteco.zkbiosecurity.base.bean.Pager
	* @date 2019/8/21 10:06
	*/
	Pager getItemsPager(VisGetAccLevelItem visGetAccLevelItem, int pageNo, int pageSize);
}
