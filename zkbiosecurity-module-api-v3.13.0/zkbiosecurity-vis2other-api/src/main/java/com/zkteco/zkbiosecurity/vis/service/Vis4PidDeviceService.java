package com.zkteco.zkbiosecurity.vis.service;

import java.util.List;

import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonInfo;

/**
 * <AUTHOR>
 * @date 2020/9/30 15:28
 * @since 1.0.0
 */
public interface Vis4PidDeviceService {
    default void setVisitorLevelToDev(List<Vis4OtherPersonInfo> pidPersonInfoItemList) {}

    default void delVisitorLevelFromDev(List<String> pinList) {}

    default void delVisitorLevels(List<String> idCardList) {}

}