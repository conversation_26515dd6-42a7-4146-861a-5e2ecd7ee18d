package com.zkteco.zkbiosecurity.vis.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 下发人员相关参数bean--应该可以提取放到系统目录下，所有的业务模块都可以共用
 *
 * <AUTHOR>
 * @since 2014年8月22日 下午2:36:01
 */
public class Vis4OtherPersonInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    private List<Vis4OtherPersonOpt> personList = new ArrayList<Vis4OtherPersonOpt>();

    private List<Vis4OtherBioTemplate> bioTemplateItemList = new ArrayList<Vis4OtherBioTemplate>();

    public List<Vis4OtherPersonOpt> getPersonList()
    {
        return personList;
    }

    public void setPersonList(List<Vis4OtherPersonOpt> personList)
    {
        this.personList = personList;
    }

	public List<Vis4OtherBioTemplate> getBioTemplateItemList()
	{
		return bioTemplateItemList;
	}

	public void setBioTemplateItemList(List<Vis4OtherBioTemplate> bioTemplateItemList)
	{
		this.bioTemplateItemList = bioTemplateItemList;
	}

}
