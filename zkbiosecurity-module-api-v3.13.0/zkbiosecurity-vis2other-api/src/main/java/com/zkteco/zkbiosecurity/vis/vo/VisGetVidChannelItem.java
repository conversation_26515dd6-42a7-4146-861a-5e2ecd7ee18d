package com.zkteco.zkbiosecurity.vis.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2018-11-16 18:27
 */
@Getter
@Setter
@Accessors(chain = true)
public class VisGetVidChannelItem extends BaseItem {
    private String id;

    /** 通道名称 */
    private String name;

    /** 通道编号 */
    private Short channelNo;

    /** 通道编码-ivs模块 */
    private String channelCode;

    /** 主设备id-ivs模块 */
    private String parentId;

    /** 主设备名称 */
    private String parentName;

    /** 设备状态 */
    private String status;

    /** 区域名称 */
    private String areaName;

    /** 区域id */
    private String areaId;

    /** 设备名称 */
    private String alias;

    /** 设备id */
    private String deviceId;

    /** 禁用启用 */
    private Boolean enabled;

    /** 设备sn */
    private String devSn;

    private String inId;
}
