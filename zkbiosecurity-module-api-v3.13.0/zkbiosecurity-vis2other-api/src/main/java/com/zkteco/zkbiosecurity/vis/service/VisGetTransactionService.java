package com.zkteco.zkbiosecurity.vis.service;

import java.util.List;

import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonInfo;

/**
 * <AUTHOR>
 * @date 2021/3/17 18:38
 * @since 1.0.0
 */
public interface VisGetTransactionService {
    /**
     * 根据访客pin获取访客信息及生物模板信息
     *
     * @param visPins:
     * @return com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonInfo
     * <AUTHOR>
     * @throws @date 2021-03-17 10:35
     * @since 1.0.0
     */
    default Vis4OtherPersonInfo getVisInfoItemByVisPins(List<String> visPins) {
        return null;
    }

    /**
     * 根据权限组id，获取访客权限组下的人员
     *
     * @param levelId:
     * @return java.util.List<com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonInfo>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/8/16 16:00
     * @since 1.0.0
     */
    default List<Vis4OtherPersonInfo> getVisiotrInfosByLevelId(String levelId) {
        return null;
    }
}