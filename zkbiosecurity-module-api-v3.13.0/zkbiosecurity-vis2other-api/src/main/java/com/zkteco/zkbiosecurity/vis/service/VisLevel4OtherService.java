package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonInfo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 类描述 访客通知其他模块统一管理
 *
 * <AUTHOR>
 * @date 2021/6/8 7:03
 * @since 1.0.0
 */
public interface VisLevel4OtherService {

    /**
     * 方法描述 访客删除访客信息通知其他模块
     *
     * @param visitorPins: 访客编号集合
     * @return
     * <AUTHOR>
     * @throws
     * @date  2021-06-08 10:29
     * @since 1.0.0
     */
    default boolean pushDelOtherModelVisitor(Collection<String> visitorPins){ return false; }

    /**
     * 方法描述 访客删除权限组通知其他模块
     *
     * @param vis4OtherPersonInfoList: 访客人员和生物模板信息集合
     * @param levelIdMap: 访客对应模块权限组id的map集合；例如：<"acc", accLevelIds>,<"ele", eleLevelIds>等
     * @return
     * <AUTHOR>
     * @throws
     * @date  2021-06-08 14:52
     * @since 1.0.0
     */
    default void deleteVisLevel4Others(List<Vis4OtherPersonInfo> vis4OtherPersonInfoList, Map<String, List<String>> levelIdMap){}
}
