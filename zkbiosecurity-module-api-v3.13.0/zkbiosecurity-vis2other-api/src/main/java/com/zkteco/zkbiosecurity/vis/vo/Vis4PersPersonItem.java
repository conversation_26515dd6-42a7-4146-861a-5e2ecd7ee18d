package com.zkteco.zkbiosecurity.vis.vo;

import java.io.Serializable;

public class Vis4PersPersonItem implements Serializable
{

	private static final long serialVersionUID = 1L;
	
	private String personId;
	private String personPin;
	private String personName;
	private String personLastName;
	private String deptId;
	private String deptName;
	private String mobilePhone;

	public String getPersonId()
	{
		return personId;
	}
	public void setPersonId(String personId)
	{
		this.personId = personId;
	}
	public String getPersonPin()
	{
		return personPin;
	}
	public void setPersonPin(String personPin)
	{
		this.personPin = personPin;
	}
	public String getPersonName()
	{
		return personName;
	}
	public void setPersonName(String personName)
	{
		this.personName = personName;
	}
	public String getPersonLastName()
	{
		return personLastName;
	}
	public void setPersonLastName(String personLastName)
	{
		this.personLastName = personLastName;
	}
	public String getDeptId()
	{
		return deptId;
	}
	public void setDeptId(String deptId)
	{
		this.deptId = deptId;
	}
	public String getDeptName()
	{
		return deptName;
	}
	public void setDeptName(String deptName)
	{
		this.deptName = deptName;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}
}
