/*
 * File Name: Vis4InsVisitorService Created by caiyun.chen on 2019/3/11 9:15. Copyright:Copyright © 1985-2017 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.vis.service;

import java.util.List;

import com.zkteco.zkbiosecurity.vis.vo.Vis4InsVisitorItem;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
public interface Vis4InsVisitorService {
    /**
     * 下发访客信息至信息屏
     * 
     * <AUTHOR>
     * @param vis4InsVisitorItemList
     * @return void
     * @date 2019/3/8 18:29
     */
    void pushInsVisitorInfo(List<Vis4InsVisitorItem> vis4InsVisitorItemList);

    /**
     * 清空访客信息
     * 
     * @param devSn:
     * @return void
     * <AUTHOR>
     * @throws @date
     *             2020-12-30 11:24
     * @since 1.0.0
     */
    default void clearVisitorInfo(String devSn) {}

    /**
     * 删除访客信息
     * 
     * @param devSn:
     * @param visitorPin:
     * @return void
     * <AUTHOR>
     * @throws @date
     *             2021-01-26 15:42
     * @since 1.0.0
     */
    default void delVisitorInfo(String devSn, String visitorPin) {}
}
