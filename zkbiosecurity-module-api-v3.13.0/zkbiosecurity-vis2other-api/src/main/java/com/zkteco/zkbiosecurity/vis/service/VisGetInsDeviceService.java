/*
 * File Name: VisGetInsDeviceService Created by caiyun.chen on 2019/3/6 9:19. Copyright:Copyright © 1985-2017 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.vis.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.vis.vo.Vis4GetInsDeviceItem;

/**
 * 访客桥接信息屏设备
 * 
 * <AUTHOR> href:"mailto:<EMAIL>">caiyun.chen</a>
 * @version v1.0
 */
public interface VisGetInsDeviceService {
    /**
     * 获取信息屏当访客机列表
     * 
     * <AUTHOR>
     * @param
     * @return java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     * @date 2019/3/6 9:21
     */
    List<SelectItem> getInsDeviceList();

    String getAreaIdsByDeviceSn(String sn);

    /**
     * 根据sn号获取信息屏设备
     *
     * @param sn:
     * @return
     * @throws @date 2020/12/8 14:41
     * <AUTHOR>
     * @since 1.0.0
     */
    default Vis4GetInsDeviceItem getInsDeviceByDeviceSn(String sn) {
        return null;
    }
}
