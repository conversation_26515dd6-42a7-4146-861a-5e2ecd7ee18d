package com.zkteco.zkbiosecurity.vis.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 访问数量
 *
 * <AUTHOR>
 * @date 2024/9/2 9:54
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class VisVisitCountItem {

    /**
     * 今日签到数量
     */
    private Integer registerCount;

    /**
     * 今日访问数量(签到+预约数量)
     */
    private Integer visitCount;
}
