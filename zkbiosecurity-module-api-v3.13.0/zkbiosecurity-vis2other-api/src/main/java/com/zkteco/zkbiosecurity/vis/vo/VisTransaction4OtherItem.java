package com.zkteco.zkbiosecurity.vis.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/3/15 18:26
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class VisTransaction4OtherItem {
    private String id;

    /** 访客编号 */
    private String visEmpPin;

    /** 访客姓名 */
    private String visEmpName;

    /** 访客姓氏 */
    private String visEmpLastName;

    /** 证件类型 */
    private String visEmpCertType;

    /** 证件编号 */
    private String visEmpCertNumber;

    /** 权限开始时间 */
    private Date validStartTime;

    /** 权限结束时间 */
    private Date validEndTime;

    private String enterPhoto;

    private String gender;

    private String visEmpPhone;

    private String email;
    /** 访客是否设防 */
    private String defense;
    /** 名单库id */
    private String personnalListId;

}
