package com.zkteco.zkbiosecurity.vis.utils;


import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonOpt;

import java.util.*;

public class Vis4AccLevelUtil {

    /**
     * 权限变动，解析数据使用
     * 将 [{"pin": 1, "id": 1, "cardNo":111}] -> {"id": [1,2,3], "pin": [1,2,3]}
     *
     * @modify by: wenxin
     * @since 2014年8月29日 上午11:30:30
     * @param personInfoList
     * @return Map<String, Collection<String>>
     */
    public static Map<String, Collection<String>> setLevelMap(List<Vis4OtherPersonOpt> personInfoList)
    {
        Map<String, Collection<String>> delParamMap = new HashMap<>();
        List<String> idList = new ArrayList<>();
        List<String> pinList = new ArrayList<>();
        for (Vis4OtherPersonOpt personInfo : personInfoList)
        {
            idList.add(personInfo.getId());
            pinList.add(personInfo.getPin());
        }
        delParamMap.put("id", idList);
        delParamMap.put("pin", pinList);
        return delParamMap;
    }
}
