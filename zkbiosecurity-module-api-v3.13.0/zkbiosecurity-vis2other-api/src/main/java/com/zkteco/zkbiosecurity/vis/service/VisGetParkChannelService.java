package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.vis.vo.VisGetParkSelectChannelItem;

/**
 * 访客模块获取停车通道当签到点
 *
 * <AUTHOR>
 * @date 2021-10-281 14:49
 * @since 1.0.0
 */
public interface VisGetParkChannelService {
    /**
     * 根据id获取停车通道
     *
     * @param pointId
     * @return com.zkteco.zkbiosecurity.vis.vo.VisGetParkSelectChannelItem
     * <AUTHOR>
     * @date  2021-10-08 14:49
     * @since 1.0.0
     */
    VisGetParkSelectChannelItem getChannelById(String pointId);

    /**
     * 获取停车通道
     *
     * @param item
     * @param pageNo
     * @param pageSize
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date  2021-10-08 14:51
     * @since 1.0.0
     */
    Pager getSelectItemByPage(VisGetParkSelectChannelItem  item, int pageNo, int pageSize);
}
