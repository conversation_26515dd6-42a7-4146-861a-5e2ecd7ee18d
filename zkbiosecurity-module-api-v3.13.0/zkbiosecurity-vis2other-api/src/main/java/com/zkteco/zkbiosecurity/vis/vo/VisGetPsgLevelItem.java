package com.zkteco.zkbiosecurity.vis.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @DATE 2020-06-15 10:05
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class VisGetPsgLevelItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    private String name;
    private String notInId;
}
