/**
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:35
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.vis.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 黑白名单
 *
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:35
 */
@Getter
@Setter
@Accessors(chain = true)
public class VisGetParkBlackWhiteItem extends BaseItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 车牌号码
     */
    private String carNumber;

    /**
     * 0：白名单、1：黑名单
     */
    private Short state;

    /**  */
    private String carArea;

    /**
     * 开始时间段
     */
    private Date startTime;

    /**
     * 结束时间段
     */
    private Date endTime;

    private String number;

    private String defaultPlate;

    public VisGetParkBlackWhiteItem() {
        super();
    }

    public VisGetParkBlackWhiteItem(Boolean equals) {
        super(equals);
    }

    public VisGetParkBlackWhiteItem(String id) {
        super(true);
        this.id = id;
    }
}
