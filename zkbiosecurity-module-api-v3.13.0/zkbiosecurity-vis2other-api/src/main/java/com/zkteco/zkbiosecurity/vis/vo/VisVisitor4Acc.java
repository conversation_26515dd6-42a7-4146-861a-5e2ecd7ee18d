/**
 * @author:	GenerationTools
 * @date:	2018-03-05 下午08:06
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.vis.vo;
import java.io.Serializable;
import java.util.Date;


public class VisVisitor4Acc implements Serializable{
	private static final long serialVersionUID = 1L;
	private String id;
	
	/** 访客编号 */
	private String pin;

	/** 姓名 */
	private String name;
	
	/** 访客姓氏 */
	private String lastName;

	/** 证件类型 */
	private String certType;
	
	/** 证件名称 */
	private String certName;

	/** 证件号码 */
	private String certNumber;

	/** 公司 */
	private String company;
	
	/** 车牌号 */
	private String carPlate;
	
	/** 手机号码 */
	private String phone;
	
	/** 启用 */
	private Boolean enabled;
	
	/** 创建时间 */
	private Date createTime;
	
	/** 证件照片 */
	private String certPhoto;

	/** 国籍 */
	private Integer nationality;

	/** 姓名拼音 */
	private String nameSpell;

	/** 性别 */
	private String gender;

	/** 访客头像 */
	private String headPortrait;

	/** 修改作者 */
	private String changeOperator;

	/** 修改时间 */
	private Date changeTime;
	
	private String parkBlackWhiteId;


	/**
	 * @return id 主键
	 */
	public String getId()
	{
		return id;
	}

	/**
	 * @param id 要设置的 主键
	 */
	public void setId(String id)
	{
		this.id = id;
	}

	public String getCertType()
	{
		return certType;
	}

	public void setCertType(String certType)
	{
		this.certType = certType;
	}

	/**
	 * @return certNumber 
	 */
	public String getCertNumber()
	{
		return certNumber;
	}

	/**
	 * @param certNumber 要设置的 
	 */
	public void setCertNumber(String certNumber)
	{
		this.certNumber = certNumber;
	}

	/**
	 * @return certPhoto 
	 */
	public String getCertPhoto()
	{
		return certPhoto;
	}

	/**
	 * @param certPhoto 要设置的 
	 */
	public void setCertPhoto(String certPhoto)
	{
		this.certPhoto = certPhoto;
	}

	/**
	 * @return company 
	 */
	public String getCompany()
	{
		return company;
	}

	/**
	 * @param company 要设置的 
	 */
	public void setCompany(String company)
	{
		this.company = company;
	}

	public Boolean getEnabled()
	{
		return enabled;
	}

	public void setEnabled(Boolean enabled)
	{
		this.enabled = enabled;
	}

	/**
	 * @return pin 
	 */
	public String getPin()
	{
		return pin;
	}

	/**
	 * @param pin 要设置的 
	 */
	public void setPin(String pin)
	{
		this.pin = pin;
	}

	/**
	 * @return name 
	 */
	public String getName()
	{
		return name;
	}

	/**
	 * @param name 要设置的 
	 */
	public void setName(String name)
	{
		this.name = name;
	}

	/**
	 * @return lastName 
	 */
	public String getLastName()
	{
		return lastName;
	}

	/**
	 * @param lastName 要设置的 
	 */
	public void setLastName(String lastName)
	{
		this.lastName = lastName;
	}

	/**
	 * @return nationality 
	 */
	public Integer getNationality()
	{
		return nationality;
	}

	/**
	 * @param nationality 要设置的 
	 */
	public void setNationality(Integer nationality)
	{
		this.nationality = nationality;
	}

	/**
	 * @return nameSpell 
	 */
	public String getNameSpell()
	{
		return nameSpell;
	}

	/**
	 * @param nameSpell 要设置的 
	 */
	public void setNameSpell(String nameSpell)
	{
		this.nameSpell = nameSpell;
	}

	/**
	 * @return gender 
	 */
	public String getGender()
	{
		return gender;
	}

	/**
	 * @param gender 要设置的 
	 */
	public void setGender(String gender)
	{
		this.gender = gender;
	}

	/**
	 * @return phone 
	 */
	public String getPhone()
	{
		return phone;
	}

	/**
	 * @param phone 要设置的 
	 */
	public void setPhone(String phone)
	{
		this.phone = phone;
	}

	/**
	 * @return carPlate 
	 */
	public String getCarPlate()
	{
		return carPlate;
	}

	/**
	 * @param carPlate 要设置的 
	 */
	public void setCarPlate(String carPlate)
	{
		this.carPlate = carPlate;
	}

	/**
	 * @return headPortrait 
	 */
	public String getHeadPortrait()
	{
		return headPortrait;
	}

	/**
	 * @param headPortrait 要设置的 
	 */
	public void setHeadPortrait(String headPortrait)
	{
		this.headPortrait = headPortrait;
	}

	/**
	 * @return changeOperator 
	 */
	public String getChangeOperator()
	{
		return changeOperator;
	}

	/**
	 * @param changeOperator 要设置的 
	 */
	public void setChangeOperator(String changeOperator)
	{
		this.changeOperator = changeOperator;
	}

	/**
	 * @return changeTime 
	 */
	public Date getChangeTime()
	{
		return changeTime;
	}

	/**
	 * @param changeTime 要设置的 
	 */
	public void setChangeTime(Date changeTime)
	{
		this.changeTime = changeTime;
	}

	public Date getCreateTime()
	{
		return createTime;
	}

	public void setCreateTime(Date createTime)
	{
		this.createTime = createTime;
	}

	public String getCertName()
	{
		return certName;
	}

	public void setCertName(String certName)
	{
		this.certName = certName;
	}

	public String getParkBlackWhiteId()
	{
		return parkBlackWhiteId;
	}

	public void setParkBlackWhiteId(String parkBlackWhiteId)
	{
		this.parkBlackWhiteId = parkBlackWhiteId;
	}

}