/**
 * @author:	GenerationTools
 * @date:	2018-03-02 下午02:15
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.vis.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

/**
 * 门禁权限组vo
 * 
 * @author: yulong.dai
 * @date: 2018-03-02 下午02:15
 */
@Getter
@Setter
@Accessors(chain=true)
public class VisGetAccLevelItem extends BaseItem {

	private static final long serialVersionUID = 1L;

	/** 主键 */
	private String id;

	/** 权限组名称 */
	private String name;

	/** 系统区域id */
	private String authAreaId;

	private String authAreaName;

    private String timeSegId;

	/**  */
	private String timeSegName;

	private String doorCount;

	private Boolean initFlag;

	private String inId;

	private String notInId;

	private Boolean changeLevel;

	private String areaIds;

	/**
	 * 默认构造方法
	 */
	public VisGetAccLevelItem() {
		super();
	}

	/**
	 * 构造方法
	 */
	public VisGetAccLevelItem(Boolean equals) {
		super(equals);
	}

	/**
	 * @param id
	 */
	public VisGetAccLevelItem(String id) {
		super(true);
		this.id = id;
	}

	public VisGetAccLevelItem(String name, Boolean initFlag, String authAreaId, String timeSegId) {
		this.name = name;
		this.initFlag = initFlag;
		this.authAreaId = authAreaId;
		this.timeSegId = timeSegId;
	}
}