package com.zkteco.zkbiosecurity.vis.service;

import java.util.List;

import com.zkteco.zkbiosecurity.vis.vo.Vis4PersPersonItem;


public interface Vis4PersPersonService
{

	/**
	 * 删除检查
	 * <AUTHOR>
	 * @since 2018年7月31日 下午7:12:16
	 * @param personIds
	 * @return
	 */
	Boolean checkDelPerson(String personIds);
	
	/**
	 * 删除人员
	 * <AUTHOR>
	 * @since 2018年7月31日 下午7:05:13
	 * @param personIds
	 */
	void delVisitedPerson(String personIds);
	
	/**
	 * 离职人员
	 * <AUTHOR>
	 * @since 2018年7月31日 下午7:14:20
	 * @param personIds
	 * @return
	 */
	Boolean leaveVisitedPerson(String personIds);

	/**
	 * 编辑人事人员更新被访人(已弃用)
	 * <AUTHOR>
	 * @since 2018年7月31日 下午6:05:15
	 * @param visitedPerson
	 * @return
	 */
	@Deprecated
	Boolean editVisitedPerson(Vis4PersPersonItem visitedPerson);

	/**
	 * 批量调整部门通知，需要切换人员的部门权限(已弃用)
	 * <AUTHOR>
	 * @since 2018年7月31日 下午7:15:54
	 * @param personIds
	 * @param deptId
	 */
	@Deprecated
	void batchDeptChange(List<String> personIds, String deptId);
	
}
