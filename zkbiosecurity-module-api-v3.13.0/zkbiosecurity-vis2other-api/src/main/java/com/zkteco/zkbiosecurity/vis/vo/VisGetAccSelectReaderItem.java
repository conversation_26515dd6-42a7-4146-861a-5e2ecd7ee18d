/**
 * @author:	GenerationTools
 * @date:	2018-03-13 上午11:35
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.vis.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 读头定义选择控件列表
 * 
 * @author: verber
 * @date: 2018-04-08 15:35
 */
@Getter
@Setter
@Accessors(chain=true)
public class VisGetAccSelectReaderItem extends BaseItem {

	/** 主键 */
	private String id;

	/**  */
	private String name;

	private String doorName;

	/**  */
	private String doorId;

	/**  */
	private String deviceAlias;

	/**  */
	private String deviceSn;

	private String inId;

	private String notInId;

	private String type;

	private String selectId;

	private String authAreaIdIn;

	private String filterIds;

	/**
	 * 默认构造方法
	 */
	public VisGetAccSelectReaderItem() {
		super();
	}

	/**
	 * 构造方法
	 */
	public VisGetAccSelectReaderItem(Boolean equals) {
		super(equals);
	}

	/**
	 * @param id
	 */
	public VisGetAccSelectReaderItem(String id) {
		super(true);
		this.id = id;
	}

	/**
	 * @param id
	 * @param name
	 */
	public VisGetAccSelectReaderItem(String id, String name) {
		super();
		this.id = id;
		this.name = name;

	}
}