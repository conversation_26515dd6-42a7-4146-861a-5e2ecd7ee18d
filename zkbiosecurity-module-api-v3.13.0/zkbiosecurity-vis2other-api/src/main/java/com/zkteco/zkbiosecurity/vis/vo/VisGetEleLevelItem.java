package com.zkteco.zkbiosecurity.vis.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
@Getter
@Setter
@Accessors(chain=true)
public class VisGetEleLevelItem implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String id;
    private String name;
    private String timeSegName;
    private String notInId;
}
