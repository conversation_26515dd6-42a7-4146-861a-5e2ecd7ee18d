package com.zkteco.zkbiosecurity.vis.vo;

import java.io.Serializable;
import java.util.Comparator;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class Vis4OtherBioTemplate implements Serializable, Comparator<Vis4OtherBioTemplate> {
    private static final long serialVersionUID = 1L;
    public static final short FORMAT_BASE64 = 0;
    public static final short FORMAT_PHOTO = 1;

    private String pin;// 人员编号
    private Short templateId;// 生物模板id
    private Short templateIndex;// 模板编号
    private Boolean duress;// 生物模板类型
    private String template;// 生物模板
    private Short type;// 指纹，面部，指静脉
    private String version;// 生物模板版本
    private Short format;// 下发类型，0：模版, 1：url（图片）
    private String url;// 生物模版提取用图片
    private Short validType;

    public Vis4OtherBioTemplate(String pin, Short templateId, Short templateIndex, Boolean duress, String template,
        Short type) {
        super();
        this.pin = pin;
        this.templateId = templateId;
        this.templateIndex = templateIndex;
        this.duress = duress;
        this.template = template;
        this.type = type;
    }

    public Vis4OtherBioTemplate(String pin, Short templateId, Short type) {
        super();
        this.pin = pin;
        this.templateId = templateId;
        this.type = type;
    }

    public Vis4OtherBioTemplate(String pin) {
        this.pin = pin;
    }

    public Vis4OtherBioTemplate() {}

    /**
     * 模块id排序，从小到大
     */
    @Override
    public int compare(Vis4OtherBioTemplate o1, Vis4OtherBioTemplate o2) {
        // 根据是否是胁迫排序，又要指令下发那块，胁迫下发必须放在最后 modified by max 20160330
        if (o1.getDuress() && !o2.getDuress()) {
            return 1;
        } else if (o1.getDuress() && o2.getDuress()) {
            return 0;
        } else {
            return -1;
        }
    }
}
