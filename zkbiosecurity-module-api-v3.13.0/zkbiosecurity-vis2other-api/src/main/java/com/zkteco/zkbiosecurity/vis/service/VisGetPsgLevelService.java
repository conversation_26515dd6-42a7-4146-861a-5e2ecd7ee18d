package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.vis.vo.VisGetPsgLevelItem;

/**
 * <AUTHOR>
 * @DATE 2020-06-15 09:38
 * @since 1.0.0
 */
public interface VisGetPsgLevelService {
    default Pager loadPagerByAuthFilter(String sessionId, VisGetPsgLevelItem visGetEleLevelItem, int pageNo,
        int pageSize) {
        return null;
    }
}
