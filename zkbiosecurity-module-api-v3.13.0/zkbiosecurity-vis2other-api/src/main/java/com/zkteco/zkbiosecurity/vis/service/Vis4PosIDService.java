package com.zkteco.zkbiosecurity.vis.service;

import java.util.List;
import java.util.Set;

import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherBioTemplate;
import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonInfo;
import com.zkteco.zkbiosecurity.vis.vo.Vis4OtherPersonOpt;

public interface Vis4PosIDService {

	void editVisPerson(List<Vis4OtherPersonInfo> accPersonInfoItemList);

	void putPerson2FaceDeviceByPersonId(Vis4OtherPersonOpt vis4OtherPersonOpt,
			List<Vis4OtherBioTemplate> bioTemplateItemList, Set<String> deviceSns);

	void applyVisitorExit(List<Vis4OtherPersonInfo> vis4OtherPersonInfoList);
	
}
