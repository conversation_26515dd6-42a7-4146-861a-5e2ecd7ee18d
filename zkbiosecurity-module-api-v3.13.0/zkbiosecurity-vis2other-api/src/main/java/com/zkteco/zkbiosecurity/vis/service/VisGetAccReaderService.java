package com.zkteco.zkbiosecurity.vis.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.vis.vo.Vis4AccReaderItem;
import com.zkteco.zkbiosecurity.vis.vo.VisGetAccSelectReaderItem;

/**
 * <AUTHOR>
 * @date 2018-11-16 18:44
 */
public interface VisGetAccReaderService {
    Vis4AccReaderItem getReaderById(String pointId);

    Pager getSelectItemByPage(VisGetAccSelectReaderItem accSelectReaderItem, int pageNo, int pageSize);

    /**
    * 根据进出状态和门ID获取item
    * <AUTHOR>
    * @param readerState, doorId
    * @return com.zkteco.zkbiosecurity.vis.vo.Vis4AccReaderItem
    * @date 2019/9/19 16:20
    */
    Vis4AccReaderItem getByReaderStateAndDoorId(short readerState, String doorId);
}
