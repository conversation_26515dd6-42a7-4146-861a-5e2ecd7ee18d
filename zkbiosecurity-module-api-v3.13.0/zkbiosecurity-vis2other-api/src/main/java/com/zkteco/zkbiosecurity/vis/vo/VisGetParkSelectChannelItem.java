package com.zkteco.zkbiosecurity.vis.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 访客模块获取停车通道当签到点
 * 用于主流访客合并园区访客功能
 * <AUTHOR>
 * @date 2021-09-26 17:30
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class VisGetParkSelectChannelItem extends BaseItem {
    private String id;
    private String name;
    private String parkingAreaId;
    private Short parkingAreaType;
    private String pavilioId;
    private String pavilioName;
    private Short pavilioOpengagebyhandFlag;
    private Short state;
    private String devMaster;
    private String devMasterName;
    private Short devMasterAvoption;
    private String devSlave;
    private String devSlaveName;
    private Short devSlaveAvoption;
    private Short monthCarOpenType;
    private Short tempCarOpenType;
    private Short limitMode;
    private String restrictVehicleType;
    private Short opengageDeviceid;
    private String port;
    private Short cardDeviceType;
    private Short pictureCaptureMode;
    private String inId;
    private String notId;
    public VisGetParkSelectChannelItem() {
    }
    public VisGetParkSelectChannelItem(Boolean equals) {
        super(equals);
    }

    public VisGetParkSelectChannelItem(String id) {
        super(Boolean.valueOf(true));
        this.id = id;
    }

    public VisGetParkSelectChannelItem(String id, String name) {
        this.id = id;
        this.name = name;
    }

}
