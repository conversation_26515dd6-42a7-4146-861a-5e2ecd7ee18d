<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.zkteco</groupId>
        <artifactId>zkbiosecurity-pom</artifactId>
        <version>3.3.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.zkteco</groupId>
    <artifactId>zkbiosecurity-module-api</artifactId>
    <name>${project.artifactId}</name>
    <packaging>pom</packaging>
    <version>${revision}</version>
    <modules>
        <!--提供业务模块间相关api模块接口-->
        <module>zkbiosecurity-acc2other-api</module>
        <module>zkbiosecurity-att2other-api</module>
        <module>zkbiosecurity-ele2other-api</module>
        <module>zkbiosecurity-ins2other-api</module>
        <module>zkbiosecurity-park2other-api</module>
        <module>zkbiosecurity-patrol2other-api</module>
        <module>zkbiosecurity-pid2other-api</module>
        <module>zkbiosecurity-pos2other-api</module>
        <module>zkbiosecurity-posID2other-api</module>
        <module>zkbiosecurity-vis2other-api</module>
        <module>zkbiosecurity-vid2other-api</module>
        <module>zkbiosecurity-led2other-api</module>
        <module>zkbiosecurity-module-all</module>
        <module>zkbiosecurity-dashboard2other-api</module>
        <module>zkbiosecurity-ivideo2other-api</module>
        <module>zkbiosecurity-psg2other-api</module>
        <module>zkbiosecurity-workflow2other-api</module>
        <module>zkbiosecurity-ad2other-api</module>
        <module>zkbiosecurity-vms2other-api</module>
        <module>zkbiosecurity-line2other-api</module>
        <module>zkbiosecurity-smsmodem2other-api</module>
        <module>zkbiosecurity-hep2other-api</module>
        <module>zkbiosecurity-fence2other-api</module>
        <module>zkbiosecurity-esdc2other-api</module>
        <module>zkbiosecurity-icm2other-api</module>
        <module>zkbiosecurity-whatsapp2other-api</module>
        <module>zkbiosecurity-oameeting2other-api</module>
        <module>zkbiosecurity-locker2other-api</module>
        <module>zkbiosecurity-vdb2other-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zkteco</groupId>
                <artifactId>zkbiosecurity-base</artifactId>
                <version>${boot.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <properties>
        <module.name>module-api</module.name>
        <revision>3.13.0-RELEASE_YFDZ2025071400104</revision>
        <boot.version>3.12.0-RELEASE</boot.version>
    </properties>

    <!-- 引入maven release 插件, 用于正式版本的自动发布 -->
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-release-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <!-- gitlab地址, 用于生产环境自动打TAG -->
    <scm>
        <developerConnection>scm:git:*******************************:biosecurity-pro/zkbiosecurity-module-api.git
        </developerConnection>
        <tag>HEAD</tag>
    </scm>

    <repositories>
        <repository>
            <id>zkteco-internal-repository-releases</id>
            <name>zkteco-internal-repository-releases</name>
            <url>http://192.168.200.31:8080/artifactory/zkteco-internal-repository</url>
        </repository>
        <repository>
            <id>zkteco-internal-repository-snapshots</id>
            <name>zkteco-internal-repository-snapshots</name>
            <url>http://192.168.200.31:8080/artifactory/zkteco-internal-repository</url>
        </repository>
    </repositories>
</project>