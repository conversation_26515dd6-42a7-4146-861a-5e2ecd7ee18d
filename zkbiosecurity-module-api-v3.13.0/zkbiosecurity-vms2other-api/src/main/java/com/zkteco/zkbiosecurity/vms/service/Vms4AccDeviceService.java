package com.zkteco.zkbiosecurity.vms.service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/2 16:21
 * @since 1.0.0
 */
public interface Vms4AccDeviceService {

    /**
     * 校验设备是否在门禁模块中使用
     *
     * @param devSnList:设备序列号集合
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2020-11-02 16:24
     * @since 1.0.0
     */
    default Boolean checkDeviceExistBySn(List<String> devSnList) {
        return true;
    }
}
