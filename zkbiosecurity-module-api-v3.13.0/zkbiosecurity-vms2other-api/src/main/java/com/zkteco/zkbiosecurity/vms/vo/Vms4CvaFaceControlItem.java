package com.zkteco.zkbiosecurity.vms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Vms4CvaFaceControlItem
 *
 * @Auther Abel.huang
 * @Date 2019/4/11
 **/
@Getter
@Setter
@Accessors(chain = true)
public class Vms4CvaFaceControlItem implements Serializable {

    /**
     * 报警日志id
     * */
    private String id;

    /**
     * 报警时间
     * */
    private String timeUTC;

    /**
     * 报警类型
     * */
    private int alarmType;

    /**
     * 设备SN
     * */
    private String serialNum;

    /**
     * 0为告警开始，1为告警结束
     * */
    private int isStoped;

    /**
     * 告警参数，告警类型为告警输入时表示告警输入号，否则为通道号
     * */
    private int param;

    /**
     * 报警源
     * */
    private String alarmSource;

    @ApiModelProperty(value = "告警设备ip")
    private String ip;

    @ApiModelProperty(value = "告警设备名称")
    private String alias;

    @ApiModelProperty(value = "告警通道名称")
    private String channelName;

    @ApiModelProperty(value = "抓图base64字符串")
    private String captureBase64;

}
