package com.zkteco.zkbiosecurity.vms.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-04-14 18:17
 */
@Getter
@Setter
@Accessors(chain = true)
public class VmsSelectAuxOutItem extends BaseItem implements Serializable {

    /** 辅助输出 Id*/
    private String id;

    private String auxName;

    private Short auxNo;

    private String deviceAlias;

    private String deviceSn;

    private String deviceId;


    private String inId;

    private String notInId;

    private String type;

    private String selectId;

    private String authAreaIdIn;

    private String filterIds;


}
