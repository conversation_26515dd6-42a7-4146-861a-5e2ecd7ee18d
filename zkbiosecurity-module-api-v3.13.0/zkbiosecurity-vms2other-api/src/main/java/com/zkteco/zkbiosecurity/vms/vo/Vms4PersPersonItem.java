package com.zkteco.zkbiosecurity.vms.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class Vms4PersPersonItem implements Serializable {

    /** 人员id */
    private String personId;

    /** 人员编号 */
    private String pin;

    /** 人员名字  */
    private String name;

    /** 人员姓氏 */
    private String lastName;

    /** 照片路径*/
    private String photoPath;

    /**手机号码*/
    private String phone;
}
