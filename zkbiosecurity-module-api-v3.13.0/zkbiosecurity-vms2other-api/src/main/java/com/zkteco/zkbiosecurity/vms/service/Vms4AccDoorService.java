package com.zkteco.zkbiosecurity.vms.service;


import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

public interface Vms4AccDoorService {

    /**
     * 通过读头id远程控制门接口
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/10/14 13:39
     * @param opType 操作类型
     * @param openInterval 操作时长
     * @param readerIds 读头id
     * @return
     */
    ZKResultMsg operateDoorByReaderIds(String opType, String openInterval, String readerIds);
}
