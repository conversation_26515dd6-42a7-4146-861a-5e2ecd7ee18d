package com.zkteco.zkbiosecurity.vms.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.vms.vo.VmsSelectDoorItem;

/**
 * VMS获取门禁设备门参数
 *
 * <AUTHOR>
 * @date 2020-04-14 18:11
 */
public interface VmsGetAccDoorService {

    /**
     * 远程操作门
     *
     * @param type
     *            openDoor/normalOpenDoor/enableNormalOpenDoor/closeDoor/closeDoor *
     *            disableNormalOpenDoor/cancelAlarm/lockDoor/unLockDoor
     * @param opInterval
     *            操作间隔
     * @param doorId
     *            门ID
     * @return 操作结果
     */
    default ZKResultMsg operate(String type, int opInterval, String doorId) {
        return null;
    }

    /**
     * 根据门id获取名称等信息
     */
    default List<VmsSelectDoorItem> getItemsById(List<String> doorIds) {
        return null;
    }

    /**
     * 根据查询条件获取门
     *
     * @param sessionId
     *            用于过滤当前用户的设备权限
     * @param condition
     *            查询条件
     * @param pageNo
     *            页码
     * @param size
     *            数量
     * @return 分页数据
     */
    default Pager getItemByAuthFilter(String sessionId, VmsSelectDoorItem condition, int pageNo, int size) {
        return null;
    }

    /**
     * 获取doorIds中所有不支持Lockdown的门信息Lockdown
     * 
     * @param doorIds
     *            需要检索的门Id
     * @return 不支持Lockdown的门
     */
    default List<VmsSelectDoorItem> getNotSupportLockdownDoors(List<String> doorIds) {
        return null;
    }

}
