package com.zkteco.zkbiosecurity.vms.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class Vms4AccMapPosItem implements Serializable {
    /** 主键 */
    @Column(name="t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name="t.MAP_ID")
    @GridColumn(label = "acc_accMapPos_map")
    private String mapId;

    /**  */
    @Column(name="am.name")
    @GridColumn(label = "acc_accMapPos_map")
    private String mapName;

    /**  */
    @Column(name="t.ENTITY_TYPE")
    @GridColumn(label = "acc_accMapPos_entityType")
    private String entityType;

    /**  */
    @Column(name="t.ENTITY_ID")
    @GridColumn(label = "acc_accMapPos_entityId")
    private String entityId;

    private String entityName;
    /**门节点时候才有值*/
//	private String lockDisplay;
    /**  */
    @Column(name="t.WIDTH")
    @GridColumn(label = "acc_accMapPos_width")
    private Double width;

    /**  */
    @Column(name="t.LEFT_X")
    @GridColumn(label = "acc_accMapPos_leftX")
    private Double leftX;

    /**  */
    @Column(name="t.TOP_Y")
    @GridColumn(label = "acc_accMapPos_topY")
    private Double topY;
}
