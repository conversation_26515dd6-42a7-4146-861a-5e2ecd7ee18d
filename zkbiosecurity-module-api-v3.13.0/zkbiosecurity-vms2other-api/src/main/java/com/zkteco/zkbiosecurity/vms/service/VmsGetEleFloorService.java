package com.zkteco.zkbiosecurity.vms.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.vms.vo.VmsSelectFloorItem;

/**
 * VMS获取梯控楼层
 *
 * <AUTHOR>
 * @date 2020-04-14 18:11
 */
public interface VmsGetEleFloorService {

    /**
     * 远程操作楼层
     *
     * @param type
     *            openFloor/closeFloor/normalOpenFloor/enableNormalOpenFloor/disableNormalOpenFloor
     * @param opInterval
     *            操作间隔
     * @param floorId
     *            楼层ID
     * @return 操作结果
     */
    default ZKResultMsg operate(String type, int opInterval, String floorId) {
        return null;
    }

    /**
     * 根据楼层id获取名称等信息
     */
    default List<VmsSelectFloorItem> getItemsByIds(List<String> floorIds) {
        return null;
    }

    /**
     * 根据查询条件获取楼层
     *
     * @param sessionId
     *            用于过滤当前用户的设备权限
     * @param condition
     *            查询条件
     * @param pageNo
     *            页码
     * @param size
     *            数量
     * @return 分页数据
     */
    default Pager getItemByAuthFilter(String sessionId, VmsSelectFloorItem condition, int pageNo, int size) {
        return null;
    }
}
