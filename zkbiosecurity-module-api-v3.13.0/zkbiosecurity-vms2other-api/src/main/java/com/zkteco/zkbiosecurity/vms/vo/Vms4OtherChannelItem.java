package com.zkteco.zkbiosecurity.vms.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 其他模块调用Vms视频通道通用类
 *
 * <AUTHOR>
 * @date 2020/11/6 8:58
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class Vms4OtherChannelItem {
    private static final long serialVersionUID = 1L;
    /** 主键 */
    private String id;

    /** 通道名称 */
    private String name;

    /** 通道编号 */
    private Short channelNo;

    /** 区域名称 */
    private String areaName;

    /** 区域id */
    private String areaId;

    /** 设备名称 */
    private String alias;

    /** 设备id */
    private String deviceId;

    /** * 禁用启用 */
    private Boolean enabled;

    /** 设备序列号 */
    private String sn;

    /** 查询条件:Id被包含 */
    private String inId;

    /** 查询条件:Id不被包含 */
    private String notInId;
}
