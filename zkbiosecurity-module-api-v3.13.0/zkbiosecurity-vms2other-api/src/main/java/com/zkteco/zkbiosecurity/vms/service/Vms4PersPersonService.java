package com.zkteco.zkbiosecurity.vms.service;

import com.zkteco.zkbiosecurity.vms.vo.Vms4PersPersonItem;

public interface Vms4PersPersonService {

	/**
	 * 提供人事调用vms人员编辑接口, 修改黑白名单人员属性
	 * <AUTHOR>
	 * @since 2020/2/27 9:57
	 */
	default Boolean editVmsPerson(Vms4PersPersonItem vmsPerson){return true;}

	/**
	 * 删除检查
	 * <AUTHOR>
	 * @since 2020/2/27 9:58
	 */
	default Boolean checkDelPerson(String personIds){return true;}

	/**
	 * 删除人员
	 * <AUTHOR>
	 * @since 2020/2/27 9:58
	 */
	void delVmsPerson(String personIds);

	/**
	 * 离职人员
	 * <AUTHOR>
	 * @since 2020/2/27 9:58
	 */
	default Boolean leaveVmsPerson(String personIds){return true;}
}
