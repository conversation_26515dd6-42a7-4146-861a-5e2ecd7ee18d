package com.zkteco.zkbiosecurity.vms.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class Vms4AccMapItem implements Serializable {
    /** 主键 */
    @Column(name="t.ID")
    @GridColumn(checkbox = true, width = "40")
    private String id;

    /**  */
    @Column(name="t.AUTH_AREA_ID")
    @GridColumn(label = "acc_accMap_baseArea")
    private String authAreaId;

    /**  */
    @Column(name="t.NAME")
    @GridColumn(label = "acc_accMap_name")
    private String name;

    /**  */
    @Column(name="t.MAP_PATH")
    @GridColumn(label = "acc_accMap_mapPath")
    private String mapPath;

    /**  */
    @Column(name="t.WIDTH")
    @GridColumn(label = "acc_accMap_width")
    private Double width;

    /**  */
    @Column(name="t.HEIGHT")
    @GridColumn(label = "acc_accMap_height")
    private Double height;

    @Condition(value = "t.AUTH_AREA_ID IN (%s)",formatType = "quote")
    private String areaIdIn;
}
