package com.zkteco.zkbiosecurity.vms.service;

import com.zkteco.zkbiosecurity.base.bean.TreeItem;
import com.zkteco.zkbiosecurity.vms.vo.Vms4AccMapItem;
import com.zkteco.zkbiosecurity.vms.vo.Vms4AccMapPosItem;

import java.util.List;

public interface Vms4AccMapService {

    default Vms4AccMapItem getItemById(String id) {
        return null;
    }

    default List<Vms4AccMapPosItem> getMapPosItemList(String mapId) {
        return null;
    }

    default String getVidChannelName(String entityId) {
        return null;
    }

    default List<TreeItem> createMapTree(String sessionId) {
        return null;
    }

    default List<String> getMapChannel(String mapId) {
        return null;
    }
}
