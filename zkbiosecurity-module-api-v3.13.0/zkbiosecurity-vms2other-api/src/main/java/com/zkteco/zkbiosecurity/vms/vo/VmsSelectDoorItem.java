package com.zkteco.zkbiosecurity.vms.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-04-14 18:17
 */
@Getter
@Setter
@Accessors(chain = true)
public class VmsSelectDoorItem extends BaseItem implements Serializable {

    /** 门Id*/
    private String id;

    private String doorName;

    private Short doorNo;

    private String deviceAlias;

    private String deviceSn;

    private String deviceId;


    private String inId;

    private String notInId;

    private String type;

    private String selectId;

    private String authAreaIdIn;

    private String filterIds;


}
