package com.zkteco.zkbiosecurity.vms.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.vms.vo.VmsSelectAuxOutItem;

/**
 * VMS获取门禁辅助输出
 *
 * <AUTHOR>
 * @date 2020-04-14 18:11
 */
public interface VmsGetAccAuxOutService {

    /**
     * 远程操作辅助输出
     *
     * @param type
     *            openAuxOut/closeAuxOut/auxOutNormalOpen
     * @param opInterval
     *            操作间隔
     * @param auxOutId
     *            辅助输出ID
     * @return 操作结果
     */
    default ZKResultMsg operate(String type, int opInterval, String auxOutId) {
        return null;
    }

    /**
     * 根据辅助输出id获取名称等信息
     */
    default List<VmsSelectAuxOutItem> getItemsByIds(List<String> auxOutIds) {
        return null;
    }

    /**
     * 根据查询条件获取辅助输出
     *
     * @param sessionId
     *            用于过滤当前用户的设备权限
     * @param condition
     *            查询条件
     * @param pageNo
     *            页码
     * @param size
     *            数量
     * @return 分页数据
     */
    default Pager getItemByAuthFilter(String sessionId, VmsSelectAuxOutItem condition, int pageNo, int size) {
        return null;
    }

}
