package com.zkteco.zkbiosecurity.vms.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020-04-14 18:18
 */
@Getter
@Setter
@Accessors(chain = true)
public class VmsSelectFloorItem extends BaseItem implements Serializable {

    /** 楼层 Id*/
    private String id;

    private String floorName;

    private Short floorNo;

    private String deviceAlias;

    private String deviceSn;

    private String deviceId;


    private String inId;

    private String notInId;

    private String type;

    private String selectId;

    private String authAreaIdIn;

    private String filterIds;


}
