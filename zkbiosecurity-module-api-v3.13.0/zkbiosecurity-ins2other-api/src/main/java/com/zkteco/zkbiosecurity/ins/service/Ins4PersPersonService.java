package com.zkteco.zkbiosecurity.ins.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.ins.vo.Ins4PersPersonItem;

public interface Ins4PersPersonService {

    /**
     * @Description: 提供人事调用大屏机人员编辑接口，即大屏机人员属性编辑
     *
     * @author: mingfa.zheng
     * @date: 2018/8/17 11:30
     * @param: [insPersonItem]
     * @return: java.lang.Boolean
     **/
    Boolean editInsPerson(Ins4PersPersonItem insPersonItem);

    /**
     * 删除人员
     * 
     * <AUTHOR>
     * @date 2018/10/9 11:58
     * @param personIds
     * @return java.lang.Boolean
     */
    void delInsPerson(String personIds);

    /**
     * 批量保存导入人员信息
     * 
     * @param insPersons
     */
    void batchPerson(List<Ins4PersPersonItem> insPersons);

    /**
     * @Description 获取信息屏扩展参数
     * <AUTHOR>
     * @Date 2019/1/14 18:57
     * @Param personId
     * @Return java.util.Map<java.lang.String,java.lang.String>
     */
    Map<String, String> getInsPersonExtParam(String personId);

    /**
     * 人事部门调整，信息屏InsPerson 同步修改。
     * 
     * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @Date 2019/5/31 17:36
     * @Param [personIds, deptId]
     * @return void
     */
    void batchDeptChange(List<String> personIds, String deptId);

    /**
     ** <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @Description 消费发卡/补卡 重新下发人员信息
     * @date 2020/1/21
     **/
    void reIssuePersonInfo(String personId);
}
