package com.zkteco.zkbiosecurity.ins.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
public class Ins4AttTransactionItem {

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 英文（lastName）
     */
    private String personLastName;


    /**
     * 区域id
     */
    private String areaId;

    /**
     * 设备Id
     */
    private String deviceId;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 门编号
     */
    private Short doorNo;

    /**
     * 考勤日期时间
     */
    private Date attDatetime;

    /**
     * 考勤日期
     */
    private String attDate;

    /**
     * 考勤时间
     */
    private String attTime;

    /**
     * 考勤状态
     */
    private String attState;

    /**
     * 验证方式
     */
    private String attVerify;

}
