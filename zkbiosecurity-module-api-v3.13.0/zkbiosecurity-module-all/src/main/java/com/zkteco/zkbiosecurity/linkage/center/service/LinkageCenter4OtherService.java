package com.zkteco.zkbiosecurity.linkage.center.service;

import com.zkteco.zkbiosecurity.linkage.center.vo.LinkageCenter4OtherTransactionItem;

/**
 * <AUTHOR>
 * @date 2024/9/5 14:40
 * @since 1.0.0
 */
public interface LinkageCenter4OtherService {

    /**
     * 推送事件到联动中心
     * 
     * <AUTHOR>
     * @date 2024-09-05 14:41
     * @since 1.0.0
     */
    default void pushTransactionToLinkageCenter(LinkageCenter4OtherTransactionItem item) {

    }

    /**
     * 输入点是否在联动配置中使用
     * 
     * @param inputPointId: 输入点ID
     * @return boolean
     * <AUTHOR>
     * @date 2024-10-11 15:25
     * @since 1.0.0
     */
    default boolean isUsedInLinkageCenterConfig(String inputPointId) {
        return false;
    }

    /**
     * 删除联动配置输入点信息
     * 
     * @param ids: 输入点ids
     * <AUTHOR>
     * @date 2025-02-12 16:02
     * @since 1.0.0
     */
    default void deleteLinkageConfigInputById(String ids) {};
}
