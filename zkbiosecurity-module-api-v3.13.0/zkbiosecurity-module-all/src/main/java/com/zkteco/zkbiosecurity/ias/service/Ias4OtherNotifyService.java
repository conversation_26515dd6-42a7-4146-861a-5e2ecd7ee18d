package com.zkteco.zkbiosecurity.ias.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.ias.vo.Ias4OtherArmTypeItem;
import com.zkteco.zkbiosecurity.ias.vo.Ias4OtherPartitionItem;

/**
 * 入侵调用，模块实现，入侵通知其他模块接口
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 13:56 2022/11/30
 * @version v1.0
 */
public interface Ias4OtherNotifyService {

    /**
     *
     * 通知分区删除（设备可以配置分区使用或者不使用，所以更新分区会删除不使用的分区，默认通知其他模块直接删除）
     *
     * @param partitionIdList:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/12/6 15:46
     * @since 1.0.0
     */
    default void deletePartitionNotify(List<String> partitionIdList) {}

}