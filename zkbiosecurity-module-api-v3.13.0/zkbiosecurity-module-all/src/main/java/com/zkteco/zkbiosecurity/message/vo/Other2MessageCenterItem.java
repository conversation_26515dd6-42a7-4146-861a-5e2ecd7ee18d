package com.zkteco.zkbiosecurity.message.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 推送至消息中心的记录
 *
 * <AUTHOR>
 * @date 2021-08-05 16:06
 * @since 1.0.0
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class Other2MessageCenterItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 标题 （必填） */
    private String title;

    /** 消息类型 1.考勤异常 2.系统提醒 3.访客动态 （必填） */
    private String msgType;

    /** 消息内容 （必填） */
    private String content;

    /**
     * 业务类型 考勤异常申请对应的业务类型：1-请假、2-加班、3-补签、4-调休补班、5-调班、14-出差、15外出 访客动态对应的业务类型：6-预约、7-签到、8-签离；
     * 知会消息：9-空间预约、10-取消预约、11-结束预约、12-服务知会、13-会议开始前知会
     */
    private String businessType;

    /** 来源模块 */
    private String sourceModule;

    /** 消息链接地址 */
    private String href;

    /** 接受者id */
    private String receiverId;

    /** 接受者对象类型 person员工 manager管理者 */
    private String receiverObj;

    /** 提示方式 1.弹出提示 */
    private String displayMode;

    /** 预留拓展业务数据 格式建议为json */
    private String extData;

    /** 创建时间 */
    private Date createTime;

    /** 业务id */
    private String businessId;

    private List<Other2MessageReceiverItem> other2MessageReceiverItemList;
}
