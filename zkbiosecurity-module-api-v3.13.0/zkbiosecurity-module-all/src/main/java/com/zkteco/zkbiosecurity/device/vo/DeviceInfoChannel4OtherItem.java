package com.zkteco.zkbiosecurity.device.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 通道item
 *
 * <AUTHOR>
 * @date 2021/1/26 13:34
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class DeviceInfoChannel4OtherItem {

    /** 主键 */
    private String id;

    /*** 摄像机名称键盘可见字符和中文，长度限制192字节*/
    private String name;

    /*** 摄像机类型：l 0：固定枪机l 1：有云台枪机 l 2：球机 l 3：半球-固定摄像机 l 4：筒机*/
    private String type;

    /*** 设备类型：l 1：IPC l 2：DVS l 3：DVR l 4：eNVR*/
    private String formType;

    /*** 前端IP点分十进制格式，例如：**************，长度限制64字节*/
    private String ip;

    /** 厂商 */
    private String vendor;

    /*** 设备状态：l 0：离线 l 1：在线 l 2：休眠*/
    private String status;

    private String devId;

    private String inChannelCode;

    /*** 通道code*/
    private String channelCode;

    /*** 设备编码 长度限制64字节*/
    private String cameraCode;

    /*** 通道号 1: 通道1，2: 通道2（双仓摄像机存在2个通道）*/
    private String channelNo;

    /*** 域编码*/
    private String domainCode;

    /*** 父设备类型*/
    private String parentType;

    /*** 位置*/
    private String location;

    /*** 操作的摄像机名称*/
    private String cameraName;

    /*** 摄像机类型*/
    private String cameraType;

    /*** 类别*/
    private String category;

    /*** 主设备型号由各设备厂家提供，长度限制32字节*/
    private String model;

    /*** 协议类型*/
    private String protocolType;

    /*** 设备所属NVR编码*/
    private String nvrCode;

    /*** 父设备编码*/
    private String parentCode;

    /*** 网络类型，0：有线，1：无线*/
    private String netType;

    /*** 智能分析*/
    private Boolean intelligent;

    /*** 启用语音*/
    private Boolean voiceInEnable;

    /*** 外域*/
    private Boolean exDomain;

    /*** 经度*/
    private String longitude;

    /*** 纬度*/
    private String latitude;

    /*** 摄像机安装高度*/
    private String height;

    /*** 删除状态，0：待彻底删除，1：设备正常*/
    private String deleteStatus;

    /*** 互联编码，长度限制64字节*/
    private String connectCode;

    /*** 是否配置告警联动计划，0：否，1：是*/
    private Boolean haveAlarmLinkage;

    /*** 1400支持能力，0：不支持，1,：仅支持1400，2：同时支持1400协议和视频能力*/
    private String supportGA1400;

    /*** 是否作为影子摄像机*/
    private Boolean shadowDev;

    /*** 组id*/
    private String groupID;

    /*** 继承状态*/
    private String extendStatus;

    private String visualAngle;

    private String horizontalAngle;

    private String visualDistance;

    /*** 通道*/
    private String channel;

    /*** 订阅状态*/
    private Boolean subscription;

    /** 唯一值*/
    private String reserve;

    /** 父设备id */
    private String vidParentDeviceId;

    /*** 来源，0：本地，1：云端*/
    private String isFrom;

    /*** 云服务设备id*/
    private String cameraId;

    /**协议类型  1-好望协议,2-GB28181 **/
    private String connectType;

    /** 区域id */
    private String areaId;
}
