package com.zkteco.zkbiosecurity.ivs.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * rtsp流返回结果
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-03-08 下午06:03
 */
@Setter
@Getter
public class IvsRtspItem implements Serializable {

    /*** rtsp流地址*/
    private String rtspUrl;

    /*** 类型*/
    private String type;

    /** 用户名 */
    private String sdcUserName;

    /** 密码 */
    private String sdcPassword;

    /** 主设备id*/
    private String parentDeviceId;

    /** 通道code */
    private String channelCode;
}
