package com.zkteco.zkbiosecurity.abs.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;

@From(after = "ABS_LINKAGE_TASK t")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
public class AbsLinkageTaskSelect4OtherItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "common_name", width = "120")
    private String name;

    @Column(name = "t.BROADCAST_TYPE")
    @GridColumn(label = "abs_task_broadcastType", width = "100")
    private String broadcastType;

    private String type;

    private String filterId;

    private String selectId;

    @Column(name = "t.ID", equalTag = "in")
    private String inId;

    @Column(name = "t.ID", equalTag = "not in")
    private String notInId;

}
