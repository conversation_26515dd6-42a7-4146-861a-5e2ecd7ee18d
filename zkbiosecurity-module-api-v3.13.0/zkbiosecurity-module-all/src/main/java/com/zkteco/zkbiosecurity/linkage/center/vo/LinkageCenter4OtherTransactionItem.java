package com.zkteco.zkbiosecurity.linkage.center.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/9/5 14:42
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class LinkageCenter4OtherTransactionItem {

    /** 模块（space/ene） */
    private String module;

    /** 事件编号 */
    private String eventNo;

    /** 事件名称 */
    private String eventName;

    /** 输入点ID（space：空间ID；ene：属性ID） */
    private String inputId;

    /** 输入点名称 */
    private String inputName;

    /** 设备名称（ene：终端名称） */
    private String deviceName;
}
