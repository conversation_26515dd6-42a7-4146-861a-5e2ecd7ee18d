package com.zkteco.zkbiosecurity.ivs.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 获取trsp回放流参数
 *
 * <AUTHOR>
 * @date 2021-08-17 15:58
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class IvsRtspPlayBackGetItem {

    /** 开始时间 */
    private String startDate;

    /** 结束时间 */
    private String endDate;

    /** 通道id */
    private String channelId;
}
