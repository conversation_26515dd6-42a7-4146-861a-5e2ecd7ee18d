package com.zkteco.zkbiosecurity.push.service;

import com.zkteco.zkbiosecurity.push.vo.PushCenter4OtherItem;

/**
 * 推送中心
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/8/31 19:26
 * @since 1.0.0
 */
public interface PushCenter4OtherService {

    /**
     * 数据推送
     *
     * @param pushCenter4OtherItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/6 8:56
     * @since 1.0.0
     */
    default void pushData(PushCenter4OtherItem pushCenter4OtherItem) {}
}
