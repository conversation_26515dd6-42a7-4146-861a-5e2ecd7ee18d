package com.zkteco.zkbiosecurity.message.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 推送事件
 *
 * <AUTHOR>
 * @date 2021-08-09 10:30
 * @since 1.0.0
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class MessageCenterLog4OtherItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 标题 */
    private String title;

    /** 消息类型 */
    private String msgType;

    /** 消息内容 */
    private String content;

    /** 业务类型 考勤异常申请对应的业务类型：1-请假、2-出差、3-出外、4-补签、5-加班； 访客动态对应的业务类型：6-预约、7-签到、8-签离； */
    private String businessType;

    /** 来源模块 */
    private String sourceModule;

    /** 消息链接地址 */
    private String href;

    /** 接受者id */
    private String receiverId;

    /** 接受者对象类型 person员工 manager管理者 */
    private String receiverObj;

    /** 提示方式 1.弹出提示 */
    private String displayMode;

    /** 预留拓展业务数据 格式建议为json */
    private String extData;

    /** 创建时间 */
    private Date createTime;
}
