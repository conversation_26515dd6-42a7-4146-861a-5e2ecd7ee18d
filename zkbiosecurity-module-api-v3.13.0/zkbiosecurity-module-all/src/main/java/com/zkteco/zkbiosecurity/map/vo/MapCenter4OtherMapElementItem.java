package com.zkteco.zkbiosecurity.map.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/1 15:17
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class MapCenter4OtherMapElementItem extends BaseItem implements Serializable {

    private String id;

    /** 地图ID */
    private String mapId;

    /** 元素类型 AccDoor:门, VidChannel:普通摄像头, VmsChannel: vms摄像头, Map:地图, DefendArea:防区 */
    private String entityType;

    /** 元素ID */
    private String entityId;

    private String inEntityId;

    private String notInEntityId;

}
