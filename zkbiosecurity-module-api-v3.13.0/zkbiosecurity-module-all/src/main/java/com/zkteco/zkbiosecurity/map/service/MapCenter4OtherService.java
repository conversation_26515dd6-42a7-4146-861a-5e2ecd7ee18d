package com.zkteco.zkbiosecurity.map.service;

import com.zkteco.zkbiosecurity.map.vo.MapCenter4OtherMapElementItem;
import com.zkteco.zkbiosecurity.map.vo.MapCenter4OtherMapElementPosItem;
import com.zkteco.zkbiosecurity.map.vo.MapCenter4OtherMapItem;
import com.zkteco.zkbiosecurity.psg.vo.PsgReader4OtherItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/1 14:44
 * @since 1.0.0
 */
public interface MapCenter4OtherService {

    /**
     * 获取地图集合
     *
     * @param sessionId:
     * @return java.util.List<com.zkteco.zkbiosecurity.map.vo.MapCenter4OtherGetMapItem>
     * <AUTHOR>
     * @date 2022-11-01 15:12
     * @since 1.0.0
     */
    default List<MapCenter4OtherMapItem> getMapItemList(String sessionId) {
        return null;
    }

    /**
     * 根据条件查询获取摄像机列表
     *
     * @param condition:
     * @param pageNo:
     * @param size:
     * @return java.util.List<com.zkteco.zkbiosecurity.map.vo.MapCenter4OtherMapElementItem>
     * <AUTHOR>
     * @date 2022-11-01 17:19
     * @since 1.0.0
     */
    default List<MapCenter4OtherMapElementItem> getMapVidElementByCondition(MapCenter4OtherMapElementItem condition,
        int pageNo, int size) {
        return null;
    }

    /**
     * 根据地图ID获取地图中摄像机坐标等信息
     *
     * @param mapId:
     * @return java.util.List<com.zkteco.zkbiosecurity.map.vo.MapCenter4OtherMapElementPosItem>
     * <AUTHOR>
     * @date 2022-11-02 9:36
     * @since 1.0.0
     */
    default List<MapCenter4OtherMapElementPosItem> getMapVidElementPosItems(String mapId) {
        return null;
    }
}
