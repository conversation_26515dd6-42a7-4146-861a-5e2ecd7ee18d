package com.zkteco.zkbiosecurity.vid.vo;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/8/13
 */
@Setter
@Getter
@Accessors(chain=true)
public class HwChannelSelect4OtherItem extends BaseItem implements Serializable {

    @GridColumn(columnType = "ra", width = "40", sort = "na")
    private String id;

    /*** 设备编码 长度限制64字节*/
    private String cameraCode;

    /*** 通道code*/
    @GridColumn(label = "通道code", width = "100")
    private String channelCode;


    private String channelNo;

    private String domainCode;

    private String parentType;

    /** 通道id */
    private String deviceId;

    @GridColumn(label = "hw_att_channelName", width = "100")
    private String name;

    @GridColumn(label = "hw_att_vidName", width = "100")
    private String deviceName;

    @GridColumn(label = "hw_att_vidIp", width = "100")
    private String deviceIp;

    @GridColumn(label = "hw_att_channelIp", width = "100")
    private String ip;

    /*** 判断是左列表noSelect、还是右列表select*/
    private String type;

    /*** 当前选中的ids*/
    private String selectId;

    @GridColumn(label = "common_ownedDev",width = "120")
    private String alias;

    @GridColumn(label = "common_dev_devType", format = "0=IVS1800,1=IVS3800,2=NVR800,5=SDC,4=CLOUD")
    private String deviceType;

    /*** 主设备ID*/
    private String parentDeviceId;
}
