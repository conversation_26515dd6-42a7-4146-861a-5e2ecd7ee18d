package com.zkteco.zkbiosecurity.event.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.event.vo.EventCenterAffirmItem;
import com.zkteco.zkbiosecurity.event.vo.EventCenterTypeItem;
import com.zkteco.zkbiosecurity.event.vo.Other2EventCenterItem;

/**
 * 推送至事件中心
 *
 * <AUTHOR>
 * @date 2021/1/7 8:59
 * @since 1.0.0
 */
public interface Other2EventCenterService {

    /**
     * 推送事件
     *
     * @param item
     * @return void
     * <AUTHOR>
     * @date 2021-01-07 9:30
     * @since 1.0.0
     */
    default void push2EventCenter(Other2EventCenterItem item) {

    }

    /**
     * 确认事件
     *
     * @param item
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-01-14 15:17
     * @since 1.0.0
     */
    default ZKResultMsg affirmEventLog(EventCenterAffirmItem item) {
        return null;
    }
}
