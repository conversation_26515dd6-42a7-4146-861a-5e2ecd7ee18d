package com.zkteco.zkbiosecurity.event.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;


/**
 * 获取事件log的请求参数item
 *
 * <AUTHOR>
 * @date 2021/1/7 9:16
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class EventCenterGetLogItem {

    private String eventIds;

    /** 开始时间*/
    private Date startTime;

    /** 结束时间 */
    private Date endTime;

    /** 对象名 */
    private String objName;

    /**
     * Obj name not in 条件
     */
    private String objNameNotIn;

    /**
     *  事件对象类型，围绕智慧出入口 "人行、车行、物检"
     *  0：人 1：车 2：物 3:设备 4：部件
     *  非空
     **/
    private String objType;

    /**
     * obj type查询条件
     */
    private String objTypeNotIn;

    /**
     *  事件对象唯一值标记，如人员pin号，人员身份证号，车牌号，第三方业务数据唯一值
     *  pin号、车牌、身份证号
     *  对于设备及部件：reserve
     **/
    private String objKey;

    /**  区域id */
    private String areaId;

    /**
     * Area id not in.
     */
    private String areaIdNotIn;

    /**
     *  事件来源的唯一值标记，reserve
     **/
    private String sourceKey;

    /**  来源模块 */
    private String sourceModuleIn;

    /** 等级值 0:正常 1:一般 2:异常 3:紧急 支持 0,1,2 = 三种等级的数据*/
    private String eventLevelValIn;

    /** 事件来源的唯一值标记 In*/
    private String sourceKeyIn;

    /** 分页参数*/
    private Integer pageNo;
    private Integer pageSize;

    /** 排序字段名 */
    private String sortName;
    /** 排序方式 倒序：desc  正序：acs */
    private String sortOrder;

    /**
     * 陌生人IDs
     */
    private String reIds;

    /**
     * Group by.
     */
    private String groupBy;

    /**  处理状态 0：未确认 1：已确认 */
    private String processState;
    
    /*是否展示标准操作流程*/
    private String isOperatingProcedure;

    /*事件类型id*/
    private String eventTypeId;
}
