package com.zkteco.zkbiosecurity.space.service;

import com.zkteco.zkbiosecurity.space.vo.SpaceReservation4AppItem;

/**
 *
 * <AUTHOR>
 * @date 2024/9/20 16:08
 * @since 1.0.0
 */
public interface SpaceReservation4AppService {
    /**
     * 消息中心根据业务id及类型获取详情
     * 
     * @param businessId:
     * @param businessType:
     * @return com.zkteco.zkbiosecurity.space.vo.SpaceReservation4AppItem
     * <AUTHOR>
     * @throws
     * @date 2024-09-24 16:18
     * @since 1.0.0
     */
    default SpaceReservation4AppItem getDetailByIdAndType(String businessId, String businessType) {
        return null;
    }
}
