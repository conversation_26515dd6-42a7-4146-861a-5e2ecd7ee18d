package com.zkteco.zkbiosecurity.push.service;

import java.util.Date;

import com.zkteco.zkbiosecurity.push.enums.PushCenter4OtherEnum;

/**
 * 推送中心手动通知各个模块重新发送
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/8/31 19:26
 * @since 1.0.0
 */
public interface PushCenterNotifyOtherService {

    /**
     * 推送中心手动通知各个模块重新发送
     *
     * @param startTime:
     * @param endTime:
     * @param pushCenter4OtherEnum:
     * @return boolean true：模块匹配已处理，false：模块未匹配
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/3 17:25
     * @since 1.0.0
     */
    default boolean notifyPushData(Date startTime, Date endTime, PushCenter4OtherEnum pushCenter4OtherEnum) {
        return true;
    }
}
