package com.zkteco.zkbiosecurity.ias.service;

import java.util.Collection;
import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.ias.vo.Ias4OtherArmTypeItem;
import com.zkteco.zkbiosecurity.ias.vo.Ias4OtherPartitionItem;

/**
 * 入侵对外公共接口
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 13:56 2022/11/30
 * @version v1.0
 */
public interface Ias4OtherService {

    /**
     * 根据分区ID集合，获取分区数据
     *
     * @param partitionIdList:
     * @return java.util.List<com.zkteco.zkbiosecurity.ias.vo.Ias4OtherPartitionItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/11/30 14:33
     * @since 1.0.0
     */
    default List<Ias4OtherPartitionItem> getItemsByIds(List<String> partitionIdList) {
        return null;
    }
    
    /**
     * 分页获取分区数据
     * 
     * @param sessionId: 
     * @param condition: 
     * @param pageNo: 
     * @param size: 
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/11/30 14:33
     * @since 1.0.0
     */
    default Pager getItemByAuthFilter(String sessionId, Ias4OtherPartitionItem condition, int pageNo, int size) {
        return null;
    }

    /**
     * 根据厂商获取分区布防类型
     *
     * @param manufacturer:
     * @return java.util.List<com.zkteco.zkbiosecurity.ias.vo.Ias4OtherArmTypeItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/11/30 14:34
     * @since 1.0.0
     */
    default List<Ias4OtherArmTypeItem> getArmType(String manufacturer) {
        return null;
    }

    /**
     * 分区布防
     *
     * @param partitionId:
     * @param armType:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/11/30 14:34
     * @since 1.0.0
     */
    default ZKResultMsg arm(String partitionId, Integer armType) {
        return null;
    }

    /**
     * 分区撤防
     *
     * @param partitionId:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/11/30 14:34
     * @since 1.0.0
     */
    default ZKResultMsg disarm(String partitionId) {
        return null;
    }
}