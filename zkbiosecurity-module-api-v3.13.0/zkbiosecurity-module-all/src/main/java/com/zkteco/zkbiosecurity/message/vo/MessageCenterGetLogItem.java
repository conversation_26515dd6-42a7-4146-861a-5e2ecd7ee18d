package com.zkteco.zkbiosecurity.message.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 获取消息
 *
 * <AUTHOR>
 * @date 2021-08-09 16:20
 * @since 1.0.0
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class MessageCenterGetLogItem {

    private String id;

    /** 标题 */
    private String title;

    /** 消息类型 */
    private String msgType;

    /** 业务类型 */
    private String businessType;

    /** 来源模块 */
    private String sourceModule;

    /** 消息内容 */
    private String content;

    /** 接受者id */
    private String receiverId;

    /** 接受者对象类型 person员工 manager管理者 */
    private String receiverObj;

    /** 分页参数 */
    private Integer pageNo;
    private Integer pageSize;
}
