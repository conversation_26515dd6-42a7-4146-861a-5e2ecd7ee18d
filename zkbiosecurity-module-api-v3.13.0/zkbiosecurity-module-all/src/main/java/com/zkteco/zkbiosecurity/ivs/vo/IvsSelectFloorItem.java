package com.zkteco.zkbiosecurity.ivs.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 梯控楼层
 *
 * <AUTHOR>
 * @date  2022/7/18 10:16
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class IvsSelectFloorItem extends BaseItem implements Serializable {

    /** 楼层 Id*/
    private String id;

    private String floorName;

    private Short floorNo;

    private String deviceAlias;

    private String deviceSn;

    private String deviceId;


    private String inId;

    private String notInId;

    private String type;

    private String selectId;

    private String authAreaIdIn;

    private String filterIds;
}
