package com.zkteco.zkbiosecurity.ivs.service;

import java.util.List;

/**
 * 智能视频调用门禁业务接口
 *
 * <AUTHOR>
 * @date 2022/5/9 17:45
 */
public interface Ivs4AccDeviceService {
    /**
     * 校验设备是否在门禁模块中使用
     * 
     * <AUTHOR>
     * @date 2022-05-09 17:57
     * @param devSnList
     * @return 
     */
    default Boolean checkDeviceExistBySn(List<String> devSnList) {
        return false;
    }
    
}
