package com.zkteco.zkbiosecurity.ivs.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.ivs.vo.IvsSelectFloorItem;

import java.util.List;

/**
 * ivs获取梯控楼层
 *
 * <AUTHOR>
 * @date  2022/7/18 9:51
 * @since 1.0.0
 */
public interface IvsGetEleFloorService {

    /**
     * 远程操作楼层
     *
     * @param type       openFloor/closeFloor/normalOpenFloor/enableNormalOpenFloor/disableNormalOpenFloor
     * @param opInterval 操作间隔
     * @param floorId    楼层ID
     * @return 操作结果
     */
    default ZKResultMsg operate(String type, int opInterval, String floorId) {
        return null;
    }

    /**
     * 根据楼层id获取名称等信息
     */
    default List<IvsSelectFloorItem> getItemsByIds(List<String> floorIds) {
        return null;
    }

    /**
     * 根据查询条件获取楼层
     *
     * @param sessionId 用于过滤当前用户的设备权限
     * @param condition 查询条件
     * @param pageNo    页码
     * @param size      数量
     * @return 分页数据
     */
    default Pager getItemByAuthFilter(String sessionId, IvsSelectFloorItem condition, int pageNo, int size) {
        return null;
    }
}
