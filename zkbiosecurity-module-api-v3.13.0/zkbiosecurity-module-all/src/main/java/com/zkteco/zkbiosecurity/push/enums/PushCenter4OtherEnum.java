package com.zkteco.zkbiosecurity.push.enums;

import com.zkteco.zkbiosecurity.core.utils.ConstUtil;
import com.zkteco.zkbiosecurity.core.utils.I18nUtil;
import com.zkteco.zkbiosecurity.push.constants.PushCenter4OtherConstants;

/**
 * 推送中心枚举定义
 * 
 * 各个模块需要新增对接推送数据，只要在这边定义推送的类型详情即可
 *
 * 定义在前面的，界面显示在前
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 19:41 2021/8/31
 * @version v1.0
 */
public enum PushCenter4OtherEnum {

    // 界面根据定义的循序显示

    // 门禁
    ACC_TRANSACTION(ConstUtil.SYSTEM_MODULE_ACC, "acc_module",
        PushCenter4OtherConstants.PUSHTYPE_ACC_TRANSACTION, "common_devOpType_transaction",
        PushCenter4OtherConstants.DATATYPE_TEXT),
    ACC_TRANSACTION_IMAGE(ConstUtil.SYSTEM_MODULE_ACC, "acc_module",
        PushCenter4OtherConstants.PUSHTYPE_ACC_TRANSACTION_IMAGE, "pers_capture_catchPhoto",
        PushCenter4OtherConstants.DATATYPE_IMAGE),

    // 考勤
    ATT_TRANSACTION(ConstUtil.SYSTEM_MODULE_ATT, "att_module",
        PushCenter4OtherConstants.PUSHTYPE_ATT_TRANSACTION, "att_pushCenter_transaction",
        PushCenter4OtherConstants.DATATYPE_TEXT),
    ATT_TRANSACTION_IMAGE(ConstUtil.SYSTEM_MODULE_ATT, "att_module",
        PushCenter4OtherConstants.PUSHTYPE_ATT_TRANSACTION_IMAGE, "att_statistical_attPhoto",
        PushCenter4OtherConstants.DATATYPE_IMAGE),

    // 梯控
    ELE_TRANSACTION(ConstUtil.SYSTEM_MODULE_ELE, "ele_module",
        PushCenter4OtherConstants.PUSHTYPE_ELE_TRANSACTION, "common_devOpType_transaction",
        PushCenter4OtherConstants.DATATYPE_TEXT),
    /*ELE_TRANSACTION_IMAGE(ConstUtil.SYSTEM_MODULE_ELE, "ele_module"),
        PushCenter4OtherConstants.PUSHTYPE_ELE_TRANSACTION_IMAGE, "pers_capture_catchPhoto"),
        PushCenter4OtherConstants.DATATYPE_IMAGE),*/

    // 人证
    PID_TRANSACTION(ConstUtil.SYSTEM_MODULE_IDENTIFICATION, "pid_module",
        PushCenter4OtherConstants.PUSHTYPE_PID_TRANSACTION, "pid_transaction_persVerification",
        PushCenter4OtherConstants.DATATYPE_TEXT),
    PID_TRANSACTION_IMAGE(ConstUtil.SYSTEM_MODULE_IDENTIFICATION, "pid_module",
        PushCenter4OtherConstants.PUSHTYPE_PID_TRANSACTION_IMAGE, "pid_transaction_capturePhoto",
        PushCenter4OtherConstants.DATATYPE_IMAGE),

    // 通道
    PSG_TRANSACTION(ConstUtil.SYSTEM_MODULE_PASSAGE, "psg_module",
        PushCenter4OtherConstants.PUSHTYPE_PSG_TRANSACTION, "common_devOpType_transaction",
        PushCenter4OtherConstants.DATATYPE_TEXT),
    /*PSG_TRANSACTION_IMAGE(ConstUtil.SYSTEM_MODULE_PASSAGE, "psg_module"),
        PushCenter4OtherConstants.PUSHTYPE_PSG_TRANSACTION_IMAGE, "pers_capture_catchPhoto"),
        PushCenter4OtherConstants.DATATYPE_IMAGE),*/

    // 信息屏
    INS_TRANSACTION(ConstUtil.SYSTEM_MODULE_INS, "ins_module",
        PushCenter4OtherConstants.PUSHTYPE_INS_TRANSACTION, "ins_transaction_manager",
        PushCenter4OtherConstants.DATATYPE_TEXT),
    INS_TRANSACTION_IMAGE(ConstUtil.SYSTEM_MODULE_INS, "ins_module",
        PushCenter4OtherConstants.PUSHTYPE_INS_TRANSACTION_IMAGE, "ins_transaction_verifyPhoto",
        PushCenter4OtherConstants.DATATYPE_IMAGE),
    // 智能视频
    IVS_TRANSACTION(ConstUtil.SYSTEM_MODULE_IVS, "ivs_module",
    PushCenter4OtherConstants.PUSHTYPE_IVS_TRANSACTION, "common_devOpType_transaction",
    PushCenter4OtherConstants.DATATYPE_TEXT),
    IVS_TRANSACTION_IMAGE(ConstUtil.SYSTEM_MODULE_IVS, "ivs_module",
    PushCenter4OtherConstants.PUSHTYPE_IVS_TRANSACTION_IMAGE, "pers_capture_catchPhoto",
    PushCenter4OtherConstants.DATATYPE_IMAGE),

    // 智能场景
    ESDC_TRANSACTION(ConstUtil.LICENSE_MODULE_ESDC, "esdc_module",
            PushCenter4OtherConstants.PUSHTYPE_ESDC_TRANSACTION, "common_devOpType_transaction",
            PushCenter4OtherConstants.DATATYPE_TEXT),
    ESDC_TRANSACTION_IMAGE(ConstUtil.LICENSE_MODULE_ESDC, "esdc_module",
            PushCenter4OtherConstants.PUSHTYPE_ESDC_TRANSACTION_IMAGE, "pers_capture_catchPhoto",
            PushCenter4OtherConstants.DATATYPE_IMAGE);

    /**
     * 数据来源模块编码
     *
     * @see ConstUtil
     */
    private String module;

    /**
     * 数据来源模块名称
     *
     * @see ConstUtil
     */
    private String moduleName;

    /**
     * 推送类型（注意需要全模块唯一）
     *
     * @see com.zkteco.zkbiosecurity.push.constants.PushCenter4OtherConstants
     *
     */
    private String pushType;

    /**
     * 推送类型名称
     */
    private String pushTypeName;

    /**
     * 数据类型（text,image）
     *
     * @see com.zkteco.zkbiosecurity.push.constants.PushCenter4OtherConstants
     */
    private String dataType;

    public String getModule() {
        return module;
    }

    public String getModuleName() {
        return moduleName;
    }

    public String getPushType() {
        return pushType;
    }

    public String getPushTypeName() {
        return pushTypeName;
    }

    public String getDataType() {
        return dataType;
    }

    PushCenter4OtherEnum(String module, String moduleName, String pushType, String pushTypeName, String dataType) {
        this.module = module;
        this.moduleName = moduleName;
        this.pushType = pushType;
        this.pushTypeName = pushTypeName;
        this.dataType = dataType;
    }
}
