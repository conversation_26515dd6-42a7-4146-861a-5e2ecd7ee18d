package com.zkteco.zkbiosecurity.ivs.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.ivs.vo.Ivs4VisVisitorInfoItem;

/**
 * 视频模块获取访客人员信息接口
 *
 * <AUTHOR>
 * @date 2023/8/7 16:13
 */
public interface Ivs4VisVisitorInfoService {

    /**
     * 获取访客人员信息
     * 
     * <AUTHOR>
     * @date 2023-08-07 16:47
     * @param condition
     * @param page
     * @param size
     * @return
     */
    default Pager getVisVisitorInfoByPage(Ivs4VisVisitorInfoItem condition, int page, int size) {
        return new Pager();
    }

    /**
     * 根据人员id获取访客人员信息
     *
     * <AUTHOR>
     * @date 2023-08-07 16:47
     * @param ids
     * @return
     */
    default List<Ivs4VisVisitorInfoItem> getVisVisitorInfoByIds(String ids) {
        return null;
    }

    /**
     * 根据人员id和访客名单库类型获取访客人员信息
     * 
     * <AUTHOR>
     * @date 2025-04-10 17:28
     * @param ids
     * @param personnelListType
     * @return 
     */
    default List<Ivs4VisVisitorInfoItem> getVisVisitorInfoByIds(String ids, String personnelListType) {
        return null;
    }
    /**
     * 根据访客名单库id获取访客名单库人数
     * 
     * <AUTHOR>
     * @date 2023-08-14 14:50
     * @param personnelListId
     * @return
     */
    default String getVisitorListCountByPersonnelListId(String personnelListId) {
        return null;
    }

    /**
     * 更新访客人员图片
     *
     * <AUTHOR>
     * @date 2023-08-07 16:47
     * @param condition
     * @return
     */
    default Boolean updateVisVisitorPhoto(Ivs4VisVisitorInfoItem condition) {
        return null;
    }

    /**
     * 根据pin获取名单库访客信息
     * 
     * @param pin:
     * @return com.zkteco.zkbiosecurity.ivs.vo.Ivs4VisVisitorInfoItem
     * <AUTHOR>
     * @throws
     * @date 2024-04-28 15:33
     * @since 1.0.0
     */
    default Ivs4VisVisitorInfoItem getVisVisitorPersonnelListByPin(String pin) {
        return null;
    }
    
    /**
     * 根据pin获取访客信息
     * 
     * <AUTHOR>
     * @date 2025-05-15 11:53
     * @param pin
     * @return 
     */
    default Ivs4VisVisitorInfoItem getVisVisitorByPin(String pin) {
        return null;
    }

    /**
     * 根据人员和名单库类型获取访客人员信息
     * 
     * @param ids:
     * @param personnelListType:
     * @return java.util.List<com.zkteco.zkbiosecurity.ivs.vo.Ivs4VisVisitorInfoItem>
     * <AUTHOR>
     * @throws
     * @date 2024-12-02 10:56
     * @since 1.0.0
     */
    default List<Ivs4VisVisitorInfoItem> getVisVisitorInfoByIdsAndPersonnelListType(String ids,
        String personnelListType) {
        return null;
    }

}
