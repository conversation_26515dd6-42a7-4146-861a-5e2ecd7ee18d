package com.zkteco.zkbiosecurity.vid.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.vid.vo.HwChannelSelect4OtherItem;
import com.zkteco.zkbiosecurity.vid.vo.HwParentDevice4OtherItem;
import com.zkteco.zkbiosecurity.vid.vo.HwSearch4OtherItem;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * Hw2OtherService
 *
 * <AUTHOR>
 * @date 2021-02-03 16:45
 */
public interface Hw2OtherService {

    /**
     * 获取主设备下拉列表集合
     *
     * @param sessionId
     * @return com.zkteco.zkbiosecurity.base.bean.TreeItem
     * <AUTHOR>
     * @date 2021-02-03 17:53
     * @since 1.0.0
     */
    default List<HwParentDevice4OtherItem> getDeviceTree(String sessionId) {
        return null;
    }

    /**
     * 根据设备ID查询设备信息
     * 
     * @param channelId:
     * @return com.zkteco.zkbiosecurity.att.vo.Att4VidChannelSelectItem
     * <AUTHOR>
     * @date 2021-02-04 14:58
     * @since 1.0.0
     */
    default HwChannelSelect4OtherItem getVidChannelById(String channelId) {
        return null;
    }

    /**
     * 根据设备ID集合查询设备信息
     *
     * @param channelIds:
     * @return com.zkteco.zkbiosecurity.att.vo.Att4VidChannelSelectItem
     * <AUTHOR>
     * @date 2021-02-04 14:58
     * @since 1.0.0
     */
    default List<HwChannelSelect4OtherItem> getVidChannelByIds(Collection<String> channelIds) {
        return null;
    }

    /**
     * 双列表设备信息查询
     * 
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-02-04 14:59
     * @since 1.0.0
     */
    default Pager getVidChannelSelectItemList(HwChannelSelect4OtherItem condition, int page, int size) {
        return null;
    }

    /**
     * 根据通道id和起始时间截止时间取数据数量
     *
     * @param channelIds
     * @param startTime
     * @param endTime
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021-02-03 18:08
     * @since 1.0.0
     */
    default Integer getCountByChannelIds(String channelIds, Date startTime, Date endTime) {
        return null;
    }

    /**
     * 根据通道ids和时间还有页码和每页条数获取分页数据
     *
     * @param channelIds
     * @param startTime
     * @param endTime
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-02-03 18:12
     * @since 1.0.0
     */
    default Pager getFaceInfoPagerByDevIds(String channelIds, Date startTime, Date endTime, int page, int size) {
        return null;
    }

    /**
     * 删除设备下的人员
     *
     * @param personIds
     * @return void
     * <AUTHOR>
     * @date 2021/5/19 10:38
     * @since 1.0.0
     */
    void delPersonToAllDev(String personIds);

    /**
     * 根据名单库ID查询是否存在已下发的名单库
     * @param personnelListId:  名单库ID
     * @return
     * @thrws
     * <AUTHOR>
     * @date2020-12-09 10:37:16
     * @since 1.0.0
     */
    boolean existsByPersonnelListIds(Collection<String> personnelListId);

    /**
     * 以图搜人
     *
     * @param item
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-01-23 12:05
     * @since 1.0.0
     */
    default ZKResultMsg getPersonInfoByImage(HwSearch4OtherItem item) {
        return null;
    }
}
