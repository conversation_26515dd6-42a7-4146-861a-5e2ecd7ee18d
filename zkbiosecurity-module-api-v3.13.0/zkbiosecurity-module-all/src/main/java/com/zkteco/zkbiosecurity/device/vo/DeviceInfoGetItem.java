package com.zkteco.zkbiosecurity.device.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 获取设备的请求参数item
 *
 * <AUTHOR>
 * @date 2021/1/14 19:15
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class DeviceInfoGetItem {

    /** session ID 必传 */
    private String sessionId;
    /** 模块in */
    private String sourceModules;
    /** 区域ids*/
    private String areaIds;
    /** 唯一标识 */
    private String reserve;
    /** 类型 */
    private String deviceModelNameIn;
    /** 父设备id */
    private String edgeDeviceId;
}
