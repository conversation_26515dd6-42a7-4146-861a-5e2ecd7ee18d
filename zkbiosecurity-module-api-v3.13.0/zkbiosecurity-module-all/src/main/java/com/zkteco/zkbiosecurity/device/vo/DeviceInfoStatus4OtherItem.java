package com.zkteco.zkbiosecurity.device.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 设备状态
 *
 * <AUTHOR>
 * @date 2021/11/05 15:33
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class DeviceInfoStatus4OtherItem {

	/**
	 * 设备id
	 */
	private String id;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 是否是部件
	 */
	private Boolean isSubset = false;

	/**
	 * 部件状态
	 */
	private JSONObject subsetStatus;
}
