package com.zkteco.zkbiosecurity.space.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 类描述
 *
 * <AUTHOR>
 * @date 2024/9/20 16:09
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class SpaceReservation4AppItem implements Serializable {
    /** 空间名称 */
    private String spaceName;
    /** 区域名称 */
    private String authAreaName;
    /** 空间地址 */
    private String spaceAddress;
    /** 空间图片路径：移动端展示使用 */
    private String picturePath;
    /** 主题 */
    private String subject;
    /** app参与人姓名，格式：name1,name2 */
    private String appParticipantNames;

    /** app申请人姓名，格式：firstName lastName */
    private String appApplyPersonWholeName;
    /** 空间服务 */
    private String serviceNames;
    private String startTime;

    /** 会议结束时间 */
    private String endTime;
}
