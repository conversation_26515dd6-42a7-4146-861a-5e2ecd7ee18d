package com.zkteco.zkbiosecurity.ivs.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 视频模块获取访客人员信息vo对象
 *
 * <AUTHOR>
 * @date 2023/8/7 16:29
 */
@Setter
@Getter
@Accessors(chain = true)
public class Ivs4VisVisitorInfoItem extends BaseItem implements Serializable {

    /** 主键 */
    private String id;

    /** 访客编号 */
    private String pin;

    /** 名单库id */
    private String personnalListId;

    /** 包含访客id */
    private String idIn;

    /** 不包含访客id */
    private String notInId;

    /** 姓名 */
    private String name;

    /** 姓氏 */
    private String lastName;

    /** 性别 */
    private String gender;

    /** 图片base64 */
    private String photoBase64;

}
