package com.zkteco.zkbiosecurity.abs.service;

import java.util.List;

import com.zkteco.zkbiosecurity.abs.vo.AbsLinkageTaskSelect4OtherItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 广播联动任务
 *
 * <AUTHOR>
 * @date 2025/4/2 11:30
 * @since 1.0.0
 */
public interface AbsGetLinkageTask4OtherService {
    /**
     * 根据id获取联动任务
     * 
     * @param ids:
     * @return java.util.List<com.zkteco.zkbiosecurity.abs.vo.AbsLinkageTaskSelect4OtherItem>
     * <AUTHOR>
     * @throws
     * @date 2025-04-02 11:31
     * @since 1.0.0
     */
    default List<AbsLinkageTaskSelect4OtherItem> getLinkageTaskByIds(String ids) {
        return null;
    }

    /**
     * 获取联动任务列表
     * 
     * @param condition:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date 2025-04-02 14:07
     * @since 1.0.0
     */
    default Pager getLinkageTaskList(AbsLinkageTaskSelect4OtherItem condition, int pageNo, int pageSize) {
        return null;
    }

    /**
     * 执行联动任务
     * 
     * @param ids:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2025-04-02 14:16
     * @since 1.0.0
     */
    default ZKResultMsg executeLinkageTask(String ids) {
        return null;
    }
}
