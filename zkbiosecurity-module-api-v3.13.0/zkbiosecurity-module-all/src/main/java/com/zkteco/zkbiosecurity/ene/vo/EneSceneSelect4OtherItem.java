package com.zkteco.zkbiosecurity.ene.vo;

import com.zkteco.zkbiosecurity.base.annotation.*;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

@From(after = "ENE_SCENE t")
@OrderBy(after = "t.CREATE_TIME DESC")
@GridConfig(operate = false, idField = "id")
@Getter
@Setter
public class EneSceneSelect4OtherItem extends BaseItem {

    /** 主键 */
    @Column(name = "t.ID")
    @GridColumn(checkbox = true, width = "40", sortNo = 0)
    private String id;

    @Column(name = "t.NAME")
    @GridColumn(label = "common_name", sortNo = 1, width = "120")
    private String name;

    @Column(name = "t.REMARK")
    @GridColumn(label = "common_remark", sortNo = 2, width = "120")
    private String remark;

    private String type;

    private String filterId;

    private String selectId;

    @Column(name = "t.ID", equalTag = "in")
    private String inId;

    @Column(name = "t.ID", equalTag = "not in")
    private String notInId;


}
