package com.zkteco.zkbiosecurity.device.service;

import com.zkteco.zkbiosecurity.device.vo.DeviceInfo4OtherItem;
import com.zkteco.zkbiosecurity.device.vo.DeviceInfoStatus4OtherItem;

import java.util.List;

/**
 * 设备中心获取其他模块设备
 *
 * <AUTHOR>
 * @date 2021/1/20 12:05
 * @since 1.0.0
 */
public interface Other2DeviceCenterService {

    /**
     * 获取业务模块设备
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.device.vo.DeviceCenterInfoItem>
     * <AUTHOR>
     * @date 2021-01-14 19:18
     * @since 1.0.0
     */
    default List<DeviceInfo4OtherItem> getDeviceFromModule() {
        return null;
    }

    /**
     * 获取业务模块设备状态
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.device.vo.DeviceInfoStatus4OtherItem>
     * <AUTHOR>
     * @date  2021-11-05 15:52
     * @since 1.0.0
     */
    default List<DeviceInfoStatus4OtherItem> getDeviceStatusFromModule() {
        return null;
    }

    /**
     * 获取实现的模块名
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021-01-21 10:13
     * @since 1.0.0
     */
    default String getServiceModule() {
        return null;
    }

}
