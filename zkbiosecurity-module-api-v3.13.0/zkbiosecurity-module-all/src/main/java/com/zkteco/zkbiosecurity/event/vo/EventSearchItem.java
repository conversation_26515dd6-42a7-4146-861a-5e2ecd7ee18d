package com.zkteco.zkbiosecurity.event.vo;

import io.swagger.models.auth.In;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 搜索接口入参item
 *
 * <AUTHOR>
 * @date 2021/1/23 11:52
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class EventSearchItem {

    /** 名单库id */
    private String groupIds;
    /** 相似度 */
    private String similarityThreshold;
    /** 算法 */
    private String algorithmCode;
    /** base64 */
    private String base64;
}
