package com.zkteco.zkbiosecurity.event.service;

import com.zkteco.zkbiosecurity.event.vo.EventCenterLog4OtherItem;

/**
 * 事件推送
 *
 * <AUTHOR>
 * @date 2021/2/3 9:27
 * @since 1.0.0
 */
public interface EventRecordListenerService {

    /**
     * 推送事件
     *
     * @param item
     * @return void
     * <AUTHOR>
     * @date 2021-02-03 9:29
     * @since 1.0.0
     */
    default void pushEvent2Other(EventCenterLog4OtherItem item) {

    }
}
