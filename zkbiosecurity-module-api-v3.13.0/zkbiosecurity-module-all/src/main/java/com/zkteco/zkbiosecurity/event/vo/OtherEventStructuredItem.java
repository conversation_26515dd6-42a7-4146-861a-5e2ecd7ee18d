package com.zkteco.zkbiosecurity.event.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;

/**
 * 人体结构化item
 *
 * <AUTHOR>
 * @date 2024/4/7 10:52
 * @since 1.0.0
 */
@Setter
@Getter
@ToString
public class OtherEventStructuredItem implements Serializable {

	private String id;
	/**
	 * 事件记录id
	 */
	private String eventRecordId;

	/*       人体结构化参数          */
	/**
	 * 年龄 0:表示未知,1：表示未成年人, 2：表示成年人
	 */
	private String age;

	/**
	 * 性别 0:表示未知,1：表示女生, 2：表示男生
	 */
	private String gender;

	/**
	 * 是否背包 0:表示未知,1：未背包, 2：背包
	 */
	private String bag;

	/**
	 * 是否戴眼镜 0:表示未知,1：未戴眼镜, 2：戴眼镜
	 */
	private String glasses;

	/**
	 * 是否戴帽子 0:表示未知,1：未戴帽子, 2：戴帽子
	 */
	private String hat;

	/**
	 * 是否戴口罩 0:表示未知,1：未戴口罩, 2：戴口罩
	 */
	private String mask;

	/**
	 * 裤子颜色 0:表示未知,1：其它颜色, 2：黑色，3：蓝色，4：灰色，5：红色，6：白色
	 */
	private String lowerColor;

	/**
	 * 衣服颜色 0:表示未知,1：其它颜色, 2：黑色，3：蓝色，4：灰色，5：红色，6：白色
	 */
	private String upperColor;

	/**
	 * 目标方框坐标点位（左、上、右、下）
	 */
	private String coordinate;
}