package com.zkteco.zkbiosecurity.device.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 部件 门 读头 辅助设备都属于部件
 *
 * <AUTHOR>
 * @date 2021/1/18 16:40
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class DeviceSubsetInfo4OtherItem {

    private String id;

    /** 部件名 */
    private String subsetName;

    /** 部件编号 */
    private String subsetNo;

    /** 部件类型 acc_door acc_reader */
    private String subsetType;

    /** 所属设备id */
    private String deviceInfoId;

    /** 所属设备别名 */
    private String deviceInfoName;

    /** 部件状态 */
    private JSONObject status;

    /** 父部件id */
    private String parentSubSetId;

    /** 唯一值 */
    private String reserve;

    /** 子部件 */
    private List<DeviceSubsetInfo4OtherItem> deviceSubsetInfo4OtherItems;
}
