package com.zkteco.zkbiosecurity.linkage.center.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 联动配置输入点
 * 
 * <AUTHOR>
 * @date 2024/9/3 15:47
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class LinkageCenter4OtherLinkageInItem {

    /** 触发事件ID */
    private String triggerEventId;

    /** 触发事件编号 */
    private String triggerEventNo;

    /** 输入点id */
    private String inputPointId;

    /** 输入点名称 */
    private String inputPointName;

    /** 输入点所属设备名称 */
    private String deviceName;

}
