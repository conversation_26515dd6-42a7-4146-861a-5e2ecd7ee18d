package com.zkteco.zkbiosecurity.event.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 事件中心统计数量接口入参item
 *
 * <AUTHOR>
 * @date 2021/1/22 13:56
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class EventCenterGetCountItem {

    /** 开始时间*/
    private Date startTime;

    /** 结束时间 */
    private Date endTime;

    /** 对象名 */
    private String objName;

    /**
     *  事件对象类型，围绕智慧出入口 "人行、车行、物检"
     *  0：人 1：车 2：物 3:设备 4：部件
     *  非空
     **/
    private String objType;

    /**
     *  事件对象唯一值标记，如人员pin号，人员身份证号，车牌号，第三方业务数据唯一值
     *  pin号、车牌、身份证号
     *  对于设备及部件：需要设备sn或domainCode 下划线"_"部件code
     **/
    private String objKey;

    /**  区域id */
    private String areaId;

    /** 等级值 0:正常 1:一般 2:异常 3:紧急 支持 0,1,2 = 三种等级的数据*/
    private String eventLevelValIn;

    /**
     *  事件来源的唯一值标记，
     *  对于设备及部件：需要设备sn或domainCode 下划线"_"部件code
     **/
    private String sourceKey;

    /**  来源模块 */
    private String sourceModuleIn;

    /**  处理状态 0：未确认 1：已确认 */
    private String processState;

    /** 事件名称扩展字段，存储国际化key */
    private String eventNameKey;
}
