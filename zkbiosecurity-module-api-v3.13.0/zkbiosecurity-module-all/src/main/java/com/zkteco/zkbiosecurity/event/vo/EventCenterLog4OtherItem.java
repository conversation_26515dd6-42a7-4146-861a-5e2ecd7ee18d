package com.zkteco.zkbiosecurity.event.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


/**
 * 事件item
 *
 * <AUTHOR>
 * @date 2021/1/7 9:36
 * @since 1.0.0
 */
@Getter
@Setter
@ToString
@Accessors(chain = true)
public class EventCenterLog4OtherItem implements Serializable {

    private String id;

    /** 对象名 */
    private String objName;

    /**
     *  事件对象类型，围绕智慧出入口 "人行、车行、物检"
     *  0：人 1：车 2：物 3:设备 4：部件
     *  非空
     **/
    private String objType;

    /**
     *  事件对象唯一值标记，如人员pin号，人员身份证号，车牌号，第三方业务数据唯一值
     *  pin号、车牌、身份证号
     *  对于设备及部件：reserve
     **/
    private String objKey;

    /**  事件名称 */
    private String eventName;

    /** 事件名称扩展字段，存储国际化key */
    private String eventNameKey;

    /**  事件时间 */
    private Date eventTime;

    /**  区域id */
    private String areaId;

    /**  区域名 */
    private String areaName;

    /**  部门id */
    private String deptId;

    /**  部门名称 */
    private String deptName;
    
    /**
     *  事件来源的唯一值标记，reserve
     **/
    private String sourceKey;

    /** 来源名称*/
    private String sourceName;

    /**  来源模块 */
    private String sourceModule;

    /** 事件类型code */
    private String eventTypeCode;

    /** 抓拍图像资源地址 相对路径或绝对路径 */
    private String captureImgURL;

    /** 全景图像资源地址 相对路径或绝对路径*/
    private String panoramaImgURL;

    /**
     * 匹配图像资源地址 相对路径或绝对路径
     * */
    private String matchedImgURL;

    /** 等级值 0:正常 1:一般 2:异常 3:紧急*/
    private String eventLevelVal;

    private String processState;

    /**  处理备注 */
    private String eventRemark;

    /**  人脸id */
    private String reId;

    /** 匹配率 */
    private String faceRate;

    /** 命中类型 允许名单 禁止名单 陌生人 */
    private String targetType;

    /** 录像通道ID */
    private String videoChannelId;

    /**sop文件路径*/
    private String path;

    /** 设备名称(objType不是设备,也有设备名称) */
    private String deviceName;

}
