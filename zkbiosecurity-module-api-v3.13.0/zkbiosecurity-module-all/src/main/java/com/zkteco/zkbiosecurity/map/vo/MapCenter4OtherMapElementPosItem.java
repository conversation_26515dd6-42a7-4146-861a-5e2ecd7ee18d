package com.zkteco.zkbiosecurity.map.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/1 17:18
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class MapCenter4OtherMapElementPosItem extends BaseItem implements Serializable {

    private String id;

    /**
     * 元素ID
     */
    private String mapElementId;

    /**
     * 元素在地图上的X轴坐标
     */
    private Double positionX;

    /**
     * 元素在地图上的Y轴坐标
     */
    private Double positionY;

    /**
     * 元素在地图上的Z轴坐标
     */
    private Double positionZ;

    /** 图标 */
    private String elementIcon;
}
