package com.zkteco.zkbiosecurity.push.vo;

import java.io.Serializable;
import java.util.List;

import com.zkteco.zkbiosecurity.push.enums.PushCenter4OtherEnum;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

@Setter
@Getter
@ToString
@Accessors(chain = true)
public class PushCenter4OtherItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据对象json字符串、图片路径
     *
     */
    private String content;

    /**
     * 数据对象json字符串、图片路径
     */
    private List<String> contentList;

    /**
     * 推送的数据类型，模块等，需要推送的模块定义
     * 
     * @see PushCenter4OtherEnum
     */
    private PushCenter4OtherEnum pushCenter4OtherEnum;
}
