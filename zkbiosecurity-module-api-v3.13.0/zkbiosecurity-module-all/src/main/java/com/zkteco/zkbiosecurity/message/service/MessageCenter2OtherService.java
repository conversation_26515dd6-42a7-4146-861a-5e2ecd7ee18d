package com.zkteco.zkbiosecurity.message.service;

import java.util.ArrayList;
import java.util.List;

import com.zkteco.zkbiosecurity.message.vo.MessageCenterGetLogItem;
import com.zkteco.zkbiosecurity.message.vo.Other2MessageCenterItem;

/**
 * 获取消息中心内容
 *
 * <AUTHOR>
 * @date 2021-08-09 16:17
 * @since 1.0.0
 */
public interface MessageCenter2OtherService {

    /**
     * 获取消息
     * 
     * @param mess
     * @return
     */
    default List<Other2MessageCenterItem> getMessage(MessageCenterGetLogItem mess) {
        return new ArrayList<>(0);
    }
}
