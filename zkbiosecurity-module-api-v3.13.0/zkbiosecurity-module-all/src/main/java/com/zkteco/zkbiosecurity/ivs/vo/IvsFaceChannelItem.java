package com.zkteco.zkbiosecurity.ivs.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 人脸通道
 *
 * <AUTHOR>
 * @date 2025/1/10 15:43
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class IvsFaceChannelItem implements Serializable {

	private String id;

	private String cameraCode;

	private String channelCode;

	private String channelNo;

	private String domainCode;

	private String parentType;

	private String deviceId;

	private String name;

	private String deviceName;

	private String deviceIp;

	private String ip;

	private String type;

	private String selectId;

	private String alias;

	private String deviceType;

	private String parentDeviceId;

    private String areaId;
}
