package com.zkteco.zkbiosecurity.ai.center.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 业务模块对接人脸服务接口
 *
 * <AUTHOR>
 * @date 2021-08-30 16:20
 * @since 1.0.0
 */
public interface Other2AiCenterService {

    /**
     * 将人脸图片推送到人脸服务
     * 
     * @param personId
     * @param imgPath
     * @return
     */
    default ZKResultMsg pushFaceByPersonIdAndImg(String personId, String imgPath) {
        return ZKResultMsg.successMsg();
    }

    /**
     * 将人脸图片推送到人脸服务
     *
     * @param personId 人员id
     * @param imgPath 图片路径
     * @param effectiveTime 有效时间（天）
     * @return
     */
    default ZKResultMsg pushFaceByPersonIdAndImg(String personId, String imgPath, int effectiveTime) {
        return ZKResultMsg.successMsg();
    }

    /**
     * 通过人员id删除人脸信息
     * 
     * @param personId
     * @return
     */
    default ZKResultMsg removeFaceByPersonId(String personId) {
        return ZKResultMsg.successMsg();
    }

    /**
     * 搜索相似人员，并指定返回返回数量（1：N）
     * 
     * @param imgPath 搜索图片
     * @param size 前几个
     * @return [ "id": 1, "score": 43.412918090820312 ]
     */
    default ZKResultMsg comparePersonByImg(String imgPath, int size) {
        return ZKResultMsg.successMsg();
    }

    /**
     * 通过base64图片查找人员
     * 
     * @param base64Img
     * @param size
     * @return
     */
    default ZKResultMsg comparePersonByBase64Img(String base64Img, int size) {
        return ZKResultMsg.successMsg();
    }

    /**
     * 通过base64处理人体结构化图片
     *
     * <AUTHOR>
     * @date 2021-09-21 20:29
     * @since 1.0.0
     */
    default ZKResultMsg bodyStructuredByBase64(String base64Img){
        return ZKResultMsg.successMsg();
    }
}
