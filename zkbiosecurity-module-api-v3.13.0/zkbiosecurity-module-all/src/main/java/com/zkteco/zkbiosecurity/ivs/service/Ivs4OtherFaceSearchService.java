package com.zkteco.zkbiosecurity.ivs.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 人脸检索
 *
 * <AUTHOR>
 * @date 2024/4/7 15:16
 * @since 1.0.0
 */
public interface Ivs4OtherFaceSearchService {

	/**
	 * 获取盒子1：N结果
	 *
	 * @param imgBase64
	 * @return com.zkteco.zkbiosecurity.ivs.vo.IvsSearchPersonItem
	 * <AUTHOR>
	 * @date 2024/4/28 9:56
	 * @since 1.0.0
	 */
	default ZKResultMsg getPerson(String imgBase64) {
		return null;
	}

	/**
	 * 人员下发
	 *
	 * @param reId
	 * @param imgStr
	 * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
	 * <AUTHOR>
	 * @date 2024/4/28 9:56
	 * @since 1.0.0
	 */
	default ZKResultMsg addPerson(String reId, String imgStr) {
		return null;
	}
}
