package com.zkteco.zkbiosecurity.message.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 消息记录接收者
 *
 * <AUTHOR>
 * @date 2025/6/4 10:32
 * @since 1.0.0
 */
@Setter
@Getter
@ToString
@Accessors(chain = true)
public class Other2MessageReceiverItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 接受者id */
    private String receiverId;
    /** 接受者对象类型 person员工 manager管理者 */
    private String receiverType;
    /** 状态 已读未读 0：未读 */
    private String state;

    public Other2MessageReceiverItem() {}

    public Other2MessageReceiverItem(String receiverId, String receiverType, String state) {
        this.receiverId = receiverId;
        this.receiverType = receiverType;
        this.state = state;
    }
}
