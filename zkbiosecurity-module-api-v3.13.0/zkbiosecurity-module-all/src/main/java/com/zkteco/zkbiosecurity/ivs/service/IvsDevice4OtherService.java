package com.zkteco.zkbiosecurity.ivs.service;

import java.util.Collection;
import java.util.List;

/**
 * 智能视频设备推送其他模块接口
 *
 * <AUTHOR>
 * @date 2021-08-17 14:56
 * @since 1.0.0
 */
public interface IvsDevice4OtherService {

    /**
     * 删除设备推送
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2020-12-24 11:23
     * @since 1.0.0
     */
    default void deleteDevice(Collection<String> ids) {}

    /**
     * 校验是否有其他模块使用设备信息
     *
     * @param ids:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-05-07 15:11
     * @since 1.0.0
     */
    default void checkIvsDeviceIsUsed(List<String> ids) {}
}
