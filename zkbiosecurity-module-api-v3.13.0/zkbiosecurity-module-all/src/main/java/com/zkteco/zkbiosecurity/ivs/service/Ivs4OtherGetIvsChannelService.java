package com.zkteco.zkbiosecurity.ivs.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.ivs.vo.*;

import java.util.List;

/**
 * 智能视频模块获取通道接口
 *
 * <AUTHOR>
 * @date 2021-08-16 09:43
 * @since 1.0.0
 */
public interface Ivs4OtherGetIvsChannelService {

    /**
     * 获取通道
     *
     * @param ivsChannel4OtherItem
     * @return java.util.List<com.zkteco.zkbiosecurity.ivs.vo.IvsChannel4OtherItem>
     * <AUTHOR>
     * @date 2021-08-16 9:57
     * @since 1.0.0
     */
    default List<IvsChannel4OtherItem> getChannel(IvsChannel4OtherItem ivsChannel4OtherItem) { return null; }


    /**
     * 获取主设备
     *
     * @param item:
     * @return java.util.List<com.zkteco.zkbiosecurity.ivs.vo.Ivs4OtherEdgeDeviceItem>
     * <AUTHOR>
     * @date 2024/4/28 16:21
     * @since 1.0.0
     */
    default List<Ivs4OtherEdgeDeviceItem> getEdgeDeviceList(Ivs4OtherEdgeDeviceItem item) {
        return null;
    }

    /**
     * 根据通道id获取实况流
     *
     * @param channelId
     * @return com.zkteco.zkbiosecurity.ivs.vo.IvsRtspItem
     * <AUTHOR>
     * @date 2021-08-17 17:11
     * @since 1.0.0
     */
    default IvsRtspItem getRtsp(String channelId) { return null; }

    /**
     * 获取回放流
     *
     * @param ivsRtspPlayBackGetItem
     * @return com.zkteco.zkbiosecurity.ivs.vo.IvsRtspItem
     * <AUTHOR>
     * @date 2021-08-17 17:11
     * @since 1.0.0
     */
    default IvsRtspItem getRtspPlayBack(IvsRtspPlayBackGetItem ivsRtspPlayBackGetItem) { return null; }

    /**
     * 摄像机抓拍
     *
     * @param ivsChannel4OtherItem
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/8/23 17:10
     * @since 1.0.0
     */
    default String createSnapshot(IvsChannel4OtherItem ivsChannel4OtherItem) { return null; }


    /**
     * 摄像机抓拍--返回base64
     *
     * @param ivsChannel4OtherItem:
     * @return java.lang.String
     * <AUTHOR>
     * @date  2022/12/6 10:58
     * @since 1.0.0
     */
    default String createSnapshotBase64(IvsChannel4OtherItem ivsChannel4OtherItem) { return null; }


    /**
     * 双列表设备信息查询--人脸
     *
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2025/1/8 10:42
     * @since 1.0.0
     */
    default Pager getFaceChannelItemList(IvsFaceChannelItem condition, int page, int size) {
        return null;
    }
}
