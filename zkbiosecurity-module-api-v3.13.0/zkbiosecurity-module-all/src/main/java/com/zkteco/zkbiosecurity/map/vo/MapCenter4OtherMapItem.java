package com.zkteco.zkbiosecurity.map.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/11/1 14:49
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class MapCenter4OtherMapItem extends BaseItem implements Serializable {

    /** 地图ID */
    private String id;

    /** 地图名称 */
    private String name;

    /** 地图类型：Normal:普通地图 GIS:gis地图 Google:谷歌地图 Baidu:百度地图 */
    private String mapType;

    /** 地图路径 */
    private String mapPath;
}
