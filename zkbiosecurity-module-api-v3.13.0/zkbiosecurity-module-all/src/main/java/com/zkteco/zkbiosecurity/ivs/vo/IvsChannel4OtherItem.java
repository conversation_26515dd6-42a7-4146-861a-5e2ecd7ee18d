package com.zkteco.zkbiosecurity.ivs.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * ivs通道item
 *
 * <AUTHOR>
 * @date 2021-08-16 9:49
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class IvsChannel4OtherItem extends BaseItem implements Serializable{
   private static final long serialVersionUID = 1L;

    /** 主键 */
	private String id;

	private String idIn;

	private String inId;

	/*** 摄像机名称键盘可见字符和中文，长度限制192字节*/
	private String name;

	/*** 设备状态：l 0：离线 l 1：在线 l 2：休眠*/
	private String status;


	/*** 设备类型：l 1：IPC l 2：DVS l 3：DVR l 4：eNVR*/
	private String formType;

	/*** 摄像机类型：l 0：固定枪机l 1：有云台枪机 l 2：球机 l 3：半球-固定摄像机 l 4：筒机*/
	private String type;

	/*** 通道code*/
	private String channelCode;

	private String inChannelCode;

	/*** 前端IP点分十进制格式，例如：**************，长度限制64字节*/
	private String ip;

	/** 厂商 */
	private String vendor;

	/** 算法模式 */
	private String algorithm;

	/** 摄像机id */
	private String devId;

	/*** 设备编码 长度限制64字节*/
	private String cameraCode;

	/*** 通道号 1: 通道1，2: 通道2（双仓摄像机存在2个通道）*/
	private String channelNo;

	/*** 域编码*/
	private String domainCode;

	/*** 父设备类型*/
	private String parentType;

	/*** 位置*/
	private String location;

	/*** 操作的摄像机名称*/
	private String cameraName;

	/*** 摄像机类型*/
	private String cameraType;

	/*** 类别*/
	private String category;

	/*** 主设备型号由各设备厂家提供，长度限制32字节*/
	private String model;

	/*** 协议类型*/
	private String protocolType;

	/*** 设备所属NVR编码*/
	private String nvrCode;

	/*** 父设备编码*/
	private String parentCode;

	/*** 网络类型，0：有线，1：无线*/
	private String netType;

	/*** 创建时间*/
	private String createTime;

	/*** 通道*/
	private String channel;

	/*** 订阅状态 */
	private Boolean subscription;

	/*** 预留字段 */
	private String reserve;

	/** 父设备id */
	private String vidParentDeviceId;

	/** 父设备名 */
	private String vidParentDeviceName;

	/** 企业id */
	private String enterpriseId;

	/** 区域id */
	private String areaId;

}
