package com.zkteco.zkbiosecurity.device.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.device.vo.DeviceInfo4OtherItem;
import com.zkteco.zkbiosecurity.device.vo.DeviceInfoGetItem;
import com.zkteco.zkbiosecurity.device.vo.DeviceSubsetInfo4OtherItem;
import com.zkteco.zkbiosecurity.device.vo.DeviceSubsetInfoGetItem;

/**
 * 设备中心对外接口
 *
 * <AUTHOR>
 * @date 2021/1/14 19:11
 * @since 1.0.0
 */
public interface DeviceCenter2OtherService {

    /**
     * 获取设备
     *
     * @param item
     * @return java.util.List<com.zkteco.zkbiosecurity.device.vo.DeviceCenterInfoItem>
     * <AUTHOR>
     * @date 2021-01-14 19:17
     * @since 1.0.0
     */
    default List<DeviceInfo4OtherItem> getDeviceInfo(DeviceInfoGetItem item) {
        return null;
    }

    /**
     * 获取部件
     *
     * @param item
     * @return java.util.List<com.zkteco.zkbiosecurity.device.vo.DeviceSubsetInfo4OtherItem>
     * <AUTHOR>
     * @date 2021-01-20 17:57
     * @since 1.0.0
     */
    default List<DeviceSubsetInfo4OtherItem> getDeviceSubset4Info(DeviceSubsetInfoGetItem item) {
        return null;
    }

    /**
     * 根据id获取设备
     *
     * @param id
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-02-01 11:46
     * @since 1.0.0
     */
    default DeviceInfo4OtherItem getDeviceInfoById(String id) {
        return null;
    }

    /**
     * 设备透传操作
     *
     * @param type
     * @param opInterval
     * @param doorId
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-01-28 11:06
     * @since 1.0.0
     */
    default ZKResultMsg penetrateInfo(String type, int opInterval, String doorId) {
        return null;
    }

    /**
     * 获取设备数量
     * 
     * @return long
     * <AUTHOR>
     * @throws
     * @date 2024-08-21 16:14
     * @since 1.0.0
     */
    default long getDeviceCount() {
        return 0;
    }
}
