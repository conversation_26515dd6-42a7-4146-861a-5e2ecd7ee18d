package com.zkteco.zkbiosecurity.device.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 部件接口请求item
 *
 * <AUTHOR>
 * @date 2021/1/20 17:57
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class DeviceSubsetInfoGetItem {

    /** 设备id */
    private String deviceInfoId;
    /** 部件类型 acc_door */
    private String subsetType;
    /** 唯一标识 */
    private String reserve;
}
