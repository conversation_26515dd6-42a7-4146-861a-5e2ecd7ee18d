package com.zkteco.zkbiosecurity.device.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 设备详情
 *
 * <AUTHOR>
 * @date 2021/1/14 19:13
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class DeviceInfo4OtherItem {

    private String id;

    /**  sn号 */
    private String deviceSn;

    /**  设备名 */
    private String deviceName;

    /** 设备别名*/
    private String deviceAlias;

    /** 设备型号*/
    private String deviceModelName;

    /** 固件版本 */
    private String deviceVersion;

    /** ip */
    private String deviceIp;

    /** 端口 */
    private String devicePort;

    /** 区域id */
    private String areaId;

    /** 区域名*/
    private String areaName;

    /**  来源模块 */
    private String sourceModule;

    /**  状态(在线/离线) */
    private String status;

    /** 通道 */
    private DeviceInfoChannel4OtherItem channel;

    /**  父设备id */
    private String parentDeviceId;

    /** 唯一值 */
    private String reserve;

    /** 子设备*/
    private List<DeviceInfo4OtherItem> deviceInfo4OtherItems;

    /** 部件 */
    private List<DeviceSubsetInfo4OtherItem> deviceSubsetInfo4OtherItems;

}
