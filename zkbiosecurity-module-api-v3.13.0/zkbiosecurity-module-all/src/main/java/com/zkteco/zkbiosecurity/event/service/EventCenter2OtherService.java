package com.zkteco.zkbiosecurity.event.service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.event.vo.*;
import org.springframework.lang.Nullable;

import java.util.List;
import java.util.Map;


/**
 * 事件中心对外接口
 *
 * <AUTHOR>
 * @date 2021/1/7 9:33
 * @since 1.0.0
 */
public interface EventCenter2OtherService {

    /**
     * 获取事件
     *
     * @param item
     * @return java.util.List<com.zkteco.zkbiosecurity.event.vo.EventCenterLog4OtherItem>
     * <AUTHOR>
     * @date 2021-01-07 11:01
     * @since 1.0.0
     */
    default List<EventCenterLog4OtherItem> getEvent(EventCenterGetLogItem item) {
        return null;
    }

    /**
     * 分页查询事件
     * @param item 查询条件
     * @return 分页对象 data类型为EventCenterLog4OtherItem
     * @throws ZKBusinessException 当没有传递pageSize或者pageNumber时
     */
    @Nullable
    default Pager getEventByPage(EventCenterGetLogItem item) {
        return null;
    }

    /**
     * 根据等级获取数量
     *
     * @param item
     * @return com.alibaba.fastjson.JSONObject
     * <AUTHOR>
     * @date 2021-01-22 13:58
     * @since 1.0.0
     */
    default JSONObject getEventCountByLevel(EventCenterGetCountItem item) {
        return null;
    }

    /**
     * 获取事件等级
     *
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2021-02-03 11:51
     * @since 1.0.0
     */
    default Map<String, String> getEventLevel() {
        return null;
    }
    
    /**
    *获取事件等级名称颜色
    *@auther 31876
    *@date  16:02
    *@since 1.0.0
    */
    default Map<String, String> getEventLevelColor() {
        return null;
    }

    /**
     * 以图搜人
     *
     * @param item
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2021-01-23 11:53
     * @since 1.0.0
     */
    default ZKResultMsg getPersonInfoByImage(EventSearchItem item) {
        return null;
    }

    /**
     * 保存事件类型
     *
     * @param items
     * @return void
     * <AUTHOR>
     * @date 2021-02-20 10:55
     * @since 1.0.0
     */
    default void saveEventCenterTypeItem(List<EventCenterTypeItem> items) {

    }

    /**
     * 获取事件类型
     *
     * @param item
     * @return java.util.List<com.zkteco.zkbiosecurity.event.vo.EventCenterTypeItem>
     * <AUTHOR>
     * @date 2021-02-20 10:54
     * @since 1.0.0
     */
    default List<EventCenterTypeItem> getEventCenterTypeList(EventCenterTypeItem item) {
        return null;
    }


    /**
     * 分组查询, 根据入参的groupBy字段进行分组查询
     * @param item
     * @return
     */
    default List<EventCenterGroupLog4OtherItem> groupByCondition(EventCenterGetLogItem item) {
        return null;
    }
	/**
	 * 获取人体结构化事件列表
	 *
	 * @param item
	 * @return java.util.List<com.zkteco.zkbiosecurity.event.vo.EventFaceStruct4OtherItem>
	 * @throws
	 * <AUTHOR>
	 * @date  2021-11-03 15:55
	 * @since 1.0.0
	 */
    default List<EventFaceStruct4OtherItem> getEventStructuredList(EventFaceStruct4OtherItem item){
    	return null;
	}

	/**
	 * 获取事件id
	 *
	 * @return java.lang.String
	 * <AUTHOR>
	 * @date 2024/7/15 16:48
	 * @since 1.0.0
	 */
	default String getEventRecordId(String businessId){
		return null;
	}
    
    /**
    *根据文件id判断资源文件是否在事件中心使用
    *@auther 31876
    *@date  11:36
    *@since 1.0.0
    */
    default List<Other2EventCenterItem> findByBaseMadileId(String baseMediaFileId){return null;}

    /**
    *根据事件id获取事件
    *@auther 31876
    *@date  17:09
    *@since 1.0.0
    */
    default List<EventCenterTypeItem> getEventTypeItemById(String eventTypeId){return null;}
}
