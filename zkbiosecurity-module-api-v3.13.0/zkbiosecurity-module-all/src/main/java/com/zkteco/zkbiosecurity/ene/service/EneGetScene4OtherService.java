package com.zkteco.zkbiosecurity.ene.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.ene.vo.EneSceneSelect4OtherItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/10 9:31
 * @since 1.0.0
 */
public interface EneGetScene4OtherService {

    /**
     * 实现获取选场景数据双列表
     *
     * <AUTHOR>
     * @date 2024/9/5 12:05
     * @since 1.0.0
     */
    default Pager getSceneItems(String sessionId, EneSceneSelect4OtherItem condition, int pageNo, int pageSize) {
        return null;
    }

    default ZKResultMsg executeScene(String ids) {
        return null;
    }

    default List<EneSceneSelect4OtherItem> getSceneByIds(String ids) {
        return null;
    }
}
