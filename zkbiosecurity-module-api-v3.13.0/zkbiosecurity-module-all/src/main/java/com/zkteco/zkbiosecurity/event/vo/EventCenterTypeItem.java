package com.zkteco.zkbiosecurity.event.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 事件类型
 *
 * <AUTHOR>
 * @date 2021/2/20 10:52
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class EventCenterTypeItem {

    private String id;

    /**  类型名称 */
    private String typeName;

    /**  类型编码 */
    private String typeCode;

    /** 类型等级 */
    private String typeLevelName;

    /**  事件等级值 初始化：0：正常 1：一般 2：异常 3紧急 */
    private String typeLevelVaL;

    /**  来源模块 门禁、考勤、视频、系统等 */
    private String sourceModule;

    /** 是否为联动配置触发事件，true：是，false：否 */
    private Boolean linkage;

}
