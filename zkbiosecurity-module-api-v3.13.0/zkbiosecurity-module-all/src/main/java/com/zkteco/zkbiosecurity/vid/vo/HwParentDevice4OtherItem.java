
/**
 * File Name: VidParentDevice 主设备组
 * Created by GenerationTools on 2020-07-17 14:32:25
 * Copyright:Copyright © 2016-2019 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.vid.vo;

import com.alibaba.fastjson.JSONArray;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 对应百傲瑞达实体 VidParentDeviceItem
 * <AUTHOR>
 * @date:	2018-03-08 下午06:03
 * @version v1.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class HwParentDevice4OtherItem extends BaseItem implements Serializable{
   private static final long serialVersionUID = 1L;

    /** 主键 */
	private String id;

	private String inId;

	private String ra;

	/**  名称 */
	private String name;

	/**  端口 */
	private String port;

	/**  用户名 */
	private String userName;

	/**  备注 */
	private String remark;

	/**  地址 */
	private String ip;

	/**  密码 */
	private String passWord;

	/** 状态 */
	private String status;

	/** 类型 */
	private String type;

	/** 订阅状态 */
	private String subStatus;

	/** session信息 */
	private String session;

	/** token信息 */
	private String token;

	/** 安全协议 */
	private String securityProtocol;

	/** 域编码 */
	private String domainCode;

	/** 域编码 */
	private String inDomainCode;

	private String isHttps;

	private String jsServerPort;

	private String jsSessionId;

	private String deviceType;

	private String addUser;

	private String addTime;

	private String isCertificate;

	/** NVR800旁位类型 */
	private String modelType;

	/** 设备所在区域id */
	private String areaId;

	/** 设备所在区域名称 */
	private String areaName;

	private String inAreaId;

    /*** 来源，0：本地，1：云端*/
    private String isFrom;

    /*** 来源类型，1：主设备(云/本地)，2：SDC*/
    private String isFromType;

    /*** 编码，1：本地SDC，2：云SDC*/
    private String code;

	/** 公司ID */
	private String companyId;

	private String companyIds;

	/**
	 * 算法列表
	 */
	private JSONArray algorithms;


    public HwParentDevice4OtherItem() {
        super();
    }

    public HwParentDevice4OtherItem(String id) {
        super();
        this.id = id;
	}

	public HwParentDevice4OtherItem(String code, String name, String domainCode, String status, String type, String isFrom, String isFromType) {
		super();
		this.code = code;
		this.name = name;
		this.domainCode = domainCode;
		this.status = status;
		this.type = type;
		this.isFrom = isFrom;
		this.isFromType = isFromType;
	}

	public HwParentDevice4OtherItem(String isFrom, String isFromType) {
		this.isFrom = isFrom;
		this.isFromType = isFromType;
	}
}
