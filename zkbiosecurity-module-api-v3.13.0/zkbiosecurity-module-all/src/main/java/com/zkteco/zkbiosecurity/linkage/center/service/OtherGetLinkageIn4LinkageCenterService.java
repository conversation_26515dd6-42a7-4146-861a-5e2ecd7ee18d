package com.zkteco.zkbiosecurity.linkage.center.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.linkage.center.vo.LinkageCenter4OtherLinkageInItem;

/**
 * <AUTHOR>
 * @date 2024/9/3 15:35
 * @since 1.0.0
 */
public interface OtherGetLinkageIn4LinkageCenterService {

    /**
     * 获取模块缩写
     * 
     * <AUTHOR>
     * @date 2024-09-03 15:51
     * @since 1.0.0
     */
    String getModule();

    /**
     * 获取输入点对象
     * 
     * <AUTHOR>
     * @date 2024-09-03 15:52
     * @since 1.0.0
     */
    default Pager getInputPointItems(LinkageCenter4OtherLinkageInItem condition, int pageNo, int pageSize) {
        return null;
    }
}
