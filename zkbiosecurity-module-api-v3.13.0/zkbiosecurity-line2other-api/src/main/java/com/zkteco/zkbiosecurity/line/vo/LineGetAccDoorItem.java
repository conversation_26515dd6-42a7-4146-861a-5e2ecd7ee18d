package com.zkteco.zkbiosecurity.line.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2019-10-10 19:19
 */
@Getter
@Setter
@Accessors(chain = true)
public class LineGetAccDoorItem implements Serializable {
    private String doorId;
    private String doorName;
    private Short doorNo;
    private String devSn;

    public LineGetAccDoorItem() {
        super();
    }
}
