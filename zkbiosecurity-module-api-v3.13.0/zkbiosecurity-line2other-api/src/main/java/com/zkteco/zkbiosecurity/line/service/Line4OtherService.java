package com.zkteco.zkbiosecurity.line.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.line.vo.OthersGetLineContactSelectItem;

/**
 * LINE和其他模块交接类
 *
 * <AUTHOR>
 * @date 2022/2/15 15:11
 */
public interface Line4OtherService {

    /**
     * 校验Line许可
     * 
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2022-02-15 15:14
     * @since 1.0.0
     */
    default boolean checkLineLicense() {
        return false;
    }

    /**
     * 将联动消息推送到lineAPP
     * 
     * @param lineContactId:
     * @param lineContent:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-02-17 14:11
     * @since 1.0.0
     */
    default void sendInfoToLine(String lineContactId, String lineContent) {
        return;
    }

    /**
     * 获取line通讯录联系人列表信息
     * 
     * @param item:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date 2022-03-28 15:46
     * @since 1.0.0
     */
    default Pager getLineContactSelectItemByPage(OthersGetLineContactSelectItem item, int pageNo, int pageSize) {
        return new Pager();
    }

    /**
     * 根据联系人id获取联系人名称
     * 
     * @param lineContactId:
     * @return java.util.List<com.zkteco.zkbiosecurity.line.vo.OthersGetLineContactSelectItem>
     * <AUTHOR>
     * @throws
     * @date 2022-03-28 15:45
     * @since 1.0.0
     */
    default List<OthersGetLineContactSelectItem> getLineContactByContactIds(String lineContactId) {
        return null;
    }
}
