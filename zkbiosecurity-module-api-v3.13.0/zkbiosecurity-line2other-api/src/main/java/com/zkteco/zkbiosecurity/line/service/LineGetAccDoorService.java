package com.zkteco.zkbiosecurity.line.service;

import java.util.Collection;
import java.util.List;

import com.zkteco.zkbiosecurity.line.vo.LineGetAccDoorItem;

/**
 * <AUTHOR>
 * @date 2019-10-10 13:59
 */
public interface LineGetAccDoorService {
    /**
     * 获取门禁模块下的所有门
     * 
     * <AUTHOR>
     * @since 2019-10-10 19:26
     * @Param []
     * @return
     */
    List<LineGetAccDoorItem> getAccDoorItems();

    /**
     * 根据门id获取门禁的门数据
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-10-21 14:35
     * @param eventPointIdList
     * @return java.util.List<com.zkteco.zkbiosecurity.line.vo.LineGetAccDoorItem>
     */
    List<LineGetAccDoorItem> getAccDoorItemByDoorIdIn(Collection<String> eventPointIdList);
}
