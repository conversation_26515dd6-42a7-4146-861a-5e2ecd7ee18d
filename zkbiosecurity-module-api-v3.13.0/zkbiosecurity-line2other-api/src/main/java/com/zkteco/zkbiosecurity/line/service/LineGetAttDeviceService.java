package com.zkteco.zkbiosecurity.line.service;

import com.zkteco.zkbiosecurity.line.vo.LineGetAttDeviceItem;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-11 16:28
 */
public interface LineGetAttDeviceService {
    /**
    * 获取考勤所有设备
    * <AUTHOR>
    * @since 2019-10-11 16:29
    * @Param []
    * @return
     */
    List<LineGetAttDeviceItem> getAttDeviceItems();

    /**
     * 根据设备id获取设备数据
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-10-24 18:07
     * @param devIdList
     * @return java.util.List<com.zkteco.zkbiosecurity.line.vo.LineGetAttDeviceItem>
     */
    List<LineGetAttDeviceItem> getAttDeviceItemByDevIdIn(Collection<String> devIdList);
}
