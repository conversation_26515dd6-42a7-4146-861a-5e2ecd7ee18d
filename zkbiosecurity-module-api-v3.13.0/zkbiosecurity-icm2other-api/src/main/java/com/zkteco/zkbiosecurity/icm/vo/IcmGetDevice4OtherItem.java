package com.zkteco.zkbiosecurity.icm.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * 水电控设备选择Vo
 *
 * <AUTHOR>
 * @date 2021-04-12 17:08
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class IcmGetDevice4OtherItem extends BaseItem implements Serializable {

    /**
     * 设备Id
     */
    private String id;

    /**
     * 设备名称
     */
    private String name;

    /**
     * 设备表号
     */
    private String code;

    /**
     * 网关ID
     */
    private String gatewayId;

    /**
     * 网关名称
     */
    private String gatewayName;

    /**
     * 网关sn
     */
    private String gatewaySn;

    /**
     * 设备类型  1：电表  2：水表
     */
    private String deviceType;

    // 查询的过滤条件

    private String inId;

    private String notInId;

    private String type;

    private String selectId;

}
