package com.zkteco.zkbiosecurity.icm.service;

/**
 * 水电控设备对接其他模块接口
 *
 * <AUTHOR>
 * @date 2021/6/2 15:46
 * @since 1.0.0
 */
public interface IcmDevice4OtherService {

    /**
     * 判断水、电表是否在其他模块使用 若被使用，实现模块抛出业务异常，throw new ZKBusinessException(msg,deviceName);
     * 
     * <AUTHOR>
     * @date 2021-06-02 15:48
     * @param deviceIds
     * @since 1.0.0
     * @return void
     */
    default void checkIcmDeviceIsUsed(String deviceIds, String type){}
}
