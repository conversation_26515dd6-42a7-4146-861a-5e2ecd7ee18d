package com.zkteco.zkbiosecurity.icm.service;


import com.zkteco.zkbiosecurity.icm.vo.IcmEWTransactionInfo;

/**
 * 其他模块获取水电控模块水电用量记录接口
 *
 * <AUTHOR>
 * @date 2021/5/27 15:18
 * @since 1.0.0
 */
public interface IcmTransaction2OtherService {

    /**
     * 获取水表、电表的最新抄表记录
     *
     * @param wDeviceId 水表设备id
     * @param eDeviceId 电表设备id
     * @return com.zkteco.zkbiosecurity.icm.vo.IcmEWTransactionInfo
     * <AUTHOR>
     * @Date   15:35 2021/6/1
     * @since  1.0.0
    **/
    IcmEWTransactionInfo getTransaction2Other(String wDeviceId, String eDeviceId);
}
