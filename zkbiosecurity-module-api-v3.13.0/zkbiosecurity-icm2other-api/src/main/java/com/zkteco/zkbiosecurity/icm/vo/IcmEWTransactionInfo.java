package com.zkteco.zkbiosecurity.icm.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 水电用量信息item
 *
 * <AUTHOR>
 * @date 2021/6/2 16:54
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class IcmEWTransactionInfo {
    /**
     * 日用水量
     */
    private String wDayResult;

    /**
     * 月用水量
     */
    private String wMonResult;

    /**
     * 水表上次抄表数
     */
    private String wLastQuantity;

    /**
     * 水表上次抄表时间
     */
    private Date wLastReadTime;

    /**
     * 日用电量
     */
    private String eDayResult;

    /**
     * 月用电量
     */
    private String eMonResult;

    /**
     * 电表上次抄表数
     */
    private String eLastQuantity;

    /**
     * 电表上次抄表时间
     */
    private Date eLastReadTime;

    public IcmEWTransactionInfo() {
    }


    public IcmEWTransactionInfo(String wDayResult, String wMonResult, String wLastQuantity, String eDayResult, String eMonResult, String eLastQuantity) {
        this.wDayResult = wDayResult;
        this.wMonResult = wMonResult;
        this.wLastQuantity = wLastQuantity;
        this.eDayResult = eDayResult;
        this.eMonResult = eMonResult;
        this.eLastQuantity = eLastQuantity;
    }
}
