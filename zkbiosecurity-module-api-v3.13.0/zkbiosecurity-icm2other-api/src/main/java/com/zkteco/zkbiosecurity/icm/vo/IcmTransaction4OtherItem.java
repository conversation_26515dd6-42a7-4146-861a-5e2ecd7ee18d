package com.zkteco.zkbiosecurity.icm.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 抄表通知Item
 *
 * <AUTHOR>
 * @date 2021/4/14 10:34
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class IcmTransaction4OtherItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    /** 设备id */
    private String deviceId;

    /** 设备类型 */
    private String deviceType;

    /**  水表名称 */
    private String name;

    /**  水表表号 */
    private String code;

    /**  网关ID */
    private String gatewayId;

    /**  网关名称 */
    private String gatewayName;

    /**  网关sn */
    private String gatewaySn;

    /**  当前读数 */
    private Double quantity;

    /**  抄表时间 */
    private Date readTime;

    /**抄表日期*/
    private Date readDate;

    /**查询月份*/
    private Date searchMonth;

}
