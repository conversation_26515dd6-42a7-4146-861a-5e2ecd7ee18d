package com.zkteco.zkbiosecurity.icm.service;

import com.zkteco.zkbiosecurity.icm.vo.IcmTransaction4OtherItem;

import java.util.List;

/**
 *  抄表通知类
 *
 * <AUTHOR>
 * @date 2021/4/14 10:18
 * @since 1.0.0
 */
public interface IcmTransaction4OtherService {

    /**
     * 抄表通知其他模块
     *
     * <AUTHOR>
     * @date  2021/4/14 10:32
     * @since 1.0.0
     */
    default void syncTransaction2Other(List<IcmTransaction4OtherItem> items){

    }
}
