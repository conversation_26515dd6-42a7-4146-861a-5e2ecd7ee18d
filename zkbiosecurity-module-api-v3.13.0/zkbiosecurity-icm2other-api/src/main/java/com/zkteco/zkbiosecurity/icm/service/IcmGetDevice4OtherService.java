package com.zkteco.zkbiosecurity.icm.service;

import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.icm.vo.IcmGetDevice4OtherItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

import java.util.List;

/**
 * 其他模块获取水电控设备接口
 *
 * <AUTHOR>
 * @date 2021-04-12 15:54
 * @since 1.0.0
 */
public interface IcmGetDevice4OtherService {

    /**
     * 根据id列表批量查询Item
     *
     * <AUTHOR>
     * @date 2021-04-13 17:14
     * @param deviceIds 设备ids
     * @param type 设备类型  1：电表  2：水表
     * @since 1.0.0
     * @return java.util.List<com.zkteco.zkbiosecurity.icm.vo.IcmGetDevice4OtherItem>
     */
    List<IcmGetDevice4OtherItem> getItemsByIds(List<String> deviceIds, String type);

    /**
     * 获取所属网关下拉列表
     *
     * <AUTHOR>
     * @date 2021-04-13 15:48
     * @param type 网关类型  1：电表网关  2：水表网关
     * @since 1.0.0
     * @return java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     */
    List<SelectItem> getGateWayList(String type);

    /**
     * 双选页面获取可以选择的水、电表
     *
     * <AUTHOR>
     * @date 2021-04-13 17:14
     * @param sessionId
     * @param condition
     * @param pageNo
     * @param size
     * @since 1.0.0
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager getOnlineDeviceByPage(String sessionId, IcmGetDevice4OtherItem condition, int pageNo, int size);

}
