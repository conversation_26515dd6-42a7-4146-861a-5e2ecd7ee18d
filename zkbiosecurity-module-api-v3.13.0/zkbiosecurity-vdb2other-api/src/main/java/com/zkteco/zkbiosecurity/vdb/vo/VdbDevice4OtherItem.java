package com.zkteco.zkbiosecurity.vdb.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 设备信息
 *
 * <AUTHOR>
 * @date 2024/3/4 10:08
 */
@Getter
@Setter
@Accessors(chain = true)
public class VdbDevice4OtherItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /* 设备ip地址 */
    private String ipAddress;

    /* 设备厂商 */
    private String manufacturer;

    /* 设备名称 */
    private String name;

    /** 设备类型 0:单元门口机、1:围墙机、2：小门口机(Linux才有)、3：室内机、 4:PBX */
    private Short deviceType;

    /** 系统区域 */
    private String authAreaId;

    /** 设备sn */
    private String deviceSn;

    private String enableUnit;

}
