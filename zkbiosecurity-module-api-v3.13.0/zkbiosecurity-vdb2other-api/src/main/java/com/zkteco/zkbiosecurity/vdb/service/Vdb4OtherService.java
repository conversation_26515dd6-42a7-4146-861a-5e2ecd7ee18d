package com.zkteco.zkbiosecurity.vdb.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.vdb.vo.OthersGetVdbExtensionItem;
import com.zkteco.zkbiosecurity.vdb.vo.VdbIvr4OtherItem;

/**
 * 可视对讲与其他模块交互接口
 *
 * <AUTHOR>
 * @date 2024/4/16 8:48
 */
public interface Vdb4OtherService {
    /**
     * 获取ivr
     *
     * @return java.util.List<com.zkteco.zkbiosecurity.vdb.vo.VdbIvr4OtherItem>
     * <AUTHOR>
     * @throws
     * @date 2024-04-16 9:06
     * @since 1.0.0
     */
    default List<VdbIvr4OtherItem> getIvrItems() {
        return null;
    }

    /**
     * 获取可视对讲分机绑定的人事人员、系统用户信息
     *
     * @param item:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date 2024-04-16 10:29
     * @since 1.0.0
     */
    default Pager getExtensionSelectItemByPage(OthersGetVdbExtensionItem item, int pageNo, int pageSize) {
        return null;
    }

    /**
     * 获取联动配置的分机信息
     *
     * @param extensionIds:
     * @return java.util.List<com.zkteco.zkbiosecurity.vdb.vo.OthersGetVdbExtensionItem>
     * <AUTHOR>
     * @throws
     * @date 2024-04-16 14:31
     * @since 1.0.0
     */
    default List<OthersGetVdbExtensionItem> getVdbExtensionByIds(String extensionIds) {
        return null;
    }

    /**
     * 实现ivr外呼
     *
     * @param ivrId:
     * @param bindObjectId:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-04-17 11:04
     * @since 1.0.0
     */
    default void dialIvr(String ivrId, String bindObjectId) {}
}
