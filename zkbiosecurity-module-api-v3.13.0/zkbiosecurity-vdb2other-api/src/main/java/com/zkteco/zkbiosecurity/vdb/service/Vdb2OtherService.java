package com.zkteco.zkbiosecurity.vdb.service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/26 13:43
 */
public interface Vdb2OtherService {
    /**
     * 查看可视对讲设备来源是否存在
     *
     * @param devSn:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-03-26 17:28
     * @since 1.0.0
     */
    default void checkVdbDeviceIsUsedByDevSns(List<String> devSn) {}

    /**
     * 删除被其他模块占用的ivr数据信息
     *
     * @param ids:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-04-18 9:18
     * @since 1.0.0
     */
    default void deleteVdbIvrByIds(String ids) {}

    /**
     * 根据id删除被其他模块占用的分机信息
     *
     * @param ids:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-04-19 14:09
     * @since 1.0.0
     */
    default void deleteVdbExtensionByIds(String ids) {}
}
