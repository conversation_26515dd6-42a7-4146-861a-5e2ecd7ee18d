package com.zkteco.zkbiosecurity.vdb.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2024/4/16 10:26
 */
@Getter
@Setter
@Accessors(chain = true)
public class OthersGetVdbExtensionItem {
    private String id;

    /** 分机号 */
    private String extensionNumber;

    private String extensionName;

    /** 绑定类型 */
    private Short bindType;
    /** 绑定对象 */
    private String bindObject;

    private String selectId;

    private String type;
    private String userId;
}
