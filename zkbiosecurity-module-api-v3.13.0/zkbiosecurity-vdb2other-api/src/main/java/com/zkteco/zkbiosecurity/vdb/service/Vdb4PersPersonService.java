package com.zkteco.zkbiosecurity.vdb.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.vdb.vo.Vdb4PersPersonItem;
import com.zkteco.zkbiosecurity.vdb.vo.VdbBuilding2otherItem;
import com.zkteco.zkbiosecurity.vdb.vo.VdbUnit2otherItem;

public interface Vdb4PersPersonService {

    /**
     * 根据人员pin获取
     *
     * @param persPersonPinList:
     * @return java.util.List<com.zkteco.zkbiosecurity.vdb.vo.Vdb4PersPersonItem>
     * <AUTHOR>
     * @throws
     * @date 2023-11-15 9:00
     * @since 1.0.0
     */
    List<Vdb4PersPersonItem> findByPersonPinIn(List<String> persPersonPinList);

    /**
     * 根据人员id获取
     *
     * @param persPersonIdList:
     * @return java.util.List<com.zkteco.zkbiosecurity.vdb.vo.Vdb4PersPersonItem>
     * <AUTHOR>
     * @throws
     * @date 2023-11-15 10:18
     * @since 1.0.0
     */
    List<Vdb4PersPersonItem> findByPersonIdIn(List<String> persPersonIdList);

    /**
     * 根据楼栋名称获取已经存在的楼栋信息；
     *
     * @param importBuildingNames:
     * @return java.util.List<com.zkteco.zkbiosecurity.vdb.vo.VdbBuilding2otherItem>
     * <AUTHOR>
     * @throws
     * @date 2023-11-15 11:39
     * @since 1.0.0
     */
    List<VdbBuilding2otherItem> getVdbBuildingByNames(Collection<String> importBuildingNames);

    /**
     * 根据单元名称查找存在的单元信息
     *
     * @param importUnitNames:
     * @return java.util.List<com.zkteco.zkbiosecurity.vdb.vo.VdbUnit2otherItem>
     * <AUTHOR>
     * @throws
     * @date 2023-11-15 11:48
     * @since 1.0.0
     */
    List<VdbUnit2otherItem> getVdbUnitByNames(Collection<String> importUnitNames);

    /**
     * 导入人员住宅信息
     *
     * @param vdb4PersPersonItemMap :
     * @param existsPersPinAndVdbInfoMap
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2023-11-16 13:48
     * @since 1.0.0
     */
    void importPersonData(Map<String, Vdb4PersPersonItem> vdb4PersPersonItemMap,
        Map<String, Vdb4PersPersonItem> existsPersPinAndVdbInfoMap);
}
