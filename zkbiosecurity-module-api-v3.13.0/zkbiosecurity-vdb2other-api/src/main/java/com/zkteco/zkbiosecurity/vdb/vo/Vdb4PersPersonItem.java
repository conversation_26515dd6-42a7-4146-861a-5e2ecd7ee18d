package com.zkteco.zkbiosecurity.vdb.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class Vdb4PersPersonItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    /** 人员ID */
    private String personId;

    /** 人员PIN */
    private String personPin;

    private String personName;

    private String personLastName;

    /** 人员手机号 */
    private String personMobilePhone;

    private String personCardNo;

    /** 楼栋id */
    private String buildingId;

    private String buildingName;

    /** 单元id */
    private String unitId;

    private String unitName;

    private String roomNo;
}
