package com.zkteco.zkbiosecurity.vdb.service;

import java.util.List;

import com.zkteco.zkbiosecurity.vdb.vo.VdbDevice4OtherItem;

/**
 * 可视对讲设备与其他模块交互接口
 *
 * <AUTHOR>
 * @date 2024/3/4 10:04
 */
public interface VdbDevice4OtherService {

    /**
     * 添加可视对讲设备-门口机
     *
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-03-04 10:07
     * @since 1.0.0
     */
    default void addVdbDevice(List<VdbDevice4OtherItem> vdbDevice4OtherItems) {}
}
