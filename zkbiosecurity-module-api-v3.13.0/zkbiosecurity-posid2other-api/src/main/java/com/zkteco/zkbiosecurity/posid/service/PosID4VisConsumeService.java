package com.zkteco.zkbiosecurity.posid.service;


import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.posid.vo.PosID4VisConsumeItem;

import java.util.List;

/**
 * PosIDVisConsumeService
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/12/30 17:27
 **/
public interface PosID4VisConsumeService {

    /**
     * 保存访客人员消费
     *
     * @param posID4VisConsumeItem
     * @return
     */
    ZKResultMsg saveVisConsume(List<PosID4VisConsumeItem> posID4VisConsumeItem);

    /**
     * 取消访客人员消费
     *
     * @param posID4VisConsumeItem
     * @return
     */
    ZKResultMsg cancelVisConsume(PosID4VisConsumeItem posID4VisConsumeItem);

    /**
     * 访客人员消费详情
     *
     * @param posID4VisConsumeItem
     * @return
     */
    ZKResultMsg visConsumeDetail(PosID4VisConsumeItem posID4VisConsumeItem);
}
