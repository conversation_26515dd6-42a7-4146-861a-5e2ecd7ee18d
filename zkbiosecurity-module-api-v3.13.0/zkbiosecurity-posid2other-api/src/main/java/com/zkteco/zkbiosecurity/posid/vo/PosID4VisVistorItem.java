package com.zkteco.zkbiosecurity.posid.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class PosID4VisVistorItem implements Serializable{
	
	private static final long serialVersionUID = 1L;

	/** 主键 */
	private String id;

	/** 访客编号 */
	private String visEmpPin;
	
	/** 访客姓名 */
	private String visEmpName;
	
	/** 证件类型 */
	private String visEmpCertType;

	/** 证件编号 */
	private String visEmpCertNumber;

	/** 手机号码 */
	private String visEmpPhone;
	
	/** 公司 */
	private String visCompany;
	
}
