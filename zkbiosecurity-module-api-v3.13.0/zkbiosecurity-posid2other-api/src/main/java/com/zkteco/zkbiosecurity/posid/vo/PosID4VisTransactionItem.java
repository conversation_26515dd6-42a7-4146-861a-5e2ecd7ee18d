package com.zkteco.zkbiosecurity.posid.vo;

import lombok.Data;

@Data
public class PosID4VisTransactionItem {

	/** 主键 */
	private String id;

	/** 访客编号 */
	private String visEmpPin;
	
	/** 访客姓名 */
	private String visEmpName;
	
	/** 访客姓氏 */
	private String visEmpLastName;
	
	/** 被访人编号 */
	private String visitedEmpPin;
	
	/** 被访人姓名 */
	private String visitedEmpName;
	
	/** 被访人姓氏 */
	private String visitedEmpLastName;
	
	/** 来访状态 */
	private Short visitState;
		
	/** 访客ID */
	private String visEmpId;

	/** 被访人ID */
	private String visitedEmpId;

}