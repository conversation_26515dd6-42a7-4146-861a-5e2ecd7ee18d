package com.zkteco.zkbiosecurity.posid.vo;

import lombok.Data;

@Data
public class PosID4VisBioTemplateItem {

	private String id;

	private String visitorId;
	
	private String visitorPin;


	/**  */
	private String template;

	/**  */
	private Short templateNo;
	
	 /**
     * 生物特征模板对应索引
     */
    private Short templateNoIndex;

    /**
     * 是否胁迫
     */
    private Boolean duress;
    
    private Short validType;
    
    /**
     * 生物特征类型0：通用的1：指纹2：面部3：声纹4：虹膜5：视网膜6：掌纹7：指静脉 8：掌静脉
     */
    private Short bioType;
    
    private String version;
}
