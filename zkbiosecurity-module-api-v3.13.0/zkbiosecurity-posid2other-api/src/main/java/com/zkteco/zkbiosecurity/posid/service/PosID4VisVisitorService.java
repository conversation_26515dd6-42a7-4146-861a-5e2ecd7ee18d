package com.zkteco.zkbiosecurity.posid.service;

import java.util.List;

import com.zkteco.zkbiosecurity.posid.vo.PosID4VisBioTemplateItem;
import com.zkteco.zkbiosecurity.posid.vo.PosID4VisTransactionItem;
import com.zkteco.zkbiosecurity.posid.vo.PosID4VisVistorItem;

/**
 * 主要查询访客列表
 */
public interface PosID4VisVisitorService {

	List<PosID4VisVistorItem> getVisitorList(String pin);
	
	PosID4VisTransactionItem findTransactionByVisEmpPin(String visEmpPin);
	
	List<PosID4VisBioTemplateItem> getVisBioTemplateItemByPins(List<String> pins);

	List<PosID4VisBioTemplateItem> getVisBioTemplateItemById(String personId);
}
