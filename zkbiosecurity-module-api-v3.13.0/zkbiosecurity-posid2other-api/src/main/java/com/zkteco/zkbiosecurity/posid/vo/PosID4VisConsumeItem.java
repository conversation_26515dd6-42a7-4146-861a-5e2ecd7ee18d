package com.zkteco.zkbiosecurity.posid.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * 访客人员登记消费管理
 *
 * <AUTHOR>
 * @className PosID4VisConsumeItem
 */
@Getter
@Setter
public class PosID4VisConsumeItem implements Serializable {

    /** id */
    private String id;

    /** 访客Id */
    private String visEmpId;

    /** 访客编号 */
    private String visEmpPin;

    /** 访客姓名 */
    private String visEmpName;

    /** 就餐人数 */
    private Integer dinersNum;

    /** 就餐餐别id */
    private String mealId;

    /** 餐别名称 */
    private String mealName;

    /** 餐厅id */
    private String hallId;

    /** 餐厅名称 */
    private String hallName;

    /** 预定时间 */
    private Date orderDate;

}