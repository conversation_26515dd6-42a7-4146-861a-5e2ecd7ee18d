package com.zkteco.zkbiosecurity.att.service;

import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.AttSdc4AttPersonItem;

/**
 * sdc操作人员接口
 *
 * <AUTHOR>
 * @date 2020/11/19 10:44
 * @since 1.0.0
 */
public interface AttSdc4AttPersonService {

    /**
     * 添加人员
     *
     * @param vid4AttPersonItem
     * @return void
     * <AUTHOR>
     * @date 2020-11-19 11:35
     * @since 1.0.0
     */
    default void addPerson(AttSdc4AttPersonItem vid4AttPersonItem) {};

    /**
     * 删除人员
     *
     * @param areaId
     * @param personPin
     * @return void
     * <AUTHOR>
     * @date 2020-11-19 11:39
     * @since 1.0.0
     */
    default void deletePerson(String areaId, String personPin) {};

    /**
     * 批量下发人员
     * 
     * @param personItemList:
     * @return void
     * <AUTHOR>
     * @date 2021-01-21 10:56
     * @since 1.0.0
     */
    default void batchAddPerson(List<AttSdc4AttPersonItem> personItemList) {}

    /**
     * 批量删除人员
     * 
     * @param personItemList:
     * @return void
     * <AUTHOR>
     * @date 2021-01-21 10:57
     * @since 1.0.0
     */
    default void batchDeletePerson(List<AttSdc4AttPersonItem> personItemList) {}
}
