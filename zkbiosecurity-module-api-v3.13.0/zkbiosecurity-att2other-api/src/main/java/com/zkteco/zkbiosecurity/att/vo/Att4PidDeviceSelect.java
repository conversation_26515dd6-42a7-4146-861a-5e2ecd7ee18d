package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 18:01 2018/11/23
 */
@Setter
@Getter
@Accessors(chain = true)
public class Att4PidDeviceSelect extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 设备名称
     */
    private String name;
    /**
     * 设备序列号
     */
    private String sn;

    /**  ip地址 */
    private String ipAddress;

    /**
     * 判断是左列表noSelect、还是右列表select
     */
    private String type;

    /**
     * 区域名称
     */
    private String authAreaId;

    /**
     * 当前选中的ids
     */
    private String selectId;

}
