package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-10-22 15:34
 */

@Getter
@Setter
@Accessors(chain = true)
public class Att4LineTransactionItem {
    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 英文（lastName）
     */
    private String personLastName;

    /**
     * 区域Id
     */
    private String areaId;

    /**
     * 区域编号
     */
    private String areaNo;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 设备Id
     */
    private String deviceId;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 门编号
     */
    private Short doorNo;

    /**
     * 考勤日期时间
     */
    private Date attDatetime;

    /** 设备名称 */
    private String devName;
}
