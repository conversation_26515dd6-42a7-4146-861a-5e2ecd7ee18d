package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.Att4PidDeviceSelect;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 14:45 2018/11/26
 */
public interface AttGetPidDeviceService {

    /**
     * 根据设备id获取 设备双列表对象
     * @param deviceId
     * @return
     */
    Att4PidDeviceSelect getPidDeviceById(String deviceId);

    /**
     * 根据设备id集合获取 设备 双列表集合对象
     * @param deviceIds
     * @return
     */
    List<Att4PidDeviceSelect> getPidDeviceItemsByIds(Collection<String> deviceIds);
}
