package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 通道考勤记录
 *
 * <AUTHOR>
 * @date 2021/8/25 16:48
 */
@Getter
@Setter
@Accessors(chain = true)
public class Att4PsgTransaction {
    private static final long serialVersionUID = 1L;

    /**
     * 设备Id
     */
    private String devId;

    /** 设备序列号 */
    private String devSn;

    /** 考勤时间 */
    private Date eventTime;

    /** 核验方式 */
    private String verifyModeNo;

    /** 人员编号 */
    private String pin;

    /**
     * 闸编号
     */
    private String gateNo;

    /**
     * 事件点id
     */
    private String eventPointId;

    /**
     * 照片路径
     */
    private String transactionPhoto;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 事件编号 (用来验证通行记录类型)
     */
    private Short eventNo;
}
