package com.zkteco.zkbiosecurity.att.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.Date;

/**
 * 打卡数据
 *
 * <AUTHOR>
 * @date 2024/6/13 16:34
 * @since 1.0.0
 */
public interface Att4OtherSignDataService {

	/**
	 * 获取打卡数据--已签到/未签到
	 *
	 * @param depIds
	 * @param startTime
	 * @param endTime
	 * @return com.alibaba.fastjson.JSONObject
	 * <AUTHOR>
	 * @date 2024/6/13 16:21
	 * @since 1.0.0
	 */
	default JSONObject getAttSignDataLst(String depIds, Date startTime, Date endTime) {
		return null;
	}

	/**
	 * 获取今日打卡记录
	 *
	 * @param count:
	 * @return com.alibaba.fastjson.JSONObject
	 * <AUTHOR>
	 * @date  2024/6/17 10:18
	 * @since 1.0.0
	 */
	default JSONArray getTodaySignTransaction(Integer count, String depIds, Date startTime, Date endTime) {
		return null;
	}
}
