package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Psg设备信息
 *
 * <AUTHOR>
 * @date 2021/7/12 11:22
 */
@Setter
@Getter
@Accessors(chain = true)
public class Att4PsgDeviceSelectItem extends BaseItem {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;

    /**
     * 所属设备Id
     */
    private String deviceId;

    /**
     * 所属设备序列号
     */
    private String deviceSn;

    /**
     * 所属设备名称
     */
    private String deviceAlias;

    /**
     * 闸编号
     */
    private Short gateNo;

    /**
     * 闸名称
     */
    private String name;
    /**
     * 判断是左列表noSelect、还是右列表select
     */
    private String type;

    /**
     * 区域ID
     */
    private String authAreaId;

    /**
     * 当前选中的id
     */
    private String selectId;

    /**
     * 区域过滤后的Ids
     */
    private String idIn;

}
