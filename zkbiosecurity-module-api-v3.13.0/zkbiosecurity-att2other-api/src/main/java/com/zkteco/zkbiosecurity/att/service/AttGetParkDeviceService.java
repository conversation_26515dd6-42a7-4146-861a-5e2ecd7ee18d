package com.zkteco.zkbiosecurity.att.service;

import java.util.Collection;
import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.Att4ParkDeviceSelect;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 13:35 2018/11/26
 */
public interface AttGetParkDeviceService {

    /**
     * 根据设备id获取对应的停车点信息
     * 
     * @param deviceId
     *            考勤保存字段为设备ID，实际上为停车场通道ID
     * @return
     */
    Att4ParkDeviceSelect getParkDeviceById(String deviceId);

    /**
     * 根据设备id获取对应的停车点信息
     * 
     * @param deviceIds
     *            考勤保存字段为设备ID，实际上为停车场通道ID
     * @return
     */
    List<Att4ParkDeviceSelect> getParkDeviceItemsByIds(Collection<String> deviceIds);
}
