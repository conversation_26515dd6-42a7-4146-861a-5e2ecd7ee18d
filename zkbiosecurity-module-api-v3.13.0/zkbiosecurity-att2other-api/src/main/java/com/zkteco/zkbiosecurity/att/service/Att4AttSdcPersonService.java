package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttSdc4AttPersonItem;

import java.util.List;

/**
 * sdc获取人员接口
 *
 * <AUTHOR>
 * @date 2020/11/19 11:42
 * @since 1.0.0
 */
public interface Att4AttSdcPersonService {

	/**
	 * 获取考勤人员
	 *
	 * @param areaId
	 * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.AttGetAttPersonItem>
	 * <AUTHOR>
	 * @date 2020-11-19 10:48
	 * @since 1.0.0
	 */
	default List<AttSdc4AttPersonItem> getAttPersonByArea(String areaId){
		return null;
	};
}
