package com.zkteco.zkbiosecurity.att.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class Att4PersPersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 是否考勤 */
    private Boolean isAttendance;

    /** 人员设备权限 */
    private Short perDevAuth;

    /** 人员Pin */
    private String personPin;

    /** 人员Name */
    private String personName;
    /** 人员LastName */
    private String personLastName;

    /** 人员id */
    private String personId;

    /** 部门id */
    private String deptId;
    /** 部门code */
    private String deptCode;
    /** 部门name */
    private String deptName;
    /** 入职时间 */
    private Date hireDate;

    /** 考勤区域ids */
    private String areaIds;

    /** 上传人员的设备的sn号 */
    private String devSn;
    /** 更新考勤设置 */
    private Boolean updateAttSet;

    /**
     * 验证方式
     */
    private Short verifyMode;

    /**
     * 异常标志位
     */
    private Short exceptionFlag;

}
