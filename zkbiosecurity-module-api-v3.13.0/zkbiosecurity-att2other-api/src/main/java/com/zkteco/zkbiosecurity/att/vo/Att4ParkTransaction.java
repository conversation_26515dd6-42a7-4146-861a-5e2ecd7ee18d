package com.zkteco.zkbiosecurity.att.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 10:17 2018/11/26
 */
@Getter
@Setter
@Accessors(chain = true)
public class Att4ParkTransaction implements Serializable {

    /**
     * 设备Id
     */
    private String deviceId;
    /** 设备序列号 */
    private String deviceSn;

    /** 考勤时间 */
    private Date attDatetime;

    /** 核验方式 */
    private String verifyMode;

    /** 人员编号 */
    private String personPin;

    /** 卡号 */
    private String cardNo;

    /** 考勤照片 */
    private String transactionPhoto;
    /**
     * 创建时间
     */
    private Date createTime;
}
