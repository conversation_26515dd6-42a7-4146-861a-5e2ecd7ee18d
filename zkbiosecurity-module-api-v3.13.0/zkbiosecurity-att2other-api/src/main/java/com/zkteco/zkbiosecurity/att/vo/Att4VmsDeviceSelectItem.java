package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * VMS设备信息
 *
 * <AUTHOR>
 * @date 2019/12/26
 */
@Setter
@Getter
@Accessors(chain = true)
public class Att4VmsDeviceSelectItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 设备Id
     */
    private String deviceId;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 通道编号
     */
    private Short channelNo;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 判断是左列表noSelect、还是右列表select
     */
    private String type;

    /**
     * 区域ID
     */
    private String authAreaId;

    /**
     * 当前选中的ids
     */
    private String selectId;

}
