package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.AttDevice4OtherItem;

/**
 * 考勤设备对接其他模块接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/3/12 9:17
 * @since 1.0.0
 */
public interface AttDevice4OtherService {

    /**
     * 判断考勤设备是否在其他模块使用 若被使用，实现模块抛出业务异常，throw new ZKBusinessException(level,msg);
     *
     * @param devIds:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/3/12 9:20
     * @since 1.0.0
     */
    default void checkAttDeviceIsUsedByDevIds(String devIds) {}

    /**
     * 通知使用考勤设备的模块修改设备信息
     *
     * @param attDevice4OtherItem:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/3/12 9:21
     * @since 1.0.0
     */
    default void editAttDeviceInfo(AttDevice4OtherItem attDevice4OtherItem) {}
}
