package com.zkteco.zkbiosecurity.att.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 其他模块当考勤点推送记录对象
 *
 * <AUTHOR>
 * @date 2019/12/26
 */
@Getter
@Setter
@Accessors(chain = true)
public class Att4OtherTransactionItem {

    /**
     * 模块 ConstUtil.SYSTEM_MODULE_*
     */
    private String moduleName;

    /**
     * 人员编号
     */
    private String personPin;

    /**
     * 姓名
     */
    private String personName;

    /**
     * 英文（lastName）
     */
    private String personLastName;

    /**
     * 区域Id
     */
    private String areaId;

    /**
     * 设备Id
     */
    private String deviceId;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 门编号 (门禁当考勤点用)
     */
    private Short doorNo;

    /**
     * 通道编号 (VMS当考勤点用)
     */
    private Short channelNo;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 考勤日期时间 yyyy-MM-dd HH:mm:ss
     */
    private Date attDatetime;

    /**
     * 考勤日期 yyyy-MM-dd
     */
    private String attDate;

    /**
     * 考勤时间 HH:mm:ss
     */
    private String attTime;

    /**
     * 考勤状态
     */
    private String attState;

    /**
     * 验证方式
     */
    private String attVerify;

    /**
     * 照片路径
     */
    private String transactionPhoto;

    /**
     * 创建时间
     */
    private Date createTime;
}
