
/**
 * File Name: InsDevice 信息屏设备
 * Created by GenerationTools on 2018-07-03 20:12:57
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 对应百傲瑞达实体 InsDevice
 * <AUTHOR>
 * @date:	2018-03-08 下午06:03
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Att4InsDeviceSelect extends BaseItem implements Serializable{

    /** 主键 */
	private String id;

	/**  设备名称 */
	private String devName;

	/**  设备序列号 */
	private String sn;

	/**  ip地址 */
	private String ipAddress;


	/** 设备状态 */
	private Boolean status;

	/**  是否登记机 */
	private Boolean isRegDevice;

	/**  时区 */
	private String timeZone;

	/** 区域id */
	private String authAreaId;

	/**
	 * 判断是左列表noSelect、还是右列表select
	 */
	private String type;

	/**
	 * 当前选中的ids
	 */
	private String selectId;
}