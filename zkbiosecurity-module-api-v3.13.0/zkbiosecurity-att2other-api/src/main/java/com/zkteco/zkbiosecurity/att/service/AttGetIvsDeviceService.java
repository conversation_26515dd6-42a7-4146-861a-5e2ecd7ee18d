package com.zkteco.zkbiosecurity.att.service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.Att4IvsChannelSelectItem;
import com.zkteco.zkbiosecurity.att.vo.Att4IvsParentDeviceItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * ivs智能视屏当考勤接口
 *
 * <AUTHOR>
 * @date 2021-02-03 16:45
 */
public interface AttGetIvsDeviceService {

    /**
     * 获取主设备下拉列表集合
     *
     * @param sessionId
     * @return com.zkteco.zkbiosecurity.base.bean.TreeItem
     * <AUTHOR>
     * @date 2021-02-03 17:53
     * @since 1.0.0
     */
    default List<Att4IvsParentDeviceItem> getDeviceTree(String sessionId) {
        return null;
    }

    /**
     * 根据设备ID查询设备信息
     *
     * @param channelId:
     * @return com.zkteco.zkbiosecurity.att.vo.Att4IvsChannelSelectItem
     * <AUTHOR>
     * @date 2021-02-04 14:58
     * @since 1.0.0
     */
    default Att4IvsChannelSelectItem getIvsChannelById(String channelId) {
        return null;
    }

    /**
     * 根据设备ID集合查询设备信息
     *
     * @param channelIds:
     * @return com.zkteco.zkbiosecurity.att.vo.Att4IvsChannelSelectItem
     * <AUTHOR>
     * @date 2021-02-04 14:58
     * @since 1.0.0
     */
    default List<Att4IvsChannelSelectItem> getIvsChannelByIds(Collection<String> channelIds) {
        return null;
    }

    /**
     * 双列表设备信息查询
     *
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-02-04 14:59
     * @since 1.0.0
     */
    default Pager getIvsChannelSelectItemList(Att4IvsChannelSelectItem condition, int page, int size) {
        return null;
    }

    /**
     * 双列表设备信息查询--人脸
     *
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2025/1/8 10:42
     * @since 1.0.0
     */
    default Pager getIvsFaceChannelSelectItemList(Att4IvsChannelSelectItem condition, int page, int size) {
        return null;
    }

    /**
     * 根据通道id和起始时间截止时间取数据数量（根据事件时间查询）
     *
     * @param channelIds
     * @param startTime 事件开始时间
     * @param endTime 事件结束时间
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021-02-03 18:08
     * @since 1.0.0
     */
    default Integer getCountByChannelIds(String channelIds, Date startTime, Date endTime) {
        return null;
    }

    /**
     * 根据通道ids和时间还有页码和每页条数获取分页数据（根据事件时间查询、分页查询需要排序）
     *
     * @param channelIds
     * @param startTime 事件开始时间
     * @param endTime 事件结束时间
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-02-03 18:12
     * @since 1.0.0
     */
    default Pager getFaceInfoPagerByDevIds(String channelIds, Date startTime, Date endTime, int page, int size) {
        return null;
    }

    /**
     * 根据通道id和起始时间截止时间取数据数量（根据创建时间查询）
     *
     * @param channelIds
     * @param startCreateTime 创建开始时间
     * @param endCreateTime 创建结束时间
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021-02-03 18:08
     * @since 1.0.0
     */
    default Integer pullCountByChannelIds(String channelIds, Date startCreateTime, Date endCreateTime) {
        return null;
    }

    /**
     * 根据通道ids和时间还有页码和每页条数获取分页数据（根据创建时间查询、分页查询需要排序）
     *
     * @param channelIds
     * @param startCreateTime 创建开始时间
     * @param endCreateTime 创建结束时间
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-02-03 18:12
     * @since 1.0.0
     */
    default Pager pullFaceInfoPagerByDevIds(String channelIds, Date startCreateTime, Date endCreateTime, int page, int size) {
        return null;
    }
}