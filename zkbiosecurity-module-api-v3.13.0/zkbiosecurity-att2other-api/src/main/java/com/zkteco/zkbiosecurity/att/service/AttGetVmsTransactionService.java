package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * 考勤获取Vms记录当做考勤记录 (导入记录功能调用)
 *
 * <AUTHOR>
 * @date 2019/12/25
 */
public interface AttGetVmsTransactionService {

    /**
     * 获取对应时段段的考勤记录,其中data对象是 Att4OtherTransactionItem
     *
     * @param deviceSn
     *            设备SN
     * @param channelNo
     *            通道NO
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @param page
     * @param size
     * @return
     */
    Pager getVmsTransactionList(String deviceSn, Short channelNo, Date startDatetime, Date endDatetime, int page,
        int size);

    /**
     * 获取设备名称还有结束时间的总条数
     *
     * @param deviceSn
     *            设备SN
     * @param channelNo
     *            通道NO
     * @param startDatetime
     * @param endDatetime
     * @return
     */
    int getVmsTransactionCount(String deviceSn, Short channelNo, Date startDatetime, Date endDatetime);

    /**
     * 根据通道集合，获取指定时间范围内的VMS事件记录
     *
     * @param channelIds
     * @param startDatetime
     * @param endDatetime
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/8/12 16:02
     */
    default Pager getVmsTransactionList(String channelIds, Date startDatetime, Date endDatetime, int page, int size) {
        return null;
    }

    /**
     * 根据通道集合，获取指定时间范围内的VMS事件记录数量
     *
     * @param channelIds
     * @param startDatetime
     * @param endDatetime
     * @return int
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/8/12 16:04
     */
    default int getVmsTransactionCount(String channelIds, Date startDatetime, Date endDatetime) {
        return 0;
    }

    /**
     * 定时拉取-获取阶段时间内新增加的记录条数
     *
     * @param deviceIds:
     * @param startCreateTime:
     *            记录创建时间开始
     * @param endCreateTime:
     *            记录创建时间结束
     * @return int
     * <AUTHOR>
     * @date 2021-06-10 17:49
     * @since 1.0.0
     */
    default Integer pullVmsTransactionCount(String deviceIds, Date startCreateTime, Date endCreateTime) {
        return 0;
    }

    /**
     * 定时拉取-获取分页记录数据
     *
     * @param deviceIds:
     * @param startCreateTime:
     *            记录创建时间开始
     * @param endCreateTime:
     *            记录创建时间结束
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-06-10 17:50
     * @since 1.0.0
     */
    default Pager pullVmsTransactionList(String deviceIds, Date startCreateTime, Date endCreateTime, int page,
        int size) {
        return null;
    }
}
