package com.zkteco.zkbiosecurity.att.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 全部记录
 * 
 * @author: verber
 * @date: 2018-04-28 14:45:58
 */
@Getter
@Setter
@Accessors(chain = true)
public class Att4AccTransaction implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 设备Id
     */
    private String devId;

    /** 设备序列号 */
    private String devSn;

    /** 考勤时间 */
    private Date eventTime;

    /** 核验方式 */
    private String verifyModeNo;

    /** 人员编号 */
    private String pin;

    /**
     * 门编号
     */
    private String doorNo;

    /**
     * 事件点id
     */
    private String eventPointId;

    /**
     * 照片路径
     */
    private String transactionPhoto;

    /**
     * 创建时间
     */
    private Date createTime;

}
