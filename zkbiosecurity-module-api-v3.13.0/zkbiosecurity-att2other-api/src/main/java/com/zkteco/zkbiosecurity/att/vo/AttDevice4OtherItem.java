package com.zkteco.zkbiosecurity.att.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤设备对接其他模块VO
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/3/12 9:15
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AttDevice4OtherItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 设备ID */
    private String id;
    /** 设备SN */
    private String sn;
    /** 区域ID */
    private String authAreaId;
    /** 设备名称 */
    private String alias;
}
