package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 11:31 2018/11/26
 */
public interface AttGetPidTransactionService {

    /**
     * 手动同步-获取对应时段段的考勤记录,其中data对象是 Att4PidTransaction
     * 
     * @param deviceIds
     *            设备ID集合
     * @param startDatetime
     *            事件开始时间
     * @param endDatetime
     *            事件结束时间
     * @param page
     * @param size
     * @return
     */
    Pager getPidTransactionList(String deviceIds, Date startDatetime, Date endDatetime, int page, int size);

    /**
     * 手动同步-获取设备名称还有结束时间的总条数
     * 
     * @param deviceIds
     * @param startDatetime
     *            事件开始时间
     * @param endDatetime
     *            事件结束时间
     * @return
     */
    int getPidTransactionCount(String deviceIds, Date startDatetime, Date endDatetime);

    /**
     * 定时拉取-获取阶段时间内新增加的记录条数
     *
     * @param deviceIds:
     * @param startCreateTime:
     *            记录创建时间开始
     * @param endCreateTime:
     *            记录创建时间结束
     * @return int
     * <AUTHOR>
     * @date 2021-06-10 17:49
     * @since 1.0.0
     */
    default Integer pullPidTransactionCount(String deviceIds, Date startCreateTime, Date endCreateTime) {
        return 0;
    }

    /**
     * 定时拉取-获取分页记录数据
     *
     * @param deviceIds:
     * @param startCreateTime:
     *            记录创建时间开始
     * @param endCreateTime:
     *            记录创建时间结束
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-06-10 17:50
     * @since 1.0.0
     */
    default Pager pullPidTransactionList(String deviceIds, Date startCreateTime, Date endCreateTime, int page,
        int size) {
        return null;
    }

}
