package com.zkteco.zkbiosecurity.att.service;


import com.zkteco.zkbiosecurity.att.vo.Att4EsdcChannelSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * esdc智能盒子当考勤接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2023/8/5 15:31
 * @since 1.0.0
 */
public interface AttGetEsdcDataService {

    /**
     * 根据条件获取通道列表（需要支持所有字段的过滤查询）
     * 
     * @param condition: 
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/5 15:36
     * @since 1.0.0
     */
    default Pager getChannelItemList(Att4EsdcChannelSelectItem condition, int pageNo, int pageSize) {
        return new Pager();
    }


    /**
     * 根据通道id集合获取通道信息集合
     *
     * @param channelIds:
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.Att4IvsChannelSelectItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/7 15:46
     * @since 1.0.0
     */
    default List<Att4EsdcChannelSelectItem> getChannelItemListByChannelIds(Collection<String> channelIds) {
        return null;
    }

    /**
     * 根据通道id获取通道信息
     *
     * @param channelId:
     * @return com.zkteco.zkbiosecurity.att.vo.Att4EsdcChannelSelectItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/7 16:34
     * @since 1.0.0
     */
    default Att4EsdcChannelSelectItem getChannelItemByChannelId(String channelId) {
        return null;
    }

    /**
     * 根据事件时间查询数量（事件时间）
     *
     * @param channelIds:
     * @param startTime:
     * @param endTime:
     * @return java.lang.Integer
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/8 10:44
     * @since 1.0.0
     */
    default Integer getCountByChannelIds(String channelIds, Date startTime, Date endTime) {
        return 0;
    }


    /**
    * 根据事件时间查询（事件时间）
    *
    * @param channelIds: 通道ids集合
    * @param startTime: 事件开始时间
    * @param endTime: 时间结束时间
    * @param page:
    * @param size:
    * @return com.zkteco.zkbiosecurity.base.bean.Pager
    * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
    * @date 2023/8/8 10:44
    * @since 1.0.0
    */
    default Pager getTransactionByChannelIds(String channelIds, Date startTime, Date endTime, int page, int size) {
        return new Pager();
    }

    /**
     * 根据创建时间查询数量（创建时间）
     *
     * @param channelIds:
     * @param startCreateTime:
     * @param endCreateTime:
     * @return java.lang.Integer
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/8 10:44
     * @since 1.0.0
     */
    default Integer getCountByCreateTimeAndChannelIds(String channelIds, Date startCreateTime, Date endCreateTime) {
        return 0;
    }

    /**
     * 根据创建时间查询分页数据（创建时间）
     * 
     * @param channelIds: 
     * @param startCreateTime:
     * @param endCreateTime: 
     * @param page: 
     * @param size: 
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2023/8/8 10:48
     * @since 1.0.0
     */
    default Pager getTransactionByChannelIdsAndCreateTime(String channelIds, Date startCreateTime, Date endCreateTime, int page, int size) {
        return new Pager();
    }
}