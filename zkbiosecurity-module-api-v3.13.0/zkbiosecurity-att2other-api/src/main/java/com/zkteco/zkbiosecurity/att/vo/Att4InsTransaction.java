package com.zkteco.zkbiosecurity.att.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
public class Att4InsTransaction implements Serializable {

    /**
     * 设备Id
     */
    private String deviceId;
    /** 设备序列号 */
    private String deviceSn;

    /** 考勤时间 */
    private Date attDatetime;

    /** 核验方式 */
    private String verifyMode;

    /** 人员编号 */
    private String personPin;

    /** 卡号 */
    private String cardNo;

    /** 考勤照片 */
    private String transactionPhoto;

    /**
     * 创建时间
     */
    private Date createTime;
}
