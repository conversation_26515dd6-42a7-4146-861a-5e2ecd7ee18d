/**
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:35 Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 设备
 *
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:35
 */
@Getter
@Setter
@Accessors(chain = true)
public class Att4ParkDeviceSelect extends BaseItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */

    private String id;

    /**
     * 设备名称
     */
    private String name;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 通道ID
     */
    private String channelId;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 通道状态
     */
    private Short channelState;

    /**
     * 进出口区域ID
     */
    private String entranceAreaId;

    /**
     * 判断是左列表noSelect、还是右列表select
     */
    private String type;

    /**
     * 当前选中的ids
     */
    private String selectId;

}
