package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.Att4LineTransactionItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-10-22 15:35
 */
public interface Att4LineTransactionService {
    /**
     * 推送考勤事件记录到Line模块
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2019-10-23 17:36
     * @param att4LineTransactionItems
     * @param devName
     * @return void
     */
    void pushTransactionsToLine(List<Att4LineTransactionItem> att4LineTransactionItems, String devName);
}
