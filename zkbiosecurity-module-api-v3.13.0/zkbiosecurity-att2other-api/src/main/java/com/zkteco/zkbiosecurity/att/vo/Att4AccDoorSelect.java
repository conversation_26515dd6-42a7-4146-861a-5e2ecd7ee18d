package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 *
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 10:22 2018/11/26
 */
@Getter
@Setter
@Accessors(chain = true)
public class Att4AccDoorSelect extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;

    private String doorName;

    private Short doorNo;

    private String deviceAlias;

    private String deviceSn;

    private String deviceId;

    private String type;

    private String selectId;

    private String areaId;

    private String inId;

    private String notInId;
}
