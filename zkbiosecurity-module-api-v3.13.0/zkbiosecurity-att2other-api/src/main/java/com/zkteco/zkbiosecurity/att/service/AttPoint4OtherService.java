package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.core.utils.ConstUtil;

/**
 * 考勤考勤点对接其他模块接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/3/12 9:17
 * @since 1.0.0
 */
public interface AttPoint4OtherService {

    /**
     * 判断是否被作为考勤点使用（查缓存判断）
     *
     * @param module: 模块编码
     * @see ConstUtil
     *
     * @param deviceId: 停车场的通道ID、视频的通道ID、通道的闸ID、信息屏设备ID、门禁门ID、人证设备ID、信息屏设备ID
     *
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/10 14:09
     * @since 1.0.0
     */
    default boolean isUsedByDeviceId(String module, String deviceId) {
        return false;
    }

    /**
     * 判断是否被作为考勤点使用
     *
     * @param deviceSn: 门禁设备SN、人证设备SN、VMS视频设备SN
     * @return boolean
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2021/9/8 14:27
     * @since 1.0.0
     */
    default boolean isUsedByDeviceSn(String deviceSn) {
        return false;
    }

}
