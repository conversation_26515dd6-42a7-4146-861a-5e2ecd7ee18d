package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.Att4InsDeviceSelect;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 11:52 2018/11/26
 */
public interface AttGetInsDeviceService {

    /**
     * 根据信息屏id获取，信息屏双列表对象
     * @param deviceId 信息屏id
     * @return
     */
    Att4InsDeviceSelect getInsDeviceById(String deviceId);

    /**
     * 根据信息屏集合id获取，信息屏双列表集合对象
     * @param devicdIds
     * @return
     */
    List<Att4InsDeviceSelect> getInsDeviceItemsByIds(Collection<String> devicdIds);

}
