package com.zkteco.zkbiosecurity.att.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * esdc智能盒子通道
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2023/8/5 15:43
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class Att4EsdcChannelSelectItem extends BaseItem implements Serializable {

    /*** 通道id*/
    private String id;

    /*** 通道名称*/
    private String name;

    /*** 通道编号*/
    private Integer videoNo;

    /** IP地址 */
    private String ipAddress;

    /** 区域ID */
    private String authAreaId;

    /** 过滤ids */
    private String idNotIn;
}
