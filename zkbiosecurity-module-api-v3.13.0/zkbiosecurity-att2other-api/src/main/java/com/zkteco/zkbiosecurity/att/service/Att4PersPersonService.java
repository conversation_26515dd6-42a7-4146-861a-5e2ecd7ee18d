package com.zkteco.zkbiosecurity.att.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.att.vo.Att4PersPersonItem;
import com.zkteco.zkbiosecurity.att.vo.AttPers4PersLeaveItem;

public interface Att4PersPersonService {

    /**
     * 提供人事调用考勤人员编辑接口，即考勤人员属性编辑
     * 
     * @author: hongyi.zeng
     * @date: 2018年4月25日 10:07:48
     * @param attPerson
     */
    Boolean editAttPerson(Att4PersPersonItem attPerson);

    /**
     * 删除检查
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/4 13:36
     * @param personIds
     * @return void
     */
    Boolean checkDelPerson(String personIds);

    /**
     * 删除人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/4 11:58
     * @param personIds
     * @return java.lang.Boolean
     */
    void delAttPerson(String personIds);

    /**
     * 离职人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/4 11:58
     * @param personIds
     * @return java.lang.Boolean
     */
    Boolean leaveAttPerson(String personIds);

    /**
     * 根据人员id集合获取group对应的id集合
     * 
     * @param personIds
     * @return
     */
    Map<String, AttPers4PersLeaveItem> getAttPersonGroupId(String personIds);

    /**
     * 人事部门调整，考勤AttPerson 同步修改。
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/7/27 11:55
     * @param personIds
     * @param deptId
     * @return void
     */
    void batchDeptChange(List<String> personIds, String deptId);

    /**
     * 人事同步更新梯控的数据。
     */
    void batchPerson(List<Att4PersPersonItem> attPersons);

    /**
     * 通过部门ID获取人员集合
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinjie.you</a>
     * @date 2018/8/10 9:58
     * @param deptIds
     *            部门ids
     * @return java.util.List<com.zkteco.zkbiosecurity.att.vo.Att4PersPersonItem>
     */
    List<Att4PersPersonItem> getPersPersonByDeptIds(List<String> deptIds);

    /**
     * @Description 获取考勤扩展参数
     * <AUTHOR>
     * @Date 2018/12/20 15:23
     * @Param personId
     * @Return java.util.Map<java.lang.String,java.lang.String>
     */
    Map<String, String> getAttPersonExtParam(String personId);

    /**
     ** <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
     * @Description 消费发卡/补卡 重新下发人员信息
     * @date 2020/1/21
     **/
    void reIssuePersonInfo(String personId);

    /**
     * 判断是否单考勤系统
     * 
     * @author: <a href="mailto:<EMAIL>">amaze.wu</a>
     * @date: 2020/5/20 15:54
     * @return: java.lang.Boolean
     **/
    default Boolean singleAttSystem() {
        return false;
    }
}
