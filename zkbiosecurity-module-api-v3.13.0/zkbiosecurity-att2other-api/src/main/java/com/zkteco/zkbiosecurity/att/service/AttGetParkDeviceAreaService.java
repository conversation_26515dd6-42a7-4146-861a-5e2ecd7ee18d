package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.Att4ParkEntranceArea;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 11:54 2018/11/26
 */
public interface AttGetParkDeviceAreaService {

    /**
     * 根据区域id获取停车区域
     * @param areaIdList
     * @return
     */
    List<Att4ParkEntranceArea> getParkEntranceAreaItemsByIds(Collection<String> areaIdList);

    /**
     * 根据区域id获取停车区域
     * @param areaId
     * @return
     */
    Att4ParkEntranceArea getParkEntranceAreaById(String areaId);

    /**
     * 获取停车点所有区域。
     * @return
     */
    List<Att4ParkEntranceArea> getAllParkEntranceArea();
}
