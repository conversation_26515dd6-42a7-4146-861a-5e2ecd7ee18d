package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 11:33 2018/11/26
 */
public interface AttGetParkTransactionService {

    /**
     * 获取停车场（车场入口）当考勤记录，其中data对象是 Att4ParkTransaction
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/11/26 10:32
     * @param channelIds
     *            考勤保存字段为设备ID，实际上为停车场通道Ids
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager getParkInTransactionList(String channelIds, Date startDatetime, Date endDatetime, int page, int size);

    /**
     * 获取停车场（车场入口）当考勤记录 总条数
     * 
     * @param channelIds
     *            考勤保存字段为设备ID，实际上为停车场通道Ids
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @return
     */
    int getParkInTransactionCount(String channelIds, Date startDatetime, Date endDatetime);

    /**
     * 获取停车场（车场出口）当考勤记录，其中data对象是 Att4ParkTransaction
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/11/26 10:32
     * @param channelIds
     *            考勤保存字段为设备ID，实际上为停车场通道Ids
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager getParkOutTransactionList(String channelIds, Date startDatetime, Date endDatetime, int page, int size);

    /**
     * 获取停车场（车场出口）当考勤记录 总条数
     * 
     * @param channelIds
     *            考勤保存字段为设备ID，实际上为停车场通道Ids
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @return
     */
    int getParkOutTransactionCount(String channelIds, Date startDatetime, Date endDatetime);

    /**
     * 定时拉取-获取阶段时间内新增加的记录条数
     *
     * @param deviceIds:
     * @param startCreateTime:
     *            记录创建时间开始
     * @param endCreateTime:
     *            记录创建时间结束
     * @return int
     * <AUTHOR>
     * @date 2021-06-10 17:49
     * @since 1.0.0
     */
    default Integer pullParkInTransactionCount(String deviceIds, Date startCreateTime, Date endCreateTime) {
        return 0;
    }

    /**
     * 定时拉取-获取分页记录数据
     *
     * @param deviceIds:
     * @param startCreateTime:
     *            记录创建时间开始
     * @param endCreateTime:
     *            记录创建时间结束
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-06-10 17:50
     * @since 1.0.0
     */
    default Pager pullParkInTransactionList(String deviceIds, Date startCreateTime, Date endCreateTime, int page,
        int size) {
        return null;
    }

    /**
     * 定时拉取-获取阶段时间内新增加的记录条数
     *
     * @param deviceIds:
     * @param startCreateTime:
     *            记录创建时间开始
     * @param endCreateTime:
     *            记录创建时间结束
     * @return int
     * <AUTHOR>
     * @date 2021-06-10 17:49
     * @since 1.0.0
     */
    default Integer pullParkOutTransactionCount(String deviceIds, Date startCreateTime, Date endCreateTime) {
        return 0;
    }

    /**
     * 定时拉取-获取分页记录数据
     *
     * @param deviceIds:
     * @param startCreateTime:
     *            记录创建时间开始
     * @param endCreateTime:
     *            记录创建时间结束
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-06-10 17:50
     * @since 1.0.0
     */
    default Pager pullParkOutTransactionList(String deviceIds, Date startCreateTime, Date endCreateTime, int page,
        int size) {
        return null;
    }
}
