package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.Att4OtherTransactionItem;

/**
 * 其他模块到考勤模块当记录
 *
 * <AUTHOR>
 * @date 2019/12/26
 */
public interface Att4OtherTransactionService {

	/**
	 * 第三方推送实时事件记录到考勤当考勤记录
	 *
	 * @param transactionItem
	 */
	void pushTransaction(Att4OtherTransactionItem transactionItem);

	/**
	 * 第三方推送考勤记录-(非考勤点判断)
	 *
	 * @param transactionItem
	 * @return void
	 * <AUTHOR>
	 * @date 2020-11-19 10:53
	 * @since 1.0.0
	 */
	default void pushAttTransaction(Att4OtherTransactionItem transactionItem) {
	}
}
