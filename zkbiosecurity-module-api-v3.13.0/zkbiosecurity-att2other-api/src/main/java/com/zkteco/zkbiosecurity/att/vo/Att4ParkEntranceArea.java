/**
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:35
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.att.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 进出口区域
 *
 * @author: GenerationTools
 * @date: 2018-02-08 下午08:35
 */
@Getter
@Setter
@Accessors(chain = true)
public class Att4ParkEntranceArea extends BaseItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 进出口区域名称
     */
    private String name;

    /**
     * 车场区域名称
     */
    private String areaName;

    /**
     * 车场区域ID
     */
    private String areaId;

}
