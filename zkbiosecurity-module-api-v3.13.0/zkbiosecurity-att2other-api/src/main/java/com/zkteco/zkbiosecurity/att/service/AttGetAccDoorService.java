package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.Att4AccDoorSelect;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 11:50 2018/11/26
 */
public interface AttGetAccDoorService {


    /**
     * 根据门id获取门信息
     * @param doorId
     * @return
     */
    Att4AccDoorSelect getDoorById(String doorId);


    /**
     * 根据门设备id集合，获取门集合
     * @param deviceIds
     * @return
     */
    List<Att4AccDoorSelect> getDoorsByIds(List<String> deviceIds);

    /**
     * 根据设备序列号和门编号获取门
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/1/29 10:51
     * @param deviceSn
     * @param doorNo
     * @return com.zkteco.zkbiosecurity.att.vo.Att4AccDoorSelect
     */
    Att4AccDoorSelect getDoorByDevSnAndDoorNo(String deviceSn, Short doorNo);
}
