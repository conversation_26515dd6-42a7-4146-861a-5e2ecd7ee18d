package com.zkteco.zkbiosecurity.att.service;

import java.util.Date;

import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
 * @version V1.0
 * @date Created In 11:30 2018/11/26
 */
public interface AttGetAccTransactionService {

    /**
     * 获取门禁当考勤记录，其中data对象是 Att4AccTransaction
     * 
     * <AUTHOR> href="mailto:<EMAIL>">pinker.lin</a>
     * @date 2018/11/26 10:32
     * @param devSn
     *            设备SN
     * @param doorNo
     *            门编号
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    Pager getAccTransactionList(String devSn, Short doorNo, Date startDatetime, Date endDatetime, int page, int size);

    /**
     * 获取门禁的总条数
     * 
     * @param devSn
     *            设备SN
     * @param doorNo
     *            门编号
     * @param startDatetime
     *            开始时间
     * @param endDatetime
     *            结束时间
     * @return
     */
    int getAccTransactionCount(String devSn, Short doorNo, Date startDatetime, Date endDatetime);

    /**
     * 根据门ID集合，获取指定时间范围内的门禁事件记录
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/8/12 16:25
     * @param doorIds
     * @param startDatetime
     * @param endDatetime
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    default Pager getAccTransactionList(String doorIds, Date startDatetime, Date endDatetime, int page, int size) {
        return null;
    }

    /**
     * 根据门ID集合，获取指定时间范围内的门禁事件记录数量
     * 
     * <AUTHOR> href="mailto:<EMAIL>">hook.fang</a>
     * @date 2020/8/12 16:26
     * @param doorIds
     * @param startDatetime
     * @param endDatetime
     * @return int
     */
    default int getAccTransactionCount(String doorIds, Date startDatetime, Date endDatetime) {
        return 0;
    }

    /**
     * 定时拉取-获取阶段时间内新增加的记录条数
     *
     * @param deviceIds:
     * @param startCreateTime:
     * @param endCreateTime:
     * @return int
     * <AUTHOR>
     * @date 2021-06-10 17:49
     * @since 1.0.0
     */
    default Integer pullAccTransactionCount(String deviceIds, Date startCreateTime, Date endCreateTime) {
        return 0;
    }

    /**
     * 定时拉取-获取分页记录数据
     *
     * @param deviceIds:
     * @param startCreateTime:
     *            记录创建时间开始
     * @param endCreateTime:
     *            记录创建时间结束
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-06-10 17:50
     * @since 1.0.0
     */
    default Pager pullAccTransactionList(String deviceIds, Date startCreateTime, Date endCreateTime, int page,
        int size) {
        return null;
    }

    void pushAttLateToAcc(String pin ,Date attDate,String timeSlotToWorkTime);
}
