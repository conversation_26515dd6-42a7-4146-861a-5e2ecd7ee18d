package com.zkteco.zkbiosecurity.att.service;

import java.util.Collection;
import java.util.Date;
import java.util.List;

import com.zkteco.zkbiosecurity.att.vo.Att4PsgDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * 通道当作考勤时候所调用的接口
 *
 * <AUTHOR>
 * @date 2021/7/12 11:11
 */
public interface AttGetPsgDeviceService {
    /**
     * 获取双列表
     *
     * @param att4PsgDeviceSelectItem
     * @param page
     * @param size
     * @return
     */
    Pager getPsgDeviceSelectItemList(Att4PsgDeviceSelectItem att4PsgDeviceSelectItem, int page, int size);

    /**
     * 根据闸ID获取设备
     *
     * @param deviceIds
     * @return
     */
    List<Att4PsgDeviceSelectItem> getPsgDeviceByGateIds(Collection<String> deviceIds);

    /**
     * 根据闸id和起始时间截止时间取数据数量（根据事件时间查询）
     *
     * @param gateIds
     * @param startTime
     * @param endTime
     * @return
     * <AUTHOR>
     * @date 2021-08-20 18:05
     */
    default Integer getPsgTransactionCount(String gateIds, Date startTime, Date endTime) {
        return null;
    }

    /**
     * 根据通道id和起始时间截止时间取数据数量（根据创建时间查询）
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2021-08-24 14:11
     */
    default Integer pullCountByGateIds(String gateIds, Date startDateTime, Date endDateTime) {
        return null;
    }

    /**
     * 根据闸ID集合，获取指定时间范围内的通道事件记录
     *
     * @param recordType:考勤记录点-通道-拉取记录类型，1=正常通行记录，2=刷卡验证记录
     * @param gateIds
     * @param startDatetime
     * @param endDatetime
     * @param page
     * @param splitSize
     * @return
     * <AUTHOR>
     * @date 2021-08-25 16:21
     */
    default Pager getPsgTransactionList(Short recordType, String gateIds, Date startDatetime, Date endDatetime,
        int page, int splitSize) {
        return null;
    }

    /**
     * 定时拉取-获取分页记录数据
     * 
     * @param gateIds
     * @param startCreateTime
     * @param endCreateTime
     * @param page
     * @param splitSize
     * <AUTHOR>
     * @date 2021-08-26 15:52
     * @param
     * @return
     */
    default Pager pullPsgTransactionList(String gateIds, Date startCreateTime, Date endCreateTime, int page,
        int splitSize) {
        return null;
    }
}
