package com.zkteco.zkbiosecurity.att.service;

import com.zkteco.zkbiosecurity.att.vo.Att4VmsDeviceSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/25
 *
 * 考勤获取Vms设备当做考勤点
 *
 */
public interface AttGetVmsDeviceService {

    /**
     * 通过视频通道ID获取设备信息
     * @param channelId
     * @return
     */
    Att4VmsDeviceSelectItem getVmsDeviceByChannelId(String channelId);

    /**
     * 通过视频通道ID集合获取设备信息集合
     *
     * @param channelIds
     * @return
     */
    List<Att4VmsDeviceSelectItem> getVmsDeviceByChannelIds(Collection<String> channelIds);

    /**
     * 获取双列表
     *
     * @param att4VmsDeviceSelectItem
     * @param page
     * @param size
     * @return
     */
    Pager getVmsDeviceSelectItemList(Att4VmsDeviceSelectItem att4VmsDeviceSelectItem, int page, int size);

}
