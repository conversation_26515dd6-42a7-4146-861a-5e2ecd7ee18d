package com.zkteco.zkbiosecurity.att.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 考勤下发设备人员item
 *
 * <AUTHOR>
 * @date 2020/11/19 10:46
 * @since 1.0.0
 */
@Getter
@Setter
public class AttSdc4AttPersonItem {
	/** 区域ids*/
	private List<String> areaIds;
	/** 员工pin号*/
	private String pin;
	/** 姓名*/
	private String name;
	/** 头像路径*/
	private String photoPath;
	/** 性别 0：男 ，1：女 ，2：未知 */
	private Integer gender;
}
