package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.Condition;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Accessors(chain=true)
public class Acc4CvaTransactionItem {

	/** 设备SN */
	private String devSn;
	/** 读头名称 */
	private String readerName;
	/** 人员编号 */
	private String pin;
	/** 人员卡号 */
	private String cardNo;
	/** 事件时间 */
	private Date eventTime;
	/** 区域名称 */
	private String areaName;
	/** 设备id */
	private String devId;
	/** 设备名称 */
	private String devAlias;
	/** 人员名称 */
	private String name;
	/** 人员lastName */
	private String lastName;
	/**事件点名称*/
	private String eventPointName;
	/**事件名称*/
	private String eventName;
	/**媒体文件*/
	private String vidLinkageHandle;
	private Short eventNo;
	private String eventPointId;


}
