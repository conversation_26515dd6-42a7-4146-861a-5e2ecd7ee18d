package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain=true)
public class Acc4AiDeviceItem {
    /** 系统区域 */
    private String authAreaId;

    /**  设备型号 */
    private String deviceName;

    private Short commType;

    /**  IP地址 */
    private String ipAddress;

    /**  设备名称 */
    private String devAlias;

    /**  设备序列号 */
    private String devSn;

    private Boolean devStatus;

    private Short machineType;

    private Boolean enabled;

    private String accReaderId;
}
