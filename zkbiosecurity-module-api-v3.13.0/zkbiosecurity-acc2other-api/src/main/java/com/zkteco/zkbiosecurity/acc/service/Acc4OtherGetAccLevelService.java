package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

/**
 * 其他模块获取权限组接口
 *
 * <AUTHOR>
 * @date 2021/6/8 9:44
 * @since 1.0.0
 */
public interface Acc4OtherGetAccLevelService {

    /**
     * 方法描述 根据权限组id集合获取权限组底下的门id集合
     * 
     * @param levelIds: 权限组id集合
     * @return
     * <AUTHOR>
     * @throws        
     * @date  2021-06-08 10:08
     * @since 1.0.0
     */
    default List<String> getDoorIdsByLevelIds(List<String> levelIds){ return null; }

    /**
     * 方法描述 根据门id集合获取权限组id集合
     *
     * @param doorIds: 门id集合
     * @return
     * <AUTHOR>
     * @throws
     * @date  2021-06-08 10:10
     * @since 1.0.0
     */
    default List<String> getLevelIdsByDoorIds(List<String> doorIds){ return null; }
}
