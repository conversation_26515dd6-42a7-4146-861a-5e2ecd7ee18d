package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁设备对接NVR设备
 *
 * <AUTHOR>
 * @date 2024/1/22 11:02
 */
@Getter
@Setter
@Accessors(chain = true)
public class Acc4NVRDeviceItem {
    /** 别名 */
    private String name;
    /** 类型 ZKNVR-7 */
    private String type;
    /** ip */
    private String ip;
    /** 端口 */
    private String port;
    /** 用户名 */
    private String user;
    /** 密码 */
    private String password;
    /** 设备sn */
    private String devSn;
}
