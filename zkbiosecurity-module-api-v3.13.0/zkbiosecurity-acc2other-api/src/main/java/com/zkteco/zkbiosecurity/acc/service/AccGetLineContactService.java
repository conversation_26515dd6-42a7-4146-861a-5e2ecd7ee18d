package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccGetLineContactSelectItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * 获取line通讯录接口
 *
 * <AUTHOR>
 * @date 2022/2/16 13:42
 */
@Deprecated
public interface AccGetLineContactService {

    /**
     * 获取line通讯录联系人列表信息
     * 
     * @param item:
     * @param pageNo:
     * @param pageSize:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @throws
     * @date 2022-02-16 13:58
     * @since 1.0.0
     */
    default Pager getLineContactSelectItemByPage(AccGetLineContactSelectItem item, int pageNo, int pageSize) {
        return new Pager();
    }

    /**
     * 根据联系人id获取联系人名称
     * 
     * @param lineContactId:
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccGetLineContactSelectItem>
     * <AUTHOR>
     * @throws
     * @date 2022-02-16 15:59
     * @since 1.0.0
     */
    default List<AccGetLineContactSelectItem> getLineContactByContactIds(String lineContactId) {
        return null;
    }
}
