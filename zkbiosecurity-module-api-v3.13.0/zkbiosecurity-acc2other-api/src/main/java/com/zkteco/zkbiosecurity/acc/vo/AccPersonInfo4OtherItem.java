package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/9 10:42
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccPersonInfo4OtherItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<AccPerson4OtherItem> personList = new ArrayList<>();

    private List<AccBioTemplate4OtherItem> bioTemplateItemList = new ArrayList<>();
}
