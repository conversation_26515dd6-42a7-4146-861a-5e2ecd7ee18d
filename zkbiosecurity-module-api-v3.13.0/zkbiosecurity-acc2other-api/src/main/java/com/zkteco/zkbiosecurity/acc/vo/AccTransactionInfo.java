package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class AccTransactionInfo {
    private Integer logId;
    private String pin;
    private Date eventTime;
    private Short readerState;
    private String cardNo;
    private String eventPointId;
    private Short eventNo;
    private String eventName;
    private String description;
    private Short triggerCond;
    private String vidLinkageHandle;
    private String uniqueKey;
    private String audioFilePath;
    private Short eventPointType;
    private String eventPointName;
    private String devAlias;
    private String name;
    private String readerName;
    private String verifyModeName;
    private String areaName;
    private String devId;
    private Integer captureTime;
    private List<Map<String, Object>> vidDevices;
    private String devSn;
    private String deptName;
    private Object vidLinkageData;
    /** 视频联动抓拍路径 */
    private Object vidLinkageFilePathData;
    /** 视频联动录像通道ID */
    private Object vidLinkageVideoChannelId;
    private String lastName;
    /**
     * 是否带口罩 0：表示没有佩戴口罩；1：表示有佩戴口罩
     */
    private String maskFlag;

    /**
     * 体温
     */
    private String temperature;

    private String deptId;

    private String persPersonId;

    private String zoneId;

    private String accZone;

    private String readerId;
}
