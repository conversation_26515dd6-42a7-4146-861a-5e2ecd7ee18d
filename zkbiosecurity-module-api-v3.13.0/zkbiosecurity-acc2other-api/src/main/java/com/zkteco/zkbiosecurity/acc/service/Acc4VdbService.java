package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.zkteco.zkbiosecurity.acc.vo.Acc4VdbOptionItem;
import com.zkteco.zkbiosecurity.acc.vo.Acc4VdbSipAccountItem;
import com.zkteco.zkbiosecurity.acc.vo.AccContact4VdbItem;
import com.zkteco.zkbiosecurity.acc.vo.AccShortKey4VdbItem;

/**
 * 门禁与可视对讲交互接口
 *
 * <AUTHOR>
 * @date 2024/3/18 14:04
 */
public interface Acc4VdbService {

    /**
     * 设备绑定分机号，进行sip配置信息下发
     *
     * @param item:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-03-18 14:33
     * @since 1.0.0
     */
    void setDeviceSipInfo(Acc4VdbSipAccountItem item);

    /**
     * 设备添加通讯录
     *
     * @param devSn:
     * @param accContact4VdbItems:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-03-19 10:36
     * @since 1.0.0
     */
    void addContacts(String devSn, List<AccContact4VdbItem> accContact4VdbItems);

    /**
     * 设备删除通讯录
     *
     * @param devSn:
     * @param accContactIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-03-19 10:36
     * @since 1.0.0
     */
    void delContacts(String devSn, List<String> accContactIds);

    /**
     * 查询设备状态：1：在线；0：离线
     *
     * @param devSns:
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @throws
     * @date 2024-03-28 15:05
     * @since 1.0.0
     */
    Map<Short, Set<String>> getDeviceStatusByDevSns(List<String> devSns);

    /**
     * 设备下发dtmf
     *
     * @param item:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-06-14 9:40
     * @since 1.0.0
     */
    default void setVdbOptionToAcc(Acc4VdbOptionItem item) {}

    /**
     * 清空设备SIP账号信息
     * 
     * @param item
     */
    default void clearDeviceSipInfo(Acc4VdbSipAccountItem item) {}

    /**
     * 设置设备快捷键
     * 
     * @param deviceSn
     * @param accShortKey4VdbItems
     */
    default void setDevShortKey(String deviceSn, List<AccShortKey4VdbItem> accShortKey4VdbItems) {}

    /**
     * 删除设备快捷键
     * 
     * @param deviceSn
     * @param shortKeyIdList
     */
    default void delDevShortKey(String deviceSn, List<String> shortKeyIdList) {}
}
