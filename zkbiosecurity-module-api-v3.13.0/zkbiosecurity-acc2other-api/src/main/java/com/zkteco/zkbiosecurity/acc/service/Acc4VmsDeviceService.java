package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4VmsDeviceItem;

/**
 * <AUTHOR>
 * @date 2020/11/2 16:02
 * @since 1.0.0
 */
public interface Acc4VmsDeviceService {

    /**
     * 将门禁设备添加到vms
     *
     * @param acc4VmsDeviceItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @date 2020-11-02 16:07
     * @since 1.0.0
     */
    default void editVmsDevice(Acc4VmsDeviceItem acc4VmsDeviceItem) {}

    /**
     * 删除vms中的门禁设备
     * 
     * @param snList:设备序列号集合
     * @return void
     * <AUTHOR>
     * @date 2020-11-02 16:15
     * @since 1.0.0
     */
    default void deleteDeviceBySn(String snList) {}

    /**
     * 更新vms设备ip地址
     *
     * @param deviceSn:设备序列号
     * @param ipAddress:设备ip地址
     * @return void
     * <AUTHOR>
     * @date 2020-11-02 16:30
     * @since 1.0.0
     */
    default void updateVmsDeviceIp(String deviceSn, String ipAddress) {}
}
