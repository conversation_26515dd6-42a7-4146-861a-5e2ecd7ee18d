package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/3/8 17:08
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccDoor4OtherItem extends BaseItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 门id */
    private String id;
    /** 门编号 */
    private Short doorNo;
    /** 门名称 */
    private String name;
    /** 设备id */
    private String deviceId;
}
