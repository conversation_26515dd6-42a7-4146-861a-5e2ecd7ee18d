package com.zkteco.zkbiosecurity.acc.vo;

public class Acc4VidChannelItem {
	private static final long serialVersionUID = 1L;
	/** 主键 */
	private String id;

	/** 通道名称 */
	private String name;

	/** 通道编号 */
	private Short channelNo;
	
	/** 区域名称 */
	private String areaName;
	
	/** 区域id */
	private String areaId;
	
	/** 设备名称 */
	private String alias;
	
	/** 设备id */
	private String deviceId;

	/** 禁用启用 */
	private Boolean enabled;

	private String inId;

	private String sn;

	private String notInId;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Short getChannelNo() {
        return channelNo;
    }

    public void setChannelNo(Short channelNo) {
        this.channelNo = channelNo;
    }

    public String getAreaName() {
        return areaName;
    }

    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    public String getAreaId() {
        return areaId;
    }

    public void setAreaId(String areaId) {
        this.areaId = areaId;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getInId() {
        return inId;
    }

    public void setInId(String inId) {
        this.inId = inId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getNotInId() {
        return notInId;
    }

    public void setNotInId(String notInId) {
        this.notInId = notInId;
    }
}
