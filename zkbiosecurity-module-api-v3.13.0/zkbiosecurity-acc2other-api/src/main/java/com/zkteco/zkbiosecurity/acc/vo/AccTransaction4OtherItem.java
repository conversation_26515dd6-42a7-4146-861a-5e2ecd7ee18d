package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/3/11 14:58
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccTransaction4OtherItem {
    /** 事件id */
    private String id;
    /** 记录编号 */
    private Integer logId;
    /** 设备id */
    private String devId;
    /** 设备序列号 */
    private String devSn;
    /** 事件时间 */
    private Date eventTime;
    /** 区域名称 */
    private String areaName;
    /** 设备名称 */
    private String devAlias;
    /** 事件点 */
    private String eventPointName;
    /** 事件名称 */
    private String eventName;
    /** 人员编号 */
    private String pin;
    /** 姓名 */
    private String name;
    /** 姓氏 */
    private String lastName;
    /** 部门编号 */
    private String deptCode;
    /** 部门名称 */
    private String deptName;
    /** 事件编号 */
    private Short eventNo;
    /** 人员卡号 */
    private String cardNo;
    /** 验证方式名称 */
    private String verifyModeName;
    /** 验证方式编号 */
    private String verifyModeNo;
    /** 读头ID */
    private String readerId;
    /** 读头名称 */
    private String readerName;
    /** 描述 */
    private String description;
    /** 开始时间 */
    private Date startTime;
    /** 结束时间 */
    private Date endTime;
    /** 门ID */
    private String doorId;
    /** 门名称 */
    private String doorName;

    private String inEventPointId;
    private String inEventNo;
    private Date startCreateTime;
    private Date endCreateTime;
    private String vidLinkageHandle;
    private String deptId;
    private Date createTime;
    private String eventPointId;
    private String areaId;

    // 防疫模块所需
    /**
     * 是否带口罩 0：表示没有佩戴口罩；1：表示有佩戴口罩
     */
    private String maskFlag;

    /** 体温 */
    private String temperature;

    /**
     * 门禁-停车双重验证新增字段；事件是否为可以通过的事件 true：表示可以通过；false：表示不能通过
     */
    private Boolean verifySuccess;

    /** 抓拍路径 */
    private String capturePhotoPath;
}
