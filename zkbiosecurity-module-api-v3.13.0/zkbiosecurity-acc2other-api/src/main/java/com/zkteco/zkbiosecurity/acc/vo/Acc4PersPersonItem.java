package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Acc4PersPersonItem implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 当前用户权限组id集合,用逗号隔开，如1,2,3,4
     */
    private String personLevelIds;

    /** 以下是门禁人员特殊属性 */

    /**
     * 是否超级用户，15-是；0-否
     */
    private Short superAuth;
    /**
     * 设备操作权限，0-一般人员，14-管理员，2-登记员
     */
    private Short privilege;
    /**
     * 是否延长通行
     */
    private Boolean delayPassage;
    /**
     * 是否黑名单
     */
    private Boolean disabled;
    /**
     * 设置有效时间
     */
    private Boolean isSetValidTime;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 人员id
     */
    private String personId;

    private String opType;

    /**
     * 更新门禁设置
     */
    private Boolean updateAccSet;

    /**
     * 人员需要下发设备的数据有没有被修改的标志
     */
    private Boolean personModify;

    public String getPersonLevelIds() {
        return personLevelIds;
    }

    public void setPersonLevelIds(String personLevelIds) {
        this.personLevelIds = personLevelIds;
    }

    public Short getSuperAuth() {
        return superAuth;
    }

    public void setSuperAuth(Short superAuth) {
        this.superAuth = superAuth;
    }

    public Short getPrivilege() {
        return privilege;
    }

    public void setPrivilege(Short privilege) {
        this.privilege = privilege;
    }

    public Boolean getDelayPassage() {
        return delayPassage;
    }

    public void setDelayPassage(Boolean delayPassage) {
        this.delayPassage = delayPassage;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public Boolean getIsSetValidTime() {
        return isSetValidTime;
    }

    public void setIsSetValidTime(Boolean isSetValidTime) {
        this.isSetValidTime = isSetValidTime;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getOpType() {
        return opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public Boolean getUpdateAccSet() {
        return updateAccSet;
    }

    public void setUpdateAccSet(Boolean updateAccSet) {
        this.updateAccSet = updateAccSet;
    }
}
