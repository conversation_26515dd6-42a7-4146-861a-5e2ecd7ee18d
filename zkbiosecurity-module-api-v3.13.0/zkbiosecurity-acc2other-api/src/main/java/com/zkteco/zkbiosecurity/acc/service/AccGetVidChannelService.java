package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4VidChannelItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

@Deprecated
public interface AccGetVidChannelService {

	/**
	 * 根据用户登录权限过滤显示摄像头
	 * 
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @since 2018年11月26日 下午6:24:19
	 * @param sessionId
	 * @param acc4VidChannelItem
	 * @param pageNo
	 * @param pageSize
	 * @return
	 */
	Pager loadPagerByAuthFilter(String sessionId, Acc4VidChannelItem acc4VidChannelItem, int pageNo, int pageSize);
}
