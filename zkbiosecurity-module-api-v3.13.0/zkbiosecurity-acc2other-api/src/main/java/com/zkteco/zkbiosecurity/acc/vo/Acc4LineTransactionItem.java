package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2019-10-15 16:27
 */
@Getter
@Setter
@Accessors(chain = true)
public class Acc4LineTransactionItem {
    /**
     * 设备SN
     */
    private String devSn;
    /**
     * 读头名称
     */
    private String readerName;

    /**
     * 人员编号
     */
    private String pin;
    /**
     * 人员卡号
     */
    private String cardNo;
    /**
     * 事件时间
     */
    private Date eventTime;
    /**
     * 区域名称
     */
    private String areaName;
    /**
     * 设备id
     */
    private String devId;
    /**
     * 设备名称
     */
    private String devAlias;
    /**
     * 人员名称
     */
    private String name;
    /**
     * 人员lastName
     */
    private String lastName;

    /**
     * 门名称
     */
    private String doorName;

    /**
     * 门id
     */
    private String doorId;

    /**
    * 描述
     */
    private String description;

    /**
    * 抓拍图片
     */
    private String attPhotoUrl;

    /**验证方式名称*/
    private String verifyModeName;

    /** 事件名称 */
    private String eventName;

    /** 事件级别 */
    private Integer eventLevel;

}
