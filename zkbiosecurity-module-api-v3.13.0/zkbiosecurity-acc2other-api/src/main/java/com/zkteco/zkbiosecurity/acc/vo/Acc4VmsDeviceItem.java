package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2020/11/2 16:03
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Acc4VmsDeviceItem implements Serializable {

    /** 设备id */
    private String id;
    /** 设备名称 */
    private String alias;
    /** 序列号 */
    private String sn;
    /** 区域id */
    private String areaId;
    /** 主机地址 */
    private String host;
    /** 端口 */
    private Integer port;
    /** 协议类型 */
    private Short protocol;
    /** 用户名 */
    private String username;
    /** 通信密码 */
    private String commPwd;
}
