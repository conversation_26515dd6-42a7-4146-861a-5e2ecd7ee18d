package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccReader4OtherItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/26 15:34
 * @since 1.0.0
 */
public interface Acc4OtherGetAccReaderService {

    /**
     * 根据设备ID获取读头
     *
     * @param deviceId: 设备ID
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccReader4OtherItem>
     * <AUTHOR>
     * @date 2023-04-26 15:37
     * @since 1.0.0
     */
    default List<AccReader4OtherItem> getItemListByDeviceId(String deviceId) {
        return null;
    }

}
