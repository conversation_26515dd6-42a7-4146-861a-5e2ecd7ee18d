package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;
import java.util.Map;

@Deprecated
public interface AccGetVidLinkageService {

	/**
     * 联动抓图接口
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月2日 下午8:09:33
     * @param vidLinkageHandle 抓图句柄
     * @param entityIds 触发联动的实体类id
     * @param entityClassName 触发联动的实体类类型
     * @return
     */
	boolean getLinkageCapture(String vidLinkageHandle, String entityIds, String entityClassName);
	
	/**
     * 联动录像接口
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月3日 上午10:41:48
     * @param vidLinkageHandle 录像句柄
     * @param entityIds 触发联动的实体类id
     * @param entityClassName 触发联动的实体类类型
     * @param actionTime 录像时间
     * @return
     */
	boolean getLinkageRecord(String vidLinkageHandle, String entityIds, String entityClassName, int actionTime);

	/**
     * 联动弹出视频
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月3日 上午10:34:14
     * @param entityIds 触发联动的实体类id
     * @param entityClassName 触发联动的实体类类型
     * @param actionTime 录像时间
     * @return
     */
	List<Map<String, Object>> getLinkagePopUpVideo(String entityIds, String entityClassName, int actionTime);

	/**
	 * 联动抓图接口（返回文件路径）
	 *
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/8/7 13:47
	 * @param vidLinkageHandle 抓图句柄
	 * @param entityIds 触发联动的实体类id
	 * @param entityClassName 触发联动的实体类类型
	 * @return
	 */
	ZKResultMsg getVidLinkageCapture(String vidLinkageHandle, String entityIds, String entityClassName);

	/**
	 * 联动录像接口(返回文件路径)
	 *
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/8/8 11:28
	 * @param vidLinkageHandle 录像句柄
	 * @param entityIds 触发联动的实体类id
	 * @param entityClassName 触发联动的实体类类型
	 * @param actionTime 录像时间
	 * @return
	 */
	ZKResultMsg getVidLinkageRecord(String vidLinkageHandle, String entityIds, String entityClassName, int actionTime);
}
