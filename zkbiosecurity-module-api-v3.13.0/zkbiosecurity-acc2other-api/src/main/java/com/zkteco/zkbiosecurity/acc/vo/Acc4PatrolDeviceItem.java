package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain=true)
public class Acc4PatrolDeviceItem implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 设备ID */
	private String devId;
	/** 设备SN */
	private String sn;
	/** 区域ID */
	private String authAreaId;

	private String authAreaName;
	/** 设备名称 */
	private String alias;

	private String deviceName;
}
