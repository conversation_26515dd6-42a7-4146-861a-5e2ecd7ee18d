package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccTransaction4OtherItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * <AUTHOR>
 * @date 2021/3/11 14:56
 * @since 1.0.0
 */
public interface AccGetTransaction4OtherService {

    /**
     * 根据设备sn获取指定时间段内的门禁事件记录
     *
     * @param accTransaction4OtherItem:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-03-11 14:56
     * @since 1.0.0
     */
    default Pager getAccTransactionList(AccTransaction4OtherItem accTransaction4OtherItem, int page, int size) {
        return null;
    }

    /**
     * 获取指定时间段某个设备的记录数量
     *
     * @param accTransaction4OtherItem:
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021-03-11 14:58
     * @since 1.0.0
     */
    default Long countAccTransactionByCondition(AccTransaction4OtherItem accTransaction4OtherItem) {
        return null;
    }

}
