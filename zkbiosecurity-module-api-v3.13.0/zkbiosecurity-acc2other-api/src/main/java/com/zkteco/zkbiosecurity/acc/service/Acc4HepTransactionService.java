package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4HepTransactionItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * <AUTHOR>
 * @date 2020-08-07 15:19
 */
@Deprecated
public interface Acc4HepTransactionService {
    /**
     * 根据设备sn获取指定时间段内的门禁事件记录
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-11 9:43
     * @param acc4HepTransactionItem
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     */
    default Pager hepGetAccTransactionList(Acc4HepTransactionItem acc4HepTransactionItem, int page, int size) {
        return null;
    }

    /**
     * 获取指定时间段某个设备的记录数量
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-08-11 9:37
     * @param acc4HepTransactionItem
     * @return java.lang.Long
     */
    default Long countAccTransactionByCondition(Acc4HepTransactionItem acc4HepTransactionItem) {
        return null;
    }

}
