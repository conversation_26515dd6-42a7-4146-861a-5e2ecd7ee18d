package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2023/4/26 15:37
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccReader4OtherItem extends BaseItem {

    /** 读头ID */
    private String id;
    /** 读头名称 */
    private String name;
    /** 读头编号 */
    private Short readerNo;
    /** 设备ID **/
    private String deviceId;
    /** 设备SN **/
    private String deviceSn;
}
