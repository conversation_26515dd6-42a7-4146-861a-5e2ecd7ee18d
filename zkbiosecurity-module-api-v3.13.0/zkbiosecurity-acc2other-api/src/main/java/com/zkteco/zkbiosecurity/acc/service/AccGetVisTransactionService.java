package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.Acc4VisPersonInfo;
import com.zkteco.zkbiosecurity.acc.vo.Acc4VisTransactionItem;

public interface AccGetVisTransactionService {

    /**
     * 根据pin号获取访客信息
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月29日 下午3:20:31
     * @param pin
     * @return
     */
    Acc4VisTransactionItem getVisitorInfoByPin(String pin);

    /**
     * 根据记录id和权限组ID查询记录信息
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月29日 下午3:42:05
     * @param visitorIds
     * @param levelIdList
     * @return
     */
    List<String> getTranPinByTranIdAndLevelId(List<String> visitorIds, List<String> levelIdList);

    /**
     * 根据访客pin号获取门禁权限组ids,用于下发权限
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月29日 下午3:44:56
     * @param visitorPin
     * @return
     */
    List<String> findModuleIdsByVisEmpPin(String visitorPin);

    /**
     * 根据权限组id获取访客信息
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月29日 下午4:28:56
     * @param levelId
     * @return
     */
    List<Acc4VisPersonInfo> getVisiotrInfosByLevelId(String levelId);

    /**
     * 获取访客门禁权限组集合
     * 
     * @param levelIds:
     * @param transactionIds:
     * @return java.util.Map<java.lang.String,java.util.List<java.lang.String>>
     * <AUTHOR>
     * @throws @date
     *             2020-12-02 14:19
     * @since 1.0.0
     */
    Map<String, List<String>> getVisiotrLevelsByLevelIdAndTranId(List<String> levelIds, List<String> transactionIds);
}
