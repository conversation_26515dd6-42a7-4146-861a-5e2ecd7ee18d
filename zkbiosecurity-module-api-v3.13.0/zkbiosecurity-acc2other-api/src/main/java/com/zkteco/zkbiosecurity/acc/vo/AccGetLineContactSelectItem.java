package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁获取line通讯录
 *
 * <AUTHOR>
 * @date 2022/2/16 13:50
 */
@Getter
@Setter
@Accessors(chain = true)
@Deprecated
public class AccGetLineContactSelectItem extends BaseItem {

    private String id;

    /** 联系人 */

    private String contact;

    private String selectId;

    private String type;
}
