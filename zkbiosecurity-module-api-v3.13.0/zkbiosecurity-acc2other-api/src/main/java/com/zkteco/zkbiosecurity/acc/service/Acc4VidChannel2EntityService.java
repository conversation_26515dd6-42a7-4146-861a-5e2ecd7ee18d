package com.zkteco.zkbiosecurity.acc.service;

@Deprecated
public interface Acc4VidChannel2EntityService {

	/**
     * 根据通道id和门禁设备实体名称删除通道绑定
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年7月18日 下午3:35:30
     * @param channelIds
     */
    void deleteByEntityIdAndAccDevice(String channelIds);
    
    /**
     * 根据通道id和门禁读头实体名称删除通道绑定
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月17日 上午11:24:34
     * @param channelIds
     */
    void deleteByEntityIdAndAccReader(String channelIds);
    
    /**
     * 根据通道id和门禁辅助输入实体名称删除通道绑定
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月17日 上午11:24:34
     * @param channelIds
     */
    void deleteByEntityIdAndAccAuxIn(String channelIds);
}
