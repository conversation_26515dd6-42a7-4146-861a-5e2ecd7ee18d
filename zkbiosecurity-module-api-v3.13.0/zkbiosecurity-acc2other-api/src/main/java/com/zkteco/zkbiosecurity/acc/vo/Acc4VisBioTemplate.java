package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.Comparator;

public class Acc4VisBioTemplate implements Serializable, Comparator<Acc4VisBioTemplate> {
    private static final long serialVersionUID = 1L;

    private String pin;// 人员编号
    private Short templateId;// 生物模板id
    private Short templateIndex;// 模板编号
    private Boolean duress;// 生物模板类型
    private String template;// 生物模板
    private Short type;// 指纹，面部，指静脉
    private String version;// 生物模板版本
    private Short format;// 下发类型，0：模版, 1：url（图片）
    private String url;// 生物模版提取用图片

    // TODO 可去掉
    public Acc4VisBioTemplate(Object[] personObj) {
        this.pin = personObj[1] != null ? personObj[1].toString() : "";
        this.templateId = personObj[2] != null ? (Short)personObj[2] : null;
        // Short validType = personObj[3] != null ? (Short) personObj[3] : null;
        // 定义大于16的为胁迫指纹索引，禁止根据valid进行判断，该字段是含义是定义有效和无效的意思，默认是1
        Boolean isDuress = false;
        if (templateId >= 16) {
            // 下发的时候，如果是胁迫恢复直接下发正常的finegerId，所以减去16，modified by max 20160330
            templateId = (short)(templateId - 16);
            isDuress = true;
        }
        this.duress = isDuress;
        this.template = personObj[4] != null ? personObj[4].toString() : "";
        this.type = personObj[5] != null ? (Short)personObj[5] : null;
        this.templateIndex = personObj[6] != null ? (Short)personObj[6] : null;
    }

    public Acc4VisBioTemplate(String pin, Short templateId, Short templateIndex, Boolean duress, String template,
        Short type) {
        super();
        this.pin = pin;
        this.templateId = templateId;
        this.templateIndex = templateIndex;
        this.duress = duress;
        this.template = template;
        this.type = type;
    }

    public Acc4VisBioTemplate(String pin, Short templateId, Short type) {
        super();
        this.pin = pin;
        this.templateId = templateId;
        this.type = type;
    }

    public Acc4VisBioTemplate(String pin) {
        this.pin = pin;
    }

    public Acc4VisBioTemplate() {}

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public Short getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Short templateId) {
        this.templateId = templateId;
    }

    public Short getTemplateIndex() {
        return templateIndex;
    }

    public void setTemplateIndex(Short templateIndex) {
        this.templateIndex = templateIndex;
    }

    public Boolean getDuress() {
        return duress;
    }

    public void setDuress(Boolean duress) {
        this.duress = duress;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public Short getType() {
        return type;
    }

    public void setType(Short type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Short getFormat() {
        return format;
    }

    public void setFormat(Short format) {
        this.format = format;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 模块id排序，从小到大
     */
    @Override
    public int compare(Acc4VisBioTemplate o1, Acc4VisBioTemplate o2) {
        // 根据是否是胁迫排序，又要指令下发那块，胁迫下发必须放在最后 modified by max 20160330
        if (o1.getDuress() && !o2.getDuress()) {
            return 1;
        } else if (o1.getDuress() && o2.getDuress()) {
            return 0;
        } else {
            return -1;
        }
    }
}
