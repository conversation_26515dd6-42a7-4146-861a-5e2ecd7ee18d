package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.Collection;

@Deprecated
public interface Acc4VidChannelService {

    /**
     * 判断视频模块是否存在通道（摄像头）
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 14:58
     * @param
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg isExistVidChannel();

    /**
     * 根据实体id和实体名称获取绑定的摄像头id
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 14:58
     * @param entityIds 实体id
     * @param entityClassName 实体名称
     * @return java.lang.String
     */
    String getBindChannelIds(Collection<String> entityIds, String entityClassName);

    /**
     * 绑定/解绑视频通道(摄像头)
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/8/15 14:59
     * @param channelIds 已绑定的通道id
     * @param entityName 实体名称
     * @param entityId 实体id
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg bindOrUnbindChannel(String channelIds, String entityName, String entityId);
}
