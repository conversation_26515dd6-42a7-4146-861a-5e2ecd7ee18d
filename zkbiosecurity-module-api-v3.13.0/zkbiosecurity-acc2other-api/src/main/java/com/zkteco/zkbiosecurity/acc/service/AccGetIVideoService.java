package com.zkteco.zkbiosecurity.acc.service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

public interface AccGetIVideoService {

    /**
     * 获取Digifort的全局事件
     * 
     * <AUTHOR>
     * @since 2020/7/15 17:39
     * @param
     * @return com.alibaba.fastjson.JSONObject
     */
    JSONObject getDigifortGlobalEventNames();

    /**
     * 下载录像
     * 
     * <AUTHOR>
     * @since 2020/7/17 18:37
     * @param cameraName
     * @param eventTime
     * @return java.util.Map
     */
    ZKResultMsg getDigifortVideoFile(String cameraName, long eventTime);

    /**
     * 检查Digifort许可，判断是否在前端显示
     * 
     * @return boolean
     * <AUTHOR>
     * @throws @date
     *             2020-11-11 10:25
     * @since 1.0.0
     */
    boolean checkDigifortLicense();
}
