package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4IVideoControlEntityItem;

public interface Acc4IVideoControlEntityService {
    /**
    * 功能描述:根据实体id修改实体名称
    * <AUTHOR>
    * @since 2019-03-19 19:53
    * @Param [acc4IVideoControlEntityItem]
    * @return
     */
    void updateEntityNameByEntityId(Acc4IVideoControlEntityItem acc4IVideoControlEntityItem);

    /**
    * 功能描述:根据设备序列号删除对接系统中的门，辅助输入，辅助输出
    * <AUTHOR>
    * @since 2019-03-20 10:08
    * @Param [sn]
    * @return
     */
    void delControlEntitiesByDevSn(String sn);
}
