package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4CvaTransactionItem;
import com.zkteco.zkbiosecurity.acc.vo.Acc4PatrolTransactionItem;

public interface Acc4CvaTransactionService {

	/**
	 * 门禁推送事件记录到智能分析模块
	 * 
	 * <AUTHOR>
	 * @since 2018年4月26日 下午5:29:47
	 * @param acc4PatrolTransactionService
	 */
	void pushTransactionsToCva(Acc4CvaTransactionItem acc4PatrolTransactionService);
}
