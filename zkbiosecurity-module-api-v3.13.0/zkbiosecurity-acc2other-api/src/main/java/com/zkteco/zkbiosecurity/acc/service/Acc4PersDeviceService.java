package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4PersDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.AccDeviceConditionItem;

import java.util.List;

/**
 * <AUTHOR> href="mailto:<EMAIL>">amaze.wu</a>
 * @date $date$ $time$
 * $params$
 * @return $returns$
 */
public interface Acc4PersDeviceService {

    List<Acc4PersDeviceItem> getItemsByPage(AccDeviceConditionItem condition, int page, int size);

    String getMasterLevelId();
}
