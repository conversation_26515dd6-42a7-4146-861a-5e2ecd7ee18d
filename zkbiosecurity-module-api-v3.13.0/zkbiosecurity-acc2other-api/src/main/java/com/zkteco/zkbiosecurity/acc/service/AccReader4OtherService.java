package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccReader4OtherItem;

import java.util.List;

/**
 * 读头对接其他模块接口
 * 
 * <AUTHOR>
 * @date 2021/3/8 15:38
 * @since 1.0.0
 */
public interface AccReader4OtherService {

    /**
     * 判断门禁读头是否在其他模块使用 若被使用，实现模块抛出业务异常，throw new ZKBusinessException(level,msg);
     *
     * @param readerIds:读头id
     * @return void
     * <AUTHOR>
     * @date 2021-03-08 15:39
     * @since 1.0.0
     */
    default void checkAccReaderIsUsed(List<String> readerIds) {};

    /**
     * 通知使用门禁读头的模块修改读头信息
     *
     * @param item: 读头信息
     * @return void
     * <AUTHOR>
     * @date 2023-04-27 15:19
     * @since 1.0.0
     */
    default void editAccReaderInfo(AccReader4OtherItem item) {}
}
