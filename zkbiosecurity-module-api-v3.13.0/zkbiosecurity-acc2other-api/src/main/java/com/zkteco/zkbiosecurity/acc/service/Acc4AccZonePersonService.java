package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccTransactionInfo;

import java.util.List;

public interface Acc4AccZonePersonService {
    /**
     * 门禁推送区域内人员数据
     *
     * @param accTransaction
     */
    void rtUpdatePersonInsideData(AccTransactionInfo accTransaction);

    /**
     * 根据人员id删除区域内人员
     *
     * @param personList
     */
    void deleteAccZonePersonByPersPersonIdIn(List<String> personList);
}
