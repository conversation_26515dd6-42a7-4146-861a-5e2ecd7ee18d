package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2020-08-05 15:52
 */
@Getter
@Setter
public class Acc4HepTransactionItem {
    /** 记录编号 */
    private Integer logId;

    /**
     * 设备序列号
     */
    private String devSn;

    /** 事件时间 */
    private Date eventTime;

    /** 区域名称 */
    private String areaName;

    /** 设备名称 */
    private String devAlias;

    /** 事件点 */
    private String eventPointName;

    /** 事件名称 */
    private String eventName;

    /** 人员编号 */
    private String pin;

    /** 姓名 */
    private String name;
    /**
     * 姓氏
     */
    private String lastName;

    /** 部门编号 */
    private String deptCode;

    /** 部门名称 */
    private String deptName;

    /**
     * 是否带口罩 0：表示没有佩戴口罩；1：表示有佩戴口罩
     */
    private String maskFlag;

    /** 体温 */
    private String temperature;

    private Short eventNo;

    private String eventNoIn;
    private Date startCreateTime;
    private Date endCreateTime;
    private String vidLinkageHandle;
    private String deptId;
    private Date createTime;
}
