package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁对接可视对讲设备
 *
 * <AUTHOR>
 * @date 2022/4/28 14:40
 */
@Getter
@Setter
@Accessors(chain = true)
public class Acc4IntercomDeviceItem {
    
    /** 设备id */
    private String id;
    /** 设备名称 */
    private String name;
    /** 序列号 */
    private String sn;
    /** 区域id */
    private String areaId;
    /** 主机地址 */
    private String ip;
    /** 端口 */
    private Integer port;
    /** 协议类型 */
    private String protocolType;
    /** 用户名 */
    private String username;
    /** 通信密码 */
    private String commPwd;
    
    
}
