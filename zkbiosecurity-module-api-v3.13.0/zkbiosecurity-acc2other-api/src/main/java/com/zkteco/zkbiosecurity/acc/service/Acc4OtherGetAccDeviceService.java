package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccDevice4OtherItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;

/**
 * 其他模块获取门禁设备信息
 *
 * <AUTHOR>
 * @date 2022/4/6 15:25
 * @since 1.0.0
 */
public interface Acc4OtherGetAccDeviceService {

    /**
     * 根据设备id获取门禁设备信息
     * <AUTHOR>
     * @date  2022/4/6 15:30
     * @param devIds
     * @since 1.0.0
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.AccDevice4OtherItem>
     */
    default List<AccDevice4OtherItem> getAccDevicesByDeviceIds(String devIds) {
        return null;
    }

    /**
     * 根据查询条件获取
     *
     * @param sessionId:sessionId
     * @param condition:条件
     * @param pageNo:当前页
     * @param size:每页数目
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2023-04-21 15:37
     * @since 1.0.0
     */
    default Pager getItemByAuthFilter(String sessionId, AccDevice4OtherItem condition, int pageNo, int size) {
        return null;
    }
}
