package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain=true)
public class Acc4PatrolTransactionItem {

	/** 设备SN */
	private String devSn;
	/** 读头名称 */
	private String readerName;
	/** 人员编号 */
	private String pin;
	/** 人员卡号 */
	private String cardNo;
	/** 事件时间 */
	private Date eventTime;
	/** 区域名称 */
	private String areaName;
	/** 设备id */
	private String devId;
	/** 设备名称 */
	private String devAlias;
	/** 人员名称 */
	private String name;
	/** 人员lastName */
	private String lastName;
	/** 验证方式名称 */
	private String verifyModeName;
}
