package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccTransactionInfo;

import java.util.List;
import java.util.Map;

public interface Acc4AccGlobalLinkageService {
    /**
     * 门禁事件判断全局联动入口
     *
     * @param item
     * @param userData
     * @return
     */
    AccTransactionInfo globalLinkage(AccTransactionInfo item, Map<String, String> userData);

    /**
     * 判断时间段是否被全局联动使用
     *
     * @param timesegId
     * @return
     */
    boolean checkTimesegUsed(String timesegId);

    /**
     * 根据人员id删除全局联动人员
     *
     * @param personList
     */
    void deleteAccGlobalLinkagePersonByPersPersonIdIn(List<String> personList);

    /**
     * 根据设备id删除全局联动中的配置
     *
     * @param devId
     */
    void delGlinkByDevId(String devId);
}
