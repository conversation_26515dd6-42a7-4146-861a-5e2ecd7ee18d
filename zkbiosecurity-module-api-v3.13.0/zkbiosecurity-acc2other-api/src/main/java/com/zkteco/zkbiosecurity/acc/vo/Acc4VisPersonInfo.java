package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 下发人员相关参数bean--应该可以提取放到系统目录下，所有的业务模块都可以共用
 *
 * <AUTHOR>
 * @since 2014年8月22日 下午2:36:01
 */
public class Acc4VisPersonInfo implements Serializable{

    private static final long serialVersionUID = 1L;

    private List<Acc4VisPersonOpt> personList = new ArrayList<Acc4VisPersonOpt>();

    private List<Acc4VisBioTemplate> accBioTemplateItemList = new ArrayList<Acc4VisBioTemplate>();

    public List<Acc4VisPersonOpt> getPersonList()
    {
        return personList;
    }

    public void setPersonList(List<Acc4VisPersonOpt> personList)
    {
        this.personList = personList;
    }

    public List<Acc4VisBioTemplate> getAccBioTemplateItemList() {
        return accBioTemplateItemList;
    }

    public void setAccBioTemplateItemList(List<Acc4VisBioTemplate> accBioTemplateItemList) {
        this.accBioTemplateItemList = accBioTemplateItemList;
    }
}
