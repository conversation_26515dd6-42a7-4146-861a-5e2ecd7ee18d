package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * 门禁sip信息配置item
 *
 * <AUTHOR>
 * @date 2024/3/18 14:10
 */
@Getter
@Setter
public class Acc4VdbSipAccountItem {
    /** sip账号唯一标识，当设备存在此id的信息时为变更sip信息，若不存在时则为新增 */
    private String id;

    /** 用户名，用户拨号认证，一般与authname保持一致即可 */
    private String userName;

    /** 认证名，用于auth认证，一般与username保持一致即可 */
    private String authName;

    /** sip账号密码 */
    private String password;

    /** sip服务器地址，如sip.minervaiot.com */
    private String server;

    /** 别名，为空时与username一致 */
    private String displayName;

    /** sip传输协议, 0: UDP, 1: TCP, 2: TLS, 缺省为0 */
    private String protocol;

    /** 是不是主sip账号，当设置多个sip账号时，有且仅能指定一个sip账号为主账号，取值，1：是(缺省)，0：否 */
    private String primary;

    private String deviceSn;
}
