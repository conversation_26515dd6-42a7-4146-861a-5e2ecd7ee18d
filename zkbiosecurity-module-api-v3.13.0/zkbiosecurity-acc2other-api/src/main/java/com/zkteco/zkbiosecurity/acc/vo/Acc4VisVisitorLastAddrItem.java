package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.Date;

/**
 * 访客最后访问位置
 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
 * @since 2018年11月29日 下午6:29:25
 */
public class Acc4VisVisitorLastAddrItem implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	/** 访客pin号 */
	private String pin;
	
	/** 访客姓名 */
	private String name;
	
	/** 访客姓氏 */
	private String lastName;
	
	/** 卡号 */
	private String cardNo;
	
	/** 时间 */
	private Date eventTime;
	
	/** 进入时间 */
	private Date enterTime;
	
	/** 设备 */
	private String devAlias;
	
	/** 事件点 */
	private String eventPointName;
	
	/** 事件描述 */
	private String eventName;
	
	/** 读头名称 */
	private String readerName;
	
	/** 验证方式 */
	private String verifyModeName;
	
	/** 区域 */
	private String areaName;
	
	/** 停留时间 */
	private String stayTime;

	private Date startTime;

	private Date endTime;
	
	private String devId;
	
	private String devSn;
	
	private String eventPointId;
	
	private Short eventNo;
	
	private Short eventPointType;
	
	private Short readerState;
	
	private Short verifyModeNo;

	private String readerId;

	public String getPin()
	{
		return pin;
	}

	public void setPin(String pin)
	{
		this.pin = pin;
	}

	public String getName()
	{
		return name;
	}

	public void setName(String name)
	{
		this.name = name;
	}

	public String getLastName()
	{
		return lastName;
	}

	public void setLastName(String lastName)
	{
		this.lastName = lastName;
	}

	public String getCardNo()
	{
		return cardNo;
	}

	public void setCardNo(String cardNo)
	{
		this.cardNo = cardNo;
	}

	public Date getEventTime()
	{
		return eventTime;
	}

	public void setEventTime(Date eventTime)
	{
		this.eventTime = eventTime;
	}

	public Date getEnterTime()
	{
		return enterTime;
	}

	public void setEnterTime(Date enterTime)
	{
		this.enterTime = enterTime;
	}

	public String getDevAlias()
	{
		return devAlias;
	}

	public void setDevAlias(String devAlias)
	{
		this.devAlias = devAlias;
	}

	public String getEventPointName()
	{
		return eventPointName;
	}

	public void setEventPointName(String eventPointName)
	{
		this.eventPointName = eventPointName;
	}

	public String getEventName()
	{
		return eventName;
	}

	public void setEventName(String eventName)
	{
		this.eventName = eventName;
	}

	public String getReaderName()
	{
		return readerName;
	}

	public void setReaderName(String readerName)
	{
		this.readerName = readerName;
	}

	public String getVerifyModeName()
	{
		return verifyModeName;
	}

	public void setVerifyModeName(String verifyModeName)
	{
		this.verifyModeName = verifyModeName;
	}

	public String getAreaName()
	{
		return areaName;
	}

	public void setAreaName(String areaName)
	{
		this.areaName = areaName;
	}

	public String getStayTime()
	{
		return stayTime;
	}

	public void setStayTime(String stayTime)
	{
		this.stayTime = stayTime;
	}

	public String getId()
	{
		return null;
	}

	public Date getStartTime()
	{
		return startTime;
	}

	public void setStartTime(Date startTime)
	{
		this.startTime = startTime;
	}

	public Date getEndTime()
	{
		return endTime;
	}

	public void setEndTime(Date endTime)
	{
		this.endTime = endTime;
	}

	public String getDevId()
	{
		return devId;
	}

	public void setDevId(String devId)
	{
		this.devId = devId;
	}

	public String getDevSn()
	{
		return devSn;
	}

	public void setDevSn(String devSn)
	{
		this.devSn = devSn;
	}

	public String getEventPointId()
	{
		return eventPointId;
	}

	public void setEventPointId(String eventPointId)
	{
		this.eventPointId = eventPointId;
	}

	public Short getEventNo()
	{
		return eventNo;
	}

	public void setEventNo(Short eventNo)
	{
		this.eventNo = eventNo;
	}

	public Short getEventPointType()
	{
		return eventPointType;
	}

	public void setEventPointType(Short eventPointType)
	{
		this.eventPointType = eventPointType;
	}

	public Short getReaderState()
	{
		return readerState;
	}

	public void setReaderState(Short readerState)
	{
		this.readerState = readerState;
	}

	public Short getVerifyModeNo()
	{
		return verifyModeNo;
	}

	public void setVerifyModeNo(Short verifyModeNo)
	{
		this.verifyModeNo = verifyModeNo;
	}

	public void setId(String id)
	{
		this.id = id;
	}

	public void setReaderId(String readerId) {
		this.readerId = readerId;
	}

	public String getReaderId() {
		return readerId;
	}
}
