package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccDoor4OtherItem;

/**
 * 门对接其他模块接口
 * 
 * <AUTHOR>
 * @date 2021/3/8 15:31
 * @since 1.0.0
 */
public interface AccDoor4OtherService {

    /**
     * 判断门禁门是否在其他模块使用 若被使用，实现模块抛出业务异常，throw new ZKBusinessException(level,msg);
     *
     * @param doorIds:门ids
     * @return void
     * <AUTHOR>
     * @date 2021-03-08 15:32
     * @since 1.0.0
     */
    default void checkAccDoorIsUsed(String doorIds) {}

    /**
     * 通知其他模块修改门信息
     *
     * @param item:门信息
     * @return void
     * <AUTHOR>
     * @date 2021-03-08 17:13
     * @since 1.0.0
     */
    default void editAccDoorInfo(AccDoor4OtherItem item) {}

}
