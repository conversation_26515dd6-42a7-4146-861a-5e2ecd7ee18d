package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 门禁设备对接其他模块VO
 * 
 * <AUTHOR>
 * @date 2021/3/8 16:18
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccDevice4OtherItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 设备ID */
    private String id;
    /** 设备SN */
    private String sn;
    /** 区域ID */
    private String authAreaId;
    /** 设备名称 */
    private String alias;
    /** 门id集合 */
    private List<String> doorIds;
    /** IP地址 */
    private String ipAddress;
    /** 设备型号 */
    private String deviceName;

    private String inId;

    private String notInId;
}
