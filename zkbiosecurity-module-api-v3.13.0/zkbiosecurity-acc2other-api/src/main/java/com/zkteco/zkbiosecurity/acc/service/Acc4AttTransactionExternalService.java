package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4AttIsExceptionTimeItem;
import com.zkteco.zkbiosecurity.acc.vo.Acc4AttTransactionExternalItem;

import java.util.Date;
import java.util.List;

public interface Acc4AttTransactionExternalService {

    /**
     * 门禁推送事件记录到考勤模块
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月27日 下午5:11:24
     * @param acc4AttTransactionExternalItem
     */
    @Deprecated
    void pushAccTransactions(Acc4AttTransactionExternalItem acc4AttTransactionExternalItem);

    public List<Acc4AttIsExceptionTimeItem> isExceptionRecord(String pin, Date outTime, Date inTime);
}
