package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.acc.vo.Acc4PersPersonItem;
import com.zkteco.zkbiosecurity.acc.vo.Acc4PersonLevelItem;

public interface Acc4PersPersonService {
    /**
     * 提供人事调用门禁人员编辑接口，即门禁人员属性编辑
     * 
     * @author: max.zheng
     * @date: 2018-04-16 17:10:77
     * @param accPerson
     * @return
     */
    Boolean editAccPerson(Acc4PersPersonItem accPerson);

    /**
     * 删除检查
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/4 13:36
     * @param personIds
     * @return void
     */
    Boolean checkDelPerson(String personIds);

    /**
     * 删除人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/4 11:58
     * @param personIds
     * @return java.lang.Boolean
     */
    void delAccPerson(String personIds);

    /**
     * 离职人员
     * 
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/4 11:58
     * @param personIds
     * @return java.lang.Boolean
     */
    Boolean leaveAccPerson(String personIds);

    /**
     * 检测密码
     * 
     * @param personPwd
     * @return
     */
    Boolean checkForcePwd(String personPwd);

    /**
     * 批量调整部门通知，需要切换人员的部门权限
     * 
     * @param personIds
     * @param deptId
     */
    void batchDeptChange(List<String> personIds, String deptId);

    /**
     * 重新下发人员信息
     * 
     * <AUTHOR> href="mailto:<EMAIL>">jinjie.you</a>
     * @date 2018/9/7 10:15
     * @param id
     * @return void
     */
    void reIssuePersonInfo(String id);

    /**
     * 获取门禁人员扩展参数
     * 
     * @auther lambert.li
     * @date 2018/11/16 15:27
     * @param
     * @return
     */
    Map<String, String> getAccPersonExtParam(String personId);

    /**
     * 根据人员ID和登录用户权限获取人员权限组信息
     * 
     * @auther lambert.li
     * @date 2018/12/3 14:55
     * @param personId
     * @param token
     * @return
     */
    List<Acc4PersonLevelItem> getPersonLevelByPersonId(String personId, String token);
}
