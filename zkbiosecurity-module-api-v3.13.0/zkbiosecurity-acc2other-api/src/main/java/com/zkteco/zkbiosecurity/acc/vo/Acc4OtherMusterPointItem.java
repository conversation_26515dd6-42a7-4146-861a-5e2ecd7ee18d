package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class Acc4OtherMusterPointItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

	private String id;

	private String name;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 签到点名称
	 */
	private String signPointName;

	/**
	 * 是否激活疏散点
	 */
	private Boolean activated;

	/**
	 * 激活时间
	 */
	private String activatedTime;

	/**
	 * 是否在紧急疏散点被激活后进行消息通知 activated 0-1的状态转变
	 */
	private Boolean messageNotification;

	/**
	 * 发送的短信类型
	 */
	private String messageNotificationType;

	/**
	 * 是否发邮件通知
	 */
	private Boolean sendReport;

	/**
	 * 定时发送邮件的时间间隔
	 */
	private String intervalTime;

	/**
	 * 邮件接收人
	 */
	private String emailRecipients;

	/**
	 * 是否统计访客人员
	 */
	private Boolean statisticsVisitors;
}
