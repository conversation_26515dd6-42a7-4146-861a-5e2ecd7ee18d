package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


/**
 * 门Vo
 *
 * <AUTHOR>
 * @date 2021/1/13 16:30
 * @since 1.0.0
 */
@Getter
@Setter
public class Acc4OtherSelectDoorItem extends BaseItem implements Serializable {

    /** 门Id*/
    private String id;

    /** 门名称 */
    private String name;

    @Deprecated
    private String doorName;

    private Short doorNo;

    private String deviceAlias;

    private String deviceSn;

    private String deviceId;

    private boolean enable;

    // 查询的过滤条件

    private String inId;

    private String notInId;

    private String type;

    private String selectId;

    private String authAreaIdIn;

    private String filterIds;

    private String devIdIn;


}
