package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.AccDevice4OtherItem;

/**
 * 门禁设备对接其他模块接口
 * 
 * <AUTHOR>
 * @date 2021/3/8 14:07
 * @since 1.0.0
 */
public interface AccDevice4OtherService {

    /**
     * 判断门禁设备是否在其他模块使用 若被使用，实现模块抛出业务异常，throw new ZKBusinessException(level,msg);
     *
     * @param devIds:门禁设备id
     * @return void
     * <AUTHOR>
     * @date 2021-03-08 14:13
     * @since 1.0.0
     */
    default void checkAccDeviceIsUsedByDevIds(String devIds) {}

    /**
     * 判断门禁设备是否在其他模块使用 若被使用，实现模块抛出业务异常，throw new ZKBusinessException(level,msg);
     *
     * @param devSn:设备序列号
     * @return void
     * <AUTHOR>
     * @date 2021-03-08 14:46
     * @since 1.0.0
     */
    default void checkAccDeviceIsUsedByDevSn(String devSn) {}

    /**
     * 通知使用门禁设备的模块修改设备信息
     *
     * @param item:设备信息
     * @return void
     * <AUTHOR>
     * @date 2021-03-08 16:22
     * @since 1.0.0
     */
    default void editAccDeviceInfo(AccDevice4OtherItem item) {}

    /**
     * 通知其他模块同步数据
     *
     * @param devIds:设备id
     * @return void
     * <AUTHOR>
     * @date 2021-03-08 16:40
     * @since 1.0.0
     */
    default void syncData2Dev(List<String> devIds) {}

    /**
     * 通知其他模块替换sn
     * 
     * @param oldSn:
     * @param newSn:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2022-09-05 14:02
     * @since 1.0.0
     */
    default void modifyDeviceSn(String oldSn, String newSn) {};

    /**
     * 通知其他模块删除设备
     *
     * @param devSn:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-03-14 10:23
     * @since 1.0.0
     */
    default void delDeviceByDevSn(String devSn) {}
}
