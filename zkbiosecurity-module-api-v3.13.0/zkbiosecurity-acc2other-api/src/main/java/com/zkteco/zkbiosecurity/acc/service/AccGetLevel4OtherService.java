package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccPersonInfo4OtherItem;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/23 14:52
 * @since 1.0.0
 */
public interface AccGetLevel4OtherService {

    /**
     * 下发人员权限
     *
     * @param item:
     * @return void
     * <AUTHOR>
     * @date 2021-03-09 14:37
     * @since 1.0.0
     */
    default void setPersonLevel2Dev(AccPersonInfo4OtherItem item, List<String> doorIds) {}

    /**
     * 删除人员权限
     *
     * @param personPinList:人员编号集合
     * @param doorIdList:门id集合
     * @param isSyncLevelData:是否同步门禁权限组数据
     * @return void
     * <AUTHOR>
     * @date 2021-03-09 14:28
     * @since 1.0.0
     */
    default void delPersonLevel2Dev(List<String> personPinList, List<String> doorIdList, boolean isSyncLevelData) {}
}
