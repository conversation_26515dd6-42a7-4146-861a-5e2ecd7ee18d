package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.AccTransaction4OtherItem;

/**
 * <AUTHOR>
 * @date 2022/5/30 11:12
 * @since 1.0.0
 */
public interface AccTransaction4OtherService {

    /**
     * 推送人员编号不为空的事件到其他业务模块
     *
     * @param accTransaction4OtherItem:
     * @return void
     * <AUTHOR>
     * @date 2022-05-30 11:25
     * @since 1.0.0
     */
    default void pushPersonTransactionData(AccTransaction4OtherItem accTransaction4OtherItem) {};

    /**
     * 推送门禁事件到其他业务模块
     *
     * @param accTransaction4OtherItem:
     * @return void
     * <AUTHOR>
     * @date 2022-05-30 11:13
     * @since 1.0.0
     */
    default void pushTransactionData(AccTransaction4OtherItem accTransaction4OtherItem) {}

    /**
     * 推送adms发布门禁事件到业务模块(针对需要及时处理业务)
     * 
     * @param accTransaction4OtherItem:
     * @return void
     * <AUTHOR>
     * @date 2023-06-01 15:10
     * @since 1.0.0
     */
    default void pushPublishTransactionData(AccTransaction4OtherItem accTransaction4OtherItem) {}

}
