package com.zkteco.zkbiosecurity.acc.service;

/**
 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
 * @version V1.0
 * @date Created In 17:14 2018/5/22
 */
public interface Acc4PersWiegandFmtService {
    /**
     * 检查是否使用韦根格式
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/22 17:16
     * @param wiegandFmtIds
     * @return java.lang.Boolean
     */
    Boolean checkUseWiegandFmt(String wiegandFmtIds);

    /**
     * 删除韦根格式
     * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
     * @date 2018/5/22 17:16
     * @param wiegandFmtIds
     * @return java.lang.Boolean
     */
    Boolean delWiegandFmt(String wiegandFmtIds);

    /**
     * 编辑韦根格式
     * @author: mingfa.zheng
     * @date: 2018/6/21 16:25
     * @return:
     */
    Boolean editWiegandFmt(String wiegandFmtId);

    /**
     *开启韦根测试的功能
     * @param deviceId
     * @return
     */
    String start(String deviceId);

    /**
     *卡格式测试-读卡
     * @param deviceId
     * @return
     */
    String readerCard(String deviceId);

    /**
     *卡格式测试-推荐格式
     * @param deviceId
     * @param sizeCode
     * @param cardNo
     * @param orgCardNo
     * @param bitscount
     * @param withSizeCode
     * @return
     */
    String recommendFmt(String deviceId, String sizeCode, String cardNo, String orgCardNo, String bitscount, String withSizeCode);

    /**
     * 过滤不支持韦根测试的设备
     * @return
     */
    String getAllFilterId();

    /**
     * 结束韦根测试的功能
     * @return
     */
    String stop(String deviceId);

}
