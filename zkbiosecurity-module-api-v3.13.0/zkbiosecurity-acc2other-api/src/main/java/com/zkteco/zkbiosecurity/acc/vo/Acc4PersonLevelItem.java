package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Auther: lambert.li
 * @Date: 2018/12/3 14:36
 */
@Getter
@Setter
@Accessors(chain = true)
public class Acc4PersonLevelItem implements Serializable {

    /* 人员ID */
    private String personId;

    /* 权限组ID */
    private String levelId;

    /* 权限组名称 */
    private String levelName;

    /* 是否选中 */
    private Boolean selected;
}
