package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4OtherSelectDoorItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;

/**
 * 其他模块获取门接口
 *
 * <AUTHOR>
 * @date 2021/1/13 16:28
 * @since 1.0.0
 */
public interface Acc4OtherGetAccDoorService {

    /**
     * 根据id获取门的信息，主要用于获取门的名称
     *
     * @param doorId
     * @return Acc4SelectDoorItem
     * <AUTHOR>
     * @date 2021/1/7 9:58
     */
    default Acc4OtherSelectDoorItem getItemById(String doorId) {
        return null;
    }

    /**
     * 根据id列表批量查询Item
     *
     * @param doorIds 门Id
     * @return List<Acc4SelectDoorItem>
     * <AUTHOR>
     * @date 2021/1/13 10:46
     */
    default List<Acc4OtherSelectDoorItem> getItemsByIds(List<String> doorIds) {
        return null;
    }

    /**
     * 全局联动双选页面获取可以选择的门
     *
     * @param sessionId sessionId
     * @param condition 门Vo
     * @param pageNo 第几页
     * @param size 每页几条
     * @return Pager
     * <AUTHOR>
     * @date 2020/12/25 14:13
     */
    default Pager getItemByAuthFilter(String sessionId, Acc4OtherSelectDoorItem condition, int pageNo, int size) {
        return null;
    }

    /**
     * 远程操作门
     *
     * @param opInterval 操作间隔
     * @param doorId 门主键
     * @param type openDoor/normalOpenDoor/enableNormalOpenDoor/closeDoor/closeDoor
     *            disableNormalOpenDoor/cancelAlarm/lockDoor/unLockDoor
     * @return ZKResultMsg
     * <AUTHOR>
     * @date 2020/12/30 16:58
     */
    default ZKResultMsg operate(String type, int opInterval, String doorId) {
        return null;
    }

    /**
     * 远程操作门（无返回值，提高批量操作效率）
     * 
     * @param type:openDoor/normalOpenDoor/enableNormalOpenDoor/closeDoor/closeDoor
     *            disableNormalOpenDoor/cancelAlarm/lockDoor/unLockDoor
     * @param opInterval:操作间隔
     * @param doorId:门id
     * <AUTHOR>
     * @date 2025-05-12 14:18
     * @since 1.0.0
     */
    default void operateNoneReturn(String type, int opInterval, String doorId) {}

    /**
     * 根据门id列表获取不支持锁定功能的门
     *
     * @param doorIds 查询的门id
     * @return 不支持门锁定功能的门
     * <AUTHOR>
     * @date 2021/1/21 10:41
     */
    default List<Acc4OtherSelectDoorItem> getNotSupportLockdownDoors(List<String> doorIds) {
        return null;
    }

    /**
     * 根据条件查询门信息
     *
     * @param condition:查询条件
     * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.Acc4OtherSelectDoorItem>
     * <AUTHOR>
     * @date 2021-03-15 15:53
     * @since 1.0.0
     */
    default List<Acc4OtherSelectDoorItem> getItemsByCondition(Acc4OtherSelectDoorItem condition) {
        return null;
    }


    /**
     * 方法描述：根据门id获取该设备的所有门id集合
     *
     * @param doorId: 门id
     * @return
     * <AUTHOR>
     * @throws
     * @date  2021-06-08 10:18
     * @since 1.0.0
     */
    default List<String> getAllDoorIdByDoorId(String doorId){ return null; }
}
