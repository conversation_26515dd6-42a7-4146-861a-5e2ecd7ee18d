package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

/**
 * 门禁权限对接其他模块接口
 * 
 * <AUTHOR>
 * @date 2021/3/9 10:41
 * @since 1.0.0
 */
public interface AccLevel4OtherService {

    /**
     * 通知其他模块删除门禁权限
     *
     * @param doorIds:门ids
     * @param personIds:人员ids
     * @return void
     * <AUTHOR>
     * @date 2021-03-23 14:22
     * @since 1.0.0
     */
    default void deleteAccLevel4Others(List<String> doorIds, List<String> personIds) {}
}
