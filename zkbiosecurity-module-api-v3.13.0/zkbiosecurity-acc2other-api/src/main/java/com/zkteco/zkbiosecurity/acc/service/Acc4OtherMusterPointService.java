package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4OtherMusterPointItem;

import java.util.List;

/**
 * 门禁紧急疏散点
 *
 * <AUTHOR>
 * @date 2024/7/2 17:26
 * @since 1.0.0
 */
public interface Acc4OtherMusterPointService {

	/**
	 * 判断紧急疏散点许可
	 *
	 * @return boolean
	 * <AUTHOR>
	 * @date 2024/7/2 17:42
	 * @since 1.0.0
	 */
	default boolean checkShowMusterPoint() {
		return false;
	}

	/**
	 * 获取疏散点
	 *
	 * @return java.util.List<com.zkteco.zkbiosecurity.acc.vo.Acc4OtherMusterPointItem>
	 * <AUTHOR>
	 * @date 2024/7/2 17:55
	 * @since 1.0.0
	 */
	default List<Acc4OtherMusterPointItem> getMusterPointList() {
		return null;
	}

	/**
	 * 启用疏散点
	 *
	 * @param ids:
	 * @return void
	 * <AUTHOR>
	 * @date  2024/7/3 11:40
	 * @since 1.0.0
	 */
	default void activateMusterPoint(String ids){

	};
}
