package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4OtherSelectAuxOutItem;
import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

import java.util.List;


/**
 * 其他模块获取辅助输出接口
 *
 * <AUTHOR>
 * @date 2021/1/13 16:03
 * @since 1.0.0
 */
public interface Acc4OtherGetAccAuxOutService {

    /**
     * 根据辅助输出id获取名称等信息
     *
     * @param auxOutId 主键
     * @return Acc4SelectAuxOutItem
     * <AUTHOR>
     * @date 2020/12/29 16:29
     */
    default Acc4OtherSelectAuxOutItem getItemById(String auxOutId) {
        return null;
    }

    /**
     * 根据辅助输出id列表获取名称等信息
     *
     * @param auxOutIds 主键
     * @return List<Acc4SelectAuxOutItem>
     * <AUTHOR>
     * @date 2020/12/29 16:29
     */
    default List<Acc4OtherSelectAuxOutItem> getItemsByIds(List<String> auxOutIds) {
        return null;
    }

    /**
     * 根据查询条件获取辅助输出
     *
     * @param sessionId sessionId
     * @param condition 辅助输出Vo
     * @param pageNo    第几页
     * @param size      每页几条
     * @return Pager
     * <AUTHOR>
     * @date 2020/12/28 14:07
     */
    default Pager getItemByAuthFilter(String sessionId, Acc4OtherSelectAuxOutItem condition, int pageNo, int size) {
        return null;
    }

    /**
     * 远程操作辅助输出
     *
     * @param type       openAuxOut/closeAuxOut/auxOutNormalOpen
     * @param opInterval 操作间隔
     * @param auxOutId   辅助输出ID
     * @return 操作结果
     */
    default ZKResultMsg operate(String type, int opInterval, String auxOutId) {
        return null;
    }
}
