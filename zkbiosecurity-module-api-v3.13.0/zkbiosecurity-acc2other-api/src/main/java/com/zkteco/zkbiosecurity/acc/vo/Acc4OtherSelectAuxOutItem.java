package com.zkteco.zkbiosecurity.acc.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;


/**
 * 辅助输出Vo
 *
 * <AUTHOR>
 * @date 2021/1/13 16:29
 * @since 1.0.0
 */
@Getter
@Setter
public class Acc4OtherSelectAuxOutItem extends BaseItem implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 辅助输出名称
     */
    private String auxName;

    /**
     * 辅助输出编号
     */
    private Short auxNo;

    /**
     * 设备别名
     */
    private String deviceAlias;

    /**
     * 设备序列号
     */
    private String deviceSn;

    /**
     * 设备Id
     */
    private String deviceId;

    /**
     * 是否启用
     */
    private boolean enable;

    /**
     * 主键
     */
    private String inId;

    /**
     * 主键
     */
    private String notInId;

    /**
     * 类别
     */
    private String type;

    /**
     * 选中的id
     */
    private String selectId;

    /**
     * 区域Id
     */
    private String authAreaIdIn;

    /**
     * 过滤
     */
    private String filterIds;
}
