package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.Acc4VisLevelItem;

public interface Acc4VisLevelService {

	/**
	 * 门禁编辑权限组时，访客权限组信息对应改动
	 * 
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @since 2018年11月29日 下午4:40:11
	 * @param acc4VisLevelItem
	 */
	void editLevelModule(Acc4VisLevelItem acc4VisLevelItem);
	
	/**
	 * 判断门禁权限组是否被使用
	 * 
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @since 2018年11月29日 下午4:40:50
	 * @param delIdList
	 * @return
	 */
	String checkLevelModuleIdIsUsed(List<String> delIdList);
	
}
