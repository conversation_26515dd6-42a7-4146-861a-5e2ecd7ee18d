package com.zkteco.zkbiosecurity.acc.vo;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 可视对讲通讯录
 *
 * <AUTHOR>
 * @date 2024/3/14 17:22
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccContact4VdbItem {
    /** 通讯录唯一id */
    private String id;
    /** 显示名称，为空时显示通讯录号码 */
    private String displayName;
    /** 通讯录号码集合 */
    private List<AccContactNumber4VdbItem> vdbNumberItemList;
    /** 头像地址 */
    private String photo;
    /** sip账号分组，可用于端侧预搜索及批拨号，取值只能为数字和字母，可以是纯数字 */
    private String group;

    /** 是否一键直呼 */
    private String directCall;

    private String passCode;

    private String pin;
}
