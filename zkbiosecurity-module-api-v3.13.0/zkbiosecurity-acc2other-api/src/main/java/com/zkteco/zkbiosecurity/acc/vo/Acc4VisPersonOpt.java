package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.Date;

public class Acc4VisPersonOpt implements Serializable{

    private static final long serialVersionUID = 1L;

    private String id;//人员id
    private String pin;//工号
    private String name;//姓名
    private String lastName;//姓
    private String personPwd;//人员密码
    private String idCard;//身份证
    private String cardNo;//卡号
    private Date startTime;//开始日期
    private Date endTime;//结束日期
    private Short superAuth;//超级用户
    private Long groupId;//多人开门组id
    private Boolean disabled;//禁用--黑名单
    private Short privilege;//设备操作员
    private Boolean delayPassage;//是否是残疾人
    private Boolean verifyStyle;//个人验证方式
    private Short cardType;//主副卡
    private Boolean tempUser;//是否临时人员
    private Short cardState;//卡状态
    private String photoPath; //照片路径
    private String bioPhotoPath; //比对照片路径


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getPersonPwd() {
        return personPwd;
    }

    public void setPersonPwd(String personPwd) {
        this.personPwd = personPwd;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Short getSuperAuth() {
        return superAuth;
    }

    public void setSuperAuth(Short superAuth) {
        this.superAuth = superAuth;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Boolean getDisabled() {
        return disabled;
    }

    public void setDisabled(Boolean disabled) {
        this.disabled = disabled;
    }

    public Short getPrivilege() {
        return privilege;
    }

    public void setPrivilege(Short privilege) {
        this.privilege = privilege;
    }

    public Boolean getVerifyStyle() {
        return verifyStyle;
    }

    public void setVerifyStyle(Boolean verifyStyle) {
        this.verifyStyle = verifyStyle;
    }

    public Short getCardType() {
        return cardType;
    }

    public void setCardType(Short cardType) {
        this.cardType = cardType;
    }

    public Short getCardState() {
        return cardState;
    }

    public void setCardState(Short cardState) {
        this.cardState = cardState;
    }

    public Boolean getDelayPassage() {
        return delayPassage;
    }

    public void setDelayPassage(Boolean delayPassage) {
        this.delayPassage = delayPassage;
    }

    public Boolean getTempUser() {
        return tempUser;
    }

    public void setTempUser(Boolean tempUser) {
        this.tempUser = tempUser;
    }

    public String getPhotoPath() {
        return photoPath;
    }

    public void setPhotoPath(String photoPath) {
        this.photoPath = photoPath;
    }

    public String getBioPhotoPath() {
        return bioPhotoPath;
    }

    public void setBioPhotoPath(String bioPhotoPath) {
        this.bioPhotoPath = bioPhotoPath;
    }
}
