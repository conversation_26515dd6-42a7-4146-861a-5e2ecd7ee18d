package com.zkteco.zkbiosecurity.acc.service;

import com.zkteco.zkbiosecurity.acc.vo.Acc4VidChannelItem;

import java.util.Collection;
import java.util.Map;

@Deprecated
public interface AccGetVidChannel2EntityService {
	
	/**
     * 获取门禁辅助输入绑定的摄像头名称(通道名称)
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月17日 上午10:40:46
     * @param entityIds 实体id
     * @return
     */
	Map<String, String> getAccAuxInBindChannelNames(Collection<String> entityIds);
	
	/**
     * 获取门禁读头绑定的摄像头名称(通道名称)
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月17日 上午10:38:01
     * @param entityIds 实体id
     * @return
     */
    Map<String, String> getAccReaderBindChannelNames(Collection<String> entityIds);
    
    /**
     * 获取摄像头节点名称
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月27日 上午9:00:56
     * @param entityId
     * @return
     */
    String getVidChannel2EntityName(String entityId);

    /**
     * 根据通道id获取通道名称
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/1/18 18:26
     * @param channelId
     * @return java.lang.String
     */
    String getVidChannelNameById(String channelId);

    /**
     * 根据通道id获取视频通道信息
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2020/7/22 15:57
     * @param channelId 通道id
     * @return com.zkteco.zkbiosecurity.acc.vo.Acc4VidChannelItem
     */
    default Acc4VidChannelItem getVidChannelById(String channelId){
        return null;
    }
}
