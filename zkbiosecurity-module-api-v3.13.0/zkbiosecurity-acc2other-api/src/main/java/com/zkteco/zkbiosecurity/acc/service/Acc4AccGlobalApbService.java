package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

public interface Acc4AccGlobalApbService {

    /**
     * 根据人员id删除全局反潜人员
     *
     * @param personList
     */
    void deleteAccPersonGlobalApbByPersPersonIdIn(List<String> personList);

    /**
     * 检测传入的读头id是否有设置过全局反潜
     *
     * @param readerIdList
     * @return
     */
    boolean existGlobalApbInReaderIds(List<String> readerIdList);
}
