package com.zkteco.zkbiosecurity.acc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.acc.vo.Acc4IntercomDeviceItem;
import com.zkteco.zkbiosecurity.acc.vo.Acc4NVRDeviceItem;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

/**
 * 门禁对接IVS模块接口
 *
 * <AUTHOR>
 * @date 2022/4/28 16:36
 */
public interface Acc4IvsDeviceService {

    /**
     * 通知视频模块添加对讲设备
     *
     * @param item:设备信息
     * @return
     * <AUTHOR>
     * @date 2022-04-28 14:36
     */
    default void addIntercomDevice(Acc4IntercomDeviceItem item) {}

    /**
     * 删除ivs中的可视对讲设备
     *
     * @param snList
     * @return
     * <AUTHOR>
     * @date 2022-04-28 18:20
     */
    default ZKResultMsg deleteDeviceBySn(List<String> snList, String type) {
        return null;
    }

    /**
     * 更新IVS设备IP地址
     *
     * @param deviceSn
     * @param ipAddress
     * @return
     * <AUTHOR>
     * @date 2022-04-28 18:20
     */
    default void updateIvsDeviceIp(String deviceSn, String ipAddress) {}

    /**
     * 门禁设备同步添加nvr设备接口
     *
     * @param acc4NVRDeviceItem:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2024-01-22 11:05
     * @since 1.0.0
     */
    default ZKResultMsg addNvrDevice(Acc4NVRDeviceItem acc4NVRDeviceItem) {
        return null;
    };

    /**
     * NVR设备账号密码校验
     * 
     * @param acc4NVRDeviceItem:
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * <AUTHOR>
     * @throws
     * @date 2024-01-22 11:11
     * @since 1.0.0
     */
    default ZKResultMsg loginCheck(Acc4NVRDeviceItem acc4NVRDeviceItem) {
        return null;
    };
}
