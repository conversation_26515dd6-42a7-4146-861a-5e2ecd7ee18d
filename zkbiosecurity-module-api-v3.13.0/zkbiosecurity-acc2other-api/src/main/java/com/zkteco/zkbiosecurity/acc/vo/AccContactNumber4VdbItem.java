package com.zkteco.zkbiosecurity.acc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 一个通讯录的号码item
 *
 * <AUTHOR>
 * @date 2024/3/14 17:32
 */
@Getter
@Setter
@Accessors(chain = true)
public class AccContactNumber4VdbItem {

    /*号码类型：1.电话号码 2.账户名称 3.IP地址或域名 */
    private Short numType;

    /*通讯录号码, 包括电话号码/账号名称/IP地址或域名 */
    private String number;

    /*拨打优先级，联系人有多个联系方式时，按优先级拨打，取值从1开始，数字越大优先级越高，默认为1 */
    private String priority;

    private String patternID;

    public AccContactNumber4VdbItem() {}

    public AccContactNumber4VdbItem(Short numType, String number, String priority) {
        this.numType = numType;
        this.number = number;
        this.priority = priority;
    }
}
