package com.zkteco.zkbiosecurity.acc.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class Acc4IVideoTransactionItem {
    /** 设备SN */
    private String devSn;
    /** 读头名称 */
    private String readerName;
    /** 人员编号 */
    private String pin;
    /** 人员卡号 */
    private String cardNo;
    /** 事件时间 */
    private Date eventTime;
    /** 区域名称 */
    private String areaName;
    /** 设备id */
    private String devId;
    /** 设备名称 */
    private String devAlias;
    /** 人员名称 */
    private String name;
    /** 人员lastName */
    private String lastName;
    /** 验证方式名称 */
    private String verifyModeName;
    /** 事件点名称 */
    private String eventPointName;
    /** 事件名称 */
    private String eventName;
    /** 部门编号 */
    private String deptCode;
    /** 部门名称 */
    private String deptName;
    private Short verifyModeNo;
    private Short eventNo;
    private Short eventPointType;
    private String eventPointId;
    private Short readerState;
    /** 体温 */
    private String temperature;
    /** 是否带口罩 0：表示没有佩戴口罩；1：表示有佩戴口罩 */
    private String maskFlag;
    /** 描述 */
    private String description;
    /** 事件等级 */
    private Integer eventLevel;
    /** 事件唯一key */
    private String uniqueKey;
}
