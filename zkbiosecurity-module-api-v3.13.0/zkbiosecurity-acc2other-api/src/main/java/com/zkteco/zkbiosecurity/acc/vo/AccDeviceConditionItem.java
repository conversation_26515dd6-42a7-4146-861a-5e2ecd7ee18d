/**
 * @author:	GenerationTools
 * @date:	2018-03-08 下午02:41
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */
package com.zkteco.zkbiosecurity.acc.vo;

import jdk.nashorn.internal.objects.annotations.Setter;

/**
 * 门禁设备vo
 * 
 * @author: yulong.dai
 * @date: 2018-03-08 下午02:41
 */

public class AccDeviceConditionItem {

	private String id;

	/**设备名称*/
	private String deviceAlias;

	/**设备型号*/
	private String deviceName;

	/**区域ID*/
	private String authAreaId;

	/**区域名称*/
	private String authAreaName;

	/**设备序列号*/
	private String deviceSn;

	private String inId;

	private String notInId;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getDeviceAlias() {
		return deviceAlias;
	}

	public void setDeviceAlias(String deviceAlias) {
		this.deviceAlias = deviceAlias;
	}

	public String getDeviceName() {
		return deviceName;
	}

	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}

	public String getAuthAreaId() {
		return authAreaId;
	}

	public void setAuthAreaId(String authAreaId) {
		this.authAreaId = authAreaId;
	}

	public String getAuthAreaName() {
		return authAreaName;
	}

	public void setAuthAreaName(String authAreaName) {
		this.authAreaName = authAreaName;
	}

	public String getDeviceSn() {
		return deviceSn;
	}

	public void setDeviceSn(String deviceSn) {
		this.deviceSn = deviceSn;
	}

	public String getInId() {
		return inId;
	}

	public void setInId(String inId) {
		this.inId = inId;
	}

	public String getNotInId() {
		return notInId;
	}

	public void setNotInId(String notInId) {
		this.notInId = notInId;
	}
}