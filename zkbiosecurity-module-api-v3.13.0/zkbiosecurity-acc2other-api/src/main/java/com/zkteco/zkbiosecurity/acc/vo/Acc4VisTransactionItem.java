package com.zkteco.zkbiosecurity.acc.vo;

import java.io.Serializable;
import java.util.Date;

public class Acc4VisTransactionItem implements Serializable {

	private static final long serialVersionUID = 1L;

	/** 主键 */
	private String id;

	/** 访客编号 */
	private String visEmpPin;
	
	/** 访客姓名 */
	private String visEmpName;
	
	/** 访客姓氏 */
	private String visEmpLastName;
	
	/** 来访事由 */
	private String visitReason;
	
	/** 被访人编号 */
	private String visitedEmpPin;
	
	/** 被访人姓名 */
	private String visitedEmpName;
	
	/** 被访人姓氏 */
	private String visitedEmpLastName;
	
	/** 来访状态 */
	private Short visitState;
	
	/** 卡号 */
	private String cardNo;
	
	/** 进入时间 */
	private Date enterTime;
	
	/** 进入地点 */
	private String enterPlace;
	
	/** 离开时间 */
	private Date exitTime;
	
	/** 离开地点 */
	private String exitPlace;
	
	/** 进入携带物品 */
	private String carriedGoodsIn;
	
	/** 离开携带物品 */
	private String carriedGoodsOut;
	
	/** 访客ID */
	private String visEmpId;

	/** 证件类型 */
	private String visEmpCertType;

	/** 证件编号 */
	private String visEmpCertNumber;

	/** 手机号码 */
	private String visEmpPhone;

	/** 被访人ID */
	private String visitedEmpId;

	/** 手机号码 */
	private String visitedEmpOfficePhone;

	/** 被访部门 */
	private String visitedEmpDept;

	/** 被访部门ID */
	private String visitedEmpDeptId;

	/** 来访人数 */
	private Integer visitorCount;

	/** 进入照片 */
	private String enterPhoto;

	/** 离开照片 */
	private String exitPhoto;

	/** 证件照片 */
	private String certPhoto;

	/** 签名照片 */
	private String signaturePhoto;

	/** 名片照片 */
	private String businessCardPhoto;

	/** 公司 */
	private String visCompany;

	/** 权限状态 */
	private Short levelState;

	/** 权限开始时间 */
	private Date validStartTime;

	/** 权限结束时间 */
	private Date validEndTime;

	/** 拜访编号 */
	private String visitNumber;

	/** 备注 */
	private String visRemark;

	/** 区域名称 */
	private String areaName;

	/** 人员密码 */
	private String personPwd;

	/** 进入携带物品照片 */
	private String enterGoodsPhoto;

	/** 离开携带物品照片 */
	private String exitGoodsPhoto;
	
    private String inId;
	
	private Date startEnterTime;
	
	private Date endEnterTime;
	
	private String createrName;
	
	private String createrCode;
	
	/** 访客头像 */
	private String headPortrait;
	
	/** 国籍 */
	private Integer nationality;
	
	/** 国籍文本 */
	private String nationalityStr;
	
	private String updaterName;
	
	private String updaterCode;



	/**
	 * @return id 主键
	 */
	public String getId()
	{
		return id;
	}

	/**
	 * @param id 要设置的 主键
	 */
	public void setId(String id)
	{
		this.id = id;
	}

	/**
	 * @return visEmpPin 
	 */
	public String getVisEmpPin()
	{
		return visEmpPin;
	}

	/**
	 * @param visEmpPin 要设置的 
	 */
	public void setVisEmpPin(String visEmpPin)
	{
		this.visEmpPin = visEmpPin;
	}

	public String getVisEmpCertType()
	{
		return visEmpCertType;
	}

	public void setVisEmpCertType(String visEmpCertType)
	{
		this.visEmpCertType = visEmpCertType;
	}

	/**
	 * @return visEmpCertNumber 
	 */
	public String getVisEmpCertNumber()
	{
		return visEmpCertNumber;
	}

	/**
	 * @param visEmpCertNumber 要设置的 
	 */
	public void setVisEmpCertNumber(String visEmpCertNumber)
	{
		this.visEmpCertNumber = visEmpCertNumber;
	}

	/**
	 * @return visEmpName 
	 */
	public String getVisEmpName()
	{
		return visEmpName;
	}

	/**
	 * @param visEmpName 要设置的 
	 */
	public void setVisEmpName(String visEmpName)
	{
		this.visEmpName = visEmpName;
	}

	/**
	 * @return visEmpLastName 
	 */
	public String getVisEmpLastName()
	{
		return visEmpLastName;
	}

	/**
	 * @param visEmpLastName 要设置的 
	 */
	public void setVisEmpLastName(String visEmpLastName)
	{
		this.visEmpLastName = visEmpLastName;
	}

	/**
	 * @return visitedEmpPin 
	 */
	public String getVisitedEmpPin()
	{
		return visitedEmpPin;
	}

	/**
	 * @param visitedEmpPin 要设置的 
	 */
	public void setVisitedEmpPin(String visitedEmpPin)
	{
		this.visitedEmpPin = visitedEmpPin;
	}

	/**
	 * @return visitedEmpName 
	 */
	public String getVisitedEmpName()
	{
		return visitedEmpName;
	}

	/**
	 * @param visitedEmpName 要设置的 
	 */
	public void setVisitedEmpName(String visitedEmpName)
	{
		this.visitedEmpName = visitedEmpName;
	}

	/**
	 * @return visitedEmpLastName 
	 */
	public String getVisitedEmpLastName()
	{
		return visitedEmpLastName;
	}

	/**
	 * @param visitedEmpLastName 要设置的 
	 */
	public void setVisitedEmpLastName(String visitedEmpLastName)
	{
		this.visitedEmpLastName = visitedEmpLastName;
	}

	/**
	 * @return visitedEmpOfficePhone 
	 */
	public String getVisitedEmpOfficePhone()
	{
		return visitedEmpOfficePhone;
	}

	/**
	 * @param visitedEmpOfficePhone 要设置的 
	 */
	public void setVisitedEmpOfficePhone(String visitedEmpOfficePhone)
	{
		this.visitedEmpOfficePhone = visitedEmpOfficePhone;
	}

	/**
	 * @return visitedEmpDept 
	 */
	public String getVisitedEmpDept()
	{
		return visitedEmpDept;
	}

	/**
	 * @param visitedEmpDept 要设置的 
	 */
	public void setVisitedEmpDept(String visitedEmpDept)
	{
		this.visitedEmpDept = visitedEmpDept;
	}

	/**
	 * @return visitorCount 
	 */
	public Integer getVisitorCount()
	{
		return visitorCount;
	}

	/**
	 * @param visitorCount 要设置的 
	 */
	public void setVisitorCount(Integer visitorCount)
	{
		this.visitorCount = visitorCount;
	}

	/**
	 * @return enterPhoto 
	 */
	public String getEnterPhoto()
	{
		return enterPhoto;
	}

	/**
	 * @param enterPhoto 要设置的 
	 */
	public void setEnterPhoto(String enterPhoto)
	{
		this.enterPhoto = enterPhoto;
	}

	/**
	 * @return exitPhoto 
	 */
	public String getExitPhoto()
	{
		return exitPhoto;
	}

	/**
	 * @param exitPhoto 要设置的 
	 */
	public void setExitPhoto(String exitPhoto)
	{
		this.exitPhoto = exitPhoto;
	}

	/**
	 * @return certPhoto 
	 */
	public String getCertPhoto()
	{
		return certPhoto;
	}

	/**
	 * @param certPhoto 要设置的 
	 */
	public void setCertPhoto(String certPhoto)
	{
		this.certPhoto = certPhoto;
	}

	/**
	 * @return signaturePhoto 
	 */
	public String getSignaturePhoto()
	{
		return signaturePhoto;
	}

	/**
	 * @param signaturePhoto 要设置的 
	 */
	public void setSignaturePhoto(String signaturePhoto)
	{
		this.signaturePhoto = signaturePhoto;
	}

	/**
	 * @return businessCardPhoto 
	 */
	public String getBusinessCardPhoto()
	{
		return businessCardPhoto;
	}

	/**
	 * @param businessCardPhoto 要设置的 
	 */
	public void setBusinessCardPhoto(String businessCardPhoto)
	{
		this.businessCardPhoto = businessCardPhoto;
	}

	/**
	 * @return visCompany 
	 */
	public String getVisCompany()
	{
		return visCompany;
	}

	/**
	 * @param visCompany 要设置的 
	 */
	public void setVisCompany(String visCompany)
	{
		this.visCompany = visCompany;
	}

	/**
	 * @return visitReason 
	 */
	public String getVisitReason()
	{
		return visitReason;
	}

	/**
	 * @param visitReason 要设置的 
	 */
	public void setVisitReason(String visitReason)
	{
		this.visitReason = visitReason;
	}

	/**
	 * @return visitState 
	 */
	public Short getVisitState()
	{
		return visitState;
	}

	/**
	 * @param visitState 要设置的 
	 */
	public void setVisitState(Short visitState)
	{
		this.visitState = visitState;
	}

	/**
	 * @return levelState 
	 */
	public Short getLevelState()
	{
		return levelState;
	}

	/**
	 * @param levelState 要设置的 
	 */
	public void setLevelState(Short levelState)
	{
		this.levelState = levelState;
	}

	/**
	 * @return enterTime 
	 */
	public Date getEnterTime()
	{
		return enterTime;
	}

	/**
	 * @param enterTime 要设置的 
	 */
	public void setEnterTime(Date enterTime)
	{
		this.enterTime = enterTime;
	}

	/**
	 * @return enterPlace 
	 */
	public String getEnterPlace()
	{
		return enterPlace;
	}

	/**
	 * @param enterPlace 要设置的 
	 */
	public void setEnterPlace(String enterPlace)
	{
		this.enterPlace = enterPlace;
	}

	/**
	 * @return exitTime 
	 */
	public Date getExitTime()
	{
		return exitTime;
	}

	/**
	 * @param exitTime 要设置的 
	 */
	public void setExitTime(Date exitTime)
	{
		this.exitTime = exitTime;
	}

	/**
	 * @return exitPlace 
	 */
	public String getExitPlace()
	{
		return exitPlace;
	}

	/**
	 * @param exitPlace 要设置的 
	 */
	public void setExitPlace(String exitPlace)
	{
		this.exitPlace = exitPlace;
	}

	/**
	 * @return carriedGoodsIn 
	 */
	public String getCarriedGoodsIn()
	{
		return carriedGoodsIn;
	}

	/**
	 * @param carriedGoodsIn 要设置的 
	 */
	public void setCarriedGoodsIn(String carriedGoodsIn)
	{
		this.carriedGoodsIn = carriedGoodsIn;
	}

	/**
	 * @return carriedGoodsOut 
	 */
	public String getCarriedGoodsOut()
	{
		return carriedGoodsOut;
	}

	/**
	 * @param carriedGoodsOut 要设置的 
	 */
	public void setCarriedGoodsOut(String carriedGoodsOut)
	{
		this.carriedGoodsOut = carriedGoodsOut;
	}

	/**
	 * @return validStartTime 
	 */
	public Date getValidStartTime()
	{
		return validStartTime;
	}

	/**
	 * @param validStartTime 要设置的 
	 */
	public void setValidStartTime(Date validStartTime)
	{
		this.validStartTime = validStartTime;
	}

	/**
	 * @return validEndTime 
	 */
	public Date getValidEndTime()
	{
		return validEndTime;
	}

	/**
	 * @param validEndTime 要设置的 
	 */
	public void setValidEndTime(Date validEndTime)
	{
		this.validEndTime = validEndTime;
	}

	/**
	 * @return visitNumber 
	 */
	public String getVisitNumber()
	{
		return visitNumber;
	}

	/**
	 * @param visitNumber 要设置的 
	 */
	public void setVisitNumber(String visitNumber)
	{
		this.visitNumber = visitNumber;
	}

	/**
	 * @return cardNo 
	 */
	public String getCardNo()
	{
		return cardNo;
	}

	/**
	 * @param cardNo 要设置的 
	 */
	public void setCardNo(String cardNo)
	{
		this.cardNo = cardNo;
	}

	/**
	 * @return visRemark 
	 */
	public String getVisRemark()
	{
		return visRemark;
	}

	/**
	 * @param visRemark 要设置的 
	 */
	public void setVisRemark(String visRemark)
	{
		this.visRemark = visRemark;
	}

	/**
	 * @return areaName 
	 */
	public String getAreaName()
	{
		return areaName;
	}

	/**
	 * @param areaName 要设置的 
	 */
	public void setAreaName(String areaName)
	{
		this.areaName = areaName;
	}

	/**
	 * @return personPwd 人员密码
	 */
	public String getPersonPwd()
	{
		return personPwd;
	}

	/**
	 * @param personPwd 要设置的 人员密码
	 */
	public void setPersonPwd(String personPwd)
	{
		this.personPwd = personPwd;
	}

	/**
	 * @return enterGoodsPhoto 
	 */
	public String getEnterGoodsPhoto()
	{
		return enterGoodsPhoto;
	}

	/**
	 * @param enterGoodsPhoto 要设置的 
	 */
	public void setEnterGoodsPhoto(String enterGoodsPhoto)
	{
		this.enterGoodsPhoto = enterGoodsPhoto;
	}

	/**
	 * @return exitGoodsPhoto 
	 */
	public String getExitGoodsPhoto()
	{
		return exitGoodsPhoto;
	}

	/**
	 * @param exitGoodsPhoto 要设置的 
	 */
	public void setExitGoodsPhoto(String exitGoodsPhoto)
	{
		this.exitGoodsPhoto = exitGoodsPhoto;
	}

	public String getVisEmpId()
	{
		return visEmpId;
	}

	public void setVisEmpId(String visEmpId)
	{
		this.visEmpId = visEmpId;
	}

	public String getVisitedEmpId()
	{
		return visitedEmpId;
	}

	public void setVisitedEmpId(String visitedEmpId)
	{
		this.visitedEmpId = visitedEmpId;
	}

	public String getVisitedEmpDeptId()
	{
		return visitedEmpDeptId;
	}

	public void setVisitedEmpDeptId(String visitedEmpDeptId)
	{
		this.visitedEmpDeptId = visitedEmpDeptId;
	}

	public String getInId()
	{
		return inId;
	}

	public void setInId(String inId)
	{
		this.inId = inId;
	}

	public Date getStartEnterTime()
	{
		return startEnterTime;
	}

	public void setStartEnterTime(Date startEnterTime)
	{
		this.startEnterTime = startEnterTime;
	}

	public Date getEndEnterTime()
	{
		return endEnterTime;
	}

	public void setEndEnterTime(Date endEnterTime)
	{
		this.endEnterTime = endEnterTime;
	}

	public String getCreaterName()
	{
		return createrName;
	}

	public void setCreaterName(String createrName)
	{
		this.createrName = createrName;
	}

	public String getHeadPortrait()
	{
		return headPortrait;
	}

	public void setHeadPortrait(String headPortrait)
	{
		this.headPortrait = headPortrait;
	}

	public Integer getNationality()
	{
		return nationality;
	}

	public void setNationality(Integer nationality)
	{
		this.nationality = nationality;
	}

	public String getNationalityStr()
	{
		return nationalityStr;
	}

	public void setNationalityStr(String nationalityStr)
	{
		this.nationalityStr = nationalityStr;
	}

	public String getCreaterCode()
	{
		return createrCode;
	}

	public void setCreaterCode(String createrCode)
	{
		this.createrCode = createrCode;
	}

	public String getUpdaterName()
	{
		return updaterName;
	}

	public void setUpdaterName(String updaterName)
	{
		this.updaterName = updaterName;
	}

	public String getUpdaterCode()
	{
		return updaterCode;
	}

	public void setUpdaterCode(String updaterCode)
	{
		this.updaterCode = updaterCode;
	}

	public String getVisEmpPhone() {
		return visEmpPhone;
	}

	public void setVisEmpPhone(String visEmpPhone) {
		this.visEmpPhone = visEmpPhone;
	}

}
