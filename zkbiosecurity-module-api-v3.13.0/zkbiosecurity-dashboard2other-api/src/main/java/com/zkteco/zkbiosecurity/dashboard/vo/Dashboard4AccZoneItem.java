package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4AccZoneItem 门禁区域监控
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4AccZoneItem implements Serializable {

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 人数
     */
    private String number;
}
