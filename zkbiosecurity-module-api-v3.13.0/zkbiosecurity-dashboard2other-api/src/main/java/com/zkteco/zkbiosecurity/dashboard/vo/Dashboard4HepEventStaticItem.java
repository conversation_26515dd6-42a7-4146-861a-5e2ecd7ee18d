package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * 防疫看板-事件统计相关VO
 *
 * <AUTHOR>
 * @date 2021-08-06 11:21
 */
@Getter
@Setter
public class Dashboard4HepEventStaticItem {
    /**
     * 时间点 本月事件统计： 1号 - 30号
     */
    private String time;

    /**
     * 事件数
     */
    private String number;

    /**
     * 未测量事件数
     */
    private String unmeasuredCount;

    /**
     * 体温异常事件数
     */
    private String abnormalCount;

    /**
     * 体温正常事件数
     */
    private String normalCount;
}
