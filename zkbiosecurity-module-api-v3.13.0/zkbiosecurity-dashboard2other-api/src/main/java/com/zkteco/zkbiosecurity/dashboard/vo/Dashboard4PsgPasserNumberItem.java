package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/8/3 18:44
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4PsgPasserNumberItem {

    /** 总人数 */
    private long personCount;

    /** 进入人数 */
    private long personInCount;

    /** 出去人数 */
    private long personOutCount;
}
