package com.zkteco.zkbiosecurity.dashboard.service;


import com.zkteco.zkbiosecurity.dashboard.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 考勤dashboard相关操作
 */
public interface Dashboard4AttService {

	/**
	 *模块设备总数
	 * <AUTHOR>
	 * @Date 2018/12/28 10:12
	 * @param
	 * @return
	 */
	long getDeviceCount();

	/**
	 *考勤工作狂人
	 * 统计人员工作时长前10名
	 * <AUTHOR>
	 * @Date 2018/12/26 14:30
	 * @param timeType 判断是统计昨日、上周、上月：day week month
	 * @return
	 */
	List<Dashboard4AttWorkaholicItem> getAttWorkaholicData(String timeType);

	/**
	 *本日分段考勤
	 * 统计今日各时间段打卡数分布
	 * number字段后面不带百分比
	 * 时间段（time字段）
	 * "00:00~08:00", "08:00~12:00", "12:00~14:00", "14:00~18:00", "18:00~23:59"
	 * <AUTHOR>
	 * @Date 2018/12/26 14:30
	 * @param
	 * @return
	 */
	Map<String, String> getAttTodayCountData();

	/**
	 *今日考勤
	 * 应过滤出今日有排班的人员
	 * <AUTHOR>
	 * @Date 2018/12/26 14:30
	 * @param
	 * @return
	 */
	Dashboard4AttTodayItem getAttTodayData();

	/**
	 *异常统计(本月)
	 * 迟到 早退 请假 旷工 外出 出差等数量
	 * <AUTHOR>
	 * @Date 2018/12/26 14:30
	 * @param
	 * @return
	 */
	List<Dashboard4AttExceptionItem> getAttExceptionCountData();

	/**
	 * 考勤设备
	 *
	 * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
	 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
	 * @date 2021/8/3 13:37
	 * @since 1.0.0
	 */
	default Dashboard4AttDeviceItem getAttDeviceData() { return null; }

	/**
	 * 部门出勤率TOP5
	 *
	 * @param timeType:
	 * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
	 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
	 * @date 2021/8/3 14:02
	 * @since 1.0.0
	 */
	default List<Dashboard4AttAttendanceItem> getAttDeptAttendanceRateData(String timeType) { return null; }

	/**
	 * 缺勤率TOP5
	 *
	 * @param timeType:
	 * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
	 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
	 * @date 2021/8/3 14:02
	 * @since 1.0.0
	 */
	default List<Dashboard4AttAbnormalItem> getAttDeptAbsentRateChartData(String timeType) { return null; }

	/**
	 * 本月考勤记录趋势
	 *
	 * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
	 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
	 * @date 2021/8/3 14:03
	 * @since 1.0.0
	 */
	default List<Dashboard4AttRecordTrendItem> getAttRecordTrendChartData() { return null; }
}
