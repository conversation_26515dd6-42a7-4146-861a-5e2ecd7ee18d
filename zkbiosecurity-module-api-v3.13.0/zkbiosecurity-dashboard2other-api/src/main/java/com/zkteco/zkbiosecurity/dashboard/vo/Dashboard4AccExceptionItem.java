package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4AccExceptionItem 门禁异常事件
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4AccExceptionItem implements Serializable {

    /**
     * 事件名称
     */
    private String exceptionEventName;

    /**
     * 事件数
     */
    private String number;
}
