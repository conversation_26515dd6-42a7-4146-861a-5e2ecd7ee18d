package com.zkteco.zkbiosecurity.dashboard.service;


import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4ParkRecordinItem;
import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4ParkRevenueItem;

import java.util.List;
import java.util.Map;

/**
 * 停车dashboard相关操作
 */
public interface Dashboard4ParkService {

    /**
     *模块设备总数
     * <AUTHOR>
     * @Date 2018/12/28 10:12
     * @param
     * @return
     */
    long getDeviceCount();

    /**
     *停车场营收额
     * <AUTHOR>
     * @Date 2018/12/28 10:45
     * @param   timeType 判断是统计今日还是本月： 值day month
     * @return
     */
    Map<String, Object> getParkRevenueData(String timeType);

    /**
     *在场车辆
     * <AUTHOR>
     * @Date 2018/12/28 10:45
     * @param
     * @return
     */
    List<Dashboard4ParkRecordinItem> getParkRecordinData();
}
