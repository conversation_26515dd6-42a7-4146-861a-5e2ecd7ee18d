package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2021/8/3 13:42
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class DashboardDeviceCountItem {

    /** 设备总数 */
    private long deviceCount;

    /** 在线设备数量 */
    private long onlineDeviceCount;

    /** 离线设备数量 */
    private long offlineDeviceCount;
}
