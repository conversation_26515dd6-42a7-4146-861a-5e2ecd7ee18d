package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 今日访问量
 *
 * <AUTHOR>
 * @DATE 2021-08-02 18:23
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4VisVisitCountItem {

    /**
     * 已签离数量
     */
    private Integer stateOutCount;

    /**
     * 未签离数量
     */
    private Integer stateNotOutCount;

    /**
     * 总数量
     */
    private Integer stateAllCount;

    public Dashboard4VisVisitCountItem() {
        super();
    }

    public Dashboard4VisVisitCountItem(Integer stateOutCount, Integer stateNotOutCount, Integer stateAllCount) {
        super();
        this.stateOutCount = stateOutCount;
        this.stateNotOutCount = stateNotOutCount;
        this.stateAllCount = stateAllCount;
    }

}
