package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4AttTodayItem 今日考勤
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4AttTodayItem implements Serializable {

    /**
     * 今日有排班人员未到数
     */
    private String notArrivedNumber;

    /**
     * 今日有排班人员实到数
     */
    private String actualNumber;

    /**
     * 今日有排班人员总数
     */
    private String perpsonNumber;
}
