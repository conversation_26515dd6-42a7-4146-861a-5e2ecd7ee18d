package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Dashboard4IvsEventTrendsItem 视频事件趋势
 * 
 * <AUTHOR>
 * @date 2025-01-06 15:50
 * @param 
 * @return 
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4IvsDealEventItem implements Serializable {

    /**
     * 总数
     */
    private long totalCount;

    /**
     * 已处理事件数
     */
    private long processedCount;
    
    /**
     * 未处理事件数
     */
    private long unProcessedCount;
    
}
