package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *
 * 今日访客列表
 * 
 * <AUTHOR>
 * @DATE 2021-08-03 18:20
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class Dashboard4VisTodayVisitListItem extends BaseItem implements Serializable {

    /** 主键 */
    private String id;

    /** 访客姓名 */
    private String visitorName;

    /** 访客姓氏 */
    private String visitorLastName;

    /** 被访人姓名 */
    private String visitEmpName;

    /** 被访人姓氏 */
    private String visitEmpLastName;

    /** 拜访部门 */
    private String visitEmpDeptName;

    /** 来访日期 */
    private String visitDate;

    /** 来访状态 */
    private String visitState;

}
