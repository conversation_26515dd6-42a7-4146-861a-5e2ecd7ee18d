package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 今日消费
 *
 * <AUTHOR>
 * @date 2021-8-5 13:33:52
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4PosTodayDataItem {

    /**
     * 统计图表-名称
     */
    private String name;

    /**
     * 统计图标-值
     */
    private String value;

    public Dashboard4PosTodayDataItem() {}

    public Dashboard4PosTodayDataItem(String name, String value) {
        this.name = name;
        this.value = value;
    }
}
