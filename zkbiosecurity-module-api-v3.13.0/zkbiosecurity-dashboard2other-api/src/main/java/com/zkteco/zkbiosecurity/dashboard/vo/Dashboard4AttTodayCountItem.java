package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *Dashboard4AttTodayCountItem 本日考勤分段统计
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Setter
@Getter
@Accessors(chain = true)
public class Dashboard4AttTodayCountItem {

	/**
     * 时间段
     * "00:00~08:00", "08:00~12:00", "12:00~14:00", "14:00~18:00", "18:00~23:59"
     */
    private String time;

    /**
     * 百分比(后面不带百分比)
     */
    private String number;
}
