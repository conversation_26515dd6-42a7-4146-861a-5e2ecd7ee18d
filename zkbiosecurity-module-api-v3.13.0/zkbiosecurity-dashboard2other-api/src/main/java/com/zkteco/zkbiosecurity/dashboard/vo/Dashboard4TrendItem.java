package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 通用趋势
 *
 * <AUTHOR>
 * @date 2021-8-5 18:31:10
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4TrendItem {
    /**
     * 百分比
     */
    private String percentage;

    /**
     * 时间
     */
    private String time;

    public Dashboard4TrendItem() {}

    public Dashboard4TrendItem(String percentage, String time) {
        this.percentage = percentage;
        this.time = time;
    }
}
