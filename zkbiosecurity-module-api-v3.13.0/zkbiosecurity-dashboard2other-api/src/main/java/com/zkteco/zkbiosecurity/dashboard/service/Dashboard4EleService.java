package com.zkteco.zkbiosecurity.dashboard.service;

import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4TrasactionItem;
import com.zkteco.zkbiosecurity.dashboard.vo.DashboardEventCountItem;

import java.util.List;

/**
 * 梯控dashboard
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-12-27 上午09:38
 */
public interface Dashboard4EleService {

	/**
	 *模块设备总数
	 * <AUTHOR>
	 * @Date 2018/12/28 10:12
	 * @param
	 * @return
	 */
	long getDeviceCount();

	/**
	 *获取梯控事件数
	 * <AUTHOR>
	 * @Date 2019/1/3 14:47
	 * @param
	 * @return
	 */
	DashboardEventCountItem getEventCount();

	/**
	 *获取梯控最近发生的前三条记录
	 * <AUTHOR>
	 * @Date 2019/1/3 14:58
	 * @param
	 * @return
	 */
	List<Dashboard4TrasactionItem> getTransactionsLast();
}
