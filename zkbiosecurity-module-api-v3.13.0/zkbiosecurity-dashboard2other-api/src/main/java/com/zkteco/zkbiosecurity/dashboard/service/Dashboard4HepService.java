package com.zkteco.zkbiosecurity.dashboard.service;

import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4HepEventStaticItem;
import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4HepTransactionItem;

/**
 * 防疫看板接口类
 *
 * <AUTHOR>
 * @date 2021-08-04 11:32
 */
public interface Dashboard4HepService {

    /**
     * 获取防疫模块面板数据
     * 
     * @param timeType:
     * @return java.util.Map<java.lang.String,java.lang.Object>
     * <AUTHOR>
     * @throws @date 2021-08-04 15:07
     * @since 1.0.0
     */
    default Map<String, Object> getHepDashboardData(String timeType) {
        return null;
    }

    /**
     * 根据状态和时间获取面板列表数据
     * 
     * @param state: 正常（0）；异常（1）；未测量（2）
     * @param timeType: 日月周-0、1、2
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4HepTransactionItem>
     * <AUTHOR>
     * @throws @date 2021-08-06 10:59
     * @since 1.0.0
     */
    default List<Dashboard4HepTransactionItem> getHepDashboardGridData(String state, String timeType) {
        return null;
    }

    /**
     * 根据选择时间获取防疫模块事件统计报表数据
     * 
     * @param date:
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4HepEventStaticItem>
     * <AUTHOR>
     * @throws @date 2021-08-06 11:34
     * @since 1.0.0
     */
    default List<Dashboard4HepEventStaticItem> getHepEventStaticData(String date) {
        return null;
    }

    /**
     * 分页获取面板数据
     * 
     * @param state:
     * @param timeType:
     * @param pageNo:
     * @param pageSize:
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4HepTransactionItem>
     * <AUTHOR>
     * @throws
     * @date 2022-07-12 14:05
     * @since 1.0.0
     */
    default List<Dashboard4HepTransactionItem> getHepDashboardGridPageData(String state, String timeType, int pageNo,
        int pageSize) {
        return null;
    }
}
