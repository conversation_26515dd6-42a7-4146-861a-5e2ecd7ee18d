package com.zkteco.zkbiosecurity.dashboard.service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.dashboard.vo.*;

import java.util.List;

/**
 * 门禁dashboard
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-12-27 上午09:38
 */
public interface Dashboard4AccService {

	/**
	 *模块设备总数
	 * <AUTHOR>
	 * @Date 2018/12/28 10:12
	 * @param
	 * @return
	 */
	Long getDeviceCount();

	/**
	 *门禁门禁事件趋势 今日\本周\本月
	 * 时间点（time字段）
	 * 今日事件趋势： 0:00 - 23:00
	 * OR本周事件趋势： "星期一", "星期二", "星期三", "星期四", "星期五","星期六", "星期日"
	 * OR本月事件趋势： 1号 - 30号
	 * <AUTHOR>
	 * @Date 2018/12/26 14:30
	 * @param timeType 判断是统计今日、本周、本月: day week month
	 * @return
	 */
	List<Dashboard4AccEventTrendsItem> getAccEventTrendsData(String timeType);

	/**
	 *门禁异常事件TOP5(本月)
	 * 统计所有历史数据的前5种异常事件条数
	 * 及总异常数，总异常数item的exceptionEventName传"total"
	 * <AUTHOR>
	 * @Date 2018/12/26 14:30
	 * @param
	 * @return
	 */
	List<Dashboard4AccExceptionItem> getAccExceptionTopData();

	/**
	 *获取门禁事件数
	 * <AUTHOR>
	 * @Date 2019/1/3 14:47
	 * @param
	 * @return
	 */
	DashboardEventCountItem getEventCount();

	/**
	 *获取门禁最近发生的前三条记录
	 * <AUTHOR>
	 * @Date 2019/1/3 14:58
	 * @param
	 * @return
	 */
	List<Dashboard4TrasactionItem> getTransactionsLast();

	/**
	 * 统计设备在线、离线及总数
	 *
	 * @return com.zkteco.zkbiosecurity.dashboard.vo.DashboardDeviceCountItem
	 * <AUTHOR>
	 * @date 2021-08-03 13:57
	 * @since 1.0.0
	 */
	DashboardDeviceCountItem getDeviceCountItem();

	/**
	 * 告警数据 4种类型告警的数量
	 *
	 * @return com.alibaba.fastjson.JSONObject
	 * <AUTHOR>
	 * @date 2024/3/7 11:34
	 * @since 1.0.0
	 */
	JSONObject getAccAlarmData(String sessionId);
}
