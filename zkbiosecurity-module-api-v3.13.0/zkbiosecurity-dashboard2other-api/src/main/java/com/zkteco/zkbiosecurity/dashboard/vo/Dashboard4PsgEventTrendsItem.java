package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4PsgEventTrendsItem 通道事件趋势
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4PsgEventTrendsItem implements Serializable {

    /**
     * 时间点 今日事件趋势： 0:00 - 23:00 OR本周事件趋势： "星期一", "星期二", "星期三", "星期四", "星期五","星期六", "星期日" OR本月事件趋势： 1号 - 30号
     */
    private String time;

    /**
     * 事件数
     */
    private String number;
}
