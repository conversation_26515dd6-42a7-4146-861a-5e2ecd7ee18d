
/**
 * File Name: DashboardDataItem Created by xjing.huang on 2018-12-25 11:41:11 Copyright:Copyright © 1985-2018 ZKTeco
 * Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 DashboardDataItem
 * 
 * <AUTHOR>
 * @date: 2018-12-25
 * @version v1.0
 */

@Setter
@Getter
@Accessors(chain = true)
@ToString
public class DashboardDataItem extends BaseItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 人员总数 */
    private String perCount;

    /** 设备总数 */
    private Integer deviceCount;

    /** 生物模板-发卡 key：类型 value：数量 */
    private Map<String, String> cardData;

    /** 生物模板-密码 key：类型 value：数量 */
    private Map<String, String> pswData;

    /** 生物模板-指纹 key：类型 value：数量 */
    private Map<String, String> fpData;

    /** 生物模板-面部 key：类型 value：数量 */
    private Map<String, String> faceData;

    /** 生物模板-指静脉 key：类型 value：数量 */
    private Map<String, String> fvData;

    /** 生物模板-掌纹 key：类型 value：数量 */
    private Map<String, String> palmData;

    /** 生物模板-可见光手掌 key：类型 value：数量 */
    private Map<String, String> visiblePlamData;

    /** 生物模板-可见光模人脸 key：类型 value：数量 */
    private Map<String, String> vislightData;

    /** 生物模板-比对照片 key：类型 value：数量 */
    private Map<String, String> biophotoData;

    /** 人员照片 key：类型 value：数量 */
    private Map<String, String> portraitPhotoData;

    /** 门禁-事件趋势 key：时间 value：事件数 */
    private List<Dashboard4AccEventTrendsItem> accEventTrendsData;

    /** 门禁-异常事件TOP5 key：事件名称 value：事件数 */
    private List<Dashboard4AccExceptionItem> accExceptionTopData;

    /** 门禁-区域监控 key：区域名称 value：人数 */
    private List<Dashboard4AccZoneItem> accZoneData;

    /** 考勤-工作狂人 key：姓名 value 工作时长 */
    private List<Dashboard4AttWorkaholicItem> attWorkaholicData;

    /** 考勤-本日考勤分段统计 key：时间段（00:00~08:00", "08:00~12:00", "12:00~14:00", "14:00~18:00", "18:00~23:59"） value 考勤人数 */
    private Map<String, String> attTodayCountData;

    /** 考勤-今日考勤 key：未到（notArrivedNumber）实到（actualNumber） value 人数 */
    private Dashboard4AttTodayItem attTodayData;

    /** 考勤-异常统计（本月） key："迟到", "早退", "请假", "出差", "外出", "旷工" value 人数 */
    private List<Dashboard4AttExceptionItem> attExceptionCountData;

    /** 访客-当日来访流量 key：时间 value 人数 */
    private List<Dashboard4VisDayTripsItem> visDayTripsData;

    /** 访客-最近12个月来访量 key：月份 value 人数 */
    private List<Dashboard4VisitorTrendsItem> visVisitorTrendsData;

    /** 访客-登记点流量 key：登记点 value 人数 */
    private List<Dashboard4VisRegistPointFlowItem> visRegistPointFlowData;

    /** 访客-被访人TOP5 key：被访人 value 人数 */
    private List<Dashboard4VisitedListItem> visVisitedTopData;

    /** 停车-营收情况 */
    private Map<String, Object> parkRevenueData;

    /** 停车-场内车辆 */
    private List<Dashboard4ParkRecordinItem> parkRecordinData;

    /** 通道-事件趋势 key：时间 value：事件数 */
    private List<Dashboard4PsgEventTrendsItem> psgEventTrendsData;

    /** 通道-异常事件TOP5 key：事件名称 value：事件数 */
    private List<Dashboard4PsgExceptionItem> psgExceptionTopData;

    /** 通道-报警监控 key：区域名称 value：人数 */
    private List<Dashboard4PsgAlarmItem> psgEventAlarmData;

    /** 实时事件数 */
    private DashboardEventCountItem eventCountItem;

    /** 最新实时事件 */
    private List<Dashboard4TrasactionItem> trasactionItems;

    public DashboardDataItem() {
        super();
    }

}