package com.zkteco.zkbiosecurity.dashboard.service;

import com.zkteco.zkbiosecurity.dashboard.vo.*;

import java.util.List;

public interface Dashboard4PsgService {

	/**
	 * 通道模块设备数量
	 *
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/5/27 16:49
	 * @param
	 * @return java.lang.Long
	 */
	Long getDeviceCount();

	/**
	 * 通道事件趋势 今日\本周\本月
	 * 时间点（time字段）
	 * 今日事件趋势： 0:00 - 23:00
	 * OR本周事件趋势： "星期一", "星期二", "星期三", "星期四", "星期五","星期六", "星期日"
	 * OR本月事件趋势： 1号 - 30号
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/5/27 16:51
	 * @param timeType 判断是统计今日、本周、本月: day week month
	 * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PsgEventTrendsItem>
	 */
	List<Dashboard4PsgEventTrendsItem> getPsgEventTrendsData(String timeType);

	/**
	 * 通道异常事件TOP5(本月)
	 * 统计所有历史数据的前5种异常事件条数
	 * 及总异常数，总异常数item的exceptionEventName传"total"
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/5/27 16:55
	 * @param
	 * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PsgExceptionItem>
	 */
	List<Dashboard4PsgExceptionItem> getPsgExceptionTopData();

	/**
	 * 获取通道事件数
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/5/27 16:57
	 * @param
	 * @return com.zkteco.zkbiosecurity.dashboard.vo.DashboardEventCountItem
	 */
	DashboardEventCountItem getEventCount();

	/**
	 * 获取通道最近发生的前三条记录
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/5/27 16:59
	 * @param
	 * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4TrasactionItem>
	 */
	List<Dashboard4TrasactionItem> getTransactionsLast();

	/**
	 * 获取通道报警监控数据
	 *
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/5/27 17:26
	 * @param psgAreaName
	 * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PsgAlarmItem>
	 */
	List<Dashboard4PsgAlarmItem> getPsgEventAlarmData(String psgAreaName);

	/**
	 * 统计通道设备在线、离线及总数
	 *
	 * @return com.zkteco.zkbiosecurity.dashboard.vo.DashboardDeviceCountItem
	 * <AUTHOR>
	 * @date 2021-08-03 16:08
	 * @since 1.0.0
	 */
    DashboardDeviceCountItem getDeviceCountItem();

    /**
     * 获取通道闸机通过人数(总人数、进/出人数)
     *
     * @return com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PsgPasserNumberItem
     * <AUTHOR>
     * @date 2021-08-03 18:53
     * @since 1.0.0
     */
	Dashboard4PsgPasserNumberItem getPasserNumber();
}
