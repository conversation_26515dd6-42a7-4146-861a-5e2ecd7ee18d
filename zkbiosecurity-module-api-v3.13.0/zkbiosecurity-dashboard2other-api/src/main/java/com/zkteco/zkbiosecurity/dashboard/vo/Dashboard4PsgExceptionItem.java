package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4PsgExceptionItem 通道异常事件
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4PsgExceptionItem implements Serializable {

    /**
     * 事件名称
     */
    private String exceptionEventName;

    /**
     * 事件数
     */
    private String number;
}
