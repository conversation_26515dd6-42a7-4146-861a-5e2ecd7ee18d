package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;
import java.util.Map;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 入职、离职人员趋势
 * 
 * <AUTHOR>
 * @date 2021/8/5 15:34
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
@ToString
public class Dashboard4PersPersonTrendsItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 入职人员 key：月份 value：数量 */
    private Map<String, String> personData;

    /** 离职人员 key：月份 value：数量 */
    private Map<String, String> leavePersonData;

}
