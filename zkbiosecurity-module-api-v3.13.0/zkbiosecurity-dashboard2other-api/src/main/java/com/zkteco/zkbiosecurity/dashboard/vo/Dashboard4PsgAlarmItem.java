package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 通道报警监控事件
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4PsgAlarmItem implements Serializable {

    /**
     * 区域名称
     */
    private String zoneName;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 事件时间
     */
    private Date eventTime;

    /**
     * 事件描述
     */
    private String eventName;
}
