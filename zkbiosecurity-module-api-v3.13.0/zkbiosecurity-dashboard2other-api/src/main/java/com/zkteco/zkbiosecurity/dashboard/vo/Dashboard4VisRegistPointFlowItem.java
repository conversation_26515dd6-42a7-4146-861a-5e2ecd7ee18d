package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4VisRegistPointFlowItem 登记点流量统计图
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4VisRegistPointFlowItem implements Serializable {

    /**
     * 登记点名称
     */
    private String registPoint;

    /**
     * 访问数
     */
    private String number;
}
