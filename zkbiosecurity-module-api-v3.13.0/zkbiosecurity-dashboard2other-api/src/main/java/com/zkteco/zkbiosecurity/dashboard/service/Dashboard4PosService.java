package com.zkteco.zkbiosecurity.dashboard.service;

import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4AttRecordTrendItem;
import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PosTodayDataItem;
import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4TrendItem;
import com.zkteco.zkbiosecurity.dashboard.vo.DashboardDeviceCountItem;

import java.util.List;

/**
 * 消费dashboard
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-12-27 上午09:38
 */
public interface Dashboard4PosService {

	/**
	 *模块设备总数
	 * <AUTHOR>
	 * @Date 2018/12/28 10:12
	 * @param
	 * @return
	 */
	long getDeviceCount();

	/**
	 * 获取今日消费金额数据
	 * @param dataType
	 * <AUTHOR>
	 * @date 2021-8-5 13:42:39
	 * @return
	 */
	List<Dashboard4PosTodayDataItem> getPosTodayData(String dataType);

	/**
	 * 获取消费设备数据
	 * <AUTHOR>
	 * @date 2021-8-5 18:10:58
	 * @return
	 */
	DashboardDeviceCountItem getPosDeviceData();

	/**
	 * 本月消费趋势
	 *
	 * @return
	 * <AUTHOR>
	 * @date 2021-8-5 18:33:41
	 */
	List<Dashboard4TrendItem> getPosTrendChartData();
}
