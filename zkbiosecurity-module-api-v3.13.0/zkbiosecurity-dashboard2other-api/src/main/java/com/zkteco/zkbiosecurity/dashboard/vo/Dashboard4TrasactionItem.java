
/**
 * File Name: Dashboard4TrasactionItem
 * Created by xjin<PERSON>.huang on 2018-12-25 11:41:11
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.dashboard.vo;

import com.zkteco.zkbiosecurity.base.annotation.Column;
import com.zkteco.zkbiosecurity.base.annotation.GridColumn;
import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 对应百傲瑞达实体 Dashboard4TrasactionItem
 * <AUTHOR>
 * @date:	2018-12-25
 * @version v1.0
 */

@Setter
@Getter
@Accessors(chain=true)
@ToString
public class Dashboard4TrasactionItem extends BaseItem implements Serializable{
	private static final long serialVersionUID = 1L;

	/** 主键 */
	private String id;

	/**设备名称*/
	private String devAlias;

	/**事件点名称*/
	private String eventPointName;

	/**事件时间 */
	private String eventTime;

	/**事件名称*/
	private String eventName;

	/**人员编号 */
	private String pin;

	/**姓名 */
	private String name;

	/**姓氏 */
	private String lastName;

	/**部门编号 */
	private String deptCode;

	/**部门名称 */
	private String deptName;

	/**读头名称*/
	private String readerName;

	/**验证方式名称*/
	private String verifyModeName;

	/**区域名称 */
	private String areaName;

	private Short eventNo;

	private Integer eventLevel;

	/**事件级别  normal：正常 warning: 异常 alarm：报警 */
	private String status;


	public Dashboard4TrasactionItem() {
		super();
	}

}