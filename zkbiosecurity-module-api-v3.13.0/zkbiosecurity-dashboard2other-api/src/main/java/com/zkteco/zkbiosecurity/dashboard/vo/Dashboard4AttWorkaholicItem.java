package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4AttWorkaholicItem 考勤工作狂人
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4AttWorkaholicItem implements Serializable {

    /**
     * 人员姓名 ：张三（pin）
     */
    private String name;

    /**
     * 工作时长
     */
    private String hour;
}
