package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4AttExceptionItem 考勤异常统计(本月)
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4AttExceptionItem implements Serializable {

    /**
     * 异常事件名称
     */
    private String exceptionName;

    /**
     * 人数
     */
    private String number;
}
