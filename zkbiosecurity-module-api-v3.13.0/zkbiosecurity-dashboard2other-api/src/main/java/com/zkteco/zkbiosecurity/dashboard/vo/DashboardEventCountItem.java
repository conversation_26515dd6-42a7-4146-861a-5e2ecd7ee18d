
/**
 * File Name: DashboardEventCountItem
 * Created by xjing.huang on 2018-12-25 11:41:11
 * Copyright:Copyright © 1985-2018 ZKTeco Inc.All right reserved.
 */

package com.zkteco.zkbiosecurity.dashboard.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 对应百傲瑞达实体 DashboardEventCountItem
 * <AUTHOR>
 * @date:	2018-12-25
 * @version v1.0
 */

@Setter
@Getter
@Accessors(chain=true)
@ToString
public class DashboardEventCountItem extends BaseItem implements Serializable{
	private static final long serialVersionUID = 1L;

	/** 主键 */
	private String id;

	/** 总事件数 */
	private Integer totalevent;

	/** 正常事件数 */
	private Integer normalevent;

	/** 异常事件 */
	private Integer exceptionevent;

	/** 报警事件 */
	private Integer alarmevent;


	public DashboardEventCountItem() {
		super();
	}

}