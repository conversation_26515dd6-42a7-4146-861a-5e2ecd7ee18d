package com.zkteco.zkbiosecurity.dashboard.service;

import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PosTodayDataItem;
import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4TrendItem;
import com.zkteco.zkbiosecurity.dashboard.vo.DashboardDeviceCountItem;

import java.util.List;

/**
 * 在线消费
 * <AUTHOR>
 *
 */
public interface Dashboard4PosIDService {

	/**
	 *在线消费模块设备总数
	 * <AUTHOR>
	 * @Date 2019/10/12 15:21
	 * @param
	 * @return
	 */
	long getDeviceCount();

	/**
	 * 面板设备数据
	 *
	 * @param
	 * @return com.zkteco.zkbiosecurity.dashboard.vo.DashboardDeviceCountItem
	 * <AUTHOR>
	 * @date 2021/8/6/006 10:16
	 * @since 1.0.0
	 */
	DashboardDeviceCountItem getPosidDeviceData();

	/**
	 * 获取今日消费金额相关数据
	 *
	 * @param dataType
	 * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PosTodayDataItem>
	 * <AUTHOR>
	 * @date 2021/8/6/006 10:55
	 * @since 1.0.0
	 */
	List<Dashboard4PosTodayDataItem> getPosidTodayData(String dataType);

	/**
	 * 本月消费趋势
	 *
	 * @param
	 * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4TrendItem>
	 * <AUTHOR>
	 * @date 2021/8/6/006 13:51
	 * @since 1.0.0
	 */
	List<Dashboard4TrendItem> getPosidTrendChartData();
}
