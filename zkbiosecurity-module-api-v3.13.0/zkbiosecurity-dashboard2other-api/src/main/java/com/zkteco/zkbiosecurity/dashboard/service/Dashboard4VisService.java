package com.zkteco.zkbiosecurity.dashboard.service;

import java.util.Date;
import java.util.List;

import com.zkteco.zkbiosecurity.dashboard.vo.*;

/**
 * 访客dashboard相关操作
 */
public interface Dashboard4VisService {

    /**
     * 当日来访流量 统计今天各个时间段来访人员数 时间点（time字段） 0:00 - 23:00
     * 
     * <AUTHOR>
     * @Date 2018/12/26 14:30
     * @param
     * @return
     */
    List<Dashboard4VisDayTripsItem> getVisDayTripsData();

    /**
     * 根据日期获取当日来访流量 统计各个时间段来访人员数 时间点（time字段） 0:00 - 23:00
     * 
     * @param date:
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4VisDayTripsItem>
     * <AUTHOR>
     * @throws
     * @date 2024-07-19 15:32
     * @since 1.0.0
     */
    default List<Dashboard4VisDayTripsItem> getVisDayTripsData(Date date) {
        return null;
    }

    /**
     * 最近12个月来访人数情况
     * 
     * <AUTHOR>
     * @Date 2018/12/26 14:30
     * @param
     * @return
     */
    List<Dashboard4VisitorTrendsItem> getVisitorTrendsData();

    /**
     * 登记点流量统计 统计各个登记点访问量
     * 
     * <AUTHOR>
     * @Date 2018/12/26 14:30
     * @param
     * @return
     */
    List<Dashboard4VisRegistPointFlowItem> getVisRegistPointFlowData();

    /**
     * 被访人TOP5
     * 
     * <AUTHOR>
     * @Date 2018/12/26 14:30
     * @param
     * @return
     */
    List<Dashboard4VisitedListItem> getVisitedListData();

    /**
     * 被访人TOP5，根据sessionid判断是否加密显示数据
     * 
     * @param sessionId:
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4VisitedListItem>
     * <AUTHOR>
     * @throws
     * @date 2024-09-27 16:11
     * @since 1.0.0
     */
    List<Dashboard4VisitedListItem> getVisitedListData(String sessionId);

    /**
     * 访客TOP5
     * 
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4VisitedListItem>
     * <AUTHOR>
     * @throws @date 2021-08-02 10:30
     * @since 1.0.0
     */
    default List<Dashboard4VisitedListItem> getVisitorListData() {
        return null;
    }

    /**
     * 访客TOP5，根据sessionid判断是否加密显示数据
     *
     * @param sessionId:
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4VisitedListItem>
     * <AUTHOR>
     * @throws
     * @date 2024-09-27 15:51
     * @since 1.0.0
     */
    default List<Dashboard4VisitedListItem> getVisitorListData(String sessionId) {
        return null;
    }

    /**
     * 根据月份显示访客TOP5
     * 
     * @param month:
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4VisitedListItem>
     * <AUTHOR>
     * @throws
     * @date 2024-07-19 15:39
     * @since 1.0.0
     */
    default List<Dashboard4VisitedListItem> getVisitorListData(Integer month) {
        return null;
    }

    /**
     * 根据月份显示访客TOP5，根据sessionid判断是否加密显示数据
     * 
     * @param month:
     * @param sessionId:
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4VisitedListItem>
     * <AUTHOR>
     * @throws
     * @date 2024-09-27 15:51
     * @since 1.0.0
     */
    default List<Dashboard4VisitedListItem> getVisitorListData(Integer month, String sessionId) {
        return null;
    }

    /**
     * 今日访问量
     * 
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4VisRegistPointFlowItem>
     * <AUTHOR>
     * @throws @date 2021-08-02 10:48
     * @since 1.0.0
     */
    default Dashboard4VisVisitCountItem getVisTodayVisitCount() {
        return null;
    }

    /**
     * 获取访客今日列表
     * 
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4VisTodayVisitListItem>
     * <AUTHOR>
     * @throws @date 2021-08-06 16:45
     * @since 1.0.0
     */
    default List<Dashboard4VisTodayVisitListItem> getVisTodayVisitList() {
        return null;
    }

    /**
     * 获取访客今日列表，根据sessionid判断是否加密显示数据
     * 
     * @param sessionId:
     * @return java.util.List<com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4VisTodayVisitListItem>
     * <AUTHOR>
     * @throws
     * @date 2024-09-27 16:22
     * @since 1.0.0
     */
    default List<Dashboard4VisTodayVisitListItem> getVisTodayVisitList(String sessionId) {
        return null;
    }
}
