package com.zkteco.zkbiosecurity.dashboard.service;

import com.zkteco.zkbiosecurity.dashboard.vo.*;

import java.util.List;

/**
 * 高级门禁dashboard
 *
 * <AUTHOR>
 * @version v1.0
 * @date: 2018-12-27 上午09:38
 */
public interface Dashboard4AccAdvancedService {



	/**
	 *门禁区域监控数据
	 * 高级门禁的各个区域下的人员数据
	 * <AUTHOR>
	 * @Date 2018/12/26 14:30
	 * @param
	 * @return
	 */
	List<Dashboard4AccZoneItem> getAccZoneMonitoringData();

	
}
