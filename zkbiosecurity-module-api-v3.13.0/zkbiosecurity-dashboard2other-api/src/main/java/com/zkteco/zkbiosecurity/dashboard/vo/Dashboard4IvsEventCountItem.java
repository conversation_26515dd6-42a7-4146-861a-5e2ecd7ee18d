package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Dashboard4IvsEventTrendsItem 视频事件
 * 
 * <AUTHOR>
 * @date 2025-01-06 15:50
 * @param 
 * @return 
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4IvsEventCountItem implements Serializable {

    /**
     * 名称
     */
    private String name;

    /**
     * 事件数
     */
    private long count;
}
