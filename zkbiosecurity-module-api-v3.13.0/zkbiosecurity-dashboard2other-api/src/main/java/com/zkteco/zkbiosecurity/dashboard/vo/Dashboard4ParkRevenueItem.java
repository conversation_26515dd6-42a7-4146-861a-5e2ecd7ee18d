package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *Dashboard4ParkRevenueItem 停车营收
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4ParkRevenueItem {

	/**
     * 时间点
     * 今日营收： 0:00 - 23:00
     * 本月营收： 1号 - 30号
     */
    private String time;

    /**
     * 营收额度
     */
    private String number;
}
