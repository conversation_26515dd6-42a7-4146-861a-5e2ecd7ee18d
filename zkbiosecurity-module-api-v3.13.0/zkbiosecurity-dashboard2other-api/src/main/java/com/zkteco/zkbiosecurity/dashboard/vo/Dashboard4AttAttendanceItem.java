package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 出勤率
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/8/3 14:24
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4AttAttendanceItem {

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 百分比
     */
    private String percentage;
}
