package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4VisDayTripsItem 当日来访流量
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4VisDayTripsItem implements Serializable {

    /**
     * 时间点 0:00 - 23:00
     */
    private String time;

    /**
     * 访问数
     */
    private String number;
}
