package com.zkteco.zkbiosecurity.dashboard.service;

import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PersPersonTrendsItem;

import java.util.Map;

/**
 * 生物模板dashboard相关操作
 */
public interface Dashboard4PersService {

    /**
     * 获取dashboard生物模板数据及人员总数 key： 发卡 hasCardTotal noCardTotal 密码 hasPswTotal noPswTotal 指纹 hasFpTotal noFpTotal 面部
     * hasFaceTotal noFaceTotal 指静脉 hasFvTotal noFvTotal 掌纹 hasPlamTotal noPlamTotal 人员总数 personCount
     * 
     * <AUTHOR>
     * @Date 2018/12/28 11:03
     * @param
     * @return
     */
    Map<String, String> getBaseBioData();

    /**
     * 获取入职、离职人员数量
     * 
     * @return com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4PersPersonTrendsItem
     * <AUTHOR>
     * @throws @date 2021-08-06 14:23
     * @since 1.0.0
     */
    default Dashboard4PersPersonTrendsItem getPersPersonTrends() {
        return null;
    }

    /**
     * 获取dashboard生物模板数据及人员总数 key： 发卡 hasCardTotal noCardTotal 密码 hasPswTotal noPswTotal 指纹 hasFpTotal noFpTotal 面部
     * hasFaceTotal noFaceTotal 指静脉 hasFvTotal noFvTotal 掌纹 hasPlamTotal noPlamTotal 人员总数 personCount
     * 
     * @return java.util.Map<java.lang.String,java.lang.String>
     * <AUTHOR>
     * @throws @date 2021-08-09 17:48
     * @since 1.0.0
     */
    default Map<String, String> getPersBioData() {
        return null;
    }

    /**
     * 获取人员图片
     *
     * @return java.lang.String
     * <AUTHOR>
     * @date  2024/3/8 14:24
     * @since 1.0.0
     */
    default String getPersonPhotoPathByPin(String pin) {
        return null;
    }
}
