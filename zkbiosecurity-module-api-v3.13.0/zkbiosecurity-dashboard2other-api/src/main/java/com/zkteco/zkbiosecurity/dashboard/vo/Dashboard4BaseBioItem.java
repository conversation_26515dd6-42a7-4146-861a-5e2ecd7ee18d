package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *Dashboard4BaseBioItem 生物模板数据
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4BaseBioItem {

	/**
     * 名称 (发卡人员、未发卡人员) (已设密码人数 未设密码人数) (已录指纹人数 未录指纹人数) (已录面部人数 未录面部人数)
     * (已录指静脉人数 未录指静脉人数) (已录掌纹人数 未录掌纹人数)
     */
    private String name;

    /**
     * 人数
     */
    private String number;
}
