package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4ParkRecordinItem 在场车辆
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4ParkRecordinItem implements Serializable {

    /**
     * 主键
     */
    private String id;

    /**
     * 原始进场记录ID checkin_id 用于微信支付场景，标记新插入的进场记录（辅助）是同一场次
     */
    private String checkinId;

    /**
     * 事件来源的设备记录ID
     */
    private Integer recordinLogId;

    /**
     * 事件来源的设备IP
     */
    private String sourceDevIp;

    /**
     * 岗亭id
     */
    private String pavilioId;

    /**
     * 岗亭名称
     */
    private String pavilioName;

    /**
     * 区域Id
     */
    private String parkingAreaId;

    /**
     * 区域类型
     */
    private Short parkingAreaType;

    /**
     * 区域名称
     */
    private String parkingAreaName;

    /**
     * 区域Id
     */
    private String entranceAreaId;

    /**
     * 区域名称
     */
    private String entranceAreaName;

    /**
     * 车主姓名
     */
    private String userName;

    /**
     * 车主ID
     */
    private String userId;

    /**
     * 车牌号码
     */
    private String carNumber;

    /**
     * 卡号
     */
    private String cardNumber;

    /**
     * 车辆类型Id
     */
    private String carTypeId;

    /**
     * 车辆类型编码
     */
    private String carTypeCode;

    /**
     * 车辆类型名称
     */
    private String carTypeName;

    /**
     * 值班操作员ID
     */
    private String dutyId;

    /**
     * 值班操作员姓名
     */
    private String dutyName;

    private String workRecordId;

    /**
     * 通道名称
     */
    private String channelName;

    /**
     * 通道进出状态：1：大入、2：大出、3：小进、4：小出、5：中央收费定点、6：中央收费出口
     */
    private Short channelState;

    /**
     * 事件类型：1：一般刷卡记录 2：固定卡转临时卡 3：遗失卡出场 4：超时收费记录 5：人工开闸记录（通过控制器或软件） 6：取消开闸 7：月卡收费 8：访客贵宾车 9：车牌校正
     */
    private Short eventType;

    /**
     * 进场时间
     */
    private Timestamp checkInTime;

    /**
     * 进场时间
     */
    private String checkInTimeStr;

    /**
     * 入场抓拍照片
     */
    private String photoPathIn;

    /**
     * 上传到云直接用线下的照片名称
     */
    private String photoName;

    /**
     * 进出通道编号
     */
    private String channelInId;

    /**
     * 人工处理方式编号
     */
    private String releaseReasonId;

    /**  */
    private String parkingMeterId;

    /**  */
    private String parkingLotName;

    /**
     * 车辆进出状态：1：进场、2：已出厂
     */
    private Short status;

    /**
     * 进场记录过滤初始化数据
     */
    private Short statusNot;

    /**
     * 开始时间
     **/
    private Date timeBegin;

    /**
     * 时间基线<用于微信>
     */
    private Timestamp lastCheckInTime;

    /**
     * 滚动已收金额<用于微信>
     */
    private Double haveFees;
}
