package com.zkteco.zkbiosecurity.dashboard.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 考勤设备
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2021/8/3 14:24
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4AttDeviceItem {

    /**
     * 在线数量
     */
    private Integer onlineCount;

    /**
     * 离线数量
     */
    private Integer offlineCount;

    /**
     * 设备总数
     */
    private Integer totalCount;
}
