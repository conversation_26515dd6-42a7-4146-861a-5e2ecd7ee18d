package com.zkteco.zkbiosecurity.dashboard.vo;

import java.util.Date;
import java.util.UUID;

import com.zkteco.zkbiosecurity.base.annotation.GridColumn;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 防疫看板事件统计人员信息
 *
 * <AUTHOR>
 * @date 2021-08-04 15:10
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4HepTransactionItem {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @GridColumn(show = false)
    private String id = UUID.randomUUID().toString();

    /**
     * 人员编号
     */
    @GridColumn(label = "pers_person_pin", width = "80")
    private String pin;

    /** 事件时间 */
    @GridColumn(label = "hep_transaction_event_date", width = "110")
    private Date eventTime;

    /** 体温 */
    @GridColumn(label = "hep_transaction_temperature", width = "80")
    private Double temperature;

    /**
     * 姓名
     */
    @GridColumn(label = "pers_person_name", width = "100")
    private String name;

    /**
     * 姓氏
     */
    @GridColumn(label = "pers_person_lastName", width = "120", showExpression = "#language!='zh_CN'")
    private String lastName;

    /**
     * 部门编号
     */
    @GridColumn(label = "pers_dept_deptNo", width = "100")
    private String deptCode;
    /**
     * 部门名称
     */
    @GridColumn(label = "pers_dept_deptName", width = "100")
    private String deptName;
}
