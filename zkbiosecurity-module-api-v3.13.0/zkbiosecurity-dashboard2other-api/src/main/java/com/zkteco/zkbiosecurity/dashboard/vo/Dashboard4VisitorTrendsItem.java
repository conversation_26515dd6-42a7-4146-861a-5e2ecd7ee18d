package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4VisVisitorTrendsItem 最近12个月来访量趋势
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4VisitorTrendsItem implements Serializable {

    /**
     * 月份
     */
    private String month;

    /**
     * 访问数
     */
    private String number;
}
