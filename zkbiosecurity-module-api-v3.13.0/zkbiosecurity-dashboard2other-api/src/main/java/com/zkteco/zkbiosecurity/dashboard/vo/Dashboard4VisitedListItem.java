package com.zkteco.zkbiosecurity.dashboard.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Dashboard4VisitedListItem 被访人TOP5
 * 
 * <AUTHOR>
 * @Date 2018/12/27 9:25
 */
@Getter
@Setter
@Accessors(chain = true)
public class Dashboard4VisitedListItem implements Serializable {

    /**
     * 被访人姓名
     */
    private String name;

    /**
     * 被访数
     */
    private String number;
}
