package com.zkteco.zkbiosecurity.dashboard.service;

import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4IvsDealEventItem;
import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4IvsEventCountItem;
import com.zkteco.zkbiosecurity.dashboard.vo.Dashboard4IvsEventTrendsItem;
import com.zkteco.zkbiosecurity.dashboard.vo.DashboardDeviceCountItem;

import java.util.List;
import java.util.Map;

/**
 * 视频dashboard
 *
 * <AUTHOR>
 * @date 2025/1/9 13:52
 */
public interface Dashboard4IvsService {

    /**
     * 统计设备在线、离线及总数
     * 
     * <AUTHOR>
     * @date 2025-01-09 13:56
     * @return 
     */
    DashboardDeviceCountItem getDeviceCountItem();

    /**
     * 告警处理统计
     * 
     * <AUTHOR>
     * @date 2025-01-06 15:51
     * @param timeType
     * @return 
     */
    Dashboard4IvsDealEventItem getDealEventCount(String timeType);

    /**
     * 区域告警统计
     *
     * <AUTHOR>
     * @date 2025-01-06 15:43
     * @param timeType
     * @return
     */
    List<Dashboard4IvsEventCountItem> getAreaEventCount(String timeType);

    /**
     * 告警事件来源统计
     *
     * <AUTHOR>
     * @date 2025-01-06 15:43
     * @param areaId
     * @param timeType
     * @return
     */
    List<Dashboard4IvsEventCountItem> getResourceEventCount(String areaId, String timeType);

    /**
     * 告警事件TOP10
     *
     * <AUTHOR>
     * @date 2025-01-06 15:43
     * @param areaId
     * @param timeType
     * @return
     */
    List<Dashboard4IvsEventCountItem> getTOP10EventCount(String areaId, String timeType);

    /**
     * 告警级别统计
     *
     * <AUTHOR>
     * @date 2025-01-06 15:43
     * @param timeType
     * @return
     */
    List<Dashboard4IvsEventCountItem> getLevelEventCount(String timeType);

    /**
     * 告警趋势统计
     *
     * <AUTHOR>
     * @date 2025-01-06 15:43
     * @param timeType
     * @return
     */
    Map<String, List<Dashboard4IvsEventTrendsItem>> getTrendsEventCount(String timeType);
}
