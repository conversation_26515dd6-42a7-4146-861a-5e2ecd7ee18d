package com.zkteco.zkbiosecurity.locker.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class Locker4VidChannelItem implements Serializable {
    private static final long serialVersionUID = 1L;
    /** 主键 */
    private String id;

    /** 通道名称 */
    private String name;

    /** 通道编号 */
    private Short channelNo;

    /** 区域名称 */
    private String areaName;

    /** 区域id */
    private String areaId;

    /** 设备名称 */
    private String alias;

    /** 设备id */
    private String deviceId;

    /** 禁用启用 */
    private Boolean enabled;

    private String inId;

    private String sn;

    private String notInId;
}
