package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * 态势告警
 */
@Getter
@Setter
public class EsdcSituationAnalysisBean {
    /**
     * 拌线标记
     * true:拌线
     * false:区域
     */
    private boolean lineFlag;
    /**
     * 区域信息
     */
    private EsdcAreaBean areaBean;
    /**
     * 拌线信息
     */
    private EsdcLineBean lineBean;
    /**
     * 截图base64
     */
    private String snapshot;
    /**
     * 全景图base64
     */
    private String panorama;

    /**
     * 目标方框
     */
    private List<EsdcDetectObjectBean> detectObject;

    /**
     * 第三方算法名称
     */
    private String thirdAlgorithmName;


    /**
     * 总进入人数
     */
    private int totalInCount;

    /**
     * 总离开人数
     */
    private int totalOutCount;

    /**
     * 当前进入人数
     */
    private int nowInCount;

    /**
     * 当前离开人数
     */
    private int nowOutCount;

    /**
     * 检测区域内存在的人数，realtime_data_type类型为人群密度类型时返回该字段，单位：/人
     */
    private int peopleCount;
}
