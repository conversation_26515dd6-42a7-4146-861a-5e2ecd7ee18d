package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 对应百傲瑞达实体 IvsDevice
 * <AUTHOR>
 * @date:	2022-01-08 13:13
 * @version v1.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Esdc2OtherDeviceItem {

	private static final long serialVersionUID = 1L;

	/*** 设备名称*/
	private String name;

	/** 端口 */
	private Integer port;

	/** 用户名 */
	private String userName;

	/** 密码 */
	private String commPwd;

	/** 设备所属区域 */
	private String areaId;

	/*** IP*/
	private String ip;

	/** 区分设备类型 0-sdc;1-aiBox */
	private Integer type;
	
}