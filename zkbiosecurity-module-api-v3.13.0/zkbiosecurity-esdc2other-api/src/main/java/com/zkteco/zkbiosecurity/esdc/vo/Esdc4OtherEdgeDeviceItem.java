package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 其他模块调用Vid视频主设备通用Item类
 *
 * <AUTHOR>
 * @date  2022-01-08 9:40
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
@ToString
public class Esdc4OtherEdgeDeviceItem {

   private static final long serialVersionUID = 1L;

	/**  主键 */
   private String id;

	/**  名称 */
	private String name;

	/**  端口 */
	private String port;

	/**  用户名 */
	private String userName;

	/**  地址 */
	private String ip;

	/**  密码 */
	private String passWord;

	/** 类型 字典值：
	 0-IVS1800、
	 1-IVS3800、
	 2-NVR800、
	 4-云服务、
	 5-SDC、
	 7-ZKNVR、
	 */
	private String type;

	/** 状态 0-禁用 1-启用 */
	private String status;

	/** 域编码 */
	private String domainCode;

	/** NVR800旁位类型 */
	private String modelType;

	/** 设备版本 */
	private String version;

	/** session信息 */
	private String securitySession;

	/** token信息 */
	private String token;

}