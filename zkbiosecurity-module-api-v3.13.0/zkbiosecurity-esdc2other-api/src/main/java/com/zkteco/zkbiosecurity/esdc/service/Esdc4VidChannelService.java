package com.zkteco.zkbiosecurity.esdc.service;


import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.esdc.vo.Esdc4OtherChannelItem;

import java.util.Collection;
import java.util.List;

/**
 * 视频桥接esdc摄像头服务类
 *
 * <AUTHOR>
 * @date 2020-12-21 09:27
 */
public interface Esdc4VidChannelService {



    /**
     * 根据通道id集合获取通道信息
     *
     * @param ids
     * @return
     * <AUTHOR>
     * @date 2020-12-21 09:27
     */
    default List<Esdc4OtherChannelItem> getItemsByChannelIds(Collection<String> ids) {
        return null;
    }

    /**
     * 根据分页查询
     *
     * @param condition
     * @param page
     * @param size
     * @return List
     * <AUTHOR>
     * @date 2020-12-21 09:27
     */
    default Pager getItemsByPage(Esdc4OtherChannelItem condition, int page, int size) {
        return null;
    }

    /**
     * 根据条件查询
     *
     * @param condition
     * @return List
     * <AUTHOR>
     * @date 2020-12-21 09:27
     */
    default List<Esdc4OtherChannelItem> getItemsByCondition(Esdc4OtherChannelItem condition) {
        return null;
    }


}
