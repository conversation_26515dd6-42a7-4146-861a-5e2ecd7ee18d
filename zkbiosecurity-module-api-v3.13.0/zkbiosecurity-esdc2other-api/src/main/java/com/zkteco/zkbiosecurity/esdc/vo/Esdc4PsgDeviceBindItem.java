package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Data;

import org.springframework.lang.Nullable;

/**
 * Esdc4PsgDeviceBindItem 绑定设备对象的信息。
 *
 * <AUTHOR> Feng
 */
@Data
public class Esdc4PsgDeviceBindItem {

	/**
	 * 视频设备通道ID, 入参/回参
	 */
	private String channelId;

	/**
	 * 通道名称,回参，入参可以为null
	 */
	@Nullable
	private String channelAlias;

	/**
	 * 时间段ID，入参/回参
	 */
	private String timeSegId;

	/**
	 * 是否启用，入参/回参，入参可以为null
	 */
	@Nullable
	private Boolean enabled;

	/**
	 * 读头ID，入参/回参
	 */
	private String readerId;

}
