package com.zkteco.zkbiosecurity.esdc.vo;

import java.util.Date;

import lombok.Data;

import org.springframework.lang.Nullable;

/**
 * Psg推送到esdc模块事件dto
 *
 * <AUTHOR> Feng
 */
@Data
public class Psg4EsdcTransactionItem {

	/**
	 * 读头ID
	 */
	private String readerId;

	/**
	 * 人员工号，当有人员工号时不为空
	 *
	 * 当事件和人员无关时为空，例如非法事件段等。
	 */
	@Nullable
	private String pin;

	/**
	 * 事件类型Code
	 */
	private Short eventNo;

	/**
	 * 事件名称
	 */
	private String eventName;

	/**
	 * 事件发生时间
	 */
	private Date eventTime;

	/**
	 * 设备ID
	 */
	private String devId;

}
