package com.zkteco.zkbiosecurity.esdc.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * Esdc模块选择门vo
 *
 * <AUTHOR>
 * @date 2020-11-02 12:06
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Esdc4AccDoorSelectItem extends BaseItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private String id;

    /** 门名称 */
    private String doorName;

    /** 门编号 */
    private Short doorNo;

    /** 设备别名 */
    private String deviceAlias;

    /** 设备序列号 */
    private String deviceSn;

    /** 设备ID */
    private String deviceId;

    /** 类型 */
    private String type;

    /** 选中的id */
    private String selectId;

    /** 区域id */
    private String areaId;

    /** 查询条件：in */
    private String inId;

    /** 查询条件：notIn */
    private String notInId;
}
