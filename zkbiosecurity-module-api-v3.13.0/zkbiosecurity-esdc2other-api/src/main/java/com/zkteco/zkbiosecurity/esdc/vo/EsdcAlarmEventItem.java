/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.zkteco.zkbiosecurity.esdc.vo;


import lombok.Getter;
import lombok.Setter;

/**
 * HwAlarmEventItem
 *
 * <AUTHOR> lwx960330
 * @version V100R001C00
 * @since : 2020年7月18日
 */

@Getter
@Setter
public class EsdcAlarmEventItem extends EsdcBaseEventRespond{
    /**
     * 告警名称
     */
    private String name;
    /**
     * 事件ID(仅IVS1800)
     */
    private String id;
    /**
     * 告警Code(仅IVS1800)
     */
    private String code;
    /**
     * 告警级别(仅IVS1800)
     */
    private String alarmLevel;
    /**
     * 元数据类型 人脸 FACE / 人体 PERSON / 机动车 MOTOR_VEHICLE / 行为分析 BEHAVIOR / 非机动车 NON_MOTOR_VEHICLE
     */
    private String metaType;
    /**
     * 告警状态(仅IVS1800)
     * •0：未处理
     * •1：待确认
     * •2：已确认
     * •3：已撤销
     * •4：已恢复
     */
    private int status;
    /**
     * 通道id
     */
    private String channelId;

    /**
     * 告警类型
     */
    private String alarmCodeType;

    /**
     * 来源 0: 前端告警 1: IVS1800内部智能分析告警
     */
    private String source;
    /**
     * 人脸信息
     */
    private EsdcFaceInfoItem faceBean;

    /**
     * 车辆信息
     */
    private EsdcVehicleInfoItem vehicleBean;

    /**
     * 行为分析
     */
    private EsdcBehaviorInfoItem behaviorBean;

    /**
     * NVR800 通道状态
     */
    private EsdcChannelStatusInfoItem channelStatusBean;

    /**
     * 态势分析告警
     */
    private EsdcSituationAnalysisBean situationAnalysisBean;

    /**
     * 一般告警
     */
    private EsdcGeneralBusinessBeanItem generalBusinessBean;

    /**
     * 第三方算法告警
     */
    private EsdcThirdAlgorithmBeanItem thirdAlgorithmBean;

    /**
     * 截图base64
     */
    private String snapshot;
    /**
     * 全景图base64
     */
    private String panorama;
    /**
     * 匹配图片base64
     */
    private String matchedImage;

}
