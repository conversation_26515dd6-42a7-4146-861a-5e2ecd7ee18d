/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * ChannelStatusBean
 *
 * <AUTHOR> lwx960330
 * @version V100R001C00
 * @since : 2020年7月18日
 */
@Getter
@Setter
public class EsdcChannelStatusInfoItem {
    /**
     * 连接状态
     */
    private String connectStatus;
    /**
     * 能力集
     */
    private List<String> abilities  = new ArrayList<>();
    /**
     * IPC接入协议
     */
    private String protocol;
    /**
     * 报警输入
     */
    private int inPutNum;
    /**
     * 报警输出
     */
    private int outPutNum;
}
