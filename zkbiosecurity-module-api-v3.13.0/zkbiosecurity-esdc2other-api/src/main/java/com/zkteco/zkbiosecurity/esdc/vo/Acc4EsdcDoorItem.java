package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * Acc4EsdcDoorItem
 * @auto fly
 * @date 2020-10-30 15:14
 */
@Getter
@Setter
@Accessors(chain=true)
@NoArgsConstructor
@AllArgsConstructor
public class Acc4EsdcDoorItem {

    /** 门id*/
    private String id;

    /** 门编号 */
    private Short doorNo;

    /** 门名称 */
    private String name;

    /** 验证方式 */
    private Short verifyMode;

    /** 门是否禁用 */
    private Boolean enabled;

}
