package com.zkteco.zkbiosecurity.esdc.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.zkteco.zkbiosecurity.core.exception.ZKBusinessException;
import com.zkteco.zkbiosecurity.esdc.vo.Esdc4PsgDeviceBindItem;

import org.springframework.lang.Nullable;

/**
 * Esdc4PsgBindService. Esdc模块供Psg模块调用接口定义.
 *
 * 设备读头和视频通道绑定
 *
 * <AUTHOR> Feng
 */
public interface Esdc4PsgBindService {

	/**
	 * 绑定通道读头和视频通道
	 * @param bindItem 需要绑定的信息
	 * @throws ZKBusinessException 入参校验不通过抛出
	 */
	default void bind(Esdc4PsgDeviceBindItem bindItem) {
	}

	/**
	 * 根据读头ID返回绑定的信息
	 * @param readerId 读头ID
	 * @return Esdc4PsgDeviceBindItem集合 可能返回null
	 */
	@Nullable
	default List<Esdc4PsgDeviceBindItem> getBindItemByReaderId(String readerId) {
		return null;
	}

	/**
	 * 根据读头ID集合返回绑定的信息
	 * @param readerIdSet 读头ID集合
	 * @return Esdc4PsgDeviceBindItem集合 可能返回null
	 */
	@Nullable
	default List<Esdc4PsgDeviceBindItem> getBindItemByReaderIdSet(Set<String> readerIdSet) {
		return null;
	}

	/**
	 * 查询绑定信息，读头ID和通道ID唯一确定一个绑定关系
	 * @param readerId 读头ID
	 * @param channelId 通道ID
	 * @return 绑定关系
	 */
	@Nullable
	default Esdc4PsgDeviceBindItem getBindItemByReaderIdAndChannelId(String readerId, String channelId) {
		return null;
	}

	/**
	 * 返回读头ID和通道ID的关系Map
	 * @param readerIds 读头ids
	 * @return Map<String,String> 第一个是读头ID，第二个是ChannelId用“，”拼接的字符串
	 */
	default Map<String, String> getReaderChannelIdMappingByReaderId(List<String> readerIds) {
		return null;
	}

	/**
	 * 返回读头ID和通道名称的关系Map
	 * @param readerIds 读头ids
	 * @return Map<String,String> 第一个是读头ID，第二个是通道名称用“，”拼接的字符串，没有的话用"".
	 */
	default Map<String, String> getReaderChannelNameMappingByReaderId(List<String> readerIds) {
		return null;
	}

}
