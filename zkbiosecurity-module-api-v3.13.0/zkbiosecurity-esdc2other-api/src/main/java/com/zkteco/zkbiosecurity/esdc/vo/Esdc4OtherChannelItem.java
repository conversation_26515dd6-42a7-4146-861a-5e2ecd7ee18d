package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 其他模块调用Vid视频通道通用Item类
 *
 * <AUTHOR>
 * @date 2020/11/6 8:58
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class Esdc4OtherChannelItem {
    private static final long serialVersionUID = 1L;
    /** 主键 */
    private String id;

    /** 通道名称 */
    private String name;

    /** 通道编号 */
    private Short channelNo;

    /** 区域名称 */
    private String areaName;

    /** 区域id */
    private String areaId;

    /** 设备名称 */
    private String alias;

    /** 设备id */
    private String deviceId;

    /** * 禁用启用 */
    private Boolean enabled;

    /** 设备序列号 */
    private String sn;

    /** 查询条件:Id被包含 */
    private String inId;

    /** 查询条件:Id不被包含 */
    private String notInId;

    /*** 域编码 */
    private String domainCode;

    /*** 通道code */
    private String channelCode;

    /*** 设备状态：l 0：离线 l 1：在线 l 2：休眠 */
    private String status;

    /*** 前端IP点分十进制格式，例如：**************，长度限制64字节*/
    private String ip;

	/** 父设备id */
	private String vidParentDeviceId;

}
