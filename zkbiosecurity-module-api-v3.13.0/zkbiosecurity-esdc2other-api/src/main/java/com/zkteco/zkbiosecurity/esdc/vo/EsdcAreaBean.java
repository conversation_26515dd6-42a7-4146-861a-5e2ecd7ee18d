package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * AreaBean 区域检测
 */
@Getter
@Setter
public class EsdcAreaBean {
    /**
     * 检测方向
     * 方向 0:无方向,1:向外,2:向内,3:双向
     */
    private int direct;
    /**
     * 类型
     */
    private int type;
    /**
     * 坐标个数
     */
    private int pointNum;
    /**
     * 区域坐标信息
     */
    private List<EsdcPointBean> pointBeans;

}
