package com.zkteco.zkbiosecurity.esdc.service;


import com.zkteco.zkbiosecurity.esdc.vo.Acc4EsdcTransactionItem;

/**
 * 门禁事件通知esdc接口
 *
 * <AUTHOR>
 * @date 2020-11-02 13:41
 * @since 1.0.0
 */
public interface Acc4EsdcTransactionService {

   /**
    * 门禁推送事件记录到esdc模块
    *
    * @param acc4EsdcTransactionItem
    * @return void
    * @throws
    * <AUTHOR> Feng
    * @date  2020-11-02 13:42
    * @since 1.0.0
    */
    default void pushTransactionsToEsdc(Acc4EsdcTransactionItem acc4EsdcTransactionItem){

    }

}
