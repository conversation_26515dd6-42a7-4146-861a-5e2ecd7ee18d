/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * VehicleBean
 *
 * <AUTHOR> lwx960330
 * @version V100R001C00
 * @since : 2020年7月18日
 */
@Getter
@Setter
public class EsdcVehicleInfoItem {
    /**
     * 左上X坐标
     */
    private int leftTopX;
    /**
     * 左上Y坐标
     */
    private int leftTopY;
    /**
     * 右下X坐标
     */
    private int rightBtmX;
    /**
     * 右下Y坐标
     */
    private int rightBtmY;
    /**
     * 车辆品牌
     */
    private String vehicleBrand;
    /**
     * 车辆类别
     */
    private String vehicleClass;
    /**
     * 车辆颜色
     */
    private String vehicleColor;
    /**
     * 车辆型号
     */
    private String vehicleModel;
    /**
     * 车辆年款
     */
    private String vehicleStyles;
    /**
     * 有无车牌 0：无  1：有
     */
    private String hasPlate;
    /**
     * 车牌号
     */
    private String plateNo;
    /**
     * 车牌颜色
     */
    private String plateColor;
    /**
     * 号牌类别
     */
    private String plateClass;
    /**
     * 命中车辆业务ID
     */
    private String targetId;
    /**
     * 截图base64
     */
    private String snapshot;
    /**
     * 车牌截图base64
     */
    private String plateSnapShot;
    /**
     * 全景图base64
     */
    private String panorama;
    /**
     * 对比图
     */
    private String matchedImage;
    /**
     * 名单组ID
     */
    private String groupId;
    /**
     * 名单组名称
     */
    private String groupName;
    /**
     * 名单组类型
     */
    private String groupType = "2";

    /**
     * 是否告警命中(0：非告警，1：命中，2：不命中)
     */
    private String alarmMatch;

    /**
     * 名单库中的车牌
     */
    private String blackPlateNo;
}
