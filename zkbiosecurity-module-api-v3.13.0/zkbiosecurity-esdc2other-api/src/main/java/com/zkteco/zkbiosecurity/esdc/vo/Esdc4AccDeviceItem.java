package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * ESDC模块需要的门禁设备vo
 *
 * <AUTHOR>
 * @date 2020-11-02 12:10
 * @since 1.0.0
 */
@Getter
@Setter
public class Esdc4AccDeviceItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 设备id */
    private String id;

    /** 设备名称 */
    private String alias;

    /** 设备序列号 */
    private String sn;

    /** 区域id */
    private String authAreaId;

    /** 区域名称 */
    private String authAreaName;

    /** IP地址 */
    private String ipAddress;

    /** 设备型号 */
    private String deviceName;

    /**  启用 */
    private Boolean enabled;

}
