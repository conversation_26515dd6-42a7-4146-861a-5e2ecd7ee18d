package com.zkteco.zkbiosecurity.esdc.service;

import com.alibaba.fastjson.JSONObject;
import com.zkteco.zkbiosecurity.esdc.vo.EsdcAlarmEventItem;

import java.util.Collection;

/**
 *  视频推送记录
 *
 * <AUTHOR>
 * @date 2020-12-21 09:27
 */
public interface EsdcGetVidPushRecordService {

    /**
     * 告警数据推送
     * @param esdcAlarmEventItem
     * @return
     * <AUTHOR>
     * @date 2020-12-21 09:27
     */
    default void sendAlarmEventData(EsdcAlarmEventItem esdcAlarmEventItem) {

    }

    /**
     * 删除设备推送
     *
     * @param ids
     * @return void
     * <AUTHOR>
     * @date 2020-12-24 11:23
     * @since 1.0.0
     */
    default void deleteDevice(Collection<String> ids) {

    }
}
