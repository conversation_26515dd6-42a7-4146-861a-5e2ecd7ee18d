package com.zkteco.zkbiosecurity.esdc.vo;

import java.util.List;

import com.alibaba.fastjson.JSONArray;
import lombok.Getter;
import lombok.Setter;

/**
 * 第三方算法告警
 *
 * <AUTHOR> rob.zou
 * @since : 2021-11-30
 */
@Getter
@Setter
public class EsdcThirdAlgorithmBeanItem {
    /**
     * 区域信息
     */
    private EsdcAreaInfoItem areaBean;
    /**
     * 拌线信息
     */
    private EsdcLineInfoItem lineBean;
    /**
     * 截图base64
     */
    private String snapshot;
    /**
     * 全景图base64
     */
    private String panorama;

    /**
     * 目标方框
     */
    private List<EsdcDetectObjectInfoItem> detectObject;

    /**
     * 第三方算法名称
     */
    private String thirdAlgorithmName;

    /**
     * 告警描述
     */
    private String alarmDesc;

    /**
     * 三方算法信息展示
     */
    private JSONArray alarmInfos;
}
