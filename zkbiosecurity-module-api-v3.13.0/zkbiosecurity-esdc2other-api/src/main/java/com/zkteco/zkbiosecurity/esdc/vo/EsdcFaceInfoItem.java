/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * FaceBean
 *
 * <AUTHOR> lwx960330
 * @version V100R001C00
 * @since : 2020年7月18日
 */
@Getter
@Setter
public class EsdcFaceInfoItem {
    /**
     * 性别
     * 1:男；2女
     */
    private String genderCode;
    /**
     * 左上X坐标
     */
    private int leftTopX;
    /**
     * 左上Y坐标
     */
    private int leftTopY;
    /**
     * 右下X坐标
     */
    private int rightBtmX;
    /**
     * 右下Y坐标
     */
    private int rightBtmY;
    /**
     * 命中人业务ID
     */
    private String targetId;
    /**
     * 命中人姓名
     */
    private String targetName;
    /**
     * 命中人证件号码
     */
    private String targetCredentialNumber;
    /**
     * 命中人组信息（名称）
     */
    private String groupName;
    /**
     * 命中人组信息（类型）
     * 2：人脸库黑名单
     * 3：人脸库白名单
     */
    private String groupType;
    /**
     * 命中人相识度
     */
    private double similarityDegree;
    /**
     * 截图base64
     */
    private String snapshot;
    /**
     * 全景图base64
     */
    private String panorama;
    /**
     * 匹配图片base64
     */
    private String matchedImage;
    /**
     * 年龄
     */
    private int age;

    /**
     * 表情
     * FE_Unknown = 0,
     * FE_Smile,
     * FE_Angry,
     * FE_Sad,
     * FE_Normal,
     * FE_Panic,
     */
    private String expression;

    /**
     * 发型
     * HT_Unknown,
     * HT_LongHair,
     * HT_ShortHair,
     */
    private String hair;

    /**
     * 帽子
     * HS_Unknown,
     * HS_None,
     * HS_HatExist,
     */
    private String hat;

    /**
     * 眼镜类型
     * GT_Unknown,
     * GT_None,
     * GT_NormalGlasses,
     * GT_Sunglasses,
     */
    private String glassType;

    /**
     * 口罩
     * MM_Unkonwn,
     * MM_None,
     * MM_MaskExist,
     */
    private String mouthMask;

    /**
     * 胡子
     * MT_Unknown,
     * MT_None,
     * MT_MustacheExist,
     */
    private String mustache;

    /**
     * 是否命中 2: 未命中 1：命中
     */
    private int alarmMatch;

    /**
     *抓拍ID，避免数据重复推送
     */
    private Integer snapId;
}
