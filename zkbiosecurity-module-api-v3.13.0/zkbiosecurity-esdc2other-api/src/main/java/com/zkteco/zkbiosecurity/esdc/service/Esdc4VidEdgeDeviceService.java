package com.zkteco.zkbiosecurity.esdc.service;

import java.util.List;

import com.zkteco.zkbiosecurity.esdc.vo.Esdc2OtherDeviceItem;
import com.zkteco.zkbiosecurity.esdc.vo.Esdc4OtherEdgeDeviceItem;

/**
 * 视频桥接esdc主设备服务类
 *
 * <AUTHOR>
 * @date 2022/1/8 9:31
 * @since 1.0.0
 */
public interface Esdc4VidEdgeDeviceService {

	/**
	 * 获取所有主设备信息
	 *
	 * @return List<com.zkteco.zkbiosecurity.esdc.vo.Esdc4OtherEdgeDeviceItem>
	 * <AUTHOR>
	 * @date  2022-01-08 9:35
	 * @since 1.0.0
	 */
	default List<Esdc4OtherEdgeDeviceItem> getEdgeDeviceItems() {
		return null;
	}

	/**
	 * 录像存储
	 *
	 * @param deviceItem
	 * @param parentId
	 * @param sessionId
	 * @return boolean
	 * <AUTHOR>
	 * @date  2022-01-08 10:24
	 * @since 1.0.0
	 */
	default boolean videoStorage(Esdc2OtherDeviceItem deviceItem, String parentId, String sessionId){
		return false;
	}

}
