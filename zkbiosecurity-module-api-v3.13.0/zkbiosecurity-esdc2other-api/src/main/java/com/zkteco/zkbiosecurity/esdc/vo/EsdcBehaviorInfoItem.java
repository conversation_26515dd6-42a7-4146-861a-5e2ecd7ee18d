/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * BehaviorBean
 *
 * <AUTHOR> lwx960330
 * @version V100R001C00
 * @since : 2020年7月18日
 */
@Getter
@Setter
public class EsdcBehaviorInfoItem {
    /**
     * 拌线标记
     * true:拌线
     * false:区域
     */
    private boolean lineFlag;
    /**
     * 区域信息
     */
    private EsdcAreaInfoItem hwAreaBeanItem;
    /**
     * 拌线信息
     */
    private EsdcLineInfoItem hwLineBeanItem;
    /**
     * 截图base64
     */
    private String snapshot;
    /**
     * 全景图base64
     */
    private String panorama;

    /**
     * 目标方框
     */
    private List<EsdcDetectObjectInfoItem> detectObject;
}
