package com.zkteco.zkbiosecurity.esdc.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * PointBean 点
 *
 * <AUTHOR> lwx960330
 * @version V100R001C00
 * @since : 2020年7月18日
 */
@Getter
@Setter
public class EsdcPointBean {
    /**
     * 横坐标
     */
    @JSONField(name = "xValue")
    @JsonProperty("xValue")
    private int xValue;
    /**
     * 纵坐标
     */
    @JSONField(name = "yValue")
    @JsonProperty("yValue")
    private int yValue;
}
