package com.zkteco.zkbiosecurity.esdc.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 门禁事件推送到edc模块vo
 *
 * <AUTHOR>
 * @date 2020-11-02 13:34
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain=true)
@NoArgsConstructor
@AllArgsConstructor
public class Acc4EsdcTransactionItem implements Serializable {

    /** 设备SN */
    private String devSn;
    /** 读头名称 */
    private String readerName;
    /** 人员编号 */
    private String pin;
    /** 人员卡号 */
    private String cardNo;
    /** 事件时间 */
    private Date eventTime;
    /** 区域名称 */
    private String areaName;
    /** 设备id */
    private String devId;
    /** 设备名称 */
    private String devAlias;
    /** 人员名称 */
    private String name;
    /** 人员lastName */
    private String lastName;
    /**事件点名称*/
    private String eventPointName;
    /**事件名称*/
    private String eventName;
    /** 事件编号 */
    private Short eventNo;
    /** 事件点 */
    private String eventPointId;
    /** 门名称 */
    private String doorName;
    /** 门ID */
    private String doorId;
}
