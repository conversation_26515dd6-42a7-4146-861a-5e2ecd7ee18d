/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 16:06 2018/12/28
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.park.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 门禁门（门禁当停车场）
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 16:06 2018/12/28
 */
@Getter
@Setter
@Accessors(chain = true)
public class ParkGetAccDoorItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String name;

    private String deviceId;

    private String deviceAlias;

    private String deviceSn;

    private String notId;
}
