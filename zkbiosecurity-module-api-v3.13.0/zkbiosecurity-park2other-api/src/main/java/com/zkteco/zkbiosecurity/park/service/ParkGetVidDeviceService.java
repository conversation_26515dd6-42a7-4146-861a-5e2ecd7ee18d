/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 17:30 2018/11/27
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.park.service;

import com.zkteco.zkbiosecurity.park.vo.ParkGetVidDeviceItem;

/**
 * 视频设备（门禁当停车场）
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 17:30 2018/11/27
 */
public interface ParkGetVidDeviceService {

    /**
     * 根据视频通道ID获取视频设备信息
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/1/3 17:59
     * @param vidChannelId
     * @return com.zkteco.zkbiosecurity.park.vo.ParkGetVidDeviceItem
     */
    ParkGetVidDeviceItem getVidDeviceByVidChannelId(String vidChannelId);
}
