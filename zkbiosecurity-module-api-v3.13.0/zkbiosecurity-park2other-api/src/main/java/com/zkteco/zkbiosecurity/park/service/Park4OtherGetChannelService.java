package com.zkteco.zkbiosecurity.park.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.park.vo.ParkChannel4OtherItem;

import java.util.List;

/**
 * 停车通道信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
 * @date 2022/11/8 10:14
 * @since 1.0.0
 */
public interface Park4OtherGetChannelService {

    /**
     * @Description: getSelectItemByPage
     *
     * @param item:
     * @param pageNo:
     * @param pageSize:
     * @return: com.zkteco.zkbiosecurity.base.bean.Pager
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2022/11/8 10:42
     * @since: 1.0.0
     */
    default Pager getSelectItemByPage(ParkChannel4OtherItem item, int pageNo, int pageSize) { return null; }

    /**
     * @Description: 根据id获取停车通道
     *
     * @param channelId:
     * @return: com.zkteco.zkbiosecurity.park.vo.ParkChannel4OtherItem
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2022/11/8 10:34
     * @since: 1.0.0
     */
    default ParkChannel4OtherItem getChannelById(String channelId) { return null; }

    /**
     * @Description: 获取车场通道列表
     *
     * @return: java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2024/1/16 10:13
     * @since: 1.0.0
     */
    default List<SelectItem> getParkChannelList() {
        return null;
    }

}
