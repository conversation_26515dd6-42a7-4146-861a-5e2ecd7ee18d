/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 14:14 2019/1/8
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.park.service;

/**
 * 门禁当停车场远程开闸接口
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 14:14 2019/1/8
 */
public interface Park4AccDoorService {

    /**
     * 门禁当停车场远程开闸接口
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/1/8 14:18
     * @param openInterval 开闸时长
     * @param accDoorId 门禁门ID
     * @return java.lang.String
     */
    String openDoorForPark(int openInterval, String accDoorId);
}
