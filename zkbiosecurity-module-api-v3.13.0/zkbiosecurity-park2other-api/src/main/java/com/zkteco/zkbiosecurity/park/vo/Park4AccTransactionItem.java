package com.zkteco.zkbiosecurity.park.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
public class Park4AccTransactionItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private Date eventTime;
    private String doorId;
    private String cardNo;
    private String pin;
}
