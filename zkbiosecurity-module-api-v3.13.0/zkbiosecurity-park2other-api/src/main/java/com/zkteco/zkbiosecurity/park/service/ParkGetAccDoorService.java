/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 17:30 2018/11/27
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.park.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.park.vo.ParkGetAccDoorItem;

import java.util.List;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 17:30 2018/11/27
 */
public interface ParkGetAccDoorService {

    /**
     * 获取门禁门数据分页以及搜索（门禁当停车场，所属设备必须支持一人多卡）
     *
     * @param sessionId
     * @param condition
     * @param page
     * @param size
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2018/12/28 17:30
     */
    Pager getAccDoorForPark(String sessionId, ParkGetAccDoorItem condition, int page, int size);

    /**
     * 根据门禁门ID获取门信息
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/12 9:21
     * @param accDoorId
     * @return com.zkteco.zkbiosecurity.park.vo.ParkGetAccDoorItem
     */
    ParkGetAccDoorItem getAccDoorForParkById(String sessionId, String accDoorId);

    /**
     * 根据门禁门ids获取门禁门列表
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/12 9:21
     * @param accDoorIds
     * @return
     */
    List<ParkGetAccDoorItem> getAccDoorForParkByIds(String sessionId, List<String> accDoorIds);
}
