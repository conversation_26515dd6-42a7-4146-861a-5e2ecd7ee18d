package com.zkteco.zkbiosecurity.park.service;

import java.util.Collection;

/**
 * 其他模块使用停车通道删除需要实现
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
 * @date 2022/11/8 11:30
 * @since 1.0.0
 */
public interface ParkChannel4OtherService {

    /**
     * @Description: 通道删除
     *
     * @param ids:
     * @return: void
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2022/11/8 10:37
     * @since: 1.0.0
     */
    default void pushDeleteChannel(Collection<String> ids) {}
}
