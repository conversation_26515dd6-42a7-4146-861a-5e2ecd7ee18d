package com.zkteco.zkbiosecurity.park.vo;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 停车看板item
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
 * @since v1.0
 * @date 2021-08-06 13:38
 */
@Getter
@Setter
@Accessors(chain = true)
public class Park4StatisticalChartItem {

    /**
     * 车场区域名称
     */
    private List<String> parkingNameList;

    /**
     * 区域剩余车位数
     */
    private List<String> parkingSpacesList;

    /**
     * 剩余车位数
     */
    private Integer remainSpaceCount;

    /**
     * 总车位数
     */
    private Integer allSpaceCount;

    /**
     * 车牌
     */
    private List<String> carNumberList;

    /**
     * 停留小时
     */
    private List<String> stayHourList;

    /**
     * 设备在线数
     */
    private Integer onLineCount;

    /**
     * 设备离线数
     */
    private Integer offLineCount;

    /**
     * 在场车辆数
     */
    private Integer recordinCount;

    /**
     * 超时停留数
     */
    private Integer overtimeCount;

    /**
     * 进出异常数
     */
    private Integer unCount;

    /**
     * 进出趋势（日，月）横轴
     */
    private List<String> recordXList;

    /**
     * 进（日，月）
     */
    private List<String> inList;

    /**
     * 出（日，月）
     */
    private List<String> outList;

    /**
     * 进场车辆数总数
     */
    private Integer inSideCount;

    /**
     * 出场车辆数总数
     */
    private Integer outSideCount;

    /**
     * 收费统计趋势 横轴
     */
    private List<String> meterXList;

    /**
     * 收费统计（日，月）
     */
    private List<String> meterList;

    /**
     * 收费统计总数
     */
    private Double meterCount;

}
