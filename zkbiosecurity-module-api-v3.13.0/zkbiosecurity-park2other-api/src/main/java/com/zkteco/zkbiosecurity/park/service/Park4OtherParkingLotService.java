package com.zkteco.zkbiosecurity.park.service;

import com.zkteco.zkbiosecurity.base.bean.SelectItem;

import java.util.List;

/**
 * 车场信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
 * @date 2024/1/16 10:23
 * @since 1.0.0
 */
public interface Park4OtherParkingLotService {

    /**
     * @Description: 获取车场列表
     *
     * @return: java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2024/1/16 10:13
     * @since: 1.0.0
     */
    default List<SelectItem> getParkingLotList() { return null; }

    /**
     * @Description: 获取车场区域列表
     *
     * @return: java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2024/1/16 10:13
     * @since: 1.0.0
     */
    default List<SelectItem> getParkingAreaList() { return null; }

}
