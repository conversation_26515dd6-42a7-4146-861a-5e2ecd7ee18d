/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 17:30 2018/11/27
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.park.service;

/**
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 17:30 2018/11/27
 * @version v1.0
 */
public interface ParkGetAttPointService {

    /**
     * 根据设备ID，判断是否在考情点中使用
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2018/11/27 17:32
     * @param devId
     * @return boolean
     */
    boolean isUsedByAttPoint(String devId);
}
