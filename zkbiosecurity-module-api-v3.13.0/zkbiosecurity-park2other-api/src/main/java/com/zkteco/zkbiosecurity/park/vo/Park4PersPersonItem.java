package com.zkteco.zkbiosecurity.park.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class Park4PersPersonItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**车牌区域*/
    private String carAreas;

    /**车牌号码*/
    private String parkCarNumbers;

    /**人员ID*/
    private String personId;

    /**人员PIN*/
    private String personPin;

    private String personName;

    private String personLastName;

    /**人员手机号*/
    private String personMobilePhone;

    private String deptId;

    private String deptName;
    
	/**更新停车设置*/
	private Boolean updateParkSet;
}
