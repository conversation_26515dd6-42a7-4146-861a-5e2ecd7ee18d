/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 14:14 2019/1/8
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.park.service;

/**
 * 门禁当停车场判断设备是否开启后台验证
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 14:14 2019/1/8
 */
public interface Park4AccDeviceService {

    /**
     * 门禁当停车场判断设备是否开启后台验证
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/1/10 18:09
     * @param accDeviceId 门禁设备ID
     * @return boolean true启用，false未启用
     */
    boolean enableBgVerify(String accDeviceId);
}
