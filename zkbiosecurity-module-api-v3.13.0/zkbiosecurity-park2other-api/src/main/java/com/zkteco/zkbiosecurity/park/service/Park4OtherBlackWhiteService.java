package com.zkteco.zkbiosecurity.park.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.park.vo.ParkBlackWhite4OtherItem;

/**
 * 其他模块设置允许、禁止名单
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
 * @date 2023/11/10 9:38
 * @since 1.0.0
 */
public interface Park4OtherBlackWhiteService {

    /**
     * @Description: 添加允许、禁止车牌
     *
     * @param parkBlackWhite4OtherItem:
     * @return: com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2023/11/10 10:20
     * @since: 1.0.0
     */
    default ZKResultMsg saveItem(ParkBlackWhite4OtherItem parkBlackWhite4OtherItem) {
        return null;
    }

    /**
     * @Description: 判断是否允许、禁止车牌
     *
     * @param carNumber:
     * @param status:
     * @return: boolean
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2023/11/10 10:20
     * @since: 1.0.0
     */
    default boolean checkBlackWhite(String carNumber, short status) {
        return false;
    }

    /**
     * @Description: 删除允许、禁止车牌
     *
     * @param parkBlackWhiteId:
     * @return: void
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2023/11/10 10:21
     * @since: 1.0.0
     */
    default void deleteByIds(String parkBlackWhiteId) {
        return;
    }

    /**
     * @Description: 根据车牌号码获取允许、禁止车牌信息
     *
     * @param carNumber:
     * @return: com.zkteco.zkbiosecurity.park.vo.ParkBlackWhite4OtherItem
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2023/11/10 10:22
     * @since: 1.0.0
     */
    default ParkBlackWhite4OtherItem getParkBlackWhiteItemsByCarNumber(String carNumber) {
        return null;
    }

    /**
     * @Description: 根据id获取允许、禁止车牌信息
     *
     * @param parkBlackWhiteId:
     * @return: com.zkteco.zkbiosecurity.park.vo.ParkBlackWhite4OtherItem
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2023/11/10 10:22
     * @since: 1.0.0
     */
    default ParkBlackWhite4OtherItem getParkBlackWhiteItemById(String parkBlackWhiteId) {
        return null;
    }

    /**
     * @Description: 获取车牌默认省份
     *
     * @return: java.lang.String
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2023/11/10 10:23
     * @since: 1.0.0
     */
    default String getDefaultCarPlateProvince() {
        return "无";
    }
}
