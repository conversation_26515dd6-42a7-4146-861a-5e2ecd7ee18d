package com.zkteco.zkbiosecurity.park.service;

import java.util.List;

import com.zkteco.zkbiosecurity.park.vo.Park4StatisticalChartItem;

/**
 * 停车看板
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
 * @since v1.0
 * @date 2021-08-06 13:38
 */
public interface Park4StatisticalChartService {

    /**
     * 车辆管理数
     *
     * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
     * @since v1.0
     * @date 2021-08-06 13:37
     * @param: []
     * @return: java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4StatisticalChartItem>
     */
    default List<Park4StatisticalChartItem> getVehicleManageTotal() {
        return null;
    }

    /**
     * 设备数
     *
     * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
     * @since v1.0
     * @date 2021-08-06 13:37
     * @param: []
     * @return: java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4StatisticalChartItem>
     */
    default List<Park4StatisticalChartItem> getDeviceTotal() {
        return null;
    }

    /**
     * 在场车辆停留时长Top5
     *
     * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
     * @since v1.0
     * @date 2021-08-06 13:37
     * @param: [sessionId]
     * @return: java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4StatisticalChartItem>
     */
    default List<Park4StatisticalChartItem> getParkRecordinStayTime(String sessionId) {
        return null;
    }

    /**
     * 车辆进出趋势
     *
     * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
     * @since v1.0
     * @date 2021-08-06 13:37
     * @param: [timeType]
     * @return: java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4StatisticalChartItem>
     */
    default List<Park4StatisticalChartItem> getVehicleAccessTrend(String timeType) {
        return null;
    }

    /**
     * 收费统计
     *
     * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
     * @since v1.0
     * @date 2021-08-06 13:36
     * @param: [timeType]
     * @return: java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4StatisticalChartItem>
     */
    default List<Park4StatisticalChartItem> getMeterTotal(String timeType) {
        return null;
    }

    /**
     * 异常事件监控
     *
     * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
     * @since v1.0
     * @date 2021-08-06 13:36
     * @param: []
     * @return: java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4StatisticalChartItem>
     */
    default List<Park4StatisticalChartItem> getParkTransaction() {
        return null;
    }

    /**
     * 区域剩余车位数
     *
     * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
     * @since v1.0
     * @date 2021-08-06 13:36
     * @param: []
     * @return: java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4StatisticalChartItem>
     */
    default List<Park4StatisticalChartItem> getRemainParkingLot() {
        return null;
    }

    /**
     * @Description: 车位数
     *
     * @return: com.zkteco.zkbiosecurity.park.vo.Park4StatisticalChartItem
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2024/8/30 11:51
     * @since: 1.0.0
     */
    default List<Park4StatisticalChartItem> getSpaceParkingLot() {
        return null;
    }
}
