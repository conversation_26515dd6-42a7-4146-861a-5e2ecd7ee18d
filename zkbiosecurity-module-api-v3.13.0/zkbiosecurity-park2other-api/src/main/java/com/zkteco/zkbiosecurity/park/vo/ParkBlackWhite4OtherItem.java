package com.zkteco.zkbiosecurity.park.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 黑白名单
 *
 * @return:
 * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
 * @date:  2023/11/10 9:51
 * @since: 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class ParkBlackWhite4OtherItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String id;

    /** 省份 */
    private String carArea;

    /** 车牌号码 */
    private String carNumber;

    /** 0：白名单、1：黑名单 */
    private Short state;

    /** 开始时间段 */
    private Date startTime;

    /** 结束时间段 */
    private Date endTime;

    /** 来源模块 */
    private String sourceType;

    /** 车场ID(设置id、下发到指定车场，不设置下发全部车场) */
    private String parkingLotInfId;

    private String number;

    private String defaultPlate;

}
