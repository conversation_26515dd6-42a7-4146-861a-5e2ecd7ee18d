package com.zkteco.zkbiosecurity.park.service;

import com.zkteco.zkbiosecurity.park.vo.Park4PersPersonItem;

import java.util.List;
import java.util.Map;

public interface Park4PersPersonService {
	/**
	 * 提供人事调用停车人员编辑接口，即停车人员属性编辑
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/5/2 10:54
	 * @param parkPerson
	 * @return java.lang.Boolean
	 */
	Boolean editParkPerson(Park4PersPersonItem parkPerson);

	/**
	 * 删除检查
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/5/4 13:36
	 * @param personIds
	 * @return void
	 */
	Boolean checkDelPerson(String personIds);

	/**
	 * 删除人员
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/5/4 11:58
	 * @param personIds
	 * @return java.lang.Boolean
	 */
	void delParkPerson(String personIds);

	/**
	 * 离职人员
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/5/4 11:58
	 * @param personIds
	 * @return java.lang.Boolean
	 */
	Boolean leaveParkPerson(String personIds);

	/**
	 * 批量调整部门通知，需要切换人员的部门权限
	 * @param personIds
	 * @param deptId
	 */
	void batchDeptChange(List<String> personIds, String deptId);

	/**
	 * 人员导入，或者设备上传批量人员
	 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
	 * @date 2018/8/22 11:02
	 * @param parkPersons
	 * @return void
	 */
	void batchPerson(List<Park4PersPersonItem> parkPersons);

	/**
	* 获取已存在的车牌号
	* <AUTHOR>
	* @param carNumberList
	* @return java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4PersPersonItem>
	* @date 2019/7/15 16:33
	*/
	List<Park4PersPersonItem> getParkCarNumberIn(List<String> carNumberList);

	/**
	 * 根据人员pin获取
	 * <AUTHOR>
	 * @param persPersonPinList
	 * @return java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4PersPersonItem>
	 * @date 2019/7/15 18:13
	 */
	List<Park4PersPersonItem> findbyPersPersonPinIn(List<String> persPersonPinList);

	/**
	* 根据人员id获取
	* <AUTHOR>
	* @param persPersonIdList
	* @return java.util.List<com.zkteco.zkbiosecurity.park.vo.Park4PersPersonItem>
	* @date 2019/7/22 9:23
	*/
	List<Park4PersPersonItem> findbyPersPersonIdIn(List<String> persPersonIdList);

	/**
	 * 人事导入车牌
	 * <AUTHOR>
	 * @param pinAndCarPlatesMap
	 * @return void
	 * @date 2019/7/16 10:51
	 */
	void importPersonData(Map<String, List<String>> pinAndCarPlatesMap, Map<String, Park4PersPersonItem> pinAndPark4PersPersonMap);
}
