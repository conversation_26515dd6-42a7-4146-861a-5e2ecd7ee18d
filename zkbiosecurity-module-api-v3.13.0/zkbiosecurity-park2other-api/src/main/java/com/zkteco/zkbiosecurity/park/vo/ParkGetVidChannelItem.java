/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 13:40 2018/12/29
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.park.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 13:40 2018/12/29
 */
@Getter
@Setter
@Accessors(chain = true)
public class ParkGetVidChannelItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String name;

    /*设备信息*/
    private String alias;

    private Integer port;

    private String host;

    private String username;

    private String commPwd;

    /** VMS设备SN */
    private String devSn;

    /** VMS设备通道号 */
    private String channelNo;
}
