/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 14:14 2019/1/8
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.park.service;

/**
 * 门禁当停车场视频抓拍
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 14:14 2019/1/8
 */
public interface Park4VidChannelService {

    /**
     * 门禁当停车场视频抓拍
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/1/8 14:18
     * @param vidChannelId 视频通道ID
     * @return java.lang.String 抓拍成功返回base64编码图片，抓拍失败返回空
     */
    String captureForPark(String vidChannelId);
}
