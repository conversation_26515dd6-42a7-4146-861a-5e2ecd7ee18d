/**
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 17:30 2018/11/27
 * @version V1.0
 */
package com.zkteco.zkbiosecurity.park.service;

import com.zkteco.zkbiosecurity.park.vo.ParkGetVidChannelItem;

import java.util.List;

/**
 * 视频通道（门禁当停车场）
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @version v1.0
 * @date Created In 17:30 2018/11/27
 */
public interface ParkGetVidChannelService {

    /**
     * 获取未被绑定的视频通道（门禁当停车场）
     * parkDeviceDao.findByIdNotInOrIdIn(otherId, editIds);)
     *
     * @param sessionId
     * @param editIds
     * @param otherId
     * @return java.util.List<com.zkteco.zkbiosecurity.park.vo.ParkGetVidChannelItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2018/12/29 14:02
     */
    List<ParkGetVidChannelItem> getVidChannelForPark(String sessionId, String[] editIds, String otherId);

    /**
     * 根据视屏通道Id获取视屏通道信息
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/12 9:24
     * @param vidChannelId
     * @return com.zkteco.zkbiosecurity.park.vo.ParkGetVidChannelItem
     */
    ParkGetVidChannelItem getVidChannelForParkById(String sessionId, String vidChannelId);

    /**
     * 根据视频通道ids获取视屏通道信息列表
     *
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2019/6/12 9:25
     * @param vidChannelIds
     * @return java.util.List<com.zkteco.zkbiosecurity.park.vo.ParkGetVidChannelItem>
     */
    List<ParkGetVidChannelItem> getVidChannelForParkByIds(String sessionId, List<String> vidChannelIds);
}
