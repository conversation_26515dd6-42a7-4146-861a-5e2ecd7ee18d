package com.zkteco.zkbiosecurity.park.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 通道信息
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
 * @date 2022/11/8 10:21
 * @since 1.0.0
 */

@Getter
@Setter
@Accessors(chain = true)
public class ParkChannel4OtherItem implements Serializable {
    private static final long serialVersionUID = 3904587823580168512L;

    /** id */
    private String id;
    /** 名称 */
    private String name;
    /** 岗亭id */
    private String pavilioId;
    /** 岗亭名称 */
    private String pavilioName;
    /** 通道状态（1：大车场入口、2：大车场出口、3：小车场入口、4：小车场出口、5：中央缴费定点、6：中央缴费出口） */
    private Short state;
    /** 固定车开闸方式（1：确认放行，2：直接放行） */
    private Short monthCarOpenType;
    /** 临时车开闸方式（1：确认放行，2：直接放行） */
    private Short tempCarOpenType;
    private String notId;

}
