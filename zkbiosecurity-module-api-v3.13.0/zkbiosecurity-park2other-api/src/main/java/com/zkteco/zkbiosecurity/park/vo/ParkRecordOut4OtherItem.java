package com.zkteco.zkbiosecurity.park.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 车辆进场记录
 *
 * <AUTHOR> href="mailto:<EMAIL>">yuliang.an</a>
 * @date 2023/3/17 15:33
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain=true)
public class ParkRecordOut4OtherItem implements Serializable {
    private static final long serialVersionUID = -1534501974147302141L;

    /** 车牌号码 */
    private String carNumber;
    /** 车辆类型名称 */
    private String carTypeName;
    /** 进场时间 */
    private Timestamp checkInTime;
    /** 出场时间 */
    private Timestamp checkOutTime;
    /** 出场通道id */
    private String channelOutId;
    /** 出场通道名称 */
    private String channelOutName;
    /** 区域名称 */
    private String parkingAreaName;

}
