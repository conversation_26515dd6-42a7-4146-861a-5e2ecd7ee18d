package com.zkteco.zkbiosecurity.whatsapp.service;

/**
 * 其他模块调用whatsapp的交互接口
 *
 * <AUTHOR>
 * @date 2022/2/21 18:22
 */
public interface Whatsapp4OtherService {
    default boolean checkShowWhatsappLicense() {
        return false;
    }

    default void sendToWhatsapp(String receiver, String whatsappContent, String modelType) {
        return;
    }

    /**
     * 检测whatsapp参数设置
     * 
     * @return boolean
     * <AUTHOR>
     * @throws
     * @date 2023-03-21 14:08
     * @since 1.0.0
     */
    default boolean completeWhatsAppModemInfo() {
        return false;
    }
}
