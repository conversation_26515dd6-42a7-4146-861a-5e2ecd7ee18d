package com.zkteco.zkbiosecurity.pos.service;

import java.util.List;

import com.zkteco.zkbiosecurity.pos.vo.Pos4PersPersonItem;

public interface Pos4PersPersonService {
	/**
	 * 提供人事调用消费人员编辑接口，即消费人员属性编辑
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/5/2 10:29
	 * @param posPerson
	 * @return java.lang.Boolean
	 */
	Boolean editPosPerson(Pos4PersPersonItem posPerson);

	/**
	 * 删除检查
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/5/4 13:36
	 * @param personIds
	 * @return void
	 */
	Boolean checkDelPerson(String personIds);

	/**
	 * 删除人员
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/5/4 11:58
	 * @param personIds
	 * @return java.lang.Boolean
	 */
	void delPosPerson(String personIds);

	/**
	 * 离职人员
	 * <AUTHOR> href="mailto:<EMAIL>">juvenile.li</a>
	 * @date 2018/5/4 11:58
	 * @param personIds
	 * @return java.lang.Boolean
	 */
	Boolean leavePosPerson(String personIds);

	/**
	 * 批量调整部门
	 * @param personIds
	 * @param deptId
	 */
	void batchDeptChange(List<String> personIds, String deptId);
}
