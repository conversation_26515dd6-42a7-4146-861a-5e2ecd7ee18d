package com.zkteco.zkbiosecurity.pid.service;

import com.zkteco.zkbiosecurity.pid.vo.Pid4OtherPersVerifyItem;
import com.zkteco.zkbiosecurity.pid.vo.Pid4OtherVisVerifyItem;

import java.util.List;

/**
 * 人证调用，模块实现，通知其他模块接口
 *
 * <AUTHOR> href:"mailto:<EMAIL>">gaoqi.lian</a>
 * @date Created In 13:56 2022/11/30
 * @version v1.0
 */
public interface Pid4OtherNotifyService {

    /**
     *
     * 设备删除通知
     *
     * @param deviceIdList:
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2022/12/6 15:46
     * @since 1.0.0
     */
    default void deleteDeviceNotify(List<String> deviceIdList) {}


    /**
     * 比对记录通知
     * 
     * @param pid4OtherPersVerifyItem: 
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/11/29 8:14
     * @since 1.0.0
     */
    default void persVerifyNotify(Pid4OtherPersVerifyItem pid4OtherPersVerifyItem) {}
    
    /**
     * 核验记录通知
     *
     * @param 
     * @return void
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/11/29 8:14
     * @since 1.0.0
     */
    default void visVerifyNotify(Pid4OtherVisVerifyItem pid4OtherVisVerifyItem) {}

}