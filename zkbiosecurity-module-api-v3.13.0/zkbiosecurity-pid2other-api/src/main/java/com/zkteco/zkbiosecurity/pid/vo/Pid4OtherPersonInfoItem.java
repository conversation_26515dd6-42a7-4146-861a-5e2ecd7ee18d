package com.zkteco.zkbiosecurity.pid.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020/11/5 14:02
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class Pid4OtherPersonInfoItem {
    /** 人员id */
    private String personId;
    /** 人员编号 */
    private String pin;
    /** 名字 */
    private String name;
    /** 姓氏 */
    private String lastName;
    /** 卡号 */
    private String cardNo;
    /** 密码 */
    private String personPwd;
}
