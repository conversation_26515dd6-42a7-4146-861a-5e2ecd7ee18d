package com.zkteco.zkbiosecurity.pid.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.pid.vo.Pid4OtherDeviceItem;

import java.util.Collection;
import java.util.List;

public interface Pid4OtherService {

    /**
     * 获取人证设备双列表数据
     * 
     * @param condition:
     * @param page:
     * @param size:
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/11/28 10:08
     * @since 1.0.0
     */
    default Pager getDeviceSelectList(Pid4OtherDeviceItem condition, int page, int size) {
        return new Pager();
    }

    /**
     * 根据设备ID获取设备信息
     *
     * @param deviceId:
     * @return com.zkteco.zkbiosecurity.pid.vo.Pid4OtherDeviceItem
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/11/28 11:14
     * @since 1.0.0
     */
    default Pid4OtherDeviceItem getDeviceItemById(String deviceId) {
        return null;
    }

    /**
     * 根据设备ID集合，获取设备信息集合
     * 
     * @param deviceIds:
     * @return java.util.List<com.zkteco.zkbiosecurity.pid.vo.Pid4OtherDeviceItem>
     * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
     * @date 2024/11/28 11:14
     * @since 1.0.0
     */
    default List<Pid4OtherDeviceItem> getDeviceItemListByIds(Collection<String> deviceIds) {
        return null;
    }
}
