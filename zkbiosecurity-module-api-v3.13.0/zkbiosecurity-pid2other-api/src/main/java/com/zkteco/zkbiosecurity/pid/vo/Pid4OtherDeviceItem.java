package com.zkteco.zkbiosecurity.pid.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class Pid4OtherDeviceItem {

    /** 主键 */
    private String id;

    /** 设备名称 */
    private String name;

    /** 设备序列号 */
    private String sn;

    /** ip地址 */
    private String ip;

    /** 区域id */
    private String authAreaId;

    /** 判断是左列表noSelect、还是右列表select */
    private String type;

    /** 当前选中的ids */
    private String selectId;

    /** 当前登陆系统用户ID */
    private String userId;
}