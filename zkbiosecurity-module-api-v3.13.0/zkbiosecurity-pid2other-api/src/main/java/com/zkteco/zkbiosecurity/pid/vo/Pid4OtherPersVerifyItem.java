package com.zkteco.zkbiosecurity.pid.vo;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class Pid4OtherPersVerifyItem {

    /** 人员编号 */
    private String personPin;

    /** 姓名 */
    private String personName;

    /** 设备名称 */
    private String deviceName;

    /** 核验时间 */
    private Date verifyTime;

    /** 核验方式 */
    private String verifyMode;

    /** 核验结果 */
    private String verifyResult;

    /** 现场抓拍图片 */
    private String capturePhoto;

    /** 部门编号 */
    private String deptCode;

    /** 部门名称 */
    private String deptName;

    /** 区域编号 */
    private String authAreaCode;

    /** 区域名称 */
    private String authAreaName;

    /**
     * 设备Id
     */
    private String deviceId;
}
