package com.zkteco.zkbiosecurity.pid.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @date 2020/11/5 14:16
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
@ToString
public class Pid4OtherBioTemplateItem {
    private String personId;
    private String pin;
    private Short validType;
    private Short bioType;
    private String version;
    private String template;
    private Short templateNo;
    private Short templateNoIndex;
    private Boolean duress;
}
