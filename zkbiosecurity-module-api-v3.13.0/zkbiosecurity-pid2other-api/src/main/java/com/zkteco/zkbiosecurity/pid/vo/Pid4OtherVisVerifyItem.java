package com.zkteco.zkbiosecurity.pid.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 核验记录
 * <AUTHOR>
 * @date 2025/3/21 17:30
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Pid4OtherVisVerifyItem {

    /*身份证号*/
    private String idCardNumber;

    /** 设备名称 */
    private String deviceName;

    /** 核验时间 */
    private Date verifyTime;

    /** 核验方式 */
    private String verifyMode;

    /** 核验结果 */
    private String verifyResult;

    /** 现场抓拍图片 */
    private String capturePhoto;

    /** 部门编号 */
    private String deptCode;

    /** 部门名称 */
    private String deptName;

    /** 区域编号 */
    private String authAreaCode;

    /** 区域名称 */
    private String authAreaName;

    /** 设备Id */
    private String deviceId;

}
