package com.zkteco.zkbiosecurity.pid.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
@ToString
public class Pid4PersPersonItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 人员id */
    private String personId;

    /** 是否有下发人证设备权限 */
    @Deprecated
    private Boolean isIssued;

    /** 人员编号 */
    private String personPin;

    /** 卡号 */
    private String cardNo;

    /** 操作类型，新增(add)/更新(update) */
    private String opType;

    /** 区域ids */
	private String areaIds;

    public Pid4PersPersonItem() {
        super();
    }

    public Pid4PersPersonItem(String personId) {
        super();
        this.personId = personId;
    }

}
