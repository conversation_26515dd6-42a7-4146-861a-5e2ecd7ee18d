package com.zkteco.zkbiosecurity.pid.service;

import com.zkteco.zkbiosecurity.pid.vo.Pid4OtherBioTemplateItem;
import com.zkteco.zkbiosecurity.pid.vo.Pid4OtherPersonInfoItem;
import com.zkteco.zkbiosecurity.pid.vo.Pid4PersPersonItem;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface Pid4PersPersonService {

    /**
     * 提供人事调用人证人员编辑接口，即人证人员属性编辑
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年5月8日 下午2:09:37
     * @param pidPerson
     * @return
     */
    Boolean editPidPerson(Pid4PersPersonItem pidPerson);

    /**
     * 删除检查
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年5月8日 下午2:10:00
     * @param personIds
     * @return
     */
    Boolean checkDelPerson(String personIds);

    /**
     * 删除人员
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年5月8日 下午2:10:16
     * @param personIds
     */
    void delPidPerson(String personIds);

    /**
     * 离职人员
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年5月8日 下午2:10:42
     * @param personIds
     * @return
     */
    Boolean leavePidPerson(String personIds);

    /**
     * 批量保存导入人员信息
     * 
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年8月30日 上午11:25:57
     * @param pidPersons
     */
    void batchImportPerson(List<Pid4PersPersonItem> pidPersons);

    /**
     * 获取pid人员扩展参数
     *
     * <AUTHOR> href="mailto:<EMAIL>">seven.wu</a>
     * @date 2020-03-18 11:00
     * @param personId
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
    default Map<String, String> getPidPersonExtParam(String personId) {
        return null;
    };
}
