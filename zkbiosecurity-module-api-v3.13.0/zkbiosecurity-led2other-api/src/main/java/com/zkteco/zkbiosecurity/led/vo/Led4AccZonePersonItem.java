package com.zkteco.zkbiosecurity.led.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
@Setter
@Getter
@Accessors(chain = true)
public class Led4AccZonePersonItem implements Serializable
{
	private static final long serialVersionUID = 1L;
	
	private String fromZoneId;
	private String zoneId;
	private String lastAccessReader;
	private String pin;
	private String personName;
	private String lastName;
	private String deptId;
	private String deptName;
}
