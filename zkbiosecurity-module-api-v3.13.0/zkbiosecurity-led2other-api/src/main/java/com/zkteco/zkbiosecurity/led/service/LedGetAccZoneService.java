package com.zkteco.zkbiosecurity.led.service;

import com.zkteco.zkbiosecurity.base.bean.SelectItem;
import com.zkteco.zkbiosecurity.led.vo.LedGetAccZoneItem;

import java.util.List;

/**
 * led桥接门禁区域
 *
 * <AUTHOR>
 * @date 2018-11-19 13:56
 */
public interface LedGetAccZoneService {

    List<LedGetAccZoneItem> getItemsByIds(String zoneIds);

    LedGetAccZoneItem getItemById(String zoneId);

    List<SelectItem> getZoneList(String zoneExpOutside);

    Integer getPersonCountByZoneId(String zoneId);

    Integer getDeptPersonCountByZoneId(String zoneId, List<String> deptIds);
}
