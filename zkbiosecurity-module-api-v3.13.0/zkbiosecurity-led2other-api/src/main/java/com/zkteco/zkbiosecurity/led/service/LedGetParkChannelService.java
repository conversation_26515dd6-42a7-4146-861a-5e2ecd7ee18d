package com.zkteco.zkbiosecurity.led.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.SelectItem;

/**
 *
 *
 * <AUTHOR>
 * @DATE 2020-09-27 17:23
 * @since 1.0.0
 */
public interface LedGetParkChannelService {

    /**
     * 获取停车通道列表
     * 
     * @return java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     * <AUTHOR>
     * @throws @date 2020-09-28 15:37
     * @since 1.0.0
     */
    default List<SelectItem> getParkChannelList() {
        return null;
    }

    /**
     * 获取停车车场区域列表
     * 
     * @return java.util.List<com.zkteco.zkbiosecurity.base.bean.SelectItem>
     * <AUTHOR>
     * @throws
     * @date 2024-01-09 17:14
     * @since 1.0.0
     */
    default List<SelectItem> getParkingAreaList() {
        return null;
    }
}