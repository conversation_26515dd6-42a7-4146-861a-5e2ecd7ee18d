package com.zkteco.zkbiosecurity.led.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *
 *
 * <AUTHOR>
 * @DATE 2020-09-28 15:22
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class Led4ParkChannelItem implements Serializable {

    private static final long serialVersionUID = 1L;

    private String channelId;

    private String parkingAreaId;

    /** 公司名称 */
    private String company;

    /** 时间 */
    private String date;

    /** 车牌 */
    private String carPlate;

    /** 系统提示语 */
    private String prompt;

    /** 剩余车位 */
    private String remainPark;

    /** 车辆数量 */
    private String carNumber;

}
