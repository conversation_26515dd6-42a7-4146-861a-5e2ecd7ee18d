package com.zkteco.zkbiosecurity.led.service;

import com.zkteco.zkbiosecurity.led.vo.Led4ParkChannelItem;

/**
 *
 *
 * <AUTHOR>
 * @DATE 2020-09-28 14:43
 * @since 1.0.0
 */
public interface Led4ParkChannelService {
    /**
     * 发送数据到LED
     * 
     * @param led4ParkChannelItem:
     * @return void
     * <AUTHOR>
     * @throws @date
     *             2020-09-28 15:37
     * @since 1.0.0
     */
    default void sendDataToLed(Led4ParkChannelItem led4ParkChannelItem) {}
}