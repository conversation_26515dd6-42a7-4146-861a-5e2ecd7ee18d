package com.zkteco.zkbiosecurity.led.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 门禁区域
 * 
 * @author: yibiao.shen
 * @date: 2018-03-06 16:36:58
 */
@Setter
@Getter
@Accessors(chain = true)
public class LedGetAccZoneItem extends BaseItem {
	/** 主键 */
	private String id;

	/**上级区域id */
	private String parentZoneId;

	/**编号*/
	private String code;

	/**区域名称 */
	private String name;

	/**备注*/
	private String remark;

	/**初始化标识*/
	private Boolean initFlag;

	private String inId;
	
}
