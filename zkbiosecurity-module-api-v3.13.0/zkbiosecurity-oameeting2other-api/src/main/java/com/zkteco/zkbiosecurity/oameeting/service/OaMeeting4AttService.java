package com.zkteco.zkbiosecurity.oameeting.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.oameeting.vo.Meeting4AttDeviceSelect;

import java.util.List;

/**
 * 会议模块调用att设备
 *
 * <AUTHOR>
 * @date 2022-03-29 14:58
 * @since 1.0.0
 */
public interface OaMeeting4AttService {

    /**
     * @Description 下发人员信息至设备
     * <AUTHOR>
     */
    void setPersonToAttDev(List<String> personIdList, List<String> attDeviceIdList);

    /**
    * @Description 获取考勤设备
    * <AUTHOR>
    */
    Pager getAttDevice(Meeting4AttDeviceSelect condition, int pageNo, int pageSize);

    /**
     * @Description 获取考勤设备
     * <AUTHOR>
     */
    Meeting4AttDeviceSelect getAttDevice(String sn);

    Pager getItemByAuthFilter(String sessionId, Meeting4AttDeviceSelect Meeting4AttDeviceSelect, int pageNo, int pageSize);

    Meeting4AttDeviceSelect getItemById(String devId);

    /**
     * 获取所有考勤设备管理员
     * <AUTHOR>
     * @date 2019/11/12 9:56
     * @return
     */
    List<String> getAllAttDevAdminPersonId();
}
