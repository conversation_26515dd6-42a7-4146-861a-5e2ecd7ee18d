package com.zkteco.zkbiosecurity.oameeting.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
/**
 * 会议对接其他模块接口
 *
 * <AUTHOR>
 * @date 2022-03-29 18:02
 * @since 1.0.0
 */
public interface OaMeeting4OtherGetMeetingInfoService {

    /**
     * 园区会议室当日使用率数据
     *
     * <AUTHOR>
     * @date 2022-03-29 17:59
     * @param
     * @since 1.0.0
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg getGardenMeetRoomUseRateData();

    /**
     * 判断考勤设备是否被会议模块占用
     * <AUTHOR>
     * @date  2023/3/8 15:03
     * @param devIds
     * @since 1.0.0
     * @return java.lang.Boolean
     */
    Boolean checkAttDeviceIsUsedByDevIds(String devIds);
}
