package com.zkteco.zkbiosecurity.oameeting.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 会议选择考勤设备vo
 * 
 * <AUTHOR>
 * @date 2022-03-30 10:08
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class Meeting4AttDeviceSelect extends BaseItem {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 设备序列号
     */
    private String devSn;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 设备型号
     */
    private String devModel;

    /**
     * 固件版本号
     */
    private String fwVersion;
    /**
     * ip地址
     */
    private String ipAddress;

    /**
     * 区域名称
     */
    private String authAreaName;

    /**
     * 区域编号
     */
    private String authAreaCode;

    /**
     * 启用状态
     */
    private Boolean status;
    /**
     * 在线状态
     */
    private String onlineStatus;

    /**
     * 是否登记机
     */
    private Boolean isRegDevice;

    /**
     * 待执行命令数
     */
    private Integer waitCmdCount;

    /**
     * 人员数
     */
    private Integer personCount;

    /**
     * 指纹数
     */
    private Integer fpCount;

    /**
     * 人脸数
     */
    private Integer faceCount;

    /**
     * 指纹版本
     */
    private String fpVersion;

    /**
     * 面部版本
     */
    private String faceVersion;

    /**
     * 通讯端口
     */
    private Integer commPort;

    /**
     * 通信方式
     */
    private String protocol;

    /**
     * 通信密码
     */
    private String pushcommkey;

    /**
     * 数据更新标志
     */
    private String updateFlag;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 刷新间隔时间(分)
     */
    private Short transInterval;

    /**
     * 定时传送时间(如：00:00;14:05)
     */
    private String transTimes;

    /**
     * 实时上传数据
     */
    private Boolean realTime;

    /**
     * 和服务器通讯的最大命令个数
     */
    private Short cmdCount;

    /**
     * 查询记录时间（秒）
     */
    private Short searchInterval;

    /**
     * 数据下发标志
     */
    private String dataDownFlag;

    /**
     * 记录数
     */
    private Integer recordCount;

    /**
     * 区域Id
     */
    private String areaId;

    private String inAreaId;

    /**
     * 面部9.0时间戳
     */
    private String bioDataStamp;

    /**
     * 是否加密
     */
    private String encrypt;

    /**
     * 支持功能，info value10
     */
    private String attSupportFunList;

    /**
     * 设备用途，为 1 时作会议用途
     */
    private String productTypeNotIn;

    private String productType;

    private String notInId;

}