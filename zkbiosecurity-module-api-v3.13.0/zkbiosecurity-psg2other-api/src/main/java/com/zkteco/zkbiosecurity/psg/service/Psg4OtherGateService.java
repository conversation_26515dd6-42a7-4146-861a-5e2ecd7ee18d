package com.zkteco.zkbiosecurity.psg.service;

import java.util.List;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;
import com.zkteco.zkbiosecurity.psg.vo.Psg4OtherGateItem;

/**
 * 通道闸机对外接口
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/11/25 11:14
 * @since 1.0.0
 */
public interface Psg4OtherGateService {

    /**
     * 远程操作楼层
     *
     * @param type normalOpenGate openOutGate openInGate
     * @param opInterval 操作间隔
     * @param gateId 闸机ID
     * @return
     */
    default ZKResultMsg operate(String type, int opInterval, String gateId) {
        return null;
    }

    /**
     * @Description: 远程操作楼层（不校验结果）
     *
     * @param type:
     * @param opInterval:
     * @param gateId:
     * @return: void
     * @author: <a href="mailto:<EMAIL>">yuliang.an</a>
     * @date:  2025/5/15 13:25
     * @since: 1.0.0
     */
    default void operateNoneReturn(String type, int opInterval, String gateId) {}

    default List<Psg4OtherGateItem> getItemsByIds(List<String> gateIdList) {
        return null;
    }

    default Pager getItemByAuthFilter(String sessionId, Psg4OtherGateItem condition, int pageNo, int size) {
        return null;
    }
}
