package com.zkteco.zkbiosecurity.psg.service;

import java.util.Collection;
import java.util.Map;

@Deprecated
public interface PsgGetVidChannel2EntityService {
	
	/**
	 * 获取通道辅助输入绑定的摄像头名称(通道名称)
	 *
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/9/19 10:19
	 * @param entityIds
	 * @return
	 */
	Map<String, String> getPsgAuxInBindChannelNames(Collection<String> entityIds);
	
	/**
	 * 获取通道读头绑定的摄像头名称(通道名称)
	 *
	 * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
	 * @date 2019/9/19 10:19
	 * @param entityIds
	 * @return
	 */
    Map<String, String> getPsgReaderBindChannelNames(Collection<String> entityIds);
}
