package com.zkteco.zkbiosecurity.psg.service;

@Deprecated
public interface Psg4VidChannel2EntityService {
    
    /**
     * 根据通道id和通道读头实体名称删除通道绑定
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/9/19 9:47
     * @param channelIds
     * @return void
     */
    void deleteByEntityIdAndPsgReader(String channelIds);
    
    /**
     * 根据通道id和通道辅助输入实体名称删除通道绑定
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/9/19 9:46
     * @param channelIds
     * @return void
     */
    void deleteByEntityIdAndPsgAuxIn(String channelIds);
}
