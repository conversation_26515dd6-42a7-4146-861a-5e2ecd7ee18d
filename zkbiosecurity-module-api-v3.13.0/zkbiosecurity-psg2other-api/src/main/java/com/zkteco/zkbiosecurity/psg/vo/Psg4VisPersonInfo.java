package com.zkteco.zkbiosecurity.psg.vo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @DATE 2020-06-28 10:26
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class Psg4VisPersonInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    private List<Psg4VisPersonOpt> personList = new ArrayList<>();

    private List<Psg4VisBioTemplateItem> psgBioTemplateItemList = new ArrayList<>();
}
