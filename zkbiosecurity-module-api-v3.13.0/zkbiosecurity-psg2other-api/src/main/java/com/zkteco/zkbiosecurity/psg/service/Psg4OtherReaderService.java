package com.zkteco.zkbiosecurity.psg.service;

import java.util.List;

/**
 * 通道读头对外接口
 * 
 * <AUTHOR>
 * @date 2021/8/18 11:39
 * @since 1.0.0
 */
public interface Psg4OtherReaderService {

    /**
     * 校验是否有其他模块使用通道读头信息
     *
     * @param readerIds:
     * @return void
     * <AUTHOR>
     * @throws
     * @date 2025-05-07 15:11
     * @since 1.0.0
     */
    default void checkPsgReaderIsUsed(List<String> readerIds) {}
}
