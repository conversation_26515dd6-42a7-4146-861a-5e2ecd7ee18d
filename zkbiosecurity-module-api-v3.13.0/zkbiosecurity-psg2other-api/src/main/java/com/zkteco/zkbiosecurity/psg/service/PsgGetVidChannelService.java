package com.zkteco.zkbiosecurity.psg.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.psg.vo.Psg4VidChannelItem;

@Deprecated
public interface PsgGetVidChannelService {

    /**
     * 根据用户登录权限过滤显示摄像头
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/7/29 15:44
     * @param sessionId
     * @param psg4VidChannelItem
     * @param pageNo
     * @param pageSize
     * @return
     */
    Pager loadPagerByAuthFilter(String sessionId, Psg4VidChannelItem psg4VidChannelItem, int pageNo, int pageSize);
}
