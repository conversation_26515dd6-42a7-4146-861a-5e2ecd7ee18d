package com.zkteco.zkbiosecurity.psg.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class Psg4VidChannelItem implements Serializable {

    /** 主键 */
    private String id;

    /** 通道名称 */
    private String name;

    /** 所属设备名称 */
    private String alias;

    /** 序列号 */
    private String sn;

    /** 判断是左列表（值为noSelect）还是右列表（值为select）*/
    private String type;

    private String selectId;

    private String inId;

    private String notInId;

    private Boolean enabled;

    private String areaIdIn;
}
