package com.zkteco.zkbiosecurity.psg.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
public class PsgTransaction4OtherItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 人员、访客pin号 */
    private String pin;

    /** 读头id */
    private String readerId;

    /** 读头名称 */
    private String readerName;

    private Short eventNo;
}
