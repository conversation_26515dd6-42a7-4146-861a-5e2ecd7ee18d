package com.zkteco.zkbiosecurity.psg.service;

/**
 * 通道设备对接其他模块接口
 *
 * <AUTHOR>
 * @date 2021/9/29 10:02
 */
public interface PsgDevice4OtherService {

    /**
     * 判断通道设备是否在其他模块使用 若被使用，实现模块抛出业务异常，throw new ZKBusinessException(level,msg);
     *
     * @param gateIds:闸ids
     * @return void
     * @date 2021-09-29 14:13
     * @since 1.0.0
     */
    default void checkPsgDeviceIsUsedByGateIds(String gateIds) {}
}
