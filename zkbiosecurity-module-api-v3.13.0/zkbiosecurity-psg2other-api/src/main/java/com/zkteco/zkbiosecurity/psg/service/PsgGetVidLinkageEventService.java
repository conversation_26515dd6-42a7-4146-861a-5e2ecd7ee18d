package com.zkteco.zkbiosecurity.psg.service;

import com.zkteco.zkbiosecurity.base.vo.ZKResultMsg;

@Deprecated
public interface PsgGetVidLinkageEventService {

    /**
     * 获取视频联动产生文件的路径，用于发送邮件（仅抓拍文件）
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年4月3日 下午6:07:19
     * @param vidLinkageHandle
     * @return
     */
    String getCaputureFilePath(String vidLinkageHandle);

    /**
     * 获取事件记录对应的视频联动信息
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/9/10 15:18
     * @param vidLinkageHandle 视频联动句柄
     * @param fileType 文件类型（1录像，2抓拍）
     * @return com.zkteco.zkbiosecurity.base.vo.ZKResultMsg
     */
    ZKResultMsg getVidLinkageEventData(String vidLinkageHandle, String fileType);
}
