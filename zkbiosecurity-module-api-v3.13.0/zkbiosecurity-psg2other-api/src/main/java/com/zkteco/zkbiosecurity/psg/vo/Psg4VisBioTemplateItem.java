package com.zkteco.zkbiosecurity.psg.vo;

import java.io.Serializable;
import java.util.Comparator;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @DATE 2020-06-28 10:31
 * @since 1.0.0
 */
@Setter
@Getter
@Accessors(chain = true)
public class Psg4VisBioTemplateItem implements Serializable, Comparator<Psg4VisBioTemplateItem> {
    private static final long serialVersionUID = 1L;

    private String pin;// 人员编号
    private Short templateId;// 生物模板id
    private Short templateIndex;// 模板编号
    private Boolean duress;// 生物模板类型
    private String template;// 生物模板
    private Short type;// 指纹，面部，指静脉
    private String version;// 生物模板版本
    private Short format;// 下发类型，0：模版, 1：url（图片）
    private String url;// 生物模版提取用图片

    @Override
    public int compare(Psg4VisBioTemplateItem o1, Psg4VisBioTemplateItem o2) {
        // 根据是否是胁迫排序，又要指令下发那块，胁迫下发必须放在最后 modified by max 20160330
        if (o1.getDuress() && !o2.getDuress()) {
            return 1;
        } else if (o1.getDuress() && o2.getDuress()) {
            return 0;
        } else {
            return -1;
        }
    }
}
