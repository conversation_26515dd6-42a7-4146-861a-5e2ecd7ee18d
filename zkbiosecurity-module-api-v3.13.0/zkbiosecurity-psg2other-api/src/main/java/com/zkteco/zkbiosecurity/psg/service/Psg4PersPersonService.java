package com.zkteco.zkbiosecurity.psg.service;

import com.zkteco.zkbiosecurity.psg.vo.Psg4OtherBioTemplateItem;
import com.zkteco.zkbiosecurity.psg.vo.Psg4OtherPersonInfoItem;
import com.zkteco.zkbiosecurity.psg.vo.Psg4PersPersonItem;

import java.util.Collection;

public interface Psg4PersPersonService {

    /**
     * 提供人事调用通道人员编辑接口，即通道人员属性编辑
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/4/1 18:05
     * @param item
     * @return void
     */
    Boolean editPsgPerson(Psg4PersPersonItem item);

    /**
     * 删除检查
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/4/1 18:26
     * @param personIds
     * @return java.lang.Boolean
     */
    Boolean checkDelPerson(String personIds);

    /**
     * 删除人员
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/4/1 18:26
     * @param personIds
     * @return void
     */
    void delPsgPerson(String personIds);

    /**
     * 人员离职
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/4/1 18:27
     * @param personIds
     * @return java.lang.Boolean
     */
    Boolean leavePsgPerson(String personIds);

    /**
     * （消费补卡/重新发卡）重新下发人员信息
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @date 2019/6/4 15:14
     * @param personId
     * @return void
     */
    void reIssuePersonInfo(String personId);
}
