package com.zkteco.zkbiosecurity.psg.vo;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/8/18 11:51
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class PsgReader4OtherItem extends BaseItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 读头id */
    private String id;
    /** 读头名称 */
    private String name;
    /** 读头编号 */
    private Short readerNo;
    /** 读头状态 */
    private Short readerState;
    /** 闸id */
    private String gateId;
    /** 闸名称 */
    private String gateName;
    /** 设备序列号 */
    private String deviceSn;

    private String inId;
    private String notId;
}
