package com.zkteco.zkbiosecurity.psg.service;

import java.util.List;

import com.zkteco.zkbiosecurity.psg.vo.Psg4VisPersonInfo;
import com.zkteco.zkbiosecurity.psg.vo.Psg4VisTransactionItem;

/**
 * <AUTHOR>
 * @DATE 2020-06-17 10:30
 * @since 1.0.0
 */
public interface PsgGetVisTransactionService {
    /**
     * 根据访客pin号获取通道权限组ids,用于下发权限
     * 
     * <AUTHOR>
     * @Param visitorPin
     * @return java.util.List<java.lang.String>
     * @date 2020/6/17 10:36
     */
    default List<String> findModuleIdsByVisEmpPin(String visitorPin) {
        return null;
    }

    /**
     * 根据权限组id获取访客信息
     * 
     * <AUTHOR>
     * @Param levelId
     * @return java.util.List<com.zkteco.zkbiosecurity.psg.vo.Psg4VisPersonInfo>
     * @date 2020/6/28 10:37
     */
    default List<Psg4VisPersonInfo> getVisiotrInfosByLevelId(String levelId) {
        return null;
    }

    /**
     * 根据pin号获取访客信息
     *
     * <AUTHOR> href=<EMAIL>>jeiffu.lee</a>
     * @since 2018年11月29日 下午3:20:31
     * @param pin
     * @return
     */
    default Psg4VisTransactionItem getVisitorInfoByPin(String pin) {
        return null;
    }

}
