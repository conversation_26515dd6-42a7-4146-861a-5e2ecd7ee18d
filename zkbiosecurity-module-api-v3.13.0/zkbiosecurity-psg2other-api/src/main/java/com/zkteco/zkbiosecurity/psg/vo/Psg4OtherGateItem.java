package com.zkteco.zkbiosecurity.psg.vo;

import java.io.Serializable;

import com.zkteco.zkbiosecurity.base.vo.BaseItem;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 通道闸机对外接口实体
 *
 * <AUTHOR> href="mailto:<EMAIL>">gaoqi.lian</a>
 * @date 2022/11/25 11:14
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Psg4OtherGateItem extends BaseItem implements Serializable {

    private String id;

    private String name;

    private String deviceAlias;

    private String deviceSn;

    private String deviceId;

    private String notId;
}
