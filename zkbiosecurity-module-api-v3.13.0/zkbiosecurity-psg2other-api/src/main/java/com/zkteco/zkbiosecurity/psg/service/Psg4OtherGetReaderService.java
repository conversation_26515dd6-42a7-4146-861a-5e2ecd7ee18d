package com.zkteco.zkbiosecurity.psg.service;

import com.zkteco.zkbiosecurity.base.bean.Pager;
import com.zkteco.zkbiosecurity.psg.vo.PsgReader4OtherItem;

import java.util.List;

/**
 * 通道读头对外接口
 * 
 * <AUTHOR>
 * @date 2021/8/18 11:39
 * @since 1.0.0
 */
public interface Psg4OtherGetReaderService {

    /**
     * 根据读头id获取
     *
     * @param readerId:读头id
     * @return com.zkteco.zkbiosecurity.psg.vo.PsgReader4OtherItem
     * <AUTHOR>
     * @date 2021-08-18 11:57
     * @since 1.0.0
     */
    default PsgReader4OtherItem getItemById(String readerId) {
        return null;
    }

    /**
     * 根据读头ids获取
     *
     * @param readerIds:读头id集合
     * @return java.util.List<com.zkteco.zkbiosecurity.psg.vo.PsgReader4OtherItem>
     * <AUTHOR>
     * @date 2021-08-18 11:58
     * @since 1.0.0
     */
    default List<PsgReader4OtherItem> getItemsByIds(List<String> readerIds) {
        return null;
    }

    /**
     * 根据条件查询读头信息
     *
     * @param sessionId:sessionId
     * @param condition:查询条件
     * @param pageNo:当前页
     * @param size:获取数量
     * @return com.zkteco.zkbiosecurity.base.bean.Pager
     * <AUTHOR>
     * @date 2021-08-18 12:00
     * @since 1.0.0
     */
    default Pager getItemsByAuthFilter(String sessionId, PsgReader4OtherItem condition, int pageNo, int size) {
        return null;
    }

    /**
     * 根据设备id查询读头信息
     *
     * @param devId:设备id
     * @return java.util.List<com.zkteco.zkbiosecurity.psg.vo.PsgReader4OtherItem>
     * <AUTHOR>
     * @date 2021-09-22 11:18
     * @since 1.0.0
     */
    default List<PsgReader4OtherItem> getItemsByDevId(String devId) {
        return null;
    }
}
