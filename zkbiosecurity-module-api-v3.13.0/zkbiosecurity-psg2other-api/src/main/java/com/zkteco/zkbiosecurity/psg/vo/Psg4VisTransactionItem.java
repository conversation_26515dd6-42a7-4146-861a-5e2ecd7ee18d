package com.zkteco.zkbiosecurity.psg.vo;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @DATE 2020-07-30 14:37
 * @since 1.0.0
 */
@Getter
@Setter
@Accessors(chain = true)
public class Psg4VisTransactionItem implements Serializable {
    private static final long serialVersionUID = 1L;

    private String visEmpPin;
    private String visEmpName;
    private String visEmpLastName;
    /** 证件照片 */
    private String certPhoto;

    public Psg4VisTransactionItem() {}
}
