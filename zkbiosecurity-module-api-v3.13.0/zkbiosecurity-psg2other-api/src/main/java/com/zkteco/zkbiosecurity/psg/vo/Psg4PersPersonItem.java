package com.zkteco.zkbiosecurity.psg.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@Accessors(chain = true)
public class Psg4PersPersonItem implements Serializable{
	private static final long serialVersionUID = 1L;
	/**
	 * 当前用户权限组id集合,用逗号隔开，如1,2,3,4
	 */
	private String personLevelIds;

	/**以下是通道人员特殊属性*/
	
	/**是否超级用户，15-是；0-否*/
	private Short superAuth;
	/**设备操作权限，0-一般人员，14-管理员，2-登记员*/
	private Short privilege;

	/** 人员id */
	private String personId;

	private String opType;

	/** 更新通道设置，是否有通道标签页权限 */
	private Boolean updatePsgSet;
}
