0 verbose cli D:\Program Files\nodejs\node.exe D:\Program Files\nodejs\node_modules\npm\bin\npm-cli.js
1 info using npm@10.9.2
2 info using node@v22.17.0
3 silly config load:file:D:\Program Files\nodejs\node_modules\npm\npmrc
4 silly config load:file:D:\.npmrc
5 silly config load:file:C:\Users\<USER>\.npmrc
6 silly config load:file:C:\Users\<USER>\AppData\Roaming\npm\etc\npmrc
7 verbose title npm config list
8 verbose argv "config" "list" "--json"
9 verbose logfile logs-max:10 dir:D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104\_logs\2025-07-17T11_44_16_273Z-
10 verbose logfile D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104\_logs\2025-07-17T11_44_16_273Z-debug-0.log
11 silly logfile done cleaning log files
12 verbose cwd D:\workspace\V6600\线下推送微信公众号消息\WioSecurity_4.5.x_R_YFDZ2025071400104
13 verbose os Windows_NT 10.0.19045
14 verbose node v22.17.0
15 verbose npm  v10.9.2
16 verbose exit 0
17 info ok
